<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-button-group>
                    <el-button style="padding: 0;width: 150px;">
                        <el-input v-model="Filter.taskId" style="padding: 0;width: 130px;" type="number" placeholder="任务编号"  clearable></el-input>
                    </el-button>
                    <!-- <el-button style="padding: 0;margin: 0;">
                      

                        <el-input-number v-model="Filter.taskId" type="number" placeholder="任务编号"  :min="1" :max="100000" 
                        @keyup.enter.native="onSearch"
                        :step="1"  :step-strictly="true" controls-position="right" clearable />
                    </el-button> -->
                    <!-- <el-button style="padding: 0;width: 155px;">
                        <el-input v-model="Filter.videoTaskName" placeholder="任务名称" @keyup.enter.native="onSearch" clearable />
                    </el-button> -->
                    <el-button style="padding: 0;">
                        <el-select filterable v-model="Filter.platform" placeholder="平台" :clearable="true" :collapse-tags="true" @change="onchangeplatform" style="width: 80px">
                            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;">
                        <el-select filterable v-model="Filter.shopId" placeholder="店铺" :clearable="true" style="width: 130px">
                            <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.id" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;width: 155px;">
                        <el-input v-model="Filter.productId" placeholder="产品ID" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                    <el-button style="padding: 0;width: 155px;">
                        <el-input v-model="Filter.productShortName" placeholder="产品简称" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                    <el-button style="padding: 0;width: 155px;">
                        <el-input v-model="Filter.goodCode" placeholder="商品编码" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.CreatedUserName" placeholder="拍摄人" clearable="true" style="width:120px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <datepicker v-model="Filter.Sdate"></datepicker>
                    </el-button>

                    <el-button type="primary" @click="onSearch">查询</el-button>

                </el-button-group>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='false' :hasexpand='false' @sortchange='sortchange' :tableData='datalist' @select='selectchange' :isSelection='false' :tableCols='tableCols' :loading="listLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
               
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getVideoList" />
        </template>

        <el-dialog title="视频预览" :visible.sync="dialogVisible" width="50%">
            <span>
                <div class="PlayVideo">
                    <!-- 视频播放 -->
                    <video class="video" controls="" ref="video" id="video" v-on:error.prevent="error($event)">
                    </video>
                    <!-- 信息提示 -->
                    <div class="msg" v-if="isError">
                        <div style="color: #fff">{{errMsg}}</div>
                    </div>
                </div>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="不通过" :visible.sync="dialogNoPassVisible" width="30%">
            <span>
                <el-form ref="noPassForm" :model="noPassForm" label-width="120px">
                    <el-form-item prop="noPassRemark" label="不通过原因：">
                        <el-input v-model="noPassForm.noPassRemark" auto-complete="off" style="width:90%" />
                    </el-form-item>
                </el-form>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogNoPassVisible = false">取消</el-button>
                <my-confirm-button type="submit" @click="videoNoPassOK" />
            </span>
        </el-dialog>
        <el-drawer title="视频剪辑" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editVideoVisible" direction="btt" size="'auto'" style="position:absolute;overflow:hidden" @close="closeCuteForm">
            <section>
                <videoplay v-if="videoplayReload" :filter="filter" ref="videoplay" :videopath="videopath" :videoduration="videoduration" :videoid='videoid' style="height: 80%;width:100%" />
            </section>
            <div class="drawer-footer">
                <el-button @click.native="editVideoVisible = false">取消</el-button>
                <my-confirm-button type="submit" :validate="editFormValidate" :loading="editLoading" @click="onCutVideoSubmit" />
            </div>
        </el-drawer>
    </my-container>
</template>
<script>
    import { getList as getshopList } from '@/api/operatemanage/base/shop'
    import { rulePlatform } from "@/utils/formruletools";
    import videoplay from '@/views/media/video/videoplay'
    import datepicker from '@/views/customerservice/datepicker'
    import { getList, cuteVideo, processVideoPassStatus, downVideoAsync } from '@/api/media/video'
    import { deleteUploadVideoByIdAsync} from '@/api/media/vediotask'; 
    import cesTable from "@/components/Table/table.vue";
    import { formatTime } from "@/utils";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";  

    const tableCols = [
        { istrue: true, prop: 'taskId', label: '任务编号', width: '53', sortable: 'custom' },
        // { istrue: true, prop: 'id', label: '拍摄编号', width: '160', sortable: 'custom' },
        // { istrue: true, prop: 'seriId', label: '序列编号', width: '160', sortable: 'custom' },
        // { istrue: true, prop: 'videoTaskName', label: '任务名称', width: '120', sortable: 'custom' },
        { istrue: true, prop: 'shopId', label: '店铺', width: '200', sortable: 'custom', formatter: (row) => row.shopName || ' ' },
        { istrue: true, prop: 'productId', label: '产品ID', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'productShortName', label: '产品简称', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'goodCode', label: '商品编码', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'uploadType', label: '参考', width: '100' },
        { istrue: true, prop: 'cuteTitle', label: '剪辑片段', width: '80' },
        { istrue: true, prop: 'cuteImgPath', videoprop: 'cuteVedioPath', label: '片段地址', width: '80', type: 'video' },
        { istrue: true, prop: 'imgPath', videoprop: 'videoPath', label: '视频地址', width: '80', type: 'imgvideo' },
        { istrue: true, prop: 'fileName', label: '视频名称', width: '80' },
        { istrue: true, prop: 'createdUserName', label: '拍摄人', width: '80', sortable: 'custom' },
 /*        { istrue: true, prop: 'UploadStatusEnum', label: '上传状态', width: '120', sortable: 'custom', formatter: (row) => row.uploadStatus || ' ' }, */
        { istrue: true, prop: 'StatusEnum', label: '审核状态', width: '90', sortable: 'custom', formatter: (row) => row.uploadStatus || ' '  },
        { istrue: true, prop: 'createdTime', label: '上传时间', width: '150', sortable: 'custom' },
        {
            istrue: true, type: "button", width: "150", align: 'center', label: '操作',
            btnList: [
                // { label: "通过 ", handle: (that, row) => that.videoPass(row), ishide: (that, row) => that.isPassBtnShow(row) },
                // { label: " 不通过", handle: (that, row) => that.videoNoPass(row), ishide: (that, row) => that.isNoPassBtnShow(row) },
                // { label: " 剪切", handle: (that, row) => that.onCuteVideo(row), ishide: (that, row) => that.isCuteBtnShow(row) },
               //{ label: " 下载", handle: (that, row) => that.downVideo(row) }
               { label: "删除", handle: (that, row) => that.DeleteUpLoad(row) }

            ]
        }
    ];
    export default {
        name: "uploadvideo",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, videoplay },
        data() {
            return {
                videopath: '',
                videoduration: 10000,
                videoid: '',
                videoplayReload: true,
                editVideoVisible: false,
                videoUrls: '',
                that: this,
                Filter: {
                    Sdate: [formatTime(new Date(), "YYYY-MM-DD "), formatTime(new Date(), "YYYY-MM-DD ")],
                },
                tableCols: tableCols,
                total: 0,
                show: true,
                summaryarry: { count_sum: 10 },
                pager: { OrderBy: "createdTime", IsAsc: false },
                listLoading: false,
                pageLoading: false,
                fileList: [],
                datalist: [],
                sels: [],
                selids: [],
                dialogVisible: true,
                isError: false, // 是否不能播放视频
                errMsg: "",
                dialogNoPassVisible: false,
                noPassForm: { noPassId: null, noPassRemark: "" },
                platformList: [],//平台下拉
                shopList: []
            };
        },
        async mounted() {
            await this.setPlatform();//平台下拉
            var that = this;
            this.show = true;
            this.dialogVisible = false;

            let video = document.getElementById("video");
            this.onSearch(); 
            //this.videoUrl = 'http://www.si-tech.com.cn/pub-ui/images/radio/sitech.mp4',
            //    this.$refs.video.src = this.videoUrl
        },
        methods: {
            //设置平台下拉
            async setPlatform() {
                var pfrule = await rulePlatform();
                this.platformList = pfrule.options;
            },
            async onchangeplatform(val) {
                const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100 });
                this.shopList = res1.data.list;
            },
            async downVideo(row) {

                var res = await downVideoAsync({ id: row.id, type: 1 });
                if(res?.data?.type  =='application/json') {
                    return;
                }
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "video/mp4" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', row.seriId + "_" + new Date().toLocaleString() + row.fileExt)
                aLink.click();
            },
            async DeleteUpLoad(row) {
                var that = this;
                this.$confirm("此操作将删除数据?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(async () => {
                    
                    debugger;
                    var res = await deleteUploadVideoByIdAsync({ videoId: row.id });
                    if(res?.success){
                        that.$message({ type: 'success', message:'操作成功' });
                        this.onSearch(); 
                    }
                });
                
            },

            
            async closeCuteForm() {
                this.videoplayReload = false;
            },
            async onCuteVideo(row) {
                this.editVideoVisible = true;
                this.videoplayReload = true;
                this.videopath = row.videoPath;
                this.videoduration = row.duration;
                this.videoid = row.id;
            },
            async onCutVideoSubmit() {
                var that = this;

                var data = this.$refs.videoplay.getData()

                var para = [];
                data.forEach((item, index) => {
                    var video = {};
                    video.ID = that.videoid;
                    video.beginTime = item.beginTime1;
                    video.endTime = item.endTime1;
                    video.title = item.name;
                    para.push(video);
                });


                const res = await cuteVideo(para);
                if (!res?.success) {
                    that.$message({ type: 'success', message: res?.msg });
                } else {
                    that.$message({ type: 'success', message: '提交成功,排队剪切中，请到剪切后视频查看剪切进度...' });
                }

            },
            async deleteBatch(row) {
                var that = this;
                this.$confirm("此操作将删除此批次客服人员咨询数据数据?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(async () => {
                        await deleteInquirsBatch({ batchNumber: row.batchNumber })
                        that.$message({ message: '已删除', type: "success" });
                        that.onRefresh()

                    });

            },
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.getVideoList();
            },
            async getVideoList() {

                if (this.Filter.Sdate) {
                    this.Filter.startSdate = this.Filter.Sdate[0];
                    this.Filter.endSdate = this.Filter.Sdate[1];
                }
                else {
                    this.Filter.startSdate = null;
                    this.Filter.endSdate = null;
                }
                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,

                };
                this.listLoading = true;
                const res = await getList(params);
                this.listLoading = false;
                this.total = res.data.total
                this.datalist = res.data.list;
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },

            checkUploadStatus(uploadStatus) {
                if (uploadStatus != "上传成功") {
                    return "非上传成功的数据无法操作";
                }
                return "";
            },
            isPassBtnShow(row) {
                if (this.checkUploadStatus(row.uploadStatus) != "")
                    return true;
                if (row.status != "待审核")
                    return true;
                //判断是否隐藏，false为不隐藏
                return false;
            },
            isNoPassBtnShow(row) {
                if (this.checkUploadStatus(row.uploadStatus) != "")
                    return true;
                if (row.status != "待审核")
                    return true;
                //判断是否隐藏，false为不隐藏
                return false;
            },
            isCuteBtnShow(row) {
                if (this.checkUploadStatus(row.uploadStatus) != "")
                    return true;
                if (row.status != "通过")
                    return true;
                //判断是否隐藏，false为不隐藏
                return false;
            },
            videoPass(row) {
                var that = this;
                this.$confirm("确定要执行【通过】操作吗?", "提示", { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning", }).then(
                    async () => {
                        var res = await processVideoPassStatus({ videoId: row.id, statusEnum: 3, statusRemark: "" });
                        if (res?.success) {
                            that.$message({ message: '操作成功', type: "success" });
                            that.onRefresh();
                        }
                    });
            },
            videoNoPass(row) {
                this.noPassForm.noPassId = row.id;
                this.noPassForm.noPassRemark = "";
                this.dialogNoPassVisible = true;
            },
            async videoNoPassOK() {
                var res = await processVideoPassStatus({ videoId: this.noPassForm.noPassId, statusEnum: 2, statusRemark: this.noPassForm.noPassRemark });
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.dialogNoPassVisible = false;
                    this.onRefresh();
                }
            },
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>


<style >
    .el-image__inner {
        max-height: 50px;
    }
</style>
