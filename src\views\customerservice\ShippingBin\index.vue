<template>
    <my-container>
        <template #header>
            <div class="top">
                <div style="font-size: 16px;">统计维度：</div>
                <el-checkbox-group v-model="filter.groupProps" style="float:left;margin-top:1px;">
                    <el-checkbox v-for="item in checkTamp" :label="item.value" :key="item.value">{{ item.label
                    }}</el-checkbox>
                </el-checkbox-group>
            </div>
            <div class="top">
                <el-date-picker v-model="pickvalue" type="daterange" align="right" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" class="rightMar"
                    :clearable="false" @change="changeDate">
                </el-date-picker>
                <el-select v-model="filter.sendGoodsWarehouse" filterable placeholder="发货仓" class="rightMar" clearable>
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-input v-model="filter.damagedGoodsCode" placeholder="商品编码" class="rightMar" style="width: 220px;"
                    clearable maxlength="200"></el-input>
                <el-button type="primary" class="rightMar" @click="onsearch">查询</el-button>
                <el-button type="primary" class="rightMar" @click="onExport">导出</el-button>
                <el-button type="primary" class="rightMar" @click="dialogVisible = true">破损订单导入</el-button>
            </div>
        </template>

        <cesTable ref="detailTable" :table-data="tableData" :table-cols="tableCols" :is-index="true" :that="that"
            style="width: 100%; height: 640px; margin: 10px 0 50px" @sortchange='sortchange' :summaryarry='summaryarry'
            @summaryClick='onsummaryClick'>
        </cesTable>
        <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="total" @get-page="getBrokenList"
            @page-change="Dispagechange" @size-change="Dissizechange" class="pageBox" />

        <el-dialog :title="isSum ? '汇总趋势图' : '趋势图'" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag
            v-loading="chatLoading">
            <div style="display: flex;">
                <el-date-picker v-model="chatPickValue" type="daterange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" @change="searchChat" :picker-options="pickerOptions" :clearable="false">
                </el-date-picker>
            </div>
            <span>
                <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
            <el-row>
                <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                    <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                        accept=".xlsx" :on-change="uploadFile" :file-list="fileList" :on-remove="uploadRemove">
                        <template #trigger>
                            <el-button size="small" type="primary">选取文件</el-button>
                        </template>
                        <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                            @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                    </el-upload>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/Table/table.vue";
import { getCols } from '@/utils/getCols.js'
import buschar from '@/components/Bus/buschar'
import { getDamagedOrdersStatistics, exportDamagedOrdersStatistics, importDamagedOrders, getStaticticsChatData } from '@/api/customerservice/DamagedOrders'
import { getAllWarehouse } from '@/api/inventory/warehouse'
import dayjs from 'dayjs'
const checkTamp = [
    {
        label: '发货日期',
        value: 'SendGoodsDate'
    },
    {
        label: '发货仓',
        value: 'SendGoodsWarehouse'
    },
    {
        label: '商品编码',
        value: 'DamagedGoodsCode'
    }
]

const tableCols = [
    { istrue: true, prop: 'sendGoodsDate', label: '发货日期', sortable: 'custom' },
    { istrue: true, prop: 'sendGoodsWarehouseStr', label: '发货仓', sortable: 'custom' },
    { istrue: true, prop: 'damagedGoodsCode', label: '商品编码', sortable: 'custom', handle: (that, row) => that.showchart(row), type: 'click' },
    { istrue: true, prop: 'sendOrderCount1', summaryEvent: true, label: '发货订货数', sortable: 'custom' },
    { istrue: true, prop: 'sendGoodsCount', summaryEvent: true, label: '发货商品数', sortable: 'custom' },
    { istrue: true, prop: 'damagedOrderCount1', summaryEvent: true, label: '破损订单数', sortable: 'custom' },
    { istrue: true, prop: 'damagedGoodsCount', summaryEvent: true, label: '破损商品数', sortable: 'custom' },
    { istrue: true, prop: 'damagedOrderRatio', summaryEvent: true, label: '破损订单比', sortable: 'custom', formatter: (row) => row.damagedOrderRatio + '%' },
    { istrue: true, prop: 'outgoingCost', summaryEvent: true, label: '订单出仓平均成本', sortable: 'custom' },
    { istrue: true, prop: 'damagedGoodsPrice', summaryEvent: true, label: '破损商品单价', sortable: 'custom' },
    { istrue: true, prop: 'damagedExpressCost', summaryEvent: true, label: '破损平均快递费', sortable: 'custom' },
    { istrue: true, prop: 'damagedPackingCost', summaryEvent: true, label: '破损平均包装费', sortable: 'custom' },
    { istrue: true, prop: 'damagedTotalCost', summaryEvent: true, label: '破损总费用', sortable: 'custom' },
]
export default {
    name: 'ShippingBin',
    components: {
        MyContainer,
        cesTable,
        buschar
    },
    data() {
        return {
            isSum: false,
            chatLoading: true,
            total: null,
            summaryarry: null,
            uploadLoading: false,
            dialogVisible: false,
            fileList: [],
            chatPickValue: [],
            buscharDialog: {
                visible: false,
                title: "",
                data: []
            },
            tableData: [],//表格数据
            tableCols,
            that: this,
            pickvalue: null,
            checkTamp,
            filter: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                groupProps: ['SendGoodsDate', 'SendGoodsWarehouse', 'DamagedGoodsCode'],
                startDate: null,
                endDate: null,
                sendGoodsWarehouse: null,
                damagedGoodsCode: null,
                column: null,
            },
            pickerOptions: {
                shortcuts: [{
                    text: '近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }],
                // 自定义日期禁用函数
                disabledDate(date) {
                    // 获取当前日期
                    const currentDate = new Date();
                    // 如果选中日期在当前日期之后，则禁用它
                    return date > currentDate;
                }
            },
            options: [],
            chatSearchInfo: {
                damagedGoodsCode: null,//商品编码
                sendGoodsWarehouse: null,//发货仓
                sendGoodsDate: null,//发货日期
                column: null,//列名
                startDate: null,//开始时间
                endDate: null,//结束时间
            },
            sumInfo: null,
        };
    },
    mounted() {
        this.getBrokenList()
        this.getHouseList()
    },
    methods: {
        async onsummaryClick(property) {
            console.log(property, 'property');
            this.chatPickValue = this.pickvalue
            this.sumInfo = this.filter
            this.isSum = true
            this.buscharDialog.visible = false
            this.filter.column = property
            this.publicChat()
            //q:为什么我这个方法打开图表就报错呢
            //a:因为你的this.filter.column = property,这个property是一个对象,而你的接口需要的是一个字符串,所以你需要把这个对象转成字符串
        },
        //根据日期搜索图表
        async searchChat(e) {
            this.buscharDialog.visible = false
            if (e == null || e == undefined || e == '') {
                this.chatSearchInfo.startDate = null
                this.chatSearchInfo.endDate = null
            } else {
                //使用dayjs格式化时间
                this.chatSearchInfo.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                this.chatSearchInfo.endDate = dayjs(e[1]).format('YYYY-MM-DD')
            }
            if (this.isSum) {
                let obj = this.filter
                obj.startDate = this.chatSearchInfo.startDate
                obj.endDate = this.chatSearchInfo.endDate
                const { data } = await getStaticticsChatData(obj)
                this.buscharDialog.data = data;
                this.buscharDialog.visible = true
                this.chatLoading = false
            } else {
                const { data } = await getStaticticsChatData(this.chatSearchInfo)
                this.buscharDialog.data = data;
                this.buscharDialog.visible = true
                this.chatLoading = false
            }
        },
        changeGroup() {
            //如果this.filter.groupProps.length == 2并且this.filter.groupProps不包含DamagedGoodsCode,那就给tableCols中的sendGoodsWarehouseStr,添加handle方法和一个type属性
            if (this.filter.groupProps.length == 2 && !this.filter.groupProps.includes('DamagedGoodsCode')) {
                this.tableCols.forEach(item => {
                    if (item.prop == 'sendGoodsWarehouseStr') {
                        item.handle = (that, row) => that.showchart(row)
                        item.type = 'click'
                    }
                })
            } else {
                this.tableCols.forEach(item => {
                    if (item.prop == 'sendGoodsWarehouseStr') {
                        delete item.handle
                        delete item.type
                    }
                })
            }
            //如果this.filter.groupProps.length == 1 并且this.filter.groupProps包含SendGoodsDate那就给tableCols中的sendGoodsDate,添加handle方法和一个type属性
            if (this.filter.groupProps.length == 1 && this.filter.groupProps.includes('SendGoodsDate')) {
                this.tableCols.forEach(item => {
                    if (item.prop == 'sendGoodsDate') {
                        item.handle = (that, row) => that.showchart(row)
                        item.type = 'click'
                    }
                })
            } else {
                this.tableCols.forEach(item => {
                    if (item.prop == 'sendGoodsDate') {
                        delete item.handle
                        delete item.type
                    }
                })
            }
        },
        async publicChat() {
            if (dayjs(this.pickvalue[1]).diff(dayjs(this.pickvalue[0]), 'day') < 30) {
                this.chatPickValue = [dayjs().subtract(30, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
            } else {
                this.chatPickValue = this.pickvalue
            }
            if (this.isSum) {
                this.sumInfo.startDate = this.chatPickValue[0]
                this.sumInfo.endDate = this.chatPickValue[1]
            } else {
                this.chatSearchInfo.startDate = this.chatPickValue[0]
                this.chatSearchInfo.endDate = this.chatPickValue[1]
            }
            if (this.isSum) {
                const { data } = await getStaticticsChatData(this.sumInfo)
                this.buscharDialog.data = data;
                this.buscharDialog.visible = true
                this.chatLoading = false
            } else {
                const { data } = await getStaticticsChatData(this.chatSearchInfo)
                this.buscharDialog.data = data;
                this.buscharDialog.visible = true
                this.chatLoading = false
            }

        },
        //显示图表
        async showchart(row) {
            this.isSum = false
            this.buscharDialog.visible = false
            if (this.filter.groupProps.includes('DamagedGoodsCode')) {
                this.chatSearchInfo.damagedGoodsCode = row.damagedGoodsCode
                this.chatSearchInfo.sendGoodsWarehouse = row.sendGoodsWarehouse
                this.chatSearchInfo.sendGoodsDate = row.sendGoodsDate
            } else if (this.filter.groupProps.length == 2 && !this.filter.groupProps.includes('DamagedGoodsCode')) {
                this.chatSearchInfo.damagedGoodsCode = null
                this.chatSearchInfo.sendGoodsWarehouse = row.sendGoodsWarehouse
                this.chatSearchInfo.sendGoodsDate = row.sendGoodsDate
            } else {
                this.chatSearchInfo.damagedGoodsCode = null
                this.chatSearchInfo.sendGoodsWarehouse = null
                this.chatSearchInfo.sendGoodsDate = row.sendGoodsDate
            }
            //清空时间
            this.chatSearchInfo.startDate = null
            this.chatSearchInfo.endDate = null
            this.publicChat()
        },
        changeDate(e) {
            //如果e为空或者e为null或者为undefined就清空时间
            if (!e) {
                this.filter.startDate = null
                this.filter.endDate = null
            }
            //如果e不为空就赋值
            if (e) {
                this.filter.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                this.filter.endDate = dayjs(e[1]).format('YYYY-MM-DD')
            }
            //拉一次列表
            this.getBrokenList()
        },
        async submitUpload() {
            if (this.fileList.length == 0) {
                this.$message.warning('您没有选择任何文件！')
                return
            }
            const form = new FormData();
            form.append("upfile", this.fileList[0].raw);
            const { success } = await importDamagedOrders(form)
            if (success) {
                this.getBrokenList()
                this.$message.success('上传成功')
                this.fileList = []
                this.dialogVisible = false
            }
        },
        uploadRemove() {
            this.fileList = []
        },
        //导出excel
        async onExport() {
            let exportInfo = this.filter
            exportInfo.currentPage = 0
            exportInfo.pageSize = 0
            exportInfo.sendGoodsWarehouse = null
            const { data } = await exportDamagedOrdersStatistics(exportInfo)
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '破损订单统计分析' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        //分销商分页数量改变
        Dissizechange(val) {
            this.filter.CurrentPage = 1;
            this.filter.PageSize = val;
            this.getBrokenList();
        },
        //分销商分页页码改变
        Dispagechange(val) {
            this.filter.CurrentPage = val;
            this.getBrokenList();
        },
        //获取所有仓库信息
        async getHouseList() {
            const { data } = await getAllWarehouse()
            this.options = data.map(item => {
                return {
                    label: item.name,
                    value: item.wms_co_id
                }
            })
        },
        //搜索
        onsearch() {
            //清除商品编码的空格
            if (this.filter.damagedGoodsCode) {
                this.filter.damagedGoodsCode = this.filter.damagedGoodsCode.replace(/\s+/g, "")
            }
            if (this.pickvalue) {
                this.filter.startDate = dayjs(this.pickvalue[0]).format('YYYY-MM-DD')
                this.filter.endDate = dayjs(this.pickvalue[1]).format('YYYY-MM-DD')
            }
            if (this.filter.groupProps.length == 0) {
                this.filter.groupProps = ['SendGoodsDate', 'SendGoodsWarehouse', 'DamagedGoodsCode']
            }
            this.tableCols = getCols(this.filter.groupProps, this.checkTamp, this.tableCols)
            this.changeGroup()
            this.getBrokenList()
        },
        //破损订单查询
        async getBrokenList() {
            //this.pickerValue默认值是从当前日期往前推算一个月
            if (!this.pickvalue) {
                this.filter.startDate = dayjs().subtract(1, 'month').format('YYYY-MM-DD')
                this.filter.endDate = dayjs().format('YYYY-MM-DD')
                this.pickvalue = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
            }
            const { data, success } = await getDamagedOrdersStatistics(this.filter)
            if (success) {
                this.tableData = data.list
                this.summaryarry = data.summary
                this.total = data.total
            } else {
                this.$message.error('获取列表失败')
            }
        },
        async uploadFile(file, fileList) {
            this.fileList = fileList

        },
        sortchange(column) {
            if (column.order) {
                var orderField = column.prop;
                this.filter.OrderBy = orderField;
                this.filter.IsAsc = column.order.indexOf("descending") == -1 ? true : false
                this.getBrokenList()
            }
        },
    },
};
</script>

<style scoped lang="scss">
.top {
    display: flex;
    align-items: center;
    height: 40px;
}

.rightMar {
    margin-right: 10px;
}
</style>