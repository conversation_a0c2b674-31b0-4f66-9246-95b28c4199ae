<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
        <el-form-item label="">
          <el-select v-model="Filter.platform" placeholder="平台" style="width:120px" class="el-select-content" clearable
            filterable>
            <el-option v-for="item in platformList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="">
          <el-date-picker style="width: 320px" v-model="Filter.timerange" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="addButton">添加出仓成本均价</el-button> </el-form-item>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :id="'crossBorderCourierFeeAverage202408310425'" :tableData='dahuixionglist' @select='selectchange'
      :isSelection='false' :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableCols='tableCols'
      :loading="listLoading">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getjSpeedDriveList" />
    </template>


    <el-drawer title="编辑出仓成本均价" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
      :visible.sync="editVisible" direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
      <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" />
      <div class="drawer-footer">
        <!-- <el-button @click.native="editVisible = false">取消</el-button> -->
        <el-button @click="cancelEditSubmit">取消</el-button>
        <my-confirm-button type="submit" @click="onEditSubmit" />
      </div>
    </el-drawer>
    <el-drawer title="新增出仓成本均价" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
      :visible.sync="addVisible" direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
      <form-create :rule="autoformAdd.rule1" v-model="autoformAdd.fApi" :option="autoformAdd.options" />
      <div class="drawer-footer">
        <el-button @click.native="addVisible = false">取消</el-button>
        <my-confirm-button type="submit" @click="onaddSubmit" />
      </div>
    </el-drawer>
  </my-container>
</template>
<script>

import { getExitCostAveragePrice, editExitCostAveragePriceAsync } from '@/api/bookkeeper/reportdayV2'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { formatPlatformkj } from "@/utils/tools";
const platformList = [
{ name: "SHEIN全托", value: 12 },
  { name: "SHEIN自营", value: 16 },
  { name: "SHEIN半托", value: 19 },
  { name: "TEMU全托", value: 13 },
  { name: "TEMU半托", value: 15 },
  { name: "跨境分销", value: 23 },

]
const tableCols = [
  { istrue: true, prop: 'platForm', label: '平台', width: '300', sortable: 'custom', formatter: (row) => formatPlatformkj(row.platForm) },
  { istrue: true, prop: 'scale', label: '出仓成本均价', width: '300', sortable: 'custom', formatter: (row) => !row.scale ? " " : row.scale },
  {
    istrue: true,
    prop: "effectiveTime",
    label: "生效日期",
    width: "300",
    sortable: "custom",
    formatter: (row) => {
      return row.effectiveTime ? formatTime(row.effectiveTime, "YYYY-MM-DD") : "";
    },
  },
  { istrue: true, type: "button", label: '操作', width: "350", btnList: [{ label: "编辑", handle: (that, row) => that.EditButton(row) }] }
];
export default {
  name: "crossBorderCourierFeeAverage",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
  data() {
    return {
      that: this,
      editLoading: false,
      addVisible: false,
      Filter: {
        timerange: [],
      },
      shopList: [],
      userList: [],
      groupList: [],
      dahuixionglist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: {},
      pager: { OrderBy: "id", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
      platform: 1,
      yearMonth: "",
      editVisible: false,
      autoform: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
        rule: []
      },
      autoformAdd: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
        rule: []
      },
      platformList: platformList,
      defaultDate: new Date(),
      pickerOptions: {
        disabledDate(date) {
          // 设置禁用日期
          const start = new Date("1970/1/1");
          const end = new Date("9999/12/31");
          return date < start || date > end;
        },
      },
    };
  },
  async mounted() {
    await this.initform();
    this.onSearch();
  },
  methods: {
    addButton() {
      this.addVisible = true;
    },
    async onaddSubmit() {
      await this.autoformAdd.fApi.validate(async (valid, fail) => {
        if (valid) {
          const formData = this.autoformAdd.fApi.formData();
          const res = await editExitCostAveragePriceAsync(formData);
          await this.autoformAdd.fApi.resetFields()
          if (res.data == true) {
            this.$message.success('添加成功！');
            this.getjSpeedDriveList();
            this.addVisible = false;
          }
          else {
            this.$message.error('该平台当月数据已存在。')
          }
        }
      })
    },
    EditButton(row) {
      this.editVisible = true
      var arr = Object.keys(this.autoform.fApi);
      if (arr.length > 0)
        this.autoform.fApi.resetFields()
      this.$nextTick(async () => {
        await this.autoform.fApi.setValue(row)
      })
    },
    async onEditSubmit() {
      await this.autoform.fApi.validate(async (valid, fail) => {
        if (valid) {
          const formData = this.autoform.fApi.formData();
          const res = await editExitCostAveragePriceAsync(formData);
          if (res.data == true) {
            this.$message.success('修改成功！');
            this.getjSpeedDriveList();
            this.editVisible = false;
          }
          else {
            this.$message.error('该平台当月数据已存在。')
          }
        }
      })
    },
    async cancelEditSubmit() {
      this.editVisible = false;
    },
    async initform() {
      this.autoform.rule = [{ type: 'hidden', field: 'id', title: 'id', value: '' },
      { type: 'select', field: 'platForm', title: '平台', value: '', col: { span: 6 }, options: [{ value: 12, label: 'SHEIN全托' }, { value: 16, label: 'SHEIN自营' }, { value: 19, label: 'SHEIN半托' }, { value: 13, label: 'TEMU全托' }, { value: 15, label: 'TEMU半托' }, { value: 23, label: '跨境分销' }], props: { clearable: true, disabled: true }, validate: [{ type: 'number', required: true, message: '请选择平台' }] },
      { type: 'input', field: 'scale', title: '均价', value: '', props: { min: 0, precision: 0, maxlength: 8 }, validate: [{ pattern: /(^-?[1-9]\d*\.\d+$|^-?0\.\d+$|^-?[1-9]\d*$|^0$)/, message: "输入均价的格式不正确！" }, { required: true, message: '请输入均价' }] },
      { type: 'DatePicker', field: 'effectiveTime', title: '生效日期', value: '', validate: [{ type: 'string', required: true, message: '请输入生效日期' }], props: { type: 'date', format: 'yyyy-MM-dd', placeholder: '' }, col: { span: 8 } },

      ],
        this.autoformAdd.rule1 = [
          { type: 'select', field: 'platForm', title: '平台', value: '', col: { span: 6 }, options: [{ value: 12, label: 'SHEIN全托' }, { value: 16, label: 'SHEIN自营' }, { value: 19, label: 'SHEIN半托' }, { value: 13, label: 'TEMU全托' }, { value: 15, label: 'TEMU半托' }, { value: 23, label: '跨境分销' }], props: { clearable: true, disabled: false }, validate: [{ type: 'number', required: true, message: '请选择平台' }] },
          { type: 'input', field: 'scale', title: '均价', value: '', props: { min: 0, precision: 0, maxlength: 8 }, validate: [{ pattern: /(^-?[1-9]\d*\.\d+$|^-?0\.\d+$|^-?[1-9]\d*$|^0$)/, message: "输入均价的格式不正确！" }, { required: true, message: '请输入均价' }] },
          { type: 'DatePicker', field: 'effectiveTime', title: '生效日期', value: '', validate: [{ type: 'string', required: true, message: '请输入生效日期' }], props: { type: 'date', format: 'yyyy-MM-dd', placeholder: '' }, col: { span: 8 } },
        ]
    },

    setplatform(platform) {
      this.platform = platform;
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getjSpeedDriveList();
    },
    async getjSpeedDriveList() {
      const para = { ...this.Filter };
      if (this.Filter.timerange) {
        para.effectiveStartTime = this.Filter.timerange[0];
        para.effectiveEndTime = this.Filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
        isCrossBorder: 1

      };
      this.listLoading = true;
      const res = await getExitCostAveragePrice(params);
      this.listLoading = false;
      this.total = res.data.total
      this.dahuixionglist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>