<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" ref="form" label-width="140px" label-position="right" >  
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="用户：">
                            {{ userName }}
                        </el-form-item>
                    </el-col>                   
                </el-row>                 
                <el-row>     
                    
                    <el-col :span="24">
                        <el-form-item label="聚水潭主账户：" prop="jstUserName" >
                              <el-input  v-model="form.jstUserName" clearable maxlength="25"  />
                        </el-form-item>
                    </el-col>   
                </el-row>                 
                <el-row>  
                    <el-col :span="24">
                        <el-form-item label="更多聚水潭账户(以英文逗号分隔)：" prop="jstUserNames" >
                              <el-input  type="textarea"  v-model="form.jstUserNames" clearable maxlength="200"  show-word-limit  />
                        </el-form-item>
                    </el-col>   
                </el-row>   
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">  
                    <el-button @click="onClose">关闭</el-button>                     
                    <el-button v-if="mode<3 " type="primary" @click="onSave(true)">保存</el-button>   
                </el-col>
            </el-row>
        </template>

    </my-container>
</template>
<script>  

    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";

    import { GetUserExtInfo,SaveUserExtInfo } from '@/api/admin/deptuser'

    export default {
        name: "UserExtInfoForm",
        components: { MyContainer, MyConfirmButton,  },
        data() {
            return {              
                that: this,
                mode:3,
                userName:'',
                form: {
                    
                },            
              
                pageLoading: false,             
                formEditMode: true,//是否编辑模式     
            };
        },
        async mounted() {          
        },
        computed: {   
        },
        methods: {             
        
            onClose(){
                this.$emit('close');
            },  
            async onSave(isClose){
                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData({dduserId,userName,mode }) {     
                let self=this;         
                self.pageLoading = true;
                self.formEditMode = mode!=3;
                self.mode = mode;     
                self.userName=userName;


                let rlt = await GetUserExtInfo( {dduserId:dduserId} );
                if (rlt && rlt.success) {
                    let formDto= rlt.data;                     
                    this.form = formDto;  
                    this.pageLoading = false;
                }else{
                    this.onClose();
                }
                self.pageLoading = false;
            },
            async save() {
                this.pageLoading = true;
                
                let saveData = { ...this.form };   
                try {
                    await this.$refs["form"].validate();
                } catch (error) {
                    this.pageLoading = false;
                    return false;
                } 

                let rlt = await SaveUserExtInfo(saveData);
                if (rlt && rlt.success) {
                    this.$message.success('保存成功！');           
                }

                this.pageLoading = false;
              
                return (rlt && rlt.success);
            }
        },
    };
</script>
