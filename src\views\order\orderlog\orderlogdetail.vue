<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="">
                    <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" :picker-options="pickerOptions">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.orderNoInner" style="width: 140px" placeholder="内部单号" @keyup.enter.native="onSearch" clearable maxlength="40" />
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.operator" style="width: 140px" placeholder="操作人" @keyup.enter.native="onSearch" clearable maxlength="50" />
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.logName" style="width: 140px" placeholder="名称" @keyup.enter.native="onSearch" clearable maxlength="20" />
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.remark" style="width: 140px" placeholder="内容" @keyup.enter.native="onSearch" clearable maxlength="20" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false" :loading="listLoading">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </my-container>
</template>

<script>
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import dayjs from "dayjs";
    import { formatTime } from "@/utils";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import { getOrderActionRecordJstPageList } from '@/api/order/orderlog';
    const tableCols = [
        { istrue: true, prop: 'operationTime', label: '时间', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'recordId', label: '日志ID', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'orderNoInner', label: '内部单号', width: '150', sortable: 'custom',type:'orderLogInfo',orderType:'orderNoInner'  },
        { istrue: true, prop: 'operator', label: '操作人', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'name', label: '名称', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'remark', label: '内容', sortable: 'custom' },
    ]
    export default {
        name: 'intransitdetail',
        components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow },
        props: {

        },
        data() {
            return {
                that: this,
                filter: {
                    orderNoInner: null,
                    operator: null,
                    remark: null,
                    logName: null,
                    timerange: [
                        formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                        formatTime(new Date(), "YYYY-MM-DD"),
                    ],
                    startDate: null,
                    endDate: null,
                },
                list: [],
                summaryarry: {},
                pager: { OrderBy: "operationTime", IsAsc: false },
                tableCols: tableCols,
                total: 0,
                sels: [],
                listLoading: false,
                pageLoading: false,
                pickerOptions: {
                    shortcuts: [
                        {
                            text: '昨天',
                            onClick(picker) {
                                const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                                const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                                start.setTime(start.getTime());
                                picker.$emit('pick', [start, start]);
                            }
                        }, {
                            text: '近三天',
                            onClick(picker) {
                                const tdate = new Date(new Date().getTime());
                                const end = new Date(new Date(tdate.toLocaleDateString()));
                                const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                                end.setTime(end.getTime() - 3600 * 1000 * 24);
                                picker.$emit('pick', [start, end]);
                            }
                        }, {
                            text: '近一周',
                            onClick(picker) {
                                const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                                const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
                                const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                                end.setTime(end.getTime() - 3600 * 1000 * 24);
                                picker.$emit('pick', [start, end]);
                            }
                        }, {
                            text: '近一个月',
                            onClick(picker) {
                                const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                                console.log("获取前一个月的时间", tdate.getDay());
                                const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                                const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                                start.setTime(start.getTime() - 3600 * 1000 * 24);
                                end.setTime(end.getTime() - 3600 * 1000 * 24);
                                picker.$emit('pick', [start, end]);
                            }
                        }]
                },
            };
        },
        async mounted() {
            await this.onSearch()
        },
        methods: {
            //查询第一页
            async onSearch() {
                console.log(this.filter)
                if (!this.filter.timerange) {
                    this.$message({ message: "请选择日期", type: "warning", });
                    return;
                }
                this.$refs.pager.setPage(1)
                await this.getlist();
            },
            //获取查询条件
            getCondition() {
                this.filter.startDate = null;
                this.filter.endDate = null;
                if (this.filter.timerange && this.filter.timerange.length > 1) {
                    this.filter.startDate = this.filter.timerange[0];
                    this.filter.endDate = this.filter.timerange[1];
                }
                else {
                    this.$message({ message: "请先选择时间", type: "warning" });
                    return false;
                }
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }
                return params;
            },
            //分页查询
            async getlist() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true
                const res = await getOrderActionRecordJstPageList(params)
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                //this.summaryarry = res.data.summaryarry;
                console.log(res.data, 'resdata')
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
            },
            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selectchange: function (rows, row) {
                this.selids = []; console.log(rows)
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
        },
    };
</script>

<style lang="scss" scoped></style>
