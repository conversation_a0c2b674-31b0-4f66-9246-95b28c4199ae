<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
            <div class="top">
                <el-button-group>
                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false"
                            :picker-options="pickerOptions" style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.status" placeholder="状态" clearable style="width: 100px;">
                            <el-option label="审批中" value="审批中" />
                            <el-option label="已审批" value="已审批" />
                            <el-option label="已拒绝" value="已拒绝" />
                            <el-option label="已撤销" value="已撤销" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <inputYunhan :key="'1'" :keys="'one'" :width="'150px'" ref="childApproveId" :maxRows="100" :maxlength="3000"
                            :inputt.sync="filter.approveIds" v-model.trim="filter.approveIds" placeholder="审批编号"
                            :clearable="true" @callback="callback1" title="审批编号" >
                        </inputYunhan>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-input v-model="filter.deptName" placeholder="所在部门" clearable style="width: 100px" maxlength="40" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-input v-model="filter.originatorUserName" placeholder="提交人" clearable
                            style="width: 100px" maxlength="40"/>
                    </el-button>
                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-input v-model="filter.originatorLeaderNames" placeholder="组长" clearable
                            style="width: 100px" maxlength="40"/>
                    </el-button>

                    <!-- <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.warehouseName" clearable placeholder="进货仓库" style="width: 120px">
                            <el-option label="义乌诚信仓" value="义乌诚信仓" />
                            <el-option label="南昌-昌东仓" value="南昌-昌东仓" />
                            <el-option label="南昌-定制仓" value="南昌-定制仓" />
                        </el-select>
                    </el-button> -->
                    
                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.warehouseNameList" multiple collapse-tags clearable filterable placeholder="请选择仓库"
                            style="width: 300px">
                            <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                                :value="item.name" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <inputYunhan :key="'2'" :keys="'two'" :width="'150px'" ref="childIndexNo" :maxRows="100" :maxlength="3000"
                            :inputt.sync="filter.indexNos" v-model.trim="filter.indexNos" placeholder="ERP采购单编号"
                            :clearable="true" @callback="callback2" title="ERP采购单编号">
                        </inputYunhan>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.refType" clearable placeholder="对公/对私" style="width: 100px">
                            <el-option label="对公" value="对公" />
                            <el-option label="对私" value="对私" />
                        </el-select>
                    </el-button>

                    <el-button type="primary" @click="onSearch()">查询</el-button>
                    <el-button type="primary" @click="onExport()">导出</el-button>
                </el-button-group>
            </div>
        </template>
        <vxetablebase :id="'PurchaseOrderNewApprove202408041727'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :border="true"
            @sortchange='sortchange' @select='selectchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="listLoading"
            :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="100" fixed="right">
                    <template #default="{ row }">
                        <div style="display: flex">
                            <el-button type="text" @click="getAuditList(row.id)" v-throttle="1000">查看审批节点</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

        <el-dialog title="审批记录" :visible.sync="auditVisible" width="30%" :close-on-click-modal="false" v-dialogDrag>
            <el-timeline>
                <el-timeline-item v-for="(activity, index) in activities" :key="index" :timestamp="activity.date">
                    {{ activity.userId + '-' + activity.result + (activity.remark ? '-' + activity.remark : '') }}
                </el-timeline-item>
            </el-timeline>
        </el-dialog>
    </MyContainer>
</template>

<script>
import inputYunhan from "@/components/Comm/inputYunhan";
import MyContainer from "@/components/my-container";
import datepicker from '@/views/customerservice/datepicker';
import { pickerOptions, formatLink } from '@/utils/tools';
import dayjs from 'dayjs';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetPurchaseOrderNewApprovePageList, GetPurchaseOrderNewApproveRecord, ExportPurchaseOrderNewApprovePageList
} from '@/api/inventory/purchaseordernew'
import { getAllWarehouse } from '@/api/inventory/warehouse'
const tableCols = [
    { sortable: 'custom', width: '130', align: 'center', prop: 'createdTime', label: '提交时间', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'status', label: '状态', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'approveId', label: '审批编号', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'deptName', label: '所在部门', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'originatorUserName', label: '提交人', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'originatorLeaderNames', label: '组长', },
    { sortable: 'custom', width: '200', align: 'center', prop: 'styleCode', label: '名称', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'totalAmont', label: '合计金额', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'warehouseName', label: '进货仓库', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'indexNo', label: 'ERP采购单编号', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'approveDeptType', label: '发起部门', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'payWay', label: '支付方式', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'payAccountName', label: '账户名', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'payAccount', label: '账号', },
    { sortable: 'custom', width: '200', align: 'center', prop: 'remark', label: '备注', },
    { sortable: 'custom', width: '140', align: 'center', prop: 'orderNo', label: '订单编号', },
    { sortable: 'custom', width: '140', align: 'center', prop: 'detailLink', label: '点击查看编码详情', type: 'html', formatter: (row) => formatLink('点击跳转查看', row.detailLink) },
    { sortable: 'custom', width: '80', align: 'center', prop: 'refType', label: '对公对私', },
    { width: '80', align: 'center', prop: 'payTypeImageUrl', type: 'images', label: '对私凭证', },
    { width: '80', align: 'center', prop: 'purImageUrl', type: 'images', label: '图片', },
    { sortable: 'custom', width: '130', align: 'center', prop: 'approveTime', label: '审批时间', },
    { sortable: 'custom', width: '200', align: 'center', prop: 'rejectReason', label: '审批拒绝原因', },
];
export default {
    name: "PurchaseOrderNewApprove",
    components: {
        MyContainer, datepicker, vxetablebase, inputYunhan
    },
    data() {
        return {
            auditVisible: false,
            activities: [],
            timeRanges: [],
            that: this,
            pickerOptions,
            filter: {
                timerange: [],
                startDate: null,
                endDate: null,
                status: null,
                warehouseNameList: [],
            },
            pager: { OrderBy: "createdTime", IsAsc: false },
            tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            warehouselist: [],
        }
    },
    async mounted() {
        await this.getWareList();
        this.onSearch();
    },
    computed: {
    },
    methods: {
        async getWareList() {
            var res3 = await getAllWarehouse();
            this.warehouselist = res3.data.filter((x) => x.name.indexOf('代发') < 0);
        },
        async callback1(val) {
            this.filter.approveIds = val;
        },
        async callback2(val) {
            this.filter.indexNos = val;
        },
        async getAuditList(id) {
            this.listLoading = true;
            const { data, success } = await GetPurchaseOrderNewApproveRecord({ id });
            this.listLoading = false;
            if (success) {
                this.activities = data
                this.auditVisible = true
                console.log(data, 'data');
            }
        },
        onClearfilter() {
            Object.keys(this.filter).forEach(key => {
                if (key == "timerange" || key == "startDate" || key == "endDate") {
                    return;
                }
                if (key.indexOf("Num") > -1) {
                    this.filter[key] = undefined;
                }
                else {
                    this.filter[key] = null;
                }
            });
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getParam() {
            if (this.filter.timerange.length == 0) {
                //默认给时间
                this.filter.startDate = dayjs().subtract(6, 'day').format('YYYY-MM-DD')
                this.filter.endDate = dayjs().format('YYYY-MM-DD')
                this.filter.timerange = [this.filter.startDate, this.filter.endDate]
            }
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message.error('日期必填');
                return;
            }
            let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            const res = await GetPurchaseOrderNewApprovePageList(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await ExportPurchaseOrderNewApprovePageList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '新品采购单审批_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}
</style>
