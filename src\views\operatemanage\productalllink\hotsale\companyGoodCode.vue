<template>
    <MyContainer>
        <template #header>
            <div class="header_top">
                <el-input v-model="ListInfo.seriesCode" placeholder="系列编码" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
                <el-select v-model="ListInfo.brandIds" multiple collapse-tags clearable filterable placeholder="请选择采购员"
                    class="publicCss">
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.poStatus" placeholder="采购单状态" clearable class="publicCss">
                    <el-option v-for="item in poStatusList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-button type="primary" @click="getList(true)">搜索</el-button>
                <el-button type="primary" @click="bulkRestocking(false)"
                    v-if="checkPermission('pageGetSeriesGoodsData')">一键进货</el-button>
            </div>
        </template>
        <vxetablebase :id="'companyGoodCode202408041703_1'" ref="table" @select="checkboxRangeEnd" :that='that'
            :isIndex='true' :hasexpand='true' v-loading="listLoading" :tablefixed='true' @sortchange='sortchange'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            :isDisableCheckBox="true" @checCheckboxkMethod="checCheckboxkMethod" style="width: 100%; margin: 0"
            height="100%">
            <template slot="right">
                <vxe-column title="拼多多">
                    <template #default="{ row, $index }">
                        <div class="btnBox">
                            <el-button type="text" :disabled="row.isPlatform != 'pdd' || row.isNotStock == 1"
                                v-if="!row.companyGoodsPdd" @click="bulkRestocking(true, row)">进货</el-button>
                            <div v-else>已上架</div>
                        </div>
                    </template>
                </vxe-column>
                <vxe-column title="淘系">
                    <template #default="{ row, $index }">
                        <div class="btnBox">
                            <el-button type="text" :disabled="row.isPlatform != 'tx' || row.isNotStock == 1"
                                v-if="!row.companyGoodsTb" @click="bulkRestocking(true, row)">进货</el-button>
                            <div v-else>已上架</div>
                        </div>
                    </template>
                </vxe-column>
                <vxe-column title="抖音">
                    <template #default="{ row, $index }">
                        <div class="btnBox">
                            <el-button type="text" :disabled="row.isPlatform != 'dy' || row.isNotStock == 1"
                                v-if="!row.companyGoodsDy" @click="bulkRestocking(true, row)">进货</el-button>
                            <div v-else>已上架</div>
                        </div>
                    </template>
                </vxe-column>
                <vxe-column title="分销">
                    <template #default="{ row, $index }">
                        <div class="btnBox">
                            <el-button type="text" :disabled="row.isPlatform != 'fx' || row.isNotStock == 1"
                                v-if="!row.companyGoodsFx" @click="bulkRestocking(true, row)">进货</el-button>
                            <div v-else>已上架</div>
                        </div>
                    </template>
                </vxe-column>
                <vxe-column title="京东">
                    <template #default="{ row, $index }">
                        <div class="btnBox">
                            <el-button type="text" :disabled="row.isPlatform != 'jd' || row.isNotStock == 1"
                                v-if="!row.companyGoodsJd" @click="bulkRestocking(true, row)">进货</el-button>
                            <div v-else>已上架</div>
                        </div>
                    </template>
                </vxe-column>
                <vxe-column title="阿里">
                    <template #default="{ row, $index }">
                        <div class="btnBox">
                            <el-button type="text" :disabled="row.isPlatform != 'al' || row.isNotStock == 1"
                                v-if="!row.companyGoodsAl" @click="bulkRestocking(true, row)">进货</el-button>
                            <div v-else>已上架</div>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog :visible.sync="warehouseVisible" width="70%" v-dialogDrag>
            <div class="goodCode">
                <div class="goodCode_item">
                    <div class="item_left">商品编码</div>
                    <el-tooltip class="item_right" effect="dark" :content="warehouseQuery.goodsCode"
                        placement="top-start">
                        <div>{{ warehouseQuery.goodsCode }}</div>
                    </el-tooltip>
                </div>
                <div class="goodCode_item">
                    <div class="item_left">商品名称</div>
                    <el-tooltip class="item_right" effect="dark" :content="warehouseQuery.goodsName"
                        placement="top-start">
                        <div>{{ warehouseQuery.goodsName }}</div>
                    </el-tooltip>
                </div>
            </div>
            <vxetablebase :id="'companyGoodCode202408041703_2'" ref="detailTable" :tableData="warehouseTableData"
                :tableCols="warehouseCols" :is-index="true" :that="that" :showsummary="true"
                :summaryarry="warehouseSummary" style="width: 100%; height: 500px;margin-top: 30px;"
                @sortchange='warehouseSortchange' class="detail">
            </vxetablebase>
            <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="warehouseTotal"
                @page-change="warehousePagechange" @size-change="warehouseSizechange" style="margin-top: 40px;" />
        </el-dialog>

        <el-dialog :visible.sync="goodCodeVisible" width="70%" v-dialogDrag>
            <div class="goodCode">
                <div class="goodCode_item">
                    <div class="item_left">商品编码</div>
                    <el-tooltip class="item_right" effect="dark" :content="goodCodeLogQuery.goodsCode"
                        placement="top-start">
                        <div>{{ goodCodeLogQuery.goodsCode }}</div>
                    </el-tooltip>
                </div>
                <div class="goodCode_item">
                    <div class="item_left">商品名称</div>
                    <el-tooltip class="item_right" effect="dark" :content="goodCodeLogQuery.goodsName"
                        placement="top-start">
                        <div>{{ goodCodeLogQuery.goodsName }}</div>
                    </el-tooltip>
                </div>
            </div>
            <el-date-picker v-model="goodCodeTimeRange" type="daterange" unlink-panels range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                @change="changeGoodCodeTime" style="margin-bottom: 10px;">
            </el-date-picker>
            <vxetablebase :id="'companyGoodCode202408041703_3'" ref="detailTable" :tableData="goodCodeTableData"
                :tableCols="goodCodeCols" :is-index="true" :that="that" :showsummary="true"
                :summaryarry="goodCodeSummary" style="width: 100%; height: 500px" @sortchange='goodCodeSortchange'
                class="detail">
            </vxetablebase>
            <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="goodCodeTotal"
                @page-change="goodCodePagechange" @size-change="goodCodeSizechange" style="margin-top: 40px;" />
        </el-dialog>

        <el-dialog title="历史进货" :visible.sync="historyVisible" width="60%" v-dialogDrag>
            <historyGoodsProps ref="historyGoodsProps" :historyRestockeInfo="historyRestockeInfo"
                :applyTableData="applyTableData" :paltForm="historyRestockeInfo.platform" @getUserPlat="getUserPlat"
                @close="close" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, formatPlatform, formatSecondToHour, formatSecondNewToHour } from '@/utils/tools'
import { replaceSpace } from '@/utils/getCols'
import middle from '@/store/middle.js'
import dayjs from 'dayjs'
import historyGoodsProps from './historyGoodsProps.vue'
import {
    pageGetSeriesGoodsData,
    getGoodsSalesByGoodsCode,
    pageGetPurchaseDataByGoodsCode,
    getUserPlatform,
} from '@/api/inventory/companyGoods'
import { getAllProBrand } from '@/api/inventory/warehouse'
const tableCols = [
    { istrue: true, label: '', type: "checkbox" },
    { istrue: true, prop: 'picture', label: '图片', type: 'images', fixed: 'left' },
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', fixed: 'left', width: '150', },
    { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', fixed: 'left', width: '150', tootip: '暂不支持进货', type: 'htmlOther', isBrage: (row) => row.isNotStock == 1, handle: (that, row) => that.goodCodeLog(row, true) },
    { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom', width: '120', },
    { istrue: true, prop: 'sellStock', label: '可售库存', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'poStatus', label: '采购单状态', sortable: 'custom', width: '150' },
    {
        istrue: true, type: 'button', label: '仓库', btnList: [
            { label: "查看", handle: (that, row) => that.warehouseLog(row, true) },
        ]
    },
    { istrue: true, prop: 'brandName', label: '采购', sortable: 'custom', width: '120', },
    { istrue: true, prop: 'lastInTransitTime', label: '最近在途时长', sortable: 'custom', width: '150', formatter: (row) => formatSecondNewToHour(row.lastInTransitTime) },
    { istrue: true, prop: 'createdTime', label: '编码创建日期', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'groupName', label: '运营组', sortable: 'custom', width: '150' },
]

const goodCodeCols = [
    { istrue: true, prop: 'rptDate', label: '日期', sortable: 'custom', formatter: (row) => dayjs(row.rptDate).format('YYYY-MM-DD') },
    { istrue: true, prop: 'proCode', label: '产品ID', sortable: 'custom' },
    { istrue: true, prop: 'onTime', label: '上架日期', formatter: (row) => row.onTime ? dayjs(row.onTime).format('YYYY-MM-DD') : null },
    { istrue: true, prop: 'platform', label: '平台', sortable: 'custom', formatter: (row) => formatPlatform(row.platform) },
    { istrue: true, prop: 'groupId', label: '运营组', sortable: 'custom', formatter: (row) => row.groupName },
    { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', sortable: 'custom', formatter: (row) => row.operateSpecialUserName },
    { istrue: true, prop: 'orderCount', label: '订单量', sortable: 'custom' },
    { istrue: true, prop: 'saleAmount', label: '销售金额', sortable: 'custom' },
]

const warehouseCols = [
    { istrue: true, prop: 'day', label: '日期', formatter: (row) => dayjs(row.day).format('YYYY-MM-DD') },
    { istrue: true, prop: 'wmsCoId', label: '仓库', formatter: (row) => row.wmsName },
    { istrue: true, prop: 'warehouseStock', label: '库存', sortable: 'custom', },
    { istrue: true, prop: 'inTransitNum', label: '在途', sortable: 'custom', },
]

const poStatusList = [
    { label: '未进货', value: '未进货' },
    { label: '待分配', value: '待分配' },
    { label: '待审核', value: '待审核' },
    { label: '已确认', value: '已确认' },
    { label: '作废', value: '作废' },
    { label: '完成', value: '完成' },
]

export default {
    name: "companyGoodCode",
    components: {
        MyContainer, vxetablebase, historyGoodsProps
    },
    data() {
        return {
            pickerOptions,
            that: this,
            tableCols,
            tableData: [],
            ListInfo: {
                currentPage: 1,
                pagesize: 50,
                orderBy: null,
                isAsc: false,
                seriesCode: null,
                goodsCode: null,
                brandIds: null,
                poStatus: null,
            },
            poStatusList,
            total: 0,
            listLoading: true,
            goodCodeVisible: false,
            goodCodeLogQuery: {
                currentPage: 1,
                pagesize: 50,
                orderBy: null,
                isAsc: false,
                goodsCode: null,
                endDate: null,
                startDate: null,
                goodsName: null,
            },
            goodCodeTimeRange: [],
            warehouseTimeRange: [],
            goodCodeCols,
            goodCodeTableData: [],
            goodCodeSummary: {},
            goodCodeTotal: 0,
            brandlist: [],
            warehouseCols,
            warehouseVisible: false,
            warehouseTableData: [],
            warehouseTotal: 0,
            warehouseQuery: {
                currentPage: 1,
                pagesize: 50,
                orderBy: null,
                isAsc: false,
                goodsCode: null,
                endDate: null,
                startDate: null,
                goodsCode: null,
                goodsName: null,
            },
            warehouseSummary: {},
            historyRestockeInfo: {
                currentPage: 1,
                pagesize: 50,
                orderBy: null,
                isAsc: false,
                platform: null,
                goodsCodes: []
            },
            historyVisible: false,
            applyTableData: [],
            goodCode: null,
            goodName: null,
        }
    },
    async mounted() {
        middle.$on('linkToGoodCode', (data) => {
            this.ListInfo.seriesCode = data
            this.ListInfo.goodsCode = null
            this.ListInfo.brandIds = null
            this.ListInfo.poStatus = null
            this.getUserPlat()
        })
        this.getUserPlat()
        const { data } = await getAllProBrand();
        this.brandlist = data.map(item => {
            return { value: item.key, label: item.value };
        });
    },

    //销毁事件
    beforeDestroy() {
        middle.$off('linkToGoodCode')
    },
    methods: {
        checCheckboxkMethod(row, callback) {
            let isNotStock = row.isNotStock != 1
            callback(isNotStock)
        },
        changeGoodCodeTime(e) {
            if (e) {
                this.goodCodeTimeRange = e
                this.goodCodeLogQuery.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                this.goodCodeLogQuery.endDate = dayjs(e[1]).format('YYYY-MM-DD')
                this.goodCodeLog(this.goodCodeLogQuery, false)
            } else {
                this.goodCodeTimeRange = []
                this.goodCodeLogQuery.startDate = null
                this.goodCodeLogQuery.endDate = null
                this.goodCodeLog(this.goodCodeLogQuery, false)
            }
        },
        close() {
            this.historyVisible = false
        },
        bulkRestocking(type, row) {
            if (type) {
                this.historyRestockeInfo.goodsCodes = [row.goodsCode]
                this.applyTableData = [row]
            }
            if (!this.historyRestockeInfo.platform) return this.$message.error('您没有权限进货')
            if (this.historyRestockeInfo.goodsCodes.length == 0) return this.$message.error('请勾选商品')

            this.$confirm('此操作将一键进货, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.historyVisible = true
                this.$nextTick(() => {
                    this.$refs.historyGoodsProps.getList()
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消进货'
                });
            });
        },
        async getUserPlat() {
            const { data, success } = await getUserPlatform()
            if (success) {
                this.historyRestockeInfo.platform = data ? data : null
                this.getList()
            } else {
                this.getList()
            }
        },
        checkboxRangeEnd(rows) {
            this.historyRestockeInfo.goodsCodes = rows.map(item => item.goodsCode);
            this.applyTableData = rows;
        },
        //分页获取商品编码销售数据
        async goodCodeLog(row, type) {
            console.log(row, 'row');
            if (type) {
                // //时间为默认当前日期的前一天
                // this.goodCodeTimeRange = [dayjs().subtract(1, 'day').format('YYYY-MM-DD'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')];
                // this.goodCodeLogQuery.startDate = this.goodCodeTimeRange[0]
                // this.goodCodeLogQuery.endDate = this.goodCodeTimeRange[1]
                this.goodCodeLogQuery.orderBy = null
                this.goodCodeLogQuery.isAsc = false
            }
            this.goodCodeLogQuery.goodsCode = row.goodsCode
            this.goodCodeLogQuery.goodsName = row.goodsName
            const { data, success } = await getGoodsSalesByGoodsCode(this.goodCodeLogQuery)
            if (success) {
                this.goodCodeTableData = data.list
                this.goodCodeTotal = data.total
                this.goodCodeSummary = data.summary
                this.goodCodeVisible = true
            }
        },
        async warehouseLog(row, type) {
            if (type) {
                //时间为默认当前日期的前一天
                this.warehouseTimeRange = [dayjs().subtract(1, 'day').format('YYYY-MM-DD'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')];
                this.warehouseQuery.startDate = this.warehouseTimeRange[0]
                this.warehouseQuery.endDate = this.warehouseTimeRange[1]
                this.warehouseQuery.orderBy = null
                this.warehouseQuery.isAsc = false
            }
            this.warehouseQuery.goodsCode = row.goodsCode
            this.warehouseQuery.goodsName = row.goodsName
            const { data, success } = await pageGetPurchaseDataByGoodsCode(this.warehouseQuery)
            if (success) {
                this.warehouseTableData = data.list
                this.warehouseTotal = data.total
                this.warehouseSummary = data.summary
                this.warehouseVisible = true
            }
        },
        warehouseSizechange(val) {
            this.warehouseQuery.currentPage = 1;
            this.warehouseQuery.pagesize = val;
            this.warehouseLog(this.warehouseQuery, false);
        },
        warehousePagechange(val) {
            this.warehouseQuery.currentPage = val;
            this.warehouseLog(this.warehouseQuery, false);
        },
        goodCodeSizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pagesize = val;
            this.goodCodeLog(this.goodCodeLogQuery, false);
        },
        goodCodePagechange(val) {
            this.goodCodeLogQuery.currentPage = val;
            this.goodCodeLog(this.goodCodeLogQuery, false);
        },
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pagesize = val;
            this.getList();
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },
        async getList(isSearch) {
            if (isSearch) {
                this.ListInfo.currentPage = 1
                this.ListInfo.pagesize = 50
            }
            this.listLoading = true
            const replaceArr = ['seriesCode', 'goodsCode']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            const { data, success } = await pageGetSeriesGoodsData(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.tableData.forEach(item => {
                    item.isPlatform = this.historyRestockeInfo.platform
                })
                console.log(this.tableData, 'this.tableData222222');
                this.total = data.total
                this.listLoading = false
            }
        },
        //排序查询
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        goodCodeSortchange({ order, prop }) {
            if (prop) {
                this.goodCodeLogQuery.orderBy = prop
                this.goodCodeLogQuery.isAsc = order.indexOf("descending") == -1 ? true : false
                this.goodCodeLog(this.goodCodeLogQuery, false)
                console.log(this.goodCodeLogQuery, 'this.goodCodeLogQuery');
            }
        },
        warehouseSortchange({ order, prop }) {
            if (prop) {
                this.warehouseQuery.orderBy = prop
                this.warehouseQuery.isAsc = order.indexOf("descending") == -1 ? true : false
                this.warehouseLog(this.warehouseQuery, false)
            }
        },
    }
}
</script>

<style scoped lang="scss">
.header_top {
    display: flex;
    margin-bottom: 10px;
}

.publicCss {
    width: 220px;
    margin-right: 10px;
}

.btnGroup {
    padding: 20px 50px 0;
    display: flex;
    justify-content: space-between;
}

.btnBox {
    display: flex;
}

.goodCode {
    margin-top: 20px;
    display: flex;
    // height: 40px;
    line-height: 40px;
    box-sizing: border-box;

    .goodCode_item {
        width: 50%;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;

        .item_left {
            width: 50%;
            background-color: #ccc;
            text-align: center;
            border: #ccc 1px solid;
        }

        .item_right {
            width: 50%;
            text-align: center;
            border: #ccc 1px solid;
            //文字超出隐藏
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>