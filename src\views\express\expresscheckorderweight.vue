<template>
    <my-container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="订单支付时间">
                    <el-date-picker style="width: 250px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :clearable="true" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item label="快递单号:">
                    <el-input v-model.trim="filter.expressNo" placeholder="快递单号" class="publicCss" maxlength="200" clearable />
                </el-form-item>
                <el-form-item label="内部订单号:">
                    <el-input v-model.trim="filter.orderInnerNo" style="width: 150px" placeholder="内部订单号" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="warning" @click="setWarningValue">设置</el-button>
                </el-form-item>
            </el-form>
        </template>

        <el-dialog title="设置" :visible.sync="dialog.visiable" width="20%" v-dialogDrag>
            <div class="dialogBox">
                <div class="dialogTop">  
                    <div>设置预警值：</div>
                    <el-input-number v-model="dialog.warningValue" type="number" placeholder="预警值" :controls="false"
                        :max="100" :min="0" :precision="0" style="width: 100px;margin: 0 5px;"></el-input-number>
                    <div>%</div>
                </div>
                <div class="btnBox">
                    <el-button @click="dialog.visiable = false" style="margin-right: 20px;">取消</el-button>
                    <el-button type="primary" @click="saveWarningValue">确定</el-button>
                </div>
            </div>
        </el-dialog>

        <vxetablebase :id="'expresscheckorderweight202408041526'" ref="table" :tableData="list" :tableCols="tableCols" :is-index="true" :that="that"
            style="width: 100%;  margin: 0" @sortchange='sortchange' :height="'100%'" :showsummary="true"
            :summaryarry="summaryarry" v-loading="listLoading">
            <template slot="right">
                <vxe-column title="操作" width="110" align="center">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <el-button type="text" v-if="row.isThenSetWarning == 1 && row.verifyStatus=='待核'" @click="verifyGoods(row)">通知复检</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getlist" />
        </template>

    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { queryOrderInfoListAsync,getExpressDictionValue,setExpressDictionValue,verifyGoodsByInnerOrderNo } from "@/api/express/expressorderinfo"
import { getExpressComanyAll } from '@/api/express/express'
import { formatWarehouse, warehouselist } from "@/utils/tools";

const tableCols = [
    { istrue: true, prop: 'expressPackageUrl', label: '包裹照片', tipmesg: '', type: 'danimages', width: '100', },
    { istrue: true, prop: 'expressCompany', label: '快递公司', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'expressNo', label: '快递单号', tipmesg: '', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'orderInnerNo', label: '内部订单号', tipmesg: '', width: '200', sortable: 'custom', }, 
    { istrue: true, prop: 'orderPayTime', label: '订单支付时间', width: '150', formatter: (row) => row.createdTime == null ? null : formatTime(row.orderPayTime, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'expressFee', label: '快递费用', tipmesg: '', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'predictWeight', label: '聚水潭商品重量', tipmesg: '', width: '160', sortable: 'custom', },
    { istrue: true, prop: 'actualWeight', label: '快递公司重量', tipmesg: '', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'marginWeight', label: '重量差值', tipmesg: '', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'deviationRate', label: '预警', tipmesg: '', width: '120', sortable: 'custom',formatter:(row)=>{return row.deviationRate? row.deviationRate+"%":""} },
    { istrue: true, prop: 'verifyWeight', label: '修正后重量', tipmesg: '', sortable: 'custom' }
]

const tableHandles = [

];

const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminEcpresscheckorder',
    components: { MyContainer, MyConfirmButton, vxetablebase },
    data() {
        return {
            that: this,
            filter: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
                expressNo: null,
                orderInnerNo: null
            },
            warehouselist: warehouselist,
            expresscompanylist: [],
            list: [],
            summaryarry: {},
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            pager: { OrderBy: "", IsAsc: false },
            listLoading: false,
            dialog: {
                visiable: false,
                warningValue: 0
            },
            pickerOptions: {
                shortcuts: [{
                    text: '近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近六个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            }
        };
    },

    async mounted() {
        await this.onSearch()
        await this.getExpressComanyList();
    },

    methods: {
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await queryOrderInfoListAsync(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            this.list = data
        },
        async getExpressComanyList() {
            const res = await getExpressComanyAll({});
            if (!res?.success) {
                return;
            }
            const data = res.data;
            this.expresscompanylist = data;
        },
        async sortchange(column) {
            if (!column.order) {
                this.pager = {};
            }
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        async setWarningValue() { 
            let obj = { key : "ExpressOrderWeight"};
            let rlt = await getExpressDictionValue(obj);
            if (rlt.success) {
                this.dialog.warningValue = rlt.data;
                this.dialog.visiable = true;
            } 
        },
        async saveWarningValue() {
            let saveData = { key : "ExpressOrderWeight", value: this.dialog.warningValue };
            let rlt = await setExpressDictionValue(saveData);
            if (rlt && rlt.success) {
                this.$message.success('保存成功！');
                this.dialog.warningValue = null;
                this.dialog.visiable = false;
                await this.getlist();
            }
        },
        async verifyGoods(row) { 
            let rlt = await verifyGoodsByInnerOrderNo({ExpressNo:row.expressNo});
            if (rlt && rlt.success) {
                this.$message.success('通知复检成功！');
                await this.getlist();
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.dialogBox {
    display: flex;
    flex-direction: column;
    align-items: center;
    line-height: 28px;
}

.dialogTop {
    display: flex;
    margin-bottom: 20px;
    justify-content: end;
}
</style>