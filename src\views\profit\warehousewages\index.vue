<template>
    <my-container v-loading="pageLoading" style="height: 100%">
        <el-tabs v-model="activeName" style="height: 94%">
            <el-tab-pane label="薪资统计" name="first1" style="height: 100%">
                <warehousewagescompute ref="warehousewagescompute" style="height: 100%" :myWarehouseList="baseWarehouseList"
                    :myOnePostNameList="baseOnePostNameList"></warehousewagescompute>
            </el-tab-pane>
            <el-tab-pane label="趋势图" name="first1-1" style="height: 100%" lazy>
                <warehousewageschat ref="warehousewageschat" style="height: 100%" :myWarehouseList="baseWarehouseList"
                    :myOnePostNameList="baseOnePostNameList"></warehousewageschat>
            </el-tab-pane>
            <el-tab-pane label="岗位" name="first2" style="height: 100%" lazy>
                <warehousepostwages ref="warehousepostwages" style="height: 100%" :myWarehouseList="baseWarehouseList"
                    :myOnePostNameList="baseOnePostNameList">
                </warehousepostwages>
            </el-tab-pane>
            <el-tab-pane label="岗位" name="first3" style="height: 100%" v-if="false" lazy>
                <warehousepostaccount ref="warehousepostaccount" style="height: 100%">
                </warehousepostaccount>
            </el-tab-pane>
            <el-tab-pane label="订单工作量" name="first4" style="height: 100%" v-if="false" lazy>
                <warehouseorderwork ref="warehouseorderwork" style="height: 100%"></warehouseorderwork>
            </el-tab-pane>
            <el-tab-pane label="卸货工作量" name="first5" style="height: 100%" v-if="false" lazy>
            </el-tab-pane>
            <el-tab-pane label="聚水潭数据源" name="first6" style="height: 100%" lazy>
                <warehouseuserworkdata ref="warehouseuserworkdata" style="height: 100%" :myWarehouseList="baseWarehouseList"
                    :myOnePostNameList="baseOnePostNameList"></warehouseuserworkdata>
            </el-tab-pane>
            <el-tab-pane label="聚水潭数据源明细" name="first62" style="height: 100%" lazy>
                <warehouseuserworkdatadtl ref="warehouseuserworkdatadtl" style="height: 100%"
                    :myWarehouseList="baseWarehouseList" :myOnePostNameList="baseOnePostNameList">
                </warehouseuserworkdatadtl>
            </el-tab-pane>
            <el-tab-pane label="常规计件" name="first7" style="height: 100%" lazy>
                <routinewages ref="routinewages" style="height: 100%" :myWarehouseList="baseWarehouseList"
                    :myOnePostNameList="baseOnePostNameList"></routinewages>
            </el-tab-pane>
            <el-tab-pane label="分拣计件" name="first8" style="height: 100%" lazy v-if="false">
                <sortingwages ref="sortingwages" style="height: 100%" :myWarehouseList="baseWarehouseList"
                    :myOnePostNameList="baseOnePostNameList"></sortingwages>
            </el-tab-pane>
            <el-tab-pane label="手工单计件" name="first81" style="height: 100%" lazy>
                <manualwages ref="manualwages" style="height: 100%" :myWarehouseList="baseWarehouseList"
                    :myOnePostNameList="baseOnePostNameList"></manualwages>
            </el-tab-pane>
            <el-tab-pane label="集包计件" name="first9" style="height: 100%" lazy>
                <sumpackwages ref="sumpackwages" style="height: 100%" :myWarehouseList="baseWarehouseList"
                    :myOnePostNameList="baseOnePostNameList"></sumpackwages>
            </el-tab-pane>
            <el-tab-pane label="集包或分摊人员" name="first10" style="height: 100%" lazy>
                <sumpackuser ref="sumpackuser" style="height: 100%" :myWarehouseList="baseWarehouseList"
                    :myOnePostNameList="baseOnePostNameList"></sumpackuser>
            </el-tab-pane>
            <el-tab-pane label="分摊薪资" name="first11" style="height: 100%" lazy>
                <warehousewagesavg ref="warehousewagesavg" style="height: 100%" :myWarehouseList="baseWarehouseList"
                    :myOnePostNameList="baseOnePostNameList">
                </warehousewagesavg>
            </el-tab-pane>
            <el-tab-pane label="在职人员" name="first12" style="height: 100%" lazy>
                <warehousemonthsalaryuser ref="warehousemonthsalaryuser" style="height: 100%"
                    :myWarehouseList="baseWarehouseList" :myOnePostNameList="baseOnePostNameList">
                </warehousemonthsalaryuser>
            </el-tab-pane>
            <el-tab-pane label="计时薪资" name="first13" style="height: 100%" lazy>
                <warehousemonthsalary ref="warehousemonthsalary" style="height: 100%" :myWarehouseList="baseWarehouseList"
                    :myOnePostNameList="baseOnePostNameList">
                </warehousemonthsalary>
            </el-tab-pane>
            <el-tab-pane label="卸货入库" name="first15" style="height: 100%" lazy>
                <videostoragewages ref="videostoragewages" style="height: 100%" :myWarehouseList="baseWarehouseList"
                    :myOnePostNameList="baseOnePostNameList">
                </videostoragewages>
            </el-tab-pane>
            <el-tab-pane label="仓库发件量" name="first14" style="height: 100%" lazy>
                <warehouseordercount ref="warehouseordercount" style="height: 100%" :myWarehouseList="baseWarehouseList"
                    :myOnePostNameList="baseOnePostNameList">
                </warehouseordercount>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
  
<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import warehouseuserworkdata from "@/views/profit/warehousewages/warehouseuserworkdata.vue";
import warehousepostwages from "@/views/profit/warehousewages/warehousepostwages.vue";
import warehousewagescompute from "@/views/profit/warehousewages/warehousewagescompute.vue";
import warehousepostaccount from "@/views/profit/warehousewages/warehousepostaccount.vue";
import warehouseorderwork from "@/views/profit/warehousewages/warehouseorderwork.vue";
import routinewages from "@/views/profit/warehousewages/routinewages.vue";
import sortingwages from "@/views/profit/warehousewages/sortingwages.vue";
import sumpackwages from "@/views/profit/warehousewages/sumpackwages.vue";
import sumpackuser from "@/views/profit/warehousewages/sumpackuser.vue";
import warehouseuserworkdatadtl from "@/views/profit/warehousewages/warehouseuserworkdatadtl.vue";
import warehousewagesavg from "@/views/profit/warehousewages/warehousewagesavg.vue";
import warehousemonthsalaryuser from "@/views/profit/warehousewages/warehousemonthsalaryuser.vue";
import warehousemonthsalary from "@/views/profit/warehousewages/warehousemonthsalary.vue";
import warehouseordercount from "@/views/profit/warehousewages/warehouseordercount.vue";
import manualwages from "@/views/profit/warehousewages/manualwages.vue";
import videostoragewages from "@/views/profit/warehousewages/videostoragewages.vue";
import warehousewageschat from "@/views/profit/warehousewages/warehousewageschat.vue";

import { getTbWarehouseList, getOnePostNameList } from '@/api/profit/warehousewages';

export default {
    name: "tailorlossindex",
    components: {
        cesTable, MyContainer, MyConfirmButton,
        warehouseuserworkdata,
        warehousepostwages,
        warehousewagescompute,
        warehousepostaccount,
        warehouseorderwork,
        routinewages, sortingwages, sumpackwages, sumpackuser, warehouseuserworkdatadtl, manualwages,
        warehousewagesavg, warehousemonthsalaryuser, warehousemonthsalary, warehouseordercount, videostoragewages,
        warehousewageschat,
    },
    data() {
        return {
            that: this,
            pageLoading: false,
            activeName: "first1",
            baseWarehouseList: [],
            baseOnePostNameList: [],
        };
    },
    created() {
    },
    async mounted() {
        await this.getWarehouseList();
        await this.getOnePostNameList();
    },
    methods: {
        async getWarehouseList() {
            this.baseWarehouseList = [];
            const res = await getTbWarehouseList();
            if (res && res.length > 0) {
                for (let i = 0; i < res.length; i++) {
                    this.baseWarehouseList.push({ label: res[i].name, value: res[i].wms_co_id })
                }
            }
        },
        async getOnePostNameList() {
            this.baseOnePostNameList = [];
            const res = await getOnePostNameList();
            if (res && res.length > 0) {
                for (let i = 0; i < res.length; i++) {
                    this.baseOnePostNameList.push({ label: res[i], value: res[i] })
                }
            }
        },
    },
};
</script>
  
<style lang="scss" scoped></style>
  