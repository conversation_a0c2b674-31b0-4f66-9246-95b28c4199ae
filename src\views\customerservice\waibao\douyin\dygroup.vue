<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='grouplist' @select='selectchange' :isSelection='true' :tableCols='tableCols'
            :loading="listLoading">

              <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.groupNameList" placeholder="组名称" clearable filterable multiple :collapse-tags="true">
                            <el-option v-for="item in filterGroupList" :key="item" :label="item"    :value="item">
                            </el-option>
                        </el-select>
                    </el-button>
                     <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.sname" placeholder="姓名" style="width:160px;" clearable :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.snick" placeholder="昵称" style="width:160px;" clearable :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.shopName" placeholder="店铺" style="width:160px;" clearable :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-switch :width="40" @change="changeingroup" v-model="filter.isleavegroup"
                            inactive-color="#228B22" active-text="包含离组" inactive-text="当前在组">
                        </el-switch>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onAddGroupShow" v-if="checkPermission(['api:Customerservice:OutSource:AddOutSourceGroupInfo1'])" >添加</el-button>
                    <el-button type="primary" @click="onImportSyj" v-if="checkPermission(['api:Customerservice:OutSource:ImportOSGroupAsync1'])" >导入</el-button>
                    <el-button type="primary" @click="onImportSyjModel">下载模板</el-button>
                    <el-button type="primary" @click="batchLeaveGroup" v-if="checkPermission(['api:Customerservice:OutSource:UpdateGroupBatchLeave1'])" >批量离组</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getgroupList" />
        </template>

        <el-dialog title="添加客服人员分组管理信息" :visible.sync="addgroupdialogVisibleSyj" width="40%" label-width="120px"
            :close-on-click-modal="false" v-dialogDrag>
            <span>
                <el-form :model="addForm" :rules="addFormRules">
                    <el-form-item prop="groupName" label="分组">
                        <el-input style="width:83%" v-model="addForm.groupName" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="leaderName" label="组长">
                        <el-input style="width:83%" v-model="addForm.leaderName" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="sname" label="姓名">
                        <el-input style="width:83%" v-model="addForm.sname" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="snick" label="客服昵称">
                        <el-input style="width:83%" v-model="addForm.snick" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="shopName" label="店铺">
                        <el-input style="width:83%" v-model="addForm.shopName" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="phoneNo" label="绑定手机号">
                        <el-input style="width:73%" v-model="addForm.phoneNo" :maxlength="20"></el-input>
                    </el-form-item>
                    <el-form-item prop="joinDate" label="入组日期">
                        <el-date-picker v-model="addForm.joinDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date"
                            style="width:63%" placeholder="选择日期">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item prop="leaveDate" label="离组日期">
                        <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="addForm.leaveDate"
                            style="width:63%" type="date" placeholder="选择日期">
                        </el-date-picker>
                    </el-form-item>
                </el-form>
            </span>

            <span slot="footer" class="dialog-footer">
                <el-button @click="addgroupdialogVisibleSyj = false">关闭</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="onAddGroup">确定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="修改客服人员分组管理信息" :visible.sync="updategroupdialogVisibleSyj" width="40%"
            :close-on-click-modal="false" v-dialogDrag>
            <span>
                <el-form :model="addForm" :rules="addFormRules">
                    <el-form-item prop="groupName" label="分组">
                        <el-input style="width:83%" v-model="addForm.groupName" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="leaderName" label="组长">
                        <el-input style="width:83%" v-model="addForm.leaderName" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="sname" label="姓名">
                        <el-input style="width:83%" v-model="addForm.sname" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="snick" label="客服昵称">
                        <el-input style="width:83%" v-model="addForm.snick" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="shopName" label="店铺">
                        <el-input style="width:83%" v-model="addForm.shopName" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="phoneNo" label="绑定手机号">
                        <el-input style="width:73%" v-model="addForm.phoneNo" :maxlength="20"></el-input>
                    </el-form-item>
                    <el-form-item prop="joinDate" label="入组日期">
                        <el-date-picker v-model="addForm.joinDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date"
                            style="width:63%" placeholder="选择日期">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item prop="leaveDate" label="离组日期">
                        <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="addForm.leaveDate"
                            style="width:63%" type="date" placeholder="选择日期">
                        </el-date-picker>
                    </el-form-item>
                </el-form>
            </span>

            <span slot="footer" class="dialog-footer">
                <el-button @click="updategroupdialogVisibleSyj = false">关闭</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="updategroup()">确定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="客服人员分组管理" :visible.sync="dialogVisibleSyj" width="30%" :close-on-click-modal="false" v-dialogDrag>
            <el-form ref="improtGroupForm" :model="improtGroupForm" label-width="55px" label-position="left">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false"  action
                            accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                        <!-- <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2"
                            :on-change="onUploadChange2" :on-remove="onUploadRemove2">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                                @click="onSubmitupload2">上传</my-confirm-button>
                        </el-upload> -->
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>

          <el-dialog title="离组日期选择" :visible.sync="dialogVisibleLeave" width="30%" :before-close="closeDialog"
            :close-on-click-modal="false" v-dialogDrag>
            <span>
                <template class="block">
                    <!-- <el-date-picker v-model="dialogLeaveDate" align="right" type="date" placeholder="选择日期" ></el-date-picker> -->
                    <el-date-picker v-model="dialogLeaveDate" style="width:63%" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                    </el-date-picker>
                </template>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleLeave = false">关闭</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="updategroupLeave()">提交</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import  {getOutSourceGroupList,
        addOutSourceGroupInfo,
        updateOutSourceGroupInfo,
        deleteOutSourceGroup,
        updateGroupBatchLeave,
        getOSGroupByList,
        importOSGroupAsync
} from "@/api/customerservice/waibao";



 
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

const tableCols = [
    { istrue: true, prop: 'groupName', label: '分组', sortable: 'custom' },
    { istrue: true, prop: 'leaderName', label: '组长', sortable: 'custom' },
    { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', sortable: 'custom' },
    { istrue: true, prop: 'snick', label: '客服昵称', sortable: 'custom' },
    { istrue: true, prop: 'phoneNo', label: '绑定手机号', sortable: 'custom' },
    { istrue: true, prop: 'joinDate', label: '入组日期', sortable: 'custom', formatter: (row) => formatTime(row.joinDate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'leaveDate', label: '离组日期', sortable: 'custom', formatter: (row) => formatTime(row.leaveDate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'createdTime', label: '导入时间', sortable: 'custom'},
    {
        istrue: true, type: "button", label: '操作', width: "120", align: "center",
        btnList: [
            { label: "编辑", handle: (that, row) => that.handleupdategroup(row) , permission: "api:Customerservice:OutSource:UpdateOutSourceGroupInfo1" },
            { label: "删除", handle: (that, row) => that.deletegroup(row), permission: "api:Customerservice:OutSource:DeleteOutSourceGroup1"  }
        ]
    }
];
export default {
    name: "pddgroup",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
    data() {
        return {
            that: this,
            filter: {
                isleavegroup:false,
            },
            addForm: {},
            addFormRules: {
                groupName: [{ required: true, message: '请输入', trigger: 'blur' }],
                leaderName: [{ required: true, message: '请输入', trigger: 'blur' }],
                sname: [{ required: true, message: '请输入', trigger: 'blur' }],
                snick: [{ required: true, message: '请输入', trigger: 'blur' }],
                shopName: [{ required: true, message: '请输入', trigger: 'blur' }],
                leaveDate: [{ required: true, message: '请输入', trigger: 'blur' }],
                joinDate: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
            shopList: [],
            filterGroupList: [],
            grouplist: [],
            tableCols: tableCols,
            total: 0,
            addgroupdialogVisibleSyj: false,
            // summaryarry: { count_sum: 10 },
            pager: { OrderBy: "createdtime", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            updategroupdialogVisibleSyj: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            improtGroupForm: {
                improtGroupShopCode: null,
                improtGroupShopName: null,
            },
            uploadLoading:false,
            dialogVisibleLeave: false,
            dialogLeaveDate: null,
        };
    },
    async mounted() {
        await this.getGroupNameList();//查询，分组list
        //await this.getAllShopList();//查询，店铺下拉
        await this.onSearch();
    },
    methods: {
        async onSearch() {
             await this.getGroupNameList();//查询，分组list
             this.$refs.pager.setPage(1);
            await this.getgroupList();//pageList
        },
        async getGroupNameList() {  //条件查询组名称下拉列表
            let res = await getOSGroupByList({ platform: 6 });
            this.filterGroupList=res.data;
        },
        // async getAllShopList() {    //查询，店铺下拉
        //     let shops = await getAllShopList();
        //     this.shopList = [];
        //     shops.data?.forEach(f => {
        //         if (f.shopCode && f.platform == 7)
        //             this.shopList.push(f);
        //     });
        // },
        async getgroupList() {  //列表pageList
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };
            params.platform = 6;    //平台
            this.listLoading = true;
            const res = await getOutSourceGroupList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.grouplist = res.data.list;
            //this.summaryarry=res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },

        changeingroup() {   //是否离组，按钮
            this.onSearch();
        },
        sortchange(column) {   //头部，点击表头排序
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async onAddGroupShow() {    //添加，按钮
            this.addForm = {};
            this.addgroupdialogVisibleSyj = true;
        },
        async onAddGroup() {    // 分组，添加
            var that = this;
            if (this.addForm.groupName == "" || this.addForm.groupName == null ||
                this.addForm.sname == "" || this.addForm.sname == null ||
                this.addForm.leaderName == "" || this.addForm.leaderName == null ||
                this.addForm.snick == "" || this.addForm.snick == null ||
                this.addForm.shopName == "" || this.addForm.shopName == null ||
                this.addForm.leaveDate == "" || this.addForm.leaveDate == null ||
                this.addForm.joinDate == "" || this.addForm.joinDate == null) {
                that.$message({ message: '请输入必填字段', type: "error" });
                return;
            }
            that.addForm.platform = 6;
            let add = await addOutSourceGroupInfo(that.addForm);
            if (add?.success) {
                that.$message({ message: '已添加', type: "success" });
                that.onSearch();
                that.addForm = {};
                that.addgroupdialogVisibleSyj = false;
            }
            else {
                //that.$message({ message: '发生异常，请刷新后重试', type: "error" });
            }
        },
        async handleupdategroup(row) {  //编辑，按钮
            this.addForm = JSON.parse(JSON.stringify(row));
            this.updategroupdialogVisibleSyj = true;
        },
        async updategroup(row) {    // 分组，编辑
            if (this.addForm.groupName == "" || this.addForm.groupName == null ||
                this.addForm.sname == "" || this.addForm.sname == null ||
                this.addForm.leaderName == "" || this.addForm.leaderName == null ||
                this.addForm.snick == "" || this.addForm.snick == null ||
                this.addForm.shopName == "" || this.addForm.shopName == null ||
                this.addForm.leaveDate == "" || this.addForm.leaveDate == null ||
                this.addForm.joinDate == "" || this.addForm.joinDate == null) {
                this.$message({ message: '请输入必填字段', type: "error" });
                return;
            }
            var that = this;
            that.addForm.platform = 6;
            let del = await updateOutSourceGroupInfo(that.addForm);
            if (del?.success) {
                that.$message({ message: '已修改', type: "success" });
                that.onSearch();
                this.updategroupdialogVisibleSyj = false;
            }
            else {
                that.$message({ message: '发生异常，请刷新后重试', type: "error" });
            }
        },
        async deletegroup(row) {    //分组，删除
            var that = this;
            this.$confirm("此操作将删除此客服人员分组管理数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let del = await deleteOutSourceGroup({ id: row.id });
                if (del?.success) {
                    that.$message({ message: '已删除', type: "success" });
                    that.onSearch();
                }
                else {
                    that.$message({ message: '发生异常，请刷新后重试', type: "error" });
                }
            });
        },
        async batchLeaveGroup() {    //批量离组，选择日期
            if (this.selids.length == 0) {
                this.$message({ message: "至少选择一行", type: "warning", });
                this.editparmVisible = false
                return
            }
            this.dialogVisibleLeave = true;
        },
         async updategroupLeave() { //批量离组提交
            if (this.dialogLeaveDate == null || this.dialogLeaveDate == '') {
                this.$message({ message: "请选择日期", type: "warning" });
                return false;
            }
            // if (Date.parse(this.dialogLeaveDate) < (Date.now() - 3600 * 1000 * 24) ) {
            //     this.$message({ message: "离组日期不得早于当前日期", type: "warning" });
            //     return false;
            // }
            const params = {
                batchLeaveDate: this.dialogLeaveDate,
                idList: this.selids,
                platform:6
            };
            var res = await updateGroupBatchLeave(params);
            if (res == true) {
                this.$message({ message: "修改成功", type: 'success', });
                this.getgroupList();
                this.dialogVisibleLeave = false;
                this.idList = null;
                this.dialogLeaveDate = null;
            }
        },
        onImportSyjModel() {
            window.open("/static/excel/customerservice/抖音分组导入模板.xlsx", "_blank");
        },
        async onImportSyj() {   //导入，按钮
            this.dialogVisibleSyj = true;
            // let shops = await getAllShopList();
            // this.shopList = [];
            // shops.data?.forEach(f => {
            //     if (f.shopCode && f.platform == 7)
            //         this.shopList.push(f);
            // });
        },

        async uploadFile () {
            if (this.fileList.length ==0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false
            };
            const form = new FormData();
            form.append("upfile", this.fileList[0].raw);
            form.append("platform", 6);
            var res = await importOSGroupAsync(form);
            if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
            // else this.$message({ message: res.msg, type: "warning" });
            this.$refs.upload.clearFiles()
            this.uploadLoading = false;
            this.dialogVisibleSyj = false;
            this.fileList = [];
        },
        async uploadChange (file, fileList) {
            if (fileList.length == 2) {
                fileList.splice(1, 1);
                this.$message({ message: "只允许单文件导入", type: "warning" });
                return false;
            }
            this.fileList.push(file);
        },
        uploadRemove (file, fileList) {
            this.fileList.splice(0, 1);
        },
        submitUpload () {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return;
            }
            this.uploadLoading = true
            this.$refs.upload.submit();
        },
        // async onUploadChange2(file, fileList) {
        //     this.fileList = fileList;
        // },
        // async onUploadRemove2(file, fileList) {
        //     this.fileList = [];
        // },
        // async uploadFile2(item) {
        //     if (!item || !item.file || !item.file.size) {
        //         this.$message({ message: "请先上传文件", type: "warning" });
        //         return false;
        //     }
        //     const form = new FormData();
        //     form.append("upfile", item.file);
        //     form.append("platform", 6);
        //     const res = await importOSGroupAsync(form);
        //     if (res?.success) {
        //         this.$message({ message: '上传成功,正在导入中...', type: "success" });
        //         this.dialogVisibleSyj = false;
        //     }
        //     // else {
        //     //     this.$message({ message: '发生异常，请刷新后重试', type: "error" });
        //     // }
        // },
        // async uploadSuccess2(response, file, fileList) {
        //     fileList.splice(fileList.indexOf(file), 1);
        // },
        // async onSubmitupload2() {
        //     if (this.fileList.length == 0) {
        //         this.$message({ message: "请先上传文件", type: "warning" });
        //         return false;
        //     }
        //     this.$refs.upload2.submit();
        // },
        closeDialog() {
            this.dialogVisibleLeave = false;
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 60px;
}

</style>
