<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
<div style="width: 150px;margin-right: 5px;">
        <inputYunhan ref="productCode2" :inputt.sync="ListInfo.OrderNo" v-model="ListInfo.OrderNo"
            class="publicCss" placeholder="线上单号/多条请按回车" :clearable="true" :clearabletext="true"
            :maxRows="200" :maxlength="3000" @callback="orderNoInnerBack" title="线上单号">
        </inputYunhan>  
      </div>
      <div style="width: 150px;margin-right: 5px;">
        <inputYunhan ref="productCode2" :inputt.sync="ListInfo.ProCode" v-model="ListInfo.ProCode"
            class="publicCss" placeholder="店铺款式编码/多条请按回车" :clearable="true" :clearabletext="true"
            :maxRows="200" :maxlength="3000" @callback="orderNoInnerBack2" title="店铺款式编码">
        </inputYunhan>  
      </div>
<div style="width: 150px;margin-left:50px;">
  <YhShopSelector :names="['所有店铺']" :values="[-1]"  platform="20" :checkboxOrRadio="'checkbox'" :addAllWarehouseConcept="true" @onChange="(v)=>{ListInfo.shopCode=v[0].join(','); }">
        </YhShopSelector>
      </div>
      <el-button type="primary" @click="getList('search')">搜索</el-button>
      <el-button type="primary" @click="onExport">导出</el-button>
    </div>
    </template>
    <vxetablebase :id="'breachPaymentVideo202412021559'" :tablekey="'breachPaymentVideo202412021559'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getDrNewBillingCharge_WeChat,exportDrNewBillingCharge_WeChat } from '@/api/bookkeeper/reportdayV2'
import inputYunhan from "@/components/Comm/inputYunhan";
import YhShopSelector from "@/components/YhCom/YhShopSelector";


import dayjs from 'dayjs'
const tableCols = [
  // { sortable: 'custom', width: 'auto', align: 'center', prop: 'yearMonthDay', label: '日期', },
  { istrue: true, prop: 'shopName', label: '店铺名', tipmesg: '', width: '100', sortable: 'custom', },
  { istrue: true, prop: 'accountingTime', label: '记账时间', tipmesg: '', width: '100', sortable: 'custom', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'transactionType', label: '动帐类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'incomeExpenseType', label: '收支类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'details', label: '详情', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '原始线上订单号', },
  {  sortable: 'custom', width: 'auto', align: 'center', prop: 'amount', label: '销售金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'amountIncome', label: '达人佣金', },
  {  sortable: 'custom', width: 'auto', align: 'center', prop: 'accountBalance', label: '佣金率',formatter: (row) =>row.accountBalance+"%" },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'relatedPolicyNumber', label: '店铺款式编码', },
]
export default {
  name: "breachPaymentVideo",
  components: {
    MyContainer, vxetablebase,inputYunhan,YhShopSelector
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: "AccountingTime",
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      onExporting:false,
    }
  },
  async mounted() {
    // await this.getList()
    if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    //导出数据,使用时将下面的方法替换成自己的接口
    // async exportProps() {
    //     const { data } = await exportStatData(this.ListInfo)
    //     const aLink = document.createElement("a");
    //     let blob = new Blob([data], { type: "application/vnd.ms-excel" })
    //     aLink.href = URL.createObjectURL(blob)
    //     aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
    //     aLink.click()
    // },
    orderNoInnerBack(val) {
            this.ListInfo.OrderNo = val;
        },
        orderNoInnerBack2(val) {
            this.ListInfo.ProCode = val;
        },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await getDrNewBillingCharge_WeChat(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    async onExport() {
     if (this.onExporting) return;
     try{
     
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
        var res= await exportDrNewBillingCharge_WeChat(this.ListInfo);
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','达人佣金明细_' + new Date().toLocaleString() + '.xlsx' )
        this.uploadLoading = false;

        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
