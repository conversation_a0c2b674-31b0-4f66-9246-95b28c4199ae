<template>
    <my-container v-loading="pageLoading">
       <template #header> 
       </template>  
       <ces-table ref="table"   :that='that'  :isIndex='true' 
                   :hasexpand='false'
                   :isSelectColumn='true'  
                   :tableData='tasklist' 
                   :tableCols='tableCols' 
                   tablekey="shootingPackageInfo" 
                   :loading="listLoading"  
                   :isBorder="false"
                   > 
                   <el-table-column type="expand">
                       <template slot-scope="props">
                           <div>
                               <el-table :data="props.row.detaildata" style="width: 100%">
                                   <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                                   </el-table-column>
                               </el-table>
                           </div>
                       </template> 
                   </el-table-column>
                   <template slot='extentbtn'>
                    <el-button-group> 
                        <el-button type="primary">历史版本</el-button> 
                        <el-button type="primary">激活修订</el-button> 
                        <el-button type="primary">提交审核</el-button>  
                        <el-button type="primary">审核通过</el-button>  
                    </el-button-group>
                   </template>
       </ces-table> 
       
       <template #footer>
           <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList" />
       </template>
     
   </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";  
import cesTable from "@/components/Table/tableforvedio.vue"; 
import accountsWorkPostionManage from '@/views/media/shooting/adjustAccounts/accountsWorkPostionManage';
import { getErpUserInfoViewforshoot,getWorkPostListAsync} from '@/api/media/mediashare'; 
import { addOrUpdatePersonnelPositionAsync,getPersonnelPositionAsync ,delPersonnelPositionAsync } from '@/api/media/shootingset'
import MyConfirmButton from "@/components/my-confirm-button";
const tableCols = [
   { istrue: true, prop: 'userName', label: '姓名', width: '80'   }, 
   {
       istrue: true, type: "button", label: '操作', width: "100", align:'center',
       btnList: [
           { label: "编辑",  handle: (that, row) => that.onEditAdd(row) },
           { label: "删除",  handle: (that, row) => that.onDel(row) }
       ]
   }
];   
export default { 
 components: { MyContainer , cesTable,MyConfirmButton,accountsWorkPostionManage},
   data() {
       return {  
           that: this,
           pageLoading: false,
           positionOrder:false,
           summaryarry:[], 
           userList:[],
           tasklist:[],
           workPositionlist:[],
           commissionPositionlist:[],
           retdata:[],
           sels: [], // 列表选中列
           tableCols: tableCols,
           listLoading: false,
           //人员编辑模块
           editLoading:false,
           editformdialog:false,
           editformTitle:null,
           editform:{
            userId:null,
            userName:null,
            companyName:null,
            workPosition:null,
            commissionPosition:null,
            commissionRate:null,
            isCyCommission:null,
            isCyFp:null
           },
           total:0,
           pager: { OrderBy: "createdTime", IsAsc: false },
           filter: { 
           }, 
           editformRules: {
                userId: [{ required: true, message: '请选择', trigger: 'blur' }],
                isCyCommission: [{ required: true, message: '请选择', trigger: 'blur' }],
                isCyFp: [{ required: true, message: '请选择', trigger: 'blur' }],
                commissionRate: [{ required: true, message: '请填写', trigger: 'blur' }], 
                companyName: [{ required: true, message: '请选择', trigger: 'blur' }], 
                commissionPosition: [{ required: true, message: '请填写', trigger: 'blur' }], 
                workPosition: [{ required: true, message: '请填写', trigger: 'blur' }]   
            },
       };
   },
   
   async mounted() {
        await this.getUserList();
        await this.getWorkPostList();
        await this.onSearch();
   }, 
   methods: {  
        async  onDel(row){ 

            this.$confirm("将进行删除操作，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await delPersonnelPositionAsync({positionId: row.positionId}); 
                if (!res?.success) {  return; }
                this.$message({message: this.$t('操作成功'),type: 'success'}); 
                await this.onSearch()
            });
           
        },
        async CyCommission(row){ 
            this.editform.isCyCommission = row.isCyCommissionbool?1:0;  
            this.editform.isCyFp = row.isCyFpbool?1:0;   
            this.editform.userId = row.userId.toString();
            this.editform.userName = row.userName; 
            this.editform.commissionRate = row.commissionRate;
            this.editform.companyName = row.companyName;
            this.editform.workPosition = row.workPosition;
            this.editform.commissionPosition = row.commissionPosition;  
            this.editform.positionId= row.positionId; 
            const para = _.cloneDeep(this.editform);     
            var res = await addOrUpdatePersonnelPositionAsync(para); 
            if (!res?.success) {  return; }
            this.$message({message: this.$t('保存成功'),type: 'success'}); 
        },
        async workPositionManage(){
            this.editLoading =true;  
            this.positionOrder =true;
            this.editLoading =false;
        },
        selUserInfoChange(val, index){
            let resultArr = this.userList.find((item) => {
                return item.id == val;
            });
            this.editform.userName = resultArr.label;
        },
        //获取岗位下拉数据
        async getWorkPostList() {
            var res = await getWorkPostListAsync();
            if(res){
                this.workPositionlist =res.workPositionlist;
                this.commissionPositionlist =res.commissionPositionlist;
            }
        }, 
        async getUserList() {
            this.userList = await getErpUserInfoViewforshoot();
            return this.userList;
        }, 
        //打开新增窗口
        async onOpenAdd(){
            this.editformdialog = true;
            this.editLoading =true;
            await getWorkPostListAsync(); 
            this.editformTitle ="新增人员"; 
            this.editform.userId = null;
            this.editform.userName = null;
            this.editform.isCyCommission = 0;
            this.editform.commissionRate =100;
            this.editform.companyName = null;
            this.editform.workPosition = null;
            this.editform.commissionPosition = null;
            this.editform.userName =null;
            this.editform.isCyFp = 1;
            this.editLoading =false;
        },  
         //打开新增窗口
         async   onEditAdd(row){ 
            this.editformdialog = true;
            this.editLoading =true;
            await getWorkPostListAsync(); 
            this.editformTitle ="编辑人员";
            this.editform.isCyFp =row.isCyFp; 
            this.editform.isCyCommission = row.isCyCommission;
            this.editform.userId = row.userId.toString();
            this.editform.userName = row.userName; 
            this.editform.commissionRate = row.commissionRate;
            this.editform.companyName = row.companyName;
            this.editform.workPosition = row.workPosition;
            this.editform.commissionPosition = row.commissionPosition;  
            this.editform.positionId= row.positionId; 
            this.editLoading =false;
        },  
        //提交保存时验证
        onSubmitValidate: function () {
            let isValid = true;
            this.$refs.editform.validate(valid => {
                isValid = valid
            })
            return isValid ;
        },
        async onSubmit() { 
           if(!this.onSubmitValidate()){
                return;
            }   
            const para = _.cloneDeep(this.editform);    
            this.editLoading =true;
            var res = await addOrUpdatePersonnelPositionAsync(para);
            this.editLoading =false;
            if (!res?.success) {  return; }
            this.$message({message: this.$t('保存成功'),type: 'success'}); 
            this.onSearch();
            this.editformdialog =false;
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            this.getTaskList();
        },
        async getTaskList() 
        { 
             
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager, 
            };
            this.listLoading = true;
            const res = await getPersonnelPositionAsync(params); 
            this.listLoading = false;  
            this.total = res.data.total
            this.tasklist = res.data.list;
            //this.summaryarry = { videoTaskId_sum: " 0" };
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
   },
};
</script>


