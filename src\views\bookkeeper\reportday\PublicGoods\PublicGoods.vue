<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-date-picker
        v-model="Filter.timerange"
        style="width: 260px"
        type="datetimerange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="年月日开始日期"
        end-placeholder="年月日结束日期"
      />

      <el-date-picker
        v-model="Filter.Computetimerange"
        style="width: 230px"
        type="datetimerange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="计算开始日期"
        end-placeholder="计算结束日期"
      />

      <el-date-picker
        v-if="!isPbl"
        v-model="Filter.Removetimerange"
        style="width: 230px"
        type="datetimerange"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="移除开始日期"
        end-placeholder="移除结束日期"
      />
      <el-input
        v-model.trim="Filter.styleCode"
        :maxlength="150"
        clearable
        placeholder="系列编码"
        style="width:160px;"
      />
      <el-select v-model="Filter.groupId" filterable collapse-tags clearable placeholder="运营组" style="width: 90px">
        <el-option key="无运营组" label="无运营组" :value="0" />
        <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>

      <el-button type="primary" @click="onSearch">查询</el-button>
    </template>
    <!--列表-->
    <vxetablebase
      :id="'productgoodscurrent20230922'"
      ref="table"
      :border="true"
      :align="'center'"
      :tablekey="'productgoodscurrent20230922'"
      :that="that"
      :is-index="true"
      :hasexpand="false"
      :is-select-column="true"
      :showsummary="true"
      :tablefixed="true"
      :summaryarry="summaryarry"
      :table-data="pddcontributeinfolist"
      :table-cols="tableCols"
      :loading="listLoading"
      style="width:100%;height:99%;margin: 0"

      :xgt="9999"
      @sortchange="sortchange"
    >
      <template slot="right">
        <vxe-column title="详情">
          <template #default="{ row, $index }">
            <div style="display: flex; justify-content: center; align-items: center; height: 100%;">
              <el-button type="text" @click="operationClick(row)">详情</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <el-dialog v-dialogDrag :visible.sync="buscharDialog.visible" width="80%">
      <el-form class="ad-form-query" :model="ChartFilter" label-width="100px" @submit.native.prevent>
        <el-form-item label="日期:">
          <el-date-picker
            v-model="ChartFilter.timerangechart"
            style="width: 260px"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :clearable="true"
            @change="getChartData"
          />
        </el-form-item>
        <el-form-item />
      </el-form>
      <div>
        <buschar v-if="buscharDialog.visible" ref="buscharPublicGoods" :analysis-data="buscharDialog.data" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog v-dialogDrag title="详情" :visible.sync="detailPopup" width="65%" :append-to-body="true">
      <vxetablebase :id="'PublicGoods202408041453'"
        ref="detailstable"
        v-loading="detailsloading"
        :that="that"
        :is-index="true"
        :hasexpand="true"
        :tablefixed="true"
        :table-data="detailstableData"
        :table-cols="detailstableCols"
        :is-selection="false"
        :is-select-column="false"
        style="width: 100%;  margin: 0"
        :height="'500'"
        @sortchange="detailssortchange"
      >
        <template slot="right">
          <vxe-column title="销售金额趋势图">
            <template #default="{ row, $index }">
              <div style="display: flex; justify-content: center; align-items: center; height: 100%;">
                <el-button type="text" @click="salesAmount(row)" v-if="row.hasAuth">趋势图</el-button>
              </div>
            </template>
          </vxe-column>
        </template>
      </vxetablebase>
      <template #footer>
        <my-pagination ref="detailspager" :total="detailstotal" @page-change="Pagechange" @size-change="Sizechange" />
      </template>
    </el-dialog>

    <el-dialog v-dialogDrag title="趋势图" :visible.sync="detailsbuscharDialog.visible" width="70%" append-to-body>
      <div>
        <buschar v-if="detailsbuscharDialog.visible" ref="detailsbuschar" :analysis-data="detailsbuscharDialog.data" />
      </div>
    </el-dialog>

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>
  </my-container>
</template>
<script>
import vxetablebase from '@/components/VxeTable/yh_vxetable.vue'
import { pageGetPblGoods, getTrendChart, getStyleInfos } from '@/api/bookkeeper/pblGood'
import MyContainer from '@/components/my-container'
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import buschar from '@/components/Bus/buschar'
import { formatLinkProCode } from "@/utils/tools";
const detailstableCols = [
  { prop: 'productId', fix: true, label: 'ID', width: 'auto',  align: 'center', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.productId) },
  { width: 'auto', align: 'center', prop: 'shopName', label: '店铺' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'groupId', label: '小组', formatter: (row) => row.groupName },
  { width: 'auto', align: 'center', prop: 'operateSpecialUserName', label: '运营专员' },
  { width: 'auto', align: 'center', prop: 'userName', label: '运营助理' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'onTime', label: '上架时间' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderCount', label: '订单量' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'profit3', label: '毛三利润' }
]
export default {
  name: 'PublicGoodsCurrent',
  components: {
    MyContainer,
    vxetablebase,
    buschar
  },
  props: {
    isPbl: { type: Boolean, default: true }
  },
  data() {
    return {
      detailsbuscharDialog: { visible: false, title: '', data: {}},
      buscharDialog: { visible: false, title: '', data: {}, loading: false },
      that: this,
      Filter: {
        startTime: null,
        endTime: null,
        timerange: [],
        Computetimerange: [],
        cptStartTime: null,
        cptEndTime: null,
        styleCode: null,
        groupId: null
      },
      shopList: [],
      userList: [],
      grouplist: [],
      pddcontributeinfolist: [],
      tableCols: this.gettableCols(),
      total: 0,
      summaryarry: {},
      pager: { OrderBy: 'Profit3', IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids: [],
      fileList: [],
      dialogVisible: false,
      uploadLoading: false,
      showtable: true,
      styleCode: null,
      groupId: null,
      ChartFilter: {
        startTime: null,
        endTime: null,
        styleCode: null,
        groupId: null,
        commonFormType: null,
        timerangechart: []
      },
      detailPopup: false,
      detailstableData: [],
      detailsloading: false,
      detailstableCols,
      destyleCode: null,
      detailstotal: 0,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false
      }
    }
  },
  async created() {
    await this.getGroupList()
  },
  methods: {
    // 每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1
      this.ListInfo.pageSize = val
      this.operationClick()
    },
    // 当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val
      this.operationClick()
    },
    detailssortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf('descending') == -1
        this.operationClick()
      }
    },
    async salesAmount(row) {
      this.detailsloading = true
      const { data, success } = await getTrendChart({ productId: row.productId, startTime: this.Filter.startTime, endTime: this.Filter.endTime })
      if (success) {
        data.series.map((item) => {
          item.itemStyle = {
            'normal': {
              'label': {
                'show': true,
                'position': 'top',
                'textStyle': {
                  'fontSize': 14
                }
              }
            }
          }

          item.emphasis = {
            'focus': 'series'
          }
          item.smooth = false
        })
        this.detailsbuscharDialog.visible = true
        this.detailsbuscharDialog.data = data
        this.detailsbuscharDialog.title = data.legend[0]
        this.detailsloading = false
        this.$nextTick(() => {
          this.$refs.detailsbuschar.initcharts()
        })
      } else {
        this.$message.error('获取趋势图失败')
      }
    },
    async operationClick(row) {
      if (row) {
        this.destyleCode = row.styleCode
      }
      this.listLoading = true
      this.detailsloading = true
      const { data, success } = await getStyleInfos({ styleCode: this.destyleCode, startTime: this.Filter.startTime, endTime: this.Filter.endTime, ...this.ListInfo })
      if (success) {
        this.detailstableData = data.list
        this.detailstotal = data.total
        this.detailPopup = true
      } else {
        this.$message.error('获取详情失败')
      }
      this.listLoading = false
      this.detailsloading = false
    },
    async getChartData() {
      this.ChartFilter.startTime = null
      this.ChartFilter.endTime = null
      if (this.ChartFilter.timerangechart) {
        this.ChartFilter.startTime = this.ChartFilter.timerangechart[0]
        this.ChartFilter.endTime = this.ChartFilter.timerangechart[1]
      }
      this.ChartFilter.styleCode = this.styleCode
      this.ChartFilter.groupId = this.groupId
      this.ChartFilter.commonFormType = 1
      const para = { ...this.ChartFilter }
      const params = {
        ...para,
        isPbl: this.isPbl
      }
      const that = this
      const res = await getTrendChart(params).then(res => {
        that.buscharDialog.data = res.data
        that.buscharDialog.title = res.data.legend[0]
        that.buscharDialog.loading = false
      })
      await this.$refs.buscharPublicGoods.initcharts()
      this.ChartFilter.startTime = null
      this.ChartFilter.endTime = null
      this.ChartFilter.timerangechart = []
    },
    ClickShowChart(row) {
      this.buscharDialog.visible = true
      this.buscharDialog.loading = true
      this.showPublicGoodschart(row.styleCode, row.groupId, row.yearMonthDay)
    },
    async showPublicGoodschart(styleCode, groupId, yearMonthDay) {
      this.ChartFilter.styleCode = styleCode
      this.ChartFilter.groupId = groupId
      this.ChartFilter.commonFormType = 1
      this.styleCode = styleCode
      this.groupId = groupId
      const para = { ...this.ChartFilter }
      const params = {
        ...para,
        isPbl: this.isPbl
      }
      const that = this
      const res = await getTrendChart(params).then(res => {
        that.buscharDialog.data = res.data
        that.buscharDialog.title = res.data.legend[0]
        that.buscharDialog.loading = false
      })
      await this.$refs.buscharPublicGoods.initcharts()
    },
    async getGroupList() {
      var res2 = await getDirectorGroupList()
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value } })
    },
    gettableCols() {
      return [
        { istrue: true, prop: 'yearMonthDay', label: '年月日', sortable: 'custom', width: '200' },
        { istrue: true, prop: 'styleCode', label: '系列编码', width: '249', sortable: 'custom' },
        { istrue: true, prop: 'groupId', label: '小组', width: '249', sortable: 'custom', formatter: (row) => row.groupName },
        { istrue: true, prop: 'brandId', label: '采购', width: '249', sortable: 'custom', formatter: (row) => row.brandName },
        { istrue: true, prop: 'cptTime', label: '计算时间', width: '249', sortable: 'custom' }
        // { istrue: true, display: true, label: '毛三趋势', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '毛三趋势', type: 'click', handle: (that, row) => that.ClickShowChart(row) },
        // { istrue: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width: '249', },
        // { istrue: true, prop: 'profit3', label: '毛三利润', sortable: 'custom', width: '249', },
      ]
    },
    sortchange(column) {
      if (!column.order) this.pager = {}
      else {
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf('descending') == -1
        }
      }
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getList()
    },
    async getList() {
      this.Filter.startTime = null
      this.Filter.endTime = null
      this.Filter.cptStartTime = null
      this.Filter.cptEndTime = null

      if (this.Filter.timerange) {
        this.Filter.startTime = this.Filter.timerange[0]
        this.Filter.endTime = this.Filter.timerange[1]
      }
      if (this.Filter.Computetimerange) {
        this.Filter.cptStartTime = this.Filter.Computetimerange[0]
        this.Filter.cptEndTime = this.Filter.Computetimerange[1]
      }
      const para = { ...this.Filter }
      const pager = this.$refs.pager.getPager()
      const params = {
        ...pager,
        ...this.pager,
        ...para,
        isPbl: this.isPbl
      }
      this.listLoading = true
      const res = await pageGetPblGoods(params)
      if (!res.success) {
        this.listLoading = false
        return
      }
      this.listLoading = false
      this.total = res.data.total
      this.pddcontributeinfolist = res.data.list
      this.summaryarry = res.data.summary
    },
    selectchange: function(rows, row) {
      this.selids = []
      rows.forEach((f) => {
        this.selids.push(f.id)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
