<template>
  <MyContainer style="position: relative" v-loading="pageLoading">
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 300px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime($event, 1)" :clearable="false">
        </el-date-picker>
        <el-date-picker v-model="lastTimeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="最后操作开始时间" end-placeholder="最后操作结束时间" :picker-options="pickerOptions1"
          style="width: 300px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime($event, 2)">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.indexNo" placeholder="erp编码" maxlength="50" clearable class="publicCss" />
        <inputYunhan ref="productCode" :inputt.sync="ListInfo.buyNo" v-model="ListInfo.buyNo" width="200"
          placeholder="采购单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
          @callback="buyNoCallback" title="采购单号" style="margin:  0 5px 5px 0;">
        </inputYunhan>
        <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode" width="200"
          placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
          @callback="goodsCodeCallback" title="商品编码" style="margin:  0 5px 0 0;">
        </inputYunhan>
        <inputYunhan ref="productCode" :inputt.sync="ListInfo.orderNumber" v-model="ListInfo.orderNumber" width="200"
          placeholder="单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
          @callback="orderNumberCallback" title="单号" style="margin:  0 5px 5px 0;">
        </inputYunhan>
        <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.remark" placeholder="备注内容" maxlength="50" clearable class="publicCss" />
        <el-select v-if="buyerInvisible" v-model="ListInfo.brandId" placeholder="采购员" class="publicCss" clearable
          filterable>
          <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <!-- <el-input v-model.trim="ListInfo.brandName" placeholder="请输入采购员" maxlength="50" clearable class="publicCss" /> -->
        <el-input v-model.trim="ListInfo.trackUserName" placeholder="请输入跟单员" maxlength="50" clearable
          class="publicCss" />
        <el-select v-model="ListInfo.wmsId" placeholder="仓库" class="publicCss" clearable filterable>
          <el-option v-for="item in warehouselist" :key="item.name" :label="item.name" :value="item.wms_co_id" />
        </el-select>
        <el-select v-model="ListInfo.status" placeholder="采购单状态" class="publicCss" clearable filterable>
          <el-option key="已提交审批中" label="已提交审批中" value="已提交审批中" />
          <el-option key="已确认未发货" label="已确认未发货" value="已确认未发货" />
          <el-option key="已发货" label="已发货" value="已发货" />
          <el-option key="已完成" label="已完成" value="已完成" v-if="activeName == 'first2'" />
        </el-select>
        <el-select v-model="ListInfo.startDept" placeholder="发起部门" class="publicCss" clearable filterable>
          <el-option :key="'采购正常进货'" label="采购正常进货" :value="'采购正常进货'" />
          <el-option :key="'运营给量进货'" label="运营给量进货" :value="'运营给量进货'" />
        </el-select>
        <el-select v-model="ListInfo.abnormalStatus" placeholder="异常状态" class="publicCss" clearable filterable>
          <el-option label="超15天未完成" :value="2" />
          <el-option label="隔自然月未完成" :value="1" />
        </el-select>
        <el-select v-model="ListInfo.purchaseInType" placeholder="入库状态" class="publicCss" clearable filterable>
          <el-option label="未入库" value="未入库" />
          <el-option label="部分入库" value="部分入库" />
          <el-option label="全部入库" value="全部入库" />
        </el-select>
        <el-select v-model="ListInfo.isSigned" placeholder="是否签收" class="publicCss" clearable filterable>
          <el-option label="是" :value="true" />
          <el-option label="否" :value="false" />
        </el-select>
        <el-select v-model="ListInfo.is1688" placeholder="是否1688" class="publicCss" clearable filterable>
          <el-option label="是" :value="true" />
          <el-option label="否" :value="false" />
        </el-select>
        <el-input v-model.trim="ListInfo.orderNo" placeholder="订单编号" maxlength="50" clearable class="publicCss" />
        <el-date-picker v-model="expectedTimeRangeTwo" type="daterange" unlink-panels range-separator="至"
          start-placeholder="预计发货开始时间" end-placeholder="预计发货结束时间" :picker-options="pickerOptions1"
          style="width: 300px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime($event, 3)">
        </el-date-picker>
        <el-date-picker v-model="expectedTimeRange" type="daterange" unlink-panels range-separator="至"
          start-placeholder="预计到货开始时间" end-placeholder="预计到货结束时间" :picker-options="pickerOptions1"
          style="width: 300px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime($event, 4)">
        </el-date-picker>

        <!-- <dateRange :startDate.sync="ListInfo.expectedDeliveryStartTime" :endDate.sync="ListInfo.expectedDeliveryEndTime"
          class="publicCss" /> -->
        <el-select v-model="ListInfo.consignmentTypes" placeholder="发货方式" clearable multiple collapse-tags filterable
          class="publicCss">
          <el-option v-for="item in consignmentTypesListDb" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </template>
    <el-tabs v-model="activeName" style="height: 94%;" @tab-click="handleClick">
      <el-tab-pane label="进行中" name="first1" style="height: 98%">
        <underWay ref="underWay" @childevent="getList" @getIds="getIds" @getSelectProp="getSelectProp"
          @oddNumber="oddNumber" @onrevealing="onrevealing" />
      </el-tab-pane>
      <el-tab-pane label="已完成" name="first2" :lazy="true" style="height: 98%">
        <completed ref="completed" @childevent="getList" @onrevealing="onrevealing" @oddNumber="oddNumber" />
      </el-tab-pane>
    </el-tabs>
    <div class="headerBtn">
      <el-checkbox style="padding-right: 15px;" v-model="ListInfo.logisticsBogDown" v-if="activeName=='first1'">物流停滞需跟进</el-checkbox>
      <el-checkbox-group v-model="checkList" @change="changeIsOnft" v-if="activeName == 'first1'">
        <el-checkbox :label="1">未发货需跟进</el-checkbox>
        <el-checkbox :label="2">已发货需跟进</el-checkbox>
      </el-checkbox-group>
      <el-button type="primary" @click="getList">查询 </el-button>
      <el-button type="primary" @click="returnStatus" v-if="activeName == 'first1'">退回状态</el-button>
      <el-button type="primary" @click="StartChat">发起聊天</el-button>
      <el-button @click="reset">重置 </el-button>
      <el-button type="primary" v-if="checkPermission('SmallNotificationSettings') && activeName == 'first1'"
        @click="notificationSettings">小昀通知设置
      </el-button>
      <el-button type="primary" v-show="activeName == 'first2'" @click="derive">导出 </el-button>
      <el-button type="primary" @click="onCopyNumber">复制采购单号 </el-button>
      <el-button type="primary" @click="synchronousJst"
        v-if="activeName == 'first1' && checkPermission('synchronousJst')">同步到聚水潭 </el-button>
    </div>

    <el-dialog title="小昀助理通知设置" :visible.sync="changeTimeSettings" width='25%' v-dialogDrag
      :close-on-click-modal="false">
      <div>
        <div>
          <span>每日通知时间</span>
          <el-button type="text" style="color: 409EFF; margin-left: 10px;" @click="addProps">新增一行</el-button>
        </div>
        <div style="height: 220px; overflow: auto;">
          <div v-for="(value, index) in notification.noticeTimes" :key="index"
            style="margin-left: 140px; display: flex; align-items: center;margin-bottom: 5px;">
            <el-time-picker v-model="notification.noticeTimes[index]" placeholder="请选择时间" value-format="HH:mm"
              format="HH:mm"></el-time-picker>
            <el-button type="text" style="color: red; margin-left: 10px;" @click="removeProps(index)">删除</el-button>
          </div>
        </div>
        <el-form :model="notification" status-icon :rules="rules" ref="ruleForm" label-width="110px"
          class="demo-ruleForm">
          <el-form-item label="物流停滞" prop="logisticsStasis">
            <el-input-number v-model="notification.logisticsStasis" placeholder="物流停滞(小时)" clearable style="width: 40%;"
              :controls="false" :precision="0" :min="0" :max="99999" />
            <span style="padding-left: 5px;">小时</span>
          </el-form-item>
          <el-form-item label="已确认未发货" prop="sureNoSend">
            <el-input-number v-model="notification.sureNoSend" placeholder="已确认未发货(小时)" clearable style="width: 50%;"
              :controls="false" :precision="0" />
          </el-form-item>
          <el-form-item label="已发货" prop="sureSend">
            <el-input-number v-model="notification.sureSend" placeholder="已发货(小时)" clearable style="width: 50%;"
              :controls="false" :precision="0" />
          </el-form-item>
          <el-form-item label="发货方式" prop="consignmentTypes">
            <el-input type="textarea" placeholder="发货方式" v-model="notification.consignmentTypes" maxlength="1000"
              :autosize="{ minRows: 3, maxRows: 10 }" resize="none" style="width: 50%;">
            </el-input>
          </el-form-item>
        </el-form>
        <div style="display: flex; flex-direction: column;margin-top: 20px;">
          <el-checkbox v-model="notification.isOver15DaysUnCpt">超15天未完成</el-checkbox>
          <el-checkbox v-model="notification.isOverMonthUnCpt">隔自然月未完成</el-checkbox>
        </div>
      </div>
      <div style="display: flex;justify-content: end;margin-top: 10px;">
        <el-button @click.native="changeTimeSettings = false">取消</el-button>
        <el-button @click="onSubmitDot" type="primary" v-throttle="2000">保存</el-button>
      </div>
    </el-dialog>

  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import underWay from "./underWay.vue";
import completed from "./completed.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions, warehouselist } from '@/utils/tools'
import { getAllProBrand, getAllWarehouse } from '@/api/inventory/warehouse'
import { getUserInfo } from '@/api/operatemanage/productalllink/alllink'
import { exportData, getSetting, setting, updateTrackStatus, purchaseOrderTrackSyncToJst } from '@/api/inventory/purchaseOrderTrack'
import { GetAliSupperChatIdByShow } from '@/api/inventory/purorder'
import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";
import dateRange from "@/components/date-range/index.vue";
import { addDictionary, getDictionaryListPage, editDictionary, getDictionary } from '@/api/admin/dictionary'
export default {
  name: "index",
  components: {
    MyContainer, vxetablebase, underWay, completed, inputYunhan, dateRange
  },
  data() {
    return {
      pickerOptions1: {
        disabledDate(time) {
          return time.getTime() > Date.now() || time.getTime() < Date.now() - 3600 * 1000 * 24 * 30;
        },
        shortcuts: [{
          text: '昨天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近半月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },]
      },
      lastTimeRanges: [],
      checkList: [],
      copyData: [],
      pageLoading: false,
      checkVerify: true,
      notification: {
        noticeTimes: [''],
        isOver15DaysUnCpt: false,
        isOverMonthUnCpt: false,
        consignmentTypes: null,
        consignmentTypesId: null,
        logisticsStasis: undefined
      },
      warehouselist: warehouselist,
      changeTimeSettings: false,
      isView: true,
      buyerInvisible: false,
      activeName: 'first1',
      that: this,
      brandlist: [],
      expectedTimeRange: [],
      expectedTimeRangeTwo: [],
      ListInfo: {
        lastTrackStartTime: null,//最后操作时间
        lastTrackEndTime: null,//最后操作时间
        isOnft: null,//只展示需跟进
        startTime: null,//开始时间
        endTime: null,//结束时间
        indexNo: null,//erp编码
        buyNo: null,//采购单号
        goodsCode: null,//商品编码
        goodsName: null,//商品名称
        brandId: null,//采购员
        wmsId: null,//仓库
        status: null,//采购单状态
        startDept: null,//发起部门
        abnormalStatus: null,//异常状态
        orderNo: null,//订单编号
        orderNumber: null,//单号
        remark: null,//备注内容
        trackUserName: null,//跟单员
        purchaseInType: null,//入库状态
        isSigned: null,//是否签收
        is1688: null,//是否1688
        consignmentTypes: null,//发货方式
        expectedDeliveryStartTime: null,//预计发货开始时间
        expectedDeliveryEndTime: null,//预计发货结束时间
        logisticsBogDown: false,
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
      },
      pager: { OrderBy: "", IsAsc: false },
      timeRanges: [],
      tableData: [],
      total: 0,
      loading: false,
      summaryarry: {},//汇总
      pickerOptions,
      rules: {
        sureNoSend: [
          { required: true, message: '请输入已确认未发货', trigger: 'blur' },
        ],
        sureSend: [
          { required: true, message: '请输入已发货', trigger: 'blur' },
        ]
      },
      returnQuery: {
        ids: [],
        trackStatus: '已确认未发货',
      },
      StartChatProps: [],
      isUpdate: false,
      consignmentTypesListDb: []
    }
  },
  async mounted() {
    this.getConsignmentTypes()
    await this.init()
    this.getList()
  },
  methods: {
    synchronousJst() {
      if (this.returnQuery.ids.length == 0) return this.$message.error('请选择要操作的数据')
      this.$confirm('此操作将会同步到聚水潭, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await purchaseOrderTrackSyncToJst(this.returnQuery.ids)
        if (success) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.getList()
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    },
    async getConsignmentTypes() {
      const { data, success } = await getDictionaryListPage({ name: 'consignmentTypes' })
      if (success) {
        const res = data.list.filter(item => item.code == 'purchaseOrderTrack')[0]
        this.consignmentTypesListDb = res.value ? res.value.split(',').map(item => {
          return {
            label: item,
            value: item
          }
        }) : []
      }
    },
    async StartChat() {
      if (this.StartChatProps.length == 0) return this.$message.error('请选择数据')
      if (this.StartChatProps.length != 1) return this.$message.error('只能选择一条数据')
      this.pageLoading = true
      const input = document.createElement('input');
      input.setAttribute('readonly', 'readonly');
      input.setAttribute('value', `老板您好! 请尽快安排${this.StartChatProps[0].indexNo}发货!`);
      document.body.appendChild(input);
      input.select();
      if (document.execCommand('copy')) {
        document.execCommand('copy');
        this.$message.success('已将快捷语复制到剪切板');
      }
      document.body.removeChild(input);
      const { data, success } = await GetAliSupperChatIdByShow({ indexNo: this.StartChatProps[0].indexNo })
      if (success) {
        window.open(`ali1688im:sendmsg?touid=cnalichn${data}`, '_self');
      } else {
        this.$message.error('获取聊天窗口失败');
        this.pageLoading = false
      }
      this.pageLoading = false
    },
    getSelectProp(val) {
      if (val.length != 1) return this.$message.error('只能选择一条数据')
      // const { data, success } = val[0]
    },
    orderNumberCallback(e) {
      this.ListInfo.orderNumber = e
    },
    onCopyNumber() {
      if (this.checkVerify) {
        this.$message.error('请选择勾选要复制的采购单号')
        return
      }
      let verify = false
      if (this.copyData.length > 100) {
        this.$message.error('最大允许复制值为100条，请重新选择');
        verify = true
      }
      if (verify) {
        return
      }
      let textToCopy = ' ';
      if (this.copyData.length > 0) {
        textToCopy = this.copyData.join('\n');
      }
      let textarea = document.createElement("textarea")
      textarea.value = textToCopy
      textarea.readOnly = "readOnly"
      document.body.appendChild(textarea)
      textarea.select()
      let result = document.execCommand("copy")
      if (result) {
        this.$message({ message: '复制当前页所选行采购单号成功', type: 'success' })
      }
      textarea.remove()
    },
    //获取勾选行数据
    oddNumber(data) {
      this.copyData = []
      if (data.length > 0) {
        this.checkVerify = false
      } else {
        this.checkVerify = true
      }
      this.copyData = data.filter(item => item.buyNo !== null).map(item => item.buyNo);
    },
    //根据返回字段判断采购员查询条件是否可见
    onrevealing(val) {
      this.buyerInvisible = val
    },
    returnStatus() {
      if (this.returnQuery.ids.length == 0) return this.$message.error('请选择要操作的数据')
      this.$confirm('此操作会将选中数据变为已确认未发货, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await updateTrackStatus(this.returnQuery)
        if (success) {
          this.getList()
          this.returnQuery.ids = []
        }
        this.$message({
          type: 'success',
          message: '操作成功!'
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        });
      });
    },
    getIds(ids) {
      this.returnQuery.ids = ids
    },
    getSelectProp(val) {
      this.StartChatProps = val
    },
    buyNoCallback(e) {
      this.ListInfo.buyNo = e
    },
    goodsCodeCallback(e) {
      this.ListInfo.goodsCode = e
    },
    changeIsOnft(e) {
      if (e && e.length > 1) {
        let isOnft = 0
        e.forEach(item => {
          isOnft = isOnft | Number(item)
        })
        this.ListInfo.isOnft = isOnft
      } else if (e && e.length == 1) {
        this.ListInfo.isOnft = e[0]
      } else {
        this.ListInfo.isOnft = null
      }
    },
    //tab切换
    handleClick(tab, event) {
      this.activeName = tab.name
      this.$nextTick(() => {
        this.copyData = []
        this.reset()
        this.getList()
      })
    },
    //导出
    async derive() {
      this.pageLoading = true
      await exportData({ ...this.ListInfo, isCpt: true }).then(({ data }) => {
        if (data) {
          const aLink = document.createElement("a");
          let blob = new Blob([data], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', '采购单跟进已确认数据' + new Date().toLocaleString() + '.xlsx')
          aLink.click()
          this.pageLoading = false
        }
      }).catch(() => {
        this.pageLoading = false
      })
    },
    //重置
    reset() {
      const arr = ['startTime', 'endTime', 'currentPage', 'pageSize', 'orderBy', 'isAsc']
      for (let key in this.ListInfo) {
        if (!arr.includes(key)) {
          this.ListInfo[key] = null
        }
      }
      this.lastTimeRanges = []
      this.expectedTimeRange = []
      this.expectedTimeRangeTwo = []
      this.checkList = []
    },
    //初始化
    async init() {
      const { data } = await getAllProBrand();
      this.brandlist = data.map(item => {
        return { value: item.key, label: item.value };
      });
      var res4 = await getAllWarehouse();
      var warehouselist1 = [];
      res4.data.map((item) => {
        if (item.name.indexOf('代发') == -1) {
          warehouselist1.push(item)
        }
      })
      this.warehouselist = warehouselist1;
      const { data: data1 } = await getUserInfo()
      if (data1.brandId && data1.brandId > 0) {
        this.isView = false
      }
    },
    //删除
    removeProps(index) {
      this.notification.noticeTimes.splice(index, 1);
    },
    //新增
    addProps() {
      this.notification.noticeTimes.push('');
    },
    //保存
    async onSubmitDot() {
      for (let i = 0; i < this.notification.noticeTimes.length; i++) {
        if (!this.notification.noticeTimes[i]) {
          this.$message.error('请填写完整通知时间')
          return
        }
      }
      let unTime = false;
      let noticeTimes = this.notification.noticeTimes;
      let uniqueTimes = new Set(noticeTimes);
      if (noticeTimes.length > uniqueTimes.size) {
        this.$message.error('存在重复的时间！');
        unTime = true;
      }
      if (unTime) {
        return;
      }
      let formattedNoticeTimes = this.notification.noticeTimes.filter(time => time !== ':').map(time => time.replace(':', '')).join(',');
      const params = {
        noticeTimes: formattedNoticeTimes,
        isOver15DaysUnCpt: this.notification.isOver15DaysUnCpt,
        isOverMonthUnCpt: this.notification.isOverMonthUnCpt,
        sureNoSend: this.notification.sureNoSend,
        sureSend: this.notification.sureSend,
        logisticsStasis: this.notification.logisticsStasis
      }
      const { success } = await setting(params)
      if (success) {
        this.$message.success('设置成功')
        this.getList()
        this.changeTimeSettings = false
      } else {
        this.$message.error('设置失败')
      }
      const query = {
        parentId: 0,
        name: 'consignmentTypes',
        code: 'purchaseOrderTrack',
        description: '采购单跟进发货方式',
        enabled: true,
        id: this.notification.consignmentTypesId,
        value: this.notification.consignmentTypes ? this.notification.consignmentTypes.replace(/[\uff0c,\s,\n]/g, ',') : ''
      }
      console.log(this.isUpdate, 'this.isUpdate');
      if (this.isUpdate) {
        await editDictionary(query)
      } else {
        await addDictionary(query)
      }
      this.$refs.underWay.getConsignmentTypes()
      await this.getConsignmentTypes()
    },
    //通知设置
    async notificationSettings() {
      const { data, success } = await getSetting()
      if (success && data) {
        let times = data.noticeTimes ? data.noticeTimes.split(',').map(time => {
          return time.slice(0, 2) + ':' + time.slice(2);
        }) : [];
        this.notification = data ? data : this.notification
        this.notification.noticeTimes = times
        this.changeTimeSettings = true
      } else {
        this.$message.error('获取失败')
      }
      const { data: data1, success: success1 } = await getDictionaryListPage({ name: 'consignmentTypes' })
      if (success1) {
        if (!data1) {
          //如果没有数据,就说明没有新增发货方式,就为新增
          this.isUpdate = false
        } else {
          //如果有数据,就说明有新增发货方式,就为修改
          data1.list.forEach(item => {
            if (item.code == 'purchaseOrderTrack') {
              this.isUpdate = true
            }
          })
        }
        const res = data1.list.filter(item => item.code == 'purchaseOrderTrack')[0]
        this.$set(this.notification, 'consignmentTypes', res.value)
        this.$set(this.notification, 'consignmentTypesId', res.id)
        // await getDictionary({ id: res.id })
      } else {
        this.$message.error('获取发货方式失败')
      }
    },
    //获取列表
    getList() {
      if (this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(3, 'month').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.$nextTick(() => {
        if (this.activeName == 'first1') {
          this.$refs.underWay.getConsignmentTypes()
          this.$refs.underWay.getList('search', this.ListInfo, false)
          this.$refs.underWay.clearCheckBox()
        } else {
          this.$refs.completed.getList('search', this.ListInfo, true)
        }
      })
    },
    //时间改变
    async changeTime(e, val) {
      if (val == 1) {
        this.ListInfo.startTime = e ? e[0] : null
        this.ListInfo.endTime = e ? e[1] : null
      } else if (val == 2) {
        this.ListInfo.lastTrackStartTime = e ? e[0] : null
        this.ListInfo.lastTrackEndTime = e ? e[1] : null
      } else if (val == 3) {
        this.ListInfo.expectedDeliveryStartTime = e ? e[0] : null
        this.ListInfo.expectedDeliveryEndTime = e ? e[1] : null
      } else if (val == 4) {
        this.ListInfo.expectedSendStartTime = e ? e[0] : null
        this.ListInfo.expectedSendEndTime = e ? e[1] : null
      }
    },

  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 5px;
  flex-wrap: wrap;

  .publicCss {
    width: 200px;
    margin: 0 5px 5px 0;
  }
}

.buttonpublic {
  position: absolute;
  right: 0;
  top: 40px;
  margin-right: 15px;
}

.headerBtn {
  display: flex;
  align-items: center;
  position: absolute;
  top: 120px;
  right: 16px;
}
</style>
