<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.status" placeholder="匹配状态" class="publicCss" clearable>
                    <el-option label="匹配成功" :value="1" />
                    <el-option label="待再匹配" :value="2" />
                </el-select>
                <el-input v-model="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.scanUserName" placeholder="扫码人" maxlength="50" clearable
                    class="publicCss" />
                <el-date-picker v-model="codeTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                <el-input v-model="ListInfo.orderNoInner" placeholder="内部订单号" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.isPrint" placeholder="面单打印" class="publicCss" clearable>
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                </el-select>
                <el-select v-model="ListInfo.wmsId" placeholder="发货仓" class="publicCss" clearable>
                    <el-option v-for="item in wareHouseList" :key="item.wms_co_id" :label="item.name"
                        :value="item.wms_co_id" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="exportProps">导出</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :toolbarshow="false"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%; height:680px; margin: 0" v-loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { pageGetScanRecord, exportScanRecord, getWcScanSendWmses } from '@/api/vo/prePackScan'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '匹配状态', formatter: (row) => row.statusStr },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'goodsCode', label: '商品编码' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderCount7Days', label: '近7日订单', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'scanUserName', label: '扫码人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'wmsName', label: '发货仓', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'scanTime', label: '扫码时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNoInner', label: '匹配订单号', type: 'orderLogInfo', orderType: 'orderNoInner' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'isPrint', label: '面单打印', formatter: (row) => { return row.isPrint ? '是' : '否' } },
]
export default {
    name: "scanLog",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                scanStartDate: null,//扫码开始时间
                scanEndDate: null,//扫码结束时间
                scanUserName: null,//扫码人
                wmsId: null,//发货仓
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            wareHouseList: [],
            codeTimeRanges: []
        }
    },
    async mounted() {
        await this.getWareHouse()
        await this.getList()
    },
    methods: {
        async getWareHouse() {
            const params = {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            }
            const { data } = await getWcScanSendWmses(params)
            this.wareHouseList = data
        },
        async changeTime(e) {
            this.ListInfo.scanStartDate = e ? e[0] : null
            this.ListInfo.scanEndDate = e ? e[1] : null
            await this.getList()
        },
        async exportProps() {
            const { data } = await exportScanRecord(this.ListInfo)
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/zip" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '扫码记录' + new Date().toLocaleString() + '.zip')
            aLink.click()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
            }
            const replaceArr = ['orderNo', 'orderNoInner'] //替换空格的方法,该数组对应str类型的input双向绑定的值
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data: { list, total }, success } = await pageGetScanRecord(this.ListInfo)
            if (success) {
                this.tableData = list
                this.total = total
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
