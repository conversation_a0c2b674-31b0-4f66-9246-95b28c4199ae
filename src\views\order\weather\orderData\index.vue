<template>
  <div style="box-sizing: border-box; overflow-y: hidden;">
    <el-tabs v-model="activeName" @tab-click="handleClick" class="thrcss">
      <!--订单数据 start-->
      <el-tab-pane label="订单数据" name="first">
      <template>
        <my-container class="fatcss" v-loading="pageLoading">
          <template #header>
            <el-form :inline="true" :model="query" @submit.native.prevent>
              <el-form-item label="时间:">
                <el-row :gutter="10">
                  <el-col :span="1.5">
                  </el-col>
                  <el-col :span="1.5">
                    <el-date-picker
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      v-model="query.timerange"
                      type="daterange"
                      unlink-panels
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      :picker-options="pickerOptions"
                      @change="updateQueryDate"
                      >
                    </el-date-picker>
                  </el-col>
                </el-row>
              </el-form-item>
              <el-form-item>
                <el-cascader collapse-tags v-model="query.addressNames" :props="cascaderProps" :options="options"
                  placeholder="选择城市" clearable filterable ></el-cascader>
              </el-form-item>
              <el-form-item>
                <el-select v-model="query.weather" placeholder="选择天气" clearable>
                  <el-option v-for="(item, i) in weatherList" :value="item" :key="i">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="白天气温: " prop="">
                <el-col :span="1.5" style="width:65px;">
                  <el-input v-model.trim="query.dayTemperStart" @input="handleTemperValidate" clearable>
                    <template slot="suffix">℃</template>
                  </el-input>
                </el-col>
                <el-col :span="1.5">
                  <span>~</span>
                </el-col>
                <el-col :span="1.5" style="width:65px;">
                  <el-input v-model.trim="query.dayTemperEnd" @input="handleTemperValidate" clearable>
                    <template slot="suffix">℃</template>
                  </el-input>
                </el-col>
              </el-form-item>

              <el-form-item label="晚上气温: " prop="">
                <el-col :span="1.5" style="width:65px;">
                  <el-input v-model.trim="query.nightTemperStart" @input="handleTemperValidate" clearable>
                    <template slot="suffix">℃</template>
                  </el-input>
                </el-col>
                <el-col :span="1.5">
                  <span>~</span>
                </el-col>
                <el-col :span="1.5" style="width:65px;">
                  <el-input v-model.trim="query.nightTemperEnd" @input="handleTemperValidate" clearable>
                    <template slot="suffix">℃</template>
                  </el-input>
                </el-col>
              </el-form-item>
              <el-form-item><el-button type="primary" @click="onSearch">查询</el-button></el-form-item>
              <el-form-item><el-button type="primary" @click="onExportStatisticData">导出</el-button></el-form-item>
            </el-form>
          </template>

          <!--列表 orderStyleCodeTableCols-->
          <vxetablebase :ib="'orderData_index202408041831_1'" ref="table" :id="'YunHanOrderData'" :that='that' :isIndex='true' :hasexpand='true'
            @sortchange='orderSortChange' :tableData='dataList' :tableCols='orderTableCols'
            :border='true' :loading="pageLoading">
          </vxetablebase>
          <!--分页-->
          <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getOrderList" />
          </template>
          <!-- </div> -->
        </my-container>
      </template>
    </el-tab-pane>
    <!--订单数据 end-->
    <!--系列编码数据 start-->
    <el-tab-pane label="系列编码数据" name="second">
      <template>
        <my-container class="fatcss" v-loading="styleCodePageLoading">
          <template #header>
            <el-form :inline="true" :model="styleCodeQuery" @submit.native.prevent>
              <el-form-item label="时间:">
                <el-row :gutter="10">
                  <el-col :span="1.5">
                  </el-col>
                  <el-col :span="1.5">
                    <el-date-picker
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      v-model="styleCodeQuery.timerange"
                      type="daterange"
                      unlink-panels
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      :picker-options="pickerOptions"
                      @change="updateQueryDate"
                      >
                    </el-date-picker>
                  </el-col>
                </el-row>
              </el-form-item>
              <el-form-item>
                <el-cascader collapse-tags v-model="styleCodeQuery.addressNames" :props="cascaderProps" :options="options"
                  placeholder="选择城市" clearable filterable style="width:100%;"></el-cascader>
              </el-form-item>
              <el-form-item>
                <el-select v-model="styleCodeQuery.weather" placeholder="选择天气" clearable>
                  <el-option v-for="(item, i) in weatherList" :value="item" :key="i">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="白天气温: " prop="">
                <el-col :span="1.5" style="width:65px;">
                  <el-input v-model.trim="styleCodeQuery.dayTemperStart" @input="handleTemperValidate" clearable>
                    <template slot="suffix">℃</template>
                  </el-input>
                </el-col>
                <el-col :span="1.5">
                  <span>~</span>
                </el-col>
                <el-col :span="1.5" style="width:65px;">
                  <el-input v-model.trim="styleCodeQuery.dayTemperEnd" @input="handleTemperValidate" clearable>
                    <template slot="suffix">℃</template>
                  </el-input>
                </el-col>
              </el-form-item>
              
              <el-form-item label="晚上气温: " prop="">
                <el-col :span="1.5" style="width:65px;">
                  <el-input v-model.trim="styleCodeQuery.nightTemperStart" @input="handleTemperValidate" clearable>
                    <template slot="suffix">℃</template>
                  </el-input>
                </el-col>
                <el-col :span="1.5">
                  <span>~</span>
                </el-col>
                <el-col :span="1.5" style="width:65px;">
                  <el-input v-model.trim="styleCodeQuery.nightTemperEnd" @input="handleTemperValidate" clearable>
                    <template slot="suffix">℃</template>
                  </el-input>
                </el-col>
              </el-form-item>
              <el-form-item>
                <el-col :span="1.5" style="width:145px;">
                    <el-input v-model.trim="styleCodeQuery.styleCode" placeholder="系列编码" :maxlength="20" clearable />
                </el-col>
              </el-form-item>
              <el-form-item><el-button type="primary" @click="onSearchStyleCodeQuery">查询</el-button></el-form-item>
              <el-form-item><el-button type="primary" @click="exportStyleCodeQueryData">导出</el-button></el-form-item>
            </el-form>
          </template>

          <!--列表 orderStyleCodeTableCols-->
          <vxetablebase :ib="'orderData_index202408041831_2'" ref="table" :id="'YunHanOrderStyleCodeData'" :that='that' :isIndex='true' :hasexpand='true'
            @sortchange='orderStyleCodeSortChange' :tableData='styleCodeDataList' :tableCols='orderStyleCodeTableCols'
            :border='true' :loading="pageStyleCodeLoading">
          </vxetablebase>
              <!--分页-->
          <template #footer>
            <my-pagination
              ref="styleCodePager"
              :total="styleCodeTotal"
              :checked-count="sels.length"
              @get-page="getStyleCodeOrderList"
            />
          </template>
        <!-- </div> -->
        </my-container>
      </template>
    </el-tab-pane>
    <!--系列编码数据 end-->

    <!--天气预报 start-->
    <el-tab-pane label="天气预报" name="third">
      <my-container  v-loading="pageLoading">
        <tianqiyubao ></tianqiyubao>
      </my-container>
    </el-tab-pane>
  <!--天气预报 end-->
    </el-tabs>
  </div>
</template>

  <!--{ istrue: true, prop: 'cityPeoples', label: '城市人口(万)', width: '160'},
  { istrue: true, prop: 'createTime', label: '创建时间', width: '150', formatter: (row) => formatTime(row.createTime, 'YYYY-MM-DD HH:mm:ss') }-->
<script>
import { formatTime } from "@/utils";
import { queryWeatherData, weatherForecastExport, getProvinceCityList } from "@/api/order/weather";
import { pageData, pageStyleCodeOrderData, orderExport, styleCodeOrderExport } from "@/api/order/orderData";
import MyContainer from '@/components/my-container';
import MyConfirmButton from '@/components/my-confirm-button';
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from "@/utils/tools";
import tianqiyubao from "@/views/order/weather/index.vue";

const orderTableCols = [
  { istrue: true, prop: 'orderPayDate', label: '付款时间', width: '160' },
  { istrue: true, prop: 'addressName', label: '城市', width: '160' },
  { istrue: true, prop: 'weather', label: '天气', width: '150' },
  { istrue: true, prop: 'dayTemper', label: '白天气温', width: '150', formatter: (row) => row.dayTemper +"℃" },
  { istrue: true, prop: 'nightTemper', label: '夜晚气温', width: '150', formatter: (row) => row.nightTemper +"℃"  },
  { istrue: true, prop: 'orderCount', label: '订单数', width: '150', sortable: 'custom'},
  { istrue: true, prop: 'sequentialGrowth', label: '环比增长%', width: '110',  type: 'html',
    formatter: (row) => row.sequentialGrowth == null?'': row.sequentialGrowth ==0?'0':
    row.sequentialGrowth>0?row.sequentialGrowth+'<i class="el-icon-top" style="color: green;"/>':
    row.sequentialGrowth+'<i class="el-icon-bottom" style="color: red;"/>'}
];

const orderStyleCodeTableCols = [
  { istrue: true, prop: 'orderPayDate', label: '付款时间', width: '160' },
  { istrue: true, prop: 'addressName', label: '城市', width: '160' },
  { istrue: true, prop: 'weather', label: '天气', width: '150' },
  { istrue: true, prop: 'dayTemper', label: '白天气温', width: '150', formatter: (row) => row.dayTemper +"℃" },
  { istrue: true, prop: 'nightTemper', label: '夜晚气温', width: '150', formatter: (row) => row.nightTemper +"℃"  },
  { istrue: true, prop: 'styleCode', label: '序列编码', width: '220' },
  { istrue: true, prop: 'orderCount', label: '订单数', width: '150', sortable: 'custom'},
  { istrue: true, prop: 'sequentialGrowth', label: '环比增长%', width: '110',  type: 'html',
    formatter: (row) => row.sequentialGrowth == null?'': row.sequentialGrowth ==0?'0':
    row.sequentialGrowth>0?row.sequentialGrowth+'<i class="el-icon-top" style="color: green;"/>':
    row.sequentialGrowth+'<i class="el-icon-bottom" style="color: red;"/>'}
];

export default {
  components: { MyContainer,MyConfirmButton, cesTable, vxetablebase, tianqiyubao },
  data() {
    return {
      that: this,
      orderTableCols: orderTableCols,
      orderStyleCodeTableCols: orderStyleCodeTableCols,
      activeName: 'first',
      defaultDialogWidth: "80%",
      dialogWidth: _.cloneDeep(this.defaultDialogWidth),
      provinceCityCodeList: [],
      weatherList: ["晴","雨","阴","其它"],
      weather:"",
      dataList: [],
      total: 0,
      options : [],
      pageLoading: false,
      orderPageLoading: false,
      pageStyleCodeLoading: false,
      isQuery: false,
      styleCodePager:[],
      radio: '昨天',
      sels: [], // 列表选中列
      // 查询参数
      query: {
        pageNum: 1,
        pageSize: 50
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      styleCodeDataList: [],
      styleCodeTotal: 0,
      styleCodePageLoading: false,
      isStyleCodeQuery: false,
      styleCodeQuery: {
        pageNum: 1,
        pageSize: 50
      },
      cascaderProps: { multiple: true, label: 'name', value: 'name', children: 'childList' },
      //tab3
      currentDate: this.formatDate(),
      weatherQuery: {
        date: this.getLastDayOfCurrentMonth(),
        province: "",
        city: ""
      },
      weatherCascaderProps: { multiple: true, label: 'name', value: 'code', children: 'childList' },
    }
  },
  async mounted() {
    await this.onSearch();
  },
  created() {
    this.listProvinceAndCity();
  },
  methods: {
    handleClick(tab, event) {
      if (tab.index == 0) {
        this.onSearch();   
      } else if (tab.index == 1) {
        this.onSearchStyleCodeQuery();
      } else {
      //网络请求3
      //  this.init();
      }
    },
    updateQueryDate(e) {
      if(e==null){
        this.query.orderPayDateStart =  ''
        this.query.orderPayDateEnd = ''
      }   
    },
    updateStyleCodeQueryDate(e)  {
      if(e==null){
        this.styleCodeQuery.orderPayDateStart =  ''
        this.styleCodeQuery.orderPayDateEnd = ''
      }   
    },

    handleTemperValidate(value){
      if(value==null || value=='' || value=='-'|| value=='0'){
        return true;
      }
      value=parseInt(value);
      const regex = /^-?[1-4][0-9]$|^-?50$/;
      const regex2 = /^-[0-8]$|^[1-8]$|^9$/;
      if (!regex.test(value) && !regex2.test(value) ) {
        // 如果不是数字，则将输入值设置为上一个有效值
        this.$message({message:"气温请输入大于-50小于50整数！",type:"warning"});
        return false;
      }
      return true;
    },
    // 选择
    onSelsChange(sels) {
      console.log("onSelsChange",sels);
      this.sels = sels;
    },
    setCurrent(row) {
      this.$refs.singleTable.setCurrentRow(row);
    },

    handleCurrentChange(val) {
      console.log("handleCurrentChange",val);
      this.currentRow = val;
    },

    listProvinceAndCity() {
      getProvinceCityList().then(response => {
        if (response.data) {
          this.provinceCityList = response.data;
          this.options = response.data
        }
      });
    },

    validateTemper(q){
      if(q.dayTemperStart=="-" || "-" == q.dayTemperEnd ||"-" == q.nightTemperStart || "-" == q.nightTemperEnd){
        this.$message({message:"气温请输入大于-50小于50整数！",type:"warning"});
        return false;
      }

      if(!this.handleTemperValidate(q.dayTemperStart) || !this.handleTemperValidate(q.dayTemperEnd)
      ||!this.handleTemperValidate(this.query.nightTemperStart) || !this.handleTemperValidate(q.nightTemperEnd)){
        return false;
      }
      return true;
    },

    //tab1
    onSearch(sortColumn, isAsc) {
      if(!this.validateTemper(this.query)){
        return;
      }
      const addressName = new Array();
      if (this.query.addressNames!=null) {
        this.query.addressNames.forEach((d) => {
          addressName.push(d[1]);
        });
        this.query.addressNameList = addressName;
      }
      if (this.query.timerange!=null) {
        this.query.orderPayDateStart = this.query.timerange[0];
        this.query.orderPayDateEnd = this.query.timerange[1];
      }
      this.$refs.pager.setPage(1);
      this.getOrderList();
      this.orderPageLoading = false;
    },

    async getOrderList() {
      var pager = this.$refs.pager.getPager();
      this.query.pageSize = pager.pageSize;
      const params = {
        ...pager,
        ...this.query,
        ...this.pager
      };
      const res = await pageData(params);
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const data = res.data.list;
      data.forEach((d) => {
        d._loading = false;
      });
      this.dataList = data;
    },

   //排序查询
   async orderSortChange(column) {
      if (!column.order)
        this.pager = {};
      else {
        var orderBy = column.prop;
        this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    },

    async onExportStatisticData() {
      if(!this.validateTemper(this.query)){
        return ;
      }
      var emptyQuery = false;
      if (!this.query.timerange && !this.query.addressNames && !this.query.weather
        && !this.query.dayTemperStart && !this.query.dayTemperEnd && !this.query.nightTemperStart && !this.query.nightTemperEnd) {
          this.$message({message:"不选择筛选条件默认导出当天数据,导出中！",type:"warning"});
          emptyQuery = true;
      } 
        
        if (this.query.addressNames!=null) {
          const addressName = new Array();
          this.query.addressNames.forEach((d) => {
            addressName.push(d[1]);
          });
          this.query.addressNameList = addressName;
        }
        if (this.query.timerange!=null) {
          this.query.orderPayDateStart = this.query.timerange[0];
          this.query.orderPayDateEnd = this.query.timerange[1];
        }
        const params = {...this.query};
        if(!emptyQuery){
          this.$message({message:"导出中",type:"info"});
        }
        const { data } = await orderExport(params)
        if (!data) {
          this.$message({message:"没有数据！",type:"warning"});
          return;
        } 
        const aLink = document.createElement("a");
        let blob = new Blob([data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '导出每天-城市天气-订单统计' + new Date().toLocaleString() + '.xlsx')
        aLink.click()
    },
    //tab2
    async orderStyleCodeSortChange(column) {
      console.log("sort by ",column);
      if (!column.order)
        this.pager = {};
      else {
        var orderBy = column.prop;
        this.styleCodePager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearchStyleCodeQuery();
    },

    async exportStyleCodeQueryData() {
      if(!this.validateTemper(this.styleCodeQuery)){
        return ;
      }
      var emptyQuery = false;
      if (!this.styleCodeQuery.timerange && !this.styleCodeQuery.addressNames && !this.styleCodeQuery.weather && !this.styleCodeQuery.styleCode 
        && !this.styleCodeQuery.dayTemperStart && !this.styleCodeQuery.dayTemperEnd && !this.styleCodeQuery.nightTemperStart && !this.styleCodeQuery.nightTemperEnd) {
        this.$message({message:"不选择筛选条件默认导出当天数据,导出中！",type:"warning"});
        emptyQuery = true;
      } 
      if (this.styleCodeQuery.addressNames!=null) {
        const addressName = new Array();
        this.styleCodeQuery.addressNames.forEach((d) => {
          addressName.push(d[1]);
        });
        this.styleCodeQuery.addressNameList = addressName;
      }
      if (this.styleCodeQuery.timerange!=null) {
        this.styleCodeQuery.orderPayDateStart = this.styleCodeQuery.timerange[0];
        this.styleCodeQuery.orderPayDateEnd = this.styleCodeQuery.timerange[1];
      }
      const params = {...this.styleCodeQuery};
      if(!emptyQuery){
          this.$message({message:"导出中",type:"info"});
        }
      const { data } = await styleCodeOrderExport(params)
      console.log("export data",data);
      if (!data && !data.size && data.size==0) {
        this.$message({message:"没有数据！",type:"warning"});
        return;
      } 
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '导出每天-城市天气-序列编号订单统计' + new Date().toLocaleString() + '.xlsx');
      aLink.click();
   },
    
    onSearchStyleCodeQuery() {
      if(!this.validateTemper(this.styleCodeQuery)){
        return ;
      }
      if (this.styleCodeQuery.addressNames!=null) {
        const addressName = new Array();
        this.styleCodeQuery.addressNames.forEach((d) => {
          addressName.push(d[1]);
        });
        this.styleCodeQuery.addressNameList = addressName;
      }
      if (this.styleCodeQuery.timerange!=null) {
        this.styleCodeQuery.orderPayDateStart = this.styleCodeQuery.timerange[0];
        this.styleCodeQuery.orderPayDateEnd = this.styleCodeQuery.timerange[1];
      }
      this.getStyleCodeOrderList();
    },

    async getStyleCodeOrderList() {
      var pager = this.$refs.styleCodePager.getPager();
      this.styleCodeQuery.pageSize = pager.pageSize;
      const params = {
        ...pager,
        ...this.styleCodeQuery,
        ...this.styleCodePager,
      };
      this.listLoading = true;
      const res = await pageStyleCodeOrderData(params);
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.styleCodeTotal = res.data.total;
      const data = res.data.list;
      data.forEach((d) => {
        d._loading = false;
      });
      this.styleCodeDataList = data;
    },
    //tab3

    formatDate() {
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const day = now.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    getLastDayOfCurrentMonth() {
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const day = 1;
      return `${year}-${month}-${day}`;
    },
    
    init() {
      let queryParams = {
        obtainDate: this.query.date,
        cityCodeList: ['59287', '59493', 'S1003', '57494', '58457', '58349', '59289', 'V8870', '58606', '58238']
      }
      queryWeatherData(queryParams).then(response => {
        if (response.data) {
          this.dataList = response.data;
        }
      });
    },

    exportWeatherData() {
      if (!this.isQuery) {
        this.$message.warning('请先查询数据后导出！');
      } else {
        this.pageLoading = true;
        weatherForecastExport(this.params).then(response => {
          this.pageLoading = false;
          const aLink = document.createElement("a");
          var blob = new Blob([response], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', '天气预报导出.xlsx')
          aLink.click()
        });
      }
    },

    change(val) {
      val.forEach(it => this.params.cityCodeList.push(it.code))
    },

  }
}
</script>

<style lang="scss" scoped>
.like {
  cursor: pointer;
  font-size: 25px;
  // display: inline-block;
}

.el-badge__content.is-fixed {
  top: 30px;
  right: 45px;
}

.fatcss ::v-deep .mycontainer{
  height: 75vh; /* 设置元素的高度为视口高度的75% */
  overflow-y: auto;
}


.body_hang {
  // flex: 1;
  // min-height: 92vh;
  // margin-top: 20px;

  .item {
    // margin-top: 30px;
    // margin-right: 45px;
  }

  .card-container {
    display: -webkit-box;
    // justify-content: 5px;
    /* 控制卡片之间的间隔 */
    margin: 10px;

    .card-item {
      // width: 180px;
      /* 控制卡片宽度 */
      margin: 0 0px;
      /* 控制卡片外边距 */
      /* 设置边框宽度 */
      border-width: 3px;
      /* 设置边框的样式为实线 */
      border-style: solid;
      /* 设置边框的颜色为渐变色 */
      border-image: rgb(153, 152, 152);
      /* 设置边框的底部边缘圆角 */
      border-radius: 0 0 10px 10px;
      // height: 200px;
    }
  }
}

.el-badge__content.is-fixed {
  top: 30px;
  right: 45px;
}

#largeNumber {
  font-size: 100px;
  /* 设置字体大小 */
  font-weight: bold;
  /* 字体加粗 */
  text-align: center;
  /* 文字居中 */
  color: rgb(223, 238, 243);
  /* 文字颜色 */
}

.card-item ::v-deep .el-card__body {
  padding: 0 !important;
}

.el-cascader .el-input .el-input__inner:focus,
.el-cascader .el-input.is-focus .el-input__inner {
  height: 34px; //这里高度根据需求自己设定
}

.el-input--mini .el-input__inner {
  height: 34px;
  line-height: 28px;
}

.el-cascader__tags {
  // display: inline-flex;
  // margin-right: 60px;
  // flex-wrap: nowrap;
  margin:2px;
}
::v-deep .el-cascader__search-input {
  margin: 2px 0 2px 1px !important;
  padding-left: 2px;
}
#redcolor {
  font-size: 12px;
  /* 设置字体大小 */
  font-weight: normal;
  color: red;
  font-style: italic;
}
.thrcss ::v-deep .el-main{
  overflow-y: hidden;
}

</style>