<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="发生开始时间" end-placeholder="发生结束时间" :picker-options="pickerOptions"
          style="width: 270px;margin-right: 10px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <div style="display: flex;gap: 10px;margin-right: 10px;">
          <inputYunhan ref="buyNo" :inputt.sync="ListInfo.buyNo" v-model="ListInfo.buyNo" :valuedOpen="true"
            width="170px" placeholder="采购单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="1000" @callback="callbackbuyNo" title="采购单号">
          </inputYunhan>
          <inputYunhan ref="warehouseNo" :inputt.sync="ListInfo.warehouseNo" v-model="ListInfo.warehouseNo"
            :valuedOpen="true" width="170px" placeholder="入库单号/若输入多条请按回车" :clearable="true" :clearabletext="true"
            :maxRows="100" :maxlength="1000" @callback="callbackwarehouseNo" title="入库单号">
          </inputYunhan>
          <inputYunhan ref="batchNumber" :inputt.sync="ListInfo.batchNumber" v-model="ListInfo.batchNumber"
            :valuedOpen="true" width="170px" placeholder="批次号/若输入多条请按回车" :clearable="true" :clearabletext="true"
            :maxRows="100" :maxlength="1000" @callback="callbackbatchNumber" title="批次号">
          </inputYunhan>
        </div>
        <el-select v-model="ListInfo.warehouse" placeholder="仓库" class="publicCss" clearable filterable multiple
          collapse-tags>
          <el-option v-for="item in warehouselist" :key="item.name" :label="item.name" :value="item.wms_co_id" />
        </el-select>
        <el-select v-model="ListInfo.brandId" placeholder="采购" class="publicCss" clearable filterable multiple
          collapse-tags>
          <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable multiple collapse-tags>
          <el-option key="待提交" label="待提交" value="待提交" />
          <el-option key="审批中" label="审批中" value="审批中" />
          <el-option key="通过" label="通过" value="通过" />
          <el-option key="拒绝" label="拒绝" value="拒绝" />
          <el-option key="作废" label="作废" value="作废" />
          <el-option key="财务初审中" label="财务初审中" value="财务初审中" />
          <el-option key="财务初审通过" label="财务初审通过" value="财务初审通过" />
          <el-option key="财务初审拒绝" label="财务初审拒绝" value="财务初审拒绝" />
        </el-select>
        <el-input v-model.trim="ListInfo.creator" placeholder="添加人" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" style="width: 80px;" @click="getList('search')">搜索</el-button>
      </div>
      <div class="top">
        <el-button type="primary" @click="startImport" v-if="checkPermission('FreightCheckWarehouseBtnPermission')">导入</el-button>
        <el-button type="primary" @click="onImportTemplate" v-if="checkPermission('FreightCheckWarehouseBtnPermission')">下载导入模版</el-button>
        <el-button type="primary" @click="exportProps" v-if="checkPermission('FreightCheckWarehouseBtnPermission')">导出</el-button>
        <!-- <el-button type="danger" @click="onDeleteOperation">删除</el-button> -->
        <el-button type="warning" @click="onInitiateApproval" v-if="checkPermission('FreightCheckWarehouseBtnPermission')">发起审批</el-button>
        <!-- <el-button type="primary" @click="onUploadCredentials">上传凭证</el-button> -->
        <el-button type="primary" @click="toPickData" v-if="checkPermission('FreightCheckWarehouseBtnPermission')">可抓取数据</el-button>
        <el-button type="primary" @click="handleAdd" v-if="checkPermission('FreightCheckWarehouseBtnPermission')">新增</el-button>
        <el-button type="primary" @click="handleFirstInstance" v-if="checkPermission('FreightCheckWarehouseBtnPermission')">财务初审</el-button>
        <el-button type="primary" @click="onDelByBatchNo" v-if="checkPermission('DelPurchaseCostVerifyWarehouse')">批次号删除</el-button>
        <el-button type="primary" @click="onDelBySel" v-if="checkPermission('DelPurchaseCostVerifyWarehouse')">勾选删除</el-button>

      </div>
    </template>
    <vxetablebase :ispoint="false" :id="'warehouseData202410151005'" :tablekey="'warehouseData202410151005'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      @select="selectchange" :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
      :height="'100%'">
      <template #status="{ row }">
        <span :style="{ color: getStatusColor(row.status) }">{{ row.status }}</span>
      </template>
      <template #totalPerTicket="{ row }">
        <div class="tableCss">
          {{ row.totalPerTicket !== null && row.totalPerTicket !== undefined ? parseFloat(row.totalPerTicket) : ' ' }}
        </div>
      </template>
      <template #orderFee1="{ row }">
        <div class="tableCss">
          {{ row.orderFee1 !== null && row.orderFee1 !== undefined ? parseFloat(row.orderFee1) : ' ' }}
        </div>
      </template>

      <template #orderFee2="{ row }">
        <div class="tableCss">
          {{ row.orderFee2 !== null && row.orderFee2 !== undefined ? parseFloat(row.orderFee2) : ' ' }}
        </div>
      </template>
      <template #haulage="{ row }">
        <div class="tableCss">
          {{ row.haulage !== null && row.haulage !== undefined ? parseFloat(row.haulage) : ' ' }}
        </div>
      </template>
      <template #deliveryFee="{ row }">
        <div class="tableCss">
          {{ row.deliveryFee !== null && row.deliveryFee !== undefined ? parseFloat(row.deliveryFee) : ' ' }}
        </div>
      </template>
      <template #pickUpFee="{ row }">
        <div class="tableCss">
          {{ row.pickUpFee !== null && row.pickUpFee !== undefined ? parseFloat(row.pickUpFee) : ' ' }}
        </div>
      </template>
      <template #huoLaLa="{ row }">
        <div class="tableCss">
          {{ row.huoLaLa !== null && row.huoLaLa !== undefined ? parseFloat(row.huoLaLa) : ' ' }}
        </div>
      </template>
      <template #loadingFee="{ row }">
        <div class="tableCss">
          {{ row.loadingFee !== null && row.loadingFee !== undefined ? parseFloat(row.loadingFee) : ' ' }}
        </div>
      </template>
      <template slot="right">
        <vxe-column title="操作" width="90" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="handleEdit(row)" v-if="(row.status == '作废' || row.status == '拒绝' || row.status == '待提交' || row.status == '财务初审拒绝') && checkPermission('FreightCheckWarehouseBtnPermission')">编辑</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="费用申请" :visible.sync="approvalDialog" width="55%" v-dialogDrag style="margin-top: -10vh;">
      <examineAndApprove ref="refexamineAndApprove" :approvalParams=approvalParams v-if="approvalDialog" @successClose="closeApprovalDialog"
        @close="approvalDialog = false" />
    </el-dialog>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="上传凭证" :visible.sync="proofVisible" width="40%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 150px;">
        <div class="chatPicUrl">
          <uploadimgFile ref="uploadimgFile" v-if="proofVisible" :disabled="isView" :ispaste="!isView" :noDel="isView"
            :reveal="false" :accepttyes="accepttyes" :isImage="true" :uploadInfo="picture" :keys="[1, 1]"
            @callback="getImg" @beforeUpload="beforeUpload" :imgmaxsize="30" :limit="30" :multiple="true">
          </uploadimgFile>
          <!-- <span class="picTips" v-if="!isView">提示:点击灰框可直接贴图,最多可上传30张！！！</span> -->
        </div>
      </div>
      <div style="display: flex;justify-content: center;gap: 10px;">
        <el-button @click="proofVisible = false">关闭</el-button>
        <el-button type="primary" @click="onSaveCertificate">上传</el-button>
      </div>
    </el-dialog>


    <el-dialog title="入库拍摄" :visible.sync="toPickDataDig" width="85%" v-dialogDrag top="10px">
      <warehousingordervidetab ref="warehousingordervidetab" @closeAddList="closeAddList" />
    </el-dialog>

    <el-dialog :title="editData.id ? '编辑' : '新增'" :visible.sync="editFormDataDig" width="35%" v-dialogDrag top="40px">
      <editForm v-if="editFormDataDig" ref="editForm" :editData="editData" @successClose="closeDialog"
        @close="editFormDataDig = false" @wsSearch="wsSearch" />
    </el-dialog>

    <el-dialog title="删除" :visible.sync="delByBatchNoVisible" width="15%" v-dialogDrag>
      <span>
        <el-input v-model.trim="delBatchNo" placeholder="批次号" clearable class="publicCss"></el-input>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="delByBatchNoVisible = false">关闭</el-button>
        <el-button type="primary" @click="btnDelByBatchNo">确认</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions, downloadLink } from '@/utils/tools'
import inputYunhan from "@/components/Comm/inputYunhan";
import { getPurchaseCostVerifyWarehousePage,
  exportPurchaseCostVerifyWarehouse,
  importPurchaseCostVerifyWarehouse,
  delPurchaseCostVerifyWarehouseByBatchNumber,
  importPurchaseCostVerifyPicture,
  submitFinancePriorAudit,
  delPurchaseCostVerifyWarehouseByBatchNo,
  delPurchaseCostVerifyWarehouseBySel
} from '@/api/inventory/purchaseCostVerify'
import {
  AddPurchaseCostVerifyForWarehouseOrderVideo,
  GetPurchaseCostVerifyInfo
} from '@/api/inventory/purchaseCostVerify.js'

import examineAndApprove from "./examineAndApprove.vue";
import { getAllProBrand, getAllWarehouse } from '@/api/inventory/warehouse'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";

import editForm from "@/views/inventory/purchaseFreightCheck/formEdit/editForm.vue";

import warehousingordervidetab from "@/views/inventory/purchaseFreightCheck/formEdit/warehousingordervidetab.vue";
import dayjs from 'dayjs'
const tableCols = [
  { istrue: true, width: '40', type: "checkbox" },
  { sortable: 'custom', width: '100', align: 'center', prop: 'status', label: '状态', },
  { sortable: 'custom', width: '150', align: 'center', prop: 'businessId', label: '流程编号', },
  { sortable: 'custom', width: '150', align: 'center', prop: 'batchNo', label: '批次号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'fsYmdDate', label: '发生日期', },
  { width: '100', align: 'center', prop: 'regionCost', label: '区域费用', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'warehousingNo', label: '入库单号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'buyNo1', label: '采购单号1', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'buyNo2', label: '采购单号2', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'brandName', label: '采购', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'warehouseName', label: '仓库', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'totalPerTicket', label: '单票合计', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'orderFee1', label: '单号1费用', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'orderFee2', label: '单号2费用', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'haulage', label: '托运费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'deliveryFee', label: '送货费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'pickUpFee', label: '提货费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'huoLaLa', label: '货拉拉', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'loadingFee', label: '装车费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'remark', label: '备注', },
  { width: '100', align: 'center', prop: 'proof1', label: '凭证1', type: "images", },
  { width: '100', align: 'center', prop: 'proof2', label: '凭证2', type: "images", },
  { width: '100', align: 'center', prop: 'proof3', label: '凭证3', type: "images", },
  { width: '100', align: 'center', prop: 'proof4', label: '凭证4', type: "images", },
  { sortable: 'custom', width: '100', align: 'center', prop: 'createdUserName', label: '添加人', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'reviewerName', label: '初审人', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'auditRemarks', label: '初审备注', },

  // {
  //   istrue: true, type: "button", width: "90", align: 'center', fixed: 'right', label: '操作',
  //   btnList: [
  //     { label: "编辑",
  //       ishide: (that, row) => (row.status == '作废' || row.status == '拒绝' || row.status == '待提交' || row.status == '财务初审拒绝') && checkPermission('FreightCheckWarehouseBtnPermission'),
  //       handle: (that, row) => that.handleEdit(row)
  //     },
  //   ]
  // },
]
export default {
  name: "warehouseData",
  components: {
    MyContainer, vxetablebase, inputYunhan, examineAndApprove, uploadimgFile, warehousingordervidetab, editForm
  },
  data() {
    return {
      editData: {},
      editRow: {},
      editFormDataDig: false,
      toPickDataDig: false,
      picture: [],
      isView: false,
      accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
      proofVisible: false,
      approvalParams: {},
      sels: [],
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      brandlist: [],//采购
      warehouselist: [],//仓库
      approvalDialog: false,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
        status: '',//状态
        creator: '',//添加人
        buyNo: '',//采购单号
        warehouseNo: '',//入库单号
        batchNumber: '',//批次号
        warehouse: '',//入库仓库
        brandId: '',//采购
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      delByBatchNoVisible: false,
      delBatchNo: null
    }
  },
  async mounted() {
    await this.init()
    await this.getList()
  },
  methods: {
    handleFirstInstance() {
      if (this.sels.length == 0) {
        this.$message({ message: "请选择需要初审的数据", type: "warning" });
        return false;
      }
      this.$confirm('是否提交财务初审?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await submitFinancePriorAudit({ ids: this.sels.map(item => item.id) })
        if (success) {
          this.$message({ message: "提交成功", type: "success" });
          this.sels = []
          await this.getList()
        }
      })
    },
    closeDialog() {
      this.editFormDataDig = false;
      this.getList();
    },
    closeApprovalDialog() {
      this.approvalDialog = false;
      this.getList();
    },
    wsSearch(row) {
      this.editData.id = row.id;
      this.$forceUpdate();
      this.handleEdit(row)
    },
    handleAdd() {
      this.editData = {};
      setTimeout(() => {
        this.editFormDataDig = true;
      }, 0);

    },
    async handleEdit(row) {
      // this.editRow = row;
      let res = await GetPurchaseCostVerifyInfo({ id: row.id });
      if (!res.success) {
        return;
      }

      // let newarr = [];
      // res.data.imgUrls.map(element => {
      //     newarr.push({
      //         url: element
      //     })
      // });
      // res.data.imgUrls = newarr;

      this.editData = res.data;

      setTimeout(() => {
        this.editFormDataDig = true;
      }, 100);
    },
    async closeAddList(value) {
      let me = this;
      let res = await AddPurchaseCostVerifyForWarehouseOrderVideo(value);

      if (!res.success) {
        return;
      }
      // if (res.data) {
      //     me.$message({
      //         message: res.data,
      //         type: "success",
      //     });
      // }
      me.tableData = res.data;
      this.$refs.warehousingordervidetab.clearList();
      me.$nextTick(() => {
        me.toPickDataDig = false;
        me.getList()
      });

    },
    toPickData() {
      this.toPickDataDig = true;
      // this.$nextTick(() => {
      //     this.$refs.warehousingordervidetab.clearList();
      // })

    },
    onImportTemplate() {
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250707/1942062189523738625.xlsx', '采购运费核对导入模版.xlsx');
    },
    getStatusColor(status) {
      if (status === '通过') {
        return '#008000';
      } else if (status === '拒绝') {
        return '#F56C6C';
      } else {
        return '#000000';
      }
    },
    async onSaveCertificate() {
      if (this.picture.length == 0) {
        this.$message({ message: "请先上传图片", type: "warning" });
        return false;
      }
      let params = JSON.stringify(this.picture)
      const { data, success } = await importPurchaseCostVerifyPicture(params)
      if (success) {
        this.$message({ message: "上传成功", type: "success" });
        this.proofVisible = false
        this.getList()
      }
    },
    beforeUpload(data) {
      if (data.length > 0) {
        this.picture = this.picture.concat(
          data.map(item => {
            return {
              url: item.url,
              name: item.name
            };
          })
        );
      }
    },
    getImg(data) {
      if (data) {
        this.picture = data.map(item => {
          return { url: item.url, name: item.fileName }
        })
      }
    },
    onUploadCredentials() {
      this.picture = []
      this.proofVisible = true
    },
    onDeleteOperation() {
      if (this.sels.length == 0) {
        this.$message({ message: "请选择数据", type: "warning" });
        return false;
      }
      let batchNumberArr = this.sels.map(item => item.batchNo)
      let batchNumberSet = new Set(batchNumberArr)
      if (batchNumberSet.size > 1) {
        this.$message({ message: "请选择相同批次号的数据", type: "warning" });
        return false;
      }
      this.$confirm('是否删除该批次所有数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { data, success } = await delPurchaseCostVerifyWarehouseByBatchNumber({ batchNumber: batchNumberArr[0] })
        //if (!success) return this.$message({ message: data, type: "warning" });
        if (success) {
          this.$message({ message: "删除成功", type: "success" });
          await this.getList()
        }
      }).catch(() => {
        this.$message({ type: 'info', message: '已取消删除' });
      });
    },
    onDelByBatchNo() {
      this.delBatchNo = null;
      this.delByBatchNoVisible = true;
    },
    async btnDelByBatchNo() {
      var param = {batchNo: this.delBatchNo};

      const { data, success } = await delPurchaseCostVerifyWarehouseByBatchNo(param);
      if (success){
        this.$message({ message: "删除成功", type: "success" });
        await this.getList();
        this.delByBatchNoVisible = false;
      }
    },
    onDelBySel() {
      if (this.sels.length == 0) {
        this.$message({ message: "请选择数据", type: "warning" });
        return false;
      }
      var ids = [];
      this.sels.map(item => {
        ids.push(item.id);
      });
      console.log('ids',ids);
      this.$confirm('是否删除所选数据？', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { data, success } = await delPurchaseCostVerifyWarehouseBySel({ ids: ids });
        if (success){
          this.$message({ message: "删除成功", type: "success" });
          await this.getList()
          this.delByBatchNoVisible = false;
        }
      }).catch(() => {
        this.$message({ type: 'info', message: '已取消删除' });
      });
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importPurchaseCostVerifyWarehouse(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    //初始化数据
    async init() {
      let res2 = await getAllProBrand();
      this.brandlist = res2.data ? res2.data.map(item => { return { value: item.key, label: item.value }; }) : [];
      let res3 = await getAllWarehouse();
      this.warehouselist = res3.data ? res3.data.filter((x) => x.name.indexOf('代发') < 0) : [];
    },
    //发起审批
    async onInitiateApproval() {
      if (this.sels.length == 0) {
        this.$message({ message: "请选择数据", type: "warning" });
        return false;
      }
      let batchArr = this.sels.map(item => item.batchNo)
      let batchSet = new Set(batchArr)
      // if (batchSet.size > 1) {
      //   this.$message({ message: "请选择相同批次号的数据", type: "warning" });
      //   return false;
      // }
      let statusArr = this.sels.map(item => item.status);
      if (!statusArr.every(status => status === '财务初审通过')) {
        this.$message({ message: "请选择状态为'财务初审通过'的数据", type: "warning" });
        return false;
      }
      this.approvalParams = this.queryCondition();
      // this.approvalParams.batchNumber = batchArr[0]
      this.approvalParams.ids = this.sels.map(item => item.id)
      this.approvalDialog = true
    },
    // 入库单号
    callbackwarehouseNo(val) {
      this.ListInfo.warehouseNo = val
    },
    // 批次号
    callbackbatchNumber(val) {
      this.ListInfo.batchNumber = val
    },
    // 采购单号
    callbackbuyNo(val) {
      this.ListInfo.buyNo = val
    },
    // 复选框数据
    selectchange(val) {
      this.sels = val
    },
    // 时间选择
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
    },
    //导出数据
    async exportProps() {
      this.loading = true
      const params = this.queryCondition();
      await exportPurchaseCostVerifyWarehouse(params)
      this.loading = false
    },
    queryCondition() {
      return {
        currentPage: this.ListInfo.currentPage,
        pageSize: this.ListInfo.pageSize,
        orderBy: this.ListInfo.orderBy,
        isAsc: this.ListInfo.isAsc,
        startDate: this.ListInfo.startDate,
        endDate: this.ListInfo.endDate,
        status: this.ListInfo.status?.length ? this.ListInfo.status.join(',') : '',
        creator: this.ListInfo.creator,
        buyNo: this.ListInfo.buyNo,
        warehouseNo: this.ListInfo.warehouseNo,
        batchNumber: this.ListInfo.batchNumber,
        warehouse: this.ListInfo.warehouse?.length ? this.ListInfo.warehouse.join(',') : '',
        brandId: this.ListInfo.brandId?.length ? this.ListInfo.brandId.join(',') : '',
      }
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给当前月第一天至今天
        this.ListInfo.startDate = dayjs().startOf('month').format('YYYY-MM-DD')
        this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      }
      this.loading = true
      const params = this.queryCondition();
      if (this.checkInputLength(params.warehouseNo, 'warehouseNo', '入库单号只能输入100个')) return;
      if (this.checkInputLength(params.batchNumber, 'batchNumber', '批次号只能输入100个')) return;
      if (this.checkInputLength(params.buyNo, 'buyNo', '采购单号只能输入100个')) return;
      const { data, success, msg } = await getPurchaseCostVerifyWarehousePage(params)
      this.loading = false
      if (success) {
        this.sels = [];
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.fsYmdDate = dayjs(item.fsYmdDate).format('YYYY-MM-DD');
          const processProof = (proof) => {
            try {
              const proofArray = JSON.parse(proof);
              if (Array.isArray(proofArray)) {
                return proofArray.map(proofItem => ({ url: proofItem.url }));
              }
            } catch (error) {
              console.error('Invalid JSON in proof:', proof);
            }
            return [];
          };
          item.Url = JSON.stringify([
            ...processProof(item.proof1),
            ...processProof(item.proof2),
            ...processProof(item.proof3),
            ...processProof(item.proof4)
          ]);
        });
        this.total = data.total
        let summary = data.summary || {};
        Object.entries(summary).forEach(([key, value]) => {
          if (typeof value !== 'string') {
            summary[key] = String(value);
          }
        });
        this.summaryarry = summary
        let arr = data.summary || {}
        let status_sum = `${arr.orderFee1_sum}-${arr.pickUpFee_sum}-${arr.haulage_sum}-${arr.deliveryFee_sum}`
        //this.summaryarry.status_sum = status_sum
        this.loading = false
      } else {
        this.$message.error(msg ? msg : '获取列表失败')
        this.loading = false
      }
    },
    checkInputLength(param, paramName, errorMessage) {
      if (param) {
        let values = param.split(',');
        if (values.length > 100) {
          this.$message.error(errorMessage);
          this.loading = false;
          return true;
        }
      }
      return false;
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 170px;
    margin-right: 10px;
  }
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}

.chatPicUrl {
  position: relative;
  height: 100px;

  .picTips {
    position: absolute;
    top: 0;
    left: 150px;
    color: #ff0000;
    font-size: 16px;
  }
}

.tableCss {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
