<template>
    <MyContainer>
        <template #header>
            <div class="header">
                <el-input v-model="ListInfo.packName" placeholder="包装名称" class="publicCss" maxlength="50" clearable />
                <el-input v-model="ListInfo.materialNames" placeholder="使用材料名称" class="publicCss" maxlength="50"
                    clearable />
                <el-input v-model="ListInfo.createdUserName" placeholder="创建人" class="publicCss" maxlength="50" clearable />
                <el-input v-model="ListInfo.modifiedUserName" placeholder="修改人" class="publicCss" maxlength="50"
                    clearable />
                <el-date-picker v-model="createTime" type="daterange" align="right" unlink-panels range-separator="至"
                    start-placeholder="创建开始时间" end-placeholder="创建结束时间" :picker-options="pickerOptions"
                    style="width: 230px;margin-right: 10px;" @change="changeTime($event, 'create')">
                </el-date-picker>
                <el-date-picker v-model="editTime" type="daterange" align="right" unlink-panels range-separator="至"
                    start-placeholder="修改开始时间" end-placeholder="修改结束时间" :picker-options="pickerOptions"
                    style="width: 230px;margin-right: 10px;" @change="changeTime($event, 'edit')">
                </el-date-picker>
                <el-button type="primary" @click="getList('click')">查询</el-button>
                <el-button type="primary" @click="addProps">新增包材资料</el-button>
                <el-button type="primary" @click="delProps(ids, 'multiple')">批量删除</el-button>
            </div>
        </template>
        <vxetablebase :id="'packagingMaterials_index202408041558'" ref="table" @select="checkboxRangeEnd" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' @sortchange='sortchange' :tableData='tableProps.tableData' :tableCols='tableCols'
            v-loading="listLoading" :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" height="100%"/>
            <template #footer>
                <my-pagination ref="pager" :total="tableProps.total" @page-change="pagechange" @size-change="sizechange" />
            </template>

        <!-- 弹窗部分 -->
        <el-dialog title="新建包材资料" :visible.sync="dialogVisible.materialDialog" v-dialogDrag :before-close="handleColse"
            class="dialog" :close-on-click-modal="false" v-loading="dialoading">
            <div @click="isFouce = false">
                <div class="dialogTopBox">
                    <div class="label">包装名称：</div>
                    <el-input v-model="addPackage.packName" placeholder="包装名称" style="width: 300px;margin-left: 10px;"
                        maxlength="50" clearable />
                    <el-button type="primary" @click="addProps('addProps')" style="margin-left: 10px;">新增材料信息</el-button>
                </div>
                <div class="dialogCenter">
                    <div class="label">
                        <div>材料信息上传：</div>
                    </div>
                    <div class="centerRight">
                        <div class="centerInfo" v-for="(item, i) in addPackage.bcDtls" @click.stop="getIndex(i)">
                            <div>
                                <el-upload class="avatar-uploader" action="/api/uploadnew/file/UploadCommonFileAsync"
                                    :show-file-list="false" :on-success="handleAvatarSuccess"
                                    accept=".jpg,.jpeg,.png,.gif,.bmp,.JPG,.JPEG,.GIF,.BMP,">
                                    <img v-if="item.materialImg" :src="item.materialImg" class="avatar">
                                    <i v-else class="el-icon-plus avatar-uploader-icon" />
                                </el-upload>
                            </div>
                            <div class="infoBox">
                                <el-input class="sleBox" @input="searchProps" placeholder="材料编码" v-model="item.materialCode"
                                    clearable style="width: 120px;margin-top: 10px;" maxlength="50"
                                    @focus="isShowInfo('focus')">
                                </el-input>
                                <div class="propsBox" v-show="isFouce && imgIndex == i" v-loading="propsBoxLoading">
                                    <div v-show="options.length > 0" v-for="propItem in options" class="propsBox_item"
                                        @click.stop="getIndex(i, propItem)">
                                        {{ propItem.materialCode }}
                                    </div>
                                    <div class="propsBox_item" @click="closeInfoBox" v-show="options.length == 0"> 暂无数据
                                    </div>
                                </div>
                                <el-input v-model="item.materialName" placeholder="材料名称"
                                    style="width: 120px;margin-top: 10px;" maxlength="30" clearable />
                                <el-input v-model="item.materialSize" placeholder="材料尺寸"
                                    style="width: 120px;margin-top: 10px;" maxlength="30" clearable />
                            </div>
                            <i class="el-icon-arrow-left" @click="changeLocation(i, 'left')" />
                            <i class="el-icon-arrow-right" @click="changeLocation(i, 'right')" />
                            <i class="el-icon-delete-solid" @click="changeLocation(i, 'del')" />
                        </div>
                    </div>
                </div>
                <div style="margin-top: 10px;display: flex;justify-content: end;">
                    <el-button @click="dialogVisible.materialDialog = false">取消</el-button>
                    <el-button type="primary" @click="materialSubmit">保存</el-button>
                </div>
            </div>

        </el-dialog>

        <el-dialog title="查看使用次数对应商品信息" :visible.sync="dialogVisible.countDialog" v-dialogDrag :before-close="handleColse"
            class="dialog">
            <vxetablebase :id="'packagingMaterials_index202408041558_2'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
                @sortchange='countSortchange' :tableData='dialogVisible.countTableData' :tableCols='countTableCols'
                :isSelection="false" :isSelectColumn="false" style="width: 100%; height: 700px; margin: 0" />
            <my-pagination ref="pager" :total="dialogVisible.countTotal" @page-change="countPagechange"
                @size-change="countSizechange" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from "dayjs";
import { pickerOptions } from '@/utils/tools'
import { replaceSpace } from '@/utils/getCols'
import _ from 'lodash'
import {
    getGoodsDocRecordCgBcPageList,
    saveGoodsDocRecordCgBc,
    getGoodsDocRecordCgBcById,
    getGoodsDocRecordCgByBcId,
    getGoodsDocRecordCgListByBcId,
    deleteGoodsDocRecordCgBcCheck,
    deleteGoodsDocRecordCgBc,
    getGoodListByName
} from '@/api/inventory/basicgoods'
const tableCols = [
    { istrue: true, label: '', type: "checkbox" },
    { istrue: true, prop: 'packName', label: '包装名称', sortable: 'custom' },
    { istrue: true, prop: 'materialNames', label: '使用材料', sortable: 'custom' },
    { istrue: true, prop: 'materialImgs', label: '材料图片', type: 'images' },
    { istrue: true, prop: 'createdUserName', label: '创建人', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '创建时间', sortable: 'custom' },
    { istrue: true, prop: 'modifiedUserName', label: '修改人', sortable: 'custom' },
    { istrue: true, prop: 'modifiedTime', label: '修改日期', sortable: 'custom' },
    { istrue: true, prop: 'useCount', label: '使用次数', type: 'click', handle: (that, row) => that.useCount(row, 'first') },
    {
        istrue: true, type: 'button', label: '更改名称', width: '90', btnList: [
            { label: "编辑", handle: (that, row) => that.editProps(row) },
            { label: "删除", handle: (that, row) => that.delProps(row, 'Single') },
        ]
    },
]

const countTableCols = [
    { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom' },
    { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom' },
    { istrue: true, prop: 'pictureUrl', label: '材料图片', type: 'images' },
]
export default {
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            that: this,
            pickerOptions,
            createTime: [],
            editTime: [],
            ListInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: false,//是否升序
                packName: null,//包装名称
                materialNames: null,//使用材料名称
                createdUserName: null,//创建人
                startCreatedTime: null,//创建开始时间
                endCreatedTime: null,//创建结束时间
                modifiedUserName: null,//修改人
                startModifiedTime: null,//修改开始时间
                endModifiedTime: null//修改结束时间
            },
            countInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: false,//是否升序
                bcId: null,
            },
            listLoading: true,
            tableProps: {
                tableData: [],
                total: 0,
            },
            dialogVisible: {
                materialDialog: false,
                countDialog: false,
                countTableData: [],
                countTotal: 0,
            },
            input: '',
            tableCols,
            countTableCols,
            imageUrl: '',
            addPackage: {
                packName: null,
                bcDtls: [
                    {
                        materialImg: null,
                        materialCode: null,
                        materialName: null,
                        materialSize: null
                    }
                ]
            },
            imgIndex: null,
            ids: [],
            getGoodListByNameInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: false,//是否升序
                goodsName: null,//商品名称
            },
            options: [],
            isFouce: false,
            props: [],
            propsBoxLoading: false,
            dialoading: false
        };
    },
    mounted() {
        this.getList();
    },
    //监听imgIndex
    watch: {
        imgIndex: function (newVal, oldVal) {
            if (newVal != oldVal) {
                this.isFouce = false
                this.options = []
            }
        }
    },
    methods: {
        getItem(i, item) {
            console.log(item, 'item');
            console.log(i, 'i');
        },
        isShowInfo(type) {
            if (type == 'blur') {
                this.isFouce = false
            } else {
                if (this.addPackage.bcDtls[this.imgIndex].materialCode) {
                    this.getGoodNameList(this.addPackage.bcDtls[this.imgIndex].materialCode)
                } else {
                    this.options = []
                }
                this.isFouce = true
            }
        },
        closeInfoBox() {
            this.isFouce = false
            this.options = []
        },
        searchProps: _.debounce(function (e) {
            if (e) {
                this.propsBoxLoading = true
                this.getGoodNameList(e)
                this.isFouce = true
            } else {
                this.options = []
                this.$nextTick(() => {
                    this.isFouce = false
                    console.log(this.imgIndex, 'this.imgIndex');
                    this.addPackage.bcDtls[this.imgIndex].materialCode = null
                    this.addPackage.bcDtls[this.imgIndex].materialName = null
                    this.addPackage.bcDtls[this.imgIndex].materialImg = null
                    this.addPackage.bcDtls[this.imgIndex].materialSize = null
                })
            }
        }, 500),
        async getGoodNameList(e) {
            const queryInfo = {
                currentPage: 1,//当前页
                pageSize: 20,//每页条数
                goodsCode: e,//商品名称
            }
            const { data, success } = await getGoodListByName(queryInfo)
            if (success) {
                this.$nextTick(() => {
                    this.options = data.map(item => {
                        return {
                            materialCode: item.goodsCode,
                            materialName: item.goodsName,
                            materialImg: item.picture
                        }
                    })
                    this.propsBoxLoading = false
                })
            }
        },
        changeTime(e, type) {
            if (type == 'create') {
                if (e) {
                    this.ListInfo.startCreatedTime = dayjs(e[0]).format('YYYY-MM-DD')
                    this.ListInfo.endCreatedTime = dayjs(e[1]).format('YYYY-MM-DD')
                } else {
                    this.ListInfo.startCreatedTime = null
                    this.ListInfo.endCreatedTime = null
                }
            }
            if (type == 'edit') {
                if (e) {
                    this.ListInfo.startModifiedTime = dayjs(e[0]).format('YYYY-MM-DD')
                    this.ListInfo.endModifiedTime = dayjs(e[1]).format('YYYY-MM-DD')
                } else {
                    this.ListInfo.startModifiedTime = null
                    this.ListInfo.endModifiedTime = null
                }
            }
        },
        changeLocation(i, type) {
            if (type == 'del') {
                this.addPackage.bcDtls.splice(i, 1)
                return
            }
            if (this.addPackage.bcDtls[i].materialImg == null) return this.$message.error('请先上传图片!')
            if (this.addPackage.bcDtls.length == 1) return this.$message.error('只有一条数据无法移动!')
            let changeProps;
            changeProps = this.addPackage.bcDtls[i]
            this.$nextTick(() => {
                if (type == 'left') {
                    if (i == 0) return this.$message.error('已经是第一条数据了!')
                    this.addPackage.bcDtls.splice(i, 1)
                    this.addPackage.bcDtls.splice(i - 1, 0, changeProps)
                } else {
                    if (i == this.addPackage.bcDtls.length - 1) return this.$message.error('已经是最后一条数据了!')
                    this.addPackage.bcDtls.splice(i, 1)
                    this.addPackage.bcDtls.splice(i + 1, 0, changeProps)
                }
            })
        },
        async editProps(row) {
            this.addPackage = row
            const { data } = await getGoodsDocRecordCgByBcId({ bcId: row.id })
            if (data == '此包装类型已有商品进行绑定') {
                this.$confirm('此包装类型已有商品进行绑定，请确认是否要进行修改若提交后对应商品信息的材料内容也将同步展示, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const { data, success } = await getGoodsDocRecordCgBcById({ id: row.id })
                    if (success) {
                        this.addPackage = data
                        this.dialogVisible.materialDialog = true
                    }
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消操作'
                    });
                });
            } else {
                const { data, success } = await getGoodsDocRecordCgBcById({ id: row.id })
                if (success) {
                    this.addPackage = data
                    this.dialogVisible.materialDialog = true
                }
            }
        },
        materialSubmit: _.debounce(async function () {
            if (!this.addPackage.packName) {
                this.$message.error('请输入包装名称')
                return
            }
            this.addPackage.bcDtls.forEach(item => {
                if (!item.materialImg) {
                    this.$message.error('请上传材料图片')
                    throw ('请上传材料图片')
                }
                if (!item.materialName) {
                    this.$message.error('请输入材料名称')
                    throw ('请输入材料名称')
                }
                if (!item.materialSize) {
                    this.$message.error('请输入材料尺寸')
                    throw ('请输入材料尺寸')
                }
            })
            this.dialogLoading = true
            const { success } = await saveGoodsDocRecordCgBc(this.addPackage)
            if (success) {
                this.$message.success('保存成功')
                this.getList()
                this.dialogVisible.materialDialog = false
                this.dialogLoading = false
            }
        }, 2000),
        async materialSubmit() {
            if (!this.addPackage.packName) {
                this.$message.error('请输入包装名称')
                return
            }
            this.addPackage.bcDtls.forEach(item => {
                if (!item.materialImg) {
                    this.$message.error('请上传材料图片')
                    throw ('请上传材料图片')
                }
                if (!item.materialName) {
                    this.$message.error('请输入材料名称')
                    throw ('请输入材料名称')
                }
                if (!item.materialSize) {
                    this.$message.error('请输入材料尺寸')
                    throw ('请输入材料尺寸')
                }
            })
            this.dialoading = true
            const { success } = await saveGoodsDocRecordCgBc(this.addPackage)
            if (success) {
                this.$message.success('保存成功')
                this.getList()
                this.dialogVisible.materialDialog = false
                this.dialoading = false
            }
        },
        checkboxRangeEnd(row) {
            this.ids = row.map(item => item.id)
        },
        getIndex(i, item1) {
            this.imgIndex = null
            this.imgIndex = i
            if (!item1) {
                console.log('点的是里面,我要item', item1);
            } else {
                console.log('点的是外面');
            }
            if (item1) {
                console.log('没进来');
                // console.log(item1, 'item1');
                // console.log(this.imgIndex, 'this.imgIndex');
                this.$nextTick(() => {
                    this.addPackage.bcDtls[this.imgIndex].materialCode = item1.materialCode
                    this.addPackage.bcDtls[this.imgIndex].materialName = item1.materialName
                    this.addPackage.bcDtls[this.imgIndex].materialImg = item1.materialImg
                    this.isFouce = false
                })
            }
        },
        handleAvatarSuccess({ data }) {
            this.addPackage.bcDtls[this.imgIndex].materialImg = data.url
        },
        async useCount(row, type) {
            if (type == 'first') {
                this.countInfo.currentPage = 1
                this.countInfo.pageSize = 50
                this.countInfo.bcId = row.id
                this.countInfo.isAsc = false
            } else {
                this.countInfo.bcId = row.bcId
            }
            const { data, success } = await getGoodsDocRecordCgListByBcId(this.countInfo)
            if (success) {
                this.dialogVisible.countTableData = data.list
                this.dialogVisible.countTotal = data.total
                this.dialogVisible.countDialog = true
            }
        },
        addProps(type) {
            if (type && type == 'addProps') {
                if (this.addPackage.bcDtls.length == 10) {
                    this.$message.error('最多添加10条材料信息')
                    return
                }
                this.addPackage.bcDtls.push({
                    materialImg: null,
                    materialCode: null,
                    materialName: null,
                    materialSize: null
                })
                return
            }
            this.addPackage = {
                packName: null,
                bcDtls: [
                    {
                        materialImg: null,
                        materialCode: null,
                        materialName: null,
                        materialSize: null
                    }
                ]
            }
            this.dialogVisible.materialDialog = true
        },
        async delProps(row, type) {
            if (type == 'multiple') {
                if (this.ids.length == 0) {
                    this.$message.error('请选择要删除的数据')
                    return
                }
            }
            let msg;
            if (type == 'multiple') {
                const { data } = await deleteGoodsDocRecordCgBcCheck(this.ids)
                msg = data
            } else {
                const { data } = await deleteGoodsDocRecordCgBcCheck([row.id])
                msg = data
            }
            if (msg == '此包装类型已有商品进行绑定') {
                this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    if (type == 'multiple') {
                        const { success } = await deleteGoodsDocRecordCgBc(this.ids)
                        if (success) {
                            this.getList();
                            this.ids = []
                            this.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                        }
                    } else {
                        const { success } = await deleteGoodsDocRecordCgBc([row.id])
                        if (success) {
                            this.getList();
                            this.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                        }
                    }
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            } else {
                this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    if (type == 'multiple') {
                        const { success } = await deleteGoodsDocRecordCgBc(this.ids)
                        if (success) {
                            this.getList();
                            this.ids = []
                            this.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                        }
                    } else {
                        const { success } = await deleteGoodsDocRecordCgBc([row.id])
                        if (success) {
                            this.getList();
                            this.$message({
                                type: 'success',
                                message: '删除成功!'
                            });

                        }
                    }
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            }
        },
        //页面数量改变
        sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList();
        },
        //当前页改变
        pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },
        countSizechange(val) {
            this.countInfo.currentPage = 1;
            this.countInfo.pageSize = val;
            this.useCount(this.countInfo);
        },
        //当前页改变
        countPagechange(val) {
            this.countInfo.currentPage = val;
            this.useCount(this.countInfo);
        },
        async getList(type) {
            this.listLoading = true
            if (type == 'click') {
                this.ListInfo.currentPage = 1
                this.ListInfo.pageSize = 50
                this.ListInfo.isAsc = false
            }
            const arr = ['packName', 'materialNames', 'createdUserName', 'modifiedUserName']
            this.ListInfo = replaceSpace(arr, this.ListInfo)
            const { data, success } = await getGoodsDocRecordCgBcPageList(this.ListInfo)
            if (success) {
                this.tableProps.tableData = data.list
                this.tableProps.total = data.total
                this.listLoading = false
            }
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        countSortchange({ order, prop }) {
            if (prop) {
                this.countInfo.orderBy = prop
                this.countInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.useCount(this.countInfo)
            }
        },
        handleColse() {
            this.dialogVisible.materialDialog = false
            this.dialogVisible.countDialog = false
        }
    }
};
</script>

<style lang="scss" scoped>
.header {
    display: flex;
    margin-bottom: 10px;
}

.publicCss {
    width: 220px;
    padding-right: 10px;
}

.dialogTopBox {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .label {
        width: 100px;
        text-align: right;
    }
}

.dialogCenter {
    display: flex;

    .label {
        width: 100px;
        text-align: right;
    }

    .centerRight {
        flex: 1;
        display: flex;
        flex-wrap: wrap;

        .centerInfo {
            width: 120px;
            margin: 0 10px;
            position: relative;
            margin-bottom: 10px;

            &:hover {
                .el-icon-arrow-left {
                    display: block;
                }

                .el-icon-arrow-right {
                    display: block;
                }

                .el-icon-delete-solid {
                    display: block;
                }
            }

            .el-icon-arrow-left {
                position: absolute;
                top: 21%;
                left: 0;
                font-size: 24px;
                color: #409EFF;
                cursor: pointer;
                display: none;
            }

            .el-icon-arrow-right {
                position: absolute;
                top: 21%;
                right: -2px;
                font-size: 24px;
                color: #409EFF;
                cursor: pointer;
                display: none;
            }

            .el-icon-delete-solid {
                color: #409EFF;
                position: absolute;
                bottom: 122px;
                right: 40%;
                font-size: 24px;
                cursor: pointer;
                display: none;
            }
        }

        .infoBox {
            display: flex;
            margin-top: 5px;
            flex-direction: column;
            position: relative;

            .propsBox {
                position: absolute;
                top: 30px;
                left: 0;
                z-index: 999;
                background: #fff;
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 10px;
                overflow: auto;
                box-sizing: border-box;
                width: 200px;
                height: 200px;

                .propsBox_item {
                    margin-bottom: 10px;

                    &:hover {
                        color: #409EFF;
                        cursor: pointer;
                    }
                }
            }
        }
    }
}

.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;


}

.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    text-align: center;
    border: 1px solid #ccc;
    line-height: 120px;
}

.avatar {
    width: 120px;
    height: 120px;
    display: block;
}

.sleBox {
    width: 300px;
    height: 30px;
    position: relative;
}

.el-icon-arrow-right {
    position: absolute;
    right: 20px;
    top: 13px;
}

.el-icon-arrow-down {
    position: absolute;
    right: 20px;
    top: 13px;
}

.propsBox {
    width: 300px;
    height: 200px;
    border: 1px solid #ccc;
    border-radius: 3px;
    padding: 10px;
    overflow: auto;
    box-sizing: border-box;
    margin-top: 20px;
}

.radioBox {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.radioItem {
    width: 100%;
    white-space: normal;
    word-break: break-all;
    margin-bottom: 10px;
}

.radioItem :hover {
    color: #409EFF;
}

// ::v-deep .dialog .
</style>