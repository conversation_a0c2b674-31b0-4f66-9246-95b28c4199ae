<template>
    <my-container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item>
                    <el-date-picker style="width: 250px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :clearable="true" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.styleCode" placeholder="请输入系列编码" class="publicCss" maxlength="200"
                        clearable />
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.platformsAscChoose" collapse-tags style="width: 60px">
                        <el-option label="正选" :value="true" />
                        <el-option label="反选" :value="false" />
                    </el-select>
                    <el-select v-model="filter.platforms" clearable multiple collapse-tags placeholder="已有平台"
                        style="width: 150px">
                        <el-option label="天猫" :value="1" />
                        <el-option label="淘宝" :value="9" />
                        <el-option label="淘工厂" :value="8" />
                        <el-option label="拼多多" :value="2" />
                        <el-option label="抖音" :value="6" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.salePlatformsAscChoose" collapse-tags style="width: 60px">
                        <el-option label="正选" :value="true" />
                        <el-option label="反选" :value="false" />
                    </el-select>
                    <el-select v-model="filter.salePlatforms" clearable multiple collapse-tags placeholder="售卖平台"
                        style="width: 150px">
                        <el-option label="天猫" :value="1" />
                        <el-option label="淘宝" :value="9" />
                        <el-option label="淘工厂" :value="8" />
                        <el-option label="拼多多" :value="2" />
                        <el-option label="抖音" :value="6" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.outStockStatus" clearable collapse-tags placeholder="编码是否缺货"
                        style="width: 110px">
                        <el-option label="部分缺货" :value="1" />
                        <el-option label="全部缺货" :value="2" />
                        <el-option label="不缺货" :value="3" />
                    </el-select>
                </el-form-item>

                </br>
                <el-form-item>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input-number v-model="filter.orderMin" :min="0" :precision="0" :max="999999999" :step="1"
                            placeholder="总订单量" style="width:130px;" />
                    </el-button>
                    至
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input-number v-model="filter.orderMax" :min="0"  :precision="0" :max="999999999" :step="1"
                            placeholder="总订单量" style="width:130px;" />
                    </el-button>
                </el-form-item>
                <el-form-item>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input-number v-model="filter.refundAmontBeforeRateMin" :precision="2" :min="0" :max="999999999" :step="1"
                            placeholder="发货前退款率" style="width:150px;" />
                    </el-button>
                    至
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input-number v-model="filter.refundAmontBeforeRateMax" :precision="2" :min="0" :max="999999999" :step="1"
                            placeholder="发货前退款率" style="width:150px;" />
                    </el-button>
                </el-form-item>

                <el-form-item>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input-number v-model="filter.refundAmontAfterRateMin" :precision="2" :min="0" :max="999999999" :step="1"
                            placeholder="发货后退款率" style="width:150px;" />
                    </el-button>
                    至
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input-number v-model="filter.refundAmontAfterRateMax" :precision="2" :min="0" :max="999999999" :step="1"
                            placeholder="发货后退款率" style="width:150px;" />
                    </el-button>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
            </el-form>
        </template>

        <el-dialog title="售卖情况" :visible.sync="saleDialog.visiable" width="20%" v-dialogDrag>
            <table style="width: 100%; " border="1">
                <tr style="height: 50px;">
                    <th style="width: 60%;">售卖小组数量：</th>
                    <th style="width: 40%;">{{ saleDialog.saleGroupCount }}</th>
                </tr>
                <tr style="height: 50px;">
                    <th style="width: 60%;">售卖链接数：</th>
                    <th style="width: 40%;">{{ saleDialog.saleProductCount }}</th>
                </tr>
            </table>
        </el-dialog>

        <el-dialog title="售卖情况" :visible.sync="orderCountDialog.visiable" width="40%" v-dialogDrag>
            <vxetablebase :id="'StyleCodeAllPlatformRpt_index202408041454'" ref="orderCountTable" :tableData="orderCountDialog.list"
                :tableCols="orderCountDialog.tableCols" :is-index="true" :that="that"
                style="width: 100%;  margin: 0; height: 300px;" @sortchange='orderCountSortchange' :height="'100%'"
                :showsummary="false" v-loading="orderCountListLoading">
            </vxetablebase>
        </el-dialog>

        <el-dialog title="缺货情况" :visible.sync="outStockDialog.visiable" width="50%" height="400px" v-dialogDrag>
            <vxetablebase :id="'StyleCodeAllPlatformRpt_index202408041454_2'" ref="outStockTable" :tableData="outStockDialog.list" :tableCols="outStockDialog.tableCols"
                :is-index="true" :that="that" style="width: 100%;  margin: 0; height: 400px;"
                @sortchange='outStockSortchange' :height="'100%'" :showsummary="false" v-loading="outStockListLoading">
            </vxetablebase>
        </el-dialog>
        <vxetablebase :id="'StyleCodeAllPlatformRpt_index202408041454_3'" ref="table" :tableData="list" :tableCols="tableCols" :is-index="true" :that="that" :border="true"
            style="width: 100%;  margin: 0" @sortchange='sortchange' :height="'100%'" :showsummary="true"
            showoverflow="false" :summaryarry="summaryarry" v-loading="listLoading">
            <template #platforms="{ row, index }">
                <div class="logoCss">
                    <el-image :src="tmLogo" style="height: 30px;width: 30px;" v-if="row.platforms.includes('1')" />
                    <el-image :src="tbLogo" style="height: 30px;width: 30px;" v-if="row.platforms.includes('9')" />
                    <el-image :src="tgcLogo" style="height: 30px;width: 30px;" v-if="row.platforms.includes('8')" />
                    <el-image :src="pddLogo" style="height: 30px;width: 30px;" v-if="row.platforms.includes('2')" />
                    <el-image :src="dyLogo" style="height: 30px;width: 30px;" v-if="row.platforms.includes('6')" />
                </div>
            </template>
            <template #saleInfoStr="{ row, index }">
                <div v-if="row.saleInfoList && row.saleInfoList.length > 0">
                    <div v-for="item in row.saleInfoList" class="strCss">
                        <div style="display: flex;margin-bottom: 5px;align-items: center;">
                            <el-image :src="tmLogo" style="height: 30px;width: 30px;margin-right: 5px;"
                                v-if="item.platform == 1" />
                            <el-image :src="tbLogo" style="height: 30px;width: 30px;margin-right: 5px;"
                                v-if="item.platform == 9" />
                            <el-image :src="tgcLogo" style="height: 30px;width: 30px;margin-right: 5px;"
                                v-if="item.platform == 8" />
                            <el-image :src="pddLogo" style="height: 30px;width: 30px;margin-right: 5px;"
                                v-if="item.platform == 2" />
                            <el-image :src="dyLogo" style="height: 30px;width: 30px;margin-right: 5px;"
                                v-if="item.platform == 6" />
                            <div> 售卖小组:{{ item.groupCount }}; 售卖链接:{{ item.proCodeCount }}</div>
                        </div>
                    </div>
                </div>
            </template>
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getlist" />
        </template>

    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { pageStyleCodeAllPlatformRptAsync, getStyleSaleGroupInfo, getStyleSalePlatformOrderCountInfo, getStyleOutOfStockData, exportStyleCodeAllPlatformRptAsync } from '@/api/bookkeeper/styleCodeRptData'

const tableCols = [
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', tipmesg: '', width: '150', },
    { istrue: true, align: 'center', prop: 'platforms', tipmesg: '', label: '已有平台', width: '300', sortable: 'custom', formatter: (row) => { return row.platformNames } },
    { istrue: true, align: 'center', prop: 'saleInfoStr', label: '售卖平台', width: '300', },
    {
        istrue: true, align: 'center', prop: 'orderCount', label: '总订单量', tipmesg: '', width: '120', sortable: 'custom'
        , type: 'colorClick', style: (that, row) => { return row.orderCount == 0 ? "color:gray;cursor:default;" : "color: #409EFF;cursor: pointer" }, handle: (that, row) => that.orderCountSearch(row)
    },
    { istrue: true, align: 'center', prop: 'zzRate', label: '周转天数', tipmesg: '', width: '120', sortable: 'custom', },
    {
        istrue: true, align: 'center', prop: 'outStockStatus', label: '商品编码是否缺货', tipmesg: '', width: '160', sortable: 'custom', formatter: (row) => { return row.outStockStatusName }
        , type: 'colorClick', style: (that, row) => { return row.outStockStatusName == '不缺货' ? "color:gray;cursor:default;" : "color: #409EFF;cursor: pointer" }, handle: (that, row) => that.outStockSearch(row)
    },
    //{ istrue: true, prop: 'null', label: '商品品质是否风险', tipmesg: '', },
    { istrue: true, align: 'center', prop: 'refundBeforeRate', label: '发货前退款率', tipmesg: '', sortable: 'custom', formatter: (row) => { return row.refundBeforeRate + "%" } },
    { istrue: true, align: 'center', prop: 'refundAfterRate', label: '发货后退款率', tipmesg: '', sortable: 'custom', formatter: (row) => { return row.refundAfterRate + "%" } },
]

const tableCols2 = [
    { istrue: true, prop: 'platform', label: '平台', tipmesg: '', sortable: 'custom', formatter: (row) => { return row.platformName } },
    { istrue: true, prop: 'orderCount', label: '数量', tipmesg: '', sortable: 'custom', },
    { istrue: true, prop: 'rate', label: '占比', tipmesg: '', sortable: 'custom', formatter: (row) => { return row.rate + "%" } },
]

const tableCols3 = [
    { istrue: true, prop: 'goodsImage', label: '图片', tipmesg: '', type: 'danimages', },
    { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom' },
    { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom' },
    { istrue: true, prop: 'isCheckError', label: '审单状态', formatter: (row) => { return "跟进" } },
    { istrue: true, prop: 'planArrivalTime', label: '预计到货日期', sortable: 'custom', formatter: (row) => { return row.planArrivalTime == null ? "" : formatTime(row.planArrivalTime, 'YYYY-MM-DD') } }
]

const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminStyleCodeAllPlatformRpt',
    components: { MyContainer, MyConfirmButton, vxetablebase },
    data() {
        return {
            pddLogo: require('@/assets/images/pddLogo.png'),
            dyLogo: require('@/assets/images/dyLogo.png'),
            tgcLogo: require('@/assets/images/tgcLogo.png'),
            tmLogo: require('@/assets/images/tmLogo.png'),
            tbLogo: require('@/assets/images/tbLogo.png'),
            that: this,
            filter: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
                styleCode: null,
                platforms: null,
                salePlatforms: null,
                outStockStatus: null,
                platformsAscChoose: true,
                salePlatformsAscChoose: true,
                // orderMin:null,
                // orderMax:null,
                // refundAmontBeforeRateMin:null,
                // refundAmontBeforeRateMax:null,
                // refundAmontAfterRateMin:null,
                // refundAmontAfterRateMax:null
            },
            expresscompanylist: [],
            list: [],
            summaryarry: {},
            tableCols: tableCols,
            total: 0,
            pager: { OrderBy: "", IsAsc: false },
            listLoading: false,
            orderCountListLoading: false,
            outStockListLoading: false,
            saleDialog: {
                visiable: false,
                saleGroupCount: 0,
                saleProductCount: 0,
            },
            orderCountDialog: {
                visiable: false,
                tableCols: tableCols2,
                filter: {
                    styleCode: null,
                    startTime: null,
                    endTime: null,
                },
                list: null,
                pager: { OrderBy: "", IsAsc: false },
            },
            outStockDialog: {
                visiable: false,
                tableCols: tableCols3,
                filter: {
                    styleCode: null
                },
                list: null,
                pager: { OrderBy: "", IsAsc: false },
            },
            pickerOptions: {
                shortcuts: [{
                    text: '近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            }
        };
    },

    async mounted() {
        await this.onSearch()
    },

    methods: {
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await pageStyleCodeAllPlatformRptAsync(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            this.list = data
        },
        async sortchange(column) {
            if (!column.order) {
                this.pager = {};
            }
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        async orderCountSortchange(column) {
            if (!column.order) {
                this.orderCountDialog.pager = {};
            }
            else {
                this.orderCountDialog.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.searchOrderCountList();
        },
        async outStockSortchange(column) {
            if (!column.order) {
                this.outStockDialog.pager = {};
            }
            else {
                this.outStockDialog.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.serachOutStockList();
        },
        async saleSearch(platform, isSale, styleCode) {
            if (!isSale) {
                return;
            }
            this.listLoading = true
            let obj = {
                filterPlatform: platform,
                styleCode: styleCode,
                startTime: this.filter.startTime,
                endTime: this.filter.endTime
            };
            let rlt = await getStyleSaleGroupInfo(obj);
            if (rlt.success) {
                this.saleDialog.saleGroupCount = rlt.data.groupCount;
                this.saleDialog.saleProductCount = rlt.data.proCodeCount;
                this.saleDialog.visiable = true;
            }
            this.listLoading = false
        },
        async orderCountSearch(row) {
            if (row.orderCount == 0) {
                return;
            }
            this.listLoading = true
            this.orderCountDialog.filter.styleCode = row.styleCode;
            this.orderCountDialog.filter.startTime = this.filter.startTime;
            this.orderCountDialog.filter.endTime = this.filter.endTime;
            await this.searchOrderCountList();
            this.listLoading = false
        },
        async searchOrderCountList() {
            this.orderCountListLoading = true;
            let req = { ...this.orderCountDialog.filter, ...this.orderCountDialog.pager };
            let rlt = await getStyleSalePlatformOrderCountInfo(req);
            if (rlt.success) {
                this.orderCountDialog.list = rlt.data;
                this.orderCountDialog.visiable = true;
            }
            this.orderCountListLoading = false;
        },
        async outStockSearch(row) {
            if (row.outStockStatusName == '不缺货') {
                return;
            }
            this.listLoading = true;
            this.outStockDialog.filter.styleCode = row.styleCode;
            await this.serachOutStockList();
            this.listLoading = false;
        },
        async serachOutStockList() {
            this.outStockListLoading = true;
            let req = { ...this.outStockDialog.filter, ...this.outStockDialog.pager };
            let rlt = await getStyleOutOfStockData(req);
            if (rlt.success) {
                this.outStockDialog.list = rlt.data;
                this.outStockDialog.visiable = true;
            }
            this.outStockListLoading = false;
        },
        async onExport() {
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...this.filter, ...this.pager }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await exportStyleCodeAllPlatformRptAsync(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '系列编码全平台数据_' + name + '_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        }
    }
};
</script>

<style lang="scss" scoped>
.logoCss {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    width: 100%;
}

.strCss {
    padding-left: 60px;
    display: flex;
    align-items: center;
    text-align: center;
}
</style>