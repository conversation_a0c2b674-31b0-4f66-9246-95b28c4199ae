<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="刷单处理" name="tab0" style="height: 100%;">
                <BrushOrderProcess  ref="brushOrderProcess" style="height: 100%;"></BrushOrderProcess>
            </el-tab-pane>
            <el-tab-pane label="订单处理" name="tab1" style="height: 100%;" lazy>
                <OrderProcess ref="orderProcess" style="height: 100%;"></OrderProcess>
            </el-tab-pane>
            <el-tab-pane label="客服聊天记录" name="second" style="height: 100%;" lazy>
                <keep-alive>
                    <ServiceChatHistory v-if="activeName === 'second'" ref="secondref" :partInfo="infos" :chatInfos="preInfos">
                    </ServiceChatHistory>
                </keep-alive>
            </el-tab-pane>
            <el-tab-pane label="售后数据统计" name="ninth" style="height: 100%;" lazy>
                <keep-alive>
                    <Salesninth v-if="activeName === 'ninth'" @showtabsecond="showtabsecond"></Salesninth>
                </keep-alive>
            </el-tab-pane>
            <el-tab-pane label="违规数据统计" name="tab4" style="height: 100%;" lazy>
                <ViolatStatistics  ref="brushOrderProcess" style="height: 100%;"></ViolatStatistics>
            </el-tab-pane>
            <el-tab-pane label="商品库存资料" name="tab5" style="height: 100%;" lazy v-if="checkPermission('CommodityInventoryDataPermissions')">
                <commodityInventoryInformation  ref="refcommodityInventoryInformation" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="库存资金周转" name="tab6" style="height: 100%;" lazy>
                <InventoryFundTurnover ref="inventoryFundTurnover" style="height: 100%;"></InventoryFundTurnover>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import BrushOrderProcess from '@/views/operatemanage/OperationalMiddleOffice/BrushOrderProcess';
import OrderProcess from '@/views/operatemanage/OperationalMiddleOffice/OrderProcess';
import ServiceChatHistory from "@/views/operatemanage/OperationalMiddleOffice/ServiceChatHistory";
import Salesninth from "@/views/operatemanage/OperationalMiddleOffice/Salesninth";
import ViolatStatistics from "@/views/operatemanage/OperationalMiddleOffice/ViolatStatistics";
import commodityInventoryInformation from "@/views/operatemanage/OperationalMiddleOffice/commodityInventoryInformation";
import InventoryFundTurnover from "@/views/operatemanage/OperationalMiddleOffice/InventoryFundTurnover";

export default {
    name: "OperationalMiddleOfficeManage",
    components: { MyContainer, BrushOrderProcess, OrderProcess, ServiceChatHistory, Salesninth, ViolatStatistics, commodityInventoryInformation, InventoryFundTurnover },
    data() {
        return {
            that: this,
            activeName: 'tab0',
            pageLoading: false,
            // filter: {
            // },
            infos: null,
            preInfos: null,
        };
    },
    async mounted() {
        // //根据权限来判断显示哪个tab
        // if (this.checkPermission('refundData')){
        //     this.activeName = 'tab0';
        // }
        // await this.onSearch();
    },
    methods: {
        async onSearch() {
            this.$nextTick(() => {
            })
        },
        showtabsecond(data) {
            this.infos = data;
            this.activeName = "second";//售后页面
        },
    },
}
</script>
