<template>
    <MyContainer>
        <template #header>
            <div class="header_top">
                <el-select v-model="ListInfo.platforms" placeholder="平台" clearable class="publicCss" multiple collapse-tags
                    @change="changePlatForm">
                    <el-option v-for="item in platFormList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.isUp" placeholder="是否上架" clearable class="publicCss">
                    <el-option label="是" :value="true">是</el-option>
                    <el-option label="否" :value="false">否</el-option>
                </el-select>
                <inputYunhan :inputt.sync="ListInfo.seriesCode" :maxRows="500" class="publicCss" style="margin: 0;"
                    v-model="ListInfo.seriesCode" placeholder="系列编码" :clearable="true" @callback="callbackGoodsCode"
                    title="系列编码"></inputYunhan>
                <el-date-picker v-model="createTimeRange" type="daterange"  unlink-panels range-separator="至"
                    @change="changeCreateTime" start-placeholder="创建开始日期" end-placeholder="创建结束日期"
                    :picker-options="pickerOptions" class="publicCss">
                </el-date-picker>
                <el-date-picker v-model="orderTimeRange" type="daterange"  unlink-panels range-separator="至"
                    @change="changeOrderTime" start-placeholder="订单/销售额开始日期" end-placeholder="订单/销售额结束日期"
                    :picker-options="pickerOptions" :clearable="false" class="publicCss">
                </el-date-picker>
                <el-button type="primary" @click="getList(true)">搜索</el-button>
            </div>
        </template>
        <vxetablebase :id="'companyStyleCode202408041704'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            v-loading="listLoading" style="width: 100%; margin: 0" height="100%"/>
            <template #footer>
                <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
            </template>
        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false" direction="btt">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" @change="chatSearch" :picker-options="pickerOptions" style="margin: 10px;" />
                <buschar :analysisData="chatProp.data" v-if="!chatProp.chatLoading"></buschar>
            </div>
            <div v-else v-loading="chatProp.chatLoading"></div>
        </el-drawer>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { replaceSpace } from '@/utils/getCols'
import buschar from '@/components/Bus/buschar'
import inputYunhan from "@/components/Comm/inputYunhan";
import middle from '@/store/middle.js'
import dayjs from 'dayjs'
import {
    pageGetSeriesData,
    getSeriesDataAnalysis
} from '@/api/inventory/companyGoods'

const tableCols = [
    { istrue: true, label: '系列编码', prop: 'styleCode', sortable: 'custom', fixed: 'left', width: '100', },
    // { istrue: true, label: '类目', prop: 'category', sortable: 'custom', width: '100', },
    { istrue: true, label: '创建时间', prop: 'firstCreated', sortable: 'custom', width: '150', },
    { istrue: true, label: '商品编码', prop: 'goodsCount', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.linkToGoodCode(row) },
    { istrue: true, label: '可售库存', prop: 'sellStock', sortable: 'custom', width: '100', },
    { istrue: true, label: '拼多多', prop: 'pinDD', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.linkToGoodCode(row) },
    { istrue: true, label: '订单量', prop: 'pddOrderCount', sortable: 'custom', width: '100', },
    { istrue: true, label: '销售金额', prop: 'pddSaleAmount', sortable: 'custom', width: '100', },
    { istrue: true, label: '淘系', prop: 'tiaoXi', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.linkToGoodCode(row) },
    { istrue: true, label: '订单量', prop: 'txOrderCount', sortable: 'custom', width: '100', },
    { istrue: true, label: '销售金额', prop: 'txSaleAmount', sortable: 'custom', width: '100', },
    { istrue: true, label: '抖音', prop: 'dy', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.linkToGoodCode(row) },
    { istrue: true, label: '订单量', prop: 'dyOrderCount', sortable: 'custom', width: '100', },
    { istrue: true, label: '销售金额', prop: 'dySaleAmount', sortable: 'custom', width: '100', },
    { istrue: true, label: '分销', prop: 'fenXiao', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.linkToGoodCode(row) },
    { istrue: true, label: '订单量', prop: 'fxOrderCount', sortable: 'custom', width: '100', },
    { istrue: true, label: '销售金额', prop: 'fxSaleAmount', sortable: 'custom', width: '100', },
    { istrue: true, label: '京东', prop: 'jingDong', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.linkToGoodCode(row) },
    { istrue: true, label: '订单量', prop: 'jdOrderCount', sortable: 'custom', width: '100', },
    { istrue: true, label: '销售金额', prop: 'jdSaleAmount', sortable: 'custom', width: '100', },
    { istrue: true, label: '阿里', prop: 'al', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.linkToGoodCode(row) },
    { istrue: true, label: '订单量', prop: 'alOrderCount', sortable: 'custom', width: '100', },
    { istrue: true, label: '销售金额', prop: 'alSaleAmount', sortable: 'custom', width: '100', },
    {
        istrue: true, type: 'button', label: '', fixed: 'right', width: '100', btnList:
            [
                { label: "趋势图", handle: (that, row) => that.openChat(row) },
            ]
    },
]
const platFormList = [
    { label: '拼多多', value: 'pdd' },
    { label: '淘系', value: 'tx' },
    { label: '抖音', value: 'dy' },
    { label: '分销', value: 'fx' },
    { label: '京东', value: 'jd' },
    { label: '阿里', value: 'al' },
]
export default {
    name: "companyStyleCode",
    components: {
        MyContainer, vxetablebase, buschar, inputYunhan
    },
    data() {
        return {
            tableCols,
            that: this,
            pickerOptions,
            total: 0,
            ListInfo: {
                currentPage: 1,
                pagesize: 50,
                orderBy: null,
                isAsc: true,
                seriesCode: null,//系列编码
                platforms: [],//平台
                createStart: null,//创建开始日期
                createEnd: null,//创建结束日期
                orderDateStart: null,//订单/销售额开始日期
                orderDateEnd: null,//订单/销售额结束日期
                isUp: null,//是否上架
            },
            chatProp: {
                chatDialog: false,//趋势图弹窗
                chatTime: null,//趋势图时间
                chatLoading: true,//趋势图loading
                data: [],//趋势图数据
            },
            chatInfo: {
                createStart: null,//创建开始日期
                createEnd: null,//创建结束日期
                seriesCode: null,//系列编码
            },
            createTimeRange: [],
            orderTimeRange: [],
            tableData: [],
            platFormList,
            listLoading: true
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        changePlatForm(e) {
            if (e.length == 0) {
                this.ListInfo.platforms = []
                this.ListInfo.isUp = null
            }
        },
        linkToGoodCode(row) {
            this.$emit('toGoodCode')
            middle.$emit('linkToGoodCode', row.styleCode)
        },
        async callbackGoodsCode(val) {
            this.ListInfo.seriesCode = val;
        },
        async chatSearch() {
            this.chatInfo.createStart = dayjs(this.chatProp.chatTime[0]).format('YYYY-MM-DD')
            this.chatInfo.createEnd = dayjs(this.chatProp.chatTime[1]).format('YYYY-MM-DD')
            this.chatProp.chatLoading = true
            const { data } = await getSeriesDataAnalysis(this.chatInfo)
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        async openChat(row) {
            this.chatInfo.seriesCode = row.styleCode
            //趋势图默认时间是当前日期往前推一个月
            this.chatProp.chatTime = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
            this.chatInfo.createStart = this.chatProp.chatTime[0]
            this.chatInfo.createEnd = this.chatProp.chatTime[1]
            this.chatProp.chatLoading = true
            const { data, success } = await getSeriesDataAnalysis(this.chatInfo)
            if (!success) return
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        changeCreateTime(e) {
            console.log(e, 'e');
            if (e) {
                this.ListInfo.createStart = dayjs(e[0]).format('YYYY-MM-DD')
                this.ListInfo.createEnd = dayjs(e[1]).format('YYYY-MM-DD')
                this.createTimeRange = e
            } else {
                this.ListInfo.createStart = null
                this.ListInfo.createEnd = null
                this.createTimeRange = []
            }
        },
        changeOrderTime(e) {
            if (e.length > 0) {
                this.ListInfo.orderDateStart = dayjs(e[0]).format('YYYY-MM-DD')
                this.ListInfo.orderDateEnd = dayjs(e[1]).format('YYYY-MM-DD')
                this.orderTimeRange = e
            } else {
                this.ListInfo.orderDateStart = null
                this.ListInfo.orderDateEnd = null
                this.orderTimeRange = []
            }
        },
        //页面数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pagesize = val;
            this.getList();
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },

        async getList(isSearch) {
            console.log(this.ListInfo, 'this.ListInfo');
            if (isSearch) {
                this.ListInfo.currentPage = 1
                this.ListInfo.pagesize = 50
            }
            this.listLoading = true
            // 如果没有订单/销售额开始日期和订单/销售额结束日期,就从当前日期往前推一天
            if (!this.ListInfo.orderDateStart && !this.ListInfo.orderDateEnd) {
                this.ListInfo.orderDateStart = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
                this.ListInfo.orderDateEnd = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
                this.orderTimeRange = [dayjs().subtract(1, 'day').format('YYYY-MM-DD'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')]
            }
            const replaceArr = ['seriesCode', 'styleCode']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            const { data, success } = await pageGetSeriesData(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.listLoading = false
            }
        },
        //页面数量改变
        detailSizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pagesize = val;
            this.getList();
        },
        //当前页改变
        detailPagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.header_top {
    display: flex;
    margin-bottom: 10px;
}

.publicCss {
    width: 220px;
    margin-right: 10px;
}
</style>