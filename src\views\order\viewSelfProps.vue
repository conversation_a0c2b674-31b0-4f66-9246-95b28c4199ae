<template>
    <MyContainer v-loading="pageLoading">
        <el-container style="height: 800px; border: 1px solid #eee;padding: 10px;">
            <el-aside width="200px">
                <div class="titleBox" v-if="dbList.length > 0">
                    <div v-for="(item, i) in dbList" :class="['title_item', index == i ? 'active' : '']">
                        <el-tooltip effect="dark" :content="item.remark" placement="top-start">
                            <div @click="chooseTitle(item, i)" class="title_item_word"
                                :style="{ textAlign: 'left', paddingLeft: '5px', paddingRight: '5px' }">{{
                                    item.title
                                }}</div>
                        </el-tooltip>
                    </div>
                </div>
            </el-aside>
            <el-container>
                <div class="right">
                    <el-form :inline="true" class="top" :label-position="'left'" label-width="80px">
                        <div v-for="(item, i) in ListInfo.conditions" :key="item.id">
                            <el-form-item :label="item.label + ':'" class="publicCss">
                                <el-date-picker v-if="item.conditionType == 1" v-model="item.timeRange" type="daterange"
                                    unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                    :picker-options="pickerOptions" @change="changeDate($event, i)" style="width:200px" />
                                <el-time-select v-if="item.conditionType == 2" v-model="item.timeRange" :picker-options="{
                                    start: '00:00',
                                    step: '00:30',
                                    end: '23:30'
                                }" :placeholder="`选择时间`" style="width:200px" @change="changeTime($event, i)" />
                                <el-select v-if="item.conditionType == 3" v-model="item.value" :placeholder="item.label"
                                    style="width:200px" clearable>
                                    <el-option v-for="opt in item.conditionDS" :key="opt.value" :label="opt.label"
                                        :value="opt.value" />
                                </el-select>
                                <el-select v-model="item.value" v-if="item.conditionType == 4" :placeholder="item.label"
                                    style="width:200px" clearable multiple collapse-tags @change="changeOpts($event, i)">
                                    <el-option v-for="opts in item.conditionDS" :key="opts.value" :label="opts.label"
                                        :value="opts.value" />
                                </el-select>
                                <el-input style="width:200px" v-if="item.conditionType == 5" v-model.trim="item.value"
                                    :placeholder="item.label" clearable maxlength="200" />
                                <el-input style="width:200px" v-if="item.conditionType == 6" v-model.trim="item.value"
                                    :placeholder="item.label" clearable maxlength="200" />
                                <div v-if="item.conditionType == 7" style="display: flex;">
                                    <el-tooltip effect="dark" content="数字范围必须填写两个值,第一个是最小值,第二个是最大值!" placement="top-start">
                                        <el-input-number v-model="num1" :precision="0" :max="999999" :controls="false"
                                            style="width:200px" />
                                    </el-tooltip>
                                    <el-tooltip effect="dark" content="数字范围必须填写两个值,第一个是最小值,第二个是最大值!" placement="top-start">
                                        <el-input-number v-model="num2" :precision="0" :max="999999" :controls="false"
                                            style="width:200px" />
                                    </el-tooltip>
                                </div>
                                <el-select v-model="item.value" v-if="item.conditionType == 8" :placeholder="item.label"
                                    style="width:200px" clearable multiple collapse-tags>
                                    <el-option key="是" label="是" :value="true" />
                                    <el-option key="否" label="否" :value="false" />
                                </el-select>
                                <el-input v-if="item.conditionType == 9" type="textarea" :rows="2" :placeholder="item.label"
                                    v-model="item.value" align="right" style="width:200px">
                                </el-input>
                                <el-date-picker v-if="item.conditionType == 3 && item.dataType == 3" v-model="item.value"
                                    type="date" placeholder="选择日期" :picker-options="datePickerOptions"
                                    :value-format="'yyyy-MM-dd'" style="width:200px">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <div>
                            <el-button type="primary" @click="getTableList(true)">查询</el-button>
                            <el-button type="primary" @click="exportData" v-if="ListInfo.canExport">导出</el-button>
                        </div>
                    </el-form>
                    <div class="bottom">
                        <vxetablebase :id="'viewSelfProps202408041740'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
                            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
                            :isSelectColumn="false" style="width:auto!important; height: 100%; margin: 0"
                            v-loading="loading" />
                        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
                    </div>
                </div>
            </el-container>
        </el-container>
    </MyContainer>
</template>
<script>
import MyContainer from "@/components/my-container";
import { getUserInfo } from '@/api/operatemanage/productalllink/alllink'
import { pageGetReports, getReporterData, query, exportData } from '@/api/bookkeeper/reporter'
import { pickerOptions } from '@/utils/tools'
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
const operatorTypeList = [
    { conditionType: '1', value: 14 },//日期范围
    { conditionType: '2', value: 15 },//时间范围
    { conditionType: '3', value: 6 },//单选
    { conditionType: '4', value: 16 },//多选
    { conditionType: '5', value: 6 },//精确查询
    { conditionType: '6', value: 0 },//模糊查询
    { conditionType: '7', value: 14 },//数字范围
    { conditionType: '8', value: 6 },//是否
    { conditionType: '9', value: 16 },//多行或选
]
export default {
    name: "viewSelfProps",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            ListInfo: {
                currentPage: 1,
                pageSize: 10000,
                orderBy: null,
                isAsc: false,
                dbName: null,//数据库名称
                sql: null,//sql语句
                canExport: null,//是否支持导出
                canQuery: null,//是否支持查询条件
                canSummary: null,//是否支持汇总
                conditions: [],
                id: null//报表id
            },
            tableData: [],
            dbList: [],
            index: 0,
            operatorTypeList,
            pickerOptions,
            tableCols: [],
            total: 0,
            that: this,
            loading: false,
            pageLoading: false,
            datePickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                },
                shortcuts: [{
                    text: '今天',
                    onClick(picker) {
                        picker.$emit('pick', new Date());
                    }
                }, {
                    text: '昨天',
                    onClick(picker) {
                        const date = new Date();
                        date.setTime(date.getTime() - 3600 * 1000 * 24);
                        picker.$emit('pick', date);
                    }
                }, {
                    text: '一周前',
                    onClick(picker) {
                        const date = new Date();
                        date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', date);
                    }
                }]
            },
        };
    },
    async mounted() {
        await this.getInfo()
        await this.getTitleList()
        await this.getSetData(this.dbList[0].id)
    },
    methods: {
        changeOpts(e, i) {
            if (e) {
                this.ListInfo.conditions[i].value = e.join(',')
            } else {
                this.ListInfo.conditions[i].value = null
            }
        },
        async exportData() {
            // if (this.tableData.length == 0) return this.$message.error('暂无数据可导出')
            const title = this.dbList[this.index].title
            const { data } = await exportData(this.ListInfo)
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', `${title}` + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        changeTime(e, i) {
            console.log(e, 'e');
            if (e) {
                let time = [e, e]
                console.log(time, 'time');
                this.ListInfo.conditions[i].value = time.join(',')
            } else {
                this.ListInfo.conditions[i].value = null
            }
        },
        changeDate(e, i) {
            if (e) {
                let time = e.map(item => dayjs(item).format('YYYY-MM-DD'))
                this.ListInfo.conditions[i].value = time.join(',')
            } else {
                this.ListInfo.conditions[i].value = null
            }
        },
        async chooseTitle(item, i) {
            this.tableData = []
            this.tableCols = []
            this.index = i
            this.loading = false
            this.ListInfo.orderBy = null
            this.ListInfo.id = item.id
            await this.getSetData(item.id)
        },
        async getSetData(id) {
            this.pageLoading = true
            const { data, success } = await getReporterData(id)
            if (success) {
                this.ListInfo.dbName = data.dbName
                this.ListInfo.sql = data.sql
                this.ListInfo.canQuery = data.canQuery
                this.ListInfo.canSummary = data.canSummary
                this.ListInfo.canExport = data.canExport
                this.ListInfo.conditions = data?.columns.map(item => {
                    return {
                        conditionDS: item.conditionDS ? JSON.parse(item.conditionDS) : null,
                        conditionType: item.conditionType,
                        field: item.name,
                        name: item.name,
                        inCondition: item.inCondition,
                        operator: this.operatorTypeList.find(i => i.conditionType == item.conditionType).value,
                        value: null,
                        isRequired: item.conditionRequired,
                        dataType: item.dataType,
                        sortColumn: item.sortColumn,
                        inSummary: item.inSummary,
                        inList: item.inList,
                        label: item.label,
                        timeRange: (item.conditionType == 1 || item.conditionType == 2) ? [] : null,
                    }
                })
                this.pageLoading = false
            }
        },
        async getTitleList() {
            const { data, success } = await pageGetReports(this.ListInfo)
            if (success) {
                this.dbList = data.list
                console.log(this.dbList, 'this.dbList');
                this.ListInfo.id = data.list[0].id
            }
        },
        async getInfo() {
            const { data, success } = await getUserInfo()
            if (success) {
                this.ListInfo.userId = data.id
            }
        },
        async getTableList(isSearch) {
            if (isSearch) {
                this.ListInfo.currentPage = 1
                this.ListInfo.pageSize = 50
            }
            this.loading = true
            this.ListInfo.conditions.forEach(item => {
                if (item.conditionType == 7) {
                    if (item.num1 < 0 || item.num2 < 0) {
                        this.$message.error('数字范围必须大于0')
                        throw new Error('数字范围必须大于0')
                    }
                    if (!item.num1 || !item.num2) {
                        this.$message.error('数字范围必须填写两个值')
                        throw new Error('数字范围必须填写两个值')
                    }
                    item.value = [item.num1, item.num2].join(',')
                }
            })
            const { data, success } = await query(this.ListInfo)
            if (success) {
                if (data.list.length > 0) {
                    const keys = Object.keys(data.list[0])
                    this.$nextTick(() => {
                        this.tableCols = keys.map(item => {
                            return { istrue: true, prop: item, label: item, sortable: 'custom', width: 'auto', minwidth: 140 }
                        })
                    })
                }
                this.tableData = data?.list
                this.total = data?.total
              }
              this.loading = false
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getTableList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getTableList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop.charAt(0).toUpperCase() + prop.slice(1)
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getTableList()
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.titleBox {
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    height: 100%;
    overflow: auto;
    border: 1px solid #eee;

    .title_item {
        width: 100%;
        text-align: center;
        height: 30px;
        line-height: 30px;
        font-size: 14px;

        .title_item_word {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        &:hover {
            cursor: pointer;
            background-color: rgb(211, 227, 253);
        }
    }

    .active {
        background-color: rgb(211, 227, 253);
    }
}

.right {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 0 10px;
    box-sizing: border-box;

    .top {
        display: flex;
        // flex: 1;
        padding: 10px;
        align-items: start;
        padding: 0 10px;
        box-sizing: border-box;
        //可以换行
        flex-wrap: wrap;
        max-height: 110px;
        // height: 110px;
        overflow: auto;

        .publicCss {
            display: flex;
            margin: 0 10px 10px 0;
        }
    }

    .bottom {
        height: 700px;
    }
}
</style>
