<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
//require('echarts/theme/macarons') // echarts theme
//import resize from './mixins/resize'

export default {
  //mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    pieData: {}
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    pieData: {
      deep: true,
      handler(val) {   
        this.setOptions(val)
      }
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initChart()
    window.addEventListener('resize', this.handleResizeChart);
    })
  },
  destroyed () {
    window.removeEventListener('resize', this.handleResizeChart);
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')

      
    },
    // setOptions(f){
    //     var series = []
    //     var data = []
        
    //     f.pieSeries.forEach(s=>{
    //         data.push({value: s.value, name: s.name})
    //     })
         
    //       series.push({
    //         name: f.title,
    //         type: 'pie',
    //         //roseType: 'radius',
    //         radius: ['30%', '50%'],
    //         center: ['45%', '45%'],
    //         itemStyle: {
    //           borderRadius: 10,
    //           borderColor: '#fff',
    //           borderWidth: 2
    //         },
    //         emphasis: {
    //               itemStyle: {
    //                 shadowBlur: 10,
    //                 shadowOffsetX: 0,
    //                 shadowColor: 'rgba(0, 0, 0, 0.5)'
    //               },
    //                     },
    //                     labelLine: {
    //                         show: true,
    //                     },
    //         data: data,
    //         animationEasing: 'cubicInOut',
    //         //animationDuration: 2600
          
    //     })   
        
    //     this.chart.setOption({
    //     tooltip: {
    //       trigger: 'item',
    //       formatter: '{a} <br/>{b} : {c} ({d}%)'
    //       },
    //     grid: {
    //       top: '100px',
    //     },
    //     legend: {
    //       left: 'center',
    //       bottom: '10',
    //       data: f.legend
    //     },
    //     series: series
    //   })
    // },
    setOptions(f) {
    var series = [];
    var data = [];

    f.pieSeries.forEach(s => {
        data.push({value: s.value, name: s.name});
    });

    series.push({
        name: f.title,
        type: 'pie',
        radius: ['30%', '50%'],
        center: ['45%', '45%'],
        itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
        },
        emphasis: {
            itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
            },
        },
        labelLine: {
            show: true,
        },
        data: data,
        animationEasing: 'cubicInOut',
    });

    // 自定义格式化函数
    function formatValue(value) {
        return value.toLocaleString(); // 使用千位符
    }

    this.chart.setOption({
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                var formattedValue = formatValue(params.value);
                return `${params.name} : ${formattedValue} (${params.percent}%)`;
            }
        },
        grid: {
            top: '100px',
        },
        legend: {
            left: 'center',
            bottom: '10',
            data: f.legend
        },
        series: series
    });
},
    handleResizeChart () {
      if (this.chart) {
        this.chart.resize();
      }
    }
  }
}
</script>
