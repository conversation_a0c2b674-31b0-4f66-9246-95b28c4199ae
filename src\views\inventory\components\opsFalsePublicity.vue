<template>
    <container v-loading="pageLoading" style="height: 100%;">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        :clearable="false" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始"
                        end-placeholder="结束">
                    </el-date-picker>
                </el-button>

                <el-button style="padding: 0;margin-left: 0;">
                    <inputYunhan :title="inputtitle" :row="inputrow" :placeholder="a" :maxRows="300"
                        :inputshow="inputshow" :clearable="true" @callback="callback" :inputt.sync="filter.goodsId"
                        filter.goodsIds></inputYunhan>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-input v-model.trim="filter.shopName" type="text" maxlength="100" clearable
                        placeholder="请输入店鋪名称..." style="width:180px;">
                    </el-input>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-input v-model.trim="filter.groupName" type="text" maxlength="100" clearable
                        placeholder="请输入小組名称..." style="width:180px;">
                    </el-input>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button>
            </el-button-group>
        </template>
        <vxetablebase :id="'opsFalsePublicity20241021'" :tableData='list' :tableCols='tableCols'
            style="height: 90%;" :tableHandles='tableHandles' :loading='listLoading' :border='true' :that="that" :isIndex='true' :hasexpand='true' @select='selectchange'
            :isSelection='true' ref="vxetable" @sortchange='sortchange' />
        <span style="text-align: right;">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :page-size="10"
                layout="total, prev, pager, next, jumper" :total="total" style="margin-top: 26px;">
            </el-pagination>
        </span>
        
        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag>
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange">
                    <template #trigger>
                        <el-button size="small" type="primary">选取数据文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px;" size="small" type="success" :loading="uploadLoading"
                        @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>

<script>
import dayjs from "dayjs";
import container from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { formatTime, formatNoLink } from "@/utils/tools";
import {opsFalsePublicityPage, opsFalsePublicityImport, opsFalsePublicityRemove} from "@/api/bladegateway/worcestorejk.js"
import inputYunhan from "@/components/Comm/inputYunhan";

const tableCols = [
    { istrue: true, align: 'center', type:'checkbox', width: 'auto', sortable: 'custom',},
    { istrue: true, align: 'center', prop: 'importDate', label: '导入日期', width: 'auto', sortable: 'custom', formatter: (row) => { return row.importDate ? formatTime(row.importDate, 'YYYY-MM-DD') : '' } },
    { istrue: true, align: 'center', prop: 'questionType', label: '问题类型', width: 'auto', sortable: 'custom',},
    { istrue: true, align: 'center', prop: 'goodsName', label: '商品名称', width: 'auto', sortable: 'custom',},
    { istrue: true, align: 'center', prop: 'goodsId', label: '商品ID', width: 'auto', sortable: 'custom',},
    { istrue: true, align: 'center', prop: 'shopName', label: '店铺', width: 'auto', sortable: 'custom',},
    { istrue: true, align: 'center', prop: 'inspectionParty', label: '稽查方', width: 'auto', sortable: 'custom',},
    { istrue: true, align: 'center', prop: 'groupName', label: '小组', width: 'auto', sortable: 'custom',},
    { istrue: true, align: 'center', prop: 'shopSize', label: '被抓时间店铺大小', width: 'auto', sortable: 'custom',},
    { istrue: true, align: 'center', prop: 'remark', label: '备注', width: 'auto', sortable: 'custom',},
    { istrue: true, align: 'center', prop: 'restrictedWithdrawalDesc', label: '是否限制提现', width: 'auto', sortable: 'custom',},
    { istrue: true, align: 'center', prop: 'questionDesc', label: '问题描述', width: 'auto', sortable: 'custom',},
];

const tableHandles = [
    { label: "模板下载", permission: 'api:inventory:opsFalsePublicityImport', handle: (that) => that.downloadImportTemplate() },
    { label: "导入", permission: 'api:inventory:opsFalsePublicityImport', handle: (that) => that.startImport() },
    { label: '批量删除', permission: 'api:inventory:opsFalsePublicityRemove', handle: (that) => that.batchDelete() },
];

const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");


export default {
    name: 'YunHanAdminGoodschange',
    components: { MyConfirmButton, container, vxetablebase, inputYunhan },

    data() {
        return {
            that: this,
            filter: {
                currentPage: 1,
                pageSize: 10,
                goodsName: null,
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
            },
            list: [],
            pager: {},
            total: 0,
            inputtitle: '请分行输入ID',
            inputrow: 12,
            a: '商品ID',
            inputshow: 0,
            tableCols: tableCols,
            tableHandles: tableHandles,
            listLoading: false,
            pageLoading: false,
            dialogVisible: false,
            uploadLoading: false,
            selids: [],
            fileList: [],
            onFinishLoading: false
        };
    },

    async mounted() {
        await this.onSearch();
    },

    methods: {
        async onSearch() {
            this.filter.currentPage = 1;
            this.getlist();
        },
        //批量数据组合，回调数据
        async callback(val) {
            this.filter.goodsIds = val
            this.filter.currentPage = 1;
            this.getlist()
        },
        async getlist() {
            this.pageLoading = true
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ... this.filter, ... this.pager }
            var res = await opsFalsePublicityPage(params);
            this.pageLoading = false
            if (!res?.success) {
                return
            }
            const data = res.data.list;
            this.list = data;
            this.total = res.data.total;
            this.selids = []
        },
        async batchDelete() {
            this.addFormVisible = true
            if (this.selids.length == 0) {
                this.$message({ message: "请先选择", type: "warning", });
                this.radio = ''
                this.addFormVisible = false
                return
            }
            this.$confirm(`确认删除${this.selids.length}行数据, 是否继续?`, '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                .then(async () => {
                    console.log('this.selids.join()', this.selids.join(','))
                    const res = await opsFalsePublicityRemove({ ids: this.selids.join(',') })
                    if (!res?.success) { return }
                    this.$message({ type: 'success', message: '操作成功!' });
                    await this.onSearch()
                }).catch(() => {
                    this.$message({ type: 'info', message: '已取消操作' });
                });
            this.addFormVisible = false;
            return;
        },
        async handleSizeChange(val) {
            this.filter.pageSize = val;
            await this.getlist();
            console.log(`每页 ${val} 条`);
        },
        downloadImportTemplate() {
            window.open("/static/excel/inventory/多多虚假宣传-模板.xlsx", "_blank");
        },
        startImport() {
            this.dialogVisible = true;
        },
        cancelImport() {
            this.dialogVisible = false;
        },
        beforeRemove() {
            return false;
        },
        //上传成功
        uploadSuccess(response, file, fileList) {
            if (response.code == 200) {
            } else {
                fileList.splice(fileList.indexOf(file), 1);
            }
        },
        submitUpload() {
            debugger
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请先选取文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        async uploadFile(item) {
            debugger
            const form = new FormData();
            form.append("token", this.token);
            form.append("file", item.file);
            this.uploadLoading = true;
            const res = await opsFalsePublicityImport(form);
            this.uploadLoading = false;
            if (res?.success) {
                this.$message({ message: '上传成功,正在导入中...', type: "success", });
            }
            else {
                this.$message({ message: res.message, type: "warning", });
                return false;
            }
            this.fileList = [];
            this.$refs.upload.clearFiles();
            this.dialogVisible = false;
            this.onSearch();
        },
        uploadChange(file, fileList) {
            if (fileList && fileList.length > 0) {
                var list = [];
                for (var i = 0; i < fileList.length; i++) {
                    if (fileList[i].status == "success")
                        list.push(fileList[i]);
                    else
                        list.push(fileList[i].raw);
                }
                this.fileList = list;
            }
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async handleCurrentChange(val) {
            this.filter.currentPage = val;
            await this.getlist();
            console.log(`当前页: ${val}`);
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep .el-main{
    overflow-y: hidden !important;
}
</style>