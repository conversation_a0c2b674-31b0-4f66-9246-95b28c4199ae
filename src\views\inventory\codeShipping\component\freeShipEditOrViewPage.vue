<template>
    <MyContainer>
        <el-form :model="ruleform" status-icon :rules="rules" ref="ruleform" label-width="100px" class="demo-ruleform"
            :disabled="isView">
            <el-form-item label="配送方式:" prop="deliveryMethod">
                <el-select v-model="ruleform.deliveryMethod" placeholder="配送方式" class="publicCss" clearable>
                    <el-option label="物流自提" :value="1" />
                    <el-option label="送货上门" :value="2" />
                </el-select>
            </el-form-item>
            <el-form-item label="报价凭证:" prop="quotationVoucher">
                <div class="chatPicUrl">
                    <uploadimgFile ref="uploadimgFile" :disabled="isView" :ispaste="!isView" :noDel="isView"
                        :accepttyes="accepttyes" :isImage="true" :uploadInfo="chatUrls" :keys="[1, 1]"
                        v-if="uploadimgFileVisable" @callback="getImg" :imgmaxsize="9" :limit="9" :multiple="true">
                    </uploadimgFile>
                    <span class="picTips" v-if="!isView">提示:点击灰框可直接贴图,最多可上传9张！！！</span>
                </div>
            </el-form-item>
            <el-form-item label="备注:">
                <el-input v-model="ruleform.remark" type="textarea" placeholder="备注" maxlength="500" clearable
                    style="width: 500px;" show-word-limit/>
            </el-form-item>
            <el-button style="margin-left: 20px;" @click="addProps" type="primary" :disabled="isView">新增编码</el-button>
            <div style="height:400px">
                <el-table :data="ruleform.goodsDtl" style="width: 100%" height="400" max-height="400">
                    <el-table-column type="index" width="50" />
                    <el-table-column prop="goodsCode" label="商品编码">
                        <template #header="{ column }">
                            <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>商品编码
                        </template>
                        <template #default="{ row, $index }">
                            <el-input v-model="row.goodsCode" placeholder="商品编码" maxlength="50" clearable
                                :disabled="row.deactivate" @change="changeGoodsCode(row.goodsCode, $index)" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="goodsName" label="商品名称" width="220">
                        <template #default="{ row, $index }">
                            <el-input v-model="row.goodsName" placeholder="商品名称" maxlength="50" clearable disabled />
                        </template>
                    </el-table-column>
                    <el-table-column prop="address" label="包邮单价">
                        <template #header="{ column }">
                            <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>包邮单价
                        </template>
                        <template #default="{ row, $index }">
                            <el-input-number v-model="row.postageFreePrice" placeholder="包邮单价" :min="0" :max="9999.999"
                                :disabled="row.deactivate" :precision="6" @change="changeMoney(row)"
                                :controls="false" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="address" label="不包邮单价">
                        <template #header="{ column }">
                            <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>不包邮单价
                        </template>
                        <template #default="{ row, $index }">
                            <el-input-number v-model="row.postageChargePrice" placeholder="不包邮单价" :min="0"
                                :max="9999.999" :precision="6" @change="changeMoney(row)" :controls="false" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="postageFreePriceDiff" label="包邮单价差额" align="center" />
                    <el-table-column label="操作" width="70">
                        <template #default="{ row, $index }">
                            <el-button type="danger" @click="ruleform.goodsDtl.splice($index, 1)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <el-form-item v-if="!isView">
                <div class="btnGroup">
                    <el-button @click="close">取消</el-button>
                    <el-button type="primary" @click="submitForm('ruleform')">提交</el-button>
                </div>
            </el-form-item>
        </el-form>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import decimal from '@/utils/decimal'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import { savePurchaseOrderGoodsPostageByFree, getPurchaseOrderGoodsPostageByFree } from '@/api/inventory/purchaseOrderGoodsPostage'
import {
    //分页查询店铺商品资料
    getList,
    //导入
} from "@/api/inventory/basicgoods"
const ys = {
    goodsCode: '商品编码',
    goodsName: '商品名称',
    postageFreePrice: '包邮单价',
    postageChargePrice: '不包邮单价',
    postageFreePriceDiff: '包邮单价差额',
}
export default {
    name: "scanCodePage",
    components: {
        MyContainer, uploadimgFile
    },
    props: {
        isView: {
            type: Boolean,
            default: false
        },
        formData: {
            type: Array,
            default: () => []
        },
        remark: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            ys,
            rules: {
                deliveryMethod: [
                    { required: true, message: '请选择配送方式', trigger: 'change' }
                ],
                quotationVoucher: [
                    { required: true, message: '请上传报价凭证', trigger: 'change' }
                ],
            },
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            ruleform: {
                deliveryMethod: null,//配送方式
                payment: '包邮',
                quotationVoucher: null,//报价凭证
                remark: null,//备注
                goodsDtl: [
                    {
                        goodsCode: null,//商品编码
                        goodsName: null,//商品名称
                        postageFreePrice: null,//包邮单价
                        postageChargePrice: null,//不包邮单价
                        postageFreePriceDiff: null,//包邮单价差额
                    }
                ],
            },
            chatUrls: [],
            uploadimgFileVisable: false
        }
    },
    async mounted() {
        if (this.formData.length == 1) {
            this.ruleform.deliveryMethod = this.formData[0].deliveryMethod
            if (this.formData[0].quotationVoucher) {
                this.chatUrls = this.formData[0].quotationVoucher.split(',').map((item, i) => {
                    return {
                        url: item,
                        name: `聊天截图${i + 1}`
                    }
                })
            }
            this.ruleform.remark = this.remark
            this.ruleform.quotationVoucher = this.formData[0].quotationVoucher
        }
        this.ruleform.goodsDtl = JSON.parse(JSON.stringify(this.formData))
        this.ruleform.goodsDtl.forEach(item => {
            this.changeMoney(item)
            item.deactivate = true
        })
        this.uploadimgFileVisable = true
    },
    methods: {
        //查看有没有重复的商品编码
        async changeGoodsCode(e, i) {
            //判断有没有相同的goodsCode
            const goodsCodeArr = this.ruleform.goodsDtl.filter(item => (item.goodsCode != null && item.goodsCode != '' && item.goodsCode != undefined)).map(item => item.goodsCode)
            const goodsCodeSet = new Set(goodsCodeArr)
            if (goodsCodeArr.length !== goodsCodeSet.size) {
                //遍历当前项,清空所有值
                this.ruleform.goodsDtl.forEach((item, index) => {
                    if (index === i) {
                        item.goodsCode = null
                        item.goodsName = null
                        item.postageFreePrice = undefined
                        item.postageChargePrice = undefined
                        item.postageFreePriceDiff = undefined
                    }
                })
                return this.$message.error(`第${i + 1}条数据商品编码有重复项,请检查`)
            }
            if (e) {
                e = e.replace(/\s+/g, "")
                const params = {
                    currentPage: 1,
                    pageSize: 50,
                    orderBy: null,
                    isAsc: false,
                    goodsCode: e
                }
                const { data, success } = await getList(params)
                if (success) {
                    if (data.list.length > 0) {
                        this.ruleform.goodsDtl[i].goodsName = data.list[0].goodsName ? data.list[0].goodsName : ''
                    } else {
                        this.ruleform.goodsDtl[i].goodsName = null
                    }
                }
            } else {
                this.ruleform.goodsDtl[i].goodsName = null
                this.ruleform.goodsDtl[i].postageFreePrice = undefined
                this.ruleform.goodsDtl[i].postageChargePrice = undefined
                this.ruleform.goodsDtl[i].postageFreePriceDiff = undefined
            }

        },
        //获取上传图片
        getImg(data) {
            if (data) {
                this.chatUrls = data ? data : []
                this.ruleform.quotationVoucher = data.map(item => item.url).join(',')
            }
        },
        //计算包邮差额单价
        changeMoney(row) {
            if (row.postageFreePrice === null || row.postageFreePrice === '') {
                row.postageFreePrice = undefined
            }
            if (row.postageChargePrice === null || row.postageChargePrice === '') {
                row.postageChargePrice = undefined
            }
            row.postageFreePriceDiff = (row.postageFreePrice !== undefined && row.postageFreePrice !== null && row.postageChargePrice !== undefined && row.postageChargePrice !== null) ? decimal(row.postageFreePrice, row.postageChargePrice, 6, '-') : null;
            this.$forceUpdate();//强制刷新
        },
        close() {
            this.$emit('close')
        },
        //包邮保存
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    const arr = ['goodsCode', 'goodsName', 'postageFreePrice', 'postageChargePrice', 'postageFreePriceDiff']
                    this.ruleform.goodsDtl.forEach((item, i) => {
                        for (let key in item) {
                            if (arr.includes(key) && (item[key] === null || item[key] === undefined)) {
                                this.$message.error(`${this.ys[key]}不能为空`)
                                throw new Error(`${this.ys[key]}不能为空`)
                            }
                        }
                    })
                    //如果没有图片,就提示
                    if (this.chatUrls.length === 0) {
                        this.$message.error('请上传报价凭证')
                        return false
                    }
                    try {
                        savePurchaseOrderGoodsPostageByFree(this.ruleform).then(({ success }) => {
                            if (success) {
                                this.$emit('getList')
                                this.$message.success('保存成功')
                                this.$emit('close')
                            }
                        })
                    } catch (error) {
                        this.$message.error('保存失败')
                    }
                } else {
                    return false;
                }
            });
        },
        addProps() {
            this.ruleform.goodsDtl.push({
                goodsCode: null,//商品编码
                goodsName: null,//商品名称
                disabled: false,
            })
        }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.chatPicUrl {
    position: relative;

    .picTips {
        position: absolute;
        top: 0;
        left: 150px;
        color: #ff0000;
        font-size: 16px;
    }
}

.btnGroup {
    display: flex;
    justify-content: end;
    margin-top: 20px;

    .el-button {
        margin: 0 10px;
    }
}
</style>
