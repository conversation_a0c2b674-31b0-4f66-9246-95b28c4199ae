<template>
  <my-container v-loading="pageLoading" style="height:80%;">
    <template #header>
        <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>   
          <el-form-item label="日期:">
            <el-date-picker
                  style="width:220px"
                  v-model="filter.timerange"
                  type="daterange"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  range-separator="至"
                  start-placeholder="开始"
                  end-placeholder="结束"
                  :clearable="false"
                  :picker-options="pickerOptions"
                  @change="onSearch"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="平台:">
              <el-select
                v-model="filter.platform" style="width:110px;"
                placeholder="请选择"
                :clearable="true" :collapse-tags="true"  filterable >
                <el-option
                  v-for="item in platformList"
                  :key="item.value"
                  :label="item.label" 
                  :value="item.value"/>
              </el-select>
          </el-form-item>       
          <el-form-item label="店铺:">
              <el-select style="width:180px;"
                v-model="filter.shopCode"
                placeholder="请选择" @change="onSearch"
                :clearable="true" :collapse-tags="true"  filterable>
                <el-option
                  v-for="item in shopList"
                  :key="item.shopCode"
                  :label="item.shopName"
                  :value="item.shopCode"/>
              </el-select>
            </el-form-item>
            <el-form-item label="运营组:">
              <el-select
                v-model="filter.groupId" style="width:120px;"
                placeholder="请选择"
                :clearable="true" :collapse-tags="true"  filterable @change="onSearch">
                <el-option
                  v-for="item in groupList"
                  :key="item.key"
                  :label="item.value" 
                  :value="item.key"/>
              </el-select>
            </el-form-item> 
            <el-form-item label="产品ID:">
                <el-input v-model.trim="filter.proCode" placeholder="产品ID" style="width:150px;" @change="onSearch"/>         
            </el-form-item>  
            <el-form-item label="最小相似度:">
              <el-input type="number" v-model="filter.similarity" placeholder="默认大于0" @change="onSearch" style="width:110px;"/>         
            </el-form-item>  
            <el-tag type="danger" size="large" effect="light"
              style="margin-left:30px;height:30px;line-height:30px;border:none;">
            提示：红色字体的行是主链接（即销量最高的链接）
            </el-tag>            
        </el-form>
          
    </template>
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarryShow"
        :tableData='list'    :tableCols='tableCols' :isSelection="false" :tablefixed="true"
        :tableHandles='tableHandles' :isSelectColumn="true" @select="selsChange"
        :loading="listLoading" :customRowStyle="customRowStyle" :headerCellStyle="headerCellStyle">
      </ces-table>
    <template #footer>
      <my-pagination
          ref="pager"
          :total="total"
          :checked-count="sels.length"
          @get-page="getlist"
       />
    </template>

    <el-dialog :visible.sync="detailPro.visible" :show-close="false" width="45%" v-dialogDrag height="700" @close="detailPro.pagerStyle={};" append-to-body> 
      <el-button type="primary" @click="onSearchDetailPro">查询</el-button>
      <el-button type="primary" @click="showNextPro">下一个</el-button>    
      <div style="margin-bottom:10px;margin-top:5px;"> 
          <el-descriptions :column="3" size="mini" border>
              <el-descriptions-item label="平台" >{{myformatPlatform(detailPro.selRow.platform)}}</el-descriptions-item> 
              <el-descriptions-item label="店铺" >{{detailPro.selRow.shopName}}</el-descriptions-item>
              <el-descriptions-item label="运营组" >{{ selGroupName }}</el-descriptions-item> 
              <el-descriptions-item label="产品ID" >
                <div v-html="myformatLinkProCode(detailPro.selRow.platform,detailPro.selRow.proCode)">
                </div>
              </el-descriptions-item> 
              <el-descriptions-item label="近30天销量" >{{ detailPro.selRow.salesQty }}</el-descriptions-item>
              <el-descriptions-item label="编码数" >{{ detailPro.selRow.goodsCodeQty }}</el-descriptions-item>
              <el-descriptions-item label="相似编码数" >{{ detailPro.selRow.goodsCodeQtySame }}</el-descriptions-item>
              <el-descriptions-item label="相似度(%)" >{{ detailPro.selRow.similarity }}</el-descriptions-item>
              <el-descriptions-item label="相似编码库存资金" >{{ detailPro.selRow.invAmountSame }}</el-descriptions-item>
              <el-descriptions-item label="非相似编码库存资金" >{{ detailPro.selRow.invAmountDiff }}</el-descriptions-item>
          </el-descriptions>
      </div>                    
 
      <ces-table ref="tableDetailPro" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchangeDetailPro'
        :tableData='detailPro.list'    :tableCols='detailPro.tableCols' :isSelection="false" :summaryarry="summaryarryDetailPro"
        :tableHandles='detailPro.tableHandles' :isSelectColumn="false" @select="selsChangeDetailPro" style="height:360px;"
        :loading="detailPro.listLoading">
      </ces-table>
    
      <my-pagination
          ref="pagerDetailPro"
          :total="detailPro.total"
          :checked-count="detailPro.sels.length"
          @get-page="getlistDetailPro" :style="detailPro.pagerStyle"
        />
    </el-dialog>
  </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatPlatform,formatLinkProCode,formatmoney} from "@/utils/tools";
import { 
  pageProCodeSimilarityDetail,
  pageProCodeSimilarityDetailGood,
  getProCodeSimilarityDetailSummary,
  exportProCodeSimilarityDetailList
} from "@/api/order/procodesimilarity";
import { getList as getshopList } from '@/api/operatemanage/base/shop';

//格式化money列：大于1 时，去掉小数点，小于1时保留小数点
var myformatmoney=function(value){
  return formatmoney(Math.abs(value)>1?Math.round(value):Math.round(value,1));
};

//日报列
const  dayReportCols=[
        {istrue:true,prop:'orderCountDayReport',label:'订单量',sortable:'custom',  permission:"prosameprofit",width:'70',formatter:(row)=> !row.orderCountDayReport?" ": row.orderCountDayReport},
        {istrue:true,prop:'saleAmont',label:'销售金额',sortable:'custom', permission:"prosameprofit",width:'100',formatter:(row)=> !row.saleAmont?" ": row.saleAmont.toFixed(2)},
        {istrue:true,prop:'saleCost',label:'销售成本',sortable:'custom', permission:"prosameprofit",width:'80',formatter:(row)=> !row.saleCost?" ": row.saleCost.toFixed(2)},
        {istrue:true,prop:'profit1',label:'订单毛利',sortable:'custom',permission:"prosameprofit", width:'80',type:'custom',tipmesg:'销售金额-销售成本',formatter:(row)=> !row.profit1?" ": row.profit1.toFixed(2)},
        {istrue:true,prop:'profit1Rate',label:'毛利率',sortable:'custom',permission:"prosameprofit", width:'80',type:'custom',tipmesg:'订单毛利/销售金额',formatter:(row)=> !row.profit1Rate?" ": (row.profit1Rate*100).toFixed(2)+'%'},
        {istrue:true,prop:'dkAmont',label:'平台扣点',sortable:'custom', permission:"prosameprofit",width:'80',type:'custom',tipmesg:'支付宝账单费用扣点，新上链接天猫-7.5%；C店2%；已有ID-上月月报百分比',formatter:(row)=> !row.dkAmont?" ": row.dkAmont.toFixed(2)},
        {istrue:true,summaryEvent:true,prop:'refundAmont',label:'总退款金额',permission:"prosameprofit",sortable:'custom', width:'70',tipmesg:'当日发生的总退款金额，包括历史订单',formatter:(row)=> !row.refundAmont?" ": row.refundAmont.toFixed(2)},
        {istrue:true,prop:'expressDeductAmount',label:'延迟发货扣款',permission:"prosameprofit",sortable:'custom', width:'80',type:'custom',tipmesg:'延迟发货扣款(快递罚款)',formatter:(row)=> !row.expressDeductAmount?" ": row.expressDeductAmount.toFixed(2)},
        {istrue:true,prop:'allMarketingCost',label:'总广告费',sortable:'custom', permission:"prosameprofit",width:'80',formatter:(row)=> !row.allMarketingCost?" ": row.allMarketingCost?.toFixed(2)},
         
        {istrue:true,prop:'packageFee',label:'包装材料',sortable:'custom', permission:"prosameprofit",width:'80',formatter:(row)=> !row.packageFee?" ": row.packageFee?.toFixed(2)},
        {istrue:true,prop:'freightFeeTotal',label:'快递费',sortable:'custom', permission:"prosameprofit",width:'80',formatter:(row)=> !row.freightFeeTotal?" ": row.freightFeeTotal?.toFixed(2)},
        {istrue:true,prop:'profit2',label:'毛二利润',sortable:'custom',permission:"prosameprofit", width:'80',type:'custom',tipmesg:'销售金额-销售成本-平台扣点-延迟发货扣款-营销费用-淘宝客-首单礼金-特殊单(刷单、补单、大灰熊)-包装材料-快递费',
                                            formatter:(row)=>!row.profit2?" ": row.profit2?.toFixed(2)},
        {istrue:true,prop:'profit2Rate',label:'毛二利润率',sortable:'custom', permission:"prosameprofit",width:'80',type:'custom',tipmesg:'毛二利润/销售金额',formatter:(row)=>!row.profit2Rate?" ": (row.profit2Rate*100).toFixed(2)+'%'},
        {istrue:true,prop:'profit3PredictRate',label:'毛三预估比例',type:'custom',permission:"prosameprofit",tipmesg:'(空白链接ID成本+异常成本+补发成本+代发成本+采购运费+产品费用+工资+损耗)/销售金额',
            sortable:'custom', width:'80',formatter:(row)=> !row.profit3PredictRate?" ": (row.profit3PredictRate*100).toFixed(2)+'%'},
        {istrue:true,prop:'profit3PredictFee',label:'预估费用',type:'custom',permission:"prosameprofit",tipmesg:'毛三预估比例*销售金额',sortable:'custom', width:'80',formatter:(row)=> !row.profit3PredictFee?" ": row.profit3PredictFee?.toFixed(2)},
        {istrue:true,prop:'profit3',label:'毛三利润',sortable:'custom', width:'80',type:'custom',tipmesg:'毛二利润-预估费用',permission:"prosameprofit",formatter:(row)=> !row.profit3?" ": row.profit3?.toFixed(2)},
        {istrue:true,prop:'profit3Rate',label:'毛三利润率',type:'custom',tipmesg:'毛三利润/销售金额',sortable:'custom', width:'80',permission:"prosameprofit",formatter:(row)=> !row.profit3Rate?" ": (row.profit3Rate*100).toFixed(2)+'%'},
        {istrue:true,prop:'shareRate',label:'公摊费率',type:'custom',tipmesg:'明细2/销售金额',sortable:'custom', width:'80',permission:"prosameprofit",formatter:(row)=> !row.shareRate?" ": (row.shareRate*100).toFixed(2)+'%'},
        {istrue:true,prop:'shareFee',label:'公摊费',type:'custom',tipmesg:'公摊费(%)*销售金额',sortable:'custom', width:'80',permission:"prosameprofit",formatter:(row)=> !row.shareFee?" ": row.shareFee.toFixed(2)},
        
        {istrue:true,prop:'profit33',label:'毛四利润',type:'custom',sortable:'custom', width:'80',permission:"prosameprofit",formatter:(row)=> !row.profit33?" ": row.profit33.toFixed(2)},
        {istrue:true,prop:'profit33Rate',label:'毛四利润率',type:'custom',sortable:'custom', width:'80',permission:"prosameprofit",formatter:(row)=> !row.profit33Rate?" ": (row.profit33Rate*100).toFixed(2)+'%'},
        
        {istrue:true,prop:'profit4',label:'净利润',type:'custom',tipmesg:'毛三利润-公摊费',sortable:'custom', width:'80',permission:"prosameprofit",formatter:(row)=> !row.profit4?" ": row.profit4.toFixed(2)},
        {istrue:true,prop:'profit4Rate',label:'净利率',type:'custom',tipmesg:'净利润/销售金额',sortable:'custom', width:'80',permission:"prosameprofit",formatter:(row)=> !row.profit4Rate?" ": (row.profit4Rate*100).toFixed(2)+'%'}
  ];

//商品客户咨询列
const customerCols =[
    {istrue:true,prop:'invAmountSame',label:'相似编码库存资金', permission:"prosameprofit",width:'100',sortable:'custom',formatter:(row)=>myformatmoney(row.invAmountSame)},
    {istrue:true,prop:'invAmountDiff',label:'非相似编码库存资金', permission:"prosameprofit",width:'100',sortable:'custom',formatter:(row)=>myformatmoney(row.invAmountDiff)},
    {istrue:true,prop:'invAmount',label:'编码库存资金', permission:"prosameprofit",width:'100',sortable:'custom',formatter:(row)=>myformatmoney(row.invAmount)},
    {istrue:true,prop:'invAmountPredict',label:'编码库存资金预估', permission:"prosameprofit",width:'100',sortable:'custom',formatter:(row)=>myformatmoney(row.invAmountPredict)},
    {istrue:true,prop:'orderCount',label:'近30天订单数',permission:"prosameprofit", width:'80',sortable:'custom',formatter:(row)=>myformatmoney(row.orderCount)}, 
    {istrue:true,prop:'salesQty',label:'近30天销量', permission:"prosameprofit",width:'80',sortable:'custom',formatter:(row)=>myformatmoney(row.salesQty)},
    {istrue:true,prop:'amount',label:'近30天销售额', permission:"prosameprofit",width:'100',sortable:'custom',formatter:(row)=>myformatmoney(row.amount)},
    {istrue:true,prop:'cost',label:'近30天成本', permission:"prosameprofit",width:'100',sortable:'custom',formatter:(row)=>myformatmoney(row.cost)},
    {istrue:true,prop:'profit',label:'近30天毛利润', permission:"prosameprofit",width:'100',sortable:'custom',formatter:(row)=>myformatmoney(row.profit)},
    {istrue:true,prop:'inquiries',label:'咨询量',type:'custom',tipmesg:'链接在查询日期范围内的顾客咨询量之和',sortable:'custom',permission:"", width:'80',formatter:(row)=> !row.inquiries?" ": row.inquiries},
    {istrue:true,prop:'inquiriesSuccessRate',label:'转化率',type:'custom',tipmesg:'成功的咨询量/总咨询量',sortable:'custom',permission:"", width:'80',formatter:(row)=> !row.inquiriesSuccessRate?" ": (row.inquiriesSuccessRate*100).toFixed(2)+'%'},
    {istrue:true,summaryEvent:true,prop:'refundAmontBeforeRate',label:'发货前退款率',sortable:'custom', permission:"prosameprofit",width:'80',formatter:(row)=> !row.refundAmontBeforeRate?" ": (row.refundAmontBeforeRate*100).toFixed(2)+'%'},
    {istrue:true,summaryEvent:true,prop:'refundAmontAfterRate',label:'发货后退款率',sortable:'custom', permission:"prosameprofit",width:'80',formatter:(row)=> !row.refundAmontAfterRate?" ": (row.refundAmontAfterRate*100).toFixed(2)+'%'},
];

const tableCols=[    
       {istrue:true,fixed:true,prop:'platform',label:'平台', width:'80',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
       {istrue:true,fixed:true,prop:'shopCode',label:'店铺', width:'100',sortable:'custom',formatter:(row)=>row.shopName}, 
       {istrue:true,fixed:true,prop:'groupId',label:'运营组', width:'90',sortable:'custom',formatter:(row)=>row.groupName},
       {istrue:true,fixed:true,prop:'proCode',label:'产品ID', width:'150',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
       {istrue:true,label:"查看",width:"80",type:'button',btnList:[
          {label:"产品编码",handle:(that,row)=>that.showDetailPro(row)}
       ]},       
       {istrue:true,label:'毛三利润走势',type:'star',permission:"prosameprofit",tipmesg:'毛三利润近四周走势，红星代表正利润，绿星代表负利润，空星则代表暂未利润'},
       {istrue:true,prop:'goodsCodeQty',label:'编码数',permission:"prosameprofit", width:'70',sortable:'custom',},
       {istrue:true,prop:'goodsCodeQtySame',label:'相似编码数',permission:"prosameprofit", width:'70',sortable:'custom',},
       {istrue:true,prop:'similarity',label:'相似度(%)',permission:"prosameprofit", width:'70',sortable:'custom',},
             
     ].concat(dayReportCols).concat(customerCols);
  
  const tableColsProGoods=[
       {istrue:true,prop:'isSame',label:'是否相似', width:'100',sortable:'custom',type:'switch',isDisabled:(row)=>true},
       {istrue:true,prop:'goodsCode',label:'商品编码', width:'130',sortable:'custom'},
       {istrue:true,prop:'goodsName',label:'商品名称', width:'auto',sortable:'custom'},
       {istrue:true,prop:'goodsImage',label:'图片', width:'60',sortable:'custom',type:'imageGoodsCode',goods:{code:'goodsCode',name:'goodsName'}},
       {istrue:true,prop:'invAmount',label:'编码库存资金', width:'100',sortable:'custom',formatter:(row)=>myformatmoney(row.invAmount)},
       {istrue:true,prop:'invAmountPredict',label:'编码库存资金预估', width:'140',sortable:'custom',formatter:(row)=>myformatmoney(row.invAmountPredict)},
     ];

  const tableHandles=[
        {label:"导出", handle:(that)=>that.onExport()}
      ];
  const startDate = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
  const endDate = formatTime(new Date(), "YYYY-MM-DD");
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  props:{
    filter: {
      parentId:null,
      proCode:null,
      status:null,
      platform:null,
      shopId:"",
      groupId:null,
      similarity:null,
      startDate:null,
      endDate:null,
      timerange:[startDate,endDate]
    },
    platformList:{type:Array,default:()=>[]},   
    groupList:{type:Array,default:()=>[]}
  },
  data() {
    return {
      that:this,
      shopList: [],
      list: [],
      summaryarry:{},
      pager:{OrderBy:"orderCountDayReport",IsAsc:false},
      tableCols:tableCols,  
      tableHandles:tableHandles,      
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading:false,
      customRowStyle:function(data){          
        if(data.row.isMain){
            return {color:'red'};
        }
      },
      detailPro:{
          list: [],
          pager:{OrderBy:"isSame",IsAsc:false},
          tableCols:tableColsProGoods,  
          tableHandles:[],      
          total: 0,
          sels: [], 
          listLoading: false,
          visible:false,
          filter:{
            detailParentId:null,
            goodsCode:null
          },
          selRow:{},
          summaryarry:{},
          pagerStyle:{
            "margin-top":"33px"
          }
      },
      pickerOptions:{
        disabledDate(time){
          return time.getTime()>Date.now();
        }
      },
    }
  },
  async mounted() {
    console.log(this.filter,'filter');
    
    if(this.filter.platform)
      await this.onchangeplatform(this.filter.platform);  
    else
      await this.getlist();        
  },
  methods: {    
    //设置店铺下拉
    async onchangeplatform(val){
      const res = await getshopList({platform:val,CurrentPage:1,PageSize:1000});
      this.shopList=res.data.list||[];
      this.filter.shopCode="";
      await this.onSearch();
    },   
    clearFilter(){
      this.filter={
          parentId:null,
          proCode:null,
          status:null,
          platform:null,
          shopId:"",
          similarity:null,
          timerange:this.filter.timerange,
          startDate:null,
          endDate:null
      };
    },
    selsChange: function(sels) {
      this.sels = sels;
    },
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else{
        var orderBy =column.prop;
        this.pager={OrderBy:orderBy,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    },
    //获取查询条件
    getCondition(){
      var pager = this.$refs.pager.getPager();
      var page  = this.pager;
      this.filter.startDate =null;
      this.filter.endDate =null;
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      const params = {
        ...pager,
        ...page,
        ...this.filter
      }

      return params;
    },
    //查询第一页
    async onSearch() {      
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    //分页查询
    async getlist() {
      var params=this.getCondition();
      if(params===false){
            return;
      }

      this.listLoading = true;
      var res = await pageProCodeSimilarityDetail(params);
      
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      this.summaryarry = res.data.summary;
      const data = res.data.list||[];
      data.forEach(d => {
        d._loading = false
      })
      this.list = data;
      await this.getSummary();
    },
    async getSummary() {
      var params=this.getCondition();
      if(params===false){
            return;
      }

      var res = await getProCodeSimilarityDetailSummary(params);;
      if (!res?.success) {
        return
      }
      this.summaryarry = res.data;
    },
    myformatLinkProCode(platform,proCode){
      return formatLinkProCode(platform,proCode);
    },
    myformatPlatform(platform){
      return formatPlatform(platform);
    },
    ///==产品明细 Start==========================================
    showDetailPro(row){
      this.detailPro.visible=true;
      this.detailPro.selRow=row;
      this.detailPro.filter.detailParentId=row.id;
      setTimeout(async () => {
        await this.onSearchDetailPro();
      }, 500);
    },
    selsChangeDetailPro: function(sels) {
      this.detailPro.sels = sels;
    },
    async sortchangeDetailPro(column){
      if(!column.order)
        this.detailPro.pager={};
      else{
        var orderBy =column.prop;
        this.detailPro.pager={OrderBy:orderBy,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearchDetailPro();
    },
    //获取查询条件
    getConditionDetailPro(){
      var pager = this.$refs.pagerDetailPro.getPager();
      var page  = this.detailPro.pager;
      const params = {
        ...pager,
        ...page,
        ...this.detailPro.filter
      }

      return params;
    },
    //查询第一页
    async onSearchDetailPro() {
      this.$refs.pagerDetailPro.setPage(1)
      await this.getlistDetailPro()
    },
    //分页查询
    async getlistDetailPro() {
      var params=this.getConditionDetailPro();
      if(params===false){
            return;
      }     
      this.detailPro.listLoading = true;
      var res= await pageProCodeSimilarityDetailGood(params);   
      this.detailPro.listLoading = false;
      if (!res?.success) {
        return
      }
      this.detailPro.total = res.data.total;     
      const data = res.data.list||[];
      data.forEach(d => {
        d._loading = false
      })
      this.detailPro.list = data;
      this.detailPro.summaryarry =res.data.summary;    
    },
    showNextPro(){
      console.log(this.list,'this.list');
      if(this.list&&this.list.length>0){
        var nextRow=this.list[0];
        var findCur=false;
        this.list.forEach(item => {
          if(findCur){
            findCur=false;
            nextRow=item;
          }
          if(item.id==this.detailPro.selRow.id){
            findCur=true;
          }          
          console.log(item,'item');
        });
        this.detailPro.selRow=nextRow;
        this.showDetailPro(nextRow);
      }
    },
    //导出
    async onExport() {
      this.filter.startDate =null;
      this.filter.endDate =null;
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      const params = {
        ...this.filter
      }
      if(params===false){
            return;
      }     
      var res = await exportProCodeSimilarityDetailList(params);
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute(
        "download",
        "系列相似产品数据_" + new Date().toLocaleString() + ".xlsx"
      );
      aLink.click();
    },
    ///==产品明细 End  ==========================================
    //表头样式
    headerCellStyle(data){       
        if(data&&data.column){
          var isDayReportCol = dayReportCols.find(a=>a.prop == data.column.property);
          if(isDayReportCol){
              return  {color:'#F56C6C'}
          }
        }
        return null;
    },
  },
  computed:{
    selGroupName(){
      var name = this.groupList?.find(a=>a.key==this.detailPro.selRow.groupId)?.value;
      return name || "未知";
    },
    selPlatformName(){
      var name = this.platformList?.find(a=>a.value==this.detailPro.selRow.platform)?.label;
      return name ;
    },
    summaryarryShow(){
      var sum ={};
      if(this.summaryarry){
        for (const key in this.summaryarry) {
          sum[key]=myformatmoney(this.summaryarry[key])
        }
      }
      return sum;
    },
    summaryarryDetailPro(){
      var sum ={};
      if(this.summaryarry){
        for (const key in this.detailPro.summaryarry) {
          sum[key]=myformatmoney(this.detailPro.summaryarry[key])
        }
      }
      return sum;
    },    
  },
  watch:{
    async "filter.platform"(){
      console.log("this.filter.platform",this.filter.platform)
      await this.onchangeplatform(this.filter.platform);
    }
  }
}
</script>
<style scoped>
  ::v-deep .el-link.el-link--primary{
    margin-right: 7px;
  }

  ::v-deep .el-table__fixed 
  {
      pointer-events:auto;
  } 
</style>
