<template>
    <div>
        <!-- 汇总 -->
        <!-- :showsummary="true" -->
        <cesTable ref="alreadyTable" :id="'vendorSummary20240821151856'" :tableData="tableData" :tableCols="tableCols"
            :is-index="true" :that="that" style="width: 100%; height: 100%; margin: 0" @sortchange='sortchange'
            @rowStyle="rowStyle" :treeProp="{ rowField: 'id', parentField: 'parentId', transform: true,}" :loading="listLoading"
            class="detail">
            <template #styleCode="{ row }">
                <div class="styleCode">{{ row.styleCode }}</div>
            </template>
            <template #warehousNo="{ row }">
                <div v-if="row.orderVideoList">
                    <div v-for="item in row.orderVideoList">{{ item.warehousNo }}</div>
                </div>
            </template>
            <template #modifiedUserName="{ row }">
                <div v-if="row.orderVideoList">
                    <div v-for="item in row.orderVideoList">{{ item.modifiedUserName }}</div>
                </div>
            </template>
            <template #createdUserName="{ row }">
                <div v-if="row.orderVideoList">
                    <div v-for="item in row.orderVideoList">{{ item.createdUserName }}</div>
                </div>
            </template>
            <template #modifiedTime="{ row }">
                <div v-if="row.orderVideoList">
                    <div v-for="item in row.orderVideoList">{{ item.modifiedTime }}</div>
                </div>
            </template>
        </cesTable>
        <my-pagination :page-size="50" ref="pager" :total="detailTotal" @page-change="detailPagechange"
            @size-change="detailSizechange" />

        <!-- 弹层部分 -->
        <el-dialog title="日志" :visible.sync="RecordsVisible" width="60%" :before-close="handleClose1" v-dialogDrag>
            <cesTable :id="'vendorSummary202408041624'" ref="detailTable" :tableData="doTableData" :toolbarshow="false"
                :tableCols="tableCols4" :is-index="true" :that="that" :showsummary="true"
                style="width: 100%; height: 500px" class="detail">
            </cesTable>
        </el-dialog>

        <!-- 入库以及驳回 -->
        <el-dialog :title="dialogtitle" :visible.sync="peizhiVisible" width="20%" v-dialogDrag>
            <rcpeizhi :peizhidata="peizhidata" @closedialog="peizhiVisible = false;" v-if="peizhiVisible"></rcpeizhi>
        </el-dialog>
    </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import rcpeizhi from "./rcpeizhi.vue"
import {
    getProviderQuotationHisRecordPageList,
    GetProviderDockingResultList,
    getProviderNewGoodsDockingResultList,
    getProviderQuotationRecordOverViewPageList,
}
    from '@/api/openPlatform/ProviderQuotation'
import { pagePurchaseOrderByProviderNameAsync } from '@/api/inventory/purchase'
import { getAllProBrand } from '@/api/inventory/warehouse'
import { getWarehousingOrderVideoWeighingPage, setOrderVideoWeighingStatus, batchSetOrderVideoWeighingStatus, getPurchaseQualityMetageWeightLog } from '@/api/inventory/purchasequality.js'
const options = [
    {
        value: '1',
        label: '是'
    },
    {
        value: '0',
        label: '否'
    }
]

//对接记录
const tableCols4 = [
    { istrue: true, prop: 'createdUserName', label: '操作人', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '操作时间', sortable: 'custom' },
    { istrue: true, prop: 'logContent', label: '操作内容', sortable: 'custom' },
]
//汇总
const tableCols = [
    { istrue: true, prop: 'buyNo', label: '采购单号', treeNode: true, width: 120, fixed: 'left', },
    { istrue: true, prop: 'warehousNo', label: '任务编号', width: 95, fixed: 'left' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: 95, },
    { istrue: true, prop: 'goodsCount', label: '数量', width: 95 },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: 95, },
    { istrue: true, prop: 'createdUserName', label: '拍摄人', width: 95, },
    { istrue: true, prop: 'brandName', label: '采购人', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'modifiedUserName', label: '采购跟单', width: 95, },
    { istrue: true, prop: 'sysWeight', label: '采购重量',  width: 95, },
    { istrue: true, prop: 'metageWeight', label: '到货重量',  width: 130, },
    { istrue: true, prop: 'modifiedTime', label: '到货时间', width: 95, },
    { istrue: true, prop: 'purStatus', label: '采购单状态', sortable: 'custom', width: 95, },
    // { istrue: true, prop: 'canQualityQty', label: '可质检数', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'status', label: '状态',  width: 95, },
    { istrue: true, prop: 'picture', label: '拍摄图片', width: 95, type: 'imagess' },
    { istrue: true, prop: 'evidence', label: '凭证', width: 95, type: 'images' },
    { istrue: true, prop: 'remark', label: '备注',  width: 95, },
    {
        istrue: true, prop: 'caozuo', type: "button", label: '操作', align: 'center', sortable: false, width: "200", fixed: "right",
        btnList: [
            { label: "入库", type: "primary", handle: (that, row) => that.rukuedit(row, '入库'), permission: "Api:Inventory:PurchaseQuality:SetOrderVideoWeighingStatus", ishide: (that, row) => row.status != null && row.parentId != null, },
            { label: "暂缓", type: "primary", handle: (that, row) => that.rukuedit(row, '暂缓'), permission: "Api:Inventory:PurchaseQuality:SetOrderVideoWeighingStatus", ishide: (that, row) => row.status != null && row.parentId != null, },
            { label: "驳回", type: "primary", handle: (that, row) => that.rukuedit(row, '驳回'), permission: "Api:Inventory:PurchaseQuality:SetOrderVideoWeighingStatus", ishide: (that, row) => row.status != null && row.parentId != null, },
            { label: "日志", type: "primary", handle: (that, row) => that.dockingRecords(row), ishide: (that, row) => row.parentId == null, },
        ]
    }
]
export default {
    components: { MyContainer, cesTable, vxetablebase, rcpeizhi },
    name: "vendorSummary",
    props: {
        ListInfo: {
            type: Object,
            default: () => { }
        },
        isHidden: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            that: this,
            dialogtitle: '',
            peizhiVisible: false,
            peizhidata: {},
            logDetail: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                openId: null,//openId
                styleCode: null,//系列编码
                orderBy: null,//排序字段
                isAsc: true,//是否升序
            },
            RecordsInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                id: null,//id
            },//对接记录请求参数
            nameInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                supplier: null,//供应商名称
            },
            // sourceType,//来源
            // positionType,//职位
            tableCols,//已提交
            tableCols4,//对接记录
            // tableCols5,//点击供应商名字
            tableData: [],//已提交
            tableData1: [],//详情
            xltableData: [],//系列编码
            newTableData: [],//新品提交
            doTableData: [],//对接记录
            brandList: [],//采购列表
            logTotal: 0,//详情总数
            detailTotal: 0,//已提交总数
            nameTotal: 0,//供应商报价详情总数
            listLoading: true,//加载中
            dialogVisible: false,//详情弹层
            RecordsVisible: false,//对接记录弹层
            operateVisible: false,//操作弹层
            nameVisible: false,//点击供应商名字弹层
            recordsTotal: 0,//对接记录总数
            options,//是否包邮,是否含税
            procurementList: [],//采购人员列表
            checkList: [],
            query: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            }
        };
    },
    mounted() {
        this.query = { ...this.ListInfo, ...this.query }
        this.getAlreadyList()
        this.getBrandList()
    },
    methods: {
        changepage(value){
            if(!value){
                // setTimeout(()=>{
                    this.$refs.alreadyTable.changecolumn('caozuo')
                // },0)
            }else{
                // setTimeout(()=>{
                    this.$refs.alreadyTable.changecolumn_setTrue('caozuo')
                // },0)

            }
        },
        rowStyle(row, callback) {

            let obj = {};
            if (row.parentId == null) {
                obj = {
                    height: '100px',
                }
            }
            callback(obj);
        },
        rukuedit(row, name) {
            this.dialogtitle = name;
            this.peizhidata = row;
            if (name == '暂缓') {
                this.$confirm('此操作将此数据暂缓, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    let success
                    if (row.parentId) {
                        const { success: success1 } = await setOrderVideoWeighingStatus({ operateType: '暂缓', id: row.id, mainId: row.mainId, buyNo: row.buyNo })
                        success = success1
                    } else {
                        const { success: success1 } = await batchSetOrderVideoWeighingStatus({ operateType: '暂缓', buyNo: row.buyNo })
                        success = success1
                    }
                    if (success) {
                        this.$message({
                            type: 'success',
                            message: '操作成功!'
                        });
                        this.getAlreadyList()
                    }
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消操作'
                    });
                });
                return;
            }
            this.peizhidata.operateType = name;
            this.peizhiVisible = true;
        },
        //获取采购列表
        async getBrandList() {
            const { data, success } = await getAllProBrand()
            if (success) {
                this.brandList = data.map(item => {
                    return {
                        label: item.value,
                        value: item.key
                    }
                })
            } else {
                this.$message.error('获取采购人员列表失败')
            }
        },
        //汇总对接记录
        async dockingRecords(row) {
            this.RecordsInfo.id = row.id
            const { data, success } = await getPurchaseQualityMetageWeightLog(this.RecordsInfo)
            if (success) {
                this.doTableData = data
                this.RecordsVisible = true

            } else {
                this.$message.error('获取对接记录失败')
            }
        },
        handleClose1() {
            this.RecordsVisible = false
        },
        //页面数量改变
        detailSizechange(val) {
            this.query.currentPage = 1;
            this.query.pageSize = val;
            this.getAlreadyList();
        },
        //当前页改变
        detailPagechange(val) {
            this.query.currentPage = val;
            this.getAlreadyList();
        },
        //获取供应商汇总列表
        async getAlreadyList(isSeach, query) {
            if (isSeach) {
                this.$refs.pager.setPage(1)
                this.query = { ...this.query, ...query }
            }
            this.listLoading = true;
            if (this.query.timerange && this.query.timerange.length > 0) {
                this.query.startTime = this.query.timerange[0];
                this.query.endTime = this.query.timerange[1];
            } else {
                this.query.startTime = null;
                this.query.endTime = null;
            }
            const { data, success } = await getWarehousingOrderVideoWeighingPage(this.query);
            if (success) {
                // data.list.forEach(item => {
                //     item.evidence = item.evidence ? item.evidence.split(',') : []
                // })
                data.list.forEach(item=>{
                    item['picture'] = item['picture'].split(',');
                });
                this.tableData = data.list;
                console.log(this.tableData, 'this.tableData');
                this.detailTotal = data.total;
                setTimeout(()=>{
                    this.listLoading = false;
                },500)
            } else {
                setTimeout(()=>{
                    this.listLoading = false;
                },500)
                this.$message.error('获取列表失败')
            }
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.query.orderBy = prop
                this.query.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getAlreadyList()
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.header {
    display: flex;
    margin-bottom: 10px;
}

.publicMargin {
    margin-right: 20px;
}

.detail ::v-deep .vxe-custom--wrapper {
    display: none !important;
}

::v-deep .el-badge__content {
    padding: 0 4px;
    top: 7px;
    right: 0;
}
</style>
