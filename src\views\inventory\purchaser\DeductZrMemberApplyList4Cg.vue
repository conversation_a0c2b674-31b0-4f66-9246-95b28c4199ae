<template>
    <container v-loading="pageLoading">
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" 
            :tableData='list' :tableCols='tableCols' :isSelection="false" @select="selectchange" 
             :loading="listLoading">

            </ces-table>
            <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag>
                <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" style="z-index:10000;height:600px" />
            </el-dialog>

        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </container>
</template>

<script>
    import container from '@/components/my-container/noheader'
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import dayjs from "dayjs";
   
    import {  formatTime,formatLinkProCode, formatSendWarehouse, formatExpressCompany } from "@/utils/tools";
    import { PageDeductZrMemberAppealList, exportOrderWithholdListNew } from "@/api/order/orderdeductmoney"
    import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";

    import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
    const tableCols = [        
        { istrue: true, prop: 'proCode', label: '宝贝ID', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
        { istrue: true, prop: 'orderNo', label: '订单编号', width: '190', sortable: 'custom', formatter: (row) => !row.orderNo ? " " : row.orderNo, type: 'click', handle: (that, row) => that.showLogDetail(row) },
        { istrue: true, prop: 'deductOccurTime', label: '扣款日期', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.deductOccurTime, "YYYY-MM-DD") },
      

        { istrue: true, prop: 'memberName', label: '申诉人', width: '80', sortable: 'custom' },        

        { istrue: true, prop: 'newMemberName', label: '新责任人', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'newUserAuditState', label: '新责任人审核状态', width: '80', formatter: (row) => row.newUserAuditStateText },
        { istrue: true, prop: 'newUserAuditTime', label: '新责任人审核时间', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.newUserAuditTime, "YYYY-MM-DD HH:mm") },

        { istrue: true, prop: 'applyContent', label: '申诉内容', width: 'auto', sortable: 'custom' , formatter: (row) => row.applyContentText },
        { istrue: true, prop: 'applyTime', label: '申诉时间', width: '130', sortable: 'custom', formatter: (row) => formatTime(row.applyTime, "YYYY-MM-DD HH:mm") },

        { istrue: true, prop: 'applyState', label: '审核状态', width: '80', sortable: 'custom' , formatter: (row) => row.applyStateText },
        { istrue: true, prop: 'auditUserName', label: '审核人', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'auditTime', label: '审核时间', width: '130', sortable: 'custom', formatter: (row) => formatTime(row.auditTime, "YYYY-MM-DD HH:mm") },
        { istrue: true, prop: 'auditRemark', label: '审核意见', width: '100', sortable: 'custom' },
        { istrue:true,label:'功能',width:'100',type:'button',fixed:'right',  btnList:[
            {
                label:'查看',
                handle:(that,row)=>that.zrApplyInner(row,false),                
            },
            {
                label:'审核',
                handle:(that,row)=>that.zrApplyInner(row,true),
                ishide:(that,row)=>{ return row.applyState!=1; }
            },
        ]} 
    ]

   

    export default {
        name: 'DeductZrMemberApplyList4Cg',
        components: { cesTable, container, MyConfirmButton, MySearch, MySearchWindow, OrderActionsByInnerNos },
        props: {
            filter: {},
        },
        data() {
            return {
                dialogHisVisible: false,
                orderNo: '',
                that: this,
                list: [],
                platformList: [],
                brandlist: [],
                illegalTypeList: [],
                summaryarry: {},
                pager: { OrderBy: "ApplyTime", IsAsc: false },              
                pickerOptions: {
                    disabledDate(time) {
                        return time.getTime() > Date.now();
                    }
                },
                tableCols: tableCols,
                total: 0,
                sels: [],
                listLoading: false,
                pageLoading: false,
                dialogVisible: false,
                uploadLoading: false,
                uploadLoading: false,               
                selids: [],
                selrows: [],
            };
        },
        async mounted() {
            await this.setPlatform()
            await this.onSearch()
        },
        methods: {           
            //内部申诉 
            async zrApplyInner(row,isAuditor) {
                let self = this;
                this.$showDialogform({
                    path: `@/views/order/orderillegal/OrderDeductZrMemberApplyAuditForm.vue`,
                    title: '内部责任申诉',
                    autoTitle: false,
                    args: { ...row, isAuditor:isAuditor, mode: 3 },
                    height: 300,
                    width: '80%',
                    callOk: self.onSearch
                })
            },
            showLogDetail(row) {
                this.dialogHisVisible = true;
                this.orderNo = row.orderNo;
            },
            //设置平台,扣款因下拉
            async setPlatform() {
                var pfrule = await rulePlatform();
                this.platformList = pfrule.options;
                var ilrule = await ruleIllegalType();
                this.illegalTypeList = ilrule.options;
            },
            //查询第一页
            async onSearch() {
                if (!this.filter.timerange) { this.$message({ message: "请选择日期", type: "warning", }); return; }
                this.$refs.pager.setPage(1)
                await this.getlist();
            },
            //获取查询条件
            getCondition() {                

                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }

                return params;
            },
            //分页查询
            async getlist() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true
                const res = await PageDeductZrMemberAppealList(params)
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;        
               
                this.list = data

            },
            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selectchange: function (rows, row) {
                console.log(rows)
                this.selrows = rows;

                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
            //导出
            async onExportDetail() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                console.log(params, 'params')
                var res = await exportOrderWithholdListNew(params);
                loadingInstance.close();
                console.log(res, 'res')
                if (!res?.data) return
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '采购-拼多多扣款详情_' + new Date().toLocaleString() + '.xlsx')
                aLink.click();
            },
        },
    };
</script>

<style lang="scss" scoped></style>
