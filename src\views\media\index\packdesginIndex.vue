<template>
    <div>
      <div class="sydhsx">
        <div>
          <div style="width: 625px; margin: 0px auto">
            <el-checkbox-group v-model="mainviewTaskcheckList">
              <el-checkbox style="margin: 5px 6px" label="0">任务列表</el-checkbox>
              <el-checkbox style="margin: 5px 6px" label="1">完成设计</el-checkbox>
              <el-checkbox style="margin: 5px 6px" label="2">确认信息</el-checkbox>
              <el-checkbox style="margin: 5px 6px" label="3">已使用</el-checkbox>
              <el-checkbox style="margin: 5px 6px" label="4">统计列表</el-checkbox>
              <el-checkbox style="margin: 5px 6px" label="5">存档信息</el-checkbox>
              <el-checkbox style="margin: 5px 6px" label="6">存档</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div style="width: 680px; margin: 8px auto">
          <el-select  size="mini" v-model="mainviewfilter.platform"  style="width: 120px"   @change="onchangeplatfor"  placeholder="平台"   clearable>
            <el-option v-for="item in platformList" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
          </el-select>
          <el-select  size="mini" v-model="mainviewfilter.shop" style="width: 200px"    placeholder="店铺"  clearable>
            <el-option v-for="item in shopListmain" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"> </el-option>
          </el-select>
          <el-date-picker size="mini" style="position: relative; top: 1px; width: 35%" type="daterange"
              align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
              :picker-options="pickerOptions" v-model="mainviewfilter.timerange" clearable> </el-date-picker>
          <el-button style="position: relative; top: 2px; height: 28px" size="mini" type="primary" @click="initsearch">查询</el-button>
        </div>
      </div>

      <div class="sybj">
        <div class="tjnrk1">
          <div class="tjnrnk">
            <div class="tjbt">
              <span>任务状态</span>
            </div>
            <div class="nrqk" style="height: 230px">
              <div   style="width:550px;margin: 0 auto; position: relative;  top: 6px;" >
                <div class="sztjk">
                  <div class="tjsz">{{taskinfo}}</div>
                  <div class="tjmc">任务条数</div>
                </div>
                <div class="sztjk">
                  <div class="tjsz">{{taskover}}</div>
                  <div class="tjmc">完成设计</div>
                </div>
                <div class="sztjk">
                  <div class="tjsz">{{taskqr}}</div>
                  <div class="tjmc">确认信息</div>
                </div>
                <div class="sztjk">
                  <div class="tjsz">{{tasksy}}</div>
                  <div class="tjmc">已使用</div>
                </div>
                <div class="sztjk">
                  <div class="tjsz">{{taskinfototal}}</div>
                  <div class="tjmc">任务总数</div>
                </div>
                <div class="sztjk">
                  <div class="tjsz">{{taskovertotal}}</div>
                  <div class="tjmc">设计总数</div>
                </div>
                <div class="sztjk">
                  <div class="tjsz">{{taskqrtotal}}</div>
                  <div class="tjmc">确认总数</div>
                </div>
                <div class="sztjk">
                  <div class="tjsz">{{tasksytotal}}</div>
                  <div class="tjmc">使用总数</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="tjnrk2">
          <div class="tjnrnk">
            <div class="tjbt">
              <span>部门公告</span>
            </div>
            <div style="height: 230px" class="nrqk">
              <div  style="width: 500px;  margin: 0 auto;  position: relative;  top: 15px;  background-color: rgb(255, 0, 0); " ></div>
            </div>
          </div>
        </div>

        <div class="tjnrk3">
          <div class="tjnrnk">
            <div class="tjbt">
              <span>任务进度</span>
            </div>
            <div   class="nrqk">
              <div style="height: 5px"></div>
              <div  id="packdesginsxtjtb"  style="width: 55vw; height: 355px; margin: 0 auto" ></div>
            </div>
          </div>
        </div>

        <div class="tjnrk5">
          <div class="tjnrnk">
            <div class="tjbt">
              <span>印刷方式</span>
            </div>
            <div style="height: 360px" class="nrqk">
              <div id="packdesginlxtjtb"  style="width: 28vw; height:355px; margin: 0 auto"></div>
            </div>
          </div>
        </div>

        <div class="tjnrk7">
          <div class="tjnrnk">
            <div class="tjbt">
              <span>包装类型</span>
            </div>
            <div  class="nrqk">
                <div style="height:5px"></div>
                <div  id="packdesginbzlxtj"  style="width: 55vw; height:355px; margin: 0 auto" ></div>
            </div>
          </div>
        </div>

        <div class="tjnrk6">
          <div class="tjnrnk">
            <div class="tjbt">
              <span>品牌统计</span>
            </div>
            <div class="nrqk">
                <div style="height:5px"></div>
              <div  id="packdesginpptjtb" style="width: 28vw; height:355px; margin: 0 auto" ></div>
            </div>
          </div>
        </div>

        <div class="tjnrk8">
          <div class="tjnrnk">
            <div class="tjbt">
              <span>任务统计</span>
            </div>
            <div style="height: 360px" class="nrqk">
              <div style="height:5px ; "></div>
              <div style="padding: 8px 25px; color: #666">
               <!--  <span>设计总数：</span>
                <span style="margin-right: 20px">5</span>
                <span>完成总数：</span>
                <span style="margin-right: 20px">5</span>
                <span>剩余总数：</span>
                <span style="margin-right: 20px">5</span>
                <span>平均工时：</span>
                <span style="margin-right: 20px">5</span> -->
              </div>
              <div   id="packdesginrwtjtb" style="width: 87vw; height:360px;margin: 0 auto" ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>

  <script>
  import * as echarts from "echarts";
  import { getList as getshopList } from '@/api/operatemanage/base/shop';
  import { rulePlatform } from "@/utils/formruletools";
  import {  getErpUserInfoView as getShootingViewPersonAsync} from '@/api/media/mediashare';
  import {getPackDesginTaskJdStatistics,getPackDesginYsFsStatistics ,getPackDesginPackClassStatistics,getPackDesginPingPaiStatistics
    ,getPackDesginTaskFpStatistics, getPackDesginTaskJdDayStatistics,getPackDesginPackClassDayStatistics,getPackDesginTaskTotalStatistics} from '@/api/media/packdesgin';
  export default {
    data() {
      return {
          //任务统计
          taskinfo: null,
          taskover: null,
          taskqr: null,
          tasksy: null,
          taskinfototal: null,
          taskovertotal: null,
          taskqrtotal: null,
          tasksytotal: null,
          //end
          groupNames: [],
          platformList: [],
          shopListmain: [],
          mainviewTaskcheckList: ['0', '1','2','3'],
          mainviewfilter: {
              startTime: null,
              endTime: null,
              platform: null,
              shop: null,
              timerange: []
          },
          pickerOptions: { disabledDate(time) { return time.getTime() > Date.now() } },
      };
    },
    async   mounted() {
      await this.onGetdrowList();
      await this.getShootingViewPer();
      this.initsearch();
    },

    methods: {
      async initsearch()
      {
        this.getTotalInfo();
        this.onechart();
        this.twochart();
        this.threechart();
        this.fourchart();
        this.fivechart();
      },
         // 格式化数值的函数
      formatNumber(value){
            const absNumber = Math.abs(value);
            const isInteger =Number.isInteger(value);
            const options ={
            minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
            maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
            useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value);     
                },  
      deleteRow(index, rows) {
        rows.splice(index, 1);
      },
      //获取下拉数据
      async onGetdrowList() {
          var pfrule = await rulePlatform();
          this.platformList = pfrule.options;
      },
      //切换平台
      async onchangeplatfor(val) {
          var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
          this.mainviewfilter.shop = null;
          this.shopListmain = res1.data.list;
          this.$forceUpdate();
      },
      //获取分配人下拉，对接人下啦
      async getShootingViewPer() {
          var res =   await getShootingViewPersonAsync();
          if(res){
            this.groupNames = res.map(item => { return item.label });
          }
      },

      //发送http请
      async  setHttpTask(index,viewname){
           //获取数据
           if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            }else{
                this.mainviewfilter.startTime =  null;
                this.mainviewfilter.endTime = null;
            }
            var params ={
                  "startTime": this.mainviewfilter.startTime,
                  "endTime":this.mainviewfilter.endTime ,
                  "checkdata":this.mainviewTaskcheckList ,
                  "shopName":this.mainviewfilter.shopName,
                  "platform":this.mainviewfilter.platform,
                  "viewName":viewname
              }
            var res =null;
            //任务进度统计图表
            if(index ==1) {
                var ret =await getPackDesginTaskJdStatistics(params);
                if(ret?.success){
                  res = ret.data
                }
            }
              //任务进度统计图表
            if(index ==11) {
                var ret =await getPackDesginTaskJdDayStatistics(params);
                if(ret?.success){
                  res = ret.data
                }
            }
            //加工定制统计图表
            if(index ==2) {
              var ret =await getPackDesginYsFsStatistics(params);
                if(ret?.success){
                  res = ret.data
                }
              }
            //品牌统计图表
            if(index ==3) {
              var ret =await getPackDesginPingPaiStatistics(params);
                if(ret?.success){
                  res = ret.data
                }
              }

            //任务统计图表
            if(index ==4) {
              var ret =await getPackDesginTaskFpStatistics(params);
                if(ret?.success){
                  res = ret.data
                }
              }
            //包装类型统计图表
            if(index ==5){
              var ret =await getPackDesginPackClassStatistics(params);
                if(ret?.success){
                  res = ret.data
                }
              }
            //包装类型统计图表每日
            if(index ==51){
              var ret =await getPackDesginPackClassDayStatistics(params);
                if(ret?.success){
                  res = ret.data
                }
              }
            return res;

      },
      //任务进度统计图表
      async  onechart() {
        const ret = await this.setHttpTask(1,1);
        var myChart = echarts.init(document.getElementById("packdesginsxtjtb"));
            const option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "cross",
                        crossStyle: {
                            color: "#999",
                        },
                    },
                },
                toolbox: {
                    show: true,
                    orient: 'vertical',
                    left: 'right',
                    top: 'center',
                    feature: {
                        mark: { show: true },
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ['line', 'bar', 'stack'] },
                        restore: { show: true },
                        saveAsImage: { show: true }
                    }
                },
                xAxis: [
                    {
                        type: "category",
                        data:ret.xAxis,
                        axisPointer: {
                            type: "shadow",
                        },
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value}",
                        },
                    },
                ],
                grid: {
                    top: "40px",
                    bottom: "50px",
                    left: "60",
                    right: "65",
                },
                series: [
                    {
                        name: "",
                        type: "bar",
                        id: "sales",
                        barGap: "5%",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            } 
                        },
                        emphasis: {
                            focus: "series",
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value) + "";
                            },
                        },
                        data:ret.series,
                    },

                ],
                graphic: [
                    {
                        type: "text",
                        left: "right",
                        top: 12,
                        style: {
                            text: "",
                            fontSize: 14,
                        },

                    }
                ],
            };
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            }          
        myChart.on("click",  async params =>  {
          if ((params&& params.name =="") ||  (!ret.xAxis.includes(params.name))) {
            return
          };
          var childret = await this.setHttpTask(11,params.name);
            myChart.setOption({

              xAxis: {
                data:  childret.xAxis
              },
              series: [
                {

                  type: "line",
                  data:  childret.series[0],
                  universalTransition: {
                    enabled: true,
                    divideShape: "clone",
                  },
                },
              ],
              graphic: [
                {
                  type: "text",
                  left: "right",
                  top: 12,
                  style: {
                    text: "Back",
                    fontSize: 14,
                  },
                  onclick: function () {
                    myChart.clear();
                    myChart.setOption(option);
                  },
                },
              ],
            });
        });
        myChart.setOption(option);
      },
      async getTotalInfo(){
         //获取数据
         if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            }else{
                this.mainviewfilter.startTime =  null;
                this.mainviewfilter.endTime = null;
            }
            var params ={
                  "startTime": this.mainviewfilter.startTime,
                  "endTime":this.mainviewfilter.endTime ,
                  "checkdata":this.mainviewTaskcheckList ,
                  "shopName":this.mainviewfilter.shopName,
                  "platform":this.mainviewfilter.platform,
              }
            var ret =await getPackDesginTaskTotalStatistics(params);
            if(ret?.success){
              this.taskinfo =this.formatNumber(ret.data.taskinfo);
              this.taskover =this.formatNumber(ret.data.taskover);
              this.taskqr =this.formatNumber(ret.data.taskqr);
              this.tasksy =this.formatNumber(ret.data.tasksy);
              this.taskinfototal =this.formatNumber(ret.data.taskinfototal);
              this.taskovertotal =this.formatNumber(ret.data.taskovertotal);
              this.taskqrtotal =this.formatNumber(ret.data.taskqrtotal);
              this.tasksytotal =this.formatNumber(ret.data.tasksytotal);
            }
      },
      //任务进度统计图表
      //加工定制统计图表
      async twochart() {
        var ret = await this.setHttpTask(2,1);
        var myChart = echarts.init(document.getElementById("packdesginlxtjtb"));
        var option = {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross",
              crossStyle: {
                color: "#999",
              },
            },
          },
          toolbox: {
            show: true,
            orient: "vertical",
            left: "right",
            top: "center",
            feature: {
              mark: { show: true },
              dataView: { show: true, readOnly: false },
              magicType: { show: true, type: ["line", "bar", "stack"] },
              restore: { show: true },
              saveAsImage: { show: true },
            },
          },
          legend: {
            top: 5,
            data:["款数","占比"],
            selected: {  "占比":false},
          },
          xAxis: [
            {
              type: "category",
              data: ret.xAxis,
              axisPointer: {
                type: "shadow",
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              name: "",
              axisLabel: {
                formatter: "{value} ",
              },
            },

            {
              type: "value",
              name: "",
              axisLabel: {
                formatter: "{value} ",
              },
            },
          ],
          grid: { top: "55px", bottom: "48px", left: "60", right: "60" },
          series: [
            {
              name: "款数",
              type: "bar",
              label: {
                show: true,
                position: "top",
                formatter: function (params) {
                   return formatValue(params.value);
                } 
              },
              tooltip: {
                valueFormatter: function (value) {
                  return formatValue(value) + "";
                },
              },
              data:ret.series[0],
            },

            {
              name: "占比",
              type: "line",
              label: {
                show: true,
                position: "top",
                formatter: function (params) {
                   return formatValue(params.value);
                }  
              },
              tooltip: {
                valueFormatter: function (value) {
                  return formatValue(value) + "%";
                },
              },
              data:ret.series[1],
            },
          ],
        };
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            }      
        myChart.setOption(option);
      },
      //加工定制统计图表
      //品牌统计图表

      async threechart() {
        var ret = await this.setHttpTask(3,1);
        var totalseries=[];
        ret.series.forEach((item,index)=>{
              var curjosn  = {
              name:index==0?"款数":"占比",
              type: index==1?"line":"bar",
              label: {
                show: true,
                position: "top",
                formatter: function (params) {
                   return formatValue(params.value);
                }                 
              },
              tooltip: {
                valueFormatter: function (value) {
                  return formatValue(value) +(index==1?"%":"") ;
                },
              },
              data:item,
            };
            totalseries.push(curjosn);
        });
        var myChart = echarts.init(document.getElementById("packdesginpptjtb"));
        var option = {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross",
              crossStyle: {
                color: "#999",
              },
            },
          },
          toolbox: {
            show: true,
            orient: "vertical",
            left: "right",
            top: "center",
            feature: {
              mark: { show: true },
              dataView: { show: true, readOnly: false },
              magicType: { show: true, type: ["line", "bar", "stack"] },
              restore: { show: true },
              saveAsImage: { show: true },
            },
          },
          xAxis: [
            {
              type: "category",
              data: ret.xAxis,
              axisPointer: {
                type: "shadow",
              },
            },
          ],
          legend: {
            top: 5,
            data:["款数","占比"],
            selected: {  "占比":false},
          },
          yAxis: [
            {
              type: "value",
              name: "",
              axisLabel: {
                formatter: "{value} ",
              },
            },

            {
              type: "value",
              name: "",
              axisLabel: {
                formatter: "{value} ",
              },
            },
          ],
          grid: { top: "55px", bottom: "48px", left: "60", right: "60" },
          series: totalseries
        };
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            } 
        myChart.setOption(option);
      },
      //品牌统计图表

      //任务统计图表
      async fourchart() {
        var ret = await this.setHttpTask(4,1);
        var totalseries=[];
        ret.series.forEach((item,index)=>{
              var curjosn  = {
              name: ret.legends[index],
              type: index==4?"line":"bar",
              label: {
                show: true,
                position: "top",
                formatter: index==4?'{c}%':'{c}'
              },
              tooltip: {
                valueFormatter: function (value) {
                  return value + "";
                },
              },
              data:item,
            };
            totalseries.push(curjosn);
        });
        var myChart = echarts.init(document.getElementById("packdesginrwtjtb"));
        var option = {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross",
              crossStyle: {
                color: "#999",
              },
            },
          },
          toolbox: {
            show: true,
            orient: "vertical",
            left: "right",
            top: "center",
            feature: {
              mark: { show: true },
              dataView: { show: true, readOnly: false },
              magicType: { show: true, type: ["line", "bar", "stack"] },
              restore: { show: true },
              saveAsImage: { show: true },
            },
          },
          legend: {
            top: 5,
            data:ret.legends,
            selected: {  "完成率":false},
          },
          xAxis: [
            {
              type: "category",
              data:  ret.xAxis,
              axisPointer: {
                type: "shadow",
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              name: "",
              axisLabel: {
                formatter: "{value} ",
              },
            },
            {
              type: "value",
              name: "",
              axisLabel: {
                formatter: "{value} ",
              },
            },
          ],

          grid: {
            top: "55px",
            bottom: "80px",
            left: "60",
            right: "65",
          },
        
          series: totalseries,
        }; 
        myChart.setOption(option);
      },
      //任务统计图表

      //包装类型统计图表
      async fivechart() {
        var ret = await this.setHttpTask(5,1);
        var myChart = echarts.init(document.getElementById("packdesginbzlxtj"));
        var option = {
          tooltip: {  trigger: "axis",
              axisPointer: {
              type: "cross",
              crossStyle: {
                color: "#999",
              },
            },
          },
          toolbox: {
            show: true,
            orient: "vertical",
            left: "right",
            top: "center",
            feature: {
              mark: { show: true },
              dataView: { show: true, readOnly: false },
              magicType: { show: true, type: ["line", "bar", "stack"] },
              restore: { show: true },
              saveAsImage: { show: true },
            },
          },
          legend: {
            top: 5,
            data: [""],
          },
          xAxis: [
            {
              type: "category",
              data:  ret.xAxis,
              axisPointer: {
                type: "shadow",
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              name: "",
              axisLabel: {
                formatter: "{value} ",
              },
            },
          ],

          grid: {
            top: "40px",
            bottom: "50px",
            left: "60",
            right: "65",
          },

          series: {
              name:  "",
              type: "bar",
              barGap: "5%",
              label: {
                show: true,
                position: "top",
                formatter: function (params) {
                   return formatValue(params.value);
                }  
              },
              emphasis: {
                focus: "series",
              },
              tooltip: {
                valueFormatter: function (value) {
                  return formatValue(value) + "";
                },
              },
              data:ret.series[0],
            },
        };
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            }      
        myChart.on("click",  async params =>  {
          if ((params&& params.name =="") ||  (!ret.xAxis.includes(params.name))) {
            return
          };

          var childret = await this.setHttpTask(51,params.name);

            myChart.setOption({
              xAxis: {
                data:  childret.xAxis
              },
              series: [
                {
                  type: "line",
                  data:  childret.series[0],
                  universalTransition: {
                    enabled: true,
                    divideShape: "clone",
                  },
                },
              ],
              graphic: [
                {
                  type: "text",
                  left: "right",
                  top: 12,
                  style: {
                    text: "Back",
                    fontSize: 14,
                  },
                  onclick: function () {
                    myChart.clear();
                    myChart.setOption(option,true);
                  },
                },
              ],
            });
        },true);

        myChart.setOption(option,true);
      },
      //包装类型统计图表
    },


  };
  </script>

  <style lang="scss" scoped>
  * {
    font-size: 14px;
  }
  .sybj {
    min-width: 1100px;
    background-color: #f3f4f6;
    padding: 5px;
    height:calc(100vh - 260px);
    overflow-y: auto;
  }

  .tjldh {
    width: 785px;
    margin: 0 auto;
    box-shadow: 0px 3px 8px #cacaca;
    /* position: fixed; */
    z-index: 999;
  }

  .el-menu-demo {
    text-align: center;
  }

  .rqxz {
    width: 1000px;
    height: 80px;
    background-color: rgb(0, 54, 36);
    margin: 0 auto;
    line-height: 50px;
  }

  .tjbt {
    /* background-color: aquamarine; */
    /* font-weight: bold; */
    color: #333;
    line-height: 30px;
  }

  .sztjk {
    min-width: 75px;
    height: 50px;
    background-color: #f5faff;
    padding: 20px;
    text-align: center;
    float: left;
    margin: 5px;
    border-radius: 8px;
  }

  .sztjk .tjsz {
    font-size: 22px;
    color: #409eff;
  }
  .sztjk .tjmc {
    font-size: 14px;
    color: #409eff;
  }

  .tjnrk1 {
    width: 60%;
    /* background-color: rgb(255, 0, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
  }

  .tjnrk2 {
    width: 40%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
  }

  .tjnrk3 {
    width: 65%;
    /* background-color: rgb(255, 0, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
  }

  .tjnrk5 {
    width: 35%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
  }

  .tjnrk6 {
    width: 35%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
  }

  .tjnrk7 {
    width: 65%;
    /* background-color: rgb(255, 0, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
  }

  .tjnrk8 {
    width: 100%;
    /* background-color: rgb(255, 0, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
  }

  .tjnrnk {
    width: 100%;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 15px 20px;
    float: left;
    border-radius: 6px;
  }

  .ptsx {
    width: 85%;
    height: 90px;
    background-color: #f7f7f7;
    border-radius: 8px;
    margin: 10px auto;
    box-sizing: border-box;
    padding: 0 35px;
    line-height: 90px;
  }

  .ptsx span {
    font-size: 16px;
    color: #555;
  }

  /* .nrqk{
    background-color: #000;

  } */

  .sydh {
    width: 100%;
    min-width: 1100px;
    height: 175px;
 /*    border: 1px solid rgb(209, 209, 209);
    border-top: 0px;
    border-right: 0px;
    border-left: 0px; */
    z-index: 999;
  }
  .sydhfl {
    width: 120px;
    height: 50px;
    display: inline-block;
    /* margin: 2px; */
    text-align: center;
    line-height: 50px;
    color: #555;
    text-decoration: none;
  }
  .sydhfl :hover {
    width: 120px;
    height: 50px;
    background: linear-gradient(#f5f5f5 96%, #409eff 96%);
    display: inline-block;
    /* margin: 2px; */
    text-align: center;
    line-height: 50px;
    color: #409eff;
  }

  .sydhfl i {
    font-size: 19px;
    color: #409eff;
    margin-right: 5px;
    position: relative;
    top: 1px;
    line-height: 50px;
  }

  .sydh .fgf {
    margin: 0 5px;
    color: #a6a6a6;
    line-height: 50px;
  }

  .sydhsx {
    width: 100%;
    height: 95px;
    background-color: #f3f4f6;
    margin: 0 auto;
    box-sizing: border-box;
    padding: 15px;
/*     border: 1px solid rgb(209, 209, 209);
    border-top: 0px;
    border-right: 0px;
    border-left: 0px; */
    z-index: 999;
  }
  </style>
