<template>
 <MyContainer>
     <el-tabs v-model="activeName" @tab-click="tabclick">
         <el-tab-pane label="企业微信客服账号绑定" name="one"  v-loading="listLoading">
             <wechatbind></wechatbind>
         </el-tab-pane>
     </el-tabs>
 </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import wechatbind from './components/wechatbind.vue';

export default {
 components: { MyContainer, wechatbind },
 name: "vendorQuote",
 data() {
     return {
         activeName: 'one',//tab切换
         listLoading: true,
     };
 },
 mounted() {
     setTimeout(() => {
         this.listLoading = false
     }, 300)
 },
 methods: {
     tabclick(e) {
         this.listLoading = true
        //  this.$refs.vendorSumIndex.clear()
         //延迟加载
         setTimeout(() => {
             this.listLoading = false
         }, 500)
     }
 }
};
</script>

<style lang="scss" scoped></style>
