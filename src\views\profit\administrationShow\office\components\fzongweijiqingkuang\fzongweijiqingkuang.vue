<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <!-- <el-date-picker v-model="ListInfo.calculateMonth" unlink-panels range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期"  type="month" style="width: 250px;margin-right: 5px;" :clearable="false"
            :value-format="'yyyy-MM'" >
          </el-date-picker> -->
                <el-date-picker v-model="ListInfo.year" unlink-panels range-separator="至" placeholder="年份"
                    end-placeholder="结束日期" type="year" style="width: 250px;margin-right: 5px;" :clearable="false"
                    :value-format="'yyyy'">
                </el-date-picker>

                <!-- <el-select v-model="ListInfo.isStock" placeholder="区域" class="publicCss" clearable>
            <el-option :key="'是'" label="是" :value="0" />
            <el-option :key="'否'" label="否" :value="1" />
          </el-select> -->
                <el-select v-model="ListInfo.regionArr" multiple collapse-tags placeholder="区域" class="publicCss" clearable>
                    <el-option v-for="item in districtList" :key="item" :label="item" :value="item" />
                </el-select>

                <!-- <el-input v-model.trim="ListInfo.orderNo" placeholder="退件快递单号" maxlength="50" clearable class="publicCss" />
          <el-input v-model.trim="ListInfo.orderNoInner" placeholder="原订单号" maxlength="50" clearable class="publicCss" /> -->
                <el-button type="primary" @click="getList('search')">查询</el-button>
                <el-button type="primary" @click="startImport">导入</el-button>
                <el-button type="primary" @click="downExcel">模板下载</el-button>
                <el-button type="primary" @click="exportExcel('search')">导出</el-button>
                <!-- <el-button type="primary" @click="saveBane('search')">存档</el-button>
          <div style="color: red; margin-left: 5px;">
            存档时间：{{timeCundang??'-'}}
          </div> -->

            </div>
        </template>
        <!-- :footer-method="footerMethod"  :footer-data="footerData" -->
        <vxe-table v-show="!exportloading ? true : false" border show-footer width="100%" height="100%" ref="newtable"
            :row-config="{ height: 40 }" show-overflow :loading="loading" :column-config="{ resizable: true }"
            :footer-data="footerData" :span-method="mergeRowMethod"
            :footer-span-method="spanFooterMethod"
            :row-class-name="rowClassName" :footer-cell-class-name="footerCellClassName" :data="tableData">
            <vxe-column field="region" width="90" title="区域"></vxe-column>
            <vxe-column field="year" width="90" title="年份"></vxe-column>
            <vxe-column field="violationType" width="150" title="违纪类型"></vxe-column>
            <vxe-column field="specificViolations" width="150" title="具体违纪情况"></vxe-column>
            <vxe-colgroup title="1月份">
              <vxe-column field="jan" width="50" title="次数"></vxe-column>
              <vxe-column field="janMoney" width="50" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="2月份">
              <vxe-column field="feb" width="50" title="次数"></vxe-column>
              <vxe-column field="febMoney" width="50" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="3月份">
              <vxe-column field="mar" width="50" title="次数"></vxe-column>
              <vxe-column field="marMoney" width="50" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="4月份">
              <vxe-column field="apr" width="50" title="次数"></vxe-column>
              <vxe-column field="aprMoney" width="50" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="5月份">
              <vxe-column field="may" width="50" title="次数"></vxe-column>
              <vxe-column field="mayMoney" width="50" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="6月份">
              <vxe-column field="jun" width="50" title="次数"></vxe-column>
              <vxe-column field="junMoney" width="50" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="7月份">
              <vxe-column field="jul" width="50" title="次数"></vxe-column>
              <vxe-column field="julMoney" width="50" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="8月份">
              <vxe-column field="aug" width="50" title="次数"></vxe-column>
              <vxe-column field="augMoney" width="50" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="9月份">
              <vxe-column field="sept" width="50" title="次数"></vxe-column>
              <vxe-column field="septMoney" width="50" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="10月份">
              <vxe-column field="oct" width="50" title="次数"></vxe-column>
              <vxe-column field="octMoney" width="50" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="11月份">
              <vxe-column field="nov" width="50" title="次数"></vxe-column>
              <vxe-column field="novMoney" width="50" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="12月份">
              <vxe-column field="dec" width="50" title="次数"></vxe-column>
              <vxe-column field="decMoney" width="50" title="金额"></vxe-column>
            </vxe-colgroup>
        </vxe-table>
        <vxe-table v-show="!exportloading ? false : true" border show-footer width="100%" height="100%" ref="exportTable"
            :row-config="{ height: 40 }" show-overflow :loading="loading" :column-config="{ resizable: true }"
            :footer-data="footerData" :span-method="mergeRowMethod"
            :footer-span-method="spanFooterMethod" :footer-method="footerMethod"
            :row-class-name="rowClassName" :footer-cell-class-name="footerCellClassName" :data="tableData">
            <vxe-column field="region" width="90" title="区域"></vxe-column>
            <vxe-column field="year" width="90" title="年份"></vxe-column>
            <vxe-column field="violationType" width="150" title="违纪类型"></vxe-column>
            <vxe-column field="specificViolations" width="150" title="具体违纪情况"></vxe-column>
            <vxe-colgroup title="1月份">
              <vxe-column field="jan" width="100" title="次数"></vxe-column>
              <vxe-column field="janMoney" width="100" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="2月份">
              <vxe-column field="feb" width="100" title="次数"></vxe-column>
              <vxe-column field="febMoney" width="100" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="3月份">
              <vxe-column field="mar" width="100" title="次数"></vxe-column>
              <vxe-column field="marMoney" width="100" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="4月份">
              <vxe-column field="apr" width="100" title="次数"></vxe-column>
              <vxe-column field="aprMoney" width="100" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="5月份">
              <vxe-column field="may" width="100" title="次数"></vxe-column>
              <vxe-column field="mayMoney" width="100" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="6月份">
              <vxe-column field="jun" width="100" title="次数"></vxe-column>
              <vxe-column field="junMoney" width="100" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="7月份">
              <vxe-column field="jul" width="100" title="次数"></vxe-column>
              <vxe-column field="julMoney" width="100" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="8月份">
              <vxe-column field="aug" width="100" title="次数"></vxe-column>
              <vxe-column field="augMoney" width="100" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="9月份">
              <vxe-column field="sept" width="100" title="次数"></vxe-column>
              <vxe-column field="septMoney" width="100" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="10月份">
              <vxe-column field="oct" width="100" title="次数"></vxe-column>
              <vxe-column field="octMoney" width="100" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="11月份">
              <vxe-column field="nov" width="100" title="次数"></vxe-column>
              <vxe-column field="novMoney" width="100" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="12月份">
              <vxe-column field="dec" width="100" title="次数"></vxe-column>
              <vxe-column field="decMoney" width="100" title="金额"></vxe-column>
            </vxe-colgroup>
        </vxe-table>
        <el-drawer title="编辑" :visible.sync="dialogVisibleEdit" size="25%">
            <departmentEdit ref="departmentEdit" v-if="dialogVisibleEdit" :editInfo="editInfo" @search="closeGetlist"
                @cancellationMethod="dialogVisibleEdit = false" />
        </el-drawer>
        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                    :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { downloadLink } from "@/utils/tools.js";
import { violationDisciplinePage, dimissionManageArchive, violationDisciplineImport, violationDisciplineRemove } from '@/api/people/peoplessc.js';
import departmentEdit from "./departmentEdit.vue";
import checkPermission from '@/utils/permission'
const tableCols = [
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '审批状态', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNoInner', label: '商品编码', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'importTime', label: '商品名称', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'planSendTime', label: '原价', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'timePay', label: '现价', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight', label: '进货数量', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderStatus', label: '涨价原因', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'seriesName', label: '添加日期', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCount', label: '涨价日期', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '添加人', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '岗位', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '分公司', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人组长', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, departmentEdit
    },
    data() {
        const tableData = []
        const footerData = []
        return {
            downloadLink,
            dialogVisibleEdit: false,
            editInfo: {},
            fileList: [],
            dialogVisible: false,
            districtList: [],
            timeCundang: '',
            tableData,
            footerData,
            somerow: 'costType,region,area,violationType',
            that: this,
            exportloading: false,
            ListInfo: {
                regionArr: [],
                calculateMonthArr: [
                    dayjs().subtract(1, 'month').format('YYYY-MM'),
                    dayjs().subtract(1, 'month').format('YYYY-MM')],
                year: dayjs().subtract(0, 'year').format('YYYY')
            },
            timeRanges: [],
            tableCols,
            summaryarry: {},
            total: 0,
            loading: false,
            pickerOptions,
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        footerMethod({ columns, data }) {
            const sums = [];
            if (!this.footerData)
                return sums
            let newfield = columns.map(item => item.field)
            let newfooterdata = [];
            this.footerData.forEach((item, index) => {
                let newarr2 = [];
                newfield.forEach((item2, index2) => {
                    newarr2.push(item[item2])
                })
                newfooterdata.push(newarr2)
            })

            return newfooterdata;
        },
        rowClassName(event) {

            if (event.row.violationType == '考勤小计' ||
                event.row.violationType == '纪律小计' ||
                event.row.violationType == '违纪次数合计'
            ) {
                return 'row-green'
            }
            if (event.row.violationType == '违纪人数合计' ||
                event.row.violationType == '人均违纪次数' ||
                event.row.violationType == '当月考勤人数'
            ) {
                return 'row-yellow1'
            }
            if (event.row.violationType == '当月违纪人数占比'
            ) {
                return 'row-yellow2'
            }

            return null
        },
        footerCellClassName(event) {
            console.log(event, "底部====")
            if (event.row.violationType == '考勤总计' ||
                event.row.violationType == '纪律总计' ||
                event.row.violationType == '违纪次数合计'
            ) {
                return 'row-yellow3'
            }
            if (event.row.violationType == '违纪人数合计' ||
                event.row.violationType == '人均违纪次数' ||
                event.row.violationType == '当月考勤人数'
            ) {
                return 'row-yellow4'
            }
            if (event.row.violationType == '当月违纪人数占比'
            ) {
                return 'row-yellow5'
            }

            return null
        },
        //上传文件
        onUploadRemove(file, fileList) {
            this.fileList = []
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            this.dialogVisible = false;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("file", item.file);
            form.append("isArchive", checkPermission("ArchiveStatusEditing"));
            form.append("calculateMonth", this.ListInfo.calculateMonth);
            var res = await violationDisciplineImport(form);
            if (res?.success) {
                this.$message({ message: res.msg, type: "success" });
            }
            this.uploadLoading = false
            this.dialogVisible = false;
            await this.getList()
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        //导入弹窗
        startImport() {
            this.fileList = []
            this.dialogVisible = true;
        },
        downExcel() {
            //下载excel
            downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250611/1932723978150264833.xlsx', '总违纪情况导入模板.xlsx');
        },
        async saveBane() {
            this.$confirm('是否存档？存档后不可修改！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { data, success } = await dimissionManageArchive(this.ListInfo)
                if (!success) {
                    return;
                }
                this.getList();
                this.$message.success('保存存档成功！')

            }).catch(() => {
                // this.$message.error('取消')
            });
        },
        async exportExcel() {
            await this.$nextTick(() => {
                this.exportloading = true;
                this.$refs.newtable.exportData({ filename: `总违纪情况${dayjs().format('YYYY-MM-DD HH:mm:ss')}`, sheetName: 'Sheet1', type: 'xlsx' })
            })
            await this.$nextTick(() => {
                this.exportloading = false;
            })
        },
        closeGetlist() {
            this.dialogVisibleEdit = false;
            this.getList()
        },
        handleEdit(index, row) {
            this.editInfo = row;
            this.dialogVisibleEdit = true;
        },
        async handleRemove(index, row) {
            this.$confirm('是否删除！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.editInfo = row;
                // this.editInfo.calculateMonthStart = this.ListInfo.calculateMonthArr[0]
                // this.editInfo.calculateMonthEnd = this.ListInfo.calculateMonthArr[1]
                this.loading = true
                const { data, success } = await violationDisciplineRemove({ ids: row.id })
                this.loading = false
                if (success) {
                    this.$message.success('删除成功')
                    this.getList();
                } else {
                    this.$message.error('删除失败')
                }
            }).catch(() => {

            });
        },
        // 通用行合并函数（将相同多列数据合并为一行）
        mergeRowMethod({ row, _rowIndex, column, visibleData }) {
            const fields = this.somerow.split(',')
            const cellValue = row[column.property]
            const specialTypes = ['违纪人数合计', '人均违纪次数', '当月考勤人数', '当月违纪人数占比'];

            // 处理特殊行类型的月份列合并
            if (specialTypes.includes(row.violationType)) {
                // 处理所有月份的次数和金额列合并
                const monthMap = {
                    'jan': 'janMoney',
                    'feb': 'febMoney',
                    'mar': 'marMoney',
                    'apr': 'aprMoney',
                    'may': 'mayMoney',
                    'jun': 'junMoney',
                    'jul': 'julMoney',
                    'aug': 'augMoney',
                    'sept': 'septMoney',
                    'oct': 'octMoney',
                    'nov': 'novMoney',
                    'dec': 'decMoney'
                };

                // 如果是金额列，隐藏它
                if (Object.values(monthMap).includes(column.property)) {
                    return { rowspan: 0, colspan: 0 };
                }

                // 如果是次数列，扩展到金额列
                if (Object.keys(monthMap).includes(column.property)) {
                    return { rowspan: 1, colspan: 2 };
                }
            }

            // 原有的行合并逻辑
            if (cellValue && fields.includes(column.property)) {
                const prevRow = visibleData[_rowIndex - 1]
                let nextRow = visibleData[_rowIndex + 1]
                if (prevRow && prevRow[column.property] === cellValue) {
                    return { rowspan: 0, colspan: 0 }
                } else {
                    let countRowspan = 1
                    while (nextRow && nextRow[column.property] === cellValue) {
                        nextRow = visibleData[++countRowspan + _rowIndex]
                    }
                    if (countRowspan > 1) {
                        return { rowspan: countRowspan, colspan: 1 }
                    }
                }
            }
        },
        // 添加处理汇总行合并单元格的方法
        spanFooterMethod({ row, column, rowIndex }) {
            const specialTypes = ['违纪人数合计', '人均违纪次数', '当月考勤人数', '当月违纪人数占比'];

            if (specialTypes.includes(row.violationType)) {
                // 处理所有月份的次数和金额列合并
                const monthMap = {
                    'jan': 'janMoney',
                    'feb': 'febMoney',
                    'mar': 'marMoney',
                    'apr': 'aprMoney',
                    'may': 'mayMoney',
                    'jun': 'junMoney',
                    'jul': 'julMoney',
                    'aug': 'augMoney',
                    'sept': 'septMoney',
                    'oct': 'octMoney',
                    'nov': 'novMoney',
                    'dec': 'decMoney'
                };

                // 如果是金额列，隐藏它
                if (Object.values(monthMap).includes(column.property)) {
                    return { rowspan: 0, colspan: 0 };
                }

                // 如果是次数列，扩展到金额列
                if (Object.keys(monthMap).includes(column.property)) {
                    return { rowspan: 1, colspan: 2 };
                }
            }
            return null;
        },
        async changeTime(e) {
            this.ListInfo.startTime = e ? e[0] : null
            this.ListInfo.endTime = e ? e[1] : null
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        // async exportProps() {
        //     const { data } = await exportStatData(this.ListInfo)
        //     const aLink = document.createElement("a");
        //     let blob = new Blob([data], { type: "application/vnd.ms-excel" })
        //     aLink.href = URL.createObjectURL(blob)
        //     aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
        //     aLink.click()
        // },
        async getList(type) {
            // if (type == 'search') {
            //   this.ListInfo.currentPage = 1
            //   this.$refs.pager.setPage(1)
            // }
            if (this.ListInfo.regionArr && this.ListInfo.regionArr.length > 0) {
                this.ListInfo.region = this.ListInfo.regionArr.join(',')
            }
            this.loading = true
            const { data, success } = await violationDisciplinePage(this.ListInfo)
            if (success) {
                //   data.list.map((row)=>{
                //     row.twoYearRate =  row.twoYearRate === 100 ? "100%" : row.twoYearRate ? Number(row.twoYearRate).toFixed(2) + "%" : ''
                //     row.notAdaptedWorkRate =  row.notAdaptedWorkRate === 100 ? "100%" : row.notAdaptedWorkRate ? Number(row.notAdaptedWorkRate).toFixed(2) + "%" : ''
                //     row.hasWorkRate =  row.hasWorkRate === 100 ? "100%" : row.hasWorkRate ? Number(row.hasWorkRate).toFixed(2) + "%" : ''

                //     row.familyReasonsRate =  row.familyReasonsRate === 100 ? "100%" : row.familyReasonsRate ? Number(row.familyReasonsRate).toFixed(2) + "%" : ''
                //     row.bodyReasonsRate =  row.bodyReasonsRate === 100 ? "100%" : row.bodyReasonsRate ? Number(row.bodyReasonsRate).toFixed(2) + "%" : ''
                //     row.eliminateRate =  row.eliminateRate === 100 ? "100%" : row.eliminateRate ? Number(row.eliminateRate).toFixed(2) + "%" : ''
                //   })
                let aaa = ['specificViolations', 'violationType', 'region','year'];
                data.list.map((item) => {
                    if (item.violationType.indexOf('占比') > -1) {
                        Object.keys(item).map((index) => {
                            aaa.indexOf(index) > -1 || item[index] == 0 || !item[index] ? '' : item[index] = item[index] + '%'
                        })
                    }
                })


                this.tableData = data.list
                //   this.total = data.total
                //   this.summaryarry = data.summary
                // data.summary.regionName = '合计';
                // data.summary.calculateMonth = data.list.length>0? data.list[0].calculateMonth : '';
                // this.timeCundang = data.list.length>0?data.list[0].archiveTime:''
                // if (data.summary) {
                //   this.timeCundang = data.summary.archiveTime
                // }

                // let zhanbi = ['eliminateRate','bodyReasonsRate','familyReasonsRate','hasWorkRate','notAdaptedWorkRate'];

                // zhanbi.map((item)=>{
                //     data.summary[item] = data.summary[item]?data.summary[item]+'%': ''
                // })


                data.summary.map((item) => {
                    if (item.violationType.indexOf('占比') > -1) {
                        Object.keys(item).map((index) => {
                            aaa.indexOf(index) > -1 || item[index] == 0 || !item[index] ? '' : item[index] = item[index] + '%'
                        })
                    }
                })

                this.footerData = data.summary
                // const fieldsToFormat = [
                //     "ywCost",
                //     "whCost",
                //     "totalCount",
                //     "szCost",
                //     "ncCost"
                // ];
                // this.footerData.forEach((item) => {
                //     fieldsToFormat.forEach((field) => {
                //         if (item[field] !== null && item['ledgerDate'] == '区域总占比') {
                //             item[field] = item[field] + '%'
                //             return;
                //         }
                //         if (item[field] !== null && item[field] !== undefined) {
                //             item[field] = this.formatNumberWithThousandSeparator(item[field]);
                //         }
                //     });
                //     // zhanbi.indexOf()

                // });
                console.log("111111", this.footerData)
                //

                //取列表中的区域
                const newDistricts = this.tableData.map(item => item.region).filter(district => district !== undefined && district !== null && district.indexOf('小计') == -1 && district.indexOf('占比') == -1)
                this.districtList = Array.from(new Set([...this.districtList, ...newDistricts]));

                this.loading = false
            } else {
                this.loading = false
                this.$message.error('获取列表失败')
            }
        },
        formatNumberWithThousandSeparator(value) {
            if (value === null || value === undefined) return value;
            return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 150px;
        margin-right: 5px;
    }
}

:deep(.vxe-header--column) {
    background: #00937e;
    color: white;
    font-weight: 600;
}

// :deep(.vxe-footer--row){
//     background: #00937e;
//     color: white;
//     font-weight: 600;
// }
:deep(.row-green) {
    background-color: #e0eed6;
    // color: #fff;
}

:deep(.row-yellow1) {
    background-color: #f8e2d3;
}

:deep(.row-yellow2) {
    background-color: #d1f1ef;
    // color: #fff;
}

:deep(.row-yellow3) {
    background-color: #f7d7dc;
}

:deep(.row-yellow4) {
    background-color: #f9e6e9;
}

:deep(.row-yellow5) {
    background-color: #fcfc01;
    color: red;
}

//取消表格横向滚动条
:deep(.vxe-table--body-wrapper) {
   overflow-x: hidden !important;
}

:deep(.vxe-table--footer-wrapper) {
   overflow-x: hidden !important;
}

:deep(.vxe-table) {
   overflow-x: hidden !important;
}
</style>