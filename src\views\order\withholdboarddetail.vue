<template>
    <my-container v-loading="pageLoading">
      <template>             
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
          :tableData='list'  :tableCols='tableCols' :isSelection="false" @select="selectchange" :isSelectColumn='false'
          :tableHandles='tableHandles' @cellclick="cellclick"
          :loading="listLoading">
        </ces-table>
    </template>
    <template #footer>
          <my-pagination
            ref="pager"
            :total="total"
            :checked-count="sels.length"
            @get-page="getlist"
          />
        </template>       
    </my-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import {formatLinkProCode, formatSendWarehouse} from "@/utils/tools";
import { GetWithholdboardDetail,} from "@/api/order/orderdeductmoney"
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

const tableCols =[
        {istrue:true,prop:'dayTime',label:'扣款时间', width:'120',sortable:'custom',},
        {istrue:true,prop:'proCode',label:'宝贝ID', width:'120',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
        {istrue:true,prop:'platform',label:'平台', width:'80',sortable:'custom',formatter:(row)=> !row.platformName?" " : row.platformName}, 
        {istrue:true,prop:'orderNo',label:'订单号', width:'200',sortable:'custom',formatter:(row)=> !row.orderNo? "" : row.orderNo},       
        {istrue:true,prop:'orderNoInner',label:'内部订单号', width:'200'},        
        {istrue:true,prop:'realpay',label:'扣款金额', width:'100',sortable:'custom',formatter:(row)=>parseFloat(row.realpay.toFixed(2))},
        {istrue:true,prop:'errorType',label:'扣款原因', width:'auto',sortable:'custom',formatter:(row)=> !row.errorType?" " : row.errorType}
]

const tableHandles=[
              
      ];

export default {
    name: 'YunhanAdminWithholdboarddetail',
    components: {cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, },
    data() {
        return {
            that:this,
            filter:{
                
            },
            list: [],
            platformList: [],
            illegalTypeList : [],
            summaryarry:{},
            pager:{OrderBy:"realpay",IsAsc:false},
            filterImport:{
            platform:1,
            occurrenceTime:formatTime(dayjs().subtract(1,"day"), "YYYY-MM-DD")
            },
            pickerOptions:{
                disabledDate(time){
                return time.getTime()>Date.now();
                }
            },           
            tableCols:tableCols,
            tableHandles:tableHandles,
            total: 0,
            sels: [], 
            listLoading: false,
            pageLoading: false,
            dialogVisible: false,
            uploadLoading: false,
            uploadLoading: false,
        };
    },

    async mounted() {
        //await this.setPlatform()
        //await this.onSearch()
        
    },

    methods: {
    //查询第一页
    async onSearch1(para) {
      this.filter = para
      await this.onSearch()
    },  
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist();
    },  
    
    //分页查询
     async getlist() {
        var pager = this.$refs.pager.getPager();
        var page  = this.pager;
        this.filter.startTime = null;
        this.filter.endTime = null;
        if (this.filter.timerange) {
            this.filter.startTime = this.filter.timerange[0];
            this.filter.endTime = this.filter.timerange[1];
        }
        const params = { ...pager,...page,... this.filter}
        if(params===false){
                return;
        }
        this.listLoading = true
        const res = await GetWithholdboardDetail(params)
        this.listLoading = false
        if (!res?.success) {
            return
        }
        this.total = res.data.total;
        const data = res.data.list;
        this.summaryarry=res.data.summary;
        // if(this.summaryarry)
        //     this.summaryarry.amountPaid_sum=parseFloat(this.summaryarry.amountPaid_sum.toFixed(6));
        // data.forEach(d => {
        //     d._loading = false
        // })
        this.list = data
       },
    //排序查询
    sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            this.onSearch();
        }, 
    selectchange:function(rows,row) {
      this.selids=[];console.log(rows)
      rows.forEach(f=>{
          this.selids.push(f.id);
      })
    },
    cellclick(row, column, cell, event){
    
    }, 
    },
};
</script>

<style lang="scss" scoped>
</style>
