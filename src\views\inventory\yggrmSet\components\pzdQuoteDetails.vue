<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss"
                    startPlaceholder="创建时间" endPlaceholder="创建时间" />
                <dateRange :startDate.sync="ListInfo.payStartTime" :endDate.sync="ListInfo.payEndTime" class="publicCss"
                    startPlaceholder="付款时间" endPlaceholder="付款时间" />
                <el-input v-model.trim="ListInfo.buyUserId" placeholder="买家ID" maxlength="200" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.orderNo" placeholder="订单号" maxlength="200" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.internalOrderNo" placeholder="内部订单号" maxlength="200" clearable
                    class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
            </div>
        </template>
        <vxetablebase :id="'tmzdQuoteDetails202408041632'" ref="table" :tableData="tableData" :tableCols="tableCols"
            :is-index="true" :that="that" style="width: 100%;  margin: 0" @sortchange='sortchange' :height="'100%'"
            :showsummary="true" class="detail" :summaryarry="summaryarry"
            :treeProp="{ rowField: 'id', parentField: 'pId', transform: true, }" :loading="loading">
            <template slot="right">
                <vxe-column title="操作" width="80" align="center" fixed="right">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <el-button type="text"
                                v-if="row.pId == '0' && checkPermission('api:inventory:customNormsGoods:ReSetPZDWeight')"
                                @click="resetWeight(row.internalOrderNo, row.orderWeight)">重置重量</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog title="重置重量" :visible.sync="resetWeightVisible" width="15%" v-dialogDrag>
            <div style="display: flex;justify-content: center;align-items: center">
                <el-input-number v-model="resetWeightInfo.weight" :min="0" :max="9999" placeholder="重量(kg)"
                    :precision="2" :controls="false" />
            </div>
            <div class="btnGroup1">
                <el-button @click="resetWeightVisible = false">取消</el-button>
                <el-button type="primary" @click="resetWeightSumbit">确定</el-button>
            </div>
        </el-dialog>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import {
    getPZDSaleOrderRecordList,
    getCostSetByTMZD,
    exportPZDSaleOrderRecordList,
    importCustomMadeMultipleAsync,
    reSetPZDWeight
} from '@/api/inventory/customNormsGoods'
import dateRange from "@/components/date-range/index.vue";
import viewLogsVue from './viewLogs.vue'
import dayjs from 'dayjs'
const tableCols = [
    { sortable: 'custom', width: '80', align: 'left', prop: 'expressCompany', label: '快递公司', treeNode: true, },
    { sortable: 'custom', width: '120', align: 'left', prop: 'internalOrderNo', label: '内部订单号', type: 'orderLogInfo', orderType: 'orderNoInner', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'actualOrderFreightBagFee', label: '快递费', },
    { sortable: 'custom', width: '70', align: 'left', prop: 'sheng', label: '省份', },
    { sortable: 'custom', width: '70', align: 'left', prop: 'shi', label: '城市', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'orderNo', label: '订单编号' },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'buyUserId', label: '买家ID', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'createdTime', label: '创建时间', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'payTime', label: '付款时间', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'norms', label: '规格', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'totalSheetCount', label: '总张数', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'orderWeight', label: '实际重量', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'realTotalCost', label: '实际成本', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'actualTotalAmount', label: '实际报价', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'orderRemark', label: '订单备注', },
    { sortable: 'custom', width: '90', align: 'center', prop: 'updatedUserName', label: '修改人', },
]

const ggList = [
    {
        label: '1.0',
        value: 0.4
    },
    {
        label: '1.5',
        value: 0.7
    },
    {
        label: '2.0',
        value: 1.1
    },
    {
        label: '3.0',
        value: 1.4
    },
    {
        label: '5.0',
        value: 2.2
    },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, uploadimgFile, viewLogsVue, dateRange
    },
    data() {
        return {
            resetWeightVisible: false,
            resetWeightInfo: {
                weight: 0,
                internalOrderNo: ''
            },
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'createdTime',
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
                buyUserId: null,//买家ID
                orderNo: null,//订单号
                createdUserName: null,//创建人
                payStartTime: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),//付款开始时间
                payEndTime: dayjs().format('YYYY-MM-DD'),//付款结束时间
                analysisError: null
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            summaryarry: {},
            Profit3Data: [
                {
                    norms: null,
                    actualLand: null,
                    length: null,
                    width: null,
                    sheet: null,
                    pf: null,
                    proportion: null,
                    kgPriceCost: null,
                    zl: null,
                    croppingCost: null,
                    dbf: null,
                    cpcb: null,
                    packProcessCost: null,
                    sjccjg: null,
                    kdf: null,
                    bhkdcb: null,
                    sj: null,
                    kd: null,
                    qtfy: null,
                    mslr: null,
                    msll: 0,
                }
            ],
            list: [
                {
                    norms: '1',
                    proportion: 2,
                    kgPriceCost: 3,
                    croppingCost: 1,
                    packProcessCost: 4,
                }
            ],
            ggList,
            drawer1: false,
            isExport: false,
            importVisible: false,
            fileList: [],
            file: null,
            importLoading: false,
            viewLogsVisible: false,
            ruleForm: {
                buyUserId: null,//买家id
                orderNo: null,//订单号
                internalTotalAmount: null,//总价
                actualTotalAmount: null,//实际总价
                chatPicUrl: null,//聊天记录
                orderWeight: 0,//快递重量
                totalSheetCount: 0,//总张数
                weight: 0,//重量
                cost: 0,//成本(不含快递+打包)
                totalCost: 0,//成本(含快递+打包)
                dtls: [
                    {
                        norms: null,//规格
                        type: null,//类型
                        sheetWidth: null,//单张宽
                        sheetLength: null,//单张长
                        sheetCount: null,//张数
                        internalTotalAmount: null,//售价
                        remark: null,//备注
                    }
                ]
            },
            chatUrls: [],
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            rules: {
                chatPicUrl: [
                    { required: true, message: '请上传聊天记录', trigger: 'change' }
                ]
            },
            chatPicUrl: [],
            drawer: false,
            catchList: [],
            paytimeRanges: []
        }
    },
    async mounted() {
        await this.getSetList()
        await this.getList()
    },
    methods: {
        async resetWeightSumbit() {
            if (this.resetWeightInfo.weight <= 0) return this.$message.error('重量不能小于等于0')
            const { success } = await reSetPZDWeight(this.resetWeightInfo)
            if (success) {
                this.$message.success('重置成功')
                this.getList()
                this.resetWeightVisible = false
            } else {
                this.$message.error('重置失败')
            }
        },
        resetWeight(internalOrderNo, weight) {
            this.resetWeightInfo = {
                weight,
                internalOrderNo
            }
            this.resetWeightVisible = true
        },
        delRuleFormDtls(i) {
            this.ruleForm.dtls.splice(i, 1)
            this.Cpt()
        },
        async viewLogs(id) {
            this.viewLogsInfo.parentId = id
            this.viewLogsVisible = true
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            //没有时间就提示
            // if (!this.yearMonthDay) return this.$message.error('请选择日期')
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            // form.append("yearMonthDay", this.yearMonthDay);
            this.importLoading = true
            await importCustomMadeMultipleAsync(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            // this.yearMonthDay = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportPZDSaleOrderRecordList(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '皮桌垫报价明细' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
            this.isExport = false
        },
        async getSetList() {
            const params = {
                currentPage: 1,
                pageSize: 10000,
                orderBy: 'createdTime',
                isAsc: false,
            }
            // 使用时将下面的方法替换成自己的接口
            const { data, success } = await getCostSetByTMZD(params)
            if (success) {
                this.list = data
            } else {
                //获取列表失败
                this.$message.error('获取数据失败')
            }
        },
        async changeTime(e, type) {
            if (type == 'create') {
                this.ListInfo.startTime = e ? e[0] : null
                this.ListInfo.endTime = e ? e[1] : null
            } else {
                this.ListInfo.payStartTime = e ? e[0] : null
                this.ListInfo.payEndTime = e ? e[1] : null
            }
            await this.getList()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
            }
            const replaceArr = ['buyUserId', 'orderNo', 'createdUserName']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data, success } = await getPZDSaleOrderRecordList(this.ListInfo)
            if (success) {
                data.list.forEach((item, index) => {
                    if (item.chatPicUrl) {
                        item.pics = JSON.stringify(item.chatPicUrl.split(",").map(a => {
                            return {
                                url: a
                            }
                        }));
                    }
                });
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
                this.loading = false
            } else {
                //获取列表失败
                this.loading = false
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.detail ::v-deep .vxetoolbar20221212 {
    display: none !important;
}

.iptCss {
    width: 60px;
}

::v-deep .cell {
    padding-left: 3px;
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}

.chatPicUrl {
    position: relative;

    .picTips {
        position: absolute;
        top: 0;
        left: 150px;
        color: #ff0000;
        font-size: 16px;
    }
}

.btnGroup1 {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}
</style>
