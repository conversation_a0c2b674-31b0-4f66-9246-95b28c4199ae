import { formatTime } from "@/utils";
import dayjs from "dayjs";
import { formatLinkProCode, platformlist } from '@/utils/tools'
import { AllDeptViewContainRootList } from '@/api/admin/deptuser'

import { dictionary } from '@/api/bladegateway/yunhangiscustomer.js';
const quyu = [
  { label: "武汉", value: "武汉" },
  { label: "南昌", value: "南昌" },
  { label: "新余", value: "新余" },
  { label: "樟树", value: "樟树" },
];
const peopleStatus = [
 { label: "试用", value: "试用" },
 { label: "转正", value: "转正" },
];
const peopleDevp = [
 { label: "银牌", value: "银牌" },
 { label: "金牌", value: "金牌" },

 { label: "机动", value: "机动" },
 { label: "预备组长", value: "预备组长" },

 { label: "组长", value: "组长" },
 { label: "预备主管", value: "预备主管" },

 { label: "主管", value: "主管" },
];
const exportStatus = [
 { label: "真实月薪", value: 2 },
 { label: "预估月薪", value: 1 },
];

const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = startDate;
const fliterjs = {
  name: "Users",
  data () {
      return {
          filter: {
           subgroupList: [],
           timeType: false,
             //  timerange: [startDate, endDate],
              // orderMenuDateRange: [
              //     formatTime(dayjs().subtract(30, "day"), "YYYY-MM-DD"),
              //     formatTime(new Date(), "YYYY-MM-DD"),
              // ],
          },
          deptList: [],
          regionList: [],
          platformList: [],

          platformlist:[],
          peopleStatus: [],
          peopleDevp: [],
          subgroupListArr: [],
          exportStatus: exportStatus,
          // props: { multiple: true, label: 'full_name', value: 'full_code' },
          props: { multiple: true, label: 'full_name', value: 'full_name' },
          quyu: [],
          pickerOptions: {
           shortcuts: [{
               text: '一周',
               onClick (picker) {
                   const end = new Date();
                   const start = new Date();
                   start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                   picker.$emit('pick', [start, end]);
               }
           }, {
               text: '一个月',
               onClick (picker) {
                   const end = new Date();
                   const start = new Date();
                   start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                   picker.$emit('pick', [start, end]);
               }
           }, {
               text: '三个月',
               onClick (picker) {
                   const end = new Date();
                   const start = new Date();
                   start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                   picker.$emit('pick', [start, end]);
               }
           }],
           // 自定义日期禁用函数
           disabledDate (date) {
               // 获取当前日期
               const currentDate = new Date();
               // 如果选中日期在当前日期之后，则禁用它
               return date > currentDate;
           }
       },
      };
  },
  async mounted() {
   localStorage.setItem('oneGetTime', 1);
   if(localStorage.getItem('oneGetTime')==1){
     localStorage.setItem('oneGetTime', localStorage.getItem('oneGetTime')+1);
     if(this.platformlist.length==0){
      await this.getFilterList('platform');
     }
     if(this.quyu.length==0){
      await this.getFilterList('region');
     }
     if(this.peopleStatus.length==0){
      await this.getFilterList('state');
     }
     if(this.peopleDevp.length==0){
      await this.getFilterList('grade');
     }
     if(this.deptList.length==0){
      await this.initDept();
     }
     if(this.subgroupListArr.length==0){
      await this.getFilterList('subgroup');
     }
   }

  },
  methods: {
    addMonthToDate(dateString) {  
    // 创建Date对象  
    const date = new Date(dateString);  
    
    // 确保日期是有效的  
    if (isNaN(date.getTime())) {  
      return null; // 或者抛出一个错误  
    }  
    
    // 先将日期设置为当前月的最后一天（以防增加月份后日期无效）  
    date.setDate(1);  
    date.setMonth(date.getMonth() + 1);  
    date.setDate(0); // 设置为当前月的最后一天（Date的setDate(0)是上个月的最后一天，但我们已经加了月份）  
    
    // 减少一天（因为上面已经是最后一天了，所以这里直接减1即可）  
    date.setDate(date.getDate());  
    
    // 格式化日期为"YYYY-MM-DD"  
    const year = date.getFullYear();  
    const month = String(date.getMonth() + 1).padStart(2, '0'); // getMonth() 返回的是0-11，所以需要+1并补0  
    const day = String(date.getDate()).padStart(2, '0'); // getDate() 返回的是日期，可能需要补0  
    
    // 返回新的日期字符串  
    return `${year}-${month}-${day}`;  
  },
  isSameYearMonth(dateString1, dateString2) {  
        const date1 = new Date(dateString1);  
        const date2 = new Date(dateString2);  
        if (isNaN(date1.getTime()) || isNaN(date2.getTime())) {  
            return false;
        }  
      
        const year1 = date1.getFullYear();  
        const month1 = date1.getMonth() + 1;  
        const year2 = date2.getFullYear();  
        const month2 = date2.getMonth() + 1;  
      
        // 比较年份和月份  
        return year1 === year2 && month1 === month2;  
    },
   async onSearch () {
    let isnext = true;
    if(this.filter?.CostDate){
      if(this.filter.CostDate.length>0){
        isnext = this.isSameYearMonth(this.filter.CostDate[0], this.filter.CostDate[1])
      }
    }

    // if(this.filter?.alldate){
      if(this.filter.alldate&&this.filter.alldate.length>0){
        this.filter.startQueryTime = this.filter.alldate[0];
        this.filter.endQueryTime = this.filter.alldate[1];
      }else{
        this.filter.startQueryTime = null;
        this.filter.endQueryTime = null;
      }
    // }
    
    if(!isnext){
      this.$message({
        message: '时间范围请选择同月区间',
        type: 'warning'
      });
      return;
    }

    // let newarr = [];
    // if(this.filter.subgroupList.length>0){
    //  this.filter.subgroupList.map((item)=>{
    //    if(item.length>0){
    //      newarr.push(item.join(","))
    //    }
    //  })
    //  this.filter.subgroup = newarr;
    // }else{
    //   this.filter.subgroup = [];
    // }

    this.$refs.pager.setPage(1);
    await this.getFilterList('region');
    await this.getFilterList('subgroup');
    await this.getList();

  },
   async initDept() {
    const data = await AllDeptViewContainRootList()
        if (data && data.success) {
            data.data.shift();
            this.deptList = this.buildTree(data.data);
        }
    },
    buildTree(items) {
      const map = {}; // 创建一个映射来存储每个部门的引用
      const tree = []; // 初始化最终的树数组

      // 首先，将所有项目映射到map中，方便后面查找父级
      items.forEach(item => {
        map[item.dept_id] = { ...item, children: [] };
      });

      // 构建树形结构
      items.forEach(item => {
        const parent = map[item.parent_id]; // 查找父级
        if (parent) {
          // 如果找到了父级，则将当前部门添加到父级的children数组中
          parent.children.push(map[item.dept_id]);
        } else {
          // 如果没有父级（即顶级部门），则将其添加到树的根数组中
          tree.push(map[item.dept_id]);
        }
      });

      // 清理没有子部门的 children
     function cleanChildren(node) {
        if (node.children && node.children.length === 0) {
          delete node.children;
        } else {
          node.children.forEach(cleanChildren); // 递归清理子节点
        }
      }

     tree.forEach(cleanChildren); // 对树的每个节点执行清理操作

      return tree;
    },
   async getFilterList(value){
    const data = await dictionary({code:value})
       if (data && data.success) {
        if(value=='platform'){
         data.data.map((item)=>{
          item.label = item.dictValue;
          item.value = item.dictKey;
         })
         this.platformlist = data.data;
        }else if(value=='region'){
         data.data.map((item)=>{
          item.label = item.dictValue;
          item.value = item.dictKey;
         })
         this.quyu = data.data;
        }else if(value=='state'){
         data.data.map((item)=>{
          item.label = item.dictValue;
          item.value = item.dictKey;
         })
         this.peopleStatus = data.data;
        }else if(value=='grade'){
         data.data.map((item)=>{
          item.label = item.dictValue;
          item.value = item.dictKey;
         })
         this.peopleDevp = data.data;
        }else if(value=='subgroup'){
          data.data.map((item)=>{
           item.label = item.dictValue;
           item.value = item.dictKey;
          })
          this.subgroupListArr = data.data;
         }
           // this.deptList = data.data;
           console.log(this.deptList)
       }
   }
  }
 }

 export default fliterjs;
