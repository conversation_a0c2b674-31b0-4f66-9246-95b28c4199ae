<template>
  <my-container v-loading="pageLoading">
    <el-select @change="onSearch" filterable v-model="filter.platFrom" collapse-tags clearable placeholder="平台"
    style="width: 90px"> 
    <el-option label="淘系" :value="1" />
    <el-option label="淘工厂" :value="8" />
  </el-select>
  <el-select filterable v-model="filter.userId" collapse-tags clearable placeholder="运营助理"
  @change="onSearch"
  style="width: 90px">
  <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
</el-select>
      <vxe-table
            border
            show-overflow
            ref="table"
            :loading="listLoading"
            :data="list"
            :edit-config="{trigger: 'click', mode: 'cell'}"
            height="770px"
            :row-config="{height: 50}"
            :column-config="{resizable: true}"
            header-align="center"
            >
            <vxe-column type="seq" width="60"></vxe-column>
            <vxe-colgroup title="淘系目标">
              <vxe-column field="assistant" title="运营助理"width="90" align="center"></vxe-column>
            </vxe-colgroup>
            <vxe-column field="platForm" title="平台" width="90" align="center">
              <template #default="{ row }">
                <template v-if="row.platForm==1">
                    <span >淘系</span>
                   </template>
                   <template v-else-if="row.platForm==8">
                       <span >淘工厂</span>
                   </template>
                  </template>
            </vxe-column>
            <vxe-column field="title" title="目标类别" width="90" align="center">
              <template #default="{ row }">
                <span >销售额</span><br/>
                 <span >毛三</span>
             </template>
            </vxe-column>
            <vxe-colgroup title="第一季度">
              <vxe-column field="date3" title="1月"width="90" align="center">
                <template #default="{ row }">
                  {{row.m1Saleamount}}
                  <br/>
                  {{row.m1profit3}}
                </template>
              </vxe-column>
              <vxe-column field="date3" title="2月"width="90" align="center">
                <template #default="{ row }">
                  {{row.m2Saleamount}}
                  <br/>
                  {{row.m2profit3}}
                </template>
              </vxe-column>
              <vxe-column field="date3" title="3月"width="90"align="center">
                <template #default="{ row }">
                  {{row.m3Saleamount}}
                  <br/>
                  {{row.m3profit3}}
                </template>
              </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="第二季度">
              <vxe-column field="date3" title="4月"width="90" align="center">
                <template #default="{ row }">
                  {{row.m4Saleamount}}
                  <br/>
                  {{row.m4profit3}}
                </template>
              </vxe-column>
              <vxe-column field="date3" title="5月"width="90"align="center">
                <template #default="{ row }">
                  {{row.m5Saleamount}}
                  <br/>
                  {{row.m5profit3}}
                </template>
              </vxe-column>
              <vxe-column field="date3" title="6月"width="90" align="center">
                <template #default="{ row }">
                  {{row.m6Saleamount}}
                  <br/>
                  {{row.m6profit3}}
                </template>
              </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="第三季度">
              <vxe-column field="date3" title="7月"width="90" align="center">
                <template #default="{ row }">
                  {{row.m7Saleamount}}
                  <br/>
                  {{row.m7profit3}}
                </template>
              </vxe-column>
              <vxe-column field="date3" title="8月"width="90" align="center">
                <template #default="{ row }">
                  {{row.m8Saleamount}}
                  <br/>
                  {{row.m8profit3}}
                </template>
              </vxe-column>
              <vxe-column field="date3" title="9月"width="90" align="center">
                <template #default="{ row }">
                  {{row.m9Saleamount}}
                  <br/>
                  {{row.m9profit3}}
                </template>
              </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="第四季度">
              <vxe-column field="date3" title="10月"width="90" align="center">
                <template #default="{ row }">
                  {{row.m10Saleamount}}
                  <br/>
                  {{row.m10profit3}}
                </template>
              </vxe-column>
              <vxe-column field="date3" title="11月"width="90" align="center">
                <template #default="{ row }">
                  {{row.m11Saleamount}}
                  <br/>
                  {{row.m11profit3}}
                </template>
              </vxe-column>
              <vxe-column field="date3" title="12月"width="90" align="center">
                <template #default="{ row }">
                  {{row.m12Saleamount}}
                  <br/>
                  {{row.m12profit3}}
                </template>
              </vxe-column>
            </vxe-colgroup>
            <vxe-column  width="150" title="操作" align="center">
              <template  #default="{ row }">
                  <vxe-button style="color: blue;" type="text" @click="AddCommissionerPlan(row)" >编辑</vxe-button>
                </template>
            </vxe-column>
          </vxe-table>
          <template #footer>
              <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
          </template>
          <el-drawer title="编辑助理计划" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="addVisible" 
                  direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
                <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
                <div class="drawer-footer">
                <el-button @click.native="addVisible = false">取消</el-button>
                <my-confirm-button type="submit" :loading="editLoading" @click="onaddSubmit" />
                </div>
          </el-drawer>
    </my-container>
  </template>
  <script>
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
   import {getDirectorList} from '@/api/operatemanage/base/shop'
  import {formatPlatform,formatTime,formatYesornoBool,formatLinkProCode} from "@/utils/tools";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import InputMult from "@/components/Comm/InputMult";
  import { Loading } from 'element-ui';
  import { ruleDirectorGroup,ruleDirector,ruleDirectorBackup } from '@/utils/formruletools'
  import {getAssistantQuarterlyPlanDetailsTx as getCommissionerQuarterlyPlanDetailsTx, editAssistantQuarterlyPlanDetailsTx as editCommissionerQuarterlyPlanDetailsTx} from '@/api/operatemanage/base/product'

  let loading;
  const startLoading = () => {
    loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
    });
  }; 
  const tableHandles=[  
        ];
  
  export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,InputMult,vxetablebase},
    data() {
      return {
         addVisible:false,
        filter: {
         platFrom:null,
         userId:null
        },
            list:[],
         directorlist:[],
         tableHandles:tableHandles,
         listLoading: false,
         pageLoading: false,
         sels: [],
        autoform:{
                 fApi:{},
                 options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
                 rule:[{required: false}]
          },
      pager: { OrderBy: "", IsAsc: false },
      total: 0,
      };
    },
    
     mounted() {
       this.initform();
       this.onSearch();
    },
  async created() {
 
   await this.getGroupList();
  
  
},
    
    methods: {
      AddCommissionerPlan(row){
        row.platForm=row.platForm.toString()
        row.userId=row.userId.toString()
        
        this.addVisible = true
        this.$nextTick(() => {
          this.autoform.fApi.setValue(row);
          });


      },
      async getGroupList() {

    var res3 = await getDirectorList();
    this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });

   
  },
  
    async onaddSubmit() {
      this.editLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          if((formData.m1profit3==0&&formData.m1Saleamount!=0)||(formData.m1profit3!=0&&formData.m1Saleamount==0)||(formData.m2profit3==0&&formData.m2Saleamount!=0)||(formData.m2profit3!=0&&formData.m2Saleamount==0)||(formData.m3profit3==0&&formData.m3Saleamount!=0)||(formData.m3profit3!=0&&formData.m3Saleamount==0)||(formData.m4profit3==0&&formData.m4Saleamount!=0)||(formData.m4profit3!=0&&formData.m4Saleamount==0)||(formData.m5profit3==0&&formData.m5Saleamount!=0)||(formData.m5profit3!=0&&formData.m5Saleamount==0)||(formData.m6profit3==0&&formData.m6Saleamount!=0)||(formData.m6profit3!=0&&formData.m6Saleamount==0)||(formData.m7profit3==0&&formData.m7Saleamount!=0)||(formData.m7profit3!=0&&formData.m7Saleamount==0)||(formData.m8profit3==0&&formData.m8Saleamount!=0)||(formData.m8profit3!=0&&formData.m8Saleamount==0)||(formData.m9profit3==0&&formData.m9Saleamount!=0)||(formData.m9profit3!=0&&formData.m9Saleamount==0)||(formData.m10profit3==0&&formData.m10Saleamount!=0)||(formData.m10profit3!=0&&formData.m10Saleamount==0)||(formData.m11profit3==0&&formData.m11Saleamount!=0)||(formData.m11profit3!=0&&formData.m11Saleamount==0)||(formData.m12profit3==0&&formData.m12Saleamount!=0)||(formData.m12profit3!=0&&formData.m12Saleamount==0))
          {
            this.$message.error('请填写销售额和毛三');
           
            return
          }
          const res = await editCommissionerQuarterlyPlanDetailsTx(formData);
          if(res.code==1){
            this.$message.success('修改成功！');
            this.autoform.fApi.resetFields()
            this.addVisible=false;  
            this.getlist(); 
          }
        }else{}
     })
     this.editLoading=false;
    },
     async initform(){
      let that=this;

          this.autoform.rule= [
                    
          { type: 'hidden', field: 'id', title: 'id', value: '', col: { span: 12 } },
                {type:'select',field:'platForm',title:'平台',value: '',col:{span:6},options: [{ value: '1', label: '淘系' }, { value: '8', label: '淘工厂' }], props:{clearable:true,disabled:true },validate: [{type: 'string', required: true, message:'请选择平台'}]},
                {type:'select',field:'userId',title:'运营助理',value: '', ...await ruleDirectorBackup(),    props:{disabled:true}},
                {type:'inputnumber',field:'m1Saleamount',title:'1月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m1profit3',title:'1月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m2Saleamount',title:'2月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m2profit3',title:'2月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m3Saleamount',title:'3月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m3profit3',title:'3月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m4Saleamount',title:'4月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m4profit3',title:'4月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m5Saleamount',title:'5月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m5profit3',title:'5月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m6Saleamount',title:'6月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m6profit3',title:'6月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m7Saleamount',title:'7月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m7profit3',title:'7月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m8Saleamount',title:'8月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m8profit3',title:'8月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m9Saleamount',title:'9月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m9profit3',title:'9月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },  
                {type:'inputnumber',field:'m10Saleamount',title:'10月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m10profit3',title:'10月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m11Saleamount',title:'11月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m11profit3',title:'11月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },  
                {type:'inputnumber',field:'m12Saleamount',title:'12月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                {type:'inputnumber',field:'m12profit3',title:'12月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },                          
                     ]
                      
       
      },
      
     async sortchange(column){
        if(!column.order)
          this.pager={};
        else
          this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
       await this.onSearch();
      },
      onRefresh(){
        this.onSearch()
      },
      async onSearch(){
        this.$refs.pager.setPage(1);
        await this.getlist().then(res=>{  });
        // loading.close();
      },
      async getlist(){
        var that=this;
        var pager = this.$refs.pager.getPager();
        const params = {...pager,...this.pager,...this.filter};
        startLoading(); 
        const res = await getCommissionerQuarterlyPlanDetailsTx(params).then(res=>{
            loading.close();
            that.total = res.data?.total;
            that.list = res.data?.list;
            that.summaryarry=res.data?.summary;
        });
      },
   
      selectchange:function(rows,row) {
        this.selids=[];
        rows.forEach(f=>{
          this.selids.push(f.id);
        })
      },
     onRefresh(){
          this.onSearch()
      },
    
  },
    
  };
  </script>
  <style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
      background-color: #fff;
    }
  </style>
   
   