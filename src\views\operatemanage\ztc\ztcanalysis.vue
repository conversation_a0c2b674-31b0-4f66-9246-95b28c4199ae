<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
        <el-row>
          <el-form-item label="统计类型">
          <el-radio-group @change="typeChange" v-model="Filter.type">
                    <el-radio
                  v-for="item in typeList"
                  :key="item.id"
                  :label="item.id">{{item.name}}             
                </el-radio>
              </el-radio-group>
        </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="7">
              <el-form-item label="统计时间:">
                <el-date-picker style="width:320px"
                  v-model="Filter.timerange"
                  type="daterange"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  range-separator="至"
                  start-placeholder="从："
                  end-placeholder="到："
                  :picker-options="pickerOptions"
                ></el-date-picker>
              </el-form-item>
          </el-col>
          <el-col :span="10">
              <el-form-item v-if="Filter.type == 1" label="平台:" label-position="right" label-width="72px">
                <el-select clearable multiple filterable  v-model="Filter.platform" placeholder="请选择" class="el-select-content" style="width:500px">
                  <el-option 
                    v-for="item in platformList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">             
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item v-else-if="Filter.type == 2" label="所属店铺:" label-position="right" label-width="72px">
                <el-select clearable multiple filterable v-model="Filter.idShop" placeholder="请选择" class="el-select-content" style="width:500px">
                <el-option 
                  v-for="item in shopList"
                  :key="item.id"
                  :label="item.shopName"
                  :value="item.id">             
                </el-option>
              </el-select>
              </el-form-item>

              <el-form-item v-else-if="Filter.type == 3" label="组长:" label-position="right" label-width="72px">
                <el-select clearable multiple filterable  v-model="Filter.idDirectorGroup" placeholder="请选择" class="el-select-content" style="width:500px">
                  <el-option 
                  v-for="item in groupList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">             
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item v-else-if="Filter.type == 4 || Filter.type == 5 || Filter.type == 6 || Filter.type == 7" label="人员:" label-position="right" label-width="72px">
                  <el-select clearable multiple filterable v-model="Filter.idUser" placeholder="请选择" class="el-select-content" style="width:500px">
                    <el-option 
                  v-for="item in userList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">             
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-else-if="Filter.type == 8" label="产品id" label-position="right" label-width="72px">
                   <el-input v-model="Filter.idProduct" style="width:500px;" placeholder="逗号分隔产品id"/>
              </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="9">
            <el-form-item label="柱形图">
              <el-select multiple clearable filterable v-model="Filter.pillarElements" placeholder="请选择" class="el-select-content" style="width:500px">
                  <el-option 
                    v-for="item in elementList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">             
                  </el-option>
                </el-select>
            </el-form-item>
         </el-col>
         <el-col :span="9">
            <el-form-item label="折线">
                <el-select clearable multiple filterable v-model="Filter.lineElements" placeholder="请选择" class="el-select-content" style="width:500px">
                  <el-option 
                    v-for="item in elementList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">             
                  </el-option>
                </el-select>
            </el-form-item>
         </el-col>
         <!--
         <el-col :span="4">
            <el-form-item label="饼图">
              <el-select filterable v-model="Filter.pieElement" placeholder="请选择" class="el-select-content" style="width:600px">
                  <el-option 
                    v-for="item in elementList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">             
                  </el-option>
                </el-select>
            </el-form-item>
         </el-col>
         -->
         <el-col :span="2">
            <el-form-item>
              <el-button type="primary" @click="onSearch" style="width:100px">统计</el-button>
            </el-form-item>
         </el-col>
        </el-row>
      </el-form>
    </template>
    <el-row>
      <el-table
      :data="SumList"
      :fit = true
      :tableCols='tableCols'
      :border=true
      max-height="400px"
      style="width: 100%;">
        <template v-for="(item, index) in tableCols">
          <el-table-column sortable show-overflow-tooltip  v-if="!(item.display==false)"
          :key="index"
          :prop="item.prop" 
          :label="item.label"
          :width="item.width" 
          align="left">
            <template slot-scope="scope" >
              <span v-if="item.type==='html'" v-html="(item.formatter && item.formatter(scope.row))"></span>
              <span v-if="item.type==='format'">{{  }} </span>
                  <!-- 默认 -->
              <span v-if="!item.type" 
                    :style="item.itemStyle && item.itemStyle(scope.row)" 
                    :class="item.itemClass && item.item.itemClass(scope.row)">{{(item.formatter && item.formatter(scope.row)) || scope.row[item.prop]}}</span>
            </template>
          </el-table-column>
        </template>
    </el-table>
    </el-row>
    <div style="height = 10px"></div>
    <el-row>
      <el-col :span="2" :offset="20">
        <el-switch :width="40" @change="displayTypeChange"
          v-model="displayType"
          inactive-color="#228B22"
          active-text="图形"
          inactive-text="表格">
        </el-switch>
      </el-col>
    </el-row>
    <el-row v-show="displayType">
      <el-col :span="24">
         <div id="echartmonitChart" style="height: 500px; box-sizing:border-box; line-height: 400px;">     
        </div>
      </el-col>
    </el-row>
    <el-row v-show="!displayType" >
     <el-table
      :data="PerDayList"
      :fit = true
      :perDayTableCols='perDayTableCols'
      :border=true
      style="width: 100%;">
        <template v-for="(item, index) in perDayTableCols">
          <el-table-column sortable show-overflow-tooltip  v-if="!(item.display==false)"
          :key="index"
          :prop="item.prop" 
          :label="item.label"
          :width="item.width" 
          align="left">
            <template slot-scope="scope" >
              <span v-if="item.type==='html'" v-html="(item.formatter && item.formatter(scope.row))"></span>
              <span v-if="item.type==='format'">{{  }} </span>
                  <!-- 默认 -->
              <span v-if="!item.type" 
                    :style="item.itemStyle && item.itemStyle(scope.row)" 
                    :class="item.itemClass && item.item.itemClass(scope.row)">{{(item.formatter && item.formatter(scope.row)) || scope.row[item.prop]}}</span>
            </template>
          </el-table-column>
        </template>
    </el-table>
    </el-row>
    <!--
    <el-row>
      <el-col :span="24">
        <div id="echartmonitPie" style="height: 500px; box-sizing:border-box; line-height: 400px;">     
        </div>
      </el-col>
    </el-row>
    -->
  </my-container>
</template>
<script>
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList,
         getDirectorGroupList as getDirectorGroupList,
         getDirectorList as getDirectorList
       } from '@/api/operatemanage/base/shop';
import {ztcAnalysisChart,ztcAnalysisPie,ztcAnalysisTable,ztcAnalysisPerDayTable} from '@/api/operatemanage/ztc/ztcanalysis'
import * as echarts from 'echarts';
import { formatPlatform,formatLink} from "@/utils/tools";
import { time } from 'echarts';

const tableCols =[
      {istrue:true, display:false,prop:'date',label:'日期', width:'100', formatter : (row) => {
          return row.date.split(" ")[0]
      }},
      {istrue:true, display:false,prop:'platform',label:'平台', width:'80', formatter:(row)=>formatPlatform(row.platform)},
      {istrue:true, display:false,prop:'nameShop',label:'店铺名', width:'150'},
      {istrue:true, display:false,prop:'nameProduct',label:'商品名称', width:'180',type:'html', formatter:(row)=>{
            var  proBaseUrl="https://detail.tmall.com/item.htm?id="+row.idProduct;
            return formatLink(row.nameProduct,proBaseUrl);
      }},
      {istrue:true, display:false,prop:'idProduct',label:'商品id', width:'120'},

      {istrue:true, display:false,prop:'nameGroup',label:'小组名', width:'80'},
      {istrue:true, display:false,prop:'nameUser',label:'用户名', width:'80'},
      {istrue:true, display:false,prop:'nameOperateSpecial',label:'运营专员', width:'80'},
      {istrue:true, display:false,prop:'nameUser1',label:'运营助理', width:'80'},
      {istrue:true, display:false,prop:'nameUser2',label:'车手', width:'80'},
      {istrue:true, display:false,prop:'nameUser3',label:'备用人员', width:'80'},

      {istrue:true, display:false,prop:'countShow',label:'展现量', width:'80', type:'html',formatter:(row)=>{
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.countShowIndex +'</span>&nbsp;' + row.countShow
        return html}},
      {istrue:true, display:false,prop:'countClick',label:'点击量', width:'80',type:'html',formatter:(row)=>{
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.countClickIndex +'</span>&nbsp;' + row.countClick
        return html}},
      {istrue:true, display:false,prop:'amountAll',label:'花费', width:'80',type:'html',formatter:(row)=>{
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.amountAllIndex +'</span>&nbsp;' + row.amountAll
        return html}},
      {istrue:true, display:false,prop:'rateClick',label:'点击率', width:'80',type:'html',formatter:(row)=>{
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.rateClickIndex +'</span>&nbsp;' + (row.rateClick?(row.rateClick.toFixed(2)+'%'):null)
        //return row.rateClick?(row.rateClick.toFixed(2)+'%'):null
        return html}},
      {istrue:true, display:false,prop:'amountClickAvg',label:'平均点击花费', width:'80',type:'html',formatter:(row)=>{
        //return row.amountClickAvg?.toFixed(2)}},
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.amountClickAvgIndex +'</span>&nbsp;' + row.amountClickAvg?.toFixed(2)
        return html}},
      {istrue:true, display:false,prop:'countStar',label:'总收藏数', width:'80',type:'html',formatter:(row)=>{
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.countStarIndex +'</span>&nbsp;' + row.countStar
        return html}},
      {istrue:true, display:false,prop:'amountStar',label:'收藏成本', width:'80',type:'html',formatter:(row)=>{
        //return row.amountStar?.toFixed(2)}},
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.amountStarIndex +'</span>&nbsp;' + row.amountStar?.toFixed(2)
        return html }},
      {istrue:true, display:false,prop:'countShoppingCarAll',label:'总购物车数', width:'80',type:'html',formatter:(row)=>{
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.countShoppingCarAllIndex +'</span>&nbsp;' + row.countShoppingCarAll
        return html }},
      {istrue:true, display:false,prop:'amountShoppingCar',label:'加购成本', width:'80',type:'html',formatter:(row)=>{
        //return row.amountShoppingCar?.toFixed(2)}},
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.amountShoppingCarIndex +'</span>&nbsp;' + row.amountShoppingCar?.toFixed(2)
        return html}},
      {istrue:true, display:false,prop:'rateStarConversion',label:'宝贝收藏转化率', width:'80',type:'html',formatter:(row)=>{
        //return row.rateStarConversion?(row.rateStarConversion.toFixed(2) + '%'):null;}},
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.rateStarConversionIndex +'</span>&nbsp;' + (row.rateStarConversion?(row.rateStarConversion.toFixed(2) + '%'):null);
        return html }},
      {istrue:true, display:false,prop:'rateShoppingCarConversion',label:'加购率', width:'80',type:'html',formatter:(row)=>{
        //return row.rateShoppingCarConversion?(row.rateShoppingCarConversion.toFixed(2) + '%'):null}},
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.rateShoppingCarConversionIndex +'</span>&nbsp;' + (row.rateShoppingCarConversion?(row.rateShoppingCarConversion.toFixed(2) + '%'):null)
        return html}},
      {istrue:true, display:false,prop:'amountDealAll',label:'总成交金额', width:'80',type:'html',formatter:(row)=>{
        //return row.amountDealAll?.toFixed(2)}},
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.amountDealAllIndex +'</span>&nbsp;' + row.amountDealAll?.toFixed(2)
        return html}},
      {istrue:true, display:false,prop:'amountDealDirect',label:'直接成交金额', width:'80',type:'html',formatter:(row)=>{
        //return row.amountDealDirect?.toFixed(2)}},
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.amountDealDirectIndex +'</span>&nbsp;' + row.amountDealDirect?.toFixed(2)
        return html}},
      {istrue:true, display:false,prop:'amountDealIndirect',label:'间接成交金额', width:'80',type:'html',formatter:(row)=>{
        //return row.amountDealIndirect?.toFixed(2)}},
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.amountDealIndirectIndex +'</span>&nbsp;' + row.amountDealIndirect?.toFixed(2)
        return html}},
      {istrue:true, display:false,prop:'countDealAll',label:'总成交笔数', width:'80',type:'html',formatter:(row)=>{
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.countDealAllIndex +'</span>&nbsp;' + row.countDealAll
        return html }},
      {istrue:true, display:false,prop:'countDealDirect',label:'直接成交笔数', width:'80',type:'html',formatter:(row)=>{
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.countDealDirectIndex +'</span>&nbsp;' + row.countDealDirect
        return html }},
      {istrue:true, display:false,prop:'countDealIndirect',label:'间接成交笔数', width:'80',type:'html',formatter:(row)=>{
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.countDealIndirectIndex +'</span>&nbsp;' + row.countDealIndirect
        return html }},
      {istrue:true, display:false,prop:'rateInputOutput',label:'投入产出比', width:'80',type:'html',formatter:(row)=>{
        //return row.rateInputOutput?row.rateInputOutput.toFixed(2):null}},
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.rateInputOutputIndex +'</span>&nbsp;' + (row.rateInputOutput?row.rateInputOutput.toFixed(2):null)
        return html}},
      {istrue:true, display:false,prop:'rateClickConversion',label:'点击转化率', width:'80',type:'html',formatter:(row)=>{
        //return row.rateClickConversion?(row.rateClickConversion.toFixed(2)+'%'):null}},
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.rateClickConversionIndex +'</span>&nbsp;' + (row.rateClickConversion?(row.rateClickConversion.toFixed(2)+'%'):null)
        return html}},
      {istrue:true, display:false,prop:'amountDealAvg',label:'平均每笔花费', width:'80',type:'html',formatter:(row)=>{
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.amountDealAvgIndex +'</span>&nbsp;' + row.amountDealAvg?.toFixed(2) 
        return html}},
      {istrue:true, display:false,prop:'amountOrderAvg',label:'平均每单花费', width:'80',type:'html',formatter:(row)=>{
        var html = ' <span style="color: yellow; background-color:deepskyblue; font-weight: 500;text-align: center;">'+ row.amountOrderAvgIndex +'</span>&nbsp;' + row.amountOrderAvg?.toFixed(2) 
        return html}},
     ];

const perDayTableCols2 =[
      {istrue:true, display:false,prop:'date',label:'日期', width:'100', formatter : (row) => {
          return row.date.split(" ")[0]
      }},
      {istrue:true, display:false,prop:'platform',label:'平台', width:'80', formatter:(row)=>formatPlatform(row.platform)},
      {istrue:true, display:false,prop:'nameShop',label:'店铺名', width:'150'},
      {istrue:true, display:false,prop:'nameProduct',label:'商品名称', width:'180',type:'html', formatter:(row)=>{
            var  proBaseUrl="https://detail.tmall.com/item.htm?id="+row.idProduct;
            return formatLink(row.nameProduct,proBaseUrl);
      }},
      {istrue:true, display:false,prop:'idProduct',label:'商品id', width:'120'},

      {istrue:true, display:false,prop:'nameGroup',label:'小组名', width:'80'},
      {istrue:true, display:false,prop:'nameUser',label:'用户名', width:'80'},
      {istrue:true, display:false,prop:'nameOperateSpecial',label:'运营专员', width:'80'},
      {istrue:true, display:false,prop:'nameUser1',label:'运营助理', width:'80'},
      {istrue:true, display:false,prop:'nameUser2',label:'车手', width:'80'},
      {istrue:true, display:false,prop:'nameUser3',label:'备用人员', width:'80'},

      {istrue:true, display:false,prop:'countShow',label:'展现量', width:'80', type:'html',formatter:(row)=>{
        var html = row.countShow
        return html}},
      {istrue:true, display:false,prop:'countClick',label:'点击量', width:'80',type:'html',formatter:(row)=>{
        var html =  row.countClick
        return html}},
      {istrue:true, display:false,prop:'amountAll',label:'花费', width:'80',type:'html',formatter:(row)=>{
        var html = row.amountAll
        return html}},
      {istrue:true, display:false,prop:'rateClick',label:'点击率', width:'80',type:'html',formatter:(row)=>{
        var html = row.rateClick?(row.rateClick.toFixed(2)+'%'):null
        //return row.rateClick?(row.rateClick.toFixed(2)+'%'):null
        return html}},
      {istrue:true, display:false,prop:'amountClickAvg',label:'平均点击花费', width:'80',type:'html',formatter:(row)=>{
        //return row.amountClickAvg?.toFixed(2)}},
        var html =  row.amountClickAvg?.toFixed(2)
        return html}},
      {istrue:true, display:false,prop:'countStar',label:'总收藏数', width:'80',type:'html',formatter:(row)=>{
        var html = row.countStar
        return html}},
      {istrue:true, display:false,prop:'amountStar',label:'收藏成本', width:'80',type:'html',formatter:(row)=>{
        //return row.amountStar?.toFixed(2)}},
        var html =  row.amountStar?.toFixed(2)
        return html }},
      {istrue:true, display:false,prop:'countShoppingCarAll',label:'总购物车数', width:'80',type:'html',formatter:(row)=>{
        var html =  row.countShoppingCarAll
        return html }},
      {istrue:true, display:false,prop:'amountShoppingCar',label:'加购成本', width:'80',type:'html',formatter:(row)=>{
        //return row.amountShoppingCar?.toFixed(2)}},
        var html = row.amountShoppingCar?.toFixed(2)
        return html}},
      {istrue:true, display:false,prop:'rateStarConversion',label:'宝贝收藏转化率', width:'80',type:'html',formatter:(row)=>{
        //return row.rateStarConversion?(row.rateStarConversion.toFixed(2) + '%'):null;}},
        var html =  row.rateStarConversion?(row.rateStarConversion.toFixed(2) + '%'):null;
        return html }},
      {istrue:true, display:false,prop:'rateShoppingCarConversion',label:'加购率', width:'80',type:'html',formatter:(row)=>{
        //return row.rateShoppingCarConversion?(row.rateShoppingCarConversion.toFixed(2) + '%'):null}},
        var html = row.rateShoppingCarConversion?(row.rateShoppingCarConversion.toFixed(2) + '%'):null
        return html}},
      {istrue:true, display:false,prop:'amountDealAll',label:'总成交金额', width:'80',type:'html',formatter:(row)=>{
        //return row.amountDealAll?.toFixed(2)}},
        var html = row.amountDealAll?.toFixed(2)
        return html}},
      {istrue:true, display:false,prop:'amountDealDirect',label:'直接成交金额', width:'80',type:'html',formatter:(row)=>{
        //return row.amountDealDirect?.toFixed(2)}},
        var html =  row.amountDealDirect?.toFixed(2)
        return html}},
      {istrue:true, display:false,prop:'amountDealIndirect',label:'间接成交金额', width:'80',type:'html',formatter:(row)=>{
        //return row.amountDealIndirect?.toFixed(2)}},
        var html =  row.amountDealIndirect?.toFixed(2)
        return html}},
      {istrue:true, display:false,prop:'countDealAll',label:'总成交笔数', width:'80',type:'html',formatter:(row)=>{
        var html =  row.countDealAll
        return html }},
      {istrue:true, display:false,prop:'countDealDirect',label:'直接成交笔数', width:'80',type:'html',formatter:(row)=>{
        var html =  row.countDealDirect
        return html }},
      {istrue:true, display:false,prop:'countDealIndirect',label:'间接成交笔数', width:'80',type:'html',formatter:(row)=>{
        var html =  row.countDealIndirect
        return html }},
      {istrue:true, display:false,prop:'rateInputOutput',label:'投入产出比', width:'80',type:'html',formatter:(row)=>{
        //return row.rateInputOutput?row.rateInputOutput.toFixed(2):null}},
        var html = row.rateInputOutput?row.rateInputOutput.toFixed(2):null
        return html}},
      {istrue:true, display:false,prop:'rateClickConversion',label:'点击转化率', width:'80',type:'html',formatter:(row)=>{
        //return row.rateClickConversion?(row.rateClickConversion.toFixed(2)+'%'):null}},
        var html =  row.rateClickConversion?(row.rateClickConversion.toFixed(2)+'%'):null
        return html}},
      {istrue:true, display:false,prop:'amountDealAvg',label:'平均每笔花费', width:'80',type:'html',formatter:(row)=>{
        var html = row.amountDealAvg?.toFixed(2) 
        console.log(html)
        return html}},
      {istrue:true, display:false,prop:'amountOrderAvg',label:'平均每单花费', width:'80',type:'html',formatter:(row)=>{
        var html =  row.amountOrderAvg?.toFixed(2) 
        console.log(html)
        return html}},
     ];

export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      tableCols:tableCols,
      perDayTableCols:perDayTableCols2,
      showAll : true,
      showSplit : false,
      displayType : true,
      Filter: {
        timerange: [formatTime(dayjs().startOf("month"), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")],
        platform:[],
        idShop:[],
        idDirectorGroup:[],
        type:1,
        pillarElements:[],
        lineElements:[],
        pieElement:1,
        idUser:[]
      },
      typeList:[{id:1,name:"平台"},{id:2,name:"店铺"},
      {id:3,name:"小组"},{id:4,name:"运营专员"},
      {id:5,name:"运营助理"},{id:6,name:"车手"},
      {id:7,name:"备用人员"},{id:8,name:"产品"}],
      platformList:[{id:1, name:"淘系"}, {id:2, name:"拼多多"}],
      elementList:[{id:20, name:"平均每笔花费", unit:"",compareFunc(val1, val2){ return val2.amountDealAvg - val1.amountDealAvg}},
                   {id:1,name:"展现量", unit:"", compareFunc(val1, val2){ return val2.countShow - val1.countShow}},
                   {id:2,name:"点击量",unit:"",compareFunc(val1, val2){ return val2.countClick - val1.countClick}},
                   {id:3,name:"花费",unit:"",compareFunc(val1, val2){ return val2.amountAll - val1.amountAll}},
                   {id:4,name:"点击率",unit:"%",compareFunc(val1, val2){ return val2.rateClick - val1.rateClick}},
                   {id:5,name:"平均点击花费",unit:"",compareFunc(val1, val2){ return val2.amountClickAvg - val1.amountClickAvg}},
                   {id:6,name:"总收藏",unit:"",compareFunc(val1, val2){ return val2.countStar - val1.countStar}},
                   {id:7,name:"收藏成本",unit:"",compareFunc(val1, val2){ return val2.amountStar - val1.amountStar}},
                   {id:8,name:"总购物车",unit:"",compareFunc(val1, val2){ return val2.countShoppingCarAll - val1.countShoppingCarAll}},
                   {id:9,name:"加购成本",unit:"",compareFunc(val1, val2){ return val2.amountShoppingCar - val1.amountShoppingCar}},
                   {id:10,name:"宝贝收藏转化率",unit:"%",compareFunc(val1, val2){ return val2.rateStarConversion - val1.rateStarConversion}},
                   {id:11,name:"加购率",unit:"%",compareFunc(val1, val2){ return val2.rateShoppingCarConversion - val1.rateShoppingCarConversion}},
                   {id:12,name:"总成交金额",unit:"",compareFunc(val1, val2){ return val2.amountDealAll - val1.amountDealAll}},
                   {id:13,name:"直接成交金额",unit:"",compareFunc(val1, val2){ return val2.amountDealDirect - val1.amountDealDirect}},
                   {id:14,name:"间接成交金额",unit:"",compareFunc(val1, val2){ return val2.amountDealIndirect - val1.amountDealIndirect}},
                   {id:15,name:"总成交笔数",unit:"",compareFunc(val1, val2){ return val2.countDealAll - val1.countDealAll}},
                   {id:16,name:"直接成交笔数",unit:"",compareFunc(val1, val2){ return val2.countDealDirect - val1.countDealDirect}},
                   {id:17,name:"间接成交笔数",unit:"",compareFunc(val1, val2){ return val2.countDealIndirect - val1.countDealIndirect}},
                   {id:18,name:"投入产出比",unit:"",compareFunc(val1, val2){ return val2.rateInputOutput - val1.rateInputOutput}},
                   {id:19,name:"点击转化率",unit:"%",compareFunc(val1, val2){ return val2.rateClickConversion - val1.rateClickConversion}}],
      shopList:[],
      groupList:[],
      SumList: [],
      PerDayList: [],
      listLoading: false,
      pageLoading: false,
      //
      pickerOptions:{
        shortcuts: [{
            text: '今天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date().setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '昨天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 1).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '3天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 2).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '7天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 6).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '15天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 14).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '30天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 29).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }]
      },
    };
  },
  async mounted() {
    await this.getShopList();
    await this.getUserList();
    await this.getGroupList();
  },
  methods: {
    typeChange:function(val) {
      console.log(val)
      //加入一项
      if(val == 8)
      {
          /*暂时去除该项，老板与大侠发现通过公式计算出来的数据无参考意义，直通车里的总笔数不是件数，而是行数（用户通过该链接每付款一次就是一行）
          this.elementList.splice(0, 0, {id:21, name:"平均每单花费", unit:"",compareFunc(val1, val2){ return val2.amountOrderAvg - val1.amountOrderAvg}})
          */
      }
      //删除21
      else
      {
          var findOrderAvg = false
          for(var i = 0; i < this.elementList.length; i++)
          {
            if(this.elementList[i].id == 21)
            {
              findOrderAvg = true
              break
            }
          }

          if(findOrderAvg)
          {
            this.elementList.splice(0,1)
            this.Filter.pillarElements = []
            this.Filter.lineElements = []
          }
      }

       //清空图
       var pieChart = document.getElementById('echartmonitChart');
       var myChartPie = echarts.init(pieChart);
       myChartPie.clear();
       //清空表
       tableCols.forEach(element =>{
          element.display = false
       })
       this.SumList = []

       this.perDayTableCols.forEach(element =>{
          element.display = false
       })
       
       this.PerDayList = []
    },
    displayTypeChange:function(val){
        console.log(val)
        if(val)
        {
           this.DealChart()
        }
        else
        {
          this.DealTable()
        }
    },
    //所属店铺列表
    async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=res1.data;
      return;
    },
     //负责人列表
    async getUserList(){
      const res = await getDirectorList();
      this.userList = res.data;
      return;
    },
    //组长列表
    async getGroupList(){
      const res = await getDirectorGroupList();
      this.groupList = res.data;
      return;
    },
    onSearch(){

      this.SumList = []

      tableCols.forEach(element =>{
          element.display = false
       })
       //tableCols[0].display = true
       this.Filter.pillarElements.forEach(element => {
            tableCols[element+10].display = true
       });
       this.Filter.lineElements.forEach(element => {
           tableCols[element+10].display = true
       });

       switch(this.Filter.type)
       {
         case 1:
            tableCols[1].display = true
           break
         case 2:
            tableCols[2].display = true
           break
         case 3:
            tableCols[5].display = true
            break
          case 4:
          case 5:
          case 6:
          case 7:
            tableCols[6].display = true
            break;
          case 8:
              tableCols[1].display = true
              tableCols[2].display = true
              tableCols[3].display = true
              tableCols[4].display = true
              tableCols[5].display = true
              tableCols[7].display = true
              tableCols[8].display = true
              tableCols[9].display = true
              tableCols[10].display = true
            break;
         default:
           break;
       }

       //this.DealPie();
       this.DealSumTable();

        if(this.displayType)
        {
           this.DealChart()
        }
        else
        {
          this.DealTable()
        }
    },
    async DealPie(){
      const para = {...this.Filter};
      if (this.Filter.timerange) {
        para.dateStart = this.Filter.timerange[0];
        para.dateEnd = this.Filter.timerange[1];
      }

      if(!(para.dateStart&&para.dateEnd)){
        this.$message({message:"请先选择日期！",type:"warning"});
        return;
      }

      var pieChart = document.getElementById('echartmonitPie');
      var myChartPie = echarts.init(pieChart);
      myChartPie.clear();

      const pieSumChart = await ztcAnalysisPie(para);
        if (!pieSumChart?.code)  return; 
        if (!pieSumChart.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
       }

        var optionPie = this.GetPieOptions(pieSumChart.data, para);
        optionPie && myChartPie.setOption(optionPie);
    },
    async DealChart(){
      const para = {...this.Filter};
      if (this.Filter.timerange) {
        para.dateStart = this.Filter.timerange[0];
        para.dateEnd = this.Filter.timerange[1];
      }

      if(!(para.dateStart&&para.dateEnd)){
        this.$message({message:"请先选择日期！",type:"warning"});
        return;
      }

      var pieChart = document.getElementById('echartmonitChart');
      var myChartPie = echarts.init(pieChart);
      myChartPie.clear();

      const pieSumChart = await ztcAnalysisChart(para);
        if (!pieSumChart?.code)  return; 
        if (!pieSumChart.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
       }

        var optionPie = this.GetChartoptions(pieSumChart.data, para);

        console.log(JSON.stringify(optionPie))

        optionPie && myChartPie.setOption(optionPie);
    },
    sortAmountPerDealAvg(val1, val2){
       
        var result = val1.amountDealAvg - val2.amountDealAvg
        console.log(val1.amountDealAvg, val2.amountDealAvg, result)
        return result
    },
    async DealSumTable(){
      const para = {...this.Filter};
      if (this.Filter.timerange) {
        para.dateStart = this.Filter.timerange[0];
        para.dateEnd = this.Filter.timerange[1];
      }

      if(!(para.dateStart&&para.dateEnd)){
        this.$message({message:"请先选择日期！",type:"warning"});
        return;
      }

      this.listLoading = true;
      const res = await ztcAnalysisTable(para);
      this.listLoading = false;
      if (!res?.code)  return;      
      if (!res?.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
       }
      console.log(res.data)

      //暂存
      var tempElements = this.Filter.lineElements.slice(0)

      for(var i = 0; i < this.Filter.pillarElements.length; i++)
        {
            for(var j = 0; j < this.Filter.lineElements.length; j++)
            {
                if(this.Filter.pillarElements[i] == this.Filter.lineElements[j])
                {
                    this.Filter.lineElements.splice(j, 1);
                    break;
                }
            }
        }
      var finalElements = this.Filter.pillarElements.concat(this.Filter.lineElements)

      //还原line
      this.Filter.lineElements = tempElements

      finalElements.forEach(element => {

        var elementIndex = 0
        for(var i = 0; i < this.elementList.length; i++)
        {
           if(this.elementList[i].id == element)
           {
              elementIndex = i
           }
        }

        if(this.elementList[elementIndex].compareFunc)
        {
          res.data.sort(this.elementList[elementIndex].compareFunc)
          for(var i = 0; i < res.data.length; i++)
          {
            switch(element)
            {
              case 1:
                  res.data[i].countShowIndex = i + 1
                break;
              case 2:
                res.data[i].countClickIndex = i + 1
                break;
              case 3:
                  res.data[i].amountAllIndex = i + 1
                  break;
              case 4:
                  res.data[i].rateClickIndex = i + 1
                  break;
              case 5:
                  res.data[i].amountClickAvgIndex = i + 1
                  break;
              case 6:
                  res.data[i].countStarIndex = i + 1
                  break;
              case 7:
                  res.data[i].amountStarIndex = i + 1
                  break;
              case 8:
                  res.data[i].countShoppingCarAllIndex = i + 1
                  break;
              case 9:
                  res.data[i].amountShoppingCarIndex = i + 1
                  break;
              case 10:
                  res.data[i].rateStarConversionIndex = i + 1
                  break;
              case 11:
                  res.data[i].rateShoppingCarConversionIndex = i + 1
                  break;
              case 12:
                  res.data[i].amountDealAllIndex = i + 1
                  break;
              case 13:
                res.data[i].amountDealDirectIndex = i + 1
                break;
              case 14:
                res.data[i].amountDealIndirectIndex = i + 1
                break;
              case 15:
                res.data[i].countDealAllIndex = i + 1
                break;
              case 16:
                res.data[i].countDealDirectIndex = i + 1
                break;
              case 17:
                res.data[i].countDealIndirectIndex = i + 1
                break;
              case 18:
                res.data[i].rateInputOutputIndex = i + 1
                break;
              case 19:
                res.data[i].rateClickConversionIndex = i + 1
                break;
              case 20:
                  res.data[i].amountDealAvgIndex = i + 1;
                break;
              case 21:
                  res.data[i].amountOrderAvgIndex = i + 1;
                break;
            }
          }
        }

      });

      this.SumList = res.data;
    },
    async DealTable(){
      
      console.log("dealtable")
       //清空表
       this.perDayTableCols.forEach(element =>{
          element.display = false
       })
       
       this.perDayTableCols[0].display = true
       this.Filter.pillarElements.forEach(element => {
            this.perDayTableCols[element+10].display = true
       });
       this.Filter.lineElements.forEach(element => {
           this.perDayTableCols[element+10].display = true
       });

       switch(this.Filter.type)
       {
         case 1:
            this.perDayTableCols[1].display = true
           break
         case 2:
            this.perDayTableCols[2].display = true
           break
         case 3:
            this.perDayTableCols[5].display = true
            break
          case 4:
          case 5:
          case 6:
          case 7:
            this.perDayTableCols[6].display = true
            break;
          case 8:
              this.perDayTableCols[4].display = true
            break;
         default:
           break;
       }

       this.PerDayList = []

      const para = {...this.Filter};
      if (this.Filter.timerange) {
        para.dateStart = this.Filter.timerange[0];
        para.dateEnd = this.Filter.timerange[1];
      }

      if(!(para.dateStart&&para.dateEnd)){
        this.$message({message:"请先选择日期！",type:"warning"});
        return;
      }

      console.log(para)

      //this.listLoading = true;
      const res = await ztcAnalysisPerDayTable(para);

      console.log(res)
      //this.listLoading = false;
      if (!res?.code)  return;      
      if (!res?.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
       }
      console.log(res.data)

      this.PerDayList = res.data
    },
    //多元素
    GetChartoptions(element, para){

      //数据
        var series=[]
        element.series.forEach(s=>{
          series.push({  smooth: true, ...s})
        })

        //y轴
        var yAxis = []

        var pillarCount = 0
        para.pillarElements.forEach(s =>{
          var name = ''
          var unit = ''
          for(var i = 0; i < this.elementList.length; i++)
          {
            if(this.elementList[i].id == s)
            {
              name = this.elementList[i].name
              unit = this.elementList[i].unit
              break;
            }
          }
          yAxis.push({type:'value', position: 'left', offset: pillarCount++*50 ,name:name, axisLabel:{formatter: '{value}'+ unit}})
        })

        var lineCount = 0
        para.lineElements.forEach(s =>{
          
          if(para.pillarElements.includes(s))
          {
              return
          }

          var name = ''
          var unit = ''
          for(var i = 0; i < this.elementList.length; i++)
          {
            if(this.elementList[i].id == s)
            {
              name = this.elementList[i].name
              unit = this.elementList[i].unit
              break;
            }
          }
          yAxis.push({type:'value', position: 'right', offset: lineCount++*50 ,name:name, axisLabel:{formatter: '{value}'+ unit}})
        })

      var option = {
        title: {
              text: '',
              subtext: '',
              left: 'left'
          },
        tooltip: {trigger: 'axis'},
        legend: {
            data: element.legend
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {feature: {
        }},
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis: yAxis,
        series:  series
    };
    return option;
    },
    //单元素
    GetPieOptions(element, para){
      var source = element.source
      var option = {
            legend: {},
             title: {
                text: '每日数据占比',
                subtext: '',
                left: 'left',
                padding:50
            },
            tooltip: {
                trigger: 'axis',
                showContent: false
            },
            dataset: {
                source: [
                    ...source
                ]
            },
            xAxis: {type: 'category'},
            yAxis: {gridIndex: 0},
            grid: {top: '55%'},
            series: [
                {type: 'line', smooth: true, seriesLayoutBy: 'row', emphasis: {focus: 'series'}},
                {type: 'line', smooth: true, seriesLayoutBy: 'row', emphasis: {focus: 'series'}},
                {type: 'line', smooth: true, seriesLayoutBy: 'row', emphasis: {focus: 'series'}},
                {type: 'line', smooth: true, seriesLayoutBy: 'row', emphasis: {focus: 'series'}},
                {
                    type: 'pie',
                    id: 'pie',
                    radius: '30%',
                    center: ['50%', '25%'],
                    emphasis: {focus: 'data'},
                    label: {
                        formatter: '{b}: {@2012} ({d}%)'
                    },
                    encode: {
                        itemName: 'product',
                        value: '2012',
                        tooltip: '2012'
                    }
                }
            ]
        };
        return option;
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>