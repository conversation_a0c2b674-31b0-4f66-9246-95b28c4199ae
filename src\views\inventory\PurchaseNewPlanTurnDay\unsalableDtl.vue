<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <div class="flexrow" style="height: 40px; margin-top: 10px;">
                <span style="margin-right:0.4%;">
                    <el-date-picker style="width: 200px" v-model="filter.yearMonthDay" type="date" placeholder="选择日期"
                        :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                    </el-date-picker>
                </span>
                <span style="margin-right:0.4%;">
                    <el-select style="width:160px" placeholder="类别" v-model.trim="filter.hourType"
                        :collapse-tags="true">
                        <el-option label="上午" value="上午"></el-option>
                        <el-option label="下午" value="下午"></el-option>
                        <el-option label="晚上" value="晚上"></el-option>
                    </el-select>
                </span>
                <span style="margin-right:0.4%;"> <!-- 编码创建时间 -->
                    <dateRange :startDate.sync="filter.startTime" :endDate.sync="filter.endTime" style="width: 280px;"
                        range-separator="至" start-placeholder="编码创建开始时间" end-placeholder="编码创建结束时间" :clearable="true" />
                </span>
                <span style="margin-right:0.4%;"> <!-- 款式编码 -->
                    <inputYunhan ref="productCode" :inputt.sync="filter.styleCode" v-model="filter.styleCode"  @callback="callbackGoodsCode($event, 'styleCode')"
                        class="publicCss" placeholder="款式编码/若输入多条请按回车" :clearable="true" :clearabletext="true"
                        :maxRows="100" :maxlength="1000000" title="款式编码" style="width: 200px;">
                    </inputYunhan>
                </span>
                <span style="margin-right:0.4%;"> <!-- 商品编码 -->
                    <inputYunhan ref="productCode" :inputt.sync="filter.goodsCode" v-model="filter.goodsCode"  @callback="callbackGoodsCode($event, 'goodsCode')"
                        class="publicCss" placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true"
                        :maxRows="100" :maxlength="1000000" title="商品编码" style="width: 200px;">
                    </inputYunhan>
                </span>
                <span style="margin-right:0.4%;">
                    <el-button type="primary" @click="getList" style="width: 70px;">搜索</el-button>
                </span>
                <span>
                    <el-button type="primary" @click="onExport" :disabled="isExport" style="width: 70px;">导出</el-button>
                </span>
            </div>
            <div class="flexrow" style="font-size: 12px;line-height: 30px; margin-top: 10px; margin-bottom: 10px;">
                <span style="margin-right:0.4%;"> <!-- 库存区间 -->
                    库存:
                    <el-input-number placeholder="最小" :min=-99999999 :max=99999999 :precision="0"
                        v-model="filter.stockQtyMin" style="width: 140px"></el-input-number>
                    至
                    <el-input-number placeholder="最大" :min=-99999999 :max=99999999 :precision="0"
                        v-model="filter.stockQtyMax" style="width: 140px"></el-input-number>
                </span>
                <span style="margin-right:0.4%;"> <!-- 成本价区间 -->
                    成本价:
                    <el-input-number placeholder="最小" :min=-99999999 :max=99999999 :precision="4"
                        v-model="filter.costPriceMin" style="width: 140px"></el-input-number>
                    至
                    <el-input-number placeholder="最大" :min=-99999999 :max=99999999 :precision="4"
                        v-model="filter.costPriceMax" style="width: 140px"></el-input-number>
                </span>
                <span style="margin-right:0.4%;"> <!-- 库存资金区间 -->
                    库存资金:
                    <el-input-number placeholder="最小" :min=-99999999 :max=99999999 :precision="4"
                        v-model="filter.stockAllCostMin" style="width: 140px"></el-input-number>
                    至
                    <el-input-number placeholder="最大" :min=-99999999 :max=99999999 :precision="4"
                        v-model="filter.stockAllCostMax" style="width: 140px"></el-input-number>
                </span>
                <span> <!-- 标签 -->
                    <el-select style="width:160px" placeholder="标签" v-model.trim="filter.label" :collapse-tags="true" clearable>
                        <el-option label="无标签" value="无标签"></el-option>
                        <el-option label="春季款" value="春季款"></el-option>
                        <el-option label="夏季款" value="夏季款"></el-option>
                        <el-option label="秋季款" value="秋季款"></el-option>
                        <el-option label="冬季款" value="冬季款"></el-option>
                        <el-option label="四季款" value="四季款"></el-option>
                        <el-option label="开学季" value="开学季"></el-option>
                        <el-option label="国庆节" value="国庆节"></el-option>
                        <el-option label="年货" value="年货"></el-option>
                        <el-option label="春节" value="春节"></el-option>
                    </el-select>
                </span>
            </div>
        </template>
        <vxetablebase :id="'unsalableDtl20250419'" ref="tabulartable" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' @sortchange='sortChange' :showsummary="true" :summaryarry='summaryarry'
            :tableData='dataList' :tableCols='tableCols' :isSelection="false" :loading="listLoading"
            :isSelectColumn="false" style="width: 100%;margin: 0" @summaryClick='onsummaryClick'>
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="dataTotal" @page-change="pagechange" @size-change="sizechange" />
        </template>

        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false"
            direction="btt">
            <div v-if="!chatProp.chatLoading">
                <div style="width:220px;">
                    <dateRange :startDate.sync="chatProp.yearMonthDayStart" :endDate.sync="chatProp.yearMonthDayEnd"
                        @change="changeChart" :clearable="false" />
                </div>
                <buschar v-if="!chatProp.chatLoading" :analysis-data="chatPropData" />
            </div>
            <div v-else v-loading="chatProp.chatLoading" />
        </el-drawer>
    </my-container>
</template>
<script>

import { 
    getPurchaseNewInventoryUnsalableDtlPage, 
    exportPurchaseNewInventoryUnsalableDtl,
    getPurchaseNewInventoryUnsalableDtlAnalysis 
} from '@/api/inventory/purchaseordernew'
import { formatTime } from "@/utils/tools";
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import dayjs from "dayjs";
import dateRange from "@/components/date-range/index.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import buschar from "@/components/Bus/buschar";

const tableCols = [
    { istrue: true, prop: 'yearMonthDay', label: '日期', width: 'auto', formatter: (row) => formatTime(row.yearMonthDay, 'YYYY-MM-DD') },
    { istrue: true, prop: 'styleCode', label: '款式编码', width: 'auto', sortable: 'custom',summaryEvent: true },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: 'auto', sortable: 'custom',summaryEvent: true },
    { istrue: true, prop: 'stockQty', label: '库存', width: 'auto', sortable: 'custom',summaryEvent: true },
    { istrue: true, prop: 'costPrice', label: '成本价', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'monthSellStockAllCost', label: '库存资金', width: 'auto', sortable: 'custom',summaryEvent: true },
    { istrue: true, prop: 'goodsAddTime', label: '创建时间', width: 'auto', sortable: 'custom', formatter: (row) => formatTime(row.goodsAddTime, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'label', label: '标签', width: 'auto', sortable: 'custom' }
];

export default {
    name: "UnsalableDtl",
    components: { MyContainer, vxetablebase, dateRange, inputYunhan, buschar },
    data() {
        return {
            that: this,
            filter: {
                orderBy: null,
                isAsc: false,
                currentPage: 1,
                pageSize: 50,
                yearMonthDay: '',
                hourType: '上午',
                startTime: null,
                endTime: null,
                styleCode: null,
                goodsCode: null,
                stockQtyMin: undefined,
                stockQtyMax: undefined,
                costPriceMin: undefined,
                costPriceMax: undefined,
                stockAllCostMin: undefined,
                stockAllCostMax: undefined,
                label: null
            },
            tableCols: tableCols,
            dataList: [],
            dataTotal: 0,
            summaryarry: {},
            pageLoading: false,
            listLoading: false,
            chatPropData: {},
            chatProp:{
                chatDialog: false,
                chatLoading: false,
                yearMonthDayStart: null,
                yearMonthDayEnd: null
            },
            isExport: false
        }
    },
    async mounted() {
        let today = new Date();
        this.filter.yearMonthDay = formatTime(today, "YYYY-MM-DD")
    },
    methods: {
        async getList() {
            this.listLoading = true;
            var res = await getPurchaseNewInventoryUnsalableDtlPage(this.filter);
            if (res?.success) {
                this.dataList = res.data.list;
                this.dataTotal = res.data.total;
                this.summaryarry = res.data.summary;
            }
            this.listLoading = false;
        },
        async onExport() {
            this.isExport = true
            await exportPurchaseNewInventoryUnsalableDtl(this.filter).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '滞销明细' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        sortChange({ order, prop }) {
            if (prop) {
                this.filter.orderBy = prop
                this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList();
            }
        },
        pagechange(val) {
            this.filter.currentPage = val;
            this.getList();
        },
        sizechange(val) {
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.getList();
        },
        async onsummaryClick() {
            this.chatProp.yearMonthDayStart = dayjs(this.filter.yearMonthDay).subtract(30, 'day').format('YYYY-MM-DD')
            this.chatProp.yearMonthDayEnd = dayjs(this.filter.yearMonthDay).format('YYYY-MM-DD')
            this.chatProp.hourType = this.filter.hourType;
            this.chatProp.startTime = this.filter.startTime;
            this.chatProp.endTime = this.filter.endTime;
            this.chatProp.styleCode = this.filter.styleCode;
            this.chatProp.goodsCode = this.filter.goodsCode;
            this.chatProp.stockQtyMin = this.filter.stockQtyMin;
            this.chatProp.stockQtyMax = this.filter.stockQtyMax;
            this.chatProp.costPriceMin = this.filter.costPriceMin;
            this.chatProp.costPriceMax = this.filter.costPriceMax;
            this.chatProp.stockAllCostMin = this.filter.stockAllCostMin;
            this.chatProp.stockAllCostMax = this.filter.stockAllCostMax;
            this.chatProp.label = this.filter.label;
            this.chatProp.chatDialog = true;
            this.chatProp.chatLoading = true;
            await this.changeChart();
        },
        async changeChart(e) {
            this.chatProp.chatLoading = true;
            const { data, success } = await getPurchaseNewInventoryUnsalableDtlAnalysis(this.chatProp);
            if (!success) return
            this.chatPropData = data;
            this.chatProp.chatLoading = false;
        },
        callbackGoodsCode(val, type) {
            const map = {
                styleCode: () => (this.filter.styleCode = val),
                goodsCode: () => (this.filter.goodsCode = val),
            };
            map[type]?.();
        },
    }
}

</script>
<style lang="scss" scoped>
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 160px;
        margin: 0 5px 5px 0px;
    }
}
.flexrow {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}
</style>