<template>
    <my-container>
    <div
      :style="showgrid ? 'display:none;' : ''"
      style="display: flex; flex-wrap: wrap"
    >
      <div
        v-for="obj in listTree"
        el-col
        span="8"
        style="padding-left: 0.2rem; padding-top: 0.3rem"
        v-bind:key="obj.proCode"
      >
        <el-card body-style="padding: 0px">
          <img
            v-if="obj.images"
            :src="obj.images"
            style="height:200px; max-width: 200px"
            
            :data-endTime="obj.onTime"
            class="image"
            
          />
          <!-- :data-html="obj.html" -->
          <img
            v-if="!obj.images"
            src="https://img.alicdn.com/tfs/TB1614dxqL7gK0jSZFBXXXZZpXa-800-800.png"
            style="height:200px; max-width: 200px"

            :data-endTime="obj.onTime"
            class="image"
            
          />
          <div style="padding: 5px">
            <div style="width: 190px">
              <div
                style="font-size: 13px;">
                <table borderColor="#000" :cellspacing="0" :border="1">
                  <tr><td>商品名</td><td>{{ obj.title | ellipsis}}</td></tr>
                  <tr><td>上架时间</td><td>{{ obj.onTime }}</td></tr>
                  <tr><td>小组</td><td>{{obj.groupName}}</td></tr>
                  <tr><td>店铺</td><td>{{obj.shopName}}</td></tr>
                  <tr><td>上新/重开</td><td>{{obj.newPattern}}</td></tr>
                  <tr><td>ID</td><td>{{ obj.proCode }}</td></tr>
                  <tr><td>仓库</td><td @click="selectck(obj.proCode)" :class="{blue:obj.wmsName==''}">{{ obj.wmsName?obj.wmsName:'请选择仓库' }}</td></tr>
                </table>
                </div>
            </div>
            <div class="bottom clearfix">
              <div style="font-size: 13px">{{ obj.endTime }}</div>
              <!-- <el-button type="text" class="button">操作按钮</el-button> -->
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <div :style="showgrid ? '' : 'display:none;'" style="height: 680px">
      <ces-table
        ref="table"
        style="height: 700px"
        :that="that"
        :isIndex="false"
        :tableData="listTree"
        :tableCols="tableCols"
        :tableHandles="tableHandles"
        :loading="listLoading"
        @sortchange="sortchange"
        :showsummary="false"
      />
    </div>

    <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length"  @get-page="onSearch"/>
    </template>

    <el-dialog
      title="选择仓库"
      :visible.sync="dialogVisible"
      width="30%">
      <el-card><div class="flexalign" >
        
        <el-radio-group v-model="radio" v-for="(item,i) in ckname" :key="i">
          <el-radio style="margin-top: 10px;" @change="selinput(item.wms_co_id)"  :label="item.wms_co_id" >{{item.name}}</el-radio>
        </el-radio-group>
      
      </div></el-card>
    </el-dialog>
    </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import cesTable from "@/components/Table/table.vue";
import {pageProductNewAsync, getProductNewImgList,updateProductCoIdAsync} from '@/api/operatemanage/base/product'
import {getAllWarehouse} from '@/api/inventory/warehouse'

const tableCols = [  
];

const tableHandles1 = [
  //{label:"下载导入类目模板", handle:(that)=>that.onDownMuban()},{label:"导入产品类目", handle:(that)=>that.onImportCategory()}
];

export default {
    name: 'YunhanAdminProductnewimg',
    components : {MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable},
    props:{
        filter:{}
    },
    filters: {
    ellipsis (value) {
      if (!value) return ''
      if (value.length >13) {
        return value.slice(0,13) + '...'
      }
      return value
      }
    },
    data() {
        return {
            that:this,
            list:[],
            listTree:[],
            pager:{OrderBy:"onTime",IsAsc:false},
            tableCols:tableCols,
            tableHandles:tableHandles1,
            total: 0,
            sels: [],
            showgrid: false,
            listLoading: false,
            dialogVisible: false,
            radio: null,
            ckname: [],
            shopId: null
        };
    },

    async mounted() {
        await this.onSearch()
    },

    methods: {
        async selinput(val){
          //仓库ID
          let _this = this;

          // console.log("仓库id",val)
          // console.log("商品id",_this.shopId)
          var res = await updateProductCoIdAsync({
            proCode: _this.shopId,
            wms_co_id: val
          });

          if(res.success){
            await this.onSearch()
          }else{
            this.$message("选择仓库失败！请重试")
          }

          console.log("返回值",res)


          _this.dialogVisible =false
        },
        async selectck(val){
          let _this = this;
          // 商品id
          _this.shopId = val;

          _this.radio = null;
          _this.dialogVisible =true
          _this.listLoading = true
          var res = await getAllWarehouse();
          _this.listLoading = true

          _this.ckname = res.data;
        },
        async onSearch() {
            await this.getlist()
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page  = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...pager,...page,... this.filter}
            if(params===false){
                return;
            }
            this.listLoading = true
            var res = await getProductNewImgList(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;

            this.listTree = data
        },
        sortchange(column) {
          if (!column.order) this.pager = {};
          else
            this.pager = {
              OrderBy: column.prop,
              IsAsc: column.order.indexOf("descending") == -1 ? true : false,
            };
          this.$refs.pager.setPage(1)
          this.onSearch();
      },
    },
};
</script>

<style lang="scss" scoped>
.flexalign{
  display: flex;
  flex-direction: column;
}
.blue{
  color: blue;
}
</style>