<template>
  <MyContainer>
    <el-tabs v-model="activeName" style="height: 95%;">
      <el-tab-pane label="扫码操作" name="first" style="height: 100%;">
        <cloudWarehouseOperateUser />
      </el-tab-pane>
      <el-tab-pane label="云仓退件" name="second" :lazy="true" style="height: 100%;">
        <cloudWarehouseWarehouseReturns />
      </el-tab-pane>
    </el-tabs>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import cloudWarehouseOperateUser from "./cloudWarehouseOperateUser.vue";
import cloudWarehouseWarehouseReturns from "./cloudWarehouseWarehouseReturns.vue";
export default {
  components: {
    MyContainer, cloudWarehouseOperateUser, cloudWarehouseWarehouseReturns
  },
  data() {
    return {
      activeName: 'first'
    };
  },
  methods: {

  }
};
</script>

<style lang="scss" scoped></style>
