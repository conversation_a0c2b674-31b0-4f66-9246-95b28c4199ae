<template>
  <div style="height: 705px;padding: 0 5px" v-loading="createLoading">
    <div>
      <el-form :model="ruleForm" :rules="editrules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-row>
          <el-col :span="8">
            <el-form-item label="快递公司" :label-width="'90px'" prop="expressCompanyId">
              <el-select v-model="ruleForm.expressCompanyId" placeholder="快递公司" class="editCss" clearable
                @change="getprosimstatelist(1)" :disabled="forbidden">
                <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="付款类型" :label-width="'90px'" prop="paymentType">
              <el-select v-model="ruleForm.paymentType" placeholder="付款类型" class="editCss" clearable filterable
                @change="onPaymentChange" :disabled="forbidden">
                <el-option label="三日结" value="三日结" />
                <el-option label="预付款" value="预付款" />
              </el-select>
            </el-form-item>
            <el-form-item label="数量" :label-width="'90px'" prop="quantity">
              <el-input-number v-model.trim="ruleForm.quantity" placeholder="数量" :min="-*********" :max="*********"
                :disabled="paymentVerify || forbidden" :precision="0" :controls="false" class="editCss"
                @change="onRemarksEcho" />
            </el-form-item>
            <el-form-item label="支付方式" :label-width="'90px'" prop="paymentMethod">
              <el-select v-model="ruleForm.paymentMethod" placeholder="支付方式" class="editCss" clearable filterable
                @change="onPayMethod" :disabled="forbidden">
                <el-option v-for="item in paymentList" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="快递站点" :label-width="'90px'" prop="prosimstateId">
              <el-select v-model="ruleForm.prosimstateId" placeholder="请选择快递站点" clearable class="editCss"
                @change="onTakePayWay" :disabled="forbidden">
                <el-option label="暂无站点" value="" />
                <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="费用分类" :label-width="'90px'" prop="costClassification">
              <el-select v-model="ruleForm.costClassification" placeholder="费用分类" class="editCss" clearable filterable
                :disabled="forbidden">
                <el-option label="面单费" value="面单费" />
                <el-option label="续重费" value="续重费" />
                <el-option label="运费" value="运费" />
              </el-select>
            </el-form-item>
            <el-form-item label="单价" :label-width="'90px'" prop="unitPrice">
              <el-input-number v-model.trim="ruleForm.unitPrice" placeholder="单价" :min="-*********" :max="*********"
                :precision="2" :controls="false" class="editCss" @change="onPUnitPriceChange" :disabled="forbidden" />
            </el-form-item>
            <el-form-item label="账户名" :label-width="'90px'" prop="accountName">
              <el-input v-model.trim="ruleForm.accountName" placeholder="请输入账户名" maxlength="50" clearable
                class="editCss" :disabled="forbidden" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发货仓库" :label-width="'90px'" prop="warehouseId">
              <el-select v-model="ruleForm.warehouseId" clearable filterable placeholder="请选择发货仓库" class="editCss"
                @change="onTakePayWay" :disabled="forbidden">
                <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="仓库分类" :label-width="'90px'" prop="warehouseClassification">
              <el-select v-model="ruleForm.warehouseClassification" clearable filterable placeholder="请选择仓库分类"
                class="editCss" :disabled="forbidden">
                <el-option v-for="item in warehouseOptions" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
            <el-form-item label="金额(元)" :label-width="'90px'" prop="amount">
              <el-input-number v-model.trim="ruleForm.amount" placeholder="金额(元)" :min="-*********" :max="*********"
                :precision="2" :controls="false" class="editCss" @change="onRemarksEcho" :disabled="forbidden" />
            </el-form-item>
            <el-form-item label="账号" :label-width="'90px'" prop="accountNumber">
              <el-input v-model.trim="ruleForm.accountNumber" placeholder="请输入账号" maxlength="50" clearable
                class="editCss" :disabled="forbidden" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开户行" :label-width="'90px'" prop="bankName">
              <el-input v-model.trim="ruleForm.bankName" placeholder="请输入开户行" maxlength="50" clearable class="editCss"
                :disabled="forbidden" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="快递公司名称" :label-width="'110px'" prop="expressCompanyFullName">
              <el-input v-model.trim="ruleForm.expressCompanyFullName" placeholder="请输入快递公司名称" maxlength="50" clearable
                class="editCss" :disabled="forbidden" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-form-item label="备注" :label-width="'90px'" prop="remark">
            <el-input type="textarea" v-model.trim="ruleForm.remark" placeholder="请输入备注" maxlength="200" clearable
              class="editCss" resize="none" :disabled="forbidden" />
          </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="截图" :label-width="'90px'" prop="screenshot">
              <div class="containerCss">
                <uploadimgFile ref="uploadimgFile" v-if="subcomponent" :disabled="isView || forbidden"
                  :reveal="!forbidden" :ispaste="!isView || forbidden" :noDel="isView || forbidden"
                  :accepttyes="accepttyes" :isImage="true" :uploadInfo="ruleForm.screenshot" :keys="[1, 1]"
                  @callback="getImg" @beforeUpload="beforeUpload" :imgmaxsize="6" :limit="6" :multiple="true"
                  :minisize='false'>
                </uploadimgFile>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="附件" :label-width="'90px'" prop="attachment">
              <div style="max-height: 90px; width: 100px;height: 90px;">
                <uploadimgFile ref="uploadimgFile1" v-if="subcomponent" :disabled="forbidden"
                  :noDel="formEditMode || forbidden" :reveal="false" :uploadInfo="ruleForm.attachment" :keys="[1, 1]"
                  :filemaxsize="1" :imgmaxsize="5" :minisize='false'>
                </uploadimgFile>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div style="padding-left: 1%;">
      <el-date-picker style="width: 150px;margin-right: 5px;" v-model="yearMonthDay" type="date" placeholder="选择日期"
        format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="changeTime" :disabled="forbidden">
      </el-date-picker>
      <el-button type="primary" @click="onGetDetails" :disabled="forbidden">获取明细</el-button>
      <el-button :loading="createLoading" :disabled="createLoading" @click="tocreateimg">{{ createLoading ? '上传中' : '截图'
        }}</el-button>
    </div>
    <el-table v-show="!cutting" :data="ruleForm.detailList" style="width: 100%" height="280px" v-loading="loading">
      <el-table-column prop="yearMonthDayDtae" label="日期" width="300">
        <template slot-scope="scope">
          <div>
            {{ formatDate(scope.row.yearMonthDayDtae) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="payableAmount" label="应付金额">
        <template slot-scope="scope">
          <div>
            {{ scope.row.payableAmount }}
            <!-- <el-input-number v-model.trim="scope.row.payableAmount" placeholder="应付金额" :min="-*********"
              @change="onAmountPayableChange" :max="*********" :precision="2" :controls="false" style="width: 60%;" /> -->
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="100">
        <template slot-scope="scope">
          <el-button type="text" size="small">
            <div style="color: red;" @click="onEditDelete(scope.row)" :disabled="forbidden">删除</div>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div v-show="cutting" style="width: 810px;height: 250px;overflow-x: auto;">
      <div id="oneboxx" style="width: 100%;">
        <el-table :data="screenshotData">
          <el-table-column prop="yearMonthDayDtae" label="日期" width="95">
            <template slot-scope="scope">
              <div>
                {{ formatDate(scope.row.yearMonthDayDtae) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="expressCompany" label="快递公司" width="50" />
          <el-table-column prop="prosimstate" label="快递站点" :width="verifyGiaDinh || verifyJiangmen ? '90' : '100'" />
          <el-table-column prop="warehouseName" label="发货仓库" :width="verifyGiaDinh || verifyJiangmen ? '90' : '100'" />
          <el-table-column prop="receiveCount" label="收寄量" :width="verifyGiaDinh || verifyJiangmen ? '90' : '100'" />
          <el-table-column prop="expressFee" label="邮费" :width="verifyGiaDinh || verifyJiangmen ? '90' : '100'" />
          <el-table-column prop="czFee" label="操作费" :width="verifyGiaDinh || verifyJiangmen ? '90' : '100'" />
          <el-table-column prop="packageFee" label="包材费" :width="verifyGiaDinh || verifyJiangmen ? '85' : '105'" />
          <el-table-column prop="sjTotalFee" label="运费总额" :width="verifyGiaDinh || verifyJiangmen ? '90' : '100'" />
          <el-table-column prop="yearMonthDayDtae" label="运费均值" width="75">
            <template slot-scope="scope">
              <div>
                {{  scope.row.receiveCount !=0? (scope.row.sjTotalFee/scope.row.receiveCount).toFixed(2):"" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="claimAmount" label="理赔" :width="verifyGiaDinh || verifyJiangmen ? '85' : '105'" />
          <el-table-column prop="addFee" label="加收" :width="verifyGiaDinh || verifyJiangmen ? '85' : '105'" />
          <el-table-column prop="diffWeight" label="重量差" :width="verifyGiaDinh || verifyJiangmen ? '85' : '105'" />
          <el-table-column prop="platformDeduction" label="平台扣款"
            :width="verifyGiaDinh || verifyJiangmen ? '85' : '105'" />
          <el-table-column prop="adjustmentAmount" label="调整金额"
            :width="verifyGiaDinh || verifyJiangmen ? '90' : '100'" />
          <el-table-column prop="payableAmount" label="应付款金额" :width="verifyGiaDinh || verifyJiangmen ? '90' : '100'" />
          <el-table-column prop="hadPayAmount" label="已付款金额" :width="verifyGiaDinh || verifyJiangmen ? '90' : '110'" />
          <el-table-column prop="payTime" label="付款时间" width="95" />
          <el-table-column prop="residualAmount" label="剩余金额" :width="development.rebateAmount ? '90' : '110'" />
          <el-table-column prop="rebateAmount" label="返款金额" width="75" v-if="development.rebateAmount" />
          <el-table-column prop="rebateAmountAdjust" label="返款金额" width="75" v-if="development.rebateAmount" />
          <el-table-column prop="isRebateAmount" label="返款金额调整" width="75" v-if="development.isRebateAmount" />
          <el-table-column prop="noRebateAmount" label="未返款余额" width="90" v-if="development.noRebateAmount" />
          <el-table-column prop="isPayOperatingCost" label="已付操作费（元）" width="75"
            v-if="development.isPayOperatingCost" />
          <el-table-column prop="noPayOperatingCost" label="未付操作费（元）" width="80"
            v-if="development.noPayOperatingCost" />
          <el-table-column prop="remark" label="备注" width="75" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { addOrUpdateExpressFeeRecharge, deleteExpressFeeRecharge, getExpressFeeRechargeList, getExpressComanyAll, getExpressComanyStationName, getExpressDayBillsSummary, getExpressBankInfoList, getExpressFeeRecharge } from "@/api/express/express";
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import decimal from "@/utils/decimalToFixed"
import { warehouselist, formatWarehouseNew } from "@/utils/tools";
import dayjs from 'dayjs'
import html2canvas from 'html2canvas';
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'

const warehouseOptions = [
  '义乌仓',
  '南昌仓东',
  '罗兵云仓',
  '上海嘉定仓',
  '代发仓',
  '杭州仓',
  '西安港务仓',
  '广东江门仓',
  '江苏昆山仓',
  '广东东莞仓',
  '安徽仓',
  '湖南云仓',
  '苏州云仓',
  '西安威阳仓（西安分仓)',
  '西安仓',
  '江苏常熟仓',
  '江西抚州仓',
  '上海金山仓',
  '其他',
]

export default {
  name: 'topUpApproval',
  components: {
    uploadimgFile
  },
  props: {
    ListInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    //表单数据
    infoFormData: {
      type: Object,
      default() {
        return {}
      }
    },
    //是否禁用(默认false)
    forbidden: {
      type: Boolean,
      default() {
        return false
      }
    },
    //是否为新增，编辑（true:编辑 false:新增）
    verifyWhether: {
      type: Boolean,
      default() {
        return false
      }
    },
  },
  data() {
    return {
      development: {
        isPayOperatingCost: false,
        noPayOperatingCost: false,
        noRebateAmount: false,
        isRebateAmount: false,
        rebateAmount: false,
      },
      verifyGiaDinh: false,
      verifyJiangmen: false,
      screenshotData: [],//截图数据
      createLoading: false,//上传loading
      cutting: false,//截图
      loading: false,
      subcomponent: false,
      warehouselist,
      yearMonthDay: null,
      expresscompanylist: [],//快递公司
      prosimstatelist: [],//快递站点
      echoData: [],
      paymentList: [],
      editTimeRanges: [],
      paymentVerify: false,
      warehouseOptions,
      isView: false,
      accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
      formEditMode: false,
      ruleForm: {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
        warehouseClassification: null,
        accountName: null,
        accountNumber: null,
        bankName: null,
        expressCompanyFullName: null,
        attachment: [],
        screenshot: [],
        amount: 0,
        unitPrice: 0,
        quantity: 0,
        paymentType: null,
        paymentMethod: null,
        costClassification: null,
        remark: null,
        startTime: null,
        endTime: null,
      },
      editrules: {
        expressCompanyId: [{ required: true, message: '请选择快递公司', trigger: 'blur' }],
        prosimstateId: [{ required: true, message: '请选择快递站点', trigger: 'blur' }],
        costClassification: [{ required: true, message: '请选择费用分类', trigger: 'blur' }],
        warehouseId: [{ required: true, message: '请选择发货仓库', trigger: 'blur' }],
        warehouseClassification: [{ required: true, message: '请输入仓库分类', trigger: 'blur' }],
        accountName: [{ required: true, message: '请输入账户名', trigger: 'blur' }],
        accountNumber: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        bankName: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
        amount: [{ required: true, message: '请输入金额', trigger: 'blur' }],
        unitPrice: [{ required: true, message: '请输入单价', trigger: 'blur' }],
        quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }],
        paymentType: [{ required: true, message: '请选择付款类型', trigger: 'blur' }],
        paymentMethod: [{ required: true, message: '请选择支付方式', trigger: 'blur' }],
        expressCompanyFullName: [{ required: true, message: '请输入快递公司名称', trigger: 'blur' }],
      },
    }
  },

  watch: {
    ruleForm: {
      handler: function (newValue, oldValue) {
        this.$emit("update:valueChanged", newValue);
      },
      deep: true
    }
  },

  async mounted() {
    this.onCleardataMethod()
    if (this.infoFormData && Object.keys(this.infoFormData).length > 0) {
      let id = this.infoFormData.expressCompanyId
      let res = await getExpressComanyStationName({ id: id });
      if (res?.code) {
        this.prosimstatelist = res.data
      }
      let transferForm = JSON.parse(JSON.stringify(this.infoFormData))
      this.$nextTick(() => {
        this.ruleForm = this.transformFormData(transferForm);
        const setDevelopmentFlags = (condition, flags, verifyFlag) => {
          Object.keys(flags).forEach(key => {
            this.development[key] = condition;
          });
          this[verifyFlag] = condition;
        };
        setDevelopmentFlags(
          this.ruleForm.warehouseId == 37 && this.ruleForm.prosimstateId == '77' && this.ruleForm.expressCompanyId == '6',
          { rebateAmount: true, isRebateAmount: true, noRebateAmount: true },
          'verifyJiangmen'
        );
        setDevelopmentFlags(
          this.ruleForm.warehouseId == 33 && this.ruleForm.prosimstateId == '58' && this.ruleForm.expressCompanyId == '6',
          { isPayOperatingCost: true, noPayOperatingCost: true },
          'verifyGiaDinh'
        );
        this.onTakePayWay()//获取支付方式
        this.onPaymentChange(this.ruleForm.paymentType ? this.ruleForm.paymentType : this.ruleForm.payType)//付款类型
      })
      if (this.verifyWhether) {
        const { data, success } = await getExpressFeeRecharge({ id: this.infoFormData.id })
        if (!success) return
        this.ruleForm.detailList = data ? data : []
        let timeRange = this.ruleForm.detailList.map(item => {
          return item.yearMonthDayDtae
        })
        timeRange = Array.from(new Set(timeRange))
        if (timeRange.length === 0) {
          this.yearMonthDay = null;
        } else {
          this.yearMonthDay = this.formatDate(timeRange[0]).toString();
        }
        this.onRemarksEcho()
      }
    }
    await this.init()
    this.subcomponent = true
    this.$forceUpdate()
  },
  methods: {
    dataURLtoFile(dataurl, filename) {
      const arr = dataurl.split(',');
      const mimeMatch = arr[0].match(/:(.*?);/);
      if (!mimeMatch) {
        throw new Error('Invalid data URL');
      }
      const mime = mimeMatch[1];
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], filename, { type: mime });
    },
    async tocreateimg() {
      if (this.yearMonthDay == null) {
        this.$message.error('请选择日期')
        return
      }
      if (this.ruleForm.screenshot.length >= 6) {
        this.$message.error('最多上传6张截图')
        return
      }
      this.createLoading = true
      this.cutting = true
      let startTimeDay = null;
      let endTimeDay = null;
      if (this.yearMonthDay) {
        startTimeDay = dayjs(this.yearMonthDay).startOf('month').format('YYYY-MM-DD');
        endTimeDay = dayjs(this.yearMonthDay).endOf('month').format('YYYY-MM-DD');
      }
      this.screenshotData = []
      const { data, success } = await getExpressDayBillsSummary({
        expressName: this.ruleForm.expressCompanyId,
        prosimstate: this.ruleForm.prosimstateId,
        warehouse: this.ruleForm.warehouseId,
        startTime: startTimeDay,
        endTime: endTimeDay,
      })
      if (!success) return
      this.screenshotData = data.list
      this.screenshotData.forEach(item => {
        item.payTime = item.payTime ? dayjs(item.payTime).format('YYYY-MM-DD') : ''
      })
      this.subcomponent = false
      setTimeout(() => {
        const element = document.querySelector('#oneboxx');
        element.style.width = '1900px';
        if (element) {
          html2canvas(element, {
            allowTaint: true,
            useCORS: true,
          }).then(async (canvas) => {
            const imgData = canvas.toDataURL('image/png');
            // 将 base64 转换为文件对象
            const imgFile = this.dataURLtoFile(imgData, `${new Date().getTime()}.png`);
            // 上传图片到服务器
            const formData = new FormData();
            formData.append('data', imgFile); // 文件对象
            formData.append('batchnumber', ''); // 示例：添加其他表单字段
            formData.append('fileName', imgFile.name); // 名称
            formData.append('total', '1');
            formData.append('index', '1');
            try {
              const res = await xMTVideoUploadBlockAsync(formData); // 上传接口
              if (res.success) {
                this.$message.success('图片上传成功');
                const imgUrlGenerate = res.data.url;
                this.ruleForm.screenshot.push({ url: imgUrlGenerate });
                this.subcomponent = true
                this.cutting = false;
                this.createLoading = false;
              } else {
                this.$message.error('图片上传失败');
              }
            } catch (error) {
              this.$message.error('图片上传失败');
            }
          });
        } else {
          console.error('Element #oneboxx not found');
        }
      }, 100);
    },
    formatDate(date) {
      return dayjs(date).format('YYYY-MM-DD');
    },
    transformFormData(data) {
      return {
        ...data,
        costClassification: data.costClassification ? String(data.costClassification) : null,
        screenshot: data.screenshot ? JSON.parse(data.screenshot) : [],
        attachment: data.attachment ? JSON.parse(data.attachment) : [],
        expressCompanyId: data.expressCompanyId ? String(data.expressCompanyId) : null,
        prosimstateId: data.prosimstateId ? String(data.prosimstateId) : null,
        paymentType: data.paymentType ? String(data.paymentType) : data.payType ? String(data.payType) : null,
        amount: data.amount || 0,
        unitPrice: data.unitPrice || 0,
        remark: data.remark || null,
        quantity: data.quantity || 0
      };
    },
    onRemarksEcho() {
      let stationName = ''
      let remarks = ''
      let startTime = this.ruleForm.startTime ? dayjs(this.ruleForm.startTime).format('YYYY-MM-DD') : ''
      let endTime = this.ruleForm.endTime ? dayjs(this.ruleForm.endTime).format('YYYY-MM-DD') : ''
      let yearMonthDay = this.yearMonthDay ? dayjs(this.yearMonthDay).format('YYYY-MM-DD') : ''
      this.prosimstatelist.forEach(item => {
        if (item.id == this.ruleForm.prosimstateId) {
          stationName = item.stationName
        }
      })
      if (this.ruleForm.paymentType == '三日结') {
        remarks = stationName + ' ' + yearMonthDay + ' ' + '应付金额' + ' ' + this.ruleForm.amount + '元'
      } else if (this.ruleForm.paymentType == '预付款') {
        remarks = stationName + ' ' + '预充' + this.ruleForm.quantity + '单'
      } else {
        remarks = stationName
      }
      this.ruleForm.remark = remarks
      this.onPriceChange()//计算金额
      this.$forceUpdate()
    },
    onPayMethod(e) {
      this.echoData.forEach(item => {
        if (item.paymentMethod === e) {
          this.ruleForm.accountNumber = item.accountNumber
          this.ruleForm.accountName = item.accountName
          this.ruleForm.bankName = item.bankName
          this.ruleForm.expressCompanyFullName = item.expressCompanyFullName
        }
      })
    },
    async onTakePayWay() {
      if (this.ruleForm.expressCompanyId && this.ruleForm.prosimstateId && this.ruleForm.warehouseId) {
        const { data, success } = await getExpressBankInfoList({
          expressName: this.ruleForm.expressCompanyId,
          prosimstateId: this.ruleForm.prosimstateId,
          warehouseId: this.ruleForm.warehouseId,
        })
        if (!success) return
        this.echoData = data.list
        this.paymentList = this.echoData.map(item => item.paymentMethod)
      }
      this.onRemarksEcho()
    },
    onAmountPayableChange() {
      this.ruleForm.unitPrice = this.ruleForm.detailList.reduce((total, item) => {
        return total + Number(item.payableAmount)
      }, 0)
      this.onPriceChange()
      this.onRemarksEcho()
      this.$forceUpdate()
    },
    onEditDelete(row) {
      const index = this.ruleForm.detailList.findIndex(item => item === row);
      if (index !== -1) {
        this.ruleForm.detailList.splice(index, 1);
      }
      this.$forceUpdate()
    },
    async onGetDetails() {
      this.loading = true
      const { data, success } = await getExpressDayBillsSummary({
        expressName: this.ruleForm.expressCompanyId,
        prosimstate: this.ruleForm.prosimstateId,
        warehouse: this.ruleForm.warehouseId,
        startTime: this.ruleForm.startTime ? this.ruleForm.startTime : null,
        endTime: this.ruleForm.endTime ? this.ruleForm.endTime : null,
      })
      this.loading = false
      if (!success) return
      this.ruleForm.detailList = data.list
      this.ruleForm.detailList.forEach(item => {
        item.yearMonthDayDtae = dayjs(item.yearMonthDayDtae).format('YYYY-MM-DD')
        item.payableAmount = item.payableAmount ? item.payableAmount : 0
      })
      this.ruleForm.unitPrice = this.ruleForm.detailList.reduce((total, item) => {
        return total + Number(item.payableAmount)
      }, 0)
      this.ruleForm.amount = decimal(this.ruleForm.unitPrice, this.ruleForm.quantity, '*', 2)
      this.onRemarksEcho()
      this.$forceUpdate()
    },
    onPUnitPriceChange(e) {
      this.onPriceChange()
      this.onRemarksEcho()
    },
    onPriceChange(e) {
      this.ruleForm.amount = decimal(this.ruleForm.unitPrice, this.ruleForm.quantity, '*', 2)
    },
    onPaymentChange(e) {
      //当为编辑时不需要判断
      if (!this.verifyWhether) {
        if (e == '三日结') {
          this.paymentVerify = true
          this.ruleForm.quantity = 1
        } else {
          this.paymentVerify = false
          this.ruleForm.quantity = 0
          this.ruleForm.startTime = null
          this.ruleForm.endTime = null
          this.ruleForm.detailList = []
          this.editTimeRanges = []
        }
      }
      this.onPriceChange()
      this.onRemarksEcho()
    },
    async changeTime(e) {
      this.ruleForm.startTime = this.yearMonthDay ? this.yearMonthDay : null
      this.ruleForm.endTime = this.yearMonthDay ? this.yearMonthDay : null
      this.onRemarksEcho()
    },
    beforeUpload(data) {
      if (data.length > 0) {
        this.ruleForm.screenshot = this.ruleForm.screenshot.concat(
          data.map(item => {
            return {
              url: item.url,
              name: item.name
            };
          })
        );
      }
    },
    getImg(data) {
      if (data) {
        this.ruleForm.screenshot = data.map(item => {
          return { url: item.url, name: item.fileName }
        })
      }
    },
    onSingleSave(val, argument) {
      let status = val//0:暂存 1:发起
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          let costsification = this.ruleForm.costClassification
          let costClassification = costsification
          const { success } = await addOrUpdateExpressFeeRecharge({
            ...this.ruleForm,
            status,
            attachment: JSON.stringify(this.ruleForm.attachment),
            screenshot: JSON.stringify(this.ruleForm.screenshot),
            costClassification,
            ...argument,
          })
          if (success) {
            this.$message.success(val == 0 ? '暂存成功' : '发起流程成功')
            this.$emit('closeCallback')
            this.subcomponent = false
          } else {
            this.$message.error('操作失败')
          }
        }
      })
    },
    onCleardataMethod() {
      this.ruleForm = {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
        warehouseClassification: null,
        accountName: null,
        accountNumber: null,
        bankName: null,
        expressCompanyFullName: null,
        prosimstateId: null,
        attachment: [],
        screenshot: [],
        amount: null,
        unitPrice: null,
        quantity: null,
        paymentType: null,
        paymentMethod: null,
        costClassification: null,
        remark: null,
        startTime: null,
        endTime: null,
      }
      this.editTimeRanges = []
    },
    async init() {
      const res = await getExpressComanyAll({});
      if (!res?.success) return
      this.expresscompanylist = res.data;
    },
    async getprosimstatelist(val) {
      let id;
      if (val == 1) {
        id = this.ruleForm.expressCompanyId
        this.ruleForm.prosimstateId = null
      } else if (val == 2) {
        id = this.ListInfo.expressName
        this.ListInfo.prosimstateId = null
      }
      let res = await getExpressComanyStationName({ id: id });
      if (res?.code) {
        this.prosimstatelist = res.data
      }
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 60px;
}

.editCss {
  width: 100%;
}

.containerCss {
  max-height: 90px;
  height: 90px;
  overflow-x: auto;
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep .el-select__tags-text {
  max-width: 30px;
}
</style>
