<template>
  <container>
    <ces-table ref="table" :that='that' :isIndex='true' @sortchange='sortchange' :isSelectColumn="false" @cellclick='cellclick'
         :hasexpand='true' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"
         :showsummary='true' :summaryarry='summaryarry'>
      </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

    <div class="imgDolg" v-show="imgPreview.show" @click.stop="imgPreview.show = false">
      <i class="el-icon-close" id="imgDolgClose" @click.stop="imgPreview.show = false"></i>
      <img @click.stop="imgPreview.show = true" :src="imgPreview.img" />
    </div>
    <el-drawer title="物流跟踪" :visible.sync="drawervisible" direction="rtl" :append-to-body="true">
      <logistics ref="logistics"></logistics>
    </el-drawer>
  </container>
</template>
<script>
import {pageGoodsCodeRecord} from '@/api/inventory/purchase'
import {formatTime,formatYesornoBool,formatPurchasePlanError,formatNoLink,formatIsError,formatIsOutStock,formatSecondToHour} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container/noheader";
import logistics from '@/components/Comm/logistics'
const tableCols =[
      {istrue:true,prop:'createdTime',label:'时间', width:'80', width:'105',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'MM-DD HH:mm:ss')},
      {istrue:true,prop:'createdUserName',label:'上报人', width:'63',},
      {istrue:true,prop:'type',label:'类型', width:'63',sortable:'custom',formatter:(row)=>{var str=''; if (row.type==1) str='采购建议'; else if (row.type==2) str= '压单异常'; else if (row.type==3) str='采购单'; return str;}},
      {istrue:true,prop:'goodsCode',label:'商品编码',  width:'100',sortable:'custom',},
      // {istrue:true,prop:'planError',label:'采购建议预警', width:'105',type:'html',formatter:(row)=>formatPurchasePlanError(row.planError)},
      {istrue:true,prop:'count',label:'数量',  width:'60'},
      {istrue:true,prop:'planArrivalTime',label:'预计到货日期',  width:'105',sortable:'custom',formatter:(row)=>formatTime(row.planArrivalTime,'YYYY-MM-DD')},
      {istrue:true,prop:'expressNo',label:'物流单号', width:'80',type:'html',formatter:(row)=>formatNoLink(row.expressNo)},
      {istrue:true,prop:'companyName',label:'物流公司', width:'80',},
      {istrue:true,prop:'reMark',label:'备注',  width:'*',type:'editor'},
     ];
const tableHandles=[ ];
export default {
  name: "goodscoderecord",
  components: {container,cesTable,logistics},
  props:{
     //filter:{goodsCode:"",buyNo:""},
  },
  data() {
    return {
      that:this,
      filter:{goodsCode:"",buyNo:""},
      imgPreview:{img:"",show:false},
      list: [],
      oderDetailView:{},
      drawervisible:false,
      visiblepopover: false,
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [],
      fileList:[],
      listLoading: false,
      dialogVisible: false,
      pageLoading: false,
    };
  },
//  watch:{
//     filter:function(val,oldval){
//          this.$nextTick(function(){
//             this.onSearch(val );
//        })
//     }
//   },
 async mounted() {
  },
 methods: {
   async onSearch(goodsCode,buyNo) {
       this.filter={goodsCode:goodsCode,buyNo:buyNo},
       this.$refs.pager.setPage(1)
       this.getlist()
    },
  async getlist() {
      if (!this.pager.OrderBy) this.pager.OrderBy="";
       var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,...this.filter}
       this.listLoading = true
      this.list=[];
      var res = await pageGoodsCodeRecord(params)
      this.listLoading = false
      console.log('this.res',res)
     // if(!(res.code==1&&res.data)) return
      this.total = res.data.total
      const data = res.data.list
      this.list = data
      console.log('this.list',this.list)
    },
  async cellclick(row, column, cell, event){
     if (column.property=='expressNo'&&row.expressNo)
        await this.showlogistics(row.companyCode,row.expressNo);
    },
  async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch(this.filter.goodsCode,this.filter.buyNo);
    },
   async showImg(e) {
      if (e.target.tagName == 'IMG') {
        this.imgPreview.img = e.target.src
        this.imgPreview.show = true
      }
    },
   async showlogistics(companycode,number){
      this.drawervisible=true;
       this.$nextTick(function(){
         this.$refs.logistics.showlogistics(companycode,number);
       })
    }
  },
};
</script>
<style lang="scss" scoped>
.imgDolg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 9999;
  background-color: rgba(140, 134, 134, 0.6);
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  #imgDolgClose {
    position: fixed;
    top: 35px;
    cursor: pointer;
    right: 7%;
    font-size: 50px;
    color: white;
  }
  img{
    width: 80%;
  }
}
</style>

