<template>
  <div style="height: 100%;">
     <template>
      <el-form class="ad-form-query" :model="filter" @submit.native.prevent label-width="100px">
        <el-form-item label="选择竞品:">
            <el-select v-model="filter1.jpProCodes" multiple filterable allow-create default-first-option clearable placeholder="请选择竞品" class="el-select-content">
              <el-option
                  v-for="item in jpprocodes"
                  :key="item.proCode"
                  :label="item.proName"
                  :value="item.proCode">
                  <span style="float: left">{{ item.shopName+' --' }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ item.proName }}</span>
              </el-option>
            </el-select>
         </el-form-item>
         <el-row>
             <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="左边轴显示:">
                  <el-select v-model="filter1.leftY" placeholder="请选择" class="el-select-content">
                      <el-option v-for="item in Ylist" :key="item.value" :label="item.label" :value="item.value"/>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                 <el-form-item label="右边轴显示:">
                   <el-select v-model="filter1.rightY" placeholder="请选择" class="el-select-content">
                      <el-option v-for="item in Ylist" :key="item.value" :label="item.label" :value="item.value"/>
                   </el-select>
                 </el-form-item>
              </el-col>
           </el-row>
        <el-form-item>
          <el-button type="primary" @click="onfresh">刷新</el-button>
        </el-form-item>
      </el-form>
      </template> 
       <div id="echartmonit" style="width: 100%;height: 389px; box-sizing:border-box; line-height: 389px;">     
      </div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
import {productMonitorAnalysis,getAllJpProducts} from '@/api/operatemanage/operate'
export default {
  name: 'Roles',
  components: { },
   props:{
       filter: { }
     },
  data() {
    return {
      filter1:{
        jpProCodes:[],
        leftY:0,
        rightY:1
      },
   Ylist:[
          {value:0,unit:"",label:"交易金额"},
          {value:1,unit:"",label:"访客人数"},
          {value:2,unit:"",label:"搜索人数"},
          {value:3,unit:"%",label:"搜索占比"},
          {value:4,unit:"%",label:"支付人数"},
          {value:5,unit:"%",label:"支付转化率"},
          {value:6,unit:"",label:"收藏人数"},
          {value:7,unit:"%",label:"收藏率"},
          {value:8,unit:"",label:"加购人数"},
          {value:9,unit:"%",label:"加购率"},
          {value:10,unit:"",label:"客单价"},
          {value:11,unit:"",label:"uv价值"}
         ],
      jpprocodes:[],
      pageLoading: false
    }
  },
  mounted() {
  },
  beforeUpdate() {
  },
methods: {
  async onSearch() {
    if (!this.filter.selfProCode){
        this.$message({message: "请先选择商品",type: "warning",});
        return;
      }
      if (this.filter1.jpProCodes.length==0) return;      
      this.getdata()
    },
  async onfresh() {
     if (this.filter1.jpProCodes.length==0) {
        this.$message({message: "请先选择竞品！",type: "warning",});
        return;
      }
      this.getdata()
    },
 async onpk(jpProCodes) {
    this.filter1.jpProCodes=jpProCodes;
    this.onfresh()
    },
   async initdata() {
      var hasparm=false;
      var arry= Object.keys(this.filter)
      if (arry.length==0)  return;
      for (let key of Object.keys(this.filter)) {
        if(this.filter[key])
            hasparm=true;
      }
      if(!hasparm) return; 
      const res = await getAllJpProducts({...this.filter,...{type:0}});
      if (!res?.code)  return; 
      this.jpprocodes= res.data;
    },
  async getdata() {
      var parm={...this.filter, ...this.filter1};
      if (parm.jpProCodes.length==0) return;
      var hasparm=false;
      var arry= Object.keys(parm)
      if (arry.length==0)  return;
      for (let key of Object.keys(parm)) {
        if(parm[key])
            hasparm=true;
      }
      if(!hasparm) return;
      const res = await productMonitorAnalysis(parm);      
      if (!res?.code)  return;       
      var chartDom = document.getElementById('echartmonit');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      if (!res.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
       }
      var option = this.Getoptions(res.data);
      //console.log(JSON.stringify(option))
      option && myChart.setOption(option); 
    },
  Getoptions(element){
     var series=[]
     element.series.forEach(s=>{
       series.push({  smooth: true, ...s})
     })
     var yAxis=[]
     this.Ylist.forEach(s=>{
       if (s.value==this.filter1.leftY)
          yAxis.push({ type: 'value',name: s.label,axisLabel: {formatter: '{value}'+s.unit}})
     })
     this.Ylist.forEach(s=>{
       if (s.value==this.filter1.rightY)
          yAxis.push({ type: 'value',name: s.label,axisLabel: {formatter: '{value}'+s.unit}})
     })      
     var option = {
        title: {text: element.title},
        tooltip: {trigger: 'axis'},
        legend: {
            data: element.legend
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {feature: {
            magicType: {show: true, type: ['line', 'bar']},
            //restore: {show: true},
        }},
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis: yAxis,
        series:  series
    };
    return option;
   },
  }
}
</script>
<style>
.el-select-content { 
    width: calc(100% - 10px);
    margin: 0;
 }
 
</style>
