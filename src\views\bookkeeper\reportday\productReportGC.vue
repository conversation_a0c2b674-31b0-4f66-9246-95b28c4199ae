<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-button-group>
        <el-button style="padding: 0;border: none;" v-show="selectedTitles.includes('分类汇总')">
          <el-select filterable v-model="filter.groupType" collapse-tags clearable placeholder="分类汇总"
            style="width: 80px">
            <el-option label="明细" :value="0" />
              <el-option label="按ID汇总" :value="1" />
              <el-option label="按运营组汇总" :value="2" />
              <el-option label="按系列编码汇总" :value="4" />
              <el-option label="按系列编码+运营组汇总" :value="5" />
              <el-option label="按系列编码+运营专员汇总" :value="6" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin: 0;border: none;" v-show="selectedTitles.includes('商品ID')">
          <inputYunhan ref="proCode" v-model="filter.proCode" :inputt.sync="filter.proCode" placeholder="商品ID"
            width="110px" :maxRows="3000" :maxlength="90000" :clearable="true" @callback="callbackProCode" title="商品ID">
          </inputYunhan>
        </el-button>
        <el-button style="padding: 0;margin: 0;border: none;" v-show="selectedTitles.includes('商品名称')">
          <el-input v-model="filter.productName" clearable placeholder="商品名称" style="width:130px;" />
        </el-button>
        <el-button style="padding: 0;width: 160px;border: none;" v-show="selectedTitles.includes('系列编码')">
          <el-select v-model="styleCode" multiple collapse-tags filterable remote reserve-keyword placeholder="系列编码"
            clearable :remote-method="remoteMethod" style="width: 160px" :loading="searchloading">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin: 0;border: none;">
          <el-date-picker style="width: 210px" v-model="filter.timerange" type="daterange" :clearable="false"
            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" @change="datepickerfuc"></el-date-picker>
        </el-button>
        <el-button style="padding: 0;border: none;margin: 0;" v-show="selectedTitles.includes('最小上架天数')">
          <el-input-number v-model="filter.minOnTimeNum" :min="-9999" :max="9999" :precision="0" :controls="false" style="width: 90px;"
            placeholder=">上架天数最小值"></el-input-number>
        </el-button>
        <el-button style="padding: 0;border: none;margin: 0;" v-show="selectedTitles.includes('最大上架天数')">
          <el-input-number v-model="filter.maxOnTimeNum" :min="-9999" :max="9999" :precision="0" :controls="false" style="width: 90px;"
            placeholder="≤上架天数最大值"></el-input-number>
        </el-button>
        <el-button style="padding: 0;border: none;" v-show="selectedTitles.includes('店铺')">
          <el-select filterable clearable v-model="filter.shopCodeList" placeholder="店铺" style="width: 160px" multiple collapse-tags>
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
              :value="item.shopCode"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;width: 150px;margin: 0; border: none;" v-show="selectedTitles.includes('品牌')">
          <el-select filterable clearable v-model="filter.brandShopList" placeholder="品牌" style="width: 150px" multiple collapse-tags>
            <el-option v-for="item in brandArr" :key="item" :label="item" :value="item" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;border: none;" v-show="selectedTitles.includes('采购')">
          <el-select filterable v-model="filter.brandId" clearable placeholder="采购" style="width: 90px">
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;border: none;" v-show="selectedTitles.includes('运营组')">
          <el-select filterable v-model="filter.groupIds" collapse-tags clearable placeholder="运营组" style="width: 160px"
            multiple>
            <el-option key="无运营组" label="无运营组" :value="0"></el-option>
            <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"
              v-if="checkPermission('DayReportGroupSelect')" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;border: none;" v-show="selectedTitles.includes('运营专员')">
          <el-select filterable v-model="filter.operateSpecialUserIds" collapse-tags clearable placeholder="运营专员"
            multiple style="width: 160px">
            <el-option key="无运营专员" label="无运营专员" :value="0"></el-option>
            <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"
              v-if="checkPermission('DayReportOperateSpecialUserSelect')" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;border: none;" v-show="selectedTitles.includes('运营助理')">
          <el-select filterable v-model="filter.userIds" collapse-tags clearable placeholder="运营助理" style="width: 160px"
            multiple>
            <el-option key="无运营助理" label="无运营助理" :value="0"></el-option>
            <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"
              v-if="checkPermission('DayReportUserIdSelect')" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;border: none;" v-show="selectedTitles.includes('车手')">
          <el-select filterable v-model="filter.userId2" collapse-tags clearable placeholder="车手" style="width: 90px">
            <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;border: none;" v-show="selectedTitles.includes('备用负责人')">
          <el-select filterable v-model="filter.userId3" collapse-tags clearable placeholder="备用负责人"
            style="width: 90px">
            <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;border: none;" v-show="selectedTitles.includes('毛利2')">
            <el-select filterable v-model="filter.profit2UnZero" collapse-tags clearable :placeholder="filter.refundType==4?'发生毛利2':'毛利2'"
              style="width: 90px">
              <el-option label="全部" />
              <el-option label="正利润" :value="false" />
              <el-option label="负利润" :value="true" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: none;" v-show="selectedTitles.includes('毛利3')">
            <el-select filterable v-model="filter.profit3UnZero" collapse-tags clearable :placeholder="filter.refundType==4?'发生毛利3':'毛利3'"
              style="width: 90px">
              <el-option label="全部" />
              <el-option label="正利润" :value="false" />
              <el-option label="负利润" :value="true" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: none;" v-show="selectedTitles.includes('毛利4')">
            <el-select filterable v-model="filter.profit33UnZero" collapse-tags clearable :placeholder="filter.refundType==4?'发生毛利4':'毛利4'"
              style="width: 90px">
              <el-option label="正利润" :value="false" />
              <el-option label="负利润" :value="true" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0; border: none;" v-show="selectedTitles.includes('毛利6')">
            <el-select filterable v-model="filter.profit6UnZero" collapse-tags clearable :placeholder="filter.refundType==4?'发生毛利6':'毛利6'"
              style="width: 90px">
              <el-option label="正利润" :value="false" />
              <el-option label="负利润" :value="true" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: none;" v-show="selectedTitles.includes('净利润')">
            <el-select filterable v-model="filter.profit4UnZero" collapse-tags clearable  :placeholder="filter.refundType==4?'发生净利润':'净利润'"
              style="width: 90px">
              <el-option label="全部" />
              <el-option label="正利润" :value="false" />
              <el-option label="负利润" :value="true" />
            </el-select>
          </el-button>
        <el-button style="padding: 0;border: none;" v-show="selectedTitles.includes('是否货损')">
          <el-select filterable v-model="filter.IsDamageLink" collapse-tags clearable placeholder="是否货损"
            style="width: 80px">
            <el-option label="否" :value="false" />
            <el-option label="是" :value="true" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;border: none;" v-show="selectedTitles.includes('是否预包')">
          <el-select filterable v-model="filter.IsWc" collapse-tags clearable placeholder="是否预包" style="width: 80px">
            <el-option label="否" :value="false" />
            <el-option label="是" :value="true" />
          </el-select>
        </el-button>
        <el-button style="padding: 0; border: none;float: left;" v-show="selectedTitles.includes('星星')">
          <el-select v-model="filter.star" placeholder="星星" clearable filterable style="width: 80px">
            <el-option label="空白" :value="9"><span>空白</span></el-option>
            <el-option label="灰色" style="color:gray;size: 20px;" :value="8"><i
                class="el-icon-star-on">灰色</i></el-option>
            <el-option label="红色" style="color:red;size: 20px;" :value="1"><i class="el-icon-star-on">红色</i></el-option>
            <el-option label="橙色" style="color:orange;size: 20px;" :value="2"><i
                class="el-icon-star-on">橙色</i></el-option>
            <el-option label="黄色" style="color:yellow;size: 10px;" :value="3"><i
                class="el-icon-star-on">黄色</i></el-option>
            <el-option label="绿色" style="color:green" :value="4"><i class="el-icon-star-on">绿色</i></el-option>
            <el-option label="蓝色" style="color:blue" :value="5"><i class="el-icon-star-on">蓝色</i></el-option>
            <el-option label="靛色" style="color:indigo" :value="6"><i class="el-icon-star-on">靛色</i></el-option>
            <el-option label="紫色" style="color:purple" :value="7"><i class="el-icon-star-on">紫色</i></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0; border: none;float: left;" v-show="selectedTitles.includes('旗帜')">
          <el-select v-model="filter.flag" placeholder="旗帜" clearable filterable style="width: 80px; ">
            <el-option label="空白" :value="9"><span>空白</span></el-option>
            <el-option label="灰色" style="color:gray" :value="8"><i class="el-icon-s-flag">灰色</i></el-option>
            <el-option label="红色" style="color:red" :value="1"><i class="el-icon-s-flag">红色</i></el-option>
            <el-option label="橙色" style="color:orange" :value="2"><i class="el-icon-s-flag">橙色</i></el-option>
            <el-option label="黄色" style="color:yellow" :value="3"><i class="el-icon-s-flag">黄色</i></el-option>
            <el-option label="绿色" style="color:green" :value="4"><i class="el-icon-s-flag">绿色</i></el-option>
            <el-option label="蓝色" style="color:blue" :value="5"><i class="el-icon-s-flag">蓝色</i></el-option>
            <el-option label="靛色" style="color:indigo" :value="6"><i class="el-icon-s-flag">靛色</i></el-option>
            <el-option label="紫色" style="color:purple" :value="7"><i class="el-icon-s-flag">紫色</i></el-option>
          </el-select>
        </el-button>
        <button style="padding: 0; border: none;float: left;" v-show="selectedTitles.includes('毛六7日趋势')">
          <el-select v-model="filter.profit3IncreaseGoOnDays" placeholder="毛六7日趋势" clearable filterable
            style="width: 90px; ">
            <el-option v-for="i in 7" :label="`连续${i}日增长`" :value="i"></el-option>
            <el-option label="持平" :value="0"></el-option>
            <el-option v-for="i in 7" :label="`连续${i}日下滑`" :value="-i"></el-option>
          </el-select>
        </button>
        <el-button style="padding: 0; border: none;float: left;" v-show="selectedTitles.includes('连续日查询')">
          连续
          <el-input-number v-model="filter.dayCount" style="width:30px" :controls="false" :precision="0"
            :min="1"></el-input-number>
          日
          <el-select v-model="filter.isPositive" style="width:60px" placeholder="" clearable filterable>
            <el-option :label="'正利润'" :value="true"></el-option>
            <el-option :label="'负利润'" :value="false"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0; border: none;float: left;" v-show="selectedTitles.includes('毛6连续负利润')">
          <el-select v-model="filter.noProfitDay" placeholder="毛6连续负利润" clearable filterable style="width: 90px; ">
            <el-option label="连续3~4天负利润" :value="3"></el-option>
            <el-option label="连续5~6天负利润" :value="5"></el-option>
            <el-option label="连续7~9天负利润" :value="7"></el-option>
            <el-option label="连续10~14天负利润" :value="10"></el-option>
            <el-option label="连续15~29天负利润" :value="15"></el-option>
            <el-option label="连续30天以上负利润" :value="30"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0; border: none;float: left;" v-show="selectedTitles.includes('毛6总计负利润')">
          <el-select v-model="filter.bfNdaysProfit3LessThen0" placeholder="毛6总计负利润" clearable filterable
            style="width: 90px; ">
            <el-option label="前1天毛6合计负利润" :value="1"></el-option>
            <el-option label="前3天毛6合计负利润" :value="3"></el-option>
            <el-option label="前7天毛6合计负利润" :value="7"></el-option>
            <el-option label="前15天毛6合计负利润" :value="15"></el-option>
            <el-option label="前30天毛6合计负利润" :value="30"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0; border: none;float: left;" v-show="selectedTitles.includes('出仓利润')">
          <el-select v-model="filter.exitProfitUnZero" placeholder="出仓利润" clearable filterable style="width: 90px; ">
            <el-option label="正利润" :value="false"></el-option>
            <el-option label="负利润" :value="true"></el-option>
          </el-select>
        </el-button>
        <!-- <el-button style="padding: 0; border: none;float: left;">
            <el-select filterable clearable multiple collapse-tags v-model.trim="filter.styleTag" placeholder="款式标签" style="width: 90px">
              <el-option v-for="item in styleTaglist" :key="item.value"  :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-button> -->
        <el-button style="padding: 0;border: none;" v-show="selectedTitles.includes('超链接')">
          <el-select filterable v-model="filter.IsHyperlink" collapse-tags clearable placeholder="是否超链接"
            style="width: 90px">
            <el-option label="否" :value="false" />
            <el-option label="是" :value="true" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin: 0;border: none;" v-show="selectedTitles.includes('经营大类')">
          <el-select style="width: 120px;" v-model="filter.bzCategory" placeholder="经营大类" :collapse-tags="true" remote
            :remote-method="remoteMethodBusinessCategory" clearable filterable>
            <el-option v-for="(item, i) in filterList.bussinessCategoryNames" :key="'bussinessCategoryNames' + i + 1"
              :label="item" :value="item" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin: 0;border: none;" v-show="selectedTitles.includes('一级类目')">
          <el-select style="width: 120px;" v-model="filter.bzCategory1" placeholder="一级类目" :collapse-tags="true" remote
            :remote-method="remoteMethodCategoryName1s" clearable filterable>
            <el-option v-for="(item, i) in filterList.categoryName1s" :key="'categoryName1Level' + i + 1" :label="item"
              :value="item" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin: 0;border: none;" v-show="selectedTitles.includes('二级类目')">
          <el-select style="width: 120px;" v-model="filter.bzCategory2" placeholder="二级类目" :collapse-tags="true" remote
            :remote-method="remoteMethodCategoryName2s" clearable filterable>
            <el-option v-for="(item, i) in filterList.categoryName2s" :key="'categoryName2Level' + i + 1" :label="item"
              :value="item" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('属性')">
          <el-select v-model="filter.attributeTag" style="width: 156px;" placeholder="属性" multiple clearable
            filterable collapse-tags>
            <el-option v-for="item in statsList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('季节/节日')">
          <el-select v-model="filter.seasonOrFestivalTag" style="width: 160px ;" placeholder="季节/节日"
            multiple clearable filterable collapse-tags>
            <el-option v-for="item in seasonList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('天气')">
          <el-select v-model="filter.weatherTag" style="width: 144px ;" placeholder="天气" multiple clearable
            filterable collapse-tags>
            <el-option v-for="item in weatherList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('温度')">
          <el-select v-model="filter.temperatureTag" style="width: 160px ;" placeholder="温度" multiple
            clearable filterable collapse-tags>
            <el-option v-for="item in temperatureList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('白名单')">
          <el-select v-model="filter.isIgnoreRptProCode" style="width: 110px ;" placeholder="白名单" filterable
            collapse-tags>
            <el-option label="启用白名单" :value="1" />
            <el-option label="禁用白名单" :value="0" />
            <el-option label="只看白名单" :value="2" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('ID/店铺')">
          <el-select v-model="filter.isIgnoreSpecialProCode" placeholder="请选择ID/店铺" style="width: 110px;" filterable>
            <el-option v-for="item in hearlist" :key="item.label" :label="item.label" :value="item.value" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('项目')">
          <el-select filterable clearable v-model="filter.projName" placeholder="请选择项目" style="width: 160px" multiple collapse-tags>
            <el-option v-for="item in projectList" :key="item.projName" :label="item.projName"
              :value="item.projName"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('爆品')">
            <el-select filterable clearable v-model="filter.baoPinList" placeholder="请选择爆品" style="width: 160px" multiple collapse-tags>
              <el-option v-for="item in popularOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: none;margin: 0;" v-show="selectedTitles.includes('爆品申报日期')">
            <el-date-picker style="width: 210px" v-model="filter.bptimerange" type="daterange" :clearable="false"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="爆品申报开始日期"
              end-placeholder="爆品申报结束日期" clearable></el-date-picker>
          </el-button>
        <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('备注')">
          <el-input v-model.trim="filter.remark" placeholder="请输入备注" maxlength="100" clearable style="width: 150px" />
        </el-button>
        <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('毛三（减退款）是否负利润') && (filter.refundType == 3 || filter.refundType == 4)">
            <el-select v-model="filter.yyProfit3Lose" style="width: 120px;" placeholder="运营毛三（减退款）是否负利润" clearable>
              <el-option label="正" :value="1" />
              <el-option label="负" :value="2" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('毛四（减退款）是否负利润') && (filter.refundType == 3|| filter.refundType == 4)">
            <el-select v-model="filter.yyProfit33Lose" style="width: 120px;" placeholder="运营毛四（减退款）是否负利润" clearable>
              <el-option label="正" :value="1" />
              <el-option label="负" :value="2" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('毛五（减退款）是否负利润') &&( filter.refundType == 3|| filter.refundType == 4)">
            <el-select v-model="filter.yyProfit5Lose" style="width: 100px;" placeholder="运营毛五（减退款）是否负利润" clearable>
              <el-option label="正" :value="1" />
              <el-option label="负" :value="2" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('毛六（减退款）是否负利润') &&( filter.refundType == 3|| filter.refundType == 4)">
            <el-select v-model="filter.yyProfit6Lose" style="width: 100px;" placeholder="运营毛六（减退款）是否负利润" clearable>
              <el-option label="正" :value="1" />
              <el-option label="负" :value="2" />
            </el-select>
          </el-button>
        <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('毛三（减退款率)') && filter.refundType == 3">
            <el-input-number v-model="filter.yyProfit3RateMin" style="width: 100px" placeholder="毛三（减退款率最小）" :controls="false" :precision="0" :min="-1000" :max="1000" />
          </el-button>
          <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('毛三（减退款率)') && filter.refundType == 3">
            至
            <el-input-number v-model="filter.yyProfit3RateMax" style="width: 100px" placeholder="毛三（减退款率最大）" :controls="false" :precision="0" :min="-1000" :max="1000" />
          </el-button>
          <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('毛四（减退款率)') && filter.refundType == 3">
            <el-input-number v-model="filter.yyProfit33RateMin" style="width: 100px" placeholder="毛四（减退款率最小）" :controls="false" :precision="0" :min="-1000" :max="1000" />
          </el-button>
          <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('毛四（减退款率)') && filter.refundType == 3">
            至
            <el-input-number v-model="filter.yyProfit33RateMax" style="width: 100px" placeholder="毛四（减退款率最大）" :controls="false" :precision="0" :min="-1000" :max="1000" />
          </el-button>
          <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('毛五（减退款率)') && filter.refundType == 3">
            <el-input-number v-model="filter.yyProfit5RateMin" style="width: 100px" placeholder="毛五（减退款率最小）" :controls="false" :precision="0" :min="-1000" :max="1000" />
          </el-button>
          <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('毛五（减退款率)') && filter.refundType == 3">
            至
            <el-input-number v-model="filter.yyProfit5RateMax" style="width: 100px" placeholder="毛五（减退款率最大）" :controls="false" :precision="0" :min="-1000" :max="1000" />
          </el-button>
          <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('毛六（减退款率)') && filter.refundType == 3">
            <el-input-number v-model="filter.yyProfit6RateMin" style="width: 100px" placeholder="毛六（减退款率最小）" :controls="false" :precision="0" :min="-1000" :max="1000" />
          </el-button>
          <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('毛六（减退款率)') && filter.refundType == 3">
            至
            <el-input-number v-model="filter.yyProfit6RateMax" style="width: 100px" placeholder="毛六（减退款率最大）" :controls="false" :precision="0" :min="-1000" :max="1000" />
          </el-button>
        <el-button v-show="selectedTitles.includes('总广告费')" style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">总广告费:</el-button>
        <el-button v-show="selectedTitles.includes('总广告费')" style="padding: 0;margin: 0;">
          <el-input-number placeholder="总广告费" v-model="filter.minAdvAmount" style="width: 120px"></el-input-number>
        </el-button>
        <el-button v-show="selectedTitles.includes('总广告费')" style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">至</el-button>
        <el-button v-show="selectedTitles.includes('总广告费')" style="padding: 0;margin: 0;">
          <el-input-number placeholder="总广告费" v-model="filter.maxAdvAmount" style="width: 120px"></el-input-number>
        </el-button>
        <el-button style="padding: 0;margin: 0;float: left;" v-show="filter.refundType == 4">
          <dailyProfitrate ref="refdailyProfitrate" :valueChanged.sync="topfilter" />
        </el-button>
        <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('外仓率')">
            <div style="flex: 1;">
              <el-input-number v-model.trim="filter.wcOrderCountRateMin" placeholder="外仓率最小值" :min="0" :max="100" :precision="2"
                :controls="false" style="width: 80px;" /> % -
              <el-input-number v-model.trim="filter.wcOrderCountRateMax" placeholder="外仓率最大值" :min="0" :max="100" :precision="2"
                :controls="false" style="width: 80px;" /> %
            </div>
          </el-button>
          <el-button style="padding: 0;border: none;float: left;" v-show="selectedTitles.includes('店铺数据是否确认')">
            <el-select v-model="filter.isConfirmShop" style="width: 135px;" placeholder="店铺数据是否确认" clearable>
              <el-option label="已确认" :value="true" />
              <el-option label="未确认" :value="false" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: none;" v-if="checkPermission('FinancialDedicatedDailyReportViewing')" v-show="filter.groupType == 1">
            <el-select filterable v-model="filter.profit6UnZeroFinance" collapse-tags clearable :placeholder="filter.refundType==4?'发生毛利6（财务）':'毛利6（财务）'"
              style="width: 120px">
              <el-option label="正利润" :value="false" />
              <el-option label="负利润" :value="true" />
            </el-select>
          </el-button>
          <el-button style="padding: 0; border: none;float: left;" v-if="checkPermission('FinancialDedicatedDailyReportViewing')" v-show="filter.groupType == 1">
            <el-select v-model="filter.exitProfitUnZeroFinance" placeholder="出仓利润（财务）" clearable filterable style="width: 120px; ">
              <el-option label="正利润" :value="false"></el-option>
              <el-option label="负利润" :value="true"></el-option>
            </el-select>
          </el-button>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button icon="vxe-icon-custom-column" @click="clickToolbar" />
        <el-button type="primary" @click="onScaleSetting">比例配置</el-button>
      </el-button-group>
    </template>
    <vxetablebase v-show="filter.refundType < 3" :id="'productReportGC20221212'" :tablekey="'productReportGC20221212'"
      :border="true" :align="'center'" :cstmExportFunc="onExport" ref="table" :that='that' :isIndex='true'
      :hasexpand='false' @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true'
      :summaryarry='summaryarry' :tableData='financialreportlist' @summaryClick='onsummaryClick' @cellClick="cellClick"
      :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"
      style="width:100%;height:94%;margin: 0" :showheaderoverflow="false" @cellStyle="cellStyle" cellStyle :treeProp="{ childrenField: 'child' }">
      <template slot='extentbtn'>
        <el-button type="primary" size="small" :style="{ color: verifyDailyPaper ? 'red' : '' }" @click="onConfirmationMethod" style="width: 90px;" title="括号中的数字表示未确认店铺数,点击可查看哪些店铺未确认">日报确认{{ noConfirmShopCount>0?"("+noConfirmShopCount+")" :""}}</el-button>
        <el-button type="primary" size="small" v-if='checkPermission("profit6SixtyCents")'   @click="onCheckDayReportItem">数据核查</el-button>
        <el-button type="primary" size="small" @click="dialogConfirmdata2 = true" style="width: 90px;">违规扣款确认</el-button>
        <el-button-group>
          <el-radio-group v-model="filter.refundType" size="small" @input="refundTypefuc">
            <el-radio-button :label="4">日报看板</el-radio-button>
            <el-radio-button :label="1">发生维度</el-radio-button>
            <!-- <el-radio-button :label="2">付款维度</el-radio-button> -->
            <el-radio-button :label="3">运营维度</el-radio-button>
          </el-radio-group>
        </el-button-group>
        <el-button-group v-show="selectedTitles.includes('毛三利润')">
          <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">毛三利润:</el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input-number placeholder="毛三利润" v-model="filter.minProfit3" style="width: 100px"></el-input-number>
          </el-button>
          <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">至</el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input-number placeholder="毛三利润" v-model="filter.maxProfit3" style="width: 100px"></el-input-number>
          </el-button>
        </el-button-group>
        <!-- <el-button-group v-show="selectedTitles.includes('毛三利润率')">
          <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">毛三利润率:</el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input-number placeholder="毛三利润率" v-model="topfilter.minProfit3Rate" style="width: 100px"></el-input-number>
          </el-button>
          <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">至</el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input-number placeholder="毛三利润率" v-model="topfilter.maxProfit3Rate" style="width: 100px"></el-input-number>
          </el-button>
        </el-button-group> -->
        <el-button-group>
          <el-button style="float: left;  ">最晚计算时间：{{ SyncTimeMax ? SyncTimeMax : '' }}</el-button>
        </el-button-group>
      </template>
      <template #alladv="{ row }">
        <el-tooltip class="item" effect="dark"
        :content="'总消耗金额:'+row.tuoguanYG_Ys+';'+'推广佣金消耗金额:'+row.promotionCommissionConsumedAmount+';日预算消耗金额:'+row.dailyBudgetConsumedAmount+';周预算消耗金额:'+row.weeklyBudgetConsumedAmount+';预算加码消耗金额:'+row.budgetIncreaseConsumedAmount
        +';淘宝客:'+row.taobaoke_fuwu +';多目标直投:'+row.duoMuBiaoZhiTou+';关键词推广:'+row.guanJianCiTuiGuang
        +';活动加速:'+row.huoDongJiaSu   +';货品加速:'+row.huoPinJiaSu   +';精准人群推广:'+row.jingZhunRenQunTuiGuang   +';活动加速:'+row.huoDongJiaSu
        +';拉新快:'+row.laXinKuai +';全站推广:'+row.quanZhanTuiGuang+';上新快:'+row.shangXinKuai+';严选无界:'+row.yanXuanWuJie
        +';淘客扣佣:'+row.dK7  +';商家出资补贴预估:'+row.yyShangJiaChuZi  +';手淘直播佣金扣费:'+row.dK48
        "
        placement="top">
          <span>{{ row.alladv ? row.alladv : '' }}</span>
        </el-tooltip>
      </template>
      <template #tuoguanYG_Ys="{ row }">
        <el-tooltip class="item" effect="dark" :content="'推广佣金消耗金额:'+row.promotionCommissionConsumedAmount+';日预算消耗金额:'+row.dailyBudgetConsumedAmount+';周预算消耗金额:'+row.weeklyBudgetConsumedAmount+';预算加码消耗金额:'+row.budgetIncreaseConsumedAmount" placement="top">
          <span>{{ row.tuoguanYG_Ys ? row.tuoguanYG_Ys : '' }}</span>
        </el-tooltip>
      </template>
    </vxetablebase>
    <vxetablebase v-show="filter.refundType == 3" :id="'productReportGC20221212_2'"
      :tablekey="'productReportGC20221212_2'" :border="true" :align="'center'" :cstmExportFunc="onExport" ref="table2"
      :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="true"
      :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='financialreportlist'
      @summaryClick='onsummaryClick' @cellClick="cellClick" :tableCols='yytableCols' :tableHandles='tableHandles'
      :loading="listLoading" style="width:100%;height:94%;margin: 0" :showheaderoverflow="false" @cellStyle="cellStyle" cellStyle :treeProp="{ childrenField: 'child' }">
      <template slot='extentbtn'>
        <el-button type="primary" size="small" :style="{ color: verifyDailyPaper ? 'red' : '' }" @click="onConfirmationMethod" style="width: 90px;" title="括号中的数字表示未确认店铺数,点击可查看哪些店铺未确认">日报确认{{ noConfirmShopCount>0?"("+noConfirmShopCount+")" :""}}</el-button>
        <el-button type="primary" size="small" v-if='checkPermission("profit6SixtyCents")'   @click="onCheckDayReportItem">数据核查</el-button>
        <el-button type="primary" size="small" @click="dialogConfirmdata2 = true" style="width: 90px;">违规扣款确认</el-button>
        <el-button-group>
          <el-radio-group v-model="filter.refundType" size="small" @input="refundTypefuc">
            <el-radio-button :label="4">日报看板</el-radio-button>
            <el-radio-button :label="1">发生维度</el-radio-button>
            <!-- <el-radio-button :label="2">付款维度</el-radio-button> -->
            <el-radio-button :label="3">运营维度</el-radio-button>
          </el-radio-group>
        </el-button-group>
        <el-button-group v-show="selectedTitles.includes('毛三利润')">
          <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">毛三利润:</el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input-number placeholder="毛三利润" v-model="filter.minProfit3" style="width: 100px"></el-input-number>
          </el-button>
          <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">至</el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input-number placeholder="毛三利润" v-model="filter.maxProfit3" style="width: 100px"></el-input-number>
          </el-button>
        </el-button-group>
        <!-- <el-button-group v-show="selectedTitles.includes('毛三利润率')">
          <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">毛三利润率:</el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input-number placeholder="毛三利润率" v-model="topfilter.minProfit3Rate" style="width: 100px"></el-input-number>
          </el-button>
          <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">至</el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input-number placeholder="毛三利润率" v-model="topfilter.maxProfit3Rate" style="width: 100px"></el-input-number>
          </el-button>
        </el-button-group> -->
        <el-button-group>
          <el-button style="float: left;  ">最晚计算时间：{{ SyncTimeMax ? SyncTimeMax : '' }}</el-button>
        </el-button-group>

      </template>
      <template #alladv="{ row }">
        <el-tooltip class="item" effect="dark"
        :content="'总消耗金额:'+row.tuoguanYG+';'+'推广佣金消耗金额:'+row.promotionCommissionConsumedAmount+';日预算消耗金额:'+row.dailyBudgetConsumedAmount+';周预算消耗金额:'+row.weeklyBudgetConsumedAmount+';预算加码消耗金额:'+row.budgetIncreaseConsumedAmount
        +';淘宝客:'+row.taobaoke_fuwu +';多目标直投:'+row.duoMuBiaoZhiTou+';关键词推广:'+row.guanJianCiTuiGuang
        +';活动加速:'+row.huoDongJiaSu   +';货品加速:'+row.huoPinJiaSu   +';精准人群推广:'+row.jingZhunRenQunTuiGuang   +';活动加速:'+row.huoDongJiaSu
        +';拉新快:'+row.laXinKuai +';全站推广:'+row.quanZhanTuiGuang+';上新快:'+row.shangXinKuai+';严选无界:'+row.yanXuanWuJie
        +';淘客扣佣:'+row.dK7  +';商家出资补贴预估:'+row.yyShangJiaChuZi  +';手淘直播佣金扣费:'+row.dK48
        "
        placement="top">
          <span>{{ row.alladv ? row.alladv : '' }}</span>
        </el-tooltip>
      </template>
      <template #tuoguanYG="{ row }">
        <el-tooltip class="item" effect="dark" :content="'推广佣金消耗金额:'+row.promotionCommissionConsumedAmount+';日预算消耗金额:'+row.dailyBudgetConsumedAmount+';周预算消耗金额:'+row.weeklyBudgetConsumedAmount+';预算加码消耗金额:'+row.budgetIncreaseConsumedAmount" placement="top">
          <span>{{ row.tuoguanYG ? row.tuoguanYG : '' }}</span>
        </el-tooltip>
      </template>
    </vxetablebase>
    <vxetablebase v-show="dailyKanban" :id="'productReportGC202408181959'" :tablekey="'productReportGC202408181959'" :showheaderoverflow="false"
      :border="true" :align="'center'" :cstmExportFunc="onExport" ref="table3" :that='that' :isIndex='true'
      :hasexpand='false' @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true'
      :summaryarry='summaryarry' :tableData='financialreportlist' @summaryClick='onsummaryClick' @cellClick="cellClick"
      :tableCols='tableCols2' :tableHandles='tableHandles' :loading="listLoading"
      style="width:100%;height:94%;margin: 0" @cellStyle="cellStyle" cellStyle :treeProp="{ childrenField: 'child' }">
      <template slot='extentbtn'>
        <el-button type="primary" size="small" :style="{ color: verifyDailyPaper ? 'red' : '' }" @click="onConfirmationMethod" style="width: 90px;" title="括号中的数字表示未确认店铺数,点击可查看哪些店铺未确认">日报确认{{ noConfirmShopCount>0?"("+noConfirmShopCount+")" :""}}</el-button>
        <el-button type="primary" size="small" v-if='checkPermission("profit6SixtyCents")'   @click="onCheckDayReportItem">数据核查</el-button>
        <el-button type="primary" size="small" @click="dialogConfirmdata2 = true" style="width: 90px;">违规扣款确认</el-button>
        <el-button-group>
          <el-radio-group v-model="filter.refundType" size="small" @input="refundTypefuc">
            <el-radio-button :label="4">日报看板</el-radio-button>
            <el-radio-button :label="1">发生维度</el-radio-button>
            <!-- <el-radio-button :label="2">付款维度</el-radio-button> -->
            <el-radio-button :label="3">运营维度</el-radio-button>
          </el-radio-group>
        </el-button-group>
        <el-button-group v-show="selectedTitles.includes('毛三利润')">
          <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">毛三利润:</el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input-number placeholder="毛三利润" v-model="filter.minProfit3" style="width: 100px"></el-input-number>
          </el-button>
          <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">至</el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input-number placeholder="毛三利润" v-model="filter.maxProfit3" style="width: 100px"></el-input-number>
          </el-button>
        </el-button-group>
        <!-- <el-button-group v-show="selectedTitles.includes('毛三利润率')">
          <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">毛三利润率:</el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input-number placeholder="毛三利润率" v-model="topfilter.minProfit3Rate" style="width: 100px"></el-input-number>
          </el-button>
          <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">至</el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input-number placeholder="毛三利润率" v-model="topfilter.maxProfit3Rate" style="width: 100px"></el-input-number>
          </el-button>
        </el-button-group> -->
        <el-button-group>
          <el-button style="float: left;  ">最晚计算时间：{{ SyncTimeMax ? SyncTimeMax : '' }}</el-button>
        </el-button-group>

      </template>
      <template #payAmont="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.payAmont < 0 ? '#12993a' : '' }">{{ row.payAmont ? row.payAmont : '' }}</span>
        </div>
      </template>
      <template #saleAmont="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.saleAmont < 0 ? '#12993a' : '' }">{{ row.saleAmont ? row.saleAmont : '' }}</span>
        </div>
      </template>
      <template #alladv="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.alladv < 0 ? '#12993a' : '' }">{{ row.alladv ? row.alladv : '' }}</span>
        </div>
      </template>
      <template #advratio="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.advratio < 0 ? '#12993a' : '' }">{{ row.advratio ? row.advratio : '' }}</span>
        </div>
      </template>
      <template #profit1="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.profit1 < 0 ? '#12993a' : '' }">{{ row.profit1 ? row.profit1 : '' }}</span>
          <!-- <span class="vertical-sublevel_2" :style="{ color: row.yyProfit1 < 0 ? '#12993a' : '' }">{{ row.yyProfit1 ? row.yyProfit1 : '' }}</span>
          <span class="vertical-sublevel_3" :style="{ color: row.yyProfit1After < 0 ? '#12993a' : '' }">{{ row.yyProfit1After ? row.yyProfit1After : '' }}</span> -->
        </div>
      </template>
      <template #profit1Rate="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.profit1Rate < 0 ? '#12993a' : '' }">{{ row.profit1Rate ? row.profit1Rate + '%' : '' }}</span>
          <!-- <span class="vertical-sublevel_2" :style="{ color: row.yyProfit1Rate < 0 ? '#12993a' : '' }">{{ row.yyProfit1Rate ? row.yyProfit1Rate + '%' : '' }}</span>
          <span class="vertical-sublevel_3" :style="{ color: row.yyProfit1AfterRate < 0 ? '#12993a' : '' }">{{ row.yyProfit1AfterRate ? row.yyProfit1AfterRate + '%' : '' }}</span> -->
        </div>
      </template>
      <template #yyProfit1="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyProfit1 < 0 ? '#12993a' : '' }">{{ row.yyProfit1 ? row.yyProfit1 : '' }}</span>
        </div>
      </template>
      <template #yyProfit1Rate="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyProfit1Rate < 0 ? '#12993a' : '' }">{{ row.yyProfit1Rate ? row.yyProfit1Rate + '%' : '' }}</span>
        </div>
      </template>
      <template #yyProfit1After="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyProfit1After < 0 ? '#12993a' : '' }">{{ row.yyProfit1After ? row.yyProfit1After : '' }}</span>
        </div>
      </template>
      <template #yyProfit1AfterRate="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyProfit1AfterRate < 0 ? '#12993a' : '' }">{{ row.yyProfit1AfterRate ? row.yyProfit1AfterRate + '%' : '' }}</span>
        </div>
      </template>
      <template #profit2="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.profit2 < 0 ? '#12993a' : '' }">{{ row.profit2 ? row.profit2 : '' }}</span>
          <!-- <span class="vertical-sublevel_2" :style="{ color: row.yyProfit2 < 0 ? '#12993a' : '' }">{{ row.yyProfit2 ? row.yyProfit2 : '' }}</span>
          <span class="vertical-sublevel_3" :style="{ color: row.yyProfit2After < 0 ? '#12993a' : '' }">{{ row.yyProfit2After ? row.yyProfit2After : '' }}</span> -->
        </div>
      </template>
      <template #profit2Rate="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.profit2Rate < 0 ? '#12993a' : '' }">{{ row.profit2Rate ? row.profit2Rate + '%' : '' }}</span>
          <!-- <span class="vertical-sublevel_2" :style="{ color: row.yyProfit2Rate < 0 ? '#12993a' : '' }">{{ row.yyProfit2Rate ? row.yyProfit2Rate + '%' : '' }}</span>
          <span class="vertical-sublevel_3" :style="{ color: row.yyProfit2AfterRate < 0 ? '#12993a' : '' }">{{ row.yyProfit2AfterRate ? row.yyProfit2AfterRate + '%' : '' }}</span> -->
        </div>
      </template>
      <template #yyProfit2="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyProfit2 < 0 ? '#12993a' : '' }">{{ row.profit2 ? row.yyProfit2 : '' }}</span>
        </div>
      </template>
      <template #yyProfit2Rate="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyProfit2Rate < 0 ? '#12993a' : '' }">{{ row.yyProfit2Rate ? row.yyProfit2Rate + '%' : '' }}</span>
        </div>
      </template>
      <template #yyProfit2After="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyProfit2After < 0 ? '#12993a' : '' }">{{ row.yyProfit2After ? row.yyProfit2After : '' }}</span>
        </div>
      </template>
      <template #yyProfit2AfterRate="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyProfit2AfterRate < 0 ? '#12993a' : '' }">{{ row.yyProfit2AfterRate ? row.yyProfit2AfterRate + '%' : '' }}</span>
        </div>
      </template>
      <template #profit3="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.wcOrderCountRate >= 80 ? '#FF0000' :row.profit3 < 0 ? '#12993a' : '' }">{{ row.profit3 ? row.profit3 : '' }}</span>
          <!-- <span class="vertical-sublevel_2" :style="{ color: row.yyProfit3 < 0 ? '#12993a' : '' }">{{ row.yyProfit3 ? row.yyProfit3 : '' }}</span>
          <span class="vertical-sublevel_3" :style="{ color: row.yyProfit3After < 0 ? '#12993a' : '' }">{{ row.yyProfit3After ? row.yyProfit3After : '' }}</span> -->
        </div>
      </template>
      <template #profit3Rate="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.wcOrderCountRate >= 80 ? '#FF0000' :row.profit3Rate < 0 ? '#12993a' : '' }">{{ row.profit3Rate ? row.profit3Rate + '%' : '' }}</span>
          <!-- <span class="vertical-sublevel_2" :style="{ color: row.yyProfit3Rate < 0 ? '#12993a' : '' }">{{ row.yyProfit3Rate ? row.yyProfit3Rate + '%' : '' }}</span>
          <span class="vertical-sublevel_3" :style="{ color: row.yyProfit3AfterRate < 0 ? '#12993a' : '' }">{{ row.yyProfit3AfterRate ? row.yyProfit3AfterRate + '%' : '' }}</span> -->
        </div>
      </template>
      <template #yyProfit3="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyProfit3 < 0 ? '#12993a' : '' }">{{ row.profit3 ? row.yyProfit3 : '' }}</span>
        </div>
      </template>
      <template #yyProfit3Rate="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyProfit3Rate < 0 ? '#12993a' : '' }">{{ row.yyProfit3Rate ? row.yyProfit3Rate + '%' : '' }}</span>
        </div>
      </template>
      <template #yyProfit3After="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyProfit3After < 0 ? '#12993a' : '' }">{{ row.yyProfit3After ? row.yyProfit3After : '' }}</span>
        </div>
      </template>
      <template #yyProfit3AfterRate="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyProfit3AfterRate < 0 ? '#12993a' : '' }">{{ row.yyProfit3AfterRate ? row.yyProfit3AfterRate + '%' : '' }}</span>
        </div>
      </template>
      <template #profit33="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.profit33 < 0 ? '#12993a' : '' }">{{ row.profit33 ? row.profit33 : '' }}</span>
          <!-- <span class="vertical-sublevel_2" :style="{ color: row.yyProfit4 < 0 ? '#12993a' : '' }">{{ row.yyProfit4 ? row.yyProfit4 : '' }}</span>
          <span class="vertical-sublevel_3" :style="{ color: row.yyProfit4After < 0 ? '#12993a' : '' }">{{ row.yyProfit4After ? row.yyProfit4After : '' }}</span> -->
        </div>
      </template>
      <template #profit33Rate="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.profit33Rate < 0 ? '#12993a' : '' }">{{ row.profit33Rate ? row.profit33Rate + '%' : '' }}</span>
          <!-- <span class="vertical-sublevel_2" :style="{ color: row.yyProfit4Rate < 0 ? '#12993a' : '' }">{{ row.yyProfit4Rate ? row.yyProfit4Rate + '%' : '' }}</span>
          <span class="vertical-sublevel_3" :style="{ color: row.yyProfit4AfterRate < 0 ? '#12993a' : '' }">{{ row.yyProfit4AfterRate ? row.yyProfit4AfterRate + '%' : '' }}</span> -->
        </div>
      </template>
      <template #yyProfit4="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyProfit4 < 0 ? '#12993a' : '' }">{{ row.profit4 ? row.yyProfit4 : '' }}</span>
        </div>
      </template>
      <template #yyProfit4Rate="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyProfit4Rate < 0 ? '#12993a' : '' }">{{ row.yyProfit4Rate ? row.yyProfit4Rate + '%' : '' }}</span>
        </div>
      </template>
      <template #yyProfit4After="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyProfit4After < 0 ? '#12993a' : '' }">{{ row.yyProfit4After ? row.yyProfit4After : '' }}</span>
        </div>
      </template>
      <template #yyProfit4AfterRate="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyProfit4AfterRate < 0 ? '#12993a' : '' }">{{ row.yyProfit4AfterRate ? row.yyProfit4AfterRate + '%' : '' }}</span>
        </div>
      </template>
      <template #profit4="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.profit4 < 0 ? '#12993a' : '' }">{{ row.profit4 ? row.profit4  : '' }}</span>
        </div>
      </template>
      <template #profit4Rate="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.profit4Rate < 0 ? '#12993a' : '' }">{{ row.profit4Rate ? row.profit4Rate + '%' : '' }}</span>
        </div>
      </template>
      <template #refundAmontBefore="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.refundAmontBefore < 0 ? '#12993a' : '' }">{{ row.refundAmontBefore ? row.refundAmontBefore  : '' }}</span>
        </div>
      </template>
      <template #refundAmontBeforeRate="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.refundAmontBeforeRate < 0 ? '#12993a' : '' }">{{ row.refundAmontBeforeRate ? (row.refundAmontBeforeRate * 100 ).toFixed(0) + '%' : '' }}</span>
        </div>
      </template>
      <template #refundAmontAfter="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.refundAmontAfter < 0 ? '#12993a' : '' }">{{ row.refundAmontAfter ? row.refundAmontAfter  : '' }}</span>
        </div>
      </template>
      <template #refundAmontAfterRate="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.refundAmontAfterRate < 0 ? '#12993a' : '' }">{{ row.refundAmontAfterRate ? (row.refundAmontAfterRate * 100 ).toFixed(0) + '%' : '' }}</span>
        </div>
      </template>
      <template #refundAmont="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.refundAmont < 0 ? '#12993a' : '' }">{{ row.refundAmont ? row.refundAmont  : '' }}</span>
        </div>
      </template>
      <template #packageAvgFee="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.packageAvgFee < 0 ? '#12993a' : '' }">{{ row.packageAvgFee ? row.packageAvgFee  : '' }}</span>
        </div>
      </template>
      <template #freightAvgFee="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.freightAvgFee < 0 ? '#12993a' : '' }">{{ row.freightAvgFee ? row.freightAvgFee  : '' }}</span>
        </div>
      </template>
      <template #exitCost="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.exitCost < 0 ? '#12993a' : '' }">{{ row.exitCost ? row.exitCost  : '' }}</span>
        </div>
      </template>
      <template #exitCostAvg="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.exitCostAvg < 0 ? '#12993a' : '' }">{{ row.exitCostAvg ? row.exitCostAvg  : '' }}</span>
        </div>
      </template>
      <template #yyRefundAmontBefore="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyRefundAmontBefore < 0 ? '#12993a' : '' }">{{ row.yyRefundAmontBefore ? row.yyRefundAmontBefore  : '' }}</span>
        </div>
      </template>
      <template #yyRefundAmontBeforeRate="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyRefundAmontBeforeRate < 0 ? '#12993a' : '' }">{{ row.yyRefundAmontBeforeRate ? row.yyRefundAmontBeforeRate + '%' : '' }}</span>
        </div>
      </template>
      <template #yyRefundAmontAfter="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyRefundAmontAfter < 0 ? '#12993a' : '' }">{{ row.yyRefundAmontAfter ? row.yyRefundAmontAfter  : '' }}</span>
        </div>
      </template>
      <template #yyRefundAmontAfterRate="{ row }">
        <div class="vertical-container">
          <span class="vertical-sublevel_1" :style="{ color: row.yyRefundAmontAfterRate < 0 ? '#12993a' : '' }">{{ row.yyRefundAmontAfterRate ? row.yyRefundAmontAfterRate + '%' : '' }}</span>
        </div>
      </template>
    </vxetablebase>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>

    <el-drawer title="设置亏损连接" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
      :visible.sync="SettingsDamageLinkDialog.visible" direction="btt" size="'auto'" class="el-drawer__wrapper"
      style="position:absolute;">
      <form-create :rule="autoformparm.rule" v-model="autoformparm.fApi" :option="autoformparm.options" />
      <div class="drawer-footer">
        <el-button @click.native="SettingsDamageLinkDialog.visible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editparmLoading" @click="onSettingsDamageLink" />
      </div>
    </el-drawer>

    <el-dialog title="计算日报" :visible.sync="dialogCalDayRepotVis" width="40%" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 50%; float: left;" v-model="calDayRepotyDate" type="date" format="yyyyMMdd"
              value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
            <el-button type="success" style="margin-left:  30px;" @click="calDayRepoty">计算日报</el-button>
          </el-col>
        </el-row>
      </span>
    </el-dialog>

    <el-dialog title="确认数据" :visible.sync="dialogConfirmdata" width="55%" v-dialogDrag>
      <div style="height: 550px;">
        <dailyConfirmation v-if="dialogConfirmdata" :dailyPaperList="dailyPaperList" />
      </div>
    </el-dialog>

    <el-dialog title="确认违规扣款数据" :visible.sync="dialogConfirmdata2" width="40%" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 50%; float: left;" v-model="confirmDate2" type="date" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
            <el-button type="success" style="margin-left:  30px;" @click="confirmData2">确认</el-button>
          </el-col>
        </el-row>
      </span>
    </el-dialog>

    <el-dialog title="导入胜算" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 100%" v-model="onimportfilter.yearmonthday" type="date" format="yyyyMMdd"
              value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
            <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
              accept=".xlsx" :http-request="uploadFile" :file-list="fileList" :data="fileparm">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
          </el-col>
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="商品数据趋势图&&生意参谋平台" :visible.sync="dialogDrVisible" width="80%" v-dialogDrag>
      <div>
        <span>
          <productdrchart v-if="dialogDrVisible"></productdrchart>
        </span>
      </div>
      <div>
        <span>
          <BusinessStaffPlatForm v-if="dialogDrVisible"></BusinessStaffPlatForm>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogDrVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="订单明细" :visible.sync="freightDetail.visible" width="80%" v-dialogDrag>
      <div><el-alert title="温馨提示:由于统计、明细数据来源不一样，有少许可接受的差异属正常情况！" type="warning" :closable="false"></el-alert></div>
      <freightDetail ref="freightDetail" :filter="freightDetail.filter" style="height:600px;"></freightDetail>
    </el-dialog>

    <el-dialog title="赠品成本明细" :visible.sync="giftDetail.visible" width="80%" v-dialogDrag>
      <ordergiftdetail ref="ordergiftdetail" style="height:600px;"></ordergiftdetail>
    </el-dialog>

    <el-dialog title="盘亏计算明细" :visible.sync="InventoryCheckFee.visible" width="80%" v-dialogDrag>
      <div>
        <InventoryCheckFee ref="InventoryCheckFee" :filter="InventoryCheckFee.filter" style="height:600px;">
        </InventoryCheckFee>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="costDialog.visible" v-dialogDrag :show-close="false" width="300px">
      <el-table :data="costDialog.rows" border style="width: 100%">
        <el-table-column prop="saleCostSrc" label="自发成本" />
        <el-table-column prop="replaceSendCost" label="代发成本" />
      </el-table>
    </el-dialog>
    <el-dialog title="每日退款明细" :visible.sync="EveryDayrefund.visible" width="80%" v-dialogDrag>
      <div>
        <EveryDayrefund ref="EveryDayrefund" :filter="EveryDayrefund.filter" style="height:600px;"></EveryDayrefund>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
      <span>
        <buschar ref="buschar" v-if="buscharDialog.visible" :analysisData="buscharDialog.data"
          :loading="buscharDialog.loading"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="快递分析" :visible.sync="expressfreightanalysisVisible" width="80%" v-dialogDrag>
      <span>
        <expressfreightanalysis ref="expressfreightanalysis"></expressfreightanalysis>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="expressfreightanalysisVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-drawer title="每日文件上传跟踪" :visible.sync="drawervisible" direction="rtl">
      <importmodule ref="importmodule" v-if="drawervisible" :id="1"></importmodule>
    </el-drawer>

    <el-dialog :visible.sync="dailyNewspaperToolbar" width="40%" v-dialogDrag>
      <template #title>
        <div class="dialog-header">
          查询条件设置
          <el-button style="margin-left: 10px;" @click="selectAll">全选/取消全选</el-button>
        </div>
      </template>
      <el-scrollbar>
        <div style="height: 200px;">
          <el-checkbox-group v-model="colOptions" @change="changeOptions">
            <el-checkbox v-for="(item, index) in colSelect" v-if="item === '外仓率' || (filter.refundType == 3 && !['毛三利润率(发生)', '毛四利润率(发生)', '毛五利润率(发生)', '毛六利润率(发生)', '运营毛三利润率(减退款)', '运营毛四利润率(减退款)'].includes(item)) ||
             (filter.refundType == 4 && !['毛三（减退款率)', '毛四（减退款率)', '毛五（减退款率)', '毛六（减退款率)'].includes(item)) || (filter.refundType != 4 && item !== '日期' && !['毛三（减退款）是否负利润', '毛四（减退款）是否负利润', '毛五（减退款）是否负利润', '毛六（减退款）是否负利润', '毛三（减退款率)', '毛四（减退款率)', '毛五（减退款率)', '毛六（减退款率)','毛三利润率(发生)', '毛四利润率(发生)', '毛五利润率(发生)', '毛六利润率(发生)', '运营毛三利润率(减退款)', '运营毛四利润率(减退款)', '运营毛五利润率(减退款)', '运营毛六利润率(减退款)'].includes(item))" :label="item"
              :key="item"></el-checkbox>
          </el-checkbox-group>
        </div>
      </el-scrollbar>
      <div style="margin-top: 40px;display: flex;justify-content: end;">
        <el-button @click="dailyNewspaperToolbar = false">取消</el-button>
        <el-button type="primary" @click="verifyOptions" v-throttle="3000">确认</el-button>
      </div>
    </el-dialog>

    <el-dialog title="比例设置" :visible.sync="scaleDispositionVisible" width="25%" v-dialogDrag style="margin-top: 15vh;">
      <div style="height:150px;display: flex;align-items: center;justify-content: center;">
        <div>比例</div>
        <el-input-number v-model="rate" :min="0" :max="9999999.99" style="width:60%;margin-left: 20px;" :precision="2"
          :controls="false" placeholder="请输入比例"></el-input-number>%
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="scaleDispositionVisible = false">取 消</el-button>
        <el-button type="primary" @click="onDispositionSave">保 存</el-button>
      </span>
    </el-dialog>

    <vxe-modal title="编码利润" v-model="encoding.visible" width="80%" :mask-closable="true" marginSize='-500'>
        <productCodedProfitIndex v-if="encoding.visible" ref="productCodedProfitIndex" :info="encoding.info" />
    </vxe-modal>

    <vxe-modal title="AI分析" v-model="ai2ProcodeModal.visible" width="80%"  height="80%"  :mask-closable="true" marginSize='-500'>
      <ai2ProcodeView v-if="ai2ProcodeModal.visible" ref="ai2ProcodeView" :info="ai2ProcodeModal.info" />
  </vxe-modal>

  </my-container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop'
import { pageProductDayReport, queryDayReportAnalysis, exportProductDayReport, getParm, setParm, calDayRepoty, insertDailyReportConfirmList } from '@/api/bookkeeper/reportday'
import { importProductDayReport, ImportBusinessStaffPlatForm } from '@/api/bookkeeper/import'
import productdrchart from '@/views/bookkeeper/reportday/productdrchart'
import BusinessStaffPlatForm from '@/views/bookkeeper/reportday/BusinessStaffPlatForm'
import { formatPlatform, formatTime, formatYesornoBool, formatLinkProCode,formatProCodeStutas3, popularOptions } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import InputMult from "@/components/Comm/InputMult";
import { Loading } from 'element-ui';
import dailyConfirmation from '@/views/bookkeeper/reportday/dailyConfirmation'
import { getPageDailyReportItemConfirmList,getNoConfirmShopNames } from '@/api/bookkeeper/dayReport'
import ai2ProcodeView from "./ai2ProcodeView.vue";
import { getAllProBrand } from '@/api/inventory/warehouse'
import { ruleDirectorGroup } from '@/utils/formruletools'
import freightDetail from '@/views/bookkeeper/reportday/freightDetail'
import buschar from '@/components/Bus/buschar'
import importmodule from '@/components/Bus/importmodule'
import expressfreightanalysis from '@/views/express/expressfreightanalysis'
import ordergiftdetail from '@/views/order/gift/ordergiftDetail'
import InventoryCheckFee from '@/views/bookkeeper/reportday/InventoryCheckFee'
import EveryDayrefund from '@/views/bookkeeper/reportday/EveryDayrefund'
import { getListByStyleCode } from "@/api/inventory/basicgoods"
import { getBusinessCategorySelectData } from '@/api/operatemanage/base/category'
import { seasonList, temperatureList, statsList, weatherList, getTimeDiff } from '@/utils/getCols';
import inputYunhan from "@/components/Comm/inputYunhan";
import { getProductProjectList, getProductRemarkList  } from "@/api/operatemanage/productmanager"
import middlevue from "@/store/middle.js"
import numberRange from '@/components/number-range/index.vue'
import dayjs from 'dayjs'
import { SetVxeTableColumnCacheAsync, GetVxeTableColumnCacheAsync } from '@/api/admin/business'
import dailyProfitrate from "./dailyProfitrate.vue";
import dailyDataCheck from '@/views/bookkeeper/reportday/dailyDataCheck'
import { getBrandFeeList, editSignUpRate, getSignUpRate } from '@/api/bookkeeper/reportdayV2'
import productCodedProfitIndex from "./productCodedProfitIndex.vue";

import {
  addorEditProduct, updateDamageLink, getProductStyleTagList
} from '@/api/operatemanage/base/product'
const hearlist = [{ label: '查看全部', value: null }, { label: '查看普通ID', value: 1 }, { label: '查看特殊ID/店铺', value: 2 }];
let loading;
const startLoading = () => {
  loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};
const tableCols = [
  { istrue: true, fixed: 'left', prop: 'aIAnalysis', label: 'AI分析', sortable: 'custom', width: '60', type: 'click' , formatter: (row) => { return `AI分析`; }, handle: (that, row) => that.onAI2ProcodeShow(row)},
  { istrue: true, fixed: 'left', prop: 'yearMonthDay', label: '年月日', sortable: 'custom', width: '100', type: 'custom', formatter: (row) => row.yearMonthDay || '', treeNode: true },
  { istrue: true, prop: 'platform', fix: true, exportField: 'platformstr', label: '平台', width: '45', sortable: 'custom', formatter: (row) => formatPlatform(row.platform), type: 'custom' },
  { istrue: true, prop: 'projName', label: '项目', width: '60', sortable: 'custom', },
  { istrue: true, prop: 'baoPin', label: '爆品', width: '80', sortable: 'custom', },
  { istrue: true, prop: 'baoPinOnTime', label: '爆品申报时间', width: '80', sortable: 'custom', formatter: (row) => formatTime(row.baoPinOnTime, 'YYYY-MM-DD') },
  { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: '70', formatter: (row) => row.styleCode || ' ', type: 'click', handle: (that, row) => that.showProcodesimilarity(row) },

  { istrue: true, prop: 'bzCategory', label: '经营大类', sortable: 'custom', width: '70' },
  { istrue: true, prop: 'bzCategory1', label: '一级类目', sortable: 'custom', width: '70' },
  { istrue: true, prop: 'bzCategory2', label: '二级类目', sortable: 'custom', width: '70' },
  { istrue: true, prop: 'shopCode', exportField: 'shopName', label: '店铺名称', sortable: 'custom', width: '70', formatter: (row) => row.shopName, type: 'custom' },
  { istrue: true, prop: 'brandId', exportField: 'brandName', label: '采购', sortable: 'custom', width: '45', formatter: (row) => row.brandName || ' ', type: 'custom' },
  { istrue: true, label: '小组头像',  width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
  { istrue: true, prop: 'groupId', exportField: 'groupName', label: '小组', sortable: 'custom', width: '70', formatter: (row) => row.groupName,type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, },
  { istrue: true, label: '专员头像', width: '70',type:'ddAvatar',ddInfo:{type:2,prop:'operateSpecialUserId'} },
  { istrue: true, prop: 'operateSpecialUserId', exportField: 'operateSpecialUserName', label: '运营专员', sortable: 'custom', width: '70', formatter: (row) => row.operateSpecialUserName,type:'ddTalk',ddInfo:{type:2,prop:'operateSpecialUserId',name:'operateSpecialUserName'}, },
  { istrue: true, prop: 'userId', exportField: 'userName', label: '运营助理', sortable: 'custom', width: '80', formatter: (row) => row.userName, type: 'custom' },
  { istrue: true, prop: 'userId2', exportField: 'userName2', label: '车手', sortable: 'custom', width: '65', formatter: (row) => row.userName2, type: 'custom' },
  { istrue: true, prop: 'userId3', exportField: 'userName3', label: '备用负责人', sortable: 'custom', width: '80', formatter: (row) => row.userName3, type: 'custom' },
  { istrue: true, prop: 'syncTime', label: '计算时间', sortable: 'custom', width: '80', formatter: (row) => row.syncTime },

  //{istrue:true,prop:'proCode1',fix:true,label:'趋势图',style:"color:red;cursor:pointer;",width:'70',formatter:(row)=>'趋势图', type:'click',handle:(that,row)=>that.showprchart2(row.proCode,row.platform)},

  { istrue: true, type: 'echarts', prop: 'profit3IncreaseGoOnDays', chartProp: 'profit3IncreaseGoOnDaysChartData', fix: true, label: '毛六趋势', width: '80', permission: "lirunprsi,profit3rsi" },
  { istrue: true, prop: 'remark', label: '备注', sortable: 'custom', width: '80' },

  { istrue: true, prop: 'star', label: '星星', width: '30', type: 'newstar' },
  { istrue: true, prop: 'flag', label: '旗帜', width: '30', type: 'flag' },
  { istrue: true, prop: 'isDamageLink', label: '是否货损', width: '90', sortable: 'custom', type: 'click', formatter: (row) => row.isDamageLink  ? '是' : '否', handle: (that, row) => that.SettingsDamageLink(row) },
  { istrue: true, prop: 'isHyperlink', label: '是否超链接', width: '90', sortable: 'custom', type: 'custom', formatter: (row) => row.isHyperlink == 1 ? '是' : '否' },
  { istrue: true, prop: 'proCode', fix: true, label: '商品ID', width: '90', sortable: 'custom', type: 'html', formatter: (row) => row.status==3 ?formatProCodeStutas3(row.proCode)  : formatLinkProCode(row.platform, row.proCode) },
  // {istrue:true,prop:'styleTag',label:'款式标签',sortable:'custom', width:'60',type:'custom'},
  { istrue: true, prop: 'attributeTag', label: '属性', sortable: 'custom', width: '60', type: 'custom' },
  { istrue: true, prop: 'seasonOrFestivalTag', label: '季节/节日', sortable: 'custom', width: '60', type: 'custom' },
  { istrue: true, prop: 'weatherTag', label: '天气', sortable: 'custom', width: '60', type: 'custom' },
  { istrue: true, prop: 'temperatureTag', label: '温度', sortable: 'custom', width: '60', type: 'custom' },
  { istrue: true, prop: 'exitProfitLessZero', label: 'SKU', width: '45', tipmesg: '链接中有SKU出仓利润是负数时，显示红色字体', type: 'clickLink', formatter: (row) => { return `SKU+`; }, handle: (that, row) => that.onGoodsProfitShow(row), style: (that, row) => { return row.exitProfitLessZero ? "color:blue;cursor:pointer;color:red" : "color:blue;cursor:pointer;color:blue"; } },
  { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'productCategoryId', exportField: 'productCategoryName', label: '类目', width: '100', sortable: 'custom', formatter: (row) => (row.productCategoryId?.length > 3 ? row.productCategoryName : " "), type: 'custom' },
  { istrue: true, prop: 'onTime', label: '上架时间', sortable: 'custom', width: '75', formatter: (row) => formatTime(row.onTime, 'YYYY-MM-DD') },
  { istrue: true, prop: 'onTimeCount', exportField: 'onTimeCount', label: '上架天数', width: '75', formatter: (row, that) => getTimeDiff([row.onTime, that.filter.endTime]) },
  { istrue: true, summaryEvent: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width: '70', tipmesg: '（付款订单量）此字段仅供参考；如果一个订单中有3种商品，这3行商品的销售订单数均为1，所以不能把此字段的合计值作为订单数量。因为订单就只有1个。但是销售订单数合计值为3。', formatter: (row) => !row.orderCount ? " " : row.orderCount },
  // { istrue: true, summaryEvent: true, prop: 'wcOrderCount', label: '预包订单量', sortable: 'custom', width: '65', tipmesg: '', formatter: (row) => !row.wcOrderCount ? " " : row.wcOrderCount },
  { istrue: true, summaryEvent: true, prop: 'wcOrderCountRate', label: '外仓率', sortable: 'custom', width: '65', tipmesg: '预包订单量/订单量', formatter: (row) => !row.wcOrderCountRate ? " " : row.wcOrderCountRate + '%' },
  { istrue: true, prop: 'ljWcRate', label: '累计外仓率', sortable: 'custom', width: '65', tipmesg: '当月的外仓率', formatter: (row) => !row.ljWcRate ? " " : row.ljWcRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'payAmont', label: '付款金额', sortable: 'custom', width: '70', formatter: (row) => !row.payAmont ? " " : row.payAmont, type: 'click', handle: (that, row) => that.showOrderDetail(row) },
  { istrue: true, summaryEvent: true, prop: 'saleAmont', label: '销售金额', sortable: 'custom', width: '80', tipmesg: '已扣除当日发生的退款', formatter: (row) => !row.saleAmont ? " " : row.saleAmont },
  { istrue: true, summaryEvent: true, prop: 'profit1', label: '毛一利润', sortable: 'custom', width: '80', type: 'custom', tipmesg: '付款金额-刷单金额-发货前退款-发货后退款-商品成本-代发成本-赠品成本+取消单返还成本+销退仓返还成本-定制款成本差-采购运费', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, summaryEvent: true, prop: 'profit1Rate', label: '毛一利率', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛一利润/销售金额', formatter: (row) => !row.profit1Rate ? " " : row.profit1Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'alladv', label: '总广告费', sortable: 'custom', width: '70', tipmesg: '运营费用求和+淘宝客佣金+淘宝客本金+淘宝客服务费+特殊单佣金', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
  { istrue: true, summaryEvent: true, prop: 'advratio', label: '广告占比%', sortable: 'custom', width: '70', formatter: (row) => !row.advratio ? " " : (row.advratio) + "%" },
  { istrue: true, summaryEvent: true, prop: 'packageAvgFee', label: '包装均价', sortable: 'custom', width: '70', formatter: (row) => !row.packageAvgFee ? " " : row.packageAvgFee },
  { istrue: true, summaryEvent: true, prop: 'freightAvgFee', label: '快递均价', sortable: 'custom', width: '70', formatter: (row) => !row.freightAvgFee ? " " : row.freightAvgFee },
  { istrue: true, summaryEvent: true, prop: 'profit2Wc', label: '毛二利润', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛一-快递费-包装费-总广费费', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit2Wc ? " " : row.profit2Wc },
  { istrue: true, summaryEvent: true, prop: 'profit2WcRate', label: '毛二利润率', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛二利润/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit2WcRate ? " " : row.profit2WcRate + '%' },
  {
    istrue: true, summaryEvent: true, prop: 'profit2', label: '毛二利润（财务）', sortable: 'custom', width: '80', permission: "DailyFinancialAuthority", type: 'custom', tipmesg: '毛一-违规扣款-扣点合计-总广告费-包装材料-快递费',
    formatter: (row) => !row.profit2 ? " " : row.profit2
  },
  { istrue: true, summaryEvent: true, prop: 'profit2Rate', label: '毛二利润率（财务）', sortable: 'custom', width: '80', permission: "DailyFinancialAuthority", type: 'custom', tipmesg: '毛二利润/销售金额', formatter: (row) => !row.profit2Rate ? " " : row.profit2Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'profit3Wc', label: '毛三利润', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛二利润-预估费用(外仓率≥80%时红色显示)', permission: "lirunprsi", formatter: (row) => !row.profit3Wc ? " " : row.profit3Wc },
  { istrue: true, summaryEvent: true, prop: 'profit4Wc', label: '毛四利润', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛三利润-出仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit4Wc ? " " : row.profit4Wc },
  { istrue: true, summaryEvent: true, prop: 'profit4WcRate', label: '毛四利润率', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛四利润/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit4WcRate ? " " : row.profit4WcRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'profit5Wc', label: '毛五利润', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛四利润-仓库薪资', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit5Wc ? " " : row.profit5Wc },
  { istrue: true, summaryEvent: true, prop: 'profit5WcRate', label: '毛五利润率', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛五利润/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit5WcRate ? " " : row.profit5WcRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'profit6', label: '毛六利润', sortable: 'custom', width: '80', tipmesg: '毛五利润-(每笔订单的实际快递费、包装费、出仓成本-按云仓计算出来的快递费)', permission: "profit6SixtyCents", formatter: (row) => !row.profit6 ? " " : row.profit6 },
  { istrue: true, summaryEvent: true, prop: 'profit6Rate', label: '毛六利润率', sortable: 'custom', width: '80', tipmesg: '毛六利润/销售金额', permission: "profit6SixtyCents", formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'profit3', label: '毛三利润（财务）', sortable: 'custom', width: '80', permission: "DailyFinancialAuthority", formatter: (row) => !row.profit3 ? " " : row.profit3 },
  { istrue: true, summaryEvent: true, prop: 'profit3Rate', label: '毛三利润率（财务）', sortable: 'custom', width: '80', tipmesg: '毛三利润/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.profit3Rate ? " " : row.profit3Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'profit33', label: '毛四利润（财务）', sortable: 'custom', width: '80', permission: "DailyFinancialAuthority", formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, summaryEvent: true, prop: 'profit33Rate', label: '毛四利润率（财务）', sortable: 'custom', width: '80', tipmesg: '毛四利润/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'profit5', label: '毛五利润（财务）', sortable: 'custom', width: '80', permission: "DailyFinancialAuthority", formatter: (row) => !row.profit5 ? " " : row.profit5 },
  { istrue: true, summaryEvent: true, prop: 'profit5Rate', label: '毛五利润率（财务）', sortable: 'custom', width: '80', tipmesg: '毛五利润/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'exitCost', label: '出仓成本', sortable: 'custom', width: '70', formatter: (row) => !row.exitCost ? " " : row.exitCost, tipmesg: '真实出仓成本+预估出仓成本' },
  { istrue: true, summaryEvent: true, prop: 'extAmount3', label: '真实出仓成本', sortable: 'custom', width: '60', formatter: (row) => row.extAmount3 == 0 ? " " : row.extAmount3 },
  { istrue: true, summaryEvent: true, prop: 'extAmount4', label: '预估出仓成本', sortable: 'custom', width: '60', formatter: (row) => row.extAmount4 == 0 ? " " : row.extAmount4 },
  { istrue: true, summaryEvent: true, prop: 'exitCostRate', label: '出仓成本占比', sortable: 'custom', width: '70', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '70', formatter: (row) => !row.warehouseSalary ? " " : row.warehouseSalary },
  { istrue: true, summaryEvent: true, prop: 'exitProfit', label: '出仓利润', sortable: 'custom', width: '70', formatter: (row) => !row.exitProfit ? " " : row.exitProfit },
  { istrue: true, summaryEvent: true, prop: 'isExitProfit', label: '正出仓利润', sortable: 'custom', width: '70', type: 'click', formatter: (row) => !row.isExitProfit ? " " : row.isExitProfit, handle: (that, row) => that.onGoodsProfitShow(row, 1) },
  { istrue: true, summaryEvent: true, prop: 'negativeExitProfit', label: '负出仓利润', sortable: 'custom', width: '70', type: 'click', formatter: (row) => !row.negativeExitProfit ? " " : row.negativeExitProfit, handle: (that, row) => that.onGoodsProfitShow(row, 2) },
  { istrue: true, summaryEvent: true, prop: 'profit32', label: '毛三利润新', sortable: 'custom', width: '67', type: 'custom', tipmesg: '毛二利润-预估费用', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit32 ? " " : row.profit32 },
  { istrue: true, summaryEvent: true, prop: 'profit3Rate', label: '毛三利润率1', type: 'custom', tipmesg: '毛三利润/销售金额(外仓率≥80%时红色显示)', sortable: 'custom', width: '60', permission: "lirunprsi", formatter: (row) => !row.profit3Rate ? " " : row.profit3Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'profit32Rate', label: '毛三利润率2', type: 'custom', tipmesg: '毛三利润/销售金额', sortable: 'custom', width: '60', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit32Rate ? " " : row.profit32Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'profit4', label: '净利润', type: 'custom', tipmesg: '毛三利润-公摊费-盘点损益', sortable: 'custom', width: '75', permission: "lirunprsi", formatter: (row) => !row.profit4 ? " " : row.profit4 },
  { istrue: true, summaryEvent: true, prop: 'profit4Rate', label: '净利率', type: 'custom', tipmesg: '净利润/销售金额', sortable: 'custom', width: '75', permission: "lirunprsi", formatter: (row) => !row.profit4Rate ? " " : row.profit4Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'saleCost', label: '总销售成本', sortable: 'custom', width: '80', tipmesg: '当天付款金额总成本', formatter: (row) => !row.saleCost ? " " : row.saleCost },
  { istrue: true, summaryEvent: true, prop: 'replaceSendCost', label: '代发成本差', sortable: 'custom', width: '95', tipmesg: '运营导入', type: 'click', formatter: (row) => !row.replaceSendCost ? " " : row.replaceSendCost },
  { istrue: true, summaryEvent: true, prop: 'goodProfitRate', label: '商品毛利率', sortable: 'custom', width: '95', tipmesg: '（付款金额-（销售成本+赠品成本+赠品链接成本+代发成本差））/付款金额', formatter: (row) => !row.goodProfitRate ? " " : row.goodProfitRate + '%' },
  {
    istrue: true, summaryEvent: true, rop: '', label: `退款`, merge: true, prop: 'mergeField', width: '70',
    cols: [
      { istrue: true, summaryEvent: true, prop: 'cancelOrderCount', label: '发货前退款单量', sortable: 'custom', width: '120', type: 'custom' },
      { istrue: true, summaryEvent: true, prop: 'refundAmontBefore', label: '发货前退款', sortable: 'custom', width: '100', type: 'custom' },
      { istrue: true, summaryEvent: true, prop: 'refundAmontBeforeRate', label: '发货前退款率', sortable: 'custom', tipmesg: '发货前退款/付款金额', width: '120', type: 'custom', formatter: (row) => !row.refundAmontBeforeRate ? " " : (row.refundAmontBeforeRate * 100)?.toFixed(0) + '%' },
      { istrue: true, summaryEvent: true, prop: 'afterCancelOrderCount', label: '发货后退款单量', sortable: 'custom', width: '90', type: 'custom' },
      { istrue: true, summaryEvent: true, prop: 'refundAmontAfter', label: '发货后退款', sortable: 'custom', width: '70', type: 'custom' },
      { istrue: true, summaryEvent: true, prop: 'refundAmontAfterRate', label: '发货后退款率', sortable: 'custom', tipmesg: '发货后退款/付款金额', width: '100', type: 'custom', formatter: (row) => !row.refundAmontAfterRate ? " " : (row.refundAmontAfterRate * 100)?.toFixed(0) + '%' },
      { istrue: true, summaryEvent: true, prop: 'onlyRefundAmont', label: '仅退款', sortable: 'custom', width: '80', formatter: (row) => !row.onlyRefundAmont ? " " : row.onlyRefundAmont },
      { istrue: true, summaryEvent: true, prop: 'onlyRefundAmontRate', label: '仅退款率', sortable: 'custom', width: '80', formatter: (row) => !row.onlyRefundAmontRate ? " " : (row.onlyRefundAmontRate) + '%' },
      { istrue: true, summaryEvent: true, prop: 'refundAmont', label: '总退款金额', sortable: 'custom', width: '90', tipmesg: '当日发生的总退款金额，包括历史订单', formatter: (row) => !row.refundAmont ? " " : row.refundAmont },
    ]
  },
  { istrue: true, summaryEvent: true, prop: 'saleAmontAvg', label: '销售金额(分摊)', sortable: 'custom', width: '110', tipmesg: '销售金额(无id分摊)', formatter: (row) => !row.saleAmontAvg ? " " : row.saleAmontAvg },
  { istrue: true, summaryEvent: true, prop: 'brushAmont', label: '刷单金额', sortable: 'custom', width: '80', tipmesg: '刷单金额', formatter: (row) => !row.brushAmont ? " " : row.brushAmont },
  { istrue: true, summaryEvent: true, prop: 'cancelCost', label: '取消单返还成本', sortable: 'custom', width: '90', tipmesg: '返还客服已确认的取消单成本,以确认时间统计', formatter: (row) => !row.cancelCost ? " " : row.cancelCost, type: 'click', handle: (that, row) => that.showEveryDayrefund(row) },
  { istrue: true, summaryEvent: true, prop: 'refundCostSj', label: '销退仓返还成本', sortable: 'custom', width: '70', formatter: (row) => !row.refundCostSj ? " " : row.refundCostSj },
  { istrue: true, summaryEvent: true, prop: 'giftAmont', label: '赠品成本', sortable: 'custom', width: '70', formatter: (row) => !row.giftAmont ? ' ' : row.giftAmont, type: 'click', handle: (that, row) => that.showGiftDetail(row) },
  { istrue: true, summaryEvent: true, prop: 'saleCostAvg', label: '销售成本(分摊)', sortable: 'custom', width: '100', tipmesg: '销售成本(无id分摊)', formatter: (row) => !row.saleCostAvg ? " " : row.saleCostAvg },
  { istrue: true, summaryEvent: true, prop: 'diffCornerCost', label: '定制款成本差', sortable: 'custom', width: '110', formatter: (row) => row.diffCornerCost == 0 ? " " : row.diffCornerCost },
  { istrue: true, summaryEvent: true, prop: 'purchaseFreight', label: '采购成本差价', sortable: 'custom', width: '110', formatter: (row) => row.purchaseFreight == 0 ? " " : row.purchaseFreight, tipmesg: '入库单的采购运费分摊到该入库单的商品编码对应的ID上' },
  //{istrue:true,summaryEvent:true,prop:'dkAmont',label:'平台扣点',sortable:'custom', width:'80',type:'custom',formatter:(row)=> !row.dkAmont?" ": row.dkAmont},
  { istrue: true, summaryEvent: true, prop: 'deductAmount', label: '违规总扣款', sortable: 'custom', width: '80', type: 'custom', tipmesg: '违规总扣款', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
  {
    istrue: true, summaryEvent: true, rop: '', label: `平台扣点`, merge: true, prop: 'mergeField1', width: '80',
    cols: [
      { istrue: true, summaryEvent: true, prop: 'dK12', label: '极速回款', sortable: 'custom', width: '80', tipmesg: '极速回款担保服务费', formatter: (row) => row.dK12 == 0 ? " " : row.dK12 },
      { istrue: true, summaryEvent: true, prop: 'dK13', label: 'C2M直联营（不计算）', sortable: 'custom', width: '80', tipmesg: '当日发生在支付宝账单的费用，不参与计算', ormatter: (row) => row.dK13 == 0 ? " " : row.dK13 },
      { istrue: true, summaryEvent: true, prop: 'dK8', label: '微信-交易佣金(不计算)', sortable: 'custom', width: '80', tipmesg: '不计算', ormatter: (row) => row.dK8 == 0 ? " " : row.dK8 },
      { istrue: true, summaryEvent: true, prop: 'dK132', label: 'C2M直联营2', sortable: 'custom', width: '150', tipmesg: '当日销售金额*类目佣金率，参与计算', ormatter: (row) => row.dK132 == 0 ? " " : row.dK132 },
      { istrue: true, summaryEvent: true, prop: 'dK23', label: '保证金退款', sortable: 'custom', width: '80', tipmesg: '保证金退款', formatter: (row) => row.dK23 == 0 ? " " : row.dK23 },

      { istrue: true, summaryEvent: true, prop: 'dK47', label: '淘免单佣金扣费', sortable: 'custom', width: '80', tipmesg: '66010186淘免单佣金扣费', formatter: (row) => row.dK47 == 0 ? " " : row.dK47 },

      { istrue: true, summaryEvent: true, prop: 'dK49', label: '营销扣款', sortable: 'custom', width: '80', tipmesg: '6001062营销扣款', formatter: (row) => row.dK49 == 0 ? " " : row.dK49 },
      { istrue: true, summaryEvent: true, prop: 'dK30', label: '淘工厂补贴', sortable: 'custom', width: '80', tipmesg: '淘工厂补贴', formatter: (row) => row.dK30 == 0 ? " " : row.dK30 },
      //  {istrue:true,summaryEvent:true,prop:'dkC2Myg',label:'C2M.直营&联营&营促销.汇总费用（预估）',sortable:'custom', width:'80',tipmesg:'（付款金额-发货前退款金额）*11%',formatter:(row)=> row.dkC2Myg==0?0: row.dkC2Myg},
      { istrue: true, summaryEvent: true, prop: 'dK2', label: '微信-营销费用', sortable: 'custom', width: '80', tipmesg: '营销费用', formatter: (row) => row.dK2 == 0 ? " " : row.dK2 },
      // { istrue: true, summaryEvent: true, prop: 'dK3', label: '微信-佣金', sortable: 'custom', width: '80', tipmesg: '佣金', formatter: (row) => row.dK3 == 0 ? " " : row.dK3 },
      { istrue: true, summaryEvent: true, prop: 'dK5', label: '合作费用扣费', sortable: 'custom', width: '80', tipmesg: '合作费用扣费', formatter: (row) => row.dK5 == 0 ? " " : row.dK5 },
     { istrue: true, summaryEvent: true, prop: 'refundFreight', label: '退运费', sortable: 'custom', width: '80', tipmesg: 'C2M退货包运费代扣', formatter: (row) => row.refundFreight == 0 ? " " : row.refundFreight },

      { istrue: true, summaryEvent: true, prop: 'dK_PinLeiXingKeLJSoftFee', label: '代扣款', sortable: 'custom', width: '80', tipmesg: '代扣款', formatter: (row) => row.dK_PinLeiXingKeLJSoftFee == 0 ? " " : row.dK_PinLeiXingKeLJSoftFee },
      { istrue: true, summaryEvent: true, prop: 'dkOther', label: '其他账单', sortable: 'custom', width: '80', tipmesg: '其他账单=买赠赠品营销费用+服务费+技术服务费+推广服务费扣款', formatter: (row) => row.dkOther == 0 ? " " : row.dkOther },
      { istrue: true, summaryEvent: true, prop: 'dkAmont', label: '扣点合计', sortable: 'custom', width: '150', formatter: (row) => row.dkAmont == 0 ? " " : row.dkAmont },
    ]
  },
  {
    istrue: true, summaryEvent: true, prop: 'yyfy', label: `运营费用`, merge: true, prop: 'mergeField2', width: '70',
    cols: [
     { istrue: true, summaryEvent: true, prop: 'dK1', label: '投流推广佣金（不计算）', sortable: 'custom', width: '100', tipmesg: '当日发生在支付宝账单的费用，不参与计算', formatter: (row) => row.dK1 == 0 ? " " : row.dK1 },
     { istrue: true, summaryEvent: true, prop: 'dK9', label: '微信-投流推广佣金(不计算)', sortable: 'custom', width: '100', tipmesg: '不计算', formatter: (row) => row.dK9 == 0 ? " " : row.dK9 },
     { istrue: true, summaryEvent: true, prop: 'tuoguanYG_Ys', label: '推广总消耗预估', sortable: 'custom', width: '100', tipmesg: '取自店铺后台推广界面当日消耗，参与计算（右侧有明细）', formatter: (row) => row.tuoguanYG_Ys == 0 ? " " : row.tuoguanYG_Ys },
     { istrue: true, summaryEvent: true, prop: 'promotionCommissionConsumedAmount', label: '推广佣金消耗金额-预估（不计算）', sortable: 'custom', width: '100', tipmesg: '佣金率推广预估费用（取自店铺后台）', formatter: (row) => row.promotionCommissionConsumedAmount == 0 ? " " : row.promotionCommissionConsumedAmount },
     { istrue: true, summaryEvent: true, prop: 'dailyBudgetConsumedAmount', label: '日预算消耗金额（不计算）', sortable: 'custom', width: '100', tipmesg: '日预算消耗金额', formatter: (row) => row.dailyBudgetConsumedAmount == 0 ? " " : row.dailyBudgetConsumedAmount },
     { istrue: true, summaryEvent: true, prop: 'weeklyBudgetConsumedAmount', label: '周预算消耗金额（不计算）', sortable: 'custom', width: '100', tipmesg: '周期预算消耗金额', formatter: (row) => row.weeklyBudgetConsumedAmount == 0 ? " " : row.weeklyBudgetConsumedAmount },
     { istrue: true, summaryEvent: true, prop: 'budgetIncreaseConsumedAmount', label: '预算加码消耗金额（不计算）', sortable: 'custom', width: '100', tipmesg: '加码消耗费用', formatter: (row) => row.budgetIncreaseConsumedAmount == 0 ? " " : row.budgetIncreaseConsumedAmount },
     { istrue: true, summaryEvent: true, prop: 'zhitongche', label: '无界', sortable: 'custom', width: '70', formatter: (row) => row.zhitongche == 0 ? " " : row.zhitongche },
      { istrue: true, summaryEvent: true, prop: 'yanXuanWuJie', label: '严选无界', sortable: 'custom', width: '70', },
      { istrue: true, summaryEvent: true, prop: 'duoMuBiaoZhiTou', label: '多目标直投', sortable: 'custom', width: '70', formatter: (row) => row.duoMuBiaoZhiTou == 0 ? " " : row.duoMuBiaoZhiTou },
      { istrue: true, summaryEvent: true, prop: 'guanJianCiTuiGuang', label: '关键词推广', sortable: 'custom', width: '70', formatter: (row) => row.guanJianCiTuiGuang == 0 ? " " : row.guanJianCiTuiGuang },
      { istrue: true, summaryEvent: true, prop: 'huoDongJiaSu', label: '活动加速', sortable: 'custom', width: '70', formatter: (row) => row.huoDongJiaSu == 0 ? " " : row.huoDongJiaSu },
      { istrue: true, summaryEvent: true, prop: 'huoPinJiaSu', label: '货品加速', sortable: 'custom', width: '70', formatter: (row) => row.huoPinJiaSu == 0 ? " " : row.huoPinJiaSu },
      { istrue: true, summaryEvent: true, prop: 'jingZhunRenQunTuiGuang', label: '精准人群推广', sortable: 'custom', width: '70', formatter: (row) => row.jingZhunRenQunTuiGuang == 0 ? " " : row.jingZhunRenQunTuiGuang },
      { istrue: true, summaryEvent: true, prop: 'laXinKuai', label: '拉新快', sortable: 'custom', width: '70', formatter: (row) => row.laXinKuai == 0 ? " " : row.laXinKuai },
      { istrue: true, summaryEvent: true, prop: 'quanZhanTuiGuang', label: '全站推广', sortable: 'custom', width: '70', formatter: (row) => row.quanZhanTuiGuang == 0 ? " " : row.quanZhanTuiGuang },
      { istrue: true, summaryEvent: true, prop: 'shangXinKuai', label: '上新快', sortable: 'custom', width: '70', formatter: (row) => row.shangXinKuai == 0 ? " " : row.shangXinKuai },
      { istrue: true, summaryEvent: true, prop: 'yinglimofang', label: '阿里妈妈代投', sortable: 'custom', width: '70', formatter: (row) => row.yinglimofang == 0 ? " " : row.yinglimofang },
      { istrue: true, summaryEvent: true, prop: 'dK25', label: '品牌新享佣金扣费', sortable: 'custom', width: '80', tipmesg: '品牌新享佣金扣费', formatter: (row) => row.dK25 == 0 ? " " : row.dK25 },
      { istrue: true, summaryEvent: true, prop: 'dK48', label: '手淘直播佣金扣费', sortable: 'custom', width: '80', tipmesg: '66010185手淘直播佣金扣费', formatter: (row) => row.dK48 == 0 ? " " : row.dK48 },
      { istrue: true, summaryEvent: true, prop: 'dK11', label: '微信-直播推广佣金', sortable: 'custom', width: '80', formatter: (row) => row.dK11 == 0 ? " " : row.dK11 },
      { istrue: true, summaryEvent: true, prop: 'dK6', label: '商家出资单品补贴', sortable: 'custom', width: '80', tipmesg: '商家出资单品补贴', formatter: (row) => row.dK6 == 0 ? " " : row.dK6 },
      { istrue: true, summaryEvent: true, prop: 'yyShangJiaChuZi', label: '商家出资补贴预估(不计算)', sortable: 'custom', width: '80', tipmesg: '商家出资补贴预估(不计算)', formatter: (row) => row.yyShangJiaChuZi == 0 ? " " : row.yyShangJiaChuZi },
      // { istrue: true, summaryEvent: true, prop: 'signUpRecordAmount', label: '商家出资补贴超链接预估', sortable: 'custom', width: '80', formatter: (row) => row.signUpRecordAmount == 0 ? " " : row.signUpRecordAmount },
      { istrue: true, summaryEvent: true, prop: 'dK7', label: '淘客扣佣', sortable: 'custom', width: '80', tipmesg: '淘客扣佣', formatter: (row) => row.dK7 == 0 ? " " : row.dK7 },
      { istrue: true, summaryEvent: true, prop: 'dK10', label: '微信-淘客推广佣金', sortable: 'custom', width: '80', formatter: (row) => row.dK10 == 0 ? " " : row.dK10 },
      { istrue: true, summaryEvent: true, prop: 'merchantSubsidy', label: '商家出资补贴-货款', sortable: 'custom', width: '80', tipmesg: '商家出资补贴-货款', formatter: (row) => row.merchantSubsidy == 0 ? " " : row.merchantSubsidy },
      { istrue: true, summaryEvent: true, prop: 'sumadv', label: '求和', sortable: 'custom', width: '70', formatter: (row) => row.sumadv == 0 ? " " : row.sumadv },
    ]
  },
  {
    istrue: true, summaryEvent: true, prop: 'yyfy', label: `运营费用ROI`, merge: true, prop: 'mergeField3', width: '70',
    cols: [
      { istrue: true, summaryEvent: true, prop: 'duoMuBiaoZhiTouCjMoney', label: '多目标直投成交', sortable: 'custom', width: '70', formatter: (row) => row.duoMuBiaoZhiTouCjMoney == 0 ? " " : row.duoMuBiaoZhiTouCjMoney },
      { istrue: true, summaryEvent: true, prop: 'duoMuBiaoZhiTouRoi', label: '多目标直投ROI', sortable: 'custom', tipmesg: '总成交金额/花费', width: '70', formatter: (row) => row.duoMuBiaoZhiTouRoi == 0 ? " " : row.duoMuBiaoZhiTouRoi },
      { istrue: true, summaryEvent: true, prop: 'guanJianCiTuiGuangCjMoney', label: '关键词推广成交', sortable: 'custom', width: '70', formatter: (row) => row.guanJianCiTuiGuangCjMoney == 0 ? " " : row.guanJianCiTuiGuangCjMoney },
      { istrue: true, summaryEvent: true, prop: 'guanJianCiTuiGuangRoi', label: '关键词推广ROI', sortable: 'custom', tipmesg: '总成交金额/花费', width: '70', formatter: (row) => row.guanJianCiTuiGuangRoi == 0 ? " " : row.guanJianCiTuiGuangRoi },
      { istrue: true, summaryEvent: true, prop: 'huoDongJiaSuCjMoney', label: '活动加速成交', sortable: 'custom', width: '70', formatter: (row) => row.huoDongJiaSuCjMoney == 0 ? " " : row.huoDongJiaSuCjMoney },
      { istrue: true, summaryEvent: true, prop: 'huoDongJiaSuRoi', label: '活动加速ROI', sortable: 'custom', tipmesg: '总成交金额/花费', width: '70', formatter: (row) => row.huoDongJiaSuRoi == 0 ? " " : row.huoDongJiaSuRoi },
      { istrue: true, summaryEvent: true, prop: 'huoPinJiaSuCjMoney', label: '货品加速成交', sortable: 'custom', width: '70', formatter: (row) => row.huoPinJiaSuCjMoney == 0 ? " " : row.huoPinJiaSuCjMoney },
      { istrue: true, summaryEvent: true, prop: 'huoPinJiaSuRoi', label: '货品加速ROI', sortable: 'custom', tipmesg: '总成交金额/花费', width: '70', formatter: (row) => row.huoPinJiaSuRoi == 0 ? " " : row.huoPinJiaSuRoi },
      { istrue: true, summaryEvent: true, prop: 'jingZhunRenQunTuiGuangCjMoney', label: '精准人群推广成交', sortable: 'custom', width: '70', formatter: (row) => row.jingZhunRenQunTuiGuangCjMoney == 0 ? " " : row.jingZhunRenQunTuiGuangCjMoney },
      { istrue: true, summaryEvent: true, prop: 'jingZhunRenQunTuiGuangRoi', label: '精准人群推广ROI', sortable: 'custom', tipmesg: '总成交金额/花费', width: '70', formatter: (row) => row.jingZhunRenQunTuiGuangRoi == 0 ? " " : row.jingZhunRenQunTuiGuangRoi },
      { istrue: true, summaryEvent: true, prop: 'laXinKuaiCjMoney', label: '拉新快成交', sortable: 'custom', width: '70', formatter: (row) => row.laXinKuaiCjMoney == 0 ? " " : row.laXinKuaiCjMoney },
      { istrue: true, summaryEvent: true, prop: 'laXinKuaiRoi', label: '拉新快成交ROI', sortable: 'custom', tipmesg: '总成交金额/花费', width: '70', formatter: (row) => row.laXinKuaiRoi == 0 ? " " : row.laXinKuaiRoi },
      { istrue: true, summaryEvent: true, prop: 'quanZhanTuiGuangCjMoney', label: '全站推广成交', sortable: 'custom', width: '70', formatter: (row) => row.quanZhanTuiGuangCjMoney == 0 ? " " : row.quanZhanTuiGuangCjMoney },
      { istrue: true, summaryEvent: true, prop: 'quanZhanTuiGuangRoi', label: '全站推广成交ROI', sortable: 'custom', tipmesg: '总成交金额/花费', width: '70', formatter: (row) => row.quanZhanTuiGuangRoi == 0 ? " " : row.quanZhanTuiGuangRoi },
      { istrue: true, summaryEvent: true, prop: 'shangXinKuaiCjMoney', label: '上新快成交', sortable: 'custom', width: '70', formatter: (row) => row.shangXinKuaiCjMoney == 0 ? " " : row.shangXinKuaiCjMoney },
      { istrue: true, summaryEvent: true, prop: 'shangXinKuaiRoi', label: '上新快成交ROI', sortable: 'custom', tipmesg: '总成交金额/花费', width: '70', formatter: (row) => row.shangXinKuaiRoi == 0 ? " " : row.shangXinKuaiRoi },
    ]
  },
  {
    istrue: true, summaryEvent: true, prop: 'taobaoke', label: `淘宝客`, merge: true, prop: 'mergeField4',
    cols: [
      { istrue: true, summaryEvent: true, prop: 'taobaoke_ben', label: '本金', sortable: 'custom', width: '80', formatter: (row) => !row.taobaoke_ben ? " " : row.taobaoke_ben },
      { istrue: true, summaryEvent: true, prop: 'taobaoke_yong', label: '佣金', sortable: 'custom', width: '80', formatter: (row) => row.taobaoke_yong == 0 ? " " : row.taobaoke_yong },
      { istrue: true, summaryEvent: true, prop: 'taobaoke_fuwu', label: '服务费', sortable: 'custom', width: '80', formatter: (row) => row.taobaoke_fuwu == 0 ? " " : row.taobaoke_fuwu },
    ]
  },
  { istrue: true, summaryEvent: true, prop: 'shoudanlijing', label: '首单礼金', sortable: 'custom', width: '80', formatter: (row) => row.shoudanlijing == 0 ? " " : row.shoudanlijing },
  { istrue: true, summaryEvent: true, prop: 'dahuixiong', label: `特殊单(刷单、大灰熊)`, sortable: 'custom', tipmesg: '大灰熊', width: '100', formatter: (row) => row.dahuixiong == 0 ? " " : row.dahuixiong },
  // {
  //   istrue: true, summaryEvent: true, prop: 'sddhx', label: `特殊单(刷单、大灰熊)`, merge: true, prop: 'mergeField14',
  //   cols: [
  //     // {istrue:true,summaryEvent:true,prop:'brushCost',label:'商品成本',type:'custom',tipmesg:'刷单商品成本',sortable:'custom', width:'80',formatter:(row)=> !row.brushCost?" ": row.brushCost},
  //     { istrue: true, summaryEvent: true, prop: 'dahuixiong', label: '佣金', sortable: 'custom', width: '80', formatter: (row) => row.dahuixiong == 0 ? " " : row.dahuixiong },
  //   ]
  // },
  //{istrue:true,summaryEvent:true,prop:'cashRedAmount',label:'现金红包',sortable:'custom', width:'80',formatter:(row)=> row.cashRedAmount==0?" ": row.cashRedAmount,},
  { istrue: true, summaryEvent: true, prop: 'packageFee', label: '包装材料', sortable: 'custom', width: '80', formatter: (row) => !row.packageFee ? " " : row.packageFee },
  { istrue: true, summaryEvent: true, prop: 'freightFeeTotal', label: '快递费', sortable: 'custom', width: '80', type: 'custom', tipmesg: '实际快递费+虚拟快递费', formatter: (row) => !row.freightFeeTotal ? " " : row.freightFeeTotal },
  { istrue: true, summaryEvent: true, prop: 'freightFee', label: '实际快递费', sortable: 'custom', width: '80', type: 'click', handle: (that, row) => that.showFreightDetail(row) },
  { istrue: true, summaryEvent: true, prop: 'freightFeeVirtual', label: '虚拟快递费', sortable: 'custom', width: '80', tipmesg: '(订单量-发货前退款订单量-代发订单量)*平均快递费', formatter: (row) => !row.freightFeeVirtual ? " " : row.freightFeeVirtual },
  { istrue: true, summaryEvent: true, prop: 'freightAvgWeight', label: '快递均重', sortable: 'custom', width: '80', formatter: (row) => !row.freightAvgWeight ? " " : row.freightAvgWeight },
  //{istrue:true,summaryEvent:true,prop:'refundAmontHistory',label:'历史退款金额',sortable:'custom', width:'90',tipmesg:'退款时间是当日且订单支付时间是以前的退款金额',formatter:(row)=> !row.refundAmontHistory?" ": row.refundAmontHistory},

  {
    istrue: true, summaryEvent: true, prop: 'profit3PredictRate', label: '毛三预估比例', type: 'custom', permission: "lirunprsi", tipmesg: '(空白链接ID成本+异常成本+补发成本+代发成本+采购运费+产品费用+工资+损耗)/销售金额',
    sortable: 'custom', width: '80', formatter: (row) => !row.profit3PredictRate ? " " : (row.profit3PredictRate * 100)?.toFixed(0) + '%'
  },
  { istrue: true, summaryEvent: true, prop: 'profit3PredictFee', label: '预估费用', type: 'custom', tipmesg: '毛三预估比例*销售金额', permission: "lirunprsi", sortable: 'custom', width: '80', formatter: (row) => !row.profit3PredictFee ? " " : row.profit3PredictFee },
  {
    istrue: true, summaryEvent: true, prop: 'ygfy', label: `预估费用（正式）`, merge: true, prop: 'mergeField5', width: '70',
    cols: [
      { istrue: true, summaryEvent: true, prop: 'productFreight', label: '产品运费', sortable: 'custom', width: '80', formatter: (row) => row.productFreight == 0 ? " " : row.productFreight, tipmesg: '总销售成本*1.2%' },
      { istrue: true, summaryEvent: true, prop: 'cuiShouFee', label: '催收费', sortable: 'custom', width: '80', formatter: (row) => row.cuiShouFee == 0 ? " " : row.cuiShouFee, tipmesg: '每日导入' },
      { istrue: true, summaryEvent: true, prop: 'sampleFeeBX', label: '样品费', sortable: 'custom', width: '80', formatter: (row) => row.sampleFeeBX == 0 ? " " : row.sampleFeeBX, tipmesg: '每日导入' },
      { istrue: true, summaryEvent: true, prop: 'sampleFee', label: '拿样费', sortable: 'custom', width: '80', formatter: (row) => row.sampleFee == 0 ? " " : row.sampleFee, tipmesg: '运营/美工拿样，预估，5000元/月，每日167元' },
      { istrue: true, summaryEvent: true, prop: 'shootFee', label: '道具费', sortable: 'custom', width: '80', formatter: (row) => row.shootFee == 0 ? " " : row.shootFee, tipmesg: '美工拍摄道具费，预估，10000元/月' },
      { istrue: true, summaryEvent: true, prop: 'wages', label: '运营小组工资', sortable: 'custom', width: '80', formatter: (row) => row.wages == 0 ? " " : row.wages, tipmesg: '(销售金额-刷单金额-发货前退款-发货后退款)*运营工资占比' },
      { istrue: true, summaryEvent: true, prop: 'customerServiceWages', label: '客服工资', sortable: 'custom', width: '70', formatter: (row) => row.customerServiceWages == 0 ? " " : row.customerServiceWages, tipmesg: '' },
      { istrue: true, summaryEvent: true, prop: 'processingCost', label: '加工部工资', sortable: 'custom', width: '70', formatter: (row) => row.processingCost == 0 ? " " : row.processingCost, tipmesg: '参考日报数据维护-加工费（工价*（订单量-发货前退款单量））' },
      { istrue: true, summaryEvent: true, prop: 'lossOffFee', label: '损耗下架', sortable: 'custom', width: '80', formatter: (row) => row.lossOffFee == 0 ? " " : row.lossOffFee, tipmesg: '预估，0.1%' },
      { istrue: true, summaryEvent: true, prop: 'serviceFee', label: '客服费', sortable: 'custom', width: '80', formatter: (row) => row.serviceFee == 0 ? " " : row.serviceFee },
      //{istrue:true,summaryEvent:true,prop:'warehousingFee',label:'仓储费',sortable:'custom', width:'80',formatter:(row)=> row.warehousingFee==0?" ": row.warehousingFee},
    ]
  },
  { istrue: true, summaryEvent: true, prop: 'shareRate', label: '公摊费率', type: 'custom', tipmesg: '明细2/销售金额', sortable: 'custom', width: '80', permission: "lirunprsi", formatter: (row) => !row.shareRate ? " " : (row.shareRate * 100)?.toFixed(0) + '%' },
  { istrue: true, summaryEvent: true, prop: 'shareFee', label: '公摊费', type: 'custom', tipmesg: '公摊费(%)*销售金额', sortable: 'custom', width: '80', permission: "lirunprsi", formatter: (row) => !row.shareFee ? " " : row.shareFee },
  { istrue: true, summaryEvent: true, prop: 'inventoryCheckFee', label: '盘点损益', sortable: 'custom', width: '95', formatter: (row) => row.inventoryCheckFee == 0 ? " " : row.inventoryCheckFee, tipmesg: '(盘亏+盘盈),商品ID对应的商品编码在仓库中盘亏的金额,多个商品ID共有的商品编码前7天的销售成本分摊', type: 'click', handle: (that, row) => that.showInventoryCheckFee(row) },
  { istrue: true, summaryEvent: true, prop: 'brandFee1', label: '品牌管理费用1', type: 'custom', sortable: 'custom', width: '90', permission: "brandManagementPermis", formatter: (row) => !row.brandFee1 ? " " : row.brandFee1 },
  { istrue: true, summaryEvent: true, prop: 'brandFee2', label: '品牌管理费用2', type: 'custom', sortable: 'custom', width: '90', permission: "brandManagementPermis", formatter: (row) => !row.brandFee2 ? " " : row.brandFee2 },
];

const yytableCols = [
  { istrue: true, fixed: 'left', prop: 'aIAnalysis', label: 'AI分析', sortable: 'custom', width: '60', type: 'click' , formatter: (row) => { return `AI分析`; }, handle: (that, row) => that.onAI2ProcodeShow(row)},
  { istrue: true, fixed: 'left', prop: 'yearMonthDay', label: '年月日', sortable: 'custom', width: '100', type: 'custom', formatter: (row) => row.yearMonthDay || '', treeNode: true },
  { istrue: true, prop: 'platform', fix: true, exportField: 'platformstr', label: '平台', width: '45', sortable: 'custom', formatter: (row) => formatPlatform(row.platform), type: 'custom' },
  { istrue: true, prop: 'projName', label: '项目', width: '60', sortable: 'custom', },
  { istrue: true, prop: 'baoPin', label: '爆品', width: '80', sortable: 'custom', },
  { istrue: true, prop: 'baoPinOnTime', label: '爆品申报时间', width: '80', sortable: 'custom', formatter: (row) => formatTime(row.baoPinOnTime, 'YYYY-MM-DD') },
  { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: '70', formatter: (row) => row.styleCode || ' ', type: 'click', handle: (that, row) => that.showProcodesimilarity(row) },

  { istrue: true, prop: 'bzCategory', label: '经营大类', sortable: 'custom', width: '70' },
  { istrue: true, prop: 'bzCategory1', label: '一级类目', sortable: 'custom', width: '70' },
  { istrue: true, prop: 'bzCategory2', label: '二级类目', sortable: 'custom', width: '70' },
  { istrue: true, prop: 'shopCode', exportField: 'shopName', label: '店铺名称', sortable: 'custom', width: '70', formatter: (row) => row.shopName, type: 'custom' },
  { istrue: true, prop: 'brandId', exportField: 'brandName', label: '采购', sortable: 'custom', width: '45', formatter: (row) => row.brandName || ' ', type: 'custom' },
  { istrue: true, label: '小组头像',  width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
  { istrue: true, prop: 'groupId', exportField: 'groupName', label: '小组', sortable: 'custom', width: '70', formatter: (row) => row.groupName,type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, },
  { istrue: true, label: '专员头像', width: '70',type:'ddAvatar',ddInfo:{type:2,prop:'operateSpecialUserId'} },
  { istrue: true, prop: 'operateSpecialUserId', exportField: 'operateSpecialUserName', label: '运营专员', sortable: 'custom', width: '70', formatter: (row) => row.operateSpecialUserName,type:'ddTalk',ddInfo:{type:2,prop:'operateSpecialUserId',name:'operateSpecialUserName'}, },
  { istrue: true, prop: 'userId', exportField: 'userName', label: '运营助理', sortable: 'custom', width: '80', formatter: (row) => row.userName, type: 'custom' },
  { istrue: true, prop: 'userId2', exportField: 'userName2', label: '车手', sortable: 'custom', width: '65', formatter: (row) => row.userName2, type: 'custom' },
  { istrue: true, prop: 'userId3', exportField: 'userName3', label: '备用负责人', sortable: 'custom', width: '80', formatter: (row) => row.userName3, type: 'custom' },
  { istrue: true, prop: 'syncTime', label: '计算时间', sortable: 'custom', width: '80', formatter: (row) => row.syncTime },

  //{istrue:true,prop:'proCode1',fix:true,label:'趋势图',style:"color:red;cursor:pointer;",width:'70',formatter:(row)=>'趋势图', type:'click',handle:(that,row)=>that.showprchart2(row.proCode,row.platform)},

  { istrue: true, type: 'echarts', prop: 'profit3IncreaseGoOnDays', chartProp: 'profit3IncreaseGoOnDaysChartData', fix: true, label: '毛六趋势', width: '80', permission: "lirunprsi,yyprofit3rsi" },
  { istrue: true, prop: 'remark', label: '备注', sortable: 'custom', width: '80' },

  { istrue: true, prop: 'star', label: '星星', width: '30', type: 'newstar' },
  { istrue: true, prop: 'flag', label: '旗帜', width: '30', type: 'flag' },
  { istrue: true, prop: 'isDamageLink', label: '是否货损', width: '90', sortable: 'custom', type: 'click', formatter: (row) => row.isDamageLink? '是' : '否', handle: (that, row) => that.SettingsDamageLink(row) },
  { istrue: true, prop: 'isHyperlink', label: '是否超链接', width: '90', sortable: 'custom', type: 'custom', formatter: (row) => row.isHyperlink == 1 ? '是' : '否' },
  { istrue: true, prop: 'proCode', fix: true, label: '商品ID', width: '90', sortable: 'custom', type: 'html', formatter: (row) => row.status==3 ?formatProCodeStutas3(row.proCode ) : formatLinkProCode(row.platform, row.proCode) },
  // {istrue:true,prop:'styleTag',label:'款式标签',sortable:'custom', width:'60',type:'custom'},
  { istrue: true, prop: 'attributeTag', label: '属性', sortable: 'custom', width: '60', type: 'custom' },
  { istrue: true, prop: 'seasonOrFestivalTag', label: '季节/节日', sortable: 'custom', width: '60', type: 'custom' },
  { istrue: true, prop: 'weatherTag', label: '天气', sortable: 'custom', width: '60', type: 'custom' },
  { istrue: true, prop: 'temperatureTag', label: '温度', sortable: 'custom', width: '60', type: 'custom' },
  { istrue: true, prop: 'exitProfitLessZero', label: 'SKU', width: '45', tipmesg: '链接中有SKU出仓利润是负数时，显示红色字体', type: 'clickLink', formatter: (row) => { return `SKU+`; }, handle: (that, row) => that.onGoodsProfitShow(row), style: (that, row) => { return row.exitProfitLessZero ? "color:blue;cursor:pointer;color:red" : "color:blue;cursor:pointer;color:blue"; } },
  { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'productCategoryId', exportField: 'productCategoryName', label: '类目', width: '100', sortable: 'custom', formatter: (row) => (row.productCategoryId?.length > 3 ? row.productCategoryName : " "), type: 'custom' },
  { istrue: true, prop: 'onTime', label: '上架时间', sortable: 'custom', width: '75', formatter: (row) => formatTime(row.onTime, 'YYYY-MM-DD') },
  { istrue: true, prop: 'onTimeCount', exportField: 'onTimeCount', label: '上架天数', width: '75', formatter: (row, that) => getTimeDiff([row.onTime, that.filter.endTime]) },
  { istrue: true, summaryEvent: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width: '70', tipmesg: '（付款订单量）此字段仅供参考；如果一个订单中有3种商品，这3行商品的销售订单数均为1，所以不能把此字段的合计值作为订单数量。因为订单就只有1个。但是销售订单数合计值为3。', formatter: (row) => !row.orderCount ? " " : row.orderCount },
  // { istrue: true, summaryEvent: true, prop: 'wcOrderCount', label: '预包订单量', sortable: 'custom', width: '65', tipmesg: '', formatter: (row) => !row.wcOrderCount ? " " : row.wcOrderCount },
  { istrue: true, summaryEvent: true, prop: 'wcOrderCountRate', label: '外仓率', sortable: 'custom', width: '65', tipmesg: '预包订单量/订单量', formatter: (row) => !row.wcOrderCountRate ? " " : row.wcOrderCountRate + '%' },
  { istrue: true, prop: 'ljWcRate', label: '累计外仓率', sortable: 'custom', width: '65', tipmesg: '当月的外仓率', formatter: (row) => !row.ljWcRate ? " " : row.ljWcRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'payAmont', label: '付款金额', sortable: 'custom', width: '70', formatter: (row) => !row.payAmont ? " " : row.payAmont, type: 'click', handle: (that, row) => that.showOrderDetail(row) },
  { istrue: true, summaryEvent: true, prop: 'brushAmont', label: '刷单金额', sortable: 'custom', width: '80', tipmesg: '刷单金额', formatter: (row) => !row.brushAmont ? " " : row.brushAmont },

  { istrue: true, summaryEvent: true, prop: 'dahuixiong', label: `特殊单(刷单、大灰熊)`, sortable: 'custom', tipmesg: '大灰熊', width: '100', formatter: (row) => row.dahuixiong == 0 ? " " : row.dahuixiong },
  // {
  //   istrue: true, summaryEvent: true, prop: 'sddhx', label: `特殊单(刷单、大灰熊)`, merge: true, prop: 'mergeField6',
  //   cols: [
  //     // {istrue:true,summaryEvent:true,prop:'brushCost',label:'商品成本',type:'custom',tipmesg:'刷单商品成本',sortable:'custom', width:'80',formatter:(row)=> !row.brushCost?" ": row.brushCost},
  //     { istrue: true, summaryEvent: true, prop: 'dahuixiong', label: '佣金', sortable: 'custom', width: '80', formatter: (row) => row.dahuixiong == 0 ? " " : row.dahuixiong },
  //   ]
  // },
  { istrue: true, summaryEvent: true, prop: 'saleAmontAvg', label: '付款金额分摊', sortable: 'custom', width: '60', tipmesg: '销售金额(无id分摊)', formatter: (row) => !row.saleAmontAvg ? " " : row.saleAmontAvg },

  { istrue: true, summaryEvent: true, prop: 'saleCost', label: '总商品成本', sortable: 'custom', width: '60', tipmesg: '销售主题分析销售成本-赠品成本', formatter: (row) => !row.saleCost ? " " : row.saleCost },
  { istrue: true, summaryEvent: true, prop: 'brushCost', label: '刷单成本', sortable: 'custom', width: '80', tipmesg: '当天付款金额总成本', formatter: (row) => !row.brushCost ? " " : row.brushCost },
  { istrue: true, summaryEvent: true, prop: 'replaceSendCost', label: '代发成本', sortable: 'custom', width: '45', tipmesg: '运营导入', type: 'click', formatter: (row) => !row.replaceSendCost ? " " : row.replaceSendCost },
  { istrue: true, summaryEvent: true, prop: 'yySaleAmount', label: '销售金额', sortable: 'custom', width: '80', tipmesg: '已扣除当日发生的退款', formatter: (row) => !row.yySaleAmount ? " " : row.yySaleAmount },
  { istrue: true, summaryEvent: true, prop: 'purchaseFreight', label: '采购运费', sortable: 'custom', width: '100', formatter: (row) => row.purchaseFreight == 0 ? " " : row.purchaseFreight, tipmesg: '1.按商品成本占比分摊至每个编码上2.计算出每个编码的单位成本< 3.销售数量（不含取消单数量）*单位成本' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit1', label: '毛一利润', sortable: 'custom', width: '70', type: 'custom', tipmesg: '付款-刷单金额-总商品成本-采购运费-代发成本差-定制款成本差', formatter: (row) => !row.yyProfit1 ? " " : row.yyProfit1 },
  { istrue: true, summaryEvent: true, prop: 'yyProfit1Rate', label: '毛一利率', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛一利润/销售金额', formatter: (row) => !row.yyProfit1Rate ? " " : row.yyProfit1Rate + '%' },
  {
    istrue: true, summaryEvent: true, rop: 'ptkd11', label: `平台扣点`, merge: true, prop: 'mergeField7', permission: "cgcoltxpddprsi3", width: '80',
    cols: [
      { istrue: true, summaryEvent: true, prop: 'dK12', label: '极速回款', sortable: 'custom', width: '80', tipmesg: '极速回款担保服务费', formatter: (row) => row.dK12 == 0 ? " " : row.dK12 },
      { istrue: true, summaryEvent: true, prop: 'dK13', label: 'C2M直联营（不计算）', sortable: 'custom', width: '80', tipmesg: '当日发生在支付宝账单的费用，不参与计算', ormatter: (row) => row.dK13 == 0 ? " " : row.dK13 },
      { istrue: true, summaryEvent: true, prop: 'dK8', label: '微信-交易佣金(不计算)', sortable: 'custom', width: '80', tipmesg: '不计算', ormatter: (row) => row.dK8 == 0 ? " " : row.dK8 },
      { istrue: true, summaryEvent: true, prop: 'dK132', label: 'C2M直联营2', sortable: 'custom', width: '150', tipmesg: '当日销售金额*类目佣金率，参与计算', ormatter: (row) => row.dK132 == 0 ? " " : row.dK132 },
      { istrue: true, summaryEvent: true, prop: 'dK23', label: '保证金退款', sortable: 'custom', width: '80', tipmesg: '保证金退款', formatter: (row) => row.dK23 == 0 ? " " : row.dK23 },
      { istrue: true, summaryEvent: true, prop: 'dK47', label: '淘免单佣金扣费', sortable: 'custom', width: '80', tipmesg: '66010186淘免单佣金扣费', formatter: (row) => row.dK47 == 0 ? " " : row.dK47 },
      { istrue: true, summaryEvent: true, prop: 'dK49', label: '营销扣款', sortable: 'custom', width: '80', tipmesg: '6001062营销扣款', formatter: (row) => row.dK49 == 0 ? " " : row.dK49 },
      { istrue: true, summaryEvent: true, prop: 'dK30', label: '淘工厂补贴', sortable: 'custom', width: '80', tipmesg: '淘工厂补贴', formatter: (row) => row.dK30 == 0 ? " " : row.dK30 },
      { istrue: true, summaryEvent: true, prop: 'dK2', label: '微信-营销费用', sortable: 'custom', width: '80', tipmesg: '营销费用', formatter: (row) => row.dK2 == 0 ? " " : row.dK2 },
      // { istrue: true, summaryEvent: true, prop: 'dK3', label: '微信-佣金', sortable: 'custom', width: '80', tipmesg: '佣金', formatter: (row) => row.dK3 == 0 ? " " : row.dK3 },
      { istrue: true, summaryEvent: true, prop: 'dK5', label: '合作费用扣费', sortable: 'custom', width: '80', tipmesg: '合作费用扣费', formatter: (row) => row.dK5 == 0 ? " " : row.dK5 },
      //  {istrue:true,summaryEvent:true,prop:'dkC2Myg',label:'C2M.直营&联营&营促销.汇总费用（预估）',sortable:'custom', width:'80',tipmesg:'（付款金额-发货前退款金额）*11%',formatter:(row)=> row.dkC2Myg==0?0: row.dkC2Myg},
      { istrue: true, summaryEvent: true, prop: 'refundFreight', label: '退运费', sortable: 'custom', width: '80', tipmesg: 'C2M退货包运费代扣', formatter: (row) => row.refundFreight == 0 ? " " : row.refundFreight },
      { istrue: true, summaryEvent: true, prop: 'dK_PinLeiXingKeLJSoftFee', label: '代扣款', sortable: 'custom', width: '80', tipmesg: '代扣款', formatter: (row) => row.dK_PinLeiXingKeLJSoftFee == 0 ? " " : row.dK_PinLeiXingKeLJSoftFee },
      { istrue: true, summaryEvent: true, prop: 'dkOther', label: '其他账单', sortable: 'custom', width: '80', tipmesg: '其他账单=淘客扣佣+买赠赠品营销费用+服务费+技术服务费+推广服务费扣款', formatter: (row) => row.dkOther == 0 ? " " : row.dkOther },
      { istrue: true, summaryEvent: true, prop: 'dkAmont', label: '扣点合计', sortable: 'custom', width: '150', formatter: (row) => row.dkAmont == 0 ? " " : row.dkAmont },
    ]
  },
  { istrue: true,summaryEvent: true,  prop: 'yyDk15RefundFreight', label: '预估扣点', sortable: 'custom', width: '70', type: 'custom', formatter: (row) => !row.yyDk15RefundFreight ? " " : row.yyDk15RefundFreight },

  {
    istrue: true, summaryEvent: true, prop: 'yyfy11', label: `运营费用`, merge: true, prop: 'mergeField8', permission: "cgcoltxpddprsi1", width: '70',
    cols: [
    { istrue: true, summaryEvent: true, prop: 'dK1', label: '投流推广佣金（不计算）', sortable: 'custom', width: '100', tipmesg: '当日发生在支付宝账单的费用，不参与计算', formatter: (row) => row.dK1 == 0 ? " " : row.dK1 },
    { istrue: true, summaryEvent: true, prop: 'dK9', label: '微信-投流推广佣金(不计算)', sortable: 'custom', width: '100', tipmesg: '不计算', formatter: (row) => row.dK9 == 0 ? " " : row.dK9 },
    { istrue: true, summaryEvent: true, prop: 'tuoguanYG', label: '推广总消耗预估', sortable: 'custom', width: '100', tipmesg: '取自店铺后台推广界面当日消耗，参与计算（右侧有明细）', formatter: (row) => row.tuoguanYG == 0 ? " " : row.tuoguanYG },
    { istrue: true, summaryEvent: true, prop: 'promotionCommissionConsumedAmount', label: '推广佣金消耗金额-预估（不计算）', sortable: 'custom', width: '100', tipmesg: '佣金率推广预估费用（取自店铺后台）', formatter: (row) => row.promotionCommissionConsumedAmount == 0 ? " " : row.promotionCommissionConsumedAmount },
     { istrue: true, summaryEvent: true, prop: 'dailyBudgetConsumedAmount', label: '日预算消耗金额（不计算）', sortable: 'custom', width: '100', tipmesg: '日预算消耗金额', formatter: (row) => row.dailyBudgetConsumedAmount == 0 ? " " : row.dailyBudgetConsumedAmount },
     { istrue: true, summaryEvent: true, prop: 'weeklyBudgetConsumedAmount', label: '周预算消耗金额（不计算）', sortable: 'custom', width: '100', tipmesg: '周期预算消耗金额', formatter: (row) => row.weeklyBudgetConsumedAmount == 0 ? " " : row.weeklyBudgetConsumedAmount },
     { istrue: true, summaryEvent: true, prop: 'budgetIncreaseConsumedAmount', label: '预算加码消耗金额（不计算）', sortable: 'custom', width: '100', tipmesg: '加码消耗费用', formatter: (row) => row.budgetIncreaseConsumedAmount == 0 ? " " : row.budgetIncreaseConsumedAmount },{ istrue: true, summaryEvent: true, prop: 'zhitongche', label: '无界', sortable: 'custom', width: '70', formatter: (row) => row.zhitongche == 0 ? " " : row.zhitongche },
      { istrue: true, summaryEvent: true, prop: 'yanXuanWuJie', label: '严选无界', sortable: 'custom', width: '70', },
      { istrue: true, summaryEvent: true, prop: 'duoMuBiaoZhiTou', label: '多目标直投', sortable: 'custom', width: '70', formatter: (row) => row.duoMuBiaoZhiTou == 0 ? " " : row.duoMuBiaoZhiTou },
      { istrue: true, summaryEvent: true, prop: 'guanJianCiTuiGuang', label: '关键词推广', sortable: 'custom', width: '70', formatter: (row) => row.guanJianCiTuiGuang == 0 ? " " : row.guanJianCiTuiGuang },
      { istrue: true, summaryEvent: true, prop: 'huoDongJiaSu', label: '活动加速', sortable: 'custom', width: '70', formatter: (row) => row.huoDongJiaSu == 0 ? " " : row.huoDongJiaSu },
      { istrue: true, summaryEvent: true, prop: 'huoPinJiaSu', label: '货品加速', sortable: 'custom', width: '70', formatter: (row) => row.huoPinJiaSu == 0 ? " " : row.huoPinJiaSu },
      { istrue: true, summaryEvent: true, prop: 'jingZhunRenQunTuiGuang', label: '精准人群推广', sortable: 'custom', width: '70', formatter: (row) => row.jingZhunRenQunTuiGuang == 0 ? " " : row.jingZhunRenQunTuiGuang },
      { istrue: true, summaryEvent: true, prop: 'laXinKuai', label: '拉新快', sortable: 'custom', width: '70', formatter: (row) => row.laXinKuai == 0 ? " " : row.laXinKuai },
      { istrue: true, summaryEvent: true, prop: 'quanZhanTuiGuang', label: '全站推广', sortable: 'custom', width: '70', formatter: (row) => row.quanZhanTuiGuang == 0 ? " " : row.quanZhanTuiGuang },
      { istrue: true, summaryEvent: true, prop: 'shangXinKuai', label: '上新快', sortable: 'custom', width: '70', formatter: (row) => row.shangXinKuai == 0 ? " " : row.shangXinKuai },
      { istrue: true, summaryEvent: true, prop: 'yinglimofang', label: '阿里妈妈代投', sortable: 'custom', width: '70', formatter: (row) => row.yinglimofang == 0 ? " " : row.yinglimofang },
      { istrue: true, summaryEvent: true, prop: 'dK25', label: '品牌新享佣金扣费', sortable: 'custom', width: '80', tipmesg: '品牌新享佣金扣费', formatter: (row) => row.dK25 == 0 ? " " : row.dK25 },
      { istrue: true, summaryEvent: true, prop: 'dK48', label: '手淘直播佣金扣费', sortable: 'custom', width: '80', tipmesg: '66010185手淘直播佣金扣费', formatter: (row) => row.dK48 == 0 ? " " : row.dK48 },
      { istrue: true, summaryEvent: true, prop: 'dK11', label: '微信-直播推广佣金', sortable: 'custom', width: '80', formatter: (row) => row.dK11 == 0 ? " " : row.dK11 },
      { istrue: true, summaryEvent: true, prop: 'dK6', label: '商家出资单品补贴（不计算）', sortable: 'custom', width: '80', tipmesg: '商家出资单品补贴（不计算）', formatter: (row) => row.dK6 == 0 ? " " : row.dK6 },
      { istrue: true, summaryEvent: true, prop: 'yyShangJiaChuZi', label: '商家出资补贴预估', sortable: 'custom', width: '80', tipmesg: '商家出资补贴预估', formatter: (row) => row.yyShangJiaChuZi == 0 ? " " : row.yyShangJiaChuZi },
      { istrue: true, summaryEvent: true, prop: 'signUpRecordAmount', label: '商家出资补贴超链接预估', sortable: 'custom', width: '80', formatter: (row) => row.signUpRecordAmount == 0 ? " " : row.signUpRecordAmount },
      { istrue: true, summaryEvent: true, prop: 'dK7', label: '淘客扣佣', sortable: 'custom', width: '80', tipmesg: '淘客扣佣', formatter: (row) => row.dK7 == 0 ? " " : row.dK7 },
      { istrue: true, summaryEvent: true, prop: 'dK10', label: '微信-淘客推广佣金', sortable: 'custom', width: '80', formatter: (row) => row.dK10 == 0 ? " " : row.dK10 },
      { istrue: true, summaryEvent: true, prop: 'merchantSubsidy', label: '商家出资补贴-货款', sortable: 'custom', width: '80', tipmesg: '商家出资补贴-货款', formatter: (row) => row.merchantSubsidy == 0 ? " " : row.merchantSubsidy },
      { istrue: true, summaryEvent: true, prop: 'sumadv', label: '求和', sortable: 'custom', width: '70', formatter: (row) => row.sumadv == 0 ? " " : row.sumadv },
    ]
  },
  {
    istrue: true, summaryEvent: true, prop: 'yyfy22', label: `运营费用ROI`, merge: true, prop: 'mergeField9', permission: "cgcoltxpddprsi2", width: '70',
    cols: [
      { istrue: true, summaryEvent: true, prop: 'duoMuBiaoZhiTouCjMoney', label: '多目标直投成交', sortable: 'custom', width: '70', formatter: (row) => row.duoMuBiaoZhiTouCjMoney == 0 ? " " : row.duoMuBiaoZhiTouCjMoney },
      { istrue: true, summaryEvent: true, prop: 'duoMuBiaoZhiTouRoi', label: '多目标直投ROI', sortable: 'custom', tipmesg: '总成交金额/花费', width: '70', formatter: (row) => row.duoMuBiaoZhiTouRoi == 0 ? " " : row.duoMuBiaoZhiTouRoi },
      { istrue: true, summaryEvent: true, prop: 'guanJianCiTuiGuangCjMoney', label: '关键词推广成交', sortable: 'custom', width: '70', formatter: (row) => row.guanJianCiTuiGuangCjMoney == 0 ? " " : row.guanJianCiTuiGuangCjMoney },
      { istrue: true, summaryEvent: true, prop: 'guanJianCiTuiGuangRoi', label: '关键词推广ROI', sortable: 'custom', tipmesg: '总成交金额/花费', width: '70', formatter: (row) => row.guanJianCiTuiGuangRoi == 0 ? " " : row.guanJianCiTuiGuangRoi },
      { istrue: true, summaryEvent: true, prop: 'huoDongJiaSuCjMoney', label: '活动加速成交', sortable: 'custom', width: '70', formatter: (row) => row.huoDongJiaSuCjMoney == 0 ? " " : row.huoDongJiaSuCjMoney },
      { istrue: true, summaryEvent: true, prop: 'huoDongJiaSuRoi', label: '活动加速ROI', sortable: 'custom', tipmesg: '总成交金额/花费', width: '70', formatter: (row) => row.huoDongJiaSuRoi == 0 ? " " : row.huoDongJiaSuRoi },
      { istrue: true, summaryEvent: true, prop: 'huoPinJiaSuCjMoney', label: '货品加速成交', sortable: 'custom', width: '70', formatter: (row) => row.huoPinJiaSuCjMoney == 0 ? " " : row.huoPinJiaSuCjMoney },
      { istrue: true, summaryEvent: true, prop: 'huoPinJiaSuRoi', label: '货品加速ROI', sortable: 'custom', tipmesg: '总成交金额/花费', width: '70', formatter: (row) => row.huoPinJiaSuRoi == 0 ? " " : row.huoPinJiaSuRoi },
      { istrue: true, summaryEvent: true, prop: 'jingZhunRenQunTuiGuangCjMoney', label: '精准人群推广成交', sortable: 'custom', width: '70', formatter: (row) => row.jingZhunRenQunTuiGuangCjMoney == 0 ? " " : row.jingZhunRenQunTuiGuangCjMoney },
      { istrue: true, summaryEvent: true, prop: 'jingZhunRenQunTuiGuangRoi', label: '精准人群推广ROI', sortable: 'custom', tipmesg: '总成交金额/花费', width: '70', formatter: (row) => row.jingZhunRenQunTuiGuangRoi == 0 ? " " : row.jingZhunRenQunTuiGuangRoi },
      { istrue: true, summaryEvent: true, prop: 'laXinKuaiCjMoney', label: '拉新快成交', sortable: 'custom', width: '70', formatter: (row) => row.laXinKuaiCjMoney == 0 ? " " : row.laXinKuaiCjMoney },
      { istrue: true, summaryEvent: true, prop: 'laXinKuaiRoi', label: '拉新快成交ROI', sortable: 'custom', tipmesg: '总成交金额/花费', width: '70', formatter: (row) => row.laXinKuaiRoi == 0 ? " " : row.laXinKuaiRoi },
      { istrue: true, summaryEvent: true, prop: 'quanZhanTuiGuangCjMoney', label: '全站推广成交', sortable: 'custom', width: '70', formatter: (row) => row.quanZhanTuiGuangCjMoney == 0 ? " " : row.quanZhanTuiGuangCjMoney },
      { istrue: true, summaryEvent: true, prop: 'quanZhanTuiGuangRoi', label: '全站推广成交ROI', sortable: 'custom', tipmesg: '总成交金额/花费', width: '70', formatter: (row) => row.quanZhanTuiGuangRoi == 0 ? " " : row.quanZhanTuiGuangRoi },
      { istrue: true, summaryEvent: true, prop: 'shangXinKuaiCjMoney', label: '上新快成交', sortable: 'custom', width: '70', formatter: (row) => row.shangXinKuaiCjMoney == 0 ? " " : row.shangXinKuaiCjMoney },
      { istrue: true, summaryEvent: true, prop: 'shangXinKuaiRoi', label: '上新快成交ROI', sortable: 'custom', tipmesg: '总成交金额/花费', width: '70', formatter: (row) => row.shangXinKuaiRoi == 0 ? " " : row.shangXinKuaiRoi },
    ]
  },

  { istrue: true, summaryEvent: true, prop: 'alladv', label: '总广告费', sortable: 'custom', width: '70', tipmesg: '运营费用求和+淘宝客佣金+淘宝客本金+淘宝客服务费+特殊单佣金', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
  { istrue: true, summaryEvent: true, prop: 'advratio', label: '广告占比%', sortable: 'custom', width: '70', formatter: (row) => !row.advratio ? " " : (row.advratio) + "%" },
  { istrue: true, summaryEvent: true, prop: 'packageAvgFee', label: '包装均价', sortable: 'custom', width: '70', formatter: (row) => !row.packageAvgFee ? " " : row.packageAvgFee },
  { istrue: true, summaryEvent: true, prop: 'freightAvgFee', label: '快递均价', sortable: 'custom', width: '70', formatter: (row) => !row.freightAvgFee ? " " : row.freightAvgFee },

  { istrue: true, summaryEvent: true, prop: 'packageFee', label: '包装材料', sortable: 'custom', width: '80', formatter: (row) => !row.packageFee ? " " : row.packageFee },
  { istrue: true, summaryEvent: true, prop: 'freightFeeTotal', label: '快递费', sortable: 'custom', width: '80', type: 'custom', tipmesg: '实际快递费+虚拟快递费', formatter: (row) => !row.freightFeeTotal ? " " : row.freightFeeTotal },
  { istrue: true, summaryEvent: true, prop: 'freightFee', label: '实际快递费', sortable: 'custom', width: '80', type: 'click', handle: (that, row) => that.showFreightDetail(row) },
  { istrue: true, summaryEvent: true, prop: 'freightFeeVirtual', label: '虚拟快递费', sortable: 'custom', width: '80', tipmesg: '(订单量-发货前退款订单量-代发订单量)*平均快递费', formatter: (row) => !row.freightFeeVirtual ? " " : row.freightFeeVirtual },
  { istrue: true, summaryEvent: true, prop: 'freightAvgWeight', label: '快递均重', sortable: 'custom', width: '85', formatter: (row) => !row.freightAvgWeight ? " " : row.freightAvgWeight },

  { istrue: true, summaryEvent: true, prop: 'yyProfit2Wc', label: '毛二利润', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛一-快递费-包装费-总广费费', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit2Wc ? " " : row.yyProfit2Wc },
  { istrue: true, summaryEvent: true, prop: 'yyProfit2WcRate', label: '毛二利润率', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛二利润/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit2WcRate ? " " : row.yyProfit2WcRate + '%' },
  {
    istrue: true, summaryEvent: true, prop: 'yyProfit2', label: '毛二利润（财务）', sortable: 'custom', width: '70', permission: "DailyFinancialAuthority", type: 'custom', tipmesg: '毛一-预估扣点-C2M直联营2-广告费-包装费-快递费',
    formatter: (row) => !row.yyProfit2 ? " " : row.yyProfit2
  },
  { istrue: true, summaryEvent: true, prop: 'yyProfit2Rate', label: '毛二利润率（财务）', sortable: 'custom', width: '80', permission: "DailyFinancialAuthority", type: 'custom', tipmesg: '毛二利润/销售金额', formatter: (row) => !row.yyProfit2Rate ? " " : row.yyProfit2Rate + '%' },

  { istrue: true, summaryEvent: true, prop: 'profit3PredictFee', label: '预估费用', type: 'custom', tipmesg: '毛三预估比例*销售金额', permission: "lirunprsi", sortable: 'custom', width: '80', formatter: (row) => !row.profit3PredictFee ? " " : row.profit3PredictFee },
  {
    istrue: true, summaryEvent: true, prop: 'ygfy', label: `预估费用（正式）`, merge: true, prop: 'mergeField10', width: '70',
    cols: [
      { istrue: true, summaryEvent: true, prop: 'productFreight', label: '产品运费', sortable: 'custom', width: '80', formatter: (row) => row.productFreight == 0 ? " " : row.productFreight, tipmesg: '总销售成本*1.2%' },
      { istrue: true, summaryEvent: true, prop: 'cuiShouFee', label: '催收费', sortable: 'custom', width: '80', formatter: (row) => row.cuiShouFee == 0 ? " " : row.cuiShouFee, tipmesg: '每日导入' },
      { istrue: true, summaryEvent: true, prop: 'sampleFeeBX', label: '样品费', sortable: 'custom', width: '80', formatter: (row) => row.sampleFeeBX == 0 ? " " : row.sampleFeeBX, tipmesg: '每日导入' },
      { istrue: true, summaryEvent: true, prop: 'sampleFee', label: '拿样费', sortable: 'custom', width: '80', formatter: (row) => row.sampleFee == 0 ? " " : row.sampleFee, tipmesg: '运营/美工拿样，预估，5000元/月，每日167元' },
      { istrue: true, summaryEvent: true, prop: 'shootFee', label: '道具费', sortable: 'custom', width: '80', formatter: (row) => row.shootFee == 0 ? " " : row.shootFee, tipmesg: '美工拍摄道具费，预估，10000元/月' },
      { istrue: true, summaryEvent: true, prop: 'wages', label: '运营小组工资', sortable: 'custom', width: '80', formatter: (row) => row.wages == 0 ? " " : row.wages, tipmesg: '(销售金额-刷单金额-发货前退款-发货后退款)*运营工资占比' },
      { istrue: true, summaryEvent: true, prop: 'customerServiceWages', label: '客服工资', sortable: 'custom', width: '70', formatter: (row) => row.customerServiceWages == 0 ? " " : row.customerServiceWages, tipmesg: '' },
      { istrue: true, summaryEvent: true, prop: 'processingCost', label: '加工部工资', sortable: 'custom', width: '70', formatter: (row) => row.processingCost == 0 ? " " : row.processingCost, tipmesg: '参考日报数据维护-加工费（工价*（订单量-发货前退款单量））' },
      { istrue: true, summaryEvent: true, prop: 'lossOffFee', label: '损耗下架', sortable: 'custom', width: '80', formatter: (row) => row.lossOffFee == 0 ? " " : row.lossOffFee, tipmesg: '预估，0.1%' },
      { istrue: true, summaryEvent: true, prop: 'serviceFee', label: '客服费', sortable: 'custom', width: '80', formatter: (row) => row.serviceFee == 0 ? " " : row.serviceFee },
      //{istrue:true,summaryEvent:true,prop:'warehousingFee',label:'仓储费',sortable:'custom', width:'80',formatter:(row)=> row.serviceFee==0?" ": row.serviceFee},
    ]
  },

  { istrue: true, summaryEvent: true, prop: 'yyProfit3Wc', label: '毛三利润', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛二-产品运费-运营工资-客服工资(外仓率≥80%时红色显示)', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3Wc ? " " : row.yyProfit3Wc },
  { istrue: true, summaryEvent: true, prop: 'yyProfit3WcRate', label: '毛三利润率', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛三利润/销售金额(外仓率≥80%时红色显示)', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3WcRate ? " " : row.yyProfit3WcRate+ '%' },



  { istrue: true, summaryEvent: true, prop: 'exitCost', label: '出仓成本', sortable: 'custom', width: '70', formatter: (row) => !row.exitCost ? " " : row.exitCost },
  { istrue: true, summaryEvent: true, prop: 'exitCostRate', label: '出仓成本占比', sortable: 'custom', width: '70', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '70', formatter: (row) => !row.warehouseSalary ? " " : row.warehouseSalary },
  { istrue: true, summaryEvent: true, prop: 'exitProfit', label: '出仓利润', sortable: 'custom', width: '70', formatter: (row) => !row.exitProfit ? " " : row.exitProfit },
  { istrue: true, summaryEvent: true, prop: 'isExitProfit', label: '正出仓利润', sortable: 'custom', width: '70', type: 'click', formatter: (row) => !row.isExitProfit ? " " : row.isExitProfit, handle: (that, row) => that.onGoodsProfitShow(row, 1) },
  { istrue: true, summaryEvent: true, prop: 'negativeExitProfit', label: '负出仓利润', sortable: 'custom', width: '70', type: 'click', formatter: (row) => !row.negativeExitProfit ? " " : row.negativeExitProfit, handle: (that, row) => that.onGoodsProfitShow(row, 2) },

  { istrue: true, summaryEvent: true, prop: 'yyProfit4Wc', label: '毛四利润', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛三-出仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4Wc ? " " : row.yyProfit4Wc },
  { istrue: true, summaryEvent: true, prop: 'yyProfit4WcRate', label: '毛四利润率', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛四利润/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4WcRate ? " " : row.yyProfit4WcRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit5Wc', label: '毛五利润', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛四利润-仓库薪资', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit5Wc ? " " : row.yyProfit5Wc },
  { istrue: true, summaryEvent: true, prop: 'yyProfit5WcRate', label: '毛五利润率', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛五利润/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit5WcRate ? " " : row.yyProfit5WcRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit6', label: '毛六利润', sortable: 'custom', width: '80', tipmesg: '毛五利润-(每笔订单的实际快递费、包装费、出仓成本-按云仓计算出来的快递费)', permission: "profit6SixtyCents", formatter: (row) => !row.yyProfit6 ? " " : row.yyProfit6 },
  { istrue: true, summaryEvent: true, prop: 'yyProfit6Rate', label: '毛六利润率', sortable: 'custom', width: '80', tipmesg: '毛六利润/销售金额', permission: "profit6SixtyCents", formatter: (row) => !row.yyProfit6Rate ? " " : row.yyProfit6Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit3', label: '毛三利润（财务）', sortable: 'custom', width: '80', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit3 ? " " : row.yyProfit3 },
  { istrue: true, summaryEvent: true, prop: 'yyProfit3Rate', label: '毛三利润率（财务）', sortable: 'custom', width: '80', tipmesg: '毛三利润/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit3Rate ? " " : row.yyProfit3Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit4', label: '毛四利润（财务）', sortable: 'custom', width: '80', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit4 ? " " : row.yyProfit4 },
  { istrue: true, summaryEvent: true, prop: 'yyProfit4Rate', label: '毛四利润率（财务）', sortable: 'custom', width: '80', tipmesg: '毛四利润/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit4Rate ? " " : row.yyProfit4Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit5', label: '毛五利润（财务）', sortable: 'custom', width: '80', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit5 ? " " : row.yyProfit5 },
  { istrue: true, summaryEvent: true, prop: 'yyProfit5Rate', label: '毛五利润率（财务）', sortable: 'custom', width: '80', permission: "DailyFinancialAuthority", tipmesg: '毛五利润/销售金额', formatter: (row) => !row.yyProfit5Rate ? " " : row.yyProfit5Rate + '%' },

  {
    istrue: true, summaryEvent: true, rop: '', label: `退款`, merge: true, prop: 'mergeField11', width: '70',
    cols: [
      { istrue: true, summaryEvent: true, prop: 'yyRefundAmontBefore', label: '发货前退款', sortable: 'custom', width: '80', type: 'custom' },
      { istrue: true, summaryEvent: true, prop: 'yyRefundAmontBeforeRate', label: '发货前退款率', sortable: 'custom', tipmesg: '发货前退款/付款金额', width: '80', type: 'custom', formatter: (row) => !row.yyRefundAmontBeforeRate ? " " : (row.yyRefundAmontBeforeRate) + '%' },
      { istrue: true, summaryEvent: true, prop: 'yyRefundAmontAfter', label: '发货后退款', sortable: 'custom', width: '80', type: 'custom' },
      { istrue: true, summaryEvent: true, prop: 'yyRefundAmontAfterRate', label: '发货后退款率', sortable: 'custom', tipmesg: '发货后退款/付款金额', width: '80', type: 'custom', formatter: (row) => !row.yyRefundAmontAfterRate ? " " : (row.yyRefundAmontAfterRate) + '%' },
      { istrue: true, summaryEvent: true, prop: 'yyRefundAmont', label: '总退款金额', sortable: 'custom', width: '80', tipmesg: '当日发生的总退款金额，包括历史订单', formatter: (row) => !row.yyRefundAmont ? " " : row.yyRefundAmont },
    ]
  },
  { istrue: true, summaryEvent: true, prop: 'yySaleAmountAfter', label: '净销售额', sortable: 'custom', width: '70', type: 'custom', tipmesg: '销售金额-总退款金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yySaleAmountAfter ? " " : row.yySaleAmountAfter },
  { istrue: true, summaryEvent: true, prop: 'cancelCost', label: '取消单返还成本', sortable: 'custom', width: '80', tipmesg: '返还客服已确认的取消单成本,以确认时间统计', formatter: (row) => !row.cancelCost ? " " : row.cancelCost, type: 'click', handle: (that, row) => that.showEveryDayrefund(row) },
  { istrue: true, summaryEvent: true, prop: 'refundCostSj', label: '销退仓返还成本', sortable: 'custom', width: '80', formatter: (row) => !row.refundCostSj ? " " : row.refundCostSj },


  { istrue: true, summaryEvent: true, prop: 'yyProfit1After', label: '毛一（减退款）', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛一-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
  { istrue: true, summaryEvent: true, prop: 'yyProfit1AfterRate', label: '毛一利润率（减退款）', sortable: 'custom', width: '90', type: 'custom', tipmesg: '毛一（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit1AfterRate ? " " : row.yyProfit1AfterRate + '%' },

  { istrue: true, summaryEvent: true, prop: 'yyProfit2WcAfter', label: '毛二（减退款）', sortable: 'custom', width: '90', type: 'custom', tipmesg: '毛二-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit2WcAfter ? " " : row.yyProfit2WcAfter },
  { istrue: true, summaryEvent: true, prop: 'yyProfit2WcAfterRate', label: '毛二利润率（减退款）', sortable: 'custom', width: '90', type: 'custom', tipmesg: '毛二（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit2WcAfterRate ? " " : row.yyProfit2WcAfterRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit2After', label: '财务毛二（减退款）', sortable: 'custom', width: '90', type: 'custom', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
  { istrue: true, summaryEvent: true, prop: 'yyProfit2AfterRate', label: '财务毛二利润率（减退款）', sortable: 'custom', width: '90', type: 'custom', tipmesg: '财务毛二(减退款)/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit2AfterRate ? " " : row.yyProfit2AfterRate + '%' },

  { istrue: true, summaryEvent: true, prop: 'yyProfit3WcAfter', label: '毛三（减退款）', sortable: 'custom', width: '90', type: 'custom', tipmesg: '毛三-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3WcAfter ? " " : row.yyProfit3WcAfter },
  { istrue: true, summaryEvent: true, prop: 'yyProfit3WcAfterRate', label: '毛三利润率（减退款）', sortable: 'custom', width: '90', type: 'custom', tipmesg: '毛三（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3WcAfterRate ? " " : row.yyProfit3WcAfterRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit4WcAfter', label: '毛四（减退款）', sortable: 'custom', width: '90', type: 'custom', tipmesg: '毛四-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4WcAfter ? " " : row.yyProfit4WcAfter },
  { istrue: true, summaryEvent: true, prop: 'yyProfit4WcAfterRate', label: '毛四利润率（减退款）', sortable: 'custom', width: '90', type: 'custom', tipmesg: '毛四（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4WcAfterRate ? " " : row.yyProfit4WcAfterRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit5WcAfter', label: '毛五（减退款）', sortable: 'custom', width: '90', type: 'custom', tipmesg: '毛五-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit5WcAfter ? " " : row.yyProfit5WcAfter },
  { istrue: true, summaryEvent: true, prop: 'yyProfit5WcAfterRate', label: '毛五利润率（减退款）', sortable: 'custom', width: '90', type: 'custom', tipmesg: '毛五（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit5WcAfterRate ? " " : row.yyProfit5WcAfterRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit6After', label: '毛六（减退款）', sortable: 'custom', width: '90', tipmesg: '毛六-总退款+取消单返还成本+销退仓成本', permission: "profit6SixtyCents", formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
  { istrue: true, summaryEvent: true, prop: 'yyProfit6AfterRate', label: '毛六利润率（减退款）', sortable: 'custom', width: '90', tipmesg: '毛六（减退款）/销售金额', permission: "profit6SixtyCents", formatter: (row) => !row.yyProfit6AfterRate ? " " : row.yyProfit6AfterRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit3After', label: '财务毛三(减退款)', sortable: 'custom', width: '90', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
  { istrue: true, summaryEvent: true, prop: 'yyProfit3AfterRate', label: '财务毛三利润率(减退款)', sortable: 'custom', width: '100', tipmesg: '财务毛三(减退款)/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit3AfterRate ? " " : row.yyProfit3AfterRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit4After', label: '财务毛四(减退款)', sortable: 'custom', width: '90', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
  { istrue: true, summaryEvent: true, prop: 'yyProfit4AfterRate', label: '财务毛四利润率(减退款)', sortable: 'custom', width: '100', tipmesg: '财务毛四(减退款)/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit4AfterRate ? " " : row.yyProfit4AfterRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit5After', label: '财务毛五(减退款)', sortable: 'custom', width: '90', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
  { istrue: true, summaryEvent: true, prop: 'yyProfit5AfterRate', label: '财务毛五利润率(减退款)', sortable: 'custom', width: '100', tipmesg: '财务毛五(减退款)/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit5AfterRate ? " " : row.yyProfit5AfterRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfitDiff', label: '差额（毛一，毛二，毛三）', sortable: 'custom', width: '100', type: 'custom', tipmesg: '总退款-取消单返还成本-销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfitDiff ? " " : row.yyProfitDiff },


  // { istrue: true, summaryEvent: true, prop: 'profit4', label: '净利润', type: 'custom', tipmesg: '毛三利润-公摊费-盘点损益', sortable: 'custom', width: '75', permission: "lirunprsi", formatter: (row) => !row.profit4 ? " " : row.profit4 },
  // { istrue: true, summaryEvent: true, prop: 'profit4Rate', label: '净利率', type: 'custom', tipmesg: '净利润/销售金额', sortable: 'custom', width: '75', permission: "lirunprsi", formatter: (row) => !row.profit4Rate ? " " : row.profit4Rate + '%' },
  // { istrue: true, summaryEvent: true, prop: 'goodProfitRate', label: '商品毛利率', sortable: 'custom', width: '95', tipmesg: '（付款金额-（销售成本+赠品成本+赠品链接成本+代发成本差））/付款金额', formatter: (row) => !row.goodProfitRate ? " " : row.goodProfitRate + '%' },

  { istrue: true, summaryEvent: true, prop: 'giftAmont', label: '赠品成本', sortable: 'custom', width: '70', formatter: (row) => !row.giftAmont ? ' ' : row.giftAmont, type: 'click', handle: (that, row) => that.showGiftDetail(row) },
  { istrue: true, summaryEvent: true, prop: 'saleCostAvg', label: '销售成本(分摊)', sortable: 'custom', width: '100', tipmesg: '销售成本(无id分摊)', formatter: (row) => !row.saleCostAvg ? " " : row.saleCostAvg },
  { istrue: true, summaryEvent: true, prop: 'diffCornerCost', label: '定制款成本差', sortable: 'custom', width: '110', formatter: (row) => row.diffCornerCost == 0 ? " " : row.diffCornerCost },

  //{istrue:true,summaryEvent:true,prop:'dkAmont',label:'平台扣点',sortable:'custom', width:'80',type:'custom',formatter:(row)=> !row.dkAmont?" ": row.dkAmont},

  {
    istrue: true, summaryEvent: true, prop: 'taobaoke', label: `淘宝客`, merge: true, prop: 'mergeField12',
    cols: [
      { istrue: true, summaryEvent: true, prop: 'taobaoke_ben', label: '本金', sortable: 'custom', width: '80', formatter: (row) => !row.taobaoke_ben ? " " : row.taobaoke_ben },
      { istrue: true, summaryEvent: true, prop: 'taobaoke_yong', label: '佣金', sortable: 'custom', width: '80', formatter: (row) => row.taobaoke_yong == 0 ? " " : row.taobaoke_yong },
      { istrue: true, summaryEvent: true, prop: 'taobaoke_fuwu', label: '服务费', sortable: 'custom', width: '80', formatter: (row) => row.taobaoke_fuwu == 0 ? " " : row.taobaoke_fuwu },
    ]
  },
  { istrue: true, summaryEvent: true, prop: 'shoudanlijing', label: '首单礼金', sortable: 'custom', width: '80', formatter: (row) => row.shoudanlijing == 0 ? " " : row.shoudanlijing },

  //{istrue:true,summaryEvent:true,prop:'cashRedAmount',label:'现金红包',sortable:'custom', width:'80',formatter:(row)=> row.cashRedAmount==0?" ": row.cashRedAmount,},
  //{istrue:true,summaryEvent:true,prop:'refundAmontHistory',label:'历史退款金额',sortable:'custom', width:'90',tipmesg:'退款时间是当日且订单支付时间是以前的退款金额',formatter:(row)=> !row.refundAmontHistory?" ": row.refundAmontHistory},

  {
    istrue: true, summaryEvent: true, prop: 'profit3PredictRate', label: '毛三预估比例', type: 'custom', permission: "lirunprsi", tipmesg: '(空白链接ID成本+异常成本+补发成本+代发成本+采购运费+产品费用+工资+损耗)/销售金额',
    sortable: 'custom', width: '80', formatter: (row) => !row.profit3PredictRate ? " " : (row.profit3PredictRate * 100)?.toFixed(0) + '%'
  },


  { istrue: true, summaryEvent: true, prop: 'shareRate', label: '公摊费率', type: 'custom', tipmesg: '明细2/销售金额', sortable: 'custom', width: '80', permission: "lirunprsi", formatter: (row) => !row.shareRate ? " " : (row.shareRate * 100)?.toFixed(0) + '%' },
  { istrue: true, summaryEvent: true, prop: 'shareFee', label: '公摊费', type: 'custom', tipmesg: '公摊费(%)*销售金额', sortable: 'custom', width: '80', permission: "lirunprsi", formatter: (row) => !row.shareFee ? " " : row.shareFee },
  { istrue: true, summaryEvent: true, prop: 'inventoryCheckFee', label: '盘点损益', sortable: 'custom', width: '95', formatter: (row) => row.inventoryCheckFee == 0 ? " " : row.inventoryCheckFee, tipmesg: '(盘亏+盘盈),商品ID对应的商品编码在仓库中盘亏的金额,多个商品ID共有的商品编码前7天的销售成本分摊', type: 'click', handle: (that, row) => that.showInventoryCheckFee(row) },
  { istrue: true, summaryEvent: true, prop: 'brandFee1', label: '品牌管理费用1', type: 'custom', sortable: 'custom', width: '90', permission: "brandManagementPermis", formatter: (row) => !row.brandFee1 ? " " : row.brandFee1 },
  { istrue: true, summaryEvent: true, prop: 'brandFee2', label: '品牌管理费用2', type: 'custom', sortable: 'custom', width: '90', permission: "brandManagementPermis", formatter: (row) => !row.brandFee2 ? " " : row.brandFee2 },
];

  const tableCols2 = [
  { istrue: true, fixed: 'left', width: '40', type: "checkbox" },
  { istrue: true, fixed: 'left', prop: 'aIAnalysis', label: 'AI分析', sortable: 'custom', width: '60', type: 'click' , formatter: (row) => { return `AI分析`; }, handle: (that, row) => that.onAI2ProcodeShow(row)},
  { istrue: true, fixed: 'left', prop: 'yearMonthDay', label: '年月日', sortable: 'custom', width: '100', type: 'custom', formatter: (row) => row.yearMonthDay || '', treeNode: true },
  { istrue: true, fixed: 'left',prop: 'platform', fix: true, exportField: 'platformstr', label: '平台', width: '35', sortable: 'custom', formatter: (row) => formatPlatform(row.platform), type: 'custom' },
  { istrue: true, fixed: 'left',prop: 'styleCode', label: '系列编码', sortable: 'custom', width: '70', formatter: (row) => row.styleCode || ' ', type: 'click', handle: (that, row) => that.showProcodesimilarity(row) },
  { istrue: true, fixed: 'left',prop: 'shopCode', exportField: 'shopName', label: '店铺名称', sortable: 'custom', width: '70', formatter: (row) => row.shopName, type: 'custom' },
  { istrue: true, fixed: 'left',label: '小组头像',  width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
  { istrue: true, fixed: 'left',prop: 'groupId', exportField: 'groupName', label: '小组', sortable: 'custom', width: '70', formatter: (row) => row.groupName,type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, },
  { istrue: true, fixed: 'left',label: '专员头像', width: '70',type:'ddAvatar',ddInfo:{type:2,prop:'operateSpecialUserId'} },
  { istrue: true, fixed: 'left',prop: 'operateSpecialUserId', exportField: 'operateSpecialUserName', label: '运营专员', sortable: 'custom', width: '70', formatter: (row) => row.operateSpecialUserName,type:'ddTalk',ddInfo:{type:2,prop:'operateSpecialUserId',name:'operateSpecialUserName'}, },
  { istrue: true, fixed: 'left', type: 'echarts', prop: 'profit3IncreaseGoOnDays', chartProp: 'profit3IncreaseGoOnDaysChartData', fix: true, label: '毛六趋势', width: '80', permission: "lirunprsi,yyprofit3rsi" },
  { istrue: true, prop: 'proCode', fix: true, label: '商品ID', width: '90', sortable: 'custom', type: 'html', formatter: (row) => row.status==3 ?formatProCodeStutas3(row.proCode)  : formatLinkProCode(row.platform, row.proCode) },
  { istrue: true, fixed: 'left',prop: 'onTimeCount', exportField: 'onTimeCount', label: '上架天数', width: '75', formatter: (row, that) => getTimeDiff([row.onTime, that.filter.endTime]) },
  { istrue: true, prop: 'baoPin', label: '爆品', width: '80', sortable: 'custom', },
  { istrue: true, prop: 'baoPinOnTime', label: '爆品申报时间', width: '80', sortable: 'custom', formatter: (row) => formatTime(row.baoPinOnTime, 'YYYY-MM-DD') },
  { istrue: true, fixed: 'left',summaryEvent: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width: '65', tipmesg: '（付款订单量）此字段仅供参考；如果一个订单中有3种商品，这3行商品的销售订单数均为1，所以不能把此字段的合计值作为订单数量。因为订单就只有1个。但是销售订单数合计值为3。', formatter: (row) => !row.orderCount ? " " : row.orderCount },
  { istrue: true, fixed: 'left',summaryEvent: true, prop: 'wcOrderCountRate', label: '外仓率', sortable: 'custom', width: '65', tipmesg: '预包订单量/订单量', formatter: (row) => !row.wcOrderCountRate ? " " : row.wcOrderCountRate + '%' },
  { istrue: true, prop: 'ljWcRate', label: '累计外仓率', sortable: 'custom', width: '65', tipmesg: '当月的外仓率', formatter: (row) => !row.ljWcRate ? " " : row.ljWcRate + '%' },
  { istrue: true, fixed: 'left',summaryEvent: true, prop: 'payAmont', label: '付款金额', sortable: 'custom', width: '70', formatter: (row) => !row.payAmont ? " " : row.payAmont, type: 'click', handle: (that, row) => that.showOrderDetail(row) },
  { istrue: true, fixed: 'left',summaryEvent: true, prop: 'saleAmont', label: '销售金额（发生）', sortable: 'custom', width: '90', tipmesg: '已扣除当日发生的退款'},
 { istrue: true, fixed: 'left',summaryEvent: true, prop: 'negativeExitProfit', label: '负出仓利润', sortable: 'custom', width: '70', type: 'click', formatter: (row) => !row.negativeExitProfit ? " " : row.negativeExitProfit, handle: (that, row) => that.onGoodsProfitShow(row, 2) },
  { istrue: true, fixed: 'left',summaryEvent: true, prop: 'alladv', label: '总广告费', sortable: 'custom', width: '70', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
  { istrue: true, fixed: 'left',summaryEvent: true, prop: 'advratio', label: '广告占比%', sortable: 'custom', width: '80', formatter: (row) => !row.advratio ? " " : (row.advratio) + "%" },
  { istrue: true, prop: 'exitProfitLessZero', label: 'SKU', width: '45', tipmesg: '链接中有SKU出仓利润是负数时，显示红色字体', type: 'clickLink', formatter: (row) => { return `SKU+`; }, handle: (that, row) => that.onGoodsProfitShow(row), style: (that, row) => { return row.exitProfitLessZero ? "color:blue;cursor:pointer;color:red" : "color:blue;cursor:pointer;color:blue"; } },
  { istrue: true, summaryEvent: true, prop: 'profit1', label: '毛一利润（发生）', sortable: 'custom',width: '80' },
  { istrue: true, summaryEvent: true, prop: 'profit1Rate', label: '毛一利润率（发生）', sortable: 'custom',width: '80', },
  { istrue: true, summaryEvent: true, prop: 'yyProfit1', label: '运营毛一（不减退款）', sortable: 'custom', width: '90'},
  { istrue: true, summaryEvent: true, prop: 'yyProfit1Rate', label: '运营毛一利润率（不减退款）', sortable: 'custom', width: '100', },
  { istrue: true, summaryEvent: true, prop: 'yyProfit1After', label: '运营毛一（减退款）', sortable: 'custom', width: '90', tipmesg: '毛一-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi" , formatter: (row) => !row.yyProfit1After ? " " :  row.yyProfit1After },
  { istrue: true, summaryEvent: true, prop: 'yyProfit1AfterRate', label: '运营毛一利润率（减退款）', sortable: 'custom', width: '100', tipmesg: '毛一（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit1AfterRate ? " " : row.yyProfit1AfterRate + '%' },

  { istrue: true, summaryEvent: true, prop: 'profit2Wc', label: '毛二利润（发生）', sortable: 'custom',width: '80', },
  { istrue: true, summaryEvent: true, prop: 'profit2WcRate', label: '毛二利润率（发生）', sortable: 'custom',width: '80', formatter: (row) => !row.profit2WcRate ? " " : row.profit2WcRate + '%', },
  { istrue: true, summaryEvent: true, prop: 'yyProfit2Wc', label: '运营毛二（不减退款）', sortable: 'custom', width: '90', formatter: (row) => !row.yyProfit2Wc ? " " : row.yyProfit2Wc },
  { istrue: true, summaryEvent: true, prop: 'yyProfit2WcRate', label: '运营毛二利润率（不减退款）', sortable: 'custom', width: '100', formatter: (row) => !row.yyProfit2WcRate ? " " : row.yyProfit2WcRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit2WcAfter', label: '运营毛二（减退款）', sortable: 'custom', width: '90', tipmesg: '毛二-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit2WcAfter ? " " : row.yyProfit2WcAfter },
  { istrue: true, summaryEvent: true, prop: 'yyProfit2WcAfterRate', label: '运营毛二利润率（减退款）', sortable: 'custom', width: '100', tipmesg: '毛二（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit2WcAfterRate ? " " : row.yyProfit2WcAfterRate + '%' },

  { istrue: true, summaryEvent: true, prop: 'profit2', label: '财务毛二利润（发生）', sortable: 'custom',width: '80', permission: "DailyFinancialAuthority" },
  { istrue: true, summaryEvent: true, prop: 'profit2Rate', label: '财务毛二利润率（发生）', sortable: 'custom',width: '80', permission: "DailyFinancialAuthority", },
  { istrue: true, summaryEvent: true, prop: 'yyProfit2After', label: '财务运营毛二（减退款）', sortable: 'custom', width: '90', permission: "DailyFinancialAuthority", tipmesg: '毛二-总退款+取消单返还成本+销退仓成本', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
  { istrue: true, summaryEvent: true, prop: 'yyProfit2AfterRate', label: '财务运营毛二利润率（减退款）', sortable: 'custom', width: '100', permission: "DailyFinancialAuthority", tipmesg: '毛二（减退款）/销售金额', formatter: (row) => !row.yyProfit2AfterRate ? " " : row.yyProfit2AfterRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit2', label: '财务运营毛二（不减退款）', sortable: 'custom', width: '90', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit2 ? " " : row.yyProfit2 },
  { istrue: true, summaryEvent: true, prop: 'yyProfit2Rate', label: '财务运营毛二利润率（不减退款）', sortable: 'custom', width: '100', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit2Rate ? " " : row.yyProfit2Rate + '%' },

  { istrue: true, summaryEvent: true, prop: 'profit3Wc', label: '毛三利润（发生）', sortable: 'custom',width: '80', tipmesg: '外仓率≥80%时红色显示' },
  { istrue: true, summaryEvent: true, prop: 'profit3WcRate', label: '毛三利润率（发生）', sortable: 'custom',width: '80', tipmesg: '外仓率≥80%时红色显示', formatter: (row) => !row.profit3WcRate? " " : row.profit3WcRate+ '%', },
  { istrue: true, summaryEvent: true, prop: 'yyProfit3Wc', label: '运营毛三（不减退款）', sortable: 'custom', width: '90', formatter: (row) => !row.yyProfit3Wc ? " " : row.yyProfit3Wc },
  { istrue: true, summaryEvent: true, prop: 'yyProfit3WcRate', label: '运营毛三利润率（不减退款）', sortable: 'custom', width: '100', formatter: (row) => !row.yyProfit3WcRate ? " " : row.yyProfit3WcRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit3WcAfter', label: '运营毛三（减退款）', sortable: 'custom', width: '90', tipmesg: '毛三-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3WcAfter ? " " : row.yyProfit3WcAfter },
  { istrue: true, summaryEvent: true, prop: 'yyProfit3WcAfterRate', label: '运营毛三利润率（减退款）', sortable: 'custom', width: '100', tipmesg: '毛三（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3WcAfterRate ? " " : row.yyProfit3WcAfterRate + '%' },

  { istrue: true, summaryEvent: true, prop: 'profit4Wc', label: '毛四利润（发生）', sortable: 'custom',width: '80' },
  { istrue: true, summaryEvent: true, prop: 'profit4WcRate', label: '毛四利润率（发生）', sortable: 'custom',width: '80', formatter: (row) => !row.profit4WcRate ? " " : row.profit4WcRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit4Wc', label: '运营毛四（不减退款）', sortable: 'custom', width: '90', formatter: (row) => !row.yyProfit4Wc ? " " : row.yyProfit4Wc },
  { istrue: true, summaryEvent: true, prop: 'yyProfit4WcRate', label: '运营毛四利润率（不减退款）', sortable: 'custom', width: '100', formatter: (row) => !row.yyProfit4WcRate ? " " : row.yyProfit4WcRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit4WcAfter', label: '运营毛四（减退款）', sortable: 'custom', width: '90', tipmesg: '毛四-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4WcAfter ? " " : row.yyProfit4WcAfter },
  { istrue: true, summaryEvent: true, prop: 'yyProfit4WcAfterRate', label: '运营毛四利润率（减退款）', sortable: 'custom', width: '100', tipmesg: '毛四（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4WcAfterRate ? " " : row.yyProfit4WcAfterRate + '%' },

  { istrue: true, summaryEvent: true, prop: 'profit5Wc', label: '毛五利润（发生）', sortable: 'custom',width: '80', },
  { istrue: true, summaryEvent: true, prop: 'profit5WcRate', label: '毛五利润率（发生）', sortable: 'custom',width: '80', formatter: (row) => !row.profit5WcRate ? " " : row.profit5WcRate + '%', },
  { istrue: true, summaryEvent: true, prop: 'yyProfit5Wc', label: '运营毛五（不减退款）', sortable: 'custom', width: '90', formatter: (row) => !row.yyProfit5Wc ? " " : row.yyProfit5Wc },
  { istrue: true, summaryEvent: true, prop: 'yyProfit5WcRate', label: '运营毛五利润率（不减退款）', sortable: 'custom', width: '100', formatter: (row) => !row.yyProfit5WcRate ? " " : row.yyProfit5WcRate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit5WcAfter', label: '运营毛五（减退款）', sortable: 'custom', width: '90', tipmesg: '毛五-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit5WcAfter ? " " : row.yyProfit5WcAfter },
  { istrue: true, summaryEvent: true, prop: 'yyProfit5WcAfterRate', label: '运营毛五利润率（减退款）', sortable: 'custom', width: '100', tipmesg: '毛五（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit5WcAfterRate ? " " : row.yyProfit5WcAfterRate + '%' },

  { istrue: true, summaryEvent: true, prop: 'profit6', label: '毛六利润（发生）', sortable: 'custom',width: '80', permission: "profit6SixtyCents" },
  { istrue: true, summaryEvent: true, prop: 'profit6Rate', label: '毛六利润率（发生）', sortable: 'custom',width: '80', permission: "profit6SixtyCents" , formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + '%', },
  { istrue: true, summaryEvent: true, prop: 'yyProfit6', label: '运营毛六（不减退款）', sortable: 'custom', width: '90', permission: "profit6SixtyCents" , formatter: (row) => !row.yyProfit6 ? " " : row.yyProfit6 },
  { istrue: true, summaryEvent: true, prop: 'yyProfit6Rate', label: '运营毛六利润率（不减退款）', sortable: 'custom', width: '100', permission: "profit6SixtyCents" , formatter: (row) => !row.yyProfit6Rate ? " " : row.yyProfit6Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit6After', label: '运营毛六（减退款）', sortable: 'custom', width: '90', tipmesg: '毛六-总退款+取消单返还成本+销退仓成本', permission: "profit6SixtyCents", formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
  { istrue: true, summaryEvent: true, prop: 'yyProfit6AfterRate', label: '运营毛六利润率（减退款）', sortable: 'custom', width: '100', tipmesg: '毛六（减退款）/销售金额', permission: "profit6SixtyCents", formatter: (row) => !row.yyProfit6AfterRate ? " " : row.yyProfit6AfterRate + '%' },

  { istrue: true, summaryEvent: true, prop: 'profit3', label: '财务毛三利润(发生)', sortable: 'custom',width: '80', permission: "DailyFinancialAuthority", formatter: (row) => !row.profit3 ? " " : row.profit3 },
  { istrue: true, summaryEvent: true, prop: 'profit3Rate', label: '财务毛三利润率(发生)', sortable: 'custom',width: '80', tipmesg: '财务毛三利润(发生)/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.profit3Rate ? " " : row.profit3Rate + '%', },
  { istrue: true, summaryEvent: true, prop: 'yyProfit3', label: '运营财务毛三(不减退款)', sortable: 'custom', width: '90', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit3 ? " " : row.yyProfit3 },
  { istrue: true, summaryEvent: true, prop: 'yyProfit3Rate', label: '运营财务毛三利润率(不减退款)', sortable: 'custom', width: '100', tipmesg: '运营财务毛三(不减退款)/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit3Rate ? " " : row.yyProfit3Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit3After', label: '运营财务毛三(减退款)', sortable: 'custom', width: '90', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
  { istrue: true, summaryEvent: true, prop: 'yyProfit3AfterRate', label: '运营财务毛三利润率(减退款)', sortable: 'custom', width: '100', tipmesg: '运营财务毛三(减退款)/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit3AfterRate ? " " : row.yyProfit3AfterRate + '%' },

  { istrue: true, summaryEvent: true, prop: 'profit33', label: '财务毛四利润(发生)', sortable: 'custom',width: '80', permission: "DailyFinancialAuthority", formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, summaryEvent: true, prop: 'profit33Rate', label: '财务毛四利润率(发生)', sortable: 'custom',width: '80', tipmesg: '财务毛四利润(发生)/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + '%', },
  { istrue: true, summaryEvent: true, prop: 'yyProfit4', label: '运营财务毛四(不减退款)', sortable: 'custom', width: '90', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit4 ? " " : row.yyProfit4 },
  { istrue: true, summaryEvent: true, prop: 'yyProfit4Rate', label: '运营财务毛四利润率(不减退款)', sortable: 'custom', width: '100', tipmesg: '运营财务毛四(不减退款)/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit4Rate ? " " : row.yyProfit4Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit4After', label: '运营财务毛四(减退款)', sortable: 'custom', width: '90', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
  { istrue: true, summaryEvent: true, prop: 'yyProfit4AfterRate', label: '运营财务毛四利润率(减退款)', sortable: 'custom', width: '100', tipmesg: '运营财务毛四(减退款)/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit4AfterRate ? " " : row.yyProfit4AfterRate + '%' },

  { istrue: true, summaryEvent: true, prop: 'profit5', label: '财务毛五利润(发生)', sortable: 'custom',width: '80', permission: "DailyFinancialAuthority", formatter: (row) => !row.profit5 ? " " : row.profit5 },
  { istrue: true, summaryEvent: true, prop: 'profit5Rate', label: '财务毛五利润率(发生)', sortable: 'custom',width: '80', tipmesg: '财务毛五利润(发生)/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + '%', },
  { istrue: true, summaryEvent: true, prop: 'yyProfit5', label: '运营财务毛五(不减退款)', sortable: 'custom', width: '90', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit5 ? " " : row.yyProfit5 },
  { istrue: true, summaryEvent: true, prop: 'yyProfit5Rate', label: '运营财务毛五利润率(不减退款)', sortable: 'custom', width: '100', tipmesg: '运营财务毛五(不减退款)/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit5Rate ? " " : row.yyProfit5Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyProfit5After', label: '运营财务毛五(减退款)', sortable: 'custom', width: '90', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
  { istrue: true, summaryEvent: true, prop: 'yyProfit5AfterRate', label: '运营财务毛五利润率(减退款)', sortable: 'custom', width: '100', tipmesg: '运营财务毛五(减退款)/销售金额', permission: "DailyFinancialAuthority", formatter: (row) => !row.yyProfit5AfterRate ? " " : row.yyProfit5AfterRate + '%' },

  { istrue: true, summaryEvent: true, prop: 'profit4', label: '净利润（发生）', tipmesg: '毛三利润-公摊费-盘点损益', sortable: 'custom', width: '80', permission: "lirunprsi", formatter: (row) => !row.profit4 ? " " : row.profit4 },
  { istrue: true, summaryEvent: true, prop: 'profit4Rate', label: '净利率（发生）', tipmesg: '净利润/销售金额', sortable: 'custom', width: '80', permission: "lirunprsi", formatter: (row) => !row.profit4Rate ? " " : row.profit4Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'refundAmontBefore', label: '发货前退款（发生）', sortable: 'custom', width: '80' },
  { istrue: true, summaryEvent: true, prop: 'refundAmontBeforeRate', label: '发货前退款率（发生）', sortable: 'custom', tipmesg: '发货前退款/付款金额', width: '90', formatter: (row) => !row.refundAmontBeforeRate ? " " : (row.refundAmontBeforeRate) + '%' },
  { istrue: true, summaryEvent: true, prop: 'refundAmontAfter', label: '发货后退款（发生）', sortable: 'custom', width: '90' },
  { istrue: true, summaryEvent: true, prop: 'refundAmontAfterRate', label: '发货后退款率（发生）', sortable: 'custom', tipmesg: '发货后退款/付款金额', width: '90', formatter: (row) => !row.refundAmontAfterRate ? " " : (row.refundAmontAfterRate) + '%' },
  { istrue: true, summaryEvent: true, prop: 'refundAmont', label: '总退款金额（发生）', sortable: 'custom', width: '90', tipmesg: '当日发生的总退款金额，包括历史订单', formatter: (row) => !row.refundAmont ? " " : row.refundAmont },
  { istrue: true, summaryEvent: true, prop: 'packageAvgFee', label: '包装均价（发生）', sortable: 'custom', width: '80', formatter: (row) => !row.packageAvgFee ? " " : row.packageAvgFee },
  { istrue: true, summaryEvent: true, prop: 'freightAvgFee', label: '快递均价（发生）', sortable: 'custom', width: '80', formatter: (row) => !row.freightAvgFee ? " " : row.freightAvgFee },
  { istrue: true, summaryEvent: true, prop: 'exitCost', label: '出仓成本（发生）', sortable: 'custom', width: '80', formatter: (row) => !row.exitCost ? " " : row.exitCost, tipmesg: '真实出仓成本+预估出仓成本' },
  { istrue: true, summaryEvent: true, prop: 'exitCostAvg', label: '平均出仓成本（发生）', tipmesg: '出仓成本/订单量', sortable: 'custom', width: '90', formatter: (row) => !row.exitCostAvg ? " " : row.exitCostAvg },
  { istrue: true, summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '70', formatter: (row) => !row.warehouseSalary ? " " : row.warehouseSalary },
  { istrue: true, summaryEvent: true, prop: 'yyRefundAmontBefore', label: '发货前退款（运营）', sortable: 'custom', width: '90' },
  { istrue: true, summaryEvent: true, prop: 'yyRefundAmontBeforeRate', label: '发货前退款率（运营）', sortable: 'custom', tipmesg: '发货前退款/付款金额', width: '90', formatter: (row) => !row.yyRefundAmontBeforeRate ? " " : (row.yyRefundAmontBeforeRate) + '%' },
  { istrue: true, summaryEvent: true, prop: 'yyRefundAmontAfter', label: '发货后退款（运营）', sortable: 'custom', width: '90' },
  { istrue: true, summaryEvent: true, prop: 'yyRefundAmontAfterRate', label: '发货后退款率（运营）', sortable: 'custom', tipmesg: '发货后退款/付款金额', width: '90', formatter: (row) => !row.yyRefundAmontAfterRate ? " " : (row.yyRefundAmontAfterRate) + '%' },
];


const tableHandles = [
  { label: "导入胜算", handle: (that) => that.onstartImport() },
  /*   {label:"参数设置", handle:(that)=>that.onShowEditParm()}, */
  /*   {label:"导入", handle:(that)=>that.startImport()}, */
  { label: "导出", handle: (that) => that.onDerivationMethod() },
  { label: "每日快递", handle: (that) => that.showexpressfreightanalysis() },
  { label: "计算日报", handle: (that) => that.showCalDayRepoty() },
  { label: "刷新", handle: (that) => that.onRefresh() },
  /* {label:"模板-生意参谋平台", handle:(that)=>that.downloadTemplate()} */
];

export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, numberRange, MySearch, MySearchWindow, cesTable, productdrchart, InputMult, freightDetail, buschar, expressfreightanalysis, importmodule, ordergiftdetail, BusinessStaffPlatForm, InventoryCheckFee, EveryDayrefund, inputYunhan, vxetablebase , dailyConfirmation, dailyProfitrate ,dailyDataCheck, productCodedProfitIndex,ai2ProcodeView},
  data() {
    return {
      popularOptions,
      encoding: {
        visible: false,
        info: {},
      },
      ai2ProcodeModal: {
        visible: false,
        info: {},
      },
      rate: undefined,
      scaleDispositionVisible: false,
      topfilter: {
        maxProfit3Rate: null,//毛三利润率(发生)高于
        minProfit3Rate: null,//毛三利润率(发生)低于
        profit3Rate: null,//毛三利润率(发生)等于
        maxProfit33Rate: null,//毛四利润率(发生)高于
        minProfit33Rate: null,//毛四利润率(发生)低于
        profit33Rate: null,//毛四利润率(发生)等于
        maxyyProfit3AfterRate: null,//运营毛三利润率(减退款)高于
        minyyProfit3AfterRate: null,//运营毛三利润率(减退款)低于
        yyProfit3AfterRate: null,//运营毛三利润率(减退款)等于
        maxyyProfit4AfterRate: null,//运营毛四利润率(减退款)高于
        minyyProfit4AfterRate: null,//运营毛四利润率(减退款)低于
        yyProfit4AfterRate: null,//运营毛四利润率(减退款)等于
      },
      brandArr: [],
      colOptions: [],
      colSelect: [ '分类汇总', "商品ID", "商品名称",
        "系列编码", '最小上架天数',
        '最大上架天数', '店铺', '品牌',
        '采购', '运营组', '运营专员',
        '运营助理', '车手', '备用负责人',
        '毛利2', '毛利3', '毛利4', '毛利6',
        '净利润',
        '星星', '旗帜', '订单量查询', '是否预包',
        '毛六7日趋势', '连续日查询', '是否货损', '毛6连续负利润', '毛6总计负利润', '达标', '星级', '出仓利润', '超链接',
        '趋势筛选', '经营大类', '一级类目', '二级类目', '属性', '季节/节日', '天气', '温度', '白名单',
        'ID/店铺', '项目','爆品','爆品申报日期', '备注', '毛三（减退款）是否负利润', '毛四（减退款）是否负利润','毛五（减退款）是否负利润','毛六（减退款）是否负利润', '毛三（减退款率)', '毛四（减退款率)','毛五（减退款率)','毛六（减退款率)',
        '毛三利润', '总广告费', '毛三利润率(发生)', '毛四利润率(发生)', '毛五利润率(发生)', '毛六利润率(发生)', '运营毛三利润率(减退款)', '运营毛四利润率(减退款)', '运营毛五利润率(减退款)', '运营毛六利润率(减退款)','外仓率', '店铺数据是否确认',
      ],
      storeid: 'taogonchangDailyPaper202408271310',
      selectedTitles: [],
      dailyNewspaperToolbar: false,
      remarkData: [],
      dailyKanban: true,
      projectList: [],
      dailyPaperList:{
        startTime: null,
        endTime: null,
        dailyReportType: null,
        platform: null,
      },
      verifyDailyPaper: false,
      hearlist,
      seasonList,//季节数据
      temperatureList,//温度数据
      statsList,//属性数据
      weatherList,//天气数据
      dialogConfirmdata: false,
      dialogConfirmdata2: false,
      confirmDate: '',
      confirmDate2: '',
      that: this,
      filter: {
        isConfirmShop: true,//店铺数据是否确认
        shopCodeList: [],
        brandShopList: [],
        projName: [],
        remark: null,
        yyProfit3Lose: null,//毛三连续负利润
        yyProfit33Lose: null,//毛三总计负利润
        yyProfit5Lose: null,//毛五连续负利润
        yyProfit6Lose: null,//毛六总计负利润
        yyProfit3RateMax: undefined,//毛三利润率
        yyProfit3RateMin: undefined,//毛三利润率
        yyProfit33RateMax: undefined,//毛四利润率
        yyProfit33RateMin: undefined,//毛四利润率
        yyProfit5RateMin: undefined,//毛五利润率
        yyProfit5RateMax: undefined,//毛五利润率
        yyProfit6RateMin: undefined,//毛六利润率
        yyProfit6RateMax: undefined,//毛六利润率
        wcOrderCountRateMin: undefined,//外仓率低于
        wcOrderCountRateMax: undefined,//外仓率高于
        isIgnoreRptProCode: 0,
        isIgnoreSpecialProCode: null,
        attributeTag: [],//属性
        seasonOrFestivalTag: [],//季节
        temperatureTag: [],//温度
        weatherTag: [],//天气
        exitProfitUnZero: null,
        refundType: 4,
        reportType: 0,
        platform: 8,
        shopCode: null,
        proCode: null,
        styleCode: null,
        productName: null,
        brandId: null,
        groupId: null,
        startTime: null,
        endTime: null,
        timerange: null,
        // 运营助理
        userId: null,
        // 车手
        userId2: null,
        // 备用
        userId3: null,
        // 运营专员 ID
        operateSpecialUserId: null,
        profit2UnZero: null,
        profit3UnZero: null,
        profit33UnZero: null,
        bzCategory: null,
        bzCategory1: null,
        bzCategory2: null,
        profit4UnZero: null,
        profit6UnZero: null,
        groupType: 0,
        noProfitDay: null,
        bfNdaysProfit3LessThen0: null,
        userIds: [],//运营助理
        operateSpecialUserIds: [],//运营专员
        groupIds: [],//小组
        SyncTimeMax: null,
        baoPinList: [],
        bpstartTime: null,
        bpendTime: null,
        bptimerange: null,
        // minProfit3:null,
        // maxProfit3:null,
        // minProfit3Rate:null,
        // maxProfit3Rate:null
      },
      filterList: {
        bussinessCategoryNames: [],
        categoryName1s: [],
        categoryName2s: []
      },
      onimportfilter: {
        yearmonthday: null,
      },
      dialogCalDayRepotVis: false,
      calDayRepotyDate: null,
      styleCode: null,
      options: [],
      shopList: [],
      userList: [],
      brandlist: [],
      grouplist: [],
      directorlist: [],
      financialreportlist: [],
      tableCols: tableCols,
      yytableCols: yytableCols,
      tableHandles: tableHandles,
      tableCols2,
      total: 0,
      pager: { OrderBy: " saleAmont ", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      searchloading: false,
      summaryarry: {},
      selids: [],
      fileList: [],
      dialogVisibleData: false,
      dialogVisible: false,
      uploadLoading: false,
      importFilte: {},
      fileList: [],
      fileparm: {},
      earchloading: false,
      editparmVisible: false,
      editLoading: false,
      editparmLoading: false,
      drawervisible: false,
      /* dialogDrVisibleShengYi:false, */
      dialogDrVisible: false,
      expressfreightanalysisVisible: false,
      dialogCheckDayReportItem:false,
      drparamProCode: '',
      autoformparm: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
        rule: []
      },
      freightDetail: {
        visible: false,
        filter: {
          proCode: null,
          timeRange: []
        }
      },
      InventoryCheckFee: {
        visible: false,
        filter: {
          proCode: null,
        }
      },
      EveryDayrefund: {
        visible: false,
        filter: {
          proCode: null,
          timeRange: [],
          afterSaleType: "2",
          orderStatus: "已取消",
          goodsStatus: "",
          timeRange1: [],
          platform: 8
        }
      },
      SettingsDamageLinkDialog: {
        visible: false,
        filter: {
          proCode: null,
          platform: null
        }
      },
      giftDetail: { visible: false },
      costDialog: { visible: false, rows: [] },
      buscharDialog: { visible: false, title: "", data: {}, loading: false },
      drawervisible: false,
      noConfirmShopCount:0,
      styleTaglist: []
    };
  },
  async mounted() {
    this.onverifyMethod()
    this.oninitializeEcho();
    const {data:data1,success:success1} = await getProductProjectList({projName:''});
    if(success1){
      this.projectList = data1;
    }
  },
  async created() {
    await this.init()
    await this.getShopList();
    await this.initformparm();
    if (this.$route.query && this.$route.query.dayCount) {

      this.filter.noProfitDay = parseInt(this.$route.query.dayCount);
      this.filter.shopCode = this.$route.query.shopCode;
      this.filter.groupId = this.$route.query.groupId;
      this.filter.operateSpecialUserId = this.$route.query.operateSpecialUserId;
      let dateStr = this.$route.query.yearMonthDay.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
      this.filter.timerange = [dateStr, dateStr];
      this.filter.refundType = 1;
      this.onSearch();
    }
  },
  methods: {
    async onAI2ProcodeShow(row) {
      const proCode = row.proCode;
      if (!proCode || typeof proCode !== 'string') {
        this.$message.error('商品ID无效，请检查数据。');
        return;
      }
      const firstFour = proCode.slice(0, 4);
      const isInvalid = firstFour.split('').every(c => c === '0');
      if (isInvalid) {
        this.$message.error('无效商品ID，AI无法获取分析。');
        return;
      }
      let request = {
        // startDate: this.filter.timerange[0],
        // endDate: this.filter.timerange[1],
        proCode: row.proCode,
        yearMonthDay: row.yearMonthDay,
        //plat: 'qpt'
      };
      this.ai2ProcodeModal.info = { row, request };
      this.ai2ProcodeModal.visible = true;
    },
      onDispositionSave() {
        if (this.rate === undefined || this.rate === '' || this.rate === null) {
          this.$message.error('请输入比例')
          return
        }
        this.$confirm('确认保存吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const { success } = await editSignUpRate({ rate: this.rate ,platform:8})
          if (success) {
            this.$message({ type: 'success', message: '保存成功!' });
            this.scaleDispositionVisible = false;
          }
        }).catch(() => {
        });
      },
      async onScaleSetting() {
        this.rate = undefined
        const { data, success } = await getSignUpRate({ platform:8})
        if (success) {
          this.rate = data?.rate;
        }
        this.scaleDispositionVisible = true
      },
      async cellStyle(row, column, callback) {
      if(this.dailyKanban){
        if (column.field === 'profit5' && row.profit5 < 0) {
          callback({ color: '#12993a' });
        } else if (column.field === 'profit5Rate' && row.profit5Rate < 0) {
          callback({ color: '#12993a' });
        } else if (column.field === 'yyProfit5' && row.yyProfit5 < 0) {
          callback({ color: '#12993a' });
        } else if (column.field === 'yyProfit5Rate' && row.yyProfit5Rate < 0) {
          callback({ color: '#12993a' });
        } else if (column.field === 'yyProfit5After' && row.yyProfit5After < 0) {
          callback({ color: '#12993a' });
        } else if (column.field === 'yyProfit5AfterRate' && row.yyProfit5AfterRate < 0) {
        callback({ color: '#12993a' });
        } else if (column.field === 'profit6' && row.profit6 < 0) {
          callback({ color: '#12993a' });
        } else if (column.field === 'profit6Rate' && row.profit6Rate < 0) {
          callback({ color: '#12993a' });
        } else if (column.field === 'yyProfit6' && row.yyProfit6 < 0) {
          callback({ color: '#12993a' });
        } else if (column.field === 'yyProfit6Rate' && row.yyProfit6Rate < 0) {
          callback({ color: '#12993a' });
        } else if (column.field === 'yyProfit6After' && row.yyProfit6After < 0) {
          callback({ color: '#12993a' });
        } else if (column.field === 'yyProfit6AfterRate' && row.yyProfit6AfterRate < 0) {
          callback({ color: '#12993a' });
        }
      } else if (this.filter.refundType <3){
        if (column.field === 'profit3' && row.wcOrderCountRate >= 80){
          callback({ color: '#FF0000' });
        } else if (column.field === 'profit3Rate' && row.wcOrderCountRate >= 80){
          callback({ color: '#FF0000' });
        }
      } else if(this.filter.refundType == 3){
        if (column.field === 'yyProfit3' && row.wcOrderCountRate >= 80){
          callback({ color: '#FF0000' });
        } else if (column.field === 'yyProfit3Rate' && row.wcOrderCountRate >= 80){
          callback({ color: '#FF0000' });
        }
      }
      },
        //初始化
        selectAll() {
      if (this.colOptions.length != this.colSelect.length) {
        this.colOptions = this.colSelect.map(i => i);
      } else {
        this.colOptions = [];
      }
    },
    //数据初始化回显
    async oninitializeEcho() {
      const { data, success } = await GetVxeTableColumnCacheAsync({ tableId: this.storeid });
      if (success) {
        let storeData = data ? JSON.parse(data) : [];
        this.colOptions = this.colSelect.filter(i => storeData.includes(i));
      } else {
        this.colOptions = [];
      }
      this.selectedTitles = this.colOptions
      this.profitRate()
    },
    profitRate(){
      this.$nextTick(() => {
        this.$refs.refdailyProfitrate.onRevealingMethod(this.selectedTitles);
      });
    },
    //点击设置
    clickToolbar() {
      this.oninitializeEcho();
      this.dailyNewspaperToolbar = true;
    },
    //全选
    changeOptions(valArr) {
      this.colOptions = valArr;
    },
    //点击确定
    async verifyOptions() {
      await SetVxeTableColumnCacheAsync({ tableId: this.storeid, ColumnConfig: JSON.stringify(this.colOptions) });
      var arr = this.colSelect.filter(i => this.colOptions.indexOf(i) < 0); // 未选中的
      this.selectedTitles = this.colSelect.filter(i => {
        if (i !== '日期') {
          return !arr.includes(i)
        }
      });
      this.profitRate()
      this.dailyNewspaperToolbar = false;
    },
    async onverifyMethod(){
      this.onConditionsMethod()
      const {data,success} = await getPageDailyReportItemConfirmList({...this.dailyPaperList,currentPage:1,pageSize:50})
      if(!success) return
      data.forEach(item => {
        if(item.dailyReportItem == "终版新版日报" && item.clacConfirmZt != 1){
          this.verifyDailyPaper = true
        }
      });
      var noconfirdata = {  platform: this.dailyPaperList.platform,

        dailyReportDate:this.dailyPaperList.endTime
      }
      const res = await getNoConfirmShopNames(noconfirdata)
      if(!res.success) return

      if(res.data)
      this.noConfirmShopCount = res.data.split(',').length
      return
      const {data:data1,success:success1} = await getProductRemarkList({remark:''});
      if(success1){
        this.remarkData = data1;
      }
    },
    onConditionsMethod(){
      if (this.filter.timerange) {
        this.dailyPaperList.startTime = this.filter.timerange[0];
        this.dailyPaperList.endTime = this.filter.timerange[1];
      }
      this.dailyPaperList.platform = this.filter.platform;
      this.dailyPaperList.dailyReportType = '淘工厂日报'
    },
    onConfirmationMethod(){
      this.onConditionsMethod()
      this.dialogConfirmdata = true
    },
    onCheckDayReportItem(){
      this.onConditionsMethod()
      this.dialogCheckDayReportItem= true
    },
    onDerivationMethod(){
      let that = this
      if(that.filter.refundType < 3){
        that.$refs.table.setExportCols()
      } else if(that.filter.refundType == 3){
        that.$refs.table2.setExportCols()
      } else if(that.filter.refundType == 4){
        that.$refs.table3.setExportCols()
      }
    },
    remoteMethodBusinessCategory(query) {
      setTimeout(async () => {
        const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 1, categoryName: query })
        this.filterList.bussinessCategoryNames = res.data
      }, 200)
    },
    remoteMethodCategoryName1s(query) {
      setTimeout(async () => {
        const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 2, categoryName: query })
        this.filterList.categoryName1s = res.data
      }, 200)
    },
    remoteMethodCategoryName2s(query) {
      setTimeout(async () => {
        const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 3, categoryName: query })
        this.filterList.categoryName2s = res.data
      }, 200)
    },
    SettingsDamageLink(row) {
      this.SettingsDamageLinkDialog.visible = true;
      this.SettingsDamageLinkDialog.filter.proCode = row.proCode;
      this.SettingsDamageLinkDialog.filter.platform = row.platform;

    },
    async onSettingsDamageLink() {
      this.editparmLoading = true;
      await this.autoformparm.fApi.validate(async (valid, fail) => {
        if (valid) {
          const formData = this.autoformparm.fApi.formData();
          formData.platform = this.SettingsDamageLinkDialog.filter.platform;
          formData.proCode = this.SettingsDamageLinkDialog.filter.proCode;
          formData.isDayReport = true;
          const res = await updateDamageLink(formData);
          if (res.code == 1) this.SettingsDamageLinkDialog.visible = false;
        } else {
          this.editparmLoading = false;
          await this.autoform.fApi.resetFields()
        }
      })
      this.editparmLoading = false;
      await this.autoform.fApi.resetFields()
    },
    datepickerfuc(e) {
      console.log("33333", e)
      middlevue.$emit('timerangeGC', e);
    },
    refundTypefuc(e) {
      localStorage.setItem("refundType", e)
      middlevue.$emit('refundType', e);
      console.log("refundType666666", e)
      this.filter.refundType = e;
      this.pager.OrderBy = ''
      this.pager.IsAsc = false
      this.financialreportlist = []
      this.summaryarry = {}
      this.total = 0
      this.$nextTick(() => {
        if(e == 4 ){
          this.dailyKanban = true
        }else{
          this.dailyKanban = false
        }
        this.$forceUpdate()
      })
    },
    cellClick(prms) {
      if (prms?.column?.field && prms?.column?.field === 'profit3IncreaseGoOnDays') {
        let row = prms.row;
        this.showprchart2(row.proCode, row.platform, row.styleCode);
      }

    },
    async confirmData() {
      if (!this.confirmDate) {
        this.$message({ type: 'warning', message: '请选择日期!' });
        return;
      }
      let par = {
        dailyReportDate: this.confirmDate,
        dailyReportType: '淘工厂日报',
      };
      let confirmtitle = '【' + this.confirmDate + '】' + par.dailyReportType + '数据，确定确认？';
      this.$confirm(confirmtitle)
        .then(async _ => {
          let res = await insertDailyReportConfirmList(par);
          if (res.data) {
            this.$message({ type: 'success', message: '保存成功!' });
          }
          this.dialogConfirmdata = false;
        })
        .catch(_ => { });
    },
    async confirmData2() {
      if (!this.confirmDate2) {
        this.$message({ type: 'warning', message: '请选择日期!' });
        return;
      }
      let par = {
        dailyReportDate: this.confirmDate2,
        dailyReportType: '淘工厂违规扣款',
      };
      let confirmtitle = '【' + this.confirmDate2 + '】' + par.dailyReportType + '数据，确定确认？';
      this.$confirm(confirmtitle)
        .then(async _ => {
          let res = await insertDailyReportConfirmList(par);
          if (res.data) {
            this.$message({ type: 'success', message: '保存成功!' });
          }
          this.dialogConfirmdata2 = false;
        })
        .catch(_ => { });
    },
    getDaysMax(dataArr) {
      let max = dataArr[0];
      for (let i = 1; i < dataArr.length; i++) {
        if (Date.parse(dataArr[i]) > Date.parse(max)) {
          max = dataArr[i]
        }
      }
      return max
    },
    showEveryDayrefund(row) {
      this.EveryDayrefund.filter.proCode = row.proCode;
      if (row.yearMonthDay != null) {
        var dayStr = row.yearMonthDay.substr(0, 4) + '-' + row.yearMonthDay.substr(4, 2) + '-' + row.yearMonthDay.substr(6, 2)
        this.EveryDayrefund.filter.timeRange = [dayStr, dayStr];
      }
      else {
        this.EveryDayrefund.filter.timeRange = this.filter.timerange
      }
      this.EveryDayrefund.visible = true;
      setTimeout(async () => {
        await this.$refs.EveryDayrefund.onSearch();
      }, 100);

    },
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init() {
      var date1 = new Date(); date1.setDate(date1.getDate() - 1);
      var date2 = new Date(); date2.setDate(date2.getDate() - 1);
      this.filter.timerange = [];
      this.filter.timerange[0] = this.datetostr(date1);
      this.filter.timerange[1] = this.datetostr(date2);
    },
    async initformparm() {
      let that = this;
      this.autoformparm.rule = [
        { type: 'select', field: 'IsDamageLink', title: '是否货损链接', options: [{ value: true, label: '是', }, { value: false, label: '否', }], props: { clearable: true }, validate: [{ type: 'boolean', required: true, message: '请设置货损链接' }] },
        { type: 'DatePicker', field: 'IsDamageLinkTime', title: '货损截止日期', value: '', validate: [{ type: 'string', required: true, message: '请输入货损链接截止日期' }], props: { type: 'date', format: 'yyyy-MM-dd', placeholder: '货损链接截止日期' }, col: { span: 8 } },
      ]
    },
    //系列编码远程搜索
    async remoteMethod(query) {
      if (query !== '') {
        this.searchloading == true;
        this.options = [];
        setTimeout(async () => {
          const res = await getListByStyleCode({ currentPage: 1, pageSize: 50, styleCode: query })
          this.searchloading = false
          res?.data?.forEach(f => {
            this.options.push({ value: f.styleCode, label: f.styleCode })
          });
        }, 200)
      }
      else {
        this.options = []
      }
    },
    async getShopList() {
      const res1 = await getAllShopList({ platforms: [8] });
      this.shopList = [];
      res1.data?.forEach(f => {
        if (f.isCalcSettlement && f.shopCode)
          this.shopList.push(f);
      });
      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });

      var res5 = await getProductStyleTagList();
      this.styleTaglist = res5.data.map(item => {
        return { value: item.styleTag, label: item.styleTag };
      });

      var res4 = await getAllProBrand();
      this.brandlist = res4.data.map(item => {
        return { value: item.key, label: item.value };
      });
      const { data:data1 } = await getBrandFeeList({ currentPage: 1, pageSize: 9999999, feeType: 1 })
      this.brandArr = data1.list.map(item => item.brandName)
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      if (column.prop == 'onTime') {
       // this.pager.IsAsc = !this.pager.IsAsc;
      }
      await this.onSearch();
    },
    onRefresh() {
      this.onSearch()
    },
    async onSearch() {
      this.listLoading = true;
      this.$refs.table.changecolumn_setTrue(["yearMonthDay"]);
      if (this.filter.groupType == 1 || this.filter.groupType == 2) {
        this.$refs.table.changecolumn(["yearMonthDay"]);
      }
      this.$refs.pager.setPage(1);


      this.listLoading = false;
      await this.getList().then(res => { });

      // loading.close();
    },
    async getList() {
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      this.filter.bpstartTime = null;
      this.filter.bpendTime = null;
      if (this.filter.bptimerange) {
        this.filter.bpstartTime = this.filter.bptimerange[0];
        this.filter.bpendTime = this.filter.bptimerange[1];
      }
      if (this.filter.minOnTimeNum && this.filter.maxOnTimeNum && this.filter.minOnTimeNum > this.filter.maxOnTimeNum) {
        return this.$message.error('最小上架天数不能大于最大上架天数');
      }
      if (this.filter.minOnTimeNum) {
        this.filter.listingEndTime = dayjs(this.filter.timerange[0]).subtract(this.filter.minOnTimeNum, 'day').format('YYYY-MM-DD');
      }
      if (this.filter.maxOnTimeNum) {
        this.filter.listingStartTime = dayjs(this.filter.timerange[1]).subtract(this.filter.maxOnTimeNum, 'day').format('YYYY-MM-DD');
      }

      this.filter.styleCode = this.styleCode.join()
      var that = this;
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter, ...this.topfilter };
      if(this.dailyKanban){
        params.refundType = 1
      }
      // this.listLoading = true;
      startLoading();
      const res = await pageProductDayReport(params).then(res => {
        loading.close();
        if (res?.data?.list && res?.data?.list.length > 0) {
          for (var i in res.data.list) {
            if (!res.data.list[i].freightFee) {
              res.data.list[i].freightFee = " ";
            }
            if (that.filter.refundType == 2) {
              res.data.list[i].RefundAmont = res.data.list[i].RefundAmontByPay;
              res.data.list[i].Profit3 = res.data.list[i].Profit3ByPay;
              res.data.list[i].Profit3Rate = res.data.list[i].Profit3RateByPay;
            }
          }
        }
        if (that.filter.refundType == 2) {
          res.data.summary.Profit3Rate_sum = res.data?.summary?.Profit3RateByPay_sum;
          res.data.summary.RefundAmontByPay = res.data?.summary?.RefundAmontByPay_sum;
          res.data.summary.Profit3ByPay = res.data?.summary?.Profit3ByPay_sum;
        }
        var arraySyncTime = [];
        res.data.list.forEach(element => {
          arraySyncTime.push(element.syncTime);
        });
        this.SyncTimeMax = this.getDaysMax(arraySyncTime)
        that.total = res.data?.total;
        that.financialreportlist = res.data?.list;
        const today = dayjs();
        const yesterday = today.subtract(1, 'day').format('YYYYMMDD');
        that.financialreportlist.forEach(item => {
          if (item.yearMonthDay === yesterday) {
            item.wcOrderCountRate = 0;
          }
        });
        if(that.filter.refundType < 3){
          that.$refs.table.loadRowEcharts();
        } else if(that.filter.refundType == 3){
          that.$refs.table2.loadRowEcharts();
        } else if(that.filter.refundType == 4){
          that.$refs.table3.loadRowEcharts();
        }
        that.summaryarry = res.data?.summary;
      });
    },
    showFreightDetail(row) {
      if (row.freightFee >= 1) {
        this.freightDetail.filter.proCode = row.proCode;
        if (row.yearMonthDay != null) {
          var dayStr = row.yearMonthDay.substr(0, 4) + '-' + row.yearMonthDay.substr(4, 2) + '-' + row.yearMonthDay.substr(6, 2)
          this.freightDetail.filter.timeRange = [dayStr, dayStr];
        }
        else {
          this.freightDetail.filter.timeRange = this.filter.timerange
        }
        this.freightDetail.visible = true;
        setTimeout(async () => {
          await this.$refs.freightDetail.onSearch();
        }, 100);
      }
    },
    showOrderDetail(row) {
      this.freightDetail.filter.proCode = row.proCode;
      if (row.yearMonthDay != null) {
        var dayStr = row.yearMonthDay.substr(0, 4) + '-' + row.yearMonthDay.substr(4, 2) + '-' + row.yearMonthDay.substr(6, 2)
        this.freightDetail.filter.timeRange = [dayStr, dayStr];
      }
      else {
        this.freightDetail.filter.timeRange = this.filter.timerange
      }
      this.freightDetail.visible = true;
      setTimeout(async () => {
        await this.$refs.freightDetail.onSearch();
      }, 100);
    },
    showInventoryCheckFee(row) {
      this.InventoryCheckFee.filter.proCode = row.proCode;
      if (row.yearMonthDay != null) {
        var dayStr = row.yearMonthDay.substr(0, 4) + '-' + row.yearMonthDay.substr(4, 2) + '-' + row.yearMonthDay.substr(6, 2)
        this.InventoryCheckFee.filter.timeRange = [dayStr, dayStr];
      }
      else {
        this.InventoryCheckFee.filter.timeRange = this.filter.timerange
      }

      this.InventoryCheckFee.visible = true;
      setTimeout(async () => {
        await this.$refs.InventoryCheckFee.onSearch();
      }, 100);

    },
    showProcodesimilarity(row) {
      if (row.styleCode != null) {
        this.$router.push({ path: '/order/procodesimilarity', query: { styleCode: row.styleCode } })
      }
    },
    async showGiftDetail(row) {
      var yearMonthDayStart = row.yearMonthDay
      var yearMonthDayEnd = row.yearMonthDay
      if (this.filter.groupType) {
        yearMonthDayStart = this.filter.timerange[0].replace("-", "").replace("-", "")
        yearMonthDayEnd = this.filter.timerange[1].replace("-", "").replace("-", "")
      }
      this.giftDetail.visible = true;
      let _th = this;
      await this.$nextTick(async () => { await _th.$refs.ordergiftdetail.onShow(yearMonthDayStart, yearMonthDayEnd, row.proCode); });
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    onRefresh() {
      this.onSearch()
    },
    async updateruleGroup(groupid) {
      if (!groupid)
        this.autoformparm.fApi.resetFields()
      else {
        const res = await getParm({ groupId: groupid })
        var arr = Object.keys(this.autoformparm.fApi);
        res.data.groupId = groupid;
        if (!res.data?.Profit3PredictRate) res.data.Profit3PredictRate = 0;
        if (!res.data?.ShareRate) res.data.ShareRate = 0;
        await this.autoformparm.fApi.setValue(res.data)
      }
    },
    async showprchart2(prcode, platform, styleCode) {
      window['lastseeprcodedrchart'] = prcode
      window['lastseeprcodedrchart1'] = platform
      window['lastseeprcodedrchart2'] = this.filter.refundType
      window['lastseeprcodedrchart3'] = this.filter.groupType
      window['lastseeprcodedrchart4'] = styleCode
      window['lastseeprcodedrchart5'] = undefined
      window['lastseeprcodedrchart6'] = null
      window['lastseeprcodedrchart7'] = undefined
      this.drparamProCode = prcode
      this.dialogDrVisible = true
      /* this.dialogDrVisibleShengYi=true */
    },
    async onstartImport() {
      this.dialogVisible = true;
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      if (!this.onimportfilter.yearmonthday) {
        this.$message({ type: 'warning', message: '请选择年月!' });
        return;
      }
      this.uploadLoading = true
      this.$refs.upload.submit();
    },
    async uploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("platform", 8);
      form.append("yearmonthday", this.onimportfilter.yearmonthday);
      var res = await importProductDayReport(form);
      if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
      else this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading = false
    },
    async onExport(opt) {
      this.filter.startTime = null;
      this.filter.endTime = null;
      this.filter.listingEndTime = null;
      this.filter.listingStartTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      this.filter.bpstartTime = null;
      this.filter.bpendTime = null;
      if (this.filter.bptimerange) {
        this.filter.bpstartTime = this.filter.bptimerange[0];
        this.filter.bpendTime = this.filter.bptimerange[1];
      }
      if (this.filter.minOnTimeNum && this.filter.maxOnTimeNum && this.filter.minOnTimeNum > this.filter.maxOnTimeNum) {
        return this.$message.error('最小上架天数不能大于最大上架天数');
      }
      if (this.filter.minOnTimeNum) {
        this.filter.listingEndTime = dayjs(this.filter.timerange[0]).subtract(this.filter.minOnTimeNum, 'day').format('YYYY-MM-DD');
      }
      if (this.filter.maxOnTimeNum) {
        this.filter.listingStartTime = dayjs(this.filter.timerange[1]).subtract(this.filter.maxOnTimeNum, 'day').format('YYYY-MM-DD');
      }
      this.filter.styleCode = this.styleCode.join()
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter,...this.topfilter , ...opt };
      if(this.dailyKanban){
        params.refundType = 1
      }
      var res = await exportProductDayReport(params);
      if (!res?.data) {
        return
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '淘工厂日报' + new Date().toLocaleString() + '_.xlsx')
      aLink.click()
    },
    /* async onShowEditParm(){
        this.editparmVisible = true
        const res = await getParm()
        var arr = Object.keys(this.autoformparm.fApi);
        if(arr.length >0)
           this.autoformparm.fApi.resetFields()
        await this.autoformparm.fApi.setValue(res.data)
      }, */
    async onSetEditParm() {
      this.editparmLoading = true;
      await this.autoformparm.fApi.validate(async (valid, fail) => {
        if (valid) {
          const formData = this.autoformparm.fApi.formData();
          const res = await setParm(formData);
          if (res.code == 1) this.editparmVisible = false;
        } else { }
      })
      this.editparmLoading = false;
    },
    async onsummaryClick(property) {
      let a = {}
      let YH_EXT_ExportColumns = []
      let YH_EXT_ExportCnColumns = []
      if(this.filter.refundType < 3){
        a = this.$refs.table.getColumnsInfo();
      } else if(this.filter.refundType == 3){
        a = this.$refs.table2.getColumnsInfo();
      } else if(this.filter.refundType == 4){
        a = this.$refs.table3.getColumnsInfo();
      }
      a?.fullColumn.forEach((item)=>{
        if(item.title && item.field){
          YH_EXT_ExportColumns.push(item.field)
          YH_EXT_ExportCnColumns.push(item.title)
        }
      })
      // this.$message({message:property,type:"warning"});
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };
      if(this.dailyKanban){
        params.refundType = 1
      }
      params.column = property;
      params.YH_EXT_ExportColumns = YH_EXT_ExportColumns
      params.YH_EXT_ExportCnColumns = YH_EXT_ExportCnColumns
      let that = this;
      that.listLoading = true;
      that.buscharDialog.loading = true;
      const res = await queryDayReportAnalysis(params).then(res => {
        that.buscharDialog.loading = false;
        that.buscharDialog.data = res.data
        that.buscharDialog.title = res.data.legend[0]
      });
      that.listLoading = false;
      that.buscharDialog.visible = true;
      this.$nextTick(async()=>{
        await that.$refs.buschar.initcharts();
      })
    },
    showCost(row) {
      if (row.replaceSendCost > 0) {
        this.costDialog.visible = true;
        this.costDialog.rows = [row];
      }
    },
    showexpressfreightanalysis() {
      this.expressfreightanalysisVisible = true;
      this.$nextTick(() => { this.$refs.expressfreightanalysis.onSearch() });
    },
    renderCost(row) {
      if (row.replaceSendCost > 0) {
        return "color:blue;cursor:pointer;";
      }
      else {
        return "";
      }
    },
    showupload() {
      this.drawervisible = true;
    },
    async callbackProCode(val) {
      this.filter.proCode = val;
    },
    async showCalDayRepoty() {
      this.dialogCalDayRepotVis = true;
    },
    async calDayRepoty() {
      if (this.calDayRepotyDate == null) {
        this.$message({ type: 'warning', message: '请先选择日期!' });
        return
      }
      let res = await calDayRepoty({ type: 'GC', yearMonthDay: this.calDayRepotyDate });
      if (!res?.success) return
      this.$message({ type: 'success', message: '正在计算中,请稍候...' });
      this.dialogCalDayRepotVis = false;
    },
    async onGoodsProfitShow(row, vala) {
      let request = {
        startDate: this.filter.timerange[0],
        endDate: this.filter.timerange[1],
        proCode: row.proCode,
        plat: 'tgc'
      };
      this.encoding.info = { row, request, vala };
      this.encoding.visible = true;
    },
  },

};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}

// .vertical-container {
//   position: relative;
//   width: 100%;
//   height: 40px; /* 设置父容器的高度以容纳三个 span */
// }

// .vertical-sublevel_1,
// .vertical-sublevel_2,
// .vertical-sublevel_3 {
//   position: absolute;
//   left: 50%; /* 水平居中 */
//   transform: translateX(-50%); /* 将元素的中心点移动到容器的中心 */
//   margin: 0;
//   padding: 0;
//   white-space: nowrap; /* 确保内容不会换行 */
// }

// .vertical-sublevel_1 {
//   top: -5px; /* 第一个元素在顶部 */
// }

// .vertical-sublevel_2 {
//   top: 44%; /* 第二个元素在中间 */
//   transform: translate(-50%, -50%); /* 进一步垂直居中 */
// }

// .vertical-sublevel_3 {
//   bottom: -2px; /* 第三个元素在底部 */
//   transform: translateX(-50%); /* 只水平居中，不需要垂直居中 */
// }

:deep(.vxe-table--render-default:not(.is--empty).is--footer.is--scroll-x .vxe-table--body-wrapper) {
	overflow-x: hidden !important;
}
</style>
