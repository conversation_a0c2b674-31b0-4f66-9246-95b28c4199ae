<template>
  <MyContainer>

    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;" :value-format="'yyyy-MM-dd'"
          @change="changeTime">
        </el-date-picker>
        <el-button style="padding: 0;margin: 0;border:none;width:100px;">
          <el-select v-model="ListInfo.appType" clearable placeholder="来源" filterable>
            <el-option v-for="(item, i) in appTypes" :label="item" :key="i" :value="item"
              style="width: 200px;"></el-option>
          </el-select>
        </el-button>

        <el-button style="padding: 0;margin: 0;border:none;width:100px;">
          <el-select v-model="ListInfo.moduleName" filterable clearable placeholder="模块">
            <el-option v-for="(item, i) in moduleNames" :label="item" :key="i" :value="item"
              style="width: 200px;"></el-option>
          </el-select>
        </el-button>

        <el-button style="padding: 0;margin: 0;border:none;width:100px;">
          <el-select v-model="ListInfo.pageName" filterable clearable placeholder="页面">
            <el-option v-for="(item, i) in pageNames" :label="item" :key="i" :value="item"
              style="width: 200px;"></el-option>
          </el-select>
        </el-button>

        <el-button style="padding: 0;margin: 0;border:none;width:100px;">
          <el-select v-model="ListInfo.pageTab" filterable clearable placeholder="页签">
            <el-option v-for="(item, i) in pageTabs" :label="item" :key="i" :value="item"
              style="width: 200px;"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin: 0;border:none;width:100px;">
          <!-- <el-select v-model="ListInfo.actionName" filterable clearable placeholder="行为">
            <el-option v-for="(item, i) in actionNames" :label="item" :key="i" :value="item"
              style="width: 300px;"></el-option>
          </el-select> -->
          <el-input v-model.trim="ListInfo.actionName" placeholder="行为" maxlength="50" clearable style="width: 100px;" />
        </el-button>
        <el-button style="padding: 0;margin: 0;border:none;width:100px;">
          <el-select v-model="ListInfo.dept1" clearable placeholder="区域" filterable>
            <el-option v-for="(item, i) in dept1s" :label="item" :key="i" :value="item"
              style="width: 200px;"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin: 0;border:none;width:100px;">
          <el-select v-model="ListInfo.dept2" clearable placeholder="部门1" filterable>
            <el-option v-for="(item, i) in dept2s" :label="item" :key="i" :value="item"
              style="width: 200px;"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin: 0;border:none;width:100px;">
          <el-select v-model="ListInfo.dept3" clearable placeholder="部门2" filterable>
            <el-option v-for="(item, i) in dept3s" :label="item" :key="i" :value="item"
              style="width: 200px;"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin: 0;border:none;width:100px;">
          <el-select v-model="ListInfo.dept4" clearable placeholder="部门3" filterable>
            <el-option v-for="(item, i) in dept4s" :label="item" :key="i" :value="item"
              style="width: 200px;"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin: 0;border:none;">
          <el-input v-model.trim="ListInfo.userName" clearable placeholder="用户" style="width:100px;" :maxlength="40">
          </el-input>
        </el-button>


        <el-button style="padding: 0;margin: 0;border:none;">
          <el-input v-model.trim="ListInfo.ProductManager" clearable placeholder="产品" style="width:100px;" :maxlength="40">
          </el-input>
        </el-button>
        <el-button style="padding: 0;margin: 0;border:none;">
          <el-input v-model.trim="ListInfo.Developer" clearable placeholder="开始" style="width:100px;" :maxlength="40">
          </el-input>
        </el-button>
        <el-button style="padding: 0;margin: 0;border:none;">
          <el-input v-model.trim="ListInfo.Tester" clearable placeholder="测试" style="width:100px;" :maxlength="40">
          </el-input>
        </el-button>

        <el-button style="padding: 0;margin: 0;border:none;width:100px;">
          <el-select v-model="ListInfo.XqDept1" clearable placeholder="需求一级部门">
              <el-option v-for="(item, i) in xqDept1s" :label="item" :key="i" :value="item"
              style="width: 200px;"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin: 0;border:none;width:100px;">
          <el-select v-model="ListInfo.XqDept2" clearable placeholder="需求二级部门">
            <el-option v-for="(item, i) in xqDept2s" :label="item" :key="i" :value="item"
              style="width: 200px;"></el-option>
          </el-select>
        </el-button>

        <el-button style="padding: 0;margin: 0;border:none;">
          <el-input v-model.trim="ListInfo.XqUserName" clearable placeholder="需求提出人" style="width:100px;" :maxlength="40">
          </el-input>
        </el-button>


        <el-button type="primary" @click="getList('search')">查询</el-button>
      </div>
    </template>



    <vxetablebase :id="'pageOperationLogl202408041350'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { pagePageReqLogList, GetPageReqLogFilters } from '@/api/admin/opration-log'
import dayjs from 'dayjs'
import { formatTime } from "@/utils/tools";

const tableCols = [
  { istrue: true, prop: 'reqTime', label: '日期', width: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'userName', label: '用户', width: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'appType', label: '来源', width: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'moduleName', label: '模块', width: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'pageName', label: '页面', width: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'pageTab', label: '页签', width: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'actionName', label: '行为', width: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'dept1', label: '区域公司', width: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'dept2', label: '一级部门', width: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'dept3', label: '二级部门', width: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'dept4', label: '三级部门', width: 'auto', sortable: 'custom', align: 'center' },

  { istrue: true, prop: 'reqCount', label: '次数', minwidth: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'userCount', label: '人数', minwidth: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'userAvg', label: '人均', minwidth: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'productManager', label: '产品', width: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'developer', label: '开发', width: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'tester', label: '测试', width: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'xqDept1', label: '需求一级部门', width: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'xqDept2', label: '需求二级部门', width: 'auto', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'xqUserName', label: '需求提出人', width: 'auto', sortable: 'custom', align: 'center' },

]
export default {
  name: "pageOperationLogl",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        reqTimeStart: null,//开始时间
        reqTimeEnd: null,//结束时间
        appType: null,//来源
        moduleName: null,//模块
        pageName: null,//页面
        pageTab: null,//页签
        actionName: null,//行为
        dept1: null,//区域公司
        dept2: null,//一级部门
        dept3: null,//二级部门
        dept4: null,//三级部门
        userName: null,//用户

        productManager: null,//产品
        developer: null,//开发
        tester:null,//测试
        xqDept1: null,//需求一级部门
        xqDept2: null,//需求二级部门
        xqUserName: null,//需求提出人
      },
      appTypes: [],
      moduleNames: [],
      pageNames: [],
      pageTabs: [],
      actionNames: [],
      dept1s: [],
      dept2s: [],
      dept3s: [],
      dept4s: [],

      xqDept1s: [],
      xqDept2s: [],

      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    let fds = await GetPageReqLogFilters();
    if (fds && fds.success) {
      this.appTypes = fds.data.appType;
      this.moduleNames = fds.data.moduleName;
      this.pageNames = fds.data.pageName;
      this.pageTabs = fds.data.pageTab;
      this.actionNames = fds.data.actionName;
      this.dept1s = fds.data.dept1;
      this.dept2s = fds.data.dept2;
      this.dept3s = fds.data.dept3;
      this.dept4s = fds.data.dept4;

      this.xqDept1s = fds.data.xqDept1;
      this.xqDept2s = fds.data.xqDept2;
    }
    await this.getList()
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.reqTimeStart = e ? e[0] : null
      this.ListInfo.reqTimeEnd = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges != null && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.reqTimeStart = dayjs().subtract(14, 'day').format('YYYY-MM-DD')
        this.ListInfo.reqTimeEnd = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.reqTimeStart, this.ListInfo.reqTimeEnd]
      }
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      const { data, success } = await pagePageReqLogList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}
</style>
