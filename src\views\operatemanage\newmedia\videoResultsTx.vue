<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template #header>
        <el-form  class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent ></el-form>
      </template>
      <!--列表-->
      <ces-table      ref="table"
        :that="that"
        :isIndex="true"
        :hasexpand="false"
        @sortchange="sortchange"
        :tableData="pddcontributeinfolist"
        @select="selectchange"
        :isSelection="false"
        :tableCols="tableCols"
        :loading="listLoading"
        :summaryarry='summaryarry'
      >
        <el-table-column type="expand">
          <template slot-scope="props">
            <div>
              <el-table :data="props.row.detaildata" style="width: 100%">
                <el-table-column
                  v-for="col in props.row.detailcols"
                  :prop="col.prop"
                  :label="col.label"
                  :key="col"
                >
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <template slot="extentbtn">
          <el-button-group>
            <el-button style="padding: 0; margin: 0">
              <el-input
                v-model.trim="Filter.Shopname"
                placeholder="店铺名"
                style="width: 120px"
                clearable
                maxlength="50"
              />
              <el-button style="padding: 0; margin: 0">
                <el-input
                  v-model.trim="Filter.Title"
                  placeholder="视频标题"
                  style="width: 120px"
                  clearable
                  maxlength="50"
                />
              </el-button>
              <el-button style="padding: 0; margin: 0">
                <el-input
                  v-model.trim="Filter.Procode"
                  placeholder="产品ID"
                  style="width: 120px"
                  clearable
                  maxlength="50"
                />
              </el-button>
            </el-button>
             <el-button style="padding: 0; margin: 0">
              <el-input
                v-model.trim="Filter.videoId"
                placeholder="视频ID"
                style="width: 120px"
                clearable
                maxlength="50"
              />
            </el-button>
            <el-button style="padding: 0; margin: 0">
              <el-select filterable v-model="Filter.version" placeholder="请选择流量来源" clearable>
                <el-option label="光合视频" value="光合视频"></el-option>
                <el-option label="逛逛视频" value="逛逛视频"></el-option>
                <el-option label="微详情视频" value="微详情视频"></el-option>
                       
             </el-select>
            </el-button>
            <el-button style="padding: 0; margin: 0">
              <datepickerTx v-model="Filter.UploadDate"></datepickerTx>
            </el-button>
            <el-button style="padding: 0; margin: 0">
              <el-input
                v-model.trim="Filter.Sname"
                placeholder="投稿人"
                style="width: 120px"
                clearable
                maxlength="50"
              />
            </el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onImportSyj">导入</el-button>
          </el-button-group>
        </template>
      </ces-table>
      <!--分页-->
      <template #footer>
        <my-pagination
          ref="pager"
          :total="total"
          :checked-count="sels.length"
          @get-page="getpddcontributeinfoList"
        />
      </template>
      <el-dialog
        title="淘系视频业绩"
        :visible.sync="dialogVisibleSyj"
        width="30%"
      >

      <el-row :gutter="20">
        <el-col :span="8">
        <el-select filterable v-model="importfilter.version" placeholder="请选择要导入的表" clearable>
            <el-option label="光合视频" value="1"></el-option>
            <el-option label="逛逛视频" value="2"></el-option>
            <el-option label="微详情视频" value="3"></el-option>
                   
         </el-select>
        </el-col>
        <el-col :span="8">
      <span>
        <el-upload
          ref="upload2"
          class="upload-demo"
          :auto-upload="false"
          :multiple="false"
          :limit="1"
          action
          accept=".xlsx"
          :http-request="uploadFile2"
          :on-success="uploadSuccess2"
        >
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <my-confirm-button
            style="margin-left: 10px"
            size="small"
            type="success"
            @click="onSubmitupload2"
            >上传</my-confirm-button
          >
        </el-upload>
    
      </span>
    </el-col>


      </el-row>
     
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisibleSyj = false">关闭</el-button>
        </span>
      </el-dialog>
    </my-container>
  </template>
  <script>
  import datepickerTx from "@/views/operatemanage/newmedia/datepickerTx";
  import {
    importNewMediaTaoXiPerformance,
    getNewMediaTaoXiPerformanceList as getPddcontributeinfoList,
    deletePddcontributeinfoBatch,
  } from "@/api/operatemanage/lookboard";
  import dayjs from "dayjs";
  import cesTable from "@/components/Table/table.vue";
  import { formatTime } from "@/utils";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  const tableCols = [
  {
      istrue: true,
      prop: "salesDate",
      label: "销售日期",
      width: "110",
      sortable: "custom",
    },
  {
      istrue: true,
      prop: "shopName",
      label: "店铺名称",
      width: "150",
      sortable: "custom",
    },
    {
      istrue: true,
      prop: "title",
      label: "视频标题",
      width: "200",
      sortable: "custom",
    },
    {
      istrue: true,
      prop: "proCode",
      label: "产品ID",
      width: "120",
      sortable: "custom",
    },
    {
      istrue: true,
      prop: "videoId",
      label: "视频ID",
      width: "120",
      sortable: "custom",
    },
    {
      istrue: true,
      prop: "channel",
      label: "流量来源",
      width: "100",
      sortable: "custom",
    },
  
    
    {
      istrue: true,
      prop: "salesuv",
      label: "种草成交人数",
      width: "150",
      sortable: "custom",
    },
    {
      istrue: true,
      prop: "amount",
      label: "种草成交金额",
      width: "150",
      sortable: "custom",
    },
   
   
    {
      istrue: true,
      prop: "clickTimes",
      label: "商品点击人数",
      width: "150",
      sortable: "custom",
    },
    {
      istrue: true,
      prop: "playCount",
      label: "有效播放次数",
      width: "150",
      sortable: "custom",
    },
    
    {
      istrue: true,
      prop: "videoReleaseDate",
      label: "投稿日期",
      width: "130",
      sortable: "custom",
    },
    {
      istrue: true,
      prop: "publishUserName",
      label: "投稿人",
      width: "110",
      sortable: "custom",
    },
    
  ];
  export default {
    name: "Users",
    components: {
      MyContainer,
      MyConfirmButton,
      MySearch,
      MySearchWindow,
      cesTable,
      datepickerTx,
    },
    data() {
      return {
        importfilter:{
          version:''
        },
        that: this,
        Filter: {},
        shopList: [],
        userList: [],
        groupList: [],
        pddcontributeinfolist: [],
        tableCols: tableCols,
        total: 0,
        summaryarry: {  },
        pager: { OrderBy: "id", IsAsc: false },
        sels: [], // 列表选中列
        listLoading: false,
        pageLoading: false,
        //
        selids: [],
        dialogVisibleSyj: false,
        fileList: [],
      };
    },
    async mounted() {},
    methods: {
      async deleteBatch(row) {
        var that = this;
        this.$confirm("此操作将删除此批次多多投稿记录数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          await deletePddcontributeinfoBatch({ batchNumber: row.batchNumber });
          that.$message({ message: "已删除", type: "success" });
          that.onRefresh();
        });
      },
      sortchange(column) {
        if (!column.order) this.pager = {};
        else
          this.pager = {
            OrderBy: column.prop,
            IsAsc: column.order.indexOf("descending") == -1 ? true : false,
          };
        this.onSearch();
      },
      onImportSyj() {
        this.dialogVisibleSyj = true;
      },
      async uploadFile2(item) {
       
        const form = new FormData();
        form.append("upfile", item.file);
        form.append("version", this.importfilter.version);
       
     

        const res = importNewMediaTaoXiPerformance(form);
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
        this.importfilter.version=''
      },
      async uploadSuccess2(response, file, fileList) {
        fileList.splice(fileList.indexOf(file), 1);
      },
      async onSubmitupload2() {
        
        if(this.importfilter.version=='')
        {
            this.$message.error('请选择要导入的表类型');
            this.importfilter.version='';
            return

        }
        this.$refs.upload2.submit();
      },
      onRefresh() {
        this.onSearch();
      },
      onSearch() {
        this.$refs.pager.setPage(1);
        this.getpddcontributeinfoList();
      },
      async getpddcontributeinfoList() {
        this.Filter.startUploadDate = null;
          this.Filter.endUploadDate = null;
        // if (this.Filter.UseDate) {
        //   this.Filter.startAccountDate = this.Filter.UseDate[0];
        //   this.Filter.endAccountDate = this.Filter.UseDate[1];
        // }
        if (this.Filter.UploadDate) {
          this.Filter.startUploadDate = this.Filter.UploadDate[0];
          this.Filter.endUploadDate = this.Filter.UploadDate[1];
        }
        const para = { ...this.Filter };
        var pager = this.$refs.pager.getPager();
        const params = {
          ...pager,
          ...this.pager,
          ...para,
        };
  
        console.log(para);
  
        this.listLoading = true;
        const res = await getPddcontributeinfoList(params);
        console.log(res);
        this.listLoading = false;
        console.log(res.data.list);
        //console.log(res.data.summary)
  
        this.total = res.data.total;
        this.pddcontributeinfolist = res.data.list;
        this.summaryarry=res.data.summary;
      },
      selectchange: function (rows, row) {
        this.selids = [];
        rows.forEach((f) => {
          this.selids.push(f.id);
        });
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
  