<template>
    <container v-loading="pageLoading">
        <vxetablebase ref="table" :id="'YunHanAdminpurchaseorder20230808'" :that='that' :isIndex='true' checkBoxReserve
            keyField="id" @sortchange='sortchange' @select='selectchange' @cellClick='cellclick' :hasexpand='true'
            :tableData='list' :tableCols='tableCols' :border='true'
            :checkbox-config="{ labelField: 'id', highlight: true, range: true }" @checkbox-range-end="callback"
            :tableHandles='tableHandles' :loading="listLoading" :height="'94%'">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="margin: 0;">
                        {{ lastUpdateTime }}
                    </el-button>

                    <!-- <el-button style="padding: 0;margin: 0;">

                        <el-date-picker style="width: 260px" v-model="filter.timerangerk" type="datetimerange"
                            format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至"
                            start-placeholder="入库开始日期" end-placeholder="入库结束日期"></el-date-picker>
                    </el-button> -->
                    <el-button style="padding: 0;margin-left: 0;">
                        <el-tooltip class="item" effect="dark" content="注：最多只能复制1000条" placement="top-start">
                            <el-button type="success" v-throttle="3000" @click="doCopy">复制采购单号</el-button>
                        </el-tooltip>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;"
                        v-if="checkPermission(['api:Inventory:PurchaseOrderNew:TaskPurchaseDingDingAsync'])">
                        <el-button type="success" v-throttle="3000" @click="ontaskPurchaseUpload(1)">新品流程</el-button>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;"
                        v-if="checkPermission(['api:Inventory:PurchaseOrderNew:TaskPurchaseDingDingAsync'])">
                        <el-button type="success" v-throttle="3000" @click="ontaskPurchaseUpload(2)">月账单流程</el-button>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;"
                        v-if="checkPermission(['api:Inventory:PurchaseOrderNew:TaskPurchaseDingDingAsync'])">
                        <el-button type="success" v-throttle="3000" @click="ontaskPurchaseUpload(5)">加工入库</el-button>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;"
                        v-if="checkPermission('DingDingApproval_PurchaseOrderCross')">
                        <el-button type="success" v-throttle="3000" @click="ontaskPurchaseUpload(4)">跨境流程</el-button>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;" v-if="checkPermission('ontaskPurchaseUploadtest')">
                        <el-button type="success" v-throttle="3000" @click="ontaskPurchaseUpload(3)">测试流程</el-button>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-button type="primary" v-throttle="3000" @click="onSearch">查询</el-button>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-tooltip class="item" effect="dark" content="注：勾选有采购单号的以进行同步。请在聚水潭与ERP不一致时才使用。"
                            placement="top-start">
                            <el-button type="warning" v-throttle="3000" @click="onSyncBuyNos">同步最新聚水潭采购单</el-button>
                        </el-tooltip>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-tooltip class="item" effect="dark" content="注：勾选采购单。请在钉钉状态与ERP不一致时才使用。"
                            placement="top-start">
                            <el-button type="warning" v-throttle="3000"
                                @click="onReCheckOrderStatus">同步钉钉采购单状态</el-button>
                        </el-tooltip>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-tooltip class="item" effect="dark" content="注：采购单审批中，但在钉钉未找到流程，使用此按钮。"
                            placement="top-start">
                            <el-button type="warning" v-throttle="3000"
                                @click="OnTerminateNoApplyPurchaseOrder">修正采购单未审核状态</el-button>
                        </el-tooltip>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-button type="primary" v-throttle="3000" @click="openWmsAddress"
                            v-if="checkPermission('wareHouseAddress')">仓库地址</el-button>
                    </el-button>

                </el-button-group>
            </template>
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%">
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action accept=".xlsx"
                    :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <!-- 编辑手动采购单 -->
        <vxe-modal v-model="dialogAddVisible" :title="addEditTitle" :width="1500" :height="872" :loading="dialogAdding"
            resize @close="closeDialog" v-loading="editOrderVisible">
            <template #default>
                <el-form ref="addForm" :model="addForm" :rules="addFormRules" label-width="110px">
                    <el-row :hidden="true">
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="id" label="id">
                                <el-input v-model="addForm.id" auto-complete="off" :disabled="true" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="indexNo" label="序号">
                                <el-input v-model="addForm.indexNo" auto-complete="off" :disabled="true"
                                    style="width:88%" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
                            <el-form-item prop="warehouse" label="仓库">
                                <el-select v-model="addForm.warehouse" ref="warehouse" placeholder="仓库" filterable
                                    @change="changeValid" style="width:79%">
                                    <el-option v-for="item in warehouseList" :key="item.name" :label="item.name"
                                        :value="item.wms_co_id" />
                                </el-select>
                                <el-link type="primary" @click="doCopy4Val(addForm.indexNo)">复制ERP单号:{{ addForm.indexNo
                                    }} </el-link>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
                            <el-form-item prop="supplier_id" label="供应商">
                                <el-select v-model="addForm.supplier_id" ref="supplier_id" filterable remote
                                    reserve-keyword placeholder="请输入供应商" :remote-method="remoteSearchSupplier"
                                    @change="changePayType($event, 1)" :loading="supplierLoading" style="width:79%">
                                    <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                                <el-link type="primary" @click="doCopy4Val(addForm.supplier)">复制供应商</el-link>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8" style="display: flex; flex-direction: row;">
                            <span style="color: red; line-height: 30px;">*</span>
                            <el-form-item prop="provinceCityDistrict" label="发货地" class="my-el-cascader"
                                label-width="55px">
                                <el-cascader ref="mycascader" placeholder="发货地" :options="options1"
                                    :props="{ multiple: false, checkStrictly: true }"
                                    v-model="addForm.provinceCityDistrict" filterable></el-cascader>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-if="aliOrderInfo.IsShow">
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item>
                                <el-link type="primary"
                                    @click="addForm.orderNo = aliOrderInfo.AliOrderNo; addForm.payAccount = aliOrderInfo.CgAccountName; aliOrderInfo.IsShow = false; aliOrderInfo.IndexNo = '';">
                                    采购单[{{ aliOrderInfo.IndexNo }}]下单成功[{{ aliOrderInfo.CgAccountName }},{{
                                    aliOrderInfo.AliOrderNo }}]，点击我将自动填充到下方。
                                </el-link>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="5" :sm="5" :md="5" :lg="5" :xl="5" :span="5">
                            <el-form-item prop="payAccount" label="账号">
                                <el-autocomplete class="inline-input" v-model.trim="addForm.payAccount"
                                    :fetch-suggestions="querySearch" placeholder="请输入内容" @select="handleSelect"
                                    style="width: 100%" clearable></el-autocomplete>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="5" :sm="5" :md="5" :lg="5" :xl="5" :span="5">
                            <el-form-item prop="orderNo" label="订单编号">
                                <el-input v-model.trim="addForm.orderNo" ref="supplier_id" placeholder="请输入内容"
                                    :maxlength="50" @input="changetextarea($event)"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="5" :sm="5" :md="5" :lg="5" :xl="5" :span="5">
                            <el-form-item prop="startDept" label="发起部门">
                                <el-select v-model="addForm.startDept" ref="startDept" placeholder="发起部门"
                                    style="width:100%;">
                                    <el-option label="采购正常进货" value="采购正常进货" />
                                    <el-option label="运营给量进货" value="运营给量进货" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="5" :sm="5" :md="5" :lg="5" :xl="5" :span="5">
                            <el-form-item prop="groupId" label="审批运营组:">
                                <el-select v-model="addForm.groupId" clearable filterable placeholder="请选择审批运营组"
                                    style="width: 100%">
                                    <el-option v-for="item in grouplist" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="4" :sm="4" :md="4" :lg="4" :xl="4" :span="4">
                            <el-form-item prop="isUrgent" label="是否加急">
                                <el-select v-model="addForm.isUrgent" clearable filterable placeholder="请选择是否加急"
                                    style="width: 100%">
                                    <el-option label="正常" :value="0" />
                                    <el-option label="加急" :value="1" />
                                    <el-option label="人工审单" :value="2" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="5" :sm="5" :md="5" :lg="5" :xl="5" :span="5">
                            <el-form-item prop="payWay" label="支付方式">
                                <el-select v-model="addForm.payWay" ref="payWay" placeholder="支付方式" style="width:100%;"
                                    @change="changePayway">
                                    <el-option label="阿里巴巴" value="阿里巴巴" />
                                    <el-option label="银行卡" value="银行卡" />
                                    <el-option label="支付宝" value="支付宝" />
                                    <el-option label="微信" value="微信" />
                                    <el-option label="现金" value="现金" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="5" :sm="5" :md="5" :lg="5" :xl="5" :span="5">
                            <el-form-item prop="payType" label="对公/对私">
                                <el-select v-model="addForm.payType" @change="changePayType($event, 2)" clearable
                                    filterable :disabled="addForm.payWay == '阿里巴巴' && addForm.payment == '包邮'"
                                    placeholder="请选择对公/对私" style="width:100%;">
                                    <el-option label="对公" value="对公" />
                                    <el-option label="对私" value="对私" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="5" :sm="5" :md="5" :lg="5" :xl="5" :span="5">
                            <el-form-item prop="payment" label="邮费方式">
                                <el-select v-model="addForm.payment" clearable filterable placeholder="请选择邮费方式"
                                    style="width: 100%">
                                    <el-option label="包邮" value="包邮" />
                                    <el-option label="寄付" value="寄付" />
                                    <el-option label="仓库到付" value="仓库到付" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="9" :sm="9" :md="9" :lg="9" :xl="9" :span="9">
                            <el-form-item prop="payReMark" label="钉钉备注">
                                <el-input v-model="addForm.payReMark" type="textarea" :rows="2" ref="supplier_id" resize="none"
                                    placeholder="仅限输入最多500字" :maxlength="500"
                                    @input="changetextarea($event)"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-if="dialogAddVisible">
                        <el-col :span="5">
                            <el-form-item prop="isProxyShipping" label="是否能代发" :rules="[
                                { required: true, message: '请选择是否能代发', trigger: 'blur', },
                                {
                                    validator: (rule, value, callback) => {
                                        if (value === null || value === '' || value === undefined) {
                                            callback(new Error('请选择是否能代发'));
                                        } else {
                                            callback();
                                        }
                                    }, trigger: 'blur'
                                }
                            ]">
                                <el-select v-model="addForm.isProxyShipping" @change="() => {
                                    !addForm.isProxyShipping ? addForm.printOrderPlatform = '' : ''
                                }" clearable filterable placeholder="请选择是否能代发" style="width: 100%">
                                    <el-option label="是" :value="true" />
                                    <el-option label="否" :value="false" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" v-if="addForm.isProxyShipping == true && dialogAddVisible">
                            <el-form-item prop="printOrderPlatform" label="打单平台名称" :rules="[
                                { required: true, message: '请输入打单平台名称', trigger: 'blur', }
                            ]">
                                <el-input v-model="addForm.printOrderPlatform" style="width: 100%" clearable
                                    placeholder="打单平台名称" :maxlength="100"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" >
                            <el-form-item prop="isProSelGoods" label="是否为选品中心的货物" :rules="[
                                { required: true, message: '请选择是否为选品中心的货物', trigger: 'blur', }
                            ]">
                                <el-select v-model="addForm.isProSelGoods" clearable filterable placeholder="是否为选品中心的货物" style="width: 100%">
                                    <el-option value="是" key="是" label="是"></el-option>
                                    <el-option value="否" key="否" label="否"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" >
                          <el-form-item prop="isOpenInvoice" label="是否能开票">
                            <el-select v-model="addForm.isOpenInvoice" clearable filterable placeholder="是否能开票" style="width: 100%" @change="changeInvoiceType">
                                <el-option value="是" label="是" />
                                <el-option value="否" label="否" />
                            </el-select>
                          </el-form-item>
                        </el-col>
                      <el-col :span="4" v-if="addForm.isOpenInvoice == '是'">
                        <el-form-item prop="supplierIsOfPublicPay" label="货款对公/对私">
                          <el-select v-model="addForm.supplierIsOfPublicPay" clearable filterable placeholder="货款对公/对私" style="width: 100%">
                            <el-option value="货款对公" label="货款对公" />
                            <el-option value="货款对私" label="货款对私" />
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="5" v-if="addForm.isOpenInvoice == '是'">
                        <el-form-item prop="invoiceType" label="发票类型">
                          <el-select v-model="addForm.invoiceType" clearable filterable placeholder="发票类型" style="width: 100%">
                              <el-option value="普票" label="普票" />
                              <el-option value="专票" label="专票" />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="5" v-if="addForm.isOpenInvoice == '是'">
                        <el-form-item prop="supplierTaxRate" label="税点(%)">
                          <div style="display: flex; align-items: center; width: 100%">
                            <el-input-number :controls="false" :min="0" :max="999" :precision="1"
                            placeholder="税点(%)" style="flex: 1;" v-model="addForm.supplierTaxRate" />
                            <span style="margin-left: 5px;">%</span>
                          </div>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="5" :sm="5" :md="5" :lg="5" :xl="5" :span="5" v-if="addForm.payWay == '银行卡'">
                            <el-form-item prop="payReMark" label="货款账户">
                                <div style="display: flex;align-items: center;">
                                    <el-select v-model="addForm.paySupplierBankAccountId" placeholder="请选择供应商账户"
                                        style="width:90%;" @change="changeBankType($event, 1)" filterable>
                                        <el-option v-for="item in bankList" :key="item.id"
                                            :label="item.accountName + '-' + item.account + '-' + item.bankType"
                                            :value="item.id">
                                        </el-option>
                                    </el-select>
                                    <el-button @click="addAccount" style="float: right; padding: 3px 0" type="text">新增
                                    </el-button>
                                    <el-button @click="copyAount(1)" style="float: right; padding: 3px 0" type="text">复制
                                    </el-button>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="5" :sm="5" :md="5" :lg="5" :xl="5" :span="5"
                            v-if="addForm.payWay == '银行卡' && addForm.payType == '对公'">
                            <el-form-item prop="taxPayType" label="税费付款方式">
                                <el-select v-model="addForm.taxPayType" placeholder="请选择税费付款方式" style="width:100%;"
                                    @change="changeTaxPayType">
                                    <el-option label="货款含税费" value="货款含税费" />
                                    <el-option label="税费公转" value="税费公转" />
                                    <el-option label="税费私转" value="税费私转" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="5" :sm="5" :md="5" :lg="5" :xl="5" :span="5"
                            v-if="addForm.payWay == '银行卡' && addForm.payType == '对公' && (addForm.taxPayType == '税费公转' || addForm.taxPayType == '税费私转')">
                            <el-form-item :prop="addForm.taxPayType == '税费私转' ? 'taxPaySupplierBankAccountId' : ''"
                                label="税费账户" ref="taxPayType">
                                <div style="display: flex;align-items: center;">
                                    <el-select v-model="addForm.taxPaySupplierBankAccountId" placeholder="请选择收款信息"
                                        style="width:100%;" @change="changeBankType($event, 2)" filterable>
                                        <el-option v-for="item in taxPayTypeBankList" :key="item.id"
                                            :label="item.accountName + '-' + item.account + '-' + item.bankType"
                                            :value="item.id">
                                        </el-option>
                                    </el-select>
                                    <el-button @click="addAccount" style="float: right; padding: 3px 0" type="text">新增
                                    </el-button>
                                    <el-button @click="copyAount(2)" style="float: right; padding: 3px 0" type="text">复制
                                    </el-button>
                                </div>

                            </el-form-item>
                        </el-col>
                        <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6" :span="6"
                            v-if="addForm.payWay == '银行卡' && addForm.payType == '对公' && (addForm.taxPayType == '税费公转' || addForm.taxPayType == '税费私转')">
                            <div style="display: flex;width: 100%;">
                                <el-form-item prop="payReMark" label="税点(%):">
                                    <el-input-number v-model="addForm.taxRate" :min="5" :max="20" placeholder="请输入税点"
                                        :precision="2" :controls="false" style="width:100px;" @change="cpt" />
                                </el-form-item>
                                <el-form-item prop="payReMark" label="税费:" label-width="50px">
                                    <el-input-number v-model="addForm.taxAmount" :min="0" :max="99999999"
                                        placeholder="请输入税费" :precision="2" :controls="false" style="width:100px;"
                                        @blur="cptRate">
                                    </el-input-number>
                                </el-form-item>
                            </div>
                        </el-col>
                        <el-col :xs="3" :sm="3" :md="3" :lg="3" :xl="3" :span="3" v-if="addForm.payWay == '银行卡'">
                            <div style="display: flex;align-items: center;">
                                <el-button @click="OneClickCopy" type="text">一键复制货款及税费信息至备注</el-button>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="10" :sm="10" :md="10" :lg="10" :xl="10">
                            <el-form-item prop="purImageUrl" label="图片" :show-message="addForm.warehouse == '12732439'">
                                <aauploadimgFile v-if="!drawer" ref="uploadimgFile" :ispaste="true"
                                    :isuploadfile="false" :noDel="false" :accepttyes="accepttyes" :isImage="true"
                                    :uploadInfo="addForm.purImageUrl" :keys="[1, 1]" @callback="callbackimg"
                                    :imgmaxsize="10" :limit="10" :multiple="true">
                                </aauploadimgFile>
                            </el-form-item>
                        </el-col>
                        <el-col v-if="addForm.payType == '对私'" :xs="14" :sm="14" :md="14" :lg="14" :xl="14">
                            <el-form-item prop="payTypeImageUrl" label="对私凭证">
                                <uploadimgFile ref="payTypeImageUrl" @change="selchange" :ispaste="true"
                                    :isuploadfile="false" :noDel="false" :accepttyes="accepttyes" :isImage="true"
                                    :uploadInfo="addForm.payTypeImageUrl" :keys="[1, 1]" @callback="callbackimg1"
                                    :imgmaxsize="10" :limit="10" :multiple="true">
                                </uploadimgFile>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-card class="box-card" style="width:98% ;height: 45px; overflow: hidden;">
                            <div slot="header" class="clearfix" style="">
                                <span>采购明细</span>
                                <el-button @click="onSelctCp()" style="float: right; padding: 3px 0" type="text">添加商品
                                </el-button>
                                <el-button @click="addCGYF()" style="float: right; padding: 3px 5px" type="text">添加采购运费
                                </el-button>
                                <el-button @click="tocreateimg()" :loading="loading"
                                    style="float: right; padding: 3px 0" type="text">复制图片
                                </el-button>
                                <el-button v-if="isshowopenofflog == false" type="text"
                                    style="float: right; padding: 3px 0" @click="clickopenofflog()">通知运营
                                </el-button>
                                <el-button type="text" style="float: right; padding: 3px 0" @click="StartChat">发起聊天
                                </el-button>
                                <div style="float: right;">
                                    一键调拨
                                    <el-switch style="padding: 3px 0" :value="addForm.isAllotSelect"
                                        @change="allotSelect" active-color="#13ce66"
                                        inactive-color="#ff4949"></el-switch>
                                </div>
                            </div>
                        </el-card>
                        <el-card class="box-card" style="width:98% ;height: 320px;overflow: auto;">
                            <div style="width: 100%; height: 100%;" ref="oneboxx" id="oneboxx">
                                <el-table :data="addForm.dtlGoods" border show-summary :summary-method="getSummaries"
                                    v-loading="tableLoading">
                                    <el-table-column label="#" width="40">
                                        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                                    </el-table-column>
                                    <el-table-column prop="Id" label="id" v-if="false" />
                                    <el-table-column prop="goodsCode" label="商品编码" sortable :sort-method="customSort"
                                        :default-sort="false" />
                                    <el-table-column prop="labels" label="标签" width="120" />
                                    <el-table-column prop="goodsName" label="商品名称" width="160" />
                                    <el-table-column prop="picture" label="图片" width="60">
                                        <template slot-scope="scope">
                                            <el-image style="width: 30px; height: 30px"
                                                :src="(!!scope.row.picture ? scope.row.picture : imagedefault)"
                                                @click="showImgDtl(scope.row.picture)"></el-image>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="count" label="数量" width="100">
                                        <template slot-scope="scope">
                                            <el-tooltip class="item" effect="dark"
                                                :content="scope.row.boxSpecs ? ('箱规' + scope.row.boxSpecs + ',' + scope.row.countResult) : '暂无箱规'"
                                                placement="top-start">
                                                <el-input-number style="width: 100px;" v-model="scope.row.count"
                                                    :controls="false" :precision="0" :min="0" :max="100000000"
                                                    placeholder="数量" align="center" @change="dtlCountChange(scope.row)">
                                                </el-input-number>
                                            </el-tooltip>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="applyCount" label="认领数量" width="100">
                                        <template slot-scope="scope">
                                            <el-tooltip class="item" effect="dark" :content="scope.row.applyMsg"
                                                placement="top-start">
                                                <el-input-number style="width: 100px;" :disabled="true"
                                                    v-model="scope.row.applyCount" :controls="false" :precision="0"
                                                    :min="0" :max="100000000" placeholder="数量" align="center">
                                                </el-input-number>
                                            </el-tooltip>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="price" label="单价" width="120">
                                        <template slot-scope="scope">
                                            <!-- <span class="time">{{ scope.row.price }}</span> -->
                                            <el-input-number style="width: 100px;" v-model="scope.row.price"
                                                :controls="false" :min="-999" :max="100000" placeholder="单价" width="120"
                                                @change="dtlPriceChange(scope.row)">
                                            </el-input-number>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="packCount" label="装箱数" width="120">
                                        <template #default="{ row }">
                                            <el-input-number style="width: 100px;" v-model="row.packCount"
                                                :precision="0"
                                                :disabled="row.goodsCode == 'CGYF' || row.goodsCode == 'CGBCJ-001'"
                                                :controls="false" :min="0" :max="100000" placeholder="装箱数" width="120">
                                            </el-input-number>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="packNum" label="件数" width="60"></el-table-column>
                                    <el-table-column prop="amount" label="金额" width="120">
                                        <template slot-scope="scope">
                                            <span class="time">{{ scope.row.amount }}</span>
                                        </template>
                                    </el-table-column>
                                    <!-- <el-table-column label="通知运营" align="center" v-if="!addForm.isRef">
                                <template slot-scope="scope" >
                                    <el-button type="text" v-if="!scope.row.isNotified" @click="clickopenofflog(scope.row)">通知运营</el-button>
                                </template>
                            </el-table-column> -->
                                    <el-table-column label="调拨" width="80">
                                        <template slot-scope="scope">
                                            <el-switch v-model="scope.row.isAllot" active-color="#13ce66"
                                                inactive-color="#ff4949" style="margin-right: 15px;"></el-switch>
                                        </template>
                                    </el-table-column>
                                    <el-table-column lable="操作" width="120">
                                        <template slot-scope="scope">
                                            <el-button type="danger" @click="onDelDtlGood(scope.row.goodsCode)">删除
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
                <el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeFuncDtl"
                    style="z-index:9999;" />
                <template align="center">
                    <span class="dialog-footer">
                        <div style="display: flex;justify-content: end;margin: 10px 25px 0 0;">
                            <el-button @click="() => {
                                dialogAddVisible = false
                                aliOrderInfo.IsShow = false
                            }">取 消</el-button>
                            <el-button type="primary" @click="viewPurchaseNewPlan()" v-if="addForm.refType=='自动采购单'">采购建议数据</el-button>
                            <el-button type="primary" @click="OrderView"
                                v-if="checkPermission('1688PurchaseOrdersView')">1688采购单预览</el-button>
                            <el-button type="primary" :loading="onFinishLoading" v-throttle="3000" @click="verifyPurchaseSupplierShipmentPlaceAsync()">确
                                定</el-button>
                            <!-- <my-confirm-button type="submit" :validate="finishFormValidate" :loading="onFinishLoading" @click="onFinish()">
                            </my-confirm-button> -->
                            <my-confirm-button type="submit" :validate="finishFormValidate" v-if="false">同步到聚水潭
                            </my-confirm-button>
                        </div>
                    </span>
                </template>

            </template>
        </vxe-modal>

        <el-dialog title="采购建议数据" :visible.sync="purchaseNewPlanVisible" ref="purchaseNewPlanDialog" width="75%" height="800px" v-dialogDrag>
            <div ref="oneboxx">
                <el-table ref="table" :data="newPlanList" row-key="id" border style="width: 100%;font-size: 13px;color: #757575;" height="800px"
                    show-overflow-tooltip v-loading="purchaseNewPlanLonding">
                    <el-table-column type="index" label="#" align="center" width="30"></el-table-column>
                    <el-table-column label="图片" :show-overflow-tooltip="true" width="100" align="left">
                        <teleport slot-scope="scope">
                            <el-image :src="scope.row.images" :preview-src-list="scope.row.imageList"
                                class="imgcss" style="margin-left: -10px;" fit="fill"
                                :lazy="true"></el-image>
                        </teleport>
                    </el-table-column>
                    <el-table-column label="商品信息" prop="styleCode" width="130"
                        :show-overflow-tooltip="true">
                        <template #header>
                            <span class="grid-header">
                                <span>
                                    <el-tooltip class="item" effect="dark" content="商品信息，款式编码，分仓"
                                        placement="top-end">
                                        <span><i class="el-icon-question"></i></span>
                                    </el-tooltip>
                                </span>
                                <span>商品信息</span>
                            </span>
                        </template>
                        <template slot-scope="scope">
                            <div style="height: 100px; overflow: hidden; display: flex; flex-direction: column; justify-content: center;">
                            <div class="divline">
                                <span style="color: #757575; display: block; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                {{ scope.row.goodsName }}
                                </span>
                            </div>
                            <div>
                                <span style="font-size: 15px;color: black; display: block; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                {{ scope.row.styleCode }}
                                </span>
                            </div>
                            <div>
                                <span style="color: #757575; display: block; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                {{ scope.row.warehouseName }}
                                </span>
                            </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品编码" prop="goodsCode" width="80" :show-overflow-tooltip="true">
                        <template slot-scope="scope">
                            <span class="linebreak">
                                {{ scope.row.goodsCode }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品标签" prop="goodsLable" width="70"
                        :show-overflow-tooltip="true">
                        <template slot-scope="scope">
                            <div>
                                <div style="color: #757575;" class="linebreak1">{{ scope.row.goodsLable }}
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品分类" prop="groupId" width="70" :show-overflow-tooltip="true">
                        <template slot-scope="scope">
                            <div>
                                {{ scope.row.groupName == null ? '' : scope.row.groupName }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="品牌" prop="brandId" width="70" :show-overflow-tooltip="true">
                        <template slot-scope="scope">
                            <div>
                                {{ scope.row.brandName == null ? '' : scope.row.brandName }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品备注" prop="goodsRemark" width="80"></el-table-column>
                    <el-table-column label="建议采购信息" width="220">
                        <template slot-scope="scope">
                            <div>
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <span style="color: red;">{{ scope.row.purchasePlanCount3 }}({{
                                    scope.row.packCount }})</span>
                            </div>
                            <div style="display: flex; flex-direction: row;">
                                <div>
                                    ￥{{ scope.row.cost }} *
                                </div>
                                <el-tooltip class="item" effect="dark" :content="scope.row.applyMsg"
                                    placement="top">
                                    <el-input-number v-model="scope.row.purchasePlanCount3"
                                        style="width: 80px;" :controls="false" size="small"
                                        :disabled="true"></el-input-number>
                                </el-tooltip>
                                <div>
                                    ={{ (scope.row.purchasePlanCount3 * scope.row.cost).toFixed(1) }}
                                </div>

                            </div>
                            <div>
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                {{ scope.row.packCount == 0 ? scope.row.purchasePlanCount3 + '件' :
                                Math.floor(scope.row.purchasePlanCount3 / scope.row.packCount) == 0 ?
                                scope.row.purchasePlanCount3 + '件' : Math.floor(scope.row.purchasePlanCount3 /
                                scope.row.packCount) + '箱' +
                                ((scope.row.purchasePlanCount3 % scope.row.packCount) == 0 ? '' :
                                (scope.row.purchasePlanCount3 % scope.row.packCount) + '件')
                                }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="库存周转天数" prop="inventoryDay" column-key="inventoryDay"
                        width="110" :show-overflow-tooltip="true">
                        <template #header>
                            <span class="grid-header">
                                <span>库存周转天数<br /> 采购周转天数</span>
                            </span>
                        </template>
                        <template slot-scope="scope">
                            <div>
                                {{ scope.row.inventoryDay }}
                            </div>
                            <br />
                            <div style="color: blue;">
                                {{ scope.row.purchaseDay }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="主仓库存" prop="masterStock" column-key="masterStock" width="95"
                        :show-overflow-tooltip="true">
                        <template #header>
                            <span class="grid-header">
                                <span>主仓库存<br /> 仓库库存数</span>
                            </span>
                        </template>
                        <template slot-scope="scope">
                            <div>
                                {{ scope.row.masterStock }}
                            </div>
                            <br />
                            <div>
                                {{ scope.row.warehouseStock }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="进货仓库存" prop="purchaseStock" column-key="purchaseStock"
                        width="140" :show-overflow-tooltip="true">
                        <template #header>
                            <span class="grid-header">
                                <span>采购在途数<br /> 进货仓库存 <br /> 待审核</span>
                            </span>
                        </template>
                        <teleport slot-scope="scope">
                            <div>
                                开单量：{{ scope.row.indexNoCount }}
                            </div>
                            <div style="color: blue;">
                                在 途：{{ scope.row.inTransitNum }}
                            </div>
                            <div style="color: blue;">
                                调拨在途：{{ scope.row.allocateTransitNum }}
                            </div>
                            <div>
                                进货仓：{{ scope.row.purchaseStock }}
                            </div>
                            <div>
                                审核中：{{ scope.row.inReviewNum }}
                            </div>
                            <div>
                                待审核：{{ scope.row.checkNum }}
                            </div>
                        </teleport>
                    </el-table-column>
                    <el-table-column label="订单待发" prop="orderWaitSend" width="80"
                        :show-overflow-tooltip="true">
                    </el-table-column>
                    <el-table-column label="可售库存" prop="sellStock" width="80" :show-overflow-tooltip="true">
                        <template #header>
                            <span class="grid-header">
                                <span>可售库存</span>
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="安全天数上~下 安全库存上~下" width="120">
                        <template #header>
                            <span class="grid-header">
                                <span>安全天数上~下<br /> 安全库存上~下</span>
                            </span>
                        </template>
                        <teleport slot-scope="scope">
                            <div>
                                {{ scope.row.safeDayUp2 }} / {{ scope.row.safeDayDown2 }}
                            </div>
                            <br />
                            <div>
                                {{ scope.row.safeStockUp2 }} / {{ scope.row.safeStockDown2 }}
                            </div>
                        </teleport>
                    </el-table-column>
                    <el-table-column label="最近在途时长 历史平均在途" width="110">
                        <template #header>
                            <span class="grid-header">
                                <span>最近在途时长<br />历史平均在途</span>
                            </span>
                        </template>
                        <teleport slot-scope="scope">
                            <div>
                                {{ formatSecondNewToHour(scope.row.lastInTransitTime) }}
                            </div>
                            <br />
                            <div>
                                {{ formatSecondNewToHour(scope.row.avgInTransitTime) }}
                            </div>
                        </teleport>
                    </el-table-column>
                    <el-table-column label="总销量/总退货" width="180">
                        <teleport slot-scope="scope">
                            <div>
                                3日： {{ scope.row.salesDay3 }} / {{ scope.row.refundDay3 }}
                            </div>
                            <div>
                                7日： {{ scope.row.salesDay7 }} / {{ scope.row.refundDay7 }}
                            </div>
                            <div>
                                15日： {{ scope.row.salesDay15 }} / {{ scope.row.refundDay15 }}
                            </div>
                            <div>
                                30日： {{ scope.row.salesDay30 }} / {{ scope.row.refundDay30 }}
                            </div>
                        </teleport>
                    </el-table-column>
                    <el-table-column label="昨日销量" prop="salesYesterday" width="80" >
                    </el-table-column>
                    <el-table-column label="日均销量/日均退货" width="180">
                        <teleport slot-scope="scope">
                            <div>
                                3日： {{ scope.row.avgDay3 }} / {{ scope.row.avgRefundDay3 }}
                            </div>
                            <div>
                                7日： {{ scope.row.avgDay7 }} / {{ scope.row.avgRefundDay7 }}
                            </div>
                            <div>
                                15日： {{ scope.row.avgDay15 }} / {{ scope.row.avgRefundDay15 }}
                            </div>
                            <div>
                                30日： {{ scope.row.avgDay30 }} / {{ scope.row.avgRefundDay30 }}
                            </div>
                        </teleport>
                    </el-table-column>
                    <el-table-column label="日均修正量" prop="amendDay" width="auto"
                        :show-overflow-tooltip="true">
                    </el-table-column>
                    <el-table-column label="总销量(排除下架)" width="180">
                        <teleport slot-scope="scope">
                            <div>
                                3日： {{ scope.row.salesDay3_2 }}
                            </div>
                            <div>
                                7日： {{ scope.row.salesDay7_2 }}
                            </div>
                            <div>
                                15日： {{ scope.row.salesDay15_2 }}
                            </div>
                            <div>
                                30日： {{ scope.row.salesDay30_2 }}
                            </div>
                        </teleport>
                    </el-table-column>
                    <el-table-column label="新日均修正量" prop="amendDayNew" width="100" :show-overflow-tooltip="true">
                        <template #header>
                            <span class="grid-header">
                                <span>
                                    <el-tooltip class="item" effect="dark" content="总销量(排除下架)3日/3" placement="top-end">
                                        <span><i class="el-icon-question"></i></span>
                                    </el-tooltip>
                                </span>
                                <span>新日均修正量</span>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-dialog>

        <!--选择商品-->
        <el-dialog title="选择采购明细" :visible.sync="goodschoiceVisible" width='88%' height='500px' v-dialogDrag>
            <goodschoice :ischoice="true" ref="goodschoice" style="z-index:10000;height:500px" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="goodschoiceVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onQueren()">确 定</el-button>
                </span>
            </template>
        </el-dialog>
        <el-drawer title="处理" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
            :visible.sync="editVisible" direction="btt" size="'auto'" class="el-drawer__wrapper"
            style="position:absolute;">
            <el-form style="margin-top: 10px;" class="ad-form-query" :model="autoform.data" ref="editQRFrom"
                :rules="editQRFromRules" label-width="110px">
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="采购单号" prop="type" col="">
                            <el-input v-model="autoform.data.buyNo" readonly></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="Erp编号" prop="type">
                            <el-input v-model="autoform.data.indexNo" readonly></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="采购负责人" prop="type">
                            <el-input v-model="autoform.data.brandName" readonly></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="箱数" prop="type">
                            <el-input-number v-model="autoform.data.boxCount" :min="0" :max="1000000"
                                :precision="0"></el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="预计到货日期" prop="planArrivalTime">
                            <el-date-picker v-model="autoform.data.planArrivalTime" type="datetime" format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd" placeholder="预计到货日期"></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="物流单号" prop="type">
                            <el-input v-model="autoform.data.expressNo"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="物流公司" prop="type">
                            <el-select clearable filterable v-model="autoform.data.companyCode" placeholder="请选择物流公司">
                                <el-option v-for="item in companyOption" :key="'p2f-' + item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                            <el-button type="primary" @click="showAddWLCompanyDialog">添加</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row style="margin-left: -45px;">
                    <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" />
                </el-row>
            </el-form>
            <div class="drawer-footer">
                <el-button @click.native="editVisible = false">取消</el-button>
                <my-confirm-button type="submit" :loading="editLoading" :validate="finisheditQRFromValidate"
                    @click="onEditSubmit" />
            </div>
        </el-drawer>

        <el-dialog title="添加物流公司" width="400px" ref="addWLCompanyDialog" :visible.sync="addWLCompanyDialog.visible"
            v-dialogDrag>
            <el-form style="margin-top: 10px;" class="ad-form-query" :model="addWLCompanyDialog.model"
                ref="addWLCompanyFrom" label-width="100px">
                <el-form-item label="公司名称" prop="type" col="">
                    <el-input v-model="addWLCompanyDialog.model.name" maxlength="20"></el-input>
                </el-form-item>
                <el-form-item label="公司编码" prop="type">
                    <el-input v-model="addWLCompanyDialog.model.code" maxlength="15"></el-input>
                </el-form-item>
            </el-form>
            <div class="drawer-footer" style="width: 350px;">
                <el-button @click.native="addWLCompanyDialog.visible = false">取消</el-button>
                <el-button @click.native="addWLCompany" type="primary">添加</el-button>
            </div>
        </el-dialog>

        <el-dialog ref="editPopover" :visible.sync="visiblepopover" v-dialogDrag>
            <goodscoderecord ref="goodscoderecord1" :filter="goodscoderecordfilter" style="height: 400px">
            </goodscoderecord>
        </el-dialog>

        <el-dialog :visible.sync="dialoganalysisVisible" v-dialogDrag :show-close="false">
            <purchasehistorytjanalysis ref="purchasehistorytjanalysis" style="height: 450px">
            </purchasehistorytjanalysis>
        </el-dialog>

        <!-- 编辑采购单详情 -->
        <el-dialog :visible.sync="dialogEditDetailVisible" v-dialogDrag :show-close="false" width="85%">
            <el-card class="box-card" style="width:100% ;height: 45px; overflow: hidden;">
                <div slot="header" class="clearfix" style="">
                    <span>采购明细</span>
                    <el-button @click="onSelctCp()" style="float: right; padding: 3px 0" type="text">添加商品
                    </el-button>
                </div>
            </el-card>
            <el-card class="box-card" style="width:100% ;height: 300px;overflow: auto;">
                <el-table :data="eidtList">
                    <el-table-column width="120" property="goodsCode" label="商品编码"></el-table-column>
                    <el-table-column width="260" property="goodsName" label="商品名称"></el-table-column>
                    <el-table-column width="60" property="price" label="单价"></el-table-column>
                    <el-table-column width="150" property="count" label="数量">
                        <template slot-scope="scope">
                            <el-input-number v-model="scope.row.count" :min="scope.row.inCount" :max="100000000"
                                placeholder="数量" align="center" :change="editCountChange(scope.row)">
                            </el-input-number>
                        </template>
                    </el-table-column>
                    <el-table-column width="90" property="inCount" label="已入库数量"></el-table-column>
                    <el-table-column width="75" property="nonInCount" label="未入库数"></el-table-column>
                    <el-table-column width="100" property="nonInAmont" label="未入库金额"></el-table-column>
                    <el-table-column width="150" property="warehouse" label="第三方物流和分仓">
                        <template slot-scope="scope">
                            <span>{{ (scope.row.warehouseName) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column width="150" property="goodsLable" label="商品标签"></el-table-column>
                    <el-table-column width="60" property="isError" label="是否异常">
                        <template slot-scope="scope">
                            <span>{{ formatYesornoBool(scope.row["isError"]) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column width="100" property="lastWarehousingDate" label="入库时间">
                        <template slot-scope="scope">
                            <div v-html="formatTime(scope.row['lastWarehousingDate'], 'MM-DD')"></div>
                        </template>
                    </el-table-column>
                    <el-table-column width="100" property="lastInTransitTime" label="在途时长">
                        <template slot-scope="scope">
                            <div v-html="formatSecondToHour(scope.row['lastInTransitTime'])"></div>
                        </template>
                    </el-table-column>
                    <el-table-column width="60" label="缺货状态">
                        <template slot-scope="scope">
                            <div v-html="formatIsOutStock(scope.row['isOutStock'])"></div>
                        </template>
                    </el-table-column>
                    <el-table-column lable="操作">
                        <template slot-scope="scope">
                            <el-button type="danger" @click="onDelEditGood(scope.$index)">删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

            </el-card>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogEditDetailVisible = false">关闭</el-button>
                <el-button type="primary" @click="onSaveOrderDetail">保存</el-button>
            </span>
        </el-dialog>

        <vxe-modal v-model="visiblepopoverdetail" :width="750" :position="{ top: 380, left: '0px' }" resize
            mask-closable>
            <template #default>
                <!-- show-zoom -->
                <!-- <el-dialog :visible.sync="visiblepopoverdetail" v-dialogDrag :show-close="false" width="50%"> -->
                <!-- <el-popover ref="detailPopover" placement="right" v-model="visiblepopoverdetail" :reference="prevTarget"
            :key="('detail' + popperFlagdetail.toString())" style="max-height:80%;"> -->
                <template>
                    <el-table :data="detaillist" border max-height="400" :show-summary="true">
                        <el-table-column width="130" property="goodsCode" label="商品编码" sortable></el-table-column>
                        <el-table-column width="260" property="goodsName" label="商品名称"></el-table-column>
                        <el-table-column v-if="isshowoldoldPrice" width="80" property="oldPrice"
                            label="上次单价"></el-table-column>
                        <el-table-column width="60" property="price" label="单价">
                            <template slot-scope="scope">
                                <!-- 上次单价比这次单价低 的 标红 -->
                                <span v-if="(scope.row.price - scope.row.oldPrice) > 0" style="color: red;">{{
                                    scope.row.price }}</span>
                                <span v-else>{{ scope.row.price }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column width="60" property="count" label="数量"></el-table-column>
                        <el-table-column width="60" property="sumPrice" label="金额">
                            <template slot-scope="scope">
                                <span>{{ scope.row.count * scope.row.price }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="isshowoldPrice" width="90" property="inCount" label="已入库数量">
                            <template slot-scope="scope">
                                <span v-if="(scope.row.inCount > 0 && (scope.row.count - scope.row.inCount) > 0)"
                                    style="color: red;">{{ scope.row.inCount }}</span>
                                <span v-else>{{ scope.row.inCount }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="isshowoldPrice" width="75" property="nonInCount" label="未入库数">
                            <template slot-scope="scope">
                                <div v-if="(scope.row.inCount > 0 && (scope.row.count - scope.row.inCount) > 0)">
                                    {{ scope.row.nonInCount }}<strong style="color:green"> 缺</strong></div>
                                <div v-else>{{ scope.row.nonInCount }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="isshowoldPrice" width="100" property="nonInAmont"
                            label="未入库金额"></el-table-column>
                        <el-table-column v-if="isshowoldPrice" width="150" property="warehouse" label="第三方物流和分仓">
                            <template slot-scope="scope">
                                <span>{{ (scope.row.warehouseName) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="isshowoldPrice" width="150" property="goodsLable"
                            label="商品标签"></el-table-column>
                        <el-table-column v-if="isshowoldPrice" width="100" property="isError" label="是否异常">
                            <template slot-scope="scope">
                                <span>{{ formatYesornoBool(scope.row["isError"]) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-colum v-if="isshowoldPrice" width="100" property="lastWarehousingDate" label="入库时间">
                            <template slot-scope="scope">
                                <div v-html="formatTime(scope.row['lastWarehousingDate'], 'MM-DD')"></div>
                            </template>
                        </el-table-colum>
                        <el-table-column v-if="isshowoldPrice" width="100" property="lastInTransitTime" label="在途时长">
                            <template slot-scope="scope">
                                <div v-html="formatSecondToHour(scope.row['lastInTransitTime'])"></div>
                            </template>
                        </el-table-column>
                        <el-table-column width="auto" label="缺货状态">
                            <template slot-scope="scope">
                                <div v-html="formatIsOutStock(scope.row['isOutStock'])"></div>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
                <!-- </el-popover> -->
                <!-- </el-dialog> -->
            </template>
        </vxe-modal>

        <div class="imgDolg" v-show="imgPreview.show" @click.stop="imgPreview.show = false">
            <i class="el-icon-close" id="imgDolgClose" @click.stop="imgPreview.show = false"></i>
            <img @click.stop="imgPreview.show = true" :src="imgPreview.img" />
        </div>

        <el-drawer title="物流跟踪" :visible.sync="drawervisible" direction="rtl">
            <logistics ref="logistics"></logistics>
        </el-drawer>

        <!-- 采购单建立成功提示 -->
        <el-dialog title="温馨提示：请前往采购单处理" :visible.sync="messagedialogVisible" v-dialogDrag width="30%">
            <el-alert :title="buyNoMessage" type="success" :closable="false"></el-alert>
            <br /> <br /> <br /> <br />
            <span slot="footer" class="dialog-footer">
                <el-button @click="messagedialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="messagedialogVisible = false">确 定</el-button>
            </span>
        </el-dialog>

        <!-- 采购单建立成功提示 -->
        <el-dialog title="提示" :visible.sync="ontaskPurchaseUploadLoading" v-dialogDrag width="30%">
            <el-alert :title="buyNoSuccessMessage" type="success" :closable="false"></el-alert>
            <span slot="footer" class="dialog-footer">
                <el-button @click="ontaskPurchaseUploadLoading = false">取 消</el-button>
                <el-button type="primary" @click="ontaskPurchaseUploadLoading = false">确 定</el-button>
            </span>
        </el-dialog>

        <!-- ERP编号明细 -->
        <el-dialog title="采购明细" :visible.sync="indexNoDetailVisible" v-dialogDrag width="60%" custom-class="dialogLeft"
            top="40vh">
            <div style="height:60%">
                <el-tabs v-model="activeName" style="height: 94%;">
                    <el-tab-pane label="采购明细" name="first" style="height: 100%;">
                        <template>
                            <el-table :data="detaillist" border max-height="400" :show-summary="true">
                                <el-table-column width="120" property="goodsCode" label="商品编码"
                                    sortable></el-table-column>
                                <el-table-column width="120" property="labels" label="标签"></el-table-column>
                                <el-table-column width="240" property="goodsName" label="商品名称"></el-table-column>
                                <el-table-column width="100" property="oldPrice" label="上次单价"></el-table-column>
                                <el-table-column width="100" property="price" label="单价">
                                    <template slot-scope="scope">
                                        <span v-if="(scope.row.price - scope.row.oldPrice) > 0" style="color: red;">{{
                                            scope.row.price }}</span>
                                        <span v-else>{{ scope.row.price }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column width="100" property="count" label="数量"></el-table-column>
                                <el-table-column width="100" property="packCount" label="件数"></el-table-column>
                                <el-table-column width="100" property="packNum" label="装箱数"></el-table-column>
                                <el-table-column width="110" property="sumPrice" label="金额"></el-table-column>
                                <el-table-column width="auto" label="缺货状态">
                                    <template slot-scope="scope">
                                        <div v-html="formatIsOutStock(scope.row['isOutStock'])"></div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </template>
                    </el-tab-pane>
                    <el-tab-pane label="操作日志" name="second" style="height: 100%;">
                        <PurchaseOrderLog ref="PurchaseOrderLog" :filter="detailLogFilter"></PurchaseOrderLog>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </el-dialog>

        <el-dialog title="截止时间" :visible.sync="dialogoffTimeVisible" width="30%" :before-close="handleClose"
            v-dialogDrag>
            <span>
                <el-form ref="procurementAllPlan" :model="procurementAllPlan" :rules="procurementAllPlanRules"
                    label-width="100px">
                    <el-form-item label="截止日期" prop="offTime">
                        <el-date-picker v-model="procurementAllPlan.offTime" format="yyyy-MM-dd HH:mm:ss"
                            value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期时间"
                            :picker-options="pickerOptions"></el-date-picker>
                    </el-form-item>
                </el-form>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogoffTimeVisible = false">取 消</el-button>
                <my-confirm-button type="submit" :loading="dialogoffTimeloding" :validate="finishprocurementValidate"
                    @click="clickProcurement()">确定</my-confirm-button>
            </span>
        </el-dialog>

        <el-dialog :visible.sync="dataVisible" width="72%" class="screenDrag" v-dialogDrag>
            <div class="screenPicShow" v-loading="loading">
                <img id="screen" :src="dataURL" />
            </div>
            <div class="buttonBox">
                <el-button @click="zoomIn"><i class="el-icon-zoom-in icon"></i></el-button>
                <el-button @click="zoomOut"> <i class="el-icon-zoom-out icon"></i></el-button>
                <el-button @click="downloadFileByBase64"> <i class="el-icon-download icon"></i></el-button>
            </div>
        </el-dialog>

        <el-dialog :visible.sync="orderViewVisible" v-dialogDrag :close-on-click-modal="false" width="72%"
            title="1688采购单预览">
            <OrderView1688 ref="OrderView1688" :data="data" :params="orderViewInfo" :propsInfo="addForm"
                v-if="orderViewVisible" @closeOrder="closeOrder">
            </OrderView1688>
        </el-dialog>

        <el-dialog :visible.sync="wmsSetVisible" v-dialogDrag width="50%" title="仓库设置">
            <wmsSet ref="wmsSet" v-if="wmsSetVisible" @closeOrder="closeOrder">
            </wmsSet>
        </el-dialog>

        <el-dialog :visible.sync="changePriceVisible" v-dialogDrag width="50%" title="改价金额">
            <changePrice1688 ref="changePrice1688" v-if="changePriceVisible" :changePriceInfo="changePriceInfo"
                @closeOrder="closeOrder">
            </changePrice1688>
        </el-dialog>

        <el-dialog :visible.sync="addAccountVisible" v-dialogDrag width="20%" title="新增收款信息">
            <el-form label-width="100px" :model="addAccountForm" :rules="addAccountRules" ref="addAccountRuleForm"
                v-if="addAccountVisible">
                <el-form-item label="对公/对私:" prop="publicPayType">
                    <el-radio-group v-model="addAccountForm.publicPayType">
                        <el-radio label="对公">对公</el-radio>
                        <el-radio label="对私">对私</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="银行账户:" prop="accountName">
                    <el-input v-model.trim="addAccountForm.accountName" maxlength="50" clearable />
                </el-form-item>
                <el-form-item label="银行账号:" prop="account">
                    <el-input v-model.trim="addAccountForm.account" maxlength="50" clearable />
                </el-form-item>
                <el-form-item label="开户行:" prop="bankType">
                    <el-input v-model.trim="addAccountForm.bankType" maxlength="50" clearable />
                </el-form-item>
                <el-form-item prop="paymentInfoPictureList" label="图片">
                    <aauploadimgFile ref="paymentInfoPictureList" :ispaste="true" :isuploadfile="false" :noDel="false" :accepttyes="accepttyes" :isImage="true"
                        :uploadInfo="paymentInfoPictureList" :keys="[1, 1]" @callback="callBackPaymentPicture" :imgmaxsize="9" :limit="9" :multiple="true">
                    </aauploadimgFile>
                </el-form-item>
                <el-form-item label="">
                    <el-button @click="addAccountVisible = false">取消</el-button>
                    <el-button type="primary" @click="addAccountSubmit('addAccountRuleForm')">确定</el-button>
                </el-form-item>
            </el-form>

        </el-dialog>

        <el-drawer title="涨价单" :visible.sync="addOrEditPropsDrawer" direction="rtl" :wrapperClosable="true"
            :destroy-on-close="false" size="50%">
          <div v-loading="orEditloading" style="height: 100%;width: 100%;">
            <el-tabs v-model="addactiveName" v-if="addOrEditPropsDrawersumbit">
                <el-tab-pane :label="'涨价单' + (index + 1)" :name="'first' + index" v-for="(item, index) in tabList" :key="index" :class="!item.isSubmit?'isshow':'hiddleshow'">
                  <addOrEditProps ref="addOrEditProps" @close="generateclose" @getList="handleClick()" @closeTabMethod="closeTabMethod" :formData="item"
                  :isEdit="isEdit" :auditProps="auditProps" :isView="isView"
                  :isReInitiated="isReInitiated" />
                </el-tab-pane>
            </el-tabs>
          </div>
        </el-drawer>

        <el-dialog :visible.sync="supplierFromInfo.visible" v-dialogDrag width="50%" title="货款对公/对私/税点变更日志">
          <supplierLog v-if="supplierFromInfo.visible" :data="supplierFromInfo.data" />
        </el-dialog>
    </container>
</template>
<script>
import { Loading } from 'element-ui';
import html2canvas from 'html2canvas'
import {
    editPurchaseOrder, getPurchaseOrder, queryPurchaseOrderDetail, queryPurchaseOrderDetailIndexNo, pagePurchaseOrder, importPurchaseOrder, getLastUpdateTimeyPurchase,
    exportPurchaseOrder, savePurchaseManual, getPurchaseOrderManual, CopeAddPurchaseOrder, deletePurchaseManual, batchUpdatePurchaseOrderAsync, taskPurchaseUpload,
    pagePurchaseOrderByCopyBuyNo, SyncPurchaseOrder4BuyNos, getLastPurchaseInfoByProviderId, ReCheckOrderStatus,ChangePackCount,TerminateNoApplyPurchaseOrder,
    verifyPurchaseSupplierShipmentPlace, getAutoPurchaseOrderPlan,GetSupplierTaxRateByProviderId
} from '@/api/inventory/purchase'
import inputnumberYunhan from '@/components/Comm/inputnumberYunhan.vue'
import { PreviewCreate1688PurchaseOrder, Create1688PurchaseOrder, GetAliSupperChatIdByEdit } from '@/api/inventory/purorder'
import { taskPurchaseDingDing } from "@/api/inventory/purchaseordernew";
import { purchaseNotified } from "@/api/inventory/goodscodestock"
import { getAllProBrand, getAllBianMaBrandByFilter, getCurBianMaBrand, getAllWarehouse } from '@/api/inventory/warehouse'
import { upLoadImage } from '@/api/upload/file'
import { formatTime, formatYesornoBool, formatWarehouseArea, formatNoLink, formatIsError, formatIsOutStock, formatSecondToHour, formatSecondNewToHour } from "@/utils/tools";
import { ruleExpressComanycode } from '@/utils/formruletools'
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import logistics from '@/components/Comm/logistics'
import goodscoderecord from '@/views/inventory/goodscoderecord'
import { throttle } from 'throttle-debounce';
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";
import purchasehistorytjanalysis from '@/views/inventory/components/purchasehistorytjanalysis'
import supplierLog from '@/views/inventory/supplier/supplierLog'
import goodschoice from "@/views/base/goods/goods2.vue";
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import { pageSupplierAll, GetSupplierAccountBankList, AddSupplierAccountBank, DeleteSupplierAccountBank } from '@/api/inventory/supplier'
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import PurchaseOrderLog from '@/views/inventory/purchaseorderlog'
import { addWLCompany } from '@/api/express/express'
// import pastimgVue from '@/components/Comm/pastimg.vue';
import uploadimgFile from "./uploadimgFile.vue";
import aauploadimgFile from "@/components/Comm/uploadimgFile.vue";
import { cos, max, min, row } from 'mathjs';
import middlevue from "@/store/middle.js"
import OrderView1688 from './OrderView1688.vue'
import wmsSet from './wmsSet.vue'
import { getListSql } from '@/api/inventory/basicgoods'
import changePrice1688 from './changePrice1688.vue'
import decimal from "@/utils/decimalToFixed"
import decimal1 from "@/utils/decimal"
import { getApprovalRecords } from '@/api/inventory/purchaseHike'
import { getPurchaseOrderDetail2PriceHikeTicketAsync } from '@/api/inventory/purchase'
import addOrEditProps from '@/views/inventory/PriceIncreaseOrders/components/addOrEditProps'

const tableCols = [
    { istrue: true, label: '', width: '80', type: "checkbox", },
    { istrue: true, prop: 'buyNo', label: '采购单号', width: '80', sortable: 'custom', type: 'html', formatter: (row) => formatNoLink(row.buyNo) },
    { istrue: true, prop: 'informStatusName', label: '来源', width: '80', },
    { istrue: true, prop: 'brandName', label: '采购员', width: '90', },
    { istrue: true, prop: 'indexNo', label: 'Erp编号', width: '100', sortable: 'custom', type: 'htmlOther', tootip: '该采购单包含亏损编码！', isBrage: (row) => row.refLable === '持续亏损系列编码进货', formatter: (row) => formatNoLink(row.indexNo == '0' ? ' ' : row.indexNo) },
    { istrue: true, prop: 'purchaseDate', label: '采购日期', width: '105', sortable: 'custom', formatter: (row) => formatTime(row.purchaseDate, 'YYYY-MM-DD HH:mm:ss') },
    // {istrue:true,prop:'checker',label:'审核人', width:'65',},
    { istrue: true, prop: 'checkDate', label: '审核日期', width: '105', sortable: 'custom', formatter: (row) => formatTime(row.checkDate, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'status', label: '状态', width: '185', type: 'clickLink', sortable: 'custom', style: (that, row) => that.renderStatus(row), formatter: (row) => row.status + (row.isBelowHalf ? "（采购需要矫正进货量）" : ""), },
    {
        istrue: true, prop: 'isJstSuccess', label: '钉钉审批状态', type: 'clickLink', width: '80', sortable: 'custom', style: (that, row) => that.renderRefundStatus(row),
        formatter: (row) => row.isJstSuccess == 2 && row.isAdd == 1 ? "审核通过" : row.isJstSuccess == 1 && row.isAdd == 1 ? "审批中" :
            row.isJstSuccess == 0 && row.isAdd == 1 && row.buyNo == null ? "未提交" : row.isJstSuccess == 3 && row.isAdd == 1 && row.buyNo == null ? "拒绝"
                : row.isJstSuccess == 4 && row.isAdd == 1 && row.buyNo == null ? "撤回" : " "
    },
    {
        istrue: true, type: 'button', label: '操作', width: '160',
        btnList: [{
            label: "编辑",
            htmlformatter: (row) => {
                return `<i class="el-icon-star-on" style="color:${row.status == "完成" ? "green" :
                    row.status == "已确认" ? "darkseagreen" : row.status == "待审核" ? "yellow" : row.status == "作废" ? "red" : ""}"></i>`
            },
            display: (row) => { return ((row.status == '完成' && row.isJstSuccess == 2) || (row.status == '完成') || (row.isJstSuccess == 1)); }, handle: (that, row) => that.onHand(row)
        },
        {
          label:'生成涨价单',htmlformatter: (row) => {
                return `<i class="el-icon-star-on" style="color:${row.status == "完成" ? "green" : row.status == "已确认" ? "darkseagreen" : row.status == "待审核" ? "yellow" : row.status == "作废" ? "red" : ""}"></i>`
            }, handle: (that, row) => that.handleClick(row)
        }]
    },
    {
        istrue: true, prop: 'aliChangeOrderPrice', label: '改价金额', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.viewChangePrice(row), color: (row) => {
            if (row.aliChangeOrderPrice == row.totalAmont) {
                return 'blue'
            } else if (row.aliChangeOrderPrice != row.totalAmont) {
                return 'red'
            } else {
                return 'black'
            }
        }
    },
    { istrue: true, prop: 'supplier', permission: "purchaseordersupplier", label: '供应商', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'provinceCityDistrict', label: '发货地', width: '100', },
    { istrue: true, prop: 'isProxyShipping', label: '是否能代发', width: '100', sortable: 'custom', formatter: (row) => row.isProxyShippingStr },
    { istrue: true, prop: 'printOrderPlatform', label: '打单平台名称', width: '100', sortable: 'custom' },

    { istrue: true, prop: 'isProSelGoods', label: '是否为选品中心的货物', width: '180', sortable: 'custom' },

    { istrue: true, prop: 'warehouse', label: '第三方物流和分仓', width: '100', sortable: 'custom', formatter: (row) => row.warehouseName },
    { istrue: true, prop: 'receivStatus', label: '收货状态', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'isOpenInvoice', label: '是否能开票', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'supplierIsOfPublicPay', label: '货款对公/对私', width: '80', sortable: 'custom', type: 'click', handle: (that, row) => that.onLogMethod(row) },
    { istrue: true, prop: 'invoiceType', label: '发票类型', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'supplierTaxRate', label: '税点(%)', width: '80', sortable: 'custom', type: 'click', handle: (that, row) => that.onLogMethod(row), formatter:(row)=> row.supplierTaxRate == 0 ? '0%' : row.supplierTaxRate ? row.supplierTaxRate + "%" : "" },
    { istrue: true, prop: 'rkps', label: '入库拍摄', width: '80', type: 'html', formatter: (row) => row.buyNo ? formatNoLink("查看") : "" },
    { istrue: true, prop: 'payment', label: '付款方式', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'nonInCount', label: '未入库数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'nonInAmont', label: '未入库金额', width: '95', sortable: 'custom' },
    { istrue: true, prop: 'totalAmont', label: '总金额', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'inAmont', label: '入库金额', width: '95', sortable: 'custom' },

    { istrue: true, prop: 'outKuAmount', label: '出库金额', width: '95', sortable: 'custom' },
    { istrue: true, prop: 'payAmount', label: '付款金额', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'adjustAmount', label: '调整金额', width: '95', sortable: 'custom' },
    { istrue: true, prop: 'returnAmount', label: '退款金额', width: '95', sortable: 'custom' },
    { istrue: true, prop: 'lastWarehousingDate', label: '入库时间', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.lastWarehousingDate, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'outKuDate', label: '出库时间', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'payDate', label: '付款时间', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'returnDate', label: '退款时间', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'isError', label: '进货仓状态', width: '70', sortable: 'custom', formatter: (row) => formatIsError(row.isError) },
    { istrue: true, prop: 'lastInTransitTime', label: '在途时长', width: '85', sortable: 'custom', formatter: (row) => formatSecondToHour(row.lastInTransitTime) },
    { istrue: true, prop: 'isOutStock', label: '缺货状态', width: '75', type: 'html', formatter: (row) => formatIsOutStock(row.isOutStock) },
    { istrue: true, prop: 'createdTime', label: '创建时间', width: '100', sortable: 'custom', },
    // {type:'button', width:'55',btnList:[{label:"编辑",display:(row)=>{return row.isHandle==true;},handle:(that,row)=>that.onHand(row)}]},
    { istrue: true, prop: 'boxCount', label: '箱数', width: '55' },
    { istrue: true, prop: 'planArrivalTime', label: '预计到货日期', width: '110', sortable: 'custom', formatter: (row) => formatTime(row.planArrivalTime, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'companyName', label: '物流公司', width: '80' },
    //{istrue:true,prop:'expressNo',label:'物流单号', width:'80'},
    { istrue: true, prop: 'expressNo', label: '物流单号', width: '80', type: 'html', formatter: (row) => formatNoLink(row.expressNo) },
    { istrue: true, prop: 'isAllot', label: '是否调拨', width: '80', formatter: (row) => row.isAllot == 1 ? "调拨" : "正常" },
    { istrue: true, prop: 'reMark', label: '备注', width: '160', type: 'editor' },
];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
    //{ label: "新增", handle: (that) => that.openAdd(), permission: "api:inventory:purchase:SavePurchaseManualAsync" },
    { label: "导出", throttle: 3000, handle: (that) => that.onExport() },
    { label: "查看历史", throttle: 3000, handle: (that) => that.onShowHistory() },
    { label: "复制单据", throttle: 3000, handle: (that) => that.onCopeAddPurchaseOrder(), permission: "api:Inventory:purchase:TeskCopeAddPurchaseOrderAsync" },
    { label: "作废", throttle: 3000, handle: (that) => that.onDeletePurchase() },
    { label: "提交钉钉审批流程", throttle: 3000, handle: (that) => that.ontaskPurchaseUpload(0), permission: "api:Inventory:PurchaseOrderNew:TaskPurchaseDingDingAsync" },
];
export default {
    name: "Users",
    components: { PurchaseOrderLog, container, cesTable, vxetablebase, MyConfirmButton, logistics, goodscoderecord, goodschoice, purchasehistorytjanalysis, ElImageViewer, YhImgUpload, uploadimgFile, aauploadimgFile, OrderView1688, wmsSet, changePrice1688, addOrEditProps, supplierLog },
    props: {
        filter: {},
        ischoice: { type: Boolean, default: true },
        options1: []
    },
    data() {
        return {
            purchaseManage: false,
            generateInfo: {},
            addOrEditPropsDrawer: false,
            addOrEditPropsDrawersumbit: true,
            orEditloading: false,
            isEdit: true,
            auditProps: [],
            isView: false,
            isReInitiated: false,
            tabList: [],
            addactiveName: 'first0',
            that: this,
            loading: false,
            formatWarehouseArea: formatWarehouseArea,
            formatYesornoBool: formatYesornoBool,
            formatTime: formatTime,
            formatIsOutStock: formatIsOutStock,
            formatSecondToHour: formatSecondToHour,
            formatSecondNewToHour: formatSecondNewToHour,
            // filter: {
            //   timerangecg:null,
            //   timerangesh:null,
            //   startPurchaseDate:null,
            //   endPurchaseDate:null,
            //   startCheckDate:null,
            //   endCheckDate:null,
            //   buyNo:null,
            //   supplier:null,
            //   goodsCode:null,
            //   brandId:null,
            //   checker:null,
            //   status:"已确认",
            //   receivStatus:null,
            //   isError:null,
            //   isOutStock:null
            // },
            supplierFromInfo: {
              visible: false,
              data: {},
            },
            procurementAllPlan: { indexNo: null, goodsCode: null, count: 0, offTime: null },
            goodscoderecordfilter: { goodsCode: "", buyNo: "" },
            imgPreview: { img: "", show: false },
            lastUpdateTime: "",
            buyNoMessage: '',
            buyNoSuccessMessage: '',
            brandlist: [],
            list: [],
            selectarray: [],
            detaillist: [],
            rowslist: [],
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            oderDetailView: {},
            drawer: false,
            drawervisible: false,
            dialoganalysisVisible: false,
            visiblepopover: false,
            prevTarget: null, // 编辑 Popover 的 Reference （参照），用于 popover.js 对齐两个元素
            popperFlag: false, // 用于编辑 Popover 的刷新
            visiblepopoverdetail: false,
            dialogOrderDetailVisible: false,
            popperFlagdetail: false,
            messagedialogVisible: false,
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "createdTime", IsAsc: false },
            summaryarry: {},
            total: 0,
            sels: [],
            selids: [],
            rows: [],
            selindexNos: [],
            selRowList: [],
            fileList: [],
            imgList: [],
            pageSupplierAllList: [],
            isshowopenofflog: false,
            isshowopenofsb: false,
            listLoading: false,
            dialogVisible: false,
            pageLoading: false,
            editVisible: false,
            editLoading: false,
            uploadLoading: false,
            hackReset: false,
            onFinishLoading: false,
            ontaskPurchaseUploadLoading: false,
            goodscoderecord1id: +new Date(),
            autoform: {
                fApi: {},
                options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
                rule: [],
                data: {}
            },
            addEditTitle: "",
            goodschoiceVisible: false,
            dialogAddVisible: false,
            dialogAdding: false,
            showGoodsImage: false,
            isshowoldPrice: false,
            isshowoldoldPrice: false,
            imagedefault: require('@/assets/images/detault.jpeg'),
            //warehouseList: warehouselist,//仓库下拉
            warehouseList: [],//仓库下拉
            supplierLoading: false,
            supplierOptions: [],
            brandLoading: false,
            brandOptions: [],
            grouplist: [],
            editQRFromRules: {
                planArrivalTime: [{ required: true, message: '请选择到货日期', trigger: 'blur' }],
            },
            //新增编辑界面数据
            addForm: {
                id: 0,
                indexNo: 0,
                warehouse: null,
                purchaseDate: null,
                supplier_id: null,
                supplier: '',
                brandId: null,
                brandName: '',
                dtlGoods: [],
                reMark: null,
                purImageUrl: null,
                startDept: '采购正常进货',
                payWay: '阿里巴巴',
                payReMark: null,
                payment: null,
                orderNo: null,
                payAccount: null,
                groupId: '2',
                isAllot: true,
                isRef: true,
                isUrgent: 0,
                isAllotSelect: false,
                payType: null,
                payTypeImageUrl: null,
                paySupplierAccount: null,//支付供应商账号
                paySupplierAccountName: null,//支付供应商账号名称
                paySupplierBankType: null,//支付供应商银行类型
                paySupplierPublicType: null,//支付供应商对公对私
                paySupplierBankAccountId: null,//支付供应商银行账号ID
                taxPayType: null,//税金支付方式
                taxPaySupplierAccount: null,//税金支付供应商账号
                taxPaySupplierAccountName: null,//税金支付供应商账号名称
                taxPaySupplierBankType: null,//税金支付供应商银行类型
                taxPaySupplierPublicType: null,//税金支付供应商对公对私
                taxPaySupplierBankAccountId: null,//支付供应商银行账号ID
                taxRate: null,//税率
                taxAmount: null,//税金金额
                supplierIsOfPublicPay:null,
                isOpenInvoice:null,
                invoiceType:null,
                supplierTaxRate:undefined,
                isProxyShipping: null, //是否能代发
                printOrderPlatform: '',
                isProSelGoods: '' //是否为选品中心的货物
                // shq: [] //省市区
            },
            aliOrderInfo: {
                IsShow: false,
                IndexNo: '',
                AliOrderNo: '',
                CgAccountName: ''
            },
            addFormRules: {
                startDept: [{ required: true, message: '请输入发起部门', trigger: 'blur' }],
                warehouse: [{ required: true, message: '请输入仓库', trigger: 'blur' }],
                supplier_id: [{ required: true, message: '请输入供应商', trigger: 'blur' }],
                payWay: [{ required: true, message: '请输入支付方式', trigger: 'blur' }],
                payAccount: [{ required: true, message: '请输入账号', trigger: 'change' }],
                //payReMark: [{ required: true, message: '请输入钉钉备注', trigger: 'blur' }],
                purImageUrl: [],
                groupId: [{ required: true, message: '请选择审批运营组', trigger: 'blur' }],
                payment: [{ required: true, message: '请选择支付方式', trigger: 'blur' }],
                orderNo: [{ required: true, message: '请输入订单编号', trigger: 'blur' }, { pattern: /^\d+$/, message: '请输入正确的数字格式', trigger: 'blur' }],
                taxPayType: [{ required: true, message: '请选择税费付款方式', trigger: 'blur' }],
                taxPaySupplierBankAccountId: [{ required: true, message: '请选择税费账号', trigger: 'blur' }],
                supplierIsOfPublicPay: [{ required: true, message: '请选择是否能对公', trigger: 'change' }],
                isOpenInvoice: [{ required: true, message: '请选择是否能开票', trigger: 'change' }],
                invoiceType: [{ required: true, message: '请选择发票类型', trigger: 'change' }],
                supplierTaxRate: [{ required: true, message: '请输入对公税点', trigger: 'blur' }],
                // provinceCityDistrict: [{ required: true, message: '请选择发货地', trigger: 'blur' }],
            },
            addAccountRules: {
                publicPayType: [{ required: true, message: '请选择对公/对私', trigger: 'change' }],
                accountName: [{ required: true, message: '请输入银行账户', trigger: 'blur' }],
                account: [{ required: true, message: '请输入银行账号', trigger: 'blur' }],
                bankType: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
            },
            procurementAllPlanRules: {
                offTime: [{ type: 'string', required: true, message: '请选择日期', trigger: 'change' }],
            },

            dtlGoodsList: [],
            dialogEditDetailVisible: false,//打开编辑窗口
            dialogoffTimeVisible: false, //认领时间选择框
            dialogoffTimeloding: false,
            eidtList: [],
            indexNoDetailVisible: false,//ERP编号弹窗
            activeName: 'first',
            detailLogFilter: {
                indexNo: null
            },
            companyOption: [],
            addWLCompanyDialog: {
                visible: false,
                model: {
                    name: null,
                    code: null
                }
            },
            pickerOptions: {
                shortcuts: [{
                    text: '三十分钟',
                    onClick(picker) {
                        const date = new Date();
                        date.setMinutes(date.getMinutes() + 30);
                        picker.$emit('pick', date);
                    }
                }, {
                    text: '一小时',
                    onClick(picker) {
                        const date = new Date();
                        date.setMinutes(date.getMinutes() + 60);
                        picker.$emit('pick', date);
                    }
                }, {
                    text: '两小时',
                    onClick(picker) {
                        const date = new Date();
                        date.setMinutes(date.getMinutes() + 120);
                        picker.$emit('pick', date);
                    }
                }]
            },
            dataVisible: false,
            dataURL: null,
            ScreenWidth: null,
            ScreenHeight: null,
            orderViewVisible: false,
            orderViewInfo: {
                supplierId: '',
                wms_Co_Id: '',
                goodsCodes: ''
            },
            wmsSetVisible: false,
            tableLoading: false,
            data: {},
            changePriceVisible: false,
            changePriceInfo: {},
            editOrderVisible: false,
            restaurants: [
                {
                    value: '昀晗采购',
                    label: '昀晗采购'
                },
                {
                    value: '昀晗采购2',
                    label: '昀晗采购2'
                },
                {
                    value: '昀晗采购3',
                    label: '昀晗采购3'
                },
                {
                    value: '昀晗贸易采购',
                    label: '昀晗贸易采购'
                }
            ],
            addAccountVisible: false,
            addAccountForm: {
                publicPayType: '对公',
                accountName: '',
                account: '',
                bankType: '',
                supplierId: '',
                picture: null
            },
            paymentInfoPictureList: [],
            bankList: [],
            taxPayTypeBankList: [],
            purchaseNewPlanVisible: false,
            purchaseNewPlanLonding: false,
            newPlanList: []
        };
    },
    watch: {
        value(n) {
            if (n) {
                this.$nextTick(() => {
                    console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
                    this.$refs.table.doLayout();
                });
                this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
            }
        },
    },
    async mounted() {
        if (this.$route.query && this.$route.query.buyNo) {
            this.filter.buyNo = this.$route.query.buyNo;
            this.filter.status = "";
        }
        else if (this.$route.query && this.$route.query.goodscodes) {
            this.filter.goodsCode = this.$route.query.goodscodes;
            this.filter.status = "";
        }
        else if (this.$route.query && this.$route.query.indexNo) {
            this.filter.indexNo = this.$route.query.indexNo;
            this.filter.status = "";
        }
        else
            this.filter.status = "";

        formCreate.component('editor', FcEditor);
        await this.initform();
        await this.init();
        //await this.onSearch();
        await this.shifttoclick();



        middlevue.$on('Inventory_Purchase_1688OrderCreate', (data) => {

            if (this.addForm.indexNo && data?.IndexNo && this.addForm.indexNo == data?.IndexNo && this.dialogAddVisible) {
                this.aliOrderInfo.IsShow = true;
                this.aliOrderInfo.IndexNo = data.IndexNo;
                this.aliOrderInfo.AliOrderNo = data.AliOrderNo;
                this.aliOrderInfo.CgAccountName = data.CgAccountName;
                //this.$message.success('1688订单创建成功'+JSON.stringify(data));
            }
        })
        middlevue.$on('Inventory_Purchase_1688OrderChangePrice', (data) => {
            this.list.forEach(item => {
                if (item.indexNo == data.IndexNo) {
                    this.$set(item, 'aliChangeOrderPrice', data.AliChangeOrderPrice)
                }
            })
        })
    },
    async beforeDestroy() {
        middlevue.$off('Inventory_Purchase_1688OrderCreate')
        middlevue.$off('Inventory_Purchase_1688OrderChangePrice')
    },
    updated() {
        //alert('来了');
    },
    methods: {
        onLogMethod(row){
          if(!row.supplier_id) return this.$message.warning('无效供应商数据')
          this.supplierFromInfo.data = row
          this.supplierFromInfo.visible = true
        },
        changeInvoiceType(e){
          if(e!='是') {
            this.$set(this.addForm,'supplierIsOfPublicPay',null)
            this.$set(this.addForm,'invoiceType',null)
            this.$set(this.addForm,'supplierTaxRate',undefined)
          }
          this.$set(this.addForm,'isOpenInvoice',e)
          this.$forceUpdate()
        },
        OneClickCopy() {
            const sum = this.addForm.dtlGoods.reduce((prev, current) => {
                return decimal1(prev, (current.amount ? Number(current.amount) : 0), 4, '+')
            }, 0)
            let str = `${this.addForm.paySupplierBankAccountId ? '货款账户信息:' + this.addForm.paySupplierAccountName + ',' + this.addForm.paySupplierAccount + `,` + this.addForm.paySupplierBankType + ',' + '货款:' + sum + `。\n` : ''}${this.addForm.taxPayType ? this.addForm.taxPayType + `:` : ''}${this.addForm.taxPaySupplierBankAccountId ? this.addForm.taxPaySupplierAccountName + `,` + this.addForm.taxPaySupplierAccount + `,` + this.addForm.taxPaySupplierBankType + `,` : ''}${this.addForm.taxRate ? '税点:' + this.addForm.taxRate + `,` : ''}${this.addForm.taxAmount ? '税费:' + this.addForm.taxAmount + `。` : ''}`
            if (this.addForm.payReMark) {
                this.$confirm('当前钉钉备注有内容点击即将覆盖, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$set(this.addForm, 'payReMark', str)
                    this.$message({
                        type: 'success',
                        message: '复制成功!'
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消复制'
                    });
                });
            } else {
                this.$set(this.addForm, 'payReMark', str)
                this.$message({
                    type: 'success',
                    message: '复制成功!'
                });
            }
        },
        copyAount(val) {
            let str
            if (val == 1) {
                str = '货款:' + this.addForm.paySupplierAccount + '-' + this.addForm.paySupplierAccountName + '-' + this.addForm.paySupplierBankType + `。\n`
            } else {
                str = `${this.addForm.taxPayType}:` + this.addForm.taxPaySupplierAccount + '-' + this.addForm.taxPaySupplierAccountName + '-' + this.addForm.taxPaySupplierBankType + ',税点:' + this.addForm.taxRate + ',税费:' + this.addForm.taxAmount + `。\n`
            }
            this.$copyText(str).then(() => {
                this.$message.success('复制成功');
            }, () => {
                this.$message.error('复制失败');
            });
        },
        changeTaxPayType(e, type) {
            if (e == '税费公转') {
                const res = this.bankList.filter(item => item.publicPayType == '对公')
                this.$refs.taxPayType.resetField()
                this.$set(this, 'taxPayTypeBankList', res)
                if (!type) {
                    this.cpt()
                }
            } else if (e == '税费私转') {
                const res = this.bankList.filter(item => item.publicPayType == '对私')
                this.$set(this, 'taxPayTypeBankList', res)
                if (!type) {
                    this.cpt()
                }
            } else if (e == '货款含税费') {
                this.$refs.taxPayType.resetField()
                // this.$set(this.addForm, 'taxPayType', null)
                this.$set(this.addForm, 'taxPaySupplierAccount', null)
                this.$set(this.addForm, 'taxPaySupplierAccountName', null)
                this.$set(this.addForm, 'taxPaySupplierBankType', null)
                this.$set(this.addForm, 'taxPaySupplierPublicType', null)
                this.$set(this.addForm, 'taxPaySupplierBankAccountId', null)
                this.$set(this.addForm, 'taxRate', null)
                this.$set(this.addForm, 'taxAmount', null)
            }
            if (!type) {
                this.$set(this.addForm, 'taxPaySupplierAccount', null)
                this.$set(this.addForm, 'taxPaySupplierAccountName', null)
                this.$set(this.addForm, 'taxPaySupplierBankType', null)
                this.$set(this.addForm, 'taxPaySupplierPublicType', null)
                this.$set(this.addForm, 'taxPaySupplierBankAccountId', null)
            }
        },
        cpt() {
            console.log('2222');
            const num = this.addForm.dtlGoods.reduce((prev, current) => {
                return decimal1(prev, Number(current.amount), 4, '+')
            }, 0)
            const price = decimal1(num, decimal1((this.addForm.taxRate ? this.addForm.taxRate : 0), 100, 4, '/'), 2, '*')
            this.$set(this.addForm, 'taxAmount', price)
        },
        cptRate() {
            const num = this.addForm.dtlGoods.reduce((prev, current) => {
                return decimal1(prev, Number(current.amount), 4, '+')
            }, 0)
            const rate = decimal1(decimal1(this.addForm.taxAmount, num, 4, '/'), 100, 2, '*')
            console.log(rate, 'rate');
            this.$set(this.addForm, 'taxRate', rate)
            console.log(this.addForm.taxRate, 'this.addForm.taxRate');
        },
        changeBankType(e, val) {
            this.drawer = true;
            if (e) {
                const res = this.bankList.filter(item => item.id == e)[0]
                console.log(res, 'res');
                if (val == 1) {
                    var arr = [];
                    if(res.picture)
                        arr = JSON.parse(res.picture);

                    this.addForm.paySupplierAccount = res.account
                    this.addForm.paySupplierAccountName = res.accountName
                    this.addForm.paySupplierBankType = res.bankType
                    this.addForm.paySupplierPublicType = res.publicPayType
                    this.addForm.paySupplierBankAccountId = res.id
                    this.addForm.payAccount = res.accountName
                    this.addForm.orderNo = res.account
                    this.addForm.purImageUrl = arr;

                    console.log(this.addForm.purImageUrl, 'purImageUrl');
                } else {
                    this.addForm.taxPaySupplierAccount = res.account
                    this.addForm.taxPaySupplierAccountName = res.accountName
                    this.addForm.taxPaySupplierBankType = res.bankType
                    this.addForm.taxPaySupplierPublicType = res.publicPayType
                    this.addForm.taxPaySupplierBankAccountId = res.id
                }
                // if (this.addForm.taxPayType == '税费公转' || this.addForm.taxPayType == '税费私转') {
                //     this.addForm.payReMark = `货款账户:${res.accountName}\n货款账号:${res.account}\n开户行:${res.bankType}\n税费付款方式:${this.addForm.taxPayType}\n税点:${this.addForm.taxRate}\n税费:${this.addForm.taxAmount}`
                // } else {
                //     this.addForm.payReMark = `货款账户:${res.accountName}\n货款账号:${res.account}\n开户行:${res.bankType}\n税费付款方式:${this.addForm.taxPayType}\n`
                // }
            } else {
                if (val == 1) {
                    this.addForm.paySupplierAccount = null
                    this.addForm.paySupplierAccountName = null
                    this.addForm.paySupplierBankType = null
                    this.addForm.paySupplierPublicType = null
                    this.addForm.paySupplierBankAccountId = null
                } else {
                    this.addForm.taxPaySupplierAccount = null
                    this.addForm.taxPaySupplierAccountName = null
                    this.addForm.taxPaySupplierBankType = null
                    this.addForm.taxPaySupplierPublicType = null
                    this.addForm.taxPaySupplierBankAccountId = null
                }
            }
            this.$nextTick(() => {
                this.drawer = false;
            });
            this.$forceUpdate()
            this.cpt()
        },
        addAccountSubmit(formName) {
            if (this.addForm.supplier_id === null) return this.$message.error('请选择供应商')
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    if(this.paymentInfoPictureList)
                    this.addAccountForm.picture = JSON.stringify(this.paymentInfoPictureList);

                    const { success } = await AddSupplierAccountBank(this.addAccountForm)
                    if (success) {
                        this.$message.success('新增成功')
                        this.addAccountVisible = false
                        await this.getBankList(this.addAccountForm.supplierId)
                        this.changeTaxPayType(this.addForm.taxPayType, true)
                    }
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        addAccount() {
            this.addAccountForm = {
                publicPayType: '对公',
                accountName: '',
                account: '',
                bankType: '',
                picture: null,
                supplierId: this.addForm.supplier_id
            }
            this.paymentInfoPictureList = [];
            this.addAccountVisible = true;
        },
        handleSelect(item) {
            console.log(item);
        },
        querySearch(queryString, cb) {
            var restaurants = this.restaurants;
            var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
            // 调用 callback 返回建议列表的数据
            cb(results);
        },
        createFilter(queryString) {
            return (restaurant) => {
                return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
            };
        },
        async StartChat() {
            if (this.addForm.payWay != '阿里巴巴') return this.$message.error('支付方式不是阿里巴巴');
            if (!this.addForm.orderNo) return this.$message.error('请填写订单编号');
            if (!this.addForm.payAccount) return this.$message.error('请填写支付账号');
            if (!this.addForm.supplier_id) return this.$message.error('请选择供应商');
            this.editOrderVisible = true;
            this.$message.info('正在复制订单号,请勿操作其他,否则图片会复制失败!');
            //复制订单号
            const orderNoString = `${this.addForm.orderNo} ${'麻烦修改一下价格'}`;
            const input = document.createElement('input');
            document.body.appendChild(input);
            input.setAttribute('value', orderNoString);
            input.select();
            if (document.execCommand('copy')) {
                document.execCommand('copy');
                this.$message.success('订单号复制成功');
            } else {
                this.$message.error('订单号复制失败');
            }
            document.body.removeChild(input); // 移除 input 元素
            const params = {
                indexNo: this.addForm.indexNo,
                supplier_id: this.addForm.supplier_id,
                payAccount: this.addForm.payAccount,
                orderNo: this.addForm.orderNo,
            }
            try {
                const { data, success } = await GetAliSupperChatIdByEdit(params)
                if (success) {
                    window.open(`ali1688im:sendmsg?touid=cnalichn${data}`, '_self');
                    this.editOrderVisible = false;
                } else {
                    this.$message.error('获取聊天窗口失败');
                    this.editOrderVisible = false;
                }
            } catch (error) {
                this.editOrderVisible = false;
            }

        },
        closeDialog() {
            this.aliOrderInfo.IsShow = false;
            this.dialogAddVisible = false;
        },
        viewChangePrice(row) {
            this.changePriceInfo = {
                indexNo: row.indexNo,
            }
            this.changePriceVisible = true
        },
        changePayway(e) {
            if (e == '阿里巴巴') {
                this.addForm.payType = '对私';
                // this.addForm.payment = '包邮';
            }
            if (e !== '银行卡') {
                this.$set(this.addForm, 'paySupplierAccount', null)
                this.$set(this.addForm, 'taxPayType', null)
                this.$set(this.addForm, 'paySupplierAccountName', null)
                this.$set(this.addForm, 'paySupplierBankType', null)
                this.$set(this.addForm, 'paySupplierPublicType', null)
                this.$set(this.addForm, 'paySupplierBankAccountId', null)
                this.$set(this.addForm, 'taxPaySupplierAccount', null)
                this.$set(this.addForm, 'taxPaySupplierAccountName', null)
                this.$set(this.addForm, 'taxPaySupplierBankType', null)
                this.$set(this.addForm, 'taxPaySupplierPublicType', null)
                this.$set(this.addForm, 'taxPaySupplierBankAccountId', null)
                this.$set(this.addForm, 'taxRate', null)
                this.$set(this.addForm, 'taxAmount', null)
            }
        },
        async addCGYF() {
            this.tableLoading = true;
            const params = {
                currentPage: 1,
                pageSize: 50,
                OrderBy: 'goodsCode',
                IsAsc: true,
                goodsCode: 'CGYF',
                isCost: 1
            }
            const { success, data } = await getListSql(params)
            let res = {}
            if (success) {
                res = data.list[0]
            }
            const flag = this.addForm.dtlGoods.some(item => item.goodsCode == 'CGYF')
            if (flag) {
                this.tableLoading = false;
                return this.$message.error('已添加采购运费')
            } else {
                this.addForm.dtlGoods.unshift({
                    amount: 0.0000,
                    applyCount: undefined,
                    count: 0,
                    goodsCode: res.goodsCode,
                    goodsName: res.goodsName,
                    isAllot: 0,
                    labels: res.labels,
                    packCount: 0,
                    picture: res.picture,
                    price: 0,
                })
                this.$set(this.addForm, 'payment', '寄付')
                this.tableLoading = false;
            }
        },
        openWmsAddress() {
            this.wmsSetVisible = true;
        },
        closeOrder(data) {
            if (data) {
                this.addForm.payAccount = data.payAccount;
                this.addForm.orderNo = data.orderNo;
            }
            this.orderViewVisible = false;
            this.wmsSetVisible = false;
        },
        async OrderView() {
            if (!this.addForm.warehouse) return this.$message.error('请选择仓库');
            if (this.addForm.payWay != '阿里巴巴') return this.$message.error('支付方式不是阿里巴巴')
            if (!this.addForm.dtlGoods || this.addForm.dtlGoods.length == 0) return this.$message.error('请添加商品')
            const goodsCodes = this.addForm.dtlGoods.filter(item => item.goodsCode != 'CGBCJ-001' && item.goodsCode != 'CGYF').map(item => item.goodsCode);
            if (goodsCodes.length == 0) return this.$message.error('没有有效的商品')
            this.addForm.dtlGoods.filter(item => item.goodsCode != 'CGBCJ-001' && item.goodsCode != 'CGYF').forEach(item => {
                if (item.count == 0) {
                    this.$message.error(`采购明细${item.goodsCode}数量不能为0,请调整`)
                    throw new Error(`采购明细${item.goodsCode}数量不能为0,请调整`)
                }
            })
            if (!this.addForm.supplier_id) return this.$message.error('请选择供应商')
            this.orderViewInfo = {
                supplierId: this.addForm.supplier_id,
                wms_Co_Id: this.addForm.warehouse,
                goodsCodes,
            }
            this.editOrderVisible = true;
            const { data, success } = await PreviewCreate1688PurchaseOrder(this.orderViewInfo)
            if (!success) {
                this.editOrderVisible = false;
                return
            }
            const result = this.addForm.dtlGoods.filter(item => {
                return data.aliGoodInfos.some(item1 => item1.goodCode === item.goodsCode)
            })
            data.aliGoodInfos.forEach(item => {
                item.productImgUrl1 = JSON.stringify(item.productImgUrl ? item.productImgUrl.map(url => ({ url })) : []);
                item.mutil = (item.mutil == '' || item.mutil == null || item.mutil == 0) ? item.mutil = 1 : item.mutil;
                item.countQty = 0
                const flag = result.filter(item1 => item1.goodsCode === item.goodCode)
                if (flag.length > 0) {
                    item.qty = decimal(item.mutil, flag[0].count, '*')
                } else {
                    item.qty = 0
                }
            })
            data.orderType = 1
            data.supperId = this.addForm.supplier_id
            data.supplierName = this.addForm.supplier
            data.wareHouseName = this.addForm.warehouse ? this.warehouseList.find(item => item.wms_co_id === this.addForm.warehouse).name : ''
            data.indexNo = this.addForm.indexNo
            this.data = data
            this.editOrderVisible = false;
            this.orderViewVisible = true;
        },
        //一键复制
        doCopy4Val: function (val) {
            let that = this;
            this.$copyText(val).then(function (e) {
                that.$message({ message: val + ":内容已复制到剪切板！", type: "success" });
            }, function (e) {
                that.$message({ message: "抱歉，复制失败！", type: "warning" });
            })
        },
        callbackimg(data) {
            console.log(data, 'data');
            let _this = this;
            if (data) {
                // this.chatUrls = data
                _this.addForm.purImageUrl = _this.addForm.purImageUrl ? _this.addForm.purImageUrl : []
                let newarr = [];
                data.map((item) => {
                    newarr.push({ url: item.url, name: item.name })
                })
                _this.addForm.purImageUrl = newarr;
            }
        },
        callBackPaymentPicture(data) {
            let _that = this;
            if(data){
                _that.paymentInfoPictureList = _that.paymentInfoPictureList ? _that.paymentInfoPictureList : [];
                let newarr = [];
                data.map((item) => {
                    newarr.push({ url: item.url, name: item.fileName })
                });
                _that.paymentInfoPictureList = newarr;
            }
        },
        async changePayType(e, val) {
            this.$forceUpdate();
            // && this.addForm.payTypeImageUrl.length == 0
            if (this.addForm.payType == '对私') {
                const res = await getLastPurchaseInfoByProviderId({ providerId: this.addForm.supplier_id });
                if (!res?.success) return
                this.addForm.payTypeImageUrl = res.data ? JSON.parse(res.data) : [];
                setTimeout(() => {
                    this.$refs.payTypeImageUrl.reloadupfile(this.addForm.payTypeImageUrl);
                }, 200)
            }
            if (val == 1) {
                this.$set(this.addForm, 'paySupplierBankAccountId', null)
                this.$set(this.addForm, 'paySupplierAccount', null)
                this.$set(this.addForm, 'paySupplierAccountName', null)
                this.$set(this.addForm, 'paySupplierBankType', null)
                this.$set(this.addForm, 'paySupplierPublicType', null)
                this.$set(this.addForm,'supplierTaxRate',null)
                this.$set(this.addForm,'supplierIsOfPublicPay',undefined )
                this.$set(this.addForm,'isOpenInvoice',null )
                this.$set(this.addForm,'invoiceType',undefined )
                let newarr = []
                this.pageSupplierAllList.map(item => {
                    if (item.supplier_id == e) {
                        newarr.push(item)
                    }
                })
                if (newarr.length > 0) {
                    if (newarr[0]['province'] && newarr[0]['city'] && newarr[0]['districts']) {
                        this.$set(this.addForm, 'provinceCityDistrict', [newarr[0]['province'], newarr[0]['city'], newarr[0]['districts']])
                    } else if (newarr[0]['province'] && newarr[0]['city']) {
                        this.$set(this.addForm, 'provinceCityDistrict', [newarr[0]['province'], newarr[0]['city']])
                    } else if (newarr[0]['province']) {
                        this.$set(this.addForm, 'provinceCityDistrict', [newarr[0]['province']])
                    } else {
                        this.$set(this.addForm, 'provinceCityDistrict', [])
                    }
                    this.$set(this.addForm, 'isProxyShipping', newarr[0].isProxyShipping)
                    this.$set(this.addForm, 'printOrderPlatform', newarr[0].printOrderPlatform)
                }
                this.$nextTick(() => {
                    this.provinceCityDistrictshow = true;
                })
                const {data,success} = await GetSupplierTaxRateByProviderId({ providerId: this.addForm.supplier_id })
                if(!success) return
                this.$set(this.addForm,'supplierTaxRate',data.supplierTaxRate)
                this.$set(this.addForm,'supplierIsOfPublicPay',data.supplierIsOfPublicPay )
                this.$set(this.addForm,'isOpenInvoice',data.isOpenInvoice )
                this.$set(this.addForm,'invoiceType',data.invoiceType )
            }
            this.$set(this.addForm, 'taxPayType', null)
            this.$set(this.addForm, 'taxPaySupplierAccount', null)
            this.$set(this.addForm, 'taxPaySupplierAccountName', null)
            this.$set(this.addForm, 'taxPaySupplierBankType', null)
            this.$set(this.addForm, 'taxPaySupplierPublicType', null)
            this.$set(this.addForm, 'taxPaySupplierBankAccountId', null)
            this.$set(this.addForm, 'taxRate', null)
            this.$set(this.addForm, 'taxAmount', null)
            this.addAccountForm.supplierId = this.addForm.supplier_id
            await this.getBankList(this.addForm.supplier_id)
        },
        callbackimg1(data) {
            let _this = this;
            if (data) {
                _this.addForm.payTypeImageUrl = _this.addForm.payTypeImageUrl ? _this.addForm.payTypeImageUrl : []
                let newarr = [];
                data.map((item) => {
                    newarr.push({ url: item.url, name: item.name })
                })
                _this.addForm.payTypeImageUrl = newarr;
            }
        },
        //商品编码排序
        customSort(a, b) {
            const aCode = a.goodsCode;
            const bCode = b.goodsCode;
            if (aCode < bCode) {
                return -1;
            }
            if (aCode > bCode) {
                return 1;
            }
            return 0;
        },
        selchange() {
            this.$forceUpdate();
        },
        getSelect(targetNode) {
            if (window.getSelection) {
                //chrome等主流浏览器
                var selection = window.getSelection();
                var range = document.createRange();
                range.selectNode(targetNode);
                selection.removeAllRanges();
                selection.addRange(range);
            } else if (document.body.createTextRange) {
                //ie
                var range = document.body.createTextRange();
                range.moveToElementText(targetNode);
                range.select();
            }
        },
        imageUrlToBase64(imageUrl) {
            return new Promise((resolve, reject) => {
                let image = new Image()
                image.setAttribute('crossOrigin', 'Anonymous')
                image.src = imageUrl
                image.onload = function () {
                    const canvas = document.createElement('canvas')
                    canvas.width = image.width
                    canvas.height = image.height
                    const context = canvas.getContext('2d')
                    context.drawImage(image, 0, 0, image.width, image.height)
                    const base64Str = canvas.toDataURL('image/png')
                    resolve(base64Str)
                }
                image.onerror = function (e) {
                    reject(e)
                }
            })
        },

        parseBase64(base64) {
            let re = new RegExp('data:(?<type>.*?);base64,(?<data>.*)')
            let res = re.exec(base64)
            if (res) {
                return {
                    type: res.groups.type,
                    ext: res.groups.type.split('/').slice(-1)[0],
                    data: res.groups.data,
                }
            }
        },


        async copyImage(imageUrl, isBase64 = false) {
            let base64Url = ''
            if (!isBase64) {
                base64Url = await this.imageUrlToBase64(imageUrl)
            } else base64Url = imageUrl
            const parsedBase64 = this.parseBase64(base64Url)
            let type = parsedBase64.type
            //将base64转为Blob类型
            let bytes = atob(parsedBase64.data)
            let ab = new ArrayBuffer(bytes.length)
            let ua = new Uint8Array(ab)
            for (let i = 0; i < bytes.length; i++) {
                ua[i] = bytes.charCodeAt(i)
            }
            let blob = new Blob([ab], { type })
            navigator.clipboard.write([new ClipboardItem({ [type]: blob })])
        },
        tocreateimg() {
            let _this = this;
            _this.loading = true;
            html2canvas(this.$refs.oneboxx, {
                allowTaint: true,
                useCORS: true,
                scale: 1.5,
            }).then(async (canvas) => {
                const data = canvas.toDataURL('image/png')
                var newImg = document.createElement('img');
                newImg.src = data;
                var oldImg = document.getElementById('screen');
                if (oldImg) {
                    var parentElement = oldImg.parentNode;
                    if (parentElement) {
                        parentElement.removeChild(oldImg);
                        newImg.id = 'screen';
                        //固定图片原始宽度为1430px
                        newImg.style.width = 1280 + 'px';
                        //重置图片大小为0
                        this.ScreenWidth = 0;
                        this.ScreenHeight = 0;
                        parentElement.appendChild(newImg);
                    }
                }
                this.dataURL = data;
            })
            this.dataVisible = true;
            _this.loading = false;
        },
        downloadFileByBase64() {
            const data = this.dataURL;
            const blob = this.base64ToBlob(data);
            const url = URL.createObjectURL(blob);
            const fileName = Date.now();

            this.downloadFile(url, fileName)
        },
        base64ToBlob(data) {
            let arr = data.split(',');
            let mime = arr[0].match(/:(.*?);/)[1];
            let bStr = window.atob(arr[1]);
            let n = bStr.length;
            let u8arr = new Uint8Array(n);

            while (n--) {
                u8arr[n] = bStr.charCodeAt(n);
            }
            return new Blob([u8arr], { type: mime });
        },
        downloadFile(url, fileName) {
            const a = document.createElement("a");
            a.setAttribute("href", url)
            a.setAttribute("download", fileName)
            a.setAttribute("target", "_blank")
            let clickEvent = document.createEvent("MouseEvents");
            clickEvent.initEvent("click", true, true);
            a.dispatchEvent(clickEvent);
            this.loading = false;
        },
        customRowStyle(data) {
            if (this.rowslist?.length > 0) {
                let a = {};
                for (var i = 0; i < this.rowslist.length; i++) {
                    if (data.row == this.rowslist[i]) {
                        a.backgroundColor = 'rgb(255,253,228)';
                        return a;
                    }
                }
            }
            // console.log("航样式",data)
        },
        async onSaveOrderDetail() {
            var that = this;
            if (this.eidtList.length < 1) {
                this.$message({ type: 'error', message: '采购明细至少包含一条信息' });
                return;
            } else if (this.status != '待审核') {
                this.$message({ type: 'error', message: '只能编辑状态为待审核的采购单' });
                return;
            }
            //保存
            this.$confirm('确定保存吗, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                .then(() => {
                    batchUpdatePurchaseOrderAsync({ buyNo: that.buyNo, Details: that.eidtList })
                        .then(function (res) {
                            if (res?.success) {
                                that.$message({ type: 'success', message: '保存成功' });
                                that.getlist();
                                that.dialogEditDetailVisible = false
                            }
                        })
                        .catch(function (err) { console.error(err); });
                }).catch(() => {
                    that.$message({ type: 'info', message: '已取消' });
                });
        },
        async initform() {
            let that = this;
            this.autoform.rule = [{ type: 'hidden', field: 'id', title: 'id', value: '' },
            { type: 'editor', field: 'reMark', title: '备注', value: '', col: { span: 24 }, props: { maxlength: 400, init: async (editor) => { await that.initeditor(editor) } } }
            ]
        },
        async removeEditPopoverListener(flag) {  // 监听滚动，用于编辑框的滚动移除
            let timer = setTimeout(() => {
                let scrollElement = this.$refs.table.$el.querySelector('.el-table__body-wrapper');
                console.log('监听滚动，用于编辑框的滚动移除', flag, scrollElement);
                let scrollHandle = () => {
                    console.log('执行--->', this.visibleEditOpinions);
                    if (this.visibleEditOpinions) {
                        this.clearEditPopperComponent();
                    }
                }
                if (flag) {
                    // 滚动节流
                    scrollElement.addEventListener('scroll', throttle(500, scrollHandle));
                } else {
                    scrollElement.removeEventListener('scroll', scrollHandle);
                }
                clearTimeout(timer);
            }, 0);
        },
        async initeditor(editor) {
            editor.config.uploadImgMaxSize = 3 * 1024 * 1024
            editor.config.excludeMenus = ['emoticon', 'video']
            editor.config.uploadImgAccept = []
            editor.config.customUploadImg = async function (resultFiles, insertImgFn) {
                // console.log('resultFiles', resultFiles)
                // const form = new FormData();
                // form.append("image", resultFiles[0]);
                // const res = await upLoadImage(form);
                // var url = `${res.data}`
                // console.log('url', url)
                // insertImgFn(url)

                var xhr = new XMLHttpRequest()
                var formData = new FormData()
                formData.append('file', resultFiles[0])
                xhr.open('post', '/api/uploadnew/file/UploadCommonFileAsync')
                xhr.withCredentials = true
                xhr.responseType = 'json'
                xhr.send(formData)
                xhr.onreadystatechange = () => {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                        console.log('url', xhr.response.data.url)
                        insertImgFn(xhr.response.data.url)
                    }
                }
            }
        },
        async changeSelection(row) {
            this.selectData = row;
            console.log('复选框选中的数据', this.selectData);
            this.seqs = this.selectData.map((el) => { return el.seq; }).toString();
            console.log('seqs---->', this.seqs);
        },
        // 清空编辑组件
        async clearEditPopperComponent() {
            this.prevTarget = null;
            this.popperFlag = !this.popperFlag;
            this.popperFlagdetail = !this.popperFlagdetail;
            this.visiblepopover = false;
            this.visiblepopoverdetail = false;
        },
        async init() {
            var res1 = await getDirectorGroupList();
            this.grouplist = res1.data.map(item => {
                return { value: item.key, label: item.value };
            });

            var res2 = await getAllProBrand();
            this.brandlist = res2.data.map(item => {
                return { value: item.key, label: item.value };
            });

            var res3 = await getLastUpdateTimeyPurchase();
            this.lastUpdateTime = "最近更新时间:" + res3.data

            var res4 = await getAllWarehouse();
            var warehouselist1 = [];
            res4.data.map((item) => {
                if (item.name.indexOf('代发') == -1) {
                    warehouselist1.push(item)
                }
            })
            this.warehouseList = warehouselist1;
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        getLastElements(arr) {
            return arr.map(subArr => subArr[subArr.length - 1]);
        },
        async getlist() {

            this.listLoading = true
            if (!this.pager.OrderBy) this.pager.OrderBy = "";
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }
            if (params.timerangecg) {
                params.startPurchaseDate = params.timerangecg[0];
                params.endPurchaseDate = params.timerangecg[1];
            }
            if (params.timerangesh) {
                params.startCheckDate = params.timerangesh[0];
                params.endCheckDate = params.timerangesh[1];
            }
            if (params.timerangerk) {
                params.startrkDate = params.timerangerk[0];
                params.endrkDate = params.timerangerk[1];
            }


            params.provinceCityDistrict = params.provinceCityDistrict ? this.getLastElements(params.provinceCityDistrict).join(',') : null;

            const res = await pagePurchaseOrder(params)
            if (!res?.success) return
            this.total = res.data.total
            const data = res.data.list
            data.forEach(d => { d._loading = false })
            this.list = data
            this.summaryarry = res.data.summary;
            this.$nextTick(() => {
                this.listLoading = false
            })
        },
        async cellclick({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {
            if (column == undefined || !column || !column.property) return;
            if (column.property == 'expressNo' && row.expressNo)
                await this.showlogistics(row.companyCode, row.expressNo);
            else if (column.property == 'planArrivalTime' || column.property == 'reMark' || column.property == 'companyName'
                || column.property == 'boxCount') {
                this.visiblepopover = true;
                await this.getrecordlist(row.buyNo)
            }
            else if (column.property == 'buyNo') {
                let loadingInstance = Loading.service();
                Loading.service({ fullscreen: true });
                await this.getdetaillist(row.buyNo)
                loadingInstance.close();
                this.isshowoldPrice = true;
                this.isshowoldoldPrice = false;
                if (event.stopPropagation) {
                    event.stopPropagation();
                } else if (window.event) {
                    window.event.cancelBubble = true;
                }
                let currentTarget = event.target;
                this.editData = row;
                if (this.prevTarget === currentTarget) {
                    this.visiblepopoverdetail = !this.visiblepopoverdetail;
                } else {
                    if (this.prevTarget) {
                        this.clearEditPopperComponent();
                        this.$nextTick(() => {
                            this.prevTarget = currentTarget;
                            this.visiblepopoverdetail = true;
                        });
                    } else {
                        this.prevTarget = currentTarget;
                        this.visiblepopoverdetail = true;
                    }
                }
                //}
            }
            else if (column.property == 'indexNo') {
                let loadingInstance = Loading.service();
                Loading.service({ fullscreen: true });
                await this.getdetaiindexNollist(row.indexNo);
                this.activeName = 'first';
                this.detailLogFilter.indexNo = row.indexNo;
                loadingInstance.close();
                if (event.stopPropagation) {
                    event.stopPropagation();
                } else if (window.event) {
                    window.event.cancelBubble = true;
                }
                let currentTarget = event.target;
                this.editData = row;
                if (this.prevTarget === currentTarget) {
                    this.indexNoDetailVisible = !this.indexNoDetailVisible;
                    this.$nextTick(() => {
                        this.$refs.PurchaseOrderLog.onFirstSearch();
                    });
                } else {
                    if (this.prevTarget) {
                        this.clearEditPopperComponent();
                        this.$nextTick(() => {
                            this.prevTarget = currentTarget;
                            this.indexNoDetailVisible = true;
                        });
                    } else {
                        this.prevTarget = currentTarget;
                        this.indexNoDetailVisible = true;
                    }
                    this.$nextTick(() => {
                        this.$refs.PurchaseOrderLog.onFirstSearch();
                    });
                }
            } else if (column.property == "rkps") {
                if (row.buyNo)
                    this.$router.push({ path: '/storehouse/warehousingordervidetab', query: { singBuyNo: row.buyNo } })
            }
        },
        async getdetaillist(buyno) {
            this.detaillist = [];
            const res = await queryPurchaseOrderDetail({ buyno: buyno })
            if (!(res.code == 1 && res.data)) return
            this.detaillist = res.data;
        },
        async getdetaiindexNollist(indexNo) {
            this.detaillist = [];
            const res = await queryPurchaseOrderDetailIndexNo({ indexNo: indexNo })
            if (!(res.code == 1 && res.data)) return
            this.detaillist = res.data;
        },
        async getrecordlist(buyno) {
            if (!buyno)
                buyno = 0;
            this.goodscoderecordfilter = { buyNo: buyno, goodsCode: '' };
            this.$nextTick(() => {
                this.$refs.goodscoderecord1.onSearch('', buyno);
            });
        },
        async getBankList(id) {
            const { data, success } = await GetSupplierAccountBankList({ id })
            if (success) {
                this.$set(this, 'bankList', data ? data.list : [])
                this.$set(this, 'taxPayTypeBankList', data ? data.list : [])
            }
            this.$forceUpdate();
        },
        generateclose() {
            this.isReInitiated = true
            this.isView = false
        },
        async viewAuditProps(id) {
            const { data, success } = await getApprovalRecords({ id })
            if (success) {
                this.auditProps = data
            }
        },
        async closeTabMethod(list) {
          if (this.purchaseManage) {
            this.addOrEditPropsDrawer = false;
            this.purchaseManage = false;
            return;
          }
          this.orEditloading = true;
          if (!this.addOrEditPropsDrawer) {
            this.listLoading = true;
          }
          this.addOrEditPropsDrawersumbit = false;
          // 更新 tabList 中匹配的项的 isSubmit 为 true
          const tabItem = this.tabList.find(item => item.index === list.index);
          if (tabItem) tabItem.isSubmit = true;
          this.isReInitiated = true;
          this.isView = false;
          this.addactiveName = 'first0';
          // 过滤出 isSubmit 为 false 的项
          const unsubmittedTabs = this.tabList.filter(item => !item.isSubmit);
          // 更新 tabList 为未提交项
          this.tabList = unsubmittedTabs;
          // 设置 purchaseManage 为 true 如果 tabList 只剩一个未提交的项
          if (unsubmittedTabs.length === 1) {
            this.purchaseManage = true;
          }
          this.$forceUpdate();
          // 更新 drawer 状态
          this.addOrEditPropsDrawer = unsubmittedTabs.length > 0;
          // 延时恢复提交状态和加载状态
          setTimeout(() => {
            this.addOrEditPropsDrawersumbit = true;
            this.orEditloading = false;
            this.listLoading = false;
          }, 1000);
        },
        async handleClick(row){
          if (this.purchaseManage == true){
            this.addOrEditPropsDrawer = false;
            this.purchaseManage = false;
            return
          }
          if(row.indexNo){
            this.orEditloading = true;
            if(this.addOrEditPropsDrawer == false){
              this.listLoading = true;
            }
           this.addOrEditPropsDrawersumbit = false;
            const {data, success} = await getPurchaseOrderDetail2PriceHikeTicketAsync({indexNo:row.indexNo})
            this.listLoading = false;
            if(!success) {
              this.addOrEditPropsDrawer = false;
              this.orEditloading = false;
              return;
            }
            await this.viewAuditProps(row.id)
            this.generateInfo = JSON.parse(JSON.stringify(row))
            this.isReInitiated = true;
            this.isView = false
            this.addactiveName = 'first0';
            // this.tabList = JSON.parse(JSON.stringify(data))
            let a = false
            for (let item of data) {
              if (item.isSubmit === false) {
                a = true;
                break;
              }
            }
            // this.tabList.forEach(item => {
            //   item.items?.forEach(it => {
            //     ['applyId', 'id'].forEach(prop => {
            //       if (prop in it) {
            //         delete it[prop];
            //       }
            //     });
            //   });
            // });
            //按提交状态排序
            // this.tabList.sort((a, b) => {
            //   return a.isSubmit === b.isSubmit ? 0 : a.isSubmit ? 1 : -1;
            // });
            this.tabList = data.filter((item)=> item.isSubmit == false);
            if (this.tabList && this.tabList.length == 1){
              this.purchaseManage = true;
            }
            this.tabList.forEach((item, index) => {
              item.isSubmit = false;
              item.index = index;
            });
            this.addOrEditPropsDrawersumbit = true
            this.orEditloading = false;
            this.$forceUpdate();
            if(a){
              this.addOrEditPropsDrawer = true;
            }else{
              this.addOrEditPropsDrawer = false;
            }
          }else{
            this.$message({ message: "无Erp编号，无法生成涨价单", type: "warning" });
          }
        },
        async onHand(row) {
            this.drawer = true;
            if (row.isAdd == 1 && ((row.buyNo == null || row.buyNo == '') && (row.isJstSuccess == 0 || row.isJstSuccess == 3 || row.isJstSuccess == 4))) {//表示手动新增，导入的默认是null
                // this.$message({ message: "手动新增的采购单不允许编辑哦", type: "warning" });
                // return;
                this.addEditTitle = '编辑采购单';
                this.dialogAddVisible = true;
                this.dialogAdding = true;
                this.rows = [];
                this.rows = row;
                this.isshowopenofflog = row.informStatus == 1 || row.informStatusName == '申报' || row.informStatusName == '新品';
                this.isshowopenofsb = row.informStatusName == '申报' ? true : false;
                this.removeAddFormData();
                await this.setDetailInfo(row.indexNo);
                await this.getBankList(this.addForm.supplier_id);
                this.changeTaxPayType(this.addForm.taxPayType, true);
                this.$nextTick(() => {
                    this.drawer = false;
                });
                this.$refs.addForm.clearValidate();
                this.changeValid();
                if (this.addForm.payWay == '阿里巴巴') {
                    this.$set(this.addForm, 'payType', '对私')
                    // if (!this.addForm.payment) {
                    //     this.$set(this.addForm, 'payment', '包邮')
                    // }
                }
            }
            else {
                this.formtitle = '编辑';
                this.editVisible = true
                const res = await getPurchaseOrder({ buyno: row.buyNo, indexNo: row.indexNo })
                var arr = Object.keys(this.autoform.fApi);
                if (arr.length > 0)
                    this.autoform.fApi.resetFields()
                await this.autoform.fApi.setValue(res.data)
                this.autoform.data = res.data;
                this.companyOption = (await ruleExpressComanycode()).options;
            }
        },
        async setDetailInfo(indexNo) {
            const res = await getPurchaseOrderManual(indexNo)
            this.$nextTick(() => {
                if (res.data) {
                    this.$set(this.addForm, 'isProxyShipping', res.data.isProxyShipping);
                    this.$set(this.addForm, 'printOrderPlatform', res.data.printOrderPlatform);
                    this.$set(this.addForm, 'isProSelGoods', res.data.isProSelGoods);

                    this.addForm.id = res.data.id;
                    this.addForm.indexNo = res.data.indexNo;
                    this.addForm.warehouse = res.data.warehouse;
                    this.addForm.purchaseDate = res.data.purchaseDate;
                    this.addForm.supplier_id = res.data.supplier_id ? res.data.supplier_id : null;
                    this.addForm.supplier = res.data.supplier ? res.data.supplier : '';
                    this.addForm.brandId = res.data.brandId;
                    this.addForm.brandName = res.data.brandName;
                    this.addForm.reMark = res.data.reMark == null ? '' : res.data.reMark;
                    this.addForm.purImageUrl = res.data.purImageUrl ? JSON.parse(res.data.purImageUrl) : [];
                    this.addForm.isAllot = res.data.isAllot == null ? false : res.data.isAllot;
                    this.addForm.payWay = res.data.payWay == null ? this.addForm.payWay : res.data.payWay;;
                    this.addForm.startDept = res.data.startDept == null && this.isshowopenofsb == true ? "运营给量进货" : res.data.startDept == null ? this.addForm.startDept : res.data.startDept;
                    this.addForm.payAccount = res.data.payAccount;
                    this.addForm.payment = res.data.payment;
                    this.addForm.orderNo = res.data.orderNo;
                    this.addForm.payReMark = res.data.payReMark;
                    this.addForm.isUrgent = res.data.isUrgent;
                    this.addForm.isRef = res.data.isRef;
                    //this.addForm.groupId = res.data.groupId == null ? this.addForm.groupId : String(res.data.groupId);
                    this.addForm.groupId = res.data.groupId != null ? String(res.data.groupId) : res.data.groupId;
                    this.addForm.dtlGoods = [];
                    this.addForm.isAllotSelect = false;
                    this.addForm.refType = res.data.refType;
                    this.addForm.payType = res.data.payType;
                    this.addForm.payTypeImageUrl = res.data.payTypeImageUrl ? JSON.parse(res.data.payTypeImageUrl) : [];
                    this.addForm.paySupplierAccount = res.data.paySupplierAccount;
                    this.addForm.paySupplierAccountName = res.data.paySupplierAccountName;
                    this.addForm.paySupplierBankType = res.data.paySupplierBankType;
                    this.addForm.paySupplierPublicType = res.data.paySupplierPublicType;
                    this.addForm.paySupplierBankAccountId = res.data.paySupplierBankAccountId;
                    this.addForm.taxPaySupplierAccount = res.data.taxPaySupplierAccount;
                    this.addForm.taxPaySupplierAccountName = res.data.taxPaySupplierAccountName;
                    this.addForm.taxPaySupplierBankType = res.data.taxPaySupplierBankType;
                    this.addForm.taxPaySupplierPublicType = res.data.taxPaySupplierPublicType;
                    this.addForm.taxPaySupplierBankAccountId = res.data.taxPaySupplierBankAccountId;
                    this.addForm.taxPayType = res.data.taxPayType;
                    this.addForm.taxRate = res.data.taxRate;
                    this.addForm.taxAmount = res.data.taxAmount;
                    this.$set(this.addForm,'supplierIsOfPublicPay',res.data.supplierIsOfPublicPay)
                    this.$set(this.addForm,'supplierTaxRate',res.data.supplierTaxRate)
                    this.$set(this.addForm,'isOpenInvoice',res.data.isOpenInvoice)
                    this.$set(this.addForm,'invoiceType',res.data.invoiceType)

                    const provinceList = JSON.parse(res.data.provinceCityDistrict);
                    this.addForm.provinceCityDistrict = provinceList;

                    // this.addForm.isAllot = res.data.isAllot;
                    this.dtlGoodsList = [];
                    this.addAccountForm.supplierId = res.data.supplier_id;
                    if (res.data.dtlGoods.some(f => f.goodsName.includes('半成品'))) {
                        this.isshowopenofflog = true;
                    }
                    res.data.dtlGoods.forEach(f => {
                        this.addForm.dtlGoods.push({
                            id: f.id,
                            indexNo: f.indexNo,
                            goodsCode: f.goodsCode, goodsName: f.goodsName,
                            price: f.price, count: f.count, amount: f.amount, picture: f.picture,
                            isAllot: f.isAllot,
                            applyCount: f.applyCount,
                            applyMsg: f.applyMsg,
                            isNotified: f.isNotified,
                            labels: f.labels,
                            packCount: f.packCount,
                            packNum: f.packNum,
                            boxSpecs: f.boxSpecs,
                        });
                        this.dtlGoodsList.push({
                            goodsCode: f.goodsCode
                        })
                    });
                    this.addForm.dtlGoods.forEach(item => {
                        if (item.boxSpecs) {
                            const values = item.boxSpecs.split('/').reverse()
                            let surplusValue = item.count;
                            item.countResult = ''
                            for (const value of values) {
                                item.countResult += Math.floor(surplusValue / value) + '/'
                                surplusValue = surplusValue % value
                            }
                            item.countResult = item.countResult.substring(0, item.countResult.length - 1)
                        } else {
                            item.countResult = ''
                        }
                    })
                    //处理采购员下拉
                    this.brandOptions = [];
                    this.brandOptions.push({ value: res.data.brandId, label: res.data.brandName });
                    //处理供应商下拉
                    this.supplierOptions = [];
                    if (res.data.supplier_id) {
                        this.supplierOptions.push({ value: res.data.supplier_id, label: res.data.supplier });
                    }
                    //处理供应商下拉
                    //this.grouplist = [];
                    //this.grouplist.push({ value: res.data.groupId, label: res.data.groupName });
                    //显示窗口
                    console.log(this.addForm,'this.addForm');
                }
                this.$forceUpdate()
            })
            this.dialogAdding = false;
        },
        onDisPlay(row) {
            return row.isHandle == true;
        },
        async onEditSubmit() {
            this.editLoading = true;
            await this.autoform.fApi.validate(async (valid, fail) => {
                if (valid) {
                    const formData = await this.autoform.fApi.formData();
                    var params = { ...this.autoform.data, ...formData }
                    const res = await editPurchaseOrder(params);
                    if (res.code == 1) {
                        this.getlist();
                        this.editVisible = false;
                    }
                } else { }
            })
            this.editLoading = false;
        },
        startImport() {
            this.dialogVisible = true;
        },
        cancelImport() {
            this.dialogVisible = false;
        },
        beforeRemove() {
            return false;
        },
        uploadSuccess(response, file, fileList) {
            if (response.code == 200) {
            } else {
                fileList.splice(fileList.indexOf(file), 1);
            }
        },
        async submitUpload() {
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请先选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit = true;
            this.uploadLoading = true;
            this.$refs.upload.submit();
        },
        async uploadFile(item) {
            if (!this.fileHasSubmit) {
                return false;
            }
            this.fileHasSubmit = false;
            const form = new FormData();
            form.append("token", this.token);
            form.append("upfile", item.file);
            const res = await importPurchaseOrder(form);
            if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
            else this.$message({ message: res.msg, type: "warning" });
            this.uploadLoading = false;
        },
        async uploadChange(file, fileList) {
            if (fileList && fileList.length > 0) {
                var list = [];
                for (var i = 0; i < fileList.length; i++) {
                    if (fileList[i].status == "success")
                        list.push(fileList[i]);
                    else
                        list.push(fileList[i].raw);
                }
                this.fileList = list;
            }
        },
        uploadRemove(file, fileList) {
            this.uploadChange(file, fileList);
        },
        async onExport() {
            if (this.onExporting) return;
            try {
                const params = { ...this.pager, ... this.filter }
                if (params.timerangecg) {
                    params.startPurchaseDate = params.timerangecg[0];
                    params.endPurchaseDate = params.timerangecg[1];
                }
                if (params.timerangesh) {
                    params.startCheckDate = params.timerangesh[0];
                    params.endCheckDate = params.timerangesh[1];
                }
                if (params.timerangerk) {
                    params.startrkDate = params.timerangerk[0];
                    params.endrkDate = params.timerangerk[1];
                }
                params.provinceCityDistrict = params.provinceCityDistrict ? this.getLastElements(params.provinceCityDistrict).join(',') : null;
                var res = await exportPurchaseOrder(params);
                if (!res?.data) return
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '采购单导出_' + new Date().toLocaleString() + '.xlsx')
                aLink.click()
            } catch (err) {
                console.log(err)
                console.log(err.message);
            }
            this.onExporting = false;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selsChange: function (sels) {
            this.sels = sels
        },
        selectchange: function (rows, row) {
            this.rowslist = rows;
            // console.log("打印row数据",[row,rows])
            this.selRowList = rows;
            this.selids = [];
            this.selindexNos = [];
            rows.forEach(f => {
                this.selids.push(f.proBianMa);
                this.selindexNos.push(f);
            })
            let _this = this;
            if (rows.length > 0) {
                var a = [];
                rows.forEach(element => {
                    let b = _this.list.indexOf(element);
                    a.push(b + 1);
                });

                let d = _this.list.indexOf(row);

                var b = Math.min(...a)
                var c = Math.max(...a)

                a.push(d);
                if (d < b) {
                    var b = _this.list.indexOf(row);
                    var c = Math.max(...a)
                } else if (d > c) {
                    var b = Math.min(...a) - 1
                    var c = Math.max(...a)
                } else {
                    var b = Math.min(...a) - 1
                    var c = _this.list.indexOf(row) + 1;
                }

                let neww = [b, c];
                _this.selectarray = neww;
            }
        },
        callback(val) {
            this.selRowList = [...val];
            this.tablelist = [];
            this.tablelist = val;

            this.selindexNos = [];
            val.forEach(f => {
                this.selids.push(f.proBianMa);
                this.selindexNos.push(f);
            })
        },
        shifttoclick() {
            var _this = this;
            _this.selids = [];
            _this.goodsCodes = [];
            document.onkeydown = function (e) {
                let key = window.event.keyCode;
                if (key == 16) {
                    // console.log("shuju",_this.selectarray);
                    window.event.preventDefault()
                    if (_this.list && _this.selectarray) {
                        _this.$refs.table.clearSelection();
                        setTimeout(() => {
                            for (var i = _this.selectarray[0]; i < _this.selectarray[1]; i++) {
                                _this.$refs.table.toggleRowSelection(_this.list[i]);
                                // _this.selids.push(_this.list[i]);
                                // _this.goodsCodes.push(_this.list[i].goodsCode);
                                _this.selids.push(_this.list[i].proBianMa);
                                _this.selindexNos.push(_this.list[i]);
                            }
                        }, 100);
                    }
                }
            };

        },
        doCopy: function (val) {
            let that = this;
            this.$copyText(val).then(function (e) {
                that.$message({ message: "内容已复制到剪切板！", type: "success" });
            }, function (e) {
                that.$message({ message: "抱歉，复制失败！", type: "warning" });
            })
        },
        async onSyncBuyNos() {
            if (this.selindexNos == null || this.selindexNos.length <= 0) {
                this.$message.warning("请先选择数据！！！");
                return false;
            }
            let buyNoStr = '';

            this.selindexNos.forEach(f => {
                if (f.buyNo) {
                    if (buyNoStr)
                        buyNoStr = buyNoStr + ',' + f.buyNo;
                    else
                        buyNoStr = f.buyNo;
                }
            })

            if (!buyNoStr) {
                this.$message.warning("请先选择有聚水潭采购单号的数据！！！");
                return false;
            }

            let rlt = await SyncPurchaseOrder4BuyNos({ buyNos: buyNoStr });

            if (rlt && rlt.success)
                this.$message.success("已提交，正在同步中,10分钟后再来刷新查看，如有疑问请与IT部门人员联系！");


        },
        async onReCheckOrderStatus() {

            if (this.selindexNos == null || this.selindexNos.length <= 0) {
                this.$message.warning("请先选择数据！！！");
                return false;
            }
            let indexNos = '';

            this.selindexNos.forEach(f => {
                if (f.indexNo) {
                    if (indexNos)
                        indexNos = indexNos + ',' + f.indexNo;
                    else
                        indexNos = f.indexNo;
                }
            });

            if (!indexNos) {
                this.$message.warning("请先选择要处理的数据！！！");
                return false;
            }

            let rlt = await ReCheckOrderStatus({ indexNos: indexNos });

            if (rlt && rlt.success) {
                this.$message.success("同步成功，如果状态刷新后没有变化，请联系IT部门人员处理！");
            }
        },
        async OnTerminateNoApplyPurchaseOrder()
        {
            if (this.selindexNos == null || this.selindexNos.length <= 0) {
                this.$message.warning("请先选择数据！！！");
                return false;
            }
            let indexNos = '';
            var isError = 0;
            this.selindexNos.forEach(f => {
                if(f.isJstSuccess != 1)
                {
                    isError =1;
                    return;
                }
                if (f.indexNo) {
                    if (indexNos)
                        indexNos = indexNos + ',' + f.indexNo;
                    else
                        indexNos = f.indexNo;
                }
            });
            if (isError == 1) {
                this.$message.warning("请选择审批中的数据！！！");
                return false;
            }
            if (!indexNos) {
                this.$message.warning("请先选择要处理的数据！！！");
                return false;
            }
            this.$prompt('请先确认钉钉未收到对应采购单流程，输入“已确认”三个字后继续！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputValidator:(value)=>{
                    if (value=='已确认'){
                        return true;
                    } else {
                        return '请输入“已确认”三个字后继续！';
                    }
                }
            }).then(async ({ value }) => {
                let rlt = await TerminateNoApplyPurchaseOrder({ indexNos: indexNos });
                if (rlt && rlt.success) {
                    this.$message.success("同步成功，如果状态刷新后没有变化，请联系IT部门人员处理！");
                }
            }).catch(() => {  });
        },
        showImg(e) {
            if (e.target.tagName == 'IMG') {
                this.imgPreview.img = e.target.src
                this.imgPreview.show = true
            }
        },
        async showlogistics(companycode, number) {
            this.drawervisible = true;
            this.$refs.logistics.showlogistics(companycode, number);
        },
        async onShowHistory() {
            this.dialoganalysisVisible = true;
            this.$nextTick(() => {
                this.$refs.purchasehistorytjanalysis.onSearch();
            });
        },
        //打开新增页
        async openAdd() {
            this.addEditTitle = "新增采购单";
            //清空人员下拉
            this.brandOptions = [];
            //清空供应商下拉
            this.supplierOptions = [];
            //清空表单数据
            this.removeAddFormData();
            var userInfo = await getCurBianMaBrand();
            if (userInfo && userInfo.data && !this.addForm.brandId) {
                this.addForm.brandId = userInfo.id;
                this.addForm.brandName = userInfo.brandName;

                this.brandOptions.push({ value: userInfo.id, label: userInfo.brandName })
            }
            this.dialogAddVisible = true;
            this.dialogAdding = true;
            this.$refs.addForm.resetFields();
        },
        //清空新增表单数据
        removeAddFormData() {
            this.addForm = {
                id: 0,
                indexNo: 0,
                warehouse: null,
                purchaseDate: null,
                supplier_id: null,
                supplier: '',
                brandId: null,
                brandName: '',
                dtlGoods: [],
                reMark: '',
                purImageUrl: null,
                startDept: '采购正常进货',
                payWay: '阿里巴巴',
                payType: '对私',
                payReMark: null,
                payAccount: null,
                payment: null,
                orderNo: null,
                groupId: '2',
                isUrgent: 0,
                paySupplierAccount: null,//支付供应商账号
                paySupplierAccountName: null,//支付供应商账号名称
                paySupplierBankType: null,//支付供应商银行类型
                paySupplierPublicType: null,//支付供应商对公对私
                taxPayType: null,//税金支付方式
                taxPaySupplierAccount: null,//税金支付供应商账号
                taxPaySupplierAccountName: null,//税金支付供应商账号名称
                taxPaySupplierBankType: null,//税金支付供应商银行类型
                taxPaySupplierPublicType: null,//税金支付供应商对公对私
                taxRate: null,//税率
                taxAmount: null,//税金金额
                paySupplierBankAccountId: null,//支付供应商银行账号id
                taxPaySupplierBankAccountId: null,//支付供应商银行账号id
            }
        },
        //绑定供应商选择
        async remoteSearchSupplier(parm) {
            this.supplierOptions = [];
            if (!parm) {
                return;
            }
            var options = [];
            const res = await pageSupplierAll({ currentPage: 1, pageSize: 50, name: parm });
            this.pageSupplierAllList = res?.data?.list;
            res?.data?.list.forEach(f => {
                options.push({ value: f.supplier_id, label: f.name })
            });
            this.supplierOptions = options;
        },
        //绑定采购员选择
        async remoteSearchBrand(parm) {
            this.brandOptions = [];
            if (!parm) {
                return;
            }
            var dynamicFilter = { field: 'brandName', operator: 'Contains', value: parm }
            var options = [];
            const res = await getAllBianMaBrandByFilter({ currentPage: 1, pageSize: 50, dynamicFilter: dynamicFilter });
            res?.data?.forEach(f => {
                options.push({ value: f.id, label: f.brandName })
            });
            this.brandOptions = options;
        },
        //添加商品
        async onSelctCp() {
            this.goodschoiceVisible = true;
            await this.$refs.goodschoice.removeSelData();
        },
        //选择商品确定按钮
        async onQueren() {
            let form = this.addForm;
            var choicelist = await this.$refs.goodschoice.getchoicelist();
            if (choicelist && choicelist.length > 0) {
                // var maxGroup = choicelist.reduce(function(prev, current) {
                //         return (prev.groupId > current.groupId) ? prev : current;
                //     });
                //     this.addForm.groupId = String(maxGroup?.groupId ?? 0);
                //如果为可编辑的话
                if (this.dialogEditDetailVisible) {
                    //反填数据,
                    if (this.eidtList) {
                        //已存在的不添加
                        var temp = this.eidtList;
                        var isNew = true;
                        choicelist.forEach(f => {
                            isNew = true;
                            temp.forEach(old => {
                                if (old.goodsCode == f.goodsCode) {
                                    isNew = false;
                                }
                            });
                            if (isNew) {
                                this.eidtList.push({
                                    goodsCode: f.goodsCode,
                                    goodsName: f.goodsName,
                                    count: 0,
                                    inCount: 0,
                                    warehouse: this.eidtList[0]?.warehouse,
                                    price: (f.costPrice == null ? 0 : f.costPrice),
                                    picture: f.picture,
                                    labels: f.labels ?? '',
                                    packCount: f.packCount,
                                    isAllot: form.isAllotSelect
                                });
                            } else {
                                this.eidtList.forEach(told => {
                                    if (told.goodsCode == f.goodsCode) {
                                        told.goodsName = f.goodsName;
                                        told.picture = f.picture;
                                        told.labels = f.labels ?? '';
                                        told.packCount = f.packCount;
                                    }
                                });
                            }
                        });
                    }
                } else {
                    //反填数据,
                    if (this.addForm.dtlGoods) {
                        //已存在的不添加
                        var temp = this.addForm.dtlGoods;
                        var isNew = true;
                        //保持插入的顺序与勾选的顺序一样
                        choicelist.reverse().forEach(f => {
                            isNew = true;
                            temp.forEach(old => {
                                if (old.goodsCode == f.goodsCode) {
                                    isNew = false;
                                }
                            });
                            if (isNew) {
                                //往最前方插入
                                this.addForm.dtlGoods.unshift({
                                    goodsCode: f.goodsCode,
                                    goodsName: f.goodsName,
                                    count: 0,
                                    price: (f.costPrice == null ? 0 : f.costPrice),
                                    picture: f.picture,
                                    labels: f.labels ?? '',
                                    packCount: f.packCount,
                                    isAllot: form.isAllotSelect
                                });
                            } else {
                                this.addForm.dtlGoods.forEach(told => {
                                    if (told.goodsCode == f.goodsCode) {
                                        told.goodsName = f.goodsName;
                                        told.picture = f.picture;
                                        told.labels = f.labels ?? '';
                                        told.packCount = f.packCount;
                                        told.count = f.count;
                                    }
                                });
                            }
                        });
                    }
                }
                if (this.addForm.dtlGoods.filter(item => item.goodsCode == 'CGYF').length > 0) {
                    this.$set(this.addForm, 'payment', '寄付')
                }

                this.goodschoiceVisible = false;
            }
        },
        //新增采购单时删除行
        async onDelEditGood(rowIndex) {
            this.eidtList.splice(rowIndex, 1);
        },
        //新增采购单时删除行
        //index是实时变化的，不能用splice删除，改为用filter返回新数组
        async onDelDtlGood(goodsCode) {
            this.addForm.dtlGoods = this.addForm.dtlGoods.filter(function (good) {
                return good.goodsCode !== goodsCode;
            });
            this.cpt()
        },
        //新增采购单时提交验证
        finishFormValidate: function () {
            let isValid = false
            this.$refs.addForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        async verifyPurchaseSupplierShipmentPlaceAsync() {
            if (!this.addForm.provinceCityDistrict || this.addForm.provinceCityDistrict?.length == 0) {
                this.$message.error('请选择发货地');
                return;
            }
            const para = _.cloneDeep(this.addForm);
            let obj = {};
            obj = this.supplierOptions.find((item) => {
                return item.value === para.supplier_id; //筛选出匹配的数据
            });
            para.supplierId = para.supplier_id
            para.supplier = obj.label;
            para.provinceCityDistrict = JSON.stringify(this.addForm.provinceCityDistrict);

            var res = await verifyPurchaseSupplierShipmentPlace(para);
            if (res?.success){
                await this.onFinish();
            }else{
                this.$confirm('供应商名称省市与发货地省市不一致，是否继续?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    await this.onFinish();
                });
                // .catch((e) => {
                // });
            }
        },
        //采购自动开单查看编码对应计划采购建议数据
        async viewPurchaseNewPlan(){
            this.newPlanList = [];
            await this.getNewPlanlist();
            this.purchaseNewPlanVisible = true;
        },
        async getNewPlanlist() {
            this.listLoading = true
            var param = { indexNo: this.addForm.indexNo };
            const res = await getAutoPurchaseOrderPlan(param)
            this.listLoading = false
            if (!res?.success) return
            const data = res.data.list
            data.forEach(d => {
                d._loading = false
            })
            this.newPlanList = data;
            await this.$nextTick();
        },
        async linkpurchaseorder(indexNos) {
            if (!indexNos || indexNos == null || indexNos == undefined) {
                this.$message.warning("没有对应采购单，无法跳转！");
                return;
            }
            this.$router.push({ path: '/inventory/purchaseindex', query: { indexNo: indexNos } })
        },
        //新增采购单时提交
        async onFinish() {
            let hasError = this.addForm.dtlGoods.some(item => {
                if (item.goodsCode == 'CGYF' && this.addForm.payment != '寄付') {
                    this.$message.error('采购明细包含CGYF商品编码，则付款方式只能选择寄付');
                    return true;
                }
                if (this.addForm.payment == '寄付' && this.addForm.dtlGoods.every(item => item.goodsCode != 'CGYF')) {
                    this.$message.error('付款方式选择寄付则该页面中采购单明细-商品编码必须要添加CGYF商品编码');
                    return true;
                }
                return false;
            });
            if (hasError) {
                return;
            }
            if (!this.addForm.provinceCityDistrict || this.addForm.provinceCityDistrict?.length == 0) {
                this.$message.error('请选择发货地');
                return;
            }
            let isValid = false
            this.$refs.addForm.validate(valid => {
                isValid = valid
            })
            if (!isValid) return;
            this.onFinishLoading = true;
            const dtlRes = this.addForm.dtlGoods.filter(item => item.goodsCode !== 'CGYF' && item.goodsCode !== 'CGBCJ-001').map(item => {
                return {
                    sku_id: item.goodsCode,
                    pack_qty: item.packCount
                }
            })
            await ChangePackCount(dtlRes);
            const para = _.cloneDeep(this.addForm);
            let obj = {};
            obj = this.supplierOptions.find((item) => {
                return item.value === para.supplier_id; //筛选出匹配的数据
            });
            para.supplier = obj.label;
            para.purImageUrl = JSON.stringify(para.purImageUrl); //转换成string
            para.payTypeImageUrl = JSON.stringify(para.payTypeImageUrl);
            para.provinceCityDistrict = JSON.stringify(this.addForm.provinceCityDistrict);
            var res = await savePurchaseManual(para);
            this.onFinishLoading = false;
            if (!res?.data?.isTrue) {
                console.log('失败');
                // this.$message({
                //     message: this.$t('建立采购单失败，请刷新界面后重试!'),
                //     type: 'error'
                // })
                this.onFinishLoading = false;
                return;
            }
            var totalCount = 0;
            var totalAmont = 0;
            para.dtlGoods.forEach(f => {
                totalCount += f.count;
                totalAmont += (f.count * f.price);
            })
            console.log('total', totalCount, totalAmont)

            this.list.forEach(f => {
                if (f.indexNo == this.rows.indexNo) {
                    f.nonInCount = totalCount;
                    f.nonInAmont = totalAmont;
                    f.totalAmont = totalAmont;
                }
            })

            this.$refs.table.toggleRowSelection(this.rows);
            this.selindexNos = [];
            this.selindexNos.push(this.rows);
            this.$message({
                message: this.$t('ok!'),
                type: 'success'
            })
            //this.buyNoMessage = '成功创建采购单,erp序号为：' + res.data.indexNo;
            this.onFinishLoading = false;
            this.dialogAddVisible = false;
            this.aliOrderInfo.IsShow = false;
            //this.messagedialogVisible = true;
            this.removeAddFormData();
            await this.onSearch();
        },
        //查看明细图片
        async showImgDtl(e) {
            this.showGoodsImage = true;
            this.imgList = [];
            if (e) {
                this.imgList.push(e);
            }
            else {
                this.imgList.push(this.imagedefault);
            }
        },
        //关闭查看明细图片
        async closeFuncDtl() {
            this.showGoodsImage = false;
        },
        //明细金额计算
        async dtlCountChange(row) {
            row.amount = (row.count * row.price).toFixed(4);
            this.cpt()
        },
        async dtlPriceChange(row) {
            row.amount = (row.count * row.price).toFixed(4);
            this.cpt()
        },
        //明细金额计算
        async editCountChange(row) {
            //未入库金额
            row.nonInCount = row.count - row.inCount;
            row.nonInAmont = (row.nonInCount * row.price).toFixed(2);
            row.amount = (row.count * row.price).toFixed(2);
        },
        //复制采购单
        onCopeAddPurchaseOrder() {
            if (this.selRowList.length <= 0) {
                this.$message.error('请勾选要复制的数据');
                return;
            }
            if (this.selRowList.length > 5) {
                this.$message.error('只支持最多同时5个单据的复制，请调整');
                return;
            }
            this.selRowList.forEach(f => {
                if (f.status != '完成' && f.status != '已确认') {
                    this.$message({
                        message: this.$t('请选择状态为完成或已确认的采购单!'),
                        type: 'warning'
                    })
                    throw new Error("ending");
                }
            })
            this.$confirm('请确定是否要复制勾选单据, 一旦复制会产生新的单据，是否继续?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await CopeAddPurchaseOrder(this.selRowList);
                if (res?.success) {
                    this.$message({ type: 'success', message: '复制成功!' });
                    this.onSearch()
                } else {
                    //this.$message({ type: 'success', message: res?.msg });
                }
            }).catch((e) => {
                this.$message({ type: 'info', message: '已取消复制' });
            });
        },
        //删除采购单-只能删除手动新增的
        onDeletePurchase() {
            if (this.selRowList.length <= 0) {
                this.$message.error('请勾选要作废的数据');
                return;
            }
            this.$confirm('确认作废, 是否继续?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await deletePurchaseManual(this.selRowList);
                if (res?.success) {
                    this.$message({ type: 'success', message: '作废成功!' });
                    this.onSearch()
                } else {
                    //this.$message({ type: 'success', message: res?.msg });
                }
            }).catch((e) => {
                this.$message({ type: 'info', message: '已取消作废' });
            });
        },
        async ontaskPurchaseUpload(val) {
            if (this.selindexNos == null || this.selindexNos.length <= 0) {
                this.$message.warning("请先选择数据！！！");
                return false;
            }
            if (this.selindexNos.length > 1) {
                this.$message.warning("只能选择一条数据！！！");
                return false;
            }
            var indexNos = [];
            var informStatusName;
            this.selindexNos.forEach(f => {
                if (f.buyNo != null && f.buyNo != '') {
                    this.$message({
                        message: this.$t('请选择未同步到聚水潭的数据!'),
                        type: 'warning'
                    })
                    throw new Error("ending");
                }
                else if (f.isJstSuccess == 1) {
                    this.$message({
                        message: this.$t('已存在审批流程,不允许重复提交审批,请刷新界面后重试!'),
                        type: 'warning'
                    })
                    throw new Error("ending");
                }
                indexNos.push(f.indexNo);
                informStatusName = f.informStatusName;
            })
            var _this = this;
            this.$confirm('此操作将提交钉钉审批, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.pageLoading = true;
                var para = { indexNo: indexNos.join(), isNew: val, isInform: informStatusName == "已通知" ? true : false };
                //var res.data = await taskPurchaseUpload(para)
                var res = await taskPurchaseDingDing(para);

                if (!res?.success) {
                    // this.$message({
                    //     message: res.msg,
                    //     type: 'error'
                    // })
                    _this.pageLoading = false;
                    return;
                }
                //this.buyNoSuccessMessage = '成功创建采购单,采购单号为：' + res.data.buyNo;
                //this.ontaskPurchaseUploadLoading = true;
                this.$message({
                    message: "已提交钉钉审批流程，请注意查看钉钉",
                    type: 'success'
                })

                await _this.onSearch();
                this.list.forEach(f => {
                    if (f.indexNo == indexNos[0])
                        f.isJstSuccess = 1
                })
                _this.pageLoading = false;
                this.selindexNos = [];
                this.$refs.table.clearSelection();
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消'
                });
            });
        },
        async clickopenofflog(row) {
            this.procurementAllPlan = { indexNos: this.addForm.indexNo }
            this.dialogoffTimeVisible = true;
        },
        async clickProcurement() {
            console.log('11', this.dtlGoodsList)
            console.log('11', this.addForm.dtlGoods)

            // 获取两个数组中所有的goodsCode
            const goodsCodeList1 = this.dtlGoodsList.map(item => item.goodsCode)
            const goodsCodeList2 = this.addForm.dtlGoods.map(item => item.goodsCode)

            // 检查是否有goodsCode不同的情况
            const hasDifferentGoodsCode = goodsCodeList1.some(code => !goodsCodeList2.includes(code)) || goodsCodeList2.some(code => !goodsCodeList1.includes(code))

            if (hasDifferentGoodsCode) {
                // goodsCode有不同，返回false
                this.$message({ message: '采购单编码明细发生编码，请保存信息后在操作！', type: "warning" });
                return false
            }


            this.dialogoffTimeloding = true;
            var para = { ...this.procurementAllPlan };
            var res = await purchaseNotified(para);
            if (res?.success) {
                this.$message({ message: '发起成功，正在通知中……', type: "success" });
            } else {
                //this.$message({ message: res.msg, type: "warning" });
            }
            this.dialogoffTimeloding = false;
            this.dialogoffTimeVisible = false;
        },
        handleClose(done) {
            this.$confirm('确认关闭？')
                .then(_ => {
                    done();
                })
                .catch(_ => { });
        },
        //新增采购单时提交验证
        finishprocurementValidate: function () {
            let isValid = false
            this.$refs.procurementAllPlan.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        finisheditQRFromValidate: function () {
            let isValid = false
            this.$refs.editQRFrom.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        //字体颜色
        renderRefundStatus(row) {
            if ((row.isJstSuccess == 0 || row.isJstSuccess == 3 || row.isJstSuccess == 4 || row.isJstSuccess == 5) && row.buyNo == null) {
                return "color:red;cursor:pointer;";
            } else if (row.isJstSuccess == 1 && row.buyNo != null) {
                return "color:burlywood;cursor:pointer;";
            } else if (row.isJstSuccess == 2 && row.buyNo != null) {
                return "color:green;cursor:pointer;";
            } else return "";
        },
        renderStatus(row) {
            if (row.status == '完成')
                return "background-color: green;color: white;";
            if (row.status == '已确认')
                return "background-color: darkseagreen;color: white;";
            else if (row.status == '待审核')
                return "background-color: yellow;color: black;";
            else if (row.status == '作废')
                return "background-color: red;color: white;";
            else return "";
        },
        //为解决弹窗input框不能输入的问题
        changetextarea() {
            this.$forceUpdate()
        },
        async doCopy() {
            var buyNoList = [];
            if (!this.pager.OrderBy) this.pager.OrderBy = "";
            const params = { ... this.filter }
            if (params.timerangecg) {
                params.startPurchaseDate = params.timerangecg[0];
                params.endPurchaseDate = params.timerangecg[1];
            }
            if (params.timerangesh) {
                params.startCheckDate = params.timerangesh[0];
                params.endCheckDate = params.timerangesh[1];
            }
            if (params.timerangerk) {
                params.startrkDate = params.timerangerk[0];
                params.endrkDate = params.timerangerk[1];
            }
            var res = await pagePurchaseOrderByCopyBuyNo(params)
            res.data.forEach(f => {
                if (f != null)
                    buyNoList.push(f)
            });
            var newbuyNo = buyNoList.join();
            var _this = this;
            this.$copyText(newbuyNo).then(function (e) {
                _this.$message({ message: "内容已复制到剪切板！", type: "success" });
            }, function (e) {
                _this.$message({ message: "抱歉，复制失败！", type: "warning" });
            })
        },
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0 || index === 1 || index === 2 || index === 3) {
                    sums[index] = '';
                    return;
                }
                const values = data.map(item => Number(item[column.property]));
                if (!values.every(value => isNaN(value))) {

                    sums[index] = values.reduce((prev, curr) => {
                        const value = Number(curr);
                        if (!isNaN(value)) {
                            let sum = (prev) + (curr);
                            return sum;
                        } else {
                            return prev;
                        }
                    }, 0);
                    sums[index] = parseFloat(sums[index].toFixed(4));
                } else {
                    sums[index] = '';
                }
            });
            return sums;
        },
        async showAddWLCompanyDialog() {
            this.addWLCompanyDialog.model.name = "";
            this.addWLCompanyDialog.model.code = "";
            this.addWLCompanyDialog.visible = true;
        },
        async addWLCompany() {
            let param = {
                companyName: this.addWLCompanyDialog.model.name,
                comanyCode: this.addWLCompanyDialog.model.code
            };
            let res = await addWLCompany(param);
            if (res.success) {
                this.$message.success('添加成功！')
                //重置下拉框
                this.companyOption = (await ruleExpressComanycode()).options;
                this.addWLCompanyDialog.visible = false;
            }
        },
        allotSelect() {
            this.addForm.isAllotSelect = !this.addForm.isAllotSelect;
            this.addForm.dtlGoods.forEach((item) => {
                item.isAllot = this.addForm.isAllotSelect;
            });
            this.$forceUpdate();
        },
        //放大图片
        zoomIn() {
            var img = document.getElementById('screen');
            var currentWidth = img.clientWidth;
            var currentHeight = img.clientHeight;

            //记录图片原始大小
            this.ScreenHeight = this.ScreenHeight == 0 ? currentHeight : this.ScreenHeight;
            this.ScreenWidth = this.ScreenWidth == 0 ? currentWidth : this.ScreenWidth;

            //取最大值
            var maxHeight = Math.max(this.ScreenHeight, 9000);
            var maxWidth = Math.max(this.ScreenWidth, 9000)
            if (currentHeight < maxHeight && currentWidth < maxWidth) {
                img.style.width = (currentWidth * 1.1) + 'px'; // 每次放大1.1倍像素
                img.style.height = (currentHeight * 1.1) + 'px';
            }

        },
        //缩小图片
        zoomOut() {
            var img = document.getElementById('screen');
            var currentWidth = img.clientWidth;
            var currentHeight = img.clientHeight;

            //记录图片原始大小
            this.ScreenHeight = this.ScreenHeight == 0 ? currentHeight : this.ScreenHeight;
            this.ScreenWidth = this.ScreenWidth == 0 ? currentWidth : this.ScreenWidth;

            //取最小值
            var minHeight = Math.min(this.ScreenHeight, 200);
            var minWidth = Math.min(this.ScreenWidth, 200)
            if (currentWidth > minWidth && currentHeight > minHeight) { // 确保图片不会缩小到小于200像素
                img.style.width = (currentWidth / 1.1) + 'px'; // 每次缩小1.1倍像素
                img.style.height = (currentHeight / 1.1) + 'px';
            }
        },
        changeValid() {
            if (this.addForm.warehouse == '12732439') {
                this.addFormRules.purImageUrl = [{ required: true, message: '请上传图片', trigger: 'blur' }]
            } else {
                this.addFormRules.purImageUrl = [];
            }
        }
    },
};
</script>
<style lang="scss" scoped>
.imgDolg {
    width: 100vw;
    height: 100vh;
    position: fixed;
    z-index: 9999;
    background-color: rgba(140, 134, 134, 0.6);
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;

    #imgDolgClose {
        position: fixed;
        top: 35px;
        cursor: pointer;
        right: 7%;
        font-size: 50px;
        color: white;
    }

    img {
        width: 80%;
    }
}

::v-deep .dialogLeft {
    margin-left: 10px;
}

::v-deep .cell {
    font-size: 14px;
}


.screenDrag {
    margin-top: -12vh;

    .screenPicShow {
        overflow: scroll;
        width: 100%;
        height: 770px;
    }

    .buttonBox {
        position: absolute;
        right: 10px;
        bottom: 10px;

        .el-button {
            margin: 2px;

            .icon {
                font-size: 16px;
            }
        }

    }
}

.my-el-cascader ::v-deep .el-input__inner {
    height: 32px !important;
}

.my-el-cascader ::v-deep .el-cascader__tags {
    // height: 22px !important;
    padding-left: 10px !important;
    left: -13px !important;
    top: 45% !important;
}

::v-deep .hiddleshow{
  display: none;
}
::v-deep .el-input-number {
	.el-input__inner {
		text-align: left;
	}
	.is-without-controls .el-input__inner {
		padding-left: 7px !important;
	}
}
</style>
