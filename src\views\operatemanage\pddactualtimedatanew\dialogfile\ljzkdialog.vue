<template>
  <div style="height: 100%; width: 100%;">
    <div style="display: flex; flex-direction: row;height: 100%; width: 100%;">
      <div style="width: 120px;">
        <el-menu default-active="2" @open="handleOpen" @close="handleClose" class="el-menu-vertical-demo">
          <el-menu-item index="2" @click="seltitle('2')">
            <i class="el-icon-document"></i>
            <span slot="title">收藏数</span>
          </el-menu-item>
          <el-menu-item index="3" @click="seltitle('3')">
            <i class="el-icon-setting"></i>
            <span slot="title">体检分</span>
          </el-menu-item>
          <el-menu-item index="4" @click="seltitle('4')">
            <i class="el-icon-document"></i>
            <span slot="title">违规内容</span>
          </el-menu-item>
          <el-menu-item index="5" @click="seltitle('5')">
            <i class="el-icon-setting"></i>
            <span slot="title">发货预警</span>
          </el-menu-item>
          <!-- <el-menu-item index="6" @click="seltitle('6')">
            <i class="el-icon-menu"></i>
            <span slot="title">库存</span>
          </el-menu-item>
          <el-menu-item index="7" @click="seltitle('7')">
            <i class="el-icon-menu"></i>
            <span slot="title">采购进度</span>
          </el-menu-item> -->
        </el-menu>
      </div>
      <!-- 收藏数 -->
      <div style="width: 100%; height: 100%;" v-if="moindex == '2'">
        <div style="margin: 0 0 0 80px;">
          收藏数
        </div>
        <div style="font-size: 600; font-size: 35px;margin: 50px 0 0 80px;">
          {{ onemsg.collectCount }}
        </div>
      </div>
      <!-- 体检分 -->
      <div style="width: 100%; height: 100%; " v-else-if="moindex == '3'">
        <div style="margin: 0 0 0 80px;">
          体检分
        </div>
        <div style="font-size: 600; font-size: 35px;margin: 50px 0 0 80px;">
          {{ onemsg.experiencePoints }}
        </div>
      </div>
      <!-- 违规内容 -->
      <div style="width: 100%; height: 100%;" v-else-if="moindex == '4'">
        <vxe-table :data="twomsg.list" height="400px">
          <vxe-column type="seq" width="60"></vxe-column>
          <vxe-column field="groupName" title="运营组"></vxe-column>
          <vxe-column field="shopName" title="店铺名称"></vxe-column>
          <vxe-column field="punishInfo" title="处罚内容"></vxe-column>
          <vxe-column field="violationType" title="违规类型"></vxe-column>
          <vxe-column field="punishBeginTime" title="限制开始日期"></vxe-column>
        </vxe-table>
      </div>

      <!-- 发货预警 -->
      <div style="width: 100%; height: 100%;" v-else-if="moindex == '5'">
        <vxe-table :data="thrmsg.list" height="400px">
          <vxe-column type="seq" width="60"></vxe-column>
          <vxe-column field="plan_delivery_date" title="承诺发货时间"></vxe-column>
          <vxe-column field="orderNo " title="订单编号"></vxe-column>
        </vxe-table>
      </div>

      <!-- 库存 -->
      <!-- <div style="width: 100%; height: 100%; background-color: red;" v-else-if="moindex == '6'">
        55555
      </div> -->

      <!-- 采购进度 -->
      <!-- <div style="width: 100%; height: 100%; background-color: red;" v-else-if="moindex == '7'">
        6666666
      </div> -->
    </div>
  </div>
</template>

<script>
import { getProductSendWarningRecord, getPddActualTimeProductLinkInfo } from '@/api/operatemanage/datapdd/actualtimedatapdd.js';
import { getPingduoduoBGShopViolationList} from '@/api/operatemanage/pddbgmanage/pddbgmanage'
export default {
  name: 'Vue2demoLjzkdialog',
  props: ['rowmsg'],
  data() {
    return {
      moindex: '2',
      onemsg: {},
      twomsg: {},
      thrmsg: {},
      tableData: [
        { id: 10001, name: 'Test1', role: 'Develop', sex: 'Man', age: 28, address: 'test abc' },
        { id: 10002, name: 'Test2', role: 'Test', sex: 'Women', age: 22, address: 'Guangzhou' },
        { id: 10003, name: 'Test3', role: 'PM', sex: 'Man', age: 32, address: 'Shanghai' },
        { id: 10004, name: 'Test4', role: 'Designer', sex: 'Women', age: 24, address: 'Shanghai' }
      ],
      tableData1: [
        { id: 10001, name: 'Test1', role: 'Develop', sex: 'Man', age: 28, address: 'test abc' },
        { id: 10002, name: 'Test2', role: 'Test', sex: 'Women', age: 22, address: 'Guangzhou' },
        { id: 10003, name: 'Test3', role: 'PM', sex: 'Man', age: 32, address: 'Shanghai' },
        { id: 10004, name: 'Test4', role: 'Designer', sex: 'Women', age: 24, address: 'Shanghai' }
      ]
    };
  },
  // watch: {
  //   async 'rowmsg'(val){
  //     if(val){
  //       // 发货预警
  //       debugger
  //       this.getDeliveryWarn();
  //     }
  //   }
  // },
  // watch: {
  //   rowmsg: {
  //     handler(val) {
  //       if (val) {
  //         // 发货预警
  //         debugger
  //         this.getDeliveryWarn();
  //       }
  //     },
  //     deep: true
  //   }
  // },

  mounted() {

  },

  methods: {
    handleOpen(key, keyPath) {
      console.log(key, keyPath);
    },
    handleClose(key, keyPath) {
      console.log(key, keyPath);
    },
    seltitle(e) {
      this.moindex = e;
    },
    // 发货预警
    async getDeliveryWarn(){
      console.log("11111",this.rowmsg)
      let params = {
        productCode: this.rowmsg.proCode,
        yearMonthDay: this.rowmsg.yearMonthDay,
      }
      console.log("请求餐数",params)
      let res = await getProductSendWarningRecord(params);
      if(!res.success){
        return
      }
      this.thrmsg = res.data;
      console.log("打印数据1",res)
    },
    // 获取收藏数，评分数
    async getPddActualTimeProduct(){
      console.log("11111",this.rowmsg)
      let params = {
        productCode: this.rowmsg.proCode,
        yearMonthDay: this.rowmsg.yearMonthDay,
      }
      let res = await getPddActualTimeProductLinkInfo(params);
      if(!res.success){
        return
      }
      this.onemsg = res.data;
      console.log("打印数据2",res)
    },
    async getPingduod(){
      console.log("打印传参",this.rowmsg)

      let params = {
        pddProductId: this.rowmsg.proCode,
        pageSize: 50,
        currentPage: 1,
        OrderBy: 'punishBeginTime',
        IsAsc: false,
        isImportant: '',
        isHandel: '',
      }
      const res = await getPingduoduoBGShopViolationList(params)
      this.listLoading = false
      if (!res?.success) {
          return
      }
      this.twomsg = res.data;
      console.log("打印数据1",res)
    }
  },
};
</script>

<style lang="scss" scoped></style>