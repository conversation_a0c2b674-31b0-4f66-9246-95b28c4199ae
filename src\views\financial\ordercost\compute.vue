<template>
  <container>
       <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='false' tablekey="compute"
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' :isSelectColumn="true"
              :loading="listLoading">
          <template slot='extentbtn'>
            <el-button-group>
              <el-button style="padding: 0;margin: 0;">
                <el-select v-model="filter.platform" placeholder="平台" style="width: 100px"  @change="changePlatform" clearable :collapse-tags="true"  filterable>
                  <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>
              </el-button>
              <el-button style="padding: 0;margin: 0;">
                <el-select v-model="filter.shopCode" @change="onSearch" placeholder="店铺" style="width: 250px" :clearable="true" :collapse-tags="true"  filterable>
                  <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"/>
                </el-select>
              </el-button>             
              <el-button style="padding: 0;margin: 0;">
                <el-select v-model="filter.finished" placeholder="是否完成" style="width: 100px" :clearable="true"  @change="onSearch">
                 <el-option v-for="item in yesNoList" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>
              </el-button>       
            </el-button-group>
        </template>
       </ces-table> 
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
  </container>
</template>
<script>
import { pageOrderCostCompute, changeOrderCostComputeStatus} from '@/api/financial/ordercost'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import {formatPlatform,formatTime} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'settMonth',label:'结算月份', width:'120',sortable:'custom',formatter:(row)=>formatTime(row.registDate,'YYYY-MM-DD')},
      {istrue:true,prop:'version',label:'月报类型', width:'100',sortable:'custom',formatter:(row)=>row.versionName},
      {istrue:true,prop:'platform',label:'平台', width:'100',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
      {istrue:true,prop:'shopCode',label:'店铺', width:'250',sortable:'custom',formatter:(row)=>row.shopName},
      {istrue:true,prop:'countTotal',label:'订单数', width:'100',sortable:'custom'},
      {istrue:true,prop:'countMatched',label:'订单数匹配', width:'120',sortable:'custom'},
      {istrue:true,prop:'remark',label:'备注', width:'auto',sortable:'custom'},
      {istrue:true,prop:'finished',label:'是否完成', width:'120',sortable:'custom',type:'switch',
        isDisabled:(row,that)=>!row.finished,
        change:(row,that)=>that.changeStatus(row)},
      {istrue:true,prop:'finishedTime',label:'完成时间', width:'150',sortable:'custom',formatter:(row)=>formatTime(row.finishedTime,'YYYY-MM-DD HH:mm:ss')},
      {istrue:true,prop:'createdUserName',label:'创建人', width:'120',sortable:'custom'},
      {istrue:true,prop:'createdTime',label:'创建时间', width:'150',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD HH:mm:ss')},     
     ];
const tableHandles=[
        {label:"计算", handle:(that)=>that.onCompute()},
        {label:"刷新", handle:(that)=>that.getlist()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, container },
   props:{
       filter: { },
       shopList:[],
       platformList:[],
     },
  data() {
    return {
      shareFeeType:0,
      that:this,
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"id",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [], 
      listLoading: false,
      pageLoading: false,
      yesNoList:[
        {label:"是",value:true},
        {label:"否",value:false},
      ],
    }
  },
  mounted() {
     this.onSearch()
  },
  beforeUpdate() { },
  methods: {
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      if(!this.filter.settMonth){
        // this.$message({message: "请选择结算月份", type: "warning" });
        // return false;
      }
      var pager = this.$refs.pager.getPager()
      this.filter.shareFeeType=this.shareFeeType;
      const params = {...pager, ...this.pager, ... this.filter}
      this.listLoading = true
      const res = await pageOrderCostCompute(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async changeStatus(row) {
     this.$confirm('重置为未完成后可重新计算，是否继续?', '提示', {confirmButtonText: '是',cancelButtonText: '否',type: 'warning'
        }).then(async () => {
            var params={
              id:row.id,
              auditStatus:row.auditStatus,
            };
            var params={id:row.id};
            const res= await changeOrderCostComputeStatus(params);
            if (!res?.success) return ;
            this.$message({message:"重置成功",type:"success"});
        }).catch(() => {
           row.finished=!row.finished;
        });     
      
    },
   async onCompute(){
      this.$emit('onstartcomput',this.shareFeeType);
   },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
    changePlatform(){
      this.$emit("changePlatform",this.filter.platform);
      this.onSearch();
    }
  }
}
</script>
