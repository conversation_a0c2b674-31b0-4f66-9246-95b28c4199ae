<template>
     <!--自动分配-->
    <my-container v-loading="pageLoading">
        <template #header>
        </template>
        <vxetablebase :id="'shootingvideotaskoverCacle202301291318001'" :that='that'  height="98%"
            border ref="table" class="draggable-table" :isIndex='true' :hasexpand='false' :isSelectColumn='true'
            :tableData='tasklist' :tableCols='tableCols' tablekey="accountsWorkCount" :loading="listLoading"
            :isBorder="false" @sortchange='sortchange'>
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" width="100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn' >
                <div style="margin:10px 5px 5px 0;">
                    <span style="padding: 0;margin-right:2px;">
                        <el-input style="width:25%" v-model="filter.userName" v-model.trim="filter.userName" :maxlength=100
                            placeholder="姓名" @keyup.enter.native="onSearch" clearable />
                    </span>
                    <span style="padding: 0;margin-right:5px;">
                        <el-select style="width:25%" v-model="filter.companyName" placeholder="公司" clearable>
                            <el-option label="义乌" value="义乌"></el-option>
                            <el-option label="南昌" value="南昌"></el-option>
                        </el-select>
                    </span>
                    <!-- <span style="padding: 0;margin-right:5px;">
                        <el-select style="width:25%" v-model="filter.shortVedioIsCyFp" placeholder="是否分配" clearable>
                            <el-option label="是" :value=1></el-option>
                            <el-option label="否" :value=0></el-option>
                        </el-select>
                    </span> -->
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </div>
            </template>
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList"
                :page-size="100" />
        </template>
        <!--新增人员编辑-->
        <el-dialog :title="editformTitle" :visible.sync="editformdialog" width="60%" :close-on-click-modal="false"
            v-loading="editLoading" element-loading-text="拼命加载中" v-dialogDrag :append-to-body="true">
            <el-form :model="editform" ref="editform" label-width="120px" :rules="editformRules">
                <el-row>&nbsp;</el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item prop="userId" label="姓名">
                            <el-select v-model="editform.userId" :disabled="true" :filterable="true" :clearable="true"
                                @change="selUserInfoChange(editform.userId, index)">
                                <el-option v-for="item in userList" :key="item.id" :label="item.label"
                                    :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="6">
                        <el-form-item prop="companyName" label="公司">
                            <el-select v-model="editform.companyName" :clearable="true" :disabled="true"
                                :collapse-tags="true" filterable>
                                <el-option label="义乌" value="义乌"></el-option>
                                <el-option label="南昌" value="南昌"></el-option>
                                <el-option label="武汉" value="武汉"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="6">
                        <el-form-item prop="isCyFp" label="是否分配">
                            <el-select v-model="editform.shortVedioIsCyFp" :clearable="true" :collapse-tags="true" :filterable="true">
                                <el-option label="是" :value=1></el-option>
                                <el-option label="否" :value=0></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="fpTaskAutoRate" label="分配比例(%)">
                            <el-input-number :clearable="true" v-model="editform.fpTaskAutoRate"
                                v-model.trim="editform.shortVedioFpRate" :min="-1" :max="100" :controls="false" :precision="0"
                                placeholder="分配比例"></el-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>

        
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="editformdialog = false">关 闭</el-button>
                    <my-confirm-button type="submit" @click="onSubmit" />
                </span>
            </template>
        </el-dialog>
        <!--岗位管理-->
        <el-drawer title="岗位管理" :visible.sync="positionOrder" size="75%" :close-on-click-modal="false" direction="btt">
            <accountsWorkPostionManage ref="accountsWorkPostionManage"></accountsWorkPostionManage>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="positionOrder = false">关 闭</el-button>
                </span>
            </template>
        </el-drawer>
    </my-container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import MyContainer from "@/components/my-container";
import cesTable from "@/components/Table/tableforvedio.vue";
import accountsWorkPostionManage from '@/views/media/shooting/adjustAccounts/accountsWorkPostionManage';
import { getErpUserInfoViewforshoot, getWorkPostListAsync } from '@/api/media/mediashare';
import { addOrUpdatePersonnelPositionAsync, getPersonnelPositionAsync} from '@/api/media/shootingset'
import MyConfirmButton from "@/components/my-confirm-button";
const tableCols = [
    { istrue: true, prop: 'userName', label: '姓名', width: '100' , align: 'left'},
    { istrue: true, prop: 'companyName', label: '公司', width: '80', align: 'left' },
    { istrue: true, prop: 'workPositionStr', label: '工作岗位', width: '100', align: 'left' },
    { istrue: true, prop: 'commissionPositionStr', label: '提成岗位', width: '100', align: 'left' },
    { istrue: true, prop: 'shortVedioIsCyFp', type: 'switch', label: '参与分配', width: '100', align: 'center',formatter: (row) => {return row.shortVedioIsCyFp ==1 } , change: async (row, that) => { return await that.CyCommission(row) } },
    { istrue: true, prop: 'shortVedioFpRate', label: '分配比例', width: '100', sortable: 'custom', align: 'left', formatter: (row) => row.shortVedioFpRate + "%" },
    { istrue: true, prop: 'shortVedioFpTask', label: '分配任务数', width: '100', sortable: 'custom', align: 'left' }, 
    { istrue: true, prop: 'shortVedioFpTaskOver', label: '完成任务数', width: '100', sortable: 'custom', align: 'left' },
    { istrue: true, type: "button", label: '操作', align: 'left',
        btnList: [
            { label: "编辑", handle: (that, row) => that.onEditAdd(row) }
        ]
    }
];
export default {
    components: { MyContainer, cesTable, MyConfirmButton, accountsWorkPostionManage, vxetablebase },
    data() {
        return {
            that: this,
            pageLoading: false,
            positionOrder: false,
            summaryarry: [],
            userList: [],
            tasklist: [],
            workPositionlist: [],
            commissionPositionlist: [],
            retdata: [],
            sels: [], // 列表选中列
            tableCols: tableCols,
            listLoading: false,
            //人员编辑模块
            editLoading: false,
            editformdialog: false,
            editformTitle: null,
            editform: {
                
            },
            brandList:[],
            total: 0,
            pager: { OrderBy: "orderNum", IsAsc: true },
            filter: {
            },
            editformRules: {
            
            },
        };
    },

    async mounted() { 
        await this.getUserList(); 
        await this.onSearch();  
    },
    methods: {
 
 
        async CyCommission(row) {   
            this.editform.shortVedioIsCyFp = row.shortVedioIsCyFp? 1 : 0; 
            this.editform.shortVedioFpRate = row.shortVedioFpRate;
            this.editform.userId = row.userId.toString(); 
            this.editform.userName = row.userName;
            this.editform.positionId = row.positionId;  
            this.editform.OpenType = 3;  
            const para = _.cloneDeep(this.editform);
            var res = await addOrUpdatePersonnelPositionAsync(para);
            if (!res?.success) { return; }
            this.$message({ message: this.$t('保存成功'), type: 'success' });
        },
       
        async getUserList() {
            this.userList = await getErpUserInfoViewforshoot();
            return this.userList;
        },
        
        //打开新增窗口
        async onEditAdd(row) {
            this.editformdialog = true;
            this.editLoading = true;
            await getWorkPostListAsync();
            this.editformTitle = "编辑人员";
            this.editform.shortVedioIsCyFp = row.shortVedioIsCyFp? 1 : 0; 
            this.editform.shortVedioFpRate = row.shortVedioFpRate;
            this.editform.userId = row.userId.toString(); 
            this.editform.userName = row.userName;
            this.editform.positionId = row.positionId;  
            this.editform.companyName= row.companyName;  
            this.editform.OpenType = 3;  
            this.editLoading = false;
        },
        //提交保存时验证
        onSubmitValidate: function () {
            let isValid = true;
            this.$refs.editform.validate(valid => {
                isValid = valid
            })
            return isValid;
        },
        async onSubmit() {
            if (!this.onSubmitValidate()) {
                return;
            }
            const para = _.cloneDeep(this.editform);
            this.editLoading = true;
            var res = await addOrUpdatePersonnelPositionAsync(para);
            this.editLoading = false;
            if (!res?.success) { return; }
            this.$message({ message: this.$t('保存成功'), type: 'success' });
            this.onSearch();
            this.editformdialog = false;
        },
        async onSearch() {
            /*   this.$refs.pager.setPage(1); */
            this.getTaskList();
        },
        async getTaskList() {

            var pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            this.listLoading = true;
            const res = await getPersonnelPositionAsync(params);
            this.listLoading = false;
            this.total = res.data.total
            this.tasklist = res.data.list;
            //this.summaryarry = { videoTaskId_sum: " 0" };
        },
  
    },
};
</script>


