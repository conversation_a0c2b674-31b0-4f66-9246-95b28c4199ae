<template>
  <div class="top" v-loading="loading">
    <div class="top_head">
      <span class="top_title">优先级地区</span>
      <el-button type="text" @click="onNewLine(1)">新增一行</el-button>
    </div>
    <div>
      <el-table :data="companyList" style="width: 100%;" height="180" :show-header="false" border
        :cell-style="{ 'text-align': 'center' }">
        <el-table-column prop="priority" width="230">
        </el-table-column>
        <el-table-column width="230">
          <template slot-scope="scope">
            <el-select v-model="scope.row.company" placeholder="请选择">
              <el-option v-for="item in placeList" :key="item" :label="item" :value="item" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column>
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleDelete(scope.$index, scope.row, 1)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="top_head">
      <span class="top_title">优先级架构</span>
      <el-button type="text" @click="onNewLine(3)">新增一行</el-button>
    </div>
    <div>
      <el-table :data="deptList" style="width: 100%;" height="180" :show-header="false" border
        :cell-style="{ 'text-align': 'center' }">
        <el-table-column prop="priority" width="230">
        </el-table-column>
        <el-table-column width="230">
          <template slot-scope="scope">
            <el-select v-model="scope.row.dept" placeholder="请选择">
              <el-option v-for="item in architectureList" :key="item" :label="item" :value="item" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column>
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleDelete(scope.$index, scope.row, 3)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="top_head">
      <span class="top_title">优先级岗位</span>
      <el-button type="text" @click="onNewLine(2)">新增一行</el-button>
    </div>
    <div>
      <el-table :data="jobList" style="width: 100%;" height="180" :show-header="false" border
        :cell-style="{ 'text-align': 'center' }">
        <el-table-column prop="priority" width="230">
        </el-table-column>
        <el-table-column width="230">
          <template slot-scope="scope">
            <el-select v-model="scope.row.job" placeholder="请选择">
              <el-option v-for="item in postList" :key="item" :label="item" :value="item" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column>
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleDelete(scope.$index, scope.row, 2)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import dayjs from 'dayjs'
import { getDeductionAnalysisSortSet, saveDeductionAnalysisSortSet } from '@/api/order/orderdeductmoney.js';

export default {
  name: "goodsSetSort",
  props: {
    optionsListInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: {
    MyContainer
  },
  data() {
    return {
      jobList: [], //岗位
      deptList: [], //架构
      companyList: [],//地区
      postList: [],//岗位选择器数据
      placeList: [],//地区选择器数据
      architectureList: [],//架构选择器数据
      that: this,
      loading: false,
    }
  },
  async mounted() {
    this.postList = this.optionsListInfo.postList ? this.optionsListInfo.postList : []
    this.placeList = this.optionsListInfo.placeList ? this.optionsListInfo.placeList : []
    this.architectureList = this.optionsListInfo.architectureList ? this.optionsListInfo.architectureList : []
    await this.getList()
  },
  methods: {
    async onSave() {
      this.$confirm('是否保存?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { data, success } = await saveDeductionAnalysisSortSet({
          companyList: this.companyList,
          jobList: this.jobList,
          deptList: this.deptList
        })
        if (success) {
          this.$message.success('保存成功')
          this.$emit('sortClose')
        } else {
          this.$message.error('保存失败')
        }
      }).catch(() => {
      });
    },
    onNewLine(val) {
      const listMap = {
        1: this.companyList,
        2: this.jobList,
        3: this.deptList
      };
      const fieldMap = {
        1: 'company',
        2: 'job',
        3: 'dept'
      };
      if (listMap[val]) {
        const list = listMap[val];
        const sortValue = list.length + 1;
        list.push({
          sort: sortValue,
          priority: `优先级${sortValue}`,
          [fieldMap[val]]: null
        });
      }
    },
    handleDelete(index, row, val) {
      const listMap = {
        1: this.companyList,
        2: this.jobList,
        3: this.deptList
      };
      if (listMap[val]) {
        listMap[val].splice(index, 1);
        for (let i = index; i < listMap[val].length; i++) {
          listMap[val][i].sort = i + 1; // 更新 sort
          listMap[val][i].priority = `优先级${i + 1}`; // 更新 priority
        }
      }
    },
    async getList(type) {
      this.loading = true
      const { data, success } = await getDeductionAnalysisSortSet()
      this.loading = false
      if (success) {
        this.companyList = this.assignPriority(data.companyList);
        this.jobList = this.assignPriority(data.jobList);
        this.deptList = this.assignPriority(data.deptList);
      } else {
        this.$message.error('获取列表失败')
      }
    },
    assignPriority(list) {
      return list.map(item => {
        if (item.sort != null) {
          item.priority = `优先级${item.sort}`; // 动态拼接优先级
        }
        return item;
      });
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  height: 680px;

  .top_head {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
  }

  .top_title {
    font-size: 16px;
    font-weight: bold;
  }
}
</style>
