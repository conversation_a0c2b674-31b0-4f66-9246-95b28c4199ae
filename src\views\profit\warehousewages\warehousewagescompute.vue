<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="" style="padding: 0;margin: 0;">
                    <el-switch v-model="filter.workDateType" active-text="按日" inactive-text="按月" @change="onSearch(1)">
                    </el-switch>
                </el-form-item>
                <el-form-item label="维度复选:" style="padding: 0;margin: 0;">
                    <!-- <el-select v-model="filter.dataType" style="width: 100px" size="mini" @change="onSearch(2)">
                        <el-option label="仓库" value="仓库"></el-option>
                        <el-option label="班次" value="班次"></el-option>
                        <el-option label="一级岗位" value="一级岗位"></el-option>
                        <el-option label="二级岗位" value="二级岗位"></el-option>
                        <el-option label="真实姓名" value="真实姓名"></el-option>
                    </el-select> -->
                    <el-checkbox v-model="filter.isWare" style="margin-right: 5px;">仓库</el-checkbox>
                    <el-checkbox v-model="filter.isWorkType" style="margin-right: 5px;">班次</el-checkbox>
                    <el-checkbox v-model="filter.isOnePost" style="margin-right: 5px;">一级岗位</el-checkbox>
                    <el-checkbox v-model="filter.isTwoPost" style="margin-right: 5px;">二级岗位</el-checkbox>
                    <el-checkbox v-model="filter.isUserName" style="margin-right: 5px;">真实姓名</el-checkbox>
                </el-form-item>
                <el-form-item label="" style="padding: 0;margin: 0;">
                    <el-select v-model="filter.warehouseCode" style="width: 160px" size="mini" @change="onSearch" clearable>
                        <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="" style="padding: 0;margin: 0;">
                    <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        :picker-options="pickerOptions">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="" style="padding: 0;margin: 0;">
                    <el-select v-model="filter.workType" style="width: 80px" size="mini" @change="onSearch" clearable
                        placeholder="班次">
                        <el-option label="白班" value="白班"></el-option>
                        <el-option label="晚班" value="晚班"></el-option>
                    </el-select>
                </el-form-item>
                <!-- <el-form-item label="">
                    <el-input v-model.trim="filter.describe" placeholder="账号" style="width:100px;" clearable
                        maxlength="20" />
                </el-form-item> -->
                <el-form-item label="" style="padding: 0;margin: 0;">
                    <el-input v-model.trim="filter.postCode" placeholder="一级岗位编码" style="width:100px;" clearable
                        maxlength="20" v-if="false" />
                </el-form-item>
                <el-form-item label="" style="padding: 0;margin: 0;">
                    <el-select v-model="filter.postName" style="width: 110px" size="mini" @change="onSearch" clearable
                        filterable placeholder="一级岗位名称">
                        <el-option v-for="item in myOnePostNameList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="" style="padding: 0;margin: 0;">
                    <el-input v-model.trim="filter.workItem" placeholder="二级岗位名称" style="width:110px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="" style="padding: 0;margin: 0;">
                    <el-input v-model.trim="filter.userName" placeholder="真实姓名" style="width:100px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="" style="padding: 0;margin: 0;">
                    <el-select v-model="filter.showType" style="width: 100px" size="mini" @change="onSearch" clearable
                        placeholder="计件计时">
                        <el-option label="计件" value="计件"></el-option>
                        <el-option label="计时" value="计时"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="" style="padding: 0;margin: 0;">
                    <el-select v-model="filter.userPostType" style="width: 100px" size="mini" @change="onSearch" clearable
                        placeholder="员工岗位类型">
                        <el-option label="普通员工" value="普通员工"></el-option>
                        <el-option label="管理员" value="管理员"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
                <el-form-item v-if="false">
                    <el-button type="primary" @click="onCompute">计算所有岗位人效</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false" :showsummary="true"
                :isSelectColumn="false" :loading="listLoading" :tableHandles="tableHandles1" @summaryClick='showchart2()'>
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="计算所有人人效" :visible.sync="dialogVisibleCompute" width="20%" v-dialogDrag
            :close-on-click-modal="false">
            <span>
                <el-row style="margin-bottom: 5px;font-size:14px; color:#EE7700">
                    不选仓库则计算全部仓库，日期必选。
                </el-row>
                <el-row style="margin-bottom: 5px;">
                    <el-col :xs="24" :sm="18" :md="24" :lg="24" :xl="24">
                        <el-select v-model="computeWarehouseCode" style="width: 280px" size="mini" clearable
                            placeholder="请选择仓库">
                            <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-date-picker style="width: 280px" v-model="computeWorkDate" type="daterange" format="yyyy-MM-dd"
                            :picker-options="pickOptions" value-format="yyyy-MM-dd" range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期">
                        </el-date-picker>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <my-confirm-button type="submit" :loading="copmuteLoading" @click="onCopmuteSave" /> &nbsp;
                <el-button @click="dialogVisibleCompute = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%" v-dialogDrag
            :close-on-click-modal="false">
            <my-container>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data">
                    </buschar>
                </span>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
                </span>
            </my-container>
        </el-dialog>

        <!-- <el-dialog :title="dialogMapVisible2.title" :visible.sync="dialogMapVisible2.visible" width="80%" v-dialogDrag
            :close-on-click-modal="false">
            <my-container>
                <span>
                    <div style="text-align: center; margin-bottom: 10px;">
                        <el-checkbox v-model="mycheck.checkQc" @change="showchart2()">全仓汇总</el-checkbox>
                        <el-checkbox v-model="mycheck.checkYw" @change="showchart2()">义乌昀晗供应链管理有限公司</el-checkbox>
                        <el-checkbox v-model="mycheck.checkNcqpl" @change="showchart2()">【南昌全品类仓】</el-checkbox>
                        <el-checkbox v-model="mycheck.checkNcdz" @change="showchart2()">【南昌定制仓】</el-checkbox>
                        <el-checkbox v-model="mycheck.checkYtbk" @change="showchart2()">【义乌圆通爆款仓】</el-checkbox>
                        <el-checkbox v-model="mycheck.checkYt5l" @change="showchart2()">【义乌圆通5楼】</el-checkbox>
                        <el-checkbox v-model="mycheck.checkNccj" @change="showchart2()">【南昌裁剪仓】</el-checkbox>
                        <el-checkbox v-model="mycheck.checkYwyz" @change="showchart2()">【义乌邮政仓】</el-checkbox>
                        <el-checkbox v-model="mycheck.checkHzf" @change="showchart2()">【杭州分仓】</el-checkbox>
                    </div>
                    <div style="text-align: center; margin-bottom: 10px;">
                        <el-checkbox v-model="mycheck2.checkZhiJian" @change="showchart2()">质检</el-checkbox>
                        <el-checkbox v-model="mycheck2.checkRuKu" @change="showchart2()">入库</el-checkbox>
                        <el-checkbox v-model="mycheck2.checkShangJia" @change="showchart2()">上架</el-checkbox>
                        <el-checkbox v-model="mycheck2.checkPeiHuo" @change="showchart2()">配货</el-checkbox>
                        <el-checkbox v-model="mycheck2.checkDaBao" @change="showchart2()">打包</el-checkbox>
                        <el-checkbox v-model="mycheck2.checkChengZhong" @change="showchart2()">称重</el-checkbox>
                        <el-checkbox v-model="mycheck2.checkJiBao" @change="showchart2()">集包</el-checkbox>
                        <el-checkbox v-model="mycheck2.checkTuiJian" @change="showchart2()">退件</el-checkbox>
                        <el-checkbox v-model="mycheck2.checkBaoCai" @change="showchart2()">包材</el-checkbox>
                        <el-checkbox v-model="mycheck2.checkDaDan" @change="showchart2()">打单</el-checkbox>
                        <el-checkbox v-model="mycheck2.checkDiaoBo" @change="showchart2()">调拨</el-checkbox>
                        <el-checkbox v-model="mycheck2.checkYueXin" @change="showchart2()">月薪岗</el-checkbox>
                    </div>
                </span>
                <span>
                    <buschar v-if="dialogMapVisible2.visible" :analysisData="dialogMapVisible2.data"
                        ref="dialogMapVisible2Buscher">
                    </buschar>
                </span>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="dialogMapVisible2.visible = false">关闭</el-button>
                </span>
            </my-container>
        </el-dialog>
         -->
        <el-dialog :title="dialogMapVisible2.title" :visible.sync="dialogMapVisible2.visible" width="80%" v-dialogDrag
            :close-on-click-modal="false">
            <my-container>
                <span>
                    <el-radio-group v-model="filter.chatType" @input="showchart2()">
                        <el-radio-button label="出仓成本" name="wareDataType">出仓成本</el-radio-button>
                        <el-radio-button label="岗位薪资" name="wareDataType">岗位薪资</el-radio-button>
                    </el-radio-group>
                </span>
                <span>
                    <div style="text-align: center; margin-bottom: 10px;">
                        <el-checkbox v-model="mycheck.checkYw" @change="showchart2()">义乌昀晗供应链管理有限公司</el-checkbox>
                        <el-checkbox v-model="mycheck.checkNcqpl" @change="showchart2()">【南昌全品类仓】</el-checkbox>
                        <el-checkbox v-model="mycheck.checkNcdz" @change="showchart2()">【南昌定制仓】</el-checkbox>
                        <el-checkbox v-model="mycheck.checkYtbk" @change="showchart2()">【义乌圆通爆款仓】</el-checkbox>
                        <el-checkbox v-model="mycheck.checkYt5l" @change="showchart2()">【义乌圆通5楼】</el-checkbox>
                        <el-checkbox v-model="mycheck.checkNccj" @change="showchart2()">【南昌裁剪仓】</el-checkbox>
                        <el-checkbox v-model="mycheck.checkYwyz" @change="showchart2()">【义乌邮政仓】</el-checkbox>
                        <el-checkbox v-model="mycheck.checkHzf" @change="showchart2()">【杭州分仓】</el-checkbox>
                    </div>
                </span>
                <span>
                    <buschar v-if="dialogMapVisible2.visible" :analysisData="dialogMapVisible2.data"
                        ref="dialogMapVisible2Buscher">
                    </buschar>
                </span>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="dialogMapVisible2.visible = false">关闭</el-button>
                </span>
            </my-container>
        </el-dialog>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MySearch from "@/components/my-search";
import buschar from '@/components/Bus/buschar';
import MySearchWindow from "@/components/my-search-window";
import {
    getWarehouseWagesComputePageList, computeWarehouseUserWorkData,
    getWarehouseWagesComputeChat, getWarehouseWagesComputeChat2, getWarehouseWagesComputeChat5,
    exportWarehouseWagesComputeList,
    computeDataOk, computeDataNoOk, warehouseComputeDataOk_Launch
} from '@/api/profit/warehousewages';
const tableColsBase = [
    { istrue: true, prop: 'workDate', label: '月份', width: '80', sortable: 'custom', formatter: (row) => row.workDateStr },
    { istrue: true, prop: 'workDate', label: '日期', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.workDate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'warehouseName', label: '仓库', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'workType', label: '班次', width: '60', sortable: 'custom' },
    //{ istrue: true, prop: 'postCode', label: '一级岗位编码', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'postName', label: '一级岗位名称', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'workItem', label: '二级岗位名称', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'userName', label: '真实姓名', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'getValueType', label: '取值类型', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'wages', label: '工价', width: '60', sortable: 'custom', summaryEvent: true },//
    { istrue: true, prop: 'workCount', label: '计件数量', width: '90', sortable: 'custom', summaryEvent: true },
    //{ istrue: true, prop: 'actualDuration', label: '工作时长', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'workDayWages', label: '计件工资', width: '90', sortable: 'custom', summaryEvent: true },
    { istrue: true, prop: 'workDayWages2', label: '计时工资', width: '90', sortable: 'custom', summaryEvent: true },
    { istrue: true, prop: 'workDayWagesTotal', label: '工资总和', width: '90', sortable: 'custom', summaryEvent: true },
    { istrue: true, prop: 'jiJianUserCount', label: '计件出勤人数', width: '110', sortable: 'custom', summaryEvent: true },
    { istrue: true, prop: 'jiShiUserCount', label: '计时出勤人数', width: '110', sortable: 'custom', summaryEvent: true },
    { istrue: true, prop: 'orderCount', label: '发件量', width: '90', sortable: 'custom', summaryEvent: true },
    { istrue: true, prop: 'orderCountCost', label: '出仓成本', width: '90', sortable: 'custom', summaryEvent: true },
    { istrue: true, prop: 'daBaoCount', label: '打包量', width: '90', sortable: 'custom', summaryEvent: true },
    { istrue: true, prop: 'daBaoCountCost', label: '出仓成本2', width: '90', sortable: 'custom', summaryEvent: true },
    //{ istrue: true, prop: 'userEfficiency', label: '人效', width: '80', sortable: 'custom' },
    //{ istrue: true, prop: 'outCost', label: '出仓成本', width: '80', sortable: 'custom' },
    //{ istrue: true, prop: 'userSend', label: '人均发件量', width: '90', sortable: 'custom' },
    //{ istrue: true, prop: 'userCost', label: '人工成本', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'errorRemark', label: '计算失败原因', width: '260', sortable: 'custom' },
    {
        istrue: true,
        display: true,
        label: "趋势图",
        style: "color:red;cursor:pointer;",
        width: 70,
        formatter: (row) => "趋势图",
        type: "click",
        handle: (that, row) => that.showchart(row),
    },
    {
        istrue: true, prop: 'isOkStatus', label: '确认状态', width: '80',
        formatter: (row) => (row.isOkStatus == 1 ? "已确认" : row.isOkStatus == -1 ? "已驳回" : row.isOkStatus == 0 ? "确认中" : "未确认")
    },
    {
        istrue: true, type: 'button', label: '操作', width: '120', tipmesg: "确认无误后当前仓当天将自动不参与计算",
        btnList: [
            //{ label: "确认无误", handle: (that, row) => that.onDataOk(row), permission: "api:profit:warehousewages:ComputeDataOk" },
            //{ label: "驳回确认", handle: (that, row) => that.onDataNoOk(row), permission: "api:profit:warehousewages:ComputeDataNoOk" },
            { label: "日志", handle: (that, row) => that.onDataOkLog(row) },
            { label: "发起确认", handle: (that, row) => that.onFqQr(row) },
        ]
    },
]
const tableHandles1 = [
    //{ label: "计算人效", handle: (that) => that.onCompute() },
    // { label: "导出", handle: (that) => that.onExport() },
];
export default {
    name: 'warehousewagescompute',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, buschar },
    props: ['myWarehouseList', 'myOnePostNameList'],
    data() {
        return {
            that: this,
            filter: {
                workDateType: true,
                dataType: "真实姓名",
                timerange: [
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                ],
                startDate: null,
                endDate: null,
                warehouseCode: 10361546,//诚信仓
                isWare: false,
                isWorkType: false,
                isOnePost: false,
                isTwoPost: false,
                isUserName: false,
                chatType: "出仓成本",
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "workDate", IsAsc: false },
            tableCols: null,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            pickerOptions: {
                shortcuts: [
                    {
                        text: '昨天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime());
                            picker.$emit('pick', [start, start]);
                        }
                    }, {
                        text: '近三天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime());
                            const end = new Date(new Date(tdate.toLocaleDateString()));
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一周',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                            const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一个月',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                            console.log("获取前一个月的时间", tdate.getDay());
                            const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
            },
            tableHandles1: tableHandles1,
            dialogVisibleCompute: false,
            copmuteLoading: false,
            computeWarehouseCode: null,
            computeWorkDate: [],
            bComputeWorkDate: null,
            eComputeWorkDate: null,

            dialogMapVisible: { visible: false, title: "", data: {} },
            dialogMapVisible2: { visible: false, title: "", data: {} },
            mycheck: {
                //checkQc: true,
                checkYw: true,
                checkNcqpl: false,
                checkNcdz: false,
                checkYtbk: false,
                checkYt5l: false,
                checkNccj: false,
                checkYwyz: false,
                checkHzf: false,
            },
            mycheck2: {
                checkZhiJian: false,
                checkRuKu: false,
                checkShangJia: false,
                checkPeiHuo: false,
                checkDaBao: false,
                checkChengZhong: false,
                checkJiBao: false,
                checkTuiJian: false,
                checkBaoCai: false,
                checkDaDan: false,
                checkDiaoBo: false,
                checkYueXin: false,
            },
            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            exportCols: [],
        };
    },
    async mounted() {
        await this.onSearch();
    },
    computed: {
        // tableCols(){
        //     let cols=[];
        //     for(let i=0;i<this.tableColsBase;i++)
        //     {

        //     }

        //     return cols;
        // }
    },
    methods: {
        async onChangeFilter() {
            this.pager = { OrderBy: "workDate", IsAsc: false };
            await this.onSearch()
        },
        setCols() {
            this.exportCols = [];
            this.tableCols = [];
            if (this.filter.workDateType == true) {//按日
                this.tableCols.push(tableColsBase[1]);
                this.exportCols.push({ key: "workDate", value: tableColsBase[1].label });
            }
            else {//按月
                this.tableCols.push(tableColsBase[0]);
                this.exportCols.push({ key: "workDateStr", value: tableColsBase[1].label });
            }
            //任意维度被选中
            let isweidu = false;
            if (this.filter.isWare == true || this.filter.isWorkType == true || this.filter.isOnePost == true || this.filter.isTwoPost == true || this.filter.isUserName == true) {
                isweidu = true;
                if (this.filter.isWare == true) {
                    this.tableCols.push(tableColsBase[2]);
                    this.exportCols.push({ key: tableColsBase[2].prop, value: tableColsBase[2].label });
                }
                if (this.filter.isWorkType == true) {
                    this.tableCols.push(tableColsBase[3]);
                    this.exportCols.push({ key: tableColsBase[3].prop, value: tableColsBase[3].label });
                }
                if (this.filter.isOnePost == true) {
                    this.tableCols.push(tableColsBase[4]);
                    this.exportCols.push({ key: tableColsBase[4].prop, value: tableColsBase[4].label });
                }
                if (this.filter.isTwoPost == true) {
                    this.tableCols.push(tableColsBase[5]);
                    this.exportCols.push({ key: tableColsBase[5].prop, value: tableColsBase[5].label });
                }
                if (this.filter.isUserName == true) {
                    this.tableCols.push(tableColsBase[6]);
                    this.exportCols.push({ key: tableColsBase[6].prop, value: tableColsBase[6].label });
                }
            }
            else {
                this.tableCols.push(tableColsBase[2]);
                this.tableCols.push(tableColsBase[3]);
                this.tableCols.push(tableColsBase[4]);
                this.tableCols.push(tableColsBase[5]);
                this.tableCols.push(tableColsBase[6]);
                this.tableCols.push(tableColsBase[7]);
                this.exportCols.push({ key: tableColsBase[2].prop, value: tableColsBase[2].label });
                this.exportCols.push({ key: tableColsBase[3].prop, value: tableColsBase[3].label });
                this.exportCols.push({ key: tableColsBase[4].prop, value: tableColsBase[4].label });
                this.exportCols.push({ key: tableColsBase[5].prop, value: tableColsBase[5].label });
                this.exportCols.push({ key: tableColsBase[6].prop, value: tableColsBase[6].label });
                this.exportCols.push({ key: tableColsBase[7].prop, value: tableColsBase[7].label });
            }
            this.tableCols.push(tableColsBase[8]);
            this.tableCols.push(tableColsBase[9]);
            this.tableCols.push(tableColsBase[10]);
            this.tableCols.push(tableColsBase[11]);
            this.tableCols.push(tableColsBase[12]);
            this.exportCols.push({ key: tableColsBase[8].prop, value: tableColsBase[8].label });
            this.exportCols.push({ key: tableColsBase[9].prop, value: tableColsBase[9].label });
            this.exportCols.push({ key: tableColsBase[10].prop, value: tableColsBase[10].label });
            this.exportCols.push({ key: tableColsBase[11].prop, value: tableColsBase[11].label });
            this.exportCols.push({ key: tableColsBase[12].prop, value: tableColsBase[12].label });
            //日维度，任务查看维度被选但真实姓名没选
            if (this.filter.workDateType == true && (this.filter.isWare == true || this.filter.isWorkType == true || this.filter.isOnePost == true || this.filter.isTwoPost == true) && this.filter.isUserName == false) {
                this.tableCols.push(tableColsBase[13]);
                this.tableCols.push(tableColsBase[14]);
                this.exportCols.push({ key: tableColsBase[13].prop, value: tableColsBase[13].label });
                this.exportCols.push({ key: tableColsBase[14].prop, value: tableColsBase[14].label });
            }
            //只有仓库为true时才显示
            var olnyIsWare = false;
            if (this.filter.isWare == true && this.filter.isWorkType == false && this.filter.isOnePost == false && this.filter.isTwoPost == false && this.filter.isUserName == false) {
                olnyIsWare = true;
                this.tableCols.push(tableColsBase[15]);
                this.tableCols.push(tableColsBase[16]);
                this.exportCols.push({ key: tableColsBase[15].prop, value: tableColsBase[15].label });
                this.exportCols.push({ key: tableColsBase[16].prop, value: tableColsBase[16].label });
                // this.tableCols.push(tableColsBase[17]);
                // this.tableCols.push(tableColsBase[18]);
            }
            if (!isweidu) {
                this.tableCols.push(tableColsBase[19]);
                this.exportCols.push({ key: tableColsBase[19].prop, value: tableColsBase[19].label });
            }
            //趋势图
            this.tableCols.push(tableColsBase[20]);
            if (olnyIsWare && this.filter.workDateType == true) {
                //确认按钮
                this.tableCols.push(tableColsBase[21]);
                this.tableCols.push(tableColsBase[22]);
            }
        },
        //查询第一页
        async onSearch() {
            this.setCols();

            if (!this.filter.timerange) {
                this.$message({ message: "请选择日期", type: "warning", });
                return;
            }
            this.$refs.pager.setPage(1);
            await this.getlist();

            this.$refs.table.initsummaryEvent();
        },
        //获取查询条件
        getCondition() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            const res = await getWarehouseWagesComputePageList(params)
            if (!res?.success) {

                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
        },
        //排序查 询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        //导出
        async onExport() {
            await this.onSearch();
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            params.cols = this.exportCols;
            console.log(params, 'params')
            this.listLoading = true;
            const res = await exportWarehouseWagesComputeList(params)
            this.listLoading = false;
            if (!res?.data) {
                this.$message({ type: 'error', message: '没有数据可导出或导出失败!' });
                return;
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', "仓库薪资统计_" + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        async onCompute() {
            this.dialogVisibleCompute = true;
            this.computeWarehouseCode = this.filter.warehouseCode;
        },
        async onCopmuteSave() {
            if (!this.computeWorkDate || this.computeWorkDate.length <= 0) {
                this.$message({ type: 'error', message: '请选择要计算的日期!' });
                return;
            }
            else {
                this.bComputeWorkDate = formatTime(this.computeWorkDate[0], "YYYY-MM-DD");
                this.eComputeWorkDate = formatTime(this.computeWorkDate[1], "YYYY-MM-DD");
                this.copmuteLoading = true;
                let res = await computeWarehouseUserWorkData({
                    bWorkDate: this.bComputeWorkDate,
                    eWorkDate: this.eComputeWorkDate,
                    warehouseCode: this.computeWarehouseCode
                });
                this.copmuteLoading = false;
                if (res?.success) {
                    this.$message({ type: 'success', message: '计算人效中..请几分钟后刷新查看!' });
                    this.dialogVisibleCompute = false;
                }
            }
        },
        async showchart(row, type) {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            this.dialogMapVisible.title = "汇总趋势图";
            if (row) {
                params.warehouseCode = row.warehouseCode;
                params.warehouseName = row.warehouseName;
                params.workType = row.workType;
                params.postCode = row.postCode;
                params.postName = row.postName;
                params.workItem = row.workItem;
                params.userName = row.userName;

                this.dialogMapVisible.title = row.warehouseName;
                if (row.workType) this.dialogMapVisible.title += ("-" + row.workType);
                if (row.postName) this.dialogMapVisible.title += ("-" + row.postName);
                if (row.workItem) this.dialogMapVisible.title += ("-" + row.workItem);
                if (row.userName) this.dialogMapVisible.title += ("-" + row.userName);
            }
            if (type == true) {
                params.isSumDayChat = true;
            }
            console.log(params, 'showchart-params')
            let that = this;
            const res = await getWarehouseWagesComputeChat(params).then(res => {
                that.dialogMapVisible.visible = true;
                that.dialogMapVisible.data = res;
                console.log(res);
            })
        },
        async showchart2() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            this.dialogMapVisible2.title = "汇总趋势图";
            params.isSumDayChat = true;
            params.wareHouseChecks = [
                //{ key: "全仓汇总", value: this.mycheck.checkQc },
                { key: "义乌市昀晗供应链管理有限公司", value: this.mycheck.checkYw },
                { key: "【南昌全品类仓】", value: this.mycheck.checkNcqpl },
                { key: "【南昌定制仓】", value: this.mycheck.checkNcdz },
                { key: "【义乌圆通爆款仓】", value: this.mycheck.checkYtbk },
                { key: "【义乌圆通5楼】", value: this.mycheck.checkYt5l },
                { key: "【南昌裁剪仓】", value: this.mycheck.checkNccj },
                { key: "【义乌邮政仓】", value: this.mycheck.checkYwyz },
                { key: "【杭州分仓】", value: this.mycheck.checkHzf },
            ];
            // params.postNameChecks = [
            //     { key: "质检", value: this.mycheck2.checkZhiJian },
            //     { key: "入库", value: this.mycheck2.checkRuKu },
            //     { key: "上架", value: this.mycheck2.checkShangJia },
            //     { key: "配货", value: this.mycheck2.checkPeiHuo },
            //     { key: "打包", value: this.mycheck2.checkDaBao },
            //     { key: "称重", value: this.mycheck2.checkChengZhong },
            //     { key: "集包", value: this.mycheck2.checkJiBao },
            //     { key: "退件", value: this.mycheck2.checkTuiJian },
            //     { key: "包材", value: this.mycheck2.checkBaoCai },
            //     { key: "打单", value: this.mycheck2.checkDaDan },
            //     { key: "调拨", value: this.mycheck2.checkDiaoBo },
            //     { key: "月薪岗", value: this.mycheck2.checkYueXin },
            // ];
            console.log(params, 'showchart2-params')
            let that = this;
            const res = await getWarehouseWagesComputeChat5(params).then(res => {
                console.log(res);
                if (res?.series && res?.series.length > 0) {
                    that.dialogMapVisible2.visible = true;
                    that.dialogMapVisible2.data = res;
                    if (that.$refs.dialogMapVisible2Buscher)
                        that.$refs.dialogMapVisible2Buscher.initcharts();
                }
            })
        },
        ///仓库确认无误
        async onDataOk(row) {
            this.$confirm('确定要执行“确认无误”操作吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                console.log(row);
                const res = await computeDataOk({ workDate: row.workDate, warehouseName: row.warehouseName });
                if (res?.success) {
                    this.$message({ type: 'success', message: '确认成功!' });
                    await this.onSearch();
                }
            }).catch(() => {
            });
        },
        ///仓库确认无误-但管理员觉得有问题驳回
        async onDataNoOk(row) {
            this.$confirm('确定要执行“驳回确认”操作吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                console.log(row);
                const res = await computeDataNoOk({ workDate: row.workDate, warehouseName: row.warehouseName });
                if (res?.success) {
                    this.$message({ type: 'success', message: '驳回成功!' });
                    await this.onSearch();
                }
            }).catch(() => {
            });
        },
        async onDataOkLog(row) {
            this.$showDialogform({
                path: `@/views/profit/warehousewages/warehousewagesdataisok.vue`,
                title: '确认日志',
                args: { ...row },
                height: '300px',
                width: '200',
            })
        },
        async onFqQr(row) {
            if (row.isOkStatus >= 0) {
                this.$message({ type: 'error', message: '只能发起被驳回和未确认的数据!' });
                return;
            }
            this.$confirm('确定要执行“发起确认”的操作吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                console.log(row);
                const res = await warehouseComputeDataOk_Launch({ strWorkDate: row.workDate, strWarehouseCode: row.warehouseName });
                if (res?.success) {
                    this.$message({ type: 'success', message: '操作成功!' });
                    await this.onSearch();
                }
            }).catch(() => {
            });

        },
    },
};
</script>

<style lang="scss" scoped></style>
