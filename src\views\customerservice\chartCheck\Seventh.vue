<template>
  <my-container>
    <!--顶部操作-->
    <div class=".top">
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
        <el-form-item label="登记时间:">
          <el-date-picker style="width: 320px" v-model="Filter.timerange" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>
        <el-form-item label="聊天时间:">
          <el-date-picker style="width: 320px" v-model="Filter.chatTime" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>

        <el-button style="padding: 0;border: none;margin: 0;">
          <inputYunhan ref="orderNoList" v-model="Filter.orderNoList" :inputt.sync="Filter.orderNoList"
            placeholder="线上订单号" :maxRows="3000" :maxlength="90000" :clearable="true" width="130px"
            @callback="callbackProCode" title="线上订单号"></inputYunhan>
        </el-button>

        <el-form-item label="平台:">
          <el-select v-model="Filter.platform" placeholder="请选择" class="el-select-content" clearable>
            <el-option v-for="item in platformTypeList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="店铺:">
          <el-select v-model="Filter.shopNameList" placeholder="请选择" class="el-select-content" filterable multiple
            clearable collapse-tags>
            <el-option v-for="item in shopList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="账号使用人:">
          <el-select v-model="Filter.userName" placeholder="请选择" class="el-select-content" filterable clearable
            collapse-tags>
            <el-option v-for="item in uNameList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="分组:">
          <el-select v-model="Filter.groupName" placeholder="请选择" class="el-select-content" filterable clearable
            collapse-tags>
            <el-option v-for="item in gNameList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="最后操作人:">
          <el-select v-model="Filter.lastOperator" placeholder="请选择" class="el-select-content" filterable clearable
            collapse-tags>
            <el-option v-for="item in gLastOperator" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="最后操作时间:">
          <el-date-picker style="width: 320px" v-model="Filter.lastTime" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport"
            v-if="checkPermission(['api:customerservice:UnPayOrder:BlacklistingBuyersExport'])">导出</el-button>
          <el-button type="primary" @click="addLists"
            v-if="checkPermission(['api:Customerservice:UnPayOrder:AddBlacklistingBuyersAsync'])">新增</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="uptime">数据更新时间: {{ upDataTime }}</div>
    <!--列表-->
    <!-- <template> -->
    <div style="height: calc(100% - 12%);">
      <vxetablebase :id="'Seventh202408041459'" ref="table" :tableData="tableData" :tableCols="tableCols" :that="that" :hasSeq="false"
        @checkbox-range-end="chooseCode" :showsummary="true" :summaryarry='summaryarry' :isIndexFixed="false"
        :isIndex="true" @sortchange='sortchange' :loading="listLoading" :showoverflow="'title'" />
    </div>




    <!-- <vxetablebase :hasSeq="false" :border="true" :align="'center'" ref="table" :that='that' :hasexpand='false'
                :showsummary="true" :summaryarry='summaryarry' :isIndexFixed="false"  :isSelection="false"
                :tableData='tableData' :tableCols='tableCols' :isSelectColumn="true" :enableCheckRange="false"
                @checkbox-range-end="callback" :isstorage="false" :loading="listLoading"
                :toolbarshow="false" :height="'100%'" :showoverflow="'ellipsis'"/> -->
    <!-- show-overflow="ellipsis" -->
    <!-- </template> -->

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="gettrainplanList" />
    </template>

    <el-dialog title="添加拉黑买家" :visible.sync="addgroupdialogVisibleSyj" width="40%" :close-on-click-modal=false
      v-dialogDrag>
      <span>
        <el-form :model="addForm" ref="addForm" :rules="addFormRules">

          <el-form-item prop="createdTime" label="登记时间">
            <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="addForm.createdTime"
              style="width:63%" type="date" placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="chatTime" label="聊天时间">
            <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="addForm.chatTime" style="width:63%"
              type="date" placeholder="选择日期">
            </el-date-picker>
          </el-form-item>

          <el-form-item prop="platform" label="平台" label-width="55px">
            <el-select v-model="addForm.platform" placeholder="请选择" class="el-select-content" style="width:72%"
              clearable>
              <el-option v-for="item in platformTypeList" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item prop="shopName" label="店铺">
            <el-input style="width:67%" clearable v-model="addForm.shopName" :maxlength="20"></el-input>
          </el-form-item>
          <el-form-item prop="orderNo" label="线上订单号">
            <el-input type="text" style="width:60%" clearable v-model="addForm.orderNo"></el-input>
          </el-form-item>
          <el-form-item prop="description" label="问题说明">
            <el-input style="width:62%" clearable v-model="addForm.description" :maxlength="100"></el-input>
          </el-form-item>

          <el-form-item prop="chatImgs" label="客服聊天截图">
            <uploadimgFile v-if="addgroupdialogVisibleSyj" ref="uploadimgFile" :disabled="isView" :ispaste="!isView"
              :noDel="isView" :accepttyes="accepttyes" :isImage="true" :uploadInfo="chatUrls" :keys="[1, 1]"
              @callback="getImg" :imgmaxsize="9" :limit="9" :multiple="true">
            </uploadimgFile>
          </el-form-item>


          <el-form-item prop="platformImgs" label="平台判定截图">
            <uploadimgFile v-if="addgroupdialogVisibleSyj" ref="uploadimgFile" :disabled="isView" :ispaste="!isView"
              :noDel="isView" :accepttyes="accepttyes" :isImage="true" :uploadInfo="ptchatUrls" :keys="[1, 1]"
              @callback="getImgpt" :imgmaxsize="9" :limit="9" :multiple="true">
            </uploadimgFile>
          </el-form-item>


          <el-form-item prop="remark" label="备注">
            <el-input style="width:68%" clearable v-model="addForm.remark" :maxlength="100"></el-input>
          </el-form-item>
          <el-form-item prop="snick" label="账号昵称">
            <el-input style="width:64%" clearable v-model="addForm.snick" :maxlength="20"></el-input>
          </el-form-item>
          <el-form-item prop="userName" label="账号使用人">
            <el-input style="width:61%" clearable v-model="addForm.userName" :maxlength="10"></el-input>
          </el-form-item>
          <el-form-item prop="groupName" label="分组">
            <el-input style="width:68%" clearable v-model="addForm.groupName" :maxlength="20"></el-input>
          </el-form-item>
        </el-form>
      </span>

      <span slot="footer" class="dialog-footer">
        <el-button @click="closeGroup">关闭</el-button>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addgroup">确定</el-button>
      </span>
    </el-dialog>


    <!-- 编辑拉黑买家 -->
    <el-dialog title="编辑拉黑买家" :visible.sync="updategroupdialogVisibleSyj" width="40%" :close-on-click-modal=false
      v-dialogDrag :before-close="closeUpGroup">
      <span>
        <el-form :model="updateForm" ref="updateForm" :rules="updateFormRules">

          <el-form-item prop="createdTime" label="登记时间">
            <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="updateForm.createdTime"
              style="width:63%" type="date" placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="chatTime" label="聊天时间">
            <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="updateForm.chatTime"
              style="width:63%" type="date" placeholder="选择日期">
            </el-date-picker>
          </el-form-item>

          <el-form-item prop="platform" label="平台" label-width="55px">
            <el-select v-model="updateForm.platform" placeholder="请选择" class="el-select-content" style="width:72%"
              clearable>
              <el-option v-for="item in platformTypeList" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item prop="shopName" label="店铺">
            <el-input style="width:67%" clearable v-model="updateForm.shopName" :maxlength="20"></el-input>
          </el-form-item>
          <el-form-item prop="orderNo" label="线上订单号">
            <el-input type="text" style="width:60%" clearable v-model="updateForm.orderNo"></el-input>
          </el-form-item>
          <el-form-item prop="description" label="问题说明">
            <el-input style="width:62%" clearable v-model="updateForm.description" :maxlength="100"></el-input>
          </el-form-item>

          <el-form-item label="客服聊天截图:" prop="chatImgs" class="first">
            <uploadimgFile v-if="updategroupdialogVisibleSyj" ref="uploadimgFile" :disabled="isView" :ispaste="!isView"
              :noDel="isView" :accepttyes="accepttyes" :isImage="true" :uploadInfo="chatUrls" :keys="[1, 1]"
              @callback="getImg" :imgmaxsize="9" :limit="9" :multiple="true">
            </uploadimgFile>
          </el-form-item>


          <el-form-item prop="platformImgs" label="平台判定截图">
            <uploadimgFile v-if="updategroupdialogVisibleSyj" ref="uploadimgFile" :disabled="isView" :ispaste="!isView"
              :noDel="isView" :accepttyes="accepttyes" :isImage="true" :uploadInfo="ptchatUrls" :keys="[1, 1]"
              @callback="getImgpt" :imgmaxsize="9" :limit="9" :multiple="true">
            </uploadimgFile>
          </el-form-item>

          <el-form-item prop="remark" label="备注">
            <el-input style="width:68%" clearable v-model="updateForm.remark" :maxlength="100"></el-input>
          </el-form-item>
          <el-form-item prop="snick" label="账号昵称">
            <el-input style="width:64%" clearable v-model="updateForm.snick" :maxlength="20"></el-input>
          </el-form-item>
          <el-form-item prop="userName" label="账号使用人">
            <el-input style="width:61%" clearable v-model="updateForm.userName" :maxlength="10"></el-input>
          </el-form-item>
          <el-form-item prop="groupName" label="分组">
            <el-input style="width:68%" clearable v-model="updateForm.groupName" :maxlength="20"></el-input>
          </el-form-item>
        </el-form>

      </span>

      <span slot="footer" class="dialog-footer">
        <el-button @click="closeUpGroup()">关闭</el-button>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="updategroup()">确定</el-button>
      </span>
    </el-dialog>


    <!-- 添加拉黑买家 -->
    <el-dialog title="添加拉黑买家" :visible.sync="addgroupdialogVisibleSyj" width="40%" :close-on-click-modal=false
      v-dialogDrag :before-close="closeGroup">
      <span>
        <el-form :model="addForm" ref="addForm" :rules="addFormRules">

          <el-form-item prop="createdTime" label="登记时间">
            <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="addForm.createdTime"
              style="width:63%" type="date" placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="chatTime" label="聊天时间">
            <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="addForm.chatTime" style="width:63%"
              type="date" placeholder="选择日期">
            </el-date-picker>
          </el-form-item>

          <el-form-item prop="platform" label="平台" label-width="55px">
            <el-select v-model="addForm.platform" placeholder="请选择" class="el-select-content" style="width:72%"
              clearable>
              <el-option v-for="item in platformTypeList" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item prop="shopName" label="店铺">
            <el-input style="width:67%" clearable v-model="addForm.shopName" :maxlength="20"></el-input>
          </el-form-item>
          <el-form-item prop="orderNo" label="线上订单号">
            <el-input type="text" style="width:60%" clearable v-model="addForm.orderNo"></el-input>
          </el-form-item>
          <el-form-item prop="description" label="问题说明">
            <el-input style="width:62%" clearable v-model="addForm.description" :maxlength="100"></el-input>
          </el-form-item>

          <el-form-item prop="chatImgs" label="客服聊天截图">
            <uploadimgFile v-if="addgroupdialogVisibleSyj" ref="uploadimgFile" :disabled="isView" :ispaste="!isView"
              :noDel="isView" :accepttyes="accepttyes" :isImage="true" :uploadInfo="chatUrls" :keys="[1, 1]"
              @callback="getImg" :imgmaxsize="9" :limit="9" :multiple="true">
            </uploadimgFile>
          </el-form-item>


          <el-form-item prop="platformImgs" label="平台判定截图">
            <uploadimgFile v-if="addgroupdialogVisibleSyj" ref="uploadimgFile" :disabled="isView" :ispaste="!isView"
              :noDel="isView" :accepttyes="accepttyes" :isImage="true" :uploadInfo="ptchatUrls" :keys="[1, 1]"
              @callback="getImgpt" :imgmaxsize="9" :limit="9" :multiple="true">
            </uploadimgFile>
          </el-form-item>


          <el-form-item prop="remark" label="备注">
            <el-input style="width:68%" clearable v-model="addForm.remark" :maxlength="100"></el-input>
          </el-form-item>
          <el-form-item prop="snick" label="账号昵称">
            <el-input style="width:64%" clearable v-model="addForm.snick" :maxlength="20"></el-input>
          </el-form-item>
          <el-form-item prop="userName" label="账号使用人">
            <el-input style="width:61%" clearable v-model="addForm.userName" :maxlength="10"></el-input>
          </el-form-item>
          <el-form-item prop="groupName" label="分组">
            <el-input style="width:68%" clearable v-model="addForm.groupName" :maxlength="20"></el-input>
          </el-form-item>
        </el-form>
      </span>

      <span slot="footer" class="dialog-footer">
        <el-button @click="closeGroup">关闭</el-button>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addgroup">确定</el-button>
      </span>
    </el-dialog>



  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import CesTable from "@/components/Table/table.vue";
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import {
  getBlacklistingBuyersPageList, addOrUpdateBlacklistingBuyersAsync, deleteBlacklistingBuyersAsync
  , getBlacklistingBuyersShopNameList, getBlacklistingBuyersUserNameList, getBlacklistingBuyersGroupNameList, getBlacklistingBuyersLastOperatorList, blacklistingBuyersExport
} from "@/api/customerservice/chartCheck";
import inputYunhan from "@/components/Comm/inputYunhan";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { e } from "mathjs";



//平台下拉
const platformTypeList = [
  { name: "天猫", value: 1 },
  { name: "拼多多", value: 2 },
  { name: "跨境", value: 3 },
  { name: "阿里巴巴", value: 4 },
  { name: "其他", value: 5 },
  { name: "抖音", value: 6 },
  { name: "京东", value: 7 },
  { name: "淘工厂", value: 8 },
  { name: "淘宝", value: 9 },
  { name: "苏宁", value: 10 },
  { name: "分销", value: 11 },
  { name: "希音", value: 12 },
  { name: "拼多多跨境", value: 13 },
  { name: "快手", value: 14 }
];

const tableCols = [
  {
    istrue: true,
    prop: "createdTime",
    label: "登记时间",
    width: "100",
    sortable: "custom",
    formatter: (row) => {
      return row.createdTime ? formatTime(row.createdTime, "YYYY-MM-DD") : "";
    },
  },
  {
    istrue: true,
    prop: "chatTime",
    label: "聊天时间",
    width: "100",
    sortable: "custom",
    formatter: (row) => {
      return row.chatTime ? formatTime(row.chatTime, "YYYY-MM-DD") : "";
    },
  },
  {
    istrue: true,
    prop: "platform",
    label: "平台",
    width: "70",
    formatter: (row) => {
      return platformTypeList.filter(item => item.value == row.platform)[0].name
    },
  },
  {
    istrue: true,
    prop: "shopName",
    label: "店铺",
    width: "180",
  },
  {
    istrue: true,
    prop: "orderNo",
    label: "线上订单号",
    width: "100",
    handle: (that, row) => that.showLogDetail(row)
  },
  {
    istrue: true,
    prop: "description",
    label: "问题说明",
    width: "130",
    // sortable: "custom",
    // show-over-flow:true
    // showoverflow: true,
    // type:
  },
  {
    istrue: true,
    prop: 'chatImgs',
    label: '客服聊天截图',
    width: '130',
    type: "imagess"
  },
  {
    istrue: true,
    prop: 'platformImgs',
    label: '平台判定截图',
    width: '130',
    type: "imagess"
  },
  {
    istrue: true,
    prop: "remark",
    label: "备注",
    width: "130",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "snick",
    label: "账号昵称",
    width: "130",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "userName",
    label: "使用账号人",
    width: "100",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "groupName",
    label: "分组",
    width: "120",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "lastOperator",
    label: "最后操作人",
    width: "100",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "lastTime",
    label: "最后操作时间",
    width: "150",
    sortable: "custom",
    formatter: (row) => {
      return row.lastTime ? formatTime(row.lastTime, "YYYY-MM-DD") : "";
    },
  },
  {
    istrue: true,
    type: "button",
    label: "操作",
    align: "center",
    fixed: "right",
    width: "200",
    btnList: [
      { label: "修改", handle: (that, row) => that.handleupdategroup(row), permission: "api:Customerservice:UnPayOrder:UpdateBlacklistingBuyersAsync" },
      { label: "删除", handle: (that, row) => that.deletegroup(row), permission: "api:customerservice:UnPayOrder:DeleteBlacklistingBuyersAsync" }
    ],
  },
];
export default {
  name: "chatSixth",
  components: {
    MyContainer,
    CesTable,
    OrderActionsByInnerNos, uploadimgFile, inputYunhan, vxetablebase
  },
  props: {
    isView: {
      type: Boolean,
      default: false
    },
  },

  data() {
    return {
      that: this,
      Filter: {
        timerange: [
          formatTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"),
          formatTime(new Date(), "YYYY-MM-DD 23:59:59"),
        ],
        shopNameList: null,

      },
      updateForm: {
        // pictures:[],
        // chatUrls:[],
        // picturespt:[],
        // ptchatUrls: [],
      },
      addForm: {

      }, //添加绑定值
      platformTypeList: platformTypeList,
      tableData: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: null,
      pager: { orderBy: "createdTime", isAsc: false },
      listLoading: false,
      shopList: [],
      uNameList: [],
      gNameList: [],
      gLastOperator: [],
      cAccountList: [],
      likeList: [
        { label: '已提取', value: 1 },
        { label: '未提取', value: 0 },
      ],
      pickerOptions: {
        disabledDate(date) {
          // 设置禁用日期
          const start = new Date("1970/1/1");
          const end = new Date("9999/12/31");
          return date < start || date > end;
        },
      },
      defaultDate: new Date(),
      upDataTime: "",
      dialogHisVisible: false,
      orderNo: '',
      updategroupdialogVisibleSyj: false,  //修改弹出框控制
      addgroupdialogVisibleSyj: false,     //添加弹出框控制
      editkfPriceVisible: false,//客服图片
      editptPriceVisible: false,//平台图片
      accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',//上传格式
      pictures: null,
      picturespt: null,
      chatUrls: [],
      ptchatUrls: [],
      editPriceVisible: false,//图片
      pteditPriceVisible: false,
      addFormRules: {
        createdTime: [{ required: true, message: '必填', trigger: 'blur' }],
        chatTime: [{ required: true, message: '必填', trigger: 'blur' }],
        platform: [{ required: true, message: '必填', trigger: 'blur' }],
        shopName: [{ required: true, message: '必填', trigger: 'blur' }],
        // orderNo: [{ required: true, message: '必填', trigger: 'blur' }],
        description: [{ required: true, message: '必填', trigger: 'blur' }],
        chatImgs: [{ required: true, message: '必填', trigger: 'blur' }],
        platformImgs: [{ required: true, message: '必填', trigger: 'blur' }],
        remark: [{ required: true, message: '必填', trigger: 'blur' }],
        snick: [{ required: true, message: '必填', trigger: 'blur' }],
        userName: [{ required: true, message: '必填', trigger: 'blur' }],
        groupName: [{ required: true, message: '必填', trigger: 'blur' }]
      },
      updateFormRules: {
        createdTime: [{ required: true, message: '必填', trigger: 'blur' }],
        chatTime: [{ required: true, message: '必填', trigger: 'blur' }],
        platform: [{ required: true, message: '必填', trigger: 'blur' }],
        shopName: [{ required: true, message: '必填', trigger: 'blur' }],
        // orderNo: [{ required: true, message: '必填', trigger: 'blur' }],
        description: [{ required: true, message: '必填', trigger: 'blur' }],
        chatImgs: [{ required: true, message: '必填', trigger: 'blur' }],
        platformImgs: [{ required: true, message: '必填', trigger: 'blur' }],
        remark: [{ required: true, message: '必填', trigger: 'blur' }],
        snick: [{ required: true, message: '必填', trigger: 'blur' }],
        userName: [{ required: true, message: '必填', trigger: 'blur' }],
        groupName: [{ required: true, message: '必填', trigger: 'blur' }]
      }
    };
  },
  async mounted() {
    if (this.pictures) {
      this.chatUrls = this.pictures.split(',').map((item, i) => {
        return {
          url: item,
          name: `聊天截图${i + 1}`
        }
      });

    }
    this.editPriceVisible = true

    if (this.picturespt) {
      this.ptchatUrls = this.pictures.split(',').map((item, i) => {
        return {
          url: item,
          name: `聊天截图${i + 1}`
        }
      });

    }

    this.pteditPriceVisible = true

  },
  methods: {

    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init() {
      var date1 = new Date(); date1.setDate(date1.getDate() - 6);
      var date2 = new Date(); date2.setDate(date2.getDate());
      this.Filter.timerange = [];
      this.Filter.timerange[0] = date1;
      this.Filter.timerange[1] = date2;
    },
    //查看线上订单号记录
    showLogDetail(row) {
      this.dialogHisVisible = true;
      this.orderNo = row.orderNo;
    },

    // 查询
    onSearch() {
      this.$nextTick(() => {
        this.$refs.pager.setPage(1);
        this.gettrainplanList();
        this.getSelectOptionList();
      });
    },
    //头部下拉列表
    async getSelectOptionList() {
      const para = { ...this.Filter };
      if (this.Filter.timerange) {
        para.CreatedTimeStart = this.Filter.timerange[0];
        para.CreatedTimeEnd = this.Filter.timerange[1];
      }
      const param = {
        platform: para.platform,
        timeStart: para.timeStart,
        timeEnd: para.shopList
      }
      const resShop = await getBlacklistingBuyersShopNameList(param); //获得店铺下拉信息
      this.shopList = resShop.data;

      const resUserName = await getBlacklistingBuyersUserNameList(param) //获取正向聊天账号使用人
      this.uNameList = resUserName.data;

      const resGroupName = await getBlacklistingBuyersGroupNameList(param) //获取正向聊天分、分组
      this.gNameList = resGroupName.data;

      const resLastOperator = await getBlacklistingBuyersLastOperatorList(param) //获取正向聊天分、分组
      this.gLastOperator = resLastOperator.data;
    },

    getCondition() {

      const para = { ...this.Filter };
      if (this.Filter.timerange) {
        para.createdTimeStart = this.Filter.timerange[0];
        para.createdTimeEnd = this.Filter.timerange[1];
      }
      if (this.Filter.chatTime) {
        para.chatTimeStart = this.Filter.chatTime[0];
        para.chatTimeEnd = this.Filter.chatTime[1];
      }
      if (this.Filter.lastTime) {
        para.lastTimeStart = this.Filter.lastTime[0];
        para.lastTimeEnd = this.Filter.lastTime[1];
      }
      if (para.orderNoList)

        var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      return params;
    },

    async gettrainplanList() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await getBlacklistingBuyersPageList(params);

      res.data.list.map(item => {
        if (!item.chatImgs) {
          item.chatImgs = [];
        } else {
          if (item.chatImgs.indexOf(',') != -1) {
            item.chatImgs = item.chatImgs.split(",")
          } else {
            item.chatImgs = [item.chatImgs]
          }
        }
      })

      res.data.list.map(item => {
        if (!item.platformImgs) {
          item.platformImgs = [];
        } else {
          if (item.platformImgs.indexOf(',') != -1) {
            item.platformImgs = item.platformImgs.split(",")
          } else {
            item.platformImgs = [item.platformImgs]
          }
        }
      })
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const resData = res.data.list;
      this.tableData = resData;
      this.summaryarry = res.data.summary;
    },


    sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          orderBy: column.prop,
          isAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.onSearch();
    },

    async closeGroup() {
      this.$refs.uploadimgFile.setData([]);
      this.$refs.addForm.resetFields();
      // addForm
      this.addgroupdialogVisibleSyj = false
    },


    async addLists() {
      this.pictures = null;
      this.picturespt = null;
      this.chatUrls = [];
      this.ptchatUrls = [];
      this.addgroupdialogVisibleSyj = true
    },

    //添加
    async addgroup() {

      this.addForm.chatImgs = this.pictures;
      this.addForm.platformImgs = this.picturespt;

      var res = await addOrUpdateBlacklistingBuyersAsync(this.addForm)
      if (res?.success) {
        this.$message({ message: '已添加', type: "success" });
        this.addForm = {}
        this.addgroupdialogVisibleSyj = false
        this.$refs.uploadimgFile.setData([]);
        this.onSearch();
      } else {

      }
    },

    async closeUpGroup() {
      this.pictures = null;
      this.picturespt = null;
      this.chatUrls = [];
      this.ptchatUrls = [];
      this.$refs.uploadimgFile.setData([]);
      this.updategroupdialogVisibleSyj = false;

    },



    //修改弹出框控制方法
    async handleupdategroup(row) {
      this.updateForm = row
      let urls = row.chatImgs.map(url => url.toString());
      this.pictures = urls.join(','); // 这里使用逗号和空格来分隔每个 URL

      if (this.pictures) {
        this.chatUrls = this.pictures.split(',').map((item, i) => {
          return {
            url: item,
            name: `聊天截图${i + 1}`
          }
        });
      }

      let urlsPt = row.platformImgs.map(url => url.toString());
      this.picturespt = urlsPt.join(','); // 这里使用逗号和空格来分隔每个 URL
      if (this.picturespt) {
        this.ptchatUrls = this.picturespt.split(',').map((item, i) => {
          return {
            url: item,
            name: `聊天截图${i + 1}`
          }
        });

      }
      this.updategroupdialogVisibleSyj = true;

    },
    //修改
    async updategroup(row) {
      this.updateForm.chatImgs = this.pictures;
      this.updateForm.platformImgs = this.picturespt;

      var res = await addOrUpdateBlacklistingBuyersAsync(this.updateForm)
      console.log(545454)
      if (res?.success) {
        this.$message({ message: '操作成功', type: "success" });
        this.updateForm = {}
        // this.$refs.uploadimgFile.setData([]);
        this.updategroupdialogVisibleSyj = false
        this.onSearch();
        this.pictures = null;
        this.picturespt = null;
        this.chatUrls = [];
        this.ptchatUrls = [];
        this.$refs.uploadimgFile.setData([]);
      } else {
        // this.$refs.uploadimgFile.setData([]);
        // this.pictures = null;
        // this.picturespt = null;
        // this.chatUrls = [];
        // this.ptchatUrls = [];
        // this.$refs.uploadimgFile.setData([]);
      }
    },

    //删除
    async deletegroup(row) {
      var that = this;
      this.$confirm("此操作将删除此该数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          var res = await deleteBlacklistingBuyersAsync({ id: row.id })
          if (res?.success) {
            that.$message({ message: '已删除', type: "success" });
            this.onSearch();
          }

        });

    },
    async onExport() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      var res = await blacklistingBuyersExport(params);
      const aLink = document.createElement("a");
      let blob = new Blob([res], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute(
        "download",
        "客服拉黑买家_" + new Date().toLocaleString() + ".xlsx"
      );
      aLink.click();
    },

    getImg(data) {
      console.log("data1234567890", data)
      if (data) {
        this.chatUrls = data
        this.pictures = data.map(item => item.url).join(',')

      }

    },

    getImgpt(data) {
      if (data) {
        this.ptchatUrls = data
        this.picturespt = data.map(item => item.url).join(',')
      }

    },
    async callbackProCode(val) {
      this.Filter.orderNoList = val

    },
    chooseCode(val) {
      this.ids = []
      val.forEach(item => {
        this.ids.push(item.id)
      });
      this.batchtableData = val
    },
    closeDialog() {
      this.pictures = null;
      this.picturespt = null;
      this.chatUrls = [];
      this.ptchatUrls = [];
      this.$refs.uploadimgFile.setData([]);
      this.$emit("closeDialog");
    },

  },
};
</script>

<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

::v-deep .mycontainer {
  position: relative;
}

.first ::v-deep .el-form-item__label:before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}

.uptime {
  font-size: 14px;
  position: absolute;
  right: 30px;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 45px;
}

.mytable-scrollbar20221212 ::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
</style>
