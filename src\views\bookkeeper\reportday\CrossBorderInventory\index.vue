<template>
    <MyContainer>
        <el-tabs v-model="current" style="height: 95%;">
            <el-tab-pane label="TEMU全托-平台库存" name="tab1" style="height: 98%;">
                <TEMUFulfillmentPlatformInventory style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="SHEIN全托-平台库存" name="tab2" style="height: 98%;">
                <FulfillmentPlatformInventory style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="SHEIN自营-平台库存" name="tab3" style="height: 98%;">
                <SelfManagedPlatformInventory style="height: 100%;" />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>
<script>
import MyContainer from "@/components/my-container";
import TEMUFulfillmentPlatformInventory from './tab/TEMUFulfillmentPlatformInventory.vue'
import FulfillmentPlatformInventory from './tab/FulfillmentPlatformInventory.vue'
import SelfManagedPlatformInventory from './tab/SelfManagedPlatformInventory.vue'

export default {
    components: {
        MyContainer, TEMUFulfillmentPlatformInventory, FulfillmentPlatformInventory, SelfManagedPlatformInventory
    },
    data() {
        return {
            current: 'tab1'
        };
    },
    methods: {

    }
};
</script>

<style lang="scss" scoped></style>