<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="销售后台明细" name="tab1" style="height: 100%;">
                <saleDetailDouYin :filter="filter" ref="saleDetailDouYin" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="结算数据" name="tab2" style="height: 100%;">
                <settlementDetailDouYin :filter="filter" ref="settlementDetailDouYin" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="保费扣除" name="tab3" style="height: 100%;">
                <bfkcDouYin :filter="filter" ref="bfkcDouYin" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="侨宝特殊单平台补贴" name="first5" style="height: 100%;" lazy>
                <QiaobaoSpecialSinglePlatformSubsidy ref="QiaobaoSpecialSinglePlatformSubsidy" style="height: 100%;" />
            </el-tab-pane>
        </el-tabs>
    </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";
import saleDetailDouYin from '@/views/bookkeeper/details/saleDetailDouYin.vue'
import settlementDetailDouYin from '@/views/bookkeeper/details/settlementDetailDouYin.vue'
import bfkcDouYin from '@/views/bookkeeper/details/bfkcDouYin.vue'
import QiaobaoSpecialSinglePlatformSubsidy from "@/views/bookkeeper/details/QiaobaoSpecialSinglePlatformSubsidy.vue"

export default {
    name: "Users",
    components: { MyContainer, saleDetailDouYin, settlementDetailDouYin, bfkcDouYin, QiaobaoSpecialSinglePlatformSubsidy },
    data() {
        return {
            that: this,
            pageLoading: '',
            filter: {
            },
            shopList: [],
            userList: [],
            groupList: [],
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            activeName: 'tab1'
        };
    },
    mounted() {

    },
    methods: {


    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
