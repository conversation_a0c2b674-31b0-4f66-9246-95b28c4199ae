<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
         <el-form-item label="姓名:">
          <el-input v-model="filter.userName" placeholder="姓名"/>
        </el-form-item> 
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
 
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
      :tableData='list'    :tableCols='tableCols'
      :tableHandles='tableHandles' :showsummary='false'
      :loading="listLoading">
    </ces-table>
    
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getlist"
      />
    </template>

    <el-drawer
      :title="formtitle"
      :modal="false"
      :wrapper-closable="true"
      :modal-append-to-body="false"
      :visible.sync="addFormVisible"
      direction="btt"
      size="'auto'"
      class="el-drawer__wrapper"
      style="position:absolute;"
    >
    <form-create ref="formcreate" :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
      <div class="drawer-footer">
        <el-button @click.native="addFormVisible = false">取消</el-button>
        <my-confirm-button type="submit"  :loading="addLoading" @click="onAddSubmit" />
      </div>
    </el-drawer>
  </my-container>
</template>

<script>
import { addoreditDirectorGroup, getDirectorGroupById} from '@/api/operatemanage/base/shop'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatYesornoBool} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'id',label:'编号', width:'180',sortable:'custom'},
      {istrue:true,prop:'userName',label:'姓名', width:'150',sortable:'custom'},
      {istrue:true,prop:'enabled',label:'是否启用', width:'100',formatter:(row)=>formatYesornoBool(row.enabled)},
      {istrue:true,type:'button',btnList:[{label:"编辑",handle:(that,row)=>that.onEdit(row)}]}
     ];
const tableHandles1=[
        {label:"新增", handle:(that)=>that.onAdd()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: {
        name: ''
      },
      list: [],
      pager:{OrderBy:"id",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      autoform:{
               fApi:{},
                //options:{onSubmit:(formData)=>{alert(JSON.stringify(formData))}},
                option:{submitBtn:false,global: {'*': {props: {  disabled: true },col: { span: 8 }}}},
                rule:[{type:'hidden',field:'id',title:'id',value: '0'},
                      {type:'input',field:'userName',title:'姓名',validate: [{type: 'string', required: true, message:'请输入姓名'}]},
                      {type:'select',field:'enabled',title:'是否启用', validate: [{type: 'boolean', required: true, message:'请选择'}],value: false,options: [{value:false, label:'否'},{value:true, label:'是'}]},
                ]
        },
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,     
      deleteLoading: false,
      formtitle:"新增",
    }
  },
  mounted() {
    this.getlist()
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {
        ...pager,
        ... this.filter
      }
      this.listLoading = true
      const res = await pageDirectorGroup(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    async onEdit(row) {
      this.formtitle='编辑';
      this.addFormVisible = true
      const res = await getDirectorGroupById( row.id )
      await this.autoform.fApi.setValue(res.data)
    },
    onAdd() {
      this.formtitle='新增';
      this.addFormVisible = true
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
          this.autoform.fApi.resetFields()
    },
    async onEditSubmit() {
      this.addFormVisible = true
      await onAddSubmit();
    },
    async onAddSubmit() {
      this.addLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData()
          const res = await addoreditDirectorGroup(formData)
          this.getlist()
          console.log(formData)
        }else{
          //todo 表单验证未通过
        }
     })
     this.addLoading=false;
    },
    deleteValidate(row) {
      let isValid = true
      if (row && row.name === 'admin') {
        this.$message({
          message: row.description + '，禁止删除！',
          type: 'warning'
        })
        isValid = false
      }
      return isValid
    },
    async onDelete(row) {
      row._loading = true
      const res = await deletebyid(row.id)
      row._loading = false
      if (!res?.success) {
        return
      }
      this.$message({
        message: this.$t('admin.deleteOk'),
        type: 'success'
      })
      this.getlist()
    },
    selsChange: function(sels) {
      this.sels = sels
    }
  }
}
</script>
