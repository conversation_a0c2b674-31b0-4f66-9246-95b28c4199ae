<template>
    <container v-loading="pageLoading">
        <!-- <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="分公司:">
                    <el-select filterable v-model="filter.company" collapse-tags clearable placeholder="分公司"
                        style="width: 100px">
                        <el-option key="义乌" label="义乌" value="义乌"></el-option>
                        <el-option key="南昌" label="南昌" value="南昌"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
            </el-form>
        </template> -->
        <!-- <ces-table ref="table" :that='that' :isIndex='true' @sortchange='sortchange' @select='selectchange'
            @cellclick='cellclick' :hasexpand='true' :tableData='list' :tableCols='tableCols'
            :tableHandles='tableHandles' :loading="listLoading" :showsummary='true' :summaryarry='summaryarry'>
        </ces-table> -->
        <vxetablebase :id="'goodsCostPriceChgSumList202301031318001'" :tableData='list' :tableCols='tableCols'
            :showsummary='true' :summaryarry='summaryarry' :loading='listLoading' :border='true' :that="that"
            :tableHandles='tableHandles' ref="vxetable1" @sortchange='sortchange'
            style="width: 100%; height: 645px; margin: 0">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="margin: 0;">
                        {{ lastUpdateTime }}
                    </el-button>
                </el-button-group>
            </template>
        </vxetablebase>
        <my-pagination :sizes="[50, 100, 200, 300]" :page-size="300" ref="pager" :total="detailTotal"
            @page-change="detailPagechange" @size-change="detailSizechange" style="margin-top: 40px;" />

    </container>
</template>

<script>
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { GetCostPriceListByBrandSum, exportCostPriceListByBrandSumAsync, updateCostPriceState } from "@/api/inventory/basicgoods"
import { getPurchaseDataSplitAmountStat, exportPurchaseDataSplitAmountStat } from "@/api/inventory/purchaseData"
const tableCols = [
    { istrue: true, prop: 'brandName', label: '采购员', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'dDeptName', label: '岗位', width: '120' },
    { istrue: true, prop: 'company', label: '分公司', width: '120' },
    { istrue: true, prop: 'purDept', label: '架构', width: '120' },
    { istrue: true, prop: 'upAmount', label: '合计涨价', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'downAmount', label: '合计降价', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'mergeAmount', label: '冲抵后金额', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'allotUpAmount', label: '调货涨价', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'allotDownAmount', label: '调货降价', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'finalAmount', label: '实际总金额', width: 'auto', sortable: 'custom', },
];
const tableHandles = [
    { label: "导出", handle: (that) => that.onExport() },
];

export default {
    name: 'YunHanAdminGoodsCostPriceChgSum',
    components: { cesTable, container, MyConfirmButton, vxetablebase },
    props: {
        filter: {},
        lastUpdateTime: '',
    },

    data() {
        return {
            that: this,
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            // pager: { OrderBy: "downAmount", IsAsc: false },
            summaryarry: {},
            total: 0,
            sels: [],
            pageLoading: false,
            listLoading: false,
            ListInfo: {
                currentPage: 1,//当前页
                pageSize: 300,//每页条数
                orderBy: "downAmount",
                isAsc: false
            },
            detailTotal: 0,
        };
    },

    async mounted() {

    },

    methods: {
        detailSizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getlist();
        },
        //当前页改变
        detailPagechange(val) {
            this.ListInfo.currentPage = val;
            this.getlist();
        },
        async onExport() {
            var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            const params = { ... this.filter }

            if (params === false) {
                return;
            }
            var res = await exportPurchaseDataSplitAmountStat(params);
            loadingInstance.close();
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '采购汇总导出_' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        async onSearch() {
            this.getlist('search');
        },
        async getlist(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            const params = { ... this.filter, ...this.ListInfo }
            this.listLoading = true
            const { data, success } = await getPurchaseDataSplitAmountStat(params)
            this.listLoading = false
            if (!success) return
            this.list = data.list
            this.detailTotal = data.total;
            this.summaryarry = data.summary;
        },
        async changeStatus(row) {
            this.$confirm('此操作将设置为调货, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                var res = await updateCostPriceState({ id: row.id, isAllot: row.isAllot })
                if (!res?.success) {
                    row.isAllot = !row.isAllot;
                    return;
                }
                this.$message({
                    type: 'success',
                    message: '操作成功!'
                });
            }).catch(() => {
                row.isAllot = !row.isAllot;
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });

        },
        async cellclick(row, column, cell, event) {

        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getlist()
            }
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>

<style lang="scss" scoped></style>
