<template>
  <container>
    <template #header>
      <div>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;">
            <el-date-picker style="width: 200px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-button>

          <el-button style="padding: 0;">
            <el-select filterable clearable v-model="filter.platform" placeholder="平台" style="width: 120px"
              @change="onchangeplatform">
              <el-option label="天猫" :value="1"> </el-option>
              <el-option label="拼多多" :value="2"> </el-option>
              <el-option label="阿里巴巴" :value="4"> </el-option>
              <el-option label="抖音" :value="6"> </el-option>
              <el-option label="京东" :value="7"> </el-option>
              <el-option label="淘工厂" :value="8"> </el-option>
              <el-option label="淘宝" :value="9"> </el-option>
              <el-option label="苏宁" :value="10"> </el-option>
            </el-select>
          </el-button>

          <el-button style="padding: 0;">
            <el-select filterable clearable v-model="filter.shopCode" placeholder="店铺" style="width: 160px">
              <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
              </el-option>
            </el-select>
          </el-button>

          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组" style="width: 120px">
              <el-option key="无运营组" label="无运营组" :value="0"></el-option>
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>

          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.operateSpecialUserId" collapse-tags clearable placeholder="运营专员"
              style="width: 120px">
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>

          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.groupType2" collapse-tags clearable placeholder="分类汇总"
              style="width: 90px">
              <el-option label="按ID汇总" :value="1" />
              <el-option label="按运营组汇总" :value="2" />
            </el-select>
          </el-button>

          <el-button type="primary" style="margin-left: 10px; " @click="onSearch">查 询</el-button>
        </el-button-group>
      </div>
    </template>
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :tableData='list' @sortchange='sortchange'
      :tableCols='tableCols' :isSelection="false" :tableHandles='tableHandles' :loading="listLoading"
      :summaryarry="summaryarry" :isSelectColumn="false">
      <el-table-column width="50" label="操作" fixed="right" v-if="filter.groupType2 != 1 && filter.groupType2 != 2">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.analysisTime == null && checkPermission('api:bookkeeper:ContinuousProfitAnalysis:SaveContinuousNoProfitAnalysisAsync') && filter.groupType2 != 1 && filter.groupType2 != 2"
            type="text" @click="onAnalysis(scope.row)">分析</el-button>
        </template>
      </el-table-column>
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <el-dialog title="负利润分析" :visible.sync="analysisForm.visible" width="800px" v-dialogDrag>
      <span>
        <template>
          <el-form class="ad-form-query" :model="analysisForm.data" ref="chooseForm" @submit.native.prevent
            label-width="120px">
            <el-form-item label="商品ID:">
              {{ analysisForm.data.proCode }}
            </el-form-item>
            <el-form-item label="商品名称:">
              {{ analysisForm.data.productName }}
            </el-form-item>
            <el-form-item label="时间:">
              {{ analysisForm.data.yearMonthDay }}
            </el-form-item>
            <el-form-item label="负利润原因:">
              <!-- <yh-quill-editor :value.sync="analysisForm.data.noProfitReason">
              </yh-quill-editor> -->
              <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" :maxlength="200"
                v-model="analysisForm.data.noProfitReason" @input="changetextarea($event)">
              </el-input>
            </el-form-item>
            <el-form-item label="负利润解决方案:">
              <!-- <yh-quill-editor :value.sync="analysisForm.data.noProfitSolution">
              </yh-quill-editor> -->
              <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" :maxlength="200"
                v-model="analysisForm.data.noProfitSolution" @input="changetextarea($event)">
              </el-input>
            </el-form-item>
          </el-form>
        </template>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveAnalysis">保存</el-button>
        <el-button @click="analysisForm.visible = false">取消</el-button>
      </span>
    </el-dialog>
  </container>
</template>

<script>
import container from '@/components/my-container'
import cesTable from "@/components/Table/table.vue";
import { getContinuousNoProfitAnalysisRecordAsync, saveContinuousNoProfitAnalysisAsync } from "@/api/bookkeeper/continuousprofitanalysis"
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop'
import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'
import { formatLinkProCode } from "@/utils/tools";

const tableCols = [
  { istrue: true, prop: 'yearMonthDay', label: '年月日', tipmesg: '', width: '80', sortable: 'custom', },
  { istrue: true, prop: 'proCode', label: '产品Id', tipmesg: '', type: 'html', width: '110', sortable: 'custom', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
  { istrue: true, prop: 'platform', label: '平台', tipmesg: '', width: '60', sortable: 'custom', formatter: (row) => row.platformName },
  { istrue: true, prop: 'a.proCode', label: '产品', tipmesg: '', width: '200', sortable: 'custom', formatter: (row) => row.productName },
  { istrue: true, prop: 'shopCode', label: '店铺名称', tipmesg: '', sortable: 'custom', formatter: (row) => row.shopName },
  { istrue: true, prop: 'groupId', label: '运营组', tipmesg: '', width: '100', sortable: 'custom', formatter: (row) => row.groupName },
  { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', width: '100', tipmesg: '', sortable: 'custom', formatter: (row) => row.operateSpecialUserName },
  { istrue: true, prop: 'profit3', label: '毛3', tipmesg: '', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'consecutiveDays', label: '持续天数', width: '80', tipmesg: '', sortable: 'custom', formatter: (row) => row.consecutiveDays + 1 },
  { istrue: true, prop: 'noProfitReason', label: '负利润原因', tipmesg: '', sortable: 'custom' },
  { istrue: true, prop: 'noProfitSolution', label: '负利润解决方案', tipmesg: '', sortable: 'custom' },
  { istrue: true, prop: 'analysisTime', label: '分析时间', width: '160', tipmesg: '', sortable: 'custom' }
]

const tableHandles = [
  //{ label: "导入", handle: (that) => that.startImport() }
];

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
  name: 'ConsecutiveNoProfitAnalysis',
  components: { container, cesTable, YhQuillEditor },
  props: {},
  data() {
    return {
      that: this,
      list: [],
      pager: { OrderBy: null, IsAsc: false },
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      sels: [],
      uploadLoading: false,
      dialogVisible: false,
      listLoading: false,
      fileList: [],
      summaryarry: {},
      filter: {
        shopCode: null,
        proCode: null,
        groupId: null,
        operateSpecialUserId: null,
        platform: null,
        startDate: null,
        endDate: null,
        timerange: null,
        groupType2: null,
      },
      shopList: [],
      grouplist: [],
      directorlist: [],
      analysisForm: {
        visible: false,
        data: {
          yearMonthDay: null,
          productCode: null,
          productName: null,
          noProfitReason: null,
          noProfitSolution: null,
          platform: null
        }
      }
    };
  },

  async mounted() {
    await this.getShopList();
    let end = new Date();
    let start = new Date();
    start.setDate(start.getDate() - 7);
    this.filter.timerange = [formatTime(start, "YYYY-MM-DD"), formatTime(end, "YYYY-MM-DD")]
  },

  methods: {
    async getShopList() {
      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    async getlist() {
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }

      let pager = this.$refs.pager.getPager();
      let page = this.pager;
      const params = { ...pager, ...page, ... this.filter }
      if (params === false) {
        return;
      }
      this.listLoading = true
      const res = await getContinuousNoProfitAnalysisRecordAsync(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.list = data
      this.summaryarry = res.data.summary;
    },
    async nSearch() {
      await this.getlist()
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    },
    async onAnalysis(row) {
      this.analysisForm.data = {};
      this.analysisForm.data.yearMonthDay = row.yearMonthDay;
      this.analysisForm.data.proCode = row.proCode;
      this.analysisForm.data.productName = row.productName;
      this.analysisForm.data.noProfitReason = row.noProfitReason;
      this.analysisForm.data.noProfitSolution = row.noProfitSolution;
      this.analysisForm.data.platform = row.platform;
      this.analysisForm.visible = true;
    },
    async saveAnalysis() {
      const res = await saveContinuousNoProfitAnalysisAsync(this.analysisForm.data)
      if (!res?.success) {
        this.$message.error("保存失败！")
        return;
      }
      this.$message.success("保存成功！");
      this.analysisForm.visible = false;
      await this.getlist();
    },
    changetextarea() {
      this.$forceUpdate()
    },
    async onchangeplatform(val) {
      this.filter.platform = val;
      const res1 = await getshop({ platform: val, CurrentPage: 1, PageSize: 10000 });
      this.shopList = res1.data.list;
      this.filter.shopCode = null;
    }
  }
};
</script>

<style lang="scss" scoped></style>