<template> 
 <my-container v-loading="pageLoading"> 
      <div class="xgylcz">
        <span>
          <el-button style="width:200px;" size="mini" type="primary"  @click="downImgXg">下载效果图</el-button>
          <el-button style="width:200px;" size="mini" type="primary"  @click="downImgSzg">下载设计稿</el-button>
          </span>
      </div>
      <div class="xgylcz2" v-if="listtype>2">
        <div><span><el-button style="width:200px;" size="mini" type="primary" @click="onPackagingCompression">开始打包</el-button><el-button style="width:200px;" size="mini" type="primary"  @click="downPackagingCompression">下载源文件</el-button></span></div>
        <div><span>上次打包时间：<span>{{packTime}}</span></span></div>
      </div>
      <div class="xgxgt" :key="idx" v-for="(i, idx) in imgList" >
          <img :src="i" alt="" width="80%"  @click="imgclick(idx)" />
      </div> 
      <el-image-viewer :nodown="false" v-if="showGoodsImage" :initialIndex="imgindex" :url-list="imgList"  :wrapperClosable="false" :on-close="closeFunc" style="z-index:9999;" />
    </my-container>
</template>
<script>
import "@/router/index";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button"; 
import { getPageDetailImgInfo,getPackagingCompressionTaskStatus,packagingCompressionTask } from '@/api/media/packdesgin';
 
import ElImageViewer from '@/views/media/shooting/imageviewer.vue'; 
function pause(msec) {
    return new Promise(
        (resolve, reject) => {
            setTimeout(resolve, msec || 500);
        }
    );
}
export default {
     components: { MyContainer,MyConfirmButton,ElImageViewer},
     props:{
          rowinfo:{ type: Number, default:0},  
          islook:{ type: Boolean, default:false },
          isOverList:{ type: Boolean, default:false },
          listtype:{ type: Number, default:1}, 
     },
     data() {
          return {
               imgindex:0,
               showGoodsImage:false,
               imgList:[],
               hasphoto:0,
               hasvedio:0,
               hasmicroDetail:0,
               hasdetail:0,
               hasmodelPhoto:0,
               hasmodelVideo:0,
               OutComInfodrawer:false,
               isPackOver:false,
               downUrl:null,
               packTime:null,
               pageLoading:false,   
               radioselcet:"链接套图",
               imglistinfo:[],
               
          };
     },

     async mounted() {  
          this.getSubScoreInfo();
     }, 
     methods: { 
           //完成表单界面关闭图片
          async closeFunc() {
               this.showGoodsImage = false;
          },
          imgclick(index){ 
               this.imgindex = index;
               this.showGoodsImage = true;
          },
          //获取上传图片
          async getSubScoreInfo(){
            this.pageLoading=true;
            var res =await getPageDetailImgInfo({taskid:this.rowinfo});
            if(res?.success){ 
               if(res.data.directImgList.length>0){
                    
                     this.imglistinfo = res.data.directImgList;
                    this.imgList =[]; 
                    for(let num in this.imglistinfo )
                    {  
                         this.imgList.push(this.imglistinfo[num].url);
                    }
               }
            }
            res = await getPackagingCompressionTaskStatus({packageDesignTaskId:this.rowinfo});
            if(res?.success)
            {  
               if(res.data !=null)
               {
                    this.isPackOver =true;
                    this.packTime= res.data.createdTime;
                    this.downUrl = res.data.pathOrErr;
               }
            }
            this.pageLoading=false;
        }, 
        async onPackagingCompression()
        {
          this.$confirm('本次打包将会,覆盖上次的打包文件！是否执行打包操作').then(  async() =>   
          {
               this.pageLoading=true;
               var res = await packagingCompressionTask({packageDesignTaskId:this.rowinfo});
                    if(res?.success){
                         this.$message({ message: res.data, type: "success" });
                    }
                    this.isPackOver = false;
               this.pageLoading =false;
               }).catch(async()=> { 
               this.pageLoading =false;
          });
        
        },
        //
        downPackagingCompression(){
          if(!this.isPackOver){
               this.$message({ message: "请先打包", type: "info" });
               return;
          }
          const domain = "/api/media/statics/"  + this.downUrl;
          window.open(domain,"_blank")  
        },  
        async   downImgXg(){
          for(let num in this.imglistinfo)
            { 
                if(this.imglistinfo[num].type ==1  ){
                    await pause(500);
                    await this.downfile(this.imglistinfo[num]);
                }  
            }
        },
        async downImgSzg(){
          for(let num in this.imglistinfo)
            { 
                if(this.imglistinfo[num].type ==4  ){
                    await pause(500);
                    await this.downfile(this.imglistinfo[num]);
                }  
            }
        },

        //下载文件
        async downfile(file)
        { 
            var xhr = new XMLHttpRequest();
            xhr.open('GET', file.url, true);
            xhr.responseType = 'arraybuffer'; // 返回类型blob
            xhr.onload = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    let blob = this.response;
                    console.log(blob);
                    // 转换一个blob链接
                    // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，
                    // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定
                    let downLoadUrl = window.URL.createObjectURL(new Blob([blob], {type: 'video/mp4'}));
                    // 视频的type是video/mp4，图片是image/jpeg
                    // 01.创建a标签
                    let a = document.createElement('a');
                    // 02.给a标签的属性download设定名称
                    a.download = file.name;
                    // 03.设置下载的文件名
                    a.href = downLoadUrl;
                    // 04.对a标签做一个隐藏处理
                    a.style.display = 'none';
                    // 05.向文档中添加a标签
                    document.body.appendChild(a);
                    // 06.启动点击事件
                    a.click();
                    // 07.下载完毕删除此标签
                    a.remove();
                };
            };
            xhr.send();
        },
   },
};
</script>
<style lang="scss" scoped>
.xgylbt {
  background-color: rgb(255, 255, 255);
  font-size: 18px;
  color: #666;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 20px 35px 10px 35px;
}

.xgylcz {
  width: 100%;
  height: 60px;
  line-height: 50px;
  background-color: #fff;
  box-shadow: 0px 3px 5px #eeeeee;
  box-sizing: border-box;
  padding: 5px 60px;
  text-align: center;
}
.xgylcz2 {
  width: 100%;
  line-height: 30px;
  background-color: #fff;
  box-shadow: 0px 3px 5px #eeeeee;
  box-sizing: border-box;
  padding: 0 60px;
  text-align: center;
  font-size: 14px;
  color: #999;
}

.xgxgt, .xgsjg {
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  padding: 5px 60px;
  text-align: center;
  margin-top: 20px;

}
</style>
