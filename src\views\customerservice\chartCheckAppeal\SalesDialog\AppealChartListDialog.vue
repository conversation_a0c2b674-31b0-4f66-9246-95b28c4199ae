<template>
    <el-dialog title="聊天记录" :visible.sync="isShow" width="70%" :before-close="closeDialog" :v-if="isShow" v-dialogDrag>
        <div class="">
            <el-descriptions size="middle" class="margin-top" title="" :column="3">
                <el-descriptions-item label="平台/店铺"> {{ platformName }} / {{ dataJson?.shopName
                    }}</el-descriptions-item>
                <el-descriptions-item label="线上单号">{{ dataJson?.orderNo }}</el-descriptions-item>
                <el-descriptions-item label="创建时间">{{ conversationTime }}</el-descriptions-item>
                <el-descriptions-item
                    v-if="interfaceType && dataJson?.afterSalesRemark != '' && dataJson?.afterSalesRemark != null"
                    label="售后原因">{{ dataJson?.afterSalesRemark }}</el-descriptions-item>

            </el-descriptions>
        </div>
        <div style="height: 500px; overflow: auto" v-loading="isLoading">
            <div class="message-container" v-for="(message, index) in chartList" :key="index"
                :class="getMessageClass(message.userType)">
                <div v-if="message.userType == 3">
                    <div class="system-box">
                        <div class="system">
                            <div>系统消息{{ chartList.length }}</div>
                            <div style="margin-left: 20px">{{ message.recordTime }}</div>
                        </div>
                        <div style="color: #333" v-html="message.content"></div>
                    </div>
                </div>
                <div v-if="message.userType == 0" style=" width:100%">
                    <el-row>
                        <el-col :span="1">
                            <img src="@/assets/images/消费者.png" />
                        </el-col>
                        <el-col :span="23">
                            <div class="bubble">
                                <div style="display: flex; color: #999;margin-bottom: 5px;">
                                    <div>
                                        <span class="consumer">消费者</span>
                                        <span class="username"> {{ message.userName }}</span>
                                    </div>
                                    <div class="recordTime" style="margin-left: 10px">
                                        {{ message.recordTime }}
                                    </div>
                                </div>
                                <div v-if="isProbablyImage(message.content)">
                                    <el-image :src="message.content" :preview-src-list="[message.content]"></el-image>
                                </div>
                                <div v-else class="message msg_consumer" style="text-align: left"
                                    v-html="message.content"></div>
                            </div>
                        </el-col>
                    </el-row>

                </div>
                <div v-if="message.userType == 1" style=" width:100%">
                    <el-row>
                        <el-col :span="1">
                            <img src="@/assets/images/商家.png" />
                        </el-col>
                        <el-col :span="23">
                            <div class="bubble">
                                <div style="display: flex; color: #999;margin-bottom: 5px;">
                                    <div>
                                        <span class="merchant">商家</span>
                                        <span class="username"> {{ message.userName }}</span>
                                    </div>
                                    <div class="recordTime" style="margin-left: 10px">
                                        {{ message.recordTime }}
                                    </div>
                                </div>
                                <div v-if="isProbablyImage(message.content)">
                                    <el-image style="object-fit: cover;" :src="message.content"
                                        :preview-src-list="[message.content]"></el-image>
                                </div>
                                <div v-else class="message msg_merchant" v-html="message.content"></div>
                            </div>
                        </el-col>
                    </el-row>
                </div>
                <div v-if="message.userType == 2">
                    <div style="display: flex;align-items: left; color: #999;margin-bottom: 5px;">
                        <div class="name">
                            {{ message.userName }}
                        </div>
                        <div style="margin-left: 10px;line-height: 29px;">
                            {{ message.recordTime }}
                        </div>
                    </div>
                    <div class="avatar">
                        <div v-if="isProbablyImage(message.content)">
                            <el-image style="width: 500px; height: 500px" :src="message.content"
                                :preview-src-list="[message.content]"></el-image>
                        </div>
                        <div v-else class="message" style="margin-right: 10px; text-align: left; color: #333"
                            v-html="message.content"></div>
                    </div>
                </div>

            </div>
        </div>
        <my-pagination ref="chartPager" :total="chartTotal" @get-page="getChartList" />
        <template #footer>

            <div class="dialog-footer"
                style="display: flex; justify-content: flex-end; align-items: center; margin-right: 10px;">

              <el-form ref="formDataRef" label-width="80px" style="margin-right: 20px; margin-top: 25px;">
                <el-form-item label="备注" prop="">
                  <el-input :maxlength="50" v-model="reviewRemarkCopy" type="text"
                            style="width: 450px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"
                            :title="reviewRemarkCopy" :disabled="dataJson.appealStatus !== 1" v-if="totalROW.switchshow
                                ? checkPermission(['api:customerservice:UnPayOrderAppeal:AppealReviewAfterSales'])
                                : checkPermission(['api:customerservice:UnPayOrderAppeal:AppealReview'])" />
                </el-form-item>
              </el-form>


                <!-- 备注输入框 -->
                <!-- <el-form ref="formDataRef" label-width="100px" style="margin-right: 50px;">
                    <el-form-item label="备注" prop="">
                        <el-input show-word-limit :maxlength="50" v-model="reviewRemarkCopy" type="textarea" :rows="2"
                            style="width: 400px;" v-if="totalROW.switchshow
                                ? checkPermission(['api:customerservice:UnPayOrderAppeal:AppealReviewAfterSales'])
                                : checkPermission(['api:customerservice:UnPayOrderAppeal:AppealReview'])"
                            :disabled="dataJson.appealStatus !== 1" />
                    </el-form-item>
                </el-form> -->



              <div style="position: relative;">
                <el-button @click="btnChange('last')" type="primary"
                           :disabled="isLastButtonDisabled">查看上一个</el-button>
                <div style="position: absolute;right:-20px;top:-20px;cursor:pointer;">
                  <el-tooltip class="item" effect="dark" content="点击键盘的上箭头↑，可以快速查看上一个" placement="top"><i
                    class="el-icon-question"></i></el-tooltip>
                </div>
              </div>
              <div style="position: relative;margin-left:20px;">
                <el-button @click="btnChange('next')" type="primary"
                           :disabled="isNextButtonDisabled">查看下一个</el-button>
                <div style="position: absolute;right:-20px;top:-20px; cursor:pointer;">
                  <el-tooltip class="item" effect="dark" content="点击键盘的下箭头↓，可以快速查看下一个" placement="top"> <i
                    class="el-icon-question"></i></el-tooltip>
                </div>
              </div>

              <!-- 同意按钮 -->
              <el-button type="primary" @click="handleAgree" :disabled="dataJson.appealStatus !== 1"
                         style="margin-left: 20px;"
                         v-if="totalROW.switchshow ? checkPermission(['api:customerservice:UnPayOrderAppeal:AppealReviewAfterSales']) : checkPermission(['api:customerservice:UnPayOrderAppeal:AppealReview'])">
                同意
              </el-button>
              <!-- 拒绝按钮 -->
              <el-button type="primary" @click="handleReject" :disabled="dataJson.appealStatus !== 1"
                         style="margin-left: 20px;"
                         v-if="totalROW.switchshow ? checkPermission(['api:customerservice:UnPayOrderAppeal:AppealReviewAfterSales']) : checkPermission(['api:customerservice:UnPayOrderAppeal:AppealReview'])">
                拒绝
              </el-button>

            </div>

        </template>

      <el-descriptions size="middle" style="margin-top: 20px;" :column="2">
        <el-descriptions-item label="审核" style="flex: 1;">
          {{ filterInitialAuditType(dataJson.initialAuditType) }}
        </el-descriptions-item>
        <el-descriptions-item label="审核类型" style="flex: 1;"  v-if="dataJson.initialAuditType == 2">
          {{ dataJson.refuseInitialAuditType }}
        </el-descriptions-item>
        <el-descriptions-item label="责任人" style="flex: 1;"  v-if="!interfaceType">
          {{ dataJson?.person }}
        </el-descriptions-item>
        <el-descriptions-item label="退款原因稽查" style="flex: 1;"  v-if="!interfaceType">
          {{ dataJson.reasonForRefund }}
        </el-descriptions-item>
        <el-descriptions-item label="责任客服" style="flex: 1;"  >
          {{ dataJson.initialResponsibleName }}
        </el-descriptions-item>
        <el-descriptions-item label="说明" style="flex: 1;"  >
          {{ dataJson.initialAuditRemark }}
        </el-descriptions-item>
        <el-descriptions-item label="审核人" style="flex: 1;"  >
          {{ dataJson.initialOperator }}
        </el-descriptions-item>
        <el-descriptions-item label="审核凭证" style="flex: 1; display: flex; align-items: center;"  >
<!--          <span>
                    <el-image v-if="dataJson?.initialAuditImgs" :src="dataJson?.initialAuditImgs"
                              :preview-src-list="[dataJson?.initialAuditImgs]"></el-image>
                </span>-->

          <div style="display: flex; flex-direction: row; flex-wrap: wrap; height: 60px; width: 220px; overflow: auto;align-items: center">
            <div v-for="(image, index) in iniImgsSplitList" :key="index">
              <el-image v-if="image" :src="image" style="height: auto;width: 40px;margin-left: 5px;vertical-align: center"
                        :preview-src-list="[image]"></el-image>
            </div>
          </div>
        </el-descriptions-item>

      </el-descriptions>

        <el-descriptions size="middle" style="margin-top: 20px;" :column="2">
            <el-descriptions-item label="新责任客服" style="flex: 1;">
                {{ dataJson?.changeResponsibleName }}
            </el-descriptions-item>
            <el-descriptions-item label="原因" style="flex: 1;">
                {{ dataJson?.remark }}
            </el-descriptions-item>
            <el-descriptions-item label="查看附件" style="flex: 1; display: flex; align-items: center;">
                <span>
                    <el-image v-if="dataJson?.imgs" :src="dataJson?.imgs"
                        :preview-src-list="[dataJson?.imgs]"></el-image>
                </span>
            </el-descriptions-item>
        </el-descriptions>

      <el-collapse :value="activeReviewIndex" @change="(val) => activeReviewIndex = val">
        <el-collapse-item :name="0" v-if="reviewList.length > 0">
          <template #title>
            <span>审核信息 1</span>
          </template>
          <el-descriptions size="middle" style="margin-top: 20px;" :column="2">
            <el-descriptions-item label="审核" style="flex: 1;">
              {{ filterInitialAuditType(reviewList[0].initialAuditType) }}
            </el-descriptions-item>
            <el-descriptions-item label="审核类型" style="flex: 1;"  v-if="reviewList[0].initialAuditType == 2">
              {{ reviewList[0].refuseInitialAuditType }}
            </el-descriptions-item>
            <el-descriptions-item label="数据编码" style="flex: 1;">
              {{ reviewList[0].conversationId }}
            </el-descriptions-item>
            <el-descriptions-item label="审核人" style="flex: 1;"  >
              {{ reviewList[0].initialOperator }}
            </el-descriptions-item>
            <el-descriptions-item label="审核日期" style="flex: 1;"  >
              {{ reviewList[0].initialOperatorTime }}
            </el-descriptions-item>
            <el-descriptions-item label="审核凭证" style="flex: 1; display: flex; align-items: center;"  >

              <div style="display: flex; flex-direction: row; flex-wrap: wrap; height: 60px; width: 220px; overflow: auto;align-items: center">
                <div v-for="(image, index) in reviewList[0].initialAuditImgs ? reviewList[0].initialAuditImgs.split(',') : []" :key="index">
                  <el-image v-if="image" :src="image" style="height: auto;width: 40px;margin-left: 5px;vertical-align: center"
                            :preview-src-list="[image]"></el-image>
                </div>
              </div>
            </el-descriptions-item>

              <el-descriptions-item label="责任客服" style="flex: 1;"  >
                {{ reviewList[0].initialResponsibleName }}
              </el-descriptions-item>
              <el-descriptions-item label="说明:" style="flex: 1;" >
                {{ reviewList[0].initialAuditRemark }}
              </el-descriptions-item>

          </el-descriptions>
        </el-collapse-item>

        <el-collapse-item v-for="(review, index) in reviewList.slice(1)" :key="index" :name="(index + 1).toString()">
          <template #title>
            <span>审核信息 {{ index + 2 }}</span>
          </template>

          <el-descriptions size="middle" style="margin-top: 20px;" :column="2">
            <el-descriptions-item label="审核" style="flex: 1;">
              {{ filterInitialAuditType(review.initialAuditType) }}
            </el-descriptions-item>
            <el-descriptions-item label="审核类型" style="flex: 1;"  v-if="reviewList[0].initialAuditType == 2">
              {{ review.refuseInitialAuditType }}
            </el-descriptions-item>
            <el-descriptions-item label="数据编码" style="flex: 1;">
              {{ review.conversationId }}
            </el-descriptions-item>
            <el-descriptions-item label="审核人" style="flex: 1;"  >
              {{ review.initialOperator }}
            </el-descriptions-item>
            <el-descriptions-item label="审核日期" style="flex: 1;"  >
              {{ review.initialOperatorTime }}
            </el-descriptions-item>
            <el-descriptions-item label="审核凭证" style="flex: 1; display: flex; align-items: center;"  >

              <div style="display: flex; flex-direction: row; flex-wrap: wrap; height: 60px; width: 220px; overflow: auto;align-items: center">
                <div v-for="(image, index) in review.initialAuditImgs ? review.initialAuditImgs.split(',') : []" :key="index">
                  <el-image v-if="image" :src="image" style="height: auto;width: 40px;margin-left: 5px;vertical-align: center"
                            :preview-src-list="[image]"></el-image>
                </div>
              </div>
            </el-descriptions-item>

            <el-descriptions-item label="责任客服" style="flex: 1;"  >
              {{ review.initialResponsibleName }}
            </el-descriptions-item>
            <el-descriptions-item label="说明:" style="flex: 1;" >
              {{ review.initialAuditRemark }}
            </el-descriptions-item>


          </el-descriptions>
        </el-collapse-item>
      </el-collapse>

    </el-dialog>
</template>
<script>
import {
    appealReview, appealReviewAfterSales
} from "@/api/customerservice/chartAppeal";
import { getChartList } from "@/api/customerservice/unpaidorder";
import { getUnPayOrderById,GetUnpayOrderSalesList} from "@/api/customerservice/chartCheck";

import { formatTime } from "@/utils";
import { downloadLink } from "@/utils/tools";
export default {
    props: {
        isShow: {
            type: Boolean,
            default: false,
        },
        totalROW: { type: Object, default: () => { } },
        // dataJson:{
        //     appealStatus:1,
        // }

    },
    data() {
        return {
            chartList: [],
            chartTotal: 0,
            keyWord: null,
            isLoading: false,
            platform: null,
            dataJson: null,
            tableData: [],
            isLastButtonDisabled: false,
            isNextButtonDisabled: false,
            interfaceType: true,// true：售前 false：售后
            reviewRemarkCopy: null,
            reviewList: [],
            activeReviewIndex: [0]
        };
    },
    created() {
        document.addEventListener('keydown', this.handleArrowUp);
        // 初始化时将 dataJson.reviewRemark 的值赋给 reviewRemarkCopy
    },

    watch: {

        isShow(newVal, oldVal) {
            if (newVal) {
                this.$nextTick(() => {
                    this.$refs.chartPager.setPage(1);
                    this.getChartList();
                });
                if (this.dataJson.reviewRemark != null) {
                    console.log("isShow1111")
                    this.reviewRemarkCopy = this.dataJson.reviewRemark;
                } else {
                    console.log("isShow2222")
                    this.reviewRemarkCopy = null;
                }
            }

        },
    },
    computed: {

        platformName()//平台初始化
        {
            let platformList = [
                { name: "拼多多", value: 2 },
                { name: "抖音", value: 6 },
                { name: "天猫", value: 1 },
                { name: "淘工厂", value: 8 },
                { name: "淘宝", value: 9 },
            ]
            if (this.dataJson?.platform) {
                return platformList.filter(item => item.value == this.dataJson?.platform)[0].name
            } else {
                return ""
            }
        },
        conversationTime() //日期转换
        {
            return this.dataJson?.conversationTime ? formatTime(this.dataJson?.conversationTime, "YYYY-MM-DD") : ""
        },
      iniImgsSplitList() //审核图片分割
      {
        return this.dataJson?.initialAuditImgs ? this.dataJson?.initialAuditImgs.split(",") : "";
      },

    },
    methods: {

        imgSplitList() {
            //图片分割
            return this.dataJson?.imgs
                ? this.dataJson?.imgs.split(",")
                : "";
        },
        filterInitialAuditType(type) {
          let name = ''
          if (type == 1) {
            name = '合格'
          } else if (type == 2) {
            name = '不合格'
          }
          return name;
        },
        handleArrowUp(event) {
            if (!this.isShow) {
                return
            }
            if (event.key === 'ArrowUp' && !this.isLastButtonDisabled) {
                this.btnChange('last');
            }
            if (event.key === 'ArrowDown' && !this.isNextButtonDisabled) {
                this.btnChange('next');
            }
        },
        async getChartList() {
            this.isLoading = true;
            var pager = this.$refs.chartPager.getPager();
            let data = {
                platform: this.platform,
                keyWordType: 2,
                keyWord: this.keyWord,
                ...pager,
            };
            const res = await getChartList(data);
            this.chartTotal = res.data.total;
            this.chartList = res.data.list;
            await this.buttonDisabled()//按钮是否禁用
            this.isLoading = false;
        },
        async buttonDisabled() { //按钮是否禁用
            this.isLastButtonDisabled = false;
            this.isNextButtonDisabled = false;
            const index = this.tableData.indexOf(this.dataJson);
            if (index == 0) {
                this.isLastButtonDisabled = true;
            }
            if (index == this.tableData.length - 1) {
                this.isNextButtonDisabled = true;
            }
        },
        //消息框样式动态选择
        getMessageClass(isSent) {
            let className = ""
            switch (isSent) {
                case 0:// 用户
                    className = "message-container-left"
                    break;
                case 1:// 客服
                    className = "message-container-right"
                    break;
                case 2:// 机器人
                    className = "message-container-right"
                    break;
                case 3:// 系统
                    className = "message-container-left"
                    break;
            }
            return className
        },
        isProbablyImage(url) {
            return (url.match(/\.(jpeg|jpg|gif|png)$/) != null)
        },
        onSubmitDot() {
            this.closeDialog();
        },
        closeDialog() {
            this.$emit("closeDialog");
        },
        async btnChange(last) {//查看上一个、查看下一个
            const index = this.tableData.indexOf(this.dataJson);
            this.$refs.chartPager.setPage(1);
            let info;
            if (last == 'last') {
                info = this.tableData[index - 1]
            } else if (last == 'next') {
                info = this.tableData[index + 1]
            }
            this.dataJson = info;
            this.keyWord = info.conversationId;
            this.platform = info.platform;
            const res = await getUnPayOrderById({ id: this.dataJson.conversationId });
            if (res.success) {
              const thisData = res.data;
              console.log("thisData", thisData);
              if (thisData) {
                // 将接口数据插入
                this.dataJson.initialAuditType = thisData.initialAuditType;
                this.dataJson.refuseInitialAuditType = thisData.refuseInitialAuditType;
                this.dataJson.initialResponsibleName = thisData.responsibleName;
                this.dataJson.initialAuditRemark = thisData.initialAuditRemark;
                this.dataJson.initialOperator = thisData.initialOperator;
                this.dataJson.initialAuditImgs = thisData.initialAuditImgs;
                this.dataJson.person = thisData.person;
                this.dataJson.reasonForRefund = thisData.reasonForRefund;
              }
            }
            if (this.dataJson.reviewRemark != null) {
              console.log("11111");
              this.reviewRemarkCopy = this.dataJson.reviewRemark;
            } else {
              console.log("22222");
              this.reviewRemarkCopy = null;
            }
          //获取是否已经审核的数据
          if (this.dataJson.conversationUserId) {
            var params = {
              conversationUserId: this.dataJson.conversationUserId,
              conversationId: this.dataJson.conversationId,
              salesType: 0,
              shopId: this.dataJson.shopId,
              auditState:2,
              orderBy: "createdTime",
              isAsc: false
            }
            const res = await GetUnpayOrderSalesList(params);
            if (!res?.success) {
              return;
            }
            this.reviewList = res.data.list;
          } else {
            this.reviewList = [];
          }
            await this.getChartList();
            await this.buttonDisabled()//按钮是否禁用
        },
        handleAgree() {
            if (this.dataJson.appealStatus === 1) {
                // 执行同意逻辑
                this.appealResultLog(2);
                this.dataJson.appealStatus = 2; // 或者设置为其他值以禁用按钮
                this.dataJson.reviewRemark = this.reviewRemarkCopy;
            }
        },
        handleReject() {
            if (this.dataJson.appealStatus === 1) {
                // 执行拒绝逻辑
                this.appealResultLog(3);
                this.dataJson.appealStatus = 3; // 或者设置为其他值以禁用按钮
                this.dataJson.reviewRemark = this.reviewRemarkCopy;
            }
        },

        async appealResultLog(num) {
            const params = {
                reviewType: num,
                conversationIdList: [this.dataJson.conversationId],
                reviewRemark: this.reviewRemarkCopy
            }
            var res = this.totalROW.switchshow ? await appealReviewAfterSales(params) : await appealReview(params);
        },
    },
};
</script>
<style scoped lang="scss">
.s {
    position: absolute;
    justify-content: flex-end;
    cursor: pointer;
}

::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
    padding: 15px;
}

::v-deep .el-descriptions__body .el-descriptions__table {
    background-color: rgb(242, 244, 245);
}

.msg_merchant {
    padding: 10px;
    color: white;
    border-radius: 5px;
    background-color: rgb(64, 158, 255);
}

.msg_consumer {
    padding: 10px;
    border-radius: 5px;
    background-color: rgb(240, 246, 255);
}

.system-box {
    background-color: #fafafa;
    padding: 10px;
    box-sizing: border-box;
    width: 300px;
}

.system {
    display: flex;
    margin-bottom: 4px;
    color: #999;
}

.message-container {
    display: flex;
    align-items: center;
    margin: 10px 0;
}

.name {
    text-align: right;
    margin: 5px 0;
    color: #999;
}

.avatar {
    float: right;
    margin-right: 0px;
    /* 修改这里将头像放在消息框的右边 */
    display: flex;
    align-items: center;
    text-align: right
}

.avatar-image {
    width: 400px;
    height: 400px;
    // object-fit: cover;
}

.bubble {
    color: #000;
    border-radius: 5px;
    padding-left: 10px;
    padding-bottom: 10px;
}

.bubble2 {
    color: #000;
    border-radius: 5px;
    padding-right: 10px;
}

.message {
    text-align: left;
    margin: 0;
    width: 400px;
}

// .message-container-right {

//   padding-left: 10px;
// }

.message-container-left {
    justify-content: flex-start;
}

.merchant {
    color: rgb(88, 170, 255) !important;
    border: 1px rgb(88, 170, 255) solid;
}

.consumer {
    color: red !important;
    border: 1px red solid;
}

.username {
    color: black !important;
    margin-right: 5px;
    margin-left: 5px;
}

.recordTime {
    color: gray !important;
}

/*::v-deep .el-image .el-image__inner {
    max-width: 500px !important;
    max-height: 1000px !important;
    height: auto;
    width: 500px !important;
}*/

.button-container {
    display: flex;
    align-items: center;
    /* 垂直居中对齐 */
    gap: 10px;
    /* 元素之间的间隔 */
}
</style>
