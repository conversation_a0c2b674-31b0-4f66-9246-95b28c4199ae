<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="filter.status" placeholder="审批状态" clearable multiple collapse-tags
                    style="width: 130px;">
                    <el-option v-for="item in statusList" :key="item" :label="item" :value="item">
                    </el-option>
                </el-select>
                <el-select v-model="filter.type" placeholder="钉钉状态" clearable multiple collapse-tags
                    style="width: 130px;">
                    <el-option v-for="item in typeList" :key="item" :label="item" :value="item">
                    </el-option>
                </el-select>
                <el-select v-model="filter.businesseId" placeholder="审批编号" filterable clearable remote
                    style="width: 130px;" :remote-method="remoteMethod" :loading="filter.loading">
                    <el-option v-for="item in busIdList" :key="item" :label="item" :value="item">
                    </el-option>
                </el-select>
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 250px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                <el-select filterable v-model="filter.platform" placeholder="请选择平台" collapse-tags clearable
                    style="width: 130px;">
                    <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.label" />
                </el-select>
                <el-input v-model="filter.operationGroup" placeholder="请输入运营组" clearable style="width: 110px;"
                    maxlength="100"></el-input>
                <button style="padding:0px;;margin: 0px;border: none;">
                    <inputYunhan ref="shopCodes" :inputt.sync="filter.shopCodes" v-model="filter.shopCodes"
                        width="110px" placeholder="店铺编码" :clearable="true" :clearabletext="true" :maxRows="100"
                        :maxlength="3000" @callback="shopCodescallback" title="请分行输入店铺编码"></inputYunhan>
                </button>
                <el-select filterable v-model="filter.shop" clearable placeholder="店铺" style="width: 150px;">
                    <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.shopName" />
                </el-select>
                <button style="padding: 0px;margin: 0px;border:none;">
                    <inputYunhan ref="innerOrders" :inputt.sync="filter.innerOrders" v-model="filter.innerOrders"
                        width="110px" placeholder="内部订单号" :clearable="true" :clearabletext="true" :maxRows="100"
                        :maxlength="3000" @callback="innerOrderscallback" title="请分行输入订单号">
                    </inputYunhan>
                </button>
                <button style="padding:0px;;margin: 0px;border: none;">
                    <inputYunhan ref="goodsCodes" :inputt.sync="filter.goodsCodes" v-model="filter.goodsCodes"
                        width="110px" placeholder="商品编码" :clearable="true" :clearabletext="true" :maxRows="100"
                        :maxlength="3000" @callback="goodsCodescallback" title="请分行输入商品编码"></inputYunhan>
                </button>
                <el-select filterable v-model="filter.results" collapse-tags clearable placeholder="结果"
                    style="width: 150px;" multiple>
                    <el-option v-for="item in resultList" :key="item" :label="item" :value="item" />
                </el-select>
                <el-select filterable v-model="filter.orderStatus" collapse-tags clearable placeholder="订单状态"
                    style="width: 150px;" multiple>
                    <el-option v-for="item in orderStatusList" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-select filterable v-model="filter.proStatus" clearable placeholder="流程状态" style="width: 90px;">
                    <el-option v-for="item in proStatusList" :key="item" :label="item" :value="item" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="exportList">导出</el-button>
            </div>
        </template>
        <vxetablebase :id="'SmapleVerify2024080402'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' :showsummary='true' :summaryarry='summaryarry' @sortchange='sortchange'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { QueryBusinesseId, QuerySampleVerify, ExportSampleVerify } from "@/api/financial/SampleVerify"
import { getAllList as getshopList } from '@/api/operatemanage/base/shop'
import { formatTime, platformlist, pickerOptions } from "@/utils/tools"
import inputYunhan from '@/components/Comm/inputYunhan.vue'
import dayjs from "dayjs";
function formatStatus(col) {
    if (col == "Delivering") {
        return "发货中";
    } else if (col == "Question") {
        return "异常";
    } else {
        return "";
    }
}
const tableCols = [
    { sortable: 'custom', width: '80', align: 'center', prop: 'status', label: '状态' },
    { sortable: 'custom', width: '80', align: 'center', prop: 'orderStatus', label: '订单状态', formatter: (row) => formatStatus(row.orderStatus) },
    { sortable: 'custom', width: '80', align: 'center', prop: 'type', label: '钉钉类型' },
    { sortable: 'custom', width: '180', align: 'center', prop: 'businesseId', label: '审批编码' },
    { sortable: 'custom', width: '120', align: 'center', prop: 'createTime', label: '年月日', formatter: (row) => formatTime(row.createTime, "YYYY-MM-DD") },
    { sortable: 'custom', width: '80', align: 'center', prop: 'platform', label: '平台' },
    { sortable: 'custom', align: 'center', prop: 'shopCode', label: '店铺编码' },
    { sortable: 'custom', align: 'center', prop: 'shop', label: '店铺' },
    { sortable: 'custom', align: 'center', prop: 'operationGroup', label: '运营组' },
    { sortable: 'custom', width: '120', align: 'center', prop: 'innerOrder', label: '内部订单号' },
    { sortable: 'custom', width: '120', align: 'center', prop: 'goodsCode', label: '商品编码' },
    { sortable: 'custom', width: '80', align: 'center', prop: 'goodsQty', label: '商品数量' },
    { sortable: 'custom', width: '80', align: 'center', prop: 'orderQty', label: '订单数量' },
    { sortable: 'custom', width: '80', align: 'center', prop: 'costPrice', label: '成本价' },
    { sortable: 'custom', width: '80', align: 'center', prop: 'amount', label: '金额' },
    { sortable: 'custom', width: '80', align: 'center', prop: 'result', label: '结果' },
    { sortable: 'custom', width: '80', align: 'center', prop: 'proStatus', label: '流程状态' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, inputYunhan
    },
    data() {
        return {
            filter: {
                status: null,
                type: null,
                businesseId: null,
                startTime: null,
                endTime: null,
                platform: null,
                goodsCodes: null,
                innerOrders: null,
                operationGroup: null,
                results: null,
                orderStatus: null,
                proStatus: null,
                shopCodes:null,
                shop: null,
                orderBy: null,
                isAsc: false,
                pageSize: 50,
                currentPage: 1,
                loading: false,
            },
            statusList: [
                "审批中",
                "同意",
                "撤销",
                "拒绝"
            ],
            typeList: [
                "寄样",
                "拿样"
            ],
            busIdList: [],
            timeRanges: null,
            pickerOptions,
            platformlist,
            shopList: [],
            resultList: [
                "一致",
                "不一致"
            ],
            orderStatusList: [
                { value: "Question", label: "异常" },
                { value: "Delivering", label: "发货中" },
            ],
            proStatusList: [
                "正常",
                "异常"
            ],
            loading: false,
            tableCols,
            tableData: [],
            summaryarry: null,
            total: null,
            that: this,
        }
    },
    async mounted() {
        //时间默认为近七天
        this.filter.startTime = dayjs().subtract(6, 'day').format('YYYY-MM-DD')
        this.filter.endTime = dayjs().format('YYYY-MM-DD')
        const res1 = await getshopList();
        this.shopList = res1.data
        this.timeRanges = [this.filter.startTime, this.filter.endTime]
        await this.getList()
    },
    methods: {
        goodsCodescallback(val) {
            this.filter.goodsCodes = val;
        },
        innerOrderscallback(val) {
            this.filter.innerOrders = val;
        },
        shopCodescallback(val) {
            this.filter.shopCodes = val;
        },
        remoteMethod(query) {
            if (query && query.length > 50) return this.$message.error("输入内容过长");
            this.filter.loading == true;
            this.busIdList = [];
            setTimeout(async () => {
                const res = await QueryBusinesseId({ query: query, pageSize: 50 })
                this.filter.loading == false;
                this.busIdList = res;
            }, 200)
        },
        async changeTime(e) {
            this.filter.startTime = e ? e[0] : null
            this.filter.endTime = e ? e[1] : null
        },
        async getList(type) {
            this.loading = true;
            if (type == 'search') {
                this.filter.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            var { success, data } = await QuerySampleVerify(this.filter);
            if (success) {
                this.tableData = data?.list;
                this.summaryarry = data?.summary;
                this.total = data?.total;
            } 
            this.loading = false;
        },
        //每页数量改变
        Sizechange(val) {
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.filter.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.filter.orderBy = prop
                this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        async exportList() {
            const res = await ExportSampleVerify(this.filter);
            if (!res?.data) return this.$message.error("导出失败,请稍后重试");
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '寄样拿样表' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        }
    }
}
</script>

<style scoped lang="scss">
::v-deep .el-select__tags-text {
    max-width: 25px;
}
</style>