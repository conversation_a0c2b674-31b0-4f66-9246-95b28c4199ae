<template>
    <container v-loading="pageLoading">
        <!-- 薪资统计 -->
        <template #header>
            <div style="display: flex;align-items: center;margin-bottom: 20px;">
                <el-switch v-model="filter.groupType" active-text="按日" inactive-text="按月" active-color="#13ce66"
                    inactive-color="#ff4949" style="margin-right: 10px;" @change="changeType">
                </el-switch>
                <el-checkbox-group v-model="filter.checkListTemp" style="float:left;margin-top:1px;">
                    <el-checkbox v-for="item in checkTamp" :label="item.value" :key="item.value">{{ item.label
                    }}</el-checkbox>
                </el-checkbox-group>
                <el-date-picker v-show="filter.groupType" style="width: 220px;margin-right: 10px;"
                    v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                    range-separator="至" start-placeholder="开始" end-placeholder="结束" :clearable="false"
                    :picker-options="pickerOptions">
                </el-date-picker>
                <el-date-picker v-model="monthValue" type="month" placeholder="选择月" v-show="!filter.groupType"
                    :clearable="false" @change="changeMonth" style="width: 220px;margin-right: 10px;">
                </el-date-picker>
                <el-select v-model="filter.company" clearable placeholder="分公司" @change="changeSetCompany"
                    style="width: 150px;margin-right: 10px;">
                    <el-option label="义乌" value="义乌" />
                    <el-option label="南昌" value="南昌" />
                </el-select>
                <el-select v-model="filter.purchasePost" filterable clearable placeholder="岗位" @change="changeSetPost"
                    style="width: 150px;margin-right: 10px;">
                    <el-option v-for="item in postList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <!-- <el-select v-model="filter.userName" filterable remote clearable placeholder="姓名"
                    style="width: 150px;margin-right: 10px;" @change="clearName">
                    <el-option v-for="item in userList" :key="item.userName" :label="item.userName"
                        :value="item.userName" />
                </el-select> -->
                <el-input v-model="filter.userName" clearable placeholder="姓名" style="width: 150px;margin-right: 10px;"
                    maxlength="50"></el-input>
                <el-select v-model="filter.employeeType" clearable placeholder="类型" style="width: 120px;margin-right:10px">
                    <el-option label="正式" :value="3" :key="1" />
                    <el-option label="试用" :value="2" :key="2" />
                </el-select>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExport">导出</el-button>
                <el-button type="primary" @click="computeDialog">一键计算</el-button>
            </div>
        </template>

        <ces-table :tablekey="'StoreTocktaking202305091124'" ref="tableCols" :hasexpandRight='true' :showsummary='true'
            :summaryarry='summaryarry' :that='that' :isIndex='true' :tablefixed='true' :hasexpand='false'
            @cellclick='cellclick' :isSelectColumn="false" :tableData='list' :tableCols='tableCols'
            :tableHandles='tableHandles' @sortchange="sortchange" :loading="listLoading"   :border="true">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <div style="display: flex;">
                <el-date-picker v-model="chatPickValue" type="daterange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" @change="chatSearch" :picker-options="pickerOptions" v-if="filter.groupType">
                </el-date-picker>
                <el-date-picker v-model="chatMonthValue" type="monthrange" align="right" unlink-panels range-separator="至"
                    start-placeholder="开始月份" end-placeholder="结束月份" :picker-options="pickerOptions"
                    @change="chatMonthSearch" v-else>
                </el-date-picker>
            </div>
            <span>
                <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <dailyPicker :showComputeDialog="this.showComputeDialog" :type="type" @handlePick="handlePick"
            @handlePickSearch="handlePickSearch" @closePicker="closePicker" ref="pickTime"></dailyPicker>

    </container>
</template>

<script>
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import inputYunhan from "@/components/Comm/inputYunhan";
import { formatTime, formatNoLink } from "@/utils/tools";
import { getPurchaseWagesComputeAsync, getPurchaseWagesComputeGroupAsync, getPurchaseWagesAnalysisAsync, getPurchaseWagesCalPositions, exportPurchaseWagesComputeAsync, getUserByPositionAsync,getUserByPositionByCurUserAsync, calPurchasePostWages } from '@/api/profit/purchasepostwages';
import buschar from '@/components/Bus/buschar'
import { Loading } from 'element-ui';
import dailyPicker from './dailyPicker'

const startDate = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");


const tableCols = [
    //如果是按日，就取出年月日，如果是按月，就取出年月
    { istrue: true, align: 'center', prop: 'yearMonthDay', label: '日期', sortable: 'custom', formatter: (row) => row.yearMonthDayDisplay },
    { istrue: true, align: 'center', prop: 'company', label: '分公司' },
    { istrue: true, align: 'center', prop: 'userName', label: '姓名' },
    { istrue: true, align: 'center', prop: 'purchasePost', label: '岗位', },
    { istrue: true, align: 'center', prop: 'employeeType', label: '类型', formatter: (row) => row.employeeType == 1 ? "离职" : row.employeeType == 2 ? "试用" : row.employeeType == 3 ? "正式" : "" },
    { istrue: true, align: 'center', prop: 'baseSalary', label: '底薪', sortable: 'custom' },
    { istrue: true, align: 'center', prop: 'performance', label: '绩效', sortable: 'custom' },
    { istrue: true, align: 'center', prop: 'commission', label: '提成', sortable: 'custom', },
    { istrue: true, align: 'center', prop: 'commissionType', label: '提成取值' },
    { istrue: true, align: 'center', prop: 'totalWages', label: '合计', sortable: 'custom' },
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

const checkTamp = [
    {
        label: '分公司',
        value: 1
    },
    {
        label: '姓名',
        value: 2
    },
    {
        label: '岗位',
        value: 3
    },
    {
        label: '类型',
        value: 4
    },
]

const tableHandles = [
    //{ label: "导出", throttle: 3000, handle: (that) => that.onExport() },
];
//默认时间的跨度是一个月,例如今天是十月五号,那么默认时间就是九月五号到十月五号
const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");
export default {
    name: 'YunHanAdminPurchasepostwages',
    components: { container, cesTable, inputYunhan, buschar, dailyPicker },

    data() {
        return {
            chatMonthValue: null,
            params: null,
            checkTamp: checkTamp,
            type: 1,
            monthValue: null,
            chatPickValue: null,//echats时间
            pickerValue: null,
            showComputeDialog: false,
            that: this,
            filter: {
                purchasePost: null,
                beginDate: null,
                endDate: null,
                userName: null,
                company: null,
                keywords: null,
                createdUserName: null,
                brandCode: null,
                job_Position: null,
                companyType: null,
                taskStatus: null,
                checkList: [],
                checkListTemp: [],
                groupType: true,
                timerange: [startTime, endTime],
                positions: [],//岗位
                isSearchMonth: 0,//是否按月查询
            },
            list: [],
            summaryarry: {},
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "YearMonthDay", IsAsc: false },
            total: 0,
            sels: [],
            brandList: [],
            userList: [],
            postList: [],
            userListAll: [],
            postListAll: [],
            basicsList: [],
            jobPositionList: [],
            chooseTags: [],
            selids: [],
            pickerOptions: {
                shortcuts: [{
                    text: '近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近半个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }],
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            chartTime: {
                beginDate: null,
                endDate: null,
                isSearchMonth: 0,
                orderBy: "yearMonthDay",
                checkList: []
            },//图表时间
            activeName: 'first',
            buscharDialog: { visible: false, title: "", data: [] },
            listLoading: false,
            pageLoading: false,
            onExporting: false,
            dialogShowInfoVisible: false,
        };
    },

    async mounted() {
        await this.onSearch();
        await this.init();
    },

    methods: {
        //按月查询趋势图
        chatMonthSearch(e) {
            console.log(e,'e');
            //将e的第一个值格式化赋值给this.params.beginDate
            this.params.beginDate = dayjs(e[0]).format('YYYY-MM-DD')
            //将e的第二个值格式化赋值给this.params.endDate
            this.params.endDate = dayjs(e[1]).format('YYYY-MM-DD')
            //将this.chatMonthValue变为当前选择的月份区间
            this.params.timerange = [this.params.beginDate, this.params.endDate];
            this.publicChat(this.params)
        },
        clearName(e) {
            //如果姓名为空,就清空岗位
            if (!e) {
                this.filter.purchasePost = null;
                return
            }
            this.filter.userName = e;
        },
        //子组件日期选择点击确定触发的方法
        async handlePickSearch(e) {
            let startTime = dayjs(e[0]).format('YYYY-MM-DD')
            let endTime = dayjs(e[1]).format('YYYY-MM-DD')
            const { data, success } = await calPurchasePostWages({ startTime, endTime });
            if (!success) {
                this.$message.error('计算失败');
                return;
            } else {
                if (data) {
                    this.list = data.list
                }
                this.list = []
                this.$message.success('计算成功');
                this.showComputeDialog = false;
            }
        },
        closePicker() {
            this.showComputeDialog = false;
        },
        //子组件传来的值
        handlePick(e) {
            this.filter.beginDate = e.startTime;
            this.filter.endDate = e.endTime;
            this.filter.timerange = [e.startTime, e.endTime];
        },
        //选择月份
        changeMonth() {
            //如果选择月份大于当前月份,就报错
            if (dayjs(this.monthValue).isAfter(dayjs())) {
                this.$message.error('选择月份不能大于当前月份');
                this.monthValue = null;
                return;
            }
            if (!this.filter.groupType) {
                //使用dayjs格式化this.monthValue成YYYY-MM
                this.filter.beginDate = dayjs(this.monthValue).format('YYYY-MM-DD');
                this.filter.endDate = dayjs(this.monthValue).format('YYYY-MM-DD');
                this.filter.timerange = [this.filter.beginDate, this.filter.endDate];
            }
        },
        //按日按月切换
        changeType(e) {
            if (e) {
                this.filter.isSearchMonth = 0;
                this.filter.beginDate = dayjs().subtract(30, 'day').format('YYYY-MM-DD');
                this.filter.endDate = dayjs().format('YYYY-MM-DD');
                this.filter.timerange = [this.filter.beginDate, this.filter.endDate];
            } else {
                this.filter.isSearchMonth = 1;
                this.public()
            }
            this.onSearch();
        },
        //按月公共逻辑
        public() {
            //如果是按月查询,并且月份不为空
            if (!this.filter.groupType && this.monthValue) {
                //用dayjs格式化城YYYY-MM
                this.filter.beginDate = dayjs(this.monthValue).format('YYYY-MM-DD');
                this.filter.endDate = dayjs(this.monthValue).format('YYYY-MM-DD');
                this.filter.timerange = [this.filter.beginDate, this.filter.endDate];
            } else {
                //如果是按月查询,并且月份为空,那就默认选择当前月份的第一天
                this.filter.beginDate = dayjs().startOf('month').format('YYYY-MM-DD');
                this.filter.endDate = dayjs().startOf('month').format('YYYY-MM-DD');
                this.filter.timerange = [this.filter.beginDate, this.filter.endDate];
                //将值赋值给this.monthValue
                this.monthValue = this.filter.beginDate;
            }
        },
        chatSearch(e) {
            if (e) {
                console.log(e,'e');
                // //使用dayjs将e[0],和e[1]转换成YYYY-MM-DD格式
                this.params.beginDate = dayjs(e[0]).format('YYYY-MM-DD')
                this.params.endDate = dayjs(e[1]).format('YYYY-MM-DD')
                this.params.timerange = [this.params.beginDate, this.params.endDate];
                this.publicChat(this.params,true);
            }
        },
        computeDialog() {
            this.showComputeDialog = true
            this.$refs.pickTime.clearTime()
        },
        async onSearch() {
            //清空name的空格
            if (this.filter.userName) {
                this.filter.userName = this.filter.userName.replace(/\s/g, '');
            }
            console.log(this.filter.userName, 'this.filter.userName');
            //如果岗位和姓名的长度超过200,就报错
            if (this.filter.brandName && this.filter.brandName.length > 200) {
                this.$message.error('姓名长度不能超过200');
                return;
            }
            //如果是按日,并且时间为空,就默认为当前月的第一天,到当前时间
            if (this.filter.groupType && !this.filter.timerange) {
                this.filter.beginDate = dayjs().startOf('month').format('YYYY-MM-DD');
                this.filter.endDate = dayjs().format('YYYY-MM-DD');
                this.filter.timerange = [this.filter.beginDate, this.filter.endDate];
            }
            if (this.filter.checkListTemp.length > 0) {
                const result = checkTamp.filter(item => !this.filter.checkListTemp.includes(item.value)).map(item => item.label);
                if (result.length > 0) {
                    tableCols.forEach(f => {
                        if (result.includes(f.label)) {
                            f.istrue = false;
                        }
                        else {
                            f.istrue = true;
                        }
                    })
                } else {
                    tableCols.forEach(f => {
                        f.istrue = true;
                    })
                }
                tableCols.forEach(f => {
                    if (f.label == '提成取值') {
                        f.istrue = false;
                    }
                })

            } else {
                tableCols.forEach(f => {
                    f.istrue = true;
                })
            }
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        async init() {
            const { data, success } = await getPurchaseWagesCalPositions()
            if (!success) {
                return
            } else {
                this.postList = data.map((item, i) => {
                    return {
                        label: item,
                        value: i,
                    }
                })
            }

            const { data: data1, success: success1 } = await getUserByPositionByCurUserAsync({ positions: this.filter.positions })
            if (!success1) {
                //给错误提示
                this.$message.error('获取岗位薪资人员失败')
                return
            } else {
                this.userList = data1
            }
        },
        async changeSetCompany() {
            if (this.filter.company === '义乌' || this.filter.company === '南昌') {
                this.userList = this.userListAll.filter(f => f.company === this.filter.company).map(item => {
                    return item;
                });
            } else if (this.filter.company === '其他') {
                this.userList = this.userListAll.filter(f => f.company !== '南昌' && f.company !== '义乌').map(item => {
                    return item;
                });
            } else {
                this.userList = this.userListAll.map(item => {
                    return item;
                });
            }
            this.filter.brandName = null;
        },
        async changeSetPost(e) {
            //如果岗位为空,就清空姓名
            if (e == null || e == '') {
                this.filter.brandName = null;
                return;
            }
            //清空岗位
            this.filter.positions = [];
            //根据e找出对应的岗位
            this.filter.positions.push(this.postList.find((item) => item.value === e).label)
            this.filter.purchasePost = this.postList.find((item) => item.value === e).label
            const { data, success } = await getUserByPositionByCurUserAsync({ positions: this.filter.positions })
            if (!success) {
                //给错误提示
                this.$message.error('获取岗位薪资人员失败')
                return
            } else {
                this.userList = data
            }
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.beginDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.beginDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            // this.public()
            this.filter.checkList = [];
            if (this.filter.checkListTemp.length > 0) {
                this.filter.checkListTemp.forEach(f => {
                    this.filter.checkList.push(f);
                })
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const { data, success } = await getPurchaseWagesComputeAsync(params);
            this.listLoading = false
            if (!success) {
                return
            }
            this.total = data.total;
            this.list = data.list;
            this.summaryarry = data.summary;

        },
        //抽离趋势图请求
        async publicChat(params,type) {
            console.log(params, 'params');
            this.buscharDialog.visible = false;
            let loadingInstance = Loading.service();
            //如果this.filter.timerange中的日期间距没有30天，就将this.chatPickValue的间隔变为三十天
            if (this.filter.groupType && !type) {
                if (dayjs(this.filter.timerange[1]).diff(dayjs(this.filter.timerange[0]), 'day') < 30) {
                    //如果this.filter.timerange中的日期间距没有30天,那就从this.filter.timerange[1]往前推30天
                    this.chatPickValue = [dayjs(this.filter.timerange[1]).subtract(30, 'day').format('YYYY-MM-DD'), this.filter.timerange[1]]
                    this.params.beginDate = this.chatPickValue[0]
                    this.params.endDate = this.chatPickValue[1]
                    const { data } = await getPurchaseWagesAnalysisAsync(this.params)
                    this.buscharDialog.visible = true;
                    this.buscharDialog.data = data;
                    loadingInstance.close();
                } else {
                    const { data } = await getPurchaseWagesAnalysisAsync(this.params)
                    this.buscharDialog.visible = true;
                    this.buscharDialog.data = data;
                    loadingInstance.close();
                }
            } else {
                this.chatMonthValue = this.params.timerange;
                const { data } = await getPurchaseWagesAnalysisAsync(this.params)
                this.buscharDialog.visible = true;
                this.buscharDialog.data = data;
                loadingInstance.close();
            }
        },
        async showchart(row) {
            Loading.service({ fullscreen: true });
            this.chatPickValue = [this.filter.beginDate, this.filter.endDate];
            this.filter.checkList = [];
            if (this.filter.checkListTemp.length > 0) {
                this.filter.checkListTemp.forEach(f => {
                    this.filter.checkList.push(f);
                })
            }
            let params;
            if (this.filter.checkList.length > 0) {
                params = JSON.parse(JSON.stringify(this.filter));
                params.company = row.company
                params.userName = row.userName
                params.purchasePost = row.purchasePost
                if (row.employeeType == 0) {
                    params.employeeType = null
                } else {
                    params.employeeType = row.employeeType
                }
            } else {
                params = JSON.parse(JSON.stringify(this.filter));
                params.userName = row.userName;
            }
            this.params = params
            if (!this.filter.groupType) {
                this.chatMonthValue = this.params.timerange;
            }
            console.log(params, 'params');
            this.publicChat(params);
        },
        async callbackGoodsCode(val) {
            this.filter.goodsCode = val;
        },
        selectchange: function (rows, row) {
            //先把当前也的数据全部移除
            this.list.forEach(f => {
                let index = this.chooseTags.findIndex((v) => (v === f.goodsCode));
                if (index !== -1) {
                    this.chooseTags.splice(index, 1);
                    this.selrows.splice(index, 1);
                }
            });
            //把选中的添加
            rows.forEach(f => {
                let index = this.chooseTags.findIndex((v) => (v === f.goodsCode));
                if (index === -1) {
                    this.chooseTags.push(f.goodsCode);
                    this.selrows.push(f);
                }
            });

            ///
            let _this = this;
            if (rows.length > 0) {
                var a = [];
                rows.forEach(element => {
                    let b = _this.list.indexOf(element);
                    a.push(b + 1);
                });

                let d = _this.list.indexOf(row);

                var b = Math.min(...a)
                var c = Math.max(...a)

                a.push(d);
                if (d < b) {
                    var b = _this.list.indexOf(row);
                    var c = Math.max(...a)
                } else if (d > c) {
                    var b = Math.min(...a) - 1
                    var c = Math.max(...a)
                } else {
                    var b = Math.min(...a) - 1
                    var c = _this.list.indexOf(row) + 1;
                }

                let neww = [b, c];
                _this.selids = neww;
            }
        },
        callback(val) {
            this.selids = [...val];

            this.tablelist = [];
            this.tablelist = val;
            var goodsCode = val.map((item) => {
                return item.goodsCode;
            })
            this.chooseTags = goodsCode;
        },
        async onExport() {
            if (this.onExporting) return;
            try {
                this.filter.beginDate = null;
                this.filter.endDate = null;
                if (this.filter.timerange) {
                    this.filter.beginDate = this.filter.timerange[0];
                    this.filter.endDate = this.filter.timerange[1];
                }
                this.filter.checkList = [];
                // this.filter.isSearchMonth = 1
                if (this.filter.checkListTemp.length > 0) {
                    this.filter.checkListTemp.forEach(f => {
                        this.filter.checkList.push(f);
                    })
                }
                const params = { ... this.filter }
                var res = await exportPurchaseWagesComputeAsync(params);
                if (!res?.data) return
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '采购薪资统计_' + new Date().toLocaleString() + '.xlsx')
                aLink.click()
            } catch (err) {
                console.log(err)
            }
            this.onExporting = false;
        },
        async cellclick(row, column, cell, event) {
            if (column.property == 'finishedProductCode') {
                if (this.filter.checkList.length <= 0) {
                    let selData = { packagesProcessingId: row.packagesProcessingId };
                    this.dialogShowInfoVisible = true;
                    this.$nextTick(async () => {
                        this.$refs.finishedpartdetailVue.loadData({ selRows: selData, });
                    });
                    // this.$showDialogform({
                    //     path: `@/views/inventory/machine/finishedpartdetail.vue`,
                    //     title: '详情信息',
                    //     autoTitle: false,
                    //     args: { selRows: selData, },
                    //     height: '600px',
                    //     width: '50%',
                    // })
                }

            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>


<style lang="scss" scoped>
.btnBox {
    display: flex;
    justify-content: space-around;
}

.pickerBox {
    display: flex;
    justify-content: center;
    margin: 20px 0;
}
</style>
