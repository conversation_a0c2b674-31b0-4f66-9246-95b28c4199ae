import Cookies from 'js-cookie'
const tokenKey = 'token'

export function getToken() {
//   return window.localStorage.getItem(tokenKey)
  return Cookies.get(tokenKey)
    
}

export function setToken(token) {
    // return window.localStorage.setItem(tokenKey, token)
    return Cookies.get(tokenKey, token)
}

export function removeToken() {
    //   return window.localStorage.removeItem(tokenKey)
    return Cookies.remove(tokenKey)
}
