<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item>
                    <el-select filterable v-model="filter.businessName" collapse-tags clearable placeholder="业务类型"
                        style="width: 160px">
                        <el-option label="产品管理一键建群" value="产品管理一键建群" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.groupChatName" placeholder="群名称" style="width: 140px" clearable
                        maxlength="40" />
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.mainUserName" placeholder="建群人" style="width: 120px" clearable
                        maxlength="40" />
                </el-form-item>
                <el-form-item>
                    <el-date-picker style="width: 230px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="创建时间" end-placeholder="创建时间">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <!-- <el-button type="primary" @click="onExport">导出</el-button> -->
                </el-form-item>
            </el-form>
        </template>

        <!--列表-->
        <vxetablebase :id="'groupchatlog202408041349'" ref="table" :that='that' :isIndex='true' @sortchange='sortchange' :tableData='grouplist'
            :tableCols='tableCols' :loading="listLoading" :isSelectColumn="false">
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getgroupList" />
        </template>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import cesTable from "@/components/Table/table.vue";
import MyConfirmButton from "@/components/my-confirm-button";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { createGroupChatProductFile } from '@/api/operatemanage/base/product'
import { getCreateGroupChatLogPageList } from '@/api/admin/groupchatlog'
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
const tableCols = [
    { istrue: true, prop: 'businessName', label: '业务类型', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'groupChatName', label: '群名称', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'mainUserName', label: '建群人', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'sendDDUserNames', label: '被通知人', width: '400', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '通知时间', width: '150', sortable: 'custom' },
    {
        istrue: true, prop: 'status', label: '状态', width: '80', sortable: 'custom',
        formatter: (row) => row.status == 1 ? "成功" : row.status == -2 ? "已解散" : "失败"
    },
    { istrue: true, prop: 'attachmentFile', label: '图片', width: '80', type: 'images', },
    { istrue: true, prop: 'chatDisbandUserIdName', label: '解散人', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'chatDisbandTime', label: '解散时间', width: '150', sortable: 'custom' },
    // {
    //     istrue: true, type: "button", label: '操作', width: "120", btnList: [
    //         { label: "上传聊天记录", handle: (that, row) => that.onChatContext(row) },
    //     ]
    // }
];
export default {
    name: "productcreategroupchatlog",
    components: { MyContainer, MyConfirmButton, cesTable, vxetablebase, YhImgUpload },
    data() {
        return {
            that: this,
            filter: {
                businessName: null,
                mainUserName: null,
                groupChatName: null,
                timerange: [
                    formatTime(dayjs().subtract(2, "day"), "YYYY-MM-DD"),
                    formatTime(dayjs(), "YYYY-MM-DD"),
                ],
            },
            tableCols: tableCols,
            grouplist: [],
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "createdTime", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],

            dialogFjVisible: false,
            dialogFjForm: {
                id: 0,
                attachmentFile: null,
            },
        };
    },
    async mounted() {
        this.onSearch();
    },
    methods: {
        //获取查询条件
        getCondition() {
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            const params = {
                ...pager,
                ...page,
                ...this.filter,
            };
            return params;
        },
        async getgroupList() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            } else {
                this.$message({ message: "请选择日期", type: "error" });
                return;
            }
            let params = this.getCondition();
            if (params === false) {
                return;
            }
            this.listLoading = true;
            const res = await getCreateGroupChatLogPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.grouplist = res.data.list;
            //this.summaryarry=res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getgroupList();
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onChatContext() {
            this.$message({ message: "功能暂未开放，敬请期待。", type: "success" });
        }
    },
};
</script>
<style lang="scss" scoped></style>
