<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.orderNo" placeholder="业务单号" maxlength="50" clearable class="publicCss" />

        <!-- <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" /> -->
        <el-button style="padding: 0;border: none;float: left;">
          <inputYunhan :key="'1'" :keys="'one'" :width="'220px'" ref="" :inputt.sync="ListInfo.goodsCodes"
            v-model.trim="ListInfo.goodsCodes" placeholder="商品编码/若输入多条请按回车" :clearable="true"
            @callback="callbackGoodsCode" title="商品编码" @entersearch="entersearch" :maxRows="100">
          </inputYunhan>
        </el-button>

        <el-input v-model.trim="ListInfo.proCode" placeholder="SKC" maxlength="50" clearable class="publicCss" />

        <el-input v-model.trim="ListInfo.shopCode" placeholder="店铺" maxlength="50" clearable class="publicCss" />

        <el-button type="primary" @click="getList('search')">搜索</el-button>

        <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button @click="startImport"
          type="primary" icon="el-icon-share" @command="handleCommand"> 导入
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">下载模版</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="primary" @click="onExport" style="margin-left: 5px;" v-if="checkPermission('salesDetail_kj_export')">导出</el-button>

      </div>
    </template>
    <vxetablebase :id="'salesDetails_SheIn202408141056'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :border="true" :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
      :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="display: flex; align-items: baseline; height: 75px;margin-top: 10px;">
        <el-date-picker style="width: 150px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
          placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
        </el-date-picker>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { formatPlatform, pickerOptions } from '@/utils/tools'
import { importCodeSalesThemeAnalysis } from '@/api/bookkeeper/reportdayV2'
import { pageCodeSalesThemeAnalysisKJAsync, exportCodeSalesSheIn } from '@/api/bookkeeper/crossBorderV2'
import inputYunhan from "@/components/Comm/inputYunhan";

import dayjs from 'dayjs'
const formatOrderType = (row) => {
  let type = '';
  switch (row) {
    case 0:
      type = '普通订单';
      break;
    case 1:
      type = '补发订单';
      break;
    case 2:
      type = '供销plus';
      break;
    case 3:
      type = '其他';
      break;
  }
  return type;
};
const tableCols = [
  { istrue: true, prop: 'yearMonthDay', label: '年月日', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'shopCode', label: '店铺ID', sortable: 'custom', width: '90' },
  // { istrue: true, prop: 'orderNoInner', label: '内部订单', sortable: 'custom', width: '90', type: 'orderLogInfo', orderType: 'orderNoInner' },
  { istrue: true, prop: 'orderType', label: '订单类型', sortable: 'custom', width: '90', formatter: (row) => formatOrderType(row.orderType) },
  { istrue: true, prop: 'orderNo', label: '业务单号', width: '150', sortable: 'custom' },
  // { istrue: true, prop: 'orderStatus ', label: '订单状态', sortable: 'custom', width: '80', formatter: (row) => row.groupName },
  //{ istrue: true, prop: 'wmsName', label: '发货仓', sortable: 'custom', width: '100', },
  { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom', width: '90', },
  { istrue: true, prop: 'platform', fix: true, label: '平台', width: '90', sortable: 'custom', formatter: (row) => formatPlatform(row.platform) },
  // { istrue: true, prop: 'timeSend', label: '发货日期', width: '120', sortable: 'custom', },
  { istrue: true, prop: 'timePay', label: '付款日期', sortable: 'custom', width: '105', },
  { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'styleCode', label: '款式编码', sortable: 'custom', width: 'auto', formatter: (row) => !row.styleCode ? " " : row.styleCode },
  { istrue: true, prop: 'proCode', label: 'SKC', sortable: 'custom', width: '90' },
  // { istrue: true, prop: 'groupName', label: '运营组', sortable: 'custom', width: '90', },
  // { istrue: true, prop: 'operateSpecialUserName', label: '运营专员', sortable: 'custom', width: '90' },
  //{ istrue: true, prop: 'brandName', label: '采购员', sortable: 'custom', width: '110', type: 'custom' },
  { istrue: true, prop: 'cost', label: '成本价', type: 'custom', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'qty', label: '销售数量', sortable: 'custom', width: '90', },
  { istrue: true, prop: 'giftQty', label: '赠品数量', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'actualAmount', label: '实发金额', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'salesAmount', label: '销售金额', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'saleCost', label: '销售成本', sortable: 'custom', width: '90' },
]
export default {
  name: "sheInSalesDetails",
  components: {
    MyContainer, vxetablebase, inputYunhan
  },
  data() {
    return {
      yearMonthDay: null,
      dialogVisible: false,//导入弹窗
      fileList: [],//上传文件列表
      uploadLoading: false,//上传按钮loading
      fileparm: {},//上传文件参数
      timeRanges: [],//时间范围
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        dataStartDate: null,//开始时间
        dataEndDate: null,//结束时间
        orderNo: null,//线上单号
        platform: 12,//希音
        orderNo: null,
        goodsCode: null,
        proCode: null,
        shopCode: null
      },
      tableCols,
      summaryarry: {},
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.dataStartDate = e ? e[0] : null
      this.ListInfo.dataEndDate = e ? e[1] : null
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("YearMonthDay", dayjs(this.yearMonthDay).format('YYYYMMDD'));
      form.append("PlatForm", 12);
      var res = await importCodeSalesThemeAnalysis(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (!this.yearMonthDay) {
        this.$message({ message: "请选择日期", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.yearMonthDay = null
      this.dialogVisible = true;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近1天时间
        this.ListInfo.dataStartDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.dataEndDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.dataStartDate, this.ListInfo.dataEndDate]
      }
      this.loading = true
      const { data, success } = await pageCodeSalesThemeAnalysisKJAsync(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async handleCommand(command) {
      switch (command) {
        //下载模版
        case 'a':
          await this.downLoadFile()
          break;
      }
    },
    async downLoadFile() {
      window.open("/static/excel/KjSalesDetails/希音-销售明细.xlsx", "_blank");
    },
    async callbackGoodsCode(val) {
      this.ListInfo.goodsCodes = val;
      //this.onSearch();
    },
    async entersearch(val) {
      // this.filter.indexNo = val;
      this.getList();
    },
    async onExport() {//导出列表数据；

      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近1天时间
        this.ListInfo.dataStartDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.dataEndDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.dataStartDate, this.ListInfo.dataEndDate]
      }
      var res = await exportCodeSalesSheIn(this.ListInfo);
      if (res?.data.success) {
        this.$message({ message: res.data.msg, type: "success" });
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
