1
<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-select v-model="Filter.platformInt" placeholder="平台" filterable clearable>
                        <el-option label="淘系" value=1></el-option>
                        <el-option label="拼多多" value=2></el-option>
                        <el-option label="阿里巴巴" value=4></el-option>
                        <el-option label="抖音" value=6></el-option>
                        <el-option label="京东" value=7></el-option>
                        <el-option label="快手" value=14></el-option>
                    </el-select>
                </el-button>
              <el-button style="padding: 0;margin: 0;border:none">
                <el-input
                  v-model="Filter.inactiveDays"
                  placeholder="请输入X天内数据"
                  type="text"
                  clearable
                  style="width: 150px; margin-left: 10px;"
                  oninput="value=value.replace(/[^\d]/g,''); if(value > 9999 || value < 0) value = '';"
                ></el-input>
              </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
            </el-button-group>
        </template>
        <!--列表-->
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='list' @select='selectchange' :isSelection='false' :tableCols='tableCols'
            :summaryarry='summaryarry' :showsummary='true' :loading="listLoading" />
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </my-container>
</template>
<script>

import { GetKeFuHuiZongPlatformPageList } from '@/api/customerservice/group'
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
const tableCols = [
    { istrue: true, prop: 'platform', label: '平台', sortable: 'custom', formatter: (row) => row.platformName },
    { istrue: true, prop: 'areaCount', label: '分区数量', sortable: 'custom', type: 'click', handle: (that, row, column, cell) => that.canclick(row, column, cell), },
    { istrue: true, prop: 'shopCount', label: '店铺数量', sortable: 'custom', type: 'click', handle: (that, row, column, cell) => that.canclick(row, column, cell), },
    { istrue: true, prop: 'nameCountSq', label: '售前人数', sortable: 'custom', type: 'click', handle: (that, row, column, cell) => that.canclick11(row, 0), },
    { istrue: true, prop: 'nickCountSq', label: '售前昵称数', sortable: 'custom', type: 'click', handle: (that, row, column, cell) => that.canclick11(row, 0), },
    { istrue: true, prop: 'shopCountSq', label: '售前店铺数', sortable: 'custom', type: 'click', handle: (that, row, column, cell) => that.canclick11(row, 0), },
    { istrue: true, prop: 'nameCountSh', label: '售后人数', sortable: 'custom', type: 'click', handle: (that, row, column, cell) => that.canclick11(row, 1), },
    { istrue: true, prop: 'nickCountSh', label: '售后昵称数', sortable: 'custom', type: 'click', handle: (that, row, column, cell) => that.canclick11(row, 1), },
    { istrue: true, prop: 'shopCountSh', label: '售后店铺数', sortable: 'custom', type: 'click', handle: (that, row, column, cell) => that.canclick11(row, 1), },
    { istrue: true, prop: 'nameCountYt', label: '一体人数', sortable: 'custom', type: 'click', handle: (that, row, column, cell) => that.canclick11(row, 2), },
    { istrue: true, prop: 'nickCountYt', label: '一体昵称数', sortable: 'custom', type: 'click', handle: (that, row, column, cell) => that.canclick11(row, 2), },
    { istrue: true, prop: 'shopCountYt', label: '一体店铺数', sortable: 'custom', type: 'click', handle: (that, row, column, cell) => that.canclick11(row, 2), },
];


export default {
    name: "kefuhuizongplatform",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, vxetablebase },
    data() {
        return {
            that: this,
            Filter: {
                platformInt: null,
                inactiveDays: null,

            },
            tableCols: tableCols,
            list: [],
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "platform", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
        };
    },
    async mounted() {
        await this.onSearch();
    },
    methods: {
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getlist();
        },
        async getlist() {
            const para = { ...this.Filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
                platform: 1
            };
            console.log(para)
            this.listLoading = true;
            const res = await GetKeFuHuiZongPlatformPageList(params);
            this.listLoading = false;
            console.log(res)

            this.total = res.data.total
            this.list = res.data.list;
            this.summaryarry = res.data.summary;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange(rows, row) {
            this.sels = rows;
        },
        canclick(row, column, cell) {
            this.$emit('toSeeDtl1', row);
        },
        canclick11(row, groupType) {
            this.$emit('toSeeDtl2', { row: row, groupType: groupType });
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

.levelQueryInfoBox {
    display: flex;
    margin-bottom: 10px;
}

.publicCss {
    width: 220px;
    margin-right: 10px;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
