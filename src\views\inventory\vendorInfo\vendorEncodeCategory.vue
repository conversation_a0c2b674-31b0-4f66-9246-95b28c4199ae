<template>
  <!-- 系列编码类目 -->
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <div class="header">
        <el-input placeholder="系列编码" v-model="ListInfo.styleCode" maxlength="50" class="publicMargin"
          style="width: 150px;" clearable>
        </el-input>
        <el-select v-model="ListInfo.categoryId" placeholder="请选择类目" clearable filterable
          style="width: 150px;margin-right: 10px;">
          <el-option v-for="item in options" :key="item.id" :label="item.categoryName" :value="item.id">
          </el-option>
        </el-select>
        <el-button type="primary" @click="getCodeList('click')">搜索</el-button>
        <div class="button-group">
          <el-button type="primary" @click="handleSet30">设置近30天销量</el-button>
          <el-button type="primary" @click="handleSet">设置类目</el-button>
          <el-button type="primary" @click="handleAdd">添加类目</el-button>
          <el-button type="primary" @click="clearCategory">清空类目</el-button>
          <el-button type="primary" @click="setDockUser">设置对接人</el-button>
          <el-button type="primary" @click="setShow('xl')">一键上下架系列编码</el-button>
          <el-button type="primary" @click="setShow('good')">一键上下架商品编码</el-button>
          <el-button type="primary" @click="showCreateStyleCode">新建系列编码</el-button>
          <el-button type="primary" @click="showMoveStyleCode">一键更改系列</el-button>
          <!-- <el-button type="primary" class="rightMar" @click="openImport">导入</el-button> -->
        </div>
      </div>
    </template>
    <!--列表-->
    <vxetablebase :id="'vendorEncodeCategory202408041612'" ref="table" @select="checkboxRangeEnd" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
      @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :treeProp="{ rowField: 'id', parentField: 'pId', transform: true, }" style="width: 100%; height: 700px; margin: 0">

      <template slot="right">
        <vxe-column title="图片操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex">
              <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                :on-success="handleSuccess" :file-list="picFileList" list-type="picture" :show-file-list="false"
                accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                <el-button class="addsc" @click="picClick(row)" type="text" v-if="row.pId == -1">更改图片</el-button>
              </el-upload>
              <el-button class="addsc" @click="recoverPicture(row)" type="text" v-if="row.pId == -1">恢复图片</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="detailPagechange" @size-change="detailSizechange" />
    </template>

    <el-dialog title="添加类目" :visible.sync="dialogappend" width="18%" :close-on-click-modal="false" v-dialogDrag>
      <el-input v-model="addCategoryName.categoryName" placeholder="请输入类目" clearable maxlength="50"
        style="margin-top: 10px;"></el-input>
      <div class="btnGroup">
        <el-button @click="dialogappend = false">取消</el-button>
        <el-button @click="handleSubmit" type="primary">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="设置类目" :visible.sync="dialogsettings" width="18%" :close-on-click-modal="false" v-dialogDrag>
      <div class="selectBox">
        <el-select v-model="setCategoryName.providerCategoryIds" placeholder="请选择类目" multiple clearable filterable
          collapse-tags @change="change">
          <el-option v-for="item in options" :key="item.id" :label="item.categoryName" :value="item.id">
          </el-option>
        </el-select>
      </div>
      <div class="btnGroup">
        <el-button @click="dialogsettings = false">取消</el-button>
        <el-button @click="handleSetSubmit" type="primary">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="设置近30天销量" :visible.sync="dialogsalesvolume" width="18%" :close-on-click-modal="false" v-dialogDrag>
      <div class="input-group">
        <el-input-number v-model="setCategoryName.startMultiple" placeholder="" style="width: 90px;" clearable
          maxlength="50" :controls="false" :precision="3" :max="10"></el-input-number>
        <span style="margin:0 10px">至</span>
        <el-input-number v-model="setCategoryName.endMultiple" placeholder="" style="width: 90px;" clearable
          maxlength="50" :controls="false" :precision="3" :max="10"></el-input-number>
      </div>
      <div class="btnGroup">
        <el-button @click="dialogsalesvolume = false">取消</el-button>
        <el-button @click="Set30Submit" type="primary">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <el-row>
        <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
          <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
            accept=".xlsx" :on-change="uploadFile" :file-list="fileList" :on-remove="uploadRemove">
            <template #trigger>
              <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button style="margin-left: 10px" size="small" type="success" @click="submitUpload">上传</el-button>
          </el-upload>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="更改名称" :visible.sync="nameVisible" width="20%" v-dialogDrag>
      <div style="display: flex;justify-content: center;margin-top: 10px;">
        <el-input v-model="setCategoryName.goodsName" placeholder="商品名称" maxlength="50" clearable style="width: 220px;" />
      </div>
      <div class="btnGroup">
        <el-button @click="nameVisible = false">取消</el-button>
        <el-button @click="handleNameSetSubmit" type="primary">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="设置对接人" :visible.sync="dockVisible" width="20%" v-dialogDrag>
      <div style="display: flex;justify-content: center;margin-top: 10px;">
        <el-select v-model="setCategoryName.dockUserId" placeholder="请选择" style="width: 220px;" @change="changeAssignment"
          clearable filterable>
          <el-option v-for="item in procurementList" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </div>
      <div class="btnGroup">
        <el-button @click="dockVisible = false">取消</el-button>
        <el-button @click="handleDockSetSubmit" type="primary">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="新建系列编码" :visible.sync="createStyleCode.visible" width="20%" v-dialogDrag :close-on-click-modal="false">
      <div style="display: flex;justify-content: center;margin-top: 10px;">
        <el-input v-model.trim="createStyleCode.filter.styleCode" placeholder="系列编码" maxlength="25" clearable style="width: 220px;" />
      </div>
      <div class="btnGroup">
        <el-button @click="createStyleCode.visible = false">取消</el-button>
        <el-button @click="createStyleCodeSave" type="primary">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="一键更改系列编码" :visible.sync="moveStyleCode.visible" width="20%" v-dialogDrag :close-on-click-modal="false">
      <div style="display: flex;justify-content: center;margin-top: 10px;">
        <el-select v-model="moveStyleCode.filter.styleCode" :filter-method="filterStyleCode" placeholder="请选择" style="width: 220px;" clearable filterable>
          <el-option v-for="item in moveStyleCodes" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>
      </div>
      <div class="btnGroup">
        <el-button @click="moveStyleCode.visible = false">取消</el-button>
        <el-button @click="moveStyleCodeSave" type="primary">确定</el-button>
      </div>
    </el-dialog>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
  getProviderStylePageList,
  addProviderCategory,
  getProviderCategory,
  setProviderStyleCategory,
  setProviderStyleMultiple,
  setProviderStyleIsShow,
  importProviderGoodsDataAsync,
  setProviderStylePic,
  setProviderGoodName,
  setProviderStyleDockUser,
  getBrandUsers,
  setProviderGoodIsShow,
  addNewProviderStyle,
  moveProviderGoodToProviderStyle,
  getTop50ProviderStyleByStyleCode
} from '@/api/openPlatform/ProviderQuotation'
const tableCols = [
  { istrue: true, label: '', type: "checkbox" },
  { istrue: true, prop: 'stylePic', chiprop: 'goodPic', label: '原图', sortable: 'custom', type: 'danimages', treeNode: true },
  { istrue: true, prop: 'setStylePic', label: '新图', sortable: 'custom', type: 'danimages' },
  { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom' },
  { istrue: true, prop: 'dockUserName', label: '对接人', sortable: 'custom' },
  { istrue: true, prop: 'isShow', label: '状态', sortable: 'custom', formatter: (row) => row.isShow == 1 ? '上架' : '下架', },
  { istrue: true, prop: 'providerCategoryNames', label: '类目', sortable: 'custom' },
  { istrue: true, prop: 'saleQty30', label: '近30天销量(真实)', sortable: 'custom',formatter: (row) => {
      if (row.pId == -1) {
        return row.styleSaleQty30
      } else {
        return row.saleQty30
      }
    }
  },
  { istrue: true, prop: 'multiple', label: '设置倍数', sortable: 'custom' },
  { istrue: true, prop: 'calSaleQty30', label: '近30天销量(虚拟)', sortable: 'custom',formatter: (row) => {
      if (row.pId == -1) {
        return row.styleCalSaleQty30
      } else {
        return row.calSaleQty30
      }
    }
  },
  { istrue: true, prop: 'goodCode', label: '商品编码', sortable: 'custom' },
  { istrue: true, prop: 'goodName', label: '商品名称', sortable: 'custom' },
  {
    istrue: true, type: 'button', label: '更改名称', width: '90', btnList: [
      { label: "更改名称", display: (row) => row.pId == -1, handle: (that, row) => that.editName(row) },
    ]
  },
]
export default {
  name: "vendorEncodeCategory",
  components: { MyContainer, vxetablebase, MyConfirmButton },
  data() {
    return {
      ListInfo: {
        currentPage: 1,//当前页
        pagesize: 50,//每页条数
        orderBy: null,//排序字段
        isAsc: true,//是否升序
        styleCode: null,//系列编码
        categoryId: null,//类目id
        openId: null,//openId
        goodCode: null,//商品编码
        isBY: null,//是否包邮
        isContaisTax: null,//是否含税
        providerName: null,//供应商名称
      },//列表参数
      setCategoryName: {
        styleCodes: [], //系列编码
        goodCodes: [],//商品编码
        startMultiple: null,//开始倍数
        endMultiple: null,//结束倍数
        providerCategoryIds: [],//类目id
        isShow: null,//是否上下架
        picUrl: null,//图片地址
        goodsName: null,//商品名称
        dockUserId: null,//对接人id
        dockUserName: null,//对接人名称
      },//设置类目,销量,上下架
      dialogsalesvolume: false,//设置三十天销量弹层
      dialogsettings: false,//设置类目弹层
      dialogappend: false,//添加类目弹层
      nameVisible: false,//更改名称弹层
      dockVisible: false,//设置对接人弹层
      addCategoryName: {
        id: 0,//系列编码
        categoryName: null,//类目名称
      },//添加类目
      options: [],//类目列表
      tableData: [],//列表数据
      tableCols,
      that: this,
      total: 0,
      pageLoading: true,
      maxMultiple: 0,//最大倍数
      fileList: [],
      dialogVisible: false,//导入弹层
      picFileList: [],//图片上传
      styleCode: [],//系列编码
      goodCodes: [],//商品编码
      allIsShow: null,//全部上下架
      allShowStatus: null,
      procurementList: [],//采购人员列表
      type: null,//类型
      styleArr: [],//系列编码
      goodsArr: [],//商品编码
      moveStyleCodes:[],
      createStyleCode:{
        visible:false,
        filter:{
          styleCode:null
        }
      },
      moveStyleCode:{
        visible:false,
        filter:{
          styleCode:null,
          moveGoodCodes:[]
        }
      }
    };
  },

  mounted() {
    this.getCodeList()
    this.getCategoryList()
  },
  methods: {
    async handleDockSetSubmit() {
      const { success } = await setProviderStyleDockUser(this.setCategoryName)
      if (success) {
        this.$message.success('设置对接人成功')
        this.clear()
        this.getCodeList()
        this.dockVisible = false
      }
    },
    changeAssignment(e) {
      this.setCategoryName.dockUserName = this.procurementList.filter(item => item.value == e)[0].label
    },
    //设置对接人
    async setDockUser() {
      this.setCategoryName.styleCodes = this.styleCode
      if (this.setCategoryName.styleCodes.length == 0) {
        return this.$message.error('您没有选择任何系列编码!')
      }
      const { data, success } = await getBrandUsers({ userName: null })
      if (success) {
        this.procurementList = data.map(item => {
          return {
            label: item.value,
            value: item.key
          }
        })
        this.dockVisible = true
      } else {
        this.$message.error('获取采购人员列表失败')
      }
    },
    clear() {
      this.setCategoryName = {
        styleCodes: [], //系列编码
        goodCodes: [],//商品编码
        startMultiple: null,//开始倍数
        endMultiple: null,//结束倍数
        providerCategoryIds: [],//类目id
        isShow: null,//是否上下架
        picUrl: null,//图片地址
        goodsName: null,//商品名称
        dockUserId: null,//对接人id
        dockUserName: null,//对接人名称
      }//设置类目,销量,上下架
      this.styleCode = []
      this.goodCodes = []
      this.allIsShow = null
      this.allShowStatus = null
    },
    //更改名称确定
    async handleNameSetSubmit() {
      const { success } = await setProviderGoodName(this.setCategoryName)
      if (success) {
        this.$message.success('更改成功')
        this.nameVisible = false
        this.clear()
        this.getCodeList()
      }
    },
    //更改名称打开弹窗
    editName(row) {
      if (row.pId != -1) {
        this.nameVisible = true
        this.setCategoryName.goodCodes = [row.goodCode]
      }
    },
    picClick(row) {
      if (row.pId == -1) {
        this.setCategoryName.styleCodes = [row.styleCode]
      }
    },
    //图片上传成功回调
    async handleSuccess({ data }) {
      this.setCategoryName.picUrl = data.url
      const { success } = await setProviderStylePic(this.setCategoryName)
      if (success) {
        this.$message.success('更改图片成功')
        this.clear()
        this.getCodeList()
      }
    },
    recoverPicture(row) {
      this.setCategoryName.styleCodes = [row.styleCode]
      this.setCategoryName.picUrl = null
      this.$confirm('此操作将恢复该编码图片, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await setProviderStylePic({ styleCodes: [row.styleCode], picUrl: null })
        if (success) {
          this.$message.success('恢复图片成功')
          this.clear()
          this.getCodeList()
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消恢复'
        });
      });
    },
    //打开导入弹层
    openImport() {
      this.fileList = []
      this.dialogVisible = true
    },
    //确定导入
    async submitUpload() {
      if (this.fileList.length == 0) {
        this.$message.warning('您没有选择任何文件！')
        return
      }
      const form = new FormData();
      form.append("upfile", this.fileList[0].raw);
      const { success } = await importProviderGoodsDataAsync(form)
      if (success) {
        this.getCodeList()
        this.$message.success('上传成功')
        this.fileList = []
        this.dialogVisible = false
      }
    },
    //清空文件
    uploadRemove() {
      this.fileList = []
    },
    //文件上传
    async uploadFile(file, fileList) {
      this.fileList = fileList
    },
    //页面数量改变
    detailSizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pagesize = val;
      this.getCodeList();
    },
    //当前页改变
    detailPagechange(val) {
      this.ListInfo.currentPage = val;
      this.getCodeList();
    },
    //设置上下架
    setShow(type) {
      if (type == 'xl') {
        this.type = type
        this.publicSet(this.styleArr)
        this.setCategoryName.styleCodes = this.styleCode
        if (this.setCategoryName.styleCodes.length == 0) {
          return this.$message.error('您没有选择任何系列编码!')
        }
        if (!this.allIsShow) {
          return this.$message.error('系列编码上下架状态不一致!')
        }
      }
      if (type == 'good') {
        this.type = type
        this.publicSet(this.goodsArr)
        this.setCategoryName.goodCodes = this.goodCodes
        if (this.setCategoryName.goodCodes.length == 0) {
          return this.$message.error('您没有选择任何商品编码!')
        }
        if (!this.allIsShow) {
          return this.$message.error('商品编码上下架状态不一致!')
        }
      }
      this.$confirm('此操作将设置该编码上下架, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        if (type == 'xl') {
          const { success } = await setProviderStyleIsShow({ styleCodes: this.setCategoryName.styleCodes, isShow: this.allShowStatus == 1 ? 0 : 1 })
          if (success) {
            this.allIsShow = null
            this.getCodeList()
            this.$message.success('设置系列编码上下架成功')
          }
        }
        if (type == 'good') {
          console.log(this.allShowStatus, 'this.allShowStatus');
          const { success } = await setProviderGoodIsShow({ goodCodes: this.setCategoryName.goodCodes, isShow: this.allShowStatus == 1 ? 0 : 1 })
          if (success) {
            this.allIsShow = null
            this.clear()
            this.getCodeList()
            this.$message.success('设置商品编码上下架成功')
          }
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消设置'
        });
      });
    },
    //清空类目
    clearCategory() {
      this.setCategoryName.styleCodes = this.styleCode
      //如果没有选择编码，提示选择编码
      if (this.setCategoryName.styleCodes.length == 0) {
        this.$message.error('您没有选择任何数据!')
        return
      }
      this.$confirm('此操作将清空该数据类目, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await setProviderStyleCategory(this.setCategoryName)
        if (success) {
          this.clear()
          this.getCodeList()
          this.$message.success('清空类目成功')
          this.dialogsettings = false
        }
      }).catch(() => {
        this.clear()
        this.$message({
          type: 'info',
          message: '已取消清空'
        });
      });
    },
    //设置三十天销量确定
    async Set30Submit() {
      //如果没有写倍数，提示写倍数
      const a = this.setCategoryName.startMultiple
      const b = this.setCategoryName.endMultiple
      if ((a <= 0 || a == null || a == undefined || a == '') || (b <= 0 || b == null || b == undefined || b == '')) {
        this.$message.error('倍数不能小于0或者空')
        return
      }
      //如果endMultiple小于startMultiple,提示endMultiple不能小于startMultiple
      if (this.setCategoryName.endMultiple < this.setCategoryName.startMultiple) {
        this.$message.error('结束倍数不能小于开始倍数')
        return
      }
      //如果this.setCategoryName.startMultiple小于this.maxMultiple就提示
      if (this.setCategoryName.startMultiple < this.maxMultiple) {
        this.$message.error('开始倍数不能小于最大倍数')
        return
      }
      const { success } = await setProviderStyleMultiple(this.setCategoryName)
      if (success) {
        //清空倍数
        this.clear()
        this.$message.success('设置成功')
        this.getCodeList()
        this.dialogsalesvolume = false
      }
    },
    //设置三十天销量
    handleSet30() {
      //清空倍数
      this.setCategoryName.startMultiple = 0
      this.setCategoryName.endMultiple = 0
      this.setCategoryName.styleCodes = this.styleCode
      //如果没有选择编码，提示选择编码
      if (this.setCategoryName.styleCodes.length == 0) {
        this.$message.error('您没有选择任何数据!')
        return
      }
      //如果有最大倍数,就将最大倍数赋值给startMultiple  
      if (this.maxMultiple) {
        this.setCategoryName.startMultiple = this.maxMultiple
      }
      this.dialogsalesvolume = true
    },
    change(e) {
      this.setCategoryName.providerCategoryIds = e
    },
    publicSet(arr) {
      if (arr.length == 1) {
        this.allIsShow = true //如果只有一条数据直接就状态一致
        this.allShowStatus = arr[0].isShow
      } else {
        this.allIsShow = arr.every(item => item.isShow == arr[0].isShow)//否则就判断每个状态是否一致
        if (this.allIsShow && arr.length > 0) {
          this.allShowStatus = arr[0].isShow //如果每个状态都相同就直接赋值一个状态
        }
      }
    },
    //点击复选框
    checkboxRangeEnd(row) {
      //系列编码和商品编码一键上下架
      this.styleArr = row.filter(item => item.pId == -1)//取出所有的系列编码
      this.styleCode = this.styleArr.map(item => item.styleCode)
      this.goodsArr = row.filter(item => item.pId != -1)//取出所有的商品编码
      this.goodCodes = this.goodsArr.map(item => item.goodCode)
      //取出row中的最大倍数
      this.maxMultiple = Math.max.apply(Math, this.styleArr.map(item => item.multiple))
    },
    //设置类目确定
    async handleSetSubmit() {
      //如果没有选择类目，提示选择类目
      if (this.setCategoryName.providerCategoryIds.length == 0) {
        this.$message.error('请选择类目!')
        return
      }
      const { success } = await setProviderStyleCategory(this.setCategoryName)
      if (success) {
        this.clear()
        this.getCodeList()
        this.$message.success('设置类目成功')
        this.dialogsettings = false
      }
    },
    //获取类目列表
    async getCategoryList() {
      const { data, success } = await getProviderCategory()
      if (success) {
        this.options = data
      } else {
        this.$message.error('获取类目列表失败')
      }
    },
    async handleSubmit() {
      //如果输入的类目为空，提示输入类目
      if (this.addCategoryName.categoryName == null || this.addCategoryName.categoryName == '') {
        this.$message.error('请输入类目')
        return
      }
      const { success } = await addProviderCategory(this.addCategoryName)
      if (success) {
        this.$message.success('添加类目成功')
        //拉一次类目列表
        this.getCategoryList()
        this.dialogappend = false
      } else {
        this.$message.error('添加类目失败')
        this.dialogappend = false
      }
    },
    handleAdd() {
      this.addCategoryName.categoryName = null
      this.dialogappend = true;
    },
    handleSet() {
      this.setCategoryName.styleCodes = this.styleCode
      //如果没有选择编码，提示选择编码
      if (this.setCategoryName.styleCodes.length == 0) {
        this.$message.error('您没有选择任何数据!')
        return
      }
      this.getCategoryList()
      this.dialogsettings = true;
    },
    //获取系别编码列表
    async getCodeList(type) {
      if (type == 'click') {
        this.ListInfo.currentPage = 1
      }
      //如果
      if (this.ListInfo.styleCode) {
        this.ListInfo.styleCode = this.ListInfo.styleCode ? this.ListInfo.styleCode.replace(/\s+/g, "") : null;
      }
      const { data, success } = await getProviderStylePageList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.pageLoading = false
        this.clear()
      } else {
        this.$message.error('获取系别编码列表失败')
      }
    },
    //排序查询
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getCodeList()
      }
    },
    async showCreateStyleCode(){
      this.createStyleCode.visible=true;
      this.createStyleCode.filter.styleCode=null;
    },
    async createStyleCodeSave() {
      if (this.createStyleCode.filter.styleCode == null || this.createStyleCode.filter.styleCode.length == 0) {
          this.$message.error('请填写系列编码名称!')
          return
      }
      const { data, success } = await addNewProviderStyle(this.createStyleCode.filter)
      if (success) {
        this.$message.success('添加成功!')
        await this.getCodeList();
        this.createStyleCode.filter.styleCode = '';
        this.createStyleCode.visible = false;
      }  
    },

    async showMoveStyleCode(){
      this.moveStyleCode.filter.moveGoodCodes = this.goodCodes
      if (this.moveStyleCode.filter.moveGoodCodes.length == 0) {
         this.$message.error('您没有选择任何商品编码!')
         return;
      } 
      await this.filterStyleCode('');
      this.moveStyleCode.visible=true;
      this.moveStyleCode.filter.styleCode=null; 
    },
    filterStyleCode(val) {
      this.getStyleCodeByKey({ styleCode: val });
    },
    async getStyleCodeByKey(val) {
      const res = await getTop50ProviderStyleByStyleCode(val);
      this.moveStyleCodes = res.data;
    },
    async moveStyleCodeSave() {
      if (this.moveStyleCode.filter.styleCode == null || this.moveStyleCode.filter.styleCode.length == 0) {
          this.$message.error('请选择系列编码!')
          return
      }
      const { data, success } = await moveProviderGoodToProviderStyle(this.moveStyleCode.filter)
      if (success) {
        this.$message.success('更改成功!')
        await this.getCodeList(); 
        this.moveStyleCode.visible = false;
      }  
    }
  },
};
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  margin-bottom: 10px;
}

.publicMargin {
  margin-right: 10px;
}

.button-group {
  margin-left: 10px;
}

.input-group {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.input-group input {
  width: 200px;
}

.btnGroup {
  padding: 20px 50px 0;
  display: flex;
  justify-content: space-between;
}

.selectBox {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
