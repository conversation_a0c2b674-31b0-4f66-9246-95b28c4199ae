<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :tableData='cuishoulist' @select='selectchange' :isSelection='false' :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableCols='tableCols' :loading="listLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;" v-if="false">
                        <el-input v-model="Filter.id" placeholder="数据ID" style="width:120px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.UseMonth" placeholder="核算月份" style="width:120px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.ShopName" placeholder="店铺" style="width:120px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.ProductID" placeholder="id" style="width:120px;" />
                    </el-button>
                    <!-- <el-button type="text" size="medium" disabled style="color: red;">周转对比:</el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input-number v-model="filter1.startdiff"></el-input-number>至<el-input-number v-model="filter1.enddiff"></el-input-number>
            </el-button> -->
                    <el-button type="primary" @click="onSearch">查询</el-button>

                    <el-button type="primary" @click="onImportSyj">导入</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getcuishouList" />
        </template>

        <el-dialog title="导入催收礼金汇总" :visible.sync="dialogVisibleSyj" width="30%" v-dialogDrag>
            <span>
                <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传</my-confirm-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>

    import { importCuishouAsync, getCuishouList, deleteCuishouBatch } from '@/api/financial/yyfy'
    import dayjs from "dayjs";
    import cesTable from "@/components/Table/table.vue";
    import { formatTime } from "@/utils";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    const tableCols = [
        { istrue: true, prop: 'productID', label: 'id', width: '200', sortable: 'custom' },
        { istrue: true, prop: 'useMoney', label: '催收费用', width: '200', sortable: 'custom' },
        { istrue: true, prop: 'useMonth', label: '核算月份', width: '200', sortable: 'custom' },
        { istrue: true, prop: 'shopName', label: '店铺', width: '200', sortable: 'custom' },

        { istrue: true, prop: 'createdTime', label: '导入时间', width: '200', sortable: 'custom' },
        { istrue: true, prop: 'batchNumber', label: '导入批次', width: '200', sortable: 'custom' },
        { istrue: true, type: "button", label: '操作', width: "430", btnList: [{ label: "删除批次", handle: (that, row) => that.deleteBatch(row) }] }
    ];
    export default {
        name: "Users",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
        data() {
            return {
                that: this,
                Filter: {
                },
                shopList: [],
                userList: [],
                groupList: [],
                cuishoulist: [],
                tableCols: tableCols,
                total: 0,
                summaryarry: {},
                pager: { OrderBy: "id", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                //
                selids: [],
                dialogVisibleSyj: false,
                fileList: [],
            };
        },
        async mounted() {
        },
        methods: {
            async deleteBatch(row) {
                var that = this;
                this.$confirm("此操作将删除此批次导入催收礼金汇总数据?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(async () => {
                        await deleteCuishouBatch({ batchNumber: row.batchNumber })
                        that.$message({ message: '已删除', type: "success" });
                        that.onRefresh()

                    });

            },
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            onImportSyj() {
                this.dialogVisibleSyj = true
            },
            async uploadFile2(item) {
                const form = new FormData();
                form.append("upfile", item.file);
                const res = importCuishouAsync(form);
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
            },
            async uploadSuccess2(response, file, fileList) {
                fileList.splice(fileList.indexOf(file), 1);
            },
            async onSubmitupload2() {
                this.$refs.upload2.submit()
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.getcuishouList();
            },
            async getcuishouList() {
                if (this.Filter.UseDate) {
                    this.Filter.startAccountDate = this.Filter.UseDate[0];
                    this.Filter.endAccountDate = this.Filter.UseDate[1];
                }
                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,

                };

                console.log(para)

                this.listLoading = true;
                const res = await getCuishouList(params);
                console.log(res)
                this.listLoading = false;
                console.log(res.data.list)
                //console.log(res.data.summary)

                this.total = res.data.total
                this.cuishoulist = res.data.list;

                this.summaryarry = res.data.summary;


                console.log(res.data);
                //this.summaryarry=res.data.summary;
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            }
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
