<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right" label-width="60px">
                <el-form-item label="平台:">
                    <el-select filterable v-model="filter.platform" placeholder="请选择平台" @change="onchangeplatform" clearable style="width: 130px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="店铺:" label-position="right">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="请选择" class="el-select-content" style="width:260px">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="运营组:">
                    <el-select v-model="filter.groupId" placeholder="运营组" :clearable="true" :collapse-tags="true" style="width: 120px" filterable>
                        <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="账号:">
                    <el-input v-model="filter.accountNum" placeholder="账号" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :loading="listLoading" :tableHandles='tableHandles'>
        </ces-table>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog :title="dialogTitle" :visible.sync="dialogAddFormVisible" width="800px" style="width: 100%;" v-dialogDrag v-loading="addFormLoading" :close-on-click-modal="false" element-loading-text="拼命加载中" @close="closeAddForm">
            <el-form ref="addForm" :model="addForm" :rules="addFormRules" label-width="120px">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="platform" label="平台">
                            <el-select v-model="addForm.platform" placeholder="请选择平台" style="width:100%;" @change="onchangeformplatform">
                                <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="shopCode" label="店铺">
                            <el-select v-model="addForm.shopCode" placeholder="请选择店铺" style="width:100%;">
                                <el-option v-for="item in formShopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="accountNum" label="账号">
                            <el-input v-model="addForm.accountNum" auto-complete="off" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="phoneNum" label="绑定手机号">
                            <el-input v-model="addForm.phoneNum" auto-complete="off" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="groupId" label="登记运营组">
                            <el-select v-model="addForm.groupId" placeholder="请选择登记运营组组" style="width:100%;">
                                <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="curUseUserName" label="当前使用人">
                            <el-input v-model="addForm.curUseUserName" auto-complete="off" :disabled="true" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item prop="applyReason" label="申请理由">
                            <el-input v-model="addForm.applyReason" auto-complete="off" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :hidden="true">
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="sharedAccountId" label="主键">
                            <el-input v-model="addForm.sharedAccountId" auto-complete="off" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="curUseUserId" label="当前使用人">
                            <el-input v-model="addForm.curUseUserId" auto-complete="off" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogAddFormVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :validate="addFormValidate" :loading="addLoading" @click="onAddEditSave" />
                </div>
            </template>

        </el-dialog>
    </my-container>
</template>

<script>
    import { formatTime } from "@/utils";
    import MyContainer from '@/components/my-container';
    import cesTable from "@/components/Table/table.vue";
    import { platformlist } from '@/utils/tools'
    import MyConfirmButton from '@/components/my-confirm-button';
    import { getDirectorGroupList, getList as getshopList } from '@/api/operatemanage/base/shop';
    import {
        getSharedAccountPageList,
        getSharedAccountById,
        saveSharedAccount,
        deleteSharedAccount,
        applySharedAccount,
        downSharedAccount
    } from '@/api/operatemanage/productalllink/sharedaccount';

    const tableCols = [
        { istrue: true, prop: 'sharedAccountId', label: '主键', width: '120', display: false },
        { istrue: true, prop: 'platform', label: '平台', width: '100', sortable: 'custom', formatter: (row) => row.platformName || ' ' },
        { istrue: true, prop: 'phoneNum', label: '绑定手机号', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'accountNum', label: '账号', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'shopCode', label: '店铺', width: '260', sortable: 'custom', formatter: (row) => row.shopName || ' ' },
        { istrue: true, prop: 'applyReason', label: '申请理由', sortable: 'custom' },
        { istrue: true, prop: 'groupId', label: '登记运营组', width: '120', sortable: 'custom', formatter: (row) => row.groupName || ' ' },
        { istrue: true, prop: 'curUseUserName', label: '当前使用人', width: '120', sortable: 'custom' },
        { istrue: true, prop: 'status', label: '状态', width: '120', sortable: 'custom', formatter: (row) => row.statusName || ' ' },
        { istrue: true, prop: 'lastDownTime', label: '最后下线时间', width: '150', sortable: 'custom', formatter: (row) => row.lastDownTime == null ? null : formatTime(row.lastDownTime, 'YYYY-MM-DD HH:mm:ss') },
        {
            istrue: false, type: 'button', label: '操作', width: '180', fixed: 'right',
            btnList: [
                { label: "编辑", handle: (that, row) => that.onEdit(row), permission: "api:operatemanage:sharedaccount:SaveSharedAccount" },
                { label: "申请", handle: (that, row) => that.onApply(row), permission: "api:operatemanage:sharedaccount:ApplySharedAccount" },
                { label: "下线", handle: (that, row) => that.onDown(row), permission: "api:operatemanage:sharedaccount:DownSharedAccount" },
                { label: "删除", handle: (that, row) => that.onDelete(row), permission: "api:operatemanage:sharedaccount:DeleteSharedAccount" }
            ]
        }
    ];
    const tableHandles1 = [
        { label: "新增", handle: (that) => that.onAdd(), permission: "api:operatemanage:sharedaccount:SaveSharedAccount" },
    ];
    export default {
        name: 'Roles',
        components: { cesTable, MyContainer, MyConfirmButton },
        props: {

        },
        data() {
            return {
                that: this,
                sels: [],
                //界面标题
                filter: {
                    ReceiptNo: null
                },
                platformlist: platformlist,//平台下拉
                shopList: [],//商店下拉
                formShopList: [],//商店下拉
                groupList: [],//运营组下拉
                list: [],
                summaryarry: {},
                pager: { OrderBy: "createdTime", IsAsc: false },
                tableCols: tableCols,
                tableHandles: tableHandles1,
                total: 0,
                listLoading: false,
                pageLoading: false,
                dialogTitle: "新增",
                dialogAddFormVisible: false,
                addFormLoading: false,
                addLoading: false,
                addForm: {
                    sharedAccountId: 0,
                    platform: null,
                    shopCode: null,
                    accountNum: null,
                    phoneNum: null,
                    groupId: null,
                    applyReason: null,
                    status: null,
                    curUseUserId: null,
                    curUseUserName: null,
                },
                addFormRules: {
                    platform: [{ required: true, message: '请选择平台', trigger: 'blur' }],
                    shopCode: [{ required: true, message: '请选择店铺', trigger: 'blur' }],
                    accountNum: [{ required: true, message: '请输入账号', trigger: 'blur' }],
                    phoneNum: [{ required: true, message: '请输入绑定手机号', trigger: 'blur' }],
                    groupId: [{ required: true, message: '请选择运营组', trigger: 'blur' }],
                    applyReason: [{ required: true, message: '请输入申请理由', trigger: 'blur' }]
                },
            }
        },
        async mounted() {
            await this.getGroupList();
            await this.onSearch();
        },
        methods: {
            //查询：平台联动商店
            async onchangeplatform(val) {
                const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
                this.shopList = res1.data.list
            },
            //获取运营组下拉
            async getGroupList() {
                var res2 = await getDirectorGroupList();
                this.groupList = res2.data?.map(item => { return { value: item.key, label: item.value }; });
            },
            //获取查询条件
            getCondition() {
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }
                return params;
            },
            //查询第一页
            async onSearch() {
                this.$refs.pager.setPage(1)
                await this.getlist()
            },
            //分页查询
            async getlist() {
                this.filter.BeginDate = null;
                this.filter.EndDate = null;
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true
                var res = await getSharedAccountPageList(params);
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                //this.summaryarry = res.data.summary;
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
            },
            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderBy = column.prop;
                    this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selsChange: function (sels) {
                this.sels = sels
            },
            async cleraForm() {
                this.addForm = {
                    sharedAccountId: 0,
                    platform: null,
                    shopCode: null,
                    accountNum: null,
                    phoneNum: null,
                    groupId: null,
                    applyReason: null,
                    status: null,
                    curUseUserId: null,
                    curUseUserName: null,
                };
            },
            //新增
            async onAdd() {
                this.dialogAddFormVisible = true;
                this.addFormLoading = false;
                this.dialogTitle = "编辑";
            },
            //表单：平台联动商店
            async onchangeformplatform(val) {
                const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
                this.addForm.shopCode = null;
                this.formShopList = res1.data.list;
            },
            //编辑
            async onEdit(row) {
                this.dialogAddFormVisible = true;
                this.addFormLoading = true;
                this.dialogTitle = "编辑";
                var editData = await getSharedAccountById(row.sharedAccountId);
                if (editData) {
                    await this.onchangeformplatform(editData.platform);

                    this.addForm.sharedAccountId = editData.sharedAccountId;
                    this.addForm.platform = editData.platform;
                    this.addForm.shopCode = editData.shopCode;
                    this.addForm.accountNum = editData.accountNum;
                    this.addForm.phoneNum = editData.phoneNum;
                    this.addForm.groupId = editData.groupId.toString();
                    this.addForm.applyReason = editData.applyReason;
                    this.addForm.curUseUserId = editData.curUseUserId;
                    this.addForm.curUseUserName = editData.curUseUserName;
                }
                this.addFormLoading = false;
            },
            //申请
            async onApply(row) {
                this.$confirm('确定要申请【' + row.accountNum + '】吗?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    const res = await applySharedAccount(row.sharedAccountId);
                    if (res?.success) {
                        this.$message({ type: 'success', message: '申请已发起，请注意在钉钉查收验证码!' });
                        this.onSearch();
                    } else {
                        //this.$message({ type: 'error', message: '申请失败!' });
                    }
                }).catch(() => {
                    this.$message({ type: 'info', message: '已取消申请' });
                });
            },
            //下线
            async onDown(row) {
                this.$confirm('确定要下线【' + row.accountNum + '】吗?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    const res = await downSharedAccount(row.sharedAccountId);
                    if (res?.success) {
                        this.$message({ type: 'success', message: '下线成功!' });
                        this.onSearch();
                    } else {
                        //this.$message({ type: 'error', message: '下线失败!' });
                    }
                }).catch(() => {
                    this.$message({ type: 'info', message: '已取消下线' });
                });
            },
            //删除
            async onDelete(row) {
                this.$confirm('确定要删除【' + row.accountNum + '】吗?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    const res = await deleteSharedAccount(row.sharedAccountId);
                    if (res?.success) {
                        this.$message({ type: 'success', message: '删除成功!' });
                        this.onSearch();
                    } else {
                        //this.$message({ type: 'error', message: '删除失败!' });
                    }
                }).catch(() => {
                    this.$message({ type: 'info', message: '已取消删除' });
                });
            },
            //新增编辑提交时验证
            addFormValidate: function () {
                let isValid = false
                this.$refs.addForm.validate(valid => {
                    isValid = valid
                })
                return isValid
            },
            //新增编辑保存
            async onAddEditSave() {
                this.addLoading = true;
                const para = _.cloneDeep(this.addForm);
                var res = await saveSharedAccount(para);
                if (!res?.success) {
                    return;
                }
                this.$message({
                    message: this.$t('保存成功'),
                    type: 'success'
                })
                this.addLoading = false;
                this.dialogAddFormVisible = false;
                await this.onSearch();
            },
            async closeAddForm() {
                this.addLoading = false;
                this.cleraForm();
            }
        }
    }
</script>
