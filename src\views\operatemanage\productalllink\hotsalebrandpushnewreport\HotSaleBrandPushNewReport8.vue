<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0; border: 0;">
                    <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                        start-placeholder="作废日期" end-placeholder="作废日期" clearable :picker-options="pickerOptions"
                        style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                </el-button>
                <el-button style="padding: 0;margin: 0; border: 0;">
                    <el-date-picker v-model="filter.timerange2" type="daterange" unlink-panels range-separator="至"
                        start-placeholder="新采购单日期" end-placeholder="新采购单日期" clearable :picker-options="pickerOptions"
                        style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                </el-button>
                <el-button style="padding: 0;margin: 0; border: 0;">
                    <el-input v-model.trim="filter.goodsCode" maxlength="40" clearable placeholder="商品编码"
                        style="width:150px;" />
                </el-button>
                <el-button style="padding: 0;margin: 0; border: 0;">
                    <el-input v-model.trim="filter.styleCode" maxlength="40" clearable placeholder="款式编码"
                        style="width:150px;" />
                </el-button>
                <el-button style="padding: 0;margin: 0; border: 0;">
                    <el-input v-model.trim="filter.zfIndexNo" maxlength="10" clearable placeholder="作废ERP编号"
                        style="width:150px;" />
                </el-button>
                <el-button style="padding: 0;margin: 0; border: 0;">
                    <el-select v-model="filter.isOver" placeholder="是否结束" style="width: 150px" clearable>
                        <el-option label="已结束" value=1 />
                        <el-option label="未结束" value=0 />
                    </el-select>
                </el-button>
                <el-button type="primary" @click="onSearch()">查询</el-button>
            </el-button-group>
        </template>
        <vxetablebase :id="'HotSaleBrandPushNewReport8202408041714'" ref="table" :that='that' :isIndex='true'
            :hasexpand='true' :tablefixed='true' :border="true" @sortchange='sortchange' @select='selectchange'
            :tableData='tableData' :tableCols='tableCols' :showsummary='true' :summaryarry='summaryarry'
            :isSelection="false" :isSelectColumn="false"
            :treeProp="{ rowField: 'rowId', parentField: 'parentId', expandAll: false, transform: true, }"
            style="width: 100%;  margin: 0" v-loading="listLoading" :height="'100%'">
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import datepicker from '@/views/customerservice/datepicker';
import { pickerOptions } from '@/utils/tools';
import dayjs from 'dayjs';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetHotSaleBrandPushNewReport6
} from '@/api/operatemanage/productalllink/alllink'
const tableCols = [
    { sortable: 'custom', width: '150', align: 'center', prop: 'goodsCompeteId', label: 'ID', treeNode: true, },
    { sortable: 'custom', width: '120', align: 'center', prop: 'createdUserName', label: '推荐人', },
    //{ sortable: 'custom', width: '120', align: 'center', prop: 'yhGoodsCode', label: '商品编码', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'styleCode', label: '系列编码', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'zfTime', label: '作废时间', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'zfIndexNo', label: '作废采购单ERP编号' },
    { sortable: 'custom', width: '150', align: 'center', prop: 'newIndexNo', label: '新采购单ERP编号', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'newBuyNo', label: '新采购单号', type: 'click', handle: (that, row) => that.routerLink(row) },
    { sortable: 'custom', width: '150', align: 'center', prop: 'newPurchaseTime', label: '新采购单时间', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'isOver', label: '当前状态', formatter: (row) => (row.isOver == 1 ? "已结束" : "未结束") },
];
export default {
    name: "HotSaleBrandPushNewReport8",
    components: {
        MyContainer, datepicker, vxetablebase
    },
    data() {
        return {
            auditVisible: false,
            activities: [],
            timeRanges: [],
            that: this,
            pickerOptions,
            filter: {
                timerange: [],
                timerange2: [],
                goodsCode: null,
                styleCode: null,
                zfIndexNo: null,
                isOver: null,
            },
            pager: { OrderBy: "zfTime", IsAsc: false },
            tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            summaryarry: {},
        }
    },
    async mounted() {

    },
    computed: {
    },
    methods: {
        routerLink(row) {
            if (row.newBuyNo.length < 3) {
                return;
            }
            this.$emit('afterSave', row.newBuyNo)
            this.$emit('close')
        },
        async loadData(args) {
            console.log(args, "args");
            this.filter.timerange2 = args.timerange2;
            this.onSearch();
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getParam() {
            //推品日期
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.createdStartDate = this.filter.timerange[0];
                this.filter.createdEndDate = this.filter.timerange[1];
            }
            else {
                this.filter.createdStartDate = null;
                this.filter.createdEndDate = null;
            }
            if (this.filter.timerange2 && this.filter.timerange2.length == 2) {
                this.filter.chooseStartDate = this.filter.timerange2[0];
                this.filter.chooseEndDate = this.filter.timerange2[1];
            }
            else {
                this.filter.chooseStartDate = null;
                this.filter.chooseEndDate = null;
            }
            let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            const res = await GetHotSaleBrandPushNewReport6(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
                this.summaryarry = res.data.summary;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}
</style>
