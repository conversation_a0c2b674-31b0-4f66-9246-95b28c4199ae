<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="">
                    <el-select v-model="filter.warehouseCode" style="width: 160px" size="mini" @change="onSearch"
                        clearable>
                        <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.postCode" placeholder="一级岗位编码" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.postName" style="width: 110px" size="mini" @change="onSearch" clearable
                        placeholder="一级岗位名称" filterable>
                        <el-option v-for="item in myOnePostNameList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.workItem" placeholder="二级岗位名称" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-checkbox v-model="filter.isImportDtl">需要导入明细的岗位</el-checkbox>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onAdd">新增</el-button>
                </el-form-item>
                <el-form-item>
                    <!-- v-if="checkPermission(['api:profit:warehousewages:ImportWarehouseWagesWorkItem'])" -->
                    <el-button type="primary" @click="onImport2" v-if="false">导入</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :isSelectColumn="false" :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <el-dialog title="岗位" :visible.sync="dialogVisibleAdd" width="50%" v-dialogDrag :close-on-click-modal="false"
            v-loading="addFromLoading">
            <template>
                <el-form ref="addForm" :model="addFormData" :rules="addFormRules" label-width="130px">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="warehouseCode" label="仓库名称" auto-complete="off">
                                <el-select v-model="addFormData.warehouseCode" style="width:100%;">
                                    <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="postCode" label="一级岗位编码">
                                <el-input v-model="addFormData.postCode" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="postName" label="一级岗位名称">
                                <el-input v-model="addFormData.postName" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="workItem" label="二级岗位名称">
                                <el-input v-model="addFormData.workItem" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                        <!-- <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="defaultWages" label="白班最新工价">
                                <el-input-number v-model="addFormData.defaultWages" :min="0" :max="100000"
                                    placeholder="默认工价" :precision="4" style="width: 100%;" disabled>
                                </el-input-number>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="defaultWages2" label="晚班最新工价">
                                <el-input-number v-model="addFormData.defaultWages2" :min="0" :max="100000"
                                    placeholder="默认工价" :precision="4" style="width: 100%;" disabled>
                                </el-input-number>
                            </el-form-item>
                        </el-col> -->
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="getValueType" label="取值类型">
                                <el-select v-model="addFormData.getValueType" style="width: 100%;">
                                    <!-- <el-option label="从订单日志取值" value="从订单日志取值"></el-option> -->
                                    <el-option label="从聚水潭工作量报表取值" value="从聚水潭工作量报表取值"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-if="false">
                        <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
                            <el-form-item prop="orderLogGetValueColNames" label="订单日志取值名称">
                                <el-input v-model="addFormData.orderLogGetValueColNames" auto-complete="off"
                                    style="width: 85%;" disabled />
                                <el-button style="float: right ; font-size:14px" type="text"
                                    @click="onSel('从订单日志取值')">选择
                                </el-button>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="orderLogGetValueType" label="计件类型">
                                <el-select v-model="addFormData.orderLogGetValueType" style="width: 100%;" clearable>
                                    <el-option label="按订单计件" value="按订单计件"></el-option>
                                    <el-option label="按商品计件" value="按商品计件"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
                            <el-form-item prop="jstWorkGetValueColNames" label="工作量取值列名称">
                                <el-input v-model="addFormData.jstWorkGetValueColNames" auto-complete="off"
                                    style="width: 85%;" disabled />
                                <el-button style="float: right ; font-size:14px" type="text"
                                    @click="onSel('从聚水潭工作量报表取值')">选择
                                </el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="outWarehouseFeeType" label="出仓费计算类型">
                                <el-select v-model="addFormData.outWarehouseFeeType" style="width:100%;">
                                    <el-option key="配货" label="配货" value="配货" />
                                    <el-option key="打包" label="打包" value="打包" />
                                    <el-option key="暂不计算" label="暂不计算" value="暂不计算" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="outWarehouseFeeOrderOrGood" label="出仓费计算方式">
                                <el-select v-model="addFormData.outWarehouseFeeOrderOrGood" style="width:100%;">
                                    <el-option key="按商品数量" label="按商品数量" value="按商品数量" />
                                    <el-option key="按订单计算" label="按订单计算" value="按订单计算" />
                                    <el-option key="暂不计算" label="暂不计算" value="暂不计算" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-tabs type="card" v-model="addFormTabsValue">
                                <el-tab-pane label="新增工作项工价" key="tab1" name="tab1">
                                    <el-container style="height:320px;">
                                        <el-main style="height:320px;">
                                            <div style="height:30px;">
                                                <el-button type="primary" style="height:30px;"
                                                    @click="addRowDate">新增一行</el-button>
                                                <span style="color:red;font-size:12px;">
                                                    &nbsp;&nbsp;更改此工作项的默认工价，自{最新工价起始日}起，此工作项按照{最新工价}计算。
                                                </span>
                                            </div>
                                            <div style="height:290px;">
                                                <el-table :data="addFormData.postDateList" :height="'290px'">
                                                    <el-table-column prop="id" label="id" width="150" v-if="false" />
                                                    <el-table-column prop="newWagesBeginDate" label="最新工价起始日"
                                                        width="160" align="center">
                                                        <template slot-scope="scope">
                                                            <el-date-picker v-model="scope.row.newWagesBeginDate"
                                                                type="date" placeholder="选择日期" style="width:150px"
                                                                value-format="yyyy-MM-dd">
                                                            </el-date-picker>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column prop="newWages" label="白班最新工价" width="210"
                                                        align="center">
                                                        <template slot-scope="scope">
                                                            <el-input-number v-model="scope.row.newWages" :min="-100000"
                                                                style="width:200px" :max="100000" placeholder="白班最新工价"
                                                                :precision="4">
                                                            </el-input-number>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column prop="newWages2" label="晚班最新工价" width="210"
                                                        align="center">
                                                        <template slot-scope="scope">
                                                            <el-input-number v-model="scope.row.newWages2"
                                                                :min="-100000" style="width:200px" :max="100000"
                                                                placeholder="晚班最新工价" :precision="4">
                                                            </el-input-number>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column prop="remark" label="备注">
                                                        <template slot-scope="scope">
                                                            <el-input v-model="scope.row.remark" placeholder="备注">
                                                            </el-input>
                                                        </template>
                                                    </el-table-column>
                                                </el-table>
                                            </div>
                                        </el-main>
                                    </el-container>
                                </el-tab-pane>
                                <el-tab-pane label="新增个人工价" key="tab2" name="tab2">
                                    <el-container style="height:320px;">
                                        <el-main style="height:320px;">
                                            <div style="height:30px;">
                                                <el-button type="primary" style="height:30px;"
                                                    @click="addRowUser">新增一行</el-button>
                                                <span style="color:red;font-size:12px;">
                                                    &nbsp;&nbsp;单独设置此工作项的某人的工价，自{最新工价起始日}起，此工作项此人按照{最新工价}计算。(非必填)
                                                </span>
                                            </div>
                                            <div style="height:290px;">
                                                <el-table :data="addFormData.postUserList" :height="290">
                                                    <el-table-column prop="id" label="id" width="150" v-if="false" />
                                                    <el-table-column prop="userName" label="姓名" width="120">
                                                        <template slot-scope="scope">
                                                            <el-input v-model="scope.row.userName" placeholder="姓名">
                                                            </el-input>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column prop="mobile" label="手机号(可不填)" width="130"
                                                        v-if="false">
                                                        <template slot-scope="scope">
                                                            <el-input v-model="scope.row.mobile" placeholder="手机号">
                                                            </el-input>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column prop="userWagesBeginDate" label="最新工价起始日"
                                                        width="160" align="center">
                                                        <template slot-scope="scope">
                                                            <el-date-picker v-model="scope.row.userWagesBeginDate"
                                                                type="date" placeholder="选择日期" style="width:150px"
                                                                value-format="yyyy-MM-dd">
                                                            </el-date-picker>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column prop="userWages" label="白班最新工价" width="210"
                                                        align="center">
                                                        <template slot-scope="scope">
                                                            <el-input-number v-model="scope.row.userWages"
                                                                :min="-100000" :max="100000" placeholder="白班最新工价"
                                                                :precision="4" style="width:200px">
                                                            </el-input-number>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column prop="userWages2" label="晚班最新工价" width="210"
                                                        align="center">
                                                        <template slot-scope="scope">
                                                            <el-input-number v-model="scope.row.userWages2"
                                                                :min="-100000" :max="100000" placeholder="晚班最新工价"
                                                                :precision="4" style="width:200px">
                                                            </el-input-number>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column prop="remark" label="备注">
                                                        <template slot-scope="scope">
                                                            <el-input v-model="scope.row.remark" placeholder="备注">
                                                            </el-input>
                                                        </template>
                                                    </el-table-column>
                                                </el-table>
                                            </div>
                                        </el-main>
                                    </el-container>
                                </el-tab-pane>
                            </el-tabs>
                        </el-col>
                    </el-row>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisibleAdd = false">取 消</el-button>
                    <my-confirm-button type="submit" :validate="addFormValidate" :loading="addLoading"
                        @click="onAddSave" />
                </span>
            </template>
        </el-dialog>

        <el-dialog title="选择" :visible.sync="dialogVisibleAdd_Sel" width="25%" v-dialogDrag
            :close-on-click-modal="false">
            <template>
                <el-row v-if="this.addFormDataSelType == '从订单日志取值'">
                    <el-col>
                        <el-select v-model="addFormDataSel" placeholder="" :clearable="true" filterable multiple
                            multiple-limit="1" style="width:80%;">
                            <el-option v-for="item in workItemLogNameList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-col>
                </el-row>
                <el-row v-if="this.addFormDataSelType == '从聚水潭工作量报表取值'">
                    <el-col>
                        <el-select v-model="addFormDataSel" placeholder="" :clearable="true" filterable multiple
                            multiple-limit="1" style="width:80%;">
                            <el-option v-for="item in workItemJstWorkList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-col>
                </el-row>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisibleAdd_Sel = false">取 消</el-button>
                    <el-button type="primary" @click="onSelOk()">确 定</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="导入" :visible.sync="dialogVisibleUpload2" width="30%" v-dialogDrag
            :close-on-click-modal="false">
            <span>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :file-list="fileList2" :data="fileparm2" :http-request="onUploadFile2"
                            :on-success="onUploadSuccess2" :on-change="onUploadChange2" :on-remove="onUploadRemove2">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading2"
                                @click="onSubmitUpload2">{{ (uploadLoading2 ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleUpload2 = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog :title="dialogVisibleComputeTitle" :visible.sync="dialogVisibleCompute2" width="20%" v-dialogDrag
            :close-on-click-modal="false">
            <span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-date-picker style="width: 280px" v-model="computeWorkDate2" type="daterange"
                            format="yyyy-MM-dd" :picker-options="pickOptions" value-format="yyyy-MM-dd"
                            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
                        </el-date-picker>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <my-confirm-button type="submit" :loading="copmuteLoading2" @click="onComputePostSave" /> &nbsp;
                <el-button @click="dialogVisibleCompute2 = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog :title="dialogVisibleUploadTitle3" :visible.sync="dialogVisibleUpload3" width="30%" v-dialogDrag
            :close-on-click-modal="false">
            <span>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-upload ref="upload3" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :file-list="fileList3" :data="fileparm3" :http-request="onUploadFile3"
                            :on-success="onUploadSuccess3" :on-change="onUploadChange3" :on-remove="onUploadRemove3">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading3"
                                @click="onSubmitUpload3">{{ (uploadLoading3 ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleUpload3 = false">关闭</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import {
    getWarehousePostWagesPageList, getWarehousePostWorkItemColList,
    saveWarehousePostWages, getWarehousePostWagesById,
    deleteWarehousePostWages, importWarehouseWagesWorkItem, computeWarehouseUserWorkData,
    importWarehousePostWagesWeight, importWarehousePostWagesNum
} from '@/api/profit/warehousewages';
import { NullLogger } from '@microsoft/signalr';
const tableCols = [
    { istrue: true, prop: 'warehouseName', label: '仓库', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'postCode', label: '一级岗位编码', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'postName', label: '一级岗位名称', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'workItem', label: '二级岗位名称', width: '180', sortable: 'custom' },
    { istrue: true, prop: 'defaultWages', label: '白班最新工价', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'defaultWages2', label: '晚班最新工价', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'getValueType', label: '取值类型', width: '170', sortable: 'custom' },
    { istrue: true, prop: 'jstWorkGetValueCols', label: '工作量取值列名称', width: '170', sortable: 'custom', formatter: (row) => row.jstWorkGetValueColNames },
    { istrue: true, prop: 'modifiedTime', label: '最新操作日期', width: '150', sortable: 'custom' },
    //{ istrue: true, prop: 'orderLogGetValueCols', label: '订单日志取值名称', width: '180', sortable: 'custom', formatter: (row) => row.orderLogGetValueColNames },
    //{ istrue: true, prop: 'orderLogGetValueType', label: '计件类型', width: '90', sortable: 'custom' },
    //{ istrue: true, prop: 'remark', label: '备注', width: '200', sortable: 'custom' },
    {
        istrue: true, type: 'button', label: '操作', width: '200', tipmesg: "计算：计算常规计件单个岗位",
        btnList: [
            { label: "编辑", handle: (that, row) => that.onEdit(row.id) },
            { label: "计算", handle: (that, row) => that.onComputePost(row) },
            { label: "删除", handle: (that, row) => that.onDelete(row.id) },
            { label: "导入重量明细", handle: (that, row) => that.onImport3("导入重量明细", row.id), ishide: (that, row) => row.jstWorkGetValueColNames != "打包内重量" && row.jstWorkGetValueColNames != "出库验货内重量" },
            { label: "导入数量明细", handle: (that, row) => that.onImport3("导入数量明细", row.id), ishide: (that, row) => row.jstWorkGetValueColNames != "拣货订单商品种类数数量" }
        ]
    },
]
const tableHandles1 = [
    //{ label: "导入", handle: (that) => that.onImport2() },
];
export default {
    name: 'warehouseuserworkdata',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow },
    props: ['myWarehouseList', 'myOnePostNameList'],
    data() {
        return {
            that: this,
            nowDate: formatTime(new Date(), "YYYY-MM-DD"),
            filter: {
                dataType: null,
                warehouseCode: 10361546,//诚信仓
                isImportDtl: false,
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "createdTime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            tableHandles1: tableHandles1,
            dialogVisibleAdd: false,
            addFromLoading: false,
            addLoading: false,
            addFormData: {
                id: 0,
                warehouseCode: null,
                warehouseName: null,
                postCode: null,
                postName: null,
                workItem: null,
                defaultWages: 0,
                defaultWages2: 0,
                getValueType: "从聚水潭工作量报表取值",
                orderLogGetValueCols: null,
                orderLogGetValueColNames: null,
                orderLogGetValueType: null,
                jstWorkGetValueCols: null,
                jstWorkGetValueColNames: null,
                postDateList: [],
                postUserList: [],
            },
            addFormRules: {
                warehouseCode: [{ required: true, message: '请输入仓库', trigger: 'blur' }],
                //warehouseName: [{ required: true, message: '请输入仓库', trigger: 'blur' }],
                postCode: [{ required: true, message: '请输入一级岗位编码', trigger: 'blur' }],
                postName: [{ required: true, message: '请输入一级岗位名称', trigger: 'blur' }],
                workItem: [{ required: true, message: '请输入二级岗位名称', trigger: 'blur' }],
                getValueType: [{ required: true, message: '请输入取值类型', trigger: 'blur' }],
                outWarehouseFeeType: [{ required: true, message: '请输入出仓费计算类型', trigger: 'blur' }],
                outWarehouseFeeOrderOrGood: [{ required: true, message: '请输入出仓费计算方式', trigger: 'blur' }],
            },
            addFormTabsValue: "tab1",

            workItemLogNameList: [],
            workItemJstWorkList: [],
            addFormDataSel: [],
            addFormDataSelType: [],
            dialogVisibleAdd_Sel: false,

            dialogVisibleUpload2: false,
            fileList2: [],
            fileparm2: {},
            uploadLoading2: false,

            dialogVisibleComputeTitle: "",
            dialogVisibleCompute2: false,
            copmuteLoading2: false,
            computeWorkDate2: [],
            computeParams: {
                bWorkDate: null,
                eWorkDate: null,
            },

            dialogVisibleUpload3: false,
            dialogVisibleUploadTitle3: "",
            dialogVisibleUploadPostId3: 0,
            fileList3: [],
            fileparm3: {},
            uploadLoading3: false,

            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
        };
    },
    async mounted() {
        await this.getWorkItemList();
        await this.onSearch()
    },
    methods: {
        async onSel(t) {
            this.addFormDataSel = [];
            this.addFormDataSelType = t;
            this.dialogVisibleAdd_Sel = true;
        },
        async onSelOk() {
            if (!this.addFormDataSel || this.addFormDataSel.length <= 0) {
                this.addFormDataSel = [];
            }
            if (this.addFormDataSelType == '从订单日志取值') {
                this.addFormData.orderLogGetValueCols = "";
                this.addFormData.orderLogGetValueColNames = "";
                if (this.addFormDataSel.length > 0) {
                    let tIndex = 0;
                    this.addFormDataSel.forEach(f => {
                        tIndex++;
                        this.addFormData.orderLogGetValueCols += (f + ((tIndex < this.addFormDataSel.length) ? "," : ""));
                        this.addFormData.orderLogGetValueColNames += (f + ((tIndex < this.addFormDataSel.length) ? "," : ""));
                    });
                }
            }
            else if (this.addFormDataSelType == '从聚水潭工作量报表取值') {
                this.addFormData.jstWorkGetValueCols = "";
                this.addFormData.jstWorkGetValueColNames = "";
                if (this.addFormDataSel.length > 0) {
                    let tIndex = 0;
                    this.addFormDataSel.forEach(f => {
                        tIndex++;
                        let def = this.workItemJstWorkList.find(w => w.value == f);
                        if (def) {
                            this.addFormData.jstWorkGetValueCols += (def.value + ((tIndex < this.addFormDataSel.length) ? "," : ""));
                            this.addFormData.jstWorkGetValueColNames += (def.label + ((tIndex < this.addFormDataSel.length) ? "," : ""));
                        }
                    });
                }
            }
            this.dialogVisibleAdd_Sel = false;
        },
        async onClearAddForm() {
            this.addFormData.id = 0;
            this.addFormData.warehouseCode = null;
            this.addFormData.warehouseName = null;
            this.addFormData.postCode = null;
            this.addFormData.postName = null;
            this.addFormData.workItem = null;
            this.addFormData.defaultWages = 0;
            this.addFormData.defaultWages2 = 0;
            this.addFormData.getValueType = "从聚水潭工作量报表取值";
            this.addFormData.orderLogGetValueCols = null;
            this.addFormData.orderLogGetValueColNames = null;
            this.addFormData.orderLogGetValueType = null;
            this.addFormData.jstWorkGetValueCols = null;
            this.addFormData.jstWorkGetValueColNames = null;
            this.addFormData.postDateList = [];
            this.addFormData.postUserList = [];
        },
        async getWorkItemList() {
            const data = await getWarehousePostWorkItemColList();
            this.workItemLogNameList = [];
            this.workItemJstWorkList = [];
            if (data) {
                data.forEach(f => {
                    if (f.workItemColType == "从订单日志取值")
                        this.workItemLogNameList.push({ value: f.workItemColName, label: f.workItemColDes });
                    if (f.workItemColType == "从聚水潭工作量报表取值")
                        this.workItemJstWorkList.push({ value: f.workItemColName, label: f.workItemColDes });
                });
            }
        },
        //查询第一页
        async onSearch() {
            console.log(this.filter)
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getWarehousePostWagesPageList(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            //this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
        },
        //排序查 询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            console.log(rows)
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onAdd() {
            await this.onClearAddForm();
            this.dialogVisibleAdd = true;
        },
        async addRowDate() {
            this.addFormData.postDateList.push({
                id: 0,
                parentId: 0,
                newWagesBeginDate: this.nowDate,
                newWages: 0,
                newWages2: 0,
            });
        },
        async addRowUser() {
            this.addFormData.postUserList.push({
                id: 0,
                parentId: 0,
                userWagesBeginDate: this.nowDate,
                userWages: 0,
                userWages2: 0,
            });
        },
        addFormValidate: function () {
            let isValid = false;
            this.$refs.addForm.validate(valid => {
                isValid = valid;
            });
            return isValid;
        },
        async onAddSave() {
            if (!this.addFormData.postDateList || this.addFormData.postDateList.length <= 0) {
                this.$message({ type: 'error', message: '请新增至少一行工作项工价!' });
                return;
            }
            let chkError = false;
            this.addFormData.postDateList.forEach(f => {
                console.log(f.newWagesBeginDate);
                console.log(f.newWages);
                console.log(f.newWages2);
                if (!f.newWagesBeginDate)
                    chkError = true;
                if (!f.newWages && !f.newWages2)
                    chkError = true;
            });
            if (chkError) {
                this.$message({ type: 'error', message: '工作项工价中的【日期、工价】必填!' });
                return;
            }
            if (this.addFormData.postUserList && this.addFormData.postUserList.length > 0) {
                this.addFormData.postUserList.forEach(f => {
                    if (!f.userName)
                        chkError = true;
                    if (!f.userWagesBeginDate)
                        chkError = true;
                    if (!f.userWages || !f.userWages2)
                        chkError = true;
                });
                if (chkError) {
                    this.$message({ type: 'error', message: '个人工价中【姓名、日期、工价】必填!' });
                    return;
                }
            }
            this.addLoading = true;
            let dto = {
                warehousePostWages: {
                    id: this.addFormData.id,
                    warehouseCode: this.addFormData.warehouseCode,
                    warehouseName: this.myWarehouseList.find(f => f.value == this.addFormData.warehouseCode)?.label,
                    postCode: this.addFormData.postCode,
                    postName: this.addFormData.postName,
                    workItem: this.addFormData.workItem,
                    defaultWages: this.addFormData.defaultWages,
                    defaultWages2: this.addFormData.defaultWages2,
                    getValueType: this.addFormData.getValueType,
                    orderLogGetValueCols: this.addFormData.orderLogGetValueCols,
                    orderLogGetValueColNames: this.addFormData.orderLogGetValueColNames,
                    orderLogGetValueType: this.addFormData.orderLogGetValueType,
                    jstWorkGetValueCols: this.addFormData.jstWorkGetValueCols,
                    jstWorkGetValueColNames: this.addFormData.jstWorkGetValueColNames,
                    outWarehouseFeeType: this.addFormData.outWarehouseFeeType,
                    outWarehouseFeeOrderOrGood: this.addFormData.outWarehouseFeeOrderOrGood,
                },
                warehousePostDateWagesList: this.addFormData.postDateList,
                warehousePostUserWagesList: this.addFormData.postUserList
            };
            console.log(dto)
            let res = await saveWarehousePostWages(dto);
            this.addLoading = false;
            if (res.success) {
                this.$message({ type: 'success', message: '保存成功!' });
                this.dialogVisibleAdd = false;
                this.onSearch();
            }
        },
        async onEdit(mainId) {
            this.dialogVisibleAdd = true;
            this.addFromLoading = true;
            let res = await getWarehousePostWagesById({ id: mainId });
            this.addFromLoading = false;
            if (res?.success && res?.data) {
                this.addFormData = res.data.warehousePostWages;
                this.addFormData.postDateList = res.data.warehousePostDateWagesList;
                this.addFormData.postUserList = res.data.warehousePostUserWagesList;
            }
            console.log(this.addFormData);
        },
        async onDelete(mainId) {
            this.$confirm('确定要执行删除吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await deleteWarehousePostWages({ id: mainId });
                if (res?.success) {
                    this.$message({ type: 'success', message: '删除成功!' });
                    this.onSearch()
                }
            }).catch(() => {
            });
        },
        async onComputePost(row) {
            if (!row || !row.id) {
                this.$message({ type: 'error', message: '获取岗位失败' });
                return;
            }
            this.dialogVisibleComputeTitle = "计算[" + row.postName + "-" + row.workItem + "]人效";
            this.computeParams.postId = row.id;
            this.dialogVisibleCompute2 = true;
        },
        async onComputePostSave() {
            console.log(this.computeParams, "computeParams");
            if (!this.computeWorkDate2 || this.computeWorkDate2.length <= 0) {
                this.$message({ type: 'error', message: '请选择要计算的日期!' });
                return;
            }
            else {
                if (!this.computeParams || !this.computeParams.postId) {
                    this.$message({ type: 'error', message: '获取岗位失败' });
                    return;
                }
                this.computeParams.bWorkDate = formatTime(this.computeWorkDate2[0], "YYYY-MM-DD");
                this.computeParams.eWorkDate = formatTime(this.computeWorkDate2[1], "YYYY-MM-DD");
                console.log(this.computeParams, "computeParams");
                this.copmuteLoading2 = true;
                const res = await computeWarehouseUserWorkData(this.computeParams);
                this.copmuteLoading2 = false;
                if (res?.success) {
                    this.$message({ type: 'success', message: '计算个人人效中..请稍后在[人效统计]页签刷新查看!' });
                    this.dialogVisibleCompute2 = false;
                }
            }
        },
        async onImport2() {
            this.dialogVisibleUpload2 = true;
        },
        async onUploadFile2(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading2 = true
            const form = new FormData();
            form.append("upfile", item.file);
            var res = await importWarehouseWagesWorkItem(form);
            if (res?.success)
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            this.uploadLoading2 = false
        },
        onUploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList2 = [];
            this.dialogVisibleUpload2 = false;
        },
        async onUploadChange2(file, fileList) {
            this.fileList2 = fileList;
        },
        onUploadRemove2(file, fileList) {
            this.fileList2 = [];
        },
        onSubmitUpload2() {
            if (this.fileList2.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload2.submit();
        },

        async onImport3(mytitle, postId) {
            this.dialogVisibleUploadPostId3 = postId;
            this.dialogVisibleUploadTitle3 = mytitle;
            this.dialogVisibleUpload3 = true;
        },
        async onUploadFile3(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading3 = true
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("postid", this.dialogVisibleUploadPostId3);
            console.log(this.dialogVisibleUploadPostId3);
            var res = null;
            if (this.dialogVisibleUploadTitle3 == "导入重量明细") {
                res = await importWarehousePostWagesWeight(form);
            }
            else if (this.dialogVisibleUploadTitle3 == "导入数量明细") {
                res = await importWarehousePostWagesNum(form);
            }
            if (res?.success)
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            this.uploadLoading3 = false;
        },
        onUploadSuccess3(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList3 = [];
            this.dialogVisibleUpload3 = false;
        },
        async onUploadChange3(file, fileList) {
            this.fileList3 = fileList;
        },
        onUploadRemove3(file, fileList) {
            this.fileList3 = [];
        },
        onSubmitUpload3() {
            if (this.fileList3.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload3.submit();
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep .el-table__body-wrapper {
    overflow-y: auto;
}
</style>
