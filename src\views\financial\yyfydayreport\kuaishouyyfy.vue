<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <el-tabs style="height:94%;" v-model="activeName">

    <el-tab-pane   label="推广流水" name="tab1" style="height: 100%;">
        <kuaishouadflow :tablekey="tablekey3" ref="adflow" style="height: 100%;"/>
    </el-tab-pane>

     <el-tab-pane    label="标准推广" name="tab2" style="height: 100%;" lazy>
        <kuaishouadcost :tablekey="tablekey1" ref="adcost" style="height: 100%;"/>
    </el-tab-pane>
    <el-tab-pane    label="推广报表" name="tab3" style="height: 100%;" lazy>
        <kuaishouadflowreport :tablekey="tablekey2" ref="adflowreport" style="height: 100%;"/>
    </el-tab-pane>
    <el-tab-pane label="全站推广" name="tab4" style="height: 100%;" lazy>
        <kuaishoufullSitePromotion :tablekey="'kuaishoufullSitePromotion202505071515'" ref="fullSitePromotion" style="height: 100%;"/>
    </el-tab-pane>

  </el-tabs>
  </my-container >

 </template>
<script>
import MyContainer from "@/components/my-container";
import accountresult from '@/views/financial/yyfydayreport/accountresult'
import unusual from '@/views/financial/yyfydayreport/unusual'
import kuaishouadflow from '@/views/financial/yyfydayreport/kuaishouadflow'
import kuaishouadcost from '@/views/financial/yyfydayreport/kuaishouadcost'
import kuaishouadflowreport from '@/views/financial/yyfydayreport/kuaishouadflowreport'
import kuaishoufullSitePromotion from '@/views/financial/yyfydayreport/kuaishoufullSitePromotion'

export default {
  name: "Users",
  components: {MyContainer,accountresult,unusual,kuaishouadflow,kuaishouadcost,kuaishouadflowreport,kuaishoufullSitePromotion},
  data() {
    return {
      that:this,
      activeName:'tab1',
      filter: {
      },
      shopList:[],
      userList:[],
      groupList:[],
      selids:[],
      dialogVisibleSyj:false,
      pageLoading:false,
      fileList:[],
      tablekey1:"tablekey1",
      tablekey2:"tablekey2",
      tablekey3:"tablekey3"
    };
  },
  mounted() {




  },
  methods: {
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
