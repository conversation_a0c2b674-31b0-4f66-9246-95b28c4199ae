<template>
  <my-container v-loading="pageLoading">
    <template #header> 
        <!-- <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent size="mini">
          <el-form-item label="销量取值期间:">
          <el-select v-model="filter.days" placeholder="请选择周转天数" style="width: 70px">
            <el-option label="1天" value="1"></el-option>
            <el-option label="3天" value="3"></el-option>
            <el-option label="7天" value="7"></el-option>
            <el-option label="15天" value="15"></el-option>
            <el-option label="30天" value="30"></el-option>
            <el-option label="45天" value="45"></el-option>
          </el-select>
        </el-form-item>
        </el-form>      -->
    </template>
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarryShow"
        :tableData='list'    :tableCols='tableCols' :isSelection="false" :tablefixed="true"
        :tableHandles='tableHandles' :isSelectColumn="false" @select="selsChange"
        :loading="listLoading" >
      </ces-table>  
    <template #footer>
      <my-pagination
          ref="pager"
          :total="total"
          :checked-count="sels.length"
          @get-page="getlist"
      />
    </template>

    <el-drawer title="耗材费用" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editparmVisible"
                direction="btt" size="'auto'" class="el-drawer__wrapper"  style="position:absolute;">
            <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
            <div class="drawer-footer">
                <el-button @click.native="editparmVisible = false">取消</el-button>
                <my-confirm-button type="submit"  :loading="editparmLoading" @click="onSetEditParm" />
            </div>
        </el-drawer>

  </my-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatmoney} from "@/utils/tools";
import { 
  pageSeriesGoods
} from "@/api/order/procodesimilarity"
import {  batchAddOrUpdatePack, getProductPackCostBy, getListByStyleCodeCost} from '@/api/operatemanage/base/product'

//格式化money列：大于1 时，去掉小数点，小于1时保留小数点
var myformatmoney=function(value){
  return formatmoney(Math.abs(value)>1?Math.round(value):Math.round(value,1));
};

const tableCols=[
    {istrue:true,prop:'seriesCode',label:'系列编码', width:'130',sortable:'custom'},
    {istrue:true,prop:'goodsCode',label:'商品编码', width:'130',sortable:'custom'},
    {istrue:true,prop:'goodsName',label:'商品名称', width:'230',sortable:'custom'},
    {istrue:true,prop:'createdTime',label:'创建时间', width:'100',sortable:'custom'},
    {istrue:true,prop:'costPrice',label:'成本', width:'130',sortable:'custom'},
    {istrue:true,prop:'packCost',label:'耗材费用', width:'130',formatter:(row) => !row.packCost? "0" : row.packCost,type:'click',handle:(that,row)=>that.showDetail(row)},
    {istrue:true,prop:'goodsImage',label:'图片', width:'60',sortable:'custom',type:'imageGoodsCode',goods:{code:'goodsCode',name:'goodsName'}},
    {istrue:true,prop:'invAmount',label:'编码库存资金', width:'120',sortable:'custom',formatter:(row)=>myformatmoney(row.invAmount)},
    {istrue:true,prop:'invAmountPredict',label:'编码库存资金预估', width:'140',sortable:'custom',formatter:(row)=>myformatmoney(row.invAmountPredict)},
    {istrue:true,prop:'orderCount',label:'订单量', width:'120',formatter:(row)=> !row.orderCount ? " " : row.orderCount},
    {istrue:true,prop:'wcOrderRate',label:'外仓率', width:'140',formatter:(row)=>!row.wcOrderRate ? " " :(row.wcOrderRate * 100).toFixed(2) + '%' },
];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
        that:this,
        list: [],
        summaryarry:{},
        pager:{OrderBy:"invAmount",IsAsc:false},
        tableCols:tableCols,  
        tableHandles:[],      
        total: 0,
        sels: [], 
        selids: [],
        isShowColumn: false,
        listLoading: false,
        pageLoading:false,
        editparmVisible:false,
        editparmLoading:false,
        visible:false, 
        autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[{type:'hidden',field:'ids',title:'ids',value: '',col:{span:12}},
                     {type:'input',field:'goodsCode',title:'商品编码',validate: [{type: 'string', required: true, message:'请输入商品编码'}]},
                     {type:'select',field:'packCost1',title:'包装费', validate: [{type: 'string', required: true, message:'请选择包装费'}],value: "",options: [],
                                                props:{filterable:true,allowCreate:false,clearable:true,remote:true,remoteMethod:(parm) => this.remoteSearchStyleCode(parm)}
                                                },
                    //  {type:'InputNumber',field:'packCost',title:'耗材费用',value: null,props:{min:0,precision:3,step:0.1},col:{span:6}},
                    //  {type: "DatePicker",field: "date",title: "具体日期(二选一)", props: {type: "date",format: "yyyy-MM-dd",placeholder:"请选择时间",},},
                    //  {type: "DatePicker",field: "beginDate",title: "开始日期(二选一)", props: {type: "date",format: "yyyy-MM-dd",placeholder:"请选择时间",},},
                    //  {type:'DatePicker',field:'endDate',title:'结束日期(二选一)',props: {type: "date",format: "yyyy-MM-dd",placeholder:"请选择时间",},}
                    ]
          },      
    }
  },
  props:{
    filter:{
        seriesCode:null,
        goodsCode:null,
        // days:null
    },
  },
  async mounted() {
    await this.getlist();       
  },
  methods: {
    clearFilter(){
      this.filter={
          seriesCode:null,
          goodsCode:null
      };
    },
    async remoteSearchStyleCode(parm){
        if(!parm){
        //this.$message({ message: this.$t('api.sync'),type: 'warn'})
        return;
        }
        var options=[];
        const res = await getListByStyleCodeCost({currentPage:1,pageSize:50, styleCode: parm})
        res?.data?.forEach(f=>{
        options.push({value:f.cost + ',' + f.goodsCode,label:f.goodsName+ ' — ' + f.cost})
        })
        this.autoform.fApi.getRule('packCost1').options= options;
    },
    selsChange: function(sels) {
      this.sels = sels;
    },
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else{
        var orderBy =column.prop;
        this.pager={OrderBy:orderBy,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    },
    //获取查询条件
    getCondition(){
      var pager = this.$refs.pager.getPager();
      var page  = this.pager;
      const params = {
        ...pager,
        ...page,
        ...this.filter
      }

      return params;
    },
    //查询第一页
    async onSearch() {      
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    //分页查询
    async getlist() {
      var params=this.getCondition();
      params.styleCodeStartDate = params.styleCodeTimerange[0]
      params.styleCodeEndDate = params.styleCodeTimerange[1]
      if(params===false){
            return;
      }
      this.listLoading = true;
      var res = await pageSeriesGoods(params);
          
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      this.summaryarry = res.data.summary;
      const data = res.data.list;
      data.forEach(d => {
        d._loading = false
      })
      this.list = data;     
    },
    async showDetail(row){
      this.editparmVisible = true
        var data = await getProductPackCostBy({goodsCode: row.goodsCode})
        var model = {ids: row.goodsCode, goodsCode : row.goodsCode, packCost1: !data.data? 0 : data.data.cost, startDate: '', endDate: ''}
        this.$nextTick(async () =>{
            var arr = Object.keys(this.autoform.fApi)
            if(arr.length > 0)
            await this.autoform.fApi.resetFields()
            await this.autoform.fApi.disabled(true, 'goodsCode')
            await this.autoform.fApi.setValue(model)
        })
    },
    async onSetEditParm(){
            this.editparmLoading=true;
            this.$nextTick(() => {
            this.autoform.fApi.validate(async (valid, fail) => {
            if(valid){
                const formData = this.autoform.fApi.formData();
                formData.styleCode = this.filter.seriesCode
                await batchAddOrUpdatePack(formData);
                this.editparmVisible=false;
                this.getlist();
                }
                else{
                //todo 表单验证未通过
                }
            })
            this.editparmLoading=false;
        });
      }, 
  },
  computed:{
    summaryarryShow(){
      var sum ={};
      if(this.summaryarry){
        for (const key in this.summaryarry) {
          sum[key]=myformatmoney(this.summaryarry[key])
        }
      }
      return sum;
    },  
  },
}
</script>
<style scoped>
  ::v-deep .el-link.el-link--primary{
    margin-right: 7px;
  }

  ::v-deep .el-table__fixed 
  {
      pointer-events:auto;
  } 
</style>
