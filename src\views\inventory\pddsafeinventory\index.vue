<template>
  <container>
    <template #header>
      <div style="display:flex;flex-direction: column;">
        <el-button-group>
          <el-button style="margin-top:0px">
            <el-date-picker style="width: 410px" v-model="filter.timerange" @change="changeDate()" type="daterange"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" clearable></el-date-picker>
            <el-input style="margin-left: 10px;width: 150px;" v-model="filter.productShortName"
              v-model.trim="filter.goodsCode" :maxlength=100 placeholder="商品编码" @keyup.enter.native="onSearch"
              clearable />
            <el-button type="primary" @click="onSearch()" style="margin-left: 10px;">查询</el-button>
          </el-button>
        </el-button-group>
        <el-button-group style="margin-top:3px">
          <el-radio-group v-model="filter.isHistory" size="small" style="margin-left:5px;" @change="onSearch();">
            <el-radio-button label="0">停发</el-radio-button>
            <el-radio-button label="1">恢复</el-radio-button>
          </el-radio-group>
        </el-button-group>
      </div>
      <div style="height:708px;margin-top: 5px;">
        <ces-table :tablekey="tablekey" ref="table" :showsummary='true' :summaryarry='summaryarry' :that='that'
          :isIndex='true' :hasexpand='false' :isSelectColumn="false" :tableData='list' :tableCols='tableCols'
          :tableHandles='tableHandles' @sortchange="sortchange" :loading="listLoading">
        </ces-table>
      </div>

      <el-dialog title="失败明细" :visible.sync="dialog.visible" width="70%" v-dialogDrag>
        <div>
          <span>
            <errorDetail style="height: 600px;" ref="errorDetail" :filter="dialog.filter" v-if="dialog.visible">
            </errorDetail>
          </span>
        </div>
      </el-dialog>

    </template>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>
  </container>
</template>
<script>
import { pagePddSafeInventoryAsync } from '@/api/inventory/pddSafeInventorys'
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import { Loading } from 'element-ui';
import errorDetail from '@/views/inventory/pddsafeinventory/purchasedetail'

let loading;
const startLoading = () => {
  loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};

const tableCols = [
  { istrue: true, prop: 'goodsCode', sortable: 'custom', label: '商品编码', width: '200' },
  { istrue: true, prop: 'images', label: '图片', type: 'image', width: '100', align: 'center' },
  { istrue: true, prop: 'goodsName', label: '商品名称' },
  { istrue: true, prop: 'oneDayZZRate', sortable: 'custom', label: '1天周转天数', width: '150', formatter: (row) => row.isHistory == 0 ? row.oneDayZZRate : "-" },
  { istrue: true, prop: 'sealStatus', label: '状态', width: '200', type: 'click', style: (that, row) => that.renderenabledStatus(row), handle: (that, row) => that.showError(row) },
  { istrue: true, prop: 'createdTime', sortable: 'custom', label: '修改时间', width: '200', }
];
const tableHandles = [
];

export default {
  name: "Pddsafeinventory",
  components: { container, cesTable, errorDetail },
  data() {
    return {
      tablekey: null,
      that: this,
      dialog: {
        visible: false,
        filter: {
          goodsCode: null
        }
      },
      filter: {
        isHistory: 0,
        goodsCode: null,
        startTime: null,
        endTime: null,
        timerange: null
      },
      tableCols: tableCols,
      tableHandles: tableHandles,
      list: [],
      total: 0,
      pager: { OrderBy: "goodsCode", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      summaryarry: {},
      selids: [],
      pickerOptions: {
        shortcuts: [{
          text: '前一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate() - 1);
            const date2 = new Date(); date2.setDate(date2.getDate() - 1);
            picker.$emit('pick', [date1, date2]);
            window.setshowprogress(false);
          }
        }]
      }
    };
  },
  async mounted() {
    this.onSearch();
  },
  async created() {

  },
  methods: {
    async sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      await this.onSearch();
    },
    async onSearch() {
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      let pager = this.$refs.pager.getPager()
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      this.listLoading = true;
      let params = { ...pager, ...this.pager, ... this.filter }
      let res = await pagePddSafeInventoryAsync(params);
      this.listLoading = false;
      this.list = res.data?.list;
      this.total = res.data?.total;
    },
    //字体颜色
    renderenabledStatus(row) {
      if (row.isHistory == 0) {
        if (row.errorCount == 0) {
          return "color:blue;cursor:pointer;";
        } else return "color:red;cursor:pointer;";
      }
    },
    async showError(row) {
      if (row.isHistory == 0) {
        if (row.errorCount > 0) {
          this.dialog.visible = true;
          this.dialog.filter.goodsCode=row.goodsCode;
          // this.$nextTick(() => {
          //    $this.
          // });
        } else {
          window.open('https://mms.pinduoduo.com/login/');
        }
      }
    }
  }
}

</script>
<style></style>