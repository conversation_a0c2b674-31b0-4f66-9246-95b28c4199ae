<template>
  <div style="height:100%;padding:10px;overflow: auto;">
    <ces-table ref="table" :isIndex="true" :tableData.sync="list" :tableCols="tableCols" :tablefixed="true"
      :isSelectColumn="false" :loading="listLoading">
    </ces-table>
    <div style="width:100%;height:200px" ref='echarts'></div>
  </div>


</template>
<script>
import * as echarts from 'echarts'
import container from '@/components/my-container/nofooter'
import cesTable from "@/components/Table/table.vue";
import { GetCostPriceChgDetialInfoAsync } from "@/api/inventory/goodscostpricechg"


const tableCols = [
  { istrue: true, fixed: true, prop: 'goodsCode', label: '编码', sortable: true },
  { istrue: true, fixed: true, prop: 'newPurchaseDate', label: '变动时间', width: '200', sortable: true, formatter: (row) => row.newPurchaseDate },
  { istrue: true, fixed: true, prop: 'oldCostPrice', label: '原成本', width: '90', sortable: true, formatter: (row) => row.oldCostPrice },
  { istrue: true, fixed: true, prop: 'newCostPrice', label: '新成本', width: '150', sortable: true },
  { istrue: true, fixed: true, prop: 'diffPrice', label: '差价单价', width: '90', sortable: true },
  { istrue: true, fixed: true, prop: 'count', label: '采购量', width: '90', sortable: true },
  { istrue: true, fixed: true, prop: 'diffTotal', label: '差价总价', width: '90', sortable: true },
];

export default {
  name: 'goodsCostPriceChgDtl',
  components: { cesTable, container },
  props: {
    id: String
  },
  data() {
    return {
      filter: {
        timerange: null,
        startDate: null,
        endDate: null,
        proCode: null
      },
      period: 0,
      pageLoading: false,
      tableCols: tableCols,
      summaryarry: {},
      tableHandles: {},
      listLoading: false,
      list: [],
      chgData: {},
    };
  },

  async mounted() {
    await this.getlist();
  },
  methods: {
    getEcharts() {
      setTimeout(_ => {
        let myChart = echarts.init(this.$refs['echarts']);
        let series = []
        this.echartsLoading = true
        this.chgData.series.forEach(s => {
          if (s.name != '日期')
            series.push({ smooth: true, showSymbol: false, ...s })
        })
        this.echartsLoading = false
        myChart.setOption({
          legend: {
            show: true
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            },
            padding: [5, 10]
          },
          grid: {
            left: "0",
            top: "6",
            right: "0",
            bottom: "0",
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            //不显示x轴线
            show: true,
            data: this.chgData.xAxis
          },
          yAxis: {
            type: 'value',
            show: true,
          },
          series: series
        });
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      }, 1000)
    },
    async getlist() {
      let res = await GetCostPriceChgDetialInfoAsync({ id: this.id });
      this.list = res.data.list;
      this.chgData = res.data;
      this.getEcharts()

    },
    async onSearch(para) {
      this.filter.proCode = para.proCode
      this.filter.timerange = para.timerange
      await this.getanalysisdata()
    },
    async onfresh() {
      await this.getanalysisdata()
    },
    async getanalysisdata() {
      if (!this.filter.timerange || this.filter.timerange.length < 2) {
        this.$message({ message: "请先选择日期！", type: "warning", });
        return;
      }
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      let parm = { ...this.filter };
      parm.period = this.period;
      const res = await proCodeSimilarityAnalysis(parm);
      if (!res?.code) return;
      let chartDom = document.getElementById('echartprocodeanalysis1');
      let myChart = echarts.init(chartDom);
      myChart.clear();
      if (!res.data) {
        this.$message({ message: "没有数据!", type: "warning", });
        return;
      }
      let option = this.Getoptions(res.data);
      option && myChart.setOption(option);
    },
    Getoptions(element) {
      let series = []
      element.series.forEach(s => {
        series.push({ smooth: true, ...s })
      })
      let yAxis = []
      element.yAxis.forEach(s => {
        yAxis.push({ type: 'value', offset: s.offset, position: s.position, name: s.name, axisLabel: { formatter: '{value}' + s.unit } })
      })
      let option = {
        title: { text: element.title },
        tooltip: { trigger: 'axis' },
        legend: {
          data: element.legend
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            magicType: { show: true, type: ['line', 'bar'] },
            //restore: {show: true},
          }
        },
        xAxis: {
          type: 'category',
          data: element.xAxis
        },
        yAxis: yAxis,
        series: series
      };
      return option;
    },
  },
};
</script>

<style lang="scss" scoped>

</style>