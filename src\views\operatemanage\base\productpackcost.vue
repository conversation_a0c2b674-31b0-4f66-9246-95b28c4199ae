<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>      
                <!-- <el-form-item label="日期:">
                    <el-date-picker style="width:240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" 
                    start-placeholder="开始" end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch">
                    </el-date-picker>
                </el-form-item> -->
                <el-form-item label="系列编码:">
                <el-select v-model="styleCode" multiple filterable remote reserve-keyword placeholder="请输入关键词" clearable :remote-method="remoteMethod" :loading="searchloading" :collapse-tags="true" style="width:200px;" > 
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
                </el-form-item>
                <el-form-item label="商品编码:">
                    <el-input v-model="filter.goodsCode" @keyup.enter.native="onSearch" clearable/>
                </el-form-item>  
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>            
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true'  @sortchange='sortchange' :isSelection='true' @select='selectchange' @cellclick='cellclick' 
         :hasexpand='true' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" 
         :showsummary='true' :summaryarry='summaryarry'>
        </ces-table>
        <template #footer>
          <my-pagination
            ref="pager"
            :total="total"
            :checked-count="sels.length"
            @get-page="getlist"
          />
        </template>
        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%">
            <span>
                <el-row>
                    <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
                    <el-date-picker style="width: 100%" v-model="onimportfilter.yearmonthday" type="date" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
                </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                    <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action accept=".xlsx"
                    :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
                    </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-drawer title="耗材费用" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editparmVisible"
                direction="btt" size="'auto'" class="el-drawer__wrapper"  style="position:absolute;">
            <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
            <div class="drawer-footer">
                <el-button @click.native="editparmVisible = false">取消</el-button>
                <my-confirm-button type="submit"  :loading="editparmLoading" @click="onSetEditParm" />
            </div>
        </el-drawer>

        <!-- 系列编码趋势图 -->
        <el-dialog title="历史包装费" :visible.sync="packcostVisible" width="80%" v-dialogDrag>
            <span>
                <productpackcostdetail ref="productpackcostdetail" :filter="filter1" style="height: 500px"></productpackcostdetail>
            </span>
        </el-dialog>

    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { ImportProductPackCost, getProductPackCost, batchAddOrUpdatePackCost, getProductPackCostByCost, batchAddPackCost, getListByStyleCodeCost } from '@/api/operatemanage/base/product'
import { number } from 'echarts';
import { getListByStyleCode } from "@/api/inventory/basicgoods"
import productpackcostdetail from "./productpackcostdetail.vue"

const tableCols =[
        //{istrue:true,prop:'yearMonthDay',label:'日期', width:'120',sortable:'custom',formatter:(row)=> formatTime(row.yearMonthDay,"YYYY-MM-DD")},
        {istrue:true,prop:'styleCode',label:'系列编码', width:'150',sortable:'custom',formatter:(row)=> !row.styleCode? " " : row.styleCode},
        {istrue:true,prop:'goodsCode',label:'商品编码', width:'150',sortable:'custom',formatter:(row)=> !row.goodsCode?" " : row.goodsCode,type:'click',handle:(that,row)=>that.showDetail(row)}, 
        {istrue:true,prop:'goodsName',label:'商品名称', width:'380',formatter:(row)=> !row.goodsName? "" : row.goodsName},       
        //{istrue:true,prop:'costType',label:'分类', width:'80',formatter:(row)=> !row.costType? " " : row.costType},  
        {istrue:true,prop:'cost',label:'包装费', width:'100 ',sortable:'custom',formatter:(row)=> !row.cost? " " : row.cost},     
        {istrue:true,prop:'costPrice',label:'成本价', width:'auto',formatter:(row)=> !row.costPrice? " " : row.costPrice},     
]

const tableHandles=[
        //{label:"导入", handle:(that)=>that.startImport()},     
        {label:"新增耗材费用", handle:(that)=>that.onHand(3)},     
        {label:"单修耗材费用", handle:(that)=>that.onHand(1)},     
        {label:"批量修改耗材费用", handle:(that)=>that.onHand(2)},     
      ];

export default {
    name: 'YunhanAdminProductpackcost',
    components :{container, MyConfirmButton, cesTable, productpackcostdetail},
    data() {
        return {
            that:this,
            filter: {
                startDate:null,
                endDate:null,
                goodsCode:null,
                styleCode:null,
                timerange:[formatTime(dayjs().subtract(7,"day"), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")]
            },
            filter1: {
                goodsCode: null   
            },
            list: [],
            summaryarry:{},
            pager:{OrderBy:"",IsAsc:false},
            tableCols:tableCols,
            tableHandles:tableHandles,
            styleCode:null,
            total: 0,
            sels: [], 
            selids: [],
            selcodes: [],
            options: [],
            onHandNumber: null,
            onimportfilter:{
                yearmonthday:formatTime(dayjs().subtract(1,"day"), "YYYY-MM-DD"),
            },
            searchloading: false,
            listLoading: false,
            pageLoading: false,
            dialogVisible: false,
            uploadLoading: false,
            editparmVisible: false,
            editparmLoading: false,
            packcostVisible:false,
            pickerOptions:{
                disabledDate(time){
                return time.getTime()>Date.now();
               }
            },
            autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[{type:'hidden',field:'ids',title:'ids',value: '',col:{span:12}},
                     {type:'select',field:'styleCode',title:'系列编码', validate: [{type: 'string', required: true, message:'请选择系列编码'}],value: "",options: [],
                                                props:{filterable:true,allowCreate:false,clearable:true,remote:true,remoteMethod:(parm) => this.remoteSearchUser(parm)}
                                                },
                     {type:'input',field:'goodsCode',title:'商品编码',validate: [{type: 'string', required: true, message:'请输入商品编码'}]},
                    //  {type:'select',field:'packCost',title:'包装费', validate: [{type: 'number', required: true, message:'请选择包装费'}],value: "",options: [],
                    //                             props:{filterable:true,allowCreate:false,clearable:true,remote:true,remoteMethod:(parm) => this.remoteSearchStyleCode(parm)}
                    //                             },
                    {type:'InputNumber',field:'packCost',title:'耗材费用',value: null,props:{min:0,precision:3,step:0.1},col:{span:6}},
                    //  {type: "DatePicker",field: "date",title: "具体日期(二选一)", props: {type: "date",format: "yyyy-MM-dd",placeholder:"请选择时间",},},
                    //  {type: "DatePicker",field: "beginDate",title: "开始日期(二选一)", props: {type: "date",format: "yyyy-MM-dd",placeholder:"请选择时间",},},
                    //  {type:'DatePicker',field:'endDate',title:'结束日期(二选一)',props: {type: "date",format: "yyyy-MM-dd",placeholder:"请选择时间",},}
                    ]
          },
        };
    },

    async mounted() {
        await this.onSearch()
    },

    methods: {
        async remoteSearchUser(parm){
            if(!parm){
            //this.$message({ message: this.$t('api.sync'),type: 'warn'})
            return;
            }
            var options=[];
            const res = await getListByStyleCode({currentPage:1,pageSize:50, styleCode: parm})
            res?.data?.forEach(f=>{
            options.push({value:f.styleCode,label:f.styleCode})
            })
            this.autoform.fApi.getRule('styleCode').options= options;
        },
        async remoteMethod(query){
            if (query !== ''){
                this.searchloading == true
                setTimeout(async () => {
                    const res = await getListByStyleCode({currentPage:1,pageSize:50, styleCode: query})
                    this.searchloading = false
                    this.options=[];
                    res?.data?.forEach(f=>{
                    this.options.push({value:f.styleCode,label:f.styleCode})
                    });
                }, 200)
            }
            else{
                this.options = []
            }
        },
        async remoteSearchStyleCode(parm){
            if(!parm){
            //this.$message({ message: this.$t('api.sync'),type: 'warn'})
            return;
            }
            var options=[];
            const res = await getListByStyleCodeCost({currentPage:1,pageSize:50, styleCode: parm})
            res?.data?.forEach(f=>{
            options.push({value:f.cost,label:f.goodsName+ ' — ' + f.cost})
            })
            this.autoform.fApi.getRule('packCost').options= options;
        },
        startImport(){
            this.dialogVisible=true;
        },
        uploadSuccess(response, file, fileList) {
            if (response.code == 200) {
            } else {
                fileList.splice(fileList.indexOf(file), 1);
            }
            },
        async submitUpload() {
            if (!this.onimportfilter.yearmonthday) {
            this.$message({type: 'warning',message: '请选择年月!'});
            return;
            }    
        if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请先选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit=true;
            this.uploadLoading=true;
            this.$refs.upload.submit();
        },
        //导入
        async uploadFile(item) {
            if(!this.fileHasSubmit){
                return false;
            }
            this.fileHasSubmit=false;
            const form = new FormData();
            form.append("token", this.token);
            form.append("upfile", item.file);
            form.append("yearmonthday", this.onimportfilter.yearmonthday);
            const res =await ImportProductPackCost(form);
            if (res.code==1)   this.$message({ message: "上传成功,正在导入中...", type: "success" });
            else  this.$message({ message: res.msg, type: "warning" });
            this.uploadLoading=false;    
        },
        async uploadChange(file, fileList) {
            if (fileList && fileList.length > 0) {
            var list = [];
            for(var i=0;i<fileList.length;i++){
                if(fileList[i].status=="success")
                list.push(fileList[i]);
                else
                list.push(fileList[i].raw);
            }
            this.fileList = list;
            }
        },
        uploadRemove(file, fileList){
            this.uploadChange(file, fileList);
        },
        //查询第一页
        async onSearch() {
            if (!this.filter.timerange) {this.$message({message: "请选择日期",type: "warning",});return;}
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        async getlist(){
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            this.filter.styleCode = this.styleCode.join()
            var pager = this.$refs.pager.getPager();
            var params = {...this.pager, ...pager, ...this.filter}
            this.listLoading = true
            const res = await getProductPackCost(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            // this.summaryarry=res.data.summary;
            // if(this.summaryarry)
            //     this.summaryarry.amountPaid_sum=parseFloat(this.summaryarry.amountPaid_sum.toFixed(6));
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
            this.selids=[];
        },  
        async onHand(number){
            this.onHandNumber=number
            if ((number==1||number==2)&&this.selids.length == 0){
                this.$message.warning("请先选择");
                this.editparmVisible = false
                return
            }
            else if ((number==1)&&this.selids.length>1){
                this.$message.warning("只能选择1行");
                this.addFormVisible = false
                return
            }    
            if (number == 1){
                this.editparmVisible = true
                var data = await getProductPackCostByCost({goodsCode: this.selids.join()})
                var model = {ids: this.selids.join(), goodsCode : this.selids.length >0 ? this.selids.join() : '', packCost: !data.data.cost? '' : data.data.cost}
                this.$nextTick(async () =>{
                    var arr = Object.keys(this.autoform.fApi)
                    if(arr.length > 0)
                    await this.autoform.fApi.resetFields()
                    await this.autoform.fApi.hidden(false, 'date')
                    await this.autoform.fApi.hidden(false, 'beginDate')
                    await this.autoform.fApi.hidden(false, 'endDate')
                    await this.autoform.fApi.hidden(true, 'styleCode')
                    await this.autoform.fApi.hidden(false, 'goodsCode')
                    //await this.autoform.fApi.disabled(true, 'goodsCode')
                    await this.autoform.fApi.setValue(model)
                })
            }
            else if (number == 2){
                this.editparmVisible = true
                var model = {ids: this.selids.join(),packCost: ''}
                this.$nextTick(async () =>{
                    var arr = Object.keys(this.autoform.fApi)
                    if(arr.length > 0)
                    await this.autoform.fApi.resetFields()
                    await this.autoform.fApi.hidden(false, 'date')
                    await this.autoform.fApi.hidden(false, 'beginDate')
                    await this.autoform.fApi.hidden(false, 'endDate')
                    await this.autoform.fApi.hidden(true, 'goodsCode')
                    await this.autoform.fApi.hidden(true, 'styleCode')
                    await this.autoform.fApi.setValue(model)
                })
            }
            else if (number == 3){
                this.editparmVisible = true
                this.$nextTick(async () =>{
                    await this.autoform.fApi.resetFields()
                    await this.autoform.fApi.hidden(false, 'date')
                    await this.autoform.fApi.hidden(false, 'beginDate')
                    await this.autoform.fApi.hidden(false, 'endDate')
                    await this.autoform.fApi.hidden(false, 'styleCode')
                    await this.autoform.fApi.hidden(false, 'goodsCode')
                    //await this.autoform.fApi.disabled(false, 'goodsCode')
                })
            }
            
        },
        async onSetEditParm(){
            this.editparmLoading=true;
            this.$nextTick(() => {
            this.autoform.fApi.validate(async (valid, fail) => {
            if(valid){
                const formData = this.autoform.fApi.formData();

                //formData.id=formData.id?formData.id:0;
                //formData.Enabled=true;
                if (this.onHandNumber == 1 || this.onHandNumber == 2){
                    await batchAddOrUpdatePackCost(formData);
                }
                else if (this.onHandNumber == 3){
                    await batchAddPackCost(formData)
                }
                this.editparmVisible=false;
                this.getlist();
                }
                else{
                //todo 表单验证未通过
                }
            })
            this.editparmLoading=false;
        });
        },    
        async showDetail(row){
            this.packcostVisible = true
            this.filter1.goodsCode = row.goodsCode
            this.$nextTick(async ()=>{
                await this.$refs.productpackcostdetail.onSearch()
            })
            
        },
        //排序查询
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else{
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
            }
            await this.onSearch();
        },  
        selectchange:function(rows,row) {
            this.selids=[];
            this.selcodes=[];
            console.log(rows)
            rows.forEach(f=>{
                this.selids.push(f.goodsCode);
                this.selcodes.push(f.styleCode)
            })
        },
        cellclick(row, column, cell, event){
        
        },
    },
};
</script>

<style lang="scss" scoped>

</style>