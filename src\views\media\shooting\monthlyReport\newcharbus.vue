<template>
  <!-- <div style="padding-left:10px;overflow: auto;">
    <div :id="'buschar' + randrom" :style="thisStyle" />
  </div> -->
  <div style="height:100%;padding-left:10px;overflow: auto;min-height: 100px;" v-loading="loading">
    <div v-if="analysisData && analysisData.series && analysisData.series != null && analysisData.series.length > 0"
      :id="'buschar' + randrom" :style="thisStyle"></div>
    <!-- <div :id="'buschar' + randrom" :style="thisStyle" /> -->
    <div v-else>没有可供展示的图表数据！</div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
export default {
  name: 'buschar',
  components: {},
  props: {
    loading: {
      type: Boolean, default: function () {
        return false;
      }
    },
    action: { type: Function, default: null },
    parms: { type: Object, default: null },
    analysisData: { type: Object, default: null },
    end: { type: Boolean, default: false },

    toolbox: {
      type: Object, default: null

    },
    thisStyle: {
      type: Object,
      default: function () {
        return {
          width: '100%', height: '550px', 'box-sizing': 'border-box', 'line-height': '360px'
        }
      }
    },
    gridStyle: {
      type: Object, default: function () {
        return {
          top: '25%',
          left: '5%',
          right: '4%',
          bottom: '10%',
          containLabel: false
        }
      }
    },
    legendPistion: {
      type: Object,
    }
  },
  data() {
    return {
      that: this,
      randrom: "",
      period: 0,
      pageLoading: false,
      listLoading: false,
      procode: '',
      myChart: null,
    }
  },
  created() {
    var e = 10;
    var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
      a = t.length,
      n = "";
    for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
    this.randrom = n;
  },
  mounted() {
    this.initcharts();
    window.addEventListener('resize', this.handleResizeChart);
  },
  destroyed() {
    window.removeEventListener('resize', this.handleResizeChart);
  },
  methods: {
    //显示数值
    onHandleShow(showlable) {
      // var chartDom = document.getElementById('buschar' + this.randrom);
      //     this.myChart = echarts.init(chartDom);
      //     this.myChart.clear();
      var option = this.Getoptions(this.analysisData);
      if (showlable) {
        option.series.forEach(item => {
          item.label = {
            show: true,
            position: 'top',
            fontSize: 12,
            formatter: (params) => {
              if (params.seriesName.indexOf("%") > -1 || params.seriesName.indexOf("占比") > -1 || params.seriesName.indexOf("率") > -1) {
                return params.value + '%'
              }
            }
          }
        })
      } else {
        option.series.forEach(item => {
          item.label = {
            show: false,
          }
        })
      }
      option && this.myChart.setOption(option);
    },
    initcharts() {
      let that = this;
      this.$nextTick(() => {
        var chartDom = document.getElementById('buschar' + that.randrom);
        this.myChart = echarts.init(chartDom);
        this.myChart.clear();
        if (this.analysisData && this.analysisData.series) {
          var option = this.Getoptions(this.analysisData);
          option && this.myChart.setOption(option);
        }
        // if (this.action != null) {
        this.myChart.on('click', async params => {
          console.log(params);
          if (params.seriesType == "line") {
            await this.$emit('action', params);
          } else if (params.seriesType == "bar") {
            if (that.end) {
              return
            }
            var option = that.Getoptions(that.analysisData);
            await this.$emit('baraction', params);
          }
        })
        // }
      });
    },
    initchartsone(val) {
      let that = this;
      this.$nextTick(() => {
        var chartDom = document.getElementById('buschar' + that.randrom);
        this.myChart = echarts.init(chartDom);
        this.myChart.clear();
        if (val && val.series) {
          var option = this.Getoptions(val);
          option && this.myChart.setOption(option);
        }
      });
    },
    randomString() {
      var e = 10;
      var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
        a = t.length,
        n = "";
      for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
      return n
    },
    Getoptions(element) {
      var series = []

      element.series.forEach(s => {
        series.push({ smooth: true, ...s })
      })
      var yAxis = []
      if (Array.isArray(element.yAxis)) {
        element.yAxis.forEach(s => {
          yAxis.push({
            type: 'value', minInterval: 1, offset: s.offset, splitLine: s.splitLine, position: s.position, name: s.name, max: s.max, min: s.min, axisLabel: {
              formatter: function (value) {
                if (value >= 100000000) {
                  value = (value / 100000000).toFixed(1) + 'Y';
                }
                if (value >= 10000000) {
                  value = (value / 10000000).toFixed(1) + 'KW';
                }
                if (value >= 10000) {
                  value = (value / 10000).toFixed(1) + 'W';
                }
                if (value >= 1000) {
                  value = (value / 1000).toFixed(1) + 'K';
                }
                if (value <= -100000000) {
                  value = (value / 100000000).toFixed(1) + 'Y';
                }
                if (value <= -10000000) {
                  value = (value / 10000000).toFixed(1) + 'KW';
                }
                if (value <= -10000) {
                  value = (value / 10000).toFixed(1) + 'W';
                }
                if (value <= -1000) {
                  value = (value / 1000).toFixed(1) + 'K';
                }
                return value + s.unit;
              }
            }
          })
        })
      } else {
        yAxis = { ...element.yAxis };
      }


      var selectedLegend = {};//{};
      if (element.selectedLegend) {
        element.legend.forEach(f => {
          //if(!element.selectedLegend.includes(f)) selectedLegend["'"+f+"'"]=false
          if (!element.selectedLegend.includes(f)) selectedLegend[f] = false
        })
      }
      var option = {
        title: { text: element.title },
        tooltip: { trigger: 'axis' },
        color: element.color,
        legend: {
          selected: selectedLegend,
          data: element.legend,
          bottom: this.legendPistion?.bottom
        },
        grid: this.gridStyle,
        toolbox: this.toolbox,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        xAxis: {
          type: 'category',
          data: element.xAxis
        },
        graphic: element.graphic,
        yAxis: yAxis,
        series: series
      };
      return option;
    },
    handleResizeChart() {
      if (this.myChart) {
        this.myChart.resize();
      }
    }
  }
}
</script>

