<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.from" placeholder="类型" class="publicCss" clearable>
                    <el-option label="1688" value="1688" />
                    <el-option label="本地" value="本地" />
                </el-select>
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable>
                    <el-option label="启用" value="启用" />
                    <el-option label="禁用" value="禁用" />
                </el-select>
                <el-cascader class="publicCss" v-model="ListInfo.categoryName" :options="CategoryList"
                    :props="{ checkStrictly: true, value: 'categoryName', label: 'categoryName', expandTrigger: 'hover' }"
                    clearable placeholder="类目"></el-cascader>
                <el-select v-model="ListInfo.showDescription" placeholder="1688详情页" class="publicCss" clearable>
                    <el-option label="是" :value="1" />
                    <el-option label="否" :value="0" />
                </el-select>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.proCodes" v-model="ListInfo.proCodes"
                    placeholder="产品ID/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="proCodeCallback($event, '1')" title="产品ID"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes" border
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="proCodeCallback($event, '2')" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="otherOperation(1, '修改类目')"
                    v-if="checkPermission('otherOperationCategory')">修改类目</el-button>
                <el-button type="primary" @click="otherOperation(2, '设置仓位')"
                    v-if="checkPermission('otherOperationPositions')">设置仓位</el-button>
                <el-button type="primary" @click="otherOperation(3, '设置销量')"
                    v-if="checkPermission('otherOperationSales')">设置销量</el-button>
                <el-button type="primary" @click="otherOperation(4, '起拍量')"
                    v-if="checkPermission('otherOperationStartingVolume')">起拍量</el-button>
                <el-button type="primary" @click="otherOperation(5, '启用禁用')"
                    v-if="checkPermission('otherOperationEnable')">启用禁用</el-button>
                <el-button type="primary" @click="otherOperation(6, '设置价格')"
                    v-if="checkPermission('otherOperationPrice')">设置价格</el-button>
                <el-button type="primary" @click="otherOperation(7, '展示详情页')"
                    v-if="checkPermission('otherOperationDetail')">展示详情页</el-button>
            </div>
        </template>

        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            :treeProp="{ rowField: 'id', parentField: 'pId', transform: true, }" @sortchange='sortchange'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" :loading="loading" :height="'100%'" @select="selectCheckBox">
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog :title="otherOperationTitle" :visible.sync="otherOperationVisible" width="30%" v-dialogDrag>
            <!-- 修改类目 -->
            <div v-if="operationType == 1" class="publicDisplay">
                <el-cascader class="publicCss" v-model="formData.categoryName" :options="CategoryList"
                    placeHolder="请选择类目"
                    :props="{ checkStrictly: true, value: 'categoryName', label: 'categoryName', expandTrigger: 'hover' }"
                    clearable></el-cascader>
            </div>
            <!-- 设置仓位 -->
            <div v-if="operationType == 2" class="publicDisplay">
                <el-input v-model.trim="formData.warePosition" placeholder="请输入仓位" maxlength="50" clearable
                    class="publicCss" style="width: 150px;" />
            </div>
            <!-- 设置销量 -->
            <div v-if="operationType == 3" class="publicDisplay">
                <el-input-number v-model="formData.saleMultipleStart" :min="1" :max="100" placeholder="销量最小倍数"
                    :controls="false" :precision="2" style="width: 150px;margin-right: 5px;" />-
                <el-input-number v-model="formData.saleMultipleEnd" :min="1" :max="100" placeholder="销量最大倍数"
                    :controls="false" :precision="2" style="width: 150px;margin-left: 5px;" />
            </div>
            <!-- 起拍量 -->
            <div v-if="operationType == 4" class="publicDisplay">
                <el-input-number v-model="formData.startBuyCount" :min="1" :max="10000" placeholder="起拍量"
                    :controls="false" :precision="0" style="width: 150px;" />
            </div>
            <!-- 启用禁用 -->
            <div v-if="operationType == 5" class="publicDisplay">
                <el-select v-model="formData.status" placeholder="状态" class="publicCss" clearable>
                    <el-option label="启用" value="启用" />
                    <el-option label="禁用" value="禁用" />
                </el-select>
            </div>
            <!-- 设置价格 -->
            <div v-if="operationType == 6" class="publicDisplay">
                <el-form ref="form" :model="formData" label-width="120px">
                    <el-form-item label="成本价/零售价:">
                        <el-select v-model="formData.priceMultipleType" placeholder="请选择成本价/零售价" class="publicCss"
                            clearable>
                            <el-option label="零售" value="零售" />
                            <el-option label="成本" value="成本" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="包邮倍数:">
                        <el-input-number v-model="formData.byPriceMultipleStart" :min="1" :max="100"
                            placeholder="包邮最小倍数" :controls="false" :precision="2"
                            style="width: 150px;margin-right: 5px;" />-
                        <el-input-number v-model="formData.byPriceMultipleEnd" :min="1" :max="100" placeholder="包邮最大倍数"
                            :controls="false" :precision="2" style="width: 150px;margin-left: 5px;" />
                    </el-form-item>
                    <el-form-item label="不包邮倍数:">
                        <el-input-number v-model="formData.notBYPriceMultipleStart" :min="1" :max="100"
                            placeholder="不包邮最小倍数" :controls="false" :precision="2"
                            style="width: 150px;margin-right: 5px;" />-
                        <el-input-number v-model="formData.notBYPriceMultipleEnd" :min="1" :max="100"
                            placeholder="不包邮最大倍数" :controls="false" :precision="2"
                            style="width: 150px;margin-left: 5px;" />
                    </el-form-item>
                </el-form>
            </div>
            <!-- 展示详情页 -->
            <div v-if="operationType == 7" class="publicDisplay">
                <el-select v-model="formData.showDescription" placeholder="1688详情页" class="publicCss" clearable>
                    <el-option label="是" :value="1" />
                    <el-option label="否" :value="0" />
                </el-select>
            </div>
            <div class="btnGroup">
                <el-button @click="otherOperationVisible = false">取 消</el-button>
                <el-button type="primary" @click="operationSubmit">确 定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import { getChooseGoodsCenterProductSetList, getSetBusinessCategory, changeChooseGoodsCenterProductSet, validateWarePosition } from '@/api/openPlatform/ChooseGoodsCenter'
const tableCols = [
    { type: 'checkbox', label: '', },
    { sortable: 'custom', width: '70', align: 'center', prop: '[from]', label: '类型', treeNode: true, formatter: (row) => row.from },
    { sortable: 'custom', width: '70', align: 'center', prop: 'status', label: '状态', },
    { sortable: 'custom', width: '95', align: 'center', prop: 'proCode', label: '产品ID', },
    { sortable: 'custom', width: '70', align: 'center', prop: 'categoryName', label: '类目', },
    { sortable: 'custom', width: '70', align: 'center', prop: 'warePosition', label: '仓位', },
    { sortable: 'custom', width: '95', align: 'center', prop: 'subject', label: '产品标题', },
    { width: '95', align: 'center', prop: 'skuName', label: '标题名称', },
    { width: '95', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: '70', align: 'center', prop: 'saleCount', label: '销量', },
    { sortable: 'custom', width: '95', align: 'center', prop: 'saleMultiple', label: '销量倍数', },
    { sortable: 'custom', width: '95', align: 'center', prop: 'showSaleCount', label: '展示销量', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'startBuyCount', label: '起拍量', },
    { width: '80', align: 'center', prop: 'price', label: '零售价', },
    { width: '80', align: 'center', prop: 'costPrice', label: '成本价', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'priceMultipleType', label: '取值类型', },
    { sortable: 'custom', width: '95', align: 'center', prop: 'byPriceMultiple', label: '包邮倍数', },
    { width: '95', align: 'center', prop: 'byPrice', label: '包邮价格', },
    { sortable: 'custom', width: '95', align: 'center', prop: 'notBYPriceMultiple', label: '不包邮倍数', },
    { width: '100', align: 'center', prop: 'notByPrice', label: '不包邮价格', },
    {
        sortable: 'custom', width: 'auto', align: 'center', prop: 'showDescription', label: '是否展示1688详情页', formatter: (row) => {
            if (row.pId == 0) {
                return row.showDescription == 1 ? '是' : '否'
            } else {
                return ''
            }
        }
    },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, inputYunhan
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            selectList: [],
            otherOperationVisible: false,
            CategoryList: [],
            formData: {
                skuIds: [],//商品编码    
                proCodes: [],//产品ID
                saleMultipleStart: undefined,//销量开始倍数
                saleMultipleEnd: undefined,//销量结束倍数
                categoryName: null,//类目
                warePosition: null,//仓位
                startBuyCount: undefined,//起拍量
                status: null,//状态
                byPriceMultipleStart: undefined,//包邮价格倍数开始
                byPriceMultipleEnd: undefined,//包邮价格倍数结束
                notBYPriceMultipleStart: undefined,//不包邮价格倍数开始
                notBYPriceMultipleEnd: undefined,//不包邮价格倍数结束
                priceMultipleType: null,//价格倍数类型
                showDescription: null,//是否展示1688详情页
            },
            operationType: null,
            otherOperationTitle: null,
        }
    },
    async mounted() {
        this.getCategory()
        await this.getList()
    },
    methods: {
        async operationSubmit() {
            let ValidateWarePosition = null
            if (this.operationType == 1) {
                if (!this.formData.categoryName) return this.$message.error('请选择类目')
                this.formData.categoryName = this.formData.categoryName.join(',')
            } else if (this.operationType == 2) {
                if (!this.formData.warePosition) return this.$message.error('请输入仓位')
                const { data } = await validateWarePosition(this.formData)
                ValidateWarePosition = data
            } else if (this.operationType == 3) {
                if (!this.formData.saleMultipleStart || !this.formData.saleMultipleEnd) return this.$message.error('请输入销量倍数')
                if (this.formData.saleMultipleEnd < this.formData.saleMultipleStart) return this.$message.error('销量结束倍数不能小于开始倍数')
            } else if (this.operationType == 4) {
                if (this.formData.startBuyCount === undefined || this.formData.startBuyCount === null || this.formData.startBuyCount === 0) return this.$message.error('起拍量不能为空或0')
            } else if (this.operationType == 5) {
                if (!this.formData.status) return this.$message('请选择状态')
            } else if (this.operationType == 6) {
                if (!this.formData.priceMultipleType) return this.$message.error('请选择成本价/零售价')
                if (this.formData.byPriceMultipleStart && !this.formData.byPriceMultipleEnd) return this.$message.error('请输入包邮价格倍数')
                if (this.formData.byPriceMultipleEnd < this.formData.byPriceMultipleStart) return this.$message.error('包邮价格倍数结束不能小于开始倍数')
                if (this.formData.byPriceMultipleStart <= this.formData.notBYPriceMultipleEnd) return this.$message.error('包邮价格倍数开始不能小于等于不包邮价格倍数结束')
                if (this.formData.notBYPriceMultipleStart && !this.formData.notBYPriceMultipleEnd) return this.$message.error('请输入不包邮价格倍数')
                if (this.formData.notBYPriceMultipleEnd < this.formData.notBYPriceMultipleStart) return this.$message.error('不包邮价格倍数结束不能小于开始倍数')
            } else if (this.operationType == 7) {
                if (this.formData.showDescription === null || this.formData.showDescription === undefined) return this.$message.error('请选择是否展示1688详情页')
            }
            if (ValidateWarePosition) {
                this.$confirm('此仓位已经占用, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const { success } = await changeChooseGoodsCenterProductSet(this.formData)
                    if (success) {
                        this.$message.success('操作成功')
                        this.getList()
                        this.selectList = []
                        this.otherOperationVisible = false
                    }
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消操作'
                    });
                });
            } else {
                const { success } = await changeChooseGoodsCenterProductSet(this.formData)
                if (success) {
                    this.$message.success('操作成功')
                    this.getList()
                    this.selectList = []
                    this.otherOperationVisible = false
                }
            }

        },
        addChildrens(data) {
            let arr = []
            data.forEach(item => {
                if (item.pCategoryName == '0') {
                    arr.push(item)
                }
            })
            arr.forEach(item => {
                item.children = []
                data.forEach(child => {
                    if (child.pCategoryName == item.categoryName) {
                        item.children.push(child)
                    }
                })
            })
            this.CategoryList = arr
        },
        async getCategory() {
            const { data, success } = await getSetBusinessCategory()
            if (success) {
                this.addChildrens(data)
            }
        },
        clear() {
            this.formData = {
                skuIds: [],//商品编码    
                proCodes: [],//产品ID
                saleMultipleStart: undefined,//销量开始倍数
                saleMultipleEnd: undefined,//销量结束倍数
                categoryName: null,//类目
                warePosition: null,//仓位
                startBuyCount: undefined,//起拍量
                status: null,//状态
                byPriceMultipleStart: undefined,//包邮价格倍数开始
                byPriceMultipleEnd: undefined,//包邮价格倍数结束
                notBYPriceMultipleStart: undefined,//不包邮价格倍数开始
                notBYPriceMultipleEnd: undefined,//不包邮价格倍数结束
                priceMultipleType: null,//价格倍数类型
                showDescription: null,//是否展示1688详情页
            }
        },
        async otherOperation(val, title) {
            if (this.selectList.length == 0) return this.$message.error('请选择数据')
            this.clear()
            const parentNode = this.selectList.filter(item => item.pId == 0)
            const childrenNode = this.selectList.filter(item => item.pId != 0)
            this.formData.proCodes = parentNode.map(item => item.proCode)
            this.formData.skuIds = childrenNode.map(item => {
                return {
                    skuId: item.skuId,
                    proCode: item.proCode
                }
            })
            this.operationType = val
            this.otherOperationTitle = title
            if (val == 1 || val == 3 || val == 6 || val == 7) {
                if (parentNode.length == 0) return this.$message.error(`请选择父节点进行操作`)
            }
            if (val == 3) {
                this.formData.saleMultipleStart = 1
                this.formData.saleMultipleEnd = 1
            }
            this.otherOperationVisible = true
        },
        selectCheckBox(val) {
            this.selectList = val
        },
        proCodeCallback(e, type) {
            if (type == '1') {
                this.ListInfo.proCodes = e
            } else {
                this.ListInfo.goodsCodes = e
            }
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            this.ListInfo.categoryName = this.ListInfo.categoryName ? this.ListInfo.categoryName.join(',') : null
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getChooseGoodsCenterProductSetList(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 150px;
        margin-right: 10px;
    }
}

.publicDisplay {
    display: flex;
    justify-content: center;
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
</style>
