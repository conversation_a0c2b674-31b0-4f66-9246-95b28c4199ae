<template>
  <div class="region-config-form" v-loading="listLoading">
    共{{ warehouseData.length || 0 }}条
    <div class="table-section box">
      <el-table ref="table" :data="warehouseData" row-key="id" border style="width: 100%" height="200"
        show-overflow-tooltip>
        <el-table-column type="index" label="#" align="center" width="60" />
        <el-table-column label="仓库" prop="wmsName" width="200" :show-overflow-tooltip="true" />
        <el-table-column label="区域" prop="region" width="200" :show-overflow-tooltip="true" />
        <el-table-column label="排序" prop="sort" :show-overflow-tooltip="true">
          <template #default="{ row }">
            <el-input-number :controls="false" :min="1" :max="999" :precision="0" placeholder="排序" style="width: 100%"
              v-model="row.sort" />
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="select-section box">
      <el-select v-model="purchasingStaff" placeholder="请选择品牌" class="select-input" filterable clearable>
        <el-option v-for="item in availablePurchasingList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button @click="onPurchasingAdd" type="primary">新增</el-button>
      <el-button type="danger" class="ml10" @click="onRemoveAll">清空</el-button>
      <div class="tag-container">
        <el-tag v-for="item in selectedList" :key="item.value" closable type="success" @close="purchasingDelete(item)">
          {{ item.label }}
        </el-tag>
      </div>
    </div>

    <div class="tag-section box">
      <div class="tag-container">
        <el-tag v-for="(tag, index) in groupTagList" :key="`tag_${index}_${tag.value}`" closable type="success"
          @close="handleClose(tag)">
          {{ tag.value }}
        </el-tag>
      </div>
      <div class="tag-list-row">
        <div class="tag-list">
          <el-tag v-for="(item, index) in customList" :key="index" style="cursor: pointer;"
            :type="groupTagList.some(t => t.value == item.key) ? '' : 'info'" @click="onAddGroup(item.id, item.key)">
            {{ item.key }}
          </el-tag>
        </div>
        <el-button type="danger" class="ml10" @click="clearTags">清空</el-button>
      </div>
    </div>

    <div class="button_section">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="saveConfig" :loading="saving">保存</el-button>
    </div>
  </div>
</template>

<script>
import {
  saveSampleRegistrationRegionConfig,
  getSampleRegistrationRegionConfig,
  getRegionWms
} from '@/api/inventory/sampleGoods';

export default {
  name: "proxyBiddingConfigForm",
  props: {
    customList: {
      type: Array,
      default: () => []
    },
    purchasingList: {
      type: Array,
      default: () => []
    },
    region: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      listLoading: true,
      purchasingStaff: null,
      selectedList: [],
      groupTagList: [],
      warehouseData: [],
      availablePurchasingList: [],
      saving: false,
      // 数据变更状态监听
      hasTableChanges: false,
      hasSelectChanges: false,
      hasTagChanges: false,
      // 初始数据备份，用于比较是否有变更
      initialData: {
        selectedList: [],
        groupTagList: [],
        warehouseData: []
      }
    }
  },
  watch: {
    purchasingList: {
      handler() {
        this.initPurchasingData();
      },
      immediate: true
    },
    region: {
      handler(newRegion, oldRegion) {
        // 区域切换才加载数据
        if (newRegion && newRegion !== oldRegion) {
          this.loadRegionData();
        }
      },
      immediate: false
    },
    // 监听仓库数据变更
    warehouseData: {
      handler(newVal) {
        this.hasTableChanges = this.checkTableChanges(newVal);
      },
      deep: true
    },
    // 监听品牌选择变更
    selectedList: {
      handler(newVal) {
        this.hasSelectChanges = this.checkSelectChanges(newVal);
      },
      deep: true
    },
    // 监听标签变更
    groupTagList: {
      handler(newVal) {
        this.hasTagChanges = this.checkTagChanges(newVal);
      },
      deep: true
    }
  },
  methods: {
    // 检查是否有数据变更
    hasAnyChanges() {
      return this.hasTableChanges || this.hasSelectChanges || this.hasTagChanges;
    },
    // 检查仓库数据变更
    checkTableChanges(newVal) {
      if (!this.initialData.warehouseData || this.initialData.warehouseData.length === 0) {
        return false;
      }
      return JSON.stringify(newVal) !== JSON.stringify(this.initialData.warehouseData);
    },
    // 检查品牌选择变更
    checkSelectChanges(newVal) {
      if (!this.initialData.selectedList || this.initialData.selectedList.length === 0) {
        return newVal && newVal.length > 0;
      }
      return JSON.stringify(newVal) !== JSON.stringify(this.initialData.selectedList);
    },
    // 检查标签变更
    checkTagChanges(newVal) {
      if (!this.initialData.groupTagList || this.initialData.groupTagList.length === 0) {
        return newVal && newVal.length > 0;
      }
      return JSON.stringify(newVal) !== JSON.stringify(this.initialData.groupTagList);
    },
    // 保存初始数据快照
    saveInitialData() {
      this.initialData = {
        selectedList: JSON.parse(JSON.stringify(this.selectedList)),
        groupTagList: JSON.parse(JSON.stringify(this.groupTagList)),
        warehouseData: JSON.parse(JSON.stringify(this.warehouseData))
      };
      // 重置变更状态
      this.hasTableChanges = false;
      this.hasSelectChanges = false;
      this.hasTagChanges = false;
    },
    //回显数据
    async loadRegionData() {
      if (!this.region) return;
      try {
        this.listLoading = true
        this.purchasingStaff = null
        await new Promise(resolve => setTimeout(resolve, 1000));
        await this.loadRegionWarehouses();//仓库
        await this.loadRegionConfig();//配置
        // 数据加载完成后保存初始数据快照
        this.$nextTick(() => {
          this.saveInitialData();
        });
      } catch (error) {
        console.error('加载区域数据失败:', error);
      } finally {
        this.listLoading = false
      }
    },
    // 获取仓库数据
    async loadRegionWarehouses() {
      try {
        const response = await getRegionWms({ region: this.region });
        if (response.success && response.data) {
          this.warehouseData = response.data.map(item => ({
            wmsName: item.name,
            wmsId: item.wms_co_id,
            sort: undefined,
            region: this.region
          }));
          return response.data;
        }
      } catch (error) {
        console.error('加载区域仓库失败:', error);
        this.$message.error('加载仓库数据失败');
        return [];
      }
    },
    // 品牌数据-备份一份
    initPurchasingData() {
      this.availablePurchasingList = [...this.purchasingList];
    },
    // 回显
    async loadRegionConfig() {
      try {
        const response = await getSampleRegistrationRegionConfig({ region: this.region });
        if (response.success && response.data) {
          const data = response.data;
          // 回显品牌
          if (data.brandList && data.brandList.length > 0) {
            this.selectedList = data.brandList.map(item => ({
              value: item.key,
              label: item.value,
            }));
            // 从可选列表中移除已选的
            this.availablePurchasingList = this.purchasingList.filter(p =>
              !this.selectedList.some(s => s.value == p.value)
            );
          }
          // 回显标签
          if (data.labels && data.labels.length > 0) {
            this.groupTagList = data.labels.map((item) => ({
              key: item.id,
              value: item.value
            }));
          }
          // 回显仓库排序（仓库数据在loadRegionWarehouses方法中）
          if (data.wareSorts && data.wareSorts.length > 0) {
            data.wareSorts.forEach(sortItem => {
              const warehouse = this.warehouseData.find(w => w.wmsId == sortItem.wmsId);
              if (warehouse) {
                warehouse.sort = sortItem.sort || undefined;
              }
            });
          }
        }
      } catch (error) {
        console.error('加载区域配置失败:', error);
      }
    },
    onPurchasingAdd() {
      if (!this.purchasingStaff) return this.$message.warning('请选择品牌');
      const selectedItem = this.availablePurchasingList.find(item => item.value == this.purchasingStaff);
      if (!selectedItem) return;
      // 校验
      if (!this.selectedList.some(item => item.value == selectedItem.value)) {
        this.selectedList.push({ value: selectedItem.value, label: selectedItem.label });
      }
      // 从可选列表移除
      this.availablePurchasingList = this.availablePurchasingList.filter(item =>
        item.value != selectedItem.value
      );
      this.purchasingStaff = null;
    },
    purchasingDelete(item) {
      this.selectedList = this.selectedList.filter(selected => selected.value !== item.value);
      // 添加回可选列表
      const originalItem = this.purchasingList.find(original => original.value == item.value);
      if (originalItem && !this.availablePurchasingList.some(p => p.value == item.value)) {
        this.availablePurchasingList.push(originalItem);
      }
    },
    onRemoveAll() {
      // 将所有已选的人员添加回可选列表
      this.selectedList.forEach(selectedItem => {
        const originalItem = this.purchasingList.find(original => original.value == selectedItem.value);
        if (originalItem && !this.availablePurchasingList.some(p => p.value == selectedItem.value)) {
          this.availablePurchasingList.push(originalItem);
        }
      });
      this.selectedList = [];
    },
    handleClose(tag) {
      this.groupTagList = this.groupTagList.filter(t => t.value !== tag.value);
    },
    clearTags() {
      this.groupTagList = [];
    },
    onAddGroup(value, label) {
      // 避免重复添加（基于标签名称判断）
      if (!this.groupTagList.some(tag => tag.value == label)) {
        this.groupTagList.push({ key: value, value: label });
      }
    },
    // 获取格式化后的参数
    getFormattedParams() {
      // 转换品牌为后端所需格式
      const brandList = this.selectedList.map(item => ({
        brandId: item.value,
        brandName: item.label
      }));
      // 转换标签为labels格式
      const labels = this.groupTagList.map(item => item.value);
      // 添加仓库排序数据
      const wareSorts = this.warehouseData.map(item => ({
        wmsId: item.wmsId,
        wmsName: item.wmsName,
        region: item.region,
        sort: item.sort || null
      }));
      return {
        region: this.region,
        brandList: brandList,
        labels: labels,
        wareSorts: wareSorts
      };
    },
    async saveConfig() {
      this.saving = true;
      try {
        const params = this.getFormattedParams();
        const { success } = await saveSampleRegistrationRegionConfig(params);
        if (success) {
          this.$message.success(`${this.region}区域配置保存成功`);
          this.$emit('saved', this.region);
          // 保存成功后更新初始数据快照
          this.saveInitialData();
          this.loadRegionData()
        } else {
          this.$message.error('保存失败');
        }
      } catch (error) {
        console.error('保存出错:', error);
        this.$message.error('保存出错');
      } finally {
        this.saving = false;
      }
    },
    cancel() {
      this.$emit('cancel');
    }
  }
}
</script>

<style scoped lang="scss">
.region-config-form {
  padding: 10px;
  background-color: #f5f7fa;
}

.box {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 12px;
  background-color: #ffffff;
  margin-bottom: 10px;
}

.select-section {
  .select-input {
    width: 50%;
    margin-right: 10px;
  }

  .ml10 {
    margin-left: 10px;
  }

  // 在选择器和按钮区域下方添加分隔线
  position: relative;
  padding-bottom: 10px;
  margin-bottom: 10px;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 15px;
    right: 15px;
    height: 1px;
    background-color: #e4e7ed;
    opacity: 0.8;
  }

  .tag-container {
    margin-top: 10px;
    max-height: 70px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    min-height: 50px;
  }
}

.tag-section {
  .tag-container {
    max-height: 70px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    min-height: 50px;
    margin-bottom: 10px;
    padding-bottom: 10px;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 15px;
      right: 15px;
      height: 1px;
      background-color: #e4e7ed;
      opacity: 0.8;
    }
  }

  .tag-list-row {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding-top: 15px;

    .tag-list {
      flex-grow: 1;
      display: flex;
      flex-wrap: wrap;
      max-height: 70px;
      overflow-y: auto;
      gap: 5px;
    }

    .ml10 {
      margin-left: 10px;
    }
  }
}

// 已选标签增强样式
.el-tag.success {
  background-color: #f0f9eb;
  color: #67c23a;
  font-weight: bold;
  border: 1px solid #b3e19d;
  box-shadow: 0 1px 3px rgba(103, 194, 58, 0.2);
}

// 未选标签增强样式
.el-tag.info {
  background-color: #f4f4f5;
  color: #909399;
  border: 1px solid #dcdfe6;
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    background-color: #e9ebf0;
    color: #606266;
    border-color: #c0c4cc;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.05);
  }
}

// 控制间距统一
.tag-container .el-tag {
  margin: 2px;
}

.button_section {
  display: flex;
  justify-content: end;
  gap: 10px;
}
</style>
