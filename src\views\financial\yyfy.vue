<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <el-tabs v-model="activeName" style="height:94%;">
    <el-tab-pane label="一站式智投汇总" name="tab0" style="height: 100%;">
        <shizhitou :filter="filter" ref="shizhitou" style="height: 100%;"/>
    </el-tab-pane>
    <el-tab-pane label="超级推荐汇总" name="tab1" style="height: 100%;">
        <tuijian :filter="filter" ref="tuijian" style="height: 100%;"/>
    </el-tab-pane>
    <el-tab-pane label="淘宝客汇总" name="tab2" style="height: 100%;">
        <taobaoke :filter="filter" ref="taobaoke" style="height: 100%;"/>
    </el-tab-pane>
   
    <el-tab-pane label="直通车汇总" name="tab4" style="height: 100%;">
        <zhitongche :filter="filter" ref="zhitongche" style="height: 100%;"/>
    </el-tab-pane>
    <el-tab-pane label="淘特商品资料" name="tab5" style="height: 100%;">
        <taoteshangping :filter="filter" ref="taoteshangping" style="height: 100%;"/>
    </el-tab-pane>

     <el-tab-pane label="淘特推广汇总" name="tab3" style="height: 100%;">
        <taotetuiguang :filter="filter" ref="taotetuiguang" style="height: 100%;"/>
    </el-tab-pane>
    
  
    <el-tab-pane label="淘礼金汇总" name="tab7" style="height: 100%;">
        <taolijing :filter="filter" ref="taolijing" style="height: 100%;"/>
    </el-tab-pane>
    <el-tab-pane label="特殊单" name="tab8" style="height: 100%;">
        <yingxiao :filter="filter" ref="yingxiao" style="height: 100%;"/>
    </el-tab-pane>
    <el-tab-pane label="催收礼金汇总" name="tab9" style="height: 100%;">
        <cuishou :filter="filter" ref="cuishou" style="height: 100%;"/>
    </el-tab-pane>
    <el-tab-pane label="拼多搜索推广" name="tab11" style="height: 100%;">
        <pingduoduo :filter="filter" ref="pingduoduotuiguang" style="height: 100%;"/>
    </el-tab-pane>
        <el-tab-pane label="拼多场景展示" name="tab12" style="height: 100%;">
        <pingduoduo :filter="filter" ref="pingduoduochangjing" style="height: 100%;"/>
    </el-tab-pane>
        <el-tab-pane label="拼多放心推" name="tab13" style="height: 100%;">
        <pingduoduo :filter="filter" ref="pingduoduofangxintui" style="height: 100%;"/>
    </el-tab-pane>
        <el-tab-pane label="拼多直播推广" name="tab14" style="height: 100%;">
        <pingduoduo :filter="filter" ref="pingduoduozhibo" style="height: 100%;"/>
    </el-tab-pane>
       <el-tab-pane label="拼多全站推广" name="tab17" style="height: 100%;">
        <pingduoduo :filter="filter" ref="pingduoduoquanzhan" style="height: 100%;"/>
    </el-tab-pane>
        <el-tab-pane label="核算" name="tab10" style="height: 100%;">
        <accountresult :filter="filter" ref="accountresult" style="height: 100%;"/>
    </el-tab-pane>
    <el-tab-pane label="异常数据表" name="tab15" style="height: 100%;">
        <unusual :filter="filter" ref="unusual" style="height: 100%;"/>
    </el-tab-pane>
  <el-tab-pane label="统计数据" name="tab16" style="height: 100%;">
        <totalbyfeetype :filter="filter" ref="totalbyfeetype" style="height: 100%;"/>
    </el-tab-pane>
  </el-tabs>
  </my-container >

 </template>
<script>
import MyContainer from "@/components/my-container";

 import shizhitou from '@/views/financial/shizhitou'

 import tuijian from '@/views/financial/tuijian'

 import taobaoke from '@/views/financial/taobaoke'

 import taotetuiguang from '@/views/financial/taotetuiguang'

 import zhitongche from '@/views/financial/zhitongche'

 import taoteshangping from '@/views/financial/taoteshangping'

 import dahuixiong from '@/views/financial/dahuixiong'

 import taolijing from '@/views/financial/taolijing'

 import yingxiao from '@/views/financial/yingxiao'

 import cuishou from '@/views/financial/cuishou'

import accountresult from '@/views/financial/accountresult'

 
 import pingduoduo from '@/views/financial/pingduoduo'

 import unusual from '@/views/financial/unusual'

 import totalbyfeetype from '@/views/financial/totalbyfeetype'

export default {
  name: "Users",
  components: { MyContainer,shizhitou,tuijian,taobaoke,taotetuiguang,zhitongche,taoteshangping,dahuixiong,taolijing,yingxiao,cuishou,accountresult,pingduoduo,unusual,totalbyfeetype},
  data() {
    return {
      that:this,
      Filter: {
      },
      shopList:[],
      userList:[],
      groupList:[],
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
    };
  },
  mounted() {
this.$refs.pingduoduotuiguang.setfeetype('拼多多搜索推广');
this.$refs.pingduoduochangjing.setfeetype('拼多多场景展示');
this.$refs.pingduoduofangxintui.setfeetype('拼多多放心推');
this.$refs.pingduoduozhibo.setfeetype('拼多多直播推广');
this.$refs.pingduoduoquanzhan.setfeetype('拼多多全站推广');

  },
  methods: {
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
