<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="140px" class="demo-ruleForm">
        <el-form-item label="南昌" prop="ncCost">
          <inputNumberYh v-model="ruleForm.ncCost" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="武汉" prop="whCost">
          <inputNumberYh v-model="ruleForm.whCost" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="义乌" prop="ywCost">
          <inputNumberYh v-model="ruleForm.ywCost" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="深圳" prop="szCost">
          <inputNumberYh v-model="ruleForm.szCost" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="选品中心" prop="productSelectionCenter">
          <inputNumberYh v-model="ruleForm.productSelectionCenter" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" v-model="ruleForm.remarks" placeholder="请输入" clearable :maxlength="50"
            show-word-limit :autosize="{ minRows: 5, maxRows: 5 }" class="publicCss" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { performanceDonationSubmit } from '@/api/people/peoplessc.js';
export default {
  name: 'departmentEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  data() {
    return {
      ruleForm: {
        ncCost: '',
        whCost: '',
        ywCost: '',
        szCost: '',
        remarks: '',
        productSelectionCenter: ''
      },
      rules: {
        ywCost: [
          { required: true, message: '请输入义乌', trigger: 'blur' },
        ],
        ncCost: [
          { required: true, message: '请输入南昌', trigger: 'blur' },
        ],
        whCost: [
          { required: true, message: '请输入武汉', trigger: 'blur' },
        ],
        szCost: [
          { required: true, message: '请输入深圳', trigger: 'blur' },
        ],
        productSelectionCenter: [
          { required: true, message: '请输入选品中心', trigger: 'blur' },
        ],
      }
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };
  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod');
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const { data, success } = await performanceDonationSubmit(this.ruleForm)
          if (!success) {
            return
          }
          this.$emit("search");
        } else {
          console.error('submit failed, reason: ', valid);
          return false;
        }
      });
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
