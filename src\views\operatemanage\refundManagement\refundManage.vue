<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="退款处理" name="tab0" style="height: 100%;">
                <refundData  ref="refundData" style="height: 100%;"></refundData>
            </el-tab-pane>
            <el-tab-pane label="ID预警" name="tab1" style="height: 100%;">
                <IDWarning ref="IDWarning" style="height: 100%;"></IDWarning>
            </el-tab-pane>
            <el-tab-pane label="退款统计" name="tab2" style="height: 100%;">
                <refundStatistics ref="refundStatistics" style="height: 100%;"></refundStatistics>
            </el-tab-pane>
            <el-tab-pane label="申诉明细" name="tab3" style="height: 100%;">
                <representationsDetail ref="representationsDetail" style="height: 100%;"></representationsDetail>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import refundData from '@/views/operatemanage/refundManagement/refundData';
import IDWarning from '@/views/operatemanage/refundManagement/IDWarning';
import refundStatistics from '@/views/operatemanage/refundManagement/refundStatistics';
import representationsDetail from '@/views/operatemanage/refundManagement/representationsDetail';

export default {
    name: "refundManage",
    components: { MyContainer, refundData , IDWarning, refundStatistics, representationsDetail },
    data() {
        return {
            that: this,
            activeName: 'tab0',
            pageLoading: false,
            // filter: {
            // },
        };
    },
    async mounted() {
        // //根据权限来判断显示哪个tab
        // if (this.checkPermission('refundData')){
        //     this.activeName = 'tab0';
        // } else if (this.checkPermission('IDWarning')){
        //     this.activeName = 'tab1';
        // } else if (this.checkPermission('refundStatistics')){
        //     this.activeName = 'tab2';
        // } else {
        //     this.activeName = 'tab3';
        // }
        // await this.onSearch();
    },
    methods: {
        async onSearch() {
            this.$nextTick(() => {
            })
        },
    },
}
</script>