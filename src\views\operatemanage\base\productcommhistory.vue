<template>
    <my-container>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' @select='selectchange' :isSelection='false' :isSelectColumn="false" :tableData='productlist' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" />
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </my-container>
</template>

<script>
    import { getProductCommission } from '@/api/operatemanage/base/product'
    import MyContainer from '@/components/my-container'
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import { formatLinkProCode } from "@/utils/tools";

    const tableCols = [
        { istrue: true, prop: 'proCode', label: '宝贝ID', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
        { istrue: true, prop: 'commissionRate1', label: '毛利提成比例1', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionUserName1 + row.commissionRate1 + '%' },
        { istrue: true, prop: 'commissionRate2', label: '毛利提成比例2', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionUserName2 + row.commissionRate2 + '%' },
        { istrue: true, prop: 'commissionRate3', label: '毛利提成比例3', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionUserName3 + row.commissionRate3 + '%' },
        { istrue: true, prop: 'commissionRate4', label: '毛利提成比例4', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionUserName4 + row.commissionRate4 + '%' },
        { istrue: true, prop: 'commissionProfitRate1', label: '净利提成比例1', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionUserName1 + row.commissionProfitRate1 + '%' },
        { istrue: true, prop: 'commissionProfitRate2', label: '净利提成比例2', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionUserName2 + row.commissionProfitRate2 + '%' },
        { istrue: true, prop: 'commissionProfitRate3', label: '净利提成比例3', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionUserName3 + row.commissionProfitRate3 + '%' },
        { istrue: true, prop: 'commissionProfitRate4', label: '净利提成比例4', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionUserName4 + row.commissionProfitRate4 + '%' },
        { istrue: true, prop: 'historyTime', label: '历史时间时间', width: '150', sortable: 'custom', permission: "productpermis", },
        { istrue: true, prop: 'createdTime', label: '添加时间', width: 'auto', sortable: 'custom', permission: "productpermis", },
        // {type:'button',btnList:[{label:"编辑",handle:(that,row)=>that.onEdit(row)},{label:"删除",handle:(that,row)=>that.onDelete(row),isDisabled:true}],},
    ];
    const tableHandles1 = []

    export default {
        name: 'YunhanAdminProductcommhistory',
        components: { MyContainer, MyConfirmButton, cesTable },

        data () {
            return {
                that: this,
                filter: {
                    name: '',
                    procode: '',
                    brandRate: null,
                    categoryids: [],
                },
                pager: { OrderBy: "id", IsAsc: false },
                tableCols: tableCols,
                tableHandles: tableHandles1,
                productlist: [],
                total: 0,
                sels: [],
                selids: [],
                listLoading: false,
            };
        },

        async mounted () {

        },

        methods: {
            async onSearch (procodes) {
                console.log('接收值', procodes)
                this.filter.procode = procodes
                this.$refs.pager.setPage(1)
                await this.getlist()
            },
            async getlist () {
                var pager = this.$refs.pager.getPager()

                if (this.filter.categoryids != null && this.filter.categoryids.length > 0) {
                    this.filter.productCategoryId = this.filter.categoryids[this.filter.categoryids.length - 1]
                }
                const params = {
                    ...pager,
                    ...this.pager,
                    ... this.filter
                }
                this.listLoading = true
                console.log('参数值：', this.filter.procode)
                const res = await getProductCommission(params)
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total
                const data = res.data.list
                data.forEach(d => {
                    d._loading = false
                })
                console.log('数据', data)
                this.productlist = data
                this.selids = []
            },
            sortchange (column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch(this.filter.procode);
            },
            selsChange: function (sels) {
                this.sels = sels
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
        },
    };
</script>

<style lang="scss" scoped>
</style>