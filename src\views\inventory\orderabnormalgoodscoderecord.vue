<template>
  <container>
    <ces-table ref="table" style="height: 97%; width: 99%;" :that='that' :isIndex='true' @sortchange='sortchange' :isSelectColumn="false" @cellclick='cellclick'
         :hasexpand='true' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"
         :showsummary='true' :summaryarry='summaryarry'>
      </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template> 
  </container>
</template>
<script>
import {
  getOutOfStockSectionReportFollowRemarksAsync
} from '@/api/inventory/abnormal'
import {formatTime} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container/noheader";
const tableCols =[
      {istrue:true,prop:'goodsCode',label:'商品编码',  width:'100',sortable:'custom',}, 
      {istrue:true,prop:'createdTime',label:'时间', width:'80', width:'105',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD HH:mm:ss')},
      {istrue:true,prop:'createdUserName',label:'上报人',sortable:'custom', width:'65',},
      {istrue:true,prop:'planArrivalTime',label:'预计到货日期',  width:'110',sortable:'custom',formatter:(row)=>formatTime(row.planArrivalTime,'YYYY-MM-DD')},
      {istrue:true,prop:'reason',label:'原因', width:'120',sortable:'custom'},  
      {istrue:true,prop:'reMark',label:'解决方案',type:'editor'}
];
const tableHandles=[ ];
export default {
  name: "goodscoderecord",
  components: {container,cesTable},
  props:{

  },
  data() {
    return {
      that:this,
      filter:{goodsCode:""},
      imgPreview:{img:"",show:false},
      list: [],
      oderDetailView:{},
      drawervisible:false,
      visiblepopover: false,
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"planArrivalTime",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [],
      fileList:[],
      listLoading: false,
      dialogVisible: false,
      pageLoading: false,
    };
  }, 
 async mounted() {
  },
 methods: {
   async onSearch(goodsCode) {
       this.filter={goodsCode:goodsCode},
       this.$refs.pager.setPage(1)
       this.getlist()
  },
  async getlist() {
      if (!this.pager.OrderBy) this.pager.OrderBy="";
       var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,...this.filter}
       this.listLoading = true
      this.list=[];
      var res = await getOutOfStockSectionReportFollowRemarksAsync(params)
      this.listLoading = false
      console.log('this.res',res)
     // if(!(res.code==1&&res.data)) return
      this.total = res.data.total
      const data = res.data.list
      this.list = data
      console.log('this.list',this.list)
    },
  async cellclick(row, column, cell, event){ 

  },
  async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch(this.filter.goodsCode);
    },  
  },
};
</script>
<style lang="scss" scoped>
.imgDolg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 9999;
  background-color: rgba(140, 134, 134, 0.6);
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  #imgDolgClose {
    position: fixed;
    top: 35px;
    cursor: pointer;
    right: 7%;
    font-size: 50px;
    color: white;
  }
  img{
    width: 80%;
  }
}
</style>

