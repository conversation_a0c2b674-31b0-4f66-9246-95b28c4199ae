<template>
 <my-container>
  <el-form label-width="80px" :model="form" ref="addForm">
    <el-form-item label="用户名称" :rules="[{ required: true, message: '请输入姓名', trigger: 'blur' }]" prop="userId">
     <el-select v-model="form.userId" :loading="selloading" filterable @blur="refformcheck"
         :remote-method="remoteMethod" fliterable placeholder="请输入姓名" remote :clearable="true">
         <el-option v-for="(item) in selname" :key="'userSelector'+item.value+ item.extData.defaultDeptId" :label="item.label"
             :value="item.value">
             <span>{{item.label}}</span>
            <span  style=" color: #8492a6; ">({{item.extData.position}},{{item.extData.empStatusText}}{{item.extData.jstUserName ? ","+item.extData.jstUserName:""}})</span>
            <span style=" color: #8492a6; "> {{item.extData.deptName}}</span>
         </el-option>
     </el-select>
    </el-form-item>
    <el-form-item label="权限类型" :rules="[{ required: true, message: '请输入权限类型', trigger: 'blur' }]" prop="prmType">
     <el-select v-model="form.prmType" placeholder="权限类型" @change="prmDatas=[]" @blur="refformcheck" >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="权限数据" prop="prmDataId" :rules="[{ required: true, message: '请输入权限数据', trigger: 'blur' }]">



     <el-select filterable v-if="form.prmType==1" v-model="prmDatas"  clearable multiple placeholder="运营组" style="width:100%">
       <!-- <el-option key="无运营组" label="无运营组" :value="0"></el-option> -->
       <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item"  />
     </el-select>

     <el-select filterable v-else-if="form.prmType==2" v-model="prmDatas"  clearable multiple placeholder="运营专员或助理"
        style="width: 150px">
        <!-- <el-option key="无运营专员或助理" label="无运营专员或助理" :value="0"></el-option> -->
        <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item"  />
      </el-select>

      <el-select filterable v-else-if="form.prmType==3" v-model="prmDatas" clearable placeholder="采购" multiple style="width:100%" >
       <!-- <el-option key="所有" label="所有" value></el-option> -->
       <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item"></el-option>
     </el-select>

     <div style="line-height: 18px;" v-else>
        <el-alert
        :closable="false"
        title="请先选择权限类型"
        type="info">
      </el-alert>
     </div>


    </el-form-item>
    <el-form-item label="平台">
      <el-select v-model="form.platformArr" placeholder="请选择平台" multiple clearable filterable style="width: 100%">
        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </el-form-item>
  </el-form>
  <template slot="footer">
      <el-row>
          <el-col :span="24" style="text-align:right;">
              <el-button @click="onClose">关闭</el-button>
              <el-button type="primary" @click="onSave(true)">保存</el-button>
          </el-col>
      </el-row>
  </template>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import { delErpDataPermissions, addErpDataPermissions, allErpDataPermissions } from '@/api/admin/user'
import { getDirectorGroupList, getDirectorList, getList as getshopList } from '@/api/operatemanage/base/shop'
import { getAllProBrand } from '@/api/inventory/warehouse'
import { getAllUser } from '@/api/inventory/packagesprocess';
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'
import { platformlist } from '@/utils/tools'
const options = [
    { value: 1, label: '运营组' },
    { value: 2, label: '运营专员或助理' },
    { value: 3, label: '采购'}
];
export default {
  name: 'Vue2Demo20231223Dialogadd',
  components: {
     MyContainer
  },
  data() {
    return {
     platformlist,
     form: {
      prmType: null,
      platformArr: [],
      userId: null
     },
     prmDatas: [],
     options: options,
     brandlist: [],
     directorlist: [],
     grouplist: [],
     selname: [],
     selloading: false,
    };
  },

  mounted() {
    this.chugetall();
  },

  methods: {
   async remoteMethod(query) {
       this.loading = true;
       if (query !== '') {
           let rlt= await QueryAllDDUserTop100({ keywords: query });
           if (rlt && rlt.success) {
            this.selname = rlt.data?.map(item => {
                   return { label: item.userName, value: item.userId, extData:item }
               });
           }
       } else {
        this.selname = [...this.orgOptions];
       }
       this.loading = false;
   },
   async chugetall(){
      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });

      var res4 = await getAllProBrand();
      this.brandlist = res4.data.map(item => {
        return { value: item.key, label: item.value };
      });
   },
   async loadData({item}) {
       console.log("name",item)
   },
   async onSave(isClose) {
       if (await this.save()) {
           this.$emit('afterSave');
           if (isClose)
               this.$emit('close');
       }
   },
   onClose() {
       this.$emit('close');
   },
   refformcheck(){
    this.$refs['addForm'].validate()
   },
   async save() {
    if (!this.$refs['addForm'].validate()) {
      return;
    }


    let newarr = [];
    this.prmDatas.map((item)=>{
     newarr.push({prmDataId: parseInt(item.value), prmDataName: item.label})
     // item.prmDataId = item.value;
     // item.prmDataName = item.label;
    })
    this.form.prmDatas = newarr;
    // return;
       this.pageLoading = true;
       let saveData = {
        ...this.form,
        userId: parseInt(this.form.userId),
        platformIds: this.form.platformArr?.join(',') || ''
        };
        delete saveData.platformArr;


       let rlt = await addErpDataPermissions(saveData);
       if (rlt && rlt.success) {
           this.$message.success('保存成功！');
       }

       this.pageLoading = false;

       return (rlt && rlt.success);
   },
   // async remoteMethod(query) {
   //  this.selloading = true;
   //     if (query !== '') {
   //         this.loading = true;
   //         const res = await getAllUser();
   //         this.selloading = false;
   //         if (!res?.success) {
   //             return
   //         }
   //         this.loading = false;
   //         this.selname = res.data.filter(item => {
   //             return item.userName.toLowerCase()
   //                 .indexOf(query.toLowerCase()) > -1;
   //         });
   //     } else {
   //         this.selname = [];
   //         this.selloading = false;
   //     }
   // },

  },
};
</script>