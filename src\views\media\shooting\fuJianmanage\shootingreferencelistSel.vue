<template>
     <my-container v-loading="pageLoading">
        <template #header> 
            <el-button-group> 
                <el-button style="padding: 0;">
                    <el-input  style=" width: 120px;" v-model.trim="filter.taskName"  :maxlength =100  placeholder="任务名称" @keyup.enter.native="onSearch" clearable />
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button> 
            </el-button-group>
        </template>  
        <ces-table ref="table" 
                    :that='that' 
                    :isIndex='true' 
                    :hasexpand='false'
                    :isSelectColumn='false'  
                    :tableData='tasklist' 
                    :tableCols='tableCols'  
                    @rowclick='rowclick' 
                    :loading="listLoading" > 
                    <el-table-column type="expand">
                        <template slot-scope="props">
                            <div>
                                <el-table :data="props.row.detaildata" style="width: 100%">
                                    <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                                    </el-table-column>
                                </el-table>
                            </div>
                        </template> 
                    </el-table-column>
                    <template slot='extentbtn'> 
                    </template>
        </ces-table> 
        
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList" />
        </template>

        <el-dialog
            title="创建参考"
            :visible.sync="dialogVisible"
            width="95%"
            fullscreen>
            <shootingcreatereference></shootingcreatereference>
        </el-dialog>
    </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";  
import cesTable from "@/components/Table/table.vue";
import { pageReferenceViewTaskAsync,getReferenceMainReferencs,saveReferenceMainReferencsForVedio,saveReferenceMainReferencsForImg,saveReferenceMainReferencsForSku} from '@/api/media/referencemanage';
import shootingcreatereference from '@/views/media/shooting/fuJianmanage/shootingcreatereference'; 
const tableCols = [
    /* { istrue: true, prop: 'referenceManageTaskId', label: '任务编码', width: '200'   }, */
    { istrue: true, prop: 'taskName', label: '任务名称', width: '200'   },
    { istrue: true, prop: 'createdTime', label: '创建时间', width: '180'  },
    { istrue: true, prop: 'shootingTaskId', label: '新品任务编号', width: '200'}, 
];   
export default { 
  components: { MyContainer , cesTable,shootingcreatereference},
    data() {
        return {  
            that: this,
            pageLoading: false,
            summaryarry:[],
            tasklist:[],
            curselectrow :{},
            sels: [], // 列表选中列
            tableCols: tableCols,
            listLoading: false,
            total:0,
            pager: { OrderBy: "createdTime", IsAsc: false },
            filter: { hiddenShoot:1}, 
            dialogVisible: false,
        };
    },
    async mounted() {
        await this.onSearch();
    }, 
    methods: {
        //获取选择的行数据 
        rowclick: function (row, column,event) { 
            this.curselectrow =row;
        },
        getSelectRow()
        {
          
            if(this.curselectrow.shootingTaskId !=null){
                this.$message({message: this.$t('请选择，未绑定新品任务的参考附件'),type: 'error'});
                return;
            }
            return this.curselectrow; 
        },
        OnCreateTask(){
            let _this = this;
            _this.dialogVisible = true;
        },
        editReference(row) {
             
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            this.getTaskList();
        },
        async getTaskList() 
        { 
      
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
               
            };
            this.listLoading = true;
            const res = await pageReferenceViewTaskAsync(params); 
            this.listLoading = false;  
            this.total = res.data.total
            this.tasklist = res.data.list;
            //this.summaryarry = { videoTaskId_sum: " 0" };
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
    },
};
</script>
 

