<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange style="width: 350px;" :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss"
                    type="datetimerange" valueFormat="yyyy-MM-dd HH:mm:ss" endPlaceholder="上传时间"
                    startPlaceholder="上传时间" />
                <el-input v-model.trim="ListInfo.proCode" placeholder="ID" maxlength="50" clearable class="publicCss" />
                <!-- <el-select v-model="ListInfo.shopCode" placeholder="店铺名称" class="publicCss" clearable filterable
                    reserve-keyword :remote-method="remoteMethod1" remote>
                    <el-option v-for="item in shopList" :label="item.shopName" :value="item.shopCode"
                        :key="item.shopCode" />
                </el-select> -->
                <el-select v-model="ListInfo.isDownload" placeholder="是否下载" class="publicCss" clearable>
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                </el-select>
                <el-select v-model="ListInfo.parallelHostId" placeholder="对应主持名称" class="publicCss" clearable filterable
                    reserve-keyword :remote-method="remoteMethod" remote>
                    <el-option v-for="item in directorlist" :key="item.userId" :label="item.userName"
                        :value="item.userId" />
                </el-select>
                <el-switch v-model="ListInfo.isUpload" active-text="上传" inactive-text="未上传">
                </el-switch>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="importProps">导入</el-button>
                    <el-button type="primary" @click="batchDownload">下载</el-button>
                    <el-button type="primary" @click="batchEdit">编辑</el-button>
                    <el-button type="primary" @click="batchDel(true)">批量删除</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'"
            @select="select">
            <template slot="right">
                <vxe-column title="操作" width="120">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <el-button type="text" @click="batchDel(false, row)">删除</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".zip" :http-request="UpSuccessload">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit" :disabled="createFile"
                    :title="createFile ? '正在生成文件地址' : ''">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog title="编辑" :visible.sync="batchEditVisible" width="15%" v-dialogDrag>
            <div style="display: flex;justify-content: center;">
                <el-select v-model="parallelHostId" placeholder="对应主持名称" class="publicCss" clearable filterable
                    reserve-keyword :remote-method="remoteMethod2" remote>
                    <el-option v-for="item in directorlist1" :key="item.userId" :label="item.userName"
                        :value="item.userId" />
                </el-select>
            </div>
            <div class="btnGroup" style="justify-content: center;margin-top: 10px;">
                <el-button @click="batchEditVisible = false">取消</el-button>
                <el-button type="primary" @click="batchEditSumbit" :disabled="createFile">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import {
    getEvaluationCollectionPage,
    delEvaluationCollection,
    batchDelEvaluationCollection,
    getEvaluationFormManageDtl,
    ImportEvaluationCollectionAsync,
    getShopList,
    getOperationDeptAllWorker,
    exportEvaluationCollectionData,
    editEvaluationCollectionParallelHost
} from '@/api/operatemanage/EvaluationFormManage'
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
const tableCols = [
    { label: '', type: 'checkbox' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'proCode', label: 'ID', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'groupId', label: '放单小组', formatter: (row) => row.groupName },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'isUpload', label: '是否上传', formatter: (row) => row.isUpload? row.uploadQty: '否' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'releaseTime', label: '放单时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'parallelHostName', label: '对应主持名字', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'uploadTime', label: '上传时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'isDownload', label: '是否下载', formatter: (row) => row.isDownload == true ? '是' : row.isDownload == false ? '否' : row.isDownload },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'downloadTime', label: '下载时间', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                //默认当天0点时间yyyy-MM-dd HH:mm:ss
                startDate: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                endDate: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
                isUpload: false
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            fileList: [],
            importLoading: false,
            importVisible: false,
            file: null,
            loading: true,
            pickerOptions,
            isExport: false,
            selectData: [],
            createFile: false,
            directorlist: [],
            shopList: [],
            directorlist1: [],
            batchEditVisible: false,
            parallelHostId: null
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async batchEditSumbit() {
            if (this.parallelHostId == null) return this.$message.error('请选择对应主持名称')
            const { success } = await editEvaluationCollectionParallelHost({ ids: this.selectData.map(item => item.id), parallelHostId: this.parallelHostId })
            if (success) {
                this.$message.success('编辑成功')
                this.batchEditVisible = false
                this.getList()
            }
        },
        batchEdit() {
            if (this.selectData.length == 0) return this.$message.error('请选择要编辑的数据')
            this.parallelHostId = null
            this.batchEditVisible = true
        },
        async remoteMethod2(query) {
            if (query) {
                const { data } = await getOperationDeptAllWorker({ keywords: query })
                this.$set(this, 'directorlist1', data)
            } else {
                this.$set(this, 'directorlist1', [])
            }
        },
        async remoteMethod(query) {
            if (query) {
                const { data } = await getOperationDeptAllWorker({ keywords: query })
                this.$set(this, 'directorlist', data)
            } else {
                this.$set(this, 'directorlist', [])
            }
        },
        async remoteMethod1(query) {
            if (query) {
                const { data } = await getShopList({ shopName: query })
                this.$set(this, 'shopList', data)
            } else {
                this.$set(this, 'shopList', [])
            }
        },
        async batchDownload() {
            if (this.selectData.length == 0) return this.$message.error('请选择要下载的数据')
            const { data, success, msg } = await exportEvaluationCollectionData({ ids: this.selectData.map(item => item.id) })
            console.log("返回的数据")
            console.log("返回的数据", data, success, msg)
            if (data.success) {
                // window.open(data, '_blank')
                let name = data.data.split('/').pop();
                let link = document.createElement("a");
                fetch(data.data)
                    .then((res) => res.blob())
                    .then((blob) => {
                        // 将链接地址字符内容转变成blob地址
                        link.href = window.URL.createObjectURL(blob);
                        link.download =  name;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link)
                        window.URL.revokeObjectURL(link.href)
                    });
            } else {
                this.$message.error(data.msg)
            }

            
        },
        select(data) {
            this.selectData = data
        },
        batchDel(isBatch, row) {
            if (isBatch) {
                if (this.selectData.length == 0) return this.$message.error('请选择要删除的数据')
            }
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let isSuccess = false
                if (isBatch) {
                    const { success } = await batchDelEvaluationCollection({ ids: this.selectData.map(item => item.id) })
                    isSuccess = success
                } else {
                    const { success } = await delEvaluationCollection({ id: row.id })
                    isSuccess = success
                }
                if (isSuccess) {
                    this.$message.success('删除成功')
                    this.getList()
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        async UpSuccessload(item) {
            this.createFile = true
            await this.AjaxFile(item.file, 0, "");
        },
        async AjaxFile(file, i, batchnumber) {
            var name = file.name; //文件名
            var size = file.size; //总大小
            var shardSize = 15 * 1024 * 1024;//2m
            var shardCount = Math.ceil(size / shardSize); //总片数
            if (i >= shardCount) {
                return;
            }
            //计算每一片的起始与结束位置
            var start = i * shardSize;
            var end = Math.min(size, start + shardSize);
            //构造一个表单，FormData是HTML5新增的
            i = i + 1;
            var form = new FormData();
            form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分
            form.append("batchnumber", batchnumber);
            form.append("fileName", name);
            form.append("total", shardCount); //总片数
            form.append("index", i); //当前是第几片
            const res = await xMTVideoUploadBlockAsync(form);
            if (res?.success) {
                if (i == shardCount) {
                    this.file = res.data.url
                    this.createFile = false
                } else {
                    await this.AjaxFile(file, i, res.data);
                }
            } else {
                this.createFile = false
                this.$message({ message: res?.msg, type: "warning" });
            }
        },
        async sumbit() {
            //没有时间就提示
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            // const form = new FormData();
            // form.append("fileUrl", this.file);
            let form = {
                fileUrl: this.file
            }
            this.importLoading = true
            await ImportEvaluationCollectionAsync(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getEvaluationCollectionPage(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;
    align-items: center;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>
