<template>
  <MyContainer style="height: 98%;">
    <template #header>
      <div class="top">
        <div style="display: flex;">
          <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
            style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
          </el-date-picker>
          <el-select v-model="ListInfo.platform" placeholder="平台" @change="onchangeplatform" multiple collapse-tags
            clearable filterable style="width: 160px;margin-right: 5px;">
            <el-option v-for="item in platformdata" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-model="ListInfo.shopName" placeholder="店铺" clearable filterable class="publicCss"
            @visible-change="shopIncident">
            <el-option v-for="item in shopList" :key="item.shopName" :label="item.shopName" :value="item.shopName" />
          </el-select>
          <div class="publicCss">
            <inputYunhan ref="productCode" :inputt.sync="ListInfo.orderNoInner" v-model="ListInfo.orderNoInner"
              width="130px" placeholder="内部单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="200"
              :maxlength="4000" @callback="callbackGoodsCode" title="内部单号">
            </inputYunhan>
          </div>
          <div class="publicCss">
            <inputYunhan ref="productCode" :inputt.sync="ListInfo.orderNo" v-model="ListInfo.orderNo" width="130px"
              placeholder="线上单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="200" :maxlength="4000"
              @callback="productCodeCallback" title="线上单号">
            </inputYunhan>
          </div>
          <el-select v-model="ListInfo.expressCompany" placeholder="快递公司" clearable filterable remote
            :remote-method="getprosimstatelist" @change="oncorporation" class="publicCss">
            <el-option v-for="item in expresscompanylist" :key="item" :label="item" :value="item" />
          </el-select>
          <el-select v-model="ListInfo.status" placeholder="状态" style="width: 160px;margin-right: 5px;" clearable
            multiple collapse-tags filterable>
            <el-option key="待修改" label="待修改" value="待修改" />
            <el-option key="修改成功" label="修改成功" value="修改成功" />
            <el-option key="修改失败" label="修改失败" value="修改失败" />
          </el-select>
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" @click="startImport"
            v-if="checkPermission('Api:Order:OrderReplaceWaybillNo:ImportOrderReplaceWaybillNo')">导入</el-button>
          <el-button type="primary" @click="exportProps"
            v-if="checkPermission('Api:Order:OrderReplaceWaybillNo:ExportOrderReplaceWaybillNo')">导出</el-button>
          <el-button style="margin-left: 10px;" type="text">
            最近更新时间:
            {{ syncCreateTime }}
          </el-button>
        </div>
      </div>
    </template>
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions, platformlist } from '@/utils/tools'
import inputYunhan from "@/components/Comm/inputYunhan";
import { getList } from '@/api/operatemanage/base/shop'
import { getLatestSyncTime, importOrderReplaceWaybillNo, getExpressCompanyList, getOrderReplaceWaybillNoPage, exportOrderReplaceWaybillNo } from '@/api/order/orderReplaceWaybillNo';
import dayjs from 'dayjs'
const tableCols = [
  { istrue: true, sortable: 'custom', width: 'auto', align: 'center', prop: 'platformStr', label: '平台', },
  { istrue: true, sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
  { istrue: true, sortable: 'custom', width: 'auto', align: 'center', prop: 'expressCompany', label: '快递公司', },
  { istrue: true, sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNoInner', label: '内部单号', type: 'orderLogInfo', orderType: 'orderNoInner' },
  { istrue: true, sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '线上单号', },
  { istrue: true, sortable: 'custom', width: 'auto', align: 'center', prop: 'waybillNo', label: '运单号', },
  { istrue: true, sortable: 'custom', width: 'auto', align: 'center', prop: 'syncTime', label: '同步时间', },
  { istrue: true, sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '状态', },
]
export default {
  name: "txTrackingNumber",
  components: {
    MyContainer, vxetablebase, inputYunhan
  },
  data() {
    return {
      dialogVisible: false,//导入弹窗
      uploadLoading: false,//上传loading
      fileList: [],//上传文件列表
      fileparm: {},//上传参数
      timer: null,//定时器
      expresscompanylist: [],//快递公司列表
      platformdata: [],//平台列表
      platformlist,//平台列表
      shopList: [],//店铺列表
      syncCreateTime: '',//最近更新时间
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        platform: [],//平台
        shopName: null,//店铺
        orderNoInner: null,//内部单号
        orderNo: null,//线上单号
        expressCompany: null,//快递公司
        status: [],//状态
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.init()
    await this.lastUpdateTime()
    await this.getList()
  },
  methods: {
    //快递公司为空,则初始化快递公司列表
    oncorporation(e) {
      if (!e) {
        this.ListInfo.expressCompany = null
        this.getprosimstatelist('')
      }
    },
    //店铺选项数据为空则提示
    shopIncident(e) {
      if (e && e == true && this.shopList.length == 0) {
        this.$message.warning('请先选择平台')
      }
    },
    //获取最近更新时间
    async lastUpdateTime() {
      const { data, success } = await getLatestSyncTime()
      if (success) {
        this.syncCreateTime = data
      }
    },
    //内部单号
    callbackGoodsCode(val) {
      this.ListInfo.orderNoInner = val
    },
    //线上单号
    productCodeCallback(val) {
      this.ListInfo.orderNo = val
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    //上传文件改变
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importOrderReplaceWaybillNo(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
      await this.lastUpdateTime()//获取最近更新时间
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    //模糊获取快递公司数据
    async getprosimstatelist(value) {
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.expresscompanylist = []
        getExpressCompanyList({ keyword: value })
          .then(res => {
            if (res.success) {
              this.expresscompanylist = res.data;
            }
          })
          .catch(err => {
          });
      }, 500);
    },
    //平台改变
    async onchangeplatform(val) {
      if (val.length == 0) {
        this.shopList = []
        this.ListInfo.shopName = null
        return
      }
      //获取店铺信息
      val.forEach(item => {
        if (this.shopList.length > 0) {
          const isItemInShopList = this.shopList.some(shop => shop.platform === item);
          if (!isItemInShopList) {
            this.storeRequestData(item);
          }
        } else {
          this.storeRequestData(item);
        }
      });
      //删除this.shopList中val中没有的
      this.shopList = this.shopList.filter(shop => val.includes(shop.platform));
      //判断是否有选中的店铺
      if (this.shopList.length > 0) {
        const isItemInShopList = this.shopList.some(shop => shop.shopName === this.ListInfo.shopName);
        if (!isItemInShopList) {
          this.ListInfo.shopName = null
        }
      }
    },
    //获取店铺信息
    async storeRequestData(val) {
      const { data, success } = await getList({ platform: val, CurrentPage: 1, PageSize: 100000 });
      if (!success) return
      this.shopList = this.shopList.concat(data.list)
    },
    //初始化
    async init() {
      this.platformdata = this.platformlist.filter(item => item.label == '淘宝' || item.label == '天猫' || item.label == '淘工厂')
      const { data, success } = await getExpressCompanyList({ keyword: '' })
      if (success) {
        this.expresscompanylist = data
      }
    },
    //时间改变
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    //导出
    async exportProps() {
      this.loading = true
      const { data } = await exportOrderReplaceWaybillNo(this.ListInfo)
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
      this.loading = false
    },
    //获取列表
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给前1天时间
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await getOrderReplaceWaybillNoPage(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.loading = false
        await this.lastUpdateTime()//获取最近更新时间
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 130px;
    margin-right: 5px;
  }
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
