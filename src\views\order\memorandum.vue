<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 90%;">
            <el-tab-pane label="当前" name="first" style="height: 100%;" lazy>
                <memorandumCurrent :styleCode="styleCode" @close="close"/>
            </el-tab-pane>
            <el-tab-pane label="历史" name="second" style="height: 100%;" lazy>
                <memorandumHistory :styleCode="styleCode" @close="close"/>
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from '@/components/my-container'
import memorandumCurrent from './memorandumCurrent.vue'
import memorandumHistory from './memorandumHistory.vue'
export default {
    name: 'procodesimilarity',
    components: {
        MyContainer, memorandumHistory, memorandumCurrent
    },
    props:{
        styleCode:{
            type:String,
            default:''
        }
    },
    data() {
        return {
            activeName: 'first'
        }
    },
    mounted() {

    },
    methods: {
        close(){
            this.$emit('close')
        }
    }
}
</script>