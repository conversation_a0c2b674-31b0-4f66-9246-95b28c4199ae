<template>
    <MyContainer>
        <template #header style="height: 90vh;">
            <div class="top">
                <el-date-picker v-model="payTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="原付款日期" end-placeholder="原付款日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" @change="changeTime($event, 'one')"
                    value-format="yyyy-MM-dd">
                </el-date-picker>
                <el-date-picker v-model="shipmentsTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="原发货日期" end-placeholder="原发货日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" @change="changeTime($event, 'two')"
                    value-format="yyyy-MM-dd">
                </el-date-picker>
                <el-date-picker v-model="codeTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="扫码时间" end-placeholder="扫码时间" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" @change="changeTime($event, 'three')"
                    value-format="yyyy-MM-dd">
                </el-date-picker>
                <el-select v-model="ListInfo.platform" placeholder="平台" class="publicCss" clearable>
                    <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.types" placeholder="类型" class="publicCss" clearable multiple collapse-tags>
                    <el-option v-for="item in type" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.isPrint" placeholder="面单打印" class="publicCss" clearable>
                    <el-option label="是" :value="true" />
                </el-select>
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable>
                    <el-option v-for="item in status" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-input v-model="ListInfo.expressNo" placeholder="商品编码/快递单号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model="ListInfo.orderNoInner" placeholder="原内部订单号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model="ListInfo.newOrderNoInner" placeholder="新内部原订单号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.scanUserName" placeholder="扫码人" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model="ListInfo.printerName" placeholder="贴单扫码人" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.skus" placeholder="Skus" maxlength="100" clearable class="publicCss" />
                <el-input v-model.trim="ListInfo.newSkus" placeholder="新Skus" maxlength="100" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.proCodes" placeholder="宝贝ID" clearable class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                    <el-button type="primary" @click="onSettings">设置</el-button>
                    <el-button type="primary" @click="importProps(1)">京东售后导入</el-button>
                    <el-button type="primary" @click="importProps(2)">京东编码映射导入</el-button>
                </div>
            </div>
        </template>
        <vxetablebase :id="'scanCodeDetails202408041753'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' :border="true" @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" style="width: 100%; height: 650px; margin: 0"
            v-loading="loading" />
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            v-dialogDrag>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNoInner="orderNoInner"
                style="z-index:10000;height:600px" />
        </el-dialog>

        <el-dialog title="设置" :visible.sync="settingsVisible" width="25%" v-dialogDrag>
            <detailSettings ref="detailSettings" v-if="settingsVisible" @resetFormCallback="settingsVisible = false" />
        </el-dialog>

        <el-dialog :title="importType == 1 ? '京东售后导入' : '京东编码映射导入'" :visible.sync="importVisible" width="30%"
            v-dialogDrag v-loading="importLoading">
            <div slot="title" class="header-title" v-if="importType == 2">
                <span class="title-text"><span>导入数据</span></span>
                <span class="title-close"><el-button @click="downLoadFile">下载模版</el-button></span>
            </div>
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { platformlist, pickerOptions, formatPlatform } from '@/utils/tools'
import { pageGetScanRecord, exportScanRecord, ImportJdAfters, ImportJdCodeMappers } from '@/api/vo/ReturnOrderScan'
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
import detailSettings from "@/views/order/documentReview/component/detailSettings.vue";
import dayjs from 'dayjs'
import { download } from "@/utils/download";
import middlevue from "@/store/middle.js"
const status = [
    { label: '无法识别', value: 0 },
    { label: '匹配成功', value: 1 },
    { label: '待再匹配', value: 2 },
    { label: '拆包', value: 3 },
    { label: '丢弃', value: 4 },
]
const type = [
    {
        label: '退件',
        value: 0
    },
    {
        label: '商品编码',
        value: 1
    },
    {
        label: '售后',
        value: 2
    }
]
const tableCols = [
    { istrue: true, sortable: 'custom', width: 'auto', label: '状态', width: '80px', prop: 'status', formatter: (row) => row.status ? status.find(item => item.value == row.status).label : null },
    { istrue: true, sortable: 'custom', width: 'auto', align: 'center', label: '类型', prop: 'type', width: '60', formatter: (row) => row.type !== null ? type.find(item => item.value == row.type).label : null },
    { istrue: true, sortable: 'custom', width: 'auto', align: 'left', label: '快递单号/商品编码', prop: 'expressNo', width: '120', },
    { istrue: true, sortable: 'custom', width: 'auto', align: 'left', label: '宝贝ID', prop: 'proCode', width: '120', },
    { istrue: true, sortable: 'custom', width: 'auto', align: 'right', label: '销售成本', prop: 'saleCost', width: '80', },
    { istrue: true, sortable: 'custom', width: 'auto', align: 'center', label: '原内部订单号', prop: 'orderNoInner', width: '100', type: 'orderLogInfo', orderType: 'orderNoInner' },
    { istrue: true, sortable: 'custom', width: 'auto', align: 'center', label: '平台', prop: 'platform', width: '80', formatter: (row) => row.platform ? formatPlatform(row.platform) : '' },
    { istrue: true, sortable: 'custom', width: 'auto', align: 'left', label: '店铺', prop: 'shopName', width: '150', },
    { istrue: true, sortable: 'custom', width: 'auto', align: 'left', label: '原付款日期', prop: 'payTime', width: 'auto', formatter: (row) => row.payTime ? dayjs(row.payTime).format('YYYY-MM-DD HH:mm:ss') : '' },
    { istrue: true, sortable: 'custom', width: 'auto', align: 'left', label: '客户发货日期', prop: 'applyTime', width: 'auto', formatter: (row) => row.applyTime ? dayjs(row.applyTime).format('YYYY-MM-DD HH:mm:ss') : '' },
    { istrue: true, sortable: 'custom', width: 'auto', align: 'left', label: '原SKUS', prop: 'skus', width: '80', },
    { istrue: true, sortable: 'custom', width: 'auto', label: '近七日销量', prop: 'orderCount7Days', width: '80', },
    { istrue: true, sortable: 'custom', width: 'auto', label: '新内部订单号', align: 'center', prop: 'newOrderNoInner', width: '80', type: 'orderLogInfo', orderType: 'orderNoInner' },
    { istrue: true, sortable: 'custom', width: 'auto', align: 'left', label: '新SKUS', prop: 'newSkus', width: '80' },
    { istrue: true, sortable: 'custom', width: 'auto', align: 'left', label: '扫码时间', prop: 'scanTime', width: 'auto', formatter: (row) => row.scanTime ? dayjs(row.scanTime).format('YYYY-MM-DD HH:mm:ss') : '' },
    { istrue: true, sortable: 'custom', width: 'auto', label: '扫码人', prop: 'scanUserName', width: '80', },
    { istrue: true, sortable: 'custom', width: 'auto', label: '贴单扫码人', prop: 'printerName', width: '100', },
    { istrue: true, sortable: 'custom', width: 'auto', label: '面单打印', prop: 'isPrint', width: '100', formatter: (row) => { return row.isPrint ? '是' : '否' } }
]
export default {
    name: "scanCodeDetails",
    components: {
        MyContainer, vxetablebase, OrderActionsByInnerNos, detailSettings
    },
    data() {
        return {
            settingsVisible: false,
            importVisible: false,
            type,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                status: null,//状态
                expressNo: null,//退件快递单号
                orderNoInner: null,//原内部订单号
                newOrderNoInner: null,//新内部原订单号
                payStartDate: null,//原付款日期
                payEndDate: null,//原付款日期
                applyStartDate: null,//客户发货日期
                applyEndDate: null,//客户发货日期
                scanStartDate: null,//扫码时间
                scanEndDate: null,//扫码时间
                platform: null,//平台
                shopName: null,//店铺
                scanUserName: null,//扫码人
                isPrint: null,//面单打印
                printerName: null,//贴单扫码人
                skus: null,//原SKUS
                newSkus: null,//新SKUS
                types: [],//类型
            },
            tableCols,//表格头
            tableData: [],//表格数据
            platformlist,//平台
            loading: false,//加载中
            payTimeRanges: [],//付款时间
            shipmentsTimeRanges: [],//发货时间
            codeTimeRanges: [],//扫码时间
            pickerOptions,//时间选择器配置
            total: 0,//总条数
            status,//状态
            dialogHisVisible: false,//订单日志信息弹窗
            orderNoInner: null,//订单号
            isExport: false,
            fileList: [],
            importLoading: false,
            importVisible: false,
            file: null,
            importType: null,
        }
    },
    async mounted() {
        await this.getList()
        await middlevue.$on('queryDetailsList', (e) => {
            this.ListInfo.scanUserName = e.scaner
            this.ListInfo.printerName = e.printer
            this.ListInfo.scanStartDate = e.startDate
            this.ListInfo.scanEndDate = e.endDate
            this.ListInfo.status = e.status
            this.codeTimeRanges = [e.startDate, e.endDate]
            this.getList()
        })
    },
    //销毁
    beforeDestroy() {
        middlevue.$off('queryDetailsList')
    },
    methods: {
        downLoadFile() {
            window.open("/static/excel/京东自营商品编码和聚水潭编码对应表.xlsx", "_self");
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            //没有时间就提示
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("file", this.file);
            this.importLoading = true
            if (this.importType == 1) {
                await ImportJdAfters(form).then(({ success }) => {
                    if (success) {
                        this.$message.success('导入成功')
                        this.importVisible = false
                        this.getList()
                    }
                    this.importLoading = false
                }).catch(err => {
                    this.importLoading = false
                    this.$message.error('导入失败')
                })
            } else {
                await ImportJdCodeMappers(form).then(({ success }) => {
                    if (success) {
                        this.$message.success('导入成功')
                        this.importVisible = false
                        this.getList()
                    }
                    this.importLoading = false
                }).catch(err => {
                    this.importLoading = false
                    this.$message.error('导入失败')
                })
            }
        },
        importProps(type) {
            this.importType = type
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        onSettings() {
            this.settingsVisible = true
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await exportScanRecord(this.ListInfo).then(download).finally(() => {
                this.isExport = false
            })
        },
        async changeTime(e, type) {
            if (type == 'one') {
                this.ListInfo.payStartDate = e ? e[0] : null
                this.ListInfo.payEndDate = e ? e[1] : null
            } else if (type == 'two') {
                this.ListInfo.applyStartDate = e ? e[0] : null
                this.ListInfo.applyEndDate = e ? e[1] : null
            } else if (type == 'three') {
                this.ListInfo.scanStartDate = e ? e[0] : null
                this.ListInfo.scanEndDate = e ? e[1] : null
            }
            await this.getList()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
            }
            const replaceArr = ['expressNo', 'orderNoInner', 'newOrderNoInner', 'shopName', 'scanUserName', 'skus', 'newSkus']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            const { data, success } = await pageGetScanRecord(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;

    .publicCss {
        width: 150px;
        margin-right: 5px;
        margin-bottom: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}

.header-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px 0 0;

    .title-text {
        display: flex;
        align-items: center;

        .title-close {
            margin-left: 10px;
        }
    }
}
</style>
