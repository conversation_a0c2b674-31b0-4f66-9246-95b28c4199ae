<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRangesFq" type="daterange" unlink-panels range-separator="至"
          start-placeholder="发起开始时间" end-placeholder="发起结束时间" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime($event, 1)">
        </el-date-picker>
        <el-date-picker v-model="timeRangesQj" type="daterange" unlink-panels range-separator="至"
          start-placeholder="请假开始时间" end-placeholder="请假结束时间" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime($event, 2)">
        </el-date-picker>
        <el-select v-model="ListInfo.processingStatus" placeholder="处理状态" class="publicCss" clearable filterable>
          <el-option label="可处理" value='可处理' />
          <el-option label="已处理" value='已处理' />
        </el-select>
        <el-input v-model.trim="ListInfo.initiatorUserName" placeholder="发起人" maxlength="50" clearable
          class="publicCss" />
        <el-input v-model.trim="ListInfo.region" placeholder="区域" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.department" placeholder="部门" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.jobLevel" placeholder="职级" maxlength="50" clearable class="publicCss" />
        <el-select v-model="ListInfo.dataStatus" placeholder="数据状态" class="publicCss" clearable filterable>
          <el-option v-for="(item, index) in approvalStatus" :key="index" :label="item" :value="item" />
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
      </div>
      <div class="top">
        <el-input-number v-model="ListInfo.leaveDuration" placeholder="请假时长(天/小时)" :precision="2" :min="0" :max="9999"
          controls-position="right" :controls="false" class="publicCss" />
        <el-input v-model.trim="ListInfo.currentApproverUserName" placeholder="当前审批人" maxlength="50" clearable
          class="publicCss" />
        <el-select v-model="ListInfo.isAdministrativeApproval" placeholder="是否已到行政审批" class="publicCss" clearable
          filterable>
          <el-option label="是" :value='1' />
          <el-option label="否" :value='0' />
        </el-select>
        <el-input v-model.trim="ListInfo.operator" placeholder="操作人" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.abnormalReason" placeholder="异常原因" maxlength="50" clearable
          class="publicCss" />
        <el-input v-model.trim="ListInfo.operationResult" placeholder="操作结果" maxlength="50" clearable
          class="publicCss" />
        <el-button type="primary" :loading="pullLoading" @click="onSynchronizeData()">
          {{ pullLoading ? '同步中...' : '同步请休假数据' }}
        </el-button>
        <el-button type="primary" @click="onBatchOperation('agree')">批量审批通过</el-button>
        <el-button type="primary" @click="onBatchOperation('refuse')">批量审批拒绝</el-button>
      </div>
    </template>
    <vxetablebase :id="'askForLeaveAutomationIndex202507261142'" :tablekey="'askForLeaveAutomationIndex202507261142'"
      ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'" :border="true" @select="selectchange"
      @checkbox-range-end="selectchange">
      <template #dataStatus="{ row }">
        <span :style="{
          color: row.dataStatus == '审批通过' ? 'green' : (row.dataStatus == '异常' ? 'red' : 'inherit'),
          textAlign: 'center'
        }">
          {{ row.dataStatus }}
        </span>
      </template>
      <template #abnormalReason="{ row }">
        <span :style="{ textAlign: 'center', color: 'red' }">
          {{ row.abnormalReason }}
        </span>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { leaveApprovalPage, synchronizeLeaveAndVacationData, batchApprovalOfLeaveApplicationData } from '@/api/people/peoplessc.js';
import dayjs from 'dayjs'
const approvalStatus = ['异常', '审批中', '审批通过', '审批拒绝', '已撤销']
const tableCols = [
  { istrue: true, width: '60', type: "checkbox" },
  { sortable: 'custom', width: '80', align: 'center', prop: 'processingStatus', label: '处理状态' },
  { sortable: 'custom', width: '90', align: 'center', prop: 'initiatorUserName', label: '发起人' },
  { sortable: 'custom', width: '80', align: 'center', prop: 'region', label: '区域' },
  { sortable: 'custom', width: '80', align: 'center', prop: 'department', label: '部门' },
  { sortable: 'custom', width: '80', align: 'center', prop: 'jobLevel', label: '职级' },
  { sortable: 'custom', width: '130', align: 'center', prop: 'initiationTime', label: '发起时间', formatter: (row, that) => that.formatDateTime(row.initiationTime) },
  { sortable: 'custom', width: '80', align: 'center', prop: 'dataStatus', label: '数据状态' },
  { sortable: 'custom', width: '130', align: 'center', prop: 'leaveStartTime', label: '请假开始时间', formatter: (row, that) => that.formatDateTime(row.leaveStartTime) },
  { sortable: 'custom', width: '130', align: 'center', prop: 'leaveEndTime', label: '请假结束时间', formatter: (row, that) => that.formatDateTime(row.leaveEndTime) },
  { sortable: 'custom', width: '80', align: 'center', prop: 'leaveDuration', label: '请假时长' },
  { sortable: 'custom', width: '130', align: 'center', prop: 'leaveType', label: '请假类型' },
  { sortable: 'custom', width: '130', align: 'center', prop: 'currentApproverUserName', label: '当前审批人' },
  { sortable: 'custom', width: '130', align: 'center', prop: 'latestSyncTime', label: '最新同步时间', formatter: (row, that) => that.formatDateTime(row.latestSyncTime) },
  { sortable: 'custom', width: '140', align: 'center', prop: 'abnormalReason', label: '异常原因' },
  { sortable: 'custom', width: '80', align: 'center', prop: 'operator', label: '操作人' },
  { sortable: 'custom', width: '130', align: 'center', prop: 'operationTime', label: '操作时间', formatter: (row, that) => that.formatDateTime(row.operationTime) },
  { sortable: 'custom', width: '80', align: 'center', prop: 'operationType', label: '操作类型' },
  { sortable: 'custom', width: '80', align: 'center', prop: 'operationResult', label: '操作结果' },
  { width: '140', align: 'center', prop: 'describe', label: '说明	' },
  { width: '80', align: 'left', prop: 'picture', label: '图片', type: 'images' },
  { width: '80', align: 'left', prop: 'attachment', label: '附件' },
]
export default {
  name: "askForLeaveAutomationIndex",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      pullLoading: false,
      approvalStatus,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'initiationTime',
        isAsc: false,
        initiationStartTime: null,//开始时间
        initiationEndTime: null,//结束时间
        leaveStartTime: null,//请假开始时间
        leaveEndTime: null,//请假结束时间
        dataStatus: null,//数据状态
        region: null,//区域
        department: null,//部门
        jobLevel: null,//职级
        leaveDuration: undefined,//请假时长
        initiatorUserName: null,//发起人
        currentApproverUserName: null,//当前审批人
        isAdministrativeApproval: null,//是否已到行政审批
        operator: null,//操作人
        abnormalReason: null,//异常原因
        operationResult: null,//操作结果
      },
      timeRangesFq: [],
      timeRangesQj: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      checkboxList: [],
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    onBatchOperation(type) {
      if (this.checkboxList.length == 0) {
        this.$message.error('请选择要操作的数据')
        return
      }
      if (!this.checkboxList.every(item => item.processingStatus == '可处理')) {
        this.$message.error('只能操作可处理的数据')
        return
      }
      this.$confirm(`是否${type == 'agree' ? '通过' : '拒绝'}选中的数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.loading = true
        const res = await batchApprovalOfLeaveApplicationData({
          ids: this.checkboxList.map(item => item.id),
          approvalType: type,
        })
        this.loading = false
        if (res?.success) {
          this.$message.success('操作成功')
          await this.getList()
        }
      }).catch(() => {
      });
    },
    onSynchronizeData() {
      this.$confirm('是否同步请休假数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.pullLoading = true
        const res = await synchronizeLeaveAndVacationData()
        this.pullLoading = false
        if (res?.success) {
          this.$message.success(res.data || res.msg || '同步成功')
          await this.getList()
        } else {
          this.$message.error(res.data || res.msg || '同步失败')
        }
      }).catch(() => {
      });
    },
    formatDateTime(dateTimeString) {
      return dateTimeString ? dayjs(dateTimeString).format('YYYY-MM-DD HH:mm:ss') : '';
    },
    selectchange(val) {
      this.checkboxList = val;
    },
    async changeTime(e, label) {
      if (label == 1) {
        this.ListInfo.initiationStartTime = e ? e[0] : null
        this.ListInfo.initiationEndTime = e ? e[1] : null
      } else if (label == 2) {
        this.ListInfo.leaveStartTime = e ? e[0] : null
        this.ListInfo.leaveEndTime = e ? e[1] : null
      }
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRangesFq && this.timeRangesFq.length == 0) {
        this.ListInfo.initiationStartTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.initiationEndTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRangesFq = [this.ListInfo.initiationStartTime, this.ListInfo.initiationEndTime]
      }
      this.loading = true
      const { data, success } = await leaveApprovalPage(this.ListInfo)
      this.loading = false
      this.checkboxList = []
      if (success) {
        function transformPictureFormat(pictureString) {
          if (!pictureString) return '[]';
          try {
            const urls = JSON.parse(pictureString);
            const transformed = urls.map(url => ({ url }));
            return JSON.stringify(transformed);
          } catch (error) {
            console.error('解析picture字段失败:', error);
            return '[]';
          }
        }
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.leaveDuration = item.leaveDuration ? item.leaveDuration + item.unit || '' : ''
          item.picture = transformPictureFormat(item.picture);
        })
        this.total = data.total
        this.summaryarry = data.summary
      } else {
        this.$message.error('获取列表失败')
      }
    },
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 160px;
    margin-right: 5px;
  }
}
</style>
