<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss" />
                <el-select v-model="ListInfo.isStock" placeholder="是否无法识别" class="publicCss" clearable>
                    <el-option label="是" :value="0" />
                    <el-option label="否" :value="1" />
                </el-select>
                <el-input v-model.trim="ListInfo.orderNo" placeholder="商品编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.orderNo" placeholder="货号" maxlength="50" clearable class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="120">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <el-button type="text" @click="handleEdit">编辑</el-button>
                            <el-button type="text" @click="handleDel">删除</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="编辑" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
            <el-table :data="tableData" style="width: 100%">
                <el-table-column prop="date" label="半成品编码" width="180">
                </el-table-column>
                <el-table-column prop="name" label="半成品名称" width="180">
                </el-table-column>
                <el-table-column prop="address" label="半成品成本">
                    <template #default="{ row }">
                        <el-input-number v-model="row.num" :min="0" :max="999" placeholder="货半成品成本号" :controls="false"
                            :precision="4" />
                    </template>
                </el-table-column>
                <el-table-column prop="address" label="所需数量">
                    <template #default="{ row }">
                        <el-input-number v-model="row.num" :min="0" :max="999" placeholder="货半成品成本号" :controls="false"
                            :precision="4" />
                    </template>
                </el-table-column>
            </el-table>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
            </span>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '成品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNoInner', label: '成品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'importTime', label: '成品成本', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'planSendTime', label: '半成品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'timePay', label: '半成品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight', label: '半成品成本', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderStatus', label: '所需数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'seriesName', label: '状态', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            dialogVisible: false,
            tableData1: []
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await ExportExpressInterceptList(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '快递拦截明细' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await pageGetVoOrder(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
