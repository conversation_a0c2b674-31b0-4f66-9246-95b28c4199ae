<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <!-- <el-date-picker v-model="exportTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="导入开始日期" end-placeholder="导入结束日期" :picker-options="pickerOptions" :clearable="false"
                    style="width: 250px;margin-right: 10px;" @change="changeTime($event, 'export')">
                </el-date-picker> -->
                <el-date-picker v-model="paymentTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="付款开始日期" end-placeholder="付款结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" @change="changeTime($event, 'payment')">
                </el-date-picker>
                <el-date-picker v-model="computeTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="计算开始日期" end-placeholder="计算结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" @change="changeTime($event, 'compute')">
                </el-date-picker>
                <el-input v-model="ListInfo.orderNo" placeholder="线上订单号" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.orderNoInner" placeholder="内部订单号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model="ListInfo.batchNo" placeholder="导入批次号" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.seriesName" placeholder="系列编码" maxlength="50" clearable class="publicCss" />
                <el-select v-model="ListInfo.orderStatus" placeholder="计算后订单状态" class="publicCss" clearable>
                    <el-option :key="1" label="普通发货" :value="'普通发货'" />
                    <el-option :key="2" label="调拨发货" :value="'调拨发货'" />
                    <el-option :key="4" label="真实缺货" :value="'真实缺货'" />
                </el-select>
                <el-select v-model="ListInfo.sendWmsCoId" clearable filterable :collapse-tags="true" placeholder="请选择仓库"
                    style="width: 130px" class="publicCss">
                    <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                        :value="item.wms_co_id" />
                </el-select>
                <el-button type="primary" @click="getList('click')">搜索</el-button>
            </div>
        </template>
        <vxetablebase :id="'synchronous202408041756'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" v-loading="loading" style="width: 100%; height: 680px; margin: 0" />
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />

        <el-dialog title="订单商品信息" :visible.sync="goodsVisible" width="50%" v-dialogDrag>
            <orderGoodInfo :orderNo="orderNo" :orderNoInner="orderNoInner" v-if="goodsVisible" />
        </el-dialog>

        <el-dialog title="日志" :visible.sync="logListVisible" width="40%" v-dialogDrag>
            <logList :orderNo="orderNo" :orderNoInner="orderNoInner" v-if="logListVisible" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { replaceSpace } from '@/utils/getCols'
import dayjs from 'dayjs'
import { pickerOptions, } from '@/utils/tools'
import { pageGetVoOrder } from '@/api/vo/VerifyOrder'
import { getAllWarehouse } from '@/api/inventory/warehouse'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import orderGoodInfo from './orderGoodInfo.vue'
import logList from './logList.vue'

const tableCols = [
    { istrue: true, prop: 'orderNo', label: '线上订单号', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'orderNoInner', label: '内部订单号', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'timePay', label: '付款日期', sortable: 'custom', width: '130', formatter: (row) => dayjs(row.timePay).format('YYYY-MM-DD HH:mm') },
    { istrue: true, prop: 'planSendTime', label: '计划发货', sortable: 'custom', width: '130', formatter: (row) => row.planSendTime ? dayjs(row.planSendTime).format('YYYY-MM-DD HH:mm') : '' },
    { istrue: true, prop: 'importTime', label: '导入时间', sortable: 'custom', width: '130', formatter: (row) => dayjs(row.importTime).format('YYYY-MM-DD HH:mm') },
    { istrue: true, prop: 'cptCompleteTime', label: '计算时间', sortable: 'custom', width: '130', formatter: (row) => dayjs(row.cptCompleteTime).format('YYYY-MM-DD HH:mm') },
    { istrue: true, prop: 'syncRpaTime', label: '同步聚水潭时间', sortable: 'custom', width: '130', formatter: (row) => dayjs(row.syncRpaTime).format('YYYY-MM-DD HH:mm') },
    { istrue: true, prop: 'seriesName', label: '系列编码', sortable: 'custom', width: '130', align: 'left', },
    { istrue: true, prop: 'weight', label: '重量', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'goodsCount', align: 'center', label: '编码数量', sortable: 'custom', width: '90', type: 'click', handle: (that, row) => that.openGoodDialog(row) },
    { istrue: true, prop: 'totalQuantity', align: 'center', label: '数量', sortable: 'custom', width: '70', type: 'click', handle: (that, row) => that.openGoodDialog(row) },
    { istrue: true, prop: 'orderStatus', label: '订单状态', sortable: 'custom', width: 'auto', type: 'html', formatter: (row) => { return "<span style='color:red'>" + row.orderStatus + "</span>" } },
    { istrue: true, prop: 'sendWmsCoId', align: 'left', label: '仓库', sortable: 'custom', width: '200', formatter: (row) => row.sendWmsName },
    {
        istrue: true, label: '', width: '70', type: 'button', btnList:
            [
                { istrue: true, label: '日志', handle: (that, row) => that.openLog(row) },
            ]
    },
]

export default {
    name: "synchronousVue",
    components: {
        MyContainer, vxetablebase, orderGoodInfo, logList
    },
    data() {
        return {
            value: '',
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                isMain: true,
                seriesName: null,//系列编码
                orderNo: null,//线上订单号
                finalStatus: 2,//计算后状态
                startDate: null,//导入时间
                endDate: null,//导入时间
                batchNo: null,//导入批次号
                payStartDate: null,//付款时间
                payEndDate: null,//付款时间
                cptStartDate: null,//计算时间
                cptEndDate: null,//计算时间
                orderStatus: null,//计算后状态
                allotStatus: null,//是否需要调拨
                sendWmsCoId: null,//仓库
                orderNoInner: null,//内部订单号
            },
            tableCols,
            tableData: [],
            pickerOptions,
            exportTimeRanges: [],
            paymentTimeRanges: [],
            computeTimeRanges: [],
            total: 0,
            loading: true,
            warehouselist: [],
            orderNo: null,
            goodsVisible: false,
            logListVisible: false,
            orderNoInner: null,
        };
    },
    async mounted() {
        this.getList()
        var res3 = await getAllWarehouse();
        var warehouselist1 = res3.data.filter((x) => x.name.indexOf('代发') < 0);
        warehouselist1.unshift({ name: "全仓", co_id: 10361546, wms_co_id: 11793337, is_main: false, remark1: null, remark2: null, wms_co_id: -1 });
        this.warehouselist = warehouselist1;
    },
    methods: {
        openLog(row) {
            this.orderNo = row.orderNo;
            this.orderNoInner = row.orderNoInner;
            this.logListVisible = true;
        },
        openGoodDialog(row) {
            this.orderNoInner = row.orderNoInner;
            this.orderNo = row.orderNo
            this.goodsVisible = true
        },
        changeTime(e, type) {
            if (e) {
                if (type == 'export') {
                    this.ListInfo.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                    this.ListInfo.endDate = dayjs(e[1]).format('YYYY-MM-DD')
                } else if (type == 'payment') {
                    this.ListInfo.payStartDate = dayjs(e[0]).format('YYYY-MM-DD')
                    this.ListInfo.payEndDate = dayjs(e[1]).format('YYYY-MM-DD')
                } else if (type == 'compute') {
                    this.ListInfo.cptStartDate = dayjs(e[0]).format('YYYY-MM-DD')
                    this.ListInfo.cptEndDate = dayjs(e[1]).format('YYYY-MM-DD')
                }
            } else {
                if (type == 'export') {
                    this.ListInfo.startDate = null
                    this.ListInfo.endDate = null
                } else if (type == 'payment') {
                    this.ListInfo.payStartDate = null
                    this.ListInfo.payEndDate = null
                } else if (type == 'compute') {
                    this.ListInfo.cptStartDate = null
                    this.ListInfo.cptEndDate = null
                }
            }

            this.getList()
        },
        //查询列表
        async getList(type) {
            if (type == 'click') {
                this.ListInfo.currentPage = 1
            }
            // if (this.exportTimeRanges.length == 0) {
            //     //默认给近7天时间
            //     this.exportTimeRanges = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
            //     this.ListInfo.startDate = this.exportTimeRanges[0]
            //     this.ListInfo.endDate = this.exportTimeRanges[1]
            // }
            if (this.paymentTimeRanges.length == 0) {
                //默认给近7天时间
                this.paymentTimeRanges = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
                this.ListInfo.payStartDate = this.paymentTimeRanges[0]
                this.ListInfo.payEndDate = this.paymentTimeRanges[1]
            }
            const replaceArr = ['orderNo', 'batchNo', 'seriesName', 'orderNoInner']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            const { data, success } = await pageGetVoOrder(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.top {
    display: flex;
    margin-bottom: 20px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>