<template>
  <div v-loading="overallLoading">
    <el-row>
      <el-col :span="24">
        <el-button :type="dimension == '000' ? 'success' : 'primary'" @click="handleMethod('000')">产品ID分析</el-button>
        <el-button :type="dimension == '003' ? 'success' : 'primary'" @click="handleMethod('003')">运营组分析</el-button>
        <el-button :type="dimension == '004' ? 'success' : 'primary'" @click="handleMethod('004')">系列编码分析</el-button>
        <el-button :type="dimension == '002' ? 'success' : 'primary'"
          @click="handleMethod('002')">系列编码+运营组分析</el-button>
        <el-button :type="dimension == '001' ? 'success' : 'primary'"
          @click="handleMethod('001')">系列编码+运营专员分析</el-button>
      </el-col>
      <el-col :span="24">
        <span style="text-align: center; font-size: 14px; color: red;">
          所有数据未做特殊说明，均以全平台数据维度展开分析
        </span>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8">
        <el-scrollbar style="height: 615px">
          <el-descriptions :column="6" border :labelStyle="{ minWidth: '70px' }" :contentStyle="{ minWidth: '40px' }">
            <template v-for="(item, index) in displayItems">
              <el-descriptions-item v-if="shouldShowItem(item)" :key="index"
                :label="item.label ? item.label : tableLabel()" :span="item.span || 6">
                <template v-if="item.type === 'image'">
                  <el-image class="userAvatar" :style="item.style" :src="getImageUrl(item)"
                    :preview-src-list="[getImageUrl(item)]" />
                </template>
                <template v-else-if="item.type === 'table'">
                  <vxe-table :data="displayedInfo[item.dataKey]" max-height="240" border style="width: 410px;"
                    :column-config="{ resizable: true }" :row-config="{ isHover: true }">
                    <vxe-column v-for="col in item.columns" :key="col.field" :type="col.type" :field="col.field"
                      :title="col.title" :width="col.width == 'auto' ? '' : col.width"
                      :show-header-overflow="col.showHeaderOverflow" :sortable="col.sortable"
                      :show-overflow="col.showOverflow" :show-footer-overflow="col.showFooterOverflow" min-width="60">
                      <template v-if="col.slot" #default="{ row }">
                        <div v-if="col.type == 'avatarImg'" style="
                            display: flex;
                            align-items: center;
                            justify-content: center;
                          ">
                          <el-image class="userAvatar" style="width: 40px; height: 40px" :src="`http://192.168.16.240:8001/api/Admin/User/GetUserAvatar?type=2&id=${row[col.ddInfo.prop]
                            }`" :preview-src-list="[
                              `http://192.168.16.240:8001/api/Admin/User/GetUserAvatar?type=2&id=${row[col.ddInfo.prop]
                              }`,
                            ]" />
                        </div>
                        <div v-else style="
                            display: flex;
                            align-items: center;
                            justify-content: center;
                          ">
                          <el-image class="userAvatar" style="width: 40px; height: 40px" :src="`${row[col.field]}`"
                            :preview-src-list="[
                              `${row[col.field]}`,
                            ]" />
                        </div>
                      </template>
                    </vxe-column>
                  </vxe-table>
                </template>
                <template v-else>
                  <div v-if="item.type == 'ddTalk'"
                    style="display: flex; align-items: center; justify-content: center;width: 100%;">
                    <el-image :src="ddLogo" style="border-radius:5px;" class="ddTalk_Css"
                      @click="startSession(displayInfo[item.ddInfo.prop], item.ddInfo.type)" />
                    {{ getDisplayValue(item) }}
                    <el-image class="userAvatar" :style="item.style" :src="getImageUrl(item)"
                      :preview-src-list="[getImageUrl(item)]" />
                  </div>
                  <div v-else>
                    {{ getDisplayValue(item) }}
                    <span v-if="item.symbol">{{ item.symbol }}</span>
                  </div>
                </template>
              </el-descriptions-item>
            </template>
            <!-- 分析内容 - 始终显示 -->
            <el-descriptions-item :span="6" label="分析内容">
              <div style="max-height: 300px; overflow: auto; white-space: pre-wrap;white-space: pre-line">
                {{ inputTextLast }}
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </el-scrollbar>
      </el-col>
      <el-col :span="16">
        <div class="ai-container">
          <!-- 顶部操作栏 -->
          <!-- <div class="header">
      <el-button
        type="primary"
        icon="el-icon-refresh"
        @click="clearChat"
        class="glass-btn"
      >清空对话</el-button>
    </div> -->

          <!-- 聊天区域 -->
          <div class="chat-area glassmorphism">
            <transition-group name="message-fade" tag="div" class="chat-history">
              <div v-for="(msg, index) in chatHistory" :key="index" class="message-bubble"
                :class="msg.type === 'user' ? 'user-bubble' : 'ai-bubble'">
                <div class="message-icon" v-if="msg.type !== 'user'">
                  <!-- <template v-if="msg.type === 'user'">
              <el-image
                :src="avatar"
                :preview-src-list="[avatar]"
                style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
              </el-image>
            </template> -->
                  <template>
                    <el-image :src="'https://nanc.yunhanmy.com:10010/media/video/20250407/1909135481253388289.jpg'"
                      :preview-src-list="[
                        'https://nanc.yunhanmy.com:10010/media/video/20250407/1909135481253388289.jpg',
                      ]" style="
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                        object-fit: cover;
                      ">
                    </el-image>
                  </template>
                </div>
                <div class="message-content" v-if="msg.type !== 'user'">
                  <!-- <div class="message-text">{{ msg.content }}</div> -->
                  <div style="max-height: 494px; overflow-y: auto">
                    <div v-html="renderMdText(msg.content)"></div>
                  </div>
                  <div class="message-time" v-loading="inLoading" element-loading-spinner="el-icon-loading">
                    {{ formatTime(msg.timestamp) }}
                  </div>
                </div>
              </div>
            </transition-group>
          </div>

          <!-- 输入区域 -->
          <!-- <div class="input-area glassmorphism">
      <el-input
        type="textarea"
        :rows="3"
        placeholder="输入您的问题..."
        v-model="inputText"
        @keyup.enter="sendMessage"
        class="input-field"
        resize="none"
      ></el-input>
      <el-button
        type="primary"
        class="send-btn gradient-btn"
        @click="sendMessage"
        :loading="isProcessing"
        size="small"
      >
        <span v-if="!isProcessing">发送</span>
        <i v-else class="el-icon-loading"></i>
      </el-button>
      <el-button
        type="primary"
        icon="el-icon-refresh"
        @click="clearChat"
        class="glass-btn"
      >清空对话</el-button>
    </div> -->
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getLoginInfo } from "@/api/admin/auth";
import { getProductDetailAI } from "@/api/bookkeeper/reportdayV2";
import MarkdownIt from "markdown-it";
import { PageReqLog } from "@/api/admin/login-log.js";
import { matchImg } from "@/utils/getCols";
import { getUserDingCode } from '@/api/admin/user'
import { toLogout } from '@/router'
const tableConfigs = [
  { label: "7日系列下链接单量前10明细", key: 'saleOrderCounttop10s' },
  { label: "7日系列下毛四利润率前10明细", key: "saleProfit4Ratetop10s" },
  { label: "7日系列下毛四利润率倒5明细", key: "saleProfit4Rate5s" }
].map(config => ({
  ...config,
  type: "table",
  dataKey: config.key,
  showWhen: "001",
  columns: [
    { type: 'seq', width: 40, title: '#' },
    { field: 'proCode', title: '商品ID', showHeaderOverflow: true, sortable: true, showOverflow: 'title', showFooterOverflow: true, width: 'auto' },
    { field: 'proCodeXuHao', title: '商品序号', showHeaderOverflow: true, sortable: true, showOverflow: 'title', showFooterOverflow: true, width: 'auto' },
  ]
}));
export default {
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      ddLogo: require('@/static/images/dingding.png'),
      overallLoading: false,
      dimension: null,
      displayInfo: {},
      displayedInfo: {},
      avatar: "", //用户头像
      inputText: "",
      inputTextLast: "",
      chatHistory: [],
      isProcessing: false,
      token: "",
      currentAiResponse: "",
      currentAiMessage: null,
      activeSessionId: "",
      inLoading: false,
      markdownRender: new MarkdownIt({
        html: true,
        linkify: true,
        typographer: true,
      }),
      pagedata: {},

      AiType000: [],
      AiType000_IsLoadAiData: true,
      displayItems: [
        { label: "运营组", key: "groupName", span: 2, type: 'ddTalk', ddInfo: { type: 1, prop: 'groupId', name: 'groupName' }, distinguish: true, style: { width: "40px", height: "40px" }, imageType: 1 },
        // {
        //   label: "头像",
        //   type: "image",
        //   key: "groupId",
        //   style: { width: "40px", height: "40px" },
        //   span: 1,
        //   imageType: 1,
        //   distinguish: true,
        // },
        { label: "运营专员", key: "operateSpecialUserName", span: 2, type: 'ddTalk', ddInfo: { type: 2, prop: 'operateSpecialUserId' }, distinguish: true, style: { width: "40px", height: "40px" }, imageType: 2 },
        // {
        //   label: "头像",
        //   type: "image",
        //   key: "operateSpecialUserId",
        //   style: { width: "40px", height: "40px" },
        //   span: 2,
        //   imageType: 2,
        //   distinguish: true,
        // },
        { label: "运营助理", key: "userName", span: 2 },
        { label: "系列编码", key: "styleCode", span: 3 },
        { label: "商品ID", key: "proCode", span: 3 },
        {
          label: "系列sku数",
          key: "styleSkuCount",
          showWhen: ["004", "002"],
          dataKey: "displayedInfo",
          span: 2,
        },
        {
          label: "图片",
          type: "image",
          key: 'skuimage',
          style: { width: "40px", height: "40px" },
          showWhen: ["004", "002", "001"],
          dataKey: "displayedInfo",
          distinguish: false,
          span: 2,
        },
        {
          label: "系列总库存",
          key: "styleSkuSumCount",
          showWhen: "004",
          dataKey: "displayedInfo",
          span: 2,
        },
        {
          label: "系列总库存",
          key: "styleCurrentStock",
          showWhen: "002",
          dataKey: "displayedInfo",
          span: 2,
        },
        {
          label: "系列产品数",
          key: "proCodeCount",
          showWhen: "004",
          dataKey: "displayedInfo",
          span: 6,
        },
        {
          label: "产品链接数",
          key: "styleProCodeCount",
          showWhen: "002",
          dataKey: "displayedInfo",
          span: 6,
        },
        {
          label: "系列产品数(运营组下)",
          key: "styleProCodeCount",
          showWhen: '001',
          dataKey: "displayedInfo",
          span: 2
        },
        {
          label: "系列sku数(运营组下)",
          key: "styleSkuCount",
          showWhen: '001',
          dataKey: "displayedInfo",
          span: 2
        },
        {
          label: "系列总库存",
          key: "styleCurrentStock",
          showWhen: '001',
          dataKey: "displayedInfo",
          span: 6
        },

        {
          label: "专员数",
          key: "operateSpecialUserCount",
          showWhen: '003',
          dataKey: "displayedInfo",
          span: 2
        },
        {
          label: "助理数",
          key: "userCount",
          showWhen: '003',
          dataKey: "displayedInfo",
          span: 2
        },
        {
          label: "经营商品系列数",
          key: "styleCodeCount",
          showWhen: '003',
          dataKey: "displayedInfo",
          span: 2
        },
        {
          label: "经营商品链接数",
          key: "proCodeCount",
          showWhen: '003',
          dataKey: "displayedInfo",
          span: 2
        },
        {
          label: "总商品系列数",
          key: "gsSumStyleCodeCount",
          showWhen: '003',
          dataKey: "displayedInfo",
          span: 2
        },
        {
          label: "总商品链接数",
          key: "gsSumProCodeCodeCount",
          showWhen: '003',
          dataKey: "displayedInfo",
          span: 2
        },

        {
          label: "SKU信息",
          type: "table",
          dataKey: "skuStockList",
          showWhen: "000",
          columns: [
            { type: 'seq', width: 40, title: '#' },
            { field: 'imageUrl', title: '图片', showHeaderOverflow: true, showOverflow: 'title', showFooterOverflow: true, slot: true, width: '60' },
            { field: 'sku', title: 'SKU', showHeaderOverflow: true, sortable: true, showOverflow: 'title', showFooterOverflow: true, width: '90' },
            { field: 'currentStock', title: '库存', showHeaderOverflow: true, sortable: true, showOverflow: 'title', showFooterOverflow: true, width: '60' },
            { field: 'aboutProCodeCount', title: '链接数', showHeaderOverflow: true, sortable: true, showOverflow: 'title', showFooterOverflow: true, width: '70' },
            { field: 'skuName', title: '名称', showHeaderOverflow: true, sortable: true, showOverflow: 'title', showFooterOverflow: true, width: 'auto' },
          ]
        },
        {
          label: '',
          type: 'table',
          dataKey: 'zhuanyuanzhulilists',
          showWhen: ['003', '001', '002'],
          span: 6,
          columns: [
            { type: 'seq', width: 50, title: '#' },
            { field: 'zhuanyuanorzhulixuhao', title: '序号', showHeaderOverflow: true, sortable: true, showOverflow: 'title', showFooterOverflow: true, width: 'auto' },
            // { field: 'zhuanyuanorzhuliID', title: '专员助理ID', showHeaderOverflow: true, sortable: true, showOverflow: 'title', showFooterOverflow: true, width: '115' },
            { field: 'zhuanyuanorzhuliName', title: '姓名', showHeaderOverflow: true, sortable: true, showOverflow: 'title', showFooterOverflow: true, width: 'auto' },
            { title: '头像', showHeaderOverflow: true, showOverflow: 'title', showFooterOverflow: true, width: 'auto', slot: true, type: 'avatarImg', ddInfo: { prop: 'zhuanyuanorzhuliID' } }
          ]
        },
        ...tableConfigs,
        // {
        //   label: "系列",
        //   key: "styleCode",
        //   showWhen: "000",
        //   dataKey: "AiType000",
        // },
        // {
        //   label: "产品ID",
        //   key: "proCode",
        //   showWhen: "000",
        //   dataKey: "AiType000",
        // },
        {
          label: "上架日期",
          key: "onTimeStr",
          showWhen: "000",
          dataKey: "AiType000",
          span: 2,
        },
        {
          label: "同系列订单数",
          key: "tongOrderCount",
          showWhen: "000",
          dataKey: "AiType000",
          span: 2,
        },
        {
          label: "同系列总广告费",
          key: "tongAlladv",
          showWhen: "000",
          dataKey: "AiType000",
          span: 2,
        },
        {
          label: "同系列毛6率",
          key: "tongProfit6WcRate",
          showWhen: "000",
          dataKey: "AiType000",
          symbol: '%',
          span: 6,
        },
      ],
      currentController: null,//控制请求的取消
      isRequesting: false,//请求状态标识
    };
  },
  async mounted() {
    // 从 cookie 中获取 token
    // this.token = document.cookie.split("=")[1];
    this.token = this.$store.getters.token;
    // const { data , success } = await getLoginInfo();
    // if(success){
    //   this.avatar = data.user.avatar;
    // }
    this.displayInfo = this.info.row;
  },
  methods: {
    tableLabel() {
      return this.dimension === '001' ? '助理列表' : '专员助理列表';
    },
    async startSession(id, type) {
      const { data, success } = await getUserDingCode({ type, id })
      if (success) {
        if (!data) return this.$message.error('未获取到钉钉id')
        window.open(`dingtalk://dingtalkclient/action/sendmsg?spm=dingtalk_id=${data}`, '_self')
      }
    },
    async handleMethod(type) {
      let params;
      if (type == '003') {
        params = {
          groupId: this.info.row.groupId,
          yearMonthDay: this.info.row.yearMonthDay,
          aiType: type
        }
      } else if (type == '004') {
        params = {
          styleCode: this.info.row.styleCode,
          yearMonthDay: this.info.row.yearMonthDay,
          aiType: type
        }
      } else if (type == '000') {
        params = {
          proCode: this.info.row.proCode,
          yearMonthDay: this.info.row.yearMonthDay,
          aiType: type,
        };
      } else if (type == "002") {
        params = {
          styleCode: this.info.row.styleCode,
          groupId: this.info.row.groupId,
          yearMonthDay: this.info.row.yearMonthDay,
          aiType: type,
        };
      } else if (type == "001") {
        params = {
          styleCode: this.info.row.styleCode,
          operateSpecialUserId: this.info.row.operateSpecialUserId,
          yearMonthDay: this.info.row.yearMonthDay,
          aiType: type,
        };
      }
      this.dimension = type;
      await this.getParameter(params);
      await this.setlog();
    },
    formatImg(img) {
      return matchImg(img)
    },
    async setlog() {
      let _this = this;

      _this.pagedata.action = 'AI分析';
      setTimeout(async () => {
        for (var i = 0; i < document.getElementsByClassName('el-breadcrumb__inner').length; i++) {
          if (document.getElementsByClassName('el-breadcrumb__inner').length == i + 1) {
            _this.pagedata.modul = document.getElementsByClassName('el-breadcrumb__inner')[0]?.innerText;
            _this.pagedata.pageName = document.getElementsByClassName('el-breadcrumb__inner')[document.getElementsByClassName('el-breadcrumb__inner').length - 1]?.innerText;
          }
        }
        if (_this.pagedata.pageName) {
          for (var i = 0; i < document.getElementsByClassName('is-active').length; i++) {
            if (document.getElementsByClassName('is-active')[i].textContent == _this.pagedata.pageName) {
              _this.pagedata.pageTab = document.getElementsByClassName('is-active')[i + 2]?.textContent;
            }
          }
        }

        await PageReqLog(_this.pagedata.modul, _this.pagedata.pageName, _this.pagedata.pageTab ? _this.pagedata.pageTab : _this.pagedata.pageName, _this.pagedata.action);
      }, 500)

    },
    renderMdText(text) {
      return this.markdownRender.render(text);
    },
    // 格式化日期字符串
    formatYearMonthDay(yearMonthDay) {
      if (!yearMonthDay) {
        return "";
      }
      const year = yearMonthDay.substring(0, 4);
      const month = yearMonthDay.substring(4, 6).padStart(2, "0");
      const day = yearMonthDay.substring(6, 8).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    async getParameter(row) {
      if (this.currentController) {
        this.currentController.abort();
      }

      this.chatHistory = []; //清空记录
      this.overallLoading = true;
      this.inLoading = true;

      try {
        const { data: data1, success: success1 } = await getProductDetailAI(row);
        this.overallLoading = false;
        if (!success1) {
          this.$message.error('获取数据失败');
          this.dimension = null;
          this.inputTextLast = '';
          this.$set(this, 'inputTextLast', '');
          return;
        }
        this.AiType000 = { ...data1 };
        this.displayedInfo = { ...data1[0] };
        let formattedText = data1[0].fenxineirong;
        if (this.dimension == '000') {
          const data = data1;
          //const inputTextLines = [];
          //inputTextLines.push(`商品ID：xxxxxx；`);
          // if (data.length > 0) {
          //   //inputTextLines.push(`商品系列：${data[0]?.styleCode}`);
          //   inputTextLines.push(`上架日期：${data[0]?.onTimeStr}`);
          // }
          // 每天订单量
          // data.forEach(item => {
          //   inputTextLines.push(`${item.ymdFmt} 订单量：${item.orderCount}，广告费：${item.alladv}，毛四利润率：${item.profit4WcRate};`);
          // });
          //每日SKU销量
          // data[0].skUlist.forEach(item => {
          //   // const formattedDate2 = this.formatYearMonthDay(item.yearMonthDay);
          //   inputTextLines.push(`${item.ymdFmt} SKU ${item.sku}： 在本链接销量(${item.orderCount})，公司总销量(${item.gsSaleQty});`);
          // });
          // data[0].skuStockList.forEach(item => {
          //   inputTextLines.push(`SKU ${item.sku}： 当前库存(${item.currentStock})，公司总商品链接数(${item.aboutProCodeCount});`);
          // });
          // if (inputTextLines.length > 2) {
          //   this.inputTextLast = inputTextLines.join('\n');
          // }
          // 帮我分析下这个经营 数据
         // formattedText = inputTextLines.join('\n') + '\n' + formattedText;
          formattedText += '\n' + '帮我分析下经营数据';
        }
        this.inputText = formattedText;
        this.inputTextLast = formattedText;
        await this.sendMessage();
      } catch (error) {
        this.overallLoading = false;
        this.inLoading = false;
        if (error.name === 'AbortError') {
          console.log('请求被取消');
        } else {
          console.error('获取数据失败:', error);
        }
      }
    },
    async sendMessage() {
      // 如果输入为空或者正在处理中，则不发送消息
      if (!this.inputText.trim() || this.isProcessing) return;

      this.isProcessing = true;
      this.currentAiResponse = '';
      this.currentAiMessage = null;

      // 添加用户消息到历史记录
      this.chatHistory.push({
        type: 'user',
        content: this.inputText,
        timestamp: new Date()
      });

      // 添加空的 AI 消息到历史记录
      this.chatHistory.push({
        type: 'ai',
        content: '',
        timestamp: new Date()
      });

      try {
        this.AiType000_IsLoadAiData = false;
        await this.fetchStreamData(this.inputText);
        this.AiType000_IsLoadAiData = true;
      } catch (error) {
        console.error('API调用失败:', error);
        const lastIndex = this.chatHistory.length - 1;
        if (lastIndex >= 0) {
          this.chatHistory[lastIndex].content = `错误：${error.message || '请求失败，请检查网络或稍后重试'}`;
          this.chatHistory[lastIndex].isError = true;
        }
      } finally {
        // 重置
        this.isProcessing = false;
        this.inputText = '';
        // 自动滚动到底部
        this.$nextTick(() => {
          const chatArea = document.querySelector(".chat-area");
          if (chatArea) {
            chatArea.scrollTop = chatArea.scrollHeight;
          }
        });
      }
    },
    async fetchStreamData(question) {
      if (this.currentController) {
        this.currentController.abort();
      }

      this.currentController = new AbortController();//来控制请求的取消
      const baseUrl = 'http://192.168.16.240:8001/api/bladegateway/yunhan-gis-personnel/data-analysis/fastgptAiChat';

      if (!this.token) {
        toLogout();
        return;
      }

      try {
        this.inLoading = true;
        this.isRequesting = true;

        const formData = new FormData();
        formData.append('question', question);

        const response = await fetch(baseUrl, {
          method: 'POST',
          headers: {
            authorization: "Bearer " + this.token,
          },
          body: formData,
          signal: this.currentController.signal
        });

        this.inLoading = false;

        if (!response.ok) {
          if (response.status === 401) {
            toLogout();
            return;
          }
          throw new Error(`请求失败，状态码：${response.status}，状态文本：${response.statusText}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder("utf-8");
        let buffer = '';
        let incompleteBuffer = '';

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = '';

          for (let line of lines) {
            if (!line.trim()) continue;

            let rawStr = line.replace(/^data:\s*/, '').trim();
            if (rawStr === '[DONE]' || rawStr === '"[DONE]"') continue;

            rawStr = incompleteBuffer + rawStr;

            try {
              // 处理可能的字符串包裹的 JSON
              if (rawStr.startsWith('"') && rawStr.endsWith('"')) {
                rawStr = JSON.parse(rawStr);
              }

              let parsed;
              let content = '';
              if (typeof rawStr !== 'string' || rawStr.trim() === '') {
                // console.warn('接收到的 rawStr 为空或不是字符串');
              } else {
                try {
                  if (rawStr.trim().length > 0) {
                    parsed = JSON.parse(rawStr);
                    if (parsed &&
                      Array.isArray(parsed.choices) &&
                      parsed.choices.length > 0 &&
                      parsed.choices[0].delta &&
                      typeof parsed.choices[0].delta.content === 'string') {
                      content = parsed.choices[0].delta.content;
                    }
                  }
                } catch (e) {
                  // console.warn('JSON 解析失败，跳过当前段:', e, rawStr);
                }
              }

              if (content) {
                const lastIndex = this.chatHistory.length - 1;
                if (lastIndex >= 0) {
                  this.chatHistory[lastIndex].content += content;
                }
              }

              incompleteBuffer = '';
            } catch (e) {
              if (e.message.includes('Unexpected end of JSON input') ||
                e.message.includes('Unterminated string')) {
                incompleteBuffer = rawStr;
              } else {
                console.warn('JSON解析失败:', {
                  errorMessage: e.message,
                  rawData: rawStr
                });
                incompleteBuffer = '';
              }
            }
          }
        }
      } catch (error) {
        if (error.name === 'AbortError') {
          console.log('请求被取消');
        } else {
          console.error('流式数据获取失败:', error);
          const lastIndex = this.chatHistory.length - 1;
          if (lastIndex >= 0) {
            this.chatHistory[lastIndex].content = `错误：${error.message || '请求失败，请检查网络或稍后重试'}`;
            this.chatHistory[lastIndex].isError = true;
          }
        }
      } finally {
        this.isRequesting = false;
        this.inLoading = false;
        this.currentController = null;
      }
      const lastIndex = this.chatHistory.length - 1;
      const full = this.chatHistory[lastIndex].content;
      const replaced = this.replaceProCodeXuHao(full, this.displayedInfo);
      this.chatHistory[lastIndex].content = replaced;
    },
    replaceProCodeXuHao(content, displayedInfo) {
      Object.entries(displayedInfo).forEach(([key, value]) => {
        if (Array.isArray(value) && value.length > 0 && value[0]?.proCodeXuHao) {
          value.forEach(item => {
            const regex = new RegExp(`\\b${item.proCodeXuHao}\\b`, 'g');
            content = content.replace(regex, item.proCode);
          });
        }
      });
      return content;
    },
    clearChat() {
      // 清空聊天历史记录
      this.chatHistory = [];
    },
    handleSettings() {
      // 提示设置功能待实现
      this.$message('设置功能待实现');
    },
    formatTime(date) {
      // 格式化时间
      return date.toLocaleTimeString();
    },
    shouldShowItem(item) {
      if (item.showWhen) {
        return (
          this.dimension === item.showWhen || item.showWhen.includes(this.dimension)
        );
      }
      return true;
    },
    getImageUrl(item) {
      if (item.distinguish) {
        return `http://192.168.16.240:8001/api/Admin/User/GetUserAvatar?type=${item.imageType}&id=${this.displayInfo[item.ddInfo.prop]}`;
      } else {
        return this.displayedInfo[item.key] ? this.displayedInfo[item.key] : 'https://nanc.yunhanmy.com:10010/media/video/20250415/1912028614636257281.jpeg';
      }
    },
    getDisplayValue(item) {
      if (item.dataKey) {
        if (item.dataKey === 'displayedInfo') {
          // 根据 dimension 动态选择 key
          let actualKey = item.key;
          if (this.dimension === '002') {
            switch (item.key) {
              case 'skuimage':
                actualKey = 'images';
                break;
              // case 'styleSkuSumCount':
              //   actualKey = 'styleCurrentStock';
              //   break;
              // case 'proCodeCount':
              //   actualKey = 'styleProCodeCount';
              //   break;
            }
          }
          return this.displayedInfo[actualKey];
        } else {
          return this[item.dataKey][0]?.[item.key] === null ? 0 : this[item.dataKey][0]?.[item.key];
        }
      }
      if (item.key == 'proCode' && (this.dimension != '000')) {
        return ''
      } else {
        return this.displayInfo[item.key];
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ai-container {
  /* max-width: 800px; */
  /* margin: 1rem auto; */
  /* padding: 1.5rem; */
}

/* 玻璃拟态效果 */
.glassmorphism {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(12px);
  /* border-radius: 20px; */
  /* border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.1); */
}

/* 聊天区域 */
.chat-area {
  height: 60vh;
  /* margin: 1.5rem 0; */
  padding: 1.5rem;
  overflow-y: auto;
  transition: all 0.3s ease;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE 10+ */
}

/* 隐藏 Chrome, Safari 和 Opera 的滚动条 */
.chat-area::-webkit-scrollbar {
  display: none;
}

/* 消息气泡 */
.message-bubble {
  display: flex;
  gap: 1rem;
  /* margin: 1.2rem 0; */
  max-width: 100%;
  animation: floatUp 0.4s ease;
}

.user-bubble {
  margin-left: auto;
  flex-direction: row-reverse;
}

.message-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  /* background: rgba(108,140,231,0.1); */
  color: #6C8CE7;
  font-size: 1.2rem;
}

.user-bubble .message-icon {
  background: rgba(108, 140, 231, 0.2);
}

.message-content {
  padding: 1rem 1.5rem;
  border-radius: 15px;
  position: relative;
  /* line-height: 1.6; */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.user-bubble .message-content {
  background: #6C8CE7;
  color: white;
  border-radius: 15px 5px 15px 15px;
}

.ai-bubble .message-content {
  background: white;
  color: #333;
  border-radius: 5px 15px 15px 15px;
}

.message-time {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.4);
  margin-top: 0.5rem;
  text-align: right;
}

.user-bubble .message-time {
  color: rgba(255, 255, 255, 0.7);
}

/* 输入区域 */
.input-area {
  display: flex;
  gap: 1rem;
  padding: 1.2rem;
}

.input-field {
  border: none !important;
  background: transparent !important;
  padding: 0.8rem !important;
  flex-grow: 1;
}

.gradient-btn {
  background: linear-gradient(135deg, #6C8CE7 0%, #849dff 100%);
  border: none;
  border-radius: 12px !important;
  transition: all 0.3s ease;
  /* 按钮高度和输入框保持一致 */
  height: 50px;
  min-height: 4em;
  align-self: stretch;
  margin-top: 20px;
}

.glass-btn {
  /* 按钮高度和输入框保持一致 */
  height: 50px;
  min-height: 4em;
  align-self: stretch;
  margin-top: 20px;
}

.gradient-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(108, 140, 231, 0.3);
}

/* 动画 */
@keyframes floatUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-fade-enter-active {
  transition: all 0.3s ease;
}

.message-fade-leave-active {
  transition: all 0.2s ease;
}

.message-fade-enter {
  opacity: 0;
  transform: translateY(20px);
}

.message-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-container {
    padding: 1rem;
  }

  .message-bubble {
    max-width: 100%;
  }

  .message-content {
    /* padding: 0.8rem 1.2rem; */
  }

  .input-area {
    flex-direction: column;
  }
}

::v-deep h3 {
  line-height: 0px !important;
}

::v-deep ul {
  line-height: 16px !important;
  margin: 0 !important;
}

::v-deep .el-button+.el-button,
.el-checkbox.is-bordered+.el-checkbox.is-bordered {
  margin-left: 3px;
}

.ddTalk_Css ::v-deep img {
  max-width: 20px !important;
  max-height: 20px !important;
  min-height: 20px !important;
  min-width: 20px !important;
  vertical-align: middle;
  cursor: pointer;
}
</style>
