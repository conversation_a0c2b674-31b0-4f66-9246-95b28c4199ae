<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template #header>
        <el-form
          class="ad-form-query"
          :inline="true"
          :model="Filter"
          @submit.native.prevent>
        </el-form>
      </template>
      <!--列表-->
      <ces-table ref="table" :that='that' :isIndex='true'
                :hasexpand='false' @sortchange='sortchange' :tableData='inquirsstatisticslist'
                @select='selectchange' :isSelection='false'
           :tableCols='tableCols' :loading="listLoading">
        <el-table-column type="expand">
          <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
        </el-table-column>
         <template slot='extentbtn'>
                  <el-input v-model="Filter.sname" v-model.trim="Filter.sname" placeholder="姓名" style="width:120px;"
                        disabled="true" :maxlength="50" />
                    <el-input v-model="Filter.startDate" style="width:120px;" disabled="true" />至
                    <el-input v-model="Filter.endDate" style="width:120px;" disabled="true" />
          </template>
      </ces-table>
      <!--分页-->
      
          
    </my-container>
  </template>
  <script>
  import {getKuaiShouShopPersonalEfficiencyPageList} from '@/api/customerservice/kuaishouinquirs'

  import {getInquirsStatisticsByShopList } from '@/api/customerservice/groupinquirsstatistics'
  import dayjs from "dayjs";
  import cesTable from "@/components/Table/table.vue";
  import { formatTime } from "@/utils";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  const tableCols =[
             
               {istrue:true,prop:'shopName',label:'店铺名称', width:'150',sortable:'custom'},
               { istrue: true, prop: 'inquirs', label: '咨询人数', width: '80', sortable: 'custom' },
                { istrue: true, prop: 'inquireCount', label: '咨询人次', width: '80', sortable: 'custom' },
                { istrue: true, prop: 'artificialConversation', label: '人工会话量', width: '100', sortable: 'custom' },
                { istrue: true, prop: 'perCapitaReception', label: '人均接待量', width: '100', sortable: 'custom' },
                { istrue: true, prop: 'conversation3MinRate', label: '三分钟回复率（会话）', width: '100', sortable: 'custom', formatter: (row) => (row.conversation3MinRate * 100).toFixed(2) + "%" },
                { istrue: true, prop: 'customerService3MinRate', label: '三分钟回复率（人维度）', width: '100', sortable: 'custom', formatter: (row) => (row.customerService3MinRate * 100).toFixed(2) + "%" },
                { istrue: true, prop: 'noServiceRate', label: '不服务率', width: '80', sortable: 'custom', formatter: (row) => (row.noServiceRate * 100).toFixed(2) + "%" },
                { istrue: true, prop: 'artificialAvgReply', label: '人工平均回复时长', width: '100', sortable: 'custom' },
                { istrue: true, prop: 'conversationNiceCommentRate', label: '好评率（会话）', width: '80', sortable: 'custom', formatter: (row) => (row.conversationNiceCommentRate * 100).toFixed(2) + "%" },
                { istrue: true, prop: 'niceCommentRate', label: '好评率（人维度）', width: '80', sortable: 'custom', formatter: (row) => (row.niceCommentRate * 100).toFixed(2) + "%" },
                { istrue: true, prop: 'badCommentRate', label: '差评率（人维度）', width: '80', sortable: 'custom', formatter: (row) => (row.badCommentRate * 100).toFixed(2) + "%" },
                { istrue: true, prop: 'centreCommentRate', label: '中评率（人维度）', width: '80', sortable: 'custom', formatter: (row) => (row.centreCommentRate * 100).toFixed(2) + "%" },
                { istrue: true, prop: 'imBadRate', label: 'IM不满意率（人维度）', width: '100', sortable: 'custom', formatter: (row) => (row.imBadRate * 100).toFixed(2) + "%" },
                { istrue: true, prop: 'changePlatformRate', label: '转平台服务率', width: '80', sortable: 'custom', formatter: (row) => (row.changePlatformRate * 100).toFixed(2) + "%" },
                { istrue: true, prop: 'conversationCentreCommentRate', label: '中评率（会话）', width: '80', sortable: 'custom', formatter: (row) => (row.conversationCentreCommentRate * 100).toFixed(2) + "%" },
                { istrue: true, prop: 'conversationBadCommentRate ', label: '差评率（会话）', width: '80', sortable: 'custom', formatter: (row) => (row.conversationBadCommentRate * 100).toFixed(2) + "%" },
                { istrue: true, prop: 'inviteConversationCount', label: '邀评会话数', width: '80', sortable: 'custom'},
                { istrue: true, prop: 'inviteCommentRate ', label: '邀评率（人维度）', width: '80', sortable: 'custom', formatter: (row) => (row.inviteCommentRate * 100).toFixed(2) + "%" },
                { istrue: true, prop: 'commentRate', label: '评价率（人维度）', width: '80', sortable: 'custom', formatter: (row) => (row.commentRate * 100).toFixed(2) + "%" },
                { istrue: true, prop: 'commentConversationCount', label: '评价会话数', width: '80', sortable: 'custom'},
                { istrue: true, prop: 'badCommentCount', label: '差评人数', width: '80', sortable: 'custom'},
                { istrue: true, prop: 'centreComment', label: '中评人数', width: '80', sortable: 'custom'},
                { istrue: true, prop: 'niceCommentCount', label: '好评人数', width: '80', sortable: 'custom'},
                { istrue: true, prop: 'askOrderRate', label: '询单转化率', width: '80', sortable: 'custom', formatter: (row) => (row.askOrderRate * 100).toFixed(2) + "%" },
                { istrue: true, prop: 'askPrice', label: '询单客单价', width: '80', sortable: 'custom'},
                { istrue: true, prop: 'salePrice', label: '客服销售额', width: '80', sortable: 'custom'},
                { istrue: true, prop: 'commentCount', label: '评价人数', width: '80', sortable: 'custom'},
                { istrue: true, prop: 'placeOrderCount', label: '下单人数', width: '80', sortable: 'custom'},
                { istrue: true, prop: 'payOrderCount', label: '支付人数', width: '80', sortable: 'custom'},
                { istrue: true, prop: 'inviteCommentCount', label: '邀评人数', width: '80', sortable: 'custom'},
                { istrue: true, prop: 'askOrderCount', label: '询单人数', width: '80', sortable: 'custom'},    
                { istrue: true, prop: 'attendanceDays', label: '出勤人次', width: '80', sortable: 'custom'}, 
       ];
       const tableCols2 =[
             
             {istrue:true,prop:'shopName',label:'店铺名称', width:'150',sortable:'custom'},
             {istrue:true,prop:'sname',label:'姓名', width:'150',sortable:'custom'},
             { istrue: true, prop: 'inviteCommentCount', label: '邀评人数', width: '80', sortable: 'custom'},
             { istrue: true, prop: 'commentCount', label: '评价人数', width: '80', sortable: 'custom'},
             { istrue: true, prop: 'niceCommentCount', label: '好评人数', width: '80', sortable: 'custom'},
             { istrue: true, prop: 'centreComment', label: '中评人数', width: '80', sortable: 'custom'},
             { istrue: true, prop: 'badCommentCount', label: '差评人数', width: '80', sortable: 'custom'},
             { istrue: true, prop: 'niceCommentRate', label: '好评率（人维度）', width: '150', sortable: 'custom', formatter: (row) => (row.niceCommentRate * 100).toFixed(2) + "%" },
             { istrue: true, prop: 'badCommentRate', label: '差评率（人维度）', width: '150', sortable: 'custom', formatter: (row) => (row.badCommentRate * 100).toFixed(2) + "%" },
     ];

  export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
    data() {
      return {
        that:this,
        Filter: {
        },
        shopList:[],
        userList:[],
        groupList:[],
        inquirsstatisticslist: [],
        tableCols:[],
        total: 0,
        summaryarry:{count_sum:10},
        pager:{OrderBy:"inquirs",IsAsc:false},
        sels: [], // 列表选中列
        listLoading: false,
        pageLoading: false,
        //
        selids:[],
        dialogVisibleSyj:false,
        fileList:[],
      };
    },
    async mounted() {
      
    },
    created(){
      
       this.onSearch();
    },
    methods: {
  
              async canclick(row, column, cell){
              
          //  if(this.filter.userId>0)
          //  this.detailName=row.userName+"的各商品业绩明细，统计周期："+this.filter.startTime+"--"+this.filter.endTime;
          // if(this.filter.operateSpecialUserId>0)
          //  this.detailName=row.operateSpecialUserName+"的各商品业绩明细，统计周期："+this.filter.startTime+"--"+this.filter.endTime;
          //   window.financialReportDetailByOneUser={startTime:this.filter.startTime,endTime:this.filter.endTime,userId:this.filter.userId,operateSpecialUserId:this.filter.operateSpecialUserId,shopCode:row.shopCode}
          //  this.dialogVisible=true;
          //  if(this.$refs.financialReportDetailByOneUser)
          //  {
          //    this.$refs.financialReportDetailByOneUser.onSearch();
          //  } 
      }, 
  
      sortchange(column){
        if(!column.order)
          this.pager={};
        else
          this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
        this.onSearch();
      },
      onImportSyj(){
        this.dialogVisibleSyj = true
      },
      async uploadFile2(item) {
        const form = new FormData();
        form.append("upfile", item.file);
        const res = importInquirsStatisticsAsync(form);
        this.$message({message: '上传成功,正在导入中...', type: "success"});
      },
      async uploadSuccess2(response, file, fileList) {
        fileList.splice(fileList.indexOf(file), 1);
      },
      async onSubmitupload2() {
        this.$refs.upload2.submit()
      },
      onRefresh(){      
          this.onSearch()
              },
      onSearch(){       
         this.getinquirsstatisticsList();      
      },
      async getinquirsstatisticsList(){    
           var rate= localStorage.getItem("rate");
          console.log(rate)
           let newArr = rate=="rate" ? tableCols2 : tableCols
           this.tableCols = newArr;

             this.Filter.startDate=localStorage.getItem("startsdate")
             console.log(localStorage.getItem("startsdate"))
             this.Filter.endDate=localStorage.getItem("endsdate") 
             this.Filter.sname=localStorage.getItem("sname") 
            this.Filter.groupType=localStorage.getItem("groupType")

        const para = {...this.Filter};    
        const params = {      
          ...this.pager,
          ...para,
        };
  
        this.listLoading = true;
        const res = await getKuaiShouShopPersonalEfficiencyPageList(params);
        console.log(res)
        this.listLoading = false;
        console.log(res.data.list)
        //console.log(res.data.summary)
  
        this.total = res.data.total
        this.inquirsstatisticslist = res.data.list;
        //this.summaryarry=res.data.summary;
      },
      selectchange:function(rows,row) {
        this.selids=[];
        rows.forEach(f=>{
          this.selids.push(f.id);
        })
      }
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
  