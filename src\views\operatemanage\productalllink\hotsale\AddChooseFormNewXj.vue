<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <!--  -->
            <el-form class="ad-form-query" :model="chooseFormData" ref="chooseForm" @submit.native.prevent
                label-width="120px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="竞品ID" prop="goodsCompeteId" :rules="[
                            { required: true, message: '请填写竞品ID', trigger: 'blur', type: 'string' },
                            { min: 4, max: 30, message: '长度4到30 个字符内', trigger: 'blur', type: 'string' },
                            { validator: checkSearch, trigger: 'blur' }]">
                            <el-input style="width: 250px" v-model.trim="chooseFormData.goodsCompeteId" :maxlength="30"
                                :minlength="4"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="国内/跨境" prop="internationalType" :rules="[
                            { required: true, message: '请选择国内/跨境', trigger: 'blur', }]">
                            <el-select v-model="chooseFormData.internationalType" placeholder="请选择"
                                @change="onInternationalTypeChange" style="width: 250px" clearable filterable
                                :collapse-tags="true">
                                <el-option :key="0" :value="0" label="国内" />
                                <el-option :key="1" :value="1" label="跨境" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="询价平台" prop="xjUserPlatform"
                            :rules="[{ required: true, message: '请选择询价平台', trigger: 'blur' }]">
                            <el-select v-model="chooseFormData.xjUserPlatform" placeholder="请选择询价平台">
                                <el-option v-for="item in platformlist" :key="'xjpt-' + item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="期望最低价格" prop="exMinPrice" :rules="[
                            { required: true, message: '请填写期望最低价格', trigger: 'blur', }]">
                            <el-input-number v-model="chooseFormData.exMinPrice" type="number" placeholder="请填写期望最低价格"
                                clearable :controls="false" :precision="3" :max="9999999" :min="0"
                                style="width: 250px" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="期望最高价格" prop="exMinPrice" :rules="[
                            { required: true, message: '请填写期望最高价格', trigger: 'blur', }]">
                            <el-input-number v-model="chooseFormData.exMaxPrice" type="number" placeholder="请填写期望最高价格"
                                clearable :controls="false" :precision="3" :max="9999999" :min="0"
                                style="width: 250px" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="商品图片" prop="goodsCompeteImgUrl" :rules="[
                            { required: true, message: '请上传', trigger: 'blur' }]">
                            <!-- <el-input v-model=" chooseFormData.goodsCompeteImgUrl " style="width: 250px" :maxlength="100"></el-input> -->
                            <yh-img-upload :limit="1" :value.sync="chooseFormData.goodsCompeteImgUrl" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    竞品平台
                </el-row>
                <vxe-table :data="chooseFormData.tablelist">
                    <!-- <vxe-column type="seq" width="60"></vxe-column> -->
                    <vxe-column field="platform" width="150" title="平台"></vxe-column>
                    <vxe-column field="price" width="200" title="价格">
                        <template #default="{ row }">
                            <!-- <el-input :max="9999999" @blur="row.price = row.price.slice(0,10)" type="number" v-model="row.price"></el-input> -->

                            <el-input-number v-model="row.price" type="number" clearable :controls="false"
                                :precision="3" :max="9999999" :min="0" @blur="row.price = row.price.slice(0, 10)" />
                        </template>
                    </vxe-column>
                    <vxe-column field="link" title="链接">
                        <template #default="{ row }">
                            <el-input maxlength="300" v-model="row.link"
                                @paste.native='pasteDescription(row)'></el-input>
                        </template>
                    </vxe-column>
                </vxe-table>

            </el-form>
        </template>

        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">
                    <el-button @click="onClose">取消</el-button>
                    <el-button type="primary" @click="onSave(true)">确认询价</el-button>
                </el-col>
            </el-row>
        </template>

    </my-container>
</template>
<script>

import { platformlist, formatPlatform } from "@/utils/tools";
import MyContainer from "@/components/my-container";
import YhImgUpload from '@/components/upload/yh-img-upload.vue';
import {
    saveHotSaleBrandPushNewXj, getHotSaleBrandPushNewById, validateGoodsIdIsRepeat
} from '@/api/operatemanage/productalllink/LogisticsAnalyse.js';
import {

} from '@/api/operatemanage/productalllink/alllink';

export default {
    name: "AddChooseFormXj",
    components: { MyContainer, YhImgUpload },
    props: {
        isedit: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            platformlist: platformlist,
            that: this,
            chooseFormData: {
                goodsCompeteId: "",
                internationalType: null,
                goodsCompeteImgUrl: "",
                tablelist: [
                    {
                        platform: '拼多多',
                    },
                    {
                        platform: '抖音',
                    },
                    {
                        platform: '淘宝',
                    },
                ]
            },
            pageLoading: false,
        };
    },
    created() {
    },
    async mounted() {

    },
    methods: {
        async checkSearch(rule, value, callback) {
            let params = {
                id: this.rowid != 0 ? this.rowid : 0,
                GoodsCompeteId: value,
            }
            let res = await validateGoodsIdIsRepeat(params);
            if (!res.success) {
                return
            }
            if (res.data) {
                callback(new Error('该竞品ID已存在'));
            }
        },
        containsChinese(str) {
            const regex = /[\u4e00-\u9fa5]/;
            return regex.test(str);
        },
        extractLink(start, str) {
            if (start == 'https') {
                const regex = /https?:\/\/[^\s]+/g;
                const match = str.match(regex);
                return match ? match[0] : '';
            } else if (start == 'http') {
                const regex = /http?:\/\/[^\s]+/g;
                const match = str.match(regex);
                return match ? match[0] : '';
            } else {
                this.$message("请填写正确的链接")
            }

        },
        pasteDescription(val) {
            let _this = this;
            setTimeout(() => {
                let ischese = _this.containsChinese(val.link);
                let url = "";
                if (val.link.indexOf('https') != -1) {
                    url = _this.extractLink('https', val.link);
                } else if (val.link.indexOf('http') != -1) {
                    url = _this.extractLink('http', val.link);
                }

                if (val.link && ischese) {
                    this.$message({ message: "链接复制匹配更换成" + url, type: "success", });
                    if (val.platform == '拼多多') {
                        this.chooseFormData.tablelist[0].link = url;
                    } else if (val.platform == '抖音') {
                        this.chooseFormData.tablelist[1].link = url;
                    } else if (val.platform == '淘宝') {
                        this.chooseFormData.tablelist[2].link = url;
                    }

                }
            }, 600)


        },
        async getmsg(id) {
            this.rowid = id;
            let res = await getHotSaleBrandPushNewById({
                id: id
            });
            if (res?.success) {
                let allobj = res.data;
                this.chooseFormData = allobj;
                this.chooseFormData.tablelist = [
                    {
                        platform: '拼多多',
                        price: allobj.pddPrice,
                        link: allobj.pddLink
                    },
                    {
                        platform: '抖音',
                        price: allobj.dyPrice,
                        link: allobj.dyLink
                    },
                    {
                        platform: '淘宝',
                        price: allobj.tbPrice,
                        link: allobj.tbLink
                    },
                ]
            }
        },
        onClose() {
            this.$emit('close');
        },
        async onSave(isClose) {
            if (await this.save()) {
                this.$emit('afterSave');
                if (isClose)
                    this.$emit('close');
            }
        },
        async loadData(val) {
            console.log(val, 'val');
            this.chooseFormData.goodsCompeteId = "";
            this.chooseFormData.goodsCompeteImgUrl = "";
            this.chooseFormData.internationalType = null;
            if (val.oid) {
                this.getmsg(val.oid);
            }

        },
        async save() {
            let that = this;
            this.pageLoading = true;
            this.onInternationalTypeChange();
            let saveData = { ...this.chooseFormData };

            this.chooseFormData.tablelist.map((item) => {
                if (item.platform == '拼多多') {
                    saveData.pddPrice = item.price;
                    saveData.pddLink = item.link;
                } else if (item.platform == '抖音') {
                    saveData.dyPrice = item.price;
                    saveData.dyLink = item.link;
                } else if (item.platform == '淘宝') {
                    saveData.tbPrice = item.price;
                    saveData.tbLink = item.link;
                }
            })

            try {
                let valid = await this.$refs["chooseForm"].validate();
                if (valid) {
                    let reqRlt = await saveHotSaleBrandPushNewXj(saveData);
                    if (reqRlt && reqRlt.success) {
                        that.$message({ message: '操作成功！', type: "success" });
                    }
                    this.pageLoading = false;
                    this.$emit('onSearch');
                    return reqRlt && reqRlt.success;
                } else {
                    this.pageLoading = false;
                    return false;
                }

            } catch (error) {
                this.pageLoading = false;
                return false;
            }
        },
        onInternationalTypeChange(value) {
            if (this.chooseFormData.internationalType == 1) {
                if (this.chooseFormData.goodsCompeteId.toLowerCase().indexOf("kj") != 0) {
                    this.chooseFormData.goodsCompeteId = ("KJ" + this.chooseFormData.goodsCompeteId);
                }
            }
            else {
                if (this.chooseFormData.goodsCompeteId.toLowerCase().indexOf("kj") == 0) {
                    this.chooseFormData.goodsCompeteId = (this.chooseFormData.goodsCompeteId.substring(2));
                }
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.flexrow {
    display: flex;
    flex-direction: row;
}
</style>
