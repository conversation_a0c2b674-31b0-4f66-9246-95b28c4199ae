<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true'
              :hasexpand='false' @sortchange='sortchange' :tableData='lookboardlist'
              @select='selectchange' :isSelection='false'
         :tableCols='tableCols' :loading="listLoading">
      <el-table-column type="expand">
        <template slot-scope="props">
        <div>
          <el-table :data="props.row.detaildata" style="width: 100%">
            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
            </el-table-column> 
          </el-table>
        </div>
      </template>
      </el-table-column>
       <template slot='extentbtn'>
          <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                   <el-input v-model="Filter.goodsid" placeholder="商品ID" style="width:120px;"/>
                 </el-button>
                           
                           
                            <el-button style="padding: 0;margin: 0;">
                                <el-input v-model="Filter.Desc" placeholder="视频描述" style="width:120px;"/>
                            </el-button>
                            <el-button style="padding: 0;margin: 0;">
                             <el-date-picker style="width:280px" v-model="Filter.gDate" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                                range-separator="至" start-placeholder="开始下载日期" end-placeholder="结束下载日期" ></el-date-picker>
                             </el-button>
                                         
            <!-- <el-button type="text" size="medium" disabled style="color: red;">周转对比:</el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input-number v-model="filter1.startdiff"></el-input-number>至<el-input-number v-model="filter1.enddiff"></el-input-number>
            </el-button> -->
            <el-button type="primary" @click="onSearch">查询</el-button>

            <el-button type="primary" @click="onImportSyj">导入</el-button>
          </el-button-group>
        </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getlookboardList1"
      />
    </template>

    <el-dialog title="拼多多视频推广看板" :visible.sync="dialogVisibleSyj" width="30%">
      <span>
          <el-upload ref="upload2" class="upload-demo"
                  :auto-upload="false"
                  :multiple="false"
                  :limit="1"
                  action
                  accept=".xlsx"
                  :http-request="uploadFile2"
                  :on-success="uploadSuccess2">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传</my-confirm-button>
          </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
function  formatterimg(row){
  if(row.pic)
      return "<a target='_blank'  href='"+row.videourl+"'><img src='"+row.pic+"' width=\"100\" height=\"100\" class=\"cover\" /></a>";
  else
  {
    return "<a target='_blank' href='"+row.videourl+"'>打开视频</a>"
  }

    }

import {importLookBoardAsync,getLookBoardList,deleteLookBoardBatch } from '@/api/operatemanage/lookboard'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
const tableCols =[
             {istrue:true,prop:'shopname',label:'店铺名', width:'100',sortable:'custom'},
                {istrue:true,prop:'likes',label:'点赞数', width:'50',sortable:'custom'},          
              {istrue:true,prop:'goodsId',label:'商品ID', width:'140',sortable:'custom',type:'click',handle:(that,row)=>that.showProduct(row)},
             {istrue:true,prop:'totalGMV',label:'成交金额', width:'100',sortable:'custom'},
             {istrue:true,prop:'orderCount',label:'当日订单数', width:'50',sortable:'custom'},
             {istrue:true,prop:'playCount',label:'播放量', width:'100',sortable:'custom'},
             {istrue:true,prop:'orderUV',label:'下单人数', width:'50',sortable:'custom'},
            {istrue:true,prop:'comments',label:'评论数', width:'50',sortable:'custom'},
            {istrue:true,prop:'gDate',label:'下载日期', width:'100',sortable:'custom'},
             {istrue:true,prop:'publishTime',label:'发布时间', width:'100',sortable:'custom'},
              {istrue:true,prop:'desc',label:'视频描述', width:'200',sortable:'custom'},             
             {istrue:true,prop:'pic',label:'封面图片', width:'200',sortable:'custom',type:'html',formatter:(row)=>formatterimg(row)},   
             {istrue:true,prop:'shares',label:'分享量', width:'50',sortable:'custom'},
             {istrue:true,prop:'sevenDaysRecPlayCount',label:'7天推荐播放量', width:'100',sortable:'custom'},
             {istrue:true,prop:'isHotTag',label:'热度标签', width:'50',sortable:'custom'},
             {istrue:true,prop:'isTop',label:'置顶', width:'50',sortable:'custom'},
             {istrue:true,prop:'feedID',label:'feedID', width:'200',sortable:'custom'}, 
      {istrue:true,prop:'createdTime',label:'导入时间', width:'100',sortable:'custom'},
      {istrue:true,prop:'batchNumber',label:'导入批次', width:'200',sortable:'custom'},
      {istrue: true,type: "button",width: "430",btnList: [{ label: "删除批次", handle: (that, row) => that.deleteBatch(row)}]}
     ];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      Filter: {
      },
      shopList:[],
      userList:[],
      groupList:[],
      lookboardlist: [],
      tableCols:tableCols,
      total: 0,
      summaryarry:{count_sum:10},
      pager:{OrderBy:"id",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
    };
  },
  async mounted() {
  },
  methods: {   
    showProduct(row){
      window.open("https://mobile.yangkeduo.com/goods.html?goods_id="+row.goodsId);
    },
    showVideo(row){
      window.open(row.videourl);
    },
   async deleteBatch(row){
      var that=this;
      this.$confirm("此操作将删除此批次拼多多视频推广看板数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async() => {
      await deleteLookBoardBatch({batchNumber:row.batchNumber})
      that.$message({message: '已删除', type: "success"});
      that.onRefresh()

        });

    },
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    onImportSyj(){
      this.dialogVisibleSyj = true
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      const res = importLookBoardAsync(form);
      this.$message({message: '上传成功,正在导入中...', type: "success"});
    },
    async uploadSuccess2(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
    },
    async onSubmitupload2() {
      this.$refs.upload2.submit()
    },
    onRefresh(){
        this.onSearch()
    },
    onSearch(){
       this.$refs.pager.setPage(1);
       this.getlookboardList1();
    },
    async getlookboardList1(){
      if(this.Filter.gDate){
        this.Filter.startGDate=this.Filter.gDate[0];
         this.Filter.endGDate=this.Filter.gDate[1];
       }
       else
       {
this.Filter.startGDate=null;
         this.Filter.endGDate=null;

       }
      const para = {...this.Filter};
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,

      };

      console.log(para)

      this.listLoading = true;
      const res = await getLookBoardList(params);
      console.log(res)
      this.listLoading = false;
      console.log(res.data.list)
      //console.log(res.data.summary)

      this.total = res.data.total
      this.lookboardlist = res.data.list;
      //this.summaryarry=res.data.summary;
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
