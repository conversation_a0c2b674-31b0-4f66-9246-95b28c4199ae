<template>
 <div>
     <el-tabs v-model="activeName" @tab-click="handleClick">
         <el-tab-pane label="基础信息" name="info">
             <div class="tabs_item">
                 <div class="tabs_item_left">
                     <el-image :src="info.image" class="infoImg" :preview-src-list="srcList">
                     </el-image>
                 </div>
                 <div class="tabs_item_right">
                     <el-input v-model="info.title" class="infoipt" placeholder="请输入宝贝名" maxlength="200"></el-input>
                     <div class="tabs_item_right_goodsInfo">
                         <div class="tabs_item_right_goodsInfo_item">
                             <div>商品 ID：</div>
                             <div class="tabs_item_right_goodsInfo_item_id">{{ info.proCode }}</div>
                         </div>
                         <div class="tabs_item_right_goodsInfo_item">
                             <div>拼单价：</div>
                             <div class="tabs_item_right_goodsInfo_item_id">{{ info.unitPrice }} 元</div>
                         </div>
                     </div>
                     <div class="tabs_item_right_day">
                         <div>预算日限额：</div>
                         <div class="tabs_item_right_day_item">
                             <span>剩：</span>
                             <div>{{ info.balanceXE }} /</div>
                             <span>共：</span>
                             <div>{{ limitRadio == 1 ? '不限额' : info.totalXE + ' 元' }} </div>
                             <el-button type="primary" style="margin-left:10px" @click="editXE">编辑</el-button>
                         </div>
                     </div>
                     <div class="selectbox">
                         <el-select v-model="selectValue" placeholder="请选择" class="select infoipt" clearable
                             @change="changeSelect">
                             <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"
                                 class="selectItem">
                             </el-option>
                         </el-select>
                         <el-input type="number" v-model="info.stationExtendValue" v-if="selectValue !== null && selectValue !== ''"
                             class="selectIpt">
                             <template slot="suffix">元</template>
                         </el-input>
                     </div>

                 </div>
             </div>

         </el-tab-pane>
         <el-tab-pane label="创意数据" name="originalityProp">
             <el-date-picker v-model="dateTime" type="datetimerange" :picker-options="pickerOptions" range-separator="——"
                 start-placeholder="开始时间" end-placeholder="结束时间" @change="originalitydateChoose"
                 value-format="yyyy-MM-dd HH:mm:ss">
             </el-date-picker>
             <div class="originality">
                 <div style="padding-left:20px;font-size:20px;margin-bottom:10px;color:black">商品主图</div>
                 <el-table :data="tableData" style="width: 100%" align="center" show-summary
                     :summary-method="footerMethod">
                     <el-table-column label="创意推广" align="center">
                         <template slot-scope="scope">
                             <el-image :src="scope.row.fileUrl"></el-image>
                         </template>
                     </el-table-column>
                     <el-table-column prop="spendAmount" label="花费" align="center">
                     </el-table-column>
                     <el-table-column prop="tradeAmount" label="交易额" align="center">
                     </el-table-column>
                     <el-table-column prop="investRate" label="实际投产比" align="center">
                     </el-table-column>
                     <el-table-column prop="orderCount" label="成交笔数" align="center">
                     </el-table-column>
                     <el-table-column prop="avgOrderSpend" label="每笔成交花费" align="center">
                     </el-table-column>
                     <el-table-column prop="avgOrderAmount" label="每笔成交金额" align="center">
                     </el-table-column>
                     <el-table-column prop="exposureCount" label="曝光量" width="80" align="center">
                     </el-table-column>
                     <el-table-column prop="clickCount" label="点击量" width="80" align="center">
                     </el-table-column>
                     <el-table-column prop="clickRate" label="点击率" width="80" align="center" >
                     </el-table-column>
                     <el-table-column prop="clickConversionRate" label="点击转化率" width="100" align="center">
                     </el-table-column>
                     <el-table-column label="查看" width="80" align="center">
                         <template slot-scope="scope">
                             <el-button size="mini" @click="viewGoodsDetails(scope.row)"
                                 v-if="scope.row.fileType == '商品图'">详情</el-button>
                             <el-button size="mini" @click="deleteMaterial(scope.row, scope.$index)"
                                 v-else>删除</el-button>
                         </template>
                     </el-table-column>
                 </el-table>
             </div>
         </el-tab-pane>
         <el-tab-pane label="创意图片" name="originalityImg">
             <div class="originalityImg ">
                 <div class="originalityImg_title">商品图</div>
                 <div class="mainImgBox">
                     <div v-for="(item, i) in goodsImgList" class="mainImag">
                         <el-checkbox class="originalityImgCheck" :value="item.checkedStatus == 1 ? true : false"
                             @change="changemain(item, i)" />
                         <el-image class="imgcss" :src="item.fileUrl"></el-image>
                         <div class="imgType">{{ item.fileType }}</div>
                     </div>
                 </div>

             </div>
             <div class="originalityImg">
                 <div class="originalityImg_title">素材图</div>
                 <div class="mainImgBox">
                     <div v-for="(item, i) in goodsMaterialList" :key="i" class="mainImag">
                         <el-checkbox class="originalityImgCheck" :value="item.checkedStatus == 1 ? true : false"
                             @change="change(item, i)" />
                         <el-image class="imgcss" :src="item.fileUrl" :preview-src-list="materialList"></el-image>
                         <div class="imgType">{{ item.fileType }}</div>
                     </div>
                 </div>
                 <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" multiple :limit="4"
                     :on-success="handleSuccess" :file-list="fileList" list-type="picture" accept=".jpeg,.jpg,.png,.bmp,.webp,.gif">
                     <span class="addsc" @click="">添加</span>
                 </el-upload>
             </div>
         </el-tab-pane>
         <el-tab-pane label="操作记录" name="operateRec">
             <el-date-picker v-model="dateTime" type="datetimerange" :picker-options="pickerOptions" range-separator="——"
                 start-placeholder="开始时间" end-placeholder="结束时间" @change="operatedateChoose"
                 value-format="yyyy-MM-dd HH:mm:ss">
             </el-date-picker>
             <el-table :data="operateRecData" style="width: 100%">
                 <el-table-column prop="operateTime" label="操作时间" width="180" align="center">
                 </el-table-column>
                 <el-table-column prop="operateName" label="操作人" width="180" align="center">
                 </el-table-column>
                 <el-table-column prop="operateModule" label="操作模块" align="center">
                 </el-table-column>
                 <el-table-column prop="operateType" label="操作类型" align="center">
                 </el-table-column>
                 <el-table-column prop="operateInfo" label="操作详情" align="center">
                 </el-table-column>
             </el-table>
             <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                 :page-sizes="[5, 10, 15, 20]" :current-page.sync="getOperateInfo.currentPage"
                 :page-size="getOperateInfo.pageSize" layout="total,sizes, prev, pager, next" :total="total">
             </el-pagination>
         </el-tab-pane>
     </el-tabs>
     <!-- 查看商品详情弹层 -->
     <el-dialog title="查看分图片数据" :visible.sync="viewGoodsDetailsLog" width="30%" :modal="false">
         <el-table :data="goodsDetailTable" style="width: 100%" :default-sort="{ prop: 'date', order: 'descending' }">
             <el-table-column prop="ClickRate" label="推广创意" width="180" align="center">
                 <template slot-scope="scope">
                     <el-image :src="scope.row.FileUrl"></el-image>
                 </template>
             </el-table-column>
             <el-table-column prop="FileType" label="图片类型" width="180" align="center">
             </el-table-column>
             <el-table-column prop="ClickRate" label="点击率" sortable align="center">
             </el-table-column>
         </el-table>
     </el-dialog>

     <!-- 修改日限额弹层 -->
     <el-dialog title="修改预算日限额" :visible.sync="editLimit" width="30%" :modal="false" class="dialog">
         <!-- <el-alert :title="'每日可修改5次,今日还可以修改' + limitCount + '次'" type="info" :closable="false">
         </el-alert> -->
         <div style="margin-top:20px">
             预算日限额:
             <el-radio-group v-model="limitRadio" style="margin-left:10px" @change="changeRadioGroup">
                 <el-radio :label="1">不限</el-radio>
                 <el-radio :label="2">自定义</el-radio>
             </el-radio-group>
         </div>

         <div style="margin-top: 20px; margin-left:80px" class="limitInfo" v-if="limitRadio == 2">
             <el-input v-model="XEprops" placeholder="请输入日限额" style="height: 40px;width:300px" class="limitIpt" @input="moneyIpt">
                 <span slot="suffix" class="money">元</span>
             </el-input>
             <div style="font-size:15px">采纳建议值 7,000 元, 预计竞争力超过 99% 的同行 <span style="color: blue;cursor:pointer"
                     @click="XEprops = 7000">立即采纳</span></div>
         </div>
         <!-- .el-input__inner -->
         <div slot="footer">
             <el-button type="primary" @click="handleLimitClick">确 定</el-button>
             <el-button @click="editLimit = false">取 消</el-button>
         </div>
     </el-dialog>
 </div>
</template>

<script>
import { getAllStationExtendCreativeByTimeId, getAllStationExtendOperateLogByTimeId, getAllStationExtendReportByTimeId,saveActualTimeAdvData } from '@/api/operatemanage/datapdd/actualtimedatapdd'
import dayjs from 'dayjs'
export default {
 name: 'Vue2demoQztgdialog',
 props: ['rowmsg'],
 data () {
     return {
         materialList: [],//素材图预览图
         srcList:[],//商品主图预览图
         remainderWidth: 0,
         activeName: 'info',
         info: null,//基础信息
         tableData: [],//创意数据表格
         originalityInfo: {
             yearMonthDay: '',
             productCode: null//商品id
         },//创意接口数据
         // urlList: [],
         goodsImgList: [],//商品主图列表
         goodsMaterialList: [],//商品素材图列表
         pickerOptions: {
             shortcuts: [{
                 text: '一周',
                 onClick (picker) {
                     const end = new Date();
                     const start = new Date();
                     start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                     picker.$emit('pick', [start, end]);
                 }
             }, {
                 text: '一个月',
                 onClick (picker) {
                     const end = new Date();
                     const start = new Date();
                     start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                     picker.$emit('pick', [start, end]);
                 }
             }, {
                 text: '三个月',
                 onClick (picker) {
                     const end = new Date();
                     const start = new Date();
                     start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                     picker.$emit('pick', [start, end]);
                 }
             }],
             // 自定义日期禁用函数
             disabledDate (date) {
                 // 获取当前日期
                 const currentDate = new Date();
                 // 如果选中日期在当前日期之后，则禁用它
                 return date > currentDate;
             }
         },
         dateTime: null,//时间范围
         operateRecData: [],//操作记录数组
         options: [
             {
                 label: '目标投产比',
                 value: 1
             },
             {
                 label: '出价',
                 value: 2
             }
         ],
         selectValue: null,//选择数据
         viewGoodsDetailsLog: false,//查看商品详情弹层
         goodsDetailTable: [],//商品详情表格
         getOperateInfo: {
             productCode: null,
             endTime: null,
             startTime: null,
             yearMonthDay: null,
             currentPage: 1,
             pageSize: 10
         },//根据时间和id获取操作记录
         total: 0,//操作记录总数
         editLimit: false,//修改日限额弹层
         limitCount: 5,//计算今日修改了多少次
         limitRadio: 2,//日限额单选组
         XEprops: null,//修改限额弹层input
         fileList: [],//素材图上传列表
         materialTbale: [],//创意数据素材图表格
         summaryarry: {}
     };
 },
 created () {
     console.log('全站推广');
     this.originalityInfo.yearMonthDay = dayjs(this.rowmsg.yearMonthDay).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
     this.originalityInfo.productCode = this.rowmsg.proCode
     this.getOperateInfo.yearMonthDay = dayjs(this.rowmsg.yearMonthDay).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
     this.getOperateInfo.productCode = this.rowmsg.proCode
 },

 async mounted () {
     this.getTabsProp()
 },

    methods: {
        //控制日限额的小数位数
        moneyIpt () {
            //如果this.XEprops是小数,就只能输入四位小数
            if (this.XEprops.indexOf('.') !== -1) {
                const arr = this.XEprops.split('.')
                if (arr[1].length > 4) {
                    this.XEprops = arr[0] + '.' + arr[1].slice(0, 4)
                }
            }
        },
     footerMethod (param) {
         const { columns, data } = param;
         const sums = [];
         columns.forEach((column, index) => {
             if (index === 0) {
                 sums[index] = '合计数据';
                 return;
             }
             const values = data.map(item => Number(item[column.property]));
             if (!values.every(value => isNaN(value))) {
                 sums[index] = values.reduce((prev, curr) => {
                     const value = Number(curr);
                     if (!isNaN(value)) {
                      return prev + curr;
                     } else {
                         return prev;
                     }
                 }, 0);
             } else {
                 sums[index] = '';
             }
         });
         sums[9] += '%'
         sums[10] += '%'
         return sums;
     },
     //获取前三个tabs的数据
     async getTabsProp () {
         const { data } = await getAllStationExtendCreativeByTimeId(this.originalityInfo)
         if (data) {
             //基础信息
             this.info = data
             console.log(this.info, 'info');
             this.srcList.push(this.info.image)
             localStorage.setItem('oldData',JSON.stringify(data))
             this.tableData = data.reports.reports //创意数据
             this.goodsImgList = data.mainImageFiles//创意主图
             this.goodsMaterialList = data.materialImageFiles//创意素材图
              //取出data.materialImageFiles.fileUrl添加到素材图预览图数组
              data.materialImageFiles.forEach(item => {
                    this.materialList.push(item.fileUrl)
                })
             this.summaryarry = data.reports.summary;
         }
     },
     //获取操作记录
     async getOperateRecList () {
         const { data } = await getAllStationExtendOperateLogByTimeId(this.originalityInfo)
         if (data) {
             this.operateRecData = data.list
             this.total = data.total
         }
     },
     //点击tabs切换
     async handleClick (e) {
         if (e.index == 3) {
             this.getOperateRecList()
         }
     },
     //抽离日期选择器公共逻辑
     changedate () {
         this.getOperateInfo.startTime = dayjs(this.dateTime[0]).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
         this.getOperateInfo.endTime = dayjs(this.dateTime[1]).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
         this.getOperateInfo.productCode = this.rowmsg.proCode
         this.getOperateInfo.yearMonthDay = this.originalityInfo.yearMonthDay
     },
     //创意日期选择
     async originalitydateChoose () {
         if (this.dateTime) {
             this.changedate()
             const { data } = await getAllStationExtendReportByTimeId(this.getOperateInfo)
             if (data) {
                 this.tableData = data.reports
             }
         }
     },
     //根据其他字段获取操作记录
     async getOperateByOther (props) {
         const { data } = await getAllStationExtendOperateLogByTimeId(props)
         if (data) {
             this.operateRecData = data.list
             this.total = data.total
         }
     },
     //操作日期选择
     async operatedateChoose () {
         if (this.dateTime) {
             this.changedate()
             this.getOperateByOther(this.getOperateInfo)
         }
     },
     //查看商品详情
     viewGoodsDetails (row) {
         this.viewGoodsDetailsLog = true
         this.goodsDetailTable = JSON.parse(row.detailJson)
     },
     //每页多少条
     handleSizeChange (val) {
         this.getOperateInfo.pageSize = val
         this.getOperateByOther(this.getOperateInfo)
     },
     //选择当前页
     handleCurrentChange (val) {
         this.getOperateInfo.currentPage = val
         this.getOperateByOther(this.getOperateInfo)
     },
     //修改单选组
     changeRadioGroup (e) {
         console.log(e, '组改变');
         consolel.log(this.limitRadio, 'limitRadio')
     },
     //点击预算日限额编辑
     editXE () {
         this.XEprops = this.info.totalXE
         this.editLimit = true
     },
     //点击编辑日限额确定
     handleLimitClick () {
         this.info.xeType = this.limitRadio
         if (this.limitRadio == 1) {
             this.editLimit = false
         } else {
             this.info.totalXE = Number(this.XEprops)
             this.editLimit = false
         }
     },
     //素材图上传成功的回调
     handleSuccess ({ data }) {
         this.goodsMaterialList.push({
             checkedStatus: 1,
             fileType: '素材',
             fileUrl: data.url,
             isRechecked: 1
         })
         this.tableData.push({
             avgOrderAmount: 0,
             avgOrderSpend: 0,
             clickConversionRate: 0,
             clickCount: 0,
             clickRate: 0,
             creativeId: 0,
             exposureCount: 0,
             investRate: 0,
             fileUrl: data.url,
             investRate: 0,
             orderCount: 0,
             spendAmount: 0,
             tradeAmount: 0
         })
         this.materialList.push(data.url)
     },
     //点击素材图的checkbox
     change (item, i) {
         this.goodsMaterialList[i].checkedStatus = item.checkedStatus == 0 ? 1 : 0;
         this.goodsMaterialList[i].isRechecked = item.isRechecked == 0 ? 1 : 0;
         this.goodsMaterialList.forEach(item1 => {
             if (item1.checkedStatus == 1) {
                 this.tableData.push({
                     avgOrderAmount: 0,
                     avgOrderSpend: 0,
                     clickConversionRate: 0,
                     clickCount: 0,
                     clickRate: 0,
                     creativeId: 0,
                     exposureCount: 0,
                     investRate: 0,
                     fileUrl: item.fileUrl,
                     investRate: 0,
                     orderCount: 0,
                     spendAmount: 0,
                     tradeAmount: 0
                 })
             } else {
                 const index = this.tableData.findIndex(item2 => item2.fileUrl == item.fileUrl)
                 this.tableData.splice(index, 1)
             }
         })
     },
     // //点击商品图表格的删除
     deleteMaterial (row, i) {
         this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
             confirmButtonText: '确定',
             cancelButtonText: '取消',
             type: 'warning'
         }).then(() => {
             this.tableData.splice(i, 1)
             //找出row.fileUrl和materialList中相同的元素并删除
             const index1 = this.materialList.findIndex(item => item == row.fileUrl)
             this.materialList.splice(index1, 1)
             const index = this.goodsMaterialList.findIndex(item => item.fileUrl == row.fileUrl)
             if (index == -1) {
                 this.$message({
                     type: 'success',
                     message: '删除成功!'
                 });
             } else {
                 this.goodsMaterialList.splice(index, 1)
                 this.$message({
                     type: 'success',
                     message: '删除成功!'
                 });
             }


         }).catch(() => {
             this.$message({
                 type: 'info',
                 message: '已取消删除'
             });
         });

     },
     // 点击商品主图上的复选框
     changemain (item, i) {
         this.goodsImgList[i].checkedStatus = item.checkedStatus == 0 ? 1 : 0;
         this.goodsImgList[i].isRechecked = item.isRechecked == 0 ? 1 : 0;
     },
     //修改日限额select事件
     changeSelect (val) {
         this.info.stationExtendType = val
     },
    async SubmitEvent () {
         this.info.materialImageFiles = this.goodsMaterialList
         this.info.reports.reports = this.tableData
        const oldVal = JSON.parse(localStorage.getItem('oldData')) 
        const popularizeProp = {
            newData:this.info,
            oldData: oldVal
        }
         await saveActualTimeAdvData(popularizeProp)
     }
 },
 watch: {
     dateTime (newval) {
         if (newval == null && this.activeName == 'operateRec') {
             this.getOperateRecList()
             this.getOperateInfo.startTime = null
             this.getOperateInfo.endTime = null
         }
         if (newval == null && this.activeName == 'originalityProp') {
             this.getTabsProp()
         }
     },
 }
};
</script>

<style lang="scss" scoped>
.selectbox {
 margin-top: 20px;
 display: flex;
 justify-content: space-between;
 align-items: center;

 .selectIpt {
     width: 120px;
     font-size: 20px;
     color: #000;
 }
}

.limitInfo {
 margin-top: 20px;
 margin-left: 80px;
 box-sizing: border-box;
 width: 300px;
}

.money {
 font-size: 18px;
 margin-right: 10px;
 line-height: 35px;
}

.el-image__inner {
 max-width: 100px;
 width: 100px;
}

.infoipt ::v-deep .el-input__inner {
//  border: none;
 font-size: 20px;
 color: #000;
 padding: 0;
 width: 200px;
}

.btn ::v-deep .el-button {
 border: 0;
 padding: 0;
}

::v-deep .el-date-editor {
 width: 300px;
 margin-top: 20px;
 border: 2px solid #ccc;
 text-align: center;
 display: flex;
 align-items: center;
 justify-content: center;
 height: 40px;
 margin-bottom: 20px;
}

::v-deep .el-range-separator {
 line-height: 35px;
 padding: 0;
}

::v-deep .el-icon-time {
 display: none;
}

::v-deep .el-range__close-icon {
 display: none;
}

::v-deep .el-range-input {
 width: 130px;
 color: #000;
}

::v-deep .el-range-input::placeholder {
 color: black;
}

::v-deep .el-picker-panel__sidebar {
 width: 90px;
}

::v-deep .el-checkbox__inner {
 width: 20px;
 height: 20px;
 text-align: center;
 line-height: 20px;
}

::v-deep .cell {
 padding-left: 0;
}

::v-deep .el-date-range-picker {
 width: 700px;
}

::v-deep .el-checkbox__inner::after {
 left: 7px;
 top: 4px;
}

.tabs_item {
 display: flex;
 padding: 80px 20px 50px 40px;
 font-size: 20px;
 color: #000;

 .tabs_item_left {
     width: 200px;
     height: 200px;
     margin-right: 18px;
 }

 .tabs_item_right {
     display: flex;
     flex-direction: column;

     .tabs_item_right_goodsInfo {
         margin-top: 20px;
         display: flex;

         .tabs_item_right_goodsInfo_item {
             display: flex;

             &:nth-child(2) {
                 margin-left: 20px;
             }
         }
     }

     .tabs_item_right_day {
         display: flex;
         margin-top: 20px;

         .tabs_item_right_day_item {
             display: flex;
         }
     }

     .tabs_item_right_target {
         margin-top: 20px;
         display: flex;
         align-items: center;

         .tabs_item_right_target_info {
             margin-left: 10px;
         }
     }
 }
}

::v-deep .el-pagination {
 float: right;
 margin-top: 20px;
}

::v-deep .el-pagination>.el-pagination__editor>.el-input__inner {
 font-size: 14px;
}

.limitIpt ::v-deep .el-input__inner {
 height: 35px;
 line-height: 30px;
 font-size: 14px;
 padding-left: 15px;
 padding-right: 15px;
}

.dialog ::v-deep .el-dialog__title {
 font-size: 16px;
 color: #000;
 font-weight: 700;
}

.originality {
 margin: 30px 0 0 20px;
 padding: 32px 32px 20px 10px;
 box-sizing: border-box;
 border: 2px solid #ccc;
}

.originalityImg {
 padding: 15px;
 width: 98%;
 height: 200px;
 border: 2px solid #ccc;
 box-sizing: border-box;
 margin: 20px auto;
 position: relative;
 overflow: auto;

 .originalityImg_title {
     font-size: 18px;
     color: #000;
     margin-bottom: 20px;
 }

 .addsc {
     position: absolute;
     color: blue;
     right: 35px;
     top: 18px;
 }

 .mainImgBox {
     width: 100%;
     height: 100%;
     display: flex;
     flex-wrap: wrap;
    //  overflow: auto;

     .mainImag {
         width: 80px;
         height: 80px;
         position: relative;
         margin-right: 10px;

         .originalityImgCheck {
             position: absolute;
             left: 0;
             top: 0;
         }

         .imgType {
             position: absolute;
             top: 0;
             right: 0;
             width: 50px;
             text-align: center;
             background-color: #fff;
             color: #000;
             font-size: 14px;
         }
     }
 }
}

.imgcss ::v-deep img {
 min-width: 80px !important;
 min-height: 80px !important;
 width: 80px !important;
 height: 80px !important;
}

.infoImg ::v-deep img {
 min-width: 200px !important;
 min-height: 200px !important;
}

.select {
 width: 200px;
 box-sizing: border-box;
}

.selectItem {
 font-size: 14px;
}
</style>