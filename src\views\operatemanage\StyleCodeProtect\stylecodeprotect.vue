<template>
    <my-container v-loading="pageLoading">
        <template #header>

            <el-button style="padding: 0;margin: 0;border: none;">
                <el-input v-model.trim="filter.styleCode" clearable placeholder="系列编码" style="width:140px;"
                    :maxlength="20" />
            </el-button>
            <el-button style="padding: 0;margin: 0;border: none;">
                <el-select filterable v-model="filter.goodGroupIds" placeholder="挂靠小组" style="width: 170px" clearable
                    multiple collapse-tags>
                    <el-option v-for="item in directorGroupList" :key="'gk' + item.key" :label="item.value"
                        :value="item.key" />
                </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;border: none;">
                <el-select filterable v-model="filter.canGroupIds" placeholder="可上架小组" style="width: 170px" clearable
                    multiple collapse-tags>
                    <el-option v-for="item in ksjGroupList" :key="'ksj' + item.key" :label="item.value"
                        :value="item.key" />
                </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;border: none;">
                <el-select filterable clearable v-model="filter.isRise" placeholder="是否起量" style="width: 90px">
                    <el-option label="是" :value="true"></el-option>
                    <el-option label="否" :value="false"></el-option>
                </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;border: none;">
                <el-select filterable v-model="filter.protectGroupIds" placeholder="保护小组" style="width: 170px" clearable
                    multiple collapse-tags>
                    <el-option v-for="item in directorGroupList" :key="'bh' + item.key" :label="item.value"
                        :value="item.key" />
                </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;border: none;">
                <el-select filterable clearable v-model="filter.isProtect" placeholder="是否保护" style="width: 90px">
                    <el-option label="是" :value="true"></el-option>
                    <el-option label="否" :value="false"></el-option>
                </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;border: none;">
                <el-select filterable v-model="filter.aloneGroupIds" placeholder="独立发展小组" style="width: 170px" clearable
                    multiple collapse-tags>
                    <el-option v-for="item in directorGroupList" :key="'dlfz' + item.key" :label="item.value"
                        :value="item.key" />
                </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;border: none;">
                <el-select filterable clearable v-model="filter.isAlone" placeholder="是否独立发展" style="width: 90px">
                    <el-option label="是" :value="true"></el-option>
                    <el-option label="否" :value="false"></el-option>
                </el-select>
            </el-button>

            <el-button style="padding: 0;margin: 0;border: none;">
                <el-select filterable v-model="filter.brandIds" placeholder="采购员" style="width: 170px" clearable
                    multiple collapse-tags>
                    <el-option v-for="item in brandList" :key="'cgy' + item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-button>

            <el-button style="padding: 0;margin: 0;border: none;">
                <el-select filterable clearable v-model="filter.seriesNameScore" placeholder="DSR评分"
                    style="width: 90px">
                    <el-option label="合格" :value="true"></el-option>
                    <el-option label="不合格" :value="false"></el-option>
                </el-select>
            </el-button>



            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onExport">导出</el-button>
        </template>

        <vxetablebase :id="'stylecodeprotect20231019'" :border="true" :align="'center'"
            :tablekey="'stylecodeprotect20231019'" ref="table2" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols' :loading="listLoading"
            style="width:100%;height:100%;margin: 0" :xgt="9999">
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

import { getAllProBrand } from '@/api/inventory/warehouse'
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import { GetStyleCodeProtectPageList, ExportStyleCodeProtecteList, GetStyleCodeProtectAlonePageList } from '@/api/operatemanage/stylecodeprotect'

const tableCols = [
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'canGroupIds', label: '可上架小组', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'protectGroupIds', label: '保护小组', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'isProtect', label: '是否保护', sortable: 'custom', width: '80', formatter: (row) => row.isProtect == true ? "是" : "否" },
    { istrue: true, prop: 'goodGroupIds', label: '挂靠小组', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'aloneGroupIds', label: '独立发展小组', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'isAlone', label: '是否独立发展', sortable: 'custom', width: '120', formatter: (row) => row.isAlone == true ? "是" : "否" },
    { istrue: true, prop: 'isRise', label: '是否起量', sortable: 'custom', width: '80', formatter: (row) => row.isRise == true ? "是" : "否" },
    { istrue: true, prop: 'brandId', label: '采购员', sortable: 'custom', width: '80', formatter: (row) => row.brandName },
    { istrue: true, prop: 'seriesNameScore', label: '系列编码DSR', sortable: 'custom', width: '140', },
    {
        istrue: true, prop: 'seriesNameScoreJudge', label: '系列编码评分判定', sortable: 'custom', width: '150', type: "colorClick",
        style: (that, row) => that.judgeColorFormatter(row.seriesNameScoreJudge), align: 'center',
    },
    { istrue: true, prop: 'wcOrderCountRate', label: '外仓率', sortable: 'custom', width: '80', formatter: (row) => row.wcOrderCountRate + '%' },
    { istrue: true, prop: 'packageAvgFee', label: '包装均价(发生)', sortable: 'custom', width: '140' },
    { istrue: true, prop: 'freightAvgFee', label: '快递均价(发生)', sortable: 'custom', width: '140' },
];
export default {
    name: "stylecodeprotect",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, vxetablebase
    },
    data() {
        return {
            that: this,
            directorGroupList: [],
            ksjGroupList: [],
            brandList: [],
            filter: {
                styleCode: null,
                goodGroupIds: [],
                canGroupIds: [],
                protectGroupIds: [],
                aloneGroupIds: [],
            },
            tableCols: tableCols,
            total: 0,
            datalist: [],
            pager: { OrderBy: "createdTime", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            selrows: [],
        };
    },
    async mounted() {
        await this.onSearch();
        await this.getloadgroupselect();
        await this.getloadgroupselect_ksj();
    },
    async created() {
    },
    methods: {
        async getloadgroupselect() {
            const res1 = await getDirectorGroupList({});
            this.directorGroupList = res1.data;

            const res2 = await getAllProBrand();
            this.brandList = res2.data.map(item => { return { value: item.key, label: item.value }; });
        },
        async getloadgroupselect_ksj() {
            //可上架小组中不能查独立发展小组
            this.canGroupIds = [];
            this.ksjGroupList = [];
            const res3 = await GetStyleCodeProtectAlonePageList({ currentPage: 1, pageSize: 1000, IsAsc: false, OrderBy: "" });
            if (res3.data?.list) {
                let canlist = res3.data?.list.map(item => item.aloneGroupId);
                this.directorGroupList.forEach(f => {
                    if (!canlist.find(w => w == f.key)) {
                        this.ksjGroupList.push(f);
                    }
                });
            }
        },
        judgeColorFormatter(row) {
            if (row == "优秀") return "color: green";
            else if (row == "普通") return "color: blue";
            else if (row == "劣质") return "color: red";
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getCondition() {
            let pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            return params;
        },
        async getList() {
            const params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params);
            this.listLoading = true;
            const res = await GetStyleCodeProtectPageList(params);
            this.listLoading = false;
            console.log(res);
            this.total = res.data?.total;
            this.datalist = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        async onExport(opt) {
            const params = this.getCondition();
            if (params === false) {
                return;
            }
            let res = await ExportStyleCodeProtecteList(params);
            if (!res?.data) {
                return
            }
            this.$message({ message: "正在后台导出中，请点击头像-在下载管理中查看", type: "success" });
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
