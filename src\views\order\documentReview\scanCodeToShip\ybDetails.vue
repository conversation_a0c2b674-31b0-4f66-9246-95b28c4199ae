<template>
    <MyContainer>
        <template #header style="height: 90vh;">
            <div class="top">
                <el-date-picker v-model="codeTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" @change="changeTime" value-format="yyyy-MM-dd">
                </el-date-picker>
                <el-select v-model="ListInfo.status" placeholder="扫码状态" class="publicCss" clearable>
                    <el-option v-for="item in  status" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.isPrint" placeholder="面单打印" class="publicCss" clearable>
                    <el-option label="是" :value="true" />
                </el-select>
                <el-input v-model="ListInfo.newOrderNoInner" placeholder="新内部订单号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model="ListInfo.scanUserName" placeholder="请输入扫码人" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model="ListInfo.printerName" placeholder="请输入贴单人" maxlength="50" clearable
                    class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
            </div>
        </template>
        <vxetablebase :id="'ybDetails202408041757'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%; height: 680px; margin: 0" v-loading="loading" />
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { platformlist, pickerOptions, formatPlatform } from '@/utils/tools'
import { pageGetScanRecord } from '@/api/vo/prePackScan'
import middlevue from "@/store/middle.js"
import dayjs from 'dayjs'
const status = [
    { label: '无法识别', value: 0 },
    { label: '匹配成功', value: 1 },
    { label: '待再匹配', value: 2 },
    { label: '拆包', value: 3 },
    { label: '丢弃', value: 4 },
]

const tableCols = [
    { istrue: true, sortable: 'custom', width: 'auto', label: '匹配状态', prop: 'status', formatter: (row) => row.status ? status.find(item => item.value == row.status).label : null },
    { istrue: true, sortable: 'custom', width: 'auto', align: 'left', label: '实体编码', prop: 'solidCode', },
    { istrue: true, sortable: 'custom', width: 'auto', align: 'left', label: '对应编码', prop: 'combineCode', },
    { istrue: true, sortable: 'custom', width: 'auto', align: 'left', label: '近七日销量', prop: 'orderCount7Days', },
    { istrue: true, sortable: 'custom', width: 'auto', label: '新内部订单号', prop: 'newOrderNoInner', type: 'orderLogInfo', orderType: 'orderNoInner' },
    { istrue: true, sortable: 'custom', width: 'auto', align: 'left', label: '扫码时间', prop: 'scanTime', formatter: (row) => row.scanTime ? dayjs(row.scanTime).format('YYYY-MM-DD HH:mm:ss') : '' },
    { istrue: true, sortable: 'custom', width: 'auto', label: '扫码人', prop: 'scanUserName', },
    { istrue: true, sortable: 'custom', width: 'auto', label: '面单打印', prop: 'isPrint', formatter: (row) => { return row.isPrint ? '是' : '否' } },
    { istrue: true, sortable: 'custom', width: 'auto', label: '贴单人员', prop: 'printerName', },
]
export default {
    name: "ybDetails",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                status: null,//状态
                scanStartDate: null,//扫码开始时间
                scanEndDate: null,//扫码结束时间
                newOrderNoInner: null,//新内部原订单号
                scanUserName: null,//扫码人
                isPrint: null,//面单打印
                printerName: null,//贴单人员
            },
            tableCols,//表格头
            tableData: [],//表格数据
            loading: false,//加载中
            codeTimeRanges: [],//扫码时间
            pickerOptions,//时间选择器配置
            total: 0,//总条数
            status,//状态
        }
    },
    async mounted() {
        await this.getList()
        await middlevue.$on('queryDetailsList', (e) => {
            this.ListInfo.scanUserName = e.scaner
            this.ListInfo.printerName = e.printer
            this.ListInfo.scanStartDate = e.startDate
            this.ListInfo.scanEndDate = e.endDate
            this.codeTimeRanges = [e.startDate, e.endDate]
            this.getList()
        })
    },
    beforeDestroy() {
        middlevue.$off('queryDetailsList')
    },
    methods: {
        async changeTime(e) {
            this.ListInfo.scanStartDate = e ? e[0] : null
            this.ListInfo.scanEndDate = e ? e[1] : null
            await this.getList()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
            }
            const replaceArr = ['newOrderNoInner', 'scanUserName', 'printerName']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            const { data, success } = await pageGetScanRecord(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    // flex-wrap: wrap;
    .publicCss {
        width: 120px;
        margin-right: 10px;
    }
}
</style>