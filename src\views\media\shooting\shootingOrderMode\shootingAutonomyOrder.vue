<template>
    <my-container>
        <template>
          <el-form class="ad-form-query" :model="addOrderForm" ref="addOrderForm" :rules="addOrderFormRules" label-position="right" label-width="100px">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="19" :xl="19">
                            <el-form-item label="已选任务编号:">
                                <span v-for="(item, index) in selShootingTaskIdSpanList" :key="index" v-html="item"></span>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="5" :xl="5">
                            <el-form-item label="">
                                <el-button @click="onAddressSet" type="text">发货地址维护
                                </el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :hidden="true">
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                            <el-form-item label="收货人" prop="receiverName">
                                <el-input v-model="addOrderForm.receiverName" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                            <el-form-item label="收货电话" prop="receiverPhone">
                                <el-input v-model="addOrderForm.receiverPhone" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row> 
                    <el-row> 
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" :hidden="addOrderForm.isZt!=0">
                            <el-form-item label="详细地址" prop="receiverAddressAllInfo">
                                <el-select v-model="addOrderForm.receiverAddressAllInfo" placeholder="选择详细地址" style="width:100%;" @change="receiverAddressSelChange">
                                    <el-option v-for="item in receiverAddressList" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                            <el-form-item label="收货省" prop="receiverStateCode">
                                <el-select v-model="addOrderForm.receiverStateCode" placeholder="收货省" style="width:100%;" @change="receiverStateChange">
                                    <el-option v-for="item in receiverStateList" :key="item.code" :label="item.name" :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                            <el-form-item label="收货市" prop="receiverCityCode">
                                <el-select v-model="addOrderForm.receiverCityCode" placeholder="收货市" style="width:100%;" @change="receiverCityChange">
                                    <el-option v-for="item in receiverCityList" :key="item.code" :label="item.name" :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                            <el-form-item label="收货区" prop="receiverDistrictCode">
                                <el-select v-model="addOrderForm.receiverDistrictCode" placeholder="收货区" style="width:100%;">
                                    <el-option v-for="item in receiverDistrictList" :key="item.code" :label="item.name" :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" :hidden="true">
                            <el-form-item label="详细地址" prop="receiverAddress">
                                <el-input v-model="addOrderForm.receiverAddress" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="备注" prop="remark">
                                <el-input v-model="addOrderForm.remark" type="textarea" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
          </el-form>
        </template>
        <template>
            <el-row>
                <el-card class="box-card" style="width:100% ;height: 45px;overflow: hidden;">
                    <div slot="header" class="clearfix">
                        <span>商品明细</span>
                        <el-button @click="onSelctOrderGoods()" style="float: right; padding: 3px 0" type="text">添加商品明细</el-button> 
                        <el-button @click="onSyncOrderGoods()" style="float: right; padding: 3px 0;margin-right: 60px;" type="text">同步附件商品</el-button>
                    </div>
                </el-card>
                <el-card class="box-card" style="width:100% ;height: 300px;overflow: auto;">
                    <el-table :data="addOrderForm.orderGoods">
                        <el-table-column label="序号" width="50">
                            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                        </el-table-column>
                        <el-table-column prop="goodsCode" width="150" label="商品编码" />
                        <el-table-column prop="goodsName" label="商品名称" />
                        <el-table-column prop="goodsPrice" width="100" label="单价" v-if="false" />
                        <el-table-column prop="goodsQty" width="150" label="数量">
                            <template slot-scope="scope">
                                <el-input-number v-model="scope.row.goodsQty" :min="1" :max="100000000" placeholder="数量" :precision="0" @change="addOrderFormGoodsQtyChange(scope.row)">
                                </el-input-number>
                            </template>
                        </el-table-column>
                        <el-table-column prop="goodsAmount" width="100" label="总额" v-if="false" />
                        <el-table-column lable="操作" width="100">
                            <template slot-scope="scope">
                                <el-button type="danger" @click="onDelDtlGood(scope.$index)">删除 <i class="el-icon-remove-outline"></i>
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
            </el-row>
        </template>
        <template #footer>
            <span class="dialog-footer">
                <span style="font-size:10px;color:red;">点击提交按钮将发起钉钉审批，自提单审批通过即代表到样，非自提单审批通过则自动同步订单到聚水潭。&nbsp;&nbsp;</span>
                <el-button @click="dialogAddOrderVisible = false">取 消</el-button>
                <my-confirm-button type="submit" :validate="addOrderFormValidate" :loading="dialogAddOrderSubmitLoding" @click="onAddOrderSave">
                    提交
                </my-confirm-button>
            </span>
        </template>
    </my-container>
</template>
<script> 
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    export default {
        name: "Users",
        props:{
            addressList:{ type: Array, default:[] },  
            islook:{ type: Boolean, default:false }, 
            onAddressSet:{ type: Function, default:{} }, 
            
        },
        components: { MyContainer, MyConfirmButton },
        data() {
            return { 
                listLoading: false,//树列表加载
                addFormVisiblerole: false,//新增编辑显隐
                editFormLoading: false,//编辑时转圈
                addLoading: false,//新增编辑提交按钮
                scenesck: [],//列表数据集
                scenesjs: [],//列表数据集
                scenesshop: [],//列表数据集 
                isEdit: false,//是否编辑模式 
                addOrderForm: 
                {
                    shootingTaskIds: "",
                    receiverName: "",
                    receiverPhone: "",
                    receiverStateCode: "",
                    receiverCityCode: "",
                    receiverDistrictCode: "",
                    receiverState: "",
                    receiverCity: "",
                    receiverDistrict: "",
                    receiverAddress: "",
                    isZt: null,
                    warehouse: null,
                    remark: "",
                    receiverAddressAllInfo: "",
                    orderGoods: [],
                },

                addFormRules: {
                    sceneCode: [{ required: true, message: '请输入编码', trigger: 'blur' }],
                    sceneName: [{ required: true, message: '请输入名称', trigger: 'blur' }]
                }
            };
        },
        async mounted() { 
        },
        methods: {
            //获取数数据源
            async receiverAddressSelChange() {
                this.addressList.forEach(f => {
                    if (f.vedioTaskOrderAddressId == this.addOrderForm.receiverAddressAllInfo && this.addOrderForm.receiverAddressAllInfo != "") {
                        this.addOrderForm.receiverStateCode = f.receiverStateCode;
                        this.addOrderForm.receiverCityCode = f.receiverCityCode;
                        this.addOrderForm.receiverDistrictCode = f.receiverDistrictCode;
                        this.addOrderForm.receiverAddress = f.receiverAddress;
                        if (f.receiverName)
                            this.addOrderForm.receiverName = f.receiverName;
                        if (f.receiverPhone)
                            this.addOrderForm.receiverPhone = f.receiverPhone;

                        return;
                    }
                });
            ;
        },
            async getDataSetList(index) {
                 
            },
          
            async onEdit( row) {
                
            },
          
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
