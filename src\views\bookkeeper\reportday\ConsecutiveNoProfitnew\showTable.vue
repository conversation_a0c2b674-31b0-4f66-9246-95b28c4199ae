<template>
  <container>
    <!-- :sort-config="{sortMethod: customSortMethod}" -->

    <vxe-table border ref="xTable" height="100%" align="center" @sort-change="customSortMethod"
      :cell-class-name="cellClassName" :header-cell-class-name="cellClassName" :footer-cell-class-name="cellClassName"
      :column-config="{ resizable: true }" show-footer :footer-method="footerMethod" :data="list">
      <!-- <vxe-column type="seq" width="60"></vxe-column> -->
      <vxe-column field="platformName" title="平台" width="160"></vxe-column>
      <vxe-column field="groupName" title="运营组" width="160" v-if="yyz"></vxe-column>
      <vxe-column field="operateSpecialUserName" title="运营专员" width="160" v-if="yyzy"></vxe-column>
      <vxe-column field="userName" title="运营助理" width="160" v-if="yyzl"></vxe-column>
      <vxe-column field="shopName" title="店铺名称" width="160" v-if="dp"></vxe-column>


      <vxe-colgroup field="consecutiveDay3" title="3天负利润" style="background-color: #eee">
        <vxe-column field="consecutiveDay3Profit6" title="金额" width="100" sortable></vxe-column>
        <vxe-column field="consecutiveDay3" title="个数" width="100" sortable></vxe-column>
      </vxe-colgroup>
      <vxe-colgroup field="consecutiveDay5" title="5天负利润" style="background-color: #eee">
        <vxe-column field="consecutiveDay5Profit6" title="金额" width="100" sortable></vxe-column>
        <vxe-column field="consecutiveDay5" title="个数" width="100" sortable></vxe-column>
      </vxe-colgroup>
      <vxe-colgroup field="consecutiveDay7" title="7天负利润" style="background-color: #e66">
        <vxe-column field="consecutiveDay7Profit6" title="金额" width="100" sortable></vxe-column>
        <vxe-column field="consecutiveDay7" title="个数" width="100" sortable></vxe-column>
      </vxe-colgroup>
      <vxe-colgroup field="consecutiveDay10" title="10天负利润" style="background-color: #e66">
        <vxe-column field="consecutiveDay10Profit6" title="金额" width="100" sortable></vxe-column>
        <vxe-column field="consecutiveDay10" title="个数" width="100" sortable></vxe-column>
      </vxe-colgroup>
      <vxe-colgroup field="consecutiveDay15" title="15天负利润" style="background-color: #e77">
        <vxe-column field="consecutiveDay15Profit6" title="金额" width="100" sortable></vxe-column>
        <vxe-column field="consecutiveDay15" title="个数" width="100" sortable></vxe-column>
      </vxe-colgroup>
      <vxe-colgroup field="consecutiveDay30" title="30天负利润">
        <vxe-column field="consecutiveDay30Profit6" title="金额" width="100" sortable></vxe-column>
        <vxe-column field="consecutiveDay30" title="个数" width="100" sortable></vxe-column>
      </vxe-colgroup>
      <vxe-column field="date3" title="操作">
        <template #default="{ row }">
          <el-button type="text" @click="clichart(row)">趋势图</el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>


    <el-dialog :visible.sync="chartdialog" width="60%" :close-on-click-modal="false" v-loading="editLoading"
      element-loading-text="拼命加载中" v-dialogDrag :append-to-body="true">
      <el-date-picker style="width: 200px" v-model="timerange" type="datetimerange" format="yyyy-MM-dd"
        @change="getchardata" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
        end-placeholder="结束日期" :clearable="false">
      </el-date-picker>
      <buschar :charid="'charNegative1'" :filter="filter" :chartsdata="chartsdata1" v-if="chartsdata1"></buschar>
      <buschar :charid="'charNegative2'" :filter="filter" :chartsdata="chartsdata2" v-if="chartsdata2"></buschar>
      <buschar :charid="'charNegative3'" :filter="filter" :chartsdata="chartsdata3" v-if="chartsdata3"></buschar>
      <buschar :charid="'charNegative4'" :filter="filter" :chartsdata="chartsdata4" v-if="chartsdata4"></buschar>
      <buschar :charid="'charNegative5'" :filter="filter" :chartsdata="chartsdata5" v-if="chartsdata5"></buschar>
      <buschar :charid="'charNegative6'" :filter="filter" :chartsdata="chartsdata6" v-if="chartsdata6"></buschar>

    </el-dialog>
  </container>
</template>

<script>
import container from '@/components/my-container'
// import cesTable from "@/components/Table/table.vue";
import { getContinuousNoProfit, newGetContinuousNoProfitRecordAsync, newGetContinuousNoProfitRecordAnalysisAsync } from "@/api/bookkeeper/continuousprofitanalysis"
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import buschar from '@/views/bookkeeper/reportday/ConsecutiveNoProfitnew/buschar.vue'


const platformvalue = [{ label: '天猫', value: 1 }, { label: '拼多多', value: 2 }, { label: '阿里巴巴', value: 4 },
{ label: '抖音', value: 6 }, { label: '京东京喜', value: 7 }, { label: '淘工厂', value: 8 }, { label: '淘宝', value: 9 }, { label: '苏宁', value: 10 },{ label: '京东自营', value: 74 },{ label: '视频号', value: 20 },]
const tableCols = [
  { istrue: true, prop: 'consecutiveDay3Profit6', label: '连续3天负利润', tipmesg: '', sortable: 'custom', },
  { istrue: true, prop: 'consecutiveDay7Profit6', label: '连续7天负利润', tipmesg: '', sortable: 'custom', },
  { istrue: true, prop: 'consecutiveDay15Profit6', label: '连续15天负利润', tipmesg: '', sortable: 'custom', },
  { istrue: true, prop: 'consecutiveDay30Profit6', label: '连续30天负利润', tipmesg: '', sortable: 'custom' }
]

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
  name: 'ConsecutiveNoProfitShowTable',
  components: { container, buschar },
  props: { filter: {} },
  data() {
    return {
      that: this,
      editLoading: true,
      chartdialog: false,
      chifilter: {},
      timerange: [],
      isselplatform: [
        '3天负利润',
        '7天负利润',
        '15天负利润',
        '30天负利润',
      ],
      dp: false,
      yyz: false,
      yyzy: false,
      yyzl: false,

      chartsdata1: {},
      chartsdata2: {},
      chartsdata3: {},
      chartsdata4: {},
      chartsdata5: {},
      chartsdata6: {},
      lastSortArgs: {
        field: "",
        order: "",
      },
      isRemoteSort: false,
      isRemoteSortnum: 0,
      list: [],
      pager: {
        orderBy: null, isAsc: false,
        currentPage: 1,
        pageSize: 50,
      },
      tableCols: [],
      total: 0,
      sels: [],
      uploadLoading: false,
      dialogVisible: false,
      listLoading: false,
      fileList: [],
      summaryarry: {},
      rowdata: {},
      platforms: [],
      tableData: [
        { id: 10001, name: 'Test1', role: 'Develop', sex: 'Man', age: 28, address: 'test abc' },
        { id: 10002, name: 'Test2', role: 'Test', sex: 'Women', age: 22, address: 'Guangzhou' },
        { id: 10003, name: 'Test3', role: 'PM', sex: 'Man', age: 32, address: 'Shanghai' },
        { id: 10004, name: 'Test4', role: 'Designer', sex: 'Women', age: 23, address: 'test abc' },
        { id: 10005, name: 'Test5', role: 'Develop', sex: 'Women', age: 30, address: 'Shanghai' },
        { id: 10006, name: 'Test6', role: 'Designer', sex: 'Women', age: 21, address: 'test abc' },
        { id: 10007, name: 'Test7', role: 'Test', sex: 'Man', age: 29, address: 'test abc' },
        { id: 10008, name: 'Test8', role: 'Develop', sex: 'Man', age: 35, address: 'test abc' }
      ]
    };
  },

  async mounted() {
    let end = new Date();
    let start = new Date();
    start.setDate(start.getDate() - 30);
    this.timerange = [formatTime(start, "YYYY-MM-DD"), formatTime(end, "YYYY-MM-DD")]

    // if(this.filter.groupType.indexOf(2)){
    //   this.dianpu = true;
    // }else{

    // }
  },
  // watch: {
  //   filter: {
  //     handler(newVal){
  //       console.log("打印变化数据",)
  //     },
  //     immediate: true
  //   }
  // },

  methods: {
    customSortMethod({ data, sortList }) {

      console.log("打印数据1", { data, sortList })
      if (this.isRemoteSort || this.isRemoteSortnum > 0) {
        return
      }
      this.isRemoteSort = true;
      this.isRemoteSortnum += 1;
      // if(!this.isRemoteSort){
      if (sortList && sortList.length > 0) {
        if (sortList[0].field != this.lastSortArgs.field || sortList[0].order != this.lastSortArgs.order) {
          this.lastSortArgs = { ...sortList[0] };
          console.log("打印数据2", this.chifilter)
          this.chifilter.isAsc = (this.lastSortArgs.order.indexOf('desc') > -1 ? true : false);
          this.chifilter.orderBy = this.lastSortArgs.field
        }

      }
      this.getlist();
      this.isRemoteSort = false;
      console.log("打印数据3", this.filter)
      // }


    },
    cellClassName({ row, column }) {
      if (column.field === 'consecutiveDay3' || column.field === 'consecutiveDay3Profit6') {
        return 'col-thr'
      } else if (column.field === 'consecutiveDay5' || column.field === 'consecutiveDay5Profit6') {
        return 'col-four'
      } else if (column.field === 'consecutiveDay7' || column.field === 'consecutiveDay7Profit6') {
        return 'col-thr'
      } else if (column.field === 'consecutiveDay10' || column.field === 'consecutiveDay10Profit6') {
        return 'col-four'
      } else if (column.field === 'consecutiveDay15' || column.field === 'consecutiveDay15Profit6') {
        return 'col-thr'
      } else if (column.field === 'consecutiveDay30' || column.field === 'consecutiveDay30Profit6') {
        return 'col-four'
      }
      return null
    },
    getPrevious30Days(dateString) {
      const date = new Date(dateString);
      date.setDate(date.getDate() - 30);

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      const previousDate = `${year}-${month}-${day}`;
      return previousDate;
    },
    getDaysDifference(date1, date2) {
      const oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
      const firstDate = new Date(date1);
      const secondDate = new Date(date2);

      // 将日期转换为时间戳，并且取绝对值以确保始终得到正数
      const diffDays = Math.abs(Math.round((firstDate - secondDate) / oneDay));

      return diffDays;
    },
    // changedate(val){
    //   let days = this.getDaysDifference(val[0],val[1]);
    //   console.log("改变时间",days)
    //   if(days<30){
    //     this.$message.info("请选择至少一个月时间，已为您自动选择一个月时间")
    //     this.filter.startTime = this.getPrevious30Days(val[1]);
    //     this.filter.endTime = val[1];
    //   }else{
    //     this.filter.startTime = val[0];
    //     this.filter.endTime = val[1];
    //   }

    //   this.timerange = [this.filter.startTime, this.filter.endTime];


    // },
    selsChange: function (sels) {
      this.sels = sels
    },
     // 格式化函数，处理千位分隔符和小数位
     formatNumber(number){
      const absNumber = Math.abs(number);
      const options = {
            minimumFractionDigits: absNumber >= 100 ? 0 : 2,
            maximumFractionDigits: absNumber >= 100 ? 0 : 2,
                    };
        return new Intl.NumberFormat('zh-CN', options).format(number);
                     }, 
    footerMethod({ columns, data }) {
      const sums = [];
      if (!this.summaryarry)
        return sums
      var arr = Object.keys(this.summaryarry);
      if (arr.length == 0)
        return sums
      //const { columns, data } = param;
      var hashj = false;
      columns.forEach((column, index) => {
        if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
          var sum = this.summaryarry[column.property + '_sum'];
          if (sum == null) return;
          else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
          else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum.toFixed(2)
          // else sums[index] = sum.toFixed(0)
             else sums[index] = this.formatNumber(sum);
        }
        else if (index == 0) sums[index] = '合计'
        else sums[index] = ''
      });
      // if (this.summarycolumns.length == 0) {
      //     this.summarycolumns = columns;
      //     //this.initsummaryEvent();
      // }
      return [sums]
    },
    getDaysDifference(date1, date2) {
      const oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
      const firstDate = new Date(date1);
      const secondDate = new Date(date2);

      // 将日期转换为时间戳，并且取绝对值以确保始终得到正数
      const diffDays = Math.abs(Math.round((firstDate - secondDate) / oneDay));

      return diffDays;
    },
    getPrevious30Days(dateString) {
      const date = new Date(dateString);
      date.setDate(date.getDate() - 30);

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      const previousDate = `${year}-${month}-${day}`;
      return previousDate;
    },
    async clichart(row) {
      console.log(row, 'row');




      this.platforms = [platformvalue.find(item => item.label == row.platformName).value];
      this.rowdata = row;


      let end = new Date();
      let start = new Date();
      let params = [];

      let days = this.getDaysDifference(this.filter.startTime, this.filter.endTime);
      if (days < 30) {
        let startTime = this.getPrevious30Days(this.filter.endTime);
        let endTime = this.filter.endTime;

        // start.setDate(start.getDate() - 30);
        params = [startTime, endTime];
      } else {
        params = [this.filter.startTime, this.filter.endTime]
      }




      this.getchardata(params);
      // return
      // let params = {
      //   "currentPage": 1,
      //   "pageSize": 50,
      //   "groupType": 1,
      //   "orderBy": "",
      //   "isAsc": true,
      //   "startTime": "2023-10-26",
      //   "endTime": "2023-11-03",
      //   "platforms": platforms,
      //   "profitTypes": [

      //   ]
      // };

    },
    async getchardata(val) {
      this.chartsdata1 = null;
      this.chartsdata2 = null;
      this.chartsdata3 = null;
      this.chartsdata4 = null;
      this.chartsdata5 = null;
      this.chartsdata6 = null;

      let days = this.getDaysDifference(val[0], val[1]);
      console.log("改变时间", days)
      if (days < 30) {
        this.$message.info("请选择至少一个月时间，已为您自动选择一个月时间")
        this.filter.startTime = this.getPrevious30Days(val[1]);
        this.filter.endTime = val[1];
      } else {
        this.filter.startTime = val[0];
        this.filter.endTime = val[1];
      }

      this.timerange = [this.filter.startTime, this.filter.endTime];

      let params = {
        ...this.filter,
        "platforms": this.platforms,
        ...this.rowdata
      };
      console.log("点击打印", params)
      // return
      this.editLoading = true;
      const res = await newGetContinuousNoProfitRecordAnalysisAsync(params)
      this.listLoading = false

      if (!res?.success) {
        return
      }

      res.data.map((item) => {
        if (item.platformName == '3天负利润') {
          this.chartsdata1 = item;
        } else if (item.platformName == '5天负利润') {
          this.chartsdata2 = item;
        }else if (item.platformName == '7天负利润') {
          this.chartsdata3 = item;
        } else if (item.platformName == '10天负利润') {
          this.chartsdata4 = item;
        }else if (item.platformName == '15天负利润') {
          this.chartsdata5 = item;
        } else if (item.platformName == '30天负利润') {
          this.chartsdata6 = item;
        }
      })
      this.chartdialog = true;
      this.editLoading = false;

      console.log("打印返回数据", res.data)
    },
    //查询第一页
    async onSearch() {

      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    async getlist() {
      // let tableColsGroups = [];
      // if (this.filter.groupType=="platform") {
      //   tableColsGroups.push({ istrue: true, prop: 'name', label: '平台', width: '200', sortable: 'custom',formatter:(row)=>row.platformName });
      // }else if (this.filter.groupType=="shopcode") {
      //   tableColsGroups.push({ istrue: true, prop: 'name', label: '店铺', width: '200', sortable: 'custom',formatter:(row)=>row.shopName });
      // }else if (this.filter.groupType=="groupid") {
      //   tableColsGroups.push({ istrue: true, prop: 'name', label: '运营组', width: '200', sortable: 'custom',formatter:(row)=>row.groupName});
      // }else if (this.filter.groupType=="operatespecialuserid") {
      //   tableColsGroups.push({ istrue: true, prop: 'name', label: '运营专员', width: '200', sortable: 'custom',formatter:(row)=>row.operateSpecialUserName });
      // }

      // tableCols.forEach(item => {
      //   tableColsGroups.push(item)
      // });
      // this.tableCols = tableColsGroups;

      console.log("打印数据", this.filter)

      this.filter.groupTypes.indexOf(2) != -1 ? this.dp = true : this.dp = false;
      this.filter.groupTypes.indexOf(3) != -1 ? this.yyz = true : this.yyz = false;
      this.filter.groupTypes.indexOf(4) != -1 ? this.yyzy = true : this.yyzy = false;
      this.filter.groupTypes.indexOf(5) != -1 ? this.yyzl = true : this.yyzl = false;


      let pager = this.$refs.pager.getPager();
      let page = this.pager;
      // const params = { ...pager, ...page, ... this.filter }
      // let params = {
      //   "currentPage": 1,
      //   "pageSize": 50,
      //   "groupType": 1,
      //   "orderBy": "",
      //   "isAsc": true,
      //   "startTime": "2023-11-01",
      //   "endTime": "2023-11-03",
      //   "platforms": [

      //   ],
      //   "profitTypes": [

      //   ]
      // };
      let params = {
        ...page,
        ...pager,

        ...this.filter,
        ...this.chifilter

        // "startTime": this.timerange[0],
        // "endTime": this.timerange[1],

      };
      if (params === false) {
        return;
      }
      this.listLoading = true
      // const res = await getContinuousNoProfit(params)
      // this.listLoading = false
      // if (!res?.success) {
      //   return
      // }

      const res = await newGetContinuousNoProfitRecordAsync(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }

      this.summaryarry =
        this.total = res.data.total;
      const data = res.data.list;
      this.list = data
      this.summaryarry = res.data.summary;
      this.isRemoteSortnum = 0;
    },
    async nSearch() {
      await this.getlist()
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .col-one {
  background-color: #efebf0da !important;
  // color: #eeeeeeda !important;
}

::v-deep .col-two {
  background-color: #d6e6d1e8 !important;
}

::v-deep .col-thr {
  background-color: #f1efdc !important;
}

::v-deep .col-four {
  background-color: #d9e8f3 !important;
}
</style>
