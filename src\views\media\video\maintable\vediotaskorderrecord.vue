<template>
  <div>
    <div>
      <el-row style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black;margin-bottom: 2px;">
          <span>下单发货信息：</span>
      </el-row>
      <el-container>
          <el-main style="height:192px;">
              <ces-table ref="tablexdfhmain" :that='that' :isIndex='true' :hasexpand='true' :tableData='xdfhmainlist' :tableCols='xdfhmainTableCols' :loading="xdfhmainLoading" style="height:190px" :isSelectColumn="false" @cellclick="onxdfhmainCellClick">
              </ces-table>
          </el-main>
      </el-container>
      <el-row style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black; margin-top: 10px;margin-bottom: 2px;">
          <span>下单发货商品明细信息：</span>
      </el-row>
      <el-container>
            <el-main style="height:262px;">
                <ces-table ref="tablexdfhdtl" :that='that' :isIndex='true' :hasexpand='true' :tableData='xdfhdtllist' :tableCols='xdfhdtlTableCols' :loading="xdfhdtlLoading" style="height:260px" :isSelectColumn="false">
                </ces-table>
            </el-main>
      </el-container>
    </div>
    <el-drawer title="物流跟踪" v-if="drawervisible" :visible.sync="drawervisible" direction="rtl" :append-to-body="true">
        <logistics ref="logistics"></logistics>
    </el-drawer> 
    <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag>
        <orderLogPage ref="orderLogPage" :orderNoInner="sendOrderNoInner" style="z-index:10000;height:600px" />
    </el-dialog>
  </div>
</template>
<script>     
import acesTable from "@/components/Table/table.vue";
import logistics from '@/components/Comm/logistics'
import orderLogPage from "@/views/order/logisticsWarning/orderLogPage.vue"; 
const xdfhmainTableCols = [
    { istrue: true, prop: 'shootingTaskId', label: '当前任务', width: '80' },
    { istrue: true, prop: 'shootingTaskIds', label: '涉及任务', width: '100' },
    { istrue: true, prop: 'shootingTaskOrderId', label: '下单号', width: '70' },
    { istrue: true, prop: 'createdUserName', label: '下单人', width: '70' },
    { istrue: true, prop: 'receiverName', label: '收货人', width: '70' },
    { istrue: true, prop: 'receiverPhone', label: '收货电话', width: '80' },
    { istrue: true, prop: 'receiverState', label: '收货省', width: '70' },
    { istrue: true, prop: 'receiverCity', label: '收货市', width: '80' },
    { istrue: true, prop: 'receiverDistrict', label: '收货区', width: '80' },
    { istrue: true, prop: 'receiverAddress', label: '收货地址' },
    { istrue: true, prop: 'sampleRrderNo', label: '聚水潭内部单号', width: '120', type: 'click', handle: (that, row) => that.showLogDetail(row)},
    { istrue: true, prop: 'sampleExpressNo', label: '快递单号', width: '120', type: 'click', handle: (that, row, column) => that.onShowExproessHttp(row)},
    { istrue: true, prop: 'sampleExpressCom', label: '物流公司', width: '80' },
    { istrue: true, prop: 'arrivalDate', label: '到货日期', width: '100', formatter: (row) => row.arrivalDate == null ? null : formatTime(row.arrivalDate, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'isDy', label: '是否到样', width: '80', formatter: (row) => row.isDy == 1 ? "是" : "否" },
    { istrue: true, prop: 'isZt', label: '是否自提', width: '80', formatter: (row) => row.isZt == 1 ? "是" : "否" },
    { istrue: true, prop: 'approveStateName', label: '状态', width: '80'},
    { istrue: true, prop: 'createdTime', label: '创建时间', width: '150', formatter: (row) => row.createdTime == null ? null : formatTime(row.createdTime, 'YYYY-MM-DD HH:mm:ss') },
];
const xdfhdtlTableCols = [
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '200' },
    { istrue: true, prop: 'goodsName', label: '商品名称' },
    { istrue: true, prop: 'goodsPrice', label: '单价', width: '120', display: false },
    { istrue: true, prop: 'goodsQty', label: '数量', width: '120' },
    { istrue: true, prop: 'goodsAmount', label: '总额', width: '120', display: false },
];
export default {
    components: {acesTable,logistics,orderLogPage}, 
    props:{
        xdfhmainlist:{ type: Array, default:[] },  
        xdfhdtllist:{ type: Array, default:[] },   
        orderType:{ type: Number, default: 1},//20230311 封装模块 1.包装设计下单 
        //批量操作方法 
    },
    data() {
        return {
            xdfhmainlist: [],
            xdfhmainTableCols: xdfhmainTableCols,
            xdfhmainLoading: false,

            xdfhdtllist: [],
            xdfhdtlTableCols: xdfhdtlTableCols,
            xdfhdtlLoading: false,

            drawervisible: false,
            dialogHisVisible: false, 
        };
    }, 
    async mounted() {  }, 
    methods: {
      async onShowExproessHttp(row) {
            this.drawervisible = true;
            let expressNo=row.expressNo;
            if(!expressNo)
            {
                expressNo=row.sampleExpressNo;
            }
            this.$nextTick(function () {
                this.$refs.logistics.showlogistics("",expressNo);
            })
        },
        showLogDetail (row) {
            this.dialogHisVisible = true;
            let sampleRrderNo=row.sampleRrderNo;
            if(!sampleRrderNo)
            {
                sampleRrderNo=row.orderNoInner;
            }
            this.sendOrderNoInner = sampleRrderNo;
        },
    }
};
</script>
 