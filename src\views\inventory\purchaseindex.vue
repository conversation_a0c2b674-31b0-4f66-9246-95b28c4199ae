<template>
  <container v-loading="pageLoading">
    <template #header>
      <!-- <el-button-group> -->
        <el-button style="padding: 0;margin-left: 0;">
          <inputYunhan :key="'1'" :keys="'one'" :width="'150px'" ref="childBuyNo" v-model.trim="filter.buyNo" :maxRows="2000"
            v-if="inputedit" :inputt.sync="filter.buyNo" placeholder="采购单号" :clearable="true" @callback="callbackBuyNo" @entersearch="entersearch"
            title="采购单号"></inputYunhan>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <inputYunhan :key="'2'" :keys="'two'" :width="'150px'" ref="childIndexNo" :inputt.sync="filter.indexNo"
            v-model.trim="filter.indexNo" placeholder="Erp编号" :clearable="true" @callback="callback" title="Erp编号"  @entersearch="entersearch">
          </inputYunhan>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select v-model="filter.status" multiple clearable collapse-tags placeholder="请选择状态" style="width: 150px">
            <el-option label="待审核" value="待审核" />
            <el-option label="完成" value="完成" />
            <el-option label="已确认" value="已确认" />
            <el-option label="作废" value="作废" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-tooltip class="item" effect="dark" content="默认为精确匹配在开始或者结尾输入*进行模糊匹配" placement="top">
            <inputYunhan :key="'3'" :keys="'three'" :width="'150px'" ref="childGoodsCode" v-model="filter.goodsCode" :inputt.sync="filter.goodsCode"
              placeholder="商品编码" :clearable="true" @callback="callbackGoodsCode" title="商品编码"  @entersearch="entersearch"></inputYunhan>
          </el-tooltip>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <span style="font-weight: 600;">采购日期:</span>
          <el-date-picker style="width: 210px" v-model="filter.timerangecg" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" :default-time="['00:00:00', '23:59:59']" range-separator="至"
            start-placeholder="采购日期" end-placeholder="采购日期" :clearable="false"></el-date-picker>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <span style="font-weight: 600;">审核日期:</span>
          <el-date-picker style="width: 210px" v-model="filter.timerangesh" type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']"
            range-separator="至" start-placeholder="审核日期" end-placeholder="审核日期"></el-date-picker>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <span style="font-weight: 600;">入库日期:</span>
          <el-date-picker style="width: 260px" v-model="filter.timerangerk" type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="入库日期"
            end-placeholder="入库日期"></el-date-picker>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-input v-model.trim="filter.supplier" placeholder="请输入供应商" clearable style="width: 130px" />
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select v-model="filter.isJstSuccess" multiple clearable collapse-tags placeholder="请选择钉钉审批状态"
            style="width: 120px">
            <el-option label="未提交" value="0" />
            <el-option label="审核中" value="1" />
            <el-option label="通过" value="2" />
            <el-option label="拒绝" value="3" />
            <el-option label="撤销" value="4" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select filterable v-model="filter.company" @change="changeSetCompany" collapse-tags clearable
            placeholder="分公司" style="width: 120px">
            <el-option key="义乌" label="义乌" value="义乌"></el-option>
            <el-option key="南昌" label="南昌" value="南昌"></el-option>
            <el-option key="武汉" label="武汉" value="武汉"></el-option>
            <el-option key="深圳" label="深圳" value="深圳"></el-option>
            <el-option key="其他" label="其他" value="其他"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select ref="filterbrandId" v-model="filter.brandId" multiple collapse-tags filterable clearable
            placeholder="请选择采购员" style="width: 200px">
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select v-model="filter.informStatus" clearable placeholder="请选择来源" style="width: 120px">
            <!-- <el-option label="未通知" value="0" />
            <el-option label="已通知" value="1" /> -->
            <el-option label="申报" value="2" />
            <el-option label="新品" value="3" />
            <el-option label="自动" value="4" />
            <el-option label="生成" value="5" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select clearable v-model="filter.PaymentStatus" multiple collapse-tags placeholder="请选择付款状态"
            style="width: 130px">
            <el-option label="已付款" value="v1"></el-option>
            <el-option label="未付款" value="v2"></el-option>
            <el-option label="超额付款" value="v3"></el-option>
            <el-option label="部分付款" value="v4"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select v-model="filter.receivStatus" multiple clearable collapse-tags placeholder="请选择收货状态"
            style="width: 130px">
            <el-option label="预计收货超时" value="预计收货超时" />
            <el-option label="全部入库" value="全部入库" />
            <el-option label="部分入库" value="部分入库" />
            <el-option label="未入库" value="未入库" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select v-model="filter.isError" placeholder="请选择进货仓状态" style="width: 130px">
            <el-option label="所有" value></el-option>
            <el-option label="正常" value='false'></el-option>
            <el-option label="异常" value="true"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select v-model="filter.isOutStock" placeholder="请选择缺货状态" style="width: 130px">
            <el-option label="所有" value></el-option>
            <el-option label="正常" value='false'></el-option>
            <el-option label="缺货" value="true"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select v-model="filter.warehouse" clearable filterable placeholder="请选择仓库" style="width: 130px">
            <el-option v-for="item in warehouselist" :key="item.name" :label="item.name" :value="item.wms_co_id" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-input v-model="filter.checker" placeholder="请输入审核人" clearable style="width: 130px" />
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select v-model="filter.isAliChangePrice" clearable placeholder="是否改价" style="width: 130px">
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0; height: 32px;" class="my-el-cascader">
                <el-cascader
                style="width: 330px;"
                placeholder="发货地"
                collapse-tags
                clearable
                :options="options1"
                :props="{ multiple: true, checkStrictly: true, filterable: true }"

                v-model="filter.provinceCityDistrict"
                filterable></el-cascader>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
            <el-select v-model="filter.isProxyShipping" clearable placeholder="是否能代发" style="width: 110px">
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-input v-model="filter.printOrderPlatform" maxlength="20" placeholder="打单平台名称" clearable style="width: 110px" />
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select v-model="filter.supplierShipmentPlace" placeholder="供应商发货地"  multiple collapse-tags clearable style="width: 140px;">
            <el-option key="一致" value="一致" label="一致"></el-option>
            <el-option key="不一致" value="不一致" label="不一致"></el-option>
            <el-option key="无法匹配" value="无法匹配" label="无法匹配"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left:0;">
          <el-select v-model="filter.isProSelGoods" placeholder="是否为选品中心的货物" collapse-tags clearable style="width: 180px;">
            <el-option key="是" value="是" label="是"></el-option>
            <el-option key="否" value="否" label="否"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select v-model="filter.isAllot" placeholder="是否调拨单" clearable style="width: 140px;">
            <el-option key="是" :value=true label="是"></el-option>
            <el-option key="否" :value=false label="否"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <inputYunhan ref="reflabels" :inputt.sync="filter.styleCodes" v-model="filter.styleCodes" title="款式编码"
            placeholder="款式编码/按回车输入多条" :clearable="true" :clearabletext="true" :maxRows="200" :maxlength="100000"
            @callback="clllbackStyleCode" :width="'150px'">
          </inputYunhan>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <!-- <yhUserselectors :value.sync="filter.userIdList" clearable placeholder="采购员" ></yhUserselectors> -->
          <el-select v-model.trim="filter.deptIdList" filterable clearable multiple collapse-tags placeholder="架构"
            style="width: 175px">
            <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select v-model="filter.isOpenInvoice" clearable filterable placeholder="是否能开票" style="width: 100px">
            <el-option value="是" label="是" />
            <el-option value="否" label="否" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select v-model="filter.supplierIsOfPublicPay" clearable filterable placeholder="货款对公/对私" style="width: 100px">
            <el-option value="货款对公" label="货款对公" />
            <el-option value="货款对私" label="货款对私" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select v-model="filter.invoiceType" clearable filterable placeholder="发票类型" style="width: 100px">
            <el-option value="普票" label="普票" />
            <el-option value="专票" label="专票" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-select v-model="filter.supplierTaxRateCondition" clearable filterable placeholder="税点类型" style="width: 80px" @change="taxTypeChange">
            <el-option value="大于" label="大于" />
            <el-option value="等于" label="等于" />
            <el-option value="小于" label="小于" />
            <el-option value="介于" label="介于" />
          </el-select>
          <el-input-number :controls="false" :min="0.1" :max="999" :precision="1"
              :placeholder="filter.supplierTaxRateCondition == '介于' ? '税点(%)最小值' : '税点(%)'" style="width: 80px" v-model="filter.supplierMinTaxRate" />
          <el-input-number :controls="false" :min="0.1" :max="999" :precision="1" v-show="filter.supplierTaxRateCondition == '介于'"
              placeholder="税点(%)最大值" style="width: 80px" v-model="filter.supplierMaxTaxRate" />
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-button type="primary" style="margin-left: 3px;" v-throttle="3000" @click="onSearch">查询</el-button>
        </el-button>
        <el-button style="padding: 0;margin-left: 0;">
          <el-button @click="removefilter" v-throttle="3000">清空</el-button>
        </el-button>
        <el-switch
          v-model="filter.unShowSum" active-text="不加载汇总"
          active-color="#13ce66"
          inactive-color="#ff4949">
        </el-switch>
      <!-- </el-button-group> -->
    </template>
    <el-tabs v-model="activeName" style="height: 94%;">
      <el-tab-pane lazy label="采购单" name="first" style="height: 100%;">
        <purchaseorder :filter="filter" :options1="options1" ref="purchaseorder" />
      </el-tab-pane>
      <el-tab-pane lazy label="付款单" v-if="checkPermission('purchaseindexpayment')" name="second" style="height: 100%;">
        <purchaseindexpayment :filter="filter" ref="purchaseindexpayment" />

      </el-tab-pane>
      <el-tab-pane lazy label="退款单" v-if="checkPermission('purchaserefundSingle')" name="fourth" style="height: 100%;">
        <purchaserefundSingle :filter="filter" ref="purchaserefundSingle" />

      </el-tab-pane>
      <el-tab-pane lazy label="供应商账目明细" v-if="checkPermission('purchasesupplierdetail2')" name="third"
        style="height: 100%;">
        <purchasesupplierdetail2 :filter="filter" ref="purchasesupplierdetail2" />

      </el-tab-pane>
      <el-tab-pane lazy label="采购退货详情" v-if="checkPermission('PurchaseReturnGoodsDetail')" name="fifth"
        style="height: 100%;">
        <PurchaseReturnGoodsDetail :filter="filter" ref="PurchaseReturnGoodsDetail" />

      </el-tab-pane>
      <el-tab-pane lazy label="退款" name="refund"
        style="height: 100%;">
        <refundModule :filter="filter" ref="refundModule" />
      </el-tab-pane>
    </el-tabs>

  </container>
</template>
<script>
import inputYunhan from "@/components/Comm/inputYunhan";
import { formatTime, formatYesornoBool, formatWarehouseArea, formatNoLink, formatIsError, formatIsOutStock, formatSecondToHour } from "@/utils/tools";
import dayjs from "dayjs";
import { getAllProBrand, getAllWarehouse } from '@/api/inventory/warehouse'
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import logistics from '@/components/Comm/logistics'
import goodscoderecord from '@/views/inventory/goodscoderecord'
import purchaseorder from '@/views/inventory/purchaseorder'
import purchaseindexpayment from '@/views/inventory/purchaseindexpayment'
import purchaserefundSingle from '@/views/inventory/purchaserefundSingle'
import purchasesupplierdetail2 from '@/views/inventory/purchasesupplierdetail2'
import PurchaseReturnGoodsDetail from '@/views/inventory/PurchaseReturnGoodsDetail'
import refundModule from '@/views/inventory/purchaseOrderRefund/refundModule'
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";
import purchasehistorytjanalysis from '@/views/inventory/components/purchasehistorytjanalysis'
import {
    GetProvinceCityDistrict
} from '@/api/inventory/purchase'
import { getPurchaseNewPlanTurnDayDeptList } from '@/api/inventory/purchaseordernew'

const startDate = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
  name: "Users",
  components: { container, cesTable, MyConfirmButton, logistics, goodscoderecord, purchasehistorytjanalysis, purchaseorder, purchaseindexpayment, purchasesupplierdetail2, purchaserefundSingle, inputYunhan, PurchaseReturnGoodsDetail, refundModule },
  data() {
    return {
      options1:[],
      activeName: 'first',
      that: this,
      formatWarehouseArea: formatWarehouseArea,
      formatYesornoBool: formatYesornoBool,
      formatTime: formatTime,
      formatIsOutStock: formatIsOutStock,
      formatSecondToHour: formatSecondToHour,
      filter: {
        timerangecg: [startDate, endDate],
        timerangesh: null,
        timerangerk: null,
        timerangefk: null,
        startPurchaseDate: null,
        endPurchaseDate: null,
        startCheckDate: null,
        endCheckDate: null,
        buyNo: null,
        indexNo: null,
        supplier: null,
        goodsCode: null,
        brandId: null,
        checker: null,
        unShowSum:true,
        status: "",
        isJstSuccess: null,
        receivStatus: null,
        isError: null,
        isOutStock: null,
        warehouse: null,
        isAllot: null,
        styleCodes: null,
        deptIdList: [],
        supplierMinTaxRate: undefined,
        supplierMaxTaxRate: undefined,
        supplierIsOfPublicPay: null,
        invoiceType: null,
        isOpenInvoice: null,
        isProxyShipping: null,
        provinceCityDistrict: null,
      },
      purchasegrouplist: [],//架构
      goodscoderecordfilter: { goodsCode: "", buyNo: "" },
      imgPreview: { img: "", show: false },
      lastUpdateTime: "",
      brandlist: [],
      brandlist1: [],
      list: [],
      warehouselist: [],
      detaillist: [],
      oderDetailView: {},
      drawervisible: false,
      dialoganalysisVisible: false,
      visiblepopover: false,
      prevTarget: null, // 编辑 Popover 的 Reference （参照），用于 popover.js 对齐两个元素
      popperFlag: false, // 用于编辑 Popover 的刷新
      visiblepopoverdetail: false,
      dialogOrderDetailVisible: false,
      popperFlagdetail: false,

      pager: { OrderBy: "", IsAsc: false },
      summaryarry: {},
      total: 0,
      sels: [],

      fileList: [],

      dialogVisible: false,
      pageLoading: false,
      editVisible: false,
      editLoading: false,
      uploadLoading: false,
      hackReset: false,
      inputedit: true,
      goodscoderecord1id: +new Date(),
      autoform: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
        rule: []
      },
    };
  },
  watch: {
    value(n) {
      if (n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    },

  },
  created() {
    this.inputedit = false;
  },
  async mounted() {
    //window.changetabs = this.changetabs;
    if (this.$route.query.t) { }
    if (this.$route.query && this.$route.query.buyNo) {
      this.filter.buyNo = this.$route.query.buyNo;
      this.filter.status = "";
    }
    else if (this.$route.query && this.$route.query.goodscodes) {
      this.filter.goodsCode = this.$route.query.goodscodes;
      this.filter.status = "";
    }
    else if (this.$route.query && this.$route.query.indexNo) {
      this.filter.indexNo = this.$route.query.indexNo;
      this.filter.status = "";
    }
    else
      this.filter.status = "";

    setTimeout(() => {
      this.inputedit = true;
    }, 800)
    // if(this.filter.buyNo||this.filter.goodsCode){
    //   this.inputedit = true;
    // }

    formCreate.component('editor', FcEditor);
    // await this.initform();
    await this.init();
    await this.onSearch()
    await this.getGetProvinceCityDistrict();
  },
  methods: {
    taxTypeChange(e){
      this.filter.supplierMaxTaxRate = undefined
      this.filter.supplierMinTaxRate = undefined
    },
    async getGetProvinceCityDistrict(){
        const { data, success } =  await GetProvinceCityDistrict();
        if (!success) {
            return;
        }
        this.options1 = data?data:[];

    },
    async callback(val) {
      this.filter.indexNo = val;
      //this.onSearch();
    },
    async entersearch(val) {
      // this.filter.indexNo = val;
      this.onSearch();
    },
    async changetabs() {
      this.inputedit = false;
      if (this.$route.query.t)
        //this.onSearch()
        if (this.$route.query && this.$route.query.buyNo) {
          this.filter.buyNo = this.$route.query.buyNo;
          this.filter.status = "";
        }
        else if (this.$route.query && this.$route.query.goodscodes) {
          this.filter.goodsCode = this.$route.query.goodscodes;
          this.filter.status = "";
        }
        else
          this.filter.status = "";

      // if(this.filter.buyNo||this.filter.goodsCode){
      //   // this.$nextTick(()=>{
      //   //   this.inputedit = true;
      //   // })
      //   this.inputedit = true;
      // }
      this.inputedit = true;
      setTimeout(() => {
        if (this.activeName == 'first') this.$refs.purchaseorder.getlist();
        if (this.activeName == 'second') this.$refs.purchaseindexpayment.getlist();
        if (this.activeName == 'fourth') this.$refs.purchaserefundSingle.getlist();
      })

    },
    async callbackBuyNo(val) {
      // this.inputedit = true;
      this.filter.buyNo = val;
      //this.onSearch();
    },
    async callbackGoodsCode(val) {
      // this.inputedit = true;
      this.filter.goodsCode = val;
      //this.onSearch();
    },
    async init() {
      var res2 = await getAllProBrand();
      this.brandlist1 = res2.data;
      this.brandlist = res2.data.map(item => {
        return { value: item.key, label: item.value };
      });

      var res3 = await getAllWarehouse();
      this.warehouselist = res3.data.filter((x) => x.name.indexOf('代发') < 0);
      //   var res3= await getLastUpdateTimeyPurchase();
      //   this.lastUpdateTime= "最晚更新时间:"+res3.data

      //架构
      let { data: deptList, success } = await getPurchaseNewPlanTurnDayDeptList();
      if (success) {
        this.purchasegrouplist = deptList.map(item => { return { value: item.dept_id, label: item.full_name }; });
      }
    },
    async changeSetCompany() {
      if (this.filter.company === '义乌' || this.filter.company === '南昌') {
        this.brandlist = this.brandlist1.filter(f => f.company === this.filter.company).map(item => {
          return { value: item.key, label: item.value };
        });
      } else if (this.filter.company === '其他') {
        this.brandlist = this.brandlist1.filter(f => f.company !== '南昌' && f.company !== '义乌').map(item => {
          return { value: item.key, label: item.value };
        });
      } else {
        this.brandlist = this.brandlist1.map(item => {
          return { value: item.key, label: item.value };
        });
      }
      this.filter.brandId = null;
    },
    async onSearch() {
      setTimeout(() => {
        if (this.activeName == 'first') this.$refs.purchaseorder.onSearch();
        if (this.activeName == 'second') this.$refs.purchaseindexpayment.onSearch();
        if (this.activeName == 'fourth') this.$refs.purchaserefundSingle.onSearch();
      }, 500)
    },
    async removefilter() {
      this.filter = {
        timerangecg: [startDate, endDate],
        timerangesh: null,
        timerangerk: null,
        timerangefk: null,
        startPurchaseDate: null,
        endPurchaseDate: null,
        startCheckDate: null,
        endCheckDate: null,
        buyNo: null,
        indexNo: null,
        supplier: null,
        goodsCode: null,
        brandId: null,
        checker: null,
        unShowSum:true,
        status: "",
        isJstSuccess: null,
        receivStatus: null,
        isError: null,
        supplierMinTaxRate: undefined,
        supplierMaxTaxRate: undefined,
        supplierIsOfPublicPay: null,
        invoiceType: null,
        isOpenInvoice: null,
        isProxyShipping: null,
        provinceCityDistrict: null,
        isOutStock: null
      }
      //清空组件的值
      this.$refs.childBuyNo.input = "";
      this.$refs.childIndexNo.input = "";
      this.$refs.childGoodsCode.input = "";
    },
    async clllbackStyleCode(val) {
      this.filter.styleCodes = val;
    }
  },
};
</script>
<style lang="scss" scoped>
.imgDolg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 9999;
  background-color: rgba(140, 134, 134, 0.6);
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  #imgDolgClose {
    position: fixed;
    top: 35px;
    cursor: pointer;
    right: 7%;
    font-size: 50px;
    color: white;
  }

  img {
    width: 80%;
  }
}

.my-el-cascader ::v-deep .el-input__inner{
    height: 30px !important;
}
.my-el-cascader ::v-deep .el-cascader__tags{
    height: 22px !important;
    padding-left: 5px !important;
    left: -13px !important;
    top: 45% !important;
    margin-left: 10px !important;
}
.my-el-cascader ::v-deep .el-input__inner{
    padding-left: 17px !important;
}

::v-deep .cell {
    padding: 0 5px !important;
}

//控制选择器多选-标签宽度
::v-deep .el-select__tags-text {
  max-width: 30px;
}

// .my-el-cascader ::v-deep .el-cascader__search-input{
//    display: none;
// }
</style>

