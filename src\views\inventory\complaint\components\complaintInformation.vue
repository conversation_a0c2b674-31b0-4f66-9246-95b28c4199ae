<template>
  <div style="padding: 0 5px" v-loading="createLoading">
    <el-form :model="ruleForm" :rules="editrules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
      <el-row>
        <el-col :span="8">
          <el-form-item label="产品ID" :label-width="'110px'">
            <span v-if="!ruleForm.goodsCode || ruleForm.proCode"
              style="color: #F56C6C; margin: 0 58px 0 -65px;">*</span>
            <el-tooltip class="item" effect="dark" content="产品ID或商品编码需必填一个" placement="top">
              <el-input v-model.trim="ruleForm.proCode" placeholder="产品ID" maxlength="50" clearable class="editCss"
                @blur="handleCodeBlurMethod('proCode')" />
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="款式编码" :label-width="'110px'">
            <el-input v-model.trim="ruleForm.styleCode" placeholder="款式编码" maxlength="50" clearable class="editCss" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="投诉类型" :label-width="'110px'" prop="complaintType">
            <el-input v-model.trim="ruleForm.complaintType" placeholder="投诉类型" maxlength="50" clearable
              class="editCss" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="商品编码" :label-width="'110px'">
            <span v-if="!ruleForm.proCode || ruleForm.goodsCode"
              style="color: #F56C6C; margin: 0 71px 0 -78px;">*</span>
            <el-input v-model.trim="ruleForm.goodsCode" placeholder="商品编码" maxlength="50" clearable class="editCss"
              @blur="handleCodeBlurMethod('goodsCode')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="商品名称" :label-width="'110px'">
            <el-input v-model.trim="ruleForm.goodsName" placeholder="商品名称" maxlength="50" clearable class="editCss" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="产品负责采购" :label-width="'110px'">
            <el-select v-model="ruleForm.brandNameID" placeholder="产品负责采购" class="editCss" clearable filterable>
              <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="产品负责专员" :label-width="'110px'">
            <el-select v-model="ruleForm.operateSpecialUserNameID" placeholder="产品负责专员" class="editCss" clearable
              filterable>
              <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="产品负责助理" :label-width="'110px'">
            <el-select v-model="ruleForm.operateAssistantNameID" placeholder="产品负责助理" class="editCss" clearable
              filterable>
              <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="产品挂靠组" :label-width="'110px'">
            <el-select v-model="ruleForm.groupNameID" placeholder="产品挂靠组" class="editCss" clearable filterable>
              <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="订单编号" :label-width="'110px'" prop="orderNo">
            <el-input v-model.trim="ruleForm.orderNo" placeholder="订单编号" maxlength="50" clearable class="editCss" />
          </el-form-item>
        </el-col>

      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="附件" :label-width="'110px'">
            <!-- <uploadimgFile ref="uploadimgFile1" v-if="subcomponent" :disabled="forbidden" :accepttyes="accepttyes"
                :noDel="formEditMode || forbidden" :reveal="false" :uploadInfo="ruleForm.attachment" :keys="[1, 1]"
                :filemaxsize="1" :imgmaxsize="5" :minisize='false'>
              </uploadimgFile> -->
            <YhImgUpload3 :value.sync="ruleForm.annex" :isImg="false" accept=".pdf" :limit="9" :ismultiple="true">
            </YhImgUpload3>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="图片上传" :label-width="'110px'">
            <div class="containerCss">
              <!-- <uploadimgFile ref="uploadimgFile" v-if="subcomponent" :disabled="isView || forbidden"
                :reveal="!forbidden" :ispaste="!isView || forbidden" :noDel="isView || forbidden"
                :accepttyes="accepttyes" :isImage="true" :uploadInfo="ruleForm.screenshot" :keys="[1, 1]"
                @callback="getImg" @beforeUpload="beforeUpload" :imgmaxsize="6" :limit="6" :multiple="true"
                :minisize='false'>
              </uploadimgFile> -->
              <YhImgUpload3 :value.sync="ruleForm.picture" :accept="'.jpg,.jpeg,.png,.bmp,.gif'" :limit="10"
                ref="goodFrontBackImgs" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :span="24">
        <el-form-item label="投诉原因" :label-width="'110px'" prop="complaintReason">
          <el-input type="textarea" v-model="ruleForm.complaintReason" :autosize="{ minRows: 8, maxRows: 8 }"
            show-word-limit placeholder="请输入投诉原因" maxlength="500" clearable class="editCss" resize="none" />
        </el-form-item>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { addBusinessComplaintRegister, getAttachGoodsCodeData, getAttachProCodeData } from '@/api/operatemanage/operate'
import { addOrUpdateExpressFeeRecharge, deleteExpressFeeRecharge, getExpressFeeRechargeList, getExpressComanyAll, getExpressComanyStationName, getExpressDayBillsSummary, getExpressBankInfoList, getExpressFeeRecharge } from "@/api/express/express";
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import decimal from "@/utils/decimalToFixed"
import { warehouselist, formatWarehouseNew } from "@/utils/tools";
import dayjs from 'dayjs'
import html2canvas from 'html2canvas';
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
import YhImgUpload3 from "@/components/upload/yh-img-upload3.vue";
import YhImgUpload from "@/components/upload/yh-img-upload.vue";

const warehouseOptions = [
  '义乌仓',
  '南昌仓东',
  '罗兵云仓',
  '上海嘉定仓',
  '代发仓',
  '杭州仓',
  '西安港务仓',
  '广东江门仓',
  '江苏昆山仓',
  '广东东莞仓',
  '安徽仓',
  '湖南云仓',
  '苏州云仓',
  '西安威阳仓（西安分仓)',
  '西安仓',
  '江苏常熟仓',
  '江西抚州仓',
  '上海金山仓',
  '其他',
]

const complaintTypeOptions = [
  '三无产品投诉',
  '3c产品投诉',
  '食品级产品投诉',
]

export default {
  name: 'complaintInformation',
  components: {
    uploadimgFile, YhImgUpload, YhImgUpload3
  },
  props: {
    formDataInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    //表单数据
    infoFormData: {
      type: Object,
      default() {
        return {}
      }
    },
    //是否禁用(默认false)
    forbidden: {
      type: Boolean,
      default() {
        return false
      }
    },
    //是否为新增，编辑（true:编辑 false:新增）
    verifyWhether: {
      type: Boolean,
      default() {
        return false
      }
    },
    selectorParameter: {
      type: Object,
      default() {
        return {}
      }
    },
  },
  data() {
    return {
      complaintTypeOptions,
      createLoading: false,
      brandlist: [],//采购
      directorList: [],//运营
      directorGroupList: [],//小组
      warehouseOptions,
      isView: false,
      accepttyes: '.pdf',
      formEditMode: false,
      ruleForm: {
        proCode: '',
        styleCode: '',
        complaintType: '',
        goodsCode: '',
        goodsName: '',
        brandNameID: '',
        operateSpecialUserNameID: '',
        operateAssistantNameID: '',
        groupNameID: '',
        orderNo: '',
        annex: '',
        picture: '',
      },
      editrules: {
        proCode: [{ required: true, message: '请输入产品ID', trigger: 'blur' }],
        complaintType: [{ required: true, message: '请输入投诉类型', trigger: 'blur' }],
        goodsCode: [{ required: true, message: '请输入商品编码', trigger: 'blur' }],
        complaintReason: [{ required: true, message: '请输入投诉原因', trigger: 'blur' }],
        orderNo: [{ required: true, message: '请输入订单编号', trigger: 'blur' }],
      },
    }
  },

  async mounted() {
    this.brandlist = this.selectorParameter.brandlist
    this.directorList = this.selectorParameter.directorList
    this.directorGroupList = this.selectorParameter.directorGroupList
    this.onCleardataMethod()
    if (this.verifyWhether) {
      this.formDataInfo.annex = this.formDataInfo.annex ? JSON.stringify(this.formDataInfo.annex) : '[]';
      this.formDataInfo.brandNameID = this.formDataInfo.brandNameID ? this.formDataInfo.brandNameID.toString() : '';
      this.formDataInfo.operateSpecialUserNameID = this.formDataInfo.operateSpecialUserNameID ? this.formDataInfo.operateSpecialUserNameID.toString() : '';
      this.formDataInfo.operateAssistantNameID = this.formDataInfo.operateAssistantNameID ? this.formDataInfo.operateAssistantNameID.toString() : '';
      this.formDataInfo.groupNameID = this.formDataInfo.groupNameID ? this.formDataInfo.groupNameID.toString() : '';
      this.ruleForm = this.formDataInfo
      this.isView = true
    }
    // await this.init()
    this.subcomponent = true
    this.$forceUpdate()
  },
  methods: {
    async handleCodeBlurMethod(codeType) {
      let code = this.ruleForm[codeType]; // 获取当前输入字段（商品编码或产品ID）
      if (code) {
        code = code.toString()
        let data, success;
        const params = {
          goodsCode: '',
          proCode: '',
        }
        if (codeType === 'goodsCode') {
          params.goodsCode = code.toString();
          const response = await getAttachGoodsCodeData(params);
          if (!response.success || response.data == null) return
          data = response.data;
          success = response.success;
        } else if (codeType === 'proCode') {
          params.proCode = code.toString();
          const response = await getAttachProCodeData(params);
          if (!response.success || response.data == null) return
          data = response.data;
          success = response.success;
        }
        if (!success) return;
        if (data) {
          if (!this.directorGroupList.some(item => item.key === data.groupNameID)) {
            data.groupNameID = "";
          }
          if (!this.brandlist.some(item => item.value === data.brandNameID)) {
            data.brandNameID = "";
          }
          // 商品编码覆盖所有字段
          if (codeType === 'goodsCode') {
            this.ruleForm.brandNameID = data.brandNameID && data.brandNameID != 0 ? data.brandNameID : '';
            this.ruleForm.goodsName = data.goodsName || '';
            this.ruleForm.groupNameID = data.groupNameID && data.groupNameID != 0 ? data.groupNameID : '';
            if (!this.ruleForm.proCode) {
              this.ruleForm.operateAssistantNameID = data.operateAssistantNameID && data.operateAssistantNameID != 0 ? data.operateAssistantNameID : '';
              this.ruleForm.operateSpecialUserNameID = data.operateSpecialUserNameID && data.operateSpecialUserNameID != 0 ? data.operateSpecialUserNameID : '';
            }
            this.ruleForm.styleCode = data.styleCode || '';
          } else if (codeType === 'proCode') {
            if (!this.directorList.some(item => item.key === data.operateSpecialUserNameID)) {
              data.operateSpecialUserNameID = "";
            }
            if (!this.directorList.some(item => item.key === data.operateAssistantNameID)) {
              data.operateAssistantNameID = "";
            }
            // 产品ID覆盖除 operateAssistantNameID 和 operateSpecialUserNameID 之外的字段
            this.ruleForm.brandNameID = data.brandNameID && data.brandNameID != 0 ? data.brandNameID : '';
            this.ruleForm.goodsName = data.goodsName || '';
            this.ruleForm.groupNameID = data.groupNameID && data.groupNameID != 0 ? data.groupNameID : '';
            this.ruleForm.styleCode = data.styleCode || '';
            // 仅在商品编码为空时覆盖 operateAssistantNameID 和 operateSpecialUserNameID
            // if (!this.ruleForm.goodsCode) {
            this.ruleForm.operateAssistantNameID = data.operateAssistantNameID && data.operateAssistantNameID != 0 ? data.operateAssistantNameID : '';
            this.ruleForm.operateSpecialUserNameID = data.operateSpecialUserNameID && data.operateSpecialUserNameID != 0 ? data.operateSpecialUserNameID : '';
            // }
          }
        }
      }
    },
    onSingleSave() {
      if (!this.ruleForm.proCode && !this.ruleForm.goodsCode) {
        this.$message.error('产品ID或商品编码需必填一个')
        return
      }
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          const params = {
            ...this.ruleForm,
            annex: this.ruleForm.annex ? JSON.parse(this.ruleForm.annex) : [],
            picture: this.ruleForm.picture ? JSON.parse(this.ruleForm.picture).map(item => item.url) : '',
            brandNameID: this.ruleForm.brandNameID ? Number(this.ruleForm.brandNameID) : '',
            operateSpecialUserNameID: this.ruleForm.operateSpecialUserNameID ? Number(this.ruleForm.operateSpecialUserNameID) : '',
            operateAssistantNameID: this.ruleForm.operateAssistantNameID ? Number(this.ruleForm.operateAssistantNameID) : '',
            groupNameID: this.ruleForm.groupNameID ? Number(this.ruleForm.groupNameID) : '',
          }
          this.createLoading = true
          const { data, success } = await addBusinessComplaintRegister(params)
          this.createLoading = false
          if (!success) return
          this.$emit('closeCallback')
        }
      })
    },
    onCleardataMethod() {
      this.ruleForm = {
        proCode: '',
        styleCode: '',
        complaintType: '',
        goodsCode: '',
        goodsName: '',
        brandNameID: '',
        operateSpecialUserNameID: '',
        operateAssistantNameID: '',
        groupNameID: '',
        orderNo: '',
        annex: '',
        picture: '',
      }
    },
    async init() {
      const res = await getExpressComanyAll({});
      if (!res?.success) return
      this.expresscompanylist = res.data;
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 60px;
}

.editCss {
  width: 100%;
}

.containerCss {
  max-height: 90px;
  height: 90px;
  overflow-x: auto;
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep .el-select__tags-text {
  max-width: 30px;
}
</style>
