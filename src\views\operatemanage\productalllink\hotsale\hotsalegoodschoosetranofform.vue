<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <el-row>
                <el-form :model="form" label-width="130px" label-position="right" :disabled="!formEditMode">
                    <el-form-item v-if="form.goodsCompeteShortName != null && form.goodsCompeteShortName != ''"
                        label="产品简称：" prop="goodsCompeteShortName">
                        {{ form.goodsCompeteShortName }}
                    </el-form-item>
                    <el-form-item v-else label="竞品标题：" prop="goodsCompeteName">
                        {{ form.goodsCompeteName }}
                    </el-form-item>
                    <el-form-item label="运营人员：">
                        <el-select v-model="form.directorId" clearable filterable placeholder="请选择运营"
                            @change="brandIdChange">
                            <el-option v-for="item in directorList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-form>

            </el-row>
        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:center;padding-top:10px;">
                    <el-button @click="onClose">关闭</el-button>
                    <el-button type="primary" @click="onSave(true)">转派</el-button>
                </el-col>
            </el-row>
        </template>



    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import { tranOfHotSaleGoodsChoose, getUserInfo} from '@/api/operatemanage/productalllink/alllink';
import { GetDirectorAllList } from "@/api/operatemanage/base/shop" 
import cesTable from "@/components/Table/table.vue";

export default {
    name: "HotPurchasePlanFormDistributePurchase",
    components: { MyContainer, cesTable },
    data() {
        return {
            that: this,
            form: {
                hotSaleGoodsChooseId: '',
                directorId: ''
            },
            directorList: [],
            pageLoading: false,
            curRow: null,
            formEditMode: true,//是否编辑模式              
            mode: 1,
            listLoading: false
        };
    },
    async mounted() {
        const res = await getUserInfo();
        if (!res?.success) {
            return
        }
        this.form.groupId = res.data.groupId; 
        const params = { groupid: res.data.groupId }
        let res2 = await GetDirectorAllList(params);
        this.directorList = res2.data.map(item => {
            return { value: item.key, label: item.value };
        });
    },
    computed: {

    },
    methods: {
        brandIdChange(v) {
            if (v && v > 0) {
                let b = this.directorList.find(x => x.value == v);
                if (b)
                    this.form.directorName = b.label;
            } else {
                this.form.directorName = '';
            }
        },
        onClose() {
            this.$emit('close');
        },
        async loadData({ oid,goodsCompeteName,goodsCompeteShortName }) {
            this.form.hotSaleGoodsChooseId = oid;
            this.form.goodsCompeteName = goodsCompeteName;
            this.form.goodsCompeteShortName = goodsCompeteShortName;
            return;
        },
        async onSave(isClose) {
            if (await this.save()) {
                this.$emit('afterSave');
                if (isClose)
                    this.$emit('close');
            }
        },
        async save() {
            this.pageLoading = true;
            let saveData = { ...this.form, mode: this.mode };
            let errMsg = '';
            if (!(saveData.directorId && saveData.directorId > 0)) {
                errMsg = '请选择运营人员！';
            }
            if (errMsg) {
                this.$alert(errMsg);
                this.pageLoading = false;
                return false;
            }
            let rlt = await tranOfHotSaleGoodsChoose(saveData);
            if (rlt && rlt.success) {
                this.$message.success('转派成功！');
            }
            this.pageLoading = false;
            return (rlt && rlt.success);
        }
    },
};
</script>
