<template>
    <MyContainer>
      <template #header>
        <div class="top">
          <el-date-picker 
          v-model="ListInfo.yearMonth" 
          type="month"
          placeholder="日期"
          format="yyyyMM"
          value-format="yyyyMM"
          :clearable="false"
          @change="changeTime">
          </el-date-picker>
          <el-input v-model.trim="ListInfo.SKU" placeholder="SKU" maxlength="50" clearable class="publicCss" />
          <el-input v-model.trim="ListInfo.ProductName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" @click="startSync">同步</el-button>
  
        </div>
      </template>
      <vxetablebase :ispoint="false" :id="'jdSelfWarehouseOutSync202505211130'" :tablekey="'jdSelfWarehouseOutSync202505211130'"
        ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
        :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
        :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
        :height="'100%'">
      </vxetablebase>
      <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template>

      <el-dialog title="同步数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <label class="required-label">
            <span class="required-mark">*</span> 月份选择：
          </label>
        <el-date-picker style="width: 150px;margin-right: 10px;margin-bottom: 10px;" v-model="SyncInfo.yearMonth" type="month"
          placeholder="选择同步年月" :clearable="false" format="yyyyMM" value-format="yyyyMM">
        </el-date-picker>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="syncLoading"
            @click="syncFile">{{ (syncLoading ? '同步中' : '同步') }}</el-button>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    </MyContainer>
  </template>
  
  <script>
  import dayjs from 'dayjs'
  import MyContainer from "@/components/my-container";
  import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
  import { SetJdSelfWarehousOutToMonthbookkeeper, GetJdSelfWarehousOutToMonthbookkeeper } from '@/api/monthbookkeeper/financialDetail'
  const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'yearMonthDay', label: '时间' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'productName', label: '商品名称' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'sku', label: ' SKU编码 ' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺名称' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'spuCode', label: 'SPU编码' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'productPrice', label: '商品价格' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'costPrice', label: '成本价' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'productCostPrice', label: '商品成本' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'nationalPurchasePrice', label: '全国采购价' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'nationalYesterdayOutgoingQuantity', label: '全国昨日出库商品件数' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'nationalYesterdayOutgoingAmount', label: '全国昨日出库金额' }
  ]
  export default {
    name: "JdSelfWarehouseOutSync",
    components: {
      MyContainer, vxetablebase
    },
    data() {
      return {
        syncLoading: false,
        dialogVisible: false,
        that: this,
        ListInfo: {
          currentPage: 1,
          pageSize: 50,
          orderBy: '',
          isAsc: false,
          yearMonth: dayjs().subtract(1, 'month').format('YYYYMM'),//年月,
          SKU: '',
          ProductName: '',
        },
        tableCols,
        tableData: [],
        summaryarry: {},
        total: 0,
        loading: false,
        SyncInfo: {
          yearMonth:dayjs().subtract(1, 'month').format('YYYYMM'), 
        }
      }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
      async syncFile(item) {
        if (!this.SyncInfo.yearMonth) {
          this.$message({ message: "请选择年月", type: "warning" });
          return false;
        }
        this.syncLoading = true
        var res = await SetJdSelfWarehousOutToMonthbookkeeper(this.SyncInfo);
        if (res?.success)
          this.$message({ message: "同步成功", type: "success" });
        this.syncLoading = false
        this.dialogVisible = false;
        await this.getList()
      },
      //同步弹窗
      startSync() {
        this.dialogVisible = true;
        this.syncLoading = false
      },
      //获取数据
      async getList(type) {
        if (type == 'search') {
          this.ListInfo.currentPage = 1
          this.$refs.pager.setPage(1)
        }
        this.loading = true
        const { data, success } = await GetJdSelfWarehousOutToMonthbookkeeper({ ...this.ListInfo })
        this.loading = false
        if (success && data && data.list) {
          this.tableData = data.list
          this.tableData.forEach(item => {
            item.yearMonthDay = item.yearMonthDay ? dayjs(item.yearMonthDay).format('YYYY-MM-DD') : ''
          })
          this.total = data.total
          this.summaryarry = data.summary
        }
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }
  </script>
  
  <style scoped lang="scss">
  .top {
    display: flex;
    margin-bottom: 10px;
  
    .publicCss {
      width: 170px;
      margin-right: 5px;
    }
  }
  
  .editCss {
    width: 90%;
  }
  
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  
  ::v-deep .el-input-number.is-without-controls .el-input__inner {
    padding-left: 5px;
  }
  .required-mark {
  color: red;
  margin-right: 4px;
  }
  </style>
  