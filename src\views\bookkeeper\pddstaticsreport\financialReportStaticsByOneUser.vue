<template>
  <my-container>
<div>
     <ces-table ref="table" :that='that'
          :tableData='financialreportlist'
          :tableCols='tableCols' :tableHandles='tableHandles' :summaryarry='summaryarry'  :loading="listLoading"  style="width:100%;height:650px;margin: 0"
          @sortchange="sortchange">
        </ces-table>
    <vxe-modal :title="detailName" v-model="dialogVisible"   @close='closeDialog' width="85%" v-dialogDrag>
      <span>
          <financialReportDetailByOneUser height="600px"    ref="financialReportDetailByOneUser" />
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
      </span>
    </vxe-modal>
<!-- <el-dialog title="明细" :style="dialogVisible?'display:block;':'display:none;'" height="600px" width="600px"  @close='closeDialog'>
      <span>   <el-input value="sss"></el-input>
       <financialReportDetailByOneUser height="600px" width="600px"   ref="financialReportDetailByOneUser" />
         </span> <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog> -->


  </div>
   </my-container>
</template>
<script>
import { getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
import {getDirectorGroupList,getDirectorList} from '@/api/operatemanage/base/shop'
import {exportFinancialStaticticsByUser,getParm,setParm,getFinancialStaticticsByUser,getPerformanceStaticticsByGroup} from '@/api/bookkeeper/pddstaticsreport'

import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import InputMult from "@/components/Comm/InputMult";
import { Loading } from 'element-ui';

import financialReportDetailByOneUser from '@/views/bookkeeper/pddstaticsreport/financialReportDetailByOneUser'
let loading;
const startLoading = () => {
  loading = Loading.service({
  lock: true,
  text: '加载中……',
  background: 'rgba(0, 0, 0, 0.7)'
  });
};
const tableCols =[

        {istrue:true,display:true, prop:'userName',label:'运营',width:'80'},
        {istrue:true,display:false, prop:'operateSpecialUserName',label:'姓名',width:'80'},
       // {istrue:true, prop:'userId',label:'姓名',width:'80',formatter:(row)=> row.userName},
        {istrue:true, sortable:'custom',prop:'shopCode',label:'店铺名称',width:'120', type:"click",handle:(that,row,column,cell)=>that.canclick(row,column,cell),formatter:(row)=> row.shopName},
         {istrue:true,sortable:'custom',prop:'payAmont',label:'付款金额',width:'100',formatter:(row)=> !row.payAmont?" ": row.payAmont.toFixed(2)},
        {istrue:true,sortable:'custom',prop:'saleAmont',label:'销售金额',width:'120',formatter:(row)=> !row.saleAmont?" ": row.saleAmont.toFixed(2)},
        {istrue:true,sortable:'custom', prop:'saleCost',label:'销售成本',width:'120',formatter:(row)=> row.saleCost==0?" ": row.saleCost?.toFixed(2)},
        {istrue:true,sortable:'custom',prop:'profit1',label:'毛一利润(发生)',width:'120',formatter:(row)=> !row.profit1?" ": row.profit1.toFixed(2)},
        {istrue:true,sortable:'custom',prop:'profit11',label:'毛一利润(付款)',width:'120',formatter:(row)=> !row.profit1?" ": row.profit1.toFixed(2)},
        {istrue:true,sortable:'custom',prop:'profit1Rate',label:'毛一利润率(发生)',width:'90',formatter:(row)=> !row.profit1Rate?" ": (row.profit1Rate*100).toFixed(2)+"%"},
        {istrue:true,sortable:'custom',prop:'profit1Rate1',label:'毛一利润率(付款)',width:'90',formatter:(row)=> !row.profit1Rate?" ": (row.profit1Rate*100).toFixed(2)+"%"},
        {istrue:true,sortable:'custom',prop:'alladv',label:'广告费',width:'100',formatter:(row)=> row.alladv==0?" ": row.alladv?.toFixed(2)},
        {istrue:true,prop:'alladv_rate',label:'广告占比',  width:'80',formatter:(row)=> (row.alladv*100/(row.saleAmont+0.01))?.toFixed(2).toString()+"%"},
        {istrue:true,sortable:'custom',prop:'profit3',label:'毛三利润(发生)',width:'120',type:'custom',formatter:(row)=> row.profit3==0?" ": row.profit3?.toFixed(2)},
        {istrue:true,sortable:'custom',prop:'profit31',label:'毛三利润(付款)',width:'120',type:'custom',formatter:(row)=> row.profit3==0?" ": row.profit3?.toFixed(2)},
        {istrue:true,prop:'profit3_rate',label:'毛三利润率(发生)',  width:'120',formatter:(row)=> (row.profit3*100/(row.saleAmont+0.01))?.toFixed(2).toString()+"%"},
        {istrue:true,prop:'profit3_rate1',label:'毛三利润率(付款)',  width:'120',formatter:(row)=> (row.profit3*100/(row.saleAmont+0.01))?.toFixed(2).toString()+"%"},
        { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33.toFixed(2) },
        { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33.toFixed(2) },
        {istrue:true,sortable:'custom',prop:'profit4',label:'净利润(发生)',width:'80',formatter:(row)=> row.profit4==0?" ": row.profit4?.toFixed(2)},
        {istrue:true,sortable:'custom',prop:'profit41',label:'净利润(付款)',width:'80',formatter:(row)=> row.profit4==0?" ": row.profit4?.toFixed(2)},
        {istrue:true,prop:'profit4_rate',label:'净利率(发生)' , width:'80',formatter:(row)=> (row.profit4*100/(row.saleAmont+0.01))?.toFixed(2).toString()+"%"},
        {istrue:true,prop:'profit4_rate1',label:'净利率(付款)' , width:'80',formatter:(row)=> (row.profit4*100/(row.saleAmont+0.01))?.toFixed(2).toString()+"%"},
             ];
const tableHandles=[
      ];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable,InputMult,financialReportDetailByOneUser},
  data() {
    return {
      that:this,
      filter: {
        platform:null,
        shopCode:null,
        proCode:null,
        productName:null,
        groupId:null,
        startTime: null,
        endTime: null,
        timerange:null,
        // 运营助理
        userId :null,
        // 车手
        userId2:null,
        // 备用
        userId3:null,
        // 运营专员 ID
        operateSpecialUserId:null,
        listingStartTime:null,
        listingEndTime:null,
        Profit3Lose:null,
        Profit33Lose:null,
      },
      onimportfilter:{
        yearmonthday:null,
      },
      shopList:[],
      userList:[],
      grouplist:[],
      directorlist:[],
      financialreportlist: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      total: 0,
      pager:{OrderBy:" saleAmont ",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      summaryarry:{},
      selids:[],
      fileList:[],
      detailName:'业绩明细',
      dialogVisible:false,
      uploadLoading:false,
      importFilte:{},
      fileList:[],
      fileparm:{},
      editparmVisible:false,
      editLoading:false,
      editparmLoading:false,
      drawervisible:false,
      dialogDrVisible:false,
      drparamProCode:'',
      autoformparm:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
        },
      freightDetail:{
        visible:false,
        filter:{
          proCode:null,
          timeRange:[]
        }
      },
      userType:null
    };
  },
  async mounted() {
    this.init();
    var that=this;
    window.setFinancialFilterTime=function(startTime,endTime,isProductUseId,userId,operateSpecialUserId,groupId,refundType=1,targetType,star=null,flag=null,startTime3,endTime3,Profit3Lose,Profit33Lose,userType){
      //  var date1 = new Date(); date1.setDate(startTime);
      //  var date2 = new Date(); date2.setDate(endTime);
      console.log(operateSpecialUserId,'operateSpecialUserId');
      that.userType=userType;
      if(userType==1)
      {
        that.tableCols[0].display=true;
        that.tableCols[1].display=false;
        that.filter.userId=userId;
        that.filter.operateSpecialUserId=null;
        that.filter.userId3=null;
        that.filter.userId2=null;
      }
      if(userType==2 || userType==21)
      {
        that.tableCols[1].display=true;
        that.tableCols[0].display=false;
        that.filter.operateSpecialUserId=operateSpecialUserId;
        that.filter.userId=null;
        that.filter.userId3=null;
        that.filter.userId2=null;
      }
        that.filter.Profit3Lose=Profit3Lose;
        that.filter.Profit33Lose=Profit33Lose;
        that.filter.listingStartTime=startTime3;
        that.filter.listingEndTime=endTime3;
        that.filter.startTime=startTime;
        that.filter.endTime=endTime;
        that.filter.refundType=refundType;
        that.filter.star=star;
        that.filter.flag=flag;
        that.filter.groupId=groupId;
        that.userType=userType;
        if(userType==5||isProductUseId==1)
      {
        that.tableCols[1].display=true;
        that.tableCols[0].display=false;
        that.filter.userId3=userId;
        that.filter.userId2=null;
        that.filter.userId=null;
        that.filter.groupId=null;
        that.filter.operateSpecialUserId=null;
      }
      if(userType==6||isProductUseId==2)
      {
        that.tableCols[1].display=true;
        that.tableCols[0].display=false;
        that.filter.userId2=userId;
        that.filter.userId3=null;
        that.filter.userId=null;
        that.filter.groupId=null;
        that.filter.operateSpecialUserId=null;
      }
        that.onSearch();
    }
  },
  async created() {
    await this.getShopList();
  },
  methods: {
    onShowupMethod(){
      let that = this;
      that.$nextTick(function () {
        const takePlace = ['profit1', 'profit1Rate', 'profit3', 'profit3_rate', 'profit33', 'profit33Rate', 'exitCost', 'profit4', 'profit4_rate'];
        const payment = ['profit11', 'profit1Rate1', 'profit31', 'profit3_rate1', 'profit331', 'profit33Rate1', 'exitCost1', 'profit41', 'profit4_rate1'];
        if (!that.$refs.table) return;
        function userAction(userType, refundType) {
          const adjustments = {
            1: 'operateSpecialUserName',
            2: 'userName'
          };
          if (userType === 21) {
              userType = 2
          }
          if (refundType === 1) {
            payment.push(adjustments[userType]);
            takePlace.push(adjustments[3 - userType]);
          } else if (refundType === 2) {
            takePlace.push(adjustments[userType]);
            payment.push(adjustments[3 - userType]);
          }
        }
        userAction.call(this, this.userType, that.filter.refundType);
        if (that.filter.refundType === 1) {
          that.$refs.table.changecolumn_setTrue(takePlace);
          that.$refs.table.changecolumn(payment);
        } else {
          that.$refs.table.changecolumn(takePlace);
          that.$refs.table.changecolumn_setTrue(payment);
        }
      });
    },

    closeDialog(){
      this.dialogVisible=false;
    },
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init(){
        var date1 = new Date(); date1.setDate(date1.getDate()-10);
        var date2 = new Date(); date2.setDate(date2.getDate()-1);
        this.filter.timerange=[];
        this.filter.timerange[0]=this.datetostr(date1);
        this.filter.timerange[1]=this.datetostr(date2);
        console.log(this.filter)
      },
   async showprchart2(prcode){
      window['lastseeprcodedrchart']=prcode
      this.drparamProCode=prcode
      this.dialogDrVisible=true
   } ,
   async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=[];
        res1.data?.forEach(f => {
          if(f.isCalcSettlement&&f.shopCode&&f.platform==2)
              this.shopList.push(f);
        });
        var res2= await getDirectorGroupList();
        this.grouplist = res2.data?.map(item => {return { value: item.key, label: item.value };});
        var res3= await getDirectorList();
        this.directorlist = res3.data?.map(item => {return { value: item.key, label: item.value };});
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
     await this.onSearch();
    },
    onRefresh(){
      this.onSearch()
    },
    async onSearch(){
      this.onShowupMethod()
      await this.getList().then(res=>{  });
    },
    async getList(){
      //this.filter.startTime =null;
      // this.filter.endTime =null;
      // if (this.filter.timerange) {
      //   this.filter.startTime = this.filter.timerange[0];
      //   this.filter.endTime = this.filter.timerange[1];
      // }
      var that=this;
      this.pager.pageSize=100;
      this.pager.currentPage=1;
      if (this.pager.orderBy==null) {
        this.pager.orderBy="profit3";
        this.pager.isAsc=false;
      }
      const params = {...this.pager,...this.filter};
      params.userType=this.userType;
     // this.listLoading = true;
      startLoading();
      const res = await getFinancialStaticticsByUser(params).then(res=>{
          loading.close();
          that.total = res.data?.total;
          if(res?.data?.list&&res?.data?.list.length>0){
            for (var i in res.data.list) {
              if (!res.data.list[i].freightFee) {
                res.data.list[i].freightFee =" ";
              }
            }
          }
          that.financialreportlist = res.data?.list;
          that.summaryarry=res.data?.summary;
      });
    },

    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
   onRefresh(){
        this.onSearch()
    },
       async canclick(row, column, cell){

         if(this.filter.userId>0)
         this.detailName=row.userName+"的各商品业绩明细，统计周期："+this.filter.startTime+"--"+this.filter.endTime;
        if(this.filter.operateSpecialUserId>0)
         this.detailName=row.operateSpecialUserName+"的各商品业绩明细，统计周期："+this.filter.startTime+"--"+this.filter.endTime;
          window.financialReportDetailByOneUser=
          {
            userId2:this.filter.userId2,userId3:this.filter.userId3,startTime:this.filter.startTime,endTime:this.filter.endTime,userId:this.filter.userId,operateSpecialUserId:this.filter.operateSpecialUserId,groupId:this.filter.groupId,shopCode:row.shopCode,
            refundType: this.filter.refundType ?? false,Star:this.filter.star,Flag:this.filter.flag,endTime3:this.filter.listingEndTime,startTime3:this.filter.listingStartTime, Profit3Lose: this.filter.Profit3Lose, Profit33Lose: this.filter.Profit33Lose,userType:this.userType
          }
         this.dialogVisible=true;
         if(this.$refs.financialReportDetailByOneUser)
         {
           this.$refs.financialReportDetailByOneUser.onSearch();
         }
    },
  }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
