<template>
  <MyContainer>
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="query" label-position="right" label-width="90px"
        @submit="getList('search')">
        <div class="top">
          <dynamic-selecter v-model="query.dynamicFilter" :scene="'inventory-everywares'" :fields="tableCols.filter(
            (item) => item.label != '' && item.label != '操作')" @update="getList('search')" />

          <el-select v-model="query.wmsCoId" clearable filterable :collapse-tags="true" placeholder="请选择仓库"
            class="publicCss">
            <el-option v-for="item in warehouselist" :key="item.name" :label="item.name" :value="item.wms_co_id" />
          </el-select>

          <el-input v-model.trim="query.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
          <el-input v-model.trim="query.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
          <number-range class="publicCss" :min.sync="query.usableQtyMin" :max.sync="query.usableQtyMax"
            min-label="库存最小值" max-label="库存最大值" />
          <number-range class="publicCss" :min.sync="query.publicUsableQtyMin" :max.sync="query.publicUsableQtyMax"
            min-label="公有库存最小值" max-label="公有库存最大值" />
          <number-range class="publicCss" :min.sync="query.jstUsableQtyMin" :max.sync="query.jstUsableQtyMax"
            min-label="聚水潭库存最小值" max-label="聚水潭库存最大值" />

          <div>
            <el-button type="primary" @click="getList('search')">搜索</el-button>
            <el-button type="primary" :disabled="isExport" @click="exportData">导出</el-button>
          </div>
        </div>
      </el-form>
    </template>
    <vxetablebase :id="'pre-pack_index202408041855'" ref="table" v-loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
      :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
      :is-select-column="false" :is-index-fixed="false" style="width: 100%; margin: 0" :height="'100%'"
      :showsummary="data.summary ? true : false" :summaryarry="data.summary" @sortchange="sortchange" />
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>
<script>
import MyContainer from '@/components/my-container'
import numberRange from '@/components/number-range/index.vue'
import vxetablebase from '@/components/VxeTable/yh_vxetable.vue'
import dynamicSelecter from '@/components/customQuery/selecter.vue'
import { getAllWarehouse } from '@/api/inventory/warehouse'
import { download } from '@/utils/download'
import { pageGetData, exportData, getColumns } from '@/api/vo/inventory'
export default {
  name: 'Inventory',
  components: {
    MyContainer,
    vxetablebase,
    dynamicSelecter,
    numberRange
  },
  data() {
    return {
      rules: {},
      that: this,
      warehouselist: [],
      query: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        summarys: []
      },
      data: { total: 0, list: [], summary: {} },
      tableCols: [],
      loading: false,
      isExport: false
    }
  },
  async mounted() {
    this.getWarehouses()
    await this.getColumns()
    this.getList()
  },
  methods: {
    async getWarehouses() {
      const res = await getAllWarehouse()
      this.warehouselist = res.data.filter((x) => x.name.indexOf('代发') < 0)
    },
    async getColumns() {
      const { data, success } = await getColumns()
      if (success) {
        this.tableCols = data
        this.query.summarys = data.filter(a => a.summaryType).map(a => { return { column: a['sort-by'], summaryType: a.summaryType } })
      }
    },
    // 导出数据,这里前端可以封装一个方法
    async exportData() {
      this.isExport = true
      await exportData(this.query)
        .then(download)
        .finally(() => {
          this.isExport = false
        })
    },
    async getList(type) {
      if (type === 'search') {
        this.query.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      try {
        const {
          data,
          success
        } = await pageGetData(this.query)
        if (success) {
          this.data = data
        } else {
          this.$message.error('获取列表失败')
        }
      } catch (error) {
        this.$message.error('获取列表失败')
      } finally {
        this.loading = false
      }
    },
    // 每页数量改变
    Sizechange(val) {
      this.query.currentPage = 1
      this.query.pageSize = val
      this.getList()
    },
    // 当前页改变
    Pagechange(val) {
      this.query.currentPage = val
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.query.orderBy = prop
        this.query.isAsc = order.indexOf('descending') === -1
        this.getList()
      }
    }
  }
}
</script>
<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
  flex-wrap: wrap;

  .publicCss {
    width: 200px;
    margin: 0 10px 5px 0;
  }
}
</style>
