<template>
    <my-container v-loading="pageLoading">
        <el-row>
            <el-col :span="24">
                <el-input placeholder="请输入链接ID" v-model.trim="queryProCode" style="width:150px">

                </el-input>
                <el-button type="primary" @click="onQuery">查询</el-button>
                <el-button type="waring" @click="onClear" style="margin-left:0">清理</el-button>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24">
                <el-tag size="mini" :key="tag.proCode" v-for="tag in queryList" closable
                    @close="onRemoveProCode(tag.proCode)" @click="onClickProCode(tag.proCode)">
                    {{ tag.proCode }}
                </el-tag>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24" style="max-height:196px;overflow-y: auto;">
                {{ currentProCodeInfo.proCode }}
                <br />
                <div v-for="tag in currentProCodeInfo.combSkuList" style="width:100%;">
                    <el-link type="primary" @click="onCombSkuClick(tag)"
                        style="float:left;width:340px;height:50px;font-size:12px; text-align: left; color: black;
                        overflow: hidden; text-overflow: ellipsis;  border: 1px #ccc solid; padding: 1px; margin: 2px 2px 2px 0px;">
                        <table style="padding: 0; margin: 0; border-spacing:0px; border-collapse:collapse; ">
                            <tr>
                                <td style="width: 50px; height: 50px; padding: 0; margin: 0;">
                                    <el-image v-if="tag.combPic && tag.combPic.length > 5"
                                        style="width: 50px; height: 50px; vertical-align: middle; " :src="tag.combPic"
                                        :preview-src-list="[tag.combPic]">
                                    </el-image>
                                </td>
                                <td
                                    style="width: 284px; height: 50px; padding: 0px 0px 0px 3px; margin: 0; vertical-align:top;">
                                    {{ tag.combSku }} {{ tag.combName }}
                                </td>
                            </tr>
                        </table>
                    </el-link>
                </div>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24">

            </el-col>
        </el-row>
        <el-row>
            <!-- 显示skus -->
            <el-col :xs="24" :sm="24" :md="8" :lg="6" :xl="4" v-for="(item, index) in currentCombSkuInfo.skuList">

                <el-descriptions :labelStyle="{ width: '70px' }" :column="1" size="mini" border>

                    <el-descriptions-item>
                        <template slot="label">
                            <span style="color:red;">组合SKU</span>
                        </template>
                        <label style="font-size:16px ;">{{ item.sku }} (*{{ item.qty }})</label>
                    </el-descriptions-item>

                    <el-descriptions-item
                        v-if="item.info && item.info.goodFrontBackImgs && item.info.goodFrontBackImgs.length > 5">
                        <template slot="label">
                            <span style="color:red">实物图</span>
                        </template>
                        <el-image :src="miniPic(JSON.parse(item.info.goodFrontBackImgs)[0].url)"
                            style="vertical-align: middle;"
                            :preview-src-list="JSON.parse(item.info.goodFrontBackImgs).map(x => x.url)">
                        </el-image>
                    </el-descriptions-item>

                    <el-descriptions-item v-if="item.info">
                        <template slot="label">
                            长宽高重
                        </template>
                        <label style="font-size:16px ;"> {{ item.info.clLength }}*{{ item.info.clWeight }}*{{
                            item.info.clHeight
                        }}mm_{{ item.info.clWeight }}g</label>
                    </el-descriptions-item>

                    <el-descriptions-item v-if="item.info">
                        <template slot="label">
                            长宽高_重(图)
                        </template>
                        <el-image v-if="item.info.clLengthImgs && item.info.clLengthImgs.length > 5"
                            :src="miniPic(JSON.parse(item.info.clLengthImgs)[0].url)" style="margin-right:5px;"
                            :preview-src-list="JSON.parse(item.info.clLengthImgs).map(x => x.url)">
                        </el-image>

                        <el-image v-if="item.info.clWidthImgs && item.info.clWidthImgs.length > 5"
                            :src="miniPic(JSON.parse(item.info.clWidthImgs)[0].url)" style="margin-right:5px"
                            :preview-src-list="JSON.parse(item.info.clWidthImgs).map(x => x.url)">
                        </el-image>

                        <el-image v-if="item.info.clHeightImgs && item.info.clHeightImgs.length > 5"
                            :src="miniPic(JSON.parse(item.info.clHeightImgs)[0].url)" style="margin-right:5px"
                            :preview-src-list="JSON.parse(item.info.clHeightImgs).map(x => x.url)">
                        </el-image>
                        <el-image v-if="item.info.clWeightImgs && item.info.clWeightImgs.length > 5"
                            :src="miniPic(JSON.parse(item.info.clWeightImgs)[0].url)"
                            :preview-src-list="JSON.parse(item.info.clWeightImgs).map(x => x.url)">
                        </el-image>
                    </el-descriptions-item>

                    <el-descriptions-item
                        v-if="item.info && item.info.clThicknessImgs && item.info.clThicknessImgs.length > 5">
                        <template slot="label">
                            厚({{ item.info.clThickness }})
                        </template>
                        <el-image :src="miniPic(JSON.parse(item.info.clThicknessImgs)[0].url)"
                            :preview-src-list="JSON.parse(item.info.clThicknessImgs).map(x => x.url)">
                        </el-image>
                    </el-descriptions-item>

                    <el-descriptions-item
                        v-if="item.info && item.info.qualificationType && item.info.qualificationType.length > 5">
                        <template slot="label">
                            专利资质
                        </template>
                        <span style="color:blue;cursor:pointer;" @click="downloadFiles(item.info.qualificationType)">{{
                            JSON.parse(item.info.qualificationType).length }}个文件</span>
                    </el-descriptions-item>

                    <el-descriptions-item
                        v-if="item.info && item.info.qualifiedImgs && item.info.qualifiedImgs.length > 5">
                        <template slot="label">
                            合格证
                        </template>
                        <el-image :src="miniPic(JSON.parse(item.info.qualifiedImgs)[0].url)"
                            :preview-src-list="JSON.parse(item.info.qualifiedImgs).map(x => x.url)">
                        </el-image>
                    </el-descriptions-item>

                    <el-descriptions-item
                        v-if="item.info && item.info.execStandard && item.info.execStandard.length > 0">
                        <template slot="label">
                            执行标准
                        </template>
                        {{ item.info.execStandard }}
                    </el-descriptions-item>

                    <el-descriptions-item v-if="item.info && item.info.material && item.info.material.length > 0">
                        <template slot="label">
                            材质
                        </template>
                        {{ item.info.material }}
                    </el-descriptions-item>

                    <el-descriptions-item v-if="item.info && item.info.isPatent && item.info.isPatent.length > 0">
                        <template slot="label">
                            是否专利
                        </template>
                        {{ item.info.isPatent == true ? '是' : item.info.isPatent == false ? '否' : '' }}
                    </el-descriptions-item>

                </el-descriptions>


            </el-col>
        </el-row>
        <el-row style="padding-top: 10px;">
            <el-col :xs="24" :sm="24" :md="8" :lg="6" :xl="4" v-for="(item, index) in currentCombSkuInfo.skuList">
                <el-descriptions :labelStyle="{ width: '70px' }" :column="1" size="mini" border
                    v-if="item.sku">
                    <el-descriptions-item>
                        <template slot="label">
                            <span style="color:red;">上传完请点击保存后生效</span>
                        </template>
                        <div style="font-size:16px ;">{{item.sku}}</div>
                        <el-button type="primary" @click="onSaveKf(item,index)">保存上传的资料</el-button>
                    </el-descriptions-item>

                    <el-descriptions-item>
                        <template slot="label">
                            <span>上传合格证</span>
                        </template>
                        <YhImgUpload3 :value.sync="item.qualifiedImgsKf" :isImg="false"
                            accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" :limit="9" :ismultiple="true"></YhImgUpload3>
                    </el-descriptions-item>

                    <el-descriptions-item>
                        <template slot="label">
                            <span>上传质检报告</span>
                        </template>
                        <YhImgUpload3 :value.sync="item.qualityInspectionImgsKf" :isImg="false"
                            accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" :limit="9" :ismultiple="true"></YhImgUpload3>
                    </el-descriptions-item>

                    <el-descriptions-item>
                        <template slot="label">
                            <span>上传产品推荐视频</span>
                        </template>
                        <viodeUpload :minisize="false" :ref="('uploadRecommendVideosKf'+index)" :limit="1" accepttyes=".mp4"
                            :uploadprogress="true" />
                    </el-descriptions-item>

                    <el-descriptions-item>
                        <template slot="label">
                            <span>上传产品使用视频</span>
                        </template>
                        <viodeUpload :minisize="false" :ref="('uploadUsageVideosKf'+index)" :limit="1" accepttyes=".mp4"
                            :uploadprogress="true"  />
                    </el-descriptions-item>

                    <el-descriptions-item>
                        <template slot="label">
                            <span>上传产品使用事项图</span>
                        </template>
                        <YhImgUpload3 :value.sync="item.usageMattersImgsKf" :isImg="false"
                            accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" :limit="9" :ismultiple="true"></YhImgUpload3>
                    </el-descriptions-item>

                </el-descriptions>
            </el-col>
        </el-row>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import { GetProductDocInfoById2, SaveGoodsDocInfoRecordKfInfo } from "@/api/customerext/ProductGoodsDocInfo";
import { matchImg } from '@/utils/getCols'
import YhImgUpload3 from "@/components/upload/yh-img-upload3.vue";
import viodeUpload from "@/views/media/shooting/uploadfile1.vue";

export default {
    name: "PubProductInfo",//商品资料库
    components: {
        MyContainer, YhImgUpload3, viodeUpload
    },
    data() {
        return {
            pageLoading: false,
            queryProCode: '',//查询商品ID
            currentProCode: '',//当前商品ID
            currentProCodeInfo: {},//当前商品信息
            currentCombSkuInfo: {},//组合子sku
            queryList: [],
        };
    },
    async mounted() {
    },
    methods: {
        downloadFiles(files) {
            if (files) {
                let jsonF = JSON.parse(files);

                this.$showDialogform({
                    path: `@/views/base/DownloadFilesForm.vue`,
                    title: '文件列表',
                    autoTitle: false,
                    args: { files: jsonF, mode: 3 },
                    height: 300,
                    width: '600px',
                    callOk: null
                })
            }
        },
        onClickProCode(v) {
            this.queryProCode = v;
            this.onQuery();
        },
        miniPic(picUrl) {
            return matchImg(picUrl);
        },
        onCombSkuClick(combSkuInfo) {
            this.currentCombSkuInfo = combSkuInfo;
            console.log(this.currentCombSkuInfo, "this.currentCombSkuInfo");

            if(this.currentCombSkuInfo.skuList){
                for (let index = 0; index < this.currentCombSkuInfo.skuList.length; index++) {
                    const element = this.currentCombSkuInfo.skuList[index];
                    let vi1 = [];
                    if (element.recommendVideosKf) {
                        vi1.push({
                            url: element.recommendVideosKf,
                            fileName: element.recommendVideosKf,
                            relativePath: null,
                            domain: null
                        });
                    }
                    let vi2 = [];
                    if (element.usageVideosKf) {
                        vi2.push({
                            url: element.usageVideosKf,
                            fileName: element.usageVideosKf,
                            relativePath: null,
                            domain: null
                        });
                    }
                    console.log(this.$refs[("uploadRecommendVideosKf"+index)],"cccccccccccccccc");
                    this.$nextTick(() => {
                        this.$refs[("uploadRecommendVideosKf"+index)][0].setData(vi1);
                        this.$refs[("uploadUsageVideosKf"+index)][0].setData(vi2);
                    })
                }
            }
        },
        onRemoveProCode() {
            //清理记录
        },
        onClear() {
            this.currentProCode = '',
                this.queryList = [];

        },
        async onQuery() {
            if (!this.queryProCode || this.queryProCode.length < 5) {
                $message({
                    message: '请输入正确的链接ID',
                    type: 'warning'
                });
                return;
            }

            this.currentCombSkuInfo = {};

            if (this.queryList && this.queryList.length > 0) {
                for (let i = 0; i < this.queryList.length; i++) {
                    if (this.queryList[i].proCode == this.queryProCode) {
                        this.currentProCode = this.queryList[i].proCode;
                        this.currentProCodeInfo = this.queryList[i];
                        this.queryProCode = '';
                        return;
                    }
                }
            }

            this.pageLoading = true;

            const res = await GetProductDocInfoById2({
                proCode: this.queryProCode
            });

            if (res && res.success) {
                this.queryList.unshift(res.data);
                this.currentProCode = res.data.proCode;
                this.currentProCodeInfo = res.data;

                this.queryProCode = '';
            }
            this.pageLoading = false;
        },

        async onSaveKf(item,index) {
            let name1="uploadRecommendVideosKf"+index;
            let name2="uploadUsageVideosKf"+index;

            let videoRes1 = this.$refs[name1][0].getReturns();
            if (videoRes1.data?.length > 0) {
                item.recommendVideosKf = videoRes1.data[0].url;
            } else {
                item.recommendVideosKf = null;
            }
            let videoRes2 = this.$refs[name2][0].getReturns();
            if (videoRes2.data?.length > 0) {
                item.usageVideosKf = videoRes2.data[0].url;
            } else {
                item.usageVideosKf = null;
            }
            let param = {
                //proCode: this.currentProCodeInfo.proCode,
                proCode:"此维度放弃，到SKU维度",
                goodsCode: item.sku,
                qualifiedImgsKf: item.qualifiedImgsKf,
                qualityInspectionImgsKf: item.qualityInspectionImgsKf,
                recommendVideosKf: item.recommendVideosKf,
                usageVideosKf: item.usageVideosKf,
                usageMattersImgsKf: item.usageMattersImgsKf,
            };
            let res = await SaveGoodsDocInfoRecordKfInfo(param);
            if (res?.success) {
                this.$message({ message: '保存成功', type: 'success' });
            }
        },
    }
};
</script>
<style lang="scss" scoped>
::v-deep .el-image__inner {
    width: 50px;
    height: 50px;
}
</style>
