<template>
  <container>
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="创建时间">
          <el-date-picker style="width: 230px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
            :picker-options="pickerOptions"></el-date-picker>
        </el-form-item>
        <el-form-item label="">
          <el-input v-model="filter.goodsCode" v-model.trim="filter.goodsCode" placeholder="商品编码"
            style="width: 120px" maxlength="50" clearable></el-input>
        </el-form-item>

        <el-form-item>
          <el-input v-model="filter.storeName" v-model.trim="filter.storeName" placeholder="仓库"
              style="width: 120px" maxlength="50" clearable></el-input>
        </el-form-item>

        <el-form-item label="">
          <el-input v-model="filter.storeNo" v-model.trim="filter.storeNo" placeholder="仓位"
            style="width: 120px" maxlength="50" clearable></el-input>
        </el-form-item>

        <el-form-item label="">
          <el-input v-model="filter.createdName" v-model.trim="filter.createdName" placeholder="创建人"
            style="width: 120px" maxlength="50" clearable></el-input>
        </el-form-item> 

        <el-form-item label="">
          <el-select filterable v-model="filter.pType" clearable  placeholder="盘点前数量1"
              style="width: 130px;margin-left: 5px;"> 
              <el-option label="盘点前大于0" value='1'></el-option>
              <el-option label="盘点前为0" value="2"></el-option>
              <el-option label="无盘点数据" value="3"></el-option>
            </el-select> 
        </el-form-item>  

        <el-form-item label="">
          <el-select v-model="filter.replenishmentType" clearable  placeholder="补货类型"
              style="width: 130px;margin-left: 5px;"> 
              <el-option label="补货上架" value="4"></el-option>
              <el-option label="二次补货" value="5"></el-option>
              <el-option label="无补货记录" value="6"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-input v-model="filter.replenishmentName" v-model.trim="filter.replenishmentName" placeholder="补货人员"
              style="width: 120px" maxlength="50" clearable></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onExport">导出</el-button>
        </el-form-item>
      </el-form>
    </template>

    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'
      :tableCols='tableCols' :isSelection="false" :tableHandles='tableHandles' :loading="listLoading"
      :summaryarry="summaryarry">
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <el-dialog :title="dialogtitle" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="4" action accept=".xlsx"
          :http-request="uploadFile" :on-remove="uploadDel" :on-change="uploadChange" :file-list="fileList" :before-upload="beforeAvatarUpload">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

  </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { getSecondReplenishmentData, importSecondReplenishmentStoreInventory, importSecondReplenishmentOnlineStoreInventory,exportSecondReplenishmentData} from '@/api/inventory/secondreplenishment'
import dayjs from "dayjs";
import { formatTime } from "@/utils";

function getReplenishmentTime(second)
{
  if (second == null || second == 0) {
    return '-';
  } else {
    //多少天
    let day = parseInt(second / (60 * 60 * 24))
    //多少小时
    let hour = parseInt((second / (60 * 60))) % 24
    //多少分钟
    let min = parseInt((second / 60)) % 60
    return day + "天" + hour + "时" + min + "分";
  }
}

const tableCols = [
  { istrue: true, prop: 'goodsCode', label: '商品编码', tipmesg: '', width: '120', sortable: 'custom', }, 
  { istrue: true, prop: 'storeName', label: '仓库', tipmesg: '', width: '100', sortable: 'custom', },
  { istrue: true, prop: 'storeNo', label: '仓位', tipmesg: '', width: '120', sortable: 'custom', },
  { istrue: true, prop: 'jH_Source', label: '单据来源', tipmesg: '', width: '100', sortable: 'custom', },
  { istrue: true, prop: 'jH_CreatedName', label: '创建人', tipmesg: '', width: '100', sortable: 'custom', },
  { istrue: true, prop: 'jH_Date', label: '创建时间', tipmesg: '', width: '150', sortable: 'custom', formatter: (row) => formatTime(row.jH_Date, "YYYY-MM-DD HH:mm:ss") },
  { istrue: true, prop: 'jH_BeforeCount', label: '盘点前数量', tipmesg: '', width: '75', sortable: 'custom', },
  { istrue: true, prop: 'jH_AfterCount', label: '盘点后数量', tipmesg: '', width: '75', sortable: 'custom', },

  { istrue: true, prop: 'pdA_Source', label: '单据来源1', tipmesg: '', width: '100', sortable: 'custom', },
  { istrue: true, prop: 'pdA_CreatedName', label: '创建人1', tipmesg: '', width: '100', sortable: 'custom', },
  { istrue: true, prop: 'pdA_Date', label: '创建时间1', tipmesg: '', width: '150', sortable: 'custom', formatter: (row) => (row.pdA_Date == null ? "" : formatTime(row.pdA_Date, "YYYY-MM-DD HH:mm:ss")) },
  { istrue: true, prop: 'pdA_BeforeCount', label: '盘点前数量1', tipmesg: '', width: '75', sortable: 'custom', },
  { istrue: true, prop: 'pdA_AfterCount', label: '盘点后数量1', tipmesg: '', width: '75', sortable: 'custom', },

  { istrue: true, prop: 'aX_CreatedName', label: '操作人', tipmesg: '', sortable: 'custom', },
  { istrue: true, prop: 'aX_Date', label: '操作日期', tipmesg: '', width: '150', sortable: 'custom', formatter: (row) => (row.aX_Date == null ? "" : formatTime(row.aX_Date, "YYYY-MM-DD HH:mm:ss")) },
  { istrue: true, prop: 'aX_Count', label: '数量', tipmesg: '', sortable: 'custom', },

  { istrue: true, prop: 'replenishmentType', label: '补货类型', tipmesg: '', sortable: 'custom', },
  { istrue: true, prop: 'replenishmentTime', label: '补货时长', tipmesg: '', sortable: 'custom', width: '95', align: "center", formatter: (row) => getReplenishmentTime(row.replenishmentTime) },
  { istrue: true, prop: 'replenishmentName', label: '补货人员', tipmesg: '', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'wareAisleUser', label: '通道负责人', tipmesg: '', sortable: 'custom', width: '100' },
]

const tableHandles = [
  { label: "盘点导入", handle: (that) => that.startImport(1) },
  { label: "按箱上架导入", handle: (that) => that.startImport(2) }
];

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
  name: 'YunHanSecondReplenishmentList',
  components: { container, cesTable, MyConfirmButton },
  data() {
    return {
      that: this,
      filter: {
        startTime: null,
        endTime: null,
        goodsCode: null,
        storeNo: null,
        createdName: null,
        source: null,
        pType:null,
        replenishmentType: null,
        replenishmentName: null,
        storeName:null,
        timerange: [startDate, endDate],
      },
      replenishmentTime: null,
      list: [],
      shopList: [],
      directorGroupList: [],
      pager: { OrderBy: "JH_Date", IsAsc: false },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      pickOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      sels: [],
      uploadType: 0,
      uploadLoading: false,
      dialogVisible: false,
      listLoading: false,
      dialogtitle: null,
      fileList: [],
      summaryarry: {}
    };
  },

  async mounted() {
    //await this.onSearch() 
  },

  methods: {
    // 根据秒数转换成对应的时差
    // getHMS(time) {
    //   const hour = (time / 3600) < 10 ? '0' + (time / 3600) : (time / 3600);
    //   const min = (time % 3600 / 60) < 10 ? '0' + (time % 3600 / 60) : (time % 3600 / 60);
    //   const sec = (time % 3600 % 60) < 10 ? '0' + (time % 3600 % 60) : (time % 3600 % 60);
    //   return hour+"h:"+min+"m:"+sec+"s"
    // },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    async getlist() {
      if (this.pager.OrderBy == null) {
        this.pager.OrderBy = "JH_Date";
        this.pager.IsAsc = false;
      }
      let pager = this.$refs.pager.getPager();
      let page = this.pager;
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      const params = { ...pager, ...page, ... this.filter }
      if (params === false) {
        return;
      }
      this.listLoading = true
      const res = await getSecondReplenishmentData(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.list = data
      this.summaryarry = res.data.summary;
    },
    async nSearch() {
      await this.getlist()
    },
    //开始导入
    startImport(type) {
      this.fileList =[];
      if (type == 1) {
        this.dialogtitle = "盘点数据导入";
      } else {
        this.dialogtitle = "按箱上架数据导入";
      }
      this.uploadType = type;
      this.uploadLoading = false;
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.upload.clearFiles();
      });
    },
    //取消导入
    cancelImport() {
      this.dialogVisible = false;
    },
    async submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit = true;
      this.uploadLoading = true;
      this.$refs.upload.submit();
    },
    async uploadFile(item) {
      if (!this.fileHasSubmit) {
        return false;
      }
      this.fileHasSubmit = false;
      this.uploadLoading = true;
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);

      if (this.uploadType == 1) {
        const res = await importSecondReplenishmentStoreInventory(form);
        if (res.code == 1) {
          this.$message({ message: "上传成功,正在导入中...", type: "success" });
          this.dialogVisible = false;
        }
      } else {
        const res = await importSecondReplenishmentOnlineStoreInventory(form);
        if (res.code == 1) {
          this.$message({ message: "上传成功,正在导入中...", type: "success" });
          this.dialogVisible = false;
        }
      }
      this.uploadLoading = false;
      this.$refs.upload.clearFiles();
    },
    async uploadChange(file, fileList) {
      let files = [];
      files.push(file)
      this.fileList = files;
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    },
    beforeAvatarUpload(file) {
      const isExcel =
        (file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          || file.type === 'application/vnd.ms-excel,application/x-xls,application/x-xlw');
      if (!isExcel) {
        this.uploadLoading = false;
        this.$message.error('上传文件格式错误!');
      }
      return isExcel;
    },
    async onExport() {
      if (this.pager.OrderBy == null) {
        this.pager.OrderBy = "JH_Date";
        this.pager.IsAsc = false;
      }
      this.filter.startTime = null;
      this.filter.endTime = null; 
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      let pager = this.$refs.pager.getPager();
      let params = { ...pager, ...this.pager, ...this.filter };
      let res = await exportSecondReplenishmentData(params);
      if (!res?.data) {
        this.$message({ message: "没有数据", type: "warning" });
        return
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '仓库二次补货_' + new Date().toLocaleString() + '_.xlsx')
      aLink.click()
    },
    async uploadDel(file,fileList){
      this.fileList = [];
    },
  }
};
</script>

<style lang="scss" scoped></style>