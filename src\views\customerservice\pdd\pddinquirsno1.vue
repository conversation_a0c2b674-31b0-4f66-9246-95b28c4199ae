<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='inquirslist' @select='selectchange' :isSelection='false'
            :tableCols='tableCols' :loading="listLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
                <el-button-group>
                  <el-button style="padding: 0;margin: 0;">
                    <el-select style="float:left;width:120px;" v-model="Filter.PartitionID" placeholder="分区" clearable
                               filterable @change="handlePartitionChange">
                      <el-option v-for="item in partitionList" :key="item.id" :label="item.partitionName"
                                 :value="item.id" />
                    </el-select>
                  </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="Filter.EnmPddGroupType" placeholder="组名称" style="width:120px;" disabled>
                            <el-option label="售前" :value=0 />
                            <el-option label="售后" :value=1 />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.Shopname" v-model.trim="Filter.Shopname" clearable placeholder="店铺"
                            style="width:200px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.Snick" v-model.trim="Filter.Snick" placeholder="昵称" clearable
                            style="width:200px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <datepicker v-model="Filter.Sdate"></datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getinquirsList" />
        </template>


        <el-dialog title="添加客服人员分组管理信息" :visible.sync="addgroupdialogVisibleSyj" width="40%"
            :close-on-click-modal="false" v-dialogDrag>
            <span>
                <el-form :model="addFormNo" ref="addFormNo" :rules="addFormRules">
                    <el-form-item label="分组" prop="groupname">
                        <el-input style="width:83%" clearable v-model="addFormNo.groupname"
                            v-model.trim="addFormNo.groupname"></el-input>
                    </el-form-item>
                    <el-form-item prop="groupManager" label="组长">
                        <el-input style="width:83%" v-model="addFormNo.groupManager"
                            v-model.trim="addFormNo.groupManager"></el-input>
                    </el-form-item>
                    <el-form-item label="店铺" prop="shopname">
                        <el-input style="width:83%" clearable v-model="addFormNo.shopname"
                            v-model.trim="addFormNo.shopname"></el-input>
                    </el-form-item>
                    <el-form-item label="姓名" prop="sname">
                        <el-input style="width:83%" clearable v-model="addFormNo.sname"
                            v-model.trim="addFormNo.sname"></el-input>
                    </el-form-item>
                    <el-form-item label="昵称" prop="snick">
                        <el-input style="width:83%" clearable v-model="addFormNo.snick"
                            v-model.trim="addFormNo.snick"></el-input>
                    </el-form-item>
                    <el-form-item label="绑定手机号">
                        <el-input style="width:73%" clearable v-model="addFormNo.phoneNo"
                            v-model.trim="addFormNo.phoneNo"></el-input>
                    </el-form-item>
                    <el-form-item label="入组日期" prop="joinDate">
                        <el-date-picker v-model="addFormNo.joinDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            type="date" style="width:63%" placeholder="选择日期">
                        </el-date-picker>
                        <!-- <el-input   v-model="addFormNo.JoinDate"></el-input> -->
                    </el-form-item>
                    <el-form-item label="离组日期" prop="leaveDate">
                        <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="addFormNo.leaveDate"
                            style="width:63%" type="date" placeholder="选择日期">
                        </el-date-picker>
                        <!-- <el-input   v-model="addFormNo.LeaveDate"></el-input> -->
                    </el-form-item>
                </el-form>
            </span>

            <span slot="footer" class="dialog-footer">
                <el-button @click="addgroupdialogVisibleSyj = false">关闭</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="addgroupform">确定</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
  GetPddInquirsNotExistsList,
  addgroup, getGroupNamesByPartitionId, getAllPartitions
} from '@/api/customerservice/pddInquirs'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import Decimal from 'decimal.js';

function reserve(data) {
    if (data == null) {
        return "0.0";
    }
    let result = (Math.floor(data * 10) / 10).toString();
    console.log(result);
    if (!result.includes(".")) {
        result = result + ".0";
    }
    return result;
};
function precision(number, multiple) {
    return new Decimal(number).mul(new Decimal(multiple));
}

const tableCols = [
    {
        istrue: true, type: "button", label: '操作', width: "90", btnList: [{
            label: "生成分组", handle: (that, row) => that.onCreateGroup(row)
        }]
    },


    { istrue: true, prop: 'sdate', label: '日期', width: '110', align: 'center', sortable: 'custom', formatter: (row) => { return formatTime(row.sdate, 'YYYY-MM-DD') } },

    { istrue: true, prop: 'shopname', label: '店铺', width: '200', sortable: 'custom' },
  { istrue: true, prop: 'partitionName', label: '分区名称', width: '120', sortable: 'custom' },

    { istrue: true, prop: 'snick', label: '昵称', width: '200', sortable: 'custom' },

    { istrue: true, prop: 'inquirs', label: '咨询人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'manualReply', label: '人工接待人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsCount', label: '询单人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'receiveCount', label: '最终成团人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'successpayrate', label: '询单转化率', width: '80', sortable: 'custom', formatter: (row) => { return (row.successpayrate ? precision(row.successpayrate, 100) : 0) + "%" } },

    { istrue: true, prop: 'threeSecondLost', label: '3分钟未回复人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'threeSecondGet', label: '3分钟回复人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'threeSecondReplyRate', label: '3分钟人工回复率', width: '90', sortable: 'custom', formatter: (row) => { return (row.threeSecondReplyRate ? precision(row.threeSecondReplyRate, 100) : 0) + "%" } },
    { istrue: true, prop: 'responseTime', label: '均响(秒)', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'thirtySecondResponseRate', label: '30秒应答率', width: '80', sortable: 'custom', formatter: (row) => { return (row.thirtySecondResponseRate ? precision(row.thirtySecondResponseRate, 100) : 0) + "%" } },

    { istrue: true, prop: 'salesvol', label: '客服销售额（元）', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'serviceScore', label: '客服服务分', width: '80', sortable: 'custom', formatter: (row) => { return Math.floor(row.serviceScore * 10) / 10 } },
    { istrue: true, prop: 'lowRatingOrderCount', label: '评分≤3订单数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'disputeRefundCount', label: '纠纷退款数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'complainCount', label: '投诉数', width: '80', sortable: 'custom' },

    { istrue: true, prop: 'createdTime', label: '导入时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'batchNumber', label: '导入批次', width: '170', sortable: 'custom' },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
    data() {
        return {
            that: this,
          partitionList: [], // 分区列表

            Filter: {
                EnmPddGroupType: 1,
                Sdate: [formatTime(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30), "YYYY-MM-DD 00:00:00"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],
            },
            inquirslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],


            addgroupdialogVisibleSyj: false,
            addFormNo: {},
            addFormRules: {
                groupname: [{ required: true, message: '必填', trigger: 'blur' }],
                shopname: [{ required: true, message: '必填', trigger: 'blur' }],
                groupManager: [{ required: true, message: '必填', trigger: 'blur' }],
                sname: [{ required: true, message: '必填', trigger: 'blur' }],
                snick: [{ required: true, message: '必填', trigger: 'blur' }],
                joinDate: [{ required: true, message: '必填', trigger: 'blur' }],
                leaveDate: [{ required: true, message: '必填', trigger: 'blur' }]
            },
        };
    },
    async mounted() {
      await this.getPartitionList();

    },
    methods: {
      async getPartitionList() {
        try {
          const res = await getAllPartitions({ EnmPddGroupType: 1 });
          this.partitionList = res.data;
        } catch (error) {
          console.error('获取分区列表失败', error);
        }
      },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getinquirsList();
        },

        async getinquirsList() {
            if (this.Filter.UseDate) {
                this.Filter.startAccountDate = this.Filter.UseDate[0];
                this.Filter.endAccountDate = this.Filter.UseDate[1];
            }
            if (this.Filter.Sdate) {
                this.Filter.startSdate = this.Filter.Sdate[0];
                this.Filter.endSdate = this.Filter.Sdate[1];
            }
            else {
                this.Filter.startSdate = null;
                this.Filter.endSdate = null;
            }
            const para = { ...this.Filter };
            let pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,

            };

            this.listLoading = true;
            const res = await GetPddInquirsNotExistsList(params);
            this.listLoading = false;

            this.total = res.data.total;
            this.inquirslist = res.data.list;
            this.summaryarry = res.data.summary;
            console.dir(this.inquirslist);
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },

        onCreateGroup(row) {
            this.addFormNo = {};
            this.addFormNo.platform = 2;
            this.addFormNo.shopname = row.shopname;
            this.addFormNo.snick = row.snick;
            this.addFormNo.pddGroup = row.groupType;
            this.addFormNo.partitionId = row.partitionId;

            this.addgroupdialogVisibleSyj = true;
        },
        //提交保存时验证
        onSubmitValidate: function () {
            let isValid = true;
            this.$refs.addFormNo.validate(valid => {
                isValid = valid
            })
            return isValid;
        },
        async addgroupform() {
            if (!this.onSubmitValidate()) {
                return;
            }
            let res = await addgroup(this.addFormNo)
            if (res?.success) {
                this.$message({ message: '已添加', type: "success" });
                this.onSearch()
                this.addFormNo = {}
                this.addgroupdialogVisibleSyj = false
            }
        },
      async handlePartitionChange(partitionId) {
        if (partitionId) {
          try {
            const res = await getGroupNamesByPartitionId({ partitionId });
            if (res.data && res.data.length > 0) {
              // 将分区下的组名转换为grouplistsel格式
              this.grouplistsel = res.data.map(groupName => ({
                groupname: groupName
              }));
            } else {
              this.grouplistsel = [];
            }
            // 清空已选择的组名
            this.Filter.GroupNames = [];
          } catch (error) {
            console.error('获取分区下组名失败', error);
          }
        } else {
          // 如果清空分区选择，则获取所有组
          await this.setGroupSelect();
        }
      },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
