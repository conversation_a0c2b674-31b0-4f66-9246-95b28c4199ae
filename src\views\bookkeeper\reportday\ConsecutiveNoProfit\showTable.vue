<template>
  <container>
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :tableData='list' @sortchange='sortchange' :isSelectColumn="false"
      :tableCols='tableCols' :isSelection="false"   :loading="listLoading"
      :summaryarry="summaryarry">
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>
  </container>
</template>

<script>
import container from '@/components/my-container'
import cesTable from "@/components/Table/table.vue";
import { getContinuousNoProfit } from "@/api/bookkeeper/continuousprofitanalysis"
import dayjs from "dayjs";
import { formatTime } from "@/utils";

const tableCols = [
  { istrue: true, prop: 'consecutiveDay3Profit6', label: '连续3天负利润', tipmesg: '',  sortable: 'custom', },
  { istrue: true, prop: 'consecutiveDay7Profit6', label: '连续7天负利润', tipmesg: '',  sortable: 'custom', },
  { istrue: true, prop: 'consecutiveDay15Profit6', label: '连续15天负利润', tipmesg: '', sortable: 'custom', },
  { istrue: true, prop: 'consecutiveDay30Profit6', label: '连续30天负利润', tipmesg: '', sortable: 'custom' }
]

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
  name: 'ConsecutiveNoProfitShowTable',
  components: { container, cesTable },
  props: { filter: {} },
  data() {
    return {
      that: this,
      list: [],
      pager: { OrderBy: null, IsAsc: false },
      tableCols: [], 
      total: 0,
      sels: [],
      uploadLoading: false,
      dialogVisible: false,
      listLoading: false,
      fileList: [],
      summaryarry: {}
    };
  },

  async mounted() {

  },

  methods: {
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    async getlist() {
      let tableColsGroups = [];
      if (this.filter.groupType=="platform") {
        tableColsGroups.push({ istrue: true, prop: 'name', label: '平台', width: '200', sortable: 'custom',formatter:(row)=>row.platformName });
      }else if (this.filter.groupType=="shopcode") {
        tableColsGroups.push({ istrue: true, prop: 'name', label: '店铺', width: '200', sortable: 'custom',formatter:(row)=>row.shopName });
      }else if (this.filter.groupType=="groupid") {
        tableColsGroups.push({ istrue: true, prop: 'name', label: '运营组', width: '200', sortable: 'custom',formatter:(row)=>row.groupName});
      }else if (this.filter.groupType=="operatespecialuserid") {
        tableColsGroups.push({ istrue: true, prop: 'name', label: '运营专员', width: '200', sortable: 'custom',formatter:(row)=>row.operateSpecialUserName });
      }   

      tableCols.forEach(item => {
        tableColsGroups.push(item)
      });
      this.tableCols = tableColsGroups;
      let pager = this.$refs.pager.getPager();
      let page = this.pager;
      const params = { ...pager, ...page, ... this.filter }
      if (params === false) {
        return;
      }
      this.listLoading = true
      const res = await getContinuousNoProfit(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.list = data
      this.summaryarry = res.data.summary;
    },
    async nSearch() {
      await this.getlist()
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    }
  }
};
</script>

<style lang="scss" scoped></style>