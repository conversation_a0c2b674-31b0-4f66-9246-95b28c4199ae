<template>
    <MyContainer>
        <template #header>
            <div class="top">

                <el-select v-model="ListInfo.groupType" placeholder="统计维度" style="width: 120px;" clearable filterable
                    @change="getList('search')">
                    <el-option label="按天" value="按天" />
                    <el-option label="合计" value="合计" />
                </el-select>

                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" :clearable="false"
                    style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                <el-select v-model="ListInfo.expressCompanyNameList" placeholder="快递公司"
                    style="width: 250px;margin-right: 5px;" clearable collapse-tags multiple filterable>
                    <el-option label="无快递公司" value="无快递公司" />
                    <el-option v-for="item in kdCompany" :key="item" :label="item" :value="item" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @summaryClick='onsummaryClick' :showsummary='true' :summaryarry='summaryarry' @sortchange='sortchange'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <!-- 趋势图 -->
        <el-drawer :title="title" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false"
            direction="btt">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" @change="chatSearch($event, dialogType)"
                    :picker-options="pickerOptions" style="margin: 10px;" :clearable="false" />
                <buschar :analysisData="chatProp.data" v-if="!chatProp.chatLoading"></buschar>
            </div>
            <div v-else v-loading="chatProp.chatLoading"></div>
        </el-drawer>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace, getTime as get30chatdays } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import buschar from '@/components/Bus/buschar'
import {
    GetExpressCompanyNameList,
    GetExpressInterceptStatisticsPageList,
    GetExpressInterceptStatisticsChat
} from '@/api/customerservice/expressIntercept'
import dayjs from 'dayjs'
const tableCols = [
    {
        sortable: 'custom', width: 'auto', align: 'center', prop: 'addTime', label: '日期',
        formatter: (row) => row.addTime !== null ? (dayjs(row.addTime).format('YYYY-MM-DD') == '1901-01-01' ? '查询区间' : dayjs(row.addTime).format('YYYY-MM-DD')) : ''
    },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressCompanyName', label: '快递公司', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'interceptRegistrationNum', label: '登记拦截条数', summaryEvent: true, },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'interceptNoticeNum', label: '通知拦截条数', summaryEvent: true, },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'interceptOperateNum', label: '操作拦截条数', summaryEvent: true, },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'interceptScanNum', label: '匹配扫码退件条数(历史)', summaryEvent: true, },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'interceptScanRate', label: '匹配率(历史)', summaryEvent: true, formatter: (row) => row.interceptScanRate !== null ? row.interceptScanRate + '%' : '' },
    {
        istrue: true, label: '趋势图', width: '70', type: 'button', btnList:
            [
                { istrue: true, label: '趋势图', handle: (that, row) => that.openDialog(row) },
            ]
    },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, buschar
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startAddTime: null,//开始时间
                endAddTime: null,//结束时间
                expressCompanyName: null,// 快递公司
                expressCompanyNameList: [],// 快递公司
                groupType: "按天",
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            chatProp: {
                chatDialog: false,//趋势图弹窗
                chatTime: null,//趋势图时间
                chatLoading: true,//趋势图loading
                data: [],//趋势图数据
            },
            chatInfo: {
                // seriesName: null,//组别
                goodsCode: null,//姓名
                startAddTime: null,//店铺
                endAddTime: null,//昵称
            },
            summaryarry: null,
            kdCompany: [],//快递公司
            dialogType: null,
            title: null
        }
    },
    async mounted() {
        await this.getKdCompany()
        await this.getList()
    },
    methods: {
        async onAddTimeHide() {
            return this.ListInfo.groupType = "合计";
        },
        async getKdCompany() {
            const { data, success } = await GetExpressCompanyNameList()
            if (success) {
                this.kdCompany = data
            }
        },
        async chatSearch(e, type) {
            if (type == '趋势图') {
                this.chatInfo.startAddTime = dayjs(this.chatProp.chatTime[0]).format('YYYY-MM-DD')
                this.chatInfo.endAddTime = dayjs(this.chatProp.chatTime[1]).format('YYYY-MM-DD')
                this.chatProp.chatLoading = true
                const data = await GetExpressInterceptStatisticsChat(this.chatInfo)
                this.chatProp.data = data
                this.chatProp.chatDialog = true
                this.chatProp.chatLoading = false
            } else {
                this.chatInfo.startAddTime = dayjs(this.chatProp.chatTime[0]).format('YYYY-MM-DD')
                this.chatInfo.endAddTime = dayjs(this.chatProp.chatTime[1]).format('YYYY-MM-DD')
                this.chatProp.chatDialog = true
                this.chatProp.chatLoading = true
                const data = await GetExpressInterceptStatisticsChat(this.chatInfo)
                this.chatProp.data = data
                this.chatProp.chatDialog = true
                this.chatProp.chatLoading = false
            }
        },
        async onsummaryClick() {
            this.dialogType = '汇总趋势图'
            this.title = '汇总趋势图'
            this.chatProp.chatDialog = true
            let newChatTime = get30chatdays([this.ListInfo.startAddTime, this.ListInfo.endAddTime]);
            this.chatProp.chatTime = newChatTime;
            this.chatInfo = {
                startAddTime: newChatTime[0],
                endAddTime: newChatTime[1],
                expressCompanyNameList: this.ListInfo.expressCompanyNameList,
                expressCompanyName: null,
            }
            this.chatProp.chatLoading = true
            const data = await GetExpressInterceptStatisticsChat(this.chatInfo)
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        async openDialog(row) {
            this.title = row.expressCompanyName
            this.dialogType = '趋势图'
            this.chatProp.chatDialog = true
            let newChatTime = get30chatdays([this.ListInfo.startAddTime, this.ListInfo.endAddTime]);
            this.chatProp.chatTime = newChatTime;
            this.chatInfo = {
                expressCompanyName: !row.expressCompanyName ? "无快递公司" : row.expressCompanyName,
                expressCompanyNameList: [],
                startAddTime: newChatTime[0],
                endAddTime: newChatTime[1],
            }
            this.chatProp.chatLoading = true
            const data = await GetExpressInterceptStatisticsChat(this.chatInfo)
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        async changeTime(e) {
            this.ListInfo.startAddTime = e ? e[0] : null
            this.ListInfo.endAddTime = e ? e[1] : null
            await this.getList()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            if (this.timeRanges && this.timeRanges.length == 0) {
                //默认给近7天时间
                this.ListInfo.startAddTime = dayjs().format('YYYY-MM-DD')
                this.ListInfo.endAddTime = dayjs().format('YYYY-MM-DD')
                this.timeRanges = [this.ListInfo.startAddTime, this.ListInfo.endAddTime]
            }
            const replaceArr = ['orderNo', 'orderNoInner'] //替换空格的方法,该数组对应str类型的input双向绑定的值
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data: { list, total, summary }, success } = await GetExpressInterceptStatisticsPageList(this.ListInfo)
                if (success) {
                    this.tableData = list
                    this.total = total
                    this.summaryarry = summary
                    this.summaryarry.interceptScanRate_sum = parseFloat(this.summaryarry.interceptScanRate_sum).toFixed(2) + '%';
                } else {
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.$message.error('获取列表失败')
            } finally {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
