<template>
  <MyContainer>
    <template #header>
      <el-row :gutter="10" class="top">
        <el-col :span="3"><el-input v-model.trim="ListInfo.combineCode" placeholder="组合编码" maxlength="50" clearable
            class="publicCss" /></el-col>
        <el-col :span="3"><el-input v-model.trim="ListInfo.entityCode" placeholder="实体编码" maxlength="50" clearable
            class="publicCss" /></el-col>
        <el-col :span="3"><el-input v-model.trim="ListInfo.combineName" placeholder="商品名称" maxlength="50" clearable
            class="publicCss" /></el-col>
        <el-col :span="3"><el-input v-model.trim="ListInfo.consumableCode" placeholder="包装编码" maxlength="50" clearable
            class="publicCss" /></el-col>
        <el-col :span="3"><el-input v-model.trim="ListInfo.consumableName" placeholder="包装名称" maxlength="50" clearable
            class="publicCss" /></el-col>
        <el-col :span="3"><number-range class="publicCss" :min.sync="ListInfo.weightMin" :max.sync="ListInfo.weightMax"
            min-label="最小重量" max-label="最大重量" /></el-col>
        <el-col :span="3">
          <el-select v-model="ListInfo.isPrePack" class="publicCss" placeholder="外仓是否加工" clearable>
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" :disabled="isExport" @click="exportProps">导出</el-button>
          <el-button type="primary" @click="importProps">导入</el-button>
        </el-col>
      </el-row>
    </template>
    <vxetablebase :id="'basicData202408041845'" ref="table" v-loading="loading" :that="that" :is-index="true" :has-seq="false" :hasexpand="true"
      :tablefixed="true" :border="true" :table-data="tableData" :table-cols="tableCols" :is-selection="false"
      :is-select-column="false" style="width: 100%;  margin: 0" :height="'100%'" @sortchange="sortchange">
      <template slot="right">
        <vxe-column title="操作" width="70">
          <template #default="{ row, $index }">
            <div style="text-align: center;">
              <el-button type="text" @click="editProps(row.combineCode)">编辑</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
    <el-dialog v-dialogDrag title="设置包装编码" :visible.sync="addVisable" width="20%">
      <el-form ref="ruleForm" :model="ruleForm" status-icon :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="外仓是否加工" prop="isPrePack">
          <el-switch v-model="ruleForm.isPrePack" />
        </el-form-item>
        <el-form-item label="发货仓" prop="sendWmsName">
          <el-select v-model="ruleForm.sendWmsName" filterable placeholder="请选择" clearable
            @change="changeCode($event, 'wareHouse')">
            <el-option v-for="item in wareHouseList" :key="item.wms_co_id" :label="item.name" :value="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="包装编码" prop="consumableCode">
          <el-select v-model="ruleForm.consumableCode" filterable placeholder="请选择" clearable
            @change="changeCode($event, 'code')">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.goodsCode" />
          </el-select>
        </el-form-item>
        <el-form-item label="包装名称" prop="consumableName">
          <el-input v-model="ruleForm.consumableName" placeholder="请输入包装名称" clearable maxlength="30" disabled />
        </el-form-item>
        <el-form-item label="包装重量" prop="consumableWeight">
          <el-input-number v-model="ruleForm.consumableWeight" clearable maxlength="30" disabled />
        </el-form-item>
        <el-form-item label="净重" prop="weight">
          <el-input-number v-model="ruleForm.weight" placeholder="请输入组合装净重" :min="0" :max="9999"
            :controls="false" />
        </el-form-item>
        <el-form-item label="总重量" prop="totalWeight">
          <el-input-number :value="ruleForm.weight + ruleForm.consumableWeight" placeholder="请输入组合装净重" :min="0"
            :max="9999" disabled :controls="false" />
        </el-form-item>
        <el-form-item>
          <el-button @click="resetForm('ruleForm')">重置</el-button>
          <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog v-dialogDrag v-loading="importLoading" title="导入数据" :visible.sync="importVisible" width="30%">
      <div style="display: flex;flex-direction: column;justify-content: center;">
        <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
          :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
          <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
            <el-button size="small" type="primary">点击上传</el-button>
          </el-tooltip>
        </el-upload>
      </div>
      <div class="btnGroup">
        <el-button type="primary" @click="importVisible = false">取消</el-button>
        <el-button type="primary" @click="sumbit">确定</el-button>
      </div>
    </el-dialog>
  </MyContainer>
</template>
<script>
import numberRange from '@/components/number-range/index.vue'
import MyContainer from '@/components/my-container'
import vxetablebase from '@/components/VxeTable/yh_vxetable.vue'
import { pickerOptions } from '@/utils/tools'
import { download } from '@/utils/download'
import {
  pageGetTbWarehouseAsync

} from '@/api/inventory/prepack.js'
import {
  consumablePageGetData,
  baseDataGetColumns,
  baseDataPageGetData,
  baseDataExportData,
  baseDataGet,
  baseDataMerge,
  baseDataImportData
} from '@/api/vo/prePack'

export default {
  name: 'ScanCodePage',
  components: {
    MyContainer, vxetablebase, numberRange
  },
  data() {
    return {
      rules: {
        consumableName: [
          { max: 30, message: '长度不能超过30个字符', trigger: 'blur' }
        ],
        weight: [
          { type: 'number', message: '请输入数字', trigger: 'blur' }
        ]
      },
      ruleForm: { weight: 0, consumableWeight: 0 },
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null, // 开始时间
        endTime: null, // 结束时间
        combineCode: null, // 组合编码
        combineName: null, // 商品名称
        entityCode: null, // 实体编码
        consumableCode: null, // 包装编码
        consumableName: null// 包装耗材
      },
      timeRanges: [],
      tableCols: [],
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
      isExport: false,
      addVisable: false,
      options: [],
      wareHouseList: [],
      importVisible: false,
      importLoading: false,
      fileList: [],
      file: null
    }
  },
  async mounted() {
    await this.getCol()
    await this.getList()
    await this.getCodeList()
    await this.getWareHouse()
  },
  methods: {
    async uploadFile(data) {
      this.file = data.file
    },
    async sumbit() {
      if (this.file == null) return this.$message.error('请上传文件')
      this.$message.info('正在导入中,请稍后...')
      const form = new FormData()
      form.append('file', this.file)
      this.importLoading = true
      await baseDataImportData(form).then(({ success }) => {
        if (success) {
          this.$message.success('导入成功')
          this.importVisible = false
          this.getList()
        }
        this.importLoading = false
      }).catch(err => {
        this.importLoading = false
        this.$message.error('导入失败')
      })
    },
    importProps() {
      this.fileList = []
      this.file = null
      this.importVisible = true
    },
    removeFile(file, fileList) {
      this.file = null
    },
    async getWareHouse() {
      const params = {
        currentPage: 1,
        pageSize: 100000
      }
      const { data, success } = await pageGetTbWarehouseAsync(params)
      if (success) {
        this.wareHouseList = data.list
      }
    },
    changeCode(e, type) {
      // 优化上面代码
      if (type == 'code') {
        const res = e ? this.options.filter(item => item.goodsCode == e)[0] : null
        if (res) {
          this.ruleForm.consumableName = res.goodsName
          this.ruleForm.consumableWeight = res.weight
        }
      } else {
        const res = e ? this.wareHouseList.filter(item => item.name == e)[0] : null
        if (res) {
          this.ruleForm.sendWmsId = res.wms_co_id
        }
      }
    },
    async getCodeList() {
      const params = {
        currentPage: 1,
        pageSize: 100000
      }
      const { data } = await consumablePageGetData(params)
      this.options = data.list
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          try {
            await baseDataMerge(this.ruleForm)
            this.$message({
              type: 'success',
              message: '提交成功!'
            })
            this.addVisable = false
            this.getList()
          } catch (error) {
            this.$message.error('提交失败')
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    // 编辑数据
    async editProps(combineCode) {
      await this.getCodeList()
      const { data, success } = await baseDataGet({ code: combineCode })
      if (success) {
        this.ruleForm = data
        this.addVisable = true
      }
    },
    async getCol() {
      const { data, success } = await baseDataGetColumns()
      if (success) {
        this.tableCols = data
      }
    },
    // 导出数据,使用时将下面的方法替换成自己的接口
    async exportProps() {
      this.isExport = true
      await baseDataExportData(this.ListInfo).then(download).finally(() => {
        this.isExport = false
      })
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      if (this.ListInfo.weightMin !== undefined && this.ListInfo.weightMax !== undefined && this.ListInfo.weightMin > this.ListInfo.weightMax) {
        this.$message.error('最小重量不能大于最大重量')
        this.loading = false
        return
      }
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data: { list, total, summary }, success } = await baseDataPageGetData(this.ListInfo)
        if (success) {
          this.tableData = list
          this.total = total
          this.summaryarry = summary
        } else {
          this.$message.error('获取列表失败')
        }
      } catch (error) {
        this.$message.error('获取列表失败')
      } finally {
        this.loading = false
      }
    },
    // 每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1
      this.ListInfo.pageSize = val
      this.getList()
    },
    // 当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf('descending') == -1
        this.getList()
      }
    }
  }
}
</script>
<style scoped lang="scss">
.top {
  margin-bottom: 10px;

  .publicCss {
    width: 100%;
  }
}

.btnGroup {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
</style>
