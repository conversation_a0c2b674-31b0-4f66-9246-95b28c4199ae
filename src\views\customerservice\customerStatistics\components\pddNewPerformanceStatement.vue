<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-input v-model="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.snick" placeholder="昵称" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.sname" placeholder="姓名" maxlength="50" clearable class="publicCss" />
                <el-select v-model="ListInfo.groupName" placeholder="组别" clearable class="publicCss">
                    <el-option v-for="item in options" :key="item.label" :label="item.label" :value="item.label">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.pddInquireGradeWayDate" placeholder="版本时间" clearable class="publicCss">
                    <el-option v-for="item in historyTableInfo.tableData" :key="item.parentEffectiveDate"
                        :label="item.parentEffectiveDate + ' 系数版本'" :value="item.parentEffectiveDate">
                    </el-option>
                </el-select>
                <el-date-picker v-model="timeList" type="daterange" align="right" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" class="publicCss"
                    @change="changeTime" :clearable="false">
                </el-date-picker>
                <el-button type="primary" @click="getList">查询</el-button>
                <el-button type="primary" @click="exportProps">导出</el-button>
                <el-button type="primary" @click="dialogVisible = true">设置提成系数</el-button>
                <!-- <el-button type="primary" @click="computeDialog = true">一键计算</el-button> -->
            </div>
        </template>
        <vxetablebase :id="'pddNewPerformanceStatement202408041500'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
            :showsummary="true" :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :noToFixed="true"
            :summaryarry="summaryarry" @summaryClick='onsummaryClick' style="width: 100%; height: 690px; margin: 0" />
        <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="total" @page-change="Pagechange"
            @size-change="Sizechange" />

        <el-dialog title="设置提成系数" :visible.sync="dialogVisible" width="30%" :close-on-click-modal="false" v-dialogDrag
            @close="handleClose">
            <div class="topbox">
                <el-date-picker v-model="commissionDate" type="date" placeholder="生效日期">
                </el-date-picker>
                <div class="history" @click="addTable">新增一行</div>
            </div>
            <span style="color: red;">生效日期仅往后日期进行选择,计算方式仅以生效的提成系数计算!</span>
            <el-table :data="commissionList" max-height="400" style="margin-bottom: 10px;">
                <el-table-column type="index" width="50" />
                <el-table-column prop="gradeWayValue1" label="南昌">
                    <template #default="{ row, $index }">
                        <el-input-number v-model="row.gradeWayValue1" placeholder="南昌" min="0" max="999999999"
                            :controls="false" @change="changePar(row, $index)" precision="5" />
                    </template>
                </el-table-column>
                <el-table-column prop="gradeWayValue2" label="新余">
                    <template #default="{ row, $index }">
                        <el-input-number v-model="row.gradeWayValue2" placeholder="新余" disabled min="0" max="999999999"
                            :controls="false" precision="5" />
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template #default="{ row, $index }">
                        <el-button type="danger" @click="deleteProps(row, $index)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="footer">
                <div class="history" @click="viewHistory">查看历史提成系数</div>
                <div class="btnbox">
                    <el-button @click="handleClose" style="margin-right: 20px;">取消</el-button>
                    <el-button type="primary" @click="handleSubmit">确定</el-button>
                </div>
            </div>

        </el-dialog>

        <el-dialog title="历史提成系数" :visible.sync="historyDialog" width="30%" :close-on-click-modal="false" v-dialogDrag>
            <div class="historyBox">
                <div v-for="item in historyTableInfo.tableData" class="parBox">
                    <p>{{ item.parentEffectiveDate }}提成系数</p>
                    <div v-for="childItem in item.children" class="childBox">
                        <div class="child_item">南昌排名{{ childItem.gradeIndex }}：{{ childItem.gradeWayValue1 }}</div>
                        <div class="child_item">新余排名{{ childItem.gradeIndex }}：{{ childItem.gradeWayValue2 }}</div>
                    </div>
                </div>
            </div>
        </el-dialog>

        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false" direction="btt">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" @change="chatSearch" :picker-options="pickerOptions" style="margin: 10px;" />
                <buschar :analysisData="chatProp.data"></buschar>
            </div>
            <div v-else v-loading="chatProp.chatLoading"></div>
        </el-drawer>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import buschar from '@/components/Bus/buschar'
import dayjs from 'dayjs'
import { pickerOptions } from '@/utils/tools'
import { getPddInquireGradeComputePageList, savePddInquireGradeWay, getPddInquireGradeComputeChat, getPddInquireGradeWayPageList, exportPddInquireGradeComputeList } from '@/api/customerservice/pddInquirs'
const tableCols = [
    { istrue: true, prop: 'groupName', label: '组别', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'sname', label: '姓名', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom' },
    { istrue: true, prop: 'snick', label: '昵称', sortable: 'custom' },
    { istrue: true, prop: 'inquirs', label: '咨询人数', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'successpayrate', label: '订单转化率', width: '100', sortable: 'custom', formatter: (row) => row.successpayrate + '%' },
    { istrue: true, prop: 'gradeCommission', label: '提成', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'gradeWayValue', label: '提成系数', sortable: 'custom' },
    { istrue: true, prop: 'gradeIndex', label: '排名', width: '100', sortable: 'custom', formatter: (row) => row.gradeIndex == '0' ? '' : row.gradeIndex },
    { istrue: true, prop: 'effectiveDate', label: '日期', sortable: 'custom', formatter: (row) => dayjs(row.effectiveDate).format('YYYY-MM-DD') },
    {
        istrue: true, prop: 'buchar', type: "button", width: '100', label: '趋势图', summaryEvent: true, btnList: [
            { label: "查看", handle: (that, row) => that.openChat(row) },
        ]
    }
]

const historyTableCols = [
    { istrue: true, prop: 'effectiveDate', label: '生效日期', sortable: 'custom' },
    { istrue: true, prop: 'gradeIndex', label: '排名', sortable: 'custom' },
    { istrue: true, prop: 'gradeWayValue1', label: '南昌', sortable: 'custom' },
    { istrue: true, prop: 'gradeWayValue2', label: '新余', sortable: 'custom' },
]

export default {
    name: 'pddNewPerformanceStatement',
    props: {
        filter: {
            type: Object,
            default: () => { }
        }
    },
    components: { MyContainer, vxetablebase, buschar },
    data() {
        return {
            ListInfo: {//列表参数
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                startDate: null,//开始时间
                endDate: null,//结束时间
                shopCode: null,//店铺编码
                shopName: null,//店铺
                groupName: null,//分组
                groupShortName: null,//分组归属
                snick: null,//昵称
                sname: null,//姓名
                pddInquireGradeWayDate: null,//版本时间
            },
            commissionList: [//提成系数列表
                {
                    id: 0,//主键id
                    enabled: true,//是否启用
                    createdTime: null,//创建时间
                    total: 0,//总提成
                    index: 0,//排名
                    batchNumber: 0,//批次号
                    createdUserId: 0,
                    createdUserName: null,
                    effectiveDate: null,//生效时间
                    gradeIndex: 0,//等级
                    gradeWayValue1: 0,//南昌
                    gradeWayValue2: 0,//新余
                },
            ],
            commissionDate: null,//生效时间
            pickerOptions,
            historyInfo: {//历史提成系数参数
                orderBy: 'effectiveDate',
                isAsc: false,
            },
            version: [],//版本时间
            chatProp: {
                chatDialog: false,//趋势图弹窗
                chatTime: null,//趋势图时间
                chatLoading: true,//趋势图loading
                data: [],//趋势图数据
            },
            chatInfo: {
                groupName: null,//组别
                sname: null,//姓名
                shopName: null,//店铺
                snick: null,//昵称
                startDate: null,//开始时间
                endDate: null,//结束时间
                pddInquireGradeWayDate: null,//版本时间
            },
            options: [//组别下拉框
                {
                    label: '南昌'
                },
                {
                    label: '新余'
                }
            ],
            summaryarry: null,
            that: this,
            tableCols,//表格列
            historyTableCols,//历史提成系数表格列
            timeList: null,//时间
            total: 0,//总条数
            tableData: [],//表格数据
            historyTableInfo: {//历史提成系数
                tableData: [],
                total: 0,
            },
            dialogVisible: false,//设置提成系数弹窗
            computeDialog: false,//一键计算弹窗
            historyDialog: false,//历史提成系数弹窗
        }
    },
    mounted() {
        this.getVersion()
    },
    methods: {
        async exportProps() {
            const { data } = await exportPddInquireGradeComputeList(this.ListInfo)
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '拼多多绩效结果' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        //趋势图时间改变
        async chatSearch() {
            this.chatInfo.startDate = dayjs(this.chatProp.chatTime[0]).format('YYYY-MM-DD')
            this.chatInfo.endDate = dayjs(this.chatProp.chatTime[1]).format('YYYY-MM-DD')
            this.chatProp.chatLoading = true
            const data = await getPddInquireGradeComputeChat(this.chatInfo)
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        //汇总趋势图
        async onsummaryClick(property) {
            this.chatInfo.groupName = null
            this.chatInfo.sname = null
            this.chatInfo.shopName = null
            this.chatInfo.snick = null
            this.chatInfo.pddInquireGradeWayDate = this.ListInfo.pddInquireGradeWayDate ? this.ListInfo.pddInquireGradeWayDate : null
            this.chatProp.chatLoading = true
            this.publicChangeTime(this.chatInfo)
            if (property == 'buchar') {
                const data = await getPddInquireGradeComputeChat(this.chatInfo)
                this.chatProp.data = data
                this.chatProp.chatDialog = true
                this.chatProp.chatLoading = false
            }
        },
        //获取版本
        async getVersion() {
            const { success, data } = await getPddInquireGradeWayPageList(this.historyInfo)
            if (success) {
                //如果data中的parentEffectiveDate与effectiveDate时间相同,那么就将effectiveDate添加到children中
                let parentEffectiveDateArr = data.filter(item => item.parentEffectiveDate !== null)
                //2.找到所有的effectiveDate
                let effectiveDateArr = data.filter(item => (item.effectiveDate !== null && item.parentEffectiveDate === null))
                //转化成树
                parentEffectiveDateArr.forEach(item => {
                    item.parentEffectiveDate = dayjs(item.parentEffectiveDate).format('YYYY-MM-DD')
                    //将item.parentEffectiveDate
                    effectiveDateArr.forEach(item2 => {
                        item2.effectiveDate = dayjs(item2.effectiveDate).format('YYYY-MM-DD')
                        if (item.parentEffectiveDate === item2.effectiveDate) {
                            if (item.children) {
                                item.children.push(item2)
                            } else {
                                item.children = []
                                item.children.push(item2)
                            }
                        }
                    })
                })
                this.historyTableInfo.tableData = parentEffectiveDateArr
                this.getList()
            } else {
                this.getList()
            }
        },
        publicChangeTime(row) {
            if (this.timeList) {
                let time = dayjs(this.timeList[1]).diff(dayjs(this.timeList[0]), 'day')
                if (time >= 30) {
                    this.chatProp.chatTime = this.timeList
                    this.chatInfo.startDate = dayjs(this.timeList[0]).format('YYYY-MM-DD')
                    this.chatInfo.endDate = dayjs(this.timeList[1]).format('YYYY-MM-DD')
                } else {
                    //否则就将时间间隔设置为三十天
                    this.chatProp.chatTime = [dayjs(this.timeList[1]).subtract(30, 'day').format('YYYY-MM-DD'), this.timeList[1]]
                    this.chatInfo.startDate = dayjs(this.timeList[1]).subtract(30, 'day').format('YYYY-MM-DD')
                    this.chatInfo.endDate = dayjs(this.timeList[1]).format('YYYY-MM-DD')
                }
            }
            this.chatInfo.groupName = row.groupName
            this.chatInfo.sname = row.sname
            this.chatInfo.shopName = row.shopName
            this.chatInfo.snick = row.snick
            this.chatInfo.pddInquireGradeWayDate = this.ListInfo.pddInquireGradeWayDate ? this.ListInfo.pddInquireGradeWayDate : null
        },
        async openChat(row) {
            //如果this.timeList的时间间隔大于等于三十天,那么就提示时间间隔不能大于三十天
            this.publicChangeTime(row)
            this.chatProp.chatLoading = true
            const data = await getPddInquireGradeComputeChat(this.chatInfo)
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        //查看历史提成系数
        async viewHistory() {
            this.getVersion()
            this.historyDialog = true
        },

        //保存提成系数
        async handleSubmit() {
            //如果有生效时间
            if (this.commissionDate) {
                this.commissionList.forEach(item => item.effectiveDate = dayjs(this.commissionDate).format('YYYY-MM-DD'))
            } else {
                this.commissionList.forEach(item => item.effectiveDate = null)
                this.$message.error('请选择生效时间')
                return
            }
            this.commissionList.forEach((item, i) => item.gradeIndex = i + 1)
            const { success } = await savePddInquireGradeWay(this.commissionList)
            if (success) {
                this.$message.success('保存成功')
                this.clear()
                this.dialogVisible = false
            }
        },
        changeTime(e) {
            if (!e) {
                this.ListInfo.startDate = null
                this.ListInfo.endDate = null
            } else {
                this.ListInfo.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs(e[1]).format('YYYY-MM-DD')
            }
            this.getList()
        },
        deleteProps(row, i) {
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.commissionList.splice(i, 1)
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        addTable() {
            this.commissionList.push({
                id: 0,//主键id
                enabled: true,//是否启用
                createdTime: null,//创建时间
                total: 0,//总提成
                index: 0,//排名
                batchNumber: 0,//批次号
                createdUserId: 0,
                createdUserName: null,
                effectiveDate: null,
                gradeIndex: 0,
                gradeWayValue1: 0,
                gradeWayValue2: 0,
            })
        },
        changePar(row, index) {
            if (row.gradeWayValue1) {
                this.commissionList[index].gradeWayValue2 = row.gradeWayValue1 * 0.5
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        //每页数量改变
        historySizechange(val) {
            this.historyInfo.currentPage = 1;
            this.historyInfo.pageSize = val;
            this.viewHistory()
        },
        //当前页改变
        historyPagechange(val) {
            this.historyInfo.currentPage = val;
            this.viewHistory()
        },
        //查询列表
        async getList() {
            //如果有店铺,昵称,姓名就清空空格,用replace
            if (this.ListInfo.shopName) {
                this.ListInfo.shopName = this.ListInfo.shopName ? this.ListInfo.shopName.replace(/\s+/g, "") : null;
            }
            if (this.ListInfo.snick) {
                this.ListInfo.snick = this.ListInfo.snick ? this.ListInfo.snick.replace(/\s+/g, "") : null;
            }
            if (this.ListInfo.sname) {
                this.ListInfo.sname = this.ListInfo.sname ? this.ListInfo.sname.replace(/\s+/g, "") : null;
            }
            if (this.historyTableInfo.tableData.length > 0 && this.ListInfo.pddInquireGradeWayDate == null) {
                this.ListInfo.pddInquireGradeWayDate = this.historyTableInfo.tableData[0].parentEffectiveDate
            }
            if (!this.timeList) {
                //默认时间为当前时间往前推一个月
                this.ListInfo.startDate = dayjs().subtract(1, 'month').format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
                this.timeList = [this.ListInfo.startDate, this.ListInfo.endDate]
            }
            const { data, success } = await getPddInquireGradeComputePageList(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
                console.log(this.summaryarry, 'this.summaryarry');
            } else {
                //获取列表失败
                window.$message.error('获取列表失败')
            }
        },
        handleClose() {
            this.clear()
            this.dialogVisible = false;
        },
        //清空
        clear() {
            this.commissionList = [
                {
                    id: 0,//主键id
                    enabled: true,//是否启用
                    createdTime: null,//创建时间
                    total: 0,//总提成
                    index: 0,//排名
                    batchNumber: 0,//批次号
                    createdUserId: 0,
                    createdUserName: null,
                    effectiveDate: null,//生效时间
                    gradeIndex: 0,//等级
                    gradeWayValue1: 0,//南昌
                    gradeWayValue2: 0,//新余
                },
            ]
            this.commissionDate = null
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        sortchange1({ order, prop }) {
            if (prop) {
                this.historyInfo.orderBy = prop
                this.historyInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getVersion()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
}

.publicCss {
    width: 220px;
    margin-right: 10px;
}

.topbox {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    justify-content: space-between;


}

.btnbox {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

.history {
    cursor: pointer;
    font-size: 16px;
    margin-right: 10px;
    line-height: 16px;
    color: #409EFF;
}

.footer {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.historyBox {
    width: 90%;
    height: 500px;
    overflow: auto;

    .parBox {
        margin-bottom: 20px;
    }

    .childBox {
        width: 90%;
        display: flex;
        justify-content: space-between;
        margin: 10px;

        .child_item {
            width: 45%;
        }
    }
}
</style>