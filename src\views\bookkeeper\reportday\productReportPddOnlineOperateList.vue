<template>
  <container>
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="提交时间">
          <el-date-picker style="width: 230px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="filter.oldProductCode" placeholder="原产品ID" style="width:160px;" maxlength="50"
            clearable />
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="filter.newProductCode" placeholder="新产品ID" style="width:160px;" maxlength="50"
            clearable />
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="filter.productName" placeholder="新链接ID商品名称" style="width:160px;" maxlength="50"
            clearable />
        </el-form-item>
        <el-form-item>
          <el-select filterable clearable v-model="filter.shopCode" placeholder="新链接ID店铺" style="width: 120px">
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select filterable v-model="filter.operateType" placeholder="类型" clearable style="width: 160px">
            <el-option label="一阶段正利润" value="一阶段正利润" />
            <el-option label="一阶段负利润" value="一阶段负利润" />
            <el-option label="一阶段跑不动，连续5天0订单" value="一阶段跑不动，连续5天0订单" />
            <el-option label="二阶段负利润" value="二阶段负利润" />
            <el-option label="二阶段正利润" value="二阶段正利润" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select filterable v-model="filter.operateResult" placeholder="最终操作" clearable style="width: 160px">
            <el-option label="提日限额" value="提日限额" />
            <el-option label="拉投产" value="拉投产" />
            <el-option label="降投产" value="降投产" />
            <el-option label="涨售价" value="涨售价" />
            <el-option label="停止推广" value="停止推广" />
            <el-option label="下架链接" value="下架链接" />
          </el-select>
        </el-form-item>
        <el-form-item style="margin-left: 10px;">
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
    <ces-table :tablekey="'productReportPddOnlineOperateList20230718'" ref="table" :that='that' :isIndex='true'
      :hasexpand='true' @sortchange='sortchange' :tableData='list' :tableCols='tableCols' :isSelection="false"
      :tableHandles='tableHandles' :loading="listLoading" :tablefixed='true' :isSelectColumn="false"
      :summaryarry="summaryarry" :hasexpandRight='true'   style="height: 99%;">
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>
  </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { getDistributionGoodsOperateLogAsync } from "@/api/operatemanage/distributiongoodsOnline.js"
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { formatLinkProCode } from "@/utils/tools";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';

const tableCols = [
  { istrue: true, prop: 'yearMonthDay', label: '年月日', tipmesg: '', width: '80', sortable: 'custom', },
  { istrue: true, prop: 'distributionTime', label: '铺货时间', tipmesg: '', width: '160', sortable: 'custom', },
  { istrue: true, prop: 'oldProductCode', label: '原链接ID', tipmesg: '', type: 'html', width: '120', sortable: 'custom', formatter: (row) => formatLinkProCode(2, row.oldProductCode) },
  { istrue: true, prop: 'newProductCode', label: '新链接ID', tipmesg: '', type: 'html', width: '120', sortable: 'custom', formatter: (row) => formatLinkProCode(2, row.newProductCode) },
  { istrue: true, prop: 'b.NewShopCode', label: '新链接ID店铺', tipmesg: '', type: 'custom', width: '120', sortable: 'custom', formatter: (row) => row.shopName },
  { istrue: true, prop: 'a.newProductCode', label: '新链接ID商品名', tipmesg: '', type: 'custom', width: '120', sortable: 'custom', formatter: (row) => row.proName },
  { istrue: true, prop: 'yestorDayOrderCount', label: '昨日订单量', tipmesg: '', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'yestorDayProfit3', label: '昨日毛3', tipmesg: '', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'createdTime', label: '提交时间', tipmesg: '', width: '160', sortable: 'custom', },
  { istrue: true, prop: 'operateStrForDisPlay', label: '操作集合', tipmesg: '', sortable: 'custom', },
  { istrue: true, prop: 'operateType', label: '类型', tipmesg: '', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'operateStatus', label: '状态', tipmesg: '', width: '60', sortable: 'custom' },
  { istrue: true, prop: 'operateResult', label: '最终操作', tipmesg: '', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'operateValue', label: '操作数值', width: '120', },
  { istrue: true, prop: 'nextNode', label: '是否有后续', width: '120', formatter: (row) => row.nextNode == null ? "" : (row.nextNode > 0 ? "是" : "否") },
  { istrue: true, prop: 'operateErrorReason', label: '失败原因' }
]

const tableHandles = [];

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
  name: 'ProductReportPddOnlineList',
  components: { container, cesTable, MyConfirmButton },
  data() {
    return {
      that: this,
      filter: {
        oldProductCode: null,
        newProductCode: null,
        timerange: [startDate, endDate],
        startTime: null,
        endTime: null,
        operateType: null,
        operateResult: null,
        shopCode: null,
        productName: null
      },
      list: [],
      skuData: [],
      pager: { OrderBy: "createdTime", IsAsc: false },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      pickOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      sels: [],
      listLoading: false,
      summaryarry: {},
      shopList: []
    };
  },

  async mounted() {
    await this.getShopList();
    await this.onSearch()
  },

  methods: {
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    async getlist() {
      if (this.pager.OrderBy == null) {
        this.pager.OrderBy = "createdTime";
        this.pager.IsAsc = false;
      }
      let pager = this.$refs.pager.getPager();
      let page = this.pager;
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      const params = { ...pager, ...page, ... this.filter }
      if (params === false) {
        return;
      }
      this.listLoading = true
      const res = await getDistributionGoodsOperateLogAsync(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.list = data
      this.summaryarry = res.data.summary;
    },
    async nSearch() {
      await this.getlist()
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    },
    async getShopList() {
      const res1 = await getAllShopList({ platforms: [2] });
      this.shopList = [];
      res1.data?.forEach(f => {
        if (f.isCalcSettlement && f.shopCode)
          this.shopList.push(f);
      });
    },
  }
};
</script>

<style lang="scss" scoped></style>
