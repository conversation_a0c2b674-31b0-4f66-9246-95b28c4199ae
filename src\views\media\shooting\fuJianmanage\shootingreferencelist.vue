<template>
     <my-container v-loading="pageLoading">
        <template #header> 
        </template>  
        <ces-table ref="table" 
                    :that='that' 
                    :isIndex='true' 
                    :hasexpand='false'
                    :isSelectColumn='true'
                    :tableData='tasklist' 
                    :tableCols='tableCols'  
                    :loading="listLoading" > 
                    <el-table-column type="expand">
                        <template slot-scope="props">
                            <div>
                                <el-table :data="props.row.detaildata" style="width: 100%">
                                    <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                                    </el-table-column>
                                </el-table>
                            </div>
                        </template> 
                    </el-table-column>
                    <template slot='extentbtn'>
                    <el-button-group> 
                        <el-button type="primary" @click="OnCreateTask">创建参考</el-button> 
                        <el-button type="primary" @click="onSearch">刷新</el-button> 
                    </el-button-group>
            </template>
        </ces-table> 
        
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList" />
        </template>

        <el-dialog
            v-if="dialogVisible"
            :title="title"
            :visible.sync="dialogVisible"
            width="95%"
            @close="cleardig"
            fullscreen>
            <shootingcreatereference ref="change" @cleardig="cleardig" :listid="rewuid"></shootingcreatereference>
        </el-dialog>
    </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";  
import cesTable from "@/components/Table/table.vue";
import { pageReferenceViewTaskAsync,getReferenceMainReferencs,saveReferenceMainReferencsForVedio,saveReferenceMainReferencsForImg,saveReferenceMainReferencsForSku} from '@/api/media/referencemanage';
import shootingcreatereference from '@/views/media/shooting/fuJianmanage/shootingcreatereference'; 
const tableCols = [
    { istrue: true, prop: 'referenceManageTaskId', label: '任务编码', width: '200'   },
    { istrue: true, prop: 'taskName', label: '任务名称', width: '200'   },
    { istrue: true, prop: 'createdTime', label: '创建时间', width: '180'  },
    { istrue: true, prop: 'shootingTaskId', label: '新品任务编号', width: '200'   },
    {
        istrue: true, type: "button", label: '操作', width: "200",
        btnList: [
            { label: "编辑",  handle: (that, row) => that.editReference(row) },
            
        ]
    }
];   
export default { 
  components: { MyContainer , cesTable,shootingcreatereference},
    data() {
        return {  
            that: this,
            pageLoading: false,
            summaryarry:[],
            tasklist:[],
            sels: [], // 列表选中列
            tableCols: tableCols,
            listLoading: false,
            total:0,
            pager: { OrderBy: "createdTime", IsAsc: false },
            filter: {
               
            }, 
            dialogVisible: false,
            rewuid: null,
            title: '创建参考'
        };
    },
    
    async mounted() {
        await this.onSearch();
    }, 
    methods: {
        OnCreateTask(){
            let _this = this;
            _this.title = '创建参考';
            _this.dialogVisible = true;
        },
        editReference(row) {
            let _this = this;
            _this.title = '编辑参考';
             console.log("编辑行",row)
             _this.rewuid = row.referenceManageTaskId;
             _this.dialogVisible = true;
        },
        cleardig(val){
            let _this = this;
             _this.rewuid = '';
             this.$refs.change.changefuc(false)
             console.log("夫接收值",val)
             _this.dialogVisible = val;
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            this.getTaskList();
        },
        async getTaskList() 
        { 
            //...this.filter
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
               
            };
            this.listLoading = true;
            const res = await pageReferenceViewTaskAsync(params); 
            this.listLoading = false;  
            this.total = res.data.total
            this.tasklist = res.data.list;
            //this.summaryarry = { videoTaskId_sum: " 0" };
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
    },
};
</script>
 

