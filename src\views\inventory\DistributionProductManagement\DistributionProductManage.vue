<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="分销商品编码与商品编码" name="tab0" style="height: 100%;">
                <DistributionProductCodeManagement  ref="refundData" style="height: 100%;"></DistributionProductCodeManagement>
            </el-tab-pane>
            <el-tab-pane label="供销商模块" name="tab1" style="height: 100%;">
                <SupplierProductCodeManagement ref="IDWarning" style="height: 100%;"></SupplierProductCodeManagement>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import  DistributionProductCodeManagement  from "@/views/inventory/DistributionProductManagement/DistributionProductCodeManagement";
import  SupplierProductCodeManagement  from "@/views/inventory/DistributionProductManagement/SupplierProductCodeManagement";
export default {
    name: "refundManage",
    components: { MyContainer, DistributionProductCodeManagement, SupplierProductCodeManagement },
    data() {
        return {
            that: this,
            activeName: 'tab0',
            pageLoading: false,
            // filter: {
            // },
        };
    },
    async mounted() {
    },
    methods: {
        async onSearch() {
            this.$nextTick(() => {
            })
        },
    },
}
</script>