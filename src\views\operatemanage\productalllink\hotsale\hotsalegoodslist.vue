<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select style="width:100px" @change="platformNameChange" clearable v-model="Filter.platformName" placeholder="选择平台">
                            <el-option v-for="(item ) in plantformList" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-select style="width:100px" clearable v-model="Filter.saleCountIncreaseBfIgnore" placeholder="销量变化" @change="saleCountIncreaseBfChanged">
                            <!-- 1:0-50   2:50-100   3:100-500   4:500-1000    5:1000-2000    6:2000以上  （筛选） -->

                            <el-option label="0-50" value="1" />
                            <el-option label="50-100" value="2" />
                            <el-option label="100-500" value="3" />
                            <el-option label="500-1000" value="4" />
                            <el-option label="1000-2000" value="5" />
                            <el-option label="2000以上" value="6" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-select style="width:130px" clearable multiple v-model="Filter.promoteTypes" placeholder="推广方式">
                            <!-- 1:0-50   2:50-100   3:100-500   4:500-1000    5:1000-2000    6:2000以上  （筛选） -->

                            <el-option label="投放广告" value="投放广告" />
                            <el-option label="短视频推广" value="短视频推广" />
                            <el-option label="直播" value="直播" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-select style="width:100px" clearable v-model="Filter.isChoosed" placeholder="是否已选">
                            <el-option label="是" value="是" />
                            <el-option label="否" value="否" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-date-picker style="width:220px" v-model="Filter.gDate" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-select style="width:120px" clearable v-model="Filter.categoryName1" @change="categoryChanged1" placeholder="选择一级类目">
                            <el-option v-for="(item ) in categoryNameList1" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-select style="width:120px" clearable v-model="Filter.categoryName2" @change="categoryChanged2" placeholder="选择二级类目">
                            <el-option v-for="(item ) in categoryNameList2" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-select style="width:120px" clearable v-model="Filter.categoryName3" @change="categoryChanged3" placeholder="选择三级类目">
                            <el-option v-for="(item ) in categoryNameList3" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-select style="width:120px" clearable v-model="Filter.categoryName4" placeholder="选择四级类目">
                            <el-option v-for="(item ) in categoryNameList4" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input-number v-model="Filter.chooseRateMin" :min="0" :max="999999999" :step="50" placeholder="最小推荐指数" style="width:150px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input-number v-model="Filter.chooseRateMax" :min="0" :max="999999999" :step="50" placeholder="最大推荐指数" style="width:150px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input-number v-model="Filter.saleCount24HMin" :min="0" :max="999999999" :step="100" placeholder="24H最小销量" style="width:150px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input-number v-model="Filter.saleCount24HMax" :min="0" :max="999999999" :step="100" placeholder="24H最大销量" style="width:150px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.goodsName" type="text" maxlength="100" clearable placeholder="商品名称" style="width:120px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.shopName" type="text" maxlength="100" clearable placeholder="店铺名称" style="width:120px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model.trim="Filter.goodsCompeteId" type="text" maxlength="40" clearable placeholder="竞品ID" style="width:120px;" />
                    </el-button>

                    

                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button @click="()=>{Filter={};}">清空查询条件</el-button>

                </el-button-group>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='tbdatalist' @select='selectchange' :isSelection='false' :isSelectColumn="true" :tableCols='tableCols' :loading="listLoading" @cellclick="cellclick">

            <template slot='extentbtn'>
                <el-button-group>
                    <el-button type="primary" href="/static/excel/productalllink/热销商品Template.xlsx" target="_blank" @click="downImportTemp(0)">下载导入抖店模板</el-button>
                    <el-button type="primary" href="/static/excel/productalllink/热销商品拼多多Template.xlsx" target="_blank" @click="downImportTemp(1)">下载导入拼多多模板</el-button>
                    <el-button type="primary" @click="onImport">导入</el-button>
                </el-button-group>
            </template>

            <!-- prop="IncreaseGoOnDays"  -->
            <el-table-column :width="140" label="七天趋势" fixed="right">
                <template slot="header" slot-scope='{}'>
                    <el-select clearable v-model="Filter.increaseGoOnDays" placeholder="七天趋势" @change="onSearch">
                        <el-option label="连续7日增长" value="7" />
                        <el-option label="连续6日增长" value="6" />
                        <el-option label="连续5日增长" value="5" />
                        <el-option label="连续4日增长" value="4" />
                        <el-option label="连续3日增长" value="3" />
                        <el-option label="连续2日增长" value="2" />
                        <el-option label="连续1日增长" value="1" />
                    </el-select>
                </template>
                <template slot-scope="scope">
                    <div style="height: 60px;width:100%;" :ref="'echarts'+scope.row.id" :id="'rptIdecharts'+scope.row.id" v-loading="echartsLoading"></div>
                </template>
            </el-table-column>

            <el-table-column width="120" label="操作" fixed="right">
                <template slot-scope="scope">
                    <el-button type="text" @click="showHotGoodsEchartByDateRange(scope.row)">趋势图</el-button>
                    <el-button type="text" @click="reqDateInfo(scope.row)">申请数据</el-button><br>
                    <el-button type="text" @click="doSelectSw(scope.row,true)">添加到已选品</el-button>
                    <!-- <el-button type="text" @click="doSelectSw(scope.row,false)">取消选择</el-button><br> -->
                    <el-button type="text" @click="onSeeDetail(scope.row)" v-if="seeDetailHidele(scope.row)">详情</el-button>
                </template>
            </el-table-column>

        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
        </template>

        <el-dialog title="热销商品导入" :visible.sync="dialogVisibleSyj" width="30%">
            <span>
                <el-select v-model="tempType">
                    <el-option :value="0" label="抖店"></el-option>
                    <el-option :value="1" label="拼多多"></el-option>
                </el-select>
                <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传</my-confirm-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span v-if="buscharDialog.isNot7Day">
                <template>
                    <el-form class="ad-form-query" :model="goodsEchartDtlFilter" @submit.native.prevent label-width="100px">
                        日期：<el-date-picker style="width: 260px" v-model="goodsEchartDtlFilter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false"></el-date-picker>

                        <el-button type="primary" @click="showHotGoodsEchartByRefresh">刷新</el-button>

                    </el-form>
                </template>
            </span>
            <span>
                <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="" :visible.sync="chooseFormDataVisible" width="700px" v-dialogDrag>
            <span>
                <template>
                    <el-form class="ad-form-query" :model="chooseFormData" ref="chooseForm" @submit.native.prevent label-width="100px">
                        <el-form-item label="商品ID">
                            <el-input v-model="chooseFormData.goodsId" :readonly="true"></el-input>
                        </el-form-item>
                        <el-form-item label="商品名称">
                            <el-input v-model="chooseFormData.goodsName" :readonly="true"></el-input>
                        </el-form-item>

                        <el-row>
                            <el-col :span="10">
                                <el-form-item label="竞品平台" prop="platform" :rules="[{ required: true, message: '请选择竞品原平台', trigger: 'blur' }]">
                                    <el-select style="float: left; " v-model="chooseFormData.platform" placeholder="请选择竞品原平台">

                                        <el-option v-for="item in platformlist" v-if="item.value!=0" :key="'p2f-' + item.value" :label="item.label"
                                            :value="item.value"></el-option>


                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="10">
                                <el-form-item label="运营平台" prop="newPlatform"
                                    :rules="[{ required: true, message: '请选择要做的平台', trigger: 'blur' }]">
                                    <el-select v-model="chooseFormData.newPlatform" placeholder="请选择要做的平台" >
                                        <el-option v-for="item in platformlist" v-if="item.value!=0" :key="'np2f-' + item.value" :label="item.label"
                                            :value="item.value"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row> 


                        <el-form-item label="竞品ID">
                            <el-input v-model="chooseFormData.goodsCompeteId" :maxlength="100" :minlength="4"></el-input>
                        </el-form-item>
                        <el-form-item label="竞品标题">
                            <el-input v-model="chooseFormData.goodsCompeteName" :maxlength="100" :minlength="4"></el-input>
                        </el-form-item>

                    </el-form>
                </template>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitChooseForm">添加选品</el-button>
                <el-button @click="chooseFormDataVisible = false">取消</el-button>
            </span>
        </el-dialog>

        <el-dialog title="查看产品" :visible.sync="alllinkproductinfoVisible" width='88%' height='650px' v-dialogDrag>
            <alllinkproductinfo ref="alllinkproductinfo" style="z-index:10000;height:650px" />
        </el-dialog>

    </my-container>
</template>
<script>  

    import { pageHotSaleGoodsAsync, importHotSaleGoodsAsync, isDoHotSaleGoodsAsync, getAllLinkPlantformsAsync, getAllLinkCategoryNamesAsyncByParent, goodsInfoQueryReqAsync, getAllHotGoodsWaitQueryAsync, getHotSaleGoodsEchartByDateAsync, getUserInfo } from '@/api/operatemanage/productalllink/alllink'
    import dayjs from "dayjs";
    import cesTable from "@/components/Table/table.vue";
    import { formatTime } from "@/utils";
    import { formatmoney, formatPercen, getUrlParam, platformlist, setStore, getStore } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import * as echarts from 'echarts'
    import buschar from '@/components/Bus/buschar'
    import alllinkproductinfo from "@/views/operatemanage/productalllink/alllinkproductinfo.vue";
    import { getCurUserDepartmentName } from '@/api/operatemanage/base/dingdingShow'

    const tableCols = [
        //{ istrue: true, prop: 'goodsId', label: '主键', display: false },
        { istrue: true, prop: 'goodsDate', label: '日期', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.goodsDate, 'YYYY-MM-DD') },
        { istrue: true, prop: 'platformName', label: '平台', width: '100', sortable: 'custom' },
        //{ istrue: false, prop: 'categoryName', label: '类目', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'categoryName1', label: '一级类目', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'categoryName2', label: '二级类目', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'categoryName3', label: '三级类目', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'categoryName4', label: '四级类目', width: '100', sortable: 'custom' },
        { istrue: false, prop: 'categoryFullName', label: '类目', width: '200', sortable: 'custom' },
        { istrue: true, prop: 'shopName', label: '店铺', width: '100', sortable: 'custom', handle: (that, row) => that.showShop(row) },// type: 'click', 
        { istrue: true, prop: 'goodsImgUrl', label: '商品图片', width: '80', type: 'image' },
        { istrue: true, prop: 'goodsName', label: '商品名称', minwidth: "360", sortable: 'custom', type: 'click', handle: (that, row) => that.showGoods(row) },
        { istrue: true, prop: 'sameGoodsName', label: '相似商品名称', width: '160', sortable: 'custom' },
        { istrue: true, prop: 'promoteType', label: '推广方式', width: '120', sortable: 'custom', importTemp: 0 },
        { istrue: true, prop: 'maxPrice', label: '价格', width: '80', sortable: 'custom', formatter: (row) => formatmoney(row.maxPrice) },
        { istrue: false, prop: 'priceRage', label: '价格范围', width: '140', sortable: 'custom' },
        { istrue: true, prop: 'chooseRate', label: '选品指数', width: '80', sortable: 'custom', importTemp: 0, formatter: (row) => formatPercen(row.chooseRate) },




        { istrue: true, prop: 'rankChange', label: '排名变化', width: '80', sortable: 'custom', importTemp: 1 },
        { istrue: true, prop: 'newSaleCount24H', label: '销量', tipmesg: '24小时销量', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'topListDays', label: '上榜天数', width: '80', sortable: 'custom', importTemp: 1 },
        { istrue: true, prop: 'rank24H', label: '日排名', width: '80', sortable: 'custom', importTemp: 1 },
        { istrue: true, prop: 'newSaleCount7D', label: '近7天销量', width: '80', sortable: 'custom', importTemp: 1 },
        { istrue: true, prop: 'rank7D', label: '周排名', width: '80', sortable: 'custom', importTemp: 1 },
        { istrue: true, prop: 'newSaleCount30D', label: '近30天销量', width: '80', sortable: 'custom', importTemp: 1 },
        { istrue: true, prop: 'rank30D', label: '月排名', width: '80', sortable: 'custom', importTemp: 1 },


        { istrue: true, prop: 'totalSaleCount', label: '累计销量', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'totalComment', label: '评论数', width: '80', sortable: 'custom', importTemp: 0 },
        { istrue: true, prop: 'totalNiceCommentRate', label: '好评率', width: '80', sortable: 'custom', importTemp: 0, formatter: (row) => formatPercen(row.totalNiceCommentRate) },
        { istrue: true, prop: 'saleCountIncreaseBf', label: '增长量', width: '80', sortable: 'custom' },
        {
            istrue: true, prop: 'infoQueryState', label: '数据状态', width: '80', sortable: 'custom', formatter: (row) => {
                //0待申请、1申请中、2查询中、3、已查询、-1查询失败
                switch (row.infoQueryState) {
                    case 1:
                        return "查询中";
                    case 2:
                        return "同步中"
                    case 3:
                        return "存在"
                    case -1:
                        return "不存在"
                    case null:
                        return ""
                    case 0:
                        return ""
                    default:
                        return ""
                }
            }
        },
        { istrue: true, prop: 'infoLastDateText', label: '数据时间', width: '110', sortable: 'custom' },
        //{ istrue: true, prop: 'isChooseText', label: '是否选择', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'chooseMember', label: '选择人', width: '160' }

    ];

    const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    const star = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");

    const _2DayAgoTime = formatTime(dayjs().subtract(3, 'day'), "YYYY-MM-DD");

    export default {
        name: "Users",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, buschar, alllinkproductinfo },
        data() {
            return {
                that: this,
                tempType: 0,
                buscharDialog: { visible: false, isNot7Day: true, title: "", data: [] },
                chooseFormDataVisible: false,
                chooseFormData: {
                    type:3,
                    platform: null, 
                    newPlatform: null,
                    goodsCompeteId: "",
                    goodsId: "",
                    goodsName: "",
                    goodsCompeteName: ""
                },
                Filter: {
                    gDate: [_2DayAgoTime, endTime],
                    categoryName1: "",
                    categoryName2: "",
                    categoryName3: "",
                    categoryName4: "",
                    increaseGoOnDays: "",
                    promoteTypes: []
                },
                platformlist: platformlist,
                shopList: [],
                userList: [],
                groupList: [],
                tbdatalist: [],
                tableCols: tableCols,
                total: 0,
                summaryarry: { count_sum: 10 },
                pager: { OrderBy: "", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                //
                selids: [],
                dialogVisibleSyj: false,
                fileList: [],
                plantformList: [],
                categoryNameList1: [],
                categoryNameList2: [],
                categoryNameList3: [],
                categoryNameList4: [],
                echartsLoading: false,
                goodsEchartDtlFilter: {
                    goodsId: null,
                    startTime: null,
                    endTime: null,
                    timerange: [star, endTime]
                },
                selfInfo: {

                },
                alllinkproductinfoVisible: false,
            };
        },
        async mounted() {  
            let rqs = await getCurUserDepartmentName()
            if (rqs && rqs.success) {
                let deptName = rqs.data;
                if (deptName != null && deptName != "") {
                    if (deptName.includes("淘系")) {
                        this.chooseFormData.newPlatform = 9;
                    } else if (deptName.includes("拼多多")) {
                        this.chooseFormData.newPlatform = 2;
                    } else if (deptName.includes("跨境")) {
                        this.chooseFormData.newPlatform = 3;
                    } else if (deptName.includes("天猫")) {
                        this.chooseFormData.newPlatform = 1;
                    } else if (deptName.includes("京东")) {
                        this.chooseFormData.newPlatform = 7;
                    }
                }
            }

            var reqRlt = await getAllLinkPlantformsAsync();
            this.plantformList = reqRlt.data;

            reqRlt = await getAllLinkCategoryNamesAsyncByParent();
            this.categoryNameList1 = reqRlt.data;

            const userInfoName = "hotsalegoods_selfuserinfo";
            // let selfInfo4Store = getStore(userInfoName);
            // if (selfInfo4Store) {
            //     this.selfInfo = selfInfo4Store;
            // } else {
            //     this.selfInfo = (await getUserInfo()).data;
            //     setStore(userInfoName, { ...this.selfInfo }, 60 * 60 * 2);
            // }

            this.selfInfo = (await getUserInfo()).data;
            setStore(userInfoName, { ...this.selfInfo }, 60 * 60 * 2);
 

            this.onSearch();
        },
        methods: {
            downImportTemp(dType) {
                if (dType == 1)
                    window.open("/static/excel/productalllink/热销商品拼多多Template.xlsx");
                else
                    window.open("/static/excel/productalllink/热销商品Template.xlsx");
            },
            formatInfoQueryState(row) {
                //0待申请、1申请中、2查询中、3、已查询、-1查询失败
                switch (row.infoQueryState) {
                    case 1:
                        return "查询中";
                    case 2:
                        return "同步中"
                    case 3:
                        return "存在"
                    case -1:
                        return "不存在"
                    default:
                        return ""
                }
            },
            platformNameChange(val) {
                let that = this;
                this.tableCols.forEach(x => {
                    x.istrue = true;
                    //如果选中拼多多，抖店，非相关字段 不显示
                    if (val && val === "抖店" && x.importTemp == 1) {
                        //抖店不显示拼多多的。
                        x.istrue = false;
                    } else if (val && val == "拼多多" && x.importTemp != undefined && x.importTemp == 0) {
                        //多多不显示抖店的。                       
                        x.istrue = false;
                    }
                });

                that.$refs.table.doLayout();
            },
            showShop(row) {
                if (row.shopUrl) {
                    var urlParams = getUrlParam(row.shopUrl);
                    var realUrl = urlParams["goto"];
                    if (realUrl)
                        window.open(realUrl);
                    else
                        window.open(row.shopUrl);
                } else {
                    this.$alert("当前店铺未导入链接！");
                }
            },
            showGoods(row) {
                if (row.goodsUrl) {
                    var urlParams = getUrlParam(row.goodsUrl);
                    var realUrl = urlParams["goto"];
                    if (realUrl)
                        window.open(realUrl);
                    else
                        window.open(row.goodsUrl);
                } else {
                    this.$alert("当前商品未导入链接！");
                }
            },
            doSelectSw(row, isDo) {
                var that = this;
                // if (that.selfInfo == null && that.selfInfo.groupId == undefined || that.selfInfo.groupId == null || that.selfInfo.groupId == 0) {
                //     that.$alert("未找到您的小组信息，无法进行选品！");
                //     return;
                // }

                if (isDo && row.chooseRecords && row.chooseRecords.length > 0) {
                    //选择 且有选中记录为自己同组时，提示并阻止
                    for (var i = 0; i < row.chooseRecords.length; i++) {
                        if (row.chooseRecords[i].groupId == that.selfInfo.groupId) {
                            that.$alert("您所在的小组已存在选品记录！");
                            return;
                        }
                    }
                }



                that.chooseFormDataVisible = true;
                that.chooseFormData.goodsId = row.goodsId;
                that.chooseFormData.goodsName = row.goodsName;
                that.chooseFormData.isDo = isDo;
                that.chooseFormData.goodsCompeteId = "";
                that.chooseFormData.goodsCompeteName = "";
                that.chooseFormData.platform = null;
            },
            //申请数据
            async reqDateInfo(row) {
                // if (row.infoQueryState && row.infoQueryState > 0) {
                //     this.$message({ message: '已申请的不要再次申请！', type: "error" });
                //     return;
                // }
                var reqRlt = await goodsInfoQueryReqAsync({ goodsId: row.goodsId });
                if (reqRlt.success) {
                    this.$message({ message: '已提交申请', type: "success" });
                    this.onRefresh();
                }
            },
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderField = column.prop;
                    if (orderField == "isChooseText") {
                        orderField = "b.isChoose";
                    } else if (orderField == "infoLastDateText") {
                        orderField = "b.InfoLastOkTime"
                    } else if (orderField == "infoQueryState") {
                        orderField = "b.InfoQueryState"
                    }

                    this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                }
                this.onSearch();
            },
            onImport() {
                this.dialogVisibleSyj = true
            },
            async uploadFile2(item) {
                const form = new FormData();
                form.append("upfile", item.file);
                form.append("tempType", this.tempType);
                const res = importHotSaleGoodsAsync(form);
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
            },
            async uploadSuccess2(response, file, fileList) {
                fileList.splice(fileList.indexOf(file), 1);
            },
            async onSubmitupload2() {
                this.$refs.upload2.submit()
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.gettbdatalist1();
            },
            async gettbdatalist1() {
                if (this.Filter.gDate) {
                    this.Filter.startGDate = this.Filter.gDate[0];
                    this.Filter.endGDate = this.Filter.gDate[1];
                }
                else {
                    this.Filter.startGDate = null;
                    this.Filter.endGDate = null;

                }
                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,
                };

                this.listLoading = true;
                const res = await pageHotSaleGoodsAsync(params);

                this.listLoading = false;

                this.total = res.data.total
                this.tbdatalist = res.data.list;

                //加载趋势图
                this.getEcharts();

            },
            //加载趋势图
            getEcharts() {
                setTimeout(_ => {
                    this.tbdatalist.forEach(e => {
                        if (e.echart7DayData != null && e.echart7DayData != "") {

                            //let myChart = echarts.init(this.$refs['echarts' + e.goodsId]);

                            //检测是否已经存在echarts实例，如果不存在，则不再去初始化
                            let myChart = echarts.getInstanceByDom(
                                this.$refs['echarts' + e.id]
                            );
                            if (myChart == null) {
                                myChart = echarts.init(this.$refs['echarts' + e.id]);
                            }

                            var series = [];
                            this.echartsLoading = true;
                            e.echart7DayData.series.forEach(s => {
                                if (s.name !== "好评率")
                                    series.push({ type: 'line', smooth: true, showSymbol: false, ...s })
                            });

                            var xAxis = { ...e.echart7DayData.xAxis };
                            xAxis.type = "category";
                            //xAxis.boundaryGap=false;
                            xAxis.show = false;
                            xAxis.boundaryGap = false;

                            this.echartsLoading = false
                            myChart.setOption({
                                legend: {
                                    show: false,
                                    //data: ["销量", "评论数", "好评率"]
                                },
                                grid: {
                                    left: "0",
                                    top: "1",
                                    right: "6",
                                    bottom: "1",
                                    containLabel: false,
                                },
                                xAxis: xAxis,
                                yAxis: {
                                    type: 'value',
                                    show: false,
                                },
                                series: series
                            });
                            window.addEventListener("resize", () => {
                                myChart.resize();
                            });
                        }
                    })
                }, 1000)
            },
            async cellclick(row, column, cell, event) {
                if (column.label == '七天趋势') {
                    this.buscharDialog.isNot7Day = false;
                    this.getDtlEcharts(row);
                }
            },
            async showHotGoodsEchartByRefresh() {
                if (this.goodsEchartDtlFilter.timerange) {
                    this.goodsEchartDtlFilter.startTime = this.goodsEchartDtlFilter.timerange[0];
                    this.goodsEchartDtlFilter.endTime = this.goodsEchartDtlFilter.timerange[1];
                }

                var params = { ...this.goodsEchartDtlFilter }
                var reqRlt = await getHotSaleGoodsEchartByDateAsync(params);
                if (reqRlt.data) {
                    this.getDtlEcharts(reqRlt.data);
                }
            },
            async showHotGoodsEchartByDateRange(row) {

                this.buscharDialog.isNot7Day = true;

                this.goodsEchartDtlFilter.goodsId = row.goodsId;

                await this.showHotGoodsEchartByRefresh();

            },
            async getDtlEcharts(row) {
                let that = this;
                var e = row;
                if (row == null || row == undefined || row.echart7DayData == null || e.echart7DayData == "")
                    return;


                var series = [];
                //this.echartsLoading = false
                row.echart7DayData.series.forEach(s => {
                    if (s.name !== "好评率")
                        series.push({ type: 'line', smooth: true, showSymbol: true, ...s })
                });
                var xAxis = [...row.echart7DayData.xAxis.data];
                //xAxis.type = "category";
                //xAxis.boundaryGap=false;
                //xAxis.show = true;

                var optData = {
                    legend: ["销量", "评论数"],//, "好评率"
                    grid: {
                        left: "0",
                        top: "6",
                        right: "0",
                        bottom: "0",
                        containLabel: true,
                    },
                    xAxis: xAxis,
                    yAxis: {
                        type: 'value',
                        show: true,
                    },
                    series: series
                };


                that.buscharDialog.visible = true;
                that.buscharDialog.data = optData;
                that.buscharDialog.title = "";
                that.$nextTick(() => {
                    that.$refs.buschar.initcharts();
                });
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
            saleCountIncreaseBfChanged(val) {
                //    <!-- 1:0-50   2:50-100   3:100-500   4:500-1000    5:1000-2000    6:2000以上  （筛选） -->
                if (val || val > 0) {
                    switch (val) {
                        case "1":
                            this.Filter.saleCountIncreaseBfMin = 0;
                            this.Filter.saleCountIncreaseBfMax = 50;
                            break;
                        case "2":
                            this.Filter.saleCountIncreaseBfMin = 50;
                            this.Filter.saleCountIncreaseBfMax = 100;
                            break;
                        case "3":
                            this.Filter.saleCountIncreaseBfMin = 100;
                            this.Filter.saleCountIncreaseBfMax = 500;
                            break;
                        case "4":
                            this.Filter.saleCountIncreaseBfMin = 500;
                            this.Filter.saleCountIncreaseBfMax = 1000;
                            break;
                        case "5":
                            this.Filter.saleCountIncreaseBfMin = 1000;
                            this.Filter.saleCountIncreaseBfMax = 2000;
                            break;
                        case "6":
                            this.Filter.saleCountIncreaseBfMin = 2000;
                            this.Filter.saleCountIncreaseBfMax = null;
                            break;
                        default:
                            this.Filter.saleCountIncreaseBfMin = null;
                            this.Filter.saleCountIncreaseBfMax = null;
                    }

                } else {
                    this.Filter.saleCountIncreaseBfMin = null;
                    this.Filter.saleCountIncreaseBfMax = null;
                }
            },
            async categoryChanged(val, cIdx) {
                var prixParentname = "";
                for (var i = 1; i <= cIdx; i++) {
                    if (prixParentname == "") {
                        prixParentname = this.Filter["categoryName" + i];
                    } else {
                        prixParentname += "-" + this.Filter["categoryName" + i];
                    }
                }
                for (var i = cIdx + 1; i <= 4; i++) {
                    this["categoryNameList" + i] = [];
                    this.Filter["categoryName" + i] = "";
                    if (i == cIdx + 1) {

                        var reqRlt = await getAllLinkCategoryNamesAsyncByParent({ parentName: prixParentname });
                        this["categoryNameList" + i] = reqRlt.data;
                    }
                }
            },
            categoryChanged1: function (val) {
                this.categoryChanged(val, 1);
            },
            categoryChanged2: function (val) {
                this.categoryChanged(val, 2);
            },
            categoryChanged3: function (val) {
                this.categoryChanged(val, 3);
            },
            async submitChooseForm() {
                var that = this;

                if ((!that.chooseFormData.platform) || that.chooseFormData.platform < 1) {
                    that.$message({ message: '请选择竞品平台！', type: "error" });
                    return;
                }

                if (!that.chooseFormData.goodsCompeteId) {
                    that.$message({ message: '请填写竞品ID', type: "error" });
                    return;
                }

                var reqRlt = await isDoHotSaleGoodsAsync(that.chooseFormData);

                if (reqRlt.success) {
                    that.chooseFormDataVisible = false;
                    that.$message({ message: '已添加到已选品！', type: "success" });
                    that.onRefresh();
                }
            },
            async onSeeDetail(row) {
                this.alllinkproductinfoVisible = true;
                this.$nextTick(() => {
                    this.$refs.alllinkproductinfo.pageProductId = row.goodsId;
                    this.$refs.alllinkproductinfo.loadData();
                });
            },
            seeDetailHidele(row) {
                if (row.infoLastDateText) {
                    return true;
                }
                else {
                    return false;
                }
            }
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
<style >
    .el-image__inner {
        max-width: 50px;
        max-height: 50px;
    }
    .el-dialog__body {
        padding-top: 0;
    }
    .el-table ::v-deep .el-table__fixed {
        height: 100% !important;
    }
    /* .el-table ::v-deep .el-table__body-wrapper {
                                                                                                                                                                                                                                    overflow-x: scroll !important;
                                                                                                                                                                                                                                } */
</style>