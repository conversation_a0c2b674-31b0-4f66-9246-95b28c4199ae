<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent></el-form>
            <el-button-group>
                <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.version" placeholder="类型" style="width: 130px">
                        <el-option label="工资月报" value="v1"></el-option>
                        <el-option label="参考月报" value="v2"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-date-picker style="width: 120px" v-model="filter.yearMonth" type="month" format="yyyyMM"
                        value-format="yyyyMM" placeholder="核算月份"></el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model.trim="filter.proCode" placeholder="商品ID" style="width:120px;" />
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model.trim="filter.ProductName" placeholder="商品名称" style="width:160px;" />
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-select filterable v-model="filter.platform1" @change="onchangeplatform" clearable placeholder="平台"
                        style="width:120px;">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组"
                        style="width: 90px">
                        <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                        <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <!-- <el-button type="primary" @click="onstartImport">导入淘客不计</el-button> -->
            </el-button-group>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :isSelectColumn="false" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
            @summaryClick='onsummaryClick' :tableData='financialreportlist' :tableCols='tableCols'
            :tableHandles='tableHandles' :loading="listLoading">
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

        <vxe-modal title="明细" v-model="calcdetails.visible" width="80%" v-dialogDrag>
            <calcdetails ref="calcdetails" style="height:600px;"></calcdetails>
        </vxe-modal>

        <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import { getAllList as getAllShopList, getDirectorGroupList, getList as getshopList } from '@/api/operatemanage/base/shop';
import { getFinancialReportList } from '@/api/financial/yyfy'
import { exportFinancialReport } from '@/api/bookkeeper/financialreport'
import { formatWarehouse, formatYesornoBool, formatLinkProCode, platformlist } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { Loading } from 'element-ui';
import { importTaoKeNoMeter } from '@/api/bookkeeper/import'
import dayjs from "dayjs";
import { formatTime1 } from "@/utils";
import calcdetails from "@/views/bookkeeper/reportmonth/calcdetails";
import buschar from '@/components/Bus/buschar'
import { getAnalysisCommonResponse } from '@/api/admin/common'
var curMonth = formatTime1(dayjs().startOf("month").subtract(1, "month"), "yyyyMM");
let loading;
const startLoading = () => {  // 使用Element loading-start 方法
    loading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)'
    });
};
const tableCols = [
    { istrue: true, fixed: true, prop: 'proCode', fix: true, label: '商品ID', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
    { istrue: true, fixed: true, prop: 'groupId', label: '小组', sortable: 'custom', width: '70', formatter: (row) => row.groupName },
    {
        istrue: true, prop: '', label: `账单`, merge: true, prop: 'mergeField',
        cols: [
            { istrue: true, prop: 'yearMonth', label: '年月', sortable: 'custom', width: '65' },
            //{istrue:true,prop:'createdTime',label:'统计日期',sortable:'custom', width:'150'},
            { istrue: true, prop: 'proName', label: '产品名称', width: '150' },
            //{istrue:true,prop:'grossProfit',label:'销售毛利',sortable:'custom', width:'80'},
            { istrue: true, prop: 'goodsCodes', label: '商品编码', width: '100', sortable: 'custom' },
            { istrue: true, prop: 'styleCode', label: '系列编码', width: '100', sortable: 'custom' },
            { istrue: true, prop: 'shopCode', label: '店铺编码', width: '100', sortable: 'custom' },
            { istrue: true, prop: 'shopCode', label: '店铺名称', sortable: 'custom', width: '150', formatter: (row) => row.shopName },
            //{istrue:true,prop:'orderCount',label:'订单数',sortable:'custom', width:'70'},
            { istrue: true, prop: 'count',  label: 'ID数', sortable: 'custom', width: '70' },
            { istrue: true, prop: 'amountSettlement',  label: '结算收入', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountSettlement_1',  label: '结算收入-1', sortable: 'custom', width: '95', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountCrossMonthIn',  label: '跨月收入', width: '80' },
            { istrue: true, prop: 'amountOut',  label: '退款', sortable: 'custom', width: '70', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountCrossMonthOut',  label: '跨月退款', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountTaoKeNot',  label: '淘客不计', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountShare',  label: '参与公摊金额', sortable: 'custom', width: '110' },
            { istrue: true, prop: 'amountSettlement_2',  label: '2月之前月份收入', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            // {istrue:true,prop:'dkTotalAmont',label:'账单扣点', sortable:'custom',width:'80'},
            //{istrue:true,prop:'AmountCrossMonthIn_1',label:'2月之前月份收入-1', sortable:'custom',width:'80'},
            { istrue: true, prop: 'amountCost',  label: '结算成本', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountOutCost',  label: '退款成本', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountEmptyId',  label: '空白链接成本', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountReSendCost',  label: '补发成本', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountExceptionCost',  label: '异常成本', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            //{istrue:true,prop:'amountExceptionCostAvg',label:'异常成本分摊', sortable:'custom',width:'80'},
            //{istrue:true,prop:'amountReSendCostAvg',label:'补发成本分摊',sortable:'custom', width:'80'},
            { istrue: true, prop: 'agentCost',  label: '代发成本差', sortable: 'custom', width: '80' },
            // {istrue:true,prop:'AmountSettleCost',label:'代发成本',sortable:'custom', width:'80'},
            // {istrue:true,prop:'AmountSettleCost',label:'护墙角定制差额',sortable:'custom', width:'80'},
            // {istrue:true,prop:'AmountSettleCost',label:'采购运费',sortable:'custom', width:'80'},
            //{istrue:true,prop:'grossProfit',label:'销售毛利',sortable:'custom', width:'80'},
            // {istrue:true,prop:'AmountReturnCrossMonth_2',label:'2月之前月份退款', sortable:'custom',width:'80'},
            // {istrue:true,prop:'AmountExceptionCost',label:'2月之前月份销售成本', sortable:'custom',width:'80'},
            //{istrue:true,prop:'AmountReSend',label:'2月补发成本', sortable:'custom',width:'80'}
            { istrue: true, prop: 'xiaoTuiFanHuan', label: '销退仓返还', sortable: 'custom', width: '100' },
            { istrue: true, prop: 'saleProfit',  label: '销售毛利', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'orderCount_FX',  label: '分销订单数', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'count_FX',  label: '分销ID数', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountSettlement_FX',  label: '分销结算收入', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountCost_FX',  label: '分销结算成本', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'dingZhiKuanTotalCost',  label: '定制款成本', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'dingZhiKuanAvgCost',  label: '定制款分摊成本', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'dingZhiKuanExceptionCost',  label: '定制款异常成本', sortable: 'custom', width: '80' },
        ]
    },
    { istrue: true, prop: 'dkTotalAmont',  sortable: 'custom', label: '账单费用', width: '80' },
    {
        istrue: true, prop: '',  label: `订单费用`, merge: true, prop: 'mergeField1',
        cols: [{ istrue: true, prop: 'freightFee',  label: '快递费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'freightFeeAvg',  label: '快递费均摊', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'withholdfee',  label: '快递违规扣款', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'packageFee',  label: '包装费', sortable: 'custom', width: '70' },
            { istrue: true, prop: 'totalOrderCost',  label: '订单费用合计', sortable: 'custom', width: '80' },
                
            { istrue: true, prop: 'wcOrderCount',  label: '外仓订单量', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'wcMinExpressFee',  label: '外仓最低快递费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'wcActuExpressFee',  label: '外仓快递费-实际快递费', sortable: 'custom', width: '80' },
    ]
    },
    {
        istrue: true, prop: '', label: `产品费用`, merge: true, prop: 'mergeField2',
        cols: [{ istrue: true, prop: 'amontPick',  label: '产品运费', sortable: 'custom', width: '80' },
        // {istrue:true,prop:'dingDingExamineActualAmont',label:'钉钉审批',sortable:'custom',width:'80'},
        { istrue: true, prop: 'amontSampleBX',  label: '样品费', sortable: 'custom', width: '80' },
        { istrue: true, prop: 'amontSampleGrop',  label: '运营/美工拿样', sortable: 'custom', width: '90' },
        { istrue: true, prop: 'amontShoot',  label: '美工拍摄费用', sortable: 'custom', width: '70' },
        { istrue: true, prop: 'amontFreightfee',  label: '采购运费', sortable: 'custom', width: '80' },
        { istrue: true, prop: 'totalProductCost',  label: '产品费用合计', sortable: 'custom', width: '80' },
        ]
    },
    {
        istrue: true, prop: '', label: `运营费用`, merge: true, prop: 'mergeField3',
        cols: [
            { istrue: true, prop: 'advFee',  sortable: 'custom', label: '运营费合计', width: '80' },
            { istrue: true, prop: 'advRate',  sortable: 'custom', label: '广告费占比', width: '80', formatter: (row) => { return row.advRate?.toFixed(2) + "%" } },
        ]
    },
    {
        istrue: true, prop: '', label: `工资`, merge: true, prop: 'mergeField4',
        cols: [{ istrue: true, prop: 'amontCommissionMG',  label: '美工提成', sortable: 'custom', width: '80' },
        { istrue: true, prop: 'amontCommissionXMT',  label: '新媒体提成', sortable: 'custom', width: '90' },
        { istrue: true, prop: 'amontCommissionCG',  label: '采购提成', sortable: 'custom', width: '90' },
        { istrue: true, prop: 'amontMachineGZ',  label: '加工工资', sortable: 'custom', width: '80' },
        { istrue: true, prop: 'amontWagesGroup',  label: '运营工资', sortable: 'custom', width: '80' },
        { istrue: true, prop: 'totalWagesCost',  label: '工资合计', sortable: 'custom', width: '80' }]
    },
    { istrue: true, prop: 'amontOffLinefee',  label: '运营下架', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'amontStoreLossfee',  label: '仓储损耗', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'grossProfit',  label: '产品利润', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'chuCangYg', label: '预估出仓成本', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'chuCangZs', label: '真实出仓成本', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'chuCang', label: '出仓成本', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'profit4', label: '毛四', sortable: 'custom', width: '120' },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, calcdetails, buschar },
    data() {
        return {
            fileparm: {},
            uploadLoading: false,
            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            importDialog: {
                filter: {
                    settMonth: curMonth,
                    version: ''
                }
            },
            ImportdialogVisible: false,
            that: this,
            filter: {
                proCode: null,
                platform1: null,
                selectplat: 1,
                yearMonth: null,
                shopCode: null,
                groupId: null,
                productName: null,
                version: "v1"
            },
            platformlist: platformlist,
            shopList: [],
            userList: [],
            grouplist: [],
            financialreportlist: [],
            tableCols: tableCols,
            tableHandles: [],
            total: 0,
            // summaryarry:{count_sum:10},
            pager: { OrderBy: " AmountSettlement ", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            dialogVisibleSyj: false,
            calcdetails: { visible: false },
            fileList: [],
            analysisFilter: {
                searchName: "View_FinancialMonthReport_All",
                extype: 5,
                selectColumn: "Count",
                filterTime: "YearMonth",
                isYearMonthDay: false,
                filter: null,
                columnList: [{ columnNameCN: '订单数', columnNameEN: 'Count' }]
            },
            buscharDialog: { visible: false, title: "", data: [] },
        };
    },
    async mounted() {

        //await this.onSearch()
        await this.getShopList();
    },
    methods: {
        async onchangeplatform(val) {
            const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
        },
        async getShopList() {
            const res1 = await getAllShopList({ platforms: [1, 9] });
            this.shopList = [];
            res1.data?.forEach(f => {
                if (f.isCalcSettlement && f.shopCode)
                    this.shopList.push(f);
            });

            var res2 = await getDirectorGroupList();
            this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        onRefresh() {
            this.onSearch()
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList().then(res => { });
            // loading.close();
        },
        async getList() {
            var that = this;
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter, };
            // this.listLoading = true;
            startLoading();
            const res = await getFinancialReportList(params).then(res => {
                loading.close();
                that.total = res.data?.total
                that.financialreportlist = res.data?.list;
                that.summaryarry = res.data?.summary;
            });
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async showcalcdetails(row, column) {
            this.calcdetails.visible = true;
            console.log('this.$refs', this.$refs)
            console.log('this.$refs.calcdetails;', this.$refs.calcdetails)
            var calc = this.$refs.calcdetails;
            console.log('calcdetails1', calc)
            var that = this;
            await this.$nextTick(async () => {
                console.log('calcdetails2', calc)
                await that.$refs.calcdetails.onSearch(column, this.filter.version, row.shopCode, row.yearMonth, row.proCode)
            });
        },
        /* async onExport(){
           if (!this.filter.yearMonth) {
             this.$message({message:"请先选择月份！",type:"warning"});
             return;
           }
           var pager = this.$refs.pager.getPager();
           const params = {  ...pager,   ...this.pager,   ...this.filter};
           var res= await exportFinancialReport(params);
           if(!res?.data) {
              this.$message({message:"没有数据",type:"warning"});
              return
           }
           const aLink = document.createElement("a");
           let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
           aLink.href = URL.createObjectURL(blob)
           aLink.setAttribute('download','财务账单数据' +  new Date().toLocaleString() + '_.xlsx' )
           aLink.click()
         }, */
        async onsummaryClick(property) {
            let that = this;
            this.analysisFilter.filter = {
                proCode: [that.filter.proCode, 0],
                //platform: [that.filter.platform, 0],
                yearMonth: [that.filter.yearMonth, 0],
                shopCode: [that.filter.shopCode, 0],
                groupId: [that.filter.groupId, 0],
                productName: [that.filter.productName, 5],
                version: [that.filter.version, 0],
            };
            this.analysisFilter.selectColumn = property;
            var cn = "金额";
            if (property == "orderCount" || property == "count") {
                cn = "数量";
            }
            this.analysisFilter.columnList = [{ columnNameCN: cn, columnNameEN: property }];

            const res = await getAnalysisCommonResponse(this.analysisFilter).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data
                that.buscharDialog.title = res.data.legend[0]
            });
        }
    },
};
</script>
<style lang="scss" scoped>.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}</style>
