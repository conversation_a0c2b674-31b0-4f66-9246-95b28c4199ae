<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-select v-model="ListInfo.userNo" filterable default-first-option reserve-keyword remote
          :remote-method="detailsMethod" placeholder="姓名" class="publicCss" clearable>
          <el-option v-for="item in detailsPersonList" :key="item.userId" :label="item.name + '-' + item.posts"
            :value="item.userId">
          </el-option>
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
      </div>
    </template>
    <vxetablebase :id="'productReportTx202302031421'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" :pageSize="20" :sizes="[10, 20, 30, 40]"/>
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import { employeeList } from '@/api/bladegateway/xzgateway.js';
import { kbsUserStatisticPage } from "@/api/order/orderData";
import dayjs from 'dayjs'
const tableCols = [
  {   width: 'auto', align: 'center', prop: 'userName', label: '姓名', },
  {   width: 'auto', align: 'center', prop: 'questionCount', label: '提问数量', },
  {   width: 'auto', align: 'center', prop: 'replyCount', label: '回复数量', },
  {   width: 'auto', align: 'center', prop: 'unReplyCount', label: '待回复数量', },

  {   width: 'auto', align: 'center', prop: 'resolveCount', label: '已解决数量', },
  {   width: 'auto', align: 'center', prop: 'unResolveCount', label: '未解决数量', },

  {   width: 'auto', align: 'center', prop: 'calledCount', label: '被@数量', },
  {   width: 'auto', align: 'center', prop: 'answerCalledCount', label: '@回复数量', },

]
export default {
  name: "viewDetails",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      detailsPersonList: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async detailsMethod(query) {
      if (query !== '') {
        const { data } = await employeeList({ name: query })
        this.detailsPersonList = data.map(item => {
          return { name: item.name, posts: item.posts, userId: item.userId }
        })
      } else {
        this.detailsPersonList = [];
      }
    },
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近1天时间
        this.ListInfo.startDate = dayjs().format('YYYY-MM-DD')
        this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      }
      this.loading = true
      const { data, success } = await kbsUserStatisticPage(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
