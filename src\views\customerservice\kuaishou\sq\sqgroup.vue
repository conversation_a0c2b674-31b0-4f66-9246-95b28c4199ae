<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='grouplist' @select='selectchange' :isSelection='true' :tableCols='tableCols'
            :loading="listLoading">

            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>


            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.GroupNameList" placeholder="分组" clearable filterable multiple
                            :collapse-tags="true">
                            <el-option v-for="item in filterGroupList" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.shopCode" filterable placeholder="店铺" style="width:160px;" clearable>
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                :value="item.shopCode">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.sname" placeholder="姓名" style="width:160px;" clearable
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.snick" placeholder="客服昵称" style="width:160px;" clearable
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-switch :width="40" @change="changeingroup" v-model="filter.isleavegroup"
                            inactive-color="#228B22" active-text="包含离组" inactive-text="当前在组">
                        </el-switch>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onAddGroupShow"
                        v-if="checkPermission(['api:Customerservice:KuaiShouInquirs:AddKuaiShouGroupAsync0'])">添加</el-button>
                    <el-button type="primary" @click="onImportSyj"
                        v-if="checkPermission(['api:Customerservice:KuaiShouInquirs:ImportKuaiShouGroupAsync0'])">导入</el-button>
                    <el-button type="primary" @click="onImportSyjModel">下载模板</el-button>
                    <el-button type="primary" @click="batchLeaveGroup"
                        v-if="checkPermission(['api:Customerservice:KuaiShouInquirs:BatchUpdateLeaveDateAsync0'])">批量离组</el-button>
                    <el-button type="primary" @click="showEditLog">分组编辑日志</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getgroupList" />
        </template>

        <el-dialog title="添加客服人员分组管理信息" :visible.sync="addgroupdialogVisibleSyj" width="40%" label-width="120px"
            :close-on-click-modal="false" v-dialogDrag>
            <span>
                <el-form :model="addForm" :rules="addFormRules">
                    <el-form-item prop="groupName" label="分组">
                        <el-input style="width:83%" v-model="addForm.groupName" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="groupManager" label="组长">
                        <el-input style="width:83%" v-model="addForm.groupManager" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="sname" label="姓名">
                        <el-input style="width:83%" v-model="addForm.sname" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="snick" label="客服昵称">
                        <el-input style="width:83%" v-model="addForm.snick" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="shopName" label="店铺">
                        <el-input style="width:83%" v-model="addForm.shopName" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="phoneNo" label="绑定手机号">
                        <el-input style="width:73%" v-model="addForm.phoneNo" :maxlength="20"></el-input>
                    </el-form-item>
                    <el-form-item prop="joinDate" label="入组日期">
                        <el-date-picker v-model="addForm.joinDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            type="date" style="width:63%" placeholder="选择日期">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item prop="leaveDate" label="离组日期">
                        <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="addForm.leaveDate"
                            style="width:63%" type="date" placeholder="选择日期">
                        </el-date-picker>
                    </el-form-item>
                </el-form>
            </span>

            <span slot="footer" class="dialog-footer">
                <el-button @click="addgroupdialogVisibleSyj = false">关闭</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="onAddGroup">确定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="修改客服人员分组管理信息" :visible.sync="updategroupdialogVisibleSyj" width="40%"
            :close-on-click-modal="false" v-dialogDrag>
            <span>
                <el-form :model="addForm" :rules="addFormRules">
                    <el-form-item prop="groupName" label="分组">
                        <el-input style="width:83%" v-model="addForm.groupName" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="groupManager" label="组长">
                        <el-input style="width:83%" v-model="addForm.groupManager" :maxlength="255"></el-input>
                    </el-form-item>
                    <el-form-item prop="sname" label="姓名">
                        <el-input style="width:83%" v-model="addForm.sname" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="snick" label="客服昵称">
                        <el-input style="width:83%" v-model="addForm.snick" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="shopName" label="店铺">
                        <el-input style="width:83%" v-model="addForm.shopName" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="phoneNo" label="绑定手机号">
                        <el-input style="width:73%" v-model="addForm.phoneNo" :maxlength="20"></el-input>
                    </el-form-item>
                    <el-form-item prop="joinDate" label="入组日期">
                        <el-date-picker v-model="addForm.joinDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            type="date" style="width:63%" placeholder="选择日期">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item prop="leaveDate" label="离组日期">
                        <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="addForm.leaveDate"
                            style="width:63%" type="date" placeholder="选择日期">
                        </el-date-picker>
                    </el-form-item>
                </el-form>
            </span>

            <span slot="footer" class="dialog-footer">
                <el-button @click="updategroupdialogVisibleSyj = false">关闭</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="updategroup()">确定</el-button>
            </span>
        </el-dialog>




        <el-dialog title="导入数据" :visible.sync="dialogVisibleData" width="600px" v-dialogDrag>
            <span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action
                            accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange"
                            :on-remove="uploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleData = false">关闭</el-button>
            </span>
        </el-dialog>


        <el-dialog title="离组日期选择" :visible.sync="dialogVisibleLeave" width="30%" :close-on-click-modal="false"
            v-dialogDrag>
            <span>
                <template class="block">
                    <!-- <el-date-picker v-model="dialogLeaveDate" align="right" type="date" placeholder="选择日期" ></el-date-picker> -->
                    <el-date-picker v-model="dialogLeaveDate" style="width:63%" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                    </el-date-picker>
                </template>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleLeave = false">关闭</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="updategroupLeave()">提交</el-button>
            </span>
        </el-dialog>



    </my-container>
</template>
<script>
import {
    getKuaiShouGroup, getKuaiShouGroupPageList, addKuaiShouGroupAsync, deleteKuaiShouGroupAsync, updateKuaiShouGroupAsync, importKuaiShouGroupAsync, batchUpdateLeaveDateAsync
} from '@/api/customerservice/kuaishouinquirs'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
const tableCols = [
    { istrue: true, prop: 'groupName', label: '分组', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'groupManager', label: '组长', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'shopName', label: '店铺', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'snick', label: '客服昵称', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'phoneNo', label: '绑定手机号', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'joinDate', label: '入组日期', width: '150', sortable: 'custom', formatter: (row) => formatTime(row.joinDate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'leaveDate', label: '离组日期', width: '150', sortable: 'custom', formatter: (row) => formatTime(row.leaveDate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'createdTime', label: '导入时间', width: '150', sortable: 'custom' },
    {
        istrue: true, type: "button", label: '操作', width: "120", btnList: [
            { label: "编辑", handle: (that, row) => that.handleupdategroup(row), permission: "api:Customerservice:KuaiShouInquirs:UpdateKuaiShouGroupAsync0" },
            { label: "删除", handle: (that, row) => that.deletegroup(row), permission: "api:Customerservice:KuaiShouInquirs:DeleteKuaiShouGroupAsync0" }
        ]
    }
];
export default {
    name: "sqgroup",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
    data() {
        return {
            that: this,
            filter: {
                isleavegroup: false,
            },
            addForm: {},
            addFormRules: {
                groupName: [{ required: true, message: '请输入', trigger: 'blur' }],
                groupManager: [{ required: true, message: '请输入', trigger: 'blur' }],
                sname: [{ required: true, message: '请输入', trigger: 'blur' }],
                snick: [{ required: true, message: '请输入', trigger: 'blur' }],
                shopName: [{ required: true, message: '请输入', trigger: 'blur' }],
                leaveDate: [{ required: true, message: '请输入', trigger: 'blur' }],
                joinDate: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
            shopList: [],
            filterGroupList: [],
            grouplist: [],
            tableCols: tableCols,
            total: 0,
            addgroupdialogVisibleSyj: false,
            summaryarry: {},
            pager: { OrderBy: "createdtime", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            updategroupdialogVisibleSyj: false,
            selids: [],
            fileList: [],
            improtGroupForm: {
                improtGroupShopCode: null,
                improtGroupShopName: null,
            },
            dialogVisibleData: false, //导入
            uploadLoading: false,
            dialogVisibleLeave: false,
            dialogLeaveDate: null,

        };
    },
    async mounted() {
        await this.getKuaiShouGroup();
        await this.getAllShopList();
    },
    methods: {
        async getKuaiShouGroup() {
            let groups = await getKuaiShouGroup({ groupType: 0, isleavegroup: this.filter.isleavegroup });
            if (groups?.success && groups?.data && groups?.data.length > 0) {
                this.filterGroupList = groups.data;
            }
        },
        async getAllShopList() {
            let shops = await getAllShopList();
            this.shopList = [];
            shops.data?.forEach(f => {
                if (f.shopCode && f.platform == 14)
                    this.shopList.push(f);
            });
        },
        async getgroupList() {
            if (this.filter.UseDate) {
                this.filter.startAccountDate = this.filter.UseDate[0];
                this.filter.endAccountDate = this.filter.UseDate[1];
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };
            params.groupType = 0;
            this.listLoading = true;
            const res = await getKuaiShouGroupPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.grouplist = res.data.list;
            //this.summaryarry=res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        onSearch() {
            this.getKuaiShouGroup();
            this.$refs.pager.setPage(1);
            this.getgroupList();
        },
        changeingroup() {
            this.$emit("callBackInfo", this.filter.isleavegroup)
            this.onSearch();
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async onAddGroupShow() {
            this.addForm = {};
            this.addgroupdialogVisibleSyj = true;
        },
        async onAddGroup() {
            var that = this;
            if (this.addForm.groupName == "" || this.addForm.groupName == null ||
                this.addForm.sname == "" || this.addForm.sname == null ||
                this.addForm.groupManager == "" || this.addForm.groupManager == null ||
                this.addForm.snick == "" || this.addForm.snick == null ||
                this.addForm.shopName == "" || this.addForm.shopName == null ||
                this.addForm.leaveDate == "" || this.addForm.leaveDate == null ||
                this.addForm.joinDate == "" || this.addForm.joinDate == null) {
                that.$message({ message: '请输入必填字段', type: "error" });
                return;
            }
            that.addForm.groupType = 0;
            let add = await addKuaiShouGroupAsync(that.addForm);
            if (add?.success) {
                that.$message({ message: '已添加', type: "success" });
                that.onSearch();
                that.addForm = {};
                that.addgroupdialogVisibleSyj = false;
            }
            else {
                //that.$message({ message: '发生异常，请刷新后重试', type: "error" });
            }
        },
        async handleupdategroup(row) {
            this.addForm = row;
            this.updategroupdialogVisibleSyj = true;
        },
        async updategroup(row) {
            if (this.addForm.groupName == "" || this.addForm.groupName == null ||
                this.addForm.sname == "" || this.addForm.sname == null ||
                this.addForm.groupManager == "" || this.addForm.groupManager == null ||
                this.addForm.snick == "" || this.addForm.snick == null ||
                this.addForm.shopName == "" || this.addForm.shopName == null ||
                this.addForm.leaveDate == "" || this.addForm.leaveDate == null ||
                this.addForm.joinDate == "" || this.addForm.joinDate == null) {
                this.$message({ message: '请输入必填字段', type: "error" });
                return;
            }
            var that = this;
            that.addForm.groupType = 0;
            let del = await updateKuaiShouGroupAsync(that.addForm);
            if (del?.success) {
                that.$message({ message: '已修改', type: "success" });
                that.onSearch();
                this.updategroupdialogVisibleSyj = false;
            }
            else {
                //that.$message({ message: '发生异常，请刷新后重试', type: "error" });
            }
        },
        async deletegroup(row) {
            var that = this;
            this.$confirm("此操作将删除此客服人员分组管理数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let del = await deleteKuaiShouGroupAsync({ id: row.id });
                if (del?.success) {
                    that.$message({ message: '已删除', type: "success" });
                    that.onSearch();
                }
                else {
                    that.$message({ message: '发生异常，请刷新后重试', type: "error" });
                }
            });
        },
        onImportSyjModel() {
            window.open("/static/excel/customerservice/快手客服导入模版.xlsx", "_blank");
        },

        async onImportSyj() {
            this.dialogVisibleData = true;
            let shops = await getAllShopList();
            this.shopList = [];
            shops.data?.forEach(f => {
                if (f.shopCode && f.platform == 14)
                    this.shopList.push(f);
            });
        },

        async uploadFile() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false
            };
            const form = new FormData();
            form.append("upfile", this.fileList[0].raw);
            form.append("inquirsType", 0);
            form.append("groupType", 0);
            var res = await importKuaiShouGroupAsync(form);
            if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
            else this.$message({ message: res.msg, type: "warning" });
            this.$refs.upload.clearFiles()
            this.uploadLoading = false;
            this.dialogVisibleData = false;
            this.fileList = [];
        },
        async uploadChange(file, fileList) {
            if (fileList.length == 2) {
                fileList.splice(1, 1);
                this.$message({ message: "只允许单文件导入", type: "warning" });
                return false;
            }
            this.fileList.push(file);
        },
        uploadRemove(file, fileList) {
            this.fileList.splice(0, 1);
        },
        submitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return;
            }
            this.uploadLoading = true
            this.$refs.upload.submit();
        },
        //批量离组
        async batchLeaveGroup() {
            if (this.selids.length == 0) {
                this.$message({ message: "至少选择一行", type: "warning", });
                this.editparmVisible = false
                return
            }
            this.dialogVisibleLeave = true;
        },
        async updategroupLeave() {

            if (this.dialogLeaveDate == null || this.dialogLeaveDate == '') {

                this.$message({ message: "请选择日期", type: "warning" });
                return false;

            }

            const params = {
                leaveDate: this.dialogLeaveDate,
                idList: this.selids
            };
            var res = await batchUpdateLeaveDateAsync(params);
            if (res.success == true) {
                this.$message({ message: "修改成功", type: 'success', });
                this.getgroupList();
                this.dialogVisibleLeave = false;
                this.ids = null;
                this.dialogLeaveDate = null;
            }
        },
        showEditLog() {
            this.$showDialogform({
                path: `@/views/customerservice/kuaishou/kuaishougrouplog.vue`,
                title: '分组编辑日志',
                args: {
                },
                height: '600px',
                width: '75%',
                callOk: this.afterSave
            });
        },
        afterSave() {

        },

    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
