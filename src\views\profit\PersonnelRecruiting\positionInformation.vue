<template>
    <div>
        <!-- 职位信息 -->
        <div class="des-box">
            <el-descriptions title="职位信息" :column="3" size="medium" :colon="false">
                <template slot="extra">
                    <!-- <el-button type="info" circle plain icon="el-icon-plus" size="mini" @click="toggleContent"></el-button> -->
                    <i @click="toggleContent()" class="el-icon-d-arrow-right"
                        :class="{ arrowTransform: !isOpen, arrowTransformReturn: isOpen }"></i>
                </template>
                <el-descriptions-item label="" span="3" v-if="isEdit">
                    <el-collapse v-model="activeName">
                        <el-collapse-item name="content">
                            <el-form :model="ruleForm" ref="positionForm" label-width="100px" class="ruleForm"
                                :border="false">
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="员工编号" prop="employeeNumber">
                                            <el-input v-model="ruleForm.employeeNumber" placeholder="请输入员编号"
                                                maxlength="30"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="部门" prop="empBigDepartment">
                                            <el-select ref="empBigDepartment" v-model="ruleForm.empBigDepartment"
                                                style="width: 100%" size="mini" placeholder="部门">
                                                <el-option hidden value="一级菜单"
                                                    :label="ruleForm.empBigDepartment"></el-option>
                                                <el-tree style="width: 200px;" :data="deptList" :props="defaultProps"
                                                    :expand-on-click-node="false" :check-on-click-node="true"
                                                    @node-click="handleNodeClick">
                                                </el-tree>
                                            </el-select>
                                            <!-- <el-input v-model="ruleForm.empBigDepartment" placeholder="请输入部门">
                                            </el-input> -->
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="二级部门" prop="empSubDDDeptId">
                                            <el-select ref="postgroupdept" v-model="groupName" style="width: 100%"
                                                size="mini" placeholder="二级部门">
                                                <el-option hidden value="一级菜单" :label="groupName"></el-option>
                                                <el-tree style="width: 200px;" :data="groupList" :props="defaultProps"
                                                    :expand-on-click-node="false" :check-on-click-node="true"
                                                    @node-click="handleGropuNodeClick">
                                                </el-tree>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="职称" prop="empTitle">
                                            <!-- <el-select v-model="ruleForm.empTitle" placeholder="请选择职称" style="width: 100%;">
                                                <el-option label="职称" value="职称"></el-option>
                                                <el-option label="职称" value="职称"></el-option>
                                            </el-select> -->
                                            <el-input v-model="ruleForm.empTitle" placeholder="请输入职称"
                                                maxlength="10"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="职级" prop="rank">
                                            <el-select v-model="ruleForm.rank" placeholder="请选择职级" style="width: 100%;">
                                                <el-option label="助理" value="助理"></el-option>
                                                <el-option label="专员" value="专员"></el-option>
                                                <el-option label="主管" value="主管"></el-option>
                                                <el-option label="经理" value="经理"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="职位" prop="position">
                                            <!-- <el-select v-model="ruleForm.position" placeholder="请选择职位" style="width: 100%;">
                                                <el-option label="助理" value="助理"></el-option>
                                                <el-option label="专员" value="专员"></el-option>
                                                <el-option label="主管" value="主管"></el-option>
                                                <el-option label="经理" value="经理"></el-option>
                                            </el-select> -->
                                            <el-input v-model="ruleForm.position" placeholder="请输入职位"
                                                maxlength="10" :style="isShowPositionChgLog?'width:75%':'width:100%'"></el-input>
                                                <el-button v-if="isShowPositionChgLog" @click="getPositionChgLog" style="width:20%" type="text">历史记录</el-button>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="员工结构" prop="employeeStructure">
                                            <el-select v-model="ruleForm.employeeStructure" placeholder="请选择员工结构"
                                                style="width: 100%;">
                                                <el-option label="试用" value="试用"></el-option>
                                                <el-option label="正式" value="正式"></el-option>
                                                <!-- <el-option label="离职" value="离职"></el-option> -->
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="入职时间" prop="employmentDate">
                                            <el-date-picker v-model="ruleForm.employmentDate" type="date"
                                                value-format="yyyy-MM-dd" clearable style="width: 100%;"
                                                @change="changeDate">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="工龄日期" prop="seniorityDate">
                                            <el-date-picker v-model="ruleForm.seniorityDate" type="date"
                                                value-format="yyyy-MM-dd" style="width: 100%;" @change="changeDate">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="转正日期" prop="conversionDate">
                                            <el-date-picker v-model="ruleForm.conversionDate" type="date"
                                                value-format="yyyy-MM-dd" :picker-options="pickerOptions"
                                                style="width: 100%;">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="工龄" prop="seniority">
                                            <el-input disabled v-model="ruleForm.seniority" placeholder="请输入工龄">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="是否办理社保" prop="isSocialSecurityHandling">
                                            <el-select v-model="ruleForm.isSocialSecurityHandling" placeholder="请选择是否办理社保"
                                                style="width: 100%;">
                                                <el-option label="是" :value="true"></el-option>
                                                <el-option label="否" :value="false"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="工位号" prop="workstationNumber">
                                            <el-input v-model="ruleForm.workstationNumber" maxlength="10"
                                                placeholder="请输入工位号"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="离职日期" prop="dimissionDate">
                                            <el-date-picker v-model="ruleForm.dimissionDate" type="date"
                                                value-format="yyyy-MM-dd" :picker-options="pickerOptions"
                                                style="width: 100%;">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="离职类型原因" prop="dimissionReason">   
                                            {{ ruleForm.dimissionType }}  -
                                            {{ ruleForm.dimissionReason }}                                   
                                          
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="备注" prop="empRemark">
                                            <el-input  type="textarea"  :rows="2" v-model="ruleForm.empRemark" maxlength="200"  show-word-limit
                                                placeholder="请输入员工的备注信息"></el-input>
                                        </el-form-item>
                                    </el-col>
                                   
                                </el-row>
                            </el-form>
                        </el-collapse-item>
                        <!-- 历史岗位记录 -->
                        <!-- <el-collapse-item title="历史岗位记录" name="content111" v-show="isOpen&&activePositionChgLog">
                            <el-table :data="positionChgLogList" style="width: 100%" size="mini">
                                <el-table-column prop="orgPosition" label="原职位" width="180">
                                </el-table-column>
                                <el-table-column prop="orgRank" label="原职级" width="180">
                                </el-table-column>
                                <el-table-column prop="orgEmpDeptFull" label="原部门全称">
                                </el-table-column>
                            </el-table>
                        </el-collapse-item> -->
                    </el-collapse>

                </el-descriptions-item>
                <el-descriptions-item label="" span="3" v-else>
                    <el-descriptions title="" :column="3" size="medium" :colon="false">
                        <el-descriptions-item label="员工编号：">{{ candidateInfo.employeeNumber }}</el-descriptions-item>
                        <el-descriptions-item label="职称：">{{ candidateInfo.empTitle }}</el-descriptions-item>
                        <el-descriptions-item label="职级：">{{ candidateInfo.rank }}</el-descriptions-item>
                        <el-descriptions-item label="部门：">{{ candidateInfo.empBigDepartment }}</el-descriptions-item>
                        <el-descriptions-item label="二级部门：">{{ candidateInfo.empSubDepartment }}</el-descriptions-item>
                        <el-descriptions-item label="员工结构：">{{ candidateInfo.employeeStructure }}</el-descriptions-item>
                        <el-descriptions-item label="" span="3">
                            <el-collapse v-model="activeName">
                                <el-collapse-item name="content">
                                    <el-descriptions title="" :column="3" size="medium" :colon="false">
                                        <el-descriptions-item label="入职日期：">{{ candidateInfo.employmentDate
                                        }}</el-descriptions-item>
                                        <el-descriptions-item label="计算工龄日期：">{{ candidateInfo.seniorityDate
                                        }}</el-descriptions-item>
                                        <el-descriptions-item label="转正日期：">{{ candidateInfo.conversionDate
                                        }}</el-descriptions-item>
                                        <el-descriptions-item label="工龄：">{{ candidateInfo.seniority
                                        }}</el-descriptions-item>
                                        <el-descriptions-item label="工位号：">{{ candidateInfo.workstationNumber
                                        }}</el-descriptions-item>
                                        <el-descriptions-item label="社保办理：">{{ candidateInfo.isSocialSecurityHandling ?
                                            '已办理'
                                            : !candidateInfo.isSocialSecurityHandling ? '未办理' : ''
                                        }}</el-descriptions-item>
                                    </el-descriptions>
                                </el-collapse-item>
                            </el-collapse>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-descriptions-item>
            </el-descriptions>
        </div>
        <el-dialog width="45%" v-dialogDrag title="岗位历史记录"  :visible.sync="activePositionChgLog" append-to-body>
            <el-table :data="positionChgLogList" style="width: 100%" size="mini">
                <el-table-column prop="orgPosition" label="原职位" :show-overflow-tooltip="true">
                </el-table-column>
                <el-table-column prop="newPosition" label="新职位" :show-overflow-tooltip="true">
                </el-table-column>
                <el-table-column prop="orgRank" label="原职级" :show-overflow-tooltip="true">
                </el-table-column>
                <el-table-column prop="newRank" label="新职级" :show-overflow-tooltip="true">
                </el-table-column>
                <el-table-column prop="orgEmpDeptFull" label="原部门全称"  width="160" :show-overflow-tooltip="true">
                </el-table-column>
                <el-table-column prop="newEmpDeptFull" label="新部门全称"  width="160" :show-overflow-tooltip="true">
                </el-table-column>
                <el-table-column prop="editorName" label="修改人" :show-overflow-tooltip="true">
                </el-table-column>
                <el-table-column prop="editTime" label="修改时间" :show-overflow-tooltip="true">
                </el-table-column>
            </el-table>
            <div slot="footer" class="dialog-footer">
                <el-button @click="activePositionChgLog = false">取消</el-button>
            </div>
        </el-dialog>

        <el-divider></el-divider>
    </div>
</template>
  
<script>
import { getALLDDDeptTree } from '@/api/profit/personnel'
import { getEmpDeptPositionChgLog } from '@/api/profit/hr'
export default {
    name: "positionInformation",//职位信息
    props: {
        isEdit: {
            type: Boolean,
            default: () => { return false; }
        },
        candidateInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
    },
    data () {
        return {
            isShowPositionChgLog:false,
            activePositionChgLog: false,
            chooseName: '',
            groupName: '',
            defaultProps: {
                children: 'childDeptList',
                label: 'name'
            },
            deptList: [],
            groupList: [],
            activeName: '',
            isOpen: false,
            // isEdit: false,
            ruleForm: {
                employeeNumber: null,
                empBigDepartment: null,
                empBigDDDeptId: null,
                empSubDDDeptId: null,
                empSubDepartment: null,
                empTitle: null,
                rank: null,
                position: null,
                employeeStructure: null,
                employmentDate: null,
                seniorityDate: null,
                conversionDate: null,
                isSocialSecurityHandling: null,
                seniority: null,
                workstationNumber: null,
                dimissionDate:null,
                dimissionType:null,
                dimissionReason:null,
                empRemark:null,
            },
            pickerOptions: {
                disabledDate: (time) => {
                    return time.getTime() < (new Date(this.ruleForm.employmentDate)).getTime()
                },
            },
            positionChgLogList: [],
        }
    },
    mounted () {
        this.getDeptList();
        for (const prop in this.candidateInfo) {
            if (prop in this.ruleForm) {
                this.ruleForm[prop] = this.candidateInfo[prop];
            }
        }
        if (this.ruleForm.empSubDepartment) {
            this.groupName = this.ruleForm.empSubDepartment
        }
        if (this.candidateInfo.employmentDate) {
            this.changeDate();
        }
        
    },
    methods: {
        // 获取员工历史岗位记录
        getPositionChgLog () {
            if (this.candidateInfo.candidateId && this.isEdit) {
                    let params = {
                        candidateId: this.candidateInfo.candidateId
                    }
                    getEmpDeptPositionChgLog(params).then(res => {
                        if (res.success) {
                            this.positionChgLogList = res.data;
                            this.activePositionChgLog = true
                        }
                    })
                }
        },
        changeDate () {
            // if (this.ruleForm.employmentDate) {
            //     if (this.ruleForm.seniorityDate) {
            //         this.ruleForm.seniority = this.diffDate(new Date(this.ruleForm.employmentDate), new Date(this.ruleForm.seniorityDate));
            //     } else {
            //         this.ruleForm.seniority = this.diffDate(new Date(this.ruleForm.employmentDate), new Date());
            //     }
            // }
            // if (this.candidateInfo.employmentDate) {
            //     if (this.candidateInfo.seniorityDate) {
            //         this.candidateInfo.seniority = this.diffDate(new Date(this.candidateInfo.employmentDate), new Date(this.candidateInfo.seniorityDate));
            //     } else {
            //         this.candidateInfo.seniority = this.diffDate(new Date(this.candidateInfo.employmentDate), new Date());
            //     }
            // }


            if (this.ruleForm.seniorityDate) {
                if (this.ruleForm.dimissionDate) {
                    this.ruleForm.seniority = this.diffDate(new Date(this.ruleForm.seniorityDate), new Date(this.ruleForm.dimissionDate));
                } else {
                    this.ruleForm.seniority = this.diffDate(new Date(this.ruleForm.seniorityDate), new Date());
                }
            }
            if (this.candidateInfo.seniorityDate) {
                if (this.candidateInfo.dimissionDate) {
                    this.candidateInfo.seniority = this.diffDate(new Date(this.candidateInfo.seniorityDate), new Date(this.candidateInfo.dimissionDate));
                } else {
                    this.candidateInfo.seniority = this.diffDate(new Date(this.candidateInfo.seniorityDate), new Date());
                }
            }
        },
        // 计算两日期差
        diffDate (date1, date2) {
            // 获取年月日
            var year1 = date1.getFullYear();
            var month1 = date1.getMonth() + 1;
            var day1 = date1.getDate();
            if (day1 > 15) {
                month1++
            }
            day1 = 1;
            var year2 = date2.getFullYear();
            var month2 = date2.getMonth() + 1;
            var day2 = date2.getDate();
            if (day2 > 15) {
                month2++
            }
            day2 = 1;
            // 计算相差年数
            var diffYear = year2 - year1;
            if (month2 < month1) {
                diffYear--;
            }
            //  ||month2 == month1
            // 计算相差月数
            var diffMonth = (month2 - month1 + 12) % 12;
            // if (day2 < day1) {
            //     diffMonth--;
            // }
            if (diffYear < 0) {
                diffMonth = diffYear * 12 + diffMonth;
                diffYear = parseInt(diffMonth / 12);
                diffMonth = diffMonth % 12;
            }
            return diffYear + '年' + diffMonth + '月';
        },
        //重置
        reset () {
            this.ruleForm = {
                employeeNumber: null,
                empBigDepartment: null,
                empBigDDDeptId: null,
                empSubDDDeptId: null,
                empSubDepartment: null,
                empTitle: null,
                rank: null,
                position: null,
                employeeStructure: null,
                employmentDate: null,
                seniorityDate: null,
                conversionDate: null,
                isSocialSecurityHandling: null,
                seniority: null,
                dimissionDate:null,
                dimissionType:null,
                dimissionReason:null,
                empRemark:null,
            }
        },
        // 获取部门列表
        async getDeptList () {
            await getALLDDDeptTree().then(res => {
                if (res.success) {
                    this.deptList = res.data.childDeptList;;
                    this.groupList = res.data.childDeptList;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        // 节点点击事件
        handleNodeClick (data) {
            // 配置树形组件点击节点后，设置选择器的值，配置组件的数据
            // this.chooseName = data.name
            this.ruleForm.empBigDepartment = data.name
            this.ruleForm.empBigDDDeptId = data.dept_id
            if (data.childDeptList)
                this.groupList = data.childDeptList
            this.groupName = '';
            this.ruleForm.empSubDepartment = '';
            this.ruleForm.empSubDDDeptId = 0;
            // this.getDeptUsersList(this.ruleForm.ddDeptId);
            // 选择器执行完成后，使其失去焦点隐藏下拉框效果
            this.$refs.empBigDepartment.blur();
        },
        handleGropuNodeClick (data) {
            this.groupName = data.name;
            this.ruleForm.empSubDDDeptId = data.dept_id;
            this.ruleForm.empSubDepartment = data.name;
            this.$refs.postgroupdept.blur();
        },
        toggleContent () {
            this.isOpen = !this.isOpen
            this.activeName = this.isOpen ? 'content' : ''
        },
    }
}
</script>
  
<style scoped>
.title {
    cursor: pointer;
}

.ruleForm {
    padding: 10px;
}

.des-box {
    padding: 0 10px 0 30px
}

::v-deep .el-descriptions__title {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
}

::v-deep .el-descriptions__header {
    margin-bottom: 10px;
}

::v-deep .el-divider--horizontal {
    margin: 5px 0;
}

/* ::v-deep .el-collapse-item__header.is-active {
    display: none;
} */
::v-deep .el-collapse-item__wrap {
    border-bottom: none;
}

::v-deep .el-collapse {
    border: none;
}

::v-deep .el-collapse-item:first-child .el-collapse-item__header {
    display: none;
}

::v-deep .el-collapse-item__content {
    padding-bottom: 0;
}

.arrowTransformReturn {
    transition: 0.2s;
    transform-origin: center;
    transform: rotateZ(90deg);
}

.arrowTransform {
    transition: 0.2s;
    transform-origin: center;
    transform: rotateZ(0deg);
}
</style>