<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='inquirslist' @select='selectchange' :isSelection='false' :tableCols='tableCols'
            :loading="listLoading" :summaryarry="summaryarry">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.shopCode" placeholder="店铺" style="width:160px;" filterable clearable>
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                :value="item.shopCode">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.snick" placeholder="客服昵称" style="width:160px;" clearable
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <datepicker v-model.trim="filter.sdate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date">
                        </datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onImportSyj">导入</el-button>
                    <el-button type="primary" @click="onImportSyjModel">下载模板</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getinquirsList" />
        </template>
        <el-dialog title="京东客服人员咨询数据" :visible.sync="dialogVisibleSyj" width="30%" :close-on-click-modal="false"
            v-dialogDrag>
            <el-form ref="improtGroupForm" :model="improtGroupForm" label-width="55px" label-position="left">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2"
                            :on-change="onUploadChange2" :on-remove="onUploadRemove2">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                                @click="onSubmitupload2">上传</my-confirm-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
    getJingDongInquirsPageList, deleteJingDongInquirsAsync, importJingDongInquirsAsync
} from '@/api/customerservice/jingdonginquirs'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
const tableCols = [
    //{ istrue: false, prop: 'id', label: 'id', width: '160', sortable: 'custom', display: false, },
    { istrue: true, prop: 'shopName', label: '店铺', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'sdate', label: '日期', width: '95', sortable: 'custom', formatter: (row) => formatTime(row.sdate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'snick', label: '客服昵称', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'visitTotalCount', label: '咨询量', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'receptionCount', label: '接待量', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'inquirs', label: '售前接待人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'orderers', label: '促成下单人数', width: '80', sortable: 'custom' },
    // { istrue: true, prop: 'outers', label: '促成出库人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'orderSalesvol', label: '促成下单商品金额', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'outSalesvol', label: '促成出库商品金额', width: '80', sortable: 'custom' },
    // { istrue: true, prop: 'ipsRate', label: '咨询->出库转化率', width: '80', sortable: 'custom', formatter: (row) => (row.ipsRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'orderConversionRate', label: '咨询->下单转化率', width: '80', sortable: 'custom', formatter: (row) => ((row.orderConversionRate || 0) * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'responseTime', label: '平均响应时间', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'responseRate', label: '30s应答率', width: '80', sortable: 'custom', formatter: (row) => (row.responseRate * 100).toFixed(2) + "%" },

    // { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'veryGoodCount', label: '非常满意', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'goodCount', label: '满意', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'normalCount', label: '一般', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'badCount', label: '不满意', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'veryBadCount', label: '非常不满意', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'evaluateCount', label: '总评价数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'satisfactionRate', label: '满意率', width: '80', sortable: 'custom', formatter: (row) => ((row.satisfactionRate || 0) * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'unsatisfactionRate', label: '不满意率', width: '80', sortable: 'custom', formatter: (row) => ((row.unsatisfactionRate || 0) * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'invitationEvaluationCount', label: '邀评数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'invitationEvaluationRate', label: '邀评率', width: '80', sortable: 'custom', formatter: (row) => ((row.invitationEvaluationRate || 0) * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'createdTime', label: '导入时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'batchNumber', label: '导入批次', width: '170', sortable: 'custom' },
    {
        istrue: true, type: "button", label: '操作', width: "115",
        btnList: [
            { label: "删除", handle: (that, row) => that.deleteBatch(row, 0) },
            { label: "删除批次", handle: (that, row) => that.deleteBatch(row, 1) }
        ]
    }
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
    data() {
        return {
            that: this,
            filter: {
                inquirsType: 0,
            },
            shopList: [],
            userList: [],
            inquirslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "sdate", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            improtGroupForm: {
            },
        };
    },
    async mounted() {
        await this.getAllShopList();
    },
    methods: {
        async getAllShopList() {
            let shops = await getAllShopList();
            this.shopList = [];
            shops.data?.forEach(f => {
                if (f.shopCode && f.platform == 7)
                    this.shopList.push(f);
            });
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getinquirsList();
        },
        async getinquirsList() {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            console.log(params)
            this.listLoading = true;
            const res = await getJingDongInquirsPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.inquirslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async deleteBatch(row, type) {
            var that = this;
            this.$confirm("确认要执行删除" + (type == 1 ? "批次" : "") + "的操作吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let del = await deleteJingDongInquirsAsync({ idOrBatchNum: (type == 1 ? row.batchNumber : row.id), type: type });
                if (del?.success) {
                    that.$message({ message: '已删除', type: "success" });
                    that.onSearch();
                }
                else {
                    that.$message({ message: '发生异常，请刷新后重试', type: "error" });
                }
            });
        },
        onImportSyjModel() {
            // window.open("https://nanc.yunhanmy.com:10010/media/video/20250316/1901176439178510336.xlsx", "_blank");


          const downloadFile = async () => {
            const url = "https://nanc.yunhanmy.com:10010/media/video/20250319/1902330106734698497.xlsx";
            const fileName = '咨询数据导入模版.xlsx';

            try {
              // 1. 发起跨域请求获取文件内容
              const response = await fetch(url);
              const blob = await response.blob();

              // 2. 创建临时 URL 并触发下载
              const link = document.createElement('a');
              link.href = URL.createObjectURL(blob);
              link.download = fileName;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);

              // 3. 释放临时 URL
              URL.revokeObjectURL(link.href);
            } catch (error) {
              console.error('下载失败:', error);
            }
          };

      // 调用下载函数
          downloadFile();

        },
        async onImportSyj() {
            this.dialogVisibleSyj = true;
        },
        async onUploadChange2(file, fileList) {
            this.fileList = fileList;
        },
        async onUploadRemove2(file, fileList) {
            this.fileList = [];
        },
        async uploadFile2(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("inquirsType", 0);
            const res = await importJingDongInquirsAsync(form);
            if (res?.success) {
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
                this.dialogVisibleSyj = false;
            }
            // else {
            //     this.$message({ message: '发生异常，请刷新后重试', type: "error" });
            // }
        },
        async uploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        async onSubmitupload2() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload2.submit();
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
