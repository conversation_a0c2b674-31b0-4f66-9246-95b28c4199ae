<template>
    <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
      <el-scrollbar style="height: 100%">
        <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="140px" class="demo-ruleForm">
          <!-- <el-form-item label="类型：">
            <el-input v-model.trim="ruleForm.type" placeholder="类型" maxlength="20" clearable class="publicCss" />
          </el-form-item>
          <el-form-item label="区域：">
            <el-input v-model.trim="ruleForm.regionName" placeholder="区域" maxlength="20" clearable class="publicCss" />
          </el-form-item>
          <el-form-item label="部门类型：">
            <el-input v-model.trim="ruleForm.deptType" placeholder="部门类型" maxlength="20" clearable class="publicCss" />
          </el-form-item>
          <el-form-item label="部门：">
            <el-input v-model.trim="ruleForm.deptName" placeholder="部门" maxlength="20" clearable class="publicCss" />
          </el-form-item> -->
          <!-- <el-form-item label="总人数：" prop="regionName">
            <inputNumberYh v-model="ruleForm.regionName" :placeholder="'总人数'" class="publicCss" />
          </el-form-item> -->
          <!-- <el-form-item label="月离职人数：" prop="monthCount" style="font-size: 22px; font-weight: bold;">
            <inputNumberYh v-model="ruleForm.monthCount" :placeholder="'月离职人数'" class="publicCss" />
          </el-form-item> -->
          <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">义乌仓</div>
          <el-form-item label="比价次数：" prop="yiwuWarehouseComparisonCount">
            <inputNumberYh v-model="ruleForm.yiwuWarehouseComparisonCount" @input="changeCount(1)"  :fixed="0" :placeholder="'比价次数'" class="publicCss" />
          </el-form-item>
          <el-form-item label="节约成本：" prop="yiwuWarehouseCostSaving">
            <inputNumberYh v-model="ruleForm.yiwuWarehouseCostSaving" @input="changeCount(1)"  :fixed="0" :placeholder="'节约成本'" class="publicCss" />
          </el-form-item>

          <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">东莞仓</div>
          <el-form-item label="比价次数：" prop="dongguanWarehouseComparisonCount">
            <inputNumberYh v-model="ruleForm.dongguanWarehouseComparisonCount" @input="changeCount(1)"  :fixed="0" :placeholder="'比价次数'" class="publicCss" />
          </el-form-item>
          <el-form-item label="节约成本：" prop="dongguanWarehouseCostSaving">
            <inputNumberYh v-model="ruleForm.dongguanWarehouseCostSaving" @input="changeCount(1)"  :fixed="0" :placeholder="'节约成本'" class="publicCss" />
          </el-form-item>

          <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">南昌仓</div>
          <el-form-item label="比价次数：" prop="nanchangWarehouseComparisonCount">
            <inputNumberYh v-model="ruleForm.nanchangWarehouseComparisonCount" @input="changeCount(1)"  :fixed="0" :placeholder="'比价次数'" class="publicCss" />
          </el-form-item>
          <el-form-item label="节约成本：" prop="nanchangWarehouseCostSaving">
            <inputNumberYh v-model="ruleForm.nanchangWarehouseCostSaving" @input="changeCount(1)"  :fixed="0" :placeholder="'节约成本'" class="publicCss" />
          </el-form-item>

          <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">西安仓</div>
          <el-form-item label="比价次数：" prop="xianWarehouseComparisonCount">
            <inputNumberYh v-model="ruleForm.xianWarehouseComparisonCount" @input="changeCount(1)"  :fixed="0" :placeholder="'比价次数'" class="publicCss" />
          </el-form-item>
          <el-form-item label="节约成本：" prop="xianWarehouseCostSaving">
            <inputNumberYh v-model="ruleForm.xianWarehouseCostSaving" @input="changeCount(1)"  :fixed="0" :placeholder="'节约成本'" class="publicCss" />
          </el-form-item>

          <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">武汉</div>
          <el-form-item label="比价次数：" prop="wuhanComparisonCount">
            <inputNumberYh v-model="ruleForm.wuhanComparisonCount" @input="changeCount(1)"  :fixed="0" :placeholder="'比价次数'" class="publicCss" />
          </el-form-item>
          <el-form-item label="节约成本：" prop="wuhanCostSaving">
            <inputNumberYh v-model="ruleForm.wuhanCostSaving" @input="changeCount(1)"  :fixed="0" :placeholder="'节约成本'" class="publicCss" />
          </el-form-item>

          <!-- <div style="font-size: 15px; font-weight: 600; margin-left: 5px;">月度小计</div>
          <el-form-item label="比价次数：" prop="monthlySubtotalComparisonCount">
            <inputNumberYh v-model="ruleForm.monthlySubtotalComparisonCount" @input="changeCount(1)"  :fixed="0" :placeholder="'比价次数'" class="publicCss" />
          </el-form-item>
          <el-form-item label="节约成本：" prop="monthlySubtotalCostSaving">
            <inputNumberYh v-model="ruleForm.monthlySubtotalCostSaving" @input="changeCount(1)"  :fixed="0" :placeholder="'节约成本'" class="publicCss" />
          </el-form-item> -->


          
  
  
  
          <!-- <el-form-item label="月离职人数(4)：" prop="monthCount" style="font-size: 22px; font-weight: bold;">
            <inputNumberYh v-model="ruleForm.monthCount3" disabled :placeholder="'月离职人数'" class="publicCss" />
          </el-form-item>
          <el-form-item label="试用期离职：" prop="probationQuitCount">
            <inputNumberYh v-model="ruleForm.probationQuitCount" @input="changeCount(4)" :placeholder="'试用期离职'" class="publicCss" />
  
          </el-form-item>
          <el-form-item label="正式工离职：" prop="regularQuitCount">
            <inputNumberYh v-model="ruleForm.regularQuitCount" @input="changeCount(4)" :placeholder="'正式工离职'" class="publicCss" />
  
          </el-form-item>
          <el-form-item label="总离职成本：" prop="regularQuitCount">
            <el-input style="width:80%;" v-model.trim="ruleForm.totalResignationCost" :maxlength="8" placeholder="总离职成本" clearable />
          </el-form-item>
          <el-form-item label="人均离职成本：" prop="regularQuitCount">
            <el-input style="width:80%;" v-model.trim="ruleForm.perResignationCost" :maxlength="8" placeholder="人均离职成本" clearable />
          </el-form-item> -->
  
        </el-form>
      </el-scrollbar>
      <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
        <el-button @click="cancellationMethod">取消</el-button>
        <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
      </div>
    </div>
  </template>
  
  <script>
  import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
  import MyConfirmButton from '@/components/my-confirm-button'
  import { warehouseThirdPartyPriceComparisonSubmit } from '@/api/people/peoplessc.js';
  import checkPermission from '@/utils/permission'
  import decimal from '@/utils/decimal'
  export default {
    name: 'departmentEdit',
    components: {
      inputNumberYh, MyConfirmButton
    },
    props: {
      editInfo: {
        type: Object,
        default: () => {
          return {}
        }
      }
    },
    data() {
      return {
        selectProfitrates: [],
        ruleForm: {
          label: '',
          name: '',
          monthCount: 0,
          monthCount1: 0,
          monthCount2: 0,
          monthCount3: 0,
  
        },
        rules: {
          name: [
            { required: true, message: '请输入活动名称', trigger: 'blur' }
          ],
          label: [
            { required: true, message: '请输入活动名称', trigger: 'blur' }
          ]
        }
      }
    },
  
    async mounted() {
      this.$nextTick(() => {
        this.$refs.refruleForm.clearValidate();
      });
      this.ruleForm = { ...this.editInfo };
  
      // 延迟执行计算，避免初始化时的循环调用
      setTimeout(() => {
        this.changeCount(1);
        this.changeCount(2);
        this.changeCount(3);
        this.changeCount(4);
      }, 100); // 减少延迟时间
    },
    methods: {
      changeCount(val){
          console.log(val,"进入计算")
          if(val == 1){
              let a = this.ruleForm.day7Count? this.ruleForm.day7Count :  0;
              let b = this.ruleForm.day15Count? this.ruleForm.day15Count :  0;
              let c = this.ruleForm.day30Count? this.ruleForm.day30Count :  0;
              let d = this.ruleForm.day90Count? this.ruleForm.day90Count :  0;
              let e = this.ruleForm.yearCount? this.ruleForm.yearCount :  0;
              let f = this.ruleForm.twoYearCount?  this.ruleForm.twoYearCount :  0;
  
              this.ruleForm.monthCount = a+b+c+d+e+f;
          }else if(val == 2){
              let a = this.ruleForm.selfDeparture? this.ruleForm.selfDeparture :  0;
              let b = this.ruleForm.quitCount? this.ruleForm.quitCount :  0;
              let c = this.ruleForm.resignCount? this.ruleForm.resignCount :  0;
  
              this.ruleForm.monthCount1 = a+b+c;
          }else if(val == 3){
              // let a = this.ruleForm.eliminateCount? this.ruleForm.eliminateCount :  0;
              // let b = this.ruleForm.bodyReasonsCount? this.ruleForm.bodyReasonsCount :  0;
              // let c = this.ruleForm.familyReasonsCount? this.ruleForm.familyReasonsCount :  0;
              // let d = this.ruleForm.hasWorkCount? this.ruleForm.hasWorkCount :  0;
              // let e = this.ruleForm.highStressCount ? this.ruleForm.highStressCount : 0;
  
              // let f = this.ruleForm.otherCount? this.ruleForm.otherCount :  0;
  
              let a = this.ruleForm.otherCount? this.ruleForm.otherCount :  0;
              let b = this.ruleForm.workErrorCount? this.ruleForm.workErrorCount :  0;
              let c = this.ruleForm.deptDissolutionCount? this.ruleForm.deptDissolutionCount :  0;
              let d = this.ruleForm.highStressCount? this.ruleForm.highStressCount :  0;
              let e = this.ruleForm.bodyReasonsCount ? this.ruleForm.bodyReasonsCount : 0;
  
              let f = this.ruleForm.hasWorkCount? this.ruleForm.hasWorkCount :  0;
  
  
              this.ruleForm.monthCount2 =  a+b+c+d+e+f;
          }else if(val == 4){
              let a = this.ruleForm.regularQuitCount? this.ruleForm.regularQuitCount :  0;
              let b = this.ruleForm.probationQuitCount? this.ruleForm.probationQuitCount :  0;
  
  
              this.ruleForm.monthCount3 =  a+b;
          }
          this.$forceUpdate();
      },
      cancellationMethod() {
        this.$emit('cancellationMethod');
      },
      submitForm(formName) {
        console.log(this.ruleForm.label, 'this.ruleForm.label');
        this.$refs[formName].validate(async(valid) => {
            if (valid) {
              this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing")
              const { data, success } = await warehouseThirdPartyPriceComparisonSubmit(this.ruleForm)
              if(!success){
                  return
              }
              await this.$emit("search");
  
            } else {
              console.log('error submit!!');
              return false;
            }
          });
      //   this.$confirm('是否保存?', '提示', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     type: 'warning'
      //   }).then(async () => {
      //     this.$refs[formName].validate(async(valid) => {
      //       if (valid) {
      //         const { data, success } = await warehouseThirdPartyPriceComparisonSubmit(this.ruleForm)
      //         if(!success){
      //             return
      //         }
      //         await this.$emit("search");
  
      //       } else {
      //         console.log('error submit!!');
      //         return false;
      //       }
      //     });
      //   }).catch(() => {
      //   });
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
      },
    }
  }
  </script>
  <style scoped lang="scss">
  .publicCss {
    width: 80%;
  }
  </style>
  