<template>
    <my-container>
        <template #header>
            <el-form class="form" :inline="true">
                <el-form-item>
                    <el-button type="primary" @click="toggleRowExpansion">
                        全部{{ isExpansion ? "收缩" : "展开" }}
                    </el-button>
                    <el-button type="primary" @click="onAdd" v-if="checkPermission(['api:media:mediasceneset:savemediascenesetasync'])">新增一级场景</el-button>
                </el-form-item>
            </el-form>
        </template>
        <el-row>
            <el-col>
                <div class="grid-content bg-purple">
                    <el-table ref="dataTreeList" v-loading="listLoading" :data="scenes" border highlight-current-row row-key="sceneId" 
                    default-expand-all :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
                        <el-table-column prop="sceneName" label="场景名称" />
                        <el-table-column label="操作" width="200">
                            <template #default="{ $index, row }">
                                <el-button type="text" @click="onAddChild($index, row)" v-if="checkPermission(['api:media:mediasceneset:savemediascenesetasync'])">新增子级</el-button>
                                <el-button type="text" @click="onEdit($index, row)" v-if="checkPermission(['api:media:mediasceneset:savemediascenesetasync'])">编辑</el-button>
                                <el-button type="text" :loading="row._loading" @click="onDelete($index, row)" v-if="checkPermission(['api:media:mediasceneset:savemediascenesetasync'])">
                                    删除
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-col>
        </el-row>
        <el-drawer :title="formTitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="addFormVisiblerole" direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;" @close="closeAddForm">
            <section style="padding:24px 48px 74px 24px;">
                <el-form ref="addForm" :model="addForm" :rules="addFormRules" label-width="80px" :inline="false" v-loading="editFormLoading">
                    <el-row :hidden="true">
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item label="主键" prop="sceneId">
                                <el-input v-model="addForm.sceneId" auto-complete="off" :disabled="true" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item label="父级主键" prop="parentId">
                                <el-input v-model="addForm.parentId" auto-complete="off" :disabled="true" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item label="场景编码" prop="sceneCode">
                                <el-input v-model="addForm.sceneCode"  maxlength="50" auto-complete="off" :disabled="isEdit" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item label="场景名称" prop="sceneName">
                                <el-input v-model="addForm.sceneName"   maxlength="50"  auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </section>
            <div class="drawer-footer">
                <el-button @click.native="addFormVisiblerole = false">取消</el-button>
                <my-confirm-button type="submit" :validate="addFormValidate" :loading="addLoading" @click="onAddSave" />
            </div>
        </el-drawer>
    </my-container>
</template>
<script>
    import { listToTree } from '@/utils'
    import { getMediaSceneSetDataById, getMediaSceneSetData, saveMediaSceneSet, deleteMediaSceneSet } from '@/api/media/scene'
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    export default {
        name: "Users",
        components: { MyContainer, MyConfirmButton },
        data() {
            return {
                formTitle: "新增场景",
                listLoading: false,//树列表加载
                addFormVisiblerole: false,//新增编辑显隐
                editFormLoading: false,//编辑时转圈
                addLoading: false,//新增编辑提交按钮
                scenes: [],//树列表数据集
                isExpansion: true,//树列表收缩展开
                isEdit: false,//是否编辑模式
                addForm: {
                    sceneId: 0,
                    parentId: null,
                    sceneCode: '',
                    //orderCode: '',
                    SceneType:0,
                    sceneName: '',
                    level: 0
                },
                addFormRules: {
                    sceneCode: [{ required: true, message: '请输入场景编码', trigger: 'blur' }],
                    sceneName: [{ required: true, message: '请输入场景名称', trigger: 'blur' }]
                }
            };
        },
        async mounted() {
            this.getTreeList();
        },
        methods: {
            //获取数数据源
            async getTreeList() {
                this.listLoading = true;
                const res = await getMediaSceneSetData({sceneType:0});
                this.listLoading = false;
                if (!res?.success) {
                    return
                }
                this.scenes = listToTree(res?.data?.data, null, "sceneId", "parentId");
            },
            //收起展开
            toggleRowExpansion() {
                this.isExpansion = !this.isExpansion;
                this.toggleRowExpansionAll(this.scenes, this.isExpansion);
            },
            toggleRowExpansionAll(data, isExpansion) {
                data.forEach((item) => {
                    this.$refs.dataTreeList.toggleRowExpansion(item, isExpansion);
                    if (item.children !== undefined && item.children !== null) {
                        this.toggleRowExpansionAll(item.children, isExpansion);
                    }
                });
            },
            //新增一级
            async onAdd() {
                this.formTitle = "新增场景";
                this.addFormVisiblerole = true;
                this.addForm.parentId = null;
                this.addForm.level = 1;
                this.addForm.SceneType=0;
            },
            //新增验证
            addFormvalidate() {
                let isValid = false
                this.$refs.addForm.validate(valid => {
                    isValid = valid
                })
                return isValid
            },
            async onAddChild(index, row) {
                this.formTitle = "新增场景";
                var curLevel = (row.level + 1);
                if (curLevel > 3) {
                    this.$message({ type: 'error', message: '最大层级为3层' });
                    return;
                }
                this.addFormVisiblerole = true;
                this.addForm.parentId = row.sceneId;
                this.addForm.level = curLevel;
                this.addForm.sceneType = 0;
            },
            async onAddSave() {
                this.addLoading = true;
                const para = _.cloneDeep(this.addForm);
                var res = await saveMediaSceneSet(para);
                this.addLoading = false;
                if (!res?.success) {
                    return;
                }
                this.$message({
                    message: this.$t('Ok'),
                    type: 'success'
                })
                this.$refs['addForm'].resetFields();
                this.addFormVisiblerole = false;
                await this.getTreeList();
            },
            async onEdit(index, row) {
                this.formTitle = "编辑场景";
                this.addFormVisiblerole = true;
                this.isEdit = true;
                this.editFormLoading = true;
                var res = await getMediaSceneSetDataById({ sceneId: row.sceneId });
                if (!res?.success) {
                    return;
                }
                var curData = res?.data?.data;
                this.addForm.sceneId = curData.sceneId;
                this.addForm.parentId = curData.parentId;
                this.addForm.sceneCode = curData.sceneCode;
                this.addForm.sceneName = curData.sceneName;
                this.addForm.level = curData.level;
                this.addForm.sceneType = 0;
                this.editFormLoading = false;
            },
            async onDelete(index, row) {
                this.$confirm('确认删除, 是否继续?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    const res = await deleteMediaSceneSet({ sceneId: row.sceneId });
                    if (!res?.success) {
                        //this.$message({ type: 'success', message: res?.msg });
                    } else {
                        this.$message({ type: 'success', message: '删除成功!' });
                        await this.getTreeList();
                    }
                }).catch(() => {
                    this.$message({ type: 'info', message: '已取消删除' });
                });
            },
            //关闭新增编辑
            async closeAddForm() {
                this.isEdit = false;
                this.$refs.addForm.resetFields()
            }
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
