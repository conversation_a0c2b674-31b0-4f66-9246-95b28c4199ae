<template>
  <MyContainer>
    <div style="display: flex; flex-direction: row; width: 100%; height: 100%;overflow-y: hidden;">
      <div style="flex: 1;height: 390px;">
        <vxetablebase :id="'productLiteraturePopup202408041539'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
          :isRemoteSort="false" :tableData='tableData' :tableCols='isPlatform ? tableCols : tableCols2'
          :isSelection="false" :isSelectColumn="false" :toolbarshow="false"
          style="width: 100%;height: 340px;  margin: 0" v-loading="loading">
        </vxetablebase>
        共 {{ tableData.length }} 条
      </div>
      <div style="flex: 1;height: 100%;height: 390px; justify-content: center; align-items: center;">
        <div style="display: flex; flex-direction: row; width: 100%; justify-content: center; align-items: center;">
          <el-radio v-model="oneradio" label="1" @change="changepie('1')">{{ isPlatform ? '销量' : '售后率' }}</el-radio>
          <el-radio v-if="isPlatform" v-model="oneradio" label="2" @change="changepie('2')">占比</el-radio>
        </div>
        <buscharpie :charid="'charNegative1'" :isshowbai="percent" :thisStyle="thisStyle" :gridStyle="gridStyle"
          :analysisData="pieList" v-if="pieList"></buscharpie>
      </div>
    </div>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from 'dayjs'
import { formatPlatform } from '@/utils/tools'
import pieChart from '@/views/admin/homecomponents/PieChart'
import buscharpie from "@/views/order/LossOrderCostStatistics/childPages/piebus.vue"
import { re } from "mathjs";
const tableCols = [
  { width: 'auto', align: 'center', prop: 'Platform', label: '平台', formatter: (row) => row.Platform ? formatPlatform(row.Platform) : null, },
  { width: 'auto', align: 'center', prop: 'Sales', label: '销量', },
  { width: 'auto', align: 'center', prop: 'Proportion', label: '占比', formatter: (row) => !row.Proportion ? " " : row.Proportion + '%', },
]
const tableCols2 = [
  { width: 'auto', align: 'center', prop: 'Platform', label: '平台', formatter: (row) => row.Platform ? formatPlatform(row.Platform) : null, },
  { width: 'auto', align: 'center', prop: 'AfterSalesRate', label: '售后率', formatter: (row) => row.AfterSalesRate + '%', },
]
export default {
  name: "productLiteraturePopup",
  components: {
    MyContainer, vxetablebase, pieChart, buscharpie
  },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    isPlatform: {
      type: Boolean,
      default: false
    },
    detailsLabel: {
      type: String,
      default: ''
    },
    styleCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      that: this,
      tableCols,//表格列
      tableCols2,//表格列
      tableData: [],//表格数据
      loading: true,//加载状态
      pieList: null,//饼图数据
      oneradio: '1',//单选框
      percent: false,//是否百分比
      thisStyle: {
        width: '100%', height: '390px', 'box-sizing': 'border-box', 'line-height': '360px'
      },
      pieobj: {},//饼图对象
      gridStyle: {
        top: '0',
        left: '5%',
        right: '15%',
        bottom: '0',
        containLabel: false
      },
    }
  },
  async mounted() {
    //初始化
    this.$nextTick(() => {
      this.tableCols.forEach(item => {
        if (item.prop == 'Sales') {
          this.$set(item, 'label', this.detailsLabel)
        }
      })
      this.tableCols2.forEach(item => {
        if (item.prop == 'Sales') {
          this.$set(item, 'label', this.detailsLabel)
        }
      })
      if (this.isPlatform == true) {
        this.pieobj.name = 'Platform';
        this.pieobj.value = 'Sales';
        this.pieobj.code = 'Proportion';
        this.changepie('1')
      } else {
        this.pieobj.name = 'Platform';
        this.pieobj.value = 'AfterSalesRate';
        this.pieobj.code = 'AfterSalesRate';
        this.changepie('2')
      }
    })
    this.tableData = this.data
    this.loading = false
  },
  methods: {
    //改变饼图
    changepie(val) {
      let chatlist = [];
      this.pieList = null;
      if (val == '1') {
        this.data.map((item) => {
          chatlist.push({
            name: this.pieobj.name == 'Platform' ? formatPlatform(item[this.pieobj.name]) : item[this.pieobj.name],
            value: item[this.pieobj.value]
          })
        })
        this.percent = false
      } else if (val == '2') {
        this.data.map((item) => {
          chatlist.push({
            name: this.pieobj.name == 'Platform' ? formatPlatform(item[this.pieobj.name]) : item[this.pieobj.name],
            value: item[this.pieobj.code].toFixed(2)
          })
        })
        this.percent = true
      }
      this.$nextTick(() => {
        this.pieList = {
          title: {
            left: 'center',
            top: 'center'
          },
          series: [
            {
              type: 'pie',
              data: chatlist,
              radius: ['40%', '70%']
            }
          ]
        };
      })
    }
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}

.tooltips {
  color: red;
  font-size: 14px;
  margin-bottom: 10px;
}
</style>
