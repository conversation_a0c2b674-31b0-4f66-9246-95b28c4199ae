<template>
     <my-container v-loading="pageLoading">
       <el-row >
            <el-col :xs="2" :sm="3" :lg="2">
                &nbsp;
            </el-col>
            <el-col :xs="2" :sm="2" :lg="2">
                <el-row style ="height: 40px;">  </el-row>
                <el-row style ="height: 40px;">  </el-row> 
                <el-row style ="height: 40px;">  <el-button type="primary" style="width: 120px;" @click="onOpenUpload(1)">拍摄照片</el-button> </el-row>
                <el-row style ="height: 40px;">  <el-button type="primary" style="width: 120px;" @click="onOpenUpload(2)">建模照片</el-button> </el-row> 
                <el-row style ="height: 10px;">  </el-row>
                <el-row style ="height: 40px;">  </el-row> 
                <el-row style ="height: 40px;">  <el-button type="primary" style="width: 120px;" @click="onOpenUpload(3)">主图视频</el-button></el-row> 
                <el-row style ="height: 40px;">  <el-button type="primary" style="width: 120px;" @click="onOpenUpload(5)">建模视频</el-button> </el-row>
                <el-row style ="height: 40px;">  </el-row> 
                <el-row style ="height: 40px;">  <el-button type="primary" style="width: 120px;" @click="onOpenUpload(6)">美工</el-button> </el-row>
                <el-row style ="height: 40px;">  </el-row> 
            </el-col>
            <el-col :xs="20" :sm="20" :lg="20"> 
                    <!--上传文件--> 
                    <el-drawer :title="title"  :key="title" :visible.sync="successfiledrawerDetial" direction="rtl" 
                    :before-close="successfileddetailrawerClose" size="45%"   :wrapperClosable="true"  :close-on-press-escape ="false"
                        :append-to-body="true" > 
                            <shootinguploadactiondetail  ref="cuploadref"  
                            :InitDataArrayOne="InitDataArrayOne"  
                            :InitDataArrayTwo="InitDataArrayTwo"  
                            :rowinfo ="rowinfo"
                            :islook="islook"
                            :reloadupfile ="onOpenUpload"
                            style="height: 92%;width:100%" ></shootinguploadactiondetail>
                            <div class="drawer-footer" >
                                <el-button  :loading="uploaddrawer" @click="successfileddetailrawerClose">取 消</el-button> 
                                <my-confirm-button type="submit" :loading="uploaddrawer"  @click="sumbitUploadInfo()" v-show="!islook"/>
                            </div> 
                    </el-drawer>
            </el-col>
        </el-row>
    </my-container>
</template>
<script> 
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import shootinguploadactiondetail from '@/views/media/shooting/pddchangeImg/pddchangeimguploadactiondetail' ;
import {getUploadSuccessAttachment  } from '@/api/media/pddchangeimg';
 

export default { 
    components: { MyContainer,shootinguploadactiondetail,MyConfirmButton  },
    props:["rowinfo","islook"],
    data() {
        return {
            imgtype:".image/jpg,image/jpeg,image/png",
            rartype:".rar,.zip,.7z",
            psdtype:".psd,.psb",
            vediotype:".mp4,.mov,.vedio,.av,.wmv,.mpg,.mpeg,.rm,.flv,.swf",
            title:"",  
            InitDataArrayOne:[],
            InitDataArrayTwo:[],
            successfiledrawerDetial:false,
            uploaddrawer:false,
            pageLoading:false,
            curActionId:0,
            tempoutlist:[],
            startIndex:0,
            atfterUplaodData:null,
            upmsginfo:null,
            uploadType:0,
            fileType:0,
            uploadinfos:[]

        };
    },
    async mounted() {
    }, 
    methods: {
        
        async  onOpenUpload(index){
            this.InitDataArrayOne=[];
            this.InitDataArrayTwo=[];
            this.pageLoading=true; 
     
            switch(index){
                case 1: 
                    var res = await getUploadSuccessAttachment({uploadType:index,changeImgTaskId:this.rowinfo,actionType:1});
                     if(res?.success){
                         //加载数据
                         var uploadinfo1=[];
                         var uploadinfo2=[];
                         var uploadinfo3=[];
                         var uploadinfo4=[];
                         var uploadinfo5=[];
                         var uploadinfo6=[]; 
                          res.data.forEach(element => {
                            if(element.fileType == 1){
                                uploadinfo1.push(element);
                            }else
                            if(element.fileType == 2){
                                 uploadinfo2.push(element);
                            }else
                            if(element.fileType == 3){
                                 uploadinfo3.push(element);
                            }else
                            if(element.fileType == 4){
                                 uploadinfo4.push(element);
                            }else
                            if(element.fileType == 5){
                                 uploadinfo5.push(element);
                            }else
                            if(element.fileType == 6){
                                 uploadinfo6.push(element);
                            }
                            
                         });   
                         this.InitDataArrayOne.push({ref:"u11",name:"首拍JPG"  ,uploadType:index,fileType:"1",acctpye:this.imgtype,uptype:"imgtype",uploadinfo:uploadinfo1});
                         this.InitDataArrayTwo.push({ref:"u12",name:"压缩包"   ,uploadType:index,fileType:"2",acctpye:this.rartype,uptype:"rartype",uploadinfo:uploadinfo2});
                         this.InitDataArrayOne.push({ref:"u13",name:"补拍1-JPG",uploadType:index,fileType:"3",acctpye:this.imgtype,uptype:"imgtype",uploadinfo:uploadinfo3});
                         this.InitDataArrayTwo.push({ref:"u14",name:"补拍2-JPG",uploadType:index,fileType:"4",acctpye:this.imgtype,uptype:"imgtype",uploadinfo:uploadinfo4});
                         this.InitDataArrayOne.push({ref:"u15",name:"补拍3-JPG",uploadType:index,fileType:"5",acctpye:this.imgtype,uptype:"imgtype",uploadinfo:uploadinfo5});
                         this.InitDataArrayTwo.push({ref:"u16",name:"补拍4-JPG",uploadType:index,fileType:"6",acctpye:this.imgtype,uptype:"imgtype",uploadinfo:uploadinfo6}); 
                         this.title="上传拍摄照片"; 
                     }else{
                        this.$message({ message: '数据获取失败，请稍后重试', type: "error" });
                     }
                    break;
                case 2:
                    var res = await getUploadSuccessAttachment({uploadType:index,changeImgTaskId:this.rowinfo,actionType:1});
                    if(res?.success){
                        //加载数据
                        var uploadinfo1=[];
                        var uploadinfo2=[];
                        var uploadinfo3=[]; 
                        res.data.forEach(element => {
                            if(element.fileType == 1){
                                uploadinfo1.push(element);
                            }else
                            if(element.fileType == 2){
                                    uploadinfo2.push(element);
                            }else
                            if(element.fileType == 3){
                                    uploadinfo3.push(element);
                            } 
                        });   
                        this.InitDataArrayOne.push({ref:"u21",name:"建模JPG"             ,uploadType:index,fileType:"1",acctpye:this.imgtype,uptype:"imgtype",uploadinfo:uploadinfo1});
                        this.InitDataArrayTwo.push({ref:"u22",name:"工程文件（压缩包）"   ,uploadType:index,fileType:"2",acctpye:this.rartype,uptype:"rartype",uploadinfo:uploadinfo2});
                        this.InitDataArrayOne.push({ref:"u23",name:"建模PSD"             ,uploadType:index,fileType:"3",acctpye:this.psdtype,uptype:"psdtype",uploadinfo:uploadinfo3});
                        this.title="上传建模照片"; 
                     }else{
                        this.$message({ message: '数据获取失败，请稍后重试', type: "error" });
                     }
                    break;
                case 3:
                var res = await getUploadSuccessAttachment({uploadType:index,changeImgTaskId:this.rowinfo,actionType:1});
                    if(res?.success){
                        //加载数据
                        var uploadinfo1=[];
                        var uploadinfo2=[]; 
                        res.data.forEach(element => {
                        if(element.fileType == 1){
                            uploadinfo1.push(element);
                        }else
                        if(element.fileType == 2){
                                uploadinfo2.push(element);
                        } 
                        });   
                        this.InitDataArrayOne.push({ref:"u31",name:"成品视频"            ,uploadType:index,fileType:"1",acctpye:this.vediotype,uptype:"vediotype",uploadinfo:uploadinfo1});
                        this.InitDataArrayTwo.push({ref:"u32",name:"工程文件（压缩包）"   ,uploadType:index,fileType:"2",acctpye:this.rartype,uptype:"rartype",uploadinfo:uploadinfo2});
                        this.title="上传主图视频"; 
                    }else{
                        this.$message({ message: '数据获取失败，请稍后重试', type: "error" });
                     }
                    break;
                case 4:
                    var res = await getUploadSuccessAttachment({uploadType:index,changeImgTaskId:this.rowinfo,actionType:1});
                    if(res?.success){
                        //加载数据
                        var uploadinfo1=[];
                        var uploadinfo2=[];
                        var uploadinfo3=[]; 
                        res.data.forEach(element => {
                            if(element.fileType == 1){
                                uploadinfo1.push(element);
                            }else
                            if(element.fileType == 2){
                                    uploadinfo2.push(element);
                            }else
                            if(element.fileType == 3){
                                    uploadinfo3.push(element);
                            } 
                        });  
                        this.InitDataArrayOne.push({ref:"u41",name:"成品视频"            ,uploadType:index,fileType:"1",acctpye:this.vediotype,uptype:"vediotype",uploadinfo:uploadinfo1});
                        this.InitDataArrayTwo.push({ref:"u42",name:"工程文件（压缩包）"   ,uploadType:index,fileType:"2",acctpye:this.rartype,uptype:"rartype",uploadinfo:uploadinfo2});
                        this.title="上传微详情视频"; 
                    }else{
                        this.$message({ message: '数据获取失败，请稍后重试', type: "error" });
                     }
                    break;
                case 5:
                    var res = await getUploadSuccessAttachment({uploadType:index,changeImgTaskId:this.rowinfo,actionType:1});
                    if(res?.success){
                        //加载数据
                        var uploadinfo1=[];
                        var uploadinfo2=[];
                        var uploadinfo3=[]; 
                        res.data.forEach(element => {
                            if(element.fileType == 1){
                                uploadinfo1.push(element);
                            }else
                            if(element.fileType == 2){
                                    uploadinfo2.push(element);
                            }else
                            if(element.fileType == 3){
                                uploadinfo3.push(element);
                            } 
                        });  
                        this.InitDataArrayOne.push({ref:"u51",name:"成品视频"            ,uploadType:index,fileType:"1",acctpye:this.vediotype,uptype:"vediotype",uploadinfo:uploadinfo1});
                        this.InitDataArrayTwo.push({ref:"u52",name:"工程文件（压缩包）"   ,uploadType:index,fileType:"2",acctpye:this.rartype,uptype:"rartype",uploadinfo:uploadinfo2});
                        this.title="上传建模视频"; 
                    }else{
                        this.$message({ message: '数据获取失败，请稍后重试', type: "error" });
                     }
                    break;
                case 6:
                var res = await getUploadSuccessAttachment({uploadType:index,changeImgTaskId:this.rowinfo,actionType:1});
                     if(res?.success){
                         //加载数据
                         var uploadinfo1=[];
                         var uploadinfo2=[];
                         var uploadinfo3=[];
                         var uploadinfo4=[];
                         var uploadinfo5=[];
                         var uploadinfo6=[];
                         var uploadinfo7=[];
                        res.data.forEach(element => {
                            if(element.fileType == 1){
                                uploadinfo1.push(element);
                            }else
                            if(element.fileType == 2){
                                uploadinfo2.push(element);
                            }else
                            if(element.fileType == 3){
                                uploadinfo3.push(element);
                            }else
                            if(element.fileType == 4){
                                uploadinfo4.push(element);
                            }else
                            if(element.fileType == 5){
                                uploadinfo5.push(element);
                            }else
                            if(element.fileType == 6){
                                uploadinfo6.push(element);
                            }else
                            if(element.fileType == 7){
                                uploadinfo7.push(element);
                            }
                        });   
                        this.InitDataArrayOne.push({ref:"u61",name:"主图-Pc端"           ,uploadType:index,fileType:"1",acctpye:this.imgtype,uptype:"imgtype",uploadinfo:uploadinfo1});
                        this.InitDataArrayTwo.push({ref:"u62",name:"主图-无线端"         ,uploadType:index,fileType:"2",acctpye:this.imgtype,uptype:"imgtype",uploadinfo:uploadinfo2});
                        this.InitDataArrayOne.push({ref:"u63",name:"SKU"                ,uploadType:index,fileType:"3",acctpye:this.imgtype,uptype:"imgtype",uploadinfo:uploadinfo3});
                        this.InitDataArrayTwo.push({ref:"u64",name:"直通车图片"          ,uploadType:index,fileType:"4",acctpye:this.imgtype,uptype:"imgtype",uploadinfo:uploadinfo4});
                        this.InitDataArrayOne.push({ref:"u65",name:"详情页-裁剪"         ,uploadType:index,fileType:"5",acctpye:this.imgtype,uptype:"imgtype",uploadinfo:uploadinfo5});
                        this.InitDataArrayTwo.push({ref:"u66",name:"详情页-未裁剪"       ,uploadType:index,fileType:"6",acctpye:this.imgtype,uptype:"imgtype",uploadinfo:uploadinfo6}); 
                        this.InitDataArrayOne.push({ref:"u67",name:"PSD文件",uploadType:index,fileType:"7",acctpye:this.psdtype,uptype:"psdtype",uploadinfo:uploadinfo7});
                        this.title="上传详情页"; 
                    }else{
                        this.$message({ message: '数据获取失败，请稍后重试', type: "error" });
                     }
                    break; 
            }
            this.curActionId = index;
            this.pageLoading = false;
            this.successfiledrawerDetial = true;
        },
        
        //提交上传的信息
        async sumbitUploadInfo(){
           this.uploaddrawer=true;
           await this.$refs.cuploadref.sumbitUploadInfo();
           this.uploaddrawer=false;
        },
       
        //关闭窗口
        async successfileddetailrawerClose() {
            var ret =  await this.$refs.cuploadref.getIsChange(); 
            if(ret){
                this.$confirm('还有未保存的工作哦确定关闭吗？')
                .then(_ => {
                    this.successfiledrawerDetial =false;
                })
                .catch(_ => {});
            }else{ 
                this.successfiledrawerDetial =false;
            }
        }
    
    },
};
</script>

