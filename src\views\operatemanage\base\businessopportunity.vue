<template>
  <container v-loading="pageLoading" style="height: 98%;">
    <template #header>
      <div class="top">
        <el-input v-model="filter.planningTitle" style="width: 200px" :clearable="true" placeholder="企划名称"
          maxlength="100" clearable class="publicCss"></el-input>
        <el-input v-model.number="filter.planningId" style="width: 200px" :clearable="true" placeholder="企划ID"
          maxlength="100" clearable class="publicCss"></el-input>
        <el-select style="width:140px" v-model="categoryLeve1" placeholder="请选择一级类目"
          @change="getcategoryall(2, categoryLeve1)" clearable filterable :collapse-tags="true" class="publicCss">
          <el-option v-for="(item) in categoryall.categoryone" :key="item.value" :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <el-select style="width:140px" v-model="categoryLeve2" placeholder="请选择二级类目"
          v-if="categoryall.categorytwo.length > 0 && categoryLeve1" @change="getcategoryall(3, categoryLeve2)"
          clearable filterable :collapse-tags="true" class="publicCss">
          <el-option v-for="(item) in categoryall.categorytwo" :key="item.value" :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <el-select style="width:140px" v-model="categoryLeve3" placeholder="请选择三级类目"
          v-if="categoryall.categorythr.length > 0 && categoryLeve2" @change="getcategoryall(4, categoryLeve3)"
          clearable filterable :collapse-tags="true" class="publicCss">
          <el-option v-for="(item) in categoryall.categorythr" :key="item.value" :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <el-select style="width:140px" v-model="categoryLeve4" placeholder="请选择四级类目"
          v-if="categoryall.categoryfou.length > 0 && categoryLeve3" @change="getcategoryall(5, categoryLeve4)"
          clearable filterable :collapse-tags="true" class="publicCss">
          <el-option v-for="(item) in categoryall.categoryfou" :key="item.value" :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <el-select style="width:140px" v-model="categoryLeve5" placeholder="请选择五级类目" clearable filterable
          v-if="categoryall.categoryfiv.length > 0 && categoryLeve4" :collapse-tags="true" class="publicCss">
          <el-option v-for="(item) in categoryall.categoryfiv" :key="item.value" :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <el-button type="primary" @click="onSearch('search')">查询</el-button>
        <el-button @click="onReset">重置</el-button>
      </div>
    </template>
    <div :style="showgrid ? 'display:none;' : ''" style="display: flex; flex-wrap: wrap;overflow:auto;">
      <div v-for="obj in listTree" style="padding: 0.5rem;height: 350px;" v-bind:key="obj.planningId">
        <el-card body-style="padding: 0px">
          <img v-if="obj.picUrls" :src="obj.picUrls" style="height: 218px; width: 218px" class="image"
            @click="showHtml(obj)" />
          <img v-if="!obj.picUrls" src="https://img.alicdn.com/tfs/TB1614dxqL7gK0jSZFBXXXZZpXa-800-800.png"
            style="height: 218px; width: 218px" class="image" @click="showHtml(obj)" />
          <div style="padding: 14px">
            <div style="width: 190px;overflow: hidden; display: flex; flex-direction: column; justify-content: center;"">
              <el-tooltip class=" item" effect="dark" :content="obj.planningTitle1 + ' ' + obj.planningTitle2"
              placement="top">
              <div style="width: 100%;height: 50px;overflow:auto;">
                {{ obj.planningTitle1 }} {{ obj.planningTitle2 }}
              </div>
              </el-tooltip>
              <div
                style="width: 100%;height: 21px; font-size: 16px; white-space: nowrap; text-overflow: ellipsis; overflow: hidden; ">
                <span v-show="obj.specMaxPrice >= 0">
                  {{ obj.specMaxPrice }}
                </span>
              </div>
            </div>
            <div class="bottom clearfix">
              <div style="font-size: 13px; text-align: right; color: #2ad480">
                <img src="https://img.alicdn.com/imgextra/i4/O1CN01n9exeN204ABScWQaX_!!6000000006795-55-tps-11-12.svg"
                  style="color: #2ad480;margin-right: 5px;">
                <span style="color: #2ad480;">提报免审</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <div :style="showgrid ? '' : 'display:none;'" style="height: 680px">
      <ces-table ref="table" style="height: 700px" :that="that" :isIndex="false" :tableData="listTree"
        :tableCols="tableCols" :tableHandles="tableHandles" :loading="listLoading" @sortchange="sortchange"
        :showsummary="false" />
    </div>

    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog :visible.sync="htmlvisible" width="70%" v-dialogDrag @close="handleDialogClose">
      <div>
        <div style="display: flex; flex-direction: row">
          <div class="block" style="width: 30%; height: 350px; position: relative;">
            <span class="demonstration" style="margin-bottom: 20px;font-size: 25px;margin-left: 44px;">商品图片</span>
            <el-carousel height="300px" :indicator-position="null" style="width: 100%;" ref="carousel"
              :initial-index="0">
              <el-carousel-item v-for="(item, index) in picUrls" :key="index"
                style="display: flex; justify-content: center; align-items: center;">
                <img :src="item" style="max-width: 100%; max-height: 300px; object-fit: contain;" />
              </el-carousel-item>
            </el-carousel>
          </div>
          <div style="width: 70%">
            <div style="display: flex; flex-direction: column;margin-bottom: 20px;">
              <div class="table-caption" style="margin-bottom: 20px;font-size: 25px;">企划详情</div>
              <div style="margin-bottom: 20px;">
                <span style="font-size:20px;margin-right: 10px;">{{ allinfo.data.planningTitle }}</span>
                <img src="https://img.alicdn.com/imgextra/i4/O1CN01n9exeN204ABScWQaX_!!6000000006795-55-tps-11-12.svg"
                  style="color: #2ad480;margin-right: 5px;">
                <span style="color: #2ad480;">提报免审</span>
              </div>
              <div class="info-container">
                <div class="info-item">
                  <span class="label">企划ID</span>
                  <span class="value">{{ allinfo.data.planningId }}</span>
                </div>
                <div class="info-item">
                  <span class="label">商品类目</span>
                  <span class="value">{{ allinfo.data.categoryValue }}</span>
                </div>
                <div class="info-item">
                  <span class="label">负责小二</span>
                  <span class="value">{{ allinfo.data.admin }}</span>
                </div>
              </div>
            </div>

            <div v-if="skuTable.length > 0" style="display: flex; flex-direction: column">
              <div class="table-caption" style="font-size: 14px;color: #b4bebb;margin-bottom: 10px;">产品SKU规格及建议价:
                <el-tooltip slot="suffix" effect="dark" content="此价格仅为有竞争力的价格参考，后续无需绑定企划规格，可根据实际情况设定SKU价格"
                  placement="right">
                  <i class="el-input__icon el-icon-question"></i>
                </el-tooltip>
              </div>
              <div v-for="item in skuTable" :key="item.key" style="display: flex;">
                <span style="width: 350px; flex-shrink: 0;margin-right: 10px;">{{ item.key }}</span>
                <span style="width: 350px; flex-shrink: 0;">￥{{ item.v }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </container>
</template>

<script>
import { replaceSpace } from '@/utils/getCols'
import { treeToList, listToTree, getTreeParents } from "@/utils";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import platformservice from "@/views/operatemanage/base/platformservice";
import { formatPlatform, formatYesornoBool, platformlist } from "@/utils/tools";
import {
  getbyid,
  deletebyid,
  deletebyids,
  addoredit,
  getList,
  importProductCategory,
} from "@/api/operatemanage/base/category";

import {
  getPageListAsync,
  getcategoryList,
  getPlanningTypeDescList,
  getCategoryListAsync
} from "@/api/operatemanage/base/businessopportunity";

const tableCols = [
  {
    istrue: true,
    sortable: "custom",
    prop: "planningId",
    label: "计划ID",
    width: "120",
    type: "click",
    handle: (that, row) => that.showDetail(row),
  },

  {
    istrue: true,
    sortable: "custom",
    prop: "categoryValue",
    label: "分类信息",
    width: "200",
  },
  {
    istrue: true,
    sortable: "custom",
    prop: "planningTitle",
    label: "计划标题",
    width: "160",
    type: "click",
    handle: (that, row) => that.showDetail(row),
  },
  {
    istrue: true,
    sortable: "custom",
    prop: "typeDesc",
    label: "类型描述",
    width: "120",
  },
  { istrue: true, prop: "productProperties", label: "产品属性", width: "200" },
  { istrue: true, prop: "skuStandards", label: "sku规格", width: "200" },
  {
    istrue: true,
    sortable: "custom",
    prop: "createTime",
    label: "创建时间",
    width: "160",
  },
  {
    istrue: true,
    sortable: "custom",
    prop: "endTime",
    label: "结束时间",
    width: "160",
  },
  {
    istrue: true,
    sortable: "custom",
    prop: "deliverRequirement",
    label: "仓发要求",
    width: "120",
  },
  {
    istrue: true,
    sortable: "custom",
    prop: "xiaoEr",
    label: "小二",
    width: "100",
  },
  {
    istrue: true,
    prop: "basicQualityRequirement",
    label: "基础品控",
    width: "200",
  },

];
const tableHandles1 = [
  //{label:"下载导入类目模板", handle:(that)=>that.onDownMuban()},{label:"导入产品类目", handle:(that)=>that.onImportCategory()}
];
export default {
  name: "Api",
  components: { cesTable, platformservice, container, MyConfirmButton },
  data() {
    return {
      category: null,
      categoryLv: 0,
      that: this,
      filter: { platform: 1, updateTime: "2022-03-16", title: "", planningId: null, planningTitle: null, categoryValue: null },
      platformlist: platformlist,
      listTree: [],
      listLoading: false,
      sels: [], // 列表选中列
      tableCols: tableCols,
      tableHandles: tableHandles1,
      addDialogFormVisible: false,
      editFormVisible: false, // 编辑界面是否显示
      editLoading: false,

      html: "",
      htmlvisible: false,
      picUrls: [],
      allinfo: { data: {} },
      propertiesTable: [],
      skuTable: [],
      categoryList: [],

      layercategoryList: [],

      selectedcategory: "",
      modules: [],
      endTime: "",
      planningTypeDescList: [],
      brandlist: [],
      filterUpdateTime: null,
      filterEndTime: null,
      syncManagerLoading: false,
      syncFinancialLoading: false,
      syncOrderLoading: false,
      deleteLoading: false,
      total: 0,
      listLoading: false,
      pager: {
        currentPage: 1,
        pageSize: 50,
        OrderBy: "id",
        IsAsc: false,
      },
      pageLoading: false,
      platformserviceVisible: false,
      showgrid: false,
      categoryLeve1: null,
      categoryLeve2: null,
      categoryLeve3: null,
      categoryLeve4: null,
      categoryLeve5: null,
      categoryall: {
        categoryone: [],
        categorytwo: [],
        categorythr: [],
        categoryfou: [],
        categoryfiv: []
      },
      selectrootpath: '',
      selectlayerdeep: 1,
      importFilte: { platform: "" },
      pickerOptions1: {
        shortcuts: [{
          text: '7天内',
          onClick(picker) {
            const yestoday = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
            const end = new Date(new Date(new Date(new Date().getTime() + 3600 * 1000 * 24).toLocaleDateString()).getTime());
            const start = new Date(new Date(yestoday.toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 6);
            //start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const yestoday = new Date(new Date().getTime() - 3600 * 1000 * 24);
            const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
            const start = new Date(new Date(new Date().toLocaleDateString()).getTime());
            start.setTime(start.getTime() - 3600 * 1000 * 24);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '今天',
          onClick(picker) {
            const end = new Date(new Date(new Date(new Date().getTime() + 3600 * 1000 * 24).toLocaleDateString()).getTime());
            const start = new Date(new Date(new Date().toLocaleDateString()).getTime());
            start.setTime(start);
            picker.$emit('pick', [start, end]);
          }
        }]
      }
    };
  },

  async mounted() {
    this.onSearch();
    this.getcategoryall(1, null);
    // this.planningTypeDescList = (await getPlanningTypeDescList()).data
  },
  methods: {
    //打开弹窗图片默认第一张
    handleDialogClose() {
      this.$refs.carousel.setActiveItem(0);
      this.picUrls = []
    },
    async getcategoryall(level, label) {
      switch (level) {
        case 1:
          this.categoryLeve1 = null;
          this.categoryLeve2 = null;
          this.categoryLeve3 = null
          this.categoryLeve4 = null
          this.categoryLeve5 = null
          break;
        case 2:
          this.categoryLeve2 = null;
          this.categoryLeve3 = null
          this.categoryLeve4 = null
          this.categoryLeve5 = null
          break;
        case 3:
          this.categoryLeve3 = null;
          this.categoryLeve4 = null
          this.categoryLeve5 = null
          break;
        case 4:
          this.categoryLeve4 = null;
          this.categoryLeve5 = null
          break;
        case 5:
          this.categoryLeve5 = null
          break;
        default:
          break;
      }
      const { data, success } = await getCategoryListAsync({ categoryLv: level, category: label })
      if (success) {
        var datalist = data.map(item => ({
          label: item.category,
          value: item.category,
        }));
        this.categoryall.categorytwo = level == 1 ? [] : this.categoryall.categorytwo;
        this.categoryall.categorythr = level <= 2 ? [] : this.categoryall.categorythr;
        this.categoryall.categoryfou = level <= 3 ? [] : this.categoryall.categoryfou;
        this.categoryall.categoryfiv = level <= 4 ? [] : this.categoryall.categoryfiv;
        switch (level) {
          case 1:
            this.categoryall.categoryone = datalist;
            break;
          case 2:
            this.categoryall.categorytwo = datalist;
            break;
          case 3:
            this.categoryall.categorythr = datalist;
            break;
          case 4:
            this.categoryall.categoryfou = datalist;
            break;
          case 5:
            this.categoryall.categoryfiv = datalist;
            break;
          default:
            break;
        }
      }
    },
    onReset() {
      this.filter.planningTitle = null
      this.filter.planningId = null
      this.filter.categoryValue = null
      this.categoryLeve1 = null
      this.categoryLeve2 = null
      this.categoryLeve3 = null
      this.categoryLeve4 = null
      this.categoryLeve5 = null
    },
    toUpLayer() {
      if (this.selectlayerdeep == 1) {
        this.$refs.pager.setPage(1)
        this.initcategoryList();
        return;
      }
      var p = this.selectrootpath.lastIndexOf('>');
      this.selectrootpath = this.selectrootpath.substring(0, p)
      this.selectlayerdeep = this.selectlayerdeep - 1;
      this.initdeepcategoryList(this.selectrootpath, this.selectlayerdeep)

    },
    initcategoryList() {
      var size = 0;
      this.layercategoryList = [];
      for (var i in this.categoryList) {
        var path = this.categoryList[i].split(">")[0];
        if (!this.exsitspath(this.layercategoryList, path))
          this.layercategoryList.push({
            path: path,
            deep: 1,
            deeppath: path,
            value: this.sum(this.categoryList, path),
          });
      }
      this.selectedcategory = "";
      this.$refs.pager.setPage(1)
      this.onSearch();
    },
    initdeepcategoryList(rootpath, layerdeep) {
      this.selectrootpath = rootpath
      this.selectlayerdeep = layerdeep


      var size = 0;
      this.layercategoryList = [];
      for (var i in this.categoryList) {
        if (this.categoryList[i].indexOf(rootpath) == 0 && this.categoryList[i].split(">")[layerdeep]) {
          var path = this.categoryList[i].split(">")[layerdeep].split('|')[0];
          if (!this.exsitspath(this.layercategoryList, path))
            if (path) {
              this.layercategoryList.push({
                path: path,
                deep: layerdeep + 1,
                deeppath: rootpath + ">" + path,
                value: this.sum(this.categoryList, rootpath + ">" + path),
              });

            }

        }
      }

      this.$refs.pager.setPage(1)

      this.selectedcategory = rootpath;
      this.onSearch();
    },
    exsitspath(list, path) {
      for (var i in list) {
        if (list[i].path == path) return true;
      }
      return false;
    },

    sum(list, path) {
      var value = 0;
      for (var i in list) {
        if (list[i].indexOf(path) == 0)
          value += parseInt(list[i].split("|")[1]);
      }
      return value;
    },

    async changePlanningType() {

      this.$refs.pager.setPage(1)
      this.freshTime();
    },

    sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.$refs.pager.setPage(1)
      this.onSearch();
    },
    changeShowView() {
      this.showgrid = !this.showgrid;
    },
    async freshTime() {
      this.$refs.pager.setPage(1)
      this.filterUpdateTime = null;
      this.filterEndTime = null;
      var UpdateTime = "";
      if (this.filter.updateTime) {
        UpdateTime = this.filter.updateTime[0].split(" ")[0];

        if (this.filter.updateTime.length > 1) {
          UpdateTime += "|" + this.filter.updateTime[1].split(" ")[0];
        }
      }
      var EndTime = "";
      if (this.filter.endTime) {
        EndTime = this.filter.endTime[0].split(" ")[0];

        if (this.filter.endTime.length > 1) {
          EndTime += "|" + this.filter.endTime[1].split(" ")[0];
        }
      }
      if (UpdateTime.length < 10)
        UpdateTime = "";
      if (EndTime.length < 10)
        EndTime = "";

      this.filterUpdateTime = UpdateTime;
      this.filterEndTime = EndTime;

      this.categoryList = (
        await getcategoryList({ UpdateTime: UpdateTime, EndTime: EndTime, planningTypeDesc: this.filter.PlanningTypeDesc })
      ).data;
      this.initcategoryList();
      this.onSearch();
    },
    showHtml(e) {
      // this.allinfo = JSON.parse(e.srcElement.dataset.html);
      // this.endTime = e.srcElement.dataset.endtime;
      this.picUrls = []
      this.picUrls = e.picUrls;
      this.allinfo.data = e
      this.skuTable = [];
      this.propertiesTable = [];
      for (var i in this.allinfo.data.skus) {
        var xinfo = this.allinfo.data.skus[i];
        if (this.allinfo.data.skus[i].value == -1)
          this.skuTable.push({ key: xinfo.key, v: "请按出厂价提报" });
        else this.skuTable.push({ key: xinfo.key, v: xinfo.value });
      }
      // for (var i in this.allinfo.data.keyProperties) {
      //   var xinfo = this.allinfo.data.keyProperties[i];
      //   if (xinfo.key)
      //     this.propertiesTable.push({ key: xinfo.key, v: xinfo.value });
      // }

      this.htmlvisible = true;
    },

    showDetail(row) {
      this.allinfo = JSON.parse(row.html);
      this.endTime = row.endtime;
      this.picUrls = this.allinfo.data.picUrls;
      this.skuTable = [];
      this.propertiesTable = [];
      for (var i in this.allinfo.data.skus) {
        var xinfo = this.allinfo.data.skus[i];
        if (this.allinfo.data.skus[i].value == -1)
          this.skuTable.push({ key: xinfo.key, v: "请按出厂价提报" });
        else this.skuTable.push({ key: xinfo.key, v: xinfo.value });
      }
      for (var i in this.allinfo.data.keyProperties) {
        var xinfo = this.allinfo.data.keyProperties[i];
        if (xinfo.key)
          this.propertiesTable.push({ key: xinfo.key, v: xinfo.value });
      }

      this.htmlvisible = true;
    },
    onSearch(type) {
      this.queryList(type);
    },
    async queryList(type) {
      if (type == 'search') {
        this.pager.currentPage = 1
      }
      this.listLoading = true;
      var category = null
      if (this.categoryLeve1) {
        category = this.categoryLeve1
      }
      if (this.categoryLeve2) {
        category = this.categoryLeve2
      }
      if (this.categoryLeve3) {
        category = this.categoryLeve3
      }
      if (this.categoryLeve4) {
        category = this.categoryLeve4
      }
      if (this.categoryLeve5) {
        category = this.categoryLeve5
      }
      // var pager = this.$refs.pager.getPager();
      if (this.filter.planningId) {
        if (!/^\d+$/.test(this.filter.planningId)) {
          this.$message.error('企划ID请输入数值');
          return;
        }
      }
      var params = {
        ...this.pager,
        planningId: this.filter.planningId,
        planningTitle: this.filter.planningTitle,
        categoryValue: category
      };
      const replaceArr = ['planningTitle']
      params = replaceSpace(replaceArr, params)
      this.pageLoading = true
      const res = await getPageListAsync(params);
      this.pageLoading = false
      this.listLoading = false;
      if (!res?.success) return;
      const list = [];
      res.data.list.forEach((element) => {
        var str = element.planningTitle;
        var i = str.indexOf("】");
        if (i) {
          element.planningTitle1 = str.slice(0, i + 1);
          element.planningTitle2 = str.slice(i + 1);
        } else {
          element.planningTitle1 = str;
          element.planningTitle2 = "";
        }
      });

      this.listTree = res.data.list;
      this.total = res.data.total;
    },
    // 显示编辑界面
    Sizechange(val) {
      this.pager.currentPage = 1;
      this.pager.pageSize = val;
      this.queryList()
    },
    //当前页改变
    Pagechange(val) {
      this.pager.currentPage = val;
      this.queryList()
    },
    async onEditSubmit() {
      this.editLoading = true;
      const para = _.cloneDeep(this.editForm);
      para.parentId = para.parentIds.pop();
      if (para.id === para.parentId) {
        this.$message({
          message: "所属模块不能是自己！",
          type: "error",
        });
        this.editLoading = false;
        return;
      }
      const res = await addoredit(para);
      this.editLoading = false;
      if (!res?.success) {
        return;
      }
      this.$message({
        message: this.$t("admin.updateOk"),
        type: "success",
      });
      this.$refs["editForm"].resetFields();
      this.editFormVisible = false;
      this.getlist();
    },
    // 新增

    onSelectAll: function (selection) {
      const selections = treeToList(selection);
      const rows = treeToList(this.listTree);
      const checked = selections.length === rows.length;
      rows.forEach((row) => {
        this.$refs.multipleTable.toggleRowSelection(row, checked);
      });

      this.sels = this.$refs.multipleTable.selection;
    },
    onSelect: function (selection, row) {
      const checked = selection.some((s) => s.id === row.id);
      if (row.children && row.children.length > 0) {
        const rows = treeToList(row.children);
        rows.forEach((row) => {
          this.$refs.multipleTable.toggleRowSelection(row, checked);
        });
      }

      this.sels = this.$refs.multipleTable.selection;
    },
  },
};
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}

.small-img {
  width: 100px;
  height: 100px;
}

.big-img {
  width: 200px;
  height: 200px;
}

ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.table {
  display: table;
  border-collapse: collapse;
  /* border: 1px solid #ccc; */
}

.table-caption {
  display: table-caption;
  margin: 0;
  padding-top: 10px;
  font-size: 16px;
}

.table-column-group {
  display: table-column-group;
}

.table-column {
  display: table-column;
  width: 20%;
  min-width: 150px;
}

.table-row-group {
  display: table-row-group;
}

.table-row {
  display: table-row;
}

.table-row-group .table-row:hover,
.table-footer-group .table-row:hover {
  background: #f6f6f6;
}

.table-cell {
  display: table-cell;
  padding: 0 5px;
  border: 1px solid #ccc;
}

.table-header-group {
  display: table-header-group;
  background: #eee;
  font-weight: bold;
}

.table-footer-group {
  display: table-footer-group;
}

.info-container {
  display: flex;
  justify-content: space-between;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 5px;
}

.info-item .label {
  text-align: left;
  width: 80px;
  white-space: nowrap;
  color: #b4bebb;
}

.info-item .value {
  text-align: left;
}
</style>
