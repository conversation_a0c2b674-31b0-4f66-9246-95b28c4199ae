<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.payStartTime" :endDate.sync="ListInfo.payEndTime" class="publicCss"
                    startPlaceholder="付款开始时间" endPlaceholder="付款结束时间" :valueFormat="'yyyy-MM-dd HH:mm:ss'"
                    type="datetimerange" :clearable="false" />
                <dateRange :startDate.sync="ListInfo.optStartTime" :endDate.sync="ListInfo.optEndTime" class="publicCss"
                    startPlaceholder="操作开始时间" endPlaceholder="操作结束时间" type="datetimerange"
                    :valueFormat="'yyyy-MM-dd HH:mm:ss'" />
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.orderNoInner" v-model="ListInfo.orderNoInner"
                    placeholder="内部订单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300"
                    :maxlength="5000" @callback="orderNoInnerCallback" title="内部订单号"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <el-input v-model.trim="ListInfo.title" placeholder="标题" maxlength="50" clearable class="publicCss" />
                <el-select v-model="ListInfo.platform" placeholder="平台" class="publicCss" clearable
                    @change="searchShopList">
                    <el-option v-for="item in platformlist" :label="item.label" :value="item.value" :key="item.value" />
                </el-select>
                <el-select v-model="ListInfo.shopName" placeholder="店铺" class="publicCss" clearable filterable>
                    <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                        :value="item.shopName" />
                </el-select>
                <el-select filterable v-model="ListInfo.groupIds" collapse-tags clearable placeholder="运营组"
                    class="publicCss" multiple>
                    <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                    <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.proCode" v-model="ListInfo.proCode"
                    placeholder="产品ID/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300"
                    :maxlength="5000" @callback="proCodeCallback" title="产品ID" style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <number-range class="publicCss" :min.sync="ListInfo.payAmountMin" :max.sync="ListInfo.payAmountMax"
                    min-label="支付金额最小值" max-label="支付金额最大值" />
                <number-range class="publicCss" :min.sync="ListInfo.costPriceMin" :max.sync="ListInfo.costPriceMax"
                    min-label="商品成本最小值" max-label="商品成本最大值" />
                <number-range class="publicCss" :min.sync="ListInfo.profit1Min" :max.sync="ListInfo.profit1Max"
                    min-label="利润金额最小值" max-label="利润金额最大值" />
                <el-input v-model.trim="ListInfo.optUserName" placeholder="操作人" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable>
                    <el-option v-for="item in statusList" :label="item.label" :value="item.value" :key="item.value" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <!-- <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button> -->
                    <el-button type="primary" @click="batchOperate">批量操作</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            @select="selectProps" :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;"
            height="100%" :showsummary="data.summary ? true : false" :summaryarry="data.summary"
            @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="批量操作" :visible.sync="batchOperateVisible" width="20%" v-dialogDrag>
            <div style="display: flex;justify-content: center;">
                <el-select v-model="batchOperateInfo.status" placeholder="状态" class="publicCss" clearable>
                    <el-option v-for="item in statusList.filter(item => item.value != 0)" :label="item.label"
                        :value="item.value" :key="item.value" />
                </el-select>
            </div>
            <div class="btnGroup">
                <el-button @click="batchOperateVisible = false">取消</el-button>
                <el-button type="primary" @click="submit">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
import { getList as getShopList, getDirectorGroupList } from '@/api/operatemanage/base/shop'
const api = '/api/verifyOrder/SalePriceAnalysis/Opt/'
import { getUserInfo } from "@/api/operatemanage/productalllink/alllink";
import { log } from "mathjs";
const statusList = [
    {
        label: '同意',
        value: 1
    },
    {
        label: '拒绝',
        value: 2
    },
    {
        label: '未操作',
        value: 0
    },
]
const dictionary = ['10005', '10113', '0', '10009', '10005', '41878', '41878', '10009', '41071']
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan
    },
    props: {
        styleCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            dictionary,
            batchOperateVisible: false,
            shopList: [],
            statusList,
            api,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'payTime',
                isAsc: false,
                styleCode: this.styleCode,
                summarys: [],
                payStartTime: dayjs().format('YYYY-MM-DD 00:00:00'),
                payEndTime: dayjs().format('YYYY-MM-DD 23:59:59'),
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            grouplist: [],
            ids: [],
            batchOperateInfo: {
                ids: [],
                status: null
            }
        }
    },
    created() {
        this.getUserInfoAsync()
    },
    async mounted() {
        this.getGroupList()
        await this.getCol();
        await this.getList()
    },
    methods: {
        async getUserInfoAsync() {
            const { data } = await getUserInfo();
            if (this.dictionary.includes(data.id)) {
                this.ListInfo.groupIds = [0]
                console.log(this.ListInfo.groupIds, 'this.ListInfo.groupIds');
            }
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async submit() {
            if (this.batchOperateInfo.status === null) return this.$message.error('请选择状态')
            const { success } = await request.post(`${this.api}Operate`, this.batchOperateInfo)
            if (success) {
                this.$message.success('操作成功')
                this.getList()
                this.batchOperateInfo.ids = []
                this.batchOperateVisible = false
            } else {
                this.$message.error('操作失败')
            }
        },
        batchOperate() {
            if (this.batchOperateInfo.ids.length == 0) return this.$message.error('您还没选择任何数据')
            this.batchOperateInfo.status = null
            this.batchOperateVisible = true
        },
        selectProps(rows) {
            this.batchOperateInfo.ids = rows.map(item => item.id)
        },
        async getGroupList() {
            var { data } = await getDirectorGroupList();
            this.grouplist = data.map(item => { return { value: item.key, label: item.value }; });
        },
        async searchShopList(platform) {
            const { data: { list, }, success } = await getShopList({ platform, pageSize: 1000, currentPage: 1 })
            if (success) {

                this.$set(this, 'shopList', list)
            }
        },
        orderNoInnerCallback(val) {
            this.ListInfo.orderNoInner = val
        },
        proCodeCallback(val) {
            this.ListInfo.proCode = val
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                data.unshift({
                    label: '',
                    type: 'checkbox',
                })
                data.forEach(item => {
                    if (item.prop == 'orderNoInner') {
                        item.type = 'orderLogInfo'
                        item.orderType = 'orderNoInner'
                    }
                    if (item.prop == 'proCode') {
                        item.type = 'html'
                        item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
                    }
                })
                this.tableCols = data;
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (!this.ListInfo.payStartTime || !this.ListInfo.payEndTime) return this.$message.error('请选择付款时间')
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;

    .publicCss {
        width: 200px;
        margin: 0 10px 10px 0;
    }
}

.btnGroup {
    margin-top: 10px;
    display: flex;
    justify-content: center;
}
</style>
