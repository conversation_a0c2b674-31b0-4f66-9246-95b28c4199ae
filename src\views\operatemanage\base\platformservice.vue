<template>
       <el-table :data="list" style="width: 100%">
         <el-table-column prop="platform" label="平台" width="100">
            <template slot-scope="scope">
               <span>{{formatPlatform(scope.row['platform'])}}</span>
            </template>
         </el-table-column>
         <el-table-column prop="serviceFeeRate" label="服务费率(%)" width="200">
            <template slot-scope="scope">
              <span v-if="!scope.row['showupdate']">{{scope.row['serviceFeeRate']}}</span>
              <el-input-number v-else v-model="scope.row['serviceFeeRate']" :precision="2" :step="0.1" :max="10" placeholder="服务费率(%)"></el-input-number> 
            </template>
         </el-table-column>
         <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button v-if="!scope.row['showupdate']" @click="onEdit(scope.row)" type="text" size="small">编辑</el-button>
              <el-button v-else @click="handleUpdate(scope.row)" type="text" size="small">更新</el-button>
            </template>
        </el-table-column>
  </el-table>
</template>

<script>
import { formatPlatform,formatYesornoBool,platformlist} from "@/utils/tools";
import { getPlatformList, updatePlatformService} from '@/api/operatemanage/base/category'
 
export default {
  name: 'Api',
  components: { },
  data() {
    return {
      formatPlatform:formatPlatform,
      filter: { platform: 1 },
      list: [],
      total: 0,
      listLoading: false,
      pageLoading: false, 
    }
  },
  mounted() {
    this.onSearch()
  },
  methods: {
    onSearch() {
      this.getlist()
    },
    async getlist() {
      this.listLoading = true
      const res = await getPlatformList(this.filter)
      this.listLoading = false
      if (!res?.success) return      
      const list=[];
      res.data.forEach(f=>{
         f.label=f.categoryName;
         f.showupdate=false
         list.push(f)
      })
      this.list = list
    },
    // 显示编辑界面
    async onEdit(row) {
        row.showupdate=true
    },
   async handleUpdate(row) {
        const res = await updatePlatformService(row)
        if (res.data) {
          this.$message({ message: '更新成功！',type: 'success'})
          row.showupdate = false
        }
    }
  }
}
</script>
