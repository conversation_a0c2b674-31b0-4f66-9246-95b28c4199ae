<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 90%;" @tab-click="tabClick">
            <el-tab-pane label="采购明细" name="first" style="height: 100%;">
                <purchaseDetails :buyNo="buyNo" ref="purchaseDetails" />
            </el-tab-pane>
            <el-tab-pane label="入库&退货" name="second" style="height: 100%;">
                <InboundOrReturn :buyNo="buyNo" ref="InboundOrReturn" />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import purchaseDetails from "./components/purchaseDetails.vue";
import InboundOrReturn from "./components/InboundOrReturn.vue";
export default {
    components: {
        purchaseDetails, MyContainer, InboundOrReturn
    },
    props: {
        buyNo: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            activeName: 'first'
        };
    },
    mounted() {
    },
    methods: {
        tabClick(e) {
            if (e == 'first') {
                this.$refs.purchaseDetails.getList()
            } else {
                this.$refs.InboundOrReturn.getList()
            }
        }
    }
};
</script>

<style lang="scss" scoped></style>