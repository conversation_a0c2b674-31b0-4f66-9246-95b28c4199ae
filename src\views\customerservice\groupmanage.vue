<template>
  <div class="ces-main">
    <el-table :data="cusgroups">
      <el-table-column type="index" width="60"></el-table-column>
      <el-table-column prop="groupname"  label="组名" width="120" />
      

       <template slot-scope="scope">
        <el-button @click="handleDelete(scope.row)" type="text" size="small">删除</el-button>
         
      </template>


      </el-table>

    
  </div>
</template>
 
        
    
<script>
import { getcusgroups,addcusgroup } from '@/api/customerservice/customergroup';
export default {
  data() {
    return {
      that: this,
      cusgroups:[],

      loading: false,
      productid:'',
      total:0,
pageSize:5,
      pager: {
        total: 0,
        pageIndex: 1,
        pageSize: 5,
        OrderBy: "traindate",
        isAsc:false,
        Trainstatus: 2,
      },
      traindata: [],
    };
  },
  components: {},
  computed: {},
    async mounted() {

        alert(1)
   
    var g=  await getcusgroups({});
    
    this.cusgroups=g.data.list.map(function(item){
      var ob=new Object();
      ob.groupname=item;
      ob.isshow=false;
      ob.selectedicon="";
      ob.selecttype="fail";

      return ob;


    });

  },
  methods: {
     clear(){
  this.traindata=[];

    },
    changePage(e) {

     var that = this;
      that.traindata = [];
      

      const params = {
         
        ...this.pager,
      };
      params.productid = that.productid;
      this.pager.pageIndex=e;

      gethistrainplandata(params).then((res) => {
        that.traindata = res.data.list;
      });



    },
    init(productid) {
      var that = this;
      this.pager.pageIndex=0;
      that.traindata = [];
      // var ProductID=this.$router.query.

      
    

      const params = {
         
        ...this.pager,
      };
      params.productid = productid;

      gethistrainplandata(params).then((res) => {
        that.traindata = res.data.list;
        that.pager.total=res.data.total;
        that.total=res.data.total;
        
        that.$forceUpdate()
      });
    },
  },
  mounted() {
    this.init();
  },
};
</script>