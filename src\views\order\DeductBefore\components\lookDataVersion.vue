<template>
    <MyContainer v-loading="loading">
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTakeTime" :endDate.sync="ListInfo.endTakeTime"
                    class="publicCss" :clearable="false" />
                <chooseWareHouse v-model="ListInfo.wmsIds" class="publicCss" :filter="sendWmsesFilter"
                    style="width: 220px;" multiple />
                <div>
                    <el-button type="primary" @click="search">搜索</el-button>
                    <el-button type="primary" @click="openSet">设置</el-button>
                </div>
            </div>
        </template>
        <div class="_content">
            <div class="_content_top">
                <div class="title">外仓订单</div>
                <div class="_content_main" v-if="tableData?.length > 0">
                    <template v-for="item in tableData">
                        <div>
                            <el-card class="_content_item" style="width: 480px">
                                <template slot="header">
                                    <div class="_content_item_title">{{ item.wmsName }}</div>
                                </template>
                                <el-descriptions class="margin-top" :column="3" :size="size" border>
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 总订单 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.totalCount == 0 || item.totalCount == null ? '#000' : '#409eff',
                                            cursor: item.totalCount == 0 || item.totalCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'totalCount', 'outWms_')">{{
                                            item.totalCount }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 第三方发货 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.otherSendCount == 0 || item.otherSendCount == null ? '#000' : '#409eff',
                                            cursor: item.otherSendCount == 0 || item.otherSendCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'otherSendCount', 'outWms_')">{{
                                            item.otherSendCount
                                            }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 取消订单 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.cancelCount == 0 || item.cancelCount == null ? '#000' : '#409eff',
                                            cursor: item.cancelCount == 0 || item.cancelCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'cancelCount', 'outWms_')">{{
                                            item.cancelCount
                                            }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item :span="3" labelClassName="item-group-label1"
                                        contentClassName="item-group-content" label="待审核" />
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 订单量 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.noVerifyCount == 0 || item.noVerifyCount == null ? '#000' : '#409eff',
                                            cursor: item.noVerifyCount == 0 || item.noVerifyCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'noVerifyCount', 'outWms_')"> {{
                                            item.noVerifyCount
                                            }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 没计划发货时间 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.verifyPlanSendTimeCount == 0 || item.verifyPlanSendTimeCount == null ? '#000' : '#409eff',
                                            cursor: item.verifyPlanSendTimeCount == 0 || item.verifyPlanSendTimeCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'verifyPlanSendTimeCount', 'outWms_')"> {{
                                            item.verifyPlanSendTimeCount }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 平均用时 </template>
                                        <span :title="formatMinutes(item.verifyTimeAvg).toolTip"> {{
                                            formatMinutes(item.verifyTimeAvg).str
                                        }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item :span="3" labelClassName="item-group-label1"
                                        contentClassName="item-group-content" label="待发货订单">
                                    </el-descriptions-item>
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 订单量 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.unSendCount == 0 || item.unSendCount == null ? '#000' : '#409eff',
                                            cursor: item.unSendCount == 0 || item.unSendCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'unSendCount', 'outWms_')"> {{
                                            item.unSendCount
                                            }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 正常用时 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.normalUnSendCount == 0 || item.normalUnSendCount == null ? '#000' : '#409eff',
                                            cursor: item.normalUnSendCount == 0 || item.normalUnSendCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'normalUnSendCount', 'outWms_')">{{
                                            item.normalUnSendCount }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 即将超时 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.willOverTimeUnSendCount == 0 || item.willOverTimeUnSendCount == null ? '#000' : '#409eff',
                                            cursor: item.willOverTimeUnSendCount == 0 || item.willOverTimeUnSendCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'willOverTimeUnSendCount', 'outWms_')">{{
                                            item.willOverTimeUnSendCount }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 超时未完成 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.overTimeUnSendCount == 0 || item.overTimeUnSendCount == null ? '#000' : '#409eff',
                                            cursor: item.overTimeUnSendCount == 0 || item.overTimeUnSendCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'overTimeUnSendCount', 'outWms_')">{{
                                            item.overTimeUnSendCount
                                            }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 超时发货 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.overTimeSendCount == 0 || item.overTimeSendCount == null ? '#000' : '#409eff',
                                            cursor: item.overTimeSendCount == 0 || item.overTimeSendCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'overTimeSendCount', 'outWms_')">
                                            {{
                                                item.overTimeSendCount
                                            }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 平均用时 </template>
                                        <span :title="formatMinutes(item.sendTimeAvg).toolTip"> {{
                                            formatMinutes(item.sendTimeAvg).str
                                            }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item :span="3" labelClassName="item-group-label1"
                                        contentClassName="item-group-content" label="待揽收">
                                    </el-descriptions-item>
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 订单量 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.unCollectCount == 0 || item.unCollectCount == null ? '#000' : '#409eff',
                                            cursor: item.unCollectCount == 0 || item.unCollectCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'unCollectCount', 'outWms_')"> {{
                                            item.unCollectCount
                                            }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <!-- <template slot="label"> 正常用时 </template>
                                        <span class="discriptions_content"
                                            :style="{color:item.normalUnCollectCount == 0 || item.normalUnCollectCount == null ? '#000' : '#409eff',
                                            cursor: item.color == 0 || item.color == null ? 'auto' : 'pointer'}" @click="openLog(item, 'normalUnCollectCount', 'outWms_')">{{
                                                item.normalUnCollectCount
                                            }}</span> -->
                                        <template slot="label"> 正常用时 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.normalUnCollectCount == 0 || item.normalUnCollectCount == null ? '#000' : '#409eff',
                                            cursor: item.normalUnCollectCount == 0 || item.normalUnCollectCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'normalUnCollectCount', 'outWms_')"> {{
                                            item.normalUnCollectCount
                                            }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 即将超时 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.willOverTimeUnCollectCount == 0 || item.willOverTimeUnCollectCount == null ? '#000' : '#409eff',
                                            cursor: item.willOverTimeUnCollectCount == 0 || item.willOverTimeUnCollectCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'willOverTimeUnCollectCount', 'outWms_')">{{
                                            item.willOverTimeUnCollectCount
                                            }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 超时未完成 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.overTimeUnCollectCount == 0 || item.overTimeUnCollectCount == null ? '#000' : '#409eff',
                                            cursor: item.overTimeUnCollectCount == 0 || item.overTimeUnCollectCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'overTimeUnCollectCount', 'outWms_')"> {{
                                            item.overTimeUnCollectCount }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item labelClassName="item-group-label" label="平均用时" :span="2"><span
                                            :title="formatMinutes(item.collectTimeTimeAvg).toolTip">{{
                                                formatMinutes(item.collectTimeTimeAvg).str
                                            }}</span> </el-descriptions-item>
                                    <el-descriptions-item :span="3" labelClassName="item-group-label1"
                                        contentClassName="item-group-content" label="已揽收" />
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 订单量 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.collectCount == 0 || item.collectCount == null ? '#000' : '#409eff',
                                            cursor: item.collectCount == 0 || item.collectCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'collectCount', 'outWms_')"> {{
                                            item.collectCount
                                            }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 未超时揽收 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.unOverTimeCollectCount == 0 || item.unOverTimeCollectCount == null ? '#000' : '#409eff',
                                            cursor: item.unOverTimeCollectCount == 0 || item.unOverTimeCollectCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'unOverTimeCollectCount', 'outWms_')">{{
                                            item.unOverTimeCollectCount }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 超时揽收 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.overTimeCollectCount == 0 || item.overTimeCollectCount == null ? '#000' : '#409eff',
                                            cursor: item.overTimeCollectCount == 0 || item.overTimeCollectCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'overTimeCollectCount', 'outWms_')">{{
                                            item.overTimeCollectCount
                                            }}</span>
                                    </el-descriptions-item>

                                    <el-descriptions-item labelClassName="item-group-label">
                                        <template slot="label"> 已签收订单 </template>
                                        <span class="discriptions_content" :style="{
                                            color: item.signCount == 0 || item.signCount == null ? '#000' : '#409eff',
                                            cursor: item.signCount == 0 || item.signCount == null ? 'auto' : 'pointer'
                                        }" @click="openLog(item, 'signCount', 'outWms_')"> {{
                                            item.signCount }}</span>
                                    </el-descriptions-item>
                                </el-descriptions>
                            </el-card>
                        </div>
                    </template>
                </div>
                <div class="_content_main" v-else>
                    暂无数据</div>
            </div>
            <div class="_content_bottom">
                <div class="title">内仓订单</div>
                <div class="_content_main1" v-if="tableData1?.length > 0">
                    <div v-for="item in tableData1">
                        <div style="margin-bottom: 10px">
                            <div style="display: flex; gap: 10px">
                                <el-card style="flex: 2">
                                    <template slot="header">
                                        <div class="_content_item_title">{{ item.wmsName }}</div>
                                    </template>
                                    <div>
                                        <el-descriptions title="" :column="1" border>
                                            <el-descriptions-item labelClassName="item-group-label" label="总订单"><span
                                                    :style="{
                                                        color: item.totalCount == 0 || item.totalCount == null ? '#000' : '#409eff',
                                                        cursor: item.totalCount == 0 || item.totalCount == null ? 'auto' : 'pointer'
                                                    }" @click="openLog(item, 'totalCount', 'inWms_')"
                                                    class="discriptions_content">
                                                    {{
                                                        item.totalCount }}</span> </el-descriptions-item>
                                            <el-descriptions-item labelClassName="item-group-label" label="取消单"><span
                                                    :style="{
                                                        color: item.cancelCount == 0 || item.cancelCount == null ? '#000' : '#409eff',
                                                        cursor: item.cancelCount == 0 || item.cancelCount == null ? 'auto' : 'pointer'
                                                    }" @click="openLog(item, 'cancelCount', 'inWms_')"
                                                    class="discriptions_content">{{
                                                        item.cancelCount
                                                    }}</span> </el-descriptions-item>
                                            <el-descriptions-item labelClassName="item-group-label" label="第三方发货"><span
                                                    :style="{
                                                        color: item.otherSendCount == 0 || item.otherSendCount == null ? '#000' : '#409eff',
                                                        cursor: item.otherSendCount == 0 || item.otherSendCount == null ? 'auto' : 'pointer'
                                                    }" @click="openLog(item, 'otherSendCount', 'inWms_')"
                                                    class="discriptions_content">{{
                                                        item.otherSendCount }}</span>
                                            </el-descriptions-item>
                                            <el-descriptions-item labelClassName="item-group-label" label="已签收订单数"><span
                                                    :style="{
                                                        color: item.signCount == 0 || item.signCount == null ? '#000' : '#409eff',
                                                        cursor: item.signCount == 0 || item.signCount == null ? 'auto' : 'pointer'
                                                    }" @click="openLog(item, 'signCount', 'inWms_')"
                                                    class="discriptions_content">{{
                                                        item.signCount
                                                    }}</span> </el-descriptions-item>
                                            <el-descriptions-item labelClassName="item-group-label" label="组团订单"><span
                                                    :style="{
                                                        color: item.teamCount == 0 || item.teamCount == null ? '#000' : '#409eff',
                                                        cursor: item.teamCount == 0 || item.teamCount == null ? 'auto' : 'pointer'
                                                    }" @click="openLog(item, 'teamCount', 'inWms_')"
                                                    class="discriptions_content">{{
                                                        item.teamCount
                                                    }}</span> </el-descriptions-item>
                                            <el-descriptions-item labelClassName="item-group-label" label="杂单订单"><span
                                                    :style="{
                                                        color: item.singleCount == 0 || item.singleCount == null ? '#000' : '#409eff',
                                                        cursor: item.singleCount == 0 || item.singleCount == null ? 'auto' : 'pointer'
                                                    }" @click="openLog(item, 'singleCount', 'inWms_')"
                                                    class="discriptions_content">{{
                                                        item.singleCount
                                                    }}</span> </el-descriptions-item>
                                            <el-descriptions-item labelClassName="item-group-label" label="未审核订单"><span
                                                    :style="{
                                                        color: item.noVerifyCount == 0 || item.noVerifyCount == null ? '#000' : '#409eff',
                                                        cursor: item.noVerifyCount == 0 || item.noVerifyCount == null ? 'auto' : 'pointer'
                                                    }" @click="openLog(item, 'noVerifyCount', 'inWms_')"
                                                    class="discriptions_content">{{
                                                        item.noVerifyCount
                                                    }}</span> </el-descriptions-item>
                                            <el-descriptions-item labelClassName="item-group-label"
                                                label="无计划发货时间"><span :style="{
                                                    color: item.verifyPlanSendTimeCount == 0 || item.verifyPlanSendTimeCount == null ? '#000' : '#409eff',
                                                    cursor: item.verifyPlanSendTimeCount == 0 || item.verifyPlanSendTimeCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'verifyPlanSendTimeCount', 'inWms_')"
                                                    class="discriptions_content">{{
                                                        item.verifyPlanSendTimeCount }}</span>
                                            </el-descriptions-item>
                                            <el-descriptions-item labelClassName="item-group-label" label="审核平均用时"><span
                                                    style="color: #000;cursor: auto;"
                                                    :title="formatMinutes(item.verifyTimeAvg).toolTip">
                                                    <!--:style="{  color: item.verifyTimeAvg == 0 || item.verifyTimeAvg == null ? '#000' : '#409eff',
                                                      cursor: item.verifyTimeAvg == 0 || item.verifyTimeAvg == null ? 'auto' : 'pointer'
                                                    }" @click="openLog(item, 'verifyTimeAvg', 'inWms_')" -->
                                                    {{
                                                        formatMinutes(item.verifyTimeAvg).str
                                                    }}</span></el-descriptions-item>
                                        </el-descriptions>
                                    </div>
                                </el-card>
                                <el-card style="flex: 2">
                                    <template slot="header">
                                        <div class="_content_item_title">待生成批次</div>
                                    </template>

                                    <el-descriptions title="" :column="1" border>
                                        <el-descriptions-item labelClassName="item-group-label" label="订单量"><span
                                                :style="{
                                                    color: item.unPickGenCount == 0 || item.unPickGenCount == null ? '#000' : '#409eff',
                                                    cursor: item.unPickGenCount == 0 || item.unPickGenCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'unPickGenCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.unPickGenCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="正常用时"><span
                                                :style="{
                                                    color: item.normalUnPickGenCount == 0 || item.normalUnPickGenCount == null ? '#000' : '#409eff',
                                                    cursor: item.normalUnPickGenCount == 0 || item.normalUnPickGenCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'normalUnPickGenCount', 'inWms_')"
                                                class="discriptions_content"> {{
                                                    item.normalUnPickGenCount
                                                }}</span></el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="即将超时"><span
                                                :style="{
                                                    color: item.willOverTimeUnPickGenCount == 0 || item.willOverTimeUnPickGenCount == null ? '#000' : '#409eff',
                                                    cursor: item.willOverTimeUnPickGenCount == 0 || item.willOverTimeUnPickGenCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'willOverTimeUnPickGenCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.willOverTimeUnPickGenCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="超时未完成"><span
                                                :style="{
                                                    color: item.overTimeUnPickGenCount == 0 || item.overTimeUnPickGenCount == null ? '#000' : '#409eff',
                                                    cursor: item.overTimeUnPickGenCount == 0 || item.overTimeUnPickGenCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'overTimeUnPickGenCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.overTimeUnPickGenCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="平均时长"><span
                                                :title="formatMinutes(item.pickGenTimeAvg).toolTip">{{
                                                    formatMinutes(item.pickGenTimeAvg).str
                                                }}</span> </el-descriptions-item>
                                    </el-descriptions>

                                    <el-descriptions title="N元M件" :column="1" border>
                                        <el-descriptions-item labelClassName="item-group-label" label="订单量"><span
                                                :style="{
                                                    color: item.nmUnPickGenCount == 0 || item.nmUnPickGenCount == null ? '#000' : '#409eff',
                                                    cursor: item.nmUnPickGenCount == 0 || item.nmUnPickGenCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'nmUnPickGenCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.nmUnPickGenCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="正常用时"><span
                                                :style="{
                                                    color: item.nmNormalUnPickGenCount == 0 || item.nmNormalUnPickGenCount == null ? '#000' : '#409eff',
                                                    cursor: item.nmNormalUnPickGenCount == 0 || item.nmNormalUnPickGenCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'nmNormalUnPickGenCount', 'inWms_')"
                                                class="discriptions_content"> {{
                                                    item.nmNormalUnPickGenCount
                                                }}</span></el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="即将超时"><span
                                                :style="{
                                                    color: item.nmWillOverTimeUnPickGenCount == 0 || item.nmWillOverTimeUnPickGenCount == null ? '#000' : '#409eff',
                                                    cursor: item.nmWillOverTimeUnPickGenCount == 0 || item.nmWillOverTimeUnPickGenCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'nmWillOverTimeUnPickGenCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.nmWillOverTimeUnPickGenCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="超时未完成"><span
                                                :style="{
                                                    color: item.nmOverTimeUnPickGenCount == 0 || item.nmOverTimeUnPickGenCount == null ? '#000' : '#409eff',
                                                    cursor: item.nmOverTimeUnPickGenCount == 0 || item.nmOverTimeUnPickGenCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'nmOverTimeUnPickGenCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.nmOverTimeUnPickGenCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="平均时长"><span
                                                :title="formatMinutes(item.nmPickGenTimeAvg).toolTip">{{
                                                    formatMinutes(item.nmPickGenTimeAvg).str
                                                }}</span> </el-descriptions-item>
                                    </el-descriptions>

                                </el-card>
                                <el-card style="flex: 3">
                                    <template slot="header">
                                        <div class="_content_item_title">待领取拣货任务</div>
                                    </template>

                                    <el-descriptions title="组团" :column="2" border>
                                        <el-descriptions-item labelClassName="item-group-label" label="订单量"><span
                                                :style="{
                                                    color: item.teamUnPickCount == 0 || item.teamUnPickCount == null ? '#000' : '#409eff',
                                                    cursor: item.teamUnPickCount == 0 || item.teamUnPickCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'teamUnPickCount', 'inWms_')"
                                                class="discriptions_content">
                                                {{
                                                    item.teamUnPickCount
                                                }}</span></el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="正常用时"><span
                                                :style="{
                                                    color: item.teamNormalUnPickCount == 0 || item.teamNormalUnPickCount == null ? '#000' : '#409eff',
                                                    cursor: item.teamNormalUnPickCount == 0 || item.teamNormalUnPickCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'teamNormalUnPickCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.teamNormalUnPickCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="即将超时"><span
                                                :style="{
                                                    color: item.teamWillOverTimeUnPickCount == 0 || item.teamWillOverTimeUnPickCount == null ? '#000' : '#409eff',
                                                    cursor: item.teamWillOverTimeUnPickCount == 0 || item.teamWillOverTimeUnPickCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'teamWillOverTimeUnPickCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.teamWillOverTimeUnPickCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="超时未完成"><span
                                                :style="{
                                                    color: item.teamOverTimeUnPickCount == 0 || item.teamOverTimeUnPickCount == null ? '#000' : '#409eff',
                                                    cursor: item.teamOverTimeUnPickCount == 0 || item.teamOverTimeUnPickCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'teamOverTimeUnPickCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.teamOverTimeUnPickCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="平均时长"><span
                                                :title="formatMinutes(item.teamPickTimeAvg).toolTip">{{
                                                    formatMinutes(item.teamPickTimeAvg).str
                                                }}</span> </el-descriptions-item>
                                    </el-descriptions>

                                    <el-descriptions title="杂单" :column="2" border>
                                        <el-descriptions-item labelClassName="item-group-label" label="订单量"><span
                                                :style="{
                                                    color: item.singleUnPickCount == 0 || item.singleUnPickCount == null ? '#000' : '#409eff',
                                                    cursor: item.singleUnPickCount == 0 || item.singleUnPickCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'singleUnPickCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.singleUnPickCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="正常用时"><span
                                                :style="{
                                                    color: item.singleNormalUnPickCount == 0 || item.singleNormalUnPickCount == null ? '#000' : '#409eff',
                                                    cursor: item.singleNormalUnPickCount == 0 || item.singleNormalUnPickCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'singleNormalUnPickCount', 'inWms_')"
                                                class="discriptions_content"> {{
                                                    item.singleNormalUnPickCount
                                                }}</span></el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="即将超时"><span
                                                :style="{
                                                    color: item.singleWillOverTimeUnPickCount == 0 || item.singleWillOverTimeUnPickCount == null ? '#000' : '#409eff',
                                                    cursor: item.singleWillOverTimeUnPickCount == 0 || item.singleWillOverTimeUnPickCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'singleWillOverTimeUnPickCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.singleWillOverTimeUnPickCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="超时未完成"><span
                                                :style="{
                                                    color: item.singleOverTimeUnPickCount == 0 || item.singleOverTimeUnPickCount == null ? '#000' : '#409eff',
                                                    cursor: item.singleOverTimeUnPickCount == 0 || item.singleOverTimeUnPickCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'singleOverTimeUnPickCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.singleOverTimeUnPickCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="平均时长"><span
                                                :title="formatMinutes(item.singlePickTimeAvg).toolTip">{{
                                                    formatMinutes(item.singlePickTimeAvg).str
                                                }}</span> </el-descriptions-item>
                                    </el-descriptions>

                                    <el-descriptions title="N元M件" :column="2" border>
                                        <el-descriptions-item labelClassName="item-group-label" label="订单量"><span
                                                :style="{
                                                    color: item.nmUnPickCount == 0 || item.nmUnPickCount == null ? '#000' : '#409eff',
                                                    cursor: item.nmUnPickCount == 0 || item.nmUnPickCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'nmUnPickCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.nmUnPickCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="正常用时"><span
                                                :style="{
                                                    color: item.nmNormalUnPickCount == 0 || item.nmNormalUnPickCount == null ? '#000' : '#409eff',
                                                    cursor: item.nmNormalUnPickCount == 0 || item.nmNormalUnPickCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'nmNormalUnPickCount', 'inWms_')"
                                                class="discriptions_content"> {{
                                                    item.nmNormalUnPickCount
                                                }}</span></el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="即将超时"><span
                                                :style="{
                                                    color: item.nmWillOverTimeUnPickCount == 0 || item.nmWillOverTimeUnPickCount == null ? '#000' : '#409eff',
                                                    cursor: item.nmWillOverTimeUnPickCount == 0 || item.nmWillOverTimeUnPickCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'nmWillOverTimeUnPickCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.nmWillOverTimeUnPickCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="超时未完成"><span
                                                :style="{
                                                    color: item.nmOverTimeUnPickCount == 0 || item.nmOverTimeUnPickCount == null ? '#000' : '#409eff',
                                                    cursor: item.nmOverTimeUnPickCount == 0 || item.nmOverTimeUnPickCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'nmOverTimeUnPickCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.nmOverTimeUnPickCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="平均时长"><span
                                                :title="formatMinutes(item.nmPickTimeAvg).toolTip">{{
                                                    formatMinutes(item.nmPickTimeAvg).str
                                                }}</span> </el-descriptions-item>
                                    </el-descriptions>
                                </el-card>
                                <el-card style="flex: 3">
                                    <template slot="header">
                                        <div class="_content_item_title">拣货未完成</div>
                                    </template>

                                    <el-descriptions title="组团" :column="2" border>
                                        <el-descriptions-item labelClassName="item-group-label" label="订单量"><span
                                                :style="{
                                                    color: item.teamUnPickCptCount == 0 || item.teamUnPickCptCount == null ? '#000' : '#409eff',
                                                    cursor: item.teamUnPickCptCount == 0 || item.teamUnPickCptCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'teamUnPickCptCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.teamUnPickCptCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="正常用时"><span
                                                :style="{
                                                    color: item.teamNormalUnPickCptCount == 0 || item.teamNormalUnPickCptCount == null ? '#000' : '#409eff',
                                                    cursor: item.teamNormalUnPickCptCount == 0 || item.teamNormalUnPickCptCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'teamNormalUnPickCptCount', 'inWms_')"
                                                class="discriptions_content"> {{
                                                    item.teamNormalUnPickCptCount
                                                }}</span></el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="即将超时"><span
                                                :style="{
                                                    color: item.teamWillOverTimeUnPickCptCount == 0 || item.teamWillOverTimeUnPickCptCount == null ? '#000' : '#409eff',
                                                    cursor: item.teamWillOverTimeUnPickCptCount == 0 || item.teamWillOverTimeUnPickCptCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'teamWillOverTimeUnPickCptCount', 'inWms_')"
                                                class="discriptions_content"> {{
                                                    item.teamWillOverTimeUnPickCptCount
                                                }}</span></el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="超时未完成"><span
                                                :style="{
                                                    color: item.teamOverTimeUnPickCptCount == 0 || item.teamOverTimeUnPickCptCount == null ? '#000' : '#409eff',
                                                    cursor: item.teamOverTimeUnPickCptCount == 0 || item.teamOverTimeUnPickCptCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'teamOverTimeUnPickCptCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.teamOverTimeUnPickCptCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="平均时长"><span
                                                :title="formatMinutes(item.teamPickCptTimeAvg).toolTip">{{
                                                    formatMinutes(item.teamPickCptTimeAvg).str
                                                }}</span> </el-descriptions-item>
                                    </el-descriptions>

                                    <el-descriptions title="杂单" :column="2" border>
                                        <el-descriptions-item labelClassName="item-group-label" label="订单量"><span
                                                :style="{
                                                    color: item.singleUnPickCptCount == 0 || item.singleUnPickCptCount == null ? '#000' : '#409eff',
                                                    cursor: item.singleUnPickCptCount == 0 || item.singleUnPickCptCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'singleUnPickCptCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.singleUnPickCptCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="正常用时"><span
                                                :style="{
                                                    color: item.singleNormalUnPickCptCount == 0 || item.singleNormalUnPickCptCount == null ? '#000' : '#409eff',
                                                    cursor: item.singleNormalUnPickCptCount == 0 || item.singleNormalUnPickCptCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'singleNormalUnPickCptCount', 'inWms_')"
                                                class="discriptions_content">
                                                {{
                                                    item.singleNormalUnPickCptCount
                                                }}</span></el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="即将超时"><span
                                                :style="{
                                                    color: item.singleWillOverTimeUnPickCptCount == 0 || item.singleWillOverTimeUnPickCptCount == null ? '#000' : '#409eff',
                                                    cursor: item.singleWillOverTimeUnPickCptCount == 0 || item.singleWillOverTimeUnPickCptCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'singleWillOverTimeUnPickCptCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.singleWillOverTimeUnPickCptCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="超时未完成"><span
                                                :style="{
                                                    color: item.singleOverTimeUnPickCptCount == 0 || item.singleOverTimeUnPickCptCount == null ? '#000' : '#409eff',
                                                    cursor: item.singleOverTimeUnPickCptCount == 0 || item.singleOverTimeUnPickCptCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'singleOverTimeUnPickCptCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.singleOverTimeUnPickCptCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="平均时长"><span
                                                :title="formatMinutes(item.singlePickCptTimeAvg).toolTip"> {{
                                                    formatMinutes(item.singlePickCptTimeAvg).str
                                                }}</span></el-descriptions-item>
                                    </el-descriptions>

                                    <el-descriptions title="N元M件" :column="2" border>
                                        <el-descriptions-item labelClassName="item-group-label" label="订单量"><span
                                                :style="{
                                                    color: item.nmUnPickCptCount == 0 || item.nmUnPickCptCount == null ? '#000' : '#409eff',
                                                    cursor: item.nmUnPickCptCount == 0 || item.nmUnPickCptCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'nmUnPickCptCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.nmUnPickCptCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="正常用时"><span
                                                :style="{
                                                    color: item.nmNormalUnPickCptCount == 0 || item.nmNormalUnPickCptCount == null ? '#000' : '#409eff',
                                                    cursor: item.nmNormalUnPickCptCount == 0 || item.nmNormalUnPickCptCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'nmNormalUnPickCptCount', 'inWms_')"
                                                class="discriptions_content">
                                                {{
                                                    item.nmNormalUnPickCptCount
                                                }}</span></el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="即将超时"><span
                                                :style="{
                                                    color: item.nmWillOverTimeUnPickCptCount == 0 || item.nmWillOverTimeUnPickCptCount == null ? '#000' : '#409eff',
                                                    cursor: item.nmWillOverTimeUnPickCptCount == 0 || item.nmWillOverTimeUnPickCptCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'nmWillOverTimeUnPickCptCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.nmWillOverTimeUnPickCptCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="超时未完成"><span
                                                :style="{
                                                    color: item.nmOverTimeUnPickCptCount == 0 || item.nmOverTimeUnPickCptCount == null ? '#000' : '#409eff',
                                                    cursor: item.nmOverTimeUnPickCptCount == 0 || item.nmOverTimeUnPickCptCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'nmOverTimeUnPickCptCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.nmOverTimeUnPickCptCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="平均时长"><span
                                                :title="formatMinutes(item.nmPickCptTimeAvg).toolTip"> {{
                                                    formatMinutes(item.nmPickCptTimeAvg).str
                                                }}</span></el-descriptions-item>
                                    </el-descriptions>
                                </el-card>
                                <el-card style="flex: 3">
                                    <template slot="header">
                                        <div class="_content_item_title">待打包</div>
                                    </template>

                                    <el-descriptions title="组团" :column="2" border>
                                        <el-descriptions-item labelClassName="item-group-label" label="订单量"><span
                                                :style="{
                                                    color: item.teamUnPackCount == 0 || item.teamUnPackCount == null ? '#000' : '#409eff',
                                                    cursor: item.teamUnPackCount == 0 || item.teamUnPackCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'teamUnPackCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.teamUnPackCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="正常用时"><span
                                                :style="{
                                                    color: item.teamNormalUnPackCount == 0 || item.teamNormalUnPackCount == null ? '#000' : '#409eff',
                                                    cursor: item.teamNormalUnPackCount == 0 || item.teamNormalUnPackCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'teamNormalUnPackCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.teamNormalUnPackCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="即将超时"><span
                                                :style="{
                                                    color: item.teamWillOverTimeUnPackCount == 0 || item.teamWillOverTimeUnPackCount == null ? '#000' : '#409eff',
                                                    cursor: item.teamWillOverTimeUnPackCount == 0 || item.teamWillOverTimeUnPackCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'teamWillOverTimeUnPackCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.teamWillOverTimeUnPackCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="超时未完成"><span
                                                :style="{
                                                    color: item.teamOverTimeUnPackCount == 0 || item.teamOverTimeUnPackCount == null ? '#000' : '#409eff',
                                                    cursor: item.teamOverTimeUnPackCount == 0 || item.teamOverTimeUnPackCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'teamOverTimeUnPackCount', 'inWms_')"
                                                class="discriptions_content"> {{
                                                    item.teamOverTimeUnPackCount
                                                }}</span></el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="平均时长"><span
                                                :title="formatMinutes(item.teamPackTimeAvg).toolTip">{{
                                                    formatMinutes(item.teamPackTimeAvg).str
                                                }}</span> </el-descriptions-item>
                                    </el-descriptions>

                                    <el-descriptions title="杂单" :column="2" border>
                                        <el-descriptions-item labelClassName="item-group-label" label="订单量"><span
                                                :style="{
                                                    color: item.singleUnPackCount == 0 || item.singleUnPackCount == null ? '#000' : '#409eff',
                                                    cursor: item.singleUnPackCount == 0 || item.singleUnPackCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'singleUnPackCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.singleUnPackCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="正常用时"><span
                                                :style="{
                                                    color: item.singleNormalUnPackCount == 0 || item.singleNormalUnPackCount == null ? '#000' : '#409eff',
                                                    cursor: item.singleNormalUnPackCount == 0 || item.singleNormalUnPackCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'singleNormalUnPackCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.singleNormalUnPackCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="即将超时"><span
                                                :style="{
                                                    color: item.singleWillOverTimeUnPackCount == 0 || item.singleWillOverTimeUnPackCount == null ? '#000' : '#409eff',
                                                    cursor: item.singleWillOverTimeUnPackCount == 0 || item.singleWillOverTimeUnPackCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'singleWillOverTimeUnPackCount', 'inWms_')"
                                                class="discriptions_content">
                                                {{
                                                    item.singleWillOverTimeUnPackCount
                                                }}</span></el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="超时未完成"><span
                                                :style="{
                                                    color: item.singleOverTimeUnPackCount == 0 || item.singleOverTimeUnPackCount == null ? '#000' : '#409eff',
                                                    cursor: item.singleOverTimeUnPackCount == 0 || item.singleOverTimeUnPackCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'singleOverTimeUnPackCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.singleOverTimeUnPackCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="平均时长"><span
                                                :title="formatMinutes(item.singlePackTimeAvg).toolTip">{{
                                                    formatMinutes(item.singlePackTimeAvg).str
                                                }}</span> </el-descriptions-item>
                                    </el-descriptions>
                                </el-card>
                                <el-card style="flex: 3">
                                    <template slot="header">
                                        <div class="_content_item_title">揽收情况</div>
                                    </template>

                                    <el-descriptions title="待揽收" :column="2" border>
                                        <el-descriptions-item labelClassName="item-group-label" label="订单量"><span
                                                :style="{
                                                    color: item.unCollectCount == 0 || item.unCollectCount == null ? '#000' : '#409eff',
                                                    cursor: item.unCollectCount == 0 || item.unCollectCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'unCollectCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.unCollectCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="正常用时"><span
                                                :style="{
                                                    color: item.normalUnCollectCount == 0 || item.normalUnCollectCount == null ? '#000' : '#409eff',
                                                    cursor: item.normalUnCollectCount == 0 || item.normalUnCollectCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'normalUnCollectCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.normalUnCollectCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="即将超时"><span
                                                :style="{
                                                    color: item.willOverTimeUnCollectCount == 0 || item.willOverTimeUnCollectCount == null ? '#000' : '#409eff',
                                                    cursor: item.willOverTimeUnCollectCount == 0 || item.willOverTimeUnCollectCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'willOverTimeUnCollectCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.willOverTimeUnCollectCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="超时未完成"><span
                                                :style="{
                                                    color: item.overTimeUnCollectCount == 0 || item.overTimeUnCollectCount == null ? '#000' : '#409eff',
                                                    cursor: item.overTimeUnCollectCount == 0 || item.overTimeUnCollectCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'overTimeUnCollectCount', 'inWms_')"
                                                class="discriptions_content"> {{
                                                    item.overTimeUnCollectCount
                                                }}</span></el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="打包揽收均长"
                                            :span="2"><span style="color: #000;cursor: auto;"
                                                class="discriptions_content">{{
                                                    item.packCollectTimeAvg
                                                }}</span>
                                            <!-- :style="{
                                                color: item.packCollectTimeAvg == 0 || item.packCollectTimeAvg == null ? '#000' : '#409eff',
                                                cursor: item.packCollectTimeAvg == 0 || item.packCollectTimeAvg == null ? 'auto' : 'pointer'
                                            }" @click="openLog(item, 'packCollectTimeAvg', 'inWms_')" -->
                                        </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="发货揽收均长"
                                            :span="2"><span :title="formatMinutes(item.sendCollectTimeAvg).toolTip">{{
                                                formatMinutes(item.sendCollectTimeAvg).str
                                                }}</span> </el-descriptions-item>
                                    </el-descriptions>

                                    <el-descriptions title="已揽收" :column="2" border>
                                        <el-descriptions-item labelClassName="item-group-label" label="订单量"><span
                                                :style="{
                                                    color: item.collectCount == 0 || item.collectCount == null ? '#000' : '#409eff',
                                                    cursor: item.collectCount == 0 || item.collectCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'collectCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.collectCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="未超时揽收"><span
                                                :style="{
                                                    color: item.unOverTimeCollectCount == 0 || item.unOverTimeCollectCount == null ? '#000' : '#409eff',
                                                    cursor: item.unOverTimeCollectCount == 0 || item.unOverTimeCollectCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'unOverTimeCollectCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.unOverTimeCollectCount
                                                }}</span> </el-descriptions-item>
                                        <el-descriptions-item labelClassName="item-group-label" label="超时揽收"><span
                                                :style="{
                                                    color: item.overTimeCollectCount == 0 || item.overTimeCollectCount == null ? '#000' : '#409eff',
                                                    cursor: item.overTimeCollectCount == 0 || item.overTimeCollectCount == null ? 'auto' : 'pointer'
                                                }" @click="openLog(item, 'overTimeCollectCount', 'inWms_')"
                                                class="discriptions_content">{{
                                                    item.overTimeCollectCount
                                                }}</span> </el-descriptions-item>
                                    </el-descriptions>
                                </el-card>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else class="_content_main1">
                    暂无数据</div>
            </div>
        </div>

        <el-dialog :visible.sync="setVisiable" width="60%" :close-on-click-modal="false" v-dialogDrag>
            <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="150px"
                class="demo-ruleForm" v-if="setVisiable">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>外仓设置</span>
                    </div>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="发货-预警时长:" prop="outPlanSend_WarningHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="发货-预警时长" style="width: 100px"
                                    v-model="ruleForm.outPlanSend_WarningHour" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="揽收-预警时长:" prop="outSendCollect_WarningHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="揽收-预警时长" style="width: 100px"
                                    v-model="ruleForm.outSendCollect_WarningHour" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="揽收-超时时长:" prop="outSendCollect_OverHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="揽收-超时时长" style="width: 100px"
                                    v-model="ruleForm.outSendCollect_OverHour" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="中转-预警时长:" prop="outCollectTransfer_WarningHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="中转-预警时长" style="width: 100px"
                                    v-model="ruleForm.outCollectTransfer_WarningHour" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="中转-超时时长:" prop="outCollectTransfer_OverHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="中转-超时时长" style="width: 100px"
                                    v-model="ruleForm.outCollectTransfer_OverHour" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>内仓设置</span>
                    </div>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="生成批次-预警时长:" prop="inPickGen_WarningHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="生成批次-预警时长" style="width: 100px"
                                    v-model="ruleForm.inPickGen_WarningHour" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="生成批次-超时时长:" prop="inPickGen_OverHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="超时时长（内仓:批次-生成)" style="width: 100px"
                                    v-model="ruleForm.inPickGen_OverHour" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="领取拣货-预警时长:" prop="inPick_WarningHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="领取拣货-预警时长" style="width: 100px"
                                    v-model="ruleForm.inPick_WarningHour" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="领取拣货-超时时长:" prop="inPick_OverHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="领取拣货-超时时长" style="width: 100px" v-model="ruleForm.inPick_OverHour" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="拣货完成-预警时长:" prop="inPickCpt_WarningHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="拣货完成-预警时长" style="width: 100px"
                                    v-model="ruleForm.inPickCpt_WarningHour" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="拣货完成-超时时长:" prop="inPickCpt_OverHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="拣货完成-超时时长" style="width: 100px"
                                    v-model="ruleForm.inPickCpt_OverHour" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="打包-预警时长:" prop="inPack_WarningHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="打包-预警时长" style="width: 100px" v-model="ruleForm.inPack_WarningHour" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="打包-超时时长:" prop="inPack_OverHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="打包-超时时长" style="width: 100px" v-model="ruleForm.inPack_OverHour" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="揽收-预警时长:" prop="inCollect_WarningHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="揽收-预警时长" style="width: 100px"
                                    v-model="ruleForm.inCollect_WarningHour" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="揽收-超时时长:" prop="inCollect_OverHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="揽收-超时时长 " style="width: 100px" v-model="ruleForm.inCollect_OverHour" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>内仓设置-N元M件</span>
                    </div>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="生成批次-预警时长:" prop="inNmPickGen_WarningHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="生成批次-预警时长" style="width: 100px"
                                    v-model="ruleForm.inNmPickGen_WarningHour" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="生成批次-超时时长:" prop="inNmPickGen_OverHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="超时时长（内仓:批次-生成)" style="width: 100px"
                                    v-model="ruleForm.inNmPickGen_OverHour" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="领取拣货-预警时长:" prop="inNmPick_WarningHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="领取拣货-预警时长" style="width: 100px"
                                    v-model="ruleForm.inNmPick_WarningHour" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="领取拣货-超时时长:" prop="inNmPick_OverHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="领取拣货-超时时长" style="width: 100px" v-model="ruleForm.inNmPick_OverHour" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="拣货完成-预警时长:" prop="inNmPickCpt_WarningHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="拣货完成-预警时长" style="width: 100px"
                                    v-model="ruleForm.inNmPickCpt_WarningHour" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="拣货完成-超时时长:" prop="inNmPickCpt_OverHour">
                                <el-input-number :controls="false" :min="0" :max="99" :precision="1"
                                    placeholder="拣货完成-超时时长" style="width: 100px"
                                    v-model="ruleForm.inNmPickCpt_OverHour" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>通知人设置</span>
                        <el-button style="float: right; padding: 3px 0" type="text" @click="addProps">新增一行</el-button>
                    </div>
                    <el-table :data="ruleForm.warningUsersList" style="width: 100%" border>
                        <el-table-column prop="wmsId" label="仓库">
                            <template #default="{ row, $index }">
                                <chooseWareHouse v-model="row.wmsId" style="width: 220px"
                                    @chooseWms="chooseWms($event, $index)" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="area" label="地区">
                            <template #default="{ row, $index }">
                                <el-select filterable v-model="row.area" collapse-tags clearable placeholder="地区"
                                    style="width: 140px">
                                    <el-option label="义乌" value="义乌" />
                                    <el-option label="南昌" value="南昌" />
                                    <el-option label="武汉" value="武汉" />
                                    <el-option label="深圳" value="深圳" />
                                    <el-option label="其他" value="其他" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="warningUserNames" label="通知人">
                            <template #default="{ row, $index }">
                                <yhUserselectors class="publicCss" :value.sync="row.warningUserDIds"
                                    :text.sync="row.warningUserNames" clearable />
                            </template>
                        </el-table-column>
                        <el-table-column prop="warningUserNames" label="操作" align="right">
                            <template #default="{ row, $index }">
                                <el-button type="danger"
                                    @click="ruleForm.warningUsersList.splice($index, 1)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
                <div style="
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 10px;
          ">
                    <el-button @click="setVisiable = false" style="margin-right: 10px">关闭</el-button>
                    <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
                </div>
            </el-form>
        </el-dialog>

        <el-dialog :visible.sync="logVisiable" width="60%" :close-on-click-modal="false" v-dialogDrag>
            <codeLog v-if="logVisiable" :logQuery="logQuery" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from "@/utils/tools";
import dayjs from "dayjs";
import dateRange from "@/components/date-range/index.vue";
import yhUserselectors from "@/components/YhCom/yh-userselectors.vue";
const api = "/api/verifyOrder/Orders/";
import request from "@/utils/request";
import codeLog from './codeLog.vue'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
export default {
    name: "scanCodePage",
    components: {
        MyContainer,
        vxetablebase,
        dateRange,
        chooseWareHouse,
        yhUserselectors,
        codeLog
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'totalCount',
                isAsc: false,
                startTakeTime: dayjs().subtract(7, 'day').format("YYYY-MM-DD"),
                endTakeTime: dayjs().format("YYYY-MM-DD"),
            },
            timeRanges: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            setVisiable: false,
            ruleForm: {
                warningUsersList: [
                    {
                        wmsId: null,
                        wmsName: null,
                        area: null,
                        warningUserNames: [],
                        warningUserDIds: [],
                    },
                ],
            },
            tableData1: [],
            rules: {
                outPlanSend_WarningHour: [
                    {
                        required: true,
                        message: "发货-预警时长",
                        trigger: "blur",
                    },
                ],
                outSendCollect_WarningHour: [
                    {
                        required: true,
                        message: "揽收-预警时长",
                        trigger: "blur",
                    },
                ],
                outSendCollect_OverHour: [
                    {
                        required: true,
                        message: "揽收-超时时长",
                        trigger: "blur",
                    },
                ],
                outCollectTransfer_WarningHour: [
                    {
                        required: true,
                        message: "中转-预警时长",
                        trigger: "blur",
                    },
                ],
                outCollectTransfer_OverHour: [
                    {
                        required: true,
                        message: "中转-超时时长",
                        trigger: "blur",
                    },
                ],
                inPickGen_WarningHour: [
                    {
                        required: true,
                        message: "生成批次-预警时长",
                        trigger: "blur",
                    },
                ],
                inPickGen_OverHour: [
                    {
                        required: true,
                        message: "生成批次-超时时长",
                        trigger: "blur",
                    },
                ],
                inPick_WarningHour: [
                    {
                        required: true,
                        message: "领取拣货-预警时长",
                        trigger: "blur",
                    },
                ],
                inPick_OverHour: [
                    {
                        required: true,
                        message: "领取拣货-超时时长",
                        trigger: "blur",
                    },
                ],
                inPickCpt_WarningHour: [
                    {
                        required: true,
                        message: "拣货完成-预警时长",
                        trigger: "blur",
                    },
                ],
                inPickCpt_OverHour: [
                    {
                        required: true,
                        message: "拣货完成-超时时长",
                        trigger: "blur",
                    },
                ],
                inPack_WarningHour: [
                    {
                        required: true,
                        message: "打包-预警时长",
                        trigger: "blur",
                    },
                ],
                inPack_OverHour: [
                    {
                        required: true,
                        message: "打包-超时时长",
                        trigger: "blur",
                    },
                ],
                inCollect_WarningHour: [
                    {
                        required: true,
                        message: "揽收-预警时长",
                        trigger: "blur",
                    },
                ],
                inCollect_OverHour: [
                    {
                        required: true,
                        message: "揽收-超时时长",
                        trigger: "blur",
                    },
                ],
                inNmPickGen_WarningHour: [
                    {
                        required: true,
                        message: "生成批次-预警时长",
                        trigger: "blur",
                    },
                ],
                inNmPickGen_OverHour: [
                    {
                        required: true,
                        message: "生成批次-超时时长",
                        trigger: "blur",
                    },
                ],
                inNmPick_WarningHour: [
                    {
                        required: true,
                        message: "领取拣货-预警时长",
                        trigger: "blur",
                    },
                ],
                inNmPick_OverHour: [
                    {
                        required: true,
                        message: "领取拣货-超时时长",
                        trigger: "blur",
                    },
                ],
                inNmPickCpt_WarningHour: [
                    {
                        required: true,
                        message: "拣货完成-预警时长",
                        trigger: "blur",
                    },
                ],
                inNmPickCpt_OverHour: [
                    {
                        required: true,
                        message: "拣货完成-超时时长",
                        trigger: "blur",
                    },
                ],
                warningUsersList: [
                    { required: true, message: "通知人设置", trigger: "blur" },
                ],
            },
            logQuery: {
                resource: null,
                unBatch_GoodsCode: null,
                title: null,
            },
            logVisiable: false
        };
    },
    async mounted() {
        await this.search()

    },
    methods: {
        sendWmsesFilter(wmses) {
            this.wmsesList = wmses;
            return wmses.filter((a) => a.isSendWarehouse == '是');
        },
        formatMinutes(val) {
            //将分钟转化为x天x小时x分钟
            if (val == 0 || val == null) {
                return 0
            }
            let day = Math.floor(val / 1440)
            let hour = Math.floor((val % 1440) / 60)
            let minute = val % 60
            const obj = {
                str: `${day},${hour},${minute}`,
                toolTip: `${day}天${hour}小时${minute}分钟`
            }
            return obj
        },
        openLog(item, key, type) {
            if (item[key] == 0 || item[key] == null) return;
            const params = {
                resource: type + key,
                wmsIds: [item.wmsId],
            }
            this.logQuery = { ...this.ListInfo, ...params, orderBy: null }
            this.logVisiable = true
        },
        async search() {
            this.loading = true;
            Promise.all([
                this.getList(),
                this.getInwmsList()
            ]).finally(() => {
                this.loading = false;
            });
        },
        chooseWms(e, index) {
            this.ruleForm.warningUsersList[index].wmsName = e ? e.name : null;
        },
        submitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    const { success } = await request.post(
                        `${api}MergeSetting`,
                        this.ruleForm
                    );
                    if (!success) return;
                    this.$message.success("保存成功");
                    this.setVisiable = false;
                } else {
                    return false;
                }
            });
        },
        addProps() {
            this.ruleForm.warningUsersList.push({
                wmsId: null,
                wmsName: null,
                area: null,
                warningUserNames: [],
                warningUserDIds: [],
            });
        },
        async openSet() {
            const { data } = await request.post(`${api}GetSetting`);
            this.ruleForm = data;
            this.setVisiable = true;
        },
        async getInwmsList(type) {
            if (type == "search") {
                this.ListInfo.currentPage = 1;
            }
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(
                    `${api}inWms/PageGetData`,
                    this.ListInfo
                );
                if (success) {
                    this.tableData1 = data.list;
                    this.total = data.total;
                    // this.loading = false;
                }
            } catch (error) {
                this.loading = false;
            }
        },
        async getList(type) {
            if (type == "search") {
                this.ListInfo.currentPage = 1;
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(
                    `${api}outWms/PageGetData`,
                    this.ListInfo
                );
                if (success) {
                    this.tableData = data.list;
                    this.total = data.total;
                    // this.loading = false;
                }
            } catch (error) {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList();
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop;
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false;
                this.getList();
            }
        },
    },
};
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}

._content {
    padding: 0 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    height: 100%;
    font-size: 12px;

    ._content_top,
    ._content_bottom {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 10px;

        .title {
            font-size: 16px;
            font-weight: bold;
            color: #409eff;
        }

        ._content_main,
        ._content_main1 {
            display: -webkit-box;
            width: 100%;
            flex-wrap: nowrap;
            overflow-x: auto;
            scrollbar-width: thin;
            scrollbar-color: #ccc transparent;
            white-space: nowrap !important;

            ._content_item {
                // width: 400px;
                border: 1px solid #ebeef5;
                margin: 0 10px 10px 0;
            }
        }
    }

    ._content_bottom {
        ._content_main1 {
            display: flex;
            flex-direction: column;

            ._content_item {
                display: -webkit-box;
                flex-direction: row;
                background-color: transparent;
                border: none;
                box-shadow: none;
                margin: 0;
                width: 100%;
                overflow-x: auto;
                scrollbar-width: thin;
                scrollbar-color: #ccc transparent;

                // 移除 hover 效果
                &:hover {
                    background-color: transparent;
                }

                ._content_item_box {
                    // width: 100%;
                    background-color: #f2f2f2;
                    border: 1px solid #ebeef5;
                    border-radius: 5px;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                    box-sizing: border-box;
                    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
                    padding: 15px;

                    &:hover {
                        background-color: #e6f7ff;
                    }

                    ._content_item_info {
                        display: flex;
                        flex-wrap: wrap;

                        ._content_item_info_left,
                        ._content_item_info_right {
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        ._content_item_info_left {
                            width: 70px;
                        }

                        ._content_item_info_right {
                            width: 50px;
                        }
                    }
                }
            }
        }
    }
}

.box-card {
    margin-bottom: 10px;
}

::v-deep .item-group-label {
    color: #000;
}

::v-deep .item-group-label1 {
    background-color: white !important;
    border: 0px !important;
}


::v-deep .item-group-content {
    border: 0px !important;
}

::v-deep .el-card__body {
    padding: 10px;
}

::v-deep .el-descriptions__title {
    font-weight: normal !important;
    font-size: 12px !important;
    color: #909399 !important;
}

// .discriptions_content {
//     cursor: pointer;.discriptions_content {
//     color:.discriptions_content { #409eff;
// }
.discriptions_content {
    cursor: pointer;
    color: #409eff;
}
</style>
<style>
.el-descriptions__header {
    margin: 5px 0 !important;
    font-weight: normal !important;
}
</style>
