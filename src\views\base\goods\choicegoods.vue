<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="编号:">
          <el-input v-model="filter.id" placeholder="编号"/>
        </el-form-item>
         <el-form-item label="标题:">
          <el-input v-model="filter.title" placeholder="标题"/>
        </el-form-item>
        <el-form-item label="商品编码:">
          <el-input v-model="filter.proBianma" placeholder="商品编码"/>
        </el-form-item>
        <el-form-item label="供应商:">
          <el-input v-model="filter.supplierName" placeholder="供应商"/>
        </el-form-item>
        <el-form-item label="平台:">
          <el-select v-model="filter.bianMaPlatform" placeholder="请选择">
            <el-option label="所有" value></el-option>
            <el-option label="国内" value='1'></el-option>
            <el-option label="跨境" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分类:">
          <el-select v-model="filter.BianMaCategoryId" placeholder="请选择">
             <el-option label="所有" value></el-option>
             <el-option v-for="item in bianMaCategoryPageList" :key="item.id" :label="item.categoryName" :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item label="状态:">
          <el-select v-model="filter.status" placeholder="请选择">
            <el-option label="所有" value></el-option>
            <el-option label="正常" value='1'></el-option>
            <el-option label="缺货" value="2"></el-option>
            <el-option label="停用" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'  @select='selectchange' :isSelection='true'
              :tableData='list' :tableCols='tableCols' :showsummary='false'
              :loading="listLoading">
    </ces-table>
    
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
  </my-container>
</template>

<script>
import {getPageList} from '@/api/operatemanage/base/productbianma'
import {getProductBianMaCategoryPageList } from '@/api/operatemanage/base/shop'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatBianMaPlatform,formatYesornofinished,formatbianmastatus} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'id',label:'编号', width:'180',sortable:'custom'},
      {istrue:true,prop:'proBianMa',label:'编码', width:'150',sortable:'custom'},
      {istrue:true,prop:'bianMaPlatform',label:'平台', width:'100',formatter:(row)=>formatBianMaPlatform(row.bianMaPlatform)},
      {istrue:true,prop:'itemNo',label:'货号', width:'150',sortable:'custom'},      
      {istrue:true,prop:'title',label:'商品名称', width:'150',sortable:'custom'},
      {istrue:true,prop:'jianChen',label:'商品简称', width:'150',sortable:'custom'},
      {istrue:true,prop:'categoryName',label:'分类', width:'150',sortable:'custom'},
      {istrue:true,prop:'virtualCategory',label:'虚拟分类', width:'150',sortable:'custom'},
      {istrue:true,prop:'brandName',label:'品牌', width:'150',sortable:'custom'},
      {istrue:true,prop:'spec',label:'颜色及规格', width:'150',sortable:'custom'},
      {istrue:true,prop:'costPrice',label:'成本价', width:'150',sortable:'custom'},
      {istrue:true,prop:'marketPrice',label:'市场价', width:'150',sortable:'custom'},
      {istrue:true,prop:'price',label:'销售价', width:'150',sortable:'custom'},
      {istrue:true,prop:'weight',label:'重量(kg)', width:'150',sortable:'custom'},
      {istrue:true,prop:'supplierName',label:'供应商', width:'150',sortable:'custom'},
      {istrue:true,prop:'isFinished',label:'成品?', width:'150',sortable:'custom',formatter:(row)=>formatYesornofinished(row.isFinished)},
      {istrue:true,prop:'status',label:'状态', width:'150',sortable:'custom',formatter:(row)=>formatbianmastatus(row.status)},
      {istrue:true,prop:'createdTime',label:'添加时间', width:'200'},
      {istrue:true,prop:'categoryName',label:'更新时间', width:'150',sortable:'custom'}
     ];
export default {
  name: 'choicebianma',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: { },
      list: [],
      bianMaCategoryPageList:[],
      pager:{OrderBy:"id",IsAsc:false},
      tableCols:tableCols,
      total: 0,
      sels: [], 
      selrows:[],
      listLoading: false,
      pageLoading: false
    }
  },
  mounted() {
    this.getlist()
    this.getBianMaCategory()
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async getBianMaCategory() {
      const res1 = await getProductBianMaCategoryPageList({CurrentPage:1,PageSize:100})
      this.bianMaCategoryPageList = res1.data?.list
    },
   async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
   async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = { ...pager, ... this.filter }
      this.listLoading = true
      const res = await getPageList(params)
      this.listLoading = false
      if (!res?.success) { return }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => { d._loading = false })
      this.list = data
      this.selids=[]
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async getchoicelist() {
      return await this.selrows;
    },
   selectchange:function(rows,row) {
      this.selrows=rows;
    }
  }
}
</script>
