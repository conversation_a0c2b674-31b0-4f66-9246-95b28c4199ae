<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='mydatalist' @select='selectchange' :isSelection='false' :tableCols='tableCols'
            :loading="listLoading" :summaryarry="summaryarry">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-date-picker style="width:240px" v-model="filter.sdate" type="daterange" format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始操作日期"
                            end-placeholder="结束操作日期" :picker-options="pickerOptions"></el-date-picker>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.name" placeholder="仓库名称" style="width:120px;" clearable
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.modifiedUserName" placeholder="最近操作人" style="width:120px;" clearable
                            :maxlength="50" />
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getdataList" />
        </template>

        <el-dialog title="修改" :visible.sync="dialogEdit.visible" width="20%" :close-on-click-modal="false" v-dialogDrag
            v-loading="dialogEdit.vLoading">
            <el-form ref="dialogEditFormData" :model="dialogEdit.editFormData" label-width="80px" label-position="right">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item prop="name" label="仓库名称">
                            <el-input v-model.trim="dialogEdit.editFormData.name" disabled style="width:95%"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item prop="positionTotal" label="总数">
                            <el-input-number v-model.trim="dialogEdit.editFormData.positionTotal" :min="0" :max="10000000"
                                style="width:95%" auto-complete="off" :precision="0"></el-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogEdit.visible = false">关闭</el-button>
                <el-button type="primary" @click="oneditmysave">保存</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import {
    pickerOptions
} from "@/utils/tools";
import { getWarehousePositionUsagePageList, updateWarehousePositionUsage } from '@/api/inventory/warehouse'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
const tableCols = [
    { istrue: true, prop: 'wms_co_id', label: '仓库名称', width: '260', sortable: 'custom', formatter: (row) => row.name },
    { istrue: true, prop: 'positionTotal', label: '总数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'positionUseNum', label: '已使用', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'positionResidueNum', label: '剩余', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'positionUseRate', label: '使用率', width: '100', sortable: 'custom', formatter: (row) => row.positionUseRate.toString() + "%" },
    { istrue: true, prop: 'modifiedPositionTotal', label: '最近操作值', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'modifiedUserName', label: '最近操作人', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'modifiedTime', label: '最近操作时间', width: '150', sortable: 'custom' },
    {
        istrue: true, type: "button", label: '操作', width: "110",
        btnList: [
            { label: "修改", handle: (that, row) => that.oneditmy(row) },
            { label: "查看日志", handle: (that, row) => that.onshowmylog(row) }
        ]
    }
];
export default {
    name: "warehousepositionusage",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
    data() {
        return {
            that: this,
            pickerOptions: pickerOptions,
            filter: {
                sdate: [],
            },
            mydatalist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "modifiedTime", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],

            dialogEdit: {
                visible: false,
                vLoading: false,
                editFormData: {
                    wms_co_id: 0,
                    name: "",
                    positionTotal: 0,
                },
            },
        };
    },
    async mounted() {
        this.onSearch();
    },
    methods: {
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getdataList();
        },
        async getdataList() {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            console.log(params)
            this.listLoading = true;
            const res = await getWarehousePositionUsagePageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.mydatalist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async oneditmy(row) {
            this.dialogEdit.visible = true;
            this.dialogEdit.editFormData = row;
        },
        async oneditmysave() {
            this.$confirm('确认要执行保存操作吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                this.dialogEdit.vLoading = true;
                let res = await updateWarehousePositionUsage(this.dialogEdit.editFormData);
                this.dialogEdit.vLoading = false;
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.dialogEdit.visible = false;
                    this.onSearch();
                }
            }).catch(() => { });
        },
        async onshowmylog(row) {
            this.$showDialogform({
                path: `@/views/inventory/warehousepositionusagelog.vue`,
                title: '日志',
                args: { ...row },
                height: '600px',
                width: '60%',
            })
        },

    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
