<template>
  <my-container>
    <!--顶部操作-->
    <div class=".top">
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
        <el-form-item label="创建时间:">
          <el-date-picker style="width: 320px" v-model="Filter.timerange" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>
         
        <el-form-item label="平台:">
          <el-select v-model="Filter.platform" placeholder="请选择" class="el-select-content" @change="changePlatform" clearable>
            <el-option v-for="item in platformTypeList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="店铺:">
          <el-select v-model="Filter.shopName" placeholder="请选择" class="el-select-content" filterable multiple clearable collapse-tags>
            <el-option v-for="item in shopList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="聊天账号:">
         <el-select v-model="Filter.chatAccount" placeholder="请选择" class="el-select-content" filterable multiple clearable collapse-tags>
            <el-option v-for="item in chatAccountList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="账号使用人:">
          <el-select v-model="Filter.userName" placeholder="请选择" class="el-select-content" filterable multiple clearable collapse-tags>
            <el-option v-for="item in userNameList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="分组:">
          <el-select v-model="Filter.groupName" placeholder="请选择" class="el-select-content" filterable multiple clearable collapse-tags>
            <el-option v-for="item in groupNameList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="线上订单号:">
          <el-input maxlength="50" v-model.trim="Filter.orderNo" clearable />
        </el-form-item>
        <el-form-item label="宝贝ID:">
          <el-input maxlength="20" v-model.trim="Filter.proId" clearable />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport" v-if="checkPermission(['api:customerservice:UnPayOrder:ReplyCarelesslyExport'])">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="uptime">数据更新时间: {{ upDataTime }}</div>

    <!--列表-->
    <Ces-table ref="table" :that="that" :isIndex="true" @sortchange="sortchange" :tableData="tableData"
      :showsummary="true" :summaryarry="summaryarry" :tableCols="tableCols" :loading="listLoading" style="width: 100%;
    height: calc(100% - 15%); margin: 0">
    </Ces-table>

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="gettrainplanList" />
    </template>

<!-- 聊天记录 -->
<ChartRecordDialog :v-if="recorddialogVisible" :isShow="recorddialogVisible" @closeDialog="recorddialogVisible = false" ref="recordithRef" ></ChartRecordDialog>
<!-- 敷衍审核 -->
<AuditChartFourth  :v-if="auditChartdialogVisible" :isShow="auditChartdialogVisible" @closeDialog="auditChartdialogVisible = false" ref="auditChartithRef" @upData="onSearch"></AuditChartFourth>

    <!-- 订单日志信息 -->
    <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag append-to-body>
      <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" style="z-index:10000;height:600px" />
    </el-dialog>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import CesTable from "@/components/Table/table.vue";
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";

import dayjs from "dayjs";
import { formatTime } from "@/utils";

import {getReplyCarelesslyPagehList,
getReplyCarelesslyPagehQueryGroup,
replyCarelesslyExport} from "@/api/customerservice/chartCheck";
import ChartRecordDialog from "@/views/customerservice/chartCheck/SalesDialog/ChartRecordDialog"
import { formatLinkProCode } from "@/utils/tools";
import AuditChartFourth from "@/views/customerservice/chartCheck/SalesDialog/auditChartFourth"


//平台下拉
 const platformTypeList=[
        { name: "拼多多", value: 2 },
        { name: "抖音", value: 6 },
 ];

const tableCols = [
  {
    istrue: true,
    prop: "platform",
    label: "平台",
    width: "60",
     formatter:(row)=>
      {
        return  platformTypeList.filter(item=>item.value==row.platform)[0].name 
      },
  },
  {
    istrue: true,
    prop: "shopName",
    label: "店铺",
    width: "150",
  },
  {
    istrue: true,
    prop: "insertTime",
    label: "创建时间",
    width: "100",
    sortable: "custom",
     formatter: (row) => {
      return row.insertTime?formatTime(row.insertTime, "YYYY-MM-DD"):"";
    },
  },
  {
    istrue: true,
    prop: "orderNo",
    label: "线上订单号",
    width: "150",
     type: 'click',
    handle: (that, row) => that.showLogDetail(row)
  },
  {
    istrue: true,
    prop: "proId",
    label: "宝贝ID",
    width: "150",
      type: "html",
    formatter: (row) => formatLinkProCode(row.platform, row.proId),
  },
   {
    istrue: true,
    prop: "remark",
    label: "问题说明",
    width: "200",
  },
  {
    istrue: true,
    prop: "chatAccount",
    label: "聊天账号",
    width: "200",
  },
    {
    istrue: true,
    prop: "userName",
    label: "使用账号人",
    width: "120",
    sortable: "custom",
  },
    {
    istrue: true,
    prop: "groupName",
    label: "分组",
    width: "150",
    sortable: "custom",
  },
   {
    istrue: true,
    prop: "responsibilityName",
    label: "责任客服",
    width: "100",
    sortable: "custom",
  },
   {
    istrue: true,
    prop: "responsibilityType",
    label: "敷衍类型",
    width: "150",
    sortable: "custom",
  },
  {
    istrue: true,
    type: "button",
    label: "操作",
    align: "center",
    fixed: "right",
    width:"250",
    btnList: [
      { label: "查看聊天记录", handle: (that, row) => that.showDetail(row) },
      { label: "敷衍审核", handle: (that, row) => that.auditChart(row),
         display:(row)=>{return  row.responsibilityType!="" && row.responsibilityType!=null},
          permission: "api:customerservice:UnPayOrder:Elaborate"
       },
    ],
  },
];
export default {
  name: "salesThird",
  components: {
    MyContainer,
    CesTable,
    OrderActionsByInnerNos,
    ChartRecordDialog,//聊天记录
    AuditChartFourth,//敷衍审核
  },
  data() {
    return {
      that: this,
      Filter: {
        timerange: [
          formatTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"),
          formatTime(new Date(), "YYYY-MM-DD 23:59:59"),
        ],
        platform:null,
        shopName:null,
        chatAccount:null,
        orderNo:null,
        proId:null,
        userName:null,
        groupName:null,
      },
      platformTypeList:platformTypeList,
      tableData: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: null,
      pager: { orderBy: "createdTime", isAsc: false },
      listLoading: false,
      shopList: [],
      userNameList:[],
      groupNameList:[],
      chatAccountList:[],
      recorddialogVisible:false,//聊天记录
      auditChartdialogVisible:false,//敷衍审核
      pickerOptions: {
        disabledDate(date) {
          // 设置禁用日期
          const start = new Date("1970/1/1");
          const end = new Date("9999/12/31");
          return date < start || date > end;
        },
      },
      defaultDate: new Date(),
      upDataTime: "",
      dialogHisVisible: false,
      orderNo: '',
    };
  },
  async mounted() {
         await this.init(); 
          await this.onSearch();
  },
  methods: {
      datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init(){
        var date1 = new Date(); date1.setDate(date1.getDate()-4);
        var date2 = new Date(); date2.setDate(date2.getDate()-1);
        this.Filter.timerange=[];
        this.Filter.timerange[0]=this.datetostr(date1);
        this.Filter.timerange[1]=this.datetostr(date2);
      },
     //查看线上订单号记录
    showLogDetail (row) {
      this.dialogHisVisible = true;
      this.orderNo = row.orderNo;
    },
    showDetail(row) {     //查看聊天记录
      this.$refs.recordithRef.interfaceType=true
      this.$refs.recordithRef.dataJson=row
      this.$refs.recordithRef.tableData=this.tableData
      this.recorddialogVisible = true;
    },
    auditChart(row) {  //敷衍审核 ResponsibilityType
      this.$refs.auditChartithRef.interfaceType=true
      this.$refs.auditChartithRef.dataJson=row
      this.$refs.auditChartithRef.tableData=this.tableData.filter(item=>{ return item.responsibilityType===null|| item.responsibilityType===''});
      this.auditChartdialogVisible = true;
    },
    // 查询
    onSearch() {
      this.$nextTick(() => {
        this.$refs.pager.setPage(1);
        this.gettrainplanList();
        this.getSelectOptionList();
      });
    },
     //头部下拉列表
    async getSelectOptionList()
    {
      const para = { ...this.Filter };
        if (this.Filter.timerange) {
          para.timeStart = this.Filter.timerange[0];
          para.timeEnd = this.Filter.timerange[1];
        }
          const param={
            platform:para.platform,
            timeStart:para.timeStart,
            timeEnd:para.timeEnd
          }
        const res = await getReplyCarelesslyPagehQueryGroup(param);
        const resData=res.data;
        this.shopList=resData.shopNameGrouop
        this.chatAccountList=resData.chatAccountGrouop
        this.groupNameList=resData.groupNameGrouop
        this.userNameList=resData.userNameGrouop
    },
     //平台联动
    async changePlatform(val) {
        const para = { ...this.Filter };
      if (this.Filter.timerange) {
          para.timeStart = this.Filter.timerange[0];
          para.timeEnd = this.Filter.timerange[1];
        }
        const param={
        platform:val,
        timeStart:para.timeStart,
        timeEnd:para.timeEnd
      }
       this.Filter.shopName=[];
        this.Filter.chatAccount=[];
        this.Filter.groupName=[];
        this.Filter.userName=[];
        const res = await getReplyCarelesslyPagehQueryGroup(param);
        const resData=res.data;
        this.shopList=resData.shopNameGrouop
        this.chatAccountList=resData.chatAccountGrouop
        this.groupNameList=resData.groupNameGrouop
        this.userNameList=resData.userNameGrouop
    },
    getCondition() {
      const para = { ...this.Filter };
      if (this.Filter.timerange) {
        para.timeStart = this.Filter.timerange[0];
        para.timeEnd = this.Filter.timerange[1];
      }
      // if (this.Filter.shopName) para.shopName = this.Filter.shopName;
      // else para.shopName = null;
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      return params;
    },
    async gettrainplanList() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await getReplyCarelesslyPagehList(params);
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const resData = res.data.list;
      this.upDataTime = res.data.extData.dataUpdateTime;
      resData.forEach((d) => {
        d._loading = false;
      });
      this.tableData = resData;
      this.summaryarry = res.data.summary;
    },
    sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          orderBy: column.prop,
          isAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.onSearch();
    },
    async onExport() {
          var params = this.getCondition();
          if (params === false) {
            return;
          }
          var res = await replyCarelesslyExport(params);
          const aLink = document.createElement("a");
          let blob = new Blob([res], { type: "application/vnd.ms-excel" });
          aLink.href = URL.createObjectURL(blob);
          aLink.setAttribute(
            "download",
            "敷衍回复_" + new Date().toLocaleString() + ".xlsx"
          );
          aLink.click();
    },
  },
};
</script>

<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

::v-deep .mycontainer {
  position: relative;
}

.uptime {
  font-size: 14px;
  position: absolute;
  right: 30px;
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 45px;
}
</style>
