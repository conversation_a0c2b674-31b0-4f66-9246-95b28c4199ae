<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :tableData='inquirsstatisticslist' @select='selectchange' :isSelection='false' :tableCols='tableCols'
      :loading="listLoading">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <template slot='extentbtn'>
        <el-button-group>
           <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="Filter.GroupNameList"  placeholder="分组"  filterable multiple clearable collapse-tags>
                            <el-option v-for="item in filterGroupList" :key="item" :label="item" :value="item"></el-option>
                        </el-select>
                    </el-button>
          <!-- <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="Filter.Groupname" placeholder="组名称" style="width:120px;" />
          </el-button> -->
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="Filter.Sname" placeholder="姓名" style="width:120px;" />
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <datepicker v-model="Filter.Sdate" :clearable="false"></datepicker>
          </el-button>
          <!-- <el-button type="text" size="medium" disabled style="color: red;">周转对比:</el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input-number v-model="filter1.startdiff"></el-input-number>至<el-input-number v-model="filter1.enddiff"></el-input-number>
            </el-button> -->
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button>

        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getinquirsstatisticsList" />
    </template>

    <el-dialog title="个人效率按店统计" :visible.sync="dialogVisibleSyj" width="80%" v-dialogDrag>
      <span>
        <inquirsstatisticsbyshop v-if="dialogVisibleSyj" ref="inquirsstatisticsbyshop" style="height: 600px;" />

      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="趋势图" :visible.sync="dialogMapVisible.visible" width="80%">
      <div>
        <span>
          <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>

import inquirsstatisticsbyshop from '@/views/customerservice/inquirsstatisticsbyshop'
import { getInquirsStatisticsList, getInquirsStatisticsListMap, exportInquirsStatisticsList } from '@/api/customerservice/groupinquirsstatistics'

import datepicker from '@/views/customerservice/datepicker'
import buschar from '@/components/Bus/buschar'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

import { GetGroupNameList } from '@/api/customerservice/group'


const tableCols = [
  { istrue: true, prop: 'groupname', label: '组名称', width: '150', sortable: 'custom' },
  { istrue: true, prop: 'sname', label: '姓名', width: '100', type: "click", handle: (that, row, column, cell) => that.canclick(row, column, cell), formatter: (row) => row.sname },
  { istrue: true, prop: 'inquirs', label: '咨询人数', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'onedayips', label: '接待量均值', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'receivecount', label: '接待人数', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'ipscount', label: '询单人数', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'nextdaysuccesspaycount', label: '询单>次日付款人数', width: '150', sortable: 'custom' },
  { istrue: true, prop: 'nextdaysuccesspayrate', label: '询单>次日付款成功率', width: '160', sortable: 'custom', formatter: (row) => { return row.nextdaysuccesspayrate ? (row.nextdaysuccesspayrate * 100).toFixed(2) + "%" : 0 } },
  { istrue: true, prop: 'successpaycount', label: '询单>最终付款人数', width: '150', sortable: 'custom' },
  { istrue: true, prop: 'successpayrate', label: '询单>最终付款成功率', width: '160', sortable: 'custom', formatter: (row) => { return row.successpayrate ? (row.successpayrate * 100).toFixed(2) + "%" : 0 } },
  { istrue: true, prop: 'responseTime', label: '平均响应(秒)', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'satisdegree', label: '满意度', width: '100', sortable: 'custom',formatter:(row) => row.satisdegree !== null  ? (100 * row.satisdegree).toFixed(2) +'%' : '0%' },
  { istrue: true, prop: 'evaluateCount', label: '服务满意度评价参与量', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'evaluateVerySatisCount', label: '服务满意度评价很满意', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'evaluateSatisCount', label: '服务满意度评价满意', width: '120', sortable: 'custom' },
  
  { istrue: true, prop: 'salesvol', label: '销售额', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'dutycount', label: '出勤人次', width: '80', sortable: 'custom' },
  { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, inquirsstatisticsbyshop, datepicker, buschar },
  props:["partInfo"],
  data() {
    return {
      dialogMapVisible: { visible: false, title: "", data: [] },
      that: this,
      Filter: {
      },
      shopList: [],
      userList: [],
      groupList: [],
      inquirsstatisticslist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: { count_sum: 10 },
      pager: { OrderBy: "ipscount", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
      filterGroupList:[],
      isleavegroup:this.partInfo,//是否离组
    };
  },
  watch:{
        partInfo(){
            this.isleavegroup = this.partInfo;
           this.GetGroupNameList();
        }
    },
  async mounted() {
        window.showlist = this.showlist
        this.isleavegroup=this.partInfo;
        await this.GetGroupNameList();
  },
  methods: {
     async GetGroupNameList() {
      let res = await GetGroupNameList({isleavegroup:this.isleavegroup});
      this.filterGroupList=res.data;
    },
    async showchart(row) {

      if (this.Filter.timerange) {
        this.Filter.startSdate = this.Filter.Sdate[0];
        this.Filter.endSdate = this.Filter.Sdate[1]
      }
      var params = { Groupname: row.groupname, Sname: row.sname, StartSdate: this.Filter.startSdate, EndSdate: this.Filter.endSdate }
      let that = this;

      const res = await getInquirsStatisticsListMap(params).then(res => {
        that.dialogMapVisible.visible = true;
        that.dialogMapVisible.data = res.data
        that.dialogMapVisible.title = res.data.legend[0]
      })
      this.dialogMapVisible.visible = true
    },
    showlist(groupname, startdate, enddate) {
       this.Filter.GroupNameList = [groupname];
      this.Filter.Sdate = [startdate, enddate];
      this.onSearch()
    },



    async canclick(row, column, cell) {
      if (this.Filter.Sdate) {
        localStorage.setItem("startsdate", this.Filter.Sdate[0]);
        localStorage.setItem("endsdate", this.Filter.Sdate[1]);
      }
      else {
        localStorage.setItem("startsdate", "2010-01-01");
        localStorage.setItem("endsdate", "2050-01-01");
      }
      localStorage.setItem("sname", row.sname);
      this.dialogVisibleSyj = true
    },
    async deleteBatch(row) {
      var that = this;
      this.$confirm("此操作将删除此批次个人效率统计数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          await deleteInquirsStatisticsBatch({ batchNumber: row.batchNumber })
          that.$message({ message: '已删除', type: "success" });
          that.onRefresh()
        });
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onImportSyj() {
      this.dialogVisibleSyj = true
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      const res = importInquirsStatisticsAsync(form);
      this.$message({ message: '上传成功,正在导入中...', type: "success" });
    },
    async uploadSuccess2(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
    },
    async onSubmitupload2() {
      this.$refs.upload2.submit()
    },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getinquirsstatisticsList();
    },
    getParam() {
      if (this.Filter.UseDate) {
        this.Filter.startAccountDate = this.Filter.UseDate[0] ? dayjs(this.Filter.UseDate[0]).format("YYYY-MM-DD") : null;
        this.Filter.endAccountDate = this.Filter.UseDate[1] ? dayjs(this.Filter.UseDate[1]).format("YYYY-MM-DD") : null;
      }
      if (this.Filter.Sdate) {
        this.Filter.startSdate = this.Filter.Sdate[0]? dayjs(this.Filter.Sdate[0]).format("YYYY-MM-DD") : null;
        this.Filter.endSdate = this.Filter.Sdate[1]? dayjs(this.Filter.Sdate[1]).format("YYYY-MM-DD") : null;
      }
      else {
        this.Filter.startSdate = null;
        this.Filter.endSdate = null;
      }
      const para = { ...this.Filter };
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,

      };
      return params;
    },
    async getinquirsstatisticsList() {
      let params = this.getParam();
      this.listLoading = true;
      const res = await getInquirsStatisticsList(params);
      console.log(res)
      this.listLoading = false;
      console.log(res.data.list)
      //console.log(res.data.summary)

      this.total = res.data.total
      this.inquirsstatisticslist = res.data.list;
      //this.summaryarry=res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    async onExport() {
      let params = this.getParam();
      this.listLoading = true
      const res = await exportInquirsStatisticsList(params)
      this.listLoading = false
      if (!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '淘系个人效率统计(售前组)_' + new Date().toLocaleString() + '.xlsx');
      aLink.click()
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 60px;
}
</style>
