<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="">
                    <el-input v-model="filter.province" @change="startfilter"  placeholder="省"  maxlength="6" />
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model="filter.city" @change="startfilter"  placeholder="市" />
                </el-form-item>                
               
                <el-form-item>
                    <el-button type="primary" @click="(dialogfilterVisible=true)">筛选</el-button>
                </el-form-item>
                
                
                <el-form-item>
                    <el-button type="primary" @click="onSearch">清空并刷新</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="settingfa">拷贝城市列表</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :isSelectColumn="false" :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
        </ces-table> 
      
        <el-dialog title="拷贝已筛选停发城市" :close-on-click-modal="true" :visible.sync="tingfavisible" width="50%" height="300px">
     <div> 

        <el-input
          type="textarea"
          resize="none"          
          :autosize="{ minRows: 4, maxRows: 200 }"
          placeholder="停发列表"
          v-model="tingfa"
        >
    </el-input> 

     </div> 

     </el-dialog>
 <el-dialog title="区县停发" :close-on-click-modal="true" :visible.sync="dialogMapVisible" width="50%" height="300px">
     <div>
      <el-table :data="countylist">
        <el-table-column prop="county" label="县" fixed></el-table-column>
         <el-table-column prop="totalOrderCount" label="昨日订单量" fixed></el-table-column>
         <el-table-column prop="stopDeliverycount" label="停发量" fixed></el-table-column>
         <el-table-column prop="stopRate" label="停发比例" fixed></el-table-column>
    </el-table>
     </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogMapVisible = false">关闭</el-button>
      </span>
    </el-dialog>
<el-dialog title="数值筛选" :close-on-click-modal="true" :visible.sync="dialogfilterVisible" width="30%" height="200px">

   

    <el-form ref="form" :model="filter" label-width="120px" size="mini">   
  <el-form-item label="停发量区间">
    <el-col :span="5">
        <el-input v-model="filter.minstopcount" autosize="true" width="100px"></el-input>
    </el-col>
    <el-col class="line" :span="2">----</el-col>
    <el-col :span="5">
        <el-input v-model="filter.maxstopcount" autosize="true"   width="100px"></el-input>
    </el-col>
  </el-form-item>
  <el-form-item label="停发比例区间">
    <el-col :span="5">
        <el-input v-model="filter.minstoprate" width="100px"></el-input>
    </el-col>
    <el-col class="line" :span="2">----</el-col>
    <el-col :span="5">
        <el-input v-model="filter.maxstoprate" width="100px"></el-input>
    </el-col>
  </el-form-item>
  <el-form-item size="large" style="margin-left:100px">
    <el-button  type="primary"   @click="startfilter">确定</el-button>
  </el-form-item>
</el-form>

</el-dialog>

    </my-container>
</template>

<script>
    import { queryCountyList, queryCityList } from '@/api/order/stopdelivery'
    import MyContainer from '@/components/my-container'
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue"; 
    import { formatPlatform,  platformlist, companylist } from "@/utils/tools";
    import { rulePlatform, ruleDirectorGroup } from "@/utils/formruletools";
    import VueClipboard from 'vue-clipboard2'
 
    const tableCols = [
    { istrue: true, prop: 'province', label: '省', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'city', label: '城市', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'totalOrderCount', label: '昨日订单量	', width: '180', sortable: 'custom' },
        { istrue: true, prop: 'stopDeliverycount', label: '停发量', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'stopRate', label: '停发比例', width: '170', sortable: 'custom' },
        { istrue: true, type: 'button', btnList: [{ label: "查看区县", handle: (that, row) => that.showcounty(row) }] },
    ];
    const tableHandles1 = [
        //{label:"新增", handle:(that)=>that.onAdd()},
        // {label:'编辑', handle:(that)=>that.onEdit()}
    ];
    export default {
        name: 'Roles',
        components: { cesTable, MyContainer, MyConfirmButton },
        data() {
            return {
                that: this,
                filter: {
                    name: ''
                },
                list: [],
                pager: { OrderBy: "id", IsAsc: false },
                tableCols: tableCols,
                tableHandles: tableHandles1,
                platformlist: platformlist,
                companylist: companylist,
                autoform: {
                    fApi: {},
                    //options:{onSubmit:(formData)=>{alert(JSON.stringify(formData))}},
                    options: { submitBtn: false, form: { labelWidth: '145px' }, global: { '*': { props: { disabled: false }, col: { span: 8 } } } },
                    rule: [
                                          ]
                },
                total: 0,
                sels: [],
                sourcelist:[],
                listLoading: false,
                pageLoading: false,
                addFormVisible: false,
                tingfavisible: false,
                addLoading: false,
                deleteLoading: false,
                formtitle: "新增",
                dialogMapVisible:false,
                dialogMapVisible11:[],
                countylist:[],
                dialogfilterVisible:false,
               
                tingfa:'' 
            }
        },
        async beforeCreate() {

        },
        async mounted() {
           
            await this.getlist();

            for (const i in this.$data.autoform.rule) {
                if (this.$data.autoform.rule[i].field == "groupId") {
                    this.$data.autoform.rule[i].validate = []
                    break;
                }
            }
        },
        beforeUpdate() {
            console.log('update')
        },
        methods: {
            settingfa(){
                this.tingfa=""
                for(var i=0;i<this.list.length;i++)
                {
                    this.tingfa+=this.list[i].city+'\r\n';
                }
                this.tingfavisible=true
                this.$copyText(this.tingfa)
                this.$message({message:'已拷贝到剪切板',type:'success'})

            },
            async  showcounty(row){
                const params = {  Platforms:1 ,CityName:row.city}
                this.listLoading = true
                const res = await queryCountyList(params)
                 this.listLoading = false
                 if (!res?.success) return              
                 this.countylist = res.data     
                
                this.dialogMapVisible=true

            },
            startfilter(){
                var that=this;
                this.dialogfilterVisible=false
               
                this.list=this.sourcelist.filter((item)=>{
                    var p=true,ct=true,r=true,c=true;
                    var cmax=true,rmax=true
                    if(that.filter.province && item.province.indexOf(that.filter.province)<0)
                      p=false
                    if(that.filter.city && item.city.indexOf(that.filter.city)<0)
                      ct=false
                    if(that.filter.minstopcount && parseInt(that.filter.minstopcount)>parseInt(item.stopDeliverycount) )
                      c=false;
                    if(that.filter.maxstopcount &&  parseInt(that.filter.maxstopcount)<parseInt(item.stopDeliverycount))
                      cmax=false;
                    if(that.filter.minstoprate && that.filter.minstoprate>item.stopRate )
                      r=false
                    if(that.filter.maxstoprate&& that.filter.maxstoprate<item.stopRate)
                        rmax=false

                  
                
                    return p && c && r && ct && cmax && rmax;
                });

            },
            onSearch() {
                this.filter={}
              
                this.getlist()
            },
            async getlist() {
              
                const params = {  Platforms:1 }
                this.listLoading = true
                const res = await queryCityList(params)
                this.listLoading = false
                if (!res?.success) return
                this.total = res.data.length
                this.list = res.data
                this.sourcelist=res.data
            },
            sortchange(column) {
                var sorttype= column.order.indexOf("descending") == -1 ? true : false;                
                    var regPos = /\d+/; 
                    var isnumber=false;
                    if(regPos.test(this.list[0][column.prop])){
                           isnumber=true
                      }
                this.list.sort(function(a,b){ 
                    if(isnumber)
                    {                      
                        if(sorttype)
	                        return a[column.prop]-(b[column.prop]);
                        else
                            return b[column.prop]-(a[column.prop]);
                    }
                    else
                    {
                    if(sorttype)
	                    return b[column.prop].localeCompare(a[column.prop]);
                    else
                        return a[column.prop].localeCompare(b[column.prop]);
                    }}
                    );              
            },
           
            selsChange: function (sels) {
                this.sels = sels
            }
        }
    }
</script>
