<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="操作起始时间" end-placeholder="操作结束时间" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-select v-model="ListInfo.logType" style="width: 150px;" placeholder="类型" multiple collapse-tags clearable class="publicCss">
          <el-option label="编号换绑" value="编号换绑" />
          <el-option label="展位登记" value="展位登记" />
          <el-option label="展会列表" value="展会列表" />
        </el-select>
          <div class="publicCss">
          <inputYunhan ref="nameManufacturer" :inputt.sync="ListInfo.nameManufacturer" v-model="ListInfo.nameManufacturer"
            width="150px" placeholder="供应商名称/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'nameManufacturer')" title="供应商名称">
          </inputYunhan>
        </div>
        <div class="publicCss">
          <inputYunhan ref="styleCode" :inputt.sync="ListInfo.styleCode" v-model="ListInfo.styleCode"
            width="150px" placeholder="款式名称/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'styleCode')" title="款式名称">
          </inputYunhan>
        </div>
        <div class="publicCss">
          <inputYunhan ref="productName" :inputt.sync="ListInfo.productName" v-model="ListInfo.productName"
            width="150px" placeholder="商品名称/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'productName')" title="商品名称">
          </inputYunhan>
        </div>
        <div class="publicCss">
          <inputYunhan ref="yhGoodsCode" :inputt.sync="ListInfo.yhGoodsCode" v-model="ListInfo.yhGoodsCode"
            width="150px" placeholder="公司商品编码/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'yhGoodsCode')" title="公司商品编码">
          </inputYunhan>
        </div>
        <div class="publicCss">
          <inputYunhan ref="goodsCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode"
            width="150px" placeholder="商品编号/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'goodsCode')" title="商品编号">
          </inputYunhan>
        </div>
        <div class="publicCss">
          <inputYunhan ref="createdUserName" :inputt.sync="ListInfo.createdUserName" v-model="ListInfo.createdUserName"
            width="150px" placeholder="操作人/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'createdUserName')" title="操作人">
          </inputYunhan>
        </div>
        <!-- <div class="publicCss">
          <inputYunhan ref="productoldGoodsCode" :inputt.sync="ListInfo.oldGoodsCode" v-model="ListInfo.oldGoodsCode"
            width="150px" placeholder="原商品编号/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'oldGoodsCode')" title="原商品编号">
          </inputYunhan>
        </div>
       
        <el-input v-model.trim="ListInfo.createdUserName" placeholder="换绑人" maxlength="50" clearable
          class="publicCss" /> -->
          <el-input v-model.trim="ListInfo.oldGoodsCode" placeholder="原值" maxlength="50" clearable
          class="publicCss" />
          <el-input v-model.trim="ListInfo.newGoodsCode" placeholder="新值" maxlength="50" clearable
          class="publicCss" />
        
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" class="top_button" @click="exportProps" :disabled="isExport">导出</el-button>

      </div>
    </template>
    <vxetablebase :id="'changeBindingLog202503110957'" :tablekey="'changeBindingLog202503110957'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getSampleGoodsLog,exportSampleGoodsLog } from '@/api/customerservice/albbinquirs'
import inputYunhan from "@/components/Comm/inputYunhan";
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'logType', label: '类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'nameManufacturer', label: '供应商名称', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '款式名称', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'productName', label: '商品名称', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'yhGoodsCode', label: '公司商品编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'oldGoodsCode', label: '原值', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'newGoodsCode', label: '新值', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '操作日期', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '操作人', },
]
export default {
  name: "changeBindingLog",
  components: {
    MyContainer, vxetablebase, inputYunhan
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        createdTimeStart: null,//开始时间
        createdTimeEnd: null,//结束时间
        nameManufacturer: null,//供应商名称
        newGoodsCode: null,//新商品编号
        oldGoodsCode: null,//原商品编号
        createdUserName: null,//换绑人
        styleCode:null,
        productName:null,
        yhGoodsCode:null,
        goodsCode:null,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      isExport:false,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    callbackGoodsCode(val, type) {
      const map = {
        newGoodsCode: () => (this.ListInfo.newGoodsCode = val),
        oldGoodsCode: () => (this.ListInfo.oldGoodsCode = val),
        nameManufacturer: () => (this.ListInfo.nameManufacturer = val),
        styleCode: () => (this.ListInfo.styleCode = val),
        productName: () => (this.ListInfo.productName = val),
        yhGoodsCode: () => (this.ListInfo.yhGoodsCode = val),
        goodsCode: () => (this.ListInfo.goodsCode = val),
        createdUserName: () => (this.ListInfo.createdUserName = val),
      };
      map[type]?.();
    },
    async changeTime(e) {
      this.ListInfo.createdTimeStart = e ? e[0] : null
      this.ListInfo.createdTimeEnd = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        this.ListInfo.createdTimeStart = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.createdTimeEnd = dayjs().subtract(0, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.createdTimeStart, this.ListInfo.createdTimeEnd]
      }
      this.loading = true
      const { data, success } = await getSampleGoodsLog(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
     //导出数据,使用时将下面的方法替换成自己的接口
     async exportProps() {
      this.isExport = true
      await exportSampleGoodsLog(this.ListInfo).then(({ data }) => {
        if (data) {
          const aLink = document.createElement("a");
          let blob = new Blob([data], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', '操作日志_' + new Date().toLocaleString() + '.xlsx')
          aLink.click()
          this.isExport = false
        }
      }).catch(() => {
        this.isExport = false
      })
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
