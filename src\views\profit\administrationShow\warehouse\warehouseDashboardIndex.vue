<template>
  <my-container>
    <el-tabs v-model="activeName" style="height:95%;">
      <el-tab-pane label="OA台账" name="first1" style="height: 100%;">
        <ledgerOA ref="refledgerOA" />
      </el-tab-pane>
      <el-tab-pane label="水电费" name="first2" style="height: 100%;" lazy>
        <waterElectricityCharges ref="refwaterElectricityCharges" />
      </el-tab-pane>

      <el-tab-pane label="宿舍住宿" name="first4" style="height: 100%;" lazy>
        <dshushezhushu ref="dshushezhushu" />
      </el-tab-pane>
      <el-tab-pane label="稽查情况数据" name="first5" style="height: 100%;" lazy>
        <ejichaqingkuangshuju ref="ejichaqingkuangshuju" />
      </el-tab-pane>

      <el-tab-pane label="仓储乐捐" name="first3" style="height: 100%;" lazy>
        <warehouseDonation ref="refwarehouseDonation" />
      </el-tab-pane>

      <el-tab-pane label="工伤台账数据" name="first6" style="height: 100%;" lazy>
        <fgongsitaizhangshuju ref="fgongsitaizhangshuju" />
      </el-tab-pane>
      <el-tab-pane label="固定资产" name="first7" style="height: 100%;" lazy>
        <hgudingzichan ref="hgudingzichan" />
      </el-tab-pane>
      <el-tab-pane label="三方比价数据" name="first8" style="height: 100%;" lazy>
        <isanfangbijiaoshuju ref="isanfangbijiaoshuju" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import ledgerOA from './components/aTaiZhang/ledgerOA.vue'
import waterElectricityCharges from './components/bshuidian/waterElectricityCharges.vue'
import warehouseDonation from './components/clejuan/warehouseDonation.vue'

import dshushezhushu from './components/dshushezhushu/index.vue'
import ejichaqingkuangshuju from './components/ejichaqingkuangshuju/index.vue'

import fgongsitaizhangshuju from './components/fgongsitaizhangshuju/index.vue'
import hgudingzichan from './components/hgudingzichan/index.vue'
import isanfangbijiaoshuju from './components/isanfangbijiaoshuju/index.vue'
export default {
  name: "officeDashboardIndex",
  components: {
    MyContainer, ledgerOA, waterElectricityCharges, warehouseDonation, dshushezhushu, ejichaqingkuangshuju,
    isanfangbijiaoshuju, fgongsitaizhangshuju, hgudingzichan
  },
  data() {
    return {
      activeName: 'first1',
    };
  },
  methods: {

  },
};
</script>
<style lang="scss" scoped></style>
