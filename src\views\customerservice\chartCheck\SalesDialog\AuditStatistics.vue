<template>
    <my-container>
        <!--列表-->
        <Ces-table ref="table" :that="that" :isIndex="true" @sortchange="sortchange" :tableData="tableData"
            :showsummary="true" :tableCols="tableCols" :loading="listLoading" style="width: 100%;
    height: calc(100% - 10%); margin: 0">
        </Ces-table>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getPageList" />
        </template>

      <ResultDialog v-if="resultdialogVisible" :isShow="resultdialogVisible" @closeDialog="resultdialogVisible = false"
                    ref="resultRef">
      </ResultDialog>

    </my-container>
</template>

<script>
//审核总量
import {
  getDataStatisticsPopupPageList,
  getJudgmentDataStatisticsPopupPageList, GetUnpayOrderSalesList
} from "@/api/customerservice/chartCheck";
import MyContainer from "@/components/my-container";
import CesTable from "@/components/Table/table.vue";
import ResultDialog from "@/views/customerservice/chartCheck/resultDialog.vue";

const platformList = [
    { name: "拼多多", value: 2 },
    { name: "抖音", value: 6 },
    { name: "天猫", value: 1 },
    // { name: "淘工厂", value: 8 },
    { name: "淘宝", value: 9 },
]
const tableCols = [
    {
        istrue: true,
        prop: "conversationId",
        label: "数据编码",
        sortable: "custom",
        // width: "180",
    },
    {
        istrue: true,
        prop: "orderNo",
        label: "线上订单号",
        sortable: "custom",
        // width: "180",
    },
    {
        istrue: true,
        display: true,
        prop: "proId",
        label: "宝贝ID",
        sortable: "custom",
        type: "html",
        // width: "150",
    },
    {
        istrue: true,
        prop: "platform",
        label: "平台",
        // width: "60",
        sortable: "custom",
        formatter: (row) => {
            return platformList.filter(item => item.value == row.platform)[0].name
        },
    },
    {
        istrue: true,
        prop: "shopName",
        label: "店铺",
        // width: "120",
        sortable: "custom",
    },
    {
        istrue: false,
        prop: "userName",
        label: "使用账号人",
        // width: "120",
        sortable: "custom",
    },
    {
        istrue: false,
        prop: "groupName",
        label: "分组",
        // width: "120",
        sortable: "custom",
    },
    {
        istrue: true,
        label: "查看",
        type:'button',
        btnList:[
          {
            label:'查看',
            handle: (that, row) => that.openResultLog(row),
          }
        ]
    },

];

export default {
    props: {
        totalROW: { type: Object, default: () => { } },
    },
    components: {ResultDialog, MyContainer, CesTable },
    data() {
        return {
            that: this,
            tableData: [],
            summaryarry: null,
            tableCols: [],
            listLoading: false,
            total: 0,
            platformList: platformList,
            resultdialogVisible: false,

        }
    },
    mounted() {
        this.onSearch();

    },
    methods: {
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getPageList();
        },
        sortchange(column) {    //表头排序
            if (!column.order) this.pager = {};
            else
                this.pager = {
                    orderBy: column.prop,
                    isAsc: column.order.indexOf("descending") == -1 ? true : false,
                };
            this.onSearch();
        },
        async getPageList() {
            console.log("this.totalROW",this.totalROW)
            this.tableCols = tableCols
            var pager = this.$refs.pager.getPager();
            var paramsPre = {
                ...pager,
                ...this.pager,
                timeStart: this.totalROW.timeStart,
                timeEnd: this.totalROW.timeEnd,
                userName: this.totalROW.operator,
                groupName: this.totalROW.groupName,
                isPdd:this.totalROW.isPdd,
                totalType:this.totalROW.totalType
             }

            // var paramsAfter = {
            //     ...pager,
            //     ...this.pager,
            //     switchshow: this.totalROW.switchshow,
            //     timeStart: this.totalROW.timeStart,
            //     timeEnd: this.totalROW.timeEnd,
            //     userName: this.totalROW.operator,
            //     groupName: this.totalROW.initialOperatorGroupName,
            //     isPdd:this.totalROW.isPdd,
            //     totalType:this.totalROW.totalType

            // }
            this.listLoading = true;
            console.log("this.totalROW.num",this.totalROW.totalType)
            const res = this.totalROW.switchshow ? await getDataStatisticsPopupPageList(paramsPre) : await getJudgmentDataStatisticsPopupPageList(paramsPre);
            this.listLoading = false;
            this.total = res.data.total
            this.tableData = res.data.list;
        },
      async openResultLog(row) {
          console.log("row",row);
        if (!this.$refs.resultRef) {
          // 如果resultRef不存在，先设置为true确保组件被渲染
          this.resultdialogVisible = true;
          // 使用nextTick等待DOM更新后再操作
          this.$nextTick(() => {
            this.setResultDialogData(row);
          });
          return;
        }
        this.setResultDialogData(row);
      },

      async setResultDialogData(row) {
        this.$refs.resultRef.dataJson = row
        this.$refs.resultRef.isShowOrHide = true
        this.$refs.resultRef.tableData = this.tableData;
        this.$refs.resultRef.salesType = row.salesType;
        if (row.conversationUserId) {
          var params = {
            conversationUserId: row.conversationUserId,
            conversationId: row.conversationId,
            salesType: row.salesType,
            shopId: row.shopId,
            auditState:2,
            orderBy: "createdTime",
            isAsc: false
          }
          //获取审核的聊天数据
          const res = await GetUnpayOrderSalesList(params);
          if (!res?.success) {
            return;
          }
          this.$refs.resultRef.reviewList = res.data.list;
        } else {
          this.$refs.resultRef.reviewList = [];
        }
        this.$refs.resultRef.chatRecordKeywords = this.totalROW?.chatRecordKeywords || ''; // 修复Filter未定义的问题
        this.resultdialogVisible = true;
      },

      async getUnpayOrderSalesList(params, row) {
        const res = await GetUnpayOrderSalesList(params);
        if (!res?.success) {
          return;
        }
        this.$refs.resultRef.reviewList = res.data.list;
        this.resultdialogVisible = true;
      },

    },


}
</script>

<style></style>
