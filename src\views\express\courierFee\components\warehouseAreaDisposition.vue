<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.warehouseArea" placeholder="仓区" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="onAddMethod">新增</el-button>
        <!-- <el-button type="primary" @click="startImport">导入</el-button> -->
      </div>
    </template>
    <vxetablebase :ispoint="false" :id="'warehouseAreaDisposition202411181135'"
      :tablekey="'warehouseAreaDisposition202411181135'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="onEdit(row)">编辑</el-button>
              <el-button type="text" @click="onDelete(row)"><span style="color: red;">删除</span></el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="ruleTitle" :visible.sync="newEditVisible" width="35%" v-dialogDrag>
      <div style="height: 170px;padding: 30px 5px 0 5px;">
        <el-form :model="ruleForm" :rules="editrules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="快递公司" :label-width="'125px'" prop="expressCompanyId">
                <el-select v-model="ruleForm.expressCompanyId" placeholder="快递公司" class="editCss" clearable
                  :disabled="ruleTitle == '编辑'" @change="getprosimstatelist(1)">
                  <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item label="发货仓" :label-width="'125px'" prop="warehouseId">
                <el-select v-model="ruleForm.warehouseId" clearable filterable :disabled="ruleTitle == '编辑'"
                  placeholder="请选择发货仓库" class="editCss">
                  <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="快递公司站点" :label-width="'125px'" prop="prosimstateId">
                <el-select v-model="ruleForm.prosimstateId" placeholder="请选择快递公司站点" clearable
                  :disabled="ruleTitle == '编辑'" class="editCss">
                  <el-option label="暂无站点" value="" />
                  <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName"
                    :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item label="仓区" :label-width="'125px'" prop="warehouseArea">
                <el-input v-model.trim="ruleForm.warehouseArea" placeholder="请输入仓区" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="newEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="onStorageMethodDebounced">确 定</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import { addOrEditWarehouseAreaConfiguration, deleteWarehouseAreaConfigurationBatchAsync, importWarehouseAreaConfigurationsync, getExpressComanyAll, getExpressComanyStationName, getWarehouseAreaConfigurationList } from "@/api/express/express";
import dayjs from 'dayjs'
import { formatTime } from "@/utils";
import { warehouselist, formatWarehouseNew } from "@/utils/tools";
const tableCols = [
  { sortable: 'custom', width: '200', align: 'center', prop: 'expressCompanyId', label: '快递公司', formatter: (row) => row.expressCompany ? row.expressCompany : '', },
  { sortable: 'custom', width: '200', align: 'center', prop: 'prosimstateId', label: '快递公司站点', formatter: (row) => row.prosimstate ? row.prosimstate : '', },
  { sortable: 'custom', width: '200', align: 'center', prop: 'warehouseId', label: '发货仓', formatter: (row) => formatWarehouseNew(row.warehouseId), },
  { sortable: 'custom', width: '200', align: 'center', prop: 'warehouseArea', label: '仓区', },
]
export default {
  name: "warehouseAreaDisposition",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      fileList: [],
      fileparm: {},
      uploadLoading: false,
      dialogVisible: false,
      ruleTitle: '新增',
      ruleForm: {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
        warehouseArea: null,
        id: null,
      },
      editrules: {
        expressCompanyId: [{ required: true, message: '请选择快递公司', trigger: 'change' }],
        prosimstateId: [{ required: true, message: '请选择快递公司站点', trigger: 'change' }],
        warehouseId: [{ required: true, message: '请选择发货仓库', trigger: 'change' }],
        warehouseArea: [{ required: true, message: '请输入仓区', trigger: 'blur' }],
      },
      newEditVisible: false,//新增编辑弹窗
      formatWarehouseNew,
      warehouselist,//发货仓
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        warehouseArea: null,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      expresscompanylist: [],//快递公司
      prosimstatelist: [],//快递公司站点
    }
  },
  async mounted() {
    await this.getList()
    await this.init()
  },
  methods: {
    //防抖
    onStorageMethodDebounced: _.debounce(function (param) {
      this.onSingleSave(param);
    }, 1000),
    onSingleSave() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          const { success } = await addOrEditWarehouseAreaConfiguration(this.ruleForm)
          if (success) {
            this.$message.success('操作成功')
            this.newEditVisible = false
            this.getList()
          } else {
            this.$message.error('操作失败')
          }
        }
      })
    },
    onCleardataMethod() {
      this.ruleForm = {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
        warehouseArea: null,
        id: null,
      }
    },
    onAddMethod() {
      this.onCleardataMethod()
      this.ruleTitle = '新增'
      this.newEditVisible = true
    },
    async onDelete(row) {
      this.$confirm('是否删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await deleteWarehouseAreaConfigurationBatchAsync({ batchNumber: row.batchNumber })
        if (success) {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      });
    },
    async onEdit(row) {
      this.onCleardataMethod()
      this.loading = true
      let res = await getExpressComanyStationName({ id: row.expressCompanyId });
      this.loading = false
      if (res?.code) {
        this.prosimstatelist = res.data
      }
      setTimeout(() => {
        this.ruleForm = JSON.parse(JSON.stringify(row))
        this.ruleForm.expressCompanyId = String(this.ruleForm.expressCompanyId);
        this.ruleForm.prosimstateId = String(row.prosimstateId);
        this.ruleForm.warehouseId = Number(row.warehouseId);
        this.ruleForm.id = row.id
        this.ruleTitle = '编辑'
        this.newEditVisible = true
      }, 100)
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importWarehouseAreaConfigurationsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async init() {
      const res = await getExpressComanyAll({});
      if (!res?.success) return
      this.expresscompanylist = res.data;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给当前月第一天至今天
        // this.ListInfo.startTime = dayjs().startOf('month').format('YYYY-MM-DD')
        // this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        // this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await getWarehouseAreaConfigurationList({ ...this.ListInfo })
      this.loading = false
      if (success && data && data.list) {
        this.tableData = data.list
        this.total = data.total
        // this.summaryarry = data.summary

        let summary = data.summary || {}

        const resultsum = {};
        Object.entries(summary).forEach(([key, value]) => {
          resultsum[key] = formatNumber(value);
        });
        function formatNumber(number) {
          const options = {
            useGrouping: true,
          };
          return new Intl.NumberFormat('zh-CN', options).format(number);
        }
        this.summaryarry = resultsum
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async getprosimstatelist(val) {
      let id;
      if (val == 1) {
        id = this.ruleForm.expressCompanyId
        this.ruleForm.prosimstateId = null
      } else if (val == 2) {
        id = this.ListInfo.expressCompanyId
        this.ListInfo.prosimstateId = null
      }
      let res = await getExpressComanyStationName({ id: id });
      if (res?.code) {
        this.prosimstatelist = res.data
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 170px;
    margin-right: 5px;
  }
}

.editCss {
  width: 90%;
}
</style>
