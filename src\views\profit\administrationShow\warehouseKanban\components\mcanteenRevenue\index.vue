<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="ListInfo.calculateMonthArr" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" type="monthrange" style="width: 250px;margin-right: 5px;" :clearable="false"
          :value-format="'yyyy-MM'">
        </el-date-picker>
        <el-select v-model="ListInfo.category" style="width: 200px;" clearable filterable placeholder="类目"
          class="publicCss">
          <el-option v-for="item in categoryList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-button type="primary" @click="getList">查询</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" @click="downExcel">模板下载</el-button>
        <el-button type="primary" @click="exportExcel">导出</el-button>
      </div>
    </template>

    <vxe-table border show-footer width="100%" height="100%" ref="newtable" :row-config="{ height: 40 }" show-overflow
      :loading="loading" :column-config="{ resizable: true }" :span-method="mergeRowMethod"
      :row-class-name="rowClassName" :data="tableData" :footer-data="footerData">
      <vxe-column field="month" width="300" title="月份"></vxe-column>
      <vxe-column field="category" width="300" title="类目"></vxe-column>
      <vxe-column field="expenseAmount" width="300" title="支出金额">
        <template slot-scope="scope">
          {{ scope.row.expenseAmount ? (scope.row.expenseAmount.toFixed(2)) : '0' }}
        </template>
      </vxe-column>
      <vxe-column field="incomeAmount" width="300" title="收入金额">
        <template slot-scope="scope">
          {{ scope.row.incomeAmount ? (scope.row.incomeAmount.toFixed(2)) : '0' }}
        </template>
      </vxe-column>
      <vxe-column title="操作" footer-align="left" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" size="mini" style="color:red" @click="handleRemove(scope.row)">删除</el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <el-drawer title="编辑" :visible.sync="dialogVisibleEdit" size="25%">
      <mcanteenRevenueEdit ref="mcanteenRevenueEdit" v-if="dialogVisibleEdit" :editInfo="editInfo"
        @search="closeGetlist" @cancellationMethod="dialogVisibleEdit = false" />
    </el-drawer>
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import dayjs from 'dayjs'
import { downloadLink } from "@/utils/tools.js";
import { warehouseCanteenRevenueDataPage, warehouseCanteenRevenueDataImport, warehouseCanteenRevenueDataRemove } from '@/api/people/peoplessc.js';
import mcanteenRevenueEdit from "./mcanteenRevenueEdit.vue";
export default {
  name: "mcanteenRevenueIndex",
  components: {
    MyContainer, mcanteenRevenueEdit
  },
  props: {
  },
  data() {
    return {
      // 工具函数
      downloadLink,
      // 对话框状态
      dialogVisibleEdit: false,
      dialogVisible: false,
      // 编辑相关
      editInfo: {},
      // 文件上传
      fileList: [],
      uploadLoading: false,
      // 表格数据
      tableData: [],
      footerData: [],
      // 下拉选项数据
      categoryList: [],
      // 加载状态
      loading: false,
      exportloading: false,
      // 查询条件
      ListInfo: {
        calculateMonthArr: [
          dayjs().subtract(1, 'month').format('YYYY-MM'),
          dayjs().subtract(1, 'month').format('YYYY-MM')
        ],
        startMonth: null,
        endMonth: null,
        category: '',
      }
    }
  },


  async mounted() {
    await this.getList()
  },
  methods: {
    //上传文件
    onUploadRemove() {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess() {
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      try {
        const form = new FormData();
        form.append("file", item.file);
        const res = await warehouseCanteenRevenueDataImport(form);
        if (res?.success) {
          this.$message({ message: res.msg || "导入成功", type: "success" });
          this.dialogVisible = false;
          await this.getList()
        } else {
          this.$message({ message: res?.msg || "导入失败", type: "error" });
        }
      } catch (error) {
        console.error('文件上传失败:', error);
        this.$message({ message: "文件上传失败，请重试", type: "error" });
      } finally {
        this.uploadLoading = false
      }
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    downExcel() {
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250719/1946512597428183041.xlsx', '食堂收支汇总表-导入模板.xlsx');
    },
    async exportExcel() {
      this.exportloading = true;
      this.$refs.newtable.exportData({ filename: '仓储行政看板-食堂收支汇总表' + new Date().toLocaleString(), sheetName: 'Sheet1', type: 'xlsx' })
      this.$nextTick(() => {
        this.exportloading = false;
      })
    },
    closeGetlist() {
      this.dialogVisibleEdit = false;
      this.getList()
    },
    handleEdit(row) {
      this.editInfo = row;
      this.dialogVisibleEdit = true;
    },
    async handleRemove(row) {
      this.$confirm('是否删除！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        if (!row || !row.id) {
          this.$message.error('删除失败：数据异常');
          return;
        }
        try {
          this.loading = true
          const { success, msg } = await warehouseCanteenRevenueDataRemove({ ids: row.id })

          if (success) {
            this.$message.success(msg || '删除成功')
            await this.getList();
          } else {
            this.$message.error(msg || '删除失败')
          }
        } catch (error) {
          console.error('删除失败:', error);
          this.$message.error('删除失败，请重试');
        } finally {
          this.loading = false
        }
      }).catch(() => {
      });
    },


    async getList() {
      try {
        this.prepareListParams();// 查询条件
        this.loading = true
        const { data, success, msg } = await warehouseCanteenRevenueDataPage(this.ListInfo)
        if (success && data) {
          this.tableData = data.list || []
          this.footerData = data.summary || []
          // 更新下拉选项
          this.updateDropdownOptions();
        } else {
          this.$message.error(msg || '获取列表失败')
          this.tableData = []
          this.footerData = []
        }
      } catch (error) {
        console.error('获取列表失败:', error);
        this.$message.error('获取列表失败，请重试');
        this.tableData = []
        this.footerData = []
      } finally {
        this.loading = false
      }
    },

    // 准备查询参数
    prepareListParams() {
      const { calculateMonthArr } = this.ListInfo;
      if (calculateMonthArr?.length >= 2) {
        this.ListInfo.startMonth = calculateMonthArr[0];
        this.ListInfo.endMonth = calculateMonthArr[1];
      } else {
        this.ListInfo.startMonth = null;
        this.ListInfo.endMonth = null;
      }
    },

    // 更新下拉选项
    updateDropdownOptions() {
      if (!this.tableData?.length) return;
      const categorySet = new Set(this.categoryList);
      this.tableData.forEach(item => {
        if (item.category) {
          categorySet.add(item.category);
        }
      });
      this.categoryList = Array.from(categorySet);
    },

  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

:deep(.vxe-header--column) {
  background: #00937e;
  color: white;
  font-weight: 600;
}

:deep(.vxe-footer--row) {
  background: #00937e;
  color: white;
  font-weight: 600;
}
</style>
