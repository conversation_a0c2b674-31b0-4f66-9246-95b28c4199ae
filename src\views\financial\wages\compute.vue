<template>
  <container>
       <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='true'
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry'
              :loading="listLoading">
         <template slot='extentbtn'>
          <el-button-group>
            <!-- <el-button style="padding: 0;margin: 0;">
                <el-date-picker style="width: 110px" v-model="filter1.yearmonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
            </el-button> -->
            <el-button style="padding: 0;margin: 0;">
                <el-select filterable v-model="filter1.version" placeholder="类型" style="width: 100px">
                  <el-option label="工资月报" value="v1"></el-option>
                  <el-option label="参考月报" value="v2"></el-option>
               </el-select>
            </el-button> 
            <el-button style="padding: 0;margin: 0;"><el-input style="width: 100px" v-model="filter1.proCode"  placeholder="产品ID"/></el-button>
            <el-button style="padding: 0;margin: 0;"><el-input style="width: 100px" v-model="filter1.platform"  placeholder="平台"/></el-button>
            <el-button style="padding: 0;margin: 0;"><el-input style="width: 100px" v-model="filter1.originId"  placeholder="源Id"/></el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select filterable clearable v-model="filter1.groupId" placeholder="运营组" style="width: 110px">
                 <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select filterable clearable v-model="filter1.shopId" placeholder="店铺" style="width: 110px">
                <el-option label="全部" value/>
                <el-option v-for="item in shoplist" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"/>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select filterable clearable v-model="filter1.type" placeholder="业务" style="width: 110px">
                  <el-option label="运营组工资" value='13'/>
                  <el-option label="加工部工资" value='11'/>
                  <el-option label="护墙角工资" value='12'/>
                  <el-option label="美工提成" value='14'/>
                  <el-option label="新媒体提成" value='15'/>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select filterable clearable v-model="filter1.shareOper" placeholder="分摊方式" style="width: 110px">
                  <el-option label="按源数据分摊" value='0'/>
                  <el-option label="按产品ID分摊" value='1'/>
                  <el-option label="按店铺分摊" value='2'/>
                  <el-option label="按组分摊" value='3'/>
                  <el-option label="按所有组分摊" value='4'/>
                  <el-option label="按商品编码分摊" value='5'/>
                  <el-option label="按采购单分摊" value='6'/>
              </el-select>
            </el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onExport">导出</el-button>
          </el-button-group>
        </template>
       </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
  </container>
</template>
<script>
import {pageWagesCompute,exportWagesComputeAsync} from '@/api/financial/wages'
import {getList as getshopList } from '@/api/operatemanage/base/shop'
import {getDirectorGroupList} from '@/api/operatemanage/base/shop'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import {formatPlatform,formatTime,formatFeeShareOper,formatShareFeeType} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'yearMonth',label:'年月', width:'70',sortable:'custom'},
      {istrue:true,prop:'version',label:'版本', width:'80',sortable:'custom'},
      {istrue:true,prop:'proCode',label:'宝贝ID', width:'120',sortable:'custom'},
      //{istrue:true,prop:'platform',label:'平台', width:'80'},
      {istrue:true,prop:'originId',label:'源ID', width:'80',sortable:'custom'},
      // {istrue:true,prop:'groupId',label:'运营组', width:'70',sortable:'custom',formatter:(row)=>{return row.groupName}},
      // {istrue:true,prop:'shopId',label:'店铺', width:'100',sortable:'custom',formatter:(row)=>{return row.shopName}},
      {istrue:true,prop:'type',label:'业务', width:'80',sortable:'custom',formatter:(row)=>formatShareFeeType(row.type)},
      {istrue:true,prop:'shareOper',label:'分摊方式', width:'100',sortable:'custom',formatter:(row)=>formatFeeShareOper(row.shareOper)},
      // {istrue:true,prop:'predictAmont',label:'预估分摊', width:'80'},
      {istrue:true,prop:'actualAmont',label:'实际分摊', width:'80',sortable:'custom'},
      {istrue:true,prop:'createdTime',label:'计算时间', width:'150',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD HH:mm:ss')},
     ];
const tableHandles=[
      ];
export default {
  name: 'Roles',
  components: {cesTable, container },
   props:{
       filter: { }
     },
  data() {
    return {
      filter1: {
        proCode:null,
        platform:null,
        originId:null,
        groupId:null,
        shopId :null,
        type :null,
        shareOper :null
       },
      purchaseFreight:{
        shareFeeType:5,
        yearmonth:null,
        amont:0,
       },
      grouplist:[],
      shoplist:[],
      that:this,
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"id",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [], 
      listLoading: false,
      pageLoading: false,
      dialogExmainPurchaseFreightVisible:false
    }
  },
  async mounted() {
     await this.init()
    await this.onSearch()
  },
  beforeUpdate() { },
  methods: {
    async init(){
        this.shoplist =[]
        const res1 = await getshopList({isOpen:1,platform:1,CurrentPage:1,PageSize:100});
        res1?.data?.list.forEach(f=>{this.shoplist.push(f)})
       
        const res11 = await getshopList({isOpen:1,platform:2,CurrentPage:1,PageSize:100});
        res11?.data?.list.forEach(f=>{this.shoplist.push(f)})

        var res2= await getDirectorGroupList();
        this.grouplist = res2.data.map(item => {return { value: item.key, label: item.value };}); 
    },
   async onSearch() {
      this.$refs.pager.setPage(1)
     await this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager, ...this.pager,... this.filter, ... this.filter1}
      this.listLoading = true
      const res = await pageWagesCompute(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
    async onExport() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager, ...this.pager,... this.filter, ... this.filter1}
      var res = await exportWagesComputeAsync(params);
      if (!res?.data) {
          //this.$message({ message: "没有数据", type: "warning" });
          return
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '明细数据' + new Date().toLocaleString() + '_.xlsx')
      aLink.click()
    },
   async onExmainPurchaseFreight(){
      this.dialogExmainPurchaseFreightVisible=true;
    },
   async oncomputPurchaseFreight(){
      if (!this.purchaseFreight.yearmonth) {
         this.$message({type: 'warning',message: '请选择月份!'});
         return;
      }
      if (!this.purchaseFreight.amont) {
         this.$message({type: 'warning',message: '请输入金额!'});
         return;
      }
      await pageCompute(this.purchaseFreight)
          .then(res=>{
                if (res.success)
                  this.$message({type: 'success',message: '提交完成，正在后台计算...!'});
                else
                  this.$message({type: 'success',message: '提交失败!'});
              });
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async onbatchDelete() {
       await this.$emit('ondeleteByBatch',this.shareFeeType);
    },
   async oncomput(){
      this.$emit('onstartcomput',this.shareFeeType);
   },
   async onimport(){
     await this.$emit('onstartImport',this.shareFeeType);
   },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
  }
}
</script>
