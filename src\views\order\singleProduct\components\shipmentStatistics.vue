<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss" />
                <el-select v-model="ListInfo.wmsType" placeholder="类型" class="publicCss" clearable>
                    <el-option label="本仓" value="本仓" />
                    <el-option label="外仓" value="外仓" />
                </el-select>
                <chooseWareHouse v-model="ListInfo.wmsIds" style="width: 300px;" multiple class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
                    <el-date-picker style="width: 160px;margin: 0 5px 0 70px;" v-model="calculateDate" type="date" format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
                    <el-button type="primary" @click="onAccounting">统计</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" id="**************" :loading="loading" :that="that" :is-index="true" :hasexpand="true"
            :tablefixed="true" :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols"
            :condition="ListInfo" :is-selection="false" :is-select-column="true" :is-index-fixed="false"
            style="width: 100%; margin: 0;" height="100%" :showsummary="data.summary ? true : false"
            :showTrendChart="false" :summaryarry="data.summary" @onTrendChart="trendChart" @sortchange="sortchange">
            <template slot="right">
                <vxe-column title="操作" width="80" fixed="right">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="() => {
                                isSummary = false
                                $refs.table.onTrendChart(row)
                            }">趋势图</el-button>
                        </div>
                    </template>
                    <template #footer="{ row, _columnIndex }">
                        <el-button type="text" @click="() => {
                            isSummary = true
                            $refs.table.onTrendChart(data.list[0])
                        }">趋势图</el-button>
                    </template>
                </vxe-column>
            </template>
            <template #wmsId="{ row }">
                <div>
                    {{ row.wmsName }}
                </div>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-drawer :title="chatProp.data._title" :visible.sync="chatProp.chatDialog" size="90%"
            :close-on-click-modal="false" direction="btt">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" :clearable="false"
                    start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                    :picker-options="pickerOptions" style="margin: 10px" @change="
                        trendChart({
                            ...chatPropOption,
                            startDate: $event[0],
                            endDate: $event[1],
                        }, row)
                        " />
                <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data"
                    :thisStyle="{ width: '100%', height: '690px', 'box-sizing': 'border-box', 'line-height': '360px' }" />
            </div>
            <div v-else v-loading="chatProp.chatLoading" />
        </el-drawer>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/OrderGoods/WmsStat/'
import { mergeTableCols } from '@/utils/getCols'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import decimal from '@/utils/decimalToFixed'
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, chooseWareHouse
    },
    data() {
        return {
            api,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'orderRate',
                isAsc: false,
                summarys: [],
                startDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
                endDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
                wmsType: '外仓',
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: {}, // 趋势图数据
            },
            calculateDate: null,
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            isSummary: false,
            row: {},
        }
    },
    async mounted() {
        await this.getCol();
        await this.getList()
    },
    methods: {
        async onAccounting() {
          if(!this.calculateDate) {
              this.$message.error('请选择日期')
              return
          }
          const { success } = await request.post(`/api/verifyOrder/OrderGoods/DurationStat/StatDaySend`,{Date:dayjs(this.calculateDate).format('YYYY-MM-DD')});
          if (success) {
              this.$message.success('统计成功')
              this.getList()
          } else {
              this.$message.error('统计失败')
          }
        },
        async trendChart(option, row) {
            this.row = JSON.parse(JSON.stringify(row));
            var endDate = null;
            var startDate = null;
            if (option.startDate && option.endDate) {
                startDate = option.startDate;
                endDate = option.endDate;
            } else {
                endDate = option.date;
                startDate = new Date(option.date);
                startDate.setDate(option.date.getDate() - 30);

                startDate = dayjs(startDate).format("YYYY-MM-DD");
                endDate = dayjs(endDate).format("YYYY-MM-DD");
            }
            option.filter.filters = option.filter.filters.filter((item) => item.field !== option.dateField);
            option.filter.filters.push({
                field: option.dateField,
                operator: "GreaterThanOrEqual",
                value: startDate,
            });
            option.filter.filters.push({
                field: option.dateField,
                operator: "LessThanOrEqual",
                value: endDate,
            });
            if (this.isSummary) {
                option.filter.filters = option.filter.filters.filter((item) => option.key.indexOf(item.field) <= -1);
            }
            option.key = !this.isSummary ? option.key : [];
            option.startDate = startDate;
            option.endDate = endDate;
            this.chatProp.chatTime = [startDate, endDate];
            this.chatProp.chatLoading = true;
            const { data, success } = await await request.post(`${this.api}GetTrendChart`, option);
            if (success) {
                if (option.key?.length > 0) {
                    data._title = this.row.wmsName + ' - 趋势图'
                } else {
                    data._title = '汇总趋势图'
                }
                data.title = null
                this.chatProp.data = data;
            }
            this.chatProp.chatLoading = false;
            this.chatProp.chatDialog = true;
            this.chatPropOption = option;
        },
        proCodeCallback(val) {
            this.ListInfo.proCode = val
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                this.tableCols = mergeTableCols(data)
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    for (let key in data.summary) {
                        if (key == 'orderRate_sum') {
                            data.summary[key] = data.summary[key] !== null ? decimal(data.summary[key], 100, '*') + '%' : ''
                        }
                        if (key == 'date_sum') {
                            data.summary[key] = data.summary[key] !== null ? String(data.summary[key]) : ''
                        }
                    }
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0;
    }
}
</style>
