<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-button-group>
                    <!-- <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="filter.showType" style="width:160px;">
                            <el-option :key=1 :value=1 label="竞品汇总模式"></el-option>
                            <el-option :key=2 :value=2 label="建编码明细模式"></el-option>
                        </el-select>
                    </el-button> -->
                    <el-button style="padding: 0;margin: 0;">
                        <el-date-picker style="width: 240px" v-model="filter.timerange" type="datetimerange"
                            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="审核时间"
                            end-placeholder="审核时间" :clearable="false"></el-date-picker>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:100px;" v-model="filter.goodsState" placeholder="产品状态" :clearable="true"
                            filterable>
                            <el-option label="全部" :value="null"></el-option>
                            <el-option label="新品" :value="1"></el-option>
                            <el-option label="老品补SKU" :value="2"></el-option>
                            <el-option label="代拍" :value="3"></el-option>
                        </el-select>
                    </el-button>

                    <!-- <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="filter.newPlatform" placeholder="平台" :clearable="true" :collapse-tags="true" style="width: 122px" filterable>
                            <el-option v-for="item in platformList" :key="'plat-'+item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button> -->

                    <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="filter.newPlatforms" placeholder="运营平台" :clearable="true"
                            :collapse-tags="true" multiple style="width: 140px" filterable>
                            <el-option v-for="item in platformList" :key="'plat-' + item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="filter.projectDept" style="width:172px;" multiple collapse-tags clearable
                            placeholder="请选择项目">
                            <el-option label="项目部-大侠" value="项目部-大侠" />
                            <el-option label="项目部-徐琛" value="项目部-徐琛" />
                            <el-option label="项目部-左玉玲" value="项目部-左玉玲" />
                            <el-option label="未设置" value="未设置" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model.trim="filter.goodsCompeteId" :maxlength="40" clearable placeholder="竞品ID"
                            style="width:120px;" @keyup.enter.native="onSearch" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model.trim="filter.goodsCompeteName" :maxlength="40" clearable placeholder="竞品标题"
                            style="width:120px;" @keyup.enter.native="onSearch" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model.trim="filter.goodsCompeteShortName" :maxlength="40" clearable
                            placeholder="产品简称" style="width:120px;" @keyup.enter.native="onSearch" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model.trim="filter.yhGoodsCode" :maxlength="40" clearable placeholder="商品编码"
                            style="width:120px;" @keyup.enter.native="onSearch" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model.trim="filter.yhGoodsName" :maxlength="40" clearable placeholder="商品名称"
                            style="width:120px;" @keyup.enter.native="onSearch" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model.trim="filter.yyGroupName" :maxlength="40" clearable placeholder="运营组"
                            style="width:90px;" @keyup.enter.native="onSearch" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model.trim="filter.submitUserName" :maxlength="40" clearable placeholder="提交人"
                            style="width:90px;" @keyup.enter.native="onSearch" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model.trim="filter.applyUserName" :maxlength="40" clearable placeholder="申请人"
                            style="width:90px;" @keyup.enter.native="onSearch" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;"
                        v-if="checkPermission('api:operatemanage:alllink:SyncBuildGoodsDocDingDingCurAuditNode')">
                        <el-input v-model.trim="filter.curAuditNode" :maxlength="40" clearable placeholder="审批节点"
                            style="width:120px;" @keyup.enter.native="onSearch" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-select style="width:120px" clearable v-model="filter.auditState2" placeholder="状态">
                            <el-option :key=-1 :value=-1 label="不审核通过"></el-option>
                            <el-option :key=1 :value=1 label="已申请"></el-option>
                            <el-option :key=2 :value=2 label="审核通过"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model.trim="filter.rejectReason" :maxlength="40" clearable placeholder="拒绝原因"
                            style="width:90px;" @keyup.enter.native="onSearch" />
                    </el-button>

                    <el-button type="primary" @click="onSearch" style="margin-left: 5px;">查 询</el-button>
                    <el-button type="primary" @click="onClearSearch" style="margin-left: 5px;">清 空</el-button>
                </el-button-group>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'
            :showsummary="true" :summaryarry="summaryarry" @select='selectchange' :tableCols='tableCols'
            :loading="listLoading" rowkey="id" :treeprops="{ children: 'buildGoodsList', hasChildren: true }">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button type="primary" @click="onSyncCurAuditNode" :loading="syncCurAuditNodeLoading"
                        v-if="checkPermission('api:operatemanage:alllink:SyncBuildGoodsDocDingDingCurAuditNode')">
                        同步当前审批节点
                    </el-button>
                    <el-button type="primary" @click="onExportHotSaleGoodsBuildGoodsRejectRecords">
                        导出拒绝次数
                    </el-button>
                    <el-button type="primary" @click="WarehouseBrandCorrespondenceSet"
                        v-if="checkPermission('warehouseBrandCorrespondence')">
                        <!-- 仓库品牌对应关系 -->
                         仓库设置
                    </el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist" />
        </template>

        <el-dialog :title="dialogTitle" :visible.sync="buildgoodscaigouVisible" width='80%'
            :close-on-click-modal="false" v-dialogDrag v-loading="dialogBuildgoodscaigouLoading"
            element-loading-text="拼命加载中">
            <hotsalegoodsbuildgoodsdoccaigou ref="hotsalegoodsbuildgoodsdoccaigou" style="z-index:1000;" />
            <template #footer>
                <span class="dialog-footer">
                    <span style="font-size:10px;color:red;">点击【保存&审批通过】将执行：1.同步商品到聚水潭，2.钉钉审批同意此建编码申请（已同意自动忽略）</span>
                    <el-button @click="buildgoodscaigouVisible = false">取 消</el-button>
                    <el-button v-if="buildgoodscaigouIsEditMode" type="primary" @click="onBuildgoodscaigouSave()"
                        :loading="buildgoodscaigouSaveLoading">保存
                    </el-button>
                    <el-button
                        v-if="buildgoodscaigouIsEditMode && checkPermission('api:operatemanage:alllink:BuildGoodsDocSyncJstAndDingDingPassAsync')"
                        type="primary" @click="onBuildgoodscaigouSp()" :loading="buildgoodscaigouSaveLoading">
                        保存&审批通过
                    </el-button>
                    <!-- <el-button v-if="buildgoodscaigouIsEditMode&&checkPermission('api:operatemanage:alllink:BuildGoodsDocDingDingNoPassAsync')" type="primary" @click="onBuildgoodscaigouSpNo()" :loading="buildgoodscaigouSaveLoading">
                        驳回给运营
                    </el-button> -->
                </span>
            </template>
        </el-dialog>

        <el-dialog title="仓库品牌对应关系" :visible.sync="warehouseBrandSetVisible" width='60%' :close-on-click-modal="false"
            v-dialogDrag append-to-body>
            <warehouseBrandSet v-if="warehouseBrandSetVisible" @close="warehouseBrandSetVisible = false" />
        </el-dialog>
    </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import { platformlist, formatLinkProCode } from "@/utils/tools";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import { rulePlatform } from "@/utils/formruletools";
import {
    pageHotSaleGoodsBuildGoodsAsync, pageHotSaleGoodsBuildGoodsTreeAsync,
    syncBuildGoodsDocDingDingCurAuditNode, exportHotSaleGoodsBuildGoodsRejectRecords
} from '@/api/operatemanage/productalllink/alllink';
import warehouseBrandSet from './warehouseBrandSet.vue';
import hotsalegoodsbuildgoodsdoccaigou from '@/views/operatemanage/productalllink/skuenquiry/hotsalegoodsbuildgoodsdoccaigou.vue';
const tableCols = [
    { istrue: true, prop: 'id', label: '主键', width: '160', sortable: 'custom', display: false },
    { istrue: true, prop: 'goodsCompeteId', label: '竞品ID', width: '150', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.goodsCompeteId) },
    { istrue: true, prop: 'goodsCompeteName', label: '竞品标题', sortable: 'custom', width: '200' },
    { istrue: true, prop: 'goodsCompeteShortName', label: '产品简称', width: '100' },
    { istrue: true, prop: 'goodsCompeteImgUrl', label: '竞品图片', width: '80', type: 'image' },
    {
        istrue: true, prop: 'goodsState', label: '产品状态', width: '80', sortable: 'custom', align: 'center',
        formatter: row => { return row.goodsState == 1 ? "新品" : row.goodsState == 2 ? "老品补SKU" : row.goodsState == 3 ? "待拍" : "" }
    },
    { istrue: true, prop: 'platform', label: '平台', width: '70', sortable: 'custom', formatter: (row) => row.platformName || ' ' },
    { istrue: true, prop: 'newPlatform', label: '运营平台', width: '70', sortable: 'custom', formatter: (row) => row.newPlatformName || ' ' },
    { istrue: true, prop: 'projectDept', label: '项目', width: '90', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'yyGroupName', label: '运营组', width: '70', sortable: false },
    { istrue: true, prop: 'patentQualificationImgUrls', label: '专利资质', width: '80', type: 'images' },
    { istrue: true, prop: 'patentQualificationPdfUrls', label: '专利PDF', width: '80', type: 'files' },
    {
        istrue: true, prop: 'outerPackaLanguage', label: '外包装语言', width: '80',
        formatter: (row) => (row.outerPackaLanguage == -1 ? "无" : row.outerPackaLanguage == 1 ? "中文" : row.outerPackaLanguage == 2 ? "英文" : "")
    },
    { istrue: true, prop: 'packingImgUrls', label: '包装图片', width: '80', type: 'images' },
    { istrue: true, prop: 'ingredient', label: '产品成分', width: '70', sortable: 'custom' },
    { istrue: true, prop: 'submitUserName', label: '提交人', width: '70', sortable: false },
    { istrue: true, prop: 'submitTime', label: '提交时间', width: '150', sortable: 'custom', formatter: (row) => row.submitTime == null ? null : formatTime(row.submitTime, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'applyUserName', label: '申请人', width: '70', sortable: false },
    { istrue: true, prop: 'applyTime', label: '申请时间', width: '150', sortable: 'custom', formatter: (row) => row.applyTime == null ? null : formatTime(row.applyTime, 'YYYY-MM-DD HH:mm:ss') },
    {
        istrue: true, prop: 'curAuditNode', label: '审批节点', width: '120', sortable: false,
        permission: 'api:operatemanage:alllink:SyncBuildGoodsDocDingDingCurAuditNode'
    },
    {
        istrue: true, prop: 'curAuditNodeTime', label: '节点最新同步时间', width: '150', sortable: false,
        formatter: (row) => row.curAuditNodeTime == null ? null : formatTime(row.curAuditNodeTime, 'YYYY-MM-DD HH:mm:ss'),
        permission: 'api:operatemanage:alllink:SyncBuildGoodsDocDingDingCurAuditNode'
    },
    { istrue: true, prop: 'auditState', label: '状态', width: '85', sortable: false, formatter: (row) => row.auditStateName || ' ' },
    {
        istrue: false, type: 'button', label: '操作', width: '140',
        btnList: [
            {
                label: "填编码", handle: (that, row) => that.onEditBuildGoodsDoc(row),
                ishide: (that, row) => (row.auditState != 1 || row.isAuditStateing == 1),
                permission: "api:operatemanage:alllink:buildgoodsdocupdatecodeasync"
            },
            {
                label: "查看编码", handle: (that, row) => that.onSeeBuildGoodsDoc(row),
                ishide: (that, row) => (row.auditState == null || row.auditState == 0),
            },
        ]
    },
    { istrue: true, prop: 'isAuditStateing', label: '是否通过中', width: '100', sortable: false, formatter: (row) => row.isAuditStateing == 1 ? '处理通过中' : '正常' },
    { istrue: true, prop: 'auditTime', label: '审核时间', width: '150', sortable: 'custom', formatter: (row) => row.auditTime == null ? null : formatTime(row.auditTime, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'auditUserName', label: '审核人', width: '70', sortable: false },
    {
        istrue: true, prop: 'rejectTimes', label: '拒绝次数', width: '70', sortable: 'custom', type: 'button',
        btnList: [
            {
                htmlformatter: (row) => row.rejectTimes,
                handle: (that, row) => that.onShowRejectRecords(row),
            },
        ]
    },
    { istrue: true, prop: 'rejectReason', label: '审批拒绝原因', sortable: true, width: '150' }
];
export default {
    name: "hotsalegoodsbuildgoodscaigou",
    components: { MyContainer, MyConfirmButton, cesTable, hotsalegoodsbuildgoodsdoccaigou, warehouseBrandSet },
    data() {
        return {
            that: this,
            pageLoading: false,
            warehouseBrandSetVisible: false,
            filter: {
                auditState: [1, 2],
                showType: 2,
                timerange: [],
                startTime: null,
                endTime: null,
                newPlatform: null,
                rejectReason: null,
                newPlatforms: []
            },
            platformlist: platformlist,
            tableCols: tableCols,
            total: 0,
            list: [],
            summaryarry: [],
            pager: { OrderBy: "CreatedTime", IsAsc: false },
            sels: [], // 列表选中列
            selids: [],
            listLoading: false,
            buildgoodscaigouVisible: false,
            dialogBuildgoodscaigouLoading: false,
            buildgoodscaigouSaveLoading: false,
            buildgoodscaigouIsEditMode: false,
            syncCurAuditNodeLoading: false,
            dialogTitle: '',
            platformList: [],//平台下拉
        };
    },
    async mounted() {
        await this.setPlatform();
        this.onSearch();
    },
    computed: {

    },
    methods: {
        WarehouseBrandCorrespondenceSet() {
            this.warehouseBrandSetVisible = true
        },
        formatLinkProCode: formatLinkProCode,
        async setPlatform() {
            let pfrule = await rulePlatform();
            this.platformList = pfrule.options;
        },
        onClearSearch() {
            this.filter.goodsCompeteId = "";
            this.filter.goodsCompeteName = "";
            this.filter.goodsCompeteShortName = "";
            this.filter.yyGroupName = "";
            this.filter.submitUserName = "";
            this.filter.applyUserName = "";
            this.filter.curAuditNode = "";
            this.filter.auditState2 = null;
            this.filter.yhGoodsCode = "";
            this.filter.yhGoodsName = "";
            this.filter.timerange = [];
            this.filter.startTime = null;
            this.filter.endTime = null;
            this.filter.newPlatform = null;
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.gettbdatalist();
        },
        getParams() {
            this.filter.auditState = [-1, 1, 2];
            //this.filter.showType = 2;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }

            const para = { ...this.filter };
            let pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };

            return params;
        },
        async gettbdatalist() {
            let params = this.getParams()
            this.listLoading = true;
            const res = await pageHotSaleGoodsBuildGoodsTreeAsync(params);
            this.listLoading = false;
            this.total = res.data.total
            this.list = res.data.list;
            this.summaryarry = res.data.summary;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                var orderBy = column.prop;
                this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            this.onSearch();
        },
        selsChange: function (sels) {
            this.sels = sels
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onEditBuildGoodsDoc(row) {
            this.dialogTitle = "编辑建编码";
            this.buildgoodscaigouIsEditMode = true;
            this.buildgoodscaigouVisible = true;
            this.$nextTick(() => {
                this.$refs.hotsalegoodsbuildgoodsdoccaigou.getSkuTableData(row.id, this.buildgoodscaigouIsEditMode);
            });
        },
        async onSeeBuildGoodsDoc(row) {
            this.dialogTitle = "查看建编码";
            this.buildgoodscaigouIsEditMode = false;
            this.buildgoodscaigouVisible = true;
            this.$nextTick(() => {
                this.$refs.hotsalegoodsbuildgoodsdoccaigou.getSkuTableData(row.id, this.buildgoodscaigouIsEditMode);
            });
        },
        async onBuildgoodscaigouSave() {
            this.buildgoodscaigouSaveLoading = true;
            var succ = await this.$refs.hotsalegoodsbuildgoodsdoccaigou.saveSkuTableData();
            this.buildgoodscaigouSaveLoading = false;
            if (succ) {
                this.buildgoodscaigouVisible = false;
            }
        },
        async onBuildgoodscaigouSp() {
            this.buildgoodscaigouSaveLoading = true;
            var succ = await this.$refs.hotsalegoodsbuildgoodsdoccaigou.spSkuTableData();
            this.buildgoodscaigouSaveLoading = false;
            if (succ) {
                this.buildgoodscaigouVisible = false;
                this.onSearch();
            }
        },
        async onBuildgoodscaigouSpNo() {
            this.buildgoodscaigouSaveLoading = true;
            var succ = await this.$refs.hotsalegoodsbuildgoodsdoccaigou.spNoSkuTableData();
            this.buildgoodscaigouSaveLoading = false;
            if (succ) {
                this.buildgoodscaigouVisible = false;
                this.onSearch();
            }
        },
        async onSyncCurAuditNode() {
            this.$confirm("确认要同步吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                this.syncCurAuditNodeLoading = true;
                var succ = await syncBuildGoodsDocDingDingCurAuditNode();
                this.syncCurAuditNodeLoading = false;
                if (succ?.success) {
                    // this.$message({ message: '正在同步中...预计30秒后可查看结果', type: "success" });
                    this.$message({ message: '同步审批节点完成', type: "success" });
                    this.onSearch();
                }
            });
        },
        //查看拒绝记录
        async onShowRejectRecords(row) {
            if (row) {
                this.$showDialogform({
                    path: `@/views/operatemanage/productalllink/skuenquiry/BuildDocRejectRecordListForm.vue`,
                    title: '驳回记录',
                    autoTitle: false,
                    args: {
                        mode: 3,
                        buildDocId: row.id,
                        goodsCompeteId: row.goodsCompeteId
                    },
                    height: 600,
                    width: '80%',

                });
            }
        },
        async onExportHotSaleGoodsBuildGoodsRejectRecords() {
            let params = this.getParams()
            this.listLoading = true
            const res = await exportHotSaleGoodsBuildGoodsRejectRecords(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '建编码拒绝记录_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()

        }
    },
};
</script>