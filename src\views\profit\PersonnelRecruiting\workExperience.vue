<template>
    <div>
        <!-- 工作经历 -->
        <div class="des-box">
            <el-descriptions title="工作经历" :column="3" size="medium" :colon="false">
                <template slot="extra">
                    <!-- <el-button type="info" circle plain icon="el-icon-d-arrow-left" size="mini" @click="toggleContent"></el-button> -->
                    <!-- <el-button type="primary" circle plain icon="el-icon-plus" size="mini" v-if="isEdit"
                        @click="addRow()"></el-button> -->
                    <i @click="toggleContent()" class="el-icon-d-arrow-right"
                        :class="{ arrowTransform: !isOpen, arrowTransformReturn: isOpen }"></i>
                </template>
                <el-descriptions-item label="" span="3" v-if="isEdit">
                    <el-form :model="ruleForm" ref="workForm" label-width="90px" class="ruleForm" :rules="workForm">
                        <el-row>
                            <el-col :span="16">
                                <el-form-item label="企业名称" prop="companyName">
                                    <el-input v-model="ruleForm.companyName" placeholder="请输入企业名称" maxlength="20"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="16">
                                <el-form-item label="在职时间" prop="employmentDate">
                                    <el-date-picker v-model="ruleForm.employmentDate" type="daterange" range-separator="至"
                                        start-placeholder="开始日期" value-format="yyyy-MM-dd" clearable end-placeholder="结束日期"
                                        style="width: 100%;" @change="handleEmploymentDate" >
                                        <!-- :picker-options="pickerOptions" -->
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="16">
                                <el-form-item label="工作内容" prop="JobDescription">
                                    <el-input v-model="ruleForm.jobDescription" type="textarea" maxlength="150"
                                        placeholder="请输入工作内容"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-form-item>
                            <el-button type="primary" @click="addRow">添加</el-button>
                        </el-form-item>
                    </el-form>
                </el-descriptions-item>
                <el-descriptions-item label="" span="3" v-if="workExperience.workExperienceList.length == 0">
                    暂无工作经历信息
                </el-descriptions-item>
                <el-descriptions-item label="" span="3" v-else>
                    <el-collapse v-model="activeName">
                        <el-collapse-item name="content">
                            <el-descriptions title="" :column="3" size="medium" :colon="false">
                                <template v-for="(item,i) in workExperience.workExperienceList">
                                    <el-descriptions-item label="">{{ item.employmentStartDate }} —— {{
                                        item.employmentEndDate }}</el-descriptions-item>
                                    <el-descriptions-item label="">{{ item.companyName }}</el-descriptions-item>
                                    <el-descriptions-item label="" v-if="isEdit"> <el-button type="danger" @click="delWork(i)" plain size="mini" icon="el-icon-delete" circle></el-button></el-descriptions-item>
                                    <el-descriptions-item label="" span="3">{{ item.jobDescription
                                    }}</el-descriptions-item>
                                </template>
                            </el-descriptions>
                        </el-collapse-item>
                    </el-collapse>

                    <!--
                    <el-table :data="workExperience.workExperienceList" style="width: 100%" empty-text="暂无面谈记录信息">
                    <el-table-column prop="meetingType" label="企业名称" width="100">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.companyName" placeholder="请输入企业名称" :disabled="!isEdit"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="employmentDate" label="在职时间" width="200">
                        <template slot-scope="scope">
                            <el-date-picker type="datetime" value-format="yyyy-MM-dd" :disabled="!isEdit"
                                placeholder="选择时间" v-model="scope.row.employmentDate" style="width: 100%;" @change="handleEmploymentDate(row)"></el-date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column prop="dataSupport" label="工作内容" width="120">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.jobDescription" :disabled="!isEdit"></el-input>
                        </template>
                    </el-table-column>
                </el-table>
                -->
                </el-descriptions-item>
            </el-descriptions>
        </div>
        <el-divider></el-divider>
    </div>
</template>
  
<script>
export default {
    name: "workExperience",//工作经历
    props: {
        isEdit: {
            type: Boolean,
            default: () => { return false; }
        },
        candidateInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
    },
    data () {
        return {
            activeName: '',
            isOpen: false,
            // isEdit: false,
            ruleForm: {
                companyName: null,
                employmentDate: [],
                employmentEndDate: null,
                employmentStartDate: null,
                jobDescription: null,
            },
            workExperience: {
                workExperienceList: [],
            },
            workForm: {
                companyName: [
                    { required: true, message: '请输入企业名称', trigger: 'blur' }
                ],
                employmentDate: [
                    { required: true, message: '请选择日期', trigger: 'blur' }
                ],
            },
            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() > Date.now()
                },
            }

        }
    },
    mounted () {
        for (const prop in this.candidateInfo) {
            if (prop in this.workExperience) {
                this.workExperience[prop] = this.candidateInfo[prop];
            }
        }
        if (!this.workExperience.workExperienceList) {
            this.workExperience.workExperienceList = [];
        }
        if (!this.isOpen && this.isEdit && this.workExperience.workExperienceList.length > 0) {
            this.toggleContent();
        }
    },
    computed: {

    },
    methods: {
        // 删除工作经历
        delWork (i) {
            this.workExperience.workExperienceList.splice(i,1);
        },
        handleEmploymentDate (date) {
            if (date) {
                this.ruleForm.employmentStartDate = date[0];
                this.ruleForm.employmentEndDate = date[1];
            }
        },
        reset () {
            this.workExperience = {
                workExperienceList: [],
            }
            this.ruleForm = {
                companyName: null,
                employmentDate: [],
                employmentEndDate: null,
                employmentStartDate: null,
                jobDescription: null,
            }
        },
        addRow () {
            // this.tableData.forEach((element, i) => {
            //     if (!element.v1) {
            //         this.tableData.splice(i, 1);
            //     }
            // });
            this.$refs.workForm.validate((valid) => {
                if (valid) {
                    this.workExperience.workExperienceList.push(this.ruleForm);
                    this.ruleForm = {
                        companyName: null,
                        employmentDate: [],
                        employmentEndDate: null,
                        employmentStartDate: null,
                        jobDescription: null,
                    }
                }
            })
            if (!this.isOpen) {
                this.toggleContent();
            }

        },
        removeRow (index) {
        },
        toggleContent () {
            this.isOpen = !this.isOpen
            this.activeName = this.isOpen ? 'content' : ''
        },
        //提交
        submitForm (formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    alert('submit!');
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
    }
}
</script>
  
<style scoped>
.title {
    cursor: pointer;
}

.ruleForm {
    padding: 10px;
}

.des-box {
    padding: 0 10px 0 30px
}

::v-deep .el-descriptions__title {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
}

::v-deep .el-descriptions__header {
    margin-bottom: 10px;
}

::v-deep .el-divider--horizontal {
    margin: 5px 0;
}

/* ::v-deep .el-collapse-item__header.is-active {
    display: none;
} */
::v-deep .el-collapse-item__wrap {
    border-bottom: none;
}

::v-deep .el-collapse {
    border: none;
}

::v-deep .el-collapse-item__header {
    display: none;
}

::v-deep .el-collapse-item__content {
    padding-bottom: 0;
}

.arrowTransformReturn {
    transition: 0.2s;
    transform-origin: center;
    transform: rotateZ(90deg);
}

.arrowTransform {
    transition: 0.2s;
    transform-origin: center;
    transform: rotateZ(0deg);
}
</style>