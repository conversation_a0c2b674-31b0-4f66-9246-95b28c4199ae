<template>
    <my-container v-loading="pageLoading">
        <div class="rowstyle" id="allbody" @click="closecolor">
            <div class="marleftrig">
                <div class="aligncenter">
                    <div class="flexrow">
                        <el-button-group>
                        <el-button :type="num==1?'primary':''" @click="whactclick(1)">直通车图</el-button>
                        <el-button :type="num==2?'primary':''" @click="whactclick(2)">主图</el-button>
                        <el-button :type="num==3?'primary':''" @click="whactclick(3)">sku</el-button>
                        <el-button :type="num==4?'primary':''" @click="whactclick(4)">详情页</el-button>
                        <el-button :type="num==5?'primary':''" @click="whactclick(5)">主图视频</el-button>
                        <el-button :type="num==6?'primary':''" @click="whactclick(6)">微详情视频</el-button>
                        </el-button-group>
                    </div>
                    <div  style="margin-top: 10px; display: flex; flex-direction: row;">
                        <el-button :type="tonum==11?'primary':''" @click="addmokuai(11)">增加模块</el-button>
                        <el-button :type="tonum==12?'primary':''" @click="savekuai(12)" v-show="listidd">直接保存</el-button>
                        <el-button :type="tonum==12?'primary':''" @click="savemokuai(12)" v-show="!listidd">点击保存</el-button>
                    </div>
                    <div style="margin-top: 10px;"><el-button type="primary" style="width:200px;" @click="tocreateimg()" v-if="creimg">生成图片</el-button></div>
                </div>

                <div class="content" ref="oneboxx">
                    <div v-if="listidd?(num==1&&listall.data1!=null):num==1">
                        <!-- <shootingcreateindex1 :ispaste = "false" :btnshow="false" @getalllist="getalllist" @changefuc="changefuc" @cleardig="cleardig" :listid="listidd" :main="mainlist" :alllist="listall.data1" ref="createindex1" :key="num" :bannum="num"  :name="'carimg'" /> -->
                        <shootingcreateindex1 @getalllist="getalllist" @changefuc="changefuc" @cleardig="cleardig" :listid="listidd" :main="mainlist" :alllist="listall.data1" ref="createindex1" :key="num" :bannum="num"  :name="'carimg'" />
                    </div>
                    <div v-else-if="listidd?(num==2&&listall.data2!=null):num==2">
                        <shootingcreateindex2 @getalllist="getalllist" @changefuc="changefuc" :listid="listidd" :main="mainlist" :alllist="listall.data2" ref="createindex2" :key="num" :bannum="num" :name="'indeximg'" />
                    </div>
                    <div v-else-if="listidd?(num==3&&listall.data3!=null):num==3">
                        <shootingcreateindex3 @getalllist="getalllist" @changefuc="changefuc" :listid="listidd" :main="mainlist" :alllist="listall.data3" ref="createindex3" :key="num" :bannum="num" :name="'skuimg'" />
                    </div>
                    <div v-else-if="listidd?(num==4&&listall.data4!=null):num==4">
                        <shootingcreateindex4 :duimoudle="false" @getalllist="getalllist" @changefuc="changefuc" :listid="listidd" :main="mainlist" :alllist="listall.data4" ref="createindex4" :key="num" :bannum="num" :name="'msgimg'"/>
                    </div>
                    <div v-else-if="listidd?(num==5&&listall.data5!=null):num==5">
                        <shootingcreateindex5 @getalllist="getalllist" @changefuc="changefuc" :listid="listidd" :main="mainlist" :alllist="listall.data5" ref="createindex5" :isroll="true" :isvideoh ="true" :key="num" :bannum="num" :name="'indexvideo'" />
                    </div>
                    <div v-else-if="listidd?(num==6&&listall.data6!=null):num==6">
                        <shootingcreateindex6 @getalllist="getalllist" @changefuc="changefuc" :listid="listidd" :main="mainlist" :alllist="listall.data6" ref="createindex6" :isroll="true" :isvideoh ="true" :key="num" :bannum="num"  :name="'weivideo'" />
                    </div>
                </div>

                <el-dialog title="海报生成" :visible.sync="dialogTableVisible" append-to-body>
                    <div class="border">
                        <img
                        style="width: auto; height: auto; max-width: 100%; max-height: 100%;"
                        :src="imgUrl"/>
                    </div>
                </el-dialog>
            </div>
        </div>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import html2canvas from 'html2canvas'
import shootingcreateindex1 from '@/views/media/shooting/fuJianmanage/shootingcreateindex1';
import shootingcreateindex2 from '@/views/media/shooting/fuJianmanage/shootingcreateindex1';
import shootingcreateindex3 from '@/views/media/shooting/fuJianmanage/shootingcreateindex2';
import shootingcreateindex4 from '@/views/media/shooting/fuJianmanage/shootingcreateindex1';
import shootingcreateindex5 from '@/views/media/shooting/fuJianmanage/shootingcreateindex3';
import shootingcreateindex6 from '@/views/media/shooting/fuJianmanage/shootingcreateindex3';
import {getReferenceMainReferencs,saveReferenceMainReferencs,saveReferenceMainReferencsForImg,getReferenceMainReferencsAll} from '@/api/media/referencemanage';

export default {
    name: 'Shootingcreatereference',
    components: {MyContainer,shootingcreateindex1,shootingcreateindex2,shootingcreateindex3,shootingcreateindex4,shootingcreateindex5,shootingcreateindex6,MyContainer},
    data() {
        return {
            pageLoading:false,
            num: 1,
            imgUrl: '',
            dialogTableVisible: false,
            tonum: null,
            listall: [],
            mainlist: null,
            listidd: 0,
            creimg:false,
            ischange: false
        };
    },
    props: {
        listid: {default: ''},
    },

    mounted() {
        let _this = this;
        _this.listidd = _this.listid;
        _this.getalllist()

        if(!this.listid){
            this.savemokuai(12);
        }
    },

    methods: {
        closecolor(){
            var all = document.getElementById('allbody').querySelectorAll('section')
            for(var i =0; i<all.length; i++){
                var a = document.getElementById(i)
                if(a){
                    a.style.border = '1px solid white';
                }
            }
        },
        cleardig(val){
            this.$emit('cleardig',val)
        },
        changefuc(val){
            this.ischange = val;
        },
        async getalllist(val){
            let _this =this;
            if(val){
                await _this.$nextTick(()=>{
                    _this.listidd = val;
                })
            }
            _this.pageLoading= true;
            if(_this.listidd){
                let res = await getReferenceMainReferencsAll(_this.listidd);
                if(res.success)
                    _this.listall = res.data;
                    _this.mainlist = res.data.mainTask;

            }
            _this.pageLoading= false;
        },
        whactclick(num){
            let _this = this;
            if(this.ischange){
                this.$message({
                    message: "请先保存再进行操作！",
                    offset: 150,
                    duration: 2000
                })
                return
            }
            _this.num = num;
            
        },
        tocreateimg(){
            html2canvas(this.$refs.oneboxx).then((canvas) => {
				let dataURL = canvas.toDataURL('image/png')
				this.imgUrl = dataURL
                this.dialogTableVisible = true;
			})
        },
        async getlist(){
            // let res = await getReferenceMainReferencs();
        },
        addmokuai(val){
            let _this = this;
            _this.tonum = val;
            if(_this.num == 1){
                this.$refs.createindex1.addlist();
            }else if(_this.num == 2){
                this.$refs.createindex2.addlist();
            }else if(_this.num == 3){
                this.$refs.createindex3.addlist();
            }else if(_this.num == 4){
                this.$refs.createindex4.addlist();
            }else if(_this.num == 5){
                this.$refs.createindex5.addlist();
            }else if(_this.num == 6){
                this.$refs.createindex6.addlist();
            }
        },
        savekuai(val){
            let _this = this;
            _this.ischange = false;
            this.tonum = val;
            if(_this.num == 1){
                this.$refs.createindex1.tosubmitt();
            }else if(_this.num == 2){
                this.$refs.createindex2.tosubmitt();
            }else if(_this.num == 3){
                this.$refs.createindex3.tosubmitt();
            }else if(_this.num == 4){
                this.$refs.createindex4.tosubmitt();
            }else if(_this.num == 5){
                this.$refs.createindex5.tosubmitt();
            }else if(_this.num == 6){
                this.$refs.createindex6.tosubmitt();
            }
        },
        savemokuai(val){
            let _this = this;
            this.tonum = val;
            if(_this.num == 1){
                this.$refs.createindex1.submitt();
            }else if(_this.num == 2){
                this.$refs.createindex2.submitt();
            }else if(_this.num == 3){
                this.$refs.createindex3.submitt();
            }else if(_this.num == 4){
                this.$refs.createindex4.submitt();
            }else if(_this.num == 5){
                this.$refs.createindex5.submitt();
            }else if(_this.num == 6){
                this.$refs.createindex6.submitt();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.rowstyle{
    margin: 0;
    height: 100%;
    // display: flex;
    // justify-content: center;
    // align-items: center;
}
.flexrow{
    display: flex;
    flex-direction: row;
    width: auto;
    // border: 1px solid #eee;
}
.aligncenter{
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    height: auto;
    flex-direction: column;
    border-bottom: 1px solid #eee;
    padding: 10px;
    // border: 1px solid blue;
}
.content{
    // margin: 0 400px;
    height: 750px;
    overflow-y: auto;
    border: 1px solid #eee;
}
.border{
    border: 2px solid #409EFF;
}
.marleftrig{
    margin: 0 370px;
//    width: 1000px;
}
</style>