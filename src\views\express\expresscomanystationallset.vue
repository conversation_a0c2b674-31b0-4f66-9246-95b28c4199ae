<template>
    <my-container>
        <el-dialog width="30%" style="height: 80%" :visible.sync="isshowprosimstate" v-dialogDrag>
            <el-tabs>
                <el-tab-pane label="选择站点" name="first">
                <el-table
                    :data="
                    prosimstatelist.slice(
                        (gpcurrentPage - 1) * gppagesize,
                        gpcurrentPage * gppagesize
                    )" style="width: 100%">
                    <el-table-column prop="state" label="站点" width="180">
                    </el-table-column>

                    <el-table-column label="操作" width="100">
                    <template slot-scope="scope">
                        <el-button
                        @click="selectstate(scope.row)"
                        :type="scope.row.selecttype"
                        :icon="scope.row.selectedicon">
                        选择
                        </el-button>
                    </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="gpcurrentPage"
                    :page-sizes="[5]"
                    :page-size="gppagesize"
                    layout=" prev, pager, next"
                    :total="parseInt(prosimstatelist.length)"
                >
                </el-pagination>
                </el-tab-pane>
                <el-tab-pane label="站点管理" name="third">
                <el-input
                    v-model.trim="newstatename"
                    placeholder="输入新的站点"
                    style="width: 130px"
                />
                <el-button @click="addstatename" type="text" size="small">添加</el-button>
                <el-table
                    :data="
                    prosimstatelist.slice(
                        (gpcurrentPage - 1) * gppagesize,
                        gpcurrentPage * gppagesize)"
                    style="width: 100%"
                >
                    <el-table-column prop="state" label="状态名称" width="180">
                    </el-table-column>

                    <el-table-column label="操作" width="100">
                    <template slot-scope="scope">
                        <el-button
                        @click="handleDeleteState(scope.row)"
                        type="text"
                        size="small"
                        >删除</el-button
                        >
                    </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="gpcurrentPage"
                    :page-sizes="[5]"
                    :page-size="gppagesize"
                    layout=" prev, pager, next"
                    :total="parseInt(prosimstatelist.length)"
                >
                </el-pagination>
                </el-tab-pane>
            </el-tabs>
        </el-dialog>

        <el-dialog
            title="提示"
            :visible.sync="deletestatedialogVisible"
            width="30%" v-dialogDrag>
            <span>确认删除站点名称吗吗？</span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="deletestatedialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="deleteStateName">确 定</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import {getProductStateName, addProductStateName, addProductState, deleteProductState,
addProcodeCustomer} from '@/api/operatemanage/base/product'
import { getExpressComanyStationName, addExpressComanyStationName, addExpressComanyStation, deleteExpressComanyStation } from "@/api/express/express";


const getDate = formatTime(new Date(), "YYYY-MM-DD HH-mm:ss");
export default {
    name: 'YunhanAdminExpresscomanystationallset',
    components : {MyContainer, MyConfirmButton},
    data() {
        return {
            that:this,
            filter:{
                id:null,
            },
            prosimstatelist:[],
            list:[],
            groupNameList: [],
            deletegroupnamevalue: "",
            bechangegoodscode: "",
            isshowcusgroup: false,
            newgroupname: "",
            newstatename: "",
            stationId:null,
            gpcurrentPage: 1,
            gppagesize: 5,
            isshowprosimstate:false,
            deletegroupdialogVisible: false,
            deletestatedialogVisible: false,
        };
    },

    async mounted() {
       //await this.getprosimstatelist(); 
    },

    methods: {
        async OnSearch(e, list){
            this.filter.id = e
            this.list=list
            await this.getprosimstatelist(this.filter.id)
            await this.setprostate(this.filter.id)
        },
        //获取状态信息
        async getprosimstatelist(id){
            var res = await getExpressComanyStationName({id: id});
            if (res?.code){
                this.prosimstatelist = res.data.map(function (item) {
                var ob = new Object();
                ob.state = item.stationName;
                ob.id = item.id;
                ob.expCompanyId = item.expCompanyId;
                ob.isshow = false;
                ob.selectedicon = "";
                ob.selecttype = "fail";
                return ob;
                })
            }
        },
        //设置系列状态
        async setprostate(e){

        this.bechangegoodscode = e;
        var selectedListIndex = 0;
        for (var listindex in this.list) {
            if (this.list[listindex].id == e) {
            selectedListIndex = listindex;
            }
        }

        for (var i = 0; i < this.prosimstatelist.length; i++) {
            this.prosimstatelist[i].selecttype = "fail";
            this.prosimstatelist[i].selectedicon = "";
        }
        if (this.list[selectedListIndex].stationName != "暂无站点") {

            var states = this.list[selectedListIndex].stationName.split(',')

            


            for (var s in states){
            for (var i = 0; i < this.prosimstatelist.length; i++){
                if (this.prosimstatelist[i].state == states[s]){
                this.prosimstatelist[i].selecttype = "success";
                this.prosimstatelist[i].selectedicon = "el-icon-check";
                }
            }
            } 
        } else {
            for (var i = 0; i < this.prosimstatelist.length; i++) {
            this.prosimstatelist[i].selecttype = "fail";
            this.prosimstatelist[i].selectedicon = "";
            }
        }
        this.isshowprosimstate = true
        },
        //状态选择
        async selectstate(e) {
            for (var i = 0; i < this.prosimstatelist.length; i++) {
                if (this.prosimstatelist[i].state == e.state) {
                if (this.prosimstatelist[i].selecttype == "success") {
                    this.prosimstatelist[i].selecttype = "fail";
                    this.prosimstatelist[i].selectedicon = "";
                } else {
                    this.prosimstatelist[i].selecttype = "success";
                    this.prosimstatelist[i].selectedicon = "el-icon-check";
                }
                }
            }
            var choosedstatename = '';
            var ids = ''
            for (var s in this.prosimstatelist) {
                if (this.prosimstatelist[s].selecttype == "success") {
                    choosedstatename += this.prosimstatelist[s].state + ","
                    ids += this.prosimstatelist[s].id + ","
                }
                    
            }
            var params = {
                stationName : choosedstatename,
                id : this.bechangegoodscode,
                ids : ids,
                companyId : e.expCompanyId
            }
            addExpressComanyStationName(params)
            .then((res) => {
                if (res.code == 1){
                for (var listindex in this.list) {
                    if (this.list[listindex].id == this.bechangegoodscode) {
                    if (choosedstatename == "") choosedstatename = "暂无站点";
                    this.list[listindex].stationName = choosedstatename;
                    }
                }
                this.$emit('changelist',this.list)
                }
            });
        },
        //点击添加状态
        async addstatename(e){
            if (!this.newstatename || this.newstatename == null) {
                this.$message.warning("请输入站点名称!")
                return;
            }
            var res = await addExpressComanyStation({id: this.filter.id, stationName: this.newstatename})
                if (res.code){
                    var ob = new Object();
                    ob.state = this.newstatename;
                    ob.isshow = false;
                    ob.selectedicon = "";
                    ob.selecttype = "fail";

                    this.prosimstatelist.push(ob);
                    this.newstatename = "";
                }
            await this.getprosimstatelist(this.filter.id)
        },
        //删除状态
        async handleDeleteState(e){
            this.deletestatedialogVisible = true;
            this.deletestatenamevalue = e.state;
            this.stationId = e.id
        },
        async deleteStateName() {
            var that = this;
            //updatecustomerservicergroup
            deleteExpressComanyStation({CompanyId: this.filter.id, id: this.stationId }).then((res) => {
                
                that.deletestatedialogVisible = false;
                that.getprosimstatelist(this.filter.id);
            });
        },
        async handleSizeChange(val) {
            this.gppagesize = val;
        },
        async handleCurrentChange(val) {
            this.gpcurrentPage = val;
        },
    },
};
</script>

<style lang="scss" scoped>

</style>