<template>
  <container>
    <template>


      <div class="bordercss">
        <div class="titlecss">平台总亏损数据</div>
        <div style="display: flex; flex-direction: row; line-break: auto; flex-wrap: wrap;" v-if="list.length > 0">
          <el-card class="box-card" v-for="(item, i) in list" :key="i" :class="i == platindex ? 'bgcolor' : ''">
            <div slot="header" class="clearfix">
              <div style="font-size: 14px; min-height: 21px">{{ item.platformName }}</div>
              <!-- <span style="font-size: 14px;">{{item.shopName}}</span> -->

              <!-- <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button> -->
            </div>
            <div style="height: 100%; width: 100%;" @click="getshop(item, i)">

              <div class="text item">
                <div v-show="i == 0">总亏损订单: {{ formatNumber(item.orderNoCount) }}</div>
                <el-button v-show="i == 0 ? false : true" type="text">总亏损订单: {{ formatNumber(item.orderNoCount) }}</el-button>

              </div>
              <div class="text item">
                <div v-show="i == 0" style="margin-top: 10px">总亏损金额: {{ formatNumber(item.exitProfitSum) }}</div>
                <el-button v-show="i == 0 ? false : true" type="text">总亏损金额: {{ formatNumber(item.exitProfitSum) }}</el-button>

              </div>
            </div>
            <!-- <div style="width: 100%" class="flexcenter"><i  class="el-icon-loading"></i></div> -->

          </el-card>
        </div>
        <el-row class="flexcenter" v-else>
          <i class="el-icon-loading" style="font-size: 30px;"></i>
        </el-row>
      </div>

      <div class="bordercss" v-show="platindex != -1">
        <div class="titlecss">{{ platdata.platformName }}_店铺总亏损数据</div>
        <div style="display: flex; flex-direction: row; line-break: auto; flex-wrap: wrap;" v-if="neilist.length > 0">
          <el-card class="box-card" v-for="(item, i) in neilist" :key="i" :class="i == platindex2 ? 'bgcolor' : ''">
            <div slot="header" class="clearfix">
              <div style="font-size: 14px; min-height: 40px">{{ item.shopName }}</div>
              <!-- <span style="font-size: 14px;">{{item.shopName}}</span> -->

              <!-- <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button> -->
              <!-- <el-button style="margin-left: auto;" type="text">上一级</el-button> -->
            </div>
            <div style="height: 100%; width: 100%;" @click="getshopchar(item, i)">

              <div class="text item">
                <div v-show="i == 0">总亏损订单: {{ formatNumber(item.orderNoCount) }}</div>
                <el-button v-show="i == 0 ? false : true" type="text">总亏损订单: {{ formatNumber(item.orderNoCount) }}</el-button>
              </div>
              <div class="text item">
                <div v-show="i == 0" style="margin-top: 10px">总亏损金额: {{ formatNumber(item.exitProfitSum) }}</div>
                <el-button v-show="i == 0 ? false : true" type="text">总亏损金额: {{ formatNumber(item.exitProfitSum) }}</el-button>

              </div>
            </div>

          </el-card>
        </div>
        <el-row class="flexcenter" v-else>
          <i class="el-icon-loading" style="font-size: 30px;"></i>
        </el-row>
      </div>

      <div class="bordercss" v-show="platindex != -1">
        <div class="titlecss">{{ platdata.platformName }}_运营小组总亏损数据</div>
        <div style="display: flex; flex-direction: row; line-break: auto; flex-wrap: wrap;" v-if="grouplist.length > 0">
          <el-card class="box-card" v-for="(item, i) in grouplist" :key="i" :class="i == platindex3 ? 'bgcolor' : ''">
            <div slot="header" class="clearfix">
              <div style="font-size: 14px;min-height: 21px">{{ item.groupName }}</div>
              <!-- <span style="font-size: 14px;">{{item.shopName}}</span> -->

              <!-- <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button> -->
              <!-- <el-button style="margin-left: auto;" type="text">上一级</el-button> -->
            </div>
            <div style="height: 100%; width: 100%;" @click="getgroupchart(item, i)">

              <div class="text item">
                <div v-show="i == 0">总亏损订单: {{ formatNumber(item.orderNoCount) }}</div>
                <el-button v-show="i == 0 ? false : true" type="text">总亏损订单: {{ formatNumber(item.orderNoCount) }}</el-button>

              </div>

              <div class="text item">
                <div v-show="i == 0" style="margin-top: 10px">总亏损金额: {{ formatNumber(item.exitProfitSum) }}</div>
                <el-button v-show="i == 0 ? false : true" type="text">总亏损金额: {{ formatNumber(item.exitProfitSum) }}</el-button>

              </div>
            </div>

          </el-card>
        </div>
        <el-row class="flexcenter" v-else>
          <i class="el-icon-loading" style="font-size: 30px;"></i>
        </el-row>
      </div>
    </template>

    <!-- <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template> -->


    <el-dialog :visible.sync="chartdialog" width="60%" :close-on-click-modal="false" v-loading="editLoading"
      element-loading-text="拼命加载中" v-dialogDrag :append-to-body="true">
      <el-date-picker style="width: 200px" :picker-options="pickerOptions" v-model="timerange" type="datetimerange"
        format="yyyy-MM-dd" @change="getchardata" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
        end-placeholder="结束日期" :clearable="false">
      </el-date-picker>
      <buschar :charid="'charNoProfit1'" ref="reftm" :isslice="true" :analysisData="chartsdata1" v-if="chartsdata1">
      </buschar>


    </el-dialog>
  </container>
</template>

<script>
import container from '@/components/my-container'
// import cesTable from "@/components/Table/table.vue";
import { getContinuousNoProfit, newGetContinuousNoProfitRecordAsync, newGetContinuousNoProfitRecordAnalysisAsync } from "@/api/bookkeeper/continuousprofitanalysis"
import dayjs from "dayjs";
// import { formatTime } from "@/utils";
import { formatTime, pickerOptions } from "@/utils/tools";
import {
  getOrderDayReportOutStoreLossShop, getOrderDayReportOutStoreLossAllPlatform, getOrderDayReportOutStoreLossGroup,
  getOrderDayReportOutStoreLossGroupChart, getOrderDayReportOutStoreLossShopChart
} from '@/api/bookkeeper/reportdayV2'
import buschar from './buschar.vue'


const platformvalue = [{ label: '天猫', value: 1 }, { label: '拼多多', value: 2 }, { label: '阿里巴巴', value: 4 },
{ label: '抖音', value: 6 }, { label: '京东', value: 7 }, { label: '淘工厂', value: 8 }, { label: '淘宝', value: 9 }, { label: '苏宁', value: 10 },]
const tableCols = [
  { istrue: true, prop: 'consecutiveDay3Profit3', label: '连续3天负利润', tipmesg: '', sortable: 'custom', },
  { istrue: true, prop: 'consecutiveDay7Profit3', label: '连续7天负利润', tipmesg: '', sortable: 'custom', },
  { istrue: true, prop: 'consecutiveDay15Profit3', label: '连续15天负利润', tipmesg: '', sortable: 'custom', },
  { istrue: true, prop: 'consecutiveDay30Profit3', label: '连续30天负利润', tipmesg: '', sortable: 'custom' }
]

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
  name: 'ConsecutiveNoProfitShowTable',
  components: { container, buschar },
  props: ['filter2', 'fatimerange'],
  data() {
    return {
      that: this,
      pickerOptions: pickerOptions,
      isgettrue: false,
      editLoading: true,
      platForm: 0,
      chartdialog: false,
      chifilter: {},
      filter: {},
      platdata: {},
      timerange: [],
      neilist: [],
      grouplist: [],
      title: '平台总亏损数据',
      isselplatform: [
        '3天负利润',
        '7天负利润',
        '15天负利润',
        '30天负利润',
      ],
      dp: false,
      yyz: false,
      yyzy: false,
      yyzl: false,
      platindex: -1,
      platindex2: -1,
      platindex3: -1,


      chartsdata1: {},
      chartsdata2: {},
      chartsdata3: {},
      chartsdata4: {},
      lastSortArgs: {
        field: "",
        order: "",
      },
      moudleindex: '',
      isRemoteSort: false,
      isRemoteSortnum: 0,
      list: [],
      shopchart: null,
      groupchart: null,
      pager: {
        orderBy: null, isAsc: false,
        currentPage: 1,
        pageSize: 50,
      },
      tableCols: [],
      total: 0,
      sels: [],
      uploadLoading: false,
      dialogVisible: false,
      listLoading: false,
      groupId: null,
      shopCode: null,
      fileList: [],
      summaryarry: {},
      rowdata: {},
      platforms: [],
      tableData: [
        { id: 10001, name: 'Test1', role: 'Develop', sex: 'Man', age: 28, address: 'test abc' },
        { id: 10002, name: 'Test2', role: 'Test', sex: 'Women', age: 22, address: 'Guangzhou' },
        { id: 10003, name: 'Test3', role: 'PM', sex: 'Man', age: 32, address: 'Shanghai' },
        { id: 10004, name: 'Test4', role: 'Designer', sex: 'Women', age: 23, address: 'test abc' },
        { id: 10005, name: 'Test5', role: 'Develop', sex: 'Women', age: 30, address: 'Shanghai' },
        { id: 10006, name: 'Test6', role: 'Designer', sex: 'Women', age: 21, address: 'test abc' },
        { id: 10007, name: 'Test7', role: 'Test', sex: 'Man', age: 29, address: 'test abc' },
        { id: 10008, name: 'Test8', role: 'Develop', sex: 'Man', age: 35, address: 'test abc' }
      ]
    };
  },

  async mounted() {

  },


  methods: {
        formatNumber(value) {
        // 处理负数
        const absoluteValue = Math.abs(value);
        let formattedValue;

        if (absoluteValue >= 100) {
          // 大于或等于100时使用千位符
          formattedValue = new Intl.NumberFormat().format(absoluteValue);
        } else {
          // 小于100时保留两位小数
          formattedValue = absoluteValue.toFixed(2);
        }

        // 添加负号
        return value < 0 ? `-${formattedValue}` : formattedValue;
      },
    getchardata() {

      this.filter.startTime = this.timerange[0]
      this.filter.endTime = this.timerange[1]
      if (this.moudleindex == 'shop') {
        this.queryshop(this.rowdata, this.rowdata.i)
      } else if (this.moudleindex == 'group') {
        this.querygroup(this.rowdata, this.rowdata.i)
      }
    },
    getshopchar(row, i) {
      if (i == 0) {
        this.$message("汇总不支持点击");
        return
      }
      this.filter.startTime = this.fatimerange[0];
      this.filter.endTime = this.fatimerange[1];
      this.timerange = this.fatimerange;
      this.queryshop(row, i)
    },
    async queryshop(row, i) {

      this.rowdata = { ...row, i: i };
      this.platindex2 = i;
      this.chartsdata1 = null;
      if (this.isgettrue) {
        return
      }
      this.isgettrue = true
      let params = {
        ...this.filter,
        shopCode: row.shopCode,
        platForm: this.platForm,
      }
      const res = await getOrderDayReportOutStoreLossShopChart(params)
      this.listLoading = false
      this.isgettrue = false;
      if (!res?.success) {
        return
      }
      this.shopchart = res.data;
      this.chartdialog = true;

      if (res.data.length == 0) {
        this.$message("暂无数据！");
        return
      }
      this.chartsdata1 = res.data[0].analysis;
      this.moudleindex = 'shop';
    },
    getgroupchart(row, i) {
      if (i == 0) {
        this.$message("汇总不支持点击");
        return
      }
      this.filter.startTime = this.fatimerange[0];
      this.filter.endTime = this.fatimerange[1];
      this.timerange = this.fatimerange;

      this.querygroup(row, i)



    },
    async querygroup(row, i) {
      this.rowdata = { ...row, i: i };

      this.chartsdata1 = null;
      this.platindex3 = i;
      let params = {
        ...this.filter,
        groupId: row.groupId,
        groupName: row.groupName,
        platForm: this.platForm,
      }
      if (this.isgettrue) {
        return
      }
      this.isgettrue = true

      const res = await getOrderDayReportOutStoreLossGroupChart(params)
      console.log(params);
      this.listLoading = false;
      this.isgettrue = false;
      if (!res?.success) {
        return
      }

      this.groupchart = res.data;
      this.chartdialog = true;

      if (res.data.length == 0) {
        this.$message("暂无数据！");
        return
      }
      this.chartsdata1 = res.data[0].analysis;
      this.moudleindex = 'group';
    },
    //查询第一页
    async onSearch() {

      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    async getshop(row, i) {
      if (row.platform == 0) {
        this.$message("总平台不支持点击");
        return
      }
      console.log(row, 'row');
      this.platdata = { ...row, i: i };
      this.platindex = i;
      this.platindex2 = -1;
      this.platindex3 = -1;
      this.platForm = row.platform;
      this.neilist = null;
      this.grouplist = null;
      this.$nextTick(async () => {
        await this.getshodata(row)
        await this.getgroupdata(row)
      });


    },
    async getshodata(row) {

      this.title = '店铺总亏损数据';
      // this.platForm = row.platform;

      let params = {
        platForm: row.platform,
        ...this.filter,
      }
      if (this.isgettrue) {
        return
      }
      this.isgettrue = true

      const res = await getOrderDayReportOutStoreLossShop(params)
      this.isgettrue = false;
      this.listLoading = false;
      if (!res?.success) {
        return
      }
      this.neilist = res.data;
    },
    async getgroupdata(row) {
      let params = {
        platForm: row.platform,
        ...this.filter,
      }
      if (this.isgettrue) {
        return
      }
      this.isgettrue = true
      const res = await getOrderDayReportOutStoreLossGroup(params)
      this.isgettrue = false
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.grouplist = res.data;
    },
    filterget(val) {
      console.log(val,"val");
      this.list = [];
      this.filter = val
      this.timerange = [val.startTime, val.endTime]
      const isOnePlatform = val.platForms.split(",").length == 1;
      this.$nextTick(() => {
        this.getlist(isOnePlatform)
        if (this.platindex != -1) {
          this.getshop(this.platdata, this.platdata.i)
          // this.querygroup(this.rowdata, this.rowdata.i)
          // this.queryshop(this.rowdata, this.rowdata.i)
        }
      });
    },
    async getlist(isOnePlatform) {
      this.platindex = -1;
      this.platindex2 = -1;
      this.platindex3 = -1;
      this.listLoading = true
      let params = {
        platForm: null,
        ...this.filter
      }
      console.log(params);
      const res = await getOrderDayReportOutStoreLossAllPlatform(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.title = '平台总亏损数据';
      const data = res.data;
      this.list = data
      if (isOnePlatform) {
        // console.log(this.list[1], 'this.list[1]');
        // console.log(isOnePlatform, 'isOnePlatform');
        this.getshop(this.list[1], this.list[1].platform)
      } 
    },
  }
};
</script>

<style lang="scss" scoped>
::v-deep .col-one {
  background-color: #efebf0da !important;
  // color: #eeeeeeda !important;
}

::v-deep .col-two {
  background-color: #d6e6d1e8 !important;
}

::v-deep .col-thr {
  background-color: #f1efdc !important;
}

::v-deep .col-four {
  background-color: #d9e8f3 !important;
}

.item {
  font-size: 14px;
}

.box-card {
  height: 120px;
  width: 210px;
  margin-right: 10px;
  margin-bottom: 10px;
}

.bgcolor {
  background-color: rgb(183, 216, 199);
}

::v-deep .el-card__header {
  padding: 9px 10px;
}

.titlecss {
  font-size: 15px;
  font-weight: 600;
  font-family: 'Courier New', Courier, monospace;
}

.bordercss {
  border: 1px solid #eee;
  border-radius: 20px;
  padding: 10px;
}

.flexcenter {
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep .el-card__body {
  padding: 5px !important;
}

::v-deep .el-button {
  user-select: unset;
}
</style>