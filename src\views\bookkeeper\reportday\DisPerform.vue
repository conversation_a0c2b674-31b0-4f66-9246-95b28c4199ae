<template>
    <container>
        <template #header>
            <el-form :inline="true">
                <el-form-item>
                    <el-date-picker style="width: 240px" v-model="timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        @change="changeTime"></el-date-picker>
                </el-form-item>
                <!-- <el-form-item>
                    <el-date-picker style="width: 240px" v-model="timerange3" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="上架开始日期"
                        end-placeholder="上架结束日期" :clearable="true" @change="changeTime3"></el-date-picker>
                </el-form-item> -->
                <el-form-item>
                    <el-input v-model.trim="filter.salesPerson" :maxlength="150" clearable placeholder="运营组"
                        style="width:100px;" />
                </el-form-item>
                <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.profit3UnZero" collapse-tags clearable placeholder="毛利3"
                        style="width: 90px">
                        <el-option label="正利润" :value="false" />
                        <el-option label="负利润" :value="true" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.profit33UnZero" collapse-tags clearable placeholder="毛利4"
                        style="width: 90px">
                        <el-option label="正利润" :value="false" />
                        <el-option label="负利润" :value="true" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.profit6UnZero" collapse-tags clearable placeholder="毛利6"
                        style="width: 90px">
                        <el-option label="正利润" :value="false" />
                        <el-option label="负利润" :value="true" />
                    </el-select>
                </el-button>
                <el-form-item>
                    <el-button type="primary" @click="getList('Search')">查询</el-button>
                    <el-button type="primary" @click="exportList">导出</el-button>
                    <el-button type="primary" @click="clear">清空</el-button>
                </el-form-item>
            </el-form>
            <!-- <div style="margin-top: -15px;">
                <el-button type="primary">运营组</el-button>
            </div>
            <div style="margin-top: 5px;">
                <el-button type="primary">毛四维度</el-button>
            </div> -->
        </template>
        <vxetablebase ref="table" :id="'DisPerform20240909'" :that='that' :isIndex='true' @sortchange='sortchange' :border="true"
            :hasexpand='true' :tableData='list' :tableCols='tableCols' :loading="loading" :showsummary='true' :summaryarry="summaryarry"  @summaryClick='openSummary'>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
        <el-dialog title="趋势图" :visible.sync="chatProp.chatDialog" size="80%"
            v-dialogDrag>
            <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                start-placeholder="开始日期" end-placeholder="结束日期" @change="changChartTime"
                style="margin: 10px;" />
            <div v-if="!chatProp.chatLoading">
                <buschar :analysisData="chatProp.data" v-if="!chatProp.chatLoading"></buschar>
            </div>
            <div v-else v-loading="chatProp.chatLoading" style="height: 550px;"></div>
        </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { QueryDisPerform, ExportDisPerform, GetTrendChart } from '@/api/bookkeeper/DisPerfrom.js'
import { formatTime } from "@/utils/tools";
import dayjs from 'dayjs';
import buschar from '@/components/Bus/buschar'

const tableCols = [
    { istrue: true, prop: 'salesPerson', label: '运营组', sortable: 'custom', width:'100', align: 'center' },
    { istrue: true, prop: 'payAmont', label: '付款金额', sortable: 'custom', summaryEvent: true, width:'100', align: 'center', },
    { istrue: true, prop: 'saleAmont', label: '销售金额', sortable: 'custom', summaryEvent: true, width:'100', align: 'center' },
    { istrue: true, prop: 'refundAmontBefore', label: '退款', sortable: 'custom', summaryEvent: true, width:'100', align: 'center' },
    { istrue: true, prop: 'refundAmontBeforeRate', label: '退款率(%)', sortable: 'custom',  summaryEvent: true, width:'82', align: 'center',formatter: (row) =>{return row.refundAmontBeforeRate*100+"%"} },
    { istrue: true, prop: 'saleCost', label: '成本', sortable: 'custom', width:'100', align: 'center'  ,summaryEvent: true,},
    { istrue: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width:'100', align: 'center'  ,summaryEvent: true,},
    { istrue: true, prop: 'profit1', label: '毛一利润', sortable: 'custom', summaryEvent: true, width:'100', align: 'center' },
    { istrue: true, prop: 'profit1Rate', label: '毛一利润率(%)', sortable: 'custom', summaryEvent: true, width:'110', align: 'center',formatter: (row) =>{return row.profit1Rate*100+"%"}  },
    { istrue: true, prop: 'profit2', label: '毛二利润', sortable: 'custom', summaryEvent: true, width:'100', align: 'center' },
    { istrue: true, prop: 'profit2Rate', label: '毛二利润率(%)', sortable: 'custom', summaryEvent: true, width:'110', align: 'center' ,formatter: (row) =>{return row.profit2Rate*100+"%"}  },
    { istrue: true, prop: 'profit3', label: '毛三利润', sortable: 'custom', summaryEvent: true, width:'100', align: 'center' },
    { istrue: true, prop: 'profit3Rate', label: '毛三利润率(%)', sortable: 'custom', summaryEvent: true, width:'110', align: 'center' ,formatter: (row) =>{ return row.profit3Rate*100+"%"} },
    { istrue: true, prop: 'profit33', label: '毛四利润', sortable: 'custom', summaryEvent: true, width:'100', align: 'center' },
    { istrue: true, prop: 'profit33Rate', label: '毛四利润率(%)', sortable: 'custom', summaryEvent: true, width:'110', align: 'center',formatter: (row) =>{return row.profit33Rate*100+"%"}  },
    { istrue: true, prop: 'profit5', label: '毛五利润', sortable: 'custom', summaryEvent: true, width:'100', align: 'center' },
    { istrue: true, prop: 'profit5Rate', label: '毛五利润率(%)', sortable: 'custom', summaryEvent: true, width:'110', align: 'center',formatter: (row) =>{return !row.profit5Rate ? '': row.profit5Rate*100+"%"}},
    { istrue: true, prop: 'profit6', label: '毛六利润', sortable: 'custom', summaryEvent: true, width:'100', align: 'center' },
    { istrue: true, prop: 'profit6Rate', label: '毛六利润率(%)', sortable: 'custom', summaryEvent: true, width:'110', align: 'center',formatter: (row) =>{return !row.profit6Rate ? '': row.profit6Rate*100+"%"}},
    { istrue: true, prop: 'exitCost', label: '出仓成本', sortable: 'custom', summaryEvent: true, width:'100', align: 'center' },
    { istrue: true, prop: 'exitCostRate', label: '出仓成本占比(%)', sortable: 'custom',  summaryEvent: true, width:'120', align: 'center',formatter: (row) =>{return row.exitCostRate*100+"%"} },
    { istrue: true, prop: 'negativeExitProfit', label: '出仓负利润', sortable: 'custom', summaryEvent: true, width:'100', align: 'center' },
    {
        istrue: true, prop: 'echarts', label: '趋势图', width:'100', align: 'center', type: 'button', btnList: [
            {
                label: '趋势图',
                handle: (that, row) => {
                    that.openChart(row);
                },
            },
        ]
    },
]

export default {
    name: 'DisPerform',
    components: { container, vxetablebase,buschar },
    data() {
        return {
            that: this,
            filter: {
                startTime: null,
                endTime: null,
                startTime3: null,
                endTime3: null,
                profit3UnZero: null,
                profit6UnZero: null,
                profit33UnZero: null,
                salesPerson: null,
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            chatInfo: {
                startTime: null,
                endTime: null,
                startTime3: null,
                endTime3: null,
                profit3UnZero: null,
                profit33UnZero: null,
                salesPerson: null,
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                summary:false,
                prop:null,
            },
            chatProp: {
                chatDialog: false,//趋势图弹窗
                chatTime: [],//趋势图时间
                chatLoading: true,//趋势图loading
                data: [],//趋势图数据
            },
            timerange: [],
            timerange3: [],
            tableCols,
            list: [],
            summaryarry:{},
            loading: false,
            total: null,
        };
    },

    async mounted() {
        this.filter.startTime = dayjs().subtract(1, "day").format('YYYY-MM-DD');
        this.filter.endTime = dayjs().subtract(1, "day").format('YYYY-MM-DD');
        this.timerange = [this.filter.startTime, this.filter.endTime];
        this.filter.orderBy="salesPerson";
        this.getList();
    },
    methods: {
        //获取列表
        async getList(val) {
            this.loading=true;
            if (val == "Search") {
                this.filter.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            const { success, data } = await QueryDisPerform(this.filter);
            if (success) {
                this.list = data?.list;
                this.total = data?.total;
                this.summaryarry = data?.summary;
            }
            if(this.summaryarry)
            {
                this.summaryarry.refundAmontBeforeRate_sum=(this.summaryarry.refundAmontBeforeRate_sum*100).toFixed(2)+"%";
                this.summaryarry.profit1Rate_sum=(this.summaryarry.profit1Rate_sum*100).toFixed(2)+"%";
                this.summaryarry.profit2Rate_sum=(this.summaryarry.profit2Rate_sum*100).toFixed(2)+"%";
                this.summaryarry.profit3Rate_sum=(this.summaryarry.profit3Rate_sum*100).toFixed(2)+"%";
                this.summaryarry.profit33Rate_sum=(this.summaryarry.profit33Rate_sum*100).toFixed(2)+"%";
                this.summaryarry.profit5Rate_sum=(this.summaryarry.profit5Rate_sum*100).toFixed(2)+"%";
                this.summaryarry.exitCostRate_sum=(this.summaryarry.exitCostRate_sum*100).toFixed(2)+"%";
                this.summaryarry.profit6Rate_sum=(this.summaryarry.profit6Rate_sum*100).toFixed(2)+"%";
            }
            this.loading=false;
        },

        async changeTime(e) {
            this.filter.startTime = e ? e[0] : null
            this.filter.endTime = e ? e[1] : null
        },
        async changeTime3(e) {
            this.filter.startTime3 = e ? e[0] : null
            this.filter.endTime3 = e ? e[1] : null
        },
        //每页数量改变
        Sizechange(val) {
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.filter.currentPage = val;
            this.getList()
        },
        //排序查询
        sortchange({ order, prop }) {
            if (prop) {
                this.filter.orderBy = prop
                this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        async exportList() {
            this.loading = true
            const res = await ExportDisPerform(this.filter)
            this.loading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '分销人员业绩统计表' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
        clear() {
            var startTime = this.filter.startTime;
            var endTime = this.filter.endTime;
            var orderBy = this.filter.orderBy;
            var isAsc = this.filter.isAsc;
            this.filter = {
                startTime: startTime,
                endTime: endTime,
                orderBy:orderBy,
                isAsc:isAsc
            };
        },
        async setChatTimeRange() {
          let endDate;
          if (Array.isArray(this.timerange) && this.timerange[1]) {
            endDate = dayjs(this.timerange[1]);
          } else {
            endDate = dayjs().subtract(1, 'day'); // 默认取昨天
          }
          const startDate = endDate.subtract(1, 'month').startOf('month');
          this.chatInfo.startTime = startDate.format('YYYY-MM-DD');
          this.chatInfo.endTime = endDate.format('YYYY-MM-DD');
          this.chatProp.chatTime = [startDate.format('YYYY-MM-DD'), endDate.format('YYYY-MM-DD')];
        },
        //打开趋势图
        async openChart(row) {
            this.chatProp.chatDialog = true;
            this.chatInfo = JSON.parse(JSON.stringify(this.filter))
            await this.setChatTimeRange();
            this.chatInfo.salesPerson = row.salesPerson;
            this.chatInfo.summary=false;
            this.chatInfo.prop=null;
            let that = this;
            this.$nextTick(() => {
                that.getChart();
            })
        },
        async openSummary(property)
        {
            this.chatProp.chatDialog = true;
            this.chatInfo = JSON.parse(JSON.stringify(this.filter))
            await this.setChatTimeRange();
            this.chatInfo.summary=true;
            this.chatInfo.prop=property;
            let that = this;
            this.$nextTick(() => {
                that.getChart();
            })
        },
        //初始化趋势图
        async getChart() {
            this.chatProp.chatLoading = true;
            var { success, data } = await GetTrendChart(this.chatInfo);
            if (success) {
                this.chatProp.data = data
                this.chatProp.chatDialog = true
                this.chatProp.chatLoading = false
            }
        },
        async changChartTime(e)
        {
            this.chatInfo.startTime = e ? e[0] : null
            this.chatInfo.endTime = e ? e[1] : null
            await  this.getChart();
        }
    },
};
</script>

<style lang="scss" scoped></style>
