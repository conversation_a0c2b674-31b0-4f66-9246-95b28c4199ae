<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="收寄开始日期" end-placeholder="收寄结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.batchNumber" placeholder="批次号" maxlength="50" clearable class="publicCss" />
        <el-select v-model="ListInfo.expressName" clearable filterable placeholder="快递公司" class="publicCss"  @change="getprosimstatelist(2)">
          <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-select v-model="ListInfo.prosimstate" clearable filterable placeholder="请选择快递站点" style="width: 130px">
                        <el-option label="暂无站点" value="" />
                        <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
                    </el-select>
        <el-select v-model="ListInfo.Warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 110px">
            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="onImport">新增</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
      </div>
    </template>
    <vxetablebase :id="'handlingMaterial202410161759'" :tablekey="'handlingMaterial202410161759'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex">
              <el-button type="text" @click="onEdit(row)">编辑</el-button>
              <el-button type="text" @click="onDelete(row)">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 75px;">
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="editTime" :visible.sync="editDialogVisible" width="25%" v-dialogDrag
      :close-on-click-modal="false">
      <div style="height: 420px;">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-form-item label="快递公司" prop="expressCompanyId">
            <el-select v-model="ruleForm.expressCompanyId" placeholder="快递公司" @change="getprosimstatelist(1)" class="editCss">
              <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="快递站点:">
                    <el-select v-model="ruleForm.prosimstate" placeholder="请选择快递站点"    style="width: 130px">
                        <el-option label="暂无站点" value="" />
                        <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
                    </el-select>
                </el-form-item>
          <el-form-item label="发货仓库" prop="Warehouse">
            <el-select v-model="ruleForm.warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 110px">
                        <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
          </el-form-item>
          <el-form-item label="区域" prop="province">
            <el-input v-model.trim="ruleForm.province" placeholder="区域" maxlength="50" clearable class="editCss" />
          </el-form-item>
          <el-form-item label="费用类型" prop="expenseType">
            <el-select v-model="ruleForm.expenseType" placeholder="费用类型" class=editCss>
              <el-option label="操作费" :value="1"></el-option>
              <el-option label="包材费" :value="2"></el-option>
              <el-option label="加收费" :value="3"></el-option>
              <el-option label="其他" :value="4"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="金额" prop="amount">
            <el-input-number v-model.trim="ruleForm.amount" placeholder="金额" :min="0" :max="99999999" :precision="2"
              :controls="false" class=editCss />
          </el-form-item>
          <el-form-item label="最小重量" prop="amount">
            <el-input-number v-model.trim="ruleForm.qsWeight" placeholder="最小重量" :min="0" :max="99999999" :precision="4"
              :controls="false" class=editCss />
          </el-form-item>
          <el-form-item label="最大重量" prop="amount">
            <el-input-number v-model.trim="ruleForm.endWeight" placeholder="最大重量" :min="0" :max="99999999" :precision="4"
              :controls="false" class=editCss />
          </el-form-item>
          <el-form-item label="日期" prop="edittimeRanges">
            <el-date-picker v-model="ruleForm.edittimeRanges" type="daterange" unlink-panels range-separator="至"
              start-placeholder="起始开始日期" end-placeholder="起始结束日期" class=editCss :value-format="'yyyy-MM-dd'"
              @change="editchangeTime">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div style="display: flex;align-items: center;justify-content: center;">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="resetForm">保存</el-button>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { warehouselist, formatWarehouseNew } from "@/utils/tools";

import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getExpressComanyAll, importExpressSundryData, getSundryExpressData, addOrEditSundryExpress,getExpressComanyStationName ,delSundryExpressData} from "@/api/express/express";
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressCompany', label: '快递公司', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'prosimstate', label: '快递站点',  formatter: (row) => row.prosimstateName, type: 'custom' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouse', label: '发货仓库', formatter: (row) => formatWarehouseNew(row.warehouse)  },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'province', label: '区域', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'expenseTypeString', label: '费用类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'amount', label: '金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'qsWeight', label: '最小重量', formatter: (row) =>  row.qsWeight+' '},
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'endWeight', label: '最大重量',  formatter: (row) =>  row.endWeight+' '},
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'startDateTime', label: '起始日期', formatter: (row) => dayjs(row.startDateTime).format('YYYY-MM-DD') },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'endDateTime', label: '结束日期', formatter: (row) => dayjs(row.endDateTime).format('YYYY-MM-DD') },
]
export default {
  name: "handlingMaterial",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      dialogVisible: false,
      fileList: [],
      uploadLoading: false,
      fileparm: {},
      editTime: '新增',
      rules: {
        expressCompanyId: [{ required: true, message: '请选择快递公司', trigger: 'change' }],
        prosimstate: [{ required: true, message: '请选择站点', trigger: 'change' }],
        warehouse: [{ required: true, message: '请选择发货仓库', trigger: 'change' }],
        expenseType: [{ required: true, message: '请选择费用类型', trigger: 'change' }],
        amount: [{ required: true, message: '请输入金额', trigger: 'change' }],
        edittimeRanges: [{ required: true, message: '请选择日期', trigger: 'change' }],
        // province: [{ required: true, message: '请输入区域', trigger: 'change' }],
      },
      editDialogVisible: false,
      ruleForm: {
        expressCompanyId: null,
        expenseType: null,
        amount: undefined,
        qsWeight:null,
        endWeight:null,
        edittimeRanges: [],
        province: null,
        warehouse: null,
        prosimstate: null,
      },
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        expressName: null,
        Warehouse: null,
        prosimstate: null,
        batchNumber: null,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      expresscompanylist: [],
      warehouselist: warehouselist,
      prosimstatelist: [],
    }
  },
  async mounted() {
    await this.getList()
    await this.init()
  },
  methods: {
    onEdit(row) {
      this.ruleForm = JSON.parse(JSON.stringify(row))
      row.startDateTime = dayjs(row.startDateTime).format('YYYY-MM-DD')
      row.endDateTime = dayjs(row.endDateTime).format('YYYY-MM-DD')
      this.$set(this.ruleForm, 'edittimeRanges', [row.startDateTime, row.endDateTime])
      this.$set(this.ruleForm, 'expressCompanyId', row.expressCompanyId)
      this.$set(this.ruleForm, 'expenseType', row.expenseType)
      this.$set(this.ruleForm, 'amount', row.amount)
      this.$set(this.ruleForm, 'qsWeight', row.qsWeight)
      this.$set(this.ruleForm, 'endWeight', row.endWeight)
      this.$set(this.ruleForm, 'warehouse', row.warehouse)
      this.$set(this.ruleForm, 'province', row.province)
      this.$set(this.ruleForm, 'prosimstate', row.prosimstate ? row.prosimstate + '' : null)
      this.getprosimstatelist(1)
      this.editTime = '编辑'
      this.editDialogVisible = true
    },
    async onDelete(row) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await delSundryExpressData({ id: row.id });
        if (success) {
          this.$message.success('删除成功')
          this.getList()
        }
      })
    },
    async getprosimstatelist (val) {
                var id;
                if (val == 1)
                    id = this.ruleForm.expressCompanyId
                else if (val == 2) {
                    id = this.ListInfo.expressName
                    this.ListInfo.prosimstate = null
                }

                var res = await getExpressComanyStationName({ id: id });
                if (res?.code) {
                    this.prosimstatelist = res.data
                }
    },
    resetForm() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          const { success } = await addOrEditSundryExpress(this.ruleForm)
          if (success) {
            this.editDialogVisible = false
            this.$message.success('操作成功')
            this.getList()
          }
        }
      })
    },
    ruleFormClear() {
      this.ruleForm = {
        expressCompanyId: null,
        expenseType: null,
        amount: undefined,
        edittimeRanges: [],
        id: null,
        province: null
      }
    },
    editchangeTime(e) {
      this.ruleForm.startDateTime = e ? e[0] : null
      this.ruleForm.endDateTime = e ? e[1] : null
    },
    onImport() {
      this.editTime = '新增'
      this.ruleFormClear()
      this.ruleForm.id = null
      this.editDialogVisible = true;
    },
    async init() {
      const res = await getExpressComanyAll({});
      if (!res?.success) return
      this.expresscompanylist = res.data;
      this.expresscompanylist.forEach(item => {
        item.id = Number(item.id)
      })
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importExpressSundryData(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await getSundryExpressData(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

.editCss {
  width: 270px;
}
</style>
