<template>
    <my-container v-loading="pageLoading">
      <template #header>
       <div> 
         <el-button-group> 
            <el-button style="padding: 0;margin: 0;">
                <el-input v-model.trim="filter.proCode"  maxlength="50" clearable  placeholder="商品ID" style="width:130px;"/>
            </el-button>
           
            <el-button style="padding: 0;margin: 0;">
               <el-input v-model.trim="filter.productName"  clearable maxlength="50"  placeholder="商品名称" style="width:130px;"/>
            </el-button>
            <el-button style="padding: 0;width: 200px;">
              <el-select v-model="styleCode" multiple filterable remote reserve-keyword placeholder="系列编码" collapse-tags clearable :remote-method="remoteMethod" :loading="searchloading" style="width: 200px"> 
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
                <el-date-picker style="width: 210px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" 
                :clearable="false"
                value-format="yyyy-MM-dd" range-separator="至"
                                  start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-button>
             <el-button style="padding: 0;">
               <el-select filterable v-model="filter.platform" placeholder="请选择平台" @change="onchangeplatform" clearable style="width: 100px">
                 <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
               </el-select>
            </el-button>
            <el-button style="padding: 0;">
              <el-select filterable v-model="filter.shopCode" clearable placeholder="店铺" style="width: 120px">
               
                <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"></el-option>
              </el-select>
            </el-button>
           
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组" style="width: 90px">
              <el-option key="无运营组" label="无运营组" :value="0"></el-option>
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.operateSpecialUserId" collapse-tags clearable placeholder="运营专员" style="width: 90px">
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.userId" collapse-tags clearable placeholder="运营助理" style="width: 90px">
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </el-button>
         
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.userId3" collapse-tags clearable placeholder="备用负责人" style="width: 90px">
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </el-button>
          
          <el-button style="padding: 0;">
            <el-select filterable v-model="profit3UnZero"  multiple collapse-tags clearable placeholder="毛三区间（可多选）" style="width: 200px">
              <el-option label="0%以下" :value="0"/>
              <el-option label="0-5%" :value="5"/>
              <el-option label="5-10%" :value="10"/>
              <el-option label="10-15%" :value="15"/>
              <el-option label="15-20%" :value="20"/>
              <el-option label="20%以上" :value="21"/>
            </el-select>
          </el-button>
          
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.groupType" collapse-tags clearable placeholder="分类汇总" style="width: 90px">
              <el-option label="按ID汇总" :value="1"/>
              <el-option label="按运营组汇总" :value="2"/>
              <el-option label="按店铺汇总" :value="3"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.Cycle" collapse-tags clearable placeholder="周期汇总" style="width: 90px">
              <el-option label="按周汇总" :value="1"/>
              <el-option label="按月汇总" :value="2"/>
              
            </el-select>
          </el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
         </el-button-group>
       </div>
      </template>
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'   :isSelection='false'
            :showsummary='true'  :summaryarry='summaryarry' :tableData='financialreportlist'
            :tableCols='tableCols' :tableHandles='tableHandles'  :loading="listLoading"  height="100%">
         
      </ces-table>
      <!--分页-->
      <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
      </template>
  
      <el-dialog title="系列编码明细" :visible.sync="StyleCodeDetail.visible" width="80%" v-dialogDrag>
        <div>    
         <StyleCodeDetail ref="StyleCodeDetail" :filter="StyleCodeDetail.filter" style="height:600px;"></StyleCodeDetail>
        </div>
      </el-dialog>

      <el-drawer title="编辑处理方案" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editVisible" 
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
       <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
       <div class="drawer-footer">
        <el-button @click.native="editVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editLoading" @click="onEditSubmit" />
      </div>
    </el-drawer>
    </my-container>
  </template>
  <script>
  import {getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
  import {getDirectorGroupList,getDirectorList,getList as getshopList} from '@/api/operatemanage/base/shop'
  import {formatPlatform,formatTime,formatYesornoBool,formatLinkProCode,platformlist} from "@/utils/tools";
  import { getDailyAnalysisList as pageProductDayReport,queryDayReportAnalysis,exportProductDayReport,getParm,setParm,editDailyAnalysis} from '@/api/bookkeeper/reportday'
  import {getAllProBrand} from '@/api/inventory/warehouse'
  import cesTable from "@/components/Table/table.vue";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import InputMult from "@/components/Comm/InputMult";
  import { Loading } from 'element-ui';
  import { ruleDirectorGroup } from '@/utils/formruletools'
  import importmodule from '@/components/Bus/importmodule'
  import { getListByStyleCode } from "@/api/inventory/basicgoods"
  import StyleCodeDetail from '@/views/bookkeeper/reportday/StyleCodeDetail'
  import inputYunhan from "@/components/Comm/inputYunhan";
  let loading;
  const startLoading = () => {
    loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
    });
  }; 
  const tableCols =[
           {istrue:true ,prop:'yearMonthDay',label:'年月日',sortable:'custom', width:'80',formatter:(row)=>!row.yearMonthDay? formatTime(row.periodStart,'YYYY-MM-DD')+'--'+formatTime(row.periodEnd,'YYYY-MM-DD'):row.yearMonthDay},
          {istrue:true ,prop:'styleCode',label:'系列编码',sortable:'custom', width:'80',formatter:(row)=> row.styleCode || ' ',type:'click',handle:(that,row)=>that.showStyleCodeDetail(row)},
          {istrue:true ,prop:'shopCode',label:'店铺名称',sortable:'custom', width:'80',formatter:(row)=> row.shopName},
           {istrue:true ,prop:'platform',fix:true,label:'平台', width:'80',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
           { istrue: true, label: '小组头像',  width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
  { istrue: true, prop: 'groupId', exportField: 'groupName', label: '小组', sortable: 'custom', width: '70', formatter: (row) => row.groupName,type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, },
  { istrue: true, label: '专员头像', width: '70',type:'ddAvatar',ddInfo:{type:2,prop:'operateSpecialUserId'} },
  { istrue: true, prop: 'operateSpecialUserId', exportField: 'operateSpecialUserName', label: '运营专员', sortable: 'custom', width: '70', formatter: (row) => row.operateSpecialUserName,type:'ddTalk',ddInfo:{type:2,prop:'operateSpecialUserId',name:'operateSpecialUserName'}, },
          {istrue:true ,prop:'userId',label:'运营助理',sortable:'custom', width:'100',permission:"cgcoltxpddprsi",formatter:(row)=> row.userName},
          {istrue:true ,prop:'userId3',label:'备用负责人',sortable:'custom', width:'90',permission:"cgcoltxpddprsi",formatter:(row)=> row.userName3},
          {istrue:true ,prop:'proCode',fix:true,label:'商品ID', width:'120',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
          {istrue:true ,prop:'goodsName',label:'商品名称',sortable:'custom', width:'105'},
          {istrue:true ,prop:'onTime',label:'上架时间',sortable:'custom', width:'100',permission:"cgcoltxpddprsi",formatter:(row)=>formatTime(row.onTime, 'YYYY-MM-DD') },
          {istrue:true ,prop:'orderCount',label:'订单量',sortable:'custom', width:'90',formatter:(row)=> !row.orderCount?" ": row.orderCount},
          {istrue:true ,prop:'saleAmont',label:'销售金额',sortable:'custom', width:'90',formatter:(row)=> !row.saleAmont?" ": row.saleAmont},
          {istrue:true ,prop:'alladv',label:'总广告费',sortable:'custom', width:'90',formatter:(row)=> !row.alladv?" ": row.alladv},
          {istrue:true ,prop:'advratio',label:'广告占比',sortable:'custom', width:'90',formatter:(row)=> !row.advratio?" ": (row.advratio*100).toFixed(2)+'%'},
          {istrue:true ,prop:'profit3',label:'毛三利润',sortable:'custom', width:'110',type:'custom',tipmesg:'毛二利润-预估费用',formatter:(row)=> !row.profit3?" ": row.profit3?.toFixed(2)},
          {istrue:true ,prop:'profit3Rate',label:'毛三利润率',type:'custom',tipmesg:'毛三利润/销售金额',sortable:'custom', width:'120',formatter:(row)=> !row.profit3Rate?" ": (row.profit3Rate*100).toFixed(2)+'%'},
          {istrue:true ,prop:'treatmentScheme',label:'处理方案',sortable:'custom', width:'90',formatter:(row)=> !row.treatmentScheme?" ": row.treatmentScheme},
          {istrue:true ,prop:'dealTime',label:'处理时间',sortable:'custom', width:'90',formatter:(row)=> !row.dealTime?" ": row.dealTime},
          {istrue:true,type:'button', width:'55',label:'操作', width:'100',btnList:[{label:"编辑处理方案",handle:(that,row)=>that.onHand(row)}]},
    ];
  const tableHandles=[  
       
        ];
  
  export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable,InputMult,importmodule,inputYunhan,StyleCodeDetail},
    data() {
      return {
        that:this,
        filter: {
          reportType:1,
          platform:null,
          shopCode:null,
          proCode:null,
          styleCode:null,
          productName:null,
          brandId:null,
          groupId:null,
          startTime: null,
          endTime: null,
          timerange:null,
          // 运营助理
          userId :null,
          // 车手
          userId2:null,
          // 备用
          userId3:null,
          // 运营专员 ID
          operateSpecialUserId:null,
          profit2UnZero:null,
          profit3UnZero :null,
          profit4UnZero:null,
          groupType:null,
          Cycle:null
        },
        
        onimportfilter:{
          yearmonthday:null,
        },
        editVisible:false,
        styleCode:null,
        profit3UnZero:null,
        options:[],
        platformlist:platformlist,
        shopList:[],
        userList:[],
        brandlist:[],
        grouplist:[],
        directorlist:[],
        financialreportlist: [],
        tableCols:tableCols,
        tableHandles:tableHandles,
        total: 0,
        pager:{OrderBy:"",IsAsc:false},
        sels: [], // 列表选中列
        listLoading: false,
        earchloading:false,
        pageLoading: false,
        summaryarry:{},
        selids:[],
        fileList:[],
        dialogVisibleData:false,
        dialogVisible:false,
        uploadLoading:false,
        importFilte:{},
        fileList:[],
        fileparm:{},
        editparmVisible:false,
        editLoading:false,
        editparmLoading:false,
        drawervisible:false,
        searchloading:false,
        /* dialogDrVisibleShengYi:false, */
        dialogDrVisible:false,
        expressfreightanalysisVisible:false,
        drparamProCode:'',
        autoform:{
                 fApi:{},
                 options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
                 rule:[]
          },
          StyleCodeDetail:{
            visible:false,
            filter:{
            StyleCode:''
            }
          },
          
        giftDetail:{visible:false},
        costDialog:{visible:false,rows:[]},
        buscharDialog:{visible:false,title:"",data:[]},
        drawervisible:false,
      };
    },
    async mounted() {
      await this.initform();
    },
    async created() {
      await this.init()
      await this.getShopList();
      await this.initformparm();
    },
    methods: {


      
    async onHand(row){
      this.formtitle='编辑';
      this.editVisible = true
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields()  
         this.$nextTick(async() =>{
          await this.autoform.fApi.setValue(row)
        })  
      
    },

    async onEditSubmit() {
      this.editLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          const res = await editDailyAnalysis(formData);
          if(res.code==1){
            this.$message.success('修改成功！');
             this.getList(); 
            this.editVisible=false;        
          }
        }else{}
     })
     this.editLoading=false;
    },


    async initform(){
       
       this.autoform.rule= [{type:'hidden',field:'id',title:'id',value: ''},
                     {type:'input',field:'yearMonthDay',title:'年月日',value: '',props:{readonly:true},col:{span:6}},
                   
                     {type:'input',field:'proCode',title:'产品ID',value: 0,props:{min:0,precision:0,readonly:true}},
                     {type:'DatePicker',field:'dealTime',title:'处理时间',value:'',validate: [{type: 'string', required: true, message:'请输入处理时间'}],props: {type:'date',format:'yyyy-MM-dd',placeholder:'处理时间'},col:{span:6}},
                     {type:'input',field:'treatmentScheme',title:'处理方案',value: '',props:{maxlength:100},col:{span:6}},
                                     
                    ]
                    
                    
    },
   

      datetostr(date) {
        var y = date.getFullYear();
        var m = ("0" + (date.getMonth() + 1)).slice(-2);
        var d = ("0" + date.getDate()).slice(-2);
        return y + "-" + m + "-" + d;
      },
      async init(){
          var date1 = new Date(); date1.setDate(date1.getDate()-10);
          var date2 = new Date(); date2.setDate(date2.getDate()-1);
          this.filter.timerange=[];
          this.filter.timerange[0]=this.datetostr(date1);
          this.filter.timerange[1]=this.datetostr(date2);
        },
     


      async showStyleCodeDetail(row){
        this.StyleCodeDetail.filter.StyleCode = row.styleCode;
       
        this.StyleCodeDetail.visible=true; 
         setTimeout(async () => {
          await this.$refs.StyleCodeDetail.onSearch(); 
        }, 100);

     },


     async onchangeplatform(val){
        this.categorylist =[]
        const res1 = await getshopList({platform:val,CurrentPage:1,PageSize:300});
        this.shopList=res1.data.list
     
      },
      //系列编码远程搜索
      async remoteMethod(query){
        if (query !== ''){
            this.searchloading == true
            setTimeout(async () => {
                const res = await getListByStyleCode({currentPage:1,pageSize:50, styleCode: query})
                this.searchloading = false
                res?.data?.forEach(f=>{
                this.options.push({value:f.styleCode,label:f.styleCode})
                });
            }, 200)
        }
        else{
            this.options = []
        }
      },


     
     async getShopList(){
        const res1 = await getAllShopList();
        this.shopList=[];
          res1.data?.forEach(f => {
            if(f.isCalcSettlement&&f.shopCode&&(f.platform==1||f.platform==8))
                this.shopList.push(f);
          });
          var res2= await getDirectorGroupList();
          this.grouplist = res2.data?.map(item => {return { value: item.key, label: item.value };}); 
  
          var res3= await getDirectorList();
          this.directorlist = res3.data?.map(item => {return { value: item.key, label: item.value };}); 
  
          var res4= await getAllProBrand();
          this.brandlist = res4.data.map(item => {
              return { value: item.key, label: item.value };
          });
      },
     async sortchange(column){
        if(!column.order)
          this.pager={};
        else
          this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
       await this.onSearch();
      },
      onRefresh(){
        this.onSearch()
      },
      async onSearch(){
        this.$refs.pager.setPage(1);
        await this.getList().then(res=>{  });
        // loading.close();
      },
      async getList(){
        this.filter.startTime =null;
        this.filter.endTime =null;
        if (this.filter.timerange) {
          this.filter.startTime = this.filter.timerange[0];
          this.filter.endTime = this.filter.timerange[1];
        }
        this.filter.styleCode = this.styleCode.join()
        
        if(this.profit3UnZero)
        this.filter.profit3UnZero = this.profit3UnZero.join()
       
        if(this.filter.Cycle==null)
        this.filter.Cycle=0;
        

        var that=this;
        var pager = this.$refs.pager.getPager();
        const params = {...pager,...this.pager,...this.filter};
       // this.listLoading = true;
        startLoading(); 
        const res = await pageProductDayReport(params).then(res=>{
            loading.close();
            that.total = res.data?.total;
            if(res?.data?.list&&res?.data?.list.length>0){
              for (var i in res.data.list) {
                if (!res.data.list[i].freightFee) {
                  res.data.list[i].freightFee =" ";
                }
              }
            }
            that.financialreportlist = res.data?.list;
            that.summaryarry=res.data?.summary;
            this.filter.profit3UnZero =null;
            if(this.filter.Cycle==0)
            this.filter.Cycle =null;
            
        });
      },

      selectchange:function(rows,row) {
        this.selids=[];
        rows.forEach(f=>{
          this.selids.push(f.id);
        })
      },
     onRefresh(){
        this.onSearch()
      },
    async updateruleGroup(groupid) {
       if(!groupid)
          this.autoformparm.fApi.resetFields()
       else{
         const res = await getParm({groupId:groupid})
         var arr = Object.keys(this.autoformparm.fApi);
         res.data.groupId=groupid;
         if(!res.data?.Profit3PredictRate) res.data.Profit3PredictRate=0;
         if(!res.data?.ShareRate) res.data.ShareRate=0;
         await this.autoformparm.fApi.setValue(res.data)
        }
      },
    
    async onstartImport(){
        this.dialogVisible=true;
      },
    
    showupload() {
       this.drawervisible=true;
    },
    async callbackProCode(val) {
          this.filter.proCode = val;
    },
  },
    
  };
  </script>
  <style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
      background-color: #fff;
    }

    /* .el-table  ::v-deep  .cell{
         color: black;
    } */
    ::v-deep .el-table td.el-table__cell div{

      color: gray;

    }
    
  </style>
   
   