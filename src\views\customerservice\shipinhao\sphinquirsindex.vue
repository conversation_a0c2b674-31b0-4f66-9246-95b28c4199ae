<template>
  <my-container>
    <!-- 顶部操作 -->
    <el-tabs v-model="activeName" :before-leave="beforeLeave" style="height:94%;">
      <el-tab-pane name="switch">
        <span slot="label">
          <el-switch v-model="switchshow" :disabled="switchshowPermission" @change="changeShowgroup" active-text="售后管理"
            inactive-text="售前管理">
          </el-switch>
        </span>
      </el-tab-pane>

      <el-tab-pane v-if="!switchshowSqSh" label="分组管理(售前)" name="tab0" style="height: 100%;">
        <group :filter="filter" ref="group" style="height: 100%;" @callBackInfo="handleInfo"></group>
      </el-tab-pane>
      <el-tab-pane v-if="!switchshowSqSh" label="咨询数据导入(售前)" name="tab1" style="height: 100%;">
        <inquirs :filter="filter" ref="inquirs" style="height: 100%;"></inquirs>
      </el-tab-pane>
      <el-tab-pane v-if="!switchshowSqSh" label="组效率统计(售前)" name="tab2" style="height: 100%;">
        <sqSphGroupEfficiencyStatistics :filter="filter" ref="refsqSphGroupEfficiencyStatistics" style="height: 100%;"
          :pickerOptions="pickerOptions" @clickgroupname="clickgroupname" />
      </el-tab-pane>
      <el-tab-pane v-if="!switchshowSqSh" label="店效率统计(售前)" name="tab3" style="height: 100%;">
        <shopInquirs :filter="filter" ref="shopInquirs" style="height: 100%;"></shopInquirs>
      </el-tab-pane>
      <el-tab-pane v-if="!switchshowSqSh" label="个人效率统计(售前)" name="tab4" style="height: 100%;">
        <sqSphPersonalEfficiencyStatistics :filter="filter" ref="refsqPersonalEfficiencyStatistics"
          style="height: 100%;" :pickerOptions="pickerOptions" @clickgroupname="clickgroupname" />
      </el-tab-pane>
      <el-tab-pane v-if="!switchshowSqSh" label="未匹配咨询数据" name="tab5" style="height: 100%;">
        <inquirsno :filter="filter" ref="inquirsno" style="height: 100%;"></inquirsno>
      </el-tab-pane>


      <el-tab-pane v-if="switchshowSqSh" label="分组管理(售后)" name="tab00" style="height: 100%;">
        <shgroup :filter="filter" ref="shgroup" style="height: 100%;" @callBackInfo="handleInfoH"></shgroup>
      </el-tab-pane>
      <el-tab-pane v-if="switchshowSqSh" label="咨询数据导入(售后)" name="tab11" style="height: 100%;">
        <shinquirs :filter="filter" ref="shinquirs" style="height: 100%;"></shinquirs>
      </el-tab-pane>
      <el-tab-pane v-if="switchshowSqSh" label="组效率统计(售后)" name="tab22" style="height: 100%;">
        <shSphGroupEfficiencyStatistics :filter="filter" ref="refshSphGroupEfficiencyStatistics" style="height: 100%;"
          :pickerOptions="pickerOptions" @clickgroupname="clickgroupname" />
      </el-tab-pane>
      <el-tab-pane v-if="switchshowSqSh" label="店效率统计(售后)" name="tab33" style="height: 100%;">
        <shshopInquirs :filter="filter" ref="shShopInquirs" style="height: 100%;"></shshopInquirs>
      </el-tab-pane>
      <el-tab-pane v-if="switchshowSqSh" label="个人效率统计(售后)" name="tab44" style="height: 100%;">
        <shSphPersonalEfficiencyStatistics :filter="filter" ref="refshPersonalEfficiencyStatistics"
          style="height: 100%;" :pickerOptions="pickerOptions" @clickgroupname="clickgroupname" />
      </el-tab-pane>
      <el-tab-pane v-if="switchshowSqSh" label="未匹配咨询数据" name="tab55" style="height: 100%;">
        <shinquirsno :filter="filter" ref="shInquirsno" style="height: 100%;"></shinquirsno>
      </el-tab-pane>

    </el-tabs>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import group from "@/views/customerservice/shipinhao/group.vue";
import inquirs from "@/views/customerservice/shipinhao/inquirs.vue";
import sqSphGroupEfficiencyStatistics from "@/views/customerservice/shipinhao/sqSphGroupEfficiencyStatistics.vue";
import shopInquirs from "@/views/customerservice/shipinhao/shopInquirs.vue";
import sqSphPersonalEfficiencyStatistics from "@/views/customerservice/shipinhao/sqSphPersonalEfficiencyStatistics.vue";
import inquirsno from "@/views/customerservice/shipinhao/inquirsno.vue";

import shgroup from "@/views/customerservice/shipinhao/shgroup.vue";
import shinquirs from "@/views/customerservice/shipinhao/shinquirs.vue";
import shSphGroupEfficiencyStatistics from "@/views/customerservice/shipinhao/shSphGroupEfficiencyStatistics.vue";
import shshopInquirs from "@/views/customerservice/shipinhao/shshopInquirs.vue";
import shSphPersonalEfficiencyStatistics from "@/views/customerservice/shipinhao/shSphPersonalEfficiencyStatistics.vue";
import shinquirsno from "@/views/customerservice/shipinhao/shinquirsno.vue";

import dayjs from "dayjs";
export default {
  name: "sphinquirsindex",
  components: {
    MyContainer, group, inquirs, sqSphGroupEfficiencyStatistics, shopInquirs, sqSphPersonalEfficiencyStatistics, inquirsno,
    shgroup, shinquirs, shinquirsno, shSphGroupEfficiencyStatistics, shSphPersonalEfficiencyStatistics, shinquirsno, shshopInquirs
  },
  data() {
    return {
      that: this,
      pageLoading: '',
      filter: {},
      activeName: 'tab0',

      //默认展示售前 true售前，false售后
      switchshow: false,
      switchshowSqSh: false,
      switchshowPermission: false,
      infoBool: false,//是否包含离组
      infoBoolH: false,
      pickerOptions: {
        shortcuts: [
          {
            text: '前一天',
            onClick(picker) {
              const start = dayjs().subtract(1, 'day').toDate();
              const end = dayjs().subtract(1, 'day').toDate();
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '近一周',
            onClick(picker) {
              const start = dayjs().subtract(7, 'day').toDate();
              const end = dayjs().toDate();
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '近一个月',
            onClick(picker) {
              const start = dayjs().subtract(1, 'month').toDate();
              const end = dayjs().toDate();
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
    }
  },
  mounted() {
  },
  methods: {
    beforeLeave(visitName, currentName) {
      if (visitName == "switch")
        return false;
    },
    changeShowgroup() {
      if (!this.switchshow) {
        this.activeName = 'tab0';
        this.switchshowSqSh = false;
      }
      else {
        this.activeName = 'tab00';
        this.switchshowSqSh = true;
      }
    },
    reload() {
      //刷新其他页面的下拉框选项
      this.$refs.inquirssh.setShopSelect();
      this.$refs.groupinquirsstatisticssh.setGroupSelect();
      this.$refs.shopinquirsstatisticssh.setShopSelect();
      this.$refs.inquirsstatisticssh.setGroupSelect();
      this.$refs.refsqSphGroupEfficiencyStatistics.setGroupSelect();
      this.$refs.refshSphGroupEfficiencyStatistics.setGroupSelect();
      this.$refs.inquirsstatisticsmonth.setGroupSelect();
      this.$refs.inquirsstatisticsmonth.setShopSelect();
      this.$refs.refshPersonalEfficiencyStatistics.setGroupSelect();
      this.$refs.refsqPersonalEfficiencyStatistics.setShopSelect();
    },
    clickgroupname(params) {
      if (this.switchshow) {
        this.activeName = 'tab44';
        this.$refs.refshPersonalEfficiencyStatistics.loadData2(params)
      } else {
        this.activeName = 'tab4';
        this.$refs.refsqPersonalEfficiencyStatistics.loadData2(params)
      }
    },
    handleInfo(data) {
      this.infoBool = data;
    },
    handleInfoH(data) {
      this.infoBoolH = data;
    }
  },
}
</script>
<style lang="scss" scoped>
.my-search::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
