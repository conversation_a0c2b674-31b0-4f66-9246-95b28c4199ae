<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
          <el-select filterable v-model="ListInfo.accountTypeist" placeholder="请选择账单项目" collapse-tags  multiple  clearable style="width: 230px">
              <el-option label="达人佣金" value="达人佣金" />
              <el-option label="技术服务费" value="技术服务费" />
              <el-option label="运费险" value="运费险" />
              <el-option label="货款" value="货款" />
              <el-option label="退款" value="退款" />
              <el-option label="团长抽佣" value="团长抽佣" />
              <el-option label="提现" value="提现" />
              <el-option label="带货机构服务费" value="带货机构服务费" />
              <el-option label="订单支付" value="订单支付" />
              <el-option label="极速退款垫资" value="极速退款垫资" />
              <el-option label="回补极速退款垫资" value="回补极速退款垫资" />
              <el-option label="未处理" value="未处理" />
          </el-select>

          <el-select filterable v-model="ListInfo.billTypeList" placeholder="请选择ERP账务类型" collapse-tags  multiple  clearable style="width: 230px">
            <el-option label="达人佣金" value="达人佣金" />
              <el-option label="技术服务费" value="技术服务费" />
              <el-option label="运费险" value="运费险" />
              <el-option label="团长抽佣" value="团长抽佣" />
              <el-option label="带货机构服务费" value="带货机构服务费" />
              <el-option label="无" value="无" />
          </el-select>
          <el-input v-model.trim="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss" />

        <el-button type="primary" @click="getList('search')">搜索</el-button>
      </div>
    </template>
    <vxetablebase :id="'breachPaymentVideo202412021559'" :tablekey="'breachPaymentVideo202412021559'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getNewBillingCharge_WeChat } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'yearMonthDay', label: '日期', },
  { istrue: true, prop: 'billingItem', label: '账单项目', tipmesg: '', width: '100', sortable: 'custom', },
  { istrue: true, prop: 'billType', label: 'ERP账务类型', tipmesg: '', width: '100', sortable: 'custom', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'serialNumber', label: '流水单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'accountingTime', label: '记账时间', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'transactionType', label: '动帐类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'incomeExpenseType', label: '收支类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'amount', label: '收支金额', },
  {  width: 'auto', align: 'center', prop: 'amountOut', label: '金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'accountBalance', label: '账户余额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '关联订单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'relatedAfterSaleNumber', label: '关联售后单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'relatedWithdrawalNumber', label: '关联提现单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'relatedPolicyNumber', label: '关联保单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'details', label: '详情', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'giftNumber', label: '礼物单号', },
  // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '记账时间', },
]
export default {
  name: "breachPaymentVideo",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        shopName: null,//店铺
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    // await this.getList()
    if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    //导出数据,使用时将下面的方法替换成自己的接口
    // async exportProps() {
    //     const { data } = await exportStatData(this.ListInfo)
    //     const aLink = document.createElement("a");
    //     let blob = new Blob([data], { type: "application/vnd.ms-excel" })
    //     aLink.href = URL.createObjectURL(blob)
    //     aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
    //     aLink.click()
    // },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await getNewBillingCharge_WeChat(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
