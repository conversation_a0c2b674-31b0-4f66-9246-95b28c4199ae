<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="filter"
        @submit.native.prevent>
      <el-form-item label="年月:">
          <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
        </el-form-item>
        <el-form-item label="所属店铺:" label-position="right" >
          <el-select filterable v-model="filter.shopCode" placeholder="请选择" class="el-select-content">
            <el-option 
              v-for="item in shopList"
              :key="item.shopCode"
              :label="item.shopName"
              :value="item.shopCode">             
            </el-option>
          </el-select>
        </el-form-item>
               <el-form-item label="订单编号:" label-position="right" >
            <el-input v-model="filter.serialNumberOrder" style="width:183px;"/>
        </el-form-item>
        <el-form-item label="商品Id:" label-position="right" >
            <el-input v-model="filter.proCode" style="width:183px;"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' 
              :hasexpand='false' @sortchange='sortchange' :tableData='ZTCKeyWordList' 
              @select='selectchange' :isSelection='false'
         :tableCols='tableCols' :loading="listLoading" style="height:730px" :isSelectColumn='false'>
      <el-table-column type="expand">
        <template slot-scope="props">
        <div>
          <el-table :data="props.row.detaildata" style="width: 100%">
            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
            </el-table-column>
          </el-table>
        </div>
      </template>
      </el-table-column>
       <template slot='extentbtn'>
          <el-button-group>            
           
          </el-button-group>
        </template>
    </ces-table>    
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
    </template>
  </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import {getSaleDetailPddPageList as getPageList} from '@/api/bookkeeper/financialDetail'

const tableCols =[
      {istrue:true,prop:'orderNumber',label:'订单号',sortable:'custom', width:'auto',type:'html'},
      {istrue:true,prop:'irderStatus',label:'订单状态',sortable:'custom', width:'auto',type:'html'},
      {istrue:true,prop:'countProduct',label:'商品数量(件)',sortable:'custom', width:'auto',type:'html'},
      {istrue:true,prop:'timePay',label:'支付时间',sortable:'custom', width:'auto',type:'html'},
      {istrue:true,prop:'proCode',label:'商品id',sortable:'custom', width:'auto',type:'html'},
      {istrue:true,prop:'bianma',label:'商家编码-SKU维度',sortable:'custom', width:'auto',type:'html'},
      {istrue:true,prop:'amountInReal',label:'商家实收金额(元)',sortable:'custom', width:'auto',type:'html',formatter:(row)=>{return row.amountInReal?.toFixed(4)}},
      {istrue:true,prop:'expressNumber',label:'快递单号',sortable:'custom', width:'auto',type:'html'},
     ];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      filter: {
        yearMonth:null,
        shopCode:null,
      },
      shopList:[],
      userList:[],
      groupList:[],
      ZTCKeyWordList: [],
      tableCols:tableCols,
      total: 0,
      pager:{OrderBy:"id",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
    };
  },
  async mounted() {
    await this.getShopList();
  },
  methods: {
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=[];
        res1.data?.forEach(f => {
          if(f.isCalcSettlement&&f.shopName.indexOf("拼多多")>=0&&f.shopCode)
              this.shopList.push(f);
        });
    },
    onRefresh(){
        this.onSearch()
    },
    onSearch(){
       this.$refs.pager.setPage(1);
       this.getList();
    },
    async getList(){
      var pager = this.$refs.pager.getPager();
      const params = {...pager,...this.pager,...this.filter,};
      this.listLoading = true;
      const res = await getPageList(params);
      this.listLoading = false;
      this.total = res.data?.total
      this.ZTCKeyWordList = res.data?.list;
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>