<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <div style="display:flex;height:100px;" v-if="[1,2].includes(filter.dataType)">
                <div :style="boxStyleColor" class="transition-box" @click="setInputNumRangeValue($event,1)">
                    <p class="transition-title">已超时</p>
                    <p :style="boxStyleColor" class="transition-sum">{{overCount}}</p>
                </div>
                <div :style="boxStyleColor" class="transition-box" @click="setInputNumRangeValue($event,2)">
                    <p class="transition-title">{{rangeList[0]}}</p>
                    <p :style="boxStyleColor" class="transition-sum">{{oneCount}}</p>
                </div>
                <div :style="boxStyleColor" class="transition-box" @click="setInputNumRangeValue($event,3)">
                    <p class="transition-title">{{rangeList[1]}}</p>
                    <p :style="boxStyleColor" class="transition-sum">{{twoCount}}</p>
                </div>
                <div :style="boxStyleColor" class="transition-box" @click="setInputNumRangeValue($event,4)">
                    <p class="transition-title">{{rangeList[2]}}</p>
                    <p :style="boxStyleColor" class="transition-sum">{{threeCount}}</p>
                </div>
                <div :style="boxStyleColor" class="transition-box" @click="setInputNumRangeValue($event,0)">
                    <p class="transition-title">历史超时总订单</p>
                    <p :style="boxStyleColor" class="transition-sum">{{historyCount}}</p>
                </div>
            </div>
            <p></p>
            <el-button style="padding: 0;margin: 0;">
                <inputYunhan title="请分行输入单号" :row="12" placeholder="内部订单号" :maxRows="100" :inputshow="0"
                    :clearable="true" @callback="callback($event, 'innerOrderId')" :inputt.sync="filter.innerOrderId">
                </inputYunhan>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
                <inputYunhan title="请分行输入编号" :row="12" placeholder="店铺编号" :maxRows="100" :inputshow="0"
                    :clearable="true" @callback="callback($event, 'shopId')" :inputt.sync="filter.shopId">
                </inputYunhan>
            </el-button>
            <el-button style="padding: 0;margin: 0;" v-if="[2,3].includes(filter.dataType)">
                <inputYunhan title="请分行输入单号" :row="12" placeholder="物流单号" :maxRows="100" :inputshow="0"
                    :clearable="true" @callback="callback($event, 'logisticsId')" :inputt.sync="filter.logisticsId">
                </inputYunhan>
            </el-button>
            <el-button style="padding: 0;margin:0;" v-if="filter.dataType == 1">
                    <el-date-picker v-model="filter.paytimerange" type="daterange" unlink-panels range-separator="至"
                        start-placeholder="付款开始时间" end-placeholder="付款结束时间" :picker-options="pickerOptions1" style="width:220px;height: 30px;" :value-format="'yyyy-MM-dd'" :clearable="false">
                    </el-date-picker>
            </el-button>
            <el-button style="padding: 0;margin: 0;" v-if="filter.dataType == 2">
                    <el-date-picker v-model="filter.deltimerange" type="daterange" unlink-panels range-separator="至"
                        start-placeholder="发货开始时间" end-placeholder="发货结束时间" :picker-options="pickerOptions1" style="width:220px;height: 30px;" :value-format="'yyyy-MM-dd'" :clearable="false">
                    </el-date-picker>
            </el-button>
            <el-button style="padding: 0;margin: 0;" v-if="filter.dataType == 3">
                    <el-date-picker v-model="filter.rectimerange" type="daterange" unlink-panels range-separator="至"
                        start-placeholder="物流日志更新开始时间" end-placeholder="物流日志更新结束时间" :picker-options="pickerOptions1" style="width:220px;height: 30px;" :value-format="'yyyy-MM-dd'" :clearable="false">
                    </el-date-picker>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
                <el-select filterable v-model="filter.groupIdList" collapse-tags clearable placeholder="运营组" multiple style="width: 150px">
                    <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
                <el-select style="width:180px" v-model="filter.platformTypeList" placeholder="选择平台" clearable multiple collapse-tags>
                    <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>
            </el-button>
        </template>
        <ces-table :ref="tablekey" :key="tablekey" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols'
            :loading="listLoading">
            <template slot='extentbtn' style="height:100px">
                <el-button-group>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <!-- <el-button type="primary" @click="startImport">导入</el-button>
                    <el-button type="primary" @click="downloadTemplate">下载导入模板</el-button> -->
                    <el-button type="primary" @click="onExport">导出</el-button>
                    <!-- <span class="titleRatio">
                        <li class="el-alert__icon el-icon-warning"></li> {{summaryarry.titleRatio}}
                    </span> -->
                </el-button-group>
            </template>
        </ces-table>

        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getlist" />
        </template>
        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%">
            <span v-loading="importLoading">
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :on-success="uploadSuccess" :http-request="uploadFile" :file-list="fileList">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px;" size="small" type="success"
                        @click="submitUpload">上传</el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            v-dialogDrag>
            <orderLogPage ref="orderLogPage" :orderNoInner="sendOrderNoInner" style="z-index:10000;height:600px" />
        </el-dialog>
        <el-dialog title="快递物流信息" v-if="drawerVisible" :visible.sync="drawerVisible" width="70%" height="600px"
            v-dialogDrag>
            <orderLogisticsPage ref="orderLogisticsPage" :ticketNumber="ticketNumber"
                :ticketCompanyName="ticketCompanyName" :platformType="platformType" style="z-index:10000;height:600px" />
        </el-dialog>
    </my-container>
</template>

<script>
    import dayjs from "dayjs";
    import MyContainer from '@/components/my-container'
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import orderLogPage from "@/views/order/logisticsWarning/LogisticsEarlyWarLog.vue";
    import orderLogisticsPage from "@/views/order/logisticsWarning/orderLogisticsRecord.vue";
    import { formatTime } from "@/utils";
    import { Loading } from 'element-ui';
    import inputYunhan from '@/components/Comm/inputYunhan.vue'
    import { getList as getshopList } from '@/api/operatemanage/base/shop'
    import { rulePlatform } from "@/utils/formruletools";
    import { getExpressComanyStationName, getExpressComanyAll } from "@/api/express/express";
    import { logisticsWarningPage, importLogisticsEarlyWarAsync, logisticsWarningExport, getOrderStatusListAsync } from "@/api/order/logisticsEarlyWarPage";
    import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
    import { getAllWarehouse } from '@/api/inventory/warehouse'
    const startTime = formatTime(dayjs().subtract(6, 'day'), "YYYY-MM-DD");
    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    export default {
        name: 'LogisticsEarlyWarPage',
        components: { cesTable, MyContainer, MyConfirmButton, orderLogPage, orderLogisticsPage, inputYunhan },
        props: {
            tablekey: {
                type: String,
                default: ''
            },
            logisticsEarlyWarDataType: {
                type: Number,
                default: 1
            },
            rangeList: {
                type: Array,
                default: []
            },
            remainingTimeStr: {
                type: String,
                default: ''
            },
            exportName: {
                type: String,
                default: ''
            },
        },
    data() {
        let that = this;
        return {
            that: this,
            pickerOptions1: {
                shortcuts: [{
                    text: '昨天',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
                        end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三天',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近半月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                        text: '最近三月',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                        }
                    }]
            },
            warehouselist: [],
            selectDate: '', // 记录首次选择的时间
            boxStyleColor: {
                "--color": "rgb(61, 127, 255)",
                "--shadow": "rgb(61 127 255 / 37%) 0px 0px 12px",
                "--bgColor": "linear-gradient(rgb(235, 246, 255) 0%, rgb(213, 230, 255) 100%)"
            },
            tableCols: [],
            filter: {
                ticketCompanyStation: '',
                platform: null,
                shopCode: "",
                paytimerange: [startTime, endTime],
                deltimerange: [startTime, endTime],
                rectimerange: [startTime, endTime],
                sendMsgTimerange: [],
                payDateStart: "",
                payDateEnd: "",
                deliveryDateStart: "",
                deliveryDateEnd: "",
                startSendMsgTime: "",
                endSendMsgTime: "",
                responsibleDepartment: null,
                dataType: this.logisticsEarlyWarDataType,
                isHistory: false,
                orderNo: "",
            },
            applyPickerOptions: {
                onPick: ({ maxDate, minDate }) => {
                    if (minDate && !maxDate) {
                        that.selectDate = minDate.getTime();
                    }
                    if (maxDate) {
                        that.selectDate = ''
                    }
                },
                disabledDate(time) {
                    let timeOptionRange = that.selectDate;
                    let secondNum = 60 * 60 * 24 * 6 * 1000; // 时间跨度为7天【一年以365天的时间计算】的限制
                    if (timeOptionRange) {
                        return time.getTime() > timeOptionRange + secondNum || time.getTime() < timeOptionRange - secondNum;
                    }
                }
            },
            sendOrderNoInner: "",
            dialogHisVisible: false,
            ticketCompanyName: "",
            platformType: "",
            ticketNumber: "",
            drawerVisible: false,
            payTimeSel: "",
            list: [],
            summaryarry: {},
            pager: {},
            platformlist: [],
            responsiblelist: ['采购责任', '仓库-发货组', '仓库-审单组', '公司承担', '运营责任'],
            grouplist: [],
            shopList: [],
            expresscompanylist: [],
            prosimstatelist: [],//快递站点
            exceptionTypelist: [],
            orderStatuslist: [],
            dialogVisible: false,
            total: 0,
            listLoading: false,
            pageLoading: false,
            importLoading: false,
            fileList: [],
            oneCount: "",
            twoCount: "",
            threeCount: "",
            historyCount: "",
            overCount: "",
        }
    },
        async mounted () {
            this.initTable();
            await this.initDirectorGroupList();
            await this.setPlatform();
            // await this.getExpressComanyList();
            // await this.getOrderStatusListAsync();
            await this.onSearch();
        },
        methods: {
            initTable () {
                this.getWareHouse();
                this.initTableCols();
                this.initBoxStyle();
            },
            initTableCols () {
                var showTableCols = [];
                showTableCols.push({ istrue: true, prop: 'innerOrderId', label: '内部订单号', width: '110', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogDetail(row) });
                if (this.filter.dataType == 1) {
                    showTableCols.push({ istrue: true, prop: 'payDate', label: '付款时间', width: '180', sortable: 'custom' });
                    showTableCols.push({ istrue: true, prop: 'planDeliveryDate', label: '计划发货时间', width: '180', sortable: 'custom' });
                    showTableCols.push({ istrue: true, prop: 'surplusDeliveryDate', label: '剩余发货时间(小时)', type: 'html', width: '180', formatter: (row) => this.calculateDeliveryRemainingTime(row) });
                    // showTableCols.push({ istrue: true, prop: 'deliveryDate', label: '发货时间', width: '180', sortable: 'custom' });
                    showTableCols.push({ istrue: true, prop: 'orderStatus', label: '订单状态', width: '150', sortable: 'custom', formatter: (row) => row.orderStatusDesc });
                    showTableCols.push({ istrue: true, prop: 'questionType', label: '订单异常标记', width: '180', sortable: 'custom', });
                    showTableCols.push({ istrue: true, prop: 'platformType', label: '平台', width: '120', sortable: 'custom', formatter: (row) => row.platform });
                    showTableCols.push({ istrue: true, prop: 'shopId', label: '店铺名称', width: '180', sortable: 'custom', formatter: (row) => row.shopName});
                    showTableCols.push({ istrue: true, prop: 'shopId', label: '店铺编号', width: '100', sortable: 'custom'});
                    showTableCols.push({ istrue: true, prop: 'groupId', label: '运营组', width: '120', sortable: 'custom', formatter: (row) => row.groupName});
                } else if (this.filter.dataType == 2) {
                    showTableCols.push({ istrue: true, prop: 'latestReceiveDate', label: '最晚揽收时间', width: '150'});
                    showTableCols.push({ istrue: true, prop: 'deliveryDate', label: '发货时间', width: '150', sortable: 'custom' });
                    showTableCols.push({ istrue: true, prop: 'planReceiveDate', label: '计划揽收时间', width: '150', sortable: 'custom' });
                    showTableCols.push({ istrue: true, prop: 'surplusReceiveDate', label: '剩余揽收时间(小时)', type: 'html', width: '150', formatter: (row) => this.calculateReceiveRemainingTime(row) });
                    showTableCols.push({ istrue: true, prop: 'orderStatus', label: '订单状态', width: '100', sortable: 'custom', formatter: (row) => row.orderStatusDesc });
                    showTableCols.push({ istrue: true, prop: 'questionType', label: '订单异常标记', width: '130', sortable: 'custom', });
                    showTableCols.push({ istrue: true, prop: 'deliveryWarehouseId', label: '发货仓', width: '120', sortable: 'custom', formatter: (row) => this.showDeliveryWarehouseName(row) });
                    showTableCols.push({ istrue: true, prop: 'logisticsId', label: '物流单号', width: '130', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogistics(row) });
                    showTableCols.push({ istrue: true, prop: 'platformType', label: '平台', width: '90', sortable: 'custom', formatter: (row) => row.platform });
                    showTableCols.push({ istrue: true, prop: 'shopId', label: '店铺名称', width: '150', sortable: 'custom', formatter: (row) => row.shopName});
                    showTableCols.push({ istrue: true, prop: 'shopId', label: '店铺编号', width: '100', sortable: 'custom'});
                    showTableCols.push({ istrue: true, prop: 'groupId', label: '运营组', width: '100', sortable: 'custom', formatter: (row) => row.groupName});
                } else if (this.filter.dataType == 3) {
                    showTableCols.push({ istrue: true, prop: 'planReceiveDate', label: '计划揽收时间', width: '150', sortable: 'custom' });
                    showTableCols.push({ istrue: true, prop: 'receiveDate', label: '物流日志更新时间', width: '150', sortable: 'custom' });
                    showTableCols.push({ istrue: true, prop: 'orderStatus', label: '订单状态', width: '150', sortable: 'custom', formatter: () => '已揽收' });
                    showTableCols.push({ istrue: true, prop: 'deliveryWarehouseId', label: '发货仓', width: '200', sortable: 'custom', formatter: (row) => this.showDeliveryWarehouseName(row) });
                    showTableCols.push({ istrue: true, prop: 'logisticsId', label: '物流单号', width: '150', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogistics(row) });
                    showTableCols.push({ istrue: true, prop: 'platformType', label: '平台', width: '150', sortable: 'custom', formatter: (row) => row.platform });
                    showTableCols.push({ istrue: true, prop: 'shopId', label: '店铺名称', width: '150', sortable: 'custom', formatter: (row) => row.shopName});
                    showTableCols.push({ istrue: true, prop: 'shopId', label: '店铺编号', width: '100', sortable: 'custom'});
                    showTableCols.push({ istrue: true, prop: 'groupId', label: '运营组', width: '150', sortable: 'custom', formatter: (row) => row.groupName});
                }
                this.tableCols = showTableCols;
            },
            handleInput(value) {
                var thisValue = this.filter[value];
                // 使用正则表达式替换非数字字符
                thisValue = thisValue.replace(/\D/g, '');
                // 限制长度为19
                thisValue = thisValue.slice(0, 18);
                // 更新filter对象中的值
                this.filter[value] = thisValue;
            },
            calculateDeliveryRemainingTime(row) {
                if (!row.planDeliveryDate) {
                    return null;
                 }
                const planDeliveryDate = new Date(row.planDeliveryDate); // 计划发货时间
                const currentDate = new Date(); // 当前时间
                const diffInMilliseconds = planDeliveryDate - currentDate;
                const diffInHours = (diffInMilliseconds / (1000 * 60 * 60)).toFixed(2); // 转换为小时并保留两位小数
                if (diffInHours > 0 && diffInHours <= 12) {
                    return "<span style='color:orange'>" + diffInHours + "(快超时)" + "</span>";
                } else if (diffInHours <= 0) {
                    return "<span style='color:red'>" + diffInHours + "(已超时)" + "</span>";

                } else {
                    return diffInHours;
                }
            },
            calculateReceiveRemainingTime(row) {
                if (!row.planReceiveDate || row.receiveDate) {
                    return null;
                 }
                const planReceiveDate = new Date(row.planReceiveDate); // 计划发货时间
                const currentDate = new Date(); // 当前时间
                const diffInMilliseconds = planReceiveDate - currentDate;
                const diffInHours = (diffInMilliseconds / (1000 * 60 * 60)).toFixed(2); // 转换为小时并保留两位小数
                if (diffInHours > 0 && diffInHours <= 12) {
                    return "<span style='color:orange'>" + diffInHours + "(快超时)" + "</span>";
                } else if (diffInHours <= 0) {
                    return "<span style='color:red'>" + diffInHours + "(已超时)" + "</span>";

                } else {
                    return diffInHours;
                }
            },
            showDeliveryWarehouseName(row) {
                if (row.deliveryWarehouseId) {
                    const wareHouse = this.warehouselist.find(x => x.wms_co_id == row.deliveryWarehouseId)
                    if (wareHouse) {
                        return wareHouse.name
                    }
                }
                return null;
            },
            initBoxStyle () {
                if (this.filter.dataType == 1) {
                    this.boxStyleColor = {
                        "--color": "rgb(61, 127, 255)",
                        "--shadow": "rgb(61 127 255 / 37%) 0px 0px 12px",
                        "--bgColor": "linear-gradient(rgb(235, 246, 255) 0%, rgb(213, 230, 255) 100%)"
                    };
                } else if (this.filter.dataType == 2) {
                    this.boxStyleColor = {
                        "--color": "rgb(255, 122, 0)",
                        "--shadow": "rgb(227 144 66 / 20%) 0px 4px 12px",
                        "--bgColor": "linear-gradient(rgb(252, 243, 235) 0%, rgb(255, 237, 219) 100%)"
                    };

                } else if (this.filter.dataType == 3) {
                    this.boxStyleColor = {
                        "--color": "rgb(7, 234, 43)",
                        "--shadow": "rgb(134 223 108 / 38%) 0px 0px 12px",
                        "--bgColor": "linear-gradient(rgb(242, 255, 244) 0%, rgb(219, 253, 238) 100%)"
                    };
                }
            },
            async getWareHouse() {
                const res = await getAllWarehouse()
                if (!res?.success) {
                    return;
                }
                this.warehouselist = res.data
            },
            showLogDetail (row) {
                this.dialogHisVisible = true;
                this.sendOrderNoInner = row.innerOrderId;
            },
            showLogistics (row) {
                this.drawerVisible = true;
                this.ticketCompanyName = row.logisticsCompany;
                this.platformType = row.platformType;
                this.ticketNumber = row.logisticsId;
            },
            async setInputNumRangeValue (event, type) {
                if (this.listLoading == false) {
                    //把div的属性重置为初始状态
                    var allDive = event.currentTarget.parentElement.children;
                    for (var i = 0; i < allDive.length; i++) {
                        allDive[i].className = "transition-box";
                        allDive[i].lastElementChild.className = "transition-sum";
                    }
                    //如果是点击历史数据
                    if (this.filter.overTimeType == type) {
                        this.filter.overTimeType = null;
                    } else {
                        this.filter.overTimeType = type;
                        event.currentTarget.className = "transition-boxActive";
                        event.currentTarget.lastElementChild.className = "transition-sumActive";
                    }
                    await this.onSearch();
                }
            },
            async initDirectorGroupList () {
                var res2 = await getDirectorGroupList();
                this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
            },
            //获取订单状态
            async getOrderStatusListAsync () {
                const res = await getOrderStatusListAsync({});
                if (!res?.success) {
                    return;
                }
                const data = res.data;
                this.orderStatuslist = data;
            },
            //时间设置
            payTimeSelChangePick () {
                if (this.filter.paytimerange && this.filter.paytimerange.length > 1) {
                    if (this.filter.paytimerange[1] != formatTime(new Date(), "YYYY-MM-DD")) {
                        this.payTimeSel = "";
                        return;
                    }
                    var d1 = dayjs(this.filter.paytimerange[0]);
                    var d2 = dayjs(this.filter.paytimerange[1]);
                    switch (d2.diff(d1, "day")) {
                        //昨天至今日
                        case 1:
                            this.payTimeSel = "1";
                            break;
                        //近7天
                        case 6:
                            this.payTimeSel = "2";
                            break;
                        //近14天
                        case 13:
                            this.payTimeSel = "3";
                            break;
                        //近30天
                        case 29:
                            this.payTimeSel = "4";
                            break;
                        //默认
                        default:
                            this.payTimeSel = "";
                            break;
                    }
                }
                else {

                    this.payTimeSel = "";
                }

            },
            //付款时间设置默认值
            payTimeSelChange () {
                let oneDayTime = 24 * 60 * 60 * 1000;
                switch (this.payTimeSel) {
                    //昨天至今日
                    case "1":
                        this.filter.paytimerange = [
                            formatTime(new Date(new Date().getTime() - 1 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //近7天
                    case "2":
                        this.filter.paytimerange = [
                            formatTime(new Date(new Date().getTime() - 6 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //近14天
                    case "3":
                        this.filter.paytimerange = [
                            formatTime(new Date(new Date().getTime() - 13 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //近30天
                    case "4":
                        this.filter.paytimerange = [
                            formatTime(new Date(new Date().getTime() - 29 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //默认
                    default:
                        this.filter.paytimerange = [];
                        break;

                }
            },
            //获取物流
            async getExpressComanyList () {
                const res = await getExpressComanyAll({});
                if (!res?.success) {
                    return;
                }
                const data = res.data;
                this.expresscompanylist = data;
            },
            //获取物流站点
            async getprosimstatelist (val) {
                var res = await getExpressComanyStationName({ id: val });
                this.filter.ticketCompanyStation = '';
                if (res?.code) {
                    this.prosimstatelist = res.data
                }
            },
            //设置平台下拉
            async setPlatform () {
                var pfrule = await rulePlatform();
                this.platformlist = pfrule.options;
            },
            //下载导入模板
            downloadTemplate () {
                window.open("../../static/excel/order/" + this.exportName + ".xlsx", "_self");
            },
            //开始导入
            startImport () {
                this.dialogVisible = true;
            },
            //取消导入
            cancelImport () {
                this.dialogVisible = false;
            },
            //上传成功
            uploadSuccess (response, file, fileList) {
                this.fileList.splice(this.fileList.indexOf(file), 1);
                this.dialogVisible = false;
                this.fileList = [];
            },
            //提交导入
            submitUpload () {
                this.$refs.upload.submit();
            },
            //上传文件
            async uploadFile (item) {
                const form = new FormData();
                form.append("token", this.token);
                form.append("upfile", item.file);
                form.append("dataType", this.filter.dataType);
                this.importLoading = true;
                const res = await importLogisticsEarlyWarAsync(form);
                this.importLoading = false;
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
            },
            //导出
            async onExport() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                const res = await logisticsWarningExport(params);
                console.log("5555", res)
                if (res?.data.success) {
                    this.$message({ message: '下载任务已创建，稍后点击头像-下载管理，进行下载！', type: "success" });
                }
            },
            //获取查询条件
            getCondition () {
                if (this.filter.paytimerange && this.filter.paytimerange.length > 1) {
                    this.filter.payDateStart = this.filter.paytimerange[0];
                    this.filter.payDateEnd = this.filter.paytimerange[1];
                } else {
                    this.filter.payDateStart = null;
                    this.filter.payDateEnd = null;
                }
                if (this.filter.deltimerange && this.filter.deltimerange.length > 1) {
                    this.filter.deliveryDateStart = this.filter.deltimerange[0];
                    this.filter.deliveryDateEnd = this.filter.deltimerange[1];
                } else {
                    this.filter.deliveryDateStart = null;
                    this.filter.deliveryDateEnd = null;
                }
                if (this.filter.rectimerange && this.filter.rectimerange.length > 1) {
                    this.filter.receiveDateStart = this.filter.rectimerange[0];
                    this.filter.receiveDateEnd = this.filter.rectimerange[1];
                } else {
                    this.filter.receiveDateStart = null;
                    this.filter.receiveDateEnd = null;
                }
                if (this.filter.sendMsgTimerange && this.filter.sendMsgTimerange.length > 1) {
                    this.filter.startSendMsgTime = this.filter.sendMsgTimerange[0];
                    this.filter.endSendMsgTime = this.filter.sendMsgTimerange[1];
                } else {
                    this.filter.startSendMsgTime = null;
                    this.filter.endSendMsgTime = null;
                }
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }
                return params;
            },
            //查询第一页
            async onSearch () {
                this.$refs.pager.setPage(1)
                await this.getlist()
            },
            //批量数据组合，回调数据
            async callback(value, field) {
                this.filter[field] = value;
                this.$refs.pager.setPage(1)
                await this.getlist()
            },
            //分页查询
            async getlist () {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true
                const res = await logisticsWarningPage(params)
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summary;
                if ([1, 2].includes(this.filter.dataType)) {
                    this.oneCount = res.data.summary.oneCount;
                    this.twoCount = res.data.summary.twoCount;
                    this.threeCount = res.data.summary.threeCount;
                    this.overCount = res.data.summary.overCount;
                    this.historyCount = res.data.summary.historyCount;
                }
                this.list = data
            },
            //排序查询
            async sortchange (column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderBy = column.prop == "shopName" ? "shopCode" : column.prop;
                    this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            }
        }
    }
</script>
<style scoped>
    ::v-deep .el-link.el-link--primary {
        margin-right: 7px;
    }
    ::v-deep #app .el-container .is-vertical .main .el-main .el-tabs--top {
        height: 95% !important;
        background-color: red;
    }
    .transition-title {
        margin-top: 30px;
        color: black;
        font-weight: 800;
    }
    .transition-box {
        margin-left: 100px;
        width: 200px;
        height: 100px;
        border-radius: 20px;
        border: 1px solid var(--color);
        text-align: center;
        color: #fff;
        cursor: pointer;
        box-sizing: border-box;
    }
    .transition-sum {
        margin-top: 10px;
        color: black;
        font-weight: 800;
    }
    .transition-box:hover {
        margin-left: 100px;
        width: 200px;
        height: 100px;
        border-radius: 20px;
        border: 1px solid var(--color);
        text-align: center;
        color: #fff;
        cursor: pointer;
        box-shadow: var(--shadow);
        transform: scale(1.04);
        box-sizing: border-box;
    }
    .transition-boxActive {
        margin-left: 100px;
        width: 200px;
        height: 100px;
        border-radius: 20px;
        border: 1px solid var(--color);
        text-align: center;
        color: #fff;
        cursor: pointer;
        box-shadow: var(--shadow);
        background: var(--bgColor);
        transform: scale(1.04);
        box-sizing: border-box;
    }
    .transition-sumActive {
        margin-top: 10px;
        color: var(--color);
        font-weight: 800;
    }
    .titleRatio {
        border-radius: 20px;
        border: 1px solid var(--color);
        text-align: center;
        cursor: pointer;
        color: red;
        background: #fdf6ec;
        transform: scale(1.04);
        box-sizing: border-box;
        -moz-user-select: none;
        -ms-user-select: none;
        -webkit-user-select: none;
        user-select: none;
    }
    ::v-deep .el-main.mycontainer {
        overflow: hidden;
    }
</style>
