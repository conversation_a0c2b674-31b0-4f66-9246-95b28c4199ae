<template>
    <div class="ces-main">
  
   <el-row>
          <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
            <el-upload
              ref="upload"
              class="upload-demo"
              :auto-upload="true"
              :multiple="true"
              :limit="2"
              action
              accept=".rar,.zip,.ppt,.xls,.xlsx,.pptx,.doc,.docx,.png,.jpg"
              :http-request="upfile"
              :on-change="uploadchange"
              :file-list="fileList"
              :data="fileparm"
              :disabled="taskStatus === 3"
            >
              <template >
                <div style="display: flex; flex-direction: row;">
                  <el-tooltip class="item" effect="dark" content="常规文件上传" placement="top">
                    <el-button  :disabled="taskStatus === 3" size="small" type="primary" 
                    >上传资料</el-button
                  > 
                  </el-tooltip>
                 

                <el-tooltip style="margin-right: auto; margin-left: 0;"  class="item" effect="dark" content="可直接截图上传图片" placement="top">
                  <el-button   :disabled="taskStatus === 3" size="small" type="primary"  @click.stop="removedemo"
                  >上传图片资料</el-button
                > 
                </el-tooltip>
                </div>
                  

               <div class="el-alert--error is-light" style="width: 28vw; text-align: left; overflow: hidden; display: flex; flex-wrap: wrap;">温馨提示：可上传图片、.xlsx、.docx的文件，点击 通知发起人按钮，会钉钉通知发起人查看资料,每次最多只能上传两个文件</div>
              </template>
            </el-upload>

           
          </el-col>

          
          <!-- <yh-quill-editor :value.sync="form.applyContent" v-if="formEditMode"></yh-quill-editor> -->
          <!-- <el-button size="small" type="warning" @click="UploadSuccessMessage" style="margin-right: 60px;"
                  >通知发起人</el-button> -->
        </el-row>
  
  
   <el-table :data="data">
        <el-table-column type="index" width="40"></el-table-column>
         
        <el-table-column
          prop="fileName"
          sortable
          label="资料名称"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="createdTime"
          sortable
          label="上传时间"
          width="220"
        ></el-table-column
      >
      <el-table-column prop="xiaz" label="下载资料" min-width="150" align="center" >
        <template  #default="{ row }" >
          <div style="display: flex;flex-direction: row;">
            <el-button   @click="downloadFile(row.filePath,row.fileName)">下载</el-button>
           <el-button :disabled="taskStatus === 3"  @click="deleteresource(row)">删除</el-button>
          </div>
           <!-- <a style="color: red;" target="_blank" :href="row.filePath">下载</a> -->
           
            
  　　　</template> 
  
   
  </el-table-column>
       
      
      </el-table>
      <div class="block">
        <el-pagination
          layout="prev, pager, next"
          @current-change="changehisPage"
          :total="pager.total"
          :page-size="pager.pageSize"
          
        >
        </el-pagination>
      </div>
      
      <el-dialog title="截图上传图片" :visible.sync="UploadImageDialog" width="80%"   append-to-body>
        <div ref="jietu">
          <yh-quill-editor :value.sync="imageFath" @imgget="imggetfuc" key="jietu"></yh-quill-editor>
        </div>
         
       
        <span slot="footer" class="dialog-footer">
            <el-button @click="closefuc">关闭</el-button>
        </span>
    </el-dialog>
    </div>
  </template>
   
          
      
  <script>
  import { upLoadFile, upLoadImage } from "@/api/upload/file";
  import {
    trainfilesUpload,
    getTrainResourcegetlistfileDataList,
    disableTrainResourceData
  } from "@/api/customerservice/trainplan";
  import { uploadCommonFile } from "@/api/upload/filenew";

  import { productReportfilesUpload,getProductReportfiles,deleteProductReportfiles,sendMessageToCreateUserName} from '@/api/operatemanage/base/product';
  import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue';

  export default {
    name: "UploadNeedDatas",
    components: { YhQuillEditor},
    data() {
      return {
        that: this,
        imageFath:null,
        loading: false,
        proCode: "",
        createUserName:"",
        appointUserName:"",
        taskStatus:null,
        title:"",
        total: 0,
        pager: {
          total: 0,
          currentPage:1,
          pageSize: 5,
          OrderBy: "",
          isAsc: false,
          Trainstatus: 2,
        },
        UploadImageDialog: false,
        data: [],
        fileList: [],
        fileparm: {},
      };
    },
    computed: {},
    methods: {
 
      async downloadFile(url, fileName) {
        debugger
      if (url == "" || url == null) {
        this.$message({ message: '无效链接', type: "warning" });
        return;
      }
      var xhr = new XMLHttpRequest();
      xhr.open('GET', url, true);
      xhr.responseType = 'arraybuffer'; // 返回类型blob
      xhr.onload = function () {
        debugger
        if (xhr.readyState === 4 && xhr.status === 200) {
          let blob = this.response;
          console.log(blob);
          // 转换一个blob链接
          // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，
          // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定
          let downLoadUrl = window.URL.createObjectURL(new Blob([blob], { type: 'video/mp4' }));
          // 视频的type是video/mp4，图片是image/jpeg
          // 01.创建a标签
          let a = document.createElement('a');
          // 02.给a标签的属性download设定名称
          a.download = fileName;
          // 03.设置下载的文件名
          a.href = downLoadUrl;
          // 04.对a标签做一个隐藏处理
          a.style.display = 'none';
          // 05.向文档中添加a标签
          document.body.appendChild(a);
          // 06.启动点击事件
          a.click();
          // 07.下载完毕删除此标签
          a.remove();
        } else {

          this.$message({ message: '无效链接', type: "warning" });
        }
      };
      xhr.send();
    },

      closefuc(){
        let c = this.$refs.jietu.getElementsByClassName('ql-editor');
          c[0].innerHTML = ""
          this.UploadImageDialog = false
      },
      removedemo(){
        this.UploadImageDialog=true;
        this.$nextTick(()=>{
          // let a = document.getElementsByClassName("jietu")[0];
          
          // // let a = document.getElementsByClassName("jietu quill-editor")[0];
          let a = this.$refs.jietu.children[0];
          let b = document.getElementsByClassName("ql-toolbar")[0];
          a.removeChild(b)
        })
      },
      imggetfuc(res){
        var that=this;
        var item = { url: res.data.url, name: res.data.fileName,ProCode:that.proCode };
       const resdata =productReportfilesUpload(item);

       this.$message.success('上传成功！');
       this.getlistfile();
    },
  

      UploadSuccessMessage(){
        const params = {
          proCode:this.proCode,
          appointUserName:this.appointUserName,
          createUserName:this.createUserName,
          title:this.title,
        };
        const res= sendMessageToCreateUserName(params);
                this.$message.success('已通知发起人');
           




      },
      changehisPage(e){
        var that=this;
        this.pager.currentPage=e;
    const params = {
          ...this.pager,
        };
        params.proCode = that.proCode;
  
  
  
            that.data = [];
         
            getProductReportfiles(params).then(res=>{
          
          
          that.data = res.data.list;
          that.pager.total=res.data.total;
          that.total=res.data.total;
  
  
  
         });
  
  
      },
      deleteresource(e){
        console.log(e)
        var that=this;
        deleteProductReportfiles({id:e.id}).then(res=>{
          
          that.init(e.proCode)
  
  
  
        });
  
      },
      uploadchange(e) {
        
  
        console.log("uploadchange");
      },
  
      submitUpload() {
        console.log("upload");
      },
      async upfile(parms) {
        var item;
        var that=this;
  
  console.log("upload finish");
  
  
        const form = new FormData();
        form.append("file", parms.file);
        const res = await uploadCommonFile(form);
        console.log("上传文件",res.data)
        console.log("上传文件的路径",res.data.url)
        if (res.code == 1)
          item = { url: res.data.url, name: parms.file.name,ProCode:that.proCode };
        else throw new Error(res.msg);
        console.log("我是子组件的上传的资料的商品ID",that.proCode)
  
        productReportfilesUpload(item).then((res) => {
  
  
          that.fileList=[];
          that.init(that.proCode);
  
        this.$message({
            message: "上传成功.....",
            type: "success",
          });
        }
        );
  
        return item;
      },
  
      uploadchange() {
      },
      changePage(e) {
        var that = this;
        that.data = [];
  
        const params = {
          ...this.pager,
        };
        params.proCode = that.proCode;
        this.pager.currentPage  = e;
      },
      async init(_proCode,createUserName,appointUserName,title,taskStatus) {
        var that = this;
        this.proCode=_proCode;
//         console.log("111",this.proCode)
// debugger
        this.createUserName=createUserName;
        this.appointUserName=appointUserName;
        this.title=title;
        this.taskStatus=taskStatus;
        console.log("我是传过来的任务状态",this.taskStatus)
       
         this.getlistfile();
    
      },
      async getlistfile(){
        var that = this;
        this.pager.currentPage  = 0;
        that.data = [];
        const params = {
          ...this.pager,
        };
        params.proCode = this.proCode;
            that.data = [];
           await getProductReportfiles(params).then(res=>{
          
          
          that.data = res.data.list;
          that.pager.total=res.data.total;
          that.total=res.data.total;
  
  
  
         });


      }
    },
    async mounted() {
    },
  };
  </script>