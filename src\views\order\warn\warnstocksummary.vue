<template>
    <ces-table ref="table" :that='that' :isIndex='true' :isSelectColumn='true' @sortchange='sortchange' :tableData='list' 
                         tablekey='warnstocksummary' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
        <template slot='extentbtn'>
        <el-button-group>
          <el-button type="primary" @click="onSearch">刷新</el-button>
      </el-button-group>
      </template>
    </ces-table>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container/nofooter";
import { queryOrderSaleOutStockSum} from "@/api/order/ordererror";
const tableCols =[
     {istrue:true,prop:'checkNum',label:'审单订单数', width:'120',sortable:'custom',type:'click',handle:(that,row)=>that.showwarnstock(0)},
     {istrue:true,prop:'urgentNum',label:'加急订单数', width:'120',sortable:'custom',type:'click',handle:(that,row)=>that.showwarnstock(1)},
     {istrue:true,prop:'distributionNum',label:'配货订单数', width:'120',sortable:'custom',type:'click',handle:(that,row)=>that.showwarnstock(2)},
     {istrue:true,prop:'packNum',label:'打包订单数', width:'120',sortable:'custom',type:'click',handle:(that,row)=>that.showwarnstock(3)},
    ];
    const tableHandles1=[ ];
export default {
  name: "warnstocksummary",
  components: {cesTable, container},
  props:{
    filter: {type:Object,default:()=>{}},
  },
  data() {
    return {
      that:this,
      filter1: {groupId: ""},
      list: [],
      listLoading: false,
      pageLoading: false,
      grouplist:[], 
      tableCols:tableCols,
      tableHandles:tableHandles1,
      pager:{OrderBy:" groupId ",IsAsc:false},
    };
  },
  async mounted() {
    await this.onSearch();
  },
  methods: {
    sortchange(column){
      if(!column.order)
        this.pager={OrderBy:"groupId",IsAsc:false}
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    onSearch() {
      this.getList();
    },
    async getList() {
      const params = {...this.filter,...this.pager};
      this.listLoading = true;
      const res = await queryOrderSaleOutStockSum(params);
      this.listLoading = false;
      if (!res?.success) return; 
      const data = res.data;
      data.forEach((d) => {d._loading = false;});
      this.list = data;
    },
    async showwarnstock(status){
       await this.$emit('showwarnstock',status);
    }
  },
};
</script>
 
