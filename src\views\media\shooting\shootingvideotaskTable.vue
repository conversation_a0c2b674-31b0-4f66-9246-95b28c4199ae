<template>
    <!-- 新品共用表 -->
    <div style="height: 100%;">
        <vxe-toolbar ref="xToolbar" custom class="vxetoolbar20221212" v-if="showToolbar">
            <template #buttons>
                <slot name="tbHeader" />
            </template>
        </vxe-toolbar>
        <vxe-table ref="xTable" border="default" :show-footer="true" style="width: 100%;"
            class="vxetable202212161323 mytable-scrollbar20221212" resizable stripe :id="id" show-overflow
            show-footer-overflow keep-source size="mini" :height="height" :loading="loading" :data="tableData"
            :scroll-y="{ gt: 100, enabled: true  }" :scroll-x="{ gt: 100, enabled: true }" :footer-method="footerMethod" @cell-dblclick="rowChange"
            @footer-cell-click="footercellclick" @checkbox-change="selectChangeEvent"  @checkbox-all="selectChangeEvent"
            :sort-config="{ sortMethod: customSortMethod }"   :row-class-name="rowStyleFun"
            :row-config="{ isCurrent: true, isHover: true }">
            <vxe-column field="checkboxlie" type="checkbox" width="35" fixed="left"></vxe-column>
            <vxe-column v-if="checkPermission('shootTaskId')" field="shootingTaskId" title="编号" width='50' fixed='left'>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootProudctName')" field="productShortName" title="产品简称" width='128'
                fixed='left'>
                <template #default="{ row }">
                    <a type="text" show-overflow="ellipsis" style="color:#409EFF" @click="openComputOutInfo(row)"> {{
                        row.productShortName }} </a>
                </template>
            </vxe-column>
            <!-- 标记 -->
            <vxe-column title="" width='28' fixed='left'>
                <template #default="{ row }">
                    <template v-if="row.isTopOldNum == '0'">
                        <span></span>
                    </template>
                    <template v-else>
                        <i class="vxe-icon-dot" style="color: #f56c6c;"></i>
                    </template>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootUrgency')" field="taskUrgencyName" title="紧急程度" width='72' fixed='left'>
                <template #default="{ row }">
                    <template v-if="row.taskUrgency == 1">
                        <vxe-button status="danger" size="mini"
                            @click="shootUrgencyCilck(row.taskUrgencyName, row.shootingTaskId)">{{ row.taskUrgencyName }}</vxe-button>
                    </template>
                    <template v-else-if="(row.taskUrgency == 2)">
                        <vxe-button status="primary" size="mini"
                            @click="shootUrgencyCilck(row.taskUrgencyName, row.shootingTaskId)">{{ row.taskUrgencyName }}</vxe-button>
                    </template>
                    <template v-else-if="(row.taskUrgency == 0)">
                        <vxe-button status="warning" size="mini"
                            @click="shootUrgencyCilck(row.taskUrgencyName, row.shootingTaskId)">{{ row.taskUrgencyName }}</vxe-button>
                    </template>
                    <template v-else>
                        <vxe-button size="mini"
                            @click="shootUrgencyCilck(row.taskUrgencyName, row.shootingTaskId)">{{ row.taskUrgencyName }}</vxe-button>
                    </template>
                </template>
            </vxe-column>
            <vxe-column field="degreeUpTimeStr" title="修改时间" width='78'   fixed='left'></vxe-column>
            <vxe-column v-if="checkPermission('shootWarehouse')" field="warehouseStr" title="大货仓" width='118'
                fixed='left'></vxe-column>
            <!-- 备注 -->
            <!-- <vxe-column v-if="checkPermission('shootRemarks')" field="beizhu" title=" " width='28' fixed='left'>
                <template #default="{ row }">
                    <template v-if="row.markCssName == '0'">
                        <i class="vxe-icon-flag-fill" style="color: #F56C6C;" @click="openTaskRmarkInfo(row)"></i>
                    </template>
                    <template v-else>
                        <i class="vxe-icon-flag-fill" style="color: #dcdfe6;" @click="openTaskRmarkInfo(row)"></i>
                    </template>
                </template>
            </vxe-column> -->

            <!-- 备注 -->
            <vxe-column field="beizhu" title="" width='28' fixed='left' v-if="checkPermission('shootRemarks')">
                <template #default="{ row }">
                    <el-popover style="overflow-y: hidden;" v-if="row.remarkList.length > 0" placement="right" trigger="hover"
                    width="500">
                    <el-card class="box-card">
                        <div style="display: flex; flex-direction: column; max-height: 250px; min-height: 60px; overflow-y: auto;">
                        <div v-for="(item, i) in row.remarkList" :key="i" style="border: 1px solid #fff;">
                            <span style="font-weight: 600;">{{ i + 1 }}</span>、{{ item }}
                        </div>
                        </div>

                    </el-card>
                    <i class="vxe-icon-flag-fill"  style="font-size: 14px;color: #F56C6C;" slot="reference"
                        @click="openTaskRmarkInfo(row)"></i>
                    </el-popover>

                    <i v-else class="vxe-icon-flag-fill" style="font-size: 15px;color: #dcdfe6;" slot="reference"
                    @click="openTaskRmarkInfo(row)"></i>

                </template>
            </vxe-column>
            <!-- 参考 -->
            <vxe-column v-if="checkPermission('shootReference')" field="cankao" title="" width='28' fixed='left'>
                <template #default="{ row }">
                    <i class="vxe-icon-file-txt" @click="videotaskuploadfileDetal(row)"></i>
                </template>
            </vxe-column>
            <!-- 操作 -->
            <vxe-column v-if="checkPermission('api:media:shootingvideo:AddOrUpdateShootingVideoTaskAsync')"
                field="caozoulie" title="" width='38' fixed='left'>
                <template #default="{ row }">
                    <i class="vxe-icon-ellipsis-h" @click="editTask(row)"></i>
                </template>
            </vxe-column>
            <vxe-column field="" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column v-if="checkPermission('shootPhoto')" field="photoLqNameStr"  title="照片" width='65'>
                <template #default="{ row }">
                    <span :style="row.photoIsReject?'color:#999':'color: #606266'"> {{row.photoLqNameStr}} </span>
                </template>
            </vxe-column>
            <!-- 照片 -->
            <vxe-column v-if="checkPermission('shootCompleteDate')" field="photoOverTimeStr" title="完成日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.photoIsReject?'color:#999':'color: #606266'"> {{row.photoOverTimeStr}} </span>
                </template>
            </vxe-column>
            <vxe-column  field="addTaskTimeStr1" title="修改时间" width='70'></vxe-column>
            <vxe-column v-if="checkPermission('shootDays')" field="photoDaysStr" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.showRed1 ? 'color: red' : (row.photoIsReject?'color:#999':'color: #606266')"> {{row.photoDaysStr}} </span>
                </template>
            </vxe-column>
            <vxe-column   field="bhDate1" title="驳回日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.photoIsReject || row.isConfirmBh1 ?'color:#999':'color: #606266'"> {{row.bhDate1}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootDays')" field="bcDate1" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.photoIsReject?'color:#999':'color: #606266'"> {{row.bcDate1}} </span>
                </template>
            </vxe-column>
            <vxe-column   v-if="checkPermission('sylbxsbg')"  field="makeUpDatetime1" title="补改日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.photoIsReject?'color:#999':'color: #606266'"> {{row.makeUpDatetime1}} </span>
                </template>
            </vxe-column>

            <vxe-column  v-if="checkPermission('sylbxsbg')"  field="makeUpDay1" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.photoIsReject?'color:#999':'color: #606266'"> {{row.makeUpDay1}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootConfirmationPersion')" field="photoConfirmNameStr" title="确认人"
                width='65'>
                <template #default="{ row }">
                    <span :style="row.photoIsReject ?'color:#999':'color: #606266'"> {{row.photoConfirmNameStr}} </span>
                </template> </vxe-column>
            <vxe-column v-if="checkPermission('shootConfirmationDate')" field="photoConfirmTimeStr" title="确认日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.photoIsReject ?'color:#999':'color: #606266'"> {{row.photoConfirmTimeStr}} </span>
                </template>
            </vxe-column>
            <vxe-column field="scoreType1" title="质量评分" width='70' v-if="checkPermission('alllistsRatinges')">
                <template #default="{ row }">
                    <span :style="row.photoIsReject?'color:#999':(row.isDefaultScoreType1?'color:#999':'color: #606266')"> {{( row.photoLqNameStr==null ||row.photoLqNameStr=='')?"":row.scoreType1}} </span>
                </template>
            </vxe-column>
            <vxe-column field="scoreType11" title="配合度评分" width='70' v-if="checkPermission('alllistsRatinges')">
                <template #default="{ row }">
                    <span :style="row.photoIsReject?'color:#999':(row.isDefaultScoreType11?'color:#999':'color: #606266')"> {{( row.photoLqNameStr==null ||row.photoLqNameStr=='')?"":row.scoreType11}} </span>
                </template>
            </vxe-column>
            <vxe-column field="scoreTypeAvg1" title="平均分" width='50' v-if="checkPermission('alllistsRatinges')">
                <template #default="{ row }">
                    <span :style="row.photoIsReject?'color:#999':(row.isDefaultScoreType11?'color:#999':'color: #606266')"> {{( row.photoLqNameStr==null ||row.photoLqNameStr=='')?"":row.scoreTypeAvg1}} </span>
                </template>
            </vxe-column>
            <vxe-column field="Divisionline2" title="|" width='28'> <span style="color: #999;">|</span> </vxe-column>
            <vxe-column v-if="checkPermission('shootVideo')" field="vedioLqNameStr" title="视频" width='65'>
                <template #default="{ row }">
                    <span :style="row.vedioIsReject?'color:#999':'color: #606266'"> {{row.vedioLqNameStr}} </span>
                </template>
            </vxe-column>
            <!-- 视频 -->
            <vxe-column v-if="checkPermission('shootCompleteDate')" field="vedioOverTimeStr" title="完成日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.vedioIsReject ?'color:#999':'color: #606266'"> {{row.vedioOverTimeStr}} </span>
                </template>
            </vxe-column>
            <vxe-column  field="addTaskTimeStr2" title="修改时间" width='70'></vxe-column>
            <vxe-column v-if="checkPermission('shootDays')" field="vedioDaysStr" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.showRed2 ? 'color: red' : (row.vedioIsReject ?'color:#999':'color: #606266')"> {{row.vedioDaysStr}} </span>
                </template> </vxe-column>
            <vxe-column  field="bhDate2" title="驳回日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.vedioIsReject || row.isConfirmBh2 ?'color:#999':'color: #606266'"> {{row.bhDate2}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootDays')" field="bcDate2" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.vedioIsReject ?'color:#999':'color: #606266'"> {{row.bcDate2}} </span>
                </template> </vxe-column>
            <vxe-column  v-if="checkPermission('sylbxsbg')"  field="makeUpDatetime2" title="补改日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.vedioIsReject?'color:#999':'color: #606266'"> {{row.makeUpDatetime2}} </span>
                </template>
            </vxe-column>

            <vxe-column  v-if="checkPermission('sylbxsbg')"  field="makeUpDay2" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.vedioIsReject?'color:#999':'color: #606266'"> {{row.makeUpDay2}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootConfirmationPersion')" field="vedioConfirmNameStr" title="确认人"
                width='65'>
                <template #default="{ row }">
                    <span :style="row.vedioIsReject ?'color:#999':'color: #606266'"> {{row.vedioConfirmNameStr}} </span>
                </template> </vxe-column>
            <vxe-column v-if="checkPermission('shootConfirmationDate')" field="vedioConfirmTimeStr" title="确认日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.vedioIsReject ?'color:#999':'color: #606266'"> {{row.vedioConfirmTimeStr}} </span>
                </template>
            </vxe-column>
            <vxe-column field="scoreType2" title="质量评分" width='70' v-if="checkPermission('alllistsRatinges')">
                <template #default="{ row }">
                    <span :style="row.vedioIsReject?'color:#999':(row.isDefaultScoreType2?'color:#999':'color: #606266')"> {{( row.vedioLqNameStr==null || row.vedioLqNameStr=='')?"":row.scoreType2}} </span>
                </template>
            </vxe-column>
            <vxe-column field="scoreType12" title="配合度评分" width='70' v-if="checkPermission('alllistsRatinges')">
              <template #default="{ row }">
                  <span :style="row.vedioIsReject?'color:#999':(row.isDefaultScoreType12?'color:#999':'color: #606266')"> {{( row.vedioLqNameStr==null ||row.vedioLqNameStr=='')?"":row.scoreType12}} </span>
              </template>
            </vxe-column>
            <vxe-column field="scoreTypeAvg2" title="平均分" width='50' v-if="checkPermission('alllistsRatinges')">
              <template #default="{ row }">
                  <span :style="row.vedioIsReject?'color:#999':(row.isDefaultScoreType12?'color:#999':'color: #606266')"> {{( row.vedioLqNameStr==null ||row.vedioLqNameStr=='')?"":row.scoreTypeAvg2}} </span>
              </template>
            </vxe-column>
            <vxe-column field="Divisionline2" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column v-if="checkPermission('shootMicrodetails')" field="microDetailLqNameStr" title="微详情" width='65'>
                <template #default="{ row }">
                    <span :style="row.microDetailsIsReject?'color:#999':'color: #606266'"> {{row.microDetailLqNameStr}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootQuantity')" field="microDetailVedioCounts" title="数量" width='54'>
                <template #default="{ row }">
                    <span :style="row.microDetailsIsReject?'color:#999':'color: #606266'"> {{row.microDetailVedioCounts}} </span>
                </template>
            </vxe-column>
            <!-- 数量 -->
            <vxe-column v-if="checkPermission('shootCompleteDate')" field="microDetailOverTimeStr" title="完成日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.microDetailsIsReject?'color:#999':'color: #606266'"> {{row.microDetailOverTimeStr}} </span>
                </template>
            </vxe-column>
            <vxe-column  field="addTaskTimeStr3" title="修改时间" width='70'></vxe-column>
            <vxe-column v-if="checkPermission('shootDays')" field="microDetailDaysStr" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.showRed3 ? 'color: red' : (row.microDetailsIsReject?'color:#999':'color: #606266')"> {{row.microDetailDaysStr}} </span>
                </template>
            </vxe-column>
            <vxe-column   field="bhDate3" title="驳回日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.microDetailsIsReject || row.isConfirmBh3 ?'color:#999':'color: #606266'"> {{row.bhDate3}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootDays')" field="bcDate3" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.microDetailsIsReject?'color:#999':'color: #606266'"> {{row.bcDate3}} </span>
                </template>
            </vxe-column>
            <vxe-column  v-if="checkPermission('sylbxsbg')" field="makeUpDatetime3" title="补改日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.microDetailsIsReject?'color:#999':'color: #606266'"> {{row.makeUpDatetime3}} </span>
                </template>
            </vxe-column>

            <vxe-column  v-if="checkPermission('sylbxsbg')" field="makeUpDay3" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.microDetailsIsReject?'color:#999':'color: #606266'"> {{row.makeUpDay3}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootConfirmationPersion')" field="microDetailConfirmNameStr" title="确认人"
                width='65'>
                <template #default="{ row }">
                    <span :style="row.microDetailsIsReject?'color:#999':'color: #606266'"> {{row.microDetailConfirmNameStr}} </span>
                </template>
                </vxe-column>
            <vxe-column v-if="checkPermission('shootConfirmationDate')" field="microDetailConfirmTimeStr" title="确认日期"
                width='70'>
                <template #default="{ row }">
                    <span :style="row.microDetailsIsReject?'color:#999':'color: #606266'"> {{row.microDetailConfirmTimeStr}} </span>
                </template>
                </vxe-column>
                <vxe-column field="scoreType3" title="质量评分" width='70' v-if="checkPermission('alllistsRatinges')">
                <template #default="{ row }">
                    <span :style="row.microDetailsIsReject?'color:#999':(row.isDefaultScoreType3?'color:#999':'color: #606266')"> {{( row.microDetailLqNameStr==null ||row.microDetailLqNameStr=='')?"":row.scoreType3}} </span>
                </template>
            </vxe-column>
            <vxe-column field="scoreType13" title="配合度评分" width='70' v-if="checkPermission('alllistsRatinges')">
              <template #default="{ row }">
                  <span :style="row.microDetailsIsReject?'color:#999':(row.isDefaultScoreType13?'color:#999':'color: #606266')"> {{( row.microDetailLqNameStr==null ||row.microDetailLqNameStr=='')?"":row.scoreType13}} </span>
              </template>
            </vxe-column>
            <vxe-column field="scoreTypeAvg3" title="平均分" width='50' v-if="checkPermission('alllistsRatinges')">
              <template #default="{ row }">
                  <span :style="row.microDetailsIsReject?'color:#999':(row.isDefaultScoreType13?'color:#999':'color: #606266')"> {{( row.microDetailLqNameStr==null ||row.microDetailLqNameStr=='')?"":row.scoreTypeAvg3}} </span>
              </template>
            </vxe-column>
            <vxe-column field="Divisionline2" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
              <!-- 详情页 -->
            <vxe-column v-if="checkPermission('shootDetailsPage')" field="detailLqNameStr" title="详情页" width='65'>
                <template #default="{ row }">
                    <span :style="row.detailIsReject?'color:#999':'color: #606266'"> {{row.detailLqNameStr}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootCompleteDate')" field="detailOverTimeStr" title="完成日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.detailIsReject?'color:#999':'color: #606266'"> {{row.detailOverTimeStr}} </span>
                </template>
            </vxe-column>
            <vxe-column  field="addTaskTimeStr4" title="修改时间" width='70'></vxe-column>
            <vxe-column v-if="checkPermission('shootDays')" field="detailDaysStr" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.showRed4 ? 'color: red' : (row.detailIsReject?'color:#999':'color: #606266')"> {{row.detailDaysStr}} </span>
                </template> </vxe-column>
            <vxe-column  field="bhDate4" title="驳回日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.detailIsReject || row.isConfirmBh4 ?'color:#999':'color: #606266'"> {{row.bhDate4}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootDays')" field="bcDate4" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.detailIsReject?'color:#999':'color: #606266'"> {{row.bcDate4}} </span>
                </template> </vxe-column>
            <vxe-column   v-if="checkPermission('sylbxsbg')" field="makeUpDatetime4" title="补改日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.detailIsReject?'color:#999':'color: #606266'"> {{row.makeUpDatetime4}} </span>
                </template>
            </vxe-column>
            <vxe-column   v-if="checkPermission('sylbxsbg')" field="makeUpDay4" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.detailIsReject?'color:#999':'color: #606266'"> {{row.makeUpDay4}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootConfirmationPersion')" field="detailConfirmNameStr" title="确认人"
                width='65'>
                <template #default="{ row }">
                    <span :style="row.detailIsReject?'color:#999':'color: #606266'"> {{row.detailConfirmNameStr}} </span>
                </template>
                </vxe-column>
            <vxe-column v-if="checkPermission('shootConfirmationDate')" field="detailConfirmTimeStr" title="确认日期"
                width='70'>
                <template #default="{ row }">
                    <span :style="row.detailIsReject?'color:#999':'color: #606266'"> {{row.detailConfirmTimeStr}} </span>
                </template> </vxe-column>
            <vxe-column field="scoreType4" title="质量评分" width='70' v-if="checkPermission('alllistsRatinges')">
                <template #default="{ row }">
                    <span :style="row.detailIsReject?'color:#999':(row.isDefaultScoreType4?'color:#999':'color: #606266')"> {{( row.detailLqNameStr==null ||row.detailLqNameStr=='')?"":row.scoreType4}} </span>
                </template>
            </vxe-column>
            <vxe-column field="scoreType14" title="配合度评分" width='70' v-if="checkPermission('alllistsRatinges')">
              <template #default="{ row }">
                  <span :style="row.detailIsReject?'color:#999':(row.isDefaultScoreType14?'color:#999':'color: #606266')"> {{( row.detailLqNameStr==null ||row.detailLqNameStr=='')?"":row.scoreType14}} </span>
              </template>
            </vxe-column>
            <vxe-column field="scoreTypeAvg4" title="平均分" width='50' v-if="checkPermission('alllistsRatinges')">
              <template #default="{ row }">
                  <span :style="row.detailIsReject?'color:#999':(row.isDefaultScoreType14?'color:#999':'color: #606266')"> {{( row.detailLqNameStr==null ||row.detailLqNameStr=='')?"":row.scoreTypeAvg4}} </span>
              </template>
            </vxe-column>
            <vxe-column field="Divisionline2" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column v-if="checkPermission('shootPhotoModeling')" field="modelPhotosLqNameStr" title="照片建模" width='70'>
                <template #default="{ row }">
                    <span :style="row.modelPhotosIsReject?'color:#999':'color: #606266'"> {{row.modelPhotosLqNameStr}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootSheets')" field="modelPhotoCounts" title="张数" width='50'>
                <template #default="{ row }">
                    <span :style="row.modelPhotosIsReject?'color:#999':'color: #606266'"> {{row.modelPhotoCounts}} </span>
                </template> </vxe-column>
                <!-- 张数 -->
            <vxe-column v-if="checkPermission('shootCompleteDate')" field="modelPhotosOverTimeStr" title="完成日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.modelPhotosIsReject?'color:#999':'color: #606266'"> {{row.modelPhotosOverTimeStr}} </span>
                </template>
            </vxe-column>
            <vxe-column  field="addTaskTimeStr5" title="修改时间" width='70'></vxe-column>
            <vxe-column v-if="checkPermission('shootDays')" field="modelPhotosDaysStr" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.showRed5 ? 'color: red' : (row.modelPhotosIsReject?'color:#999':'color: #606266')"> {{row.modelPhotosDaysStr}} </span>
                </template>
            </vxe-column>
            <vxe-column  field="bhDate5" title="驳回日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.modelPhotosIsReject || row.isConfirmBh5 ?'color:#999':'color: #606266'"> {{row.bhDate5}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootDays')" field="bcDate5" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.modelPhotosIsReject?'color:#999':'color: #606266'"> {{row.bcDate5}} </span>
                </template>
            </vxe-column>
            <vxe-column   v-if="checkPermission('sylbxsbg')" field="makeUpDatetime5" title="补改日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.modelPhotosIsReject?'color:#999':'color: #606266'"> {{row.makeUpDatetime5}} </span>
                </template>
            </vxe-column>
            <vxe-column  v-if="checkPermission('sylbxsbg')"  field="makeUpDay5" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.modelPhotosIsReject?'color:#999':'color: #606266'"> {{row.makeUpDay5}} </span>
                </template>
            </vxe-column>
                <vxe-column v-if="checkPermission('shootConfirmationPersion')" field="modelPhotosConfirmNameStr" title="确认人"
                width='65'>
                <template #default="{ row }">
                    <span :style="row.detailIsReject?'color:#999':'color: #606266'"> {{row.modelPhotosConfirmTimeStr}} </span>
                </template>
                </vxe-column>
            <vxe-column v-if="checkPermission('shootConfirmationDate')" field="modelPhotosConfirmTimeStr" title="确认日期"
                width='70'>
                <template #default="{ row }">
                    <span :style="row.detailIsReject?'color:#999':'color: #606266'"> {{row.modelPhotosConfirmTimeStr}} </span>
                </template> </vxe-column>
                <vxe-column field="scoreType5" title="质量评分" width='70' v-if="checkPermission('alllistsRatinges')">
                <template #default="{ row }">
                    <span :style="row.modelPhotosIsReject?'color:#999':(row.isDefaultScoreType5?'color:#999':'color: #606266')"> {{( row.modelPhotosLqNameStr==null ||row.modelPhotosLqNameStr=='')?"":row.scoreType5}} </span>
                </template>
            </vxe-column>
            <vxe-column field="scoreType15" title="配合度评分" width='70' v-if="checkPermission('alllistsRatinges')">
              <template #default="{ row }">
                  <span :style="row.modelPhotosIsReject?'color:#999':(row.isDefaultScoreType15?'color:#999':'color: #606266')"> {{( row.modelPhotosLqNameStr==null ||row.modelPhotosLqNameStr=='')?"":row.scoreType15}} </span>
              </template>
            </vxe-column>
            <vxe-column field="scoreTypeAvg5" title="平均分" width='50' v-if="checkPermission('alllistsRatinges')">
              <template #default="{ row }">
                  <span :style="row.modelPhotosIsReject?'color:#999':(row.isDefaultScoreType15?'color:#999':'color: #606266')"> {{( row.modelPhotosLqNameStr==null ||row.modelPhotosLqNameStr=='')?"":row.scoreTypeAvg5}} </span>
              </template>
            </vxe-column>
            <vxe-column field="Divisionline2" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column v-if="checkPermission('shootVideoModeling')" field="modelVideoLqNameStr" title="视频建模" width='70'>
                <template #default="{ row }">
                    <span :style="row.modelVideoIsReject?'color:#999':'color: #606266'"> {{row.modelVideoLqNameStr}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootGs')" field="modelVedioCounts" title="个数" width='50'>
                <template #default="{ row }">
                    <span :style="row.modelVideoIsReject?'color:#999':'color: #606266'"> {{row.modelVedioCounts}} </span>
                </template> </vxe-column>
            <vxe-column v-if="checkPermission('shootCompleteDate')" field="modelVideoOverTimeStr" title="完成日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.modelVideoIsReject?'color:#999':'color: #606266'"> {{row.modelVideoOverTimeStr}} </span>
                </template>
            </vxe-column>
            <vxe-column  field="addTaskTimeStr6" title="修改时间" width='70'></vxe-column>
            <vxe-column v-if="checkPermission('shootDays')" field="modelVideoDaysStr" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.showRed6 ? 'color: red' : (row.modelVideoIsReject?'color:#999':'color: #606266')"> {{row.modelVideoDaysStr}} </span>
                </template> </vxe-column>
            <vxe-column field="bhDate6" title="驳回日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.modelVideoIsReject || row.isConfirmBh6 ?'color:#999':'color: #606266'"> {{row.bhDate6}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootDays')" field="bcDate6" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.modelVideoIsReject?'color:#999':'color: #606266'"> {{row.bcDate6}} </span>
                </template> </vxe-column>
            <vxe-column  v-if="checkPermission('sylbxsbg')" field="makeUpDatetime6" title="补改日期" width='70'>
                <template #default="{ row }">
                    <span :style="row.modelVideoIsReject?'color:#999':'color: #606266'"> {{row.makeUpDatetime6}} </span>
                </template>
            </vxe-column>
            <vxe-column   v-if="checkPermission('sylbxsbg')"  field="makeUpDay6" title="天数" width='45'>
                <template #default="{ row }">
                    <span :style="row.modelVideoIsReject?'color:#999':'color: #606266'"> {{row.makeUpDay6}} </span>
                </template>
            </vxe-column>
           <vxe-column v-if="checkPermission('shootConfirmationPersion')" field="modelVideoConfirmNameStr" title="确认人"
                width='65'>
                <template #default="{ row }">
                    <span :style="row.detailIsReject?'color:#999':'color: #606266'"> {{row.modelVideoConfirmNameStr}} </span>
                </template>
                </vxe-column>
            <vxe-column v-if="checkPermission('shootConfirmationDate')" field="modelVideoConfirmTimeStr" title="确认日期"
                width='70'>
                <template #default="{ row }">
                    <span :style="row.detailIsReject?'color:#999':'color: #606266'"> {{row.modelVideoConfirmTimeStr}} </span>
                </template> </vxe-column>
            <vxe-column field="scoreType6" title="质量评分" width='70' v-if="checkPermission('alllistsRatinges')">
                <template #default="{ row }">
                    <span :style="row.modelVideoIsReject?'color:#999':(row.isDefaultScoreType6?'color:#999':'color: #606266')"> {{( row.modelVideoLqNameStr==null ||row.modelVideoLqNameStr=='')?"":row.scoreType6}} </span>
                </template>
            </vxe-column>
            <vxe-column field="scoreType16" title="配合度评分" width='70' v-if="checkPermission('alllistsRatinges')">
              <template #default="{ row }">
                  <span :style="row.modelVideoIsReject?'color:#999':(row.isDefaultScoreType16?'color:#999':'color: #606266')"> {{( row.modelVideoLqNameStr==null ||row.modelVideoLqNameStr=='')?"":row.scoreType16}} </span>
              </template>
            </vxe-column>
            <vxe-column field="scoreTypeAvg6" title="平均分" width='50' v-if="checkPermission('alllistsRatinges')">
              <template #default="{ row }">
                  <span :style="row.modelVideoIsReject?'color:#999':(row.isDefaultScoreType16?'color:#999':'color: #606266')"> {{( row.modelVideoLqNameStr==null ||row.modelVideoLqNameStr=='')?"":row.scoreTypeAvg6}} </span>
              </template>
            </vxe-column>
            <vxe-column field="Divisionline3" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column v-if="checkPermission('shootFpPhoto')" field="fpPhotoLqNameStr" title="分配照片" width='70'>
                <template #default="{ row }">
                    <span :style="row.pIsReject?'color:#999':'color: #606266'"> {{row.fpPhotoLqNameStr}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootFpVedio')" field="fpVideoLqNameStr" title="分配视频" width='70'>
                <template #default="{ row }">
                    <span :style="row.vIsReject?'color:#999':'color: #606266'"> {{row.fpVideoLqNameStr}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootFpDetail')" field="fpDetailLqNameStr" title="分配详情" width='70'>
                <template #default="{ row }">
                    <span :style="row.dIsReject?'color:#999':'color: #606266'"> {{row.fpDetailLqNameStr}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootFpModel')" field="fpModelLqNameStr" title="分配建模" width='70'>
                <template #default="{ row }">
                    <span :style="row.mpIsReject || row.mvIsReject?'color:#999':'color: #606266'"> {{row.fpModelLqNameStr}} </span>
                </template>
            </vxe-column>

            <vxe-column field="Divisionline2" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>

            <vxe-column v-if="checkPermission('shootingvideoSearchpc')" field="fpNoPhotoLqName" title="排除照片" width='70'>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootingvideoSearchpc')" field="fpNoVideoLqName" title="排除视频" width='70'>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootingvideoSearchpc')" field="fpNoDetailLqName" title="排除详情" width='70'>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootingvideoSearchpc')" field="fpNoModelLqName" title="排除建模" width='70'>
            </vxe-column>
            <vxe-column  v-if="checkPermission('shootingvideoSearchpc')" field="Divisionline2" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column v-if="checkPermission('shootOperate')" field="operationGroupstr" title="运营小组" width='70'>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootCounter')" field="dockingPeople" title="对接人" width='65'> </vxe-column>
            <vxe-column v-if="checkPermission('shootTaskId')" field="platformStr" title="平台" width='65'> </vxe-column>
            <vxe-column v-if="checkPermission('shootShop')" field="shopNameStr" title="店铺" width='145'> </vxe-column>
            <vxe-column field="Divisionline" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column v-if="checkPermission('shootTaskOverTime')" field="productID" title="产品ID" width='105'> </vxe-column>
            <vxe-column field="fillTime" title="填写时间"  width='90'>
              <template #default="{ row }">
                <span> {{ thisForTime(row.fillTime) }} </span>
              </template>
            </vxe-column>
            <vxe-column field="styleName" title="类型" width='70'>
                <template #default="{ row }">
                    <span> {{row.styleName}} </span>
                </template>
            </vxe-column>
            <vxe-column field="fineStyleStr" title="款式" width='50'>
                <template #default="{ row }">
                    <span> {{row.fineStyleStr}} </span>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('showimplicitexamine')" field="isAssess" title="考核" :edit-render="{}" width='70'>
                <template #default="{ row }">
                    <vxe-switch v-model="row.isAssess" open-label="是" close-label="否" :open-value="true" :disabled="examine"
                        :close-value="false" @change="saveRowEvent(row)"></vxe-switch>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('alllistsRatinges')" field="allScoreAvg" title="平均分" width='55'></vxe-column>
            <vxe-column v-if="checkPermission('shootTaskOverTime')" field="taskOverTimeStr" title="完成时间" width='90'>
            </vxe-column>
            <vxe-column field="modifiedTimeStr" title="修改日期" width='75'></vxe-column>
            <vxe-column field="actualCompleteDay" title="完成天数" width='90'>
                <template #header>
                            <span>
                                <el-tooltip class="item" effect="dark" content="实际完成天数" placement="top-end">
                                    <span><i class="el-icon-question"></i></span>
                                </el-tooltip>
                                完成天数
                            </span>
                    </template>
                    <template #default="{ row }">
                      <span :class="row.showRed ? 'red-text':'black-text'">
                        {{ row.actualCompleteDay }}
                      </span>
                    </template>
                    <template #footer>
                      <el-tooltip effect="light" placement="top">
                        <template slot="content">
                          <span v-html="summaryarry.actualCompleteDay_msg" style="white-space: pre-line"></span>
                        </template>
                        <span>{{ summaryarry.actualCompleteDay_sum }}</span>
                      </el-tooltip>
                    </template>
            </vxe-column>
            <vxe-column field="confirmTimeStr" title="确认时间" width='75'> </vxe-column>

            <vxe-column v-if="checkPermission('shootArrivalTime')" field="arrivalTimeStr" title="到货日期" width='75'>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootArrivalDays')" field="arrivalTimeDays" title="到货天数" width='35'>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootDeliverTime')" field="deliverTimeStr" title="发货日期" width='75'>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootDeliverDays')" field="deliverTimeDays" title="发货天数" width='35'>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootApplicationTime')" field="applyTimeStr" title="申请日期" width='75'>
            </vxe-column>
            <vxe-column v-if="checkPermission('shootApplicationDay')" field="applyTimeDays" title="申请天数" width='35'>
            </vxe-column>
            <!-- <vxe-column field="mainUpdateTime" title="修改日期" width='75'> <template #default="{ row }">{{
                formatIsCommission(row.mainUpdateTime) }} </template></vxe-column> -->
            <vxe-column v-if="checkPermission('shootCreationDate')" field="createdTime" title="创建日期" width='78' sortable>
                <template #default="{ row }">{{ formatIsCommission(row.createdTime) }} </template>
            </vxe-column>
            <vxe-column field="Divisionline4" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column v-if="checkPermission('shootorderNo')" field="orderNoInner" title="内部单号" width='100'>
                <template #default="{ row }">
                    <a type="text" show-overflow="ellipsis" style="color:#409EFF" @click="onShowOrderDtl(row)"> {{
                        row.orderNoInner }} </a>
                </template> </vxe-column>
            <vxe-column v-if="checkPermission('shootorderExpressNumber')" field="expressNo" title="快递单号" width='135'>
                <template #default="{ row }">
                    <a type="text" show-overflow="ellipsis" style="color:#409EFF" @click="onShowExproessHttp(row)"> {{
                        row.expressNo }} </a>
                </template> </vxe-column>
            <vxe-column v-if="checkPermission('shootorderNo')" field="shootOrderTrack" title="拿样跟踪" width='80'>
                <template #default="{ row }">
                    <a type="text" show-overflow="ellipsis" style="color:#409EFF" @click="onShowOrderDtl(row)"> {{
                        row.shootOrderTrack }} </a>
                </template>
            </vxe-column>
            <div v-if="showCacle">
                <vxe-column field="Divisionline5" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
                <!-- <vxe-column field="taskType" title="类型" width='75'> </vxe-column>
                <vxe-column field="shootingRate" title="拍摄使用率" width='75'>
                    <template #default="{ row }">{{ row.shootingRate.toFixed(4) + '%' }} </template>
                </vxe-column>
                <vxe-column field="modelingRate" title="建模使用率" width='75'>
                    <template #default="{ row }">{{ row.modelingRate.toFixed(4) + '%' }} </template>
                </vxe-column> -->
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDeptCommission" title="部门总款" width='65'>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDeptPhotoCommission" title="部门照片总款"
                    width='65'> </vxe-column>

                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingPhotoCommission" title="义乌照片总款"
                    width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingPhotoCommissionNc" title="南昌照片总款"
                    width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingPhotoCommissionWh" title="武汉照片总款"
                    width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingPhotoPrice" title="照片单款" width='65'>
                </vxe-column>

                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDeptVeidoCommission" title="部门视频总款"
                    width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingVedioCommission" title="义乌视频总款"
                    width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingVedioCommissionNc" title="南昌视频总款"
                    width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingVedioCommissionWh" title="武汉视频总款"
                    width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingVedioPrice" title="视频单款" width='65'>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingMicroVedioPrice" title="微。拍摄单条"
                    width='65'> </vxe-column>

                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDeptDetailCommission" title="部门详情页总款"
                    width='75'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDetailCommission" title="义乌详情页总款"
                    width='75'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDetailCommissionNc" title="南昌详情页总款"
                    width='75'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDetailCommissionWh" title="武汉详情页总款"
                    width='75'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDetailPrice" title="详情页单款" width='55'>
                </vxe-column>

                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDeptModelPhotoCommission" title="部门建模照片总数"
                    width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingModelPhotoCommission" title="义乌建模照片总数"
                    width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingModelPhotoCommissionNc" title="南昌建模照片总数"
                    width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingModelPhotoCommissionWh" title="武汉建模照片总数"
                    width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingModelPhotoPrice" title="建模照片单张"
                    width='65'> </vxe-column>

                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDeptModelVeidoCommission" title="部门建模视频总数"
                    width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingModelVedioCommission" title="义乌建模视频总数"
                    width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingModelVedioCommissionNc" title="南昌建模视频总数"
                    width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingModelVedioCommissionWh" title="武汉建模视频总数"
                    width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingModelVedioPrice" title="建模视频单个"
                    width='65'> </vxe-column>

                <vxe-column v-if="checkPermission('shootlist-hjl')" field="shootingCommissionTotal" title="提成合计" width='291'>
                </vxe-column>
<!--
                <vxe-column v-if="checkPermission('shootlist-hjl')" field="shootingBaseSalary" title="拍摄底薪" width='65'>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-hjl')" field="modelBaseSalary" title="建模底薪" width='65'>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-hjl')" field="shootinPropPrice" title="道具费" width='65'>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-hjl')" field="shootingTotal" title="合计" width='100'>
                </vxe-column>-->

            </div>
        </vxe-table>
    </div>
</template>
<script>
// import VXETable from 'vxe-table'
// import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
// import ExcelJS from 'exceljs'
import { formatTime } from "@/utils";

// VXETable.use(VXETablePluginExportXLSX, {
//       ExcelJS
//     })
import { updateFineStyle } from '@/api/media/ShootingVideo';
export default {
    props: {
        editconfig: { type: Object, default: () => { return {} } },
        treeProp: { type: Object, default: () => { return {} } },
        hasSeq: { type: Boolean, default: () => { return true } },
        hascheck: { type: Boolean, default: () => { return false } },
        showToolbar: { type: Boolean, default: () => { return true } },
        // 表格数据

        // 表格数据
        tableData: { type: Array, default: () => [] },
        // 表格型号：mini,medium,small
        size: { type: String, default: 'mini' },
        type: { type: String, default: 'primary' },
        isBorder: { type: Boolean, default: true },
        // 表格列配置
        tableCols: { type: Array, default: () => [] },
        isRemoteSort: { type: Boolean, default: () => { return true } },
        id: { type: String, default: () => { return new Date().valueOf().toString() } },
        that: { type: Object, default: () => { return null } },
        loading: { type: Boolean, default: () => { return false; } },
        border: { type: Boolean | Object, default: () => { return 'default' } },
        tableHandles: { type: Array, default: () => [] },
        showsummary: { type: Boolean, default: false },
        align: { type: String, default: '' }, //对齐方式
        summaryarry: { type: Object, default: () => { } },
        tablekey: { type: String, default: '' },//表格key
        height: { type: String, default: '100%' },//固定表头作用

        showCacle: { type: Boolean, default: () => { return false } },
        examine: { type: Boolean, default: () => { return true } },

    },
    created() {
        // VXETable.use(VXETablePluginExportXLSX);
        this.$nextTick(() => {
            // 手动将表格和工具栏进行关联
            this.$refs.xTable.connect(this.$refs.xToolbar)
        })
    },
    data() {
        return {
            lastSortArgs: {
                field: "",
                order: "",
            },
            arrlist: [],
            summarycolumns: [],
            tablecolumns: [],
            aaaa: '',
        }
    },
    async mounted() {
        this.$nextTick(() => {
            this.tablecolumns = this.$refs.xTable.getColumns()

        })
    },
    methods: {
        thisForTime (value) {
          return value == null ? null : formatTime(value, 'YYYY-MM-DD')
        },
        async saveRowEvent(row){
          if(row.fineStyleStr == '精品'){
            this.$message.error('款式为精品不可进行该操作');
            row.isAssess = false
            return
          }
          const params = {
            shootingTaskId:row.shootingTaskId,
            isAssess:row.isAssess
          }
          const {success} = await updateFineStyle(params)
          if(success){
            this.$emit("saveRowEvent");
            this.$message({ message: '操作成功', type: 'success' });
          }else{
            row.isAssess = !row.isAssess
          }
        },
        customSortMethod({ data, sortList }) {
            if (this.isRemoteSort) {
                if (sortList && sortList.length > 0) {
                    if (sortList[0].field != this.lastSortArgs.field || sortList[0].order != this.lastSortArgs.order) {
                        this.lastSortArgs = { ...sortList[0] };
                        this.$emit('sortchange', {
                            order: (this.lastSortArgs.order.indexOf('desc') > -1 ? 'descending' : 'asc'),
                            prop: this.lastSortArgs.field
                        });
                    }
                }
            } else {
                this.$refs.xTable.sort(sortList[0].field, sortList[0].order)
            }
        },
        rowStyleFun({ row, rowIndex, $rowIndex }) {
            if (row && row.isend == 0) {
                return '';
            } else {
                return 'droprow';
            }
        },
        onShowOrderDtl(row) {
            this.$emit('onShowOrderDtl', row)
        },
        onShowExproessHttp(row) {
            this.$emit('onShowExproessHttp', row)
        },
        //行切换事件
        rowChange({ newValue, oldValue, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }) {
            this.$emit('rowChange', row);
        },
        openComputOutInfo(row) {
            this.$emit('openComputOutInfo', row)
        },
        shootUrgencyCilck(one, two) {
            this.$emit('shootUrgencyCilck', one, two)
        },
        openTaskRmarkInfo(row) {

            this.$emit('openTaskRmarkInfo', row)
        },
        videotaskuploadfileDetal(row) {

            this.$emit('videotaskuploadfileDetal', row)
        },
        editTask(row) {
            this.$emit('editTask', row)
        },
        selectChangeEvent({ checked }) {
            const records = this.$refs.xTable.getCheckboxRecords()
            this.$emit('selectchangeevent', records);
        },
        //导出
        exportData(filename,outdata) {
            this.$refs.xTable.exportData({filename:filename, data: outdata,  sheetName: 'Sheet1',type: 'xlsx' })
        },
        formatIsCommission(value) {
            return value == null ? null : formatTime(value, 'YY-MM-DD')
        },
        //批量控制列的显影
        async ShowHidenColums(arrlist) {
            let _this = this;
            this.$refs.xTable.getTableColumn().collectColumn.forEach(column => {
                if (arrlist.includes(column.property)) {
                    column.visible = false
                } else {
                    column.visible = true
                }
            })
            _this.$nextTick(() => {
                if (_this.$refs.xTable) {
                    _this.$refs.xTable.refreshColumn()
                }
            })

        },
        //清空全选
        clearSelection() {
            this.$refs.xTable.clearCheckboxRow()
        },
        footerMethod({ columns, data }) {
            const sums = [];
            if (!this.summaryarry)
                return sums
            var arr = Object.keys(this.summaryarry);
            if (arr.length == 0)
                return sums
            //const { columns, data } = param;
            var hashj = false;
            columns.forEach((column, index) => {
                if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
                    var sum = this.summaryarry[column.property + '_sum'];
                    if (sum == null) return;
                    else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
                    else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum
                    else sums[index] = sum
                }
                else sums[index] = ''
            });
            return [sums]
        },
        footercellclick({ items, $rowIndex, column, columnIndex, $columnIndex, $event }) {

            let self = this;
            var col = findcol(self.tableCols, column.property);
            if (col && col.summaryEvent)
                self.$emit('summaryClick', column.property)

            function findcol(cols, property) {
                let column;
                for (var i = 0; i < cols.length; i++) {
                    var c = cols[i];
                    if (column) break
                    else if (c.prop && c.prop.toLowerCase() == property.toLowerCase()) {
                        column = c;
                        break
                    }
                    else if (c.cols && c.cols.length > 0) column = findcol(c.cols, property)
                }
                return column
            }
        }
    }


}
</script>


<style lang="scss" scoped> .vxe-table--render-default.border--default .vxe-table--header-wrapper {
     background-color: #fafbff;
 }

 /*斑马线颜色*/
 .vxe-table--render-default .vxe-body--row.row--stripe {
     background-color: #fafbff;
 }

 .vxe-table--render-default .vxe-body--row.row--current {
     background-color: #e5ecf5;
 }


 .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
     background-color: #A8A8A8;
 }

 .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
     background-color: #787878;
 }

 /*滚动条整体部分*/
 .mytable-scrollbar20221212 ::-webkit-scrollbar {
     width: 18px;
     height: 26px;
 }

 /*滚动条的轨道*/
 .mytable-scrollbar20221212 ::-webkit-scrollbar-track {
     background-color: #f1f1f1;
 }

 /*滚动条里面的小方块，能向上向下移动*/
 .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
     background-color: #c1c1c1;
     border-radius: 3px;
     box-sizing: border-box;
     border: 2px solid #F1F1F1;
     box-shadow: inset 0 0 6px rgba(255, 255, 255, .5);
 }

 // 滚动条鼠标悬停颜色
 .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
     background-color: #A8A8A8;
 }

 // 滚动条拖动颜色
 .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
     background-color: #787878;
 }

 /*边角，即两个滚动条的交汇处*/
 .mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
     background-color: #dcdcdc;
 }


 // 图片大小
 .mytable-scrollbar20221212 .images20221212 {
     max-width: 150px;
     max-height: 150px;
     width: 40px !important;
     height: 40px !important;
 }

 // 图片张数标记
 .mytable-scrollbar20221212 .badgeimage20221212 .el-badge__content.is-fixed {
     top: 10px;
 }

 /*  工具箱位置  */
 .vxetoolbar20221212 {
     position: absolute;
     top: 58px;
     right: 3px;
     padding-top: 0;
     padding-bottom: 0;
     z-index: 999;
     background-color: rgb(255 255 255 / 0%);
 }
// 表头高度
::v-deep .vxe-table--render-default.size--mini .vxe-header--column:not(.col--ellipsis) {
    height:50px !important;
 }
// 表头文字行间距
 ::v-deep  .vxe-header--column {
    line-height: 18px !important;
}
 // 表格内边距
 ::v-deep .vxe-table--render-default .vxe-cell {
     padding: 0 0 0 8px !important;
 }
 .vxetableheadercell-left-20221216 {
     text-align: left;
 }

 .vxetableheadercell-center-20221216 {
     text-align: center;
 }

 .vxetableheadercell-right-20221216 {
     text-align: right;
 }

 .vxe-icon-ellipsis-h:hover {
     color: #409EFF;
     margin-left: 2px;
     background-color: #F1F1F1;
 }

 .vxe-icon-ellipsis-h {
     color: #999;
     font-size: 15px;
 }

 .vxe-icon-file-txt:hover {
     color: #409EFF;
     margin-left: 2px;
     background-color: #F1F1F1;
     font-weight: 600;
 }

 .vxe-icon-file-txt {
     color: #999;
     font-size: 15px;
 }

 .vxetablecss {
     margin: 0;
 }

 ::v-deep span.vxe-cell--item {
     cursor: pointer !important;
 }

 ::v-deep .droprow td {
     color: rgb(250, 9, 9);
     position: relative;
 }

 ::v-deep .droprow ::after {
     content: "";
     position: absolute;
     top: 50%;
     left: 0;
     width: 100%;
     height: 0.1px;
     background-color: rgb(250, 9, 9);
     transform: translateY(-50%);
 }
 .red-text {
  color: red;
}

.black-text {
  color: black;
}

 </style>
