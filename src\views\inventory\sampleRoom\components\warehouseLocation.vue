<template>
  <div class="top_container">
    <div class="container_left">
      <el-button type="primary" @click="addWarehouse">新增仓库</el-button>
      <el-scrollbar style="height: 95%;margin-top: 3%;" v-loading="leftloading">
        <el-tree :data="stashData" node-key="warehouseCode" default-expand-all :expand-on-click-node="false"
          :props="treeProps" :default-expanded-keys="defaultExpandedKeys">
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <el-tooltip class="item" v-if="node.label.length > 10" effect="dark" :content="node.label" placement="top">
              <span class="label" @click="() => onTreeQuery(node, data)">
                {{ node.label }}
              </span>
            </el-tooltip>
            <span v-else class="label" @click="() => onTreeQuery(node, data)">{{ node.label }}</span>
            <span class="actions">
              <el-button v-if="data.isLeaf" type="text" size="mini" @click="() => append(data)">
                新增区域
              </el-button>
              <el-button type="text" size="mini" @click="() => edit(node, data)">
                编辑
              </el-button>
            </span>
          </span>
        </el-tree>
      </el-scrollbar>
    </div>
    <div class="container_right">
      <MyContainer>
        <template #header>
          <div class="top">
            <el-input v-model.trim="ListInfo.warehouseBitCode" placeholder="库位编码" maxlength="50" clearable
              class="publicCss" />
            <el-input v-model.trim="ListInfo.channelNumber" placeholder="通道号" maxlength="50" clearable
              class="publicCss" />
            <el-input v-model.trim="ListInfo.shelfNumber" placeholder="货架号" maxlength="50" clearable
              class="publicCss" />
            <el-input v-model.trim="ListInfo.floorNumber" placeholder="层号" maxlength="50" clearable class="publicCss" />
            <el-input v-model.trim="ListInfo.bitNumber" placeholder="位号" maxlength="50" clearable class="publicCss" />
            <el-input v-model.trim="ListInfo.warehouse" placeholder="仓库" maxlength="50" clearable class="publicCss" />
            <el-input v-model.trim="ListInfo.warehouseArea" placeholder="区域" maxlength="50" clearable
              class="publicCss" />
            <el-button type="primary" @click="getList('search')">搜索</el-button>
            <el-button type="primary" @click="addLocation">批量新增库位</el-button>
            <el-button type="primary" @click="newStorageLocation">新增库位</el-button>
            <el-button type="primary" @click="onBatchDeletion">批量删除</el-button>
            <el-button type="primary" @click="onBatchPrint">批量打印</el-button>
          </div>
        </template>
        <vxetablebase :id="'warehouseLocation202502181535'" :tablekey="'warehouseLocation202502181535'" ref="table"
          :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
          :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
          :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
          :height="'100%'" @select="checkboxSelect" @checkbox-range-end="checkboxSelect">
          <template slot="right">
            <vxe-column title="操作" width="60" fixed="right">
              <template #default="{ row, $index }">
                <div style="display: flex; justify-content: center; width: 100%;">
                  <el-button type="text" @click="handleEdit(row, $index)"
                    :disabled="row.sampleCode ? true : false">修改</el-button>
                </div>
              </template>
            </vxe-column>
          </template>
        </vxetablebase>
        <template #footer>
          <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
      </MyContainer>
    </div>

    <el-dialog :title="stashTitle" :visible.sync="stashVisible" width="20%" v-dialogDrag>
      <div style="padding: 20px 5px 0 5px;">
        <el-form :model="stashForm" :rules="stashrules" ref="refstashForm" label-width="100px" class="demo-ruleForm">
          <el-row :gutter="20">
            <el-form-item label="仓库名称" :label-width="'125px'" prop="warehouse">
              <el-input v-model.trim="stashForm.warehouse" placeholder="仓库名称" maxlength="50" clearable
                class="editCss" />
            </el-form-item>
          </el-row>
          <el-row :gutter="20">
            <el-form-item label="仓库编码" :label-width="'125px'" prop="warehouseCode">
              <el-input v-model.trim="stashForm.warehouseCode" placeholder="仓库编码" maxlength="50" clearable
                class="editCss" :disabled="stashTitle === '编辑仓库'" />
            </el-form-item>
          </el-row>
          <el-row :gutter="20">
            <el-form-item label="区域" :label-width="'125px'">
              <el-input v-model.trim="stashForm.warehouseArea" placeholder="区域" maxlength="50" clearable
                class="editCss" />
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="stashVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSingleSave">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="regionTitle" :visible.sync="regionVisible" width="20%" v-dialogDrag>
      <div style="padding: 20px 5px 0 5px;">
        <el-form :model="regionForm" :rules="regionrules" ref="refregionForm" label-width="100px" class="demo-ruleForm">
          <el-row :gutter="20">
            <el-form-item label="区域名称" :label-width="'125px'" prop="warehouseArea">
              <el-input v-model.trim="regionForm.warehouseArea" placeholder="区域名称" maxlength="50" clearable
                class="editCss" />
            </el-form-item>
          </el-row>
          <el-row :gutter="20">
            <el-form-item label="区域编码" :label-width="'125px'" prop="warehouseAreaCode">
              <el-input v-model.trim="regionForm.warehouseAreaCode" placeholder="区域编码" maxlength="50" clearable
                class="editCss" :disabled="regionTitle === '编辑区域'" />
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="regionVisible = false">取 消</el-button>
        <el-button type="primary" @click="onStashSave">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="库位批量生成" :visible.sync="storageLocationVisible" width="50%" v-dialogDrag>
      <batchStorageLocation v-if="storageLocationVisible" ref="refstorageLocation" @successClick="successClick"
        @cancelClick="storageLocationVisible = false" :regionList="regionList" :stashList="stashList" :result="result"
        :stashlt="stashlt" />
    </el-dialog>

    <el-dialog title="库位管理" :visible.sync="manageLocationVisible" width="30%" v-dialogDrag>
      <manageStorageLocation v-if="manageLocationVisible" ref="refmanageStorageLocation" @successClick="successClick"
        @cancelClick="manageLocationVisible = false" :regionList="regionList" :stashList="stashList"
        :parameterEdit="parameterEdit" :stashData="stashData" :result="result" :stashlt="stashlt" />
    </el-dialog>

    <el-dialog title="批量打印" :visible.sync="batchPrintingVisible" width="40%" v-dialogDrag>
      <batchPrinting v-if="batchPrintingVisible" ref="refbatchPrinting" @successClick="successClick"
        @cancelClick="batchPrintingVisible = false" :checkboxList="checkboxList" />
    </el-dialog>
  </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import batchStorageLocation from "./batchStorageLocation.vue"
import manageStorageLocation from "./manageStorageLocation.vue"
import batchPrinting from "./batchPrinting.vue"
import { getWarehouseLocationManagement, getSampleWarehouseAreaWarehouse, getSampleWarehouse, getSampleWarehouseArea, addOrEditSampleWarehouse, addOrEditSampleWarehouseArea, deleteWarehouseLocationManagement } from '@/api/inventory/sampleGoods';
import dayjs from 'dayjs'
// import { data } from "jquery";
const tableCols = [
  { istrue: true, width: '60', type: "checkbox" },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouse', label: '仓库', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouseArea', label: '区域', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouseBitCode', label: '库位编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'channelNumber', label: '通道号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shelfNumber', label: '货架号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'floorNumber', label: '层号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'bitNumber', label: '位号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'sampleCode', label: '样品编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'sampleName', label: '样品名称', },
]
export default {
  name: "warehouseLocation",
  components: {
    MyContainer, vxetablebase, batchStorageLocation, manageStorageLocation, batchPrinting
  },
  data() {
    return {
      result: [],
      stashlt: [],
      defaultExpandedKeys: [],
      batchPrintingVisible: false,
      parameterEdit: {},
      checkboxList: [],//选中的数据
      stashList: [],//仓库列表
      regionList: [],//区域列表
      treeProps: {
        children: "warehouseAreaList",
        label: (data) => (data.warehouseAreaList ? data.warehouse : data.warehouseArea)
      },
      leftloading: false,
      regionVisible: false,
      regionForm: {
        warehouseArea: '',
        warehouseAreaCode: '',
        warehouse: '',
        warehouseCode: '',
        id: null,
      },
      regionrules: {
        warehouseArea: [
          { required: true, message: '请输入区域名称', trigger: 'blur' },
        ],
        warehouseAreaCode: [
          { required: true, message: '请输入区域编码', trigger: 'blur' },
        ],
      },
      regionTitle: '新增区域',
      storageLocationVisible: false,
      manageLocationVisible: false,
      manageEdit: false,
      stashForm: {
        warehouse: '',
        warehouseCode: '',
        warehouseArea: '',
        id: null,
      },
      stashrules: {
        warehouse: [
          { required: true, message: '请输入仓库名称', trigger: 'blur' },
        ],
        warehouseCode: [
          { required: true, message: '请输入仓库编码', trigger: 'blur' },
        ],
      },
      stashTitle: '新增仓库',
      stashVisible: false,
      stashData: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'createdTime',
        isAsc: false,
        warehouse: '',
        warehouseArea: '',
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.init()
    await this.getList()
  },
  methods: {
    onTreeQuery(node, data) {
      console.log(node, data)
      if (data.warehouseAreaList && data.warehouseAreaList.length >= 0) {
        this.ListInfo.warehouse = data.warehouse
        this.ListInfo.warehouseArea = ''
      } else {
        this.ListInfo.warehouse = data.warehouse
        this.ListInfo.warehouseArea = data.warehouseArea
      }
      this.getList()
    },
    handleEdit(row) {
      this.parameterEdit = JSON.parse(JSON.stringify(row))
      this.manageEdit = true
      this.manageLocationVisible = true
    },
    onBatchPrint() {
      if (this.checkboxList.length == 0) {
        this.$message.warning('请选择要打印的数据')
        return
      }
      this.batchPrintingVisible = true
    },
    onBatchDeletion() {
      if (this.checkboxList.length == 0) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      this.$confirm('是否删除所选数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.loading = true
        const { success } = await deleteWarehouseLocationManagement({ ids: this.checkboxList.map(item => item.id) });
        this.loading = false
        if (success) {
          this.$message.success('删除成功')
          this.getList()
          this.checkboxList = []
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
      });
    },
    checkboxSelect(data) {
      this.checkboxList = data
    },
    successClick() {
      this.storageLocationVisible = false
      this.manageLocationVisible = false
      this.getList()
    },
    onStashSave() {
      this.$refs.refregionForm.validate(async (valid) => {
        if (valid) {
          const { success } = await addOrEditSampleWarehouseArea(this.regionForm)
          if (success) {
            this.$message.success('操作成功')
            this.regionVisible = false
            this.getList()
            this.init()
          } else {
            this.$message.error('操作失败')
          }
        }
      })
    },
    onSingleSave() {
      this.$refs.refstashForm.validate(async (valid) => {
        if (valid) {
          const { success } = await addOrEditSampleWarehouse(this.stashForm)
          if (success) {
            this.$message.success('操作成功')
            this.stashVisible = false
            this.getList()
            this.init()
          } else {
            this.$message.error('操作失败')
          }
        }
      })
    },
    onCleardataMethod() {
      this.$nextTick(() => {
        this.$refs.refstashForm.clearValidate();
      });
      this.stashForm = {
        warehouse: '',
        warehouseCode: '',
        warehouseArea: '',
        id: null,
      }
    },
    onClearRegionMethod() {
      this.$nextTick(() => {
        this.$refs.refregionForm.clearValidate();
      });
      this.regionForm = {
        warehouseArea: '',
        warehouseAreaCode: '',
        warehouse: '',
        warehouseCode: '',
        id: null,
      }
    },
    addWarehouse() {
      this.onCleardataMethod()
      this.stashTitle = '新增仓库'
      this.stashVisible = true
    },
    append(data) {
      this.regionVisible = true
      this.onClearRegionMethod()
      this.regionTitle = '新增区域'
      this.regionForm.warehouse = data.warehouse ? data.warehouse : ''
      this.regionForm.warehouseCode = data.warehouseCode ? data.warehouseCode : ''
    },
    edit(node, data) {
      this.$nextTick(() => {
        if (data.isLeaf) {
          this.stashTitle = '编辑仓库'
          this.onCleardataMethod()
          this.stashForm = JSON.parse(JSON.stringify(data))
          this.stashForm.id = data.id
          this.stashVisible = true
        } else {
          this.regionTitle = '编辑区域'
          this.onClearRegionMethod()
          this.regionForm = JSON.parse(JSON.stringify(data))
          this.regionForm.id = data.id
          this.regionVisible = true
        }
      })
      this.$forceUpdate()
    },
    addLocation() {
      this.storageLocationVisible = true
    },
    newStorageLocation() {
      this.parameterEdit = {}
      this.manageLocationVisible = true
      this.manageEdit = true
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data: data1, success: success1 } = await getSampleWarehouseAreaWarehouse(this.ListInfo)
      if (success1) {
        this.stashData = data1
        this.stashData.forEach(item => {
          item.isLeaf = true
        })
        const warehouseAreaList = [];
        const warehouseInfoList = [];
        this.stashData.filter(warehouse => warehouse.warehouseAreaList).forEach(warehouse => {
          const filteredAreas = warehouse.warehouseAreaList.filter(area =>
            this.regionList.includes(area.warehouseArea)
          );
          warehouseAreaList.push(
            ...filteredAreas.map(area => ({
              warehouseAreaCode: area.warehouseAreaCode,
              warehouseCode: area.warehouseCode,
              warehouseArea: area.warehouseArea
            }))
          );
          if (filteredAreas.length >= 0) {
            warehouseInfoList.push({
              warehouseCode: warehouse.warehouseCode,
              warehouse: warehouse.warehouse
            });
          }
        });
        this.result = warehouseAreaList
        this.stashlt = warehouseInfoList
      }
      const { data, success } = await getWarehouseLocationManagement(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async init() {
      const { data: data1, success: success1 } = await getSampleWarehouse()
      this.stashList = [...new Set(data1)];
      const { data: data2, success: success2 } = await getSampleWarehouseArea()
      this.regionList = [...new Set(data2)];
    },
  }
}
</script>

<style scoped lang="scss">
.top_container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;

  .container_left {
    flex: 2;
    overflow: hidden;
    flex-basis: 20%;
    border: 1px solid #dcdfe6;
    border-radius: 5px;
    height: 95%;
    padding: 10px 5px;
    min-width: 200px;
  }

  .container_right {
    flex: 8;
    overflow: hidden;
    flex-basis: 80%;
    height: 100%;
    min-width: 800px;
  }
}

.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 120px;
    margin-right: 5px;
  }
}

.custom-tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.label {
  flex-grow: 1;
  text-align: left;
  max-width: 170px;
  /* 设置最大宽度 */
  overflow: hidden;
  /* 隐藏溢出内容 */
  text-overflow: ellipsis;
  /* 显示省略号 */
  white-space: nowrap;
  /* 文本不换行 */
  font-size: 13px;
}

.actions {
  display: flex;
  justify-content: flex-start;
  // gap: 3px;
  width: 100px;
}

.editCss {
  width: 80%;
  margin-right: 5px;
}
</style>
