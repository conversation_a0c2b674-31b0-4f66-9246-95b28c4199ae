<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <div>
                <!-- <el-button style="padding: 0;margin:0;" clearable>
                <el-radio-group v-model="payTimeSel" @change="payTimeSelChange">
                    <el-radio-button label="1">昨天至今日</el-radio-button>
                    <el-radio-button label="2">近7天</el-radio-button>
                    <el-radio-button label="3">近14天</el-radio-button>
                    <el-radio-button label="4">近30天</el-radio-button>
                </el-radio-group>
            </el-button> -->
                <!-- <el-button style="padding: 0;margin: 0;">
                <el-input style="width:140px" v-model="filter.batchNumber" v-model.trim="filter.batchNumber" placeholder="批次号" clearable></el-input>
            </el-button> -->
                <span style="font-size:14px; color:#303133">导入时间：</span>
                <el-button style="padding: 0;margin:0px 10px 0px 0px;">
                    <el-date-picker style="width:130px" v-model="filter.importTime" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="导入时间" @change="payTimeSelChangePick" clearable></el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin:0px 10px 0px 0px;">
                    <el-date-picker style="width:220px" v-model="filter.paytimerange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="支付时间" end-placeholder="支付时间" @change="payTimeSelChangePick" clearable></el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin:0px 10px 0px 0px;">
                    <el-date-picker style="width:220px" v-model="filter.calctimerange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="计算时间" end-placeholder="计算时间" @change="payTimeSelChangePick" clearable></el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin:0px 10px 0px 0px;">
                    <el-date-picker style="width:220px" v-model="filter.exptimerange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="能发时间" end-placeholder="能发时间" @change="payTimeSelChangePick" clearable></el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin: 0px 10px 0px 0px;">
                    <el-input style="width:188px" v-model="filter.orderNoInner" v-model.trim="filter.orderNoInner" placeholder="内部订单号" clearable></el-input>
                </el-button>
            </div>
            <div>
                <span style="font-size:14px; color:#303133">其他条件：</span>
                <el-button style="padding: 0;margin:0px 10px 0px 0px;">
                    <el-select style="width:130px" v-model="filter.platform" placeholder="选择平台" clearable @change="onchangeplatform">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" clearable />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin:0px 10px 0px 0px;">
                    <el-select v-model="filter.shopCode" placeholder="选择店铺" clearable style="width: 220px">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin:0px 10px 0px 0px;">
                    <el-select v-model="filter.warehouse" placeholder="选择发货仓" clearable style="width: 123px">
                        <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0px 10px 0px 0px;">
                    <el-select v-model="filter.isCalc" placeholder="是否计算" clearable style="width: 85px">
                        <el-option label="是" :value="true" />
                        <el-option label="否" :value="false" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0px 10px 0px 0px;">
                    <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组" style="width: 120px">
                        <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0px 10px 0px 0px;">
                    <el-input style="width:88px" v-model="filter.province" v-model.trim="filter.province" placeholder="省" clearable></el-input>
                </el-button>
                <el-button style="padding: 0;margin: 0px 10px 0px 0px;">
                    <el-input style="width:88px" v-model="filter.city" v-model.trim="filter.city" placeholder="市" clearable></el-input>
                </el-button>
                <el-button style="padding: 0;margin:0px 10px 0px 0px;">
                    <el-input style="width:88px" v-model="filter.area" v-model.trim="filter.area" placeholder="区" clearable></el-input>
                </el-button>
                <el-button style="padding: 0;margin: 0px 10px 0px 0px;">
                    <el-input style="width:120px" v-model="filter.street" v-model.trim="filter.street" placeholder="街道" clearable></el-input>
                </el-button>
            </div>
        </template>
        <ces-table :ref="tablekey" :key="tablekey" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :loading="listLoading">
            <template slot='extentbtn' style="height:100px">
                <el-button-group>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" v-if="checkPermission(['api:order:CovidStopSend:ImportCovidStopSendAsync'])" @click="startImport">导入疫情停发订单</el-button>
                    <!-- <el-button type="primary" @click="downloadTemplate">下载导入模板</el-button> -->
                    <el-button type="primary" v-if="checkPermission(['api:order:CovidStopSend:ExportCovidStopSendDataAsync'])" @click="onExport">导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getlist" />
        </template>
        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%">
            <span v-loading="importLoading">
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx" :on-success="uploadSuccess" :http-request="uploadFile" :file-list="fileList">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传</el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag>
            <orderLogPage ref="orderLogPage" :orderNoInner="sendOrderNoInner" style="z-index:10000;height:600px" />
        </el-dialog>
    </my-container>
</template>

<script>
    import dayjs from "dayjs";
    import { formatTime } from "@/utils";
    import MyContainer from '@/components/my-container'
    import cesTable from "@/components/Table/table.vue";
    import { sendwarehouselist } from "@/utils/tools";
    import { getList as getshopList, getDirectorGroupList } from '@/api/operatemanage/base/shop'
    import { rulePlatform } from "@/utils/formruletools";
    import { getExpressComanyStationName, getExpressComanyAll } from "@/api/express/express";
    import { getOrderStatusListAsync } from "@/api/order/logisticsEarlyWarPage";
    import { getPageCovidStopSendDataAsync, importCovidStopSendAsync, exportCovidStopSendDataAsync } from "@/api/order/covidStopSendPage";
    import orderLogPage from "@/views/order/logisticsWarning/orderLogPage.vue";
    const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    export default {
        name: 'LogisticsEarlyWarPage',
        components: { cesTable, MyContainer, orderLogPage },
        props: {
            tablekey: {
                type: String,
                default: ''
            }
        },
        data () {
            return {
                that: this,
                tableCols: [],
                filter: {
                    platform: null,
                    shopCode: null,
                    //导入时间
                    importTime: formatTime(new Date(), "YYYY-MM-DD"),
                    //支付时间
                    paytimerange: [],
                    payStartdate: null,
                    payEnddate: null,
                    //计算时间
                    calctimerange: [],
                    calcTimeStart: null,
                    calcTimeEnd: null,
                    //能发时间
                    exptimerange: [],
                    expTimeStart: null,
                    expTimeEnd: null,

                    orderNoInner: null,
                    batchNumber: null,
                    isCalc: null,
                    groupId: null,
                    province: null,
                    city: null,
                    area: null,
                    street: null,
                },
                sendOrderNoInner: "",
                dialogHisVisible: false,
                payTimeSel: "",
                list: [],
                summaryarry: {},
                pager: {},
                platformlist: [],
                grouplist: [],
                shopList: [],
                expresscompanylist: [],
                prosimstatelist: [],//快递站点
                exceptionTypelist: [],
                orderStatuslist: [],
                warehouselist: sendwarehouselist,
                dialogVisible: false,
                total: 0,
                listLoading: false,
                pageLoading: false,
                importLoading: false,
                fileList: []
            }
        },
        async mounted () {
            this.initTableCols();
            await this.initDirectorGroupList();
            await this.setPlatform();
            await this.getOrderStatusListAsync();
            await this.onSearch();
        },
        methods: {
            initTableCols () {
                this.tableCols = [
                    { istrue: true, prop: 'orderNoInner', label: '内部订单号', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogDetail(row) },
                    //{ istrue: true, prop: 'orderNo', label: '线上订单号', width: '100', sortable: 'custom', },
                    { istrue: true, prop: 'groupName', label: '运营组', width: '70', sortable: 'custom', },
                    //{ istrue: true, prop: 'orderStatus', label: '订单状态', width: '80', sortable: 'custom', formatter: (row) => row.orderStatusStr },
                    { istrue: true, prop: 'warehouse', label: '发货仓', width: '70', sortable: 'custom', formatter: (row) => row.warehouseStr },
                    { istrue: true, prop: 'exceptionTypeFlag', label: '异常标记', width: '80', sortable: 'custom', },
                    { istrue: true, prop: 'orderLable', label: '订单标签', width: '120', sortable: 'custom', },
                    { istrue: true, prop: 'platform', label: '平台', width: '60', sortable: 'custom', formatter: (row) => row.platformStr },
                    { istrue: true, prop: 'shopName', label: '店铺', width: '80', sortable: 'custom' },
                    //{ istrue: true, prop: 'distributors', label: '分销商', width: '90', sortable: 'custom', },
                    { istrue: true, prop: 'payTime', label: '支付时间', width: '110', sortable: 'custom', },
                    { istrue: true, prop: 'payedAmount', label: '已付金额', width: '80', sortable: 'custom', },
                    //{ istrue: true, prop: 'orderAmount', label: '商品总成交金额', width: '130', sortable: 'custom', },
                    { istrue: true, prop: 'isCalc', label: '计算', width: '70', sortable: 'custom', formatter: (row) => row.isCalc ? '是' : '否' },
                    { istrue: true, prop: 'calcTime', label: '计算时间', width: '145', sortable: 'custom', formatter: (row) => row.calcTime == null ? null : formatTime(row.calcTime, 'YYYY-MM-DD HH:mm:ss') },
                    { istrue: true, prop: 'expDate', label: '能发日期', width: '90', sortable: 'custom', formatter: (row) => row.expDate == null ? null : formatTime(row.expDate, 'YYYY-MM-DD') },
                    { istrue: true, prop: 'expStr', label: '能发快递', width: '120', sortable: 'custom', },
                    { istrue: true, prop: 'expStopStr', label: '停发快递', width: '120', sortable: 'custom', },
                    { istrue: true, prop: 'expStopDetl', label: '停发依据', width: '80', sortable: 'custom', },
                    //{ istrue: true, prop: 'shippingAddress', label: '省市区街', width: '150', sortable: 'custom', formatter: (row) => `${row.province}-${row.city}-${row.area}-${row.street}` },
                    { istrue: true, prop: 'fullAddress', label: '省市区街', width: '150', sortable: 'custom' },
                    { istrue: true, prop: 'importTime', label: '导入时间', width: '150', sortable: 'custom', }];
            },
            showLogDetail (row) {
                this.dialogHisVisible = true;
                this.sendOrderNoInner = row.orderNoInner;
            },
            async initDirectorGroupList () {
                var res2 = await getDirectorGroupList();
                this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
            },
            //获取订单状态
            async getOrderStatusListAsync () {
                const res = await getOrderStatusListAsync({});
                if (!res?.success) {
                    return;
                }
                const data = res.data;
                this.orderStatuslist = data;
            },
            //时间设置
            payTimeSelChangePick () {
                if (this.filter.paytimerange && this.filter.paytimerange.length > 1) {
                    if (this.filter.paytimerange[1] != formatTime(new Date(), "YYYY-MM-DD")) {
                        this.payTimeSel = "";
                        return;
                    }
                    var d1 = dayjs(this.filter.paytimerange[0]);
                    var d2 = dayjs(this.filter.paytimerange[1]);
                    switch (d2.diff(d1, "day")) {
                        //昨天至今日
                        case 1:
                            this.payTimeSel = "1";
                            break;
                        //近7天
                        case 6:
                            this.payTimeSel = "2";
                            break;
                        //近14天
                        case 13:
                            this.payTimeSel = "3";
                            break;
                        //近30天
                        case 29:
                            this.payTimeSel = "4";
                            break;
                        //默认
                        default:
                            this.payTimeSel = "";
                            break;
                    }
                }
                else {

                    this.payTimeSel = "";
                }

            },
            //付款时间设置默认值
            payTimeSelChange () {
                let oneDayTime = 24 * 60 * 60 * 1000;
                switch (this.payTimeSel) {
                    //昨天至今日
                    case "1":
                        this.filter.paytimerange = [
                            formatTime(new Date(new Date().getTime() - 1 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //近7天
                    case "2":
                        this.filter.paytimerange = [
                            formatTime(new Date(new Date().getTime() - 6 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //近14天
                    case "3":
                        this.filter.paytimerange = [
                            formatTime(new Date(new Date().getTime() - 13 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //近30天
                    case "4":
                        this.filter.paytimerange = [
                            formatTime(new Date(new Date().getTime() - 29 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //默认
                    default:
                        this.filter.paytimerange = [];
                        break;

                }
            },
            //获取物流
            async getExpressComanyList () {
                const res = await getExpressComanyAll({});
                if (!res?.success) {
                    return;
                }
                const data = res.data;
                this.expresscompanylist = data;
            },
            //获取物流站点
            async getprosimstatelist (val) {
                var res = await getExpressComanyStationName({ id: val });
                this.filter.ticketCompanyStation = '';
                if (res?.code) {
                    this.prosimstatelist = res.data
                }
            },
            //设置平台下拉
            async setPlatform () {
                var pfrule = await rulePlatform();
                this.platformlist = pfrule.options;
            },
            //设置店铺下拉
            async onchangeplatform (val) {
                const res = await getshopList({ platform: val, CurrentPage: 1, PageSize: 1000 });
                this.shopList = res.data.list || [];
                this.shopList.push({ shopCode: "{线下}", shopName: "{线下}" });
                this.filter.shopCode = "";
            },
            // //下载导入模板
            // downloadTemplate () {
            //     window.open("../../static/excel/order/.xlsx", "_self");
            // },
            //开始导入
            startImport () {
                this.dialogVisible = true;
            },
            //取消导入
            cancelImport () {
                this.dialogVisible = false;
            },
            //上传成功
            uploadSuccess (response, file, fileList) {
                this.fileList.splice(this.fileList.indexOf(file), 1);
                this.dialogVisible = false;
                this.fileList = [];
            },
            //提交导入
            submitUpload () {
                this.$refs.upload.submit();
            },
            //上传文件
            async uploadFile (item) {
                const form = new FormData();
                form.append("token", this.token);
                form.append("upfile", item.file);
                this.importLoading = true;
                const res = await importCovidStopSendAsync(form);
                this.importLoading = false;
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
            },
            //导出
            async onExport () {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                var res = await exportCovidStopSendDataAsync(params);
                loadingInstance.close();
                if (!res?.data) return
                var fileName = res.headers['content-disposition'].split("=")[2];
                fileName = decodeURIComponent(fileName.split("'")[2]);
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', fileName);
                aLink.click();
            },
            //获取查询条件
            getCondition () {
                if (this.filter.paytimerange && this.filter.paytimerange.length > 1) {
                    this.filter.payStartdate = this.filter.paytimerange[0];
                    this.filter.payEnddate = this.filter.paytimerange[1];
                } else {
                    this.filter.payStartdate = null;
                    this.filter.payEnddate = null;
                }
                if (this.filter.exptimerange && this.filter.exptimerange.length > 1) {
                    this.filter.expTimeStart = this.filter.exptimerange[0];
                    this.filter.expTimeEnd = this.filter.exptimerange[1];
                } else {
                    this.filter.expTimeStart = null;
                    this.filter.expTimeEnd = null;
                }
                if (this.filter.calctimerange && this.filter.calctimerange.length > 1) {
                    this.filter.calcTimeStart = this.filter.calctimerange[0];
                    this.filter.calcTimeEnd = this.filter.calctimerange[1];
                } else {
                    this.filter.calcTimeStart = null;
                    this.filter.calcTimeEnd = null;
                }
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = { ...pager, ...page, ... this.filter }
                return params;
            },
            //查询第一页
            async onSearch () {
                this.$refs.pager.setPage(1)
                await this.getlist()
            },
            //分页查询
            async getlist () {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true
                const res = await getPageCovidStopSendDataAsync(params)
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summary;
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
            },
            //排序查询
            async sortchange (column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderBy = column.prop == "shopName" ? "shopCode" : column.prop;
                    this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
        }
    }
</script>
<style scoped>
    ::v-deep .el-link.el-link--primary {
        margin-right: 7px;
    }
    ::v-deep #app .el-container .is-vertical .main .el-main .el-tabs--top {
        height: 95% !important;
        background-color: red;
    }
</style>
