<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" label-width="100px" label-position="right" :disabled="!formEditMode">
                <el-row>
                    <el-col :span="18">
                        <el-form-item label="商品名称：">
                            <el-input v-model="form.spGoodsName" />                            
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-button type="primary" @click="()=>{ 
                            if(this.skuTableData.length>0){
                                this.$alert('已存在SKU数据！');
                                return;
                            }
                                
                            this.skuTableData.push({id:-(new Date().valueOf()),spSkuName:this.form.spGoodsName,  spSkuCode:form.spGoodsCode}) 
                            }">无SKU 默认为SKU</el-button>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="商品Id：">
                            <el-input v-model="form.spGoodsId" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="商品编码：">
                            <el-input v-model="form.spGoodsCode" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="商品链接：">
                            <el-input v-model="form.spGoodsUrl" />
                        </el-form-item>
                    </el-col>
                </el-row>  
                      
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注：">
                            <el-input type="textarea" v-model="form.remark" clearable maxlength="200" show-word-limit />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="查询状态：">
                            {{fmtJqrNoticeState(form.jqrNoticeState)}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="查询时间：">
                            {{form.jqrLastNoticeTime}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="错误内容：">
                            {{form.jqrErrMsg}}                           
                        </el-form-item>
                    </el-col>
                </el-row>        
                <el-row>
                    <el-col :span="24">
                        <el-tabs>
                            <el-tab-pane label="SKU明细">

                                <div :style="'height:'+ tableHeight+'px;'">
                                    <!--列表-->
                                    <ces-table ref="skuTable" :that='that' :isIndex='false' :hasexpandRight='true' 
                                    :hasexpand='true' :tableData='skuTableData' 
                                    :tableCols='skuTableCols' :loading="false" :isSelectColumn="false"
                                     rowkey="id" :treeprops="{children: 'skuList', hasChildren: true}">
                                        <!-- center from jsonCols array  -->
                                        <template slot="right">
                                            <!-- right  -->
                                            <el-table-column width="100" label="SKU编码" prop="spSkuCode" :sortable="false">
                                                <template slot-scope="scope">
                                                    <el-input v-model="scope.row.spSkuCode" style="width:100%" size="mini" clearable maxlength="20" />
                                                </template>
                                            </el-table-column>
                                            <el-table-column min-width="200" label="SKU名称" prop="spSkuName" :sortable="false">
                                                <template slot-scope="scope">
                                                    <el-input v-model="scope.row.spSkuName" style="width:100%" size="mini" clearable maxlength="100" />
                                                </template>
                                            </el-table-column>
                                            <el-table-column width="100" label="价格" prop="price" :sortable="false">
                                                <template slot-scope="scope">
                                                    <el-input-number v-model.number="scope.row.price" 
                                                        style="width:80px" type="number" 
                                                        :min="0" :max="100000" :precision="2" :controls="false"  size="mini"                                                       
                                                    />        
                                                </template>
                                            </el-table-column>
                                            <el-table-column width="120" label="属性1" prop="spSkuAttr1" :sortable="false">
                                                <template slot-scope="scope">
                                                    <el-input v-model="scope.row.spSkuAttr1" style="width:100%" size="mini" clearable maxlength="40" show-word-limit />
                                                </template>
                                            </el-table-column>
                                            <el-table-column width="100" label="属性2" prop="spSkuAttr2" :sortable="false">
                                                <template slot-scope="scope">
                                                    <el-input v-model="scope.row.spSkuAttr2" style="width:100%" size="mini" clearable maxlength="40" show-word-limit />
                                                </template>
                                            </el-table-column>
                                            <el-table-column width="100" label="属性3" prop="spSkuAttr3" :sortable="false">
                                                <template slot-scope="scope">
                                                    <el-input v-model="scope.row.spSkuAttr3" style="width:100%" size="mini" clearable maxlength="40" show-word-limit />
                                                </template>
                                            </el-table-column>
                                            <el-table-column width="100" label="属性4" prop="spSkuAttr4" :sortable="false">
                                                <template slot-scope="scope">
                                                    <el-input v-model="scope.row.spSkuAttr4" style="width:100%" size="mini" clearable maxlength="40" show-word-limit />
                                                </template>
                                            </el-table-column>

                                            <el-table-column width="110" label="" flexd="right">
                                                <template slot="header">
                                                    <el-button type="primary" @click="skuTableData.push({id:-(new Date().valueOf())})">添加一行</el-button>
                                                </template>
                                                <template slot-scope="scope">
                                                    <el-button type="text" @click="skuTableData.splice(scope.$index, 1)">移除</el-button>
                                                </template>
                                            </el-table-column>

                                        </template>
                                    </ces-table>
                                </div>

                            </el-tab-pane>
                        </el-tabs>
                    </el-col>
                </el-row>

                
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">  
                    <el-button @click="onClose">关闭</el-button>                     
                    <el-button v-if="this.formEditMode" type="primary" @click="onSave(true)">保存</el-button>
                    
                    
                </el-col>
            </el-row>
        </template>

    </my-container>
</template>
<script>  


    import cesTable from "@/components/Table/table.vue";
    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import {
        GetDpSupplierByIdAsync, SaveDpSupplierAsync, GetSupplierGoodsByIdAsync, DelSupplierGoodsAsync, SaveSupplierGoodsAsync
    } from '@/api/order/alllinkDaiPai';

    import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'

    import YhImgUpload from '@/components/upload/yh-img-upload.vue';
    import YhCityselector from '@/components/YhCom/yh-cityselector.vue';


    const skuTableCols = [
    ];

    //机器人查询状态 1成功、0下架、-1失败
    const fmtJqrNoticeState=(v)=>{
        switch(v){
            case 0:return '下架';
            case 1:return '已查询';
            case -1:return '失败';
        }
        return ' ';
    };

    export default {
        name: "SupplierGoodsForm",
        components: { MyContainer, MyConfirmButton, cesTable, YhQuillEditor, YhImgUpload, YhCityselector },
        data() {
            return {              
                that: this,
                form: {
                   
                },
                skuTableCols: skuTableCols,
                total: 0,
                skuTableData: [],
                //summaryarry: {},
                pageLoading: false,
                curRow: null,
                formEditMode: true,//是否编辑模式              

            };
        },
        async mounted() {

        },
        computed: {
            tableHeight() {
                let rowsCount = 1;
                if (this.skuTableData && this.skuTableData.length > 0) {
                    rowsCount = this.skuTableData.length;                    
                }
                let rowsHeight = (rowsCount + 1) * 40 + 40;
                return rowsHeight > 360 ? 360 : rowsHeight;
            },         
            
        },
        methods: {     
            fmtJqrNoticeState:fmtJqrNoticeState,
            onClose(){
                this.$emit('close');
            },  
            async onSave(isClose){
                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData({oid, mode,dpSupplierId}) {
               
                    
                this.pageLoading = true;
                this.formEditMode = mode!=3;
                if (oid && oid > 0) {
                  
                    let rlt = await GetSupplierGoodsByIdAsync( { spGoodsId:oid } );
                    if (rlt && rlt.success) {
                        this.form = rlt.data;
                        this.skuTableData = rlt.data.skuList==null?[]:rlt.data.skuList;                      
                        this.pageLoading = false;
                    }
                } else {
                    Object.keys(this.form).forEach(key => (this.form[key] = null));
                    this.form.enabled=true;
                    this.form.id=0;
                    this.form.dpSupplierId=dpSupplierId;
                    this.form.spMaxPrice=0;
                    this.form.spMinPrice=0;
                    this.skuTableData =[];

                    this.pageLoading = false;                    
                }
            },
            async save() {
                this.pageLoading = true;

                let saveData = { ...this.form };
                
                saveData.skuList = [...this.skuTableData];

                let rlt = await SaveSupplierGoodsAsync(saveData);
                if (rlt && rlt.success) {
                    this.$message.success('保存成功！');           
                }

                this.pageLoading = false;
              
                return (rlt && rlt.success);
            }
        },
    };
</script>
