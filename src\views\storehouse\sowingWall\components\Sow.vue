<template>
    <MyContainer>
      <template #header>
        <el-form :inline="true" ref="topForm" :model="queryEnum" class="demo-ruleForm">
          <el-form-item prop="seed_status">
            <el-select v-model="queryEnum.seed_status" placeholder="请选择播种状态" clearable>
              <el-option v-for="item in statuOption" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="sow_channel_id">
            <el-select v-model="queryEnum.sow_channel_id" placeholder="请选择播种通道" clearable>
              <el-option v-for="item in sowingChannelList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="sower">
            <el-input v-model="queryEnum.sower" placeholder="播种人" clearable style="width: 150px">
            </el-input>
          </el-form-item>
          <el-form-item prop="wave_id">
            <el-input v-model="queryEnum.wave_id" placeholder="批次号" clearable style="width: 150px">
            </el-input>
          </el-form-item>
          <el-form-item prop="timeRanges">
            <el-date-picker v-model="queryEnum.timeRanges" type="daterange" unlink-panels range-separator="至"
              start-placeholder="开始开播日期" end-placeholder="结束开播日期" style="width: 250px;margin-right: 5px;"
              :clearable="true" :value-format="'yyyy-MM-dd'" @change="changeTime">
            </el-date-picker>
          </el-form-item>
          <!-- <el-form-item prop="goDownName">
                  <el-input  v-model="queryEnum.goDownName" placeholder="仓库" clearable style="width: 150px" >
                  </el-input>
              </el-form-item> -->
          <el-form-item>
            <el-button type="primary" @click="getList('search')">搜索</el-button>
            <el-button type="primary" v-if="checkPermission('api:wmsoperation:Sow:SyncSnowData')" @click="addWaveId">同步</el-button>
          </el-form-item>
  
        </el-form>
      </template>
  
      <template>
        <vxetablebase :id="'sow20240913'" :tablekey="'sow20240913'" :tableData='tableData' :tableCols='tableCols'
          @sortchange='sortchange' :loading='loading' :border='true' :that="that" ref="vxetable" :showsummary='true'
          :summaryarry="summaryarry">
          <template #wave_id>
            <vxe-column field="wave_id" title="批次号" width="150" :sortable="true"> 
            </vxe-column>
          </template>
        </vxetablebase>
        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
          v-dialogDrag>
          <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" style="z-index:10000;height:600px" />
        </el-dialog>
      </template>
      <el-dialog title="播种订单" v-if="orderListVisible" :visible.sync="orderListVisible" width="70%" height="600px"
        v-dialogDrag>
        <vxetablebase :id="'orderList20240913'" :tablekey="'orderList20240913'" :tableData='orderList'
          :tableCols='orderListTableCols' @sortchange='sortchange' :loading='orderListLoading' :border='true' :that="that"
          ref="orderListVxetable" />
        <my-pagination ref="pageLog" :total="totalOrderList" @get-page="getOrderList()" />
      </el-dialog>
  
      <template #footer>
        <my-pagination ref="pager" :total="total" @get-page="getList" />
      </template>
  
  
      <el-dialog title="同步批次号" :visible.sync="addWaveIddialogVisibleSyj" width="20%" :close-on-click-modal=false
        v-dialogDrag>
  
        <el-form ref="" @submit.native.prevent label-width="100px" label-position="right">
          <el-form-item prop="wave_id" label="批次号">
            <el-input  clearable v-model="wave_id" style="width: 80%" :maxlength="100"></el-input>
          </el-form-item>
          <el-form-item prop="isBigBatch" label="是否大批次">
            <el-switch v-model="isBigBatch" />
          </el-form-item>
          <el-form-item prop="sowChannelId" label="播种通道">
            <el-select
              v-model="sowChannelId"
              clearable
              placeholder="请选择"
              filterable
              style="width: 80%"
            >
              <el-option
                v-for="item in sowingChannelList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
          
  
        <span slot="footer" class="dialog-footer">
          <el-button @click="addWaveIddialogVisibleSyj = false">关闭</el-button>
          <el-button type="primary" @click="addWave">确定</el-button>
        </span>
        
      </el-dialog>
  
      <el-dialog title="商品编码" :visible.sync="goodCodeShow" top="80px"  @close="closeDialog" width="1200px" :close-on-click-modal=false
        v-dialogDrag>
  
        <el-row >
          <el-col :span="4">
            <el-radio-group v-model="radio" @input="inputRadio">
                <el-radio-button  label="拣货"></el-radio-button>
                <el-radio-button  label="播种"></el-radio-button>
            </el-radio-group>
          </el-col>
          <el-col :span="8">
                  <el-button @click="clickGoodCode">批量打印商品二维码</el-button>
                  <el-button @click="printKdCode">打印快递条码</el-button>
          </el-col>
          <!-- <el-col :span="18">
              手动调整打印的每行间隙 <el-input style="width: 100px" v-model="marginTopnum"></el-input>px
          </el-col> -->
        </el-row>
        <el-row >
          <el-form ref="form" :inline="true" :model="filterdata"  style="margin-top: 10px;" >
            
            <el-form-item  prop="timeRanges">
              <el-date-picker v-model="filterdata.timeRanges" type="daterange" unlink-panels range-separator="至"
              :start-placeholder="radio=='拣货'? '开拣日期':'开播日期'" :end-placeholder="radio=='拣货'? '结拣日期':'结播日期'" style="width: 250px;margin-right: 5px;"
              :clearable="true" :value-format="'yyyy-MM-dd'" @change="changeTime">
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="bin">
              <el-input v-if="radio == '拣货'" v-model="filterdata.bin" placeholder="仓位" clearable style="width: 150px;"></el-input>
            </el-form-item>
            <el-form-item prop="sku_id">
              <el-input v-model="filterdata.sku_id" placeholder="商品编码"  clearable style="width: 150px;"></el-input>
            </el-form-item>
            <el-form-item prop="sku_name">
              <el-input v-model="filterdata.sku_name" placeholder="商品名称"  clearable style="width: 150px;"></el-input>
            </el-form-item>
            <el-form-item v-if="radio == '拣货'" prop="picker">
              <el-input v-model="filterdata.picker" placeholder="拣货人"  clearable style="width: 150px;"></el-input>
            </el-form-item>
            <el-form-item v-else prop="sower">
              <el-input v-model="filterdata.sower" placeholder="播种人" clearable style="width: 150px;"></el-input>
            </el-form-item>
             <el-form-item v-if="radio == '拣货'&&storeList.length>0" prop="wms_co_id">
                <el-select
                v-model="filterdata.wms_co_id"
                filterable
                placeholder="请选择仓库"
                style="width: 150px;"
                clearable
                >
                <el-option
                  v-for="item in storeList"
                  :key="item.wms_co_id"
                  :label="item.name"
                  :value="item.wms_co_id">
                </el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item v-if="radio == '拣货'">
              <el-input v-model="filterdata.remark" placeholder="备注"  clearable style="width: 150px;"></el-input>
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" @click="getList1('search')">搜索</el-button>
            </el-form-item>
          </el-form>
        </el-row>

        <div style="display:flex; flex-direction: column; width: 100%; justify-content: center; align-items: center;"  id="printCodeid" ref="printCoderef" v-if="codeshow">
          <div v-for="(item,index) in tableData2" :key="index" >
              <!-- <div style="width: 100%; background: red;" :style="{height:(1)+'px'}" v-show="index>0"></div> -->
              <!-- border: 1px solid red; -->
              <div  style="display:flex; flex-direction: row; background: white;  padding-left: 5px; width: 370px; " :style="index>0 ?{height: '138px'}:{height: '138px'}" >
                  
                  <div style="">
                      <canvas :id="'QRCode'+index"></canvas>
                  </div>
                  <div style="display:flex; flex-direction: column; z-index: 999; height: 130px;">
                      <div style="font-size: 14px; width: 230px; word-break: break-all; overflow-wrap: break-word; "> {{
                          item.name.length > 30
                          ? item.name.slice(0, 30) + '...'
                          : item.name.padEnd(30, '-')
                      }} </div>
                  <div style="width: 100%; display: flex; flex-direction: row; white-space: normal"><span style="font-size: 20px; font-weight: 600; "> {{item.qty}} </span><span style="margin-left: auto;margin-right: 20px;">{{item.wave_id}}</span></div>
                  <span style="font-size: 14px; ">{{item.sku_id}}</span>
                  <span style="font-size: 20px;font-weight: 600; ">{{item.bin}}</span>
                  <span style="font-size: 14px;">{{item.idxs}}</span>
                  
                  </div>
                  
              </div>
              <!-- index*0.15+ -->
              
          </div>
        </div>
        <div style="display:flex; flex-direction: column; width: 100%; justify-content: center; align-items: center;"  id="printKdCodeid" ref="printCoderef" v-if="codeshow2">
          <div v-for="(item,index) in tableData3" :key="index" >
              <!-- <div style="width: 100%; background: red;" :style="{height:(1)+'px'}" v-show="index>0"></div> -->
              <!-- border: 1px solid red; -->
              <div  style="display:flex; flex-direction: column; background: red; background: white;  padding-left: 5px; width: 370px; " :style="index>0 ?{height: '138px'}:{height: '138px'}" >
                  
                  <div style="width: 100%; display: flex; flex-direction: row; justify-content: center;">
                      <canvas :id="'QRKdCode'+index"></canvas>
                  </div>
                  <div style="display: flex; flex-direction: row;">
                      <div style="width: 50%; font-size: 24px; font-weight: 600; margin-left: 40px;">{{item.idx}}</div>
                      <div style="width: 50%; text-align: right; margin-right: 40px;">{{item.wave_id}}</div>
                  </div>
                  <!-- <div style="display:flex; flex-direction: column; z-index: 999; height: 130px;">
                      <div style="font-size: 14px; width: 230px; word-break: break-all; overflow-wrap: break-word; "> {{
                          item.name.length > 30
                          ? item.name.slice(0, 30) + '...'
                          : item.name.padEnd(30, '-')
                      }} </div>
                  <div style="width: 100%; display: flex; flex-direction: row; white-space: normal"><span style="font-size: 20px; font-weight: 600; "> {{item.qty}} </span><span style="margin-left: auto;margin-right: 20px;">{{item.wave_id}}</span></div>
                  <span style="font-size: 14px; ">{{item.sku_id}}</span>
                  <span style="font-size: 20px;font-weight: 600; ">{{item.bin}}</span>
                  <span style="font-size: 14px;">{{item.idxs}}</span> -->
                  
                  </div>
                  
              </div>
              <!-- index*0.15+ -->
              
          </div>
        <!-- </div> -->
        <!-- <vxe-table
          :data="tableData1" :loading="dialoading">
          <vxe-column field="bin" title="仓位"></vxe-column>
          <vxe-column field="sku_id" title="商品编码"></vxe-column>
          <vxe-column field="qty" title="数量" width="100"></vxe-column>
          <vxe-column field="name" title="商品名称"></vxe-column>
        </vxe-table> -->
        <vxetablebase :id="'pick20241113'" v-show="radio == '拣货'" :tablekey="'pick20241113'" :tableData='tableData1' :tableCols='tableCols1'
          :showsummary='true' :summaryarry="codesummary"  @sortchange='sortchange1' :loading='dialoading' :border='true' height="440px" :that="that" ref="vxetable1" 
          >
        </vxetablebase>
        <vxetablebase :id="'sow20241113'" v-show="radio == '播种'" :tablekey="'sow20241113'" :tableData='tableData1' :tableCols='tableCols2'
          :showsummary='true' :summaryarry="codesummary"  @sortchange='sortchange1' :loading='dialoading' :border='true' height="440px" :that="that" ref="vxetable2" 
          >
        </vxetablebase>
        <my-pagination ref="pager1" :total="total1" :pageSize="10" :sizes="[10, 50,100,300]" @get-page="getList1" />
  
        <span slot="footer" class="dialog-footer">
          <el-button @click="goodCodeShow = false">关闭</el-button>
        </span>
        <!-- <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="addWave">确定</el-button>
        </span> -->
      </el-dialog>
      <!-- <div style="position">打印中</div> -->
       <!-- 订单数  订单 -->
      <el-dialog title="订单" :visible.sync="orderNumVisible" top="80px" width="50%" :close-on-click-modal=false
        v-dialogDrag>
       
        <vxetablebase :id="'order20241124'" :tablekey="'order20241124'" :tableData='orderNumTableData' :tableCols='orderNumtableCols'
          @sortchange='orderNumSortchange' :loading='orderNumLodin' :border='true' height="440px" :that="that" ref="orderNumVxetable" 
          >
          <template #innerorder>
            <vxe-column field="innerorder" width="150" title="内部订单号" :sortable="true">
             
            </vxe-column>
          </template>
        </vxetablebase>
        <!-- <my-pagination ref="orderNumPage" :total="orderNumTotal" :pageSize="10" :sizes="[10, 50,100,300]" @get-page="getOrderNumTableData" /> -->
  
        <span slot="footer" class="dialog-footer">
          <el-button @click="orderNumVisible = false">关闭</el-button>
        </span>
      </el-dialog>

      <el-dialog title="录入拣货" :visible.sync="enterPickVisible" top="80px" custom-class="enterDlog" center width="400px" :close-on-click-modal=false
        v-dialogDrag>
        <el-form ref="enterForm" :model="enterForm" style="width: 300px;" label-width="80px" >
          <el-form-item label="拣货人"  prop="person"
          :rules="[
            { required: true, message: '请输入拣货人', trigger: 'blur' },
          ]"
          >
            <el-select
              v-model="enterForm.person"
              filterable
              remote
              reserve-keyword
              placeholder="请输入关键词"
              :remote-method="enterRemoteMethod"
              value-key="value"
              :loading="enterSLoading">
              <el-option
                v-for="item in personList"
                :key="item.value"
                :label="item.label+'-'+item.title"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="拣货通道"  prop="passageway"
          :rules="[
            { required: true, message: '请输入拣货通道', trigger: 'blur' },
          ]"
          >
            <el-select v-model="enterForm.passageway"  multiple placeholder="请选择">
              <el-option
                v-for="item in passagewayList"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      
  
        <span slot="footer" class="dialog-footer">
          <el-button @click="enterPickVisible = false">关 闭</el-button>
          <el-button type="primary" @click="enterPickSave" :loading="enterSaveLoding">确 认</el-button>
        </span>
      </el-dialog>
      <el-dialog title="选择仓库" @close="closePick"  :visible.sync="pickVisible" top="80px" custom-class="enterDlog" center width="400px" :close-on-click-modal=false
        v-dialogDrag>
        <el-form ref="pickForm" :model="pickForm" style="width: 300px;" label-width="80px"  @submit.prevent >
          <el-form-item label="仓库"  prop="wms_co_id"
          :rules="[
            { required: true, message: '请选择仓库', trigger: 'blur' },
          ]"
          >
         
            <el-select
              v-model="pickForm.wms_co_id"
              filterable
              placeholder="请选择仓库"
              clearable
              >
              <el-option
                v-for="item in storeList"
                :key="item.wms_co_id"
                :label="item.name"
                :value="item.wms_co_id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      
  
        <span slot="footer" class="dialog-footer">
          <el-button @click="pickVisible = false">关 闭</el-button>
          <el-button type="primary" @click="printCode" :loading="pickLoding">确 认</el-button>
        </span>
      </el-dialog>

      
    </MyContainer>
  </template>
  
  <script>
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import MyContainer from "@/components/my-container";
  import Print from 'print-js'
  import dayjs from 'dayjs';
  import { ComputeKMeansOrder,QuerySowList, QueryBatchPickList,FinishSow,DeleteSowBatch,QueryBatchSowList,StartSow, syncSnowData, PrintPickDetail, 
    QueryPickDetailList ,QueryOrderBinsDetail,BatchUpdatePicker, PrintExpressBarCodes,GetSowingChannel,GetInventorys} from '@/api/wmsoperation/sow.js'
  // import {  getUserListPage } from '@/api/admin/user'
  import { QueryAllDDUserTop100 } from '@/api/admin/deptuser';
  import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
  import QRCode from "qrcode";
  import { Loading } from 'element-ui';
  import JsBarcode from 'jsbarcode'
  const statuOption = [{
    value: '0',
    label: '待播种'
  }, {
    value: '1',
    label: '播种中'
  }, {
    value: '2',
    label: '播种完'
  }, {
    value: '3',
    label: '待拣货'
  }, {
    value: '4',
    label: '拣货中'
  }]
  
  const tableCols = [ 
  
    { istrue: true, sortable: 'custom', prop: 'wave_id', label: '批次号', width: 'auto',type:'slot' },
    { istrue: true, sortable: 'custom', prop: 'sow_channel_id', label: '播种通道', width: '80',formatter: (row,that) =>   that.formatChanneInfo(row.sow_channel_id)  },
    { istrue: true, sortable: 'custom', prop: 'order_qty', label: '订单数', width: '60px',  type: 'click', handle: (that, row) => that.showOrderNum(row) },
    { istrue: true, sortable: 'custom', prop: 'sku_type_qty', label: '编码数', width: '60px',  type: 'click', handle: (that, row) => that.showOrderList(row) },
    { istrue: true, sortable: 'custom', prop: 'sku_qty', label: '商品数', width: '60px', },
    { istrue: true, sortable: 'custom', prop: 'bin_count', label: '播种柜数', width: '80px', },
    { istrue: true, sortable: 'custom', prop: 'seed_status', label: '播种状态', width: '80px', formatter: (row) => { return statuOption[row.seed_status].label } },
    // { istrue: true, sortable: 'custom', prop: 'status', label: '聚水潭状态', width: '130px', },
    { istrue: true, sortable: 'custom', prop: 'picker', label: '拣货人', width: '130px', },
    { istrue: true, sortable: 'custom', prop: 'pick_starttime', label: '开拣时间', width: '125px', },
    { istrue: true, sortable: 'custom', prop: 'pick_endtime', label: '完拣时间', width: '125px', },
    { istrue: true, sortable: 'custom', prop: 'sower', label: '播种人', width: '130px', },
    { istrue: true, sortable: 'custom', prop: 'sow_starttime', label: '开播时间', width: '125px', },
    { istrue: true, sortable: 'custom', prop: 'sow_endtime', label: '完播时间', width: '125px', },
    { istrue: true, sortable: 'custom', prop: 'pick_times', label: '拣货时长(分钟)', width: '110px', },
    { istrue: true, sortable: 'custom', prop: 'sow_times', label: '播种时长(分钟)', width: '110px', },
    { istrue: true, sortable: 'custom', prop: 'remark', label: '备注', width: '110px', },
    // { istrue: true, sortable: 'custom', prop: 'batch_pretimes', label: '等待时长(分钟)', width: '130px', },
    { 
      type: 'button', label: '操作', width: '180px', btnList: [
        { label: "补位",ishide: (that, row) => { return true }, },
        { label: "聚集订单",ishide: (that, row) => { return row.seed_status != 3 }, handle: (that, row) => that.handleClusterOrder(row) },

        { label: "拣货",ishide: (that, row) => { return row.seed_status != 3 && row.seed_status != 4}, handle: (that, row) => that.enterPickOpen(row) },
        { label: "完成",/*display:(row)=>{return true},display:true,*/permission:'api:wmsoperation:Sow:FinishSow', ishide: (that, row) => { return row.seed_status != 1 }, handle: (that, row) => that.finish(row) },


        // { label: "开始播种",/*display:(row)=>{return true},display:true,*/ permission:'api:wmsoperation:sow:StartSow',ishide: (that, row) => { return row.seed_status != 0 }, handle: (that, row) => that.sowing(row) },
        { label: "删除",ishide: (that, row) => { return row.seed_status != 3 },  handle: (that, row) => that.onDeleteOperation(row) },

      ]
    },
  ];
  
  
  const orderListTableCols = [
  
    { istrue: true, sortable: 'custom', prop: 'shipout_edison_sellable', label: '订单号', width: '80px', formatter: (row) => !row.orderNo ? " " : row.orderNo, type: 'click', handle: (that, row) => that.showLogDetail(row) },
  ];
  
  const pickLogTableCols = [
    { istrue: true, sortable: 'custom', prop: 'wms_co_id', label: '仓库', width: '90', formatter: (row,that)=> that.formatStoreInfo(row.wms_co_id) },
    { istrue: true, sortable: 'custom', prop: 'bin', label: '仓位', width: '90' },
    { istrue: true, sortable: 'custom', prop: 'sku_id', label: '商品编码', width: '120' },
    { istrue: true, sortable: 'custom', prop: 'name', label: '商品名称', width: 'auto' },
    { istrue: true, sortable: 'custom', prop: 'order_qty', label: '订单数量', width: '60' },
    { istrue: true, sortable: 'custom', prop: 'qty', label: '数量', width: '50' },
    { istrue: true, sortable: 'custom', prop: 'picker', label: '拣货人', width: '60' },
    { istrue: true, sortable: 'custom', prop: 'pick_starttime', label: '开拣时间', width: '120' },
    { istrue: true, sortable: 'custom', prop: 'pick_endtime', label: '完拣时间', width: '120' },
    { istrue: true, sortable: 'custom', prop: 'picktimes', label: '拣货时长(秒)', width: '95' },
    { istrue: true, sortable: 'custom', prop: 'pretimes', label: '等待时长(秒)', width: '100' },
    { istrue: true, sortable: 'custom', prop: 'remark', label: '备注', width: '60' },
  ]

  const sowLogTableCols = [
    // { istrue: true, sortable: 'custom', prop: 'bin', label: '仓位', width: '90' },
    { istrue: true, sortable: 'custom', prop: 'idxs', label: '播种柜', width: '90' },
    { istrue: true, sortable: 'custom', prop: 'sku_id', label: '商品编码', width: '120' },
    { istrue: true, sortable: 'custom', prop: 'name', label: '商品名称', width: 'auto' },
    { istrue: true, sortable: 'custom', prop: 'order_qty', label: '订单数量', width: '80' },
    { istrue: true, sortable: 'custom', prop: 'qty', label: '数量', width: '50' },
    { istrue: true, sortable: 'custom', prop: 'sower', label: '播种人', width: '60' },
    { istrue: true, sortable: 'custom', prop: 'seed_starttime', label: '开播时间', width: '120' },
    { istrue: true, sortable: 'custom', prop: 'seed_endtime', label: '完播时间', width: '120' },
    { istrue: true, sortable: 'custom', prop: 'seedtimes', label: '播种时长(秒)', width: '100' },
    { istrue: true, sortable: 'custom', prop: 'pretimes', label: '等待时长(秒)', width: '100' },
  ]

  const orderNumtableCols = [
    { istrue: true, sortable: 'custom', prop: 'innerorder', label: '内部订单号', width: '200',type:'slot' },
    { istrue: true, sortable: 'custom', prop: 'sku_qty', label: '编码数', width: '90' },
    { istrue: true, sortable: 'custom', prop: 'qty', label: '商品数', width: '90' },
    { istrue: true, sortable: 'custom', prop: 'bins', label: '仓位', width: 'auto' },

  ]

  export default {
    name: 'sow',
    components: { vxetablebase, MyContainer, OrderActionsByInnerNos },
    data() {
      return {
        that: this,
        statuOption: statuOption,
        goodCodeShow: false,
        radio:'拣货',
        queryEnum: {
          timeRanges: [],
          sower: '',
          seed_status: '',
          wave_id: '',
          orderBy: '',
          isAsc: true,
        },
        marginTopnum: 200,
  
        tableData: [
        ],
        tableData1: [
        ],
        tableData2: [],
        tableData3: [],

        tableCols: tableCols,
        loading: false,
        dialoading: false,
        codeshow: false,
        codeshow2: false,

        summaryarry: {},
        total: 0,
        total1: 0,
        //订单号dialog
        orderNo: null,
        orderListVisible: false,
        orderListTableCols: orderListTableCols,
        orderList: [],
        orderListLoading: false,
        totalOrderList: 0,
        dialogHisVisible: false,
        addWaveIddialogVisibleSyj: false,
        //同步批次号，播种通道列表
        sowingChannelList:[],
        wave_id: null,
        isBigBatch:false,
        sowChannelId:null,
        filterdata: {
          orderBy: '',
          isAsc: true,
        },
        tableCols1:pickLogTableCols,
        tableCols2:sowLogTableCols,
        codesummary:{},
        // addForm: {
        //   wave_id:1
        // },

        //订单数 订单
        orderNumVisible:false,
        orderNumTableData:[],
        orderNumtableCols:orderNumtableCols,
        orderNumLodin:false,
        orderNumTotal:null,
        orderNumQuery:{},

        //录入拣货
        enterPickVisible:false,
        enterForm:{
        },
        enterSLoading:false,
        enterSaveLoding:false,
        personList:[],
        passagewayList:[
          'T-','H-','C-'
        ],
        enterCurrentRow:{},
        
        //选择仓库
        pickVisible:false,
        pickForm:{
        },
        pickLoding:false,
        storeList:[],
      };
    },
    async mounted() {
      const { data } = await GetSowingChannel()
      this.sowingChannelList = data
      await this.getList()
  
    },
    methods: {
      closeDialog( ){
        this.$refs.form.resetFields();
        console.log(this.filterdata,)
        this.goodCodeShow = false;
      },
       formatChanneInfo(value) {
        let info = ' '
        this.sowingChannelList.forEach(item => { if (item.id === value) info = item.name })
        return info
      },
      formatStoreInfo(value) {
        let info = ' '
        this.storeList.forEach(item => { if (item.wms_co_id === value) info = item.name })
        return info
      },
      async clickGoodCode() {
        if(this.storeList.length>0){this.pickVisible = true}else{this.printCode()}
      },
      async printCode(){
        let flag = true
       if(this.pickVisible){
          await this.$refs.pickForm.validate(async (valid) => {
            if (!valid) {
             
              flag = false
            }
          })
       }
        if (flag) {
            let _this = this;
            let loadingInstance = Loading.service({
                background: 'white',
                fullscreen: true
            });
          await this.getList2();
          if(this.pickVisible)this.closePick();
          this.codeshow = true;
  
          this.$nextTick(()=>{
  
              this.tableData2.forEach((item,index)=>{
                  const element = document.getElementById("QRCode"+index);
                  const options = {
                      width: 128,
                  };
  
                  const url = item.sku_id;
                  QRCode.toCanvas(element, url, options);
              })
  
              setTimeout(()=>{
                  Print({
                  printable: "printCodeid",
                  type: "html",
                  scanStyles: false,
                  targetStyles: ["*"],
                  // style:
                  //     "table {border-collapse: collapse; width: 100%;} th, td {border: 1px solid #dfe6ec; padding: 4px 0;}", // 可选-打印时去掉眉页眉尾
                  
                  onCancel: function() {
                      console.log('');
                  },
                  onPrint: function() {
                      console.log('');
                  }},)
                  setTimeout(function() {
                      _this.tableData2 = [];
                      _this.codeshow = false;
                      loadingInstance.close();
                      _this.$forceUpdate();
                  }, 2000);
              },3000)
  
          })
  
          
          
          } else {
            return false
          }
          // this.$nextTick(() => {
          //     loadingInstance.close();
          // });
      },
      closePick(){
        this.$refs.pickForm.resetFields();
        this.pickVisible = false;
      },
      async printKdCode(){
          let _this = this;
          let loadingInstance = Loading.service({
              background: 'white',
              fullscreen: true
          });
          await this.getList3();
  
          this.codeshow2 = true;
  
          this.$nextTick(()=>{
  
              this.tableData3.forEach((item,index)=>{
                  const element = document.getElementById("QRKdCode"+index);
                  element.style.height = '90px'
                //   const options = {
                //       width: 128,
                //   };
  
                //   const url = item.expressCode;
                //   QRCode.toCanvas(element, url, options);

                  JsBarcode(element, item.expressCode, {
                        displayValue: false // 是否在条形码下方显示文字
                  })
              })
  
              setTimeout(()=>{
                  Print({
                  printable: "printKdCodeid",
                  type: "html",
                  scanStyles: false,
                  targetStyles: ["*"],
                  // style:
                  //     "table {border-collapse: collapse; width: 100%;} th, td {border: 1px solid #dfe6ec; padding: 4px 0;}", // 可选-打印时去掉眉页眉尾
                  
                  onCancel: function() {
                      console.log('');
                  },
                  onPrint: function() {
                      console.log('');
                  }},)
                  setTimeout(function() {
                      _this.tableData3 = [];
                      _this.codeshow2 = false;
                      loadingInstance.close();
                      _this.$forceUpdate();
                  }, 2000);
              },3000)
  
          })
  
          
          
  
          
  
          // this.$nextTick(() => {
          //     loadingInstance.close();
          // });
      },
      changeTime(e) {
        if(this.goodCodeShow){
          this.filterdata.starttime = e ? e[0] : null
          this.filterdata.endtime = e ? e[1] : null
        }else{
          this.queryEnum.begin_sow_starttime = e ? e[0] : null
          this.queryEnum.end_sow_starttime = e ? e[1] : null
        }
        
      },
      async getList1(type) {
        if (type == 'search') {
          this.$refs.pager1.setPage(1)
        }
        let pager = this.$refs.pager1.getPager()
  
        let params = {
          ...pager,
          ...this.filterdata
        }
        this.dialoading = true
        let res
        if(this.radio == '拣货'){
          res = await QueryBatchPickList(params)
        }else{
          res = await QueryBatchSowList(params)
        }
        // const { list, total, summary } = await QueryPickDetailList(params)
        this.dialoading = false
        this.tableData1 = res.list
        this.total1 = res.total
        this.codesummary = res.summary
      },
      async getList2() {
        let params = {
          wave_id: this.filterdata.wave_id,
          wms_co_id:this.pickForm.wms_co_id
        }
        this.dialoading = true
        const { data, total, summary } = await PrintPickDetail(params)
        this.dialoading = false
        this.tableData2 = data
      },
      async getList3() {
        let params = {
          wave_id: this.filterdata.wave_id
        }
        this.dialoading = true
        const { data, total, summary } = await PrintExpressBarCodes(params)
        this.dialoading = false
        if(!data){
            this.tableData3 = []
            return;
        }
        this.tableData3 = data
      },
      async getList(type) {
        if (type == 'search') {
          this.$refs.pager.setPage(1)
        }
        let pager = this.$refs.pager.getPager()
  
        let params = {
          ...pager,
          ...this.queryEnum
  
        }
        this.loading = true
        const { list, total, summary } = await QuerySowList(params)
        this.loading = false
        this.tableData = list
        this.total = total
        this.summaryarry = summary
      },
      sortchange({ order, prop }) {
        if (prop) {
          this.queryEnum.orderBy = prop
          this.queryEnum.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
      sortchange1({ order, prop }) {
        if (prop) {
          this.filterdata.orderBy = prop
          this.filterdata.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList1()
        }
      },
      async showOrderList (row) {
          this.goodCodeShow = true;
          this.filterdata.wave_id = row.wave_id;
          let res = await GetInventorys({wave_id:row.wave_id})
          this.storeList = res.data
          this.$nextTick(()=>{
              this.getList1('search');
          })
      },
      showLogDetail(row) {
        this.dialogHisVisible = true;
        this.orderNo = row.orderNo;
      },
      getOrderList() {
  
      },

      
      async sowing(row) {
        const data = await StartSow({ wave_id: row.wave_id })
        if (data.isSuccess) {
          this.$message({
            message: "操作成功",
            type: "success"
          });
          await this.getList()
        } else {
          this.$message({
            message: data.message,
            type: "error"
          });
        }
      },
      //完成播种
      async finish(row) {
        this.$confirm('确认要完成吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const data = await FinishSow({ wave_id: row.wave_id })
          if (data?.isSuccess) {
            this.$message({
              message: "操作成功",
              type: "success"
            });
            await this.getList()
          }else{
              this.$message({
                message: data.message,
                type: "error"
              });
          }
        }).catch(() => {
        });
      },
      onsummaryClick() {
  
      },
      async addWaveId() {
        
        if(this.sowingChannelList.length>0){
          this.sowChannelId = this.sowingChannelList[0].id
        }
        this.addWaveIddialogVisibleSyj = true
      },
  
      async addWave() {
        // 检查批次号是否为空
        if (!this.wave_id.toString()) {
          this.$message.error('请输入批次号');
          return; // 如果批次号为空，直接返回，不执行后续操作
        }
        // 检查播种通道是否为空
        if (!this.sowChannelId) {
          this.$message.error('播种通道不能为空');
          return; // 如果播种通道，直接返回，不执行后续操作
        }
        try {
          var res = await syncSnowData({ wave_id: Number(this.wave_id) ,isBigBatch:this.isBigBatch,sowChannelId:this.sowChannelId});
          if (res?.isSuccess) {
            this.$message({ message: '已同步', type: "success" });
            this.wave_id = null;
            this.addWaveIddialogVisibleSyj = false;
            this.getList();
          } else {
            this.$message.error(res.message);
          }
        } catch (error) {
          this.$message.error(error);
          console.error(error);
        }
      },
      //订单数 订单
      showOrderNum(row) {
          this.orderNumVisible = true;
          this.orderNumQuery.wave_id = row.wave_id;
          this.$nextTick(()=>{
              this.getOrderNumTableData('search');
          })
      },
      orderNumSortchange({ order, prop }) {
        if (prop) {
          this.orderNumQuery.orderBy = prop
          this.orderNumQuery.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getOrderNumTableData()
        }
      },
      async getOrderNumTableData(type){
        // if (type == 'search') {
        //   this.$refs.orderNumPage.setPage(1)
        // }
        // let pager = this.$refs.orderNumPage.getPager()
  
        let params = {
          // ...pager,
          ...this.orderNumQuery
  
        }
        this.orderNumLodin = true
        const list = await QueryOrderBinsDetail(params)
        this.orderNumLodin = false
        this.orderNumTableData = list
        // this.orderNumTotal = total
        // this.summaryarry = summary
      },

      //录入拣货
      //接口查找
      async enterRemoteMethod(query){
        if (query !== '') {
            this.enterSLoading = true;
            // var dynamicFilter = { field: 'nickName', operator: 'Contains', value: query }
            this.personList = []
            const res = await QueryAllDDUserTop100({ keywords: query })
            res?.data?.forEach(f => {
                this.personList.push({ value: f.ddUserId, label: f.userName,title:f.position})
            })
            this.enterSLoading = false;
          } else {
            this.personList = [];
          }
      },
      enterPickOpen(row){
        this.enterPickVisible = true
        this.enterCurrentRow = row
        //通道下拉接口获取

      },
      async enterPickSave(){
        this.$refs.enterForm.validate(async(result)=>{
          if(result){
            this.enterSaveLoding = true
            //调接口
            let params = {
              picker:this.enterForm.person.label,
              picker_id:this.enterForm.person.value,
              bin_prefix:this.enterForm.passageway,
              wave_id:this.enterCurrentRow.wave_id,
              
            }
            let res  = await BatchUpdatePicker(params)
            if(res.isSuccess){
              this.$message({ message: res.message,type: "success"})
              this.$refs.enterForm.resetFields()
              this.enterPickVisible = false
              this.getList()
            }else{
              this.$message({ message: res.message,type: "error"})
            }
            this.enterSaveLoding = false
          }
        })
      },
      async onDeleteOperation(row) {
        this.$confirm('是否删除该条数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          var res = await DeleteSowBatch({ wawave_id: row.wave_id})
          if (res?.isSuccess) {
            this.$message({ message: "删除成功", type: "success" });
            await this.getList('search')
          }
        }).catch(() => {
        });
      },
      inputRadio(val){
        this.$refs.vxetable1.$refs.xTable.clearSort()
        this.$refs.vxetable2.$refs.xTable.clearSort()
        this.filterdata = {
          orderBy: '',
          isAsc: true,
          wave_id:this.filterdata.wave_id
        }
        this.getList1('search')
      },
      handleClusterOrder(row){
        this.$confirm('确定聚集订单吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          var res = await ComputeKMeansOrder({ wawave_id: row.wave_id})
          console.log(res,'聚集订单')
          if (res?.isSuccess) {
            this.$message({ message: "操作成功", type: "success" });
            await this.getList('search')
          }else{
            this.$message({ message: res.message, type: "error" });
          }
        }).catch(() => {
        });
      },
    }
  };
  </script>
  
  <style lang="scss" scoped>
  .hang {
      overflow: hidden;
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;
      display: -webkit-box;
      white-space: normal;
      -webkit-box-orient: vertical;
  }
  .qrcode-container canvas {
      display: block;
      margin: 0 !important;
      padding: 0 !important;
  }
 ::v-deep .enterDlog .el-dialog__body{
    display: flex;
    justify-content: center;
  }
  ::v-deep .el-dialog__body{
    padding-top: 30px;
  }
  </style>
  