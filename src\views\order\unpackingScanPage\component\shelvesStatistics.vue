<template>
    <MyContainer class="contain">
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.wmsId" placeholder="仓库" class="publicCss" clearable filterable
                    @change="getList('search')">
                    <el-option v-for="item in wareHouseList" :key="item.wms_co_id" :label="item.name"
                        :value="item.wms_co_id" />
                </el-select>
                <el-input v-model.trim="ListInfo.area" placeholder="区域" maxlength="50" clearable class="publicCss" />
                <el-input v-model.trim="ListInfo.shelve" placeholder="货架" maxlength="50" clearable class="publicCss" />
                <div style="margin-right: 10px;">
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
                    <el-button type="primary" size="mini" @click="batchPrint">批量打印</el-button>
                </div>
                <div class="extData" v-if="extData">
                    <div class="extData_item">订单数:{{ extData.stat.orderCount }}</div>
                    <div class="extData_item">拆包订单:{{ extData.stat.splitOrderCount }}</div>
                    <div class="extData_item">丢弃订单:{{ extData.stat.discardOrderCount }}</div>
                    <div class="extData_item">再扭转订单:{{ extData.stat.turnOrderCount }}</div>
                    <div class="extData_item">总成本:{{ extData.stat.cost }}</div>
                    <div class="extData_item">拆包成本:{{ extData.stat.splitCost }}</div>
                    <div class="extData_item">丢弃成本:{{ extData.stat.discardCost }}</div>
                    <div class="extData_item">再扭转成本:{{ extData.stat.turnCost }}</div>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%"
            @select="select" :showsummary="data.summary ? true : false" :summaryarry="data.summary"
            @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange"
                :sizes="[500, 1000]" />
        </template>

        <div id="print" class="print">
            <div v-for="(item, i) in selectList" :key="item.id" class="print_item">
                <el-image :src="item.picture" class="imgcss" />
                <div style="display: flex;flex-direction: column;">
                    <div style="display: flex;justify-content: space-between;align-items: center;">
                        <div style="font-size: 36px;text-align: left">{{ item.id }}</div>
                        <el-image :src="`${apiPrefix}BarCode?RawData=${item.id}`" class="imgCode"></el-image>
                    </div>
                    <div class="print_item_bottom">
                        <div style=" text-align: left;white-space: nowrap;">{{ item.goodsCode }} </div>
                        <div class="print_item_bottom_word" style=" text-align: right;"> {{ item.goodsName }}</div>
                    </div>
                </div>
            </div>
        </div>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableVirtualScroll.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/Split/Pin/'
import { pageGetTbWarehouseAsync } from "@/api/inventory/prepack.js"
import { printJS } from '@/utils/print'
const apiPrefix = `${process.env.VUE_APP_BASE_API_VerifyOrder}/Image/`;
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan
    },
    props: {
        styleCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            apiPrefix,
            api,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 500,
                orderBy: '',
                isAsc: false,
                summarys: [],
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            isExport: false,
            wareHouseList: [],
            selectList: [],
            dialogVisible: false,
            extData: null,
            extDataMap: {
                orderCount: '订单数',
                splitOrderCount: '拆包订单',
                discardOrderCount: '丢弃订单',
                turnOrderCount: '再扭转订单',
                cost: '总成本',
                splitCost: '拆包成本',
                discardCost: '丢弃成本',
                turnCost: '再扭转成本',
            }
        }
    },
    async mounted() {
        await this.getCol();
        // await this.getList()
        await this.getWareHouse()
    },
    methods: {
        batchPrint() {
            if (this.selectList.length == 0) return this.$message.error('请选择数据')
            this.loading = true
            const params = {
                id: 'print',
                type: 'html',
            }
            document.getElementById('print').style.display = 'flex'
            printJS(params)
            this.loading = false
        },
        select(val) {
            this.selectList = val
        },
        async getWareHouse() {
            const params = {
                currentPage: 1,
                pageSize: 1000,
                orderBy: null,
                isAsc: false,
            }
            const { data: { list } } = await pageGetTbWarehouseAsync(params)
            this.wareHouseList = list
        },
        proCodeCallback(val) {
            this.ListInfo.proCode = val
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                data.unshift({
                    label: '',
                    type: 'checkbox',
                })
                data.forEach(item => {
                    if (item.prop == 'orderNoInner') {
                        item.type = 'orderLogInfo'
                        item.orderType = 'orderNoInner'
                    }
                    if (item.prop == 'proCode') {
                        item.type = 'html'
                        item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
                    }
                })
                this.tableCols = data;
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                    this.extData = data.extData
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

#print {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;

    .print_item {
        display: flex;
        flex-direction: column;
        width: 23%;
        height: 25%;
        page-break-inside: avoid;
        background-color: #f2f2f2;
        margin-bottom: 5px;

        .img {
            height: 255px;
        }

        .print_item_bottom {
            display: flex;
            justify-content: space-between;

            .print_item_bottom_word {
                font-size: 24px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
            }
        }
    }
}


.imgcss ::v-deep img {
    min-width: 100% !important;
    min-height: 255px !important;
    width: 100% !important;
    height: 255px !important;
    page-break-inside: avoid;
}

.imgCode ::v-deep img {
    min-width: 200px !important;
    min-height: 40px !important;
    width: 200px !important;
    height: 20px !important;
    page-break-inside: avoid;
}

.extData {
    display: flex;
    align-items: center;
    font-size: 14px;

    gap: 3px;

    .extData_item {
        margin-right: 5px;
        background-color: #f2f2f2;
        padding: 5px;
        border-radius: 2px;
    }
}

::v-deep .mycontainer {
    overflow: hidden;
}
</style>
