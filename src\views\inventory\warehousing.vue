<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
         <el-form-item label="采购日期:">
         <el-date-picker style="width: 260px" v-model="filter.timerangecg" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="审核日期:">
         <el-date-picker style="width: 260px" v-model="filter.timerangesh" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="入库日期:">
         <el-date-picker style="width: 260px" v-model="filter.timerangerk" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="采购员:">
        <el-select v-model="filter.brandId" multiple collapse-tags clearable placeholder="请选择采购员" style="width: 300px">
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="采购单号:"><el-input v-model="filter.buyNo" /></el-form-item>
      <el-form-item label="入库单号:"><el-input v-model="filter.warehousingNo" /></el-form-item>
      <el-form-item label="供应商:"><el-input v-model="filter.supplier" /></el-form-item>
      <el-form-item label="商品编码:"><el-input v-model="filter.goodsCode" /></el-form-item>
      <el-form-item label="状态:"><el-input v-model="filter.status" /></el-form-item>
      <el-form-item><el-button type="primary" @click="onSearch">查询</el-button></el-form-item>
      <!-- <el-form-item label="状态:">
        <el-select v-model="filter.status"  placeholder="请选择状态" style="width: 100%">
          <el-option label="所有" value/>
          <el-option label="待审核" value="待审核"/>
          <el-option label="完成" value="完成"/>
          <el-option label="已确认" value="已确认"/>
          <el-option label="作废" value="作废"/>
        </el-select>
      </el-form-item> -->
      </el-form>
    </template>
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @cellclick='cellclick' 
        :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
        <template slot='extentbtn'>
          <el-button-group>
            <el-button style="margin: 0;">
              {{lastUpdateTime}}
            </el-button>
          </el-button-group>
        </template>
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

   <el-dialog title="导入数据" :visible.sync="importVisible" width="30%">
      <span>
        <el-upload ref="upload" class="upload-demo"
          :auto-upload="false" :multiple="false" action accept=".xlsx"
          :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}   </el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importVisible = false">关闭</el-button>
      </span>
    </el-dialog>
   <el-popover ref="detailPopover" placement="bottom-end" v-model="popoverdetailvisible" :reference="prevTarget" :key="('detail'+popperFlagdetail.toString())">
      <el-table :data="detaillist">
        <el-table-column width="130" property="goodsCode" label="商品编码"></el-table-column>
        <el-table-column width="260" property="goodsName" label="商品名称"></el-table-column>
        <el-table-column width="60" property="price" label="单价"></el-table-column>
        <el-table-column width="60" property="count" label="数量"></el-table-column>
        <el-table-column width="60" property="amont" label="金额"></el-table-column>
        <el-table-column width="90" property="weight" label="重量"></el-table-column>
        <el-table-column width="75" property="volume" label="体积"></el-table-column>
      </el-table>
    </el-popover>
  </my-container>
</template>

<script>
import {queryWarehousingOrderDetail,pageWarehousingOrder,importWarehousingOrder,getLastUpdateTimeWarehousingOrder} from '@/api/inventory/purchase'
import {getAllProBrand} from '@/api/inventory/warehouse'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatYesornoBool,formatNoLink,formatmoney,formatTime,formatWarehouseArea,formatWarehouse,formatSecondToHour} from "@/utils/tools";
import { throttle } from 'throttle-debounce';
const tableCols =[
      {istrue:true,prop:'warehousingNo',label:'入库单号', width:'100',sortable:'custom',type:'html',formatter:(row)=>formatNoLink(row.warehousingNo)},
      {istrue:true,prop:'buyNo',label:'采购单号', width:'80',sortable:'custom',type:'html',formatter:(row)=>formatNoLink(row.buyNo)},
      {istrue:true,prop:'brandName',label:'采购员', width:'65'},
      {istrue:true,prop:'purchaseDate',label:'采购日期',  width:'105',sortable:'custom',formatter:(row)=>formatTime(row.purchaseDate,'MM-DD HH:mm:ss')},
      {istrue:true,prop:'checkDate',label:'审核日期',  width:'105',sortable:'custom',formatter:(row)=>formatTime(row.checkDate,'MM-DD HH:mm:ss')},
      {istrue:true,prop:'supplier',label:'供应商', width:'210',sortable:'custom'},
      {istrue:true,prop:'warehousingDate',label:'入库日期',width:'105',sortable:'custom',formatter:(row)=>formatTime(row.warehousingDate,'MM-DD HH:mm:ss')},
      {istrue:true,prop:'inTransitTime',label:'在途时长', width:'120',sortable:'custom',formatter:(row)=>formatSecondToHour(row.inTransitTime)},
      {istrue:true,prop:'preparer',label:'制单人', width:'110',sortable:'custom'}, 
      {istrue:true,prop:'warehouse',label:'入库仓', width:'190',sortable:'custom',formatter:(row)=> row.warehouseName},
      {istrue:true,prop:'status',label:'入库状态', width:'80',sortable:'custom'},
      {istrue:true,prop:'count',label:'入库数量', width:'75',sortable:'custom'}, 
      {istrue:true,prop:'amont',label:'入库金额', width:'90',sortable:'custom'},
      {istrue:true,prop:'weight',label:'入库重量',  width:'80',sortable:'custom',formatter:(row)=>formatmoney(row.weight)},
      {istrue:true,prop:'remark',label:'备注', width:'auto'},
     ];
const tableHandles=[
  //{label:"导入", handle:(that)=>that.startImport()}
];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: {
        timerangecg:null,
        timerangesh:null,
        timerangerk:null,
        startPurchaseDate:null, 
        endPurchaseDate:null, 
        startCheckDate:null, 
        endCheckDate:null, 
        startWarehousingDate:null, 
        endWarehousingDate:null, 
        buyNo:null, 
        supplier:null, 
        warehousingNo:null,
        goodsCode:null, 
        brandId:null,
        status:null,
      },
      lastUpdateTime:"",
      list: [],
      detaillist:[],
      brandlist:[],
      tableCols:tableCols,
      tableHandles:tableHandles,
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      popoverdetailvisible: false,
      importVisible: false,
      prevTarget: null, // 编辑 Popover 的 Reference （参照），用于 popover.js 对齐两个元素
      popperFlag: false, // 用于编辑 Popover 的刷新
      popperFlagdetail: false,
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      selids: [],
      fileList:[],
      listLoading: false, 
      pageLoading: false,
      uploadLoading:false
    }
  },
  async mounted() {
    this.init();
    this.getlist();
    //await this.setBandSelect();
    //await this.setGroupSelect();
  },
  beforeUpdate() {
    console.log('update')
  },
  watch: {
    value(n) {
      if(n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);
      }
    }
  },
  methods: {
    async init(){
        var res2= await getAllProBrand();
        this.brandlist = res2.data.map(item => {
            return { value: item.key, label: item.value };
        });

        var res3= await getLastUpdateTimeWarehousingOrder();
        this.lastUpdateTime= "最晚更新时间:"+res3.data
    },
    async removeEditPopoverListener(flag) {
      let timer = setTimeout(() => {
        let scrollElement = this.$refs.table.$el.querySelector('.el-table__body-wrapper');
        console.log('监听滚动，用于编辑框的滚动移除', flag, scrollElement);
        let scrollHandle = () => {
          console.log('执行--->', this.visibleEditOpinions);
          if (this.visibleEditOpinions) {
            this.clearEditPopperComponent();
          }
        }
        if (flag) {
          // 滚动节流
          scrollElement.addEventListener('scroll', throttle(500, scrollHandle));
        } else {
          scrollElement.removeEventListener('scroll', scrollHandle);
        }
        clearTimeout(timer);
      }, 0);
    },
    async onSearch() {      
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      if (params.timerangecg) {
         params.startPurchaseDate = params.timerangecg[0];
         params.endPurchaseDate = params.timerangecg[1];
      }
      if (params.timerangesh) {
        params.startCheckDate = params.timerangesh[0];
        params.endCheckDate = params.timerangesh[1];
      }
      if (params.timerangerk) {
        params.startWarehousingDate = params.timerangerk[0];
        params.endWarehousingDate = params.timerangerk[1];
      }
      this.listLoading = true
      const res = await pageWarehousingOrder(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
   async cellclick(row, column, cell, event){
     if (column.property=='buyNo') {
         this.$router.push({path: '/inventory/purchaseindex', query: {buyNo: row.buyNo}})
      }
     else if (column.property=='warehousingNo') {
      await this.getdetaillist(row.warehousingNo)
      if (event.stopPropagation) {//阻止事件冒泡，兼容ie
        event.stopPropagation();
      } else if (window.event) {
        window.event.cancelBubble = true;
      }
      let currentTarget = event.target; // 赋值当前点击的编辑
      this.editData = row; // 设置编辑数据
      if (this.prevTarget === currentTarget) { // 判断是否需要切换
        this.popoverdetailvisible = !this.popoverdetailvisible; // 同一个元素重复点击
      } else {
        if (this.prevTarget) {  // 切换不同元素, 判断之前是否有点击其他编辑 prevTarget
          this.clearEditPopperComponent();   // 先清除之前的编辑框
          this.$nextTick(() => { // 然后生成新的编辑框
            this.prevTarget = currentTarget;
            this.popoverdetailvisible = true;
          });
        } else {
          console.log('首次--->this.prevTarget'); // 首次
          this.prevTarget = currentTarget;
          this.popoverdetailvisible = true;
        }
      }
     }
    },
    async clearEditPopperComponent() {
      this.prevTarget = null;
      this.popperFlag = !this.popperFlag;
      this.popperFlagdetail= !this.popperFlagdetail;
      this.popoverdetailvisible= false;
    },
    async getdetaillist(warehousingNo){
       this.detaillist=[];
       const res = await queryWarehousingOrderDetail({warehousingNo:warehousingNo})
       if (!(res.code==1&&res.data)) return 
       this.detaillist=res.data;
    },
  async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
  async onEdit(row) {
      this.formtitle='编辑';
      this.addFormVisible = true
      const res = await getWarehousesigle({id:row.id})
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields()
      await this.autoform.fApi.setValue(res.data)
    },  
  async onEditSubmit() {
      this.addFormVisible = true
      await onAddSubmit();
    },
  async onAddSubmit() {
      this.addLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          formData.id=formData.id?formData.id:0;
          formData.Enabled=true;
          const res = await updateWarehouse(formData);
          if(res.code==1){
            this.getlist();
            this.addFormVisible=false;
          }
        }else{
          //todo 表单验证未通过
        }
     })
     this.addLoading=false;
    },
    startImport() {
      this.importVisible = true;
    },
    cancelImport() {
      this.importVisible = false;
    },
    beforeRemove() {
      return false;
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
   async submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit=true;
      this.uploadLoading=true;
      this.$refs.upload.submit();
    },
   async uploadFile(item) {
      if(!this.fileHasSubmit){
        return false;
      }
      this.fileHasSubmit=false;
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      const res =await importWarehousingOrder(form);
      if (res.code==1)   this.$message({ message: "上传成功,正在导入中...", type: "success" });
      else  this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading=false; 
    },
   async uploadChange(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for(var i=0;i<fileList.length;i++){
          if(fileList[i].status=="success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.fileList = list;
      }
    },
   async uploadRemove(file, fileList){
       this.uploadChange(file, fileList);
    },
    selsChange: function(sels) {
      this.sels = sels
    }
  }
}
</script>
