<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="年月:">
          <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM"
            value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
        </el-form-item>
        <el-form-item label="平台:" label-position="right">
          <el-select filterable v-model="filter.platform" placeholder="请选择" class="el-select-content" clearable>
            <el-option label="天猫" value=1 />
            <el-option label="阿里巴巴" value=4 />
            <el-option label="淘工厂" value=8 />
            <el-option label="淘宝" value=9 />
          </el-select>
        </el-form-item>
        <el-form-item label="所属店铺:" label-position="right">
          <el-select filterable v-model="filter.shopCode" placeholder="请选择" class="el-select-content" clearable>
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单编号:" label-position="right">
          <el-input v-model="filter.serialNumberOrder" style="width:183px;" clearable />
        </el-form-item>
        <el-form-item label="财务流水号:" label-position="right">
          <el-input v-model="filter.serialNumberFinacial" style="width:183px;" clearable />
        </el-form-item>
        <el-form-item label="收入金额:" label-position="right">
          <el-button style="padding: 0; border: none; float: left">
            <el-input-number v-model="filter.minAmountIncome" style="width: 90px" :max="999999999999.99" :min="-999999999999.99"></el-input-number>
          </el-button>
          <span style="float: left; line-height: 30px; padding: 0 8px">至</span>
          <el-button style="padding: 0; border: none; float: left">
            <el-input-number v-model="filter.maxAmountIncome" style="width: 90px" :max="999999999999.99" :min="-999999999999.99"></el-input-number>
          </el-button>
        </el-form-item>
        <el-form-item label="支出金额:" label-position="right">
          <el-button style="padding: 0; border: none; float: left">
            <el-input-number v-model="filter.minAmountPaid" style="width: 90px" :max="999999999999.99" :min="-999999999999.99"></el-input-number>
          </el-button>
          <span style="float: left; line-height: 30px; padding: 0 8px">至</span>
          <el-button style="padding: 0; border: none; float: left">
            <el-input-number v-model="filter.maxAmountPaid" style="width: 90px" :max="999999999999.99" :min="-999999999999.99"></el-input-number>
          </el-button>
        </el-form-item>
          
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport">导出所有</el-button>
        </el-form-item>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :tableData='ZTCKeyWordList' :showsummary="true" :summaryarry="summaryarry" @select='selectchange'
      :isSelection='false' :tableCols='tableCols' :loading="listLoading" :isSelectColumn='false' style="height: 99%;">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <template slot='extentbtn'>
        <el-button-group>
          <el-button type="primary" @click="onRefresh">刷新</el-button>
        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>
  </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { getTxWeiJieSuanPageList, ExportWeiJieSuanList } from '@/api/monthbookkeeper/financialDetail'

const tableCols = [
  { istrue: true, prop: 'serialNumberFinacial', label: '财务流水号', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'serialNumberMerchantOrder', label: '商户订单号', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'timeOccur', label: '发生时间', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'amountIncome', label: '收入金额(+元)', sortable: 'custom', width: 'auto', type: 'html', formatter: (row) => { return row.amountIncome?.toFixed(2) } },
  { istrue: true, prop: 'amountPaid', label: '支出金额(-元)', sortable: 'custom', width: 'auto', type: 'html', formatter: (row) => { return row.amountPaid?.toFixed(2) } },
  { istrue: true, prop: 'typeBusiness', label: '业务类型', sortable: 'custom', width: 'auto' },
  //{ istrue: true, prop: 'recoganizeTypeBusiness', label: '识别业务类型', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'descRemark', label: '备注', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'descBusinessType', label: '业务描述', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'typeBusinessOrderOrigin', label: '业务账单来源', sortable: 'custom', width: 'auto' },
  //{ istrue: true, prop: 'serialNumberBusinessBaseOrder', label: '业务基础订单号', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'recoganizeSerialNumberOrder', label: '订单编号', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'nameMerchandise', label: '商品名称', sortable: 'custom', width: 'auto' },
];
export default {
  name: "unsettledTx",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
  data() {
    return {
      that: this,
      filter: {
        yearMonth: null,
        shopCode: null,
      },
      shopList: [],
      userList: [],
      groupList: [],
      ZTCKeyWordList: [],
      tableCols: tableCols,
      total: 0,
      pager: { OrderBy: "id", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
      summaryarry: {}
    };
  },
  async mounted() {
    await this.getShopList();
  },
  methods: {
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    async getShopList() {
      let p = { platforms: [1, 4, 8, 9] }
      const res1 = await getAllShopList(p);
      this.shopList = [];
      res1.data?.forEach(f => {
        if (f.isCalcSettlement && f.shopCode)
          this.shopList.push(f);
      });
    },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      const params = this.getCondition();
      this.listLoading = true;
      const res = await getTxWeiJieSuanPageList(params);
      this.listLoading = false;
      this.total = res.data?.total
      this.ZTCKeyWordList = res.data?.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    getCondition() {
      let pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };
      return params;
    },
    async onExport(opt) {
      if (!this.filter.yearMonth) {
        this.$message({ message: "请先选择月份", type: "warning" });
        return;
      }
      let pars = this.getCondition();
      if (pars === false) {
        return;
      }
      const params = { ...pars, ...opt };
      this.listLoading = true;
      let res = await ExportWeiJieSuanList(params);
      this.listLoading = false;
      if (!res?.data) {
        return
      }
      this.$message({ message: "正在后台导出中，请点击头像-在下载管理中查看", type: "success" });
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
