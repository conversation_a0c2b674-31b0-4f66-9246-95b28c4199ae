<template>
  <my-container>
    <el-tabs v-model="activeName" style="height:95%;">
      <el-tab-pane label="宿舍入住管理" name="first1" style="height: 100%;">
        <ledgerOA ref="refledgerOA" />
      </el-tab-pane>
      <el-tab-pane label="雇主责任险" name="first2" style="height: 100%;" lazy>
        <waterElectricityCharges ref="refwaterElectricityCharges" />
      </el-tab-pane>

      <el-tab-pane label="三方比价数据" name="first4" style="height: 100%;" lazy>
        <clejuan ref="clejuan" />
      </el-tab-pane>
      <el-tab-pane label="仓储支出费用明细" name="first5" style="height: 100%;" lazy>
        <ejichaqingkuangshuju ref="ejichaqingkuangshuju" />
      </el-tab-pane>

      <el-tab-pane label="仓储收入明细" name="first3" style="height: 100%;" lazy>
        <dshushezhushu ref="dshushezhushu" />
      </el-tab-pane>

      <el-tab-pane label="员工餐" name="first6" style="height: 100%;" lazy>
        <fgongsitaizhangshuju ref="fgongsitaizhangshuju" />
      </el-tab-pane>
      <el-tab-pane label="水电费" name="first7" style="height: 100%;" lazy>
        <hgudingzichan ref="hgudingzichan" />
      </el-tab-pane>
      <el-tab-pane label="仓储维修" name="first8" style="height: 100%;" lazy>
        <isanfangbijiaoshuju ref="isanfangbijiaoshuju" />
      </el-tab-pane>
      <el-tab-pane label="仓储稽查乐捐" name="first9" style="height: 100%;" lazy>
        <jauditDonation ref="jauditDonation" />
      </el-tab-pane>
      <el-tab-pane label="仓储发货数据" name="first10" style="height: 100%;" lazy>
        <kdeliveryData ref="kdeliveryData" />
      </el-tab-pane>
      <el-tab-pane label="邮政食堂费用收入明细" name="first11" style="height: 100%;" lazy>
        <lcanteenIncome ref="lcanteenIncome" />
      </el-tab-pane>
      <el-tab-pane label="食堂收支汇总表" name="first12" style="height: 100%;" lazy>
        <mcanteenRevenue ref="mcanteenRevenue" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import ledgerOA from './components/aTaiZhang/ledgerOA.vue'
import waterElectricityCharges from './components/bshuidian/waterElectricityCharges.vue'

import clejuan from './components/clejuan/index.vue'
import ejichaqingkuangshuju from './components/ejichaqingkuangshuju/index.vue'

import fgongsitaizhangshuju from './components/fgongsitaizhangshuju/index.vue'
import hgudingzichan from './components/hgudingzichan/index.vue'
import isanfangbijiaoshuju from './components/isanfangbijiaoshuju/index.vue'
import lcanteenIncome from './components/lcanteenIncome/index.vue'
import mcanteenRevenue from './components/mcanteenRevenue/index.vue'
import jauditDonation from './components/jauditDonation/index.vue'
import dshushezhushu from './components/dshushezhushu/index.vue'
import kdeliveryData from './components/kdeliveryData/index.vue'


export default {
  name: "officeDashboardIndex",
  components: {
    MyContainer, ledgerOA, waterElectricityCharges, clejuan, ejichaqingkuangshuju,
    isanfangbijiaoshuju, fgongsitaizhangshuju, hgudingzichan, lcanteenIncome, jauditDonation, dshushezhushu, kdeliveryData, mcanteenRevenue
  },
  data() {
    return {
      activeName: 'first1',
    };
  },
  methods: {

  },
};
</script>
<style lang="scss" scoped></style>
