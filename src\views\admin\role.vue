		  <template>
    <my-container v-loading="pageLoading">
        <!--查询-->
        <template #header>
            <el-form class="form" :inline="true">
                <!-- v-if="checkPermission(['api:admin:role:add'])" -->
                <el-form-item>
                    <el-button type="primary" @click="toggleRowExpansion">
                        全部{{ isExpansion ? "收缩" : "展开" }}
                    </el-button>
                    <el-button type="primary" @click="onAdd">新增角色</el-button>
                </el-form-item>
                <!-- <el-form-item v-if="checkPermission(['api:admin:role:batchsoftdelete'])">
                    <my-confirm-button :disabled="sels.length === 0" :type="'delete'" :placement="'bottom-end'" :loading="deleteLoading" :validate="batchDeleteValidate" style="margin-left: 0px;" @click="onBatchDelete">
                        <template #content>
                            <p>确定要批量删除吗？</p>
                        </template>
                        批量删除角色信息
                    </my-confirm-button>
                </el-form-item> -->
            </el-form>
        </template>

        <!--左边显示部分（显示角色信息）-->
        <el-row style="height:calc(100% - 1px);">
            <el-col :span="12" style="height:100%;">
                <div class="grid-content bg-purple" style="height:100% ;">
                    <!--列表-->
                    <el-table ref="dataTreeList" height="100%" v-loading="listLoading" :data="roles" border highlight-current-row @select="handleSelect" @select-all="handleSelectAll" @selection-change="debounceHandleSelectionChange" @row-click="GetRoleUsersinfo" row-key="id" default-expand-all :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
                        <!-- <el-table-column type="selection" width="50" /> -->
                        <el-table-column type="index" width="50" label="#" />
                        <el-table-column prop="name" label="角色名" />
                        <!-- <el-table-column prop="description" label="说明" width="160" /> -->
                        <el-table-column prop="modifiedTime" label="修改时间" :formatter="formatCreatedTime" width="100" />
                        <!--<el-table-column prop="CreatedUserName" label="创建者" width="" >-->
                        <!--</el-table-column>-->
                        <el-table-column prop="enabled" label="状态" width="60">
                            <template #default="{row}">
                                <el-tag :type="row.enabled ? 'success' : 'danger'" disable-transitions>{{ row.enabled ? '正常' : '禁用' }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="checkPermission(['api:admin:role:update','api:admin:role:softdelete'])" label="操作" width="210">
                            <template #default="{ $index, row }">
                                <el-button type="text" v-if="checkPermission(['api:admin:role:update'])" @click="onEdit($index, row)">编辑</el-button>
                                <my-confirm-button v-if="checkPermission(['api:admin:role:softdelete'])" type="text" :loading="row._loading" :validate="deleteValidate" :validate-data="row" @click="onDelete($index, row)">
                                    删除
                                </my-confirm-button>
                                <el-button type="text" style="margin-left:10px;" @click="onAddChildren($index, row)">添加角色</el-button>
                                <el-button type="text" style="margin-left:10px;" @click="onAddDirector($index, row)">添加用户</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-col>
            <el-col :span="12" style="height:100%;">
                <div style="height:100%;">
                    <!--弹出显示该角色的用户-->
                    <el-table :data="list" border  height="100%" >
                        <el-table-column prop="userName" label="用户名" width="170"></el-table-column>
                        <el-table-column prop="nickName" label="昵称" width="170"></el-table-column>
                        <el-table-column prop="name" label="所属角色" ></el-table-column>
                        <el-table-column prop="status" label="用户状态" width="100">
                            <template #default="{row}">
                                <el-tag :type="row.status==0 ? 'success' : 'danger'" disable-transitions>{{ row.status==0 ? '正常' : '禁用' }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作">
                            <template slot-scope="scope">
                                <el-button type="text" @click="DeleteUserClick(scope.row)" size="small">
                                    移除
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-col>
        </el-row>

        <!--分页-->
        <!-- <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getRoles" />
        </template> -->
        <el-drawer v-if="checkPermission(['api:admin:role:add'])" title="新增角色" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="addFormVisiblerole" direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;" @close="closeAddForm">
            <section style="padding:24px 48px 74px 24px;">
                <el-form ref="addForm" :model="addForm" :rules="addFormRules" label-width="80px" :inline="false">
                    <el-row>
                        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
                            <el-form-item label="角色名" prop="name">
                                <el-input v-model="addForm.name" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
                            <el-form-item label="状态" prop="enabled">
                                <el-select v-model="addForm.enabled" placeholder="请选择角色状态" style="width:100%;">
                                    <el-option v-for="item in statusList" :key="item.value" :label="item.name" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                            <el-form-item label="说明" prop="description">
                                <el-input v-model="addForm.description" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                            <el-form-item label="绑定首页">
                                <el-cascader placeholder="请选择需要绑定的首页" :options="showMenuList" :props="optionProps" v-model="defaultMenuList" style="width:100%;" filterable clearable />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </section>
            <div class="drawer-footer">
                <el-button @click.native="addFormVisiblerole = false">取消</el-button>
                <my-confirm-button type="submit" :validate="addFormValidate" :loading="addLoading" @click="onAddSubmit" />
            </div>
        </el-drawer>

        <!--新增成员窗口-->
        <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="adduserFormVisible" direction="btt" size="30%" class="el-drawer__wrapper" style="position:absolute;">
            <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" />
            <div class="drawer-footer">
                <el-button @click.native="adduserFormVisible = false">取消</el-button>
                <my-confirm-button type="submit" :loading="addLoading" @click="onAddUserSubmit" />
            </div>
        </el-drawer>

        <!--编辑窗口-->
        <el-drawer v-if="checkPermission(['api:admin:role:update'])" title="编辑角色" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editFormVisible" direction="btt" size="'auto'" style="position:absolute;" @close="closeEditForm">
            <section style="padding:24px 48px 74px 24px;">
                <el-form ref="editForm" :model="editForm" :rules="editFormRules" label-width="80px" :inline="false">
                    <el-row>
                        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
                            <el-form-item label="角色名" prop="name">
                                <el-input v-model="editForm.name" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
                            <el-form-item label="状态" prop="enabled">
                                <el-select v-model="editForm.enabled" placeholder="请选择角色状态" style="width:100%;">
                                    <el-option v-for="item in statusList" :key="item.value" :label="item.name" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                            <el-form-item label="父级角色" prop="ParentId">
                                <el-cascader placeholder="请选择角色" :options="rolesFilter" v-model="editForm.parentId" style="width:100%;" :props="roleOptionProps" :show-all-levels="false"  size="small"  filterable  clearable />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                            <el-form-item label="说明" prop="description">
                                <el-input v-model="editForm.description" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                            <el-form-item label="绑定首页">
                                <el-cascader placeholder="请选择需要绑定的首页" :options="showMenuList" :props="optionProps" v-model="defaultMenuList" style="width:100%;" filterable clearable />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </section>
            <div class="drawer-footer">
                <el-button @click.native="editFormVisible = false">取消</el-button>
                <my-confirm-button type="submit" :validate="editFormValidate" :loading="editLoading" @click="onEditSubmit" />
            </div>
        </el-drawer>
    </my-container>
</template>

<script>
    import { debounce } from 'lodash'
    import { formatTime } from '@/utils'
    import { getRoleListPage, removeRole, editRole, addRole, batchRemoveRole, getRole, GetRoleUsers, DeleteRoleUsers, addRoleUser, GetMenuDropDownList, validateSoftDeleteAsync,getRoleListPageByFilter} from '@/api/admin/role'
    import { addUser, getUserListPage } from '@/api/admin/user'
    import MyContainer from '@/components/my-container'
    import MyConfirmButton from '@/components/my-confirm-button'
    const tableHandles2 = [{ label: "新增用户", handle: (that) => that.onAddDirector() }];

    export default {
        name: 'Roles',
        components: { MyContainer, MyConfirmButton },
        data () {
            return {
                adduserFormVisible: false,
                userNameReadonly: true,
                // 新增用户界面数据
                addFormuser: {
                    userName: '',
                    nickName: '',
                    password: '',
                    roleIds: []
                },
                optionProps: {
                    value: 'id',
                    label: 'name',
                    parentId: 'parentId',
                    children: 'children'
                },
                roleOptionProps: {
                    value: 'id',
                    label: 'name',
                    parentId: 'parentId',
                    children: 'children',
                    checkStrictly:true
                },
                defaultMenuList: [],
                showMenuList: [],
                formtitle: "",
                roleid: '',
                rolename: "",
                dialog: false,
                form: "",
                addFormVisible: false,
                dialogTableVisible: false,
                roles: [],
                rolesFilter:[],
                list: [],
                total: 0,
                sels: [], // 列表选中列
                statusList: [
                    { name: '激活', value: true },
                    { name: '禁用', value: false }
                ],
                listLoading: false,
                pageLoading: false,
                addDialogFormVisible: false,
                editFormVisible: false, // 编辑界面是否显示
                editLoading: false,
                editFormRules: {
                    name: [{ required: true, message: '请输入角色名', trigger: 'blur' }],
                    enabled: [{ required: true, message: '请输入状态', trigger: 'change' }]
                },
                // 编辑界面数据
                editForm: {
                    id: 0,
                    name: '',
                    description: '',
                    enabled: '',
                    menuId: "",
                    menuIdList: "",
                    parentId: "",
                    hierarchyCode: ""
                },
                editFormRef: null,
                addFormVisiblerole: false, // 新增界面是否显示
                addLoading: false,
                addFormRules: {
                    name: [{ required: true, message: '请输入角色名', trigger: 'blur' }],
                    enabled: [{ required: true, message: '请输入状态', trigger: 'change' }]
                },
                // 新增界面数据
                addForm: {
                    name: '',
                    description: '',
                    enabled: true,
                    menuId: "",
                    menuIdList: "",
                    parentId: "",
                    hierarchyCode: ""
                },
                addFormRef: null,
                deleteLoading: false,
                autoform: {
                    fApi: {},
                    options: {
                        submitBtn: false, form: { labelWidth: '145px' },
                        global: { '*': { props: { disabled: false }, col: { span: 8 } } },
                        rule: [{ type: 'hidden', field: 'roleid', title: 'id', value: '' },

                        {
                            type: 'select', field: 'userId', title: '用户名', validate: [{ type: 'number', required: true, message: '请选择用户' }], value: 0, options: [],
                            props: { filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchUser(parm) }
                        }
                        ],
                    },
                },
                isExpansion: true
            }
        },
        async mounted () {
            await this.onInitautoform();
            this.getRoles()
        },
        beforeUpdate () {
            console.log('update')
        },
        created () {
            this.debounceHandleSelectionChange = debounce(this.handleSelectionChange)
        },
        methods: {
            async remoteSearchUser (parm) {
                if (!parm) {
                    //this.$message({ message: this.$t('api.sync'),type: 'warn'})
                    return;
                }
                var dynamicFilter = { field: 'nickName', operator: 'Contains', value: parm }
                var options = [];
                const res = await getUserListPage({ currentPage: 1, pageSize: 50, dynamicFilter: dynamicFilter })
                res?.data?.list.forEach(f => {
                    options.push({ value: f.id, label: f.nickName })
                })
                this.autoform.fApi.getRule('userId').options = options;
            },
            async onInitautoform () {
                let rule = [
                    {
                        type: 'select', field: 'userId', title: '用户名', validate: [{ type: 'number', required: true, message: '请搜索用户' }], value: null, options: [],
                        props: { filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchUser(parm) }
                    }
                ];

                this.autoform.rule = rule
            },
            async onAddDirector (index, row) {
                this.adduserFormVisible = true;
                var model = { roleid: row.id, userId: '' }
                console.log("新增用户的角色id", model)
                this.$nextTick(async () => {
                    var arr = Object.keys(this.autoform.fApi);
                    console.log('arr', arr)
                    if (arr.length > 0) {
                        await this.autoform.fApi.setValue(model)
                    }
                })
                this.formtitle = "新增用户";
            },
            //新增用户验证
            addFormvalidate () {
                let isValid = false
                this.$refs.addFormuser.validate(valid => {
                    isValid = valid
                })
                return isValid
            },

            //根据角色id获取该角色的用户信息
            async GetRoleUsersinfo (row, column, e) {
                //展开行
                this.roleid = row.id
                this.rolename = column.property;
                if (column.property == 'name' || column.property == 'description' || column.property == 'modifiedTime') {
                    this.dialogTableVisible = true;
                    var list = await GetRoleUsers({ id: row.id })
                    this.list = list.data
                }
            },
            //移除该角色下的用户
            async DeleteUserClick (row) {
                this.$confirm('确定要移除该用户吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    var userinfo = await DeleteRoleUsers({ rid: row.roleId, uid: row.userId })
                    if (userinfo != null) {
                        this.$message({
                            type: 'success',
                            message: '移除成功!'
                        });
                    }
                    //this.GetRoleUsersinfo(this.roleid,this.rolename);
                    var list = await GetRoleUsers({ id: this.roleid })
                    this.list = list.data
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            },
            formatCreatedTime: function (row, column, time) {
                return formatTime(time, 'YYYY-MM-DD')
            },
            // 获取角色列表
            async getRoles () {
                this.listLoading = true
                const res = await getRoleListPage()
                this.listLoading = false

                if (!res?.success) {
                    return
                }
                const data = res.data
                data.forEach(d => {
                    d._loading = false
                })
                this.roles = data
            },
            async getRolesFilter(){
                this.listLoading = true
                const res = await getRoleListPageByFilter( {userId: this.editForm.id })
                this.listLoading = false

                if (!res?.success) {
                    return
                }
                const data = res.data
                data.forEach(d => {
                    d._loading = false
                })
                this.rolesFilter = data
            },
            // 显示编辑界面
            async onEdit (index, row) {
                this.pageLoading = true
                const res = await getRole({ id: row.id })
                this.pageLoading = false
                if (res && res.success) {
                    const data = res.data
                    this.editForm = data
                    this.defaultMenuList = [];
                    if (data.menuIdList) {
                        this.defaultMenuList = data.menuIdList.split(',');
                    }
                    this.editFormVisible = true
                    const menuList = await GetMenuDropDownList()
                    if (menuList && menuList.success) {
                        this.showMenuList = menuList.data;
                    }
                    this.getRolesFilter();
                }
            },
            closeEditForm () {
                this.$refs.editForm.resetFields()
            },
            // 显示新增界面
            async onAdd () {
                this.addFormVisiblerole = true
                this.defaultMenuList = [];
                this.addForm.parentId = 0;
                const menuList = await GetMenuDropDownList()
                if (menuList && menuList.success) {
                    this.showMenuList = menuList.data;
                }
            },
            // 显示新增界面
            async onAddChildren (index, row) {
                this.addFormVisiblerole = true
                this.defaultMenuList = [];
                this.addForm.parentId = row.id;
                const menuList = await GetMenuDropDownList()
                if (menuList && menuList.success) {
                    this.showMenuList = menuList.data;
                }
            },
            closeAddForm () {
                this.$refs.addForm.resetFields()
            },
            closeAddForm () {
                this.$refs.addFormuser.resetFields()
            },
            // 编辑验证
            editFormValidate: function () {
                let isValid = false
                this.$refs.editForm.validate(valid => {
                    isValid = valid
                })
                return isValid
            },
            // 编辑
            async onEditSubmit () {
                this.editLoading = true
                this.editForm.menuId = this.defaultMenuList[(this.defaultMenuList.length - 1)]
                this.editForm.menuIdList = this.defaultMenuList.toString()
                if(this.editForm.parentId instanceof Array){
                    this.editForm.parentId =  this.editForm.parentId[(this.editForm.parentId.length - 1)];
                }
                const para = _.cloneDeep(this.editForm)
                const res = await editRole(para)
                this.editLoading = false
                if (!res?.success) {
                    return
                }

                this.$message({
                    message: this.$t('admin.updateOk'),
                    type: 'success'
                })
                this.$refs['editForm'].resetFields()
                this.editFormVisible = false
                this.getRoles()
            },
            // 新增验证
            addFormValidate: function () {
                let isValid = false
                this.$refs.addForm.validate(valid => {
                    isValid = valid
                })
                return isValid
            },
            //新增成员信息
            async onAddUserSubmit () {
                this.addLoading = true;
                await this.autoform.fApi.validate(async (valid, fail) => {
                    if (valid) {
                        const formData = this.autoform.fApi.formData();
                        console.log("添加用户的角色id", formData)
                        formData.roleid = this.roleid ? this.roleid : 0;
                        //formData.Enabled=true;
                        const res = await addRoleUser(formData);
                        this.$message.success('添加用户成功');
                        this.adduserFormVisible = false;
                        var list = await GetRoleUsers({ id: this.roleid })

                        this.list = list.data
                    } else {
                        //todo 表单验证未通过
                    }
                })
                this.addLoading = false;
            },
            // 新增角色
            async onAddSubmit () {
                this.addLoading = true
                this.addForm.menuId = this.defaultMenuList[(this.defaultMenuList.length - 1)]
                this.addForm.menuIdList = this.defaultMenuList.toString()
                const para = _.cloneDeep(this.addForm)
                const res = await addRole(para)
                this.addLoading = false
                if (!res?.success) {
                    return
                }

                this.$message({
                    message: this.$t('admin.addOk'),
                    type: 'success'
                })
                this.$refs['addForm'].resetFields()
                this.addFormVisiblerole = false
                this.getRoles()
            },
            // 删除验证
            deleteValidate (row) {
                let isValid = true
                if (row && row.name === 'admin') {
                    this.$message({
                        message: row.name + '，禁止删除！',
                        type: 'warning'
                    });
                    isValid = false;
                }
                return isValid;
            },
            // 删除
            async onDelete (index, row) {
                row._loading = true
                const para = { id: row.id }
                //如果父级下的子集存在数据则不允许删除
                const isVa = await validateSoftDeleteAsync({ hierarchyCode: row.hierarchyCode })
                if (!isVa?.success) {
                    row._loading = false;
                    return;
                };
                const res = await removeRole(para)
                row._loading = false

                if (!res?.success) {
                    return
                }

                this.$message({
                    message: this.$t('admin.deleteOk'),
                    type: 'success'
                })

                this.getRoles()
                //this.getRolesFilter()
            },
            // 批量删除验证
            batchDeleteValidate () {
                let isValid = true
                var row = this.sels && this.sels.find(s => s.name === 'admin')
                if (row && row.name === 'admin') {
                    this.$message({
                        message: row.description + '，禁止删除！',
                        type: 'warning'
                    })
                    isValid = false
                }
                //如果父级下的子集存在数据则不允许删除

                return isValid
            },
            // 批量删除
            async onBatchDelete () {
                const para = { ids: [] }
                para.ids = this.sels.map(s => {
                    return s.id
                })

                this.deleteLoading = true
                const res = await batchRemoveRole(para.ids)
                this.deleteLoading = false

                if (!res?.success) {
                    return
                }
                this.$message({
                    message: this.$t('admin.batchDeleteOk'),
                    type: 'success'
                })

                this.getRoles()
            },
            handleSelectionChange (val) {
                console.log(val)
                this.sels = val
            },
            handleSelectAll () {
                const isAllSelected = this.$refs.dataTreeList.store.states.isAllSelected
                var _handleSelectAll = (data) => {
                    data.forEach(item => {
                        this.$refs.dataTreeList.toggleRowSelection(item, isAllSelected)
                        _handleSelectAll(item.children || [])
                    })
                }
                _handleSelectAll(this.roles)
            },
            handleSelect (selection, current) {
                // 判断selection中是否存在current,若是存在那么就代表是被勾选上了,若是不存在代表是取消勾选了
                const isChecked = !!selection.find(item => item.id === current.id)
                // 如果当前项被取消勾选
                if (!isChecked) {
                    // 那么其所有的祖先也应该被取消勾选
                    this.uncheckedParents(selection, current)
                    // 那么其所有的后代也应该被取消勾选
                    this.toggleCheckedChildrens(selection, current, false)
                } else { // 如果当前项被勾选
                    // 那么若同一组的元素都被勾选了,那么父元素将也被勾选,依次往上类推
                    //this.checkedParents(selection)
                    // 那么其所有的后代都要被勾选
                    this.toggleCheckedChildrens(selection, current, true)
                }
            },
            toggleCheckedChildrens (selection, item, isChecked) {
                var _toggleCheckedChildrens = (data) => {
                    data.find(element => {
                        this.$refs.dataTreeList.toggleRowSelection(element, isChecked)
                        if (isChecked && !selection.find(item => item.id === element.id)) {
                            selection.push(element)
                        } else if (!isChecked && selection.find(item => item.id === element.id)) {
                            for (let i = selection.length - 1; i >= 0; i--) {
                                if (selection[i].id === element.id) {
                                    selection.splice(i, 1)
                                    break
                                }
                            }
                        }
                        _toggleCheckedChildrens(element.children || [])
                    })
                }
                _toggleCheckedChildrens(item.children || [])
            },
            checkedParents (selection) {
                var _checkedParents = (element) => {
                    const children = element.children
                    if (children && children.length) {
                        const allChildrenChecked = children.every(child => {
                            return _checkedParents(child)
                        })
                        if (allChildrenChecked) {
                            this.$refs.dataTreeList.toggleRowSelection(element, true)
                            if (!selection.find(item => item.id === element.id)) {
                                selection.push(element)
                            }
                        }
                    }
                    return selection.find(item => item.id === element.id)
                }
                this.roles.forEach(element => {
                    _checkedParents(element)
                })
            },
            uncheckedParents (selection, item) {
                var _uncheckedParents = (data) => {
                    return data.find(element => {
                        if (element.id === item.id) {
                            return true
                        } else if (_uncheckedParents(element.children || [])) {
                            this.$refs.dataTreeList.toggleRowSelection(element, false)
                            for (let i = selection.length - 1; i >= 0; i--) {
                                if (selection[i].id === element.id) {
                                    selection.splice(i, 1)
                                    break
                                }
                            }
                            return true
                        } else {
                            return false
                        }
                    })
                }
                _uncheckedParents(this.roles)
            },
            toggleRowExpansion () {
                this.isExpansion = !this.isExpansion;
                this.toggleRowExpansionAll(this.roles, this.isExpansion);
            },
            toggleRowExpansionAll (data, isExpansion) {
                data.forEach((item) => {
                    this.$refs.dataTreeList.toggleRowExpansion(item, isExpansion);
                    if (item.children !== undefined && item.children !== null) {
                        this.toggleRowExpansionAll(item.children, isExpansion);
                    }
                });
            },
            changeRole (value, activeType) {
                const self = this;
                console.log(value);
                this.$refs.editForm.parentId = value;
            }
        }
    }
</script>
