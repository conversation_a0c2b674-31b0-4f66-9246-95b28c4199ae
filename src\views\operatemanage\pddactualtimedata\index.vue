<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="店铺概览" name="first1" style="height: 100%">
        <shopsummary ref="shopsummary" style="height: 100%"></shopsummary>
      </el-tab-pane>
      <el-tab-pane label="商品明细" name="first2" style="height: 100%">
        <goodsdetail ref="goodsdetail" style="height: 100%"></goodsdetail> 
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import goodsdetail from "./goodsdetail.vue";
import shopsummary from "./shopsummary.vue";

export default {
  name: "PddActualTimeDataIndex",
  components: {
    MyContainer, goodsdetail , shopsummary
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },
  async mounted() {

  },
  methods: {
    
  },
};
</script>

<style lang="scss" scoped>
</style>
