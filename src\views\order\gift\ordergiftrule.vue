<template>
  <container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="赠品规则:">
          <el-input v-model="filter.ruleNo" clearable />
        </el-form-item>
        <el-form-item label="平台:">
         <el-select filterable v-model="filter.platform" placeholder="请选择平台" @change="onchangeplatform" clearable style="width: 130px">
            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="店铺:">
          <el-select filterable v-model="filter.shopId" placeholder="请选择店铺" clearable style="width: 130px">
            <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item label="店铺:">
            <el-select v-model="filter.shopCode" placeholder="请选择" :clearable="true" :collapse-tags="true"  filterable>
              <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"/>
            </el-select>
          </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <el-form-item>
          <a href="../static/excel/order/赠品规则导入模板.xlsx" target="_blank"><el-button type="primary">下载导入模板</el-button></a>
        </el-form-item>
        <el-form-item>
            <el-button size="small" type="primary" @click="startImport">导入赠品规则</el-button>
        </el-form-item>
      </el-form>
    </template>
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :isSelectColumn="false"
                             :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
     </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
    </template>
     <el-dialog title="导入赠品规则" :visible.sync="dialogVisible" width="30%">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                                accept=".xlsx" :http-request="uploadFile" :file-list="fileList">
         <template #trigger>
            <el-button size="small" type="primary">选取赠品规则文件</el-button>
         </template>
         <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </container>
</template>
<script>
import { platformlist} from '@/utils/tools'
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import container from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { importOrderGiftRule, pageOrderGiftRule} from "@/api/order/ordergoods";
const tableCols =[
      {istrue:true,prop:'ruleNo',label:'规则编号', width:'80',sortable:'custom'},
      {istrue:true,prop:'ruleName',label:'规则名称', width:'250',sortable:'custom'},
      {istrue:true,prop:'shopCodes',label:'店铺', width:'*',sortable:'custom',formatter:(row)=> row.shopNames||''},
      {istrue:true,prop:'giftGoodsCode',label:'赠品', width:'100',sortable:'custom'},
      {istrue:true,prop:'buyContainsGoodsCode',label:'购买包含其中任何商品', width:'250',sortable:'custom'},
      {istrue:true,prop:'createdTime',label:'添加时间', width:'150',sortable:'custom'},
     ];
const tableHandles1=[
        {label:"导入",permission:"api:order:ordergoods:ImportOrderGiftRuleAsync", handle:(that)=>that.startImport()},
      ];
export default {
  name: "Users",
  components: { container, MyConfirmButton, MySearch, MySearchWindow,cesTable },
  data() {
    return {
      that:this,
      filter: {
        ruleNo: "",
        shopCode: ""
      },
      tableCols:tableCols,
      tableHandles:tableHandles1,
      platformlist:platformlist,
      list: [],
      shopList: [],
      total: 0,
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      dialogVisible: false,
      userNameReadonly: true,
      fileList:[],
      importFilte:{companyid:null,warehouse:null},
      pager:{OrderBy:" RuleNo ",IsAsc:false},
    };
  },
  async mounted() {
    await this.onSearch();
  },
  methods: {
    async onchangeplatform(val){
      const res = await getshopList({platform:val,CurrentPage:1,PageSize:1000});
      this.shopList=res.data.list||[];
      this.shopList.push({shopCode:"{线下}",shopName:"{线下}"});
      this.filter.shopCode="";
    },
    startImport(){
      this.fileList = []
      this.dialogVisible=true;
    },
    cancelImport(){
      this.dialogVisible=false;
    },
    beforeRemove() {
      return false;
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
        this.$refs.upload.submit();
    },
    uploadFile(item) {
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      const res = importOrderGiftRule(form);
      this.$message({message: '上传成功,正在导入中...',type: "success"});
    },
    // 查询
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      var pager = this.$refs.pager.getPager();
      const params = {...pager,...this.pager,...this.filter};
      this.listLoading = true;
      const res = await pageOrderGiftRule(params);
      this.listLoading = false;
      if (!res?.success) return;
      this.total = res.data.total;
      const data = res.data.list;
      data.forEach((d) => {
        d._loading = false;
      });
      this.list = data;
    },
      //排序查询
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else{
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    },
    // 选择
    onSelsChange(sels) {
      this.sels = sels;
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
