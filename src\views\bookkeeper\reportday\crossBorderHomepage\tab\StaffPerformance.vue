<template>
    <MyContainer style="height: 98%;">
        <template #header>
            <div class="top">
                <el-date-picker v-model="ListInfo.timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 5px;" :clearable="false" :value-format="'yyyy-MM-dd'"
                    @change="changeTime">
                </el-date-picker>
                <!-- <el-select v-model="ListInfo.userName" placeholder="人员" filterable clearable class="publicCss">
                    <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.label" />
                </el-select> -->
                <el-select style="width: 180px;margin-right: 5px;" v-model="ListInfo.userId" placeholder="人员" class="el-select-content"
                                filterable  clearable collapse-tags>
                                <el-option v-for="item in groupNameList" :key="item.key" :label="item.label"
                                :value="item.key" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                
                <el-button type="primary" @click="onExport()" v-if="checkPermission(['ExportCrossBorderHomePage_Kj'])">导出</el-button>
            </div>
        </template>
        <vxetablebase :id="'crossBorderHomePage_index202408041650'" ref="table" :that='that' :isIndex='true'
            :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" :showsummary="true" :summaryarry="summaryarry"
            style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'" @summaryClick='onsummaryClick'>
            <template slot="right">
                <vxe-column width="60">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <el-button type="text" @click="onTrendChart(row, null)">趋势图</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="趋势图" :visible.sync="buscharDialog.visible" width="70%" v-dialogDrag append-to-body>
            <div>
                <buschar v-if="buscharDialog.visible" ref="refbuschar" :analysisData="buscharDialog.data"></buschar>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">取 消</el-button>
            </div>
        </el-dialog>

        <el-dialog :visible.sync="buscharDialogSum.visible" width="80%" v-dialogDrag>
            <span>
                <buschar ref="buschar" v-if="buscharDialogSum.visible" :analysisData="buscharDialogSum.data"
                    :loading="buscharDialogSum.loading"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialogSum.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getDirectorList, getList as getshopList } from '@/api/operatemanage/base/shop'
import { pickerOptions } from '@/utils/tools'
import buschar from '@/components/Bus/buschar'
import dayjs from 'dayjs'
import { getCrossBorderHomePage_Kj, importTemuListingAsync, getTemuExpressPackage, addOrEditTemuExpressPackage, getCrossBorderHomePage_KjAnalysis,getDirectorGroupNameList,exportCrossBorderHomePage_Kj } from '@/api/bookkeeper/crossBorderV2'
const tableCols = [
    { width: 'auto', align: 'center', prop: 'userName', label: '人员', },
    { sortable: 'custom', summaryEvent: true, width: 'auto', align: 'center', prop: 'orderCount', label: '订单量', type: 'click', handle: (that, row, field, val) => that.onTrendChart(row, field, val), },
    { sortable: 'custom', summaryEvent: true, width: 'auto', align: 'center', prop: 'saleAmount', label: '订单销售金额', type: 'click', handle: (that, row, field, val) => that.onTrendChart(row, field, val), },
    { sortable: 'custom', summaryEvent: true, width: 'auto', align: 'center', prop: 'saleCost', label: '订单销售成本', type: 'click', handle: (that, row, field, val) => that.onTrendChart(row, field, val), },
    { sortable: 'custom', summaryEvent: true, width: 'auto', align: 'center', prop: 'profit3', label: '毛三利润', type: 'click', handle: (that, row, field, val) => that.onTrendChart(row, field, val), },
    { sortable: 'custom', summaryEvent: true, width: 'auto', align: 'center', prop: 'profit3Rate', label: '毛三利润率', type: 'click', handle: (that, row, field, val) => that.onTrendChart(row, field, val), },
]
export default {
    name: "index",
    components: {
        MyContainer, vxetablebase, buschar
    },
    data() { 
        return {
            expenseForm: {
                express: undefined,//快递费
                packageFree: undefined,//打包费
            },
            costSettingPopup: false,//费用设置弹窗
            fileList: [],//上传文件列表
            postData: [],//地区岗位
            fileparm: {},//上传文件参数
            dialogVisible: false,//导入弹窗
            uploadLoading: false,//上传loading
            directorlist: [],//人员
            buscharDialog: {
                visible: false,//趋势图弹窗 
                data: {},//趋势图数据
            },
            buscharDialogSum: {
                visible: false,//趋势图弹窗
                data: {},//趋势图数据
            },
            summaryarry: {},//汇总 
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
                //   district: [],//地区
                //   post: [],//岗位
                //   platform: null,//平台
                // userName: null,//人员
                column: null,//列
                timeRanges: [],//时间范围
                //   employeeStatus:1
            },
            // timeRanges: [],//时间范围
            tableCols,//表头
            tableData: [],//表格数据
            total: 0,//总数
            loading: false,//加载中
            pickerOptions,//时间选择器配置
            //人员列表
            groupNameList:[],
        }
    },
    async mounted() {
        const GroupName = await getDirectorGroupNameList();
        this.groupNameList = GroupName.data?.map(item => {
            return {
                label: item.userName,
                value: [
                    item.id ? item.id : '',
                    item.userId ? item.userId : '',
                    item.userName ? item.userName : '',
                ].filter(Boolean).join(','), // 拼接 id 和 userId，用逗号分隔
                key: item.userId,

            }
        });
        await this.init()
        await this.getList()
    },
    methods: {
        datetostr(date) {
            var y = date.getFullYear();
            var m = ("0" + (date.getMonth() + 1)).slice(-2);
            var d = ("0" + date.getDate()).slice(-2);
            return y + "-" + m + "-" + d;
        },  
        async init() {
            var date1 = new Date(); date1.setDate(date1.getDate() - 8);
            var date2 = new Date(); date2.setDate(date2.getDate() - 1);
            this.ListInfo.timeRanges = [];
            this.ListInfo.timeRanges[0] = this.datetostr(date1);
            this.ListInfo.timeRanges[1] = this.datetostr(date2);

    },      
        async onTrendChart(row, field, val) {
            let startTimes = null
            let endTimes = null
            if (this.ListInfo.startTime && this.ListInfo.endTime) {
                startTimes = this.ListInfo.startTime
                endTimes = this.ListInfo.endTime
                if (dayjs(endTimes).diff(dayjs(startTimes), 'month') < 1) {
                    startTimes = dayjs(endTimes).subtract(1, 'month').format('YYYY-MM-DD')
                }
            }
            const params = {
                column: field ? field.prop : null,
                userName: row.userName,
                startTime: startTimes,
                endTime: endTimes
            }
            const { data, success } = await getCrossBorderHomePage_KjAnalysis(params)
            if (!success) return
            let that = this
            that.buscharDialog.visible = true;
            that.buscharDialog.data = data
            that.buscharDialog.title = data.legend[0]
            that.$nextTick(() => {
                that.$refs.refbuschar.initcharts();
            });
        },

        async changeTime(e) {
            this.ListInfo.startTime = e ? e[0] : null
            this.ListInfo.endTime = e ? e[1] : null
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            if (this.ListInfo.timeRanges) {
                // 默认给近7天时间

                // this.ListInfo.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
                this.ListInfo.startTime=this.ListInfo.timeRanges[0]
                this.ListInfo.endTime=this.ListInfo.timeRanges[1]
            }
            this.loading = true
            const { data, success } = await getCrossBorderHomePage_Kj(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        async onsummaryClick(property) {
            let startTimes = null
            let endTimes = null
            if (this.ListInfo.startTime && this.ListInfo.endTime) {
                startTimes = this.ListInfo.startTime
                endTimes = this.ListInfo.endTime
                if (dayjs(endTimes).diff(dayjs(startTimes), 'month') < 1) {
                    startTimes = dayjs(endTimes).subtract(1, 'month').format('YYYY-MM-DD')
                }
            }
            var pager = this.$refs.pager.getPager();
            const params = {
                ...this.ListInfo,
                startTime: startTimes,
                endTime: endTimes
            };
            params.column = property;
            let that = this;
            that.listLoading = true;
            that.buscharDialogSum.loading = true;
            const res = await getCrossBorderHomePage_KjAnalysis(params).then(res => {
                that.buscharDialogSum.loading = false;
                that.buscharDialogSum.data = res.data;
                that.buscharDialogSum.title = res.data.legend[0];
            });
            that.listLoading = false;
            that.buscharDialogSum.visible = true;
            await that.$refs.buschar.initcharts();

        },
            async onExport() {
            this.loading = true
            var res = await exportCrossBorderHomePage_Kj(this.ListInfo);
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.setAttribute(
                "download",
                "运营人员业绩_" + new Date().toLocaleString() + ".xlsx"
            );
            aLink.click();
            this.loading = false
            },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 190px;
        margin-right: 5px;
    }
}

::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>