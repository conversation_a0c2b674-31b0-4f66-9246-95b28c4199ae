<template>
    <my-container>
        <template #header>
        </template>
        <div style="height:550px;">
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles'
            :isSelectColumn="true" :loading="listLoading" @cellclick="cellclick">
            <el-table-column v-if="checkPermission('productnewpermission')" :width="500" label="表格" fixed="right">
                <template slot="header" slot-scope="scope">
                    <div class="table-div" ref="tableColumn">

                        <el-link v-for="(todo, index) in searchColumn" :underline="false" v-bind:class="{
                            ascending: (index == searchIndex && searchOrder == 'ascending'),
                            descending: (index == searchIndex && searchOrder == 'descending')
                        }"
                            @click="tableColumnClick(todo.value, index)">{{ todo.text }}
                            <span class="caret-wrapper">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span>
                        </el-link>

                    </div>
                </template>
                <template slot-scope="props">
                    <el-table :data="props.row.series" :show-header="false" border style="width: 100%">
                        <el-table-column prop="name" label="姓名" width="80"></el-table-column>
                        <el-table-column label="7天数据" width="auto">
                            <template slot-scope="scope">
                                <el-col :span="3" v-for="(ii, num) in scope.row.data" :key="num">
                                    <template>
                                        <div v-if="scope.row['name'] == '广告访客量'" @click="clicknum(scope.row, ii)"
                                            style="color: red;">{{ ii }}</div>
                                        <div v-else> {{ ii }} </div>
                                    </template>
                                </el-col>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </el-table-column>
            <el-table-column v-if="checkPermission('productnewpermission')" :width="250" label="图表" fixed="right">
                <template slot-scope="scope">
                    <div style="height: 120px;width:15vw;margin-left: -50px;" :ref="'echarts' + scope.row.proCode"
                        v-loading="echartsLoading"></div>
                </template>
            </el-table-column>
            <el-table-column width="120px" label="操作" fixed="right">
                <template slot-scope="scope">
                    <el-select  v-model="scope.row.yearMonthDay" @change="moveProductType(scope.row)"  collapse-tags clearable placeholder="请选择操作"
                          style="width: 100px">
                          <!-- <el-option label="移至关注" :value="11"/> -->
                          <el-option label="移至爆款" :value="22"/>
                          <el-option label="移至任务中心" :value="33" />
                        </el-select>
                </template>
            </el-table-column>
        </ces-table>
        </div>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <template>
                    <el-form class="ad-form-query" :model="detailfilter" @submit.native.prevent label-width="100px">
                        <el-row>
                            <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                                <el-form-item label="日期:">
                                    <el-date-picker style="width: 260px" v-model="detailfilter.timerange"
                                        type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                        :clearable="false"></el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                                <el-form-item>
                                    <el-button type="primary" @click="getecharts">刷新</el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </template>
            </span>
            <span>
                <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <!-- <el-dialog title="选择接收人1"  @close="closediolag" :visible.sync="dialogChooseRecipientVisible" height="100px" v-dialogDrag>
             <el-row>
                <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="24">

                   <el-select  v-model="dialogfilter.Majordomo"  collapse-tags clearable placeholder="运营总监"
                   style="width: 120px;margin-left: 150px;">
                   <el-option label="汪大侠" :value="lables"/>
                    </el-select>



                   <el-select filterable v-model="dialogfilter.groupId" collapse-tags clearable placeholder="运营组长"
                           style="width: 120px;margin-left: 50px;">
                           <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                           <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.label" />
                     </el-select>



                   <el-select filterable v-model="dialogfilter.operateSpecialUserId" collapse-tags clearable placeholder="运营专员"
                     style="width: 120px;margin-left: 50px;">
                     <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.label" />
                     </el-select>



                   <el-select filterable v-model="dialogfilter.userId" collapse-tags clearable placeholder="运营助理"
                   style="width: 120px;margin-left: 50px;">
                   <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.label" />
                   </el-select>
                </el-col>

                <div style="display: flex; flex-direction: row; height: 70px; width: 300vw; background-color: red;">
            <el-button style="margin-left: auto;" type="primary" @click="Notarize" >确认</el-button>
            <el-button  type="primary" @click="dialogclosds" >取消</el-button>
           </div>

            </el-row>



        </el-dialog> -->

        <el-dialog title="选择接收人" @close="closediolag" :visible.sync="dialogChooseRecipientVisible" width="1100px" height="100px" v-dialogDrag>
            <el-form :model="form" ref="form" label-width="120px" label-position="right" >
             <el-row justify="center">

                <el-col :span="6" >
                    <el-form-item label="接收人：" :rules="[
                    { required: true, message: '请选择接收人', trigger: ['blur', 'change'] }
                    ]">
                    <el-select filterable v-model="dialogfilter.userId" collapse-tags clearable placeholder="接收人"
                    style="width: 120px;">
                    <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.label" />
                    </el-select>
                </el-form-item>
                </el-col>
            </el-row>
            <el-row style="margin-top: 10px;">
                <el-col :span="24" >
                    <el-form-item label="资料：" :rules="[
                    { required: true, message: '请填写资料', trigger: ['blur'] }
                    ]">
                     <yh-quill-editor :value.sync="imageFath" @imgget="imggetfuc"></yh-quill-editor>
                    </el-form-item>
                 </el-col>
            </el-row>

            <div style="display: flex; flex-direction: row; height: auto; width: 100%; margin-top: 20px;">
            <el-button style="margin-left: auto;" type="primary" @click="UpdateSendMessage" >确认</el-button>
            <el-button  type="primary" @click="dialogclosds" >取消</el-button>
           </div>
        </el-form>
        </el-dialog>






    </my-container>
</template>

<script>
import { getDirectorList, getDirectorGroupList, getProductBrandPageList, getList as getshopList } from '@/api/operatemanage/base/shop'
import { platformlist } from '@/utils/tools'
import { formatLinkProCode } from "@/utils/tools";
import { getAllProBrand } from '@/api/inventory/warehouse'
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import * as echarts from 'echarts'
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { pageProductNewAsync, queryGuardProductNewsis, getProductStateName, getProductAdveClick,pageProductAttentionReport ,editProductAttention} from '@/api/operatemanage/base/product'
import { getRateDetailList } from "@/api/customerservice/productconsulting";
import { addtrainplan } from "@/api/customerservice/trainplan";
import { getcusgroups, } from "@/api/customerservice/customergroup";
import cesTable from "@/components/Table/table.vue";
import buschar from '@/components/Bus/buschar'
import personstatistics from '../../customerservice/personstatistics.vue'
import trainresourceupload from "@/views/customerservice/trainresourceupload.vue"
import { mapGetters } from 'vuex'
import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue';

const tableCols = [
    { istrue: true, prop: 'proCode', label: '关注商品ID', width: '105',  type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
    { istrue: true, prop: 'title', label: '关注商品名称', width: '110',  },
    { istrue: true, prop: 'shopName', label: '店铺', width: '120', },
    { istrue: true, prop: 'onTime', label: '上架时间', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.onTime, "YYYY-MM-DD") },
     { istrue: true, prop: 'newPattern', label: '上新模式', sortable: 'custom', width: '90', type: 'custom' },
    { istrue: true, prop: 'images', label: '关注商品图片', width: '120', type: "image" },
    { istrue: true, prop: 'hotCake', label: '状态', width: '100', formatter: (row) =>row.hotCake==22&&row.attention==11&&row.task==33?"爆款、关注、任务中心":row.hotCake==22&&row.attention==11?"爆款、关注":row.hotCake==22&&row.task==33?"爆款、任务中心":row.attention==11&&row.task==33?"关注、任务中心":row.hotCake==22?"爆款":row.attention==11?"关注":row.task==33?"任务中心":"" },
];
const tableHandles1 = [];
const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");
const star = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminProductnew',
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, buschar, personstatistics, trainresourceupload,YhQuillEditor },
    filters: {
        myTime(val) {
            return formatTime(val, "YYYY-MM-DD")
        }
    },
    props: {
        filter: {},
        filterDetail: {}
    },
    data() {
        return {
            imageFath:null,
            lables:"汪大侠",
            dialogfilter:{
                Majordomo:null,
                groupId:null,
                operateSpecialUserId:null,
                userId:null
            },
            Notarizelist:[],
            dialogChooseRecipientVisible:false,
            that: this,

            searchOrder: "",
            searchIndex: -1,
            searchColumn: [
                { text: '搜索', value: 'searchVisitorNumber' },
                { text: '总访客量', value: 'orderCount' },
                { text: '广告访客量', value: 'adveNumber' },
                { text: '净利', value: 'profit4' },
                { text: '毛利', value: 'profit3' },
                { text: '支付买家数', value: 'payBuyNumber' },
            ],
            detailfilter: {
                procode: null,
                platform: null,
                startTime: null,
                endTime: null,
                timerange: [star, endTime]
            },
            Filter: {
                StartDate: null,
                EndDate: null,
                timerange: [startTime, endTime]
            },
            personpager: {
                OrderBy: "rensuccess",
                pageSize: 200,
                pageIndex: 1,
                IsAsc: false,
            },
            amontDialog: { visible: false, rows: [] },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "orderCount", IsAsc: false },
            tableCols: tableCols,
            tableHandles: tableHandles1,
            platformlist: platformlist,
            platformList: [],
            grouplist: [],
            brandlist: [],
            directorlist: [],
            productnewList: [],
            cusgroupslist: [],
            shopList: [],
            directorGroupList: [],
            opList: [],
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            echartsLoading: false,
            isshowstate: false,
            everyPersonVisible: false,
            resourcedialogVisible: false,
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                },
            },
            buscharDialog: { visible: false, title: "", data: [] },
        };
    },

    async mounted() {
        await this.getlist();
        await this.getDirectorlist()
        await this.init()
        await this.getPruductNewState()
        await this.getProductCustomer()
        await this.getGroupList()
    },
    created(){
        this.getGroupList()
    },
    computed:{
    ...mapGetters([
      'menus',
      'userName',
      'avatar'
    ]),
  },
    methods: {
        async UpdateSendMessage(){
             if(this.imageFath==null)
             {

                this.$message.error('请输入相关资料！！！！！！');
                return;
             }
             if(this.dialogfilter.userId==null||this.dialogfilter.userId==""||this.dialogfilter.userId==0)
             {

                this.$message.error('请选择接收人！！！！！！');
                return;
             }
            this.$confirm('确定要执行此操作吗, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
            }).then(async() => {
                this.Notarizelist.AppointUserName=this.dialogfilter.userId;
                this.Notarizelist.RelevantData=this.imageFath;
             const res = await editProductAttention(this.Notarizelist);

            if(res.code==1){

                this.$message.success('保存成功！');
                this.getlist();
                this.imageFath = null
                this.dialogfilter.userId=null
                this.dialogChooseRecipientVisible=false
            }else{
                this.imageFath = null
                this.dialogfilter.userId=null
                this.dialogChooseRecipientVisible=false
            }
            }).catch(() => {
            this.$message({
                type: 'info',
                message: '已取消操作'
            });
            });
        },
        closediolag(){
		            this.list.map((data)=>{

		                        data.yearMonthDay = ''

		                })
		        },
        dialogclosds(){
            this.imageFath = null
            this.dialogfilter.userI=null
            this.dialogChooseRecipientVisible=false;

        },
        async Notarize(){
            this.$confirm('确定要添加吗, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(async() => {
                if(this.dialogfilter.operateSpecialUserId!=null&&this.dialogfilter.userId!=null)
                    {

                        this.Notarizelist.AppointUserName= this.dialogfilter.operateSpecialUserId

                    }
                    else if(this.dialogfilter.operateSpecialUserId!=null)
                    {

                        this.Notarizelist.AppointUserName= this.dialogfilter.operateSpecialUserId

                    }
                    else if(this.dialogfilter.userId!=null)
                    {

                        this.Notarizelist.AppointUserName= this.dialogfilter.userId
                    }
                    else if(this.dialogfilter.groupId!=null)
                    {

                        this.Notarizelist.AppointUserName= this.dialogfilter.groupId
                    }
                    else if(this.dialogfilter.Majordomo!=null)
                    {

                        this.Notarizelist.AppointUserName= this.dialogfilter.Majordomo
                    }
                    else if(this.dialogfilter.Majordomo!="汪大侠"&&this.dialogfilter.userId==null&&this.dialogfilter.operateSpecialUserId==null&&this.dialogfilter.groupId==null)
                    {
                        this.$message.error('请选择接收人！！');
                       return;
                    }

                    if(this.dialogfilter.Majordomo!=null)
		                    {

		                        this.Notarizelist.AppointUserName=this.dialogfilter.Majordomo
		                    }



                const res = await editProductAttention(this.Notarizelist);
                if(res.code==1){
                    this.$message.success('添加成功！');
                    this.dialogChooseRecipientVisible=false;
                    this.list.map((data)=>{

                            data.yearMonthDay = ''

                    })
                    this.dialogfilter.groupId=null
                    this.dialogfilter.userId=null
                    this.dialogfilter.Majordomo=null
                    this.dialogfilter.operateSpecialUserId=null
                    this.getlist();
                }
              }).catch(() => {
                this.list.map((data)=>{

                            data.yearMonthDay = ''

                    })
                    this.dialogfilter.groupId=null
                    this.dialogfilter.userId=null
                    this.dialogfilter.Majordomo=null
                    this.dialogfilter.operateSpecialUserId=null
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
              });


    },
    async getGroupList() {

      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });


    },
        async moveProductType(val){
                if(val.yearMonthDay==33)
                {
                    this.dialogChooseRecipientVisible=true
                  if(!this.userName.includes("助理")&&!this.userName.includes("专员"))
                  {

                    if(val.userRealName!=null&&val.operateSpecialUserName!=null)
                    {

                        this.dialogfilter.userId=val.operateSpecialUserName


                    }
                    else if(val.operateSpecialUserName!=null&&val.userRealName==null)
                    {

                        this.dialogfilter.userId=val.operateSpecialUserName


                    }
                    else if(val.operateSpecialUserName==null&&val.userRealName!=null)
                    {

                        this.dialogfilter.userId=val.userRealName
                    }

                  }

                    this.Notarizelist=val;
                }

                else{
                    const res = await editProductAttention(val);
                                console.log("111",res)
                                if(res.code==1){
                                    this.$message.success('添加成功！');
                                    this.getlist();
                                    this.list.map((data)=>{
                                        if(data == val){
                                            data.yearMonthDay = ''
                                        }
                                    })
                                    this.addVisible=false;
                                    this.dialogfilter.groupId=null
                                    this.dialogfilter.userId=null
                                    this.dialogfilter.Majordomo=null
                                    this.dialogfilter.operateSpecialUserId=null
                                }else{
                                    this.list.map((data)=>{
                                        if(data == val){
                                            data.yearMonthDay = ''
                                        }
                                    })
                                    this.dialogfilter.groupId=null
                                    this.dialogfilter.userId=null
                                    this.dialogfilter.Majordomo=null
                                    this.dialogfilter.operateSpecialUserId=null
                                }


                }






      },
        async getPruductNewState() {
            var res = await getProductStateName();
            if (res?.code) {
                this.productnewList = res.data.map(function (item) {
                    var ob = new Object();
                    ob.state = item;
                    return ob;
                })
            }
        },
        async getProductCustomer() {
            var g = await getcusgroups({});

            this.cusgroupslist = g.data.list.map(function (item) {
                var ob = new Object();
                ob.state = item;
                return ob;
            });
        },

        async onchangeplatform(val) {
            this.categorylist = []
            const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
        },
        async init() {
            var res = await getAllProBrand();
            this.brandlist = res.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            this.filter.dataStartTime = null;
            this.filter.dataEndTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            if (this.filter.timerange1) {
                this.filter.dataStartTime = this.filter.timerange1[0];
                this.filter.dataEndTime = this.filter.timerange1[1];
            }
            this.filter.ProductType=11;
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await pageProductAttentionReport(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
            this.getEcharts()
        },
        getEcharts() {
            setTimeout(_ => {
                this.list.forEach(e => {
                    let myChart = echarts.init(this.$refs['echarts' + e.proCode]);
                    var series = []
                    this.echartsLoading = true
                    e.series.forEach(s => {
                        if (s.name != '日期')
                            series.push({ smooth: true, showSymbol: false, ...s })
                    })
                    this.echartsLoading = false
                    myChart.setOption({
                        legend: {
                            show: false
                        },
                        grid: {
                            left: "0",
                            top: "6",
                            right: "0",
                            bottom: "0",
                            containLabel: true,
                        },
                        xAxis: {
                            type: 'category',
                            //不显示x轴线
                            show: false,
                            data: e.xAxis
                        },
                        yAxis: {
                            type: 'value',
                            show: false,
                        },
                        series: series
                    });
                    window.addEventListener("resize", () => {
                        myChart.resize();
                    });
                })
            }, 1000)
        },
        async cellclick(row, column, cell, event) {
            if (column.label == '图表') {
                this.detailfilter.procode = row.proCode
                this.detailfilter.platform = row.platform
                this.getecharts()
            }
        },
        async getecharts() {
            this.detailfilter.startTime = null;
            this.detailfilter.endTime = null;
            if (this.detailfilter.timerange) {
                this.detailfilter.startTime = this.detailfilter.timerange[0];
                this.detailfilter.endTime = this.detailfilter.timerange[1];
            }
            var params = { ...this.detailfilter, isMedia: false }
            let that = this;
            const res = await queryGuardProductNewsis(params).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data
                that.buscharDialog.title = res.data.legend[0]
            })
            await this.$refs.buschar.initcharts()
        },

        async changelist(e) {
            this.list = e
        },
        async clicknum(num, ii) {
            var res = await getProductAdveClick({ proCode: num.yearMonthDay })
            this.amontDialog.visible = true;
            this.amontDialog.rows = res.data;
        },
        tableColumnClick(orderColumn, index) {
            let column = {};
            column.prop = orderColumn;
            let currentNode = this.$refs.tableColumn.children[index];
            let className = currentNode.className;
            if (className.indexOf('ascending') > -1) {
                column.order = "descending";
            } else if (className.indexOf('descending') > -1) {
                column.order = "ascending";
            }
            else {
                column.order = "ascending";
            }

            this.searchOrder = column.order;
            this.searchIndex = index;

            this.$refs.table.clearSort();
            this.sortchange(column);
        },
        sortchange(column) {
            if(column.column){
                this.searchIndex=-1;
            }
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selsChange: function (sels) {
            this.sels = sels
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
    },
};
</script>

<style lang="scss" scoped>
.table-div {
    display: inline-block;
    text-align: center;
    width: 100%;
}

.table-div>a {
    padding: 0 10px;
}

.table-div .el-link--inner span {
    left: -9px;
}
::v-deep img{
    width: 110px !important;
    height: 110px !important;
}
::v-deep .el-image__inner{
    max-height: 400px !important;
    max-width: 400px !important;
}
</style>
