<template>
    <div>
        <!-- 汇总 -->
        <vxetablebase ref="alreadyTable" :tableData="tableData" :tableCols="tableCols" :is-index="true" :that="that"
            :showsummary="true" style="width: 100%; height: 620px; margin: 0" @sortchange='sortchange'
            v-show="checkList.length == 0" :treeProp="{ rowField: 'disId', parentField: 'disParentId' }"
            :loading="listLoading" class="detail">
        </vxetablebase>
        <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="detailTotal"
            @page-change="detailPagechange" @size-change="detailSizechange" />

        <!-- 弹层部分 -->
        <el-dialog title="对接记录" :visible.sync="RecordsVisible" width="60%" :before-close="handleClose1" v-dialogDrag>
            <cesTable :id="'vendorSummary202408041624'" ref="detailTable" :tableData="doTableData" :tableCols="tableCols4" :is-index="true" :that="that"
                :showsummary="true" style="width: 100%; height: 500px" @sortchange='sortchange2' class="detail">
            </cesTable>
            <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="recordsTotal"
                @page-change="dockingRecordsPagechange" @size-change="dockingRecordsSizechange" style="margin-top: 40px;" />
        </el-dialog>

        <!-- 点击供应商名字 -->
        <el-dialog title="采购记录" :visible.sync="nameVisible" width="40%" :before-close="handleClose" v-dialogDrag>
            <vxetablebase ref="detailTable" :tableData="nameTableData" :tableCols="tableCols5" :is-index="true" :that="that"
                :showsummary="true" :summaryarry="nameSummary" style="width: 100%; height: 500px" @sortchange='sortchange3'>
            </vxetablebase>
        </el-dialog>
    </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
import {
    getProviderQuotationHisRecordPageList,
    GetProviderDockingResultList,
    getProviderNewGoodsDockingResultList,
    getProviderQuotationRecordOverViewPageList,
}
    from '@/api/openPlatform/ProviderQuotation'
import { pagePurchaseOrderByProviderNameAsync } from '@/api/inventory/purchase'
import { getAllProBrand } from '@/api/inventory/warehouse'
const options = [
    {
        value: '1',
        label: '是'
    },
    {
        value: '0',
        label: '否'
    }
]

const positionType = [
    {
        label: '老板'
    },
    {
        label: '业务员'
    },
    {
        label: '经理'
    }
]

const sourceType = [
    {
        label: '朋友圈'
    },
    {
        label: '聊天'
    },
    {
        label: '其他'
    }
]

//汇总
const tableCols = [
    {
        istrue: true, prop: 'setStylePic', label: '系列编码图片', type: 'treeimages', treeNode: true, isImgtree: true, width: 100, fixed: 'left', formatter: (row) => {
            if (row.setStylePic) {
                return row.setStylePic
            } else {
                return row.stylePic
            }
        }
    },
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: 95, fixed: 'left' },
    { istrue: true, prop: 'goodCode', label: '商品编码', sortable: 'custom', width: 95 },
    { istrue: true, prop: 'goodName', label: '商品名称', sortable: 'custom', width: 95 },
    { istrue: true, prop: 'goodPic', label: '商品图片', width: 95, type: 'images' },
    { istrue: true, prop: 'brandName', label: '采购', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'costPrice', label: '成本价', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'sourceName', label: '推荐人', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'sourceType', label: '来源', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'providerName', label: '供应商名称', type: 'treeStar1', sortable: 'custom', width: 130, style: "color: rgb(72, 132, 243);cursor:pointer;", handle: (that, row) => that.openNameDialog(row) },
    { istrue: true, prop: 'phone', label: '联系电话', sortable: 'custom', width: 95, },
    {
        istrue: true, prop: 'isWX', label: '微信是否同号', width: 120, sortable: 'custom', formatter: (row) => {
            if (row.disParentId != 0) {
                return row.isWX == 1 ? '是' : '否'
            } else {
                return ''
            }
        }
    },
    { istrue: true, prop: 'position', label: '职位', width: 70, sortable: 'custom' },
    {
        istrue: true, prop: 'isBY', label: '是否包邮', width: 95, sortable: 'custom', formatter: (row) => {
            if (row.disParentId != 0) {
                return row.isBY == 1 ? '是' : '否'
            } else {
                return ''
            }
        }
    },
    {
        istrue: true, prop: 'isContaisTax', label: '是否含税', width: 95, sortable: 'custom', formatter: (row) => {
            if (row.disParentId != 0) {
                return row.isContaisTax == 1 ? '是' : '否'
            } else {
                return ''
            }
        }
    },
    {
        istrue: true, prop: 'sheng', label: '发货地址', width: 95, sortable: 'custom', formatter: (row) => {
            if (row.sheng != null) {
                return row.sheng + row.shi + row.qu
            } else {
                return ''
            }
        }
    },
    { istrue: true, prop: 'quotation1', label: '进货量100报价', width: 130, sortable: 'custom', type: 'changeColor', formatter: (row) => row.quotation1 > row.costPrice },
    { istrue: true, prop: 'quotation2', label: '进货量10000报价', width: 150, sortable: 'custom', type: 'changeColor', formatter: (row) => row.quotation2 > row.costPrice },
    { istrue: true, prop: 'quotation3', label: '进货量100000报价', width: 170, sortable: 'custom', type: 'changeColor', formatter: (row) => row.quotation3 > row.costPrice },
    { istrue: true, prop: 'remark', label: '备注', sortable: 'custom', width: 80 },
    { istrue: true, prop: 'modifiedTime', label: '最后填写日期', sortable: 'custom', width: 150 },
    {
        istrue: true, prop: 'address', label: '归属地', sortable: 'custom', width: 150, formatter: (row) => {
            if (row.address != null || row.ip != null) {
                return row.address + `(${row.ip})`
            } else {
                return ''
            }
        }
    },
    { istrue: true, prop: 'dockingBrandName', label: '对接人', sortable: 'custom', width: 120 },
    { istrue: true, prop: 'dockingStatus', label: '对接状态', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'dockingCount', label: '对接次数', sortable: 'custom', width: 120, type: 'click', fixed: 'right', handle: (that, row) => that.dockingRecords(row, 0) },
]
//对接记录
const tableCols4 = [
    { istrue: true, prop: 'createdUserName', label: '对接人', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '对接时间', sortable: 'custom' },
    { istrue: true, prop: 'dockingStatus', label: '对接状态', sortable: 'custom' },
    { istrue: true, prop: 'result', label: '对接结果', sortable: 'custom' },
    { istrue: true, prop: 'picJsons', label: '图片', type: 'images' },
]
//采购记录
const tableCols5 = [
    { istrue: true, prop: 'purchaseDate', label: '采购时间', sortable: 'custom' },
    { istrue: true, prop: 'buyNo', label: '采购单号', sortable: 'custom' },
    { istrue: true, prop: 'totalAmont', label: '采购金额', sortable: 'custom' },
    { istrue: true, prop: 'count', label: '采购量', sortable: 'custom' },
]

const status = [
    {
        label: '待沟通',
        value: '待沟通'
    },
    {
        label: '沟通中',
        value: '沟通中'
    },
    {
        label: '寄样中',
        value: '寄样中'
    },
    {
        label: '采购中',
        value: '采购中'
    },
    {
        label: '采购完成',
        value: '采购完成'
    },
    {
        label: '不适合',
        value: '不适合'
    },
]
export default {
    components: { MyContainer, cesTable, vxetablebase },
    name: "vendorSummary",
    props: {
        ListInfo: {
            type: Object,
            default: () => { }
        },
        isHidden: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            that: this,
            logDetail: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                openId: null,//openId
                styleCode: null,//系列编码
                orderBy: null,//排序字段
                isAsc: true,//是否升序
            },
            RecordsInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                recordId: null,//记录id
                newGoodsRecordId: null,//新品记录id
                isDockingCG: 0,//是否对接采购 0已提交 1未提交
                styleCode: null,//系列编码
                openId: null,//openId
                result: null,//对接结果
                dockingStatus: null,//对接状态
                pics: null,
                picLists: []//图片列表
            },//对接记录请求参数
            nameInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                supplier: null,//供应商名称
            },
            sourceType,//来源
            positionType,//职位
            tableCols,//已提交
            tableCols4,//对接记录
            tableCols5,//点击供应商名字
            tableData: [],//已提交
            tableData1: [],//详情
            xltableData: [],//系列编码
            newTableData: [],//新品提交
            doTableData: [],//对接记录
            nameTableData: [],//点击供应商名字
            brandList: [],//采购列表
            logTotal: 0,//详情总数
            detailTotal: 0,//已提交总数
            nameTotal: 0,//供应商报价详情总数
            listLoading: true,//加载中
            dialogVisible: false,//详情弹层
            RecordsVisible: false,//对接记录弹层
            operateVisible: false,//操作弹层
            nameVisible: false,//点击供应商名字弹层
            recordsTotal: 0,//对接记录总数
            options,//是否包邮,是否含税
            status,//状态
            procurementList: [],//采购人员列表
            checkList: [],
            nameSummary:null
        };
    },
    mounted() {
        this.getAlreadyList()
        this.getBrandList()
    },
    methods: {
        //折叠树
        foldTree() {
            this.$refs.alreadyTable.$refs.xTable.clearTreeExpand()
        },
        //展开树
        unfoldTree() {
            const foldTree = this.tableData.filter(item => item.disParentId == 0)
            this.$refs.alreadyTable.$refs.xTable.setTreeExpand(foldTree, true)
        },
        //获取采购列表
        async getBrandList() {
            const { data, success } = await getAllProBrand()
            if (success) {
                this.brandList = data.map(item => {
                    return {
                        label: item.value,
                        value: item.key
                    }
                })
            } else {
                this.$message.error('获取采购人员列表失败')
            }
        },
        //点击供应商名称打开弹层
        async openNameDialog(row) {
            this.nameInfo.supplier = row.providerName ? row.providerName : row.supplier
            const { data, success } = await pagePurchaseOrderByProviderNameAsync(this.nameInfo)
            if (success) {
                this.nameTableData = data.list
                this.nameTotal = data.total
                this.nameSummary = data.summary
                this.nameVisible = true
                this.nameInfo.orderBy = null
            } else {
                this.$message.error('获取供应商采购记录失败')
            }
        },
        //对接记录弹层每页数量改变
        nameSizechange(val) {
            this.nameInfo.currentPage = 1;
            this.nameInfo.pageSize = val;
            this.openNameDialog(this.nameInfo)
        },
        //对接记录弹层当前页改变
        namePagechange(val) {
            this.nameInfo.currentPage = val;
            this.openNameDialog(this.nameInfo)
        },
        //对接记录弹层每页数量改变
        dockingRecordsSizechange(val) {
            this.RecordsInfo.currentPage = 1;
            this.RecordsInfo.pageSize = val;
            this.dockingRecords(this.RecordsInfo)
        },
        //对接记录弹层当前页改变
        dockingRecordsPagechange(val) {
            this.RecordsInfo.currentPage = val;
            this.dockingRecords(this.RecordsInfo)
        },
        //汇总对接记录
        async dockingRecords(row) {
            this.RecordsInfo.picLists = []
            if (!this.RecordsInfo.recordId) {
                this.RecordsInfo.recordId = row.id
            }
            this.RecordsInfo.openId = row.openId
            this.RecordsInfo.styleCode = row.styleCode
            const { data, success } = await GetProviderDockingResultList(this.RecordsInfo)
            if (success) {
                this.doTableData = data.list
                this.recordsTotal = data.total
                this.RecordsInfo.orderBy = null
                this.RecordsVisible = true
            } else {
                this.$message.error('获取对接记录失败')
            }
        },
        handleClose() {
            this.dialogVisible = false
            this.RecordsVisible = false
            this.operateVisible = false
            this.nameVisible = false
        },
        handleClose1() {
            this.RecordsVisible = false
        },
        //打开弹层
        async openView(row) {
            this.logDetail.openId = row.openId
            this.logDetail.styleCode = row.styleCode
            const { data, success } = await getProviderQuotationHisRecordPageList(this.logDetail)
            if (success) {
                this.tableData1 = data.list
                this.logTotal = data.total
                this.dialogVisible = true
                this.logDetail.orderBy = null
            } else {
                this.$message.error('获取提交历史失败')
            }
        },
        //页面数量改变
        detailSizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.PageSize = val;
            this.getAlreadyList();
        },
        //当前页改变
        detailPagechange(val) {
            this.ListInfo.currentPage = val;
            this.getAlreadyList();
        },
        searchList() {
            //清除styleCode,goodCode,providerName的空格
            this.ListInfo.styleCode = this.ListInfo.styleCode ? this.ListInfo.styleCode.replace(/\s+/g, "") : null;
            this.ListInfo.goodCode = this.ListInfo.goodCode ? this.ListInfo.goodCode.replace(/\s+/g, "") : null;
            this.ListInfo.providerName = this.ListInfo.providerName ? this.ListInfo.providerName.replace(/\s+/g, "") : null;
            this.getAlreadyList()
        },
        //获取供应商汇总列表 
        async getAlreadyList() {
            const { data, success } = await getProviderQuotationRecordOverViewPageList(this.ListInfo);
            if (success) {
                this.tableData = data.list;
                this.detailTotal = data.total;
                this.listLoading = false;
                this.$nextTick(() => {
                    if (this.isHidden) {
                        this.unfoldTree()
                    }
                })
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getAlreadyList()
            }
        },
        sortchange2({ order, prop }) {
            if (prop) {
                this.RecordsInfo.orderBy = prop
                this.RecordsInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.dockingRecords(this.RecordsInfo)
            }
        },
        sortchange3({ order, prop }) {
            if (prop) {
                if (prop == 'count') {
                    this.nameInfo.orderBy = 'totalCount'
                } else {
                    this.nameInfo.orderBy = prop
                }
                this.nameInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.openNameDialog(this.nameInfo)
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.header {
    display: flex;
    margin-bottom: 10px;
}

.publicMargin {
    margin-right: 20px;
}

.detail ::v-deep .vxe-custom--wrapper {
    display: none !important;
}

::v-deep .el-badge__content {
    padding: 0 4px;
    top: 7px;
    right: 0;
}
</style>
