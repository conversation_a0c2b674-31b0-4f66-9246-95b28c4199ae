<template>
    <container>
        <div style="height: 600px;">
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'
                :tableCols='tableCols' :isSelection="false" :loading="listLoading" :hasexpandRight='true'>
                <template slot='extentbtn'>
                    <el-button-group>
                        <el-button type="primary" @click="Purchase(filter.chooseId, false, 0)">申请进货</el-button>
                    </el-button-group>
                </template>
                <template slot="right">
                    <el-table-column width="auto" label="操作">
                        <template slot-scope="scope">
                            <el-button type="text" v-if="scope.row.applyState > 0"
                                @click="Purchase(filter.chooseId, true, scope.row.hotStockInApplyId)">查看</el-button>
                            <el-button type="text" v-if="scope.row.applyState <= 0"
                                @click="Purchase(filter.chooseId, false, scope.row.hotStockInApplyId)">修改</el-button>
                            <el-button type="text" v-if="scope.row.applyState <= 0"
                                @click="DelStockInApply(scope.row.hotStockInApplyId)">删除</el-button>
                        </template>
                    </el-table-column>
                </template>
            </ces-table>
        </div>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog :title="dialogTitle" :visible.sync="purchaseVisible" append-to-body width='80%' :close-on-click-modal="false"
            v-dialogDrag>
            <hotsalegoodspurchasegoods ref="hotsalegoodspurchasegoodsPage" style="z-index:1000;" @loaded="(d) => {
                if (d.auditState > 0) {
                    buildGoodsDocHiddle = false;
                }
            }" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="purchaseVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onPurchaseGoodsSave(false)" :loading="purchaseGoodsLoading"
                        v-if="purchasegoodsHiddle">保存&关闭</el-button>
                    <el-button type="primary" @click="onPurchaseGoodsSave(true)" :loading="purchaseGoodsLoading"
                        v-if="purchasegoodsHiddle">保存&申请进货</el-button>
                </span>
            </template>
        </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import cesTable from "@/components/Table/table.vue";
import { getAllWarehouse } from '@/api/inventory/warehouse'
import { getStockInApplyList, delStockInApply,validateStockInApplyCount } from '@/api/operatemanage/productalllink/alllink'
import hotsalegoodspurchasegoods from '@/views/operatemanage/productalllink/hotsale/hotsalegoodspurchasegoods.vue'
import { formatTime } from "@/utils";
import { formatPlatform, sendWarehouse4HotGoodsBuildGoodsDocList } from "@/utils/tools";

//上新仓库 
function formatForNewWarehouse(val) {
    let item = sendWarehouse4HotGoodsBuildGoodsDocList.find(x => x.value == val);
    if (item && item.label)
        return item.label;

    return val;
}

const tableCols = [
    { istrue: true, prop: 'styleCode', label: '款式编码', align: "center", tipmesg: '', width: '140' },
    { istrue: true, prop: 'forNewWarehouse', label: '上新仓库', align: "center", tipmesg: '', width: '160', formatter: (row) => row.forNewWarehouseName},
    { istrue: true, prop: 'newPlatForm', label: '运营平台', align: "center", tipmesg: '', width: '100', formatter: (row) => formatPlatform(parseInt(row.newPlatForm)) },
    { istrue: true, prop: 'groupName', label: '运营组', align: "center", width: '120', tipmesg: '' },
    { istrue: true, prop: 'userNickName', label: '申请人', align: "center", width: '80', tipmesg: '' },
    { istrue: true, prop: 'applyTime', label: '申请时间', align: "center", width: '160', tipmesg: '', sortable: 'custom', formatter: (row) => row.applyTime == null ? "" : formatTime(row.applyTime, "YYYY-MM-DD HH:mm:ss") },
    { istrue: true, prop: 'estimateStockInCount', label: '预计进货数量', width: '130', tipmesg: '', sortable: 'custom', },
    { istrue: true, prop: 'estimateStockInAmount', label: '预计进货金额', width: '150', tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'goodsCount', label: '进货编码数', width: '100', tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'pusCount', label: '供应商数', width: '100', tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'applyState', label: '状态', width: '100',align: "center", sortable: 'custom', formatter: (row) => (row.applyState == -1 ? '申请被拒' : row.applyState == 1 ? '申请中' : row.applyState == 2 ? '申请通过' : '暂存') }
]

export default {
    name: 'HotStockInApplyList',
    components: { container, cesTable, hotsalegoodspurchasegoods },
    props: {
        filter: {
            chooseId: null
        }
    },
    data() {
        return {
            that: this,
            list: [],
            directorGroupList: [],
            pager: { OrderBy: null, IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            warehouselist: [],
            listLoading: false,
            purchaseVisible: false,
            purchasegoodsHiddle: true,
            purchaseGoodsLoading: false,
            dialogTitle:null,
        };
    },

    async mounted() {
        var res3 = await getAllWarehouse();
        var warehouselist1 = res3.data;
        this.warehouselist = warehouselist1;
    },

    methods: {
        //查询第一页
        async onSearch() {
            this.list = [];
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getStockInApplyList(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            data.forEach(f => {
                f.forNewWarehouseName = this.warehouselist.find(w => w.wms_co_id === parseInt(f.forNewWarehouse))?.name;
            })
            this.list = data
        },
        async nSearch() {
            await this.getlist()
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        //保存进货
        async onPurchaseGoodsSave(isapply) {
            this.purchaseGoodsLoading = true;
            let save = await this.$refs.hotsalegoodspurchasegoodsPage.saveSkuTableData(isapply);
            this.purchaseGoodsLoading = false;
            if (save) {
                this.$message({ type: 'success', message: '保存成功!' });
                this.purchaseVisible = false;
                await this.onSearch();
            }
        },
        async Purchase(chooseId, readonly, hotStockInApplyId) {
            if(hotStockInApplyId==0)
            {
               //新增时要校验不能重复进货
              let valid = await validateStockInApplyCount({chooseId:chooseId});
              console.log(valid);
              if(valid?.success&&valid?.data==false)
              {
                this.$message({ type: 'error', message: '已存在进货单，不允许重复进货!' });
                return;
              }
            }
            this.purchasegoodsHiddle = !readonly;
            this.purchaseVisible = true;
            this.readonly = readonly;
            this.$nextTick(() => {
                this.$refs.hotsalegoodspurchasegoodsPage.getSkuTableData(chooseId, readonly, hotStockInApplyId);
            });
            if (hotStockInApplyId==0) {
                this.dialogTitle="申请进货";
            }else if (readonly) {
                this.dialogTitle="查看申请进货";
            }else{
                this.dialogTitle="编辑申请进货";
            }
        },
        async DelStockInApply(hotStockInApplyId) {
            this.$confirm('确认删除, 是否继续?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await delStockInApply({ applyId: hotStockInApplyId });
                if (!res?.success) {

                } else {
                    this.$message({ type: 'success', message: '删除成功!' });
                    await this.onSearch();
                }
            }).catch(() => {
                this.$message({ type: 'info', message: '已取消删除' });
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>