<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%;">
            <el-tab-pane label="销售在途" name="first" lazy style="height:100%">
                <salesOnWay />
            </el-tab-pane>
            <el-tab-pane label="采购在途" name="second" lazy style="height:100%">
                <procurementOnWay />
            </el-tab-pane>
            <el-tab-pane label="库存资金" name="third" lazy style="height:100%">
                <fundsInHand />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import procurementOnWay from './procurementOnWay.vue';
import salesOnWay from './salesOnWay.vue';
import fundsInHand from './fundsInHand.vue';
export default {
    components: {
        MyContainer, procurementOnWay, salesOnWay, fundsInHand
    },
    data() {
        return {
            activeName: 'first'
        };
    },
    methods: {

    }
};
</script>

<style lang="scss" scoped></style>