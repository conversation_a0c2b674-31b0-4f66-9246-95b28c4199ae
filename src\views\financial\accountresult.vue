<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :tableData='accountresultlist' @select='selectchange' :isSelection='false' :tableCols='tableCols' :loading="listLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button type="primary" @click="startaccountResult">执行核算</el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.SourceTable" placeholder="来源数据表名" style="width:100px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.ProductID" placeholder="产品ID" aria-placeholder="" style="width:100px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.Remarks" placeholder="备注" style="width:100px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.UseMonth" placeholder="核算月份" style="width:100px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.ShopName" placeholder="店铺名" style="width:100px;" />
                    </el-button>
                    <!-- <el-button type="text" size="medium" disabled style="color: red;">周转对比:</el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input-number v-model="filter1.startdiff"></el-input-number>至<el-input-number v-model="filter1.enddiff"></el-input-number>
            </el-button> -->
                    <el-button type="primary" @click="onSearch">查询</el-button>

                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getaccountresultList" />
        </template>

        <el-dialog title="统计结果明细" :visible.sync="dialogVisibleSyj" width="30%" v-dialogDrag>
            <span>
                <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传</my-confirm-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="选择核算月份" :visible.sync="dialogVisibleSmonth" width="30%" v-dialogDrag>
            <span>
                <el-form>

                    <el-form-item label="年月:">
                        <el-date-picker style="width: 240px" v-model="fft.yearmonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
                    </el-form-item>
                </el-form>

            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="accountResult">确定</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>
<script>

    import { deleteAccountResultBatch, accountResultAsync, getAccountResultList } from '@/api/financial/yyfy'
    import dayjs from "dayjs";
    import cesTable from "@/components/Table/table.vue";
    import { formatTime } from "@/utils";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    const tableCols = [

        { istrue: true, prop: 'sourceID', label: '来源数据ID', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'accountTime', label: '计算时间', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'sourceTable', label: '来源数据表名', width: '100', sortable: 'custom' },

        { istrue: true, prop: 'batchNumber', label: '核算批次', width: '200', sortable: 'custom' },


        { istrue: true, prop: 'productID', label: '产品ID', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'createdTime', label: '核算时间', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'accountFunction', label: '计算方法', width: '100', sortable: 'custom' },

        { istrue: true, prop: 'useMonth', label: '核算月份', width: '100', sortable: 'custom' },

        { istrue: true, prop: 'shopName', label: '店铺名', width: '100', sortable: 'custom' },

        { istrue: true, prop: 'sourceMoney', label: '原始金额', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'resultMoney', label: '摊派金额', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'remarks', label: '备注', width: '200', sortable: 'custom' },
        { istrue: true, type: "button", label: '操作', width: "100", btnList: [{ label: "删除批次", handle: (that, row) => that.deleteBatch(row) }] },


    ];
    export default {
        name: "Users",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
        data() {
            return {
                that: this,
                Filter: {
                },
                shopList: [],
                userList: [],
                groupList: [],
                fft: {},
                accountresultlist: [],
                tableCols: tableCols,
                total: 0,
                summaryarry: { count_sum: 10 },
                pager: { OrderBy: "id", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                //
                selids: [],
                dialogVisibleSyj: false,
                fileList: [],
                dialogVisibleSmonth: false,
            };
        },
        async mounted() {
        },
        methods: {
            startaccountResult() {

                this.dialogVisibleSmonth = true;

            },
            accountResult() {
                this.dialogVisibleSmonth = false;

                var that = this;
                accountResultAsync({ useMonth: this.fft.yearmonth }).then(res => {


                    that.$message({ message: '正在进行核算，请稍后', type: "success" });
                    that.listLoading = false;

                });


            },
            async deleteBatch(row) {
                var that = this;
                this.$confirm("此操作将删除此批次统计结果明细数据?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(async () => {
                        await deleteAccountResultBatch({ batchNumber: row.batchNumber }).then(res => {

                            that.$message({ message: '已删除', type: "success" });

                        });

                        that.onRefresh()

                    });

            },
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            onImportSyj() {
                this.dialogVisibleSyj = true
            },
            async uploadFile2(item) {
                const form = new FormData();
                form.append("upfile", item.file);
                const res = importaccountresultAsync(form);
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
            },
            async uploadSuccess2(response, file, fileList) {
                fileList.splice(fileList.indexOf(file), 1);
            },
            async onSubmitupload2() {
                this.$refs.upload2.submit()
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.getaccountresultList();
            },
            async getaccountresultList() {
                if (this.Filter.UseDate) {
                    this.Filter.startAccountDate = this.Filter.UseDate[0];
                    this.Filter.endAccountDate = this.Filter.UseDate[1];
                }
                if (this.Filter.AccountTime) {
                    this.Filter.startAccountTime = this.Filter.AccountTime[0];
                    this.Filter.endAccountTime = this.Filter.AccountTime[1];
                }
                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,

                };

                console.log(para)

                this.listLoading = true;
                const res = await getAccountResultList(params);
                console.log(res)
                this.listLoading = false;
                console.log(res.data.list)
                //console.log(res.data.summary)

                this.total = res.data.total
                this.accountresultlist = res.data.list;
                //this.summaryarry=res.data.summary;
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            }
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
