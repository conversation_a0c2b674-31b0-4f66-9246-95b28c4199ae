<template>
  <MyContainer>
    <el-tabs v-model="activeName" style="height: 95%">
      <el-tab-pane label="TEMU全托日报" name="first1" lazy style="height: 98%" v-if="checkPermission('productReportPddTemu')">
        <productReportPddTemu ref="productReportPddTemu" />
      </el-tab-pane>
      <el-tab-pane label="TEMU半托日报" name="first2" style="height: 98%">
        <productReportTemuHalfPallet ref="productReportTemuHalfPallet" />
      </el-tab-pane>
      <el-tab-pane label="SHEIN全托日报" name="first3" style="height: 100%">
        <productReportSheIn ref="productReportSheIn" style="height: 100%"></productReportSheIn>
      </el-tab-pane>
      <el-tab-pane label="SHEIN自营日报" name="first4" style="height: 100%">
        <selfOperatedDaily ref="selfOperatedDaily" style="height: 100%"></selfOperatedDaily>
      </el-tab-pane>
       
    </el-tabs>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import productReportPddTemu from "./productReportPddTemu.vue";
import productReportTemuHalfPallet from "./productReportTemuHalfPallet.vue";
import productReportSheIn from "./productReportSheIn.vue";
import selfOperatedDaily from "./selfOperatedDaily.vue";
export default {
  name: "productReportPddTemuIndex",
  components: {
    MyContainer, productReportPddTemu, productReportTemuHalfPallet,productReportSheIn, selfOperatedDaily
  },
  data() {
    return {
      that: this,
      activeName: "first2",
    };
  },
};
</script>

<style lang="scss" scoped></style>
