<template>
 <container v-loading="pageLoading">
     <template #header>
         <el-row>
             <el-input v-model.trim="filter.styleCode" placeholder="系列编码" style="width: 160px" :maxlength="100" clearable></el-input>

             <el-select v-model="filter.isQualityCheck" placeholder="检测报告" clearable style="width: 160px">
                 <el-option label="有" value="1"  />
                 <el-option label="无" value="0"  />
             </el-select>
             <!-- <el-select v-model="filter.qualityCheckCompanyName" placeholder="检测报告公司抬头" clearable filterable style="width: 160px">
                 <el-option :label="item.name" :value="item.id" v-for="(item,index) in gsttList" :key="index" />
             </el-select> -->

             <el-input v-model.trim="filter.qualityCheckCompanyName" placeholder="检测报告公司抬头" style="width: 160px" :maxlength="100" clearable></el-input>
             <!-- <el-select v-model="filter.menuStatus" placeholder="添加人" clearable filterable style="width: 160px">
                 <el-option label="添加人" value="添加人" v-for="(item,index) in userList" :key="index" />
             </el-select> -->

             <el-input v-model.trim="filter.createdUserName" placeholder="添加人" style="width: 160px" :maxlength="100" clearable></el-input>

             <el-date-picker style="width: 260px" v-model="filter.timerange" :picker-options="pickerOptions" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                  range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false"></el-date-picker>

             <el-button type="primary" @click="onSearch">查询</el-button>
             <el-button  @click="tozero" type="warning">重置</el-button>

             <el-button type="primary" @click="addQualityReport">添加质检报告</el-button>
             <!-- <el-button type="primary" @click="allEdit">批量操作</el-button> -->

             <el-dropdown style="box-sizing: border-box; margin-left:6px;" v-if="checkPermission('api:operatemanage:StyleCodeManage:DelStyleQualityCheckReportByIds')"
                    size="mini" split-button type="primary"
                   icon="el-icon-share" @command="allEdit"> 批量操作 <el-dropdown-menu slot="dropdown">
                       <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                           command="del">批量删除</el-dropdown-item>
                       <el-dropdown-item class="Batcoperation" style="height: 10px"></el-dropdown-item>
                   </el-dropdown-menu>
               </el-dropdown>

         </el-row>



     </template>
     <container >
         <template>
             <yhVxetable ref="tabletwo" :id="'YunHanAdminGoods20231220'" :that='that' :isIndex='true'  @sortchange='sortchange' :summaryarry="summaryarry"
             :tableData='tableData' :tableCols='tableCols' :border='true' :hasexpand='true' @select='selectchange'
             :isSelectColumn="false" :loading="listLoading">
             </yhVxetable>
         </template>

         <template #footer>
             <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
         </template>
     </container>



    

     <el-dialog :title="isedit==1?'新增':'编辑'" :visible.sync="dialogEdit" width="600px" v-dialogDrag>
         <span>
             <el-form ref="refeditform" :model="editForm" max-height="600">
                 <el-form-item label="系列编码:" label-width="130px" :rules="[{ required: true, message: '请输入系列编码', trigger: 'blur' }]" prop="styleCode">
                  <el-select v-model="editForm.styleCode"  placeholder="系列编码" clearable filterable @change="isStyleCode" :remote-method="changeStyleCode" remote>
                      <el-option :label="item.styleCode" :value="item.styleCode" v-for="(item) in styleCodeList" :key="item.styleCode" />
                  </el-select>
                 </el-form-item>
                 <el-form-item label="图片:" label-width="130px" :rules="[{ required: isedit==1?true:false, message: '请上传图片', trigger: 'change' }]"  prop="goodsImage">
                  <YhImgUpload3 v-if="goodsImageshow" :value.sync="editForm.goodsImage" :isImg="false"
                        accept=".jpg," :limit="1" :ismultiple="true"></YhImgUpload3>
                  </el-form-item>
                 <el-form-item label="质检报告:" label-width="130px" :rules="[{ required: isedit==1?true:false, message: '请输入质检报告', trigger: 'blur' }]" prop="qualityCheckReport">

                  <YhImgUpload v-if="qualityCheckReportshow" :value.sync="editForm.qualityCheckReport"  :isImg="false" accept=".pdf" :limit="1" ></YhImgUpload>

                 </el-form-item>
                 <el-form-item label="检测报公司抬头:" label-width="130px" :rules="[{ required: true, message: '请上传质检报告', trigger: 'blur' }]" prop="qualityCheckCompanyId">
                  <el-select v-model="editForm.qualityCheckCompanyId" placeholder="选择检测报公司抬头" clearable filterable >
                   <el-option :label="item.name" :value="item.id" v-for="(item,index) in gsttList" :key="index" />
                  </el-select>
                 </el-form-item>
             </el-form>
         </span>

         <span slot="footer" class="dialog-footer">
             <el-button type="primary" @click="updateBaseMenu"  v-throttle="3000">保存</el-button>
             <el-button @click="dialogEdit = false">关闭</el-button>
         </span>
     </el-dialog>
 </container>
</template>
<script>
import YhImgUpload3 from "@/components/upload/yh-img-upload2Table.vue";
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import container from '@/components/my-container';
import yhVxetable from '@/components/VxeTable/yh_vxetable.vue';
import { formatLinkProCode, platformlist, downloadLink } from '@/utils/tools'
import checkPermission from '@/utils/permission'

  import { getStyleQualityCheckRecordList, getStyleCodeSelectList,
   getQualityCheckCompanySelectList, getStyleQualityCheckReportById, saveStyleQualityCheckReport, delStyleQualityCheckReportByIds } from '@/api/operatemanage/qualityCompany.js';

const tableCols = [
{ istrue: true, type: "checkbox", sortable: 'custom', width: '40' },
 { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: '160', align: 'center'  },
 { istrue: true, prop: 'goodsImage', label: '图片',  type: 'images', width: '160', align: 'center' },
 { istrue: true, prop: 'qualityCheckReport', label: '质检报告', width: '100', type:'button', align: 'center',  btnList:[           
      {
          label:'下载',
          handle:(that,row)=>{
              that.downloadLink(row.fileUrl,row.fileTitle);
          },
          ishide:(that,row)=> !row.fileUrl 
      },           
  ]} ,
 { istrue: true, prop: 'qualityCheckCompanyName', label: '检测报告公司抬头', width: 'auto', sortable: 'custom', align: 'center' },
 { istrue: true, prop: 'createdTime', label: '添加时间', width: '130', sortable: 'custom', align: 'center', formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD') },
 { istrue: true, prop: 'createdUserName', label: '添加人', width: '100', sortable: 'custom', align: 'center' },
 {
     istrue: true, type: "button", label: '操作', width: "200", align: 'center',
     btnList: [
         { label: "编辑", handle: (that, row) => that.editBaseMenu(row, 1),  },
         { label: "删除", type: 'danger', handle: (that, row) => that.delBaseMenu(row, 1),  permission: 'api:operatemanage:StyleCodeManage:DelStyleQualityCheckReportByIds'   }
     ]
 }
];

const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = startDate;
// const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
 name: "Users",
 components: { container, yhVxetable, YhImgUpload, YhImgUpload3 },
 data () {
     return {
         onetab: 'two',
         fileList:[],
         isedit: 1,
         qualityCheckReportshow: true,
         dialogEdit: false,
         goodsImageshow: true,
         editForm: {},
         form: {
             menuTypeList: [],
         },
         pickerOptions: {
            shortcuts: [{
                text: '近一周',
                onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                    picker.$emit('pick', [start, end]);
                }
            }, {
                text: '近半个月',
                onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                    picker.$emit('pick', [start, end]);
                }
            }, {
                text: '近一个月',
                onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                    picker.$emit('pick', [start, end]);
                }
            }, {
                text: '近三个月',
                onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                    picker.$emit('pick', [start, end]);
                }
            }]
        },
         // getjcbgList: [], //质检报告
         gsttList: [], //公司抬头
         // userList: [], //用户
         dialogMenuType: false,
         uploadLoading: false,
         dialogVisibleData: false,
         that: this,
         filter: {
             timerange: [],
             createdUserName: '',
             qualityCheckCompanyName: '',
             isQualityCheck: '',
             styleCode: '',
         },
         selrows: [],
         platformlist: platformlist,
         tableCols: tableCols,
         // tableColstwo: tableColstwo,
         tableDatatwo: [],
         quyuList: [],
         tableHandles: null,
         tableData: [
        ],
         total: 0,
         pager: { orderBy: "createdTime", isAsc: false },
         listLoading: false,
         pageLoading: false,
         summaryarry: {},
         sels: [],
         gysList: [],
         styleCodeList: [],
         downloadLink:downloadLink,
     };
 },
 mounted () {
     // this.getOrderMenuType();
     this.onSearch();
 },
 methods: {
      tozero(){
        this.filter = {
         timerange: [],
         createdUserName: null,
         qualityCheckCompanyName: '',
         isQualityCheck: '',
         styleCode: '',
        }
      },
      allEdit(val){
        if(this.selrows.length<=0){
          this.$message.error("请至少选择一条数据");
          return
        }
        if(val=='del'){
         this.delBaseMenu(this.selrows, 2);
        }
      },
      selectchange(row){
       this.selrows = row;
     },
     async addQualityReport(){
      this.isedit = 1;
      this.editForm = {};
      this.getgstt(); //获取报告抬头
      this.$nextTick(() => {
       this.dialogEdit = true;
       this.$refs['refeditform']?.clearValidate();
      })
     },
     async getgstt(){ //获取检查公司
         const res = await getQualityCheckCompanySelectList({});
         if(!res.success){
             return
         }
         this.gsttList = res.data;
     },
     isStyleCode(val){
    
      this.editForm.goodsImage = this.styleCodeList.filter(item=>item.styleCode == this.editForm.styleCode)[0].goodsImage;
     },
     async changeStyleCode(val){
         const res = await getStyleCodeSelectList({styleCode:val});
         if(!res.success){
             return
         }
         this.styleCodeList = res.data;
     },
     async getquyu(){
         const res = await getAreaSetList({getLevel: 1});
         if(!res.success){
             return
         }
         this.quyuList = res.data;
         if(!this.filter.zoneName){
             this.filter.zoneName = this.quyuList[0];
         }
     },
     async onExport() {
         window.open("/static/excel/profit/导入模板.xlsx", "_blank");
     },
     // async openDialogMenuType () {
     //     this.dialogMenuType = true;
     //     await this.getOrderMenuType();
     // },
     async updateBaseMenu () {
         let isValid = false
         this.$refs.refeditform.validate(valid => {
             isValid = valid
         })
         if(!isValid){
           return
         }
         let p = this.editForm;
         if(this.isedit == 1){ //新增保存
             const res = await saveStyleQualityCheckReport(p);
             if (!res?.success) {
              return
             }
                 this.$message({ type: 'success', message: '保存成功!' });
                 this.dialogEdit = false;
                 await this.getList();
             
         }else if(this.isedit == 2){ //编辑保存
             const res = await saveStyleQualityCheckReport(p);
             if (!res?.success) {
              return
             }
                 this.$message({ type: 'success', message: '保存成功!' });
                 this.dialogEdit = false;
                 await this.getList();
             
         }

     },
     addMenuType () {
         this.form.menuTypeList.push({
             menuType: '',
             orderId: null
         });
     },
     removeMenuType (index) {
         this.form.menuTypeList.splice(index, 1)
     },
     async editBaseMenu (row) {
         // this.getOrderMenuType();
         this.goodsImageshow = false;
         this.qualityCheckReportshow = false;
         this.isedit = 2;
         this.editForm = {
          qualityCheckReport: row.qualityCheckReport,
          goodsImage: row.goodsImage,
          styleCode: row.styleCode,
          qualityCheckCompanyId: row.qualityCheckCompanyId,
          // qualityCheckCompanyName: row.qualityCheckCompanyName,
          id: row.id
         }
         this.getgstt(); //获取报告抬头
         this.$nextTick(()=>{
          this.qualityCheckReportshow = true;
          this.goodsImageshow = true;
          this.dialogEdit = true;
          this.$refs['refeditform']?.clearValidate();
         })
     },

     async delBaseMenu(row, index) {
         this.$confirm('确定删除, 是否继续?', '提示', {
             confirmButtonText: '确定',
             cancelButtonText: '取消',
             type: 'warning'
         })
             .then(async _ => {
                 
                 if(index==1){ //行删除
                     let par = {
                        ids: [row.id]
                    }
                     const res = await delStyleQualityCheckReportByIds(par)
                     if (!res?.success) {
                        return
                     }
                     this.$message({ type: 'success', message: '删除成功!' });
                     await this.getList();
                 }else if(index==2){//批量删除
                     
                     let ids = [];
                     row.map((item)=>{
                      ids.push(item.id);
                     })
                     let par = {
                         ids: ids
                     }
                     const res = await delStyleQualityCheckReportByIds(par)
                     if (!res?.success) {
                        return
                     }
                     this.$message({ type: 'success', message: '删除成功!' });
                     await this.getList();
                 }

             })
             .catch(_ => { });

     },
     async onSearch () {
         // await this.getjcbg(); //获取质检报告
         await this.getgstt(); //获取报告抬头
         // await this.getuser(); //获取添加人

         await this.getList(); 
     },
     async sortchange (column) {

        if (!column.order){
         this.pager = {};
         }else{
             this.pager = { orderBy: column.prop, isAsc: column.order.indexOf("descending") == -1 ? true : false };
         }
         this.$refs.pager.setPage(1);

         await this.onSearch();
     },
     async getList () {
         if (this.filter.timerange) {
             this.filter.createdStartTime = this.filter.timerange[0];
             this.filter.createdEndTime = this.filter.timerange[1];
         }
         var that = this;
         this.listLoading = true;
         var pager = this.$refs.pager.getPager();
         const params = { ...pager, ...this.pager, ...this.filter };
         const res = await getStyleQualityCheckRecordList(params)

         if(!res?.success){
          return
         }
        this.listLoading = false;
        that.total = res.data?.total;
        if(res?.data?.list.length>0){
          res.data?.list.map(item=>{
           if(item.qualityCheckReport){
            item.fileUrl = JSON.parse(item.qualityCheckReport)[0].url;
            item.fileTitle = JSON.parse(item.qualityCheckReport)[0].name;
           }else{
            item.fileUrl = '';
            item.fileTitle = '';
           }
           
          })
        }
        that.tableData = res.data?.list;
         this.listLoading = false;
     },
     async uploadFile () {
         if (this.fileList.length ==0) {
             this.$message({ message: "请先上传文件", type: "warning" });
             return false
         };
         const form = new FormData();
         form.append("upfile", this.fileList[0].raw);
         //form.append("platform", 1);
         var res = await importBaseMenuAsync(form);
         if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
         else this.$message({ message: res.msg, type: "warning" });
         this.$refs.upload.clearFiles()
         this.uploadLoading = false;
         this.dialogVisibleData = false;
         this.fileList = [];
     },
     async uploadChange (file, fileList) {
         if (fileList.length == 2) {
             fileList.splice(1, 1);
             this.$message({ message: "只允许单文件导入", type: "warning" });
             return false;
         }
         this.fileList.push(file);
     },
     uploadRemove (file, fileList) {
         this.fileList.splice(0, 1);
     },
     submitUpload () {
         if (this.fileList.length == 0) {
             this.$message({ message: "请先上传文件", type: "warning" });
             return;
         }
         this.uploadLoading = true
         this.$refs.upload.submit();
     },
 }
})
</script>
<style scoped>
::v-deep .el-table__fixed-footer-wrapper tbody td {
 color: blue;
}
</style>

