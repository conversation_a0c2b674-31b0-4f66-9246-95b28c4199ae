<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <el-tabs v-model="activeName" style="height:94%;">

      <el-tab-pane   label="标准推广" name="tab1" style="height: 100%;">
          <JLThousandRivers :filter="Filter" ref="JLThousandRivers" style="height: 100%;"/>
      </el-tab-pane>

      <el-tab-pane   label="全域推广" name="tab2" style="height: 100%;">
        <PushBroadcastRoom :filter="Filter" ref="PushBroadcastRoom" style="height: 100%;"/>
    </el-tab-pane>

    <el-tab-pane   label="平台赠款" name="tab3" style="height: 100%;">
      <PlatformIncrement :filter="Filter" ref="PlatformIncrement" style="height: 100%;"/>
  </el-tab-pane>

      <el-tab-pane label="电商返佣" name="tab4" style="height: 100%;">
          <e_CommerceRebate :filter="Filter" ref="e_CommerceRebate" style="height: 100%;"/>
      </el-tab-pane>

    </el-tabs>
    </my-container >

   </template>
  <script>
  import MyContainer from "@/components/my-container";
  import JLThousandRivers from '@/views/financial/yyfydayreport/JLThousandRivers'
  import PushBroadcastRoom from '@/views/financial/yyfydayreport/PushBroadcastRoom'
  import PlatformIncrement from '@/views/financial/yyfydayreport/PlatformIncrement'
  import e_CommerceRebate from '@/views/financial/yyfydayreport/e_CommerceRebate'
  import checkPermission from '@/utils/permission'
  export default {
    name: "Users",
    components: { MyContainer,JLThousandRivers,checkPermission,PushBroadcastRoom,PlatformIncrement,e_CommerceRebate},
    data() {
      return {
        that:this,
        Filter: {
        },
        pageLoading:"",
        activeName:"tab1",
        shopList:[],
        userList:[],
        groupList:[],
        selids:[],
        dialogVisibleSyj:false,
        fileList:[],
      };
    },
    mounted() {
    },
    methods: {
  async onSearch(){
    if (this.activeName=='tab1')
    this.$refs.JLThousandRivers.onSearch();

  }


    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
