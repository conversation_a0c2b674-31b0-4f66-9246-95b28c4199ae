<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="仅退款商品数据" name="tab1" style="height: 100%;">
                <onlyrefundgoods v-if="batchStrBaseList.length>0" :filter="filter" ref="onlyrefundgoods" style="height: 100%;"
                    :batchStrBaseList="onlyrefundgoodList"
                    @showtab_onlyrefundorder="showtab_onlyrefundorder" />
            </el-tab-pane>
            <el-tab-pane label="仅退款订单数据" name="tab2" style="height: 100%;" :lazy="true">
                <onlyrefundorder :filter="filter" :saleAfterReasonBaseList="saleAfterReasonBaseList"
                    :batchStrBaseList="batchStrBaseList" :expressStatusBaseList="expressStatusBaseList"
                    :partInfo="partInfo"
                    ref="onlyrefundorder" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="退货退款商品数据" name="tab3" style="height: 100%;" :lazy="true">
                <backrefundgoods :filter="filter" ref="backrefundgoods" style="height: 100%;"
                    :saleAfterReasonBaseList="saleAfterReasonBaseList" :expressStatusBaseList="expressStatusBaseList"
                    :batchStrBaseList="batchStrBaseList" :backExpressComBaseList="backExpressComBaseList" />
            </el-tab-pane>
            <el-tab-pane label="退货退款订单数据" name="tab4" style="height: 100%;" :lazy="true">
                <backrefundorder :filter="filter" ref="backrefundorder"
                    :saleAfterReasonBaseList="saleAfterReasonBaseList" :expressStatusBaseList="expressStatusBaseList"
                    :batchStrBaseList="batchStrBaseList" :backExpressComBaseList="backExpressComBaseList"
                    style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="店铺数据汇总" name="tab5" style="height: 100%;" :lazy="true">
                <shoprefundsum :filter="filter" ref="shoprefundsum" :saleAfterReasonBaseList="saleAfterReasonBaseList"
                    :batchStrBaseList="batchStrBaseList" :expressStatusBaseList="expressStatusBaseList"
                    style="height: 100%;" :backExpressComBaseList="backExpressComBaseList" />
            </el-tab-pane>
            <el-tab-pane label="小额打款" name="tab7" style="height: 100%;" :lazy="true">
                <smallPayments :filter="filter" :batchStrBaseList="pettyPaymentList" ref="smallPayments" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="系列产品汇总" name="tab8" style="height: 100%;" :lazy="true">
                <productSeriesSummary :filter="filter" ref="productSeriesSummary" @update="handleUpdate" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="导入历史" name="tab6" style="height: 100%;" :lazy="true">
                <importbatch :filter="filter" ref="importbatch" style="height: 100%;" />
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
<script>
import checkPermission from '@/utils/permission';
import MyContainer from "@/components/my-container";
import onlyrefundgoods from '@/views/customerservice/douyin/refund/onlyrefundgoods';
import onlyrefundorder from '@/views/customerservice/douyin/refund/onlyrefundorder';
import backrefundgoods from '@/views/customerservice/douyin/refund/backrefundgoods';
import backrefundorder from '@/views/customerservice/douyin/refund/backrefundorder';
import shoprefundsum from '@/views/customerservice/douyin/refund/shoprefundsum';
import importbatch from '@/views/customerservice/douyin/refund/importbatch';
import smallPayments from '@/views/customerservice/douyin/refund/pettyPayment/smallPayments';
import productSeriesSummary from '@/views/customerservice/douyin/refund/productSeriesSummary';
import {
    GetImportDouYinRefundOrderBatchPageList
} from '@/api/customerservice/douyinrefund'

export default {
    name: "douyinrefundindex",
    provide() {
        return {
            reload: this.reload
        }
    },
    components: {
        MyContainer,
        onlyrefundgoods, onlyrefundorder, backrefundgoods, backrefundorder, shoprefundsum, importbatch, smallPayments, productSeriesSummary
    },
    data() {
        return {
            that: this,
            pageLoading: false,
            filter: {
            },
            shopList: [],
            selids: [],
            activeName: 'tab1',
            saleAfterReasonBaseList: [
                '不想要了',
                '地址/电话信息填写错误',
                '退运费',
                '商品质量问题',
                '与商家协商一致退款',
                '其他',
                '商品做工粗糙/有瑕疵',
                '拍多了',
                '7天无理由退款',
                '不喜欢/效果不好',
                '商品信息拍错（规格/尺码/颜色等）',
                '商品尺寸/尺码/大小与预期不符',
                '多拍/错拍/不想要',
                '规格等与商品描述不符',
                '未按约定时间发货',
                '商家未按约定时间发货',
                '商品破损/包装破损',
                '系统签收商品未收到',
                '商品与页面描述不符',
                '商品/赠品/配件/证书漏发',
                '快递信息长时间未更新',
                '商家发错货',
                '商品价格问题（买贵、降价等）',
                '缺货',
                '假冒品牌',
                '商品买贵了或降价',
                '商品功能有问题',
                '效果与商品描述不符',
                '商品材质/品牌/外观等与商品描述不符',
                '生产日期/保质期/规格等与商品描述不符',
            ],
            expressStatusBaseList: [
                '-',
                '买家已签收',
                '商家已签收',
                '快递退回中',
            ],
            backExpressComBaseList: [
                '韵达快递',
                '中通快递',
                '京东快递',
                '圆通快递',
                '顺丰速运',
                '丹鸟',
                '申通快递',
                '极兔快递',
                '邮政快递包裹',
                'EMS',
                '邮政电商标快',
                '德邦快递'
            ],
            batchStrBaseList: [],
            pettyPaymentList: [],//小额打款数据
            onlyrefundgoodList: [],//仅退款商品数据
            partInfo:{},
        };
    },
    mounted() {
        this.pageLoading = true;
        this.getOtherData();
        window.showtab_onlyrefundgoods = this.showtab_onlyrefundgoods;
        // window.showtab_onlyrefundorder = this.showtab_onlyrefundorder;
        window.showtab_backrefundgoods = this.showtab_backrefundgoods;
        window.showtab_backrefundorder = this.showtab_backrefundorder;
    },
    methods: {
        handleUpdate(row, timerange, val){
          if(val == 1){
            this.activeName = "tab1";
            this.$nextTick(() => {
              this.$refs.onlyrefundgoods.updateFilterMonthDay(row, timerange);
            });
          } else if(val == 2){
            this.activeName = "tab3";
            this.$nextTick(() => {
              this.$refs.backrefundgoods.updateFilterMonthDay(row, timerange);
            });
          }
        },
        showtab_onlyrefundgoods() {
            this.activeName = "tab1"
        },
        showtab_onlyrefundorder(data) {
            this.partInfo=data
            this.activeName = "tab2"
        },
        showtab_backrefundgoods() {
            this.activeName = "tab3"
        },
        showtab_backrefundorder() {
            this.activeName = "tab4"
        },
        async reload() {
            //刷新其他页面的下拉框选项
        },
        async getOtherData() {
            let params = {};
            const res = await GetImportDouYinRefundOrderBatchPageList(params);
            this.batchStrBaseList = res.data.list;
            this.pettyPaymentList = res.data.list.filter(item => item.fileType == 2);//小额打款数据
            this.onlyrefundgoodList = res.data.list.filter(item => item.fileType == 1);//仅退款商品数据
            this.pageLoading = false;
        }
    },


};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
