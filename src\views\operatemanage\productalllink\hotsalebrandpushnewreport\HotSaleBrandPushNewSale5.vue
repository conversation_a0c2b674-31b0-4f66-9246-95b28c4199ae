<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
            <div class="top">
                <el-button-group>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        销量日期
                        <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="销量开始日期" end-placeholder="销量结束日期" :picker-options="pickerOptions"
                            :clearable="false" style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0; margin-left: 10px;">
                        存档月份
                        <el-date-picker v-model="filter.cunDangMonths" type="monthrange" range-separator="至"
                            start-placeholder="开始月份" end-placeholder="结束月份" style="width: 240px;">
                        </el-date-picker>
                    </el-button>




                    <el-button style="padding: 0;margin: 0; border: 0; margin-left: 10px;">
                        <el-input v-model.trim="filter.createdGoodsCompeteId2" type="text" maxlength="40" clearable
                            placeholder="推品ID" style="width:150px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0; margin-left: 10px;">
                        <el-select v-model="filter.createdUserIds" clearable filterable placeholder="推荐人" multiple
                            collapse-tags style="width: 150px">
                            <el-option v-for="item in createdUserList" :key="item.createdUserId"
                                :label="item.createdUserName" :value="item.createdUserId" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0; border: 0; margin-left: 10px;">
                        <el-select v-model="filter.isDelete" clearable filterable placeholder="在职状态"
                            style="width: 120px">
                            <el-option label="在职" :value="false" />
                            <el-option label="离职" :value="true" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0; margin-left: 10px;">
                        <el-select v-model="filter.chooseUserIds" clearable filterable multiple collapse-tags
                            placeholder="选品人" style="width: 150px">
                            <el-option v-for="item in chooseUserList" :key="item.chooseUserId"
                                :label="item.chooseUserName" :value="item.chooseUserId" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0; margin-left: 10px;">
                        <el-input v-model.trim="filter.styleCode" type="text" maxlength="40" clearable
                            placeholder="系列编码" style="width:150px;" />
                    </el-button>

                    <!-- <el-button style="padding: 0;margin: 0; border: 0; margin-left: 10px;">
                        <el-select v-model="filter.styleCodeFirstSaleTime2" clearable filterable placeholder="终止时间"
                            style="width: 150px">
                            <el-option label="6个月内" value="6个月内" />
                            <el-option label="6个月外" value="6个月外" />
                        </el-select>
                    </el-button> -->

                    <!-- <el-button style="padding: 0;margin: 0; border: 0;margin-left: 10px;">
                        业绩统计周期
                        <el-select v-model="filter.youXiaoMonth" filterable placeholder="业绩统计周期" style="width: 100px">
                            <el-option label="1个月内" :value=1 />
                            <el-option label="2个月内" :value=2 />
                            <el-option label="3个月内" :value=3 />
                            <el-option label="4个月内" :value=4 />
                            <el-option label="5个月内" :value=5 />
                            <el-option label="6个月内" :value=6 />
                            <el-option label="7个月内" :value=7 />
                            <el-option label="8个月内" :value=8 />
                            <el-option label="9个月内" :value=9 />
                            <el-option label="10个月内" :value=10 />
                            <el-option label="11个月内" :value=11 />
                            <el-option label="12个月内" :value=12 />
                        </el-select>
                    </el-button> 
                    <el-button style="padding: 0;margin: 0; border: 0;margin-left: 10px;">
                        <el-select v-model="filter.youXiao" clearable filterable placeholder="有效业绩"
                            style="width: 150px">
                            <el-option label="有效业绩" value="有效业绩" />
                            <el-option label="无效业绩" value="无效业绩" />
                        </el-select>
                    </el-button>-->

                    <el-button style="padding: 0;margin: 0; border: 0;margin-left: 10px;">
                        <el-select v-model="filter.styleEnable" clearable filterable placeholder="款式新增移除"
                            style="width: 150px">
                            <el-option label="正常" value="正常" />
                            <el-option label="被新增" value="被新增" />
                            <el-option label="已移除" value="已移除" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;margin-left: 10px;">
                        <el-select v-model="filter.styleEnableSku" clearable filterable placeholder="SKU新增移除"
                            style="width: 150px">
                            <el-option label="正常" value="正常" />
                            <el-option label="被新增" value="被新增" />
                            <el-option label="已移除" value="已移除" />
                            <el-option label="补sku新增" value="补sku新增" />
                            <el-option label="补sku" value="补sku" />
                        </el-select>
                    </el-button>



                    <el-button style="padding: 0;margin: 0; border: 0;margin-left: 10px;">
                        <el-select v-model="filter.xlArea" filterable placeholder="销量范围" style="width: 150px" clearable>
                            <el-option label=">=1万<2万" :value=1 />
                            <el-option label=">=2万<3万" :value=2 />
                            <el-option label=">=3万" :value=3 />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;margin-left: 10px;">
                        <el-select v-model="filter.dlArea" filterable placeholder="单量范围" style="width: 150px" clearable>
                            <el-option label=">=500<1000" :value=10 />
                            <el-option label=">=1000<2000" :value=11 />
                            <el-option label=">=2000<3000" :value=12 />
                            <el-option label=">=3000<4000" :value=13 />
                            <el-option label=">=4000<5000" :value=14 />
                            <el-option label=">=5000<6000" :value=15 />
                            <el-option label=">=6000<7000" :value=16 />
                            <el-option label=">=7000<8000" :value=17 />
                            <el-option label=">=8000<9000" :value=18 />
                            <el-option label=">=9000<10000" :value=19 />
                            <el-option label=">=10000" :value=20 />
                        </el-select>
                    </el-button>

                    <el-button type="primary" @click="onSearch()" style="margin-left: 10px;">查询</el-button>
                    <el-button type="primary" v-if="!isSum" @click="onAddStyleCodeShow()">新增系列编码</el-button>

                    <el-button type="primary" @click="onCunDangShow(1)">批量存档</el-button>
                    <el-button type="primary" @click="onCunDang2(-1)">批量取消存档</el-button>

                    <el-button type="primary" @click="onExport()">导出</el-button>
                </el-button-group>
            </div>
        </template>
        <vxetablebase :id="'HotSaleBrandPushNewSale2202408041719'" ref="table" :that='that' :isIndex='true'
            :hasexpand='true' :tablefixed='true' :border="true" @sortchange='sortchange' @select='selectchange'
            :tableData='tableData' :tableCols='tableCols' :showsummary='true' :summaryarry='summaryarry'
            :isSelection="false" :isSelectColumn="false"
            :treeProp="{ rowField: 'rowId', parentField: 'parentId', expandAll: false, transform: true, }"
            style="width: 100%;  margin: 0" v-loading="listLoading" :height="'100%'">
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>


        <el-dialog title="新增系列编码" :visible.sync="delStyleCodeDialog.visible" width="400px" :close-on-click-modal="false"
            append-to-body v-dialogDrag>
            <el-row>
                <el-col :span="12">
                    <el-select v-model="delStyleCodeSel" placeholder="系列编码" filterable clearable>
                        <el-option v-for="item in delStyleCodeList" :key="item" :label="item" :value="item">

                        </el-option>
                    </el-select>
                </el-col>
                <el-col :span="12">
                    <el-select v-model="delStyleCodeUserSel" placeholder="推荐人" filterable clearable>
                        <el-option v-for="item in createdUserList" :key="item.createdUserId"
                            :label="item.createdUserName" :value="item.createdUserId" />
                    </el-select>
                </el-col>
            </el-row>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary" @click="onAddStyleCode">确认</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="新增系列编码" :visible.sync="cunDangDialog.visible" width="400px" :close-on-click-modal="false"
            append-to-body v-dialogDrag>
            <el-row>
                <el-col :span="12">
                    <el-date-picker v-model="cunDangMonth" type="month" format="yyyy-MM" value-format="yyyy-MM"
                        placeholder="选择月"></el-date-picker>
                </el-col>
                <el-col :span="12">
                    <el-select v-model="cunDangAreaEnm" filterable placeholder="销量范围" clearable>
                        <el-option label=">=1万<2万" :value=1 />
                        <el-option label=">=2万<3万" :value=2 />
                        <el-option label=">=3万" :value=3 />
                    </el-select>
                </el-col>
            </el-row>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary" @click="onCunDang" :loading="cunDangDialog.loading">确认</el-button>
                </span>
            </template>
        </el-dialog>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import dayjs from 'dayjs';
import datepicker from '@/views/customerservice/datepicker';
import { pickerOptions } from '@/utils/tools';
import { formatTime } from "@/utils";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetHotSaleBrandPushNewReportUsers,
    GetHotSaleBrandPushNewSaleDtlPageList2, SetHotSaleBrandPushNewSaleEnable2, GetHotSaleBrandPushNewSaleDelEnable, GetHotSaleBrandPushNewChooseSearch,
    ExportHotSaleBrandPushNewSaleDtlList2, HotSaleBrandPushNewSaleStyleCunDang2
} from '@/api/operatemanage/productalllink/alllink'

const styleEnableOpts = [
    { label: '<span style="color:green">正常</span>', value: 0 },
    { label: '<span style="color:green">被新增</span>', value: 1 },
    { label: '<span style="color:red">已移除</span>', value: 2 },
    { label: '<span style="color:green">补sku新增</span>', value: 3 },
    { label: '<span style="color:red">补sku</span>', value: 4 },
    { label: '<span style="color:green">正常</span>', value: 5 },
    { label: '<span style="color:red">已移除</span>', value: 6 },
];
const styleEnableFmt = (val) => {
    let opt = styleEnableOpts.find(x => x.value == val);
    if (opt)
        return opt.label;

    return val;
}
const tableCols = [
    { istrue: true, fixed: 'left', width: '40', type: "checkbox" },
    { sortable: 'custom', width: '140', align: 'center', prop: 'hotSaleBrandPushNewGoodsCompeteId2', treeNode: true, label: '推荐ID' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'createdUserName', label: '推荐人', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'isDelete', label: '在职状态', formatter: (row) => row.isDelete == true ? "离职" : "在职" },
    { sortable: 'custom', width: '70', align: 'center', prop: 'chooseUserId', label: '选品人', formatter: (row) => row.chooseUserName },
    { sortable: 'custom', width: '120', align: 'center', prop: 'styleCode', label: '系列编码', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'styleCodeFirstSaleTime', label: '首次销量时间', formatter: (row) => row.styleCodeFirstSaleTime == null ? "" : formatTime(row.styleCodeFirstSaleTime, "YYYY-MM-DD") },
    { sortable: 'custom', width: '80', align: 'center', prop: 'styleCodeFirstSaleTime2', label: '终止时间', formatter: (row) => row.styleCodeFirstSaleTime2 == null ? "" : formatTime(row.styleCodeFirstSaleTime2, "YYYY-MM-DD") },
    { sortable: 'custom', width: '70', align: 'center', prop: 'orderCount', label: '单量', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'goodsCount', label: '销量', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'saleAmont', label: '销售额', },
    // { sortable: 'custom', width: '90', align: 'center', prop: 'profit3', label: '毛三利润', },
    // { sortable: 'custom', width: '90', align: 'center', prop: 'profit3Rate', label: '毛三利润率', formatter: (row) => row.profit3Rate + '%' },
    { sortable: 'custom', width: '80', align: 'center', prop: 'profit4', label: '毛四利润', },
    { sortable: 'custom', width: '90', align: 'center', prop: 'profit4Rate', label: '毛四利润率', formatter: (row) => row.profit4Rate + '%' },
    { sortable: 'custom', width: '80', align: 'center', prop: 'cunDangYearMonth', label: '销量存档', type: 'html', formatter: (row) => ('<span style="color:red">' + (row.cunDangYearMonth ? row.cunDangYearMonth : '') + '</span>') },
    { sortable: 'custom', width: '100', align: 'center', prop: 'cunDangYearMonthFw', label: '销量范围', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'cunDangYearMonth2', label: '单量存档', type: 'html', formatter: (row) => ('<span style="color:red">' + (row.cunDangYearMonth2 ? row.cunDangYearMonth2 : '') + '</span>') },
    { sortable: 'custom', width: '100', align: 'center', prop: 'cunDangYearMonth2Fw', label: '单量范围', },
    { width: '120', align: 'center', prop: 'styleEnable', label: '新增移除', type: 'html', formatter: (row) => styleEnableFmt(row.styleEnable) },
    {
        istrue: true, type: 'button', label: '操作', width: '120', align: 'center',
        btnList: [
            { label: "新增", ishide: (that, row) => { return row.parentId <= 0 || row.styleEnable != 4; }, handle: (that, row) => that.onJinYongQiYong(row, 1) },
            { label: "恢复", ishide: (that, row) => { return row.styleEnable != 2 && row.styleEnable != 6; }, handle: (that, row) => that.onJinYongQiYong(row, 1) },
            { label: "移除", ishide: (that, row) => { return row.styleEnable == 2 || row.styleEnable == 4 || row.styleEnable == 6; }, handle: (that, row) => that.onJinYongQiYong(row, 2) },
        ]
    },
];
export default {
    name: "HotSaleBrandPushNewSale5",
    components: {
        MyContainer, datepicker, vxetablebase
    },
    data() {
        return {
            auditVisible: false,
            activities: [],
            timeRanges: [],
            that: this,
            pickerOptions,
            filter: {
                timerange: [(dayjs().subtract(1, 'day').format('YYYY-MM') + '-01'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')],
                cunDangMonths: [],
                saleStartDate: null,
                saleEndDate: null,
                cdStartDate: null,
                cdEndDate: null,
                createdUserId: null,
                styleCodeFirstSaleTime2: null,
                styleEnable: null,
                youXiaoMonth: 6
            },
            pager: { OrderBy: "goodsCount", IsAsc: false },
            tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            summaryarry: {},
            createdUserList: [],
            chooseUserList: [],

            isSum: false,
            delStyleCodeDialog: {
                visible: false,
            },
            delStyleCodeSel: null,
            delStyleCodeUserSel: null,
            delStyleCodeList: [],

            cunDangDialog: {
                visible: false,
                loading: false,
            },
            cunDangMonth: null,
            cunDangAreaEnm: null,
            cunDangOperType: null,
        }
    },
    async mounted() {
        await this.otherdata();
    },
    computed: {
    },
    methods: {
        // async loadData(args) {
        //     this.filter.createdUserId = args.createdUserId;
        //     this.filter.createdUserName = args.createdUserName;
        //     this.filter.timerange = args.timerange;
        //     this.isSum = args.isSum;

        //     this.filter.createdUserNames = args.createdUserNames;
        //     this.filter.createUserAreas = args.createUserAreas;
        //     this.filter.createUserRoles = args.createUserRoles;
        //     this.filter.createUserDeptNames = args.createUserDeptNames;

        //     console.log(this.filter, "this.filter");
        //     this.onSearch();

        //     let ret2 = await GetHotSaleBrandPushNewChooseSearch({ type: 2 });
        //     this.chooseUserList = ret2.data;

        // },
        async otherdata() {
            let ret = await GetHotSaleBrandPushNewChooseSearch({ type: 5 });
            this.createdUserList = ret.data;

            let ret2 = await GetHotSaleBrandPushNewChooseSearch({ type: 2 });
            this.chooseUserList = ret2.data;
        },
        async onSearch() {
            // if (args) {
            //     this.filter.createdUserId = args.createdUserId;
            //     this.filter.createdUserName = args.createdUserName;
            //     this.filter.timerange = args.timerange;
            //     this.isSum = args.isSum;

            //     this.filter.createdUserNames = args.createdUserNames;
            //     this.filter.createUserAreas = args.createUserAreas;
            //     this.filter.createUserRoles = args.createUserRoles;
            //     this.filter.createUserDeptNames = args.createUserDeptNames;

            //     console.log(this.filter, "this.filter");
            // }

            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getParam() {
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.saleStartDate = this.filter.timerange[0];
                this.filter.saleEndDate = this.filter.timerange[1];
            }
            else {
                this.filter.saleStartDate = null;
                this.filter.saleEndDate = null;
            }
            if (this.filter.cunDangMonths && this.filter.cunDangMonths.length == 2) {
                this.filter.cdStartDate = this.filter.cunDangMonths[0];
                this.filter.cdEndDate = this.filter.cunDangMonths[1];
            }
            else {
                this.filter.cdStartDate = null;
                this.filter.cdEndDate = null;
            }
            let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            this.sels = [];
            this.selids = [];
            let param = this.getParam();
            this.listLoading = true
            const res = await GetHotSaleBrandPushNewSaleDtlPageList2(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
                // Object.keys(res.data.summary).forEach(f => {
                //     res.data.summary[f] = res.data.summary[f].toString();
                // });
                this.summaryarry = res.data.summary;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onJinYongQiYong(row, type) {//1新增/恢复，2移除
            //非推新sku被新增删除时
            if (row.styleEnable == 3 || row.styleEnable == 4) {
                let sty = this.tableData.find(f => f.rowId == row.parentId);
                //上级被新增的不允许有任何操作
                if (sty.styleEnable == 1) {
                    this.$message.error("上级来源是【被新增】不允许做任何操作，归还给原始推荐人后才允许操作")
                    return;
                }
                //上级被移除后子集不能做任何操作
                if (sty.styleEnable == 2) {
                    this.$message.error("上级被移除后子集不允许做任何操作")
                    return;
                }

                this.listLoading = true;
                let obj = {
                    businessDimension: "采购推新选品销量-单推荐人单款2",
                    pushNewUserId: sty.createdUserId,
                    pushNewUserName: sty.createdUserName,
                    chooseUserId: sty.chooseUserId,
                    chooseUserName: sty.chooseUserName,
                    hotSaleBrandPushNewGoodsCompeteId2: sty.hotSaleBrandPushNewGoodsCompeteId2,
                    styleCodeFirstCreatedTime: sty.styleCodeFirstCreatedTime,
                    styleCodeFirstSaleTime: sty.styleCodeFirstSaleTime,
                    styleCode: sty.styleCode,
                    goodsCode: row.styleCode,
                    enableStyleCode: (row.styleEnable == 4 ? 3 : 4),
                };
                console.log(obj, "objobjobj");
                const res = await SetHotSaleBrandPushNewSaleEnable2(obj);
                this.listLoading = false;
                if (res?.success == true) {
                    await this.getList();
                }
                else {
                    this.$message.error(res.msg)
                }
            }
            //我自己的sku被新增删除时
            else if (row.styleEnable == 5 || row.styleEnable == 6) {
                let sty = this.tableData.find(f => f.rowId == row.parentId);
                //上级被新增的不允许有任何操作
                if (sty.styleEnable == 1) {
                    this.$message.error("上级来源是【被新增】不允许做任何操作，归还给原始推荐人后才允许操作")
                    return;
                }
                //上级被移除后子集不能做任何操作
                if (sty.styleEnable == 2) {
                    this.$message.error("上级被移除后子集不允许做任何操作")
                    return;
                }
                this.listLoading = true;
                let obj = {
                    businessDimension: "采购推新选品销量-单推荐人单款2",
                    pushNewUserId: sty.createdUserId,
                    pushNewUserName: sty.createdUserName,
                    chooseUserId: sty.chooseUserId,
                    chooseUserName: sty.chooseUserName,
                    hotSaleBrandPushNewGoodsCompeteId2: sty.hotSaleBrandPushNewGoodsCompeteId2,
                    styleCodeFirstCreatedTime: sty.styleCodeFirstCreatedTime,
                    styleCodeFirstSaleTime: sty.styleCodeFirstSaleTime,
                    styleCode: sty.styleCode,
                    goodsCode: row.styleCode,
                    enableStyleCode: (row.styleEnable == 6 ? 5 : 6),
                };
                console.log(obj, "objobjobj");
                const res = await SetHotSaleBrandPushNewSaleEnable2(obj);
                this.listLoading = false;
                if (res?.success == true) {
                    await this.getList();
                }
                else {
                    this.$message.error(res.msg)
                }
            }
            else {
                this.listLoading = true;
                let obj = {
                    businessDimension: "采购推新选品销量-单推荐人单款2",
                    pushNewUserId: row.createdUserId,
                    pushNewUserName: row.createdUserName,
                    styleCode: row.styleCode,
                    enableStyleCode: type,
                };
                const res = await SetHotSaleBrandPushNewSaleEnable2(obj);
                this.listLoading = false;
                if (res?.success == true) {
                    //销量单量联动
                    obj.businessDimension = "采购推新选品销量-单推荐人单款1";
                    const res2 = await SetHotSaleBrandPushNewSaleEnable2(obj);
                    await this.getList();
                }
                else {
                    this.$message.error(res.msg)
                }
            }
        },
        async onAddStyleCodeShow() {
            this.delStyleCodeSel = null;
            this.delStyleCodeUserSel = null;
            this.delStyleCodeDialog.visible = true;

            let res = await GetHotSaleBrandPushNewSaleDelEnable({ businessDimension: "采购推新选品销量-单推荐人单款2" });
            this.delStyleCodeList = res.data;

        },
        async onAddStyleCode() {
            if (!this.delStyleCodeSel || !this.delStyleCodeUserSel) {
                this.$message.error('请选择系列编码和推荐人')
                return;
            }
            let delStyleCodeUserNameSel = this.createdUserList.find(f => f.createdUserId == this.delStyleCodeUserSel)?.createdUserName;
            if (!delStyleCodeUserNameSel) {
                this.$message.error('请选择系列编码和推荐人')
                return;
            }
            this.listLoading = true;
            let obj = {
                businessDimension: "采购推新选品销量-单推荐人单款2",
                pushNewUserId: this.delStyleCodeUserSel,
                pushNewUserName: delStyleCodeUserNameSel,
                styleCode: this.delStyleCodeSel,
                enableStyleCode: 1,
            };
            const res = await SetHotSaleBrandPushNewSaleEnable2(obj);
            this.listLoading = false;
            if (res?.success == true) {
                //销量单量联动
                obj.businessDimension = "采购推新选品销量-单推荐人单款1";
                const res2 = await SetHotSaleBrandPushNewSaleEnable2(obj);
                this.delStyleCodeDialog.visible = false;
                await this.getList();
            }
            else {
                //this.$message.error(res.msg)
            }
        },

        async onExport(opt) {
            let pars = this.getParam();
            if (pars === false) {
                return;
            }
            const params = { ...pars, ...opt };
            let res = await ExportHotSaleBrandPushNewSaleDtlList2(params);
            if (!res?.data) {
                return
            }
            this.$message({ message: "正在后台导出中，请点击头像-在下载管理中查看", type: "success" });
        },

        async onCunDangShow(operType) {
            if (this.sels.length <= 0) {
                this.$message.error('未勾选')
                return;
            }
            let entitys = [];
            this.sels.forEach(row => {
                if (row.parentId == 0) {
                    entitys.push({
                        createdUserId: row.createdUserId,
                        CreatedUserName: row.createdUserName,
                        styleCode: row.styleCode,
                        cunDangDate: this.cunDangMonth,
                        areaEnm: this.cunDangAreaEnm,
                        operType: this.cunDangOperType
                    });
                }
            })
            if (entitys.length <= 0) {
                this.$message.error('请勾选父级，勾选子级无效')
                return;
            }
            this.cunDangMonth = null;
            this.cunDangAreaEnm = null;
            this.cunDangOperType = operType;
            this.cunDangDialog.loading = false;
            this.cunDangDialog.visible = true;
        },
        async onCunDang() {
            if (this.sels.length <= 0) {
                this.$message.error('未勾选')
                return;
            }
            if (!this.cunDangMonth || !this.cunDangAreaEnm || !this.cunDangOperType) {
                this.$message.error('请填写存档月和销量范围')
                return;
            }
            let entitys = [];
            this.sels.forEach(row => {
                if (row.parentId == 0) {
                    entitys.push({
                        createdUserId: row.createdUserId,
                        CreatedUserName: row.createdUserName,
                        styleCode: row.styleCode,
                        cunDangDate: this.cunDangMonth,
                        areaEnm: this.cunDangAreaEnm,
                        operType: this.cunDangOperType
                    });
                }
            })
            if (entitys.length <= 0) {
                this.$message.error('请勾选父级，勾选子级无效')
                return;
            }

            this.$confirm('确定要将勾选的数据存档吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.cunDangDialog.loading = true;
                const res = await HotSaleBrandPushNewSaleStyleCunDang2(entitys);
                this.cunDangDialog.loading = false;
                if (res?.success) {
                    this.$message.success('批量存档成功');
                    this.cunDangDialog.visible = false;
                    await this.getList();
                }
            }).catch(() => {
            });
        },

        async onCunDang2(operType) {
            if (this.sels.length <= 0) {
                this.$message.error('未勾选')
                return;
            }
            let entitys = [];
            this.sels.forEach(row => {
                if (row.parentId == 0) {
                    entitys.push({
                        createdUserId: row.createdUserId,
                        CreatedUserName: row.createdUserName,
                        styleCode: row.styleCode,
                        cunDangDate: '2025-01-01',
                        areaEnm: 0,
                        operType: -1
                    });
                }
            })
            if (entitys.length <= 0) {
                this.$message.error('请勾选父级，勾选子级无效')
                return;
            }
            this.$confirm('确定要将勾选的数据取消存档吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = await HotSaleBrandPushNewSaleStyleCunDang2(entitys);
                if (res?.success) {
                    this.$message.success('取消存档成功');
                    await this.getList();
                }
            }).catch(() => {
            });
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}
</style>
