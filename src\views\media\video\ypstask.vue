<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" 
        :that='that' 
        :isIndex='false' 
        :hasexpand='false' 
        @sortchange='sortchange'
        :tableData='tasklist'
        @select='selectchange' 
        :isSelection='true' 
        :tableCols='tableCols'
        :loading="listLoading"
        :column-cell-class-name="columnCellClass"
        :summaryarry="summaryarry"
        >
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;">
                        <el-select filterable v-model="Filter.isAllAudioStatus" placeholder="是否完成" clearable style="width: 90px">
                            <el-option label="否" value="0" />
                            <el-option label="是" value="1" />
                        </el-select>
                    </el-button> 
                    <el-button style="padding: 0;width: 150px;">
                        <el-input v-model="Filter.videoTaskId" style="padding: 0;width: 130px;" type="number" placeholder="任务编号"  clearable></el-input>
                    </el-button>

                    <el-button style="padding: 0;">
                        <el-select filterable v-model="Filter.platform" placeholder="平台" clearable :collapse-tags="true" @change="onchangeplatform" style="width: 80px">
                            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;">
                        <el-select filterable v-model="Filter.shopId" placeholder="店铺" clearable style="width: 130px">
                            <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.id" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;width: 155px;">
                        <el-input v-model="Filter.productId" placeholder="产品ID" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                    <el-button style="padding: 0;width: 155px;">
                        <el-input v-model="Filter.productShortName" placeholder="产品简称" @keyup.enter.native="onSearch" clearable />
                    </el-button> 
                    <el-button style="padding: 0;width: 130px;">
                        <el-date-picker style="width:100%" v-model="Filter.approvedTime" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" placeholder="审核日期">
                        </el-date-picker>
                    </el-button>
                    <el-button style="padding: 0;width: 90px;">
                        <el-select style="width:100%" v-model="Filter.approvedstatus" clearable filterable placeholder="审核状态">
                            <el-option label="未传" value="-"/>
                            <el-option label="待审" value="1"/>
                            <el-option label="通过" value="2"/>
                            <el-option label="补拍" value="3"/>
                            <el-option label="重拍" value="4"/>
                        </el-select>
                    </el-button>
             
                    <el-button style="padding: 0;width: 90px;">
                        <el-select style="width:100%" v-model="Filter.taskUrgency" clearable filterable placeholder="紧急程度">
                            <el-option v-for="item in taskUrgencyList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;width: 100px;">
                        <el-input v-model="Filter.cuteLqName" placeholder="负责人" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                    <el-button style="padding: 0;width: 100px;">
                        <el-input v-model="Filter.dockingPeopleStr" placeholder="分配拍摄" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                    
                    <el-button  style="margin-left: 20px" @click="onMoveTask" icon="el-icon-share"  v-if="checkPermission('api:media:vediotask:ComplatVideoTaskAsync')" >批量完成</el-button>

                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExeprotVedioTask"  v-if="checkPermission('api:media:vediotask:ExportVedioTaskReport')" >导出</el-button>
                    
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList" />
        </template>
        <!--视频播放-->
        <el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer">
            <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-drawer :title="shstatustitleText" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="uploadVideoAudioVisible"
         direction="btt" size="'auto'" style="position:absolute;overflow:hidden" @close="closeVideoAudioCuteForm">
            <section>
                <videotaskaudio v-if="uploadVideoAudioReload" @playVideo="playVideo" :ckindex="ckindex" :cardHeight="'600px'" 
                :videoProductName='videoProductName'
                :islook="false" ref="videotaskaudio" :videoTaskId='videotaskid' style="height: 90%;width:100%" />
            </section>
            <div class="drawer-footer">
                <el-button @click.native="uploadVideoAudioVisible = false">取消</el-button>
                <my-confirm-button type="submit" :validate="editFormValidate" :loading="editLoading" @click="onVideoAudioSubmit" />
            </div>
        </el-drawer>
        <el-drawer :title="stdetalstatustitleText" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="videVideoAudioVisible" 
        direction="btt" size="'auto'" style="position:absolute;overflow:hidden" @close="closeViewVideoAudioForm">
            <section>
                <videotaskaudio v-if="videVideoAudioReload" @playVideo="playVideo" :cardHeight="'550px'" :ckindex="ckindex" :islook="true" 
                :videoProductName='videoProductName'
                ref="videotaskaudio" :videoTaskId='videotaskid' style="height: 90%;width:100%" />
            </section>
            <div class="drawer-footer">
                <el-button @click.native="videVideoAudioVisible = false">关闭</el-button>
            </div>
        </el-drawer>

        <!--s上传成品视频视频-->
        <el-dialog title="成品视频文件" :visible.sync="UploadSuccessVideo" width="70%" @close="closeUploadSuccessVideo" :element-loading-text="addloadingText" v-loading ="addloading"  v-dialogDrag >
            <el-form :model="addForm" ref="UploadSuccessVideo" label-width="120px" >
                <el-row style="height:200px">
                        <el-col :span="8" >
                            <el-row>
                                <el-form-item label="成品视频一">
                                    <el-upload ref="upload1"
                                            :auto-upload="true" 
                                            :multiple="true" 
                                            :data="1"
                                            :show-file-list="false" 
                                            :http-request="UpSuccesslaod"
                                            >
                                            <el-button size="small" type="primary">点击上传<i class="el-icon-upload el-icon--right"></i></el-button>
                                    </el-upload>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <ul  style="height:160px;overflow-y:scroll;" >
                                    <li v-for="i in UpSuccesslaodList1" class="infinite-list-item">
                                        <el-link  @click="playVideo(i.url)">{{ i.fileName }}</el-link> <span>|</span>
                                      <!--   <el-link type="primary" @click="paloutvedio(i.url)" >播放</el-link> <span>|</span> -->
                                        <el-link type="primary"  @click="downfile(i)" >下载</el-link> <span>|</span>
                                        <el-link type="danger"  @click="removeUpload(i,1)">移除</el-link>
                                    </li>
                                </ul>
                            </el-row>
                        </el-col>
                        <el-col :span="8">
                            <el-row>
                                <el-form-item label="成品视频二">
                                    <el-upload ref="upload2"
                                            :auto-upload="true" 
                                            :multiple="true" 
                                            :data="2"
                                            :show-file-list="false" 
                                            :http-request="UpSuccesslaod"
                                            >
                                            <el-button size="small" type="primary">点击上传<i class="el-icon-upload el-icon--right"></i></el-button>
                                    </el-upload>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <ul  style="height:160px;overflow-y:scroll;" >
                                    <li v-for="i in UpSuccesslaodList2" >
                                        <el-link  @click="playVideo(i.url)">{{ i.fileName }}</el-link> <span>|</span>
                                      <!--   <el-link type="primary" @click="paloutvedio(i.url)" >播放</el-link> <span>|</span> -->
                                        <el-link type="primary"   @click="downfile(i)" >下载</el-link> <span>|</span>
                                        <el-link type="danger"  @click="removeUpload(i,2)">移除</el-link>
                                    </li>
                                </ul>
                            </el-row>
                        </el-col>
                        <el-col :span="8">
                            <el-row>
                                <el-form-item label="成品视频三">
                                    <el-upload ref="upload3"
                                            :auto-upload="true" 
                                            :multiple="true" 
                                            :data="3" 
                                            :show-file-list="false" 
                                            :http-request="UpSuccesslaod"
                                            >
                                            <el-button size="small" type="primary">点击上传<i class="el-icon-upload el-icon--right"></i></el-button>
                                    </el-upload>
                                </el-form-item>
                            </el-row>
                            <el-row  style="height: 100; overflow:auto">
                                <ul  style="height:160px;overflow-y:scroll;" >
                                    <li v-for="i in UpSuccesslaodList3" class="infinite-list-item">
                                        <el-link  @click="playVideo(i.url)">{{ i.fileName }}</el-link> <span>|</span>
                                        <!-- <el-link type="primary" @click="paloutvedio(i.url)" >播放</el-link> <span>|</span> -->
                                        <el-link type="primary"   @click="downfile(i)" >下载</el-link> <span>|</span>
                                        <el-link type="danger"  @click="removeUpload(i,3)">移除</el-link>
                                    </li>
                                </ul>
                            </el-row>
                        </el-col> 
                    </el-row>
                    <el-row style="height:200px">
                        <el-col :span="8" >
                            <el-row>
                                <el-form-item label="成品视频四">
                                    <el-upload ref="upload4" 
                                            :auto-upload="true" 
                                            :multiple="true" 
                                            :data="4"
                                            :show-file-list="false" 
                                            :http-request="UpSuccesslaod"
                                        >
                                        <el-button size="small" type="primary">点击上传<i class="el-icon-upload el-icon--right"></i></el-button>
                                    </el-upload>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <ul  style="height:160px;overflow-y:scroll;" >
                                    <li v-for="i in UpSuccesslaodList4" class="infinite-list-item">
                                        <el-link  @click="playVideo(i.url)">{{ i.fileName }}</el-link> <span>|</span>
                                       <!--  <el-link type="primary" @click="paloutvedio(i.url)" >播放</el-link> <span>|</span> -->
                                        <el-link type="primary"   @click="downfile(i)" >下载</el-link> <span>|</span>
                                        <el-link type="danger"  @click="removeUpload(i,4)">移除</el-link>
                                    </li>
                                </ul>
                            </el-row>
                        </el-col>
                        <el-col :span="8">
                            <el-row>
                                <el-form-item label="成品视频五">
                                    <el-upload ref="upload5"
                                            :auto-upload="true" 
                                            :multiple="true" 
                                            :data="5"
                                            :show-file-list="false" 
                                            :http-request="UpSuccesslaod"
                                            >
                                            <el-button size="small" type="primary">点击上传<i class="el-icon-upload el-icon--right"></i></el-button>
                                    </el-upload>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <ul  style="height:160px;overflow-y:scroll;" >
                                    <li v-for="i in UpSuccesslaodList5" class="infinite-list-item">
                                        <el-link  @click="playVideo(i.url)">{{ i.fileName }}</el-link> <span>|</span>
                                      <!--   <el-link type="primary" @click="paloutvedio(i.url)" >播放</el-link> <span>|</span> -->
                                        <el-link type="primary"   @click="downfile(i)">下载</el-link> <span>|</span>
                                        <el-link type="danger"  @click="removeUpload(i,5)">移除</el-link>
                                    </li>
                                </ul>
                            </el-row>
                        </el-col>
                        <el-col :span="8">
                       
                        </el-col>
                    </el-row> 
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="UploadSuccessVideo = false">关闭</el-button>
                 
                </span>
            </template>
        </el-dialog>
    </my-container>
</template>
<script>

    import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
    import videotaskaudio from '@/views/media/video/videotaskaudio'
    import cesTable from "@/components/Table/table.vue";
    import { formatTime } from "@/utils";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import { getList as getshopList } from '@/api/operatemanage/base/shop'
    import { rulePlatform } from "@/utils/formruletools";
    import { pageViewTaskAsync, cutPickVideoTaskAsync, unCutPickVideoTaskAsync, complatVideoTask  ,moveTaskToOver,
        exportVedioTaskReport,getOutcomeVideo,uploadOutcomeVideo,delOutcomeVideo,getTaskUrgencyList} from '@/api/media/vediotask';
    import { videoTaskAudioAsync } from '@/api/media/video'
    import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'

    const tableCols = [
        { istrue: true, align:'center', prop: 'videoTaskId', label: '任务编号', width: '50', sortable: 'custom' ,fixed: 'left'},
        { istrue: true,  prop: 'productShortName', label: '产品简称', width: '200', sortable: 'custom', fixed: true },
        { istrue: true, align:'center', prop: 'cuteLqName', dingdingcode: 'cuteLqNameDDingUserId', label: '负责人', width: '80', type: 'ddingtalk' , fixed: true},
        { istrue: true, type: 'color'  ,backgroudColor:'rgb(200,200,200)'},
        { istrue: true, width: '140', prop: '', label: '视频一',  merge: true, prop: 'mergeField',  
             cols:  [
                { istrue: true,  align:'center',prop: 'claimant1', dingdingcode: 'claimantId1DDingUserId', label: '拍摄人', width: '80', type: 'ddingtalk' },
                { istrue: true, align:'center',width: '75', prop: 'claimTime1', label: '拍摄时间', formatter: (row) => { if (!row.claimTime1) { return '' } else { return formatTime(row.claimTime1 || '', 'MM月DD日') } } },
                {
                    istrue: true, align:'center', type: "button", width: "85", label: '审核', align:'center',
                    btnList: [
                        { label: "审核", permission: "api:Vedio:VideoTaskAudioAsync",  handle: (that, row) => that.onUploadVideoAudio(row, 1) },
                        { label: "|"},
                        { label: "详情", handle: (that, row) => that.onViewVideoAudio(row, 1) }
                    ]
                },
                { istrue: true,  align:'center',width: '50', prop: 'claimAudioStatus1', label: '审核结果', 
                    type:'html',
                        formatter: (row) => row.claimAudioStatus1 == 2 ? `<div style="color:#4EEE94">通过</div>`:
                             row.claimAudioStatus1 == 3 ? `<div style="color:#f59e1a">补拍</div>`:
                             row.claimAudioStatus1 == 1 ? `<div style="color:#000" >待审</div>`:
                            row.claimAudioStatus1 == 4 ? `<div style="color:red">重拍</div>`:' '
                },
                { istrue: true,  align:'center',width: '75', prop: 'claimAudioTime1', label: '审核时间', formatter: (row) => { if (!row.claimAudioTime1) { return '' } else { return formatTime(row.claimAudioTime1 || '', 'MM月DD日') } } },
                {
                    istrue: true, align:'center', type: "button", width: "50", label: '完成',
                    btnList: [
                        { label: "完成", permission: "api:media:vediotask:CutPickVideoTaskAsync", ishide: (that, row) => that.IsNotPicking(row, 6), handle: (that, row) => that.cutPickTask(row, 1) },
                        { label: "取消", permission: "api:media:vediotask:UnCutPickVideoTaskAsync", ishide: (that, row) => !that.IsNotPicking(row, 6), handle: (that, row) => that.unCutPickTask(row, 1) }
                    ]
                },

                { istrue: true, align:'center', width: '80', prop: 'cutClaimant1', dingdingcode: 'cutClaimantId1DDingUserId', label: '剪辑人', type: 'ddingtalk' },
                { istrue: true, align:'center', width: '75', prop: 'cutClaimTime1', label: '剪辑时间', formatter: (row) => { if (!row.cutClaimTime1) { return '' } else { return formatTime(row.cutClaimTime1 || '', 'MM月DD日') } } },
             
            ]
        },
        { istrue: true, type: 'color'  ,backgroudColor:'rgb(200,200,200)'},
        { istrue: true, width: '140', prop: '', label: '视频二',  merge: true, prop: 'mergeField1',  
             cols:  [
                { istrue: true, align:'center', prop: 'claimant2', dingdingcode: 'claimantId2DDingUserId', label: '拍摄人', width: '80', type: 'ddingtalk' },
                { istrue: true, align:'center', width: '75', prop: 'claimTime2', label: '拍摄时间', formatter: (row) => 
                    { if (!row.claimTime2) { return '' } else { return formatTime(row.claimTime2 || '', 'MM月DD日') } } },
                 {
                    istrue: true,  align:'center',type: "button", width: "85", label: '审核', align:'center',
                    btnList: [
                        { label: "审核", permission: "api:Vedio:VideoTaskAudioAsync", handle: (that, row) => that.onUploadVideoAudio(row, 2) },
                        { label: "|"},
                        { label: "详情", handle: (that, row) => that.onViewVideoAudio(row, 2)  }
                    ] 
                },
             
                { istrue: true,  align:'center',width: '50', prop: 'claimAudioStatus2', label: '审核结果', 
                    
                    type:'html',
                            formatter: (row) => row.claimAudioStatus2 == 2 ? `<div style="color:#4EEE94">通过</div>`:
                                row.claimAudioStatus2 == 3 ? `<div style="color:#f59e1a">补拍</div>`:
                                row.claimAudioStatus2 == 1 ? `<div  style="color:#000" >待审</div>`:
                                row.claimAudioStatus2 == 4 ? `<div style="color:red">重拍</div>`:''
                },
             
                { istrue: true,  align:'center',width: '75', prop: 'claimAudioTime2', label: '审核时间', formatter: (row) => { if (!row.claimAudioTime2) { return '' } else { return formatTime(row.claimAudioTime2 || '', 'MM月DD日') } } },
                { 
                    istrue: true,  align:'center',type: "button", width: "50", label: '完成',
                    btnList: [
                        { label: "完成", permission: "api:media:vediotask:CutPickVideoTaskAsync", ishide: (that, row) => that.IsNotPicking(row, 7), handle: (that, row) => that.cutPickTask(row, 2) },                    
                        { label: "取消", permission: "api:media:vediotask:UnCutPickVideoTaskAsync", ishide: (that, row) => !that.IsNotPicking(row, 7), handle: (that, row) => that.unCutPickTask(row, 2) }
                    ]
                },
              
                { istrue: true, align:'center', width: '80', prop: 'cutClaimant2', dingdingcode: 'cutClaimantId2DDingUserId', label: '剪辑人', type: 'ddingtalk' },
                { istrue: true,  align:'center',width: '75', prop: 'cutClaimTime2', label: '剪辑时间', formatter: (row) => { if (!row.cutClaimTime2) { return '' } else { return formatTime(row.cutClaimTime2 || '', 'MM月DD日') } } },
               
            ]
        },
        { istrue: true, type: 'color'  ,backgroudColor:'rgb(200,200,200)'},

        { istrue: true, width: '140', prop: '', label: '视频三',  merge: true, prop: 'mergeField2',  
             cols:  [
                { istrue: true, align:'center', prop: 'claimant3', dingdingcode: 'claimantId3DDingUserId', label: '拍摄人', width: '80', type: 'ddingtalk' },
                { istrue: true, align:'center', width: '75', prop: 'claimTime3', label: '拍摄时间', formatter: (row) => 
                    { if (!row.claimTime3) { return '' } else { return formatTime(row.claimTime3 || '', 'MM月DD日') } } },
                {
                    istrue: true,  align:'center',type: "button", width: "85", label: '审核', align:'center',
                    btnList: [
                        { label: "审核", permission: "api:Vedio:VideoTaskAudioAsync",  handle: (that, row) => that.onUploadVideoAudio(row, 3) },
                        { label: "|"},
                        { label: "详情",  handle: (that, row) => that.onViewVideoAudio(row, 3)  }
                    ]
                },
                { istrue: true,  align:'center',width: '50', prop: 'claimAudioStatus3', label: '审核结果',
                    type:'html',
                            formatter: (row) => row.claimAudioStatus3 == 2 ? `<div style="color:#4EEE94">通过</div>`:
                                row.claimAudioStatus3 == 3 ? `<div style="color:#f59e1a">补拍</div>`:
                                row.claimAudioStatus3 == 1 ? `<div style="color:#000" > 待审</div>`:
                                row.claimAudioStatus3 == 4 ? `<div style="color:red">重拍</div>`:' '
                },
                {
                    istrue: true, align:'center', type: "button", width: "50", label: '完成',
                    btnList: [
                        { label: "完成", permission: "api:media:vediotask:CutPickVideoTaskAsync", ishide: (that, row) => that.IsNotPicking(row, 8), handle: (that, row) => that.cutPickTask(row, 3) },
                        { label: "取消", permission: "api:media:vediotask:UnCutPickVideoTaskAsync", ishide: (that, row) => !that.IsNotPicking(row, 8), handle: (that, row) => that.unCutPickTask(row, 3) }
                    ]
                },
                { istrue: true, align:'center', width: '75', prop: 'claimAudioTime3', label: '审核时间',formatter: (row) => { if (!row.claimAudioTime3) { return '' } else { return formatTime(row.claimAudioTime3 || '', 'MM月DD日') } } },
                { istrue: true, align:'center', prop: 'cutClaimant3', dingdingcode: 'cutClaimantId3DDingUserId', label: '剪辑人', width: '80', type: 'ddingtalk' },
                { istrue: true, align:'center', width: '75', prop: 'cutClaimTime3', label: '剪辑时间', formatter: (row) => { if (!row.cutClaimTime3) { return '' } else { return formatTime(row.cutClaimTime3 || '', 'MM月DD日') } } },
             
            ]
        },
        { istrue: true, type: 'color'  ,backgroudColor:'rgb(200,200,200)'},
        { istrue: true, width: '140', prop: '', label: '视频四',  merge: true, prop: 'mergeField3',  
             cols:  [
                { istrue: true, align:'center', prop: 'claimant4', dingdingcode: 'claimantId4DDingUserId', label: '拍摄人', width: '80', type: 'ddingtalk' },
                { istrue: true, align:'center', width: '75', prop: 'claimTime4', label: '拍摄时间', formatter: (row) => 
                    { if (!row.claimTime4) { return '' } else { return formatTime(row.claimTime4 || '', 'MM月DD日') } } },
                {
                    istrue: true, align:'center', type: "button", width: "85", label: '审核', align:'center',
                    btnList: [
                        { label: "审核", permission: "api:Vedio:VideoTaskAudioAsync",  handle: (that, row) => that.onUploadVideoAudio(row, 4) },
                        { label: "|"},
                        { label: "详情",  handle: (that, row) => that.onViewVideoAudio(row, 4) }
                    ]
                },
               
                { istrue: true, align:'center', width: '50', prop: 'claimAudioStatus4', label: '审核结果',  type:'html',
                        formatter: (row) => row.claimAudioStatus4 == 2 ? `<div style="color:#4EEE94">通过</div>`:
                             row.claimAudioStatus4 == 3 ? `<div style="color:#f59e1a">补拍</div>`:
                             row.claimAudioStatus4 == 1 ? `<div style="color:#000" >待审</div>`:
                             row.claimAudioStatus4 == 4 ? `<div style="color:red">重拍</div>`:' '
                },
                { istrue: true,  align:'center',width: '85', prop: 'claimAudioTime4', label: '审核时间', formatter: (row) => { if (!row.claimAudioTime4) { return '' } else { return formatTime(row.claimAudioTime4 || '', 'MM月DD日') } } },
                {
                    istrue: true,  align:'center',type: "button", width: "50", label: '完成',
                    btnList: [
                        { label: "完成", permission: "api:media:vediotask:CutPickVideoTaskAsync", ishide: (that, row) => that.IsNotPicking(row, 9), handle: (that, row) => that.cutPickTask(row, 4) },
                        { label: "取消", permission: "api:media:vediotask:UnCutPickVideoTaskAsync", ishide: (that, row) => !that.IsNotPicking(row, 9), handle: (that, row) => that.unCutPickTask(row, 4) }
                    ]
                },

                { istrue: true,  align:'center',prop: 'cutClaimant4', dingdingcode: 'cutClaimantId4DDingUserId', label: '剪辑人', width: '80', type: 'ddingtalk' },
                { istrue: true,  align:'center',width: '75', prop: 'cutClaimTime4', label: '剪辑时间', formatter: (row) => { if (!row.cutClaimTime4) { return '' } else { return formatTime(row.cutClaimTime4 || '', 'MM月DD日') } } },
                
            ]
        },
        { istrue: true, type: 'color'  ,backgroudColor:'rgb(200,200,200)'},
        { istrue: true, width: '140', prop: '', label: '视频五',  merge: true, prop: 'mergeField4', 
             cols:  [
             { istrue: true,  align:'center',prop: 'claimant5', dingdingcode: 'claimantId5DDingUserId', label: '拍摄人', width: '80', type: 'ddingtalk' },
                { istrue: true, align:'center', width: '75', prop: 'claimTime5', label: '拍摄时间', formatter: (row) => 
                    { if (!row.claimTime5) { return '' } else { return formatTime(row.claimTime5 || '', 'MM月DD日') } } },

                {
                    istrue: true,  align:'center',type: "button", width: "85", label: '审核', align:'center',
                    btnList: [
                        { label: "审核", permission: "api:Vedio:VideoTaskAudioAsync", handle: (that, row) => that.onUploadVideoAudio(row, 5) },
                        { label: "|"},
                        { label: "详情",  handle: (that, row) => that.onViewVideoAudio(row, 5) }
                    ]
                },
     
                { istrue: true,  align:'center',width: '50', prop: 'claimAudioStatus5', label: '审核结果',
                    type:'html',
                        formatter: (row) => row.claimAudioStatus5 == 2 ? `<div style="color:#4EEE94">通过</div>`:
                             row.claimAudioStatus5 == 3 ? `<div style="color:#f59e1a">补拍</div>`:
                                  row.claimAudioStatus5 == 1 ? `<div style="color:#000">待审</div>`:
                             row.claimAudioStatus5 == 4 ? `<div style="color:red">重拍</div>`:' '
                },
                { istrue: true,  align:'center',width: '75', prop: 'claimAudioTime5', label: '审核时间', formatter: (row) => { if (!row.claimAudioTime5) { return '' } else { return formatTime(row.claimAudioTime5 || '', 'MM月DD日') } } },
                {
                    istrue: true,  align:'center',type: "button", width: "50", label: '完成', align:'center',
                    btnList: [
                        { label: "完成", permission: "api:media:vediotask:CutPickVideoTaskAsync", ishide: (that, row) => that.IsNotPicking(row, 10), handle: (that, row) => that.cutPickTask(row, 5) },
                        { label: "取消", permission: "api:media:vediotask:UnCutPickVideoTaskAsync", ishide: (that, row) => !that.IsNotPicking(row, 10), handle: (that, row) => that.unCutPickTask(row, 5) }
                    ]
                },
                { istrue: true, align:'center', prop: 'cutClaimant5', dingdingcode: 'cutClaimantId5DDingUserId', label: '剪辑人', width: '80', type: 'ddingtalk' },
                { istrue: true,  align:'center',width: '75', prop: 'cutClaimTime5', label: '剪辑时间', formatter: (row) => { if (!row.cutClaimTime5) { return '' } else { return formatTime(row.cutClaimTime5 || '', 'MM月DD日') } } },
               
             
            ]
        },
        { istrue: true, type: 'color'  ,backgroudColor:'rgb(200,200,200)'},
        {istrue: true, prop: 'dockingPeopleStr', label: '分配拍摄', width: '90' },
        {
            istrue: true,  align:'center', type: "button", label: '操作', width: "120",fixed:"right",
            btnList: [
                { label: "成品视频", handle: (that, row) => that.UploadComplatVideoTask(row) }
          
                ,{ label: "完成", permission: "api:media:vediotask:ComplatVideoTaskAsync", ishide: (that, row) => { return row.isAudioComplate == 1 }, handle: (that, row) => that.onComplatVideoTask(row) }
            ]
        }
    ];
    export default {
        name: "Users",
        inject:['reload'],
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, videotaskaudio, videoplayer },
        data() {
            return {
                that: this,
                Filter: {
                    approvedTime: null,
                    isAudioComplate: "0",
                    isShop: "0",
                },
                stdetalstatustitleText:null,
                shstatustitleText:null,
                addloadingText:null,
                uploadVideoAudioReload: false,
                uploadVideoAudioVisible: false,
                videVideoAudioVisible: false,
                videVideoAudioReload: false,
                UploadSuccessVideo:false,
                
                platformList: [],//平台下拉
                shopList: [],
                updateForm: {},
                taskUrgencyList: [],
                tasklist: [],
                selids:[],
                tableCols: tableCols,
                total: 0,
                addTask: false,
                summaryarry: { videoTaskId_sum: " _" },
                pager: { OrderBy: "videoTaskId", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                dialogVisible: false,
                videoplayerReload: false,
                addloading :false,
                videoUrl: '',
                ckindex: 0,
                addForm:{ },
                UpSuccesslaodList1:[],
                UpSuccesslaodList2:[],
                UpSuccesslaodList3:[],
                UpSuccesslaodList4:[],
                UpSuccesslaodList5:[],
                UpSuccesslaodTaskId:0,
            };
        },
        watch: {
        },
        async mounted() {

            this.onSearch();
            await this.setPlatform();//平台下拉
            await this.getTaskUrgencyList();//紧急状态下拉
            await this.getFirstSceneList();
        },
        methods: {
            async onExeprotVedioTask(){
                var pager = this.$refs.pager.getPager(); 
                this.Filter.isUploadVideo =1;
                const params = {
                    ...pager,
                    ...this.pager,
                    ...this.Filter,
                    exporttype:"over"

                };
                this.pageLoading = true;
                var res = await exportVedioTaskReport(params);
                if (res?.data?.type == 'application/json') {return;}
                this.pageLoading = false;
                const aLink = document.createElement("a");
                var blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '短视频已拍摄任务导出.xlsx')
                aLink.click()

            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.videoTaskId);
                })
            },
            //批量
            async onMoveTask(){
                if(this.selids.length==0){
                this.$message({ type: 'warning', message: "请选择一个任务" });
                return;
                }
                this.$confirm("选中的任务将会移动到完成列表，是否确定", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(async () => {
                    const res= await moveTaskToOver(this.selids)
                    if (res?.success)  {
                        this.$message({ message: '移动成功！', type: "success" });
                        this.selids=[];
                        this.onRefresh();
                        var self = this;
                        setTimeout(() => { self.reload();}, 100); 
                    }
                });
            },
             //设置紧急状态下拉
            async getTaskUrgencyList() {
                const res = await getTaskUrgencyList();
                this.taskUrgencyList = res.data || [];
            },
            //设置平台下拉
            async setPlatform() {
                var pfrule = await rulePlatform();
                this.platformList = pfrule.options;
            },
            async onchangeplatform(val) {
                const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100 });
                this.shopList = res1.data.list;
            },
            OpenDdingPicking(id){
            if(id){

                window.location.href = "dingtalk://dingtalkclient/action/sendmsg?dingtalk_id="+id; 
            }else{

                this.$message({ type: 'warning', message: "钉钉号未设置" });
            }
            },
             paloutvedio(url){
                window.open(url,"_blank")
             },

            columnCellClass(rowIndex,columnName,rowData){
                if (columnName =='s_CNO'){
                    return 'bgColor';
                }
            },	
            //上传成品视频视频
            async UploadComplatVideoTask(row){
               //获取成品视频视频
               this.UploadSuccessVideo=true;
               this.addloading = true;
               this.UpSuccesslaodTaskId = row.videoTaskId;
               const res = await getOutcomeVideo({ videoTaskId:this.UpSuccesslaodTaskId});
               if (res?.success) {
                
                    this.UpSuccesslaodList1=res.data[0].details;
                    this.UpSuccesslaodList2=res.data[1].details;
                    this.UpSuccesslaodList3=res.data[2].details;
                    this.UpSuccesslaodList4=res.data[3].details;
                    this.UpSuccesslaodList5=res.data[4].details;
                } 
               this.addloading = false;
            },
            //关闭成品视频视频
            closeUploadSuccessVideo(){
                this.UpSuccesslaodList1=[];
                this.UpSuccesslaodList2=[];
                this.UpSuccesslaodList3=[];
                this.UpSuccesslaodList4=[];
                this.UpSuccesslaodList5=[];
                this.addloadingText =null;
                this.UpSuccesslaodTaskId=0;
            },
     
            async UpSuccesslaod(item){
                this.addloading = true;
                this.atfterUplaodData = null;
                var vindex  = item.data;
                var filename = item.file.name;
                this.addloadingText = "正在上传:"+item.file.name;
                await this.AjaxFile(item.file, 0,"");
                if(this.atfterUplaodData !=null)
                {
                    await this.AfterUpSuccesslaod(vindex,filename);
                }
                this.addloadingText = null;
                this.addloading = false;
            },
            //上传成果
            async AfterUpSuccesslaod(index,filename){
                const form = new FormData();
                var cururl = this.atfterUplaodData.url;
                this.atfterUplaodData.fileName =filename;
                form.append("upfile",  JSON.stringify(this.atfterUplaodData)); 
                form.append("index", index);
                form.append("taskId",  this.UpSuccesslaodTaskId);
                const res = await uploadOutcomeVideo(form);
                if (res?.success) {
                    switch(index){
                        case 1:
                            this.UpSuccesslaodList1.push({ fileName :res.data.fileName,upLoadvedioid : res.data.uploadId ,url:cururl});
                        break;
                        case 2:
                            this.UpSuccesslaodList2.push({ fileName :res.data.fileName,upLoadvedioid : res.data.uploadId ,url:cururl});
                        break;
                        case 3:
                            this.UpSuccesslaodList3.push({ fileName :res.data.fileName,upLoadvedioid : res.data.uploadId ,url:cururl});
                        break;
                        case 4:
                            this.UpSuccesslaodList4.push({ fileName :res.data.fileName,upLoadvedioid : res.data.uploadId ,url:cururl});
                        break;
                        case 5:
                            this.UpSuccesslaodList5.push({ fileName :res.data.fileName,upLoadvedioid : res.data.uploadId ,url:cururl});
                        break;
                        
                    }
                } 

            },
            async AjaxFile(file,i,batchnumber) {
                var name = file.name; //文件名
                var size = file.size; //总大小shardSize = 2 * 1024 * 1024,
                var shardSize = 15 * 1024 * 1024;
                var shardCount = Math.ceil(size / shardSize); //总片数
                if (i >= shardCount) {
                    return;
                }
                //计算每一片的起始与结束位置
                var start = i * shardSize;
                var end = Math.min(size, start + shardSize);
                //构造一个表单，FormData是HTML5新增的
                i=i+1;
                var form = new FormData();
                form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分
                form.append("batchnumber", batchnumber);
                form.append("fileName", name);
                form.append("total", shardCount); //总片数
                form.append("index", i); //当前是第几片
                const res = await xMTVideoUploadBlockAsync(form);
                if (res?.success) {
                    if(i == shardCount){
                        this.atfterUplaodData = res.data;
                    }else{
                        await this.AjaxFile(file, i,res.data);
                    }
                }else{
                    this.$message({ message: res?.msg, type: "warning" });

                }
            },
            //移除上传文件
            async removeUpload(uploadrow,index){
                this.addloading = true;
                const res = await delOutcomeVideo({ upLoadvedioid:uploadrow.upLoadvedioid});
                if (res?.success) {
                    switch(index){
                        case 1:
                            for(let num in this.UpSuccesslaodList1){
                                if(this.UpSuccesslaodList1[num].upLoadvedioid==uploadrow.upLoadvedioid){
                                    this.UpSuccesslaodList1.splice(num,1)
                                }
                            }
                            break;
                        case 2:
                             for(let num in this.UpSuccesslaodList2){
                                if(this.UpSuccesslaodList2[num].upLoadvedioid==uploadrow.upLoadvedioid){
                                    this.UpSuccesslaodList2.splice(num,1)
                                }
                            }
                            break;
                        case 3:
                        for(let num in this.UpSuccesslaodList3){
                                if(this.UpSuccesslaodList3[num].upLoadvedioid==uploadrow.upLoadvedioid){
                                    this.UpSuccesslaodList3.splice(num,1)
                                }
                            }
                            break;
                        case 4:
                        for(let num in this.UpSuccesslaodList4){
                                if(this.UpSuccesslaodList4[num].upLoadvedioid==uploadrow.upLoadvedioid){
                                    this.UpSuccesslaodList4.splice(num,1)
                                }
                            }
                            break;
                        case 5:
                        for(let num in this.UpSuccesslaodList5){
                                if(this.UpSuccesslaodList5[num].upLoadvedioid==uploadrow.upLoadvedioid){
                                    this.UpSuccesslaodList5.splice(num,1)
                                }
                            }
                            break;
                    }
                } 
               this.addloading = false;
            },

            async downfile(row){
                var filetype= 'video/mp4'
                var xhr = new XMLHttpRequest();
                xhr.open('GET', row.url, true);
                xhr.responseType = 'arraybuffer'; // 返回类型blob
                xhr.onload = function() {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                        let blob = this.response;
                        // 转换一个blob链接
                        // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，
                        // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定
             
                        let downLoadUrl = window.URL.createObjectURL(new Blob([blob], {type: filetype}));
                        // 视频的type是video/mp4，图片是image/jpeg
                        // 01.创建a标签
                        let a = document.createElement('a');
                        // 02.给a标签的属性download设定名称
                        a.download = row.fileName;
                        // 03.设置下载的文件名
                        a.href = downLoadUrl;
                        // 04.对a标签做一个隐藏处理
                        a.style.display = 'none';
                        // 05.向文档中添加a标签
                        document.body.appendChild(a);
                        // 06.启动点击事件
                        a.click();
                        // 07.下载完毕删除此标签
                        a.remove();
                    };
                    };
                xhr.send();
            },
            async onComplatVideoTask(row) {
                var that = this;
                this.$confirm("确认完成, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(async () => {
                    var res = await complatVideoTask({ videoTaskId: row.videoTaskId, type: 2 });
                    if(res?.success){
                        that.$message({ message: '操作成功', type: "success" });
                        that.getTaskList();
                    }
                 
                });
            },
            //取消拍摄领取任务
            async unCutPickTask(row, index) {
                var that = this;
                this.$confirm("确认取消完成剪辑, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(async () => {
                        var res = await unCutPickVideoTaskAsync({ videoTaskId: row.videoTaskId, index: index })
                       if(res?.success){
                        that.$message({ message: '取消成功', type: "success" });
                        that.getTaskList();
                       }
                       
                    });
            },
            //拍摄领取任务
            async cutPickTask(row, index) {
                var that = this;
                this.$confirm("确认完成剪辑, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(async () => {
                        var res =  await cutPickVideoTaskAsync({ videoTaskId: row.videoTaskId, index: index })
                        if(res?.success){
                        that.$message({ message: '领取成功', type: "success" });
                        that.onRefresh();
                        }
                    });
            },
            //查看删除拍摄领取任务按钮显藏
            IsNotPicking(row, index) {
                var result = false;
                switch (index) {
                    case 1:
                        if (row.claimant1 != null && row.claimant1 != "") {
                            result = true;
                        }
                        break;
                    case 2:
                        if (row.claimant2 != null && row.claimant2 != "") {
                            result = true;
                        }
                        console.log(result)
                        break;
                    case 3:
                        if (row.claimant3 != null && row.claimant3 != "") {
                            result = true;
                        }
                        break;
                    case 4:
                        if (row.claimant4 != null && row.claimant4 != "") {
                            result = true;
                        }
                        break;
                    case 5:
                        if (row.claimant5 != null && row.claimant5 != "") {
                            result = true;
                        }
                        break;
                    case 6:
                        if (row.cutClaimant1 != null && row.cutClaimant1 != "") {
                            result = true;
                        }
                        break;
                    case 7:
                        if (row.cutClaimant2 != null && row.cutClaimant2 != "") {
                            result = true;
                        }
                        break;
                    case 8:
                        if (row.cutClaimant3 != null && row.cutClaimant3 != "") {
                            result = true;
                        }
                        break;
                    case 9:
                        if (row.cutClaimant4 != null && row.cutClaimant4 != "") {
                            result = true;
                        }
                        break;
                    case 10:
                        if (row.cutClaimant5 != null && row.cutClaimant5 != "") {
                            result = true;
                        }
                        break;
                    default:
                        if (row.audioStatus != null && row.audioStatus > 0) {
                            result = true;
                        }
                        break;

                }
                return result;
            },
            playVideo(videoUrl) {
                this.videoplayerReload = false;
                this.videoplayerReload = true;
                this.dialogVisible = true;
                this.videoUrl = videoUrl;
            },
            async closeVideoPlyer() {
                this.videoplayerReload = false;
            },
            async onUploadVideoAudio(row, index) {
                this.uploadVideoAudioReload = false;
                this.uploadVideoAudioVisible = true;
                this.uploadVideoAudioReload = true;
                this.ckindex = index;
                this.videotaskid = row.videoTaskId;
                this.videoProductName = row.productShortName;
                this.shstatustitleText = "任务审核    任务编号：" +row.videoTaskId+ "   产品简称："+ row.productShortName;
            },
            async closeVideoAudioCuteForm() {
                this.uploadVideoAudioReload = false;
            },
            async onViewVideoAudio(row, index) {
                this.videVideoAudioReload = false;
                this.videVideoAudioVisible = true;
                this.videVideoAudioReload = true;
                this.videoProductName = row.productShortName;
                this.videotaskid = row.videoTaskId;
                this.stdetalstatustitleText = "审核详情      任务编号：" +row.videoTaskId+ "   产品简称："+ row.productShortName;
                this.ckindex = index;
            },
            async closeViewVideoAudioForm() {
                this.videVideoAudioReload = false;
            },
            async onVideoAudioSubmit() {
                var that = this;
                var data = this.$refs.videotaskaudio.getSaveData();
                var hasallcheck = false;
                data.videoList.forEach(i=>{
                    if(i.StatusEnum == 1 && i.CuteType == 0){
                        hasallcheck = true;
                    }
                }); 
                if(hasallcheck){
                    that.$message({ type: 'warning', message:"请选择是否通过" });
                    return ;

                }
                const res = await videoTaskAudioAsync(data);
                if (res?.success) {
                 
                    that.$message({ type: 'success', message: '提交成功' });
                    that.onRefresh();
                    if(data.audioStatus == 2){

                        this.uploadVideoAudioVisible = false;
                    }else{
                        this.$refs.videotaskaudio.loadList();
                        this.uploadVideoAudioVisible = false;
                    }
               }
            },
            async openVideo(url) {
                var that = this;
                this.$copyText(url).then(function (e) {
                    that.$message({ type: 'success', message: '复制成功' });
                    window.open(url);
                }, function (e) {
                    that.$message({ type: 'success', message: '复制失败' });
                    window.open(url);
                })
            },

            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.getTaskList();
            },
            async getTaskList() {

                var pager = this.$refs.pager.getPager();
                if(this.Filter.approvedstatus =="-"){
                    this.Filter.approvedstatus =null;
                }
                
                const params = {
                    ...pager,
                    isUploadVideo: 1,
                    ...this.pager,
                    ...this.Filter
                };
                this.listLoading = true;
                const res = await pageViewTaskAsync(params);
                this.listLoading = false;
                //console.log(res.data.summary)
                this.total = res.data.total;
                this.tasklist = res.data.list;
                //this.summaryarry=res.data.summary;
            },
          
           
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
