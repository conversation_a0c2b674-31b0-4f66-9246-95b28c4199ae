<template>
    <my-container>
        <el-tabs v-model="activeName" style="height: 95%;" @tab-click="tabclick" type="card">
            <el-tab-pane label="全部订单" name="one" style="height: 100%;">
                <indexgrid ref="indexgrid0" :tableCols="getTableCols(0)" :orderImportType="99" :isshowAll="true"
                    :tablekey="'allordergrid'" />
            </el-tab-pane>
            <el-tab-pane label="各仓平均时长汇总" name="indexchat" style="height: 100%;" lazy>
                <indexchat ref="indexchat" />
            </el-tab-pane>
            <el-tab-pane label="组团配货" name="two" style="height: 100%;" lazy v-if="false">
                <indexgrid ref="indexgrid1" :tableCols="getTableCols(1)" :orderImportType="0" :isshowZt="true"
                    :tablekey="'ztphgrid'" />
            </el-tab-pane>
            <el-tab-pane label="波次拣货" name="three" style="height: 100%;" lazy v-if="false">
                <indexgrid ref="indexgrid2" :tableCols="getTableCols(2)" :orderImportType="1" :isshowJh="true"
                    :tablekey="'bcjhgrid'" />
            </el-tab-pane>
            <el-tab-pane label="裁剪" name="four" style="height: 100%;" lazy v-if="false">
                <indexgrid ref="indexgrid3" :tableCols="getTableCols(3)" :orderImportType="2" :isshowCj="true"
                    :tablekey="'cjgrid'" />
            </el-tab-pane>
            <el-tab-pane label="其他订单" name="five" style="height: 100%;" lazy v-if="false">
                <indexgrid ref="indexgrid4" :tableCols="getTableCols(4)" :orderImportType="10" :isshowOther="true"
                    :tablekey="'qtddgrid'" />
            </el-tab-pane>
            <el-tab-pane label="补货批次" name="six" style="height: 100%;" lazy v-if="false">
                <indexgrid ref="indexgrid5" :tableCols="getTableCols(5)" :orderImportType="4" :isshowpick="true"
                    :tablekey="'bhpcgrid'" />
            </el-tab-pane>
            <el-tab-pane label="导入数据" name="seven" style="height: 100%;" v-if="checkPermission('PressNewOrderNodeImport')"
                lazy>
                <importtab ref="importtab" />
            </el-tab-pane>
            <el-tab-pane label="补货巡检" name="bhxj" style="height: 100%;" lazy v-if="false">
                <openwebjushuitanjhpc ref="openwebjushuitanjhpc" />
            </el-tab-pane>
            <el-tab-pane label="实时库存" name="sskc" style="height: 100%;" lazy>
                <realtimeinventory ref="realtimeinventory" />
            </el-tab-pane>
            <el-tab-pane label="仓库缺货巡检" name="ckqhxj" style="height: 100%;" lazy v-if="false">
                <realtimeinventorycheck ref="realtimeinventorycheck" />
            </el-tab-pane>
            <el-tab-pane label="实时缺货订单" name="ssqhdd" style="height: 100%;" lazy>
                <realtimelackorder ref="realtimelackorder" />
            </el-tab-pane>
            <el-tab-pane label="延迟补货" name="ycbh" style="height: 100%;" lazy>
                <realtimereplenisorder ref="realtimereplenisorder" />
            </el-tab-pane>
            <el-tab-pane label="通道维护" name="tdwh" style="height: 100%;" lazy >
                <wareaislelist ref="wareaislelist" />
            </el-tab-pane>
        </el-tabs>

    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import indexgrid from '@/views/order/newordernodes/indexgrid'
import importtab from '@/views/order/newordernodes/importtab'
import indexchat from '@/views/order/newordernodes/indexchat'
import openwebjushuitanjhpc from '@/views/order/newordernodes/openwebjushuitanjhpc'
import realtimeinventory from '@/views/order/newordernodes/realtimeinventory'
import realtimeinventorycheck from '@/views/order/newordernodes/realtimeinventorycheck'
import realtimelackorder from '@/views/order/newordernodes/realtimelackorder'
import realtimereplenisorder from '@/views/order/newordernodes/realtimereplenisorder'
import wareaislelist from '@/views/order/newordernodes/wareaislelist'
const tableCols = [
    { istrue: true, prop: 'orderNoInner', label: '内部订单号', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogDetail(row) },
    { istrue: true, prop: 'orderNo', label: '线上订单号', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'shopCode', label: '店铺名称', width: '110', sortable: 'custom', formatter: (row) => row.shopName },
    { istrue: true, prop: 'payTime', label: '付款时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'lackReason', label: '影响原因', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'confirmTime', label: '审单时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'replenishTime', label: '补货完成时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'createBatchTime', label: '生成批次时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'printTime', label: '打单时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'pickTime', label: '配货时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'deliveryTime', label: '发货时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'weighTime', label: '称重时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'batchStartTime', label: '领批次时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'batchEndTime', label: '批次结束时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'inWarehouseTimeHH', label: '在仓时长', width: '100', sortable: 'custom', sortable: 'custom', tipmesg: '小时:发货时间-付款时间' },//(如果付款时间在上午8点到下午4点前 则以当前付款时间作为计算 否则以第二天上午8点作为计算时间)
    { istrue: true, prop: 'replenishSpanTimeHH', label: '补货时长', width: '100', sortable: 'custom', tipmesg: '小时:审单时间-付款时间' },//(付款时间大于下午4点则为付款时间加32小时否则判断是不是在上午8点前是的话则为付款时间加8小时否则获取真实付款时间)
    { istrue: true, prop: 'createBatchSpanTimeHH', label: '生成批次时长', width: '120', sortable: 'custom', tipmesg: '小时:补货完成时间-审单时间' },
    { istrue: true, prop: 'confirmSpanTimeHH', label: '审单时长', width: '100', sortable: 'custom', tipmesg: '小时:生成批次时间-补货完成时间' },
    { istrue: true, prop: 'printSpanTimeHH', label: '打单时长', width: '100', sortable: 'custom', tipmesg: '小时:打单时间-审单时间' },
    { istrue: true, prop: 'batchWaitSpanTimeHH', label: '波次等待时长', width: '120', sortable: 'custom', tipmesg: '小时:领批次时间-打单时间(审单时间)' },//波次等待时长
    { istrue: true, prop: 'batchSpanTimeHH', label: '波次时长', width: '100', sortable: 'custom', tipmesg: '小时:批次结束时间-领批次时间' },//波次时长  ,tipmesg:'分钟'
    { istrue: true, prop: 'pickSpanTimeHH', label: '配货时长', width: '100', sortable: 'custom', tipmesg: '小时:配货时间(发货时间)-领批次时间或者为:配货时间(发货时间)-打单时间' },
    { istrue: true, prop: 'deliverySpanTimeHH', label: '打包时长', width: '100', sortable: 'custom', tipmesg: '小时:发货时间-配货时间' },
    { istrue: true, prop: 'confirmer', label: '审单人员', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'printer', label: '打单人员', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'picker', label: '配货人', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'batchPicker', label: '波次操作人', width: '90', sortable: 'custom' },//领波次人
    { istrue: true, prop: 'delivery', label: '发货人', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'pickCount', label: '拣货次数', width: '85', sortable: 'custom' },
    { istrue: true, prop: 'label', label: '订单标签', width: '240', sortable: 'custom' },
    { istrue: true, prop: 'orderType', label: '单据类型', width: '85', sortable: 'custom', formatter: (row) => row.orderType == 1 ? 'PDA' : '纸质' },
];

const tableCols1 = [
    { istrue: true, prop: 'orderNoInner', label: '内部订单号', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogDetail(row) },
    { istrue: true, prop: 'orderNo', label: '线上订单号', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'shopCode', label: '店铺名称', width: '110', sortable: 'custom', formatter: (row) => row.shopName },
    { istrue: true, prop: 'payTime', label: '付款时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'confirmTime', label: '审单时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'printTime', label: '打单时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'pickTime', label: '配货时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'deliveryTime', label: '发货时间', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'confirmSpanTimeHH', label: '审单时长', width: '100', sortable: 'custom', tipmesg: '小时:审单时间-(付款时间大于下午4点则为付款时间加32小时否则判断是不是在上午8点前是的话则为付款时间加8小时否则获取真实付款时间)' },
    { istrue: true, prop: 'printSpanTimeHH', label: '打单时长', width: '100', sortable: 'custom', tipmesg: '小时:打单时间-审单时间' },
    { istrue: true, prop: 'batchWaitSpanTimeHH', label: '波次等待时长', width: '120', sortable: 'custom', tipmesg: '小时:领批次时间-打单时间(审单时间)' },//波次等待时长
    { istrue: true, prop: 'batchSpanTimeHH', label: '波次时长', width: '100', sortable: 'custom', tipmesg: '小时:批次结束时间-领取拣货任务' },//波次时长  ,tipmesg:'分钟'
    { istrue: true, prop: 'pickSpanTimeHH', label: '配货时长', width: '100', sortable: 'custom', tipmesg: '小时:配货时间(发货时间)-打单时间' },
    { istrue: true, prop: 'deliverySpanTimeHH', label: '打包时长', width: '100', sortable: 'custom', tipmesg: '小时:发货时间-拣货时间' },
    { istrue: true, prop: 'confirmer', label: '审单人员', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'printer', label: '打单人员', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'picker', label: '配货人', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'batchPicker', label: '波次操作人', width: '80', sortable: 'custom' },//领波次人
    { istrue: true, prop: 'delivery', label: '发货人', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'pickCount', label: '拣货次数', width: '85', sortable: 'custom' },
    { istrue: true, prop: 'label', label: '订单标签', width: '240', sortable: 'custom' },
];

const tableCols2 = [
    { istrue: true, prop: 'bcBatchNumber', label: '批次号', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'generatTime', label: '生成日期', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'batchTime', label: '批次领取日期', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'endTime', label: '结束日期', width: '145', sortable: 'custom' },
    { istrue: true, prop: 'warehouse', label: '发货仓', width: '75', sortable: 'custom', formatter: (row) => row.warehouseStr },
    { istrue: true, prop: 'picker', label: '拣货员', width: '110', sortable: 'custom' },
    { istrue: true, prop: 'orderStatus', label: '状态', width: '110', sortable: 'custom', formatter: (row) => row.orderStatusStr },
    { istrue: true, prop: 'flag', label: '标签', width: '110', sortable: 'custom' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '110', sortable: 'custom' },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'pickedQty', label: '已拣数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'goodsQty', label: '数量', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'pickTimeHH', label: '拣货时长', width: '115', sortable: 'custom', tipmesg: '小时' }

];
export default {
    name: 'newOrderNodeIndex',
    components: {
        MyContainer, indexgrid, importtab, indexchat, openwebjushuitanjhpc, realtimeinventory,
        realtimeinventorycheck, realtimelackorder, realtimereplenisorder,wareaislelist
    },
    props: {

    },
    data() {
        return {
            activeName: "one"
        }
    },
    async mounted() {

    },
    beforeUpdate() { },
    methods: {
        async tabclick() {
            if (this.activeName == 'one') this.$refs.indexgrid0.ShowHideonSearch();
            if (this.activeName == 'two') this.$refs.indexgrid1.ShowHideonSearch();
            if (this.activeName == 'three') this.$refs.indexgrid2.ShowHideonSearch();
            if (this.activeName == 'four') this.$refs.indexgrid3.ShowHideonSearch();
            if (this.activeName == 'five') this.$refs.indexgrid4.ShowHideonSearch();
            if (this.activeName == 'six') this.$refs.indexgrid5.ShowHideonSearch();
        },
        getTableCols(tabtype) {
            if (tabtype == 0) {
                return tableCols;
            } else if (tabtype == 1 || tabtype == 2 || tabtype == 3 || tabtype == 4) {
                return tableCols1;
            } else if (tabtype == 5) {
                return tableCols2;
            }
        }
    }
}
</script>
