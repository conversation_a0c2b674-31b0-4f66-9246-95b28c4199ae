<template>
	<div>
		<iframe id="iframeContainer" scrolling='no' style="border:none" ref="iframe"  :width="searchTableWidth" :height="searchTableHeight"
			v-bind:src="reportUrl" @load="loaded"></iframe>

	</div>
</template>
<script>
  import usermessage from '@/store/modules/user'
	import Vue from 'vue'
  // import { ref } from 'vue'
  import Cookies from 'js-cookie'
  // const iframe = ref()
  import {
  	mapGetters
  } from 'vuex'
	export default {
		data() {
			return {
        onereq: true,
				reportUrl: '',
				searchTableHeight: 0,
				searchTableWidth: 0,
        iframeshow: true,
			}
		},
		mounted() {
      const _this = this;
			_this.initIframe()

			window.onresize = () => {
				_this.initIframe()
			}
      // FAT的传参
        // window.addEventListener('message',function(event){
        //     //此处执行事件
        //     // console.log('CHILD的传参')
        //     console.log(event.data)
        //     if(event.data.type==1){
        //       /////////////////////////
        //       // console.log("ref参数",_this);
        //       var iframe = _this.$refs.iframe.contentWindow
        //       iframe.postMessage(
        //         {
        //           typeone: 'erqq',
        //           isShowMenu: false,
        //         },
        //         '*'
        //       )
        //     }
        // })
      /////
		},
		created() {
      const nowrouter = this.$route.meta.pathh;
      const start = nowrouter.indexOf("http");
      const routerr = nowrouter.slice(start);
      this.reportUrl = routerr;
      this.initIframe();

		},

    computed: {
      watchrouter(){
        return this.$store.state.routerNow;
      },
      ...mapGetters([
      	'menus',
      	'userName',
      	'avatar'
      ]),
    },
		watch: {
      watchrouter(data){
        this.reportUrl = this.$store.state.routerNow;
      }
		},
    methods: {
    	initIframe() {
    		const iframeContainer = document.getElementById('iframeContainer')
    		const deviceWidth = document.body.clientWidth
    		const deviceHeight = document.body.clientHeight
    		iframeContainer.style.width = '100%'
    		iframeContainer.style.height = (Number(deviceHeight)) + 'px'
    	},
      loaded() {
            const username=usermessage.state.userName;
            const _this = this;
            const iframe = _this.$refs.iframe.contentWindow;
            // const token = Cookies.get('bbsToken');
            if(_this.onereq){
              iframe.postMessage(
                {
                  typeone: 'ytzg',
                  username,
                },
                '*'
              )
              _this.onereq = false;
            }
          }
    }
	}
</script>
