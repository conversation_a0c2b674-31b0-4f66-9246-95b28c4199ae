<template>
    <MyContainer style="position: relative;">
        <template #header>
            <div class="top" @click="getFocus">
                <el-select v-model="query.wmsId" placeholder="发货仓" class="publicCss" clearable filterable
                    @change="changeWareHouse">
                    <el-option v-for="item in wareHouseList" :key="item.wms_co_id" :label="item.name"
                        :value="item.wms_co_id" />
                </el-select>
                <el-input v-model="query.expressNo" placeholder="物流单号" maxlength="200" clearable class="publicCss"
                    ref="input" @keyup.enter.native="getProps" />
                <el-button type="primary" @click="getProps" style="margin-right: 10px;">搜索</el-button>
                <div>仓位:{{ lastData.splitPin }}</div>
            </div>
        </template>
        <div class="main" @click="getFocus">
            <div v-for="(item, i) in 3" :class="['main_item', index == i ? 'active' : '']">
                <div class="main_item_top">
                    <div class="item_fixed_top"><span>{{ i == 0 ? '拆' : i == 1 ? '丢' : '再' }}</span></div>
                    <vxetablebase :id="'operateUser202408041827_1'" v-if="i == 0" ref="table1" :that='that'
                        :isIndex='true' :hasexpand='true' :indexWidth="40" :tablefixed='true' :tableData='passageOne'
                        :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
                        style="width: 100%;  margin: 0" :height="'100%'" v-loading="pageLoading" class="already" />
                    <vxetablebase :id="'operateUser202408041827_2'" v-if="i == 1" ref="table2" :that='that'
                        :isIndex='true' :hasexpand='true' :indexWidth="40" :tablefixed='true' :tableData='passageTwo'
                        :tableCols='tableCols2' :isSelection="false" :isSelectColumn="false"
                        style="width: 100%;  margin: 0" :height="'100%'" v-loading="pageLoading" class="already" />
                    <vxetablebase :id="'operateUser202408041827_3'" v-if="i == 2" ref="table3" :that='that'
                        :isIndex='true' :hasexpand='true' :indexWidth="40" :tablefixed='true' :tableData='passageThree'
                        :tableCols='tableCols3' :isSelection="false" :isSelectColumn="false"
                        style="width: 100%;  margin: 0" :height="'100%'" v-loading="pageLoading" class="already" />
                </div>
                <div class="main_item_bottom" v-if="i == 0">共{{ passageOne.length }}件</div>
                <div class="main_item_bottom" v-if="i == 1">共{{ passageTwo.length }}件</div>
                <div class="main_item_bottom" v-if="i == 2">共{{ passageThree.length }}件</div>
            </div>
        </div>
        <audio src="../../../static/audio/拆包.mp3" ref="one"></audio>
        <audio src="../../../static/audio/丢弃.mp3" ref="two"></audio>
        <audio src="../../../static/audio/再扭转.mp3" ref="three"></audio>
        <audio src="../../../static/audio/已扫码.mp3" ref="five"></audio>
        <audio src="../../../static/audio/扫码失败.mp3" ref="six"></audio>
        <audio src="../../../static/audio/请拆包检查包裹.mp3" ref="seven"></audio>

        <div id='print' v-if="lastData.splitPins?.length > 0">
            <div v-for="pin in lastData.splitPins" :key="pin.pin" class="print_item">
                <div>{{ pin.goodsName }}</div>
                <div>
                    <img :id="'barcode1' + pin.goodsCode" />
                    <div>{{ pin.pin }}</div>
                </div>
            </div>
        </div>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { returnOrderScan, print, reject } from "@/api/vo/ReturnOrderScan";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from "dayjs";
import { pageGetTbWarehouseAsync } from "@/api/inventory/prepack.js"
const api = '/api/verifyOrder/Split/'
import request from '@/utils/request'
import { printJS } from '@/utils/print'
import JsBarcode from 'jsbarcode'
import {
    //分页查询店铺商品资料
    getListForScan,
    //导入
    importData,
} from "@/api/inventory/basicgoods"
const tableCols = [
    { align: 'left', label: '快递单号', prop: 'expressNo', width: 'auto', },
    { align: 'center', label: '扫码时间', prop: 'scanTime', width: 'auto', formatter: (row) => row.scanTime ? dayjs(row.scanTime).format('HH:mm:ss') : '' },
    { align: 'right', label: '仓位', prop: 'splitPin', width: 'auto' },
]
const tableCols2 = [
    { align: 'left', label: '快递单号', prop: 'expressNo', width: 'auto', },
    { align: 'right', label: '扫码时间', prop: 'scanTime', width: 'auto', formatter: (row) => row.scanTime ? dayjs(row.scanTime).format('HH:mm:ss') : '' },
]
const tableCols3 = [
    { align: 'left', label: '快递单号', prop: 'expressNo', width: 'auto', },
    { align: 'right', label: '扫码时间', prop: 'scanTime', width: 'auto', formatter: (row) => row.scanTime ? dayjs(row.scanTime).format('HH:mm:ss') : '' },
]
export default {
    name: "operateUser",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            api,
            pageLoading: false,
            passageOne: [], //通道一
            passageTwo: [], //通道二
            passageThree: [], //通道三
            passageFour: [], //通道四
            tableCols,
            tableCols2,
            tableCols3,
            that: this,
            index: null,
            lastExpressNo: null,
            bgTableData: [],
            query: {
                isSecondScan: false,//是否是二次扫码
                expressNo: null,//快递单号
                wmsId: null,//仓库id
                wmsName: null,//仓库名称
                isGoodsCode: false,//是否是商品编码
                orderNoInner: null//订单号
            },
            wareHouseList: [],
            orderNoInner: null,
            dialogHisVisible: false,
            isSuccess: 0,
            filtration: ['R02T', 'r02t'],
            lastData: {}
        }
    },
    async mounted() {
        await this.getWareHouse()
        this.getFocus()
    },
    methods: {
        //查询破损包裹信息
        async getBrekenProps(skus) {
            this.bgTableData = []
            //先按,切割,再按*切割,获取到商品编码和数量
            const arr = skus.split(',')
            const res = arr.map(item => {
                const [code, num] = item.split('*')
                return {
                    goodsCode: code,
                    num: num
                }
            })
            //根据商品编码获取商品信息
            const params = {
                currentPage: 1,
                pageSize: 1000,
                orderBy: null,
                isAsc: false,
                goodsCode: res.map(item => item['goodsCode']).join(',')
            }
            const { data: { list }, success } = await getListForScan(params)
            if (success) {
                this.bgTableData = list.map((item, index) => {
                    return {
                        ...item,
                        ...res[index]
                    }
                })
            } else {
                this.$message.error('暂未查询到相关商品信息')
            }
        },
        async unErrorOperte() {
            const { success } = await print({ orderNoInner: this.orderNoInner, IsSecondScan: this.query.isSecondScan })
            if (success) {
                this.dialogHisVisible = false
                this.$message.success('打印成功')
            } else {
                this.dialogHisVisible = false
                this.$message.error('打印失败')
            }
        },
        async errorOperte() {
            const { success } = await reject({ orderNoInner: this.orderNoInner })
            if (success) {
                this.dialogHisVisible = false
                this.$message.success('操作成功')
            } else {
                this.dialogHisVisible = false
                this.$message.error('操作失败')
            }
        },
        changeWareHouse(e) {
            this.query.wmsName = e ? this.wareHouseList.find(item => item.wms_co_id == e).name : null
        },
        async getWareHouse() {
            const params = {
                currentPage: 1,
                pageSize: 1000,
                orderBy: null,
                isAsc: false,
            }
            const { data: { list } } = await pageGetTbWarehouseAsync(params)
            this.wareHouseList = list
        },
        changeType() {
            this.expressNo = null
            this.lastExpressNo = null
            this.passageOne = []
            this.passageTwo = []
            this.passageThree = []
            this.passageFour = []
        },
        async getProps() {
            if (!this.query.expressNo) return this.$message.error('物流单号为空')
            //过滤掉圆通的特殊字符
            this.filtration.forEach(item => {
                if (this.query.expressNo.includes(item)) {
                    this.query.expressNo = this.query.expressNo.replace(item, '')
                }
            })
            //物流单号长度小于7或者大于18,就提示
            if (this.query.expressNo.length < 7 || this.query.expressNo.length > 18) {
                this.$refs.six.play()
                console.log('扫码失败');
                return
            }
            if (!this.query.wmsId) return this.$message.error('仓库为空')
            this.query.expressNo = this.query.expressNo.replace(/\s*/g, '')
            //如果有特殊符号就报错或者与上次一样就报错
            if ((this.query.expressNo.match(/[^0-9a-zA-Z]/) && !this.query.isGoodsCode)) {
                this.$refs.six.play()
                return
            }
            var regex = /[^a-zA-Z0-9-]/
            if (regex.test(this.query.expressNo) && this.query.isGoodsCode) {
                this.$refs.six.play()
                return
            }
            this.lastExpressNo = this.query.expressNo
            const expressNo = this.query.expressNo
            if (!this.query.isSecondScan) {
                this.query.expressNo = null
            }
            const params = {
                expressNo,
                wmsId: this.query.wmsId,
                wmsName: this.query.wmsName
            }
            this.pageLoading = true
            const { data, success } = await request.post(api + 'SplitScan', params)
            if (success) {
                this.pageLoading = false
                const map = {
                    1: {
                        data: this.passageOne,
                        fn: () => this.$refs.one.play()
                    },
                    2: {
                        data: this.passageTwo,
                        fn: () => this.$refs.two.play()
                    },
                    3: {
                        data: this.passageThree,
                        fn: () => this.$refs.three.play()
                    },
                }
                this.index = data.status - 1
                map[data.status].data.unshift(data)
                this.lastData = data
                this.query.expressNo = null
                map[data.status].fn()
                this.getFocus()
            } else {
                this.pageLoading = false
                this.$refs.six.play()
            }

            console.log(this.lastData)

            if (this.lastData?.splitPins?.length > 0) {
                this.lastData.splitPins.forEach((item, index) => {

                    setTimeout(() => {
                        JsBarcode(`#barcode1${item.goodsCode}`, item.goodsCode, {
                            displayValue: false
                        })


                    }, 0);
                })

                const params = {
                    id: 'print',
                    type: 'html',
                }

                this.$nextTick(() => {
                    document.getElementById('print').style.display = 'flex'
                    setTimeout(() => {
                        printJS(params)
                    }, 200);
                })
            }
        },
        getFocus() {
            this.$refs.input.focus()
        }
    }
}
</script>

<style scoped lang="scss">
.btngroup {
    margin-top: 10px;
    display: flex;
    justify-content: center;
}

.top {
    display: flex;
    margin-bottom: 10px;
    align-items: center;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.main {
    height: 85vh;
    width: 100%;
    display: flex;
    padding: 5px;
    overflow: auto;
    box-sizing: border-box;
    flex-wrap: wrap;
    justify-content: space-between;
    //边框线合并
    border-collapse: collapse;

    .main_item {
        width: 33%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        box-sizing: border-box;
        border: 1px solid #ccc;

        .main_item_top {
            flex: 1;
            display: flex;
            font-size: 16px;
            box-sizing: border-box;
            position: relative;
            flex-direction: column;
            overflow-y: hidden;

            .item_fixed_top {
                position: absolute;
                top: 5px;
                left: 0px;
                width: 100%;
                // height: 30px;
                z-index: 1000;
                font-weight: 700;
                color: #fff;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;
                border-top-left-radius: 0px;

                span {
                    font-size: 16px;
                    text-align: center;
                    width: 30px;
                    height: 30px;
                    background-color: #409EFF;
                    border-radius: 50%;
                    line-height: 30px;
                    margin: auto;
                }
            }
        }

        .main_item_bottom {
            height: 40px;
            width: 100%;
            display: flex;
            justify-content: end;
            font-size: 14px;
            padding: 10px;
            box-sizing: border-box;
        }
    }
}

.active {
    //红色盒子阴影
    outline: #409EFF solid 2px;
}

.already ::v-deep .vxe-tools--operate {
    display: none !important;
}

::v-deep .vxe-table--render-default .vxe-cell {
    padding-left: 10px !important;
    padding-right: 10px !important;
}

.success {
    width: 200px;
    height: 35px;
    opacity: 1;
    background-color: rgb(240, 249, 235);
    color: rgb(24, 151, 33);
    text-align: center;
    line-height: 35px;
    font-size: 16px;
    border-radius: 3px;
    position: fixed;
    top: 100px;
    right: 17px;
    transition: all 0.5s;
}

.error {
    width: 200px;
    height: 35px;
    opacity: 1;
    background-color: #fef0f0;
    color: #f56c6c;
    text-align: center;
    line-height: 35px;
    font-size: 16px;
    border-radius: 3px;
    position: fixed;
    top: 100px;
    right: 17px;
    transition: all 0.5s;
}

#print {
    // line-height: 30px;
    // padding: 20px;
    // box-sizing: border-box;
    text-align: center;
    width: 200px;
    height: 150px;
    display: flex;
    flex-wrap: wrap;
    page-break-inside: avoid;
    justify-content: center;
    align-items: center;

    // scale: 2;
    //防止跨页
    .print_item {
        page-break-inside: avoid;
    }

    img {
        width: 80%;
        height: 20px;
    }
}
</style>