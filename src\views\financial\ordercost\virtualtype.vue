<template>
  <container>
       <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='false' tablekey="virtualtype"
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' :isSelectColumn="true"
              :loading="listLoading">
         <template slot='extentbtn'>
            <el-button-group>
              <el-button style="padding: 0;margin: 0;">
                <el-select v-model="filter.specialType" placeholder="特殊类别" style="width: 200px" :clearable="true"  @change="onSearch">
                 <el-option v-for="item in specialTypeList" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>
              </el-button>             
              <el-button style="padding: 0;margin: 0;"><el-input style="width: 200px" v-model="filter.virtualType"  placeholder="虚拟分类" @change="onSearch"/></el-button>
            </el-button-group>
         </template>
       </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

    <el-drawer
                      :title="formtitle"
                      :modal="false"
                      :wrapper-closable="true"
                      :modal-append-to-body="false"
                      :visible.sync="addFormVisible"
                      direction="btt"
                      size="'auto'"
                      class="el-drawer__wrapper"
                      style="position:absolute;"
                    >
                    <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" style="margin-top:10px;"/>
                      <div class="drawer-footer">
                        <el-button @click.native="addFormVisible = false">取消</el-button>
                        <my-confirm-button type="submit"  :loading="addLoading" @click="onAddSubmit" />
                      </div>
    </el-drawer> 
  </container>
</template>
<script>
import {
  pageVirtualType,
  addVirtualType,
  updateVirtualType,
  deleteVirtualType,
} from '@/api/financial/ordercost'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import {formatWarehouse,formatTime,formatYesornoBool} from "@/utils/tools";
import MyConfirmButton from '@/components/my-confirm-button'
const tableCols =[
      
      {istrue:true,prop:'specialType',label:'特殊类别', width:'230',sortable:'custom',},
      {istrue:true,prop:'virtualType',label:'虚拟分类', width:'230',sortable:'custom',},
      {istrue:true,prop:'createdUserName',label:'创建人', width:'120'},
      {istrue:true,prop:'createdTime',label:'创建时间', width:'160',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD HH:mm:ss')},
      {istrue:true,prop:'modifiedUserName',label:'修改人', width:'120'},
      {istrue:true,prop:'modifiedTime',label:'修改时间', width:'160',sortable:'custom',formatter:(row)=>formatTime(row.modifiedTime,'YYYY-MM-DD HH:mm:ss')},
      {istrue:true,type:'button', label:'操作',width:'90',btnList:[
          {label:"编辑",handle:(that,row)=>that.onEditRow(row)},
          {label:"删除",handle:(that,row)=>that.onDeleteRow(row)}
        ]
      },
     ];
const tableHandles=[
        {label:"新增", handle:(that)=>that.onAddRow()},
        {label:"刷新", handle:(that)=>that.getlist()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, container,MyConfirmButton },
   props:{
       filter: { }
     },
  data() {
    return {
      shareFeeType:0,
      that:this,
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"id",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [], 
      listLoading: false,
      pageLoading: false,
      //自动表单 - Start
      addFormVisible: false,
      addLoading: false,     
      deleteLoading: false,
      autoform:{
               fApi:{},
               rule:[],
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
      },
      addFormVisible: false,
      addLoading: false,     
      formtitle:"",
      isAdd:true,
      //自动表单 -End
      specialTypeList:[
        {label:"护墙角",value:"护墙角"},
        {label:"节能罩",value:"节能罩"},
        {label:"纸管",value:"纸管"},
        {label:"鞋拔子",value:"鞋拔子"},
      ],
    }
  },
  mounted() {
     this.onSearch()
  },
  beforeUpdate() { },
  methods: {
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      this.filter.shareFeeType=this.shareFeeType;
      const params = {...pager, ...this.pager, ... this.filter}
      this.listLoading = true
      const res = await pageVirtualType(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async onbatchDelete() {
       await this.$emit('ondeleteByBatch',this.shareFeeType);
    },
   async oncomput(){
      this.$emit('onstartcomput',this.shareFeeType);
   },
   async onimport(){
     await this.$emit('onstartImport',this.shareFeeType);
   },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
    //==自动表单Start===========================
    //加载特殊类别
    async setSpecialType(){
      let rule= {validate: [{type: 'string', required: true, message:'请选择'}],options: [{value:null, label:'请选择'}]}
      rule.options=this.specialTypeList;
      return rule;
    },
    async onAddRow() {
      this.formtitle='新增';
      this.isAdd=true;
      this.addFormVisible = true;
      var that=this;
      this.autoform.rule=[
                {type:'hidden',field:'id',title:'id',value: ''},
                      {type:'select',field:'specialType',title:'特殊类别', validate: [{type: 'string', required: true, message:'请选择特殊类别'}],value: "",
                        options: [],...await this.setSpecialType(),
                      },
                      {type:'input',field:'virtualType',title:'虚拟分类', validate: [{type: 'string', required: true, message:'虚拟分类必填'}],value: "",
                        options: []
                      },
      ];

      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields();       
    },
    async onAddSubmit() {
      this.addLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          formData.id=formData.id?formData.id:0;
          formData.Enabled=true;
          if(this.isAdd){
            const res = await addVirtualType(formData);
            if(res.code==1){
              await this.getlist();
              this.addFormVisible=false;
              this.$message({
                message: '新增成功',
                type: 'success'
              });
            }
          }
          else{
            const res = await updateVirtualType(formData);
            if(res.code==1){
              await this.getlist();
              this.addFormVisible=false;
              this.$message({
                message: '修改成功',
                type: 'success'
              });
            }
          }
        }else{
          //todo 表单验证未通过
        }
     })
     this.addLoading=false;
    },
    async onEditRow(row) {
      this.onAddRow();
      this.formtitle='修改';
      this.isAdd=false;
         
      setTimeout(async () => {
        await this.autoform.fApi.setValue({id:row.id});
        await this.autoform.fApi.setValue({virtualType:row.virtualType});
        await this.autoform.fApi.setValue({specialType:row.specialType});
      }, 100);
      
    },
    async onDeleteRow(row){
       var params ={id:row.id||0};
       this.$confirm('确认删除?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
        }).then(async () => {
            const res = await deleteVirtualType(params);
            if(res.code==1){
                await this.getlist();
                this.$message({
                      message: '修改成功',
                      type: 'success'
                });
            }
        }).catch(() => {});      
    },
    //==自动表单End  ===========================
  }
}
</script>
