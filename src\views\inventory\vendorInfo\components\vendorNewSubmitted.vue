<template>
    <MyContainer>
        <template #header>
            <div class="header">
                <el-input placeholder="供应商名称" v-model="ListInfo.providerName" maxlength="50" class="publicMargin"
                    style="width: 220px;" clearable></el-input>
                <el-select v-model="ListInfo.dockingStatus" placeholder="对接状态" style="width: 220px" class="publicMargin"
                    clearable>
                    <el-option v-for="item in status" :key="item.label" :label="item.label" :value="item.label" />
                </el-select>
                <el-select v-model="ListInfo.IsExitProvider" placeholder="重复供应商" style="width: 220px" class="publicMargin"
                    clearable>
                    <el-option label="未重复" :value="2" />
                    <el-option label="重复" :value="1" />
                </el-select>
                <el-button type="primary" @click="searchList">查询</el-button>
                <el-button type="primary" @click="ClickAssignment">一键分配</el-button>
            </div>
        </template>
        <vxetablebase ref="newTable" :tableData="newTableData" :tableCols="tableCols2" :is-index="true" :that="that"
            style="width: 100%; height: 650px; margin: 0" @sortchange='sortchange' class="already" :loading="listLoading">
        </vxetablebase>
        <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="detailTotal"
            @page-change="detailPagechange" @size-change="detailSizechange" />

        <!-- 弹层部分 -->
        <el-dialog title="对接记录" :visible.sync="RecordsVisible" width="60%" :before-close="handleClose1" v-dialogDrag>
            <cesTable :id="'vendorNewSubmitted202408041618'" ref="detailTable" :tableData="doTableData" :tableCols="tableCols4" :is-index="true" :that="that"
                :showsummary="true" style="width: 100%; height: 500px" @sortchange='sortchange2' class="detail">
            </cesTable>
            <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="recordsTotal"
                @page-change="dockingRecordsPagechange" @size-change="dockingRecordsSizechange" style="margin-top: 40px;" />
        </el-dialog>

        <el-dialog title="操作" :visible.sync="operateVisible" width="50%" :before-close="handleClose" v-dialogDrag>
            <el-select v-model="RecordsInfo.dockingStatus" placeholder="对接状态" style="width: 220px;margin-bottom: 10px;">
                <el-option v-for="item in status" :key="item.label" :label="item.label" :value="item.label" />
            </el-select>
            <el-input type="textarea" placeholder="请输入内容" v-model="RecordsInfo.result" maxlength="300" show-word-limit
                rows="5" />
            <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="9"
                :on-success="handleSuccess" :file-list="picFileList" multiple :show-file-list="false"
                accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                <el-button class="addsc" type="text">上传图片</el-button>
            </el-upload>
            <div v-if="RecordsInfo.picLists.length > 0">
                <div class="imageList_box">
                    <div class="imageList" v-for="item in RecordsInfo.picLists">
                        <el-image class="imgcss" style="width: 100px; height: 100px" :src="item"
                            :preview-src-list="RecordsInfo.picLists">
                        </el-image>
                        <span class="del" @click="delImg(item, i)">x</span>
                    </div>
                </div>

            </div>
            <div style="text-align: right;margin-top: 20px;">
                <el-button type="primary" @click="operateVisible = false">取消</el-button>
                <el-button type="primary" @click="operateSubmit">提交</el-button>
            </div>
        </el-dialog>

        <el-dialog title="采购记录" :visible.sync="nameVisible" width="40%" :before-close="handleClose" v-dialogDrag>
            <vxetablebase ref="detailTable" :tableData="nameTableData" :tableCols="tableCols5" :is-index="true" :that="that"
                :showsummary="true" :summaryarry="nameSummary" style="width: 100%; height: 500px" @sortchange='sortchange3'>
            </vxetablebase>
            <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="nameTotal"
                @page-change="namePagechange" @size-change="nameSizechange" style="margin-top: 40px;" />
        </el-dialog>

        <el-dialog title="一键分配" :visible.sync="assignmentVisible" width="20%" :before-close="handleClose" v-dialogDrag>
            <div class="assignmentBox">
                <el-select v-model="assignmentInfo.brandId" placeholder="请选择" style="width: 220px;"
                    @change="changeAssignment" clearable filterable>
                    <el-option v-for="item in procurementList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <div class="btnBox">
                    <el-button @click="assignmentVisible = false" style="margin-right: 20px;">取消</el-button>
                    <el-button type="primary" @click="assignmentSubmit">确定</el-button>
                </div>
            </div>
        </el-dialog>

        <el-dialog title="预览" :visible.sync="previewVisible" width="20%" :before-close="handleClose" v-dialogDrag>
            <p style="color: red;">此预览仅支持图片文件,其他类型文件请下载查看!</p>
            <div class="previewBox">
                <div v-for="(item, i) in previewSrcList">
                    <el-image v-if="imgType.some(item1 => item.includes(item1))" style="width: 100px; height: 100px"
                        :src="item" :preview-src-list="srcList" />
                    <div class="fileCss" v-else>{{ `附件${i + 1} ${item.substring(item.lastIndexOf('.'))}` }}</div>
                </div>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
import {
    getProviderQuotationRecordPageList,
    getProviderQuotationCGRecordPageList,
    getProviderNewGoodsPageList,
    getProviderQuotationHisRecordPageList,
    GetProviderDockingResultList,
    addProviderDockingResult,
    setProviderDockingUser,
    setProviderNewGoodsDockingUser,
    addProviderNewGoodsDockingResult,
    getProviderNewGoodsDockingResultList,
    getBrandUsers
}
    from '@/api/openPlatform/ProviderQuotation'
import { pagePurchaseOrderByProviderNameAsync } from '@/api/inventory/purchase'
import { getAllProBrand } from '@/api/inventory/warehouse'
const options = [
    {
        value: '1',
        label: '是'
    },
    {
        value: '0',
        label: '否'
    }
]

const positionType = [
    {
        label: '老板'
    },
    {
        label: '业务员'
    },
    {
        label: '经理'
    }
]

const sourceType = [
    {
        label: '朋友圈'
    },
    {
        label: '聊天'
    },
    {
        label: '其他'
    }
]

//详情
const tableCols1 = [
    { istrue: true, prop: 'openId', label: 'openId', sortable: 'custom', width: 85 },
    {
        istrue: true, prop: 'setStylePic', label: '系列编码图片', type: 'treeimages', isImgtree: true, width: 100, formatter: (row) => {
            if (row.setStylePic) {
                return row.setStylePic
            } else {
                return row.stylePic
            }
        }
    },
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: 100 },
    { istrue: true, prop: 'brandName', label: '采购', sortable: 'custom', width: 100 },
    { istrue: true, prop: 'sourceName', label: '推荐人', sortable: 'custom', width: 100 },
    { istrue: true, prop: 'sourceType', label: '来源', sortable: 'custom', width: 100 },
    { istrue: true, prop: 'providerName', label: '供应商名称', sortable: 'custom', width: 130, type: 'treeStar' },
    { istrue: true, prop: 'phone', label: '联系电话', sortable: 'custom', width: 100 },
    { istrue: true, prop: 'isWX', label: '微信是否同号', sortable: 'custom', width: 120, formatter: (row) => row.isWX == 1 ? '是' : '否' },
    { istrue: true, prop: 'position', label: '职位', sortable: 'custom', width: 100 },
    { istrue: true, prop: 'isBY', label: '是否包邮', width: 100, sortable: 'custom', formatter: (row) => row.isBY == 1 ? '是' : '否' },
    { istrue: true, prop: 'isContaisTax', label: '是否含税', width: 100, sortable: 'custom', formatter: (row) => row.isContaisTax == 1 ? '是' : '否' },
    { istrue: true, prop: 'sheng', label: '发货地址', width: 150, sortable: 'custom', formatter: (row) => row.sheng + row.shi + row.qu },
    // { istrue: true, prop: 'goodsCode', label: '商品编码', width: 85, sortable: 'custom' },
    { istrue: true, prop: 'quotation1', label: '进货量100报价', width: 150, sortable: 'custom' },
    { istrue: true, prop: 'quotation2', label: '进货量10000报价', width: 150, sortable: 'custom' },
    { istrue: true, prop: 'quotation3', label: '进货量100000报价', width: 170, sortable: 'custom' },
    { istrue: true, prop: 'remark', label: '备注', sortable: 'custom', width: 80, },
    { istrue: true, prop: 'modifiedTime', label: '最后填写日期', sortable: 'custom', width: 150 },
    {
        istrue: true, prop: 'address', label: '归属地', sortable: 'custom', width: 100, formatter: (row) => {
            if (row.address != null || row.ip != null) {
                return row.address + `(${row.ip})`
            } else {
                return ''
            }
        }
    },
]
//新品提交
const tableCols2 = [
    { istrue: true, label: '', type: "checkbox", width: 60, fixed: 'left' },
    { istrue: true, prop: 'picUrl', label: '新品图片', sortable: 'custom', width: 150, fixed: 'left', type: 'images' },
    { istrue: true, prop: 'sourceName', label: '推荐人', sortable: 'custom', width: 100, },
    { istrue: true, prop: 'sourceType', label: '来源', sortable: 'custom', width: 100, },
    { istrue: true, prop: 'providerName', width: 130, label: '供应商名称', sortable: 'custom', style: "color: rgb(72, 132, 243);cursor:pointer;", type: 'treeStar1', handle: (that, row) => that.openNameDialog(row) },
    { istrue: true, prop: 'isWX', label: '微信是否同号', sortable: 'custom', width: 120, formatter: (row) => row.isWX == 1 ? '是' : '否' },
    { istrue: true, prop: 'phone', label: '联系电话', sortable: 'custom', width: 100, },
    { istrue: true, prop: 'position', label: '职位', sortable: 'custom', width: 100, },
    { istrue: true, prop: 'createdTime', label: '提交时间', sortable: 'custom', width: 100, },
    {
        istrue: true, width: 100, prop: 'address', label: '归属地', sortable: 'custom', formatter: (row) => {
            if (row.address != null || row.ip != null) {
                return row.address + `(${row.ip})`
            } else {
                return ''
            }
        }
    },
    { istrue: true, prop: 'dayCapacity', label: '工厂产能', sortable: 'custom', width: 100, },
    { istrue: true, prop: 'stylecode', label: '新品名称', sortable: 'custom', width: 100, },
    { istrue: true, prop: 'quotation1', label: '进货量100报价', sortable: 'custom', width: 150, },
    { istrue: true, prop: 'quotation2', label: '进货量10000报价', sortable: 'custom', width: 170, },
    { istrue: true, prop: 'quotation3', label: '进货量100000报价', sortable: 'custom', width: 170, },
    { istrue: true, prop: 'dockingBrandName', label: '对接人', sortable: 'custom', width: 100, },
    { istrue: true, prop: 'dockingStatus', label: '对接状态', sortable: 'custom', width: 100, },
    { istrue: true, prop: 'openId', label: 'openId', sortable: 'custom', width: 150 },
    { istrue: true, prop: 'dockingCount', label: '对接次数', sortable: 'custom', width: 100, type: 'click', fixed: 'right', handle: (that, row) => that.dockingRecords(row) },
    { istrue: true, display: true, label: '附件', prop: 'zipFile', sortable: 'custom', style: "color: rgb(72, 132, 243);cursor:pointer;", fixed: 'right', width: 80, formatter: (row) => ((row.zipFile == "" || row.zipFile == null || row.zipFile == undefined) && (row.zipStatus == null || row.zipStatus == '打包中')) ? ' ' : '下载', type: 'click', handle: (that, row) => that.downLoad(row) },
    { istrue: true, display: true, label: '查看', style: "color: rgb(72, 132, 243);cursor:pointer;", fixed: 'right', width: 60, formatter: (row) => (row.files == "" || row.files == null || row.files == undefined) ? '' : '预览', type: 'click', handle: (that, row) => that.previewFiles(row) },
    { istrue: true, display: true, label: '操作', style: "color: rgb(72, 132, 243);cursor:pointer;", fixed: 'right', width: 60, formatter: (row) => row.dockingBrandName == null ? '' : '操作', type: 'click', handle: (that, row) => that.operate(row) },
]
//对接记录
const tableCols4 = [
    { istrue: true, prop: 'createdUserName', label: '对接人', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '对接时间', sortable: 'custom' },
    { istrue: true, prop: 'dockingStatus', label: '对接状态', sortable: 'custom' },
    { istrue: true, prop: 'result', label: '对接结果', sortable: 'custom' },
    { istrue: true, prop: 'picJsons', label: '图片', type: 'images' },
]
//采购记录
const tableCols5 = [
    { istrue: true, prop: 'purchaseDate', label: '采购时间', sortable: 'custom' },
    { istrue: true, prop: 'buyNo', label: '采购单号', sortable: 'custom' },
    { istrue: true, prop: 'totalAmont', label: '采购金额', sortable: 'custom' },
    { istrue: true, prop: 'count', label: '采购量', sortable: 'custom' },
]

const status = [
    {
        label: '待沟通',
        value: '待沟通'
    },
    {
        label: '沟通中',
        value: '沟通中'
    },
    {
        label: '寄样中',
        value: '寄样中'
    },
    {
        label: '采购中',
        value: '采购中'
    },
    {
        label: '采购完成',
        value: '采购完成'
    },
    {
        label: '不适合',
        value: '不适合'
    },
]
export default {
    components: { MyContainer, cesTable, vxetablebase },
    name: "vendorNewSubmitted",
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: 'createdTime',//排序字段
                isAsc: false,//是否升序
                styleCode: null,//系列编码
                categoryId: null,//品类id
                openId: null,//openId
                goodCode: null,//商品编码
                isBY: null,//是否包邮
                isContaisTax: null,//是否含税
                providerName: null,//供应商名称
                dockingStatus: null,//对接状态
                sourceType: null,//来源
                brandId: null,//采购人员id
                position: null,//职位
            },
            logDetail: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                openId: null,//openId
                styleCode: null,//系列编码
                orderBy: null,//排序字段
                isAsc: true,//是否升序
            },
            RecordsInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                recordId: null,//记录id
                newGoodsRecordId: null,//新品记录id
                isDockingCG: 0,//是否对接采购 0已提交 1未提交
                styleCode: null,//系列编码
                openId: null,//openId
                result: null,//对接结果
                dockingStatus: null,//对接状态
                pics: null,
                picLists: []//图片列表
            },//对接记录请求参数
            nameInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                supplier: null,//供应商名称
            },
            assignmentInfo: {
                recordIds: [],//选中的id
                brandId: null,//采购人员id
                brandName: null,//采购人员名字
                dockingStatus: '待沟通'
            },
            sourceType,//来源
            positionType,//职位
            tableCols1,//详情
            tableCols2,//新品提交
            tableCols4,//对接记录
            tableCols5,//点击供应商名字
            tableData: [],//已提交
            tableData1: [],//详情
            newTableData: [],//新品提交
            doTableData: [],//对接记录
            nameTableData: [],//点击供应商名字
            picFileList: [],//图片上传列表
            brandList: [],//采购列表
            previewSrcList: [],//预览列表
            srcList: [],//预览图片列表
            imgType: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.pdf', '.JPG', '.JPEG', '.PBG', '.GIF', '.BMP', '.PDF'],
            logTotal: 0,//详情总数
            detailTotal: 0,//已提交总数
            nameTotal: 0,//供应商报价详情总数
            listLoading: true,//加载中
            dialogVisible: false,//详情弹层
            RecordsVisible: false,//对接记录弹层
            operateVisible: false,//操作弹层
            nameVisible: false,//点击供应商名字弹层
            assignmentVisible: false,//一键分配弹层
            previewVisible: false,//预览弹窗
            recordsTotal: 0,//对接记录总数
            activeName: 'first',//tab切换
            options,//是否包邮,是否含税
            status,//状态
            isOuter: false,//判断是外面的对接记录还是弹窗的对接记录 false为外面的 true为弹窗的
            procurementList: [],//采购人员列表
            nameSummary:null,
        };
    },
    mounted() {
        this.getNewList()
        this.getBrandList()
    },
    methods: {
        //预览文件
        previewFiles(row) {
            this.previewSrcList = row.files.split(',')
            //如果是图片类型就存到数组
            this.srcList = this.previewSrcList.filter(item => this.imgType.some(item1 => item.includes(item1)))
            this.previewVisible = true
        },
        //获取采购列表
        async getBrandList() {
            const { data, success } = await getAllProBrand()
            if (success) {
                this.brandList = data.map(item => {
                    return {
                        label: item.value,
                        value: item.key
                    }
                })
            } else {
                this.$message.error('获取采购人员列表失败')
            }
        },
        delImg(item, i) {
            this.RecordsInfo.picLists.splice(i, 1)
        },
        //指派确定按钮
        async assignmentSubmit() {
            const { success } = await setProviderNewGoodsDockingUser(this.assignmentInfo)
            if (success) {
                //关闭弹窗
                this.assignmentVisible = false
                this.$message.success('指派成功')
                //拉取已提交列表
                this.getNewList()
            }
        },
        changeAssignment(e) {
            //根据e找出procurementList相匹配的value
            this.assignmentInfo.brandName = this.procurementList.filter(item => item.value == e)[0].label
        },
        async ClickAssignment() {
            //清空数据
            this.assignmentInfo.recordIds = []
            this.assignmentInfo.brandId = null
            this.assignmentInfo.brandName = null
            this.assignmentInfo.recordIds = this.$refs.newTable.$refs.xTable.getCheckboxRecords().map(item => item.id)
            if (this.assignmentInfo.recordIds.length == 0) {
                this.$message.warning('请选择要分配的数据')
                return
            }
            const { data, success } = await getBrandUsers({ userName: null })
            if (success) {
                this.procurementList = data.map(item => {
                    return {
                        label: item.value,
                        value: item.key
                    }
                })
            } else {
                this.$message.error('获取采购人员列表失败')
            }
            this.assignmentVisible = true
        },
        //图片上传成功回调
        async handleSuccess({ data }) {
            this.RecordsInfo.picLists.push(data.url)
        },
        //点击供应商名称打开弹层
        async openNameDialog(row) {
            this.nameInfo.supplier = row.providerName ? row.providerName : row.supplier
            const { data, success } = await pagePurchaseOrderByProviderNameAsync(this.nameInfo)
            if (success) {
                this.nameTableData = data.list
                this.nameTotal = data.total
                this.nameSummary = data.summary
                this.nameVisible = true
                this.nameInfo.orderBy = null
            } else {
                this.$message.error('获取供应商采购记录失败')
            }
        },
        //对接记录弹层每页数量改变
        nameSizechange(val) {
            this.nameInfo.currentPage = 1;
            this.nameInfo.pageSize = val;
            this.openNameDialog(this.nameInfo)
        },
        //对接记录弹层当前页改变
        namePagechange(val) {
            this.nameInfo.currentPage = val;
            this.openNameDialog(this.nameInfo)
        },
        async operateSubmit() {
            if (this.RecordsInfo.result == null || this.RecordsInfo.result == '') {
                this.$message.error('请输入内容')
                return
            }
            if (this.RecordsInfo.picLists.length == 0) {
                this.$message.error('请上传图片')
                return
            }
            if (this.RecordsInfo.dockingStatus == null || this.RecordsInfo.dockingStatus == '') {
                this.$message.error('请选择对接状态')
                return
            }
            this.RecordsInfo.recordId = null
            const { success } = await addProviderNewGoodsDockingResult(this.RecordsInfo)
            if (success) {
                this.$message.success('操作成功')
            } else {
                this.$message.error('操作失败')
            }
            this.getNewList()
            this.operateVisible = false
        },
        //操作
        async operate(row) {
            this.RecordsInfo.picLists = []
            this.picFileList = []//清空图片上传列表
            this.RecordsInfo.result = null//清空对接结果
            this.RecordsInfo.dockingStatus = null//清空对接状态
            this.RecordsInfo.newGoodsRecordId = row.id
            this.RecordsInfo.recordId = row.id
            this.RecordsInfo.openId = row.openId
            this.RecordsInfo.styleCode = row.styleCode
            this.operateVisible = true
        },
        //对接记录弹层每页数量改变
        dockingRecordsSizechange(val) {
            this.RecordsInfo.currentPage = 1;
            this.RecordsInfo.pageSize = val;
            this.dockingRecords(this.RecordsInfo)
        },
        //对接记录弹层当前页改变
        dockingRecordsPagechange(val) {
            this.RecordsInfo.currentPage = val;
            this.dockingRecords(this.RecordsInfo)
        },
        //已提交对接记录
        async dockingRecords(row) {
            this.RecordsInfo.picLists = []
            if (!this.RecordsInfo.recordId) {
                this.RecordsInfo.recordId = row.id
            }
            this.RecordsInfo.openId = row.openId
            this.RecordsInfo.styleCode = row.styleCode
            const { data, success } = await getProviderNewGoodsDockingResultList(this.RecordsInfo)
            if (success) {
                this.doTableData = data.list
                this.recordsTotal = data.total
                this.RecordsInfo.orderBy = null
                this.RecordsVisible = true
            } else {
                this.$message.error('获取对接记录失败')
            }
        },
        async downLoad(row) {
            console.log(row, 'row');
            let files = row.zipFile
            if (files) {
                // files = JSON.parse(row.files)
                //按照,来切割
                files = files.split(',')
            } else {
                this.$message.error('暂无附件')
                return
            }
            //取出file中的files,将他转化成数组
            if (files.length == 0) {
                this.$message.error('暂无附件')
                return
            }
            //循环下载row.files中的文件
            //判断文件类型有没有zip
            let zip = files.filter(item => item.includes('.zip'))
            //从files中删除zip文件
            files = files.filter(item => !item.includes('.zip'))
            if (zip.length > 0) {
                // window.open()
                zip.forEach(item => {
                    window.open(item, '_blank')
                })
            } else {
                for (let i = 0; i < files.length; i++) {
                    let xhr = new XMLHttpRequest();
                    xhr.open('GET', files[i], true);
                    xhr.responseType = 'arraybuffer'; // 返回类型blob
                    xhr.onload = function () {
                        if (xhr.readyState === 4 && xhr.status === 200) {
                            var blob = this.response;
                            // 转换一个blob链接
                            // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，
                            // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定
                            //根据每个文件的最后一个.来判断文件的类型
                            let type = files[i].split('.')[files[i].split('.').length - 1]
                            //统一将type转化成小写
                            type = type.toLowerCase()
                            //如果是png
                            let downLoadUrl;
                            downLoadUrl = window.URL.createObjectURL(new Blob([blob], { type: 'application/octet-stream' }));
                            // 01.创建一个a标签
                            var a = document.createElement('a');
                            // 02.给a标签的属性download设定名称
                            // a.download = files[i].fileName;
                            a.download = '附件' + i + '.' + type + '';
                            // 03.设置下载的文件名
                            a.href = downLoadUrl;
                            // 04.对a标签做一个隐藏处理
                            a.style.display = 'none';
                            // 05.向文档中添加a标签
                            document.body.appendChild(a);
                            // 06.启动点击事件
                            a.click();
                            // 07.下载完毕删除此标签
                            a.remove();
                        };
                    };
                    xhr.send();
                }
            }
        },
        handleClose() {
            this.dialogVisible = false
            this.RecordsVisible = false
            this.operateVisible = false
            this.nameVisible = false
            this.assignmentVisible = false
            this.previewVisible = false
        },
        handleClose1() {
            this.RecordsVisible = false
        },
        //打开弹层
        async openView(row) {
            this.logDetail.openId = row.openId
            this.logDetail.styleCode = row.styleCode
            const { data, success } = await getProviderQuotationHisRecordPageList(this.logDetail)
            if (success) {
                this.tableData1 = data.list
                this.logTotal = data.total
                this.dialogVisible = true
                this.logDetail.orderBy = null
            } else {
                this.$message.error('获取提交历史失败')
            }
        },
        //页面数量改变
        detailSizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getNewList();
        },
        //当前页改变
        detailPagechange(val) {
            this.ListInfo.currentPage = val;
            this.getNewList();
        },
        searchList() {
            //清除styleCode,goodCode,providerName的空格
            this.ListInfo.styleCode = this.ListInfo.styleCode ? this.ListInfo.styleCode.replace(/\s+/g, "") : null;
            this.ListInfo.goodCode = this.ListInfo.goodCode ? this.ListInfo.goodCode.replace(/\s+/g, "") : null;
            this.ListInfo.providerName = this.ListInfo.providerName ? this.ListInfo.providerName.replace(/\s+/g, "") : null;
            this.getNewList()
        },
        //获取供应商新品提交列表
        async getNewList() {
            const { data, success } = await getProviderNewGoodsPageList(this.ListInfo);
            if (success) {
                this.newTableData = data.list;
                this.detailTotal = data.total;
                this.listLoading = false;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getNewList()
            }
        },
        sortchange1({ order, prop }) {
            if (prop) {
                this.logDetail.orderBy = prop
                this.logDetail.isAsc = order.indexOf("descending") == -1 ? true : false
                this.openView(this.logDetail)
            }
        },
        sortchange2({ order, prop }) {
            if (prop) {
                this.RecordsInfo.orderBy = prop
                this.RecordsInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.dockingRecords(this.RecordsInfo)
            }
        },
        sortchange3({ order, prop }) {
            if (prop) {
                if (prop == 'count') {
                    this.nameInfo.orderBy = 'totalCount'
                } else {
                    this.nameInfo.orderBy = prop
                }
                this.nameInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.openNameDialog(this.nameInfo)
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.header {
    display: flex;
    margin-bottom: 10px;
}

.publicMargin {
    margin-right: 20px;
}

::v-deep .vxetoolbar20221212 {
    display: none !important;
}

::v-deep .el-badge__content {
    padding: 0 4px;
    top: 7px;
    right: 0;
}

.assignmentBox {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .btnBox {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
    }


}

.imageList_box {
    display: flex;
    flex-wrap: wrap;

    .imageList {
        position: relative;
        width: 100px;
        height: 100px;

        .imgcss ::v-deep img {
            min-width: 100px !important;
            min-height: 100px !important;
            width: 100px !important;
            height: 100px !important;
        }


        .del {
            position: absolute;
            top: 0;
            right: 0;
            font-size: 16px;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            color: black;
            text-align: center;
            line-height: 15px;
            cursor: pointer;
        }
    }
}

.already ::v-deep .vxe-tools--operate {
    display: block !important;
    position: absolute;
    top: -25px !important;
    left: -36px !important;
    padding-top: 0;
    padding-bottom: 0;
    z-index: 999;
    background-color: rgb(255 255 255 / 0%);
}

.doesn ::v-deep .vxe-tools--operate {
    display: block !important;
    position: absolute;
    top: -25px !important;
    left: -36px !important;
    padding-top: 0;
    padding-bottom: 0;
    z-index: 999;
    background-color: rgb(255 255 255 / 0%);
}

.fileCss {
    width: 100px;
    height: 100px;
    background-color: #ccc;
    text-align: center;
    line-height: 100px;
    color: #000;
}

.previewBox {
    display: flex;
    flex-wrap: wrap;
}
</style>
