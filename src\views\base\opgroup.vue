<template>
    <el-container style="height:100%;">
        <container v-loading="pageLoading" style="width:50%;">
            <div style="margin-top:0px;height:100%;">
            <el-select filterable v-model="ListInfo.architecture" clearable multiple collapse-tags placeholder="架构" style="width: 150px">
              <el-option v-for="item in buildList" :key="item.value" :label="item.label" :value="item.value" />
              <el-option label="未设置" value="5"></el-option>
            </el-select>
            <el-button type="primary" @click="getlist">查询</el-button>
            <div style="height: 96%;">
            <ces-table ref="groupTable" :treeprops="{ children: 'children', hasChildren: true }" :defaultexpandall='true'
                :hasexpand='true' rowkey="key" :that='that' :isIndex='true' :tableData='list' :tableCols='tableCols'
                :isSelection="false" @select="selectchange" :tableHandles='tableHandles' @cellclick="cellclick"
                :loading="listLoading" :indexWidth="50">
            </ces-table>
            </div>
            </div>
        </container>
        <container style="width:50%;">
            <div style="margin-top:0px;height:100%;">
                <el-input v-model="zyFilter.userName" placeholder="组员" style="width:160px;" clearable maxlength="10" />
                <el-select filterable v-model="zyFilter.company" collapse-tags clearable placeholder="分公司"
                    style="width: 100px">
                    <el-option v-for="item in childcompany" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select filterable v-model="zyFilter.userType" collapse-tags clearable placeholder="人员类型"
                    style="width: 100px">
                    <el-option v-for="item in userTypeLables" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select filterable v-model="zyFilter.enabled" clearable placeholder="是否启用" style="width: 100px">
                  <el-option label="是" :value='true' ></el-option>
                  <el-option label="否" :value='false' ></el-option>
                </el-select>
                <el-button type="primary" @click="getDetailList">过滤</el-button>
                <div style="height: 96%;">
                    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :tableData='detail.list'
                        :tableCols='detail.tableCols' :tableHandles='tableHandles2' :isSelection="false"
                        :isSelectColumn="true" :loading="detail.listLoading">
                    </ces-table>
                </div>
            </div>
        </container>
        <!-- 组长信息编辑 -->
        <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
            :visible.sync="addFormVisible1" direction="btt" size="'auto'" class="el-drawer__wrapper"
            style="position:absolute;" width="100%">
            <form-create :rule="autoform.rules" v-model="autoform.fApi" :option="autoform.options" />
            <div class="drawer-footer">
                <el-button @click.native="addFormVisible1 = false">取消</el-button>
                <my-confirm-button type="submit" :loading="addLoading" @click="onAddSubmitGroup" />
            </div>
        </el-drawer>
        <!-- 组员信息编辑 -->
        <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
            :visible.sync="addFormVisible" direction="btt" size="'auto'" class="el-drawer__wrapper"
            style="position:absolute;">
            <form-create :rule="autoformZY.rule" v-model="autoformZY.fApi" :option="autoformZY.options" />
            <div class="drawer-footer">
                <el-button @click.native="addFormVisible = false">取消</el-button>
                <my-confirm-button type="submit" :loading="addLoading" @click="onAddSubmit" />
            </div>
        </el-drawer>
    </el-container>
</template>

<script>
import { GetDirectorGroupAllList, GetDirectorGroupAllListV2, GetDirectorAllList, addOrUpdateDirector, addoreditDirectorGroup, GetDirectorGroupById, GetDirectorById } from "@/api/operatemanage/base/shop"
import { getUserListPage, getUser } from "@/api/admin/user"
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'
import { formatTime } from '@/utils';

import { ruleDirectorGroup, ruleMultityDirectorGroup, projList } from '@/utils/formruletools'
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import checkPermission from '@/utils/permission'

const tableCols1 = [
    { istrue: true, prop: 'key', label: '分组ID', width: '100' },
    { istrue: true, prop: 'value', label: '组长', width: 'auto' },
    { istrue: true, prop: 'architectureName', label: '架构', width: '80' },
    { istrue: true, prop: 'projName', label: '项目', width: '80' },
    { istrue: true, prop: 'enabled', label: '是否启用', width: '80', formatter: (row) => { return (row.enabled == true ? "是" : "否") } },
    { istrue: true, type: 'button', width: '55', label: '操作', btnList: [{ label: "编辑", display: (row) => { return row.isHandle == true; }, handle: (that, row) => that.onEditDirectorGroup(row) }] },
];
const tableCols2 = [
    { istrue: true, prop: 'key', label: '序号', width: '60' },
    { istrue: true, prop: 'value', label: '组员', width: '70' },
    { istrue: true, prop: 'thirdId', label: '是否绑定登录用户', width: '150', formatter: (row) => { return (row.thirdId ? "是" : "否") } },
    { istrue: true, prop: 'company', label: '分公司', width: '80' },
    { istrue: true, prop: 'userType', label: '人员类型', width: '80' },
    { istrue: true, prop: 'enabled', label: '是否启用', width: '80', formatter: (row) => { return (row.enabled == true ? "是" : "否") } },
    {
        istrue: true, type: 'button', width: '55', label: '操作',
        btnList: [
            { label: "编辑", display: (row) => { return row.isHandle == true; }, handle: (that, row) => that.onEditDirector(row) }
        ]
    },
];
const tableHandles1 = [{ label: "新增组长", handle: (that) => that.onAddDirectorGroup() }];
const tableHandles2 = [{ label: "新增组员", handle: (that) => that.onAddDirector() }];
const userTypeLables = [{ label: '免费', value: '免费' }, { label: '付费', value: '付费' }, { label: '产品', value: '产品' }, { label: '品类', value: '品类' }, { label: '行政', value: '行政' }];
const childcompany = [{ label: '义乌', value: '义乌' }, { label: '南昌', value: '南昌' }, { label: '武汉', value: '武汉' }, { label: '西安', value: '西安' }, { label: '深圳', value: '深圳' }]
export default {
    name: 'roles',
    components: { cesTable, container, MyConfirmButton, },
    props: {
        isHistory: false,
    },
    data() {
        return {
            that: this,
            childcompany: childcompany,
            buildList: [{value:'2',label:'汪大侠'},{value:'4',label:'徐琛'},{value:'11',label:'左玉玲'},],
            userTypeLables: userTypeLables,
            filter: {
                combineCode: null,
                combineName: null,
                goodsCode: null
            },
            ListInfo: {
              architecture: [],
            },
            zyFilter: {
                selectId: 0,
                company: null,
                userName: null,
                userType: null,
                enabled: true,
                architectureArr: [],
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "combineCode", IsAsc: true },
            Director: {},
            tableCols: tableCols1,
            tableHandles: tableHandles1,
            tableHandles2: tableHandles2,
            dialogVisible: false,
            formtitle: "",
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            addFormVisible: false,
            addFormVisible1: false,
            addLoading: false,
            deleteLoading: false,
            formtitle: "新增",
            fileList: [],
            selids: [],//选择的id
            detail: {
                pager: { OrderBy: "goodsCode", IsAsc: true },
                list: [],
                tableCols: tableCols2,
                listLoading: false,
                total: 0,
                sels: [],
            },
            autoform: {
                fApi: {},
                options: {
                    submitBtn: false,
                    form: { labelWidth: '145px' },
                    global: {
                        '*': {
                            props: { disabled: false },
                            col: { span: 6 }
                        }
                    },
                    rules: [{ type: 'hidden', field: 'id', title: 'id', value: '' },
                    { type: 'input', field: 'userName', title: '名称', validate: [{ type: 'string', required: true, message: '请输入组员名称' }] },
                    {
                        type: 'select', field: 'userId', title: '登录用户', validate: [{ type: 'number', required: true, message: '请选择用户' }], value: 0, options: [],
                        props: { filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchUser(parm) }
                    },
                    {
                        type: 'select', field: 'pGroupId', title: '上级组长', value: null, validate: [{ type: 'string', required: false }]
                    },
                    { type: 'switch', field: 'enabled', title: '是否启用', value: true },
                    {
                        type: 'select', field: 'pGroupId', title: '上级组长', value: null, validate: [{ type: 'string', required: false }]
                    },
                    {
                        type: 'select', field: 'superviseId', title: '主管', value: null, validate: [{ type: 'string', required: true, message: '请输入主管'  }]
                    },
                    {
                        type: 'select', field: 'managerId', title: '经理', value: null, validate: [{ type: 'string', required: true, message: '请输入经理'  }]
                    },
                    ],
                },
            },
            autoformZY: {
                fApi: {},
                options: {
                    submitBtn: false, form: { labelWidth: '145px' },
                    global: { '*': { props: { disabled: false }, col: { span: 8 } } },
                    rule: [{ type: 'hidden', field: 'id', title: 'id', value: '' },
                    { type: 'input', field: 'userName', title: '名称1', validate: [{ type: 'string', required: true, message: '请输入组员名称' }] },
                    { type: 'select', field: 'groupId', title: '组长1', value: '', props: { filterable: true } },
                    {
                        type: 'select', field: 'userId', title: '登录用户', validate: [{ type: 'number', required: true, message: '请选择用户' }], value: 0, options: [],
                        props: { filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchUser(parm) }
                    },
                    { type: 'switch', field: 'enabled', title: '是否启用', value: true, }
                    ]
                },
            },
        }
    },
    async mounted() {
        await this.getlist();
        var res2 = await getDirectorGroupList();
        const b = res2.data?.map(item => { return { value: item.key, label: item.value }; });//运营组
        console.log(b,'b');

        const a = ['汪大侠','左玉玲','徐琛']
        //this.buildList = b.filter(item => a.includes(item.label)).map(item => ({ value: item.value, label: item.label }));
        console.log(this.btnList,'this.buildList');

        await this.onInitautoform();
    },
    methods: {
        checkPermission,
        onBuildChange(e){
          if(e == 1){
            const architectureArr = this.autoform.fApi.getValue('architectureArr');
            const architectureStr = architectureArr && architectureArr.length > 0 ? architectureArr.join(',') : null;
            this.autoform.fApi.setValue('architecture', architectureStr);
          } else if(e == 2){
            const architectureArr = this.autoformZY.fApi.getValue('architectureArr');
            const architectureStr = architectureArr && architectureArr.length > 0 ? architectureArr.join(',') : null;
            this.autoformZY.fApi.setValue('architecture', architectureStr);
          }
        },
        async selectSpecialRoleType() {
            let type = this.autoformZY.fApi.getValue('specialRoleType');
            if (type == 2) {
                this.autoformZY.fApi.hidden(false, 'specialGroupIds')
            } else {
                this.autoformZY.fApi.hidden(true, 'specialGroupIds')
            }
            this.autoformZY.fApi.setValue('specialGroupIds', [])
        },
        async onInitautoform() {
            let rule = [{ type: 'hidden', field: 'id', title: 'id', value: '' },
            { type: 'input', field: 'userName', title: '名称', validate: [{ type: 'string', required: true, message: '请输入组员名称' }] },
            { type: 'select', field: 'groupId', title: '组长', value: '', ...await ruleDirectorGroup(), props: { filterable: true } },
            {
                type: 'select', field: 'userId', title: '登录用户', validate: [{ type: 'number', required: true, message: '请搜索用户' }], value: null, options: [],
                props: { filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchZYUser(parm) },
                on: { change: () => { this.selectZYUser() } }
            },
            { type: 'input', field: 'nicName', title: '登录用户名', props: { readonly: true }, col: { span: 8 } },
            { type: 'switch', field: 'enabled', title: '是否启用', value: true, col: { span: 8 } },
            {
                type: 'select', field: 'company', title: '分公司', value: null,
                options: childcompany, col: { span: 8 }, validate: [{ required: true, message: '请选择分公司' }]
            }];

            // 根据权限条件添加运营数据特殊权限配置项
            if (this.checkPermission('OperationalSpecialPrivilege')) {
                rule.push({
                    type: "radio", field: "specialRoleType", title: "运营数据特殊权限", value: null,
                    options: [{ value: 0, label: "无", disabled: false }, { value: 1, label: "全部组", disabled: false }, { value: 2, label: "部分组", disabled: false }], col: { span: 8 },
                    on: { change: () => { this.selectSpecialRoleType() } }
                });
                rule.push({
                    type: "select", field: "specialGroupIds", size: "large", title: "运营数据特殊权限", value: null, ...await ruleMultityDirectorGroup(),
                    props: { multiple: true, clearable: true, filterable: true, 'collapse-tags': true }, col: { span: 8 }, validate: [{ required: false }]
                });
            }

            rule.push({
                type: 'select', field: 'userType', title: '人员类型', value: null,
                options: userTypeLables
            });
            // {
            //     type: 'select', field: 'architectureArr', title: '架构', value: null, props: { multiple: true, clearable: true, filterable: true, 'collapse-tags': true }, validate: [{ required: false }],
            //     options: this.buildList, on: { change: () => { this.onBuildChange(2) } }
            // },
            // { type: 'hidden', field: 'architecture', title: '架构字符串', value: null }

            let rules = [{ type: 'hidden', field: 'id', title: 'id', value: '' },
            { type: 'input', field: 'userName', title: '名称', validate: [{ type: 'string', required: true, message: '请输入组员名称' }] },
            {
                type: 'select', field: 'userId', title: '登录用户', validate: [{ type: 'number', required: true, message: '请搜索用户' }], value: null, options: [],
                props: { filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchUser(parm) },
                on: { change: () => { this.selectUser() } }
            },
            { type: 'input', field: 'nicName', title: '登录用户名', props: { readonly: true } },
            {
                type: 'select', field: 'pGroupId', title: '上级组长', value: null, ...await ruleDirectorGroup(), props:{filterable:true,clearable:true}, validate: [{ type: 'string', required: false }], props: { filterable: true }
            },
            { type: 'switch', field: 'enabled', title: '是否启用', value: true },
            {
                type: 'select', field: 'superviseId', title: '主管', value: null, ...await ruleDirectorGroup(), props:{filterable:true,clearable:true}, validate: [{ type: 'string', required: true, message: '请输入主管' }]
            },
            {
                type: 'select', field: 'managerId', title: '经理', value: null, ...await ruleDirectorGroup(), props:{filterable:true,clearable:true}, validate: [{ type: 'string', required: true, message: '请输入经理' }]
            },
            {   type:'select',  field:  'projName', title:  '项目', value: null, ...await projList({projName:''}), props:{filterable:true,clearable:true}},
            {
                type: 'select', field: 'architectureArr', title: '架构', value: null, props: { multiple: true, clearable: true, filterable: true, 'collapse-tags': true }, validate: [{ required: false }],
                options: this.buildList, on: { change: () => { this.onBuildChange(1) } }
            },
            { type: 'hidden', field: 'architecture', title: '架构字符串', value: null }
            ];
            this.autoformZY.rule = rule
            this.autoform.rules = rules
        },
        async remoteSearchUser(parm) {
            if (!parm) {
                //this.$message({ message: this.$t('api.sync'),type: 'warn'})
                return;
            }
            var options = [];
            let rlt = await QueryAllDDUserTop100({ keywords: parm });
            if (rlt && rlt.success) {
                options = rlt.data?.map(item => {
                    return { label: item.userName + "(" + item.deptName + ")", value: item.userId, extData: item }
                });
            }
            this.autoform.fApi.getRule('userId').options = options;
        },
        async selectUser() {
            var userId = this.autoform.fApi.getValue('userId');
            if (userId > 0) {
                const res = await getUser({ id: userId });
                if (!res?.success) return
                this.autoform.fApi.getRule('nicName').value = res.data?.userName
            }
        },
        async remoteSearchZYUser(parm) {
            if (!parm) {
                //this.$message({ message: this.$t('api.sync'),type: 'warn'})
                return;
            }
            var options = [];
            let rlt = await QueryAllDDUserTop100({ keywords: parm });
            if (rlt && rlt.success) {
                options = rlt.data?.map(item => {
                    return { label: item.userName + "(" + item.deptName + ")", value: item.userId, extData: item }
                });
            }
            this.autoformZY.fApi.getRule('userId').options = options;
        },
        async selectZYUser() {
            var userId = this.autoformZY.fApi.getValue('userId');
            if (userId > 0) {
                const res = await getUser({ id: userId });
                if (!res?.success) return
                this.autoformZY.fApi.getRule('nicName').value = res.data?.userName
            }
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        //分页查询
        async getlist() {
            this.listLoading = true
            var res = await GetDirectorGroupAllListV2(this.ListInfo);

            this.listLoading = false
            if (!res?.success) return
            const data = res.data;
            this.list = data
        },
        selsChange: function (sels) {
            this.sels = sels;
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event) {
            if (column.property == 'value' && row.value)
                this.Director = row.key
            this.zyFilter.selectId = row.key;
            this.getDetailList();
        },
        async getDetailList() {
            if (this.zyFilter.selectId <= 0) {
                return;
            }
            this.detail.listLoading = true
            const params = { groupid: this.zyFilter.selectId, name: this.zyFilter.userName, company: this.zyFilter.company, userType: this.zyFilter.userType, enabled: this.zyFilter.enabled}
            params.architecture = this.zyFilter.architectureArr ? this.zyFilter.architectureArr.join(',') : null;
            var res = await GetDirectorAllList(params);
            this.listLoading = false;
            this.detail.listLoading = false
            if (!res?.success) return
            const data = res.data;
            res?.data?.forEach(f => {
                f.groupId = this.zyFilter.selectId
            })
            this.detail.list = data
        },
        async onAddDirector() {
            this.addFormVisible = true;
            var arr = Object.keys(this.autoformZY.fApi);
            if (arr.length > 0) {
                await this.autoformZY.fApi.resetFields()
                await this.autoformZY.fApi.disabled(false, 'userName')
                await this.autoformZY.fApi.sync('userName')
            }
            await this.$nextTick(() => {
                // 只有在用户有权限时才处理specialRoleType和specialGroupIds
                if (this.checkPermission('OperationalSpecialPrivilege')) {
                    this.autoformZY.fApi.hidden(true, 'specialGroupIds')
                    this.autoformZY.fApi.setValue('specialRoleType', 0)
                }
            })
            this.formtitle = "新增组员";
        },
        async onAddDirectorGroup() {
            this.addFormVisible1 = true;
            var arr = Object.keys(this.autoform.fApi);
            if (arr.length > 0) {
                await this.autoform.fApi.resetFields()
                await this.autoform.fApi.disabled(false, 'userName')
            }
            this.formtitle = "新增组长";
        },
        //编辑组长信息
        async onEditDirectorGroup(row) {
            this.addFormVisible1 = true;
            let res = null;
            if (row.key) {
                var _user = await getUser({ id: row.thirdId });
                res = await GetDirectorGroupById({ id: row.key });
            }
            let model = {
                id: row.key,
                userName: row.value,
                userId: !res.data.userId ? '' : parseInt(res.data.userId),
                groupId: !row.groupId ? '' : row.groupId,
                nickName: _user?.nickName,
                enabled: res.data.enabled,
                pGroupId: !res.data.pGroupId ? '' : res.data.pGroupId.toString(),
                superviseId: !res.data.superviseId ? '' : res.data.superviseId.toString(),
                architectureArr: res.data.architecture ? res.data.architecture.split(',') : [],
                projName: !res.data.projName ? '' : res.data.projName.toString(),
                managerId: !res.data.managerId ? '' : res.data.managerId.toString()
            }
            let arr = Object.keys(this.autoform.fApi);
            if (arr.length > 0) {
                this.autoform.fApi.getRule('nicName').value = "";
                this.autoform.fApi.getRule('nicName').value = _user?.data?.userName;
                await this.autoform.fApi.setValue(model)
                await this.autoform.fApi.disabled(true, 'userName')
                await this.autoform.fApi.sync('userName')
                await this.autoform.fApi.sync('nickName')
            }
            let options = [{ value: res.data.userId, label: res.data.userFullName }];
            this.autoform.fApi.getRule('userId').options = options;
            this.formtitle = "编辑组长";
        },
        //编辑组员信息
        async onEditDirector(row) {
            this.addFormVisible = true;
            var res = null;
            if (row.thirdId) {
                var _user = await getUser({ id: row.thirdId });
                res = await GetDirectorById({ id: row.key })
            }
            var model = {
                id: row.key, userName: row.value, userId: !res.data.userId ? '' : parseInt(res.data.userId), groupId: !res.data.groupId ? '' : res.data.groupId + '',
                nickName: _user?.nickName, enabled: res.data.enabled, specialRoleType: res.data.specialRoleType, specialGroupIds: res.data.specialGroupIds,
                company: res.data.company, userType: res.data.userType
            }
            await this.$nextTick(async () => {
              // 如果用户有权限，则正常处理specialRoleType和specialGroupIds
              if (this.checkPermission('OperationalSpecialPrivilege')) {
                  this.autoformZY.fApi.hidden(true, 'specialGroupIds')
                  // 不要重置specialRoleType为0，保持原有值
              } else {
                  // 如果用户没有权限，但需要保持原有值用于保存，创建隐藏字段
                  if (model.specialRoleType !== undefined && model.specialRoleType !== null) {
                      // 检查字段是否已存在，如果不存在则添加隐藏字段
                      try {
                          this.autoformZY.fApi.getRule('specialRoleType');
                      } catch (e) {
                          // 字段不存在，添加隐藏字段
                          const specialRoleTypeRule = {
                              type: 'hidden',
                              field: 'specialRoleType',
                              value: model.specialRoleType
                          };
                          this.autoformZY.fApi.addRule(specialRoleTypeRule);
                      }
                  }
                  if (model.specialGroupIds !== undefined && model.specialGroupIds !== null) {
                      // 检查字段是否已存在，如果不存在则添加隐藏字段
                      try {
                          this.autoformZY.fApi.getRule('specialGroupIds');
                      } catch (e) {
                          // 字段不存在，添加隐藏字段
                          const specialGroupIdsRule = {
                              type: 'hidden',
                              field: 'specialGroupIds',
                              value: model.specialGroupIds
                          };
                          this.autoformZY.fApi.addRule(specialGroupIdsRule);
                      }
                  }
              }
            })
            var arr = Object.keys(this.autoformZY.fApi);
            if (arr.length > 0) {
                this.autoformZY.fApi.getRule('nicName').value = "";
                this.autoformZY.fApi.getRule('nicName').value = _user?.data?.userName;
                await this.autoformZY.fApi.setValue(model)
                await this.autoformZY.fApi.disabled(true, 'userName')
                await this.autoformZY.fApi.sync('userName')
                await this.autoformZY.fApi.sync('nickName')
            }

            // 只有在用户有权限时才处理specialGroupIds的显示逻辑
            if (this.checkPermission('OperationalSpecialPrivilege') && model.specialRoleType == 2) {
                this.autoformZY.fApi.hidden(false, 'specialGroupIds')
            }
            let options = [{ value: res.data.userId, label: res.data.userFullName }];
            this.autoformZY.fApi.getRule('userId').options = options;
            this.formtitle = "编辑组员";
        },
        //修改组长信息
        async onAddSubmitGroup() {
            const formlist = this.autoform.fApi.formData();
            if(!formlist.userName || !formlist.userId || !formlist.superviseId|| !formlist.managerId){
              this.$message({ message: '请填写必填完整名称和登录用户信息', type: 'warning' })
              return
            }
            this.addLoading = true;
            await this.autoform.fApi.validate(async (valid, fail) => {
                if (valid) {
                    const formData = this.autoform.fApi.formData();
                    formData.id = formData.id ? formData.id : 0;
                    //formData.Enabled=true;
                    const res = await addoreditDirectorGroup(formData);
                    if (res.success) {
                        this.getlist();
                        this.$message({ message: '保存成功', type: 'success' })
                        this.addFormVisible1 = false;
                    }
                } else {
                    //todo 表单验证未通过
                }
            })
            this.addLoading = false;
        },
        //修改组员信息
        async onAddSubmit() {
            const formlist = this.autoformZY.fApi.formData();
            if(!formlist.userName || !formlist.userId || !formlist.groupId || !formlist.company){
              this.$message({ message: '请填写完整的必填信息', type: 'warning' })
              return
            }
            this.addLoading = true;
            await this.autoformZY.fApi.validate(async (valid, fail) => {
                if (valid) {
                    const formData = this.autoformZY.fApi.formData();
                    formData.id = formData.id ? formData.id : 0;
                    //formData.Enabled=true;
                    const res = await addOrUpdateDirector(formData);
                    if (res.success) {
                        this.getDetailList(this.Director);
                        this.$message({ message: '保存成功', type: 'success' })
                        this.addFormVisible = false;
                    }
                } else {
                    //todo 表单验证未通过
                }
            })
            this.addLoading = false;
        }
    }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary {
    margin-right: 7px;
}
</style>
