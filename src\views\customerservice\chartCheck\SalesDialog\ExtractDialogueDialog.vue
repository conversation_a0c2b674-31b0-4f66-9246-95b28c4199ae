<template>
    <el-dialog title="提取话术" :visible.sync="isShow" width="50%" :before-close="closeDialog" v-if="isShow" v-dialogDrag>
    <div style="">
        <el-descriptions size="small" class="margin-top" title="" :column="3">
          <el-descriptions-item label="平台/店铺"> {{platformName}} — {{dataJson?.shopName}}</el-descriptions-item>
          <el-descriptions-item label="线上订单号">{{dataJson?.orderNo}}</el-descriptions-item>
          <el-descriptions-item label="会话时间">{{dataJson.orderTime }}</el-descriptions-item>
        </el-descriptions>
    </div>
  <!-- 聊天记录 -->
    <div v-show="isShowOrHide"> 
        <QualityComponent   ref="chartRef" :isShow="isShow" ></QualityComponent>
    </div> 

    <!-- 提取话术 -->

    <el-form ref="formDataRef"  style="margin-top: 20px" label-width="100px">
      <!-- <el-form-item v-if="infoList.length === 0" :label="' 1'">
        <el-input  show-word-limit :maxlength="100" v-model="" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" clearable />
      </el-form-item> -->
      <el-form-item :key="index" label="提取话术：" class="first" >
      </el-form-item>
      <el-form-item v-for="(item, index) in infoList" :key="index" :label=" ` ${index + 1}`">
           <el-input   :maxlength="100" v-model="item.info" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" clearable />
      </el-form-item>
      <div style="margin-left: 70px; color: blue;width:80px" @click="addFormItem">新增一行</div>
    </el-form> 



        <template #footer>
             <div class="dialog-footer" style="display:flex;justify-content: flex-end;">
                      <div style="position: relative;">
                          <el-button @click="btnChange('last')" :disabled="isLastButtonDisabled" type="primary">查看上一个</el-button>
                          <div style="position: absolute;right:-20px;top:-20px;cursor:pointer;" > 
                              <el-tooltip class="item" effect="dark" content="点击键盘的上箭头↑，可以快速查看上一个" placement="top"><i class="el-icon-question"></i></el-tooltip>
                          </div>
                      </div>
                      <div style="position: relative;margin-left:20px;">
                           <el-button @click="btnChange('next')" :disabled="isNextButtonDisabled" type="primary" >查看下一个</el-button>
                            <div  style="position: absolute;right:-20px;top:-20px; cursor:pointer;" >
                                <el-tooltip class="item" effect="dark" content="点击键盘的下箭头↓，可以快速查看下一个" placement="top"> <i class="el-icon-question"></i></el-tooltip>
                           </div>
                      </div>
                      <div style="position: relative;margin-left:20px;">
                            <el-button @click="submitForm" :disabled="isSubmitDisabled" :loading="isLoading" type="primary" v-throttle="3000">保存关闭</el-button>
                             <el-button @click="saveNextSubmitDot" :disabled="isNextSubmitDisabled" :loading="isLoading2" type="primary" v-throttle="3000">保存提取下一个</el-button>
                      </div>
           </div>
      </template>
    </el-dialog>
  </template>
  <script>
  import { formatTime } from "@/utils";
  import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
  import QualityComponent from "@/views/customerservice/chartCheck/SalesDialog/qualityComponent.vue"
  import {uploadCredentialsAsync,uploadResponsibilityAsync,getReplyCarelesslyById,addQualityChatInfoAsync,getQualityChatInfoList} from "@/api/customerservice/chartCheck";
  
  export default {
    props: {
      isShow: {
        type: Boolean,
        default: false,
      },
       isView: {
              type: Boolean,
              default: false
          },
    },
    components:{uploadimgFile,QualityComponent},
    computed: {
      platformName()//平台初始化
       {
        let platformList = [
          { name: "拼多多", value: 2 },
          { name: "抖音", value: 6 },
          { name: "天猫", value: 1 },
          { name: "淘工厂", value: 8 },
          { name: "淘宝", value: 9 },
        ]
        if (this.dataJson?.platform) {
          return platformList.filter(item => item.value == this.dataJson?.platform)[0].name
        } else {
          return ""
        }
      },
      insertTime() //日期转换
      {
        return this.dataJson?.insertTime?formatTime(this.dataJson?.insertTime , "YYYY-MM-DD"):""
      },
    },
      created(){
  document.addEventListener('keydown', this.handleArrowUp);
    },
    watch: {
      isShow(newVal, oldVal) {
        if (newVal) {
          this.$nextTick(() => {
            // this.$refs.chartRef.interfaceType = true;//表示敷衍回复页面
            this.$refs.chartRef.dataJson = this.dataJson
            // this.getChartList();
          });
          this.buttonDisabled();//按钮显示
          this.getChartList();
        }
      },
    },
    mounted(){
    },
    data() {
      return {
          keyword:null,
          dataJson: {},
          typeList:["对话中出现两句/三句一样的话术/表情/标点符号","发送商品链接三次及以上","多次发截图","敷衍对话，多次发欢迎用语，抱歉话术","答非所问"],//敷衍类型
          isShowOrHide:true,
             currentInfo:null,
          isLoading2:false,
          isLoading:false,
          tableData:[],
          isLastButtonDisabled:false,
        isNextButtonDisabled:false,
        isNextSubmitDisabled:false,
        isSubmitDisabled:false,
        infoList: [{info:""}] // 存储表单项的数组
      };
    },
    methods: {
        
        handleArrowUp(event) {
            if(!this.isShow){ 
              return
            }
            if (event.key === 'ArrowUp' && !this.isLastButtonDisabled) {
              // 处理向上键被按下的逻辑
              this.btnChange('last');
            }
            if (event.key === 'ArrowDown' && !this.isNextButtonDisabled) {
              console.log('ArrowDown')
              // 处理向下键被按下的逻辑
              this.btnChange('next');
            }
        },
        //关闭当前页面
        closeDialog() {
            this.$emit("closeDialog");
        },
        async getChartList() {
          var data={orderNo:this.dataJson.orderNo}
          const res=  await getQualityChatInfoList(data);
          console.log(res)
          this.infoList = res.data;
        },
        onSubmitValidate: function () {
            let isValid = true;
            this.$refs.formDataRef.validate((valid) => {
              isValid = valid;
            });
            return isValid;
        },
        async  submitForm() {  //保存关闭
              //  if (!this.onSubmitValidate()) {
              //       return;
              //     }
                const data={
                    orderNo:this.dataJson.orderNo,
                    infoList:this.infoList
                }
                const res =await addQualityChatInfoAsync(data)
                  if (res.success==true) {
                      this.$message.success('操作成功');
                      this.$emit("upData");
                      this.$emit("closeDialog");
                  } else {
                      this.$message.error('操作失败')
                  }
                  this.infoList=[{info:''}];
        },
        async saveNextSubmitDot() {   //保存审核下一个
              if (!this.onSubmitValidate()) {
                    return;
                  }
                const params={
                    orderNo:this.dataJson.orderNo,
                    infoList:this.infoList
                }
                  const res = await addQualityChatInfoAsync(params)
                  if ((res.success==true)) {
                      this.$message.success('操作成功');
                      this.btnChange('next','submit');//下一条数据
                      this.$emit("upData");
                  } else {
                      this.$message.error('操作失败')
                  }
      
        },
        async btnChange(last,btn){//查看上一个、查看下一个
          this.infoList=[{info:''}];
            const index = this.tableData.findIndex(index=>index.orderNo==this.dataJson.orderNo);
            
            if(btn=='submit'){//已审核过后的数据进行移除
                this.tableData = this.tableData.filter((item) => {return item.orderNo != this.dataJson.orderNo;}); 
            }    

            if(last=='last' && this.tableData.length>0){
      
                var number=index-1;
                //这种情况只能是最后一条数据审核完了，并且table里面已经清除当前审核id，
                //所以找不到当前审核的id下标，但table中还有数据，我需要上翻页
                if(this.tableData.length>0 && index==-1)
                {
                    number=this.tableData.length-1
                }
      
                  const info=  this.tableData[number]
                  this.dataJson=JSON.parse(JSON.stringify(info))
                  this.$refs.chartRef.dataJson = info;
                  this.$refs.chartRef.getChartList()
            }
            else if(last=='next' && this.tableData.length>0)
            {
                  const number=btn =='submit' ? index : index+1;
                  const info=this.tableData[number]
                  this.dataJson= JSON.parse(JSON.stringify(info))
                  this.$refs.chartRef.dataJson = info;
                  this.$refs.chartRef.getChartList()
            }
                  this.buttonDisabled(btn,index)//按钮是否禁用
        },
        async  buttonDisabled(btn,indexs){ //按钮是否禁用
        
              this.isLastButtonDisabled=false;
              this.isNextButtonDisabled=false;
              this.isSubmitDisabled=false;
              this.isNextSubmitDisabled=false;
              const index = btn =='submit' ? indexs : this.tableData.findIndex(index=>index.orderNo==this.dataJson.orderNo);
            if (this.tableData.length === 1) {
                this.isLastButtonDisabled = true;
                this.isNextButtonDisabled = true;
            }
              if(index==0 || this.tableData.length==0){
                this.isLastButtonDisabled=true;
              }
              if(index==this.tableData.length-1 || (btn=='submit' && index==this.tableData.length)){
                this.isNextButtonDisabled=true;
              }
              //查看当前信息是否被修改过
              // this.currentInfo = await getReplyCarelesslyById({id:this.dataJson.id});
              // if(this.currentInfo.data.responsibilityType!="" && this.currentInfo.data.responsibilityType!=null){
              //    this.isSubmitDisabled=true;
              //    this.isNextSubmitDisabled=true;
              // }
        },
        addFormItem() {
          // 新增表单项
            this.infoList.push({ info: '' });
        }
        //   showOrhide(){
        //     if(this.isShowOrHide)
        //      this.isShowOrHide=false;
        //     else this.isShowOrHide=true
        //   },
    },
  };
  </script>
  
  
  <style lang="scss" scoped>
  
  //顶部可点击div样式
  ::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell{
    padding: 15px;
  }
  ::v-deep .el-descriptions__body .el-descriptions__table{
    background-color: rgb(242, 244, 245);
  }
  
  
  ::v-deep .el-descriptions__body .el-descriptions-item__container {
    font-size: 14px;
  }
  
  .first ::v-deep .el-form-item__label:before {
    content: "*";
    color: #f56c6c;
    margin-right: 4px;
  }
  </style>
  