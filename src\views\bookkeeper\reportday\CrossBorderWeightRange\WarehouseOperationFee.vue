<template>
    <MyContainer>
        <el-tabs v-model="current" style="height: 95%;">
            <el-tab-pane label="佳速达" name="tab1" style="height: 98%;">
                <OperationShipOut :type="'ShipOut'" name="佳速达" />
            </el-tab-pane>
            <el-tab-pane label="九方" name="tab2" :lazy="true" style="height: 98%;">
                <OperationTofba :type="'Tofba'" name="九方" />
            </el-tab-pane>
            <el-tab-pane label="亚吉达" name="tab3" :lazy="true" style="height: 98%;">
                <OperationElt :type="'Elt'" name="亚吉达" />
            </el-tab-pane>
            <el-tab-pane label="赤道" name="tab4" :lazy="true" style="height: 98%;">
                <OperationSogoodseller :type="'Sogoodseller'" name="赤道" />
            </el-tab-pane>
            <el-tab-pane label="左海" name="tab5" :lazy="true" style="height: 98%;">
                <OperationLeftOcean :type="'LeftOcean'" name="左海" />
            </el-tab-pane>
            <el-tab-pane label="环世" name="tab6" :lazy="true" style="height: 98%;">
                <OperationWorldwide name="环世" />
            </el-tab-pane>

        </el-tabs> 
    </MyContainer>
</template>
<script>
import MyContainer from "@/components/my-container";
import OperationShipOut from './tab/OperationShipOut.vue'
import OperationTofba from './tab/OperationTofba.vue'
import OperationElt from './tab/OperationElt.vue' 
import OperationSogoodseller from './tab/OperationSogoodseller.vue'
import OperationLeftOcean from './tab/OperationLeftOcean.vue'
import OperationWorldwide from './tab/OperationWorldwide.vue'

export default {
    components: {
        MyContainer, OperationShipOut, OperationTofba, OperationElt, OperationSogoodseller,OperationLeftOcean,OperationWorldwide
    },
    data() {
        return {
            current: 'tab1'
        };
    },
    methods: {

    }
};
</script>

<style lang="scss" scoped></style>