<template>
    <my-container v-loading="pageLoading" style="position: relative">
        <el-tabs v-if="activeName" v-model="activeName" @tab-click="handleClick"
            style="position: absolute;right: 0;top: 0;left: 0; background-color:white;">
            <el-tab-pane  :label="orderNo"  :name="orderNo"></el-tab-pane>
        </el-tabs>
        <div
            style="cursor:pointer;margin-left:95%; margin-top: 5px;margin-bottom: 2px;position: absolute;right: 0;top: 0;left: 0; background-color:white;">
            <span @click="orderByList" style="color:#0094ff;display:inline-block;user-select:none;">
                ⬆⬇{{ isOrder ? "正序" : "倒序" }}
            </span>
        </div>
       <div style="margin-top:43px;">
        <tbody v-for="orderLog in chatLogList">
            <tr style="line-height:30px;">
                <td style="width:150px;height:20px;">{{ orderLog.chatTime }}</td>
                <td style="width:200px;height:20px;">{{ orderLog.chatUser }}</td>
                <td style="width:800px;height:20px;">{{ orderLog.chatContext }}</td>
            </tr>
        </tbody>
           {{ this.noContext }}
        </div>
    </my-container>
</template>

<script>
import MyContainer from '@/components/my-container';
import { GetOrderChatListByOrderNo } from "@/api/customerservice/pddonlyrefund";
import Table from '../../../components/Table/table.vue';
export default {
    name: 'OrderChatListByInnerNos',
    components: { MyContainer },
    props: {
        orderNo: { type: String, default: "" },
    },
    data () {
        return {
            activeName: '',
            saleAfterOrderNoList: [],
            chatLogList: [],
            pageLoading: false,
            isOrder: true,
            noContext:""
        }
    },
    async mounted () {
        await this.initData();
    },
    methods: {
        async initData () {
            this.pageLoading = true;
            let rsp=await GetOrderChatListByOrderNo(this.orderNo);
            if(rsp?.success){
                this.saleAfterOrderNoList = rsp.data;
                this.activeName = this.orderNo + '';
                if (this.saleAfterOrderNoList.length > 0) {
                    this.noContext="";
                    this.chatLogList = rsp.data;
                }else
                {   
                    this.noContext ="暂无内容" 
                }
            }                       
            this.pageLoading = false;
        },
        async handleClick (tab, event) {
            this.chatLogList = this.saleAfterOrderNoList;
        },
        orderByList () {
            this.isOrder = !this.isOrder;
            //进行数组排序
            if (this.isOrder) {
                this.sortKeyAsc(this.chatLogList, "chatTime");
            } else {
                this.sortKeyDesc(this.chatLogList, "chatTime");
            }
        },
        //正序
        sortKeyAsc (array, key) {
            return array.sort(function (a, b) {
                var x = a[key];
                var y = b[key];
              
                return ((x < y) ? -1 : (x > y) ? 1 : 0)
            })
        },
        //倒序
        sortKeyDesc (array, key) {
            return array.sort(function (a, b) {
               
                var x = a[key];
                var y = b[key];
                return ((x > y) ? -1 : (x < y) ? 1 : 0)
            })
        }

    },
}
</script>

<style scoped></style>