<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="抖音账单费用" name="first1" style="height: 100%">
        <DYBill ref="refKDYBill" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="抖音精选联盟" name="first2" style="height: 100%" lazy>
        <DYSelectAlliance ref="refDYSelectAlliance" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="保费扣除" name="first3" style="height: 100%" lazy>
        <PremiumDeduction ref="refPremiumDeduction" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="订单管理" name="first4" style="height: 100%;" lazy>
        <OrderManagement ref="OrderManagement" style="height: 100%;"/>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import DYBill from "./DYBill.vue";
import DYSelectAlliance from "./DYSelectAlliance.vue";
import PremiumDeduction from "./PremiumDeduction.vue";
import OrderManagement from "./OrderManagement.vue";
export default {
  name: "videoBillIndex",
  components: {
    MyContainer, DYBill, DYSelectAlliance, PremiumDeduction, OrderManagement
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },
};
</script>

<style lang="scss" scoped></style>
