<template>
  <containernofooter v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="日期:">
            <el-date-picker style="width: 260px"
                v-model="filter.timerange"
                type="datetimerange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
           ></el-date-picker>
        </el-form-item>
        <el-form-item label="分仓:">
          <el-select v-model="filter.warehouse" clearable filterable placeholder="请选择分仓" style="width: 100px">
            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value"/>
            <!-- <el-option label="请选择" value></el-option>
            <el-option label="义乌" value="0"></el-option>
            <el-option label="昌东" value="1"></el-option>
            <el-option label="安徽" value="3"></el-option> -->
          </el-select>
        </el-form-item>
       <el-form-item label="运营组:">
          <el-select v-model="filter.proCategroyId" clearable filterable placeholder="请选择运营组" style="width: 120px">
            <el-option label="所有" value=""/>
            <el-option v-for="item in categroylist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
      <el-form-item label="采购员:">
        <el-select v-model="filter.proBrandId" multiple collapse-tags  clearable filterable placeholder="请选择采购员" style="width: 150px">
           <el-option v-for="item in brandlist" :key=item.value :label="item.label" :value=item.value />
        </el-select>
      </el-form-item>
      <el-form-item label="商品编码:">
          <el-input v-model="filter.proBianMa" style="width: 120px"/>
      </el-form-item>
      <el-form-item label="商品编码名称:">
          <el-input v-model="filter.proBianMaName" style="width: 150px"/>
      </el-form-item>
      <el-form-item label="在途时长:">
        <el-button-group>
              <el-button style="padding: 0;margin: 0;">
                <el-input-number placeholder="最小(天)" v-model="filter.mindayLastInTransitTime" style="width: 110px"></el-input-number>
                <el-input-number placeholder="最小(时)" v-model="filter.minhourLastInTransitTime" style="width: 110px"></el-input-number>
              </el-button>
              <el-button >至</el-button>
              <el-button style="padding: 0;margin: 0;">
                <el-input-number placeholder="最大(天)" v-model="filter.maxdayLastInTransitTime" style="width: 110px"></el-input-number>
                <el-input-number placeholder="最大(时)" v-model="filter.maxhourLastInTransitTime" style="width: 110px"></el-input-number>
              </el-button> 
          </el-button-group> 
      </el-form-item>         
      <el-form-item label="平均在途时长:"> 
          <el-button-group>
              <el-button style="padding: 0;margin: 0;">
                <el-input-number placeholder="最小(天)" v-model="filter.mindayAvgInTransitTime" style="width: 110px"></el-input-number>
                <el-input-number placeholder="最小(时)" v-model="filter.minhourAvgInTransitTime" style="width: 110px"></el-input-number>
              </el-button>
              <el-button >至</el-button>
              <el-button style="padding: 0;margin: 0;">
                <el-input-number placeholder="最大(天)" v-model="filter.maxdayAvgInTransitTime" style="width: 110px"></el-input-number>
                <el-input-number placeholder="最大(时)" v-model="filter.maxhourAvgInTransitTime" style="width: 110px"></el-input-number>
              </el-button> 
          </el-button-group>
      </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <!-- <el-button type="primary" @click="onDown">下载导入模板</el-button> -->
          <el-button type="primary" @click="startImport">导入</el-button>
          <el-button type="primary" @click="startImportV2"
          v-if="checkPermission(['api:Inventory:Warehouse:ImportWarehouseRecordv2Async'])">导入V2.0</el-button>
       </el-form-item>
      </el-form>
    </template>
  <el-tabs v-model="activeName" style="height:94%;">
    <el-tab-pane label="商品编码" name="first" style="height: 100%;">
        <probianmadata :filter="filter" ref="probianmadata" style="height: 100%;" @onpkbianma="onpkbianma"/>
      </el-tab-pane>
    <el-tab-pane label="商品编码分析" name="second" style="height: 100%;">
        <probianmaanalysis :filter="filter" ref="probianmaanalysis"/>
      </el-tab-pane>
  </el-tabs>
  <el-dialog title="导入" :visible.sync="dialogVisible" width="30%">
     <div>
       <!-- <el-alert title="温馨提示：请同时选取全仓、义乌、昌北、昌东 4个文件！" type="success" :closable="false">
      </el-alert> -->
        <!-- <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
             <el-date-picker v-model="importFilte.date" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd 23:59:59" placeholder="选择日期"></el-date-picker>
          </el-col>
          </el-row> -->
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="40" action accept=".xlsx"
                :on-change="uploadChange" :on-remove="uploadRemove" :http-request="uploadFile" :data="fileparm">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template> 
                  <el-button style="margin-left: 10px;" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?`上传中${upNumber}`:'上传' )}}</el-button>
                </el-upload>
            </el-col>
          </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
  </el-dialog>
  <el-dialog title="导入" :visible.sync="dialogV2Visible" width="30%">
     <div>
    
        <el-row>
          <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
            <el-checkbox v-model="fileparmV2.istoday">今天</el-checkbox>
          </el-col>
          <el-col :xs="24" :sm="19" :md="19" :lg="19" :xl="19">
              <el-radio-group v-model="fileparmV2.warehouse" size="mini">
                <el-radio-button label border>总仓</el-radio-button>
                <el-radio-button label="0" border>义乌</el-radio-button>
                <el-radio-button label="2" border>昌北</el-radio-button>
                <el-radio-button label="1" border>昌东</el-radio-button>
              </el-radio-group>
          </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="40" action accept=".xlsx"
                :on-change="uploadChangeV2" :on-remove="uploadRemoveV2" :http-request="uploadFileV2" :data="fileparm">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template> 
                  <el-button style="margin-left: 10px;" size="small" type="success" :loading="uploadLoadingV2" @click="submitUploadV2">{{(uploadLoadingV2?`上传中${upNumberV2}`:'上传' )}}</el-button>
                </el-upload>
            </el-col>
          </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogV2Visible = false">关闭</el-button>
      </span>
  </el-dialog>
  </containernofooter>
</template>
<script>
  import {getAllProBrand,importWarehouseRecord,importWarehouseRecordV2} from '@/api/inventory/warehouse'
  import {getDirectorGroupList as getAllProBianMaCategroy } from '@/api/operatemanage/base/shop'
  import probianmaanalysis from '@/views/inventory/probianmaanalysis'
  import probianmadata from '@/views/inventory/probianmadata'
  import containernofooter from '@/components/my-container/nofooter'
  import { warehouselist } from "@/utils/tools";
export default {
  name: 'Roles',
  components: { containernofooter,probianmaanalysis,probianmadata},
  data() {
    return {
      activeName: 'first',
      filter: {
        startDate: null,
        endDate: null,
        timerange:null,
        warehouse:null,
        proCategroyId:null,
        proBrandId:[],
        proBianMa:"",
        maxLastInTransitTime:null,
        minLastInTransitTime :null,
        maxAvgInTransitTime:null,
        minAvgInTransitTime :null,
        maxdayLastInTransitTime:null,
        maxhourLastInTransitTime:null,
        mindayLastInTransitTime :null,
        minhourLastInTransitTime :1,
        maxdayAvgInTransitTime:null,
        maxhourAvgInTransitTime:null,
        mindayAvgInTransitTime :null,
        minhourAvgInTransitTime :null,
      },
      warehouselist:warehouselist,
      importFilte:{date:null},
      options:[],
      categroylist:[],
      brandlist:[],
      fileList:[],
      fileparm:{ },
      fileparmV2:{istoday:true,warehouse:null},
      pageLoading: false,    
      dialogVisible: false,
      selectloading:false,
      uploadLoading:false,
      upNumber:0,
      fileListV2:[],
      dialogV2Visible: false,
      uploadLoadingV2: false,
    }
  },
  mounted() {
    this.defaultDate();
    this.init();
    this.onSearch();
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: { 
    defaultDate(){
        let date = new Date()
        let year = date.getFullYear().toString()   //'2019'
        let month = date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1).toString():(date.getMonth()+1).toString()  //'04'
        let da = date.getDate() < 10 ? '0'+date.getDate().toString():date.getDate().toString()  //'12'
        let end = year + '-' + month + '-' + da  //当天'2019-04-12'
        let beg = year + '-' + month + '-01'    //当月第一天'2019-04-01'
        this.filter.timerange = [beg,end]
    },
   init(){
      this.selectloading = true;
      setTimeout(async () => {
        this.selectloading = false;
        var res1= await getAllProBianMaCategroy();
        if (!res1?.success) return;
        this.categroylist = res1.data.map(item => {
           return { value: item.key, label: item.value };
        });
        var res2= await getAllProBrand();
        if (!res2?.success) return;
        this.brandlist = res2.data.map(item => {
           return { value: item.key, label: item.value };
        });
      }, 200);
    },
    onSearch() {
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      if (!this.filter.timerange||this.filter.timerange.length<2){
        this.$message({message: "请先选择日期！",type: "warning",});
        return;
      }
      if (this.activeName=='first'){
        this.$refs.probianmadata.onSearch();
      }
      else if (this.activeName=='second')
        this.$refs.probianmaanalysis.onSearch();
    },
    onpkbianma(probianmas,warehouse){
        this.activeName="second"
        this.$refs.probianmaanalysis.onpk(probianmas,warehouse);
    },
   onDown() {
      var alink = document.createElement("a");
      alink.href =`../static/excel/inventory/商品编码导入模板.xlsx`;
      alink.click();
    },
    startImport(){
      this.dialogVisible=true;
    },
     uploadChange(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for(var i=0;i<fileList.length;i++){
          if(fileList[i].status=="success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.fileList = list;
      }
    },
     uploadRemove(file, fileList){
       this.uploadChange(file, fileList);
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    async submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
        }
      this.fileHasSubmit=true;
      this.uploadLoading=true;
      this.$refs.upload.submit();
    },
   async uploadFile(item) {
    if(!this.fileHasSubmit) return false; 
    this.fileHasSubmit=false;
    this.upNumber=0;   
    this.fileList.forEach(async f=>{
      var file=f;
      console.log('file',file) 
      const form = new FormData();
      form.append("file", file);
　    var res=await importWarehouseRecord(form); 
      if (res.code==1) this.$message({ message: `${file.name}上传成功,总计${this.fileList.length}个文件...`, type: "success" });
      else this.$message({ message:`${file.name}上传失败,${res.msg}`, type: "warning" });
    })
     this.$refs.upload.clearFiles();
     this.uploadLoading=false;
    },

  async startImportV2(){
      this.dialogV2Visible=true;
    },
  uploadChangeV2(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for(var i=0;i<fileList.length;i++){
          if(fileList[i].status=="success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.fileListV2 = list;
      }
    },
     uploadRemoveV2(file, fileList){
       this.uploadChangeV2(file, fileList);
    },
    //上传成功
    uploadSuccessV2(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    async submitUploadV2() {
      if (!this.fileListV2 || this.fileListV2.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
        }
      this.fileHasSubmitV2=true;
      this.uploadLoadingV2=true;
      this.$refs.upload.submit();
    },
   async uploadFileV2(item) {
    if(!item){
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
        }
    if(!this.fileHasSubmitV2) return false; 
    this.fileHasSubmitV2=false;
    this.upNumber=0;   
    this.fileListV2.forEach(async f=>{
      var file=f;
      console.log('file',file) 
      const form = new FormData();
      form.append("file", file);
      form.append("istoday", this.fileparmV2.istoday);
      form.append("warehouse", this.fileparmV2.warehouse);
　    var res=await importWarehouseRecordV2(form); 
      if (res.code==1) this.$message({ message: `${file.name}上传成功,总计${this.fileListV2.length}个文件...`, type: "success" });
      else this.$message({ message:`${file.name}上传失败,${res.msg}`, type: "warning" });
    })
     this.$refs.upload.clearFiles();
     this.uploadLoadingV2=false;
    },
  }
}
</script>