<template>
  <!-- style="height: 60vh;" -->
  <my-container v-loading="pageLoading">
    <template #header>
      <div style="">
        <el-form :inline="true" :model="query" @submit.native.prevent>
          <el-form-item label="日期:">
            <el-date-picker class="datecss" style="height: 34px;" v-model="query.date" type="month"
              value-format="yyyy-MM-dd" placeholder="选择月"></el-date-picker>
          </el-form-item>
          <el-form-item label="省份/城市" prop="provinceCode">
            <el-cascader collapse-tags v-model="query.provinceCityCodeList" :props="cascaderProps" :options="options"
              placeholder="省份/城市" clearable filterable style="width:100%;"></el-cascader>
          </el-form-item>
          <el-form-item><el-button type="primary" @click="search">查询</el-button></el-form-item>
          <el-form-item><el-button type="primary" @click="exportData">导出</el-button></el-form-item>
        </el-form>
        <el-row :gutter="10">
          <el-col :span="1.5">
            <span>热门城市：</span>
          </el-col>
          <el-col :span="1.5">
            <el-checkbox-group v-model="checkedCities">
              <el-checkbox v-for="city in popularCityList" @change="search" :label="city.code" :key="city.code">{{
                city.name }}</el-checkbox>
            </el-checkbox-group>
          </el-col>
        </el-row>
      </div>
    </template>
    <div class="body_hang">
        <span v-for="item in dataList">
          <div style="font-weight: bolder;">
            <el-row :gutter="10">
              <el-col :span="1.5">
                <span>{{ item.cityName }}</span>
              </el-col>
              <el-col :span="1.5">
                <span id="redcolor">表格展示数据：未来时间为预测天气，过去时间为真实天气</span>
              </el-col>
            </el-row>
          </div>

          <div class="card-container">

            <el-card shadow="never" class="card-item" style="position: relative;"
              v-for="(weatherForecast, index) in item.weatherForecastList">

              <template v-if="weatherForecast.date < currentDate">
                <el-popover placement="right" popper-class="popover" trigger="hover">
                  <div slot="reference" style="position: relative;text-align: center;  width: 150px; height: 200px;">
                    <div style="position: absolute; top: 0; left: 0; z-index: 100; width: 150px; height: 200px;">
                      <div style="text-align: center;font-size: 14px;font-weight: bold;padding: 5px;">{{
                        weatherForecast.dayOfWeek }}</div>
                      <div style="text-align: center;font-size: 14px;font-weight: bold;padding: 5px;">{{
                        weatherForecast.date }}</div>
                      <div style="text-align: center;font-size: 14px;padding: 5px;">{{ weatherForecast.weaDay }}-{{
                        weatherForecast.weaNight }}</div>
                      <div
                        style="text-align: center;font-size: 14px;padding-top: 20px; display: flex; justify-content: center; width: 130px;">
                        {{ weatherForecast.winDay }}-{{ weatherForecast.winNight }}</div>
                      <div style="text-align: center;font-size: 14px;padding: 5px;">{{ weatherForecast.winSpeedDay }}-{{
                        weatherForecast.winSpeedNight }}</div>
                      <div style="text-align: center;font-size: 14px;padding: 5px;">{{ weatherForecast.temDay }}~{{
                        weatherForecast.temNight }}</div>
                      <div style="position: absolute; top: 5px; right: 25px;"
                        v-show="weatherForecast.date == currentDate">
                        <div
                          style="border-radius: 25px; height: 25px; width: 25px; background-color: red; color: white; display: flex; justify-content: center; align-items: center;">
                          今
                        </div>
                      </div>
                    </div>
                    <div
                      style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 99;">
                      <div id="largeNumber">
                        {{ weatherForecast.date.substring(6, 7) }}
                      </div>
                    </div>
                  </div>
                  <div style="width: 150px; height: 210px;" v-if="weatherForecast.date < currentDate">
                    <div style="text-align: left;font-size: 14px;font-weight: bold;padding: 5px;">预测天气</div>
                    <div style="text-align: center;font-size: 14px;font-weight: bold;padding: 5px;">{{
                      weatherForecast.realForecast.dayOfWeek }}</div>
                    <div style="text-align: center;font-size: 14px;font-weight: bold;padding: 5px;">{{
                      weatherForecast.realForecast.date }}</div>
                    <div style="text-align: center;font-size: 14px;padding: 5px;">{{ weatherForecast.realForecast.weaDay
                      }}-{{
                        weatherForecast.realForecast.weaNight }}</div>
                    <div style="text-align: center;font-size: 14px;padding-top: 20px;">{{
                      weatherForecast.realForecast.winDay }}-{{
                        weatherForecast.realForecast.winNight }}</div>
                    <div style="text-align: center;font-size: 14px;padding: 5px;">{{
                      weatherForecast.realForecast.winSpeedDay }}-{{
                        weatherForecast.realForecast.winSpeedNight }}</div>
                    <div style="text-align: center;font-size: 14px;padding: 5px;">{{ weatherForecast.realForecast.temDay
                      }}~{{
                        weatherForecast.realForecast.temNight }}</div>
                  </div>
                </el-popover>
              </template>
              <template v-else>

                <div style="position: relative;text-align: center;  width: 150px; height: 200px;">
                  <div style="position: absolute;text-align: center;width: 150px;z-index: 100; height: 200px;">
                    <div style="text-align: center;font-size: 14px;font-weight: bold;z-index: 100; padding: 5px;">{{
                      weatherForecast.dayOfWeek }}</div>
                    <div style="text-align: center;font-size: 14px;font-weight: bold;padding: 5px;">{{
                      weatherForecast.date }}</div>
                    <div style="text-align: center;font-size: 14px;padding: 5px;">{{ weatherForecast.weaDay }}-{{
                      weatherForecast.weaNight }}</div>
                    <div
                      style="text-align: center;font-size: 14px;padding-top: 20px; display: flex; justify-content: center; width: 130px;">
                      {{ weatherForecast.winDay }}-{{ weatherForecast.winNight }}</div>
                    <div style="text-align: center;font-size: 14px;padding: 5px;">{{ weatherForecast.winSpeedDay }}-{{
                      weatherForecast.winSpeedNight }}</div>
                    <div style="text-align: center;font-size: 14px;padding: 5px;">{{ weatherForecast.temDay }}~{{
                      weatherForecast.temNight }}</div>
                    <div style="position: absolute; top: 5px; right: 25px;"
                      v-show="weatherForecast.date == currentDate">
                      <div
                        style="border-radius: 25px; height: 25px; width: 25px; background-color: red; color: white; display: flex; justify-content: center; align-items: center;">
                        今
                      </div>
                    </div>
                  </div>
                  <div
                    style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 99;  transform: translate(-50%, -50%); z-index: 99;">
                    <div id="largeNumber">
                      {{ weatherForecast.date.substring(6, 7) }}
                    </div>
                  </div>
                </div>
              </template>
            </el-card>
          </div>
        </span>
      </div>
  </my-container>
</template>

<script>
import { getPopularCityList, getProvinceList, getCityListList, queryWeatherData, weatherForecastExport, getProvinceCityList } from "@/api/order/weather";
import MyContainer from '@/components/my-container'

export default {
  name: "aaa",
  components: { MyContainer },
  data() {
    return {
      defaultDialogWidth: "80%",
      dialogWidth: _.cloneDeep(this.defaultDialogWidth),
      popularCityList: [],
      provinceCityList: [],
      provinceCityCodeList: [],
      provinceList: [],
      cityList: [],
      //checkedCities: ['59287', '59493', 'S1003', '57494', '58457', '58349', '59289', 'V8870', '58606', '58238'],
      checkedCities: [],
      dataList: [],
      checkedProvinces: [],
      pageLoading: false,
      isQuery: false,
      currentDate: this.formatDate(),

      query: {
        date: this.getLastDayOfCurrentMonth(),
        province: "",
        city: ""
      },
      params: {
        obtainDate: "",
        cityCodeList: []
      },
      cascaderProps: { multiple: true, label: 'name', value: 'code', children: 'childList' },
      options: [],
    }
  },
  created() {
    this.listPopularCity();
    this.listProvince();
    this.listProvinceAndCity();
    this.init();
  },

  methods: {

    formatDate() {
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const day = now.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    getLastDayOfCurrentMonth() {
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const day = 1;
      return `${year}-${month}-${day}`;
    },


    init() {
      let queryParams = {
        obtainDate: this.query.date,
        cityCodeList: ['59287', '59493', 'S1003', '57494', '58457', '58349', '59289', 'V8870', '58606', '58238']
      }
      queryWeatherData(queryParams).then(response => {
        if (response.data) {
          this.dataList = response.data;
        }
      });
    },

    exportData() {
      if (!this.isQuery) {
        this.$message.warning('请先查询数据后导出！');
      } else {
        this.pageLoading = true;
        weatherForecastExport(this.params).then(response => {
          this.pageLoading = false;
          const aLink = document.createElement("a");
          var blob = new Blob([response], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', '天气预报导出.xlsx')
          aLink.click()
        });
      }
    },

    search() {
      this.isQuery = true
      this.params = {
        obtainDate: "",
        cityCodeList: []
      }

      if (this.checkedCities !== undefined && this.checkedCities != null && this.checkedCities.length > 0) {
        this.checkedCities.map((cityCode, index) => {
          this.params.cityCodeList.push(cityCode)
        })
      }

      if (this.query.date) {
        this.params.obtainDate = this.query.date
      }

      if (this.query.provinceCityCodeList) {
        this.query.provinceCityCodeList.map((item, index) => {
          this.params.cityCodeList.push(item[1])
        })
      }

      setTimeout(() => {
        console.log("++++++++++++++++", this.params)
        this.queryData(this.params)
      }, 0)
    },

    listPopularCity() {
      getPopularCityList().then(response => {
        if (response.data) {
          this.popularCityList = response.data;
        }
      });
    },

    listProvinceAndCity() {
      getProvinceCityList().then(response => {
        if (response.data) {
          this.provinceCityList = response.data;
          this.options = response.data
        }
      });
    },

    change(val) {
      val.forEach(it => this.params.cityCodeList.push(it.code))
    },

    listProvince() {
      getProvinceList().then(response => {
        if (response.data) {
          this.provinceList = response.data;
        }
      });
    },

    changeQueryProvince(provinceCode) {
      var params = {
        "provinceCode": provinceCode
      }
      getCityListList(params).then(response => {
        if (response.data) {
          this.cityList = response.data;
        }
      });
    },

    queryData(params) {
      queryWeatherData(params).then(response => {
        if (response.data) {
          this.dataList = response.data;
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.like {
  cursor: pointer;
  font-size: 25px;
}

.body_hang {
  height: 73vh;
  width: 100%;
  margin-top: 20px;
  overflow-x: auto;
  overflow-y: auto;

  .item {
    margin-top: 30px;
    margin-right: 45px;
  }

  .card-container {
    display: -webkit-box;
    justify-content: 5px;
    /* 控制卡片之间的间隔 */
    margin: 10px;

    .card-item {
      // width: 180px;
      /* 控制卡片宽度 */
      margin: 0 0px;
      /* 控制卡片外边距 */
      /* 设置边框宽度 */
      border-width: 3px;
      /* 设置边框的样式为实线 */
      border-style: solid;
      /* 设置边框的颜色为渐变色 */
      border-image: rgb(153, 152, 152);
      /* 设置边框的底部边缘圆角 */
      border-radius: 0 0 10px 10px;
      height: 200px;
    }
  }
}

.el-badge__content.is-fixed {
  top: 30px;
  right: 45px;
}

#largeNumber {
  font-size: 100px;
  /* 设置字体大小 */
  font-weight: bold;
  /* 字体加粗 */
  text-align: center;
  /* 文字居中 */
  color: rgb(223, 238, 243);
  /* 文字颜色 */
}

.card-item ::v-deep .el-card__body {
  padding: 0 !important;
}

.el-cascader .el-input .el-input__inner:focus,
.el-cascader .el-input.is-focus .el-input__inner {
  height: 34px; //这里高度根据需求自己设定
}

.el-input--mini .el-input__inner {
  height: 34px;
  line-height: 28px;
}

.el-cascader__tags {
  display: inline-flex;
  margin-right: 60px;
  flex-wrap: nowrap;
}

#redcolor {
  font-size: 12px;
  /* 设置字体大小 */
  font-weight: normal;
  color: red;
  font-style: italic;

}

.datecss ::v-deep .el-input__inner {
  height: 34px !important;
  line-height: 34px !important;
}

::v-deep .el-cascader__search-input {
  margin: 2px 0 2px 1px !important;
  padding-left: 2px;
}

.fatcss ::v-deep .mycontainer[data-v-4471cf9a]{
  height: 76vh; /* 设置元素的高度为视口高度的75% */
  box-sizing: border-box;
}
</style>
