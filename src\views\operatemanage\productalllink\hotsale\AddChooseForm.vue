<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form class="ad-form-query" :model="chooseFormData" ref="chooseForm" @submit.native.prevent
                label-width="100px">
                <el-form-item label="类型" prop="type">
                    <el-select v-model="chooseFormData.type" placeholder="类型" @change="typeChange">
                        <el-option v-if="isExceptDept" label="同品跨平台" :value="1"></el-option>
                        <el-option label="老品补SKU" :value="2"></el-option>
                        <el-option v-if="isExceptDept" label="竞品" :value="3"></el-option>
                        <el-option v-if="isExceptDept" label="新品" :value="4"></el-option>
                    </el-select>
                </el-form-item>

                <el-row>
                    <el-col v-show="chooseFormData.type != 2" :span="6">
                        <el-form-item label="竞品平台" prop="platform"
                            :rules="[{ required: true, message: '请选择竞品原平台', trigger: 'blur' }]">
                            <el-select style="float: left; " v-model="chooseFormData.platform" placeholder="请选择竞品原平台"
                                @change="platformChange" :disabled="(chooseFormData.type == 4) || (chooseFormData.type == 1)">
                                <el-option v-for="item in platformlist" v-if="(chooseFormData.type == 4) || (chooseFormData.type!=4 && item.value!=0)" :key="'p2f-' + item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="运营平台" prop="newPlatform"
                            :rules="[{ required: true, message: '请选择要做的平台', trigger: 'blur' }]">
                            <el-select v-model="chooseFormData.newPlatform" placeholder="请选择要做的平台" :disabled="chooseFormData.type == 2">
                                <el-option v-for="item in platformlist" v-if="(item.value!=0)" :key="'np2f-' + item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item v-if="chooseFormData.type != 1 && chooseFormData.type != 2 && chooseFormData.type != 2" label="竞品ID"
                    prop="goodsCompeteId" :rules="[
                    { required: true, message: '请填写竞品ID', trigger: 'blur' },
                    { min: 4, max: 50, message: '长度在 4 到 50 个字符', trigger: 'blur' }]">
                    <el-input v-model.trim="chooseFormData.goodsCompeteId" :maxlength="100" :minlength="4"></el-input>
                </el-form-item>
                <el-form-item v-if="chooseFormData.type != 1 && chooseFormData.type != 2 && chooseFormData.type != 2" label="竞品标题"
                    prop="goodsCompeteName" :rules="[
                    { required: true, message: '请填写竞品标题', trigger: 'blur' },
                    { min: 4, max: 100, message: '长度在 4 到 100 个字符', trigger: 'blur' }]">
                    <el-input v-model.trim="chooseFormData.goodsCompeteName" :maxlength="100" :minlength="4"></el-input>
                </el-form-item>

                <el-form-item v-if="chooseFormData.type === 1 || chooseFormData.type === 2" label="商品来源"
                    prop="chooseGoodsType" >
                    <el-radio-group v-model="chooseFormData.chooseGoodsType" @change="this.chooseGoodsTypeChange">
                        <el-radio  label="1" >商品ID</el-radio>
                        <el-radio  label="2" >已选品</el-radio>
                    </el-radio-group> 
                </el-form-item>
                
                <el-form-item v-if="chooseFormData.type === 1 || chooseFormData.type === 2" :label="chooseFormData.chooseGoodsType == 1 ? '商品ID':'选品Id' "
                    prop="goodsCompeteId" :rules="chooseFormData.chooseGoodsType == 1 ?
                     [ { required: true, message: '请选择商品', trigger: 'blur' }] : [{ required: true, message: '请选择选品', trigger: 'blur' }]">
                    <el-select v-model="chooseFormData.goodsCompeteId" collapse-tags filterable clearable
                        :placeholder="chooseFormData.chooseGoodsType == 1 ? '请选择商品':'请选择选品'" :filter-method="getProductList" style="width: 100%" @change="ProductChange">
                        <el-option v-for="item in productList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="chooseFormData.type === 1 || chooseFormData.type === 2" :label="chooseFormData.chooseGoodsType == 1 ? '商品标题':'选品标题' "
                    prop="goodsCompeteName" :rules="chooseFormData.chooseGoodsType == 1 ?
                    [ { required: true, message: '请填写商品标题', trigger: 'blur' }] : [{ required: true, message: '请填写选品标题', trigger: 'blur' }]">
                    <el-input v-model.trim="chooseFormData.goodsCompeteName" :maxlength="100" :minlength="4"
                        :disabled="chooseFormData.chooseGoodsType == 1 || chooseFormData.type === 2"></el-input>
                </el-form-item>



                <el-form-item v-if="chooseFormData.platform === 0" label="竞品图片" prop="goodsCompeteImgUrl" :rules="[
                { required: true, message: '请选择竞品图片', trigger: 'blur' }]">
                    <yh-img-upload :value.sync="chooseFormData.goodsCompeteImgUrl" />
                </el-form-item>

                <el-form-item label="备注" prop="chooseRemark" :rules="[
                { min: 0, max: 100, message: '长度不能超过100个字符', trigger: 'blur' }]">
                    <el-input v-model.trim="chooseFormData.chooseRemark" :maxlength="100" show-word-limit></el-input>
                </el-form-item>

            </el-form>
        </template>

        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">
                    <el-button v-if="chooseFormData.type != 2" @click="onClose">取消</el-button>
                    <el-button v-if="chooseFormData.type != 2 && this.formEditMode" type="primary"
                        @click="onSave(true)">添加选品</el-button>

                    <el-button v-if="chooseFormData.type === 2" @click="onClose">取消</el-button>
                    <el-button v-if="chooseFormData.type === 2" type="primary" @click="onSave(true)">确定</el-button>
                </el-col>
            </el-row>
        </template>

    </my-container>
</template>
<script>


import { platformlist,formatPlatform } from "@/utils/tools";

import MyContainer from "@/components/my-container";

import YhImgUpload from '@/components/upload/yh-img-upload.vue';

import { getProductsByFilter } from '@/api/operatemanage/base/product' 

import {
    isDoHotSaleGoodsAsync,getHotSaleGoodsByFilter
} from '@/api/operatemanage/productalllink/alllink'

import { getCurUserDepartmentName } from '@/api/operatemanage/base/dingdingShow'

export default {
    name: "AddChooseForm",
    components: { MyContainer, YhImgUpload },
    data() {
        return {
            platformlist: platformlist,
            that: this,
            isExceptDept:false,
            chooseFormData: {
                oldType: 3,
                type: 2,
                newPlatform: null,
                platform: null,
                goodsCompeteId: "",
                goodsId: "",
                goodsName: "",
                goodsCompeteName: "",
                goodsCompeteImgUrl: "",
                chooseRemark: "",
                chooseGoodsType:"1",
                hotSaleGoodsChooseId:null
            },
            pageLoading: false,
            formEditMode: true,//是否编辑模式
            productList: []
        };
    },
    async mounted() { 

        this.platformlist=this.platformlist.filter(f=>f.value!=3);

        let rqs=await getCurUserDepartmentName()
        if(rqs && rqs.success){
            let deptName=rqs.data;
            if(deptName!= null && deptName!=""){
                if(deptName.includes("淘系")){
                    this.chooseFormData.newPlatform = 9;
                }
                else if (deptName.includes("拼多多")){
                    this.chooseFormData.newPlatform = 2;
                }
                else if (deptName.includes("SHEIN")){
                    this.chooseFormData.newPlatform = 12;
                }
                else if (deptName.includes("TEMU")){
                    this.chooseFormData.newPlatform = 13;
                }
                else if (deptName.includes("天猫")){
                    this.chooseFormData.newPlatform = 1;
                }
                else if (deptName.includes("京东")){
                    this.chooseFormData.newPlatform = 7;
                } 
            }
        }
    },
    computed: {

    },
    methods: {
        chooseGoodsTypeChange(v){
            this.chooseFormData.goodsCompeteId = "";
            this.chooseFormData.goodsCompeteName = "";
            this.productList=[];
            this.ProductChange(this.chooseFormData.goodsCompeteId);
        },
        typeChange(v) {
            if ((v == 1 || v == 2) && (this.chooseFormData.oldType == 1 || this.chooseFormData.oldType == 2)) {  
                this.chooseFormData.oldType = v; 
            }
            else if ((v == 3 || v == 4) && (this.chooseFormData.oldType == 3 || this.chooseFormData.oldType == 4)) {
                this.chooseFormData.oldType = v;
            }
            else {
                this.chooseFormData.oldType = v;
                this.chooseFormData.goodsCompeteId = ""
                this.chooseFormData.goodsCompeteName = ""
                this.productList=[];
            } 
            this.ProductChange(this.chooseFormData.goodsCompeteId);
            if (v == 4) {
                this.chooseFormData.platform = 0; 
                this.platformChange(0)
            } else {
                this.chooseFormData.platform = this.chooseFormData.platform == 0 ? null : this.chooseFormData.platform;
                this.chooseFormData.newPlatform = this.chooseFormData.newPlatform == 0 ? null : this.chooseFormData.newPlatform;
            }  
            this.$refs["chooseForm"].clearValidate();
        },
        platformChange(v) { 
            this.chooseFormData.goodsCompeteImgUrl = '';
            if (v == 0 && !this.chooseFormData.goodsCompeteId) {
                this.chooseFormData.goodsCompeteId = 'CJ_' + (new Date().valueOf()).toString();
            }
        },
        onClose() {
            this.$emit('close');
        },
        async getProductList(v) {
            if (v == null || v == "") {
                return;
            }
            this.productList = [];
            if((this.chooseFormData.type == 1 || this.chooseFormData.type == 2) && this.chooseFormData.chooseGoodsType == 2){
                //选择选品
                let reqRlt = await getHotSaleGoodsByFilter({ proCode: v })
                if (reqRlt && reqRlt.success) {
                    this.productList = reqRlt.data.map(item => {
                        return { value: item.id, label: '【' + item.goodsCompeteId + '】【'+ formatPlatform(item.newPlatForm) + '】'+  item.goodsCompeteName  ,
                                  title: item.goodsCompeteName, newPlatform: (item.newPlatForm==0?null:item.newPlatForm), goodsCompeteId:item.goodsCompeteId };
                    });
                }
            }else{
                let reqRlt = await getProductsByFilter({ proCode: v })
                if (reqRlt && reqRlt.success) {
                    this.productList = reqRlt.data.map(item => {
                        return { value: item.proCode, label: '【' + item.proCode + '】' + item.shopName + '【' + item.title + '】', title: item.title, newPlatform: item.platform };
                    });
                }
            }
        },
        ProductChange(value) {
            let obj = null;
            obj = this.productList.find(x => x.value == value);
            if (obj != null) {
                this.chooseFormData.goodsCompeteName = obj.title
                this.chooseFormData.platform=obj.newPlatform
                if(this.chooseFormData.type==2){
                    this.chooseFormData.newPlatform=obj.newPlatform
                }
            }else{
                this.chooseFormData.goodsCompeteName = ""
                if(this.chooseFormData.type==2){
                    this.chooseFormData.newPlatform=null 
                }
                if(this.chooseFormData.type==1 || this.chooseFormData.type==2){
                    this.chooseFormData.platform=null
                }
            }
        },
        async onSave(isClose) {
            if (await this.save()) {
                this.$emit('afterSave');
                if (isClose)
                    this.$emit('close');
            }
        },
        async loadData(val) {
            console.log(val,'val');
            this.isExceptDept = val.isExceptDept
            this.chooseFormData.goodsId = '';
            this.chooseFormData.goodsName = '';
            this.chooseFormData.isDo = true;
            this.chooseFormData.goodsCompeteId = "";
            this.chooseFormData.goodsCompeteName = "";
            this.chooseFormData.platform = null;
            this.chooseFormData.goodsCompeteImgUrl = "";
            this.chooseFormData.chooseRemark = "";
            this.chooseFormData.chooseGoodsType="1";
            if (val.isOut == 'pdd') {
                this.chooseFormData.platform = 2
                this.chooseFormData.newPlatform = 2
                this.chooseFormData.goodsCompeteId = val.goodsId
                this.chooseFormData.goodsCompeteName = val.goodsName
            }
        },
        async save() {
            let that = this;
            this.pageLoading = true;
            
            let saveData = { ...this.chooseFormData };
            //同品跨平台并且是选择的选品，需要重新对goodsCompeteId赋值
            if ((this.chooseFormData.type == 1 || this.chooseFormData.type == 2) && this.chooseFormData.chooseGoodsType == 2) {
                let obj = this.productList.find(x => x.value == this.chooseFormData.goodsCompeteId);
                saveData.hotSaleGoodsChooseId=this.chooseFormData.goodsCompeteId;
                saveData.goodsCompeteId = obj.goodsCompeteId;
            }else{
                saveData.hotSaleGoodsChooseId=null;
            }
            try {
                let valid = await this.$refs["chooseForm"].validate();
                if (valid) {
                    let reqRlt = await isDoHotSaleGoodsAsync(saveData);
                    if (reqRlt && reqRlt.success) {
                        that.$message({ message: '添加到选品成功！', type: "success" });
                    }
                    this.pageLoading = false;
                    return reqRlt && reqRlt.success;
                } else {
                    this.pageLoading = false;
                    return false;
                }
            } catch (error) {
                this.pageLoading = false;
                return false;
            }
        }
    },
};
</script>
