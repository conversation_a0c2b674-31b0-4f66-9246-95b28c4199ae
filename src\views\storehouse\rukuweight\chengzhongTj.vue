<template>
    <MyContainer>
        <template #header>
            <div class="header">
                
                <el-date-picker style="width:230px; margin-right: 10px;" v-model="ListInfo.timerange" type="daterange"
                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" :clearable="false"
                    end-placeholder="结束" ></el-date-picker>
                <el-input placeholder="操作员" v-model="ListInfo.createdUserName" maxlength="50" class="publicMargin"
                    style="width: 120px;" clearable></el-input>
                <!-- <el-date-picker
                v-model="ListInfo.timerange"
                type="datetimerange"
                format="yyyy-MM-dd hh:mm:ss" value-format="yyyy-MM-dd hh:mm:ss"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
                </el-date-picker> -->
                <!-- <el-date-picker style="width:200px; margin-right: 10px;" v-model="ListInfo.timerange" type="daterange"
                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始"
                    end-placeholder="结束" clearable></el-date-picker> -->

                <el-input placeholder="商品编码" v-model="ListInfo.goodsCode" maxlength="50" class="publicMargin"
                    style="width: 120px;" clearable></el-input>
             
                <!-- <el-input placeholder="拍摄人" v-model="ListInfo.photographer" maxlength="50" class="publicMargin"
                    style="width: 120px;" clearable></el-input> -->
                <el-button type="primary" @click="searchList">查询</el-button>
                <el-button @click="showpeizhiVisible">导出</el-button>
                <el-button @click="showpeizhiVisibleDetail">导出明细</el-button>

            </div>

        </template>
        <template>
            <vendorSummaryTj :ListInfo="ListInfo" style="height: 95%; width: 100%;" ref="summaryTable"
                :isHidden="isHidden" />
        </template>

        <!-- 配置 -->
        <el-dialog title="配置" :visible.sync="peizhiVisible" width="20%" v-dialogDrag>
            <peizhi :peizhidata="peizhidata" @closedialog="peizhiVisible = false;" v-if="peizhiVisible"></peizhi>
        </el-dialog>
        <!-- 分组 -->
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
import vendorSummaryTj from "./vendorSummaryTj.vue"
import peizhi from "./peizhi.vue"
import { getAllProBrand } from '@/api/inventory/warehouse'
import dayjs from 'dayjs'
import { formatTime, formatPlatform,downloadLink } from "@/utils/tools";
import { getWarehousingOrderVideoWeightCompareConfig, getMerchandiserList, exportWarehouseWorkload } from '@/api/inventory/purchasequality.js'
export default {
    components: { MyContainer, cesTable, vxetablebase, peizhi, vendorSummaryTj },
    name: "vendorSumIndex",
    data() {
        return {
            that: this,
            peizhidata: {},
            peizhiVisible: false,
            // isWeightNormal: false,
            ListInfo: {
                // isWeightNormal: false,
                timerange: [formatTime(new Date(), "YYYY-MM-DD"), formatTime(new Date(), "YYYY-MM-DD")]
            },
            checkList: [],
            brandList: [],
            btntype: 1,
            isHidden: false,//是否展开
            inventoryList: [],
        };
    },
    provide() {
        return {
            faListInfo: this.ListInfo
        }
    },
    mounted() {
        this.getBrandList()
        this.getInventory()
    },
    methods: {
        //获取采购跟单列表
        async getInventory() {
            const { data } = await getMerchandiserList()
            this.inventoryList = data
            console.log(this.inventoryList, 'this.inventoryList');
        },
        btnchange() {
            this.$refs.summaryTable.changepage(this.isWeightNormal)
            this.isWeightNormal = !this.isWeightNormal;
            this.ListInfo.isWeightNormal = this.isWeightNormal;
            this.searchList();
        },
        async showpeizhiVisible() {
            // this.getpeizhi();
            //导出
            this.loading = true
            this.ListInfo.exportType = 1;
            await exportWarehouseWorkload(this.ListInfo)
            // this.loading = false
            // const aLink = document.createElement("a");
            // let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            // aLink.href = URL.createObjectURL(blob)
            // aLink.setAttribute('download', '仓库工作量统计' + new Date().toLocaleString() + '.xlsx')
            // aLink.click()

        },
        async showpeizhiVisibleDetail() {
            // this.getpeizhi();
            //导出
            this.loading = true
            this.ListInfo.exportType = 2;
            await exportWarehouseWorkload(this.ListInfo)
            // this.loading = false
            // const aLink = document.createElement("a");
            // let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            // aLink.href = URL.createObjectURL(blob)
            // aLink.setAttribute('download', '仓库工作量统计' + new Date().toLocaleString() + '.xlsx')
            // aLink.click()

        },
        async getpeizhi() {
            let res = await getWarehousingOrderVideoWeightCompareConfig();
            if (!res.success) {
                return;
            }
            this.peizhidata = res.data || {};
            setTimeout(() => {
                this.peizhiVisible = true;
            }, 0)
        },
        changeHidden(e) {
            if (e) {
                this.$refs.summaryTable.unfoldTree()
            } else {
                this.$refs.summaryTable.foldTree()
            }
        },
        clear() {
            this.checkList = []
        },
        //获取采购列表
        async getBrandList() {
            const { data, success } = await getAllProBrand()
            if (success) {
                this.brandList = data.map(item => {
                    return {
                        label: item.value,
                        value: item.key
                    }
                })
            } else {
                this.$message.error('获取采购人员列表失败')
            }
        },
        searchList() {
            if (this.ListInfo.timerange && this.ListInfo.timerange.length > 0) {
                this.ListInfo.startDate = this.ListInfo.timerange[0];
                this.ListInfo.endDate = this.ListInfo.timerange[1];
            } else {
                this.ListInfo.startDate = null;
                this.ListInfo.endDate = null;
            }
            setTimeout(() => {
                this.$refs.summaryTable.getAlreadyList(true, this.ListInfo)
            }, 0)
        },
    }
};
</script>

<style lang="scss" scoped>
.header {
    display: flex;
    margin-bottom: 10px;
}

.publicMargin {
    margin-right: 20px;
}

.detail ::v-deep .vxetoolbar20221212 {
    display: none !important;
}

::v-deep .el-badge__content {
    padding: 0 4px;
    top: 7px;
    right: 0;
}

.radioGrp {
    margin-bottom: 10px;
    display: flex;
}

::v-deep .el-checkbox__inner {
    border-radius: 7px !important;
}
</style>
