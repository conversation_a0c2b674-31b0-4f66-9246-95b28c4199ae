<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="导入开始日期" end-placeholder="导入结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" :clearable="false" @change="changeTime($event, 'export')">
                </el-date-picker>
                <el-input v-model="ListInfo.seriesName" placeholder="系列编码" class="publicCss" clearable maxlength="50" />
                <el-input v-model="ListInfo.sku" placeholder="SKUS" class="publicCss" clearable maxlength="50" />
                <!-- <inputYunhan :inputt.sync="ListInfo.goodCode" :maxRows="500" class="publicCss" style="margin: 0 10px 0 0 ;"
                    v-model="ListInfo.goodCode" placeholder="商品编码" :clearable="true" @callback="callbackGoodsCode"
                    title="商品编码" /> -->
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="exportProps" v-throttle="5000">导出</el-button>
            </div>
        </template>
        <vxetablebase :id="'documentReview_summary202408041755_1'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" v-loading="loading" @summaryClick='onsummaryClick'
            style="width: 100%; height: 680px; margin: 0" :showsummary='true' :summaryarry='summaryarry' />
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />

        <!-- 调拨记录 -->
        <el-dialog title="调拨记录" :visible.sync="dialogVisible" width="60%" :before-close="clear" v-dialogDrag>
            <div class="dialogBox_top">
                <el-date-picker v-model="dbTimerange" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" class="publicCss"
                    @change="changeDbTime($event, 'db')">
                </el-date-picker>
                <el-select v-model="dialogQueryInfo.allotWmsCoId" clearable filterable :collapse-tags="true"
                    placeholder="请选择调出仓" style="width: 130px" class="publicCss">
                    <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                        :value="item.wms_co_id" />
                </el-select>
                <el-select v-model="dialogQueryInfo.sendWmsCoId" clearable filterable :collapse-tags="true"
                    placeholder="请选择调入仓" style="width: 130px" class="publicCss">
                    <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                        :value="item.wms_co_id" />
                </el-select>
                <el-button type="primary" @click="openDbDialog(dialogQueryInfo, true)">搜索</el-button>
            </div>
            <vxetablebase :id="'documentReview_summary202408041755_2'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
                @sortchange='sortchange1' :tableData='dialogTableData' :tableCols='dialogTableCols' :isSelection="false"
                :isSelectColumn="false" style="width: 100%; height: 400px; margin: 0" />
            <my-pagination ref="pager" :total="dialogTotal" @page-change="dialogPagechange"
                @size-change="dialogSizechange" />
        </el-dialog>

        <!-- 趋势图 -->
        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false"
            direction="btt">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" @change="chatSearch($event, dialogType)"
                    :picker-options="pickerOptions" style="margin: 10px;" />
                <buschar :analysisData="chatProp.data" v-if="!chatProp.chatLoading"></buschar>
            </div>
            <div v-else v-loading="chatProp.chatLoading"></div>
        </el-drawer>

        <!-- 订单量/缺货次数 -->
        <el-dialog :title="dialogTitle" :visible.sync="ddlOrQhcsDialogVisible" width="60%" :before-close="clear"
            v-dialogDrag>
            <div class="dialogBox_top">
                <el-date-picker v-model="ddlOrQhcsTimerange" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" class="publicCss"
                    @change="changeDbTime($event, 'ddlOrQhcs')">
                </el-date-picker>
            </div>
            <vxetablebase :id="'documentReview_summary202408041755_3'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
                @sortchange='sortchange2' :tableData='ddlOrqhcsTableData' :tableCols='ddlOrqhcsDialogTableCols'
                :isSelection="false" :isSelectColumn="false" style="width: 100%; height: 400px; margin: 0" />
            <my-pagination ref="pager" :total="ddlOrqhcsTotal" @page-change="ddlOrqhcsPagechange"
                @size-change="ddlOrqhcsSizechange" />
        </el-dialog>

        <el-dialog title="配货仓" :visible.sync="distributionVisible" width="40%" v-dialogDrag>
            <distributionTable ref="distributionTable" :distributionProps="distributionProps"
                v-if="distributionVisible" />
        </el-dialog>

        <el-dialog title="组团订单（>=10单）" :visible.sync="groupOrderPageVisible" width="60%" v-dialogDrag>
            <groupOrderPage ref="distributionTable" v-if="groupOrderPageVisible"
                :groupOrderQueryInfo="groupOrderQueryInfo" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, } from '@/utils/tools'
import inputYunhan from "@/components/Comm/inputYunhan";
import { replaceSpace } from '@/utils/getCols'
import distributionTable from './distributionTable.vue'
import { getAllWarehouse } from '@/api/inventory/warehouse'
import groupOrderPage from './groupOrderPage.vue'
import {
    pageGetStatData,
    getStatTrendChart,
    getSummaryStatTrendChart,
    pageGetAllotItem,
    pageGetVoOrder,
    exportStatData,
} from '@/api/vo/VerifyOrder'
import dayjs from 'dayjs'
import buschar from '@/components/Bus/buschar'
const allotStatus = [
    {
        label: '调拨失败',
        value: -1
    },
    {
        label: '等待调拨',
        value: 0
    },
    {
        label: '调拨中',
        value: 1
    },
    {
        label: '调拨成功',
        value: 2
    },
]

const tableCols = [
    // { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', width: '100', align: 'left' },
    { istrue: true, prop: 'clusterWmsLabel', label: '组团仓', width: '260', align: 'left', type: 'click', handle: (that, row) => that.openDistribution(row) },
    // { istrue: true, prop: 'goodsName', align: 'left', label: '商品名称', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'seriesName', label: '系列编码', sortable: 'custom', align: 'left', width: 'auto' },
    { istrue: true, prop: 'sku', label: 'SKUS', sortable: 'custom', align: 'left', width: ' auto', formatter: (row) => row.sku ? row.sku : null },
    { istrue: true, prop: 'orderCount', align: 'center', label: '订单量', sortable: 'custom', width: 'auto', summaryEvent: true, type: 'click', handle: (that, row) => that.openDdlOrQhcsDialog(row, '订单量', false) },
    { istrue: true, prop: 'mobilizeOrderCount', label: '组团订单', sortable: 'custom', width: 'auto', summaryEvent: true, type: 'click', handle: (that, row) => that.opengroupOrder(row) },
    { istrue: true, prop: 'mobilizeOrderRate', label: '组团订单率', sortable: 'custom', width: 'auto', formatter: (row) => row.mobilizeOrderRateStr },
    { istrue: true, prop: 'notMobilizeOrderCount', label: '配货订单', sortable: 'custom', width: 'auto', summaryEvent: true, },
    { istrue: true, prop: 'notMobilizeOrderRate', label: '配货订单率', sortable: 'custom', width: 'auto', formatter: (row) => row.notMobilizeOrderRateStr },
    { istrue: true, align: 'center', prop: 'stockOrderCount', label: '缺货订单', sortable: 'custom', width: 'auto', summaryEvent: true, type: 'click', handle: (that, row) => that.openDdlOrQhcsDialog(row, '缺货次数', false) },
    { istrue: true, align: 'center', prop: 'allotOrderCount', label: '调拨订单', sortable: 'custom', width: 'auto', summaryEvent: true, type: 'click', handle: (that, row) => that.openDbDialog(row, false) },
    // { istrue: true, prop: 'quantity', align: 'center', label: '销量', sortable: 'custom', width: '80', },
    // { istrue: true, prop: 'mobilizeCount', label: '组团数量', sortable: 'custom', width: '100', },
    // { istrue: true, prop: 'mobilizeRate', label: '销量组团率', sortable: 'custom', width: '100', formatter: (row) => row.mobilizeRateStr },
    // { istrue: true, prop: 'notMobilizeCount', label: '配货数量', sortable: 'custom', width: '100', },
    // { istrue: true, prop: 'notMobilizeRate', label: '销量配货率', sortable: 'custom', width: '100', formatter: (row) => row.notMobilizeRateStr },
    // { istrue: true, prop: 'stockCount', label: '缺货数量', sortable: 'custom', width: '100' },
    // { istrue: true, prop: 'allotCount', label: '调拨数量', sortable: 'custom', width: '100' },
    {
        istrue: true, label: '趋势图', width: 'auto', type: 'button', btnList:
            [
                { istrue: true, label: '趋势图', handle: (that, row) => that.openDialog(row) },
            ]
    },
]

const dialogTableCols = [
    { istrue: true, prop: 'orderNo', label: '订单号', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'createdTime', label: '创建时间', sortable: 'custom', width: 'auto', formatter: (row) => dayjs(row.createdTime).format('YYYY-MM-DD') },
    { istrue: true, prop: 'completedTime', label: '完成时间', sortable: 'custom', width: 'auto', formatter: (row) => row.completedTime ? dayjs(row.completedTime).format('YYYY-MM-DD') : null },
    { istrue: true, prop: 'allotWmsCoId', label: '调出仓', sortable: 'custom', width: 'auto', formatter: (row) => row.allotWmsName },
    { istrue: true, prop: 'sendWmsCoId', label: '调入仓', sortable: 'custom', width: 'auto', formatter: (row) => row.sendWmsName },
    { istrue: true, prop: 'allotQty', label: '调拨数量', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'allotResult', label: '调拨状态', sortable: 'custom', width: 'auto', formatter: (row) => allotStatus.find((x) => x.value == row.allotResult).label },
]

const ddlOrqhcsDialogTableCols = [
    // { istrue: true, prop: 'batchNumber', label: '导入批次号', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'orderNoInner', label: '内部订单号', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'importTime', label: '导入时间', sortable: 'custom', width: 'auto', formatter: (row) => dayjs(row.importTime).format('YYYY-MM-DD HH:mm') },
    { istrue: true, prop: 'orderNo', label: '线上订单号', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'timePay', label: '付款日期', sortable: 'custom', width: 'auto', formatter: (row) => dayjs(row.timePay).format('YYYY-MM-DD') },
    { istrue: true, prop: 'weight', label: '订单商品重量', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'seriesName', label: '系列编码', sortable: 'custom', width: 'auto' },
]
export default {
    name: "summaryVue",
    components: {
        MyContainer, vxetablebase, inputYunhan, buschar, distributionTable, groupOrderPage
    },
    data() {
        return {
            that: this,
            allotStatus,//调拨状态
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: null,//开始时间
                endDate: null,//结束时间
                goodsCodes: null,//商品编码
                goodCode: null,//商品编码
                seriesName: null,//系列编码
                sku: null,//sku
            },
            timeRanges: [],//时间范围
            summaryarry: {},//汇总数据
            tableCols,//列表列
            tableData: [],//列表数据
            pickerOptions,//时间选择器配置
            total: 0,//总数
            loading: true,//列表loading
            dialogTableCols,//调拨记录列
            chatProp: {
                chatDialog: false,//趋势图弹窗
                chatTime: null,//趋势图时间
                chatLoading: true,//趋势图loading
                data: [],//趋势图数据
            },
            chatInfo: {
                // seriesName: null,//组别
                goodsCode: null,//姓名
                startDate: null,//店铺
                endDate: null,//昵称
            },
            dialogTableData: [],
            dialogVisible: false,
            dialogQueryInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                seriesName: null,//系列编码
                goodsCode: null,//商品编码
                allotWmsCoId: null,//调出仓
                sendWmsCoId: null,//调入仓
                payStartDate: null,//开始时间
                payEndDate: null,//结束时间
                startDate: null,//开始时间
                endDate: null,//结束时间
                isStock: null,//是否缺货
            },
            dialogTotal: 0,//调拨记录总数
            warehouselist: [],//仓库列表
            dbTimerange: [],//调拨时间
            dialogTitle: null,//弹窗标题
            ddlOrQhcsTimerange: [],//订单量/缺货次数时间
            ddlOrQhcsDialogVisible: false,//订单量/缺货次数弹窗
            ddlOrqhcsTableData: [],//订单量/缺货次数数据
            ddlOrqhcsTotal: 0,//订单量/缺货次数总数
            ddlOrqhcsDialogTableCols,//订单量/缺货次数列
            dialogType: null,//趋势图/汇总趋势图
            distributionVisible: false,//分仓数据弹窗
            distributionProps: null,//分仓数据
            groupOrderPageVisible: false,//组团订单
            groupOrderQueryInfo: {
                startDate: null,//开始时间
                endDate: null,//结束时间
                sku: null,//sku
            }
        };
    },
    async mounted() {
        this.getList()
        var res3 = await getAllWarehouse();
        var warehouselist1 = res3.data.filter((x) => x.name.indexOf('代发') < 0);
        warehouselist1.unshift({ name: "全仓", co_id: 10361546, wms_co_id: 11793337, is_main: false, remark1: null, remark2: null, wms_co_id: -1 });
        this.warehouselist = warehouselist1;
    },
    methods: {
        opengroupOrder(row) {
            if (row.mobilizeOrderCount < 10) return this.$message.error('当前订单不是组团订单')
            this.groupOrderQueryInfo = {
                startDate: this.ListInfo.startDate,
                endDate: this.ListInfo.endDate,
                sku: row.sku
            }
            this.groupOrderPageVisible = true
        },
        openDistribution(row) {
            this.distributionProps = row.wmsesSummary
            this.distributionVisible = true
        },
        async exportProps() {
            const { data } = await exportStatData(this.ListInfo)
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        clear() {
            this.dialogQueryInfo = {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                seriesName: null,//系列编码
                goodsCode: null,//商品编码
                allotWmsCoId: null,//调出仓
                sendWmsCoId: null,//调入仓
                startDate: null,//开始时间
                endDate: null,//结束时间
                isStock: null,//是否缺货
                sku: null,//sku
            }
            this.dialogVisible = false
            this.ddlOrQhcsDialogVisible = false
        },
        changeDbTime(e, type) {
            if (type == 'db') {
                if (e) {
                    this.dialogQueryInfo.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                    this.dialogQueryInfo.endDate = dayjs(e[1]).format('YYYY-MM-DD')
                } else {
                    this.dbTimerange = []
                    this.dialogQueryInfo.startDate = null
                    this.dialogQueryInfo.endDate = null
                }
                this.openDbDialog(this.dialogQueryInfo, true)
            } else {
                if (e) {
                    this.dialogQueryInfo.payStartDate = dayjs(e[0]).format('YYYY-MM-DD')
                    this.dialogQueryInfo.payEndDate = dayjs(e[1]).format('YYYY-MM-DD')
                } else {
                    this.ddlOrQhcsTimerange = []
                    this.dialogQueryInfo.payStartDate = null
                    this.dialogQueryInfo.payEndDate = null
                }
                this.openDdlOrQhcsDialog(this.dialogQueryInfo, this.dialogTitle, true)
            }
        },
        //每页数量改变
        ddlOrqhcsSizechange(val) {
            this.dialogQueryInfo.currentPage = 1;
            this.dialogQueryInfo.pageSize = val;
            this.openDdlOrQhcsDialog(this.dialogQueryInfo, this.dialogTitle, true)
        },
        //当前页改变
        ddlOrqhcsPagechange(val) {
            this.dialogQueryInfo.currentPage = val;
            this.openDdlOrQhcsDialog(this.dialogQueryInfo, this.dialogTitle, true)
        },
        //每页数量改变
        dialogSizechange(val) {
            this.dialogQueryInfo.currentPage = 1;
            this.dialogQueryInfo.pageSize = val;
            this.openDbDialog(this.dialogQueryInfo)
        },
        //当前页改变
        dialogPagechange(val) {
            this.dialogQueryInfo.currentPage = val;
            this.openDbDialog(this.dialogQueryInfo)
        },
        clearQuery() {
            this.dialogQueryInfo = {
                currentPage: 1,
                pageSize: 50,
                orderBy: this.dialogQueryInfo.orderBy ? this.dialogQueryInfo.orderBy : null,
                isAsc: false,
                seriesName: null,//系列编码
                goodsCode: null,//商品编码
                allotWmsCoId: null,//调出仓
                sendWmsCoId: null,//调入仓
                payStartDate: null,//开始时间
                payEndDate: null,//结束时间
                isStock: null,//是否缺货
            }
        },
        publicGetQuery(row, type, isFirst) {
            // this.dialogQueryInfo.seriesName = row.seriesName
            // this.dialogQueryInfo.goodsCode = row.goodsCode
            this.dialogQueryInfo.sku = row.sku
            //dbTimerange默认七天时间
            if (type == 'db' && isFirst) {
                this.dbTimerange = this.timeRanges
                this.dialogQueryInfo.startDate = dayjs(this.dbTimerange[0]).format('YYYY-MM-DD')
                this.dialogQueryInfo.endDate = dayjs(this.dbTimerange[1]).format('YYYY-MM-DD')
                this.dialogQueryInfo.payStartDate = null
                this.dialogQueryInfo.payEndDate = null
            } else {
                this.ddlOrQhcsTimerange = this.timeRanges
                this.dialogQueryInfo.payStartDate = dayjs(this.ddlOrQhcsTimerange[0]).format('YYYY-MM-DD')
                this.dialogQueryInfo.payEndDate = dayjs(this.ddlOrQhcsTimerange[1]).format('YYYY-MM-DD')
                this.dialogQueryInfo.startDate = null
                this.dialogQueryInfo.endDate = null
            }
        },
        async openDdlOrQhcsDialog(row, title, ischange) {
            this.dialogTitle = title
            this.dialogQueryInfo.isStock = title == '缺货次数' ? true : null
            if (!ischange) {
                this.publicGetQuery(row, 'ddlOrQhcs')
            }
            const { data, success } = await pageGetVoOrder(this.dialogQueryInfo)
            if (!success) return
            this.ddlOrqhcsTableData = data.list
            this.ddlOrqhcsTotal = data.total
            this.ddlOrQhcsDialogVisible = true
        },
        async openDbDialog(row, isSearch) {
            if (!isSearch) {
                this.clearQuery()
                this.publicGetQuery(row, 'db', true)
            }
            const { data, success } = await pageGetAllotItem(this.dialogQueryInfo)
            if (!success) return
            this.dialogTableData = data.list
            this.dialogTotal = data.total
            this.dialogVisible = true
        },
        changeTime(e, type) {
            if (e) {
                if (type == 'export') {
                    this.ListInfo.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                    this.ListInfo.endDate = dayjs(e[1]).format('YYYY-MM-DD')
                }
            } else {
                if (type == 'export') {
                    this.ListInfo.startDate = null
                    this.ListInfo.endDate = null
                }
            }
            this.getList()
        },
        async openDialog(row) {
            this.dialogType = '趋势图'
            this.chatProp.chatDialog = true
            this.chatProp.chatTime = [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
            this.chatInfo = {
                // seriesName: row.seriesName,//组别
                // goodsCode: row.goodsCode,//姓名
                sku: row.sku,
                startDate: this.chatProp.chatTime[0],
                endDate: this.chatProp.chatTime[1],
            }
            this.chatProp.chatLoading = true
            const { data } = await getStatTrendChart(this.chatInfo)
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        async chatSearch(e, type) {
            if (type == '趋势图') {
                this.chatInfo.startDate = dayjs(this.chatProp.chatTime[0]).format('YYYY-MM-DD')
                this.chatInfo.endDate = dayjs(this.chatProp.chatTime[1]).format('YYYY-MM-DD')
                this.chatProp.chatLoading = true
                const { data } = await getStatTrendChart(this.chatInfo)
                this.chatProp.data = data
                this.chatProp.chatDialog = true
                this.chatProp.chatLoading = false
            } else {
                this.chatInfo.startDate = dayjs(this.chatProp.chatTime[0]).format('YYYY-MM-DD')
                this.chatInfo.endDate = dayjs(this.chatProp.chatTime[1]).format('YYYY-MM-DD')
                this.chatProp.chatDialog = true
                this.chatProp.chatLoading = true
                const { data } = await getSummaryStatTrendChart(this.chatInfo)
                this.chatProp.data = data
                this.chatProp.chatDialog = true
                this.chatProp.chatLoading = false
            }
        },
        async onsummaryClick() {
            this.dialogType = '汇总趋势图'
            this.chatProp.chatDialog = true
            this.chatProp.chatTime = [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
            this.chatInfo = {
                // seriesName: null,//组别
                // goodsCode: null,//姓名
                startDate: this.chatProp.chatTime[0],
                endDate: this.chatProp.chatTime[1],
            }
            this.chatProp.chatLoading = true
            const { data } = await getSummaryStatTrendChart(this.chatInfo)
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        async callbackGoodsCode(val) {
            this.ListInfo.goodCode = val
        },
        //查询列表
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1;
            }
            if (this.timeRanges.length == 0) {
                //默认给近7天时间
                this.timeRanges = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
                this.ListInfo.startDate = this.timeRanges[0]
                this.ListInfo.endDate = this.timeRanges[1]
            }
            const replaceArr = ['seriesName', 'sku']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            this.ListInfo.goodsCodes = this.ListInfo.goodCode ? this.ListInfo.goodCode.split(',') : null;
            const { data, success } = await pageGetStatData(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            console.log(val, 'val');
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        sortchange1({ order, prop }) {
            if (prop) {
                this.dialogQueryInfo.orderBy = prop
                this.dialogQueryInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.openDbDialog(this.dialogQueryInfo, true)
            }
        },
        sortchange2({ order, prop }) {
            if (prop) {
                this.dialogQueryInfo.orderBy = prop
                this.dialogQueryInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.openDdlOrQhcsDialog(this.dialogQueryInfo, this.dialogTitle, false)
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.top {
    display: flex;
    margin-bottom: 20px;
}

.publicCss {
    width: 200px;
    margin-right: 10px;
}

.dialogBox_top {
    display: flex;
    margin-bottom: 10px;
}
</style>