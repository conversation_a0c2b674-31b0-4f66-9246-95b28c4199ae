<template>
  <div>
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn='false'
        @cellclick='cellclick' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
     </ces-table>

     <el-dialog :visible.sync="fstockgoodsvisible" :modal="false" v-dialogDrag width="70%">
       <fstockgoodscode ref="fstockgoodscode" style="height: 450px"></fstockgoodscode> 
     </el-dialog>
  </div>
</template>

<script>
import {queryFStockRate,queryFStockGoodsCode} from '@/api/inventory/abnormal'
import {upLoadImage} from '@/api/upload/file'
import cesTable from "@/components/Table/table.vue";
import fstockgoodscode from '@/views/inventory/components/fstockgoodscode'
import { formatYesornoBool,formatPurchasePlanError,formatmoney,formatTime,formatNoLink,formatSecondToHour} from "@/utils/tools";
import { ruleExpressComanycode } from '@/utils/formruletools'
import { throttle } from 'throttle-debounce';

const tableCols =[
      {istrue:true,prop:'dateStr',label:'年月', width:'65'},
      {istrue:true,prop:'brandName',label:'采购员', width:'65'},
      {istrue:true,prop:'purchaseCount',label:'当月采购单数', width:'110'},
      {istrue:true,prop:'purchaseGoodsCodeCount',label:'当月采购编码数', width:'120'}, 
      {istrue:true,prop:'fStockCount',label:'当月负库存数', width:'110', type:"html", formatter:(row)=>formatNoLink(row.fStockCount)},
      //{istrue:true,prop:'fStockRate',label:'当月负库存率', width:'120',formatter:(row)=>formatNoLink(row.fStockRate) `${row.fStockRate}%`},
      {istrue:true,prop:'fStockRate',label:'当月负库存率', width:'110',formatter:(row)=>`${row.fStockRate}%` },
     ];
const tableHandles=[];
export default {
  name: 'Roles',
  components: {cesTable,fstockgoodscode},
  data() {
    return {
      that:this,
      formatTime:formatTime,
      filter: {
        goodsCode:null, 
        brandId:null,
        goodsName:null,
        isError:null
      },
      list: [],
      brandlist:[],
      recodelist:[],
      tableCols:tableCols,
      tableHandles:tableHandles,
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      visiblepopover: false,
      listLoading: false, 
      pageLoading: false,
      fstockgoodsvisible:false
    }
  },
  methods: {
    async onSearch(brandid) {
      this.getlist(brandid)
    },
    async getlist(brandid) {
      this.listLoading = true
      const res = await queryFStockRate({brandid:brandid})
      this.listLoading = false
      if (!res?.success)return
      const data = res.data
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
   async cellclick(row, column, cell, event){
     if (column.property=='fStockCount')  
        await this.ongoodsdetail(row.brandId,row.dateStr)
    },
   async ongoodsdetail(brandId,dateStr){
        this.fstockgoodsvisible=true;
        this.$nextTick(() => {
           var year= dateStr.substring(0,4)
           var month= dateStr.substring(5)
           this.$refs.fstockgoodscode.onSearch(brandId,year,month);
        });
    }, 
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    }
  }
}
</script>
