<template>
  <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <el-tabs v-model="activeName" style="height:94%;">
          <el-tab-pane label="系列编码" name="tab0" style="height: 100%;">
              <StyleCode  ref="brushOrderProcess" lazy></StyleCode>
          </el-tab-pane>
          <el-tab-pane label="宝贝ID" name="tab1" style="height: 100%;" lazy>
              <PreCode ref="orderProcess" lazy></PreCode>
          </el-tab-pane>
      </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import StyleCode from "@/views/operatemanage/OperationalMiddleOffice/SalesninthDialog/SalesinthStyleCode";
import PreCode from "@/views/operatemanage/OperationalMiddleOffice/SalesninthDialog/SalesninthProCode";

export default {
  name: "OperationalMiddleOfficeManage",
  components: { MyContainer, StyleCode, PreCode },
  data() {
      return {
          that: this,
          activeName: 'tab0',
          pageLoading: false,
          // filter: {
          // },
          infos: null,
          preInfos: null,
      };
  },
  async mounted() {
      // //根据权限来判断显示哪个tab
      // if (this.checkPermission('refundData')){
      //     this.activeName = 'tab0';
      // }
      // await this.onSearch();
  },
  methods: {
      async onSearch() {
          this.$nextTick(() => {
          })
      },
      showtabsecond(data) {
          this.infos = data;
          this.activeName = "tab0";//售后页面
      },
  },
}
</script>