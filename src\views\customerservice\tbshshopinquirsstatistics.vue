<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='groupinquirsstatisticslist' @select='selectchange' :isSelection='false'
            :tableCols='tableCols' :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.groupNameList" placeholder="分组" style="width:180px;" multiple
                            clearable filterable :collapse-tags="true">
                            <el-option v-for="item in filterGroupList" :key="item" :label="item"
                                :value="item">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.shopCode" placeholder="店铺" style="width:180px;" filterable clearable>
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                :value="item.shopCode">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <datepicker v-model="filter.sdate"></datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
                @get-page="getShopInquirsStatisticsList" />
        </template>
        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="false" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
    getTaoBaoShouHouGroup,
    getTaoBaoShouHouShopEfficiencyList, getTaoBaoShouHouShopEfficiencyChar, exportTaoBaoShouHouShopEfficiencyList
} from '@/api/customerservice/taobaoshouhou'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar';
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
const tableCols = [
    { istrue: true, prop: 'shopName', label: '店名', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'inquirs', label: '咨询人数', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'receiveds', label: '接待人数', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'responseTime', label: '平均响应(秒)', width: '120', sortable: 'custom', formatter: (row) => (row.responseTime).toFixed(2) },
    { istrue: true, prop: 'salesvol', label: '销售额', width: '150', sortable: 'custom', formatter: (row) => (row.salesvol).toFixed(2) },
    { istrue: true, prop: 'satisfaction', label: '客户满意率', width: '120', sortable: 'custom', formatter: (row) => (row.satisfaction).toFixed(2) + "%" },
    { istrue: true, prop: 'lateReceiveds', label: '慢接待人数', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'responseRate', label: '回复率', width: '120', sortable: 'custom', formatter: (row) => (row.responseRate).toFixed(2) + "%" },
    { istrue: true, prop: 'verySatisfied', label: '很满意', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'threeResponseRate', label: '平台3分钟响应率', width: '130', sortable: 'custom', formatter: (row) => (row.threeResponseRate).toFixed(2) + "%" },
    { istrue: true, prop: 'outTimes', label: '出勤人次', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '120', sortable: 'custom' },
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar },
    props:["partInfo"],
    data() {
        return {
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            filter: {
            },
            shopList: [],
            filterGroupList: [],
            userList: [],
            groupList: [],
            groupinquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "inquirs", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            isleavegroup:this.partInfo,//是否离组
        };
    },
    watch:{
        partInfo(){
            console.log(this.partInfo,"店铺效率，监控");
            this.isleavegroup = this.partInfo;
            this.getTaoBaoShouHouGroup();
        }
    },
    async mounted() {
        this.isleavegroup=this.partInfo;
        await this.getTaoBaoShouHouGroup();
        await this.getAllShopList();
    },
    methods: {
        async getTaoBaoShouHouGroup() {
            let groups = await getTaoBaoShouHouGroup({isleavegroup:this.isleavegroup});
             this.filterGroupList=groups.data;
            // if (groups?.success && groups?.data && groups?.data.length > 0) {
            //     groups?.data.forEach(f => {
            //         this.filterGroupList.push({ lable: f, value: f });
            //     });
            // }
        },
        async getAllShopList() {
            let shops = await getAllShopList();
            this.shopList = [];
            shops.data?.forEach(f => {
                if (f.isCalcSettlement && f.shopCode && (f.platform == 1 || f.platform == 4 || f.platform == 9))
                    this.shopList.push(f);
            });
            console.log(this.shopList)
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getShopInquirsStatisticsList();
        },
        getParam() {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            return params;
        },
        async getShopInquirsStatisticsList() {
            let params = this.getParam();
            this.listLoading = true;
            const res = await getTaoBaoShouHouShopEfficiencyList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.groupinquirsstatisticslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async showchart(row) {
            let params = this.getParam();
            params.shopCode = row.shopCode;
            const res = await getTaoBaoShouHouShopEfficiencyChar(params).then(res => {
                if (res) {
                    this.dialogMapVisible.visible = true;
                    this.dialogMapVisible.data = res;
                    this.dialogMapVisible.title = res.title;
                    res.title = "";
                }
            })
            this.dialogMapVisible.visible = true
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await exportTaoBaoShouHouShopEfficiencyList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '淘系店效率统计(售后组)_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
