<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                <el-select v-model="ListInfo.logType" placeholder="类型" class="publicCss" clearable>
                    <el-option v-for="item in typeList" :label="item" :value="item" />
                </el-select>
                <el-input v-model.trim="ListInfo.modifiedUser" placeholder="修改人" maxlength="50" clearable
                    class="publicCss" />
                <el-button type="primary" @click="getList">搜索</el-button>
                <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
            </div>
        </template>
        <vxetablebase :id="'logDetails202408041557'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :toolbarshow="false"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0;height: 400px;" v-loading="loading">
            <template #beforeValue="{ row, index }">
                <div v-if="row.type.includes('图片') || row.type.includes('凭证')">
                    <el-badge :value="row.beforeValue ? row.beforeValue.length : ''" class="imgBadge">
                        <el-image style="width: 50px;height: 50px;" :src="row.beforeValue[0]" fit="fill"
                            :preview-src-list="row.beforeValue" />
                    </el-badge>
                </div> 
                <div v-else-if="row.type.includes('链接')">
                    <a :href="row.beforeValue" style="color: blue;" target="_blank">{{ row.beforeValue }}</a>
                </div>
                <div v-else>{{ row.beforeValue }}</div>
            </template>
            <template #afterValue="{ row, index }">
                <div v-if="row.type.includes('图片') || row.type.includes('凭证')">
                    <el-badge style="width: 50px;height: 50px;" :value="row.afterValue ? row.afterValue.length : ''"
                        class="imgBadge">
                        <el-image :src="row.afterValue[0]" fit="fill" :preview-src-list="row.afterValue" />
                    </el-badge>
                </div>
                <div v-else-if="row.type.includes('链接')">
                    <a :href="row.afterValue" style="color: blue;" target="_blank">{{ row.afterValue }}</a>
                </div>
                <div v-else>{{ row.afterValue }}</div>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getChangeLogType, getVolumeGoodsChangeLogListAsync, exportVolumeGoodsChangeLog } from '@/api/inventory/volumeGoods'
import dayjs from 'dayjs'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'type', label: '类型', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'beforeValue', label: '修改前值', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'afterValue', label: '修改后值', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'modifiedTime', label: '操作时间' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'modifiedUserName', label: '操作人', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase
    },
    props: {
        goodsCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'modifiedTime',
                isAsc: false,
                goodsCode: this.goodsCode,
                logType: null,//类型
                modifiedUser: null,//修改人
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            typeList: []//类型列表
        }
    },
    async mounted() {
        this.init()
        await this.getList()
    },
    methods: {
        async init() {
            const { data } = await getChangeLogType()
            this.typeList = data
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportVolumeGoodsChangeLog(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '日志明细' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async changeTime(e) {
            this.ListInfo.startTime = e ? e[0] : null
            this.ListInfo.endTime = e ? e[1] : null
            await this.getList()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getVolumeGoodsChangeLogListAsync(this.ListInfo)
                if (success) {
                    data.list.forEach(item => {
                        if (item.type.includes('图片') || item.type.includes('凭证')) {
                            item.beforeValue = item.beforeValue ? item.beforeValue.split(',') : []
                            item.afterValue = item.afterValue ? item.afterValue.split(',') : []
                        }
                    })
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.imgBadge {
    top: 10px;
}
</style>
