<template>
  <container>
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='true'
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' :loading="listLoading">
         <!-- <template slot='extentbtn'>
          <el-button-group>
            <el-button style="padding: 0;margin: 0;"><el-input style="width: 100px" v-model="filter1.goodsCode"  placeholder="商品编码"/></el-button>
          </el-button-group>
        </template> -->
      </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
  </container>
</template>
<script>
import {pageGropuWages} from '@/api/financial/wages'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import {formatFeeShareOper,formatTime,formatYesornoBool} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'yearMonth',label:'年月', width:'70',sortable:'custom'},
      {istrue:true,prop:'groupId',label:'运营组', width:'120',sortable:'custom',formatter:(row)=>row.groupName},
      {istrue:true,prop:'amont',label:'金额', width:'100',sortable:'custom'},
      {istrue:true,prop:'shareOperV1',label:'分摊方式', width:'100',sortable:'custom',formatter:(row)=>formatFeeShareOper(row.shareOperV1),tipmesg:'工资月报'},
      {istrue:true,prop:'computeStatusV1',label:'状态', width:'80',sortable:'custom',formatter:(row)=>{return row.computeStatusV1==0?'未计算':'已计算'},tipmesg:'工资月报'},
      {istrue:true,prop:'computeTimeV1',label:'计算时间', width:'145',sortable:'custom',formatter:(row)=>formatTime(row.computeTimeV1,'YYYY-MM-DD HH:mm:ss'),tipmesg:'工资月报'},
      {istrue:true,prop:'shareOperV2',label:'分摊方式 ', width:'100',sortable:'custom',formatter:(row)=>formatFeeShareOper(row.shareOperV2),tipmesg:'参考月报'},
      {istrue:true,prop:'computeStatusV2',label:'状态 ', width:'80',sortable:'custom',formatter:(row)=>{return row.computeStatusV2==0?'未计算':'已计算'},tipmesg:'参考月报'},
      {istrue:true,prop:'computeTimeV2',label:'计算时间 ', width:'145',sortable:'custom',formatter:(row)=>formatTime(row.computeTimeV2,'YYYY-MM-DD HH:mm:ss'),tipmesg:'参考月报'},
      {istrue:true,prop:'createdTime',label:'导入时间', width:'145',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD HH:mm:ss')}
     ];
const tableHandles=[
        {label:"导入", handle:(that)=>that.onimport()},
        {label:"下载导入模板", handle:(that)=>that.ondownloadmb('运营工资模板')},
        {label:"计算分摊", handle:(that)=>that.oncomput()},
        {label:"按月删除", handle:(that)=>that.onbatchDelete()},
        {label:"刷新", handle:(that)=>that.getlist()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, container},
   props:{
       filter: { }
     },
  data() {
    return {
       filter1: {
        goodsCode:null,
       },
      shareFeeType:13,
      that:this,
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"id",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [], 
      listLoading: false,
      pageLoading: false
    }
  },
  mounted() {
     //this.onSearch()
  },
  beforeUpdate() { },
  methods: {
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      this.filter.shareFeeType=this.shareFeeType;
      const params = {...pager, ...this.pager, ... this.filter,...this.filter1}
      this.listLoading = true
      const res = await pageGropuWages(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async onbatchDelete() {
       await this.$emit('ondeleteByBatch',this.shareFeeType);
    },
   async oncomput(){
      this.$emit('onstartcomput',this.shareFeeType);
   },
   async onimport(){
     await this.$emit('onstartImport',this.shareFeeType);
   },
   async ondownloadmb(name){
     await this.$emit('ondownloadmb',name);
   },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
  }
}
</script>
