<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" label-width="100px" label-position="right" :disabled="!formEditMode">
                <el-row >                  
                    <el-col :span="24">                                        
                       竞品ID：{{form.goodsCompeteId}}
                       <br/>
                        竞品标题：{{form.goodsCompeteName}}                       
                    </el-col>
                </el-row>
                
                <el-row>
                    <el-col :span="24">
                   
                          <el-alert v-if="!(fbList && fbList.length>0) "
    title="当前暂无报价信息，以下是询价明细"
    type="info" :closable="false">
  </el-alert>
                        <div :style="'height:'+ tableHeight+'px;'"  v-if="!(fbList && fbList.length>0) ">
                            <!--列表-->
                          
                            <ces-table ref="skuTable" :that='that' :isIndex='false' :hasexpandRight='true' 
                            :hasexpand='true' :tableData='skuTableData' 
                            :tableCols='skuTableCols' :loading="false" :isSelectColumn="false"
                                rowkey="id" >
                            </ces-table>
                        </div>
                        <div style="height:550px;" v-else>
                            <el-collapse v-model="activeName" accordion size="mini">                                
                                <template v-for="(item,index) in fbList">
                                    <el-collapse-item :title="item.createdTime" :name="index+1" :key="index" :class=" (form.winnerFbId&& form.winnerFbId== item.id)? 'x202301051755001success': 'x202301051755001'">
                                        <template slot="title">
                                            {{item.createdTime}}
                                            <span style="margin-left:20px;"> 价格范围：{{item.priceRange}}</span>
                                          
                                        </template>
                                        <table style="width:100%">
                                            <tr>
                                                <td width="200">
                                                    <strong>
                                                        供应商平台：
                                                    </strong>
                                                    {{item.supplierPlatformName}}
                                                </td>
                                                <td>
                                                    <strong>
                                                        供应商名称：
                                                    </strong>
                                                    {{item.supplierName}}
                                                </td>
                                                <td width="70">
                                                     <el-button type="primary" @click="useFb(item)" style="float:right" >采纳</el-button>    
                                                </td>
                                            </tr>
                                            <tr>
                                                <td width="200">
                                                    <strong>
                                                        报价凭证：
                                                    </strong>
                                                    
                                                    <template v-if="item.voucherImgUrls">
                                                        <template v-if="item.voucherImgUrls[0]=='[' && JSON.parse(item.voucherImgUrls).length>1">
                                                            <el-badge :value="JSON.parse(item.voucherImgUrls).length" style="margin-top:10px;margin-right:40px;">
                                                                <el-image v-if="item.voucherImgUrls&& item.voucherImgUrls.length>2" :src="(item.voucherImgUrls[0]=='['?(JSON.parse(item.voucherImgUrls)[0].url):item.voucherImgUrls )" style="max-width: 150px;max-height: 150px;margin:-5px 0px -5px -5px;" :preview-src-list="(item.voucherImgUrls[0]=='['
                                                        ?(()=>{
                                                            let tempArray=JSON.parse(item.voucherImgUrls);
                                                            let tempRltArr=[];
                                                            tempArray.forEach(x=>tempRltArr.push(x.url));
                                                            return tempRltArr
                                                        })()
                                                        :(()=>{
                                                            return [item.voucherImgUrls]
                                                        })()  )">
                                                                </el-image>
                                                            </el-badge>
                                                        </template>
                                                        <template v-else>
                                                            <el-image v-if="item.voucherImgUrls&& item.voucherImgUrls.length>2" :src="(item.voucherImgUrls[0]=='['?(JSON.parse(item.voucherImgUrls)[0].url):item.voucherImgUrls )" style="max-width: 150px;max-height: 150px;margin:-5px 0px -5px -5px;" :preview-src-list="(item.voucherImgUrls[0]=='['
                                                        ?(()=>{
                                                            let tempArray=JSON.parse(item.voucherImgUrls);
                                                            let tempRltArr=[];
                                                            tempArray.forEach(x=>tempRltArr.push(x.url));
                                                            return tempRltArr
                                                        })()
                                                        :(()=>{
                                                            return [item.voucherImgUrls]
                                                        })()  )">
                                                            </el-image>
                                                        </template>
                                                    </template>
                                                </td>
                                                <td  colspan="2" v-if="item.supplierPlatformName=='1688'">
                                                    <strong>
                                                        产品链接：
                                                    </strong>
                                                    {{item.factoryUrl}}
                                                </td>
                                                <td  colspan="2" v-else>
                                                    <strong>
                                                        微信账号：
                                                    </strong>
                                                    {{item.supplierWxNum}}
                                                </td>
                                               
                                            </tr>
                                            <tr>
                                                <td colspan="3">
                                                    <strong>
                                                        备注：
                                                    </strong>
                                                    {{item.fbRemark}}
                                                </td>
                                            </tr>
                                        </table>
                                      
                                        <div style="width:100%;height:300px;">                                            
                                            <ces-table :ref="'fbTable'+index" :that='that' :isIndex='false' :hasexpandRight='true' 
                                            :hasexpand='true' :tableData='item.fbSkuList' 
                                            :tableCols='fbTableCols' :loading="false" :isSelectColumn="false"
                                                rowkey="id" >
                                            </ces-table>                                            
                                        </div>
                                      
                                    </el-collapse-item>
                                </template>
                            </el-collapse>
                        </div>
                    </el-col>
                </el-row>

                
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;padding-top:10px">  
                    <el-button @click="onClose">关闭</el-button>                     
                    <!-- <el-button type="primary" @click="onSave(true)">发起询价&关闭</el-button>                     -->
                </el-col>
            </el-row>
        </template>

    </my-container>
</template>
<script>  


    import cesTable from "@/components/Table/table.vue";
    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    
    import { EnquiryLaunchAsync,GetEnquiryFbInfo ,SetEnquiryFeedbackWinnerBefore    } from '@/api/operatemanage/productalllink/alllink';
      

    import vxetablebase from "@/components/VxeTable/vxetablebase.vue";

    const skuTableCols = [
        { istrue: true, prop: 'skuImgUrl', label: 'SKU图', width: '64', type: 'image' },        
        { istrue: true, prop: 'skuName', label: '规格名称', minwidth: '180'},//, width: '120'
        { istrue: true, prop: 'hopeCostPrice', label: '期望成本价', width: '120'},//, width: '120'
        { istrue: true, prop: 'estimatedQuantity', label: '每次大概进货量', width: '140'},//, width: '120'
 
    ];

    const fbTableCols=[
        ...skuTableCols,
        { istrue: true, prop: 'fbCostPrice', label: '报价', width: '90'}
    ];

    export default {
        name: "EnquiryFbInfoForChoose",
        components: { MyContainer, MyConfirmButton, cesTable,  vxetablebase  },
        data() {
            return {              
                that: this,
                form: {
                   
                },
                skuTableCols: skuTableCols,
                fbTableCols:fbTableCols,
                total: 0,
                skuTableData: [],
                //summaryarry: {},
                pageLoading: false,
                curRow: null,
                formEditMode: true,//是否编辑模式              
                mode:1,
                goodschoiceVisible:false,
                fbList:[]   ,//报价列表
                activeName:1
            };
        },
        async mounted() {

        },
        computed: {
            tableHeight() {
                let rowsCount = 1;
                if (this.skuTableData && this.skuTableData.length > 0) {
                    rowsCount = this.skuTableData.length;                    
                }
                let rowsHeight = (rowsCount + 1) * 40 + 40;
                return rowsHeight > 500 ? 500 : rowsHeight;
            },    
        },
        methods: {  
          
            onClose(){
                this.$emit('close');
            },  
            async onSave(isClose){
                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData({oid}) {
                let mode=2;
              
                this.mode=mode;
                this.formEditMode = mode!=3;
                this.pageLoading = true;

                var rlt=await GetEnquiryFbInfo({chooseId:oid});
                if(rlt && rlt.success){
                    this.form={...rlt.data};
                    this.skuTableData=[...rlt.data.eqList];      
                    this.fbList=   [...rlt.data.fbList];      
                }
                  
                this.pageLoading = false;
                   
            },
            async useFb(fbInfo){
                var self=this;
                
                var rlt=await SetEnquiryFeedbackWinnerBefore({chooseId:self.form.chooseId,fbId:fbInfo.id});
                if(rlt && rlt.success){
                    this.$emit('afterSave',fbInfo);   
                    this.$emit('close');                
                }             
            },
            async save() {                
              
                return true;
            }
        },
    };
</script>

<style scoped>
 .x202301051755001 ::v-deep .el-collapse-item__header{
    background-color: rgb(247,247,247);
    padding-left: 10px;
 }

 .x202301051755001success ::v-deep .el-collapse-item__header{
    background-color: #67C23A;
    padding-left: 10px;
 }
</style>
