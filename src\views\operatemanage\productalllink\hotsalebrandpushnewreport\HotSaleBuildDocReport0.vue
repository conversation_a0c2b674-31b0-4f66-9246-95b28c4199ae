<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
            <div class="top">
                <el-button-group>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.chooseDimension" placeholder="维度" style="width: 150px">
                            <el-option label="选品平台" value="选品平台" />
                            <el-option label="选品小组" value="选品小组" />
                            <el-option label="选品运营" value="选品运营" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.chooseType" placeholder="类型" style="width: 120px" clearable
                            collapse-tags>
                            <el-option label="新品" :value=1 />
                            <el-option label="老品补SKU" :value=2 />
                            <el-option label="代拍" :value=3 />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" :clearable="false"
                            style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.choosePlatforms" placeholder="选品平台" style="width: 150px" clearable
                            multiple collapse-tags>
                            <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.chooseGroupIds" clearable filterable placeholder="选品小组" multiple
                            collapse-tags style="width: 150px">
                            <el-option v-for="item in chooseGroupList" :key="item.groupId" :label="item.groupName"
                                :value="item.groupId" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.chooseUserIds" clearable filterable placeholder="选品人" multiple
                            collapse-tags style="width: 150px">
                            <el-option v-for="item in chooseUserList" :key="item.userId" :label="item.userNickName"
                                :value="item.userId" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.chooseUserRoles" clearable filterable placeholder="职位" multiple
                            collapse-tags style="width: 250px">
                            <el-option v-for="item in chooseUserRoleList" :key="item.createUserRole"
                                :label="item.createUserRole" :value="item.createUserRole" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.chooseUserDeptNames" clearable filterable placeholder="架构" multiple
                            collapse-tags style="width: 250px">
                            <el-option v-for="item in chooseUserDeptNameList" :key="item.createUserDeptName"
                                :label="item.createUserDeptName" :value="item.createUserDeptName" />
                        </el-select>
                    </el-button>

                    <el-button type="primary" @click="onSearch()">查询</el-button>
                </el-button-group>
            </div>
        </template>
        <vxetablebase :id="'HotSaleBuildDocReport0202408041713'" ref="vxetable" :that='that' :isIndex='true'
            :hasexpand='true' :tablefixed='true' :border="true" @sortchange='sortchange' @select='selectchange'
            :tableData='tableData' :tableCols='tableCols' :showsummary='true' :summaryarry='summaryarry'
            :isSelection="false" :isSelectColumn="false" @summaryClick='onsummaryClick' style="width: 100%;  margin: 0"
            v-loading="listLoading" :height="'100%'">
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import datepicker from '@/views/customerservice/datepicker';
import { platformlist, pickerOptions } from '@/utils/tools';
import dayjs from 'dayjs';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetHotSaleBuildDocReportDataPageList, GetHotSaleBuildDocReportSearch
} from '@/api/operatemanage/productalllink/alllink'
import buschar from '@/components/Bus/buschar';
const tableCols = [
    { sortable: 'custom', width: '120', align: 'center', prop: 'newPlatForm', label: '选品平台', formatter: (row) => row.newPlatFormName },
    { sortable: 'custom', width: '120', align: 'center', prop: 'groupId', label: '选品小组', formatter: (row) => row.groupName },
    { sortable: 'custom', width: '100', align: 'center', prop: 'userId', label: '选品运营', formatter: (row) => row.userNickName },
    { sortable: 'custom', width: '200', align: 'center', prop: 'createUserRole', label: '职位', },
    { sortable: 'custom', width: '500', align: 'center', prop: 'createUserDeptName', label: '架构', },
    {
        sortable: 'custom', width: '180', align: 'center', prop: 'docCount', label: '已创建系列编码数量',
        type: 'click', handle: (that, row) => that.JumpDetail(row), summaryEvent: true
    },
    {
        sortable: 'custom', width: '180', align: 'center', prop: 'saleCount', label: '已上架有销量/已进货',
        type: 'click', handle: (that, row) => that.JumpChat2(row), summaryEvent: true
    },

];
export default {
    name: "HotSaleBuildDocReport0",
    components: {
        MyContainer, datepicker, vxetablebase, buschar
    },
    data() {
        return {
            that: this,
            auditVisible: false,
            activities: [],
            timeRanges: [],
            platformlist: platformlist,
            pickerOptions,
            filter: {
                chooseDimension: "选品运营",
                timerange: [(dayjs().subtract(1, 'day').format('YYYY-MM') + '-01'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')],
                docStartDate: null,
                docEndDate: null,
                choosePlatforms: [],
                chooseGroupIds: [],
                chooseUserIds: [],
                chooseUserRoles: [],
                chooseUserDeptNames: [],
            },
            pager: { OrderBy: "docCount", IsAsc: false },
            tableCols: tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            summaryarry: {},

            chooseGroupList: [],
            chooseUserList: [],
            chooseUserRoleList: [],
            chooseUserDeptNameList: [],
        }
    },
    async mounted() {
        await this.getSelectData();
        this.onSearch();
    },
    computed: {
    },
    methods: {
        async getSelectData() {
            let ret = await GetHotSaleBuildDocReportSearch({ type: 1 });
            this.chooseGroupList = ret.data;

            let ret2 = await GetHotSaleBuildDocReportSearch({ type: 2 });
            this.chooseUserList = ret2.data;

            let ret3 = await GetHotSaleBuildDocReportSearch({ type: 3 });
            this.chooseUserRoleList = ret3.data;

            let ret4 = await GetHotSaleBuildDocReportSearch({ type: 4 });
            this.chooseUserDeptNameList = ret4.data;
        },
        async onSearch() {
            this.$nextTick(() => {
                if (this.filter.chooseDimension == "选品运营") {
                    this.$refs.vxetable.$refs.xTable.showColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('groupId'))
                    this.$refs.vxetable.$refs.xTable.showColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('userId'))
                    this.$refs.vxetable.$refs.xTable.showColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('createUserRole'))
                    this.$refs.vxetable.$refs.xTable.showColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('createUserDeptName'))
                }
                else if (this.filter.chooseDimension == "选品小组") {
                    this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('userId'))
                    this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('createUserRole'))
                    this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('createUserDeptName'))
                }
                else if (this.filter.chooseDimension == "选品平台") {
                    this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('groupId'))
                    this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('userId'))
                    this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('createUserRole'))
                    this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('createUserDeptName'))
                }
                this.$refs.pager.setPage(1);
            });
            await this.getList();
        },
        getParam() {
            //选品日期
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.docStartDate = this.filter.timerange[0];
                this.filter.docEndDate = this.filter.timerange[1];
            }
            else {
                this.filter.docStartDate = null;
                this.filter.docEndDate = null;
            }
            let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            const res = await GetHotSaleBuildDocReportDataPageList(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
                this.summaryarry = res.data.summary;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async JumpDetail(row) {
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBuildDocReport1.vue`,
                title: '系列编码',
                args: {
                    chooseType: this.filter.chooseType,
                    choosePlatform: row.newPlatForm,
                    chooseGroupId: row.groupId,
                    chooseUserId: row.userId,
                    choosePlatforms: this.filter.choosePlatforms,
                    chooseGroupIds: this.filter.chooseGroupIds,
                    chooseUserIds: this.filter.chooseUserIds,
                    chooseUserRoles: this.filter.chooseUserRoles,
                    chooseUserDeptNames: this.filter.chooseUserDeptNames,
                    timerange: this.filter.timerange,
                },
                height: '650px',
                width: '1000px',
                callOk: this.afterSave
            });
        },
        afterSave() {

        },
        async onsummaryClick(property) {
            if (property == "docCount") {
                this.$showDialogform({
                    path: `@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBuildDocReport1.vue`,
                    title: '系列编码',
                    args: {
                        chooseType: this.filter.chooseType,
                        choosePlatform: null,
                        chooseGroupId: null,
                        chooseUserId: null,
                        choosePlatforms: this.filter.choosePlatforms,
                        chooseGroupIds: this.filter.chooseGroupIds,
                        chooseUserIds: this.filter.chooseUserIds,
                        chooseUserRoles: this.filter.chooseUserRoles,
                        chooseUserDeptNames: this.filter.chooseUserDeptNames,
                        timerange: this.filter.timerange,
                    },
                    height: '650px',
                    width: '1000px',
                    callOk: this.afterSave
                });
            }
            else if (property == "saleCount") {
                this.$showDialogform({
                    path: `@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBuildDocReport2.vue`,
                    title: '系列编码',
                    args: {
                        chooseType: this.filter.chooseType,
                        choosePlatform: null,
                        chooseGroupId: null,
                        chooseUserId: null,
                        choosePlatforms: this.filter.choosePlatforms,
                        chooseGroupIds: this.filter.chooseGroupIds,
                        chooseUserIds: this.filter.chooseUserIds,
                        chooseUserRoles: this.filter.chooseUserRoles,
                        chooseUserDeptNames: this.filter.chooseUserDeptNames,
                        timerange: this.filter.timerange,
                    },
                    height: '650px',
                    width: '1000px',
                    callOk: this.afterSave2
                });
            }
        },
        async JumpChat2(row) {
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBuildDocReport2.vue`,
                title: '系列编码',
                args: {
                    chooseType: this.filter.chooseType,
                    choosePlatform: row.newPlatForm,
                    chooseGroupId: row.groupId,
                    chooseUserId: row.userId,
                    choosePlatforms: this.filter.choosePlatforms,
                    chooseGroupIds: this.filter.chooseGroupIds,
                    chooseUserIds: this.filter.chooseUserIds,
                    chooseUserRoles: this.filter.chooseUserRoles,
                    chooseUserDeptNames: this.filter.chooseUserDeptNames,
                    timerange: this.filter.timerange,
                },
                height: '650px',
                width: '1000px',
                callOk: this.afterSave2
            });
        },
        afterSave2(buyNo) {
            this.$router.push({ path: '/inventory/purchaseindex', query: { buyNo } })
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}

::v-deep .el-select__tags-text {
    max-width: 120px;
}
</style>
