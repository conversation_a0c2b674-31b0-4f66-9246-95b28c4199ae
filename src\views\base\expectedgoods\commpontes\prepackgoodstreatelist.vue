<template>
  <my-container>
    <ces-table style="height:550px" ref="treateListTable" :that='that' :isIndex='false' :hasexpandRight='true'
      :hasexpand='true' :tableData='list' :tableCols='tableCols' :loading="listLoading" :isSelectColumn="false"
      rowkey="goodsCode">
      <template slot="right">
        <el-table-column width="180" label="加工仓">
          <template slot-scope="scope">
            <!-- <el-select v-model="scope.row.wareHouseId" v-if="selectShow" :disabled="!scope.row.isProcessClaim" @change="changeSelectWarehouse('packcang', scope.row, list.indexOf(scope.row))" >
                          <el-option v-for="op in warehouselist" :label="op.label" :value="op.value" :key="op.value"></el-option>
                      </el-select> -->
            <div style="display: flex;">
              <el-select v-model="scope.row.wareHouseId" v-if="selectShow" :disabled="!scope.row.isCanPackage"
                @change="changeSelectWarehouse('packcang', scope.row, list.indexOf(scope.row))">
                <el-option v-for="op in scope.row.packageWarehouseListsmy" :label="op.label" :value="op.value"
                  :key="op.value"></el-option>
              </el-select>
              <el-button type="text" @click="batchOperate('加工仓', scope.row, scope.$index)"
                :disabled="!scope.row.isCanPackage">批量</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column width="120" label="加工数量">
          <template slot-scope="scope">
            <el-input-number type="number" :precision="0" :disabled="!scope.row.isCanPackage" :min="0" :max="999999"
              :controls="false" v-model.number="scope.row.treateCount" style="width: 90px;">
            </el-input-number>
          </template>
        </el-table-column>

        <el-table-column width="180" label="发货仓">
          <template slot-scope="scope">
            <!-- :disabled="!scope.row.IsCanPackage" -->
            <!-- <el-select v-model="scope.row.sendWarehouseId"  v-if="selectShow" @change="changeSelectWarehouse('sendcang', scope.row, list.indexOf(scope.row))" >
                          <el-option v-for="op in warehouselist" :label="op.label" :value="op.value" :key="op.value"></el-option>
                      </el-select> -->
            <div style="display: flex;">
              <el-select v-model="scope.row.sendWarehouseId" v-if="selectShow" :disabled="!scope.row.isCanPackage"
                @change="changeSelectWarehouse('sendcang', scope.row, list.indexOf(scope.row))">
                <el-option v-for="op in scope.row.sendWarehouseListsmy" :label="op.label" :value="op.value"
                  :key="op.value"></el-option>
              </el-select>
              <el-button type="text" @click="batchOperate('发货仓', scope.row, scope.$index)"
                :disabled="!scope.row.isCanPackage">批量</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column width="120" label="发货仓库存">
          <template slot-scope="scope">
            <el-input-number type="number" :precision="0" disabled :min="0" :max="999999" :controls="false"
              v-model.number="scope.row.sendWarehouseInventory" style="width: 90px;">
            </el-input-number>
          </template>
        </el-table-column>
      </template>

    </ces-table>
    <el-button style="float: right; margin-top: 10px; margin-right: 10px;" type="danger"
      @click="onTreate">一键加工</el-button>
    <el-button style="float: right; margin-top: 10px; margin-right: 10px;" @click="onClose">取消</el-button>
  </my-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import cesTable from "@/components/Table/table.vue";
import { getAllWarehouse } from '@/api/inventory/warehouse'
import {
  //分页查询店铺商品资料
  savePrePackGoodsTreateAsync,
  changePrePackWarehouse,
  changeSendWarehouse
} from "@/api/inventory/prepack.js"


let cols = [
  //{ istrue: true, prop: 'yhGoodsCode', type: "inputtext", label: '商品编码', width: '180', maxlength: 30, isRequired: true },
  { istrue: true, prop: 'goodsCode', label: '商品编码', minwidth: '120', isRequired: true, type: "click", style: (that, row) => that.renderRefundStatus(row) },
  { istrue: true, prop: 'goodsName', label: '商品名称', minwidth: '200', isRequired: true, type: "click", style: (that, row) => that.renderRefundStatus(row) },
  { istrue: true, prop: 'combineDetailGoodsCount', label: '包含子商品数量', minwidth: '90', isRequired: true, type: "click", style: (that, row) => that.renderRefundStatus(row) },
  { istrue: true, prop: 'isSameWareHouse', label: '是否同仓', width: '90', isRequired: true, type: "click", style: (that, row) => that.renderRefundStatus(row) },
  //{ istrue: true, prop: 'isProcessClaim', label: '加工认领', width: '90', isRequired: true, type: "click", style: (that, row) => that.renderRefundStatus(row), formatter: (row) => row.isProcessClaim ? "已认领" : "未认领" },

  { istrue: true, prop: 'prePackCode', label: '预包编码', width: '105', isRequired: true, type: "click", style: (that, row) => that.renderRefundStatus(row) },
  { istrue: true, prop: 'quantity', label: '订单数', width: '90', isRequired: true, type: "click", style: (that, row) => that.renderRefundStatus(row) },
  { istrue: true, prop: 'weekAvgQuantity', label: '7天日均订单数', width: '90', isRequired: true, type: "click", style: (that, row) => that.renderRefundStatus(row) },

  { istrue: true, prop: 'minMasterStock', label: '最小商品编码可售库存', width: '100', isRequired: true, type: "click", style: (that, row) => that.renderRefundStatus(row) },
  { istrue: true, prop: 'minInventoryDay', label: '最小商品编码周转天数', width: '100', isRequired: true, type: "click", style: (that, row) => that.renderRefundStatus(row) }
];


export default {
  name: "prepackgoodstreatelist",
  components: { MyContainer, cesTable },
  props: {
    data: { type: Array, default: () => [] },
  },
  data() {
    return {
      that: this,
      tableCols: cols,
      listLoading: false,
      warehouselist: [],
      list: [],
      selectShow: true,
    };
  },
  async created() {

    let res3 = await getAllWarehouse();
    this.warehouselist = res3.data.filter((x) => x.name.indexOf('代发') < 0);
    this.warehouselist.map((x) => {
      x.value = x.wms_co_id;
      x.label = x.name;
    });
    this.list = this.data;
  },
  methods: {
    closeLoading() {
      this.pageLoading = false;
    },
    batchOperate(type, row, index) {
      if (type == '加工仓' && !row.wareHouseId) return this.$message.error('请选择加工仓');
      if (type == '发货仓' && !row.sendWarehouseId) return this.$message.error('请选择发货仓');
      if (type == '加工仓') {
        this.$confirm('此操作将批量操作加工仓, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.list.forEach((item, i) => {
            if (item.isCanPackage) {
            item.wareHouseId = row.wareHouseId
            item.packageWarehouseListsmy = this.$set(item, 'packageWarehouseListsmy', Array.from(new Set(item.packageWarehouseListsmy.concat(row.packageWarehouseListsmy))))
            }
          })
          this.$emit('updatePage', this.list)
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          });
        });
      } else {
        this.$confirm('此操作将批量操作发货仓, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.list.forEach((item, i) => {
            if (item.isCanPackage) {
              item.sendWarehouseId = row.sendWarehouseId
              item.sendWarehouseListsmy = this.$set(item, 'sendWarehouseListsmy', Array.from(new Set(item.sendWarehouseListsmy.concat(row.sendWarehouseListsmy))))
            }
          })
            this.$emit('updatePage', this.list)
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          });
        });

      }
    },
    //解决下拉不刷新值问题
    async changeSelectWarehouse(val, row, index) {
      if (val == 'packcang') {
        let par = {
          warehouseId: row.wareHouseId,
          childGoodsCodes: row.childGoodsCodes
        }
        const res = await changePrePackWarehouse(par);
        // if (res && res.success) {
        //     await this.$emit("onTreateClose");
        // }
        if (!res.success) {
          return
        }
        this.list.forEach((item, i) => {
          if (i == index) {
            this.list[i].minMasterStock = res.data.minMasterStock;
            this.list[i].minInventoryDay = res.data.minInventoryDay;
          }
        })
      } else if (val == 'sendcang') {
        let par = {
          warehouseId: row.sendWarehouseId,
          prePackCode: row.prePackCode,
          yesterdayQuantity: row.yesterdayQuantity
        }
        const res = await changeSendWarehouse(par);
        if (!res.success) {
          return
        }
        this.list.forEach((item, i) => {
          if (i == index) {
            this.list[i].treateCount = res.data.treateCount;
            this.list[i].sendWarehouseInventory = res.data.sendWarehouseInventory;
          }
        })
        // if (res && res.success) {
        //     await this.$emit("onTreateClose");
        // }
      }
      this.selectShow = false;
      this.selectShow = true;
    },
    renderRefundStatus(row) {
      if (!row.isCanPackage) {
        return "color:#ddd;";
      } else return "";
    },
    async onClose() {
      await this.$emit("onTreateClose");
    },
    // async onTreate(){
    //   let total = this.list.length;
    //   let warningCount = 0;
    //   this.list.forEach(item=>{
    //     if(!item.isCanPackage){
    //         warningCount++;
    //     }
    //   })
    //   if(total == warningCount){
    //     this.$message({ message: '无可生成的数据！', type: "error" });
    //     return;
    //   }

    //   if(warningCount > 0){
    //     this.$confirm('存在未认领数据，未认领数据不会生成加工信息, 是否继续?', '提示', {
    //           confirmButtonText: '确定',
    //           cancelButtonText: '取消',
    //           type: 'warning'
    //       }).then(async _ => {
    //               let par = this.list;
    //               const res = await savePrePackGoodsTreateAsync(par); 
    //               if (res && res.success) {
    //                 this.$message({ message: '生成成功！', type: "success" });
    //                 await this.$emit("onTreateClose");
    //               }
    //       }).catch(_ => { });
    //   }else{
    //     let par = this.list;
    //     const res = await savePrePackGoodsTreateAsync(par); 
    //     if (res && res.success) {
    //         this.$message({ message: '生成成功！', type: "success" });
    //         await this.$emit("onTreateClose");
    //     }
    //   }
    // },
    async onTreate() {
      let total = this.list.length;
      let warningCount = 0;
      this.list.forEach(item => {
        if (!item.prePackCode) {
          warningCount++;
        }
      })
      if (total == warningCount) {
        this.$message({ message: '无可生成的数据！', type: "error" });
        return;
      }

      if (warningCount > 0) {
        this.$confirm('存在无预包编码的数据，无预包编码的数据不会生成加工信息, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async _ => {
          let par = this.list;
          const res = await savePrePackGoodsTreateAsync(par);
          if (res && res.success) {
            this.$message({ message: '生成成功！', type: "success" });
            await this.$emit("onTreateClose");
          }
        }).catch(_ => { });
      } else {
        let par = this.list;
        const res = await savePrePackGoodsTreateAsync(par);
        if (res && res.success) {
          this.$message({ message: '生成成功！', type: "success" });
          await this.$emit("onTreateClose");
        }
      }
    },
  }
}
</script>