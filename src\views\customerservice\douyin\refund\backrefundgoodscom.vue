<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='pagelist' @select='selectchange' :isSelection='false'
            :isSelectColumn='false' :tableCols='tableCols' :loading="listLoading">
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getpageList" />
        </template>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import {
    getBackRefundGoodsComDtl
} from '@/api/customerservice/douyinrefund'

const tableCols = [
    { istrue: true, prop: 'backExpressCom', label: '退货物流公司', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'itemCount', label: '单量', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'itemAmount', label: '金额', width: '160', sortable: 'custom' },
];
export default {
    name: "backrefundgoodscom",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
    data() {
        return {
            that: this,
            pageLoading: false,
            filter: {
                timerange: [],
                startDate: null,
                endDate: null,
                shopCode: null,
                goodsCode: null,
                goodsName: null,
                saleAfterReason: null,
                expressStatus: null,
                batchStr: null,
            },
            shopList: [],
            tableCols: tableCols,
            listLoading: false,
            pagelist: [],
            total: 0,
            summaryarry: {},
            pager: { OrderBy: "", IsAsc: false },
            sels: [], // 列表选中列
            selids: [],
        };
    },
    async mounted() {
    },
    methods: {
        async loadData(args) {
            let t = { ...args.filter };
            t.goodsCode = args.goodsCode;
            t.goodsName = args.goodsName;
            this.filter = t;
            this.onSearch();
        },
        getParam() {
            if (this.filter.timerange && this.filter.timerange.length > 0) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
                this.$message({ message: "请先选择日期！", type: "warning", });
                return;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            let params = {
                ...para,
                ...this.pager,
                ...pager,
            };
            return params;
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getpageList();
        },
        async getpageList() {
            let params = this.getParam();
            console.log(params, 'params');
            this.listLoading = true;
            const res = await getBackRefundGoodsComDtl(params);
            this.listLoading = false;
            this.pagelist = res.data.list;
            this.total = res.data.total
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

.top {
    display: flex;
    margin-bottom: 10px;
}
</style>
