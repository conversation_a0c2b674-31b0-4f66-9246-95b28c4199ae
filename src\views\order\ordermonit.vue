<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
        <el-form-item label="订单时间:">
          <el-date-picker
            v-model="Filter.timerange"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="线上订单号:">
          <el-input v-model="Filter.OrderNo" />
        </el-form-item>
        <el-form-item label="快递单号:">
          <el-input v-model="Filter.ExpressNo" />
        </el-form-item>
        <el-form-item label="快递公司:">
          <el-input v-model="Filter.ExpressCompany" />
        </el-form-item>
        <el-form-item label="店铺名称:">
          <el-input v-model="Filter.ShopName" />
        </el-form-item>
        <el-form-item label="订单状态:">
          <el-input v-model="Filter.Status" />
        </el-form-item>
        <el-form-item label="异常类型:">
          <el-input v-model="Filter.ErrorType" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <el-row>
          <el-alert
              title="重量差额=实际重量-仓库重量;时间差(分钟)=揽收日期-发货日期"
              type="warning"
              show-icon
              :closable="false">
          </el-alert>
        </el-row>
      </el-form>
    </template>
     <ces-table ref="table" :that='that' :isIndex='true' @sortchange='sortchange' :tableData='OrderList'
                :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"></ces-table>
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getOrderList"
      />
    </template>
  </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { pageOrdersMonit,syncNewOrderMonit} from "@/api/order/ordergoods";
const tableCols =[
      {istrue:true,prop:'orderTime',label:'订单日期', width:'150',sortable:'custom'},
      {istrue:true,prop:'orderNo',label:'线上订单号', width:'200',sortable:'custom'},
      {istrue:true,prop:'shopNo',label:'店铺编号', width:'120',sortable:'custom'},
      {istrue:true,prop:'shopName',label:'店铺名称', width:'200',sortable:'custom'},
      {istrue:true,prop:'team',label:'团队', width:'80',sortable:'custom'},
      {istrue:true,prop:'orderTime',label:'下单时间', width:'150',sortable:'custom'},
      {istrue:true,prop:'sendTime',label:'发货日期', width:'150',sortable:'custom'},
      {istrue:true,prop:'expReceiveTime',label:'揽收日期', width:'150',sortable:'custom'},
      {istrue:true,prop:'receiveSendDiff',label:'时间差(分钟)', width:'150',sortable:'custom'},
      {istrue:true,prop:'amont',label:'应付金额', width:'80',sortable:'custom'},
      {istrue:true,prop:'freightMoney',label:'订单运费', width:'80',sortable:'custom'},
      {istrue:true,prop:'status',label:'状态', width:'80',sortable:'custom'},
      {istrue:true,prop:'errorType',label:'异常类型', width:'120',sortable:'custom'},
      {istrue:true,prop:'expressCompany',label:'快递公司', width:'120',sortable:'custom'},
      {istrue:true,prop:'expressNo',label:'快递单号', width:'150',sortable:'custom'},
      {istrue:true,prop:'refundCount',label:'退款数量', width:'80',sortable:'custom'},
      {istrue:true,prop:'goodsCount',label:'数量', width:'80',sortable:'custom'},
      {istrue:true,prop:'amont',label:'订单金额', width:'80',sortable:'custom'},
      {istrue:true,prop:'sjFreightMoney',label:'实收运费', width:'80',sortable:'custom'},
      {istrue:true,prop:'cost',label:'成本', width:'80',sortable:'custom'},
      {istrue:true,prop:'storeWeight',label:'仓库重量', width:'80',sortable:'custom'},
      {istrue:true,prop:'sjWeight',label:'实际重量', width:'80',sortable:'custom'},
      {istrue:true,prop:'diffWeight',label:'重量差额', width:'80',sortable:'custom'},
      {istrue:true,prop:'noStockCount',label:'缺货数量', width:'80',sortable:'custom'},
      {istrue:true,prop:'sendWarehouseStr',label:'发货仓', width:'80',sortable:'custom'},
      {istrue:true,prop:'createdTime',label:'同步时间', width:'150',sortable:'custom'},
    ];
    const tableHandles1=[
        {label:"同步最新数据", handle:(that)=>that.startSync()}
      ];
export default {
  name: "Users",
  components: { cesTable,MyContainer, MyConfirmButton, MySearch, MySearchWindow },
  data() {
    return {
      that:this,
      Filter: {
        StartCreatedTime: "",
        EndCreatedTime: "", 
        timerange: "",
      },
      OrderList: [],
      total: 0,
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      pager:{OrderBy:"id",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
    };
  },
  async mounted() {
    await this.onSearch();
  },
  methods: {
    async startSync(){
      await syncNewOrderMonit({});
       this.$message({
                      message: '后台正在同步...',
                      type: "success",
                    }); 
    },
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getOrderList();
    },
    async getOrderList() {
      const para = {...this.Filter};
      if (para.timerange&&para.timerange.length>0)
        para.StartCreatedTime = para.timerange[0];
      if (para.timerange&&para.timerange.length>1)
        para.EndCreatedTime = para.timerange[1];

      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      this.listLoading = true;
      const res = await pageOrdersMonit(params);
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const data = res.data.list;
      data.forEach((d) => {
        d._loading = false;
      });
      this.OrderList = data;
    },
    // 选择
    onSelsChange(sels) {
      this.sels = sels;
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
