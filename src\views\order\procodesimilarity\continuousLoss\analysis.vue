<template>
  <container>
    <template #header>
      <div>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;">
            年月日：
            <el-date-picker style="width: 200px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" :clearable="false"
              value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"  :picker-options="pickerOptions">
            </el-date-picker>
          </el-button>

          <el-button style="padding: 0;margin: 0;">
            上架时间：
            <el-date-picker style="width: 200px" v-model="filter.timerangetwo" type="daterange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" range-separator="至" start-placeholder="上架时间" end-placeholder="结束日期"  :picker-options="pickerOptions">
            </el-date-picker>
          </el-button>

          

          <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="filter.proCode" style="width: 160px" placeholder="商品ID" clearable :maxlength="200"  />
          </el-button>

          <el-button style="padding: 0;">
            <el-select filterable clearable v-model="filter.platform" placeholder="平台" style="width: 120px"
              @change="onchangeplatform">
              <el-option label="天猫" :value="1"> </el-option>
              <el-option label="拼多多" :value="2"> </el-option>
              <el-option label="阿里巴巴" :value="4"> </el-option>
              <el-option label="抖音" :value="6"> </el-option>
              <el-option label="京东" :value="7"> </el-option>
              <el-option label="淘工厂" :value="8"> </el-option>
              <el-option label="淘宝" :value="9"> </el-option>
              <el-option label="苏宁" :value="10"> </el-option>
            </el-select>
          </el-button>

          <el-button style="padding: 0;">
            <el-select filterable clearable v-model="filter.shopCode" placeholder="店铺名称" style="width: 160px">
              <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
              </el-option>
            </el-select>
          </el-button>

          <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="filter.styleCode" style="width: 160px" placeholder="系列编码" clearable :maxlength="200"  />
          </el-button>


          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组" style="width: 90px">
              <el-option key="无运营组" label="无运营组" :value="0"></el-option>
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" v-if="checkPermission('DayReportGroupSelect')" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.operateSpecialUserId" collapse-tags clearable placeholder="运营专员"
              style="width: 90px">
              <el-option key="无运营专员" label="无运营专员" :value="0"></el-option>
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" v-if="checkPermission('DayReportOperateSpecialUserSelect')" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.operationUserId" collapse-tags clearable placeholder="运营助理" style="width: 90px">
              <el-option key="无运营助理" label="无运营助理" :value="0"></el-option>
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" v-if="checkPermission('DayReportUserIdSelect')" />
            </el-select>
          </el-button>
          <br/>
          <el-button style="padding: 0; ">
            <el-select filterable v-model="filter.searchInterval" @change="searchIntervalChange" collapse-tags clearable placeholder="区间搜索" style="width: 90px">
              <el-option key="操作天数" label="操作天数" :value="1"></el-option>
              <el-option key="订单量" label="订单量" :value="2"></el-option>
              <el-option key="付款金额" label="付款金额" :value="3"></el-option>
              <el-option key="毛三率" label="毛三率" :value="4"></el-option>
              <!-- <el-option key="付费占比" label="付费占比" :value="5"></el-option> -->
             
            </el-select>
          </el-button>

          <el-button style="padding: 0; ">
            <div class="flexrow" style="font-size: 12px;" v-show="filter.searchInterval">
                      <el-input-number placeholder="开始" :min=-99999999 :max=99999999 :precision="2" v-model="filter.startVal" style="width: 140px"></el-input-number>
                      至
                      <el-input-number placeholder="结束" :min=-99999999 :max=99999999 :precision="2" v-model="filter.endVal" style="width: 140px"></el-input-number>
            </div>
          </el-button>

          <el-button type="primary" style="margin-left: 10px; " @click="onSearch">查 询</el-button>

          <!-- <el-button type="primary" style="margin-left: 10px; " @click="analysisForm.visible = true">负利润分析</el-button> -->
        </el-button-group>
      </div>
    </template>
    <ces-table ref="table" :that='that' :isIndex='true' :showsummary='true' :hasexpand='true' :tableData='list' @sortchange='sortchange'
      :tableCols='tableCols' :isSelection="false" :tableHandles='tableHandles' :loading="listLoading"
      :summaryarry="summaryarry"  :isSelectColumn="false" :isvirtual="true">
      <template slot="left">
        <vxe-column title="年月日" field="yearMonthDay" :sortable="true" width="90" fixed="left" align="center"></vxe-column>
        <vxe-column title="平台" field="platformName"  width="90" fixed="left" align="center"></vxe-column>

        <vxe-column title="系列编码" field="styleCode" :sortable="true" width="90" fixed="left" align="center"></vxe-column>

        <vxe-column title="店铺名称" field="shopName" width="90" fixed="left" align="center"></vxe-column>

        <vxe-column title="运营组" field="groupName" width="90" fixed="left" align="center"></vxe-column>

        <vxe-column title="运营专员" field="operateSpecialUserName" width="90" fixed="left" align="center"></vxe-column>

        <vxe-column title="运营助理" field="userName"  width="90" fixed="left" align="center"></vxe-column>

        <vxe-column title="产品ID" field="proCode" :sortable="true" width="140" fixed="left" align="center">
          <template #default="{ row }">
            <div  v-html="formatLinkProCodefuc(row.platform, row.proCode)"></div>
          </template>
        </vxe-column>



        <vxe-column title="亏损原因" width="80" fixed="left" align="center">
          <template #default="{ row }">
             <div style="display: flex; justify-content: center; width: 100%;">
              

              <el-popover style="overflow-y: hidden;" v-if="row.lossesReason" placement="right" trigger="hover"
                width="500">
                <el-card class="box-card">
                    <div style="display: flex; flex-direction: column; max-height: 250px; min-height: 60px; overflow-y: auto;">
                      亏损原因: <span style="margin-left: 10px;">{{row.lossesReason}}</span>
                    </div>
                </el-card>
                <i :class="row.lossesReason ? 'el-icon-message-solid':'el-icon-bell'" slot="reference" :style="row.lossesReason ? {color: 'red'} : {}"></i>
                </el-popover>
                <i v-else :class="row.lossesReason ? 'el-icon-message-solid':'el-icon-bell'" slot="reference" :style="row.lossesReason ? {color: 'red'} : {}"></i>
             </div>
          </template>
        </vxe-column>
        <vxe-column title="解决方案" width="80" fixed="left" align="center">
          <template #default="{ row }">
             <div style="display: flex; justify-content: center; width: 100%;">
              <el-popover style="overflow-y: hidden;" v-if="row.solutionPlan" placement="right" trigger="hover"
                width="500">
                <el-card class="box-card">
                    <div style="display: flex; flex-direction: column; max-height: 250px; min-height: 60px; overflow-y: auto;">
                      解决方案:<span style="margin-left: 10px;">{{row.solutionPlan}}</span>
                    </div>
                </el-card>
                  <i :class="row.solutionPlan ? 'el-icon-message-solid':'el-icon-bell'" slot="reference" :style="row.solutionPlan ? {color: 'red'} : {}"></i>
                </el-popover>
                <i v-else :class="row.solutionPlan ? 'el-icon-message-solid':'el-icon-bell'" slot="reference" :style="row.solutionPlan ? {color: 'red'} : {}"></i>
             </div>
          </template>
        </vxe-column>
      </template>
      <template slot="right">
        <vxe-column title="操作" width="130" fixed="right" align="center">
          <template #default="{ row }">
             <div style="display: flex; justify-content: center; width: 100%;">

              <el-button
              type="text" @click="onAnalysis(row)">分析</el-button>

              <el-button
              type="text" @click="getorder(row)">操作日志</el-button>
             </div>
          </template>
        </vxe-column>
      </template>
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <el-dialog  :visible.sync="todomsg.visible" width="800px" v-dialogDrag>
      <span>
        <template>
          商品ID: {{todomsg.proCode}}
          <div style="display: flex; flex-drirection: row;">
            <div style="flex: 1">亏损原因：<br/>{{todomsg.lossesReason }}</div>
            <div style="flex: 1">解决方案<br/>{{todomsg.solution }}</div>
          </div>
        </template>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="todomsg.visible = false">取消</el-button>
      </span>
    </el-dialog>

    <el-dialog title="操作日志" :visible.sync="orderobj.visible" width="800px" v-dialogDrag>
      <span>
        <template>
          <vxe-table
            height="400px"
            :data="orderobj.orderlist">
            <vxe-column type="seq" width="60"></vxe-column>
            <vxe-column field="createdUserName" title="操作人"></vxe-column>
            <vxe-column field="createdTime" title="操作日期"></vxe-column>
            <vxe-column field="lossesReason" title="亏损原因"></vxe-column>
            <vxe-column field="solution" title="解决方案"></vxe-column>
          </vxe-table>
          <my-pagination ref="reforderpager" :total="ordertotal" :checked-count="ordersels.length" @get-page="getorder" />
        </template>
        
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="orderobj.visible = false">取消</el-button>
      </span>
    </el-dialog>


    <el-dialog title="负利润分析" :visible.sync="analysisForm.visible" width="800px" v-dialogDrag>
      <span>
        <template>
          <el-form class="ad-form-query" :model="analysisForm" ref="analysisForm" @submit.native.prevent
            label-width="120px">
            <el-form-item label="商品ID:" label-width="150px">
              {{ analysisForm.data.proCode }}
            </el-form-item>
            <el-form-item label="商品名称:" label-width="150px">
              {{ analysisForm.data.goodsName }}
            </el-form-item>
            <el-form-item label="时间:" label-width="150px">
              {{ analysisForm.data.yearMonthDay }}
            </el-form-item>
            <el-form-item label="负利润原因:" prop="data.lossesReason" label-width="150px" :rules="[{ required: true, message: '请输入负利润原因', trigger: 'blur' }]" >

              <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 5 }" show-word-limit :maxlength="100" @blur="checkform(analysisForm.data.lossesReason)"
                v-model="analysisForm.data.lossesReason" @input="changetextarea($event)">
              </el-input>
            </el-form-item>
            <el-form-item label="负利润解决方案:" prop="data.solution" label-width="150px" :rules="[{ required: true, message: '请输入负利润解决方案', trigger: 'blur' }]">
 
              <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 5 }"  show-word-limit :maxlength="100" @blur="checkform(analysisForm.data.solution)"
                v-model="analysisForm.data.solution" @input="changetextarea($event)">
              </el-input>
            </el-form-item>
          </el-form>
        </template>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="analysisForm.visible = false">取消</el-button>
        <el-button type="primary" v-throttle="3000" @click="saveAnalysis">保存</el-button>
      </span>
    </el-dialog>
  </container>
</template>

<script>
import container from '@/components/my-container'
import cesTable from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { getContinuousNoProfitAnalysisRecordAsync, saveContinuousNoProfitAnalysisAsync } from "@/api/bookkeeper/continuousprofitanalysis"
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop'
import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'
import {  pickerOptions,formatLinkProCode } from "@/utils/tools";
import { getList as getshop  } from '@/api/operatemanage/base/shop';

import {
  getPlatformsLossesLinkAnalysis,
  getContinuLossesDayReportList,
  editLossReason,
  getLossReason,
  getContinuLossesDayReportOperateLogList
 } from '@/api/operatemanage/continuLosses' //持续亏损

const tableCols = [
  // { istrue: true, prop: 'yearMonthDay', label: '年月日', tipmesg: '', width: '80', sortable: 'custom', fixed: 'left' },
  // //  { istrue: true, prop: 'proCode', label: '平台', tipmesg: '', type: 'html', width: '110', sortable: 'custom', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
  // { istrue: true, prop: 'platform', label: '平台', tipmesg: '', width: '80', sortable: 'custom', formatter: (row) => row.platformName, fixed: 'left' },
  // { istrue: true, prop: 'styleCode', label: '系列编码', tipmesg: '', width: '80', sortable: 'custom', fixed: 'left' },
  // { istrue: true, prop: 'shopName', label: '店铺名称', tipmesg: '', width: '80', sortable: 'custom',  fixed: 'left' },
  // { istrue: true, prop: 'groupName', label: '运营组', tipmesg: '', width: '80', sortable: 'custom', fixed: 'left'  },
  // { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', width: '80', tipmesg: '', sortable: 'custom',  fixed: 'left' },
  // { istrue: true, prop: 'userName', label: '运营助理', tipmesg: '', width: '80', sortable: 'custom', fixed: 'left' },
  // { istrue: true, prop: 'proCode', label: '产品ID', width: '80', tipmesg: '', sortable: 'custom', fixed: 'left' }, 
  // // ======
  // { istrue: true, prop: 'lossesReason', label: '亏损原因', tipmesg: '',  width: '80',  fixed: 'left', type:"click", handle:(that,row,column,cell)=>that.showreason(row, true), formatter: (row) => '点击查看' },
  // { istrue: true, prop: 'solutionPlan', label: '解决方案', tipmesg: '',  width: '80',  fixed: 'left', type:"click", handle:(that,row,column,cell)=>that.showreason(row, true), formatter: (row) => '点击查看' },
  

  { istrue: true, prop: 'onTime', label: '上架时间', width: '100', tipmesg: '', sortable: 'custom' },

  { istrue: true, prop: 'operateDayCount', label: '操作天数', width: '100', tipmesg: '', sortable: 'custom',  },
  { istrue: true, prop: 'continuLossesDayCount', label: '持续亏损天数', tipmesg: '', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'realityProfit3', label: '实际毛三', width: '100', tipmesg: '', sortable: 'custom',  },
  { istrue: true, prop: 'realityProfit3Rate', label: '实际毛三率', width: '100', tipmesg: '', sortable: 'custom', formatter: (row) => row.realityProfit3Rate+'%' },
  { istrue: true, prop: 'invAmountTotal', label: '库存资金', tipmesg: '', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'threeDayZZRate', label: '3天周转天数', width: '160', tipmesg: '', sortable: 'custom' },

  { istrue: true, prop: 'profit3', label: '毛三利润', width: '100', tipmesg: '', sortable: 'custom',  },
  { istrue: true, prop: 'profit3Rate', label: '毛三利率', tipmesg: '', width: '100', sortable: 'custom', formatter: (row) => row.profit3Rate+'%' },
  { istrue: true, prop: 'orderCount', label: '订单量', width: '100', tipmesg: '', sortable: 'custom',  },
  { istrue: true, prop: 'payAmont', label: '付款金额', width: '100', tipmesg: '', sortable: 'custom' },
  { istrue: true, prop: 'saleAmont', label: '销售金额', width: '100', tipmesg: '', sortable: 'custom' },
  { istrue: true, prop: 'advratio', label: '广告占比', width: '100', tipmesg: '', sortable: 'custom', formatter: (row) => row.profit3Rate+'%' },

  { istrue: true, prop: 'allroi', label: '总投产', width: '100', tipmesg: '', sortable: 'custom', },
  { istrue: true, prop: 'freightAvgFee', label: '快递均价', tipmesg: '', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'packageAvgFee', label: '包装均价', width: '100', tipmesg: '', sortable: 'custom',  },
  { istrue: true, prop: 'deductAmount', label: '总退款金额', width: '100', tipmesg: '', sortable: 'custom' },
  { istrue: true, prop: 'deductRate', label: '总退款率', width: '100', tipmesg: '', sortable: 'custom',formatter: (row) => row.deductRate+'%' },
  { istrue: true, prop: 'purchaseFreight', label: '采购成本差价', width: '110', tipmesg: '', sortable: 'custom' },

  { istrue: true, prop: 'profit4', label: '净利润', tipmesg: '', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'profit4Rate', label: '净利率', tipmesg: '', width: '90', sortable: 'custom', formatter: (row) => row.profit4Rate+'%' },
  { istrue: true, prop: 'inCount', label: '出现次数', width: '100', tipmesg: '', sortable: 'custom' },

  // { istrue: true, type: 'button', width: '120', label: '操作', btnList: [
  //   { label: "分析", display: (row) => { return row.isHandle == true; }, handle:(that,row,column,cell)=>that.onAnalysis(row) },
  //   { label: "操作日志", display: (row) => { return row.isHandle == true; }, handle:(that,row,column,cell)=>that.getorder(row) }
  // ] },
]

const tableHandles = [
  //{ label: "导入", handle: (that) => that.startImport() }
];

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
  name: 'ConsecutiveNoProfitAnalysis',
  components: { container, cesTable, YhQuillEditor },
  props: {},
  data() {
    return {
      that: this,
      list: [],
      orderobj: {
        orderlist: [],//操作日志
        visible: false
      },
      
      todomsg: { //操作日志
        visible: false,
        todoData: []
      },
      pickerOptions:pickerOptions,
      pager: { OrderBy: null, IsAsc: false },
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      sels: [],
      ordertotal: 0,
      ordersels: [],
      orderpager: { OrderBy: 'createdTime', IsAsc: false },
      wairow: {},
      uploadLoading: false,
      dialogVisible: false,
      listLoading: false,
      fileList: [],
      summaryarry: {},
      filter: {
        shopCode: null,
        proCode: null,
        groupId: null,
        operateSpecialUserId: null,
        // platform: null,
        // startDate: null,
        // endDate: null,
        timerange: null,
        timerangetwo: null,
        // groupType2: null,
      },
      shopList: [],
      grouplist: [],
      directorlist: [],
      analysisForm: {
        visible: false,
        data: {
          yearMonthDay: null,
          productCode: null,
          goodsName: null,
          lossesReason: null,
          solution: null,
          platform: null
        }
      },
    };
  },

  async mounted() {
    await this.getShopList();
    
    let end = new Date();
    let start = new Date();
    start.setDate(start.getDate() - 7);
    this.filter.timerange = [formatTime(start, "YYYY-MM-DD"), formatTime(end, "YYYY-MM-DD")]

    this.filter.timerangetwo = []

    await this.onSearch();
  },

  methods: {
    checkform(val){
      if(val){
        setTimeout(()=>{
          this.$refs['analysisForm'].clearValidate();
        }, 100)
      }
      
    },
    formatLinkProCodefuc(platform, proCode){
      return formatLinkProCode(platform, proCode)
    },
    searchIntervalChange(val){
      if(!val){
        this.filter.startVal = null;
        this.filter.endVal = null;
      }
    },
    async showreason(row, show){
      // if(show){
      //   this.todomsg.visible = true;
      // }

      // const res = await getLossReason({yearMonthDay: row.yearMonthDay, proCode: row.proCode})
      // if (!res?.success) {
      //   return;
      // }
      this.todomsg.lossesReason = row.lossesReason;
      this.todomsg.solution = row.solution;
      this.todomsg.proCode = row.proCode;
      this.$forceUpdate();
      

    },
    async getorder(row){ //获取操作日志
      this.orderobj.visible = true;
      if(!row){
          row = this.wairow;
        }
        let _this = this;
      this.$nextTick(async() => {
        
        this.wairow = row;
        let pager = _this.$refs.reforderpager.getPager();
        let page = _this.orderpager;
        let params = {
          ...pager, 
          ...page,
          rptDate: row.rptDate,
          proCode: row.proCode,
          yearMonthDay: row.yearMonthDay

        }
        let res = await getContinuLossesDayReportOperateLogList(params);
        if (!res?.success) {
          return
        }
        this.orderobj.orderlist = res.data.list;

        this.ordertotal = res.data.total;
      });
      
    },
    async getShopList() { //获取下拉选择
      const res1 = await getAllShopList();
      this.shopList = [];
      res1.data?.forEach(f => {
        if (f.isCalcSettlement && f.shopCode)
          this.shopList.push(f);
      });
      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });

    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    async getlist() {
      this.filter.startTime = null;
      this.filter.endTime = null;
      // if (this.filter.timerange) {
        this.filter.yearMonthDayStart = this.filter.timerange?this.filter.timerange[0]:null;
        this.filter.yearMonthDayEnd = this.filter.timerange?this.filter.timerange[1]:null;
      // }

      // if (this.filter.timerangetwo) {
        this.filter.onTimeStart = this.filter.timerangetwo?this.filter.timerangetwo[0]:null;
        this.filter.onTimeEnd = this.filter.timerangetwo?this.filter.timerangetwo[1]:null;
      // }

      let pager = this.$refs.pager.getPager();
      let page = this.pager;
      const params = { ...pager, ...page, ... this.filter }
      if (params === false) {
        return;
      }
      this.listLoading = true
      const res = await getContinuLossesDayReportList(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.list = data
      this.summaryarry = res.data.summary;
    },
    // async nSearch() {
    //   await this.getlist()
    // },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    },
    async onAnalysis(row) {
      // await this.showreason(row, false);
      await this.changeupdate(row);
    },
    changeupdate(row){
      this.analysisForm.data = {};
      this.analysisForm.data.yearMonthDay = row.yearMonthDay;
      this.analysisForm.data.proCode = row.proCode;
      this.analysisForm.data.goodsName = row.goodsName;
      this.analysisForm.data.lossesReason = row.lossesReason;
      this.analysisForm.data.solution = row.solutionPlan;
      this.analysisForm.data.platform = row.platform;

      this.analysisForm.data.rptDate = row.rptDate;
      this.analysisForm.visible = true;


    },
    async saveAnalysis() {
      if(!this.analysisForm.data.solution||!this.analysisForm.data.lossesReason){
        this.$message.error("请填写完整！")
        return;
      }
      let params = { 
        ...this.analysisForm.data,
       };
      const res = await editLossReason(params)
      if (!res?.success) {
        this.$message.error("保存失败！")
        return;
      }
      this.$message.success("保存成功！");
      this.analysisForm.visible = false;
      await this.getlist();
    },
    changetextarea() {
      this.$forceUpdate()
    },
    async onchangeplatform(val) {
      this.filter.shopName = ''
      this.filter.platform = val;
      const res1 = await getshop({ platform: val, CurrentPage: 1, PageSize: 10000 });
      this.shopList = res1.data.list;
      this.filter.shopCode = null;
    }
  }
};
</script>

<style lang="scss" scoped></style>