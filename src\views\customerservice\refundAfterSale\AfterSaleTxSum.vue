<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template #header>
        <el-form  class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent ></el-form>
      </template>
      <!--列表-->
      <ces-table      ref="table"
        :that="that"
        :isIndex="true"
        :hasexpand="false"
        @sortchange="sortchange"
        :tableData="pddcontributeinfolist"
        @select="selectchange"
        :isSelection="false"
        :tableCols="tableCols"
        :loading="listLoading"
        :summaryarry='summaryarry'
      >
        <el-table-column type="expand">
          <template slot-scope="props">
            <div>
              <el-table :data="props.row.detaildata" style="width: 100%">
                <el-table-column
                  v-for="col in props.row.detailcols"
                  :prop="col.prop"
                  :label="col.label"
                  :key="col"
                >
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <template slot="extentbtn">
          <el-button-group>
           
              <el-button style="padding: 0;margin: 0;">
               
                <el-date-picker
                    v-model="Filter.timerange"
                    type="monthrange"
                    align="right"
                    unlink-panels
                    range-separator="至"
                    start-placeholder="开始月份"
                    end-placeholder="结束月份"
                    :picker-options="pickerOptions"
                    :clearable="false"
                    >                 
                </el-date-picker>
              </el-button>
            <el-button style="padding: 0;">
                <el-select filterable clearable v-model="Filter.shopCode" placeholder="店铺" style="width: 150px">
                  <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
                  </el-option>
                </el-select>
          </el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
           
          </el-button-group>
        </template>
      </ces-table>
      <!--分页-->
      <template #footer>
        <my-pagination
          ref="pager"
          :total="total"
          :checked-count="sels.length"
          @get-page="getpddcontributeinfoList"
        />
      </template>
    
    </my-container>
  </template>
  <script>
  import { getSaleAfterTxSumList } from '@/api/bookkeeper/reportday'
  import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
  import dayjs from "dayjs";
  import cesTable from "@/components/Table/table.vue";
  import { formatPlatform, formatTime, formatYesornoBool, formatLinkProCode } from "@/utils/tools";
  import { getListByStyleCode } from "@/api/inventory/basicgoods"
  import { importSaleAfterPdd } from '@/api/bookkeeper/import'
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  const tableCols = [
  { istrue: true,  prop: 'yearMonthDay', label: '年月日',  width: '80' },
  { istrue: true,  prop: 'orderCount', label: '总单量', sortable: 'custom', width: '90', formatter: (row) => row.orderCount},
  { istrue: true,  prop: 'refundCountBefore', label: '发货前退款单量', sortable: 'custom', width: '150', formatter: (row) => row.refundCountBefore },
  { istrue: true,  prop: 'refundBeforeRate', label: '发货前退款率' , sortable: 'custom', width: '150', formatter: (row) => !row.refundBeforeRate ? "0" : row.refundBeforeRate.toFixed(2) + '%'},
  { istrue: true,  prop: 'refundCountAfter', label: '发货后退款单量', sortable: 'custom', width: '150', formatter: (row) => row.refundCountAfter },
  { istrue: true,  prop: 'refundAfterRate', label: '发货后退款率', sortable: 'custom', width: '150', formatter: (row) => !row.refundAfterRate ? "0" : row.refundAfterRate.toFixed(2) + '%' },
  { istrue: true,  prop: 'payAmont', label: '支付金额', sortable: 'custom', width: '110',  formatter: (row) => row.payAmont },
  { istrue: true,  prop: 'refundAmontBefore', fix: true, label: '发货前退款金额', width: '150', sortable: 'custom',  },
  { istrue: true,  prop: 'refundBeforeRatio', label: '发货前退款占比',sortable: 'custom', width: '150' ,formatter: (row) => !row.refundBeforeRatio ? "0" : !row.payAmont?"0": row.refundBeforeRatio.toFixed(2) + '%'},
  { istrue: true,  prop: 'refundAmontAfter', label: '发货后退款金额', width: '150', sortable: 'custom',  },
  { istrue: true,  prop: 'refundAfterRatio', label: '发货后退款占比', sortable: 'custom', width: '150',  formatter: (row) => !row.refundAfterRatio ? "0" : !row.payAmont?"0":row.refundAfterRatio.toFixed(2) + '%'},
  ];
  export default {
    name: "Users",
    components: {
      MyContainer,
      MyConfirmButton,
      MySearch,
      MySearchWindow,
      cesTable,
      
    },
    data() {
      return {

        pickerOptions: {
          shortcuts: [{
            text: '本月',
            onClick(picker) {
              let date = new Date();
              date.setDate(1);
              let month = parseInt(date.getMonth() + 1);
              if (month < 10) {
                month = '0' + month
              }
              let start = new Date(date.getFullYear() + '-' + month + '-01');
              let end = new Date(date.getFullYear() + '-' + month + '-01');
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '今年至今',
            onClick(picker) {
              let date = new Date();
              date.setDate(1);
              let month = parseInt(date.getMonth() + 1);
              if (month < 10) {
                month = '0' + month
              }
              let end = new Date(date.getFullYear() + '-' + month + '-01');
              let start = new Date(new Date().getFullYear(), 0);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近六个月',
            onClick(picker) {
              //3-12,取的是9-1~3-1,后台处理加1个月
              let date = new Date();
              date.setDate(1);
              let month = parseInt(date.getMonth() + 1);
              if (month < 10) {
                month = '0' + month
              }
              let start = new Date(date.getFullYear() + '-' + month + '-01');
              let end = new Date(date.getFullYear() + '-' + month + '-01');
              start.setMonth(start.getMonth() - 6);
              picker.$emit('pick', [start, end]);
            }
          }]
        },
        onExportDialogVisible: false,
        searchloading:false,
        // dialogVisible: false,
        importfilter:{
          version:''
        },
        that: this,
        Filter: {
        platform: 2,
        // shopCode: null,
        // proCode: null,
        StartTime: null,
        endTime: null,
        // styleCode: null,
        timerange: null,
        // ExportType: ''
      },
      options: [],
      // styleCode: null,
        shopList: [],
        userList: [],
        groupList: [],
        pddcontributeinfolist: [],
        tableCols: tableCols,
        total: 0,
        summaryarry: {  },
        pager: { OrderBy: "", IsAsc: false },
        sels: [], // 列表选中列
        listLoading: false,
        pageLoading: false,
        selids: [],
        fileList: [],
      };
    },
    async mounted() {},
    async created() {
    await this.init()
    await this.getShopList();
    
  },
    methods: {
      async init() {
      var date1 = new Date();date1.setDate(date1.getDate() );
      var date2 = new Date();date2.setDate(date2.getDate());
      this.Filter.timerange = [];
      this.Filter.timerange[0] = this.datetostr(date1);
      this.Filter.timerange[1] = this.datetostr(date2);
    },
   
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      return y + "-" + m ;
    },
        //系列编码远程搜索
     async remoteMethod(query) {
      if (query !== '') {
        this.searchloading == true
        setTimeout(async () => {
          const res = await getListByStyleCode({ currentPage: 1, pageSize: 50, styleCode: query })
          this.searchloading = false
          res?.data?.forEach(f => {
            this.options.push({ value: f.styleCode, label: f.styleCode })
          });
        }, 200)
      }
      else {
        this.options = []
      }
    },
      async getShopList() {
      const res1 = await getAllShopList({ platforms: [1,9] });
      this.shopList = [];
      res1.data?.forEach(f => {
        if (f.isCalcSettlement && f.shopCode)
          this.shopList.push(f);
      });
      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });

      var res4 = await getAllProBrand();
      this.brandlist = res4.data.map(item => {
        return { value: item.key, label: item.value };
      });
    },
    
      sortchange(column) {
        if (!column.order) this.pager = {};
        else
          this.pager = {
            OrderBy: column.prop,
            IsAsc: column.order.indexOf("descending") == -1 ? true : false,
          };
        this.onSearch();
      },
      
      onSearch() {
        this.$refs.pager.setPage(1);
        this.getpddcontributeinfoList();
      },
      async getpddcontributeinfoList() {
        this.Filter.StartTime = null;
          this.Filter.EndTime = null;
          
        if (this.Filter.timerange) {
          this.Filter.StartTime = formatTime(this.Filter.timerange[0], 'YYYY-MM-DD');
          this.Filter.EndTime = formatTime(this.Filter.timerange[1], 'YYYY-MM-DD');
        }
        const para = { ...this.Filter };
        var pager = this.$refs.pager.getPager();
        const params = {
          ...pager,
          ...this.pager,
          ...para,
        };


        // this.Filter.styleCode = this.styleCode.join()
        this.listLoading = true;
        const res = await getSaleAfterTxSumList(params);
        console.log(res);
        this.listLoading = false;
        console.log(res.data.list);
        this.total = res.data.total;
        this.pddcontributeinfolist = res.data.list;
        this.summaryarry=res.data.summary;
      },
      selectchange: function (rows, row) {
        this.selids = [];
        rows.forEach((f) => {
          this.selids.push(f.id);
        });
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
  