<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="采购开始" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable multiple
                    collapse-tags>
                    <el-option label="待审核" value="待审核" />
                    <el-option label="完成" value="完成" />
                    <el-option label="已确认" value="已确认" />
                    <el-option label="作废" value="作废" />
                </el-select>
                <el-select v-model="ListInfo.isJstSuccess" placeholder="钉钉审批状态" class="publicCss" clearable multiple
                    collapse-tags>
                    <el-option v-for="item in ddStatus" :label="item.label" :value="item.value" />
                </el-select>
                <el-input v-model.trim="ListInfo.buyNo" placeholder="采购单号" maxlength="50" clearable class="publicCss" />
                <inputYunhan :inputt.sync="ListInfo.indexNo" v-model="ListInfo.indexNo" class="publicCss"
                    placeholder="erp编号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300"
                    :maxlength="6000" @callback="indexNoCallback" title="erp编号" style="margin: 0 25px 5px 0;">
                </inputYunhan>
                <inputYunhan :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode" placeholder="商品编码/若输入多条请按回车"
                    :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
                    style="margin: 0 10px 5px 0;" @callback="goodsCodeCallback" title="商品编码">
                </inputYunhan>
                <inputYunhan :inputt.sync="ListInfo.artNo" v-model="ListInfo.artNo" placeholder="货号/若输入多条请按回车"
                    :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000" @callback="artNoCallback"
                    title="货号" class="publicCss" style="margin: 0 25px 5px 0;">
                </inputYunhan>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                    <el-button type="primary" @click="handleAdd">新增</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :tree-config="{}"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <!-- 导入数据 -->
        <el-dialog title="新增数据" :visible.sync="addVisible" width="15%" v-dialogDrag>
            <div>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.indexNoList" v-model="ListInfo.indexNoList"
                    v-if="addVisible" placeholder="ERP编号/若输入多条请按回车" :clearable="true" :clearabletext="true"
                    :maxRows="300" :maxlength="6000" @callback="addCallback" title="ERP编号">
                </inputYunhan>
                <div class="btnGroup">
                    <el-button @click="addVisible = false">取消</el-button>
                    <el-button type="primary" @click="sumbit">确定</el-button>
                </div>
            </div>

        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import {
    getPurchaseOrderExtPage,
    exportPurchaseOrderExt
} from '@/api/inventory/purchase'
import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";
const ddStatus = [
    {
        label: '未提交',
        value: 0
    },
    {
        label: '审核中',
        value: 1
    },
    {
        label: '通过',
        value: 2
    },
    {
        label: '拒绝',
        value: 3
    },
    {
        label: '撤销',
        value: 4
    },
]
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'buyNo', label: '采购单号', treeNode: true },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'indexNo', label: 'erp编号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '状态', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'isJstSuccess', label: '钉钉审批状态', formatter: (row) => ddStatus.find(item => item.value == row.isJstSuccess)?.label },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'purchaseDate', label: '采购日期', },
    { width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
    { width: 'auto', align: 'center', prop: 'artNo', label: '货号', },
    { width: 'auto', align: 'center', prop: 'count', label: '数量', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, inputYunhan
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: null,//开始时间
                endDate: null,//结束时间
                status: [], //状态
                isJstSuccess: [],//钉钉审批状态
                buyNo: null,//采购单号
                indexNo: null,//erp编号
                goodsCode: null,//商品编码
                artNo: null,//货号
                indexNoList: '',//新增数据
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            ddStatus,
            addVisible: false
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        artNoCallback(val) {
            this.ListInfo.artNo = val;
        },
        goodsCodeCallback(val) {
            this.ListInfo.goodsCode = val;
        },
        sumbit() {
            this.$message.success('新增成功')
            this.getList('search')
            this.addVisible = false
        },
        handleAdd() {
            this.ListInfo.indexNoList = null
            this.addVisible = true
        },
        addCallback(val) {
            this.ListInfo.indexNoList = val;
        },
        indexNoCallback(val) {
            this.ListInfo.indexNo = val;
        },
        async changeTime(e) {
            this.ListInfo.startDate = e ? e[0] : null
            this.ListInfo.endDate = e ? e[1] : null
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportPurchaseOrderExt(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '采购信息' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getPurchaseOrderExtPage(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 180px;
        height: 30px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
</style>
