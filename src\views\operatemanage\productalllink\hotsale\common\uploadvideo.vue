<template>
  <el-row v-loading="uploading">
    <el-row>
      <el-col :xs="19" :sm="19" :lg="19">
        <div style="display: flex; flex-direction: row;">
          <el-upload ref="upload" action="#" :auto-upload="true" :multiple="true" :limit="limit" :show-file-list="false"
            :accept="accepttyes" :http-request="UpSuccessload" :on-exceed="exceed" :file-list="retdata"
            :disabled="islook">
            <div style="display: flex; flex-direction: row;">
              <el-button size="mini" type="primary" :disabled="islook" :v-loading="uploading">点击上传<i
                  class="el-icon-upload el-icon--right"></i></el-button>
            </div>
          </el-upload>
          <!-- <el-button style="margin-left: 10px;" size="mini" type="primary" :v-loading="uploading"
            @click="downfileall()">一键下载</el-button> -->
        </div>

      </el-col>

      <el-col :xs="5" :sm="5" :lg="5">
        <span>数量：{{ retdata.length }}</span>
      </el-col>
    </el-row>
    <!-- <div class="contentup" :style="heightpro">
      <el-row>
        <el-col :xs="17" :sm="17" :lg="17">
      <template>
        <div class="smaslling" v-for="(i, index) in retdata" :key="index">
          <video width="auto" height="150px" :src="i.url" loop muted
            @click="imgclick(i.url, retdata[index], index)"></video>
        </div>
      </template>
      </el-col>
        <el-col :xs="4" :sm="4" :lg="4">
        </el-col>
      </el-row>
    </div> -->

    <div class="contentup" :style="heightpro">
      <template>
        <div v-for="(i, index) in retdata" :key="index"
          style="display: inline-block;margin-right: 7px;position: relative;">
          <video width="auto" height="150px" :src="i.url" loop muted @click="imgclick(i.url, retdata[index], index)">
          </video>
          <span class="del" @click="delImg(i, index)" style="position: absolute; top: 0; right: 0;">x</span>
        </div>
      </template>
    </div>

    <!-- <el-image-viewer v-if="showGoodsImage" :initialIndex="imgindex" :url-list="imgList"  :wrapperClosable="false" :on-close="closeFunc" style="z-index:9999;" /> -->
    <!--视频播放-->
    <el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer" :append-to-body="true"
      v-dialogDrag>
      <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeVideoPlyer">关闭</el-button>
      </span>
    </el-dialog>
  </el-row>
</template>
<script>
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
import draggablevue from 'vuedraggable'
import MyContainer from "@/components/my-container";
// import ElImageViewer from './imageviewer.vue';
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
function pause(msec) {
  return new Promise(
    (resolve, reject) => {
      setTimeout(resolve, msec || 500);
    }
  );
}
export default {
  components: { MyContainer, draggablevue, videoplayer },
  props: {
    uploadInfo: {
      type: Array, default: function () {
        return [];
      }
    },
    accepttyes: { type: String, default: ".mp4" },
    uptype: { type: String, default: "other" },
    limit: { type: Number, default: 100000 },
    delfunction: { type: Function, default: null },
    islook: { type: Boolean, default: false },
    contentheight: { type: Object, default: null }
  },
  data() {
    return {
      retdata: [],
      deldata: [],
      uploading: false,
      imgindex: 0,
      icontype: 'primary',
      imgList: [],
      showGoodsImage: false,
      IsChang: false,
      dialogVisible: false,
      videoplayerReload: false,
      heightpro: null
    };
  },
  async mounted() {

    this.deldata = [];
    this.heightpro = this.contentheight ? this.contentheight : { height: '200px' }
  },
  methods: {
    delImg(item, i) {
      this.retdata.splice(i, 1)
    },
    getval(val) {
      this.retdata = val;
    },
    playVideo(videoUrl) {
      this.videoplayerReload = false;
      this.videoplayerReload = true;
      this.dialogVisible = true;
      this.videoUrl = videoUrl;
    },
    async closeVideoPlyer() {
      this.dialogVisible = false;
      this.videoplayerReload = false;
    },
    async downfileall() {
      for (let num in this.retdata) {
        if (this.retdata[num].filestaus == 2) {
          await pause(500);
          await this.downfile(this.retdata[num]);
        }
      }
    },
    dataUpdate() {
      this.IsChang = true;
    },
    getChange() {
      return this.islook ? false : this.IsChang;
    },
    //获取返回值
    getReturns() {
      var curdata = [];
      this.retdata.forEach(function (item) {
        //filestaus 0 新增，1移除，2原来的文件
        curdata.push({
          fileName: item.fileName
          , url: item.url
          , OutComeId: item.outComeId
          , uid: item.uid
          , filestaus: item.filestaus
          , file: item.file,
        })
      });

      this.deldata.forEach(function (item) {
        //filestaus 0 新增，1移除，2原来的文件
        curdata.push({
          fileName: item.fileName
          , url: item.url
          , OutComeId: item.outComeId
          , uid: item.uid
          , filestaus: item.filestaus
          , file: item.file,
        })
      });

      return { success: true, data: curdata };
    },
    //下载文件
    async downfile(file) {
      var xhr = new XMLHttpRequest();
      xhr.open('GET', file.url, true);
      xhr.responseType = 'arraybuffer'; // 返回类型blob
      xhr.onload = function () {
        if (xhr.readyState === 4 && xhr.status === 200) {
          let blob = this.response;
          console.log(blob);
          // 转换一个blob链接
          // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，
          // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定
          let downLoadUrl = window.URL.createObjectURL(new Blob([blob], { type: 'video/mp4' }));
          // 视频的type是video/mp4，图片是image/jpeg
          // 01.创建a标签
          let a = document.createElement('a');
          // 02.给a标签的属性download设定名称
          a.download = file.fileName;
          // 03.设置下载的文件名
          a.href = downLoadUrl;
          // 04.对a标签做一个隐藏处理
          a.style.display = 'none';
          // 05.向文档中添加a标签
          document.body.appendChild(a);
          // 06.启动点击事件
          a.click();
          // 07.下载完毕删除此标签
          a.remove();
        };
      };
      xhr.send();
    },
    //移除文件
    async removefile(file) {
      this.IsChang = true;
      //发出父级页面移除请求
      if (this.delfunction) {
        await this.delfunction(file);
      }
      for (let num in this.retdata) {
        if (this.retdata[num].uid == file.uid) {
          //原上传文件，移除到删除列表
          if (this.retdata[num].outComeId > 0) {
            //打算删除标记
            this.retdata[num].filestaus = 1;
            this.deldata.push(this.retdata[num]);
          }
          this.retdata.splice(num, 1)
        }
      }
    },
    //上传超出提出
    exceed() {
      this.$message({ message: "超出上传数量限制", type: "warning" });
    },
    //上传方法
    // async UpSuccessload(file){
    //   console.log(file,'file');
    //   // debugger
    //     this.IsChang =true;
    //     this.uploading = true;
    //     this.retdata.push({
    //         fileName:file.file.name
    //         ,url:""
    //         ,outComeId:0
    //         ,uid:file.file.uid
    //         ,filestaus:0
    //         ,file: file.file
    //     });
    //     this.uploading = false;
    // },
    //上传方法
    async UpSuccessload(item) {
      this.uploadprogressPercentage = 0;
      this.uploading = true;
      this.$emit('uploadFinish', this.uploading);
      await this.AjaxFile(item.file, 0, "");
      this.uploading = false;
      this.$emit('uploadFinish', this.uploading);

    },
    //切片上传
    async AjaxFile(file, i, batchnumber) {
      var name = file.name; //文件名
      var size = file.size; //总大小
      var shardSize = 200 * 1024;//2m
      var shardCount = Math.ceil(size / shardSize); //总片数
      if (i >= shardCount) {
        return;
      }
      //计算每一片的起始与结束位置
      var start = i * shardSize;
      var end = Math.min(size, start + shardSize);
      //构造一个表单，FormData是HTML5新增的
      i = i + 1;
      var form = new FormData();
      form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分
      form.append("batchnumber", batchnumber);
      form.append("fileName", name);
      form.append("total", shardCount); //总片数
      form.append("index", i); //当前是第几片
      try {
        const res = await xMTVideoUploadBlockAsync(form);
        if (res?.success) {
          this.uploadprogressPercentage = (i / shardCount).toFixed(2) * 100;
          if (i == shardCount) {
            res.data.fileName = name;
            res.data.uid = file.uid;
            res.data.upLoadPhotoId = 0;
            this.retdata.push(res.data);
            this.$emit("update:retdatafa", this.retdata);
            // this.imglist.push(res.data.url)
            this.$message.success('上传完成！')
          } else {
            await this.AjaxFile(file, i, res.data);
          }
        } else {
          this.$message({ message: res?.msg, type: "warning" });
        }
      } catch (error) {
        this.uploading = false;
        this.$emit('uploadFinish', this.uploading);
        this.$message.error('上传文件异常！')
        this.$refs.upload.clearFiles();
        this.retdata = [];
      }
    },
    imgclick(e, data, index) {
      // debugger
      //   //判断当前是 图片，还是视频，还是不可预览文件
      //   if(this.uptype =="imgtype" ){
      //       this.imgList = [];
      //       for(let num in this.retdata)
      //       {
      //           this.imgList.push(this.retdata[num].url);
      //       }
      //       this.imgindex=index;
      //       this.showGoodsImage = true;
      //   }
      //   else if(this.uptype =="rartype" ){
      //       //不可预览
      //   }
      //   else if(this.uptype =="psdtype" ){
      //        //不可预览
      //   }
      //   else if(this.uptype =="vediotype" ){
      //视频预览
      this.playVideo(data.url);
      // }
      return;
    },

    //完成表单界面关闭图片
    async closeFunc() {
      this.showGoodsImage = false;
    },
  },
};
</script>
<style  lang="scss" scoped>
.infinite-list-item {
  list-style: none;
  margin: 0;
  padding: 0;
}

.infinite-list-item span {
  width: 50px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.outline {
  width: 370px;
  line-height: 20px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.contentup {
  // height: 250px;
  // width: 80%;
  width: 380px;
  margin-top: 10px;
  overflow: scroll;

  .del {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 20px;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    color: rgb(228, 18, 18);
    text-align: center;
    line-height: 15px;
    cursor: pointer;
  }
}

.flexrow {
  z-index: 999;
  display: flex;
  flex-direction: row;
  font-size: 14px;
  color: rgb(50, 50, 50);
  justify-content: center;
  align-items: center;
}

.flexroww {
  // background-color: red;
  width: 365px;
  display: flex;
  flex-direction: row;
  font-size: 14px;
  color: rgb(50, 50, 50);
  position: relative;
}

.rightflex {
  // width: 100%;
  // background-color: red;
  display: flex;
  justify-content: flex-end;
  margin-left: auto;
}

.rowclass {
  height: 300px;
  min-width: 500px;
  // background-color: green;
}

.hoversign:hover {
  background-color: red;
  cursor: move;
}
</style>
