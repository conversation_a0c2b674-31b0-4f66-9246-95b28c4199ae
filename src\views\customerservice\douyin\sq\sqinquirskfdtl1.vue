<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' style="height:93%;"
            :tableData='inquirslist' @select='selectchange' :isSelection='false' :tableCols='tableCols'
            :loading="listLoading" :summaryarry="summaryarry">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.groupName" placeholder="组" style="width:120px;" clearable disabled
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.sname" placeholder="姓名" style="width:120px;" clearable disabled
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none" v-if="revealing">
                      <el-select v-model.trim="filter.groupType" placeholder="请选择">
                        <el-option label="全部" :value=null ></el-option>
                        <el-option label="售前" :value=0 ></el-option>
                        <el-option label="售后" :value=1 ></el-option>
                      </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model="filter.startDate" style="width:120px;" disabled />至
                        <el-input v-model="filter.endDate" style="width:120px;" disabled />
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getinquirsList" />
        </template>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
    getDouYinInquirsKfPageList
} from '@/api/customerservice/douyininquirs'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
const tableCols = [
    { istrue: true, prop: 'huiHuaId', label: '会话ID', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'sdate', label: '日期', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'snick', label: '昵称', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'huiHuaResult', label: '评价值', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'huiHuaFlag', label: '评价标签', sortable: 'custom' },
    { istrue: true, prop: 'huiHuaType', label: '评价类型', width: '100', sortable: 'custom' },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
    data() {
        return {
            revealing: false,
            date: '',
            that: this,
            filter: {
                inquirsType: null,
                sname: "",
                groupName: "",
                sdate: [],
                endDate: "",
                startDate: "",
                isManYi: "",
                groupType: null,
                shopCode:null,
            },
            shopList: [],
            userList: [],
            inquirslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "sdate", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            fileList: [],
            improtGroupForm: {
            },
            defaultDate: new Date('1970/1/1'),
            uploadLoading: false,
        };
    },
    async mounted() {
        //await this.getAllShopList();
    },
    methods: {
        async dialogOpenAfter(data) {
            this.filter.sdate[0] = data.startDate;
            this.filter.sdate[1] = data.endDate;
            this.filter.startDate = data.startDate;
            this.filter.endDate = data.endDate;
            this.filter.sname = data.sname;
            this.filter.groupName = data.groupName;
            this.filter.isManYi = data.isManYi;
            this.filter.shopCode = data.shopCode;
            this.revealing = true;
            this.onSearch();
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getinquirsList();
        },
        async getinquirsList() {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            this.filter.inquirsType = this.filter.groupType
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            console.log(params)
            this.listLoading = true;
            const res = await getDouYinInquirsKfPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.inquirslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
