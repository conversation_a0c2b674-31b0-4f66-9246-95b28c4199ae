<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="分类名称:">
          <el-input v-model="filter.brandName" placeholder="品牌名称"/>
        </el-form-item>
        <el-form-item label="平台:">
          <el-select v-model="filter.platform" placeholder="请选择">
            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
 
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='false'
              :loading="listLoading">
    </ces-table>
    
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getlist"
      />
    </template>

    <el-drawer
      :title="formtitle"
      :modal="false"
      :wrapper-closable="true"
      :modal-append-to-body="false"
      :visible.sync="addFormVisible"
      direction="btt"
      size="'auto'"
      class="el-drawer__wrapper"
      style="position:absolute;"
    >
    <form-create ref="formcreate" :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
      <div class="drawer-footer">
        <el-button @click.native="addFormVisible = false">取消</el-button>
        <my-confirm-button type="submit"  :loading="addLoading" @click="onAddSubmit" />
      </div>
    </el-drawer>
  </my-container>
</template>

<script>
import { addOrUpdateProductBianMaCategory, deleteProductBianMaCategory,getProductBianMaCategoryById,getProductBianMaCategoryPageList} from '@/api/operatemanage/base/shop'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatYesornoBool,formatPlatform,platformlist} from "@/utils/tools";
import { rulePlatform} from '@/utils/formruletools'
const tableCols =[
      {istrue:true,prop:'id',label:'编号', width:'180',sortable:'custom'},
      {istrue:true,prop:'categoryName',label:'分类名称', width:'150',sortable:'custom'},
      {istrue:true,prop:'platform',label:'平台', width:'100',formatter:(row)=>formatPlatform(row.platform)},
      {istrue:true,prop:'reMark',label:'备注', width:'200'},
      {istrue:true,type:'button',btnList:[{label:"编辑",handle:(that,row)=>that.onEdit(row)},{label:"删除",handle:(that,row)=>that.onDelete(row)}]}
     ];
const tableHandles1=[
        {label:"新增", handle:(that)=>that.onAdd()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: { 
        name: ''
      },
      platformlist:platformlist,
      list: [],
      pager:{OrderBy:"id",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      autoform:{
               fApi:{},
               rule:[],
               option:{submitBtn:false,global: {'*': {props: {  disabled: true },col: { span: 8 }}}}
        },
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,     
      deleteLoading: false,
      formtitle:"新增",
    }
  },
  mounted() {
    this.getlist()
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {
        ...pager,
        ... this.filter
      }
      this.listLoading = true
      const res = await getProductBianMaCategoryPageList(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    async onEdit(row) {
      this.formtitle='编辑';
      this.addFormVisible = true
      const res = await getProductBianMaCategoryById( row.id )
      await this.autoform.fApi.setValue(res.data)
    },
   async onAdd() {
      this.formtitle='新增';
      this.addFormVisible = true
      this.autoform.rule=[{type:'hidden',field:'id',title:'id',value: '0'},
                      {type:'input',field:'categoryName',title:'分类名称',validate: [{type: 'string', required: true, message:'请输入品牌名称'}]},
                      {type:'select',field:'platform',title:'平台', value: null, ...await rulePlatform()},
                      {type:'input',field:'reMark',title:'备注',props:{type:'textarea'}}]
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
          this.autoform.fApi.reload()
    },
    async onEditSubmit() {
      this.addFormVisible = true
      await onAddSubmit();
    },
    async onAddSubmit() {
       this.addFormVisible=true;
       this.addLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData()
          const res = await addOrUpdateProductBianMaCategory(formData)
          this.getlist()
          console.log(formData)
          this.addFormVisible=false;
        }else{
          //todo 表单验证未通过
        }
     })
      this.addLoading=false;
    },
   
    async onDelete(row) {
      row._loading = true
      this.$confirm('确认删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
            const res = await deleteProductBianMaCategory({id:row.id})
            row._loading = false
            if (!res?.success) {return }
            this.$message({
                type: 'success',
                message: '删除成功!'
            });
            this.getlist()
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
           row._loading = false
        });
    },
    selsChange: function(sels) {
      this.sels = sels
    }
  }
}
</script>
