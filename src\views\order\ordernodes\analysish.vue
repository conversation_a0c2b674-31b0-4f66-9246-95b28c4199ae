<template>
  <div>
    <el-tabs v-model="activeName" style="height: 94%;" @tab-click="tabclick">
        <el-tab-pane label="汇总分析" name="first" style="height: 100%;">
           <ordernodesanalysishz :filter="filter" ref="ordernodesanalysishz"/>
        </el-tab-pane>
        <el-tab-pane label="详细分析" name="second" style="height: 100%;">
           <ordernodesanalysis :filter="filter" ref="ordernodesanalysis"/>
        </el-tab-pane>
        <el-tab-pane label="订单时长分析" name="third" style="height: 100%;">
           <ordernodetimesanalysis :filter="filter" ref="ordernodetimesanalysis"/>
        </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import ordernodesanalysishz from '@/views/order/ordernodes/ordernodesanalysishz'
import ordernodesanalysis from '@/views/order/ordernodes/ordernodesanalysis'
import ordernodetimesanalysis from '@/views/order/ordernodes/ordernodestimeanalysishz'
export default {
  name: 'Roles',
  components: {ordernodesanalysishz,ordernodesanalysis,ordernodetimesanalysis},
  props:{
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    filter:{}
  },
  data() {
    return {
        activeName:"first",
        parms:{},
        selectedIndex:0,
        buscharData:{},
        righttext:'平均时长',
        groupbtnty:{ty0:'warning',ty1:'primary',ty2:'primary',ty3:'primary'}
      }
  },
  async mounted() {
  },
  beforeUpdate() { },
  methods: {
   async tabclick(){
      if (this.activeName=='second') this.$refs.ordernodesanalysis.onSearch();
      if (this.activeName=='third') this.$refs.ordernodetimesanalysis.onSearch();
    },
    async onSearch() {
      let _th=this;
      await this.$nextTick(async () => {
        await _th.$refs.ordernodesanalysishz.onSearch();
        await _th.$refs.ordernodesanalysis.onSearch();
        await _th.$refs.ordernodetimesanalysis.onSearch();
      });
    },
  }
}
</script>
