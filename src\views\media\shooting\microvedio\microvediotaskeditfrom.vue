<template>
    <my-container v-loading="pageLoading"> 
        <el-form :model="addForm" ref="addForm" label-width="120px" :rules="addFormRules" :disabled="islook">
            <el-row>
                <el-col :span="6">
                    <el-form-item   prop="productShortName"  label="产品简称">
                        <el-input style="width:100%" :clearable="true" v-model="addForm.productShortName"   v-model.trim="addForm.productShortName" :maxlength =100></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item   prop="platform"  label="平台:">
                        <el-select v-model="addForm.platform" :clearable="true" :collapse-tags="true" filterable @change="onchangeplatform" >
                            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item  prop="shopName"  label="店铺:">
                        <el-select v-model="addForm.shopName" :clearable="true" :collapse-tags="true" filterable>
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item  prop="operationGroup"  label="运营小组:">
                        <el-select v-model="addForm.operationGroup" :clearable="true"  filterable>
                            <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="6">
                    <el-form-item   prop="dockingPeople"  label="对接人:">
                        <el-input style="width:100%" :clearable="true" v-model="addForm.dockingPeople"  v-model.trim="addForm.dockingPeople" :maxlength =100></el-input>
                    </el-form-item>
                </el-col>
                <!--!checkPermission('shootingtaskUrgencyEdit')-->
                <el-col :span="6">
                    <el-form-item label="紧急程度" >
                        <el-select style="width:100%" v-model="addForm.taskUrgency" :disabled="!checkPermission('microVedioUrgencyEdit')"  filterable  >
                            <el-option v-for="item in taskUrgencyList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item  prop="warehouse"  label="大货仓" :span="12">
                        <el-select v-model="addForm.warehouse" placeholder="请选择货仓" style="width:100%;">
                            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item prop="isDelivered" label="大货到样">
                        <el-radio-group v-model="addForm.isDelivered">
                            <el-radio-button label="1">是</el-radio-button>
                            <el-radio-button label="0">否</el-radio-button>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row  v-if="checkPermission('microVedioFpTask') &&addForm.microVedioTaskId >0 ">
                <el-row > 
                    <el-col :span="6">
                    <el-form-item   prop="fpVideoLqName"  label="分配视频:">
                        <el-input style="width:100%"  
                        :disabled="fpVideoLqNameEnable"   
                        :clearable="true" v-model="addForm.fpVideoLqName"  v-model.trim="addForm.fpVideoLqName" :maxlength =100></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item   prop="fpModelLqName"  label="分配建模:">
                        <el-input style="width:100%"  
                        :disabled="fpModelLqNameEnable"   
                        :clearable="true" v-model="addForm.fpModelLqName"  v-model.trim="addForm.fpModelLqName" :maxlength =100></el-input>
                    </el-form-item>
                </el-col>
            </el-row > 
 
            </el-row>
            <el-row > 
                <el-col :span="24">
                <el-form-item label="拍摄任务" prop="shootingTaskPickList"  >
                    <el-checkbox-group v-model="addForm.shootingTaskPickList" @change="taskPickChange">
                        <el-checkbox  label="3"  :disabled="addForm.microDetailIsOver==1" border>微详情视频</el-checkbox>
                        <el-checkbox  label="6"  :disabled="addForm.modelVideoIsOver==1 " border>建模需求</el-checkbox> 
                    </el-checkbox-group>   
                </el-form-item>
                </el-col> 
            </el-row> 
            <el-row style = "height: 150px;"> 
                <el-col :span="6">
                    <el-form-item  prop="uploadexl" label="参考附件">
                        <uploadfile v-if="addForm.pxeclUpfiles" ref = "uploadexl" :isdown="true" :islook="islook" :uploadInfo="addForm.pxeclUpfiles"  :limit="10000" :accepttyes="'.xlsx'" :delfunction="deluplogexl" />
                    </el-form-item>
                </el-col> 
            </el-row>
            <el-row>
                <el-col :span="24">
                    <el-form-item label="备注" prop="remark">
                        <el-input type="textarea" :rows="3" v-model="addForm.taskRemark" placeholder="请输入备注" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24" v-if ="addForm.microVedioTaskId>0">
                    <el-form-item label="操作日志" prop="loginfo">
                        <el-input type="textarea" :rows="5" v-model="loginfo" :disabled="true" placeholder="请输入备注" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </my-container>
</template>
<script> 
import uploadfile from '@/views/media/shooting/uploadfile' 
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import { addOrUpdateShootingVideoTaskAsync,getShootingTaskFliesAsync,delShootingTploadFileTaskAsync} from '@/api/media/microvedio';
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import {  formatWarehouse } from "@/utils/tools";
export default {
    props:["taskUrgencyList",'groupList','platformList','islook','onCloseAddForm','warehouselist'],
    components: { MyContainer, MyConfirmButton,uploadfile}, 
    watch: {  },
    data() {
        return {
            that: this,
            pageLoading :false, 
            formatWarehouse: formatWarehouse, 
            shopList: [],
            userList: [],   
            taskPhotofileList:[],
            taskExeclfileList:[],
            //warehouseList:[],
            addForm: {
                microVedioTaskId:0, 
                photoIsOver :0,
                vedioIsOver :0,
                microDetailIsOver :0,
                detailIsOver :0,
                modelPhotosIsOver:0,
                modelVideoIsOver :0,
                fpPhotoLqName:null,
                fpVideoLqName:null,
                fpDetailLqName:null,
                fpModelLqName:null,
                shopName:null,
                platform:null,
                productShortName:null,
                operationGroup:null,
                taskUrgency:9,
                dockingPeople:null,
                warehouse:null,
                taskDate:null,
                isReissue:null,
                isDelivered:null,
                taskRemark:null,
                shootingTaskPick:0,
                shootingTaskPickList:[],
                photoUpfiles:[],
                pxeclUpfiles:[],
            },
            loginfo:null,
            fpPhotoLqNameEnable:false,
            fpVideoLqNameEnable:false,
            fpDetailLqNameEnable:false,
            fpModelLqNameEnable:false, 
            addFormRules: {
                productShortName: [{ required: true, message: '请填写', trigger: 'blur' }],
                shopName: [{ required: true, message: '请选择', trigger: 'blur' }],
                operationGroup: [{ required: true, message: '请选择', trigger: 'blur' }], 
                dockingPeople: [{ required: true, message: '请填写', trigger: 'blur' }], 
                warehouse: [{ required: true, message: '请选择', trigger: 'blur' }], 
                isDelivered: [{ required: true, message: '请选择', trigger: 'blur' }],
                shootingTaskPickList: [{ required: true, message: '请选择', trigger: 'blur' }],
                platform: [{ required: true, message: '请选择', trigger: 'blur' }],
                
            },
        };
    },
    watch: {},
    async created() {},
    async mounted() {
        this.addForm.dockingPeople = this.$store.getters.userName?.split("-")[0].trim();
    },
    methods: { 
        setShootO(warehouselist){  
            this.warehouselist =warehouselist; 
        },
        //编码
        taskPickChange(value){
            //未选择，只读，且清空
        
            if(value.indexOf("3")<0){ 
                this.fpVideoLqNameEnable = true;
            }else{ 
                this.fpVideoLqNameEnable = false;
            }
            if( value.indexOf("6")<0) {
                this.fpModelLqNameEnable = true;
            }else{
                this.fpModelLqNameEnable = false;
            } 
        },
        async onchangeplatform(val) {
            var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100 });
            this.addForm.shopName = ""; 
            this.shopList = res1.data.list;
        },
         //编辑任务
        async editTask(row) {
            this.pageLoading =true;   
            await this.onchangeplatform(row.platform); 
            this.taskPickChange(row.shootingTaskPickList);
            this.addForm = _.cloneDeep(row); 
            //获取拍摄上传的附件
            var res  =  await getShootingTaskFliesAsync({taskid:row.microVedioTaskId});
            this.addForm.photoUpfiles=[];
            this.addForm.pxeclUpfiles=[];
            this.loginfo = null;
            if(res?.success){ 
                res.data.data.forEach(element => {
                    if(element.upLoadType ==1){
                        this.addForm.photoUpfiles.push(element);
                    }
                    if(element.upLoadType == 2){
                        this.addForm.pxeclUpfiles.push(element);
                    }
                });
                await this.$nextTick(function () { 
                    //this.$refs.shootingchartforfp.showviewMain();
                    this.$refs.uploadexl.setData(this.addForm.pxeclUpfiles); 
                });
                //this.$refs.uploadimg.setData(this.addForm.photoUpfiles); 
      
                this.loginfo  = res.data.loginfo;
            }
            this.pageLoading =false;
        },
        //提交保存时验证
        onSubmitValidate: function () {
            let isValid = true;
            this.$refs.addForm.validate(valid => {
                isValid = valid
            })
            return isValid ;
        },
        //提交保存
        async onSubmit() { 
           if(!this.onSubmitValidate()){
                return;
            }  
           /*  var res = this.$refs.uploadimg.getReturns(); 
            if(!res.success){return;}
            this.addForm.photoUpfiles = res.data; */
            var res = this.$refs.uploadexl.getReturns();
            if(!res.success)  return;
            if(res.data.length == 0){
                this.$message({message: this.$t('请上传附件'),type: 'error'});
                return;
            }
            this.addForm.execlUpfiles = res.data;  
              //未选择，只读，且清空
            var value =  this.addForm.shootingTaskPickList; 
            if(value.indexOf("3")<0){ this.addForm.fpVideoLqName = null;}   
            if(value.indexOf("6")<0){ this.addForm.fpModelLqName = null;} 
            const para = _.cloneDeep(this.addForm);   
            this.addLoading =true;
            var res = await addOrUpdateShootingVideoTaskAsync(para);
            this.addLoading =false;
            if (!res?.success) {  return; }
            this.$message({message: this.$t('保存成功'),type: 'success'});
            this.onCloseAddForm(1); 
        },
        //删除上传附件操作
        async deluplogexl(ret){
            this.addLoading =true;
            await delShootingTploadFileTaskAsync({upLoadPhotoId:ret.upLoadPhotoId,type:2}).catch(_ => {
                this.addLoading =false;
            });  
            this.addLoading =false;
        },
        //删除上传图片操作
        async deluplogimg(ret){ 
            this.addLoading =true; 
            await delShootingTploadFileTaskAsync({upLoadPhotoId:ret.upLoadPhotoId,type:1}).catch(_ => {
                this.addLoading =false;
            }); 
            this.addLoading =false;
        },
        initaddform(){
            this.addForm.microVedioTaskId=0;
            this.addForm.platform = null;
            this.addForm.photoIsOver =0;
            this.addForm.vedioIsOver =0;
            this.addForm.microDetailIsOver =0;
            this.addForm.detailIsOver =0;
            this.addForm.modelPhotosIsOver =0;
            this.addForm.modelVideoIsOver =0;
            this.addForm.fpVideoLqName=null;
            this.addForm.fpPhotoLqName=null;
            this.addForm.fpModelLqName=null;
            this.addForm.fpDetailLqName=null;
            this.addForm.shopName=null;
            this.addForm.productShortName=null;
            this.addForm.operationGroup=null;
            this.addForm.taskUrgency=null;
            this.addForm.dockingPeople=null;
            this.addForm.warehouse=null;
            this.addForm.taskDate=null;
            this.addForm.isDelivered=null;
            this.addForm.taskRemark=null;
            this.addForm.shootingTaskPickList=[],
            this.addForm.photoUpfiles=[];
            this.addForm.pxeclUpfiles=[];
            this.addForm.photoPlay=0;
            this.addForm.vedioPlay=0;
            this.addForm.microDetailPlay=0;
            this.addForm.detailPlay=0;
            this.addForm.modelPhotosPlay=0;  
        },
    }, 
};
</script>

