<template>
  <container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="日期:">
          <el-date-picker style="width: 210px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                          start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false" class="date_picker" @change="pickerDateChange"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
  <el-tabs v-model="activeName" style="height: 94%;">
    <el-tab-pane label="催收费" name="first" style="height: 100%;">
       <cuishoufee :filter="filter" ref="cuishoufee" @ondownloadmb='ondownloadmb' @onstartImport='onstartImport' @ondeleteByBatch='ondeleteByBatch'/>
    </el-tab-pane>
    <el-tab-pane label="样品费" name="second" style="height: 100%;">
       <samplefee :filter="filter" ref="samplefee" @ondownloadmb='ondownloadmb' @onstartImport='onstartImport' @ondeleteByBatch='ondeleteByBatch'/>
    </el-tab-pane>
    <el-tab-pane label="产品运费" name="second01" style="height: 100%;">
       <purchasepickup :filter="filter" ref="purchasepickup" @ondownloadmb='ondownloadmb' @onstartImport='onstartImport' @ondeleteByBatch='ondeleteByBatch'/>
    </el-tab-pane>
    <el-tab-pane label="现金红包" name="daycashred" style="height: 100%;">
       <daycashred :filter="filter" ref="daycashred" @ondownloadmb='ondownloadmb' @onstartImport='onstartImport' @ondeleteByBatch='ondeleteByBatch'/>
    </el-tab-pane>
    <el-tab-pane label="库存盘点" name="four" style="height: 100%;">
       <Stocktaking :filter="filter" ref="Stocktaking"   />
    </el-tab-pane>
    <el-tab-pane label="每日汇总" name="third" style="height: 100%;">
       <daysumfee :filter="filter" ref="daysumfee"/>
    </el-tab-pane>
    <el-tab-pane label="代发成本差" name="fifth" style="height: 100%;">
       <replacesend :filter="filter" ref="replacesend" @ondownloadmb='ondownloadmb' @onstartImport='onstartImport'/>
    </el-tab-pane>
    <el-tab-pane label="特殊单" name="sixth" style="height: 100%;">
       <specialorder :filter="filter" ref="specialorder" @ondownloadmb='ondownloadmb'  @onstartImport='onstartImport'/>
    </el-tab-pane>
    <el-tab-pane label="淘宝客数据" name="seventh" style="height: 100%;">
       <tbvisitor :filter="filter" ref="tbvisitor" @ondownloadmb='ondownloadmb'  @onstartImport='onstartImport'/>
    </el-tab-pane>
    <el-tab-pane label="分销代拍成本差" name="eightth" style="height: 100%;">
      <daipaifenxiao :filter="filter" ref="daipaifenxiao" @ondownloadmb='ondownloadmb'  @onstartImport='onstartImport'/>
   </el-tab-pane>
  </el-tabs>
  <el-dialog title="批量删除" :visible.sync="dialogdeletebatchNumberVisible" width="500px" v-dialogDrag>
      <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker v-model="deletefilter.yearMonthDay" type="date" format="yyyyMMdd" value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
            <el-button type="primary" @click="deleteByBatch">删除</el-button>
          </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogdeletebatchNumberVisible = false">关闭</el-button>
      </span>
    </el-dialog>
   <el-dialog title="导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <span>
        <!-- <el-row v-if="onimportfilter.shareFeeType!=5">
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
           <el-date-picker v-if="onimportfilter.shareFeeType==4" style="width: 100%" v-model="onimportfilter.yearmonthday" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择入库日期"></el-date-picker>
           <el-date-picker v-else style="width: 100%" v-model="onimportfilter.yearmonthday" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
         </el-col>
         </el-row> -->
          <el-row>
          <el-col span="6" v-if="activeName == 'sixth'">
            <span style="color: #F56C6C; margin-right: 7px;">*</span>
            <el-select v-model="platform" placeholder="平台" filterable clearable style="width: 90%">
              <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </el-col>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                <el-upload :on-change="uploadChange" :on-remove="uploadDel" ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx" :http-request="uploadFile" :file-list="fileList" :data="fileparm">
                <template #trigger>
                  <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
              </el-upload>
            </el-col>
          </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </container>
</template>

<script>
import {deleteDayCost,importDayCuiShouFee,importDaySampleFee,importDayPurchasePickUp,importDayCashRed,importDaySpecialOrder,importDayTBVistorData,ImportDayReportProxyPhotography_FenXiao} from '@/api/financial/daycost'
import {importNewReplaceSend,importReduceNewReplaceSend} from '@/api/financial/replacesend'
import cuishoufee from '@/views/financial/daycost/cuishoufee'
import samplefee from '@/views/financial/daycost/samplefee'
import purchasepickup from '@/views/financial/daycost/purchasepickup'
import Stocktaking from '@/views/financial/daycost/Stocktaking'
import daycashred from '@/views/financial/daycost/daycashred'
import daysumfee from '@/views/financial/daycost/daysumfee'
import replacesend from '@/views/financial/daycost/replacesend'
import specialorder from '@/views/financial/daycost/specialorder'
import tbvisitor from '@/views/financial/daycost/tbvisitor'
import daipaifenxiao from '@/views/financial/daycost/daipaifenxiao'
import container from '@/components/my-container/nofooter'
import { formatPlatform,formatLink,platformlist} from "@/utils/tools";
export default {
  name: 'Roles',
  components: {container,purchasepickup,daysumfee,cuishoufee,samplefee,daycashred,Stocktaking,replacesend,specialorder,tbvisitor,daipaifenxiao},
  data() {
    return {
      platform: null,
      activeName: 'first',
      filter: {
        startTime:null,
        endTime:null,
        companyId:null,
        warehouse:null,
        timerange:null,
      },
      platformlist:platformlist,
      deletefilter: {shareFeeType:null,yearMonthDay:'' },
      onimportfilter: {shareFeeType:0 ,yearmonthday:'' },
      expresscompanylist: [],
      pageLoading: false,
      dialogVisible: false,
      uploadLoading:false,
      importFilte:{},
      fileList:[],
      fileparm:{},
      dialogdeletebatchNumberVisible:false,
      dialogcomputVisible:false
    }
  },
  mounted() { },
  beforeUpdate() {
    console.log('update')
  },
async created() {
    await this.init()
  },
  methods: {
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init(){
        var date1 = new Date(); date1.setDate(date1.getDate()-1);
        var date2 = new Date(); date2.setDate(date2.getDate()-1);
        this.filter.timerange=[];
        this.filter.timerange[0]=this.datetostr(date1);
        this.filter.timerange[1]=this.datetostr(date2);
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      },
    onSearch() {
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      if (this.activeName=='first') this.$refs.cuishoufee.onSearch();
      else if (this.activeName=='second') this.$refs.samplefee.onSearch();
      else if (this.activeName=='third') this.$refs.daysumfee.onSearch();
      else if (this.activeName=='second01') this.$refs.purchasepickup.onSearch();
      else if (this.activeName=='daycashred') this.$refs.daycashred.onSearch();
      else if (this.activeName=='four') this.$refs.Stocktaking.onSearch();
      else if (this.activeName=='fifth') this.$refs.replacesend.onSearch();
      else if (this.activeName=='sixth') this.$refs.specialorder.onSearch();
      else if (this.activeName=='seventh') this.$refs.tbvisitor.onSearch();
      else if (this.activeName=='eightth') this.$refs.daipaifenxiao.onSearch();
    },
    async onstartImport(shareFeeType){
      this.platform = null;
      this.dialogVisible=true;
      this.onimportfilter.shareFeeType=shareFeeType;
      this.uploadLoading = false;
      this.fileList = [];
    },
    async ondeleteByBatch(shareFeeType) {
      this.dialogdeletebatchNumberVisible=true;
      this.deletefilter.shareFeeType=shareFeeType;
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    async uploadChange(file, fileList) {
      let files = [];
      files.push(file)
      this.fileList = files;
    },
    async uploadDel(file,fileList){
      this.fileList = [];
    },
    submitUpload() {
      // if (!this.onimportfilter.yearmonthday&&this.onimportfilter.shareFeeType!=5) {
      //  this.$message({type: 'warning',message: '请选择日期!'});
      //  return;
      // }
      if(this.activeName == 'sixth' && !this.platform){
        this.$message({message: "请选择平台", type: "warning" });
        return false;
      }
      if(this.fileList.length==0){
        this.$message({message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading=true
      this.$refs.upload.submit();
    },
    clearFiles(){
        this.$refs['upload'].clearFiles();
    },
    async uploadFile(item) {
      console.log('文件来了',item)
     if(!item||!item.file||!item.file.size){
        this.$message({message: "请先上传文件", type: "warning" });
        return false;
      }

      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearmonthday", this.onimportfilter.yearmonthday);
      console.log('参数',form,this.onimportfilter.shareFeeType)
      var res;
      if (this.onimportfilter.shareFeeType==1) res= await importDayCuiShouFee(form);
      else if (this.onimportfilter.shareFeeType==2)res= await importDaySampleFee(form);
      else if (this.onimportfilter.shareFeeType==4)res= await importDayPurchasePickUp(form);
      else if (this.onimportfilter.shareFeeType==5)res= await importDayCashRed(form);
      else if (this.onimportfilter.shareFeeType==6)res= await importNewReplaceSend(form);
      else if (this.onimportfilter.shareFeeType == 7) {
        form.append("platform", this.platform);
        res = await importDaySpecialOrder(form);
      }
      else if (this.onimportfilter.shareFeeType==8)res= await importDayTBVistorData(form);
      else if (this.onimportfilter.shareFeeType==9)res= await importReduceNewReplaceSend(form);
      else if (this.onimportfilter.shareFeeType==10)res= await ImportDayReportProxyPhotography_FenXiao(form);
      if (res.code==1) this.$message({message: "上传成功,正在导入中...", type: "success" });
      else this.$message({message: res.msg, type: "warning" });
      this.$refs.upload.clearFiles();
      this.uploadLoading=false
    },
  async deleteByBatch() {
      if (!this.deletefilter.yearMonthDay) {
       this.$message({type: 'warning',message: '请选择月份!'});
       return;
      }
      this.$confirm('确认删除, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
        }).then(async () => {
            const res = await deleteDayCost(this.deletefilter)
            if (!res?.success) {return }
            this.$message({type: 'success',message: '删除成功!'});
            this.onSearch()
        }).catch(() => {
          this.$message({type: 'info',message: '已取消删除'});
        });
    },
    async ondownloadmb(name) {
       var alink = document.createElement("a");
       alink.href =`../../static/excel/dayreport/daycost/${name}.xlsx`;
       alink.click();
    },
    pickerDateChange(t){
      if(t == null){
        //清空则重新赋值，不让清空
        this.filter.timerange =[this.filter.startTime,this.filter.endTime]
      }else{
        if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
       }
     }
  }
}
}
</script>
