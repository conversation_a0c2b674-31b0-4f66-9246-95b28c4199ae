<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <div>
        <span style="margin-right:0.4%;">
          <el-date-picker style="width: 200px" v-model="filter.yearMonthDay" type="date" placeholder="选择日期"
            :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
          </el-date-picker>
        </span>

        <span style="margin-right:0.4%;">
          <el-select filterable v-model.trim="filter.brandIds" clearable multiple collapse-tags placeholder="采购"
            style="width: 175px">
            <!-- <el-option label="其他" value="99999"></el-option> -->
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </span>

        <span style="margin-right:0.4%;">
          <el-select filterable v-model.trim="filter.deptId" clearable multiple collapse-tags placeholder="架构"
            style="width: 175px">
            <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </span>

        <span style="margin-right:0.4%;">
          <el-select v-model.trim="filter.company" multiple collapse-tags filterable clearable placeholder="地区"
            style="width: 175px">
            <el-option label="义乌" value="义乌"></el-option>
            <el-option label="南昌" value="南昌"></el-option>
            <el-option label="武汉" value="武汉"></el-option>
            <el-option label="深圳" value="深圳"></el-option>
            <el-option label="其他" value="其他"></el-option>
            <el-option label="耗材包材" value="耗材包材"></el-option>
          </el-select>
        </span>

        <span style="margin-right:0.4%;">
          <el-select v-model.trim="filter.dDeptNames" filterable clearable multiple collapse-tags placeholder="岗位"
            style="width: 175px">
            <el-option v-for="item in positionList" :key="item.titleName" :label="item.titleName"
              :value="item.titleName" />
          </el-select>
        </span>

        <span style="margin-left:5px;">
          <el-button type="primary" @click="onSearch" style="width: 70px;">搜索</el-button>
        </span>

        <span style="margin-left:5px;">
          <el-button type="primary" @click="onExport" style="width: 70px;">导出</el-button>
        </span>

        <span style="margin-left:5px;">
          <el-button type="primary" @click="onImport" style="width: 70px;"
            v-if="checkPermission('PurchaseTurnoverDays')">导入</el-button>
        </span>
        <span style="margin-left:5px;">
          <el-button v-if="checkPermission('Api:Inventory:PurchaseOrderNew:SavePurchaseNewPlanTurnDaySet')"
            type="primary" @click="onSettingsMethod" style="width: 70px;">设置</el-button>
        </span>
        <span style="margin-left:5px;">
          <el-button type="primary" @click="computedCost" v-if="checkPermission('computedMonthSaleCost')" >计算月销成本</el-button>
        </span>
        <div style="margin: 5px 0;display: flex;align-items: center;font-size: 12px;">
        <span style="margin-left: 10px;">销售主题:{{ statusObj?.saleTitle ? '已导入' : '未导入' }},</span>
        <span style="margin-left: 10px;">商品资料:{{ statusObj?.goodsInfo ? '已导入' : '未导入' }},</span>
        <span style="margin-left: 10px;">计划采购建议:{{ statusObj?.purchasePlan ? '已导入' : '未导入' }},</span>
        <span style="margin-left: 10px;">计划采购建议中存在30天销量数据:{{ statusObj?.purchasePlanSalesDay30NotZero }},</span>
        <span style="margin-left: 10px;color: red;">最后计算时间:{{ statusObj?.cptTime }}</span>
      </div>
      </div>
    </template>

    <ces-table :id="'purchaseIndividualTurnoverDays202408041542'" ref="table" :that='that' :isIndex='true'
      :hasexpand='true' @sortchange='sortchange' @summaryClick='onsummaryClick' :summaryarry='summaryarry' :showheaderoverflow="false"
      :showsummary='true' :tableData='list' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :loading="listLoading" style="width:100%;height:94%;margin: 0">
    </ces-table>
    <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" :pageSize="200"
      :sizes="[50, 100, 200, 300]" />

    <el-dialog :visible.sync="turnoverdaysdialog" width="85%" height="90%" v-dialogDrag>
      <div style="height: 40px; margin-top: 10px; display: flex; align-items: center;">
        <span style="margin-right:0.4%;">
          <el-date-picker style="width: 250px" v-model="timerange" type="daterange" :clearable="false"
            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" :start-placeholder="'开始时间'"
            :end-placeholder="'结束时间'" @change="handleCheckedCitiesChange($event, 1)"></el-date-picker>
        </span>
        <span style="margin-right:0.4%;">
          <el-select v-model.trim="ruleForm.brandId" filterable clearable placeholder="采购" style="width: 90px"
            @change="handleCheckedCitiesChange($event, 2)">
            <el-option label="公司" :value="-1" />
            <el-option label="筛选" :value="-2" />
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </span>
        <span style="margin-right:0.4%;">
          <el-select v-model.trim="ruleForm.company" multiple collapse-tags filterable clearable placeholder="地区"
            style="width: 165px" @change="handleCheckedCitiesChange($event, 3)" :disabled="ruleForm.brandId == -1 || ruleForm.brandId == -2">
            <el-option label="义乌" value="义乌"></el-option>
            <el-option label="南昌" value="南昌"></el-option>
            <el-option label="其他" value="其他"></el-option>
            <el-option label="耗材包材" value="耗材包材"></el-option>
          </el-select>
        </span>
        <span style="margin-right:0.4%;">
          <el-select v-model.trim="ruleForm.dDeptName" filterable clearable placeholder="岗位" style="width: 90px"
            @change="handleCheckedCitiesChange($event, 4)" :disabled="ruleForm.brandId == -1 || ruleForm.brandId == -2">
            <el-option v-for="item in positionList" :key="item.titleName" :label="item.titleName"
              :value="item.titleName" />
          </el-select>
        </span>
        对比
        <span style="margin-right:0.4%;">
          <el-select v-model.trim="ruleForm.brandId2" filterable clearable placeholder="采购" style="width: 90px"
            @change="handleCheckedCitiesChange($event, 5)">
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </span>
        <span style="margin-right:0.4%;">
          <el-select v-model.trim="ruleForm.company2" multiple collapse-tags filterable clearable placeholder="地区"
            style="width: 165px" @change="handleCheckedCitiesChange($event, 6)">
            <el-option label="义乌" value="义乌"></el-option>
            <el-option label="南昌" value="南昌"></el-option>
            <el-option label="其他" value="其他"></el-option>
            <el-option label="耗材包材" value="耗材包材"></el-option>
          </el-select>
        </span>
        <span style="margin-right:20px;">
          <el-select v-model.trim="ruleForm.dDeptName2" filterable clearable placeholder="岗位" style="width: 90px"
            @change="handleCheckedCitiesChange($event, 7)">
            <el-option v-for="item in positionList" :key="item.titleName" :label="item.titleName"
              :value="item.titleName" />
          </el-select>
        </span>
        <span style="margin-right:20px;">
          <el-radio v-model="ruleForm.monthOrDay" label="月" @input="handleCheckedCitiesChange($event, 9)">月</el-radio>
          <el-radio v-model="ruleForm.monthOrDay" label="日" @input="handleCheckedCitiesChange($event, 9)">日</el-radio>
        </span>
        <span style="margin-right:0.4%;">
          <el-checkbox-group v-model="ruleForm.checkList" @change="handleCheckedCitiesChange($event, 8)">
            <el-checkbox label="月销成本"></el-checkbox>
            <el-checkbox label="货品成本"></el-checkbox>

            <el-checkbox label="进货仓库存资金"></el-checkbox>
            <el-checkbox label="采购在途库存资金"></el-checkbox>
            <el-checkbox label="调拨在途资金"></el-checkbox>

            <el-checkbox label="个人库存周转天数"></el-checkbox>
            <el-checkbox label="个人库存周转天数(新)"></el-checkbox>
          </el-checkbox-group>
        </span>
      </div>
      <div style="height: 550px;">
        <span>
          <buschar v-if="detailtrendchart.visible" ref="detailtrendchartref" :analysisData="detailtrendchart.data">
          </buschar>
        </span>
      </div>
    </el-dialog>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="45%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 100px;">
        <el-date-picker style="width: 200px;margin-right: 10px;margin-bottom: 10px;" v-model="imports.yearMonthDay"
          type="date" placeholder="选择日期" :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
        </el-date-picker>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action=""
          accept=".xlsx" :file-list="fileList" :http-request="onUploadFile" :on-success="onUploadSuccess"
          :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary" style="width: 90px;">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px;width: 90px;" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="设置" :visible.sync="settingsVisible" width="40%" v-dialogDrag :close-on-click-modal="false"
      style="margin-top: -7vh;">
      <div class="SettingCss" v-loading="settingsLoading">
        <div class=SettingCss_item>
          <div class=SettingCss_top>
            <el-checkbox v-model="settings.brandFilter" />
            <span>品牌过滤</span>
            <el-select v-model="settings.filtering" placeholder="请选择" filterable clearable>
              <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.label" />
            </el-select>
            <el-button type="primary" @click="onAddLabelMethod(settings.filtering, 1)"
              style="width: 70px;">新增</el-button>
          </div>
          <el-scrollbar class=SettingCss_bottom style="height: 120px;">
            <el-tag v-for="tagitem in settings.brandFilterList" :key="tagitem" closable style="margin: 3px;"
              @close="handleClose(tagitem, 1)">
              <span>{{ tagitem }}</span>
            </el-tag>
          </el-scrollbar>
        </div>
        <div class=SettingCss_item>
          <div class=SettingCss_top>
            <el-checkbox v-model="settings.costPriceFilter" />
            <span>成本价过滤</span>
          </div>
          <div class=SettingCss_bottom style="height: 60px;">
            <el-table :data="settings.costPriceFilterList" style="width: 100%" height="68px" :show-header="false">
              <el-table-column prop="contrastType" width="80">
                <template slot-scope="scope" class="name-wrapper">
                  <span>成本价</span>
                </template>
              </el-table-column>
              <el-table-column prop="contrastType" width="200">
                <template slot-scope="scope">
                  <div slot="reference" class="name-wrapper">
                    <el-select v-model="scope.row.contrastType" filterable clearable placeholder="请选择"
                      @change="contrastChange($event, scope.row, settings.costPriceFilterList.indexOf(scope.row))">
                      <el-option label="大于" :value="1"></el-option>
                      <el-option label="等于" :value="2"></el-option>
                      <el-option label="小于" :value="3"></el-option>
                      <el-option label="介于" :value="4"></el-option>
                    </el-select>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="firstVal" width="200">
                <template slot-scope="scope">
                  <div slot="reference" class="name-wrapper">
                    <el-input-number v-model.trim="scope.row.firstVal" placeholder="请输入" :min="-999999" :max="999999"
                      :precision="2" :controls="false" style="width: 100%;" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="secondVal" width="200">
                <template slot-scope="scope">
                  <div slot="reference" class="name-wrapper">
                    <el-input-number v-model.trim="scope.row.secondVal" placeholder="请输入" :min="-999999" :max="999999"
                      :precision="2" :controls="false" style="width: 100%;" :disabled="scope.row.contrastType != 4" />
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class=SettingCss_item>
          <div class=SettingCss_top>
            <el-checkbox v-model="settings.goodsLabelFilter" />
            <span>商品标签过滤</span>
            <el-input v-model.trim="commodity" placeholder="请输入" maxlength="50" clearable style="width: 30%;" />
            <el-button type="primary" @click="onAddLabelMethod(commodity, 2)" style="width: 70px;">新增</el-button>
          </div>
          <el-scrollbar class=SettingCss_bottom style="height: 120px;">
            <el-tag v-for="tagitem in settings.goodsLabelFilterList" :key="tagitem" closable style="margin: 3px;"
              @close="handleClose(tagitem, 2)">
              <span>{{ tagitem }}</span>
            </el-tag>
          </el-scrollbar>
        </div>
      </div>
      <div style="display: flex; justify-content: center; gap: 20px;margin-top: 25px;">
        <el-button @click="settingsVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSortSave">保 存</el-button>
      </div>
    </el-dialog>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import buschar from '@/components/Bus/buschar'
import { formatTime, formatPlatform, pickerOptions } from "@/utils/tools";
import { getAllProBrand, getBianManPositionListV2 } from '@/api/inventory/warehouse'
import { 
  getPurchaseNewPlanTurnDayPageList, 
  getPurchaseNewPlanTurnDayAnalysis, 
  getPurchaseNewPlanTurnDayBrandList, 
  exportPurchaseNewPlanTurnDayList, 
  getPurchaseDeptList, 
  getPurchaseNewPlanTurnDaySet, 
  savePurchaseNewPlanTurnDaySet,
  runTurnDay,
  getPurchaseNewPlanTurnDayDeptList,
  getBrandPositionList ,
  GetSellCostStatus
} from '@/api/inventory/purchaseordernew'
import { getTime } from '@/utils/getCols'
import { importPurchaseIndividualTurnoverDayAsync } from '@/api/inventory/importPurchaseordernew'
import { computed } from "vue";
import dayjs from "dayjs";
const tableCols = [
  { istrue: true, prop: 'yearMonthDay', label: '日期', width: '90', align: 'center', formatter: (row) => formatTime(row.yearMonthDay, 'YYYY-MM-DD') },
  { istrue: true, prop: 'brandName', label: '采购', width: '95', align: 'center', sortable: 'custom' },
  { istrue: true, prop: 'purDept', label: '架构', width: '80', align: 'center' },
  { istrue: true, prop: 'company', label: '地区', width: '83', align: 'center', sortable: 'custom' },
  { istrue: true, prop: 'dDeptName', label: '岗位', width: '91', align: 'center', sortable: 'custom' },
  { istrue: true, prop: 'goodsCodeAllCount', width: '75', label: '商品编码数量', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'monthSaleAllCost', label: '月销成本', tipmesg: '销售主题或计划采购建议30天的销量(排除特殊单) * 成本价', width: '97', align: 'center', sortable: 'custom', summaryEvent: true, type: 'click', handle: (that, row) => that.openSumDialog(row, 1) },
  { istrue: true, prop: 'monthSellStockAllCost', label: '货品成本', tipmesg: '(可用库存 - 虚拟库存) * 成本价', width: '93', align: 'center', sortable: 'custom', summaryEvent: true, type: 'click', handle: (that, row) => that.openSumDialog(row, 2) },
  { istrue: true, prop: 'brandStockTurnDay', label: '个人库存周转天数', tipmesg: '货品成本 / 月销成本 * 30', width: 'auto', align: 'center', sortable: 'custom', summaryEvent: true, type: 'click', handle: (that, row) => that.openSumDialog(row, 3) },
  { istrue: true, prop: 'monthBrandStockTurnDay', label: '自然月平均周转天数', tipmesg: '个人库存周转天数 月平均', width: '85', align: 'center', sortable: 'custom' },
  { istrue: true, prop: 'brandSort', label: '排序', width: '80', align: 'center', sortable: 'custom' },
  { istrue: true, prop: 'createdTime', label: '最近更新时间', width: '130', align: 'center' },
  { istrue: true, prop: 'purchaseWarehouseStockFund', label: '进货仓库存资金', tipmesg: '进货仓库存 * 成本价', width: '94', align: 'center', sortable: 'custom', summaryEvent: true, type: 'click', handle: (that, row) => that.openSumDialog(row, 4) },
  { istrue: true, prop: 'purchaseInTransitQuantityFund', label: '采购在途库存资金', tipmesg: '采购在途库存 * 成本价', width: '94', align: 'center', sortable: 'custom', summaryEvent: true, type: 'click', handle: (that, row) => that.openSumDialog(row, 5) },
  { istrue: true, prop: 'transferInTransitQuantityFund', label: '调拨在途资金', tipmesg: '调拨在途 * 成本价', width: '70', align: 'center', sortable: 'custom', summaryEvent: true, type: 'click', handle: (that, row) => that.openSumDialog(row, 6) },
  { istrue: true, prop: 'brandStockTurnDayExt', label: '个人库存周转天数(新)', tipmesg: '(货品成本 + 进货仓库存资金 + 采购在途库存资金 + 调拨在途资金) / (月销成本) * 30', width: '100', align: 'center', sortable: 'custom', summaryEvent: true, type: 'click', handle: (that, row) => that.openSumDialog(row, 7) },
  { istrue: true, prop: 'avgMonthlyTurnDayExt', label: '自然月平均周转天数(新)', tipmesg: '个人库存周转天数(新) 月平均', width: '121', align: 'center', sortable: 'custom' },
];

export default {
  name: 'purchaseIndividualTurnoverDays',
  components: { MyContainer, cesTable, buschar },
  data() {
    return {
      settingsLoading: false,
      settingsVisible: false,
      commodity: '',
      verifyCost: false,//验证是否清除成本价
      settings: {
        costPriceFilter: false,
        costPriceFilterList: [],
        brandFilter: false,
        brandFilterList: [],
        goodsLabelFilter: false,
        goodsLabelFilterList: [],
      },
      imports: {
        yearMonthDay: ''
      },
      purchasegrouplist: [],//架构
      positionList: [],//岗位
      fileList: [],
      uploadLoading: false,
      dialogVisible: false,
      turnoverdaysdialog: false,
      detailtrendchart: { visible: false, title: "", data: {} },
      sels: [],
      listLoading: false,
      brandlist: [],//采购选择器数据
      that: this,//赋值
      list: [],//表格数据
      total: null,
      tableCols: tableCols,
      summaryarry: {},
      timerange: [],
      pager: { orderBy: 'brandSort', isAsc: true },
      filter: {
        yearMonthDay: '',
        company: [],
        dDeptName: null,
        brandIds: [],
        deptId: [],
        dDeptNames: []
      },
      ruleForm: {
        brandIds: [],
        dDeptNames: [],
        checkList: [],
        startDate: null,
        endDate: null,
        brandId: null,
        deptId: [],
        company: [],
        dDeptName: null,
        brandId2: null,
        company2: [],
        dDeptName2: null,
        monthOrDay: '月',
      },
      pageLoading: false,
      statusObj:{}
    };
  },

  async mounted() {
    this.getStatus()
    let end = new Date();
    let start = new Date();
    end.setDate(start.getDate());
    start.setDate(start.getDate() - 30);
    this.timerange = [formatTime(start, "YYYY-MM-DD"), formatTime(end, "YYYY-MM-DD")]
    this.filter.yearMonthDay = formatTime(end, "YYYY-MM-DD")
    await this.init();
    await this.onSearch()
  },

  methods: {
    async getStatus() {
      const { data, success } = await GetSellCostStatus()
      this.statusObj = data
    },
    async computedCost(){
      const {success} = await runTurnDay(dayjs().format('YYYY-MM-DD'))
        if (success) {
          this.$message.success('计算成功')
        }
    },
    contrastChange(val, row, index) {
      if (!val) {
        this.verifyCost = true
      }
      if (index !== -1) {
        this.settings.costPriceFilterList[index].firstVal = '';
        this.settings.costPriceFilterList[index].secondVal = '';
      }
    },
    handleClose(tag, type) {
      if (type == 1) {
        this.settings.brandFilterList.splice(this.settings.brandFilterList.indexOf(tag), 1);
      } else {
        this.settings.goodsLabelFilterList.splice(this.settings.goodsLabelFilterList.indexOf(tag), 1);
      }
    },
    onAddLabelMethod(e, val) {
      if (val == 1) {
        if (!this.settings.filtering) return;
        this.settings.brandFilterList.push(this.settings.filtering);
        this.settings.filtering = '';
      } else {
        if (!this.commodity) return;
        this.settings.goodsLabelFilterList.push(this.commodity);
        this.commodity = '';
      }
    },
    async onSortSave() {
      if (this.verifyCost) {
        this.settings.costPriceFilterList = []
      }
      this.settingsLoading = true;
      const { success } = await savePurchaseNewPlanTurnDaySet(this.settings);
      this.settingsLoading = false;
      if (success) {
        this.$message({ message: '保存成功', type: 'success' });
      }
      this.settingsVisible = false
    },
    async onSettingsMethod() {
      const { data, success } = await getPurchaseNewPlanTurnDaySet();
      if (!success) return;
      const {
        brandFilter,
        brandFilterList,
        costPriceFilter,
        costPriceFilterList,
        goodsLabelFilter,
        goodsLabelFilterList
      } = data;
      this.settings = {
        ...this.settings,
        brandFilter,
        brandFilterList,
        costPriceFilter,
        costPriceFilterList: costPriceFilterList.length ? costPriceFilterList : [{ contrastType: '', firstVal: '', secondVal: '' }],
        goodsLabelFilter,
        goodsLabelFilterList
      };
      this.settingsVisible = true
    },
    async onsummaryClick(property) {
      this.ruleForm.brandId2 = null;
      this.ruleForm.company2 = [];
      this.ruleForm.company = [];
      this.timerange = []
      this.ruleForm.company = this.filter.company
      this.ruleForm.dDeptName2 = null;
      if(this.filter.brandIds?.length > 0 || this.filter.deptId?.length > 0 || this.filter.company?.length > 0 || this.filter.dDeptNames?.length > 0)
        this.ruleForm.brandId = -2;
      else
        this.ruleForm.brandId = -1
      this.ruleForm.dDeptName = null;
      if (property == 'monthSaleAllCost') {
        this.ruleForm.checkList = ['月销成本'];
      } else if (property == 'monthSellStockAllCost') {
        this.ruleForm.checkList = ['货品成本'];
      } else if (property == 'purchaseWarehouseStockFund') {
        this.ruleForm.checkList = ['进货仓库存资金'];
      } else if (property == 'purchaseInTransitQuantityFund') {
        this.ruleForm.checkList = ['采购在途库存资金']; 
      } else if (property == 'transferInTransitQuantityFund') {
        this.ruleForm.checkList = ['调拨在途资金']; 
      } else if (property == 'brandStockTurnDay') {
        this.ruleForm.checkList = ['个人库存周转天数'];
      } else if (property == 'brandStockTurnDayExt') {
        this.ruleForm.checkList = ['个人库存周转天数(新)'];
      }
      this.timerange = getTime([this.filter.yearMonthDay, this.filter.yearMonthDay])
      this.ruleForm.endDate = this.timerange[1];
      this.ruleForm.startDate = this.timerange[0];
      const params = { ...this.ruleForm, ...this.filter, yearMonthDay: this.filter.yearMonthDay }
      await this.showchart(params)
      this.turnoverdaysdialog = true
    },
    onSubmitUpload() {
      if (!this.imports.yearMonthDay) {
        this.$message({ message: "请选择日期", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearMonthDay", this.imports.yearMonthDay);
      const res = await importPurchaseIndividualTurnoverDayAsync(form);
      if (res?.success) {
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      } else {
        this.uploadLoading = false
      }
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.onSearch()
    },
    //导入
    onImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async handleCheckedCitiesChange(data, type) {
      if (type == 1) {
        this.ruleForm.startDate = null;
        this.ruleForm.endDate = null;
        if (data) {
          this.ruleForm.startDate = data[0];
          this.ruleForm.endDate = data[1];
        }
      } else if (type == 2) {
        this.ruleForm.brandId = data
        this.ruleForm.company = this.ruleForm.brandId == -1 ? [] : this.filter.company
        this.ruleForm.dDeptName = this.ruleForm.brandId == -1 ? null : this.filter.dDeptName
      } else if (type == 3) {
        this.ruleForm.company = data
      } else if (type == 4) {
        this.ruleForm.dDeptName = data
      } else if (type == 5) {
        this.ruleForm.brandId2 = data
      } else if (type == 6) {
        this.ruleForm.company2 = data
      } else if (type == 7) {
        this.ruleForm.dDeptName2 = data
      } else if (type == 9) {
        this.ruleForm.monthOrDay = data
      }
      // 判断相差的年数是否超过五年
      // 将日期字符串转换为Date对象
      const date1 = new Date(this.timerange[0]);
      const date2 = new Date(this.timerange[1]);
      // 计算两个日期之间的毫秒差
      const diffInMs = Math.abs(date2 - date1);
      // 将毫秒转换为年
      const diffInYears = diffInMs / (1000 * 60 * 60 * 24 * 365);
      // 检查日期差是否超过五年
      if (diffInYears > 5) {
        this.$message.error('查询日期最大范围为5年');
        return;
      }
      const params = { ...this.ruleForm, ...this.filter, yearMonthDay: null }
      await this.showchart(params)
    },
    async openSumDialog(row, val) {
      this.ruleForm.brandId2 = null;
      this.ruleForm.company2 = [];
      this.ruleForm.company = [];
      this.timerange = []
      this.ruleForm.company = this.filter.company
      this.ruleForm.dDeptName2 = null;
      this.ruleForm.brandId = row.brandId;
      this.ruleForm.dDeptName = row.dDeptName;
      if (val == 1) {
        this.ruleForm.checkList = ['月销成本']
      } else if (val == 2) {
        this.ruleForm.checkList = ['货品成本']
      } else if (val == 3) {
        this.ruleForm.checkList = ['个人库存周转天数']
      } else if (val == 4){
        this.ruleForm.checkList = ['进货仓库存资金']
      } else if (val == 5){
        this.ruleForm.checkList = ['采购在途库存资金']
      } else if (val == 6){
        this.ruleForm.checkList = ['调拨在途资金']
      } else if (val == 7){
        this.ruleForm.checkList = ['个人库存周转天数(新)']
      }
      this.ruleForm.startDate = null;
      this.ruleForm.endDate = null;
      this.timerange = getTime([this.filter.yearMonthDay, this.filter.yearMonthDay])
      this.ruleForm.endDate = this.timerange[1]
      this.ruleForm.startDate = this.timerange[0]
      const params = { ...this.ruleForm, yearMonthDay: this.filter.yearMonthDay }
      await this.showchart(params)
      this.turnoverdaysdialog = true
    },
    //趋势图
    async showchart(params) {
      const { data } = await getPurchaseNewPlanTurnDayAnalysis(params);
      const a = [];
      this.ruleForm.checkList.forEach(item => {
        if (item === '货品成本') {
          a.push("货品成本");
        } else if (item === '月销成本') {
          a.push("月销成本");
        } else if (item === '进货仓库存资金'){
          a.push("进货仓库存资金")
        } else if (item === '采购在途库存资金'){
          a.push("采购在途库存资金")
        } else if (item === '调拨在途资金'){
          a.push("调拨在途资金")
        } else if (item === '个人库存周转天数') {
          a.push("个人周转天数");
        } else if (item === '个人库存周转天数(新)'){
          a.push("个人库存周转天数(新)")
        }
          
      });
      data.legend = data.legend.filter(item => {
        return a.some(str => item.includes(str))
      })
      data.selectedLegend = data.selectedLegend.filter(item => {
        return a.some(str => item.includes(str))
      })
      data.series = data.series.filter(item => {
        return a.some(str => item.name.includes(str))
      })
      this.detailtrendchart.visible = true;
      this.detailtrendchart.data = data
      this.detailtrendchart.title = data.legend[0]
      this.$nextTick(() => {
        if (this.$refs.detailtrendchartref) {
          this.$refs.detailtrendchartref.initcharts();
        }
      });
    },
    //获取选择器数据
    async init() {
      this.listLoading = true
      //采购
      var { data } = await getPurchaseNewPlanTurnDayBrandList();
      this.brandlist = data.map(item => { return { value: item.id, label: item.brandName }; });
      //架构
      let { data: deptList, success } = await getPurchaseNewPlanTurnDayDeptList();
      if (success) {
        this.purchasegrouplist = deptList.map(item => { return { value: item.dept_id, label: item.full_name }; });
      }
      //岗位
      var resPosition = await getBrandPositionList();
      this.positionList = resPosition?.data;
      this.listLoading = false
    },
    //导出
    async onExport() {
      this.sels = []
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = {
        ...pager,
        ...page,
        ...this.filter
      }
      this.listLoading = true
      var res = await exportPurchaseNewPlanTurnDayList(params);
      if (res.success) {
        this.$message({ type: 'success', message: '导出成功!' });
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '采购个人周转天数数据' + new Date().toLocaleString() + '_.xlsx')
      aLink.click()
      this.listLoading = false
    },
    async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist();
    },
    async getlist() {
      this.sels = []
      this.listLoading = true
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = {
        ...pager,
        ...page,
        ...this.filter
      }
      await this.querypopup(params)
      this.listLoading = false
    },
    async querypopup(params) {
      const { data, success } = await getPurchaseNewPlanTurnDayPageList(params)
      if (!success) {
        return
      }
      let a = data.list.filter(item => item.brandName != '☆供应链代发商品');
      let b = data.list.filter(item => item.brandName == '☆供应链代发商品');
      this.list = a.concat(b); //☆供应链代发商品 置放最后一个
      // this.list = data.list
      this.total = data.total;
      this.summaryarry = data.summary;
    },
    // //排序查询
    async sortchange(column) {
      if (!column.order) {
        this.filter.orderBy = 'yearMonthDay';
        this.filter.isAsc = false;
      } else {
        this.filter.orderBy = column.prop
        this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false
        this.getlist();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
//控制选择器多选-标签宽度
::v-deep .el-select__tags-text {
  max-width: 50px;
}

.SettingCss {
  height: 500px;
  padding: 1%;

  .SettingCss_item {
    margin: 1% 0;
  }

  .SettingCss_top {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    height: 30px;
  }

  .SettingCss_top>* {
    margin-right: 8px;
  }

  .SettingCss_bottom {
    padding: 1%;
    margin-bottom: 10px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    flex-wrap: wrap;
  }

}
</style>
