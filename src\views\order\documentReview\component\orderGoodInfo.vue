<template>
    <div class="dialogBox" v-loading="dialogLoading">
        <div class="dialogBox_top">
            <div>
                <div>线上订单号</div>
                <el-tooltip effect="dark" :content="extData.orderNo" placement="top-start">
                    <div class="word">{{ extData.orderNo ? extData.orderNo : '' }}</div>
                </el-tooltip>
            </div>
            <div>
                <div>内部订单号</div>
                <el-tooltip effect="dark" :content="extData.orderNoInner" placement="top-start">
                    <div class="word">{{ extData.orderNoInner ? extData.orderNoInner : '' }}</div>
                </el-tooltip>
            </div>
            <div>
                <div>付款日期</div>
                <el-tooltip effect="dark" :content="extData.timePay" placement="top-start">
                    <div class="word">{{ extData.timePay ? extData.timePay : '' }}</div>
                </el-tooltip>
            </div>
            <div>
                <div>系列编码</div>
                <el-tooltip effect="dark" :content="extData.seriesName" placement="top-start">
                    <div class="word">{{ extData.seriesName ? extData.seriesName : '' }}</div>
                </el-tooltip>
            </div>
        </div>
        <vxetablebase :id="'orderGoodInfo202408041752'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange1' :tableData='dialogTableData' :tableCols='dialogTableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%; height: 200px; margin: 0">
            <template slot="right">
                <vxe-column title="操作" width="120">
                    <template #default="{ row, $index }">
                        <el-button type="text" @click="allocateOperate(row)" v-if="row.allotStatus == 1"
                            :disabled="!(row.orderStatus == '调拨失败')">调拨</el-button>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <my-pagination ref="pager" :total="dialogTotal" @page-change="dialogPagechange" @size-change="dialogSizechange" />
    </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pageGetVoOrder, orderGoodsCodeAllot } from '@/api/vo/VerifyOrder'
const dialogTableCols = [
    { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'quantity', label: '数量', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'orderStatus', label: '订单状态', sortable: 'custom', width: 'auto' },
]
export default {
    name: "orderGoodInfo",
    components: {
        MyContainer, vxetablebase
    },
    props: {
        orderNo: {
            type: String,
            default: ''
        },
        orderNoInner: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            that: this,
            dialogQueryInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                orderNo: null,
                orderNoInner: null,
            },
            extData: {},
            dialogTableData: [],
            dialogTotal: 0,
            dialogTableCols,
            dialogLoading: true,
        }
    },
    mounted() {
        this.dialogQueryInfo.orderNoInner = this.orderNoInner
        this.dialogQueryInfo.orderNo = this.orderNo
        this.openDialog()
    },
    methods: {
        allocateOperate(row) {
            this.$confirm('此操作将改变该商品的调拨状态, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await orderGoodsCodeAllot({ orderNo: row.orderNo, orderNoInner: row.orderNoInner })
                if (success) {
                    this.openDialog(this.dialogQueryInfo)
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消'
                });
            });
        },
        //每页数量改变
        dialogSizechange(val) {
            this.dialogQueryInfo.currentPage = 1;
            this.dialogQueryInfo.pageSize = val;
            this.openDialog()
        },
        //当前页改变
        dialogPagechange(val) {
            this.dialogQueryInfo.currentPage = val;
            this.openDialog()
        },
        async openDialog() {
            this.dialogLoading = true
            const { data, success } = await pageGetVoOrder(this.dialogQueryInfo)
            if (!success) return
            this.extData = data.extData
            this.dialogTableData = data.list
            this.dialogTotal = data.total
            this.dialogLoading = false
        },
        sortchange1({ order, prop }) {
            if (prop) {
                this.dialogQueryInfo.orderBy = prop
                this.dialogQueryInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.openDialog()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.dialogBox {
    display: flex;
    flex-direction: column;

    .dialogBox_top {
        display: flex;
        flex-direction: column;
        margin-bottom: 10px;

        .word {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        div {
            display: flex;


            div {
                width: 80%;
                height: 40px;
                line-height: 40px;
                border: 1px solid #e9e4e4;

                &:nth-child(1) {
                    background-color: rgb(239, 239, 239);
                    width: 20%;
                    border: 1px solid #e9e4e4;
                }
            }
        }
    }
}
</style>