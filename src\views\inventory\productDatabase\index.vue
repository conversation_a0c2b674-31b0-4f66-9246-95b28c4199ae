<!-- <template>
    <my-container v-loading="pageLoading" style="height: 100%">
        <el-tabs v-model="activeName" style="height: 94%">
            <el-tab-pane label="待拍" name="first1" style="height: 100%">
                <daipai ref="daipai" ></daipai>
            </el-tab-pane>
            <el-tab-pane label="已拍" name="first1-1" style="height: 100%" lazy>
                <yipai ref="yipai" ></yipai>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";

import daipai from "@/views/inventory/productDatabase/daipai.vue";
import yipai from "@/views/inventory/productDatabase/yipai.vue";

export default {
    name: "tailorlossindex",
    components: {
        cesTable, MyContainer, MyConfirmButton, daipai, yipai
    },
    data() {
        return {
            that: this,
            pageLoading: false,
            activeName: "first1",
        };
    },
    created() {
    },
    async mounted() {

    },
    methods: {

    },
};
</script>

<style lang="scss" scoped></style> -->
<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item>
                    <!-- <el-input placeholder="商品编码" v-model="filter.goodsCode" clearable style="width: 300px;">
                        <el-tooltip slot="suffix" class="item" effect="dark" content="多重查询用英文逗号,分割"
                            placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input> -->
                    <el-tooltip class="item" effect="dark" content="默认为精确匹配在开始或者结尾输入*进行模糊匹配" placement="bottom">
                        <inputYunhan :key="'3'" :keys="'three'" :width="'130px'" ref="childGoodsCode"
                            v-model="filter.goodsCode" :inputt.sync="filter.goodsCode" placeholder="商品编码"
                            :clearable="true" @callback="(val) => filter.goodsCode = val" title="商品编码"></inputYunhan>
                    </el-tooltip>
                </el-form-item>
                <el-form-item>
                    <el-tooltip class="item" effect="dark" content="模糊匹配：商品名称/商品编码/供应商/创建人/库位" placement="bottom">
                        <el-input placeholder="请输入关键字" v-model="filter.keywords" clearable maxlength="100"
                            style="width: 100px"></el-input>
                    </el-tooltip>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.groupId" style="width: 90px" placeholder="运营组" :clearable="true"
                        :collapse-tags="true" filterable>
                        <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.brandId" clearable filterable placeholder="采购员" style="width: 90px"
                        :collapse-tags="true">
                        <el-option v-for="item in brandlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="checkPermission('Select.BasicGoods.ProviderName')">
                    <el-input placeholder="厂家名称" v-model="filter.providerName" clearable maxlength="70"
                        style="width: 100px"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.isEnabled" style="width: 100px" placeholder="商品状态" :clearable="true">
                        <el-option label="备用" :value="0" />
                        <el-option label="启用" :value="1" />
                        <el-option label="禁用" :value="-1" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input placeholder="添加人" v-model="filter.createdUserName" clearable maxlength="100"
                        style="width: 80px;"></el-input>
                </el-form-item>
                <el-form-item v-if="!filter.hasVedioOrImg">
                    <!-- <el-date-picker type="date" placeholder="创建日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        v-model="filter.createdDay" style="width: 100%;" clearable></el-date-picker> -->
                    <el-date-picker size="mini" v-model="filter.daterange" type="daterange" range-separator="至"
                        start-placeholder="待拍开始时间" value-format="yyyy-MM-dd" end-placeholder="待拍结束时间"
                        style="width: 230px;" :picker-options="pickerOptions" clearable>
                    </el-date-picker>
                </el-form-item>
                <el-form-item v-else-if="filter.hasVedioOrImg">
                    <el-date-picker size="mini" v-model="filter.daterange1" type="daterange" range-separator="至"
                        start-placeholder="已拍开始时间" value-format="yyyy-MM-dd" end-placeholder="已拍结束时间"
                        style="width: 240px;" :picker-options="pickerOptions" clearable @change="changeAlreadyTime">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.isQualificationType" style="width: 90px" placeholder="资质证书"
                        :clearable="true">
                        <el-option label="已上传" :value="true" />
                        <el-option label="未上传" :value="false" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.hasVedioOrImg" style="width: 90px" placeholder="是否已拍" :clearable="false"
                        @change="changeType">
                        <el-option label="待拍" :value="false" />
                        <el-option label="已拍" :value="true" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.goodsState" style="width: 90px" placeholder="编码状态" :clearable="true"
                        v-if="!filter.hasVedioOrImg">
                        <el-option label="换厂待拍" :value="1" />
                        <el-option label="新品待拍" :value="2" />
                        <el-option label="采购降价需要比样" :value="10" />
                        <el-option label="月首单且以往都有采购记录" :value="11" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.warehouseCode" multiple collapse-tags placeholder="仓库"
                        style="width: 140px" clearable filterable>
                        <el-option v-for="item in stashhouseList" :key="item.name" :label="item.name"
                            :value="item.wms_co_id" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">筛选</el-button>
                </el-form-item>
                <el-form-item v-if="!filter.hasVedioOrImg">
                    <el-button type="primary" @click="editProduct({ id: 0 })">新增</el-button>
                </el-form-item>
                <el-form-item>
                    <el-dropdown @command="onExportGoods">
                      <el-button style="height: 28px;" type="primary">
                        导出<i class="el-icon-arrow-down el-icon--right"></i>
                      </el-button>
                      <el-dropdown-menu slot="dropdown" >
                          <el-dropdown-item :command=1>导出全部</el-dropdown-item>
                          <el-dropdown-item :command=2>导出最新一条</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                </el-form-item>
                <div style="margin-bottom: 10px;">
                    <el-form-item v-if="!filter.hasVedioOrImg">
                        <el-button type="primary" @click="onGoodCodeGetSet()">编码抓取设置</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="batchediting()">批量编辑</el-button>
                    </el-form-item>
                    <el-button type="primary" @click="performAction('delete');"
                        v-if="checkPermission('api:inventory:basicgoods:DeleteGoodsDocRecordCgUnrestricted')">批量删除</el-button>
                    <el-button type="primary" @click="performAction('sync');"
                        v-if="(filter.hasVedioOrImg == true) && (checkPermission('Api:Inventory:BasicGoods:SyncGoodsDocRecordCgWeight'))">同步重量</el-button>
                    <el-button type="primary" @click="startImport"
                        v-if="checkPermission('Api:ImportInventory:Purchase:ImportBatchGoodsDocRecordCg')">导入</el-button>
                    <el-button type="primary" @click="onDownloadTemplate">下载导入模版</el-button>
                    <el-button type="primary" @click="printBarcode">打印条码</el-button>
                </div>
            </el-form>
        </template>
        <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <template>
            <ces-table :id="'productDatabase_index202408041601_1'" ref="table" :that='that' :isIndex='true'
                :hasexpand='false' @sortchange='sortchange' @cellClick='cellclick' :tableData='datalist'
                :tableCols='tableCols' :selectColumnHeight="'0px'" :loading="listLoading" :isBorder="false"
                :treeProp="{ rowField: 'id', parentField: 'parentId' }" :hasexpandRight="true"
                :editconfig="{ trigger: 'click', mode: 'cell', showIcon: false }">
                <template #materialImgs="{ row }">
                    <el-image v-if="(row.materialImgs != null) && row.materialImgs.length > 0 && (row.parentId != 0)"
                        :src="row.materialImgs[0]" :preview-src-list="row.materialImgs"
                        style="width: 100px; height: 50px;" />
                </template>
                <template #qualification="{ row }">
                    <el-button type="text" @click="editProduct(row, 3)"
                        v-if="(!row.qualificationType) && (row.parentId != 0)">查看</el-button>
                    <el-button type="text" @click="editProduct(row)"
                        v-if="(!!row.qualificationType) && (row.parentId != 0)">上传</el-button>
                </template>
                <template #testReport="{ row }">
                    <el-button type="text" @click="editProduct(row, 3)"
                        v-if="(!row.testReport) && (row.parentId != 0)">查看</el-button>
                    <el-button type="text" @click="editProduct(row)"
                        v-if="(!!row.testReport) && (row.parentId != 0)">上传</el-button>
                </template>
                <template #productUpdateLog="{ row }">
                    <el-button type="text" @click="getlogList(row)" v-if="(row.parentId != 0)">查看</el-button>
                </template>
                <template slot="right">
                    <vxe-column field="vedioOrImgRemarks" title="待拍备注" :edit-render="{}" width="100"
                        v-if="!filter.hasVedioOrImg">
                        <template #edit="{ row }">
                            <vxe-input v-model="row.vedioOrImgRemarks" type="text" v-throttle="1000" placeholder="请输入备注"
                                @blur="performancescoreverify(row, 1, 2)"
                                @keyup.enter.native="performancescoreverify(row, $event, 3)"
                                maxlength="100"></vxe-input>
                        </template>
                    </vxe-column>
                    <vxe-column title="操作" width="140" fixed="right">
                        <template #default="{ row, $index }">
                            <div style="display: flex;justify-content: center;align-items: center;">
                                <el-button type="text" @click="editProduct(row)"
                                    v-if="(row.parentId != 0)">编辑</el-button>
                                <el-button type="text" @click="deleteProduct(row)"
                                    v-if="(row.parentId != 0)">删除</el-button>
                                <el-button type="text" @click="handlePrint(row)" v-if="row.parentId != 0 && row.goodsNumber">打印</el-button>
                                <el-button type="text" @click="replayMethod(row)" v-if="row.parentId != 0 && row.goodsNumber">补打</el-button>
                            </div>
                        </template>
                    </vxe-column>
                </template>
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getDataList" />
        </template>
        <!-- 历史价格 -->
        <el-dialog title="历史价格" :visible.sync="historyDialog" width="50%" element-loading-text="拼命加载中" v-dialogDrag>
            <my-container>
                <ces-table1 :id="'productDatabase_index202408041601_2'" ref="getHistorytable" :that='that'
                    :isIndex='true' :hasexpand='false' style="height: 500px;" :tableData='historyList'
                    :isSelection="false" :tableCols='tableColsHistory' :isSelectColumn='true'
                    :customRowStyle="customRowStyle" :selectColumnHeight="'0px'" :isBorder="false">
                </ces-table1>
                <template #footer>
                    <my-pagination ref="pagerhistory" :total="totalhistory" @get-page="getHistory" />
                </template>
            </my-container>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="historyDialog = false">关 闭</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 操作日志 -->
        <el-dialog title="产品更新日志" :visible.sync="logListDialog" width="50%" element-loading-text="拼命加载中" v-dialogDrag>
            <my-container>
                <ces-table1 :id="'productDatabase_index202408041601_3'" ref="logtable" :that='that' :isIndex='true'
                    :hasexpand='false' style="height: 500px;" :tableData='logList' :isSelection="false"
                    :tableCols='tableColslog' :isSelectColumn='true' :customRowStyle="customRowStyle"
                    :selectColumnHeight="'0px'" :isBorder="false">
                </ces-table1>
                <template #footer>
                    <my-pagination ref="pagerlog" :total="totallog" @get-page="getlogList" />
                </template>
            </my-container>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="logListDialog = false">关 闭</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 库存仓库 -->
        <el-dialog title="商品仓库" :visible.sync="purchaseDialog" width="50%" element-loading-text="拼命加载中" v-dialogDrag>
            <my-container>
                <ces-table1 :id="'productDatabase_index202408041601_4'" ref="logtable" :that='that' :isIndex='true'
                    :hasexpand='false' style="height: 500px;" :tableData='purchaseList' :isSelection="false" :isRemoteSort="false"
                    :tableCols='tableColspurchase' :isSelectColumn='true' :customRowStyle="customRowStyle"
                    :selectColumnHeight="'0px'" :isBorder="false">
                </ces-table1>
            </my-container>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="purchaseDialog = false">关 闭</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 新增/编辑 -->
        <el-dialog :title="eidtDialogTitle" :visible.sync="eidtDialog" :width="eidtDialogTitle == '编辑商品资料'?'70%':'50%'" element-loading-text="拼命加载中"
            :close-on-click-modal="false" v-dialogDrag style="margin-top: -10vh;">
            <el-form label-width="80px" ref="editFormRef" :model="editForm" :rules="formrules">
                <span style="font-weight: 600">商品基础信息</span>
                <div style="border: 1px solid #eee; width: 100%; padding: 10px; margin-bottom: 10px; box-sizing: border-box;">
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="商品编码" prop="goodsCode">
                                <el-input placeholder="商品编码" :disabled="forbidden" v-model.trim="editForm.goodsCode"
                                    @change="getGoodsDoc" clearable></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="16">
                            <el-form-item label="商品名称" prop="goodsName">
                                <el-input placeholder="商品名称" :disabled="true" v-model.trim="editForm.goodsName"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-if="checkPermission('Select.BasicGoods.ProviderName')">
                        <el-col :span="8" v-if="checkPermission('Select.BasicGoods.ProviderName')">
                            <el-form-item label="厂家名称" prop="supplierId">
                                <el-select v-if="editForm.remark == '原供应商-仓库的货'" v-model="editForm.supplierId"
                                    :disabled="forbidden || fordisable" style="width: 100%" placeholder="请选择"
                                    :clearable="true" @change="getLink(editForm.goodsCode, editForm.supplierId)">
                                    <el-option v-for="(item, i) in providerNames" :label="item.supplier"
                                        :value="item.supplierId" :key="item.supplierId" />
                                </el-select>
                                <el-input v-else placeholder="新供应商" :disabled="forbidden || fordisable"
                                    v-model.trim="editForm.providerName" :maxlength="100"></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item label="厂家地址">
                                <el-input v-model="editForm.supplierAddress" placeholder="厂家地址" maxlength="100" clearable />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="厂家链接" label-width="100px">
                                <el-input v-model.trim="editForm.supplierLink" placeholder="厂家链接" maxlength="100"
                                    clearable />
                            </el-form-item>
                        </el-col>

                    </el-row>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="采购单" prop="buyNo">
                                <el-select v-model="editForm.buyNo" style="width: 100%" placeholder="采购单"
                                    :disabled="forbidden" clearable remote @change="purchaseChange">
                                    <el-option v-for="item in purchaseOrder" :label="item.buyNo" :value="item.buyNo"
                                        :key="item.buyNo" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="成本" prop="costPrice">
                                <el-input-number v-model="editForm.costPrice" :disabled="forbidden || fordisable"
                                    :precision="2" :controls="false" style="width:100%" :min="0" :max="1000000"
                                    class="append_unit" data-unit="元">
                                </el-input-number>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="平均在途时长" prop="deliveryCycle" label-width="100px">
                                <el-input-number v-model="editForm.deliveryCycle" disabled
                                    :precision="1" :controls="false" style="width:100%" :min="0" :max="1000000"
                                    class="append_unit" data-unit="d">
                                </el-input-number>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="仓库" prop="warehouseCode">
                                <el-select v-model="editForm.warehouseCode" :disabled="forbidden || fordisable"
                                    style="width: 100%" placeholder="请选择" :clearable="true"
                                    @change="onEditFormWarehouseChange" filterable>
                                    <el-option v-for="item in warehouseAllList" :label="item.label" :value="item.value"
                                        :key="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="仓位" prop="warePosition">
                                <el-tooltip class="item" effect="dark" content="对应仓库下的历史仓位信息,不会自动同步更新(填入数据之后即可显示该数据)"
                                    placement="top">
                                    <el-select v-model="editForm.warePosition" @blur="selectEnd" style="width: 100%"
                                        placeholder="模糊输入获取关联仓位" :remote-method="onWarePositionMethod"
                                        @clear="locationDataMethod" :clearable="true" filterable remote>
                                        <el-option v-for="item in locationData" :label="item.label" :value="item.value"
                                            :key="item.value" />
                                        <el-option v-for="item in warePositionList" :label="item.label" :value="item.value"
                                            :key="item.value" />
                                    </el-select>
                                </el-tooltip>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="装箱仓位" prop="packingWarePosition" label-width="100px">
                                <el-tooltip class="item" effect="dark" content="对应仓库下的历史仓位信息,不会自动同步更新(填入数据之后即可显示该数据)"
                                    placement="top">
                                    <el-select v-model="editForm.packingWarePosition" @blur="packselectEnd"
                                        style="width: 100%" placeholder="模糊输入获取关联仓位"
                                        :remote-method="packonWarePositionMethod" @clear="clearDataMethod" :clearable="true"
                                        filterable remote>
                                        <el-option v-for="item in clearData" :label="item.label" :value="item.value"
                                            :key="item.value" />
                                        <el-option v-for="item in packingWarehouseLocationList" :label="item.label"
                                            :value="item.value" :key="item.value" />
                                    </el-select>
                                </el-tooltip>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="岗位变更">
                                <el-button type="info" @click="warehouseposition()">点击查看仓库岗位变更</el-button>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="编码状态: " prop="goodsState">
                                {{ codedState }}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="备注信息" prop="remark" label-width="100px">
                                <el-select v-model="editForm.remark" style="width: 100%" placeholder="备注信息"
                                    :disabled="forbidden" @change="() => { editForm.supplierId = '' }">
                                    <el-option label="原供应商-仓库的货" value="原供应商-仓库的货" />
                                    <el-option label="新供应商的货" value="新供应商的货" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <!-- <el-row>



                    </el-row> -->
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="商品图片" prop="pictureUrl">
                                <!-- <YhImgUpload :value.sync="editForm.pictureUrl" :limit="1" :ismultiple="true" ref="img"></YhImgUpload> -->
                                <el-image style="width: 60px; height: 60px" v-if="editForm.pictureUrl"
                                    :src="editForm.pictureUrl" :preview-src-list="[editForm.pictureUrl]">
                                </el-image>
                            </el-form-item>
                        </el-col>
                    </el-row>

                </div>
                <!-- <el-row>



                </el-row> -->
                <!-- <el-row>


                </el-row> -->
                <span style="font-weight: 600;">商品参数信息</span>
                <div style="border: 1px solid #eee; width: 100%; padding: 10px; margin-bottom: 10px; box-sizing: border-box;">
                        <el-row>
                          <el-col :span="8">
                            <el-form-item label="折叠单边是否超40CM" prop="isChao40CM" label-width="163px">
                              <el-select v-model="editForm.isChao40CM" placeholder="请选择折叠单边是否超40CM" clearable style="width: 70%;"
                                @change="onFoldingMethod()">
                                <el-option label="是" :value="1"></el-option>
                                <el-option label="否" :value="0"></el-option>
                              </el-select>
                            </el-form-item>
                          </el-col>
                          <el-col :span="8">
                            <el-form-item label="是否可折叠" prop="isZheDie" label-width="150px">
                              <el-select v-model="editForm.isZheDie" placeholder="请选择是否可折叠" clearable style="width: 70%;"
                                @change="onFoldingMethod()">
                                <el-option label="是" :value="1"></el-option>
                                <el-option label="否" :value="0"></el-option>
                              </el-select>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row v-if="foldingVerification">
                            <el-col :span="8">
                                <el-form-item label="打包长" prop="clLength">
                                    <el-input-number v-model="editForm.clLength" :precision="2" :controls="false"
                                        style="width:80%" :min="0" class="append_unit" data-unit="mm" :max="1000000">
                                    </el-input-number>
                                    <YhImgUpload :value.sync="editForm.clLengthImgs" :limit="10" :ismultiple="true" ref="img2">
                                    </YhImgUpload>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="打包宽" prop="clWidth">
                                    <el-input-number v-model="editForm.clWidth" :precision="2" :controls="false"
                                        class="append_unit" :min="0" style="width:80%" data-unit="mm" :max="1000000">
                                    </el-input-number>
                                    <YhImgUpload :value.sync="editForm.clWidthImgs" :limit="10" :ismultiple="true" ref="img3">
                                    </YhImgUpload>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="打包高" prop="clHeight">
                                    <el-input-number v-model="editForm.clHeight" :precision="2" :controls="false"
                                        style="width:80%" :min="0" class="append_unit" data-unit="mm" :max="1000000">
                                    </el-input-number>
                                    <YhImgUpload :value.sync="editForm.clHeightImgs" :limit="10" :ismultiple="true" ref="img4">
                                    </YhImgUpload>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="展开长" prop="zhankaiMeasuredChang">
                                    <el-input-number v-model="editForm.zhankaiMeasuredChang" :precision="2" :controls="false"
                                        style="width:80%" :min="0" class="append_unit" data-unit="mm" :max="1000000">
                                    </el-input-number>
                                    <YhImgUpload :value.sync="editForm.zhankaiMeasuredChangImages" :limit="10" :ismultiple="true" ref="img6">
                                    </YhImgUpload>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="展开宽" prop="zhankaiMeasuredWidth">
                                    <el-input-number v-model="editForm.zhankaiMeasuredWidth" :precision="2" :controls="false"
                                        class="append_unit" :min="0" style="width:80%" data-unit="mm" :max="1000000">
                                    </el-input-number>
                                    <YhImgUpload :value.sync="editForm.zhankaiMeasuredWidthImages" :limit="10" :ismultiple="true" ref="img7">
                                    </YhImgUpload>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="展开高" prop="zhankaiMeasuredHeight">
                                    <el-input-number v-model="editForm.zhankaiMeasuredHeight" :precision="2" :controls="false"
                                        style="width:80%" :min="0" class="append_unit" data-unit="mm" :max="1000000">
                                    </el-input-number>
                                    <YhImgUpload :value.sync="editForm.zhankaiMeasuredHeightImages" :limit="10" :ismultiple="true" ref="img8">
                                    </YhImgUpload>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="材质">
                                    <el-input v-model="editForm.material" placeholder="材质" style="width: 80%" maxlength="100" clearable />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="测量重" prop="clWeight">
                                    <el-input-number v-model="editForm.clWeight" :precision="2" :controls="false"
                                        style="width:80%" :min="0" class="append_unit" data-unit="g" :max="1000000">
                                    </el-input-number>
                                    <YhImgUpload :value.sync="editForm.clWeightImgs" :limit="10" :ismultiple="true" ref="img1">
                                    </YhImgUpload>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="测量厚" prop="clThickness">
                                    <el-input-number v-model="editForm.clThickness" :precision="2" :controls="false"
                                        class="append_unit" :min="0" style="width:80%" data-unit="mm" :max="1000000">
                                    </el-input-number>
                                    <YhImgUpload :value.sync="editForm.clThicknessImgs" :limit="10" :ismultiple="true"
                                        ref="img5">
                                    </YhImgUpload>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="箱规长">
                                    <el-input-number v-model="editForm.boxRuleLength" :precision="2" :controls="false"
                                        class="append_unit" :min="0" style="width:80%" data-unit="mm" :max="1000000">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="箱规宽">
                                    <el-input-number v-model="editForm.boxRuleWidth" :precision="2" :controls="false"
                                        class="append_unit" :min="0" style="width:80%" data-unit="mm" :max="1000000">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="箱规高">
                                    <el-input-number v-model="editForm.boxRuleHeight" :precision="2" :controls="false"
                                        class="append_unit" :min="0" style="width:80%" data-unit="mm" :max="1000000">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>

                        </el-row>

                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="外包装" prop="externalPacking">
                                    <YhImgUpload3 :value.sync="editForm.externalPacking" :isImg="false"
                                        accept=".jpg,.jpeg,.png,.gif" :limit="3" :ismultiple="true"></YhImgUpload3>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="实物图" prop="goodFrontBackImgs">
                                    <YhImgUpload :value.sync="editForm.goodFrontBackImgs" :limit="3" :ismultiple="true"
                                        ref="goodFrontBackImgs">
                                    </YhImgUpload>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="商品视频" style="margin-top: 10px; " prop="goodsVedio">
                                    <viodeUpload :minisize="false" ref="uploadexl" :limit="1" accepttyes=".mp4"
                                        :uploadprogress="true" @uploadFinish="uploadFinish" />
                                </el-form-item>
                            </el-col>

                        </el-row>
                </div>

                <span style="font-weight: 600;">商品资质信息</span>
                <div style="border: 1px solid #eee; width: 100%; padding: 10px; box-sizing: border-box;">
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="检测报告" prop="testReport" label-width="100px">
                                    <YhImgUpload3 :value.sync="editForm.testReport" :isImg="false"
                                        accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" :limit="10" :ismultiple="true"></YhImgUpload3>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="合格证" prop="qualifiedImgs" label-width="100px">
                                    <YhImgUpload3 :value.sync="editForm.qualifiedImgs" :isImg="false"
                                        accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" :limit="6" :ismultiple="true"></YhImgUpload3>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="执行标准" label-width="100px">
                                    <el-input v-model.trim="editForm.execStandard" placeholder="执行标准" maxlength="100"
                                        clearable />
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="是否专利" label-width="100px">
                                    <!-- <el-switch v-model="editForm.isPatent" active-color="#13ce66" inactive-color="#ff4949">
                                    </el-switch> -->
                                    <el-radio-group v-model="editForm.isPatent">
                                        <el-radio :label="true">是</el-radio>
                                        <el-radio :label="false">否</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="资质证书" prop="qualificationType" label-width="100px">
                                    <YhImgUpload3 :value.sync="editForm.qualificationType" :isImg="false"
                                        accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" :limit="10" :ismultiple="true"></YhImgUpload3>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>
                        <el-form-item label="专利控价" label-width="100px">
                            <el-input type="textarea" placeholder="请输入专利控价内容" v-model="editForm.controlPriceDescribe" maxlength="500" show-word-limit :autosize="{ minRows: 4, maxRows: 4}"></el-input>
                        </el-form-item>
                        </el-row>

                        <el-row>
                            <el-form-item label="备注" prop="otherRemark" label-width="100px">
                                <el-input placeholder="备注" v-model.trim="editForm.otherRemark" clearable type="textarea" rows="2" maxlength="500" show-word-limit
                                     style="width:100%"></el-input>
                                <br/><el-button type="text" @click="otherRemarkClickShow(editForm.id)"
                                    v-if="!!editForm.id">备注历史</el-button>
                            </el-form-item>
                        </el-row>
                </div>



                <!-- <el-row>


                    <el-col :span="8">
                        <el-form-item label="标准装箱数" label-width="90px">
                          <el-input v-model="editForm.packCount" placeholder="标准装箱数" maxlength="100" clearable disabled />
                        </el-form-item>
                    </el-col>
                </el-row> -->
                <!-- <el-row>



                </el-row> -->
                <!-- <el-row>

                    <el-col :span="8">
                        <el-form-item label="是否控价">
                            <el-switch v-model="editForm.isControlPrice" active-color="#13ce66"
                                inactive-color="#ff4949">
                            </el-switch>
                        </el-form-item>
                    </el-col>
                </el-row> -->

                <!-- <el-row>


                </el-row>
                <el-row>

                </el-row>
                <el-row>


                </el-row>
                <el-row>



                </el-row> -->
                <!-- <el-row>
                    <el-col :span="12">
                        <el-form-item label="控价图片" prop="testReport" v-if="editForm.isControlPrice">
                            <YhImgUpload :value.sync="editForm.controlPricePicture" :limit="5" :ismultiple="true"
                                ref="controlPricePicture">
                            </YhImgUpload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="材料名称" style="margin-top: 10px; " prop="goodsDocRecordCgBcId">
                    <el-select v-model="editForm.goodsDocRecordCgBcId" placeholder="请选择" filterable clearable
                        @change="changeInfo">
                        <template #default>
                            <el-tooltip class="item" effect="dark" :content="item.label" placement="top-start"
                                v-for="item in selectList">
                                <el-option class="selectBox" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-tooltip>
                        </template>
                    </el-select>
                </el-form-item>

                <el-form-item label="材料信息" style="margin-top: 10px; ">
                    <div class="materialInfoBox">
                        <div class="materialInfoBox_item" v-for="item in materialInfo">
                            <el-image :src="item.materialImg" class="materialImg"></el-image>
                            <div>{{ item.materialName }}</div>
                            <div>{{ item.materialSize }}</div>
                        </div>
                    </div>
                </el-form-item> -->



                <!-- <el-row>


                </el-row> -->
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="eidtDialog = false">取 消</el-button>
                    <el-button type="primary" :loading="subLoad" @click="saveGoodsDoc"
                        v-if="editmode != 3">保存</el-button>
                </span>
            </template>
        </el-dialog>
        <!--视频播放-->
        <el-dialog title="视频播放" :visible.sync="videoDialogVisible" width="50%" @close="closeVideoPlyer"
            :append-to-body="true" v-dialogDrag>
            <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeVideoPlyer">关闭</el-button>
            </span>
        </el-dialog>
        <!-- 销量 -->
        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
        <!-- 编码抓取设置 -->
        <el-dialog :title="goodCodeGetSetDialog.title" :visible.sync="goodCodeGetSetDialog.visible" width="33%"
            v-dialogDrag>
            <div>
                <div style="display: flex;justify-content: space-between;">
                    <div>
                        <span v-for="(item, i) in districtList" :key="i"
                            :style="{ margin: '0 7px', cursor: 'pointer', color: (i === regional ? '#409EFF' : 'black') }"
                            @click="onAreaSwitching(item, i)" style="position: relative;">
                            {{ item.region }}
                            <span class="del" @click.stop="ondelete(item, i)">x</span>
                        </span>
                    </div>
                    <el-button type="text" @click="onNewArea">新增地区</el-button>
                </div>
                <div>
                    <el-select v-model="wmsId" placeholder="仓库" multiple collapse-tags clearable
                        style="margin-right: 15px;width: 40%;">
                        <el-option v-for="item in warehouseList" :key="item.name" :label="item.name"
                            :value="item.wms_co_id" />
                    </el-select>
                    <el-button type="primary" @click="onNewWarehouse">新增</el-button>
                </div>
                <div style="border: 1px solid #dcdfe6;border-radius: 5px;height: 150px;margin: 10px 0;">
                    <el-scrollbar style="height: 100%;">
                        <el-tag v-for="(item, i) in warehouseLabelList" :key="item.warehouseName" closable
                            style="margin: 5px;" @close="handleClose(item, i)">
                            {{ item.warehouseName }}
                        </el-tag>
                    </el-scrollbar>
                </div>
            </div>
            <div>
                <el-form class="ad-form-query" :inline="true" :model="goodCodeGetSetDialog.setData">
                    <div>
                        <el-form-item label="一键抓取" prop="isGet" style="margin-right: 20px;">
                            <el-switch v-model="goodCodeGetSetDialog.setData.isGet" active-text="开" inactive-text="关">
                            </el-switch>
                        </el-form-item>
                        <el-form-item label="连续进货" prop="continuousNum" style="margin-right: 20px;">
                            <el-input-number v-model="goodCodeGetSetDialog.setData.continuousNum" :precision="0"
                                :min="0" :max="10">
                            </el-input-number>
                        </el-form-item>
                        <el-form-item label="调拨开关" prop="isAllot" v-if="false">
                            <el-switch v-model="goodCodeGetSetDialog.setData.isAllot" active-text="开" inactive-text="关">
                            </el-switch>
                        </el-form-item>
                        <el-button @click="getGoodCode">自定义抓取条件</el-button>
                    </div>
                    <div style="height: 150px;padding: 2% 0;">
                        <div style="display: flex; align-items: center;">
                            <el-checkbox v-model="goodCodeGetSetDialog.setData.isChangeMerchant"
                                @change="factoryChange">抓取换厂编码</el-checkbox>
                            <i :class="checkHiding ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
                                style="cursor: pointer;margin-left: 2%;font-size: 20px;" @click="checkHiddenEvent"></i>
                        </div>
                        <div style="margin: 3% 0;">
                            <el-checkbox v-model="goodCodeGetSetDialog.setData.isEqualSupplier" v-show="checkHiding"
                                :disabled="!goodCodeGetSetDialog.setData.isChangeMerchant">三个月内供应商变回</el-checkbox>
                            <el-checkbox v-model="goodCodeGetSetDialog.setData.isAllot" v-show="checkHiding"
                                :disabled="!goodCodeGetSetDialog.setData.isChangeMerchant">不抓取调拨编码</el-checkbox>
                            <el-checkbox v-model="goodCodeGetSetDialog.setData.isLabelKeyWords" v-show="checkHiding"
                                :disabled="!goodCodeGetSetDialog.setData.isChangeMerchant">自定义标签剔除</el-checkbox>
                            <el-checkbox v-model="goodCodeGetSetDialog.setData.isNameKeyWords" v-show="checkHiding"
                                :disabled="!goodCodeGetSetDialog.setData.isChangeMerchant">自定义名称剔除</el-checkbox>
                        </div>
                        <div style="display: flex; align-items: center;margin: 3% 0;">
                            <el-checkbox v-model="goodCodeGetSetDialog.setData.isGrabNewPro"
                                @change="grabNewChange">抓取新品编码</el-checkbox>
                            <i :class="checkNewCode ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
                                style="cursor: pointer; margin-left: 2%; font-size: 20px;" @click="grabCoding"></i>
                        </div>
                        <div>
                            <el-checkbox v-model="goodCodeGetSetDialog.setData.isNewProAllot" v-show="checkNewCode"
                                :disabled="!goodCodeGetSetDialog.setData.isGrabNewPro">不抓取调拨编码</el-checkbox>
                            <el-checkbox v-model="goodCodeGetSetDialog.setData.isNewLabelKeyWords" v-show="checkNewCode"
                                :disabled="!goodCodeGetSetDialog.setData.isGrabNewPro">自定义标签剔除</el-checkbox>
                            <el-checkbox v-model="goodCodeGetSetDialog.setData.isNewNameKeyWords" v-show="checkNewCode"
                                :disabled="!goodCodeGetSetDialog.setData.isGrabNewPro">自定义名称剔除</el-checkbox>
                        </div>
                    </div>
                </el-form>
            </div>

            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="saveGoodCodeGetSet"
                    :loading="goodCodeGetSetDialog.loading">保存</el-button>
                <el-button @click="goodCodeGetSetDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="备注历史" :visible.sync="otherRemarkDialog.visible" width="35%" v-dialogDrag>
            <el-container style="height:300px;">
                <el-main style="height:300px;">
                    <el-table :data="otherRemarkDialog.data" :height="220">
                        <el-table-column label="序号" width="50">
                            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                        </el-table-column>
                        <el-table-column prop="otherRemarkMan" label="备注人" width="100" />
                        <el-table-column prop="otherRemarkTime" label="备注时间" width="150" />
                        <el-table-column prop="otherRemarkText" label="备注内容" width="300" />
                    </el-table>
                </el-main>
            </el-container>
        </el-dialog>

        <el-dialog title="仓库仓位变更信息查看" :visible.sync="warehousepositionchange" width="55%" v-dialogDrag>
            <el-table :data="epositionlist" max-height="250" style="width: 100%;margin-top:10px">
                <el-table-column type="index" width="50" sortable></el-table-column>
                <el-table-column width="100" property="modifier" label="修改人" sortable></el-table-column>
                <el-table-column width="160" property="modifyTime" label="修改时间" sortable></el-table-column>
                <el-table-column width="350" property="originalValue" label="修改前" sortable></el-table-column>
                <el-table-column width="350" property="newValue" label="修改后" sortable></el-table-column>
            </el-table>
        </el-dialog>

        <el-dialog title="批量编辑商品资料" :visible.sync="dialogredact" width="35%" v-dialogDrag>
            <el-form :close-on-click-modal="false">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item label="备注" prop="otherRemark">
                            <el-input style="width: 90%; height:60px" type="textarea" :maxlength="300" :rows="2"
                                placeholder="请输入内容" v-model="batch.otherRemark" :autosize="{ minRows: 2, maxRows: 4 }">
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="资质证书" prop="qualificationType">
                            <span style="position: absolute; top: 30px;left:1px;color: red ;">覆盖上传</span>
                            <YhImgUpload3 :value.sync="batch.qualificationType" :isImg="false"
                                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" :limit="99" :ismultiple="true"></YhImgUpload3>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="合格证" prop="qualifiedImgs">
                            <span style="position: absolute; top: 30px;left:-3px;color: red ;">覆盖上传</span>
                            <YhImgUpload3 :value.sync="batch.qualifiedImgs" :isImg="false"
                                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" :limit="99" :ismultiple="true"></YhImgUpload3>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="batcheditingsave">保存</el-button>
                <el-button @click="dialogredact = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="图片预览" :visible.sync="viewImageVisiable" width="50%" v-dialogDrag>
            <div class="imgBox">
                <el-image :src="imgInfo.url ? imgInfo.url : ''" class="imgcss"></el-image>
                <el-tooltip class="item" effect="dark" :content="imgInfo.name ? imgInfo.name : ''"
                    placement="top-start">
                    <div class="imgBox_item1">{{ imgInfo.name ? imgInfo.name : '' }}</div>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" :content="imgInfo.other1 ? imgInfo.other1 : ''"
                    placement="top-start">
                    <div class="imgBox_item2">{{ imgInfo.other1 ? imgInfo.other1 : '' }}</div>
                </el-tooltip>
                <div class="imgBox_item3">{{ imageIndex + 1 }}</div>
                <i class="el-icon-arrow-left" @click="changeLocation('left')" />
                <i class="el-icon-arrow-right" @click="changeLocation('right')" />
            </div>
        </el-dialog>

        <el-dialog :visible.sync="leadDialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
            <div slot="title" class="header-title">
                <span>导入数据</span>
            </div>
            <div style="height: 50px;margin: 30px 0;">
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                    :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                </el-upload>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="leadDialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog :visible.sync="graspingCondition" width="20%" v-dialogDrag append-to-body>
            <div style="height: 200px;width: 100%;padding: 5% 3%;">
                <el-radio-group v-model="tabEncoding" style="margin-bottom: 30px;" @change="codeSwitching">
                    <el-radio-button label="first1">换厂编码</el-radio-button>
                    <el-radio-button label="first2">新品编码</el-radio-button>
                </el-radio-group>
                <div style="margin: 10% 0;">
                    <span style="margin-right: 10px;">排除标签关键字</span>
                    <el-input v-show="verifykeyword" v-model.trim="encoding.labelKeyWords" placeholder="请输入"
                        maxlength="50" clearable style="width: 60%;" />
                    <el-input v-show="!verifykeyword" v-model.trim="encoding.newLabelKeyWords" placeholder="请输入"
                        maxlength="50" clearable style="width: 60%;" />
                </div>
                <div>
                    <span style="margin-right: 10px;">排除名称关键字</span>
                    <el-input v-show="verifykeyword" v-model.trim="encoding.nameKeyWords" placeholder="请输入"
                        maxlength="50" clearable style="width: 60%;" />
                    <el-input v-show="!verifykeyword" v-model.trim="encoding.newNameKeyWords" placeholder="请输入"
                        maxlength="50" clearable style="width: 60%;" />
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="graspingCondition = false">取 消</el-button>
                <el-button type="primary" @click="onSaveEvent">确 定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="新增地区" :visible.sync="area.dialog" width="20%" v-dialogDrag append-to-body>
            <div
                style="height: 100px;width: 100%;display: flex;justify-content: center;align-items: center;padding-bottom: 20px;">
                <el-input ref="regioninput" v-model.trim="area.region" placeholder="请输入" maxlength="50" clearable
                    style="width: 60%;" />
            </div>
            <div style="display: flex; justify-content: center; gap: 20px;">
                <el-button @click="area.dialog = false">取 消</el-button>
                <el-button type="primary" @click="onStorageMethod">确 定</el-button>
            </div>
        </el-dialog>

        <el-dialog title="打印" :visible.sync="printVisible" width="40%" v-dialogDrag @close="printCloseMethod">
          <div style="height: 500px;display: flex;flex-direction: column;">
            <div>
              <el-select v-model="printSize" placeholder="请选择打印尺寸" clearable style="width: 110px;margin-right: 5px;"
                @change="changePrintSize($event, 2)">
                <el-option v-for="size in printSizeList" :key="size" :label="size + '*' + size" :value="size" />
              </el-select>
              高(厘米):
              <el-input-number v-model="qrCodeHeight" :min="1" :max="999" :controls="false" :precision="1"
                placeholder="请输入高度(厘米)" style="width:90px;margin-right: 5px;" @blur="onPrintMethod(1)" />
              长(厘米):
              <el-input-number v-model="qrCodeWidth" :min="1" :max="999" :controls="false" :precision="1"
                placeholder="请输入长度(厘米)" style="width:90px;margin-right: 5px;" @blur="onPrintMethod(1)" />
                <el-input-number v-model="numQRCode" :min="1" :max="2000" placeholder="二维码张数"
                  style="width: 20%;margin-right: 5px;" :precision="0" v-if="!islinePrinting"></el-input-number>
                <el-button v-if="!islinePrinting" type="primary" @click="generateQRCode">生成</el-button>
              <el-button type="primary" @click="printQRCode('#printid')">打印</el-button>
            </div>
            <div style="margin-top: 5px;">
              <el-switch @change="onPrintMethod(6)" v-model="switchshow" active-text="显示id" inactive-text="隐藏id">
              </el-switch>
            </div>
            <el-scrollbar ref="refscrollbar" style="height: 95%;margin-top: 10px;">
              <div id="printid" v-if="printVisible" v-loading="printLoading" :style="qrcodeContainerStyle">
                <div v-for="(item, index) in goodsCodeList" :key="index" class="qrcode-item">
                  <img :id="'barcode1' + item" :style="qrcodeContainerStyle" />
                </div>
              </div>
            </el-scrollbar>
          </div>
        </el-dialog>

        <el-dialog title="补打" :visible.sync="replayInfo.visible" width="45%" v-dialogDrag style="margin-top: -7vh;">
          <replayComponent ref="refreplayComponent" v-if="replayInfo.visible" :rowData="replayInfo.row" />
        </el-dialog>
    </my-container>
</template>
<script>
import inputYunhan from "@/components/Comm/inputYunhan";
import { Loading } from 'element-ui';
import dayjs from "dayjs";
import cesTable from "@/components/VxeTable/vxetablebase.vue";
import cesTable1 from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import {
    pageGoodsDocRecordCgList, saveGoodsDocRecordCg, getGoodsDocCgProvidersDto, pageGoodsPriceList,
    pageGoodsDocRecordCgChangeLogList, getPurchaseNewPlan2ByGoodsCode, saveVedioOrImgRemarks, deleteGoodsDocRecordCgUnrestricted, getPurchaseOrderList, syncGoodsDocRecordCgWeight,
    getTbWarehouseList, getTbWarePositionList, deleteGoodsDocRecordCg, exportGoodsDocRecordCgListGoods, pageGetChangeLogs,
    getGoodsDocRecordCgSet, saveGoodsDocRecordCgSet, getGoodsDocRecordCgOtherRemarkListByParnetId, batchUpdateGoodsDocRecordCg, getGoodsDocRecordCgBcPageList, getGoodsDocRecordCgBcById, getAliGoodsLink, getGoodsDocRecordOther
} from "@/api/inventory/basicgoods"
import { getBathSuiJiGoodsCode } from '@/api/inventory/sampleGoods';
import {getUserInfo} from "@/api/operatemanage/productalllink/alllink";
import { getPurOrderAnalysis, } from "@/api/inventory/goodscodestock"
import { formatTime, formatSecondNewToHour, downloadLink } from "@/utils/tools";
import { getGroupKeyValue } from "@/api/operatemanage/base/product";
import { getAllProBrand, getAllWarehouse } from '@/api/inventory/warehouse'
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import YhImgUpload3 from "@/components/upload/yh-img-upload3.vue";
import { replaceSpace } from '@/utils/getCols'
import viodeUpload from "@/views/media/shooting/uploadfile1.vue";
import buschar from '@/components/Bus/buschar'
import { importBatchGoodsDocRecordCg } from '@/api/inventory/purchaseImport'
import { printJS } from '@/utils/print'
import printQRCode from "@/utils/printQRCode";
import _ from 'lodash'
import decimal from '@/utils/decimal'
import request from '@/utils/request'
import { getTrendChart } from '@/api/vo/orderGoods'
import replayComponent from "@/views/inventory/sampleRoom/components/replayComponent.vue"

const tableCols = [
    { istrue: true, label: "", type: "checkbox", width: 60, align: 'left' },
    { istrue: true, prop: 'goodsCode', align: 'left', label: '商品编码', sortable: 'custom', treeNode: true, width: '130', fixed: 'left', },
    { istrue: true, prop: 'codeCount', align: 'left', label: '编码数量', sortable: 'custom', width: '100', },
    { istrue: true, prop: 'warehouseCode', align: 'left', label: '仓库', width: '100', formatter: (row) => !row.warehouseCode ? '' : row.warehouseName },
    { istrue: true, prop: 'goodsName', align: 'left', label: '商品名称', width: '100', },
    { istrue: true, prop: 'materialImgs', align: 'left', label: '包装图片', width: '100' },
    { istrue: true, prop: 'pictureUrl', align: 'left', label: '商品图片', type: "images", width: '100', },
    { istrue: true, prop: 'externalPacking', align: 'left', label: '外包装图片', type: "images", width: '120', },
    { istrue: true, prop: 'goodFrontBackImgs', align: 'left', label: '实物图片', type: "images", width: '120', },
    { istrue: true, sortable: 'custom', width: '100', align: 'center', prop: 'isChao40CM', label: '折叠单边是否超40CM', formatter: (row) => row.isChao40CM == 1 ? '是' : row.isChao40CM == 0 ? '否' : '', },
    { istrue: true, sortable: 'custom', width: '100', align: 'center', prop: 'isZheDie', label: '是否可折叠', formatter: (row) => row.isZheDie == 1 ? '是' : row.isZheDie == 0 ? '否' : '', },
    { istrue: true, prop: 'clWeight', align: 'left', label: '测量重', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'clWeightImgs', align: 'left', label: '测重图', width: '70', type: "images", },
    { istrue: true, prop: 'clLength', align: 'left', label: '打包长', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'clLengthImgs', align: 'left', label: '测打包长图', width: '70', type: "images", },
    { istrue: true, prop: 'clWidth', align: 'left', label: '打包宽', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'clWidthImgs', align: 'left', label: '测打包宽图', width: '70', type: "images", },
    { istrue: true, prop: 'clHeight', align: 'left', label: '打包高', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'clHeightImgs', align: 'left', label: '测打包高图', width: '70', type: "images", },
    { istrue: true, prop: 'zhankaiMeasuredChang', align: 'left', label: '展开长', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'zhankaiMeasuredChangImages', align: 'left', label: '测展开长图', width: '70', type: "images", },
    { istrue: true, prop: 'zhankaiMeasuredWidth', align: 'left', label: '展开宽', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'zhankaiMeasuredWidthImages', align: 'left', label: '测展开宽图', width: '70', type: "images", },
    { istrue: true, prop: 'zhankaiMeasuredHeight', align: 'left', label: '展开高', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'zhankaiMeasuredHeightImages', align: 'left', label: '测展开高图', width: '70', type: "images", },
    {
        istrue: true, prop: 'goodsVedio', align: 'left', label: '商品视频', sortable: 'custom', type: 'html', width: '70',
        formatter: (row) => { return row.goodsVedio != null && row.goodsVedio.length > 0 ? '<i class="el-icon-video-play"></i>' : '' },
    },
    {
        istrue: true, prop: 'qualification', label: '资质证书', width: '80',
    },
    { istrue: true, prop: 'qualifiedImgs', align: 'left', label: '合格证', width: '70', type: "images", },
    { istrue: true, prop: 'goodsState', align: 'left', label: '编码状态', sortable: 'custom', width: '120', formatter: (row) => row.goodsStateStr },
    { istrue: true, prop: 'createdTime', align: 'left', label: '待拍创建时间', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'mediaTime', align: 'left', label: '已拍创建时间(拍摄时间)', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'mediaCount', align: 'left', label: '拍摄次数', width: '100' },
    { istrue: true, prop: 'bitNumber', align: 'left', label: '样品间仓位', width: '100' },
    { istrue: true, prop: 'buyNo', align: 'left', label: '采购单号', width: '100' },
    // { istrue: true, prop: 'createdTime', align: 'left', label: '创建日期', sortable: 'custom', formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD'), width: '70', },
    { istrue: true, prop: 'providerName', align: 'left', label: '厂家名称', sortable: 'custom', width: '100', permission: 'Select.BasicGoods.ProviderName', },
    { istrue: true, prop: 'costPrice', align: 'left', width: '70', label: '单价', handle: (that, row) => { that.historyparams = row.goodsCode; that.getHistory(); }, sortable: 'custom', },
    { istrue: true, prop: 'inventoryQty', align: 'left', label: '主仓库存', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.getPurchase(row.goodsCode) },
    { istrue: true, prop: 'saleQty', align: 'left', label: '销量', width: '70', sortable: 'custom', type: 'click', handle: (that, row) => that.getbirchart(row.goodsCode, 30) },
    { istrue: true, prop: 'remark', align: 'left', label: '备注信息', sortable: 'custom', width: '100', },
    {
        istrue: true, prop: 'productUpdateLog', label: '产品更新日志', width: '120',
    },
    { istrue: true, prop: 'isEnabled', align: 'left', label: '商品状态', sortable: 'custom', width: '100', formatter: (row) => row.parentId == 0 ? '' : row.isEnabled == 0 ? '备用' : row.isEnabled == 1 ? '启用' : '禁用', },
    { istrue: true, prop: 'groupName', align: 'left', label: '运营组', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'brandName', align: 'left', label: '采购员', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'brandDeptName', align: 'left', label: '采购组', width: '100' },
    { istrue: true, prop: 'packingWarePosition', align: 'left', label: '装箱仓位', width: '100' },
    { istrue: true, prop: 'warehouseStock', align: 'left', label: '仓库库存', width: '100' },
    { istrue: true, prop: 'sellStock', align: 'left', label: '可售库存', width: '100' },
    { istrue: true, prop: 'inTransitNum', align: 'left', label: '在途', width: '100' },
    { istrue: true, prop: 'deliveryCycle', align: 'left', label: '在途时长', width: '100', formatter: (row) => formatSecondNewToHour(row.deliveryCycle) },
    { istrue: true, prop: 'orderUseQty', align: 'left', label: '订单占有', width: '100' },
    { istrue: true, prop: 'supplierAddress', align: 'left', label: '厂家地址', width: '100', permission: 'Select.BasicGoods.ProviderName' },
    { istrue: true, prop: 'supplierLink', align: 'left', label: '厂家链接', width: '100', permission: 'Select.BasicGoods.ProviderName' },
    { istrue: true, prop: 'testReport', align: 'left', label: '检测报告', width: '100' },
    { istrue: true, prop: 'execStandard', align: 'left', label: '执行标准', width: '100' },
    { istrue: true, prop: 'material', align: 'left', label: '材质', width: '100' },
    { istrue: true, prop: 'boxRule', align: 'left', label: '箱规', width: '100' },
    { istrue: true, prop: 'isPatent', align: 'left', label: '是否专利', width: '100', formatter: (row) => row.isPatent == true ? '是' : row.isPatent == false ? '否' : '' },
    // { istrue: true, prop: 'isControlPrice', align: 'left', label: '是否控价', width: '100', formatter: (row) => row.isControlPrice == true ? '是' : row.isControlPrice == false ? '否' : '' },
    { istrue: true, prop: 'controlPriceDescribe', align: 'left', label: '专利控价', width: '100' },
    // { istrue: true, prop: 'controlPricePicture', align: 'left', label: '控价图片', width: '100', type: "images", },
];
const tableColslog = [
    { istrue: true, prop: 'goodsCode', align: 'left', label: '商品编码', sortable: true, width: '140' },
    { istrue: true, prop: 'changeContent', align: 'left', label: '修改内容', sortable: true, },
    { istrue: true, prop: 'editUserName', align: 'left', label: '操作人', sortable: true, width: '100' },
    { istrue: true, prop: 'editTime', align: 'left', label: '操作时间', sortable: true, width: '140' },

];
const tableColsHistory = [
    { istrue: true, prop: 'buyNo', align: 'left', label: '采购单号', sortable: true, },
    { istrue: true, prop: 'purchaseDate', align: 'left', label: '采购日期', sortable: true, },
    { istrue: true, prop: 'supplier', align: 'left', label: '供应商', sortable: true, },
    { istrue: true, prop: 'count', align: 'left', label: '数量', sortable: true, },
    { istrue: true, prop: 'price', align: 'left', label: '单价', sortable: true, },
]
const tableColspurchase = [
    { istrue: true, prop: 'goodsCode', align: 'left', label: '商品编码' },
    {
        istrue: true, prop: 'warehouseName', align: 'left', label: '仓库', sortable: true,
        formatter: (row) => { return row.warehouse == -1 ? '全仓' : row.warehouseName }
    },
    { istrue: true, prop: 'masterStock', align: 'left', label: '库存数', sortable: true },
]
export default {
    name: "productDatabase",//商品资料库
    components: {
        MyContainer, cesTable, videoplayer, YhImgUpload, YhImgUpload3, viodeUpload, buschar, inputYunhan, cesTable1, replayComponent
    },
    data() {
        return {
            foldingVerification: true,
            replayInfo: {
              visible: false,
              row: {},
            },
            districtList: [],//地区列表
            warehouseList: [],//仓库列表
            stashhouseList: [],//仓位列表
            wmsId: [],//仓库id
            warehouseLabelList: [],//仓库标签列表
            regional: 0,//地区索引
            area: {
                dialog: false,//新增地区弹窗
                region: ''//地区名称
            },
            verifykeyword: true,
            encoding: {
                nameKeyWords: null,
                labelKeyWords: null,
                newLabelKeyWords: null,
                newNameKeyWords: null,
            },
            tabEncoding: 'first1',
            graspingCondition: false,
            leadDialogVisible: false,
            fileList: [],
            uploadLoading: false,
            fileparm: {},
            checkNewCode: true,
            checkHiding: true,
            fordisable: false,//输入采购单后是否禁用
            forbidden: true,
            purchaseOrder: [],//采购单
            clearData: [],
            locationData: [],
            codedState: null,//编码状态
            place: null,
            index1: null,
            editNote: false,
            scene: null,
            epositionlist: [],
            changepage: { isAsc: true, orderBy: "", currentPage: 1, pageSize: 50 },
            warehousepositionchange: false,
            dialogredact: false,
            warePositionmy: '',
            packingWarePositionmy: '',
            videoDialogVisible: false,
            videoplayerReload: false,
            videoUrl: null,//视频地址
            // 历史价格
            historyDialog: false,
            historyList: [],
            totalhistory: 0,
            historyparams: null,
            tableColsHistory: tableColsHistory,
            // 库存仓库
            purchaseDialog: false,
            purchaseList: [],
            totalpurchase: 0,
            purchaseparams: null,
            tableColspurchase: tableColspurchase,
            // 操作日志
            logListDialog: false,
            logList: [],
            logparams: null,
            totallog: 0,
            tableColslog: tableColslog,
            buscharDialog: { visible: false, title: "", data: [] },
            filterchart: {
                startDate: null,
                endDate: null,
                timerange: []
            },
            eidtDialog: false,
            eidtDialogTitle: '',
            subLoad: false,
            providerNames: [],
            warehouseAllList: [],
            warePositionList: [],
            wareBeforePositionList: [],
            packingWarehouseLocationList: [],
            filter: {
                goodsCode: '',
                groupId: null,
                brandId: null,
                providerName: '',
                isEnabled: null,
                createdDay: '',
                createdUserName: '',
                keywords: '',
                daterange: [],
                daterange1: [],
                daterange2: [],
                createdTimeStart: '',
                createdTimeEnd: '',
                hasVedioOrImg: false,
                startMediaTime: null,
                endMediaTime: null,
                goodsState: null,
            },
            editmode: 0,
            editForm: {
                packingWarePosition: null,
                buyNo: null,//采购单号
                goodsState: null,
                supplierId: null,
                costPrice: null,
                providerName: null,
                goodsCode: null,
                isPatent: false,
                isControlPrice: false,
                supplierLink: null,
                packCount: null,
            },
            formrules: {
                goodsCode: [{ required: true, message: '请填写商品编码', trigger: 'blur' }],
                remark: [{ required: true, message: '请填写备注信息', trigger: 'blur' }],
                warehouseCode: [{ required: true, message: '请填写仓库', trigger: 'blur' }],
                // warePosition: [{ required: true, message: '请填写仓位', trigger: 'blur' }],
                // clWeight: [{ required: true, message: '请填写测量重', trigger: 'blur' }],
                // goodFrontBackImgs: [{ required: true, message: '请填写正反面图', trigger: 'blur' }]
                isChao40CM: [{ required: true, message: '请选择折叠单边是否超40CM', trigger: 'change' }],
                isZheDie: [{ required: true, message: '请选择是否可折叠', trigger: 'change' }],
            },
            summaryarry: {},
            datalist: [
            ],
            total: 0,
            sels: [], // 列表选中列
            listLoading: false,
            that: this,
            pageLoading: false,
            pager: {},
            tableCols: tableCols,
            groupList: [],
            brandlist: [],
            pickerOptions: {
                shortcuts: [{
                    text: '近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            goodCodeGetSetDialog: {
                regionWarehouseJson: [],
                visible: false, title: "编码抓取设置", loading: false,
                setData: { isGet: false, continuousNum: 3, isAllot: true, isEqualSupplier: false, isChangeMerchant: false, isAllot: false, isGrabNewPro: false, isNewProAllot: false, isLabelKeyWords: false, isNameKeyWords: false, isNewLabelKeyWords: false, isNewNameKeyWords: false }
            },
            batch: {
                qualificationType: null,
                otherRemark: null,
                qualifiedImgs: null,
            },
            otherRemarkDialog: { visible: false, title: "", data: [] },
            viewImageVisiable: false,
            viewImageList: [],
            imgInfo: {
                url: null,
                name: null,
                other1: null
            },
            imageIndex: null,
            selectList: [],
            printVisible: false,
            printLoading: false,
            printSizeList: [10, 8, 5, 4, 3, 2.5, 1.5],
            printSize: 4,
            qrCodeWidth: 4,
            qrCodeHeight: 4,
            numQRCode: 0,
            scrollbarWidth: 0,
            rowDisplayNum: 1,
            goodsCodeList: [],
            switchshow: true,
            materialInfo: []
        };
    },
    watch: {
      printVisible(val) {
        if (val) {
          this.getScrollbarWidth()
        }
      },
    },
    async created() {

    },
    computed: {
      qrcodeContainerStyle() {
        const containerWidth = this.scrollbarWidth
        const qrCodeWidthWithMargin = this.qrCodeWidth * 37.795 + 20; // 200px宽度 + 20px的间距
        const maxRowDisplayNum = Math.floor(containerWidth / qrCodeWidthWithMargin); // 最大能显示的二维码个数
        return {
          width: `${decimal(decimal(this.qrCodeWidth, 37.795, 0, '*'), 90, 0, '+')}px`,
          height: `${decimal(decimal(this.qrCodeHeight, 37.795, 0, '*'), 15, 0, '+')}px`,
          pageBreakInside: 'avoid',
        };
      }
    },
    async mounted() {
        this.init();
        this.onSearch();
        this.getInventoryWareHouseList();
    },
    methods: {
        onFoldingMethod() {
          if (this.editForm.isZheDie == 1 && this.editForm.isChao40CM == 0) {
            this.foldingVerification = false;
            // this.editForm.clLength = null;
            // this.editForm.clWidth = null;
            // this.editForm.clHeight = null;
            // this.editForm.clLengthImgs = [];
            // this.editForm.clWidthImgs = [];
            // this.editForm.clHeightImgs = [];
          } else {
            this.foldingVerification = true;
          }
        },
        replayMethod(row) {
          this.replayInfo.row = JSON.parse(JSON.stringify(row))
          this.replayInfo.row.numberShots = row.mediaCount
          this.replayInfo.row.shootingTime = row.mediaTime
          this.$nextTick(() => {
            this.replayInfo.visible = true
          })
        },
        printQRCode(val) {
          if (this.goodsCodeList && this.goodsCodeList.length == 0) {
            this.$message.error('暂无可打印的二维码');
            return;
          }
          printJS({
            id: 'printid'
          })
        },
        getScrollbarWidth() {
          this.$nextTick(() => {
            if (this.$refs.refscrollbar && this.$refs.refscrollbar.$el) {
              this.scrollbarWidth = this.$refs.refscrollbar.$el.offsetWidth;
            } else {
              console.warn('Scrollbar ref not found.');
            }
          });
        },
        async handlePrint(row) {
          this.printSize = 4
          this.qrCodeWidth = 4
          this.qrCodeHeight = 4
          this.numQRCode = 1
          this.goodsCodeList = [row.goodsNumber]
          this.islinePrinting = true
          await this.onPrintMethod()
          this.printVisible = true
        },
        async generateQRCode() {
          if (!this.numQRCode || this.numQRCode <= 0) {
            this.$message.warning('请输入要打印的二维码数量')
            return
          }
          const { data } = await getBathSuiJiGoodsCode({ num: this.numQRCode })
          this.goodsCodeList = data
          this.onPrintMethod()
        },
        async onPrintMethod(val) {
          this.goodsCodeList.forEach((item, index) => {
            setTimeout(() => {
              JsBarcode(`#barcode1${item}`, item, {
                displayValue: this.switchshow,
                fontSize: 20,
                fontOptions: "bold",
                font: "Arial",
                textAlign: "center"
              })
            }, 0);
          })
          this.printLoading = false;
        },
        changePrintSize(e, val) {
          this.qrCodeWidth = e
          this.qrCodeHeight = e
          this.onPrintMethod(val)
        },
        printBarcode() {
          this.printVisible = true
          this.islinePrinting = false
          this.goodsCodeList = []
        },
        printCloseMethod() {
          this.goodsCodeList = []
          this.printVisible = false
        },
        ondelete(item, i) {
            this.$confirm('是否删除地区页签?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.districtList.splice(i, 1)
                this.goodCodeGetSetDialog.regionWarehouseJson.forEach((item1, index) => {
                    if (item1.region == item.region) {
                        this.goodCodeGetSetDialog.regionWarehouseJson.splice(index, 1)
                        item1.warehouseList.forEach(item2 => {
                            this.warehouseList.unshift({ name: item2.warehouseName, wms_co_id: item2.warehouseCode })
                        })
                    }
                })
                this.onAreaSwitching(this.districtList[0], 0)
                this.$message({ type: 'success', message: '删除成功!' });
                this.$forceUpdate()
            }).catch(() => {
            });
        },
        handleClose(e, i) {
            this.warehouseLabelList.splice(i, 1)
            this.warehouseList.unshift({ name: e.warehouseName, wms_co_id: e.warehouseCode })
            this.warehouseList = this.warehouseList.filter((item, index) => {
                return this.warehouseList.findIndex((item1) => item1.wms_co_id == item.wms_co_id) == index
            })
        },
        onAreaSwitching(item, i) {
            this.warehouseLabelList = []
            this.goodCodeGetSetDialog.regionWarehouseJson.forEach((item, index) => {
                if (index == i) {
                    this.warehouseLabelList = item.warehouseList
                }
            })
            this.regional = i
        },
        onNewWarehouse() {
            if (this.districtList.length == 0) {
                this.$message.error('请先添加地区')
                return
            }
            this.warehouseList = this.warehouseList.filter(item => {
                if (this.wmsId.indexOf(item.wms_co_id) > -1) {
                    const newWarehouse = { warehouseCode: item.wms_co_id, warehouseName: item.name };
                    if (!this.warehouseLabelList.some(wh => wh.warehouseCode === newWarehouse.warehouseCode)) {
                        this.warehouseLabelList.push(newWarehouse);
                    }
                    if (!this.goodCodeGetSetDialog.regionWarehouseJson[this.regional].warehouseList.some(wh => wh.warehouseCode === newWarehouse.warehouseCode)) {
                        this.goodCodeGetSetDialog.regionWarehouseJson[this.regional].warehouseList.push(newWarehouse);
                    }
                    return false;
                }
                return true;
            });
            this.wmsId = [];
        },
        onStorageMethod() {
            if (this.districtList.some(item => item.region == this.area.region)) {
                this.$message.error('地区已存在')
                return
            }
            this.districtList.push({ region: this.area.region })
            this.regional = this.districtList.length - 1
            this.goodCodeGetSetDialog.regionWarehouseJson.push({ region: this.area.region, warehouseList: [] })
            this.warehouseLabelList = []
            this.area.dialog = false
        },
        onNewArea() {
            this.area.region = ''
            this.area.dialog = true
            this.$nextTick(() => {
                this.$refs.regioninput.focus()
            })
        },
        //导入模板
        onDownloadTemplate() {
            downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250430/1917509224133738497.xlsx', '采购商品资料库导入模板.xlsx');
            return
            window.open("../../static/excel/inventory/采购商品资料库导入模板.xlsx", "_self");
        },
        onSaveEvent() {
            for (let key in this.encoding) {
                this.goodCodeGetSetDialog.setData[key] = this.encoding[key]
            }
            this.graspingCondition = false
        },
        codeSwitching(e) {
            if (e == 'first1') {
                this.verifykeyword = true
            } else {
                this.verifykeyword = false
            }
        },
        getGoodCode() {
            this.tabEncoding = 'first1'
            this.verifykeyword = true
            this.graspingCondition = true
        },
        //上传文件
        onUploadRemove(file, fileList) {
            this.fileList = []
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            this.leadDialogVisible = false;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            var res = await importBatchGoodsDocRecordCg(form);
            if (res?.success)
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            this.uploadLoading = false
            this.leadDialogVisible = false;
            await this.getList()
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        //导入弹窗
        startImport() {
            this.fileList = []
            this.leadDialogVisible = true;
        },
        grabNewChange(e) {
            if (!e) {
                this.goodCodeGetSetDialog.setData.isNewProAllot = false;
                this.goodCodeGetSetDialog.setData.isNewLabelKeyWords = false;
                this.goodCodeGetSetDialog.setData.isNewNameKeyWords = false;
            }

        },
        factoryChange() {
            this.goodCodeGetSetDialog.setData.isEqualSupplier = !this.goodCodeGetSetDialog.setData.isChangeMerchant ? false : this.goodCodeGetSetDialog.setData.isEqualSupplier;
            this.goodCodeGetSetDialog.setData.isAllot = !this.goodCodeGetSetDialog.setData.isChangeMerchant ? false : this.goodCodeGetSetDialog.setData.isAllot;
            this.goodCodeGetSetDialog.setData.isLabelKeyWords = !this.goodCodeGetSetDialog.setData.isChangeMerchant ? false : this.goodCodeGetSetDialog.setData.isLabelKeyWords;
            this.goodCodeGetSetDialog.setData.isNameKeyWords = !this.goodCodeGetSetDialog.setData.isChangeMerchant ? false : this.goodCodeGetSetDialog.setData.isNameKeyWords;
        },
        checkHiddenEvent() {
            this.checkHiding = !this.checkHiding
        },
        grabCoding() {
            this.checkNewCode = !this.checkNewCode
        },
        purchaseChange(e) {
            if (this.purchaseOrder) {
                this.purchaseOrder.forEach(item => {
                    if (item.buyNo == "无关联选项") {
                        this.editForm.buyNo = null
                        return
                    }
                    if (item.buyNo == e) {
                        let isExist = this.providerNames.some(a => a.supplierId == item.supplier_id);
                        if (!isExist) {
                            this.providerNames.push({ supplierId: item.supplier_id, supplier: item.supplier });
                        }
                        this.editForm.warehouseCode = item.warehouse;
                        this.editForm.supplierId = item.supplier_id;
                        this.editForm.costPrice = item.costPrice;
                        this.editForm.providerName = item.supplier;
                        this.codedState = item.goodsStatus == 1 ? '换厂待拍' : item.goodsStatus == 2 ? '新品待拍' : '';
                        this.editForm.goodsState = item.goodsStatus;
                    }
                })
                if (this.editForm.buyNo) {
                    this.fordisable = true
                } else {
                    this.fordisable = false
                }
            }
        },
        locationDataMethod() {
            this.warePositionList = [];
            this.editForm.warePosition = null;
            this.warePositionmy = null;
        },
        //清空数据,清空仓位,将第一次选择的仓位赋值给下拉框
        clearDataMethod() {
            this.packingWarehouseLocationList = [];
            this.editForm.packingWarePosition = null;
            this.packingWarePositionmy = null;
        },
        async performAction(actionType) {
            let batid = []
            batid = this.$refs.table.$refs.xTable.getCheckboxRecords().map(a => a.id);
            if (batid.length == 0) {
                let message = actionType === 'delete' ? "请选择需要删除的行" : "请选择需要同步重量的行";
                this.$message({ message, type: "warning" });
                return;
            }
            let confirmMessage = actionType === 'delete' ? "此操作将执行删除操作, 是否继续?" : "此操作将执行同步重量操作, 是否继续?";
            this.$confirm(confirmMessage, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let { success } = actionType === 'delete' ? await deleteGoodsDocRecordCgUnrestricted({ ids: batid }) : await syncGoodsDocRecordCgWeight({ ids: batid });
                if (success) {
                    let successMessage = actionType === 'delete' ? '删除成功' : '同步重量成功';
                    this.$message({ message: successMessage, type: 'success' });
                    this.onSearch();
                }
            }).catch(() => {
            });
        },
        async performancescoreverify(row, event, val) {
            if (event == 1 && this.place == 3) {
                return
            }
            if (event !== 1) {
                event.preventDefault();
                this.place = val
            }
            if (row.vedioOrImgRemarks) {
                const { data, success } = await saveVedioOrImgRemarks({ id: row.id, vedioOrImgRemarks: row.vedioOrImgRemarks })
                if (success) {
                    this.$message({ message: "操作成功", type: "success" });
                    this.onSearch();
                }
            }
            setTimeout(() => {
                this.place = 2;
            }, 2000);
        },
        async changeInfo(e) {
            if (e) {
                const { data } = await getGoodsDocRecordCgBcById({ id: e });
                this.materialInfo = data.bcDtls;
            } else {
                this.materialInfo = [];
            }
        },
        changeLocation(type) {
            if (this.imageIndex == null) {
                this.imageIndex = 0
            }
            if (type == 'right') {
                if (this.imageIndex == this.viewImageList.length - 1) return
                this.imageIndex++
            } else if (type == 'left') {
                if (this.imageIndex == 0) return
                this.imageIndex--
            }
            this.$nextTick(() => {
                this.imgInfo = {
                    url: this.viewImageList[this.imageIndex].url,
                    name: this.viewImageList[this.imageIndex].name,
                    other1: this.viewImageList[this.imageIndex].other1
                }
            })
            console.log(this.imageIndex, 'this.imageIndex');
        },
        viewImg(row) {
            if (row.materialImgs == null || row.materialImgs.length == 0) return
            console.log(row, 'row');
            this.viewImageList = JSON.parse(row.materialImgs);
            console.log(this.viewImageList, 'this.viewImageList');
            this.imgInfo = {
                url: this.viewImageList[0].url,
                name: this.viewImageList[0].name,
                other1: this.viewImageList[0].other1
            }
            this.imageIndex = null
            this.viewImageVisiable = true;
        },
        //岗位变更
        async warehouseposition() {
            let logicId = this.editForm['id'].toString();
            this.changepage.pageSize = 5;
            const params = {
                ...this.changepage,
                scene: 'Good_Warehouse_Position',
                logicId
            };
            const { data, success } = await pageGetChangeLogs(params)
            this.epositionlist = data.list;
            this.warehousepositionchange = true;
        },
        //批量编辑
        batchediting() {
            let batid = this.$refs.table.$refs.xTable.getCheckboxRecords().map(a => a.id);
            if (batid.length == 0) {
                this.$message({ message: "请选择需要编辑的行", type: "warning" });
                return
            }
            this.batch.qualificationType = null;
            this.batch.otherRemark = null;
            this.dialogredact = true;
        },
        //批量编辑商品资料保存
        async batcheditingsave() {
            let ids = this.$refs.table.$refs.xTable.getCheckboxRecords().map(a => a.id);
            const params = { ...this.batch, ids };
            const { data, success } = await batchUpdateGoodsDocRecordCg(params);
            this.$message({ message: "操作成功", type: "success" });
            this.onSearch();
            this.dialogredact = false;
        },
        selectEnd() {
            this.editForm.warePosition = this.warePositionmy;
        },
        packselectEnd() {
            this.editForm.packingWarePosition = this.packingWarePositionmy;
        },
        changeType(e) {
            //如果是已拍就清空待拍时间
            if (e) {
                this.filter.createdTimeStart = null;
                this.filter.createdTimeEnd = null;
                this.filter.goodsState = null
                this.filter.daterange = [];
                this.$refs.table.changecolumn(["goodsState"]);
            } else {
                this.filter.startMediaTime = null;
                this.filter.endMediaTime = null;
                this.filter.daterange1 = [];
                this.$refs.table.changecolumn([]);
            }
        },
        changeAlreadyTime(e) {
            //如果e为空，说明清空了时间
            if (e) {
                this.filter.startMediaTime = e[0];
                this.filter.endMediaTime = e[1];
            } else {
                this.filter.startMediaTime = null;
                this.filter.endMediaTime = null;
            }
        },
        //获取仓库
        async getInventoryWareHouseList() {
            if (this.warehouseAllList.length <= 0) {
                let wares = await getTbWarehouseList();
                if (wares?.success && wares?.data && wares?.data.length > 0) {
                    wares?.data.forEach(f => {
                        this.warehouseAllList.push({ value: f.wms_co_id, label: f.name });
                    });
                }
            }
        },

        // 判断视频是否上传成功
        uploadFinish(data) {
            this.subLoad = data
        },
        // 库存
        getPurchase(goodsCode) {
            this.purchaseDialog = true;
            this.$nextTick(() => {
                getPurchaseNewPlan2ByGoodsCode({ goodsCode: goodsCode }).then(res => {
                    if (res.success) {
                        this.purchaseList = res.data;
                    }
                })
            })
        },
        //销量统计
        async getbirchart(goodsCode, number) {
            let loadingInstance = Loading.service();
            let startDate = formatTime(dayjs().subtract(number, 'day'), "YYYY-MM-DD");
            let endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
            // this.filterchart.startDate = startDate;
            // this.filterchart.endDate = endDate;
            Loading.service({ fullscreen: true });
            var that = this;
            // const params = { goodsCode: goodsCode, day: number, timeType: 0, ...this.filterchart };

            const param = {
                goodsCode: goodsCode,
                startDate: startDate,
                endDate: endDate
            };
            let { data, success } = await getTrendChart(param);
            if (success) {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = data;
                that.buscharDialog.title = '商品编码：' + goodsCode;
            }

            // await getPurOrderAnalysis(params).then(res => {
            //     that.buscharDialog.visible = true;
            //     that.buscharDialog.data = res.data;
            //     that.buscharDialog.title = '商品编码：' + goodsCode;
            // });
            loadingInstance.close();
        },
        // 产品拍摄更新日志
        getlogList(row) {
            if (row.id) {
                this.logparams = row.id
            }
            this.logListDialog = true;
            this.$nextTick(() => {
                var pager = this.$refs.pagerlog.getPager();
                const params = {
                    ...pager,
                    ...{ recordId: this.logparams }
                };
                pageGoodsDocRecordCgChangeLogList(params).then(res => {
                    if (res.success) {
                        this.logList = res.data.list;
                        this.totallog = res.data.total;
                    }
                })
            })

        },
        // 历史价格
        getHistory() {
            this.historyDialog = true;
            this.$nextTick(() => {
                var pager = this.$refs.pagerhistory.getPager();
                const params = {
                    ...pager,
                    ...{ keyValue: this.historyparams }
                };
                pageGoodsPriceList(params).then(res => {
                    if (res.success) {
                        this.historyList = res.data.list
                        this.totalhistory = res.data.total
                    }
                })
            })

        },
        // 重置表单
        resetForm() {
            this.editForm = {
                id: 0,
                goodsCode: '',
                providerName: '',
                supplierId: null,
                clWeight: null,
                clWeightImgs: null,
                clLength: null,
                clLengthImgs: null,
                clWidth: null,
                clWidthImgs: null,
                clHeight: null,
                clHeightImgs: null,
                clThickness: null,
                clThicknessImgs: '',
                zhankaiMeasuredChang: null,
                zhankaiMeasuredChangImages: null,
                zhankaiMeasuredWidth: null,
                zhankaiMeasuredWidthImages: null,
                zhankaiMeasuredHeight: null,
                zhankaiMeasuredHeightImages: null,
                costPrice: null,
                goodsVedio: [],
                qualificationType: null,
                warehouseCode: null,
                warePosition: null,
                packingWarePosition: null,
                remark: '',
                otherRemark: '',
                isPatent: false,
                isControlPrice: false,
                controlPriceDescribe: '',
                controlPricePicture: '',
                goodsState: null,
                buyNo: null,
                boxRuleHeight: null,
                boxRuleLength: null,
                boxRuleWidth: null,
            }
            this.$nextTick(() => {
                this.$refs.uploadexl.setData([]);
            })
        },
        // 查询商品资料及供应商
        getGoodsDoc(goodsCode) {
            this.editForm.warePosition = "";
            this.editForm.packingWarePosition = "";
            this.editForm.warePosition = null;
            this.warePositionList = [];
            this.wareBeforePositionList = [];
            this.packingWarehouseLocationList = [];
            if (goodsCode) {
                getGoodsDocCgProvidersDto({ goodsCode: goodsCode }).then(res => {
                    if (res.data) {
                        this.editForm.deliveryCycle = formatSecondNewToHour(res.data.deliveryCycle)

                        this.editForm.goodsName = res.data.goodsName;
                        this.editForm.pictureUrl = res.data.pictureUrl;
                        this.providerNames = res.data.providerNames;
                        this.editForm.supplierId = res.data.providerNames[0].supplierId;
                        this.editForm.remark = '原供应商-仓库的货';
                        this.editForm.costPrice = res.data.costPrice;
                        this.locationData = [];
                        this.warePositionList = [];
                        if (res.data.positionList.length == 1) {
                            let f = res.data.positionList[0];
                            this.editForm.warehouseCode = f.wms_co_id;
                            this.warePositionList.push({ value: f.bin, label: f.bin })
                            this.editForm.warePosition = f.bin;
                        }
                        else if (res.data.positionList.length >= 1) {
                            res.data.positionList.forEach(f => {
                                this.wareBeforePositionList.push({ wms_co_id: f.wms_co_id, sku_id: f.sku_id, bin: f.bin });
                                this.warePositionList.push({ value: f.bin, label: f.bin });
                            });
                            this.warePositionList = res.data.positionList.map(item => { return { value: item.bin, label: item.bin }; });
                            this.editForm.warePosition = this.warePositionList[0].value;
                        }
                        if (!this.editForm.warehouseCode) {
                            this.editForm.warehouseCode = this.warehouseAllList[0].value;
                        }
                    }
                    this.locationData = this.warePositionList;
                    this.warePositionList = [];
                    this.clearData = []
                    if (res.data.packPositionList.length == 1) {
                        let f = res.data.packPositionList[0];
                        this.editForm.warehouseCode = f.wms_co_id;
                        this.packingWarehouseLocationList.push({ value: f.bin, label: f.bin })
                        this.editForm.packingWarePosition = f.bin;
                    } else if (res.data.packPositionList.length >= 1) {
                        this.packingWarehouseLocationList = res.data.packPositionList.map(item => { return { value: item.bin, label: item.bin }; });
                        this.editForm.packingWarePosition = this.packingWarehouseLocationList[0].value;
                    }
                    this.clearData = this.packingWarehouseLocationList;
                    this.packingWarehouseLocationList = [];
                })
                this.obtainpurchaseOrder(goodsCode)
                this.getLink(goodsCode, this.editForm.supplierId)
            } else {
                this.editForm.goodsName = '';
                this.editForm.pictureUrl = null;
                this.providerNames = '';
                this.editForm.remark = '';
                this.editForm.costPrice = 0;
                this.editForm.deliveryCycle = ''
            }
        },
        //获取采购单
        async obtainpurchaseOrder(goodsCode) {
            getPurchaseOrderList({ goodsCode: goodsCode }).then(res => {
                if (res.success && res.data && res.data.length > 0) {
                    this.purchaseOrder = []
                    this.purchaseOrder = res.data
                } else {
                    this.purchaseOrder = [{ buyNo: '无关联选项' }]
                }
            })
        },
        async onEditFormWarehouseChange(value) {
            this.editForm.warePosition = null;
            this.warePositionList = [];
            var f = this.wareBeforePositionList.find(f => f.wms_co_id == value);
            if (f) {
                this.warePositionList.push({ value: f.bin, label: f.bin });
                this.editForm.warePosition = f.bin;
            }
        },
        async onWarePositionMethod(value) {
            this.warePositionList = [];
            this.warePositionmy = value;
            if (value) {
                let { data, success } = await getTbWarePositionList({ warecode: this.editForm.warehouseCode, pos: value });
                let pos = data.positionList;
                if (success && pos && pos.length > 0) {
                    pos?.forEach(f => {
                        if (!this.warePositionList.find(f1 => f1.value == f.bin))
                            this.warePositionList.push({ value: f.bin, label: f.bin });
                    });
                }
            }
        },
        async packonWarePositionMethod(value) {
            this.packingWarehouseLocationList = [];
            this.packingWarePositionmy = value;
            if (value) {
                let { data, success } = await getTbWarePositionList({ warecode: this.editForm.warehouseCode, pos: value });
                let pos = data.packPositionList;
                if (success && pos && pos.length > 0) {
                    pos?.forEach(f => {
                        if (!this.packingWarehouseLocationList.find(f1 => f1.value == f.bin))
                            this.packingWarehouseLocationList.push({ value: f.bin, label: f.bin });
                    });
                }
            }
        },
        // 保存商品资料
        saveGoodsDoc() {
            this.editForm.goodsVedio = '';
            this.editForm.providerName = null;
            this.editForm.providerName = this.providerNames.find(f => f.supplierId == this.editForm.supplierId)?.supplier;
            let videoRes = this.$refs.uploadexl.getReturns();
            if (videoRes.data?.length > 0) {
                this.editForm.goodsVedio = videoRes.data[0].url;
            }
            this.$refs.editFormRef.validate((valid) => {
                if (valid) {
                    // if ((this.editForm.warePosition == '' || this.editForm.warePosition == undefined || this.editForm.warePosition == null) && (this.editForm.packingWarePosition == '' || this.editForm.packingWarePosition == undefined || this.editForm.packingWarePosition == null)) {
                    //     this.$message({ type: 'warning', message: '须选择一个仓位或装箱仓位!' });
                    //     return;
                    // }
                    this.subLoad = true;
                    this.editForm.isPc = true;
                    if (this.editForm.goodsDocRecordCgBcId == '' || this.editForm.goodsDocRecordCgBcId == undefined) {
                        this.editForm.goodsDocRecordCgBcId = null;
                    }
                    const replaceArr = ['goodsCode']//去空格
                    this.editForm = replaceSpace(replaceArr, this.editForm)
                    this.editForm.warehouseName = this.warehouseAllList.find(f => f.value == this.editForm.warehouseCode)?.label;
                    saveGoodsDocRecordCg(this.editForm).then(res => {
                        if (res.success) {
                            this.$message({ type: 'success', message: '保存成功!' });
                            this.subLoad = false;
                            this.eidtDialog = false
                            this.onSearch();
                        }
                        else
                            this.subLoad = false;
                    });
                }
            });
        },
        // 显示视频播放弹窗
        playVideo(videoUrl) {
            this.videoplayerReload = false;
            this.videoplayerReload = true;
            this.videoDialogVisible = true;
            this.videoUrl = videoUrl;
        },
        // 关闭视频弹窗
        async closeVideoPlyer() {
            this.videoDialogVisible = false;
            this.videoplayerReload = false;
        },
        //设置运营组、采购组下拉
        async init() {
            const {data} = await getUserInfo();
            if(data?.fullName == '浙江义乌总公司-采购部-采购文员'){
                this.$set(this.filter, 'hasVedioOrImg', false);
            }else{
                this.$set(this.filter, 'hasVedioOrImg', true);
            }
            const res = await getGroupKeyValue({});
            this.groupList = res.data;
            var res2 = await getAllProBrand();
            //取消系统挂靠
            this.brandlist = res2.data
                .filter(item => item.value !== '☆系统挂靠' && item.value !== '☆耗材包材')
                .map(item => {
                    return { value: item.key, label: item.value };
                });
            var res4 = await getAllWarehouse();
            var warehouselist1 = [];
            res4.data.map((item) => {
                if (item.name.indexOf('代发') == -1) {
                    warehouselist1.push(item)
                }
            })
            this.warehouseList = warehouselist1;
            this.stashhouseList = warehouselist1;
        },
        async getLink(goodsCode, supplierId) {
            const { data, success } = await getAliGoodsLink({ goodsCode, supplierId });
            if (success) {
                this.$set(this.editForm, 'supplierLink', data);
            }
        },
        // 编辑或新增商品资料
        async editProduct(row, editmode = 0) {
            this.editmode = editmode;
            this.forbidden = row.isAllot == 1 ? true : false;
            this.codedState = row.goodsState == 1 ? '换厂待拍' : row.goodsState == 2 ? '新品待拍' : '';
            this.editForm.warePosition = null;
            this.eidtDialog = true
            this.purchaseOrder = []
            this.packingWarehouseLocationList = []
            if (row.buyNo) {
                this.fordisable = true
            } else {
                this.fordisable = false
            }
            this.clearData = []
            this.locationData = []
            this.warePositionList = [];
            await this.obtainpurchaseOrder(row.goodsCode)
            if (row.id) {
                this.resetForm();
                getGoodsDocCgProvidersDto({ goodsCode: row.goodsCode }).then(res => {
                    this.providerNames = []
                    this.providerNames = res.data.providerNames;
                    this.editForm.supplierId = row.supplierId;
                    if (res.data.packPositionList) {
                        this.packingWarehouseLocationList = res.data.packPositionList.reduce((acc, item) => {
                            if (item.bin !== '') {
                                acc.push({ value: item.bin, label: item.bin });
                            }
                            return acc;
                        }, []);
                        this.clearData = []
                        this.clearData = this.packingWarehouseLocationList;
                        this.packingWarehouseLocationList = []
                    } else {
                        this.packingWarehouseLocationList = [];
                    }
                    if (res.data.positionList.length == 1) {
                        let f = res.data.positionList[0];
                        this.warePositionList.push({ value: f.bin, label: f.bin })
                    } else if (res.data.positionList.length >= 1) {
                        res.data.positionList.forEach(f => {
                            this.warePositionList.map(item => { return { value: item.bin, label: item.bin }; });
                        });
                    }
                    this.locationData = this.warePositionList
                    this.warePositionList = [];
                })
                this.eidtDialogTitle = '编辑商品资料';
                this.$nextTick(async () => {
                    const res = row.boxRule ? row.boxRule.split('*') : [];
                    this.editForm = { ...row };
                    this.editForm.boxRuleLength = res[0] ? res[0] : null;
                    this.editForm.boxRuleWidth = res[1] ? res[1] : null;
                    this.editForm.boxRuleHeight = res[2] ? res[2] : null;
                    this.editForm.isPatent = row.isPatent ? row.isPatent : false;
                    this.editForm.isControlPrice = row.isControlPrice ? row.isControlPrice : false;
                    this.editForm.goodsVedio = [];
                    this.editForm.deliveryCycle = formatSecondNewToHour(this.editForm.deliveryCycle)
                    if (row.goodsVedio) {
                        let vi = {
                            url: row.goodsVedio,
                            fileName: row.goodsVedio,
                            relativePath: null,
                            domain: null
                        }
                        this.editForm.goodsVedio = [vi];
                    }

                    this.$refs.uploadexl.setData(this.editForm.goodsVedio);
                    const params = {
                      id: row.id,
                      goodsCode: row.goodsCode,
                      supplierId: row.supplierId,
                      getPackCount: !row.packCount,
                      getSupplierLink: !row.supplierLink
                    };
                    if (params.getPackCount || params.getSupplierLink) {
                      this.editForm.packCount = row.packCount || this.editForm.packCount;
                      this.editForm.supplierLink = row.supplierLink || this.editForm.supplierLink;
                      const { data, success } = await getGoodsDocRecordOther(params);
                      if (!success) return;
                      this.editForm.packCount = data.packCount ?? this.editForm.packCount;
                      this.editForm.supplierLink = data.supplierLink ?? this.editForm.supplierLink;
                    }
                    this.onFoldingMethod()
                })
            } else {
                this.resetForm();
                this.eidtDialogTitle = '新增商品资料'
            }
            const queryInfo = {
                currentPage: 1,
                pageSize: 20,
            }
            const { data } = await getGoodsDocRecordCgBcPageList(queryInfo);
            this.selectList = data.list.map(item => {
                return {
                    value: item.id,
                    label: item.materialNameSizes
                }
            })
            console.log(row, 'row');
            console.log(row.goodsDocRecordCgBcId, 'row.goodsDocRecordCgBcId');
            if (row.goodsDocRecordCgBcId) {
                const { data: data1, success } = await getGoodsDocRecordCgBcById({ id: row.goodsDocRecordCgBcId })
                if (success) {
                    this.materialInfo = data1 ? data1.bcDtls : [];
                }
            } else {
                this.materialInfo = []
            }

        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getDataList();
        },
        //获取数据
        async getDataList() {
            if (this.filter.daterange) {
                this.filter.createdTimeStart = this.filter.daterange[0];
                this.filter.createdTimeEnd = this.filter.daterange[1];
            } else {
                this.filter.createdTimeStart = null;
                this.filter.createdTimeEnd = null;
            }
            if (this.filter.daterange1) {
                this.filter.startMediaTime = this.filter.daterange1[0];
                this.filter.endMediaTime = this.filter.daterange1[1];
            }
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.listLoading = true;
            const res = await pageGoodsDocRecordCgList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.datalist = res.data.list;
            this.datalist.forEach(item => {
                if (item.materialImgs) {
                    item.materialImgs = JSON.parse(item.materialImgs);
                    const urlsArray = item.materialImgs.map(imgObject => imgObject.url);
                    item.materialImgs = urlsArray;
                }
            });
            this.summaryarry = res.data.summary;
        },
        async onExportGoods(command) {
            if (this.filter.daterange) {
                this.filter.createdTimeStart = this.filter.daterange[0];
                this.filter.createdTimeEnd = this.filter.daterange[1];
            } else {
                this.filter.createdTimeStart = null;
                this.filter.createdTimeEnd = null;
            }
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                exportType: command,
                ...this.filter
            };
            this.listLoading = true;
            const res = await exportGoodsDocRecordCgListGoods(params);
            this.listLoading = false;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', "待拍商品编码_" + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        cellclick(row) {
            if (row.column.property == 'goodsVedio' && row.row.goodsVedio) {
                //弹出视频播放
                this.playVideo(row.row.goodsVedio)
            }
        },

        //列表排序
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        //多选事件
        selectchange: function (rows, row) {
            this.self = [];
            rows.forEach(f => {
                this.self.push(f.shopDecorationTaskId);
            })
        },

        customRowStyle(row, index) {
            if (row.row?.isend && row.row.isend == 1) {
                let styleJson = {};
                styleJson.color = "rgb(216 216 216)";
                return styleJson
            } else {
                return null
            }

        },
        deleteProduct(row) {
            this.$confirm('确定要执行删除吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await deleteGoodsDocRecordCg({ id: row.id });
                if (res?.success) {
                    this.$message({ type: 'success', message: '删除成功!' });
                    this.onSearch();
                }
            }).catch(() => {
            });
        },
        async onGoodCodeGetSet() {
            this.goodCodeGetSetDialog.visible = true;
            this.$nextTick(() => {
                getGoodsDocRecordCgSet().then(res => {
                    console.log(res);
                    if (res?.success && res?.data) {
                        this.goodCodeGetSetDialog.setData = {
                            isGet: res.data.isGet,
                            continuousNum: res.data.continuousNum,
                            isAllot: res.data.isAllot,
                            isEqualSupplier: res.data.isEqualSupplier ? res.data.isEqualSupplier : false,
                            isChangeMerchant: res.data.isChangeMerchant ? res.data.isChangeMerchant : false,
                            isGrabNewPro: res.data.isGrabNewPro ? res.data.isGrabNewPro : false,
                            isNewProAllot: res.data.isNewProAllot ? res.data.isNewProAllot : false,
                            isNameKeyWords: res.data.isNameKeyWords ? res.data.isNameKeyWords : false,
                            isLabelKeyWords: res.data.isLabelKeyWords ? res.data.isLabelKeyWords : false,
                            isNewNameKeyWords: res.data.isNewNameKeyWords ? res.data.isNewNameKeyWords : false,
                            isNewLabelKeyWords: res.data.isNewLabelKeyWords ? res.data.isNewLabelKeyWords : false,
                        };
                        this.encoding.nameKeyWords = res.data.nameKeyWords ? res.data.nameKeyWords : null;
                        this.encoding.labelKeyWords = res.data.labelKeyWords ? res.data.labelKeyWords : null;
                        this.encoding.newNameKeyWords = res.data.newNameKeyWords ? res.data.newNameKeyWords : null;
                        this.encoding.newLabelKeyWords = res.data.newLabelKeyWords ? res.data.newLabelKeyWords : null;
                        if (res.data.regionWarehouseList && res.data.regionWarehouseList.length > 0) {
                            this.goodCodeGetSetDialog.regionWarehouseJson = res.data.regionWarehouseList;
                            res.data.regionWarehouseList.forEach(item => {
                                const regionExists = this.districtList.some(district => district.region === item.region);
                                if (!regionExists) {
                                    this.districtList.push({ region: item.region });
                                }
                                if (this.districtList.length != 0) {
                                    this.regional = 0
                                }
                                // 过滤掉与当前warehouseList中的warehouseName相同的对象
                                this.warehouseList = this.warehouseList.filter(warehouse => {
                                    return !item.warehouseList.some(wh => wh.warehouseName == warehouse.name);
                                });
                            });
                            // 获取第一个区的warehouseList
                            this.warehouseLabelList = res.data.regionWarehouseList[0].warehouseList;
                        }
                    }
                })
            })
        },
        async saveGoodCodeGetSet() {
            let isverify = false;
            const warehouseNames = new Set(); // 用于存储唯一的仓库名称
            for (const region of this.goodCodeGetSetDialog.regionWarehouseJson) {
                for (const warehouse of region.warehouseList) {
                    if (warehouseNames.has(warehouse.warehouseName)) {
                        this.$message.error(`重复的仓库名称: ${warehouse.warehouseName}`);
                        isverify = true;
                        return;
                    }
                    warehouseNames.add(warehouse.warehouseName); // 添加到集合
                }
            }
            if (isverify) return;
            let { setData } = this.goodCodeGetSetDialog;
            let params = {
                ...setData,
                ...this.encoding,
                isNameKeyWords: setData.isNameKeyWords ?? false,
                isLabelKeyWords: setData.isLabelKeyWords ?? false,
                isNewNameKeyWords: setData.isNewNameKeyWords ?? false,
                isNewLabelKeyWords: setData.isNewLabelKeyWords ?? false,
                regionWarehouseJson: JSON.stringify(this.goodCodeGetSetDialog.regionWarehouseJson),
            };
            this.goodCodeGetSetDialog.loading = true;
            var res = await saveGoodsDocRecordCgSet(params);
            this.goodCodeGetSetDialog.loading = false;
            if (res?.success) {
                this.$message({ type: 'success', message: '设置成功!' });
                this.goodCodeGetSetDialog.visible = false;
            }
        },
        collapasechange(val) {
            if (val.length) {
                this.collatitle = '折叠';
                this.vxetableheight = '221';
            } else {
                this.collatitle = ' 展开';
                this.vxetableheight = '550';
            }
        },
        async otherRemarkClickShow(id) {
            this.otherRemarkDialog.visible = true;
            var res = await getGoodsDocRecordCgOtherRemarkListByParnetId({ id: id });
            if (res?.success) {
                this.otherRemarkDialog.data = res.data;
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.el-form-item__content {
    .append_unit {
        position: relative;

        &::after {
            content: attr(data-unit);
            position: absolute;
            top: 0;
            right: 0;
            padding: 0 10px;
            color: #909399;
            background-color: #f5f7fa;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
        }
    }
}

::v-deep .vxe-table--tooltip-wrapper.theme--dark {
    z-index: 2023 !important;
}

::v-deep .el-link.el-link--primary {
    margin-left: 5px;
}

.imgcss ::v-deep img {
    min-width: 900px !important;
    min-height: 700px !important;
    width: 900px !important;
    height: 700px !important;
}

.materialImg ::v-deep img {
    min-width: 80px !important;
    min-height: 80px !important;
    width: 80px !important;
    height: 80px !important;
}

// ::v-deep .el-popup-parent--hidden>>>.v-modal{
//     z-index: 1000 !important;
// }
// ::v-deep .el-dialog__wrapper{
//     z-index: 1001 !important;
// }

.imgBox {
    width: 100%;
    height: 100%;
    position: relative;

    &:hover {
        .el-icon-arrow-left {
            display: block;
        }

        .el-icon-arrow-right {
            display: block;
        }
    }

    .imgBox_item1 {
        position: absolute;
        top: 1px;
        left: 2px;
        width: 300px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        background-color: #ccc;
        color: #000;
        font-size: 16px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .imgBox_item2 {
        position: absolute;
        top: 55px;
        left: 2px;
        width: 300px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        background-color: #ccc;
        color: #000;
        font-size: 16px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .imgBox_item3 {
        position: absolute;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #ccc;
        color: red;
        font-size: 16px;
        text-align: center;
        line-height: 30px;
        top: 0;
        right: 15px;
    }

    .el-icon-arrow-left {
        position: absolute;
        top: 50%;
        left: 0;
        font-size: 40px;
        color: #409EFF;
        transform: translateY(-50%);
        cursor: pointer;
        display: none;
    }

    .el-icon-arrow-right {
        position: absolute;
        top: 50%;
        right: 10px;
        font-size: 40px;
        color: #409EFF;
        transform: translateY(-50%);
        cursor: pointer;
        display: none;
    }
}

.materialInfoBox {
    display: flex;
    flex-wrap: wrap;

    .materialInfoBox_item {
        display: flex;
        flex-direction: column;
        margin-right: 10px;
    }
}

.selectBox {
    max-width: 500px !important;
}

.del {
    position: absolute;
    top: -6px;
    right: -10px;
    font-size: 12px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    color: gray;
    text-align: center;
    line-height: 9px;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s, opacity 0.3s;
    opacity: 0;
    /* 初始隐藏 */
}

span:hover .del {
    opacity: 1;
    /* 鼠标移入时显示 */
}

.del:hover {
    background-color: gray;
    color: white;
}
</style>
