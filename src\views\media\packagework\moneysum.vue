<template>
    <!-- 核算列表 -->
    <my-container  style="width:100%">
           <el-tabs :tab-position="'left'" style="height: 100%;" :before-leave="beforeleave"  v-model="activeName"  @tab-click="tabclick">
               <el-tab-pane name="tab0" label="工作统计" :lazy="true" style="height: 100%;"> <span slot="label"><i class="el-icon-data-analysis"></i> 工作统计</span>
                   <!-- <mediaWorkCommission style="height: 85vh;"  ref="mediaWorkCommission" key="mediaWorkCommission" :classType ="0"> </mediaWorkCommission>  -->
                   <tabIndex   ref="mediaWorkCommission" key="mediaWorkCommission" :classType ="0"> </tabIndex>
                   <!-- <tabIndex style="width: 100%; height: 85vh"/> -->
               </el-tab-pane>
               <!-- <el-tab-pane name="tab1" label="道具统计" :lazy="true"> <span slot="label"><i class="el-icon-bangzhu"></i> 道具统计</span>
                   <propStatistics  ref="propStatistics" key="propStatistics" :classType ="0"> </propStatistics>
               </el-tab-pane> -->
               <el-tab-pane style="height: 100%;" name="tab2"  key="mediaTotalCommission" label="薪资核算" :lazy="true">
                  <span slot="label"><i class="el-icon-document-copy"></i> 薪资核算</span>
                    <mediaTotalCommission @chireach="chireach"  ref="mediaTotalCommission" key="mediaTotalCommission" :classType ="0"> </mediaTotalCommission>
               </el-tab-pane>
               <!-- <el-tab-pane name="tab3" style="height: 100%;" v-if="checkPermission('shootingHs-yytc')"  label="义乌提成" :lazy="true"><span slot="label"><i class="el-icon-edit-outline"></i> 义乌提成</span>
                   <mediaNCCommission  ref="mediaNCCommission" key="mediaNCCommission" :classType ="0" @editclosed="editclosed"> </mediaNCCommission>
               </el-tab-pane>
               <el-tab-pane name="tab4" style="height: 100%;" v-if="checkPermission('shootingHs-nctc')"  label="南昌提成" :lazy="true"><span slot="label"><i class="el-icon-edit-outline"></i> 南昌提成</span>
                   <mediaNCCommission  ref="mediaYYCommission" key="mediaYYCommission" :classType ="1" @editclosed="editclosed"> </mediaNCCommission>
               </el-tab-pane> -->
               <el-tab-pane :lazy="true" name="tab5" style="height: 100%;"  v-if="checkPermission('packagesHs-nctc-history')"  label="历史版本"  ><span slot="label"><i class="el-icon-edit-outline"></i> 历史版本</span>
                   <mediaCommissionHistory  ref="mediaCommissionHistory" key="mediaCommissionHistory" :isHistory="isHistory"> </mediaCommissionHistory>
               </el-tab-pane>
           </el-tabs>
   </my-container>

</template>
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import tabIndex from '@/views/media/packagework/tabIndex.vue';
import accountsWorkCount from '@/views/media/shooting/adjustAccounts/accountsWorkCount';
// import mediaNCCommission from '@/views/media/shooting/adjustAccounts/commissionConfig/mediaNCCommission';


import mediaWorkCommission from '@/views/media/packagework/commissionConfig/mediaWorkCommission.vue';
import mediaTotalCommission from '@/views/media/packagework/commissionConfig/mediaTotalCommission';

// import propStatistics from '@/views/media/shooting/adjustAccounts/commissionConfig/propStatistics';


import mediaCommissionHistory from '@/views/media/packagework/commissionHistoryConfig/mediaCommissionHistory.vue';

import shootingchart from '@/views/media/shooting/shootingchart';

export default {
   components: { MyContainer ,accountsWorkCount,shootingchart,vxetablebase,mediaWorkCommission,mediaTotalCommission,mediaCommissionHistory,tabIndex },
   data() {
       return {
           isHistory:true,
           that: this,
           listLoading: false,
           menuview: 1,
           isedit: false,
           activeName:'tab0',
            tab0isfirst:true,
            tab1isfirst:true,
            tab2isfirst:true,
            tab3isfirst:true,
            tab4isfirst:true,
            tab5isfirst:true,
       };
   },
   //向子组件注册方法
   provide () {
       return {
       }
   },
   async mounted() {
        console.log("打印数据",this.activeName)
   },
   methods: {
        chireach(){
            this.$refs.mediaCommissionHistory.initVersionInfo();
        },
       editclosed(val){
           this.isedit = val;
           this.$emit('editclosed',val);
       },
       beforeleave(){
           if(this.isedit){
               this.$message("请先保存再进行操作")
           }
           return !this.isedit;
       },
       handleClose(){

       },
       handleOpen(){

       },
       async tabclick(){
            switch(this.activeName){
                //工作统计
                case 'tab0' :
                    if(this.tab0isfirst){

                        this.tab0isfirst =false;
                    }
                    break;
                //道具统计
                case 'tab1' :
                    if(this.tab1isfirst){
                        this.tab1isfirst =false;
                    }
                    break;
                //薪资核算
                case 'tab2' :
                    if(this.tab2isfirst){
                        await this.$nextTick(() =>{
                            this.$refs.mediaTotalCommission.onSearch();
                        })
                        this.tab2isfirst =false;
                    }
                    break;
                //义乌提成
                case 'tab3' :
                    if(this.tab3isfirst){
                        await  this.$nextTick(() =>{
                            this.$refs.mediaNCCommission.onSearch();
                        })
                        this.tab3isfirst =false;
                    }
                    break;
                //南昌提成
                case 'tab4' :
                    if(this.tab4isfirst){
                        await  this.$nextTick(() =>{
                            this.$refs.mediaYYCommission.onSearch();
                        })
                        this.tab4isfirst =false;
                    }
                    break;
                //历史版本
                case 'tab5' :
                    if(this.tab5isfirst){
                         await  this.$nextTick(() =>{
                             this.$refs.mediaCommissionHistory.initVersionInfo();
                         })
                        this.tab5isfirst =false;
                    }
                    break;
            }
        }

   },
};
</script>
<style lang="scss" scoped>
.content{
//    display: flex;
   flex-direction: row;
}
.content::v-deep .el-tabs__nav{
   padding-left: 0 !important;
}
::v-deep .myheader{
padding: 5px !important;
}
::v-deep .vxe-tools--operate{
    margin-top: -25px !important;
}
</style>

