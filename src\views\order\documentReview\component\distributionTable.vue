<template>
    <MyContainer>
        <vxetablebase :id="'distributionTable202408041747'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :tableData='tableData'
            :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            style="width: 100%; max-height: 200px; margin: 0" />
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
const tableCols = [
    { istrue: true, prop: 'sendWmsName', label: '分仓', align: 'left', width: 'auto' },
    { istrue: true, prop: 'orderCount', label: '订单量', width: 'auto' },
    { istrue: true, prop: 'quantity', label: '销量', width: 'auto' },
]
export default {
    props: {
        distributionProps: {
            type: Array,
            default: []
        }
    },
    components: { MyContainer, vxetablebase },
    data() {
        return {
            that: this,
            tableCols,
            tableData: [],
        }
    },
    mounted() {
        this.tableData = this.distributionProps
    },
    methods: {
    }
}
</script>

<style scoped lang="scss"></style>