<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
            </el-form>
            <el-button type="primary" @click="onSearch">刷新</el-button>
            <el-button type="primary" @click="addButton">添加预估比例</el-button>
        </template>
        <!--列表-->

        <vxetablebase ref="table" :id="'crossBorderCourierFeeAverage202408310425'" :that='that' :isIndex='true'
            :hasexpand='false' :tablefixed='true' @sortchange='sortchange' :tableData='dahuixionglist'
            :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry'
            :showsummary='true' style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button type="primary" @click="onSearch">刷新</el-button>
                    <el-button type="primary" @click="addButton">添加预估比例</el-button>
                </el-button-group>
            </template>
        </vxetablebase>


        <!-- <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :id="'crossBorderEstimatedCost202410150425'" :tableData='dahuixionglist' @select='selectchange'
            :isSelection='false' :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
            :tableCols='tableCols' :loading="listLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button type="primary" @click="onSearch">刷新</el-button>
                    <el-button type="primary" @click="addButton">添加预估比例</el-button>
                </el-button-group>
            </template>
        </ces-table> -->
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getjSpeedDriveList" />
        </template>

        <el-dialog title="导入公摊费率" :visible.sync="dialogVisibleSyj" width="30%" v-dialogDrag>
            <span>
                <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2" :file-list="fileList"
                    :on-change="onsuccess">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px;" size="small" type="success" :loading="uploadLoading"
                        @click="onSubmitupload2">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                    <!-- <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传</my-confirm-button> -->
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-drawer title="编辑预估比例" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
            :visible.sync="editVisible" direction="btt" size="'auto'" class="el-drawer__wrapper"
            style="position:absolute;">
            <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" />
            <div class="drawer-footer">
                <el-button @click.native="editVisible = false">取消</el-button>
                <my-confirm-button type="submit" @click="onEditSubmit" />
            </div>
        </el-drawer>
        <el-drawer title="新增预估比例" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
            :visible.sync="addVisible" direction="btt" size="'auto'" class="el-drawer__wrapper"
            style="position:absolute;">
            <form-create :rule="autoformAdd.rule1" v-model="autoformAdd.fApi" :option="autoformAdd.options" />
            <div class="drawer-footer">
                <el-button @click.native="addVisible = false">取消</el-button>
                <my-confirm-button type="submit" @click="onaddSubmit" />
            </div>
        </el-drawer>
    </my-container>
</template>
<script>

import { getWarehouseEstimatePageList, editWarehouseEstimateAsync } from '@/api/bookkeeper/crossBorderV2'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { formatPlatformkj } from "@/utils/tools";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
const WarehouseTypeList = [
    { name: "佳速达", value: 1 },
    { name: "九方", value: 2 },
    { name: "亚吉达", value: 3 },
    { name: "赤道", value: 4 },
]
const tableCols = [
    {
        istrue: true, prop: 'warehouseType', label: '仓库类别', sortable: 'custom', formatter: (row) => {
            return row.warehouseType ? WarehouseTypeList.filter(item => item.value == row.warehouseType)[0].name : ""
        },
    },
    { istrue: true, prop: 'scale', label: '预估比例', sortable: 'custom' },
    { istrue: true, prop: 'remark', label: '备注', sortable: 'custom' },
    {
        istrue: true,
        prop: "effectiveTime",
        label: "生效日期",
        sortable: "custom",
        formatter: (row) => {
            return row.effectiveTime ? formatTime(row.effectiveTime, "YYYY-MM-DD") : "";
        },
    },
    {
        istrue: true,
        prop: "createdTime",
        label: "创建时间",
        sortable: "custom",
        formatter: (row) => {
            return row.createdTime ? formatTime(row.createdTime, "YYYY-MM-DD") : "";
        },
    },
    {
        istrue: true,
        prop: "updatedTime",
        label: "更新时间",
        sortable: "custom",
        formatter: (row) => {
            return row.updatedTime ? formatTime(row.updatedTime, "YYYY-MM-DD") : "";
        },
    },
    { istrue: true, type: "button", label: '操作', btnList: [{ label: "编辑", handle: (that, row) => that.EditButton(row) }] }
];
export default {
    name: "crossBorderContributoryRate",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, vxetablebase },
    data() {
        return {
            that: this,
            editLoading: false,
            addVisible: false,
            Filter: {


            },
            shopList: [],
            userList: [],
            groupList: [],
            dahuixionglist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: {},
            pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            //
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            platform: 0,
            yearMonth: "",
            editVisible: false,
            WarehouseTypeList: WarehouseTypeList,
            autoform: {
                fApi: {},
                options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
                rule: []
            },
            autoformAdd: {
                fApi: {},
                options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
                rule: []
            },
        };
    },
    async mounted() {
        await this.initform();
        this.onSearch();
    },
    methods: {
        addButton() {
            this.addVisible = true;
        },
        async onaddSubmit() {
            await this.autoformAdd.fApi.validate(async (valid, fail) => {
                if (valid) {
                    const formData = this.autoformAdd.fApi.formData();
                    const res = await editWarehouseEstimateAsync(formData);
                    await this.autoformAdd.fApi.resetFields()
                    if (res.success) {
                        this.$message.success('添加成功！');
                        this.getjSpeedDriveList();
                        this.addVisible = false;
                    }
                } else { }
            })
        },
        EditButton(row) {
            this.editVisible = true
            var arr = Object.keys(this.autoform.fApi);
            if (arr.length > 0)
                this.autoform.fApi.resetFields()
            this.$nextTick(async () => {
                await this.autoform.fApi.setValue(row)
            })
        },
        async onEditSubmit() {
            await this.autoform.fApi.validate(async (valid, fail) => {
                if (valid) {
                    const formData = this.autoform.fApi.formData();
                    const res = await editWarehouseEstimateAsync(formData);
                    if (res.code == 1) {
                        this.$message.success('修改成功！');
                        this.getjSpeedDriveList();
                        this.editVisible = false;
                    }
                } else { }
            })
        },
        async initform() {
            this.autoform.rule = [{ type: 'hidden', field: 'id', title: 'id', value: '' },
            { type: 'select', field: 'warehouseType', title: '仓库类别', value: '', col: { span: 6 }, options: [{ value: 1, label: '佳速达' }, { value: 2, label: '九方' }, { value: 3, label: '亚吉达' }, { value: 4, label: '赤道' }], props: { clearable: true, disabled: true }, validate: [{ type: 'number', required: true, message: '请选择仓库' }] },
            { type: 'input', field: 'scale', title: '预估比例', value: '', props: { min: 0, precision: 0, maxlength: 7 }, validate: [{ pattern: /(^-?[1-9]\d*\.\d+$|^-?0\.\d+$|^-?[1-9]\d*$|^0$)/, message: "输入占比的格式不正确！" }, { required: true, message: '请输入占比' }] },
            { type: 'input', field: 'remark', title: '备注', value: '', props: { readonly: false, maxlength: 200 }, col: { span: 11 } },
            { type: 'DatePicker', field: 'effectiveTime', title: '生效日期', value: '', validate: [{ type: 'string', required: true, message: '请输入生效日期' }], props: { type: 'date', format: 'yyyy-MM-dd', placeholder: '' }, col: { span: 8 } },
            ],
                this.autoformAdd.rule1 = [
                    { type: 'select', field: 'warehouseType', title: '仓库类别', value: '', col: { span: 6 }, options: [{ value: 1, label: '佳速达' }, { value: 2, label: '九方' }, { value: 3, label: '亚吉达' }, { value: 4, label: '赤道' }], props: { clearable: true, disabled: false }, validate: [{ type: 'number', required: true, message: '请选择仓库' }] },
                    { type: 'input', field: 'scale', title: '预估比例', value: '', props: { min: 0, precision: 0, maxlength: 7 }, validate: [{ pattern: /(^-?[1-9]\d*\.\d+$|^-?0\.\d+$|^-?[1-9]\d*$|^0$)/, message: "输入占比的格式不正确！" }, { required: true, message: '请输入占比' }] },
                    { type: 'input', field: 'remark', title: '备注', value: '', props: { readonly: false, maxlength: 200 }, col: { span: 11 } },
                    { type: 'DatePicker', field: 'effectiveTime', title: '生效日期', value: '', validate: [{ type: 'string', required: true, message: '请输入生效日期' }], props: { type: 'date', format: 'yyyy-MM-dd', placeholder: '' }, col: { span: 8 } },
                ]
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onImportSyj() {
            this.dialogVisibleSyj = true
        },
        onsuccess(file, fileList) {
            this.fileList = fileList;
        },
        async onSubmitupload2() {
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请先选取文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true;
            this.$refs.upload2.submit()
        },
        async uploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        async uploadFile2(item) {
            const form = new FormData();
            form.append("upfile", item.file);
            // form.append("yearMonth",this.yearMonth);
            const res = importProcessingCost(form);
            this.$message({ message: '上传成功,正在导入中...', type: "success" });
            this.dialogVisibleSyj = false;
            this.uploadLoading = false
            this.$refs.upload2.clearFiles();
            this.fileList = [];
        },


        onRefresh() {
            this.onSearch()
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getjSpeedDriveList();
        },
        async getjSpeedDriveList() {
            this.Filter.platform = this.platform;
            const para = { ...this.Filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            this.listLoading = true;
            const res = await getWarehouseEstimatePageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.dahuixionglist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>