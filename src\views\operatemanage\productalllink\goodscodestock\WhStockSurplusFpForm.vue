<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-row>
                <el-col :span="24" style="height:442px">
                    <ces-table ref="table" :that='that' :isIndex='false' :hasexpandRight="true" 
                        :hasexpand='true' :tableData='sellist' :tableCols='tableCols' 
                        :isSelection="false"
                        :isSelectColumn='false' :loading="sellistLoading">
                        <template slot="right">
                            <el-table-column label="到期时限" prop="stockInQtyDaily" width="240" fixed="right">
                                <template slot="header" slot-scope="scope">
                                    <div style="display: flex; flex-direction: row; align-items: center;">
                                        <div style="margin-top: 7px;">
                                            <el-tooltip class="item" effect="dark"
                                                content="选择后，将批量同步列表中商品。到达指定期限时，如果运营未进行操作，将自动领取成功。" placement="top">
                                                <i class="el-icon-question">
                                                    到期时限<el-button type="text" @click="onBatchSetExpireTime">批量设置</el-button>
                                                    <br />
                                                    <el-date-picker v-model="batchExpireTime" type="datetime"
                                                        value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss"
                                                        placeholder="选择日期时间">
                                                    </el-date-picker>
                                                    
                                                </i>
                                            </el-tooltip>
                                        </div>
                                    </div>


                                </template>
                                <template slot-scope="{row}">
                                    <el-date-picker v-model="row.expireTime" type="datetime"
                                        value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss"
                                        placeholder="选择日期时间">
                                    </el-date-picker>
                                </template>
                            </el-table-column>

                        </template>
                    </ces-table>
                </el-col>
            </el-row>
        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;padding-top:10px;">
                    <el-button @click="onClose">关闭</el-button>
                    <el-button type="primary" @click="onSave(true)">确认分配</el-button>
                </el-col>
            </el-row>
        </template>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime, formatSecondNewToHour } from "@/utils/tools";
import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";

import { WhStockSurplusFpSubmit } from "@/api/inventory/goodscodestock"
import { formatNoLink } from "@/utils/tools";
import store from '@/store'

var formatSecondToHour1 = function (time) {
    return formatSecondNewToHour(time);
}
const tableCols = [
    { istrue: true, prop: 'picture', label: '图片', width: '50', type: 'images', fixed: 'left' },
    { istrue: true, prop: 'styleCode', label: '系列编码', width: '140', sortable: true, fixed: 'left' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '140', sortable: true, fixed: 'left' },
    { istrue: true, prop: 'goodsName', label: '商品名称', minwidth: '140', sortable: true },

    { istrue: true, prop: 'publicQyt', label: '公有可用数', width: '120', sortable: true},
    { istrue: true, prop: 'personQyt', label: '个人可用数', width: '120', align: 'center', sortable: true },
    { istrue: true, prop: 'syPubQyt', label: '剩余公用可用数', width: '120', sortable: true },
]

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");


export default {
    name: "WhStockSurplusFpForm",
    components: { cesTable, MyContainer, MyConfirmButton },
    data() {
        return {
            that: this,
            batchExpireTime: '',
            sellist: [],
            detaillist: [],
            sellistLoading: false,
            visiblepopoverdetail: false,
            popoverdetailloding: false,
            tableCols: tableCols,
            mode: 3,
            form: {
            },
            //summaryarry: {},
            pageLoading: false,
            curRow: null,
            formEditMode: true,//是否编辑模式  

        };
    },
    async mounted() {
    },
    computed: {

    },
    methods: {
        onBatchSetExpireTime(){
          
            if(!this.batchExpireTime){
                this.$message.warning( "请选择要批量选择的到期时限！");
                return;
            }

            this.sellist.forEach((item)=>{
                item.expireTime=this.batchExpireTime;
            });
        },
        async loadData({ selRows }) {
            this.pageLoading = true;
            let dts = selRows.map((item) => {
                let d = { ...item,expireTime:null };
                return d;
            });

            this.pageLoading = false;
            this.sellist = dts;
        },
        onClose() {
            this.$emit('close');
        },
        async onSave(isClose) {
            if (await this.save()) {
                this.$emit('afterSave');
                if (isClose)
                    this.$emit('close');
            }
        },
        async save() {
            let saveData = [...this.sellist];

            try {
                saveData.forEach(row => {
                    let v = row.expireTime;
                    if (v == null || v == "") {
                        this.$message.warning(row.goodsCode + "请选择到期时限！");
                        throw "校验失败";
                    }
                });
            } catch (e) {
                console.log(e);
                return;
            }
            this.pageLoading = true;
            let rlt = await WhStockSurplusFpSubmit(saveData);
            if (rlt && rlt.success) {
                this.$message.success("操作成功！无剩余公用可用数或7天内无销量的商品将自动忽略！");
            } 
            this.pageLoading = false;
            return (rlt && rlt.success);
        },

    },
};
</script>
