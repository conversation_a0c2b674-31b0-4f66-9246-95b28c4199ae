<template>
  <el-container style="height:100%;">
    <my-container v-loading="pageLoading" style="width:54%;">
      <template #header>
        <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
          <el-form-item label="付款时间:">
            <el-date-picker
              style="width:230px"
              v-model="filter.timerange"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始"
              end-placeholder="结束"
              :picker-options="pickerOptions"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="商品编码:">
              <el-input v-model.trim="filter.goodsCode" placeholder="商品编码" :maxLength="40" clearable />
          </el-form-item>
          <el-form-item label="款式编码:">
              <el-input v-model.trim="filter.styleCode" placeholder="款式编码" :maxLength="40" clearable />
          </el-form-item>
          <br/>
          <!-- <el-form-item label="原运营组:">
           <el-select v-model="filter.groupIdSrc"   placeholder="请选择" style="width:230px;" :clearable="true" :filterable="true">
              <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key"/>
          </el-select>
        </el-form-item> -->
        <el-form-item label="运营组:" >
           <el-select v-model="filter.groupIdNew"   placeholder="请选择" style="width:178px;" :clearable="true" :filterable="true">
              <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key"/>
          </el-select>
        </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchChartsSameTime">刷新右侧</el-button>
          </el-form-item>
        </el-form>
      </template>
      <ces-table
        ref="mainTable"
        :that='that'
        :isIndex='true'
        :hasexpand='false'
        @sortchange='sortchange'
        :summaryarry="summaryarry"
        :tableData='list'
        :tableCols='tableCols'
        :tableHandles='tableHandles'
        :isSelection="true"
        @select="selsChange"
        :loading="listLoading">
      </ces-table>
      <template #footer>
        <my-pagination
          ref="pager"
          :total="total"
          :checked-count="sels.length"
          @get-page="getlist"
        />
      </template>
      <el-popover ref="editPopover"  v-model="visiblepopover" :reference="prevTarget" :key="popperFlag" :width="900" placement="bottom" popper-class="mypopper">
        <el-table :data="detailList">
          <el-table-column width="150" property="goodsCode" label="商品编码"></el-table-column>
          <el-table-column width="auto" property="goodsName" label="商品名称"></el-table-column>
          <el-table-column width="110" property="groupName" label="运营组"></el-table-column>
          <el-table-column width="150" property="qty" label="销售数量"></el-table-column>
          <el-table-column width="150" property="saleRatio" label="销售占比（%）"></el-table-column>
        </el-table>
      </el-popover>

      <el-dialog :visible.sync="proDetail.visible" width="54%" append-to-body v-dialogDrag>
        <!-- <el-card style="padding:1px;margin-bottom:10px;"> -->
         <div style="margin-bottom:10px;">
             <el-descriptions :column="3" size="mini" border>
                  <el-descriptions-item label="商品编码">{{proDetail.showRow.goodsCode}}</el-descriptions-item>
                  <el-descriptions-item label="商品名称" min-width="150">{{proDetail.showRow.goodsName}}</el-descriptions-item>
                  <el-descriptions-item label="销售数量">{{proDetail.showRow.qty}}</el-descriptions-item>
            </el-descriptions>
         </div>

         <el-form
            class="ad-form-query3"
            :inline="true"
            @submit.native.prevent>
            <el-form-item label="付款时间:">
            <el-date-picker
              style="width:230px"
              v-model="proDetail.filter.timerange"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始"
              end-placeholder="结束"
              :picker-options="pickerOptions"
              @change="onSearchProDetail"
              :clearable="false"
            ></el-date-picker>
           </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearchProDetail">刷新</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onExportProDetail">导出</el-button>
            </el-form-item>
            <!-- <el-form-item>
              <el-button type="success" size="mini" @click="copyDeductMoneyTabooDetailOrderNos">一键复制所有订单号</el-button>
            </el-form-item> -->
          </el-form>
       <!-- </el-card>  -->

      <ces-table ref="tableProDetail" :that='proDetail.that' :isIndex='true' :hasexpand='false' @sortchange='sortchangeProDetail' :tableData='proDetail.tableData'
         :tableCols='proDetail.tableCols' :loading="proDetail.listLoading" :isSelectColumn="false"  style="height:510px;">
        </ces-table>
        <!--分页-->
        <my-pagination
            ref="proDetailPager"
            :total="proDetail.total"
            @get-page="getDetailList"
          />
    </el-dialog>

    <el-dialog :visible.sync="groupStockFinanceDialog.visible" width="54%" append-to-body title="库存资金使用统计"  v-dialogDrag>
         <el-form
            class="ad-form-query3"
            :inline="true"
            @submit.native.prevent>
            <el-form-item label="库存天数下限">
                <el-select v-model="groupStockFinanceDialog.filter.lowerLimit" placeholder="请选择" class="el-select-content"  style="width:100px;"
                  @change="changeLowerLimit">
                    <el-option
                      v-for="item in groupStockFinanceDialog.lowerLimitList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="库存天数上限">
                <el-select v-model="groupStockFinanceDialog.filter.upperLimit" placeholder="请选择" class="el-select-content" style="width:100px;"
                  @change="changeUpperLimit">
                    <el-option
                      v-for="item in groupStockFinanceDialog.upperLimitList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearchChartsGroupStockFinance">刷新</el-button>
            </el-form-item>
          </el-form>

          <div style="margin-top:20px;"
               v-loading="chartsLoading.groupStockFinanceCharts"
               element-loading-text="加载中"
               element-loading-spinner="el-icon-loading">
                <div id="groupStockFinanceCharts"></div>
          </div>
    </el-dialog>

    <el-dialog :visible.sync="proCodeCountDetail.visible" width="54%" append-to-body title="编码链接"  v-dialogDrag>
        <!-- <el-card style="padding:1px;margin-bottom:10px;"> -->
         <div style="margin-bottom:10px;">
             <el-descriptions :column="2" size="mini" border>
                  <el-descriptions-item label="商品编码">{{proCodeCountDetail.showRow.goodsCode}}</el-descriptions-item>
                  <el-descriptions-item label="商品名称" min-width="150">{{proCodeCountDetail.showRow.goodsName}}</el-descriptions-item>
            </el-descriptions>
         </div>

         <el-form
            class="ad-form-query3"
            :inline="true"
            @submit.native.prevent>
            <el-form-item>
              <el-button type="primary" @click="onSearchProCodeCountDetail">刷新</el-button>
            </el-form-item>
            <!-- <el-form-item>
              <el-button type="primary" @click="onExportProDetail">导出</el-button>
            </el-form-item> -->
            <!-- <el-form-item>
              <el-button type="success" size="mini" @click="copyDeductMoneyTabooDetailOrderNos">一键复制所有订单号</el-button>
            </el-form-item> -->
          </el-form>
       <!-- </el-card>  -->

      <ces-table ref="tableProCodeCountDetail" :that='proCodeCountDetail.that' :isIndex='true' :hasexpand='false'
         @sortchange='sortchangeProCodeCountDetail' :tableData='proCodeCountDetail.tableData'
         :tableCols='proCodeCountDetail.tableCols' :loading="proCodeCountDetail.listLoading" :isSelectColumn="false"  style="height:510px;">
        </ces-table>
        <!--分页-->
        <my-pagination
            ref="proCodeCountDetailPager"
            :total="proCodeCountDetail.total"
            @get-page="getProCodeCountDetailList"
          />
    </el-dialog>

    </my-container>
    <my-container style="width:46%;" v-show="isShowRight">
        <template #header>
            <div style="margin-top:0px;margin-right:5px;" v-show="showChartsRow">
                <el-descriptions :column="3" size="mini" border>
                      <el-descriptions-item label="商品编码">{{chartsRow.goodsCode}}</el-descriptions-item>
                      <el-descriptions-item label="商品名称" min-width="150">{{chartsRow.goodsName}}</el-descriptions-item>
                      <el-descriptions-item label="销售数量">{{chartsRow.qty}}</el-descriptions-item>
                </el-descriptions>
            </div>
        </template>
             <el-row>
              <el-col :span="12">
                  <div style="margin-top:20px;"
                    v-loading="chartsLoading.groupCharts"
                    element-loading-text="加载中"
                    element-loading-spinner="el-icon-loading">
                    <div id="groupCharts"></div>
                  </div>
              </el-col>
              <el-col :span="12"
                v-loading="chartsLoading.shopCharts"
                element-loading-text="加载中"
                element-loading-spinner="el-icon-loading">
                  <div style="margin-top:20px;">
                      <div id="shopCharts"></div>
                  </div>
              </el-col>
            </el-row>
        <template #footer>
            <div v-show="shopGroupByDateWay">
              <el-radio-group v-model="filter.groupByDateWay" @change="changeGroupByDateWay">
                <el-radio-button label="日" class="dateBtn">
                </el-radio-button>
                <el-radio-button label="周"></el-radio-button>
                <el-radio-button label="月"></el-radio-button>
              </el-radio-group>
            </div>
            <div style="margin-top:20px;"
               v-loading="chartsLoading.groupWeekCharts"
               element-loading-text="加载中"
               element-loading-spinner="el-icon-loading">
                <div id="groupWeekCharts"></div>
            </div>
        </template>
    </my-container>
  </el-container>
</template>
<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { formatLinkProCode,formatPlatform,pickerOptions } from "@/utils/tools";
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { getList as getshopList,getDirectorList,getDirectorGroupList } from "@/api/operatemanage/base/shop";
import {
  pageOrderGoodsSales,
  exportOrderGoodsSalesDetail,
  getOrderGoodsSalesDetail,
  getOrderGoodsSalesPie,
  getOrderGoodsSalesProDetail,
  exportOrderGoodsSalesProDetail,
  getOrderGoodsSalesGroupWeek,
  getOrderGoodsSalesGroupStockFinance,
} from "@/api/order/ordergoods";
import {
  getGoodsProduct
} from "@/api/operatemanage/base/productbianma";
import * as echarts from "echarts";
const tableCols =[
       {istrue:true,prop:'goodsCode',label:'商品编码', width:'90',type:'click',handle:(that,row,column,cell)=>that.canclick(row,column,cell)},
       {istrue:true,prop:'styleCode',label:'款式编码', width:'90'},
       {istrue:true,prop:'goodsName',label:'商品名称', width:'auto'},
       {istrue:true,prop:'groupNameNew',label:'运营组', width:'80'},
       {istrue:true,prop:'goodsImage',label:'图片', width:'60',type:'imageGoodsCode',goods:{code:'goodsCode',name:'goodsName'}},
       {istrue:true,prop:'groupCount',label:'小组数', width:'80',sortable:'custom',type:'click',handle:(that,row,column,cell)=>that.canclick(row,column,cell)},
       {istrue:true,prop:'proCodeCount',label:'链接数', width:'80',sortable:'custom',type:'click',handle:(that,row,column,cell)=>that.canclick(row,column,cell)},
       {istrue:true,prop:'qty',label:'销售数量', width:'90',sortable:'custom'},
       {istrue:true,prop:'saleRatio',label:'销售占比', width:'90',sortable:'custom',formatter:(row)=>row.saleRatio+"%"},
       {istrue:true,label:"操作",width:"50",type:'button',btnList:[{label:"明细",handle:(that,row)=>that.onShowDetail(row)}]},
     ];
const tableHandles1=[
        {label:"库存资金使用统计", handle:(that)=>that.onSearchChartsGroupStockFinance()},
        {label:"导出", handle:(that)=>that.onExport()}
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: {
        startDate:null,
        endDate:null,
        goodsCode:null,
        timerange:[formatTime(dayjs().startOf("month"), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")],
        groupIdSrc:null,
        groupIdNew:null,
        groupByDateWay:"日",
        groupByDay:true,//按日分组
        groupByWeek:false,//按周分组
        groupByMonth:false,//按月分组
      },
      list: [],
      summaryarry:{},
      pager:{OrderBy:"saleRatio",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      treeprops:{
        children: 'children',
        hasChildren: 'hasChildren'
      },
      platformList: [],
      directorGroupList:[],
      shopList: [],
      dialogVisible: false,
      autoform:{
               fApi:{},
               rule:[],
               options:{submitBtn:false},
        },
      total: 0,
      sels: [],
      isShowRight:true,
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,
      deleteLoading: false,
      formtitle:"新增",
      fileList:[],
      pickerOptions:pickerOptions,
      //图表配置 Start
      myChart:null,
      myChartShop:null,
      myChartGroupWeek:null,
      myChartGroupStockFinance:null,
      piesData:[],
      chartsRow:{},//图表的行
      Ylist: [
        { value: 0, unit: ""},
      ],
      chartsLoading:{
        groupCharts:false,
        shopCharts:false,
        groupWeekCharts:false,
        groupStockFinanceCharts:false,
      },
      showChartsRow:false,
      shopGroupByDateWay:false,
      groupStockFinanceDialog:{ //库存资金统计图表弹窗
        filter:{
          upperLimit:360,//库存天数上限
          lowerLimit:0,//库存天数下限
        },
        upperLimitList:[
          {value:30,label:30},
          {value:60,label:60},
          {value:90,label:90},
          {value:180,label:180},
          {value:360,label:360},
        ],
        lowerLimitList:[
          {value:0,label:0},
          {value:30,label:30},
          {value:60,label:60},
          {value:90,label:90},
          {value:180,label:180},
        ],
        visible:false
      },
      //图表配置 End
      visiblepopover: false,
      prevTarget: null, // 编辑 Popover 的 Reference （参照），用于 popover.js 对齐两个元素
      popperFlag: false, // 用于编辑 Popover 的刷新
      visiblepopoverdetail: false,
      dialogOrderDetailVisible:false,
      popperFlagdetail: false,
      detailList:[],
      proDetail:{
        title:"",
        visible:false,
        tableCols:[
          {istrue:true,prop:'platform',label:'平台', width:'80',sortable:'custom',formatter:(row)=>row.platformName},
          {istrue:true,prop:'shopCode',label:'店铺', width:'140',sortable:'custom',formatter:(row)=>row.shopName},
          {istrue:true,prop:'proCode',label:'宝贝ID', width:'140',sortable:'custom',type:"html",formatter:(row)=>formatLinkProCode(row.platformName,row.proCode)},
          {istrue:true,prop:'proName',label:'宝贝名称', width:'auto',sortable:'custom'},
          {istrue:true,prop:'groupId',label:'运营组', width:'100',sortable:'custom',formatter:(row)=>row.groupName},
          {istrue:true,prop:'orderCount',label:'订单量', width:'80',sortable:'custom'},
          {istrue:true,prop:'sales',label:'销量', width:'80',sortable:'custom'},
          {istrue:true,prop:'operateSpecialUserId',label:'运营专员', width:'90',sortable:'custom',formatter:(row)=>row.director},
          {istrue:true,prop:'userId',label:'运营助理', width:'90',sortable:'custom',formatter:(row)=>row.directorAssistant},
        ],
        tableData:[],
        total:0,
        sels:[],
        listLoading:false,
        pager:{OrderBy:"orderCount",IsAsc:false},
        pageLoading: false,
        that:this,
        filter:{
          goodsCode:null,
          goodsName:null,
          startDate:null,
          endDate:null,
          timerange:[],
        },
        showRow:{},//明细的行
      },
      proCodeCountDetail:{
        title:"链接数明细",
        visible:false,
        tableCols:[
          {istrue:true,prop:'platform',label:'平台', width:'80',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
          {istrue:true,prop:'shopCode',label:'店铺', width:'180',sortable:'custom',formatter:(row)=>row.shopName},
          {istrue:true,prop:'proCode',label:'宝贝ID', width:'160',sortable:'custom',type:"html",formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
          {istrue:true,prop:'title',label:'宝贝名称', width:'auto',sortable:'custom',},
          {istrue:true,prop:'groupId',label:'运营组', width:'100',sortable:'custom',formatter:(row)=>row.groupName},
          { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', width: '80', sortable: 'custom', formatter: (row) => row.operateSpecialUserName || ' ' },
          { istrue: true, prop: 'userId', label: '运营助理', width: '80', sortable: 'custom', formatter: (row) => row.userRealName || ' ' },
        ],
        tableData:[],
        total:0,
        sels:[],
        listLoading:false,
        pager:{OrderBy:"proCode",IsAsc:true},
        pageLoading: false,
        that:this,
        filter:{
          goodsCode:null,
          goodsName:null,
          groupIdNew:null
        },
        showRow:{},//明细的行
      },
    }
  },
  async mounted() {
    await this.setDirectorGroupList();
    var res = await this.setFilterFromQuery();
    if(!res){
      //await this.onSearch();
    }
  },
  methods: {
    //从查询字符串中获取参数
    async setFilterFromQuery(){
      if(this.$route.query.goodsCode){
          this.filter.goodsCode=this.$route.query.goodsCode;
          this.filter.groupIdSrc=null;
          this.filter.groupIdNew=null;
          if(this.$route.query.timerange){
            this.filter.timerange=this.$route.query.timerange;
          }
          await this.onSearch();
          return true;
      }

      return false;
    },
    //设置运营组下拉
    async setDirectorGroupList(){
        var res=await getDirectorGroupList();
        this.directorGroupList=res.data;
    },
    //导出
    async onExport(){
        var params=this.getCondition();
        if(params===false){
            return;
        }
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportOrderGoodsSalesDetail(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','商品销售分析_' +this.filter.goodsCode+'_'+ new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },
    //获取查询条件
    getCondition(){
        if (this.filter.timerange&&this.filter.timerange.length>1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      else {
        this.$message({message:"请先选择付款时间",type:"warning"});
        return false;
      }
      if(!this.filter.goodsCode){
        //this.$message({message:"请先输入商品编码",type:"warning"});
        //return false;
      }
      var pager = this.$refs.pager.getPager();
      var page  = this.pager;
      const params = {
        ...pager,
        ...page,
        ... this.filter
      }

      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1);
      await this.getlist();
    },
    //分页查询
    async getlist() {
      var params=this.getCondition();
      if(params===false){
        return;
      }
      this.clearEditPopperComponent();
      this.listLoading = true
      const res = await pageOrderGoodsSales(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry=res.data.summary;
      data.forEach(d => {
        d._loading = false
      })
      this.list = data;
      this.isShowRight=this.list?.length>0;

    },
    //排序查询
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else{
        var orderBy =column.prop=="shopName"?"shopCode":column.prop;
        this.pager={OrderBy:orderBy,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    },
    //查询明细第一页
    async onSearchProDetail() {
      this.$refs.proDetailPager.setPage(1)
      await this.getDetailList()
    },
    //明细排序查询
    async sortchangeProDetail(column){
      if(!column.order)
        this.proDetail.pager={OrderBy:null,IsAsc:false};
      else{
        this.proDetail.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearchProDetail();
    },
    //宝贝ID明细查询条件
    getProDetailCondition(){
          var params=this.getCondition();
          if(params===false){
                return false;
          }
          if (this.proDetail.filter.timerange&&this.proDetail.filter.timerange.length>1) {
            this.proDetail.filter.startDate = this.proDetail.filter.timerange[0];
            this.proDetail.filter.endDate = this.proDetail.filter.timerange[1];
          }
          else {
            this.$message({message:"请先选择付款时间",type:"warning"});
            return false;
          }
          var pager = this.$refs.proDetailPager.getPager();
          var page  = this.proDetail.pager;
          params = {
            ...params,
            ...pager,
            ...page,
            ...this.proDetail.filter
          }

          return params;
    },
    //明细查询
    async getDetailList() {
      this.proDetail.visible=true;
      this.proDetail.title="商品编码："+this.proDetail.filter.goodsCode +" 商品名称：" +this.proDetail.filter.goodsName;
      setTimeout(async () => {
          var params=this.getProDetailCondition();
          if(params===false){
                return false;
          }
          this.proDetail.listLoading = true;
          const res = await getOrderGoodsSalesProDetail(params);
          this.proDetail.listLoading = false;
          if (!res?.success) {
            return
          }
          this.proDetail.total=res.data.total;
          const data = res.data.list;
          data.forEach(d => {
            d._loading = false
          })
          this.proDetail.tableData = data;
      }, 200);

    },
    //查看明细
    async onShowDetail(row){
        this.proDetail.filter.goodsCode=row.goodsCode||"";
        this.proDetail.filter.goodsName=row.goodsName||"";
        this.proDetail.showRow=row;
        this.proDetail.filter.timerange=this.filter.timerange;
        await this.getDetailList();
    },
    //导出明细
    async onExportProDetail(){
        var params=this.getProDetailCondition();
        if(params===false){
            return false;
        }
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportOrderGoodsSalesProDetail(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','商品销售分析ID明细_' +this.proDetail.filter.goodsCode+'_'+ new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },
    //单击单元格
    async cellclick(row, column, cell, event){
     if (column.property=='groupCount') {
       this.chartsRow=row;
       this.$refs.mainTable.clearSelection();console.log(row)
       this.$refs.mainTable.toggleRowSelection(row,true);
       this.sels=[row];
       this.searchChartsSameTime();
     }
     else if (column.property=='goodsCode') {
        this.$router.push({
          name:"/order/ordergoodsrelevance",
          query:{
            goodsCode:row.goodsCode,
            timerange:this.filter.timerange,
          }
        })
     }
     else if(column.property=='proCodeCount')
     {
        await this.onShowProCodeCountDetail(row);
     }
    },
    //canclick类型的单元格span标签点击
    async canclick(row, column, cell){
        column.property=column.prop;
        await this.cellclick(row, column, cell);
    },
    // 清空编辑组件
   async clearEditPopperComponent() {
      this.prevTarget = null;
      this.popperFlag = !this.popperFlag;
      this.popperFlagdetail= !this.popperFlagdetail;
      this.visiblepopover = false;
      this.visiblepopoverdetail= false;
    },
    //获取图表的查询条件
    getChartsCondition(){
        var params=this.getCondition();
        if(params===false){
          return false;
        }
        if(this.sels&&this.sels.length>0){
          params.goodsCode=this.sels.map(a=>a.goodsCode).join(",");
          return params;
        }
        return false
    },
    //图表搜索 - 运营组
    async onSearchCharts(goodsCode,goodsName) {
      var params=this.getChartsCondition();
      if(params===false){
        return false;
      }

      params.byGroup=true;
      params.byShop=false;
      params.byPlatform=false;

      this.chartsLoading.groupCharts=true;
      const res = await getOrderGoodsSalesPie(params);
      this.chartsLoading.groupCharts=false;
      if (!res?.code) {
        return false;
      }
      var chartDom = document.getElementById("groupCharts");
      this.myChart && this.myChart.clear();
      this.piesData = res.data;
      this.myChart = this.myChart ?? echarts.init(chartDom);

      var option = await this.Getoptions(res.data,'运营组分布：');
      await option && this.myChart.setOption(option);
    },
    //图表搜索 - 店铺
    async onSearchChartsShop(goodsCode,goodsName) {
      var params=this.getChartsCondition();
      if(params===false){
        return false;
      }

      params.byGroup=false;
      params.byShop=true;
      params.byPlatform=false;
      this.chartsLoading.shopCharts=true;
      const res = await getOrderGoodsSalesPie(params);
      this.chartsLoading.shopCharts=false;
      if (!res?.code) {
        return false;
      }
      var chartDom = document.getElementById("shopCharts");
      this.myChartShop && this.myChartShop.clear();
      this.myChartShop = this.myChartShop ?? echarts.init(chartDom);

      var option = await this.Getoptions(res.data,'店铺分布：');
      await option && this.myChartShop.setOption(option);
    },
    //图表搜索 - 平台
    async onSearchChartsPlatform(goodsCode,goodsName) {
      var params=this.getChartsCondition();
      if(params===false){
        return false;
      }

      params.byGroup=false;
      params.byShop=false;
      params.byPlatform=true;
      this.chartsLoading.shopCharts=true;
      const res = await getOrderGoodsSalesPie(params);
      this.chartsLoading.shopCharts=false;
      if (!res?.code) {
        return false;
      }
      var chartDom = document.getElementById("shopCharts");
      this.myChartShop && this.myChartShop.clear();
      this.myChartShop = this.myChartShop ?? echarts.init(chartDom);

      var option = await this.Getoptions(res.data,'平台分布：');
      await option && this.myChartShop.setOption(option);
    },
    //图表搜索 - 运营组 周分布
    async onSearchChartsGroupWeek(goodsCode) {
      var params=this.getChartsCondition();
      if(params===false){
        return false;
      }

      this.chartsLoading.groupWeekCharts=true;
      this.shopGroupByDateWay=false;
      const res = await getOrderGoodsSalesGroupWeek(params);
      this.chartsLoading.groupWeekCharts=false;
      if (!res?.code) {
        return false;
      }
      this.shopGroupByDateWay=true;
      var chartDom = document.getElementById("groupWeekCharts");
      this.myChartGroupWeek && this.myChartGroupWeek.clear();
      this.myChartGroupWeek = this.myChartGroupWeek ?? echarts.init(chartDom);

      var option = await this.GetoptionsLine(res.data,'销量趋势：');
      await option && this.myChartGroupWeek.setOption(option);
    },
    //图表配置
    async Getoptions(data,title) {
      var option = {
        title: { text: title,left: 'left' },
        tooltip: {
          trigger: "item",
          textStyle: { align: "left" },
        },
        legend: {
          formatter: function (name) {
            return echarts.format.truncateText(
              name,
              200,
              "10px Microsoft Yahei",
              "..."
            );
          },
          tooltip: {
            show: true,
          },
          // left: 'center',
          // bottom:'0%',
          type: "scroll",
          pageIconColor: "#409EFF",
          pageIconInactiveColor: "#909399",
          width: "100%",
          orient: 'vertical',  //垂直显示
          y: 'center',    //延Y轴居中
          x: 'right' //居右显示
        },
        grid: {
          left: "1%",
          right: "1%",
          bottom: "1%",
          containLabel: true,
        },
        series: {
          type: 'pie',
          radius: '45%',
          center: ["40%", "58%"],
          data:data,
          /*emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
          },*/
          labelLine: {
              show: true
          },
          label:{
            normal:{
               show:true,
               formatter:'{d}%',//'{b}'+'\n\r'+'{c}' + '\n\r' + '{d}%',
               position:'outside'
            }
          }
        },
      };
      return option;
    },
    //折线/柱形图图表配置
    async GetoptionsLine(element,title,type) {
      if(!element) return;
      var colors = [
        "#5470C6",
        "#c77eb5",
        "#EE6666",
        "#409EFF",
        "#00ae9d",
        "#67C23A",
      ];
      var series = [];
      element.series.forEach((s) => {
        series.push({ smooth: true, ...s });
      });
      var legendData = element.legend||[];
      var yAxis = [];
      var left = true;
      var leftOffset = 0;
      var rightOffet = 0;
      var ii = 0;
      this.Ylist.forEach((s) => {
        yAxis.push({
          type: "value",
          name: s.label,
          show: true,
          axisLabel: {
            formatter: "{value}" + s.unit,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: colors[ii++],
            },
          },
          position: left ? "left" : "right",
          offset: left ? leftOffset : rightOffet,
        });
        left ? (leftOffset += 50) : (rightOffet += 50);
        left = !left;
      });
      var option = {
        title: { text: title },
        tooltip: {
          trigger: "axis",
          textStyle: { align: "left" },
        },
        legend: {
          formatter: function (name) {
            return echarts.format.truncateText(
              name,
              200,
              "10px Microsoft Yahei",
              "..."
            );
          },
          tooltip: {
            show: true,
          },
          data: legendData,
          type: "scroll",
          pageIconColor: "#409EFF",
          pageIconInactiveColor: "#909399",
          width: "75%",
        },
        grid: {
          left: "3%",
          right: "3%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            magicType: { show: true, type: ["line", "bar"] },
            //restore: { show: false },
          },
        },
        xAxis: {
          type: "category",
          data: element.xAxis,
          axisLabel: {
            interval:0,
            rotate:40
          },
        },
        yAxis: yAxis,
        series: series,
      };
      return option;
    },
    searchChartsSameTime(){
      setTimeout(async() => {
          await this.onSearchCharts(this.chartsRow.goodsCode,this.chartsRow.goodsName);
      }, 10);

      setTimeout(async() => {
          await this.onSearchChartsPlatform(this.chartsRow.goodsCode,this.chartsRow.goodsName);
      }, 20);

      setTimeout(async() => {
              await this.onSearchChartsGroupWeek(this.chartsRow.goodsCode);
      }, 30);
    },
    async changeGroupByDateWay(){
      this.filter.groupByDay=false;
      this.filter.groupByWeek=false;
      this.filter.groupByMonth=false;
      switch(this.filter.groupByDateWay){
        case "日":
          this.filter.groupByDay=true;
          break;
        case "周":
          this.filter.groupByWeek=true;
          break;
        case "月":
          this.filter.groupByMonth=true;
          break;
      }

      await this.onSearchChartsGroupWeek(this.chartsRow.goodsCode);
    },
    //图表搜索 - 运营组库存资金统计汇总
    async onSearchChartsGroupStockFinance() {
      var params=this.getCondition();
      if(params===false){
        return false;
      }
      params={...params,...this.groupStockFinanceDialog.filter};
      if(params.lowerLimit>params.upperLimit){
        $.$message({message:"上限需大于或等于下限",type:"warning"});
        return;
      }
      this.groupStockFinanceDialog.visible=true;
      this.chartsLoading.groupStockFinanceCharts=true;
      const res = await getOrderGoodsSalesGroupStockFinance(params);
      this.chartsLoading.groupStockFinanceCharts=false;
      if (!res?.code) {
        return false;
      }
      var chartDom = document.getElementById("groupStockFinanceCharts");
      this.myChartGroupStockFinance && this.myChartGroupStockFinance.clear();
      this.myChartGroupStockFinance = this.myChartGroupStockFinance ?? echarts.init(chartDom);

      var option = await this.GetoptionsLine(res.data,'库存资金（元）','bar');
      await option && this.myChartGroupStockFinance.setOption(option);
    },
    async changeLowerLimit(){
      if(this.groupStockFinanceDialog.filter.lowerLimit>this.groupStockFinanceDialog.filter.upperLimit){
        return;
      }
      await this.onSearchChartsGroupStockFinance();
    },
    async changeUpperLimit(){
      if(this.groupStockFinanceDialog.filter.lowerLimit>this.groupStockFinanceDialog.filter.upperLimit){
        return;
      }
      await this.onSearchChartsGroupStockFinance();
    },
    //==链接数明细查询 Start===============================================
    //查询明细第一页
    async onSearchProCodeCountDetail() {
      this.proCodeCountDetail.visible=true;
      setTimeout(async () => {
        this.$refs.proCodeCountDetailPager.setPage(1);
        await this.getProCodeCountDetailList();
      }, 100);
    },
    //明细排序查询
    async sortchangeProCodeCountDetail(column){
      if(!column.order)
        this.proCodeCountDetail.pager={};
      else{
        this.proCodeCountDetail.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearchProCodeCountDetail();
    },
    //链接数明细查询条件
    getProCodeCountDetailCondition(){
          this.proCodeCountDetail.filter.groupIdNew = this.filter.groupIdNew;
          var pager = this.$refs.proCodeCountDetailPager.getPager();
          var page  = this.proCodeCountDetail.pager;
          var params = {
            ...params,
            ...pager,
            ...page,
            ...this.proCodeCountDetail.filter
          }

          return params;
    },
    //明细查询
    async getProCodeCountDetailList() {
      this.proCodeCountDetail.visible=true;
      this.proCodeCountDetail.title="商品编码："+this.proCodeCountDetail.filter.goodsCode +" 商品名称：" +this.proCodeCountDetail.filter.goodsName;
      setTimeout(async () => {
          var params=this.getProCodeCountDetailCondition();
          if(params===false){
                return false;
          }
          if (this.filter.timerange&&this.filter.timerange.length>1) {
            params.startDate = this.filter.timerange[0];
            params.endDate = this.filter.timerange[1];
          } else {
            params.startDate = null;
            params.endDate = null;
          }
          this.proCodeCountDetail.listLoading = true;
          console.log(params,"params");
          const res = await getGoodsProduct(params);
          this.proCodeCountDetail.listLoading = false;
          if (!res?.success) {
            return
          }
          this.proCodeCountDetail.total=res.data.total;
          const data = res.data.list;
          data.forEach(d => {
            d._loading = false
          })
          this.proCodeCountDetail.tableData = data;
      }, 200);

    },
    //查看明细
    async onShowProCodeCountDetail(row){
        this.proCodeCountDetail.filter.goodsCode=row.goodsCode||"";
        this.proCodeCountDetail.filter.goodsName=row.goodsName||"";
        this.proCodeCountDetail.showRow=row;
        await this.onSearchProCodeCountDetail();
    },
    //==链接数明细查询 End=================================================
    selsChange: function(sels) {
      this.sels = sels;

      if(sels&&sels.length>0){
        this.chartsRow=sels[0];
      }
    },
  },
  watch:{
    '$route':{
      async handler(route){
        if(route.name=="/order/ordergoodssales"){
          await this.setFilterFromQuery();
        }
      }
    },
    list(val){
        if(this.list&&this.list.length>0){
            this.chartsRow=this.list[0];
            setTimeout(() => {
              //有商品编码条件则选中全部
              if(this.filter.goodsCode){
                this.$refs.mainTable.toggleAllSelection();
                this.sels=[this.list];
              }
              else{
                this.$refs.mainTable.toggleRowSelection(this.chartsRow,true);
                this.sels=[this.chartsRow];
              }
              this.searchChartsSameTime();
            }, 500);

        }
        else{
          this.showChars=false;
        }
    }
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary{
  margin-right: 7px;
}
#groupCharts{
  width:88%;
  height:350px;
}
#shopCharts{
  width:88%;
  height:350px;
}
#groupWeekCharts{
  width:93%;
  height:350px;
  margin-bottom: 10px;
}
#groupStockFinanceCharts{
  width:98%;
  height:360px;
  margin-bottom: 10px;
}
.mypopper{
  background: pink !important;
}
::v-deep .atooltip.el-tooltip__popper[x-placement^="left"] .popper__arrow {
  border-left-color: pink;
}
::v-deep .atooltip.el-tooltip__popper[x-placement^="left"] .popper__arrow:after {
  border-left-color: pink;
}
::v-deep .atooltip {
  background: pink !important;
}

.dateBtn{
  position: relative !important;
}
.dateBtn .el-date-editor{
  position: absolute;
  top:0;
  left:0;
  opacity: 0;
}
</style>
