<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :inline="true" :model="form" label-width="130px" label-position="right" :disabled="formEditMode"
                :rules="thisformRules">
                <el-row>
                    <el-col :span="4">
                        <el-form-item label="竞品平台：">
                            <el-select disabled v-model="form.platform" style="width:100px;">
                                <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="goodsState" label="产品状态：">
                            <el-select v-model="form.goodsState" style="width:180px;" :disabled="true">
                                <el-option v-for="item in goodsStatelist" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="竞品ID：">
                            <div v-html="formatLinkProCode(form.platform, form.goodsCompeteId)" @click="handleClick($event,form.goodsCompeteId)"></div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="运营组：">
                            {{ form.yyGroupName }}
                        </el-form-item>
                    </el-col> 
                </el-row>
                <el-row>
                    <el-col :span="4">
                        <el-form-item label="竞品图片：">
                            <el-image style="width: 50px; height: 50px" :src="form.goodsCompeteImgUrl"
                                :preview-src-list="[form.goodsCompeteImgUrl]">
                            </el-image> 
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="goodsCompeteShortName" label="产品简称：">
                            <el-input v-model="form.goodsCompeteShortName"  :disabled="true" auto-complete="off" style="width:180px;" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="竞品标题：">
                            {{ form.goodsCompeteName }}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                     
                    <el-col :span="4">
                        <el-form-item prop="forNewWarehouse" label="上新仓库：">
                            <el-select v-model="form.forNewWarehouse" style="width: 180px" :disabled="true">
                                <!-- <el-option v-for="item in fomSendWarehouse4HotGoodsBuildGoodsDocList" :key="item.value"
                                    :label="item.label" :value="item.value" /> -->
                                <el-option v-for="item in fomSendWarehouse4HotGoodsBuildGoodsDocList" :key="item.name" :label="item.name"
                                    :value="item.wms_co_id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="质检报告：">
                            <YhImgUpload2Table :value.sync="form.inspectionReportImgUrl"></YhImgUpload2Table>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="专利资质：">
                            <YhImgUpload :value.sync="form.patentQualificationImgUrls" :disabled="true" :limit="10"></YhImgUpload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="专利资质pdf：">
                            <YhImgUpload :value.sync="form.patentQualificationPdfUrls" :disabled="true"  :isImg="false" accept=".pdf" :limit="10" ></YhImgUpload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="包装图片：">
                            <YhImgUpload :value.sync="form.packingImgUrls" :limit="10"  :disabled="true"></YhImgUpload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4"  v-if="form.supplierList && form.supplierList.length>0 && calcCurSupplier.hasProductionLicense==1">
                        <el-form-item prop="hasProductionLicense" label="生产许可证：" style="float: left;">
                            <el-image style="margin-left:10px;width: 50px; height: 50px" :src="calcCurSupplier.productionLicenseUrl" :preview-src-list="[calcCurSupplier.productionLicenseUrl]">
                            </el-image>
                        </el-form-item>
                    </el-col>
                </el-row> 
                <el-row v-if="form.supplierList && form.supplierList.length > 0">
                    <el-col :span="4">
                        <el-form-item prop="supplierPlatForm" label="供应商平台：">
                            <el-select v-model="calcCurSupplier.supplierPlatForm" style="width:100px;" disabled>
                                <el-option v-for="item in supplierPlatFormList" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6" :hidden="(calcCurSupplier.supplierPlatForm != 1)">
                        <el-form-item prop="supplierWxNum" label="微信账号：">
                            <!-- {{ calcCurSupplier.supplierWxNum}} -->
                            <el-input v-model="calcCurSupplier.supplierWxNum" style="width:200px" disabled></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="supplierName" label="供应商名称：">
                            <!-- {{ calcCurSupplier.supplierName}} -->
                            <el-input v-model="calcCurSupplier.supplierName" style="width:200px" disabled></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6" :hidden="(calcCurSupplier.supplierPlatForm != 2)">
                        <el-form-item label="供应商链接：">
                            <!-- {{ calcCurSupplier.supplierLink}} -->
                            <el-input v-model="calcCurSupplier.supplierLink" style="width:200px" disabled></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6" :hidden="(calcCurSupplier.supplierPlatForm != 2)">
                        <el-form-item prop="supplierGoodLink" label="产品链接：">
                            <!-- {{ calcCurSupplier.supplierGoodLink}} -->
                            <el-input v-model="calcCurSupplier.supplierGoodLink" style="width:200px" disabled></el-input>
                        </el-form-item>
                    </el-col> 
                </el-row>
                <el-row>
                    <el-col :span="4">
                        <el-form-item prop="outerPackaLanguage" label="外包装语言：">
                            <el-select v-model="form.outerPackaLanguage" style="width:100px;">
                                <el-option label="中文" :value="1"></el-option>
                                <el-option label="英文" :value="2"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="18">
                        <el-form-item prop="remark" label="备注：">
                            <el-input v-model.trim="form.remark"  maxlength="200"  style="width:765px;"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row> 
                    <el-col :span="24">
                        <el-form-item prop="chatImgUrls" label="供应商聊天记录：">
                            <div style="width: 1300px;">
                                <uploadfile ref="uploadChatImg" :islook="formEditMode" :minisize="true"
                                    :uploadInfo="form.chatImgUrls == null ? [] : form.chatImgUrls" :limit="10000"
                                    :accepttyes="'.image/jpg,image/jpeg,image/png'" uploadVerifyType="图片" />
                            </div>
                        </el-form-item>
                    </el-col> 
                </el-row> 
            </el-form>
            <el-form :disabled="formEditMode">
                <el-row>
                    <el-col :span="24">
                        <el-form style="float:left">
                            <el-radio-group v-model="curSupplierId" size="mini" @change="clearnSel()" >
                                <el-radio-button v-for="item in form.supplierList" :label="item.id" :key="item.id">
                                    {{ item.supplierName }}
                                </el-radio-button>
                            </el-radio-group>
                        </el-form>

                        <span style="display:none">
                            {{ calcEstimateStockInCount }}
                            {{ calcEstimateStockInAmount }}
                        </span>
                        <el-button-group style="margin-left:10px;">
                            <el-button type="primary" @click="onOpenAddSupplier">添加供应商</el-button>  
                            <el-button type="primary" @click="onOpenEditSupplier" v-if="form.supplierList && form.supplierList.length>0">修改供应商</el-button>                            
                            <el-button type="danger" @click="onDelSupplier"  v-if="form.supplierList && form.supplierList.length>0" >删除当前供应商</el-button>
                            <el-button type="warning" @click="onOpenChangeSupplier">更换商品供应商</el-button>
                         <!-- <el-button  v-if="form.supplierList && form.supplierList.length>0"  type="primary" @click="skuTableData.push({id:rowTempIndex--,supplierId:curSupplierId,goodsProgressType:'成品',isMainSale:true,isZengPin:false,isJiaGong:false})">新增一行</el-button> -->

                        </el-button-group>
                        <div v-if="form.supplierList && form.supplierList.length > 0"
                            :style="'height:' + tableHeight + 'px;'">
                            <!--列表-->
                            <ces-table ref="skutable" :tablefixed="true" :isSelection="true" :showsummary="true"
                                :summaryarry="summaryarry" :that='that' :isIndex='false' :hasexpandRight='true'
                                :hasexpand='true' :tableData='calcSkuTableData' :tableCols='skuTableCols'
                                :loading="listLoading" :isSelectColumn="false" @select='selectchange' rowkey="id">

                                <template>
                                    <el-table-column width="100" label="商品图片" column-key="id" :render-header="renderHeader"
                                        :disabled="true">
                                        <template slot-scope="scope">
                                            <YhImgUpload2Table :disabled="true" :value.sync="scope.row.goodsImageUrl"></YhImgUpload2Table>
                                        </template>
                                    </el-table-column>
                                </template>

                                <template slot="right">
                                    <el-table-column width="200" label="长宽高（cm）" :render-header="renderHeader">
                                        <template slot-scope="scope">
                                                <el-input-number type="number" :precision="2" :min="0" :max="10000" :controls="false" 
                                                v-model.number="scope.row.goodsLength" style="width:50px;" disabled>
                                                </el-input-number>*
                                                <el-input-number type="number" :precision="2" :min="0" :max="10000" :controls="false" 
                                                v-model.number="scope.row.goodsWidth" style="width:50px;" disabled>
                                                </el-input-number>*
                                                <el-input-number type="number" :precision="2" :min="0" :max="10000" :controls="false" 
                                                v-model.number="scope.row.goodsHeigdth" style="width:50px;" disabled>
                                                </el-input-number> 
                                        </template>
                                    </el-table-column>
                                    <el-table-column width="70" label="重量（kg）" :render-header="renderHeader">
                                        <template slot-scope="scope">
                                                <el-input-number type="number" :precision="2" :min="0" :max="10000" :controls="false" 
                                                v-model.number="scope.row.goodsWeight" style="width:50px;" disabled>
                                                </el-input-number> 
                                        </template>
                                    </el-table-column>
                                    <el-table-column width="150" label="备注">
                                        <template slot-scope="scope">
                                                <el-input type="text" :maxlength="100" :controls="false" v-model="scope.row.remark" style="width:130px;" disabled>
                                                </el-input> 
                                        </template>
                                    </el-table-column>
                                    <el-table-column width="50" label="">
                                        <template slot-scope="scope">
                                            <el-button type="text"
                                                @click="delRow(scope.row)">删除</el-button>
                                        </template>
                                    </el-table-column>
                                </template>
                            </ces-table>
                        </div>

                    </el-col>
                </el-row>
            </el-form>
        </template>

        <el-dialog :title="dialogTitle" :visible.sync="supOpt.visible" width="780px" :close-on-click-modal="false" append-to-body
            v-dialogDrag>
            <el-form :model="supOpt.form" :rules="supOpt.rules" label-width="140px" label-position="right">
                <el-row>
                    <el-col :span="24">
                        <el-form-item prop="supplierPlatForm" label="供应商平台：">
                            <el-select v-model="supOpt.form.supplierPlatForm">
                                <el-option v-for="item in supplierPlatFormList" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" :hidden="(supOpt.form.supplierPlatForm != 1)">
                        <el-form-item prop="supplierWxNum" label="微信账号：">
                            <!-- {{ supOpt.form.supplierWxNum}} -->
                            <el-input v-model.trim="supOpt.form.supplierWxNum" maxlength="30"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="supplierName" label="供应商名称：">
                            <!-- {{ supOpt.form.supplierName}} -->
                            <el-input v-model.trim="supOpt.form.supplierName" maxlength="30" style="width:100%;"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" :hidden="(supOpt.form.supplierPlatForm != 2)">
                        <el-form-item prop="supplierLink" label="供应商链接：" style="width:100%;">
                            <!-- {{ supOpt.form.supplierLink}} -->
                            <el-input v-model.trim="supOpt.form.supplierLink" maxlength="200"
                                style="width:100%;"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" :hidden="(supOpt.form.supplierPlatForm != 2)">
                        <el-form-item prop="supplierGoodLink" label="产品链接：" style="width:100%;">
                            <!-- {{ supOpt.form.supplierGoodLink}} -->
                            <el-input v-model.trim="supOpt.form.supplierGoodLink" maxlength="200"
                                style="width:100%;"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col  :span="24">
                        <el-form-item prop="hasProductionLicense" label="生产许可证：" style="float: left;" >
                            <el-select v-model="supOpt.form.hasProductionLicense" style="width:200px;">
                                        <el-option  label="无" :value="0"></el-option>
                                        <el-option  label="有" :value="1"></el-option>
                            </el-select> 
                            <div v-if="supOpt.form.hasProductionLicense==1" class="el-form-item__error" style="color:red; width: 200px; margin-top: 5px;">食品级产品需食品级检测报告和生产许可证，且需与供应商名称、地址一致</div>
                        </el-form-item>
                        <YhImgUpload2Table v-if="supOpt.form.hasProductionLicense==1" style="margin-left: 20px;float: left;" :value.sync="supOpt.form.productionLicenseUrl"></YhImgUpload2Table> 
                    </el-col>
                </el-row>
            </el-form>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="supOpt.visible = false">取 消</el-button>
                    <el-button type="primary" @click="onAddSupplier" v-if="!supOpt.isEdit">添 加</el-button>
                    <el-button type="primary" @click="onSaveEditSupplier" v-if="supOpt.isEdit">保 存</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="更换供应商" :visible.sync="changeSupOpt.visible" width="20%" :close-on-click-modal="false"
            append-to-body v-dialogDrag>
            <el-form :model="form" label-width="140px" label-position="right">
                <el-row>
                    <el-col :span="24">
                        <el-form-item prop="supplierPlatForm" label="供应商：">
                            <el-select v-model="changeSupOpt.supplierId">
                                <el-option v-for="item in form.supplierList" :key="item.id" :label="item.supplierName"
                                    :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="changeSupOpt.visible = false">取 消</el-button>
                    <el-button type="primary" @click="onChangeSupplier">确 定</el-button>
                </span>
            </template>
        </el-dialog>
    </my-container>
</template>
<script>
import uploadfile from '@/components/Comm/uploadfile.vue'; 
import formCreate from '@form-create/element-ui';
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import { formatmoney, formatPercen, platformlist, setStore, getStore, formatLinkProCode, sendWarehouse4HotGoodsBuildGoodsDocList } from "@/utils/tools";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import { saveStockInApply, GetSnowflakeId, getStockInApplyInfo } from '@/api/operatemanage/productalllink/alllink';
import { getAllWarehouse } from '@/api/inventory/warehouse'
import FcEditor from "@form-create/component-wangeditor";
import { SaveProductIdViewLog } from '@/api/operatemanage/PddChart'
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import YhImgUpload2Table from '@/components/upload/yh-img-upload2Table.vue';

//const process = process;
function rowChanged4EstimateStockInAmount(x) {
    //let x = row;
    let estimateStockInCount = isNaN(x.estimateStockInCount) ? 0 : x.estimateStockInCount;
    let costPrice = isNaN(x.estimateCostPrice) ? 0 : x.estimateCostPrice;
    x.estimateStockInAmount = (estimateStockInCount * costPrice);
}

const skuTableCols = [
    { istrue: true, prop: 'yhGoodsName', type: "inputtext", label: '商品名称', minwidth: '200', maxlength: 40, isRequired: true, isDisabled: (row) => true },
    { istrue: true, prop: 'estimateCostPrice', type: "inputnumber", precision: 3, label: '成本单价', width: '100', min: 0, max: 900000, isRequired: true, change: rowChanged4EstimateStockInAmount },// 
    { istrue: true, prop: 'forNewWarehouse', type: "select", label: '上新仓库', width: '100', isRequired: true, display: false, options: sendWarehouse4HotGoodsBuildGoodsDocList, isDisabled: (row) => true },
    { istrue: true, prop: 'goodsProgressType', type: "select", label: '商品类型', width: '90', isRequired: true, options: [{ label: '成品', value: '成品' }, { label: '半成品', value: '半成品' }], isDisabled: (row) => true },
    { istrue: true, prop: 'isMainSale', type: "select", label: '是否主卖', width: '80', isRequired: true, options: [{ label: '是', value: true }, { label: '否', value: false }], isDisabled: (row) => true },
    { istrue: true, prop: 'mainSaleAvgCount', type: "inputnumber", label: '主卖人均件数', width: '80', min: 0, max: 10000, isDisabled: (row) => true},
    { istrue: true, prop: 'estimateStockInCount', type: "inputnumber", label: '预计进货数量', width: '120', min: 0, max: 900000, isRequired: true, change: rowChanged4EstimateStockInAmount },
    { istrue: true, prop: 'estimateStockInAmount', type: "inputnumber", precision: 2, label: '预计进货金额', width: '120', min: 0, isRequired: true, max: 999999999 },
    { istrue: true, prop: 'isZengPin', type: "select", label: '有无赠品', width: '80', options: [{ label: '有', value: true }, { label: '无', value: false }], isDisabled: (row) => true },
    { istrue: true, prop: 'isJiaGong', type: "select", label: '是否加工', width: '80', options: [{ label: '是', value: true }, { label: '否', value: false }], isDisabled: (row) => true }
];
export default {
    name: "hotsalegoodsbuildgoodsdoc",
    components: { MyContainer, MyConfirmButton, cesTable, YhImgUpload, uploadfile, YhImgUpload2Table },
    data() {
        return {
            //: process,
            that: this,
            supplierPlatFormList: [{ value: 1, label: "微信" }, { value: 2, label: "1688" }],
            //fomSendWarehouse4HotGoodsBuildGoodsDocList: sendWarehouse4HotGoodsBuildGoodsDocList,
            fomSendWarehouse4HotGoodsBuildGoodsDocList: [],
            form: {
                sampleImgUrls: [],
                voucherImgUrls: []
            },
            goodsStatelist: [
                { label: '新品', value: 1 },
                { label: '老品补SKU', value: 2 },
                { label: '代拍', value: 3 },
            ],
            thisformRules: {
                goodsCompeteShortName: [{ required: true, message: '请输入产品简称', trigger: 'blur' }],
                forNewWarehouse: [{ required: true, message: '请输入上新仓库', trigger: 'blur' }],
                neeShootingTask: [{ required: true, message: '请选择是否拍摄', trigger: 'change' }],
                goodsState: [{ required: true, message: '请选择产品状态' }],
                delayedPurchase: [{ required: true, message: '请选择是否延时进货' }],
                outerPackaLanguage: [{ required: true, message: '请选择外包装语言' }],
                chatImgUrls: [{ required: true, message: '请上传供应商聊天记录' }],
            },
            rowTempIndex: -1,
            skuTableCols: skuTableCols,
            platformlist: platformlist,

            skuTableData: [],
            summaryarry: {
                'estimateStockInCount_sum': 0,
                'estimateStockInAmount_sum': 0
            },
            listLoading: false,
            pageLoading: false,
            goodschoiceVisible: false,//选择商品显隐
            curRow: null,
            curCol: "",
            formEditMode: true,//是否编辑模式

            selfInfo: {

            },
            curSupplierId: null,//当前选中的供应商
            supOpt: {
                visible: false,
                isEdit: false,
                form: {
                    supplierPlatForm: 2,
                    supplierWxNum: "",
                    supplierName: "",
                    supplierLink: "",
                    supplierGoodLink: "",
                    remark: "",
                    chatImgUrls: []
                },
                rules: {
                    supplierPlatForm: [{ required: true, message: '平台必填', trigger: 'change' }],
                    supplierName: [{ required: true, message: '请填写供应商', trigger: 'blur' }],
                    supplierLink: [{ required: true, message: '请填写供应商链接', trigger: 'blur' }],
                    supplierWxNum: [{ required: true, message: '请填写微信账号', trigger: 'blur' }],
                    supplierGoodLink: [{ required: true, message: '请填写产品链接', trigger: 'blur' }]
                }
            },
            changeSupOpt: {
                visible: false,
                selectIds: [],
                supplierId: null
            },
            dialogTitle:''
        };
    },
    async mounted() {
        formCreate.component('editor', FcEditor);
        await this.init();
        const userInfoName = "hotsalegoods_selfuserinfo";
        let selfInfo4Store = getStore(userInfoName);
        if (selfInfo4Store) {
            this.selfInfo = selfInfo4Store;
        }

    },
    computed: {

        calcCurSupplier: function () {
            if (this.form.supplierList && this.form.supplierList.length > 0) {
                let sp = this.form.supplierList.find(x => x.id == this.curSupplierId);
                if (sp) {
                    return sp;
                }

            }

            return {
                id: '',
                supplierName: '未知',
                bldDocId: "",
                createdTime: null,
                enabled: true,
                supplierGoodLink: null,
                supplierLink: null,
                supplierName: null,
                supplierPlatForm: 2,
                supplierWxNum: null,
            }
        },
        calcPatentQualificationImgUrl: function () {
            if (this.form.patentQualificationImgUrl) {
                let v = (process.env.NODE_ENV === 'development' ? 'http://192.168.90.12:8004/' : 'http://192.168.90.12:8001/') + this.form.patentQualificationImgUrl;

                return v;
            } else {
                return "";
            }

        },
        tableHeight: function () {
            let rowsCount = 1;
            if (this.calcSkuTableData && this.calcSkuTableData.length > 0) {
                rowsCount = this.calcSkuTableData.length;
            }
            let rowsHeight = (rowsCount + 2) * 50 + 80;
            return rowsHeight > 400 ? 400 : rowsHeight;
        },
        calcSkuTableData: function () {
            return this.skuTableData.filter(x => {
                if (x.id > -10000 && x.stockInSupplierId == this.curSupplierId) {
                    return true;
                }
                return false;
            });
        },
        calcSaveSkuTableData: function () {
            return this.skuTableData.filter(x => {
                if (x.id > -10000) {
                    return true;
                }
                return false;
            });
        },
        calcEstimateStockInCount: function () {
            let that = this;
            let estimateStockInCount_sum = 0;
            this.calcSaveSkuTableData.forEach((x) => {
                let estimateStockInCount = isNaN(x.estimateStockInCount) ? 0 : x.estimateStockInCount;
                estimateStockInCount_sum += estimateStockInCount
            });

            that.summaryarry.estimateStockInCount_sum = estimateStockInCount_sum.toFixed(0);
            return that.summaryarry.estimateStockInCount_sum;
        },
        calcEstimateStockInAmount: function () {
            let that = this;
            let estimateStockInAmount_sum = 0;
            this.calcSaveSkuTableData.forEach((x) => {
                let estimateStockInAmount = isNaN(x.estimateStockInAmount) ? 0 : x.estimateStockInAmount;
                estimateStockInAmount_sum += estimateStockInAmount;
            });

            that.summaryarry.estimateStockInAmount_sum = estimateStockInAmount_sum.toFixed(2);
            return that.summaryarry.estimateStockInAmount_sum;
        },
        calcSampleVolume: function () {
            let sampleVolume = ((isNaN(this.form.sampleLength) ? 0 : this.form.sampleLength) *
                (isNaN(this.form.sampleWidth) ? 0 : this.form.sampleWidth) *
                (isNaN(this.form.sampleHeigdth) ? 0 : this.form.sampleHeigdth)
            ).toFixed(2);
            this.form.sampleVolume = sampleVolume;
            return sampleVolume;
        }
    },
    methods: {
        handleClick(e,prop){
            if (!prop) return
            if (!e.target.parentNode.innerHTML.includes('复') && !e.target.parentNode.innerHTML.includes('查 ')) return
            let res = JSON.parse(JSON.stringify(prop));
            if (res.length > 6) {
                res = res.substring(0, 2) + '**' + res.substring(res.length - 2, res.length);
            }
            if (e.target.innerHTML == '复') {
                var _this = this;
                this.$copyText(prop).then(function (e) {
                    _this.$message({ message: "内容已复制到剪切板！", type: "success" });
                }, function (e) {
                    _this.$message({ message: "抱歉，复制失败！", type: "warning" });
                })
                this.sendLog(prop, '复制宝贝ID', 'ERP')
            } else if (e.target.innerHTML == '查 ') {
                if (e.target.parentNode.innerHTML.includes(res)) {
                    e.target.parentNode.innerHTML = e.target.parentNode.innerHTML.replace(res, prop)
                }
                this.sendLog(prop, '查看宝贝ID', 'ERP')
            } else {
                if (res == e.target.innerHTML || prop == e.target.innerHTML) {
                    this.sendLog(prop, '打开链接', 'ERP')
                }
            }
        },  
        async sendLog(proCode, action, source) {
            await SaveProductIdViewLog({ proCode, action, source })
        },
        async init() {
            var res3 = await getAllWarehouse();
            var warehouselist1 = res3.data;
            this.fomSendWarehouse4HotGoodsBuildGoodsDocList = warehouselist1;
        },
        formatLinkProCode: formatLinkProCode,
        formatTime: formatTime,
        async onOpenAddSupplier() {
            let getIdRlt = await GetSnowflakeId();
            if (getIdRlt && getIdRlt.success) {
                this.supOpt.form = {
                    id: getIdRlt.data,
                    supplierPlatForm: 2,
                    supplierWxNum: null,
                    supplierName: null,
                    supplierLink: null,
                    supplierGoodLink: null,
                    hasProductionLicense: 0
                };
                this.dialogTitle="添加供应商"
                this.supOpt.visible = true;
                this.supOpt.isEdit = false;
            }

        },
        async onOpenEditSupplier() {
            let sp = this.form.supplierList.find(x => x.id == this.curSupplierId);
            if (sp && sp.id) {
                this.supOpt.form = { ...sp };
                this.dialogTitle="修改供应商"
                this.supOpt.visible = true;
                this.supOpt.isEdit = true;
            }
            else {
                this.$alert("未选择可修改的供应商");
            }

        },
        onDelSupplier() {
            if (!this.form.supplierList || this.form.supplierList.length <= 1) {
                this.$alert("请至少保留一个供应商！");
                return false;
            }

            this.$confirm('删除供应商时将同步删除当前供应商下商品，是否确定?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let self = this;
                let idx = -1;
                for (let i = 0; i < self.form.supplierList.length; i++) {
                    if (self.form.supplierList[i].id == self.curSupplierId) {
                        idx = i;
                        break;
                    }
                }
                if (idx > -1) {
                    //删除供应商
                    self.form.supplierList.splice(idx, 1);
                    //删除商品
                    self.skuTableData.forEach(x => {
                        if (x.supplierId && x.stockInSupplierId == self.curSupplierId) {
                            x.id = -999999 + (self.rowTempIndex--);
                        }
                    })

                    this.curSupplierId = this.form.supplierList[0].id;
                }
            }).catch(() => {

            });
        },
        onSaveEditSupplier() {
            let sp = this.form.supplierList.find(x => x.id == this.curSupplierId);

            let supOpt = this.supOpt;
            let errMsg = "";
            if (!supOpt.form.supplierName) {
                errMsg += "请输入供应商名称;";
            } else {
                if (this.form.supplierList && this.form.supplierList.length > 0) {
                    let findSup = this.form.supplierList.find(x => x.supplierName == supOpt.form.supplierName && x.id != sp.id);
                    if (findSup && findSup.supplierName)
                        errMsg += "已存在同名供应商；";
                }
            }
            if (supOpt.form.supplierPlatForm == 1) {
                if (!supOpt.form.supplierWxNum) {
                    errMsg += "请输入微信账号;";
                }
            } else if (supOpt.form.supplierPlatForm == 2) {
                if (!supOpt.form.supplierLink) {
                    errMsg += "请输入供应商链接;";
                }
                if (!supOpt.form.supplierGoodLink) {
                    errMsg += "请输入产品链接;";
                }
            }
            if(supOpt.form.hasProductionLicense==1 && !supOpt.form.productionLicenseUrl){
                    errMsg+="请上传生产许可证;";
                }
            if (errMsg) {
                this.$alert(errMsg);
                return false;
            }

            sp.supplierPlatForm = supOpt.form.supplierPlatForm;
            sp.supplierWxNum = supOpt.form.supplierWxNum;
            sp.supplierName = supOpt.form.supplierName;
            sp.supplierLink = supOpt.form.supplierLink;
            sp.supplierGoodLink = supOpt.form.supplierGoodLink;
            sp.hasProductionLicense=supOpt.form.hasProductionLicense;
            sp.productionLicenseUrl=supOpt.form.productionLicenseUrl;
            supOpt.visible = false;
        },
        async onAddSupplier() {
            let supOpt = this.supOpt;
            let errMsg = "";
            if (!supOpt.form.supplierName) {
                errMsg += "请输入供应商名称;";
            } else {
                if (this.form.supplierList && this.form.supplierList.length > 0) {
                    let findSup = this.form.supplierList.find(x => x.supplierName == supOpt.form.supplierName);
                    if (findSup && findSup.supplierName)
                        errMsg += "已存在同名供应商；";
                }
            }
            if (supOpt.form.supplierPlatForm == 1) {
                if (!supOpt.form.supplierWxNum) {
                    errMsg += "请输入微信账号;";
                }
            } else if (supOpt.form.supplierPlatForm == 2) {
                if (!supOpt.form.supplierLink) {
                    errMsg += "请输入供应商链接;";
                }
                if (!supOpt.form.supplierGoodLink) {
                    errMsg += "请输入产品链接;";
                }
            }
            if(supOpt.form.hasProductionLicense==1 && !supOpt.form.productionLicenseUrl){
                    errMsg+="请上传生产许可证;";
            }
            if (errMsg) {
                this.$alert(errMsg);
                return false;
            }
            this.form.supplierList.push({ ...supOpt.form });
            this.curSupplierId = supOpt.form.id;

            //历史数据，可能存在没有供应商的，默认给这个供应商
            var nonSupplierGoods = this.skuTableData.filter(x => x.stockInSupplierId == undefined || x.stockInSupplierId == null || x.stockInSupplierId == 0);
            if (nonSupplierGoods && nonSupplierGoods.length > 0) {
                nonSupplierGoods.forEach(x => {
                    x.stockInSupplierId = supOpt.form.id;
                })
            }

            supOpt.visible = false;
        },
        // handleAvatarSuccess: function (response, file, fileList) {
        //     if (response && response.success && response.data.url) {
        //         this.form.patentQualificationImgUrl = response.data.relativePath;
        //     }
        // },
        rowChanged4EstimateStockInAmount: rowChanged4EstimateStockInAmount,
        async getSkuTableData(docId, formEditMode, applyId) {
            //this.form = { platform: null };
            let form = this.form;
            // 清空from对象

            Object.keys(form).forEach(key => {
                if (key == "sampleImgUrl" || key == "voucherImgUrl")
                    form[key] = "";
                else if (key == "sampleImgUrls" || key == "voucherImgUrls")
                    form[key] = [];
                else
                    form[key] = null;
            });
            this.form.sampleWeight = null;
            this.form.sampleLength = null;
            this.form.sampleWidth = null;
            this.form.sampleHeigdth = null;

            //处理编辑模式
            this.formEditMode = formEditMode;
            this.$refs.uploadChatImg.setData([]);
            this.listLoading = true;
            const res = await getStockInApplyInfo({ chooseId: docId, applyId: applyId });

            
            this.listLoading = false;

            let emptyImgs = {
                sampleImgUrl: "",
                sampleImgUrls: [],
                voucherImgUrl: "",
                voucherImgUrls: []
            };

            this.skuTableData = res?.data?.detailList;

            let tempData = { ...emptyImgs, ...res?.data?.mainForm };

            this.form = tempData;

            this.form.supplierList = res?.data?.supplierList;

            if (this.form.supplierList && this.form.supplierList.length > 0)
                this.curSupplierId = this.form.supplierList[0].id;
 
            this.$refs.uploadChatImg.setData(this.form.chatImgUrls == null ? [] : this.form.chatImgUrls);
        },
        async saveSkuTableData(isApply) {

            if (!(this.form.supplierList && this.form.supplierList.length > 0)) {
                this.$alert("请维护供应商信息！");
                return false;
            }

            //有供应商无商品的，不允许保存 1
            for (let i = 0; i < this.form.supplierList.length; i++) {
                var skuData = this.calcSaveSkuTableData.find(x => x.stockInSupplierId == this.form.supplierList[i].id);
                if (skuData == undefined || skuData == null) {
                    this.$alert("供应商【" + this.form.supplierList[i].supplierName + "】没有商品数据，如果不需要该供应商，请删除！");
                    return false;
                }
            }

            //this.listLoading = true;
            let listData = [...this.calcSaveSkuTableData];
            let dtoData = {
                ...this.form
            };
            dtoData.details = listData;
            dtoData.SaveAndAppply = isApply;
            var rlt =await saveStockInApply(dtoData); 
            return rlt.success;
        },
        getPreviewImgList(index, imgArr) {
            let arr = []
            let i = 0;
            for (i; i < imgArr.length; i++) {
                arr.push(imgArr[i + index])
                if (i + index >= imgArr.length - 1) {
                    index = 0 - (i + 1);
                }
            }
            return arr;
        },
        renderHeader(h, { column }) { // h即为cerateElement的简写 
            return h(
                'div',
                [
                    h('div', { domProps: { innerHTML: "<span style='color:red'>*</span><span>" + column.label + "</span>" } })
                ],
            );
        },
        onOpenChangeSupplier() {
            if (this.changeSupOpt.selectIds.length <= 0) {
                this.$alert('请选择要更换供应商的商品！');
                return;
            }
            this.changeSupOpt.visible = true;
        },
        selectchange(rows, row) {
            this.changeSupOpt.selectIds = [];
            rows.forEach(f => {
                this.changeSupOpt.selectIds.push(f.hotSaleGoodsBuildGoodsDtlId);
            })
        },
        onChangeSupplier() {
            if (this.changeSupOpt.supplierId == null) {
                this.$alert('请选择更换的供应商！');
                return;
            }
            this.changeSupOpt.selectIds.forEach(id => {
                this.skuTableData.forEach(sku => {
                    if (sku.hotSaleGoodsBuildGoodsDtlId == id) {
                        sku.stockInSupplierId = this.changeSupOpt.supplierId;
                    }
                });
            });
            this.changeSupOpt.supplierId = null;
            this.changeSupOpt.visible = false;
            this.changeSupOpt.selectIds = [];
        },
        clearnSel(){  
            this.changeSupOpt.selectIds = [];
        },
        delRow(row){
            this.$confirm('是否确定删除该商品?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                row.id = -999999 + (this.rowTempIndex--);
            }).catch(() => {

            });
           
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

.tempdiv ::v-deep img {
    width: auto;
    max-width: 1000px;
}
</style>
