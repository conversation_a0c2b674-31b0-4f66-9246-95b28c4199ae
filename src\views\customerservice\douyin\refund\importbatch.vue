<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter">
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :isSelection="true" :summaryarry="summaryarry" :tableData='pagelist' @select='selectchange'
            :tableCols='tableCols' :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.batchStr" placeholder="批次号" style="width:160px;" clearable
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                      <el-select v-model="filter.fileType " placeholder="类型" clearable style="width: 160px;" filterable>
                        <el-option label="仅退款商品数据" :value=1 />
                        <el-option label="小额打款" :value=2 />
                      </el-select>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onBatchDelete" style="margin-left: 20px;">批量删除</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getpageList" />
        </template>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import {
    GetImportDouYinRefundOrderBatchPageList, DeleteImportDouYinRefundOrderBatch
} from '@/api/customerservice/douyinrefund'

const tableCols = [
    { istrue: true, prop: 'fileType', label: '类型', width: '150',  formatter: (row) => row.fileType == 1 ? "仅退款商品数据" : row.fileType == 2 ? "小额打款" : ''},
    { istrue: true, prop: 'batchStr', label: '批次号', width: '150' },
    { istrue: true, prop: 'batchCount', label: '总条数', width: '150' },
    { istrue: true, prop: 'batchSuccessCount', label: '已导入条数', width: '150' },
    { istrue: true, prop: 'createdUserName', label: '操作人', width: '150' },
    { istrue: true, prop: 'createdTime', label: '操作时间', width: '150' },
];
export default {
    name: "onlyrefundgoods",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
    data() {
        return {
            that: this,
            pageLoading: false,
            filter: {
                batchStr: null,
                fileType: null,
            },
            tableCols: tableCols,
            listLoading: false,
            pagelist: [],
            total: 0,
            summaryarry: {},
            pager: { OrderBy: "BatchStr", IsAsc: false },
            sels: [], // 列表选中列
            selids: [],

            dialogVisibleRefund: false,
            uploadLoading: false,
            improtRefundForm: {},
        };
    },
    async mounted() {
        this.onSearch();
    },
    methods: {
        getParam() {
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };
            return params;
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getpageList();
        },
        async getpageList() {
            const params = this.getParam();
            this.listLoading = true;
            const res = await GetImportDouYinRefundOrderBatchPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.pagelist = res.data.list;
            //this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            this.sels = rows;
            rows.forEach(f => {
                this.selids.push(f.batchStr);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async onBatchDelete() {
            var that = this;
            let isCheck = false;
            this.sels.forEach(f => {
                if (f.batchCount != f.batchSuccessCount) {
                    isCheck = true;
                    return;
                }
            });
            if (isCheck) {
                that.$message({ message: '[总条数]不等于[已导入条数]请等待导入完成再删除，或联系管理员删除', type: "error" });
                return;
            }
            this.$confirm("确认要执行批量删除的操作吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let del = await DeleteImportDouYinRefundOrderBatch(this.selids);
                if (del?.success) {
                    that.$message({ message: '已删除', type: "success" });
                    that.onSearch();
                }
                else {
                    that.$message({ message: '发生异常，请刷新后重试', type: "error" });
                }
            });

        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
