<template>
  <container>
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item>
          <el-input v-model.trim="filter.styleCode" placeholder="系列编码" style="width:160px;" maxlength="50" clearable/>
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="filter.productName" placeholder="商品名称" style="width:160px;" maxlength="50" clearable/>
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="filter.shopName" placeholder="原、新店铺名称" style="width:160px;" maxlength="50" clearable/>
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="filter.productCode" placeholder="原、新产品ID" style="width:160px;" maxlength="50" clearable/>
        </el-form-item>
        <el-form-item label="操作时间">
          <el-date-picker style="width: 230px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="铺货时间">
          <el-date-picker style="width: 230px" v-model="filter.timerange2" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束">
          </el-date-picker>
        </el-form-item>
        <el-select filterable v-model="filter.status" placeholder="状态" clearable style="width: 160px">
          <el-option label="铺货中" value="铺货中" />
          <el-option label="设置折扣" value="设置折扣" />
          <el-option label="设置优惠券" value="设置优惠券" />
          <el-option label="成功" value="成功" />
          <el-option label="失败" value="失败" />
        </el-select>
        <el-form-item style="margin-left: 10px;">
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <p></p>
        <el-checkbox-group v-model="filter.checkList" @change="changeCheck" style="float:left; ">
            <el-checkbox :label="1">按备注划分</el-checkbox>
        </el-checkbox-group>
      </el-form>
    </template>
    <ces-table :tablekey="'productReportPddOnlineList20230715'" ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
     :tableData='list' :tableCols='tableCols' :isSelection="false" :tableHandles='tableHandles' :loading="listLoading" :tablefixed='true'
      :isSelectColumn="false" :summaryarry="summaryarry" :hasexpandRight='true'   style="height: 99%;">
      <template v-if="filter.checkList.length == 0" slot="right">
        <el-table-column width="100" label="操作" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" @click="showDetail(scope.row)">查看SKU明细</el-button>
          </template>
        </el-table-column>
      </template>
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <vxe-modal title="SKU明细" v-model="sku.visible" :width="1300"  marginSize='-500'>
      <template #default>
            <my-container>
                <div style="height: 650px;">
                    <cesTable ref="skuDetailTable" :that='that' :isIndex='false' :hasexpand='true'   @sortchange='skuSortchange'
                     :tableData='skuData' :tableCols='skuTableCols' :isSelectColumn="false" :isSelection="false">
                    </cesTable>
                </div>
            </my-container>
            </template>
        </vxe-modal>
  </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { getDistributionGoodsOnlineRecordAsync,distributionGoodsOnlineAgainAsync,getDistributionGoodsOnlineSkuRecordAsync,getDistributionGoodsOnlineRecordGroupAsync } from "@/api/operatemanage/distributiongoodsOnline.js"
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { formatLinkProCode } from "@/utils/tools";

const tableCols = [
  { istrue: true, prop: 'styleCode', label: '系列编码', tipmesg: '', width: '100', sortable: 'custom', },
  { istrue: true, prop: 'title', label: '商品名称', tipmesg: '', sortable: 'custom', formatter: (row) => row.productName },
  { istrue: true, prop: 'b.groupId', label: '商品运营组', tipmesg: '', width: '80', sortable: 'custom', formatter: (row) => row.oldGroupName ,type:'custom'},
  { istrue: true, prop: 'packCost', label: '快递包装费', tipmesg: '', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'profitRate', label: '利润率%', tipmesg: '', width: '80', sortable: 'custom',formatter: (row) => row.profitRate+'%' },
  { istrue: true, prop: 'freight', label: '运费模板', tipmesg: '', width: '80', sortable: 'custom', },
  { istrue: true, prop: 'couponAmount', label: '优惠券', tipmesg: '', width: '80', sortable: 'custom', },
  { istrue: true, prop: 'discountAmount', label: '折扣', tipmesg: '', width: '80', sortable: 'custom',formatter: (row) => row.discountAmount+'%'  },
  { istrue: true, prop: 'invest', label: '建议投产', tipmesg: '', width: '80', sortable: 'custom'  },
  { istrue: true, prop: 'dayLimitAmount', label: '日限额', tipmesg: '', width: '80', sortable: 'custom'  },
  { istrue: true, prop: 'star', label: '星星', width: '60', type: 'newstar' },
  { istrue: true, prop: 'flag', label: '旗帜', width: '60', type: 'flag' },
  { istrue: true, prop: 'c.ShopCode', label: '原店铺名称', tipmesg: '', width: '130', sortable: 'custom', formatter: (row) => row.oldShopName },
  { istrue: true, prop: 'oldProductCode', label: '原商品ID', type:'html', tipmesg: '', width: '120', sortable: 'custom',formatter: (row) => formatLinkProCode(2, row.oldProductCode) },
  { istrue: true, prop: 'newShopCode', label: '新店铺名称', tipmesg: '', width: '130', sortable: 'custom', formatter: (row) => row.newShopName },
  { istrue: true, prop: 'newProductCode', label: '新商品ID', type:'html', tipmesg: '', width: '120', sortable: 'custom', formatter: (row) => formatLinkProCode(2, row.newProductCode) },
  { istrue: true, label: '小组头像',  width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
  { istrue: true, prop: 'groupId', exportField: 'groupName', label: '运营组', sortable: 'custom', width: '90', formatter: (row) => row.groupName,type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, },
  { istrue: true, label: '专员头像', width: '70',type:'ddAvatar',ddInfo:{type:2,prop:'operateSpecialUserId'} },
  { istrue: true, prop: 'operateSpecialUserId', exportField: 'operateSpecialUserName', label: '运营专员', sortable: 'custom', width: '90', formatter: (row) => row.operateSpecialUserName,type:'ddTalk',ddInfo:{type:2,prop:'operateSpecialUserId',name:'operateSpecialUserName'}, },
  { istrue: true, prop: 'userId', label: '运营助理', tipmesg: '', width: '80', sortable: 'custom', formatter: (row) => row.userName,type:'custom' },
  { istrue: true, prop: 'userId3', label: '备用', tipmesg: '', width: '80', sortable: 'custom', formatter: (row) => row.user3Name ,type:'custom'},
  { istrue: true, prop: 'a.createdTime', label: '操作时间', tipmesg: '', width: '160', sortable: 'custom', formatter: (row) => formatTime(row.createdTime, "YYYY-MM-DD HH:mm:ss") },
  { istrue: true, prop: 'a.createdUserName', label: '操作人', tipmesg: '', width: '80', sortable: 'custom', formatter: (row) => row.createdUserName},
  { istrue: true, prop: 'a.status', label: '状态', tipmesg: '', width: '80', sortable: 'custom', formatter: (row) => row.status },
  { istrue: true, prop: 'distributionTime', label: '铺货时间', tipmesg: '', width: '160', sortable: 'custom', formatter: (row) => row.distributionTime == null ? "" : formatTime(row.distributionTime, "YYYY-MM-DD HH:mm:ss") },
  { istrue: true, prop: 'a.remark', label: '备注', tipmesg: '', width: '160', sortable: 'custom', formatter: (row) => row.remark },
  // { istrue: true, type: 'button', width: '55', label: '操作', tipmesg: '', btnList: [{ label: "同步", display: (row) => { return row.status != '失败'; }, handle: (that, row) => that.DistributionGoods(row) }] },
]

const tableHandles = [
  //{ label: "导入", handle: (that) => that.startImport() }
];

const skuTableCols = [
    { istrue: true, sortable: 'custom', prop: 'shopGoodsCode', label: 'SKU ID', width: '120', },
    { istrue: true,  prop: 'goodsImage', label: '图片', width: '80', type: "images", },
    { istrue: true, sortable: 'custom', prop: 'goodsCode', label: '商品编码', width: '100', },
    //{ istrue: true, prop: 'goodsName', label: '商品名称', width: '180', },
    { istrue: true, sortable: 'custom', prop: 'norms', label: '款式' },
    { istrue: true, sortable: 'custom', prop: 'size', label: '型号' },
    { istrue: true, sortable: 'custom', prop: 'unitPrice', label: '拼单价', width: '100',},
    { istrue: true, sortable: 'custom', prop: 'oldPrice', label: '到手价', width: '100',  }
]

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
  name: 'ProductReportPddOnlineList',
  components: { container, cesTable, MyConfirmButton },
  data() {
    return {
      that: this,
      filter: {
        styleCode: null,
        productName: null,
        shopName: null,
        productCode: null,
        timerange: [startDate, endDate],
        startTime: null,
        endTime: null,
        timerange2: [],
        startTime2: null,
        endTime2: null,
        status: null,
        checkList:[]
      },
      list: [],
      skuData:[],
      skuTableCols:skuTableCols,
      pager: { OrderBy: "createdTime", IsAsc: false },
      skupager: { OrderBy: "goodsCode", IsAsc: false },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      pickOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      sels: [],
      uploadLoading: false,
      dialogVisible: false,
      listLoading: false,
      fileList: [],
      summaryarry: {},
      sku:{
        filter:{
          batchId:null,
          productCode:null,
          shopCode:null
        },
        visible:false
      }
    };
  },

  async mounted() {
    await this.onSearch()
    await this.loadData()
  },

  methods: {
    //获取店铺
    async loadData() {

    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    async getlist() {
      if (this.pager.OrderBy == null) {
        this.pager.OrderBy = "createdTime";
        this.pager.IsAsc = false;
      }
      let pager = this.$refs.pager.getPager();
      let page = this.pager;
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      this.filter.startTime2 = null;
      this.filter.endTime2 = null;
      if (this.filter.timerange2) {
        this.filter.startTime2 = this.filter.timerange2[0];
        this.filter.endTime2 = this.filter.timerange2[1];
      }
      const params = { ...pager, ...page, ... this.filter }
      if (params === false) {
        return;
      }
      this.listLoading = true
      if(this.filter.checkList.length==0){
        const res = await getDistributionGoodsOnlineRecordAsync(params)
        this.listLoading = false
        if (!res?.success) {
          return
        }
        this.total = res.data.total;
        const data = res.data.list;
        this.list = data
        this.summaryarry = res.data.summary;
      }else{
        const res = await getDistributionGoodsOnlineRecordGroupAsync(params)
        this.listLoading = false
        if (!res?.success) {
          return
        }
        this.total = res.data.total;
        const data = res.data.list;
        this.list = data
        this.summaryarry = res.data.summary;
      }

    },
    async nSearch() {
      await this.getlist()
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    },
    async DistributionGoods(row) {
      let param = { Id: row.id };
      let res = await distributionGoodsOnlineAgainAsync(param);
      if (!res?.data) {
        this.$message({ message: '发起同步失败！', type: "error" });
      } else {
        this.$message({ message: '发起同步成功，请稍后查看同步结果....', type: "success" });
        await this.getlist();
      }
    },
    async loadSku() {
      let skuPager= this.skupager;
      let params = { ...this.sku.filter, ...skuPager }
      const res = await getDistributionGoodsOnlineSkuRecordAsync(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.skuData = res.data.list;
    },
    async showDetail(row) {
      this.skuData = [];
      this.sku.filter.batchId = row.batchId;
      this.sku.filter.productCode = row.oldProductCode;
      this.sku.filter.shopCode = row.newShopCode;
      await this.loadSku();
      this.sku.visible=true;
    },
    async skuSortchange(column) {
      if (!column.order)
        this.skupager = {};
      else {
        this.skupager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.loadSku();
    },
    async changeCheck() {
      if (this.filter.checkList.length > 0) {
        let tableColsGroups = [
          { istrue: true, prop: 'qty', label: '数量', width: '200', sortable: 'custom' }
        ];
        if (this.filter.checkList.includes(1)) {
          tableColsGroups.unshift({ istrue: true, prop: 'a.remark', sortable: 'custom', label: '备注', formatter: (row) => row.remark });
        }
        this.tableCols = tableColsGroups;
      } else {
        this.tableCols = tableCols;
      }
      this.pager = {
        OrderBy: null,
        IsAsc: false,
      };
      this.$refs.table.clearSort();
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
  }
};
</script>

<style lang="scss" scoped></style>
