<template>
    <MyContainer v-loading="loading" style="position: relative;">
        <template #header>
            <div class="top">
                <el-input v-model.trim="ListInfo.userName" placeholder="姓名" maxlength="50" clearable
                    style="width: 100px;" class="publicCss" v-show="ListInfo.groupType != 4" />
                <el-input v-model.trim="ListInfo.leaderName" placeholder="上级" maxlength="50" clearable
                    style="width: 100px;" class="publicCss" v-show="ListInfo.groupType != 4" />
                <number-range class="publicCss" :min.sync="ListInfo.minAllStyleCount" style="width: 180px;"
                    :max.sync="ListInfo.maxAllStyleCount" min-label="经营款式数最小" max-label="经营款式数最大" />
                <number-range class="publicCss" :min.sync="ListInfo.minZjhbl" :max.sync="ListInfo.maxZjhbl"
                    min-label="资金回报率最小" max-label="资金回报率最大" style="width: 180px;" />
                <number-range class="publicCss" :min.sync="ListInfo.minProfit33" :max.sync="ListInfo.maxProfit33"
                    min-label="毛4最小" max-label="毛4最大" style="width: 150px;" />
                <number-range class="publicCss" :min.sync="ListInfo.minZjzy" :max.sync="ListInfo.maxZjzy"
                    min-label="资金占用最小" max-label="资金占用最大" style="width: 170px;" />
                <number-range class="publicCss" :min.sync="ListInfo.minHireDays" :max.sync="ListInfo.maxHireDays"
                    min-label="在职天数最小" max-label="在职天数最大" style="width: 170px;" />
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.styleCodes" v-model="ListInfo.proCode"
                    placeholder="系列编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="styleCodesCallback" title="系列编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <number-range class="publicCss" :min.sync="ListInfo.minAdvRate" :max.sync="ListInfo.maxAdvRate"
                    min-label="付费占比最小" max-label="付费占比最大" style="width: 160px;" />
                <!-- <el-tooltip class="item" effect="dark"
                    :content="'资金回报率大于' + (computedColor.zjhbl ? computedColor.zjhbl : '') + '并且毛4大于' + (computedColor.profit33 ? computedColor.profit33 : '') + '的资金回报率的颜色为红色'"
                    placement="top-start">
                    <number-range class="publicCss" :min.sync="computedColor.zjhbl" :max.sync="computedColor.profit33"
                        min-label="资金回报率" max-label="毛4" style="width: 150px;" />
                </el-tooltip>   -->
            </div>
            <div class="top">
                <el-select v-model="ListInfo.platform" placeholder="平台" class="publicCss" clearable
                    style="width: 100px;">
                    <el-option v-for="item in platformlist" :label="item.label" :value="item.value" :key="item.value" />
                </el-select>
                <el-select filterable v-model="ListInfo.groupId" class="publicCss" collapse-tags clearable
                    placeholder="运营组" style="width: 100px">
                    <el-option v-for="item in grouplist" :key="'groupId' + item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-select filterable v-model="ListInfo.groupSuperviseId" class="publicCss" collapse-tags clearable
                    placeholder="主管" style="width: 90px">
                    <el-option v-for="item in grouplist" :key="'groupSuperviseId' + item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.company" placeholder="地区" class="publicCss" clearable style="width: 80px;">
                    <el-option label="义乌" value="义乌" />
                    <el-option label="南昌" value="南昌" />
                    <el-option label="武汉" value="武汉" />
                    <el-option label="西安" value="西安" />
                    <el-option label="深圳" value="深圳" />
                </el-select>
                <el-select v-model="ListInfo.userType" placeholder="人员类型" class="publicCss" clearable
                    style="width: 80px;">
                    <el-option label="免费" value="免费" />
                    <el-option label="付费" value="付费" />
                    <el-option label="产品" value="产品" />
                    <el-option label="品类" value="品类" />
                    <el-option label="行政" value="行政" />
                </el-select>
                <el-select v-model="ListInfo.enabled" placeholder="是否离职" class="publicCss" clearable
                    style="width: 80px;">
                    <el-option label="在职" :value="true" />
                    <el-option label="离职" :value="false" />
                </el-select>

                <el-select v-model="ListInfo.groupType" placeholder="汇总方式" class="publicCss" style="width: 100px;">
                    <el-option label="按助理汇总" :value="1" />
                    <el-option label="按专员汇总" :value="2" />
                    <el-option label="按带教汇总" :value="21" />
                    <el-option label="按组汇总" :value="3" />
                    <el-option v-if="checkPermission('ProfileInformation.PlatformGroup')" label="按平台汇总" :value="4" />
                </el-select>
                <el-select v-model="ListInfo.versionId" style="width: 150px" placeholder="拍照版本" :clearable="false"
                    :collapse-tags="true" filterable>
                    <el-option v-for="item in versionList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="openPhotographDialog">拍照</el-button>
                </div>
                <div style="margin:0 10px;">
                    <el-switch v-model="isCard" active-text="展示卡片" inactive-text="展示表格" style="margin-right: 10px;" />
                    <el-switch v-model="showMore" @change="changeShowMore" active-text="显示查看更多" inactive-text="隐藏查看更多"
                        v-if="isCard" />
                </div>
                <div class="computedTime">统计时间: {{ extData.rptStartDate + ' - ' + extData.rptEndDate }}</div>
            </div>
        </template>
        <div class="orderByContet" v-show="isCard">
            <div :class="['orderByItem', active.orderBy == item.orderBy ? 'active' : '']" v-for="item in orderByList"
                :key="item.orderBy">
                <div @click="changeSort(item)" style="cursor: pointer;">{{ item.label }}</div>
                <div style="display: flex;flex-direction: column;align-items: center;justify-content: center;">
                    <div :class="['ascendingOrder', (active.isAsc == true && active.orderBy == item.orderBy) ? 'ascendingOrder1' : '']"
                        @click="sortchange(item, true)"></div>
                    <div :class="['Descending', (active.isAsc == false && active.orderBy == item.orderBy) ? 'Descending1' : '']"
                        @click="sortchange(item, false)"></div>
                </div>
            </div>
        </div>
        <div class="mainContent" v-show="showPersonCard" ref="mainContent">
            <div class="informationBox" v-for="item in tableData" :key="item.id" :style="{ height: autoHeight }">
                <div class="avatar">
                    <div class="imgBox">
                        <el-image :src="item.avatarUrl" :preview-src-list="[item.avatarUrl]" style="border-radius:5px;"
                            class="imgCss" v-show="isPlatForm" />
                        <el-image :src="formatPlatFormLogo(item.platform).logo"
                            :preview-src-list="[formatPlatFormLogo(item.platform).logo]" style="border-radius:50%;"
                            class="imgCss" v-show="!isPlatForm" />
                    </div>
                    <div class="personalInfo">
                        <div class="name" v-show="isPlatForm">
                            <div class="name_left">
                                <div class="username">{{ item.userName }}</div>
                                <el-image :src="ddLogo" style="border-radius:5px;margin-left: 5px;"
                                    v-show="item.enabled === true" class="ddTalk" @click="startSession(item.userId)" />
                                <div class="post" v-if="item.title">{{ item.title }}</div>
                                <div class="incumbency"
                                    :style="{ backgroundColor: item.enabled == true ? '#EF9020' : '#FF4E2E' }">
                                    {{
                                        item.enabled === true ? '在职' : item.enabled === false ? '离职' : ''
                                    }}
                                </div>
                            </div>
                        </div>
                        <div class="name" v-show="!isPlatForm">
                            <div class="name_left">
                                <div class="username">{{ formatPlat(item.platform) }}</div>
                            </div>
                        </div>
                        <div class="personOther" v-show="isPlatForm">
                            <div class="personOther_item" style="width: 120px;">上级：{{ (item.leader1 ? item.leader1 +
                                '、' :
                                '') + (item.leader2 ? item.leader2 : '') }}
                            </div>
                            <div class="personOther_item" style="width: 105px;">在职时长：{{ item.hireDays ?
                                item.hireDays +
                                '天' : ''
                            }}</div>
                            <div class="personOther_item" style="width: 80px;">平台：{{ formatPlat(item.platform) }}
                            </div>
                            <el-tooltip class="item" effect="dark" :content="item.jiGuan" placement="top-start">
                                <div class="personOtherJG">籍贯：{{ item.jiGuan }}</div>
                            </el-tooltip>
                        </div>
                    </div>
                </div>
                <div class="personInformation">
                    <div class="personInformation_3">
                        <div class="personInformation_3_item">
                            <div class="personInformation_title">经营ID数：</div>
                            <div class="content">{{ item.proCodeCount }}</div>
                        </div>
                        <div class="personInformation_3_item">
                            <el-tooltip class="item" effect="dark" content="当前时间系列编码售卖数" placement="top-start">
                                <div class="personInformation_title">经营款式数：</div>
                            </el-tooltip>
                            <div class="content">{{ item.allStyleCount }}</div>
                        </div>
                        <div class="personInformation_3_item">
                            <el-tooltip class="item" effect="dark" content="近30天的售卖成本+库存资金+采购在途占用。"
                                placement="top-start">
                                <div class="personInformation_title">资金占用：</div>
                            </el-tooltip>
                            <div class="content">{{ computedNum(item.zjzySum) + `(${item.zjzySumPerc +
                                '%'})` }}</div>
                        </div>
                    </div>
                    <div class="personInformation_3">
                        <div class="personInformation_3_item">
                            <div class="personInformation_title">毛4利润：</div>
                            <div class="content">{{ computedNum(item.profit33) + `(${item.profit33Perc +
                                '%'})` }}</div>
                        </div>
                        <div class="personInformation_3_item">
                            <div class="personInformation_title">毛5利润：</div>
                            <div class="content">{{ computedNum(item.profit5)+ `(${item.profit5Perc +
                                '%'})`  }}</div>
                        </div>
                        <div class="personInformation_3_item">
                            <div class="personInformation_title">毛6利润：</div>
                            <div class="content">{{ computedNum(item.profit6) + `(${item.profit6Perc +
                                '%'})` }}</div>
                        </div>
                    </div>
                    <div class="personInformation_3">
                        <div class="personInformation_3_item">
                            <el-tooltip class="item" effect="dark" content="毛6/[资金占用]*12" placement="top-start">
                                <div class="personInformation_title">资金回报率：</div>
                            </el-tooltip>
                            <div class="content" :style="{ color: computedZJHBLColor(item.zjhbl) }">{{
                                item.zjhbl !== null ?
                                    item.zjhbl +
                                    '%' : '0%' }}</div>
                        </div>
                        <div class="personInformation_3_item">
                            <el-tooltip class="item" effect="dark" content="广告费/销售金额" placement="top-start">
                                <div class="personInformation_title">付费占比：</div>
                            </el-tooltip>
                            <div class="content">{{ item.advRate !== null ? item.advRate + '%' : '' }}</div>
                        </div>
                        <div class="personInformation_3_item">
                        </div>
                    </div>
                    <div class="personInformation_3">
                        <div class="personInformation_3_item">
                            <el-tooltip class="item" effect="dark" content="自己经营的所有款式里，按自己的资金占用靠前的款式。"
                                placement="top-start">
                                <div class="personInformation_title">主营款式：</div>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" :content="item.zyStyleCodes" placement="top-start">
                                <div class="longContent content">{{ item.zyStyleCodes }}</div>
                            </el-tooltip>
                        </div>
                    </div>
                    <div class="personInformation_3">
                        <div class="personInformation_3_item">
                            <el-tooltip class="item" effect="dark" content="自己经营的所有款式里，按自已的毛6排名靠前的款式。"
                                placement="top-start">
                                <div class="personInformation_title">擅长款式1：</div>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" :content="item.scStyleCodes" placement="top-start">
                                <div class="longContent content">{{ item.scStyleCodes }}</div>
                            </el-tooltip>
                        </div>
                    </div>
                    <div class="personInformation_3">
                        <div class="personInformation_3_item">
                            <el-tooltip class="item" effect="dark" content="公司所有数据，按款式排名，自己能排到靠前的款式。如：X系[n]，n表示排名。"
                                placement="top-start">
                                <div class="personInformation_title">擅长款式2：</div>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" :content="item.pmscStyleCodes" placement="top-start">
                                <div class="longContent content">{{ item.pmscStyleCodes }}</div>
                            </el-tooltip>
                        </div>
                    </div>

                </div>
                <div class="mainCategories" v-if="showMore && isCard">
                    <div class="mainCategories_top">
                        <div class="mainCategories_title">主营类目</div>
                        <el-button type="text" @click="learnMore(item, 'main')">查看更多</el-button>
                    </div>
                    <el-table :data="item.zyList" style="width: 100%" border>
                        <el-table-column prop="bzCategory" label="经营类目" width="65" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="bzCategory1" label="一级类目" width="65" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="bzCategory2" label="二级类目" width="65" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="orderCount" label="订单量" sortable width="64" show-overflow-tooltip>
                            <template #default="{ row }">
                                {{ computedNum(row.orderCount) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="zjzy" label="占用资金" sortable width="80" show-overflow-tooltip>
                            <template #default="{ row }">
                                <div>
                                    {{ computedNum(row.zjzy) }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="profit33" label="毛利润" sortable width="80" show-overflow-tooltip>
                            <template #default="{ row }">
                                <div>
                                    <span style="font-size: 12px;">毛4:{{ computedNum(row.profit33) }}</span>
                                </div>
                                <div>
                                    <span style="font-size: 12px;">毛5: {{ computedNum(row.profit5) }}</span>
                                </div>
                                <div>
                                    <span style="font-size: 12px;">毛6: {{ computedNum(row.profit6) }}</span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="zjhbl" label="回报率" sortable width="64" show-overflow-tooltip>
                            <template #default="{ row }">
                                <div>
                                    {{ row.zjhbl ? row.zjhbl + '%' : '' }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="bzProfit33Index" label="排名" width="52" show-overflow-tooltip />
                    </el-table>
                </div>
                <div class="mainCategories" v-if="showMore && isCard">
                    <div class="mainCategories_top">
                        <div class="mainCategories_title">擅长类目</div>
                        <el-button type="text" @click="learnMore(item, 'excel')">查看更多</el-button>
                    </div>
                    <el-table :data="item.scList" style="width: 100%" border>
                        <el-table-column prop="bzCategory" label="经营类目" width="65" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="bzCategory1" label="一级类目" width="65" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="bzCategory2" label="二级类目" width="65" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="orderCount" label="订单量" sortable width="64" show-overflow-tooltip>
                            <template #default="{ row }">
                                {{ computedNum(row.orderCount) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="zjzy" label="占用资金" sortable width="80" show-overflow-tooltip>
                            <template #default="{ row }">
                                <div>
                                    {{ computedNum(row.zjzy) }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="profit33" label="毛利润" sortable width="80" show-overflow-tooltip>
                            <template #default="{ row }">
                                <div>
                                    <span style="font-size: 12px;">毛4:{{ computedNum(row.profit33) }}</span>
                                </div>
                                <div>
                                    <span style="font-size: 12px;">毛5: {{ computedNum(row.profit5) }}</span>
                                </div>
                                <div>
                                    <span style="font-size: 12px;">毛6: {{ computedNum(row.profit6) }}</span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="zjhbl" label="回报率" sortable width="64" show-overflow-tooltip>
                            <template #default="{ row }">
                                <div>
                                    {{ row.zjhbl ? row.zjhbl + '%' : '' }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="bzProfit33Index" label="排名" sortable width="52" show-overflow-tooltip />
                    </el-table>
                </div>
            </div>
        </div>
        <div class="mainContent1" v-show="(!tableData || tableData.length == 0) && isCard">暂无数据</div>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            v-show="showPersonTable" @sortchange='sortchange1' :tableData='tableData' :tableCols='tableCols2'
            :isSelection="false" border :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
            <template slot="right">
                <vxe-column title="主营类目" width="70" align="center">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="learnMore(row, 'main')" v-throttle="1000">查看更多</el-button>
                        </div>
                    </template>
                </vxe-column>
                <vxe-column title="擅长类目" width="70" align="center">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="learnMore(row, 'excel')" v-throttle="1000">查看更多</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
            <template #zjhbl="{ row }">
                <span :style="{ color: computedZJHBLColor(row.zjhbl) }">{{ row.zjhbl !== null ?
                    row.zjhbl + '%' : '' }}</span>
            </template>
            <template #avatarUrl="{ row }">
                <el-image :src="getImgUrl(row)" :preview-src-list="[getImgUrl(row)]" style="border-radius:50%;"
                    class="tableImgCss" />
            </template>
            <template #enabled="{ row }">
                <span>{{ formatEnabled(row) }}</span>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" :pageSize="30"
                :sizes="[30, 60, 120, 240, 480]" />
        </template>

        <vxe-modal v-model="dialogVisible" :width="1200" marginSize='-500' :title="title">
            <profileInformationDtl :item="item" :type="type" :groupType="groupType" :platform="platform"
                :versionId="ListInfo.versionId" v-if="dialogVisible" />
        </vxe-modal>

        <el-dialog title="拍照" :visible.sync="photographVisible" width="15%" v-dialogDrag destroy-on-close>
            <dateRange :startDate.sync="photographTime.startDate" :endDate.sync="photographTime.endDate"
                v-if="photographVisible" />
            <div class="btnGroup">
                <el-button @click="photographVisible = false">取消</el-button>
                <el-button type="primary" @click="photograph" v-throttle="1000">确定</el-button>
            </div>
        </el-dialog>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import cestable from "@/components/Table/table.vue";
import { pickerOptions, platformlist, formatPlatform } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { pageStyleCodeDirectorFullInfoWarList, saveSimilarityVersion, getSeriesMainVersion } from '@/api/bookkeeper/styleCodeRptData'
import buschar from '@/components/Bus/buschar'
import decimal from '@/utils/decimal.js'
import profileInformationDtl from './profileInformationDtl.vue'
import inputYunhan from "@/components/Comm/inputYunhan";
import { getUserDingCode } from '@/api/admin/user'
import middlevue from "@/store/middle.js"
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
const computedNum = (val) => {
    return val !== null ? decimal(val, 10000, 1, '/') + '万' : ''
}
const formatDate = (item) => {
    return dayjs().diff(dayjs(item.hireDate), 'day') + '天'
}
const orderByList = [
    { isAsc: false, label: '姓名', orderBy: 'userName', },
    { isAsc: false, label: '所属平台', orderBy: 'platform', },
    { isAsc: false, label: '在职时长', orderBy: 'hireDays', },
    { isAsc: false, label: '毛四利润', orderBy: 'profit33', },
    { isAsc: false, label: '经营款式数', orderBy: 'allStyleCount', },
    { isAsc: false, label: '籍贯', orderBy: 'jiGuan', },
    { isAsc: false, label: '上级1', orderBy: 'leader1', },
    { isAsc: false, label: '上级2', orderBy: 'leader2', },
    { isAsc: false, label: '经营ID数', orderBy: 'proCodeCount', },
    { isAsc: false, label: '资金占用', orderBy: 'zjzySum', },
    { isAsc: false, label: '付费占比', orderBy: 'advRate', },
    { isAsc: false, label: '是否离职', orderBy: 'enabled', },
    { isAsc: false, label: '资金回报率', orderBy: 'zjhbl', },
]
const tableCols = [
    { istrue: true, sortable: 'custom', width: '48', align: 'center', prop: 'bzCategory', label: '经营类目', },
    { istrue: true, sortable: 'custom', width: '48', align: 'center', prop: 'bzCategory1', label: '一级类目', },
    { istrue: true, sortable: 'custom', width: '48', align: 'center', prop: 'bzCategory2', label: '二级类目', },
    { istrue: true, sortable: 'custom', width: '48', align: 'center', prop: 'orderCount', label: '订单量', formatter: (row) => row.orderCount !== null ? computedNum(row.orderCount) : '' },
    { istrue: true, sortable: 'custom', width: '48', align: 'center', prop: 'zjzy', label: '占用资金', formatter: (row) => row.zjzy !== null ? computedNum(row.zjzy) : '' },
    { istrue: true, sortable: 'custom', width: '48', align: 'center', prop: 'profit33', label: '毛四利润', formatter: (row) => row.profit33 != null ? computedNum(row.profit33) : '' },
    { istrue: true, sortable: 'custom', width: '48', align: 'center', prop: 'zjhbl', label: '回报率', formatter: (row) => row.zjhbl ? row.zjhbl + '%' : '' },
    { istrue: true, sortable: 'custom', width: '48', align: 'center', prop: 'zyzjIndex', label: '排名', },
]

const tableCols2 = [
    { width: '50', align: 'center', prop: 'avatarUrl', label: '头像', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'userName', label: '姓名', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'platform', label: '所属平台', formatter: (row) => formatPlatform(row.platform) },
    { sortable: 'custom', width: '80', align: 'center', prop: 'hireDays', label: '在职时长', formatter: (row) => row.hireDays !== null ? row.hireDays + '天' : '' },
    { sortable: 'custom', width: '80', align: 'center', prop: 'profit33', label: '毛四利润', formatter: (row) => row.profit33 !== null ? computedNum(row.profit33) : '' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'profit5', label: '毛五利润',  formatter: (row) => row.profit5 !== null ? computedNum(row.profit5) : '' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'profit6', label: '毛六利润',  formatter: (row) => row.profit6 !== null ? computedNum(row.profit6) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'allStyleCount', label: '经营款式数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'zyStyleCodes', label: '主营款式', tipmesg: '自己经营的所有款式里，按自己的资金占用靠前的款式。' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'scStyleCodes', label: '擅长款式1', tipmesg: '自己经营的所有款式里，按自已的毛6排名靠前的款式。' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'pmscStyleCodes', label: '擅长款式2', tipmesg: '公司所有数据，按款式排名，自己能排到靠前的款式。如：X系[n]，n表示排名。' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'jiGuan', label: '籍贯', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'leader1', label: '上级', formatter: (row => (row.leader1 ? row.leader1 + '-' : '') + (row.leader2 ? row.leader2 : '')), },
    { sortable: 'custom', width: '80', align: 'center', prop: 'proCodeCount', label: '经营ID数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'zjzySum', label: '资金占用', formatter: (row) => row.zjzySum !== null ? computedNum(row.zjzySum) : '', tipmesg: '近30天的售卖成本+库存资金+采购在途占用。' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'zjhbl', label: '资金回报率', formatter: (row) => row.zjhbl !== null ? row.zjhbl + '%' : '', tipmesg: '毛6/[资金占用]*12' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'advRate', label: '付费占比', tipmesg: '广告费/销售金额' },
    { sortable: 'custom', width: '80', align: 'center', prop: 'enabled', label: '是否离职', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, profileInformationDtl, cestable, numberRange, inputYunhan
    },
    data() {
        return {
            computedColor: {
                zjhbl: 150,
                profit33: 100000
            },
            showMore: false,
            isCard: true,
            active: {
                orderBy: '',
                isAsc: null,
                type: ''
            },
            ddLogo: require('@/static/images/dingding.png'),
            pddLogo: require('@/assets/images/pddLogo.png'),
            dyLogo: require('@/assets/images/dyLogo.png'),
            tgcLogo: require('@/assets/images/tgcLogo.png'),
            tmLogo: require('@/assets/images/tmLogo.png'),
            tbLogo: require('@/assets/images/tbLogo.png'),
            ksLogo: require('@/assets/images/ksLogo.png'),
            albbLogo: require('@/assets/images/albbLogo.png'),
            jdLogo: require('@/assets/images/jdLogo.png'),
            snLogo: require('@/assets/images/snLogo.png'),
            fxLogo: require('@/assets/images/fxLogo.png'),
            qtLogo: require('@/assets/images/qtLogo.png'),
            wzLogo: require('@/assets/images/wzLogo.png'),
            title: '',
            versionList: [],
            orderByList,
            platformlist,
            dialogVisible: false,
            that: this,
            photographTime: {
                startDate: '',
                endDate: ''
            },
            photographVisible: false,
            ListInfo: {
                currentPage: 1,
                pageSize: 30,
                orderBy: 'profit33',
                isAsc: false,
                groupType: 2,
                versionId: null,
                groupSuperviseId: null,
                company: null,
                userType: null
            },
            groupType: null,
            platform: null,
            extData: {
                rptStartDate: '',
                rptEndDate: ''
            },
            chatProp: {
                chatDialog: false,//趋势图弹窗
                chatTime: null,//趋势图时间
                chatLoading: true,//趋势图loading
                data: {},//趋势图数据
            },
            timeRanges: [],
            tableCols,
            tableCols2,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            item: {},
            type: '',
            grouplist: []
        }
    },
    async mounted() {
        var res2 = await getDirectorGroupList();
        this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

        await this.getVersionList()
        await this.getList()
        middlevue.$on('BookKeeper_StyleCodeRptData_SimilarityVersionSuccessMsg', (data) => {
            if (!this.versionList.some(item => item.name == data.Name)) {
                this.$message.success('添加版本成功,请在下拉选择中查看')
                this.versionList.unshift({ id: data.Id, name: data.Name })
            } else {
                this.$message.warning('版本已存在')
            }
        })
    },
    beforeDestroy() {
        middlevue.$off('BookKeeper_StyleCodeRptData_SimilarityVersionSuccessMsg')
    },
    computed: {
        showPersonCard() {
            return this.isCard && this.tableData && this.tableData.length > 0 && !this.loading
        },
        showPersonTable() {
            return !this.isCard
        },
        autoHeight() {
            return this.tableData.length <= 4 && !this.showMore ? '40%' : 'auto'
        },
        isPlatForm() {
            return this.groupType != 4
        },
    },
    methods: {
        openPhotographDialog() {
            this.photographTime.startDate = ''
            this.photographTime.endDate = ''
            this.photographVisible = true
        },
        async getVersionList() {
            const { data, success } = await getSeriesMainVersion()
            if (success) {
                this.versionList = data
            }
        },
        async photograph() {
            await saveSimilarityVersion({ startDate: this.photographTime.startDate, endDate: this.photographTime.endDate })
            this.$message.success('正在拍照中,预计3-5分钟后可查看')
            this.photographVisible = false
        },
        formatEnabled(row) {
            if (this.groupType == 4) {
                return ''
            } else {
                return row.enabled === true ? '在职' : row.enabled === false ? '离职' : ''
            }
        },
        getImgUrl(item) {
            let url = ''
            if (this.groupType == 4) {
                url = this.formatPlatFormLogo(item.platform).logo
            } else {
                url = item.avatarUrl
            }
            return url
        },
        async startSession(id) {
            const { data, success } = await getUserDingCode({ type: 4, id })
            if (success) {
                if (!data) return this.$message.error('未获取到钉钉id')
                window.open(`dingtalk://dingtalkclient/action/sendmsg?spm=dingtalk_id=${data}`, '_self')
            }
        },
        formatPlatFormLogo(val) {
            const platformLogoList = [
                { label: '拼多多', value: 2, logo: this.pddLogo },
                { label: '抖音', value: 6, logo: this.dyLogo },
                { label: '淘宝', value: 9, logo: this.tbLogo },
                { label: '天猫', value: 1, logo: this.tmLogo },
                { label: '淘工厂', value: 8, logo: this.tgcLogo },
                { label: '快手', value: 14, logo: this.ksLogo },
                { label: '阿里巴巴', value: 4, logo: this.albbLogo },
                { label: '京东', value: 7, logo: this.jdLogo },
                { label: '苏宁', value: 10, logo: this.snLogo },
                { label: '分销', value: 11, logo: this.fxLogo },
                { label: '其他', value: 5, logo: this.qtLogo },
                { label: '未知', value: 0, logo: this.wzLogo },
                { label: '京喜', value: 72, logo: this.jdLogo },
            ]
            return platformLogoList.find(item => item.value == val) ? platformLogoList.find(item => item.value == val) : ''
        },
        styleCodesCallback(val) {
            this.ListInfo.styleCodes = val
        },
        computedZJHBLColor(zjhbl) {
            // if (zjhbl >= this.computedColor.zjhbl && profit33 >= this.computedColor.profit33) {
            //     return 'red'
            // } else {
            //     return ''
            // }

            if (zjhbl >= 150) {
                return 'red'
            } else if (zjhbl < 100) {
                return 'green'
            } else {
                return ''
            }
        },
        formatRate(val) {
            return val ? decimal(val, 100, 2, '*') + '%' : ''
        },
        computedNum(val) {
            return val !== null ? decimal(val, 10000, 1, '/') + '万' : ''
        },
        formatDate(item) {
            if (item.enabled === false) {
                return dayjs(item.leaveDate).diff(dayjs(item.hireDate), 'day') + '天'
            } else if (item.enabled === true) {
                return dayjs().diff(dayjs(item.hireDate), 'day') + '天'
            }
        },
        formatPlat(platform) {
            return formatPlatform(platform)
        },
        learnMore(item, type) {
            if (this.groupType != 4) {
                this.title = item.userName + (type == 'main' ? '主营类目详情' : '擅长类目详情')
                if (this.ListInfo.groupType == 1) {
                    this.title = this.title + "-按助理汇总"
                } else if (this.ListInfo.groupType == 2) {
                    this.title = this.title + "-按专员汇总"
                } else if (this.ListInfo.groupType == 21) {
                    this.title = this.title + "-按带教汇总"
                } else if (this.ListInfo.groupType == 3) {
                    this.title = this.title + "-按组汇总"
                }
            } else {
                this.title = formatPlatform(item.platform) + (type == 'main' ? '主营类目详情' : '擅长类目详情')
            }
            this.item = item
            this.type = type
            this.platform = item.platform
            this.dialogVisible = true
        },
        async getList(type) {
            // 将mainContent的滚动条置顶
            this.$nextTick(() => {
                this.$refs.mainContent.scrollTop = 0
            })
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.groupType = this.ListInfo.groupType
            this.loading = true
            if (!this.ListInfo.versionId) {
                this.ListInfo.versionId = this.versionList.find(item => item.isDefaultVersion == 1)?.id
            }
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await pageStyleCodeDirectorFullInfoWarList(this.ListInfo)
                if (success) {
                    this.$set(this, 'tableData', [])
                    this.$set(this, 'tableData', data.list)
                    this.total = data.total
                    this.extData = data.extData ? data.extData : {}
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                console.log(error);
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ orderBy, isAsc }, sort) {
            this.active = {
                orderBy,
                isAsc: sort
            }
            this.orderByList.forEach(item => {
                if (item.orderBy == orderBy) {
                    item.isAsc = sort
                }
            })
            this.ListInfo.orderBy = orderBy
            this.ListInfo.isAsc = sort
            this.getList()
        },
        changeSort({ orderBy, isAsc }) {
            if (this.active.orderBy === '' && this.active.isAsc === null) {
                //说明目前没有排序,就默认给当前点击的名字降序
                this.active.orderBy = orderBy
                this.active.isAsc = false
                this.orderByList.forEach(item => {
                    if (item.orderBy == orderBy) {
                        item.isAsc = false
                    }
                })
            } else {
                //否则就说明有排序,就判断当前点击的名字和之前的名字是否一样
                this.active.isAsc = !this.active.isAsc
                this.orderByList.forEach(item => {
                    if (item.orderBy == this.active.orderBy) {
                        item.isAsc = this.active.isAsc
                    }
                })
                this.active.orderBy = orderBy
            }
            this.ListInfo.orderBy = orderBy
            this.ListInfo.isAsc = this.active.isAsc
            this.getList()
        },
        sortchange1({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        changeShowMore() {
            this.ListInfo.showMore = this.showMore;
            if (this.ListInfo.showMore) {
                this.getList();
            }
        }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    .publicCss {
        width: 200px;
        margin: 0 5px 0px 0px;
    }

    .computedTime {
        font-size: 14px;
        color: #333;
        color: red;
    }
}

.orderByContet {
    position: sticky;
    top: 0;
    left: 0;
    width: 98%;
    background-color: #fff;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    border-collapse: collapse;
    padding: 0 5px 5px 5px;

    .orderByItem {
        padding: 2px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        color: #353232;
        background-color: #fff;
        // flex: 1;
        // border: 1px solid #ccc;
        display: flex;
        margin-right: 10px;
        align-items: center;
        justify-content: center;
    }

    .ascendingOrder,
    .ascendingOrder1,
    .Descending,
    .Descending1 {
        width: 15px;
        height: 8px;
        //不平铺
        background-repeat: no-repeat;
        cursor: pointer;
    }

    .ascendingOrder {
        background-image: url('../../static/images/ascendingOrder.png');
    }

    .ascendingOrder1 {
        background-image: url('../../static/images/ascendingOrder1.png');
    }

    .Descending {
        background-image: url('../../static/images/Descending.png');
    }

    .Descending1 {
        background-image: url('../../static/images/Descending1.png');
    }

    // .active {
    //     border: 1px solid #409EFF;
    //     background-color: #ecf5ff;
    // }

    .iconActive {
        color: #409EFF;
    }
}

.mainContent {
    display: flex;
    flex-wrap: wrap;
    overflow: auto;
    width: 100%;
    height: 94%;
    padding-left: 5px;
    box-sizing: border-box;
    position: relative;

    .informationBox {
        box-shadow: 1px 1px 5px 1px #ccc;
        width: 547px;
        padding: 5px;
        box-sizing: border-box;
        margin-bottom: 10px;
        overflow: hidden;
        margin-right: 6px;

        .avatar {
            padding-top: 5px;
            display: flex;
            align-items: center;
            font-size: 14px;
            line-height: 25px;
            box-sizing: border-box;
            font-family: '微软雅黑';

            .imgBox {
                width: 60px;
                margin-right: 8px;
            }

            .personalInfo {
                flex: 1;
                display: flex;
                flex-direction: column;
                font-size: 12px;

                .name {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    align-items: center;

                    .name_left {
                        display: flex;
                        align-items: center;

                        .username {
                            font-size: 16px;
                            font-weight: 700;
                            color: #333;
                            font-style: normal;
                            font-family: '微软雅黑';
                        }

                        .incumbency,
                        .post {
                            font-size: 12px;
                            height: 18px;
                            border-radius: 3px;
                            line-height: 18px;
                            margin-left: 7px;
                            text-align: center;
                            padding: 1px 5px;
                        }

                        .incumbency {
                            background: #EF9020;
                            width: 30px;
                            color: #fff;

                        }

                        .post {
                            background: #2c5074;
                            color: #fff;
                        }

                        .ddTalk {
                            cursor: pointer;
                        }
                    }

                }

                .personOther {
                    display: flex;

                    .personOther_item,
                    .personOtherJG {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        margin-right: 5px;

                    }

                    .personOther_item {
                        width: 100px;
                    }

                    .personOtherJG {
                        width: 160px;
                    }
                }
            }
        }

        .personInformation {
            width: 100%;
            background-color: #F8F8F9;
            height: 150px;
            font-size: 12px;
            color: #888;
            border: 1px solid #ccc;

            .personInformation_3 {
                display: flex;
                border-bottom: 1px solid #ccc;
                height: 16.6%;

                &:last-child {
                    border-bottom: none;
                }

                .personInformation_3_item {
                    display: flex;
                    flex: 1;
                    border-right: 1px solid #ccc;
                    align-items: center;
                    padding-left: 5px;

                    &:last-child {
                        border-right: none;
                    }

                    .personInformation_title {
                        width: 73px;
                        text-align: left;
                    }

                    .content {
                        color: #333;
                    }

                    .longContent {
                        width: 450px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                }
            }
        }

        .mainCategories {
            .mainCategories_title {
                border-left: 3px solid #409EFF;
                padding-left: 3px;
            }

            .mainCategories_top {
                display: flex;
                justify-content: space-between;
                font-size: 14px;
                align-items: center;
                box-sizing: border-box;
                margin-right: 4px 0;
                color: #333;
            }

            .viewMore {
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }

    }
}

.mainContent1 {
    width: 100%;
    height: 94%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #ccc;
}

.imgCss ::v-deep img {
    max-width: 50px !important;
    max-height: 50px !important;
    min-height: 50px !important;
    min-width: 50px !important;
}

.tableImgCss ::v-deep img {
    max-width: 30px !important;
    max-height: 30px !important;
    min-height: 30px !important;
    min-width: 30px !important;
}

.ddTalk ::v-deep img {
    max-width: 20px !important;
    max-height: 20px !important;
    min-height: 20px !important;
    min-width: 20px !important;
    vertical-align: middle;
}

.orderByDown,
.orderByup {
    font-size: 16px;
    cursor: pointer;
}

::v-deep .cell {
    padding: 0 !important;
}

.btnGroup {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
}
</style>
