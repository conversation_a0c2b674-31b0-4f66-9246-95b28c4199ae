<template>
  <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template>
          <p v-for="f in files">
            <!-- 原： -->
            <!-- <el-link type="primary" :href="f.url" target="_blank">{{ f.name }}</el-link> -->
              <el-link type="primary" @click.prevent="downloadFile(f.url, f.name)" style="cursor: pointer;">{{ f.name }}</el-link>
          </p>

      </template>

      <template slot="footer">
          <el-row>
              <el-col :span="24" style="text-align:right;">
                  <el-button @click="onClose">关闭</el-button>
              </el-col>
          </el-row>
      </template>

  </my-container>
</template>
<script>
  import MyContainer from "@/components/my-container";

  export default {
      name: "DownloadFilesForm",
      components: { MyContainer},
      data() {
          return {
              that: this,
              pageLoading:false,
              files:[]
          };
      },
      async mounted() {

      },
      computed: {
      },
      methods: {

          onClose(){
              this.$emit('close');
          },

          async loadData({files}) {
              this.files=files;
          },
          async downloadFile(url, name) {
            try {
                const response = await fetch(url);
                const blob = await response.blob(); // 获取文件的 Blob 数据
                // 创建隐藏的 <a> 标签用于下载
                const aLink = document.createElement("a");
                const objectUrl = URL.createObjectURL(blob); // 创建 Blob URL
                aLink.href = objectUrl;
                aLink.download = name; // 设置文件名
                aLink.style.display = "none";
                // 添加到 DOM 并触发点击事件
                document.body.appendChild(aLink);
                aLink.click();
                // 移除 DOM 节点和释放 URL
                document.body.removeChild(aLink);
                URL.revokeObjectURL(objectUrl);
            } catch (error) {
                console.error("下载文件失败：", error);
                this.$message.error("下载失败，请重试！");
            }
        }
      },
  };
</script>
