<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="年月:">
                    <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
                </el-form-item>
                <el-form-item label="所属店铺:" label-position="right">
                    <el-select filterable v-model="filter.shopCode" placeholder="请选择" class="el-select-content">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="Id:" label-position="right">
                    <el-input v-model="filter.proCode" style="width:183px;" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :isSelectColumn='false' :hasexpand='false' @sortchange='sortchange' :tableData='ZTCKeyWordList' @select='selectchange' :isSelection='false' :tableCols='tableCols' :loading="listLoading" :showsummary='true' :summaryarry='summaryarry' @summaryClick='onsummaryClick'>
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
                <el-button-group>

                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

        <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
    import cesTable from "@/components/Table/table.vue";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
    import { formatPlatform, formatLink } from "@/utils/tools";
    import { getFinancialResultIdPageList as getPageList, exportFinacialIdResult } from '@/api/monthbookkeeper/financialDetail'
    import buschar from '@/components/Bus/buschar'
    import { getAnalysisCommonResponse } from '@/api/admin/common'

    const tableCols = [
        { istrue: true, prop: 'yearMonth', label: '年月', width: '65' },
        { istrue: true, prop: 'proCode', label: '产品id', width: '110' },
        { istrue: true, prop: 'shopCode', label: '店铺', width: '150', sortable: 'custom', formatter: (row) => { return row.shopName } },
        { istrue: true, prop: 'amountSettlement', summaryEvent: true, label: '结算收入', width: '80', sortable: 'custom', formatter: (row) => { return row.amountSettlement?.toFixed(4) } },
        { istrue: true, prop: 'amountCost', summaryEvent: true, label: '结算成本', width: '80', sortable: 'custom', formatter: (row) => { return row.amountCost?.toFixed(4) } },
        { istrue: true, prop: 'amountCrossMonthIn', summaryEvent: true, label: '跨月收入', width: '80', sortable: 'custom', formatter: (row) => { return row.amountCrossMonthIn?.toFixed(4) } },
        { istrue: true, prop: 'amountExceptionIn', summaryEvent: true, label: '异常收入', width: '80', formatter: (row) => { return row.amountExceptionIn?.toFixed(4) } },
        { istrue: true, prop: 'amountCrossMonthOut', summaryEvent: true, label: '跨月退款', width: '80', sortable: 'custom', formatter: (row) => { return row.amountCrossMonthOut?.toFixed(4) } },
        { istrue: true, prop: 'amountExceptionOut', summaryEvent: true, label: '异常退款', width: '80', formatter: (row) => { return row.amountExceptionOut?.toFixed(4) } },
        { istrue: true, prop: 'amountOut', summaryEvent: true, label: '退款', width: '80', sortable: 'custom', formatter: (row) => { return row.amountOut?.toFixed(4) } },
        { istrue: true, prop: 'amountOutCost', summaryEvent: true, label: '退款成本', width: '80', sortable: 'custom', formatter: (row) => { return row.amountOutCost?.toFixed(4) } },
        { istrue: true, prop: 'amountEmptyId', summaryEvent: true, label: '空白链接成本', width: '110', sortable: 'custom', formatter: (row) => { return row.amountEmptyId?.toFixed(4) } },
        { istrue: true, prop: 'amountReSendCost', summaryEvent: true, label: '补发成本', width: '80', sortable: 'custom', formatter: (row) => { return row.amountReSend?.toFixed(4) } },
        { istrue: true, prop: 'amountExceptionCost', summaryEvent: true, label: '异常成本', width: '80', sortable: 'custom', formatter: (row) => { return row.amountExceptionCost?.toFixed(4) } },
        { istrue: true, prop: 'amountFirstNew', summaryEvent: true, label: '品牌新享拉新费', width: '100', sortable: 'custom', formatter: (row) => { return row.amountFirstNew?.toFixed(4) } },
        { istrue: true, prop: 'createdTime', label: '计算时间', sortable: 'custom', width: '150' },
    ];
    export default {
        name: "Users",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, buschar },
        data() {
            return {
                that: this,
                filter: {
                    yearMonth: null,
                    shopCode: null
                },
                shopList: [],
                userList: [],
                groupList: [],
                ZTCKeyWordList: [],
                summaryarry: {},
                tableCols: tableCols,
                total: 0,
                pager: { OrderBy: "", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                selids: [],
                dialogVisibleSyj: false,
                fileList: [],
                analysisFilter: {
                    searchName: "tb_productmap",
                    extype: 5,
                    selectColumn: "amountSettlement",
                    filterTime: "YearMonth",
                    isYearMonthDay: false,
                    filter: null,
                    columnList: [{ columnNameCN: '结算收入', columnNameEN: 'amountSettlement' }]
                },
                buscharDialog: { visible: false, title: "", data: [] },
            };
        },
        async mounted() {
            await this.getShopList();
        },
        methods: {
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            async getShopList() {
                const res1 = await getAllShopList();
                this.shopList = [];
                res1.data?.forEach(f => {
                    //if(f.isOpen==1&&f.shopCode)
                    if (f.isCalcSettlement && f.shopCode)
                        this.shopList.push(f);
                });
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.getList();
            },
            async onExport() {
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...this.filter,
                };
                var res = await exportFinacialIdResult(params);
                if (!res?.data) {
                    this.$message({ message: "没有数据", type: "warning" });
                    return;
                }

                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '销售结算数据_Id_' + new Date().toLocaleString() + '_.xlsx')
                aLink.click()
            },
            async getList() {
                var pager = this.$refs.pager.getPager();
                const params = { ...pager, ...this.pager, ...this.filter, };
                this.listLoading = true;
                const res = await getPageList(params);
                this.listLoading = false;
                this.total = res.data?.total
                this.ZTCKeyWordList = res.data?.list;
                this.summaryarry = res.data?.summary;
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
            async onsummaryClick(property) {
                let that = this;
                this.analysisFilter.filter = {
                    yearMonth: [that.filter.yearMonth, 0],
                    shopCode: [that.filter.shopCode, 0],
                    proCode: [that.filter.proCode, 0],
                };
                this.analysisFilter.selectColumn = property;
                this.analysisFilter.columnList = [{ columnNameCN: "金额", columnNameEN: property }];

                const res = await getAnalysisCommonResponse(this.analysisFilter).then(res => {
                    that.buscharDialog.visible = true;
                    that.buscharDialog.data = res.data
                    that.buscharDialog.title = res.data.legend[0]
                });
            }
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>