<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker style="width: 220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束" 
                        clearable></el-date-picker>
                <el-select v-model="filter.brandIds" multiple clearable collapse-tags filterable placeholder="请选择采购员" class="publicCss">
                    <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="filter.titles" multiple clearable collapse-tags filterable placeholder="请选择岗位" class="publicCss">
                    <el-option v-for="item in titleList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <el-select v-model="filter.deptIds" multiple clearable collapse-tags filterable placeholder="请选择架构" class="publicCss">
                    <el-option v-for="item in deptList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%"
            :isNeedExpend="false" :showsummary="data.summary ? true : false" :summaryarry="data.summary"
            @sortchange="sortChange" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="pageChange" @size-change="sizeChange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from "dayjs";
import { getAutoStatistics, getAutoBrandDept, getAutoBrandTitle } from '@/api/inventory/purchaseordernew'
import { getAllProBrand } from '@/api/inventory/warehouse'
import { pickerOptions, platformlist, formatLinkProCode, formatTime } from '@/utils/tools'

const tableCols = [
    { istrue: true, prop: 'brandName', label: '采购', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'title', label: '岗位', width: '140', sortable: 'custom' },
    { istrue: true, prop: 'deptName', label: '架构', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'shouldAutoOrderCount', label: '应开单数量', width: '180', sortable: 'custom', },
    { istrue: true, prop: 'shouldAutoGoodsCount', label: '应开单编码数', width: '180', sortable: 'custom', },
    { istrue: true, prop: 'actualAutoOrderCount', label: '实际开单数量', width: '180', sortable: 'custom', },
    { istrue: true, prop: 'actualAutoGoodsCount', label: '实际开单编码数', width: '180', sortable: 'custom', },
];

export default {
    name: 'YunHanAutoStatistics',
    components: {MyContainer,vxetablebase},
    data(){
        return{
            platformlist,
            pickerOptions,
            that: this,
            tableCols: tableCols,
            data: {
                list: [],
                summary: [],
                total: 0,
            },
            loading: false,
            brandList: [],
            titleList: [],
            deptList: [],
            filter: {
                startDate: null,
                endDate: null,
                brandId: null,
                title: null,
                timerange: [formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")],
                deptId: null,
                brandIds: [],
                titles: [],
                deptIds: [],
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false
            }
        }
    },
    async mounted() {
        await this.init();
    },
    methods: {
        async init() {
            //采购
            var res1 = await getAllProBrand();
            this.brandList = res1.data.map(item => {
                return { value: item.key, label: item.value };
            });
            //岗位
            var res2 = await getAutoBrandTitle();
            this.titleList = res2.data.map(item => {
                return { value: item, label: item };
            });
            //架构
            var res3 = await getAutoBrandDept();
            this.deptList = res3.data.map(item => {
                return { value: item.deptId, label: item.deptName };
            });
            await this.getList();
        },
        async getList(type) {
            if (type === "search") {
                this.filter.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }

            var param = {...this.filter};

            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const res = await getAutoStatistics(param);
                if (res?.success) {
                    this.data = res.data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        sizeChange(val) {
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.getList()
        },
        //当前页改变
        pageChange(val) {
            this.filter.currentPage = val;
            this.getList()
        },
        sortChange({ order, prop }) {
            if (prop) {
                this.filter.orderBy = prop
                this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
::v-deep .el-select__tags-text {
  max-width: 30px;
}

.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>