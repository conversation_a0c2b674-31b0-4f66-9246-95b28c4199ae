<template>
 <div style="height:100%;padding:10px;overflow: auto;">
    <template>
      <el-form class="ad-form-query" :model="filter" @submit.native.prevent label-width="100px">
         <el-row>
            <el-col :xs="24" :sm="10" :md="10" :lg="10" :xl="10">
                <el-form-item label="日期:">
                    <el-date-picker style="width: 260px"
                        v-model="filter.timerange"
                        type="monthrange"
                        format="yyyy-MM"
                        value-format="yyyy-MM"
                        range-separator="至"
                        start-placeholder="开始月份"
                        end-placeholder="结束月份"
                    ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
                  <el-form-item>
                     <el-button type="primary" @click="onfresh">刷新</el-button>
                  </el-form-item>
              </el-col>
         </el-row>
      </el-form>
      </template> 
      <div id="echartabnormalgeneralanalysis2" style="width: 100%;height: 400px; box-sizing:border-box; line-height: 360px;"/>
  </div>
</template>
<script>
import * as echarts from 'echarts';
import container from '@/components/my-container/nofooter'
import cesTable from "@/components/Table/table.vue";
import {queryAbnormalGeneralMonthAnalysis} from '@/api/inventory/abnormal'
export default {
  name: 'Roles1',
  components: {container,cesTable},
  //  props:{
  //      filter: {brandId:null }
  //    },
  data() {
    return {
      that:this,
       filter: {
         timerange:null,
         startDate:null,
         endDate:null,
         brandId:null
       },
      period:0,
      pageLoading: false,
      listLoading:false
    }
  },
  mounted() {
  },
  beforeUpdate() {
  },
methods: {
   async defaultDate(){
        let date = new Date()
        let year = date.getFullYear().toString()   //'2022'
        let month = date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1).toString():(date.getMonth()+1).toString()  //'04'
        let date1= new Date(); date1.setMonth(date1.getMonth()-10) 
        let month1 = date1.getMonth() 
        let year1= date1.getFullYear()
        let beg = year1 + '-' + month1 + '-01'  //最近十二个月        
        let end = year + '-' + month + '-01'    //当月第一天'2022-01-01'
        this.filter.timerange = [beg,end]

    },
   async onSearch(brandid) {
      this.filter.brandId=brandid;
      this.filter.timerange = []
      await this.defaultDate();
      await this.getanalysisdata()
    },
   async onfresh() {
     await this.getanalysisdata()
    },
   async getanalysisdata() {
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      var parm={...this.filter};
      parm.period=this.period;
      const res = await queryAbnormalGeneralMonthAnalysis(parm);
      if (!res?.code)  return;       
      var chartDom = document.getElementById('echartabnormalgeneralanalysis2');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      if (!res.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
       }
      var option = this.Getoptions(res.data);
      option && myChart.setOption(option);
    },
    Getoptions(element){
     var series=[]
     element.series.forEach(s=>{
       series.push({smooth: true, ...s})
     })
     var yAxis=[]
     element.yAxis.forEach(s=>{
       yAxis.push({type: 'value',offset:s.offset,position:s.position,name: s.name,axisLabel: {formatter: '{value}'+s.unit}})
     })
     var option = {
        title: {text: element.title},
        tooltip: {trigger: 'axis'},
        legend: {
           data: element.legend
         },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {feature: {
            magicType: {show: true, type: ['line', 'bar']},
            //restore: {show: true},
        }},
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis: yAxis,
        series:  series
    };
    return option;
   },
  }
}
</script>
<style>
.el-select-content { 
    width: calc(100% - 10px);
    margin: 0;
 }
 
</style>
