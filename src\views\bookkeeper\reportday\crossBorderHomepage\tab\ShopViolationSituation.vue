<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="ListInfo.timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                <el-select style="width: 180px;margin-right: 5px;" v-model="ListInfo.userId" placeholder="人员"
                    class="el-select-content" filterable clearable collapse-tags>
                    <el-option v-for="item in groupNameList" :key="item.key" :label="item.label" :value="item.key" />
                </el-select>
                <el-select style="width: 180px;" v-model="ListInfo.platform" placeholder="平台" class="publicCss" @change="changePlatform" 
                    clearable  filterable collapse-tags>
                    <el-option v-for="item in platformlistKj" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select style="width: 180px;" v-model="ListInfo.shopCode" placeholder="店铺名称" class="publicCss"
                    clearable  filterable collapse-tags>
                    <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
                </el-select>

                <el-input v-model.trim="ListInfo.violationNo" placeholder="违规编号" maxlength="50" clearable class="publicCss" />
                <el-input v-model.trim="ListInfo.violationType" placeholder="扣款类型" maxlength="50" clearable class="publicCss" />
                <el-input v-model.trim="ListInfo.skc" placeholder="SKC" maxlength="50" clearable class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="onExport()" v-if="checkPermission(['ExportShopViolationHomePage_Kj'])">导出</el-button>

            </div>
        </template>
        <vxetablebase :id="'billingChargeTemu202408041401'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
            style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>


    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions ,platformlistKj,formatPlatformkj} from '@/utils/tools'
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { getShopViolationList,getDirectorGroupNameList,exportShopViolationHomePage_Kj } from '@/api/bookkeeper/crossBorderV2'
import dayjs from 'dayjs'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'platform', label: '平台',formatter: (row) => {return formatPlatformkj(row.platform)} },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopCode', label: '店铺ID', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'userName', label: '人员', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'happenTime', label: '记账日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'violationNo', label: '违规编号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'violationType', label: '扣款类型', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'actualAmount', label: '实际扣款金额', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'skc', label: 'SKC', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'estimateAmount', label: '预估违规金额', },
]
export default {
    name: "refundAfterSale",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            yearMonthDay: null,//日期
            dialogVisible: false,//导入弹窗
            fileList: [],//上传文件列表
            uploadLoading: false,//上传按钮loading
            fileparm: {},//上传文件参数
          
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                start: null,//开始时间
                end: null,//结束时间
                timeRanges: [],//时间范围
                // orderNo: null,//线上单号
                userId:null,
                // violationTypes: null
            },
            tableCols,
            tableData: [],
            summaryarry: {},
            total: 0,
            loading: false,
            pickerOptions,
            billingExpenseType: [],
            platformlistKj,
            //人员列表
            groupNameList:[],
            //店铺列表
            shopList:[]
        }
    },
    async mounted() {
        const GroupName = await getDirectorGroupNameList();
        this.groupNameList = GroupName.data?.map(item => {
            return {
                label: item.userName,
                value: [
                    item.id ? item.id : '',
                    item.userId ? item.userId : '',
                    item.userName ? item.userName : '',
                ].filter(Boolean).join(','), // 拼接 id 和 userId，用逗号分隔
                key: item.id,

            }
        });
        await this.init()
        await this.getList()

    },
    methods: {
        datetostr(date) {
            var y = date.getFullYear();
            var m = ("0" + (date.getMonth() + 1)).slice(-2);
            var d = ("0" + date.getDate()).slice(-2);
            return y + "-" + m + "-" + d;
        },
        async init() {
            var date1 = new Date(); date1.setDate(date1.getDate() - 8);
            var date2 = new Date(); date2.setDate(date2.getDate() - 1);
            this.ListInfo.timeRanges = [];
            this.ListInfo.timeRanges[0] = this.datetostr(date1);
            this.ListInfo.timeRanges[1] = this.datetostr(date2);

        },
        //平台联动 店铺
        async changePlatform(val){
            //调用获取店铺接口
           if(!!val){
            const res1 = await getAllShopList({ platforms: [val] });
                this.shopList = [];
                res1.data?.forEach(f => {
                    if (f.shopName && f.shopCode)
                    this.shopList.push(f);
                });
           }
        },

        //更新 
        async changeTime(e) {
            this.ListInfo.start = e ? e[0] : null
            this.ListInfo.end = e ? e[1] : null
        },

        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            if (this.ListInfo.timeRanges) {
                // 默认给近7天时间

                // this.ListInfo.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
                this.ListInfo.start=this.ListInfo.timeRanges[0]
                this.ListInfo.end=this.ListInfo.timeRanges[1]
            }
            const { data, success } = await getShopViolationList(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
                this.loading = false
            } else {
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        async onExport() {
            this.loading = true
            var res = await exportShopViolationHomePage_Kj(this.ListInfo);
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.setAttribute(
                "download",
                "店铺违规情况_" + new Date().toLocaleString() + ".xlsx"
            );
            aLink.click();
            this.loading = false
            },

    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 150px;
        margin-right: 5px;
    }
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 45px;
}
</style>