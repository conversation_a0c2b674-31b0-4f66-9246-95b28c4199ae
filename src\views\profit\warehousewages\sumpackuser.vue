<!-- 常规计件 -->
<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="">
                    <el-select v-model="filter.warehouseCode" style="width: 160px" size="mini" @change="onSearch"
                        placeholder="仓库">
                        <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label" clearable
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.postCode" placeholder="一级岗位编码" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.postName" style="width: 110px" size="mini" @change="onSearch" clearable
                        filterable placeholder="一级岗位名称">
                        <el-option v-for="item in myOnePostNameList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.workItem" placeholder="二级岗位名称" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.userName" placeholder="姓名" style="width:110px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onaddsumpackuser">新增</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :isSelectColumn="false" :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog :title="sumpackuserTitle" :visible.sync="sumpackuserVisible" width="35%" v-dialogDrag
            :close-on-click-modal="false" v-loading="sumpackusersaveLoading">
            <span>
                <el-form class="ad-form-query" :model="sumpackuserFormData" :inline="true" :rules="sumpackuserFormRules"
                    label-width="140px">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="仓库" prop="warehouseCode">
                                <el-select v-model="sumpackuserFormData.warehouseCode" style="width: 360px" size="mini"
                                    filterable @change="onWarehouseCodeChange">
                                    <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="姓名" prop="userName">
                                <el-input v-model.trim="sumpackuserFormData.userName" placeholder="姓名" style="width:360px;"
                                    clearable maxlength="20" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="选择对应岗位" prop="postId">
                                <el-select v-model="sumpackuserFormData.postId" style="width: 360px" size="mini"
                                    @change="onMyPostIdListChange" filterable>
                                    <el-option v-for="item in myPostIdList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="岗位编码" prop="postCode">
                                <el-input v-model.trim="sumpackuserFormData.postCode" placeholder="" style="width:360px;"
                                    clearable maxlength="20" disabled />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="一级岗位名称" prop="postName">
                                <el-input v-model.trim="sumpackuserFormData.postName" placeholder="" style="width:360px;"
                                    clearable maxlength="20" disabled />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="二级岗位名称" prop="workItem">
                                <el-input v-model.trim="sumpackuserFormData.workItem" placeholder="" style="width:360px;"
                                    clearable maxlength="20" disabled />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label=" " prop="enabled">
                                <el-checkbox v-model="sumpackuserFormData.enabled">禁用</el-checkbox>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="参与计算时间范围" prop="computeDate">
                                <el-date-picker style="width: 240px" v-model="sumpackuserFormData.computeDate"
                                    type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                                    start-placeholder="开始日期" end-placeholder="结束日期" clearable>
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="不参与计算日期" prop="noComputeDate">
                                <el-input type="textarea" :rows="8" placeholder="请输入内容"
                                    v-model="sumpackuserFormData.noComputeDate" style="width:400px;">
                                </el-input>
                                <el-tooltip class="item" effect="dark"
                                    content="参与计算时间范围内某些天不参与计算（比如请假），日期使用英文逗号隔开，无顺序要求，如：2023-01-01,2023-01-03,2023-01-02"
                                    placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </span>
            <span slot="footer" class="dialog-footer">
                <my-confirm-button type="submit" @click="onsumpackusersave" /> &nbsp;
                <el-button @click="sumpackuserVisible = false">关闭</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import cesTable from "@/components/Table/table.vue";
import MySearch from "@/components/my-search";
import buschar from '@/components/Bus/buschar';
import MySearchWindow from "@/components/my-search-window";
import {
    getWarehouseSumPackUserPageList, warehouseSumPackUserSave, warehouseSumPackUserDelete,
    getWarehousePostWagesPageList
} from '@/api/profit/warehousewages';
const tableCols = [
    { istrue: true, prop: 'warehouseName', label: '仓库', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'postCode', label: '岗位编码', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'postName', label: '一级岗位名称', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'workItem', label: '二级岗位名称', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'userName', label: '姓名', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'enabled', label: '状态', width: '80', sortable: 'custom', formatter: (row) => row.enabled ? "禁用" : "启用" },
    { istrue: true, prop: 'computeStartDate', label: '开始有效时间', width: '120', sortable: 'custom', formatter: (row) => formatTime(row.computeStartDate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'computeEndDate', label: '结束有效时间', width: '120', sortable: 'custom', formatter: (row) => formatTime(row.computeEndDate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'noComputeDate', label: '不参与计算日期', width: '400', sortable: 'custom' },
    {
        istrue: true, type: 'button', width: '55', label: '操作', width: '120',
        btnList: [
            { label: "编辑", handle: (that, row) => that.oneditsumpackuser(row.id, row.warehouseCode) },
            { label: "删除", handle: (that, row) => that.ondeletesumpackuser(row.id) }
        ]
    },
]
const tableHandles1 = [
    //{ label: "计算人效", handle: (that) => that.onCompute() },
    // { label: "导出", handle: (that) => that.onExport() },
];
export default {
    name: 'sumpackuser',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, buschar },
    props: ['myWarehouseList', 'myOnePostNameList'],
    data() {
        return {
            that: this,
            filter: {
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "createdTime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            tableHandles1: tableHandles1,

            sumpackuserTitle: "",
            sumpackuserVisible: false,
            sumpackusersaveLoading: false,
            myPostIdList: [],
            sumpackuserFormData: {
                warehouseCode: null,
                warehouseName: null,
                userName: null,
                enabled: false,
                noComputeDate: null,
                postId: "",
                postCode: "",
                postName: "",
                workItem: "",
                computeDate: [],
                computeStartDate: null,
                computeEndDate: null,
            },
            sumpackuserFormRules: {
                warehouseCode: [{ required: true, message: '请输入仓库', trigger: 'blur' }],
                userName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
                postId: [{ required: true, message: '请选择对应岗位', trigger: 'blur' }],
                computeDate: [{ required: true, message: '请选择参与计算时间范围', trigger: 'blur' }],
            },
        };
    },
    async mounted() {
        await this.onSearch();
    },
    methods: {
        async onWarehouseCodeChange(warecode) {
            this.sumpackuserFormData.postId = null;
            this.sumpackuserFormData.postCode = null;
            this.sumpackuserFormData.postName = null;
            this.sumpackuserFormData.workItem = null;
            await this.getMyPostIdList(warecode);
        },
        async getMyPostIdList(warecode) {
            this.myPostIdList = [];
            this.myPostIdList.push({ value: "0", label: "JiBao|集包|集包" });
            let params = { warehouseCode: warecode, currentPage: 1, pageSize: 300 }
            this.sumpackusersaveLoading = true;
            const res = await getWarehousePostWagesPageList(params);
            this.sumpackusersaveLoading = false;
            if (res?.success && res?.data?.list?.length > 0) {
                res.data.list.forEach(f => {
                    this.myPostIdList.push({ value: f.id, label: f.postCode + "|" + f.postName + "|" + f.workItem });
                });
            }
        },
        async onMyPostIdListChange(cvalue) {
            this.sumpackuserFormData.postCode = null;
            this.sumpackuserFormData.postName = null;
            this.sumpackuserFormData.workItem = null;
            let pLabel = this.myPostIdList.find(f => f.value == cvalue);
            if (pLabel) {
                let split = pLabel.label.split('|');
                if (split.length == 3) {
                    this.sumpackuserFormData.postCode = split[0].toString();
                    this.sumpackuserFormData.postName = split[1].toString();
                    this.sumpackuserFormData.workItem = split[2].toString();
                }
            }
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getWarehouseSumPackUserPageList(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            //this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
        },
        //排序查 询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            console.log(rows)
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onaddsumpackuser() {
            this.sumpackuserFormData = {
                id: 0,
                enabled: false,
                warehouseCode: null,
                warehouseName: null,
                userName: null,
                noComputeDate: null,
                postId: "",
                postCode: "",
                postName: "",
                workItem: "",
                computeDate: [],
                computeStartDate: null,
                computeEndDate: null,
            };
            this.sumpackuserTitle = "新增";
            this.sumpackuserVisible = true;
        },
        async onsumpackusersave() {
            if (!this.sumpackuserFormData.computeDate || this.sumpackuserFormData.computeDate.length != 2) {
                this.$message({ type: 'error', message: '请选择参与计算时间范围!' });
                return;
            }
            this.sumpackuserFormData.computeStartDate = this.sumpackuserFormData.computeDate[0];
            this.sumpackuserFormData.computeEndDate = this.sumpackuserFormData.computeDate[1];

            this.sumpackuserFormData.warehouseName = this.myWarehouseList.find(f => f.value == this.sumpackuserFormData.warehouseCode)?.label;
            console.log(this.sumpackuserFormData);
            this.sumpackusersaveLoading = true;
            const res = await warehouseSumPackUserSave(this.sumpackuserFormData);
            this.sumpackusersaveLoading = false;
            if (res?.success) {
                this.$message({ type: 'success', message: '操作成功!' });
                this.sumpackuserVisible = false;
                await this.onSearch();
            }
        },
        async ondeletesumpackuser(rowid) {
            this.$confirm('确定要执行删除吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                console.log(rowid);
                const res = await warehouseSumPackUserDelete({ id: rowid });
                if (res?.success) {
                    this.$message({ type: 'success', message: '删除成功!' });
                    await this.onSearch();
                }
            }).catch(() => {
            });
        },
        async oneditsumpackuser(rowid, warehouseCode) {
            this.sumpackuserVisible = true;
            let params = { id: rowid, currentPage: 1, pageSize: 1 };
            this.sumpackusersaveLoading = true;
            const res = await getWarehouseSumPackUserPageList(params);
            await this.getMyPostIdList(warehouseCode);
            this.sumpackusersaveLoading = false;
            if (res?.success) {
                let data = res.data.list[0];
                console.log(data);
                this.sumpackuserFormData = {
                    id: data.id,
                    enabled: data.enabled,
                    warehouseCode: data.warehouseCode,
                    warehouseName: data.warehouseName,
                    userName: data.userName,
                    noComputeDate: data.noComputeDate,
                    postId: data.postId,
                    postCode: data.postCode,
                    postName: data.postName,
                    workItem: data.workItem,
                    computeDate: [formatTime(data.computeStartDate, 'YYYY-MM-DD'), formatTime(data.computeEndDate, 'YYYY-MM-DD')],
                    computeStartDate: data.computeStartDate,
                    computeEndDate: data.computeEndDate,
                };
            }
        },
    },
};
</script>

<style lang="scss" scoped></style>
