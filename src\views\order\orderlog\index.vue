<template>
    <my-container v-loading="pageLoading" style="height: 100%">
        <el-tabs v-model="activeName" style="height: 94%">
            <el-tab-pane label="日志明细" name="first1" style="height: 100%">
                <orderlogdetail ref="orderlogdetail" style="height: 100%"></orderlogdetail>
            </el-tab-pane>
            <!-- <el-tab-pane label="实时操作节点" name="first2" style="height: 100%" lazy>
                <orderlognode ref="orderlognode" style="height: 100%"></orderlognode>
            </el-tab-pane> -->
        </el-tabs>
    </my-container>
</template>
  
  <script>
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import cesTable from "@/components/Table/table.vue";
    import dayjs from "dayjs";
    import { formatTime } from "@/utils";
    import orderlogdetail from "@/views/order/orderlog/orderlogdetail.vue";
    import orderlognode from "@/views/order/orderlog/orderlognode.vue";
    export default {
        name: "orderlogindex",
        components: {
            cesTable, MyContainer, MyConfirmButton, orderlogdetail, orderlognode
        },
        data() {
            return {
                that: this,
                pageLoading: false,
                activeName: "first1",
            };
        },
        async mounted() {

        },
        methods: {
        },
    };
  </script>
  
  <style lang="scss" scoped>
</style>
  