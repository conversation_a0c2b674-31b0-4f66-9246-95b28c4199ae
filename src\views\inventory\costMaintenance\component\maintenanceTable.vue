<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.finishedProductCode" placeholder="成品编码" maxlength="50" clearable
          class="publicCss" />
        <el-input v-model.trim="ListInfo.finishedProductName" placeholder="成品名称" maxlength="50" clearable
          class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'maintenanceTable202507191150'" :tablekey="'maintenanceTable202550191107'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'" :border="true" />
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import { GetPackagesProcesingCostMaintenanceLogList, ExportPackagesProcesingCostMaintenanceLogList } from '@/api/cwManager/costMaintenanceManager'
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: '150', align: 'center', prop: 'pullDate', label: '日期' },
  { sortable: 'custom', width: '150', align: 'center', prop: 'finishedProductCode', label: '成品编码' },
  { sortable: 'custom', width: '300', align: 'center', prop: 'finishedProductName', label: '成品名称' },
  { sortable: 'custom', width: '150', align: 'center', prop: 'newCost', label: '新成本' },
  { sortable: 'custom', width: '150', align: 'center', prop: 'oldCost', label: '历史成本' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'remark', label: '备注' },
  { sortable: 'custom', width: '150', align: 'center', prop: 'modifiedTime', label: '维护日期' },
  { sortable: 'custom', width: '150', align: 'center', prop: 'difference', label: '差异' },
]
export default {
  name: "maintenanceTable",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startCreateTime: null,//开始时间
        endCreateTime: null,//结束时间
        finishedProductCode: null,//成品编码
        finishedProductName: null,//成品名称
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.startCreateTime = e ? e[0] : null
      this.ListInfo.endCreateTime = e ? e[1] : null
    },
    async exportProps() {
      this.loading = true
      const { data } = await ExportPackagesProcesingCostMaintenanceLogList(this.ListInfo)
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '加工成本维护表' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        this.ListInfo.startCreateTime = dayjs().format('YYYY-MM-DD')
        this.ListInfo.endCreateTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startCreateTime, this.ListInfo.endCreateTime]
      }
      this.loading = true
      const { data, success } = await GetPackagesProcesingCostMaintenanceLogList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.pullDate = dayjs(item.pullDate).format('YYYY-MM-DD')
          item.modifiedTime = dayjs(item.modifiedTime).format('YYYY-MM-DD HH:mm:ss')
        })
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
