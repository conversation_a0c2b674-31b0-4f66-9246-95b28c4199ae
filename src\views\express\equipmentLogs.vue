<template>
    <MyContainer>
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane label="操作日志" name="operation">
                <div class="top">
                    <dateRange class="publicCss" v-model="timeRanges" @change="timeRangeChange"></dateRange>
                    <el-button type="primary" size="small" @click="getList()">查询</el-button>
                </div>
                <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
                    @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
                    :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'600px'">
                </vxetablebase>
                <template #footer>
                    <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
                </template>
            </el-tab-pane>
            <el-tab-pane label="数据日志" name="data">
                <vxetablebase ref="dataTable" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
                    @sortchange='dataSortchange' :tableData='dataTableData' :tableCols='dataTableCols' :isSelection="false"
                    :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="dataLoading" :height="'600px'">
                </vxetablebase>
                <template #footer>
                    <my-pagination ref="dataPager" :total="dataTotal" @page-change="dataPagechange" @size-change="dataSizechange" />
                </template>
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { logPage, getDeviceLogPage } from '@/api/order/orderData'
const statusList = [
    { label: '开始', value: 0 },
    { label: '成功', value: 1 },
    { label: '异常失败', value: 2 },
]
const deviceStatusList = [
    { label: '在线', value: 1 },
    { label: '离线', value: 2 },
]
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'deviceId', label: '集包台IP', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createTime', label: '创建时间', formatter: (row) => row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop:'updateTime', label: '修改时间', formatter: (row) => row.updateTime? dayjs(row.updateTime).format('YYYY-MM-DD HH:mm:ss') : ''  },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'step', label: '步骤说明', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '状态', formatter: (row) => statusList.find(item => item.value == row.status)?.label },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'updateUserName', label: '操作人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'remark', label: '备注', },
]
const dataTableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'macAddress', label: '硬件地址', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'deviceIp', label: '设备IP', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'deviceId', label: '设备编号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '状态', formatter: (row) => deviceStatusList.find(item => item.value == row.status)?.label },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalReceived', label: '接收数据总量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'validData', label: '有效数据量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'filterData', label: '过滤数据量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'failedData', label: '异常数据量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'onlineTime', label: '在线时间', formatter: (row) => row.onlineTime ? dayjs(row.onlineTime).format('YYYY-MM-DD HH:mm:ss') : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'offlineTime', label: '离线时间', formatter: (row) => row.offlineTime ? dayjs(row.offlineTime).format('YYYY-MM-DD HH:mm:ss') : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'durationSeconds', label: '在线时长(秒)', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'remark', label: '备注', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    props: {
        deviceIp: {
            type: String,
            default: ''
        },
        macAddress: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            that: this,
            activeTab: 'operation',
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                deviceId: this.deviceIp,
                startTime: '',
                endTime: '',
            },
            dataListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                deviceId: this.deviceIp,
                macAddress: this.macAddress,
                startTime: '',
                endTime: '',
            },
            timeRanges: [],
            dataTimeRanges: [],
            tableCols,
            dataTableCols,
            tableData: [],
            dataTableData: [],
            total: 0,
            dataTotal: 0,
            loading: true,
            dataLoading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getList()
    },
    watch: {
        deviceIp: {
            handler(newVal) {
                this.ListInfo.deviceId = newVal
                this.dataListInfo.deviceId = newVal
                this.getList()
                if (this.activeTab === 'data') {
                    this.getDataList()
                }
            },
            immediate: true
        }
    },
    methods: {
        timeRangeChange(val) {
            if (val && val.length === 2) {
                this.ListInfo.startTime = val[0] ? dayjs(val[0]).format('YYYY-MM-DD') : '';
                this.ListInfo.endTime = val[1] ? dayjs(val[1]).format('YYYY-MM-DD') : '';
            } else {
                this.ListInfo.startTime = '';
                this.ListInfo.endTime = '';
            }
            this.getList('search');
        },
        handleTabClick(tab) {
            if (tab.name === 'data' && this.dataTableData.length === 0) {
                this.getDataList()
            }
        },
        async getList() {
            this.loading = true
            try {
                const { data, success } = await logPage(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        async getDataList(type) {
            if (type == 'search') {
                this.dataListInfo.currentPage = 1
                this.$refs.dataPager.setPage(1)
            }
            this.dataLoading = true
            try {
                const { data, success } = await getDeviceLogPage(this.dataListInfo)
                if (success) {
                    this.dataTableData = data.list
                    this.dataTotal = data.total
                    this.dataLoading = false
                } else {
                    //获取列表失败
                    this.dataLoading = false
                    this.$message.error('获取数据日志列表失败')
                }
            } catch (error) {
                this.dataLoading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        //数据日志每页数量改变
        dataSizechange(val) {
            this.dataListInfo.currentPage = 1;
            this.dataListInfo.pageSize = val;
            this.getDataList()
        },
        //数据日志当前页改变
        dataPagechange(val) {
            this.dataListInfo.currentPage = val;
            this.getDataList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        dataSortchange({ order, prop }) {
            if (prop) {
                this.dataListInfo.orderBy = prop
                this.dataListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getDataList()
            }
        },
        dataTimeRangeChange(val) {
            if (val && val.length === 2) {
                this.dataListInfo.startTime = val[0] ? dayjs(val[0]).format('YYYY-MM-DD HH:mm:ss') : '';
                this.dataListInfo.endTime = val[1] ? dayjs(val[1]).format('YYYY-MM-DD HH:mm:ss') : '';
            } else {
                this.dataListInfo.startTime = '';
                this.dataListInfo.endTime = '';
            }
            this.getDataList('search');
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
