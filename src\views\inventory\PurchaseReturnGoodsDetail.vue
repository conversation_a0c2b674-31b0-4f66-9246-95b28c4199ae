<template>
    <container v-loading="pageLoading">
       <ces-table ref="table" :that='that' :isIndex='true' @sortchange='sortchange' @select='selectchange'  
           :hasexpand='true' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"
           :showsummary='true' :summaryarry='summaryarry'>
           <template slot='extentbtn'>  
         <el-button-group>    
             <el-button style="padding: 0;margin: 0;">
           <!-- <el-input v-model.trim="filter.buyNo" clearable style="width: 200px" placeholder="空格分隔采购单号"/> -->
           <el-input v-model.trim="filter.buyNo"  maxlength="30" clearable style="width: 100px" placeholder="采购单号"/>
           </el-button>
           <el-button style="padding: 0;margin: 0;">
            <!-- <el-input v-model.trim="filter.buyNo" clearable style="width: 200px" placeholder="空格分隔采购单号"/> -->
            <el-input v-model.trim="filter.io_id" maxlength="30" clearable style="width: 100px" placeholder="退货单号"/>
            </el-button>
          <el-button style="padding: 0;margin: 0;">
         
           <el-input v-model.trim="filter.SuName" maxlength="30" clearable style="width: 150px" placeholder="供应商"/>
        </el-button>
           <el-button style="padding: 0;margin: 0;">
           <el-date-picker style="width: 300px" v-model="filter.timerangetk" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                  range-separator="至" start-placeholder="退货日期" end-placeholder="退货日期"></el-date-picker>
       
           </el-button>
         <el-button type="primary" @click="onSearch">查询</el-button>
          </el-button-group>
         </template>
        </ces-table>
        
      <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
      </template>

      <el-dialog title="采购退货明细" :visible.sync="fundGoodsDetail.visible" width="80%" v-dialogDrag>
        <div>
          <FundGoodsDetail ref="fundGoodsDetail" :filter="fundGoodsDetail.filter" style="height:600px;"></FundGoodsDetail>
        </div>
      </el-dialog>
    </container>
  </template>
  <script>
  
  import {getPurchaseReturnGoods} from '@/api/inventory/purchase'
  import {formatTime} from "@/utils/tools";
  import cesTable from "@/components/Table/table.vue";
  import container from "@/components/my-container";
  import MyConfirmButton from '@/components/my-confirm-button'
  import logistics from '@/components/Comm/logistics'
  import FundGoodsDetail from '@/views/inventory/FundGoodsDetail'
  import {getAllSupplier} from '@/api/inventory/supplier'
  
  const tableCols =[
        {istrue:true,prop:'po_id',label:'采购单号', width:'100',sortable:'custom'},
        {istrue:true,prop:'io_id',label:'退货单号', width:'100',sortable:'custom', type: 'click', handle: (that, row) => that.showDetail(row) },
        {istrue:true,prop:'io_date',label:'退货日期', width:'200',sortable:'custom'},
        {istrue:true,prop:'status',label:'状态', width:'100',sortable:'custom'},
        {istrue:true,prop:'so_id',label:'线上单号', width:'100',sortable:'custom'},
        {istrue:true,prop:'warehouse',label:'仓库名',  width:'200',sortable:'custom'},
        {istrue:true,prop:'receiver_name',label:'收货人/供应商名称', width:'200',sortable:'custom'},
        {istrue:true,prop:'wh_id',label:'仓库编号', width:'100',sortable:'custom',formatter: (row) =>(row.wh_id==1?"主仓":row.wh_id==2?"销退仓":row.wh_id==3?"进货仓":row.wh_id==4?"次品仓":"")},
        {istrue:true,prop:'remark',label:'备注', width:'200',sortable:'custom'},
        {istrue:true,prop:'logistics_company',label:'物流公司', width:'100',sortable:'custom'},
        {istrue:true,prop:'l_id',label:'物流单号', width:'100',sortable:'custom'},
       ];
  const tableHandles=[
    //   {label:"导入", handle:(that)=>that.startImport()},
  ];
  export default {
    name: "Users",
    components: {container,cesTable,MyConfirmButton,logistics,FundGoodsDetail},
    
    data() {
      return {
        fundGoodsDetail: {
        visible: false,
        filter: {
            io_id: null,
           
        }
      },
         editLoading:false,
        addVisible:false,
        deletefilter:{
         id:"",
        },
        
        editVisible:false,
        that:this,
        formatTime:formatTime,
        filter: {       
          io_id: null,
          timerangetk:'',
          buyNo:'',
          SuName:null
        },
        
        list: [],
        tableCols:tableCols,
        tableHandles:tableHandles,
        pager:{OrderBy:"io_id",IsAsc:false},
        summaryarry:{},
        total:0,
        sels: [],
        selids: [],
        fileList:[],
        suppilelist:[],
        listLoading: false,
        dialogVisible: false,
        pageLoading: false,
        uploadLoading:false,
   
      };
    },
    watch: {
      value(n) {
        if(n) {
          this.$nextTick(() => {
            console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
            this.$refs.table.doLayout();
          });
          this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
        }
      }
    },
   async mounted() {
      await this.onSearch();
      await this.init();
      await this.initform();
    },
   methods: {
    showDetail(row) {
      this.fundGoodsDetail.filter.io_id = row.io_id;

      this.fundGoodsDetail.visible = true;
      setTimeout(async () => {
        await this.$refs.fundGoodsDetail.onSearch();
      }, 100);

    },
 
     async onSearch() {
         this.$refs.pager.setPage(1)
         this.getlist()
      },
     async getlist() {
        if (!this.pager.OrderBy) this.pager.OrderBy="";
        var pager = this.$refs.pager.getPager()
        const params = {...pager,...this.pager,... this.filter}
       if (params.timerangetk) {
          params.startDate = params.timerangetk[0];
          params.endDate = params.timerangetk[1];
        }
        this.listLoading = true
        const res = await getPurchaseReturnGoods(params)
        this.listLoading = false
        if (!res?.success) return 
        this.total = res.data.total
        const data = res.data.list
        data.forEach(d => {d._loading = false})
        this.list = data
        this.summaryarry=res.data.summary;
      },
      cancelImport() {
        this.dialogVisible = false;
      },
      beforeRemove() {
        return false;
      },
     sortchange(column){
        if(!column.order)
          this.pager={};
        else
          this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
        this.onSearch();
      },
     selsChange: function(sels) {
        this.sels = sels
      },
     selectchange:function(rows,row) {
        this.selids=[];
        rows.forEach(f=>{
          this.selids.push(f.proBianMa);
        })
      },
    },
  };
  </script>
  
  
  