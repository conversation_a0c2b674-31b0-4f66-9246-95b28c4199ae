<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="">
                    <el-select ref="selectUpResId" v-model="chooseName" clearable style="width: 100px" size="mini"
                        @clear="() => { filter.ddDeptId = null }" placeholder="招聘部门">
                        <el-option hidden value="一级菜单" :label="chooseName"></el-option>
                        <el-tree style="width: 200px;" :data="deptList" :props="defaultProps" :expand-on-click-node="false"
                            :check-on-click-node="true" @node-click="handleNodeClick">
                        </el-tree>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.recruiterDDUserId" placeholder="招聘专员" style="width: 100px" size="mini"
                        clearable filterable>
                        <el-option v-for="item in recruiterList" :label="item.userName" :value="item.ddUserId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.initialTestResult" placeholder="初始结果" style="width: 100px" size="mini"
                        clearable>
                        <el-option label="通过" :value="true"></el-option>
                        <el-option label="未通过" :value="false"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.finalTestResult" placeholder="复试结果" style="width: 100px" size="mini"
                        clearable>
                        <el-option label="通过" :value="true"></el-option>
                        <el-option label="未通过" :value="false"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.positionName" placeholder="请输入岗位名称" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.name" placeholder="请输入人才名称" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.candidateFrom" placeholder="类型" style="width: 80px" size="mini"
                        clearable>
                        <el-option label="全部" :value="null"></el-option>
                        <el-option label="社招" :value="0"></el-option>
                        <el-option label="内推" :value="1"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="">
                    <el-input v-model.trim="filter.keywords" style="width: 160px" :maxLength="100" placeholder="关键字查询"
                        clearable>
                        <el-tooltip slot="suffix" class="item" effect="dark" content="姓名、电话、部门、岗位、专员、初试部门、复试部门、流失原因"
                            placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">筛选</el-button>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            style="height: calc(100vh - 230px);" :tableData='datalist' :isSelection="false" :tableCols='tableCols'
            :isSelectColumn='false' :customRowStyle="customRowStyle" :loading="listLoading" :summaryarry="summaryarry"
            :selectColumnHeight="'0px'" :isBorder="false">
            <template slot='extentbtn'> </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getDataList" />
        </template>
        <!-- 新增/编辑 -->
        <el-dialog :title="dialogTitle" :visible.sync="showDialogSon" width="50%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <div style="height: 65vh; overflow-x: hidden;">
                <talentInformation v-if="showDialogSon" ref="talentInformation" :candidateInfo="candidateInfo"
                    :isEdit="isEdit" @closeDialog="closeDialog" @closeLoading="() => { subLoad = false }"
                    @openLoading="() => { subLoad = true }"></talentInformation>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showDialogSon = false">取 消</el-button>
                    <!-- <my-confirm-button type="submit" @click="submitTalent" /> -->
                    <el-button type="primary" :loading="subLoad" @click="submitTalent">保存</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 人才流失 -->
        <el-dialog title="人才流失原因" :visible.sync="showFinishDialog" width="20%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <el-form label-width="60px" :model="loseForm" disabled>
                <el-form-item label="原因:" prop="lostReason">
                    <el-select v-model="loseForm.lostReason" placeholder="流失原因" size="mini" style="width: 100%;">
                        <el-option label="通勤时间过长" value="通勤时间过长"></el-option>
                        <el-option label="公司福利不好" value="公司福利不好"></el-option>
                        <el-option label="薪资太低" value="薪资太低"></el-option>
                        <el-option label="氛围不喜欢" value="氛围不喜欢"></el-option>
                        <el-option label="工作压力大" value="工作压力大"></el-option>
                    </el-select>
                    <!-- {{ loseForm.lostReason }} -->
                </el-form-item>
                <el-form-item label="备注:">
                    <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" placeholder="流失备注"
                        v-model="loseForm.lostRemark" maxlength="80" show-word-limit>
                    </el-input>
                    <!-- {{ loseForm.lostRemark }} -->
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showFinishDialog = false">关闭</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 查看人才信息 -->
        <el-drawer title="人才信息" size="50%" :visible.sync="drawer" :direction="direction">
            <talentInformation v-if="drawer" :isEdit="isEdit" :candidateInfo="candidateInfo"></talentInformation>
        </el-drawer>

    </my-container>
</template>
<script>
import cesTable from "@/components/Table/tableforvedio.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import postionDialogform from "@/views/profit/PersonnelRecruiting/postionDialogform";
import talentInformation from "@/views/profit/PersonnelRecruiting/talentInformation";
import { AllDDDeptTreeNcWh, getDeptUsers } from '@/api/profit/personnel'
import { pageCandidateYb, getCandidateInfo, candidateLostBackBatch, exportCandidateYb } from '@/api/profit/hr'

const tableCols = [
    { istrue: true, prop: 'positionName', align: 'left', label: '岗位名称', sortable: 'custom', width: "280" },
    { istrue: true, prop: 'department', align: 'left', label: '招聘部门', sortable: 'custom', },
    { istrue: true, prop: 'name', align: 'left', label: '姓名', type: "click", handle: (that, row) => that.editPostion(row.candidateId, 0), sortable: 'custom', },
    { istrue: true, prop: 'gender', align: 'left', label: '性别', formatter: (row) => row.gender == null ? '' : row.gender == 1 ? '男' : '女', sortable: 'custom' },
    { istrue: true, prop: 'phone', align: 'left', label: '联系电话', sortable: 'custom', },
    { istrue: true, prop: 'initialTestResult', align: 'left', label: '初试结果', formatter: (row) => row.initialTestResult == null ? '' : row.initialTestResult ? '通过' : '未通过' },
    { istrue: true, prop: 'finalTestResult', align: 'left', label: '复试结果', formatter: (row) => row.finalTestResult == null ? '' : row.finalTestResult ? '通过' : '未通过' },
    { istrue: true, prop: 'recruiter', align: 'left', label: '招聘专员', sortable: 'custom', },
    { istrue: true, prop: 'lostReason', align: 'left', label: '流失原因', sortable: 'custom', type: "click", handle: (that, row) => that.showLostRemark(row) },
    { istrue: true, prop: 'candidateFrom', align: 'center', label: '类型', formatter: (row) => row.candidateFromTxt, sortable: 'custom' },
    {
        istrue: true, type: "button", label: '操作', width: "240",
        btnList: [
            { label: "编辑", permission: "", handle: (that, row) => that.editPostion(row.candidateId, 1) },
            { label: "进入人才库", permission: "", handle: (that, row) => that.finishPostion(row.candidateId) },
        ]
    }
];

export default {
    name: "reserveTalents",//试用人才库
    components: {
        MyContainer, postionDialogform, MyConfirmButton
        , cesTable, talentInformation,
    },
    props: {
        showDialog: {
            type: Boolean,
            default: () => { return false; }
        },
        diologTitle: {
            type: String,
            default: () => { return ''; }
        },
    },
    watch: {
    },
    data () {
        return {
            showFinishDialog:false,
            loseForm: {
                lostReason: null,
                lostRemark:null,
            },
            subLoad: false,
            showDialogSon: this.showDialog,
            chooseId: '',
            chooseName: '',
            defaultProps: {
                children: 'childDeptList',
                label: 'name'
            },
            deptList: [],
            recruiterList: [],
            filter: {
                ddDeptId: null,//
                recruiterDDUserId: null,//
                name: null,//
                initialTestResult: null,//
                finalTestResult: null,//
                positionName: null,//
                queryType: 2,//1面试人才库、2预备人才库、3试用人才库、4正式人才库、5离职人才库
                candidateFrom:null,
                keywords: null,
            },
            istLoading: false,
            summaryarry: {},
            datalist: [
            ],
            total: 0,
            sels: [], // 列表选中列
            listLoading: false,
            that: this,
            pageLoading: false,
            pager: {},
            tableCols: tableCols,
            isEdit: false,
            drawer: false,
            direction: 'rtl',
            candidateInfo: {},
            dialogTitle: null,
        };
    },
    watch: {
    },
    created () {

    },
    mounted () {
        this.getDeptList();
        this.getRecruiters();
        this.onSearch()
    },
    methods: {
        // 显示流失具体原因
        showLostRemark (row) {
            // if (row.lostRemark != null) {
            //     this.$alert(row.lostRemark, '流失备注', {
            //     confirmButtonText: '确定',
            // });
            // } else {
            //     this.$message({ message: '当前预备人才暂无具体流失备注。', type: "info" });
            // }
            this.loseForm.lostReason = row.lostReason;
            this.loseForm.lostRemark = row.lostRemark;
            this.showFinishDialog=true
        },
        //获取招聘专员
        getRecruiters () {
            let params = {
                deptName: '招聘组,人事组,SSC&员工关系组,培训组',
                includeAllChildDpt: 1,
            }
            getDeptUsers(params).then(res => {
                if (res.success) {
                    this.recruiterList = res.data;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        // 节点点击事件
        handleNodeClick (data) {
            // 配置树形组件点击节点后，设置选择器的值，配置组件的数据
            this.chooseName = data.name;
            this.filter.ddDeptId = data.dept_id;
            // 选择器执行完成后，使其失去焦点隐藏下拉框效果
            this.$refs.selectUpResId.blur();
        },
        // 获取部门列表
        async getDeptList () {
            await AllDDDeptTreeNcWh().then(res => {
                if (res.success) {
                    this.deptList = res.data.childDeptList;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        // 保存人才信息
        async submitTalent () {
            await this.$refs.talentInformation.submitForm();
        },
        //关闭弹窗
        closeDialog () {
            this.showDialogSon = false;
            this.$refs.talentInformation.resetFrom();
            this.onSearch();
        },

        // 筛选
        onSearch (filter) {
            this.$refs.pager.setPage(1);
            this.getDataList();
        },
        //预备人才导出
        async onExport() {
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter};
            var res = await exportCandidateYb(params);
            if(!res?.data) {
                return false;
            };
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '预备人才导出' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        //完成
        finishPostion (candidateId) {
            this.$confirm('进入人才库后，该人才可重新进行入职操作，可在人才库查看。', '确认将该人才放至人才库吗？', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await candidateLostBackBatch([candidateId])
                if (!res?.success) { return }
                this.$message({ type: 'success', message: '成功进入人才库!' });
                this.onSearch()
            }).catch(() => {
                this.$message({ type: 'info', message: '取消操作' });
            });
        },
        // 编辑
        async editPostion (candidateId, number) {
            await getCandidateInfo({ candidateId: candidateId }).then(res => {
                if (res.success) {
                    this.candidateInfo = res.data;
                }
            })
            if (number) {
                this.showDialogSon = true;
                this.dialogTitle = '编辑人才';
                this.isEdit = true;
            } else {
                this.drawer = true;
                this.isEdit = false;
            }

        },

        //获取数据
        async getDataList () {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.listLoading = true;
            const res = await pageCandidateYb(params);
            this.listLoading = false;
            this.total = res.data.total
            this.datalist = res.data.list;
            this.summaryarry = res.data.summary;
        },

        //列表排序
        sortchange (column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        customRowStyle (row, index) {
            if (row.row?.isend && row.row.isend == 1) {
                let styleJson = {};
                styleJson.color = "rgb(216 216 216)";
                return styleJson
            } else {
                return null
            }

        },
    },
};
</script>
<style scoped></style>
