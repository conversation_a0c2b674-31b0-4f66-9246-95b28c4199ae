<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="">
                    <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM"
                        value-format="yyyyMM" placeholder="年月">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.shopName" placeholder="店铺" style="width:160px;" clearable />
                </el-form-item>
                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.orderNumber" placeholder="原始线上订单号" style="width:160px;" clearable />
                </el-form-item>
                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.numberExpress" placeholder="快递单号" style="width:160px;" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onImport1">导入</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='ZTCKeyWordList' :showsummary='true' :summaryarry='summaryarry' @select='selectchange'
            :isSelection='false' :tableCols='tableCols' :loading="listLoading" :isSelectColumn='false'>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>


        <el-dialog :title="importTitle" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading"
            :close-on-click-modal="false">
            <div style="display: flex;flex-direction: column;justify-content: center;">

                <el-date-picker style="width: 120px;margin-top: 20px; margin-bottom: 20px;" v-model="importUseMonth"
                    type="month" format="yyyyMM" value-format="yyyyMM" placeholder="导入月份"></el-date-picker>

                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>

            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

    </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { GetVirtualOrderJD as getPageList } from '@/api/monthbookkeeper/financialDetail'
import { ImportJingDongVirtualOrderAsync } from '@/api/monthbookkeeper/import'
const tableCols = [
    { istrue: true, prop: 'yearMonth', label: '年月', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'numberInternalOrder', label: '内部订单号', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'numberOnlineOrder', label: '线上订单号', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'numberOnlineOrderOrigin', label: '原始线上订单号', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'shopCode', label: '店铺编号', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'shopName', label: '店铺名称', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'orderTime', label: '下单时间', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'payTime', label: '付款日期', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'sendTime', label: '发货日期', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'statusOrder', label: '状态', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'numberExpress', label: '快递单号', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'expressCompany', label: '快递公司', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'typeOrder', label: '订单类型', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'nameWarehouse', label: '发货仓', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'nameMultiTag', label: '标签', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amountPayShould', label: '应付金额', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amountPayAlready', label: '已付金额', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amountDiKou', label: '抵扣金额', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amountDeal', label: '商品总成交金额', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amountBuyerPayActual', label: '买家实付金额', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amountSellerInActual', label: '卖家实收金额', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amountCost', label: '成本价', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'styleCode', label: '款号', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'nameProductBianma', label: '商品编码', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'countSale', label: '数量', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'isGift', label: '是否赠品', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'nameBrand', label: '品牌', sortable: 'custom', width: '120' },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
    data() {
        return {
            that: this,
            filter: {
                yearMonth: null,
                shopCode: null,
                shopName: null,
                orderNumber: null,
                numberExpress: null,
            },
            ZTCKeyWordList: [],
            tableCols: tableCols,
            summaryarry: {},
            total: 0,
            pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],


            fileList: [],
            importTitle: "",
            importFeeType: "",
            importUseMonth: null,
            importLoading: false,
            importVisible: false,
        };
    },
    async mounted() {
    },
    methods: {
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onRefresh() {
            this.onSearch()
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getList();
        },
        async getList() {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter,
            };
            this.listLoading = true;

            const res = await getPageList(params);

            this.listLoading = false;
            this.total = res.data?.total
            this.ZTCKeyWordList = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        onImport1() {
            this.importTitle = "导入虚拟发货订单"
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            if (!this.importUseMonth) {
                this.$message({ message: "请先选择月份！", type: "warning" });
                return;
            }
            if (this.file == null) return this.$message.error('请上传文件')
            const form = new FormData();
            form.append("upfile", this.file);
            form.append("yearmonth", this.importUseMonth);
            this.importLoading = true
            let res = await ImportJingDongVirtualOrderAsync(form)
            this.importLoading = false
            if (res?.success) {
                this.$message.success('正在后台导入中,请稍后刷新界面查看....')
                this.importVisible = false
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>
