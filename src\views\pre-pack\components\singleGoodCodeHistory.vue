<template>
    <MyContainer>
        <vxetablebase :id="'singleGoodCodeHistory202408041853'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0;height: 400px;" :loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from 'dayjs'
import request from '@/utils/request'
const tableCols = [
    { sortable: 'custom', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', align: 'center', prop: 'startDate', label: '开始日期', formatter: (row) => row.startDate ? dayjs(row.startDate).format('YYYY-MM-DD') : '' },
    { sortable: 'custom', align: 'center', prop: 'endDate', label: '截止日期', formatter: (row) => row.endDate ? dayjs(row.endDate).format('YYYY-MM-DD') : '' },
    { sortable: 'custom', align: 'center', prop: 'confirmQty', label: '确认数量', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase,
    },
    props: {
        goodsCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                goodsCode: this.goodsCode
            },
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post('/api/verifyOrder/PrePackOperateConfirm/SingleGoods/Histories', this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
