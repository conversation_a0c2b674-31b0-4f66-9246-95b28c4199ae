<template>
    <MyContainer>
        <template #header>
            <div style="display: flex;flex-wrap: wrap; align-items:center;margin-bottom: 10px;">

                <div class="marginleft">
                    <el-date-picker v-model="queryEnum.archiveDate" type="date" placeholder="选择日期" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" :clearable="false"></el-date-picker>
                </div>
                <el-input class="marginleft" v-model="queryEnum.sku" placeholder="SKU编码" clearable style="width: 150px"
                    maxlength="100">
                </el-input>
                <el-input class="marginleft" v-model="queryEnum.skuName" placeholder="商品名称" clearable
                    style="width: 150px" maxlength="100">
                </el-input>
                <div style="margin-right:5px;margin-left: 50px;">
                    <span>美国可用库存数量区间：</span>
                    <el-input-number v-model="queryEnum.sellableMin" placeholder="最低" :min="0" :max="9999"
                        :precision="0" :controls="false" @blur="expNum('min')">
                    </el-input-number>
                    <span>-</span>
                    <el-input-number v-model="queryEnum.sellableMax" placeholder="最高" :min="0" :max="9999"
                        :precision="0" :controls="false" @blur="expNum('max')">
                    </el-input-number>
                </div>
                <el-button type="primary" @click="getlist('search')">查询</el-button>
                <el-button @click="getlist('reset')">重置</el-button>
                <el-button @click="getlist()">刷新</el-button>
                <el-button type="primary" @click="handleExport()"
                    v-if="checkPermission('overseasGodown_manage_export')">导出</el-button>
                <el-checkbox style="margin-left: 10px;" v-model="isHideZeroInventory"
                    @change="hideInventoryData">隐藏0库存数据</el-checkbox>

            </div>
        </template>

        <template>
            <vxetablebase :id="'overseasGodown20240719'" :tablekey="'overseasGodown20240719'" :tableData='datalist'
                :tableCols='tableCols' @sortchange='sortchange' :loading='listLoading' :border='true' :that="that"
                ref="vxetable" :showsummary='true' :summaryarry="summaryarry">
            </vxetablebase>
        </template>

        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getlist" />
        </template>

        <!-- 查看操作日志dialog -->
        <el-dialog title="Temu店铺改动数据" :visible.sync="packcostVisible" :center="false" width="80%" v-dialogDrag
            @close="getLogList('resetEnum', 'logAll')">
            <div class="dialogHeader">
                <el-input v-model="logEnum.sku" placeholder="SKU编码" clearable style="width: 150px" maxlength="100">
                </el-input>
                <el-input v-model="logEnum.shoopName" placeholder="店铺名称" clearable style="width: 150px" maxlength="100">
                </el-input>
                <div>
                    <span>操作日期：</span>
                    <el-date-picker style="width: 320px" v-model="logEnum.timerange" type="daterange"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']"
                        :picker-options="pickerOptions"></el-date-picker>
                </div>
                <div>
                    <span>修改后库存区间：</span>
                    <el-input-number v-model="logEnum.editNumMin" controls-position="right" placeholder="最低" :min="0"
                        :max="9999" :precision="0" :controls="true" @blur="expNum('logmin')">
                    </el-input-number>
                    <span> 至 </span>
                    <el-input-number v-model="logEnum.editNumMax" controls-position="right" placeholder="最高" :min="0"
                        :max="9999" :precision="0" :controls="true" @blur="expNum('logmax')">
                    </el-input-number>
                </div>
                <el-button type="primary" @click="getLogList('serve')">查询</el-button>
                <el-button @click="getLogList('reset')">重置</el-button>
            </div>
            <vxetablebase :id="'overseasGodown20240720'" :tablekey="'overseasGodown20240720'"
                :tableData='dialogDatalist' :tableCols='dialogTableColsAll' :loading='Loading' :border='true'
                :that="that" @sortchange="sortLogAllList" height="440px" ref="vxelogAlltable" :showsummary='false'
                :toolbarshow="false">
            </vxetablebase>
            <my-pagination ref="pageAll" :total="totalLog" @get-page="getLogList('logAll')" />
        </el-dialog>


        <el-dialog title="操作日志" :visible.sync="logVisible" :center="true" width="60%" v-dialogDrag
            @close="getLogList('resetEnum', 'log')">

            <vxetablebase :id="'overseasGodown20240812'" :tablekey="'overseasGodown20240812'"
                :tableData='dialogDatalist' :tableCols='dialogTableCols' :loading='Loading' :border='true' :that="that"
                @sortchange="sortLogList" height="440px" ref="vxelogtable" :showsummary='false' :toolbarshow="false">
            </vxetablebase>
            <my-pagination ref="pageLog" :total="totalLog" @get-page="getLogList('log')" />
        </el-dialog>

    </MyContainer>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import dayjs from 'dayjs';
import { deleteStock, exportStock, getBlackListConfigLogPageList, stockArchive } from '@/api/kj/stock.js';
import { skuBlackListCreate } from '@/api/kj/product.js';
import inputYunhan from "@/components/Comm/inputYunhan";
import { nextTick } from 'vue';
import { formatTime } from "@/utils";

const tableCols = [
    {
        istrue: true, prop: 'archive_date', label: '存档时间', sortable: 'custom', formatter: (row) => {
            return row.archive_date ? formatTime(row.archive_date, "YYYY-MM-DD") : "";
        },
    },
    { istrue: true, prop: 'image', label: '商品图片', type: 'images' },
    { istrue: true, prop: 'skuName', label: '商品名称', sortable: 'custom', },
    { istrue: true, sortable: 'custom', prop: 'sku', label: 'SKU编码', },
    { sortable: 'custom', istrue: true, prop: 'sellable', label: '美国可用库存', },
    { sortable: 'custom', istrue: true, prop: 'product_weight', label: '重量(KG)', tipmesg: 'ST佳速达，SR赤道，TA九方，ZI左海，WO环世 ' },
    { istrue: true, sortable: 'custom', prop: 'shipout_east_sellable', label: '佳速达美东可用库存', },
    { istrue: true, sortable: 'custom', prop: 'shipout_west_sellable', label: '佳速达美西可用库存', },
    { istrue: true, sortable: 'custom', prop: 'sogoodseller_sellable', label: '赤道可用库存', },
    { istrue: true, sortable: 'custom', prop: 'tofba_east_sellable', label: '九方美东可用库存', },
    { istrue: true, sortable: 'custom', prop: 'tofba_west_sellable', label: '九方美西可用库存', },
    { istrue: true, sortable: 'custom', prop: 'zuohai_east_sellable', label: '左海美东可用库存', },
    { istrue: true, sortable: 'custom', prop: 'zuohai_west_sellable', label: '左海美西可用库存', },
    { istrue: true, sortable: 'custom', prop: 'wwlcargo_east_sellable', label: '环世美东', },
    { istrue: true, sortable: 'custom', prop: 'wwlcargo_west_sellable', label: '环世美西', },
    { istrue: true, sortable: 'custom', prop: 'westernpost_east_sellable', label: '西邮美东', width: '80px', },
    { istrue: true, sortable: 'custom', prop: 'westernpost_west_sellable', label: '西邮美西', width: '80px', },
    { istrue: true, sortable: 'custom', prop: 'supplyPrice', label: '单品成本（元）', },
    { istrue: true, sortable: 'custom', prop: 'totalSupplyPrice', label: '总成本（元）', tipmesg: '总成本= 美国总库存*单品成本', },
    { istrue: true, sortable: 'custom', prop: 'skuBlack', label: '黑名单', },
    { istrue: true, sortable: 'custom', prop: 'skuWhite', label: '白名单', },
    // {
    //   istrue: true, sortable: 'custom', prop: 'skuBlack', label: '黑名单', type: 'switch', change: (row, that) => that.changeStatus(row, 1), align: 'center'
    // },
    // {
    //   istrue: true, sortable: 'custom', prop: 'skuWhite', label: '白名单', type: 'switch', change: (row, that) => that.changeStatus(row, 2), align: 'center'
    // },
];

const dialogTableColsAll = [
    { istrue: true, prop: 'extCode', label: 'SKU编码', width: '150', sortable: 'custom' },

    { istrue: true, prop: 'shoopName', label: '店铺名称', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'quantity', label: '预警值', width: '150', sortable: 'custom' },

    { istrue: true, prop: 'virtualStock', label: '修改库存前', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'edit_num', label: '修改库存后', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'editTime', label: '日期', width: '180', formatter: (row) => dayjs(row.editTime).format("YYYY-MM-DD HH:mm:ss"), sortable: 'custom' },
    { istrue: true, prop: 'operator', label: '操作人', width: '150', sortable: 'custom' },
]
const dialogTableCols = [
    { istrue: true, prop: 'createTime', label: '时间', formatter: (row) => dayjs(row.createTime).format("YYYY-MM-DD HH:mm:ss"), sortable: 'custom' },
    { istrue: true, prop: 'operationContent', label: '事件', sortable: 'custom' },
    { istrue: true, prop: 'createName', label: '创作人', sortable: 'custom' },
]

const dialogOverseasStoryTableCols = [
    { istrue: true, prop: 'yearMonthDay', label: '仓库名称', width: '120', },
    { istrue: true, prop: 'styleCode', label: '是否启用', type: 'switch', width: '150', change: (row, that) => that.onDeleteOverseasStory(row) },
    {
        istrue: true, type: 'button', label: '操作', width: '150', btnList: [{
            label: "删除",
           /*display:(row)=>{return true},display:true,*/ handle: (that, row) => that.onDeleteOverseasStory(row)
        },
        ]
    },
]

export default {
    name: 'overseasGodown',
    components: { vxetablebase, MyContainer, inputYunhan, },
    data() {
        return {
            that: this,
            tableCols: tableCols,
            dialogTableCols: dialogTableCols,
            dialogTableColsAll: dialogTableColsAll,
            listLoading: false,
            Loading: false,
            datalist: [{
            }],
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > dayjs(new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)).valueOf();
                },

            },
            totalLog: 0,
            dialogDatalist: [],
            queryEnum: {
                sku: '',
                skuName: '',
                orderBy: '',
                isAsc: true,
                sellableMin: 1,
                archiveDate: this.getPreviousDay()
            },
            logVisible: false,
            total: 0,
            packcostVisible: false,
            logEnum: {
                sku: '',
                orderBy: '',
                isAsc: true,
                skuName: '',
                timerange: [],
            },
            summaryarry: {},
            warringDialogNum: 9,
            warringDialogOpen: false,
            addOverseasStoryDialogOpen: false,

            dialogOverseasStorylist: [
            ],
            dialogOverseasStoryTableCols: dialogOverseasStoryTableCols,
            show: false,
            isHideZeroInventory: false
        };
    },
    async mounted() {
        this.isHideZeroInventory = true
        await this.getlist()
    },
    methods: {
        getPreviousDay() {
            const today = new Date();
            const previousDay = new Date(today);
            previousDay.setDate(today.getDate() - 1);
            return this.formatDate(previousDay);
        },
        formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },
        async handleExport() {
            let params = {
                //  pageSize:pager.pageSize,
                //  page:pager.currentPage,
                isQueryarchiveDate: true,
                ...this.queryEnum
            }
            if (params.sellableMin && params.sellableMax && params.sellableMin > params.sellableMax) {
                return this.$message.error('最小值不能大于最大值');
            }
            this.listLoading = true

            this.listLoading = true
            const { data } = await exportStock(params)
            this.listLoading = false
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel;charset=utf-8" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '海外仓-库存表' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        async onDelete(row) {
            this.$confirm('确认要删除这条数据吗？', '提示', {
                type: 'warning',
                center: true,
                customClass: 'tipBox'
            })
                .then(async () => {
                    const res = await deleteStock({ skus: [row.sku] })
                    if (res.isSuccess) {
                        this.getlist()

                    }
                })
                .catch(_ => { });
        },

        //查看日志
        async openLog(row) {
            this.logEnum.sku = row.sku
            this.logVisible = true
            await this.getLogList('log')
        },
        //查看操作日志
        async viewOperateLog() {
            this.packcostVisible = true
            await this.getLogList('logAll')
        },
        sortLogList({ order, prop }) {
            if (prop) {
                this.logEnum.orderBy = prop
                this.logEnum.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getLogList('log')

            }
        },
        sortLogAllList({ order, prop }) {
            if (prop) {
                this.logEnum.orderBy = prop
                this.logEnum.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getLogList('logAll')
            }
        },
        async getLogList(type, flag) {


            nextTick(async () => {
                var pager

                if (type == 'reset' || type == 'serve') {
                    this.$refs.pageAll.setPage(1)
                    pager = this.$refs.pageAll.getPager()

                }
                if (type == 'log') {
                    pager = this.$refs.pageLog.getPager()
                } else if (type == 'logAll') {
                    pager = this.$refs.pageAll.getPager()
                }
                let params = {
                    page: pager.currentPage,
                    pageSize: pager.pageSize,
                    //  ...this.logEnum,
                    isAsc: this.logEnum.isAsc,
                    orderBy: this.logEnum.orderBy,
                    sku: this.logEnum.sku
                    //  beginDt: this.logEnum.timerange ?this.logEnum.timerange[0]:'',
                    //  endDt:this.logEnum.timerange ? this.logEnum.timerange[1] : '',
                }
                if (params.editNumMin && params.editNumMax && params.editNumMin > params.editNumMax) {
                    return this.$message.error('最小值不能大于最大值');
                }
                const { data, total } = await getBlackListConfigLogPageList(params)
                this.totalLog = total
                this.dialogDatalist = data
            })

        },

        // 排序
        sortchange({ order, prop }) {
            if (prop) {
                this.queryEnum.orderBy = prop
                this.queryEnum.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getlist()
            }
        },
        async getlist(type) {
            if (type == 'search' || type == 'reset') {
                this.$refs.pager.setPage(1)
            }
            let pager = this.$refs.pager.getPager()
            if (type == 'reset') {
                this.queryEnum.sku = ''
                this.queryEnum.skuName = ''
                delete this.queryEnum.sellableMax
                delete this.queryEnum.sellableMin
            }

            let params = {
                pageSize: pager.pageSize,
                page: pager.currentPage,
                ...this.queryEnum
            }
            if (params.sellableMin && params.sellableMax && params.sellableMin > params.sellableMax) {
                return this.$message.error('最小值不能大于最大值');
            }
            this.listLoading = true

            let { data, total, summary } = await stockArchive(params)

            this.listLoading = false
            // data.forEach(item => {
            //     item.skuBlack = item.skuBlack === "1" ? true : false;
            // });
            // data.forEach(item => {
            //     item.skuWhite = item.skuWhite === "2" ? true : false;
            // });

            this.datalist = data
            this.summaryarry = summary
            this.total = total

            var date = dayjs().format("YYYY-MM-DD HH:mm:ss")
            if (this.datalist) {
                this.datalist.forEach((ite) => {
                    ite.evaluationsNum = date
                })
            }

        },
        warningNumOpenDialog() {
            this.warringDialogOpen = true
        },
        //预警值设置
        warringDialogNumSet() {
            let mode = {
                istrue: true, prop: 'sellable', label: '美国可用库存', width: '150px', type: 'html', formatter: (row) => {
                    return `<div style="font-weight:bold;font-size:small;color:${(row.sellable - 0 >= this.warringDialogNum ? "black" : "red")};text-align:center">${(row.sellable)}</div>`;
                }
            }
            this.tableCols[3] = mode
            this.tableCols = JSON.parse(JSON.stringify(this.tableCols))
            this.warringDialogOpen = false
        },
        //增加海外仓库
        addOverseasStory() {
            this.addOverseasStoryDialogOpen = true
        },
        onDeleteOverseasStory() {
            this.$confirm('确认要删除该仓库吗？', '提示', {
                type: 'warning',
                center: true,
            })
                .then(_ => {
                    done();
                })
                .catch(_ => { });
        },

        checkSort({ order, prop }) {
            if (prop) {
                this.logEnum.orderBy = prop
                this.logEnum.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getLogList()
            }
        },

        expNum(type) {
            let value
            if (type == 'logmin' || type == 'logmax') {
                value = type == 'logmin' ? this.logEnum.sellableMin : this.logEnum.sellableMax
            } else {
                value = type == 'min' ? this.queryEnum.sellableMin : this.queryEnum.sellableMax
            }
            if (!/^[0-9]\d*$/i.test((value))) {
                if (type == 'logmin') {
                    this.logEnum.sellableMin = undefined
                } else if (type == 'logmax') {
                    this.logEnum.sellableMax = undefined
                } else if (type == 'min') {
                    this.queryEnum.sellableMin = undefined
                } else {
                    this.queryEnum.sellableMax = undefined
                }
            }

        },
        async changeStatus(row, num) {

            this.$confirm('将改变按钮的监控状态，是否继续?', '提示', {
                confirmButtonText: '是', cancelButtonText: '否', type: 'warning'
            }).then(async () => {
                var params = {
                    sku: row.sku,
                    skuBlackType: num == 1 ? (row.skuBlack ? 1 : 0) : (row.skuWhite ? 2 : 0)
                }
                const res = await skuBlackListCreate(params);
                if (res.isSuccess) {
                    this.$message({ message: "操作成功", type: "success" });
                    await this.getlist()
                }
            }).catch(() => {
                row.isUpdate = !row.isUpdate;
                this.getlist()
            });
        },
        async hideInventoryData() {
            if (this.isHideZeroInventory) {
                this.queryEnum.sellableMin = 1;
            } else {
                this.queryEnum.sellableMin = undefined;
            }
            await this.getlist();
        },
    }
};
</script>

<style lang="scss" scoped>
.father {
    position: relative;

    .imageModalPos {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 999;
        overflow: hidden;
    }
}

.a {
    align-items: center;
    font-size: 15px
}

.marginleft {
    margin-right: 5px;
    margin-bottom: 5px;
}

.dialogHeader {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    margin: 20px 0;
}

.warringDialogContent {
    display: flex;
    justify-content: center;
    align-items: center;
}

.viewImageBox {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    height: 700px;
    overflow: auto;

    .viewImageBox_item {
        width: 300px;
        height: 300px;
        display: flex;
        flex-direction: column;
        margin-right: 10px;
        position: relative;

        .viewImageBox_item_fixed {
            position: absolute;
            top: 0;
            left: 0;
            width: 50px;
            height: 30px;
            background-color: red;
            color: #fff;
            text-align: center;
            line-height: 30px;
        }


        .viewImageBox_item_bottom {
            flex: 1;

            .viewImageBox_item_info {
                display: flex;

                .viewImageBox_item_info_left,
                .viewImageBox_item_info_right {
                    text-align: center;
                    height: 30px;
                    box-sizing: border-box;
                    border: 1px solid #ccc;
                    line-height: 30px;
                }

                .viewImageBox_item_info_left {
                    width: 80px;
                }

                .viewImageBox_item_info_right {
                    flex: 1;
                }
            }
        }
    }
}

.viewImageBox_item_img ::v-deep img {
    min-width: 300px !important;
    min-height: 220px !important;
    width: 300px !important;
    height: 220px !important;
}

.viewImageBox_item_img ::v-deep div {
    min-width: 300px !important;
    min-height: 220px !important;
    width: 300px !important;
    height: 220px !important;
}
</style>