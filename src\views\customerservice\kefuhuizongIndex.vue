<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" :before-leave="beforeLeave" style="height:94%;">
            <el-tab-pane label="平台店铺汇总" name="tab0" style="height: 100%;">
                <kefuhuizongplatform ref="kefuhuizongplatform" style="height: 100%;" @toSeeDtl1="toSeeDtl1"
                    @toSeeDtl2="toSeeDtl2" />
            </el-tab-pane>
            <el-tab-pane label="分区汇总" name="tab1" style="height: 100%;">
                <kefuhuizonggroup ref="kefuhuizonggroup" style="height: 100%;" @toSeeDtl1="toSeeDtl1" />
            </el-tab-pane>
            <el-tab-pane label="个人汇总" name="tab2" style="height: 100%;">
                <kefuhuizonguser ref="kefuhuizonguser" style="height: 100%;" @toSeeDtl1="toSeeDtl1" />
            </el-tab-pane>

            <el-tab-pane label="明细" name="tab3" style="height: 100%;">
                <kefuhuizongdtl ref="kefuhuizongdtl" style="height: 100%;" />
            </el-tab-pane>


        </el-tabs>
    </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";
import kefuhuizongplatform from '@/views/customerservice/kefuhuizongplatform'
import kefuhuizonggroup from '@/views/customerservice/kefuhuizonggroup'
import kefuhuizonguser from '@/views/customerservice/kefuhuizonguser'
import kefuhuizongdtl from '@/views/customerservice/kefuhuizongdtl'

export default {
    name: "kefuhuizongIndex",
    components: {
        MyContainer,
        kefuhuizongplatform, kefuhuizonggroup, kefuhuizonguser, kefuhuizongdtl
    },
    data() {
        return {
            that: this,
            pageLoading: '',
            filter: {
            },
            activeName: 'tab0',
        };
    },
    mounted() {
    },
    methods: {
        toSeeDtl1(params) {
            console.log(params);
            this.activeName = 'tab3'
            this.$refs.kefuhuizongdtl.loadData2(params)
        },
        toSeeDtl2(params) {
            console.log(params);
            this.activeName = 'tab2'
            this.$refs.kefuhuizonguser.loadData2(params)
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
