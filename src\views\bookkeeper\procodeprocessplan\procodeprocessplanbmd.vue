<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
            <div>
                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" clearable
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="创建开始日期"
                        end-placeholder="创建结束日期">
                    </el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-input v-model="filter.createdUserName" placeholder="创建人" style="width:90px;" maxlength="20"
                        clearable />
                </el-button>


                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-select v-model="filter.statusStr" placeholder="状态" style="width:90px;" clearable>
                        <el-option label="生效中" value="生效中" />
                        <el-option label="已失效" value="已失效" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-input v-model="filter.styleCode" placeholder="系列编码" style="width:150px;" maxlength="20"
                        clearable />
                </el-button>

                <el-button style="padding: 0;margin: 0;border: 0;">
                    <inputYunhan ref="productCode2" :inputt.sync="filter.proCode" v-model="filter.proCode"
                        class="publicCss" style="width:200px;" placeholder="宝贝ID" :clearable="true"
                        :clearabletext="true" :maxRows="300" :maxlength="6000" @callback="proCodeBack" title="宝贝ID">
                    </inputYunhan>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-select v-model="filter.groupIdList" style="width: 160px" size="mini" clearable placeholder="小组"
                        collapse-tags multiple filterable>
                        <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-select v-model="filter.userIdList" style="width: 160px" size="mini" clearable placeholder="专员"
                        collapse-tags multiple filterable>
                        <el-option v-for="item in directorList" :key="'zy' + item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-select v-model="filter.userId2List" style="width: 160px" size="mini" clearable placeholder="助理"
                        collapse-tags multiple filterable>
                        <el-option v-for="item in directorList" :key="'zl' + item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>

                <el-button type="primary" @click="onSearch">查询</el-button>
            </div>
            <div>
            <el-button type="primary" @click="onAddShow" :disabled="statusReadOnly">新增</el-button>

            <el-button type="primary" @click="onImportSyj" :disabled="statusReadOnly">导入</el-button>
            <el-button type="primary" @click="onImportSyjModel">下载导入模板</el-button>

            <el-button type="primary" @click="onDelete">批量删除</el-button>

            <el-button type="primary" @click="onExport">导出</el-button>
            <el-switch v-model="status" active-text="启用" inactive-text="禁用" @change="changeSwitch"
              v-if="checkPermission('NewPlanPermissionControl')" />
            </div>

        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :border="true"
            @sortchange='sortchange' @select='selectchange' :tableData='tableData' :tableCols='tableCols'
            :showsummary='true' :summaryarry='summaryarry' :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" v-loading="listLoading" :height="'100%'">
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>


        <el-dialog title="导入白名单宝贝ID" :visible.sync="dialogVisibleSyj" width="30%" :close-on-click-modal="false"
            v-dialogDrag>
            <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2" :on-change="onUploadChange2"
                :on-remove="onUploadRemove2">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                    :loading="dialogVisibleSyjLoad" @click="onSubmitupload2">上传</my-confirm-button>
            </el-upload>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="新增白名单宝贝ID" :visible.sync="dialogVisibleAdd" width="20%" :close-on-click-modal="false"
            v-dialogDrag>
            <el-input type="textarea" :rows="20" placeholder="多个宝贝ID请使用回车或英文逗号隔开" v-model="addProTxt">
            </el-input>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleAdd = false">关闭</el-button>
                <el-button type="primary" @click="onAddSave" :loading="dialogVisibleAddLoad">保存</el-button>
            </span>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import datepicker from '@/views/customerservice/datepicker';
import { pickerOptions } from '@/utils/tools';
import dayjs from 'dayjs';
import { formatTime } from "@/utils/tools";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getGroupKeyValue } from "@/api/operatemanage/base/product";
import { GetDirectorAllList } from "@/api/operatemanage/base/shop"
import { GetProcodeProcessPlanBmdList, DelProcodeProcessPlanBmd, SaveProcodeProcessPlanBmd, ImportProcodeProcessPlanBmd, ExportProcodeProcessPlanBmdList, UpdateProcodeProcessPlanByAnNiu, GetProcodeProcessPlanByAnNiu } from '@/api/bookkeeper/procodeprocessplan'
import inputYunhan from "@/components/Comm/inputYunhan";
import { trueDependencies } from 'mathjs';
import middlevue from "@/store/middle.js"
const tableCols = [
    { istrue: true, label: '', type: "checkbox", },
    { sortable: 'custom', align: 'center', prop: 'proCode', label: '宝贝ID', width: '150' },
    { sortable: 'custom', align: 'center', prop: 'styleCode', label: '系列编码', width: '150' },
    { sortable: 'custom', align: 'center', prop: 'status', label: '状态', width: '80', },
    { sortable: 'custom', align: 'center', prop: 'groupId', label: '小组', width: '80', formatter: (row) => row.groupName },
    { sortable: 'custom', align: 'center', prop: 'userId', label: '专员', width: '80', formatter: (row) => row.userName },
    { sortable: 'custom', align: 'center', prop: 'userId2', label: '助理', width: '80', formatter: (row) => row.userId2Name },
    { sortable: 'custom', width: '120', align: 'center', prop: 'loseDate', label: '失效日期', width: '150', formatter: (row) => formatTime(row.loseDate, 'YYYY-MM-DD') },
    { sortable: 'custom', width: '150', align: 'center', prop: 'createdTime', label: '创建时间', width: '150', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'createdUserName', label: '创建人', width: '80', },
];
export default {
    name: "procodeprocessplanbmd",
    components: {
        MyContainer, MyConfirmButton, datepicker, vxetablebase, inputYunhan
    },
    data() {
        return {
            status: true,
            statusReadOnly: false,
            that: this,
            pickerOptions,
            filter: {
                timerange: [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
                createdStartDate: null,
                createdEndDate: null,
                timerange2: [],
                curStartDate: null,
                curEndDate: null,
                timerange3: [],
                startDate: null,
                endDate: null,
                status: null,
                processPlanName: null,
            },
            pager: { OrderBy: "createdTime", IsAsc: false },
            tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            summaryarry: {},

            groupList: [],
            directorList: [],

            dialogVisibleSyj: false,
            dialogVisibleSyjLoad: false,
            fileList: [],

            dialogVisibleAdd: false,
            dialogVisibleAddLoad: false,
            addProTxt: "",
            addProList: [],

        }
    },
    async mounted() {
        this.getStatus();
        middlevue.$on('BookKeeper_ProcodeProcessPlan_HeiBaiImport', async (data) => {
          let res = data;
          this.getStatus();
        })
        await this.onSearch();

        const res = await getGroupKeyValue({});
        this.groupList = res.data;

        const res2 = await GetDirectorAllList();
        this.directorList = res2.data.map(item => {
            return { value: item.key, label: item.value };
        });
    },
    computed: {
    },
    methods: {
        async getStatus() {
          const { data, success } = await GetProcodeProcessPlanByAnNiu({ typeData: 2 });
          if (success) {
            this.statusReadOnly = !data.status;
            this.status = data.status;
          }
        },
        changeSwitch(val) {
          this.$confirm(`确定要${ val ? '启用' : '禁用'}吗？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async () => {
            let res = await UpdateProcodeProcessPlanByAnNiu({ status: val, typeData: 2 });
            this.statusReadOnly = !val;
          }).catch(() => {
            this.status = !val;
          });
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getParam() {
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            const res = await GetProcodeProcessPlanBmdList(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
                this.summaryarry = res.data.summary;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onDelete() {
            if (!this.selids || this.selids.length <= 0) {
                this.$message.warning('请至少勾选一行')
                return;
            }
            this.$confirm('确定要删除吗？', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await DelProcodeProcessPlanBmd(this.selids);
                if (res?.success == true) {
                    this.$message.success('删除成功')
                    this.sels = [];
                    this.selids = [];
                    await this.onSearch();
                }
            }).catch(() => {
            });
        },
        proCodeBack(val) {
            this.filter.proCode = val;
        },
        async onExport() {
            let param = this.getParam();
            this.listLoading = true
            const res = await ExportProcodeProcessPlanBmdList(param)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '产品处理方案白名单_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },

        onImportSyjModel() {
            window.open("/static/excel/book/产品处理方案白名单宝贝.xlsx", "_blank");
        },
        async onImportSyj() {
            this.dialogVisibleSyj = true;
            this.dialogVisibleSyjLoad = false;
        },
        async onUploadChange2(file, fileList) {
            this.fileList = fileList;
        },
        async onUploadRemove2(file, fileList) {
            this.fileList = [];
        },
        async uploadFile2(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            const form = new FormData();
            form.append("upfile", item.file);
            this.dialogVisibleSyjLoad = true;
            const res = await ImportProcodeProcessPlanBmd(form);
            this.dialogVisibleSyjLoad = false;
            if (res?.success) {
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
                this.dialogVisibleSyj = false;
            }
        },
        async uploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        async onSubmitupload2() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload2.submit();
        },

        onAddShow() {
            this.addProTxt = "";
            this.addProList = [];
            this.dialogVisibleAdd = true;
            this.dialogVisibleAddLoad = false;
        },
        async onAddSave() {
            if (!this.addProTxt || this.addProTxt.length <= 0) {
                this.$message({ message: "请选填写宝贝ID", type: "warning" });
            }
            let txts = this.addProTxt.split(/[\n,，]/);
            if (!txts || txts.length <= 0) {
                this.$message({ message: "解析失败，请重试", type: "warning" });
                return;
            }
            this.addProList = txts.map((txt) => ({
                proCode: txt.trim(),
                id: 0
            }));
            if (!this.addProList || this.addProList.length <= 0) {
                this.$message({ message: "请选填写宝贝ID", type: "warning" });
            }
            this.dialogVisibleAddLoad = true;
            const res = await SaveProcodeProcessPlanBmd(this.addProList);
            this.dialogVisibleAddLoad = false;
            if (res?.success) {
                this.$message({ message: '新增成功', type: "success" });
                this.dialogVisibleAdd = false;
                await this.onSearch();
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}
</style>
