<template>
    <my-container v-loading="pageLoading">
     
         <ces-table :id="'OrderDeductZrApplySumList20230514100001'" 
            :isIndex='true' 
            :isSelectColumnCols="false"
            ref="xTable" :showsummary="true"
            :summaryarry="summaryarry"
            :that='that' :loading="listLoading"
            :tableData='tbdatalist' :tableCols='calcTableCols'    
            >         
            <template slot="extentbtn">
              
             
                <el-button style="padding: 0;margin: 0;border:none;">
                    统计维度
                        <!-- groupProps:["ApplyDate","OrgZrDept","ApplyUserName","NewZrDept","NewMemberName","FirstAuditUserName","AuditUserName"],                       -->
                        <el-checkbox-group v-model="groupProps" @change="groupPropsChange">
                            <el-checkbox label="ApplyDate">申诉日期</el-checkbox>
                            <el-checkbox label="OrgZrDept">申诉部门</el-checkbox>
                            <el-checkbox label="ApplyUserName">申诉人</el-checkbox>
                            <el-checkbox label="NewZrDept" >新部门</el-checkbox>
                            <el-checkbox label="NewMemberName" >新责任人</el-checkbox>
                            <el-checkbox label="FirstAuditUserName" >初审人</el-checkbox>
                            <el-checkbox label="AuditUserName" >终审人</el-checkbox>
                        </el-checkbox-group>
                    </el-button>
            </template>  
        </ces-table>

        <!--分页-->
        <!-- <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
        </template>
        -->

    </my-container>
</template>
<script>  

    
    import { SummaryZrAppeal} from "@/api/order/orderdeductmoney"
    import dayjs from "dayjs";
    import cesTable from "@/components/Table/table.vue";
    import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";

    import { formatmoney, formatPercen, getUrlParam, platformlist, formatPlatform, formatTime, setStore, getStore, formatLinkProCode,formatNoLink } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";


   

    const tableCols = [    
        { istrue: true, prop: 'applyDate', align:'center',label: '申诉日期',minwidth:'100', sortable: true , formatter: (row) => formatTime(row.applyDate, "YYYY-MM-DD") },
        { istrue: true, prop: 'orgZrDept', align:'center',label: '申诉部门',minwidth:'80', sortable: true },
        { istrue: true, prop: 'applyUserName',align:'center', label: '申诉人',minwidth:'80', sortable: true },        
        { istrue: true, prop: 'newZrDept', align:'center',label: '新部门', minwidth: '80', sortable: true },       
        { istrue: true, prop: 'newMemberName',align:'center', label: '新责任人', minwidth: '80', sortable: true },
        { istrue: true, prop: 'firstAuditUserName', align:'center',label: '初审人', minwidth: '80', sortable: true },
        { istrue: true, prop: 'auditUserName', align:'center',label: '终审人', minwidth: '80', sortable: true },

        { istrue: true, prop: 'applyCount',align:'center', label: '申诉次数', minwidth: '80', sortable:true },

        { istrue: true, prop: 'firstRejectCount',align:'center', label: '初审拒绝次数', minwidth: '80', sortable:true },
        { istrue: true, prop: 'firstPassCount',align:'center', label: '初审通过次数', minwidth: '80', sortable:true },
        { istrue: true, prop: 'firstRejectRate',align:'center', label: '初审拒绝率', minwidth: '80', sortable:true, formatter: (row) => row.firstRejectRate.toString()+' %' },

        { istrue: true, prop: 'lastRejectCount',align:'center', label: '终审拒绝次数', minwidth: '80', sortable:true },
        { istrue: true, prop: 'lastPassCount',align:'center', label: '终审通过次数', minwidth: '80', sortable:true },
        { istrue: true, prop: 'lastRejectRate',align:'center', label: '终审拒绝率', minwidth: '80', sortable:true , formatter: (row) => row.lastRejectRate.toString()+' %' },
    ];   
   

    export default {
        name: "OrderDeductZrApplySumList",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable,vxetablebase },
        props:{
            filter:{                
            }
        },
        data() {
            return {
                that: this,               
                summaryarry:{},
                groupProps:["ApplyDate","OrgZrDept","ApplyUserName"],                           
                  
                tbdatalist: [],
                tableCols: tableCols,
                total: 0,               
                pager: { OrderBy: "", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
              
                curRow: {},   

            };
        },
        computed:{
            calcTableCols(){
                var rltCols=this.tableCols.filter(x=> { 
                    if(x.prop=="applyCount" 
                    || x.prop=="firstRejectCount" 
                    || x.prop=="firstPassCount"
                    || x.prop=="firstRejectRate"
                    || x.prop=="lastRejectCount"
                    || x.prop=="lastPassCount"
                    || x.prop=="lastRejectRate")
                        return true;
                    
                    if(this.groupProps!=null && this.groupProps.length>0){
                        let tempP=this.groupProps.find(y=> y.toUpperCase()== x.prop.toUpperCase());
                        if(tempP )
                            return true;
                    }

                    return false;
                });

                console.log(rltCols);
                return rltCols;
            }
        },
        async mounted() {         
        },
        methods: {             
            groupPropsChange(){     
                this.$nextTick(()=>{
                    //this.$refs["xTable"].$refs.xTable.resetColumn(true);
                    //this.$refs["xTable"].$refs.xTable.recalculate();
                    this.onSearch();
                });     
            }  ,
           
            
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                if(!this.groupProps || this.groupProps.length<1){
                    this.$message.error('请选择统计维度');
                    return false;
                }


                //this.$refs.pager.setPage(1);
                this.gettbdatalist1();
            },
            async gettbdatalist1() {
               
                const para = { ...this.filter ,groupProps:this.groupProps};
                //var pager = this.$refs.pager.getPager();
                const params = {
                    //...pager,
                    ...this.pager,
                    ...para,
                };    

                this.listLoading = true;
                const res = await SummaryZrAppeal(params);

                this.listLoading = false;

                this.total = res.data.total
                this.tbdatalist = res.data.list;
                this.summaryarry=res.data.summary;

            },
        },
    };
</script>

