<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="拣货跳过时间">
          <el-date-picker style="width: 230px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
            :picker-options="pickerOptions"></el-date-picker>
        </el-form-item>
        <el-form-item label="">
          <el-select v-model="filter.replenishmentType" clearable placeholder="补货类型"
            style="width: 130px;margin-left: 5px;">
            <el-option label="补货上架" value="4"></el-option>
            <el-option label="二次补货" value="5"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
    <el-tabs v-model="activeName" style="height: calc(100% - 40px);">
      <el-tab-pane label="拣货人跳过次数统计" name="tab1" style="height: 100%;">
        <useralysis :filter="filter" ref="useralysis" />
      </el-tab-pane>
      <el-tab-pane label="补货次数补货平均时长统计" name="tab2" style="height: 100%;" :lazy="true">
        <countalysis :filter="filter" ref="countalysis" />
      </el-tab-pane>
      <el-tab-pane label="各仓补货平均时长" name="tab3" style="height: 100%;" :lazy="true">
        <storealysis :filter="filter" ref="storealysis" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import useralysis from "@/views/storehouse/secondreplenishment/useralysis.vue";
import storealysis from "@/views/storehouse/secondreplenishment/storealysis.vue";
import countalysis from "@/views/storehouse/secondreplenishment/countalysis.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
  name: "YunHanSecondReplenishmentReportAnalysis",
  components: {
    MyContainer, useralysis, countalysis,storealysis
  },
  data() {
    return {
      filter: {
        startTime: null,
        endTime: null,
        timerange: [startDate, endDate]
      },
      activeName: 'tab1',
      that: this,
      pageLoading: false,
      importNumber: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      }
    };
  },
  methods: {
    onSearch() {
      if (this.activeName == "tab1") {
        this.$refs.useralysis.onSearch();
      } else if (this.activeName == "tab2") {
        this.$refs.countalysis.onSearch();
      } else if (this.activeName == "tab3") {
        this.$refs.storealysis.onSearch();
      }
    }
  }
}

</script>