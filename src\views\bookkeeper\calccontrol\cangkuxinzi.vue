<template>
    <container>
        <template #header>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            @select='selectchange' :isSelection='false' :isSelectColumn='true' :tableData='list' :tableCols='tableCols'
            :tableHandles='tableHandles' :showsummary='false' :loading="listLoading" />
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>


        <el-dialog title="月报仓库薪资" :visible.sync="dialogVisibleSyj" width="30%" :close-on-click-modal="false"
            v-dialogDrag>
            <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2" :on-change="onUploadChange2"
                :on-remove="onUploadRemove2">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <my-confirm-button style="margin-left: 10px;" size="small" type="success" :loading="dialogLoadingSyj"
                    @click="onSubmitupload2">上传</my-confirm-button>
            </el-upload>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="出仓成本" :visible.sync="dialogVisibleCost" width="50%" :close-on-click-modal="false" v-dialogDrag>
            <div>
                <el-button type="primary" @click="onExportCost" :loading="dialogVisibleLoading">导出</el-button>
            </div>
            <div>
                <vxetablebase :id="'outboundTable202506111413'" :tablekey="'outboundTable202506111413'"
                    ref="tables" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
                    :tableData='lists' :tableCols='costTableCols' :isSelection="false" :isSelectColumn="false"
                    :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="costLoading"
                    :height="'500'">
                </vxetablebase>
            </div>
            <template #footer>
                <my-pagination ref="pagers" :total="totals" :checked-count="sel.length" @get-page="GetMonthOutboundCostPageList" />
            </template>
        </el-dialog>
    </container>
</template>
<script>
import { importMonthWareWages, } from '@/api/monthbookkeeper/import'
 import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { GetMonthWareWagesPageList, ExportMonthWareWagesList, GetMonthOutboundCostPageList, exportMonthOutboundCostRpt } from '@/api/monthbookkeeper/financialDetail'
import container from '@/components/my-container/noheader'
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
const tableCols = [
    { istrue: true, prop: 'yearMonth', label: '月份', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'wareName', label: '仓库', sortable: 'custom', width: '260' },
    { istrue: true, prop: 'wareWages', label: '仓库薪资', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'wareWages2', label: '参与分摊仓库薪资', sortable: 'custom', width: '160' },
    //以前这个字段是存出仓成本（运），20250416财务提需求改成了出仓成本，实际后台也从存出仓成本，只不过字段名还叫chuCangYunYing
    { istrue: true, prop: 'chuCangYunYing', label: '出仓成本', sortable: 'custom', width: '160' },
];
const costTableCols = [
    { istrue: true, prop: 'yearMonth', label: '月份', width: '100' },
    { istrue: true, prop: 'wareHouse', label: '主仓', width: '260' },
    { istrue: true, prop: 'wareHouseSub', label: '子仓', width: 'auto' },
    { istrue: true, prop: 'outWarehouseCostTotal', label: '出仓成本', width: '160' },
];
const tableHandles = [
    { label: "导入", handle: (that) => that.onImportShow() },
    { label: "导入模板", handle: (that) => that.onImportModel() },
    { label: "导出", handle: (that) => that.onExport() },
    { label: "主仓子仓设置", handle: (that) => that.onWarechild() },
    { label: "出仓成本", handle: (that) => that.onOutboundCost()},
]
export default {
    name: 'cangkuxinzi',
    components: { cesTable, container, MyConfirmButton, vxetablebase },
    props: {
        filter: {}
    },
    data() {
        return {
            that: this,
            list: [],
            lists: [],
            tableCols: tableCols,
            costTableCols: costTableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "wareWages", IsAsc: false },
            summaryarry: {},
            total: 0,
            totals: 0,
            sel: [],
            sels: [],
            selids: [],
            listLoading: false,
            pageLoading: false,
            dialogVisibleSyj: false,
            dialogLoadingSyj: false,
            costLoading: false,
            dialogVisibleCost: false,
            dialogVisibleLoading: false,
            fileList: [],
            improtForm: {
            },
        }
    },
    mounted() {

    },
    beforeUpdate() { },
    methods: {
        //导出
        async onExport() {
            let pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }

            if (!params.yearMonth) {
                this.$message({ message: "请选择月份", type: "warning" });
                return;
            }
            let loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            let res = await ExportMonthWareWagesList(params);
            loadingInstance.close();
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '月报仓库薪资_' + new Date().toLocaleString() + '.xlsx')
            aLink.click();
        },
        onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }

            if (!params.yearMonth) {
                this.$message({ message: "请选择月份", type: "warning" });
                return;
            }
            this.listLoading = true
            const res = await GetMonthWareWagesPageList(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res.data?.total ?? 0
            const data = res.data?.list
            data?.forEach(d => {
                d._loading = false
            })
            this.list = data
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },


        onImportModel() {
            window.open("/static/excel/book/月报仓库薪资导入.xlsx", "_blank");
        },
        async onImportShow() {
            this.dialogVisibleSyj = true;
        },
        async onUploadChange2(file, fileList) {
            this.fileList = fileList;
        },
        async onUploadRemove2(file, fileList) {
            this.fileList = [];
        },
        async uploadFile2(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.dialogLoadingSyj = true;
            const form = new FormData();
            form.append("upfile", item.file);
            const res = await importMonthWareWages(form);
            this.dialogLoadingSyj = false;
            if (res?.success) {
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
                this.dialogVisibleSyj = false;
            }
        },
        async uploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        async onSubmitupload2() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload2.submit();
        },
        onWarechild() {
            this.$showDialogform({
                path: `@/views/bookkeeper/calccontrol/warechild.vue`,
                title: '主仓子仓设置',
                args: {},
                height: '600px',
                width: '50%',
                callOk: this.afterSave
            });
        },
        async GetOutboundCost(){
            let pager = this.$refs.pagers.getPager()
            const params = { ...pager, ... this.filter }
            this.costLoading = true
            const res = await GetMonthOutboundCostPageList(params)
            this.costLoading = false
            if (!res?.success) return
            this.totals = res.data?.total ?? 0
            const data = res.data?.list
            data?.forEach(d => {
                d._loading = false
            })
            this.lists = data
            this.summaryarry = res.data?.summary
        },
        onOutboundCost(){
            this.$refs.pagers.setPage(1)
            this.GetOutboundCost();
            this.dialogVisibleCost = true;
        },
        async onExportCost(){
            let pager = this.$refs.pagers.getPager()
            const params = { ...pager, ... this.filter }
            this.dialogVisibleLoading = true
            let res = await exportMonthOutboundCostRpt(params);
            this.dialogVisibleLoading = false
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '月报仓库薪资_出仓成本_' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
    }
}
</script>
