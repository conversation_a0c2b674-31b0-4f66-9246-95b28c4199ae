<template>
  <MyContainer>
    <template #header>
      <div class="top">
         <el-input v-model.trim="ListInfo.combGoodsName" :maxlength="150" clearable placeholder="组合装商品名称" style="width:200px;margin-right: 5px;"/>
        <div style="margin-right: 5px;">
          <inputYunhan ref="productmailNumber" :inputt.sync="ListInfo.combGoodsCode" v-model="ListInfo.combGoodsCode" width="230px"
            placeholder="组合商品编码(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
            :maxlength="21000" @callback="combGoodsCodeCallback" title="组合商品编码">
          </inputYunhan>
        </div>
        <div style="margin-right: 5px;">
          <inputYunhan ref="productmailNumber" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode" width="230px"
            placeholder="商品编码(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
            :maxlength="21000" @callback="mailNumberCallback" title="商品编码">
          </inputYunhan>
        </div>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport" v-if="checkPermission('CombinationCodeImportPermissions')">导入</el-button>
        <el-button type="primary" @click="downExcel" v-if="checkPermission('CombinationCodeImportPermissions')">导入模板下载</el-button>
        <el-button type="primary" @click="onAddnewMethod" v-if="checkPermission('CombinationCodeAddEditPermissions')">新增</el-button>
        <el-button type="primary" @click="onexport" v-if="checkPermission('CombinationCodeExportPermissions')">导出</el-button>
      </div>
    </template>
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
      :height="'100%'">
      <template slot="right" v-if="checkPermission('CombinationCodeAddEditPermissions') || checkPermission('CombinationCodeDeletePermissions')">
        <vxe-column title="操作" width="120" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;align-items: center;">
              <el-button type="text" @click="onEditMethod(row)" v-if="checkPermission('CombinationCodeAddEditPermissions')">编辑</el-button>
              <my-confirm-button  type="delete" :loading="row._loading" @click="onDelete($index, row)" v-if="checkPermission('CombinationCodeDeletePermissions')" />
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="editName" :visible.sync="editdialogVisible" width="30%" v-dialogDrag>
      <div style="height: 160px;width: 100%;" v-loading="editloading">
        <el-form :model="editform" ref="editform" :rules="editrules" label-width="150px" class="demo-ruleForm">
          <el-form-item label="组合装商品名称" prop="combGoodsName">
            <el-input v-model="editform.combGoodsName" style="width: 80%;" maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="组合商品编码" prop="combGoodsCode">
            <el-input v-model="editform.combGoodsCode" style="width: 80%;"  maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="商品编码" prop="goodsCode">
            <el-input v-model="editform.goodsCode" style="width: 80%;"  maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="数量" prop="goodsCount">
            <el-input-number v-model="editform.goodsCount" style="width: 80%;"></el-input-number>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editdialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSaveMethod">确 定</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { downloadLink, pickerOptions, platformlist } from "@/utils/tools.js";
import { importBrandCombGoodsAsync, getBrandCombGoodsList, addorEditBrandCombGoods ,deleteBrandCombGoodsAsync,exportBrandCombGoods} from '@/api/bookkeeper/reportdayV2'
import MyConfirmButton from '@/components/my-confirm-button'
import inputYunhan from "@/components/Comm/inputYunhan";

const tableCols = [
  { sortable: 'custom', width: '300', align: 'center', prop: 'combGoodsName', label: '【昀晗-聚水潭】组合装商品名称', },
  { sortable: 'custom', width: '300', align: 'center', prop: 'combGoodsCode', label: '组合商品编码', },
  { sortable: 'custom', width: '300', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: '300', align: 'center', prop: 'goodsCount', label: '数量', },

]
export default {
  name: "particularlyExpressFee",
  components: {
    MyContainer, vxetablebase,MyConfirmButton,inputYunhan
  },
  data() {
    return {
      editloading: false,
      editform: {
        expressCompany: null,
        area: null,
        expressFee: null,
        id: 0,
      },
      editdialogVisible: false,
      editName: '新增',
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        proCode: null,
      },
      editrules: {
        combGoodsName: [{ required: true, message: '组合装商品名称', trigger: 'blur' }],
        combGoodsCode: [{ required: true, message: '组合商品编码', trigger: 'blur' }],
        goodsCode: [{ required: true, message: '商品编码', trigger: 'blur' }],
        goodsCount: [{ required: true, message: '数量', trigger: 'blur' }],
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      isExport:false,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    mailNumberCallback(val) {
      this.ListInfo.goodsCode = val
    },
    combGoodsCodeCallback(val) {
      this.ListInfo.combGoodsCode = val
    },
    downExcel(){
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250225/1894256861951787008.xlsx', '品牌组合编码导入模板.xlsx')
    },
    async onSaveMethod() {
      if (!this.editform.combGoodsName || !this.editform.combGoodsCode || !this.editform.goodsCode|| !this.editform.goodsCount) {
        this.$message({ message: "请填写完整信息", type: "warning" });
        return false;
      }
      this.editloading = true
      var res = await addorEditBrandCombGoods(this.editform)
      this.editloading = false
      if (res?.success) {
        this.$message({ message: "保存成功", type: "success" });
        this.editdialogVisible = false
        await this.getList()
      }
    },
    onAddnewMethod() {
      this.setEditForm('新增', {
        expressCompany: null,
        area: null,
        expressFee: null,
        id: 0
      });
    },
    onEditMethod(row) {
      this.setEditForm('编辑', row);
    },
    async onDelete(index, row) {
        row._loading = true
        const para = { id: row.id }
        const res = await deleteBrandCombGoodsAsync(para)

        row._loading = false

        if (!res?.success) {
          return
        }
        this.$message({
          message: this.$t('admin.deleteOk'),
          type: 'success'
        })
        await  this.getList()
      },
         //导出
     async onexport() {
            this.isExport = true


            await exportBrandCombGoods(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '品牌组合装商品编码' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
    setEditForm(editName, formData) {
      this.editName = editName;
      this.editform = {
        combGoodsName: formData.combGoodsName,
        combGoodsCode: formData.combGoodsCode,
        goodsCode: formData.goodsCode,
        goodsCount: formData.goodsCount,
        id: formData.id
      };
      this.editdialogVisible = true;
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importBrandCombGoodsAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getBrandCombGoodsList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 5px;
  }
}
</style>
