<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;border:none;">
                    <el-date-picker style="width: 260px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        :picker-options="pickerOptions" :clearable="false"></el-date-picker>
                </el-button>

                <el-button style="padding: 0;margin-left: 0;border:none;">
                    <inputYunhan :key="'3'" :width="'150px'" ref="childGoodsCode" v-model="filter.goodsCode"
                        placeholder="商品编码" :clearable="true" @callback="callbackGoodsCode" :inputt.sync="filter.goodsCode" title="商品编码"></inputYunhan>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;border:none;">
                    <el-input v-model.trim="filter.keywords" placeholder="关键字查询" maxLength="20" clearable>
                        <el-tooltip slot="suffix" effect="dark" content="模糊查询：款式编码、商品编码、商品名称。" placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;border:none;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button>

            </el-button-group>
        </template>
        <!--列表-->
        <vxetablebase :id="'WhStockSurplusFpRecordList230427174601'" :isIndex='true' @select='selectchange'
            :tableData='list' :tableCols='tableCols' :tablefixed='true' :loading='listLoading' :border='true' :that="that"
            ref="vxetable" @sortchange='sortchange' :checkbox-config="{ labelField: 'id', highlight: true, range: true }"
            @checkbox-range-end="callback"   :tablekey="'WhStockSurplusFpRecordList230427174601'">
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>


    </container>
</template>
<script>
import { Loading } from 'element-ui';
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import inputYunhan from "@/components/Comm/inputYunhan";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { PageWhStockSurplusFpRecord, } from "@/api/inventory/goodscodestock"
import { formatNoLink, formatSecondNewToHour } from "@/utils/tools";

const tableCols = [
    { istrue: true, prop: 'picture', label: '图片', width: '50', type: 'images', fixed: 'left' },
    { istrue: true, prop: 'styleCode', label: '系列编码', width: '130', sortable: 'custom', fixed: 'left' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '130', sortable: 'custom', fixed: 'left' },
    { istrue: true, prop: 'goodsName', label: '商品名称', minwidth: '140', sortable: 'custom' },

    { istrue: true, prop: 'publicQyt', label: '公有可用数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'usableQty', label: '个人可用数', width: '100', align: 'center', sortable: 'custom', type: 'clickLink', handle: (that, row) => that.showPersonQtyList(row.goodsCode) },
    { istrue: true, prop: 'syPubQyt', label: '剩余公用可用数', width: '110', sortable: 'custom' },

    { istrue: true, prop: 'fpQty', label: '待领取数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'rlQty', label: '已领取数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'wrlQty', label: '剩余领取数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'expireTime', label: '到期时限', width: '140', sortable: 'custom' },
    { istrue: true, prop: 'completeTime', label: '完成时间', width: '140', sortable: 'custom' },


];

const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

export default {
    name: 'WhStockSurplusFpRecordList',
    components: { container, MyConfirmButton, vxetablebase, inputYunhan },
    data() {
        return {
            that: this,
            activeName: 'tab1',
            activeNameNew: 'tab1',
            filter: {
                startDate: null,
                endDate: null,
                goodsCode: null,
                auditState: null,
                keywords: null,
                timerange: [startTime, endTime],
            },

            list: [],
            tableCols: tableCols,
            pager: { OrderBy: "id", IsAsc: false },
            total: 0,
            sels: [],
            selrows: [],
            chooseTags: [],

            listLoading: false,
            pageLoading: false,


            dialogVisible: false,
            buscharDialog: { visible: false, title: "", data: [] },
            profit3BuscharDialog: { visible: false, title: "", data: [] },
            pickerOptions: {
                onPick: ({ maxDate, minDate }) => {
                    this.selectDate = minDate.getTime()
                    if (maxDate) {
                        this.selectDate = ''
                    }
                },
                disabledDate: (time) => {
                    if (this.selectDate !== '') {
                        const one = 30 * 24 * 3600 * 1000
                        const minTime = this.selectDate - one
                        const maxTime = this.selectDate + one
                        return time.getTime() < minTime || time.getTime() > maxTime
                    }
                }
            },
            pickerOptionspie: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近半个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
        };
    },

    async mounted() {
        await this.onSearch();
    },

    methods: {
        //个人可用数弹窗
        async showPersonQtyList(goodsCode) {
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/goodscodestock/WhStockPersonUsableQtySumList.vue`,
                title: '个人可用数',
                autoTitle: false,
                args: { goodsCode: goodsCode },
                height: 700,
                width: '455px',
                //callOk: self.onSearch
            })
        },
        //批量申报
        async batchApply() {
            let self = this;
            if (!self.selrows || self.selrows.length <= 0) {
                this.$message({ message: "请勾选至少一行数据", type: "warning" });
                return;
            }

            let selData = self.selrows.map((item) => {
                let newItem = { ...item };

                return newItem;
            })


            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/goodscodestock/ApplyStockGoodsForm.vue`,
                title: '批量申报',
                autoTitle: false,
                args: { selRows: selData },
                height: 700,
                width: '80%',
                //callOk: self.onSearch
            })
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await PageWhStockSurplusFpRecord(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
        },

        async callbackGoodsCode(val) {
            // this.inputedit = true;
            this.filter.goodsCode = val;
            this.onSearch();
        },

        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },

        callback(val) {
            this.selrows = [...val];

            this.tablelist = [];
            this.tablelist = val;
            var goodsCode = val.map((item) => {
                return item.goodsCode;
            })
            this.chooseTags = goodsCode;
            console.log("goods返回值", this.chooseTags)
        },
        selectchange: function (rows, row) {

            //先把当前也的数据全部移除
            this.list.forEach(f => {
                let index = this.chooseTags.findIndex((v) => (v === f.goodsCode));
                if (index !== -1) {
                    this.chooseTags.splice(index, 1);
                    this.selrows.splice(index, 1);
                }
            });
            //把选中的添加
            rows.forEach(f => {
                let index = this.chooseTags.findIndex((v) => (v === f.goodsCode));
                if (index === -1) {
                    this.chooseTags.push(f.goodsCode);
                    this.selrows.push(f);
                    console.log("选中数据", this.selrows);
                }
            });

            ///
            let _this = this;
            if (rows.length > 0) {
                var a = [];
                rows.forEach(element => {
                    let b = _this.list.indexOf(element);
                    a.push(b + 1);
                });

                let d = _this.list.indexOf(row);

                var b = Math.min(...a)
                var c = Math.max(...a)

                a.push(d);
                if (d < b) {
                    var b = _this.list.indexOf(row);
                    var c = Math.max(...a)
                } else if (d > c) {
                    var b = Math.min(...a) - 1
                    var c = Math.max(...a)
                } else {
                    var b = Math.min(...a) - 1
                    var c = _this.list.indexOf(row) + 1;
                }

                let neww = [b, c];
                _this.selids = neww;
            }
            console.log('选择的数据', this.selids)
        }
    }
};
</script>

<style lang="scss" scoped></style>
