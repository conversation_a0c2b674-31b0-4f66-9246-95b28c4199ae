<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <el-tabs v-model="activeName" style="height:94%;">

    <el-tab-pane   label="京速推" name="tab1" style="height: 100%;">
        <jSpeedDrive :filter="Filter" ref="jSpeedDrive" style="height: 100%;"/>
    </el-tab-pane>

     <el-tab-pane    label="京东快车" name="tab2" style="height: 100%;">
        <jdExpress :filter="Filter" ref="jdExpress" style="height: 100%;"/>
    </el-tab-pane>

    <el-tab-pane    label="全站营销" name="tab3" style="height: 100%;">
      <WholeSiteMarketing :filter="Filter" ref="WholeSiteMarketing" style="height: 100%;"/>
  </el-tab-pane>
  <el-tab-pane label="京喜NNSKU" name="tab4" :lazy="true" style="height: 100%;">
    <JXNNSKU ref="JXNNSKU" style="height: 100%;"/>
  </el-tab-pane>
  <el-tab-pane label="京东自营入仓成本" name="tab5" :lazy="true" style="height: 100%;">
    <warehousingSelfJd ref="refwarehousingSelfJd" style="height: 100%;"/>
  </el-tab-pane>
  <el-tab-pane label="子商品编码期初期末" name="tab61" :lazy="true" style="height: 100%;">
    <jdSelfSkuPriceDayRpt ref="refjdSelfSkuPriceDayRpt" style="height: 100%;"/>
  </el-tab-pane>
  <el-tab-pane label="商品编码维护" name="tab6" :lazy="true" style="height: 100%;">
    <commodityMaintenance ref="refcommodityMaintenance" style="height: 100%;"/>
  </el-tab-pane>
  <el-tab-pane label="京东联盟扣款-正常扣费" name="tab7" :lazy="true" style="height: 100%;">
    <allianceDeductionJdNormal ref="refallianceDeductionJdNormal" style="height: 100%;"/>
  </el-tab-pane>
  <el-tab-pane label="京东联盟扣款-退货返款" name="tab8" :lazy="true" style="height: 100%;">
    <allianceDeductionJdRefund ref="refallianceDeductionJdRefund" style="height: 100%;"/>
  </el-tab-pane>
  <el-tab-pane label="SKU产品维护" name="tab9" :lazy="true" style="height: 100%;">
    <productMaintenanceSku ref="refproductMaintenanceSku" style="height: 100%;"/>
  </el-tab-pane>
  <el-tab-pane label="京东仓自营入仓出库" name="tab10" :lazy="true" style="height: 100%;">
    <JdSelfWarehousOut ref="refJdSelfWarehousOut" style="height: 100%;"/>
  </el-tab-pane>
  <el-tab-pane label="京东仓自营入仓退货数据" name="tab11" :lazy="true" style="height: 100%;">
    <JdSelfWarehousOutReturn ref="refJdSelfWarehousOutReturn" style="height: 100%;"/>
  </el-tab-pane>
  </el-tabs>
  </my-container >

 </template>
<script>
import MyContainer from "@/components/my-container";

 import jdExpress from '@/views/financial/yyfydayreport/jdExpress'

 import jSpeedDrive from '@/views/financial/yyfydayreport/jSpeedDrive'
 import JXNNSKU from '@/views/bookkeeper/reportday/JXNNSKU'

 import WholeSiteMarketing from '@/views/financial/yyfydayreport/WholeSiteMarketing'
 import warehousingSelfJd from "./operatingExpensesJd/warehousingSelfJd.vue";
 import commodityMaintenance from "./operatingExpensesJd/commodityMaintenance.vue";
 import allianceDeductionJdNormal from "./operatingExpensesJd/allianceDeductionJdNormal.vue";
 import allianceDeductionJdRefund from "./operatingExpensesJd/allianceDeductionJdRefund.vue";
 import productMaintenanceSku from "./operatingExpensesJd/productMaintenanceSku.vue";
 import jdSelfSkuPriceDayRpt from "./operatingExpensesJd/jdSelfSkuPriceDayRpt.vue";
 import JdSelfWarehousOut from "./operatingExpensesJd/JdSelfWarehousOut.vue";
 import JdSelfWarehousOutReturn from "./operatingExpensesJd/JdSelfWarehousOutReturn.vue";

 import checkPermission from '@/utils/permission'

export default {
  name: "Users",
  components: { MyContainer,jSpeedDrive,jdExpress,checkPermission,WholeSiteMarketing,JXNNSKU,warehousingSelfJd,commodityMaintenance,allianceDeductionJdNormal
    ,jdSelfSkuPriceDayRpt,allianceDeductionJdRefund,productMaintenanceSku,JdSelfWarehousOut,JdSelfWarehousOutReturn },
  data() {
    return {
      that:this,
      Filter: {
      },
      pageLoading:"",
      activeName:"tab1",
      shopList:[],
      userList:[],
      groupList:[],
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
    };
  },
  mounted() {






  },
  methods: {
async onSearch(){
  if (this.activeName=='tab1')
  this.$refs.jSpeedDrive.onSearch();
  if (this.activeName=='tab2')
this.$refs.jdExpress.onSearch();
if (this.activeName=='tab3')
this.$refs.WholeSiteMarketing.onSearch();
}


  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
