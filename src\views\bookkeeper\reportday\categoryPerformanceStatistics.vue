<template>
		  <container v-loading="pageLoading">
		    <template #header>


			<el-cascader
            v-model="filter.ParentIds"
            placeholder="请选择，支持搜索功能"
            :options="modules"
            :props="{ checkStrictly: true, value: 'id' }"
            filterable
			@change="onSearch"
            style="width: 300px"
			clearable
          />

		  <el-select filterable   @change="onSearch"  v-model="filter.platform" collapse-tags  placeholder="平台" style="width: 100px" clearable>
			<el-option key="淘系" label="淘系" :value="1"></el-option>
			<el-option key="淘工厂" label="淘工厂" :value="8"></el-option>
	      </el-select>
     
		  <el-select filterable @change="onSearch"  clearable v-model="filter.shopCode" placeholder="店铺" style="width: 150px">
			<el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"></el-option>
		  </el-select>

		 <el-select filterable  @change="onSearch"   v-model="filter.groupId" collapse-tags clearable placeholder="运营组" style="width: 120px">
		            <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"/>
		          </el-select>
		<el-select filterable @change="onSearch"  v-model="filter.operateSpecialUserId" collapse-tags clearable placeholder="运营专员" style="width: 120px">
		<el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"/>
		</el-select>
		<el-select filterable @change="onSearch"  v-model="filter.userId" collapse-tags clearable placeholder="运营助理" style="width: 120px">
            <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
		  <el-date-picker
		  @change="onSearch"
		   style="width: 280px"
		   v-model="filter.timeRange"
		   type="datetimerange"
		   format="yyyyMMdd"
		   value-format="yyyyMMdd"
		   range-separator="至"
		   start-placeholder="开始时间"
		   end-placeholder="结束时间"
		   :picker-options="pickerOptions1"
		 ></el-date-picker>
		      <el-row> </el-row>
		      <el-button-group>
		       
		       
		        <el-button  class="button2" type="info" @click="toCategories"> <span></span>
					<span></span>
					<span></span>
					<span></span>返回大类</el-button>
		       
		      </el-button-group>
		    </template>
		    
		    
		      <ces-table
		        ref="table"			
				:isSelectColumn='false'
		        :that="that"
		        :isIndex="false"
		        :tableData="listTree"
		        :tableCols="tableCols"
		        :tableHandles="tableHandles"
		        :loading="listLoading"
		        element-loading-text="拼命加载中"
		        element-loading-spinner="el-icon-loading"
		        element-loading-background="rgba(0, 0, 0, 0.8)"
		        @sortchange="sortchange"
		        :showsummary="true"
		        :summaryarry='summaryarry' 
		      />
		   
		
		    <template #footer>
		      <my-pagination
		        ref="pager"
		        :total="total"
		        :checked-count="sels.length"
		        @get-page="onSearch"
		      />
		    </template>
		
		
		<el-dialog title="趋势图" :visible.sync="dialogMapVisible.visible" width="80%">
		     <div>
		       <span>     
		          <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
		      </span>
		     </div>
		      <span slot="footer" class="dialog-footer">
		        <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
		      </span>
		    </el-dialog>
		  </container>
		</template>
		
		<script>
		import {getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
		import buschar from '@/components/Bus/buschar'
		import {getDirectorGroupList as getCategoryIdstDirectorGroupList,getDirectorList } from '@/api/operatemanage/base/shop'
		import { treeToList, listToTree, getTreeParents } from "@/utils";
		import cesTable from "@/components/Table/table.vue";
		import container from "@/components/my-container";
		import MyConfirmButton from "@/components/my-confirm-button";
		import platformservice from "@/views/operatemanage/base/platformservice";
		import { platformlist} from "@/utils/tools";
		import {
		  getCategoryLists
		} from "@/api/operatemanage/base/category";
		import {
		  getPlanningTypeDescList
		} from "@/api/operatemanage/base/businessopportunity";
		
		
		import {getCategoryPerformanceStatistics as getlist,getCategoryPSChart} from '@/api/bookkeeper/reportday'
		
		const tableCols = [
		    {
		    istrue: true,
		    sortable: "custom",
		    prop: "productCategoryName",
		    label: "类目",
		    width: "150",
		    type:"click",
		    handle: (that, row) => that.nextCategoryData(row),
		  },

		
		  {
		    istrue: true,
		    sortable: "custom",
		    prop: "payAmont",
		    label: "付款金额",
		    width: "100",
		  },
		  {
		    istrue: true,
		    sortable: "custom",
		    prop: "saleAmont",
		    label: "销售金额",
		    width: "100",
		   
		   
		  },
		  {
		    istrue: true,
		    sortable: "custom",
		    prop: "saleCost",
		    label: "成本",
		    width: "110",
		  },
		  { istrue: true, prop: "profit1", label: "毛利", width: "110" ,sortable: "custom", },
		  { istrue: true, prop: "profit1Rate", label: "毛利率", width: "100",formatter:(row)=> !row.profit1Rate?" ": (row.profit1Rate*100).toFixed(2)+'%'},
		  {
		    istrue: true,
		    sortable: "custom",
		    prop: "alladv",
		    label: "广告费",
		    width: "100",
		  },
		  {
		    istrue: true,
		    sortable: "custom",
		    prop: "advratio",
		    label: "广告占比",
		    width: "100"
		    ,formatter:(row)=> !row.advratio?" ": (row.advratio*100).toFixed(2)+'%'
		  },
		  {
		    istrue: true,
			sortable: "custom",
		    prop: "profit3",
		    label: "毛三利润",
		    width: "130",
		  },
		  {
		    istrue: true,
		    sortable: "custom",
		    prop: "profit3Rate",
		    label: "毛三利润率",
		    width: "130"
		     ,formatter:(row)=> !row.profit3Rate?" ": (row.profit3Rate*100).toFixed(2)+'%'
		  },
		  {
		    istrue: true,
		    sortable: "custom",
		    prop: "profit4",
		    label: "净利润",
		    width: "100",
		  },
		   {
		    istrue: true,
			sortable: "custom",
		    prop: "profit4Rate",
		    label: "净利率",
		    width: "100"
		    ,formatter:(row)=> !row.profit4Rate?" ": (row.profit4Rate*100).toFixed(2)+'%'
		  },
		//    {istrue:true,display:true,label:'趋势图', style:"color:red;cursor:pointer;",width:70,formatter:(row)=>'趋势图', type:'click',handle:(that,row)=>that.showchart(row)}, 
		   
		];
		const tableHandles1 = [
		  
		];
		export default {
		  name: "Api",
		  components: { cesTable, platformservice, container, MyConfirmButton,buschar},
		  data() {
		    return {
				
			  directorlist:[],
			  shopList:[],
			  viewTree: [],
			  datacount:[],
			  modules: [],
			  expandRowKeys: [],
		      that: this,
		      filter: {platform:1,ParentIds:[],ParentId:'',timeRange:[],startYearMonth:null,endYearMonth:null,groupId:'',pidNext:1,shopCode:null,operateSpecialUserId:null,userId :null,categorys:null},
		      filter2:{platform: 1},
		      platformlist: platformlist,
		      listTree: [],
		      grouplist:[],
		      listLoading: false,
		      sels: [], // 列表选中列
		      tableCols: tableCols,
		      tableHandles: tableHandles1,
		      addDialogFormVisible: false,
		      editFormVisible: false, // 编辑界面是否显示
		      editLoading: false,
		      summaryarry:{},
		      html: "",
		      htmlvisible: false,
		      picUrls: [],
		      allinfo: { data: {} },
		      propertiesTable: [],
		      skuTable: [],
		      categoryList: [],
		      pids:[],
		      layercategoryList: [],
		      dialogMapVisible:{visible:false,title:"",data:[]},
		      selectedcategory: "",
		      modules: [],
		      endTime: "",
		      planningTypeDescList:[],
		      pid:null,
		      lastpid:null,
		      filterUpdateTime: null,
		      filterEndTime: null,
		      syncManagerLoading: false,
		      syncFinancialLoading: false,
		      syncOrderLoading: false,
		      deleteLoading: false,
		      total: 0,
    
		      pager: { OrderBy: "payAmont", IsAsc: false },
		      pageLoading: false,
		      platformserviceVisible: false,
		      showgrid: false,
	 
		      selectrootpath:'',
		      selectlayerdeep:1,
		
		      importFilte: { platform: "" },
		       pickerOptions1: {
		          shortcuts: [{
		            text: '7天内',
		            onClick(picker) {     
		              const yestoday=new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
		              const end = new Date(new Date(new Date(new Date().getTime() + 3600 * 1000 * 24).toLocaleDateString()).getTime());
		              const start = new Date(new Date(yestoday.toLocaleDateString()).getTime()- 3600 * 1000 * 24 * 6);
		              //start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
		              picker.$emit('pick', [start, end]);
		            }
		          }, {
		            text: '昨天',
		            onClick(picker) {
		              const yestoday=new Date(new Date().getTime() - 3600 * 1000 * 24 );
		              const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
		              const start = new Date(new Date(new Date().toLocaleDateString()).getTime());
		              start.setTime(start.getTime() - 3600 * 1000 * 24);
		              picker.$emit('pick', [start, end]);
		            }
		          }, {
		            text: '今天',
		            onClick(picker) {
		              const end = new Date(new Date(new Date(new Date().getTime() + 3600 * 1000 * 24).toLocaleDateString()).getTime());
		              const start = new Date(new Date(new Date().toLocaleDateString()).getTime());
		              start.setTime(start);
		              picker.$emit('pick', [start, end]);
		            }
		          }]}
		    };
		  },
		
		  async mounted() {
		    await this.getcategorydata();
		    await this.init()
		      this.onSearch();
			  await this.onGetList();    
		    // this.planningTypeDescList=(await getPlanningTypeDescList()).data
		  },
		  async created() { 
			await this.getShopList();
			
		  },
		  methods: {
		  async	nextCategoryData(row){
              console.log("aaaaaaaaididididi",row.id)

               this.filter.parentId=row.id;
               this.filter.pidNext=this.filter.pidNext+1;
			   this.queryList();

		  },
			//店铺
			async getShopList(){
				const res1 = await getAllShopList({platforms:[1,9]});
				this.shopList=[];
					res1.data?.forEach(f => {
					if(f.isCalcSettlement&&f.shopCode)
						this.shopList.push(f);
					});
					
				var res3= await getDirectorList();
				this.directorlist = res3.data?.map(item => {return { value: item.key, label: item.value };}); 
					
    },
			//获取类目级联选择器
			async onGetList() {
				this.listLoading = true
				const res = await getCategoryLists(this.filter2)
				this.datacount=res;
				this.listLoading = false
				if (!res?.success) {
					return
				}
				const list = _.cloneDeep(res.data.data)
				const keys = list.filter(l => l.parentId === 0).map(l => l.id + '')
				this.expandRowKeys = keys

				this.modules = listToTree(_.cloneDeep(list), {
					id: 0,
					parentId: 0,
					label: '请选择'
				})

				list.forEach(l => {
					l._loading = false
				})

				const tree = listToTree(list)
				this.sels = []
				this.viewTree = tree

				
				},



		    //获取类目父ID
		     async GetcategoryPid(name,id){
		          this.pid=id;
		          console.log("获取类目父id",this.pid);
		          this.queryList();
		          this.getcategorydata();
		     },
		    //趋势图
		    async showchart(row){
		     
		       this.filter.startYearMonth =null;
		      this.filter.endYearMonth =null;
		      if (this.filter.timeRange) {
		        this.filter.startYearMonth = this.filter.timeRange[0];
		        this.filter.endYearMonth = this.filter.timeRange[1];
		      }  
		           var params = {startYearMonth:this.filter.startYearMonth,enendYearMonthdSdate:this.filter.endYearMonthdSdate,groupId:row.groupId,platform:this.filter.platform,productCategoryName:row.productCategoryName}
		           
		      let that = this;
		      
		      const res = await getCategoryPSChart(params).then(res=>{                    
		          that.dialogMapVisible.visible=true;
		          that.dialogMapVisible.data=res.data
		          that.dialogMapVisible.title=res.data.legend[0]
		      })
		      this.dialogMapVisible.visible=true
		   },
		   datetostr(date) {
		      var y = date.getFullYear();
		      var m = ("0" + (date.getMonth() + 1)).slice(-2);
		      var d = ("0" + date.getDate()).slice(-2);
		      return y + "" + m + "" + d;
		    },
		    async init(){
		        var date1 = new Date(); date1.setDate(date1.getDate()-10);
		        var date2 = new Date(); date2.setDate(date2.getDate()-1);
		        this.filter.timeRange=[];
		        this.filter.timeRange[0]=this.datetostr(date1);
		        this.filter.timeRange[1]=this.datetostr(date2);
		      },
		    
		    toCategories(){
		      this.filter.pidNext=1;
			  this.filter.parentId='';
		      this.queryList();
		    },
		    
		
		    sortchange(column) {
		      if (!column.order) this.pager = {};
		      else
		        this.pager = {
		          OrderBy: column.prop,
		          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
		        };
		      this.$refs.pager.setPage(1)
		      this.onSearch();
		    },
		    
		async  getcategorydata(){
		
		
		
		     
		    
		    var res2= await getCategoryIdstDirectorGroupList();
		    this.grouplist = res2.data?.map(item => {return { value: item.key, label: item.value };}); 
		    this.pid=null;
		},
		    
		
		    onSearch() {
		      this.queryList();
		    },
		    async queryList() {
		       this.listLoading = true;
		      this.filter.startTime =null;
		      this.filter.endTime =null;
			  this.filter.ParentId=null;
			 
		      if (this.filter.timeRange) {
		        this.filter.startYearMonth = this.filter.timeRange[0];
		        this.filter.endYearMonth = this.filter.timeRange[1];
		      }
		      this.filter.ParentId=this.pid;
		      this.lastpid=this.pid;
		      var pager = this.$refs.pager.getPager();
		      var params = {
		        ...pager,
				...this.pager,
		        OrderBy: this.pager.OrderBy,
		        IsAsc: this.pager.IsAsc,
		        ...this.filter
		      };
			 params.ParentIds=this.filter.ParentIds.join();
		      var that=this;
		      const res = await getlist(params);
		     this.listLoading = false;
		      that.listTree = res.data.list;
		      that.total = res.data.total;
		      that.summaryarry=res.data?.summary;
		    },
		    
		  },
		};
		</script>
		
		<style scoped >
		.small-img {
		  width: 100px;
		  height: 100px;
		}
		.big-img {
		  width: 200px;
		  height: 200px;
		}
		
		ul {
		  margin: 0;
		  padding: 0;
		  list-style: none;
		}
		.table {
		  display: table;
		  border-collapse: collapse;
		  /* border: 1px solid #ccc; */
		}
		.table-caption {
		  display: table-caption;
		  margin: 0;
		  padding-top: 10px;
		  font-size: 16px;
		}
		.table-column-group {
		  display: table-column-group;
		}
		.table-column {
		  display: table-column;
		  width: 20%;
		  min-width: 150px;
		}
		.table-row-group {
		  display: table-row-group;
		}
		.table-row {
		  display: table-row;
		}
		.table-row-group .table-row:hover,
		.table-footer-group .table-row:hover {
		  background: #f6f6f6;
		}
		.table-cell {
		  display: table-cell;
		  padding: 0 5px;
		  border: 1px solid #ccc;
		}
		.table-header-group {
		  display: table-header-group;
		  background: #eee;
		  font-weight: bold;
		}
		.table-footer-group {
		  display: table-footer-group;
		}
.button2 {
 position: relative;
 padding: 1em 1.8em;
 outline: none;
 border: 1px solid white;
 background:#bfbfbf;
 color: #f6f6f6;
 text-transform: uppercase;
 letter-spacing: 2px;
 font-size: 10px;
 overflow: hidden;
 transition: 0.2s;
 border-radius: 20px;
 cursor: pointer;
 font-weight: bold;
}

.button2:hover {
 box-shadow: 0 0 10px #ae00ff, 0 0 25px #001eff, 0 0 50px #ae00ff;
 transition-delay: 0.6s;
}

.button2 span {
 position: absolute;
}

.button2 span:nth-child(1) {
 top: 0;
 left: -100%;
 width: 100%;
 height: 2px;
 background: linear-gradient(90deg, transparent, #ff0037b0);
}

.button2:hover span:nth-child(1) {
 left: 100%;
 transition: 0.7s;
}

.button2 span:nth-child(3) {
 bottom: 0;
 right: -100%;
 width: 100%;
 height: 2px;
 background: linear-gradient(90deg, transparent, #001eff);
}

.button2:hover span:nth-child(3) {
 right: 100%;
 transition: 0.7s;
 transition-delay: 0.35s;
}

.button2 span:nth-child(2) {
 top: -100%;
 right: 0;
 width: 2px;
 height: 100%;
 background: linear-gradient(180deg, transparent, #ff002bb9);
}

.button2:hover span:nth-child(2) {
 top: 100%;
 transition: 0.7s;
 transition-delay: 0.17s;
}

.button2 span:nth-child(4) {
 bottom: -100%;
 left: 0;
 width: 2px;
 height: 100%;
 background: linear-gradient(360deg, transparent, #001eff);
}

.button2:hover span:nth-child(4) {
 bottom: 100%;
 transition: 0.7s;
 transition-delay: 0.52s;
}

.button2:active {
 background: #af0026e3;
 background: linear-gradient(to top right, #ae00af, #001eff);
 color: #bfbfbf;
 box-shadow: 0 0 8px #ae00ff, 0 0 8px #001eff, 0 0 8px #ae00ff;
 transition: 0.1s;
}

.button2:active span:nth-child(1) 
span:nth-child(2) 
span:nth-child(2) 
span:nth-child(2) {
 transition: none;
 transition-delay: none;
}

		</style>