<template>
  <container>
    <ces-table ref="table" :that='that' :isIndex='true' @sortchange='sortchange' :isSelectColumn="false" @cellclick='cellclick'
         :hasexpand='true' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"
         :showsummary='true' :summaryarry='summaryarry'>
      </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

    <div class="imgDolg" v-show="imgPreview.show" @click.stop="imgPreview.show = false">
      <i class="el-icon-close" id="imgDolgClose" @click.stop="imgPreview.show = false"></i>
      <img @click.stop="imgPreview.show = true" :src="imgPreview.img" />
    </div>
    <el-drawer title="物流跟踪" :visible.sync="drawervisible" direction="rtl" :append-to-body="true">
      <logistics ref="logistics"></logistics>
    </el-drawer>
  </container>
</template>
<script>
import {formatTime,formatNoLink,} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container/noheader";
import logistics from '@/components/Comm/logistics'
import { getOrderWithholdRecord } from "@/api/order/orderdeductmoney"
const tableCols =[
      {istrue:true,prop:'createdTime',label:'创建时间', width:'80', width:'105',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'MM-DD HH:mm:ss')},
      {istrue:true,prop:'createdUserName',label:'上报人', width:'63',},
      {istrue:true,prop:'goodsCode',label:'商品编码',  width:'100',sortable:'custom',},
      {istrue:true,prop:'reason',label:'原因部门', width:'100',sortable:'custom'},
      {istrue:true,prop:'cgReMark',label:'采购备注', width:'auto',sortable:'custom',type:'editor'},
      {istrue:true,prop:'ckReMark',label:'仓库备注', width:'auto',sortable:'custom',type:'editor'},
      {istrue:true,prop:'yyReMark',label:'运营备注', width:'auto',sortable:'custom',type:'editor'},
      {istrue:true,prop:'kfReMark',label:'客服备注', width:'auto',sortable:'custom',type:'editor'},
      {istrue:true,prop:'cwReMark',label:'财务备注', width:'auto',sortable:'custom',type:'editor'}
     ];
const tableHandles=[ ];
export default {
  name: "goodscoderecord",
  components: {container,cesTable,logistics},
  props:{
     //filter:{goodsCode:"",buyNo:""},
  },
  data() {
    return {
      that:this,
      filter:{goodsCode:"",occurrenceTime:""},
      imgPreview:{img:"",show:false},
      list: [],
      oderDetailView:{},
      drawervisible:false,
      visiblepopover: false,
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [],
      fileList:[],
      listLoading: false,
      dialogVisible: false,
      pageLoading: false,
    };
  },
//  watch:{
//     filter:function(val,oldval){
//          this.$nextTick(function(){
//             this.onSearch(val );
//        })
//     }
//   },
 async mounted() {
  },
 methods: {
   async onSearch(occurrenceTime,goodsCode) {
       this.filter={goodsCode:goodsCode,occurrenceTime:occurrenceTime},
       this.$refs.pager.setPage(1)
       this.getlist()
    },
  async getlist() {
      if (!this.pager.OrderBy) this.pager.OrderBy="";
       var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
       this.listLoading = true
      this.list=[];
      var res = await getOrderWithholdRecord(params) 
      this.listLoading = false
      console.log('this.res',res)
     // if(!(res.code==1&&res.data)) return 
      this.total = res.data.total
      const data = res.data.list
      this.list = data
      console.log('this.list',this.list)
    },
  async cellclick(row, column, cell, event){
     if (column.property=='expressNo'&&row.expressNo)
        await this.showlogistics(row.companyCode,row.expressNo);
    },
  async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async showImg(e) {
      if (e.target.tagName == 'IMG') {
        this.imgPreview.img = e.target.src
        this.imgPreview.show = true
      }
    },
   async showlogistics(companycode,number){
      this.drawervisible=true;
       this.$nextTick(function(){
         this.$refs.logistics.showlogistics(companycode,number);
       })
    }
  },
};
</script>
<style lang="scss" scoped>
.imgDolg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 9999;
  background-color: rgba(140, 134, 134, 0.6);
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  #imgDolgClose {
    position: fixed;
    top: 35px;
    cursor: pointer;
    right: 7%;
    font-size: 50px;
    color: white;
  }
  img{
    width: 80%;
  }
}
</style>

