<template>
    <!-- 员工设置 + 行内编辑 -->
    <my-container v-loading="pageLoading">
        <vxetablebase :id="'accountmanage202301291318001'" :that='that' height="98%" border ref="xtable" :hascheck="true"
            class="draggable-table" :isIndex='true' :hasexpand='false' :isSelectColumn='true' :tableData='tasklist'
        @selectchangeevent="selectchangeevent"   @checkboxall="selectchangeevent"
          :tableCols='tableCols' tablekey="accountmanage" :loading="listLoading" :isBorder="false" :editconfig="{
                trigger: 'manual', mode: 'row', showStatus: true, showIcon: false
                , autoClear: false
            }" @sortchange='sortchange'>
            <template slot='extentbtn'>
                <div style="margin:10px 5px 5px 0;">
                    <span style="padding: 0;margin-right:2px;">
                        <el-input style="width:25%;" v-model="filter.userName" v-model.trim="filter.userName" :maxlength=100
                            placeholder="姓名" @keyup.enter.native="onSearch" clearable />
                    </span>
                    <span style="padding: 0;margin-right:5px;">
                        <el-select style="width:25%;" v-model="filter.companyName" placeholder="公司" clearable>
                            <el-option label="义乌" value="义乌"></el-option>
                            <el-option label="南昌" value="南昌"></el-option>
                            <el-option label="武汉" value="武汉"></el-option>
                            <el-option label="深圳" value="深圳"></el-option>
                        </el-select>
                    </span>
                    <span style="padding: 0;margin-right:5px;">
                        <el-button type="primary" @click="onSearch">查询</el-button>
                    </span>
                </div>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList"
                :page-size="100" />
        </template>
    </my-container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/vxetablemedianew.vue";
import MyContainer from "@/components/my-container";
import cesTable from "@/components/Table/tableforvedio.vue";
import { getPersonnelPositionAsync } from '@/api/media/shootingset'
import MyConfirmButton from "@/components/my-confirm-button";


export default {
    components: { MyContainer, cesTable, MyConfirmButton, vxetablebase },
    data() {
        return {
            that: this,
            pageLoading: false,
            positionOrder: false,
            summaryarry: [],
            userList: [],
            tasklist: [],
            fileList: [],
            workPositionlist: [],
            commissionPositionlist: [],
            retdata: [],
            sels: [], // 列表选中列
            listLoading: false,
            //人员编辑模块
            editLoading: false,
            editformdialog: false,
            dialogVisibleSyj: false,
            editformTitle: null,

            total: 0,
            pager: { OrderBy: "orderNum", IsAsc: true },
            filter: {
            },
            editformRules: {
                userId: [{ required: true, message: '请选择', trigger: 'blur' }],
                commissionRate: [{ required: true, message: '请填写', trigger: 'blur' }],
                companyName: [{ required: true, message: '请选择', trigger: 'blur' }],
                commissionPosition: [{ required: true, message: '请填写', trigger: 'blur' }],
                workPosition: [{ required: true, message: '请填写', trigger: 'blur' }],
                fpTaskAutoRate: [{ required: true, message: '请填写', trigger: 'blur' }],
                classtype: [{ required: true, message: '请填写', trigger: 'blur' }],
            },

        };
    },
    computed: {
        tableCols() {
            let tableCols = [
                { istrue: true, type: '', prop: 'userName', label: '姓名', width: '',  },
                { istrue: true, type: '', prop: 'companyName', label: '公司',   width: '' },
                { istrue: true, type: '', prop: 'workPositionStr', label: '工作岗位', width: '' },
                { istrue: true, type: '', prop: 'commissionPositionStr', label: '提成岗位', width: '' },
            ];
            return tableCols;
        }
    },
    async mounted() {
        await this.onSearch();
    },
    methods: {
        getReturnSelect(){
            if(this.sels.length==0){
                this.$message({ message: "请至少一条数据", type: "warning", });
                return;
            }
            return this.sels;
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            this.getTaskList();
        },
        async getTaskList() {
            var pager = this.$refs.pager.getPager();
            let params = {
                ...this.filter,
                ...pager,
                ...this.pager,
                isAutoadd:1
            };
            this.listLoading = true;
            let res = await getPersonnelPositionAsync(params);
            this.listLoading = false;

            this.tasklist = res.data.list;
            this.total = res.data.total;

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async selectchangeevent(records) {
            this.sels = [];
            records.forEach(f => {
                this.sels.push(f.userId);
            })
        },
    },
};
</script>

