<template>
  <div>
    <div class="row-box" v-if="type=='retail' && hasNullretail">
      <el-alert title="当前页面存在【零售价】为空的商品，公式将无法计算，请手工填写基本售价(供货价)或选择其他公式" type="error" show-icon :closable="false"></el-alert>
    </div>
    <div class="row-box" v-if="type=='supply' || type == 'cost' || type == 'retail'">
      <div>
        <span style="color:#f00;">* </span>基本售价(供货价) = {{type=='supply'?'原基本售价(供货价)':type=='cost'?'成本价':'零售价'}} x 
      </div>
      <div style="width: 70px;">
        <el-input type="number" v-model="rate" :min="0" :max="10000" @change="rateChange">
        </el-input>
      </div>
      <div>%</div>
      <div style="width: 58px;">
        <el-select v-model="operator" @change="beginCal">
          <el-option :value="0" label="+"></el-option>
          <el-option :value="1" label="-"></el-option>
          <el-option :value="2" label="×"></el-option>
          <el-option :value="3" label="÷"></el-option>
        </el-select>
      </div>
      <div>
        <el-input type="number" placeholder="请输入固定金额" v-model="fixed" @change="fixedChange"></el-input>
      </div>
      <div style="margin-left:auto;" v-if="type=='cost' && lossCount>0">
        <i class="el-icon-warning" style="color:#f00;"></i>有{{lossCount}}个sku ≤ 【成本价】可能亏损
      </div>
    </div>
    <div class="row-box" v-if="type=='supply' || type == 'cost' || type == 'retail'">
      <div>
        <span style="color:#f00;">* </span>价格修正方案(避免实际价格有小数) ：
      </div>
      <div>
        <el-radio-group v-model="plan" @input="beginCal">
          <el-radio :label="0">保留四位小数</el-radio>
          <el-radio :label="1">四舍五入取整</el-radio>
          <el-radio :label="2">向上取整(如¥10.23->¥11)</el-radio>
          <el-radio :label="3">向下取整(如¥10.54->¥10)</el-radio>
        </el-radio-group>
      </div>
    </div>
    <div class="row-box" v-if="type=='fixed'">
      <div>
        <span style="color:#f00;">* </span>基本售价(供货价) = 
      </div>
      <div>
        <el-input type="number" placeholder="请输入固定金额" v-model="fixed" @change="fixedChange"></el-input>
      </div>
      <div>元</div>
    </div>
    <el-table :data="datalist" class="mydata" height="350" :border="true" :header-cell-style="{backgroundColor:'#eee'}">
      <el-table-column type="index" width="50" label="序号"></el-table-column>
      <el-table-column label="图片" width="100" align="center">
        <template #default="{ row }">
          <el-image class="imgstyle" :src="row.skuImage" fit="fill" :preview-src-list="[row.skuImage]">
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="colour" label="商品名称" width="180"></el-table-column>
      <el-table-column prop="skuCode" label="商品编码" width="180"></el-table-column>
      <el-table-column prop="costPrice" label="成本价" width="120"></el-table-column>
      <el-table-column prop="supplyPrice" label="原基本售价(供货价)" v-if="type=='supply'"></el-table-column>
      <el-table-column prop="minSalesPrice" label="零售价" v-if="type=='retail'"></el-table-column>
      <el-table-column prop="baseSalePrice" label="基本售价(供货价)">
        <template #default="{ row }">
          <div style="max-width: 130px;">
            <el-input type="number" v-model="row.baseSalePrice" @change="baseSalePriceChange(row)"></el-input>
            <br />
            <span style="color:#f00;" v-if="type=='cost' && row.baseSalePrice<=row.costPrice">可能亏损</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row>
        <el-col :span="24" style="text-align:right;margin-top:30px;">   
            <el-button type="primary" @click="save()">保存</el-button>
        </el-col>
    </el-row>
  </div>
</template>

<script>
import {updateSkuBaseSale} from '@/api/distribution/distribution.js';
export default {
  name: "GoodsEditPriceForm",
  props: ['type','list'],
  data() {
      return {              
          that: this,
          pageLoading: false,
          rate: 100,
          operator:0,
          fixed: null,
          plan: 0,
          datalist:[]
      };
  },
  // 这样写会导致 几个panel中的数据同步变化 改为用mounted写法
  // watch:{
  //   list:{
  //     handler(newVal, oldVal){
  //       this.datalist = newVal;
  //     },
  //     immediate: true,
  //     deep: true
  //   }
  // },
  mounted(){
    this.datalist = JSON.parse(JSON.stringify(this.list))
    this.datalist.forEach(item=>{
      item.baseSalePrice = null;
    })
  },
  computed:{
    // 按零售价计算时 是否有零售价为空
    hasNullretail:function(){
      if(this.type!='retail') return false;
      return this.datalist.some(i=>i.minSalesPrice==null||i.minSalesPrice==undefined||i.minSalesPrice<=0);
    },
    // 按成本价计算时 亏损的个数
    lossCount:function(){
      if(this.type!='cost') return 0;
      var list = this.datalist.filter(i=>i.baseSalePrice<=i.costPrice);
      if(!list || list.length<=0) return 0;
      return list.length;
    }
  },
  methods:{
    // 比例改变
    rateChange(val){
      if(!val) val=0;
      var match = val.toString().match(/^\d+(?:\.\d{0,2})?/);
      if(match && match.length>0){
        val = Number.parseFloat(match[0]);
      }
      if(val<0 || val>10000) {
        val = 10000;
      }
      this.rate = val;
      this.beginCal();
    },
    // 固定金额改变
    fixedChange(val){
      if(!val) val=0;
      var match = val.toString().match(/^\d+(?:\.\d{0,4})?/);
      if(match && match.length>0){
        val = Number.parseFloat(match[0]);
      }
      if(val>1000) val = 1000;
      this.fixed = val;
      this.beginCal();
    },
    // 手动修改列表文本框值
    baseSalePriceChange(row){
      row.baseSalePrice = this.checkPrice(row.baseSalePrice);
    },
    // 检查列表文本框值
    checkPrice(price){
      if(!price) price=0;
      var re = price;
      if(price>=1000000000){
        re = 999999999;
      } else {
        re = this.calPlan(price);
      }
      return re;
    },
    // 数据变化开始计算
    beginCal(){
      // 按固定金额
      if(this.type=='fixed'){
        this.datalist.forEach(item=>{
          item.baseSalePrice = this.fixed
        });
      } else {
        this.datalist.forEach(item=>{

          item.baseSalePrice = this.calData(this.type=='supply'?item.supplyPrice:this.type=='cost'?item.costPrice:item.minSalesPrice);
        });
      }

    },
    // 计算
    calData(val){
      if(!val) val=0;
      var tmp = Number.parseFloat(val) * Number.parseFloat(this.rate) / 100;
      var re = tmp;
      if(this.fixed) {
        if(this.operator==0){
          re = (tmp*10000 + Number.parseFloat(this.fixed)*10000)/10000;
        } else if(this.operator==1){
          re = (tmp*10000 - Number.parseFloat(this.fixed)*10000)/10000;
        } else if(this.operator==2){
          re = tmp * Number.parseFloat(this.fixed);
        } else if(this.operator==3){
          re = (tmp*10000 / (Number.parseFloat(this.fixed)*10000))/10000;
        }
      }
      return this.checkPrice(re);
    },
    // 处理修正方案
    calPlan(val){
      if(!val) val=0;
      var re = val;
      if(re<0) re=0;
      if(this.plan==0){
        var match = val.toString().match(/^\d+(?:\.\d{0,4})?/);
        if(match && match.length>0){
          re = Number.parseFloat(match[0]);
        }
      } else if(this.plan==1){
        re = Math.round(Number.parseFloat(val));
      } else if(this.plan==2){
        re = Math.ceil(Number.parseFloat(val));
      } else if(this.plan==3){
        re = Math.floor(Number.parseFloat(val));
      }
      return re;
    },
    // 保存数据
    async save(){
      this.$emit('loading', true);
      var parm = this.datalist.map(item=> { return {
          proCode:item.proCode,
          skucode:item.skuCode,
          colour:item.colour,
          baseSalePrice:item.baseSalePrice
        }
      })
      const res = await updateSkuBaseSale(parm);
      if(res?.success){
        this.$message({ message: "修改供货价成功", type: "success" });
        this.$emit('handle', true);
      }
      this.$emit('loading', false);
    }
  }
}
</script>

<style lang="scss" scoped>
.row-box{
  display:flex;
  align-items: center;
  margin-top: 20px;
}
.row-box div{
  margin-left: 10px;
}

.mydata{
  margin: 20px 0;
  width: 100%;height:350px !important;
}

.el-radio {
  margin-right: 12px !important;
}
</style>