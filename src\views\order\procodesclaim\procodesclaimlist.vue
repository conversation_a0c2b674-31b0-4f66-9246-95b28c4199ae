<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="系列编码:" style="padding: 0;margin: 0; ">
                    <el-input v-model.trim="filter.styleCode" style="width: 100px" placeholder="系列编码" clearable maxlength="50" />
                </el-form-item>
                <el-form-item label="编码负责人:">
                    <el-select v-model="filter.groupId" style="width: 100px" placeholder="请选择" :clearable="true"
                        :collapse-tags="true" filterable @change="onSearch">
                        <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item label="采购负责人:">
                    <el-select v-model="filter.brandId"  clearable filterable :collapse-tags="true" placeholder="请选择采购员" style="width: 100px" >
                        <el-option v-for="item in brandlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="认领人:">
                    <el-input v-model.trim="filter.claimant" style="width: 100px" placeholder="认领人" clearable maxlength="50" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <el-dialog :visible.sync="detail.visible" :show-close="false" width="80%" v-dialogDrag>
            <!-- <el-button type="primary" @click="onSearchDetail">查询</el-button> -->
            <series-goods :filter="detail.filter" ref="seriesGoods" style="height: 480px">
            </series-goods>
        </el-dialog>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatmoney, tothousands } from "@/utils/tools";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getProCodeClaimListAsync, addProCodeLogAsync } from '@/api/inventory/procodeclaim';
import { getGroupKeyValue } from "@/api/operatemanage/base/product";
import { getAllProBrand } from '@/api/inventory/warehouse'
import SeriesGoods from '@/views/order/procodesclaim/procodesclaimloglist.vue'
var thoundszero = function (value) {
    var money = tothousands(
        Math.round(value)
    )
    return money
};
const tableCols = [
    { istrue: true,  prop: 'styleCode', label: '系列编码', width: '150'},
    { istrue: true,prop: 'groupName', label: '编码负责人', tipmesg: '', width: '220', formatter: (row) => !row.groupName ? '未知' : row.groupName },
    { istrue: true,  prop: 'brandName', label: '采购负责人', tipmesg: '', width: '220', formatter: (row) => !row.brandName ? '未知' : row.brandName },
    { istrue: true, prop: 'invAmountTotal', label: '库存资金',  tipmesg: '', width: '100', sortable: 'custom', formatter: (row) => thoundszero(row.invAmountTotal) },
    { istrue: true, prop: 'masterStock', label: '库存数量',  tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'operateClaimant', label: '运营认领', tipmesg: '', width: 'auto', tipmesg: '' },
    { istrue: true, prop: 'purchaseClaimant', label: '采购认领', tipmesg: '', width: 'auto', tipmesg: ''},
    { istrue: true, type: 'button', label: '认领', width: '82', btnList: [{ label: "认领",  handle: (that, row) => that.onSimilarityReceiv(row) }] },
    { istrue: true, type: 'button', label: '日志', width: '82',btnList: [{ label: "查看日志", handle: (that, row) => that.showDetail(row) }] }
]
const tableHandles1 = [
  
];
export default {
    name: 'proopeartionloglist',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow ,SeriesGoods},
    props: {

    },
    data() {
        return {
            that: this,
            filter: {
                styleCode: null,
                groupId: null,
                brandId: null,
                claimant: null
            },
            detail: {
                visible: false,
                filter: {
                    styleCode: null
                }
            },
            brandlist: [],
            groupList: [],
            list: [],
            summaryarry: {},
            pager: { OrderBy: "pricingTime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            tableHandles1: tableHandles1,
            dialogVisibleUpload: false,
            fileList: [],
            fileparm: {},
            uploadLoading: false
        };
    },
    async mounted() {
        await this.onSearch();
        await this.setGroupSelect();
    },
    methods: {
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getProCodeClaimListAsync(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            // data.forEach(d => {
            //     d._loading = false;
            // })
            this.list = data;
        },
        //排序查 询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        //设置运营组下拉
        async setGroupSelect() {
            const res = await getGroupKeyValue({});
            this.groupList = res.data;

            var res2 = await getAllProBrand();
            this.brandlist = res2.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        async onSimilarityReceiv(row) {
            var styleCode = row.styleCode
                this.$confirm('是否认领该系列编码', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    var res = await addProCodeLogAsync({ styleCode: styleCode });
                    if (res?.success) {
                        this.$message({ type: 'success', message: '认领成功!' });
                        await this.onSearch()
                    }
                    // else
                    // {
                    //     this.$message({ type: 'waring', message: res?.msg });
                    // }
                 
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消认领'
                    });
                });
        },
        async showDetail(row) {

            this.detail.filter.styleCode = row.styleCode;
            this.detail.visible = true

            this.$nextTick(async () => {
                await this.$refs.seriesGoods.onSearch();
            })
            },
    },
};
</script>

<style lang="scss" scoped></style>
