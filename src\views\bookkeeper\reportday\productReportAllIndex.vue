<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="全平台日报" name="first1" style="height: 100%">
        <productReportAll ref="productReportAll" style="height: 100%"></productReportAll>
      </el-tab-pane>
      <el-tab-pane label="订单日报" name="first2" :lazy="true" style="height: 100%"
        v-if="checkPermission('AllOrderDayReport')">
        <AllOrderDayReport @ChangeActiveName2="ChangeActiveName2" ref="AllOrderDayReport" style="height: 100%">
        </AllOrderDayReport>
      </el-tab-pane>
      <el-tab-pane label="编码日报" name="first3" :lazy="true" style="height: 100%"
        v-if="checkPermission('AllGoodCodeDayReport')">
        <AllGoodCodeDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="AllGoodCodeDayReport" style="height: 100%"></AllGoodCodeDayReport>
      </el-tab-pane>
      <el-tab-pane label="ID日报" name="first4" :lazy="true" style="height: 100%" v-if="checkPermission('AllIdDayReport')">
        <AllIdDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName" ref="AllIdDayReport"
          style="height: 100%"></AllIdDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺日报" name="first5" :lazy="true" style="height: 100%"
        v-if="checkPermission('AllShopDayReport')">
        <AllShopDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="AllShopDayReport" style="height: 100%"></AllShopDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺SKU日报" name="first7" :lazy="true" style="height: 100%"
        v-if="checkPermission('AllCommodityDayReport')">
        <AllCommodityDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="AllCommodityDayReport" style="height: 100%"></AllCommodityDayReport>
      </el-tab-pane>
      <el-tab-pane label="明细日报" name="first6" :lazy="true" style="height: 100%"
        v-if="checkPermission('AllDetailDayReport')">
        <AllDetailDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="AllDetailDayReport" style="height: 100%"></AllDetailDayReport>
      </el-tab-pane>
      <el-tab-pane label="出仓负利润ID订单明细" name="first8" :lazy="true" style="height: 100%"
        v-if="checkPermission('AllOutgoingprofitIDorderdetail')">
        <AllOutgoingprofitIDorderdetail @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="AllOutgoingprofitIDorderdetail" style="height: 100%"></AllOutgoingprofitIDorderdetail>
      </el-tab-pane>
      <el-tab-pane label="SKUS日报" name="first9" :lazy="true" style="height: 100%"
        v-if="checkPermission('AllSkusDayReport')">
        <AllSkusDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="AllSkusDayReport" style="height: 100%"></AllSkusDayReport>
      </el-tab-pane>
      <el-tab-pane label="运营销售分析" name="first10" :lazy="true" style="height: 100%" v-if="checkPermission('AllOperateSalesAnalysis')">
        <AllOperateSalesAnalysis ref="AllOperateSalesAnalysis" style="height: 100%"></AllOperateSalesAnalysis>
      </el-tab-pane>
      <el-tab-pane label="团队业绩分析" name="first11" :lazy="true" style="height: 100%" v-if="checkPermission('AllPlatformTeamRptSum')" >
        <TeamRptSum ref="TeamRptSum" style="height: 100%"></TeamRptSum>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import productReportAll from "./productReportAll.vue";
import AllOrderDayReport from "./AllOrderDayReport.vue";
import AllSkusDayReport from "./AllSkusDayReport.vue";
import AllGoodCodeDayReport from "./AllGoodCodeDayReport.vue";
import AllIdDayReport from "./AllIdDayReport.vue";
import AllShopDayReport from "./AllShopDayReport.vue";
import AllCommodityDayReport from "./AllCommodityDayReport.vue";
import AllOutgoingprofitIDorderdetail from "./AllOutgoingprofitIDorderdetail.vue";
import AllDetailDayReport from "./AllDetailDayReport.vue";
import AllOperateSalesAnalysis from "./AllOperateSalesAnalysis.vue";
import TeamRptSum from "./TeamRptSum.vue";

import middlevue from "@/store/middle.js"
export default {
  name: "productReportAllIndex",
  components: {
    MyContainer, productReportAll, AllOrderDayReport,AllSkusDayReport, AllGoodCodeDayReport, AllIdDayReport, AllShopDayReport, AllDetailDayReport, AllCommodityDayReport, AllOutgoingprofitIDorderdetail, AllOperateSalesAnalysis,TeamRptSum
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },
  async mounted() {
    middlevue.$on('toLinkTxDetailDayReport', (data) => {
      if (data.isTrue && data.plat == 'qpt') {
        this.activeName = 'first6';
        setTimeout(() => {
          middlevue.$emit('toLinkTxDetailDayReportQuery', data)
        }, 500);

      } else {
        this.activeName = 'first8';
        setTimeout(() => {
          middlevue.$emit('toLinkTxOutgoingprofitIDorderdetailQuery', data)
        }, 500);
      }
    })
  },
  beforeDestroy() {
    middlevue.$off('toLinkTxDetailDayReport')
  },
  methods: {
    ChangeActiveName(activeName) {
      this.activeName = 'first2';
      this.$refs.AllOrderDayReport.AllGoodCodeDayReportArgument(activeName)
    },
    ChangeActiveName2(activeName, No, Time) {
      this.activeName = 'first6';
      this.$refs.AllDetailDayReport.AllDetailDayReportArgument(activeName, No, Time)
    }
  },
};
</script>

<style lang="scss" scoped></style>
