<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <!-- <el-button-group>
                <el-button  style="padding:0;margin:0;">
                    <el-date-picker v-model="filter.timeRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        range-separator="至" start-placeholder="确认开始日期" end-placeholder="确认结束日期" style="width:230px;">
                    </el-date-picker>
                </el-button>                               
                <el-button  style="padding:0;margin:0;">
                    <el-input v-model.trim="filter.proCode" placeholder="产品ID" :clearable="true" ></el-input>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
              
            </el-button-group> -->
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="false"  
          :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='tableData' 
          :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:98%;margin: 0">
        </ces-table>
        <!--分页-->
        <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
        </template>
    </my-container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import MyContainer from '@/components/my-container';
import cesTable from '@/components/Table/table.vue';
import {
    PageCollectDataDetail
} from '@/api/inventory/goodscodestock';
import {formatPlatform,formatYesornoBool,formatLinkProCode,platformlist} from "@/utils/tools";

const tableCols =[
     {istrue:true,prop:'warehousingTime',label:'入库时间', width:'200',sortable:'custom'},
      {istrue:true,prop:'type',label:'类型',sortable:'custom', width:'200',formatter:(row)=>row.type==1?"领取":""},
      {istrue:true,prop:'goodsCode',fix:true,label:'商品编码', width:'200',sortable:'custom',},
      {istrue:true,prop:'groupName',label:'运营组',sortable:'custom', width:'200'},
      {istrue:true,prop:'operationSpecialistName',label:'运营专员', width:'200',sortable:'custom',},
      {istrue:true,prop:'qty',label:'数量', width:'200',sortable:'custom',},
];

const tableHandles=[
       // {label:"导出", handle:(that)=>that.onExport()},
      ];

const startDate = formatTime(dayjs().subtract(1,'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
    name:"Users",
    components:{MyContainer,cesTable},
    data(){
        return {
          
            that:this,                     
            tableCols:tableCols,
            tableHandles:tableHandles,
            tableData:[],
            total: 0,
            pager:{OrderBy:"warehousingTime",IsAsc:false},
            listLoading: false,
            pageLoading: false,
            summaryarry:{},    
            sels:[],

        };
    },
    props:{
        filter:{
            proCode:null,
            timeRange:[startDate,endDate],
            StartTime:null,
            EndTime:null,
            orderStatus:"",
            goodsStatus:"",
            timeRange1:[],
            platform:null

            //orderNo:null,
            //orderNoInner:null
        },   
    },

    methods:{
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            await this.onSearch();
            },
            async onSearch(){
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async getList(){
            this.filter
            var that=this;
            this.listLoading=true;
            var pager = this.$refs.pager.getPager();
            const params = {...pager,...this.pager,...this.filter};
            const res = await PageCollectDataDetail(params).then(res=>{
                that.total = res.data?.total;
                that.tableData = res.data?.list;
                that.summaryarry=res.data?.summary;               
            });
            this.listLoading=false;
        },
        
    }
})
</script>
<style scoped>
    ::v-deep .el-table__fixed-footer-wrapper tbody td{
        color:blue;
    }
</style>

