<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <el-tabs v-model="activeName" style="height:94%;">
      <!-- <el-tab-pane label="投稿人信息" name="tab0" style="height: 100%;">
          <publisher :filter="filter" ref="publisher" style="height: 100%;"/>
      </el-tab-pane> -->
   

      <el-tab-pane label="视频数据管理" name="tab2" style="height: 100%;">
          <videoinfo :filter="filter" ref="videoinfo" style="height: 100%;"/>
      </el-tab-pane>

      <!-- <el-tab-pane label="投稿记录管理" name="tab3" style="height: 100%;">
          <pddcontributeinfo :filter="filter" ref="pddcontributeinfo" style="height: 100%;"/>
      </el-tab-pane> -->

      <el-tab-pane label="人员业绩查询" name="tab1" style="height: 100%;">
          <inquirsstatistics :filter="filter" ref="inquirsstatistics" style="height: 100%;" :lazy="true"/>
      </el-tab-pane>
      <el-tab-pane label="店业绩查询" ref="tab4" name="tab4" style="height: 100%;">
          <shopinquirsstatistics :filter="filter" ref="shopinquirsstatistics" style="height: 100%;" :lazy="true"/>
      </el-tab-pane>
      <el-tab-pane label="人员维护" tab="tab5" style="height: 100%;" v-if="checkPermission('/api/OperateManage/LookBoard/DeleteVideoContributor')">
        <videoContributor :filter="filter" ref="videoContributor" style="height: 100%;" :lazy="true"></videoContributor>
      </el-tab-pane>
    
    </el-tabs>
  </my-container >

 </template>
<script>
import MyContainer from "@/components/my-container";
import inquirsstatistics from '@/views/operatemanage/newmedia/empachievement' 
import publisher from '@/views/operatemanage/newmedia/publisher' 
import videoinfo from '@/views/operatemanage/newmedia/videoinfo' 
import shopinquirsstatistics from '@/views/operatemanage/newmedia/shopachievement' 
import pddcontributeinfo from '@/views/operatemanage/newmedia/pddcontributeinfo' 
import videoContributor from '@/views/operatemanage/newmedia/videoContributor';

export default {
  name: "Users",
  components: { MyContainer,inquirsstatistics,publisher,videoinfo,shopinquirsstatistics,pddcontributeinfo, videoContributor},
  data() {
    return {
      that:this,
      pageLoading:'',
      filter: {
      },
      shopList:[],
      userList:[],
      groupList:[],
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
      activeName:'tab2'
    };
  },
  mounted() { 
    window.showtab4=this.showtab4
  },
  methods: {
    showtab4(){
     this.activeName="tab3"


    }

  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
