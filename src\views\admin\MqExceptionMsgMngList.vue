<!-- 异常队列消息处理  -->
<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <ces-table ref="table" :that='that' :tableData='list' :tableCols='tableCols' >
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button type="primary" @click="loadData" style="margin-right: 20px;">刷新</el-button>

                    <el-switch v-model="switchOperate" active-text="正式" inactive-text="测试" 
                        inactive-color="#13ce66"  :width="40" @change="changeRefund"
                        style="margin-right: 20px;">
                    </el-switch>
                </el-button-group>

            </template>
        </ces-table>
    </my-container>
</template>
<script>

    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import cesTable from "@/components/Table/table.vue";
    import request from '@/utils/request';
    import $ from 'jquery';

    const tableCols = [
        { istrue: true, prop: 'serviceName', label: '服务名称', width: '120', sortable: true },
        { istrue: true, prop: 'queueServiceName', label: '队列服务器名称', width: '120', sortable: true },
        { istrue: true, prop: 'queueName', label: '队列名称', width: '160', sortable: true },
        { istrue: true, prop: 'deliveryTag', label: '消息标识', width: '70', sortable: true },
        { istrue: true, prop: 'queueMessage', label: '消息内容' },
        { istrue: true, prop: 'exceptionContent', label: '异常内容'},
        {
            istrue: false, type: 'button', label: '操作', width: '140',
            btnList: [
                {
                    label: "重回队列", handle: (that, row) => that.execMsg(row,1),
                },
                {
                    label: "默认成功", handle: (that, row) => that.execMsg(row,3),
                },
            ]
        },
    ];

    const mqUrlsTest = [
        { url: `*************:32484`, serviceName: 'Customerservice' },
        { url: `*************:30342`, serviceName: 'Verifyorder' },
        { url: `*************:32298`, serviceName: 'Distribution' },
        { url: `*************:32455`, serviceName: 'Express' },
        { url: `*************:30387`, serviceName: 'Financial' },

        { url: `*************:30071`, serviceName: 'Inventory' },
        { url: `*************:32098`, serviceName: 'Manager' },
        { url: `*************:30474`, serviceName: 'Media' },
        { url: `*************:32700`, serviceName: 'Monthbookkeeper' },
        { url: `*************:32081`, serviceName: 'Open' },

        { url: `*************:31954`, serviceName: 'Operatemanage' },
        { url: `*************:30081`, serviceName: 'Order' },
        { url: `*************:30605`, serviceName: 'Packprocess' },
        { url: `*************:32542`, serviceName: 'Pddoperatemanage' },

        { url: `*************:31755`, serviceName: 'Pddplatform' },
        { url: `*************:30802`, serviceName: 'Profit' },
        { url: `*************:32016`, serviceName: 'Warning' },
        { url: `*************:31989`, serviceName: 'Importinventory' },
        { url: `*************:30488`, serviceName: 'Openplatform' },

        { url: `*************:31318`, serviceName: 'Bookkeeper' },
        { url: `*************:31629`, serviceName: 'Msgcenter' },
        // { url: `localhost:8030`, serviceName: 'Msgcenter'},
        { url: `*************:31575`, serviceName: 'Cwmanager'},
    ];

    const mqUrlsDevelop = [
        { url: `*************:30143`, serviceName: 'Customerservice' },
        { url: `*************:31435`, serviceName: 'Verifyorder' },
        { url: `*************:31291`, serviceName: 'Distribution' },
        { url: `*************:30877`, serviceName: 'Express' },
        { url: `*************:30497`, serviceName: 'Financial' },

        { url: `*************:31921`, serviceName: 'Inventory' },
        { url: `*************:30684`, serviceName: 'Manager' },
        { url: `*************:31878`, serviceName: 'Media' },
        { url: `*************:31661`, serviceName: 'Monthbookkeeper' },
        { url: `*************:31192`, serviceName: 'Open' },

        { url: `*************:32327`, serviceName: 'Operatemanage' },
        { url: `*************:32132`, serviceName: 'Order' },
        { url: `*************:31443`, serviceName: 'Neworder' },
        { url: `*************:31342`, serviceName: 'Packprocess' },
        { url: `*************:30931`, serviceName: 'Pddoperatemanage' },

        { url: `*************:32178`, serviceName: 'Pddplatform' },
        { url: `*************:31205`, serviceName: 'Profit' },
        { url: `*************:30907`, serviceName: 'Warning' },
        { url: `*************:30119`, serviceName: 'Importinventory' },
        { url: `*************:32656`, serviceName: 'Openplatform' },

        { url: `*************:30390`, serviceName: 'Bookkeeper' },
        { url: `*************:31102`, serviceName: 'Hangfire.sync' },
        { url: `*************:30232`, serviceName: 'Dingdingsync' },
        { url: `*************:30589`, serviceName: 'Hangfiresz' },
        { url: `*************:31908`, serviceName: 'Msgcenter' },
        { url: `*************:30353`, serviceName: 'Operator' },
        { url: `*************:32309`, serviceName: 'Cwmanager'},
    ];

    export default {
        name: 'MqExceptionMsgMngList',
        components: { MyContainer, MyConfirmButton ,cesTable, request},
        data() {
            return {
                pageLoading: false,
                that: this,
                formEditMode: true,//是否编辑模式              
                mode: 1,
                tableCols:tableCols,
                list:[],
                mqUrls:mqUrlsTest,
                switchOperate: false,//操作-测试false-仅正式true
            }
        },
        async mounted() {
            this.loadData();
        },
        computed: {
        },
        methods: {
            //测试、正式切换
            changeRefund() {
                if (this.switchOperate) {
                    this.mqUrls = mqUrlsDevelop;
                }
                else {
                    this.mqUrls = mqUrlsTest;
                }
            },
            async loadData() {
                let self=this;
                this.list=[];
                this.pageLoading=true;

                this.mqUrls.forEach(async ({ url, serviceName }) => {
                    try {
                        $.get(`http://${url}/rabbitmq/RabbitMQ/getexceptionconsumdtos`).then(tempRlt => {
                            if (tempRlt && tempRlt.length > 0) {
                                tempRlt.forEach(d => {
                                    self.list.push({ ...d, apiPrefix: `http://${url}/rabbitmq/RabbitMQ/`, serviceName: serviceName, queueServiceName: 'RabbitMQ' });
                                });
                            }
                        })
                    } catch (ex) {}

                    try {
                        $.get(`http://${url}/rabbitmq/ClusterRabbitMQ/getexceptionconsumdtos`).then(tempRlt => {
                            if (tempRlt && tempRlt.length > 0) {
                                tempRlt.forEach(d => {
                                    self.list.push({ ...d, apiPrefix: `http://${url}/rabbitmq/ClusterRabbitMQ/`, serviceName: serviceName, queueServiceName: 'ClusterRabbitMQ' });
                                });
                            }
                        })
                    } catch (ex) {}

                });
                this.pageLoading = false;
            },
            
            /*
                @execType 1驳回重新归队、2驳回不归队、3默认为成功
            */
            async execMsg(row, execType){
                let func1 = async ({ apiPrefix, queueName, tag, execType }) => {
                    return $.get(`${apiPrefix}ExecExceptionConsum` + `?queueName=${queueName}` + `&tag=${tag}` + `&execType=${execType}`);
                }

                try {
                    let tempRlt = await func1({ apiPrefix: row.apiPrefix, queueName: row.queueName, tag: row.deliveryTag, execType: execType });
                    if (tempRlt && tempRlt == "OK") {
                        this.$message.success('操作成功！');
                        this.loadData();
                    }
                } catch (ex) {}
            },
        }
    }
</script>
