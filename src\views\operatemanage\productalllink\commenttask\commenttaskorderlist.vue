<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right" label-width="90px">
                <el-form-item>
                    <el-input v-model="filter.proCode" placeholder="产品ID" style="width: 120px" />
                </el-form-item>
                <el-form-item>
                    <el-input v-model="filter.proName" placeholder="产品名称" style="width: 120px" />
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.platform" placeholder="平台" :clearable="true" :collapse-tags="true" style="width: 120px" filterable>
                        <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.groupId" placeholder="运营组" :clearable="true" :collapse-tags="true" style="width: 120px" filterable>
                        <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <!-- <el-form-item>
                    <el-select v-model="filter.statusEnum" placeholder="状态" :clearable="true" :collapse-tags="true" style="width: 120px" filterable>
                        <el-option label="待发布" value=0>待发布</el-option>
                        <el-option label="已发布" value=1>已发布</el-option>
                        <el-option label="已认领" value=2>已认领</el-option>
                        <el-option label="已完成" value=3>已完成</el-option>
                    </el-select>
                </el-form-item> -->
                <el-form-item>
                    <el-input v-model="filter.groupUserName" placeholder="运营" style="width: 120px" />
                </el-form-item>
                <!-- <el-form-item>
                    <el-input v-model="filter.receiveUserName" placeholder="认领人" style="width: 120px" />
                </el-form-item> -->
                <el-form-item>
                    <el-input v-model="filter.orderNumber" placeholder="订单号" style="width: 120px" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :loading="listLoading">
        </ces-table>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </my-container>
</template>

<script>
    import { formatTime } from "@/utils";
    import MyContainer from '@/components/my-container';
    import cesTable from "@/components/Table/table.vue";
    import { platformlist } from '@/utils/tools';
    import MyConfirmButton from '@/components/my-confirm-button';
    import { getDirectorGroupList } from '@/api/operatemanage/base/shop';
    import { pageCommentTaskOrderAsync } from '@/api/operatemanage/productalllink/alllink';
    const tableCols = [
        { istrue: true, prop: 'createdTime', label: '创建时间', width: '150', sortable: 'custom', formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'proCode', label: '产品ID', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'proImageUrl', label: '产品图片', width: '80', type: 'images' },
        { istrue: true, prop: 'proName', label: '产品名称', width: '80', sortable: 'custom' },

        { istrue: true, prop: 'orderNumber', label: '订单号', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'platformCommissionAmount', label: '平台佣金', width: '80', sortable: 'custom' },
        // { istrue: true, prop: 'fishPondCommissionAmount', label: '鱼塘佣金', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'principalAmount', label: '本金', width: '80', sortable: 'custom' },

        { istrue: true, prop: 'platform', label: '平台', width: '80', sortable: 'custom', formatter: (row) => row.platformName || ' ' },
        { istrue: true, prop: 'taoKouLing', label: '淘口令', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'hopeOrderCount', label: '预计单量', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'startDate', label: '开始时间', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.startDate, 'YYYY-MM-DD') },
        { istrue: true, prop: 'endDate', label: '结束时间', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.endDate, 'YYYY-MM-DD') },
        { istrue: true, prop: 'groupId', label: '运营组', width: '80', sortable: 'custom', formatter: (row) => row.groupName || ' ' },
        { istrue: true, prop: 'groupUserName', label: '发布人(运营)', width: '80', sortable: 'custom' },
        //{ istrue: true, prop: 'receiveUserName', label: '认领人', width: '80', sortable: 'custom' },
        //{ istrue: true, prop: 'receiveDate', label: '认领时间', width: '150', sortable: 'custom', formatter: (row) => row.receiveDate == null ? null : formatTime(row.receiveDate, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'finishOrderCount', label: '完成单量', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'finishDate', label: '完成时间', width: '150', sortable: 'custom', formatter: (row) => row.finishDate == null ? null : formatTime(row.finishDate, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'statusEnum', label: '状态', width: '80', sortable: 'custom', formatter: (row) => row.statusName || ' ' },
    ];

    export default {
        name: "commenttasklist",
        components: { MyContainer, MyConfirmButton, cesTable },
        data() {
            return {
                that: this,
                filter: {},
                tableCols: tableCols,
                pageLoading: false,
                list: [],
                summaryarry: {},
                total: 0,
                pager: { OrderBy: "CreatedTime", IsAsc: false },
                groupList: [],//运营组下拉
                platformList: platformlist,//平台下拉
                listLoading: false,
                sels: [],
            };
        },
        async mounted() {
            await this.getGroupList();
            await this.getlist();
        },
        methods: {
            //获取运营组下拉
            async getGroupList() {
                var res2 = await getDirectorGroupList();
                this.groupList = res2.data?.map(item => { return { value: item.key, label: item.value }; });
            },
            //获取查询条件
            getCondition() {
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }
                return params;
            },
            //分页查询
            async getlist() {
                this.filter.beginDate = null;
                this.filter.endDate = null;
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true
                var res = await pageCommentTaskOrderAsync(params);
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summary;
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
            },
            //查询第一页
            async onSearch() {
                this.$refs.pager.setPage(1)
                await this.getlist()
            },
            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderBy = column.prop;
                    this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
        }

    }
</script>
