<template>
  <div style="width: 100%; height: 100%;">

    <div style="width: 100%; height: 100%; position: relative; overflow-x: hidden;">
      <vxe-toolbar ref="xToolbar" custom class="vxetoolbar20221212">
          <template #buttons>
              <slot name="tbHeader" />
          </template>
     </vxe-toolbar>
      <vxe-table ref="xTable" border="default" height="100%" :show-footer="true" style="width: 100%;"
        class="vxetable202212161323 mytable-scrollbar20221212" resizable stripe :id="id" show-overflow
        :show-footer-overflow="'tooltip'" keep-source size="mini" :loading="tableloading" :data="tableData"
        :scroll-y="{ gt: 100, enabled: true }" :scroll-x="{ gt: 100, enabled: true }" :footer-method="footerMethod"
        :header-row-class-name="'height cellheight1'" @checkbox-change="checkchange" @checkbox-all="checkchange"
        :expand-config="{ accordion: true, trigger:'cell', visibleMethod: ()=>{ return false} }" @footer-cell-click="footercellclick" :row-class-name="rowStyleFun"
        @cell-click="cellclick" :row-config="{ isCurrent: true, isHover: true }">
        <vxe-column type="checkbox" width="28" align="left" fixed='left'></vxe-column>
        <vxe-column title="编号" field="packagesProcessingId" width="55" align="left" fixed='left'>
        </vxe-column>
        <vxe-column field="finishedProductImg" title="成品图片" width="70" align="left" fixed='left'>
          <template #default="{ row }">
            <div v-if="row.finishedProductImg" class="mypopver">
              <el-popover placement="right-start" width="230" trigger="hover">
                <div style="display: flex; flex-direction: column;">
                  <div style="display:flex; flex-direction: row;">
                    上传人：{{ row.updateFinishImgUser }}
                  </div>
                  <div style="display:flex; flex-direction: row; font-size: 14px;">
                    上传时间：{{ row.updateFinishImgDate }}
                  </div>
                </div>

                <el-image slot="reference" style="width: 50px; height: 50px" :src="row.finishedProductImg"
                  :preview-src-list="[row.finishedProductImg]">
                </el-image>
              </el-popover>
            </div>
            <el-image v-else slot="reference" style="width: 50px; height: 50px" :src="row.finishedProductImg"
              :preview-src-list="[row.finishedProductImg]">
            </el-image>

          </template>
        </vxe-column>
        <vxe-column type="expand" align="left" width="25" :edit-render="{}" fixed='left' field="expand">
          <template #default="{ row, rowIndex }">
            <div class="clihover">
              <i :class="(rowIndex==isselindex&&isselnow)?'el-icon-arrow-down':'el-icon-arrow-right'" style="font-size: 14px;"></i>
            </div>
          </template>
          <template #content="{ row, rowIndex }">
            <el-row style="padding: 20px 10px 20px 205px;background-color: #909399;">
              <!-- <button @click="openexpand(row, rowIndex)" ref="neipackageid">
                  刷新
                </button> -->
                <div style="background-color: white;">
                <vxe-table v-if="neitableshow" height="137px" show-overflow resizable border="default"
                :row-config="{ height: '36', isCurrent: true, isHover: true }" :header-row-class-name="'height1'"
                :cell-class-name="'cellheight1'" :align="allAlign1" stripe :data="neitable.detialList" :loading="loadingg"
                @checkbox-change="chilcheckchange" @checkbox-all="chilcheckchange">
                <vxe-column type="checkbox" width="28"></vxe-column>
                <vxe-column title="编号" width="60">
                  <template #default="{ row }">
                    <div>{{ row.showCode }}</div>
                  </template>
                </vxe-column>
                <vxe-column field="id" title="半成品Id" width="135" v-if="false" fixed='left'></vxe-column>
                <vxe-column field="halfProductImg" title="半成品图" width="90" align="left">
                  <template #default="{ row }">
                    <el-image style="width: 50px; height: 50px" :src="row.halfProductImg"
                      :preview-src-list="[row.halfProductImg]">
                    </el-image>
                  </template>
                </vxe-column>
                <!-- <vxe-column field="requiredImg" title="所需图" width="90" align="left">
                  <template #default="{ row }">
                    <el-image style="width: 50px; height: 50px" :src="row.requiredImg"
                      :preview-src-list="[row.requiredImg]">
                    </el-image>
                  </template>
                </vxe-column> -->
                <vxe-column field="halfProductCode" title="半成品编码" width="140" min-width="125">
                  <template #default="{ row }">
                    <div class="relativebox">
                      <!-- <el-tooltip effect="dark" :content="row.halfProductCode" placement="top-start"> -->
                      <div class="textover" style="width: 69%;">{{ row.halfProductCode }}</div>
                      <!-- </el-tooltip> -->

                      <div class="copyhover" @click="copytext(row.halfProductCode)">
                        <i class="el-icon-document-copy"></i>
                      </div>
                    </div>


                  </template>
                </vxe-column>
                <vxe-column field="halfProductName" title="半成品名称" width="280" min-width="125"></vxe-column>
                <vxe-column field="combinationQuantity" title="组合数量" width="78"></vxe-column>
                <vxe-column field="quantityRequired" title="所需数量" width="78"></vxe-column>
                <vxe-column field="stockQuantity" title="入库数量" width="78">
                  <template #default="{ row }">
                    <div style="color: rgb(4, 184, 255);cursor:pointer" @click="openDetails(row)">{{ row.stockQuantity}}</div>
                  </template>
                </vxe-column>
                <vxe-column field="ioDate" title="到货时间" width="150"></vxe-column>
                <!-- <vxe-column field="incomingQuantity" title="调入数量" width="78">
                  <template #default="{ row }">
                    <el-button type="text" @click="tableeditfuc(row, 4)" size="mini">{{ row.incomingQuantity
                    }}</el-button>
                  </template>
                </vxe-column>
                <vxe-column field="incomingDate" title="调入日期" width="78">
                  <template #default="{ row }">
                    <span>{{ formatIsCommission(row.incomingDate) }}</span>
                  </template>
                </vxe-column>
                <vxe-column field="deliveryUser" title="收货员" width="65" align="center">
                  <template #default="{ row }">
                    <el-button type="text" @click="tableeditfuc(row, 3)" size="mini">{{ row.deliveryUser }}</el-button>
                  </template>
                </vxe-column>
                <vxe-column field="deliveryWorkPrice" title="收货工价" width="70"
                  v-if="checkPermission('api:Inventory:PackagesProcessing:DeliveryWorkPrice')">
                  <template #default="{ row }">
                    <el-button type="text" style="color: #999" @click="changepriceshow(row, 3)" size="mini">{{
                      row.deliveryWorkPrice ? row.deliveryWorkPrice : 0 }}</el-button>
                  </template>
                </vxe-column>
                <vxe-column field="deliveryDate" title="收货日期" width="85">
                  <template #default="{ row }">
                    <span>{{ formatIsCommission(row.deliveryDate) }}</span>
                  </template>
                </vxe-column>
                <vxe-column field="totalDeliveryQuantity" title="收货数量" width="78"></vxe-column> -->

                <vxe-column field="age" title="操作">
                  <template #default="{ row }">
                    <!-- <el-button type="primary" @click="workshowfuc(row, 3)">收货记录</el-button> -->
                    <el-button type="primary" icon="el-icon-picture-outline"  @click="showupfuc(row)" :disabled="isCopy"></el-button>
                  </template>
                </vxe-column>

              </vxe-table>
            </div>
            </el-row>
            <el-row style="padding: 0px 10px 20px 205px;background-color: #909399;">
              <div style="background-color: white;">
                <vxe-table v-if="neitableshow" height="114px" show-overflow resizable border="default"
                :row-config="{ height: '36', isCurrent: true, isHover: true }" :header-row-class-name="'height1'"
                :cell-class-name="'cellheight1'" :align="allAlign1" stripe :data="neitable.consumableList" :loading="editLoading"
                @checkbox-change="chilcheckchange" @checkbox-all="chilcheckchange">
                <vxe-column type="checkbox" width="28"></vxe-column>
                <vxe-column title="编号" width="60">
                  <template #default="{ row }">
                    <div>{{ row.showCode }}</div>
                  </template>
                </vxe-column>
                <vxe-column field="id" title="半成品Id" width="135" v-if="false" fixed='left'></vxe-column>
                <vxe-column field="consumableImg" title="耗材图片" width="90" align="left">
                  <template #default="{ row }">
                    <el-image style="width: 50px; height: 50px" :src="row.consumableImg"
                      :preview-src-list="[row.consumableImg]">
                    </el-image>
                  </template>
                </vxe-column>
                <vxe-column field="consumableProductCode" title="耗材编码" width="140" min-width="125">
                  <template #default="{ row }">
                    <div class="relativebox">
                      <!-- <el-tooltip effect="dark" :content="row.consumableProductCode" placement="top-start"> -->
                      <div class="textover" style="width: 69%;">{{ row.consumableProductCode }}</div>
                      <!-- </el-tooltip> -->

                      <div class="copyhover" @click="copytext(row.consumableProductCode)">
                        <i class="el-icon-document-copy"></i>
                      </div>
                    </div>
                  </template>
                </vxe-column>
                <vxe-column field="consumableProductName" title="耗材名称" width="350" min-width="350"></vxe-column>
                <vxe-column field="consumableQuantity" title="所用数量" width="100"></vxe-column>
                <vxe-column field="age" title="操作">
                  <template #default="{ row }">
                    <!-- <el-button type="primary" @click="workshowfuc(row, 3)">收货记录</el-button> -->
                    <el-button type="primary" icon="el-icon-picture-outline"  @click="consumableshowupfuc(row)" :disabled="isCopy"></el-button>
                  </template>
                </vxe-column>
              </vxe-table>
            </div>
            </el-row>
          </template>
        </vxe-column>
         <!-- 标记 -->
         <vxe-column field="isMark" title="" width='28' fixed='left'>
                <template #default="{ row }">
                    <template v-if="row.isMark == false">
                        <span></span>
                    </template>
                    <template v-else>
                        <i class="vxe-icon-dot" style="color: #f56c6c;"></i>
                    </template>
                </template>
            </vxe-column>
        <vxe-column field="finishedProductCode" title="成品编码" width="125" align="left" min-width="125">
          <template #header>
            <span>成品编码<i class="el-icon-document-copy" style="margin-left: 10px; cursor: pointer;" @click="productcodecopy"></i></span>
          </template>
          <template #default="{ row }">
            <div class="relativebox">
              <!-- <el-tooltip effect="dark" :content="row.finishedProductCode" placement="top-start" :disabled="!isShowTooltip"> -->
              <div class="textover" style="width: 69%;">{{ row.finishedProductCode }}</div>
              <!-- </el-tooltip> -->

              <div class="copyhover" @click="copytext(row.finishedProductCode)">
                <i class="el-icon-document-copy"></i>
              </div>
              <div class="addhover" @click="addNumber(row.packagesProcessingId, row.finishedProductQuantity)">
                <i class="el-icon-circle-plus-outline"></i>
              </div>
            </div>
          </template>
        </vxe-column>

        <vxe-column field="finishedProductName" title="成品名称" width="250" align="left" min-width="125">
          <template #default="{ row }">
            <div class="relativeboxx">
              <!-- <el-tooltip effect="dark" :content="row.finishedProductName" placement="top-start" :disabled="!isShowTooltip"> -->
              <div @click="doubleclick(row)" class="point  textover" style="width: 80%;">{{ row.finishedProductName }}
              </div>
              <!-- </el-tooltip> -->
              <div v-show="row.isMark" class="positioncenter"><el-badge is-dot class="item"></el-badge></div>
            </div>
          </template>
        </vxe-column>
        <vxe-column  align="left" width="55" field="sourceType" title="来源"></vxe-column>
        <vxe-column field="urgencyDegreeName" title="紧急状态" width="80" align="left">
          <template #default="{ row }">
            <el-button :disabled="!checkPermission('api:Inventory:PackagesProcessing:EdUrgencyDegree') ||isCopy" v-if="row.urgencyDegreeName == '加急'"
              :type="row.urgencyDegreeName == '加急' ? 'danger' : row.urgencyDegreeName == '待审' ? 'primary' : ''"
              @click="updateDegree(row)">{{ row.urgencyDegreeName }}</el-button>
            <el-button v-else-if="row.urgencyDegreeName == '待审'"
              :type="row.urgencyDegreeName == '加急' ? 'danger' : row.urgencyDegreeName == '待审' ? 'primary' : ''"
              :disabled="!checkPermission('api:Inventory:PackagesProcessing:EdUrgencyDegree') || isCopy" @click="updateDegree(row)">{{
                checkPermission('api:Inventory:PackagesProcessing:EdUrgencyDegree') ? '审核' : '待审' }}</el-button>
            <el-button :disabled="!checkPermission('api:Inventory:PackagesProcessing:EdUrgencyDegree') ||isCopy" v-else-if="row.urgencyDegreeName == '压单'"
              :type="row.urgencyDegreeName == '加急' ? 'danger' : row.urgencyDegreeName == '压单' ? 'warning' : row.urgencyDegreeName == '待审' ? 'primary' : ''"
              @click="updateDegree(row)">{{ row.urgencyDegreeName }}
            </el-button>
            <el-button :disabled="!checkPermission('api:Inventory:PackagesProcessing:EdUrgencyDegree') ||isCopy" v-else @click="updateDegree(row)">{{ row.urgencyDegreeName }}</el-button>
          </template>
        </vxe-column>

        <vxe-column field="brandName" title="品牌" width="60" align="left">
          <template #content="{ row }">
            <vxe-input v-model="row.brandName" type="text"></vxe-input>
          </template>
        </vxe-column>

        <!-- 备注 -->
        <vxe-column field="remark" title="备注" width='45'>
          <template #default="{ row }">
            <el-popover style="overflow-y: hidden;" v-if="row.remarkList.length > 0" placement="right" trigger="hover"
              width="500">
              <el-card class="box-card">
                <div
                  style="display: flex; flex-direction: column; max-height: 250px; min-height: 60px; overflow-y: auto;">
                  <div v-for="(item, i) in row.remarkList" :key="i" style="border: 1px solid #fff;">
                    <span style="font-weight: 600;">{{ i + 1 }}</span>、{{ item }}
                  </div>
                </div>

              </el-card>

              <i class="vxe-icon-flag-fill" style="font-size: 14px" slot="reference"
                :style="row.remarkCssName == '1' ? { color: '#F56C6C' } : { color: '#dcdfe6' }"
                @click="openTaskRmarkInfo(row)"></i>
            </el-popover>

            <i class="vxe-icon-flag-fill" style="font-size: 15px" v-else slot="reference"
              :style="row.remarkCssName == '1' ? { color: '#F56C6C' } : { color: '#dcdfe6' }"
              @click="openTaskRmarkInfo(row)"></i>

          </template>
        </vxe-column>


        <vxe-column title="合格证" width="60" align="left">
          <template #default="{ row }">
            <i class="el-icon-document" style="font-size: 15px"
              :style="row.noCertificate ? { color: '#dcdfe6' } : (row.certificateInfo || row.certificateUrl) ? { color: '#409eff' } : { color: '#E6A23C' }"
              @click="qualificationshowfuc(row)"></i>
          </template>
        </vxe-column>
        <vxe-column field="machineTypeName" title="机型" width="66" align="left">
          <template #content="{ row }">
            <vxe-input v-model="row.machineTypeName"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column field="finishedProductQuantity" title="所需成品" width="72" align="left">
          <template #content="{ row }">
            <vxe-input v-model="row.finishedProductQuantity" type="text"></vxe-input>
          </template>
        </vxe-column>

        <!-- <vxe-column field="processer" title="加工员" width="50" align="center">
          <template #default="{ row }">
            <el-button type="text" @click="tableeditfuc(row, 1)" size="mini">{{ row.processer }}</el-button>
          </template>
        </vxe-column>
        <vxe-column title="加工记录" width="78" align="left">
          <template #default="{ row }">
            <div class="minibtn"><el-button type="primary" @click="workshowfuc(row, 1)" size="mini">加工记录</el-button></div>
          </template>
        </vxe-column>
        <vxe-column field="processDate" title="加工日期" width="68" align="left">
          <template #default="{ row }">
            <span>{{ formatIsCommission(row.processDate) }}</span>
          </template>
        </vxe-column>

        <vxe-column field="totalProcessQuantity" title="加工数量" width="85" align="left" show-footer-overflow="tooltip">
          <template #default="{ row }">
            <div type="text" :style="(row.totalProcessQuantity>=row.finishedProductQuantity)?{color:'#67C23A'}:{}" >{{ row.totalProcessQuantity }}</div>
          </template>
        </vxe-column>
        <vxe-column field="workPrice" title="加工工价" width="70" v-if="checkPermission('api:Inventory:PackagesProcessing:ProcessWorkPrice')">
          <template #default="{ row }">
            <el-button type="text" style="color: #999" @click="changepriceshow(row, 1)" size="mini">{{
              row.workPrice ? row.workPrice : 0 }}</el-button>
          </template>
        </vxe-column> -->
        <vxe-column field="dispatcher" title="加工员" width="62" align="center">
          <template #default="{ row }">
            <el-button type="text" @click="tableeditfuc(row, 6)" size="mini">{{ row.dispatcher }}</el-button>
          </template>
        </vxe-column>
        <vxe-column field="date" title="加工记录" width="80" align="left" >
          <template #default="{ row }">
            <div class="minibtn"><el-button type="primary" @click="workshowfuc(row, 2)" size="mini" :disabled="isCopy">加工记录</el-button></div>
          </template>
        </vxe-column>
        <vxe-column field="dispatchDate" title="加工日期" width="68" align="left">
          <template #default="{ row }">
            <!-- <el-button type="text" @click="tableeditfuc(row, 2)" size="mini">{{ formatIsCommission(row.dispatchDate) }}</el-button> -->
            <div>{{ formatIsCommission(row.dispatchDate) }}</div>
          </template>
        </vxe-column>
        <vxe-column field="totalDispatchQuantity" title="加工数量" width="72" align="left">
          <template #default="{ row }">
            <div :style="(row.totalDispatchQuantity >= row.finishedProductQuantity) ? { color: '#67C23A' } : {}">{{
              row.totalDispatchQuantity }}</div>
          </template>
        </vxe-column>
        <vxe-column field="dispatchWorkPrice" title="加工工价" width="75"
          v-if="checkPermission('api:Inventory:PackagesProcessing:ProcessWorkPrice')">
          <template #default="{ row }">
            <el-button type="text" :style="{ color: row.isMatchPriceTemp ? '#ebc8a0' : '#999' }" @click="changepriceshow(row, 2)" size="mini">{{
              row.dispatchWorkPrice ? row.dispatchWorkPrice : 0 }}</el-button>
            <!-- <vxe-input @change="changeprice(2,$event)" @blur="subchange(2,row)" v-model="row.dispatchWorkPrice" type="text"></vxe-input> -->
          </template>
        </vxe-column>
        <vxe-column field="totalDispatchQuantity" title="保存状态" width="72" align="left">
          <template #default="{ row }">
            <div :style="row.saveType == 1 ? { color: '#67C23A' } : {}">{{
              row.saveType == 1 ? '是':row.saveType == 0 ? '否':'' }}</div>
          </template>
        </vxe-column>
        <vxe-column field="allotQuantity" title="调出数量" width="70">
          <template #default="{ row }">
            <!-- <el-button type="text"   size="mini">{{
              row.allotQuantity ? row.allotQuantity : 0 }}</el-button> -->
            <div>{{ row.allotQuantity ? row.allotQuantity : 0 }}</div>
          </template>
        </vxe-column>
        <vxe-column field="typeStr" title="类型" width="66" align="left">
          <template #content="{ row }">
            <vxe-input v-model="row.typeStr" type="text"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column field="consumableProductName" title="包装耗材" width="70" align="left">
          <template #content="{ row }">
            <vxe-input v-model="row.consumableProductName" type="text"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column field="consumableLength" title="长" width="45" align="left">
          <template #content="{ row }">
            <vxe-input v-model="row.consumableLength" type="text"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column field="consumableWide" title="宽" width="45" align="left">
          <template #content="{ row }">
            <vxe-input v-model="row.consumableWide" type="text"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column field="halfProcessWarehouseName" title="半成品加工仓" width="125" align="left">
          <template #content="{ row }">
            <vxe-input v-model="row.halfProcessWarehouseName" type="text"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column field="packingMaterialName" title="包装材料" width="75" align="left">
          <template #content="{ row }">
            <vxe-input v-model="row.packingMaterialName" type="text"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column field="packageSizeName" title="包装尺寸" width="70" align="left">
          <template #content="{ row }">
            <vxe-input v-model="row.packageSizeName" type="text"></vxe-input>
          </template>
        </vxe-column>

        <vxe-column field="combineCode" title="组合编码" width="110" align="left">
          <template #default="{ row }">
            <el-tag type="danger" v-if="row.combineCode == '多编码组合'">{{ row.combineCode }}</el-tag>
            <el-tag type="success" v-else-if="row.combineCode == '单编码多装'">{{ row.combineCode }}</el-tag>
            <el-tag type="primary" v-else-if="row.combineCode == '单编码单装'">{{ row.combineCode }}</el-tag>
          </template>
          <template #footer>
            <el-tooltip effect="dark" :content="summaryarry.combineDetialCode_sum" placement="top">
              <span>
               {{summaryarry.combineCode_sum}}
              </span>
            </el-tooltip>
            </template>
        </vxe-column>

        <vxe-column field="taskStatusName" title="任务状态" width="72" align="left">
          <template #default="{ row }">
            <!-- <el-tag :type="row.taskStatusName=='已完成'?'success':'info'">{{ row.taskStatusName }}</el-tag> -->
            <el-tag :type="row.taskStatusName == '已完成' ? 'success' : 'info'" effect="dark">{{ row.taskStatusName
            }}</el-tag>
            <!-- <div class="minisize">
              <vxe-button :status="row.taskStatusName=='已完成'?'success':'info'" size="mini" :content="row.taskStatusName"></vxe-button>
            </div> -->
          </template>
        </vxe-column>
        <vxe-column field="completeDate" title="完成日期" width="62" align="left">
          <template #default="{ row }">
            <span type="text">{{ formatIsCommission(row.completeDate) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="createdUserName" title="创建人" width="60" align="left">
          <template #content="{ row }">
            <vxe-input v-model="row.createdUserName" type="text"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column field="createdTime" title="创建日期" width="70" align="left">
          <template #default="{ row }">
            <el-tooltip :content="row.createdTime" placement="top">
              <span type="text">{{ formatIsCommission(row.createdTime) }}</span>
            </el-tooltip>
          </template>
        </vxe-column>

        <vxe-column title="操作" width="90" align="left">
          <template #default="{ row }">
            <my-confirm-button type="submit" @click="endtask(row)" :disabled="isCopy">完成</my-confirm-button>
          </template>
        </vxe-column>

      </vxe-table>
    </div>


    <!--查看备注------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
    <el-dialog title="查看备注" :show-close="false" :visible.sync="viewReferenceRemark" width="60%" close-on-click-modal
      element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
      <packdesgtaskRemark ref="packdesgtaskRemark" :rowinfo="rowinfo" v-if="viewReferenceRemark"></packdesgtaskRemark>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="viewReferenceRemark = false">关 闭</el-button>
          <my-confirm-button type="submit" :loading="shootingTaskRemarkrawer" @click="sumbitshootingTaskRemark" />
        </span>
      </template>
    </el-dialog>

    <!--提交审批------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
    <el-dialog title="调拨申请" :show-close="false" :visible.sync="shenpishow" width="420px" close-on-click-modal
      element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
      <div style="height: 35px;box-sizing: border-box;padding:0 25px;border-bottom: 1px solid #ebeef5;">
        <div style="font-size: 16px;color:#666;">调拨申请</div>
        <!-- <vxe-input style="margin: 10px;" v-model="pricelist.price" type="text"></vxe-input> -->
      </div>
      <div style="box-sizing: border-box;padding:35px 35px;">
        <div style="width:100%;margin:15px auto 70px auto;">
          <vxe-select style="width:100%" placeholder="请选择仓库" @change="storagechange" v-model="diaobodata.wareId"
            :clearable="true" :collapse-tags="true" filterable>
            <vxe-option v-for="item in storagelist" :key="item.wms_co_id" :label="item.name" :value="item.wms_co_id" />
          </vxe-select>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="shenpishow = false">取消</el-button>
          <el-button @click="approvefuc" v-throttle="3000" type="primary" v-loading="btnloading">保存</el-button>
          <!-- <my-confirm-button type="submit"  @click="subchange" /> -->
        </span>
      </template>
    </el-dialog>

    <!-- 价格 -->
    <el-dialog title="更改价格" :show-close="false" :visible.sync="priceshow" width="420px" close-on-click-modal
      element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
      <div style="height: 35px;box-sizing: border-box;padding:0 25px;border-bottom: 1px solid #ebeef5;">
        <div style="font-size: 16px;color:#666;">编辑{{ pricetitle }}</div>
        <!-- <vxe-input style="margin: 10px;" v-model="pricelist.price" type="text"></vxe-input> -->
      </div>
      <div style="box-sizing: border-box;padding:35px 35px;"><el-input-number style="width: 100%;"
          v-model="pricelist.price" :precision="5" :step="0.1" :min="0" :max="100000" :controls="false" :disabled="isCopy"></el-input-number>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="priceshow = false">取消</el-button>
          <el-button @click="subchange" type="primary">保存</el-button>
          <!-- <my-confirm-button type="submit"  @click="subchange" /> -->
        </span>
      </template>
    </el-dialog>


    <vxe-modal v-model="qualificationshow" mask-closable title="合格证信息">
      <template #default>
        <vxe-textarea v-model="qualfifter.msg" :disabled="disabled" placeholder="" :autosize="{ minRows: 6, maxRows: 10 }"
          clearable :maxlength=300>
        </vxe-textarea>
        <div class="qualibtn" style="width: 100%;">
          <div><el-button style="width:100%;" size="mini" @click="copytext(qualfifter.msg)">复制内容</el-button></div>
          <div>
            <upfile ref="refupfile" @geturl="geturl" :downmsg="qualfifter"></upfile>
          </div>

          <el-button style="width:49%;margin-right:2%;" type="info" size="mini"
            @click="qualificationedit('不加合格证')">无合格证</el-button>
          <el-button style="width:49%;" v-if="disabled" size="mini" type="primary" @click="qualfieditfuc">编辑</el-button>

          <div v-else>
            <div class="flexrow">
              <div class="marginrt">
                <el-button width="200px" size="medium" @click="disabled = true">取消</el-button>
              </div>
              <el-button width="200px" size="medium" type="primary" @click="savecertificateInfo">保存</el-button>
            </div>
          </div>
        </div>
      </template>
    </vxe-modal>



    <vxe-modal v-model="workshow" mask-closable :title="recordTitle"
      @close="taskroww.selUserId = null" transfer>
      <template #default>
        <div style="width:100%;">
          <div style="width:100%;margin:15px auto;">
            <!-- //隐藏元素bug -->
            <div style="display: none;">{{ userid }}</div>
            <div style="margin-top: 10px;" v-if="isnei">
              <div style="width:100%;margin-top:-15px;margin-bottom:15px;display:flex;justify-content:center;">
                <el-radio v-model="taskrow.classType" :label="0" border size="mini">白班</el-radio>
                <el-radio v-model="taskrow.classType" :label="1" border size="mini">夜班</el-radio>
              </div>
              <vxe-select :disabled="!checkPermission('api:Inventory:PackagesProcessing:EditPerson')" style="width:100%;"
                @change="userselect" :value="fiftername(taskrow.createPackId)" size="" placeholder="请选择人员" filterable>
                <vxe-option v-for="(uitem, i) in userlist" :key="i" :value="uitem.userId"
                  :label="uitem.userName"></vxe-option>
              </vxe-select>
              <div style="width:100%;margin:10px auto;">
                <vxe-input controls-position="right" @change="qualityinput" style="width:100%;" v-model="taskrow.quantity"
                  placeholder="请输入数量" type="integer" :step="1" :min="1" :max="10000" size=""></vxe-input>
              </div>
              <div style="height: 10px;"></div>
              <vxe-select disabled v-if="typeId == 6" style="width:100%;" :value="taskrow.quatilyUserName" size=""
                placeholder="请选择人员" filterable>
              </vxe-select>
              <vxe-select v-if="typeId == 4" style="width:100%" placeholder="请选择仓库" @change="storagechange"
                v-model="outlist.wareId" :clearable="true" :collapse-tags="true" filterable>
                <vxe-option v-for="item in storagelist" :key="item.wms_co_id" :label="item.name"
                  :value="item.wms_co_id" />
              </vxe-select>
            </div>

            <div v-else>
              <div style="width:100%;margin-top:-15px;margin-bottom:15px;display:flex;justify-content:center;">
                <el-radio v-model="taskroww.classType" label="0" border size="mini">白班</el-radio>
                <el-radio v-model="taskroww.classType" label="1" border size="mini">夜班</el-radio>
              </div>
              <vxe-select :disabled="!checkPermission('api:Inventory:PackagesProcessing:EditPerson')" style="width:100%;"
                @change="userselect" @blur="userselect" :value="fiftername(taskroww.selUserId)" size=""
                placeholder="请选择人员" filterable>
                <vxe-option v-for="(uitem, i) in userlist" :key="i" :value="uitem.userId"
                  :label="uitem.userName"></vxe-option>
              </vxe-select>
              <div style="width:100%;margin:10px auto;">
                <vxe-input controls-position="right" @change="qualityinput" style="width:100%;" v-model="taskrow.quantity"
                  placeholder="请输入数量" type="integer" :step="1" :min="1" :max="10000" size=""></vxe-input>
              </div>
              <div style="width:100%;margin-top:10px;">
                <vxe-input controls-position="right" style="width:100%;" v-model="taskroww.boxNum" placeholder="请输入箱数"
                  v-if="typeId == 6 || typeId == 2" type="integer" :step="1" :min="1" :max="200" size=""></vxe-input>
              </div>

              <div style="height: 10px;"></div>

              <vxe-select disabled v-if="typeId == 6 || typeId == 2" style="width:100%;" @change="userselect"
                :value="loginuser.userName" size="" placeholder="请选择人员" filterable>
              </vxe-select>
            </div>
          </div>
          <div style="height:5px;"></div>
        </div>
        <div class="qualibtn" style="width: 100%;display: flex; flex-direction: column;" v-if="isnei">
          <div class="flexrow">
            <div class="marginrt">
              <el-button width="200px" size="medium" @click="workshow = false">取消</el-button>
            </div>
            <el-button width="200px" size="medium" type="primary" @click="neisaveworkshow" v-throttle="3000"
              v-loading="btnloading">保存</el-button>
          </div>
        </div>

        <div class="qualibtn" style="width: 100%;display: flex; flex-direction: column;" v-else>
          <div class="flexrow">
            <div class="marginrt">
              <el-button width="200px" size="medium" @click="workshow = false">取消</el-button>
            </div>
            <el-button width="200px" size="medium" type="primary" @click="saveworkshow" v-throttle="3000"
              v-loading="btnloading">保存</el-button>
          </div>
        </div>
      </template>
    </vxe-modal>

    <el-drawer :visible.sync="tableeditshow"  close-on-click-modal append-to-body direction="btt" z-index="99" style="z-index:99"
      :size="typeId == 6 ? '700px' : '980px'" element-loading-text="拼命加载中" v-loading="addLoading" :show-close="false">
      <template #default>
        <div>
          <div class="bzbjbt">
            <span style="float: left">{{ tableedittitle }}</span>
          </div>
          <div class="rwmc">
            <div class="xh" style="width: 8%">{{ (typeId == 3 || typeId == 4) ? titlerow.showCode :
              titlerow.packagesProcessingId }}</div>
            <div class="mc" style="width: 2%">|</div>
            <div class="mc" style="width: 70%">{{ (typeId == 3 || typeId == 4) ? titlerow.halfProductName :
              titlerow.finishedProductName }}</div>
            <div class="icon" style="width: 10%;text-align: right;" v-if="typeId == 6">
              <el-button type="primary" plain @click="onetoincang" :disabled="allshippingMark || !checkPermission('packprocessedit:addstock')"
                :visible.sync="shippingmarkshow" v-throttle="3000">一键入库</el-button>
            </div>
            <div class="icon" style="text-align: right;" :style="typeId == 6 ? { width: '5%' } : { width: '20%' }">
              <span v-if="typeId == 6"><el-button v-if="checkPermission('packprocessedit:allot')"
                  type="primary" @click="onediaobo">一键调拨</el-button></span>
              <span v-if="typeId == 4"><el-button v-if="checkPermission('packprocessedit:allot')"
                  type="primary" @click="onediaobo">一键调拨</el-button></span>
            </div>
          </div>
          <div class="box-card tablehei">
            <!-- //TODO:点开弹层没有数据 -->
            <vxe-table height="100%" resizable border="default" :key="tokey" :align="allAlign2" :data="tableData2"
              show-footer :footer-method="footerMethod1" @checkbox-all="neitablesel" @checkbox-change="neitablesel"
              :seq-config="{ seqMethod }">
              <vxe-column type="checkbox" v-if="typeId == 2 || typeId == 4 || typeId == 6" width="40"></vxe-column>
              <vxe-column type="seq" width="40"></vxe-column>
              <vxe-column field="createUserName" title="姓名"  v-if="typeId != 6">
              </vxe-column>
              <vxe-column field="createUserName" title="加工员"  v-else width="80">
              </vxe-column>
              <vxe-column field="classTypeStr" title="班次" width="75">
              </vxe-column>
              <vxe-column field="quantity" title="数量" width="75">
                <template #default="{ row }">
                  <div style="margin-top: 0px;">{{ row.quantity }}</div>
                </template>
              </vxe-column>

              <vxe-column field="createDate" title="加工日期"  v-if="typeId == 6" width="95">
                <template #default="{ row }">
                  <div class="flexrow">
                    <el-tooltip effect="dark" :content="row.createDate" placement="top">
                      <div> {{ formatIsCommission(row.createDate) }}</div>
                    </el-tooltip>

                  </div>
                </template>
              </vxe-column>
              <vxe-column field="processWorkTimes" title="加工时长" width="110"></vxe-column>
              <vxe-column field="quatilyUserName" title="调配员"  v-if="typeId == 6" width="85"></vxe-column>
              <vxe-column title="调配工价" width="85" v-if="tabel == 3 || tabel == 4">
                <template #default="{ row }">
                    <div>
                      <el-popover style="overflow-y: hidden;" placement="right" trigger="hover"
                          width="350">
                          <el-card class="box-card" style="overflow-y: hidden;">
                            <div class="tooltip-content" style="overflow-y: hidden;">
                              <div class="tooltip-item">调配工价(个人)<br/>{{ row.deployPricePersonal }}</div>
                              <div class="tooltip-item">调配工价(全体)<br/>{{ row.deployPriceAll }}</div>
                              <div class="tooltip-item">打标工价<br/>{{ row.markPrice }}</div>
                            </div>
                            <span slot="reference">
                              {{ row.deployPricePersonal + row.deployPriceAll + row.markPrice }}
                            </span>
                          </el-card>
                          <span slot="reference">
                             <div style="margin-top: 0px;"> {{ row.deployPricePersonal + row.deployPriceAll + row.markPrice }}</div>
                            </span>
                        </el-popover>
                    </div>
                </template>
              </vxe-column>
              <vxe-column field="workPrice" title="工价" v-if="typeId == 1 || typeId == 2 || typeId == 3" width="80">
              </vxe-column>
              <vxe-column field="totalWorkPrice" title="合计"  v-if="typeId == 1 || typeId == 2 || typeId == 3" width="80">
              </vxe-column>
              <vxe-column field="shippingMark" title="箱唛"  v-if="typeId == 6" width="70">
                <template #default="{ row }">
                  <div class="flexrow statuscss">
                    <el-tag @click="printpage(row, 1)" style="cursor: pointer;"
                      :color="row.shippingMark == '否' ? '#909399' : row.shippingMark == '是' ? '#67C23A' : '#ffffff'"
                      effect="dark">{{ row.shippingMark }}</el-tag>
                  </div>
                </template>
              </vxe-column>
              <vxe-column field="printDate" title="打印日期" v-if="typeId == 6" width="110">
                <template #default="{ row }">
                  <div class="flexrow">
                    <el-tooltip effect="dark" :content="`${row.printer}+${row.printDate}`" placement="top">
                      <div> {{ formatIsCommission(row.printDate) }}</div>
                    </el-tooltip>

                  </div>
                </template>
              </vxe-column>
              <!-- <vxe-column field="printer" title="打印人" width="70" v-if="typeId == 6">
              </vxe-column> -->




              <vxe-column field="createDate" title="日期"  v-else>
                <template #default="{ row }">
                  <div class="flexrow">
                    <el-tooltip effect="dark" :content="row.createDate" placement="top">
                      <div> {{ formatIsCommission(row.createDate) }}</div>
                    </el-tooltip>
                  </div>
                </template>
              </vxe-column>
              <!-- <vxe-column field="storageTime" title="出库时间" width="75" v-if="typeId == 6">
                <template #default="{ row }">
                  <div class="flexrow">
                    <el-tooltip effect="dark" :content="row.allotDate" placement="top">
                     <div> {{ formatIsCommission(row.allotDate) }}</div>
                    </el-tooltip>

                  </div>
                </template>
              </vxe-column> -->
              <vxe-column field="finishTranWarehouseName" title="成品调入仓"  v-if="typeId == 6" width="155"></vxe-column>
              <vxe-column field="allotDate" title="调拨日期"  v-if="typeId == 6" width="90">
                <template #default="{ row }">
                  <div class="flexrow">
                    <el-tooltip effect="dark" :content="row.allotDate" placement="top">
                      <div> {{ formatIsCommission(row.allotDate) }}</div>
                    </el-tooltip>

                  </div>
                </template>
              </vxe-column>
              <vxe-column field="allotWorkTimes" title="调拨时长" width="95"></vxe-column>
              <vxe-column field="state" title="状态"  v-if="typeId == 6 || typeId == 4 || typeId == 2" width="125">
                <template #default="{ row }">
                  <div class="flexrow statuscss">
                    <el-tag
                      :color="row.state == '已撤销' ? '#909399' : row.state == '审批中' ? '#409EFF' : row.state == '已完成' ? '#67C23A' : row.state == '已拒绝' ? '#F56C6C' : '#ffffff'"
                      effect="dark">{{ row.state }}</el-tag>
                  </div>
                </template>
              </vxe-column>
              <vxe-column field="age" title="操作" >
                <template #default="{ row }">
                  <div class="flexrow" v-if="typeId == 1">
                    <div class="flexrow" v-if="checkPermission('api:Inventory:PackagesProcessing:ProcessOp')">
                      <el-button plain :disabled="row.state == '审批中'" @click="workshowfuc(row, 5, 'nei')">编辑</el-button>
                      <el-button type="danger" :disabled="row.state == '审批中' || row.state == '审批完'"
                        style="margin-left: 10px;" @click="delRecord(row)">删除</el-button>
                    </div>

                  </div>
                  <div class="flexrow" v-else-if="typeId == 3">
                    <div class="flexrow" v-if="checkPermission('api:Inventory:PackagesProcessing:DeliveryOp')">
                      <el-button plain :disabled="row.state == '审批中'" @click="workshowfuc(row, 5, 'nei')">编辑</el-button>
                      <el-button :disabled="row.state == '审批中'" type="primary" @click="shnpishowfuc(row, 5, 'nei')"
                        v-if="typeId == 6 || typeId == 4">调拨</el-button>
                      <el-button type="danger" :disabled="row.state == '审批中' || row.state == '审批完'"
                        style="margin-left: 10px;" @click="delRecord(row)">删除</el-button>
                    </div>

                  </div>
                  <div class="flexrow" v-else-if="typeId == 6">
                    <div class="flexrow">
                      <el-button plain :disabled="row.state == '审批中' || !checkPermission('packprocessedit:edit')" @click="workshowfuc(row, 5, 'nei')">编辑</el-button>
                      <el-button plain :disabled="row.shippingMark == '是' || !checkPermission('packprocessedit:addstock')" v-throttle="3000"
                        :visible.sync="shippingmarkshow" @click="printpage(row, null)">入库装箱</el-button>
                      <el-button :disabled="row.state == '审批中' || !checkPermission('packprocessedit:allot')" type="primary" @click="shnpishowfuc(row, 5, 'nei')"
                        v-if="typeId == 6 || typeId == 4">调拨</el-button>
                      <el-button type="danger" :disabled="row.state == '审批中' || row.state == '审批完' || !checkPermission('packprocessedit:del')"
                        style="margin-left: 10px;" @click="delRecord(row)">删除</el-button>
                    </div>

                  </div>
                  <div class="flexrow" v-else>
                    <el-button plain :disabled="row.state == '审批中'" @click="workshowfuc(row, 5, 'nei')">编辑</el-button>
                    <el-button :disabled="row.state == '审批中'" type="primary" @click="shnpishowfuc(row, 5, 'nei')"
                      v-if="typeId == 6 || typeId == 4">调拨</el-button>
                    <el-button type="danger" :disabled="row.state == '审批中' || row.state == '审批完'"
                      style="margin-left: 10px;" @click="delRecord(row)">删除</el-button>
                  </div>

                </template>
              </vxe-column>
            </vxe-table>
          </div>
        </div>
      </template>
    </el-drawer>
    <!-- e.packagesProcessingId,this.versionId -->
    <el-drawer :visible.sync="editTaskshow" wrapperClosable :close-on-click-modal="true" direction="rtl" size="800px"
      element-loading-text="拼命加载中" v-loading="addLoading" :show-close="false">
      <dialogright v-if="editTaskshow" :editTaskshow="editTaskshow" ref="refdialogright"
        :packagesProcessingId="packagesProcessingId" :versionId="versionId" @getTaskList="getTaskList"
        style="height: 100%;width:100%" @onCloseAddForm="onCloseAddForm" :isCopy="isCopy" :islook="islook"></dialogright>
      <span slot="title" style="height: 0px;"></span>
    </el-drawer>

    <el-dialog title="文件上传" :visible.sync="updialogVisible" width="30%" v-dialogDrag :close-on-click-modal="true">
      <div style="padding: 30px;">
        <el-row>
          <el-col :span="12" :push="10">
            半成品图
            <!-- <el-form-item prop="purImageUrl" label="出厂图"> -->
            <yh-img-upload :value.sync="upForm.halfProductImg" ref="supplier_id" :limit="1" keys="one"></yh-img-upload>
            <!-- </el-form-item> -->
          </el-col>
          <!-- <el-col :span="12" :push="2">
            所需图
            <yh-img-upload :value.sync="upForm.requiredImg" ref="supplier_id" :limit="1" keys="two"></yh-img-upload>
          </el-col> -->
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="updialogVisible = false">取 消</el-button>
        <el-button type="primary" v-throttle="200" @click="upfinish">上 传</el-button>
      </span>
    </el-dialog>

    <el-dialog title="文件上传" :visible.sync="consumabledialogVisible" width="30%" v-dialogDrag :close-on-click-modal="true">
      <div style="padding: 30px;">
        <el-row>
          <el-col :span="12" :push="10">
            耗材图片
            <!-- <el-form-item prop="purImageUrl" label="出厂图"> -->
            <yh-img-upload :value.sync="consumableupForm.consumableImg" ref="conpplier_id" :limit="1" keys="one"></yh-img-upload>
            <!-- </el-form-item> -->
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="consumabledialogVisible = false">取 消</el-button>
        <el-button type="primary" v-throttle="200" @click="consumableupfinish">上 传</el-button>
      </span>
    </el-dialog>

    <el-dialog title="预览" :visible.sync="previewshow" width="25%" v-dialogDrag v-loading="printloading"
      :close-on-click-modal="true">
      <!-- 预览 -->
      <print-preview ref="preView" />
    </el-dialog>
    <!-- 创建加工 -->
    <vxe-modal title="创建加工" v-model="packagesProceVisible" size="mini" :maskClosable="true">
      <template #default>
        <span style="margin: 0 20px;">成品数量</span>
        <el-input-number style="width: 60%;" v-model="packagesProce.finishedProductQuantity" :controls="false" :min="1"
          label="成品数量"></el-input-number>
        <div class="qualibtn" style="width: 100%;display: flex; flex-direction: column;">
          <div class="flexrow">
            <div class="marginrt">
              <el-button width="200px" size="mini" @click="packagesProceVisible = false">取 消</el-button>
            </div>
            <el-button width="200px" size="mini" type="primary" @click="submitProductQuantity" v-throttle="3000">提
              交</el-button>
          </div>
        </div>
      </template>
    </vxe-modal>
    <el-drawer title="每日产量" :visible.sync="drawerVisible" direction="btt" :append-to-body="true" size='80%' class="mine">
      <div class="sybj">
        <div class="tjnrnk">
          <div class="tjbt">
            <div style="width:100%;display: inline-block;">
              <span style="display:inline-block;margin-right: 20px;">
                <el-date-picker size="mini" style="position: relative; top: 1px; width: 300px;" type="daterange"
                  align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                  value-format="yyyy-MM-dd" v-model="filter.createdtimerange">
                </el-date-picker>
              </span>
              <span style="display:inline-block;margin-right: 20px;">
                <el-select v-model="filter.memberList" placeholder="请选择人员" multiple style="width: 150px;" collapse-tags clearable filterable>
                  <el-option v-for="item in userlist" :key="item.userId" :label="item.userName" :value="item.userId"></el-option>
                </el-select>
              </span>
              <div style="display: inline-block; margin-right: 20px; margin-bottom: 10px;width: 150px;">
                <inputYunhan ref="productCode" :inputt.sync="filter.finishProductCode" v-model="filter.finishProductCode"
                  placeholder="成品编码" :clearable="true" :clearabletext="true" :maxRows="50" :maxlength="3998" :width="'238'"
                  @callback="callbackGoodsCode" title="成品编码">
                </inputYunhan>
              </div>
              <span style="display:inline-block;margin-right: 35px;">
                <el-button size="mini" type="primary" @click="footercellclick">查询</el-button>
              </span>
              <span style="display:inline-block;margin-right: 20px;">
                <el-checkbox-group v-model="checkList" @change="Change">
                  <el-checkbox :label="item.label" v-for="item in processedWorkItems"></el-checkbox>
                </el-checkbox-group>
              </span>
            </div>
          </div>
        </div>
        <div style="margin-top: 35px;" class="nrqk">
          <buschar v-if="quantityprocessed.visible" ref="buschar" :analysisData="quantityprocessed.data">
          </buschar>
        </div>
      </div>
    </el-drawer>

    <!-- <el-dialog title="到货明细" :visible.sync="detailsDialogInfo.visible"  v-dialogDrag>
      <div class="topgroup">
        <el-button type="primary" @click="openDetails(productInfo)" style="margin-right: 10px;">刷新</el-button>
        <el-radio-group v-model="productInfo.showType" @change="openDetails(productInfo)">
          <el-radio :label="-1">显示全部</el-radio>
          <el-radio :label="1">显示绑定</el-radio>
      </el-radio-group>
      </div>
      <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :showsummary='true'
            :summaryarry='logsummaryarry'
      :tableData='taobleData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :loading="listLoading"
      style="width: 90%; height: 530px; margin: 40px 0 0;padding: 0 0 0 30px;">
    </vxetablebase>
    <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="logtotal"
                @page-change="logPagechange" @size-change="logSizechange" style="margin-top: 40px;"/>
    </el-dialog> -->

    <el-dialog title="到货明细" :visible.sync="detailsDialogInfo.visible"  v-dialogDrag
      :close-on-click-modal="true">
      <div style="padding-left: 30px;font-size: 20px;">到货明细</div>
      <div class="topgroup">
        <el-button type="primary" @click="openDetails(productInfo)" style="margin-right: 10px;">刷新</el-button>
        <el-radio-group v-model="productInfo.showType" @change="openDetails(productInfo)">
          <el-radio :label="-1">显示全部</el-radio>
          <el-radio :label="1">显示绑定</el-radio>
      </el-radio-group>
      </div>
      <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :showsummary='true'
            :summaryarry='logsummaryarry'
      :tableData='taobleData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :loading="listLoading"
      style="width: 95%; height: 530px; margin: 20px 0 0;padding: 0 0 0 30px;">
    </vxetablebase>
    <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="logtotal"
                @page-change="logPagechange" @size-change="logSizechange" style="padding: 30px 33px 30px 30px;"/>
    </el-dialog>
  </div>
</template>
<script>
import { getHistoryPackagesListInfo, exportPackagesProcessingDetialList, exportHistoryPackagesProcessingList } from '@/api/inventory/packagesSetProcessing.js';
import MyConfirmButton from "@/components/my-confirm-button";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container/noheader.vue";
import dialogright from '@/views/media/packagework/dialogright';
import printPreview from './common/preview'
import {
  getPackagesProcessingDetialListAsync, editPackagesProcessRecord, getPackagesProcessingRecordListAsync, getCallOutRecordListAsync, updateWorkPrice,
  updateCertificateBatch, updateFinishState, updateUrgencyDegree, deleteReord, getCurrentUser, editRecord, pckagesProcessingCallOut, pckagesProcessingCallIn, updateHalfProductImg,
  editCallInOutRecord, initiateApprovalCallInOut, packagesProcessQuality, getPackagesProcessingPrintList, copyPackagesProcessing,getArrivalDetails,bindArrivalDetails,getPackagesProcessingListFinishCodeAsync,updateConsoumableProductImg
} from '@/api/inventory/packagesprocess';//包装加工
import { getHistoryPackagesDetialTaskInfo,getPackagesProcessingRecordListAsync1 } from '@/api/inventory/packagesSetProcessing.js';
import { getShootOperationsGroup, getOperationsGroup, getErpUserInfoView } from '@/api/media/mediashare';
import { getUserRoleList, getShootingViewPersonAsync } from '@/api/media/ShootingVideo';
import packdesgtaskRemark from '@/views/media/packagework/packRemark';
import YhImgUpload from "@/components/upload/yh-img-upload1.vue";
import upfile from "@/views/media/packagework/upfile.vue"
import buschar from '@/components/Bus/buscharforShooting.vue';
import { getRecordUser } from '@/api/inventory/packagesprocess'
// import printData from './common/print-data'
import { defaultElementTypeProvider, hiprint } from './common/index'
import panel from './common/panel'
import fontSize from "./common/font-size.js";
import scale from "./common/scale.js";
import provider from './common/providers'
import { getStatTaskProcessList } from '@/api/inventory/packagesprocess';//每日加工数量统计
import { getShootingSetData } from '@/api/media/shootingset'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import inputYunhan from "@/components/Comm/inputYunhan";

const tableCols = [
{ istrue: true, prop: 'busType', label: '业务类型' ,align: 'left',  },
  { istrue: true, prop: 'qty', label: '出入库' , align: 'left', },
  { istrue: true, prop: 'ioDate', label: '日期',  align: 'left',},
  { istrue: true, prop: 'ioiId', label: '内部订单号' , align: 'left',},
  { istrue: true, prop: 'wmsName', label: '仓储方' , align: 'left', },
  { istrue: true, display: true, label: '操作', align: 'left',disabled:'iscopy ? true : false', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => row.isBind == true ? '取消绑定' : '绑定加工', type: 'click', handle: (that, row) => that.setShow(row) },
]
//  const startDate = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
//  const endDate = formatTime(new Date(), "YYYY-MM-DD");
let hiprintTemplate;
export default {
  name: "Users",
  // props: ['summaryarry', 'storagelist', 'versionId','tableData'],
  props: {
    id: { type: String, default: () => { return new Date().valueOf().toString() } },
    summaryarry: { type: Object, default: () => { } },
    tableData: { type: Array, default: () => [] },
    storagelist: { type: Array, default: () => [] },
    versionId: { type: String, default: '' },
    isCopy:{isCopy:Boolean,default:false},
    isHistory:{isHistory:Boolean,default:false},
    islook: false,
    productcopy: { type: Object, default: () => { } },
    tabel: { type: Number, default: 0 },
  },
  // inject: ['allsellist','alluserlist'],
  components: { buschar, MyContainer, dialogright, MyConfirmButton, packdesgtaskRemark, YhImgUpload, upfile, printPreview,vxetablebase, inputYunhan },
  filters: {
    datefifter: (date) => {
      if (!date) {
        return '';
      }
      return date.slice(2, 10);
    }
  },
  data () {
    return {
      tableData2Loading: true,//表格加载
      isselnow: false,
      isselindex: -1,
      checkList: [],
      filter: {
        memberList: [],
        createdtimerange: [],
        startDate: null,
        endDate: null,
        machineTypes: [],
        finishProductCode: null,
      },
      processedWorkItems: [],
      quantityprocessed: { visible: false, title: "", data: {} },
      column: null,
      drawerVisible: false,
      shippingmarkshow: true,
      isShowTooltip: false,
      neitableshow: false,
      packagesProcessingId: null,
      loadingg: true,
      editLoading: true,
      previewshow: false,
      printloading: false,
      allshippingMark: false,
      idshow: this.$refs.neipackageid,
      neitable: [],
      userid: '',
      value101: '',
      value20: '',
      tableedittitle: '',
      tokey: 'one',
      shenpidata: {},
      diaobodata: {},
      shenpishow: false,
      btnloading: false,
      updialogVisible: false,
      consumabledialogVisible: false,
      addLoading: true,
      tableeditshow: false,
      pageLoading: false,
      qualificationshow: false,
      disabled: true,
      workshow: false,
      editTaskshow: false,
      viewReferenceRemark: false,
      shootingTaskRemarkrawer: false,
      nohgz: false,
      editshow: true,
      qualfifter: {
        msg: ''
      },
      zhijiandata: {},
      isnei: false,
      taskroww: {},
      outlist: {},
      upForm: {},
      consumableupForm: {},
      titlerow: null,
      loginuser: {},
      tableloading: true,
      allAlign1: null,
      tableData1: [],
      allAlign2: null,
      tableData2: [],
      typeId: null,
      taskrow: null,
      allid: [],
      userlist: [],
      neiarr: [],
      recordTitle: "",
      tbPid: "",
      tbPCode: "",
      pricelist: {},
      priceshow: false,
      pricetitle: '',
      waiid: null,
      ///////////
      template: null,
      count: 1,
      // 当前纸张
      curPaper: {
        type: 'other',
        width: 80,
        height: 60
      },
      // 纸张类型
      paperTypes: {
        'A3': {
          width: 420,
          height: 296.6
        },
        'A4': {
          width: 210,
          height: 296.6
        },
        'A5': {
          width: 210,
          height: 147.6
        },
        'B3': {
          width: 500,
          height: 352.6
        },
        'B4': {
          width: 250,
          height: 352.6
        },
        'B5': {
          width: 250,
          height: 175.6
        }
      },
      // 自定义纸张
      paperPopVisible: false,
      paperWidth: '100',
      paperHeight: '50',
      //新增加工
      packagesProceVisible: false,
      packagesProce: {
        packagesProcessingId: null,
        finishedProductQuantity: 1
      },
      ////////
      detailsDialogInfo:{
        visible: false,
        data: {},
      },
      tableCols,
      taobleData:[],
      listLoading: true,
      logtotal:null,
      productInfo:{
        currentPage:1,
        pageSize:50,
        orderBy:null,
        isAsc:true,
        halfProductCode:null,
        showType:-1
      },
      that:this,
      params:{
        ioiId:null,
        qty:null,
        ioDate:null,
        isBind:null
      },
      logsummaryarry:{}
    };
  },
  created() {
        this.$nextTick(() => {
            // 手动将表格和工具栏进行关联
            this.$refs.xTable.connect(this.$refs.xToolbar)
        })
    },
  async mounted () {
    this.modelmultipleselection();
    this.ShowHideonSearch();
  },
  watch: {
    idshow (val) {
    },
  },
  computed: {
    idshowfuc () {
      return this.idshow
    }
  },
  methods: {
    //成品编码
    async callbackGoodsCode(val) {
      this.filter.finishProductCode = val;
    },
    //复制所有成品编码
    async productcodecopy(){
      const { data } = await getPackagesProcessingListFinishCodeAsync(this.productcopy)
      let textToCopy = data.join('\n');
      let textarea = document.createElement("textarea")
      textarea.value = textToCopy
      textarea.readOnly = "readOnly"
      document.body.appendChild(textarea)
      textarea.select()
      let result = document.execCommand("copy")
      if (result) {
        this.$message({
          message: '复制当前页成品编码成功',
          type: 'success'
        })
      }
      textarea.remove()
    },
    //隐藏指定列
    ShowHideonSearch(){
      let _this = this;
      _this.$nextTick(() => {
        _this.columns2 = _this.$refs.xTable.getColumns()
      })
      _this.loading = true
      setTimeout(() => {
        // 将指定列设置为隐藏状态
        _this.columns2.forEach(column => {
          if ([ 'completeDate' , 'isMark'].includes(column.property)) {
            column.visible = false
          }
        })
        if (_this.$refs.xTable) {
          _this.$refs.xTable.refreshColumn()
        }
        _this.loading = false
      }, 800)
    },
   async setShow(row){
    this.params.ioiId = row.ioiId;
    this.params.qty = row.qty;
    this.params.ioDate = row.ioDate
    this.params.isBind = !row.isBind
    this.params.halfProductCode = this.productInfo.halfProductCode
    console.log(this.params,'this.params');
    const {success} = await bindArrivalDetails(this.params)
    if (success) {
      this.openDetails(this.productInfo)
      this.$message.success('操作成功')
      this.getneireq()
    }else{
      this.$message.error('操作失败')
    }
    },
     //弹层页面数量改变
     logSizechange(val) {
            this.productInfo.currentPage = 1;
            this.productInfo.pageSize = val;
            this.openDetails(this.productInfo);
        },
        //弹层当前页改变
        logPagechange(val) {
            this.productInfo.currentPage = val;
            this.openDetails(this.productInfo);
        },
    //点击入库数量打开table
   async openDetails(row){
    this.productInfo.halfProductCode = row.halfProductCode;
      const {data,success} = await getArrivalDetails(this.productInfo)
      if(success){
        this.taobleData = data.list
        this.logtotal = data.total
        this.detailsDialogInfo.visible = true;
        this.listLoading = false
        this.logsummaryarry = data.summary
      }
    },
    async modelmultipleselection () {
      var { data } = await getShootingSetData({ setType: 16 });
      if (data?.success) {
        this.processedWorkItems = data.data.map(item => ({ label: item.sceneCode, value: item.setId }));
        this.checkList = data.data.map(item => item.sceneCode);
        this.filter.machineTypes = data.data.map(item => item.setId);
      }
    },
    Change () {
      this.filter.machineTypes = this.processedWorkItems.filter(item => this.checkList.includes(item.label)).map(item => item.value)
    },
    async footercellclick () {
      this.filter.startDate = null;
      this.filter.endDate = null;
      if (this.filter.createdtimerange && this.filter.createdtimerange.length > 1) {
        this.filter.startDate = this.filter.createdtimerange[0];
        this.filter.endDate = this.filter.createdtimerange[1];
      }
      const params = { ... this.filter }
      console.log("params", params)
      const res = await getStatTaskProcessList(params);
      res.series.map((item) => {
        item.itemStyle = {
          "normal": {
            "label": {
              "show": true,
              "position": "top",
              "textStyle": {
                "fontSize": 14
              }
            }
          }
        }

        item.emphasis = {
          "focus": "series"
        }
        item.smooth = false;
      })
      this.quantityprocessed.visible = true;
      this.quantityprocessed.data = res
      this.quantityprocessed.title = res.legend[0]
      this.$nextTick(() => {
        this.$refs.buschar.initcharts();
      });
      this.drawerVisible = true;
      // this.modelmultipleselection();
    },
    submitProductQuantity () {
      copyPackagesProcessing(this.packagesProce).then(res => {
        if (!res.success) {
          return
        } else {
          this.$message.success(res.data.data);
          this.packagesProceVisible = false;
          this.getTaskList();
        }
      })
    },
    addNumber (packagesProcessingId, finishedProductQuantity) {
      // if(this.isCopy) return//已完成页该功能是否禁用
      this.packagesProceVisible = true;
      this.packagesProce.packagesProcessingId = packagesProcessingId
      this.packagesProce.finishedProductQuantity = finishedProductQuantity
    },
    cellclick ({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {
      let _this = this;
      // if (events.column.field == 'expand') {
      //   _this.loadingg = true;
      //   _this.openexpand(events.row, null)
      // }
      if(rowIndex == _this.isselindex){
        this.isselnow = !this.isselnow;
      }else{
        this.isselnow = true
      }
      _this.isselindex = rowIndex;
      _this.loadingg = true;
      _this.openexpand(row, rowIndex)

    },
    openexpand (row, rowindex) {
      let _this = this;
      this.$emit('getfinishedProductQuantity', row.finishedProductQuantity);
      this.waiid = row.packagesProcessingId;
      _this.$nextTick(() => {
        _this.getneireq();
      })
      this.neitableshow = true
    },
    getTaskList () {
      this.tableloading = true;
      this.$emit('getTaskList');
    },
    async onetoincang () {
      this.shippingmarkshow = true;
      this.printloading = true;
      this.init();
      // let params = this.neiarr.join();
      let params = {
        ids: this.neiarr.join()
      };


      const res = await getPackagesProcessingPrintList(params);
      if (!res?.success) {
        this.printloading = false;
        return
      }
      var printData = res.data;
      this.shippingmarkshow = false;

      // let {width} = this.curPaper

      hiprintTemplate = new hiprint.PrintTemplate({
        template: panel
      })
      hiprintTemplate.print(printData, {})

      this.tableeditfuc(this.taskrow, 6);
      this.printloading = false;
    },
    updateJson () {
      hiprintTemplate.update(JSON.parse(this.jsonIn))
    },
    init () {
      hiprint.init({
        providers: [new defaultElementTypeProvider()]
      });
      // 还原配置
      hiprint.setConfig()
      // 替换配置
      hiprint.setConfig({
        optionItems: [
          fontSize,
          scale,
          function () {
            function t () {
              this.name = "zIndex";
            }

            return t.prototype.css = function (t, e) {
              if (t && t.length) {
                if (e) return t.css('z-index', e);
              }
              return null;
            }, t.prototype.createTarget = function () {
              return this.target = $('<div class="hiprint-option-item">\n        <div class="hiprint-option-item-label">\n        元素层级2\n        </div>\n        <div class="hiprint-option-item-field">\n        <input type="number" class="auto-submit"/>\n        </div>\n    </div>'), this.target;
            }, t.prototype.getValue = function () {
              var t = this.target.find("input").val();
              if (t) return parseInt(t.toString());
            }, t.prototype.setValue = function (t) {
              this.target.find("input").val(t);
            }, t.prototype.destroy = function () {
              this.target.remove();
            }, t;
          }(),
        ],
        movingDistance: 2.5,
        text: {
          tabs: [
            // 隐藏部分
            {
              // name: '测试', // tab名称 可忽略
              options: [] // 必须包含 options
            },// 当修改第二个 tabs 时,必须把他之前的 tabs 都列举出来.
            {
              name: '样式', options: [
                {
                  name: 'scale',
                  after: 'transform', // 自定义参数，插入在 transform 之后
                  hidden: false
                },
              ]
            }
          ],
          supportOptions: [
            {
              name: 'styler',
              hidden: true
            },
            {
              name: 'scale', // 自定义参数，supportOptions 必须得添加
              after: 'transform', // 自定义参数，插入在 transform 之后
              hidden: false
            },
            {
              name: 'formatter',
              hidden: true
            },
          ]
        },
        image: {
          tabs: [
            {
              // 整体替换
              replace: true,
              name: '基本', options: [
                {
                  name: 'field',
                  hidden: false
                },
                {
                  name: 'src',
                  hidden: false
                },
                {
                  name: 'fit',
                  hidden: false
                }
              ]
            },
          ],
        }
      })
      // eslint-disable-next-line no-undef
      hiprint.PrintElementTypeManager.buildByHtml($('.ep-draggable-item'));
      $('#hiprint-printTemplate').empty()
      let that = this;
      this.template = hiprintTemplate = new hiprint.PrintTemplate({
        template: panel,
        // 图片选择功能
        onImageChooseClick: (target) => {
          // 测试 3秒后修改图片地址值
          setTimeout(() => {
            // target.refresh(url,options,callback)
            // callback(el, width, height) // 原元素,宽,高
            // target.refresh(url,false,(el,width,height)=>{
            //   el.options.width = width;
            //   el.designTarget.css('width', width + "pt");
            //   el.designTarget.children('.resize-panel').trigger($.Event('click'));
            // })
            target.refresh("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAtAAAAIIAQMAAAB99EudAAAABlBMVEUmf8vG2O41LStnAAABD0lEQVR42u3XQQqCQBSAYcWFS4/QUTpaHa2jdISWLUJjjMpclJoPGvq+1WsYfiJCZ4oCAAAAAAAAAAAAAAAAAHin6pL9c6H/fOzHbRrP0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0u/SY9LS0tLS0tLS0tLS0n+edm+UlpaWlpaWlpaWlpaW/tl0Ndyzbno7/+tPTJdd1wal69dNa6abx+Lq6TSeYtK7BX/Diek0XULSZZrakPRtV0i6Hu/KIt30q4fM0pvBqvR9mvsQkZaW9gyJT+f5lsnzjR54xAk8mAUeJyMPwYFH98ALx5Jr0kRLLndT7b64UX9QR/0eAAAAAAAAAAAAAAAAAAD/4gpryzr/bja4QgAAAABJRU5ErkJggg==", {
              // auto: true, // 根据图片宽高自动等比(宽>高?width:height)
              // width: true, // 按宽调整高
              // height: true, // 按高调整宽
              real: true // 根据图片实际尺寸调整(转pt)
            })
          }, 3000)
          // target.getValue()
          // target.refresh(url)
        },
        // 自定义可选字体
        // 或者使用 hiprintTemplate.setFontList([])
        // 或元素中 options.fontList: []
        fontList: [
          { title: '微软雅黑', value: 'Microsoft YaHei' },
          { title: '黑体', value: 'STHeitiSC-Light' },
          { title: '思源黑体', value: 'SourceHanSansCN-Normal' },
          { title: '王羲之书法体', value: '王羲之书法体' },
          { title: '宋体', value: 'SimSun' },
          { title: '华为楷体', value: 'STKaiti' },
          { title: 'cursive', value: 'cursive' },
        ],
        dataMode: 1, // 1:getJson 其他：getJsonTid 默认1
        history: true, // 是否需要 撤销重做功能
        onDataChanged: (type, json) => {
          console.log(type); // 新增、移动、删除、修改(参数调整)、大小、旋转
          console.log(json); // 返回 template
        },
        onUpdateError: (e) => {
          console.log(e);
        },
        settingContainer: '#PrintElementOptionSetting',
        paginationContainer: '.hiprint-printPagination'
      });
      hiprintTemplate.design('#hiprint-printTemplate', { grid: true });
      // 获取当前放大比例, 当zoom时传true 才会有
      this.scaleValue = hiprintTemplate.editingPanel.scale || 1;
    },
    async printpage (e, typeId) {
      this.shippingmarkshow = false;
      this.init();
      let _this = this;
      this.previewshow = true;
      this.printloading = true;
      ////////////////////////////////
      // 测试, 点预览更新拖拽元素
      // hiprint.updateElementType('defaultModule.text', (type) => {
      //   type.title = '这是更新后的元素';
      //   return type
      // })
      // // 测试, 通过socket刷新打印机列表； 默认只有连接的时候才会获取到最新的打印机列表
      // hiprint.refreshPrinterList((list) => {
      //   console.log('refreshPrinterList')
      //   console.log(list)
      // });
      // 测试, 获取IP、IPV6、MAC地址、DNS
      // 参数格式：
      // 1. 类型（ip、ipv6、mac、dns、all、interface、vboxnet）
      // 2. 回调 data => {addr, e}  addr: 返回的数据 e:错误信息
      // 3. 其他参数 ...args
      hiprint.getAddress('ip', (data) => {
        console.log('ip')
        console.log(data)
      })
      hiprint.getAddress('ipv6', (data) => {
        console.log('ipv6')
        console.log(data)
      })
      hiprint.getAddress('mac', (data) => {
        console.log('mac')
        console.log(data)
      })
      hiprint.getAddress('dns', (data) => {
        console.log('dns')
        console.log(data)
      })
      hiprint.getAddress('all', (data) => {
        console.log('all')
        console.log(data)
      })
      //
      hiprint.getAddress('interface', (data) => {
        console.log('interface')
        console.log(data)
      }, 'IPv4', 'eth1')

      ///////////////////

      let params = typeId ? {
        ids: e.recordId,
        typeId: typeId
      } : {
        ids: e.recordId
      }
      const res = await getPackagesProcessingPrintList(params);
      if (!res?.success) {
        this.printloading = false;
        this.previewshow = false;
        return
      }
      this.shippingmarkshow = true;
      this.tableData2.map((item) => {
        if (item.recordId == e.recordId) {
          item.shippingMark = '是';
        }
      })
      var printData = res.data[0];
      if (res.data.length > 0) {//请求数据
        //  _this.$refs.preView.show(hiprintTemplate, printData)

        ////////////////
        let { width } = { type: 'other', width: 100, height: 50 }
        setTimeout(() => {
          let { width } = this.curPaper
          this.$refs.preView.show(hiprintTemplate, printData, width)
          this.printloading = false;
          _this.$forceUpdate();
        }, 500);

        ////////////////////////////////

      }
    },
    onediaobo () {
      if (this.neiarr.length == 0) {
        this.$message({
          message: '请勾选需要调出质检！',
          type: 'fail'
        })
        return
      }
      this.shenpishow = true;
    },
    async geturl (data) {

      this.qualfifter.certificateUrl = data.url;
      this.qualfifter.fileName = data.fileName;
      this.$message.success("上传成功");
      await this.savecertificateInfo();
      this.qualificationshow = false;
    },
    rowStyleFun ({ row, rowIndex, $rowIndex }) {
      if (!row.isEnd) {
        return '';
      } else {
        return 'droprow';
      }
    },
    changepriceshow (row, index) {
      this.pricelist.typeId = index;
      this.pricelist.packagesProcessId = row.packagesProcessingId;
      switch (index) {
        case 1:
          this.pricelist.price = row.workPrice;
          this.pricetitle = '加工工价';
          break;
        case 2:
          this.pricelist.price = row.dispatchWorkPrice;
          this.pricetitle = '加工工价';
          break;
        case 3:
          this.pricelist.price = row.deliveryWorkPrice;
          this.pricelist.packagesProcessDetialId = row.id;
          this.pricetitle = '收货工价';
          break;
        case 4:
          this.pricelist.price = row.allotWorkPrice;
          this.pricelist.packagesProcessDetialId = row.id;
          this.pricetitle = '调拨工价';
          break;
      }
      this.priceshow = true;
    },
    async subchange (type, row) {
      let params = {
        ...this.pricelist,
      }
      const res = await updateWorkPrice(params);
      if (!res?.success) {
        return
      }
      this.$message({
        message: '操作成功！',
        type: 'success'
      })
      this.priceshow = false;
      if (this.pricelist.typeId == 3) {
        this.getneireq();
      } else {
        this.$emit('getTaskList');
      }
      // this.$emit('getTaskList');
      // this.getneireq()
    },
    changeprice (type, price) {
      this.pricelist.price = price.value;
      this.pricelist.typeId = type;
    },
    async toggleTreeMethod ({ expanded, row }) {
      this.$emit('getfinishedProductQuantity', row.finishedProductQuantity);
      this.waiid = row.packagesProcessingId;
      if (expanded) {
        this.getneireq();
      }
      return true;
    },
    async getneireq (val) {
      let _this = this;
      if(val == 1){
        _this.loadingg = true;
      }
      if(val == 2){
        _this.editLoading = true;
      }
      if (this.versionId) {
        let params = { packagesProcessId: _this.waiid, versionId: this.versionId };
        const res = await getHistoryPackagesDetialTaskInfo(params);
        if (res?.success) {
          _this.loadingg = false;
          _this.editLoading = false;
          _this.neitable = res.data;
        }
        return
      }
      const res = await getPackagesProcessingDetialListAsync(_this.waiid);
      if (res?.success) {
        _this.loadingg = false;
        _this.editLoading = false;
        _this.neitable = res.data;
      }

    },
    async upfinish () {
      const res = await updateHalfProductImg(this.upForm);
      if (!res?.success) {
        return
      }
      this.$message({
        message: '操作成功！',
        type: 'success'
      })
      // this.$emit('getTaskList');
      this.getneireq(1);
      this.updialogVisible = false;
    },
    showupfuc (row) {
      this.upForm.packagesDetialId = row.id;
      this.upForm.requiredImg = row.requiredImg;
      this.upForm.halfProductImg = row.halfProductImg;
      let _this = this;
      _this.$nextTick(() => {
        _this.updialogVisible = true;
      })
    },
    async consumableupfinish(){
      if(this.consumableupForm.consumableImg == null){
        this.$message.error('请选择图片！');
        return
      }
      const res = await updateConsoumableProductImg(this.consumableupForm);
      if (!res?.success) {
        return
      }
      this.$message({
        message: '操作成功！',
        type: 'success'
      })
      // this.$emit('getTaskList');
      this.getneireq(2);
      this.consumabledialogVisible = false;
    },
    consumableshowupfuc(row) {
      this.consumableupForm.consumableId = row.id;
      this.consumableupForm.consumableImg = row.consumableImg;
      let _this = this;
      _this.$nextTick(() => {
        _this.consumabledialogVisible = true;
      })
    },
    qualityinput (val) {
      this.taskroww.quantity = val.value;
    },
    storagechange (val) {
      var name = '';
      this.storagelist.map((item) => {
        if (item.wms_co_id == val.value) {
          this.outlist.wareName = item.name;
          this.diaobodata.wareName = item.name;
        }
      })
    },
    sumbitshootingTaskRemark () {
      let res = this.$refs.packdesgtaskRemark.onsubmit();
      if (res) {
        this.viewReferenceRemark = false;
      }
      this.$emit('getTaskList');
    },
    //查看详情备注页
    openTaskRmarkInfo (row) {
      // return
      this.remarkRow = row;
      this.rowinfo = row.packagesProcessingId;
      this.viewReferenceRemark = true;
    },
    sumNum (list, field) {
      let count = 0
      list.forEach(item => {
        count += Number(item[field])
      })
      return count
    },
    countAmount (row) {
      return row.amount * row.num1
    },
    countAllAmount (data) {
      let count = 0
      data.forEach(row => {
        count += this.countAmount(row)
      })
      return count
    },
                // 格式化函数，处理千位分隔符和小数位
                formatNumber(number){
                    const absNumber = Math.abs(number);
                    const options = {
                        minimumFractionDigits: absNumber >= 100 ? 0 : 2,
                        maximumFractionDigits: absNumber >= 100 ? 0 : 2,
                    };
                    return new Intl.NumberFormat('zh-CN', options).format(number);
                },
    footerMethod ({ columns, data }) {
      // debugger
      const sums = [];
      if (!this.summaryarry)
        return sums
      var arr = Object.keys(this.summaryarry);
      if (arr.length == 0)
        return sums
      var hashj = false;
      columns.forEach((column, index) => {
        if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
          var sum = this.summaryarry[column.property + '_sum'];
          if (sum == null) return;
          // sums[index] = sum
          else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
          // else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum
          else sums[index] = this.formatNumber(sum);
        }
        else sums[index] = ''
      });
      return [sums]
    },
    footerMethod1 ({ columns, data }) {
      if (this.typeId == 1) {
        const sums = [];
        sums[2] = this.summaryy.totalQuantity;
        return [sums]
      } else {
        const sums = [];
        sums[4] = this.summaryy.totalQuantity;
        return [sums]
      }

    },
    userselect (user) {
      let _this = this;
      this.taskrow.createPackId = user.value;
      this.taskroww.selUserId = user.value;
      this.taskroww.createPackId = user.value;
      this.userid = user.value;
      this.userlist.map((item) => {
        if (item.userId == user.value) {
          this.taskroww.createUserName = item.userName;
          this.zhijiandata.createUserName = item.userName;
        }
      })
      // this.tableData2.map((item)=>{
      //   if(item.recordId == this.taskrow.recordId){
      //     item.createUserName = this.fiftername(user.value);
      //   }
      // })
      _this.$nextTick(() => {
        _this.$forceUpdate();
      })
    },
    formatIsCommission (value) {
      return value == null ? null : formatTime(value, 'YY-MM-DD')
    },
    async endtask (row) {
      let params = {
        processId: row.packagesProcessingId
      }
      // return
      const res = await updateFinishState(params);
      if (!res?.success) {
        return
      }
      this.$message({
        message: '更新状态成功！',
        type: 'success'
      })
      this.$emit('getTaskList');
    },
    async updateDegree (row) {
      let that = this;
      switch (row.urgencyDegree) {
        case 0://压单
          // if (row.urgencyDegreeName == '压单') {
          //   // that.$message({ message: '处于压单状态', type: "info" });
          //   return
          // }
          this.$confirm("是否改为正常?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            distinguishCancelAndClose: true,
          })
          .then(async () => {
              let params = {
                processId: row.packagesProcessingId,
                degreeType: 3
              }
              var res = await updateUrgencyDegree(params);
              if (res?.success) {
                that.$message({ message: '更新正常成功！', type: "success" });
                await this.$emit('getTaskList');
              }
            });
            // .catch(async (e) => {
            //   if (e == 'cancel') {
            //     let params = {
            //       processId: row.packagesProcessingId,
            //       degreeType: 2
            //     }
            //     var res = await updateUrgencyDegree(params);
            //     if (res?.success) {
            //       that.$message({ message: '取消更新正常！', type: "success" });
            //       await this.$emit('getTaskList');
            //     }

            //   } else if (e == 'close') {

            //   }

            // });
          break;
        case 1://加急
          // if (row.urgencyDegreeName == '加急') {
          //   that.$message({ message: '处于加急状态', type: "info" });
          //   return
          // }
          this.$confirm("是否改为压单?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            distinguishCancelAndClose: true,
          })
          .then(async () => {
              let params = {
                processId: row.packagesProcessingId,
                degreeType: 2
              }
              var res = await updateUrgencyDegree(params);
              if (res?.success) {
                that.$message({ message: '更新压单成功！', type: "success" });
                await this.$emit('getTaskList');
              }
            });
            // .catch(async (e) => {
            //   if (e == 'cancel') {
            //     let params = {
            //       processId: row.packagesProcessingId,
            //       degreeType: 1
            //     }
            //     var res = await updateUrgencyDegree(params);
            //     if (res?.success) {
            //       that.$message({ message: '取消压单成功！', type: "success" });
            //       await this.$emit('getTaskList');
            //     }

            //   } else if (e == 'close') {

            //   }

            // });
          break;
        case 2://待审
          this.$confirm("是否改为加急?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            distinguishCancelAndClose: true,
          })
            .then(async () => {
              let params = {
                processId: row.packagesProcessingId,
                degreeType: 1
              }
              var res = await updateUrgencyDegree(params);
              if (res?.success) {
                that.$message({ message: '更新紧急程度成功！', type: "success" });
                await this.$emit('getTaskList');
              }
            });
            // .catch(async (e) => {
            //   if (e == 'cancel') {
            //     let params = {
            //       processId: row.packagesProcessingId,
            //       degreeType: 2
            //     }
            //     var res = await updateUrgencyDegree(params);
            //     if (res?.success) {
            //       that.$message({ message: '更新紧急程度成功！', type: "success" });
            //       await this.$emit('getTaskList');
            //     }

            //   } else if (e == 'close') {

            //   }

            // });
          break;
        case 9://正常
          let params = {
            processId: row.packagesProcessingId,
            degreeType: 0
          }
          if (row.urgencyDegreeName == "正常") {
            this.$confirm("是否改为审核?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(async () => {
                var res = await updateUrgencyDegree(params);
                if (res?.success) {
                  that.$message({ message: '更新审核成功！', type: "success" });
                  await this.$emit('getTaskList');
                }
              });
          }
          break;
      }


    },
    getuser (val) {
      this.userlist = val;
      this.getloginuser();
    },
    async getloginuser () {
      const res = await getCurrentUser();
      if (!res?.success) {
        return
      }
      this.loginuser = res.data;
    },
    fiftername (value) {
      var info = ' '
      this.userlist.forEach((item) => {
        if (item.userId == value) info = item.userName
      })
      return info;
    },
    async savecertificateInfo () {
      const res = await updateCertificateBatch({
        fileName: this.qualfifter.fileName,
        certificateUrl: this.qualfifter.certificateUrl,
        certificateInfo: this.qualfifter.msg,
        packProcessingIds: this.allid,
        noCertificate: this.nohgz ? true : false,
      });
      if (!res?.success) {
        return
      }
      this.$message({
        message: '保存成功',
        type: 'success'
      })
      this.$emit('getTaskList');
      this.qualificationshow = false;
      this.nohgz = false;
      this.disabled = true;
    },
    async saveworkshow () {
      if(this.taskroww.classType === undefined){
          this.$message({ message: "请选择班次", type: "warning",  zIndex: 9999999999999 });
          return
        }
      this.btnloading = true;
      if (this.typeId == 2) {
        // this.outlist.detialList = [];
        // this.outlist.detialList.push(this.taskroww);
        // debugger
        this.taskroww.typeId = 2;
        if (!this.taskroww.createPackId) {
          this.taskroww.createPackId = this.loginuser.userId;
          this.taskroww.createUserName = this.loginuser.userName;
        }
        // console.log("打印数据",this.taskroww);
        // console.log("打印数据",this.outlist);
        // return
        // this.outlist.detialList[0].qua = this.outlist.wareId;
        // this.outlist.detialList[0].wareId = this.outlist.wareId;
        // this.outlist.detialList[0].wareName = this.outlist.wareName;
        // if (!this.outlist.wareId) {
        //   this.$message({ type: 'warning', message: "请选择仓库" });
        //   return
        // }
        if(this.taskroww.quantity === undefined || this.taskroww.quantity === null || this.taskroww.quantity === ''){
          this.$message({ message: "数量必须为大于0的整数", type: "warning",  zIndex: 9999999999999 });
          this.btnloading = false;
          return;
        }
        if(this.taskroww.boxNum === undefined || this.taskroww.boxNum === null || this.taskroww.boxNum === ''){
          this.$message({ message: "箱数必须为大于0的整数", type: "warning",  zIndex: 9999999999999 });
          this.btnloading = false;
          return;
        }
        var res = await packagesProcessQuality(this.taskroww);
        if (!res?.success) {
          this.btnloading = false;
          return
        }

        this.$message({ type: 'success', message: "操作成功" });
        this.$emit('getTaskList');
        this.workshow = false;
      } else if (this.typeId == 4) {
        this.outlist.detialList = [];
        this.outlist.detialList.push(this.taskroww);
        this.outlist.detialList[0].wareId = this.outlist.wareId;
        this.outlist.detialList[0].wareName = this.outlist.wareName;

        this.outlist.detialList[0].packaesProcessingDetialId = this.packaesProcessingDetialIds;

        this.outlist.detialList[0].produnctName = this.halfrow.halfProductName;
        this.outlist.detialList[0].produnctCode = this.halfrow.halfProductCode;
        // if (!this.outlist.wareId) {
        //   this.$message({ type: 'warning', message: "请选择仓库" });
        //   return
        // }

        var res = await pckagesProcessingCallIn(this.outlist);
        if (!res?.success) {
          return
        }

        this.$message({ type: 'success', message: "操作成功" });
        this.$emit('getTaskList');
        this.workshow = false;
      } else {
        let params = {
          packaesProcessingId: this.packagesProcessingId,
          packaesProcessingDetialIds: this.packaesProcessingDetialIds,
          packaesProcessingDetialIds: this.taskrow.id,
          ...this.taskrow,
          typeId: this.typeId
        }
        const res = await editPackagesProcessRecord(params);
        if (!res?.success) {
          return
        }
        this.$message({
          message: '保存成功',
          type: 'success'
        })
        if (this.typeId == 3) {
          this.getneireq();
        } else {
          this.$emit('getTaskList');
        }
        // this.$emit('getTaskList');
        this.workshow = false;
      }
      this.btnloading = false;
    },

    async neisaveworkshow () {

      if (this.typeId == 2) {

        let params = {
          id: this.taskrow.recordId,
          ...this.taskroww,
          ...this.outlist
        }

        // if (!this.outlist.wareId) {
        //   this.$message({ type: 'warning', message: "请选择仓库" });
        //   return
        // }
        var res = await editCallInOutRecord(params);
        if (!res?.success) {
          this.btnloading = false;
          return
        }

        this.$message({ type: 'success', message: "操作成功" });
        this.$emit('getTaskList');
        this.workshow = false;
      } else if (this.typeId == 4) {

        let params = {
          id: this.taskrow.recordId,
          ...this.taskroww,
          ...this.outlist
        }

        if (!this.outlist.wareId) {
          this.$message({ type: 'warning', message: "请选择仓库" });
          return
        }
        var res = await editCallInOutRecord(params);
        if (!res?.success) {
          this.btnloading = false;
          return
        }

        this.$message({ type: 'success', message: "操作成功" });
        // this.$emit('getTaskList');
        this.getneireq();
        this.workshow = false;
      } else if (this.typeId == 6) {
        if(this.taskrow.classType===" " || this.taskrow.classType===undefined )
        {
          this.$message({ message: "请选择班次", type: "warning",  zIndex: 9999999999999 });return;
        }
        let params = {
          packaesProcessingId: this.taskrow.packagesProcessingId,
          ...this.zhijiandata
        }
        if(params.quantity === undefined || params.quantity === null || params.quantity === ''){
          this.$message({ message: "数量必须为大于0的整数", type: "warning",  zIndex: 9999999999999 });
          this.btnloading = false;
          return;
        }
        var res = await packagesProcessQuality(params);
        if (!res?.success) {
          return
        }
        this.$message({ type: 'success', message: "操作成功",  zIndex: 9999999999999  });
        this.tableeditfuc(this.taskrow, 6);
        this.$emit('getTaskList');
        this.workshow = false;

      } else {
        let params = {
          packaesProcessingId: this.packagesProcessingId,
          packaesProcessingDetialIds: this.packaesProcessingDetialIds,
          ...this.taskrow,
          typeId: this.typeId
        }
        const res = await editPackagesProcessRecord(params);
        if (!res?.success) {
          return
        }
        this.$message({
          message: '保存成功',
          type: 'success'
        })
        if (this.typeId == 3) {
          this.getneireq();
        } else {
          this.$emit('getTaskList');
        }

        this.workshow = false;
      }
    },
    async shnpishowfuc (row, i, nei) {
      this.shenpishow = true;
      this.diaobodata.id = row.recordId;

      this.shenpidata.row = row;
      this.shenpidata.i = i;
      this.shenpidata.nei = nei;
      // let params = {
      //   id: row.recordId,
      // }
      // var res = await initiateApprovalCallInOut(params);
      // if (!res?.success) {
      //   return
      // }

      // this.$message({ type: 'success', message: "发起审批成功" });
      // // this.$emit('getTaskList');
      // this.workshow = false;
    },

    async approvefuc () {
      this.btnloading = true;
      let params = {
        ...this.diaobodata,
        typeId: this.typeId == 6 ? 2 : 1 ////1半成品调入 2质检调出
      }
      var res = await initiateApprovalCallInOut(params);
      if (!res?.success) {
        this.btnloading = false;
        return
      }

      this.$message({ type: 'success', message: "发起审批成功" });
      // this.$emit('getTaskList');
      // this.neiarr = [];
      this.btnloading = false;
      this.shenpishow = false;
    },
    updatelist () {
      // this.tableData = e.list;
      this.tableloading = false;
      // this.init();
      // console.log("全部数据list0000",e.list)

    },
    async hisupdatelist (e) {
      // const res = await getHistoryPackagesListInfo(e);
      // if (!res?.success) return;


      this.tableData = e.list;
      this.tableloading = false;
      // this.init();

    },
    openAlert (options) {
      VXETable.modal.alert(options)
    },
    qualificationshowfuc (row) {
      this.disabled = true;
      this.qualfifter.certificateUrl = row.certificateUrl;
      this.qualfifter.fileName = row.fileName;
      this.allid = [];
      this.allid.push(row.packagesProcessingId);
      this.qualfifter.msg = row.certificateInfo;
      this.qualificationshow = true;
      this.$nextTick(() => {
        this.$refs.refupfile.downmsgfuc(this.qualfifter)
      });
    },
    qualificationedit (e) {
      this.qualfifter.msg = '无合格证';
      this.nohgz = true;
      this.$nextTick(() => {
        this.savecertificateInfo();
      });
    },
    qualfieditfuc () {
      this.disabled = false
    },
    workshowfuc (row, i, nei) {
      // return;
      // return
      this.taskrow = row;
      this.taskroww.quantity = row.quantity;
      if (nei) {
        this.zhijiandata = row;
        this.taskrow.createPackId = row.createUserId;
        this.editshow = false;//可编辑
        this.isnei = true;
        this.taskroww.selUserId = row.createUserId;
      } else {
        this.taskrow.createPackId = this.loginuser.userId;
        this.editshow = true;//不可编辑
        this.isnei = false;
        this.taskroww.selUserId = this.loginuser.userId;
      }
      this.taskrow.quantity = null;
      this.taskroww.boxNum = null;

      this.taskroww.produnctName = row.finishedProductName;
      this.taskroww.produnctCode = row.finishedProductCode;
      this.taskroww.packaesProcessingId = row.packagesProcessingId;




      this.packagesProcessingId = row.packagesProcessingId;

      if (!nei) {
        this.typeId = i
      }

      switch (i) {
        case 1:
          this.recordTitle = '加工记录';
          break;
        case 2:
          this.recordTitle = '加工记录';
          break;
        case 5:
          this.recordTitle = '编辑记录';
          this.packaesProcessingDetialIds = row.id ? row.id : row.packagesProcessingDetialId;
          break;
        case 3:
          this.recordTitle = '编辑记录';
          break;
      }
      this.workshow = true;

    },
    async tableeditfuc (row, i) {
      this.typeId = i;
      this.titlerow = row;
      this.taskrow = row;
      this.tableedittitle = i == 1 ? '加工编辑' : this.tableedittitle = i == 2 ? '调出编辑' : this.tableedittitle = i == 3 ? '收货编辑'
        : this.tableedittitle = i == 4 ? '调入编辑' : this.tableedittitle = i == 6 ? '加工编辑' : '其他';
      this.tokey = 'seven';
      //
      switch (i) {
        case 1:
          var params = {
            packagesProcessId: row.packagesProcessingId,
            typeId: i
          }
          this.tokey = 'one';
          const res = await getPackagesProcessingRecordListAsync(params);
          if (!res?.success) {
            return
          }

          // res.data.recordList.map((row)=>{
          //   if(row.createDate){
          //     row.createDate = row.createDate.slice(2,10)
          //   }
          // })
          this.tableData2 = res.data.recordList;
          this.summaryy = res.data;
          break;
        case 2:
          var params = {
            packagesProcessId: row.packagesProcessingId,
            typeId: i
          }
          this.tokey = 'two';
          const res2 = await getCallOutRecordListAsync(params);
          if (!res2?.success) {
            return
          }
          this.tableData2 = res2.data.recordList;
          this.summaryy = res2.data;
          break;
        case 3:
          var params = {
            packagesProcessId: row.packagesProcessingId,
            packagesProcessDetialId: row.id,
            typeId: i
          }
          this.tokey = 'three';
          const res3 = await getPackagesProcessingRecordListAsync(params);
          if (!res3?.success) {
            return
          }
          this.tableData2 = res3.data.recordList;
          this.summaryy = res3.data;
          break;
        case 4:
          this.halfrow = row;
          var params = {
            packagesProcessId: row.packagesProcessingId,
            typeId: 1,
            packagesProcessDetialId: row.id
            // packaesProcessingDetialId: row.id

          }
          this.tokey = 'four';
          const res4 = await getCallOutRecordListAsync(params);
          if (!res4?.success) {
            return
          }

          this.tableData2 = res4.data.recordList;
          this.summaryy = res4.data;
          break;
        case 6:
          // isHistory
          var params = {
            packagesProcessId: row.packagesProcessingId,
            typeId: 2
          }
          this.tokey = 'six';
          if (this.isHistory) {
            params.versionId  = this.versionId
            const {data} = await getPackagesProcessingRecordListAsync1(params);
            data.recordList.map((item) => {
            if (item.shippingMark == '否') {
              this.allshippingMark = false;
            }
            this.tableData2 = data.recordList;
          })
          }else{
            params.tabel = this.tabel
            const res6 = await getPackagesProcessingRecordListAsync(params);
          if (!res6?.success) {
            return
          }
          this.allshippingMark = true;
          res6.data.recordList.map((item) => {
            if (item.shippingMark == '否') {
              this.allshippingMark = false;
            }
          })
          this.tableData2 = res6.data.recordList;
          this.summaryy = res6.data;
          }
          break;
      }
      this.tableeditshow = true;
    },
    doubleclick (e) {
      // this.addLoading = true;
      this.packagesProcessingId = e.packagesProcessingId;
      this.editTaskshow = true;
      // if(this.versionId){
      //   // this.$nextTick(() => {
      //     // this.$refs.refdialogright.gettabmsg(this.allsellist);
      //     // this.$refs.refdialogright.hisgetfamsg(e.packagesProcessingId,this.versionId);
      //     // setTimeout(()=>{
      //     //   this.addLoading = false;
      //     // },500)
      //   // })
      //   return
      // }

      // this.$nextTick(() => {
      // this.$refs.refdialogright.gettabmsg(this.allsellist);
      // this.$refs.refdialogright.getfamsg(e.packagesProcessingId);

      // setTimeout(()=>{
      //   this.addLoading = false;
      // },500)
      // })

    },
    //关闭窗口，初始化数
    async onCloseAddForm (type) {
      await this.$emit('getTaskList');
      if (type == 1) {
        // this.addTask = false;
        this.editTaskshow = false;
      }
    },
    copytext (e) {
      let textarea = document.createElement("textarea")
      textarea.value = e
      textarea.readOnly = "readOnly"
      document.body.appendChild(textarea)
      textarea.select()
      let result = document.execCommand("copy")
      if (result) {
        this.$message({
          message: '复制成功',
          type: 'success'
        })
      }
      textarea.remove()
    },
    neitablesel (data) {
      var arr = [];
      data.records.map((item) => {
        arr.push(item.recordId)
      })
      this.neiarr = arr;
      this.diaobodata.id = arr.join();
    },
    checkchange (data) {
      this.$nextTick(() => {
        this.$emit('pids', data.records);
      });
    },
    chilcheckchange (data) {
      this.$emit('chipids', data.records);
    },
    seqMethod (data) {
      // return `${row}`
      var index = this.tableData2.length - data.rowIndex;
      return index;

    },
    async delRecord (row) {
      this.$confirm("是否确定删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let params = {
          recordIds: row.recordId,
          typeId: (this.typeId == 1 || this.typeId == 3 || this.typeId == 6) ? 0 : 1
        }
        var res = await deleteReord(params);
        if (!res?.success) {
          return
        }
        this.tableData2.splice(this.tableData2.indexOf(row), 1)
        this.$message({
          message: '删除成功',
          type: 'success'
        })
        if (this.typeId == 3) {
          this.getneireq();
        } else {
          this.$emit('getTaskList');
        }
        // this.$emit('getTaskList');
      });
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 20px;
}

::v-deep .vxe-table--render-default .vxe-header--column {
  line-height: 18px !important;
}

/*滚动条整体部分*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar {
  width: 18px !important;
  height: 26px !important;
}

/*滚动条的轨道*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-track {
  background-color: #f1f1f1 !important;
}

/*滚动条里面的小方块，能向上向下移动*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
  background-color: #c1c1c1 !important;
  border-radius: 3px !important;
  box-sizing: border-box !important;
  border: 2px solid #F1F1F1 !important;
  box-shadow: inset 0 0 6px rgba(255, 255, 255, .5) !important;
}

// 滚动条鼠标悬停颜色
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
  background-color: #A8A8A8 !important;
}

// 滚动条拖动颜色
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
  background-color: #787878 !important;
}

/*边角，即两个滚动条的交汇处*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
  background-color: #dcdcdc !important;
}


// 表格内边距
::v-deep .vxe-table--render-default .vxe-cell {
  // padding: 0 0 0 8px !important;
}





.flexrow {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.qualibtn ::v-deep .el-button {
  margin-left: 0 !important;
  margin-top: 10px;
}

.marginrt {
  margin: 0 10px 0 auto;
}

.point:hover {
  cursor: pointer;
}

.point {
  color: #409EFF;
}


.item {
  margin-bottom: 18px;
}

.clearfix {
  font-size: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both
}

::v-deep .box-card {
  margin-top: 20px;
  box-sizing: border-box;
  padding: 0 60px;

}

.flexcenter {
  display: flex;
  justify-content: left;
  align-items: left;
}

::v-deep .height {
  height: 58px !important;
}

::v-deep .height1 {
  height: 48px !important;
  font-size: 12px !important;
}

::v-deep .cellheight1 {
  font-size: 12px !important;
}

// .relativebox{
//   position: relative;
//   width: 100%;
// }
.relativeboxx {
  position: relative;
  width: 120%;
}

.positioncenter {
  position: absolute;
  right: 10px;
  top: 28%;
  bottom: 50%;
  // transform: translate(-50%,-50%);
}

::v-deep .droprow td {
  color: rgb(250, 9, 9);
  position: relative;
}

::v-deep .droprow ::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 0.1px;
  background-color: rgb(250, 9, 9);
  transform: translateY(-50%);
}


.copyhover {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  display: none;
}

.addhover {
  display: none;
}

.addhover:hover {
  cursor: pointer;
}

.clihover:hover {
  cursor: pointer;
}

.relativebox {
  // width: 100px;
  width: 150%;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.relativebox:hover {
  width: 100%;
}

.textover {
  // white-space: nowrap;

  flex: 1;
  display: block;
  overflow: hidden;
  //  max-width: 60%;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  white-space: nowrap;
}

.relativebox:hover .textover {
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.relativebox:hover .copyhover {
  display: block;
  position: absolute;
  top: 50%;
  right: 12px;
  margin: 0 10px;
  z-index: 99;
  transform: translate(-50%, -50%);
  color: #409EFF;
  font-weight: 600;
}

.relativebox:hover .addhover {
  display: block;
  position: absolute;
  top: 50%;
  right: 0;
  margin: 0 10px;
  z-index: 99;
  transform: translate(-50%, -50%);
  color: #409EFF;
  font-weight: 600;
}

// .copyhover:hover>.copyhover{
//   display: block;

//   position: absolute;
//   top: 50%;
//   left: 50%;
//   transform: translate(-50%,-50%);
// }

.minibtn ::v-deep .el-button--mini {
  padding: 7px 8px;
}

.vxeclass {
  z-index: 10000 !important;
}

::v-deep .vxe-body--expanded-cell {
  padding: 0 !important;
}

.el-icon-document-copy {
  font-size: 16px;
  margin-left: 2px;
}

.el-icon-circle-plus-outline {
  font-size: 16px;
  margin-left: -16px;
}

.el-icon-document-copy:hover {
  font-size: 16px;
  margin-left: 2px;

}

::v-deep .vxe-modal--header {
  margin-top: 8px !important;
  background-color: transparent !important;
  padding: 0 6px;

}

::v-deep .vxe-modal--header-title {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

::v-deep .vxe-modal--header-right {
  // color: transparent ;
  font-size: 12px;
  line-height: 32px;
}

::v-deep .vxe-modal--content {
  padding: 20px 35px 35px 35px;
}

::v-deep .bzbjbt {
  height: 60px;
  background-color: rgb(255, 255, 255);
  font-size: 18px;
  color: #666;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 20px 35px;
}

::v-deep .rwmc {
  // width: 750px;
  height: 70px;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 3px 5px #eeeeee;
  box-sizing: border-box;
  padding: 0 56px;
  display: flex;
}

::v-deep .rwmc .xh {
  height: 65px;
  font-size: 18px;
  line-height: 68px;
  box-sizing: border-box;
  padding: 0 2px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666;
}

.tablehei {
  height: 55vh;
}

::v-deep .rwmc .mc,
::v-deep .icon {
  height: 65px;
  font-size: 18px;
  line-height: 68px;
  box-sizing: border-box;
  margin-left: 10px;
  padding: 0 2px;
  display: inline-block;
  color: #666;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .el-drawer .el-drawer__header {
  padding: 0 !important;
}

::v-deep .vxe-footer--row {
  height: 50px;
}

.statuscss ::v-deep .el-tag--dark {
  border-color: #fff !important;
}

.minisize ::v-deep .vxe-button {
  padding: 0 !important;
}

.sybj {
  min-width: 1100px;
  margin-top: 20px;
}

.tjbt {
  /* background-color: aquamarine; */
  /* font-weight: bold; */
  color: #333;
  line-height: 30px;
}

.tjnrnk {
  width: 100%;
  background-color: rgb(255, 255, 255);
  box-sizing: border-box;
  padding: 0px 20px;
  float: left;
  border-radius: 6px;
}

.custom-drawer .el-drawer__title {
  margin-top: 10px;
  font-size: 18px;
  padding: 16px 24px 0 24px;
}

::v-deep .el-drawer__header {
  border: none !important;
  padding: 16px 24px 0 24px;
}

//  ::v-deep .el-popper{
//   overflow: hidden !important;
// }

.topgroup{
  display: flex;
  align-items: center;
  padding:0 30px;
  margin-top: 30px;
}

.mine ::v-deep .btt{
  padding:20px 35px 35px 35px;
  background-color:white;
  // border:solid 35px white;
  // border-bottom:30px;
  box-sizing:border-box;
}

.tooltip-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between; /* 均匀分布间距 */
  width: 300px;
}

.tooltip-item {
  text-align: center; /* 文本居中 */
}

.vxetoolbar20221212{
    position:absolute ;
    top: 0;
    right: -15px;
    padding-top:0;
    padding-bottom:0;
    z-index: 88;
    background-color: rgb(255 255 255 / 0%);
    // background-color: blue;

}

 ::v-deep .el-popover{
  overflow-y: hidden !important;
  line-height: 50px;
}

 ::v-deep .el-popper{
  overflow-y: hidden !important;
  line-height: 50px;
}


</style>

