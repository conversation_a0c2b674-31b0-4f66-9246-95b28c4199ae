<template>
  <div style="display: flex;flex-direction: column;height: 100%;">
    <el-form :inline="true" :model="changePostData">
      <el-form-item label="">
        <el-date-picker style="width: 160px" v-model="changePostData.changePostMonth" :clearable="false" type="month"
          placeholder="请选择更新月份" icon="el-icon-date" format="yyyy-MM" value-format="yyyy-MM"></el-date-picker>
      </el-form-item>
      <el-form-item label="采购员">
        <el-select v-model="changePostData.brandId" placeholder="请选择采购员" style="width: 170px;" clearable multiple
          collapse-tags filterable>
          <el-option v-for="item in buyerList" :key="item.id" :label="item.brandName" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="岗位变动">
        <el-select v-model="changePostData.archName" placeholder="请选择变动岗位" style="width: 120px;" clearable filterable>
          <el-option label="新晋专员" value="新晋专员"></el-option>
          <el-option label="降级助理" value="降级助理"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList">查询</el-button>
        <el-button type="primary" @click="onAdd">添加</el-button>
      </el-form-item>
    </el-form>
    <vxetablebase :id="'structuralChange202501021001'" :tablekey="'structuralChange202501021001'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'300px'" :isNeedExpend="false">
    </vxetablebase>
  </div>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { addChangePost, getChangePost } from '@/api/inventory/purchaseSummary';
import dayjs from 'dayjs'
const tableCols = [
  { width: 'auto', align: 'center', prop: 'changePostMonth', label: '变动月份', },
  { width: 'auto', align: 'center', prop: 'brandName', label: '采购员', },
  { width: 'auto', align: 'center', prop: 'archName', label: '变动岗位', },
]
export default {
  name: 'structuralChange',
  components: {
    vxetablebase
  },
  props: {
    buyerList: {
      type: Array,
      default() { return [] }
    },
  },
  data() {
    return {
      changePostData: {
        changePostMonth: null,//更新月份
        archName: null,//岗位
        brandId: [],//采购员ID
      },
      tableCols,
      tableData: [],
      loading: false,
    }
  },

  async mounted() {
    this.changePostData.changePostMonth = dayjs().startOf('month').format('YYYY-MM-DD');
    await this.getList();
  },
  methods: {
    async onAdd() {
      if (this.changePostData.archName == null) {
        this.$message.error('请选择岗位变动！');
        return false;
      }
      if (null === this.changePostData.brandId || (this.changePostData.brandId && this.changePostData.brandId.length == 0)) {
        this.$message.error("请选择采购员！");
        return false;
      }
      const firstDayOfMonth = dayjs(this.changePostData.changePostMonth).startOf('month').format('YYYY-MM-DD');
      const params = { ...this.changePostData, changePostMonth: firstDayOfMonth };
      const { data, success } = await addChangePost(params);
      if (!success) return;
      this.$message.success('添加成功!');
      await this.getList();
    },
    async getList() {
      this.loading = true;
      const { data, success } = await getChangePost(this.changePostData);
      this.loading = false;
      if (!success) return;
      this.tableData = data.list;
    },
  }
}
</script>
<style scoped lang="scss">
::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
