<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="共享账号" name="tab1" style="height: 100%;">
                <sharedaccount :filter="filter" ref="sharedaccount" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="申请记录" name="tab2" style="height: 100%;">
                <sharedaccountapplyrecord :filter="filter" ref="sharedaccountapplyrecord" style="height: 100%;" />
            </el-tab-pane>
        </el-tabs>
    </my-container>

</template>
<script>
    import MyContainer from "@/components/my-container";
    import sharedaccount from '@/views/operatemanage/productalllink/sharedaccount/sharedaccount.vue';
    import sharedaccountapplyrecord from '@/views/operatemanage/productalllink/sharedaccount/sharedaccountapplyrecord.vue';

    export default {
        name: "Users",
        components: { MyContainer, sharedaccount, sharedaccountapplyrecord },
        data() {
            return {
                that: this,
                pageLoading: '',
                filter: {
                },
                shopList: [],
                userList: [],
                groupList: [],
                selids: [],
                dialogVisibleSyj: false,
                fileList: [],
                activeName: 'tab1'
            };
        },
        mounted() {

        },
        methods: {


        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
