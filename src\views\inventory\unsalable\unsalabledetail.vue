<template>
  <my-container v-loading="pageLoading">
     <ces-table ref="table" :that='that' :isIndex='false' :hasexpand='true' @sortchange='sortchange' :isSelectColumn="true"  tablekey="detail"
          :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
          <template slot='extentbtn'>
          <el-button-group>
            <el-button style="padding: 0;margin: 0;">
                <el-select v-model="filter1.status" placeholder="状态" style="width: 100px">
                  <el-option label="全部" value></el-option>
                  <el-option label="变好" value="1"></el-option>
                  <el-option label="变坏" value="2"></el-option>
                </el-select>
            </el-button>
            <el-button type="primary" @click="onfresh">刷新</el-button>
            <el-button type="primary" :loading="onExporting" @click="onExport">导出</el-button>
          </el-button-group>
        </template>
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
    <el-dialog :visible.sync="dialogVisible" :show-close="false" v-dialogDrag width="60%">      
      <div style="margin-top:-50px;margin-bottom:-30px;margin-right:-20px;margin-left:-20px;">
       <probianmaanalysis :filter="analysisfilter" ref="probianmaanalysis1"/>
      </div>
    </el-dialog>
  </my-container>
</template>

<script>
import {getProCodeUnsalable,exportProCodeUnsalable} from '@/api/inventory/unsalable'
import MyContainer from '@/components/my-container/noheader'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import probianmaanalysis from '@/views/inventory/probianmaanalysis'
//import ordergoodssales from '@/views/order/ordergoodssales'
import { formatYesornoBool,formatLink,formatmoney,formatWarehouse,formatNoLink,formatTurnoverRang} from "@/utils/tools";
const tableHandles1=[];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton,probianmaanalysis},
   props:{
       filter: { }
     },
  data() {
    return {
      that:this,
      pickerOptions: {
          disabledDate(time) {return time.getTime() > Date.now();},
          shortcuts: [{
            text: '今天',
            onClick(picker) {picker.$emit('pick', new Date());}
          }, {
            text: '昨天',
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit('pick', date);
            }
          }, {
            text: '一周前',
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', date);
            }
          }]
        },
      filter1: {
        status:null,
      },
      analysisfilter:{
        startDate: null,
        endDate: null,
        proBianMa:""
      },
      summaryarry:{},
      list: [],
      brandlist:[],
      grouplist:[],
      pager:{OrderBy:"GoodsCode",IsAsc:false},
      tableCols:[],
      tableHandles:tableHandles1,
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      dialogVisible:false,
      onExporting:false,
    }
  },
  beforeUpdate() {  },
  methods: {
      async addDate(date,days){ 
          var d=new Date(date); 
          d.setDate(d.getDate()+days); 
          var m=d.getMonth()+1; 
          return d.getFullYear()+'-'+m+'-'+d.getDate(); 
       },
      async datetostr(date) {
          var y = date.getFullYear();
          var m = ("0" + (date.getMonth() + 1)).slice(-2);
          var d = ("0" + date.getDate()).slice(-2);
          return y + "-" + m + "-" + d;
      },
      async inittableCols(){
       this.tableCols =[
        {istrue:true,fixed:true,prop:'warehouse',label:'分仓', width:'50',formatter:(row)=> this.filter.isWarehouse?formatWarehouse(row.warehouse):"全仓"},
        {istrue:true,fixed:true,prop:'goodsCode',label:'商品编码', width:'80',sortable:'custom',type:'click',handle:(that,row,column,cell)=>that.showonanalysis(row,column,cell)},
        {istrue:true,fixed:true,prop:'goodsName',label:'商品编码名称', width:'100',type:'click',handle:(that,row,column,cell)=>that.toorergoodssales(row,column,cell)},
        {istrue:true,fixed:true,prop:'image',label:'图片', width:'60',type:'imageGoodsCode',goods:{code:'goodsCode',name:'goodsName'}},
        {istrue:true,fixed:true,prop:'groupId',label:'运营', width:'50',type:'format',formatter:(row)=>{return row.groupName;}},
        {istrue:true,fixed:true,prop:'newGroupId',label:'新负责人', width:'60',sortable:'custom',type:'format',formatter:(row)=>{return row.newGroupName;}},
        {istrue:true,fixed:true,prop:'brandId',label:'采购员', width:'62',type:'format',formatter:(row)=>{return row.brandName;}},
        {istrue:true,fixed:true,prop:'groupCount',label:'小组数', width:'62'},
        {istrue:true,fixed:true,prop:'productCount',label:'链接数', width:'62'},
        {istrue:true,prop:'',label:`${this.filter.date2}`, merge:true,
            cols:[{istrue:true,prop:'stock2',label:'库存数', sortable:'custom',width:'70',classname:'columnstock'},
                  {istrue:true,prop:'stockAmont2',label:'库存金额', sortable:'custom',width:'80',classname:'columnamont'},
                  {istrue:true,prop:'turnoverRang2',label:'周转区间', width:'80',classname:'columnrang',formatter:(row)=>formatTurnoverRang(row.turnoverRang2,1)},
                  {istrue:true,prop:'saleCount2',label:'销量', sortable:'custom',width:'60',classname:'columnsalase'},
                  {istrue:true,prop:'saleCountQj2',label:'期间销量',sortable:'custom', width:'80'},
                  {istrue:true,prop:'purchaseInDays202',label:'20天进仓数',sortable:'custom', width:'95'},
                  {istrue:true,prop:'turnover2',label:'周转天数', sortable:'custom',width:'80',classname:'columndays' }]},
        {istrue:true,prop:'',label:`${this.filter.date1}`, merge:true,
            cols:[{istrue:true,prop:'stock1',label:'库存数',sortable:'custom', width:'70',classname:'columnstock'},
                  {istrue:true,prop:'stockAmont1',label:'库存金额', sortable:'custom',width:'80',classname:'columnamont'},
                  {istrue:true,prop:'turnoverRang1',label:'周转区间', width:'80',classname:'columnrang',formatter:(row)=>formatTurnoverRang(row.turnoverRang1,1)},
                  {istrue:true,prop:'saleCount1',label:'销量',sortable:'custom', width:'60',classname:'columnsalase'},
                  {istrue:true,prop:'saleCountQj1',label:'期间销量',sortable:'custom', width:'80'},
                  // {istrue:true,prop:'purchaseInDays201',label:'20天进仓数',sortable:'custom', width:'95'},
                  {istrue:true,prop:'turnover1',label:'周转天数',sortable:'custom', width:'80',classname:'columndays'}]},
        {istrue:true,prop:'changeTurnoverDay',label:`周转天数变化`, width:'70',sortable:'custom'},
        {istrue:true,prop:'changeStock',label:`库存增加`, width:'60',sortable:'custom'}];//goodsCode
    },
    async onSearch() {
       if (!this.filter.date1) {
         this.$message({message: "请选择日期1",type: "warning",});
         return;
        }
       if (!this.filter.date2) {
         this.$message({message: "请选择日期2",type: "warning",});
         return;
        }
      await this.inittableCols()
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async onfresh() { 
      await this.onSearch()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter,... this.filter1}
      this.listLoading = true
      const res = await getProCodeUnsalable(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false,
        d.goods={code:d.goodsCode,name:d.goodsName }
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
    async toorergoodssales(row,column,cell){
        this.$router.push({path: '/order/ordergoodssales', query: {goodsCode: row.goodsCode}})
    },
    async showonanalysis(row,column,cell){
        await this.onanalysis(row)
    },
    async onanalysis(row){
        this.dialogVisible=true;
        this.analysisfilter.proBianMa=row.goodsCode;
        this.analysisfilter.endDate=this.filter.date2;
        var date2=await this.addDate(this.analysisfilter.endDate,-90);
        this.analysisfilter.startDate=await this.datetostr(new Date(date2));
        this.$nextTick(() => {
           this.$refs.probianmaanalysis1.onpk(row.goodsCode,row.warehouse);
        });
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    selsChange: function(sels) {
      this.sels = sels
    },
    async onExport() {
     if (this.onExporting) return;
     try{
        const params = {...this.pager,... this.filter}
        var res= await exportProCodeUnsalable(params);
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','滞销分析导出_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },
  }
}
</script>
<style scoped>
 .columnsalase{
   background-color: rgb(203, 252, 203);
 }
.columnstock{
   background-color: rgb(131, 134, 131);
 }
.columnamont{
   background-color: rgb(241, 230, 223);
 }
.columnrang{
   background-color: rgb(117, 236, 252);
 }
 .columndays{
   background-color: rgb(245, 81, 122);
 }
</style>