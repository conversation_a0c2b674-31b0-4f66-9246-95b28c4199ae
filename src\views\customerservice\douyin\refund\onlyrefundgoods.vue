<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter">
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='pagelist' @select='selectchange' :isSelection='false'
            :tableCols='tableCols' :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:0">
                        <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期"
                            style="width: 250px" :value-format="'yyyy-MM-dd'" @change="changeTime">
                        </el-date-picker>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:0">
                        <el-input v-model.trim="filter.goodsCode" placeholder="商品编码" style="width:160px;" clearable
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:0">
                        <el-input v-model.trim="filter.goodsName" placeholder="商品名称" style="width:160px;" clearable
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:0">
                        <el-select v-model="filter.batchStr" placeholder="批次号" style="width:120px;" filterable
                            clearable>
                            <el-option v-for="item in pettyPaymentList" :key="item.batchStr" :label="item.batchStr"
                                :value="item.batchStr">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onImportOnlyRefund" style="margin-left: 20px;">导入</el-button>
                    <el-button type="primary" @click="onExportOnlyRefund">导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getpageList" />
        </template>

        <el-dialog title="导入抖音退款" :visible.sync="dialogVisibleRefund" width="30%" :close-on-click-modal="false"
            v-dialogDrag>
            <el-form ref="improtRefundForm" :model="improtRefundForm" label-width="55px" label-position="left">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2"
                            :on-change="onUploadChange2" :on-remove="onUploadRemove2">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                                :loading="uploadLoading" @click="onSubmitupload2">{{ (uploadLoading ? '上传中' : '上传')
                                }}</my-confirm-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleRefund = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import dayjs from 'dayjs'
import { pickerOptions } from '@/utils/tools'
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import {
    getOnlyRefundGoodsPageList, importAlbbGroupAsync, exportOnlyRefundGoodsList
} from '@/api/customerservice/douyinrefund'

const tableCols = [
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '150', sortable: 'custom', type: 'click', handle: (that, row) => that.jumponlyrefundorder(row) },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: '500', sortable: 'custom' },
    { istrue: true, prop: 'onlyOrderCount', label: '仅退款单量-占比', width: '130', sortable: 'custom',
        formatter: (row) => {
            const number=row.onlyOrderCountProportion>0 && row.onlyOrderCountProportion!=null ? row.onlyOrderCountProportion : 0 ;
          return row.onlyOrderCount +" / "+ number+"%";
        },
    },
    { istrue: true, prop: 'onlyYSendOrderCount', label: '已发货仅退款单量-占比', width: '170', sortable: 'custom', type: 'click',
        formatter: (row) => {
            const number=row.onlyYSendOrderCountProportion>0  && row.onlyYSendOrderCountProportion!=null ? row.onlyYSendOrderCountProportion:0;
          return row.onlyYSendOrderCount+' / '+ number + " %"
        },
        handle: (that, row) => that.jumponlyrefundgoodssend(row) },
    { istrue: true, prop: 'onlyNSendOrderCount', label: '未发货仅退款单量-占比', width: '170', sortable: 'custom',
        formatter: (row) => {
              const number=row.onlyNSendOrderCountProportion>0 ? row.onlyNSendOrderCountProportion :0;
          return row.onlyNSendOrderCount+' / '+ number + " %"
        },
     },
    { istrue: true, prop: 'onlyAllAmount', label: '仅退款总金额-占比', width: '170', sortable: 'custom',
        formatter: (row) => {
            const number= row.onlyAllAmountProportion>0 && row.onlyAllAmountProportion!=null ? row.onlyAllAmountProportion : 0;
          return row.onlyAllAmount+' / '+ number + " %"
        },
     },
    { istrue: true, prop: 'onlyYSendOrderAmount', label: '已发货仅退款金额-占比', width: '170', sortable: 'custom',
        formatter: (row) => {
            const number= row.onlyYSendOrderAmountProportion>0 && row.onlyYSendOrderAmountProportion!=null ? row.onlyYSendOrderAmountProportion : 0;
          return row.onlyYSendOrderAmount+' / '+ number + " %"
        },
     },
    { istrue: true, prop: 'onlyNSendOrderAmount', label: '未发货仅退款金额-占比', width: '170', sortable: 'custom',
        formatter: (row) => {
            const number= row.onlyNSendOrderAmountProportion>0 && row.onlyNSendOrderAmountProportion!=null ? row.onlyNSendOrderAmountProportion :0;
          return row.onlyNSendOrderAmount+' / '+ number + " %"
        },
     },
    { istrue: true, prop: 'onlysaleAfterReasonCount', label: '仅退款售后原因', width: '150', sortable: 'custom', type: 'click', handle: (that, row) => that.jumponlyrefundgoodsreason(row) },
];
export default {
    name: "onlyrefundgoods",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
    props: { batchStrBaseList: { type: Array, default: () => [] }, },
    data() {
        return {
            that: this,
            pageLoading: false,
            filter: {
                timerange: [],
                startDate: null,
                endDate: null,
                goodsCode: null,
                goodsName: null,
                batchStr: null,
            },
            tableCols: tableCols,
            listLoading: false,
            pagelist: [],
            pettyPaymentList: [],
            total: 0,
            summaryarry: {},
            pager: { OrderBy: "onlyOrderCount", IsAsc: false },
            sels: [], // 列表选中列
            selids: [],

            dialogVisibleRefund: false,
            uploadLoading: false,
            improtRefundForm: {},
        };
    },
    watch: {
        batchStrBaseList: {
            handler: function (newVal, oldVal) {
                this.pettyPaymentList = newVal;
            },
            immediate: true,
        },
    },
    async mounted() {
      if (this.filter.timerange.length == 0) {
            this.filter.startDate = dayjs().format('YYYY-MM-DD')
            this.filter.endDate = dayjs().format('YYYY-MM-DD')
            this.filter.timerange = [this.filter.startDate, this.filter.endDate]
            this.batchScreening()
        }
        this.onSearch();
    },
    methods: {
        updateFilterMonthDay(row, timerange) {
          this.filter.timerange = timerange
          this.filter.goodsCode = row.styleProductCode
          this.onSearch();
        },
        async changeTime(e) {
          this.filter.startDate = e ? e[0] : null
          this.filter.endDate = e ? e[1] : null
          this.filter.timerange = e
          this.batchScreening()
        },
        batchScreening() {
          let dates = [ new Date(this.filter.timerange[0]), new Date(this.filter.timerange[1] + ' 23:59:59') ];
          this.pettyPaymentList = this.batchStrBaseList.filter(item => {
            let createdTime = new Date(item.createdTime);
            return createdTime >= dates[0] && createdTime <= dates[1];
          });
        },
        getParam() {
            if (this.filter.timerange && this.filter.timerange.length > 0) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
                this.$message({ message: "请先选择日期！", type: "warning", });
                return;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };
            return params;
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getpageList();
        },
        async getpageList() {
            const params = this.getParam();
            this.listLoading = true;
            const res = await getOnlyRefundGoodsPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.pagelist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onImportOnlyRefund() {
            this.dialogVisibleRefund = true;
        },
        async onUploadChange2(file, fileList) {
            this.fileList = fileList;
        },
        async onUploadRemove2(file, fileList) {
            this.fileList = [];
        },
        async uploadFile2(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true;
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("groupType", 1);
            const res = await importAlbbGroupAsync(form);
            this.uploadLoading = false;
            if (res?.success) {
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
                this.dialogVisibleRefund = false;
            }
        },
        async uploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        async onSubmitupload2() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload2.submit();
        },
        async onExportOnlyRefund() {
            const params = this.getParam();
            this.listLoading = true;
            const res = await exportOnlyRefundGoodsList(params);
            this.listLoading = false;
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '抖音仅退款商品数据_' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        jumponlyrefundorder(row) {
            row.startDate = this.filter.startDate;
            row.endDate = this.filter.endDate;
            this.$emit("showtab_onlyrefundorder",row) 
            // window.showlist_onlyrefundorder(row);
            // window.showtab_onlyrefundorder();
        },
        jumponlyrefundgoodssend(row) {
            this.$showDialogform({
                path: `@/views/customerservice/douyin/refund/onlyrefundgoodssend.vue`,
                title: row.goodsCode,
                args: { filter: this.filter, goodsCode: row.goodsCode, goodsName: row.goodsName },
                height: '380px',
                width: '700px',
            });
        },
        jumponlyrefundgoodsreason(row) {
            this.$showDialogform({
                path: `@/views/customerservice/douyin/refund/onlyrefundgoodsreason.vue`,
                title: row.goodsCode,
                args: { filter: this.filter, goodsCode: row.goodsCode, goodsName: row.goodsName },
                height: '480px',
                width: '700px',
            });
        },



    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
