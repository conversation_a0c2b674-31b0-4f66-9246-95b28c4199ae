<!-- 卸货数据 -->
<template>
    <MyContainer style="width: 100%">
        <template #header>
            <div class="top">
                <div class="top_left">
                    <el-input placeholder="任务编号" clear maxlength="50" style="width: 150px" class="publicMargin"
                        v-model="ListInfo.taskNo" clearable />
                    <el-input placeholder="货品名称" clear maxlength="50" style="width: 200px" class="publicMargin"
                        v-model="ListInfo.goodsName" clearable />
                    <el-button type="primary" @click="getList" v-if="type" class="publicMargin">查询</el-button>
                    <el-button type="primary" @click="getHisUnloadListAsync" v-else class="publicMargin">查询</el-button>
                    <el-button class="publicMargin" @click="clear">重置</el-button>
                    <div v-if="type" style="display: flex">
                        <div>
                           <span style="font-size: 14px;">工价 : </span>
                            <el-input-number placeholder="工价" clear maxlength="15" :disabled="isDisabled"
                                style="width: 150px" class="publicMargin" v-model="workPrice" :controls="false" :max="10000"
                                :min="0" :precision="2" />
                        </div>
                        <el-button @click="isDisabled = false">修改</el-button>
                        <el-button type="primary" class="publicMargin" @click="editWordPrice">保存</el-button>
                    </div>
                    <el-button type="primary" @click="downLoad" v-if="type">下载模版</el-button>
                </div>
                <div>
                    <el-dropdown v-if="type" class="publicMargin" @command="deleteWorkPrice">
                        <el-button type="primary">
                            批量操作<i class="el-icon-arrow-down el-icon--right"></i>
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="first">删除选中</el-dropdown-item>
                            <el-dropdown-item command="second">一键删除</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                    <el-button type="primary" @click="onexport" v-if="type" >导出</el-button>
                    <el-button type="primary" @click="getUnloadExportData" v-if="!type" >导出</el-button>
                    <el-button type="primary" @click="dialogVisible = true" v-if="type">导入</el-button>
                </div>
            </div>
        </template>
        <vxetablebase :id="'unloadProps202408041638'" ref="table" @checkbox-range-end="chooseCode" :that="that" :isIndex="true" :hasexpand="false"
            :tableData="tableData" :tableCols="tableCols" :isSelection="false" :isSelectColumn="false" :isIndexFixed="false" :indexWidth="30"
            stripe style="width: 100%; height: 100%; margin: 0">
        </vxetablebase>
        <template #footer>
            <my-pagination :sizes="[500, 1000, 2000, 3000]" :page-size="50" ref="pager" :total="unloadTotal"
            @page-change="unloadPagechange" @size-change="unloadSizechange" />
        </template>
     

        <el-dialog title="导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
            <el-row>
                <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                    <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                        accept=".xlsx" :on-change="uploadFile" :file-list="fileList" :on-remove="uploadRemove">
                        <template #trigger>
                            <el-button size="small" type="primary">选取文件</el-button>
                        </template>
                        <el-button style="margin-left: 10px" size="small" type="success"
                            @click="submitUpload">上传</el-button>
                    </el-upload>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    getunloadListAsync,
    updateUnloadWorkPriceAsync,
    exportPackagesProcessingUnloadData,
    importUnloadSetAsync,
    deleteUnloadAsync,
    deleteUnloadAllAsync
} from "@/api/inventory/packagesprocess";
import {
    getHisUnloadListAsync,
    getUnloadExportData,
} from "@/api/inventory/packagesSetProcessing";
const tableCols = [
    { istrue: true, label: "", type: "checkbox",width:60, align: 'left' },
    { istrue: true, prop: "taskNo", label: "任务编号" ,width:115, align: 'left'},
    { istrue: true, prop: "warehouseName", label: "仓库",width:245, align: 'left' },
    {
        istrue: true,
        prop: "goodsImg",
        label: "主图",
        type: "danimages",
        width:170, 
        align: 'left'
    },
    { istrue: true, prop: "goodsName", label: "货品名称",width:295 , align: 'left'},
    { istrue: true, prop: "uploadName", label: "上传人" ,width:75, align: 'left'},
    { istrue: true, prop: "uploadDateStr", label: "上传时间",width:250, align: 'left' },
    { istrue: true, prop: "qty", label: "件数",width:125, align: 'left' },
    { istrue: true, prop: "workPrice", label: "工价",width:'auto' , align: 'left'},
    { istrue: true, prop: "totalPrice", label: "金额" ,width:125, align: 'left'},
];
export default {
    name: "unloadProps",
    props: {
        type: {
            type: Boolean,
            default: true,
        },
        versionId: {
            type: String,
            default: null,
        },
    },
    components: {
        MyContainer,
        vxetablebase,
    },
    data() {
        return {
            ids: [], //删除的id
            workPrice: null, //工价
            tableCols,
            that: this,
            tableData: [],
            unloadTotal: 0,
            ListInfo: {
                currentPage: 1, //当前页
                pageSize: 50, //每页条数
                orderBy: null, //排序字段
                isAsc: true, //排序方式
                taskNo: null, //任务编号
                goodsName: null, //货品名称
                versionId: null, //版本id
            },
            fileList: [],
            dialogVisible: false,
            isDisabled: true, //是否编辑
        };
    },
    mounted() {
        //根据type判断是卸货数据还是卸货历史数据
        if (this.type) {
            this.ListInfo.versionId = null;
            this.getList();
        } else {
            this.ListInfo.versionId = this.versionId;
            this.getHisUnloadListAsync();
        }
    },
    methods: {
        deleteWorkPrice(e) {
            if (e == "first") {
                if (this.ids.length == 0) {
                    this.$message.warning("请选择要删除的数据");
                    return;
                }
                this.$confirm("此操作将永久删除该数据, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(async () => {
                        const { success } = await deleteUnloadAsync({ ids: this.ids });
                        if (success) {
                            this.getList();
                            this.$message.success("删除成功");
                        } else {
                            this.$message.error("删除失败");
                        }
                    })
                    .catch(() => {
                        this.$message({
                            type: "info",
                            message: "已取消删除",
                        });
                    });
            } else {
                this.$confirm("此操作将永久删除卸货数据所有数据, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(async () => {
                        const { success } = await deleteUnloadAllAsync();
                        if (success) {
                            this.getList();
                            this.$message.success("删除成功");
                        } else {
                            this.$message.error("删除失败");
                        }
                    })
                    .catch(() => {
                        this.$message({
                            type: "info",
                            message: "已取消删除",
                        });
                    });
            }
        },
        //下载模版
        downLoad() {
            window.open("../../static/excel/package/卸货数据导入模板.xlsx", "_self");
        },
        //修改卸货数据工价
        async editWordPrice() {
            const { success } = await updateUnloadWorkPriceAsync({
                workPrice: this.workPrice,
            });
            if (success) {
                this.getList();
                this.$message.success("修改成功");
                this.isDisabled = true;
            } else {
                this.$message.error("修改失败");
            }
        },
        //重置
        clear() {
            this.ListInfo.taskNo = null;
            this.ListInfo.goodsName = null;
        },
        async submitUpload() {
            if (this.fileList.length == 0) {
                this.$message.warning("您没有选择任何文件！");
                return;
            }
            const form = new FormData();
            form.append("upfile", this.fileList[0].raw);
            const { success } = await importUnloadSetAsync(form);
            if (success) {
                this.getList();
                this.$message.success("上传成功");
                this.fileList = [];
                this.dialogVisible = false;
            }
        },
        uploadRemove() {
            this.fileList = [];
        },
        async uploadFile(file, fileList) {
            this.fileList = fileList;
        },
        //历史数据导出
        async getUnloadExportData() {
            let params = this.ListInfo;
            //删除currentPage,pageSize
            delete params.currentPage;
            delete params.pageSize;
            const data = await getUnloadExportData(params);
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.setAttribute(
                "download",
                "卸货历史数据" + new Date().toLocaleString() + ".xlsx"
            );
            aLink.click();
        },
        //导出
        async onexport() {
            let params = this.ListInfo;
            //删除currentPage,pageSize
            delete params.currentPage;
            delete params.pageSize;
            const { data } = await exportPackagesProcessingUnloadData(params);
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.setAttribute(
                "download",
                "卸货数据" + new Date().toLocaleString() + ".xlsx"
            );
            aLink.click();
        },
        chooseCode(row) {
            this.ids = []
            //将row这个数组里面的styleCode放入setCategoryName里面的styleCodes
            this.ids = row.map(item => item.id)
        },
        //页面数量改变
        unloadSizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.PageSize = val;
            this.getList();
        },
        //当前页改变
        unloadPagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },
        async getList() {
            if (this.ListInfo.taskNo || this.ListInfo.goodsName) {
                this.ListInfo.taskNo = this.ListInfo.taskNo.replace(/\s+/g, "");
            }
            if (this.ListInfo.goodsName) {
                this.ListInfo.goodsName = this.ListInfo.goodsName.replace(/\s+/g, "");
            }
            const { data, success } = await getunloadListAsync(this.ListInfo);
            if (success) {
                this.tableData = data.list;
                this.unloadTotal = data.total;
                this.workPrice = data.extData.workPrice;
            } else {
                this.$message.error("获取数据失败");
            }
        },
        //获取卸货历史数据
        async getHisUnloadListAsync() {
            if (this.ListInfo.taskNo || this.ListInfo.goodsName) {
                this.ListInfo.taskNo = this.ListInfo.taskNo.replace(/\s+/g, "");
            }
            if (this.ListInfo.goodsName) {
                this.ListInfo.goodsName = this.ListInfo.goodsName.replace(/\s+/g, "");
            }
            const { data, success } = await getHisUnloadListAsync(this.ListInfo);
            if (success) {
                this.tableData = data.list;
                this.unloadTotal = data.total;
            } else {
                this.$message.error("获取历史数据失败");
            }
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop;
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false;
                this.getList();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.top {
    display: flex;
    width: 100%;
    margin-bottom: 10px;
    justify-content: space-between;

    .top_left {
        display: flex;
    }
}


.publicMargin {
    margin-right: 10px;
}

::v-deep .vxetoolbar20221212 {
    position: absolute;
    top: 60px;
    right: 0px;
    padding-top: 0;
    padding-bottom: 0;
    z-index: 999;
    background-color: rgb(255 255 255 / 0%);
}
</style>
