<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker class="publicCss" v-model="ListInfo.yearMonth" type="month" format="yyyyMM"
          value-format="yyyyMM" placeholder="请选择月份" :clearable="false" style="width: 130px;">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.shopCode" placeholder="店铺编码" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.skuCode" placeholder="SKU" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
      </div>
    </template>
    <vxetablebase :id="'monthlyOutboundRawData202505221156'" :tablekey="'monthlyOutboundRawData202505221156'"
      ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading" :height="'100%'"
      :border="true">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="35%" v-dialogDrag :close-on-click-modal="false">
      <div class="upload-section">
        <div class="upload-row">
          <label class="required-label">
            <span class="required-mark">*</span> 月份选择：
          </label>
          <el-date-picker class="upload-month" v-model="yearMonth" type="month" format="yyyyMM" value-format="yyyyMM"
            placeholder="请选择月份" :clearable="false" />
        </div>
        <div class="upload-row">
          <el-upload ref="upload" class="upload-area" :auto-upload="false" :multiple="false" :limit="1" action
            accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
            <template #trigger>
              <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button size="small" type="success" :loading="uploadLoading" @click="onSubmitUpload" class="upload-btn">
              {{ uploadLoading ? '上传中' : '上传' }}
            </el-button>
          </el-upload>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { importMonthOutbound, } from '@/api/monthbookkeeper/import'
import { getMonthOutboundPageList, deleteMonthOutbound } from '@/api/monthbookkeeper/financialDetail'
const tableCols = [
  { sortable: 'custom', width: '100', align: 'center', prop: 'yearMonth', label: '年月', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'shopCode', label: '店铺编码', },
  { sortable: 'custom', width: '160', align: 'center', prop: 'shop', label: '店铺', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'time', label: '时间', },
  { sortable: 'custom', width: '160', align: 'center', prop: 'productName', label: '商品名称', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'skuCode', label: 'SKU', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'upperAndLowerCabinetStatus', label: '上下柜状态', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'spuCode', label: 'SPU编码', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'nationalSpotInventory', label: '全国现货库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'lockedNationwide', label: '全国已锁定', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'nationalAvailableInventory', label: '全国可用库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutNationwideYesterday', label: '全国昨日出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutNationwideMonth', label: '全国月至今出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'nationalOutboundAmountYesterday', label: '全国昨日出库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'nationalOutboundAmountMonth', label: '全国月至今出库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'shangHaiSpotInventory', label: '上海现货库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'lockedShangHai', label: '上海已锁定', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'shangHaiAvailableInventory', label: '上海可用库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutOfShangHaiYesterday', label: '上海昨日出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutOfShangHaiMonth', label: '上海月至今出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'shangHaiOutboundAmountYesterday', label: '上海昨日出库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'shangHaiOutboundAmountMonth', label: '上海月至今出库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'chengDuSpotInventory', label: '成都现货库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'lockedChengDu', label: '成都已锁定', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'chengDuAvailableInventory', label: '成都可用库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutOfChengDuYesterday', label: '成都昨日出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutOfChengDuMonth', label: '成都月至今出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'chengDuOutboundAmountYesterday', label: '成都昨日出库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'chengDuOutboundAmountMonth', label: '成都月至今出库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'wuHanSpotInventory', label: '武汉现货库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'lockedWuHan', label: '武汉已锁定', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'wuHanAvailableInventory', label: '武汉可用库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutOfWuHanYesterday', label: '武汉昨日出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutOfWuHanMonth', label: '武汉月至今出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'wuHanOutboundAmountYesterday', label: '武汉昨日出库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'wuHanOutboundAmountMonth', label: '武汉月至今出库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'beiJingSpotInventory', label: '北京现货库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'lockedBeiJing', label: '北京已锁定', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'beiJingAvailableInventory', label: '北京可用库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutOfBeiJingYesterday', label: '北京昨日出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutOfBeiJingMonth', label: '北京月至今出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'beiJingOutboundAmountYesterday', label: '北京昨日出库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'beiJingOutboundAmountMonth', label: '北京月至今出库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'shenYangSpotInventory', label: '沈阳现货库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'lockedShenYang', label: '沈阳已锁定', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'shenYangAvailableInventory', label: '沈阳可用库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutOfShenYangYesterday', label: '沈阳昨日出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutOfShenYangMonth', label: '沈阳月至今出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'shenYangOutboundAmountYesterday', label: '沈阳昨日出库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'shenYangOutboundAmountMonth', label: '沈阳月至今出库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'guangZhouSpotInventory', label: '广州现货库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'lockedGuangZhou', label: '广州已锁定', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'guangZhouAvailableInventory', label: '广州可用库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutOfGuangZhouYesterday', label: '广州昨日出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutOfGuangZhouMonth', label: '广州月至今出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'guangZhouOutboundAmountYesterday', label: '广州昨日出库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'guangZhouOutboundAmountMonth', label: '广州月至今出库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'xiAnSpotInventory', label: '西安现货库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'lockedXiAn', label: '西安已锁定', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'xiAnAvailableInventory', label: '西安可用库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutOfXiAnYesterday', label: '西安昨日出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutOfXiAnMonth', label: '西安月至今出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'xiAnOutboundAmountYesterday', label: '西安昨日出库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'xiAnOutboundAmountMonth', label: '西安月至今出库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'deZhouSpotInventory', label: '德州现货库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'lockedDeZhou', label: '德州已锁定', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'deZhouAvailableInventory', label: '德州可用库存', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutOfDeZhouYesterday', label: '德州昨日出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberOfGoodsShippedOutOfDeZhouMonth', label: '德州月至今出库商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'deZhouOutboundAmountYesterday', label: '德州昨日出库金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'deZhouOutboundAmountMonth', label: '德州月至今出库金额', },
  { istrue: true, fixed: 'right',align: 'center', type: "button", label: '操作', width: "120", 
    btnList: 
    [
      { label: "删除", handle: (that, row) => that.delete(row) }
    ] 
  }
]
export default {
  name: "monthlyOutboundRawData",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      yearMonth: null,//导入时间
      dialogVisible: false,//导入弹窗
      fileList: [],//上传文件列表
      uploadLoading: false,//上传按钮loading
      fileparm: {},//上传文件参数
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        yearMonth: dayjs().subtract(1, 'month').format('YYYYMM'),//年月
        shopCode: null,//店铺编码
        skuCode: null,//SKU
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearMonth", this.yearMonth);
      var res = await importMonthOutbound(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (!this.yearMonth) {
        this.$message({ message: "请选择月份", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.yearMonth = dayjs().subtract(1, 'month').format('YYYYMM')
      this.dialogVisible = true;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getMonthOutboundPageList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.time = item.time ? dayjs(item.time).format('YYYY-MM-DD') : ''
        })
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //删除
    async delete(row){
      this.$confirm('此操作将删除此月数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const request = {
          YearMonth : row.yearMonth
        }
        deleteMonthOutbound(request).then(res => {
          if (res.success) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      })
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 5px;
  }
}

.upload-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 12px 0;
}

.upload-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.required-label {
  font-weight: 500;
  color: #333;
}

.required-mark {
  color: red;
  margin-right: 4px;
}

.upload-month {
  width: 200px;
}

.upload-area {
  display: flex;
  align-items: center;
  gap: 10px;
}

.upload-btn {
  margin-left: 0;
}
</style>
