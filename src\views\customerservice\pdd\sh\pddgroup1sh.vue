1
<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='grouplist' @select='selectchange' :isSelection='true' :tableCols='tableCols'
            :loading="listLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
                <el-button-group>

                    <el-button style="padding: 0;margin: 0;">
                        <el-select multiple v-model="Filter.GroupNames" placeholder="组名称" clearable
                            :collapse-tags="true" filterable>
                            <el-option v-for="item in grouplistsel" :key="item.groupname" :label="item.groupname"
                                :value="item.groupname" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="Filter.Shopname" placeholder="店铺名称" clearable :collapse-tags="true"
                            style="width: 140px" filterable>
                            <el-option v-for="item in shopList" :key="item.shopname" :label="item.shopname"
                                :value="item.shopname" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input clearable v-model="Filter.Sname" v-model.trim="Filter.Sname" placeholder="姓名"
                            style="width:120px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input clearable v-model="Filter.Snick" v-model.trim="Filter.Snick" placeholder="昵称"
                            style="width:120px;" />
                    </el-button>


                    <el-button style="padding: 0;margin: 0;">

                        <el-switch :width="40" @change="changeingroup" v-model="Filter.isleavegroup"
                            inactive-color="#228B22" active-text="包含离组" inactive-text="当前在组">
                        </el-switch>

                    </el-button>
                    <!-- <el-button type="text" size="medium" disabled style="color: red;">周转对比:</el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input-number v-model="filter1.startdiff"></el-input-number>至<el-input-number v-model="filter1.enddiff"></el-input-number>
            </el-button> -->
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="addgroupdialogVisibleSyj = true">添加</el-button>
                    <el-button type="primary" @click="onImportSyj">导入</el-button>
                    <el-button type="primary" @click="ClickdownloadTemplate">下载模板</el-button>
                    <el-button type="primary" @click="batchLeaveGroup">批量离组</el-button>
                    <el-button type="primary" @click="setSubstitute"
                        v-if="checkPermission('setSubstitutePerson')">转移数据</el-button>
                    <el-button type="primary" @click="showSubstituteLog"
                        v-if="checkPermission('getSubstitutePersonLog')">转移数据日志</el-button>
                    <el-button type="primary" @click="showEditLog">分组编辑日志</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getgroupList" />
        </template>

        <el-dialog title="添加客服人员分组管理信息" :visible.sync="addgroupdialogVisibleSyj" width="40%"
            :close-on-click-modal="false" v-dialogDrag>
            <span>
                <el-form :model="addForm" ref="addForm" :rules="addFormRules">
                    <el-form-item label="分组" prop="groupname">
                        <el-input style="width:83%" clearable v-model="addForm.groupname"
                            v-model.trim="addForm.groupname"></el-input>
                    </el-form-item>
                    <el-form-item label="组长" prop="groupManager">
                        <el-input style="width:83%" clearable v-model="addForm.groupManager"
                            v-model.trim="addForm.groupManager"></el-input>
                    </el-form-item>
                    <el-form-item label="店铺" prop="shopname">
                        <el-input style="width:83%" clearable v-model="addForm.shopname"
                            v-model.trim="addForm.shopname"></el-input>
                    </el-form-item>
                    <el-form-item label="姓名" prop="sname">
                        <el-input style="width:83%" clearable v-model="addForm.sname"
                            v-model.trim="addForm.sname"></el-input>
                    </el-form-item>
                    <el-form-item label="昵称" prop="snick">
                        <el-input style="width:83%" clearable v-model="addForm.snick"
                            v-model.trim="addForm.snick"></el-input>
                    </el-form-item>
                    <el-form-item label="绑定手机号">
                        <el-input style="width:73%" clearable v-model="addForm.phoneNo"
                            v-model.trim="addForm.phoneNo"></el-input>
                    </el-form-item>
                    <el-form-item label="入组日期" prop="joinDate">
                        <el-date-picker v-model="addForm.joinDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            type="date" style="width:63%" placeholder="选择日期">
                        </el-date-picker>
                        <!-- <el-input   v-model="addForm.JoinDate"></el-input> -->
                    </el-form-item>
                    <el-form-item label="离组日期" prop="leaveDate">
                        <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="addForm.leaveDate"
                            style="width:63%" type="date" placeholder="选择日期">
                        </el-date-picker>
                        <!-- <el-input   v-model="addForm.LeaveDate"></el-input> -->
                    </el-form-item>
                </el-form>
            </span>

            <span slot="footer" class="dialog-footer">
                <el-button @click="addgroupdialogVisibleSyj = false">关闭</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="addgroup">确定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="修改客服人员分组管理信息" :visible.sync="updategroupdialogVisibleSyj" width="40%"
            :close-on-click-modal="false" v-dialogDrag>
            <span>
                <el-form :model="updateForm" ref="updateForm" :rules="addFormRules">
                    <el-form-item label="分组" prop="groupname">
                        <el-input style="width:83%" v-model="updateForm.groupname"
                            v-model.trim="updateForm.groupname"></el-input>
                    </el-form-item>
                    <el-form-item label="组长" prop="groupManager">
                        <el-input style="width:83%" v-model="updateForm.groupManager"
                            v-model.trim="updateForm.groupManager"></el-input>
                    </el-form-item>
                    <el-form-item label="店铺" prop="shopname">
                        <el-input style="width:83%" v-model="updateForm.shopname"
                            v-model.trim="updateForm.shopname"></el-input>
                    </el-form-item>

                    <el-form-item label="姓名" prop="sname">
                        <el-input style="width:83%" v-model="updateForm.sname"
                            v-model.trim="updateForm.sname"></el-input>
                    </el-form-item>
                    <el-form-item label="昵称" prop="snick">
                        <el-input style="width:83%" v-model="updateForm.snick"
                            v-model.trim="updateForm.snick"></el-input>
                    </el-form-item>
                    <el-form-item label="绑定手机号">
                        <el-input style="width:83%" v-model="updateForm.phoneNo"
                            v-model.trim="updateForm.phoneNo"></el-input>
                    </el-form-item>
                    <el-form-item label="入组日期" prop="joinDate">
                        <el-date-picker v-model="updateForm.joinDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            type="date" style="width:63%" placeholder="选择日期">
                        </el-date-picker>
                        <!-- <el-input   v-model="addForm.JoinDate"></el-input> -->
                    </el-form-item>
                    <el-form-item label="离组日期" prop="leaveDate">
                        <el-date-picker v-model="updateForm.leaveDate" style="width:63%" format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                        </el-date-picker>
                        <!-- <el-input   v-model="addForm.LeaveDate"></el-input> -->
                    </el-form-item>
                </el-form>
            </span>

            <span slot="footer" class="dialog-footer">
                <el-button @click="updategroupdialogVisibleSyj = false">关闭</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="updategroup()">确定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="客服人员分组管理" :visible.sync="dialogVisibleSyj" width="30%" :close-on-click-modal="false"
            v-dialogDrag>
            <span>
                <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2" :on-change="onUploadChange2"
                    :on-remove="onUploadRemove2">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                        @click="onSubmitupload2">上传</my-confirm-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="离组日期选择" :visible.sync="dialogVisibleLeave" width="30%" :close-on-click-modal="false"
            v-dialogDrag>
            <span>
                <template class="block">
                    <!-- <el-date-picker v-model="dialogLeaveDate" align="right" type="date" placeholder="选择日期" ></el-date-picker> -->
                    <el-date-picker v-model="dialogLeaveDate" style="width:63%" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                    </el-date-picker>
                </template>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleLeave = false">关闭</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="updategroupLeave()">提交</el-button>
            </span>
        </el-dialog>
        <el-dialog title="设置修改人" :visible.sync="setSubstituteDialogVisiable" width="50%" :close-on-click-modal="false"
            v-dialogDrag>
            <div>
                <el-button type="primary" @click="addSubstitute">设置修改人</el-button>
            </div>
            <vxetablebase :id="'setSubstitute202408041706_2'" ref="tabulartable" :that='that' :isIndex='true'
                :hasexpand='true' :tablefixed='true' :tableData='substituteTableData' :tableCols='substituteTableCols'
                :isSelection="false" :isSelectColumn="false" style="width: 100%;margin: 0" v-loading="substituteLoading"
                :height="'450px'">
            </vxetablebase>
        </el-dialog>
        <el-dialog title="选择修改人" :visible.sync="addSubstituteDialogVisiable" width="25%" :close-on-click-modal="false"
            v-dialogDrag>
            <span>
                <el-form>
                    <el-form-item label="姓名">
                        <el-select style="width:160px" clearable filterable remote reserve-keyword placeholder="请选择"
                            :remote-method="substituteMethod" :loading="substituteMethodLoading"
                            v-model.trim="substituteName" :collapse-tags="true">
                            <el-option v-for="(item, i) in substituteNameData" :key="'substituteNameData' + i + 1"
                                :label="item.sname + '-' + item.groupName + (item.groupManager ? '-' + item.groupManager : '')"
                                :value="(item.sname ?? '') + '-' + (item.groupName ?? '') + '-' + (item.groupManager ?? '')" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="修改日期" prop="substituteDate">
                        <el-date-picker style="width: 280px" v-model="substituteDateRange" type="datetimerange"
                            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                            end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                </el-form>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="saveSubstitute()">确定</el-button>
            </span>
        </el-dialog>
        <el-dialog title="修改日志" :visible.sync="substituteLogDialogVisiable" width="65%" :close-on-click-modal="false"
            v-dialogDrag>
            <div style="display: flex;align-items: center;margin: 10px 0;gap: 10px;">
                <div>
                    <span>修改时间</span>
                    <el-date-picker style="width: 280px" v-model="substituteCreatedDateQuery" type="datetimerange"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                </div>
                <div>
                    <span>修改数据时间段</span>
                    <el-date-picker style="width: 280px" v-model="substituteDateQuery" type="datetimerange"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                </div>
                <el-button type="primary" @click="getSubstituteLog">查询</el-button>
            </div>
            <vxetablebase :id="'substituteLog202412150900'" ref="tabulartable" :that='that' :isIndex='true'
                :hasexpand='true' :tablefixed='true' @sortchange='substituteLogSortChange'
                :tableData='substituteLogList' :tableCols='substituteLogTableCols' :isSelection="false"
                :isSelectColumn="false" style="width: 100%;margin: 0" v-loading="substituteLogLoading"
                :height="'450px'">
            </vxetablebase>
            <!--分页-->
            <template #footer>
                <my-pagination ref="pager" :total="substituteLogTotal" @page-change="substituteLogPagechange"
                    @size-change="substituteLogSizechange" />
            </template>
        </el-dialog>
    </my-container>
</template>
<script>
import {
    getPddGroupList, getPddGroup, getPddShop, importPddGroupAsync, addgroup, deletegroup, updategroupinfo, batchUpdatePddLeaveGroup
    , saveSubstitutePersonData,
    getSubstituteList,
    getSubstitutePersonPage
} from '@/api/customerservice/pddInquirs'


import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
const tableCols = [
    { istrue: true, prop: 'groupname', label: '分组', width: '180', sortable: 'custom' },
    { istrue: true, prop: 'groupManager', label: '组长', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'shopname', label: '店铺', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'snick', label: '昵称', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'phoneNo', label: '绑定手机号', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'joinDate', label: '入组日期', width: '120', sortable: 'custom', formatter: (row) => { return formatTime(row.joinDate, 'YYYY-MM-DD') } },
    { istrue: true, prop: 'leaveDate', label: '离组日期', width: '120', sortable: 'custom', formatter: (row) => { return formatTime(row.leaveDate, 'YYYY-MM-DD') } },
    { istrue: true, prop: 'createdTime', label: '导入时间', width: '200', sortable: 'custom' },
    { istrue: true, type: "button", label: '操作', width: "120", btnList: [{ label: "编辑", handle: (that, row) => that.handleupdategroup(row) }, { label: "删除", handle: (that, row) => that.deletegroup(row) }] }
];
const substituteTableCols = [
    { istrue: true, prop: 'groupname', label: '分组', width: 'auto', align: 'center' },
    { istrue: true, prop: 'groupManager', label: '组长', width: 'auto', align: 'center' },
    { istrue: true, prop: 'shopname', label: '店铺', width: 'auto', align: 'center' },
    { istrue: true, prop: 'sname', label: '姓名', width: 'auto', align: 'center' },
    { istrue: true, prop: 'snick', label: '昵称', width: 'auto', align: 'center' },
    {
        istrue: true, type: 'button', label: '操作', width: '150', align: 'center',
        btnList: [
            {
                label: "删除", handle: (that, row) => that.removeSubstitute(row)
            },
        ]
    }
];
const substituteLogTableCols = [
    { istrue: true, prop: 'shopName', label: '店铺', width: 'auto', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'groupName', label: '分组(修改前)', width: 'auto', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'substituteGroup', label: '分组(修改后)', width: 'auto', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'groupManager', label: '组长(修改前)', width: 'auto', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'substituteManager', label: '组长(修改后)', width: 'auto', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名(修改前)', width: 'auto', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'substituteName', label: '姓名(修改后)', width: 'auto', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'snick', label: '昵称', width: 'auto', align: 'center', sortable: 'custom' },
    {
        istrue: true, prop: 'createdTime', label: '修改时间', width: 'auto', align: 'center', sortable: 'custom', formatter: (row) => {
            return row.createdTime ? formatTime(row.createdTime, 'YYYY-MM-DD') : '';
        }
    },
    { istrue: true, prop: 'createdUserName', label: '修改人', width: 'auto', align: 'center', sortable: 'custom' },
    {
        istrue: true, prop: 'updateDates', label: '修改数据时间段', width: '180', align: 'center', formatter: (row) => {
            return (row.substituteStartDate ? formatTime(row.substituteStartDate, 'YYYY-MM-DD') : '') + '至' +
                (row.substituteEndDate ? formatTime(row.substituteEndDate, 'YYYY-MM-DD') : '');
        }
    }
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, vxetablebase },
    inject: ['reload'],
    data() {
        return {
            that: this,
            Filter: {
                Sdate: [formatTime(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30), "YYYY-MM-DD 00:00:00"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],
                isleavegroup: false,
            },
            addForm: {
                groupname: null,
                groupManager: null,
                shopname: null,
                sname: null,
                snick: null,
                phoneNo: null,
                joinDate: null,
                leaveDate: null,

            },
            updateForm: {
                groupname: null,
                groupManager: null,
                shopname: null,
                sname: null,
                snick: null,
                phoneNo: null,
                joinDate: null,
                leaveDate: null,
            },
            shopList: [],
            userList: [],
            grouplist: [],
            grouplistsel: [],
            tableCols: tableCols,
            total: 0,
            addgroupdialogVisibleSyj: false,
            dialogVisibleLeave: false,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            updategroupdialogVisibleSyj: false,
            //
            selids: [],
            dialogVisibleSyj: false,
            addSubstituteDialogVisiable: false,
            setSubstituteDialogVisiable: false,
            substituteLogDialogVisiable: false,
            substituteLoading: false,//弹窗表格加载
            substituteTableData: [],//弹窗表格数据
            substituteTableCols,
            substituteName: null,
            substituteDateRange: [],
            substituteMethodLoading: false,
            substituteNameData: [],
            substituteDateQuery: [],
            substituteCreatedDateQuery: [],
            substituteLogList: [],
            substituteLogTableCols,
            substituteLogLoading: false,
            substituteLogQueryParams: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: null,
                endDate: null,
                substituteStartDate: null,
                substituteEndDate: null,
                enmPddGroupType: null
            },
            substituteLogTotal: 0,
            fileList: [],
            addFormRules: {
                groupname: [{ required: true, message: '必填', trigger: 'blur' }],
                groupManager: [{ required: true, message: '必填', trigger: 'blur' }],
                shopname: [{ required: true, message: '必填', trigger: 'blur' }],
                sname: [{ required: true, message: '必填', trigger: 'blur' }],
                snick: [{ required: true, message: '必填', trigger: 'blur' }],
                joinDate: [{ required: true, message: '必填', trigger: 'blur' }],
                leaveDate: [{ required: true, message: '必填', trigger: 'blur' }]
            },
            pickerOptions: {
                disabledDate(time) {
                    return (time.getTime() > Date.now() && time.getTime() < Date.now() - 8.64e7);
                },
                shortcuts: [{
                    text: '今天',
                    onClick(picker) {
                        picker.$emit('pick', new Date());
                    }
                }, {
                    text: '昨天',
                    onClick(picker) {
                        const date = new Date();
                        date.setTime(date.getTime() - 3600 * 1000 * 24);
                        picker.$emit('pick', date);
                    }
                }, {
                    text: '一周前',
                    onClick(picker) {
                        const date = new Date();
                        date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', date);
                    }
                }]
            },
            dialogLeaveDate: null,
        };
    },
    async mounted() {
        await this.setShopSelect();
        await this.setGroupSelect();
    },
    methods: {
        //提交保存时验证
        onSubmitValidate: function () {
            let isValid = true;
            this.$refs.addForm.validate(valid => {
                isValid = valid
            })
            return isValid;
        },
        //提交保存时验证
        onSubmitValidate2: function () {
            let isValid = true;
            this.$refs.updateForm.validate(valid => {
                isValid = valid
            })
            return isValid;
        },
        async setGroupSelect() {
            console.log('分组管理，分组下拉')
            const form = new FormData();
            form.append("enmPddGroupType", 1);
            form.append("isleavegroup", this.Filter.isleavegroup);
            const res = await getPddGroup(form);
            this.grouplistsel = res.data;
        },
        async setShopSelect() {
            const form = new FormData();
            form.append("enmPddGroupType", 1);
            const res = await getPddShop(form);
            this.shopList = res.data;
        },
        async addgroup() {
            if (!this.onSubmitValidate()) {
                return;
            }
            var that = this;
            // this.$confirm("确定添加分组管理数据?", "提示", {
            //   confirmButtonText: "确定",
            //   cancelButtonText: "取消",
            //   type: "warning",
            // })
            //   .then(async() => {
            //   });
            that.addForm.pddGroup = 1;
            that.addForm.platform = 2;
            var res = await addgroup(that.addForm)
            if (res?.success) {
                that.$message({ message: '已添加', type: "success" });
                that.onRefresh()
                that.addForm = {}
                that.addgroupdialogVisibleSyj = false
            }
        },
        async handleupdategroup(row) {
            this.updateForm = row
            this.updategroupdialogVisibleSyj = true;

        },
        async updategroup(row) {
            if (!this.onSubmitValidate2()) {
                return;
            }
            var that = this

            var res = await updategroupinfo(that.updateForm)
            if (res?.success) {
                that.$message({ message: '操作成功', type: "success" });
                that.onRefresh()
                that.updateForm = {}
                this.updategroupdialogVisibleSyj = false;
            }


        },

        async deletegroup(row) {
            var that = this;
            this.$confirm("此操作将删除此客服人员分组管理数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(async () => {
                    var res = await deletegroup({ id: row.id })
                    if (res?.success) {
                        that.$message({ message: '已删除', type: "success" });
                        that.onRefresh()
                    }

                });

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onImportSyj() {
            this.dialogVisibleSyj = true
        },
        async onUploadChange2(file, fileList) {
            this.fileList = fileList;
        },
        async onUploadRemove2(file, fileList) {
            this.fileList = [];
        },
        async uploadFile2(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("platform", 2);
            form.append("pddGroup", 1);
            const res = await importPddGroupAsync(form);
            if (res?.success) {
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
                this.dialogVisibleSyj = false;
            }
        },
        async uploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        async onSubmitupload2() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload2.submit()
        },
        onRefresh() {
            this.setShopSelect();
            this.setGroupSelect();
            this.onSearch()
            var self = this;
            setTimeout(() => {
                self.reload();
            }, 100);
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getgroupList();
        },
        changeingroup() {
            console.log('离组按钮')
            this.$emit("callBackInfoH", this.Filter.isleavegroup)
            this.setGroupSelect();
            this.onSearch();
        },
        async getgroupList() {
            if (this.Filter.UseDate) {
                this.Filter.startAccountDate = this.Filter.UseDate[0];
                this.Filter.endAccountDate = this.Filter.UseDate[1];
            }
            this.Filter.EnmPddGroupType = 1;
            const para = { ...this.Filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
                platform: 2,
                EnmPddGroupType: 1,
            };

            console.log(para)

            this.listLoading = true;
            const res = await getPddGroupList(params);
            console.log(res)
            this.listLoading = false;
            console.log(res.data.list)
            //console.log(res.data.summary)

            this.total = res.data.total
            this.grouplist = res.data.list;
            //this.summaryarry=res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            this.substituteTableData = [];
            rows.forEach(f => {
                this.selids.push(f.id);
                this.substituteTableData.push({
                    id: f.id,
                    groupname: f.groupname,
                    groupManager: f.groupManager,
                    shopname: f.shopname,
                    sname: f.sname,
                    snick: f.snick
                });
            })
        },
        ClickdownloadTemplate() {
            window.open("/static/excel/customerservice/分组导入模板.xlsx", "_blank");
            //window.location.href = '/static/excel/customerservice/咨询数据模板.xlsx';
        },
        //批量离组
        async batchLeaveGroup() {
            if (this.selids.length == 0) {
                this.$message({ message: "至少选择一行", type: "warning", });
                this.editparmVisible = false
                return
            }
            this.dialogVisibleLeave = true;
        },
        async updategroupLeave() {
            if (this.dialogLeaveDate == null || this.dialogLeaveDate == '') {
                this.$message({ message: "请选择日期", type: "warning" });
                return false;
            }
            // if (Date.parse(this.dialogLeaveDate) < (Date.now() - 3600 * 1000 * 24) ) {
            //     this.$message({ message: "离组日期不得早于当前日期", type: "warning" });
            //     return false;
            // }
            const params = {
                dialogLeaveDate: this.dialogLeaveDate,
                ids: this.selids.join(',')
            };
            var res = await batchUpdatePddLeaveGroup(params);
            if (res == true) {
                this.$message({ message: "修改成功", type: 'success', });
                this.getgroupList();
                this.dialogVisibleLeave = false;
                this.ids = null;
                this.dialogLeaveDate = null;
            }
        },
        setSubstitute() {
            if (this.substituteTableData.length == 0) {
                this.$message({ message: "至少选择一行", type: "warning", });
                return
            }
            this.setSubstituteDialogVisiable = true;
        },
        addSubstitute() {
            this.substituteNameData = [];
            this.substituteName = null;
            this.substituteDateRange = [];
            this.addSubstituteDialogVisiable = true;
        },
        async saveSubstitute() {
            if (!this.substituteName) {
                this.$message({ message: "请选择姓名", type: "error", });
            }
            if (this.substituteDateRange.length <= 0) {
                this.$message({ message: "请选择修改数据时间段", type: "error", });
            }

            var arr = this.substituteName.split("-");
            var params = {
                substituteName: arr[0],
                substituteGroup: arr[1],
                substituteManager: arr[2],
                startDate: this.substituteDateRange[0],
                endDate: this.substituteDateRange[1],
                enmPddGroupType: 1,
                originalData: this.substituteTableData
            };

            var res = await saveSubstitutePersonData(params);
            if (res?.success) {
                this.setSubstituteDialogVisiable = false;
                this.addSubstituteDialogVisiable = false;
                this.substituteName = null;
                this.substituteDateRange = [];
                this.$message({ message: "设置成功", type: "success", });
            }

        },
        async showSubstituteLog() {
            this.substituteLogDialogVisiable = true;
            await this.getSubstituteLog();
        },
        removeSubstitute(row) {
            if (this.substituteTableData.length == 1) {
                this.$message({ message: "至少保留一行数据", type: "warning", });
                return
            }
            this.substituteTableData = this.substituteTableData.filter(item => item.id !== row.id);
        },
        async substituteMethod(e) {
            this.substituteMethodLoading = true;
            this.substituteNameData = [];
            var params = {
                sname: e,
                enmPddGroupType: 1
            };
            var res = await getSubstituteList(params);
            if (res?.success) {
                var list = res.data;
                list.map(x => {
                    this.substituteNameData.push({
                        groupName: x.groupName,
                        sname: x.sname,
                        groupManager: x.groupManager
                    });
                });
            }
            this.substituteMethodLoading = false;
        },
        async getSubstituteLog() {
            this.substituteLogLoading = true;
            this.substituteLogList = [];
            if (this.substituteCreatedDateQuery && this.substituteCreatedDateQuery.length > 1) {
                this.substituteLogQueryParams.startDate = this.substituteCreatedDateQuery[0];
                this.substituteLogQueryParams.endDate = this.substituteCreatedDateQuery[1];
            } else {
                this.substituteLogQueryParams.startDate = null;
                this.substituteLogQueryParams.endDate = null;
            }
            if (this.substituteDateQuery && this.substituteDateQuery.length > 1) {
                this.substituteLogQueryParams.substituteStartDate = this.substituteDateQuery[0];
                this.substituteLogQueryParams.substituteEndDate = this.substituteDateQuery[1];
            } else {
                this.substituteLogQueryParams.substituteStartDate = null;
                this.substituteLogQueryParams.substituteEndDate = null;
            }
            this.substituteLogQueryParams.enmPddGroupType = 1;
            var res = await getSubstitutePersonPage(this.substituteLogQueryParams);
            this.substituteLogList = res?.list ?? [];
            this.substituteLogTotal = res?.total ?? 0;
            this.substituteLogLoading = false;
        },
        async substituteLogPagechange(val) {
            this.substituteLogQueryParams.currentPage = val;
            await this.getSubstituteLog();
        },
        async substituteLogSizechange(val) {
            this.substituteLogQueryParams.currentPage = 1;
            this.substituteLogQueryParams.pageSize = val;
            await this.getSubstituteLog();
        },
        async substituteLogSortChange(column) {
            this.substituteLogQueryParams.currentPage = 1;
            if (!column.order) {
                this.substituteLogQueryParams.orderBy = null;
                this.substituteLogQueryParams.isAsc = false;
            }
            else {
                this.substituteLogQueryParams.orderBy = column.prop;
                this.substituteLogQueryParams.isAsc = column.order.indexOf("descending") == -1 ? true : false;
            }
            await this.getSubstituteLog();
        },
        showEditLog() {
            this.$showDialogform({
                path: `@/views/customerservice/pdd/pddgrouplog.vue`,
                title: '分组编辑日志',
                args: {
                },
                height: '600px',
                width: '75%',
                callOk: this.afterSave
            });

        },
        afterSave() {

        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
