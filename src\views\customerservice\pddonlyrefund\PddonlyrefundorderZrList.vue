<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="申请时间" style="padding: 0;margin: 0; ">
                    <el-date-picker style="width: 280px" v-model="filter.timerange" type="datetimerange"
                        format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" range-separator="至"
                        start-placeholder="开始申请时间" end-placeholder="结束申请时间" :picker-options="pickerOptions" >
                    </el-date-picker>
                </el-form-item>               
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-input v-model.trim="filter.saleAfterNo" style="width: 100px" placeholder="售后编号"
                        @keyup.enter.native="onSearch" clearable maxlength="100" />
                </el-form-item>               
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-input v-model.trim="filter.orderNos" style="width: 140px" placeholder="线上单号"
                        @keyup.enter.native="onSearch" clearable maxlength="100" />
                </el-form-item>
            
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-input v-model.trim="filter.keywords" style="width: 200px" placeholder="请输入关键字.."
                        @keyup.enter.native="onSearch" clearable maxlength="100">
                        <el-tooltip slot="suffix" class="item" effect="dark"
                            content="支持搜索的内容：商品ID、物流单号、Sku信息、退款原因、同意退款人" placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :loading="listLoading" >              
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
      
        <el-dialog title="订单日志信息" v-if="dialogHisVisible1" :visible.sync="dialogHisVisible1" width="70%" height="600px"
            v-dialogDrag>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="sendOrderNo"
                style="z-index:10000;height:600px" />
        </el-dialog>
        <el-dialog title="聊天信息" v-if="dialogHisVisible2" :visible.sync="dialogHisVisible2" width="70%" height="600px"
            v-dialogDrag>
            <OrderChatListByInnerNos ref="OrderChatListByInnerNos" :orderNo="sendSaleAfterNo"
                style="z-index:10000;height:600px" />
        </el-dialog>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime,pickerOptions } from "@/utils/tools.js";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { PagePddOnlyRefundZrList } from '@/api/customerservice/pddonlyrefund';
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
import OrderChatListByInnerNos from "@/views/customerservice/pddonlyrefund/pddonlyfundchatLog.vue";
const tableCols = [
    { istrue: true, prop: 'saleAfterNo', label: '售后编号', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showChatLogDetail(row) },
    { istrue: true, prop: 'orderNo', label: '订单编号', width: '180', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogDetail(row) },
    { istrue: true, prop: 'applyTime', label: '申请时间', width: '150', sortable: 'custom' },

    { istrue: true, prop: 'zrDeptAction', label: '责任部门', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'memberName', label: '责任人', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'zrReason', label: '责任原因', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'zrConditionFullName', label: '责任规则', width: '140', sortable: 'custom' },

    { istrue: true, prop: 'zrSetTime', label: '责任时间', width: '146', sortable: 'custom' },
    { istrue: true, prop: 'lanShouHours', label: '揽收时长', width: '80', sortable: 'custom' },


    { istrue: true, prop: 'shopName', label: '店铺', width: '180', sortable: 'custom' },
    { istrue: true, prop: 'goodId', label: '商品ID', width: '110', sortable: 'custom' },
    { istrue: true, prop: 'tradeMoney', label: '交易金额', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'saleAfterState', label: '售后状态', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'refundType', label: '退款类型', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'refundMoney', label: '退款金额', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'orderState', label: '订单状态', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'signState', label: '签收状态', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'expressInterceptState', label: '快递拦截', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'buyer', label: '买家', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'deliverNo', label: '物流单号', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.ShowLogistics(row) },
    { istrue: true, prop: 'overTime', label: '超时时间', width: '150', sortable: 'custom' },
    
    { istrue: true, prop: 'agreeRefundTime', label: '同意退款时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'refundReason', label: '退款原因', width: '130', sortable: 'custom' },
    { istrue: true, prop: 'agreeRefundUser', label: '同意退款人', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'skuInfo', label: 'sku信息', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'orderFlag', label: '订单标记', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'remark', label: '备注', width: '80', sortable: 'custom' },
    //{ istrue: true, prop: 'lastReceiveUser', label: '最后接待人', width: '100' },
    // { istrue: true, prop: 'lastReceiveUserUp', label: '接待人上级',  width: '100'},
    //{ istrue: true, prop: 'lastReceiveMsg', label: '最后一条接待信息', width: '140' },
    
]

export default {
    name: 'PddonlyrefundorderZrList',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, OrderActionsByInnerNos, OrderChatListByInnerNos },
    props: {

    },
    data() {
        return {
          date: '',
            that: this,
            shopList: [],
            filter: {
                timerange: [
                    formatTime(dayjs().subtract(3, "day"), "YYYY-MM-DD HH:mm"),
                    formatTime(new Date(), "YYYY-MM-DD HH:mm"),
                ],
                applyStartDate: null,
                applyEndDate: null,
                timerange2: [],             
                saleAfterNo: '',
                orderNos:'',
                keywords:''
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "applyTime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            pickerOptions: pickerOptions,
          
            dialogVisibleUpload: false,
            fileList: [],
            fileparm: {},
            uploadLoading: false,
            dialogHisVisible: false,
            sendOrderNo: null,
            dialogHisVisible1: false,
            dialogHisVisible2: false,
            sendSaleAfterNo: null
        };
    },
    async mounted() {
      
        await this.onSearch()
    },
    methods: {
      
        //查询第一页
        async onSearch() {
            console.log(this.filter)
            if (!this.filter.timerange) {
                this.$message({ message: "请选择申请时间", type: "warning", });
                return;
            }
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            this.filter.applyStartDate = null;
            this.filter.applyEndDate = null;
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.applyStartDate = this.filter.timerange[0];
                this.filter.applyEndDate = this.filter.timerange[1];
            }
            else {
                this.$message({ message: "请先选择申请时间", type: "warning" });
                return false;
            }
            this.filter.agreeStartDate = null;
            this.filter.agreeEndDate = null;
            if (this.filter.timerange2 && this.filter.timerange2.length > 1) {
                this.filter.agreeStartDate = this.filter.timerange2[0];
                this.filter.agreeEndDate = this.filter.timerange2[1];
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await PagePddOnlyRefundZrList(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
        },
        //排序查 询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },       
        showLogDetail(row) {
            this.dialogHisVisible1 = true;
            this.sendOrderNo = String(row.orderNo);
        },
        showChatLogDetail(row) {
            this.dialogHisVisible2 = true;
            console.log(row.saleAfterNo);
            this.sendSaleAfterNo = String(row.saleAfterNo);
        },
        ShowLogistics(row) {
            let self = this;
            this.$showDialogform({
                path: `@/views/order/logisticsWarning/DbLogisticsRecords.vue`,
                title: '物流明细',
                args: { expressNos: row.deliverNo },
                height: 300,
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>
