<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true'
              :hasexpand='false' @sortchange='sortchange' :tableData='inquirsstatisticslist'
              @select='selectchange' :isSelection='false'
         :tableCols='tableCols' :loading="listLoading">
      <el-table-column type="expand">
        <template slot-scope="props">
        <div>
          <el-table :data="props.row.detaildata" style="width: 100%">
            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
            </el-table-column>
          </el-table>
        </div>
      </template>
      </el-table-column>
       <template slot='extentbtn'>
         
        </template>
    </ces-table>
    <!--分页-->
    
        
  </my-container>
</template>
<script>

import {getInquirsStatisticsByShopList } from '@/api/customerservice/groupinquirsstatistics'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
const tableCols =[
  {istrue:true,prop:'shopname',label:'店名', width:'100',sortable:'custom'},
  {istrue:true,prop:'inquirs',label:'咨询人数', width:'100',sortable:'custom'},
  {istrue:true,prop:'receivecount',label:'接待人数', width:'100',sortable:'custom'},
  {istrue:true,prop:'ipscount',label:'询单人数', width:'100',sortable:'custom'},
  {istrue:true,prop:'nextdaysuccesspaycount',label:'询单>次日付款人数', width:'160',sortable:'custom'},
  {istrue:true,prop:'nextdaysuccesspayrate',label:'询单>次日付款成功率', width:'160',sortable:'custom',formatter:(row)=>{return row.nextdaysuccesspayrate?(row.nextdaysuccesspayrate*100).toFixed(2)+"%":0}},
  {istrue:true,prop:'successpaycount',label:'询单>最终付款人数', width:'160',sortable:'custom'},
  {istrue:true,prop:'successpayrate',label:'询单>最终付款成功率', width:'160',sortable:'custom',formatter:(row)=>{return row.successpayrate?(row.successpayrate*100).toFixed(2)+"%":0}},
  {istrue:true,prop:'responseTime',label:'平均响应(秒)', width:'200',sortable:'custom'},
  {istrue:true,prop:'salesvol',label:'销售额', width:'200',sortable:'custom'},
  { istrue: true, prop: 'satisdegree', label: '满意度', width: '100', sortable: 'custom',formatter:(row) => row.satisdegree !== null  ?(100 * row.satisdegree).toFixed(2) +'%' : '0%' },
  { istrue: true, prop: 'evaluateCount', label: '服务满意度评价参与量', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'evaluateVerySatisCount', label: '服务满意度评价很满意', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'evaluateSatisCount', label: '服务满意度评价满意', width: '120', sortable: 'custom' },
  
];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      Filter: {
      },
      shopList:[],
      userList:[],
      groupList:[],
      inquirsstatisticslist: [],
      tableCols:tableCols,
      total: 0,
      summaryarry:{count_sum:10},
      pager:{OrderBy:"ipscount",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
    };
  },
  async mounted() {
    
  },
  created(){
    
     this.onSearch();
  },
  methods: {

            async canclick(row, column, cell){
            
        //  if(this.filter.userId>0)
        //  this.detailName=row.userName+"的各商品业绩明细，统计周期："+this.filter.startTime+"--"+this.filter.endTime;
        // if(this.filter.operateSpecialUserId>0)
        //  this.detailName=row.operateSpecialUserName+"的各商品业绩明细，统计周期："+this.filter.startTime+"--"+this.filter.endTime;
        //   window.financialReportDetailByOneUser={startTime:this.filter.startTime,endTime:this.filter.endTime,userId:this.filter.userId,operateSpecialUserId:this.filter.operateSpecialUserId,shopCode:row.shopCode}
        //  this.dialogVisible=true;
        //  if(this.$refs.financialReportDetailByOneUser)
        //  {
        //    this.$refs.financialReportDetailByOneUser.onSearch();
        //  } 
    }, 


   async deleteBatch(row){
      var that=this;
      this.$confirm("此操作将删除此批次个人效率统计数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async() => {
      await deleteInquirsStatisticsBatch({batchNumber:row.batchNumber})
      that.$message({message: '已删除', type: "success"});
      that.onRefresh()

        });

    },
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    onImportSyj(){
      this.dialogVisibleSyj = true
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      const res = importInquirsStatisticsAsync(form);
      this.$message({message: '上传成功,正在导入中...', type: "success"});
    },
    async uploadSuccess2(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
    },
    async onSubmitupload2() {
      this.$refs.upload2.submit()
    },
    onRefresh(){      
        this.onSearch()
            },
    onSearch(){       
       this.getinquirsstatisticsList();      
    },
    async getinquirsstatisticsList(){    
              this.Filter.startSdate=localStorage.getItem("startsdate") ? dayjs(localStorage.getItem("startsdate")).format("YYYY-MM-DD") : null
           this.Filter.endSdate=localStorage.getItem("endsdate") ? dayjs(localStorage.getItem("endsdate")).format("YYYY-MM-DD") : null
            this.Filter.sname=localStorage.getItem("sname") 
      const para = {...this.Filter};    
      const params = {      
        ...this.pager,
        ...para,
      };

      this.listLoading = true;
      const res = await getInquirsStatisticsByShopList(params);
      console.log(res)
      this.listLoading = false;
      console.log(res.data.list)
      //console.log(res.data.summary)

      this.total = res.data.total
      this.inquirsstatisticslist = res.data.list;
      //this.summaryarry=res.data.summary;
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
