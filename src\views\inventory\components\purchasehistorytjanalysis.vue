<template>
 <div style="height:100%;padding:10px;overflow: auto;">
      <div id="purchasehistorytjanalysis1" style="width: 100%;height: 389px; box-sizing:border-box; line-height: 360px;"/>
  </div>
</template>
<script>
import * as echarts from 'echarts';
import container from '@/components/my-container/nofooter'
import cesTable from "@/components/Table/table.vue";
import {queryPurchaseAnalysis} from '@/api/inventory/purchase'
export default {
  name: 'Roles',
  components: {container,cesTable},
  data() {
    return {
      that:this,
       filter: {
         timerange:null,
         startDate:null,
         endDate:null,
         brandId:null
       },
      period:0,
      pageLoading: false,
      listLoading:false
    }
  },
  mounted() {
  },
  beforeUpdate() {
  },
methods: {
   async onSearch() {
     await this.getanalysisdata()
    },
   async onfresh() {
     await this.getanalysisdata()
    },
   async getanalysisdata() {
      var parm={...this.filter};
      parm.period=this.period;
      const res = await queryPurchaseAnalysis(parm);
      if (!res?.code)  return;       
      var chartDom = document.getElementById('purchasehistorytjanalysis1');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      if (!res.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
       }
      var option = this.Getoptions(res.data);
      option && myChart.setOption(option);
    },
    Getoptions(element){
     var series=[]
     element.series.forEach(s=>{
       series.push({smooth: true, ...s})
     })
     var yAxis=[]
     element.yAxis.forEach(s=>{
       yAxis.push({type: 'value',offset:s.offset,position:s.position,name: s.name,axisLabel: {formatter: '{value}'+s.unit}})
     })
     var option = {
        title: {text: element.title},
        tooltip: {trigger: 'axis'},
        legend: {
           data: element.legend
         },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {feature: {
            magicType: {show: true, type: ['line', 'bar']},
            //restore: {show: true},
        }},
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis: yAxis,
        series:  series
    };
    return option;
   },
  }
}
</script>
<style>
.el-select-content { 
    width: calc(100% - 10px);
    margin: 0;
 }
</style>
