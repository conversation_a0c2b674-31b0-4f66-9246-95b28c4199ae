<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="ListInfo" @submit.native.prevent>
                <el-form-item label="日期:">
                    <el-date-picker style="width:220px" v-model="ListInfo.timerange" type="daterange"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始"
                        end-placeholder="结束" :clearable="false" :picker-options="pickerOptions"
                        @change="changeTime"></el-date-picker>
                </el-form-item>
                <el-form-item label="平台:">
                    <el-select v-model="ListInfo.platform" style="width:110px;" placeholder="请选择" :clearable="true"
                        :collapse-tags="true" filterable @change="onchangeplatform">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="店铺:">
                    <el-select style="width:180px;" v-model="ListInfo.shopCode" placeholder="请选择" :clearable="true"
                        :collapse-tags="true" filterable>
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode" />
                    </el-select>
                </el-form-item>
                <el-form-item label="运营组:">
                    <el-select v-model="ListInfo.groupId" style="width:120px;" placeholder="请选择" :clearable="true"
                        :collapse-tags="true" filterable>
                        <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item label="产品ID:">
                    <el-input v-model.trim="ListInfo.proCode" placeholder="产品ID" clearable
                        style="width:150px;margin-right: 10px;" />
                </el-form-item>
                <el-form-item label="上架天数:">
                    <el-select style="width:70px;" v-model="ListInfo.onDaysEnd" placeholder="上架天数" :clearable="true"
                        filterable @change="changeOnDays">
                        <el-option label="昨天" :value="1" />
                        <el-option label="近3天" :value="3" />
                        <el-option label="近7天" :value="7" />
                    </el-select>
                </el-form-item>
                <el-form-item label="显示/隐藏盈利">
                    <el-switch v-model="ListInfo.isHideProfitData">
                    </el-switch>
                </el-form-item>
                <el-form-item label="显示/隐藏下架">
                    <el-switch v-model="ListInfo.isHideDisOnline">
                    </el-switch>
                </el-form-item>
                <el-form-item label="按专员/按平台">
                    <el-switch v-model="ListInfo.groupByPlatform">
                    </el-switch>
                </el-form-item>
                <el-form-item label="是否展示周趋势">
                    <el-switch v-model="ListInfo.isShowWeekQs">
                    </el-switch>
                </el-form-item>
            </el-form>
        </template>
        <vxetablebase ref="alreadyTable" :id="'newStryleCodeReports_20240812184810'" :that='that' :isIndex='true'
            :showheaderoverflow="false" :tableHandles="tableHandles" @select='selectchange' :treeProp="{}"
            v-show="!groupByPlatform" :showsummary='true' :summaryarry="summaryarry" :border="true"
            @cellClick="cellClick" :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='list'
            :tableCols='tableCols' :isSelection="true" :isSelectColumn="true"
            style="width: 100%; height: 98%;  margin: 0" :loading="listLoading">
        </vxetablebase>
        <vxetablebase :id="'newStryleCodeReports_20241123143815'" :that='that' :isIndex='true' v-show="groupByPlatform"
            :showheaderoverflow="false" :tableHandles="tableHandles" @select='selectchange' :showsummary='true'
            :summaryarry="summaryarry" :border="true" @cellClick="cellClick" :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='list' :tableCols='tableCols1' :isSelection="true"
            :isSelectColumn="true" style="width: 100%; height: 98%;  margin: 0" :loading="listLoading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog :visible.sync="detailPro.visible" width="45%" v-dialogDrag height="700" append-to-body>
            <el-button type="primary" @click="getlistDetailPro('search')">查询</el-button>
            <el-button type="primary" @click="showNextPro('last')">上一个</el-button>
            <el-button type="primary" @click="showNextPro('next')">下一个</el-button>
            <div style="margin-bottom:10px;margin-top:5px;">
                <el-descriptions :column="3" size="mini" border>
                    <el-descriptions-item label="平台">{{ detailPro.selRow.platformName
                    }}</el-descriptions-item>
                    <el-descriptions-item label="店铺">{{ detailPro.selRow.shopName }}</el-descriptions-item>
                    <el-descriptions-item label="运营组">{{ detailPro.selRow.groupName }}</el-descriptions-item>
                    <el-descriptions-item label="产品ID">
                        <div v-html="myformatLinkProCode(detailPro.selRow.platform, detailPro.selRow.proCode)"
                            @click="handleClick($event, detailPro.selRow.proCode)">
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item label="销售数量">{{ detailPro.selRow.meSalesQty }}</el-descriptions-item>
                </el-descriptions>
            </div>

            <vxetablebase ref="tableDetailPro" :id="'newStryleCodeReports_20240810165402'" :that='that' :isIndex='true'
                :hasexpand='true' :toolbarshow="false" @sortchange='prosortchange' :tableData='detailPro.list'
                :tableCols='tableColsProGoods' :isSelection="false" :summaryarry="summaryarryDetailPro"
                :isNeedExpend="false" :isSelectColumn="false" style="height:360px;" :loading="detailPro.listLoading">

            </vxetablebase>

            <my-pagination ref="proPage" :total="detailPro.total" @page-change="proPagechange"
                @size-change="proSizechange" />
        </el-dialog>

        <el-dialog title="毛三趋势图" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag :modal="false">
            <productdrchart v-if="buscharDialog.visible"></productdrchart>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>

<script>
import dayjs from "dayjs";
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import vxetablebase from "@/components/VxeTable/yh_vxetableVirtualScroll.vue";
import { formatPlatform, formatLinkProCode, formatmoney, platformlist } from "@/utils/tools";
import dateRange from "@/components/date-range/index.vue";
import { getGroupKeyValue } from "@/api/operatemanage/base/product";
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import buschar from '@/components/Bus/buschar'
import productdrchart from '@/views/bookkeeper/reportday/productdrchart'
import { getUserDingCode } from '@/api/admin/user'
import request from '@/utils/request'
import {
    pageStyleCodeDtlRptListAsync,
    pageStyleCodeGoodsDtlRptByProCodeListAsync,
    exportStyleCodeDtlRptListAsync
} from '@/api/bookkeeper/styleCodeRptData'
import { SaveProductIdViewLog } from '@/api/operatemanage/PddChart'
const tableColsProGoods = [
    { istrue: true, prop: 'styleCode', label: '系列编码', width: 'auto', },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'goodsImage', label: '图片', width: 'auto', sortable: 'custom', type: 'images' },
    { istrue: true, prop: 'createdTime', label: '创建时间', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'costPrice', label: '成本', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'kczj', label: '库存资金', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'threeDayZZRate', label: '3天周转天数', width: 'auto', },
];
const tableCols = [
    { istrue: true, width: '30', type: "checkbox", fixed: 'left' },
    { istrue: true, prop: 'platform', label: '平台', width: '100', sortable: 'custom', formatter: (row) => row.platformName, treeNode: true, fixed: 'left', align: 'left' },
    { istrue: true, prop: 'shopCode', label: '店铺', width: '120', sortable: 'custom', formatter: (row) => row.shopName },
    { istrue: true, prop: 'specialUserAvatar', label: '专员头像', width: '40', type: 'ddAvatar', ddInfo: { type: 2, prop: 'specialUserId' } },
    { istrue: true, prop: 'specialUserId', label: '运营专员', width: '75', sortable: 'custom', type: 'ddTalk', ddInfo: { type: 2, prop: 'specialUserId', name: 'specialUserName' }, },
    { istrue: true, prop: 'proCode', label: '产品ID', width: '90', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
    { istrue: true, prop: 'brandId', label: '采购组', width: '50', sortable: 'custom', formatter: (row) => row.brandName },
    {
        istrue: true, prop: 'onDays', label: '上架天数', width: '30', sortable: 'custom', formatter: (row) => {
            if (row.proCode == null) {
                return ''
            } else {
                return row.onDays
            }
        }
    },
    {
        istrue: true, prop: 'onStatus', label: 'ID状态', width: '50', formatter: (row) => {
            if (row.proCode == null) {
                return ''
            } else {
                return row.onStatus
            }
        }
    },
    { istrue: true, type: 'echarts', prop: 'profit3IncreaseGoOnDays', chartProp: 'profit3IncreaseGoOnDaysChartData', sign: 'proCode', fix: true, label: '毛三趋势', width: '80', },
    {
        istrue: true, label: "查看", width: "60", type: 'button', btnList: [
            { display: (row) => { return row.platformName == '分销' || row.proCode == null }, label: "产品编码", handle: (that, row) => that.showDetailPro(row) }
        ]
    },
    { istrue: true, prop: 'profit3', label: '毛3', width: '55', sortable: 'custom', },
    { istrue: true, prop: 'profit33', label: '毛4', width: '55', sortable: 'custom' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'profit5', label: '毛5' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'profit6', label: '毛6' },
    { istrue: true, prop: 'profit3Rate', label: '毛3利润率', width: '80', formatter: (row) => row.profit3Rate !== null ? row.profit3Rate + '%' : '', tipmesg: '毛三利润/销售金额', },
    { istrue: true, prop: 'profit33Rate', label: '毛4利润率', width: '70', formatter: (row) => row.profit33Rate !== null ? row.profit33Rate + '%' : '' },
    { width: '80', align: 'center', prop: 'profit5Rate', label: '毛5利润率', formatter: (row) => row.profit5Rate !== null ? row.profit5Rate + '%' : '' },
    { width: '80', align: 'center', prop: 'profit6Rate', label: '毛6利润率', formatter: (row) => row.profit6Rate !== null ? row.profit6Rate + '%' : '', },
    { istrue: true, prop: 'yyProfit3After', label: '运营毛3(减退款)', width: '60', sortable: 'custom' },
    { istrue: true, prop: 'yyProfit4After', label: '运营毛4(减退款)', width: '60', sortable: 'custom' },
    { sortable: 'custom', width: '60', align: 'center', prop: 'yyProfit5After', label: '运营毛5(减退款)', },
    { sortable: 'custom', width: '60', align: 'center', prop: 'yyProfit6After', label: '运营毛6(减退款)', },
    { istrue: true, prop: 'yyProfit3AfterRate', label: '运营毛3(减退款)利润率', width: '80', formatter: (row) => row.yyProfit3AfterRate !== null ? row.yyProfit3AfterRate + '%' : '' },
    { istrue: true, prop: 'yyProfit4AfterRate', label: '运营毛4(减退款)利润率', width: '80', formatter: (row) => row.yyProfit4AfterRate !== null ? row.yyProfit4AfterRate + '%' : '' },
    { width: '80', align: 'center', prop: 'yyProfit5AfterRate', label: '运营毛5(减退款)率', formatter: (row) => row.yyProfit5AfterRate !== null ? row.yyProfit5AfterRate + '%' : '', tipmesg: '运营维度毛5(减退款)率' },
    { width: '80', align: 'center', prop: 'yyProfit6AfterRate', label: '运营毛6(减退款)率', formatter: (row) => row.yyProfit6AfterRate !== null ? row.yyProfit6AfterRate + '%' : '', tipmesg: '运营维度毛6(减退款)率' },
    { istrue: true, prop: 'wcOrderRate', prop: 'orderCount', label: '订单数', width: '45', sortable: 'custom', },
    { istrue: true, prop: 'wcOrderRate', label: '外仓率', width: '45', sortable: 'custom', formatter: (row) => row.wcOrderRate ? row.wcOrderRate + '%' : '' },
    { istrue: true, prop: 'meSalesQty', label: '销售数量', width: '60', sortable: 'custom' },
    { istrue: true, prop: 'saleAmt', label: '销售金额', width: '45', sortable: 'custom' },
    { istrue: true, prop: 'saleCost', label: '销售成本', width: '45', sortable: 'custom' },
    { istrue: true, prop: 'deductAmt', label: '违规扣款成本', width: '60', sortable: 'custom' },
    { istrue: true, prop: 'dkAmont', label: '平台扣点', width: '45', sortable: 'custom', tipmesg: '支付宝账单费用扣点，新上链接天猫-7.5%；C店2%；已有ID-上月月报百分比', },
    { istrue: true, prop: 'refundAmont', label: '总退款金额', width: '50', sortable: 'custom', tipmesg: '当日发生的总退款金额，包括历史订单', },
    { sortable: 'custom', width: '55', align: 'center', prop: 'refundAmontBefore', label: '发货前退款金额', },
    { istrue: true, prop: 'refundAmontBeforeRate', label: '发货前退款率', width: '60', sortable: 'custom', formatter: (row) => row.refundAmontBeforeRate !== null ? row.refundAmontBeforeRate + '%' : '' },
    { sortable: 'custom', width: '55', align: 'center', prop: 'refundAmontAfter', label: '发货后退款金额', },
    { istrue: true, prop: 'refundAmontAfterRate', label: '发货后退款率', width: '60', sortable: 'custom', formatter: (row) => row.refundAmontAfterRate !== null ? row.refundAmontAfterRate + '%' : '' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'onlyRefundAmont', label: '仅退款', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'onlyRefundAmontRate', label: '仅退款率', formatter: (row) => row.onlyRefundAmontRate !== null ? row.onlyRefundAmontRate + '%' : '' },
    { istrue: true, prop: 'alladv', label: '总广告费', width: '45', sortable: 'custom' },
    { istrue: true, prop: 'packageFee', label: '包装材料费', width: '40', sortable: 'custom' },
    { istrue: true, prop: 'packageFeeAvg', label: '包装材料费均价', width: '45', sortable: 'custom' },
    { istrue: true, prop: 'freightFeeTotal', label: '快递费', width: '45', sortable: 'custom' },
    { istrue: true, prop: 'freightFeeTotalAvg', label: '快递均价', width: '45', sortable: 'custom' },
    { istrue: true, prop: 'profit1', label: '毛1', width: '45', sortable: 'custom', tipmesg: '销售金额-销售成本', },
    { istrue: true, prop: 'profit2', label: '毛2', width: '45', sortable: 'custom', tipmesg: '销售金额-销售成本-平台扣点-延迟发货扣款-营销费用-淘宝客-首单礼金-特殊单(刷单、补单、大灰熊)-包装材料-快递费', },
    { istrue: true, prop: 'yyProfit3', label: '运营毛3', width: '45', sortable: 'custom' },
    { istrue: true, prop: 'yyProfit4', label: '运营毛4', width: '45', sortable: 'custom' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'yyProfit5', label: '运营毛5', },
    { sortable: 'custom', width: '70', align: 'center', prop: 'yyProfit6', label: '运营毛6', },
    { istrue: true, prop: 'profit1Rate', label: '毛1利润率', width: '60', formatter: (row) => row.profit1Rate !== null ? row.profit1Rate + '%' : '', tipmesg: '订单毛利/销售金额', },
    { istrue: true, prop: 'profit2Rate', label: '毛2利润率', width: '60', formatter: (row) => row.profit2Rate !== null ? row.profit2Rate + '%' : '', tipmesg: '毛二利润/销售金额', },
    { istrue: true, prop: 'yyProfit3Rate', label: '运营毛3利润率', width: '60', formatter: (row) => row.yyProfit3Rate !== null ? row.yyProfit3Rate + '%' : '' },
    { istrue: true, prop: 'yyProfit4Rate', label: '运营毛4利润率', width: '60', formatter: (row) => row.yyProfit4Rate !== null ? row.yyProfit4Rate + '%' : '' },
    { width: '100', align: 'center', prop: 'yyProfit5Rate', label: '运营毛5利润率', formatter: (row) => row.yyProfit5Rate !== null ? row.yyProfit5Rate + '%' : '', tipmesg: '运营维度毛5率' },
    { width: '100', align: 'center', prop: 'yyProfit6Rate', label: '运营毛6利润率', formatter: (row) => row.yyProfit6Rate !== null ? row.yyProfit6Rate + '%' : '', tipmesg: '运营维度毛6率' },
    { istrue: true, prop: 'groupUserAvatar', label: '头像', width: '40', type: 'ddAvatar', ddInfo: { type: 1, prop: 'groupId' } },
    { istrue: true, prop: 'groupId', label: '运营组', width: '75', sortable: 'custom', formatter: (row) => row.groupName, type: 'ddTalk', ddInfo: { type: 1, prop: 'groupId', name: 'groupName' }, },
    { istrue: true, starProp: 'profit3', type: 'star', label: '毛三利润走势', width: '115', tipmesg: '毛三利润近四周走势，红色代表正利润，绿色代表负利润，十万以上则单位为萬，X则代表暂未利润' },
    { istrue: true, starProp: 'profit33', type: 'star', label: '毛四利润走势', width: '115', tipmesg: '毛四利润近四周走势，红色代表正利润，绿色代表负利润，十万以上则单位为萬，X则代表暂未利润' },
    { istrue: true, prop: 'shareFee', label: '公摊费', width: '45', sortable: 'custom', tipmesg: '公摊费(%)*销售金额', },
    { istrue: true, prop: 'shareFeeRate', label: '公摊费率', width: '45', sortable: 'custom', formatter: (row) => row.shareFeeRate !== null ? row.shareFeeRate + '%' : '', tipmesg: '明细2/销售金额', },
]

const tableCols1 = [
    { istrue: true, prop: 'platform', label: '平台', width: '100', sortable: 'custom', formatter: (row) => row.platformName, treeNode: true, fixed: 'left', align: 'left' },
    { istrue: true, prop: 'profit3', label: '毛3', width: '55', sortable: 'custom', },
    { istrue: true, prop: 'profit33', label: '毛4', width: '55', sortable: 'custom' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'profit5', label: '毛5' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'profit6', label: '毛6' },
    { istrue: true, prop: 'profit3Rate', label: '毛3利润率', width: '80', formatter: (row) => row.profit3Rate !== null ? row.profit3Rate + '%' : '', tipmesg: '毛三利润/销售金额', },
    { istrue: true, prop: 'profit33Rate', label: '毛4利润率', width: '70', formatter: (row) => row.profit33Rate !== null ? row.profit33Rate + '%' : '' },
    { width: '80', align: 'center', prop: 'profit5Rate', label: '毛5利润率', formatter: (row) => row.profit5Rate !== null ? row.profit5Rate + '%' : '' },
    { width: '80', align: 'center', prop: 'profit6Rate', label: '毛6利润率', formatter: (row) => row.profit6Rate !== null ? row.profit6Rate + '%' : '', },
    { istrue: true, prop: 'yyProfit3After', label: '运营毛3(减退款)', width: '60', sortable: 'custom' },
    { istrue: true, prop: 'yyProfit4After', label: '运营毛4(减退款)', width: '60', sortable: 'custom' },
    { sortable: 'custom', width: '60', align: 'center', prop: 'yyProfit5After', label: '运营毛5(减退款)', },
    { sortable: 'custom', width: '60', align: 'center', prop: 'yyProfit6After', label: '运营毛6(减退款)', },
    { istrue: true, prop: 'yyProfit3AfterRate', label: '运营毛3(减退款)利润率', width: '80', formatter: (row) => row.yyProfit3AfterRate !== null ? row.yyProfit3AfterRate + '%' : '' },
    { istrue: true, prop: 'yyProfit4AfterRate', label: '运营毛4(减退款)利润率', width: '80', formatter: (row) => row.yyProfit4AfterRate !== null ? row.yyProfit4AfterRate + '%' : '' },
    { width: '80', align: 'center', prop: 'yyProfit5AfterRate ', label: '运营毛5(减退款)率', formatter: (row) => row.yyProfit5AfterRate !== null ? row.yyProfit5AfterRate + '%' : '', tipmesg: '运营维度毛5(减退款)率' },
    { width: '80', align: 'center', prop: 'yyProfit6AfterRate', label: '运营毛6(减退款)率', formatter: (row) => row.yyProfit6AfterRate !== null ? row.yyProfit6AfterRate + '%' : '', tipmesg: '运营维度毛6(减退款)率' },
    { istrue: true, prop: 'wcOrderRate', prop: 'orderCount', label: '订单数', width: '45', sortable: 'custom', },
    { istrue: true, prop: 'wcOrderRate', label: '外仓率', width: '45', sortable: 'custom', formatter: (row) => row.wcOrderRate ? row.wcOrderRate + '%' : '' },
    { istrue: true, prop: 'meSalesQty', label: '销售数量', width: '60', sortable: 'custom' },
    { istrue: true, prop: 'saleAmt', label: '销售金额', width: '45', sortable: 'custom' },
    { istrue: true, prop: 'saleCost', label: '销售成本', width: '45', sortable: 'custom' },
    { istrue: true, prop: 'deductAmt', label: '违规扣款成本', width: '60', sortable: 'custom' },
    { istrue: true, prop: 'dkAmont', label: '平台扣点', width: '45', sortable: 'custom', tipmesg: '支付宝账单费用扣点，新上链接天猫-7.5%；C店2%；已有ID-上月月报百分比', },
    { istrue: true, prop: 'refundAmont', label: '总退款金额', width: '50', sortable: 'custom', tipmesg: '当日发生的总退款金额，包括历史订单', },
    { sortable: 'custom', width: '55', align: 'center', prop: 'refundAmontBefore', label: '发货前退款金额', },
    { istrue: true, prop: 'refundAmontBeforeRate', label: '发货前退款率', width: '60', sortable: 'custom', formatter: (row) => row.refundAmontBeforeRate !== null ? row.refundAmontBeforeRate + '%' : '' },
    { sortable: 'custom', width: '55', align: 'center', prop: 'refundAmontAfter', label: '发货后退款金额', },
    { istrue: true, prop: 'refundAmontAfterRate', label: '发货后退款率', width: '60', sortable: 'custom', formatter: (row) => row.refundAmontAfterRate !== null ? row.refundAmontAfterRate + '%' : '' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'onlyRefundAmont', label: '仅退款', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'onlyRefundAmontRate', label: '仅退款率', formatter: (row) => row.onlyRefundAmontRate !== null ? row.onlyRefundAmontRate + '%' : '' },
    { istrue: true, prop: 'alladv', label: '总广告费', width: '45', sortable: 'custom' },
    { istrue: true, prop: 'packageFee', label: '包装材料费', width: '40', sortable: 'custom' },
    { istrue: true, prop: 'packageFeeAvg', label: '包装材料费均价', width: '45', sortable: 'custom' },
    { istrue: true, prop: 'freightFeeTotal', label: '快递费', width: '45', sortable: 'custom' },
    { istrue: true, prop: 'freightFeeTotalAvg', label: '快递均价', width: '45', sortable: 'custom' },
    { istrue: true, prop: 'profit1', label: '毛1', width: '45', sortable: 'custom', tipmesg: '销售金额-销售成本', },
    { istrue: true, prop: 'profit2', label: '毛2', width: '45', sortable: 'custom', tipmesg: '销售金额-销售成本-平台扣点-延迟发货扣款-营销费用-淘宝客-首单礼金-特殊单(刷单、补单、大灰熊)-包装材料-快递费', },
    { istrue: true, prop: 'yyProfit3', label: '运营毛3', width: '45', sortable: 'custom' },
    { istrue: true, prop: 'yyProfit4', label: '运营毛4', width: '45', sortable: 'custom' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'yyProfit5', label: '运营毛5', },
    { sortable: 'custom', width: '70', align: 'center', prop: 'yyProfit6', label: '运营毛6', },
    { istrue: true, prop: 'profit1Rate', label: '毛1利润率', width: '60', formatter: (row) => row.profit1Rate !== null ? row.profit1Rate + '%' : '', tipmesg: '订单毛利/销售金额', },
    { istrue: true, prop: 'profit2Rate', label: '毛2利润率', width: '60', formatter: (row) => row.profit2Rate !== null ? row.profit2Rate + '%' : '', tipmesg: '毛二利润/销售金额', },
    { istrue: true, prop: 'yyProfit3Rate', label: '运营毛3利润率', width: '60', formatter: (row) => row.yyProfit3Rate !== null ? row.yyProfit3Rate + '%' : '' },
    { istrue: true, prop: 'yyProfit4Rate', label: '运营毛4利润率', width: '60', formatter: (row) => row.yyProfit4Rate !== null ? row.yyProfit4Rate + '%' : '' },
    { width: '100', align: 'center', prop: 'yyProfit5Rate', label: '运营毛5利润率', formatter: (row) => row.yyProfit5Rate !== null ? row.yyProfit5Rate + '%' : '', tipmesg: '运营维度毛5率' },
    { width: '100', align: 'center', prop: 'yyProfit6Rate', label: '运营毛6利润率', formatter: (row) => row.yyProfit6Rate !== null ? row.yyProfit6Rate + '%' : '', tipmesg: '运营维度毛6率' },
    { istrue: true, prop: 'shareFee', label: '公摊费', width: '45', sortable: 'custom', tipmesg: '公摊费(%)*销售金额', },
    { istrue: true, prop: 'shareFeeRate', label: '公摊费率', width: '45', sortable: 'custom', formatter: (row) => row.shareFeeRate !== null ? row.shareFeeRate + '%' : '', tipmesg: '明细2/销售金额', },
]
const tableHandles = [
    { label: "导出", handle: (that) => that.exportProps() },
    { label: "批量上下架", handle: (that) => that.bulkLoadingUnloading(), isDisabled: (that) => that.groupByPlatform },
    { label: "上一个", handle: (that) => that.showNextDtl('last') },
    { label: "下一个", handle: (that) => that.showNextDtl('next') },
    { label: "一键展开", handle: (that) => that.expendAllprop(true), isDisabled: (that) => that.groupByPlatform },
    { label: "一键收缩", handle: (that) => that.expendAllprop(false), isDisabled: (that) => that.groupByPlatform }
];
export default {
    name: 'Roles',
    components: { MyContainer, MyConfirmButton, dateRange, vxetablebase, buschar, productdrchart },
    props: {
        filter: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            buscharDialog: { visible: false, title: "", data: {} },
            tableHandles,
            tableColsProGoods,
            platformlist: platformlist.filter(a => a.value != '0'),
            that: this,
            shopList: [],
            list: [],
            summaryarry: {},
            tableCols: tableCols,
            tableCols1,
            total: 0,
            sels: [],
            ddLogo: require('@/static/images/dingding.png'),
            ListInfo: {
                // currentPage: 1,
                // pageSize: 50,
                // orderBy: 'profit33',
                // isAsc: true,
                timerange: [],
                isHideProfitData: true,
                isHideDisOnline: true,
                onDaysStart: 1,
                onDaysEnd: null,
                isShowWeekQs: false,
                groupByPlatform: false,
                // startDate: dayjs().subtract(30, 'day').format("YYYY-MM-DD"),
                // endDate: dayjs().format("YYYY-MM-DD"),
            },
            groupByPlatform: false,
            listLoading: false,
            pageLoading: false,
            detailPro: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                total: 0,
                selRow: {},
                visible: false,
                detailParentId: null,
                listLoading: false,
            },
            isExport: false,
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            groupList: [],
            summaryarryDetailPro: {},
            selProps: [],
            nextProcode: ''
        }
    },
    async mounted() {
        console.log(this.filter, 'this.filter');

        this.ListInfo = { ...this.ListInfo, ...this.filter }
        this.ListInfo.timerange = [this.filter.startDate, this.filter.endDate]
        this.init()
        this.onchangeplatform(this.filter.platform);
        await this.getList();
    },
    methods: {
        handleClick(e, prop) {
            if (!prop) return
            let res = JSON.parse(JSON.stringify(prop));
            if (res.length > 6) {
                res = res.substring(0, 2) + '**' + res.substring(res.length - 2, res.length);
            }
            if (e.target.innerHTML == '复') {
                var _this = this;
                this.$copyText(prop).then(function (e) {
                    _this.$message({ message: "内容已复制到剪切板！", type: "success" });
                }, function (e) {
                    _this.$message({ message: "抱歉，复制失败！", type: "warning" });
                })
                this.sendLog(prop, '复制宝贝ID', 'ERP')
            } else if (e.target.innerHTML == '查 ') {
                if (e.target.parentNode.innerHTML.includes(res)) {
                    e.target.parentNode.innerHTML = e.target.parentNode.innerHTML.replace(res, prop)
                }
                this.sendLog(prop, '查看宝贝ID', 'ERP')
            } else {
                if (res == e.target.innerHTML || prop == e.target.innerHTML) {
                    this.sendLog(prop, '打开链接', 'ERP')
                }
            }
        },
        async sendLog(proCode, action, source) {
            await SaveProductIdViewLog({ proCode, action, source })
        },
        expendAllprop(val) {
            if (val) {
                this.$refs.alreadyTable.expandAllTree(true)
            } else {
                this.$refs.alreadyTable.expandAllTree(false)
            }
        },
        changeOnDays(e) {
            this.ListInfo.onDaysEnd = e
        },
        async startSession(id, type) {
            const { data, success } = await getUserDingCode({ type, id })
            if (success) {
                if (!data) return this.$message.error('未获取到钉钉id')
                window.open(`dingtalk://dingtalkclient/action/sendmsg?spm=dingtalk_id=${data}`, '_self')
            }
        },
        showNextDtl(type) {
            this.$emit('showNext', type)
        },
        cellClick(prms) {
            if (prms.row.proCode == null) return
            if (prms?.column?.field && prms?.column?.field === 'profit3IncreaseGoOnDays') {
                let row = prms.row;
                this.showprchart2(row.proCode, row.platform);
            }
        },
        async showprchart2(prcode, platform) {
            window['lastseeprcodedrchart'] = prcode
            window['lastseeprcodedrchart1'] = platform
            window['lastseeprcodedrchart2'] = 1
            this.drparamProCode = prcode
            this.buscharDialog.visible = true
        },
        changeTime(e) {
            this.filter.startDate = e ? e[0] : null;
            this.filter.endDate = e ? e[1] : null;
            this.ListInfo.startDate = e ? e[0] : null;
            this.ListInfo.endDate = e ? e[1] : null;
        },
        bulkLoadingUnloading() {
            if (this.selProps.length == 0) {
                this.$message({ type: 'warning', message: '请选择产品' })
                return
            }
            this.$showDialogform({
                path: `@/views/base/batchListingDelist.vue`,
                title: '批量上下架',
                autoTitle: false,
                args: {
                    checkdata: this.selProps
                },
                height: '650px',
                width: '80%',
            })
        },
        selectchange(row) {
            this.selProps = row
        },
        rowStyle(row, callback) {
            let obj = {};
            if (this.list[row.rowIndex]['isMain']) {
                obj = {
                    color: 'red'
                }
            }
            callback(obj);
        },
        async init() {
            const { data: data3 } = await getGroupKeyValue({});
            this.groupList = data3;
        },
        //设置店铺下拉
        async onchangeplatform(val) {
            if (!val) {
                this.shopList = []
                this.ListInfo.shopCode = ""
            } else {
                const { data } = await getshopList({ platform: val, CurrentPage: 1, PageSize: 1000 });
                this.shopList = data ? data.list : [];
            }
        },
        async getList(type) {
            this.listLoading = true
            this.groupByPlatform = this.ListInfo.groupByPlatform
            if (type == 'search') {
                let params = {
                    currentPage: this.ListInfo.currentPage,
                    pageSize: this.ListInfo.pageSize,
                    orderBy: this.ListInfo.orderBy,
                    isAsc: this.ListInfo.isAsc,
                    platform: this.ListInfo.platform,
                    shopCode: this.ListInfo.shopCode,
                    groupId: this.ListInfo.groupId,
                    proCode: this.ListInfo.proCode,
                    startDate: this.ListInfo.startDate,
                    endDate: this.ListInfo.endDate,
                    timerange: this.ListInfo.timerange
                }
                console.log(params, 'params');
                this.ListInfo = { ...this.ListInfo, ...this.filter, ...params }
                console.log(this.ListInfo, 'this.ListInfo');
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            try {
                const { data, success } = await pageStyleCodeDtlRptListAsync(this.ListInfo)
                if (success) {
                    this.$nextTick(() => {
                        this.list = data.list
                        this.total = data.total
                        this.summaryarry = data.summary
                        this.listLoading = false
                        if (this.groupByPlatform) {
                            this.$refs.alreadyTable.loadRowEcharts();
                            this.$forceUpdate()
                        }
                    })
                } else {
                    this.listLoading = false
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.listLoading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        //每页数量改变
        proSizechange(val) {
            this.detailPro.currentPage = 1;
            this.detailPro.pageSize = val;
            this.getlistDetailPro()
        },
        //当前页改变
        proPagechange(val) {
            this.detailPro.currentPage = val;
            this.getlistDetailPro()
        },
        prosortchange({ order, prop }) {
            if (prop) {
                this.detailPro.orderBy = prop
                this.detailPro.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getlistDetailPro()
            }
        },
        myformatLinkProCode(platform, proCode) {
            return formatLinkProCode(platform, proCode);
        },
        myformatPlatform(platform) {
            return formatPlatform(platform);
        },
        ///==产品明细 Start==========================================
        async showDetailPro(row) {
            console.log(row, 'row');
            this.detailPro.selRow = row;
            // this.detailPro.meSalesQty = row.meSalesQty
            this.detailPro.proCode = row.proCode;
            this.nextProcode = row.proCode
            this.detailPro.styleCode = this.filter.styleCode;
            this.detailPro.startDate = this.ListInfo.startDate;
            this.detailPro.endDate = this.ListInfo.endDate;
            this.detailPro.visible = true;
            setTimeout(() => {
                this.getlistDetailPro();
            }, 0);
        },
        async sortchangeDetailPro({ order, prop }) {
            if (prop) {
                this.detailPro.orderBy = prop
                this.detailPro.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getlistDetailPro()
            }
        },
        //分页查询
        async getlistDetailPro(type) {
            if (type == 'search') {
                this.detailPro.currentPage = 1
                this.$refs.proPage.setPage(1)
            }
            this.detailPro.listLoading = true;
            const { data, success } = await pageStyleCodeGoodsDtlRptByProCodeListAsync(this.detailPro);
            this.detailPro.listLoading = false;
            if (!success) return

            this.detailPro.list = data.list;
            this.detailPro.total = data.total;
            this.detailPro.summaryarryDetailPro = data.summary;
        },
        showNextPro(type) {
            this.detailPro.listLoading = true;
            let res = []
            this.list.forEach(item => {
                if (item.children && item.children.length > 0) {
                    item.children.forEach(a => {
                        res.push(a)
                    })
                }
            })
            let index = res.findIndex(a => a.proCode == this.nextProcode);
            if (type == 'last') {
                index--
                if (index < 0) {
                    index = res.length - 1
                    this.$message({ type: 'warning', message: '当前已经是第一条数据,已跳转到最后一条' })

                }
            } else {
                index++
                if (index >= res.length) {
                    index = 0
                    this.$message({ type: 'warning', message: '当前是最后一条数据,已跳转到第一条' })
                }
            }
            this.showDetailPro(res[index])
        },
        // showNextPro(type) {
        //     this.detailPro.listLoading = true;
        //     console.log(type, 'type');
        //     let index = this.list.findIndex(a => a.proCode == this.nextProcode);
        //     if (type == 'last') {
        //         console.log(index, 'index');
        //         index--
        //         if (index < 0) {
        //             index = this.list.length - 1
        //         }
        //     } else {
        //         index++
        //         if (index >= this.list.length) {
        //             index = 0
        //         }
        //     }
        //     this.showDetailPro(this.list[index])
        // },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportStyleCodeDtlRptListAsync(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '系列相似产品数据_' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        //表头样式
        headerCellStyle(data) {
            if (data && data.column) {
                var isDayReportCol = dayReportCols.find(a => a.prop == data.column.property);
                if (isDayReportCol) {
                    return { color: '#F56C6C' }
                }
            }
            return null;
        },
    },
}
</script>
<style lang="scss" scoped>
::v-deep .el-link.el-link--primary {
    margin-right: 7px;
}

::v-deep .el-table__fixed {
    pointer-events: auto;
}
</style>
