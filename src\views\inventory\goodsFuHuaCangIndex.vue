<template>
    <my-container v-loading="pageLoading" >
      <el-tabs v-model="activeName" style="height: 95%">
        <el-tab-pane label="当前" name="first1" style="height: 100%">
          <goodsFuHuaCang ref="goodsFuHuaCang" ></goodsFuHuaCang>
        </el-tab-pane>
        <el-tab-pane label="历史" name="first2" style="height: 100%">
          <goodsFuHuaCangHistory ref="goodsFuHuaCangHistory" ></goodsFuHuaCangHistory>
        </el-tab-pane>
      </el-tabs>
    </my-container>
  </template>
  
  <script>
  import MyContainer from "@/components/my-container";
  import goodsFuHuaCang from "./goodsFuHuaCang.vue";
  import goodsFuHuaCangHistory from "./goodsFuHuaCangHistory.vue";
  
  export default {
    name: "goodsFuHuaCangIndex",
    components: {
      MyContainer, goodsFuHuaCang, goodsFuHuaCangHistory
    },
    data() {
      return {
        that: this,
        pageLoading: false,
        activeName: "first1",
      };
    },
    async mounted() {
  
    },
    methods: {
  
    },
  };
  </script>
  
  <style lang="scss" scoped></style>
  