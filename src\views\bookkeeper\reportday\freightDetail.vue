<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button  style="padding:0;margin:0;">
                    <el-date-picker v-model="filter.timeRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width:230px;">
                    </el-date-picker>
                </el-button>                   
                <el-button  style="padding:0;margin:0;">
                    <el-input v-model.trim="filter.proCode" placeholder="商品ID" :clearable="true" ></el-input>
                </el-button>
                <el-button  style="padding:0;margin:0;">
                    <el-input v-model.trim="myfilter.orderNoInner" placeholder="内部订单号" :clearable="true" ></el-input>
                </el-button>
                <el-button  style="padding:0;margin:0;">
                    <el-input v-model.trim="myfilter.orderNo" placeholder="原始订单号" :clearable="true" ></el-input>
                </el-button>
                <el-button  style="padding:0;margin:0;">
                    <el-input v-model.trim="myfilter.expressNo" placeholder="快递单号" :clearable="true" ></el-input>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExport">导出</el-button>
            </el-button-group>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="false"  
          :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='tableData' 
          :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:98%;margin: 0">
        </ces-table>
        <!--分页-->
        <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
        </template>
    </my-container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import MyContainer from '@/components/my-container';
import cesTable from '@/components/Table/table.vue';
import {formatLinkProCode} from '@/utils/tools'
import {
    getOrderDetail, exportOrderDetail
} from '@/api/order/ordergoods';

const tableCols =[
    {istrue:true,prop:'sourceType',label:'类型',sortable:'custom', width:'60'},
    {istrue:true,prop:'payTime',label:'付款日期',sortable:'custom', width:'90'},
    {istrue:true,prop:'proCode',label:'商品ID',sortable:'custom', width:'120',type:'html',formatter:row=>formatLinkProCode(row.platform,row.proCode)},
    {istrue:true,prop:'goodsCode',label:'商品编码', width:'110',sortable:'custom'},
    {istrue:true,prop:'expressNo',label:'快递单号', width:'140',sortable:'custom'},
    {istrue:true,prop:'orderNo',label:'原始订单号', width:'185',sortable:'custom'},
    {istrue:true,prop:'goodFreightMoney',label:'快递费', width:'70',sortable:'custom'},
    {istrue:true,prop:'faceSheetFee',label:'面单费', width:'70',sortable:'custom'},
    {istrue:true,prop:'overWeightFee',label:'续重费', width:'70',sortable:'custom'},
    {istrue:true,prop:'qty',label:'销售数量', width:'80',sortable:'custom'},
    {istrue:true,prop:'expressWeight',label:'重量', width:'60',sortable:'custom'},
    {istrue:true,prop:'goodsCost',label:'成本价', width:'70',sortable:'custom'},  
    {istrue:true,prop:'amounted',label:'已付金额', width:'80',sortable:'custom'},
    {istrue:true,prop:'orderNoInner',label:'内部订单号', width:'110',sortable:'custom'},
    
    {istrue:true,prop:'goodsName',label:'商品名称', width:'auto'}
];

const tableHandles=[
       // {label:"导出", handle:(that)=>that.onExport()},
      ];

const startDate = formatTime(dayjs().subtract(1,'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
    name:"Users",
    components:{MyContainer,cesTable},
    data(){
        return {
            that:this, 
            myfilter:{
                //proCode:null,
                // timeRange:[startDate,endDate],
                // startDate:null,
                // endDate:null,
                orderNo:null,
                orderNoInner:null,
                expressNo:null
            },                      
            tableCols:tableCols,
            tableHandles:tableHandles,
            tableData:[],
            total: 0,
            pager:{OrderBy:" PayTime ",IsAsc:false},
            listLoading: false,
            pageLoading: false,
            summaryarry:{},    
            sels:[],

        };
    },
    props:{
        filter:{
            proCode:null,
            timeRange:[startDate,endDate],
            startDate:null,
            endDate:null,
            //orderNo:null,
            //orderNoInner:null
        },   
    },
    methods:{
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            await this.onSearch();
            },
            async onSearch(){
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async getList(){
            this.filter.startDate =null;
            this.filter.endDate =null;
            if (this.filter.timeRange && this.filter.timeRange.length>0) {
                this.filter.startDate = this.filter.timeRange[0];
                this.filter.endDate = this.filter.timeRange[1];
            }
            else{
                this.$message({type: 'warning',message: '请选择日期范围'});
                return;
            }
            if(!this.filter.proCode){
                this.$message({type: 'warning',message: '请输入商品ID'});
                return;
            }
            var that=this;
            this.listLoading=true;
            var pager = this.$refs.pager.getPager();
            const params = {...pager,...this.pager,...this.filter,...this.myfilter};
            console.log('最终参数',params)
            const res = await getOrderDetail(params).then(res=>{
                that.total = res.data?.total;
                that.tableData = res.data?.list;
                that.summaryarry=res.data?.summary;               
            });
            this.listLoading=false;
        },
        async onExport(){
            this.filter.startDate =null;
            this.filter.endDate =null;
            if (this.filter.timeRange && this.filter.timeRange.length>0) {
                this.filter.startDate = this.filter.timeRange[0];
                this.filter.endDate = this.filter.timeRange[1];
            }
            else{
                this.$message({type: 'warning',message: '请选择日期范围'});
                return;
            }
            if(!this.filter.proCode){
                this.$message({type: 'warning',message: '请输入商品ID'});
                return;
            }
            var pager = this.$refs.pager.getPager();
            const params = {...pager,...this.pager,...this.filter,...this.myfilter};
            var res= await exportOrderDetail(params);
            if(!res?.data) {
                this.$message({message:"没有数据",type:"warning"});
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download','订单明细数据' +  new Date().toLocaleString() + '_.xlsx' )
            aLink.click()
        },
    }
})
</script>
<style scoped>
    ::v-deep .el-table__fixed-footer-wrapper tbody td{
        color:blue;
    }
</style>

