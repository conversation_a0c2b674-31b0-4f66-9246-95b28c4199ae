<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="proCodeCallback" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable
                    class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
                <el-button type="primary" @click="importProps">导入</el-button>
                <el-button type="primary" @click="scaleSettings">比例设置</el-button>
                <el-button type="primary" @click="batchDel">批量删除</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%"
            @select="selectCheckBox" :showsummary="data.summary ? true : false" :summaryarry="data.summary"
            @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
            <div slot="title" class="header-title">
                <span class="title-text"><span>导入数据</span></span>
                <span class="title-close"><el-button @click="downLoadFile">下载模版</el-button></span>
            </div>
            <div>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                    :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                </el-upload>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="importVisible = false">关闭</el-button>
            </div>
        </el-dialog>

        <el-dialog title="比例设置" :visible.sync="scaleSettingsVisible" width="30%" v-dialogDrag>
            <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="120px"
                class="demo-ruleForm">
                <el-form-item label="诚信仓占比" prop="rate">
                    <el-input-number v-model="ruleForm.rate" :min="0" :max="100" placeholder="诚信:邮政(%)"
                        :controls="false" :precision="0" />
                </el-form-item>
                <el-form-item>
                    <el-button @click="scaleSettingsVisible = false">关闭</el-button>
                    <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/WmsAllot/Fittings/'
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan
    },
    data() {
        return {
            api,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false,
                summarys: [],
            },
            data: {},
            ruleForm: {
                rate: '',
            },
            rules: {
                rate: [{ required: true, message: '请输入诚信:邮政占比', trigger: 'blur' }],
            },
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            scaleSettingsVisible: false,
            fileList: [],
            importLoading: false,
            importVisible: false,
            uploadLoading: false,
            file: null,
            selectList: [],
            fileparm: {},

        }
    },
    async mounted() {
        await this.getCol();
        await this.getList()
    },
    methods: {
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        //上传文件
        onUploadRemove(file, fileList) {
            this.fileList = []
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            this.importVisible = false;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("file", item.file);
            var res = await request.post(`${this.api}Import`, form);
            if (res?.success)
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            this.uploadLoading = false
            this.importVisible = false;
            await this.getList()
        },
        downLoadFile() {
            window.open("../../../static/excel/仓库调拨配件占比导入模版.xlsx", "_self");
        },
        submitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    const res = this.selectList.map(item => {
                        return {
                            goodsCode: item.goodsCode,
                            goodsName: item.goodsName,
                            id: item.id,
                            rate: this.ruleForm.rate
                        }
                    })
                    const { success } = await request.post(`${this.api}ChangeRate`, res)
                    if (success) {
                        this.$message({
                            type: 'success',
                            message: '设置成功!'
                        });
                        this.scaleSettingsVisible = false
                        this.getList()
                        this.selectList = []
                    }
                } else {
                    return false;
                }
            });
        },
        selectCheckBox(val) {
            this.selectList = val
        },
        batchDel() {
            if (this.selectList.length == 0) return this.$message.error('请选择要删除的数据!')
            this.$confirm('此操作将删除这些数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await request.post(`${this.api}Delete`, this.selectList.map(item => {
                    return {
                        id: item.id,
                        goodsCode: item.goodsCode
                    }
                }))
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                    this.getList()
                    this.selectList = []
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        scaleSettings() {
            if (this.selectList.length == 0) return this.$message.error('请选择要设置的数据!')
            this.ruleForm.rate = undefined
            this.scaleSettingsVisible = true
            this.$nextTick(() => {
                this.$refs.ruleForm.resetFields();
            });
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        proCodeCallback(val) {
            this.ListInfo.goodsCodes = val
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                data.unshift({
                    label: '',
                    type: 'checkbox'
                })
                data.forEach(item => {
                    if (item.prop == 'rate') {
                        item.formatter = (row) => {
                            return row.rate !== null ? row.rate + '%' : ''
                        }
                    }
                })
                this.tableCols = data;
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.dialog_top {
    height: 150px;
    display: flex;
    align-items: center;
    padding-bottom: 20px;
}

.header-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px 0 0;

    .title-text {
        display: flex;
        align-items: center;

        .title-close {
            margin-left: 10px;
        }
    }
}

.dialog_bottom {
    display: flex;
    justify-content: center;
    padding: 20px 0;
}
</style>
