<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="日期">
                    <el-date-picker style="width: 249px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" :clearable="false"
                        end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <!-- <el-form-item label="发生时间">
                    <el-date-picker style="width: 249px" v-model="filter.timerange1" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" :clearable="true"
                        end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item> -->
                <el-form-item label="ID:">
                    <inputYunhan ref="productCode2" :inputt.sync="filter.ProCode" v-model="filter.ProCode"
                        class="publicCss" placeholder="ID/多条请按回车" :clearable="true" :clearabletext="true"
                        :maxRows="2000" :maxlength="600000" @callback="orderNoInnerBack" title="ID">
                    </inputYunhan>  </el-form-item>
               

               <el-form-item label="平台:">
                        <el-select filterable v-model="platFormNameTypeist" placeholder="请选择平台"  multiple collapse-tags  clearable style="width: 165px">
                             <el-option label="天猫" value="天猫" />
                             <el-option label="淘宝" value="淘宝" />
                             <el-option label="工厂" value="工厂" />
                             <el-option label="阿里巴巴" value="阿里巴巴" />
                             <el-option label="苏宁" value="苏宁" />
                             <el-option label="拼多多" value="拼多多" />
                             <el-option label="抖音" value="抖音" />
                             <el-option label="抖音3元3件" value="抖音3元3件" />
                             <el-option label="京东自营" value="京东自营" />
                             <el-option label="京东pop" value="京东pop" />
                             <el-option label="京喜" value="京喜" />
                             <el-option label="小红书" value="小红书" />
                             <el-option label="视频号" value="视频号" />
                             <el-option label="快手" value="快手" />
                        </el-select>
                </el-form-item>
               
                <el-form-item label="是否报备:">
                    <el-select filterable v-model="filter.IsBaoBei" placeholder="请选择是否报备"   clearable style="width: 165px">
                            <el-option label="是" value="1" />
                            <el-option label="否" value="2" />
                    </el-select>
                </el-form-item>


                <el-form-item label="下架状态:">
                    <el-select filterable v-model="filter.LossStatus" placeholder="请选择下架状态"   clearable style="width: 165px">
                            <el-option label="成功" value="1" />
                            <el-option label="失败" value="0" />
                    </el-select>
                </el-form-item>

                 <el-form-item label="上架状态:">
                    <el-select filterable v-model="filter.noBaoBeiOnLine" placeholder="请选择上架状态"   clearable style="width: 165px">
                            <el-option label="已报备再上架" value="1" />
                            <el-option label="未报备再上架" value="2" />
                            <el-option label="未重新上架" value="3" />
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport"  v-if="checkPermission('profit6Loss:export')" >导出</el-button>
                </el-form-item>
                     <el-form-item>
                    <el-button type="primary" @click="onBaoBei(1,0)"  v-if="checkPermission('profit6Loss:baobei')" >批量报备</el-button>
                </el-form-item>
            </el-form>
        </template>
           <vxetablebase :id="'brandStores20250211171530'" :tablekey="'brandStores20250217153110'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='list'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'" @select="selectchange" @checkbox-range-end="selectchange">
      <template slot="right">
        <vxe-column fixed="right" title="操作" width="80"  v-if="checkPermission('profit6Loss:baobei')" >
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="onBaoBei(2,row.id)">报备</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="报备" :visible.sync="dialogVisible" :close-on-click-modal="false" width="40%" v-dialogDrag>
            <el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                        <el-input v-model="baobeiRemark" placeholder="请输入报备原因" type="textarea"
              :autosize="{ minRows: 4, maxRows: 4 }" style="width: 100%;"></el-input>
          
                  </el-col>
               
            </el-row>
            <span slot="footer" class="dialog-footer">
               <el-button type="primary" @click="onBaoBeiSubmit">提交报备</el-button>

                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { platformlist} from '@/utils/tools'
import { formatTime, formatTime1 } from "@/utils";
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import {getProfit6TakeOffLoss,exportProfit6TakeOffLoss,profit6TakeOffLossBaoBei,batchProfit6TakeOffLossBaoBei} from '@/api/bookkeeper/reportdayV2'
import inputYunhan from "@/components/Comm/inputYunhan";
import YhShopSelector from "@/components/YhCom/YhShopSelector";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";

const tableCols = [
    { istrue: true, width: '60', type: "checkbox" },
    { istrue: true, prop: 'yearMonthDay', label: '日期', tipmesg: '', width: '90', sortable: 'custom',formatter: (row) => formatTime(row.yearMonthDay, "YYYY-MM-DD") },
    { istrue: true, prop: 'reportDayTotalCount', label: '日报数据天数', tipmesg: '从日报日期（含当天）往前推的天数，例如日期为2025-7-15，日报数据天数为2，则取日报的数据为2025-7-15和2025-7-14这两天', width: '160', sortable: 'custom', },
    { istrue: true, prop: 'platFormName', label: '平台', tipmesg: '', width: '90', sortable: 'custom', },
    { istrue: true, prop: 'proCode', label: 'ID', tipmesg: '', width: '180', sortable: 'custom', },
    { istrue: true, prop: 'profit6', label: '发生毛六',  sortable: 'custom', tipmesg: '', width: '100', formatter: (row) => !row.profit6 ? " " : row.profit6 },
    { istrue: true, prop: 'yyProfit6', label: '运营毛六',  sortable: 'custom', tipmesg: '', width: '100', formatter: (row) => !row.yyProfit6 ? " " : row.yyProfit6 },
    { istrue: true, prop: 'lossStatus', label: '下架状态', tipmesg: '', width: '100', sortable: 'custom', formatter: (row) => row.lossStatus==1 ? "已下架" : "下架失败" },
    { istrue: true, prop: 'lossRemark', label: '下架失败原因', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'baoBeiDate', label: '报备日期', tipmesg: '', width: '100', sortable: 'custom',formatter: (row) => row.baoBeiDate ? formatTime(row.baoBeiDate, "YYYY-MM-DD") : ""},
    { istrue: true, prop: 'baoBeiRemark', label: '报备原因', tipmesg: '', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'baoBeiUserName', label: '报备人', tipmesg: '', width: '90', sortable: 'custom', },
    { istrue: true, prop: 'onlineDays', label: '再次上架次数', tipmesg: '', width: '130', sortable: 'custom', },
    { istrue: true, prop: 'onlineTime', label: '最新出现日期', tipmesg: '', width: '130', sortable: 'custom',formatter: (row) => row.onlineTime ? formatTime(row.onlineTime, "YYYY-MM-DD") : "" },
    { istrue: true, prop: 'reportDayCount', label: '日报有效天数', tipmesg: '订单量大于0或者有广告费的日报数据', width: '130', sortable: 'custom', },
]

const tableHandles = [
];

const startTime = formatTime(dayjs().subtract(2, 'day'), "YYYY-MM-DD");
const endTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const curMonth = formatTime1(dayjs().startOf("month").subtract(1, "month"), "yyyyMM");

export default {
    name: 'YunHanAdminIndex',
    components: { container, cesTable,vxetablebase, MyConfirmButton,inputYunhan,YhShopSelector },

    data() {
        return {
            that: this,
            baobeiRemark:'',
            importfilter: {
             YearMonthDay:null
            },
            filter: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
                platFormName:null,
                timerange1:[],
                IsBaoBei:null,
                LossStatus:null,
                noBaoBeiOnLine:null,
                ProCode:null,
                startOccTime: null,
                endOccTime: null,
            },
            platformlist:platformlist,
            importDialog: {
                filter: {
                    settMonth: curMonth,
                    platform: null
                }
            },
            list: [],
            platFormNameTypeist:[],
            summaryarry: {},
            pager: { OrderBy: "", IsAsc: false },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            sels: [],
            uploadLoading: false,
            dialogVisible: false,
            listLoading: false,
            fileList: [],
            id:0,
            selectList:[],
            baoBeiType:0,
        };
    },

    async mounted() {
        await this.onSearch()
        //await this.onchangeplatform()
    },

    methods: {
        onBaoBei(type,id){
            this.baoBeiType=type;
            this.dialogVisible = true;
            this.id = id;
        },
       async onBaoBeiSubmit(){
        if(!this.baobeiRemark){
            this.$message({ message: "请填写报备原因", type: "warning" });
            return;
        }
        //批量报备
        if(this.baoBeiType==1){
            var bblist = [];
            this.selids.forEach(element => {
                   var parm ={};
        parm.id=element;
        parm.baoBeiRemark=this.baobeiRemark;
        bblist.push(parm)
            });
     
         try{
       var res =  await batchProfit6TakeOffLossBaoBei(bblist);
           
            this.dialogVisible = false;
            this.baobeiRemark=''
              if(res.success) {
                this.$message({ message: "提交成功", type: "success" });
                await this.onSearch();
             }
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
        }else{
 var parm ={};
        parm.id=this.id;
        parm.baoBeiRemark=this.baobeiRemark;
         try{
           var res =    await profit6TakeOffLossBaoBei(parm);
             this.baobeiRemark=''
            this.dialogVisible = false;
             if(res.success) {
            this.$message({ message: "提交成功", type: "success" });
            await this.onSearch();
             }
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
        }
       
       },
  
    async onExport() {
     if (this.onExporting) return;
     try{
        this.filter.startTime = null;
        this.filter.endTime = null;
        this.filter.startOccTime = null;
        this.filter.endOccTime = null;
        if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            // if (this.filter.timerange1) {
            //     this.filter.startOccTime = this.filter.timerange1[0];
            //     this.filter.endOccTime = this.filter.timerange1[1];
            // }
            this.filter.platFormName = this.platFormNameTypeist.join(',');
        this.uploadLoading = true;
        const params = {...this.pager,...this.filter}
        var res= await exportProfit6TakeOffLoss(params);
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','毛六亏损下架ID_' + new Date().toLocaleString() + '.xlsx' )
        this.uploadLoading = false;

        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },

        //获取店铺
        // async onchangeplatform() {
        //     this.categorylist = []
        //     const res1 = await getshopList({ platform: this.filter.platform, CurrentPage: 1, PageSize: 100000 });
        //     this.filter.shopCode = null
        //     this.shopList = res1.data.list
        // },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
        this.filter.endTime = null;
        this.filter.startOccTime = null;
        this.filter.endOccTime = null;
        if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            // if (this.filter.timerange1) {
            //     this.filter.startOccTime = this.filter.timerange1[0];
            //     this.filter.endOccTime = this.filter.timerange1[1];
            // }
            this.filter.platFormName = this.platFormNameTypeist.join(',');
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getProfit6TakeOffLoss(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            this.list = data
        },
        async nSearch() {
            await this.getlist()
        },
        orderNoInnerBack(val) {
            this.filter.ProCode = val;
        },

        //字体颜色
        // renderRefundStatus(row) {
        //     if (row.refundStatus == '成功退款' || row.refundStatus == '等待退款') {
        //         return "color:red;cursor:pointer;";
        //     } else return "";
        // },
        //开始导入
        // startImport() {
        //    // this.importDialog.filter.platform = null
        //     this.fileList = []
        //     this.uploadLoading=false
        //     this.dialogVisible = true;
        // },
        // //取消导入
        // cancelImport() {
        //     this.dialogVisible = false;
        // },
        // uploadSuccess(response, file, fileList) {
        //     if (response.code == 200) {
        //     } else {
        //         fileList.splice(fileList.indexOf(file), 1);
        //     }
        // },
        // async submitUpload() {
        //     if (!this.importfilter.YearMonthDay || this.importfilter.YearMonthDay == null) {
        // this.$message({ message: "请先选择日期", type: "warning" });
        // return false;
        //    }
        //     if (!this.fileList || this.fileList.length == 0) {
        //         this.$message({ message: "请选取文件", type: "warning" });
        //         return false;
        //     }
        //     this.fileHasSubmit = true;
        //     this.uploadLoading = true;
        //     this.$refs.upload.submit();
        // },
        // clearFiles(){
        //     this.$refs['upload'].clearFiles();
        // },
        // async uploadFile(item) {
        //     if (!this.fileHasSubmit) {
        //         return false;
        //     }
        //     this.fileHasSubmit = false;
        //     this.uploadLoading = true;
        //     const form = new FormData();
        //     //form.append("platForm", this.importDialog.filter.platform);
        //     form.append("token", this.token);
        //     form.append("upfile", item.file);
        //     form.append("YearMonthDay", this.importfilter.YearMonthDay);
        //     let res = await importBillFee(form);
        //         if (res.code == 1) {
        //             this.$message({ message: "上传成功,正在导入中...", type: "success" });
        //             this.$refs.upload.clearFiles();
        //             this.dialogVisible = false;
        //         }
        //     this.fileList = []
        //     this.uploadLoading = false;
        // },
        // async uploadChange(file, fileList) {
        //     let files=[];
        //     files.push(file)
        //     this.fileList = files;
        // },
        // async uploadRemove(file, fileList) {
        //     this.fileList = []
        // },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event) {

        },
    }
};
</script>

<style lang="scss" scoped>
::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
