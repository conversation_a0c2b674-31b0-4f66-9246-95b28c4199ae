<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-button style="padding: 0;margin: 0;width: 250px; border: none;">
          <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="导入开始日期" end-placeholder="导入结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime" :clearable="false">
        </el-date-picker>
        </el-button>
        <!-- <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
            <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="" />
        </el-button> -->
        <!-- <inputYunhan ref="goodsCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode" width="200px"
              placeholder="商品编码(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
              :maxlength="21000" @callback="productCodeCallback2" title="商品编码">
            </inputYunhan> -->
            <!-- <el-button style="padding: 0;margin: 0;width: 200px; border: none;">
            <el-select  ref="goodsCodeInput" v-model="ListInfo.goodsCodeList" multiple clearable collapse-tags  filterable placeholder="商品编码" @blur="productSelect($event,1)" allow-create >
                <el-option v-for="(item,index) in goodsCodeList" :key="index" :label="item" :value="item" />
            </el-select>
          </el-button> -->
          <el-button style="padding: 0;margin-left: 10px;width: 200px; border: none;">
                <!-- <el-input v-model.trim="ListInfo.originalOnlineOrderNo" placeholder="原始线上订单号" maxlength="50" clearable class="" /> -->
                <div  class="publicCss" >
          <inputYunhan ref="ListInfo.goodsCodeSel" :inputt.sync="ListInfo.goodsCodeSel" v-model="ListInfo.goodsCodeSel" width="200px"
              placeholder="商品编码(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
              :maxlength="21000" @callback="productCodeCallback" title="商品编码">
            </inputYunhan>
         </div>
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 500px; border: none;margin-left: 10px;">
          <queryCondition ref="refqueryCondition" :valueChanged.sync="topfilter" />
        </el-button>
          <!-- <el-button style="padding: 0;margin-left: 10px; border: none;">
            <el-select v-model="ListInfo.warehouseId" clearable filterable placeholder="请选择发货仓库" style="width: 130px">
                        <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-button> -->
                  <el-button style="padding: 0;border: none;margin-left: 10px;">
            <el-input-number v-model="ListInfo.minOrderCount" :min="0" :max="9999999" :precision="0" :controls="false" style="width: 130px;"
              placeholder=">=订单数最小值"></el-input-number>
          </el-button>
          <el-button style="padding: 0;border: none;margin-left: 10px;">
            <el-input-number v-model="ListInfo.maxOrderCount" :min="0" :max="99999999" :precision="0" :controls="false" style="width: 130px;"
              placeholder="≤订单数最大值"></el-input-number>
          </el-button>
          <el-button style="padding: 0;margin-left: 10px; border: none;">
            <el-select v-model="ListInfo.maxGoodsWeightFb" clearable filterable placeholder="请选择重量段" style="width: 130px">
                        <el-option label="0-0.5kg" value="2" />
                        <el-option label="0.5-1kg" value="3" />
                        <el-option label="1-2kg" value="4" />
                        <el-option label="2-3kg" value="5" />
                        <el-option label="3-4kg" value="6" />
                        <el-option label="4-5kg" value="7" />
                        <el-option label="5kg以上" value="8" />
                    </el-select>
                  </el-button>
                  <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'storeSummary2024101309159'" :tablekey="'storeSummary2024101130959'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'" @cellStyle="cellStyle"
      cellStyle >
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { warehouselist,pickerOptions , formatWarehouseNew,formatTime } from "@/utils/tools";
import { getExpressComanyAll , getExpressGoodsWeight,exportExpressGoodsWeight } from "@/api/express/express";
import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";
import queryCondition from "../../dailyCourierFee/components/queryCondition.vue";

const goodsCodeList = [
  'PPK',
  'TM-2021',
]
const tableCols = [
{  width: '110', sortable: 'custom',align: 'center', prop: 'goodsCode', label: '商品编码', },
  { width: '130',sortable: 'custom', align: 'center', prop: 'goodsWeight', label: '聚水潭重量（KG）', },
  { width: '130', sortable: 'custom',align: 'center', prop: 'billingWeight1', label: '快递总重量（KG）', },
  {  width: '120',sortable: 'custom', align: 'center', prop: 'billingWeightAvg', label: '快递均重（KG）', },
  {  width: '120', sortable: 'custom',align: 'center', prop: 'goodsWeightDiff', label: '差额（KG）', formatter: (row) => row.goodsWeightDiff == 0 ? " " : row.goodsWeightDiff > 0 ? '+'+row.goodsWeightDiff: row.goodsWeightDiff },

  {  width: '80',sortable: 'custom', align: 'center', prop: 'maxGoodsWeightFb', label: '重量段取值', },
  {  width: '80', sortable: 'custom',align: 'center', prop: 'orderCount1', label: '订单数', type: 'click',  handle: (that, row) => that.onExpressCompanyGoodsShow(row, 1,0) },
  {  width: '80',sortable: 'custom', align: 'center', prop: 'quantity1', label: '编码数', type: 'click',  handle: (that, row) => that.onExpressCompanyGoodsShow(row, 2,0) },
  {
    istrue: true,  prop: '', label: `0-0.5kg`, merge: true,  width: '120', border:true,
    cols: [
    { istrue: true,sortable: 'custom',  prop: 'orderCount2', label: '订单数',  width: '80', type: 'click',  handle: (that, row) => that.onExpressCompanyGoodsShow(row, 1,2), formatter: (row) => row.orderCount2 == 0 ? " " : row.orderCount2 },
    { istrue: true, sortable: 'custom', prop: 'orderCount2Rate', label: '占比',  width: '80', formatter: (row) => row.orderCount2Rate == 0 ? " " : row.orderCount2Rate+'%' },
    { istrue: true, sortable: 'custom', prop: 'quantity2', label: '编码数',  width: '80', formatter: (row) => row.quantity2 == 0 ? " " : row.quantity2 },
      { istrue: true,sortable: 'custom',  prop: 'quantity2Rate', label: '占比',  width: '80', formatter: (row) => row.quantity2Rate == 0 ? " " : row.quantity2Rate+'%' },
      { istrue: true, sortable: 'custom', prop: 'orderAvgCount2', label: '均编码数', tipmesg: '向上取整（2.3则取3）',  width: '80', formatter: (row) => row.orderAvgCount2 == 0 ? " " : row.orderAvgCount2 },
      { istrue: true,sortable: 'custom',  prop: 'goodsWeight2', label: '聚水潭重量',  width: '80', formatter: (row) => row.goodsWeight2 == 0 ? " " : row.goodsWeight2 },
      { istrue: true,sortable: 'custom',  prop: 'billingWeightAvg2', label: '快递重量',  width: '80', formatter: (row) => row.billingWeightAvg2 == 0 ? " " : row.billingWeightAvg2 },
      ]
  },
  {
    istrue: true,  prop: '', label: `0.5-1kg`, merge: true,  width: '120',
    cols: [
    { istrue: true, sortable: 'custom', prop: 'orderCount3', label: '订单数',  width: '80', type: 'click',  handle: (that, row) => that.onExpressCompanyGoodsShow(row, 1,3), formatter: (row) => row.orderCount3 == 0 ? " " : row.orderCount3 },
    { istrue: true, sortable: 'custom', prop: 'orderCount3Rate', label: '占比',  width: '80', formatter: (row) => row.orderCount3Rate == 0 ? " " : row.orderCount3Rate+'%' },
     { istrue: true,sortable: 'custom',  prop: 'quantity3', label: '编码数',  width: '80', formatter: (row) => row.quantity3 == 0 ? " " : row.quantity3 },
      { istrue: true,sortable: 'custom',  prop: 'quantity3Rate', label: '占比',  width: '80', formatter: (row) => row.quantity3Rate == 0 ? " " : row.quantity3Rate +'%'},
      { istrue: true,sortable: 'custom',  prop: 'orderAvgCount3', label: '均编码数', tipmesg: '向上取整（2.3则取3）',  width: '80', formatter: (row) => row.orderAvgCount3 == 0 ? " " : row.orderAvgCount3 },
      { istrue: true,sortable: 'custom',  prop: 'goodsWeight3', label: '聚水潭重量',  width: '80', formatter: (row) => row.goodsWeight3 == 0 ? " " : row.goodsWeight3 },
      { istrue: true, sortable: 'custom', prop: 'billingWeightAvg3', label: '快递重量',  width: '80', formatter: (row) => row.billingWeightAvg3 == 0 ? " " : row.billingWeightAvg3 },
      ]
  },
  {
    istrue: true,  prop: '', label: `1-2kg`, merge: true,  width: '120',
    cols: [
    { istrue: true, sortable: 'custom', prop: 'orderCount4', label: '订单数',  width: '80', type: 'click',  handle: (that, row) => that.onExpressCompanyGoodsShow(row, 1,4), formatter: (row) => row.orderCount4 == 0 ? " " : row.orderCount4 },
    { istrue: true,sortable: 'custom',  prop: 'orderCount4Rate', label: '占比',  width: '80', formatter: (row) => row.orderCount4Rate == 0 ? " " : row.orderCount4Rate+'%' },
     { istrue: true,sortable: 'custom',  prop: 'quantity4', label: '编码数',  width: '80', formatter: (row) => row.quantity4 == 0 ? " " : row.quantity4 },
      { istrue: true, sortable: 'custom', prop: 'quantity4Rate', label: '占比',  width: '80', formatter: (row) => row.quantity4Rate == 0 ? " " : row.quantity4Rate+'%' },
      { istrue: true,sortable: 'custom',  prop: 'orderAvgCoun4', label: '均编码数', tipmesg: '向上取整（2.3则取3）',  width: '80', formatter: (row) => row.orderAvgCoun4 == 0 ? " " : row.orderAvgCoun4 },
      { istrue: true,sortable: 'custom',  prop: 'goodsWeight4', label: '聚水潭重量',  width: '80', formatter: (row) => row.goodsWeight4 == 0 ? " " : row.goodsWeight4 },
      { istrue: true, sortable: 'custom', prop: 'billingWeightAvg4', label: '快递重量',  width: '80', formatter: (row) => row.billingWeightAvg4 == 0 ? " " : row.billingWeightAvg4 },
       ]
  },
  {
    istrue: true,  prop: '', label: `2-3kg`, merge: true,  width: '120',
    cols: [
    { istrue: true, sortable: 'custom', prop: 'orderCount5', label: '订单数',  width: '80', type: 'click',  handle: (that, row) => that.onExpressCompanyGoodsShow(row, 1,5), formatter: (row) => row.orderCount5 == 0 ? " " : row.orderCount5 },
    { istrue: true,sortable: 'custom',  prop: 'orderCount5Rate', label: '占比',  width: '80', formatter: (row) => row.orderCount5Rate == 0 ? " " : row.orderCount5Rate+'%' },
      { istrue: true,sortable: 'custom',  prop: 'quantity5', label: '编码数',  width: '80', formatter: (row) => row.quantity5 == 0 ? " " : row.quantity5 },
      { istrue: true,sortable: 'custom',  prop: 'quantity5Rate', label: '占比',  width: '80', formatter: (row) => row.quantity5Rate == 0 ? " " : row.quantity5Rate+'%' },
      { istrue: true,sortable: 'custom',  prop: 'orderAvgCount5', label: '均编码数', tipmesg: '向上取整（2.3则取3）',  width: '80', formatter: (row) => row.orderAvgCount5 == 0 ? " " : row.orderAvgCount5 },
      { istrue: true,sortable: 'custom',  prop: 'goodsWeight5', label: '聚水潭重量',  width: '80', formatter: (row) => row.goodsWeight5 == 0 ? " " : row.goodsWeight5 },
      { istrue: true,sortable: 'custom',  prop: 'billingWeightAvg5', label: '快递重量',  width: '80', formatter: (row) => row.billingWeightAvg5 == 0 ? " " : row.billingWeightAvg5 },
      ]
  },
  {
    istrue: true,  prop: '', label: `3-4kg`, merge: true,  width: '120',
    cols: [
    { istrue: true, sortable: 'custom', prop: 'orderCount6', label: '订单数',  width: '80', type: 'click',  handle: (that, row) => that.onExpressCompanyGoodsShow(row, 1,6), formatter: (row) => row.orderCount6 == 0 ? " " : row.orderCount6 },
    { istrue: true,sortable: 'custom',  prop: 'orderCount6Rate', label: '占比',  width: '80', formatter: (row) => row.orderCount6Rate == 0 ? " " : row.orderCount6Rate+'%' },
     { istrue: true, sortable: 'custom', prop: 'quantity6', label: '编码数',  width: '80', formatter: (row) => row.quantity6 == 0 ? " " : row.quantity6 },
      { istrue: true,sortable: 'custom',  prop: 'quantity6Rate', label: '占比',  width: '80', formatter: (row) => row.quantity6Rate == 0 ? " " : row.quantity6Rate+'%' },
      { istrue: true,sortable: 'custom',  prop: 'orderAvgCount6', label: '均编码数', tipmesg: '向上取整（2.3则取3）',  width: '80', formatter: (row) => row.orderAvgCount6 == 0 ? " " : row.orderAvgCount6 },
      { istrue: true,sortable: 'custom',  prop: 'goodsWeight6', label: '聚水潭重量',  width: '80', formatter: (row) => row.goodsWeight6 == 0 ? " " : row.goodsWeight6 },
      { istrue: true, sortable: 'custom', prop: 'billingWeightAvg6', label: '快递重量',  width: '80', formatter: (row) => row.billingWeightAvg6 == 0 ? " " : row.billingWeightAvg6 },
      ]
  },
  {
    istrue: true,  prop: '', label: `4-5kg`, merge: true,  width: '120',
    cols: [
    { istrue: true,sortable: 'custom',  prop: 'orderCount7', label: '订单数',  width: '80', type: 'click',  handle: (that, row) => that.onExpressCompanyGoodsShow(row, 1,6), formatter: (row) => row.orderCount7 == 0 ? " " : row.orderCount7 },
    { istrue: true, sortable: 'custom', prop: 'orderCount7Rate', label: '占比',  width: '80', formatter: (row) => row.quantity7Rate == 0 ? " " : row.quantity7Rate+'%' },
     { istrue: true, sortable: 'custom', prop: 'quantity7', label: '编码数',  width: '80', formatter: (row) => row.quantity7 == 0 ? " " : row.quantity7 },
      { istrue: true,sortable: 'custom',  prop: 'quantity7Rate', label: '占比',  width: '80', formatter: (row) => row.quantity7Rate == 0 ? " " : row.quantity7Rate +'%'},
      { istrue: true,sortable: 'custom',  prop: 'orderAvgCount7', label: '均编码数', tipmesg: '向上取整（2.3则取3）',  width: '80', formatter: (row) => row.orderAvgCount7 == 0 ? " " : row.orderAvgCount7 },
      { istrue: true,sortable: 'custom',  prop: 'goodsWeight7', label: '聚水潭重量',  width: '80', formatter: (row) => row.quantity7 == 0 ? " " : row.quantity7},
      { istrue: true, sortable: 'custom', prop: 'billingWeightAvg7', label: '快递重量',  width: '80', formatter: (row) => row.quantity7 == 0 ? " " : row.quantity7 },
       ]
  },
  {
    istrue: true,  prop: '', label: `5kg以上`, merge: true,  width: '120',
    cols: [
    { istrue: true, sortable: 'custom', prop: 'orderCount8', label: '订单数',  width: '80', type: 'click',  handle: (that, row) => that.onExpressCompanyGoodsShow(row, 1,8), formatter: (row) => row.orderCount8 == 0 ? " " : row.orderCount8 },
    { istrue: true, sortable: 'custom', prop: 'orderCount8Rate', label: '占比',  width: '80', formatter: (row) => row.orderCount8Rate == 0 ? " " : row.orderCount8Rate+'%' },
     { istrue: true, sortable: 'custom', prop: 'quantity8', label: '编码数',  width: '80', formatter: (row) => row.quantity8 == 0 ? " " : row.quantity8 },
      { istrue: true,sortable: 'custom',  prop: 'quantity8Rate', label: '占比',  width: '80', formatter: (row) => row.quantity8Rate == 0 ? " " : row.quantity8Rate+'%' },
      { istrue: true,sortable: 'custom',  prop: 'orderAvgCount8', label: '均编码数', tipmesg: '向上取整（2.3则取3）',  width: '80', formatter: (row) => row.orderAvgCount8 == 0 ? " " : row.orderAvgCount8 },
      { istrue: true, sortable: 'custom', prop: 'goodsWeight8', label: '聚水潭重量',  width: '80', formatter: (row) => row.goodsWeight8 == 0 ? " " : row.goodsWeight8 },
      { istrue: true, sortable: 'custom', prop: 'billingWeightAvg8', label: '快递重量',  width: '80', formatter: (row) => row.billingWeightAvg8 == 0 ? " " : row.billingWeightAvg8 },
       ]
  },
]
export default {
  name: "storeSummary2",
  components: {
    MyContainer, vxetablebase,inputYunhan,queryCondition
  },
  data() {
    return {
      goodsCodeList,
      warehouselist,
      topfilter: {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
      },
      formatWarehouseNew:formatWarehouseNew,
      timeRanges: [],
      that: this,
      pickerOptions,
      ListInfo: {
        DataType:2,
        yearmonth:null,
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        noUseCatch: false,//非缓存
        shopName1:null,//店铺
        goodsCodeSel:null
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      goodsCode:''
    }
  },
  async mounted() {
    this.ListInfo.yearmonth= formatTime(new Date(),'YYYYMM');
    if (this.timeRanges && this.timeRanges.length == 0) {
      //默认给当前月第一天至今天
      this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
      this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
      this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
    }
    // await this.getList()
    await this.init()
  },

  methods: {
    async cellStyle(row, column, callback) {
      if (row.goodsWeightDiff != 0 && ( column.field == 'goodsWeightDiff' ||  column.field == 'billingWeightAvg')) {
        callback({  color: 'red' })
      }
    },
    productCodeCallback(val) {

this.ListInfo.goodsCodeSel = val;
},
    async init() {
      this.$nextTick(() => {
        this.$refs.refqueryCondition.init()
      })
      // const res = await getExpressComanyAll({});
      // if (!res?.success) return
      // this.expresscompanylist = res.data;
    },
    async onExpressCompanyGoodsShow(row, vala,weighttype1) {
      let request = {
        startDate: this.timeRanges[0],
        endDate: this.timeRanges[1],
        goodsCode: row.goodsCode,
        weighttype:weighttype1
      };
      if(vala==1){
        this.$showDialogform({
        path: `@/views/express/courierFee/components/expressCompanyGoodsOrder.vue`,
        title: '编码订单',
        args: { row, request, vala },
        height: '650px',
        width: '80%',
      })
      }else{
        this.$showDialogform({
        path: `@/views/express/courierFee/components/expressCompanyGoodsWeight.vue`,
        title: '快递公司编码重量',
        args: { row, request, vala },
        height: '650px',
        width: '90%',
      })

      }
     
    },
    productSelect(e,val) {
      this.$nextTick(() => {
      let value = e.target.value; // 输入框值
        if(value) { // 你输入才有这个值 不为空，如果你下拉框选择的话 这个值为空
          if(val == 1) {
            this.ListInfo.goodsCodeList = value
          } else if(val == 2) {
            this.ListInfo.labels = value
          } else if(val == 3) {
            this.ListInfo.status = value
          } else if(val == 4) {
            this.ListInfo.remark = value
          }
        }
      });
    },
    //导出
    async exportProps() {
      this.loading = true
      const res = await exportExpressGoodsWeight({...this.ListInfo,...this.topfilter})
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', 'ERP导出月账单数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getExpressGoodsWeight({...this.ListInfo,...this.topfilter})
        if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary

        let summary = data.summary || {}

const resultsum = {};
Object.entries(summary).forEach(([key, value]) => {
    resultsum[key] = formatNumber(value);
});
function formatNumber(number) {
    const options = {
        useGrouping: true,
    };
    return new Intl.NumberFormat('zh-CN', options).format(number);
}
this.summaryarry = resultsum
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
