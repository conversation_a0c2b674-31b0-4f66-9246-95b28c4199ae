<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-input v-model="ListInfo.goodsCodes" placeholder="商品编码" clearable maxlength="100" style="width: 220px;"
                    class="publicMargin" />
                <el-input v-model="ListInfo.initiateDDUserName" placeholder="发起人" clearable maxlength="100"
                    style="width: 220px;" class="publicMargin" />
                <el-select v-model="ListInfo.backReason" placeholder="退货事由" style="width: 220px;" class="publicMargin"
                    clearable>
                    <el-option v-for="item in backReasons" :key="item.label" :label="item.label" :value="item.label">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.approvalStatus" placeholder="请选择审核状态" style="width: 220px;"
                    class="publicMargin" clearable>
                    <el-option v-for="item in approvalStatus" :key="item.label" :label="item.label" :value="item.label">
                    </el-option>
                </el-select>
                <el-date-picker v-model="timeList" type="daterange" align="right" unlink-panels range-separator="至"
                    start-placeholder="审核开始时间" end-placeholder="审核结束时间" :picker-options="pickerOptions"
                    style="width: 240px;" class="publicMargin" @change="changeAuditTime" />
                <el-date-picker v-model="timeList1" type="daterange" align="right" unlink-panels range-separator="至"
                    start-placeholder="发起开始时间" end-placeholder="发起结束时间" :picker-options="pickerOptions"
                    style="width: 240px;" class="publicMargin" @change="changeLaunchTime" />
                <el-button type="primary" @click="getList">查询</el-button>
            </div>
        </template>
        <vxetablebase :id="'encodingProcessingBack202408041723'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            :loading='listLoading' style="width: 100%; height: 700px; margin: 0" />
        <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="total" @page-change="Pagechange"
            @size-change="Sizechange" />

        <el-dialog title="摘要信息" :visible.sync="dialogVisible" width="30%" :before-close="handleClose" v-dialogDrag
            class="dialog">
            <el-form ref="form" :model="formPorp" :label-position="left" label-width="70px">
                <el-form-item label="商品编码">
                    <el-input v-model="formPorp.goodsCodes" type="textarea" :rows="4" placeholder="商品编码" maxlength="1000"
                        style="width: 260px" class="publicMargin" />
                </el-form-item>
                <el-form-item label="图片" label-position="">
                    <div class="imgBox">
                        <el-image v-for="item in srcList" style="width: 100px; height: 100px" :src="item"
                            :preview-src-list="srcList" class="imgcss" />
                    </div>
                </el-form-item>
                <el-form-item label="备注" prop="">
                    <el-input v-model="formPorp.remark" type="textarea" :rows="4" placeholder="备注" maxlength="1000"
                        style="width: 260px" class="publicMargin" />
                </el-form-item>
            </el-form>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import inputYunhan from '@/components/Comm/inputYunhan.vue'
import dayjs from 'dayjs'
import { getGoodsBackDingDingPageList } from '@/api/operatemanage/goodsHandledDingDing'
const backReasons = [
    {
        label: '滞销品退货',
    },
    {
        label: '运营要求退回',
    },
    {
        label: '约定退货',
    },
    {
        label: '采购正常错单退回',
    },
]

const approvalStatus = [
    {
        label: '已拒绝',
    },
    {
        label: '已通过',
    },
    {
        label: '已申请',
    },
    {
        label: '已撤销',
    },
]
const tableCols = [
    { istrue: true, prop: 'approvalNumber', label: '审批编号', sortable: 'custom' },
    { istrue: true, prop: 'goodsCodes', label: '商品编码', sortable: 'custom' },
    { istrue: true, prop: 'backReason', label: '退货事由', sortable: 'custom' },
    { istrue: true, prop: 'initiateDDUserName', label: '发起人', sortable: 'custom' },
    { istrue: true, prop: 'initiateDateTime', label: '发起时间', sortable: 'custom' },
    { istrue: true, prop: 'approvalStatus', label: '审批状态', sortable: 'custom' },
    { istrue: true, prop: 'approvalPassDateTime', label: '审核时间', sortable: 'custom', type: "click", handle: (that, row) => that.msglog(row.id) },
    { istrue: true, prop: 'approvalRejectReason', label: '审批拒绝原因', sortable: 'custom' },
    {
        istrue: true, type: "button", label: '操作', btnList: [
            { label: "查看申请摘要", handle: (that, row) => that.openInformation(row) },
        ]
    }
]
export default {
    name: "encodingProcessing",
    components: { MyContainer, vxetablebase, inputYunhan },
    data() {
        return {
            that: this,
            ListInfo: {//列表信息
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                initiateStartTime: null,//发起开始时间
                initiateEndTime: null,//发起结束时间
                approvalPassStartTime: null,//审核开始时间
                approvalPassEndTime: null,//审核结束时间
                backReason: null,//退货事由
                approvalStatus: null,//审核状态
                initiateDDUserName: null,//发起人
                goodsCodes: null,//商品编码
            },
            listLoading: true,
            isOne: false,//是否是一个编码
            timeList: [],//审核时间
            timeList1: [],//发起时间
            total: 0,//总条数
            tableData: [],
            srcList: [],//图片列表
            tableCols,//表头
            backReasons,//退货事由
            approvalStatus,//审核状态
            formPorp: {
                goodsCodes: null,//商品编码
                remark: null,//备注
                pic: null
            },//弹层表单
            dialogVisible: false,//弹层显示
            pickerOptions: {//时间选择器快捷选项
                shortcuts: [
                    {
                        text: "近一周",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: "近一个月",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: "近三个月",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                ],
                disabledDate(time) {
                    return time.getTime() > Date.now();
                },
            },
        };
    },
    mounted() {
        this.getList()
    },
    methods: {
        //查看申请摘要
        openInformation(row) {
            //根据\n来分割字符串 
            this.formPorp = JSON.parse(JSON.stringify(row))
            this.dialogVisible = true
            this.srcList = JSON.parse(row.pic).map(item => item.url)
            //根据\n来切割row.pic
            this.isOne = row.goodsCodes.split('\n').length == 1 ? true : false
            console.log(this.isOne, 'this.isOne');
        },
        //发起时间更改
        changeLaunchTime(e) {
            if (!e) {
                this.ListInfo.initiateStartTime = null
                this.ListInfo.initiateEndTime = null
            } else {
                //使用dayjs将时间转化成yyyy-mm-dd格式
                this.ListInfo.initiateStartTime = dayjs(e[0]).format('YYYY-MM-DD')
                this.ListInfo.initiateEndTime = dayjs(e[1]).format('YYYY-MM-DD')
            }
            this.getList()
        },
        //审核时间更改
        changeAuditTime(e) {
            if (!e) {
                this.ListInfo.approvalPassStartTime = null
                this.ListInfo.approvalPassEndTime = null
            } else {
                this.ListInfo.approvalPassStartTime = dayjs(e[0]).format('YYYY-MM-DD')
                this.ListInfo.approvalPassEndTime = dayjs(e[1]).format('YYYY-MM-DD')
            }
            this.getList()
        },
        //对接记录弹层每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //对接记录弹层当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        //获取列表
        async getList() {
            // 清除发起人的空格
            if (this.ListInfo?.goodsCodes) {
                this.ListInfo.goodsCodes = this.ListInfo.goodsCodes.replace(/\s*/g, "")
            }
            //清除发起人的空格
            if (this.ListInfo?.initiateDDUserName) {
                this.ListInfo.initiateDDUserName = this.ListInfo.initiateDDUserName.replace(/\s*/g, "")
            }
            this.listLoading = true
            console.log(this.ListInfo,"aaaaaaaaaaaaaaaaaaaaaaaaaa");
            const { data, success } = await getGoodsBackDingDingPageList(this.ListInfo)
            this.listLoading = false
            if (success) {
                this.tableData = data.list
                this.total = data.total
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        handleClose() {
            this.dialogVisible = false;
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        msglog(rowid) {

            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/skuenquiry/encodingProcessingBackmsglog.vue`,
                title: '通知查看',
                args: { rowid },
                height: '500px',
                width: '50%',
            })
        },
    },
};
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
}

.publicMargin {
    margin-right: 10px;
}

.imgBox {
    display: flex;
    width: 80%;
    overflow: auto;
    flex-wrap: wrap;

    .imgcss ::v-deep img {
        min-width: 100px !important;
        min-height: 100px !important;
        width: 100px !important;
        height: 100px !important;
    }
}

::v-deep .dialog .el-dialog {
    min-width: 500px;
}
</style>
