<template>
    <!-- 设置 -->
    <my-container> 
        <el-tabs v-model="activeName" tab-position="left" style="height:100%;"  @tab-click="tabclick"  :before-leave="beforeleave" >  
            <el-tab-pane name="tab0" label="员工设置"  style="height: 100%;"  >
                <accountsManage></accountsManage>
            </el-tab-pane> 
            <el-tab-pane name="tab1" label="工作岗位"  style="height: 100%;" :lazy="true">
                <accountsWorkPostionManage :tichengshow="false"></accountsWorkPostionManage>
            </el-tab-pane>  
            <el-tab-pane name="tab2" label="提成岗位"   v-if="checkPermission('shootingCacl')"   style="height: 100%;" :lazy="true">
                <accountsWorkPostionManage :tichengshow="true"></accountsWorkPostionManage>
             </el-tab-pane>
            <el-tab-pane name="tab3"  label="岗位设置" v-if="checkPermission('shootingCd')" style="height: 100%;">
                <accountuserPositonManage></accountuserPositonManage>
            </el-tab-pane>   
        </el-tabs>  
           
    </my-container>
</template>
<script>
import accountsWorkPostionManage from '@/views/media/shooting/adjustAccounts/accountsWorkPostionManage'; 
import accountsManage from '@/views/media/shooting/adjustAccounts/accountsManageedit';
import accountsWorkCount from '@/views/media/shooting/adjustAccounts/accountsWorkCount';
import accountuserPositonManage from '@/views/media/shooting/adjustAccounts/accountuserPositonManage'; 
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
export default { 
    components: { MyContainer, MyConfirmButton, accountsWorkCount, accountsWorkPostionManage, accountuserPositonManage, accountsManage },
    data() {
        return {
            activeName: 'tab0',
            menuview: 1,
        };
    },
    async mounted() {

    },
    methods: {
    },
};
</script>
<style lang="scss" scoped>
.content {
    display: flex;
    flex-direction: row;
    margin: 0;
}

::v-deep .myheader {
    display: none;
}

.content::v-deep .el-submenu .el-menu-item {
    min-width: 155px !important;
} 
</style>

