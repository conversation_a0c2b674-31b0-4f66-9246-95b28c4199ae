<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="淘宝日报" name="first1" style="height: 100%">
        <productReportTaoBao ref="productReportTaoBao" style="height: 100%"></productReportTaoBao>
      </el-tab-pane>
      <el-tab-pane label="订单日报" name="first2" :lazy="true" style="height: 100%"
        v-if="checkPermission('TaoBaoOrderDayReport')">
        <TaoBaoOrderDayReport @ChangeActiveName2="ChangeActiveName2" :orderNoInner="orderNoInner"
          ref="TaoBaoOrderDayReport" style="height: 100%"></TaoBaoOrderDayReport>
      </el-tab-pane>
      <el-tab-pane label="编码日报" name="first3" :lazy="true" style="height: 100%"
        v-if="checkPermission('TaoBaoGoodCodeDayReport')">
        <TaoBaoGoodCodeDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="TaoBaoGoodCodeDayReport" style="height: 100%"></TaoBaoGoodCodeDayReport>
      </el-tab-pane>
      <el-tab-pane label="ID日报" name="first4" :lazy="true" style="height: 100%"
        v-if="checkPermission('TaoBaoIdDayReport')">
        <TaoBaoIdDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="TaoBaoIdDayReport" style="height: 100%"></TaoBaoIdDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺日报" name="first5" :lazy="true" style="height: 100%"
        v-if="checkPermission('TaoBaoShopDayReport')">
        <TaoBaoShopDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="TaoBaoShopDayReport" style="height: 100%"></TaoBaoShopDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺SKU日报" name="first7" :lazy="true" style="height: 100%"
        v-if="checkPermission('TaoBaoCommodityDayReport')">
        <TaoBaoCommodityDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="TaoBaoCommodityDayReport" style="height: 100%"></TaoBaoCommodityDayReport>
      </el-tab-pane>
      <el-tab-pane label="明细日报" name="first6" :lazy="true" style="height: 100%"
        v-if="checkPermission('TaoBaoDetailDayReport')">
        <TaoBaoDetailDayReport @ChangeActiveName="ChangeActiveName" @ChangeActiveName2="ChangeActiveName2"
          ref="TaoBaoDetailDayReport" style="height: 100%"></TaoBaoDetailDayReport>
      </el-tab-pane>
      <el-tab-pane label="出仓负利润ID订单明细" name="first8" :lazy="true" style="height: 100%"
        v-if="checkPermission('TaoBaoOutgoingprofitIDorderdetail')">
        <TaoBaoOutgoingprofitIDorderdetail @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="TaoBaoOutgoingprofitIDorderdetail" style="height: 100%"></TaoBaoOutgoingprofitIDorderdetail>
      </el-tab-pane>

      <el-tab-pane label="SKUS日报" name="first9" :lazy="true" style="height: 100%"
        v-if="checkPermission('TaoBaoOrderDayReport')">
        <TaoBaoSkusDayReport @ChangeActiveName2="ChangeActiveName2" :orderNoInner="orderNoInner"
          ref="TaoBaoSkusDayReport" style="height: 100%"></TaoBaoSkusDayReport>
      </el-tab-pane>

    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import productReportTaoBao from "./productReportTaoBao.vue";
import TaoBaoOrderDayReport from "./TaoBaoOrderDayReport.vue";
import TaoBaoSkusDayReport from "./TaoBaoSkusDayReport.vue";
import TaoBaoGoodCodeDayReport from "./TaoBaoGoodCodeDayReport.vue";
import TaoBaoIdDayReport from "./TaoBaoIdDayReport.vue";
import TaoBaoCommodityDayReport from "./TaoBaoCommodityDayReport.vue";
import TaoBaoOutgoingprofitIDorderdetail from "./TaoBaoOutgoingprofitIDorderdetail.vue";
import TaoBaoShopDayReport from "./TaoBaoShopDayReport.vue";
import middlevue from "@/store/middle.js"
import TaoBaoDetailDayReport from "./TaoBaoDetailDayReport.vue";
export default {
  props: ['orderNo', 'No'],
  name: "productReportTaoBaoIndex",
  components: {
    MyContainer, productReportTaoBao, TaoBaoOrderDayReport, TaoBaoSkusDayReport, TaoBaoGoodCodeDayReport, TaoBaoIdDayReport, TaoBaoShopDayReport, TaoBaoDetailDayReport, TaoBaoCommodityDayReport, TaoBaoOutgoingprofitIDorderdetail
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
      orderNoInner: null,
    };
  },
  async mounted() {
    middlevue.$on('toLinkTxDetailDayReport', (data) => {
      if (data.isTrue && data.plat == 'tb') {
        this.activeName = 'first6';
        setTimeout(() => {
          middlevue.$emit('toLinkTxDetailDayReportQuery', data)
        }, 500);

      } else {
        this.activeName = 'first8';
        setTimeout(() => {
          middlevue.$emit('toLinkTxOutgoingprofitIDorderdetailQuery', data)
        }, 500);
      }
    })
  },
  beforeDestroy() {
    middlevue.$off('toLinkTxDetailDayReport')
  },
  methods: {
    ChangeActiveName(activeName) {
      this.activeName = 'first2';
      this.$refs.TaoBaoOrderDayReport.TaoBaoGoodCodeDayReportArgument(activeName)
    },
    ChangeActiveName2(activeName, No, Time) {
      this.activeName = 'first6';
      this.$refs.TaoBaoDetailDayReport.TaoBaoDetailDayReportArgument(activeName, No, Time)
    }
  },
};
</script>

<style lang="scss" scoped></style>
