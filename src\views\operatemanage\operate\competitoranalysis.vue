<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="日期:">
            <el-date-picker @change="ondatechange"
                v-model="filter.timerange"
                type="datetimerange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
           ></el-date-picker>
        </el-form-item>
        <el-form-item label="类目:">
          <el-cascader @change="oncategorychange" 
            v-model="filter.categoryids"
            placeholder="请选择，支持搜索功能"
            :options="categorylist"
            :props="{ checkStrictly: false, value: 'id',multiple:false }"
            style="width:100%;"/>
        </el-form-item>
         <el-form-item label="组长:">
            <el-select v-model="filter.groupId" placeholder="请选择" class="el-select-content" @change="ongroupchange">
              <el-option label="请选择" value=""></el-option>
              <el-option
                  v-for="item in grouplist"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">
              </el-option>
            </el-select>
        </el-form-item>
         <el-form-item label="商品:">
            <el-select v-model="filter.selfProCode" placeholder="请选择" class="el-select-content">
              <el-option
                  v-for="item in productlist"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key">
                  <span style="float: left">{{ item.key+":" }}</span>
                  <span style="float: left; color: #8492a6; font-size: 13px">{{ item.value }}</span>
              </el-option>
            </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>

  <el-tabs v-model="activeName" style="height: auto;">
      <el-tab-pane label="手淘搜索分析" name="third">
        <prokeywordstanalysis :filter="filter" ref="prokeywordstanalysis"/>
      </el-tab-pane>
      <el-tab-pane label="手淘搜索数据" name="fourth">
        <prokeywordstdata :filter="filter" ref="prokeywordstdata" @onpktype2="onpktype2"/>
      </el-tab-pane>
      <el-tab-pane label="直通车分析" name="five">
        <prokeywordztcanalysis :filter="filter" ref="prokeywordztcanalysis"/>
      </el-tab-pane>
      <el-tab-pane label="直通车数据" name="six">
        <prokeywordztcdata :filter="filter" ref="prokeywordztcdata" @onpktype3="onpktype3"/>
      </el-tab-pane>
      <el-tab-pane label="关键词对比" name="seven">
        <fancypkanalysis :filter="filter" ref="fancypkanalysis"/>
      </el-tab-pane>
      <el-tab-pane label="订单占有率" name="seven1">
        <prokeywordorderdata :filter="filter" ref="prokeywordorderdata"/>
      </el-tab-pane>
  </el-tabs>
  </my-container>
</template>

<script>
 import {seachProCode} from '@/api/operatemanage/operate'
 import { getList as getcategorylist} from '@/api/operatemanage/base/category'
 import { getProductKeyValueByGroup,getGroupKeyValueByCategory} from '@/api/operatemanage/base/product'
 import prokeywordstanalysis from '@/views/operatemanage/operate/prokeywordstanalysis'
 import prokeywordstdata from '@/views/operatemanage/operate/prokeywordstdata'
 import prokeywordztcanalysis from '@/views/operatemanage/operate/prokeywordztcanalysis'
 import prokeywordztcdata from '@/views/operatemanage/operate/prokeywordztcdata'
 import fancypkanalysis from '@/views/operatemanage/operate/fancypkanalysis'
 import prokeywordorderdata from '@/views/operatemanage/operate/prokeywordorderdata'
 import MyContainer from '@/components/my-container/nofooter'
 import { treeToList, listToTree, getTreeParents } from '@/utils'
export default {
  name: 'Roles',
  components: { MyContainer,prokeywordstanalysis,prokeywordstdata,prokeywordztcanalysis,prokeywordztcdata,fancypkanalysis,prokeywordorderdata},
  data() {
    return {
      activeName: 'third',
      filter: {
        startDate: null,
        endDate: null,
        proCodeKey:null,
        selfProCode:null,
        timerange:null,
        categoryids:[],
        groupId:null,
      },
      options:[],
      categorylist:[],
      grouplist:[],
      productlist:[],
      pageLoading: false,    
      dialogVisible: false,
      selectloading:false,
    }
  },
  mounted() {
    this.getcategorylist();
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async getcategorylist() {
      const res = await getcategorylist({platform:1 })
      if (!res?.code) {
        return
      }
      const list=[];
      res.data.forEach(f=>{
        var item=  {label:f.categoryName,id:f.id,parentId:f.parentId}
        list.push(item)
      })
      this.categorylist = listToTree(_.cloneDeep(list), {
        id: '',
        parentId: '',
        label: '所有'
      })
    },
   ondatechange(){
       
    },
   async oncategorychange(value) {
    this.filter.selfProCode=null;
    this.filter.groupId=null;
    console.log('value',value)
    this.grouplist=[]
    if (value.length>1) {
      const res = await getGroupKeyValueByCategory({categoryId:value[value.length-1]})
      if (!res?.code) return
      this.grouplist=res.data
    }
  },
  async ongroupchange(value) {
    this.filter.selfProCode=null;
    var parm={categoryId:null, groupId:null};
    if (this.filter.categoryids.length>0) parm.categoryId=this.filter.categoryids[this.filter.categoryids.length-1]
    this.productlist=[]
    if (value) {
      parm.groupId=value
      const res = await getProductKeyValueByGroup(parm)
      if (!res?.code) return
      this.productlist=res.data
    }
  },
   remoteSeachProCode(key){
     if (!this.filter.timerange||this.filter.timerange.length<2){
        this.$message({message: "请先选择日期！",type: "warning",});
        return;
      }
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      if (key !== '') {
          this.filter.proCodeKey=key
          this.selectloading = true;
          setTimeout(async () => {
            this.selectloading = false;
            var res= await  seachProCode(this.filter);
            if (!res?.success) return;
            this.options = res.data.map(item => {
               return { value: item, label: item };
            });
          }, 200);
        } else {
          this.options = [];
        }
    },
    onSearch() {
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      if (!this.filter.timerange||this.filter.timerange.length<2){
        this.$message({message: "请先选择日期！",type: "warning",});
        return;
      }
      if (!this.filter.selfProCode){
        this.$message({message: "请先选择商品ID！",type: "warning",});
        return;
      }
      if (this.activeName=='third'){
         this.$refs.prokeywordstanalysis.initdata();
         this.$refs.prokeywordstanalysis.onSearch();
      }
      else if (this.activeName=='fourth'){
          this.$refs.prokeywordstdata.initdata();
          this.$refs.prokeywordstdata.onSearch();
      }
      else if (this.activeName=='five') {
         this.$refs.prokeywordztcanalysis.initdata();
         this.$refs.prokeywordztcanalysis.onSearch();
      }
      else if (this.activeName=='six'){
         this.$refs.prokeywordztcdata.initdata();
         this.$refs.prokeywordztcdata.onSearch();
      }
      else if (this.activeName=='seven'){
         this.$refs.fancypkanalysis.initdata();
         this.$refs.fancypkanalysis.onSearch();
      }
      else if (this.activeName=='seven1'){
         this.$refs.prokeywordorderdata.initdata();
         this.$refs.prokeywordorderdata.onSearch();
      }
    },
    onpktype2(ids,periodday,jpcode){
       this.activeName="third"
       this.$refs.prokeywordstanalysis.initdata();
       this.$refs.prokeywordstanalysis.onpk(ids,periodday,jpcode);
    },
    onpktype3(ids,periodday,jpcode){
       this.activeName="five"
       this.$refs.prokeywordztcanalysis.initdata();
       this.$refs.prokeywordztcanalysis.onpk(ids,periodday,jpcode);
    }
  }
}
</script>

<style>
.el-tabs__content {
    overflow: hidden;
    position: relative;
    height: 100%;
}
.el-select-content { 
    width: calc(100% - 10px);
    margin: 0;
 }
.bottombadge {
    position: absolute;
    
    transform: translateY(40%) translateX(100%);
    /* margin: 0 0px -5px 15px; */
}
.bigbadge {
  font-size: initial;
}
.redcolor {
  color: red;
}
.greencolor {
  color:green;
}
</style>
