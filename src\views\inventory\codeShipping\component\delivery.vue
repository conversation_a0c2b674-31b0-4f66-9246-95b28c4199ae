<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                <el-select v-model="ListInfo.deliveryMethod" placeholder="配送方式" class="publicCss" clearable>
                    <el-option label="物流自提" :value="1" />
                    <el-option label="送货上门" :value="2" />
                </el-select>
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable>
                    <el-option label="待维护" :value="1" />
                    <el-option label="待更新" :value="2" />
                    <el-option label="完成" :value="3" />
                </el-select>
                <el-select v-model="ListInfo.brandId" clearable filterable placeholder="请选择采购员" class="publicCss">
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.postNameList" placeholder="岗位" class="publicCss" clearable multiple
                    collapse-tags @change="changePost($event, 'post')">
                    <el-option v-for="item in postNameList" :label="item.label" :value="item.value" :key="item.value" />
                </el-select>
                <el-select v-model="ListInfo.deptIdsList" placeholder="架构" class="publicCss" clearable multiple
                    collapse-tags @change="changePost($event, 'dept')">
                    <el-option v-for="item in deptNameList" :label="item.label" :value="item.value" :key="item.value" />
                </el-select>
                <el-select v-model="ListInfo.companyName" placeholder="分公司" class="publicCss" clearable>
                    <el-option label="南昌" value="南昌" />
                    <el-option label="义乌" value="义乌" />
                    <el-option label="其他" value="其他" />
                </el-select>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode"
                    width="156px" placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300"
                    :maxlength="6000" @callback="productCodeCallback" title="商品编码" class="publicCss">
                </inputYunhan>
                <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="100" clearable
                    class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
            </div>
        </template>
        <vxetablebase :id="'delivery202408041551'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="170">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <el-button type="text" @click="viewProps(row.buyNo, true)">查看</el-button>
                            <el-button type="text" @click="viewProps(row.buyNo, false)"
                                v-if="!row.editDisable">编辑</el-button>
                            <el-button type="text"
                                v-if="checkPermission('api:inventory:purchaseOrderGoodsPostage:DeletePurchaseOrderGoodsPostage')"
                                @click="delProps(row.goodsCode)" style="color: red;">删除</el-button>
                            <el-button type="text" @click="viewHistoryProps(row.goodsCode)">历史记录</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="历史记录" :visible.sync="historyVisible" width="50%" v-dialogDrag>
            <div style="height: 400px;">
                <historyProps :goodsCode="goodsCode" payment="寄付" v-if="historyVisible" />
            </div>
        </el-dialog>

        <el-drawer :title="title" :visible.sync="freeShipVisable" direction="rtl" :wrapperClosable="false" size="70%">
            <deliveryEditOrViewPage :isView="isView" @close="close" @getList="getList" payment="寄付" :buyNo="buyNo"
                v-if="freeShipVisable" />
        </el-drawer>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import inputYunhan from "@/components/Comm/inputYunhan";
import { getAllProBrand } from '@/api/inventory/warehouse'
import {
    getPurchasePostNameList,
    getPurchaseDept2List,
    getPurchaseOrderGoodsPostagePageList,
    exportPurchaseOrderGoodsPostagePageList,
    deletePurchaseOrderGoodsPostage,
} from '@/api/inventory/purchaseOrderGoodsPostage'
import historyProps from './historyProps.vue'
import dayjs from 'dayjs'
import deliveryEditOrViewPage from './deliveryEditOrViewPage.vue'
import { getUserInfo } from '@/api/operatemanage/productalllink/alllink'
const status = [
    { label: '待维护', value: 1 },
    { label: '待更新', value: 2 },
    { label: '完成', value: 3 },
]
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'postageFreePrice', label: '包邮单价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'postageChargePrice', label: '不包邮单价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'postageFreePriceDiff', label: '包邮单价差额', },
    {
        sortable: 'custom', width: 'auto', align: 'center', prop: 'deliveryMethod', label: '配送方式', formatter: (row) => {
            if (row.deliveryMethod == 1) {
                return '物流自提'
            } else if (row.deliveryMethod == 2) {
                return '送货上门'
            } else {
                return ''
            }
        }
    },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'csResult', label: '计算结果', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '状态', formatter: (row) => status.find(item => item.value == row.status).label },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'brandName', label: '采购', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'postName', label: '岗位', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'deptName', label: '架构', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'companyName', label: '分公司', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'modifiedTime', label: '更新日期', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, inputYunhan, historyProps, deliveryEditOrViewPage
    },
    data() {
        return {
            userinfo: {},
            status,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'modifiedTime',
                isAsc: false,
                startDate: null,//开始时间
                endDate: null,//结束时间
                payment: '寄付',//配送方式
                goodsCode: null,//商品编码
                goodsName: null,//商品名称
                deliveryMethod: null,//配送方式
                status: null,//状态
                brandId: null,//采购员
                postNames: null,//岗位
                deptName: null,//架构
                deptIds: '',//架构
                deptIdsList: [],//架构
                postName: '',//岗位
                postNameList: [],//岗位
                companyName: null,//分公司
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            brandlist: [],
            postNameList: [],
            deptNameList: [],
            goodsCode: null,//商品编码
            historyVisible: false,//查看历史记录
            buyNo: null,
            freeShipVisable: false,
            isView: false,
            title: '编辑',
        }
    },
    async mounted() {
        await this.init()
        await this.getList()
    },
    methods: {
        changePost(e, type) {
            if (type == 'post') {
                this.ListInfo.postNames = e ? e.join(',') : ''
            } else {
                this.ListInfo.deptIds = e ? e.join(',') : ''
            }
        },
        close() {
            this.freeShipVisable = false
        },
        viewProps(buyNo, isView) {
            this.buyNo = buyNo
            this.isView = isView
            this.title = isView ? '查看' : '编辑'
            this.freeShipVisable = true
        },
        async viewHistoryProps(goodsCode) {
            this.goodsCode = goodsCode
            this.historyVisible = true
        },
        async init() {
            const { data } = await getAllProBrand();
            this.brandlist = data.map(item => {
                return { value: item.key, label: item.value };
            });
            const { data: data1 } = await getPurchasePostNameList()
            this.postNameList = data1
            const { data: data2 } = await getPurchaseDept2List()
            this.deptNameList = data2
            this.userinfo = await getUserInfo();
        },
        delProps(goodsCode) {
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                //删除方法
                const { success } = await deletePurchaseOrderGoodsPostage({ goodsCode, payment: '寄付' })
                if (success) {
                    await this.getList()
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        async productCodeCallback(val) {
            this.ListInfo.goodsCode = val;
        },
        async changeTime(e) {
            this.ListInfo.startDate = e ? e[0] : null
            this.ListInfo.endDate = e ? e[1] : null
            await this.getList()
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportPurchaseOrderGoodsPostagePageList(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '寄付数据' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data: { list, total, summary }, success } = await getPurchaseOrderGoodsPostagePageList(this.ListInfo)
                if (success) {
                    this.tableData = list
                    this.total = total
                    this.summaryarry = summary
                    this.tableData.forEach(item => {
                        item.editDisable = item.brandId != this.userinfo.data?.brandId
                    })
                } else {
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.$message.error('获取列表失败')
            } finally {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 170px;
        margin-right: 10px;
    }
}

::v-deep .el-select__tags-text {
    max-width: 40px;
}
</style>
