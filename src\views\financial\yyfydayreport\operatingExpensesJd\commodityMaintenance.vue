<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.shopGoodsCode" placeholder="SKU" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.combinationCode" placeholder="组合编码" maxlength="50" clearable
          class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="onAddMethod">新增</el-button>
        <el-button type="success" @click="startImport">导入</el-button>
      </div>
    </template>
    <vxetablebase :ispoint="false" :id="'productMaintenance202411261650'" :tablekey="'productMaintenance202411261650'"
      ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
      :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="onEdit(row)">编辑</el-button>
              <!-- <el-button type="text" @click="onDelete(row)"><span style="color: red;">删除</span></el-button> -->
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="ruleTitle" :visible.sync="newEditVisible" width="35%" v-dialogDrag>
      <div style="height: 170px;padding: 30px 5px 0 5px;">
        <el-form :model="ruleForm" :rules="editrules" ref="ruleForm" label-width="110px" class="demo-ruleForm">
          <el-row>
            <el-col :span="12">
              <el-form-item label="店铺商品编码" prop="shopGoodsCode">
                <el-input v-model.trim="ruleForm.shopGoodsCode" placeholder="请输入店铺商品编码" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="组合编码" prop="combinationCode">
                <el-input v-model.trim="ruleForm.combinationCode" placeholder="请输入组合编码" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="子编码" prop="subcoding">
                <el-input v-model.trim="ruleForm.subcoding" placeholder="请输入子编码" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数量" prop="qty">
                <el-input-number v-model="ruleForm.qty" placeholder="请输入数量" :min="0" :max="9999999" :precision="0"
                  :controls="false" class="editCss" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="newEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="onStorageMethodDebounced">确 定</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import { importSKU_JingDongSelfSupportsync, getJdSelfSKUList, addorEditShopGoodsCode_JingDongSelfSupport, deleteJdSelfSKUBatchAsync, getSKU_JingDongSelfSupport } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
import { formatTime } from "@/utils";
const tableCols = [
  { sortable: 'custom', width: '200', align: 'center', prop: 'shopGoodsCode', label: 'SKU', },
  { sortable: 'custom', width: '200', align: 'center', prop: 'combinationCode', label: '组合编码', },
  { sortable: 'custom', width: '200', align: 'center', prop: 'subcoding', label: '子编码', },
  { sortable: 'custom', width: '200', align: 'center', prop: 'qty', label: '数量', },
]
export default {
  name: "productMaintenance",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      fileList: [],
      fileparm: {},
      uploadLoading: false,
      dialogVisible: false,
      ruleTitle: '新增',
      ruleForm: {
        subcoding: null,
        combinationCode: null,
        qty: undefined,
        shopGoodsCode: null,
        id: null,
      },
      editrules: {
        shopGoodsCode: [{ required: true, message: '请输入店铺商品编码', trigger: 'blur' }],
        combinationCode: [{ required: true, message: '请输入组合编码', trigger: 'blur' }],
        qty: [{ required: true, message: '请输入数量', trigger: 'blur' }],
        subcoding: [{ required: true, message: '请输入子编码', trigger: 'blur' }],
      },
      newEditVisible: false,//新增编辑弹窗
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        shopGoodsCode: null,
        combinationCode: null,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    //防抖
    onStorageMethodDebounced: _.debounce(function (param) {
      this.onSingleSave(param);
    }, 1000),
    onSingleSave() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          const { success } = await addorEditShopGoodsCode_JingDongSelfSupport(this.ruleForm)
          if (success) {
            this.$message.success('操作成功')
            this.newEditVisible = false
            this.getList()
          } else {
            this.$message.error('操作失败')
          }
        }
      })
    },
    onCleardataMethod() {
      this.ruleForm = {
        shopGoodsCode: null,
        combinationCode: null,
        qty: undefined,
        subcoding: null,
        id: null,
      }
    },
    onAddMethod() {
      this.onCleardataMethod()
      this.ruleTitle = '新增'
      this.newEditVisible = true
    },
    async onDelete(row) {
      this.$confirm('是否删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await deleteJdSelfSKUBatchAsync({ batchNumber: row.batchNumber })
        if (success) {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      });
    },
    async onEdit(row) {
      this.onCleardataMethod()
      setTimeout(() => {
        this.ruleForm = JSON.parse(JSON.stringify(row))
        this.ruleTitle = '编辑'
        this.newEditVisible = true
      }, 100)
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importSKU_JingDongSelfSupportsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getSKU_JingDongSelfSupport({ ...this.ListInfo })
      this.loading = false
      if (success && data && data.list) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        // let summary = data.summary || {}
        // const resultsum = {};
        // Object.entries(summary).forEach(([key, value]) => {
        //   resultsum[key] = formatNumber(value);
        // });
        // function formatNumber(number) {
        //   const options = {
        //     useGrouping: true,
        //   };
        //   return new Intl.NumberFormat('zh-CN', options).format(number);
        // }
        // this.summaryarry = resultsum
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 170px;
    margin-right: 5px;
  }
}

.editCss {
  width: 90%;
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep .el-input-number.is-without-controls .el-input__inner {
  padding-left: 5px;
}
</style>
