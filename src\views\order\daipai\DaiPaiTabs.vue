<template>
    <my-container v-loading="pageLoading">

        <el-tabs v-model="activeName" style="height: calc(100% - 40px);">
            <el-tab-pane label="内部代拍订单" name="tab1" style="height: 100%;">
                <DpImpOrderList ref="dpImpOrderList" />
            </el-tab-pane>
            <el-tab-pane label="厂家代拍订单" name="tab2" style="height: 100%;" :lazy="true">
                <DpSpOrderList ref="dpSpOrderList" />
            </el-tab-pane>
            <el-tab-pane label="代拍厂家" name="tab3" style="height: 100%;" :lazy="true">
                <SupplierList ref="supplierList" />
            </el-tab-pane>
            <el-tab-pane label="代拍商品关联" name="tab4" style="height: 100%;" :lazy="true">
                <YhGoodsList ref="yhGoodsList" />
            </el-tab-pane>

        </el-tabs>

    </my-container>
</template>
<script>

 
    import MyContainer from "@/components/my-container";    

    import DpImpOrderList from "@/views/order/daipai/DpImpOrderList.vue";
    import DpSpOrderList from "@/views/order/daipai/DpSpOrderList.vue";
    import SupplierList from "@/views/order/daipai/SupplierList.vue";
    import YhGoodsList from "@/views/order/daipai/YhGoodsList.vue";

    
 

    export default {
        name: "DaiPaiTabs",
        components: { MyContainer, SupplierList ,YhGoodsList,DpImpOrderList,DpSpOrderList},
        data() {
            return {                
                activeName: 'tab1',
                that: this,
                pageLoading: false,
                importNumber: ''
            };
        }
    }

</script>