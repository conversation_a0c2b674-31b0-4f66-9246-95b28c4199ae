<template>
  <container v-loading="pageLoading">
      <!-- :tableHandles='tableHandles'  -->
     <ces-table ref="table" :that='that' :isIndex='true'   
         :hasexpand='true' :tableData='list' :tableCols='tableCols' @sortchange='sortchange' :loading="listLoading"
         :showsummary='true' :summaryarry='summaryarry'>
      
         <template slot='extentbtn'>
      <el-button-group>
    <el-button type="primary" @click="onSearch">刷新</el-button> 
      </el-button-group>
       </template>
      </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

  </container>
</template>
<script>
import {getShopProfitAnalysisSum,getOperatingProfitAnalysisSum,getDetail2Pdd}from '@/api/bookkeeper/financialreport'
import {formatTime,formatYesornoBool,formatWarehouseArea,formatIsOutStock,formatSecondToHour,formatPlatform} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import logistics from '@/components/Comm/logistics'
import goodscoderecord from '@/views/inventory/goodscoderecord'
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";

const tableCols =[
   
      {istrue:true,prop:'version',label:'类型', width:'50',formatter:(row)=>!row.version?"": row.version=='v1'?"工资月报":"参考月报"},
      {istrue:true,prop:'yearMonth',label:'年月',sortable:'custom', width:'80',},
      {istrue:true,prop:'productID',label:'产品ID',sortable:'custom', width:'80',},
      {istrue:true,prop:'operating',label:'运营',sortable:'custom', width:'80',},
       {istrue:true,prop:'remark',label:'备注',sortable:'custom', width:'80',},
      {istrue:true,prop:'operatingFee',label:'运营提成', width:'80',},
      {istrue:true,prop:'shopName',label:'店铺',sortable:'custom', width:'60',},
      {istrue:true,prop:'goodsCode',label:'商品编码',sortable:'custom', width:'80',},
      {istrue:true,prop:'seriesCoding',label:'系列编码',sortable:'custom', width:'80',},
      {istrue:true,prop:'productName',label:'产品名称', width:'80',sortable:'custom'},
      {istrue:true,prop:'idNumber',label:'ID计数', width:'80',sortable:'custom'},
      {istrue:true,prop:'settlementIncome',label:'结算收入', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountSettlement_2',label:'2月之前月份收入', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountCrossMonthIn',label:'跨月收入', width:'80',sortable:'custom'},
      {istrue:true,prop:'refund',label:'退款', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountCrossMonthOut',label:'跨月退款', width:'80',sortable:'custom'},
      {istrue:true,prop:'taoKeNot',label:'淘客不计', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountShare',label:'参与公摊金额', width:'80',sortable:'custom'},
      {istrue:true,prop:'saleBen',label:'销售成本', width:'80',sortable:'custom'},
      {istrue:true,prop:'agentCost',label:'代拍成本', width:'80',sortable:'custom'},
      {istrue:true,prop:'replacementAmount',label:'补发成本', width:'80',sortable:'custom'},
      {istrue:true,prop:'abnormalAmount',label:'异常成本', width:'80',sortable:'custom'},
      {istrue:true,prop:'cornerBalance',label:'定制护墙角差额', width:'80',sortable:'custom'},
      {istrue:true,prop:'purchaseFreight',label:'采购运费', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountGrossProfitSale',label:'销售毛利', width:'80',sortable:'custom'},
      {istrue:true,prop:'grossProfit',label:'产品利润', width:'80',sortable:'custom'},
      {istrue:true,prop:'amontAdditionfee',label:'除运营工资、美工提成、采购提成工资', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_1',label:'万达办公房租', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_2',label:'万达宿舍', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_3',label:'圆通仓储/办公房租', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_4',label:'圆通宿舍房租', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_5',label:'诚信通仓库房租', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_6',label:'诚信通宿舍', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_7',label:'上海云仓宿舍', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_8',label:'南昌办公', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_9',label:'南昌仓库房租', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_10',label:'南昌宿舍房租', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_11',label:'维修费', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_12',label:'软件费（年付）', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_13',label:'保险费', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_14',label:'餐费', width:'80',sortable:'custom'},

      {istrue:true,prop:'detail2_15',label:'软件费(月付)', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_16',label:'日常记帐-线下支出', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_17',label:'水电费', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_18',label:'折旧', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_19',label:'招待费', width:'80',sortable:'custom'},    
      {istrue:true,prop:'detail2_21',label:'税费', width:'80',sortable:'custom'},
      {istrue:true,prop:'detail2_22',label:'做账费', width:'80',sortable:'custom'},
      {istrue:true,prop:'netProfit',label:'净利润', width:'80',sortable:'custom'},
    

     ];


export default {
  name: "Users",
  components: {container,cesTable,MyConfirmButton,logistics},
   props:{
       filter: { }
     },
  data() {
    return {
    
      uploadLoading:false,
      dialogVisible: false,
      that:this,
      // filter: {
      //   timerange:'',   
      // },
      list: [],
      drawervisible:false,
      tableCols:tableCols,
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      listLoading: false,
      pageLoading: false,
    };
  },
  watch: {
    value(n) {
      if(n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    }
  },
 async mounted() {
    await this.onSearch();
    await this.getlist();
  },
 methods: {
   onSelsChangeReturnGoods(sels){
    this.sels2 = sels
   },

  
   async onSearch() {
       this.$refs.pager.setPage(1)
       this.getlist()
    },
   async getlist() {
     this.filter.startTime =null;
       this.filter.endTime =null;
       if (this.filter.yearMonth && this.filter.yearMonth.length>0) {
                this.filter.startTime = this.filter.yearMonth[0];
                this.filter.endTime = this.filter.yearMonth[1];
            }
      if (!this.pager.OrderBy) this.pager.OrderBy="";
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      params.SeriesCoding=this.filter.SeriesCoding.join();
      this.listLoading = true
      const res = await getDetail2Pdd(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {d._loading = false})
      this.list = data
      this.summaryarry=res.data.summary;
    },

    beforeRemove() {
      return false;
    },
    
   doCopy: function (val) {       
      let that=this;                         
      this.$copyText(val).then(function (e) {
          that.$message({ message: "内容已复制到剪切板！", type: "success" });
      }, function (e) {
          that.$message({ message: "抱歉，复制失败！", type: "warning" });
      })
    },
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   
  },
};
</script>
