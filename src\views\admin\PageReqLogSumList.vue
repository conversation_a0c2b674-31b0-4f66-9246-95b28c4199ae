<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                <el-button-group style="border:none;">

                    <el-button style="padding: 0;margin: 0;border:none;">
                        <el-date-picker style="width:220px"
                            v-model="Filter.gDate"
                            type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            :picker-options="pickerOptions">
                        </el-date-picker>
                    </el-button>


                    <el-button style="padding: 0;margin: 0;border:none;width:80px;">
                        <el-select v-model="Filter.appType"  clearable placeholder="来源" filterable>
                            <el-option v-for="(item,i) in appTypes" :label="item" :key="i" :value="item" style="width: 200px;"></el-option>
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none;width:100px;">
                        <el-select v-model="Filter.moduleName" filterable clearable placeholder="模块" >
                            <el-option v-for="(item,i) in moduleNames" :label="item" :key="i" :value="item" style="width: 200px;"></el-option>
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none;width:100px;">
                        <el-select v-model="Filter.pageName" filterable clearable placeholder="页面" >
                            <el-option v-for="(item,i) in pageNames" :label="item" :key="i" :value="item" style="width: 200px;"></el-option>
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none;width:100px;">
                        <el-select v-model="Filter.pageTab" filterable clearable placeholder="页签" >
                            <el-option v-for="(item,i) in pageTabs" :label="item" :key="i" :value="item" style="width: 200px;"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none;width:80px;">
                        <!-- <el-select v-model="Filter.actionName" filterable clearable placeholder="行为">
                            <el-option v-for="(item,i) in actionNames" :label="item" :key="i" :value="item" style="width: 300px;"></el-option>
                        </el-select> -->
                        <el-input v-model.trim="Filter.actionName" placeholder="行为" maxlength="50" clearable style="width: 80px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none;width:100px;">
                        <el-select v-model="Filter.dept1"  clearable placeholder="区域" filterable>
                            <el-option v-for="(item,i) in dept1s" :label="item" :key="i" :value="item" style="width: 200px;"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none;width:100px;">
                        <el-select v-model="Filter.dept2"  clearable placeholder="部门1" filterable>
                            <el-option v-for="(item,i) in dept2s" :label="item" :key="i" :value="item" style="width: 200px;"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none;width:100px;">
                        <el-select v-model="Filter.dept3"  clearable placeholder="部门2" filterable>
                            <el-option v-for="(item,i) in dept3s" :label="item" :key="i" :value="item" style="width: 200px;"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none;width:100px;">
                        <el-select v-model="Filter.dept4"  clearable placeholder="部门3" filterable>
                            <el-option v-for="(item,i) in dept4s" :label="item" :key="i" :value="item" style="width: 200px;"></el-option>
                        </el-select>
                    </el-button>



                    <el-button style="padding: 0;margin: 0;border:none;">
                        <el-input v-model.trim="Filter.userName" clearable placeholder="用户" style="width:80px;"  :maxlength="40">
                        </el-input>
                    </el-button>


                    <el-button style="padding: 0;margin: 0;border:none;">
                        <el-input v-model.trim="Filter.productManager" clearable placeholder="产品" style="width:80px;"  :maxlength="40">
                        </el-input>
                    </el-button><el-button style="padding: 0;margin: 0;border:none;">
                        <el-input v-model.trim="Filter.developer" clearable placeholder="开发" style="width:80px;"  :maxlength="40">
                        </el-input>
                    </el-button><el-button style="padding: 0;margin: 0;border:none;">
                        <el-input v-model.trim="Filter.tester" clearable placeholder="测试" style="width:80px;"  :maxlength="40">
                        </el-input>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none;width:100px;">
                    <el-select v-model="Filter.xqDept1" clearable placeholder="需求一级部门">

                        <el-option v-for="(item, i) in xqDept1s" :label="item" :key="i" :value="item" style="width: 200px;"></el-option>

                    </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none;width:100px;">
                    <el-select v-model="Filter.xqDept2" clearable placeholder="需求二级部门">

                        <el-option v-for="(item, i) in xqDept2s" :label="item" :key="i" :value="item" style="width: 200px;"></el-option>


                    </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none;">
                        <el-input v-model.trim="Filter.xqUserName" clearable placeholder="需求提出人" style="width:100px;" :maxlength="40">
                        </el-input>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none;padding-top: 3px;padding-bottom: 3px;">
                        <el-switch
                            v-model="Filter.exceptIt" active-text="排除IT部访问记录"
                            active-color="#13ce66"
                            inactive-color="#ff4949">
                            </el-switch>
                    </el-button>


                    <el-button type="primary" @click="onSearch" >查询</el-button>
                    <el-button type="primary" @click="exportProps">导出</el-button>
                    <el-button type="primary" @click="onSetPageDeveloper">设置项目人员</el-button>

                </el-button-group>

                <el-button style="padding: 0;margin: 0;border:none;">
                        <el-checkbox-group v-model="groupProps" @change="groupPropsChange">
                            <el-checkbox label="ReqDate">日期</el-checkbox>
                            <el-checkbox label="AppType">来源</el-checkbox>
                            <el-checkbox label="ModuleName">模块</el-checkbox>
                            <el-checkbox label="PageName">页面</el-checkbox>
                            <el-checkbox label="PageTab" >页签</el-checkbox>
                            <el-checkbox label="ActionName" >行为</el-checkbox>
                            <el-checkbox label="UserName" >用户</el-checkbox>
                            <el-checkbox label="Dept1" >区域公司</el-checkbox>
                            <el-checkbox label="Dept2" >一级部门</el-checkbox>
                            <el-checkbox label="Dept3" >二级部门</el-checkbox>
                            <el-checkbox label="Dept4" >三级部门</el-checkbox>

                            <el-checkbox label="ProductManager" >产品</el-checkbox>
                            <el-checkbox label="Developer" >开发</el-checkbox>
                            <el-checkbox label="Tester" >测试</el-checkbox>
                            <el-checkbox label="XqDept1" >需求一级部门</el-checkbox>
                            <el-checkbox label="XqDept2" >需求二级部门</el-checkbox>
                            <el-checkbox label="XqUserName" >需求提出人</el-checkbox>

                        </el-checkbox-group>
                    </el-button>
            </el-form>
        </template>

         <vxetablebase :id="'PageReqLogSumList202405142102'"
            ref="xTable" :showsummary="true"
            :summaryarry="summaryarry"
            v-if="tbdatalist.length>0"
            :that='that' :loading="listLoading"
            :tableData='tbdatalist' :tableCols='calcTableCols'
            @sortchange='sortchange'
            >
            <template slot="right">
                <vxe-column title="操作" fixed="right" :width="120">
                <template #default="{row,$index}">
                    <el-button type="primary" @click="onSetPageDeveloper(row)">设置项目人员</el-button>
                </template>
                </vxe-column>
            </template>

        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
        </template>


    </my-container>
</template>
<script>

    import { GetPageReqLogFilters, PagePageReqLog, pagePageReqLogExportAsync } from '@/api/admin/opration-log';


    import dayjs from "dayjs";

    import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";

    import { formatmoney, formatPercen, getUrlParam, platformlist, formatPlatform, formatTime, setStore, getStore, formatLinkProCode,formatNoLink } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";



    const tableCols = [

        { istrue: true, prop: 'reqDate', label: '日期',width:'80', sortable: 'custom', formatter: (row) => formatTime(row.reqDate, "YYYY-MM-DD")},
        { istrue: true, prop: 'appType', label: '来源',minwidth:'70', sortable: 'custom' },
        { istrue: true, prop: 'moduleName', label: '模块',minwidth:'80', sortable: 'custom' },
        { istrue: true, prop: 'pageName', label: '页面',minwidth:'80', sortable: 'custom' },
        { istrue: true, prop: 'pageTab', label: '页签', minwidth: '80', sortable: 'custom' },
        { istrue: true, prop: 'actionName', label: '行为', minwidth: '80', sortable: 'custom' },

        { istrue: true, prop: 'dept1', label: '区域公司', minwidth: '80', sortable: 'custom' },
        { istrue: true, prop: 'dept2', label: '一级部门', minwidth: '80', sortable: 'custom' },
        { istrue: true, prop: 'dept3', label: '二级部门', minwidth: '80', sortable: 'custom' },
        { istrue: true, prop: 'dept4', label: '三级部门', minwidth: '80', sortable: 'custom' },
        { istrue: true, prop: 'userName', label: '用户', minwidth: '80', sortable: 'custom' },
        { istrue: true, prop: 'reqCount', label: '次数', minwidth: '80', sortable: 'custom'  },
        { istrue: true, prop: 'userCount', label: '人数', minwidth: '80', sortable: 'custom'  },
        { istrue: true, prop: 'userAvg', label: '人均', minwidth: '80', sortable: 'custom'  },

        { istrue: true, prop: 'productManager', label: '产品', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'developer', label: '开发', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'tester', label: '测试', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'xqDept1', label: '需求一级部门', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'xqDept2', label: '需求二级部门', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'xqUserName', label: '需求提出人', width: '80', sortable: 'custom' },

       // {  width:'130', align:'center', fixed: 'right',  label: '操作', type: 'button',    btnList:[ { label: "设置项目人员", handle: (that, row) => that.onSetPageDeveloper(row)   }]     },

    ];


    const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    const star = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");

    export default {
        name: "PageReqLogSumList",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, vxetablebase },
        data() {
            return {
                that: this,
                Filter: {
                    appType:'',
                    moduleName:'',
                    pageName:'',
                    pageTab:'',
                    actionName:'',
                    userName:'',
                    dept1:'',
                    dept2:'',
                    dept3:'',
                    dept4:'',

                    productManager:'',
                    developer:'',
                    tester:'',
                    xqDept1:'',
                    xqDept2:'',
                    xqUserName:'',
                    exceptIt:true,
                    gDate: [
                        formatTime(dayjs().subtract(14, "day"), "YYYY-MM-DD"),
                        formatTime(new Date(), "YYYY-MM-DD"),
                    ],

                },
                lastFilter:{},
                summaryarry:{},
                groupProps:["ReqDate","AppType","ModuleName","PageName","Dept1","Dept2","Dept3"],
                appTypes:[],
                moduleNames:[],
                pageNames:[],
                pageTabs:[],
                actionNames:[],
                dept1s:[],
                dept2s:[],
                dept3s:[],
                dept4s:[],

                xqDept1s:[],
                xqDept2s:[],

                tbdatalist: [],
                tableCols: tableCols,
                total: 0,
                pager: { OrderBy: "", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,

                curRow: {},



                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一月',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三月',
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                        }
                    }]
                },

            };
        },
        computed:{
            calcTableCols(){
                var rltCols=this.tableCols.filter(x=> {
                    if(x.prop=="reqCount" || x.prop=="userCount" || x.prop=="userAvg" )
                        return true;

                    if(this.groupProps!=null && this.groupProps.length>0){
                        let tempP=this.groupProps.find(y=> y.toUpperCase()== x.prop.toUpperCase());
                        if(tempP )
                            return true;
                    }

                    return false;
                });

                console.log(rltCols);
                return rltCols;
            }
        },
        async mounted() {
            let fds=await GetPageReqLogFilters();
            if(fds && fds.success){
                this.appTypes=fds.data.appType;
                this.moduleNames=fds.data.moduleName;
                this.pageNames=fds.data.pageName;
                this.pageTabs=fds.data.pageTab;
                this.actionNames=fds.data.actionName;
                this.dept1s=fds.data.dept1;
                this.dept2s=fds.data.dept2;
                this.dept3s=fds.data.dept3;
                this.dept4s=fds.data.dept4;

                this.xqDept1s = fds.data.xqDept1;
                this.xqDept2s = fds.data.xqDept2;
            }

            this.onSearch();
        },
        methods: {
            onSetPageDeveloper(row){
                this.$showDialogform({
                    path: `@/views/admin/Setting/PageDevelopSettingForm.vue`,
                    title: '页面开发人员设置',
                    autoTitle:false,
                    args: {model:3,row:row?row:null},
                    height: 600,
                    width: '640px',
                });
            },
            //导出
            async exportProps() {
              var pager = this.$refs.pager.getPager();
              let para = { ...this.Filter,groupProps:this.groupProps,...pager, ...this.pager, };
              this.listLoading = true
              const res = await pagePageReqLogExportAsync(para)
              this.listLoading = false
              const aLink = document.createElement("a");
              let blob = new Blob([res], { type: "application/vnd.ms-excel" })
              aLink.href = URL.createObjectURL(blob)
              aLink.setAttribute('download', '操作日志导出_' + name + '_' + new Date().toLocaleString() + '.xlsx');
              aLink.click()
            },
            groupPropsChange(){
                this.total = 0;
                this.tbdatalist = [];
                this.onSearch();
                this.$nextTick(()=>{
                    this.$refs["xTable"].$refs.xTable.resetColumn(true);
                    //this.$refs["xTable"].$refs.xTable.recalculate();
                });
            }  ,
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderField = column.prop;

                    this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                }
                this.onSearch();
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.gettbdatalist1();
            },
            async gettbdatalist1() {
                if (this.Filter.gDate) {
                    this.Filter.reqTimeStart = this.Filter.gDate[0];
                    this.Filter.reqTimeEnd = this.Filter.gDate[1];
                }
                else {
                    this.Filter.reqTimeStart = null;
                    this.Filter.reqTimeEnd = null;

                }
                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,
                };

                params.groupProps=this.groupProps;

                this.lastFilter={...para};

                this.listLoading = true;
                const res = await PagePageReqLog(params);

                this.listLoading = false;

                this.total = res.data.total
                this.tbdatalist = res.data.list;
                this.summaryarry=res.data.summary;

            },
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>

