<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss" />
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable>
                    <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="handleClick(false)">新增</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' id="20250617141105"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false" border
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="120">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="handleClick(true, row)">编辑</el-button>
                            <el-button type="text" @click="handleDelete(row.id)">删除</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
            <template #content="{ row }">
                <el-button type="text" @click="viewContent(row.content)">查看</el-button>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog :title="isEdit ? '编辑公告' : '新增公告'" :visible.sync="dialogVisible" width="50%" v-dialogDrag
            :close-on-click-modal="false" :before-close="() => { dialogVisible = false; dialogVisible1 = false }">
            <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="130px"
                class="demo-ruleForm">
                <el-form-item label="区域:" prop="areaNameList">
                    <el-select v-model="ruleForm.areaNameList" placeholder="区域" filterable style="width: 300px;"
                        clearable multiple collapse-tags>
                        <el-option :label="item" :value="item" v-for="(item, index) in quyuList" :key="index" />
                    </el-select>
                </el-form-item>
                <el-form-item label="公告标题:" prop="title">
                    <el-input v-model="ruleForm.title" clearable maxLength="20" placeholder="公告标题"
                        style="width: 300px;"></el-input>
                </el-form-item>
                <el-form-item label="公告内容:" prop="content">
                    <YhQuillEditor :value.sync="ruleForm.content" :maxCharacters="99999" />
                </el-form-item>
                <el-form-item label="有效期:" prop="age">
                    <dateRange :startDate.sync="ruleForm.startTime" :endDate.sync="ruleForm.endTime" class="publicCss"
                        style="width: 300px;" v-if="dialogVisible1" />
                </el-form-item>
                <el-form-item label="发布时间:" prop="publishTime">
                    <el-date-picker v-model="ruleForm.publishTime" type="datetime" placeholder="选择日期时间"
                        style="width: 300px;" value-format="yyyy-MM-dd HH:mm:ss" />
                </el-form-item>
                <el-form-item label="每天看标识:" prop="everyDayReadMark">
                  <el-switch v-model="ruleForm.everyDayReadMark" active-text="是" inactive-text="否" />
                </el-form-item>
                <el-form-item label="每天看时间:" prop="timeRanges" v-if="ruleForm.everyDayReadMark">
                  <el-date-picker v-model="ruleForm.timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始时间"
                    end-placeholder="结束时间" :picker-options="pickerOptions" style="width: 300px;"
                    :value-format="'yyyy-MM-dd'" @change="changeTime">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="过期读标识:" prop="expireReadMark">
                  <el-switch v-model="ruleForm.expireReadMark" active-text="是" inactive-text="否" />
                </el-form-item>
            </el-form>
            <span slot="footer" style="display: flex;justify-content: center;gap: 10px;">
                <el-button @click="() => { dialogVisible = false; dialogVisible1 = false }">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')">确 定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="预览" :visible.sync="viewVisible" width="50%" v-dialogDrag :lose-on-click-modal="false">
            <div v-html="content" style="width: 100%;height: 500px;overflow: auto;"></div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'
import { GetPageBaseMenuNoticeAsync, GetBaseMenuNoticeInfos, AddOrUpdateBaseMenuAreaNoticeSetInfo, getAreaSetList, DelBaseMenuAreaNoticeSetInfo } from '@/api/profit/orderfood'
const tableCols = [
    { width: 'auto', align: 'center', prop: 'areaName', label: '区域', formatter: (row) => row.areaNameList ? row.areaNameList.join(',') : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'title', label: '公告标题', },
    { width: 'auto', align: 'center', prop: 'content', label: '公告内容', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'startTime', label: '有效期', formatter: (row) => row.startTime && row.endTime ? `${dayjs(row.startTime).format('YYYY-MM-DD')} 至 ${dayjs(row.endTime).format('YYYY-MM-DD')}` : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'publishTime', label: '发布时间', },
    { width: '90', align: 'center', prop: 'everyDayReadMarkName', label: '每天看标识' },
    { width: '90', align: 'center', prop: 'expireReadMarkName', label: '过期读标识' },
    { width: 'auto', align: 'center', prop: 'browseQuantity', label: '查看人数', },
    { width: 'auto', align: 'center', prop: 'statusName', label: '状态', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '登记人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '登记时间', },
]

const statusList = [
    { label: '未开始', value: 0 },
    { label: '生效', value: 1 },
    { label: '失效', value: 2 },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, YhQuillEditor,
    },
    data() {
        return {
            content: '',
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
            },
            statusList,
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            dialogVisible: false,
            viewVisible: false,
            ruleForm: {
              everyDayReadMark: false,
              expireReadMark: false,
              everyDayReadStartTime: null,
              everyDayReadEndTime: null,
              timeRanges: [],
            },
            dialogVisible1: false,
            rules: {
                areaNameList: [{ required: true, message: '请选择区域', trigger: 'change' }],
                title: [{ required: true, message: '请输入公告标题', trigger: 'blur' }],
                content: [{ required: true, message: '请输入公告内容', trigger: 'blur' }],
                startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
                endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
                timeRanges: [{ required: true, message: '请选择每天看时间', trigger: 'change' }],
                publishTime: [{ required: true, message: '请选择发布时间', trigger: 'change' }]
            }
        }
    },
    async mounted() {
        this.getquyu()
        await this.getList()
    },
    methods: {
        async changeTime(e) {
          this.ruleForm.everyDayReadStartTime = e ? e[0] : null
          this.ruleForm.everyDayReadEndTime = e ? e[1] : null
        },
        viewContent(content) {
            this.content = content
            this.viewVisible = true
        },
        handleDelete(id) {
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await DelBaseMenuAreaNoticeSetInfo({ id });
                if (!success) return
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
                await this.getList();
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        submitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    const propsToDelete = ['timeRanges']
                    if (!this.ruleForm.everyDayReadMark) {
                      propsToDelete.push('everyDayReadStartTime', 'everyDayReadEndTime')
                    }
                    propsToDelete.forEach(key => delete this.ruleForm[key])
                    const { success } = await AddOrUpdateBaseMenuAreaNoticeSetInfo(this.ruleForm);
                    if (!success) return
                    this.$message.success('保存成功');
                    this.getList();
                    this.dialogVisible = false;
                    this.dialogVisible1 = false
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        async getquyu() {
            const res = await getAreaSetList({ getLevel: 1 });
            if (!res.success) {
                return
            }
            this.quyuList = res.data;
        },
        async handleClick(isEdit, row) {
            this.dialogVisible = true
            this.ruleForm = {}
            if (isEdit) {
                const { data } = await GetBaseMenuNoticeInfos({ id: row.id });
                data.everyDayReadMark = !!data?.everyDayReadMark
                data.expireReadMark = !!data?.expireReadMark
                data.timeRanges = data.everyDayReadStartTime &&  data.everyDayReadEndTime ? [data.everyDayReadStartTime, data.everyDayReadEndTime] : []
                this.$set(this, 'ruleForm', data)
            } else {
                this.$set(this, 'ruleForm', {})
            }
            this.$nextTick(() => {
                this.dialogVisible1 = true
                this.$refs.ruleForm.clearValidate()
            })
            this.isEdit = isEdit
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetPageBaseMenuNoticeAsync(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 300px;
        margin: 0 5px 5px 0px;
    }
}
</style>
