<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" ref="refruleForm" label-width="160px" class="demo-ruleForm">
        <div class="section-title">邮政收银系统</div>
        <el-form-item label="订单数：" prop="postOrderCount">
          <inputNumberYh v-model="ruleForm.postOrderCount" :placeholder="'订单数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="金额：" prop="postAmount">
          <inputNumberYh v-model="ruleForm.postAmount" :fixed="2" :placeholder="'金额'" class="publicCss" />
        </el-form-item>
        <el-form-item label="邮政系统差异：" prop="postSystemDiff">
          <inputNumberYh v-model="ruleForm.postSystemDiff" :fixed="2" :placeholder="'邮政系统差异'" class="publicCss" />
        </el-form-item>
        <div class="section-title section-title-with-top">我司收款码</div>
        <el-form-item label="订单数：" prop="companyQrOrderCount">
          <inputNumberYh v-model="ruleForm.companyQrOrderCount" :placeholder="'订单数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="金额：" prop="companyQrAmount">
          <inputNumberYh v-model="ruleForm.companyQrAmount" :fixed="2" :placeholder="'金额'" class="publicCss" />
        </el-form-item>
        <div class="section-title section-title-with-top">餐盒（保税）</div>
        <el-form-item label="数量：" prop="boxBondedQty">
          <inputNumberYh v-model="ruleForm.boxBondedQty" :placeholder="'数量'" class="publicCss"
            @input="calculateBondedAmount" />
        </el-form-item>
        <el-form-item label="单价：" prop="boxBondedPrice">
          <inputNumberYh v-model="ruleForm.boxBondedPrice" :fixed="2" :placeholder="'单价'" class="publicCss"
            @input="calculateBondedAmount" />
        </el-form-item>
        <el-form-item label="金额：" prop="boxBondedAmount">
          {{ ruleForm.boxBondedAmount }}
        </el-form-item>
        <div class="section-title section-title-with-top">餐盒（昀晗）</div>
        <el-form-item label="数量：" prop="boxYunhanQty">
          <inputNumberYh v-model="ruleForm.boxYunhanQty" :placeholder="'数量'" class="publicCss"
            @input="calculateYunhanAmount" />
        </el-form-item>
        <el-form-item label="单价：" prop="boxYunhanPrice">
          <inputNumberYh v-model="ruleForm.boxYunhanPrice" :fixed="2" :placeholder="'单价'" class="publicCss"
            @input="calculateYunhanAmount" />
        </el-form-item>
        <el-form-item label="金额：" prop="boxYunhanAmount">
          {{ ruleForm.boxYunhanAmount }}
        </el-form-item>
        <div class="section-title section-title-with-top">餐盒（棒杰）</div>
        <el-form-item label="数量：" prop="boxBangjieQty">
          <inputNumberYh v-model="ruleForm.boxBangjieQty" :placeholder="'数量'" class="publicCss"
            @input="calculateBangjieAmount" />
        </el-form-item>
        <el-form-item label="单价：" prop="boxBangjiePrice">
          <inputNumberYh v-model="ruleForm.boxBangjiePrice" :fixed="2" :placeholder="'单价'" class="publicCss"
            @input="calculateBangjieAmount" />
        </el-form-item>
        <el-form-item label="金额：" prop="boxBangjieAmount">
          {{ ruleForm.boxBangjieAmount }}
        </el-form-item>
        <div class="section-title section-title-with-top">餐盒数据合计</div>
        <el-form-item label="数量合计：" prop="boxTotalQty">
          {{ ruleForm.boxTotalQty }}
        </el-form-item>
        <el-form-item label="合计金额：" prop="boxTotalAmount">
          {{ ruleForm.boxTotalAmount }}
        </el-form-item>
        <div class="section-title section-title-with-top">其他收入</div>
        <el-form-item label="其他收入：" prop="otherIncome">
          <inputNumberYh v-model="ruleForm.otherIncome" :fixed="2" :placeholder="'其他收入'" class="publicCss" />
        </el-form-item>
        <el-form-item label="备注：" prop="remarks">
          <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 4 }" placeholder="请输入内容" maxlength="100"
            show-word-limit v-model="ruleForm.remarks" class="publicCss">
          </el-input>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div class="button-container">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { warehouseCanteenIncomeDetailSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
export default {
  name: 'lcanteenIncomeEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      ruleForm: {
        // 邮政收银系统
        postOrderCount: null,
        postAmount: null,
        postSystemDiff: null,
        // 我司收款码
        companyQrOrderCount: null,
        companyQrAmount: null,
        // 餐盒（保税）
        boxBondedQty: null,
        boxBondedPrice: null,
        // 餐盒（昀晗）
        boxYunhanQty: null,
        boxYunhanPrice: null,
        boxYunhanAmount: null,
        // 餐盒（棒杰）
        boxBangjieQty: null,
        boxBangjiePrice: null,
        boxBangjieAmount: null,
        // 餐盒数据合计
        boxTotalQty: null,
        boxTotalAmount: null,
        // 其他收入
        otherIncome: null,
        // 备注
        remarks: ''
      },
      rules: {
        postOrderCount: [
          { required: true, message: '请输入订单数', trigger: 'blur' },
          { type: 'integer', min: 0, message: '请输入有效的整数', trigger: 'blur' }
        ],
        postAmount: [
          { required: true, message: '请输入金额', trigger: 'blur' },
          { type: 'number', min: 0, message: '请输入有效的金额', trigger: 'blur' }
        ],
        postSystemDiff: [
          { required: true, message: '请输入邮政系统差异', trigger: 'blur' },
          { type: 'number', message: '请输入有效的金额', trigger: 'blur' }
        ],
        companyQrOrderCount: [
          { required: true, message: '请输入订单数', trigger: 'blur' },
          { type: 'integer', min: 0, message: '请输入有效的整数', trigger: 'blur' }
        ],
        companyQrAmount: [
          { required: true, message: '请输入金额', trigger: 'blur' },
          { type: 'number', min: 0, message: '请输入有效的金额', trigger: 'blur' }
        ],
        boxBondedQty: [
          { required: true, message: '请输入数量', trigger: 'blur' },
          { type: 'integer', min: 0, message: '请输入有效的整数', trigger: 'blur' }
        ],
        boxBondedPrice: [
          { required: true, message: '请输入单价', trigger: 'blur' },
          { type: 'number', min: 0, message: '请输入有效的单价', trigger: 'blur' }
        ],
        boxYunhanQty: [
          { required: true, message: '请输入数量', trigger: 'blur' },
          { type: 'integer', min: 0, message: '请输入有效的整数', trigger: 'blur' }
        ],
        boxYunhanPrice: [
          { required: true, message: '请输入单价', trigger: 'blur' },
          { type: 'number', min: 0, message: '请输入有效的单价', trigger: 'blur' }
        ],
        boxBangjieQty: [
          { required: true, message: '请输入数量', trigger: 'blur' },
          { type: 'integer', min: 0, message: '请输入有效的整数', trigger: 'blur' }
        ],
        boxBangjiePrice: [
          { required: true, message: '请输入单价', trigger: 'blur' },
          { type: 'number', min: 0, message: '请输入有效的单价', trigger: 'blur' }
        ],
        otherIncome: [
          { required: true, message: '请输入其他收入', trigger: 'blur' },
          { type: 'number', min: 0, message: '请输入有效的金额', trigger: 'blur' }
        ],
        remarks: [
          { max: 100, message: '备注内容不能超过100个字符', trigger: 'blur' }
        ]
      }
    }
  },
  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };
    console.log(this.editInfo, 'editInfo');
    // 初始化计算
    this.calculateYunhanAmount();
    this.calculateBangjieAmount();
    this.calculateTotalBoxAmount();
  },
  methods: {
    // 计算保税餐盒金额
    calculateBondedAmount() {
      const quantity = this.ruleForm.boxBondedQty || 0;
      const unitPrice = this.ruleForm.boxBondedPrice || 0;
      this.ruleForm.boxBondedAmount = (quantity * unitPrice).toFixed(2);
      this.calculateTotalBoxAmount();
    },
    // 计算昀晗餐盒金额
    calculateYunhanAmount() {
      const quantity = this.ruleForm.boxYunhanQty || 0;
      const unitPrice = this.ruleForm.boxYunhanPrice || 0;
      this.ruleForm.boxYunhanAmount = (quantity * unitPrice).toFixed(2);
      this.calculateTotalBoxAmount();
    },
    // 计算棒杰餐盒金额
    calculateBangjieAmount() {
      const quantity = this.ruleForm.boxBangjieQty || 0;
      const unitPrice = this.ruleForm.boxBangjiePrice || 0;
      this.ruleForm.boxBangjieAmount = (quantity * unitPrice).toFixed(2);
      this.calculateTotalBoxAmount();
    },
    // 计算餐盒数据合计
    calculateTotalBoxAmount() {
      const yunhanAmount = parseFloat(this.ruleForm.boxYunhanAmount) || 0;
      const bangjieAmount = parseFloat(this.ruleForm.boxBangjieAmount) || 0;
      const bondedAmount = parseFloat(this.ruleForm.boxBondedAmount) || 0;
      this.ruleForm.boxTotalAmount = (yunhanAmount + bangjieAmount + bondedAmount).toFixed(2);
      this.calculateTotalBoxQty();
    },
    // 计算餐盒数据合计数量
    calculateTotalBoxQty() {
      const boxBondedQty = parseFloat(this.ruleForm.boxBondedQty) || 0;
      const boxYunhanQty = parseFloat(this.ruleForm.boxYunhanQty) || 0;
      const boxBangjieQty = parseFloat(this.ruleForm.boxBangjieQty) || 0;
      this.ruleForm.boxTotalQty = (boxBondedQty + boxYunhanQty + boxBangjieQty).toFixed(0);
    },
    cancellationMethod() {
      this.$emit('cancellationMethod');
    },
    async submitForm(formName) {
      try {
        const valid = await this.$refs[formName].validate();
        if (valid) {
          this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing");
          const { success, msg } = await warehouseCanteenIncomeDetailSubmit(this.ruleForm);

          if (success) {
            this.$message.success(msg || '保存成功');
            this.$emit("search");
          } else {
            this.$message.error(msg || '保存失败');
          }
        }
      } catch (error) {
        console.error('表单提交失败:', error);
        this.$message.error('保存失败，请重试');
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    }
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  margin-left: 20px;
  margin-bottom: 15px;
}

.section-title-with-top {
  margin-top: 20px;
}

.button-container {
  display: flex;
  justify-content: end;
  margin: auto 30px 20px 0;
}
</style>
