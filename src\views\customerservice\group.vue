<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" :before-leave="beforeLeave" style="height:94%;">
            <el-tab-pane name="switch">
                <span slot="label">
                    <el-switch v-model="switchshow" :disabled="switchshowPermission" @change="changeShowgroup"
                        active-text="售后管理" inactive-text="售前管理">
                    </el-switch>
                </span>
            </el-tab-pane>
            <el-tab-pane v-if="!switchshowSqSh" label="分组管理(售前)" name="tab0" style="height: 100%;">
                <group1 :filter="filter" ref="group1" style="height: 100%;" @callBackInfo="handleInfo" />
            </el-tab-pane>
            <el-tab-pane v-if="!switchshowSqSh" label="咨询数据导入(售前)" name="tab1" style="height: 100%;">
                <inquirs :filter="filter" ref="inquirs" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="!switchshowSqSh" label="组效率统计(售前)" name="tab2" style="height: 100%;">
                <groupinquirsstatistics :filter="filter" ref="groupinquirsstatistics" style="height: 100%;"
                    :partInfo="infoBool" />
            </el-tab-pane>
            <el-tab-pane v-if="!switchshowSqSh" label="店效率统计(售前)" ref="tab4" name="tab4" style="height: 100%;">
                <shopinquirsstatistics :filter="filter" ref="shopinquirsstatistics" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="!switchshowSqSh" label="个人效率统计(售前)" name="tab3" style="height: 100%;">
                <inquirsstatistics :filter="filter" ref="inquirsstatistics" style="height: 100%;"
                    :partInfo="infoBool" />
            </el-tab-pane>
            <el-tab-pane v-if="!switchshowSqSh" label="未匹配咨询数据(售前)" name="tab5" style="height: 100%;">
                <txinquirsno :filter="filter" ref="txinquirsno" style="height: 100%;" />
            </el-tab-pane>


            <el-tab-pane v-if="switchshowSqSh" label="分组管理(售后)" name="tab10" style="height: 100%;">
                <tbshgroup1 :filter="filter" ref="tbshgroup1" style="height: 100%;" @callBackInfoH="handleInfoH" />
            </el-tab-pane>
            <el-tab-pane v-if="switchshowSqSh" label="咨询数据导入(售后)" name="tab11" style="height: 100%;">
                <tbshinquirs :filter="filter" ref="tbshinquirs" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="switchshowSqSh" label="组效率统计(售后)" name="tab12" style="height: 100%;">
                <tbshgroupinquirsstatistics :filter="filter" ref="tbshgroupinquirsstatistics" style="height: 100%;"
                    :partInfo="infoBoolH" />
            </el-tab-pane>
            <el-tab-pane v-if="switchshowSqSh" label="店效率统计(售后)" ref="tab14" name="tab14" style="height: 100%;">
                <tbshshopinquirsstatistics :filter="filter" ref="tbshshopinquirsstatistics" style="height: 100%;"
                    :partInfo="infoBoolH" />
            </el-tab-pane>
            <el-tab-pane v-if="switchshowSqSh" label="个人效率统计(售后)" name="tab13" style="height: 100%;">
                <tbshinquirsstatistics :filter="filter" ref="tbshinquirsstatistics" style="height: 100%;"
                    :partInfo="infoBoolH" />
            </el-tab-pane>
            <el-tab-pane v-if="switchshowSqSh" label="店铺组效率(售后)" name="tab15" style="height: 100%;">
                <tbshinquirsstatisticsmonth :filter="filter" ref="tbshinquirsstatisticsmonth" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="switchshowSqSh" label="未匹配咨询数据(售后)" name="tab16" style="height: 100%;">
                <txinquirsno2 :filter="filter" ref="txinquirsno2" style="height: 100%;" />
            </el-tab-pane>


        </el-tabs>
    </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";
import group1 from '@/views/customerservice/group1'
import inquirs from '@/views/customerservice/inquirs'
import groupinquirsstatistics from '@/views/customerservice/groupinquirsstatistics'
import inquirsstatistics from '@/views/customerservice/inquirsstatistics'
import shopinquirsstatistics from '@/views/customerservice/shopinquirsstatistics'
import txinquirsno from '@/views/customerservice/txinquirsno'

import tbshgroup1 from '@/views/customerservice/tbshgroup1'
import tbshinquirs from '@/views/customerservice/tbshinquirs'
import tbshgroupinquirsstatistics from '@/views/customerservice/tbshgroupinquirsstatistics'
import tbshshopinquirsstatistics from '@/views/customerservice/tbshshopinquirsstatistics'
import tbshinquirsstatistics from '@/views/customerservice/tbshinquirsstatistics'
import tbshinquirsstatisticsmonth from '@/views/customerservice/tbshinquirsstatisticsmonth'
import txinquirsno2 from '@/views/customerservice/txinquirsno2'

export default {
    name: "Users",
    components: {
        MyContainer,
        group1, inquirs, groupinquirsstatistics, inquirsstatistics, shopinquirsstatistics, txinquirsno,
        tbshgroup1, tbshinquirs, tbshgroupinquirsstatistics, tbshshopinquirsstatistics, tbshinquirsstatistics, tbshinquirsstatisticsmonth, txinquirsno2
    },
    data() {
        return {
            that: this,
            pageLoading: '',
            filter: {
            },
            shopList: [],
            userList: [],
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            activeName: 'tab0',

            //默认展示售前 true售前，false售后
            switchshow: false,
            switchshowSqSh: false,
            switchshowPermission: false,
            infoBool: false,//是否包含离组
            infoBoolH: false,
        };
    },
    mounted() {
        window.showtab4 = this.showtab4
        window.showtab13 = this.showtab13
    },
    methods: {
        showtab4() {
            this.activeName = "tab3"
        },
        showtab13() {
            this.activeName = "tab13"
        },
        beforeLeave(visitName, currentName) {
            if (visitName == "switch")
                return false;
        },
        changeShowgroup() {
            if (!this.switchshow) {
                this.activeName = 'tab0';
                this.switchshowSqSh = false;
            }
            else {
                this.activeName = 'tab10';
                this.switchshowSqSh = true;
            }
        },
        handleInfo(data) {
            this.infoBool = data
        },
        handleInfoH(data) {
            this.infoBoolH = data
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
