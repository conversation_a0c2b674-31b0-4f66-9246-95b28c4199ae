<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" :before-leave="beforeLeave" style="height:94%;">
            <el-tab-pane name="switch">
                <span slot="label">
                    <el-switch v-model="switchshow" @change="changeShowgroup" :disabled="isAllcheck" active-text="售后管理"
                        inactive-text="售前管理">
                    </el-switch>
                </span>
            </el-tab-pane>
            <!-- 售前 -->
            <el-tab-pane v-if="showSq" label="分组管理(售前组)" name="tab30" style="height: 100%;" lazy>
                <sqgroup :filter="filter" ref="sqgroup" style="height: 100%;"  @callBackInfo="handleInfo"/>
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="咨询数据导入(售前组)" name="tab31" style="height: 100%;" lazy>
                <sqinquirs :filter="filter" ref="sqinquirs" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="服务数据导入(售前组)" name="tab36" style="height: 100%;" lazy>
                <sqinquirskf :filter="filter" ref="sqinquirskf" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="组效率统计(售前组)" name="tab32" style="height: 100%;" lazy>
                <sqgroupinquirsstatistics :filter="filter" ref="sqgroupinquirsstatistics" style="height: 100%;" :partInfo="infoBool" />
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="店效率统计(售前组)" name="tab33" style="height: 100%;" lazy>
                <sqshopinquirsstatistics :filter="filter" ref="sqshopinquirsstatistics" style="height: 100%;"  :partInfo="infoBool"/>
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="个人效率统计(售前组)" name="tab34" style="height: 100%;">
                <sqinquirsstatistics :filter="filter" ref="sqinquirsstatistics" style="height: 100%;" :partInfo="infoBool"/>
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="服务数据统计(售前组)" name="tab37" style="height: 100%;">
                <sqinquirskfstatistics :filter="filter" ref="sqinquirskfstatistics" style="height: 100%;" :partInfo="infoBool"/>
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="店铺组效率统计" name="tab35" style="height: 100%;" lazy>
                <inquirsstatisticsmonth :filter="filter" ref="inquirsstatisticsmonth" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="未匹配咨询数据" name="tab38" style="height: 100%;" lazy>
                <douyininquirsno ref="douyininquirsno" style="height: 100%;" />
            </el-tab-pane>

            <!-- 售后 -->
            <el-tab-pane v-if="showSh" label="分组管理(售后组)" name="tabsh40" style="height: 100%;" lazy>
                <shgroup :filter="filter" ref="shgroup" style="height: 100%;" @callBackInfoH="handleInfoH"/>
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="咨询数据导入(售后组)" name="tab41" style="height: 100%;" lazy>
                <shinquirs :filter="filter" ref="shinquirs" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="服务数据导入(售后组)" name="tab46" style="height: 100%;" lazy>
                <shinquirskf :filter="filter" ref="shinquirskf" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="组效率统计(售后组)" name="tab42" style="height: 100%;" lazy>
                <shgroupinquirsstatistics :filter="filter" ref="shgroupinquirsstatistics" style="height: 100%;" :partInfo="infoBoolH" />
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="店效率统计(售后组)" name="tab43" style="height: 100%;" lazy>
                <shshopinquirsstatistics :filter="filter" ref="shshopinquirsstatistics" style="height: 100%;" :partInfo="infoBoolH" />
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="个人效率统计(售后组)" name="tab44" style="height: 100%;">
                <shinquirsstatistics :filter="filter" ref="shinquirsstatistics" style="height: 100%;" :partInfo="infoBoolH" />
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="服务数据统计(售后组)" name="tab47" style="height: 100%;">
                <shinquirskfstatistics :filter="filter" ref="shinquirskfstatistics" style="height: 100%;" :partInfo="infoBoolH" />
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="店铺组效率统计" name="tab45" style="height: 100%;" lazy>
                <inquirsstatisticsmonth :filter="filter" ref="inquirsstatisticsmonth" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="未匹配咨询数据" name="tab48" style="height: 100%;" lazy>
                <douyininquirsno1 ref="douyininquirsno1" style="height: 100%;" />
            </el-tab-pane>
            
            <el-tab-pane label="合并店效率统计" name="tab50" style="height: 100%;" lazy>
                <mergeEfficiencyStatistics ref="mergeEfficiencyStatistics" style="height: 100%;" :partInfo2="infoBool2"/>
            </el-tab-pane>
            
        </el-tabs>
    </my-container>
</template>
<script>
import checkPermission from '@/utils/permission';
import MyContainer from "@/components/my-container";

import sqgroup from '@/views/customerservice/douyin/sq/sqgroup';
import sqinquirs from '@/views/customerservice/douyin/sq/sqinquirs';
import sqinquirsstatistics from '@/views/customerservice/douyin/sq/sqinquirsstatistics';
import sqgroupinquirsstatistics from '@/views/customerservice/douyin/sq/sqgroupinquirsstatistics';
import sqshopinquirsstatistics from '@/views/customerservice/douyin/sq/sqshopinquirsstatistics';
import sqinquirskf from '@/views/customerservice/douyin/sq/sqinquirskf';
import sqinquirskfstatistics from '@/views/customerservice/douyin/sq/sqinquirskfstatistics';
import douyininquirsno from '@/views/customerservice/douyin/douyininquirsno';

import inquirsstatisticsmonth from '@/views/customerservice/douyin/inquirsstatisticsmonth';
import mergeEfficiencyStatistics from '@/views/customerservice/douyin/mergeEfficiencyStatistics';

import shgroup from '@/views/customerservice/douyin/sh/shgroup';
import shinquirs from '@/views/customerservice/douyin/sh/shinquirs';
import shinquirsstatistics from '@/views/customerservice/douyin/sh/shinquirsstatistics';
import shgroupinquirsstatistics from '@/views/customerservice/douyin/sh/shgroupinquirsstatistics';
import shshopinquirsstatistics from '@/views/customerservice/douyin/sh/shshopinquirsstatistics';
import shinquirskf from '@/views/customerservice/douyin/sh/shinquirskf';
import shinquirskfstatistics from '@/views/customerservice/douyin/sh/shinquirskfstatistics';
import douyininquirsno1 from '@/views/customerservice/douyin/douyininquirsno1';


export default {
    name: "Users",
    provide() {
        return {
            reload: this.reload
        }
    },
    components: {
        MyContainer,
        sqgroup, sqinquirs, sqinquirsstatistics, sqgroupinquirsstatistics, sqshopinquirsstatistics, sqinquirskf, sqinquirskfstatistics,douyininquirsno,
        inquirsstatisticsmonth,
        shgroup, shinquirs, shinquirsstatistics, shgroupinquirsstatistics, shshopinquirsstatistics, shinquirskf, shinquirskfstatistics,douyininquirsno1,
        mergeEfficiencyStatistics,
    },
    data() {
        return {
            that: this,
            pageLoading: '',
            filter: {
            },
            shopList: [],
            userList: [],
            groupList: [],
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            activeName: 'tab34',
            //判断权限用
            IsSq: true,

            switchshow: false,
            //默认展示售前
            showSq: true,
            //默认展示售前
            showSh: false,
            isAllcheck: false,
            isShowTj: false,
            infoBool:false,//是否包含离组
            infoBoolH:false,
            infoBool2:false,
        };
    },
    mounted() {
        window.showtab34 = this.showtab34
        window.showtab44 = this.showtab44
        window.showtab37 = this.showtab37
        window.showtab47 = this.showtab47
    },
    methods: {
        showtab34() {
            this.activeName = "tab34"
        },
        showtab44() {
            this.activeName = "tab44"
        },
        showtab37() {
            this.activeName = "tab37"
        },
        showtab47() {
            this.activeName = "tab47"
        },
        beforeLeave(visitName, currentName) {
            if (visitName == "switch")
                return false;
        },
        changeShowgroup() {
            if (this.switchshow) {
                this.activeName = 'tab44';
                this.showSh = true;
                this.showSq = false;
            } else {
                this.activeName = 'tab34';
                this.showSh = false;
                this.showSq = true;
            }
        },
        reload() {
            //刷新其他页面的下拉框选项
            this.$refs.inquirssh.setShopSelect();
            this.$refs.groupinquirsstatisticssh.setGroupSelect();
            this.$refs.shopinquirsstatisticssh.setShopSelect();
            this.$refs.inquirsstatisticssh.setGroupSelect();

            this.$refs.inquirsstatisticsmonth.setGroupSelect();
            this.$refs.inquirsstatisticsmonth.setShopSelect();
        },
        handleInfo(data){
            this.infoBool = data;
            this.infoBool2=data;
        },
        handleInfoH(data){
            this.infoBoolH = data;
              this.infoBool2=data;
        }
    },


};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
