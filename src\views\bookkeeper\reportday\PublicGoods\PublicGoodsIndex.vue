<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane v-if="checkPermission('PublicGoodsCurrent')" label="当前" name="first1" style="height: 100%">
        <PublicGoods ref="PublicGoodsCurrent" style="height: 100%" :is-pbl="true" />
      </el-tab-pane>
      <el-tab-pane v-if="checkPermission('PublicGoodsHistory')" label="历史" name="first2" style="height: 100%">
        <PublicGoods ref="PublicGoodsHistory" style="height: 100%" :is-pbl="false" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>
<script>
import MyContainer from '@/components/my-container'
import PublicGoods from '@/views/bookkeeper/reportday/PublicGoods/PublicGoods.vue'
export default {
  name: 'PublicGoodsIndex',
  components: {
    MyContainer, PublicGoods
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: 'first1'
    }
  },
  async mounted() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped></style>
