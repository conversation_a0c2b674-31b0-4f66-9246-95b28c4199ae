<template>
  <MyContainer>
    <template #header>
      <el-form :inline="true" ref="topForm" :model="queryEnum" class="demo-ruleForm">
          <el-form-item prop="timeRanges">
              <el-date-picker v-model="queryEnum.timeRanges" type="daterange" unlink-panels range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" style="width: 250px;margin-right: 5px;"
              :clearable="true" :value-format="'yyyy-MM-dd'">
              </el-date-picker>
          </el-form-item>
          <el-form-item prop="financialType">
            <el-select v-model="queryEnum.financialType" placeholder="财务类型" clearable filterable>
              <el-option v-for="item in financialTypeList" :key="item" :label="item" :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="flowType">
            <el-select v-model="queryEnum.flowType" placeholder="明细类型" clearable filterable>
              <el-option v-for="item in ['增加','减少']" :key="item" :label="item" :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="checkStatus">
              <el-select v-model="queryEnum.checkStatus" placeholder="核对状态" clearable filterable>
                <el-option v-for="item in checkTypeList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          <el-form-item>
              <el-button type="primary" @click="getList('search')">查询</el-button>
              <el-dropdown style="box-sizing: border-box; margin:0 10px;" size="mini" split-button @click="onImport"
                type="primary" icon="el-icon-share" @command="handleCommand"> 导入
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">下载模版</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button type="primary" @click="onExport()">导出</el-button>
              <el-button type="primary" @click="batchCheck()">批量核对</el-button>
              <el-button type="primary" @click="batchFinance()" v-if="checkPermission('incomePayDetail_batchEdit_financialType')">批量修改财务类型</el-button>
          </el-form-item>
      </el-form>
    </template>

    <template>
      <vxetablebase :id="'LeftOcean20241108'" :tablekey="'LeftOcean20241108'" :tableData='tableData' :tableCols='tableCols'
        @sortchange='sortchange' @select='selectchange' :loading='loading' :border='true' :that="that" ref="vxetable" :showsummary='true'
        :summaryarry="summaryarry">
      </vxetablebase>
    </template>
    
    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="getList" />
    </template>

    <!-- 修改核对状态 -->
    <el-dialog title="修改核对状态"  custom-class="formCenter" center :visible.sync="checkVisible" width="20%" @close="checkClose"
    v-dialogDrag>

    <el-form ref="" @submit.native.prevent>
      <el-form-item prop="checkStatu" >
          <el-radio-group v-model="checkStatu">
              <el-radio :label="2">已核对</el-radio>
              <el-radio :label="3">需调整</el-radio>
          </el-radio-group>
      </el-form-item>
    </el-form>

    <template slot="footer" >
      <el-button @click="checkVisible = false;checkStatu = null">取消</el-button>
      <el-button @click="checkSaveEdit" type="primary" >保存</el-button>
    </template>
  </el-dialog>

  <!-- 修改财务类型 -->
  <el-dialog title="修改财务类型" custom-class="formCenter" center :visible.sync="financeVisible" width="20%"
    v-dialogDrag>

    <el-form ref="" @submit.native.prevent class="financeForm">
      <el-form-item prop="financeStatu" >
          <el-select v-model="financeStatu" placeholder="财务类型" clearable>
              <el-option v-for="item in financialTypeList.slice(0,3)" :key="item" :label="item" :value="item">
              </el-option>
            </el-select>
      </el-form-item>
    </el-form>

    <template slot="footer" >
      <el-button @click="financeVisible = false">取消</el-button>
      <el-button @click="financeSaveEdit" type="primary" >保存</el-button>
    </template>
  </el-dialog>

  <!-- 查看日志 -->
  <el-dialog title="操作日志" center :visible.sync="logVisible" 
    v-dialogDrag>
      <vxetablebase 
         :id="'LeftOceanLog20241108'" 
         :tablekey="'LeftOceanLog20241108'" 
         :tableData='logData' 
         :tableCols='logTableCols'
          :loading='logLoading' 
          :border='true'  
          :that="that" 
          height="440px" 
          ref="logtable"  
          :showsummary='false' 
           @sortchange='logSortchange'
          >
      </vxetablebase>
      <my-pagination ref="logPage" :total="logTotal"  @get-page="logListGet()"/>
  </el-dialog>

  <!-- 导入 -->
  <el-dialog title="导入数据" :visible.sync="uploadVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 75px;">
        <!-- <el-date-picker style="width: 200px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
          placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
        </el-date-picker> -->
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="uploadVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import dayjs from 'dayjs';
import {PageWarehouseListAsync,ImportWarehouseAsync,GetBusinessTypeList,UpdateWarehouseAsync,UpdateWarehouseByCheckStatusAsync,PageWarehouseFeesLogAsync,ExportZuoHai } from "@/api/bookkeeper/crossBorderV2.js";

  const tableCols =[
    { istrue: true,  type: "checkbox" },
    { istrue: true, sortable: 'custom', prop: 'customerName', label: '客户名称', width: 'auto' },
    { istrue: true, sortable: 'custom', prop: 'customerID', label: '客户编号', width: 'auto' },
    { istrue: true, sortable: 'custom', prop: 'flowType', label: '明细类型', width: '90' },
    { istrue: true, sortable: 'custom', prop: 'amount', label: '变动金额', width: '90' },
    { istrue: true, sortable: 'custom', prop: 'accountBalance', label: '账户余额', width: '90' },
    { istrue: true, sortable: 'custom', prop: 'remark', label: '备注', width: 'auto' },
    { istrue: true, sortable: 'custom', prop: 'timeOfOccurrence', label: '添加时间', width: 'auto' },
    { istrue: true, sortable: 'custom', prop: 'financialType', label: '财务类型', width: '90' },
    { istrue: true, sortable: 'custom', prop: 'checkStatus', label: '核对状态', width: '90',formatter:(row,that)=>that.checkTypeList.find((item)=>item.value == Number(row.checkStatus)).label  },
    {
      type: 'button', label: '操作', width: '150px', btnList: [
          { label: "编辑",handle: (that, row) => {that.checkVisible = true;that.currentRow = row;that.checkStatu = row.checkStatus;} },
          { label: "查看日志",handle: (that, row) => {that.logVisible = true;that.currentId = row.id;that.logListGet('search')} },
      ]
    },
  ]
const logTableCols =[
{ istrue: true, sortable: 'custom', prop: 'createdTime', label: '时间', width: 'auto' },
{ istrue: true, sortable: 'custom', prop: 'remark', label: '事件', width: 'auto' },
{ istrue: true, sortable: 'custom', prop: 'createdUserName', label: '操作人', width: 'auto' },
]

export default {
  name: 'LeftOcean',
  components: { vxetablebase, MyContainer,  },
  data() {
    return {
      that: this,
      queryEnum: {
        timeRanges: [],
        orderBy: '',
        isAsc: true,
        warehouseType:5,
      },
      tableData: [
      ],
      tableCols: tableCols,
      loading: false,
      summaryarry: {},
      total: 0,
      selids:[],
      selTimes:[],
      financialTypeList:[
        '充值','收入','支出','其他'
      ],
      checkTypeList:[
        {
          value:1,
          label:'未核对'
        },
        {
          value:2,
          label:'已核对'
        },
        {
          value:3,
          label:'需调整'
        },
      ],
      businessTypeList:[],

      currentRow:null,//当前行数据
      currentId:null,//当前行id
      //导入
      uploadVisible: false,//导入弹窗
      fileList: [],//上传文件列表
      uploadLoading: false,//上传按钮loading
      fileparm: {},//上传文件参数

      //修改核对状态
      checkVisible:false,
      checkStatu:null,

      //修改财务类型
      financeVisible:false,
      financeStatu:'',

      //查看日志
      logVisible:false,
      logData:[],
      logTableCols:logTableCols,
      logLoading:false,
      logTotal:0,
      logEnum:{
          orderBy: '',
          isAsc: true,
      }
    };
  },
  async mounted() {
      // 获取业务类型列
    // const {data}  =  await GetBusinessTypeList()
    // this.businessTypeList = data
    await this.getList()

  },
  methods: {
      async getList(type) {
          if (type == 'search') {
          this.$refs.pager.setPage(1)
          }
          
          let pager = this.$refs.pager.getPager()
          
          let Enum = { ...this.queryEnum };
          if (this.queryEnum.timeRanges) {
              Enum.start = this.queryEnum.timeRanges[0];
              Enum.end = this.queryEnum.timeRanges[1];
          }
          let params = {
              ...pager,
              ...Enum
          }
          this.loading = true
          const { data } = await PageWarehouseListAsync(params)
          this.loading = false
          this.tableData = data.list
          //重置选中的值
          this.selids = []
          this.total = data.total
          this.summaryarry = data.summary
      },
      sortchange({ order, prop }) {
          if (prop) {
          this.queryEnum.orderBy = prop
          this.queryEnum.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
          }
      },

      selectchange(rows,row) {
          this.selids=[];
          this.selTimes = [];
          rows.forEach(f=>{
              this.selids.push(f.id);
              this.selTimes.push(dayjs(f.timeOfOccurrence).format('YYYY-MM-DD'))
          })
          this.selTimes = Array.from(new Set(this.selTimes))
      },
      // 导出
      async onExport(){
          this.loading = true
          let Enum = {...this.queryEnum}
          if (this.queryEnum.timeRanges) {
              Enum.start = this.queryEnum.timeRanges[0];
              Enum.end = this.queryEnum.timeRanges[1];
          }
          const { data } = await ExportZuoHai(Enum)
          this.loading = false
            if(data.success){
                this.$message({ message: data.msg, type: "success" })
            }
      },
      // 导入
      onImport(){
          this.fileList = []
          this.uploadVisible = true;
      },
      async handleCommand(command) {
        switch (command) {
          //下载模版
          case 'a':
            await this.downLoadFile()
            break;
        }
      },
      async downLoadFile() {
        window.open("/static/excel/kj/overseasGodownIncomePayDetail/左海收支明细导入模版.xlsx", "_blank");
        
      },
      async onUploadFile(item) {
          if (!item || !item.file || !item.file.size) {
          this.$message({ message: "请先上传文件", type: "warning" });
          return false;
          }
          this.uploadLoading = true
          const form = new FormData();
          form.append("upfile", item.file);
          form.append("warehouseType", this.queryEnum.warehouseType);
          var res = await ImportWarehouseAsync(form);
          if (res?.success)
          this.$message({ message: "上传成功,正在导入中...", type: "success" });
          this.uploadLoading = false
          this.uploadVisible = false;
      },
      onUploadSuccess(response, file, fileList) {
          fileList.splice(fileList.indexOf(file), 1);
          this.fileList = [];
          this.uploadVisible = false;
      },
      async onUploadChange(file, fileList) {
          this.fileList = fileList;
      },
      onUploadRemove(file, fileList) {
          this.fileList = []
      },
      onSubmitUpload() {
          // if (!this.yearMonthDay) {
          //   this.$message({ message: "请选择日期", type: "warning" });
          //   return false;
          // }
          if (this.fileList.length == 0) {
          this.$message({ message: "请先上传文件", type: "warning" });
          return false;
          }
          this.$refs.upload.submit();
      },
      //修改核对状态 
      checkClose(){
          this.checkStatu = null
          this.currentRow = null
      },
      async checkSaveEdit(){
          // 保存编辑核对状态
        if(!this.checkStatu||this.checkStatu==1){
              this.$message({type: 'warning',message: '请先选择!'});
              return;
        }
        //  取选中值 this.selids 调接口
        // this.checkStatu 调接口保存修改

        let params = {
              warehouseType:this.queryEnum.warehouseType,
              ids:this.currentRow ? this.currentRow.id:this.selids.join(','),
              checkStatus:Number(this.checkStatu),
              isBatch:this.currentRow?false:true,
              calcDate:this.currentRow ? [dayjs(this.currentRow.timeOfOccurrence).format('YYYY-MM-DD')]:this.selTimes,
          }
          const  res=await UpdateWarehouseByCheckStatusAsync(params)
          if(res.success){
              this.$message({type: 'success',message: this.currentRow?'修改成功':'批量修改成功'});
          }
          this.getList()
          this.checkVisible = false
        
      },
      async batchCheck(){
          if (this.selids.length==0) {
              this.$message({type: 'warning',message: '请先选择!'});
              return;
          } 
          this.checkVisible = true
      },

      //修改财务类型
      async financeSaveEdit(){
          // this.financeStatu 调接口保存修改
        if(!this.financeStatu){
              this.$message({type: 'warning',message: '请先选择!'});
              return;
        }
        let params = {
              warehouseType:this.queryEnum.warehouseType,
              ids:this.selids.join(','),
              financialType:this.financeStatu,
              isBatch:true
          }
          const  res=await UpdateWarehouseAsync(params)
          if(res.success){
              this.$message({type: 'success',message: '批量修改成功'});
          }
          this.getList()
          this.financeVisible = false
          this.financeStatu = null
      },
      async batchFinance(){
          if (this.selids.length==0) {
              this.$message({type: 'warning',message: '请先选择!'});
              return;
          } 
          this.financeVisible = true
      },

      //查看日志
      async logListGet(type){
          this.$nextTick(async()=>{
              if(type == 'search'){
              this.$refs.logPage.setPage(1)
          }
          let pager = this.$refs.logPage.getPager()

          let params = {
              warehouse:this.queryEnum.warehouseType,
              masterTableId:this.currentId,
             ...pager,
             ...this.logEnum,
          }
          this.logLoading = true
          const {data}= await PageWarehouseFeesLogAsync(params) //该接口
          this.logLoading = false

          this.logTotal  = data.total
          this.logData = data.list
          })
      },
      logSortchange({ order, prop }) {
          if (prop) {
              this.logEnum.orderBy = prop
              this.logEnum.isAsc = order.indexOf("descending") == -1 ? true : false
              this.logListGet()
          }
      },
  }
};
</script>

<style lang="scss" scoped>
::v-deep .formCenter .el-form-item{
  display: flex;
  justify-content: center;
}


</style>
