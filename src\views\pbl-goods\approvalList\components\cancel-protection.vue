<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss"
                    startPlaceholder="申请开始时间" endPlaceholder="申请结束时间" />
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable>
                    <el-option label="待审批" :value="0" />
                    <el-option label="同意" :value="1" />
                    <el-option label="拒绝" :value="2" />
                </el-select>
                <el-select filterable v-model="ListInfo.groupId" collapse-tags clearable placeholder="申请组"
                    class="publicCss">
                    <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-input v-model.trim="ListInfo.styleCode" placeholder="系列编码" maxlength="50" clearable
                    class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="handleAgree(ids, 1, 'batch')">批量同意</el-button>
                <el-button type="primary" @click="handleAgree(ids, 2, 'batch')">批量拒绝</el-button>
                <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
            </div>
        </template>
        <vxetablebase :id="'cancel-protection202408041834'" ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0" :height="'100%'"
            :showsummary="data.summary ? true : false" :summaryarry="data.summary" @sortchange="sortchange"
            @select="checkboxRangeEnd">
            <template #annex="{ row, index }">
                <el-button type="text" @click="downLoadFile(row.annex, row.styleCode)">下载</el-button>
            </template>
            <template slot="right">
                <vxe-column title="操作" width="200" fixed="right">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="open30Props(row.styleCode)">近30天数据</el-button>
                            <el-button type="text" @click="handleAgree(row.id, 1)" v-if="row.status == 0">同意</el-button>
                            <el-button type="text" @click="handleAgree(row.id, 2)" v-if="row.status == 0">拒绝</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
        <el-dialog title="近30天数据" :visible.sync="styleCode30DayVisible" width="70%" v-dialogDrag>
            <styleCode30Props v-if="styleCode30DayVisible" :styleCode="styleCode" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { download } from '@/utils/download'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import styleCode30Props from './styleCode30Props.vue'
import dayjs from 'dayjs'
import request from '@/utils/request'
import dateRange from "@/components/date-range/index.vue";
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, styleCode30Props
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: "status asc,time",
                isAsc: false,
                type: 1,
                summarys: [],
                startTime: dayjs().format('YYYY-MM-DD'), //开始时间
                endTime: dayjs().format('YYYY-MM-DD'), //结束时间
            },
            data: {},
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            grouplist: [],
            ids: [],
            styleCode: '',
            styleCode30DayVisible: false
        }
    },
    async mounted() {
        this.init()
        await this.getCol()
        await this.getList()
    },
    methods: {
        downLoadFile(url, styleCode) {
            if (!url) return this.$message.error('暂无附件');
            let a = document.createElement('a');
            a.download = `${styleCode}附件`;
            // 设置下载链接
            a.href = url;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            // await request.get(url, {}, { responseType: 'blob' }).then(download).finally(() => {
            // })
        },
        open30Props(styleCode) {
            this.styleCode = styleCode
            this.styleCode30DayVisible = true
        },
        checkboxRangeEnd(row) {
            this.ids = row.map((item) => item.id);
        },
        handleAgree(ids, status, type) {
            if (type == 'batch' && this.ids.length == 0) return this.$message.error('请选择要操作的数据')
            this.$confirm(`是否${status == 1 ? '同意' : '拒绝'}, 是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await request.post('/api/bookkeeper/PblGoodPdd/Protect/Operate', {
                    ids: type == 'batch' ? ids : [ids],
                    status,
                })
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                    this.getList()
                }
                if (type == 'batch') {
                    this.ids = []
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        async getCol() {
            let { data, success } = await request.post('/api/bookkeeper/pblGoodPdd/Protect/GetColumns')
            if (success) {
                data.unshift({
                    label: "",
                    type: "checkbox",
                });
                //删除延长时间
                data = data.filter((item) => item.prop != "extendDay");
                const arr = ['profit3Rate', 'adRate', 'profitRate', 'profit4Rate', 'netProfitRate']
                data.forEach(item => {
                    if (arr.includes(item.prop) && item.format.indexOf('%') == item.format.length - 1) {
                        var v = 0
                        var tem = item.format.split('.')[1]
                        v = tem.length - 1
                        item.formatter = (row) => row[item.prop] !== null ? (row[item.prop] * 100).toFixed(v).toLocaleString() + '%' : ''
                    }
                })
                this.tableCols = data;
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async init() {
            const { data } = await getDirectorGroupList();
            this.grouplist = data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async exportProps() {
            this.isExport = true
            await request.post('/api/bookkeeper/pblGoodPdd/Protect/ExportData', this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post('/api/bookkeeper/pblGoodPdd/Protect/PageGetData', this.ListInfo)
                if (success) {
                    data.list.forEach(item => {
                        item.picture = item.picture ? JSON.stringify(item.picture.split(',').map(item => {
                            return {
                                url: item,
                                name: item
                            }
                        })) : []
                    })
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
