<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
          <el-button-group>
            <el-button style="padding: 0;margin: 0;">
              <el-select style="float:left;width:120px;" v-model="Filter.PartitionID" placeholder="分区" clearable
                         filterable @change="handlePartitionChange">
                <el-option v-for="item in partitionList" :key="item.id" :label="item.partitionName"
                           :value="item.id" />
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select multiple v-model="Filter.GroupNames" placeholder="组名称" clearable
                         :collapse-tags="true" filterable>
                <el-option v-for="item in grouplistsel" :key="item.groupname" :label="item.groupname"
                           :value="item.groupname" />
              </el-select>
            </el-button>

            <el-button style="padding: 0;margin: 0;">
              <el-input v-model="Filter.Sname" v-model.trim="Filter.Sname" placeholder="姓名" clearable
                        style="width:120px;" maxlength="100" />
            </el-button>

            <el-button style="padding: 0;margin: 0;">
              <datepicker v-model="Filter.Sdate"></datepicker>
            </el-button>
            <!-- <el-button type="text" size="medium" disabled style="color: red;">周转对比:</el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input-number v-model="filter1.startdiff"></el-input-number>至<el-input-number v-model="filter1.enddiff"></el-input-number>
            </el-button> -->
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onUpLoadSalary"
                       v-if="checkPermission(['api:customerservice:PddInquirs:ImportPddSalaryAsync'])">导入员工月薪</el-button>
            <el-button type="primary" @click="onDownSalary"
                       v-if="checkPermission(['api:customerservice:PddInquirs:ImportPddSalaryAsync'])">下载月薪模板</el-button>
            <el-button type="primary" @click="onExport">导出</el-button>
          </el-button-group>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='inquirsstatisticslist' @select='selectchange' :isSelection='false'
            :tableCols='tableCols' :loading="listLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>

            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
                @get-page="getinquirsstatisticsList" />
        </template>

        <el-dialog title="个人效率按店统计" :visible.sync="dialogVisibleSyj" width="85%" :close-on-click-modal="true" 
            v-dialogDrag>
            <!-- <span> -->
                <pddinquirsstatisticsbyshop v-if="dialogVisibleSyj" ref="pddinquirsstatisticsbyshop"
                    style="height: 600px;" />
            <!-- </span> -->
            <!-- <span style="display: flex;justify-content: end;margin-top: 5px;"> -->
                <!-- <el-button style="display: flex;margin-bottom: -20px;" @click="dialogVisibleSyj = false">关闭</el-button> -->
            <!-- </span> -->
        </el-dialog>

        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="true" v-dialogDrag>
            <!-- <div> -->
                <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
            <!-- </div> -->
            <!-- <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span> -->
        </el-dialog>

        <el-dialog title="员工月薪导入" :visible.sync="dialogVisibleUpload8" width="30%" :close-on-click-modal="false"
            v-dialogDrag>
            <span>
                <el-upload ref="upload8" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :http-request="uploadFile8" :on-success="uploadSuccess8" :on-change="onUploadChange8"
                    :on-remove="onUploadRemove8">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <!-- <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                        @click="onSubmitUpload8">上传</my-confirm-button> -->
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="onSubmitUpload8">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleUpload8 = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>

import pddinquirsstatisticsbyshop from '@/views/customerservice/pdd/pddinquirsstatisticsbyshopnew'
import {
  getPddGroup2,
  pagePddUserKfEfficiencyList,
  pagePddUserKfEfficiencyListMap,
  pddImportPddSalaryAsync,
  exportPddUserKfEfficiencyList,
  getGroupNamesByPartitionId, getAllPartitions
} from '@/api/customerservice/pddInquirsnew'

import datepicker from '@/views/customerservice/datepicker'
import buschar from '@/components/Bus/buschar'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

import Decimal from 'decimal.js';
function precision(number, multiple) {
    return new Decimal(number).mul(new Decimal(multiple));
}

const tableCols = [
    { istrue: true, prop: 'groupName', label: '组名称', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'partitionName', label: '分区名称', width: '120', sortable: 'custom' },

    { istrue: true, prop: 'sname', label: '姓名', width: '80', type: "click", handle: (that, row, column, cell) => that.canclick(row, column, cell), formatter: (row) => row.sname },

    // { istrue: true, prop: 'jrQty', label: '介入退款数量', width: '100', permission: "pddinquirs.pddinquirsstatisticssh.jr" },
    // { istrue: true, prop: 'jrRate', label: '介入退款率', width: '100', permission: "pddinquirs.pddinquirsstatisticssh.jr" },

    { istrue: true, prop: 'inquirs', label: '咨询人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'manualReply', label: '人工接待人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsCount', label: '询单人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'receiveCount', label: '最终成团人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'successpayrate', label: '询单转化率', width: '80', sortable: 'custom', 
        formatter: (row) => { return (row.successpayrate ? row.successpayrate : 0) + "%" } 
        // formatter: (row) => { return (row.successpayrate ? precision(row.successpayrate, 100) : 0) + "%" }
    },

    { istrue: true, prop: 'threeSecondLost', label: '3分钟未回复人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'threeSecondGet', label: '3分钟回复人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'threeSecondReplyRate', label: '3分钟人工回复率', width: '90', sortable: 'custom', 
        formatter: (row) => { return (row.threeSecondReplyRate ? row.threeSecondReplyRate : 0) + "%" } 
        // formatter: (row) => { return (row.threeSecondReplyRate ? precision(row.threeSecondReplyRate, 100) : 0) + "%" }
    },
    { istrue: true, prop: 'responseTime', label: '均响(秒)', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'thirtySecondResponseRate', label: '30秒应答率', width: '80', sortable: 'custom', 
        formatter: (row) => { return (row.thirtySecondResponseRate ? row.thirtySecondResponseRate : 0) + "%" } 
        // formatter: (row) => { return (row.thirtySecondResponseRate ? precision(row.thirtySecondResponseRate, 100) : 0) + "%" }
    },

    { istrue: true, prop: 'outTimes', label: '出勤人次', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsPercentstr', label: '询单占比', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsPriceRate', label: '客单价', width: '80', sortable: 'custom' },

    // { istrue: true, prop: 'inquireCost', label: '接待成本（元）', width: '130', sortable: 'custom' },
    // { istrue: true, prop: 'inquireCostPrice', label: '接待成本单价', width: '130', sortable: 'custom' },
    // { istrue: true, prop: 'ipsCostPrice', label: '询单成本单价', width: '130', sortable: 'custom' },
    // { istrue: true, prop: 'receiveCostPrice', label: '成团成本单价', width: '130', sortable: 'custom' },

    { istrue: true, prop: 'salesvol', label: '客服销售额（元）', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'serviceScore', label: '客服服务分', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'lowRatingOrderCount', label: '评分≤3订单数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'disputeRefundCount', label: '纠纷退款数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'complainCount', label: '投诉数', width: '80', sortable: 'custom' },

    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 120, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, pddinquirsstatisticsbyshop, datepicker, buschar },
    props: ["partInfo"],
    data() {
        return {
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            Filter: {
                Sdate: [formatTime(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30), "YYYY-MM-DD 00:00:00"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],
            },
            shopList: [],
            userList: [],
            groupList: [],
            inquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "groupName", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
          partitionList: [], // 分区列表

            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            grouplistsel: [],
            dialogVisibleUpload8: false,
            isleavegroup: this.partInfo,
            uploadLoading: false,
        };
    },
    watch: {
        partInfo() {
            this.isleavegroup = this.partInfo;
            this.setGroupSelect();

        }
    },
    async mounted() {
        this.isleavegroup = this.partInfo;
        window.showpddpddlist = this.showlist;
        await this.setGroupSelect();
        await this.getPartitionList();

    },
    methods: {
      async getPartitionList() {
        try {
          const res = await getAllPartitions();
          this.partitionList = res.data;
        } catch (error) {
          console.error('获取分区列表失败', error);
        }
      },
        async showchart(row) {

            if (this.Filter.timerange) {
                this.Filter.startSdate = this.Filter.Sdate[0];
                this.Filter.endSdate = this.Filter.Sdate[1]
            }
            var params = { Groupname: row.groupname, Sname: row.sname, StartSdate: this.Filter.startSdate, EndSdate: this.Filter.endSdate }
            let that = this;

            const res = await pagePddUserKfEfficiencyListMap(params).then(res => {
                that.dialogMapVisible.visible = true;
                that.dialogMapVisible.data = res
                that.dialogMapVisible.title = res.title;
                res.title = "";
            })
            this.dialogMapVisible.visible = true
        },
        showlist(groupname, startdate, enddate) {
            this.Filter.PartitionID = null;
            this.Filter.Sname = '';
            this.Filter.GroupNames = [groupname];
            this.Filter.Sdate = [startdate, enddate];
            this.Filter.startSdate = startdate;
            this.Filter.endSdate = enddate;
            this.onSearch()
        },
        async canclick(row, column, cell) {
            if (this.Filter.Sdate) {
                var d = new Date(this.Filter.Sdate[0])
                var startsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                if (startsdate == "NaN-NaN-NaN") {
                    startsdate = "";
                }
                localStorage.setItem("startsdate", startsdate);

                d = new Date(this.Filter.Sdate[1])
                var endsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                if (endsdate == "NaN-NaN-NaN") {
                    endsdate = "";
                }
                localStorage.setItem("endsdate", endsdate);
            }
            else {
                localStorage.setItem("startsdate", "2010-01-01");
                localStorage.setItem("endsdate", "2050-01-01");
            }
            localStorage.setItem("sname", row.sname);
            this.dialogVisibleSyj = true
        },
        async deleteBatch(row) {
            var that = this;
            this.$confirm("此操作将删除此批次个人效率统计数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(async () => {
                    await deleteInquirsStatisticsBatch({ batchNumber: row.batchNumber })
                    that.$message({ message: '已删除', type: "success" });
                    that.onRefresh()
                });
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onRefresh() {
            this.setGroupSelect();
            this.onSearch();
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getinquirsstatisticsList();
        },
        getParam() {

            if (this.Filter.UseDate) {
                this.Filter.startAccountDate = this.Filter.UseDate[0];
                this.Filter.endAccountDate = this.Filter.UseDate[1];
            }
            if (this.Filter.Sdate) {
                this.Filter.startSdate = this.Filter.Sdate[0];
                this.Filter.endSdate = this.Filter.Sdate[1];
            }
            else {
                this.Filter.startSdate = null;
                this.Filter.endSdate = null;
            }
            this.Filter.EnmPddGroupType = 0;
            const para = { ...this.Filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,

            };
            return params;
        },
        async getinquirsstatisticsList() {
            let params = this.getParam();
            this.listLoading = true;
            const res = await pagePddUserKfEfficiencyList(params);
            this.listLoading = false;
            this.total = res.total
            this.inquirsstatisticslist = res.list;
            this.summaryarry = res.summary;
            },
        async handlePartitionChange(partitionId) {
            if (partitionId) {
            try {
                const res = await getGroupNamesByPartitionId({ partitionId });
                if (res.data && res.data.length > 0) {
                //   // 将分区下的组名转换为grouplistsel格式，并只保留一体组
                //   this.grouplistsel = res.data.map(groupName => ({
                //     groupname: groupName
                //   })).filter(item => item.groupname.includes("一体"));
                //   this.grouplistsel = res.data.map(item => ({
                //     groupname: item.groupname
                //   }));
                this.grouplistsel = Array.from(
                    new Set(res.data.map(item => item.groupname))
                    ).map(groupname => ({ groupname }));
                } else {
                this.grouplistsel = [];
                }
                // 清空已选择的组名
                this.Filter.GroupNames = [];
            } catch (error) {
                console.error('获取分区下组名失败', error);
            }
            } else {
            // 如果清空分区选择，则获取所有组
            await this.setGroupSelect();
            }
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        //导入员工月薪
        onUpLoadSalary() {
            this.dialogVisibleUpload8 = true;
            this.uploadLoading = false;
        },
        async onUploadChange8(file, fileList) {
            this.fileList = fileList;
        },
        async onUploadRemove8(file, fileList) {
            this.fileList = [];
        },
        //开始月薪上传
        async uploadFile8(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            const form = new FormData();
            form.append("upfile", item.file);
            const res = await pddImportPddSalaryAsync(form);
            if (res?.success) {
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
                this.dialogVisibleUpload8 = false;
            }
        },
        //月薪上传成功
        async uploadSuccess8(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        //提交上传
        async onSubmitUpload8() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload8.submit();
        },
        //下载月薪模板
        async onDownSalary() {
            window.open("/static/excel/customerservice/拼多多客服效率-员工月薪导入模板.xlsx", "_blank");
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await exportPddUserKfEfficiencyList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '拼多多个人效率统计(售前组)_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
        async setGroupSelect() {
            const form = new FormData();
            form.append("enmPddGroupType", 0);
            form.append("isleavegroup", this.isleavegroup);
            const res = await getPddGroup2(form);
            this.grouplistsel = res.data;
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
