<template>
    <my-container>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
          :tableData='list'  :tableCols='tableCols' :isSelection="false" @select="selectchange"
          :tableHandles='tableHandles'
          :loading="listLoading">
        </ces-table>
          <template #footer>
            <my-pagination
              ref="pager"
              :total="total"
              :checked-count="sels.length"
              @get-page="getlist"
            />
        </template>

    </my-container>
</template>

<script>
import cesTable from "@/components/Table/table.vue";
import {formatTime} from "@/utils";
import dayjs from "dayjs";
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import {getOrderIllegalPurchaseDetailAsync, exportOrderIllegalPurchaseDetail} from '@/api/order/orderdeductmoney'

const tableCols = [{
      istrue: true,
      prop: 'occurrenceTime',
      label: '日期',
      width: '100'
    },{
      istrue: true,
      prop: 'proCode',
      label: '宝贝ID',
      width: '120'
    },{
      istrue: true,
      prop: 'orderNo',
      label: '订单号',
      width: '120'
    },{
      istrue: true,
      prop: 'platformName',
      label: '平台',
      width: '100'
    },{
      istrue: true,
      prop: 'shopName',
      label: '店铺',
      width: '100'
    },
    {
      istrue: true,
      prop: 'amountPaid',
      label: '扣款金额',
      width: 'auto'
    },];

const tableHandles=[
    {label:"导出扣款详情", handle:(that)=>that.onExportDetail()},        
    ];

  const startDate = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
  const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunHanAdminGoodscutmoneydetail',
    components: {cesTable, formatTime, dayjs, MyContainer, MyConfirmButton},
    props: {
        filter: {}
    },

    data() {
        return {
            that: this,
            list: [],
            pager: {
                OrderBy: "id",
                IsAsc: false
            },
            tableCols: tableCols,
            tableHandles:tableHandles,
            summaryarry:{},
            total: 0,
            sels: [],
            isSelectColumn: false,
            pageLoading: false,
            listLoading: false,
            pageLoading: false,
        };
    },

    async mounted() {

    },

    methods: {
        async onSearch() {
            console.log('来了')
            this.$refs.pager.setPage(1);
            await this.getlist();
        },
        async getlist() {
            if (this.filter.timerange&&this.filter.timerange.length>1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message({message:"请先选择日期",type:"warning"});
                return false;
            }
            //this.filter.platform=2
            var pager = this.$refs.pager.getPager();
            var page  = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }

            if(params===false){
                    return;
            }
            // params.brandId = this.brandId;
            console.log("dianji看板最终参数",params);
            this.listLoading = true
            const res = await getOrderIllegalPurchaseDetailAsync(params)
            this.listLoading = false;

            if (!res?.code) {
                return
            }
            this.total = res.data.total
            const data = res.data.list
            this.list = data
            //////////
            this.summaryarry=res.data.summary;
            if(this.summaryarry)
                this.summaryarry.amountPaid_sum=parseFloat(this.summaryarry.amountPaid_sum.toFixed(6));
        },
        //导出
        async onExportDetail(){
            if (this.filter.timerange&&this.filter.timerange.length>1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message({message:"请先选择日期",type:"warning"});
                return false;
            }
            //this.filter.platform=2
            var pager = this.$refs.pager.getPager();
            var page  = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }

            if(params===false){
                    return;
            }
            var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
            var res= await exportOrderIllegalPurchaseDetail(params);
            loadingInstance.close();
            if(!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download','订单违规扣款详情统计_' + new Date().toLocaleString() + '.xlsx' )
            aLink.click();
        },
        async sortchange(column) {
            if (!column.order)
            this.pager = {};
            else
            this.pager = {
                OrderBy: column.prop,
                IsAsc: column.order.indexOf("descending") == -1 ? true : false
            }
            this.onSearch();
        },
        selectchange: function(rows, row) {
            // console.log('打印选择',rows);
            this.selids = [];
            rows.forEach(f => {
            this.selids.push(f.id);
            })
        },
    },
};
</script>

<style lang="scss" scoped>

</style>