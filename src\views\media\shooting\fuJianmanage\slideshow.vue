<template>
    <el-container v-loading="pageLoading" :element-loading-text="upmsginfo">
    <div class="body">
        <div class="content">
            <div class="list flexcenter">
                <div class="flexrow smaslling">
                        <div style=" width: 100%; height: 100%; display: flex;">
                            <div class="leftbox">
                                <el-upload
                                action="#"
                                :limit="200"
                                :accept="name=='weivideo'?'.mp4,.MOV':'.png,.jpg,.jpeg'"
                                ref="uploadversion"
                                :show-file-list="false"
                                :auto-upload="false"
                                multiple
                                :on-change="onchangevideo">
                                <el-button type="primary" style="margin: 7.5px;">点击上传</el-button>
                                </el-upload>
                                <div class="manybox outline" :style="item.uploadList!=null?{border: '1px solid #eee'}:{}">
                                    <div style="width: 95px; height: 95px; position: relative; margin-top: 5px; margin-left: 10px;" v-for="(itemm,ind) in item.uploadList" :key="ind">
                                        <i class="el-icon-error close-img" style="position: absolute; top: -5%; right: 0; color: red; z-index: 100;" @click="delimg(itemm,ind)"></i>
                                        <img :class="[itemm.isDefault==1?'borderr':'']" @click="selimg(itemm,i,ind)" :src="itemm.url" width="90%" height="90%" alt="" v-if="name!='weivideo'&&selimgshow">
                                        <video :class="[itemm.isDefault==1?'borderr':'']" @click="selimg(itemm,i,ind)" :src="itemm.url" width="90%" height="90%" alt="" v-if="name=='weivideo'&&selimgshow"></video>
                                    </div>
                                </div>
                            </div>
                            <div class="rightbox">
                                <el-button type="primary" @click="isdefault(showimg[i])" style="margin: 7.5px 0; width: 80px;">设为默认</el-button>
                                <div style="width: 520px; height: auto; margin-bottom: 210px;" v-if="showimg[i]&&name!='weivideo'||item.defuploadinfo&&name!='weivideo'" >
                                    <img style="width: 100%; height: auto;" :src="showimg[i]?showimg[i]:item.defuploadinfo.url" class="fleximg" alt=""/> 
                                </div>
                                <div style="width: 520px; height: auto;"  v-else-if="showimg[i]&&name=='weivideo'||item.defuploadinfo&&name=='weivideo'">
                                    <video style="width: 520px; height: auto;" :src="showimg[i]?showimg[i]:item.defuploadinfo.url" width="100%" height="100%" alt="" controls></video>
                                </div>
                                <div style="width: 520px; height: 520px;" v-else class="flexcenter">
                                    <span >请从左边选择默认图片或视频</span>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
        </div>

        <el-dialog
        :title="title"
        :visible.sync="dialogVisible"
        width="30%">
        <el-card>
            <el-radio-group style="display: flex; flex-direction: column;" v-model="radio" v-for="(item,i) in imglists" :key="i">
                <el-radio :label="i" style="margin-top: 10px;">{{item.fileName}}</el-radio>
            </el-radio-group>
        </el-card>
        </el-dialog>

    </div>
</el-container>
</template>

<script>
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
import MyContainer from "@/components/my-container";
import {saveShootRefenceInfo,getShootRefenceInfo  } from '@/api/media/ShootingVideo';
export default {
    name: 'DEMOSlideshow',
    props:{
        list:{type: Array,default: function() {
			return []
		}},
        item:{ type: Object, default:null},
        type:{ type: String, default:'img'},
        bannum: {type: Number,default: 1},
        name: {type: String,default: ''},
        i: {type: Number,default: 0},

    },
    components: {MyContainer},
    data() {
        return {
            typee: 'img',
            radio: 0,
            title: '主图-PC端',
            dialogVisible: false,
            whiteimg: 'https://i2.100024.xyz/2022/12/11/nysuym.webp',
            imgtest: 'https://img.alicdn.com/imgextra/i2/725677994/O1CN01ksdbKH28vIoa7itH0_!!725677994-0-sm.jpg_430x430q90.jpg',
            arraylist: [],
            imglists: [],
            moimg: '',
            index1: null,
            index2: null,
            item1: null,
            params: null,
            pageLoading: false,
            contentshow: false,
            arraylists: [],
            upmsginfo: '努力加载中...',
            showimg: [],
            selimgshow: true,
            listlength: null,
            chunum: 0,
        };
    },
    watch: {
        list: "chulist"
    },

    mounted() {
        let _this = this;
        // _this.arraylist = _this.list;
        _this.typee = _this.type;
        _this.contentshow = true;
        // _this.pageLoading = false;
        // if(_this.name == 'skuimg'){
        //     _this.arraylist = _this.list.photo;
        // }else{
        //     _this.arraylist = _this.list;
        // }
        _this.arraylist = _this.list;
    },

    methods: {
        ischange(val){
            if(val){
                this.$emit("changefuc",val)
            }
        },
        chulist(val){
            let _this = this;
            _this.contentshow = false;
            if(_this.name == 'skuimg'){
                _this.arraylist = val.photo;
            }else{
                _this.arraylist = val;
            }
            // _this.arraylist = val.photo;
            _this.contentshow = true;
        },
        selimg(item,index,index2){
            this.selimgshow = false;
            this.showimg[index] = item.url;
            this.index2 = index2;
            this.selimgshow = true;
        },
        async delimg(item,index){
            let _this =this;
            if(item.name){
                _this.arraylist[_this.i].uploadList.splice(index,1);
            }else{
                await this.afteruploadtempList(item, 3);
            }
        },
        async saveimg(){
            let _this =this;
            // _this.pageLoading = true;
            if(!_this.listlength){
                let res = await saveShootRefenceInfo(_this.params);
                await _this.$emit("getlist") 
                _this.pageLoading = false;  
            }else{
                _this.chunum = _this.chunum+1;
                let res = await saveShootRefenceInfo(_this.params);
                if(_this.chunum==_this.listlength){
                    await _this.$emit("getlist")                               
                    _this.pageLoading = false;
                    _this.chunum = 0;
                    _this.listlength = null;
                }
            }
            

           
        },
        indexx(item,index){
            this.index1 = index;
            this.item1 = item;
        },
        async isdefault(val){

            let _this = this;
            _this.ischange(true)
            // _this.pageLoading = true;
            let arrays = _this.arraylist;
            for(var i =0; i<arrays.length; i++){
                let item = arrays[i];
                for(var ii =0; ii<item.uploadList.length; ii++){
                    let itemm = item.uploadList[ii];
                        if(i==_this.i){
                            if(ii==_this.index2){
                                itemm.isDefault =1;
                            }else{
                                itemm.isDefault =0;
                            }
                            
                        }
                }
            }
        },
        async onchangevideo(file,arrayfile){
            let _this = this;
            _this.ischange(true)
            let params = {
                url: URL.createObjectURL(file.raw),
                ...file
            }
            _this.pageLoading = true;
            _this.listlength = arrayfile.length;
            // await _this.submitt();
            this.upmsgtxt = "正在上传："+ file.name 
            this.upmsginfo =this.upmsgtxt +"   " +  this.percentage +"%";
            await _this.AjaxFile(file,0,"")
            if (this.atfterUplaodData != null) {
                await this.afteruploadtempList(this.atfterUplaodData, 0);

                let uploadFilesArr = this.$refs.uploadversion?.uploadFiles;
                if(!uploadFilesArr){
                    this.$refs.uploadversion.uploadFiles =[]
                }
            }

        },
        async submitt(){
            let _this = this;
            _this.pageLoading = true;
            let arrays = _this.arraylist;
            for(var i =0; i<arrays.length; i++){
                let item = arrays[i];
                for(var ii =0; ii<item.uploadList.length; ii++){
                    let itemm = item.uploadList[ii];
                        if(itemm?.name){
                            this.upmsgtxt = "正在上传："+ itemm.name 
                            this.upmsginfo =this.upmsgtxt +"   " +  this.percentage +"%";
                            await _this.AjaxFile(itemm,0,"")
                            if (this.atfterUplaodData != null) {
                                await this.afteruploadtempList(this.atfterUplaodData, 0);
                                // await this.afteruploadtempList(this.atfterUplaodData, 1);
                            }
                        }else{
                            await this.afteruploadtempList(itemm, 2);
                        }
                }
            }
            // _this.pageLoading = false;
        },
        async afteruploadtempList(item,isDefault){
            let _this = this;
            _this.params = {
                url: item.url,
                filePath: isDefault==2?item.filePath:item.relativePath,
                fileName :item.fileName,
                shootingRefUploadInfoId: item.shootingRefUploadInfoId?item.shootingRefUploadInfoId:0,
                shootingTaskId: _this.$route.query.id,
                isDefault: item.isDefault,
                referenceInfoId: _this.name=='weivideo'?_this.item.mainVedioId:_this.item.mainPhotoId,
                type: _this.bannum,
                filestaus: isDefault==3?1:(isDefault==2||isDefault==1)?2:0,
                orderNum: 0
            }

            await _this.saveimg();
        },
         //切片上传
         async AjaxFile(file, i, batchnumber) {
            var name = file.name; //文件名
            var size = file.size; //总大小shardSize = 2 * 1024 * 1024,
            var shardSize = 1 * 1024 * 1024;
            var shardCount = Math.ceil(size / shardSize); //总片数
            if (i >= shardCount) {
                return;
            }
            //计算每一片的起始与结束位置
            var start = i * shardSize;
            var end = Math.min(size, start + shardSize);
            //构造一个表单，FormData是HTML5新增的
            i = i + 1;
            var form = new FormData();
            form.append("data", file.raw.slice(start, end)); //slice方法用于切出文件的一部分
            form.append("batchnumber", batchnumber);
            form.append("fileName", name);
            form.append("total", shardCount); //总片数
            form.append("index", i); //当前是第几片
            const res = await xMTVideoUploadBlockAsync(form);
            if (res?.success) {
                this.percentage = (i * 100 / shardCount).toFixed(2);
                this.upmsginfo =this.upmsgtxt +"   " +  this.percentage +"%";
                if (i == shardCount) {
                    this.atfterUplaodData = res.data;
                } else {
                    await this.AjaxFile(file, i, res.data);
                }
            } else {
                this.$message({ message: res?.msg, type: "warning" });
            }
        }, 
        uploadToServer(file, callback) {
            var xhr = new XMLHttpRequest()
            var formData = new FormData()
            formData.append('file', file)
            xhr.open('post', '/api/uploadnew/file/UploadCommonFileAsync')
            xhr.withCredentials = true
            xhr.responseType = 'json'
            xhr.send(formData)
            xhr.onreadystatechange = () => {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    // debugger;
                    callback(xhr.response)
                }
            }
        },
      showdilog(){
        let _this = this;
        _this.dialogVisible = true;
      }
    },
};
</script>

<style lang="scss" scoped>
.body{
    margin: 0;
    // display: flex;
    // justify-content: center;
    // align-items: center;
    // padding: 20px 10px;
    
}
.content{
width: 100%;
min-height: 520px;
}
.list{
//  min-height: 780px;
 border: 1px solid #eee;
 border-bottom: none;
//  margin-bottom: 30px;

}
.flexcenter{
    display: flex;
    justify-content: center;
    align-items: center;
}
.smaslling{
  width: 100%;
//   margin-top: -45px;
  display: flex;
  justify-content: center;
//   align-items: center;
}
.flexrow{
  display: flex;
  flex-direction: row;
}
.showcs{
    color: #409EFF;
    font-weight: 600;
    font-size: 22px;
}
#click-scroll-X {
    display: flex;
    align-items: center;
    .left_btn,.right_btn {
      font-size: 2.8rem;
      cursor: pointer;
    }
  .scroll_wrapper {
    width: 900px;
    overflow-x: scroll;
    // padding: 20px 20px;
    overflow: hidden;
    // border: 1px solid red;
    .scroll_list{
    display: flex;
    align-items: center;
    justify-content: space-between;
    // overflow: hidden;

        .item {
        width: 460px;
        height: 460px;
        display: flex;
        align-items: center;
        justify-content: center;
        // border: 1px solid rgb(223, 223, 223);
        box-sizing: border-box;
        flex-shrink: 0;
        }
    }
  }
}
.iconsize{
    font-size: 40px;
    font-weight: 600;
}
.btnright{
    position: absolute;
    top: -7%;
    right: 80%;
    z-index: 999;
}
.iconright{
    position: absolute;
    top: -7%;
    right: 75%;
    z-index: 999;
}
.leftbox{
    flex: 3.5;
    min-height: 780px;
    width: 230px;
    margin-bottom: auto;
    display: flex;
    flex-direction: column;
    overflow-y: hidden;
}
.rightbox{
    flex: 5;
    min-height: 780px;
    width: 520px;
    display: flex;
    margin-bottom: auto;
    flex-direction: column;
    // justify-content: center;
    // align-items: center;
}
.manybox{
    margin-left: 10px;
    // border: 1px solid #eee;
    display: flex;
    // justify-content: center;
    // margin-top: 10px;
}

.outline{
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    overflow-x: hidden;
}

.borderr{
    border: 1px solid red;
}
.centerflex{
  display: flex;
  justify-content: center;
  align-items: center;
}
.fleximg{
    max-width: 100%; 
    max-height: 100%; 
    height: auto; 
    width: auto;
}
</style>