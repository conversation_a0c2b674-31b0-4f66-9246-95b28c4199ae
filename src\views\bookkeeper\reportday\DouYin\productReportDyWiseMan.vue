<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;border: none;width:200px;">
                    <el-select v-model.trim="filter.wiseManAccountName" filterable clearable placeholder="达人抖音号" remote
                        reserve-keyword :remote-method="remoteMethodWiseManAccountCode"
                        :loading="wiseManAccountCodeloading" style="width:200px">
                        <el-option v-for="item in accountNameList" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>


                </el-button>
                <el-button style="padding: 0;margin: 0;border: none;width:140px;">
                    <el-select v-model.trim="filter.businessMan" filterable clearable placeholder="商务"
                        style="width:140px" :disabled="businessManDisabled">
                        <el-option label="无商务" value="无商务"></el-option>
                        <el-option v-for="item in businessManList" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;border: none;width:140px;">
                    <el-input v-model.trim="filter.proCode" clearable placeholder="商品ID" maxlength="50">
                    </el-input>
                </el-button>
                <el-button style="padding: 0;margin: 0;border: none;width:240px;">
                    <el-input v-model.trim="filter.keywords" clearable placeholder="关键字查询 商务/达人/组/商品" maxlength="50">
                    </el-input>
                </el-button>

                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onAddBzRef">新增达人商务商品关系</el-button>
                <el-button type="primary" @click="onManageShow">管理达人</el-button>
                <el-button type="primary" @click="onExport">导出</el-button>
                <el-button type="primary" @click="onUpdateBusinessManShow">批量更改商务</el-button>
                <el-button type="primary" @click="onImport">导入</el-button>
                <el-button type="primary" @click="onModel">下载模板</el-button>
            </el-button-group>
        </template>

        <vxetablebase :id="'productReportDyWiseMan'" :border="true" :align="'center'"
            :tablekey="'productReportDyWiseMan'" ref="table" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :isSelectColumn="true" @select="callback" :showsummary='true' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols' :loading="listLoading"
            style="width:100%;height:100%;margin: 0" :xgt="9999">
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

        <!-- 商务编辑 -->
        <el-dialog title="商务编辑" :visible.sync="updateBusinessManVisible" width="20%" v-dialogDrag>
            <span>
                <el-select v-model="updateBusinessManData.businessDDUserId" style="width: 100%" placeholder="请选择"
                    clearable filterable>
                    <el-option v-for="item in businessManList1" :label="item.label" :value="item.value" />
                </el-select>
                <br /> <br />
            </span>
            <span slot="footer" class="dialog-footer">
                <my-confirm-button type="submit" :loading="updateBusinessManLoading" @click="onUpdateBusinessMan" />
                &nbsp;
                <el-button @click="updateBusinessManVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="导入" :visible.sync="dialogUploadData.visible" width="30%" v-dialogDrag append-to-body
            :close-on-click-modal="false">
            <span>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success"
                                :loading="dialogUploadData.uploadLoading" @click="onSubmitUpload">{{
                                    (dialogUploadData.uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
                <el-row v-if="dialogUploadData.showError" style="color:darkorange;">
                    <span v-for="(err, errIndex) in dialogUploadData.showErrorList" :key="errIndex">{{ err
                        }};<br /></span>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogUploadData.visible = false">关闭</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";

import {
    GetDYBusinessWiseManList, GetAllWiseManDetailList, GetAllBusinessDetailList,
    ExportDouYinWiseManExcelAsync, OneKeyUpdateBusinessMan, GetBusinessMan, ImportProductDyWiseManBzRef, GetBusinessManInfo,
    GetAllWiseManDetailListPage
} from "@/api/bookkeeper/reportdayDouYin";

import { formatPlatform, formatTime,formatLinkProCode } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

const tableCols = [
    { istrue: true, label: '', type: "checkbox", },
    { istrue: true, label: '小组头像',  width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
    { istrue: true, prop: 'groupName', label: '运营组', sortable: 'custom', width: '80',type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, },
    { istrue: true, prop: 'businessMan', label: '商务', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'wiseManAccountName', label: '达人', sortable: 'custom', width: '170' },
    { istrue: true, prop: 'wiseManUID', label: '达人UID', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'wiseManAccountCode', label: '达人抖音号', sortable: 'custom', width: '145' },
    { istrue: true, prop: 'weChat', label: '微信号', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'proCode', label: '商品ID', sortable: 'custom', width: '150',type:'html',formatter:(row) => formatLinkProCode(6,row.proCode,) },
    { istrue: true, prop: 'proName', label: '商品名称', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '添加时间', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'modifiedTime', label: '修改时间', sortable: 'custom', width: '150' },
    {
        istrue: true, label: '功能', width: '100', type: 'button', fixed: 'right', align: 'center', btnList: [
            {
                label: '编辑',
                handle: (that, row) => that.onEditBzRef(row),
            },
        ]
    }
];

export default {
    name: "productReportDyWiseMan",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable,
        vxetablebase
    },
    data() {
        return {
            that: this,
            filter: {
                wiseManAccountName: null,
                businessMan: null,
            },
            businessManDisabled: false,

            wiseManList: [],
            accountNameList: [],
            businessManList: [],
            tableCols: tableCols,

            total: 0,
            datalist: [],
            pager: { OrderBy: "", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            selrows: [],
            businessManList1: [],
            updateBusinessManVisible: false,
            updateBusinessManLoading: false,
            updateBusinessManData: {
                businessDDUserId: null,
                businessMan: null,
            },

            fileList: [],
            fileparm: {},

            dialogUploadData: {
                title: "",
                visible: false,
                uploadLoading: false,
                showError: false,
                showErrorList: [],
            },
        };
    },
    async mounted() {
        await this.getBusinessManInfo();
        await this.onBusinessManMethod1("");
        await this.onSearch();
    },
    async created() {
        //await this.getWiseManNameList();
        await this.getBusinessNameList();
    },
    methods: {
        // async getWiseManNameList() {
        //     const res = await GetAllWiseManDetailList();
        //     this.accountNameList = res.data?.map(item => { return { value: item.wiseManAccountName, label: item.wiseManAccountName }; });
        // },
        async getBusinessNameList() {
            const resBusiness = await GetAllBusinessDetailList();
            this.businessManList = resBusiness.data?.map(item => { return { value: item.businessMan, label: item.businessMan }; });
        },
        async getBusinessManInfo() {
            const res = await GetBusinessManInfo();
            if (res.success && res.data) {
                this.businessManDisabled = true;
                this.filter.businessMan = res.data;
            }
        },
        //排序
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async getList() {
            var that = this;
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };

            that.pageLoading = true;
            const res = await GetDYBusinessWiseManList(params);
            that.total = res.data?.total;
            that.datalist = res.data?.list;
            that.summaryarry = res.data?.summary;
            that.pageLoading = false;

        },
        //导出
        async onExport() {
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            var res = await ExportDouYinWiseManExcelAsync(params);
            if (!res?.data) {
                return false;
            };
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '商务达人导出' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        onRefresh() {
            this.onSearch()
        },
        //导出
        async onExport() {
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            var res = await ExportDouYinWiseManExcelAsync(params);
            if (!res?.data) {
                return false;
            };
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '商务达人导出' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        // 新增商务达人商品关系
        onAddBzRef() {
            let self = this;

            this.$showDialogform({
                path: `@/views/bookkeeper/reportday/DouYin/DyWiseManBzRefForm.vue`,
                title: '达人商务商品关系管理',
                autoTitle: false,
                args: { mode: 1 },
                height: 500,
                width: '760px',
                callOk: self.onSearch
            })

        },
        // 修改商务达人商品关系
        onEditBzRef(row) {
            let self = this;

            this.$showDialogform({
                path: `@/views/bookkeeper/reportday/DouYin/DyWiseManBzRefForm.vue`,
                title: '达人商务商品关系管理',
                autoTitle: false,
                args: { mode: 2, data: row },
                height: 500,
                width: '760px',
                callOk: self.onSearch
            })

        },

        //管理添加达人
        onManageShow() {
            let self = this;

            this.$showDialogform({
                path: `@/views/bookkeeper/reportday/DouYin/DayReportDyWiseManMng.vue`,
                title: '达人管理',
                autoTitle: false,
                args: { mode: 1 },
                height: 500,
                width: '60%',
                //callOk: self.onSearch
            })

        },
        callback(val) {
            this.selrows = [...val];
        },
        async onUpdateBusinessManShow() {
            if (this.selrows.length <= 0) {
                this.$message({ type: 'error', message: '请勾选!' });
                return;
            }
            this.updateBusinessManData.businessDDUserId = null;
            this.updateBusinessManData.businessMan = null;
            this.updateBusinessManVisible = true;
        },
        async onUpdateBusinessMan() {
            if (this.selrows.length <= 0) {
                this.$message({ type: 'error', message: '请勾选!' });
                return;
            }
            let la = this.businessManList1.find(f => f.value == this.updateBusinessManData.businessDDUserId)?.label;
            if (la) {
                this.updateBusinessManData.businessMan = la;
            }

            if (this.updateBusinessManData.businessDDUserId == null || this.updateBusinessManData.businessMan == null) {
                this.$message({ type: 'error', message: '请选择商务!' });
                return;
            }
            let ids = [];
            this.selrows.forEach(f => {
                ids.push(f.id);
            });
            let param = {
                ids: ids,
                businessDDUserId: this.updateBusinessManData.businessDDUserId,
                businessMan: this.updateBusinessManData.businessMan
            };
            console.log(param);
            this.updateBusinessManLoading = true;
            var res = await OneKeyUpdateBusinessMan(param);
            this.updateBusinessManLoading = false;
            if (res?.success) {
                this.$message({ type: 'success', message: '更新成功!' });
                await this.onSearch();
                this.updateBusinessManVisible = false;
            }
        },
        async onBusinessManMethod1(value) {
            this.businessManList1 = [];
            let pos = await GetBusinessMan({ keywords: value });
            if (pos?.success && pos?.data && pos?.data.length > 0) {
                pos?.data.forEach(f => {
                    this.businessManList1.push({ value: f.key, label: f.value1 });
                });
            }
        },

        async onModel() {
            window.open("/static/excel/dayreport/抖音达人商务关系导入模板.xlsx", "_blank");
        },
        async onImport() {
            this.dialogUploadData.showError = false;
            this.dialogUploadData.showErrorList = [];
            this.dialogUploadData.visible = true;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            debugger
            this.dialogUploadData.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            var res = await ImportProductDyWiseManBzRef(form);
            if (res?.success) {
                this.$message({ message: "导入成功", type: "success" });
                this.dialogUploadData.showError = false;
                this.dialogUploadData.visible = false;
            }
            else {
                if (res?.data) {
                    this.dialogUploadData.showErrorList = res?.data;
                    this.dialogUploadData.showError = true;
                }
            }
            this.dialogUploadData.uploadLoading = false;
        },
        async onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            if (this.dialogUploadData.showError == false) {
                this.dialogUploadData.visible = false;
                await this.onSearch();
            }
        },
        onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadRemove(file, fileList) {
            this.fileList = [];
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        async remoteMethodWiseManAccountCode(value) {
            const res4 = await GetAllWiseManDetailListPage({ str: value });
            this.accountNameList = res4.data?.map(item => {
                return { value: item.wiseManAccountName, label: item.wiseManAccountName + '（' + item.wiseManAccountCode + '）' };
            });
        }
    }
};
</script>
