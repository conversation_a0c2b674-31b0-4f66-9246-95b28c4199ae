<template>
    <MyContainer>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
        </vxetablebase>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import dayjs from 'dayjs'
import { getMatchOrReleases } from '@/api/vo/prePackScan'
const tableCols = [
    { width: 'auto', align: 'center', prop: 'orderNoInner', label: '内部订单号', type: 'orderLogInfo', orderType: 'orderNoInner' },
    { width: 'auto', align: 'center', prop: 'timePay', label: '支付时间', },
]
export default {
    name: "matchOrReleasesDialog",
    components: {
        MyContainer, vxetablebase
    },
    props: {
        queryInfo: {
            type: Object,
            default: () => {
                return {
                    wmsId: null,
                    goodsCode: null,
                    isRelease: null
                }
            }
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {},
            loading: false,
            tableCols,
            tableData: []
        }
    },
    async mounted() {
        this.ListInfo = this.queryInfo
        await this.getList()
    },
    methods: {
        async getList() {
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data, success } = await getMatchOrReleases(this.ListInfo)
            if (success) {
                this.tableData = data
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>