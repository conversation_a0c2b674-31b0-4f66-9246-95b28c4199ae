<template>
    <container v-loading="pageLoading">
      <template #header>
        <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>               
            <el-form-item label="时间:">
              <!-- <el-date-picker style="width: 100%" v-model="filter.yearMonthDay" type="date" format="yyyyMMdd"   value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker> -->
              <el-date-picker v-model="timerange" style="width:220px" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                range-separator="至" start-placeholder="开始" end-placeholder="结束" :picker-options="pickerOptions" :clearable="false" @change="onSearch"></el-date-picker>
            </el-form-item>     
            <el-form-item label="系列编码:"><el-input v-model="filter.seriesCode" clearable /></el-form-item>          
            <el-form-item label="商品编码:"><el-input v-model="filter.goodsCode" clearable /></el-form-item>                
            <el-form-item label="商品名称:"><el-input v-model="filter.goodsName" clearable /></el-form-item> 
            <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            </el-form-item>
        </el-form>
      </template>
       
      <ces-table ref="table" :that='that' :isIndex='true' :isSelection='true' @select='selectchange' @sortchange='sortchange'
         :hasexpand='true' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"
         :showsummary='true' :summaryarry='summaryarry'>
      </ces-table>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

      <el-drawer title="参数设置" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editparmVisible"
                direction="btt" size="'auto'" class="el-drawer__wrapper"  style="position:absolute;">
        <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
          <div class="drawer-footer">
            <el-button @click.native="editparmVisible = false">取消</el-button>
            <my-confirm-button type="submit"  :loading="editparmLoading" @click="onSetEditParm" />
         </div>
    </el-drawer>  
    </container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue"
import container from "@/components/my-container"
import {batchAddOrUpdateProduct, getPageDayReportList, addOrUpdateGoodsPercentagePoints, batchGetGoodsPercentagePointsByCode} from '@/api/financial/yyfy'
import MyConfirmButton from "@/components/my-confirm-button"
const tableCols = [
    {istrue:true, prop:'yearMonthDay', label:'年月日', width:'100', sortable:'custom', formatter:(row)=>formatTime(row.yearMonthDay,'YYYYMMDD')},
    {istrue:true, prop:'seriesCode', label:'系列编码', width:'150', sortable:'custom'},
    {istrue:true, prop:'goodsCode', label:'商品编码', width:'120', sortable:'custom'},
    {istrue:true, prop:'goodsName', label:'商品名称', width:'280', sortable:'custom'},
    {istrue:true, prop:'taoPercentagePoints', label:'淘宝提成点', width:'120', sortable:'custom',formatter:(row)=> !row.taoPercentagePoints?" ": (row.taoPercentagePoints*100).toFixed(2)+'%'},
    {istrue:true, prop:'pinPercentagePoints', label:'拼多多提成点', width:'120', sortable:'custom',formatter:(row)=> !row.pinPercentagePoints?" ": (row.pinPercentagePoints*100).toFixed(2)+'%'},
    {istrue:true, prop:'gongPercentagePoints', label:'工厂店提成点', width:'auto', sortable:'custom',formatter:(row)=> !row.gongPercentagePoints?" ": (row.gongPercentagePoints*100).toFixed(2)+'%'},
];

const tableHandles=[{label:'批量设置', handle:(that)=>that.onHand()},];
  const startDate = formatTime(dayjs().subtract(7,'day'), "YYYY-MM-DD");
  const endDate = formatTime(new Date(), "YYYY-MM-DD");
export default {
    name: 'YunhanAdminDayreportcoding',
    components: {container, cesTable, MyConfirmButton, },
    data() {
        return {
            that:this,
            list:[],
            summaryarry:{},
            filter: {
                goodsCode:null,
                seriesCode:null,
                goodsName:null,
                startDate:null,
                endDate:null,
                brandId:null,
            },
            timerange:[startDate,endDate],
            pager:{OrderBy:'', IsAsc:false},
            total:0,
            sels:[], 
            selids: [],
            ids: [],
            tableCols:tableCols,
            tableHandles:tableHandles,
            editparmVisible:false,
            pageLoading: false,
            listLoading:true,
            editparmLoading:false,
            autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
          },    
          pickerOptions:{
          disabledDate(time){
          return time.getTime()>Date.now();
        }
      },  
        };
    },

    async mounted() {
      await this.onSearch();
      this.initformparm()  
    },

    methods: {
    initformparm(){
       this.autoform.rule= [{type:'hidden',field:'id',title:'id',value: '',col:{span:12}},
                     {type:'InputNumber',field:'taoPercentagePoints',title:'淘宝提成点%',value: null,props:{min:0,precision:3,step:0.5},col:{span:6}},
                     {type:'InputNumber',field:'pinPercentagePoints',title:'拼多多提成点%',value: null,props:{min:0,precision:3,step:0.5},col:{span:6}},
                     {type:'InputNumber',field:'gongPercentagePoints',title:'工厂店提成点%',value: null,props:{min:0,precision:3,step:0.5},col:{span:6}},
                    ]
    },    
    async onSearch() {
       this.$refs.pager.setPage(1)
       this.getlist()      
    },
    async getlist(){
        if (!this.pager.OrderBy) this.pager.OrderBy="";
        var pager = this.$refs.pager.getPager()    
        this.filter.startDate =null;
        this.filter.endDate =null;
        if (this.timerange) {
          this.filter.startDate = this.timerange[0];
          this.filter.endDate = this.timerange[1];
        }   
        const params = {...pager,...this.pager,... this.filter}
        console.log('params',params)
        this.listLoading = true
        const res = await getPageDayReportList(params)
        this.listLoading = false
        if (!res?.success) return
        const data = res.data.list
        this.total = res.data.total
        data.forEach(d => {d._loading = false})
        this.list = data
        },
        //编辑
    async onHand(){
        if(this.selids.length==0)
        {
            this.$message({message: "请先选择",type: "warning",});
            this.editparmVisible = false
            return
        }
        
        this.editparmVisible = true
        var model={ ids: this.selids.join(),pinPercentagePoints: 0, taoPercentagePoints: 0, gongPercentagePoints : 0  }
        console.log('model',model)
        this.$nextTick(() => {
        var arr = Object.keys(this.autoform.fApi);
        if(arr.length >0) {
        this.autoform.fApi.setValue(model)
          }
        });
        },
    async onSetEditParm(){
        this.editparmLoading=true;
        this.$nextTick(() => {
        this.autoform.fApi.validate(async (valid, fail) => {
        if(valid){
            const formData = this.autoform.fApi.formData();
            formData.id = formData.id == '' ? 0 : formData.id;
            formData.ids =this.selids.join();
            //formData.Enabled=true;
            const res = await batchAddOrUpdateProduct(formData);
            if(res.code==1){
                this.getlist();
                this.editparmVisible=false;
            }
            }else{
            //todo 表单验证未通过
            }
        })
        this.editparmLoading=false;
      });
    },
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    selectchange:function(rows,row) {
      this.selids=[];
      this.ids=[];
      rows.forEach(f=>{
        this.selids.push(f.goodsCode);
        this.ids.push(f.id)
      })
    },    
    },
};
</script>

<style lang="scss" scoped>

</style>