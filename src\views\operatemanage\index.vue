<template>
    <my-container v-loading="pageLoading">
        <template #header> </template>
        <template>
            <div>
                <el-row :gutter="0" class="row-condition" style="margin-right: 50px;">
                    <el-col :span="3" :offset="1">
                        <div class="my-title">当前压单概况</div>
                    </el-col>
                    <el-col :span="18" :offset="1">
                        <div class="my-title" style="text-align: right;">
                            <el-link type="primary" target="_blank" @click="openGeneral()">历史压单</el-link>
                        </div>
                    </el-col>
                </el-row>
                <el-row style="margin-right: 50px;">
                    <el-col :span="3" :offset="1">
                        <el-card :body-style="{ padding: '0px' }" shadow="always">
                            <div class="grid-header">总压单量
                                <el-tooltip class="item" effect="dark" content="总压单量" placement="top-end"><span><i
                                            class="el-icon-question"></i></span></el-tooltip>
                            </div>
                            <div class="grid-text">
                                <span>{{ formattedTotalWaitOrderNum1 }}</span>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="3" :offset="1">
                        <el-card :body-style="{ padding: '0px' }" shadow="always">
                            <div class="grid-header">实际压单量
                                <el-tooltip class="item" effect="dark" content="总压单量" placement="top-end"><span><i
                                            class="el-icon-question"></i></span></el-tooltip>
                            </div>
                            <div class="grid-text">
                                <span>{{ formattedTotalWaitOrderNum2 }}</span>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="3" :offset="1">
                        <el-card :body-style="{ padding: '0px' }" shadow="always">
                            <div class="grid-header">预售压单量
                                <el-tooltip class="item" effect="dark" content="总压单量" placement="top-end"><span><i
                                            class="el-icon-question"></i></span></el-tooltip>
                            </div>
                            <div class="grid-text">
                                <span>{{ formattedTotalWaitOrderNum3 }}</span>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="3" :offset="1">
                        <el-card :body-style="{ padding: '0px' }" shadow="always">
                            <div class="grid-header">淘客压单量
                                <el-tooltip class="item" effect="dark" content="总压单量" placement="top-end"><span><i
                                            class="el-icon-question"></i></span></el-tooltip>
                            </div>
                            <div class="grid-text">
                                <span>{{ formattedTotalWaitOrderNum4 }}</span>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="3" :offset="1">
                        <el-card :body-style="{ padding: '0px' }" shadow="always">
                            <div class="grid-header">总压品数
                                <el-tooltip class="item" effect="dark" content="总压品数" placement="top-end"><span><i
                                            class="el-icon-question"></i></span></el-tooltip>
                            </div>
                            <div class="grid-text">
                                <span>{{ formattedTotalWaitOrderNum5 }}</span>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="3" :offset="1">
                        <el-card :body-style="{ padding: '0px' }" shadow="always">
                            <div class="grid-header">未完结采购单
                                <el-tooltip class="item" effect="dark" content="未完结采购单" placement="top-end"><span><i
                                            class="el-icon-question"></i></span></el-tooltip>
                            </div>
                            <div class="grid-text">
                                <span>{{ formattedTotalWaitOrderNum6 }}</span>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
                <br>
                <el-row style="padding-left: 1%;padding-right: 1%;">
                    <el-col :span="9" style="padding-right: 5px;">
                        <el-card class="abnormalcard">
                            <div slot="header" class="clearfix grid-header"
                                style="text-align:left;margin: 0;padding: 0;">
                                <span>运营组概况</span>
                            </div>
                            <div class="abnormalcard" style="margin:0px; word-break: break-all">
                                <div>
                                    <el-table :data="abnormalModel.yyItems">
                                        <!-- <el-table-column prop="firstTime" label="最早时间" width="180"></el-table-column> -->
                                        <el-table-column prop="name" label="运营组" width="80"></el-table-column>
                                        <el-table-column prop="totalWaitOrderNum" label="总压单量"
                                            width="80"></el-table-column>
                                        <el-table-column prop="sjWaitOrderNum" label="实际压单量"
                                            width="90"></el-table-column>
                                        <el-table-column prop="preSaleOrderNum" label="预售压单量"
                                            width="90"></el-table-column>
                                        <el-table-column prop="taoKeOrderNum" label="淘客压单量"
                                            width="90"></el-table-column>
                                        <el-table-column prop="totalWaitGoodNum" label="压品数"
                                            width="80"></el-table-column>
                                        <!-- <el-table-column prop="nonCompletedPurchase" label="未完结采购单" width="150"></el-table-column>
                    <el-table-column prop="nonInAmont" label="未入库总金额" width="150"></el-table-column> -->
                                    </el-table>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="15">
                        <el-card class="abnormalcard">
                            <div slot="header" class="clearfix grid-header"
                                style="text-align:left;margin: 0;padding: 0;">
                                <span>运营异常订单</span><span class="my-last-time">&nbsp;&nbsp;&nbsp;&nbsp;最晚更新{{
                                    lastUpdateTime }}</span>
                            </div>
                            <div style="text-align:left;margin: 0;padding: 0;">
                                <el-form class="ad-form-query3" :inline="true">
                                    <el-form-item label="">
                                        <el-date-picker
                                            style="width:140px;height:26px;font-size:15px;margin-left:0px;color:#409EFF;"
                                            v-model="filter.backupDate" @change="getRightList" v-loading="loading"
                                            :picker-options="pickOptions" placeholder="日期" type="date"
                                            format="yyyy-MM-dd">
                                        </el-date-picker>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" @click="getRightList">刷新</el-button>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" @click="resetCondition">重置</el-button>
                                    </el-form-item>
                                    <el-form-item label="">
                                        <el-select style="width:130px;" filterable v-model="filter.platform"
                                            placeholder="平台" :clearable="true" :collapse-tags="true"
                                            @change="changePlatformTotal">
                                            <el-option v-for="item in platformList" :key="item.value"
                                                :label="item.label" :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="">
                                        <el-select v-model="filter.shopCode" filterable placeholder="店铺"
                                            :clearable="true" :collapse-tags="true" @change="getRightList">
                                            <el-option v-for="item in shopList" :key="item.shopCode"
                                                :label="item.shopName" :value="item.shopCode">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="">
                                        <el-select style="width:130px;" filterable v-model="filter.groupId"
                                            placeholder="运营组" :clearable="true" :collapse-tags="true"
                                            @change="getRightList">
                                            <el-option v-for="item in groupList" :key="item.key" :label="item.value"
                                                :value="item.key">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="">
                                        <el-select filterable v-model="filter.wmsCoId" clearable placeholder="请选择发货仓"
                                            style="width: 130px" @change="getRightList">
                                            <el-option v-for="item in newWareHouseList" :key="item.name"
                                                :label="item.name" :value="item.wms_co_id" />
                                        </el-select>
                                        <!-- <el-select v-model="filter.sendWarehouse" filterable style="width:130px;" placeholder="发货仓" @change="getRightList" :clearable="true">
                                            <el-option v-for="item in sendWarehouseList" :key="item.value" :label="item.label" :value="item.value" />
                                        </el-select> -->
                                    </el-form-item>
                                </el-form>
                            </div>
                            <el-row :gutter="0" class="row-condition">
                                <el-col :span="5" :offset="1">
                                    <div class="my-title2">{{ this.yunYingData.dutyDept }} </div>
                                    <div class="my-title2"><span type="info" class="my-principal2">负责人：{{
                                        this.yunYingData.dutyDeptPrincipal }}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5" :offset="1">
                                    <div class="my-title2">订单数</div>
                                    <div class="my-title2">共 <span class="canclick2"
                                            @click="onShowDetailByDutyDept(yunYingData.dutyDept, yunYingData.qty, yunYingData.dutyDeptPrincipal)">{{
                                                this.yunYingData.qty }}
                                        </span><span class="my-unit2">个</span></div>
                                </el-col>
                                <el-col :span="5" :offset="1">
                                    <div class="my-title2">平均耗时</div>
                                    <div class="my-title2"><span class="canclick2"
                                            @click="onSearchChartsTime(2, yunYingData.dutyDept)">{{
                                                minuteToDayHourStr(this.yunYingData.timeConsuming / this.yunYingData.qtyAll)
                                            }}
                                        </span></div>
                                </el-col>
                                <el-col :span="5" :offset="1">
                                    <div class="my-title2">销售额</div>
                                    <div class="my-title2"><span class="canclick2"
                                            @click="onSearchChartsAmount(2, yunYingData.dutyDept)">{{
                                                this.yunYingData.goodsAmount }}</span><span class="my-unit2">元</span></div>
                                </el-col>
                            </el-row>
                            <el-row style="margin-left: -40px;">
                                <el-col :span="11" :offset="1" v-for="(dataItem, dataIndex) in this.yunYingData.data"
                                    :key="dataIndex">
                                    <el-card :body-style="{ padding: '0px', margin: '10px' }" shadow="always">
                                        <div class="grid-header2">
                                            <el-row style="margin-right:0px;">
                                                <el-col :span="15" :title="dataItem.errorType">
                                                    <span @dblclick="doCopy(dataItem.errorType)">{{ (dataItem.errorType
                                                        || "").substring(0, 9) }}</span>
                                                    <el-tooltip class="item" effect="dark"
                                                        :content="dataItem.errorTypeDesc" placement="top-end"><span><i
                                                                class="el-icon-question"></i></span></el-tooltip>
                                                </el-col>
                                                <el-col :span="9"><span type="info" class="my-principal2">负责人：{{
                                                    dataItem.errorTypePrincipal }}</span>
                                                </el-col>
                                            </el-row>
                                        </div>
                                        <div class="grid-text2">
                                            <el-row style="margin-right: 0px;">
                                                <el-col :span="8" class="my-content2">
                                                    <span class="canclick2 my-left2"
                                                        @click="onSearchChartsTime(3, yunYingData.dutyDept, dataItem)">{{
                                                            minuteToDayHourStr(dataItem.timeConsuming / dataItem.qtyAll)
                                                        }}</span>
                                                </el-col>
                                                <el-col :span="8" class="my-content2">
                                                    <span class="canclick2 my-mid"
                                                        @click="onShowDetail(yunYingData.dutyDept, dataItem)">{{
                                                            dataItem.qty }}</span>
                                                </el-col>
                                                <el-col :span="8" class="my-content2">
                                                    <span class="canclick2 my-right2"
                                                        @click="onSearchChartsAmount(3, yunYingData.dutyDept, dataItem)">{{
                                                            dataItem.goodsAmount }}<span class="my-unit2">元</span></span>
                                                </el-col>
                                            </el-row>
                                        </div>
                                    </el-card>
                                </el-col>
                            </el-row>
                        </el-card>
                    </el-col>
                </el-row>
                <el-dialog :visible.sync="dialogVisible" v-dialogDrag :show-close="false">
                    <abnormalgeneral style="height: 500px"></abnormalgeneral>
                </el-dialog>
            </div>
        </template>

        <el-dialog :visible.sync="orderDetail.visible" width="63%" :show-close="false" :title="orderDetail.title">
            <el-tabs v-model="orderDetailActiveName" style="height:670px;" tab-position="left"
                @tab-click="tabOrderDetailChange">
                <el-tab-pane label="今日数据" name="tabOrderDetailTable" style="height:100%;">
                    <div>
                        <div style="margin-bottom:10px;">
                            <el-descriptions :column="4" size="mini" border>
                                <el-descriptions-item label="部门">{{ orderDetail.showRow.dutyDept
                                    }}</el-descriptions-item>
                                <el-descriptions-item label="原因">{{ orderDetail.showRow.errorType
                                    }}</el-descriptions-item>
                                <el-descriptions-item label="负责人">{{ orderDetail.showRow.errorTypePrincipal
                                    }}</el-descriptions-item>
                                <el-descriptions-item label="订单数量">{{ orderDetail.showRow.qty }}</el-descriptions-item>
                                <el-descriptions-item label="建议">{{ orderDetail.showRow.errorTypeDesc
                                    }}</el-descriptions-item>
                            </el-descriptions>
                        </div>

                        <el-form class="ad-form-query3" :inline="true" size="mini" @submit.native.prevent>
                            <el-form-item label="付款日期">
                                <el-date-picker style="width:240px" v-model="orderDetail.filter.timerange"
                                    type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                                    start-placeholder="开始" end-placeholder="结束" :picker-options="pickerOptions"
                                    @change="onSearchOrderDetail"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="平台">
                                <el-select style="width:150px" v-model="orderDetail.filter.platform" placeholder="请选择"
                                    :clearable="true" :disabled="filter.platform > 0" :collapse-tags="true"
                                    @change="changePlatform">
                                    <el-option v-for="item in platformList" :key="item.value" :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="店铺">
                                <el-select style="width:180px" v-model="orderDetail.filter.shopCode" placeholder="请选择"
                                    :clearable="true" :disabled="filter.shopCode != null && filter.shopCode != ''"
                                    :collapse-tags="true" @change="onSearchOrderDetail">
                                    <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                        :value="item.shopCode">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="订单类型">
                                <el-input v-model="orderDetail.filter.orderType" style="width:150px" placeholder="订单类型"
                                    @change="onSearchOrderDetail"></el-input>
                            </el-form-item>
                            <el-form-item label="运营组">
                                <el-select style="width:120px" v-model="orderDetail.filter.groupId" placeholder="请选择"
                                    :clearable="true" :disabled="filter.groupId > 0" :collapse-tags="true"
                                    @change="onSearchOrderDetail">
                                    <el-option v-for="item in groupList" :key="item.key" :label="item.value"
                                        :value="item.key">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="采购组">
                                <el-select v-model="orderDetail.filter.brandId" style="width:120px" :clearable="true"
                                    placeholder="请选择" @change="onSearchOrderDetail">
                                    <el-option v-for="item in brandlist" :key="item.key" :label="item.value"
                                        :value="item.key" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="内部订单号">
                                <el-input v-model="orderDetail.filter.orderNoInner" style="width:120px"
                                    placeholder="内部订单号" @change="onSearchOrderDetail"></el-input>
                            </el-form-item>
                            <el-form-item label="是否处理">
                                <el-select v-model="orderDetail.filter.isDealByErp" style="width:120px"
                                    :clearable="true" placeholder="请选择" @change="onSearchOrderDetail">
                                    <el-option v-for="item in yesNoList" :key="item.key" :label="item.value"
                                        :value="item.key" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="异常类型">
                                <el-select v-model="orderDetail.filter.errorType" style="width:120px" :clearable="true"
                                    placeholder="请选择" :disabled="!!orderDetail.showRow.errorType"
                                    @change="onSearchOrderDetail">
                                    <el-option v-for="item in errorTypeList" :key="item.key" :label="item.value"
                                        :value="item.key" />
                                </el-select>
                            </el-form-item>
                            <br />
                            <el-form-item>
                                <el-button type="primary" @click="onSearchOrderDetail">刷新</el-button>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="onExportOrderDetail">导出</el-button>
                            </el-form-item>
                            <el-form-item v-if="enableChange">
                                <el-button type="danger" @click="onChangeErrorType">修改异常类型</el-button>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="success" size="mini"
                                    @click="copyOrderDetailOrderNos">一键复制所有订单号</el-button>
                            </el-form-item>
                        </el-form>

                        <ces-table ref="tableOrderDetail" :that='orderDetail.that' :isIndex='true' :hasexpand='false'
                            @sortchange='sortchangeOrderDetail' :tableData='orderDetail.tableData'
                            :isSelection="orderDetail.enableSelect" @select="selectOrderDetail"
                            :tableCols='orderDetail.tableCols' :loading="orderDetail.listLoading"
                            :isSelectColumn="false" style="height:420px;">
                        </ces-table>

                        <!--分页-->
                        <my-pagination ref="orderDetailPager" :total="orderDetail.total"
                            @get-page="getOrderDetailList" />

                        <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true"
                            :modal-append-to-body="false" :visible.sync="addFormVisible" direction="btt" size="'auto'"
                            class="el-drawer__wrapper" style="position:absolute;">
                            <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"
                                style="margin-top:10px;" />
                            <div class="drawer-footer">
                                <el-button @click.native="addFormVisible = false">取消</el-button>
                                <my-confirm-button type="submit" :loading="addLoading" @click="onAddSubmit" />
                            </div>
                        </el-drawer>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="历史分析" name="tabOrderDetailChart" style="height:100%;">
                    <div>
                        <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                            <el-form-item label="日期:">
                                <el-date-picker style="width:230px" v-model="charts.filter.timeRange" type="daterange"
                                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                                    start-placeholder="开始" end-placeholder="结束" :picker-options="pickerOptions"
                                    :clearable="false" @change="onSearchCharts"></el-date-picker>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="onSearchCharts">刷新</el-button>
                            </el-form-item>
                        </el-form>
                        <div style="margin-top:20px;" v-loading="charts.loading" element-loading-text="加载中"
                            element-loading-spinner="el-icon-loading">
                            <div id="myChartOrderCount"></div>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </el-dialog>

        <el-dialog :visible.sync="charts.visible" width="54%" append-to-body :title="charts.title" :show-close="false">
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="日期:">
                    <el-date-picker style="width:230px" v-model="charts.filter.timeRange" type="daterange"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始"
                        end-placeholder="结束" :picker-options="pickerOptions" :clearable="false"
                        @change="onSearchCharts"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearchCharts">刷新</el-button>
                </el-form-item>
            </el-form>
            <div style="margin-top:20px;" v-loading="charts.loading" element-loading-text="加载中"
                element-loading-spinner="el-icon-loading">
                <div id="myChart"></div>
            </div>
        </el-dialog>
    </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import * as echarts from "echarts";
import dayjs from "dayjs";
import { queryAbnormalGeneralNow } from '@/api/inventory/abnormal'
import MyConfirmButton from '@/components/my-confirm-button'
import MyContainer from '@/components/my-container'
import { formatTime1 } from "@/utils";
import abnormalgeneral from '@/views/inventory/components/abnormalgeneral'
import {
    getOrderErrorReport,
    getOrderDetailOrderNos,
    exportOrderDetail,
    getDutyDept,
    pageOrderErrorType,
    changeOrderDetailErrorType,
    getLastUpdateTime,
    getOrderErrorChartsTime,
    getOrderErrorChartsAmount,
    getOrderErrorOrder,
    getOrderErrorOrderDetail,
    getOrderErrorChartsOrderCount,
    changeIsDealByErp,
} from '@/api/order/ordererror';
import { getGroupKeyValue } from '@/api/operatemanage/base/product';
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { rulePlatform, ruleSendWarehouse } from "@/utils/formruletools";
import { getAllProBrand } from '@/api/inventory/warehouse';
import { formatPlatform, formatSendWarehouse } from "@/utils/tools";
import { getAllWarehouse } from '@/api/inventory/warehouse';
var tableCols = [
    {
        istrue: true, prop: 'isDealByErp', label: '是否处理', width: '90', sortable: 'custom', type: 'switch'
        , isDisabled: (row, that) => that.setIsDealByErpDisabled(row)
        , change: (row, that) => that.changeIsDealByErp(row)
    },
    { istrue: true, prop: 'orderNoInner', label: '内部订单号', width: '100', sortable: 'custom', type: 'click', handle: (that, row, column, cell) => that.canclick(row, column, cell) },
    { istrue: true, prop: 'shopCode', label: '店铺名称', width: 'auto', sortable: 'custom', formatter: row => row.shopName },
    { istrue: true, prop: 'payTime', label: '付款日期', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'status', label: '状态', width: '60' },
    { istrue: true, prop: 'errorType', label: '异常类型', width: '140', sortable: 'custom' },
    { istrue: true, prop: 'orderType', label: '订单类型', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'sendWarehouse', label: '发货仓', width: '90', sortable: 'custom', formatter: row => formatSendWarehouse(row.sendWarehouse) },
    { istrue: true, prop: 'platform', label: '平台站点', width: '80', sortable: 'custom', formatter: row => formatPlatform(row.platform) },
];

var tableColsDetail = [
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: 'auto' },
    { istrue: true, prop: 'goodsImage', label: '图片', width: '70', type: 'imageGoodsCode', goods: { code: 'goodsCode', name: 'goodsName' } },
    { istrue: true, prop: 'groupName', label: '运营组', width: '90', sortable: 'custom', },
    { istrue: true, prop: 'brandName', label: '采购组', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'refundStatus', label: '退款状态', width: '100', sortable: 'custom' },
];
export default {
    name: 'Roles',
    components: { MyConfirmButton, MyContainer, abnormalgeneral, cesTable },
    data() {
        return {
            newWareHouseList: [],
            that: this,
            today: formatTime1(new Date(), "yyyy-MM-dd"),
            filter: {
                backupDate: null,//备份日期
                platform: null,
                shopCode: '',
                groupId: null,
                sendWarehouse: null,
            },
            platformList: [],
            shopList: [],
            groupList: [],
            sendWarehouseList: [],
            dataList: [],
            yesNoList: [
                { key: true, value: '是' },
                { key: false, value: '否' },
            ],
            yunYingData: {},
            orderDetail: {
                title: "订单",
                visible: false,
                tableCols: tableCols,
                tableData: [],
                total: 0,
                sels: [],
                listLoading: false,
                pager: { OrderBy: "payTime", IsAsc: false },
                pageLoading: false,
                that: this,
                filter: {
                    errorType: "",
                    timerange: [],
                    startDate: null,
                    endDate: null,
                    platform: null,
                    shopCode: '',
                    groupId: null,
                    brandId: null,
                    orderNoInner: null,
                    orderType: null,
                    dutyDept: null,
                    isDealByErp: null,
                    isTotal: false,
                    isDept: false,
                    isErrorType: false,
                },
                showRow: {},//明细的行
                enableSelect: true,//允许选择
            },
            orderDetailSingle: {
                title: "订单明细",
                visible: false,
                tableCols: tableColsDetail,
                tableData: [],
                total: 0,
                sels: [],
                listLoading: false,
                pager: { OrderBy: "goodsCode", IsAsc: false },
                pageLoading: false,
                that: this,
                filter: {
                    orderNoInner: null,
                },
                showRow: {},//明细的行
            },
            //自动表单 - Start
            autoform: {
                fApi: {},
                rule: [],
                options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
            },
            addFormVisible: false,
            addLoading: false,
            formtitle: "修改异常类型",
            //自动表单 -End
            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            enableChange: true,//是否允许修改
            loading: false,
            lastUpdateTime: null,
            //图表配置 Start
            charts: {
                myChart: null,
                chartsType: null,
                Ylist: [
                    { value: 0, unit: "" },
                ],
                loading: false,
                visible: false,
                title: null,
                filter: {
                    isTotal: false,
                    isDept: false,
                    isErrorType: false,
                    dutyDept: null,
                    errorType: null,
                    startDate: null,
                    endDate: null,
                    timeRange: [formatTime1(dayjs().subtract(6, 'day'), "yyyy-MM-dd"), formatTime1(new Date(), "yyyy-MM-dd")],
                },
            },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            //图表配置 End
            platformList: [],
            shopList: [],
            groupList: [],
            brandlist: [],
            sendWarehouseList: [],
            collapseActiveNames: [],//折叠面板激活页面
            orderDetailActiveName: "tabOrderDetailTable",

            abnormalModel: { firstTime: '', totalWaitOrderNum: 0, totalWaitGoodNum: 0, cGItems: [], yYItems: [] },
            abnormalModelLoading: false,
            pageLoading: false,
            dialogVisible: false,
        }
    },
    async mounted() {
        this.filter.backupDate = this.today;
        await this.setPlatform();
        await this.setGroupSelect();
        await this.setBandSelect();
        await this.setWarehouse();

        await this.getRightList();

        await this.getAbnormal();

        var res = await getAllWarehouse();
        this.newWareHouseList = res.data.filter((x) => { return x.name.indexOf('代发') < 0; });
    },
    methods: {
        //加载部门
        async getAbnormal() {
            this.pageLoading = true;
            const res = await queryAbnormalGeneralNow({ type: 1 })
            this.pageLoading = false;
            if (!res?.success) {
                return
            }
            this.abnormalModel = res.data;
        },
        async setBandSelect() {
            var res = await getAllProBrand();
            if (!res?.success) return;
            this.brandlist = res.data;
        },
        async openGeneral() {
            this.dialogVisible = true;
        },
        async setWarehouse() {
            var whrule = await ruleSendWarehouse();
            this.sendWarehouseList = whrule.options;
        },
        async setGroupSelect() {
            const res = await getGroupKeyValue({});
            this.groupList = res.data;
        },
        //设置平台下拉
        async setPlatform() {
            var pfrule = await rulePlatform();
            this.platformList = pfrule.options;
        },
        async changePlatform(val) {
            const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list;
            this.orderDetail.filter.shopCode = "";
            this.onSearchOrderDetail();
        },
        async changePlatformTotal(val) {
            const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list;
            this.filter.shopCode = "";
            await this.getRightList();
        },
        async getRightList() {
            await this.getOrderErrorReport();
            await this.getLastUpdateTime();
        },
        //重置
        async resetCondition() {
            this.filter.platform = null;
            this.filter.shopCode = '';
            this.filter.groupId = null;
            this.filter.sendWarehouse = null;
            await this.getRightList();
        },
        myFormatPlatform(val) {
            return formatPlatform(val);
        },
        myFormatSendWarehouse(val) {
            return formatSendWarehouse(val);
        },
        //获取
        async getLastUpdateTime() {
            const res = await getLastUpdateTime()
            if (!res?.success) return
            this.lastUpdateTime = res.data;
        },
        //获取看版数据
        async getOrderErrorReport() {
            var backupDate = formatTime1(this.filter.backupDate, "yyyy-MM-dd");
            var params = {
                ...this.filter,
                backupDate: backupDate,
            };
            this.pageLoading = true;
            const res = await getOrderErrorReport(params)
            this.pageLoading = false;
            if (!res?.success) {
                //this.dataList =[];
                return
            }
            this.dataList = res.data;
            if (this.dataList && this.dataList.length > 0) {
                this.yunYingData = res.data.find(f => f.dutyDept == "运营");
                console.log(this.yunYingData)
            }
        },
        //分钟转天+小时
        minuteToDayHour(minute) {
            const days = Math.floor(minute / 1440);
            const hours = Math.floor(minute % 1440 / 60);
            const forMatDate = {
                day: days,
                hour: hours,
            };
            return forMatDate;
        },
        minuteToDayHourStr(minute) {
            var timeConsuming = this.minuteToDayHour(minute);
            var str = (timeConsuming.day > 0 ? timeConsuming.day + '天' : '') + (timeConsuming.hour > 0 ? timeConsuming.hour + '小时' : '');
            return str || '0小时';
        },
        doCopy: function (val) {
            let that = this;
            this.$copyText(val).then(function (e) {
                that.$message({ message: "内容已复制到剪切板！", type: "success" });
            }, function (e) {
                that.$message({ message: "抱歉，复制失败！", type: "warning" });
            })
        },
        //改变折叠面板
        changeCollapse(val) {

        },
        //是否处理列禁止操作
        setIsDealByErpDisabled(row) {
            var backupDate = formatTime1(this.filter.backupDate, "yyyy-MM-dd");
            if (backupDate && backupDate != this.today) {
                return true;
            }
            return false;
        },
        async changeIsDealByErp(row) {
            var params = {
                orderNoInner: row.orderNoInner,
                isDealByErp: row.isDealByErp,
            };
            console.log(params)
            const res = await changeIsDealByErp(params);
            if (!res?.success) return;
            this.$message({ message: "操作成功", type: "success" });
        },
        //加载部门
        async setDutyDept() {
            let rule = { validate: [{ type: 'string', required: true, message: '请选择' }], options: [{ value: null, label: '请选择' }] }
            var res = await getDutyDept();
            var data = [];
            for (var k = 0; k < res.data.length; k++) {
                data.push({ value: res.data[k], label: res.data[k] });
            }
            rule.options = data;
            return rule;
        },
        async updateRuleErrorType(val) {
            await this.autoform.fApi.setValue({ errorType: '' });
            await this.autoform.fApi.updateRule('errorType', { ... await this.ruleErrorType(val) });
            await this.autoform.fApi.sync('errorType');
        },
        async ruleErrorType(val) {
            let rule = { validate: [{ type: 'string', required: true, message: '请选择' }], options: [{ value: null, label: '请选择' }] }
            if (!val) return rule;
            var params = {
                dutyDept: val,
                currentPage: 1,
                pageSize: 1000
            };
            var res = await pageOrderErrorType(params);

            if (!res?.success) { return }
            if (res.data.list && res.data.list.length > 0)
                res.data.list.forEach(f => { rule.options.push({ value: f.errorType, label: f.errorType }) })
            return rule;
        },
        async onEdit() {
            this.formtitle = '修改异常类型';
            this.addFormVisible = true;
            var that = this;
            this.autoform.rule = [
                { type: 'hidden', field: 'id', title: 'id', value: '' },
                {
                    type: 'select', field: 'dutyDept', title: '部门', validate: [{ type: 'string', required: true, message: '请选择部门' }], value: "",
                    options: [], async update(val, rule) { if (val) { await that.updateRuleErrorType(val) } }, ...await this.setDutyDept(),
                },
                {
                    type: 'select', field: 'errorType', title: '原因', validate: [{ type: 'string', required: true, message: '请选择' }], value: "",
                    options: []
                },
            ];

            var arr = Object.keys(this.autoform.fApi);
            if (arr.length > 0)
                this.autoform.fApi.resetFields()

            var data = {
                id: this.orderDetail.sels.map(a => a.orderNoInner).join(","),
                dutyDept: this.orderDetail.showRow.dutyDept,
                errorType: this.orderDetail.showRow.errorType,
            };
            setTimeout(async () => {
                await this.autoform.fApi.setValue({ ...data });
            }, 500);
        },
        async onAddSubmit() {
            this.addLoading = true;
            await this.autoform.fApi.validate(async (valid, fail) => {
                if (valid) {
                    const formData = this.autoform.fApi.formData();
                    formData.id = formData.id ? formData.id : "";
                    formData.Enabled = true;
                    const res = await changeOrderDetailErrorType(formData);
                    if (res.code == 1) {
                        await this.onSearchOrderDetail();
                        await this.getList();

                        //更新订单数量
                        setTimeout(() => {
                            this.updateQty();
                        }, 500);
                        this.addFormVisible = false;
                    }
                } else {
                    //todo 表单验证未通过
                }
            })
            this.addLoading = false;
        },
        updateQty() {
            if (this.dataList && this.dataList.length > 0) {
                for (const key in this.dataList) {
                    if (this.dataList[key].dutyDept == this.orderDetail.showRow.dutyDept) {
                        var data = this.dataList[key].data;
                        if (data && data.length > 0) {
                            for (const i in data) {
                                if (data[i].errorType == this.orderDetail.showRow.errorType) {
                                    this.orderDetail.showRow.qty = data[i].qty;
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
            this.orderDetail.showRow.qty = 0;
        },
        //==订单明细查询 Start===============================================
        //单击单元格
        async canclick(row, column, cell) {
            if (column.prop == 'orderNoInner') {
                await this.onShowDetailSingle(row);
            }
        },
        //查询明细第一页
        async onSearchOrderDetail() {
            this.orderDetail.visible = true;
            this.orderDetail.filter.isErrorType = !!this.orderDetail.showRow.errorType;
            this.orderDetail.filter.isDept = !this.orderDetail.filter.isErrorType && !!this.orderDetail.showRow.dutyDept;
            this.orderDetail.filter.isTotal = !this.orderDetail.filter.isErrorType && !this.orderDetail.filter.isDept;
            this.orderDetail.title = this.orderDetail.showRow.errorType ? ("原因：" + this.orderDetail.showRow.errorType) :
                (this.orderDetail.showRow.dutyDept ? "部门：" + this.orderDetail.showRow.dutyDept : "整体");
            if (this.orderDetailActiveName == "tabOrderDetailChart") {
                this.tabOrderDetailChange();
            }
            this.enableChange = true;
            this.orderDetail.enableSelect = true;
            var backupDate = formatTime1(this.filter.backupDate, "yyyy-MM-dd");
            if (backupDate && backupDate != this.today)//||this.orderDetail.filter.isTotal||this.orderDetail.filter.isDept)
            {
                this.enableChange = false;
                this.orderDetail.enableSelect = false;
            }
            setTimeout(async () => {
                this.$refs.orderDetailPager.setPage(1);
                await this.getOrderDetailList();
            }, 100);
        },
        //明细排序查询
        async sortchangeOrderDetail(column) {
            if (!column.order)
                this.orderDetail.pager = {};
            else {
                this.orderDetail.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearchOrderDetail();
        },
        //明细查询条件
        getOrderDetailCondition() {
            var pager = this.$refs.orderDetailPager.getPager();
            var page = this.orderDetail.pager;
            this.orderDetail.filter.startDate = null;
            this.orderDetail.filter.endDate = null;
            var backupDate = formatTime1(this.filter.backupDate, "yyyy-MM-dd");
            if (this.orderDetail.filter.timerange && this.orderDetail.filter.timerange.length > 1) {
                this.orderDetail.filter.startDate = this.orderDetail.filter.timerange[0];
                this.orderDetail.filter.endDate = this.orderDetail.filter.timerange[1];
            }
            var params = {
                ...params,
                ...pager,
                ...page,
                ...this.filter,
                ...this.orderDetail.filter,
                backupDate,
            }

            return params;
        },
        //明细查询
        async getOrderDetailList() {
            this.orderDetail.visible = true;
            setTimeout(async () => {
                var params = this.getOrderDetailCondition();
                if (params === false) {
                    return false;
                }
                this.orderDetail.listLoading = true;
                const res = await getOrderErrorOrder(params);
                this.orderDetail.listLoading = false;
                if (!res?.success) {
                    return
                }
                this.orderDetail.total = res.data.total;
                const data = res.data.list;
                data.forEach(d => {
                    d._loading = false
                })
                this.orderDetail.tableData = data;
            }, 200);

        },
        async onShowDetailByTotal() {
            var data = {
                qty: this.totalOrderQty,
            };
            await this.onShowDetail(null, data);
        },
        async onShowDetailByDutyDept(dutyDept, qty, principal) {
            var data = {
                qty,
                errorTypePrincipal: principal
            };
            await this.onShowDetail(dutyDept, data);
        },
        //查看明细
        async onShowDetail(dutyDept, data) {
            this.orderDetail.filter.errorType = data.errorType;
            this.orderDetail.filter.dutyDept = dutyDept;
            this.orderDetail.showRow = { dutyDept, ...data };
            await this.onSearchOrderDetail();
        },
        async copyOrderDetailOrderNos() {
            var params = this.getOrderDetailCondition();
            if (params === false) {
                return false;
            }
            const res = await getOrderDetailOrderNos(params);
            if (!res?.success) {
                return false;
            }
            this.doCopy(res.data);
        },
        //导出明细
        async onExportOrderDetail() {
            var params = this.getOrderDetailCondition();
            if (!params) {
                return false;
            }
            const res = await exportOrderDetail(params);
            if (!res?.data) return;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.setAttribute('download', '订单异常明细_' + params.errorType + new Date().toLocaleString() + '.xlsx');
            aLink.click();
        },
        //修改异常类型
        async onChangeErrorType() {
            //是否已选择
            if (!this.orderDetail.sels || this.orderDetail.sels.length == 0) {
                this.$message({ message: "请先选择数据", type: "warning" });
                return false;
            }
            //弹窗修改内容
            await this.onEdit();
        },
        //选择订单明细
        selectOrderDetail(sel) {
            this.orderDetail.sels = sel;
        },
        //==订单明细查询 End=================================================
        //==图表查询 Start==================================================
        setSearchChartsCondition(type, dutyDept, item) {
            this.charts.filter.isTotal = type == 1;
            this.charts.filter.isDept = type == 2;
            this.charts.filter.isErrorType = type == 3;
            this.charts.filter.dutyDept = dutyDept;
            this.charts.filter.errorType = item?.errorType;
            var backupDate = formatTime1(this.filter.backupDate, "yyyy-MM-dd");
            this.charts.filter.backupDate = backupDate;
        },
        //耗时图表查询。type:1总，2部门，3原因
        async onSearchChartsTime(type, dutyDept, item) {
            this.setSearchChartsCondition(type, dutyDept, item);
            this.charts.chartsType = "time";
            await this.onSearchCharts();
        },
        //销售额图表查询。type:1总，2部门，3原因
        async onSearchChartsAmount(type, dutyDept, item) {
            this.setSearchChartsCondition(type, dutyDept, item);
            this.charts.chartsType = "amount";
            await this.onSearchCharts();
        },
        //订单数查询。type:1总，2部门，3原因
        async onSearchChartsOrderCount() {
            this.charts.filter.isTotal = this.orderDetail.filter.isTotal;
            this.charts.filter.isDept = this.orderDetail.filter.isDept;
            this.charts.filter.isErrorType = this.orderDetail.filter.isErrorType;
            this.charts.filter.dutyDept = this.orderDetail.showRow?.dutyDept;
            this.charts.filter.errorType = this.orderDetail.showRow?.errorType;
            var backupDate = formatTime1(this.filter.backupDate, "yyyy-MM-dd");
            this.charts.filter.backupDate = backupDate;
            this.charts.chartsType = "orderCount";
            await this.onSearchCharts();
        },
        //公共查询图表
        async onSearchCharts() {
            if (this.charts.filter.timeRange && this.charts.filter.timeRange.length > 1) {
                this.charts.filter.startDate = this.charts.filter.timeRange[0];
                this.charts.filter.endDate = this.charts.filter.timeRange[1];
            }
            var backupDate = formatTime1(this.filter.backupDate, "yyyy-MM-dd");
            var params = { ...this.filter, ...this.charts.filter, backupDate };
            this.charts.loading = true;
            var res = null;
            var title = "";
            var unit = "";
            var chartsId = "myChart";
            var titleDialog = this.charts.filter.errorType ? ("原因：" + this.charts.filter.errorType) :
                (this.charts.filter.dutyDept ? "部门：" + this.charts.filter.dutyDept : "整体");
            if (this.charts.chartsType == "time") {
                res = await getOrderErrorChartsTime(params);
                this.charts.loading = false;
                if (!res?.code) {
                    return false;
                }
                title = "平均耗时";
                unit = "小时";
                this.charts.visible = true;
                this.charts.title = titleDialog;
            }
            else if (this.charts.chartsType == "amount") {
                title = "销售额";
                unit = "元";
                res = await getOrderErrorChartsAmount(params);
                this.charts.loading = false;
                if (!res?.code) {
                    return false;
                }
                this.charts.visible = true;
                this.charts.title = titleDialog;
            }
            else if (this.charts.chartsType == "orderCount") {
                title = "订单数";
                unit = "个";
                res = await getOrderErrorChartsOrderCount(params);
                this.charts.loading = false;
                if (!res?.code) {
                    return false;
                }
                chartsId = "myChartOrderCount";
            }

            setTimeout(async () => {
                var chartDom = document.getElementById(chartsId);
                this.charts.myChart && this.charts.myChart.dispose();

                this.charts.myChart = echarts.init(chartDom);

                var option = await this.Getoptions(res.data, title, unit);
                await option && this.charts.myChart.setOption(option);
            }, 100);
        },
        //折线/柱形图图表配置
        async Getoptions(element, title, unit) {
            var colors = [
                "#5470C6",
                "#c77eb5",
                "#EE6666",
                "#409EFF",
                "#00ae9d",
                "#67C23A",
            ];
            var series = [];
            element.series.forEach((s) => {
                series.push({ smooth: true, ...s, itemStyle: { normal: { label: { show: true } } }, });
            });
            var legendData = element.legend || [];
            var yAxis = [];
            var left = true;
            var leftOffset = 0;
            var rightOffet = 0;
            var ii = 0;
            this.charts.Ylist.forEach((s) => {
                yAxis.push({
                    type: "value",
                    name: s.label,
                    show: true,
                    axisLabel: {
                        formatter: "{value} " + unit,
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: colors[ii++],
                        },
                    },
                    position: left ? "left" : "right",
                    offset: left ? leftOffset : rightOffet,
                });
                left ? (leftOffset += 50) : (rightOffet += 50);
                left = !left;
            });
            var option = {
                title: { text: title },
                tooltip: {
                    trigger: "axis",
                    textStyle: { align: "left" },
                },
                legend: {
                    formatter: function (name) {
                        return echarts.format.truncateText(
                            name,
                            200,
                            "10px Microsoft Yahei",
                            "..."
                        );
                    },
                    tooltip: {
                        show: true,
                    },
                    data: legendData,
                    type: "scroll",
                    pageIconColor: "#409EFF",
                    pageIconInactiveColor: "#909399",
                    width: "75%",
                },
                grid: {
                    left: "3%",
                    right: "3%",
                    bottom: "3%",
                    containLabel: true,
                },
                toolbox: {
                    feature: {
                        magicType: { show: true, type: ["line", "bar"] },
                        //restore: { show: false },
                    },
                },
                xAxis: {
                    type: "category",
                    data: element.xAxis,
                    axisLabel: {
                        interval: 0,
                        rotate: 40
                    },
                },
                yAxis: yAxis,
                series: series,
            };
            return option;
        },
        //==图表查询 End=====================================================

        //==订单明细-单个查询 Start===============================================
        //查询明细第一页
        async onSearchOrderDetailSingle() {
            this.orderDetailSingle.visible = true;
            setTimeout(async () => {
                this.$refs.orderDetailSinglePager.setPage(1);
                await this.getOrderDetailSingleList();
            }, 100);
        },
        //明细排序查询
        async sortchangeOrderDetailSingle(column) {
            if (!column.order)
                this.orderDetailSingle.pager = {};
            else {
                this.orderDetailSingle.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearchOrderDetailSingle();
        },
        //明细查询条件
        getOrderDetailSingleCondition() {
            var pager = this.$refs.orderDetailSinglePager.getPager();
            var page = this.orderDetailSingle.pager;
            var backupDate = formatTime1(this.filter.backupDate, "yyyy-MM-dd");
            var params = {
                ...params,
                ...pager,
                ...page,
                ...this.filter,
                ...this.orderDetailSingle.filter,
                backupDate,
            }

            return params;
        },
        //明细查询
        async getOrderDetailSingleList() {
            this.orderDetailSingle.visible = true;
            setTimeout(async () => {
                var params = this.getOrderDetailSingleCondition();
                if (params === false) {
                    return false;
                }
                this.orderDetailSingle.listLoading = true;
                const res = await getOrderErrorOrderDetail(params);
                this.orderDetailSingle.listLoading = false;
                if (!res?.success) {
                    return
                }
                this.orderDetailSingle.total = res.data.total;
                const data = res.data.list;
                data.forEach(d => {
                    d._loading = false
                })
                this.orderDetailSingle.tableData = data;
            }, 200);
        },
        //查看明细
        async onShowDetailSingle(data) {
            this.orderDetailSingle.filter.orderNoInner = data.orderNoInner;
            this.orderDetailSingle.showRow = data;
            await this.onSearchOrderDetailSingle();
        },
        //==订单明细-单个查询 End=================================================
        //tab页签变更触发
        async tabOrderDetailChange() {
            await this.onSearchChartsOrderCount();
        },
        formatNumber(num) {
            // 去掉小数位并添加千位分隔符
            return num > 100 ? Math.floor(num).toLocaleString() : num.toLocaleString();
        }
    },
    computed: {

        formattedTotalWaitOrderNum1() {
            
            return this.formatNumber(this.abnormalModel.totalWaitOrderNum);
        },
        formattedTotalWaitOrderNum2() {
            return this.formatNumber(this.abnormalModel.sjWaitOrderNum);
        },
        formattedTotalWaitOrderNum3() {
            return this.formatNumber(this.abnormalModel.preSaleOrderNum);
        },
        formattedTotalWaitOrderNum4() {
            return this.formatNumber(this.abnormalModel.taoKeOrderNum);
        },
        formattedTotalWaitOrderNum5() {
            return this.formatNumber(this.abnormalModel.totalWaitGoodNum);
        },
        formattedTotalWaitOrderNum6() {
            return this.formatNumber(this.abnormalModel.nonCompletedPurchase);
        },

        // formattedTotalWaitOrderNum() {
        //     const num = this.abnormalModel.totalWaitOrderNum;
        //     // 去掉小数位并添加千位分隔符
        //     return num > 100 ? Math.floor(num).toLocaleString() : num.toLocaleString();
        // },
        //计算异常订单总数，对于无法处理的，不计入总数
        totalOrderQty() {
            if (this.dataList && this.dataList.length > 0) {
                return this.dataList.map(a => {
                    if (a.dutyDept != "无法处理")
                        return a.qty;
                    return 0;
                }).reduce((a, b) => a + b);
            }
            return 0;
        },
        totalOrderGoodsAmount() {
            if (this.dataList && this.dataList.length > 0) {
                var data = this.dataList.map(a => {
                    if (a.dutyDept != "无法处理")
                        return a.goodsAmount;
                    return 0;
                }).reduce((a, b) => a + b);
                data = data.toFixed(2);
                return data;
            }
            return 0;
        },
        totalOrderTimeConsumingAvg() {
            if (this.dataList && this.dataList.length > 0) {
                var timeConsuming = this.dataList.map(a => {
                    if (a.dutyDept != "无法处理")
                        return a.timeConsuming;
                    return 0;
                }).reduce((a, b) => a + b);//分钟
                timeConsuming = timeConsuming * 1.0 / this.totalOrderQtyAll;
                return this.minuteToDayHourStr(timeConsuming);
            }
            return 0 + '小时';
        },
        totalOrderQtyAll() {
            if (this.dataList && this.dataList.length > 0) {
                return this.dataList.map(a => {
                    if (a.dutyDept != "无法处理")
                        return a.qtyAll;
                    return 0;
                }).reduce((a, b) => a + b);
            }
            return 0;
        },
        errorTypeList() {
            var errorTypeList = [];
            if (this.dataList && this.dataList.length > 0) {
                this.dataList.forEach(item => {
                    if (item.data && item.data.length > 0
                        && (
                            (!!this.orderDetail.showRow.dutyDept && this.orderDetail.showRow.dutyDept == item.dutyDept)
                            || (!this.orderDetail.showRow.dutyDept && item.dutyDept != "无法处理")
                        )
                    ) {
                        var arr = item.data.map(a => { return { key: a.errorType, value: a.errorType } });
                        errorTypeList = errorTypeList.concat(arr);
                    }
                });
            }
            return errorTypeList;
        }
    },
    watch: {
        "filter.platform"(val) {
            this.orderDetail.filter.platform = val;
        },
        "filter.groupId"(val) {
            this.orderDetail.filter.groupId = val;
        },
        "filter.shopCode"(val) {
            this.orderDetail.filter.shopCode = val;
        },
        "filter.sendWarehouse"(val) {
            this.orderDetail.filter.sendWarehouse = val;
        },
    }
}
</script>
<style lang="scss" scoped>
.row-condition {
    margin-top: 10px;
    margin-bottom: 20px;
    color: #606266;
    font-weight: bold;
}

.grid-header {
    color: #606266;
    font-size: 12px;
    margin-top: 10px;
    font-weight: bold;
}

.grid-text {
    color: #606266;
    font-size: 12px;
    margin-top: 10px;
    padding: 10px;
    font-weight: bold;
}

.el-col {
    text-align: center;
}

// .el-row {
//     margin-right: 50px;
// }
.el-icon-question {
    color: #909399;
}

.abnormalcard {
    padding: 0;
}

.canclick2 {
    color: #409eff;
    cursor: pointer;
}

.my-title2 {
    color: #f56c6c;
    font-size: 18px;
}

.my-principal2 {
    margin-left: 10px;
    font-weight: normal;
    font-size: 12px;
}

.grid-header2 {
    color: #606266;
    font-size: 18px;
    margin-top: 10px;
    font-weight: bold;
}

.grid-text2 {
    color: #606266;
    font-size: 18px;
    margin-top: 10px;
    padding: 5px;
    font-weight: bold;
}

.my-content2 {
    text-align: center;
}

.my-unit2 {
    font-size: 6px;
    margin-top: 20px;
    margin-left: 5px;
    font-weight: normal;
}

.my-left2 {
    font-size: 12px;
    font-weight: 500;
}

.my-right2 {
    font-size: 12px;
    font-weight: 500;
}

.my-last-time {
    font-size: 12px;
    color: #909399;
    font-weight: normal;
}

#myChart {
    width: 99%;
    height: 500px;
}

#myChartOrderCount {
    width: 99%;
    height: 500px;
}

::v-deep.el-dialog__header {
    padding: 0px;
}

.el-dialog__header {
    padding: 0px;
}
</style>
