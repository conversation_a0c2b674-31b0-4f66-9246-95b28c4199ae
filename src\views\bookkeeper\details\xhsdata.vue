<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="店铺余额" name="tab1" style="height: 100%;">
                <xhsshopbalance :filter="filter" ref="xhsshopbalance" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="支付宝微信" name="tab2" style="height: 100%;">
                <xhszfbwx :filter="filter" ref="xhszfbwx" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="账户明细" name="tab3" style="height: 100%;">
                <xhsfinacialdetail :filter="filter" ref="xhsfinacialdetail" style="height: 100%;" />
            </el-tab-pane>
        </el-tabs>
    </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";
import xhsshopbalance from '@/views/bookkeeper/details/xhsshopbalance.vue'
import xhszfbwx from '@/views/bookkeeper/details/xhszfbwx.vue'
import xhsfinacialdetail from '@/views/bookkeeper/details/xhsfinacialdetail.vue'

export default {
    name: "Users",
    components: { MyContainer, xhsshopbalance, xhszfbwx, xhsfinacialdetail },
    data() {
        return {
            that: this,
            pageLoading: '',
            filter: {
            },
            shopList: [],
            userList: [],
            groupList: [],
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            activeName: 'tab1'
        };
    },
    mounted() {

    },
    methods: {


    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
