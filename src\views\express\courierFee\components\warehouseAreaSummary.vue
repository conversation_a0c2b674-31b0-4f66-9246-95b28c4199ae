<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime" :clearable="false">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.warehouseArea" placeholder="仓区" maxlength="50" clearable class="publicCss" />
        <div style="display: flex;align-items: center;padding-top: 1px;">
          <el-checkbox v-model="ListInfo.noUseCatch" class="publicCss" style="width: 60px;">非缓存</el-checkbox>
        </div>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
      </div>
    </template>
    <vxetablebase :id="'warehouseAreaSummary202411181127'" :tablekey="'warehouseAreaSummary202411181127'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { getWarehouseArea_MonthBill_NoAddFeeList } from "@/api/express/express";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouseArea', label: '仓区', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'vote', label: '票数', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'additionalWeightFee', label: '续重费', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'waybill', label: '面单费', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalFreight', label: '运费合计', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'meanValue', label: '均值', tipmesg: '运费合计/票数', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'quantity1', label: '0-1数量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight1', label: '0-1重量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'amount1', label: '0-1费用', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'meanValue1', label: '0-1均值', tipmesg: '0-1费用/0-1数量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'quantity3', label: '0-3数量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight3', label: '0-3重量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'amount3', label: '0-3费用', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'meanValue3', label: '0-3均值', tipmesg: '0-3费用/0-3数量', },
]
export default {
  name: "warehouseAreaSummary",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        dataType: 11,
        warehouseArea: null,
        noUseCatch: false,//是否使用缓存
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    if (this.timeRanges && this.timeRanges.length == 0) {
      //默认给近7天时间
      this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
      this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
      this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
    }
    // await this.getList()
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getWarehouseArea_MonthBill_NoAddFeeList({ ...this.ListInfo })
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
