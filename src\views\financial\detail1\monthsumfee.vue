<template>
    <container>
        <ces-table ref="table" :that='that' :tablekey="tablekey" :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='false' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' @summaryClick='onsummaryClick' :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select filterable v-model="filter1.version" placeholder="类型" style="width: 120px">
                            <el-option label="工资月报" value="v1"></el-option>
                            <el-option label="参考月报" value="v2"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input style="width: 100px" v-model="filter1.proCode" placeholder="宝贝ID" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="filter1.platform" placeholder="请选择平台" style="width: 120px">
                            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="filter1.groupId" filterable clearable placeholder="运营组" style="width: 110px">
                            <el-option label="全部" value />
                            <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="filter1.shopId" filterable clearable placeholder="店铺" style="width: 110px">
                            <el-option label="全部" value />
                            <el-option v-for="item in shoplist" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
                        </el-select>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>
<script>
    import { getAnalysisCommonResponse } from '@/api/admin/common'
    import { pageDetail1MonthSumfee } from '@/api/financial/detail1'
    import { getList as getshopList } from '@/api/operatemanage/base/shop'
    import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
    import container from '@/components/my-container/noheader'
    import cesTable from "@/components/Table/table.vue";
    import { formatWarehouse, formatTime, platformlist,formatYesornoBool } from "@/utils/tools";
    import buschar from '@/components/Bus/buschar'
    const tableCols = [
        { istrue: true, prop: 'yearMonth', label: '年月', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'proCode', label: '宝贝ID', width: '200', sortable: 'custom' },
        { istrue: true, prop: 'groupId', label: '组成员', width: '150', sortable: 'custom' ,formatter:(row)=>row.groupName},
        { istrue: true, prop: 'shopCode', label: '店铺名称', width: '150', sortable: 'custom' ,formatter:(row)=>row.shopName},
        { istrue: true, summaryEvent: true, prop: 'detail1_1', label: '淘系红包', width: '80', sortable: 'custom' },
        { istrue: true, summaryEvent: true, prop: 'detail1_2', label: '淘客不计', width: '80', sortable: 'custom' },
        { istrue: true, summaryEvent: true, prop: 'detail1_3', label: '代发成本差', width: '95', sortable: 'custom' },
    ];
    const tableHandles = [];
    export default {
        name: 'Roles',
        components: { cesTable, container, buschar },
        props: {
            filter: {},
            tablekey: { type: String, default:'' }
        },
        data () {
            return {
                filter1: {
                    proCode: null,
                    platform: null,
                    fKId: null,
                    groupId: null,
                    shopId: null,
                    type: null,
                    shareOper: null
                },
                analysisFilter: {
                    searchName: "View_ProductDetail1MonthSumfee",
                    extype: 6,
                    selectColumn: "",
                    filterTime: "YearMonth",
                    isYearMonthDay: false,
                    filter: null,
                    columnList: []
                },
                buscharDialog: { visible: false, title: "", data: [] },
                grouplist: [],
                shoplist: [],
                that: this,
                list: [],
                tableCols: tableCols,
                tableHandles: tableHandles,
                pager: { OrderBy: "YearMonth", IsAsc: false },
                summaryarry: {},
                total: 0,
                sels: [],
                selids: [],
                listLoading: false,
                pageLoading: false,
                dialogExmainPurchaseFreightVisible: false,
                platformlist: platformlist,
            }
        },
        async mounted () {
            await this.init()
            this.onSearch()

        },
        beforeUpdate () { },
        methods: {
            async init () {
                this.shoplist = []
                const res1 = await getshopList({ isOpen: 1, platform: 1, CurrentPage: 1, PageSize: 100 });
                res1?.data?.list.forEach(f => { this.shoplist.push(f) })

                const res11 = await getshopList({ isOpen: 1, platform: 2, CurrentPage: 1, PageSize: 100 });
                res11?.data?.list.forEach(f => { this.shoplist.push(f) })

                var res2 = await getDirectorGroupList();
                this.grouplist = res2.data.map(item => { return { value: item.key, label: item.value }; });
            },
            onSearch () {
                this.$refs.pager.setPage(1)
                this.getlist()
            },
            async getlist () {
                var pager = this.$refs.pager.getPager()
                const params = { ...pager, ...this.pager, ... this.filter, ... this.filter1 }
                this.listLoading = true
                const res = await pageDetail1MonthSumfee(params)
                this.listLoading = false
                if (!res?.success) return
                this.total = res.data.total
                const data = res.data.list
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
                this.summaryarry = res.data.summary;
            },
            sortchange (column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            async onbatchDelete () {
                await this.$emit('ondeleteByBatch', this.shareFeeType);
            },
            async oncomput () {
                this.$emit('onstartcomput', this.shareFeeType);
            },
            async onimport () {
                await this.$emit('onstartImport', this.shareFeeType);
            },
            selsChange: function (sels) {
                this.sels = sels
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
            async onsummaryClick (property) {
                var summaryEventList = this.tableCols.filter(f => f.summaryEvent == true);
                summaryEventList.forEach(element => {
                    this.analysisFilter.columnList.push({ columnNameCN: element.label, columnNameEN: element.prop });
                });
                this.analysisFilter.filter = {
                    YearMonth: [this.filter.yearmonth, 0],
                    Version: [this.filter1.version, 0],
                    proCode: [this.filter1.proCode, 0],
                    platform: [this.filter1.platform, 0],
                    groupId: [this.filter1.groupId, 0],
                    shopCode: [this.filter1.shopCode, 0]
                };
                this.analysisFilter.selectColumn = property;
                let that = this;
                const res = await getAnalysisCommonResponse(this.analysisFilter).then(res => {
                    that.buscharDialog.visible = true;
                    that.buscharDialog.data = res.data
                    that.buscharDialog.title = res.data.legend[0]
                });
            },
        }
    }
</script>
