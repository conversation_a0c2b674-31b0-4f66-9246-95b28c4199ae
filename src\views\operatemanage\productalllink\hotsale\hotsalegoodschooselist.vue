<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                <el-button-group>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-date-picker style="width:220px" v-model="Filter.gDate" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"></el-date-picker>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <label style="margin-left: 5px;">选品类型：</label>
                        <el-select style="width:100px;" v-model="Filter.chooseType" placeholder="类型" filterable :collapse-tags="true">
                            <el-option label="全部" :value="0"></el-option>
                            <el-option label="同品跨平台" :value="1"></el-option>
                            <el-option label="老品补SKU" :value="2"></el-option>
                            <el-option label="竞品" :value="3"></el-option>
                            <el-option label="新品" :value="4"></el-option>
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:140px" v-model="Filter.platform" placeholder="竞品平台" clearable filterable :collapse-tags="true">
                            <el-option v-for="(item) in platformlist" :key="'p1p-'+item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:140px" v-model="Filter.newPlatform" placeholder="运营平台" multiple clearable filterable :collapse-tags="true">
                            <el-option v-for="(item) in platformlist" :key="'p1p-'+item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                    </el-button>

                    <!-- <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:100px" clearable v-model="Filter.orgPlatformName" placeholder="原平台">
                            <el-option v-for="(item ) in plantformList" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>
                    </el-button> -->

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model="Filter.chooseGroupName" type="text" maxlength="20" clearable placeholder="选品小组" style="width:100px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model="Filter.chooseUserName" type="text" maxlength="20" clearable placeholder="选品人" style="width:100px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                      <inputYunhan ref="refGoodsCompeteId" :inputt.sync="Filter.goodsCompeteId" v-model="Filter.goodsCompeteId" width="160px"
                        placeholder="竞品ID/Enter多行输入" :clearable="true" :clearabletext="true" :maxRows="100" :maxlength="2000"
                        @callback="callbackGoodsCompeteId" title="竞品ID">
                      </inputYunhan>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model="Filter.goodsCompeteName" type="text" maxlength="100" clearable placeholder="竞品标题" style="width:120px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model="Filter.goodsName" type="text" maxlength="100" clearable placeholder="原产品名称" style="width:120px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model="Filter.fullCateGoryName" type="text" maxlength="100" clearable placeholder="竞品类目" style="width:120px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input-number v-model="Filter.salePriceMin" :min="0" :max="999999999" :step="50" placeholder="最低售价" style="width:120px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input-number v-model="Filter.salePriceMax" :min="0" :max="999999999" :step="50" placeholder="最高售价" style="width:120px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input-number v-model="Filter.lastMonthSaleCountMin" :min="0" :max="999999999" :step="100" placeholder="最低月销量" style="width:130px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input-number v-model="Filter.lastMonthSaleCountMax" :min="0" :max="999999999" :step="100" placeholder="最高月销量" style="width:130px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input-number v-model="Filter.lastMonthSaleAmountMin" :min="0" :max="999999999" :step="100" placeholder="最低月售额" style="width:130px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input-number v-model="Filter.lastMonthSaleAmountMax" :min="0" :max="999999999" :step="100" placeholder="最高月售额" style="width:130px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input-number v-model="Filter.lastMonthSaleProfitMin" :min="0" :max="999999999" :step="100" placeholder="最低月利润" style="width:130px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input-number v-model="Filter.lastMonthSaleProfitMax" :min="0" :max="999999999" :step="100" placeholder="最高月利润" style="width:130px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input-number v-model="Filter.lastMonthSaleProfitRatioMin" :min="0" :max="999999999" :step="10" placeholder="最低利润率" style="width:130px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input-number v-model="Filter.lastMonthSaleProfitRatioMax" :min="0" :max="999999999" :step="10" placeholder="最高利润率" style="width:130px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:130px" clearable v-model="Filter.skuDataState" placeholder="SKU数据状态">
                            <el-option v-for="(item ) in [-1,0,1,2,3]" :key="item" :label="formatInfoQueryState(item)" :value="item">
                            </el-option>
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:130px" clearable v-model="Filter.cmRefInfoState" placeholder="参谋数据状态">
                            <el-option v-for="(item ) in [-1,0,1,2,3]" :key="item" :label="formatInfoQueryState(item)" :value="item">
                            </el-option>
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select style="width:200px" clearable v-model="Filter.allDataStates" multiple :collapse-tags="true" placeholder="选品状态">
                            <el-option v-for="item in AllDataStateOpts" :value="item.value" :label="item.label" :key="'allDataStates'+item.value"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="Filter.createdUserNameList" multiple collapse-tags clearable filterable
                            placeholder="推荐人" style="width: 180px">
                            <el-option v-for="(item) in hotSaleBrandPushNewCreatedUserNameList" :key="'tjr' + item.key"
                                :label="item.value" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="Filter.projectDept" style="width:172px" multiple collapse-tags clearable
                            placeholder="请选择项目">
                            <el-option label="项目部-大侠" value="项目部-大侠"/>
                            <el-option label="项目部-徐琛" value="项目部-徐琛"/>
                            <el-option label="项目部-左玉玲" value="项目部-左玉玲"/>
                            <el-option label="未设置" value="未设置"/>
                        </el-select>
                    </el-button>

                    <!-- allDataState  multiple clearable filterable  :collapse-tags="true" -->

                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="()=>{Filter={chooseType:0};}">清空条件</el-button>
                    <el-button type="primary" @click="setExportCols">导出</el-button>

                </el-button-group>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :cstmExportFunc="onExport"  :isIndex='true' :hasexpand='true' :hasexpandRight='true' @sortchange='sortchange' :tableData='tbdatalist' @select='selectchange' :isSelection='false' :isSelectColumn="true" :tableCols='tableCols' :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button type="primary" @click="openChooseGoodsForm">新增选品</el-button>
                </el-button-group>
            </template>

            <template slot="right">
                <el-table-column width="200" label="操作" fixed="right">
                    <template slot-scope="scope">
                        <template v-if="scope.row.platform>=1 && (scope.row.bldDocAuditState==null || scope.row.bldDocAuditState<1)&&scope.row.allDataState!=-10">
                            <!-- <el-button type="text" v-if="!scope.row.skuDataTime" @click="onApplyCompeteGoodSKU(scope.row,true)">申请竞品SKU</el-button>
                            <el-button type="text" v-else @click="onApplyCompeteGoodSKU(scope.row,false)">重新申请SKU</el-button> -->
                        </template>
                        <template v-if="scope.row.platform==2 && (scope.row.bldDocAuditState==null || scope.row.bldDocAuditState<1)&&scope.row.allDataState!=-10">
                            <!-- <el-button type="text" v-if="!scope.row.cmRefInfoLastOkTime" @click="reqCmRefDateInfo(scope.row,false)">申请竞品参谋数据</el-button>
                            <el-button type="text" v-else @click="reqCmRefDateInfo(scope.row,true)">重查参谋数据</el-button> -->

                        </template>

                        <!-- 【计算利润】未申请建编码、非老品补SKU -->
                        <el-button type="text" v-if="(scope.row.bldDocAuditState==null || scope.row.bldDocAuditState<1)
                            && scope.row.chooseType!=2 &&scope.row.allDataState!=-10 && scope.row.allDataState != 98 && (scope.row.exclude || checkPermission('AllExclude'))"
                            @click="onEditCompeteGoodSKU(scope.row)">计算利润</el-button>
                        <el-button type="text" v-if="(scope.row.allDataState==-10||(!!scope.row.lastProfitSumDate))
                            && (scope.row.exclude || checkPermission('AllExclude')) && scope.row.allDataState != 98"
                            @click="onSeeCompeteGoodSKU(scope.row)">查看利润</el-button>
                        <el-button type="text" v-if="checkPermission('api:OperateManage:AllLink:RejectOperationalInquiry') && scope.row.extData.isReject"
                            @click="btnRejectOperationalInquiry(scope.row)">询价驳回</el-button>
                        <!-- 【归档】未上架、未申请建编码 -->
                        <el-button type="text" v-if="scope.row.allDataState!=100  && !(scope.row.extData.buildDocEntity && scope.row.allDataState != 98
                            && scope.row.extData.buildDocEntity.auditState>0) && scope.row.allDataState!=-10 && (scope.row.exclude || checkPermission('AllExclude'))"
                            @click="onSealClose(scope.row)">归档</el-button>

                        <!-- 【归档】组长归档 已申请建编码、未上架、未归档-->
                        <el-button type="text" v-if="checkPermission('HotSaleGoods_SealCloseByLeader') && scope.row.allDataState!=100
                            && (scope.row.extData.buildDocEntity && scope.row.extData.buildDocEntity.auditState>0) && scope.row.allDataState!=-10"
                            @click="onSealClose(scope.row)">归档</el-button>

                        <el-button type="text" v-if="(scope.row.allDataState==-10||(!!scope.row.cmRefInfoLastOkTime))
                            && (scope.row.exclude || checkPermission('AllExclude')) && scope.row.allDataState != 98"
                            @click="showCmRefDateInfo(scope.row)">查看竞品参谋数据</el-button>

                        <!-- <el-button type="text" v-if="(!!scope.row.lastProfitSumDate)&&(!scope.row.registerSkuOrderTime)" @click="onRegisterSkuOrder(scope.row)">登记采样</el-button> -->

                        <!-- 【确认选品】自己的选品、未发起给组长审批或审批不通过的 、 非老品补SKU 、计算过利润  -->
                        <el-button type="text" v-if=" (scope.row.bldDocAuditState==null ||scope.row.bldDocAuditState<1)  && scope.row.chooseType!=2 && scope.row.allDataState != 98
                            && !!scope.row.lastProfitSumDate&&scope.row.allDataState!=-10&& (scope.row.exclude || checkPermission('AllExclude'))"
                            @click="onBeforeApplyBuildDoc(scope.row)">确认选品</el-button>
                        <!-- <el-button type="text" v-if="scope.row.bldDocAuditState===2" @click="onBuildGoodsDoc(scope.row)">建编码</el-button> -->

                        <!-- 【申请编码】 确认选品已审核 、不存在申请编码流程或存在但没发起审核或审核驳回 -->
                        <el-button type="text" v-if="scope.row.chooseType!= 1 && scope.row.bldDocAuditState===2 && !(scope.row.extData.buildDocEntity && scope.row.allDataState != 98
                            && scope.row.extData.buildDocEntity.auditState>0)&&scope.row.allDataState!=-10 && scope.row.allDataState != 98 && (scope.row.exclude || checkPermission('AllExclude'))"
                            @click="onBuildGoodsDocNew(scope.row,1)">申请编码</el-button>
                        <!-- 【申请编码】 存在申请编码流程且  流程为审核中、已审核 -->
                        <el-button type="text" v-if="(scope.row.allDataState==-10||(scope.row.extData.buildDocEntity && scope.row.extData.buildDocEntity.auditState>0))
                            && (scope.row.exclude || checkPermission('AllExclude')) && scope.row.allDataState != 98"
                            @click="onBuildGoodsDocNew(scope.row,3)">建编码详情</el-button>
                        <!-- 【拍摄】 建编码为需要拍摄且已审核 ，且无拍摄任务  -->
                        <el-button type="text" @click="onOpenAddTask(scope.row,1)" v-if="scope.row.extData && scope.row.extData.buildDocEntity
                            && scope.row.extData.buildDocEntity.auditState===2  && scope.row.extData.buildDocEntity.neeShootingTask===1
                            && !(scope.row.extData.shootingTaskDto && scope.row.extData.shootingTaskDto.shootingTaskId)  &&scope.row.allDataState!=-10">拍摄</el-button>

                        <!-- <el-button type="text" v-if="(scope.row.allDataState==50)" @click="onApprovalGoodsDoc(scope.row)">申请编码</el-button> -->

                        <!--【上架】 未上架、建编码审核通过 、存在进货时必须进货完成、存在拍摄时，必须拍摄完成 -->
                        <el-button type="text" v-if="scope.row.showOnSellBtn&&scope.row.allDataState!=-10&& scope.row.allDataState != 98 (scope.row.exclude || checkPermission('AllExclude'))"
                            @click="openSetOnSellForm(scope.row)">上架</el-button>
                        <el-button type="text" v-if="scope.row.allDataState!=-10&& (scope.row.exclude || checkPermission('AllExclude')) && scope.row.allDataState != 98"
                            @click="trunToOther(scope.row)">转派</el-button>
                        <el-button type="text" v-if=" scope.row.allDataState!=-10 && (scope.row.extData.buildDocEntity && scope.row.extData.buildDocEntity.auditState==2
                            && (scope.row.extData.buildDocEntity.delayedPurchase==1||scope.row.extData.buildDocEntity.isRejectPurchasePlan==1))
                            && (scope.row.exclude || checkPermission('AllExclude')) && scope.row.allDataState != 98"
                            @click="Purchase(scope.row)">进货</el-button>
                        <el-button type="text" @click="showLog(scope.row)">日志</el-button>
                        <el-button type="text" v-if="scope.row.isAdmin == 1"
                            @click="correctProps(scope.row)">修正进货数据</el-button>
                    </template>
                </el-table-column>

            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
        </template>

        <el-dialog title="竞品利润" :visible.sync="dialogSkuVisible" width='80%' top="1vh" :close-on-click-modal="false" :before-close="beforeclose" v-dialogDrag v-loading="dialogSkuLoading" element-loading-text="拼命加载中">
            <skuPage ref="skuPage" style="z-index:1000;" @onSearch="onSearch"  @isclose="dialogSkuVisible= false" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="beforeclose">取 消</el-button>
                    <!-- <el-button type="primary" @click="onSkuSave(false)" :loading="skuSaveLoading" v-if="skuSaveHiddle">保存草稿</el-button> -->
                    <el-button type="primary" @click="onSkuSave(true)" v-throttle="2000"  >保存利润&关闭</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="竞品参谋数据" :visible.sync="dialogTmCmRefInfoVisible" width='95%' :close-on-click-modal="false" v-dialogDrag v-loading="true" element-loading-text="拼命加载中">
            <cmRefTmPage ref="cmRefTmPage" style="z-index:1000;width:100%" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogTmCmRefInfoVisible = false">关 闭</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="竞品参谋数据" :visible.sync="dialogPddCmRefInfoVisible" width='80%' :close-on-click-modal="false" v-dialogDrag v-loading="dialogPddCmLoading" element-loading-text="拼命加载中">
            <cmRefPddPage ref="cmRefPddPage" style="z-index:1000;height:610px;" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogPddCmRefInfoVisible = false">关 闭</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="竞品建编码" :visible.sync="dialogBuildGoodsDocListVisible" width='80%' :close-on-click-modal="false" v-dialogDrag v-loading="dialogBuildGoodsDocListLoading" element-loading-text="拼命加载中">
            <el-row style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black;margin-bottom: 2px;">
                <span>竞品信息：</span>
                <el-button type="primary" @click="onBuildGoodsDocAdd">新增</el-button>
            </el-row>
            <el-container>
                <el-main style="height:190px;">
                    <ces-table ref="tablejdmain" :that='that' :isIndex='true' :hasexpand='true' :tableData='jdmainlist' :tableCols='jdmainTableCols' :loading="jdmainLoading" style="height:190px" :isSelectColumn="false" @cellclick="onjdmainCellClick">
                    </ces-table>
                </el-main>
            </el-container>
            <el-row style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black; margin-top: 10px;margin-bottom: 2px;">
                <span>竞品商品明细信息：</span>
            </el-row>
            <el-container>
                <el-main style="height:350px;">
                    <ces-table ref="tablejddtl" :that='that' :isIndex='true' :hasexpand='true' :tableData='jddtllist' :tableCols='jddtlTableCols' :loading="jddtlLoading" style="height:350px" :isSelectColumn="false">
                    </ces-table>
                </el-main>
            </el-container>

        </el-dialog>
        <el-dialog title="竞品建编码" :visible.sync="dialogBuildGoodsDocVisible" append-to-body width='85%' :close-on-click-modal="false" v-dialogDrag v-loading="dialogBuildGoodsDocLoading" element-loading-text="拼命加载中">
            <buildGoodsDocPage ref="buildGoodsDocPage" style="z-index:1000;" @loaded="(d)=>{
                    if(d.auditState>0) {
                        buildGoodsDocHiddle=false;
                    }

                    }" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogBuildGoodsDocVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onBuildGoodsDocSave(false)" :loading="buildGoodsDocLoading" v-if="buildGoodsDocHiddle">保存&关闭</el-button>
                    <el-button type="primary" @click="onBuildGoodsDocSave(true)" :loading="requestCodeLoading" v-if="buildGoodsDocHiddle">保存&申请编码</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="日志" :visible.sync="dialogShowLog" width='80%' :close-on-click-modal="false" v-dialogDrag v-loading="logTableLoding" element-loading-text="拼命加载中">
            <el-row style="line-height:35px; font-size: 14px;color: black;margin-bottom: 2px;">
                <el-col :span="6" style="float: left; margin-left: 20px;">
                    <span style="font-weight: bold;">竞品Id：</span>
                    <span>{{ showLogData.goodsCompeteId }}</span>
                </el-col>
                <el-col :span="12" style="float: left;">
                    <span style="font-weight: bold;">竞品标题：</span>
                    <span>{{ showLogData.goodsCompeteName }}</span>
                </el-col>
                <el-col :span="4" style="float: left;">
                    <span style="font-weight: bold;">运营组：</span>
                    <span>{{ showLogData.groupName }}</span>
                </el-col>
            </el-row>
            <el-row>
                <el-container>
                    <el-main style="height:500px;">
                        <ces-table ref="tableLog" :that='that' :isIndex='false' :hasexpand='true' :tableData='showLogData.logList' :tableCols='logTableCols' style="height:480px;" :isSelectColumn="false">
                        </ces-table>
                    </el-main>
                </el-container>
            </el-row>
        </el-dialog>

        <!--创建任务-->
        <el-dialog :title="addTaskPageTitle" :visible.sync="addTask" width="80%" :close-on-click-modal="false" :key="addTaskOpentime" element-loading-text="拼命加载中" v-dialogDrag v-loading="addTaskLoading">

            <shootingvideotaskeditfrom :key="'shootingvideotaskeditfrom'+ addTaskOpentime" ref="shootingvideotaskeditfrom" :taskUrgencyList="addTaskUrgencyList" :groupList="addTaskGroupList" :warehouselist="addTaskWarehouselist" :platformList="addTaskPlatformList" :islook='addTaskIslook' :onCloseAddForm="()=>{addTask=false;onRefresh();}"></shootingvideotaskeditfrom>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="addTask=false">取 消</el-button>
                    <my-confirm-button type="submit" @click="addTaskOnSubmit" v-show="!addTaskIslook" />
                </span>
            </template>
        </el-dialog>

        <el-dialog title="历史进货" :visible.sync="hotstockinapplylistVisible" append-to-body width='80%' :close-on-click-modal="false" v-dialogDrag>
            <hotstockinapplylist ref="hotstockinapplylistPage" :filter="hotstockinapplylistFilter" style="z-index:1000;" />
        </el-dialog>

        <el-dialog title="采购驳回询价" :visible.sync="brandInquiryRejectVisible" with="20%" v-dialogDrag append-to-body :close-on-click-modal="false">
            <el-input type="textarea" placeholder="请输入驳回原因" v-model="rejectReason" maxlength="200" show-word-limit rows="4" style="margin-bottom: 5px;" />
            <aauploadimgFile ref="paymentInfoPictureList" :ispaste="true" :isuploadfile="false" :noDel="false" :accepttyes="accepttyes" :isImage="true"
                :uploadInfo="paymentInfoPictureList" :keys="[1, 1]" @callback="callBackPaymentPicture" :imgmaxsize="9" :limit="9" :multiple="true">
            </aauploadimgFile>
            <span slot="footer" class="dialog-footer">
                <el-button @click="brandInquiryRejectVisible = false">取 消</el-button>
                <el-button type="primary" :loading="brandInquiryRejectLoading" @click="btnBrandInquiryReject">确认驳回</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>

    import {
        pageHotSaleGoodsChooseAsync, isDoHotSaleGoodsAsync, getAllLinkPlantformsAsync,
        getAllLinkCategoryNamesAsyncByParent, goodsInfoQueryReqAsync, reqGoodsCmRefInfoQueryAsync, getHotGoodsCmRefInfoAsync,
        getAllHotGoodsWaitQueryAsync, getHotSaleGoodsEchartByDateAsync, getUserInfo, rejectOperationalInquiry,
        applyCompeteGoodSKU, registerSkuOrderAsync, dingDingApproval_BuildGoodsDoc, pageHotSaleGoodsBuildGoodsAsync, pageHotSaleGoodsBuildGoodsCodeAsync,
        DingDingApproval_BuildGoodsDocBeforConfirm, getHotSaleGoodsChooseLog, GetDataBeforeBuildGoodsMediaTask, exportHotSaleGoodsChooseAsync,syncBuildGoodsGeneratePurchasePlan
    } from '@/api/operatemanage/productalllink/alllink'
    import {getHotSaleBrandPushNewCreatedUserNameList} from '@/api/operatemanage/productalllink/LogisticsAnalyse.js';
    import dayjs from "dayjs";
    import cesTable from "@/components/Table/table.vue";

    import {
        formatmoney, formatPercen, getUrlParam, platformlist,
        formatPlatform, formatTime, setStore, getStore,
        formatLinkProCode, sendWarehouse4HotGoodsBuildGoodsDocList,
        pickerOptions, fmtAllLinkChooseProfitState, AllLinkChooseProfitStateOpts,
        ShootingVideoTaskUrgencyOptions
    } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import * as echarts from 'echarts';
    import buschar from '@/components/Bus/buschar';
    import skuPage from '@/views/operatemanage/productalllink/hotsale/hotsalegoodschooselistsku.vue';
    import cmRefTmPage from '@/views/operatemanage/productalllink/hotsalegoodscmrefinfotmall.vue';
    import cmRefPddPage from '@/views/operatemanage/productalllink/hotsalegoodscmrefinfopdd.vue';
    import inputYunhan from "@/components/Comm/inputYunhan";

    import buildGoodsDocPage from '@/views/operatemanage/productalllink/hotsale/hotsalegoodsbuildgoodsdoc.vue';
    import hotsalegoodspurchasegoods from '@/views/operatemanage/productalllink/hotsale/hotsalegoodspurchasegoods.vue'
    import hotstockinapplylist from '@/views/operatemanage/productalllink/hotsale/hotstockinapplylist.vue'
    import { assertRegexLiteral } from '@babel/types';

    import YhImgUpload from '@/components/upload/yh-img-upload.vue';

    import { getDirectorGroupList } from '@/api/operatemanage/base/shop';
    import shootingvideotaskeditfrom from '@/views/media/shooting/shootingvideotaskeditfromNew'
    import { getShootOperationsGroup, getOperationsGroup, getErpUserInfoView } from '@/api/media/mediashare';
    import { rulePlatform } from "@/utils/formruletools";
    import aauploadimgFile from "@/components/Comm/uploadimgFile.vue";

    const taskPageParams = {
        addTaskPageTitle: "创建任务",
        addTask: false,
        addTaskOpentime: 0,
        addTaskLoading: false,
        addTaskUrgencyList: ShootingVideoTaskUrgencyOptions,
        addTaskGroupList: [],
        addTaskWarehouselist: [],
        addTaskPlatformList: [],
        addTaskIslook: false,
    };

    function formatInfoQueryState(val) {
        //-1同步失败、0未申请、1申请中、2同步中、3已同步
        switch (val) {
            case 0:
                return "未申请";
            case 1:
                return "申请中";
            case 2:
                return "同步中";
            case 3:
                return "存在";
            case -1:
                return "同步失败";
            default:
                return "";
        }
    }

    function getStatus(row) {
        let html = "";
        // <!-- 基础状态 ： 上架-进货、拍摄-基础状态 -->
        //<!-- 上架 -->
        if (row.allDataStateTxt == '上架') {
            html += '<p class="xAllDataStateCol202301111351001success"><b>' + row.allDataStateTxt + '</b></p>"';
        }
        //<!-- 进货、拍摄 -->
        else if (row.extData.shootingTaskDto || row.extData.otherInfo.wareInHouseState) {

            if (row.extData.otherInfo.wareInHouseState) {
                html += '<p><b> ' + row.extData.otherInfo.wareInHouseState + '</b></p>';
            }
            if (row.extData.shootingTaskDto) {
                let className = row.extData.shootingTaskDto.isComplate == 1 ? 'xAllDataStateCol202301111351001success' : 'xAllDataStateCol202301111351001primary';
                let psName = row.extData.shootingTaskDto.isComplate == 1 ? '拍摄完成' : '拍摄中';
                html += '<p class="' + className + '"><b>' + psName + '</b></p>';
            }
        } else {
            html += '<p class="xAllDataStateCol202301111351001primary"><b>' + row.allDataStateTxt + '</b></p>';
        }
        //<!-- 询价  询价中、已报价、报价已采纳-->
        if (row.extData.otherInfo.enquiryState) {
            let className = row.extData.otherInfo.enquiryState == '报价已采纳' ? 'xAllDataStateCol202301111351001success' :
                (row.extData.otherInfo.enquiryState == '已报价' ? 'xAllDataStateCol202301111351001primary' : 'xAllDataStateCol202301111351001info')
            html += '<p class="' + className + '">' + row.extData.otherInfo.enquiryState + '</p>';
        }
        //<!-- 采样 对样成功 对样失败 采样登记 其他状态 -->
        if (row.extData.otherInfo.sampling) {
            let className = row.extData.otherInfo.sampling == '对样成功'
                ? 'xAllDataStateCol202301111351001success'
                : (row.extData.otherInfo.sampling == '对样失败'
                    ? 'xAllDataStateCol202301111351001danger'
                    : (row.extData.otherInfo.sampling == '采样登记' ? 'xAllDataStateCol202301111351001info' : 'xAllDataStateCol202301111351001primary'))
            html += '<p class="' + className + '">' + row.extData.otherInfo.sampling + '</p>';
        }
        return html;
    }

    //选品全状态
    const AllDataStateOpts = [
        { label: "已选品", value: 0 },
        { label: "数据申请中", value: 10 },
        { label: "数据已同步", value: 20 },
        { label: "已计算利润", value: 30 },
        //{label:"已登记采样",value:40},
        { label: "组长确认中", value: 45 },
        { label: "组长已拒绝", value: 46 },
        { label: "组长已确认", value: 47 },
        { label: "询价被驳回", value: 98 },
        { label: "编码已拒绝", value: 51 },
        { label: "编码已申请", value: 60 },
        { label: "编码已审核", value: 70 },
        { label: "已上架", value: 100 },

        { label: "已归档", value: -10 },//已归档，单独处理
    ]

    const tableCols = [
        //{ istrue: true, prop: 'id', label: '主键', width: '200', display: false },
        { istrue: true, prop: 'goodsCompeteId', label: 'ID', width: '110', sortable: 'custom', tipmesg: '包括竞品ID、商品ID', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.goodsCompeteId) },
        {
            istrue: true, prop: 'chooseType', label: '选品类型', width: '80', sortable: 'custom', align: 'center',
            formatter: row => { return row.chooseType == 1 ? "同品跨平台" : row.chooseType == 2 ? "老品补SKU" : row.chooseType == 3 ? "竞品" : "新品" }
        },
        { istrue: true, prop: 'newPlatform', label: '运营平台', width: '80', sortable: 'custom', align: 'center', formatter: row => formatPlatform(row.newPlatform) },
        { istrue: true, prop: 'platform', label: '竞品平台', width: '80', sortable: 'custom', align: 'center', formatter: row => formatPlatform(row.platform) },
        { istrue: true, prop: 'fullCateGoryName', label: '竞品类目', width: '220', sortable: 'custom' },
        { istrue: true, prop: 'goodsCompeteImgUrl', label: '竞品图片', width: '80', type: 'image', align: 'center' },
        { istrue: true, prop: 'goodsCompeteName', label: '竞品标题', width: '220', sortable: 'custom' },

        { istrue: true, prop: 'goodsCompeteShortName', label: '产品简称', sortable: 'custom' },
        { istrue: true, prop: 'allDataState', label: '选品状态', width: '100', sortable: 'custom', classname: "xAllDataStateCol202301111351001", align: 'center', type: 'html', formatter: (row) => getStatus(row) },
        { istrue: true, prop: 'salePriceRange', label: '售价', width: '70', sortable: 'custom' },
        { istrue: true, prop: 'lastMonthSaleCount', label: '月销量汇总', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'lastMonthSaleAmount', label: '月销售额汇总', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'grossProfit3', label: '月销利润汇总', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'grossProfit3Ratio', label: '月销利润率', width: '80', sortable: 'custom', formatter: (row) => formatPercen(row.grossProfit3Ratio) },
        { istrue: true, prop: 'lastProfitSumDate', label: '利润计算日期', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.lastProfitSumDate, 'YYYY-MM-DD') },
        { istrue: true, prop: 'skuDataState', label: 'Sku数据状态', width: '80', sortable: 'custom', align: 'center', formatter: (row) => formatInfoQueryState(row.skuDataState) },
        { istrue: true, prop: 'skuDataTime', label: 'Sku数据时间', width: '110', sortable: 'custom', formatter: (row) => formatTime(row.skuDataTime, 'MM月DD日HH:mm') },
        { istrue: true, prop: 'skuCount', label: 'SKU数量', width: '60', sortable: 'custom', align: 'center' },
        { istrue: true, prop: 'projectDept', label: '项目', width: '90', align: 'center',formatter:(row) => row.extData.buildDocEntity ?row.extData.buildDocEntity.projectDept : null},
        { istrue: true, prop: 'groupName', label: '小组', width: '80', sortable: 'custom', align: 'center' },
        { istrue: true, prop: 'userNickName', label: '添加人', width: '80', sortable: 'custom', align: 'center' },
        { istrue: true, prop: 'recommendUserName', label: '推荐人', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'createdTime', label: '添加日期', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'cmRefInfoState', label: '参谋数据状态', width: '80', sortable: 'custom', formatter: (row) => formatInfoQueryState(row.cmRefInfoState) },
        { istrue: true, prop: 'cmRefInfoLastOkTime', label: '参谋数据时间', width: '110', sortable: 'custom', formatter: (row) => formatTime(row.cmRefInfoLastOkTime, 'MM月DD日HH:mm') },
        { istrue: true, prop: 'chooseRemark', label: '备注', width: '100', sortable: 'custom' },
    ];
    const jdmainTableCols = [
        { istrue: true, prop: 'id', label: '选品建编码主键', width: '160', display: false },
        { istrue: true, prop: 'hotSaleGoodsChooseId', label: '选品主键', width: '160', display: false },
        { istrue: true, prop: 'goodsCompeteId', label: '竞品ID', width: '120' },
        { istrue: true, prop: 'goodsCompeteName', label: '竞品标题' },
        { istrue: true, prop: 'goodsCompeteShortName', label: '产品简称' },
        { istrue: true, prop: 'goodsCompeteImgUrl', label: '竞品图片', width: '80', type: 'image' },
        { istrue: true, prop: 'platform', label: '平台', width: '70', formatter: (row) => row.platformName || ' ' },
        { istrue: true, prop: 'yyGroupName', label: '运营组', width: '70' },
        { istrue: true, prop: 'patentQualificationImgUrls', label: '专利资质', width: '80', type: 'images' },
        { istrue: true, prop: 'patentQualificationPdfUrls', label: '专利PDF', width: '80', type: 'files' },
        { istrue: true, prop: 'packingImgUrls', label: '包装图片', width: '80', type: 'images' },
        { istrue: true, prop: 'submitUserName', label: '提交人', width: '70' },
        { istrue: true, prop: 'submitTime', label: '提交时间', width: '150', formatter: (row) => formatTime(row.submitTime, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'applyUserName', label: '申请人', width: '70' },
        { istrue: true, prop: 'applyTime', label: '申请时间', width: '150', formatter: (row) => formatTime(row.applyTime, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'auditState', label: '状态', width: '80', formatter: (row) => row.auditStateName || ' ' },
        { istrue: true, prop: 'auditTime', label: '审核时间', width: '150', formatter: (row) => formatTime(row.auditTime, 'YYYY-MM-DD HH:mm:ss') },
        {
            istrue: true, type: 'button', label: '操作', width: '140',
            btnList: [
                {
                    label: "编辑", handle: (that, row) => that.onEditBuildGoodsDoc(row, true), permission: "api:operatemanage:alllink:BuildGoodsDocAsync",
                    ishide: (that, row) => (row.auditState > 0)
                },
                {
                    label: "申请编码", handle: (that, row) => that.onApprovalGoodsDoc(row), permission: "api:operatemanage:alllink:ApplyBuildGoodsDocCodeAsync",
                    ishide: (that, row) => (row.auditState > 0)
                },
                {
                    label: "查看", handle: (that, row) => that.onEditBuildGoodsDoc(row, false)
                },
            ]
        }
    ];
    const jddtlTableCols = [
        { istrue: true, prop: 'id', label: '选品建编码明细主键', width: '160', display: false },
        { istrue: true, prop: 'parentId', label: '选品建编码主键', width: '160', display: false },
        // { istrue: true, prop: 'yhGoodsCode', label: '商品编码', width: '160' },//
        { istrue: true, prop: 'yhGoodsName', label: '商品名称' },
        //{ istrue: true, prop: 'yhGoodsUnit', label: '品名单位', width: '80' },
        { istrue: true, prop: 'costPrice', label: '成本单价', width: '120' },
        { istrue: true, prop: 'forNewWarehouse', type: "select", label: '上新仓库', width: '120', isDisabled: (row) => true, options: sendWarehouse4HotGoodsBuildGoodsDocList },
        { istrue: true, prop: 'goodsProgressType', type: "select", label: '商品类型', width: '120', isDisabled: (row) => true, options: [{ label: '成品', value: '成品' }, { label: '半成品', value: '半成品' }] },
        { istrue: true, prop: 'isMainSale', type: "select", label: '是否主卖', width: '120', isDisabled: (row) => true, options: [{ label: '是', value: true }, { label: '否', value: false }] },
        { istrue: true, prop: 'estimateStockInCount', label: '预计进货数量', width: '120' },
        { istrue: true, prop: 'estimateStockInAmount', label: '预计进货金额', width: '120' },
        { istrue: true, prop: 'remark', label: '备注', },

    ];
    const logTableCols = [
        { istrue: true, prop: 'id', label: '日志主键', width: '160', display: false },
        { istrue: true, prop: 'logContent', label: '事件', align: 'center' },
        { istrue: true, prop: 'opearTime', label: '日期', align: 'center', formatter: (row) => formatTime(row.opearTime, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'opearName', label: '操作人', align: 'center' }
    ]
    const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    const star = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");

    export default {
        name: "Users",
        components: {
            MyContainer, MyConfirmButton, MySearch, MySearchWindow, skuPage, cesTable, aauploadimgFile,inputYunhan,
            buschar, cmRefTmPage, cmRefPddPage, buildGoodsDocPage, YhImgUpload, shootingvideotaskeditfrom, hotsalegoodspurchasegoods, hotstockinapplylist
        },
        data() {
            return {
                that: this,
                AllLinkChooseProfitStateOpts: AllLinkChooseProfitStateOpts,
                AllDataStateOpts: AllDataStateOpts,
                pickerOptions: pickerOptions,
                datetime: null,
                buscharDialog: { visible: false, isNot7Day: true, title: "", data: [] },
                Filter: {
                    gDate: [],
                    platform: null,
                    newPlatform: null,
                    chooseType: 0
                },
                platformlist: platformlist,
                shopList: [],
                userList: [],
                groupList: [],
                tbdatalist: [],
                tableCols: tableCols,
                total: 0,
                summaryarry: { count_sum: 10 },
                pager: { OrderBy: "", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                //
                selids: [],
                dialogVisibleSyj: false,
                fileList: [],
                plantformList: [],
                categoryNameList1: [],
                categoryNameList2: [],
                categoryNameList3: [],
                categoryNameList4: [],
                echartsLoading: false,
                goodsEchartDtlFilter: {
                    goodsId: null,
                    startTime: null,
                    endTime: null,
                    timerange: [star, endTime]
                },
                dialogSkuVisible: false,
                dialogSkuLoading: false,
                skuSaveLoading: false,
                skuSaveHiddle: false,

                dialogBuildGoodsDocVisible: false,
                dialogBuildGoodsDocLoading: false,
                buildGoodsDocLoading: false,
                requestCodeLoading: false,
                buildGoodsDocHiddle: false,
                dialogTmCmRefInfoVisible: false,
                selfInfo: {

                },
                dialogPddCmRefInfoVisible: false,
                dialogPddCmLoading: false,

                selHotSaleGoodsChooseId: 0,
                dialogBuildGoodsDocListVisible: false,
                dialogBuildGoodsDocListLoading: false,
                jdmainLoading: false,
                jdmainlist: [],
                jdmainTableCols: jdmainTableCols,
                jddtlLoading: false,
                jddtllist: [],
                jddtlTableCols: jddtlTableCols,
                seldocId: null,
                //日志
                dialogShowLog: false,
                logTableCols: logTableCols,
                logTableLoding: false,
                showLogData: {
                    goodsCompeteId: '',
                    goodsCompeteName: '',
                    groupName: '',
                    logList: []
                },
                //进货
                hotstockinapplylistVisible: false,
                hotstockinapplylistFilter: {
                    chooseId: ""
                },
                isExceptDept:false,
                hotSaleBrandPushNewCreatedUserNameList:[],

                //运营询价数据驳回
                brandInquiryRejectVisible: false,
                rejectReason: null,
                paymentInfoPictureList: [],
                accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
                brandInquiryRejectLoading: false,
                goodsChooseId: null,

                //拍摄任务
                ...taskPageParams
            };
        },
        async mounted() {
            const userInfoName = "hotsalegoods_selfuserinfo";
            // let selfInfo4Store = getStore(userInfoName);
            // if (selfInfo4Store) {
            //     this.selfInfo = selfInfo4Store;
            // } else {
            //     this.selfInfo = (await getUserInfo()).data;
            //     setStore(userInfoName, { ...this.selfInfo }, 60 * 60 * 2);
            // }

            this.selfInfo = (await getUserInfo()).data;
            if (this.selfInfo.fullName.includes('广东深圳分公司-跨境运营部') || this.selfInfo.fullName.includes('浙江义乌总公司-首力供应链项目部')) {
                this.isExceptDept = true;
            }
            setStore(userInfoName, { ...this.selfInfo }, 60 * 60 * 2);
            this.getHotSaleBrandPushNewCreatedUserNameList();

            //拍摄任务参数
            let res_group = await getDirectorGroupList();
            this.addTaskGroupList = res_group.data?.map(item => { return { value: item.key, label: item.value }; });
            let res_warehouse = await getShootOperationsGroup({ type: 3 });
            this.addTaskWarehouselist = res_warehouse?.map(item => { return { value: item.id, label: item.label }; });
            let pfrule = await rulePlatform();
            this.addTaskPlatformList = pfrule.options;


            this.onSearch();
        },
        methods: {
             correctProps(row) {
                this.$confirm('此操作将修正这条数据, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const { success } = await syncBuildGoodsGeneratePurchasePlan({ docid: row.extData.buildDocEntity.id })
                    if (!success) return
                    this.$message.success('修正成功')
                    this.onSearch();
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消'
                    });
                });
            },
            callbackGoodsCompeteId(val) {
              this.Filter.goodsCompeteId = val;
            },
            callBackPaymentPicture(data) {
                let _that = this;
                if(data){
                    _that.paymentInfoPictureList = _that.paymentInfoPictureList ? _that.paymentInfoPictureList : [];
                    let newarr = [];
                    data.map((item) => {
                        newarr.push({ url: item.url, name: item.fileName })
                    });
                    _that.paymentInfoPictureList = newarr;
                }
            },
            btnRejectOperationalInquiry(row) {
                this.brandInquiryRejectVisible = true;
                this.goodsChooseId = row.id;
            },
            btnBrandInquiryReject() {
                if (!this.rejectReason) {
                    this.$message({ type: "error", message: "请填写驳回原因!" });
                    return;
                }
                if (this.paymentInfoPictureList.length == 0){
                    this.$message({ type: "error", message: "请上传图片!" });
                    return;
                }
                this.$confirm("确定要驳回询价吗?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(async () => {
                    let imgJson = JSON.stringify(this.paymentInfoPictureList);
                    let param = { id: this.goodsChooseId, rejectReason: this.rejectReason, rejectImgUrl: imgJson };
                    this.brandInquiryRejectLoading = true;
                    let res = await rejectOperationalInquiry(param);
                    this.brandInquiryRejectLoading = false;
                    if (res?.success == true) {
                        this.onSearch();
                        this.$message({ type: "success", message: "驳回成功!" });
                        this.brandInquiryRejectVisible = false;
                    }
                });
            },
            async getHotSaleBrandPushNewCreatedUserNameList() {
                this.hotSaleBrandPushNewCreatedUserNameList = [];
                const res = await getHotSaleBrandPushNewCreatedUserNameList();
                if (res?.success == true) {
                    this.hotSaleBrandPushNewCreatedUserNameList = res.data;
                }
            },
            formatInfoQueryState: formatInfoQueryState,
            async setExportCols(){
             await this.$refs.table.setExportCols();
            },
            async onExport(opt) {
                var pager = this.$refs.pager.getPager();
                const params = { ...pager, ...this.pager, ...this.Filter, ...opt };
                var res = await exportHotSaleGoodsChooseAsync(params);
                if (!res?.data) {
                    return
                }
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '选品信息' + new Date().toLocaleString() + '_.xlsx')
                aLink.click()
            },
            beforeclose(){
                this.$refs.skuPage.beforeclose(this.datetime);
            },
            //拍摄任务
            async onOpenAddTask(row, mode) {

                let bfData = await GetDataBeforeBuildGoodsMediaTask({ "chooseId": row.id });
                if (bfData && bfData.errMsg) {
                    this.$alert(bfData.errMsg);
                    return;
                }

                this.addTaskLoading = true;
                this.addTaskOpentime = this.addTaskOpentime + 1;
                this.addTask = true;
                this.addTaskPageTitle = "创建任务";
                this.addTaskIslook = false;
                this.addTaskLoading = false;
                this.$nextTick(() => {
                    this.$refs.shootingvideotaskeditfrom.initaddform({
                        productShortName: row.goodsCompeteShortName,
                        platform: row.platform,
                        operationGroup: (this.selfInfo && this.selfInfo.groupId ? this.selfInfo.groupId.toString() : null),
                        dockingPeople: (this.selfInfo && this.selfInfo.nickName ? this.selfInfo.nickName : null),
                        extBzTypeArgs: {
                            extBzType: '建编码',
                            extBzIdOne: row.id,
                            extBzInWarehouse: bfData.warehouseCount > 0
                        }

                    });
                });
            },
            //保存拍摄任务
            async addTaskOnSubmit() {
                this.addTaskLoading = true;
                await this.$nextTick(function () {
                    this.$refs.shootingvideotaskeditfrom.onSubmit();
                });
                this.addTaskLoading = false;

            },
            onBeforeApplyBuildDoc(row) {
                let that = this;
                //建编码前，要确认选品。
                this.$confirm(`${row.recommendUserId ? '是否确认选品？' : '是否确认选品？确定后将发起确认流程，确认流程审批通过后（拼多多无审核流程直接通过），就可以进行建编码操作了。'}`, '', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    DingDingApproval_BuildGoodsDocBeforConfirm({ hotSaleGoodsChooseId: row.id }).then((reqRlt) => {
                        if (reqRlt.success) {
                            that.$message({ message:`${row.recommendUserId ? '已通过' : '确认选品流程已提交，请关注钉钉审批流（拼多多无审核流程直接通过）'}`, type: "success" });
                            that.onRefresh();
                        }
                    });
                }).catch(() => {
                });
            },
            //归档
            onSealClose(row) {
                let self = this;

                // if (!(
                //     (row.groupId == self.selfInfo.groupId && self.selfInfo.id == self.selfInfo.groupLeaderId)
                //     ||
                //     (row.userId == self.selfInfo.id)
                // )) {
                //     self.$alert('只有选品本人或对应组的组长才能进行手动归档！');

                //     return;
                // }

                this.$showDialogform({
                    path: `@/views/operatemanage/productalllink/hotsale/HostChooseSealCloseForm.vue`,
                    title: '已选品归档',
                    args: { ...row },
                    height: 300,
                    width: '600px',
                    callOk: self.onRefresh
                })
            },
            async decideState(row) {
                //决定状态，1做，0不做，null未选择
                this.$alert("开发中：" + row.decideState);
            },
            showShop(row) {
                if (row.shopUrl) {
                    var urlParams = getUrlParam(row.shopUrl);
                    var realUrl = urlParams["goto"];
                    if (realUrl)
                        window.open(realUrl);
                    else
                        window.open(row.shopUrl);
                } else {
                    this.$alert("当前店铺未导入链接！");
                }
            },
            showGoods(row) {
                if (row.goodsUrl) {
                    var urlParams = getUrlParam(row.goodsUrl);
                    var realUrl = urlParams["goto"];
                    if (realUrl)
                        window.open(realUrl);
                    else
                        window.open(row.goodsUrl);
                } else {
                    this.$alert("当前商品未导入链接！");
                }
            },
            //申请数据
            async reqCmRefDateInfo(row, isReQuery) {
                let that = this;
                if (isReQuery) {
                    //重查要确认
                    this.$confirm('已存在参谋数据，重新查询将覆盖当前数据，是否继续?', '', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        reqGoodsCmRefInfoQueryAsync({ id: row.id }).then((reqRlt) => {
                            if (reqRlt.success) {
                                that.$message({ message: '已提交申请', type: "success" });
                                that.onRefresh();
                            }
                        });
                    }).catch(() => {
                    });

                } else {
                    var reqRlt = await reqGoodsCmRefInfoQueryAsync({ id: row.id });
                    if (reqRlt.success) {
                        this.$message({ message: '已提交申请', type: "success" });
                        this.onRefresh();
                    }
                }


            },
            //查看参谋数据
            async showCmRefDateInfo(row) {
                if (row.platform == 1 || row.platform == 9) {
                    //天猫
                    var reqRlt = await getHotGoodsCmRefInfoAsync({ platform: row.platform, goodsCompeteId: row.goodsCompeteId, goodsId: row.goodsId });
                    if (!reqRlt.success || reqRlt.data == null) {
                        //this.$message({ message: '未查询到有效参谋数据！', type: "error" });
                        return;
                    }

                    this.dialogTmCmRefInfoVisible = true;

                    this.$nextTick(() => {
                        this.$refs.cmRefTmPage.setInfoData(reqRlt.data);
                    });
                } else if (row.platform == 2) {
                    //拼多多
                    this.dialogPddCmRefInfoVisible = true;
                    this.$nextTick(() => {
                        this.$refs.cmRefPddPage.pddsalescharLoadData(row.platform, row.goodsCompeteId);
                    });

                } else {
                    this.$alert("当前平台暂不支持！");
                }



            },
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderField = column.prop;
                    var bFields = [];// ['skuDataState', 'skuDataTime', 'cmRefInfoState', 'cmRefInfoLastOkTime'];
                    if (bFields.indexOf(orderField) > -1) {
                        orderField = "b." + orderField;
                    }

                    this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                }
                this.onSearch();
            },
            onImport() {
                this.dialogVisibleSyj = true
            },
            async uploadSuccess2(response, file, fileList) {
                fileList.splice(fileList.indexOf(file), 1);
            },
            async onSubmitupload2() {
                this.$refs.upload2.submit()
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.gettbdatalist1();
            },
            async gettbdatalist1() {
                if (this.Filter.gDate) {
                    this.Filter.startGDate = this.Filter.gDate[0];
                    this.Filter.endGDate = this.Filter.gDate[1];
                }
                else {
                    this.Filter.startGDate = null;
                    this.Filter.endGDate = null;

                }
                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,
                };
                this.listLoading = true;
                const res = await pageHotSaleGoodsChooseAsync(params);
                this.listLoading = false;
                res.data.list.forEach(item => {
                    //如果有推荐人,就判断推荐人是否和当前用户一致,否则就判断添加人是否一致
                    if (item.recommendUserId) {
                        item.exclude = item.recommendUserId == this.selfInfo.id ? true : false;
                    } else if (item.userId) {
                        item.exclude = item.userId == this.selfInfo.id ? true : false;
                    } else {
                        item.exclude = false;
                    }
                })
                this.total = res.data.total
                this.tbdatalist = res.data.list;


            },

            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
            openChooseGoodsForm: function () {
                let self = this;
                self.$showDialogform({
                    path: `@/views/operatemanage/productalllink/hotsale/AddChooseForm.vue`,
                    title: '新增选品',
                    args: { oid: '', mode: 0,isExceptDept:this.isExceptDept },
                    height: 700,
                    callOk: self.onRefresh
                });
            },
            //上架
            openSetOnSellForm: function (row) {
                let self = this;
                self.$showDialogform({
                    path: `@/views/operatemanage/productalllink/hotsale/SetOnSellForm.vue`,
                    title: '上架',
                    args: {
                        chooseId: row.id,
                        goodsCompeteName: row.goodsCompeteName,
                        goodsCompeteId: row.goodsCompeteId,
                        goodsCompeteShortName: row.goodsCompeteShortName
                    },
                    height: 700,
                    callOk: self.onRefresh
                });
            },
            async categoryChanged(val, cIdx) {
                var prixParentname = "";
                for (var i = 1; i <= cIdx; i++) {
                    if (prixParentname == "") {
                        prixParentname = this.Filter["categoryName" + i];
                    } else {
                        prixParentname += "-" + this.Filter["categoryName" + i];
                    }
                }
                for (var i = cIdx + 1; i <= 4; i++) {
                    this["categoryNameList" + i] = [];
                    this.Filter["categoryName" + i] = "";
                    if (i == cIdx + 1) {

                        var reqRlt = await getAllLinkCategoryNamesAsyncByParent({ parentName: prixParentname });
                        this["categoryNameList" + i] = reqRlt.data;
                    }
                }
            },
            categoryChanged1: function (val) {
                this.categoryChanged(val, 1);
            },
            categoryChanged2: function (val) {
                this.categoryChanged(val, 2);
            },
            categoryChanged3: function (val) {
                this.categoryChanged(val, 3);
            },

            //申请竞品SKU
            async onApplyCompeteGoodSKU(row, isFirst) {
                if (isFirst) {
                    var reqRlt = await applyCompeteGoodSKU({ hotSaleGoodsChooseId: row.id, reReq: false });
                    if (reqRlt?.success && reqRlt?.data) {
                        this.$message({ message: '已提交申请', type: "success" });
                        this.onRefresh();
                    }
                    else {
                        if (reqRlt?.data == false)
                            this.$message({ message: '申请失败，请联系管理员', type: "error" });
                    }
                } else {
                    this.$confirm('重新申请SKU成功后，已计算的利润需要重新计算，是否确认继续?', '', {
                        confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                    }).then(async () => {
                        var reqRlt = await applyCompeteGoodSKU({ hotSaleGoodsChooseId: row.id, reReq: true });
                        if (reqRlt?.success && reqRlt?.data) {
                            this.$message({ message: '已提交申请', type: "success" });
                            this.onRefresh();
                        }
                        else {
                            if (reqRlt?.data == false)
                                this.$message({ message: '申请失败，请联系管理员', type: "error" });
                        }
                    }).catch(() => {
                        //this.$message({ type: 'info', message: '已取消' });
                    });
                }

            },
            //编辑竞品SKU
            onEditCompeteGoodSKU(row) {
                // let self = this;
                // this.$showDialogform({
                //     path: `@/views/operatemanage/productalllink/hotsale/hotsalegoodschooselistsku.vue`,
                //     args: { oid: row.id, mode: 2 },
                //     title: "竞品利润",
                //     top: '50px',
                //     autoTitle: false,
                //     width: '95%',
                //     height: '95%',
                //     callOk: () => {
                //         self.onRefresh();
                //     },
                //     callCancel: ()=>{
                //         self.beforeclose();
                //     }
                // });

                this.datetime = row.lastProfitSumDate;
                this.skuSaveHiddle = true;
                this.dialogSkuVisible = true;
                this.$nextTick(() => {
                    this.$refs.skuPage.loadData({ oid: row.id, mode: 2 });
                    // this.$refs.skuPage.getSkuTableData(row.id, true);
                });
            },
            //查看竞品SKU
            async onSeeCompeteGoodSKU(row) {

                let self = this;
                this.$showDialogform({
                    path: `@/views/operatemanage/productalllink/hotsale/hotsalegoodschooselistsku.vue`,
                    args: { oid: row.id, mode: 3 },
                    title: "查看竞品利润",
                    autoTitle: false,
                    width: '95%',
                    height: '95%',
                    callOk: () => {
                        self.onRefresh();
                    }
                });

                // this.skuSaveHiddle = false;
                // this.dialogSkuVisible = true;
                // this.$nextTick(() => {
                //     this.$refs.skuPage.getSkuTableData(row.id, false);
                // });
            },
            //保存SKU
            async onSkuSave(isEnd) {
                this.skuSaveLoading = true;

                // let save = await this.$refs.skuPage.saveSkuTableData(isEnd);
                let save = await this.$refs.skuPage.onSave(true);
                await this.onSearch();
                // this.skuSaveLoading = false;
                // if (save) {
                //     if (isEnd) {
                //         this.dialogSkuVisible = false;
                //         this.onRefresh();
                //     }
                // }
            },
            //登记采样
            async onRegisterSkuOrder(row) {
                const rlt = await registerSkuOrderAsync({ chooseId: row.id });

                if (rlt.success) {
                    this.$message({ message: '已登记采样', type: "success" });
                    this.onRefresh();
                }
            },
            //建编码
            async onBuildGoodsDoc(row) {
                const params = {
                    currentPage: 1,
                    pageSize: 100,
                    orderBy: "CreatedTime",
                    isAsc: false,
                    hotSaleGoodsChooseId: (row.id ?? 0),
                };
                this.dialogBuildGoodsDocListVisible = true;
                this.jdmainLoading = true;
                const res = await pageHotSaleGoodsBuildGoodsAsync(params);
                this.jdmainLoading = false;
                this.jdmainlist = res?.data.list;
                if (this.jdmainlist.length > 0) {
                    await this.onBuildGoodsDocDtl(this.jdmainlist[0].id);
                }
                else {
                    this.jddtllist = [];
                }
                this.selHotSaleGoodsChooseId = (row.id ?? 0);
            },
            async onBuildGoodsDocDtl(parentId) {
                const params = {
                    currentPage: 1,
                    pageSize: 100,
                    orderBy: "CreatedTime",
                    isAsc: false,
                    parentId: (parentId ?? 0),
                };
                this.jddtlLoading = false;
                const res = await pageHotSaleGoodsBuildGoodsCodeAsync(params);
                this.jddtlLoading = false;
                this.jddtllist = res.data.list;
            },
            async onjdmainCellClick(row, column, cell) {
                var isLoad = true;
                if (this.jddtllist.length > 0) {
                    var parentId = this.jddtllist[0].parentId;
                    if (row.id == parentId)
                        isLoad = false;
                }
                if (isLoad == true) {
                    await this.onBuildGoodsDocDtl(row.id);
                }
            },
            //新建编码功能
            async onBuildGoodsDocNew(row, mode) {
                this.buildGoodsDocHiddle = true;
                this.dialogBuildGoodsDocVisible = true;
                this.$nextTick(() => {
                    this.$refs.buildGoodsDocPage.getSkuTableData(row.id, mode != 3);
                });
            },
            async onBuildGoodsDocAdd() {
                this.buildGoodsDocHiddle = true;
                this.dialogBuildGoodsDocVisible = true;
                this.$nextTick(() => {
                    this.$refs.buildGoodsDocPage.getSkuTableData(this.selHotSaleGoodsChooseId, true);
                });
            },
            async onEditBuildGoodsDoc(row, edit) {
                this.buildGoodsDocHiddle = true;
                this.dialogBuildGoodsDocVisible = true;
                this.seldocId = row.id;
                if (!edit) {
                    edit = false;
                    this.buildGoodsDocHiddle = false;
                }
                this.$nextTick(() => {
                    this.$refs.buildGoodsDocPage.getSkuTableData(row.id, edit);
                });
            },
            //保存竞品编码建档
            async onBuildGoodsDocSave(isApply) {
                this.buildGoodsDocLoading = !isApply;
                this.requestCodeLoading = isApply;
                let rlt = await this.$refs.buildGoodsDocPage.saveSkuTableData(isApply);
                this.buildGoodsDocLoading = false;
                this.requestCodeLoading = false;
                if (rlt && rlt.success) {
                    this.$message({ message: '保存成功', type: "success" });
                    this.dialogBuildGoodsDocVisible = false;

                    // //刷新主明细
                    // await this.onBuildGoodsDoc({ id: this.selHotSaleGoodsChooseId });
                    // if (this.jddtllist.length > 0 && this.jddtllist[0].parentId == this.seldocId) {
                    //     await this.onBuildGoodsDocDtl(this.seldocId);
                    // }
                    this.onRefresh();
                }
            },
            //申请编码-走钉钉审批
            async onApprovalGoodsDoc(row) {
                this.$confirm('申请编码将推送到钉钉审批，是否继续?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    const res = await dingDingApproval_BuildGoodsDoc({ docId: row.id });
                    if (!res?.success) {
                        //this.$message({ type: 'success', message: res?.msg });
                    } else {
                        this.$message({ type: 'success', message: '已发起申请，请关注钉钉审批流程!' });
                    }
                    //刷新主明细
                    await this.onBuildGoodsDoc({ id: this.selHotSaleGoodsChooseId });
                    if (this.jddtllist.length > 0 && this.jddtllist[0].parentId == this.seldocId) {
                        await this.onBuildGoodsDocDtl(this.seldocId);
                    }
                    this.onRefresh();
                }).catch(() => {
                    this.$message({ type: 'info', message: '已取消' });
                });
            },
            async showLog(row) {
                this.showLogData.goodsCompeteId = row.goodsCompeteId;
                this.showLogData.goodsCompeteName = row.goodsCompeteName;
                this.showLogData.groupName = row.groupName;
                this.showLogData.logList = [];
                this.dialogShowLog = true;
                let params = {
                    id: (row.id ?? 0),
                };
                this.logTableLoding = true;
                let res = await getHotSaleGoodsChooseLog(params);
                this.logTableLoding = false;
                this.showLogData.logList = res?.data;
            },
            async trunToOther(row) {
                let self = this;
                this.$showDialogform({
                    path: `@/views/operatemanage/productalllink/hotsale/hotsalegoodschoosetranofform.vue`,
                    args: { ...row, oid: row.id },
                    title: "转派运营",
                    width: '400px',
                    height: 400,
                    callOk: () => {
                        self.onRefresh();
                    }
                });
            },
            async Purchase(row) {
                console.log(row)
                this.hotstockinapplylistFilter.chooseId = row.id;
                this.hotstockinapplylistVisible = true;
                this.$nextTick(() => {
                    this.$refs.hotstockinapplylistPage.onSearch();
                });
            }
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }

    ::v-deep .xAllDataStateCol202301111351001 div {
        text-align: center;
        padding-left: 1px !important;
        padding-right: 1px !important;
    }
    ::v-deep .xAllDataStateCol202301111351001 p {
        padding: 0;
        text-align: center;
        margin: 1px 1px 1px 1px;
        font-size: 9pt;
        line-height: 14px;
    }

    // ::v-deep .xAllDataStateCol202301111351001primary{
    //     background-color:#409EFF;
    //     color: white;
    // }
    // ::v-deep .xAllDataStateCol202301111351001info{
    //     background-color:#909399;
    //     color: white;
    // }
    // ::v-deep .xAllDataStateCol202301111351001success{
    //     background-color: #67C23A;
    //     color: white;
    // }
    // ::v-deep .xAllDataStateCol202301111351001warning{
    //     background-color: #E6A23C;
    //     color: white;
    // }
    // ::v-deep .xAllDataStateCol202301111351001danger{
    //     background-color: #F56C6C;
    //     color: white;
    // }
</style>
<style >
    .el-image__inner {
        max-width: 50px;
        max-height: 50px;
    }
</style>
