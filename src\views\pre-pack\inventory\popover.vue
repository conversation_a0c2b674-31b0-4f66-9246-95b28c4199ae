<template>
  <el-popover
    title="分仓库存明细"
    :width="width"
    trigger="click"
    @show="init"
  >
    <MyContainer>
      <vxetablebase :id="'popover202408041855'"
        ref="table"
        v-loading="loading"
        :is-index="true"
        :hasexpand="true"
        :tablefixed="true"
        :has-seq="false"
        :border="true"
        :table-data="tableData"
        :table-cols="tableCols"
        :is-selection="false"
        :is-select-column="false"
        :is-index-fixed="false"
        style="width: 100%;  margin: 0"
        :height="height"
        @sortchange="sortchange"
      />
    </MyContainer>
    <el-button slot="reference" type="text">
      <slot />
    </el-button>
  </el-popover>
</template>
<script>
import MyContainer from '@/components/my-container'
import vxetablebase from '@/components/VxeTable/yh_vxetable.vue'
import {
  pageGetData, getColumns
} from '@/api/vo/inventory'
export default {
  name: 'InventoryPopover',
  components: {
    MyContainer, vxetablebase
  },
  props: {
    goodsCodes: {
      type: Array,
      default: () => { return [] }
    },
    columns: {
      type: Array,
      default: () => { return [] }
    },
    width: {
      type: Number,
      default: 600
    },
    height: {
      type: String,
      default: '600'
    }
  },
  data() {
    return {
      rules: {
      },
      query: {
        total: 0,
        currentPage: 1,
        pageSize: 500,
        orderBy: null,
        isAsc: false
      },
      tableCols: [],
      tableData: [],
      loading: false,
      isExport: false
    }
  },
  mounted() {
  },
  methods: {
    init() {
      this.getColumns()
      this.getList()
    },
    async getColumns() {
      const { data, success } = await getColumns()
      if (success) {
        this.tableCols = data.filter(a => this.columns.indexOf(a.field) > -1)
      }
    },
    async getList(type) {
      if (type === 'search') {
        this.query.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      try {
        this.query.goodsCodes = this.goodsCodes
        const { data: { list, total, summary }, success } = await pageGetData(this.query)
        if (success) {
          this.tableData = list
          this.query.total = total
          this.summaryarry = summary
        } else {
          this.$message.error('获取列表失败')
        }
      } catch (error) {
        this.$message.error('获取列表失败')
      } finally {
        this.loading = false
      }
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.query.orderBy = prop
        this.query.isAsc = order.indexOf('descending') === -1
        this.getList()
      }
    }
  }
}
</script>
    <style scoped lang="scss">
    .top {
      display: flex;
      margin-bottom: 10px;
      flex-wrap: wrap;

      .publicCss {
        width: 200px;
        margin: 0 10px 5px 0;
      }
    }
    </style>
