<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="淘系微信结算数据" name="tab1" style="height: 100%;">
                <financialDetailTxWx :filter="filter" ref="financialDetailTxWx" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="淘工厂微信结算数据" name="tab2" style="height: 100%;">
                <financialDetailTgcWx :filter="filter" ref="financialDetailTgcWx" style="height: 100%;" />
            </el-tab-pane>
        </el-tabs>
    </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";
import financialDetailTxWx from '@/views/bookkeeper/details/financialDetailTxWx.vue'
import financialDetailTgcWx from '@/views/bookkeeper/details/financialDetailTgcWx.vue'

export default {
    name: "financialDetailWx",
    components: { MyContainer, financialDetailTgcWx, financialDetailTxWx},
    data() {
        return {
            that: this,
            pageLoading: '',
            filter: {
            },
            shopList: [],
            userList: [],
            groupList: [],
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            activeName: 'tab1'
        };
    },
    mounted() {

    },
    methods: {


    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
