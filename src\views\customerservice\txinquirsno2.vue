<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='inquirslist' @select='selectchange' :isSelection='false' :tableCols='tableCols'
            :summaryarry="summaryarry" :loading="listLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="filter.enmPddGroupType" placeholder="组名称" style="width:120px;" disabled>
                            <el-option label="售前" :value=0 />
                            <el-option label="售后" :value=1 />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="filter.shopName" v-model.trim="filter.shopName" clearable placeholder="店铺"
                            style="width:200px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="filter.snick" v-model.trim="filter.snick" placeholder="昵称" clearable
                            style="width:200px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <datepicker v-model="filter.sdate"></datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getinquirsList" />
        </template>


        <el-dialog title="添加客服人员分组管理信息" :visible.sync="addgroupdialogVisibleSyj" width="40%" label-width="120px"
            :close-on-click-modal="false" v-dialogDrag>
            <span>
                <el-form :model="addFormNo" :rules="addFormRules">
                    <el-form-item prop="groupName" label="分组">
                        <el-input style="width:83%" v-model="addFormNo.groupName" :maxlength="200"></el-input>
                    </el-form-item>
                    <el-form-item prop="groupManager" label="组长">
                        <el-input style="width:83%" v-model="addFormNo.groupManager" :maxlength="255"></el-input>
                    </el-form-item>
                    <el-form-item prop="sname" label="姓名">
                        <el-input style="width:83%" v-model="addFormNo.sname" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="snick" label="旺旺">
                        <el-input style="width:83%" v-model="addFormNo.snick" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item prop="shopName" label="店铺">
                        <el-input style="width:83%" v-model="addFormNo.shopName" :maxlength="200"></el-input>
                    </el-form-item>
                    <el-form-item prop="phoneNo" label="绑定手机号">
                        <el-input style="width:73%" v-model="addFormNo.phoneNo" :maxlength="20"></el-input>
                    </el-form-item>
                    <el-form-item prop="joinDate" label="入组日期">
                        <el-date-picker v-model="addFormNo.joinDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            type="date" style="width:63%" placeholder="选择日期">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item prop="leaveDate" label="离组日期">
                        <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="addFormNo.leaveDate"
                            style="width:63%" type="date" placeholder="选择日期">
                        </el-date-picker>
                    </el-form-item>
                </el-form>
            </span>

            <span slot="footer" class="dialog-footer">
                <el-button @click="addgroupdialogVisibleSyj = false">关闭</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="onAddGroup">确定</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import { GetTaoBaoShouHouInquirsNotExistsList, addTaoBaoShouHouGroupAsync } from '@/api/customerservice/taobaoshouhou'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import Decimal from 'decimal.js';

function reserve(data) {
    if (data == null) {
        return "0.0";
    }
    var result = (Math.floor(data * 10) / 10).toString();
    console.log(result);
    if (!result.includes(".")) {
        result = result + ".0";
    }
    return result;
};
function precision(number, multiple) {
    return new Decimal(number).mul(new Decimal(multiple));
}

const tableCols = [
    {
        istrue: true, type: "button", label: '操作', width: "90", btnList: [{
            label: "生成分组", handle: (that, row) => that.onCreateGroup(row)
        }]
    },
    { istrue: true, prop: 'shopName', label: '店铺', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'snick', label: '旺旺', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'inquirs', label: '咨询人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'receiveds', label: '接待人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'responseTime', label: '平均响应(秒)', width: '110', sortable: 'custom' },
    { istrue: true, prop: 'salesvol', label: '销售额', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'satisfaction', label: '客户满意率', width: '100', sortable: 'custom', formatter: (row) => (row.satisfaction * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'lateReceiveds', label: '慢接待人数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'responseRate', label: '回复率', width: '80', sortable: 'custom', formatter: (row) => (row.responseRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'verySatisfied', label: '很满意', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'threeResponseRate', label: '平台3分钟响应率', width: '130', sortable: 'custom', formatter: (row) => (row.threeResponseRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'satisDegree', label: '满意度', width: '100', sortable: 'custom', formatter: (row) => row.satisDegree !== null ? (row.satisDegree).toFixed(2) + '%' : '0%' },
    { istrue: true, prop: 'sdate', label: '日期', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.sdate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'createdTime', label: '导入时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'batchNumber', label: '导入批次', width: '170', sortable: 'custom' },
];
export default {
    name: "txinquirsno",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
    data() {
        return {
            that: this,
            filter: {
                enmPddGroupType: 1,
                sdate: [formatTime(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30), "YYYY-MM-DD 00:00:00"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],
            },
            inquirslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],


            addgroupdialogVisibleSyj: false,
            addFormNo: {},
            addFormRules: {
                groupName: [{ required: true, message: '请输入', trigger: 'blur' }],
                groupManager: [{ required: true, message: '请输入', trigger: 'blur' }],
                sname: [{ required: true, message: '请输入', trigger: 'blur' }],
                snick: [{ required: true, message: '请输入', trigger: 'blur' }],
                shopName: [{ required: true, message: '请输入', trigger: 'blur' }],
                leaveDate: [{ required: true, message: '请输入', trigger: 'blur' }],
                joinDate: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
        };
    },
    async mounted() {
        var that = this;
    },
    methods: {
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getinquirsList();
        },

        async getinquirsList() {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            this.listLoading = true;
            const res = await GetTaoBaoShouHouInquirsNotExistsList(params);
            this.listLoading = false;

            this.total = res.data.total;
            this.inquirslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        onCreateGroup(row) {
            this.addFormNo = {};
            this.addFormNo.shopName = row.shopName;
            this.addFormNo.snick = row.snick;

            console.log(this.addFormNo);

            this.addgroupdialogVisibleSyj = true;
        },
        async onAddGroup() {
            var that = this;
            if (this.addFormNo.groupName == "" || this.addFormNo.groupName == null ||
                this.addFormNo.sname == "" || this.addFormNo.sname == null ||
                this.addFormNo.snick == "" || this.addFormNo.snick == null ||
                this.addFormNo.shopName == "" || this.addFormNo.shopName == null ||
                this.addFormNo.leaveDate == "" || this.addFormNo.leaveDate == null ||
                this.addFormNo.joinDate == "" || this.addFormNo.joinDate == null) {
                that.$message({ message: '请输入必填字段', type: "error" });
                return;
            }
            let add = await addTaoBaoShouHouGroupAsync(that.addFormNo);
            if (add?.success) {
                that.$message({ message: '已添加', type: "success" });
                that.onSearch();
                that.addFormNo = {};
                that.addgroupdialogVisibleSyj = false;
            }
            else {
                //that.$message({ message: '发生异常，请刷新后重试', type: "error" });
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
