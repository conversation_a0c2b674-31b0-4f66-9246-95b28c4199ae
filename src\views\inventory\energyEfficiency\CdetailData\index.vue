<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="数据开始日期" end-placeholder="数据结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :clearable="false" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-select v-model="ListInfo.regionName" placeholder="片区" class="publicCss" clearable filterable remote
          reserve-keyword :remote-method="(query) => remoteMethod(query, 1)" multiple collapse-tags>
          <el-option v-for="item in fullNameList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.platform" placeholder="平台" class="publicCss" clearable filterable multiple
          collapse-tags>
          <el-option v-for="item in platformlist" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.groupId" placeholder="运营组" class="publicCss" clearable remote reserve-keyword filterable multiple collapse-tags>
          <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="ListInfo.userName" placeholder="姓名" class="publicCss" clearable remote reserve-keyword
          :remote-method="(query) => remoteMethod(query, 3)" filterable multiple collapse-tags>
          <el-option v-for="item in directorlist" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.employeeStatus" placeholder="员工状态" class="publicCss" clearable filterable multiple
          collapse-tags>
          <el-option key="试用" label="试用" :value="2" />
          <el-option key="'正式'" label="正式" :value="3" />
          <el-option key="'离职'" label="离职" :value="-1" />
        </el-select>
        <el-button type="primary" @click="getList('search')">查询</el-button>
        <el-button type="primary" @click="exportProps('search')">导出</el-button>
        <div style="color: red; margin-left: 5px;font-size: 14px;display: flex;align-items: center;">
          数据来源:各平台运营人员业绩统计
        </div>
      </div>
    </template>
    <vxetablebase :id="'cdetailData202112211010'" :tablekey="'cdetailData202112211010'" ref="table" :that='that'
      :border="true" :showheaderoverflow="false" :isIndex='true' :hasexpand='true' :tablefixed='true'
      @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'" @cellStyle="cellStyle" cellStyle @footerCellStyle="footerCellStyle">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog :visible.sync="turnoverdaysdialog" width="85%" v-dialogDrag>
      <div>
        <detailDialogBox ref="refdetailDialogBox" v-if="turnoverdaysdialog" :ListInfo="ListInfo"
          :optionData="optionData" />
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getOperatePerformancePage, getOperateListValue, getOperatePerformanceExport, getOperateListValueRemote } from '@/api/people/peoplessc.js';
import detailDialogBox from "./detailDialogBox.vue";
import dayjs from 'dayjs'
import {getDirectorGroupList} from "@/api/operatemanage/base/shop";
const platformlist = [
  '淘系', '拼多多', '抖音', '京东'
]
const commonStyle = {
  style: 'font-weight: bold',
  headerBgColor: '#93d2f3',
}
const tableCols = [
  { sortable: 'custom', width: '100', align: 'center', prop: 'regionName', label: '片区', style: 'font-weight: bold', headerBgColor: '#FFB6C1' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'platform', label: '平台', style: 'font-weight: bold', headerBgColor: '#FFB6C1' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'deptName', label: '运营组', style: 'font-weight: bold', headerBgColor: '#FFB6C1' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'userName', label: '姓名', style: 'font-weight: bold', headerBgColor: '#FFB6C1' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'employeeStatusStr', label: '员工状态', style: 'font-weight: bold', headerBgColor: '#FFB6C1' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'hiredDate', label: '入职时间', style: 'font-weight: bold', headerBgColor: '#FFB6C1' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'seniority', label: '工龄', style: 'font-weight: bold', headerBgColor: '#FFB6C1' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'overtimeDurationMonth', label: '月均加班时长', style: 'font-weight: bold', headerBgColor: '#FFFFE0', type: 'click', handle: (that, row) => that.onTrendChart(row, '月均加班时长') },
  { sortable: 'custom', width: '100', align: 'center', prop: 'overtimeDurationDay', label: '日均加班时长', style: 'font-weight: bold', headerBgColor: '#FFFFE0', type: 'click', handle: (that, row) => that.onTrendChart(row, '日均加班时长') },
  { sortable: 'custom', width: '100', align: 'center', prop: 'orderCount', label: '订单量', style: 'font-weight: bold', headerBgColor: '#FFFFE0', type: 'click', handle: (that, row) => that.onTrendChart(row, '订单量') },
  { sortable: 'custom', width: '100', align: 'center', prop: 'saleAmount', label: '销售金额', style: 'font-weight: bold', headerBgColor: '#FFFFE0', type: 'click', handle: (that, row) => that.onTrendChart(row, '销售金额') },
  { sortable: 'custom', width: '150', align: 'center', prop: 'profit33', label: '毛四利润(发生)', style: 'font-weight: bold', headerBgColor: '#FFFFE0', type: 'click', handle: (that, row) => that.onTrendChart(row, '毛四利润(发生)') },
  { sortable: 'custom', width: '150', align: 'center', prop: 'profit33RateStr', label: '毛四利润率', style: 'font-weight: bold', headerBgColor: '#FFFFE0', type: 'click', handle: (that, row) => that.onTrendChart(row, '毛四利润率'), formatter: (row) => row.profit33RateStr ? row.profit33RateStr + '%' : '' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isOvertimeDurationMonth', label: '低于月加班均值', ...commonStyle, formatter: (row) => row.isOvertimeDurationMonth == true ? '是' : row.isOvertimeDurationMonth == false ? '否' : '' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isSaleAmount', label: '低于销售额均值', ...commonStyle, formatter: (row) => row.isSaleAmount == true ? '是' : row.isSaleAmount == false ? '否' : '' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isProfit33', label: '低于毛四利润均值', ...commonStyle, formatter: (row) => row.isProfit33 == true ? '是' : row.isProfit33 == false ? '否' : '' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isProfit33Rate', label: '低于毛四利润率均值', ...commonStyle, formatter: (row) => row.isProfit33Rate == true ? '是' : row.isProfit33Rate == false ? '否' : '' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isThree', label: '三项低于均值', ...commonStyle, formatter: (row) => row.isThree == true ? '是' : row.isThree == false ? '否' : '' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isFour', label: '四项低于均值', ...commonStyle, formatter: (row) => row.isFour == true ? '是' : row.isFour == false ? '否' : '' },
]
export default {
  name: "cdetailData",
  components: {
    MyContainer, vxetablebase, detailDialogBox
  },
  data() {
    return {
      optionData: {},//详情参数
      fullNameList: [],//片区
      turnoverdaysdialog: false,//详情弹窗
      platformlist,//平台
      grouplist: [],//运营组
      directorlist: [],//姓名
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startQueryTime: null,//开始时间
        endQueryTime: null,//结束时间
        regionName: [],//片区
        platform: [],//平台
        // deptName: [],//运营组
        userName: [],//姓名
        employeeStatus: [],//员工状态
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.init()
    await this.getList()
  },
  methods: {
    async remoteMethod(query, type) {
      if (type === 1) {
        let { data } = await getOperateListValueRemote({ currentPage: 1, pageSize: 50, value: query, fieldName: 'fullName' });
        this.fullNameList = data;
      } else if (type === 2) {
        var res2 = await getDirectorGroupList();
        this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
       /* let { data } = await getOperateListValueRemote({ currentPage: 1, pageSize: 50, value: query, fieldName: 'deptName' });
        this.grouplist = data;*/
      } else if (type === 3) {
        let { data } = await getOperateListValueRemote({ currentPage: 1, pageSize: 50, value: query, fieldName: 'userName' });
        this.directorlist = data;
      }
    },
    //导出数据,使用时将下面的方法替换成自己的接口
    async exportProps() {
      this.isExport = true
      let { regionName, platform, groupId, userName, employeeStatus } = this.ListInfo;
      function joinMultipleChoices(choices) {
        return choices.join(',');
      }
      const fullNameMultiple = joinMultipleChoices(regionName);
      const platformMultiple = joinMultipleChoices(platform);
      const groupIdMultiple = joinMultipleChoices(groupId);
      const userNameMultiple = joinMultipleChoices(userName);
      const employeeStatusMultiple = joinMultipleChoices(employeeStatus);
      await getOperatePerformanceExport({
        ...this.ListInfo,
        regionName: fullNameMultiple,
        platform: platformMultiple,
        groupId: groupIdMultiple,
        userName: userNameMultiple,
        employeeStatus: employeeStatusMultiple,
      }).then((data) => {
        if (data) {
          const aLink = document.createElement("a");
          let blob = new Blob([data], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', '明细数据' + new Date().toLocaleString() + '.xlsx')
          aLink.click()
          this.isExport = false
        }
      }).catch(() => {
        this.isExport = false
      })
    },
    footerCellStyle(row, callback) {
      let cols = []
      this.tableCols.forEach(item => {
        if (item.cols && item.cols.length > 0) {
          item.cols.forEach(item1 => {
            cols.push(item1)
          })
        } else {
          cols.push(item)
        }
      })
      const res = cols.find(item => item.prop == row.column.field)
      for (let i = 0; i <= cols.length; i++) {
        if (res?.headerBgColor) {
          callback({ backgroundColor: res.headerBgColor })
        }
      }
    },
    booleanFormatter(row, prop) {
      return row[prop] === true ? '是' : row[prop] === false ? '否' : '';
    },
    async cellStyle(row, column, callback) {
      const colorMapping = {
        '#FFB6C1': [
          'regionName',
          'platform',
          'deptName',
          'userName',
          'employeeStatusStr',
          'hiredDate',
          'seniority'
        ],
        '#FFFFE0': [
          'overtimeDurationMonth',
          'overtimeDurationDay',
          'orderCount',
          'saleAmount',
          'perSaleAmount',
          'profit33',
          'profit33Rate',
          'profit33RateStr'
        ],
        '#93d2f3': [
          'isOvertimeDurationMonth',
          'isSaleAmount',
          'perProfit33Rate',
          'perProfit33',
          'isProfit33',
          'isProfit33Rate',
          'isThree',
          'isFour'
        ]
      };
      for (const [color, fields] of Object.entries(colorMapping)) {
        if (fields.includes(column.field)) {
          return callback({ backgroundColor: color });
        }
      }
    },
    async onTrendChart(row, type) {
      let { data: data2 } = await getOperateListValueRemote({ currentPage: 1, pageSize: 50, value: '', fieldName: 'userName' })
      this.directorlist = data2
      this.optionData = JSON.parse(JSON.stringify({
        fullNameList: this.fullNameList,
        platformlist: this.platformlist,
        grouplist: this.grouplist,
        directorlist: this.directorlist,
        row,
        type,
      }))

      this.turnoverdaysdialog = true
    },
    async changeTime(e) {
      this.ListInfo.startQueryTime = e ? e[0] : null
      this.ListInfo.endQueryTime = e ? e[1] : null
    },
    // async exportProps() {
    //   this.loading = true
    //   const { data } = await exportStatData(this.ListInfo)
    //   this.loading = false
    //   const aLink = document.createElement("a");
    //   let blob = new Blob([data], { type: "application/vnd.ms-excel" })
    //   aLink.href = URL.createObjectURL(blob)
    //   aLink.setAttribute('download', '运营能效-明细数据' + new Date().toLocaleString() + '.xlsx')
    //   aLink.click()
    // },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        // this.ListInfo.startQueryTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        // this.ListInfo.endQueryTime = dayjs().format('YYYY-MM-DD')
        this.ListInfo.startQueryTime = dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),//开始时间
          this.ListInfo.endQueryTime = dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),//结束时间
          this.timeRanges = [this.ListInfo.startQueryTime, this.ListInfo.endQueryTime]
      }
      this.loading = true
      function joinMultipleChoices(choices) {
        return choices.join(',');
      }
      let { regionName, platform, groupId, userName, employeeStatus } = this.ListInfo;
      const fullNameMultiple = joinMultipleChoices(regionName);
      const platformMultiple = joinMultipleChoices(platform);
      const groupIdMultiple = joinMultipleChoices(groupId);
      const userNameMultiple = joinMultipleChoices(userName);
      const employeeStatusMultiple = joinMultipleChoices(employeeStatus);
      const { data, success } = await getOperatePerformancePage({
        ...this.ListInfo,
        regionName: fullNameMultiple,
        platform: platformMultiple,
        groupId: groupIdMultiple,
        userName: userNameMultiple,
        employeeStatus: employeeStatusMultiple,
      });
      if (success) {
        this.tableData = data.list
        this.tableData.forEach((item) => {
          item.hiredDate = item.hiredDate ? dayjs(item.hiredDate).format('YYYY-MM-DD') : ''
        })
        this.total = data.total
        // this.summaryarry = data.summary
        let sum = data.summary
        this.summaryarry = Object.fromEntries(
          Object.entries(sum).map(([key, value]) => [`${key}_sum`, value])
        );
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },

    async init() {
      const params = { currentPage: 1, pageSize: 50 }
      let { data } = await getOperateListValueRemote({ currentPage: 1, pageSize: 50, value: '', fieldName: 'fullName' })
      this.fullNameList = data
      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
      let { data: data2 } = await getOperateListValueRemote({ currentPage: 1, pageSize: 50, value: '', fieldName: 'userName' })
      this.directorlist = data2
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 160px;
    margin-right: 5px;
  }
}

::v-deep .el-select__tags-text {
  max-width: 35px;
}
</style>
