<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                
            </el-button-group>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="false"  
          :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='tableData' 
          :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:98%;margin: 0">
        </ces-table>
        <!--分页-->
        <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
        </template>
    </my-container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import MyContainer from '@/components/my-container';
import cesTable from '@/components/Table/table.vue';
import {formatPlatform,formatYesornoBool,formatLinkProCode,platformlist} from "@/utils/tools";
import {getPurchaseReturnGoodsDetail} from '@/api/inventory/purchase'

const tableCols =[

      {istrue:true,prop:'io_id',label:'退货单号', width:'100',sortable:'custom'},
      {istrue:true,prop:'sku_id',label:'商品编码', width:'120',sortable:'custom'},
      {istrue:true,prop:'name',label:'商品名称',sortable:'custom', width:'349'},
      {istrue:true,prop:'qty',fix:true,label:'数量', width:'100',sortable:'custom'},
      {istrue:true,prop:'cost_price',label:'单价',sortable:'custom', width:'100',formatter:(row)=> !row.cost_price?  (row.cost_amount/row.qty): row.cost_price},
      {istrue:true,prop:'cost_amount',label:'金额', width:'100',sortable:'custom'},
      {istrue:true,prop:'i_id',label:'款式编码', width:'120',sortable:'custom'},
      {istrue:true,prop:'remark',label:'备注', width:'200',sortable:'custom'},
];

const tableHandles=[
       // {label:"导出", handle:(that)=>that.onExport()},
      ];

const startDate = formatTime(dayjs().subtract(1,'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
    name:"Users",
    components:{MyContainer,cesTable},
    data(){
        return {
          
            that:this, 
            myfilter:{
                orderNo:null,
                orderNoInner:null,
                expressNo:null
            },                      
            tableCols:tableCols,
            tableHandles:tableHandles,
            tableData:[],
            total: 0,
            pager:{OrderBy:"",IsAsc:false},
            listLoading: false,
            pageLoading: false,
            summaryarry:{},    
            sels:[],

        };
    },
    props:{
        filter:{
            io_id:null,
        },   
    },

    methods:{
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            await this.onSearch();
            },
            async onSearch(){
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async getList(){
            this.filter
            var that=this;
            this.listLoading=true;
            var pager = this.$refs.pager.getPager();
            const params = {...pager,...this.pager,...this.filter,...this.myfilter};
            
            const res = await getPurchaseReturnGoodsDetail(params).then(res=>{
                that.total = res.data?.total;
               
                that.tableData = res.data?.list;
                that.summaryarry=res.data?.summary;               
            });
            this.listLoading=false;
        },
        
    }
})
</script>
<style scoped>
    ::v-deep .el-table__fixed-footer-wrapper tbody td{
        color:blue;
    }
</style>

