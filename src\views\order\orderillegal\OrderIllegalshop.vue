<template>
  <container v-loading="pageLoading"> 
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
          :tableData='list' :tableHandles='tableHandles' :tableCols='tableCols' :isSelection="false" @select="selectchange" @cellclick="cellclick"
          :loading="listLoading">
        </ces-table>       
        <template #footer>
          <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
        </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%">
          <el-form class="ad-form-query" :inline="true" :model="filterImport" @submit.native.prevent>
              <el-form-item label="日期:">
                <el-date-picker style="width:130px" v-model="filterImport.occurrenceTime" type="date"
                  format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="发生日期" :picker-options="pickerOptions"></el-date-picker>
              </el-form-item>
              <el-form-item label="平台:">
                <el-select style="width:130px" v-model="filterImport.platform" placeholder="请选择" :clearable="true" :collapse-tags="true">
                  <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
          </el-form>
          <el-alert title="温馨提示：请选取两个文件，文件名需分别包含“原始订单明细”、“每日明细”。" type="success" :closable="false" style="margin-bottom:10px;">
          </el-alert>
          <span>
            <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="2" action accept=".xlsx"
                 :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}   </el-button>
            </el-upload>
          </span>
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">关闭</el-button>
          </span>
      </el-dialog>
  </container>
</template>

<script>
import container from '@/components/my-container/noheader'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatPlatform,formatSendWarehouse} from "@/utils/tools";
import { rulePlatform} from "@/utils/formruletools";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { 
  importOrderIllegal,
  getOrderIllegalShopList,
  exportOrderIllegalShop,
  getOrderIllegalWrhList,
  exportOrderIllegalWrh,
} from "@/api/order/orderdeductmoney"
const tableCols =[
        {istrue:true,prop:'platform',label:'平台', width:'150',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
        {istrue:true,prop:'occurrenceTime',label:'日期', width:'150',sortable:'custom',formatter:(row)=>formatTime(row.occurrenceTime,"YYYY-MM-DD")},
        {istrue:true,prop:'shopCode',label:'店铺', width:'200',sortable:'custom',formatter:(row)=>row.shopName},
        {istrue:true,prop:'amountPaid',label:'求和项：扣款金额', width:'auto',sortable:'custom',formatter:(row)=>parseFloat(row.amountPaid.toFixed(6))}
     ];

const tableHandles=[       
        {label:"导入", handle:(that)=>that.startImport()},
        {label:"导出店铺统计", handle:(that)=>that.onExportShop()},       
        {label:"模板-原始订单", handle:(that)=>that.downloadTemplate()},
        {label:"模板-淘系每日明细", handle:(that)=>that.downloadTemplateTaoXi()},
        {label:"模板-拼多多每日明细", handle:(that)=>that.downloadTemplatePdd()}          
      ];     
export default {
  name: 'Roles',
  components: {cesTable, container, MyConfirmButton },
  props:{
    filter:{ }
  },
  data() {
    return {
      that:this,
      pickerOptions:{
        disabledDate(time){
          return time.getTime()>Date.now();
        }
      },
      filterImport:{
            platform:1,
            occurrenceTime:formatTime(dayjs().subtract(1,"day"), "YYYY-MM-DD")
            },
            pickerOptions:{
                disabledDate(time){
                return time.getTime()>Date.now();
                }
            },       
      platformList: [],
      list: [],
      summaryarry:{},
      tableHandles:tableHandles,
      pager:{OrderBy:"platform",IsAsc:true},
      tableCols:tableCols,
      dialogVisible: false,
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,     
      deleteLoading: false,
      uploadLoading:false,
      fileHasSubmit:false,
      formtitle:"新增",
      fileList:[],
      selids:[],//选择的id
    }
  },
  async mounted() {
    await this.setPlatform()
    await this.onSearch();  
  },
  methods: {
    //设置平台下拉
    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
    },
        //下载导入模板
    downloadTemplate(){
        window.open("../static/excel/原始订单明细导入模板.xlsx","_self");
    },
    //下载导入模板
    downloadTemplateTaoXi(){
        window.open("../static/excel/淘系每日明细导入模板.xlsx","_self");
    },
    //下载导入模板
    downloadTemplatePdd(){
        window.open("../static/excel/拼多多每日明细导入模板.xlsx","_self");
    },
    //开始导入
    startImport(){
      this.dialogVisible=true;
    },
    //取消导入
    cancelImport(){
      this.dialogVisible=false;
    },
    beforeRemove() {
      return true;
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
   async submitUpload() {
      if(!this.filterImport.occurrenceTime){
        this.$message({ message: "请先选择日期", type: "warning" });
        return false;
      }
      if(!this.filterImport.platform){
        this.$message({ message: "请先选择平台", type: "warning" });
        return false;
      }
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit=true;     
      this.$refs.upload.submit();
    },
   clearFiles(){
     this.$refs['upload'].clearFiles();
   },
   async uploadFile(item) {
      if(!this.fileHasSubmit){
        return false;
      }
      this.fileHasSubmit=false;
      var orderFile=null;
      var billFile=null;
      for (const key in this.fileList) {
        var name=this.fileList[key].name;
        if (name.indexOf("原始订单明细")>-1 && name.indexOf("每日明细")<0) {
          orderFile=this.fileList[key];         
        }
        if (name.indexOf("原始订单明细")<0 && name.indexOf("每日明细")>-1) {
          billFile=this.fileList[key];         
        }
      }
      if(!orderFile||!billFile){
        this.$message({ message: "请同时上传“原始订单明细”和“每日明细”文件", type: "warning" });
        this.uploadLoading=false;
        return false;
      }
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfileOrder", orderFile);
      form.append("upfileBill", billFile);
      form.append("platform", this.filterImport.platform);
      form.append("occurrenceTime", this.filterImport.occurrenceTime);
      this.uploadLoading=true;
      const res =await importOrderIllegal(form);
      this.fileHasSubmit=true;
      this.clearFiles();
      if (res.code==1)   
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      //else  
        //this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading=false;
      this.fileList=[];
    },
    uploadChange(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for(var i=0;i<fileList.length;i++){
          if(fileList[i].status=="success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.fileList = list;
      }
    },
    uploadRemove(file, fileList){
       this.uploadChange(file, fileList);
    },
    async onSearch() {
      if (!this.filter.timerange) {this.$message({message: "请选择日期",type: "warning",});return;}
      this.$refs.pager.setPage(1)
      await this.getlist();
    },
    //获取查询条件
    getCondition(){
      if (this.filter.timerange&&this.filter.timerange.length>1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      else {
        this.$message({message:"请先选择日期",type:"warning"});
        return false;
      }
      var pager = this.$refs.pager.getPager();
      var page  = this.pager;
      const params = {
        ...pager,
        ...page,
        ... this.filter
      }

      return params;
    },
    //分页查询
    async getlist() {
      var params=this.getCondition();
      if(params===false){
            return;
      }

      this.listLoading = true
      const res = await getOrderIllegalShopList(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry=res.data.summary;
      if(this.summaryarry)
        this.summaryarry.amountPaid_sum=parseFloat(this.summaryarry.amountPaid_sum.toFixed(6));
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
    //排序查询
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else{
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    },
    //导出
    async onExportShop(){
        var params=this.getCondition();
        if(params===false){
            return;
        }
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportOrderIllegalShop(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','订单违规扣款店铺统计_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },         
    selsChange: function(sels) {
      this.sels = sels;
    },
    selectchange:function(rows,row) {
      this.selids=[];console.log(rows)
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
    cellclick(row, column, cell, event){
       
    },
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary{
  margin-right: 7px;
}
</style>
