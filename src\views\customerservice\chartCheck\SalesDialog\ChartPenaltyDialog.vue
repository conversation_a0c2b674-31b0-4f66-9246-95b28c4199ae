<template>
  <el-dialog title="查看判罚" :visible.sync="isShow" width="50%" :before-close="closeDialog" v-if="isShow" v-dialogDrag>
    
    <!-- 判罚商铺信息 -->
    
    <div>
        <div v-if="platformName==='抖音'">
            <el-descriptions size="small" class="margin-top" title="" :column="3">
              <el-descriptions-item label="平台/店铺"> {{platformName}} — {{dataJson?.shopName}}</el-descriptions-item>
              <el-descriptions-item label="违规单号">{{dataJson?.violationOrderNo}}</el-descriptions-item>
              <el-descriptions-item label="违规时间">{{violationTime }} </el-descriptions-item>
            </el-descriptions>
        </div>
        <div  v-else style="">
            <el-descriptions size="small" class="margin-top" title="" :column="3">
              <el-descriptions-item label="平台/店铺"> {{platformName}} — {{dataJson?.shopName}}</el-descriptions-item>
              <el-descriptions-item label="违规单号">{{dataJson?.violationOrderNo}}</el-descriptions-item>
              <el-descriptions-item label="违规时间">{{violationTime }}  <el-button style="margin-left:20px;" type="primary" @click="showOrhide">{{this.isShowOrHide?"收起聊天记录":"展开聊天记录"}}</el-button></el-descriptions-item>
            </el-descriptions>
            <el-descriptions size="small" class="margin-top" title="" :column="3">
              <el-descriptions-item label="聊天账号"> {{dataJson?.chatAccount}}  </el-descriptions-item>
              <el-descriptions-item label="账号使用人/分组">{{dataJson?.userName}} — {{dataJson?.groupName}}</el-descriptions-item>
            </el-descriptions>
        </div>
        
    </div>



<!-- 聊天记录 -->
<!-- <PenaltyComponent   ref="chartRef"    :isShow="isShow" ></PenaltyComponent> -->
  <div  v-show="isShowOrHide"> 
    <div class="violations" v-if="platformName==='抖音'">
      <div> 违规判定： {{dataJson?.platformPunishMsg}} </div>
    </div> 
    <div v-else>
      <PenaltyComponent   ref="chartRef"    :isShow="isShow" ></PenaltyComponent>
    </div>
  </div>

<el-form  ref="formDataRef" style="margin-top: 20px" label-width="120px" >
  <div v-if="platformName==='抖音'" >
          <el-form-item label="判罚支付凭证:" prop="firstStatus" class="first custom-label">
                <span v-for="(image, index) in imgSplitList" :key="index" >
                    <el-image  v-if="image" :src="image" :preview-src-list="[image]" style="padding-right:10px"></el-image>
                    <span v-else>无</span>
                </span>
            </el-form-item>
          
            <el-form-item label="备注说明：" prop="firstExplain"  class="custom-label">
                  <div>{{ dataJson?.remark }}</div>
            </el-form-item>
  </div> 
  <div v-else>
<!-- 初审  -->
      <div style="border: 1px #eeeeee solid;padding:20px">
          <div style="display: flex">
              <el-form-item label="判罚初审:" prop="firstStatus" class="first custom-label">
                <div>{{dataJson?.refuseInitialAuditType }}</div>
              </el-form-item>
               <el-form-item label="初审人:" prop="firstStatus" class="custom-label" style="margin-left:80px" >
                <div>{{dataJson?.initialOperator}}</div>
              </el-form-item>
              <el-form-item label="说明：" prop="firstExplain" style="margin-left:80px" class="custom-label">
                <div>{{ dataJson?.remark }}</div>
              </el-form-item>
          </div>
          <el-form-item label="初审凭证:" prop="firstStatus" class="first custom-label">
              <span v-for="(image, index) in imgSplitList" :key="index" >
                  <el-image  v-if="image" :src="image" :preview-src-list="[image]" style="padding-right:10px"></el-image>
                  <span v-else>无</span>
              </span>
          </el-form-item>
      </div>
<!-- 初审  END-->

<!-- 复审  -->
      <div style="border: 1px #eeeeee solid;padding:20px;margin-top:20px;">
          <div style="display: flex">
              <el-form-item label="判罚复审:" prop="firstStatus" class="first custom-label">
                <div>{{dataJson?.refuseFinalAuditType  }}</div>
              </el-form-item>
               <el-form-item label="复审人:" prop="firstStatus" class="custom-label" style="margin-left:80px" >
                <div>{{dataJson?.finalOperator}}</div>
              </el-form-item>
              <el-form-item label="说明：" prop="firstExplain" style="margin-left:80px" class="custom-label">
                <div>{{ dataJson?.finalAuditRemark }}</div>
              </el-form-item>
          </div>
          <el-form-item label="复审凭证:" prop="firstStatus" class="first custom-label">
              <span v-for="(image, index) in imgFinalSplitList" :key="index" >
                  <el-image  v-if="image" :src="image" :preview-src-list="[image]" style="padding-right:10px"></el-image>
                  <span v-else>无</span>
              </span>
          </el-form-item>
      </div>
<!-- 复审  END-->
  </div>
</el-form>

    <template #footer>
      <div class="dialog-footer" style="display:flex;justify-content: flex-end;margin-right:35px">
                    <div style="position: relative;">
                        <el-button @click="btnChange('last')" type="primary" :disabled="isLastButtonDisabled">查看上一个</el-button>
                        <div style="position: absolute;right:-20px;top:-20px;cursor:pointer;" > 
                            <el-tooltip class="item" effect="dark" content="点击键盘的上箭头↑，可以快速查看上一个" placement="top"><i class="el-icon-question"></i></el-tooltip>
                        </div>
                    </div>
                    <div style="position: relative;margin-left:20px;">
                      <el-button @click="btnChange('next')" type="primary" :disabled="isNextButtonDisabled">查看下一个</el-button>
                          <div  style="position: absolute;right:-20px;top:-20px; cursor:pointer;" >
                              <el-tooltip class="item" effect="dark" content="点击键盘的下箭头↓，可以快速查看下一个" placement="top"> <i class="el-icon-question"></i></el-tooltip>
                         </div>
                    </div>
                </div>
    </template>
  </el-dialog> 
</template>
<script>
import { formatTime } from "@/utils";
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import ElImageViewerplus from '@/views/media/shooting/imageviewer.vue'//图片查看
import PenaltyComponent from "@/views/customerservice/chartCheck/SalesDialog/penaltyComponent.vue"


export default {
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
  },
  components:{ElImageViewer,ElImageViewerplus,PenaltyComponent},
  computed: {
    platformName()//平台初始化
     {
      let platformList = [
        { name: "拼多多", value: 2 },
        { name: "抖音", value: 6 },
        { name: "天猫", value: 1 },
        { name: "淘工厂", value: 8 },
        { name: "淘宝", value: 9 },
      ]
        console.log(this.dataJson);
      if (this.dataJson?.platform) {
        return platformList.filter(item => item.value == this.dataJson?.platform)[0].name
      } else {
        return ""
      }
    },
    violationTime() //日期转换
    {
      return this.dataJson?.violationTime?formatTime(this.dataJson?.violationTime , "YYYY-MM-DD"):""
    },
    imgSplitList() //初审图片分割
    {
      return  this.dataJson?.punishImgs?this.dataJson?.punishImgs.split(","):"";
    },
    imgFinalSplitList(){  //复审图片分割
      return  this.dataJson?.finalPunishImgs?this.dataJson?.finalPunishImgs.split(","):"";
    },
  },
    created(){
document.addEventListener('keydown', this.handleArrowUp);
  },
  data() {
    return {
      dataJson: {},
       isShowOrHide:true,
        tableData:[],
      isLastButtonDisabled:false,
      isNextButtonDisabled:false,
    };
  },
  watch: {
    isShow(newVal, oldVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.buttonDisabled();
          if(this.platform!=6){
            this.$refs.chartRef.dataJson = this.dataJson
           }
        });
      }
    },
  },
  methods: {
     handleArrowUp(event) {
        if(!this.isShow){ 
        return
      }
      if (event.key === 'ArrowUp' && !this.isLastButtonDisabled) {
        // 处理向上键被按下的逻辑
        this.btnChange('last');
      }
      if (event.key === 'ArrowDown' && !this.isNextButtonDisabled) {
        // 处理向上键被按下的逻辑
        this.btnChange('next');
      }
    },
    //关闭当前页面
    closeDialog() {
      this.$emit("closeDialog");
    },
     showOrhide(){
      if(this.isShowOrHide)
       this.isShowOrHide=false;
      else this.isShowOrHide=true
    },
    btnChange(last){  //查看上一个、查看下一个

      // const index = this.tableData.findIndex(i=>i.id==this.dataJson.id);
      const index= this.tableData.indexOf(this.dataJson);
      if(last=='last'){
            const info=this.tableData[index-1]
            this.dataJson=info;
            this.keyWord=info.id;
            this.platform=info.platform;
            if(this.dataJson.platform!=6)
            {
              this.$refs.chartRef.dataJson = info;
              this.$refs.chartRef.getChartList()
            }
      }else if(last=='next'){
             const info=this.tableData[index+1]
            this.dataJson=info;
            this.keyWord=info.id;
            this.platform=info.platform;
            if(this.dataJson.platform!=6)
            {
              this.$refs.chartRef.dataJson = info;
              this.$refs.chartRef.getChartList()
            }
         
      }
         this.pictures = [];
         this.chatUrls = []; 
         this.$refs.formDataRef.resetFields();
         this.buttonDisabled()//按钮是否禁用
    },
    async  buttonDisabled(){ //按钮是否禁用
        this.isLastButtonDisabled=false;
        this.isNextButtonDisabled=false;
        // const index = this.tableData.findIndex(i=>i.id==this.dataJson.id);

        const index= this.tableData.indexOf(this.dataJson);
        if(index==0){
          this.isLastButtonDisabled=true;
        }
        if(index==this.tableData.length-1){
          this.isNextButtonDisabled=true;
        }
    },
  },
};
</script>
<style lang="scss" scoped>
//顶部可点击div样式
::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell{
  padding: 15px;
}
::v-deep .el-descriptions__body .el-descriptions__table{
  background-color: rgb(242, 244, 245);
}

::v-deep .el-descriptions__body .el-descriptions-item__container {
  font-size: 14px;
}

.first ::v-deep .el-form-item__label:before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}
.violations{
  margin-top: 20px;
  margin-left: 20px;
  margin-right: 20px;
  height: 200px;
}
</style>
