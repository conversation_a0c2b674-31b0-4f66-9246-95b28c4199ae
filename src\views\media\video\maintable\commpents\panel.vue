<template>
    <div style="width: 100%; ">
        <div style="   background-color: #fff;  box-sizing: border-box; padding: 0.5%;  ">
            <el-progress :text-inside="true" :stroke-width="3" :percentage="70"></el-progress>
        </div>
        <div class="scsppdw" style="">
            <div class="sppdn">
                <div class="jqdpd divcenter" style="border: 1px solid #eee;"> <!-- 剪切的片段放这里 -->
                    <img width="100%" :src="cutimgpath == null ? imagedefault : cutimgpath" controls
                        @click="playVideo(cutvideopath)" />
                </div>
                <div>
                    <el-popconfirm v-if="vt!=2" :key="cuteid" title="确定删除此片段？" @confirm="ondelCuteInfo">
                        <el-button style="width: 100%" size="mini" slot="reference" type="danger"
                            :disabled="!checkPermission('vedioTask-cksp-scpd')"><i class="el-icon-delete"></i></el-button>
                    </el-popconfirm>

                    <el-button v-if="vt==2" style="width: 100%" size="mini" slot="reference" type="danger"  @click="ondelCuteInfoc"
                            :disabled="!checkPermission('vedioTask-cksp-scpd')  || !checkPermission('bjczscts')"><i class="el-icon-delete"></i></el-button>
                </div>
            </div>
            <div class="sppdn">
                <div class="scdpd divcenter" style="border: 1px solid #eee; "> <!-- 上传的片段放这里 -->
                    <img width="100%" :src="uploadimgpath == null ? imagedefault : uploadimgpath" controls
                        @click="playVideo(uploadvideopath)" />
                </div>
                <div style="width: 100%;">
                    <el-upload ref="upload" class="inline-block" style="width: 100%;" action="#" :auto-upload="true"
                        :multiple="true" :limit="limit" :show-file-list="false" :accept="accepttyes"
                        :http-request="UpSuccessload" :file-list="retdata" :islook="islook">
                        <div class="flexwidth">
                            <el-button size="mini" style="width: 100%; height: auto;" type="primary"
                                icon="el-icon-upload"></el-button>
                        </div>
                    </el-upload>
                </div>
            </div>
            <div class="sppdnbz">
                <div class="jqdpd">
                    <div style="  font-weight: bold;   font-size: 14px; color: #555;  line-height: 22px; "> 
                        {{ pdtitle + " | " + cuttitle }}</div>
                    <div :title="cutmark"> {{ cutmark }} </div>
                </div>
                <div>
                    <el-button slot="reference" style="width: 100%" size="mini" plain
                        @click="uploadshow = !uploadshow">已上传片素材：{{ retdata.length }}</el-button>
                </div>
            </div>
        </div>
        <div style="width: 100%; ">
            <el-progress :text-inside="true" :stroke-width="3" :percentage="0"></el-progress>
            <div v-show="uploadshow" class="transition-box">
                <div style="width: 100%; height: 80px;overflow-y: auto;">
                    <div class="wjnrq">
                        <div v-for="(i, index) in retdata" :key="i.uid" style="display: flex; flex-direction: row;">
                            <el-tooltip effect="dark" :content="i.fileName" placement="left">
                                <div @click="imgclick(i.url, retdata[index], index)"><span style="width: 220px;"
                                        class="wjdnr">{{ i.fileName }}</span></div>
                            </el-tooltip>
                            <div class="dnrxz" style="margin: 0 2px;" v-if="checkPermission('vedioTask-cksp-scpd')">
                                <el-link :underline="false" type="primary" @click="downfile(i)">
                                    <i :class="i.filestaus == 2 ? 'el-icon-download' : 'el-icon-warning'"></i>
                                </el-link>
                            </div>
                            <div class="dnrsc" style="margin: 0 2px;" v-if="checkPermission('vedioTask-cksp-xzsc')">
                                <el-link :underline="false" type="danger" @click="removefile(i)">
                                    <i class="el-icon-delete"></i>
                                </el-link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <el-image-viewer v-if="showGoodsImage" :initialIndex="imgindex" :url-list="imgList" :wrapperClosable="false"
            :on-close="closeFunc" style="z-index:9999;" />
        <!--视频播放-->
        <el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer" :append-to-body="true"
            v-dialogDrag>
            <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeVideoPlyer">关闭</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import draggablevue from 'vuedraggable'
import ElImageViewer from '@/views/media/shooting/imageviewer.vue'//图片查看
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
export default {
    props: {
        cuttitle: { type: String, default: "other" },//剪辑的第几段
        cutmark: { type: String, default: "other" },//剪辑的备注
        pdtitle: { type: String, default: "other" },//参考片段几
        cutvideopath: { type: String, default: "other" },//剪辑视频
        cutimgpath: { type: String, default: "other" },//剪辑第一针

        uploadvideopath: { type: String, default: "other" },//剪辑对应上传视频
        uploadimgpath: { type: String, default: "other" },//剪辑对应上传第一针

        uploadInfo: { type: Array, default: [] }, //上传的视频
        accepttyes: { type: String, default: "*" },//接收的上传格式
        uptype: { type: String, default: "other" },//列表类型

        limit: { type: Number, default: 100000 },//限制上传的数量
        delfunction: { type: Function, default: null },//删除操作 
        islook: { type: Boolean, default: false },//是否查看模块式
        ckVideoIndex: { type: Number, default: 100000 },//对应cuteid
        cuteid: { type: String, default: "0" },//对应cuteid
        vt: { type: String, default: "0" },//对应videoType
    },
    components: { draggablevue, ElImageViewer, videoplayer },
    data() {
        return {
            retdata: [],
            deldata: [],
            uploading: false,
            imgindex: 0,
            icontype: 'primary',
            imgList: [],
            showGoodsImage: false,
            IsChang: false,
            dialogVisible: false,
            videoplayerReload: false,
            imagedefault: require('@/assets/images/detault.jpeg'),
            uploadshow: false
        };
    },

    mounted() {
        this.retdata = this.uploadInfo;
        this.deldata = [];
    },

    methods: {

        //删除剪辑
        async ondelCuteInfo() {
            this.$emit('delCuteInfo', { cuteId: this.cuteid, ckIndex: this.ckVideoIndex,vt:this.vt })
        },
        async ondelCuteInfoc()
        {
            this.$confirm("该删除将删除上传视频及全部剪切片段视频，是否确认删除", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                this.$emit('delCuteInfo', { cuteId: this.cuteid, ckIndex: this.ckVideoIndex,vt:this.vt })
            });
        },
        dataUpdate() {
            this.IsChang = true;
        },
        //获取返回值
        getReturns() {
            var curdata = [];
            this.retdata.forEach(function (item) {
                //filestaus 0 新增，1移除，2原来的文件
                curdata.push({
                    fileName: item.fileName
                    , url: item.url
                    , OutComeId: item.outComeId
                    , uid: item.uid
                    , filestaus: item.filestaus
                    , file: item.file
                    , cuteid: item.cuteid
                    , cuttitle: item.cuttitle
                    , ckVideoIndex: item.ckVideoIndex
                })
            });

            this.deldata.forEach(function (item) {
                //filestaus 0 新增，1移除，2原来的文件
                curdata.push({
                    fileName: item.fileName
                    , url: item.url
                    , OutComeId: item.outComeId
                    , uid: item.uid
                    , cuteid: item.cuteid
                    , cuttitle: item.cuttitle
                    , filestaus: item.filestaus
                    , file: item.file
                    , ckVideoIndex: item.ckVideoIndex
                })
            });

            return { success: true, data: curdata };
        },
        //上传方法
        async UpSuccessload(file) {
            this.IsChang = true;
            this.uploading = true;
            this.retdata.push({
                fileName: file.file.name
                , url: ""
                , outComeId: 0
                , cuteid: this.cuteid
                , cuttitle: this.cuttitle
                , uid: file.file.uid
                , filestaus: 0
                , file: file.file
                , ckVideoIndex: this.ckVideoIndex
            });
            this.uploading = false;
        },
        retdatafuc(val) {
        },
        uploadshowfuc(val) {
            this.uploadshowcd = val;
        },
        handleChange(val) {
        },
        // 关闭图片
        async closeFunc() {
            this.showGoodsImage = false;
        },
        async closeVideoPlyer() {
            this.dialogVisible = false;
            this.videoplayerReload = false;
        },
        playVideo(videoUrl) {
            this.videoplayerReload = false;
            this.videoplayerReload = true;
            this.dialogVisible = true;
            this.videoUrl = videoUrl;
        },
        getCaption(obj, state) {
            var index = obj.lastIndexOf("\.");
            if (state == 0) {
                obj = obj.substring(0, index);
            } else {
                obj = obj.substring(index + 1, obj.length);
            }
            return obj;
        },
        imgclick(e, data, index) {
            let ret = this.getCaption(data.fileName, 1);
            let img = ['jpg', 'jpeg', 'bpm', 'png', 'tiff', 'raw'];
            let video = ['mp4', 'avi', 'rmvb', 'flv', 'MOV'];
            console.log("打印类型e,data,index", [e, data, index])
            this.uptype = img.indexOf(ret) != -1 ? 'imgtype' : video.indexOf(ret) != -1 ? 'vediotype' : 'else';

            console.log("图片还是视频", this.uptype)
            //判断当前是 图片，还是视频，还是不可预览文件 
            if (this.uptype == "imgtype") {
                this.imgList = [];
                for (let num in this.retdata) {
                    if (this.retdata[num].filestaus == 2)
                        this.imgList.push(this.retdata[num].url);
                }
                this.imgindex = index;
                this.showGoodsImage = true;
            }
            else if (this.uptype == "rartype") {
                //不可预览
            }
            else if (this.uptype == "psdtype") {
                //不可预览
            }
            else if (this.uptype == "vediotype") {
                //视频预览
                this.playVideo(data.url);
            }
            return;
        },
        //移除文件
        async removefile(file) {
            this.IsChang = true;
            let that = this;
            //发出父级页面移除请求
            if (file?.outComeId > 0) {
                this.$confirm('此操作将会彻底删除该文件，是否执行')
                    .then(async () => {
                        var ret = await that.delfunction(file);
                        if (ret) {
                            for (let num in that.retdata) {
                                if (that.retdata[num].uid == file.uid) {
                                    that.retdata.splice(num, 1)
                                }
                            }
                        }
                    })
                    .catch(_ => {
                    });
            } else {
                for (let num in this.retdata) {
                    if (this.retdata[num].uid == file.uid) {
                        //原上传文件，移除到删除列表
                        if (this.retdata[num].outComeId > 0) {
                            //打算删除标记
                            this.retdata[num].filestaus = 1;
                            this.deldata.push(this.retdata[num]);
                        }
                        this.retdata.splice(num, 1)
                    }
                }
            }

        },
        //下载文件
        async downfile(file) {
            if (file.filestaus != 2)
                return;
            var xhr = new XMLHttpRequest();
            xhr.open('GET', file.url, true);
            xhr.responseType = 'arraybuffer'; // 返回类型blob
            xhr.onload = function () {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    let blob = this.response;
                    console.log(blob);
                    // 转换一个blob链接
                    // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，
                    // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定
                    let downLoadUrl = window.URL.createObjectURL(new Blob([blob], { type: 'video/mp4' }));
                    // 视频的type是video/mp4，图片是image/jpeg
                    // 01.创建a标签
                    let a = document.createElement('a');
                    // 02.给a标签的属性download设定名称
                    a.download = file.fileName;
                    // 03.设置下载的文件名
                    a.href = downLoadUrl;
                    // 04.对a标签做一个隐藏处理
                    a.style.display = 'none';
                    // 05.向文档中添加a标签
                    document.body.appendChild(a);
                    // 06.启动点击事件
                    a.click();
                    // 07.下载完毕删除此标签
                    a.remove();
                };
            };
            xhr.send();
        },

    },
};
</script>

<style lang="scss" scoped>
/* 短视频参考 star */
.ckbt,
.ckck {
    margin: 0 auto;
    width: 100%;
    min-width: 1250px;

}

.ckbt {
    height: 55px;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 20px 275px;
    font-size: 18px;
    color: #666;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
}

.ckck {
    min-width: 1700px;
    /* height: 1000px; */
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 20px 300px;
    padding-bottom: 60px;
}

.cksp {
    width: 99%;
    background-color: antiquewhite;
    box-sizing: border-box;
    margin: 10px auto;
    /* font-weight: bold; */
    text-align: center;
}

.ckck .wzsp {
    width: 20%;
    height: 360px;
    background-color: rgb(255, 255, 255);
    display: inline-block;
    box-sizing: border-box;
    padding: 0 0.5%;
    margin-bottom: 10px;
}

.wzsp .wzpd {
    width: 100%;
    height: 80%;
    background-color: #f5f5f5;
    box-sizing: border-box;
    border: 2px solid #dcdfe6;
    text-align: center;
}

.wzsp .wzpd:hover {
    width: 100%;
    height: 80%;
    background-color: #f5f5f5;
    box-sizing: border-box;
    border: 2px solid #409eff;
    text-align: center;
}

.wzsp .wzpdbz {
    width: 100%;
    height: 20%;
    font-size: 14px;
    color: #fff;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.6);
    box-sizing: border-box;
    padding: 11px 15px;

    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;

}

.scsppd {
    width: 24%;
    /* height: 180px; */
    display: inline-block;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 1%;
    margin: 5px 0.5%;
    border: 1px solid #dcdfe6;
}

.ckck .scsppdw {
    width: 100%;
    // background-color: rgb(255, 224, 224);
    display: inline-block;
    margin-right: 1%;
}

.scsppdw .sppdn {
    width: 23%;
    background-color: #ffffff;
    display: inline-block;
    box-sizing: border-box;
    padding: 0.5%;
}

.scsppdw .sppdnbz {
    width: 54%;
    background-color: #ffffff;
    display: inline-block;
    box-sizing: border-box;
    padding: 0.5%;
}

.wjdnr {
    overflow: hidden;
    -webkit-line-clamp: 1;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}

.scdpd,
.jqdpd {
    width: 100%;
    height: 80px;
    /* background-color: #077bff; */
    box-sizing: border-box;
    /* border: 1px solid #dcdfe6; */
    margin: 5px 0;
    font-size: 14px;
    color: #999;
    overflow: hidden;
    text-overflow: ellipsis;

    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
}

/* 短视频参考 end */

/* 短视频剪切 star */

.jqsp {
    background-color: #fff;
}

.jqsp .jqspbt {
    height: 55px;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 20px 35px;
    font-size: 16px;
    color: #666;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
}

.jqsp .jqwzpd {
    width: 18%;
    height: 550px;
    background-color: #ffffff;
    display: inline-block;
    box-sizing: border-box;
    padding: 10px 35px;
    overflow: hidden;
}

.jqsp .jqwzpdnr {
    width: 90%;
    height: 340px;
    background-color: #fff;
    margin: 0 auto;
    align-items: center;
    text-align: center;
    box-sizing: border-box;
    border: 1px solid #dcdfe6;
}

.jqsp .jqwzpdbz {
    width: 90%;
    height: 70px;
    background-color: rgba(0, 0, 0, 0.5);
    text-align: center;
    font-size: 14px;
    color: #fff;
    margin: 0px auto 10px auto;
    box-sizing: border-box;
    padding: 10px 15px;


    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;


}

.jqsp .jqwzpdan {
    width: 90%;
    max-height: 100px;
    background-color: rgb(255, 255, 255);
    text-align: center;
    font-size: 14px;
    color: #fff;
    margin: 0px auto;
    box-sizing: border-box;
    padding: 15px 20px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.jqsp .jqxzsp {
    width: 10%;
    height: 550px;
    background-color: rgb(255, 255, 255);
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.jqsp .jqyjdl {
    width: 72%;
    height: 550px;
    background-color: rgb(255, 255, 255);
    display: inline-block;
    box-sizing: border-box;
    padding: 20px 35px;
    overflow: auto;
}

.jqsp .jqsjx {
    width: 100%;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 10px 15px;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
}

.jqsp .qxtj {
    height: 80px;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 20px 60px;
}

.divcenter {
    width: 100%;
    background-color: rgb(255, 255, 255);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.flexwidth {
    width: 3.1vw;
    display: flex;
}

::v-deep .el-carousel__container {
    position: relative;
    height: 100%;
}

::-webkit-scrollbar {
    width: 5px;
    height: 10px;
}
</style>