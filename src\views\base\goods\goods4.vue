<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right" label-width="90px">
                <el-form-item label="款式编码:">
                    <el-input v-model.trim="filter.styleCode" placeholder="款式编码" @change="onSearch" style="width:200px" />
                </el-form-item>
                <el-form-item label="商品编码:">
                    <!-- <el-input v-model="filter.goodsCode" placeholder="商品编码" @change="onSearch" /> -->
                    <inputYunhan v-model.trim="filter.goodsCode" :inputt.sync="filter.goodsCode" placeholder="商品编码" :clearable="true" @callback="callback" title="商品编码"></inputYunhan>
                </el-form-item>
                <el-form-item label="商品名称:">
                    <el-input v-model.trim="filter.goodsName" placeholder="商品名称" @change="onSearch" style="width:200px" />
                </el-form-item>
                <!-- <el-form-item label="运营组:">
                    <el-select style="width:130px;" v-model="filter.groupId" placeholder="请选择" :clearable="true" :collapse-tags="true" @change="onSearch">
                        <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="采购组:">
                    <el-select style="width:130px;" v-model="filter.brandId" placeholder="请选择" :clearable="true" :collapse-tags="true" @change="onSearch">
                        <el-option v-for="item in brandList" :key="item.key" :label="item.value" :value="item.key">
                        </el-option>
                    </el-select>
                </el-form-item> -->

                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>

            <el-form :model="selectForm" ref="selectForm" label-position="right" label-width="90px">
                <el-form-item label="已选商品:">
                    <div style="max-height: 50px; overflow: auto;">
                        <span v-for="(goodsGood, index) in selGoodList" :key="index" class="spangoods">
                            {{goodsGood}}
                            <a href="javascript:void(0)" class="spangoods_a" @click="onRemoveSelItem(index)">x</a>
                        </span>
                    </div>
                </el-form-item>
            </el-form>
        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' @select='selectchange' :isSelection.sync="ischoice" :tableHandles='tableHandles' :isSelectColumn="!ischoice" :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="margin: 0;" type="primary" v-if="!ischoice" @click="startImport">导入</el-button>
                </el-button-group>
            </template>
        </ces-table>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </my-container>
</template>

<script>
    import { formatTime } from "@/utils";
    import MyContainer from '@/components/my-container'
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import { getGroupKeyValue } from '@/api/operatemanage/base/product';
    import { getAllProBrand } from '@/api/inventory/warehouse';
    import inputYunhan from '@/components/Comm/inputYunhan';
    import {
        //分页查询店铺商品资料
        getList, getListSql,
        //导入
        importData,
    } from "@/api/inventory/basicgoods"
    const tableCols = [
        { istrue: true, prop: 'goodsCode', label: '商品编码', width: '140', sortable: 'custom', },
        { istrue: true, prop: 'labels', label: '商品标签', width: '140', sortable: 'custom' },
        { istrue: true, prop: 'goodsName', label: '商品名称',  },
        { istrue: true, prop: 'costPrice', label: '成本价', width: '80', sortable: 'custom',display:false },
        { istrue: true, prop: 'pictureBig', label: '图片', width: '60', type: 'imageGoodsCode', goods: { code: 'goodsCode', name: 'goodsName' } },
        { istrue: true, prop: 'styleCode', label: '款式编码', width: '130', sortable: 'custom', },
        { istrue: true, prop: 'shortName', label: '简称', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'color', label: '颜色', width: '60', sortable: 'custom' },
        { istrue: true, prop: 'itemType', label: '商品属性', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'endNum', label: '库存数量', width: '100', sortable: 'custom' },
    ];
    const tableHandles1 = [
        // {label:"导入", handle:(that)=>that.startImport()},
        // {label:"确定选择", handle:(that)=>that.onselected()},
    ];
    export default {
        name: 'goods',
        components: { cesTable, MyContainer, MyConfirmButton, inputYunhan },
        props: {
            ischoice: { type: Boolean, default: false },
        },
        data() {
            return {
                that: this,
                filter: {
                    styleCode: null,
                    goodsCode: null,
                    goodsName: null,
                    groupId: null,
                    brandId: null
                },
                selectForm: { selGoodCodes: '' },
                selGoodList: [],
                list: [],
                summaryarry: {},
                pager: { OrderBy: "goodsCode", IsAsc: true },
                tableCols: tableCols,
                tableHandles: tableHandles1,
                platformList: [],
                shopList: [],
                groupList: [],
                brandList: [],
                dialogVisible: false,
                total: 0,
                sels: [],
                listLoading: false,
                pageLoading: false,
                fileList: [],
                yesnoList: [
                    { value: true, label: "是" },
                    { value: false, label: "否" }
                ],
                selrows: [],
            }
        },
        async mounted() {
            await this.setGroupSelect();
            await this.setBandSelect();
            //await this.getlist();
        },
        methods: {
            async callback(val) {
                this.filter.goodsCode = val;
                await this.onSearch();
            },
            async setGroupSelect() {
                const res = await getGroupKeyValue({});
                this.groupList = res.data;
            },
            async setBandSelect() {
                var res = await getAllProBrand();
                if (!res?.success) return;
                this.brandList = res.data;
            },
            //获取查询条件
            getCondition() {
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = { ...pager, ...page, ... this.filter }
                return params;
            },
            //查询第一页
            async onSearch() {
                this.$refs.pager.setPage(1);
                await this.getlist();
                this.list.forEach(f => {
                    let obj = this.selGoodList.findIndex((v) => (v == f.goodsCode));
                    if (obj !== -1) {
                        this.$refs.table.toggleRowSelection(f);
                    }
                });
            },
            onShowChoice() {
                // this.ischoice=true;
            },
            //分页查询
            async getlist() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true
                var res = await getListSql(params);
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summary;
                data.forEach(d => {
                    d._loading = false;
                    d.id = d.goodsCode;
                })
                this.list = data
            },

            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderBy = column.prop;
                    this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selsChange: function (sels) {
                this.sels = sels
            },
            selectchange: function (rows, row) {
                //先把当前也的数据全部移除
                this.list.forEach(f => {
                    let index = this.selGoodList.findIndex((v) => (v === f.goodsCode));
                    if (index !== -1) {
                        this.selGoodList.splice(index, 1);
                        this.selrows.splice(index, 1);
                    }
                });
                //把选中的添加 
                rows.forEach(f => {
                    let index = this.selGoodList.findIndex((v) => (v === f.goodsCode));
                    if (index === -1) {
                        this.selGoodList.push(f.goodsCode);
                        this.selrows.push(f);
                    }
                });
            },
            onRemoveSelItem(index) {
                this.selGoodList.splice(index, 1);
                this.selrows.splice(index, 1);
                this.list.forEach(f => {
                    let obj = this.selGoodList.findIndex((v) => (v == f.goodsCode));
                    if (obj === -1) {
                        this.$refs.table.toggleRowSelection(f, false);
                    }
                });
            },
            async removeSelData() {
                this.selGoodList = [];
                this.selrows = [];
                this.$refs.table.clearSelection()
            },
            async getchoicelist() {
                if (!this.selrows || this.selrows.length == 0)
                    this.$message({ message: "你还没有选择", type: "warning", });
                return this.selrows
            },
            async getchoicelistOnly() {
                if (!this.selrows || this.selrows.length == 0)
                    this.$message({ message: "请选择一条数据，", type: "warning", });
                if (!this.selrows || this.selrows.length > 1)
                    this.$message({ message: "只能选择一条数据", type: "warning", });
                return this.selrows
            },
            startImport() {
                this.dialogVisible = true;
            },
            cancelImport() {
                this.dialogVisible = false;
            },
            beforeRemove() {
                return false;
            },
            //上传成功
            uploadSuccess(response, file, fileList) {
                if (response.code == 200) {
                } else {
                    fileList.splice(fileList.indexOf(file), 1);
                }
            },
            submitUpload() {
                this.$refs.upload.submit();
            },
            async uploadFile(item) {
                const form = new FormData();
                form.append("token", this.token);
                form.append("upfile", item.file);
                const res = await importData(form);
                if (res?.success) {
                    this.$message({ message: '上传成功,正在导入中...', type: "success", });
                }
                else {
                    this.$message({ message: res.message, type: "warning", });
                }
            },
        }
    }
</script>
<style scoped>
    .spangoods {
        display: block;
        float: left;
        margin-left: 10px;
    }
    .spangoods_a {
        border-radius: 5px;
        font-family: "微软雅黑";
        width: 8px;
        height: 8px;
        border: 1px solid #ccc;
        display: block;
        float: right;
        line-height: 4px;
        text-align: center;
        color: #ccc;
        font-size: 12px;
        margin-left: 3px;
    }
    ::v-deep .el-link.el-link--primary {
        margin-right: 7px;
    }
</style>
