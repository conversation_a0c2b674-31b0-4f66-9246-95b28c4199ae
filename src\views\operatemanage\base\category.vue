<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="平台:">
                    <el-select v-model="filter.platform" placeholder="请选择平台" style="width: 100%">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item  v-show="filter.platform==2"  label="店铺:">
                    <el-select filterable v-model="filter.shopCode" placeholder="请选择店铺" clearable style="width: 120px">
                        <el-option v-for="item in shopList" :key="item.id" :label="item.shopName"
                            :value="item.shopCode" />
                    </el-select>
                </el-form-item>

                <!-- <el-form-item label="是否显示:">
          <el-select v-model="filter.isOpen" placeholder="请选择" style="width: 100%">
            <el-option label="所有" value></el-option>
            <el-option label="已开启" value="1"></el-option>
            <el-option label="未开启" value="0"></el-option>
          </el-select>
        </el-form-item> -->
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onAdd">新增</el-button>
                    <el-button type="primary" @click="onshowPlatform">平台服务费</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!-- <ces-table ref="table" :that='that' :isIndex='false' :hasexpand='true' :tableData='listTree' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"
                                :treeprops="{ children: 'children', hasChildren: 'hasChildren' }" :showsummary="false"/> -->
        <template class="block">
            <div style="width: 80%; display: flex; flex-direction: row;">
                <div class="table-column" style="width: 35%; margin-left: 20px;"> 类目 </div>
                <div class="table-column" style="width: 10%; margin-left: 10px;"> 平台 </div>
                <div class="table-column" style="width: 20%; margin-left: 7px;"> 服务费率</div>
                <div class="table-column" style="width: 20%; margin-left: 25px;">操作</div>
            </div>

            <div style="height: 80vh; overflow-y: auto;">
                <el-tree :data="listTree"  node-key="id" :expand-on-click-node="false">
                    <span class="custom-tree-node" style=" display:flex;border:2px; " slot-scope="{ node, data }">
                        <span>{{node.label}}
                        </span>
                        <span style="">
                            <div class="table-row" :span="24" style="display:flex;border:2px;">
                                <div class="table-column" style="position: absolute;left:30%">
                                    {{platformlist.filter(f=>f.value==data.platform)[0].label}}
                                </div>
                                <div class="table-column" style="position: absolute;left:40%">
                                    {{ data.serviceFeeRate}}
                                </div>
                                <div class="table-column" @click="showTree" style="position: absolute;left:50%">
                                    <el-button @click.native="onAddChild(data)">新增子集</el-button>
                                    <el-button @click.native="onEdit(data)">编辑</el-button>
                                    <el-button @click.native="onDelete(data)">删除</el-button>
                                </div>
                            </div>
                        </span>
                    </span>
                </el-tree>
            </div>
            
        </template>
        <div>
        </div>
        <!-- <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template> -->

        <el-dialog title="新增" :visible.sync="addFormVisible" :close-on-click-modal="false" @close="onCloseAddForm">
            <el-form ref="addForm" :model="addForm" label-width="110px" :rules="addFormRules">
                <el-form-item prop="parentIds" label="上级类目">
                    <el-cascader :key="addFormKey" v-model="addForm.parentIds" placeholder="请选择，支持搜索功能" :options="modules" :props="{ checkStrictly: true, value: 'id' }" filterable style="width:100%;" />
                </el-form-item>
                <el-form-item label="平台">
                    <el-select v-model="addForm.platform" placeholder="请选择平台" style="width: 100%">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="标题" prop="categoryName">
                    <el-input v-model="addForm.categoryName" placeholder="标题" />
                </el-form-item>
                <el-form-item label="服务费率(%)" prop="serviceFeeRate">
                    <el-input-number v-model="addForm.serviceFeeRate" :precision="2" :step="0.1" :max="10" placeholder="服务费率(%)"></el-input-number>
                </el-form-item>
                <!-- <el-form-item label="是否显示:">
          <el-select v-model="addForm.enabled" placeholder="请选择" style="width: 100%">
            <el-option label="是" value="true"></el-option>
            <el-option label="否" value="false"></el-option>
          </el-select>
        </el-form-item> -->
                <el-form-item label="排序" prop="sort">
                    <el-input-number v-model="addForm.sort" :step="2"></el-input-number>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click.native="addFormVisible = false">取消</el-button>
                    <my-confirm-button type="submit" :validate="addFormValidate" :loading="addLoading" @click="onAddSubmit" />
                </div>
            </template>
        </el-dialog>

        <el-dialog title="编辑" :visible.sync="editFormVisible" :close-on-click-modal="false" @close="onCloseEditForm">
            <el-form ref="editForm" :model="editForm" :rules="editFormRules" label-width="110px">
                <!-- <el-form-item prop="parentIds" label="上级类目">
          <el-cascader :key="addFormKey" v-model="editForm.parentIds" placeholder="请选择，支持搜索功能"
                       :options="modules" :props="{ checkStrictly: true, value: 'id' }" filterable style="width:100%;"/>
        </el-form-item> -->
                <el-form-item label="平台">
                    <el-select v-model="editForm.platform" placeholder="请选择平台" style="width: 100%">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="标题" prop="categoryName">
                    <el-input v-model="editForm.categoryName" placeholder="标题" />
                </el-form-item>
                <el-form-item label="服务费率(%)" prop="serviceFeeRate">
                    <el-row>
                        <el-col :xs="24" :sm="10" :md="10" :lg="10" :xl="10">
                            <el-input-number v-model="editForm.serviceFeeRate" :precision="2" :step="0.1" :max="10" placeholder="服务费率(%)"></el-input-number>
                        </el-col>
                        <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
                            <el-alert type="warning" show-icon :closable="false" title="温馨提示:更新服务费率会同时更新自类目服务费率"></el-alert>
                        </el-col>
                    </el-row>
                </el-form-item>
                <!-- <el-form-item label="是否显示:">
          <el-select v-model="editForm.enabled" placeholder="请选择" style="width: 100%">
            <el-option label="是" value="true"></el-option>
            <el-option label="否" value="false"></el-option>
          </el-select>
        </el-form-item> -->
                <el-form-item label="排序" prop="sort">
                    <el-input-number v-model="editForm.sort" :step="2"></el-input-number>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click.native="editFormVisible = false">取消</el-button>
                    <my-confirm-button type="submit" :validate="editFormValidate" :loading="editLoading" @click="onEditSubmit" />
                </div>
            </template>
        </el-dialog>

        <el-dialog title="导入产品类目" :visible.sync="importdialogVisible" width="30%">
            <span>
                <el-row>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-select v-model="importFilte.platform" placeholder="请选择平台" style="width: 100%">
                            <el-option label="请选择" value=""></el-option>
                            <el-option label="淘系" value="1"></el-option>
                            <el-option label="拼多多" value="2"></el-option>
                        </el-select>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx" :http-request="uploadFile">
                            <template #trigger>
                                <el-button size="small" type="primary">选取产品类目文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitUpload">上传</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="importdialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="平台服务费" :visible.sync="platformserviceVisible" width="50%" v-dialogDrag>
            <div>
                <platformservice v-if="platformserviceVisible" ref="platformservice" style="height:600px;"></platformservice>
            </div>
        </el-dialog>
    </container>
</template>

<script>
    import { treeToList, listToTree, getTreeParents } from '@/utils'
    import cesTable from "@/components/Table/table.vue";
    import container from '@/components/my-container'
    import MyConfirmButton from '@/components/my-confirm-button'
    import platformservice from '@/views/operatemanage/base/platformservice'
    import { formatPlatform, formatYesornoBool, platformlist } from "@/utils/tools";
    import { getbyid, deletebyid, deletebyids, addoredit, getList, importProductCategory } from '@/api/operatemanage/base/category'
    import { getList as getshopList } from '@/api/operatemanage/base/shop'

    const tableCols = [
        { istrue: true, prop: 'categoryName', label: '标题	', width: '200' },
        { istrue: true, prop: 'platform', label: '平台', width: '150', sortable: 'custom', formatter: (row) => formatPlatform(row.platform) },
        { istrue: true, prop: 'serviceFeeRate', label: '服务费率(%)', width: '120', formatter: (row) => row.serviceFeeRate || ' ' },
        //{istrue:true,prop:'enabled',label:'是否显示', width:'100',formatter:(row)=>formatYesornoBool(row.enabled)},
        { istrue: true, prop: 'sort', label: '排序', width: '150', sortable: 'custom' },
        { istrue: true, type: 'button', btnList: [{ label: "新增子级", handle: (that, row) => that.onAddChild(row) }, { label: "编辑", handle: (that, row) => that.onEdit(row) }, { label: "删除", handle: (that, row) => that.onDelete(row) }] },
    ];
    const tableHandles1 = [
        { label: "新增", handle: (that) => that.onAdd() },
        { label: "平台服务费", handle: (that) => that.onshowPlatform() },
        //{label:"下载导入类目模板", handle:(that)=>that.onDownMuban()},{label:"导入产品类目", handle:(that)=>that.onImportCategory()}
    ];
    export default {
        name: 'Api',
        components: { cesTable, platformservice, container, MyConfirmButton },
        data () {
            return {
                that: this,
                shopList:[],
                filter: { platform: 1,shopCode:null },
                platformlist: platformlist,
                listTree: [],
                listLoading: false,
                sels: [], // 列表选中列
                tableCols: tableCols,
                tableHandles: tableHandles1,
                addDialogFormVisible: false,
                editFormVisible: false, // 编辑界面是否显示
                editLoading: false,
                editFormRules: {
                    parentIds: [{ required: true, message: '请选择上级类目', trigger: 'change' }],
                    categoryName: [{ required: true, message: '请输入标题', trigger: 'blur' }],
                    sort: [{ required: true, message: '请输入排序', trigger: 'blur' }]
                },
                // 编辑界面数据
                editForm: { id: '0', parentIds: [], label: '', categoryName: '', platform: 1, serviceFeeRate: undefined, enabled: 'true', sort: 0 },
                editFormKey: 1,
                addFormVisible: false, // 新增界面是否显示
                addLoading: false,
                addFormRules: {
                    parentIds: [{ required: true, message: '请选择上级类目', trigger: 'change' }],
                    categoryName: [{ required: true, message: '请输入标题', trigger: 'blur' }],
                    sort: [{ required: true, message: '请输入排序', trigger: 'blur' }]
                },
                // 新增界面数据
                addForm: { parentIds: ['0'], label: '', categoryName: '', platform: 1, serviceFeeRate: undefined, enabled: 'true', sort: 0 },
                addFormKey: 1,
                modules: [],
                syncManagerLoading: false,
                syncFinancialLoading: false,
                syncOrderLoading: false,
                deleteLoading: false,
                total: 0,
                listLoading: false,
                pager: { OrderBy: "id", IsAsc: false },
                pageLoading: false,
                platformserviceVisible: false,
                importdialogVisible: false,
                importFilte: { platform: "" }
            }
        },
        mounted () {
            this.loadShop();
            this.onSearch();
        },
        methods: {
            async loadShop(){
                let res1 = await getshopList({ platform: 2, CurrentPage: 1, PageSize: 100000 });
                this.shopList = res1.data.list;
            },
            onSearch () {
                this.getlist()
            },
            async getlist () {
                this.listLoading = true
                const res = await getList(this.filter)
                this.listLoading = false
                if (!res?.success) return
                const list = [];
                res.data.forEach(f => {
                    f.label = f.categoryName;
                    list.push(f)
                })
                this.modules = listToTree(_.cloneDeep(list), { id: '0', parentId: '0', label: '根节点' })
                list.forEach(l => {
                    l._loading = false
                })
                const tree = listToTree(list)
                this.sels = []
                this.listTree = tree

                console.log(tree, "mytree");
            },
            // 显示编辑界面
            async onEdit (row) {
                const loading = this.$loading()
                const res = await getbyid(row.id)
                loading.close()
                if (res && res.code) {
                    //const parents = getTreeParents(this.listTree, row.id)
                    // console.log(parents)
                    // const parentIds = parents.map(p => p.id)
                    // parentIds.unshift('0')
                    const data = res.data
                    // data.parentIds = parentIds
                    this.editForm.id = data.id
                    this.editForm.parentId = data.parentId
                    this.editForm.platform = data.platform
                    this.editForm.serviceFeeRate = data.serviceFeeRate
                    this.editForm.enabled = '' + data.enabled + ''
                    this.editForm.categoryName = data.categoryName
                    this.editForm.sort = data.sort
                    this.editForm.parentIds = data.parentIds
                    this.editFormVisible = true
                    ++this.editFormKey
                }
            },
            onCloseEditForm () {
                this.$refs.editForm.resetFields()
                ++this.editFormKey
            },
            // 显示新增界面
            onAdd () {
                this.addFormVisible = true
            },
            onAddChild (row) {
                this.addFormVisible = true
                //console.log(this.listTree)
                const parents = getTreeParents(this.listTree, row.id)
                console.log(parents)
                const parentIds = this.getparentids(parents);
                parentIds.unshift('0')
                if (parentIds.indexOf(row.id) < 0)
                    parentIds.push(row.id)
                this.addForm.id = '0',
                    this.addForm.parentId = row.parentId
                this.addForm.platform = row.platform
                this.addForm.isshow = '' + row.isshow + ''
                this.addForm.categoryName = ''
                this.addForm.sort = '0'
                this.addForm.parentIds = parentIds
            },
            getparentids (parents) {
                const parentIds = [];
                if (parents && parents.length > 0) {
                    parentIds.push(parents[0].id)
                    if (parents[0].children) {
                        parentIds.push.apply(parentIds, this.getparentids(parents[0].children));
                    }
                }
                return parentIds;
            },
            onCloseAddForm () {
                this.$refs.addForm.resetFields()
                ++this.addFormKey
            },
            // 编辑
            editFormValidate: function () {
                let isValid = false
                this.$refs.editForm.validate(valid => {
                    isValid = valid
                })
                return isValid
            },
            async onEditSubmit () {
                this.editLoading = true
                const para = _.cloneDeep(this.editForm)
                const res = await addoredit(para)
                this.editLoading = false
                if (!res?.success) {
                    return
                }
                this.$message({
                    message: this.$t('admin.updateOk'),
                    type: 'success'
                })
                this.$refs['editForm'].resetFields()
                this.editFormVisible = false
                this.getlist()
            },
            // 新增
            addFormValidate: function () {
                let isValid = false
                this.$refs.addForm.validate(valid => {
                    isValid = valid
                })
                return isValid
            },
            async onAddSubmit () {
                this.addLoading = true
                const para = _.cloneDeep(this.addForm)
                para.parentId = para.parentIds.pop()
                const res = await addoredit(para)
                this.addLoading = false

                if (!res?.success) {
                    return
                }
                this.$message({
                    message: this.$t('admin.addOk'),
                    type: 'success'
                })
                this.$refs['addForm'].resetFields()
                this.addFormVisible = false
                this.getlist()
            },
            showTree () {

                console.log(this.listTree);

            },
            // 删除
            async onDelete (row) {
                row._loading = true
                this.$confirm('确定删除吗, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                    .then(async () => {
                        const para = { id: row.id }
                        const res = await deletebyid(para)
                        row._loading = false
                        if (!res?.success) return
                        this.$message({ type: 'success', message: '删除成功!' });
                        this.getlist()
                    }).catch(() => {
                        this.$message({ type: 'info', message: '已取消' });
                    });
            },
            onSelectAll: function (selection) {
                const selections = treeToList(selection)
                const rows = treeToList(this.listTree)
                const checked = selections.length === rows.length
                rows.forEach(row => {
                    this.$refs.multipleTable.toggleRowSelection(row, checked)
                })

                this.sels = this.$refs.multipleTable.selection
            },
            onSelect: function (selection, row) {
                const checked = selection.some(s => s.id === row.id)
                if (row.children && row.children.length > 0) {
                    const rows = treeToList(row.children)
                    rows.forEach(row => {
                        this.$refs.multipleTable.toggleRowSelection(row, checked)
                    })
                }

                this.sels = this.$refs.multipleTable.selection
            },
            async onDownMuban () {
                let url = "../static/excel/operation/产品类目导入模板.xlsx";
                let link = document.createElement("a");
                let fileName = "产品类目导入模板" + ".xlsx";
                document.body.appendChild(link);
                link.href = url;
                link.dowmload = fileName;
                link.click();
                link.remove();
            },
            async onImportCategory () { this.importdialogVisible = true },
            async onSubmitUpload () {
                if (!this.importFilte.platform) {
                    this.$message({ message: "请选择平台！", type: "warning" });
                    return;
                }
                this.$refs.upload.submit()
            },
            async uploadSuccess (response, file, fileList) {
                console.log(response)
                if (response.code == 200) {
                } else {
                    fileList.splice(fileList.indexOf(file), 1);
                }
            },
            async uploadFile (item) {
                const form = new FormData();
                form.append("upfile", item.file);
                form.append("platform", this.importFilte.platform);
                const res = importProductCategory(form);
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
            },
            async onshowPlatform () {
                this.platformserviceVisible = true
            }
        }
    }
</script>
<style scoped>
    .setleft {
        position: relative;
        margin-right: 300px;
    }
</style>
