<template>
  <MyContainer>
    <template #header>
    </template>
    <div style="height: 100%;width: 100%;" v-loading="loading">
      <el-table :data="tableData" border stripe height="360" style="width: 100%"
        :default-sort="{ prop: 'createdTime', order: 'descending' }">
        <el-table-column prop="beforeVal" label="修改前值" width="330" tooltip="true" :resizable="false">
          <template slot-scope="scope">
            <div style="display: flex; align-items: center; position: relative;">
              <div style="position: relative; display: inline-block;"
                v-if="scope.row.beforePic != null && scope.row.beforePic != undefined && scope.row.beforePic.length">
                <el-image class="custom-image" :src="scope.row.beforePic[0].url || ''" fit="fill"
                  :preview-src-list="scope.row.beforePic.map(item => item.url) || []"
                  style="width: 40px; height: 38px;">
                </el-image>
                <span class="circle-badge">
                  {{ scope.row.beforePic.length }}
                </span>
              </div>
              <div style="margin-left: 10px;">
                {{ scope.row.beforeVal }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="afterVal" label="修改后值" width="330" tooltip="true">
          <template slot-scope="scope">
            <div style="display: flex; align-items: center; position: relative;">
              <div style="position: relative; display: inline-block;"
                v-if="scope.row.afterPic != null && scope.row.afterPic != undefined && scope.row.afterPic.length">
                <el-image class="custom-image" :src="scope.row.afterPic[0].url || ''" fit="fill"
                  :preview-src-list="scope.row.afterPic.map(item => item.url) || []" style="width: 40px; height: 38px;">
                </el-image>
                <span class="circle-badge">
                  {{ scope.row.afterPic.length }}
                </span>
              </div>
              <div style="margin-left: 10px;">
                {{ scope.row.afterVal }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="操作时间" :show-overflow-tooltip="true" tooltip="true"
          :resizable="false">
        </el-table-column>
        <el-table-column prop="createdUserName" label="操作人" width="90" sortable :show-overflow-tooltip="true"
          tooltip="true" :resizable="false">
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getPurchaseCostVerifyPurOrderLogPage } from '@/api/inventory/purchaseCostVerify'
export default {
  name: "logPage",
  props: {
    logsid: {
      type: String,
      default() {
        return '';
      }
    },
    logIsParent: {
      type: String,
      default() {
        return 0;
      }
    },
  },
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      that: this,
      tableData: [],
      total: 0,
      loading: false,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      const { data, success } = await getPurchaseCostVerifyPurOrderLogPage({ id: this.logsid ? this.logsid : null, isParent: this.logIsParent });
      this.loading = false
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          if (item.afterPic) {
            item.afterPic = JSON.parse(item.afterPic)
          }
          if (item.beforePic) {
            item.beforePic = JSON.parse(item.beforePic)
          }
        })
      } else {
        this.$message.error('获取列表失败')
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

::v-deep .custom-image img {
  max-width: 40px !important;
  max-height: 40px !important;
}

.circle-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: red;
  color: white;
  font-size: 12px;
  width: 13px;
  height: 13px;
  line-height: 13px;
  text-align: center;
  border-radius: 50%;
}
</style>
