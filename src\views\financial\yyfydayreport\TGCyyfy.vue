<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <el-tabs v-model="activeName" style="height:94%;">
      <el-tab-pane   label="无界" name="tab1" style="height: 100%;">
          <TGCztc :filter="Filter" ref="TGCztc" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane   label="国货-无界" name="tab2" style="height: 100%;">
        <TGCyinLiMoFang :filter="Filter" ref="TGCyinLiMoFang" style="height: 100%;"/>
    </el-tab-pane>
    <el-tab-pane   label="淘工厂产品管理" name="tab3" style="height: 100%;">
      <TGCLink :filter="Filter" ref="TGCLink" style="height: 100%;"/>
  </el-tab-pane>
  <el-tab-pane   label="阿里妈妈代投" name="tab4" style="height: 100%;">
    <AliMaMaDaiTou :filter="Filter" ref="AliMaMaDaiTou" style="height: 100%;"/>
</el-tab-pane>
<el-tab-pane   label="商品托管预估" name="tab5" style="height: 100%;">
    <TuoguanYG :filter="Filter" ref="TuoguanYG" style="height: 100%;"/>
</el-tab-pane>
<el-tab-pane label="商品出资补贴" name="tab6" style="height: 100%;">
    <investmentSubsidy :filter="Filter" ref="investmentSubsidy" style="height: 100%;"/>
</el-tab-pane>
<el-tab-pane label="订单管理" name="tab7" style="height: 100%;">
  <orderManagement :filter="Filter" ref="orderManagement" style="height: 100%;"/>
</el-tab-pane>
<el-tab-pane   label="推广明细报表" name="tab8" style="height: 100%;">
    <MonthTuoguanYG :filter="Filter" ref="MonthTuoguanYG" style="height: 100%;"/>
</el-tab-pane>
<el-tab-pane label="广告费验算" name="tab9" style="height: 100%;">
    <advertisingCostCheck :filter="Filter" ref="advertisingCostCheck" style="height: 100%;"/>
</el-tab-pane>
<el-tab-pane label="商家出资补贴-货款" name="tab10" style="height: 100%;">
    <investmentSubsidy_GoodsPay :filter="Filter" ref="investmentSubsidy_GoodsPay" style="height: 100%;"/>
</el-tab-pane>
    </el-tabs>
    </my-container >
   </template>
  <script>
  import MyContainer from "@/components/my-container";
  import TGCztc from '@/views/financial/yyfydayreport/TGCztc'
  import TGCyinLiMoFang from '@/views/financial/yyfydayreport/TGCyinLiMoFang'
  import AliMaMaDaiTou from '@/views/financial/yyfydayreport/AliMaMaDaiTou'
  import TuoguanYG from '@/views/financial/yyfydayreport/TuoguanYG'
  import MonthTuoguanYG from '@/views/financial/yyfydayreport/MonthTuoguanYGIndex'
  import TGCLink from '@/views/financial/yyfydayreport/TGCLink'
  import investmentSubsidy from '@/views/financial/yyfydayreport/investmentSubsidy'
  import investmentSubsidy_GoodsPay from '@/views/financial/yyfydayreport/investmentSubsidy_GoodsPay'
  import orderManagement from '@/views/financial/yyfydayreport/orderManagement'
  import advertisingCostCheck from '@/views/financial/yyfydayreport/advertisingCostCheck'

  import checkPermission from '@/utils/permission'
  export default {
    name: "Users",
    components: { MyContainer,TGCztc,checkPermission,TGCyinLiMoFang,TGCLink,AliMaMaDaiTou,TuoguanYG,investmentSubsidy,orderManagement,MonthTuoguanYG,advertisingCostCheck,investmentSubsidy_GoodsPay},
    data() {
      return {
        that:this,
        Filter: {
        },
        pageLoading:"",
        activeName:"tab1",
        shopList:[],
        userList:[],
        groupList:[],
        selids:[],
        dialogVisibleSyj:false,
        fileList:[],
      };
    },
    mounted() {
    },
    methods: {
  async onSearch(){
    if (this.activeName=='tab1')
    this.$refs.TGCztc.onSearch();

  }
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
