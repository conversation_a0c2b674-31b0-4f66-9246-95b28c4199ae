<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template #header>
        <el-form
          class="ad-form-query"
          :inline="true"
          :model="Filter"
          @submit.native.prevent>
        </el-form>
      </template>
      <!--列表-->
      <ces-table ref="table" :that='that' :isIndex='true'
                :hasexpand='false' @sortchange='sortchange' :tableData='dahuixionglist'
                @select='selectchange' :isSelection='false' :showsummary='true'  :summaryarry='summaryarry'
           :tableCols='tableCols' :loading="listLoading">
        <el-table-column type="expand">
          <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
        </el-table-column>
         <template slot='extentbtn'>
            <el-button-group>
                              <el-button style="padding: 0;margin: 0;">
                                  <el-input v-model.trim="Filter.ProCode" clearable maxlength="100" placeholder="商品ID" style="width:120px;"/>
                              </el-button>
                              <!-- <el-button style="padding: 0;margin: 0;">
                                  <el-input v-model.trim="Filter.PromotePlan" clearable maxlength="100" placeholder="推广计划" style="width:120px;"/>
                              </el-button> -->
                              <el-button style="padding: 0;margin: 0;">
                                  <el-input v-model.trim="Filter.ShopName" clearable maxlength="100" placeholder="店铺名" style="width:120px;"/>
                              </el-button>
                              <el-button style="padding: 0;margin: 0;">
                                  <el-input v-model.trim="Filter.BatchNumber" clearable maxlength="19" placeholder="导入批次" style="width:120px;"/>
                              </el-button>
                              <el-button style="padding: 0;margin: 0;">
                                 <el-date-picker style="width:280px" v-model="Filter.UseDate" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                  range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" ></el-date-picker>
                              </el-button>



              <el-button type="primary" @click="onSearch">查询</el-button>

              <el-button type="primary" @click="onImportSyj">导入</el-button>
            </el-button-group>
          </template>
      </ces-table>
      <!--分页-->
      <template #footer>
        <my-pagination
          ref="pager"
          :total="total"
          :checked-count="sels.length"
          @get-page="getJdExpressList"
        />
      </template>

      <el-dialog title="导入标准推广" :visible.sync="dialogVisibleSyj" width="30%" v-dialogDrag>
        <!-- <span>
            <el-date-picker style="width: 50%" v-model="yearMonth" type="date" format="yyyyMMdd"   value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
        </span> -->
        <span>
            <el-upload ref="upload2" class="upload-demo"
                    :auto-upload="false"
                    :multiple="false"
                    :limit="1"
                    action
                    accept=".xlsx"
                    :http-request="uploadFile2"
                    :on-change="uploadChange" :on-remove="uploadRemove">
                  <template #trigger>
                      <el-button size="small" type="primary">选取文件</el-button>
                  </template>
                  <el-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传</el-button>
            </el-upload>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisibleSyj = false">关闭</el-button>
        </span>
      </el-dialog>
    </my-container>
  </template>
  <script>

  import {importJLThousandRivers,getJLThousandRiversList,deleteJLThousandRiversBatch } from '@/api/financial/yyfy'
  import dayjs from "dayjs";
  import cesTable from "@/components/Table/table.vue";
  import { formatTime } from "@/utils";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  const tableCols =[
        {istrue:true,prop:'yearMonthDay',label:'日期', width:'100',sortable:'custom'},
        {istrue:true,prop:'proCode',label:'商品ID', width:'170',sortable:'custom'},
        {istrue:true,prop:'shopName',label:'店铺', width:'200',sortable:'custom'},
        {istrue:true,prop:'proName',label:'商品名称', width:'100',sortable:'custom'},
        // {istrue:true,prop:'promotePlan',label:'推广计划', width:'100',sortable:'custom'},
        {istrue:true,prop:'sumCost',label:'总费用', width:'100',sortable:'custom',tipmesg:'消耗(元)'},
        {istrue:true,prop:'directlyOrderAmount',label:'总订单金额', width:'150',sortable:'custom',tipmesg:'直接成交金额(元)'},
        // {istrue:true,prop:'directionalType',label:'定向方式', width:'100',sortable:'custom'},
        {istrue:true,prop:'createdTime',label:'导入时间', width:'200',sortable:'custom'},
        {istrue:true,prop:'batchNumber',label:'导入批次', width:'200',sortable:'custom'},
        {istrue: true,type: "button",label:'操作',width: "430",btnList: [{ label: "删除批次", handle: (that, row) => that.deleteBatch(row)}]}
       ];

const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

  export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
    data() {
      return {
        that:this,
        Filter: {
          ProCode:'',
          PromotePlan:'',
          ShopName:'',
          BatchNumber:null,
          UseDate:[startDate, endDate],
          UseDstartAccountDateate:'',
          endAccountDate:'',
        },
        shopList:[],
        userList:[],
        groupList:[],
        dahuixionglist: [],
        tableCols:tableCols,
        total: 0,
        summaryarry:{},
        pager:{OrderBy:"id",IsAsc:false},
        sels: [], // 列表选中列
        listLoading: false,
        pageLoading: false,
        //
        selids:[],
        dialogVisibleSyj:false,
        fileList:[],
        platform:0,
        yearMonth:""
      };
    },
    async mounted() {
      this.onSearch();
    },
    methods: {
      setplatform(platform){
  this.platform=platform;
  },
     async deleteBatch(row){
        var that=this;
        this.$confirm("此操作将删除此批次导入费用数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async() => {
        await deleteJLThousandRiversBatch({batchNumber:row.batchNumber})
        that.$message({message: '已删除', type: "success"});
        that.onRefresh()

          });

      },
      sortchange(column){
        if(!column.order)
          this.pager={};
        else
          this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
        this.onSearch();
      },
      onImportSyj(){
        this.dialogVisibleSyj = true
      },
      async onSubmitupload2() {
        if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
        this.$refs.upload2.submit()
      },
      async uploadFile2(item) {
        const form = new FormData();
        form.append("upfile", item.file);
        // form.append("yearMonth",this.yearMonth);
        const res = importJLThousandRivers(form);
        this.$message({message: '上传成功,正在导入中...', type: "success"});
        this.fileList = []
        this.$refs.upload2.clearFiles();
        this.dialogVisibleSyj = false;
      },
      async uploadChange (file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for (var i = 0; i < fileList.length; i++) {
          if (fileList[i].status == "success") list.push(fileList[i]);
          else list.push(fileList[i].raw);
        }
        this.fileList = list;
      } else {
        this.fileList = [];
      }
    },
    uploadRemove (file, fileList) {
      this.uploadChange(file, fileList);
    },

      onRefresh(){
          this.onSearch()
      },
      onSearch(){
         this.$refs.pager.setPage(1);
         this.getJdExpressList();
      },
      async getJdExpressList(){
        this.Filter.proCode=null;

        this.Filter.promotePlan=null;
        this.Filter.startAccountDate=null;
           this.Filter.endAccountDate=null;

        if(this.Filter.UseDate){
          this.Filter.startAccountDate=this.Filter.UseDate[0];
           this.Filter.endAccountDate=this.Filter.UseDate[1];
         }
         if(/^\d+$/.test(this.Filter.BatchNumber)==false&&this.Filter.BatchNumber!=null&&this.Filter.BatchNumber!=""){
          this.$message.error('请输入正确的批次号！！！！！');
          this.Filter.BatchNumber=null;
          return;
         }
        //  this.Filter.platform=this.platform;
        const para = {...this.Filter};
        var pager = this.$refs.pager.getPager();
        const params = {
          ...pager,
          ...this.pager,
          ...para,

        };

        console.log(para)

        this.listLoading = true;
        const res = await getJLThousandRiversList(params);
        console.log(res)
        this.listLoading = false;
        console.log(res.data.list)
        //console.log(res.data.summary)

        this.total = res.data.total
        this.dahuixionglist = res.data.list;
        this.summaryarry=res.data.summary;
      },
      selectchange:function(rows,row) {
        this.selids=[];
        rows.forEach(f=>{
          this.selids.push(f.id);
        })
      }
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
