<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss" />
                <el-input v-model.trim="ListInfo.orderNo" placeholder="店铺名称" maxlength="50" clearable
                    class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="importProps">导入</el-button>
                <el-button type="primary" @click="clickDiff">核对差异</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :showsummary='true' :summaryarry='summaryarry' :isSelectColumn="false" style="width: 100%;  margin: 0"
            :loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button type="primary" @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog title="核对差异" :visible.sync="diffVisible" width="30%" v-dialogDrag v-loading="diffLoading">
            <div style="display: flex;justify-content: center;">
                <el-date-picker v-model="diffInfo.dataDate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd">
                </el-date-picker>
            </div>
            <div style="display: flex;justify-content: center;margin-top: 20px;">
                <el-button type="primary" @click="diffVisible = false">取消</el-button>
                <el-button type="primary" @click="diffSubmit">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { PageShopBill_Jd, ImportStoreFundStatementAsync, CheckShopFundStatement } from '@/api/bookkeeper/reportdayV2'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'yearMonthDayDate', label: '日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'receivable', label: '应收', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'copeWith', label: '应付', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'actualSettlement', label: '实际结算', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'incomeVerification', label: '收入核对', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'reconciliationExpenditure', label: '支出核对', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            fileList: [],
            importLoading: false,
            importVisible: false,
            file: null,
            diffVisible: false,
            diffLoading: false,
            diffInfo: {
                billingType: 2,
                dataDate: ''
            },
            summaryarry: {}
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        clickDiff() {
            this.diffInfo = {
                billingType: 2,
                dataDate: ''
            }
            this.diffVisible = true
        },
        async diffSubmit() {
            if (this.diffInfo.dataDate == '') return this.$message.error('请选择日期')
            this.$message.info('正在核对差异中,请稍后...')
            this.diffLoading = true
            const { success } = await CheckShopFundStatement(this.diffInfo)
            if (success) {
                this.$message.success('核对差异成功')
                this.diffVisible = false
                this.getList()
            } else {
                this.$message.error('核对差异失败')
            }
            this.diffLoading = false
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            //没有时间就提示
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            form.append("billingType", 2);
            this.importLoading = true
            await ImportStoreFundStatementAsync(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            if (this.timeRanges.length == 0) {
                //默认给近7天时间
                this.ListInfo.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await PageShopBill_Jd(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.summaryarry = data.summary
                    this.loading = false
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>
