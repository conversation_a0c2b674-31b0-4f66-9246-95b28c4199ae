<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="运单号:">
                    <el-input v-model.trim="filter.billnumber" style="width: 150px" />
                </el-form-item>
                <el-form-item label="批次号:">
                    <!-- <el-input v-model="filter.batchNumber" type="number"  style="width: 150px" /> -->
                    <el-input v-model.trim="filter.batchNumber" style="width: 150px" :maxlength="19" oninput="if(value){value=value.replace(/[^\d]/g,'')} if(value<0){value=''}"/>
                </el-form-item>
                <el-form-item label="发货仓库:">
                    <el-select v-model="filter.warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 100px">
                        <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="发往省份:">
                    <el-input v-model="filter.province" style="width: 100px" />
                </el-form-item>
                <el-form-item label="快递公司:">
                    <el-select v-model="filter.companyId" placeholder="请选择快递公司" style="width: 130px">
                        <el-option label="所有" value="" />
                        <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="揽收时间:">
                    <el-date-picker v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                </el-form-item>
                <el-form-item label="导入时间:">
                    <el-date-picker v-model="filter.timerangeimport" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :isSelection='true' :hasexpand='true' @sortchange='sortchange' :tableData='list' @select='selectchange' :summaryarry='summaryarry' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col,index) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="index"></el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="批量操作" :visible.sync="dialogbatchNumberVisible" width="500px" v-dialogDrag>
            <el-row>
                <el-col :xs="24" :sm="16" :md="16" :lg="16" :xl="16">
                    <el-input placeholder="请输入批次号" v-model="operfilter.batchNumber" oninput="if(value){value=value.replace(/[^\d]/g,'')} if(value<=0){value=''}" style="width: 100%" />
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" v-if="operfilter.type==0">
                    <el-button type="primary" @click="onDelete(1)">删除</el-button>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" v-else-if="operfilter.type==1">
                    <el-button type="primary" @click="onCumulation(1)">累加</el-button>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" v-else-if="operfilter.type==2">
                    <el-button type="primary" @click="onCover(1)">覆盖</el-button>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogbatchNumberVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>
<script>
    import cesTable from "@/components/Table/table.vue";
    import container from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import { getExpressComanyAll } from "@/api/express/express";
    import { pageFreightRepeat, batchUpdateRepeatExpress, exportFreightRepeat } from "@/api/express/freight";
    import { formatTime, formatPass, formatRule, formatWarehouse, formatExpressCompany, formatYesornoBool, warehouselist } from "@/utils/tools";
    const tableCols = [
        { istrue: true, prop: 'receiveTime', label: '年月', width: '60', sortable: 'custom', formatter: (row) => formatTime(row.receiveTime, 'YYYYMM') },
        { istrue: true, prop: 'billnumber', label: '运单号码', width: '120', sortable: 'custom' },
        { istrue: true, prop: 'isHandler', label: '是否处理', width: '80', sortable: 'custom', formatter: (row) => formatYesornoBool(row.isHandler) },
        {
            istrue: true, prop: 'handlerType', label: '处理', width: '60', sortable: 'custom', type: 'format', formatter: (row) => {
                if (!row.isHandler) return "";
                else if (row.handlerType == 0) return "删除";
                else if (row.handlerType == 1) return "累加";
                else if (row.handlerType == 2) return "覆盖";
                else return "";
            }
        },
        { istrue: true, prop: 'expressCompanyId', label: '快递公司', width: '75', sortable: 'custom', formatter: (row) => formatExpressCompany(row.expressCompanyId) },
        { istrue: true, prop: 'warehouse', label: '发货仓', width: '75', sortable: 'custom', formatter: (row) => formatWarehouse(row.warehouse) },
        { istrue: true, prop: 'weight', label: '重量(KG)', width: '85', sortable: 'custom' },
        { istrue: true, prop: 'totalFee', label: '账单费', width: '75', sortable: 'custom', summary: true },
        { istrue: true, prop: 'volumeWeight', label: '体积重(kg)', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'province', label: '发往省份', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'receiveTime', label: '揽收时间', width: '145', sortable: 'custom' },
        { istrue: true, prop: 'createdTime', label: '创建时间', width: '145' },
        { istrue: true, prop: 'consignee', label: '收货人', width: '150' },
        { istrue: true, prop: 'reMark', label: '备注', width: '150' },
        { istrue: true, prop: 'createdUserName', label: '创建者' },
        { istrue: true, prop: 'batchNumber', label: '批次号', width: '180' },
        { istrue: true, prop: 'ruleBatchNumber', label: '规则批次号', width: '180' },
    ];
    const tableHandles1 = [
        { label: "导出", handle: (that) => that.onExport() },
        { label: '覆盖', handle: (that) => that.onCover(0) },
        { label: '累加', handle: (that) => that.onCumulation(0) },
        { label: '删除', handle: (that) => that.onDelete(0) },
        { label: '覆盖(按批次号)', handle: (that) => that.onStartBatch(2) },
        { label: '累加(按批次号)', handle: (that) => that.onStartBatch(1) },
        { label: '删除(按批次号)', handle: (that) => that.onStartBatch(0) },
    ];
    export default {
        name: "Users",
        components: { container, MyConfirmButton, MySearch, MySearchWindow, cesTable },
        data () {
            return {
                that: this,
                filter: {
                    startCreatedTime: "",
                    endCreatedTime: "",
                    startReciveTime: "",
                    endReciveTime: "",
                    warehouse: null,
                    companyId: null,
                    province: null,
                    billnumber: null,
                    timerange: "",
                    timerangeimport: "",
                    batchNumber: null,
                },
                warehouselist: warehouselist,
                operfilter: {
                    selids: [],
                    batchNumber: null,
                    type: null,//0:刪除 1:累加 2:覆盖
                },
                list: [],
                summaryarry: { difference_sum: 10 },
                total: 0,
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                userNameReadonly: true,
                deleteLoading: false,
                selectedexpresscompanyid: "",
                expresscompanylist: [],
                rulebatchnumberlist: [],
                dialogVisible: false,
                pager: { OrderBy: "id", IsAsc: false },
                tableCols: tableCols,
                tableHandles: tableHandles1,
                importtype: 0,//0:快递账单 1:月账单
                dialogbatchNumberVisible: false
            };
        },
        async mounted () {
            await this.onSearch();
            await this.getExpressComanyList();
        },
        methods: {
            async onShow () {
                this.$refs.pager.setPage(1);
                this.getlist();
            },
            async onSearch () {
                this.$refs.pager.setPage(1);
                this.getlist();
            },
            async getlist () {
                if (this.filter.timerangeimport) {
                    this.filter.startCreatedTime = this.filter.timerangeimport[0];
                    this.filter.endCreatedTime = this.filter.timerangeimport[1];
                }
                else { 
                    this.filter.startCreatedTime = null;
                    this.filter.endCreatedTime = null;
                }
                if (this.filter.timerange) {
                    this.filter.startReciveTime = this.filter.timerange[0];
                    this.filter.endReciveTime = this.filter.timerange[1];
                }
                else { 
                    this.filter.startReciveTime = null;
                    this.filter.endReciveTime = null;
                }

                var pager = this.$refs.pager.getPager();
                const params = { ...pager, ...this.pager, ...this.filter };
                this.listLoading = true;
                const res = await pageFreightRepeat(params);
                this.listLoading = false;
                if (!res?.success) return;
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summary;
                data.forEach((d) => {
                    d._loading = false;
                    d.detailcols = [];
                    d.detaildata = [];
                    if (d.detail) {
                        var i = 0;
                        d.detail.forEach((da) => {
                            var dlist = JSON.parse(da.extent);
                            var row = {};
                            dlist.forEach((item) => {
                                if (i == 0)
                                    d.detailcols.push({ prop: item.name, label: item.lable });
                                row[item.name] = item.value;
                            });
                            d.detaildata.push(row);
                            i++;
                        });
                    }
                });
                this.list = data;
            },
            sortchange (column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            async getExpressComanyList () {
                const res = await getExpressComanyAll({});
                if (!res?.success) {
                    return;
                }
                const data = res.data;
                this.expresscompanylist = data;
            },
            onSelsChange (sels) {
                this.sels = sels;
            },
            selectchange: function (rows, row) {
                this.operfilter.selids = [];
                rows.forEach(f => {
                    this.operfilter.selids.push(f.id);
                })
            },
            async onStartBatch (type) {
                this.dialogbatchNumberVisible = true;
                this.operfilter.type = type;
            },
            async onDelete (selecttype) {
                if (selecttype == 0) this.operfilter.batchNumber = null;
                this.$confirm('确定删除吗, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                    .then(async () => {
                        console.log('operfilter2', this.operfilter)
                        const res = await batchUpdateRepeatExpress({ type: 0, repeatIds: this.operfilter.selids.join(), batchNumber: this.operfilter.batchNumber })
                        if (!res?.success) { return }
                        this.$message({ type: 'success', message: '提交成功!' });
                        this.getlist()
                        this.operfilter = { selids: [], batchNumber: null, type: null }
                        this.dialogbatchNumberVisible=false;
                    }).catch(() => {
                        this.$message({ type: 'info', message: '已取消' });
                        this.operfilter = { selids: [], batchNumber: null, type: null }
                        this.dialogbatchNumberVisible=false;
                    });
            },
            async onCover (selecttype) {
                if (selecttype == 0) this.operfilter.batchNumber = null;
                this.$confirm('确认覆盖, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                    .then(async () => {
                        const res = await batchUpdateRepeatExpress({ type: 2, repeatIds: this.operfilter.selids.join(), batchNumber: this.operfilter.batchNumber })
                        if (!res?.success) { return }
                        this.$message({ type: 'success', message: '提交成功!' });
                        this.getlist()
                        this.operfilter = { selids: [], batchNumber: null, type: null }
                        this.dialogbatchNumberVisible=false;
                    }).catch(() => {
                        this.$message({ type: 'info', message: '提交失败' });
                        this.operfilter = { selids: [], batchNumber: null, type: null }
                        this.dialogbatchNumberVisible=false;
                    });
            },
            async onCumulation (selecttype) {
                if (selecttype == 0) this.operfilter.batchNumber = null;
                this.$confirm('确认累加, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                    .then(async () => {
                        const res = await batchUpdateRepeatExpress({ type: 1, repeatIds: this.operfilter.selids.join(), batchNumber: this.operfilter.batchNumber })
                        if (!res?.success) { return }
                        this.$message({ type: 'success', message: '提交成功!' });
                        this.getlist()
                        this.operfilter = { selids: [], batchNumber: null, type: null }
                        this.dialogbatchNumberVisible=false;
                    }).catch(() => {
                        this.$message({ type: 'info', message: '提交失败' });
                        this.operfilter = { selids: [], batchNumber: null, type: null }
                        this.dialogbatchNumberVisible=false;
                    });
            },
            async onExport () {
                if (this.filter.timerangeimport) {
                    this.filter.startCreatedTime = this.filter.timerangeimport[0];
                    this.filter.endCreatedTime = this.filter.timerangeimport[1];
                }
                if (this.filter.timerange) {
                    this.filter.startReciveTime = this.filter.timerange[0];
                    this.filter.endReciveTime = this.filter.timerange[1];
                }
                const params = { ...this.pager, ...this.filter };
                var res = await exportFreightRepeat(params);
                if (!res?.data) return
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '重复账单_' + new Date().toLocaleString() + '.xlsx')
                aLink.click()
            },
        },
    };
</script>

<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
