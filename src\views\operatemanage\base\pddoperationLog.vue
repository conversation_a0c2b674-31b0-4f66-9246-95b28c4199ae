<template>
  <container>
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="ListInfo" @submit.native.prevent>
        <el-form-item label="时间">
          <el-date-picker style="width: 220px" v-model="ListInfo.timerange" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
            :picker-options="pickerOptions"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
    <ces-table :id="'pddoperationLog202408041645'" ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :loading="listLoading">
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>
  </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import { getProductTemuLogList } from "@/api/operatemanage/productmanager"
import { formatTime } from "@/utils";

const tableCols = [
  { istrue: true, prop: 'createdUserName', label: '操作人', tipmesg: '', width: 'auto', sortable: 'custom', },
  { istrue: true, prop: 'createdTime', label: '操作时间', tipmesg: '', width: 'auto', sortable: 'custom', formatter: (row) => formatTime(row.createdTime, "YYYY-MM-DD HH:mm:ss") },
  { istrue: true, prop: 'changeType', label: '修改类型', tipmesg: '', width: 'auto' },
  { istrue: true, prop: 'oldDisPlayValue', label: '修改前', tipmesg: '', width: 'auto' },
  { istrue: true, prop: 'newDisPlayValue', label: '修改后', tipmesg: '', width: 'auto' }
]

export default {
  name: 'pddoperationLog',
  components: { container, cesTable, MyConfirmButton },
  props: {
    proCodeLogs: { type: String, default: '' },
  },
  data() {
    return {
      that: this,
      ListInfo: {
        startTime: null,
        endTime: null,
        timerange: null
      },
      list: [],
      pager: { OrderBy: "createdTime", IsAsc: false },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      pickOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      tableCols: tableCols,
      total: 0,
      sels: [],
      uploadLoading: false,
      dialogVisible: false,
      listLoading: false,
      fileList: []
    };
  },

  async mounted() {
    // await this.onSearch()
  },

  methods: {
    async clearnFilter() {
      this.ListInfo.timerange = null;
    },
    //查询第一页
    async onSearch() {
      this.list = [];
      this.$refs.pager.setPage(1);
      await this.getlist();
    },
    async getlist() {
      if (this.pager.OrderBy == null) {
        this.pager.OrderBy = "createdTime";
        this.pager.IsAsc = false;
      }
      let pager = this.$refs.pager.getPager();
      let page = this.pager;
      this.ListInfo.startTime = null;
      this.ListInfo.endTime = null;
      if (this.ListInfo.timerange) {
        this.ListInfo.startTime = this.ListInfo.timerange[0];
        this.ListInfo.endTime = this.ListInfo.timerange[1];
      }
      const params = { ...pager, ...page, ...this.ListInfo, proCode: this.proCodeLogs }
      if (params === false) {
        return;
      }
      this.listLoading = true
      const res = await getProductTemuLogList(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.list = data
    },
    async nSearch() {
      await this.getlist()
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    }
  }
};
</script>

<style lang="scss" scoped></style>
