<template>
    <container>

        <!-- <template #header>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onSaveWare">保存</el-button>
        </template> -->
        <div>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onAddRow">新增一行</el-button>
            <el-button type="primary" @click="onSaveWare">保存</el-button>
            <el-button type="primary" @click="onExport" :loading="exportLoading">导出</el-button>
        </div>
        <!-- <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            @select='selectchange' :isSelection='false' :isSelectColumn='true' :tableData='list' :tableCols='tableCols'
            :tableHandles='tableHandles' :showsummary='false' :loading="listLoading" /> -->
        <div>
            <el-table :data="list" ref="warechildTable1" height="500">
                <el-table-column prop="warehouse_main" label="主仓">
                    <template slot-scope="scope">
                        <el-input v-model.trim="scope.row.warehouse_main" :placeholder="主仓">
                        </el-input>
                    </template>
                </el-table-column>
                <el-table-column prop="warehouse_sub" label="子仓">
                    <template slot-scope="scope">
                        <el-input v-model.trim="scope.row.warehouse_sub" :placeholder="子仓">
                        </el-input>
                    </template>
                </el-table-column>
                <el-table-column lable="操作" width="100">
                    <template slot-scope="scope">
                        <el-button type="danger" @click="onDelRow(scope.$index)">移除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </container>
</template>
<script>
import { GetMonthWareWagesChildWarePageList, SaveMonthWareWagesChildWare, exportMonthWareWagesChildWareRpt } from '@/api/monthbookkeeper/financialDetail'
import container from '@/components/my-container/noheader'
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
const tableCols = [
    { istrue: true, prop: 'warehouse_main', label: '主仓', sortable: 'custom', width: '300' },
    { istrue: true, prop: 'warehouse_sub', label: '子仓', sortable: 'custom', width: '300' },
];
const tableHandles = [
    { label: "查询", handle: (that) => that.onSearch() },
    { label: "保存", handle: (that) => that.onSaveWare() },
]
export default {
    name: 'warechild',
    components: { cesTable, container, MyConfirmButton },
    props: {
        filter: {}
    },
    data() {
        return {
            that: this,
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "warehouse_main", IsAsc: false },
            summaryarry: {},
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            pageLoading: false,
            exportLoading: false,
        }
    },
    mounted() {

    },
    beforeUpdate() { },
    methods: {
        loadData() {
            this.onSearch();
        },
        onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }
            this.listLoading = true
            const res = await GetMonthWareWagesChildWarePageList(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res.data?.total ?? 0
            const data = res.data?.list
            data?.forEach(d => {
                d._loading = false
            })
            this.list = data
        },
        async onExport(){
            let pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }
            this.exportLoading = true
            let res = await exportMonthWareWagesChildWareRpt(params);
            this.exportLoading = false
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '月报仓库薪资_子仓_' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onSaveWare() {
            const res = await SaveMonthWareWagesChildWare(this.list);
            if (res?.success) {
                this.$message({ message: '保存成功', type: "success" });
            }
        },
        onAddRow() {
            this.list.push({ warehouse_main: '', warehouse_sub: '' })
        },
        onDelRow(index) {
            this.$confirm("是否确定移除?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                this.list.splice(index, 1);
            });
        },
    }
}
</script>
