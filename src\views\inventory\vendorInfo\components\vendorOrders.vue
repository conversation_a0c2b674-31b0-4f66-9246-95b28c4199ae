<template>
    <MyContainer>
        <template #header>
            <div class="header">
                <el-input placeholder="系列编码" v-model="ListInfo.styleCode" maxlength="50" class="publicMargin"
                    style="width: 220px;" clearable></el-input>
                <el-input placeholder="商品编码" v-model="ListInfo.goodCode" maxlength="50" class="publicMargin"
                    style="width: 220px;" clearable></el-input>
                <el-input placeholder="供应商名称" v-model="ListInfo.providerName" maxlength="50" class="publicMargin"
                    style="width: 220px;" clearable></el-input>
                <el-select v-model="ListInfo.isBY" placeholder="是否包邮" class="publicMargin" style="width: 220px;" clearable>
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.brandId" placeholder="采购" class="publicMargin" style="width: 220px;" clearable
                    filterable>
                    <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.sourceType" placeholder="来源" class="publicMargin" style="width: 220px;"
                    clearable>
                    <el-option v-for="item in sourceType" :key="item.label" :label="item.label" :value="item.label">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.position" placeholder="职位" class="publicMargin" style="width: 220px;"
                    clearable>
                    <el-option v-for="item in positionType" :key="item.label" :label="item.label" :value="item.label">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.isContaisTax" placeholder="是否含税" class="publicMargin" style="width: 220px;"
                    clearable>
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.dockingStatus" placeholder="对接状态" style="width: 220px" class="publicMargin"
                    v-show="activeName != 'second'" clearable>
                    <el-option v-for="item in status" :key="item.label" :label="item.label" :value="item.label" />
                </el-select>
                <el-select v-model="ListInfo.IsExitProvider" placeholder="重复供应商" style="width: 220px" class="publicMargin"
                    clearable>
                    <el-option label="未重复" :value="2" />
                    <el-option label="重复" :value="1" />
                </el-select>
                <el-button type="primary" @click="searchList">查询</el-button>
                <el-button type="primary" @click="ClickAssignment">一键分配</el-button>
                <el-button type="primary" @click="setExportCols">导出</el-button> 
            </div>
        </template>
        <vxetablebase ref="alreadyTable" :tableData="tableData" :tableCols="tableCols" :is-index="true" :that="that"
            :showsummary="true" style="width: 100%; height: 650px; margin: 0" @sortchange='sortchange' :cstmExportFunc="onExport"
            :treeProp="{ rowField: 'disId', parentField: 'disParentId' }" :loading="listLoading" class="already">
        </vxetablebase>
        <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="detailTotal"
            @page-change="detailPagechange" @size-change="detailSizechange" />

        <!-- 弹层部分 -->
        <el-dialog title="详情" :visible.sync="dialogVisible" width="90%" :before-close="handleClose" v-dialogDrag>
            <cesTable :id="'vendorOrders2024080416221_1'" ref="detailTable" :tableData="tableData1" :tableCols="tableCols1" :is-index="true" :that="that"
                :showsummary="true" style="width: 100%; height: 500px" @sortchange='sortchange1' class="detail">
            </cesTable>
            <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="logTotal"
                @page-change="logPagechange" @size-change="logSizechange" style="margin-top: 40px;" />
        </el-dialog>

        <el-dialog title="对接记录" :visible.sync="RecordsVisible" width="60%" :before-close="handleClose1" v-dialogDrag>
            <cesTable :id="'vendorOrders2024080416221_2'" ref="detailTable" :tableData="doTableData" :tableCols="tableCols4" :is-index="true" :that="that"
                :showsummary="true" style="width: 100%; height: 500px" @sortchange='sortchange2' class="detail">
            </cesTable>
            <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="recordsTotal"
                @page-change="dockingRecordsPagechange" @size-change="dockingRecordsSizechange" style="margin-top: 40px;" />
        </el-dialog>

        <el-dialog title="操作" :visible.sync="operateVisible" width="50%" :before-close="handleClose" v-dialogDrag>
            <el-select v-model="RecordsInfo.dockingStatus" placeholder="对接状态" style="width: 220px;margin-bottom: 10px;">
                <el-option v-for="item in status" :key="item.label" :label="item.label" :value="item.label" />
            </el-select>
            <el-input type="textarea" placeholder="请输入内容" v-model="RecordsInfo.result" maxlength="300" show-word-limit
                rows="5" />
            <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="9"
                :on-success="handleSuccess" :file-list="picFileList" multiple :show-file-list="false"
                accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                <el-button class="addsc" type="text">上传图片</el-button>
            </el-upload>
            <div v-if="RecordsInfo.picLists.length > 0">
                <div class="imageList_box">
                    <div class="imageList" v-for="item in RecordsInfo.picLists">
                        <el-image class="imgcss" style="width: 100px; height: 100px" :src="item"
                            :preview-src-list="RecordsInfo.picLists">
                        </el-image>
                        <span class="del" @click="delImg(item, i)">x</span>
                    </div>
                </div>

            </div>
            <div style="text-align: right;margin-top: 20px;">
                <el-button type="primary" @click="operateVisible = false">取消</el-button>
                <el-button type="primary" @click="operateSubmit">提交</el-button>
            </div>
        </el-dialog>

        <el-dialog title="采购记录" :visible.sync="nameVisible" width="40%" :before-close="handleClose" v-dialogDrag>
            <vxetablebase ref="detailTable" :tableData="nameTableData" :tableCols="tableCols5" :is-index="true" :that="that"
            :showsummary="true" :summaryarry="nameSummary" style="width: 100%; height: 500px" @sortchange='sortchange3'>
            </vxetablebase>
            <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="nameTotal"
                @page-change="namePagechange" @size-change="nameSizechange" style="margin-top: 40px;" />
        </el-dialog>

        <el-dialog title="一键分配" :visible.sync="assignmentVisible" width="20%" :before-close="handleClose" v-dialogDrag>
            <div class="assignmentBox">
                <el-select v-model="assignmentInfo.brandId" placeholder="请选择" style="width: 220px;"
                    @change="changeAssignment" clearable filterable>
                    <el-option v-for="item in procurementList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <div class="btnBox">
                    <el-button @click="assignmentVisible = false" style="margin-right: 20px;">取消</el-button>
                    <el-button type="primary" @click="assignmentSubmit">确定</el-button>
                </div>
            </div>

        </el-dialog>

        <el-dialog title="预览" :visible.sync="previewVisible" width="20%" :before-close="handleClose" v-dialogDrag>
            <p style="color: red;">此预览仅支持图片文件,其他类型文件请下载查看!</p>
            <div class="previewBox">
                <div v-for="(item, i) in previewSrcList">
                    <el-image v-if="imgType.some(item1 => item.includes(item1))" style="width: 100px; height: 100px"
                        :src="item" :preview-src-list="srcList" />
                    <div class="fileCss" v-else>{{ `附件${i + 1} ${item.substring(item.lastIndexOf('.'))}` }}</div>
                </div>
            </div> 
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
import {
    getProviderQuotationRecordPageList,
    getProviderQuotationCGRecordPageList,
    getProviderNewGoodsPageList,
    getProviderQuotationHisRecordPageList,
    GetProviderDockingResultList,
    addProviderDockingResult,
    setProviderDockingUser,
    setProviderNewGoodsDockingUser,
    addProviderNewGoodsDockingResult,
    getProviderNewGoodsDockingResultList,
    getBrandUsers,
    exportProviderQuotationRecord
}
    from '@/api/openPlatform/ProviderQuotation'
import { pagePurchaseOrderByProviderNameAsync } from '@/api/inventory/purchase'
import { getAllProBrand } from '@/api/inventory/warehouse'
const options = [
    {
        value: '1',
        label: '是'
    },
    {
        value: '0',
        label: '否'
    }
]

const positionType = [
    {
        label: '老板'
    },
    {
        label: '业务员'
    },
    {
        label: '经理'
    }
]

const sourceType = [
    {
        label: '朋友圈'
    },
    {
        label: '聊天'
    },
    {
        label: '其他'
    }
]

//已提交
const tableCols = [
    { istrue: true, label: '', type: "checkbox" }, 
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: 95, fixed: 'left', treeNode: true, },
    { istrue: true, prop: 'goodCode', label: '商品编码', sortable: 'custom', width: 95, fixed: 'left' },
    { istrue: true, prop: 'providerName', label: '供应商名称', type: 'treeStar1', sortable: 'custom', width: 130, style: "color: rgb(72, 132, 243);cursor:pointer;", handle: (that, row) => that.openNameDialog(row) },
    { istrue: true, prop: 'sourceName', label: '推荐人', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'sourceType', label: '来源', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'dockingStatus', label: '对接状态', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'goodName', label: '商品名称', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'goodPic', label: '商品图片', sortable: 'custom', width: 95, type: 'images' },
    { istrue: true, prop: 'costPrice', label: '成本价', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'quotation1', label: '进货量100报价', width: 130, sortable: 'custom', type: 'changeColor', formatter: (row) => row.quotation1 > row.costPrice },
    { istrue: true, prop: 'quotation2', label: '进货量10000报价', width: 150, sortable: 'custom', type: 'changeColor', formatter: (row) => row.quotation2 > row.costPrice },
    { istrue: true, prop: 'quotation3', label: '进货量100000报价', width: 170, sortable: 'custom', type: 'changeColor', formatter: (row) => row.quotation3 > row.costPrice },
    { istrue: true, prop: 'phone', label: '联系电话', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'isWX', label: '微信是否同号', width: 120, sortable: 'custom', formatter: (row) => row.isWX == 1 ? '是' : '否' },
    { istrue: true, prop: 'position', label: '职位', width: 70, sortable: 'custom' },
    { istrue: true, prop: 'isBY', label: '是否包邮', width: 95, sortable: 'custom', formatter: (row) => row.isBY == 1 ? '是' : '否' },
    { istrue: true, prop: 'isContaisTax', label: '是否含税', width: 95, sortable: 'custom', formatter: (row) => row.isContaisTax == 1 ? '是' : '否' },
    { istrue: true, prop: 'sheng', label: '发货地址', width: 95, sortable: 'custom', formatter: (row) => row.sheng + row.shi + row.qu },
    { istrue: true, prop: 'remark', label: '备注', sortable: 'custom', width: 80 },
    { istrue: true, prop: 'modifiedTime', label: '最后填写日期', sortable: 'custom', width: 150 },
    {
        istrue: true, prop: 'address', label: '归属地', sortable: 'custom', width: 150, formatter: (row) => {
            if (row.address != null || row.ip != null) {
                return row.address + `(${row.ip})`
            } else {
                return ''
            }
        }
    },
    { istrue: true, prop: 'brandName', label: '采购', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'dockingBrandName', label: '对接人', sortable: 'custom', width: 120 },
    { istrue: true, prop: 'dockingCount', label: '对接次数', sortable: 'custom', width: 120, type: 'click', fixed: 'right', handle: (that, row) => that.dockingRecords(row, 0) },
    {
        istrue: true, type: "treeButton", display: true, label: '操作', style: "color: rgb(72, 132, 243);cursor:pointer;", width: 120, fixed: 'right',
        btnList: [
            // { display: (row) => { return row.disParentId == 0 }, label: "查看", handle: (that, row) => that.openView(row) },
            { display: (row) => { return (row.disParentId != 0 || row.dockingBrandName == null); }, label: "操作", handle: (that, row) => that.operate(row, 0, true) }
        ]
    },
]
//详情
const tableCols1 = [
    { istrue: true, prop: 'openId', label: 'openId', sortable: 'custom', width: 85 },
    {
        istrue: true, prop: 'setStylePic', label: '系列编码图片', type: 'treeimages', isImgtree: true, width: 100, formatter: (row) => {
            if (row.setStylePic) {
                return row.setStylePic
            } else {
                return row.stylePic
            }
        }
    },
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: 100 },
    { istrue: true, prop: 'brandName', label: '采购', sortable: 'custom', width: 100 },
    { istrue: true, prop: 'sourceName', label: '推荐人', sortable: 'custom', width: 100 },
    { istrue: true, prop: 'sourceType', label: '来源', sortable: 'custom', width: 100 },
    { istrue: true, prop: 'providerName', label: '供应商名称', sortable: 'custom', width: 130, type: 'treeStar1' },
    { istrue: true, prop: 'phone', label: '联系电话', sortable: 'custom', width: 100 },
    { istrue: true, prop: 'isWX', label: '微信是否同号', sortable: 'custom', width: 120, formatter: (row) => row.isWX == 1 ? '是' : '否' },
    { istrue: true, prop: 'position', label: '职位', sortable: 'custom', width: 100 },
    { istrue: true, prop: 'isBY', label: '是否包邮', width: 100, sortable: 'custom', formatter: (row) => row.isBY == 1 ? '是' : '否' },
    { istrue: true, prop: 'isContaisTax', label: '是否含税', width: 100, sortable: 'custom', formatter: (row) => row.isContaisTax == 1 ? '是' : '否' },
    { istrue: true, prop: 'sheng', label: '发货地址', width: 150, sortable: 'custom', formatter: (row) => row.sheng + row.shi + row.qu },
    // { istrue: true, prop: 'goodsCode', label: '商品编码', width: 85, sortable: 'custom' },
    { istrue: true, prop: 'quotation1', label: '进货量100报价', width: 150, sortable: 'custom' },
    { istrue: true, prop: 'quotation2', label: '进货量10000报价', width: 150, sortable: 'custom' },
    { istrue: true, prop: 'quotation3', label: '进货量100000报价', width: 170, sortable: 'custom' },
    { istrue: true, prop: 'remark', label: '备注', sortable: 'custom', width: 80, },
    { istrue: true, prop: 'modifiedTime', label: '最后填写日期', sortable: 'custom', width: 150 },
    {
        istrue: true, prop: 'address', label: '归属地', sortable: 'custom', width: 100, formatter: (row) => {
            if (row.address != null || row.ip != null) {
                return row.address + `(${row.ip})`
            } else {
                return ''
            }
        }
    },
]
//对接记录
const tableCols4 = [
    { istrue: true, prop: 'createdUserName', label: '对接人', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '对接时间', sortable: 'custom' },
    { istrue: true, prop: 'dockingStatus', label: '对接状态', sortable: 'custom' },
    { istrue: true, prop: 'result', label: '对接结果', sortable: 'custom' },
    { istrue: true, prop: 'picJsons', label: '图片', type: 'images' },
]
//采购记录
const tableCols5 = [
    { istrue: true, prop: 'purchaseDate', label: '采购时间', sortable: 'custom' },
    { istrue: true, prop: 'buyNo', label: '采购单号', sortable: 'custom' },
    { istrue: true, prop: 'totalAmont', label: '采购金额', sortable: 'custom' },
    { istrue: true, prop: 'count', label: '采购量', sortable: 'custom' },
]

const status = [
    {
        label: '待沟通',
        value: '待沟通'
    },
    {
        label: '沟通中',
        value: '沟通中'
    },
    {
        label: '寄样中',
        value: '寄样中'
    },
    {
        label: '采购中',
        value: '采购中'
    },
    {
        label: '采购完成',
        value: '采购完成'
    },
    {
        label: '不适合',
        value: '不适合'
    },
]
export default {
    components: { MyContainer, cesTable, vxetablebase, },
    name: "vendorQuote",
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: 'modifiedTime',//排序字段
                isAsc: false,//是否升序
                styleCode: null,//系列编码
                categoryId: null,//品类id
                openId: null,//openId
                goodCode: null,//商品编码
                isBY: null,//是否包邮
                isContaisTax: null,//是否含税
                providerName: null,//供应商名称
                dockingStatus: null,//对接状态
                sourceType: null,//来源
                brandId: null,//采购人员id
                position: null,//职位
            },
            logDetail: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                openId: null,//openId
                styleCode: null,//系列编码
                orderBy: null,//排序字段
                isAsc: true,//是否升序
            },
            RecordsInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                recordId: null,//记录id
                newGoodsRecordId: null,//新品记录id
                isDockingCG: 0,//是否对接采购 0已提交 1未提交
                styleCode: null,//系列编码
                openId: null,//openId
                result: null,//对接结果
                dockingStatus: null,//对接状态
                pics: null,
                providerQuotationRecordId: null,
                recordId: null,
                id: null,
                picLists: []//图片列表
            },//对接记录请求参数
            nameInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                supplier: null,//供应商名称
            },
            assignmentInfo: {
                recordIds: [],//选中的id
                brandId: null,//采购人员id
                brandName: null,//采购人员名字
                dockingStatus: '待沟通'
            },
            sourceType,//来源
            positionType,//职位
            tableCols,//已提交
            tableCols1,//详情
            tableCols4,//对接记录
            tableCols5,//点击供应商名字
            tableData: [],//已提交
            tableData1: [],//详情
            newTableData: [],//新品提交
            doTableData: [],//对接记录
            nameTableData: [],//点击供应商名字
            picFileList: [],//图片上传列表
            brandList: [],//采购列表
            previewSrcList: [],//预览列表
            srcList: [],//预览图片列表
            imgType: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.pdf', '.JPG', '.JPEG', '.PBG', '.GIF', '.BMP', '.PDF'],
            logTotal: 0,//详情总数
            detailTotal: 0,//已提交总数
            nameTotal: 0,//供应商报价详情总数
            listLoading: true,//加载中
            dialogVisible: false,//详情弹层
            RecordsVisible: false,//对接记录弹层
            operateVisible: false,//操作弹层
            nameVisible: false,//点击供应商名字弹层
            assignmentVisible: false,//一键分配弹层
            previewVisible: false,//预览弹窗
            recordsTotal: 0,//对接记录总数
            activeName: 'first',//tab切换
            options,//是否包邮,是否含税
            status,//状态
            isOuter: false,//判断是外面的对接记录还是弹窗的对接记录 false为外面的 true为弹窗的
            procurementList: [],//采购人员列表
            nameSummary:null, 
        };
    },
    mounted() {
        this.getAlreadyList()
        this.getBrandList()
    },
    methods: {
        //预览文件
        previewFiles(row) {
            this.previewSrcList = row.files.split(',')
            //如果是图片类型就存到数组
            this.srcList = this.previewSrcList.filter(item => this.imgType.some(item1 => item.includes(item1)))
            this.previewVisible = true
        },
        //获取采购列表
        async getBrandList() {
            const { data, success } = await getAllProBrand()
            if (success) {
                this.brandList = data.map(item => {
                    return {
                        label: item.value,
                        value: item.key
                    }
                })
            } else {
                this.$message.error('获取采购人员列表失败')
            }
        },
        delImg(item, i) {
            this.RecordsInfo.picLists.splice(i, 1)
        },
        //指派确定按钮
        async assignmentSubmit() {
                const { success } = await setProviderDockingUser(this.assignmentInfo)
                if (success) {
                    //关闭弹窗
                    this.assignmentVisible = false
                    this.$message.success('指派成功')
                    //拉取已提交列表
                    this.getAlreadyList()
                }
            
        },
        changeAssignment(e) {
            //根据e找出procurementList相匹配的value
            this.assignmentInfo.brandName = this.procurementList.filter(item => item.value == e)[0].label
        },
        async ClickAssignment() {
            //清空数据
            this.assignmentInfo.recordIds = []
            this.assignmentInfo.brandId = null
            this.assignmentInfo.brandName = null
            this.assignmentInfo.recordIds = this.$refs.alreadyTable.$refs.xTable.getCheckboxRecords().filter(item => item.disParentId != 0 || item.children.length == 0).map(item => item.id)
            if (this.assignmentInfo.recordIds.length == 0) {
                this.$message.warning('请选择要分配的数据')
                return
            }
            const { data, success } = await getBrandUsers({ userName: null })
            if (success) {
                this.procurementList = data.map(item => {
                    return {
                        label: item.value,
                        value: item.key
                    }
                })
            } else {
                this.$message.error('获取采购人员列表失败')
            }
            this.assignmentVisible = true
        },
        //图片上传成功回调
        async handleSuccess({ data }) {
            this.RecordsInfo.picLists.push(data.url)
        },
        //点击供应商名称打开弹层
        async openNameDialog(row) {
            this.nameInfo.supplier = row.providerName ? row.providerName : row.supplier
            const { data, success } = await pagePurchaseOrderByProviderNameAsync(this.nameInfo)
            if (success) {
                this.nameTableData = data.list
                this.nameTotal = data.total
                console.log(data.summary,'data.summary');
                this.nameSummary = data.summary
                this.nameVisible = true
                this.nameInfo.orderBy = null
            } else {
                this.$message.error('获取供应商采购记录失败')
            }
        },
        //对接记录弹层每页数量改变
        nameSizechange(val) {
            this.nameInfo.currentPage = 1;
            this.nameInfo.pageSize = val;
            this.openNameDialog(this.nameInfo)
        },
        //对接记录弹层当前页改变
        namePagechange(val) {
            this.nameInfo.currentPage = val;
            this.openNameDialog(this.nameInfo)
        },
        async operateSubmit() {
            if (this.RecordsInfo.result == null || this.RecordsInfo.result == '') {
                this.$message.error('请输入内容')
                return
            }
            if (this.RecordsInfo.picLists.length == 0) {
                this.$message.error('请上传图片')
                return
            }
            if (this.RecordsInfo.dockingStatus == null || this.RecordsInfo.dockingStatus == '') {
                this.$message.error('请选择对接状态')
                return
            }
                this.RecordsInfo.newGoodsRecordId = null
                const { success } = await addProviderDockingResult(this.RecordsInfo)
                if (success) {
                    this.$message.success('操作成功')
                } else {
                    this.$message.error('操作失败')
                }
            this.getAlreadyList()
            this.operateVisible = false
        },
        //操作
        async operate(row) {
            this.RecordsInfo.picLists = []
            this.picFileList = []//清空图片上传列表
            this.RecordsInfo.result = null//清空对接结果
            this.RecordsInfo.dockingStatus = null//清空对接状态
            this.RecordsInfo.newGoodsRecordId = row.id
            // this.RecordsInfo.recordId = row.id
            this.RecordsInfo.providerQuotationRecordId = row.id
            this.RecordsInfo.openId = row.openId
            this.RecordsInfo.styleCode = row.styleCode
            this.operateVisible = true
        },
        //对接记录弹层每页数量改变
        dockingRecordsSizechange(val) {
            this.RecordsInfo.currentPage = 1;
            this.RecordsInfo.pageSize = val;
            this.dockingRecords(this.RecordsInfo)
        },
        //对接记录弹层当前页改变
        dockingRecordsPagechange(val) {
            this.RecordsInfo.currentPage = val;
            this.dockingRecords(this.RecordsInfo)
        },
        //已提交对接记录
        async dockingRecords(row) {
            this.RecordsInfo.picLists = []
            this.RecordsInfo.recordId = row.id
            this.RecordsInfo.id = row.id
            this.RecordsInfo.openId = row.openId
            this.RecordsInfo.styleCode = row.styleCode
            const { data, success } = await GetProviderDockingResultList(this.RecordsInfo)
            if (success) {
                this.doTableData = data.list
                this.recordsTotal = data.total
                this.RecordsInfo.orderBy = null
                this.RecordsVisible = true
            } else {
                this.$message.error('获取对接记录失败')
            }
        },
        //弹层页面数量改变
        logSizechange(val) {
            this.logDetail.currentPage = 1;
            this.logDetail.pageSize = val;
            this.openView(this.logDetail);
        },
        //弹层当前页改变
        logPagechange(val) {
            this.logDetail.currentPage = val;
            this.openView(this.logDetail);
        },
        handleClose() {
            this.dialogVisible = false
            this.RecordsVisible = false
            this.operateVisible = false
            this.nameVisible = false
            this.assignmentVisible = false
            this.previewVisible = false
        },
        handleClose1() {
            this.RecordsVisible = false
        },
        //打开弹层
        async openView(row) {
            this.logDetail.openId = row.openId
            this.logDetail.styleCode = row.styleCode
            const { data, success } = await getProviderQuotationHisRecordPageList(this.logDetail)
            if (success) {
                this.tableData1 = data.list
                this.logTotal = data.total
                this.dialogVisible = true
                this.logDetail.orderBy = null
            } else {
                this.$message.error('获取提交历史失败')
            }
        },
        //页面数量改变
        detailSizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getAlreadyList();
        },
        //当前页改变
        detailPagechange(val) {
            this.ListInfo.currentPage = val;
            this.getAlreadyList();
        },
        //公共清除
        clear() {
            this.ListInfo = {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                styleCode: null,//系列编码
                categoryId: null,//品类id
                openId: null,//openId
                goodCode: null,//商品编码
                isBY: null,//是否包邮
                isContaisTax: null,//是否含税
                providerName: null,//供应商名称
            }
        },
        searchList() {
            //清除styleCode,goodCode,providerName的空格
            this.ListInfo.styleCode = this.ListInfo.styleCode ? this.ListInfo.styleCode.replace(/\s+/g, "") : null;
            this.ListInfo.goodCode = this.ListInfo.goodCode ? this.ListInfo.goodCode.replace(/\s+/g, "") : null;
            this.ListInfo.providerName = this.ListInfo.providerName ? this.ListInfo.providerName.replace(/\s+/g, "") : null;
            this.getAlreadyList()
        },
        //获取供应商已提交列表
        async getAlreadyList() {
            const { data, success } = await getProviderQuotationRecordPageList(this.ListInfo);
            if (success) {
                this.tableData = data.list;
                this.detailTotal = data.total;
                this.listLoading = false;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getAlreadyList()
            }
        },
        sortchange1({ order, prop }) {
            if (prop) {
                this.logDetail.orderBy = prop
                this.logDetail.isAsc = order.indexOf("descending") == -1 ? true : false
                this.openView(this.logDetail)
            }
        },
        sortchange2({ order, prop }) {
            if (prop) {
                this.RecordsInfo.orderBy = prop
                this.RecordsInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.dockingRecords(this.RecordsInfo)
            }
        },
        sortchange3({ order, prop }) {
            if (prop) {
                if (prop == 'count') {
                    this.nameInfo.orderBy = 'totalCount'
                } else {
                    this.nameInfo.orderBy = prop
                }
                this.nameInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.openNameDialog(this.nameInfo)
            }
        }, 
        async onExport(opt) { 
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter, ...opt };
            var res = await exportProviderQuotationRecord(params);
            if (!res?.data) {
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '供应商报价-报价记录' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        async setExportCols(){
           await this.$refs.alreadyTable.setExportCols();
        }
    }
};
</script>

<style lang="scss" scoped>
.header {
    display: flex;
    margin-bottom: 10px;
}

.publicMargin {
    margin-right: 20px;
}

::v-deep .vxetoolbar20221212 {
    display: none !important;
}

::v-deep .el-badge__content {
    padding: 0 4px;
    top: 7px;
    right: 0;
}


.assignmentBox {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .btnBox {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
    }


}

.imageList_box {
    display: flex;
    flex-wrap: wrap;

    .imageList {
        position: relative;
        width: 100px;
        height: 100px;

        .imgcss ::v-deep img {
            min-width: 100px !important;
            min-height: 100px !important;
            width: 100px !important;
            height: 100px !important;
        }


        .del {
            position: absolute;
            top: 0;
            right: 0;
            font-size: 16px;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            color: black;
            text-align: center;
            line-height: 15px;
            cursor: pointer;
        }
    }
}

.already ::v-deep .vxe-tools--operate {
    display: block !important;
    position: absolute;
    top: -25px !important;
    left: -36px !important;
    padding-top: 0;
    padding-bottom: 0;
    z-index: 999;
    background-color: rgb(255 255 255 / 0%);
}

.doesn ::v-deep .vxe-tools--operate {
    display: block !important;
    position: absolute;
    top: -25px !important;
    left: -36px !important;
    padding-top: 0;
    padding-bottom: 0;
    z-index: 999;
    background-color: rgb(255 255 255 / 0%);
}

.fileCss {
    width: 100px;
    height: 100px;
    background-color: #ccc;
    text-align: center;
    line-height: 100px;
    color: #000;
}

.previewBox {
    display: flex;
    flex-wrap: wrap;
}
</style>
