<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' style="height:93%;"  :isIndex='true'
                :hasexpand='false' @sortchange='sortchange' :tableData='inquirsstatisticslist' @select='selectchange'
                :isSelection='false' :tableCols='tableCols' :loading="listLoading">
                <template slot='extentbtn'>
                    <el-input v-model="filter.name" v-model.trim="filter.name" placeholder="姓名" style="width:120px;"
                        disabled="true" :maxlength="50" />
                    <el-input v-model="filter.timeStart" style="width:120px;" disabled="true" />至
                    <el-input v-model="filter.timeEnd" style="width:120px;" disabled="true" />
                </template>
            </ces-table>
        </template>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

import {
  getOSPddShopPersonalEfficiency
} from "@/api/customerservice/waibao";

const tableCols = [
    { istrue: true, prop: 'shopName', label: '店铺名称', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', width: '80', sortable: 'custom' },
     { istrue: true, prop: 'inquirs', label: '咨询人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipscount', label: '询单人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'receivecount', label: '最终成团人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'successpayRate', label: '询单转化率', width: '80', sortable: 'custom', formatter: (row) => (row.successpayRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'threeSecondReplyRate', label: '3分钟人工回复率', width: '80', sortable: 'custom', formatter: (row) => (row.threeSecondReplyRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'responseTime', label: '均响(秒)', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'thirtySecondResponseRate', label: '30秒应答率', width: '80', sortable: 'custom',formatter: (row) => (row.thirtySecondResponseRate * 100).toFixed(2) + "%"  },
    // { istrue: true, prop: 'outTimes', label: '出勤人次', width: '80', sortable: 'custom' },
    // { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'inquirRate', label: '询单占比', width: '80', sortable: 'custom',formatter: (row) => (row.inquirRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'inquirValue', label: '询单价值（元）', width: '120', sortable: 'custom', formatter: (row) => row.inquirValue.toFixed(2) },
    { istrue: true, prop: 'salesvol', label: '客服销售额(元)', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'serviceScore', label: '客服服务分', width: '100', sortable: 'custom'},
    { istrue: true, prop: 'lowRatingOrderCount', label: '评分<=3订单数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'disputeRefundCount', label: '纠纷退款数', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'complainCount', label: '投诉数', width: '80', sortable: 'custom' },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, datepicker, cesTable },
    data() {
        return {
            that: this,
            filter: {
                name: "",
                sdate: [],
                timeEnd: "",
                timeStart: ""
            },
            shopList: [],
            userList: [],
            groupList: [],
            inquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "shopName", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
        };
    },
    async mounted() {

    },
    created() {

    },
    methods: {
        async dialogOpenAfter(data) {
            this.filter.sdate[0] = data.startDate;
            this.filter.sdate[1] = data.endDate;
            this.filter.timeStart = data.startDate;
            this.filter.timeEnd = data.endDate;
            this.filter.name = data.name;
            this.onSearch();
        },
        onSearch() {
            this.getinquirsstatisticsList();
        },
        async getinquirsstatisticsList() {
            const para = { ...this.filter };
            const params = {
                ...this.pager,
                ...para,
            };
            this.listLoading = true;
            const res = await getOSPddShopPersonalEfficiency(params);
            this.listLoading = false;
            this.total = res.data.total
            this.inquirsstatisticslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
