<template>
    <my-container v-loading="pageLoading">
        <el-tabs tab-position="left" v-model="tabname" :before-leave="beforeLeave">
            <el-tab-pane label="包装材料" name="one">
                <el-main>
                    <el-col :span="24" style="">
                        <div style="margin-bottom:5px; height: 82vh; overflow-y: hidden;" class="draggable-table">
                            <div style="height: 40px;">
                                <el-button type="primary" @click="onAdd(14, '新增')">新增</el-button>
                                <el-button type="primary" @click="getDataSetList(14)">刷新</el-button>
                                <el-button type="primary" @click="saveOrder(14)">保存排序</el-button>
                            </div>
                            <vxe-table
                            :show-header="false"
                            :align="allAlign"
                            height="92%"
                            :data="packingMaterialList">
                            <!-- <vxe-column field="orderNum" width="60"></vxe-column> -->
                            <vxe-column field="sceneCode" title="Name"></vxe-column>
                            <vxe-column field="age" title="操作">
                                <template #default="{row}">
                                    <el-button type="primary" @click="onEdit(row,14, '编辑包装类型')" >编辑</el-button>
                                    <el-button type="danger"  @click="onDelete(row)">删除</el-button>
                                </template>
                            </vxe-column>
                            </vxe-table>
                            <div style="font-size: 14px; color: #606266;">共：{{packingMaterialList?.length}} 条</div>
                        </div>
                    </el-col>
                </el-main>
            </el-tab-pane>
            <el-tab-pane label="品牌设置" name="two">
                <el-main>
                    <el-col :span="24">
                        <div style="margin-bottom:5px; height: 82vh; overflow-y: hidden;" class="draggable-table2">
                            <div style="height: 40px;">
                                <el-button type="primary" @click="onAdd(15, '新增')">新增</el-button>
                                <el-button type="primary" @click="getDataSetList(15)">刷新</el-button>
                                <el-button type="primary" @click="saveOrder(15)">保存排序</el-button>
                            </div>

                            <vxe-table
                            :show-header="false"
                            :align="allAlign"
                            height="92%"
                            :data="brandList">
                            <vxe-column field="setId" width="60" v-if="false"></vxe-column>
                            <vxe-column field="sceneCode" title="Name"></vxe-column>
                            <vxe-column field="age" title="操作">
                                <template #default="{row}">
                                    <el-button type="primary" @click="onEdit(row,15, '编辑包装类型')" >编辑</el-button>
                                    <el-button type="danger"  @click="onDelete(row)">删除</el-button>
                                </template>
                            </vxe-column>
                            </vxe-table>
                            <div style="font-size: 14px; color: #606266;">共：{{brandList?.length}} 条</div>
                        </div>
                    </el-col>
                </el-main>
            </el-tab-pane>
            <el-tab-pane label="机型设置" name="three">
                <el-main>
                    <el-col :span="24" style="">
                        <div style="margin-bottom:5px; height: 82vh; overflow-y: hidden;" class="draggable-table3">
                            <div style="height: 40px;">
                                <el-button type="primary" @click="onAdd(16, '新增')">新增</el-button>
                                <el-button type="primary" @click="getDataSetList(16)">刷新</el-button>
                                <el-button type="primary" @click="saveOrder(16)">保存排序</el-button>
                            </div>

                            <vxe-table
                            :show-header="false"
                            :align="allAlign"
                            height="92%"
                            :data="machineTypeList">
                            <vxe-column field="setId" width="60" v-if="false"></vxe-column>
                            <vxe-column field="sceneCode" title="Name"></vxe-column>
                            <vxe-column field="age" title="操作">
                                <template #default="{row}">
                                    <el-button type="primary" @click="onEdit(row,16, '编辑包装类型')">编辑</el-button>
                                    <el-button type="danger"  @click="onDelete(row)">删除</el-button>
                                </template>
                            </vxe-column>
                            </vxe-table>
                            <div style="font-size: 14px; color: #606266;">共：{{machineTypeList?.length}} 条</div>
                        </div>
                    </el-col>
                </el-main>
            </el-tab-pane>
            <el-tab-pane label="类型设置" name="nine">
                <el-main>
                    <el-col :span="24" style="">
                        <div style="margin-bottom:5px; height: 82vh; overflow-y: hidden;" class="draggable-table12">
                            <div style="height: 40px;">
                                <el-button type="primary" @click="onAdd(12, '新增类型设置')">新增</el-button>
                                <el-button type="primary" @click="getDataSetList(12)">刷新</el-button>
                                <el-button type="primary" @click="saveOrder(12)">保存排序</el-button>
                            </div>

                            <vxe-table
                            :show-header="false"
                            :align="allAlign"
                            height="92%"
                            :data="typeSettingList">
                            <vxe-column field="setId" width="60" v-if="false"></vxe-column>
                            <vxe-column field="sceneCode" title="Name"></vxe-column>
                            <vxe-column field="age" title="操作">
                                <template #default="{row}">
                                    <el-button type="primary"  @click="onEdit(row,12, '编辑类型设置')">编辑</el-button>
                                    <el-button type="danger" @click="onDelete(row)">删除</el-button>
                                </template>
                            </vxe-column>
                            </vxe-table>
                            <div style="font-size: 14px; color: #606266;">共：{{typeSettingList?.length}} 条</div>
                        </div>
                    </el-col>
                </el-main>
            </el-tab-pane>
            <el-tab-pane label="包装尺寸" name="four">
                <el-main>
                    <el-col :span="24" style="">
                        <div style="margin-bottom:5px; height: 82vh; overflow-y: hidden;" class="draggable-table4">
                            <div style="height: 40px;">
                                <el-button type="primary" @click="onAdd(17, '新增')">新增</el-button>
                                <el-button type="primary" @click="getDataSetList(17)">刷新</el-button>
                                <el-button type="primary" @click="saveOrder(17)">保存排序</el-button>
                            </div>

                            <vxe-table
                            :show-header="false"
                            :align="allAlign"
                            height="92%"
                            :data="packageSizeList">
                            <vxe-column field="setId" width="60" v-if="false"></vxe-column>
                            <vxe-column field="sceneCode" title="Name"></vxe-column>
                            <vxe-column field="age" title="操作">
                                <template #default="{row}">
                                    <el-button type="primary"  @click="onEdit(row,17, '编辑包装类型')">编辑</el-button>
                                    <el-button type="danger" @click="onDelete(row)">删除</el-button>
                                </template>
                            </vxe-column>
                            </vxe-table>
                            <div style="font-size: 14px; color: #606266;">共：{{packageSizeList?.length}} 条</div>
                        </div>
                    </el-col>
                </el-main>
            </el-tab-pane>
            <el-tab-pane label="工价模板" name="five">
                <el-main>
                    <el-col :span="24" style="">
                        <div style="height: 82vh; ">
                            <pricelist ref="refpricelist" :machineTypeList="machineTypeList" @getPriceTemplateListfuc="getPriceTemplateListfuc"></pricelist>
                        </div>
                        <div style="margin-top: -30px;">
                            <my-pagination :sizes="[50, 100, 200]" :page-size="50" ref="pager" :total="total" :checked-count="sels.length" @page-change="getPriceTemplateListfuc" @size-change="sizechange" />
                        </div>
                    </el-col>
                </el-main>
            </el-tab-pane>
            <el-tab-pane label="公司类型" name="eight">
                <el-main>
                    <el-col :span="24" style="">
                        <div style="margin-bottom:5px; height: 82vh; overflow-y: hidden;" class="draggable-table6">
                            <div style="height: 40px;">
                                <el-button type="primary" @click="onAdd(2, '新增公司类型')">新增</el-button>
                                <el-button type="primary" @click="getDataSetList(2)">刷新</el-button>
                                <el-button type="primary" @click="saveOrder(2)">保存排序</el-button>
                            </div>

                            <vxe-table
                            :show-header="false"
                            :align="allAlign"
                            height="92%"
                            :data="companyList">
                            <vxe-column field="setId" width="60" v-if="false"></vxe-column>
                            <vxe-column field="sceneCode" title="Name"></vxe-column>
                            <vxe-column field="age" title="操作">
                                <template #default="{row}">
                                    <el-button type="primary"  @click="onEdit(row,2, '编辑公司类型')">编辑</el-button>
                                    <el-button type="danger" @click="onDelete(row)">删除</el-button>
                                </template>
                            </vxe-column>
                            </vxe-table>
                            <div style="font-size: 14px; color: #606266;">共：{{companyList?.length}} 条</div>
                        </div>
                    </el-col>
                </el-main>
            </el-tab-pane>
            <el-tab-pane label="调配工价" name="six">
                <el-main>
                    <el-col :span="24" style="">
                        <div style="height: 82vh; overflow-y: hidden;">
                            <redeploymentRate ref="refredeploymentRate"></redeploymentRate>
                        </div>
                    </el-col>
                </el-main>
            </el-tab-pane>
            <el-tab-pane label="其他员工" name="seven">
                <el-main>
                    <el-col :span="24" style="">
                        <div style="height: 82vh; overflow-y: hidden;">
                            <otheremployeeslist ref="refotheremployeeslist"  :machineTypeList="machineTypeList" @getOtheremployeeslisfuc="getOtheremployeeslisfuc"></otheremployeeslist>
                        </div>
                    </el-col>
                </el-main>
            </el-tab-pane>
            <!-- <el-tab-pane label="员工设置">
                <el-main>
                    <el-col :span="24" style="">
                        <div style="height: 80vh; overflow-y: hidden;">
                            <peopleset ref="refpeopleset" :machineTypeList="machineTypeList" @getPriceTemplateListfuc="getPriceTemplateListfuc"></peopleset>
                        </div>
                    </el-col>
                </el-main>
            </el-tab-pane> -->
        </el-tabs>

        <vxe-modal v-model="moadlshow" :title="formTitle">
            <template #default>
                <!-- <vxe-input v-model="addForm.sceneId"  v-if="false"></vxe-input> -->
                <vxe-input v-model="addForm.sceneCode" placeholder="请输入名称" size="medium"  @blur="slichange(addForm.sceneCode,addForm.setType)" :maxlength="20"></vxe-input>
                <div class="qualibtn" style="width: 100%;display: flex; flex-direction: column;">
                    <div class="flexrow">
                        <div class="marginrt">
                            <el-button width="200px" size="medium" @click="closemodel">取消</el-button>
                        </div>
                        <el-button width="200px" size="medium" type="primary" @click="onAddSave">保存</el-button>
                    </div>
                </div>
            </template>
        </vxe-modal>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import { getShootingSetDataById, getShootingSetData, saveShootingSet, deleteShootingSet, saveDataOrderListDataAsync } from '@/api/media/shootingset'
import {getPackagesSetData,savePackagesSet, saveDataOrderListDataAsync as saveDataOrderPackagesAsync,deletePackagesSet} from '@/api/inventory/packagesSetProcessing';
import { getPackagesSetData as getPackagesSetDataById , savePackagesSet as savePackagesSetById, deletePackagesSet as deletePackagesSetById, saveDataOrderListDataAsync as saveDataOrderPackagesById} from '@/api/inventory/packagesSetProcessing.js';
import { getPriceTemplateList,getPackagesOtherEmployees} from '@/api/inventory/packagesprocess';//包装加工
import Sortable from 'sortablejs'
import pricelist from '@/views/media/packagework/pricelist.vue'
import otheremployeeslist from '@/views/media/packagework/otheremployees.vue'
import redeploymentRate from '@/views/media/packagework/redeploymentRate.vue'
import { filter } from "xe-utils";
// import peopleset from '@/views/media/packagework/peopleset.vue'
export default {
    name: 'MainPackageset',
    components: {MyContainer, pricelist,otheremployeeslist,redeploymentRate},
    data() {
        return {
            a: 0,
            tabname: 'one',
            allAlign: null,
            listLoading: false,
            issave: true,
            formTitle: "新增",
            pageLoading: false,//树列表加载
            addFormVisiblerole: false,//新增编辑显隐
            editFormLoading: false,//编辑时转圈
            addLoading: false,//新增编辑提交按钮
            brandList: [],//品牌列表数据集
            packingMaterialList: [],//包装方式列表数据集
            typeSettingList: [],//类型设置
            machineTypeList:[],//机型列表数据集
            packageSizeList:[],//尺寸列表数据集
            qualityPriceList:[],//调配工价数据集
            companyList:[],//公司数据集
            isEdit: false,//是否编辑模式
            addForm: {
                sceneId: 0,
                sceneCode: null,
                OrderNum: 0,
                sceneName: '',
                setType: 0,
            },
            addFormRules: {
                sceneCode: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
            moadlshow: false,
            total: 0,
            sels: [],
            pager: { orderBy:'',isAsc: true},
            filter:{
              currentPage:1,
              pageSize:50,
            },
        };
    },
    async created() {
        await this.getDataSetList(14);
        await this.getDataSetList(15);
        await this.getDataSetList(16);
        await this.getDataSetList(17);
        await this.getDataSetList(1);
        await this.getDataSetList(2);
        await this.getDataSetList(12);
        await this.getPriceTemplateListfuc();
        await this.getOtheremployeeslisfuc();
    },

    mounted() {
        this.rowDrop();
        this.rowDrop2();
        this.rowDrop3();
        this.rowDrop4();
        this.rowDrop6();
        this.rowDrop12();
    },
    watch: {
        "packingMaterialList":"reffuc",
        "brandList":"reffuc",
        "machineTypeList": "reffuc",
        "packageSizeList": "reffuc",
        "qualityPriceList": "reffuc",
        "companyList": "reffuc",
        "typeSettingList": "reffuc"
    },

    methods: {
     slichange(value,settype){
        if(settype==1)
        {
            var num = Number(value.toString().match(/^\d+(?:\.\d{0,5})?/));
            this.addForm.sceneCode=num;
        }
       },
        reffuc(){
            this.a = this.a +1;
            if(this.a>6){
                this.issave = false;
            }
        },
        beforeLeave(activeName, oldActiveName){
            if(!this.issave){
                this.$message({ type: 'info', message: '请先保存排序顺序' });
                return false;
            }
            return true;
        },
        //工价模板分页查询
        async onRefresh() {
            // this.pageLoading = true;
            this.$refs.refpricelist.tableloading = true;
            await this.getPriceTemplateListfuc();
        },
        async getPriceTemplateListfuc(data,val){
            const params = {
            ...this.pager,
            ...this.filter,
            ...data
          }
          console.log("params",params)
            const res = await getPriceTemplateList(params);
            if (!res?.success) {
                return
            }
            console.log("res",res)
            this.total = res.data.total;
            const list = res.data.list;
            this.$refs.refpricelist.updatelistt1(list);
        },
        sizechange(val){
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.onRefresh();
        },
        pagechange(val){
            this.filter.currentPage = val;
            this.onRefresh();
        },
        async getOtheremployeeslisfuc(){
            const res = await getPackagesOtherEmployees();
            if (!res?.success) {
                return
            }
            console.log("1111",res)
            this.$refs.refotheremployeeslist.updatelistt(res);
        },
        // getlist(e){
        //     this.$refs.refpricelist.updatelistt(e);
        // },
        //保存排序
        async saveOrder(index) {
            this.listLoading = true;
            switch (index) {
                case 14:
                    var params = this.packingMaterialList;
                    break;
                case 15:
                    var params = this.brandList;
                    break;
                case 16:
                    var params =  this.machineTypeList;
                    break;
                case 17:
                    var params =  this.packageSizeList;
                    break;
                case 2:
                    var params =  this.companyList;
                    break;
                case 12:
                    var params =  this.typeSettingList;
                    break;
            }
            if(index==1 || index==2)
            {
                var res = await saveDataOrderPackagesAsync(params);
                if (res?.success)
                this.$message({ message: this.$t('操作成功'), type: 'success' });
            }else if(index==12){
                var res = await saveDataOrderPackagesById(params);{
                    if (res?.success)
                    this.$message({ message: this.$t('操作成功'), type: 'success' });
                }
            }else
            {
                var res = await saveDataOrderListDataAsync(params);
                if (res?.success)
                this.$message({ message: this.$t('操作成功'), type: 'success' });
            }

            this.listLoading = false;
            this.issave = true;
        },
        rowDrop() {
            const tbody = document.querySelector('.draggable-table .vxe-table--body-wrapper tbody')
            const _this = this
            Sortable.create(tbody, {
                onEnd({ newIndex, oldIndex }) {
                    if (newIndex == oldIndex) return
                    _this.packingMaterialList.splice(
                        newIndex,
                        0,
                        _this.packingMaterialList.splice(oldIndex, 1)[0]
                    )
                    var newArray = _this.packingMaterialList.slice(0)
                    _this.packingMaterialList = []
                    _this.$nextTick(function () {
                        _this.packingMaterialList = newArray
                    })
                }
            })
        },
        rowDrop2() {
            const tbody = document.querySelector('.draggable-table2 .vxe-table--body-wrapper tbody')
            const _this = this
            Sortable.create(tbody, {
                onEnd({ newIndex, oldIndex }) {
                    if (newIndex == oldIndex) return
                    _this.brandList.splice(
                        newIndex,
                        0,
                        _this.brandList.splice(oldIndex, 1)[0]
                    )
                    var newArray = _this.brandList.slice(0)
                    _this.brandList = []
                    _this.$nextTick(function () {
                        _this.brandList = newArray
                    })
                }
            })
        },
        rowDrop3() {
            const tbody = document.querySelector('.draggable-table3 .vxe-table--body-wrapper tbody')
            const _this = this
            Sortable.create(tbody, {
                onEnd({ newIndex, oldIndex }) {
                    if (newIndex == oldIndex) return
                    _this.machineTypeList.splice(
                        newIndex,
                        0,
                        _this.machineTypeList.splice(oldIndex, 1)[0]
                    )
                    var newArray = _this.machineTypeList.slice(0)
                    _this.machineTypeList = []
                    _this.$nextTick(function () {
                        _this.machineTypeList = newArray
                    })
                }
            })
        },
        rowDrop4() {
            const tbody = document.querySelector('.draggable-table4 .vxe-table--body-wrapper tbody')
            const _this = this
            Sortable.create(tbody, {
                onEnd({ newIndex, oldIndex }) {
                    if (newIndex == oldIndex) return
                    _this.packageSizeList.splice(
                        newIndex,
                        0,
                        _this.packageSizeList.splice(oldIndex, 1)[0]
                    )
                    var newArray = _this.packageSizeList.slice(0)
                    _this.packageSizeList = []
                    _this.$nextTick(function () {
                        _this.packageSizeList = newArray
                    })
                }
            })
        },
        rowDrop6() {
            const tbody = document.querySelector('.draggable-table6 .vxe-table--body-wrapper tbody')
            const _this = this
            Sortable.create(tbody, {
                onEnd({ newIndex, oldIndex }) {
                    if (newIndex == oldIndex) return
                    _this.companyList.splice(
                        newIndex,
                        0,
                        _this.companyList.splice(oldIndex, 1)[0]
                    )
                    var newArray = _this.companyList.slice(0)
                    _this.companyList = []
                    _this.$nextTick(function () {
                        _this.companyList = newArray
                    })
                }
            })

        },
        rowDrop12() {
            const tbody = document.querySelector('.draggable-table12 .vxe-table--body-wrapper tbody')
            const _this = this
            Sortable.create(tbody, {
                onEnd({ newIndex, oldIndex }) {
                    if (newIndex == oldIndex) return
                    _this.typeSettingList.splice(
                        newIndex,
                        0,
                        _this.typeSettingList.splice(oldIndex, 1)[0]
                    )
                    var newArray = _this.typeSettingList.slice(0)
                    _this.typeSettingList = []
                    _this.$nextTick(function () {
                        _this.typeSettingList = newArray
                    })
                }
            })
        },
        onAdd(index,title){
            this.formTitle = title;
            this.addForm.sceneCode = "";
            this.addForm.setType = index;
            this.addForm.setId = 0;
            this.moadlshow =true;
        }
        ,async getDataSetList(index) { //14包装加工-品牌，15包装加工-包装方式，16包装加工-机型，17包装加工-尺寸,12类型设置
            this.listLoading = true;
            if(index==1 ||  index==2)
            {
                const res = await getPackagesSetData({ setType: index });
                if (!res?.success) {
                    return
                }
                switch (index) {
                    case 1:
                        this.qualityPriceList=res?.data;
                        break;
                    case 2:
                        this.companyList=res?.data;
                };

            }else if(index == 12){
              const {data,success} = await getPackagesSetDataById({ setType: index })
                if (success) {
                  this.typeSettingList = data;
                }
            }else
            {
                const res = await getShootingSetData({ setType: index });
                if (!res?.success) {
                    return
                }
                switch (index) {
                    case 14:
                        this.packingMaterialList = res?.data?.data;
                        break;
                    case 15:
                        this.brandList = res?.data?.data;
                        break;
                    case 16:
                        this.machineTypeList = res?.data?.data;
                        break;
                    case 17:
                        this.packageSizeList = res?.data?.data;
                        break;
                }
            }
            this.listLoading = false;
            this.$nextTick(()=>{
                this.issave = true;
            })
        },

        closemodel(){
            this.moadlshow = false;
        },
        async onAddSave() {
                this.addLoading = true;
                const para = {
                    ...this.addForm,
                }
                if(this.addForm.setType==1 || this.addForm.setType==2)
                {
                    var res = await savePackagesSet(para);
                    if (!res?.success) {
                        return;
                    }
                }else if( this.addForm.setType==12 ){
                    var res = await savePackagesSetById(para);
                    if (!res?.success) {
                        return;
                    }
                }else
                {
                    var res = await saveShootingSet(para);
                    if (!res?.success) {
                        return;
                    }
                }

                this.$message({message: '保存成功！',type: 'success'});

                await this.getDataSetList(this.addForm.setType);
                await this.$emit("getPriceTemplateListfuc");
                await this.getOtheremployeeslisfuc();
                // this.$refs['addForm'].resetFields();
                this.moadlshow = false;
                this.issave = true;
            }
            ,async onEdit(row,type, title) {
                this.moadlshow = true;
                this.formTitle = title;
                this.addForm.sceneCode = row.sceneCode;
                this.addForm.setType = row.setType;
                this.addForm.setId = row.setId;
            }
            ,
            async onDelete(row) {
                this.$confirm('确认删除, 是否继续?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    if(row.setType==2)
                    {
                        const res = await deletePackagesSet({ setId: row.setId });
                        if (res?.success) {
                            this.$message({ type: 'success', message: '删除成功!' });
                            await this.getDataSetList(row.setType);
                            await this.$emit("getOtheremployeeslisfuc")
                        }
                   }else if(row.setType==12){
                      const {data,success} = await deletePackagesSetById({ setId: row.setId });
                      if (success) {
                          this.$message({ type: 'success', message: '删除成功!' });
                          await this.getDataSetList(row.setType);
                        }
                   }else
                   {
                       const res = await deleteShootingSet({ setId: row.setId });
                        if (res?.success) {
                            this.$message({ type: 'success', message: '删除成功!' });
                            await this.getDataSetList(row.setType);
                        }
                   }
                }).catch(() => {
                    this.$message({ type: 'info', message: '已取消删除' });
                });
            }
    },
};
</script>

<style lang="scss" scoped>
    .flexrow{
    display: flex;
    flex-direction: row;
    align-items: center;
   }

   .qualibtn ::v-deep .el-button{
    margin-left: 0 !important;
    margin-top: 10px;
   }

   .marginrt{
    margin: 0 10px 0 auto;
   }
</style>
