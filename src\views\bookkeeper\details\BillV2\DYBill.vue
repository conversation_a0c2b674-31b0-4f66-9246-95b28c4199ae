<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="时间">
                    <el-date-picker style="width: 249px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" :clearable="false"
                        end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <!-- <el-form-item label="平台:">
                    <el-select filterable v-model="filter.platform" placeholder="平台" @change="onchangeplatform" clearable style="width: 80px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                </el-form-item> -->
                <el-form-item label="线上单号:">
                    <!-- <el-input v-model.trim="filter.ProCode" :clearable="true" maxlength="20" placeholder="线上单号" style="width:130px;"/> -->
                    <inputYunhan ref="productCode2" :inputt.sync="filter.OrderNo" v-model="filter.OrderNo" width="115px"
                        class="publicCss" placeholder="线上单号/多条请按回车" :clearable="true" :clearabletext="true"
                        :maxRows="2000" :maxlength="600000" @callback="orderNoInnerBack" title="线上单号">
                    </inputYunhan>
                </el-form-item>
                <el-form-item label="店铺款式编码:">
                    <el-input v-model="filter.ProCode" :clearable="true" maxlength="20" placeholder="店铺款式编码" style="width:130px;"/>
                </el-form-item>
                <el-form-item label="店铺:">
                    <YhShopSelector :names="['所有店铺']" :values="[-1]"  platform="6" :checkboxOrRadio="'checkbox'" :addAllWarehouseConcept="true" @onChange="(v)=>{filter.shopCode=v[0].join(','); }">
                    </YhShopSelector>
                </el-form-item>
                <el-form-item label="账单项目:">
                    <el-select filterable v-model="accountTypeist" placeholder="请选择账单项目"  multiple  clearable style="width: 130px">
                        <el-option label="银行补贴" value="银行补贴" />
                        <el-option label="拦截费" value="拦截费" />
                        <el-option label="保费扣除" value="保费扣除" />
                        <el-option label="公益捐款" value="公益捐款" />
                        <el-option label="商家赔付" value="商家赔付" />
                        <el-option label="预扣费" value="预扣费" />
                        <el-option label="配送费" value="配送费" />
                        <el-option label="操作费" value="操作费" />
                        <el-option label="打款" value="打款" />
                        <el-option label="评价有礼活动资金回退" value="评价有礼活动资金回退" />
                        <el-option label="达人责任赔付" value="达人责任赔付" />
                        <el-option label="充值" value="充值" />
                        <el-option label="实际平台补贴_运费" value="实际平台补贴_运费" />
                        <el-option label="实际平台补贴" value="实际平台补贴" />
                        <el-option label="以旧换新抵扣" value="以旧换新抵扣" />
                        <el-option label="实际达人补贴" value="实际达人补贴" />
                        <el-option label="实际抖音支付补贴" value="实际抖音支付补贴" />
                        <el-option label="实际抖音月付营销补贴" value="实际抖音月付营销补贴" />
                        <el-option label="服务商佣金" value="服务商佣金" />
                        <el-option label="渠道分成" value="渠道分成" />
                        <el-option label="招商服务费" value="招商服务费" />
                        <el-option label="其他分成" value="其他分成" />
                        <el-option label="站外推广费" value="站外推广费" />
                        <el-option label="佣金" value="佣金" />
                        <el-option label="订单退款" value="订单退款" />
                        <el-option label="平台服务费" value="平台服务费" />
                        <el-option label="订单实付" value="订单实付" />
                        <el-option label="国内虚假发货发运超时" value="国内虚假发货发运超时" />
                        <el-option label="小额打款" value="小额打款" />
                        <el-option label="商家开票" value="商家开票" />
                        <el-option label="返佣订单" value="返佣订单" />
                        <el-option label="提现" value="提现" />
                        <el-option label="评价有礼保证金" value="评价有礼保证金" />
                        <el-option label="普通退款转赔付服务打款" value="普通退款转赔付服务打款" />
                        <el-option label="极速退款分账" value="极速退款分账" />
                        <el-option label="货款结算入账" value="货款结算入账" />
                        <el-option label="普通退款转赔付服务打款" value="普通退款转赔付服务打款" />
                        <el-option label="极速退款分账" value="极速退款分账" />
                        <el-option label="退款-结算后退款-退用户" value="退款-结算后退款-退用户" />
                        <el-option label="退款-退转付扣减商家货款" value="退款-退转付扣减商家货款" />
                        <el-option label="消费者赔付" value="消费者赔付" />
                        <el-option label="退款-订单退款触发-退分账" value="退款-订单退款触发-退分账" />
                        <el-option label="上门取件运费" value="上门取件运费" />
                        <el-option label="退款-订单退款触发-分账" value="退款-订单退款触发-分账" />
                        <el-option label="未处退款-极速退二阶段商家资金回补理" value="退款-极速退二阶段商家资金回补" />
                        <el-option label="其他违规" value="其他违规" />
                        <el-option label="追缴" value="追缴" />
                        <el-option label="退款失败" value="退款失败" />
                          <el-option label="未处理" value="未处理" />
                    </el-select>
                </el-form-item>

                <el-form-item label="ERP账务类型:">
                    <el-select filterable  v-model="BillTypeList" placeholder="请选择ERP账务类型"  multiple collapse-tags clearable style="width: 155px">
                        <el-option label="银行补贴" value="银行补贴" />
                        <el-option label="拦截费" value="拦截费" />
                        <el-option label="评价有礼活动资金回退" value="评价有礼活动资金回退" />
                        <el-option label="达人责任赔付" value="达人责任赔付" />
                        <el-option label="充值" value="充值" />
                        <el-option label="商家赔付" value="商家赔付" />
                        <el-option label="预扣费" value="预扣费" />
                        <el-option label="配送费" value="配送费" />
                        <el-option label="操作费" value="操作费" />
                        <el-option label="打款" value="打款" />
                        <el-option label="实际平台补贴_运费" value="实际平台补贴_运费" />
                        <el-option label="实际平台补贴" value="实际平台补贴" />
                        <el-option label="以旧换新抵扣" value="以旧换新抵扣" />
                        <el-option label="实际达人补贴" value="实际达人补贴" />
                        <el-option label="实际抖音支付补贴" value="实际抖音支付补贴" />
                        <el-option label="实际抖音月付营销补贴" value="实际抖音月付营销补贴" />
                        <el-option label="服务商佣金" value="服务商佣金" />
                        <el-option label="渠道分成" value="渠道分成" />
                        <el-option label="招商服务费" value="招商服务费" />
                        <el-option label="其他分成" value="其他分成" />
                        <el-option label="站外推广费" value="站外推广费" />
                        <el-option label="佣金" value="佣金" />
                        <el-option label="订单退款" value="订单退款" />
                        <el-option label="平台服务费" value="平台服务费" />
                        <el-option label="订单实付" value="订单实付" />
                        <el-option label="国内虚假发货发运超时" value="国内虚假发货发运超时" />
                        <el-option label="小额打款" value="小额打款" />
                        <el-option label="商家开票" value="商家开票" />
                        <el-option label="返佣订单" value="返佣订单" />
                        <el-option label="提现" value="提现" />
                        <el-option label="评价有礼保证金" value="评价有礼保证金" />
                        <el-option label="普通退款转赔付服务打款" value="普通退款转赔付服务打款" />
                        <el-option label="极速退款分账" value="极速退款分账" />
                        <el-option label="货款结算入账" value="货款结算入账" />
                        <el-option label="退款-结算后退款-退用户" value="退款-结算后退款-退用户" />
                        <el-option label="退款-退转付扣减商家货款" value="退款-退转付扣减商家货款" />
                        <el-option label="消费者赔付" value="消费者赔付" />
                        <el-option label="退款-订单退款触发-退分账" value="退款-订单退款触发-退分账" />
                        <el-option label="上门取件运费" value="上门取件运费" />
                        <el-option label="退款-订单退款触发-分账" value="退款-订单退款触发-分账" />
                        <el-option label="未处退款-极速退二阶段商家资金回补理" value="退款-极速退二阶段商家资金回补" />
                        <el-option label="无" value="无" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                <el-switch v-model="filter.isGroup"  active-color="#67C23A"  inactive-color="#409EFF"
                    @change="onSearch"   active-text="汇总查询"  active-value="1" inactive-value="0"  inactive-text="全量查询">
                    </el-switch>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
            @select="selectchange" :tableHandles='tableHandles' @cellclick="cellclick" :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" :close-on-click-modal="false" width="40%" v-dialogDrag>
            <el-row>
                <el-col :xs="4" :sm="6" :md="8" :lg="6">
                    <el-date-picker style="width: 100%" v-model="importfilter.YearMonthDay" type="date" format="yyyyMMdd"
                    value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
                  </el-col>
                <el-col :xs="4" :sm="6" :md="8" :lg="6" >
                    <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="4" action
                        accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove"
                        :file-list="fileList">
                        <template #trigger>
                            <el-button size="small" type="primary">选取文件</el-button>
                        </template>
                        <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                            @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
                    </el-upload>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { platformlist} from '@/utils/tools'
import { formatTime, formatTime1 } from "@/utils";
import { getList as getshopList } from '@/api/operatemanage/base/shop'
//import { getBillFeePageList } from '@/api/bookkeeper/financialDetail'
import {getNewDYBillingCharge,exportNewDYBillingChargeList} from '@/api/bookkeeper/reportdayV2'
import inputYunhan from "@/components/Comm/inputYunhan";
import YhShopSelector from "@/components/YhCom/YhShopSelector";

const tableCols = [
    { istrue: true, prop: 'yearMonthDay', label: '日期', tipmesg: '', width: '80', sortable: 'custom', },
    { istrue: true, prop: 'recordId', label: '记录编号', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'proCode', label: '店铺款式编码', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'shopCode', label: '店铺名', sortable: 'custom',  tipmesg: '', width: '100', formatter: (row) => !row.shopName ? " " : row.shopName },
    { istrue: true, prop: 'billingItem', label: '账单项目', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'billType', label: 'ERP账务类型', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'amountIncome', label: '金额', tipmesg: '收入金额-支出金额', width: '80', sortable: 'custom', },
    { istrue: true, prop: 'transactionNumber', label: '动账流水号', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'orderNumber', label: '订单号', tipmesg: '', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'orderNo', label: '线上单号', tipmesg: '', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'subOrderNumber', label: '子订单号', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'transactionSummary', label: '动账摘要', tipmesg: '', width: '150', sortable: 'custom', },
    { istrue: true, prop: 'settlementTime', label: '结算时间', tipmesg: '', width: '160', sortable: 'custom', },
    { istrue: true, prop: 'remark', label: '备注', tipmesg: '', sortable: 'custom', },
]

const tableHandles = [
    // { label: "导入", handle: (that) => that.startImport() },
];

const startTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const curMonth = formatTime1(dayjs().startOf("month").subtract(1, "month"), "yyyyMM");

export default {
    name: 'YunHanAdminIndex',
    components: { container, cesTable, MyConfirmButton,inputYunhan,YhShopSelector },

    data() {
        return {
            that: this,
            importfilter: {
             YearMonthDay:null
            },
            filter: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
                shopCode: null,
                BillType:null,
                ShopName:null
            },
            platformlist:platformlist,
            BillTypeList:[],
            accountTypeist:[],
            importDialog: {
                filter: {
                    settMonth: curMonth,
                    platform: null
                }
            },
            list: [],
            shopList: [],
            summaryarry: {},
            pager: { OrderBy: "RecordId", IsAsc: false },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            // onHandNumber: null,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            sels: [],
            // editparmLoading: false,
            uploadLoading: false,
            // editparmLoading1: false,
            // editparmLoading2: false,
            // editparmVisible: false,
            // editparmVisible1: false,
            // editparmVisible2: false,
            dialogVisible: false,
            listLoading: false,
            // showDetailVisible: false,
            fileList: []
        };
    },

    async mounted() {
        await this.onSearch()
        await this.onchangeplatform()
    },

    methods: {
    async onExport() {
     if (this.onExporting) return;
     try{
        this.filter.startTime = null;
        this.filter.endTime = null;
        if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            this.filter.BillType = this.BillTypeList.join(',');
            this.filter.AccountType = this.accountTypeist.join(',');
        this.uploadLoading = true;
        const params = {...this.pager,...this.filter}
        this.listLoading = true
        const res = await exportNewDYBillingChargeList(params)
        this.listLoading = false
        if (!res) {
          return
        }
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/zip" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '新版抖音日报账单费用_' + new Date().toLocaleString() + '.zip')
        this.uploadLoading = false;

        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },

        //获取店铺
        async onchangeplatform() {
            this.categorylist = []
            const res1 = await getshopList({ platform: this.filter.platform, CurrentPage: 1, PageSize: 100000 });
            this.filter.shopCode = null
            this.shopList = res1.data.list
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            this.filter.BillType = this.BillTypeList.join(',');
            this.filter.AccountType = this.accountTypeist.join(',');
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getNewDYBillingCharge(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            this.list = data
        },
        async nSearch() {
            await this.getlist()
        },
        orderNoInnerBack(val) {
            this.filter.OrderNo = val;
        },
        //字体颜色
        // renderRefundStatus(row) {
        //     if (row.refundStatus == '成功退款' || row.refundStatus == '等待退款') {
        //         return "color:red;cursor:pointer;";
        //     } else return "";
        // },
        //开始导入
        startImport() {
           // this.importDialog.filter.platform = null
            this.fileList = []
            this.uploadLoading=false
            this.dialogVisible = true;
        },
        //取消导入
        cancelImport() {
            this.dialogVisible = false;
        },
        uploadSuccess(response, file, fileList) {
            if (response.code == 200) {
            } else {
                fileList.splice(fileList.indexOf(file), 1);
            }
        },
        async submitUpload() {
            if (!this.importfilter.YearMonthDay || this.importfilter.YearMonthDay == null) {
        this.$message({ message: "请先选择日期", type: "warning" });
        return false;
           }
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit = true;
            this.uploadLoading = true;
            this.$refs.upload.submit();
        },
        clearFiles(){
            this.$refs['upload'].clearFiles();
        },
        async uploadFile(item) {
            if (!this.fileHasSubmit) {
                return false;
            }
            this.fileHasSubmit = false;
            this.uploadLoading = true;
            const form = new FormData();
            //form.append("platForm", this.importDialog.filter.platform);
            form.append("token", this.token);
            form.append("upfile", item.file);
            form.append("YearMonthDay", this.importfilter.YearMonthDay);
            let res = await importBillFee(form);
                if (res.code == 1) {
                    this.$message({ message: "上传成功,正在导入中...", type: "success" });
                    this.$refs.upload.clearFiles();
                    this.dialogVisible = false;
                }
            this.fileList = []
            this.uploadLoading = false;
        },
        async uploadChange(file, fileList) {
            let files=[];
            files.push(file)
            this.fileList = files;
        },
        async uploadRemove(file, fileList) {
            this.fileList = []
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event) {

        },
    }
};
</script>

<style lang="scss" scoped>

::v-deep .el-select__tags-text {
  max-width: 30px;
}
</style>
