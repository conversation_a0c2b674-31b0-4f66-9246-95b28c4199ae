<template>
  <MyContainer>
    <template #header>
      <div style="margin-bottom: 10px;">
        <span>订单量时序图</span>
      </div>
    </template>
    <template #default>
      <buschar v-if="dialogMapVisible.visible" ref="buschar" :analysisData="dialogMapVisible.data"
        :thisStyle="{ width: '100%', height: '700px', 'box-sizing': 'border-box', 'line-height': '360px' }">
      </buschar>
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import buschar from '@/components/Bus/buschar';
import { todayOrderTimeFrame } from "@/api/order/orderData";

export default {
  name: "orderQuantity",
  components: {
    MyContainer, vxetablebase, buschar
  },

  data() {
    return {
      dialogMapVisible: { visible: false, title: "", data: {} },
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
      },
      timeRanges: [],
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      const { data, success } = await todayOrderTimeFrame()
      if (success) {
        this.loading = false
        let res = {
          xAxis: data.times,
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '7天前',
              type: 'bar',
              emphasis: {
                focus: 'series'
              },
              data: data.before7Counts
            },
            {
              name: '3天前',
              type: 'bar',
              emphasis: {
                focus: 'series'
              },
              data: data.before3Counts
            },
            {
              name: '昨天',
              type: 'bar',
              emphasis: {
                focus: 'series'
              },
              data: data.yesterdayCounts
            },
            {
              name: '今天',
              type: 'line',
              stack: 'Total',
              data: data.todayCounts
            },
          ]
        };
        res.series.map((item) => {
          item.itemStyle = {
            "normal": {
              "label": {
                "show": true,
                "position": "top",
                "textStyle": {
                  "fontSize": 14
                }
              }
            }
          }

          item.emphasis = {
            "focus": "series"
          }
          item.smooth = false;
        })
        this.dialogMapVisible.data = res
        this.dialogMapVisible.visible = true;
      } else {
        //获取列表失败
        this.$message.error('获取趋势图失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
