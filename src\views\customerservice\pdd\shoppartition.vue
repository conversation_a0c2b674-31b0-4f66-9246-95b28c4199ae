<template>
  <div class="app-container">
    <div class="filter-container" style="margin-top: 10px">
      <el-autocomplete
        v-model="listQuery.shopName"
        :fetch-suggestions="queryShopList"
        placeholder="店铺名称"
        style="width: 200px;"
        class="filter-item"
        @select="handleListShopSelect"
        @keyup.enter.native="handleFilter"
        @input="handleFilter"
        clearable
        maxlength="100"
      />
      <el-input
        v-model="listQuery.partitionName"
        placeholder="分区名称"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
        clearable
        maxlength="50"
      />
      <el-select
        v-model="listQuery.partitionManager"
        placeholder="分区管理人"
        style="width: 200px;"
        class="filter-item"
        filterable
        clearable
        maxlength="50"
      >
        <el-option
          v-for="item in partitionManagerList"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
      <el-input
        v-model="listQuery.responsibleManager"
        placeholder="负责经理"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
        clearable
        maxlength="50"
      />
      <el-select v-model="listQuery.shopNameIsSame" placeholder="店铺名称是否一致" style="width: 200px;" class="filter-item" filterable clearable>
        <el-option label="一致" :value="true"></el-option>
        <el-option label="不一致" :value="false"></el-option>
      </el-select>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">
        新增
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-upload2" @click="handleImport">
        导入
      </el-button>
      <el-button type="primary" @click="ClickdownloadTemplate">下载模板</el-button>
      <div style="color: red"> {{ extData }} </div>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;height: 700px;"
      @sort-change="handleSortChange"
      height="700px;"
    >
      <el-table-column label="序号" align="center" width="80">
        <template slot-scope="{ $index }">
          <span>{{ $index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="店铺ID" min-width="100" align="center" prop="shopID" sortable="custom">
        <template slot-scope="{row}">
          <span>{{ row.shopID }}</span>
        </template>
      </el-table-column>
      <el-table-column label="店铺编码" min-width="100" align="center" prop="shopCode" sortable="custom">
        <template slot-scope="{row}">
          <span>{{ row.shopCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="店铺名称" min-width="150" align="center" prop="shopName" sortable="custom">
        <template slot-scope="{row}">
          <span :style="{ color: row.isSame ? 'black' : 'red'}">{{ row.shopName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分区名称" min-width="100" align="center" prop="partitionName" sortable="custom">
        <template slot-scope="{row}">
          <span>{{ row.partitionName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="承接内容" min-width="100" align="center" prop="contentResponsibility" sortable="custom">
        <template slot-scope="{row}">
          <span>{{ row.contentResponsibility }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分区管理人" min-width="100" align="center" prop="partitionManager" sortable="custom">
        <template slot-scope="{row}">
          <span>{{ row.partitionManager }}</span>
        </template>
      </el-table-column>
      <el-table-column label="负责经理" min-width="100" align="center" prop="responsibleManager" sortable="custom">
        <template slot-scope="{row}">
          <span>{{ row.responsibleManager }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="100" align="center" prop="remark" sortable="custom" class-name="fixed-cell-height">
        <template slot-scope="{row}">
          <el-tooltip :content="row.remark" placement="top" :disabled="!row.remark || row.remark.length <= 19">
          <span>{{ row.remark }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="160" align="center" prop="createTime" sortable="custom">
        <template slot-scope="{row}">
          <span>{{ row.createTime | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button v-if="row.status!=='deleted'" size="mini" type="danger" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <MyPagination v-show="total>0" :total="total" @page-change="Pagechange" @size-change="Sizechange" v-dialogDrag/>

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" v-dialogDrag >
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="120px" style="width: 400px; margin-left:50px;">
        <el-form-item label="店铺名称" prop="shopName">
          <el-autocomplete
            v-model="temp.shopName"
            :fetch-suggestions="queryShopList"
            placeholder="请输入店铺名称"
            @select="handleShopSelect"
            style="width: 100%"
          ></el-autocomplete>
        </el-form-item>
        <el-form-item label="店铺ID" prop="shopID">
          <el-input v-model="temp.shopID" disabled maxlength="50"/>
        </el-form-item>
        <el-form-item label="店铺编码" prop="shopCode" >
          <el-input v-model="temp.shopCode" maxlength="100"/>
        </el-form-item>
        <el-form-item label="分区名称" prop="partitionName" >
          <el-input v-model="temp.partitionName" maxlength="50"/>
        </el-form-item>
        <el-form-item label="承接内容" prop="contentResponsibility">
          <el-select v-model="temp.contentResponsibility" placeholder="请选择承接内容" style="width: 100%">
            <el-option label="售前" value="售前"></el-option>
            <el-option label="售后" value="售后"></el-option>
            <el-option label="一体" value="一体"></el-option>
            <el-option label="离线留言" value="离线留言"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分区管理人" prop="partitionManager" >
          <el-input v-model="temp.partitionManager" maxlength="50"/>
        </el-form-item>
        <el-form-item label="负责经理" prop="responsibleManager" >
          <el-input v-model="temp.responsibleManager" maxlength="50"/>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="temp.remark" type="textarea" :rows="2" maxlength="500"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确认
        </el-button>
      </div>
    </el-dialog>
    
    <el-dialog title="店铺分区管理导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <el-row>
        <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
          <el-upload ref="upload" :auto-upload="false" :multiple="false" action 
            accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
            <template #trigger>
              <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
              @click="submitupload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
          </el-upload>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { importShopPartitionList, getShopPartitionList, createShopPartition, updateShopPartition, deleteShopPartition, getShopsByName } from '@/api/shoppartition'
import { getAllPartitions } from '@/api/customerservice/pddInquirs'
import { parseTime } from '@/utils'
import MyPagination from '@/components/my-pagination'
import MyContainer from "@/components/my-container/index.vue";
import { error } from 'jquery';

export default {
  name: 'ShopPartition',
  components: {MyContainer, MyPagination },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'info',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      partitionManagerList: [], // 分区管理人列表
      listQuery: {
        currentPage: 1,
        pageSize: 50,
        shopID: '',
        shopName: '',
        partitionName: '',
        partitionManager: '', // 分区管理人查询条件
        responsibleManager: '',
        shopNameIsSame: '', // 店铺是否一致
        // enmPddGroupType: '0',
        orderBy: 'ID',
        isAsc: false
      },
      temp: {
        id: undefined,
        shopID: '',
        shopName: '',
        shopCode: '', // 店铺编码
        partitionName: '',
        contentResponsibility: '',
        partitionManager: '',
        responsibleManager: '',
        remark: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑店铺分区',
        create: '新增店铺分区'
      },
      rules: {
        shopName: [{ required: true, message: '店铺名称不能为空', trigger: 'blur' }],
        shopID: [{ required: true, message: '店铺ID不能为空', trigger: 'change' }],
        partitionName: [{ required: true, message: '分区名称不能为空', trigger: 'blur' }],
        contentResponsibility: [{ required: true, message: '承接内容不能为空', trigger: 'change' }],
        partitionManager: [{ required: true, message: '分区管理人不能为空', trigger: 'blur' }],
        responsibleManager: [{ required: true, message: '负责经理不能为空', trigger: 'blur' }]
      },
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      extData: '',
    }
  },
  created() {
    this.getList()
    this.getPartitionManagers()
  },
  methods: {
    getPartitionManagers() {
      // 获取分区管理人列表
      getShopPartitionList({
        currentPage: 1,
        pageSize: 1000, // 获取足够多的数据
        enmPddGroupType: '0'
      }).then(response => {
        if (response.data && response.data.list) {
          // 提取所有分区管理人，并去重
          const managers = [...new Set(response.data.list.map(item => item.partitionManager).filter(Boolean))]
          this.partitionManagerList = managers
        }
      }).catch(error => {
        console.error('获取分区管理人列表失败:', error)
      })
    },
    getList(currentPage) {
      this.listLoading = true
      if (!this.listQuery.shgopName) {
        this.listQuery.shopID = ''
        this.listQuery.shopCode = ''
      }
      if (currentPage) {
        this.listQuery.currentPage = currentPage
      }
      getShopPartitionList(this.listQuery).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.extData = response.data.extData.fenQu
        this.listLoading = false
      }).catch(error => {
        console.error('获取分区列表失败:', error)
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.currentPage = 1
      this.getList()
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    //打开上传弹窗
    handleImport() {
      this.dialogVisible = true;
      this.uploadLoading = false;
      this.$nextTick(() => {
        if (this.$refs.upload) {
          this.$refs.upload.clearFiles();
        }
      });
      this.fileList.splice(0, 1);
    },
    //上传文件
    async uploadFile(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      console.log(form,'form');
      this.uploadLoading = true;
      const res = await importShopPartitionList(form);
      this.uploadLoading = false;
      if (res?.success) {
        this.dialogVisible = false;
      }
    },
    //更改上传文件
    async uploadChange(file, fileList) {
      if (fileList.length == 2) {
        fileList.splice(1, 1);
        this.$message({ message: "只允许单文件导入", type: "warning" });
        return false;
      }
      this.fileList.push(file);
    },
    //移除上传文件
    uploadRemove() {
      this.fileList.splice(0, 1);
    },
    //提交上传文件
    async submitupload() {
      if (this.fileList.length == 0) {
        this.$message.warning('您没有选择任何文件！');
        return;
      }
      this.$refs.upload.submit();
      this.$refs.upload.clearFiles();
      this.fileList.splice(0, 1);
      this.importVisible = false;
    },


    resetTemp() {
      this.temp = {
        id: undefined,
        shopID: '',
        shopName: '',
        shopCode: '',
        partitionName: '',
        contentResponsibility: '',
        partitionManager: '',
        responsibleManager: '',
        remark: ''
      }
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 转换ID字段名
          const tempData = Object.assign({}, this.temp)
          // tempData.ID = 0 // 新增时ID为0
          // tempData.enmPddGroupType = '0';
          createShopPartition(tempData).then(response => {
            if (response?.success) {
              this.$notify({
                title: '成功',
                message: '创建成功',
                type: 'success',
                duration: 2000
              })
              this.dialogFormVisible = false
              this.getList()
              this.getPartitionManagers() // 刷新分区管理人列表
            } else {
              this.$notify({
                title: '失败',
                message: response?.message,
                type: 'error',
                duration: 2000
              })
            }
          })
          .catch(error => {
            this.$notify({
              title: '失败',
              message: '创建失败',
              type: 'error',
              duration: 2000
            })
          });
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.temp.id = this.temp.id || this.temp.ID // 兼容后端返回的字段名
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          tempData.ID = tempData.id || tempData.ID // 兼容字段名

          updateShopPartition(tempData).then(response => {
            if (response?.success) {
              this.$notify({
                title: '成功',
                message: '更新成功',
                type: 'success',
                duration: 2000
              })
              this.dialogFormVisible = false
              this.getList()
              this.getPartitionManagers() // 刷新分区管理人列表
            } else {
              this.$notify({
                title: '失败',
                message: response?.message,
                type: 'error',
                duration: 2000
              })
            }
          })
          .catch(error => {
            console.log('更新店铺失败：' + error);
            this.$notify({
              title: '失败',
              message: '更新失败',
              type: 'error',
              duration: 2000
            })
          });
        }
      })
    },
    handleDelete(row) {
      const id = row.id || row.ID // 兼容后端返回的字段名
      this.$confirm('确认删除该记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteShopPartition({id:id}).then(() => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
          this.getPartitionManagers() // 刷新分区管理人列表
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    queryShopList(queryString, cb) {
      // 根据输入的店铺名称模糊查询店铺列表
      if (queryString.length === 0) {
        cb([])
        return
      }

      getShopsByName({ shopName: queryString, Platform: 2 }).then(response => {
        if (null == response.data || response.data.length == 0) {
          this.temp.shopID = null;
          this.temp.shopCode = null;
          this.$message.warning("【" + queryString + '】未找到该店铺！');
          return;
        }
        const shops = response.data.map(item => {
          return { value: item.shopName, shopID: item.platformShopID || item.shopCode, shopCode: item.shopCode }
        })
        console.log("shopselect", shops);
        cb(shops)
      })
    },
    handleShopSelect(item) {
      // 选择店铺后自动填充店铺ID和店铺编码
      this.temp.shopID = item.shopID
      this.temp.shopCode = item.shopCode
    },
    handleListShopSelect(item) {
      // 选择店铺后自动填充店铺ID到查询条件
      this.listQuery.shopID = item.shopID
      this.listQuery.shopCode = item.shopCode
      this.handleFilter()
    },
    Sizechange(val) {
      this.listQuery.currentPage = 1;
      this.listQuery.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.listQuery.currentPage = val;
      this.getList()
    },
    handleSortChange(column) {
      if (!column.order) {
        this.listQuery.orderBy = null
        this.listQuery.isAsc = false
      } else {
        this.listQuery.orderBy = column.prop
        this.listQuery.isAsc = column.order === 'ascending'
      }
      this.getList()
    },
    ClickdownloadTemplate() {
      window.open("/static/excel/customerservice/店铺分区管理模板.xlsx", "_blank");
    }
  }
}
</script>
<style lang="scss" scoped>
.fixed-cell-height .cell {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: normal;
}

.fixed-cell-height .cell span {
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 限制显示两行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
