<template>
    <my-container v-loading="pageLoading">

         <el-row>
            <el-col :span="24"><div class="div1">
              <div id="app" style="height: 100px; width: 100%; display: flex; flex-direction: row;">

               
               <div style="margin-top: 45px;margin-left: 600px;" >
                <a style="font-size: 15px; font-weight: bolder;"  :style="tabnum==1?{'color':'red'}:{}"  @click="tableShowFuc(1)">待选</a>
               </div>

               <div style="margin-top: 45px; margin-left: 50px;">
                <a style="font-size: 15px; font-weight: bolder; " :style="tabnum==2?{'color':'red'}:{}" @click="tableshowHotCakeFuc(2)" >爆款</a>
               </div>

               <div style="margin-top: 45px;margin-left: 50px;">
                <a style="font-size: 15px; font-weight: bolder; " :style="tabnum==3?{'color':'red'}:{}"  @click="tableshowProductNewFuc(3)">新品</a>
               </div>

               <div style="margin-top: 45px;margin-left: 50px;">
                <a style="font-size: 15px; font-weight: bolder; " :style="tabnum==4?{'color':'red'}:{}" @click="tableShowAttentionFuc(4)" >关注</a>
               </div>

               <div style="margin-top: 45px;margin-left: 50px;">
                <a style="font-size: 15px; font-weight: bolder; " :style="tabnum==5?{'color':'red'}:{}" @click="tableShowTaskFuc(5)" >任务中心</a>
               </div>

              </div>

            </div></el-col>
            
          </el-row>
          <el-row>
            <el-col :span="24"><div class="div2">
                <div class="div2-1">
                    <el-button-group>
                    <el-button style="padding: 0;margin: 0;">
                        <el-date-picker style="width: 210px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="上架时间" end-placeholder="上架时间" :clearable="true" :picker-options="pickerOptions">
                        </el-date-picker>
                    </el-button>
                    <el-button style="padding: 0;margin-left: 10px;">
                      <el-date-picker style="width: 210px" v-model="filter.timerange1" type="daterange" format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd" range-separator="至" start-placeholder="数据时间" end-placeholder="数据时间" :clearable="true" :picker-options="pickerOptions">
                      </el-date-picker>
                  </el-button>
                    <el-button style="padding: 0;margin-left: 10px;" >
                        <el-select filterable v-model="filter.Platform" collapse-tags clearable placeholder="平台"
                          style="width: 90px"> 
                          <el-option label="淘系" :value="1" />
                          <el-option label="淘工厂" :value="8" />
                        </el-select>
                      </el-button>
                    <el-button style="padding: 0;margin-left: 10px;" v-if="checkPermission('NXOperationGroup')">
                    <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组"
                      style="width: 90px">
                      <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                      <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-button>
                  <el-button style="padding: 0; margin-left: 10px;" v-if="checkPermission('NXOperationoperateSpecialUserId')">
                    <el-select filterable v-model="filter.operateSpecialUserId" collapse-tags clearable placeholder="运营专员"
                      style="width: 90px">
                      <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-button>
                  <el-button style="padding: 0;  margin-left: 10px;" v-if="checkPermission('NXOperationuserId')">
                    <el-select filterable v-model="filter.userId" collapse-tags clearable placeholder="运营助理"
                      style="width: 90px">
                      <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-button>

                  <el-button style="padding: 0;  margin-left: 10px;">
                    <el-input v-model.trim="filter.ProName" maxlength="300" clearable placeholder="商品名称" style="width:150px;" />
                  </el-button>
                  <el-button style="padding: 0; border: none; margin-left: 10px;">
                    <inputYunhan :title="inputtitle" :inputt.sync="filter.procode" :maxlength="3000" :row="inputrow"  :placeholder="placeholderContent" :inputshow="inputshow" :clearable="true" @callback="callback"></inputYunhan>
                </el-button>
                  <el-button class="button" style="padding: 0;  margin-left: 10px;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
                  </el-button>
                 
                
                </el-button-group>
                </div>
                
                <div class="div2-2" v-show="tableshow">
                 
                    <WaitingSelection :filter="filter" ref="WaitingSelection" ></WaitingSelection>
                  
                </div>
                <div class="div2-3" v-show="tableshowProductNew">
              
                    <ProductNew :filter="filter" ref="ProductNew" ></ProductNew>
                  
    
                  
                </div>
                <div class="div2-4" v-show="tableshowHotCake">
              
                    <HotCake  :filter="filter" ref="HotCake" ></HotCake>
                  
    
                  
                </div>
                <div class="div2-5" v-show="tableshowAttention">
              
                    <Attention  :filter="filter"   ref="Attention" ></Attention>
                  
    
                  
                </div>
                <div class="div2-6" v-show="tableshowTask">
              
                    <Task :filter="filter"  ref="Task" ></Task>

                </div>
               
            </div></el-col>
            
          </el-row>
          <el-row>
            <el-col :span="24">
        </el-col> 
          </el-row>
    </my-container>
  </template>
  <script>

  import dayjs from "dayjs";
  import { mapGetters } from 'vuex'
  import cesTable from "@/components/Table/table.vue";
  import { formatPlatform, formatTime, formatYesornoBool, formatLinkProCode } from "@/utils/tools";
  import { getEnergyEfficiencyYesterdayTX ,getEnergyEfficiencyTX_Chart} from '@/api/bookkeeper/pddstaticsreport'
  import * as echarts from 'echarts'
  import buschar from '@/components/Bus/buscharOpeation'
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import {getDirectorGroupList,getDirectorList } from '@/api/operatemanage/base/shop';
  import ProductNew from "./ProductNew.vue";
  import WaitingSelection from "./WaitingSelection.vue";
  import HotCake from "./HotCake.vue";
  import Attention from "./Attention.vue";
  import Task from "./Task.vue";
  import inputYunhan from '@/components/Comm/inputYunhan.vue'
  

  export default {
    name: "Users",
    components: {
      MyContainer,
      MyConfirmButton,
      MySearch,
      MySearchWindow,
      cesTable,
      buschar,
      ProductNew,
      WaitingSelection,
      HotCake,
      Attention,
      Task,
      inputYunhan
    },
    data() {
      return {
        placeholderContent:"请输入产品ID",
        tabnum: 1,
        tableshow:true,
        tableshowProductNew:false,
        tableshowHotCake:false,
        tableshowAttention:false,
        tableshowTask:false,
        grouplist: [],
        pageLoading: false,
        charData:[],
        fileList: [],
        dialogVisible: false,
        uploadLoading: false,
        directorlist: [],
        avatarDefault: require('@/assets/images/avatar.png'),
        
        pddcontributeinfolist: [],
        filter: {
        startTime: null,
        endTime: null,
        dataStartTime: null,
        dataEndTime: null,
        Platform:null,
        timerange: null,
        timerange1:null,
        // 运营助理id
        userId: null,
        //组id
        groupId: null,
        // 运营专员 ID
        operateSpecialUserId: null,
        procode:null,
        ProName:null
       },
       filter1:{
            DateType:null,
            startTime: null,
            endTime: null,
            Platform:null,
            timerange: null,
            // 运营助理id
            userId: null,
            //组id
            groupId: null,
            // 运营专员 ID
            operateSpecialUserId: null,
          },
          pickerOptions: {
                shortcuts: [{
                    text: '近一周',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                    picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近半个月',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                    picker.$emit('pick', [start, end]);
                    }
                },{
                    text: '近一个月',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                    picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近三个月',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                    picker.$emit('pick', [start, end]);
                    }
                }]
            },
      };
    },
    async mounted() {
      await this.getGroupList();
    },
    async created() {
    //  await this.init()
     await this.showchart();
     
    // await this.getShopList();
    
  },
  computed:{
    ...mapGetters([
      'menus',
      'userName',
      'avatar'
    ]),

  
  

  },
 
    methods: {
    //批量数据组合，回调数据
    async callback(val) {
      this.filter.procode = val  
    }, 
        tableShowFuc(val){
            this.tableshow = true;
            this. tableshowProductNew=false,
            this.tableshowHotCake=false,
            this.tableshowAttention=false,
            this.tableshowTask=false
            this.tabnum = val;
        },
        tableshowHotCakeFuc(val){
            this.tableshow = false;
            this.tableshowProductNew=false,
            this.tableshowHotCake=true,
            this.tableshowAttention=false,
            this.tableshowTask=false
            this.tabnum = val;
        },
        tableshowProductNewFuc(val){
            this.tableshow = false;
            this.tableshowProductNew=true,
            this.tableshowHotCake=false,
            this.tableshowAttention=false,
            this.tableshowTask=false
            this.tabnum = val;
        },
        tableShowAttentionFuc(val){
            this.tableshow = false;
            this.tableshowProductNew=false,
            this.tableshowHotCake=false,
            this.tableshowAttention=true,
            this.tableshowTask=false
            this.tabnum = val;
        },
        tableShowTaskFuc(val){
            this.tableshow = false;
            this.tableshowProductNew=false,
            this.tableshowHotCake=false,
            this.tableshowAttention=false,
            this.tableshowTask=true
            this.tabnum = val;
        },

      async getGroupList() {
      
      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });

     
    },
    
      onSearch() {

       if(this.tableshow==true)
       {

        this.$refs.WaitingSelection.onSearch();
        // this.tableshow=false;
       }
         
       
        if( this.tableshowProductNew==true)
        {
          this.$refs.ProductNew.onSearch();
        }

        if( this.tableshowHotCake==true)
        {
          this.$refs.HotCake.onSearch();
        }

        if( this.tableshowAttention==true)
        {
          this.$refs.Attention.onSearch();
        }

        if( this.tableshowTask==true)
        {
          this.$refs.Task.onSearch();
        }
        
   
      },
    
    // datetostr(date) {
    //   var y = date.getFullYear();
    //   var m = ("0" + (date.getMonth() + 1)).slice(-2);
    //   var d = ("0" + date.getDate()).slice(-2);
    //   return y + "-" + m + "-" + d;
    // },
    // async init() {
    //   var date1 = new Date(); date1.setDate(date1.getDate() - 30);
    //   var date2 = new Date(); date2.setDate(date2.getDate() - 1);
    //   this.filter.timerange = [];
    //   this.filter.timerange[0] = this.datetostr(date1);
    //   this.filter.timerange[1] = this.datetostr(date2);
    // },
     
    }
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }

  .div1 {
    margin-top: 0px;
    background: #d7dfeb;
    height: 100px;
  }
  .div2 {
    background: #e0e5f0;
    height: 70vh;
  }
  .div3 {
    margin-top: 0px;
    background: #d7dfeb;
    height: 600px;
  }
  .div2-1{
    margin-top: 0px;
    background: #bed1ec;
    height: 50px;
  }
  .div2-2{
    margin-top: 0px;
    /* background: #9ba6b4; */
    height: 600px;
    width: 98%;
  }
  .div2-3{
    margin-top: 0px;
    /* background: #98bef1; */
    height: 600px;
  }
  .div2-4{
    margin-top: 0px;
    /* background: #98bef1; */
    height: 600px;
  }
  .div2-5{
    margin-top: 0px;
    /* background: #98bef1; */
    height: 600px;
  }
  .div2-6{
    margin-top: 0px;
    /* background: #98bef1; */
    height: 600px;
  }
  .span1{
   margin-top: 0px;
   margin-left: 100px;
   width: 80px;
   color: red;
  }
  .span2{
   margin-top: 0px;
   margin-left: 100px;
   color: red;
  }
  .span3{
   margin-top: 0px;
   margin-left: 100px;
   color: red;
  }
  .span4{
   margin-top: 0px;
   margin-left: 270px;
   width: 80px;
   color: red;
  }
  .span5{
   margin-top: 0px;
   margin-left: 200px;
   color: red;
  }
  .span6{
   margin-top: 0px;
   margin-left: 200px;
   color: red;
  }
  .span7{
   margin-top: 0px;
   margin-left: 200px;
   color: red;
  }


  
  </style>
  