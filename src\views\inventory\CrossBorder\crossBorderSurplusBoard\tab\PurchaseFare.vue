<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
        <el-form-item label="">
          <el-date-picker style="width: 320px" v-model="Filter.timerange" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间"
            :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>

        <!-- <el-form-item label="">
          <el-select filterable clearable v-model="Filter.platFrom" placeholder="平台" style="width: 120px">
            <el-option v-for="item in platformlistKj" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item> -->

        <!-- <el-form-item label="">
          <el-select filterable clearable v-model="Filter.violationType" placeholder="违规类型" style="width: 120px">
            <el-option v-for="item in ViolationTypeList" :key="item" :label="item" :value="item">
            </el-option>
          </el-select>
        </el-form-item> -->


        <el-form-item label="">
          <!-- <el-input v-model.trim="Filter.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" /> -->
          <inputYunhan :key="'1'" :keys="'one'" :width="'220px'" ref="" :inputt.sync="Filter.goodsCodes"
                v-model.trim="Filter.goodsCodes" placeholder="商品编码/若输入多条请按回车" :clearable="true"
                @callback="callbackGoodsCode" title="商品编码" @entersearch="entersearch" :maxRows="100">
            </inputYunhan>  
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport" style="margin-left: 5px;">导出</el-button>
          <!-- <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button @click="startImport"
            type="primary" icon="el-icon-share" @command="handleCommand"> 导入
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">下载模版</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->
        </el-form-item>
      </el-form>
    </template>
    <!--列表-->
    <vxetablebase ref="table" :id="'crossBorderCourierFeeAverage202408310425'" :that='that' :isIndex='true'
      :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='dahuixionglist' :tableCols='tableCols'
      :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="margin: 0" :loading="listLoading" :height="'100%'">
    </vxetablebase>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getjSpeedDriveList" />
    </template>
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 75px;">
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

  </my-container>
</template>
<script>

import {
  GetDayRptPurchaseFreight_KJPageList, imporTemuBanShopViolationAsync, DayRptPurchaseFreight_KJ_Export, getViolationTypeList
} from '@/api/bookkeeper/crossBorderV2'
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from 'dayjs';
import inputYunhan from "@/components/Comm/inputYunhan";

import { formatPlatformkj,platformlistKj } from "@/utils/tools";
const tableCols = [
  // { istrue: true, prop: 'platFrom', label: '平台', sortable: 'custom',  formatter: (row) => {return formatPlatformkj(row.platFrom)}},
  { istrue: true, prop: 'rptDate', label: '数据日期', sortable: 'custom',formatter:row=>{return dayjs(row.rptDate).format('YYYY-MM-DD')} },
  { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', },
  { istrue: true, prop: 'openingBalance', label: '期初余额', sortable: 'custom', },
  { istrue: true, prop: 'openingQty', label: '期初数量', sortable: 'custom', },
  { istrue: true, prop: 'todayBalance', label: '当期金额', sortable: 'custom', },
  { istrue: true, prop: 'todayQty', label: '当期数量', sortable: 'custom', },
  { istrue: true, prop: 'todayPrice', label: '当期单价', sortable: 'custom', },
  { istrue: true, prop: 'dayRptQuantity', label: '日报用量', sortable: 'custom', },
  { istrue: true, prop: 'dayRptFreight', label: '日报用额', sortable: 'custom', },
  { istrue: true, prop: 'endBalance', label: '期末余额', sortable: 'custom', },
  { istrue: true, prop: 'endQty', label: '期末数量', sortable: 'custom', },
];
export default {
  name: "crossBorderCourierFeeAverage",
  components: { MyContainer, vxetablebase,inputYunhan },
  data() {
    return {
      that: this,
      editLoading: false,
      addVisible: false,
      Filter: {
        timerange: [],
      },
      userList: [],
      groupList: [],
      dahuixionglist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: {},
      pager: { OrderBy: "id", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
      platform: 1,
      yearMonth: "",
      editVisible: false,
      defaultDate: new Date(),
      pickerOptions: {
        disabledDate(date) {
          // 设置禁用日期
          const start = new Date("1970/1/1");
          const end = new Date("9999/12/31");
          return date < start || date > end;
        },
      },
      dialogVisible: false,//导入弹窗
      fileList: [],//上传文件列表
      uploadLoading: false,//上传按钮loading
      fileparm: {},//上传文件参数
      yearMonthDay: null,//导入日期
      shopList: [],
      ViolationTypeList: [],
      platformlistKj
    };
  },
  async mounted() {
    await this.init();
    this.onSearch();
  },
  methods: {
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init() {
      var date1 = new Date(); date1.setDate(date1.getDate() - 6);
      var date2 = new Date(); date2.setDate(date2.getDate() - 1);
      this.Filter.timerange = [];
      this.Filter.timerange[0] = this.datetostr(date1);
      this.Filter.timerange[1] = this.datetostr(date2);
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getjSpeedDriveList();
    },
    async getjSpeedDriveList() {
      const para = { ...this.Filter };
      if (this.Filter.timerange) {
        para.startDate  = this.Filter.timerange[0];
        para.endDate = this.Filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,

      };
      this.listLoading = true;
      const res = await GetDayRptPurchaseFreight_KJPageList(params);
      this.listLoading = false;
      this.total = res.data.total
      this.dahuixionglist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },

    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      // form.append("yearMonthDay", this.yearMonthDay);
      // var res = await imporTemuBanShopViolationAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.onSearch()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    async handleCommand(command) {
      switch (command) {
        //下载模版
        case 'a':
          await this.downLoadFile()
          break;
      }
    },
    async downLoadFile() {
      window.open("/static/excel/CrossBorderDownloadTemplate/TEMU半托-违规明细导入模版.xlsx", "_blank");
    },
    async onExport() {//导出列表数据；
      const para = { ...this.Filter };
      if (this.Filter.timerange) {
        para.startDate = this.Filter.timerange[0];
        para.endDate = this.Filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,

      };
      var res = await DayRptPurchaseFreight_KJ_Export(params);
      if (res?.success) {
        this.$message({ message: res.msg, type: "success" });
      }
    },
    //多条查询部分
    async entersearch(val) {
            this.onSearch();
    },
    async callbackGoodsCode(val) {
        this.Filter.goodsCodes = val;
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>