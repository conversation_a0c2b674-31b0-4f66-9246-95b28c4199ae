<template>
 <MyContainer >
   <template #header>
        <div style="display: flex;flex-wrap: wrap; align-items:center;">
            <el-form :inline="true" :model="trialForm"  class="demo-form-inline" ref="trialForm" :rules="rules">
                <el-form-item label=" " prop="batch" required  >
                    <el-select v-model="trialForm.batch" filterable placeholder="批次选择" :loading="loadingBatchList" @focus="getListContainerBatch">
                        <el-option
                        v-for="item in batchList"
                        :key="item.key"
                        :label="item.value"
                        :value="item.key">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="产品信息" required >
                    <el-radio-group v-model="trialForm.info">
                        <el-radio :label="3">选择SKU</el-radio>
                        <el-radio :label="6">手工输入</el-radio>
                    </el-radio-group>

                </el-form-item>
                <el-form-item v-if="trialForm.info ==6">
                    <el-input v-model="trialForm.Id" maxlength="20" placeholder="请输入竞品ID"></el-input>

                </el-form-item>
                <el-form-item label=" "  v-if="trialForm.info ==6" prop="price">
                    <el-input-number type="number" clearable :precision="2" :min="0.01" :max="99999"  placeholder="产品成本" :controls="false" v-model.number="trialForm.price" style="width:100px"></el-input-number>
                </el-form-item>
                 <el-form-item label="尺寸"   v-if="trialForm.info ==6" >
                    <div >
                        <div style="display: flex;" class="ckg">
                            <el-form-item prop="c">
                                <el-input-number type="number" clearable :precision="2" :min="0.01"  :max="99999"  placeholder="长" :controls="false" v-model.number="trialForm.c" style="width:50px"></el-input-number>
                            </el-form-item>
                            <el-form-item prop="k">
                                <el-input-number type="number" clearable :precision="2" :min="0.01" :max="99999" placeholder="宽" :controls="false" v-model.number="trialForm.k" style="width:50px"></el-input-number>
                            </el-form-item>
                            <el-form-item prop="g">
                                <el-input-number type="number" clearable :precision="2" :min="0.01"  :max="99999" placeholder="高" :controls="false" v-model.number="trialForm.g" style="width:50px"></el-input-number>
                            </el-form-item>
                            <div class="unitStyle" >cm</div>
                        </div>
                    </div>
                </el-form-item>
                 <el-form-item label="重量" prop="kg"  v-if="trialForm.info ==6">
                    <div >
                        <div style="display: flex;" class="ckg">
                            <el-input-number type="number" clearable :precision="3" :min="0.001" :max="99999"  placeholder="重量" :controls="false" v-model.number="trialForm.kg" style="width:100px"></el-input-number>
                            <div class="unitStyle" style="height:28px">kg</div>
                        </div>
                    </div>
                </el-form-item>
                
                <el-form-item>
                    <el-button type="primary" v-if="trialForm.info ==3"  @click="addSKU()">添加SKU</el-button>
                    <el-button type="primary" @click="onTrialForm('trialForm')">试算</el-button>
                    <el-button type="primary"  @click="onTrialSave()">保存</el-button>
                    <el-button type="primary" @click="showTrialTable = !showTrialTable">{{showTrialTable?'关闭试算板块':'打开试算板块'  }}</el-button>
                </el-form-item>
            </el-form>
            
        </div>
        <vxetablebase 
        v-if="showTrialTable"
        :id="'temuHalfTrusteeship20240822'" 
        :tablekey="'temuHalfTrusteeship20240822'" 
        :tableData='trialData' 
        :tableCols='trialtableCols'
        :loading='trialLoading' 
        :border='true'  
        :that="that" 
        :hasSeq="false"
        :toolbarshow="false"
        ref="trialtable"  
        :showsummary='false' 
       >
    </vxetablebase>

        <div style="display: flex;flex-wrap: wrap; align-items:center;margin: 10px 0;">
            <el-input class="marginleft" v-model="queryEnum.productSku" placeholder="SKU编码" maxlength="20" clearable style="width: 150px;" >
            </el-input>
            <el-select v-model="queryEnum.containerBatchNo" filterable  clearable placeholder="批次选择" style="margin:0 10px" :loading="loadingBatchList" @focus="getListContainerBatch">
                <el-option
                v-for="item in batchList"
                :key="item.key"
                :label="item.value"
                :value="item.value">
                </el-option>
            </el-select>
            <el-button type="primary" @click="getList('search')">查询</el-button>
            <el-button type="primary" @click="openAdd" v-if="checkPermission('temu_add_price_batch')">新增批次</el-button>
            <el-button type="primary" @click="handleExport()" v-if="checkPermission('temuHalfTrusteeship_export')">导出</el-button>
            <el-button type="primary" @click="onBatchDelete()">批量删除</el-button>
            <el-button type="primary" @click="logisticsVisibleOpen">查看物流报价</el-button>

        </div>
   </template>

   <template>
     <vxetablebase 
     :id="'temuHalfTrusteeship20240821'" 
     :tablekey="'temuHalfTrusteeship20240821'" 
     :tableData='tableData' 
     :tableCols='tableCols' 
     @sortchange='sortchange'
      :loading='loading' 
      :border='true' 
      :that="that" 
      ref="vxetable"  
      :hasSeq="false"
      :showsummary='false'
      @select='selectchange'
      >
     </vxetablebase>
   </template>

   <template #footer>
      <my-pagination ref="pager" :total="total"  @get-page="getList"/>
    </template>
    <!-- 查看物流报价弹窗 -->
    <el-dialog title="物流报价明细" :visible.sync="logisticsVisible"  v-dialogDrag center>
        <div style="margin-bottom: 20px;"> 
            <el-select v-model="logisticsQuery.platformShippingMethod" filterable   placeholder="仓库物流渠道" style="margin-right: 20px;">
                <el-option
                v-for="item in godownList"
                :key="item.key"
                :label="item.key"
                :value="item.value">
                </el-option>
            </el-select>
            <el-button type="primary" @click="logisticsListGet">查询</el-button>

            <el-button type="primary" @click="startImport" v-if="checkPermission('temu_logistics_quotation_import')">导入</el-button>
            <el-button type="primary" @click="logVisibleOpen">查看日志</el-button>
            <el-button type="primary" @click="logisticsExport">导出</el-button>

        </div>
        <vxetablebase 
           :id="'temuHalfTrusteeship20241009'" 
           :tablekey="'temuHalfTrusteeship20241009'" 
           :tableData='logisticsData' 
           :tableCols='logisticstableCols'
            :loading='logisticsLoading' 
             @sortchange='logisticsSortchange'
            :border='true'  
            :that="that" 
            height="440px" 
            ref="logisticstable"  
            :showsummary='false' 
            >
          </vxetablebase>
        <my-pagination ref="logisticsPage" :total="logisticsTotal"  @get-page="logisticsListGet()"/>

    </el-dialog>
    <!--物流报价明细 =》 操作日志 -->
    <el-dialog title="操作日志" :visible.sync="logVisible" width="550px"  v-dialogDrag center>
        <vxetablebase 
           :id="'temuHalfTrusteeship20241010'" 
           :tablekey="'temuHalfTrusteeship20241010'" 
           :tableData='logData' 
           :tableCols='logtableCols'
            :loading='logLoading' 
            :border='true'  
            :that="that" 
            height="440px" 
            ref="logtable"  
            :showsummary='false' 
            >
          </vxetablebase>
        <my-pagination ref="logPage" :total="logTotal"  @get-page="logListGet()"/>

    </el-dialog>
   
   <!-- 导入弹窗 -->
   <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" 
        :auto-upload="false" :multiple="false" 
        :limit="1" action
          accept=".xlsx" 
          :file-list="fileList"
           :data="fileparm" 
           :http-request="onUploadFile"
          :on-success="onUploadSuccess"
           :on-change="onUploadChange" 
          :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
   
    <el-dialog title="新增货柜批次" :visible.sync="addBatchVisible" @close="resetAddVatch('addBatchForm')" v-dialogDrag>
        <el-form :inline="true" :model="addBatchForm" ref="addBatchForm" class="demo-form-inline">
            <el-form-item label="批次日期" prop="batchDate" :rules="{ required: true, message: '请选择批次日期', trigger: 'blur' }">
                <el-date-picker
                    v-model="addBatchForm.batchDate"
                    type="date"
                    placeholder="选择日期时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="仓库名称" prop="supplierWarehouse" :rules="{ required: true, message: '请选择仓库', trigger: 'change' }">
                <el-select v-model="addBatchForm.supplierWarehouse" placeholder="请选择">
                    <el-option
                    v-for="item in warehouseList"
                    :key="item.value"
                    :label="item.key"
                    :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="整柜费用" prop="containerBatchFee" :rules="{ required: true, message: '请选择费用', trigger: 'blur' }">
                
                <div style="display: flex;">
                    <el-input-number type="number" clearable :precision="2" :min="0.01"  :max="9999999" placeholder="费用" :controls="false" v-model.number="addBatchForm.containerBatchFee" style="width:80px"></el-input-number>
                <div class="unitStyle" style="height:28px">元</div>
                </div>

            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onSubmit('addBatchForm')">保存</el-button>
            </el-form-item>
        </el-form>
        <vxetablebase 
           :id="'temuHalfTrusteeship20240820'" 
           :tablekey="'temuHalfTrusteeship20240820'" 
           :tableData='addBatchData' 
           :tableCols='addBatchtableCols'
            :loading='addBatchLoading' 
            :border='true'  
            :that="that" 
            height="440px" 
            ref="addBatchtable"  
            :showsummary='false' 
            >
          </vxetablebase>
        <my-pagination ref="addBatchPage" :total="addBatchTotal"  @get-page="addBatchList()"/>

    </el-dialog>
    <!-- sku列表 -->
    <el-dialog :visible.sync="skuListVisible"  custom-class="skuListClass" @close="closeSkuList" v-dialogDrag append-to-body>
        <!-- v-if="skuListVisible" 不缓存表格 -->
         <skuList  ref="skuList" :oldCurrentRadioRow= "oldCurrentRadioRow"/>
         <div slot="footer" class="dialog-footer">
                <el-button @click="skuListVisible = false">取 消</el-button>
                <el-button type="primary" @click="onSkuList">确 定</el-button>
            </div> 
    </el-dialog>
    <!-- 修改备注 -->
    <el-dialog :visible.sync="editNoteVisible" width="540px"   title="修改备注" center>
        <el-form ref="editNoteForm" :model="editNoteForm" label-width="80px">
            <el-form-item label="备注信息" prop="remarks">
                <el-input
                type="textarea"
                :rows="6"
                placeholder="请输入内容"
                v-model="editNoteForm.remarks"
                maxlength="200"
                show-word-limit
                >
                </el-input>
            </el-form-item>
        </el-form>
       

         <div slot="footer" class="dialog-footer">
                <el-button @click="editNoteVisible = false">取 消</el-button>
                <el-button type="primary" @click="editNoteSet()">保 存</el-button>
            </div>
    </el-dialog>
 </MyContainer>

 
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import dayjs from 'dayjs';
import skuList from './common/skuList';
import { getWarehouseList,saveContainerBatch,listContainerBatch,deleteSkuPricing,pageListContainerBatch,skuExport,deleteContainerBatch,pageListSkuPricing 
    ,calSkuPricing,saveSkuPricing,upDateRemarks,getShippingMethodFeeGroupList,getShippingMethodFeePageList,
    exportWarehouseShipmentPricing,importWarehouseShipmentPricing,getImportLogPageList
} from '@/api/kj/skuPricing.js';
import checkPermission from '@/utils/permission'
import { nextTick } from 'vue';
import { formatmoney } from "@/utils/tools";
const tableCols = [
  { istrue: true,  type: "checkbox" },
 { sortable: 'custom',fixed:'left', prop: 'productSku', label: 'SKU',  width: '150px' },
 { sortable: 'custom',fixed:'left', prop: 'containerBatchNo', label: '货柜批次', width: '120px', },
 { sortable: 'custom',fixed:'left', prop:'productLength',label: '长 (cm)',  width:'75px'},
 { sortable: 'custom',fixed:'left', prop: 'productWidth', label: '宽 (cm)',width:'75px'  },
 { sortable: 'custom',fixed:'left', prop: 'productHeight', label: '高 (cm)',width:'75px' },
 { sortable: 'custom',prop: 'productVolumeCal', label: '体积 (cm³)',  width: '85px',formatter: (row) => { return row.productVolumeCal  }},
 { sortable: 'custom',prop: 'productCount', label: '个数',width: '70px',},
 { sortable: 'custom',prop: 'productFirstTripPrice', label: '头程价格(RMB)',   width: '115px',},
 { sortable: 'custom',prop: 'productSupplyPrice', label: '产品成本(RMB)',   width: '115px',},
 { sortable: 'custom',prop: 'productWeight', label: '重量 (kg)',   width: '75px',},
 { sortable: 'custom',prop: 'productVolumeWeightLB', label: '物流计费重量 (磅)',   width: '120px',},
 { sortable: 'custom',prop: 'productFreightFeeUsps18', label: '1-8(USPS)美金',   width: '110px',},
 { sortable: 'custom',prop: 'productFreightFeeUsps15', label: '1-5(USPS)美金',   width: '110px',},
 { sortable: 'custom',prop: 'productFreightFeeUps18', label: '1-8(UPS)美金',   width: '105px',},
 { sortable: 'custom',prop: 'productFreightFeeUps15', label: '1-5(UPS)美金',   width: '105px',},
 { sortable: 'custom',prop: 'productFreightFeeAmazon18', label: '1-8(Amazon)美金',   width: '127px',},
 { sortable: 'custom',prop: 'productFreightFeeAmazon15', label: '1-5(Amazon)美金',   width: '127px',},
 { sortable: 'custom',prop: 'productFreightFeeFedex18', label: '1-8(FedEx)美金',   width: '112px',},
 { sortable: 'custom',prop: 'productFreightFeeFedex15', label: '1-5(FedEx)美金',   width: '112px',},
 { sortable: 'custom',prop: 'productLastTripPrice', label: '尾程费用(RMB)',   width: '120px',tipmesg:'按美金汇率7.24计算'},
 { sortable: 'custom',prop: 'productTotalSupplyPrice', label: '总成本(RMB)',   width: '100px'},
 { sortable: 'custom',prop: 'productPricing', label: '定价(RMB)',   width: '90px'},
 { sortable: 'custom',prop: 'remarks', label: '备注',   width: '90px'},
 {type:'button',label:'操作',width:'100px',btnList:[
  {label:"删除", handle:(that,row)=>that.onDelete(row,'tableCols')},
  {label:"修改备注", handle:(that,row)=>{that.editNoteVisible = true ;that.editNoteForm = JSON.parse(JSON.stringify(row))}}

  ]},
];

const trialtableCols=[
{ prop: 'product_sku', label: 'SKU(竞品ID)',  width: '150px' },
 { prop: 'container_batch', label: '货柜批次', width: '120px', },
 {prop:'product_length',label: '长 (cm)',  width: '50px' ,},
 { prop: 'product_width', label: '宽 (cm)',  width: '50px',},
 {prop: 'product_height', label: '高 (cm)',  width: '50px',},
 {prop: 'product_volume', label: '体积 (cm³)',  width: '65px',},
 {prop: 'product_count', label: '个数',width: '65px',},
 {prop: 'product_first_trip_price', label: '头程价格(RMB)',   width: '100px',},
 {prop: 'product_supply_price', label: '产品成本(RMB)',   width: '100px',},
 {prop: 'product_weight', label: '重量 (kg)',   width: '65px',},
 {prop: 'product_freight_fee_weight', label: '物流计费重量 (磅)',   width: '110px',},
 {prop: 'product_freight_fee_usps18', label: '1-8(USPS)美金',   width: '90px',},
 {prop: 'product_freight_fee_usps15', label: '1-5(USPS)美金',   width: '90px',},
 {prop: 'product_freight_fee_ups18', label: '1-8(UPS)美金',   width: '90px',},
 {prop: 'product_freight_fee_ups15', label: '1-5(UPS)美金',   width: '90px',},
 {prop: 'product_freight_fee_amazon18', label: '1-8(Amazon)美金',   width: '110px',},
 {prop: 'product_freight_fee_amazon15', label: '1-5(Amazon)美金',   width: '110px',},
 {prop: 'product_freight_fee_fedex18', label: '1-8(FedEx)美金',   width: '100px',},
 {prop: 'product_freight_fee_fedex15', label: '1-5(FedEx)美金',   width: '100px',},
 {prop: 'product_last_trip_price', label: '尾程费用(RMB)',   width: '115px',tipmesg:'按美金汇率7.24计算'},
 {prop: 'product_total_supply_price', label: '总成本(RMB)',   width: '80px'},
 {prop: 'product_pricing', label: '定价(RMB)',   width: '75px'},

]

const addBatchtableCols = [
 {  prop: 'batchDate', label: '日期',  width: '200px',formatter: (row) => { return dayjs(row.batchDate).format('YYYY-MM-DD')  }},
 { prop: 'containerBatchNo', label: '货柜批次',  width: 'auto',},
 { prop: 'containerBatchFee', label: '整柜费用',  width: '120px',formatter:(row)=>{return formatmoney(row.containerBatchFee)}},
 { prop: 'updateName', label: '操作人',width: '120px',},
 {type:'button',label:'操作',width:'100px',btnList:[
  {label:"删除",/*display:(row)=>{return true},display:true,*/ handle:(that,row)=>that.onDelete(row,'addBatchtableCols')}]}
];
 
//物流列表
const logisticstableCols = [
    {sortable: 'custom',prop: 'weightG', label: '对应克',   width: '60px',},
    {sortable: 'custom',prop: 'weightOZ', label: '对应盎司',   width: '80px',},
    {sortable: 'custom',prop: 'weightKG', label: '对应千克',   width: '80px',},
    {sortable: 'custom',prop: 'weightLB', label: '计费重量（磅）',   width: '120px',},
    {sortable: 'custom',prop: 'zone1', label: 'Zone1',   width: '60px',},
    {sortable: 'custom',prop: 'zone2', label: 'Zone2',   width: '60px',},
    {sortable: 'custom',prop: 'zone3', label: 'Zone3',   width: '60px',},
    {sortable: 'custom',prop: 'zone4', label: 'Zone4',   width: '60px',},
    {sortable: 'custom',prop: 'zone5', label: 'Zone5',   width: '60px',},
    {sortable: 'custom',prop: 'zone6', label: 'Zone6',   width: '60px',},
    {sortable: 'custom',prop: 'zone7', label: 'Zone7',   width: '60px',},
    {sortable: 'custom',prop: 'zone8', label: 'Zone8',   width: '60px',},
    {sortable: 'custom',prop: 'zone9', label: 'Zone9',   width: '60px',},
]


//物流列表 =》日志
const logtableCols = [
    {prop: 'createTime', label: '时间',   width: '160px',},
    {prop: 'moduleName', label: '事件',   width: '170px',},
    {prop: 'createName', label: '操作人',   width: 'auto',},
]



export default {
 name: 'temuHalfTrusteeship',
 components: { vxetablebase, MyContainer,skuList },
 data() {
   return {
        
        that:this,
        //主体
        batchList:[],
        queryEnum:{
            productSku:'',
            containerBatchNo:'',
            orderBy: '',
            isAsc: true,
        },
        loadingBatchList:false,
        total:0,
        tableCols:tableCols,
        tableData:[],
        loading:false,
        selids:[],
        //新增批次弹窗
        addBatchtableCols:addBatchtableCols,
        addBatchData:[],
        addBatchVisible:false,
        addBatchTotal:0,
        addBatchLoading:false,
        addBatchForm:{
            batchDate:'',
            supplierWarehouse:'',
            containerBatchFee:undefined,
        },
        addBatchPageListInfo:{
            orderBy: '',
            isAsc: true,
        },
        warehouseList:[],
        //addsku and 试算部分
        currentRadioRow:{},
        oldCurrentRadioRow:{},
        trialForm:{
            batch:'',
            info:3,
            Id:'',
        },
        trialData:[],
        trialtableCols:[],
        showTrialTable:false,
        trialLoading:false,
        trialtableCols:trialtableCols,
        skuListVisible:false,
        rules:{
            batch: [
            { required: true, message: '请选择批次', trigger: 'blur' },
          ], 
          price: [
            { required: true, message: '请输入成本', trigger: 'blur' },
          ], 
            c: [
            { required: true, message: '请输入长度', trigger: 'blur' },
            // { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
          ],
           k: [
            { required: true, message: '请输入宽度', trigger: 'blur' },
          ], 
          g: [
            { required: true, message: '请输入高度', trigger: 'blur' },
          ],
          kg: [
            { required: true, message: '请输入重量', trigger: 'blur' },
          ],
        },

        //导入文件列表
        fileList:[],
        dialogVisible:false,//导入弹窗
        fileparm:{},//上传文件参数
        uploadLoading:false,
        //物流报价明细
        logisticsVisible:false,
        logisticsTotal:0,
        logisticsData:[],
        logisticstableCols:logisticstableCols,
        logisticsLoading:false,
        logisticsQuery:{
            platformShippingMethod:'',
        },
        godownList:[],
        //物流报价明细 =》 操作日志
        logVisible:false,
        logData:[],
        logtableCols:logtableCols,
        logLoading:false,
        logTotal:0,
        //修改备注
        editNoteVisible:false,
        editNoteForm:{
            remarks:''
        }
        
   };
 },
  async mounted(){
    
    await this.getList()
  },
 methods:{
    //打开日志弹窗
    logVisibleOpen(){

        this.logVisible = true;
        this.$nextTick(()=>{
            this.logListGet('search')
        })
    },
    async logListGet(type){
        if(type == 'search'){
            this.$refs.logPage.setPage(1)
        }
        let pager = this.$refs.logPage.getPager()

        let params = {
          
            page:pager.currentPage,
            pageSize:pager.pageSize
        }
        this.logLoading = true
        const {data,total}= await getImportLogPageList(params) //该接口
        this.logLoading = false

        this.logTotal  = total
        this.logData = data
    },
    

    //获取批次列表
    async getListContainerBatch(type){
        console.log(type,'current type')
        this.loadingBatchList = true
        const {data} =  await listContainerBatch()
        this.loadingBatchList = false
        this.batchList = data  
    },
    async onSubmit(formName){
        await this.$refs[formName].validate( async(valid) => {
          if (valid) {
            console.log(dayjs(this.addBatchForm.batchDate).format('YYYY-MM-DD'),'输入日期查看')
            let params = {
                batchDate:dayjs(this.addBatchForm.batchDate).format('YYYY-MM-DD'),
                supplierWarehouse:this.addBatchForm.supplierWarehouse,
                containerBatchFee:this.addBatchForm.containerBatchFee,
            }
            const {data} =   await saveContainerBatch(params)

            if(data){
                this.addBatchList()
                this.$message({
                    message: '保存批次成功',
                    type: 'success'
                });
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
    },
    closeSkuList(){
        this.currentRadioRow = this.$refs.skuList.currentRadio?this.$refs.skuList.currentRadio :{}
        if(this.currentRadioRow?.product_sku!==this.oldCurrentRadioRow?.product_sku){
            this.$refs.skuList.setSelectRow(this.oldCurrentRadioRow)
            this.$refs.skuList.currentRadio = this.oldCurrentRadioRow
            this.currentRadioRow  = this.oldCurrentRadioRow
        }
      
    },
    
    onSkuList(){
        
        this.currentRadioRow = this.$refs.skuList.currentRadio?this.$refs.skuList.currentRadio :{}
        this.oldCurrentRadioRow =  this.currentRadioRow 
        this.skuListVisible = false
    },
    resetAddVatch(fromName){
        this.$refs[fromName].resetFields()
    },
    async getList(type){
        if(type == 'search'){
            this.$refs.pager.setPage(1)
        }
        let pager = this.$refs.pager.getPager()

        let params = {
            ...this.queryEnum,
            page:pager.currentPage,
            pageSize:pager.pageSize
        }
        this.loading = true
        const {data,total}= await pageListSkuPricing(params)
        this.loading = false

        this.total  = total
        this.tableData = data
    },
    async handleExport(){
        
        this.loading = true
        const { data } = await skuExport({})
        this.loading = false
        const aLink = document.createElement("a");
        let blob = new Blob([data], { type: "application/vnd.ms-excel;charset=utf-8"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', 'Temu半托定价' + dayjs().format('YYYY_MM_DD') +'.xlsx')
        aLink.click()
    },
    onDelete(row,type){
       
        this.$confirm('确认要删除这条数据吗？','提示',{
        type:'warning',
        center:true,
        customClass:'tipBox'
        })
        .then(async() => {
            let res
            if(type == 'tableCols'){

                 res = await deleteSkuPricing([row.productSku])
            }else{
                res = await deleteContainerBatch([row.id])
            }
            if(res.isSuccess){
                this.$message({
                    message: '删除数据成功',
                    type: 'success'
                });
                if(type !== 'tableCols' ){
                     //做了删除批次操作吧外面 两个选择框重置一次
                    
                     this.$refs.trialForm.resetFields('batch')
                    this.queryEnum.containerBatchNo = ''
                }
               type == 'tableCols'? this.getList('search'):this.addBatchList()
                // type =='tableCols'?this.getList('search'):

            }
        })
        .catch(_ => {});
    },
    addSKU(){
        // 添加sku 不做检验，没有分页只做模糊查询
        //   fuzzyQuerySku()
        this.skuListVisible = true
        nextTick(()=>{
            this.oldCurrentRadioRow = this.$refs.skuList.currentRadio?this.$refs.skuList.currentRadio :{}
        })
    },
    async onBatchDelete(){
        if (this.selids.length==0) {
       this.$message({type: 'warning',message: '请先选择!'});
       return;
      } 
      this.$confirm('确认要批量删除数据吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {

            const res = await deleteSkuPricing(this.selids)
            if (!res?.isSuccess) {return }
            this.$message({type: 'success',message: '删除成功!'});
            this.getList('search')
        }).catch(() => {
          this.$message({type: 'info',message: '已取消删除'});
        });
    },
    selectchange(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.productSku);
      })
    },
   async onTrialForm(formName,type){
        //试算校验  校验必填项  如手动输入除了Id
        
      await  this.$refs[formName].validate(async(valid) => {
            if(!this.currentRadioRow?.product_sku&&this.trialForm.info==3){
                this.$message({
                    message: '请先添加一条sku数据',
                    type: 'warning'
                });
                return
            }
          if (valid) {
           
            let params={
                productSku:this.trialForm.info == 3?this.currentRadioRow?.product_sku:this.trialForm.Id,
                containerBatchId:this.trialForm.batch,
                isManuallySpecifySkuAttr:this.trialForm.info == 3?false:true,
                productWeight:this.trialForm.kg,
                productLength:this.trialForm.c,
                productWidth:this.trialForm.k,
                productHeight:this.trialForm.g,
                productSupplyPrice:this.trialForm.price,
            }
            if(this.trialForm.info == 6&&!this.trialForm.Id){
                this.$message({
                    message: '手工输入保存请先填写完整竞品ID',
                    type: 'warning'
                });
                return
            }
            this.trialLoading = true
            const  {data,isSuccess,message} = await calSkuPricing(params)
            this.trialLoading = false

            if(!isSuccess){
                this.$message({
                    message: message,
                    type: 'warning'
                });
                return
            }else{
                this.$message({
                    message: '试算成功',
                    type: 'success'
                });
            }
            this.trialData = data

            this.showTrialTable = true
            console.log(data,params,'现在的试算传递')
            //校验有没有选择sku 没有提示

          } else {
            console.log('error submit!!');
            return false;
          }
        });
    },
    // 排序
    sortchange({ order, prop }) {
      if (prop) {
        this.queryEnum.orderBy = prop
        this.queryEnum.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async onTrialSave(){
        if(!this.trialData.length){
            this.$message({
                    message: '当前没有试算结果，请先试算再保存',
                    type: 'warning'
                });
                return
        }
        const data  = await saveSkuPricing({list:this.trialData})
        if(data.isSuccess){
            this.$message({
                message: '保存成功',
                type: 'success'
            });
            this.getList('search')
        }
    },
    async openAdd(){
        this.addBatchVisible = true
        const {data} = await getWarehouseList() 
        await this.addBatchList()
        this.warehouseList = data
    },
    //批次获取列表
    async addBatchList(){
        
        let pager = this.$refs.addBatchPage.getPager()

        let params = {
            ...this.addBatchPageListInfo,
            page:pager.currentPage,
            pageSize:pager.pageSize
        }
        this.addBatchLoading = true
        const {data,total}= await pageListContainerBatch(params)
        this.addBatchLoading = false

        this.addBatchTotal  = total
        this.addBatchData = data
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("exeSql", true);
      var res = await importWarehouseShipmentPricing(form);
      if (res?.isSuccess){
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
        //刷新页面
        this.logisticsListGet('search')
        
      }else{
        this.$message({message :res.message,type:'error'})
      }
      this.uploadLoading = false
        this.dialogVisible = false;
        
    //   await this.getList()   //导入成功调刷新数据
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },


    //修改备注
    async editNoteSet(){

        // 调接口修改 
        let res =  await upDateRemarks(this.editNoteForm)
        this.$message({message:res.message,type:res.isSuccess?'success':'error'})
        this.getList()
        this.editNoteVisible = false
        this.editNoteForm = {}
    },
    //打开报价弹窗加查询列表
    async logisticsVisibleOpen(){
        this.logisticsVisible = true
        //获取仓库物流报价列表
        if(!this.godownList.length){
            let {data} = await getShippingMethodFeeGroupList()
            this.godownList = data
            //默认选中第一个仓库物流
            this.logisticsQuery.platformShippingMethod =  this.godownList[0].value
        }
        //
       this.$nextTick(()=>{
            this.logisticsListGet('search')
       })
    },
    //查询物流列
    async logisticsListGet(type){
        console.log(111)
        if(type == 'search'){
            this.$refs.logisticsPage.setPage(1)
        }
        let pager = this.$refs.logisticsPage.getPager()

        let params = {
            ...this.logisticsQuery,
            page:pager.currentPage,
            pageSize:pager.pageSize
        }
        this.logisticsLoading = true
        const {data,total}= await getShippingMethodFeePageList(params)  //改接口
        this.logisticsLoading = false

        this.logisticsTotal  = total
        this.logisticsData = data
    },
    // //查询物流列 排序
    logisticsSortchange({ order, prop }){
      if (prop) {
        this.logisticsQuery.orderBy = prop
        this.logisticsQuery.isAsc = order.indexOf("descending") == -1 ? true : false
        this.logisticsListGet()
      }
    },
    async logisticsExport(){
        this.logisticsLoading = true
        const { data } = await exportWarehouseShipmentPricing({})
        this.logisticsLoading = false
        const aLink = document.createElement("a");
        let blob = new Blob([data], { type: "application/vnd.ms-excel;charset=utf-8"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '仓库物流渠道报价表-' + dayjs().format('YYYYMMDD') +'.xlsx')
        aLink.click()
    }
 }
};
</script>

<style lang="scss" scoped>
 ::v-deep .myheader .xTableMin {
    height:93px;
    .vxe-table--body-wrapper{
        height: 52px !important;
        min-height: 52px !important;
    }
}
.hInput ::v-deep .el-input__inner{
    width: 50px;
}
.unitStyle{
       text-align: center;
    /* display: inline-block; */
    width: 50px;
    background-color: #F5F7FA;
    color: #909399;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 29px;
    line-height: 27px;
}
::v-deep .ckg .el-form-item{
    margin-right: 0;
}
::v-deep .skuListClass .el-dialog__body{
    
    height: 489px;
}
</style>
