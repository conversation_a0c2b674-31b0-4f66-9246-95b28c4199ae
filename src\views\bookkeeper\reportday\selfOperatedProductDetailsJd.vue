<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.productName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.shopGoodsCode" placeholder="SKU" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
      </div>
    </template>
    <vxetablebase :id="'selfOperatedProductDetailsJd202411282236'"
      :tablekey="'selfOperatedProductDetailsJd202411282236'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="display: flex; align-items: baseline; height: 75px;margin-top: 10px;">
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatTime,pickerOptions,formatProCodeStutas3 } from '@/utils/tools'
import { getCodeSalesThemeAnalysis_JDSelfSupport, importCodeSalesThemeAnalysisJDSelfSupportAsync } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'

const tableCols = [
  { sortable: 'custom', width: '100', align: 'center', prop: 'yearMonthDayDate', label: '时间', },
  { sortable: 'custom', width: '200', align: 'center', prop: 'productName', label: '商品名称', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'shopGoodsCode', label: 'SKU',type:'html',formatter:(row) => row.shopGoodsCode ? formatProCodeStutas3(row.shopGoodsCode) : row.shopGoodsCode },
  { sortable: 'custom', width: '100', align: 'center', prop: 'brand', label: '品牌', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'firstLevelCategory', label: '一级类目', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'secondLevelCategory', label: '二级类目', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'thirdLevelCategory', label: '三级类目', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'shopName', label: '店铺名称', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'businessModel', label: '经营模式', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'pageViews', label: '浏览量', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'visitors', label: '访客数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'averagePageViewsPerVisitor', label: '人均浏览量', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'averageStayDuration', label: '平均停留时长', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'transactionPeople', label: '成交人数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'transactionConversionRate', label: '成交转化率', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'transactionOrders', label: '成交单量', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'transactionProductQuantity', label: '成交商品件数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'transactionAmount', label: '成交金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'transactionCustomerUnitPrice', label: '成交客单价', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'addToCartPeople', label: '加购人数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'addToCartConversionRate', label: '加购转化率', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'addToCartProductQuantity', label: '加购商品件数', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'comboProductCode', label: '组合装商品编码', },
]
const startTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
export default {
  name: "selfOperatedProductDetailsJd",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      that: this,
      ListInfo: {
        platform: 74,
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: startTime,//开始时间
        endTime: endTime,//结束时间
        productName: '',//商品名称
        shopGoodsCode: '',//SKU
      },
      timeRanges: [startTime,endTime],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importCodeSalesThemeAnalysisJDSelfSupportAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }


      this.loading = true
      const { data, success } = await getCodeSalesThemeAnalysis_JDSelfSupport(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.yearMonthDayDate = item.yearMonthDayDate ? dayjs(item.yearMonthDayDate).format('YYYY-MM-DD') : ''
        })
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
