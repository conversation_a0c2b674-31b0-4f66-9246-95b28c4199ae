<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss"
          :clearable="false" style="width: 200px;" />
        <inputYunhan ref="refgoodsCodes" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes" width="100px"
          placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500" :maxlength="1000000"
          @callback="goodsCodesCallback($event, 1)" title="商品编码" style="margin:0 2px 0 0;">
        </inputYunhan>
        <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss"
          style="width: 100px;" />
        <number-range :min.sync="ListInfo.totalOrderQtyMin" :max.sync="ListInfo.totalOrderQtyMax" min-label="总销量小"
          max-label="总销量大" class="publicCss" style="width: 130px;" />
        <number-range :min.sync="ListInfo.totalYiWuInvMin" :max.sync="ListInfo.totalYiWuInvMax" min-label="内仓总库存小"
          max-label="内仓总库存大" class="publicCss" />
        <number-range :min.sync="ListInfo.totalOutInvMin" :max.sync="ListInfo.totalOutInvMax" min-label="外仓总库存小"
          max-label="外仓总库存大" class="publicCss" />
        <el-select v-model="ListInfo.hasWmsSet" clearable filterable placeholder="设置仓库" class="publicCss"
          style="width: 100px;">
          <el-option label="是" :value="true"></el-option>
          <el-option label="否" :value="false"></el-option>
        </el-select>
        <number-range :min.sync="ListInfo.yiWuOrderCountMin" :max.sync="ListInfo.yiWuOrderCountMax" min-label="义乌订单小"
          max-label="义乌订单大" class="publicCss" :precision="0" />
        <number-range :min.sync="ListInfo.yiWuOrderQtyMin" :max.sync="ListInfo.yiWuOrderQtyMax" min-label="义乌销量小"
          max-label="义乌销量大" class="publicCss" :precision="0" />
        <number-range :min.sync="ListInfo.outOrderCountMin" :max.sync="ListInfo.outOrderCountMax" min-label="外仓订单小"
          max-label="外仓订单大" class="publicCss" :precision="0" />
        <number-range :min.sync="ListInfo.outOrderQtyMin" :max.sync="ListInfo.outOrderQtyMax" min-label="外仓销量小"
          max-label="外仓销量大" class="publicCss" :precision="0" />
        <number-range :min.sync="ListInfo.avgWeightMin" :max.sync="ListInfo.avgWeightMax" min-label="均重小"
          max-label="均重大" class="publicCss" style="width: 140px;" />

        <number-range :min.sync="ListInfo.inWmsSellStockTunDayMin" :max.sync="ListInfo.inWmsSellStockTunDayMax"
          min-label="本仓可售库存周转天数小" max-label="本仓可售库存周转天数大" class="publicCss" :precision="0" />
        <number-range :min.sync="ListInfo.wmsSellStockTunDayMin" :max.sync="ListInfo.wmsSellStockTunDayMax"
          min-label="外仓可售库存周转天数小" max-label="外仓可售库存周转天数大" class="publicCss" :precision="0" />
        <number-range :min.sync="ListInfo.fullWmsSellStockTunDayMin" :max.sync="ListInfo.fullWmsSellStockTunDayMax"
          min-label="全仓可售库存周转天数小" max-label="全仓可售库存周转天数大" class="publicCss" />
        <el-select v-model="ListInfo.yiWuEnough" clearable filterable placeholder="义乌库存是否充足" class="publicCss"
          style="width: 120px;">
          <el-option label="是" :value="true"></el-option>
          <el-option label="否" :value="false"></el-option>
        </el-select>
        <el-select v-model="ListInfo.outEnough" clearable filterable placeholder="外仓库存是否充足" class="publicCss"
          style="width: 120px;">
          <el-option label="是" :value="true"></el-option>
          <el-option label="否" :value="false"></el-option>
        </el-select>
        <!-- <el-select v-model="ListInfo.approveStatues" clearable filterable placeholder="审批状态" collapse-tags multiple
          class="publicCss" style="width: 140px;">
          <el-option label="待审批" value="待审批"></el-option>
          <el-option label="同意" value="同意"></el-option>
          <el-option label="拒绝" value="拒绝"></el-option>
        </el-select> -->
        <inputYunhan ref="packMtlCodes" :inputt.sync="ListInfo.packMtlCodes" v-model="ListInfo.packMtlCodes"
          width="80px" placeholder="包材编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
          :maxlength="1000000" @callback="goodsCodesCallback($event, 2)" title="包材编码" style="margin:0 2px 0 0;">
        </inputYunhan>
        <el-input v-model.trim="ListInfo.packMtl" placeholder="包材名称" maxlength="50" clearable class="publicCss"
          style="width: 80px;" />
        <!-- <el-select v-model="ListInfo.isMatchProvince" placeholder="匹配发货地" clearable class="publicCss"
          style="width: 80px;">
          <el-option label="一致" :value="true" />
          <el-option label="不一致" :value="false" />
        </el-select> -->
        <el-select v-model="ListInfo.isNew" clearable filterable placeholder="新增编码" class="publicCss"
          style="width: 100px;">
          <el-option label="是" :value="true"></el-option>
          <el-option label="否" :value="false"></el-option>
        </el-select>
        <el-select v-model="ListInfo.isPackGoods" clearable filterable placeholder="是否加工成品" class="publicCss"
          style="width: 110px;">
          <el-option label="是" :value="true" />
          <el-option label="否" :value="false" />
        </el-select>
        <div>
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" size="mini" @click="importProps">导入包材</el-button>
          <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
          <el-button type="primary" @click="scaleSettings" v-if="checkPermission('basicDataSetWms')">设置仓库</el-button>
          <el-button type="primary" @click="codeRemark" v-if="checkPermission('basicDataCodeRemark')">编码备注</el-button>
          <el-button type="primary" @click="PackagSet"
            v-if="checkPermission('PackagingMaterialSettings')">包材设置</el-button>
          <el-button type="primary" size="mini" @click="labelSetVisible = true">设置</el-button>
        </div>
      </div>
    </template>
    <vxetablebase ref="table" id="20241201104644" :loading="loading" :that="that" :is-index="true" :hasexpand="true"
      :tablefixed="true" :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" checkStrictly
      :is-selection="false" :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;"
      height="100%" @select="selectCheckBox" :showsummary="data.summary ? true : false" :summaryarry="data.summary"
      @sortchange="sortchange" @onTrendChart="trendChart" keyField="goodsCode" :treeProp="{
        transform: true,
        rowField: 'goodsCode',
        parentField: 'relationCode',
        lazy: true,
        hasChild: 'hasChild',
        hasChildField: 'hasChild',
        childrenField: 'children',
        accordion: true,
        loadMethod: fetchChildListApi
      }">
      <template #aIAnalysis="{ row }">
        <el-button type="text" size="mini" @click="onAIAnalysisMethod(row)"
          v-if="!row.relationCode && row.skuCount == 1">
          AI分析
        </el-button>
      </template>
      <template #goodsCode="{ row }">
        <div :style="{ color: row.isPackGoods ? '#409eff' : '', cursor: row.isPackGoods ? 'pointer' : '' }"
          @click="openProductDetails(row)">{{ row.goodsCode }}
        </div>
      </template>
      <template #yiWuInv="{ row, index }">
        <div v-if="row.yiWuCbeDetails">
          <el-popover style="overflow: hidden" placement="left" width="550"
            :trigger="row.yiWuCbeDetails.length > 0 ? 'hover' : 'manual'">
            <el-table :data="row.yiWuCbeDetails" max-height="200">
              <el-table-column property="GoodsCode" width="100" label="商品编码" show-overflow-tooltip />
              <el-table-column property="GoodsName" label="商品名称" show-overflow-tooltip />
              <el-table-column property="GoodsQty" width="50" label="数量" show-overflow-tooltip />
              <el-table-column property="Qty" width="60" label="库存" show-overflow-tooltip />
              <el-table-column property="Enough" width="80" label=">2天销量" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span :style="{ color: scope.row.Enough == 0 ? 'red' : '' }">
                    {{ scope.row.Enough == 1 ? '是' : '否' }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
            <div slot="reference" :style="{ color: row.yiWuEnough ? '' : 'red' }">{{ row.yiWuInv }}</div>
          </el-popover>
        </div>
        <span v-else>
          <div slot="reference" :style="{ color: row.yiWuEnough ? '' : 'red' }">{{ row.yiWuInv }}</div>
        </span>
      </template>
      <template #outInv="{ row, index }">
        <div v-if="row.outCbeDetails">
          <el-popover style="overflow: hidden" placement="left" width="550"
            :trigger="row.outCbeDetails.length > 0 ? 'hover' : 'manual'">
            <el-table :data="row.outCbeDetails" max-height="200">
              <el-table-column property="GoodsCode" width="100" label="商品编码" show-overflow-tooltip />
              <el-table-column property="GoodsName" label="商品名称" show-overflow-tooltip />
              <el-table-column property="GoodsQty" width="50" label="数量" show-overflow-tooltip />
              <el-table-column property="Qty" width="60" label="库存" show-overflow-tooltip />
              <el-table-column property="Enough" width="80" label=">2天销量" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span :style="{ color: scope.row.Enough == 0 ? 'red' : '' }">
                    {{ scope.row.Enough == 1 ? '是' : '否' }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
            <div slot="reference" :style="{ color: row.outEnough ? '' : 'red' }">{{ row.outInv }}</div>
          </el-popover>
        </div>
        <span v-else>
          <div slot="reference" :style="{ color: row.outEnough ? '' : 'red' }">{{ row.outInv }}</div>
        </span>
      </template>
      <template #isMatchProvince="{ row, index }">
        <div :style="{ color: row.isMatchProvince == false ? 'red' : '' }">{{ row.isMatchProvince ? '一致' : '不一致' }}
        </div>
      </template>
      <template #checkbox_header>
        <div style="display: flex;justify-content: center;align-items: center;">
          <vxe-checkbox v-model="val1" @change="changeCheckBox"></vxe-checkbox>
        </div>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-drawer :title="chatProp.title + '趋势图'" :visible.sync="chatProp.chatDialog" size="80%"
      :close-on-click-modal="false" direction="btt">
      <div v-if="!chatProp.chatLoading">
        <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" :picker-options="pickerOptions" style="margin: 10px" @change="
            trendChart({
              ...chatPropOption,
              startDate: $event[0],
              endDate: $event[1],
            }, row, true)
            " />
        <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data" />
      </div>
      <div v-else v-loading="chatProp.chatLoading" />
    </el-drawer>

    <el-dialog title="设置仓库" :visible.sync="scaleSettingsVisible" width="70%" v-dialogDrag>
      <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="80px" class="demo-ruleForm"
        v-if="scaleSettingsVisible">
        <div style="display: flex;">
          <el-form-item label="安全天数" prop="safeDay">
            <el-input-number v-model="ruleForm.safeDay" :min="0" :max="100" placeholder="安全天数" :controls="false"
              :precision="1" style="width: 130px;" />
          </el-form-item>
          <el-form-item label="仓库" prop="wmsId" label-width="60px">
            <chooseWareHouse v-model="ruleForm.wmsId" :filter="sendWmsesFilter" @chooseWms="chooseWms"
              v-if="scaleSettingsVisible" style="width: 140px;" />
          </el-form-item>
          <el-form-item label="克重区间" prop="weightMax">
            <number-range :min.sync="ruleForm.weightMin" :max.sync="ruleForm.weightMax" min-label="重量小"
              v-if="scaleSettingsVisible" max-label="重量大" style="width: 140px;" />
          </el-form-item>
          <el-form-item label="包装材料" prop="packMtlCode">
            <el-select v-model="ruleForm.packMtlCode" placeholder="请选择包装材料" style="width: 140px;" clearable filterable>
              <el-option v-for="item in basicData" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="外仓加工比例(%)" prop="rateNum" label-width="120px">
            <el-input-number v-model="ruleForm.rateNum" :min="0" :max="100" placeholder="外仓加工比例(%)" :precision="2"
              :controls="false" style="width: 140px;" />
          </el-form-item>
          <el-form-item label="是否锁定" prop="isLock">
            <el-select v-model="ruleForm.isLock" clearable filterable placeholder="是否锁定"
              style="width: 110px;">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </el-form-item>
        </div>
        <vxetablebase ref="table" v-if="scaleSettingsVisible" :that="that" :is-index="true" :hasexpand="true"
          :tablefixed="true" :has-seq="false" :border="true" :table-data="ruleForm.list" :table-cols="tableCols1"
          :is-selection="false" :is-select-column="true" :is-index-fixed="false" :isRemoteSort="false"
          style="width: 100%;height: 300px; margin: 0;">
        </vxetablebase>
        <div>共 {{ ruleForm.list ? ruleForm.list.length : 0 }} 条</div>
        <div style="display: flex;justify-content: center;align-items: center;margin-top: 10px;">
          <el-button @click="scaleSettingsVisible = false">关闭</el-button>
          <el-button type="primary" @click="submitForm('ruleForm')" v-throttle="2000">提交</el-button>
        </div>
      </el-form>
    </el-dialog>

    <el-dialog title="编码备注" :visible.sync="codeRemarkVisible" width="20%" v-dialogDrag>
      <el-form :model="codeForm" status-icon :rules="rules1" ref="ruleForm1" label-width="120px" class="demo-ruleForm"
        v-if="codeRemarkVisible">
        <el-form-item label="备注内容" prop="remark">
          <el-input v-model="codeForm.remark" type="textarea" row="3" placeholder="请输入备注内容" maxlength="500"
            show-word-limit></el-input></el-form-item>
        <el-form-item>
          <el-button @click="codeRemarkVisible = false">关闭</el-button>
          <el-button type="primary" @click="codeRemarkSubmit('ruleForm1')" v-throttle="1000">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog title="包材设置" :visible.sync="PackagSetVisible" width="20%" v-dialogDrag>
      <el-form :model="PackagSetForm" status-icon :rules="rules2" ref="ruleForm2" label-width="120px"
        class="demo-ruleForm" v-if="PackagSetVisible">
        <el-form-item label="包装材料" prop="packMtlCode">
          <el-select v-model="PackagSetForm.packMtlCode" placeholder="请选择包装材料" style="width: 200px;" clearable
            filterable>
            <el-option v-for="item in basicData" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="PackagSetVisible = false">关闭</el-button>
          <el-button type="primary" @click="PackagSetSubmit('ruleForm2')" v-throttle="1000">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div slot="title" class="header-title">
        <span class="title-text"><span>导入数据</span></span>
        <span class="title-close"><el-button @click="downLoadFile">下载模版</el-button></span>
      </div>
      <div>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <vxe-modal title="AI分析" v-model="aiProcodeModal.visible" width="80%" height="80%" :mask-closable="true"
      marginSize='-500'>
      <basicDataAIAnalysis v-if="aiProcodeModal.visible" ref="basicDataAIAnalysis" :info="aiProcodeModal.info" />
    </vxe-modal>

    <el-dialog title="标签设置" :visible.sync="labelSetVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <labelSet v-if="labelSetVisible" @close="labelSetVisible = false" />
    </el-dialog>

    <el-dialog :title="productTitle" :visible.sync="productVisible" width="80%" v-dialogDrag
      :close-on-click-modal="false">
      <finishedOrSemiFinished v-if="productVisible" :query="query" />
    </el-dialog>

  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode, downloadLink } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/SaleItems/Weight/'
import { mergeTableCols } from '@/utils/getCols'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import finishedOrSemiFinished from './finishedOrSemiFinished.vue'
import basicDataAIAnalysis from "@/views/order/singleProduct/components/basicDataAIAnalysis.vue";
import {
  getList as getGoodsList,
} from "@/api/inventory/basicgoods"
import decimal from "@/utils/decimalToFixed";
import labelSet from './labelSet.vue'
export default {
  name: "expressAverageWeight",
  components: {
    MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, chooseWareHouse, basicDataAIAnalysis, labelSet, finishedOrSemiFinished
  },
  data() {
    return {
      aiProcodeModal: {
        visible: false,
        info: {},
      },
      uploadLoading: false,
      fileList: [],
      file: null,
      fileparm: {},
      importVisible: false,
      val1: false,
      api,
      labelSetVisible: false,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'yiWuOrderQty',
        isAsc: false,
        summarys: [],
        startDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
        endDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
        orderCountMin: undefined,
        orderCountMax: undefined,
        avgWeightMin: undefined,
        avgWeightMax: undefined,
        isMatchProvince: null,
        isNew: null,
        type: 0,
      },
      data: {},
      chatProp: {
        chatDialog: false, // 趋势图弹窗
        chatTime: null, // 趋势图时间
        chatLoading: true, // 趋势图loading
        data: [], // 趋势图数据
        title: '', // 趋势图标题
      },
      timeRanges: [],
      tableCols: [],
      tableCols1: [],
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
      isExport: false,
      selectList: [],
      scaleSettingsVisible: false,
      ruleForm: {
        safeDay: undefined,
        wmsId: null,
        wmsName: '',
        packMtlCode: '',
        rateNum: undefined,
        rate: null,
        weightMin: undefined,
        weightMax: undefined,
        isLock: false,
        list: []
      },
      codeForm: {
        remark: ''
      },
      codeRemarkVisible: false,
      rules: {
        safeDay: [{ required: true, message: '请输入安全天数', trigger: 'blur' }],
        wmsId: [{ required: true, message: '请选择仓库', trigger: 'change' }],
      },
      rules1: {
        remark: [{ required: true, message: '请输入备注内容', trigger: 'blur' }],
      },
      rules2: {
        packMtlCode: [{ required: true, message: '请选择包装材料', trigger: 'change' }],
      },
      basicData: [],
      PackagSetForm: {
        packMtlCode: ''
      },
      PackagSetVisible: false,
      row: null,
      productVisible: false,
      productTitle: '',
      query: {}
    }
  },
  async mounted() {
    this.init()
    await this.getCol();
    await this.getList()
  },
  methods: {
    async fetchChildListApi({ row }) {
      const params = {
        ...this.ListInfo,
        type: 1,
        relationCode: row.goodsCode,
      }

      const { data, success } = await request.post(`${this.api}PageGetData`, params)

      for (const item of data.list) {

        if (item.outCbeDetails) {
          try {
            item.outCbeDetails = JSON.parse(item.outCbeDetails);
          } catch (e) {
            console.error('Error parsing yiWuCbeDetails:', e);
          }
        }
        if (item.yiWuCbeDetails) {
          try {
            item.yiWuCbeDetails = JSON.parse(item.yiWuCbeDetails);
          } catch (e) {
            console.error('Error parsing outCbeDetails:', e);
          }
        }
      }


      return data.list
    },

    async openProductDetails(row) {
      if (!row.isPackGoods) return
      this.productTitle = `${row.goodsCode} - 加工成品`
      this.query = row
      this.productVisible = true
    },
    importProps() {
      this.fileList = []
      this.file = null
      this.importVisible = true
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.importVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("file", item.file);
      var res = await request.post(`${this.api}ImportPackMtl`, form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.importVisible = false;
      await this.getList()
    },
    downLoadFile() {
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250216/1891025598059085825.xlsx', '包装耗材导入模版.xlsx');
    },
    changeCheckBox() {
      this.$refs.table.setCheckBoxAll(this.val1)
    },
    PackagSetSubmit(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const res = this.selectList.map(item => {
            return {
              packMtlCode: this.PackagSetForm.packMtlCode,
              goodsCode: item.goodsCode,
            }
          })
          const { success } = await request.post(`/api/verifyOrder/SaleItems/Weight/BatchEditPackMtl`, res)
          if (success) {
            this.$message({
              type: 'success',
              message: '保存成功!'
            });
            this.PackagSetVisible = false
            this.getList()
            this.selectList = []
          }
        } else {
          return false;
        }
      });
    },
    PackagSet() {
      if (this.selectList.length == 0) return this.$message.error('请选择要设置的数据!')
      this.PackagSetForm.packMtlCode = null
      this.PackagSetVisible = true
    },
    async init() {
      const params = {
        currentPage: 1,
        pageSize: 2000,
        brandId: ['5'],
        groupId: ['23'],
        unBrandId: ["23", "12", "141", "150", "10101"]
      }
      const { data, success } = await getGoodsList(params)
      if (success) {
        this.basicData = data.list.map(item => {
          return {
            label: item.goodsCode + ' - ' + item.goodsName,
            value: item.goodsCode
          }
        })
      }
    },
    codeRemarkSubmit(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const res = this.selectList.map(item => {
            return {
              remark: this.codeForm.remark,
              goodsCode: item.goodsCode,
              date: item.date
            }
          })
          const { success } = await request.post(`/api/verifyOrder/SaleItems/Weight/BatchEditRemark`, res)
          if (success) {
            this.$message({
              type: 'success',
              message: '保存成功!'
            });
            this.codeRemarkVisible = false
            this.getList()
            this.selectList = []
          }
        } else {
          return false;
        }
      });
    },
    codeRemark() {
      if (this.selectList.length == 0) return this.$message.error('请选择要设置的数据!')
      this.codeForm.remark = ''
      this.codeRemarkVisible = true
      this.$nextTick(() => {
        this.$refs.ruleForm1.resetFields();
      });
    },
    chooseWms(wms) {
      this.ruleForm.wmsName = wms.name
    },
    sendWmsesFilter(wmses) {
      console.log(wmses);
      this.wmsesList = wmses;
      return wmses.filter((a) => a.name.includes('【昀晗-'));
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const res = this.selectList.map(item => {
            item.safeDay = this.ruleForm.safeDay
            item.wmsName = this.ruleForm.wmsName
            item.oldWmsId = item.wmsId
            item.wmsId = this.ruleForm.wmsId
            item.packMtlCode = this.ruleForm.packMtlCode
            item.rate = this.ruleForm.rateNum ? decimal(this.ruleForm.rateNum, 100, '/') : null
            item.weightMin = this.ruleForm.weightMin
            item.weightMax = this.ruleForm.weightMax
            item.isLock = this.ruleForm.isLock
            return item
          })
          const { success } = await request.post(`/api/verifyOrder/SaleItems/WmsCode/ChangeWms`, res)
          if (success) {
            this.$message({
              type: 'success',
              message: '保存成功!'
            });
            this.scaleSettingsVisible = false
            this.getList()
            this.selectList = []
          }
        } else {
          return false;
        }
      });
    },
    async scaleSettings() {
      if (this.selectList.length == 0) return this.$message.error('请选择要设置的数据!')
      this.ruleForm = {
        safeDay: undefined,
        wmsId: null,
        wmsName: '',
        packMtlCode: '',
        rateNum: undefined,
        rate: null,
        weightMin: undefined,
        weightMax: undefined,
        isLock: false,
        list: []
      }
      await this.getRuleFromCols()
      await this.getRuleFormList()
      this.scaleSettingsVisible = true
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
      });
    },
    async getRuleFormList() {
      const res = this.selectList.map(item => {
        return item.goodsCode
      })
      const { data, success } = await request.post(`/api/verifyOrder/SaleItems/WmsCodeRelation/Get`, res)
      if (success) {
        this.ruleForm.list = data
      }
    },
    async getRuleFromCols() {
      const { data, success } = await request.post(`/api/verifyOrder/SaleItems/WmsCodeRelation/GetColumns`)
      if (success) {
        this.tableCols1 = data
      }
    },
    selectCheckBox(val) {
      this.selectList = JSON.parse(JSON.stringify(val))
    },
    async trendChart(option, row, isDrawer) {
      var endDate = null;
      var startDate = null;
      this.row = JSON.parse(JSON.stringify(row))
      const res = JSON.parse(JSON.stringify(option))
      if (!isDrawer) {
        res.endDate = this.ListInfo.endDate
        res.startDate = dayjs(res.endDate).subtract(7, 'day').format('YYYY-MM-DD');
      }
      if (res.startDate && res.endDate) {
        startDate = res.startDate;
        endDate = res.endDate;
      } else {
        endDate = res.date;
        startDate = new Date(res.date);
        startDate.setDate(res.date.getDate() - 30);

        startDate = dayjs(startDate).format("YYYY-MM-DD");
        endDate = dayjs(endDate).format("YYYY-MM-DD");
      }
      res.filter.filters = res.filter.filters.filter((item) => item.field !== res.dateField);
      res.filter.filters.push({
        field: res.dateField,
        operator: "GreaterThanOrEqual",
        value: startDate,
      });
      res.filter.filters.push({
        field: res.dateField,
        operator: "LessThanOrEqual",
        value: endDate,
      });
      res.startDate = startDate;
      res.endDate = endDate;
      if (row.isRoot) {
        res.condition.type = 0;
      } else {
        res.condition.type = 1;
      }
      this.chatProp.chatTime = [startDate, endDate];
      this.chatProp.chatLoading = true;
      const { data, success } = await await request.post(`${this.api}GetTrendChart`, res);
      if (success) {
        this.chatProp.title = JSON.parse(JSON.stringify(data.title));
        data.title = null
        this.chatProp.data = data;
      }
      this.chatProp.chatLoading = false;
      this.chatProp.chatDialog = true;
      this.chatPropOption = option;
    },
    //商品编码
    goodsCodesCallback(e, val) {
      if (val == 1) {
        this.ListInfo.goodsCodes = e
      } else {
        this.ListInfo.packMtlCodes = e
      }
    },
    async exportProps() {
      this.isExport = true
      await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
        this.isExport = false
      })
    },
    onAIAnalysisMethod(row) {
      this.aiProcodeModal.info = { ...row };
      this.aiProcodeModal.visible = true;
    },
    async getCol() {
      const { data, success } = await request.post(`${this.api}GetColumns`)
      if (success) {
        data.forEach(item => {
          if (item.prop == 'orderNoInner') {
            item.type = 'orderLogInfo'
            item.orderType = 'orderNoInner'
          }
          if (item.prop == 'proCode') {
            item.type = 'html'
            item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
          }
        })
        let a = mergeTableCols(data)
        a.unshift({ label: 'AI', prop: 'aIAnalysis', width: '60', align: 'center', fixed: 'left' },)
        a.unshift({ label: '', type: 'checkbox' })
        this.tableCols = a
        this.ListInfo.summarys = data
          .filter((a) => a.summaryType)
          .map((a) => {
            return { column: a["sort-by"], summaryType: a.summaryType };
          });
      }
    },
    async getList(type) {
      if (type === "search") {
        this.ListInfo.currentPage = 1;
        this.$refs.pager.setPage(1);
      }
      this.loading = true;
      try {
        const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
        if (success) {
          this.data = data;
          if (data.list) {
            data.list.forEach(item => {
              item.isRoot = true;
              if (item.outCbeDetails) {
                try {
                  item.outCbeDetails = JSON.parse(item.outCbeDetails);
                } catch (e) {
                  console.error('Error parsing yiWuCbeDetails:', e);
                }
              }
              if (item.yiWuCbeDetails) {
                try {
                  item.yiWuCbeDetails = JSON.parse(item.yiWuCbeDetails);
                } catch (e) {
                  console.error('Error parsing outCbeDetails:', e);
                }
              }
              // if (item.children && item.children.length > 0) {
              //   item.children.forEach(child => {
              //     if (child.outCbeDetails) {
              //       try {
              //         child.outCbeDetails = JSON.parse(child.outCbeDetails);
              //       } catch (e) {
              //         console.error('Error parsing yiWuCbeDetails:', e);
              //       }
              //     }
              //     if (child.yiWuCbeDetails) {
              //       try {
              //         child.yiWuCbeDetails = JSON.parse(child.yiWuCbeDetails);
              //       } catch (e) {
              //         console.error('Error parsing outCbeDetails:', e);
              //       }
              //     }
              //   });
              // }
            });
          }
          if (data.summary) {
            Object.keys(data.summary).forEach(key => {
              if (key == 'yiWuOrderRate_sum' || key == 'outOrderRate_sum') {
                data.summary[key] = (data.summary[key] * 100).toFixed(2) + '%';
              }
            });
          }
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.loading = false;
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;

  .publicCss {
    width: 140px;
    margin: 0 1px 5px 0;
  }
}

::v-deep .el-select__tags-text {
  max-width: 20px;
}

::v-deep .el-button+.el-button,
.el-checkbox.is-bordered+.el-checkbox.is-bordered {
  margin-left: 1px;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px 0 0;

  .title-text {
    display: flex;
    align-items: center;

    .title-close {
      margin-left: 10px;
    }
  }
}

::v-deep .vxe-cell--tree-node {
  padding-left: 0 !important;
}
</style>
