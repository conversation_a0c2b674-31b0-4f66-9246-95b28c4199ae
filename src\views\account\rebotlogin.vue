<template>
  <div class="bg">
   <div class="land">
        <div class="Loginbox">
          <div class="Loginmode">


            <!-- 屏蔽 star -->
            <div>
              <el-menu
                :default-active="activeIndex"
                class="el-menu-demo"
                mode="horizontal"
                collapse-transition
              >
                  <el-menu-item style="width: 185px; line-height: 80px" @click="activeIndex='1'" index="1"
                  >手机号登陆</el-menu-item>
                <el-menu-item style="width: 185px; line-height: 80px"  @click="activeIndex='2'" index="2"
                  >二维码登陆</el-menu-item>
              </el-menu>
            </div>
            <!-- 屏蔽 end -->


          </div>



          <!-- 二维码扫描 star -->
          <!-- <div> -->
            <div class="QRcode" :style="activeIndex=='2'?{ 'margin': '20px auto'}:{}" id="qrcodemy">
              <iframe v-if="activeIndex=='2'" id="ddloginiframe" src="/dingding/ddlogin.html"  style="margin-left:-6%;" frameborder="no" border="0" marginwidth="0" marginheight="0"
                  scrolling="no" allowtransparency="yes" width="350px" height="350px">
              </iframe>
              <el-form ref="form" :model="form" :rules="formRules" size="small" v-else>

                <div style="width: 370px; height: 350px;" >
                <div >
                  <div style="width: 100%; display: flex; margin-left: auto; ">
                    <div style="margin-left: auto;"></div>
                    <el-dropdown @command="commandclick">
                      <span class="el-dropdown-link">
                        {{ subobj.title }}<i class="el-icon-arrow-down el-icon--right"></i>
                      </span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :command="item" v-for="(item,i) in componey" :key="i">{{ item.title }}</el-dropdown-item>
                        <!-- <el-dropdown-item command="b">瀚笃服饰</el-dropdown-item> -->
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                  
                <div class="Inputbox" style="margin-top: 10px;">
                  <el-form-item prop="userName">
                    <el-input ref="userName" height="40px" v-model="form.userName" type="text" auto-complete="off" placeholder="请输入账号" @keyup.enter.native="onLogin">
                      <template #prefix>
                        <i class="el-input__icon el-icon-user" />
                      </template>
                    </el-input>
                  </el-form-item>
                </div>
                <div class="Inputbox">
                  <el-form-item prop="password">
                    <el-input style="height: 40px;" ref="password" v-model="form.password" auto-complete="new-password" show-password placeholder="请输入密码" @keyup.enter.native="onLogin">
                      <template #prefix>
                        <i class="el-input__icon el-icon-lock" />
                      </template>
                    </el-input>
                  </el-form-item>
                </div>
                <div style="display: inline-block">
                  <el-form-item prop="verifyCode">
                    <el-input ref="verifyCode" v-model="form.verifyCode" type="text" clearable maxlength="4" auto-complete="off" placeholder="验证码"
                        class="verifyCode" style="width:66%; height: 40px;" @keyup.enter.native="onLogin">
                      <template #prefix>
                        <i class="el-input__icon fa fa-shield" />
                      </template>
                    </el-input>
                    <img :src="verifyCodeUrl" alt style="width:33%;cursor: pointer;vertical-align: middle;" @click="getLoginVerifyCode">
                  </el-form-item>
                </div>
                <div style="margin: auto; margin-top: 30px;">
                  <el-button type="primary" :loading="loginLoading"  round style="width:100%; height: 40px;" @click.native.prevent="onLogin">{{ loginText }}</el-button>
                </div>
              </div>
              </div>

              </el-form>
              

            </div>
          <!-- </div> -->
            <!-- 钉钉授权登录 -->
          <!-- <div v-if="activeIndex=='1'" style="display: flex; justify-content: center; align-items: center; height: 80px; flex-direction: column; margin-top: -40px;">
                <img
                  src="../../static/images/dingding.png"
                  alt=""
                  style="display: block; margin: 10px auto"
                  @click="dingdinglogin"
                />
                <div style="width: 160px;margin: 0 auto;text-align: center;font-size: 14px;color: #999;">
                  使用钉钉快捷登陆
                </div>
            </div> -->
        </div>
      </div>
 </div>
</template>
<script>
import Cookies from 'js-cookie'
import { getVerifyCode,ddurl,getCompany } from '@/api/admin/auth'
export default {
  name: 'login',
  data() {
    return {
      shouyeUrl: '',
      editableTabsValue: '1',
      editableTabs: [{title: '扫码登录',name: '1'}, {title: '密码登录',name: '2'}],
      tabIndex: 0,
      qrcodelogin:true,
      form: {
        userName: '',
        password: '',
        verifyCode: '',
        verifyCodeKey: '',
        passwordKey: ''
      },
      formRules: {
        userName: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        verifyCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
      },
      checked: true,
      verifyCodeUrl: '',
      loginLoading: false,
      loginText: '登录',
      activeIndex: "1",
      componey: [],
      subobj: {
        corpId: "",
        title: "昀晗贸易",
        appId: ""
      }
    }
  },
  created() {
    this.getLoginVerifyCode()
  },
 mounted() {
    var that=this;
    if(Cookies.get('token')){
        this.$store.commit('user/setToken', Cookies.get('token'))
        this.getLoginInfo()
        return;
    }
    that.getcomponey();
    window.gotourl=function(u){
      window.location.href=(u);
    }
    window.getdingdingurl= async function() {
       var url=await that.getredirecturl()
       return url;
    }
    window.qrlogin= async function(code) {
      return await that.qrlogin(code)
    }
  },
  methods: {
    async getcomponey(){
      const res = await getCompany({});
      if(!res.success){
        this.$message({type: 'error',title:'扫码错误！'})
        return
      }
      this.subobj = res.data[0];
      this.componey = res.data;
      return res.data;
    },
    commandclick(e){
      this.subobj = e;
    },
    dingdinglogin(){
      //唤醒钉钉操作
    },
    async getredirecturl(){
      const res = await ddurl({});
      return res.data;
    },
   changetab(){
    if(this.qrcodelogin) this.qrcodelogin=false;
    else this.qrcodelogin=true;
  },
  loginValidate() {
      let isValid = false
      this.$refs.form.validate(async valid => {
        isValid = valid
      })
      return isValid
      // if(reg.test(nowrouter)){
      //   var newpath = '/'+res.data.homeData.id;
      // }else{
      //   var newpath = res.data.homeData.path;
      // }
    },
    // 登录获取Token
    async onLogin() {
      if (!this.loginValidate()) {
        return
      }
      this.loginLoading = true
      this.loginText = '登录中...'
      const paras = { ...this.form, ...this.subobj }
      const res = await this.$store.dispatch('user/login', paras)
      //返回参数值
      if(res.data.homeData){
        const nowrouter = res.data.homeData.path;

        var reg = RegExp(/http/);
        var newpath = reg.test(nowrouter)?('/'+res.data.homeData.id):res.data.homeData.path;

        this.shouyeUrl = newpath;
      }

      if (!res) {
        this.loginLoading = false
        this.loginText = '重新登录'
        return
      }
      if (!res.success) {
        this.loginLoading = false
        this.loginText = '重新登录'
        switch (res.data) {
          case 1:
            this.getLoginVerifyCode()
            this.$refs.verifyCode.focus()
            break
          case 2:
            this.$refs.verifyCode.focus()
            break
          case 3:
            this.getLoginVerifyCode()
            this.$refs.userName.focus()
            break
          case 4:
            this.getLoginVerifyCode()
            this.$refs.password.focus()
            break
        }
        return
      }
      this.getLoginInfo()
    },
    async getLoginInfo() {
      const res = await this.$store.dispatch('user/getLoginInfo')
      this.loginLoading = false
      if (!res?.success) {
        this.loginLoading = false
        this.loginText = '重新登录'
        return
      }
      if (!(res.data?.menus?.length > 0)) {
        this.loginLoading = false
        this.loginText = '重新登录'
        this.$message({
          message: '该账号未分配权限，请联系管理员！',
          type: 'error'
        })
        return
      }
      const redirect = this.$route.query ? this.$route.query.redirect : ''
      // this.$router.push({ path: redirect || '/' })
      this.shouyeUrl?this.$router.push({ path: redirect || this.shouyeUrl }):this.$router.push({ path: redirect || '/' })
    },
    // 获取验证码
    async getLoginVerifyCode() {
      this.form.verifyCode = ''
      const res = await getVerifyCode({ lastKey: this.form.verifyCodeKey })
      if (res && res.success) {
        this.verifyCodeUrl = 'data:image/png;base64,' + res.data.img
        this.form.verifyCodeKey = res.data.key
      }
    },
    async qrlogin(code){
      var flag=false;
      const res = await this.$store.dispatch('user/ddlogin', {tempcode:code})
      if (res && res.success) {
        this.getLoginInfo()
        flag=true;
      // window.parent.gotourl("/")
      }
      return flag;
    }
  }
}
</script>

<style scoped>
.bg {
  height: 100%;
  width: 100%;
  margin: 0;
  background: #3a8ee6;
  background: -webkit-linear-gradient(top left, #3a8ee6 0%, #3a8ee6 100%);
  background: linear-gradient(to bottom right, #3a8ee6 0, #3a8ee6);
  /* background-image: url('/static/images/bg.png'); */
  background-image: url('../../static/images/bj2.jpg');
  opacity: 0.9;
  display: flex;
  justify-content: center;
  align-items: center;
  background-position: 50% 50%;
}
.bg ::v-deep .el-scrollbar__view {
  height: 100%;
}
.verifyCode ::v-deep .el-input__inner {
  letter-spacing: 2px;
}

@media screen and (max-width: 868px) {
  .login-card {
    width: 90%;
  }
}
</style>
<style lang="scss" scoped>
.logintabs{
  left: 50%;
  top: 50%;
  margin-left:-200px;
  margin-top: -150px;
  position:fixed;
  text-align: center;
  background-image: url('/static/images/login.png');
  display: block;
}
.login-card {
  width: 350px;
  padding: 25px 25px 5px 25px;
  position: relative;
  margin: 0 auto;
  .title {
    color: rgba(0, 0, 0, 0.85);
    font-weight: 600;
    font-size: 33px;
    font-family: "Myriad Pro", "Helvetica Neue", Arial, Helvetica, sans-serif;
    vertical-align: middle;
    margin: 0px;
    text-align: center;
  }
  .desc {
    margin-top: 12px;
    margin-bottom: 30px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
    text-align: center;
  }
  .remember {
    margin: 0px 0px 25px 0px;
  }
}
.land {
  width: 1000px;
  height: 445px;
  // background-image: url(..//assets/lt2.jpg);
  background-image: url('../../static/images/lt2.jpg');

  border-radius: 2px;
}
.Loginbox {
  width: 370px;
  height: 480px;
  background-color: rgb(255, 255, 255);
  padding: 5px 50px;
  /* float: left; */
  /* margin: 00px 0 0 480px; */
  box-shadow: 0px 2px 20px #cacaca;
  border-radius: 2px;
  position: relative;
  top: -5%;
  left: 50%;
}

.Loginbox .Loginmode {
  /* width: 220px; */
  height: 60px;
  margin: 10px auto;
  font-weight: bold;
  text-align: center;
}

.Loginbox .Inputbox {
  margin: 20px 0;
}

.QRcode {
  width: 300px;
  height: 300px;
  // background-color: #f5f5f5;
  /* margin-top: 65px; */
}
.QRcodetxt {
  width: 300px;
  margin: 0 auto;
  /* background-color: blueviolet; */
  text-align: center;
  color: #999;
}
::v-deep input.el-input__inner{
  height: 40px;
}
</style>
