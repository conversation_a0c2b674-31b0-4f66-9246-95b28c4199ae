<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select filterable v-model.trim="ListInfo.brandId" clearable multiple collapse-tags placeholder="采购"
                    class="publicCss">
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model.trim="ListInfo.company" multiple collapse-tags filterable clearable placeholder="分公司"
                    class="publicCss">
                    <el-option label="义乌" value="义乌"></el-option>
                    <el-option label="南昌" value="南昌"></el-option>
                    <el-option label="武汉" value="武汉"></el-option>
                    <el-option label="深圳" value="深圳"></el-option>
                    <el-option label="其他" value="其他"></el-option>
                </el-select>
                <el-select v-model.trim="ListInfo.title" filterable clearable multiple collapse-tags placeholder="岗位"
                    class="publicCss">
                    <el-option v-for="item in positionList" :key="item.titleName" :label="item.titleName"
                        :value="item.titleName" />
                </el-select>
                <el-select filterable v-model.trim="ListInfo.dept" clearable multiple collapse-tags placeholder="架构"
                    class="publicCss" style="width: 370px;">
                    <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                <!-- <el-button type="primary" @click="importProps">导入</el-button> -->
                <el-button type="primary" @click="batchSet">批量设置</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            @select="selectCheckBox" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="80" align="center">
                    <template #default="{ row, $index }">
                        <el-button type="text" @click="handleEdit(row)">编辑</el-button>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button type="primary" @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog :title="title" :visible.sync="editVisible" width="20%" v-dialogDrag v-loading="importLoading">
            <el-form label-width="80px" :model="ruleForm" ref="ruleForm" :rules="rules">
                <el-form-item label="下单账号" prop="account">
                    <el-select v-model.trim="ruleForm.account" clearable collapse-tags placeholder="下单账号"
                        style="width: 200px;">
                        <el-option v-for="item in accountList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="旺旺账号" v-if="title == '编辑'">
                    <el-input v-model.trim="ruleForm.wangAccount" placeholder="旺旺账号" maxlength="50" clearable
                        style="width: 200px;" />
                </el-form-item>
                <el-form-item label="旺旺密码" v-if="title == '编辑'">
                    <el-input v-model.trim="ruleForm.wangPass" placeholder="旺旺密码" maxlength="50" clearable
                        style="width: 200px;" />
                </el-form-item>
            </el-form>
            <div style="display: flex;justify-content: center;">
                <el-button @click="editVisible = false">取消</el-button>
                <el-button type="primary" @click="submitEdit('ruleForm')" v-throttle="1000">确定</el-button>
            </div>
        </el-dialog>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { getPurchaseNewPlanTurnDayDeptList, getPurchaseNewPlanTurnDayBrandList } from '@/api/inventory/purchaseordernew'
import { getAllProBrand, getBianManPositionListV2 } from '@/api/inventory/warehouse'
import { GetBianMaBrandAccountList, ExportBianMaBrandAccountFile, UpdateBianMaBrandAccount, BulkUpdateBianMaBrandAccount, ImportAliAccount } from '@/api/inventory/purorder'
const tableCols = [
    { type: 'checkbox', label: '', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'brandName', label: '姓名', },
    { width: 'auto', align: 'center', prop: 'company', label: '分公司', },
    { width: 'auto', align: 'center', prop: 'dDeptName', label: '岗位名称', },
    { width: 'auto', align: 'center', prop: 'purDept', label: '架构', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'account', label: '下单账号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'wangAccount', label: '旺旺账号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'wangPass', label: '旺旺密码', },
]

const accountList = [
    { label: '昀晗采购', value: '昀晗采购' },
    { label: '昀晗采购2', value: '昀晗采购2' },
    { label: '昀晗采购3', value: '昀晗采购3' },
    { label: '昀晗采购部门', value: '昀晗采购部门' }
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            that: this,
            accountList,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            selectList: [],
            fileList: [],
            importLoading: false,
            importVisible: false,
            file: null,
            purchasegrouplist: [],
            editVisible: false,
            ruleForm: {},
            title: '',
            brandlist: [],
            positionList: [],
            rules: {
                account: [{ required: true, message: '请选择下单账号', trigger: 'change' }]
            }
        }
    },
    async mounted() {
        this.init()
        await this.getList()
    },
    methods: {
        submitEdit(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    if (this.title == '编辑') {
                        const { success } = await UpdateBianMaBrandAccount(this.ruleForm)
                        if (success) {
                            this.$message.success('编辑成功')
                            this.editVisible = false
                            this.getList()
                        }
                    } else {
                        const { success } = await BulkUpdateBianMaBrandAccount({ ids: this.selectList, entity: this.ruleForm })
                        if (success) {
                            this.$message.success('批量设置成功')
                            this.editVisible = false
                            this.selectList = []
                            this.getList()
                        }
                    }
                } else {
                    return false;
                }
            });
        },
        handleEdit(row) {
            this.ruleForm = JSON.parse(JSON.stringify(row))
            this.title = '编辑'
            this.editVisible = true
        },
        async init() {
            let { data: deptList, success } = await getPurchaseNewPlanTurnDayDeptList();
            if (success) {
                this.purchasegrouplist = deptList.map(item => { return { value: item.dept_id, label: item.full_name }; });
            }
            var { data } = await getPurchaseNewPlanTurnDayBrandList();
            this.brandlist = data.map(item => { return { value: item.id, label: item.brandName }; });
            var resPosition = await getBianManPositionListV2();
            this.positionList = resPosition?.data;
        },
        batchSet() {
            if (this.selectList.length == 0) return this.$message.error('请选择数据')
            this.title = '批量设置'
            this.ruleForm = {}
            this.editVisible = true
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            this.importLoading = true
            await ImportAliAccount(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        selectCheckBox(val) {
            this.selectList = val.map(item => item.id)
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await ExportBianMaBrandAccountFile(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '采购账号管理' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async changeTime(e) {
            this.ListInfo.startTime = e ? e[0] : null
            this.ListInfo.endTime = e ? e[1] : null
            await this.getList()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetBianMaBrandAccountList(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>
