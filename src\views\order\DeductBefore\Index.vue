<template>
  <my-container style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="数据看版" name="first" style="height: 100%" lazy>
        <lookDataVersion ref="lookDataVersion" />
      </el-tab-pane>
      <el-tab-pane label="规则看板" name="seventh" style="height: 100%" lazy>
        <lookRuleVersion ref="lookRuleVersion" />
      </el-tab-pane>
      <el-tab-pane label="仓库看板" name="sixth" style="height: 100%" lazy>
        <lookDataVersionC ref="lookDataVersionC" />
      </el-tab-pane>
      <el-tab-pane label="待生成批次" name="second" style="height: 100%" lazy>
        <unCreateLotNumber ref="unCreateLotNumber" />
      </el-tab-pane>
      <el-tab-pane label="内仓节点维度" name="third" style="height: 100%" lazy>
        <innerWarehouse ref="innerWarehouse" />
      </el-tab-pane>
      <el-tab-pane label="外仓节点维度" name="forth" style="height: 100%" lazy>
        <outerWarehouse ref="outerWarehouse" />
      </el-tab-pane>
      <el-tab-pane label="订单维度" name="fifth" style="height: 100%" lazy>
        <orderNode ref="orderNode" />
      </el-tab-pane>
      <!-- <el-tab-pane label="违规统计" name="fifth" style="height: 100%" lazy>
        <DeductOrderNodeDtlRept ref="DeductOrderNodeDtlRept" />
      </el-tab-pane>
      <el-tab-pane label="发货时效" name="sixth" style="height: 100%" lazy>
        <DeductOrderNodeXiaoLv1 ref="DeductOrderNodeXiaoLv1" />
      </el-tab-pane>
      <el-tab-pane label="揽收时效" name="seventh" style="height: 100%" lazy>
        <DeductOrderNodeXiaoLv2 ref="DeductOrderNodeXiaoLv2" style="height: 100%" />
      </el-tab-pane> -->
    </el-tabs>
  </my-container>
</template>

<script>

import MyContainer from "@/components/my-container";
import lookDataVersion from "./components/lookDataVersion.vue";
import unCreateLotNumber from "./components/unCreateLotNumber.vue";
import innerWarehouse from "./components/innerWarehouse.vue";
import outerWarehouse from "./components/outerWarehouse.vue";
import DeductOrderNodeDtlRept from "./DeductOrderNodeDtlRept.vue";
import DeductOrderNodeXiaoLv1 from "./DeductOrderNodeXiaoLv1.vue";
import DeductOrderNodeXiaoLv2 from "./DeductOrderNodeXiaoLv2.vue";
import lookDataVersionC from "./components/lookDataVersion_c.vue";
import lookRuleVersion from "./components/lookRuleVersion.vue";
import orderNode from "./components/orderNode.vue";
export default {
  name: "Index",
  components: {
    lookDataVersion,
    unCreateLotNumber,
    innerWarehouse,
    outerWarehouse,
    DeductOrderNodeDtlRept,
    DeductOrderNodeXiaoLv1,
    DeductOrderNodeXiaoLv2,
    orderNode,
    lookDataVersionC,
    lookRuleVersion
  },
  data() {
    return {
      activeName: "first",
    };
  },
  async mounted() {
  },
  methods: {
  },
};
</script>

<style lang="scss" scoped></style>
