<template>
    <ces-table ref="table" :that='that' :isIndex='true' :isSelectColumn='true' @sortchange='sortchange' :tableData='list' 
                         tablekey='warnsummary' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
        <template slot='extentbtn'>
        <el-button-group>
          <el-button type="primary" @click="onSearch">刷新</el-button>
      </el-button-group>
      </template>
    </ces-table>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container/nofooter";
import { queryOrderWarnByGroupId} from "@/api/order/ordererror";
import {getDirectorGroupList} from '@/api/operatemanage/base/shop'
const tableCols =[
     {istrue:true,prop:'groupId',label:'运营组', width:'120',sortable:'custom',formatter:(row)=>row.groupId==0?' ': row.groupName},
     {istrue:true,prop:'qty8',label:'剩8小时订单数', width:'120',sortable:'custom',type:'click',handle:(that,row)=>that.showwarnorders(8,row.groupId)},
     {istrue:true,prop:'qty7',label:'剩7小时订单数', width:'120',sortable:'custom',type:'click',handle:(that,row)=>that.showwarnorders(7,row.groupId)},
     {istrue:true,prop:'qty6',label:'剩6小时订单数', width:'120',sortable:'custom',type:'click',handle:(that,row)=>that.showwarnorders(6,row.groupId)},
     {istrue:true,prop:'qty5',label:'剩5小时订单数', width:'120',sortable:'custom',type:'click',handle:(that,row)=>that.showwarnorders(5,row.groupId)},
     {istrue:true,prop:'qty4',label:'剩4小时订单数', width:'120',sortable:'custom',type:'click',handle:(that,row)=>that.showwarnorders(4,row.groupId)},
     {istrue:true,prop:'qty3',label:'剩3小时订单数', width:'120',sortable:'custom',type:'click',handle:(that,row)=>that.showwarnorders(3,row.groupId)},
     {istrue:true,prop:'qty2',label:'剩2小时订单数', width:'120',sortable:'custom',type:'click',handle:(that,row)=>that.showwarnorders(2,row.groupId)},
     {istrue:true,prop:'qty1',label:'剩1小时订单数', width:'120',sortable:'custom',type:'click',handle:(that,row)=>that.showwarnorders(1,row.groupId)},
     {istrue:true,prop:'qty_0',label:'严重告警订单数',tipmesg:'离最晚发货时间还剩0~3小时', width:'128',sortable:'custom',type:'click',handle:(that,row)=>that.showwarnorders(0,row.groupId)},
     {istrue:true,prop:'qty_1',label:'已延迟订单数', tipmesg:'已过最晚发货时间',width:'120',sortable:'custom',type:'click',handle:(that,row)=>that.showwarnorders(-1,row.groupId)},
    ];
    const tableHandles1=[ ];
export default {
  name: "warnsummary",
  components: {cesTable, container },
  props:{
    filter: {type:Object,default:()=>{}},
  },
  data() {
    return {
      that:this,
      filter1: {groupId: ""},
      list: [],
      listLoading: false,
      pageLoading: false,
      grouplist:[], 
      tableCols:tableCols,
      tableHandles:tableHandles1,
      pager:{OrderBy:" groupId ",IsAsc:false},
    };
  },
  async mounted() {
    await this.setGroupSelect();
    await this.onSearch();
  },
  methods: {
    async setGroupSelect(){
        var res2= await getDirectorGroupList();
        this.grouplist = res2.data?.map(item => {return { value: item.key, label: item.value };}); 
    },
    sortchange(column){
      if(!column.order)
        this.pager={OrderBy:"groupId",IsAsc:false}
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    onSearch() {
      this.getList();
    },
    async getList() {
      const params = {...this.filter,...this.pager};
      this.listLoading = true;
      const res = await queryOrderWarnByGroupId(params);
      this.listLoading = false;
      if (!res?.success) return; 
      const data = res.data;
      data.forEach((d) => {d._loading = false;});
      this.list = data;
    },
    async showwarnorders(hours,groupid){
       await this.$emit('showwarnorders',hours,groupid);
    }
  },
};
</script>
 
