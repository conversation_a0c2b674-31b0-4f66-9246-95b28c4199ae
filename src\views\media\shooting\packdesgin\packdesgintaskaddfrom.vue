<template>
  <!-- 创建任务 -->
  <my-container  v-loading="pageLoading" >
      <el-form :model="addForm"  :rules="calcAddFormRules"  :disabled="islook"
       ref="addForm" label-width="100px" class="demo-addForm" >
        <div class="bzcjrw">
          <div class="bt">
            <span style="float: left">创建任务</span>
          </div>
          <!-- 自定义类型处 star -->
          <div class="bzccjlx">
            <div class="lxwz">商品名称</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="productShortName">
                <el-input
                  size="mini"
                  style="width: 120%"
                  :maxlength =100
                  v-model="addForm.productShortName"
                  placeholder="请填写产品简称"

                ></el-input>
              </el-form-item>
            </div>

          </div>

          <div class="bzccjlx">
            <div class="lxwz">产品编码</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="productCode">
                <el-input   size="mini"  style="width: 200%"   v-model="addForm.productCode"  ></el-input>
              </el-form-item>
            </div>
            <i v-if="!iscodeCreate"  style="font-size: 18px;  color: #409eff; font-weight: bold; position: relative; top: 3px; left: 190px;  "
             @click="onSelctProduct" title="系列编码" class="el-icon-circle-plus-outline"
            ></i>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">产品ID</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="productId">
                <el-input size="mini"  style="width: 100%"  v-model="addForm.productId"     maxlength="500" :clearable="true" ></el-input>

              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">平台</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="platform">
                <el-select  size="mini"  style="width: 60%" v-model="addForm.platform"  placeholder="请选择平台"
                    :clearable="true" :collapse-tags="true" filterable @change="onchangeplatform"   >
                    <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">店铺</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="shopName">
                <el-select
                  size="mini"
                  style="width: 100%"
                  v-model="addForm.shopName"
                  placeholder="请选择店铺"
                  :clearable="true" :collapse-tags="true"
                   @change="selShopInfoChange(addForm.shopName, index)" filterable

                >
                <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"  />
                </el-select>
              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">运营小组</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="operationGroup">
                <el-select
                  size="mini"
                  style="width: 60%"
                  v-model="addForm.operationGroup"
                  placeholder="请选择小组"
                  @change="selOpergroupInfoChange(addForm.operationGroup, index)" filterable

                >
                <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">对接人</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="dockingPeople">
                <el-input size="mini"  style="width: 50%"  v-model="addForm.dockingPeople"  :clearable="true" ></el-input>

              </el-form-item>
            </div>
          </div>
          <div class="bzccjlx">
            <div class="lxwz">品牌</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="brand">
                <el-select
                  size="mini"
                  style="width: 55.5%"
                  v-model="addForm.brand"
                  placeholder="请选择"
                >
                  <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="bzccjlx">
            <div class="lxwz">包装类型</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="packClass">
                <el-select
                  size="mini"
                  style="width: 55.5%"
                  v-model="addForm.packClass"
                  placeholder="请选择"
                >
                  <el-option v-for="item in packclasslist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">厂家定制</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="izcjdz">
                 <el-select size="mini"  style="width: 55.5%"  v-model="addForm.izcjdz"  placeholder="请选择" >
                  <el-option   label="是" :value=1></el-option>
                  <el-option   label="否" :value=0></el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">大货仓</div>
            <div style="display: inline-block">
              <el-form-item  label=" " label-width="12px" label-position="left"  prop="warehouse" >
                  <el-select  size="mini"  style="width: 80%"  v-model="addForm.warehouse"  placeholder="请选择仓库"
                    @change="selwarehouseChange()" >
                      <el-option v-for="item in cwarehouselist" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="bzccjlx">
                <div class="lxwz">尺寸</div>
                <div style="display: inline-block">
                  <el-form-item   label=" " label-width="12px" label-position="left"   >
                    <el-input  size="mini"  style="width: 70%"   v-model="addForm.chiCunStr"  placeholder="请填写尺寸"  :clearable="true" ></el-input>
                  </el-form-item>
                </div>
              </div>
          <div class="bzccjlx">
            <div class="lxwz">订单号</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="yyOrderNum">
                <el-input  size="mini"  style="width: 70%"   v-model="addForm.yyOrderNum"  placeholder="请填写订单号"  :clearable="true" ></el-input>
              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">快递单号</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="yyExpressNum">
                <el-input   size="mini"  style="width: 100%"   v-model="addForm.yyExpressNum"  placeholder="请填写快递单号" :clearable="true" ></el-input>
              </el-form-item>
            </div>
          </div>

          <!-- <div class="bzccjlx">
                <div class="lxwz">人才管理</div>
                <div style="display: inline-block">
                  <el-form-item label=" " label-width="12px" prop="yymanage">
                    <el-button style="width: 100%" size="small"  type="primary" :disabled="false" @click="toResultmatt">管理</el-button>
                  </el-form-item>
                </div>
              </div> -->
          <!-- 自定义类型处 end -->

          <div class="bzccjlx" style="margin-top: 15px;height: 150px;">
            <div class="lxwz">附件</div>

            <div style="display: inline-block; margin-left: 12px;width: 160px;">
              <uploadfile :minisize="false"  ref="uploadimg"
                    :islook="islook" :buttontext="'上传图片'"
                    :uploadInfo="addForm.photoUpfiles"
                    :limit="10000"
                    :accepttyes="'.image/jpg,image/jpeg,image/png'" />
            </div>

            <div style="display: inline-block; margin-left: 120px ;width: 160px;">
              <uploadfile :minisize="false"  ref="uploadexl"
                    :islook="islook"  :buttontext="'上传附件'"  :isdown="true"
                    :uploadInfo="addForm.execlUpfiles"
                    :limit="10000" :accepttyes="'.xlsx'"  />
            </div>
          </div>

          <div class="bzccjlx" style="margin: auto auto 20px auto">
            <el-input   type="textarea"  :rows="2"  :maxlength="800" show-word-limit   placeholder="请输入内容" v-model="addForm.taskRemark"  >
            </el-input>
          </div>
          <div class="qxtj">
            <span style="float: left"></span>
            <span style="float: right">
              <el-form-item>
                <el-button size="mini" @click="onCloseAddForm(1)"  >取消</el-button  >
                <el-button  size="mini"  type="primary"  @click="submitForm('addForm')"  >创建任务</el-button>
              </el-form-item>
            </span>
          </div>
        </div>
      </el-form>
       <!--选择商品-->
       <el-dialog title="选择产品" :visible.sync="productVisible" width='1200px'   v-dialogDrag append-to-body>
            <productselect :ischoice="true" ref="productselect" style="z-index:10000;height:500px" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="productVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onQuerenProduct()">确 定</el-button>
                </span>
            </template>
        </el-dialog>
      </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import uploadfile from '@/views/media/shooting/uploadfile';
import productselect from "@/views/base/goods/goods5";
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import {  addOrUpdateShootingVideoTaskAsync} from '@/api/media/packdesgin';
export default {
  props:{
        taskUrgencyList:{ type: Array, default:[] },
        userList:{ type: Array, default:[] },
        groupList:{ type: Array, default:[] },
        platformList:{ type: Array, default:[] },
        warehouselist:{ type: Array, default:[] },
        packclasslist:{ type: Array, default:[] },
        onCloseAddForm:{ type: Function, default: null},
        islook:{ type: Boolean, default:false },
        iscodeCreate:{ type: Boolean, default:false },
        packclasslist:{ type: Array, default:[] }, //
        brandList:{ type: Array, default:[] }, //
    },
    components: {  productselect ,uploadfile,MyContainer},
  data() {
    return {
      that: this,
      inputshow:true,
      pageLoading :false,
      productVisible: false,//选择产品窗口
      shopList: [],
      cwarehouselist:[],
      addForm: {
        packageDesignTaskId:0,
        productShortName:null,
        productCode:null,
        productId:null,
        platform:null,
        shopName:null,
        operationGroup:null,
        dockingPeople:null,
        taskUrgency:9,
        warehouse:null,
        yyOrderNum:null,
        yyExpressNum:null,
        photoUpfiles:[],
        execlUpfiles:[],
        taskRemark:null,
        loginfo:null,

        detailLqBtnStr:null,
        detailLqNameStr:null,
        detailOverTimeStr:null,
        detailIsOver:0,
        chiCunStr:null,
        detailConfirmBtnStr:null,
        detailConfirmNameStr:null,
        detailConfirmTimeStr:null,
        detailConfirmIsOver:0,
        brand:null,
        izcjdz:null,
        IscodeCreate:0
      },
    };
  },
  computed:{
      calcAddFormRules(){
        if(this.iscodeCreate){
          return {
              productShortName: [{ required: true, message: '请填写', trigger: 'blur' }],
              shopName: [{ required: true, message: '请选择', trigger: 'blur' }],
              operationGroup: [{ required: true, message: '请选择', trigger: 'blur' }],
              dockingPeople: [{ required: true, message: '请填写', trigger: 'blur' }],
              shootingTaskPickList: [{ required: true, message: '请选择', trigger: 'blur' }],
              platform: [{ required: true, message: '请选择', trigger: 'blur' }],

              taskUrgency: [{ required: true, message: '请选择', trigger: 'blur' }],
              productCode: [{ required: true, message: '请选择', trigger: 'blur' }],
              fpDetailLqName: [{ required: true, message: '请选择', trigger: 'blur' }],
          }

        }else{
          return {
              productShortName: [{ required: true, message: '请填写', trigger: 'blur' }],
              shopName: [{ required: true, message: '请选择', trigger: 'blur' }],
              operationGroup: [{ required: true, message: '请选择', trigger: 'blur' }],
              dockingPeople: [{ required: true, message: '请填写', trigger: 'blur' }],
              shootingTaskPickList: [{ required: true, message: '请选择', trigger: 'blur' }],
              platform: [{ required: true, message: '请选择', trigger: 'blur' }],

              taskUrgency: [{ required: true, message: '请选择', trigger: 'blur' }],
              productCode: [{ required: true, message: '请选择', trigger: 'blur' }],
              fpDetailLqName: [{ required: true, message: '请选择', trigger: 'blur' }],
              packClass: [{ required: true, message: '请选择', trigger: 'blur' }],
              brand: [{ required: true, message: '请选择', trigger: 'blur' }],
              izcjdz: [{ required: true, message: '请选择', trigger: 'blur' }],
          }
        }

      }
  },
  async mounted() {
        for(let num in this.warehouselist){
            this.cwarehouselist.push( this.warehouselist[num]);
        }
        if(this.addForm.dockingPeople ==null)
                this.addForm.dockingPeople = this.$store.getters.userName?.split("-")[0].trim();
    },
  methods: {

    toResultmatt(){
          let routeUrl = this.$router.resolve({
              path: '/packdesgintalentmanage',
          });
          window.open(routeUrl.href, '_blank');
      },

    async initSomething(row){
      this.pageLoading =true;
      if(this.iscodeCreate){
            this.addForm.IscodeCreate =1;
            this.addForm.productShortName = row.goodsName;
            this.addForm.productId = row.proCode;
            this.addForm.productCode = row.goodsCode;
            this.addForm.platform = row.platform;
            await this.onchangeplatform( this.addForm.platform );
            this.addForm.shopName =  row.shopCode;
            this.addForm.operationGroup =row.groupId.toString();
      }
      this.pageLoading =false;
    },
    //下拉改变值
    async onchangeplatform(val) {
        var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 10000 });
        this.addForm.shopName = "";
        this.shopList = res1.data.list;
    },
    selwarehouseChange(){
        for(let num in this.warehouselist){
            if(this.warehouselist[num].value==this.addForm.warehouse){
                this.addForm.warehouseStr =this.warehouselist[num].label;
            }
        }
    },
    selShopInfoChange(val){
        let resultArr = this.shopList.find((item) => {
            return item.shopCode == val;
        });
        this.addForm.shopNameStr = resultArr.shopName;
    },
    selOpergroupInfoChange(val){
        var resultArr = this.groupList.find((item) => {
            return item.value == val;
        });
        this.addForm.operationGroupstr = resultArr.label;
    },
    onSelctProduct() {
            this.productVisible = true;
        },
    //选择产品确定
    async onQuerenProduct() {
      var choicelist = await this.$refs.productselect.getchoicelistOnly();
           if(choicelist != null){
            this.addForm.productShortName = choicelist.goodsCode;
            this.addForm.productId = choicelist.proCode;
            this.addForm.productCode = choicelist.productCode;
            this.addForm.platform = choicelist.platform;
            await this.onchangeplatform( this.addForm.platform );
            this.addForm.shopName =  choicelist.shopCode;
            this.addForm.operationGroup =choicelist.groupId.toString();
            this.productVisible = false;
           }
    },
      //下拉改变值结束
    async submitForm(formName) {
      this.$refs[formName].validate(async (valid)  => {
        if (valid) {
            var res = this.$refs.uploadimg.getReturns();
            if(!res.success){return;}
            this.addForm.photoUpfiles = res.data;
            res = this.$refs.uploadexl.getReturns();
            if(!res.success)  return;
            let para = _.cloneDeep(this.addForm);
            this.pageLoading =true;
            var res = await addOrUpdateShootingVideoTaskAsync(para);
            this.pageLoading =false;
            if (!res?.success) {  return; }
            this.$message({message: this.$t('保存成功'),type: 'success'});
            this.onCloseAddForm(2);
        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep  .el-form-item {
  margin: 0px !important;
}

::v-deep .el-form-item__error {
  position: absolute !important;
  top: 30% !important;
  left: 400px !important;
  width: 60px !important;
}


::v-deep .bzcjrw {
  width: 100%;
  background-color: #fff;
}
::v-deep .bzcjrw .bt {
  height: 40px;
  /* background-color: aquamarine; */
  font-size: 18px;
  color: #666;
  margin-bottom: 20px;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 0 35px;
}
::v-deep .bzcjrw .bt i {
  color: #999;
}
::v-deep .bzcjrw .bt i {
  margin-left: 8px;
  line-height: 26px;
}
::v-deep .bzcjrw .bt i:hover {
  margin-left: 8px;
  line-height: 26px;
  color: #409eff;
  position: relative;
  top: -2px;
}

::v-deep .bzcjrw .bzccjlx {
  width: 100%;
  height: 35px;
  /* background-color: bisque; */
  box-sizing: border-box;
  padding: 0 60px;
}

::v-deep .bzcjrw .bzbjlxf {
  width: 100%;
  /* background-color: bisque; */
  box-sizing: border-box;
  padding: 0 60px;
}

::v-deep .bzcjrw .bzccjlx .lxwz {
  width: 112px;
  font-size: 14px;
  color: #666;
  vertical-align: top;
  line-height: 26px;
  /* background-color: rgb(204, 204, 255); */
  display: inline-block;
}

::v-deep .bzcjrw .scpd {
  width: 100px;
  font-size: 14px;
  color: #666;
  line-height: 36px;
  /* background-color: rgb(204, 204, 255); */
  float: left;
}

.bzcjrw .zjck {
  display: inline-block;
  float: right;
  position: relative;
  top: 5px;
}

.bzcjrw .qxtj {
  height: 100px;
  /* background-color: aquamarine; */
  box-sizing: border-box;
  padding: 25px 60px;
}
</style>
