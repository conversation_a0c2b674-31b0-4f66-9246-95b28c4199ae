<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="资金账单" name="first1" style="height: 100%">
        <fundBillVideo ref="fundBillVideo" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="违规赔付" name="first2" style="height: 100%" lazy>
        <breachPaymentVideo ref="breachPaymentVideo" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="商责欠款" name="first3" style="height: 100%" lazy>
        <commercialLiabilityArrears ref="commercialLiabilityArrears" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="达人佣金明细" name="first4" style="height: 100%" lazy>
        <fundBillVideo_dr ref="fundBillVideo_dr" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="达人佣金汇总" name="first5" style="height: 100%" lazy>
        <fundBillVideo_dr_group ref="fundBillVideo_dr_group" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="订单管理" name="first6" style="height: 100%" lazy>
        <videoOrderManage ref="videoOrderManage" style="height: 100%" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import breachPaymentVideo from "./breachPaymentVideo.vue";
import commercialLiabilityArrears from "./commercialLiabilityArrears.vue";
import fundBillVideo from "./fundBillVideo.vue";
import fundBillVideo_dr from "./fundBillVideo_dr.vue";
import fundBillVideo_dr_group from "./fundBillVideo_dr_group.vue";
import videoOrderManage from "./videoOrderManage.vue";
export default {
  name: "videoBillIndex",
  components: {
    MyContainer, breachPaymentVideo, commercialLiabilityArrears, fundBillVideo,fundBillVideo_dr,fundBillVideo_dr_group,videoOrderManage
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },
};
</script>

<style lang="scss" scoped></style>
