<template>
  <MyContainer>
    <vxetablebase :id="'approvalCode202501151902'" ref="table" :loading="loading" :that="that" :is-index="true"
      :hasexpand="true" :tablefixed="true" :has-seq="false" :border="true" :table-data="data.list"
      :table-cols="tableCols" :is-selection="false" :is-select-column="true" :is-index-fixed="false"
      style="width: 100%; margin: 0;" height="100%" :showsummary="data.summary ? true : false"
      :summaryarry="data.summary" @sortchange="sortchange" :isRemoteSort="false" >
      <template #io_id="{ row }">
          <div>
              {{ row.io_id !== null ? row.io_id.toString() : row.io_id }}
          </div>
      </template>
      <template #link_io_id="{ row }">
          <div>
              {{ row.link_io_id !== null  ? row.link_io_id.toString() : row.link_io_id }}
          </div>
      </template>
    </vxetablebase>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
const api = '/api/verifyOrder/GoodAllot/AllotOperateDetail/'
import { mergeTableCols } from '@/utils/getCols'
export default {
  name: "allotInTime",
  components: {
    MyContainer, vxetablebase, dateRange, buschar, numberRange,
  },
  props: {
    allotInTimeListInfo: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      api,
      that: this,
      ListInfo: {
        orderBy: '',
        isisAsc: false,
        currentPage: 1,
        pageSize: 50,
        instanceId: null,
      },
      data: {},
      tableCols: [],
      tableData: [],
      total: 0,
      loading: true,
    }
  },
  async mounted() {
    this.ListInfo.instanceId = this.allotInTimeListInfo.instanceId
    await this.getCol();
    await this.getList()
  },
  methods: {
    async getCol() {
      const { data, success } = await request.post(`${this.api}GetColumns`)
      if (success) {
        data.forEach(item => {
          if (item.prop == 'orderNoInner') {
            item.type = 'orderLogInfo'
            item.orderType = 'orderNoInner'
          }
          if (item.prop == 'proCode') {
            item.type = 'html'
            item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
          }
        })
        this.tableCols = mergeTableCols(data)
      }
    },
    async getList(type) {
      if (type === "search") {
        this.ListInfo.currentPage = 1;
        this.$refs.pager.setPage(1);
      }
      this.loading = true;
      try {
        const { data, success } = await request.post(`${this.api}GetDetails`,null,{params: {instanceId:this.ListInfo.instanceId}});
        if (success) {
          this.data.list = data;
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.loading = false;
      }
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss"></style>
