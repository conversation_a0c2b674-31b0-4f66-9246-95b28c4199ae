<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%;">
            <el-tab-pane label="汇总" name="first" :lazy="true">
                <scanCodeSummay @toDetails="toDetails" />
            </el-tab-pane>
            <el-tab-pane label="明细" name="second" :lazy="true">
                <scanCodeDetails />
            </el-tab-pane>
            <el-tab-pane label="异常" name="third" style="height: 100%;">
                <errorStaus ref="errorStaus" />
            </el-tab-pane>
            <el-tab-pane label="待再匹配扫码次数" name="four" style="height: 100%;">
                <numberOfCcans ref="numberOfCcans" />
            </el-tab-pane>
            <el-tab-pane label="拆包件分析" name="five" style="height: 100%;">
                <unpackingStat />
            </el-tab-pane>
            <el-tab-pane label="云仓退件" name="seventh" style="height: 100%;">
                <scanCodeWarehouseReturns />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import scanCodeDetails from "./scanCodeDetails.vue";
import scanCodeSummay from "./scanCodeSummay.vue";
import errorStaus from "./errorStaus.vue";
import unpackingStat from "./unpackingStat.vue";
import numberOfCcans from "./numberOfCcans.vue";
import scanCodeWarehouseReturns from "./scanCodeWarehouseReturns.vue";
export default {
    components: {
        MyContainer, scanCodeDetails, scanCodeSummay, errorStaus, unpackingStat, numberOfCcans, scanCodeWarehouseReturns
    },
    data() {
        return {
            activeName: 'first'
        };
    },
    methods: {
        toDetails(e) {
            this.activeName = e
        }
    }
};
</script>

<style lang="scss" scoped></style>
