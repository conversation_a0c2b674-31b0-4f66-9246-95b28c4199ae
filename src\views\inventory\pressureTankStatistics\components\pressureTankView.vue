<template>
    <MyContainer>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' id="20250308131630"
            :isRemoteSort="false" @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :height="'100%'">
        </vxetablebase>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'supplierName', label: '厂家名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'supplierLink', label: '厂家链接', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    props: {
        data: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            that: this,
            timeRanges: [],
            tableCols,
            tableData: this.data,
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
