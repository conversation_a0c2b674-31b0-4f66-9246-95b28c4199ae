<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <div style="display: flex;gap: 10px;margin-right: 10px;">
          <inputYunhan ref="buyNo" :inputt.sync="ListInfo.buyNo" v-model="ListInfo.buyNo" :valuedOpen="true"
            width="170px" placeholder="采购单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="1000" @callback="callbackMethod($event, 1)" title="采购单号">
          </inputYunhan>
          <inputYunhan ref="businessId" :inputt.sync="ListInfo.businessId" v-model="ListInfo.businessId" :valuedOpen="true"
            width="170px" placeholder="流程编号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="1000" @callback="callbackMethod($event, 4)" title="流程编号">
          </inputYunhan>
          <inputYunhan ref="warehouseNo" :inputt.sync="ListInfo.warehouseNo" v-model="ListInfo.warehouseNo"
            :valuedOpen="true" width="170px" placeholder="入库单号/若输入多条请按回车" :clearable="true" :clearabletext="true"
            :maxRows="100" :maxlength="1000" @callback="callbackMethod($event, 2)" title="入库单号">
          </inputYunhan>
          <inputYunhan ref="batchNumber" :inputt.sync="ListInfo.batchNumber" v-model="ListInfo.batchNumber"
            :valuedOpen="true" width="170px" placeholder="批次号/若输入多条请按回车" :clearable="true" :clearabletext="true"
            :maxRows="100" :maxlength="1000" @callback="callbackMethod($event, 3)" title="批次号">
          </inputYunhan>
        </div>
        <el-select v-model="ListInfo.warehouse" placeholder="仓库" class="publicCss" clearable filterable multiple
          collapse-tags>
          <el-option v-for="item in warehouselist" :key="item.name" :label="item.name" :value="item.wms_co_id" />
        </el-select>
        <el-select v-model="ListInfo.brandId" placeholder="采购" class="publicCss" clearable filterable multiple
          collapse-tags>
          <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable multiple collapse-tags>
          <el-option key="审批中" label="审批中" value="审批中" />
          <el-option key="通过" label="通过" value="通过" />
          <el-option key="拒绝" label="拒绝" value="拒绝" />
          <el-option key="财务初审中" label="财务初审中" value="财务初审中" />
          <el-option key="财务初审通过" label="财务初审通过" value="财务初审通过" />
          <el-option key="财务初审拒绝" label="财务初审拒绝" value="财务初审拒绝" />
        </el-select>
        <el-input v-model.trim="ListInfo.creator" placeholder="添加人" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
        <el-button type="primary" @click="batchExamine">批量预审</el-button>
      </div>
    </template>
    <vxetablebase :id="'financialPreliminaryReview202504131720'" :tablekey="'financialPreliminaryReview202504131720'"
      ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'" @select="selectchange">
      <template #status="{ row }">
        <span :style="{ color: getStatusColor(row.status) }">{{ row.status }}</span>
      </template>
      <template slot="right">
        <vxe-column title="操作" width="70" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="handleExamine(row)" v-if="row.status == '财务初审中'">预审</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog :title="distinguish ? '批量预审' : '预审'" :visible.sync="preliminaryHearingVisible" width="25%" v-dialogDrag
      :close-on-click-modal="false">
      <div style="height: 150px;">
        <el-form :model="ruleFormExamine" :rules="rulesExamine" ref="ruleFormExamine" height="200" label-width="100px"
          class="demo-ruleForm">
          <el-form-item label="预审状态" prop="priorAuditType">
            <el-select v-model="ruleFormExamine.priorAuditType" placeholder="状态" clearable filterable
              style="width: 100%;">
              <el-option key="通过" label="通过" :value="1" />
              <el-option key="拒绝" label="拒绝" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="预审备注">
            <el-input type="textarea" placeholder="请输入预审备注" v-model="ruleFormExamine.priorAuditRemark"
              :autosize="{ minRows: 5, maxRows: 5 }" resize="none" maxlength="200" show-word-limit>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div style="display: flex;justify-content: center;gap: 10px;margin-top: 30px;">
        <el-button @click="preliminaryHearingVisible = false">关闭</el-button>
        <el-button type="primary" @click="onSaveCertificate">确定</el-button>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import { getFinancePriorAuditPage, exportFinancePriorAuditData, financePriorAudit, batchFinancePriorAudit } from '@/api/inventory/purchaseCostVerify.js'
import { getAllProBrand, getAllWarehouse } from '@/api/inventory/warehouse'
import inputYunhan from "@/components/Comm/inputYunhan";
import dayjs from 'dayjs'
const tableCols = [
  { istrue: true, width: '40', type: "checkbox" },
  { sortable: 'custom', width: '100', align: 'center', prop: 'status', label: '状态', },
  { width: '100', align: 'center', prop: 'businessId', label: '流程编号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'batchNo', label: '批次号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'fsYmdDate', label: '发生日期', },
  { width: '100', align: 'center', prop: 'regionCost', label: '区域费用', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'warehousingNo', label: '入库单号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'buyNo1', label: '采购单号1', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'buyNo2', label: '采购单号2', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'brandName', label: '采购名称', },
  { sortable: 'custom', width: '170', align: 'center', prop: 'warehouseName', label: '仓库', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'totalPerTicket', label: '单票合计', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'orderFee1', label: '单号1费用', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'orderFee2', label: '单号2费用', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'haulage', label: '托运费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'deliveryFee', label: '送货费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'pickUpFee', label: '提货费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'huoLaLa', label: '货拉拉', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'loadingFee', label: '装车费', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'remark', label: '备注', },
  { width: '100', align: 'center', prop: 'proof1', label: '凭证1', type: "images", },
  { width: '100', align: 'center', prop: 'proof2', label: '凭证2', type: "images", },
  { width: '100', align: 'center', prop: 'proof3', label: '凭证3', type: "images", },
  { width: '100', align: 'center', prop: 'proof4', label: '凭证4', type: "images", },
  { sortable: 'custom', width: '100', align: 'center', prop: 'createdUserName', label: '添加人', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'reviewerName', label: '初审人', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'auditRemarks', label: '初审备注', },
]
export default {
  name: "financialPreliminaryReview",
  components: {
    MyContainer, vxetablebase, inputYunhan
  },
  data() {
    return {
      preliminaryHearingVisible: false,
      distinguish: false,
      ruleFormExamine: {
        priorAuditRemark: null,
        priorAuditType: null,
        id: null,
        ids: [],
      },
      rulesExamine: {
        priorAuditRemark: [{ required: true, message: '请输入预审备注', trigger: 'blur' }],
        priorAuditType: [{ required: true, message: '请选择预审状态', trigger: 'blur' }],
      },
      brandlist: [],//采购
      warehouselist: [],//仓库
      checkedList: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
        status: '',//状态
        creator: '',//添加人
        buyNo: '',//采购单号
        warehouseNo: '',//入库单号
        batchNumber: '',//批次号
        warehouse: '',//入库仓库
        brandId: '',//采购
        businessId: '',//流程编号
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.init()
    await this.getList()
  },
  methods: {
    batchExamine() {
      if (!this.checkedList.length) {
        this.$message.error('请选择要预审的单据')
        return
      }
      this.preliminaryHearingVisible = true
      this.onClearCheck()
      this.distinguish = true
      this.ruleFormExamine.ids = this.checkedList.map(item => item.id)
    },
    // 复选框数据
    selectchange(val) {
      this.checkedList = val
    },
    getStatusColor(status) {
      if (status === '通过') {
        return '#008000';
      } else if (status === '拒绝') {
        return '#F56C6C';
      } else {
        return '#000000';
      }
    },
    onSaveCertificate() {
      this.$refs.ruleFormExamine.validate(async (valid) => {
        if (valid) {
          let result;
          const params = {
            ids: this.ruleFormExamine.ids,
            id: this.ruleFormExamine.id,
            priorAuditType: this.ruleFormExamine.priorAuditType,
            priorAuditRemark: this.ruleFormExamine.priorAuditRemark,
          }
          if (this.distinguish) {
            delete params.id
            result = await batchFinancePriorAudit(params)
          } else {
            delete params.ids
            result = await financePriorAudit(params)
          }
          if (result.success) {
            this.$message.success('操作成功')
            this.preliminaryHearingVisible = false
            this.checkedList = []
            this.getList()
          }
        }
      })
    },
    onClearCheck() {
      this.$nextTick(() => {
        this.$refs.ruleFormExamine.resetFields();
        this.$refs.ruleFormExamine.clearValidate();
      });
      this.ruleFormExamine = {
        priorAuditRemark: null,
        priorAuditType: null,
        id: null,
        ids: [],
      }
    },
    handleExamine(row) {
      this.preliminaryHearingVisible = true
      this.onClearCheck()
      this.distinguish = false
      this.ruleFormExamine.id = row.id
    },
    async init() {
      let res2 = await getAllProBrand();
      this.brandlist = res2.data ? res2.data.map(item => { return { value: item.key, label: item.value }; }) : [];
      let res3 = await getAllWarehouse();
      this.warehouselist = res3.data ? res3.data.filter((x) => x.name.indexOf('代发') < 0) : [];
    },
    callbackMethod(e, type) {
      if (type == 1) {
        this.ListInfo.buyNo = e
      } else if (type == 2) {
        this.ListInfo.warehouseNo = e
      } else if (type == 3) {
        this.ListInfo.batchNumber = e
      } else if (type == 4) {
        this.ListInfo.businessId = e
      }
    },
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
    },
    async exportProps() {
      this.loading = true
      const params = this.queryCondition();
      await exportFinancePriorAuditData(params)
      this.loading = false
    },
    queryCondition() {
      return {
        ...this.ListInfo,
        currentPage: this.ListInfo.currentPage,
        pageSize: this.ListInfo.pageSize,
        orderBy: this.ListInfo.orderBy,
        isAsc: this.ListInfo.isAsc,
        startDate: this.ListInfo.startDate,
        endDate: this.ListInfo.endDate,
        status: this.ListInfo.status?.length ? this.ListInfo.status.join(',') : '',
        creator: this.ListInfo.creator,
        buyNo: this.ListInfo.buyNo,
        warehouseNo: this.ListInfo.warehouseNo,
        businessId: this.ListInfo.businessId,
        batchNumber: this.ListInfo.batchNumber,
        warehouse: this.ListInfo.warehouse?.length ? this.ListInfo.warehouse.join(',') : '',
        brandId: this.ListInfo.brandId?.length ? this.ListInfo.brandId.join(',') : '',
      }
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      }
      this.loading = true
      const params = this.queryCondition();
      if (this.checkInputLength(params.warehouseNo, 'warehouseNo', '入库单号只能输入100个')) return;
      if (this.checkInputLength(params.batchNumber, 'batchNumber', '批次号只能输入100个')) return;
      if (this.checkInputLength(params.buyNo, 'buyNo', '采购单号只能输入100个')) return;
      if (this.checkInputLength(params.businessId, 'businessId', '流程编号只能输入100个')) return;
      const { data, success } = await getFinancePriorAuditPage(params)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.fsYmdDate = dayjs(item.fsYmdDate).format('YYYY-MM-DD');
          const processProof = (proof) => {
            try {
              const proofArray = JSON.parse(proof);
              if (Array.isArray(proofArray)) {
                return proofArray.map(proofItem => ({
                  name: proofItem.name || "",  // 保留name字段，如果不存在则设为空字符串
                  url: proofItem.url
                }));
              }
            } catch (error) {
              console.error('Invalid JSON in proof:', proof);
            }
            return [];
          };
          item.proof1 = JSON.stringify(processProof(item.proof1));
          item.proof2 = JSON.stringify(processProof(item.proof2));
          item.proof3 = JSON.stringify(processProof(item.proof3));
          item.proof4 = JSON.stringify(processProof(item.proof4));
        });
        this.total = data.total
        this.summaryarry = data.summary
        console.log('summary', data.summary);
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    checkInputLength(param, paramName, errorMessage) {
      if (param) {
        let values = param.split(',');
        if (values.length > 100) {
          this.$message.error(errorMessage);
          this.loading = false;
          return true;
        }
      }
      return false;
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 170px;
    margin-right: 5px;
  }
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
