<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="聊天时间" style="padding: 0;margin: 0; ">
                    <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始聊天时间" end-placeholder="结束聊天时间"
                        :picker-options="pickerOptions" :default-value="defaultDate">
                    </el-date-picker>
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-input v-model.trim="filter.saleAfterNo" style="width: 140px" placeholder="售后编号"
                        @keyup.enter.native="onSearch" clearable maxlength="100" />
                </el-form-item>
                <el-form-item style="padding: 0;margin: 0; ">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.chatUser" style="width: 140px" placeholder="聊天人"
                        @keyup.enter.native="onSearch" clearable maxlength="100" />
                </el-form-item>
                <el-form-item label="是否客服" size="mini">
                    <el-select v-model="filter.isKf" placeholder="请选择" style="width: 100px" size="mini">
                        <el-option label="请选择" :value="-1"></el-option>
                        <el-option label="是" :value="1"></el-option>
                        <el-option label="否" :value="0"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { getPddOnlyRefundChatPageList, exportPddOnlyRefundOrderChatList } from '@/api/customerservice/pddonlyrefund';
const tableCols = [
    { istrue: true, prop: 'saleAfterNo', label: '售后编号', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'shopName', label: '店铺名称', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'chatTime', label: '聊天时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'chatUser', label: '聊天人', width: '240', sortable: 'custom' },
    { istrue: true, prop: 'isKf', label: '是否客服', width: '80', formatter: (row) => { return (row.isKf == 1 ? "是" : "否") }, sortable: 'custom' },
    { istrue: true, prop: 'chatContext', label: '聊天内容', width: '500' },
]
const tableHandles1 = [
];
export default {
    name: 'pddonlyrefundchat',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow },
    props: {

    },
    data() {
        return {
          date: '',
            that: this,
            shopList: [],
            filter: {
                timerange: [
                    formatTime(dayjs().subtract(3, "day"), "YYYY-MM-DD"),
                    formatTime(new Date(), "YYYY-MM-DD"),
                ],
                applyStartDate: null,
                applyEndDate: null,
                timerange2: [],
                chatUser: null,
                isKf: null,
                saleAfterNo: null

            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "saleAfterNo", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            pickerOptions: {
                shortcuts: [
                    {
                        text: '昨天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime());
                            picker.$emit('pick', [start, start]);
                        }
                    }, {
                        text: '近三天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime());
                            const end = new Date(new Date(tdate.toLocaleDateString()));
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一周',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                            const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一个月',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                            console.log("获取前一个月的时间", tdate.getDay());
                            const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }],
                      disabledDate(date) {
                  // 设置禁用日期
                  const start = new Date('1970/1/1');
                  const end = new Date('9999/12/31');
                  return date < start || date > end;
                  }
                },
            defaultDate: new Date('1970/1/1'),
            tableHandles1: tableHandles1,
        };
    },
    async mounted() {
        await this.getShopList();
        await this.onSearch()
    },
    methods: {
        async getShopList() {
            const res1 = await getAllShopList({ platforms: [2] });
            this.shopList = [];
            res1.data?.forEach(f => {
                if (f.isCalcSettlement && f.shopCode)
                    this.shopList.push(f);
            });
        },
        //查询第一页
        async onSearch() {
            console.log(this.filter)
            if (!this.filter.timerange) {
                this.$message({ message: "请选择日期", type: "warning", });
                return;
            }
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            this.filter.applyStartDate = null;
            this.filter.applyEndDate = null;
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.applyStartDate = this.filter.timerange[0];
                this.filter.applyEndDate = this.filter.timerange[1];
            }
            else {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getPddOnlyRefundChatPageList(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
        },
        //排序查询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params');
            this.listLoading = true;
            var res = await exportPddOnlyRefundOrderChatList(params);
            if (!res?.data) {
                this.listLoading = false;
                return;
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob);
            aLink.setAttribute('download', '拼多多仅退款订单聊天记录_' + new Date().toLocaleString() + '.xlsx');
            aLink.click();
            this.listLoading = false;

        }
    },
};
</script>

<style lang="scss" scoped></style>
