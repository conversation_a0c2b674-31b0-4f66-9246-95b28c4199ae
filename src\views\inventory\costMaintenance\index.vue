<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%;">
            <el-tab-pane label="成品" name="forth" style="height: 100%;" lazy>
                <finishedProduct />
            </el-tab-pane>
            <el-tab-pane label="钉钉核价" name="second" style="height: 100%;" lazy>
                <dingTalkNuclearPrice />
            </el-tab-pane>
            <!-- <el-tab-pane label="半成品" name="third" style="height: 100%;" lazy>
                <semiFinishedProducts />
            </el-tab-pane> -->
            <el-tab-pane label="成本维护" name="first" style="height: 100%;">
                <costMaintenance />
            </el-tab-pane>
            <el-tab-pane label="加工成本维护明细表" name="fifth" style="height: 100%;">
                <maintenanceDetailsList />
            </el-tab-pane>
            <el-tab-pane label="加工成本维护表" name="sixth" style="height: 100%;">
                <maintenanceTable />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import costMaintenance from './component/costMaintenance.vue'
import dingTalkNuclearPrice from './component/dingTalkNuclearPrice.vue'
import semiFinishedProducts from './component/semiFinishedProducts.vue'
import finishedProduct from './component/finishedProduct.vue'
import maintenanceDetailsList from './component/maintenanceDetailsList.vue'
import maintenanceTable from './component/maintenanceTable.vue'
export default {
    components: {
        MyContainer, costMaintenance, dingTalkNuclearPrice, semiFinishedProducts, finishedProduct, maintenanceDetailsList, maintenanceTable
    },
    data() {
        return {
            activeName: 'forth'
        };
    },
    methods: {

    }
};
</script>

<style lang="scss" scoped></style>
