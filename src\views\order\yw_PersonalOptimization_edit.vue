<template>
    <div>
        <div class="form_header" v-if="!isBatch">
            <el-image class="imgCss" :src="editRow?.goodsImage" lazy :preview-src-list="[editRow?.goodsImage]"
                :fit="fit"></el-image>
            <el-descriptions class="margin-top" :column="4" :size="size" border style="width: 100%;">
                <el-descriptions-item label="系列编码">{{ editRow?.styleCode }}</el-descriptions-item>
                <el-descriptions-item label="商品编码">{{ editRow?.goodsCode }}</el-descriptions-item>
                <el-descriptions-item label="创建日期">{{ editRow?.goodsCreateTime }}</el-descriptions-item>
                <el-descriptions-item label="成本价">{{ editRow?.cost }}</el-descriptions-item>
                <el-descriptions-item label="订单数">{{ editRow?.orderCount }}</el-descriptions-item>
                <el-descriptions-item label="销售数量">{{ editRow?.saleCount }}</el-descriptions-item>
                <el-descriptions-item label="销售金额">{{ editRow?.saleAmount }}</el-descriptions-item>
                <el-descriptions-item label="销售成本">{{ editRow?.saleCost }}</el-descriptions-item>
            </el-descriptions>
        </div>
        <el-scrollbar class="edit_container">
            <div style="padding: 10px;">
                <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="90px"
                    class="demo-ruleForm">
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="类型:" prop="goodsType">
                                <el-select v-model="ruleForm.goodsType" placeholder="请选择类型" clearable>
                                    <el-option label="老品优化" value="老品优化" />
                                    <el-option label="新建编码" value="新建编码" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="优化方向:" prop="optimizeWayList">
                                <el-select v-model="ruleForm.optimizeWayList" clearable placeholder="请选择优化方向" filterable
                                    multiple collapse-tags>
                                    <el-option v-for="item in optimizeList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="优化进度【前期】:">
                                <el-input type="textarea" :rows="3" placeholder="优化进度【前期】"
                                    v-model="ruleForm.firOptimizeProgress" resize="none" maxlength="800"
                                    show-word-limit />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="优化进度【中期】:">
                                <el-input type="textarea" :rows="3" placeholder="优化进度【中期】"
                                    v-model="ruleForm.secOptimizeProgress" resize="none" maxlength="800"
                                    show-word-limit />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="新成本价:">
                                <el-input-number v-model="ruleForm.newCost" placeholder="新成本价" :min="0" :max="999999999"
                                    :controls="false" :precision="4" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" v-if="!isBatch">
                            <el-form-item label="降价比例:">
                                {{ computedJiangJiaRate ? computedJiangJiaRate + '%' : '0.00%' }}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <div>优化后变化</div>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="成本:" prop="costType">
                                <el-select v-model="ruleForm.costType" clearable placeholder="请选择品质">
                                    <el-option label="涨价" value="涨价" />
                                    <el-option label="降价" value="降价" />
                                    <el-option label="不变" value="不变" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="品质:" prop="quality">
                                <el-select v-model="ruleForm.quality" clearable placeholder="请选择品质">
                                    <el-option label="提质" value="提质" />
                                    <el-option label="降质" value="降质" />
                                    <el-option label="不变" value="不变" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="包装变化:" prop="packageChange">
                                <el-select v-model="ruleForm.packageChange" clearable placeholder="请选择品质">
                                    <el-option label="是" value="是" />
                                    <el-option label="否" value="否" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <div>产品标准:</div>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="尺寸:">
                                <div style="display:flex;gap:5px;justify-content: space-between;">
                                    长
                                    <el-input-number v-model="ruleForm.length" placeholder="长(mm)" :min="0"
                                        :max="999999" :controls="false" :precision="2" style="width:33%" />
                                    宽
                                    <el-input-number v-model="ruleForm.width" placeholder="宽(mm)" :min="0" :max="999999"
                                        :controls="false" :precision="2" style="width:33%" />
                                    高
                                    <el-input-number v-model="ruleForm.height" placeholder="高(mm)" :min="0"
                                        :max="999999" :controls="false" :precision="2" style="width:33%" />
                                </div>

                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="克重:" props="grammage">
                                <el-input-number v-model="ruleForm.grammage" placeholder="克重(mm)" :min="0" :max="999999"
                                    :controls="false" :precision="2" style="width:33%" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="厚度:" props="thickness">
                                <el-input-number v-model="ruleForm.thickness" placeholder="厚度(mm)" :min="0"
                                    :max="999999" :controls="false" :precision="2" style="width:33%" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <div style="display: flex; align-items: center; justify-content: space-between;" v-if="!isBatch">
                        <div>竞品利润:</div>
                        <el-button type="text" @click="addProps">新增一行</el-button>
                    </div>
                    <el-row v-if="!isBatch">
                        <el-table :data="ruleForm.dtls" style="width: 100%" border height="250">
                            <el-table-column prop="date" label="竞品平台" width="180" sortable>
                                <template #default="{ row }">
                                    <el-select v-model="row.platform" placeholder="竞品平台" clearable>
                                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column prop="chooseId" label="竞品ID" width="180" sortable>
                                <template #default="{ row }">
                                    <el-input v-model="row.chooseId" placeholder="竞品ID" maxlength="100"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column prop="monthSaleCount" label="竞品月销" sortable>
                                <template #default="{ row }">
                                    <el-input-number v-model="row.monthSaleCount" placeholder="竞品月销" :min="0"
                                        :max="999999999" :controls="false" :precision="0" />
                                </template>
                            </el-table-column>
                            <el-table-column prop="daySaleCount" label="竞品日销" sortable>
                                <template #default="{ row }">
                                    <el-input-number v-model="row.daySaleCount" placeholder="竞品月销" :min="0"
                                        :max="999999999" :controls="false" :precision="0" />
                                </template>
                            </el-table-column>
                            <el-table-column prop="salePrice" label="竞品售价" sortable>
                                <template #default="{ row }">
                                    <el-input-number v-model="row.salePrice" placeholder="竞品月销" :min="0"
                                        :max="999999999" :controls="false" :precision="4" />
                                </template>
                            </el-table-column>
                            <el-table-column prop="ygExpressFee" label="模拟运费" sortable>
                                <template #default="{ row }">
                                    <el-input-number v-model="row.ygExpressFee" placeholder="竞品月销" :min="0" :max="9999"
                                        :controls="false" :precision="4" />
                                </template>
                            </el-table-column>
                            <el-table-column prop="profit1" label="毛一利润" sortable>
                            </el-table-column>
                            <el-table-column prop="profit1Rate" label="毛一利润率" sortable>
                                <template #default="{ row }">
                                    {{ row.profit1Rate ? row.profit1Rate + '%' : '0.00%' }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="address" label="操作">
                                <template #default="{ row, $index }">
                                    <el-button type="danger" @click="ruleForm.dtls.splice($index, 1)">删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-row>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="运维备注【运营】:">
                                <el-input type="textarea" :rows="3" placeholder="运维备注【运营】"
                                    v-model="ruleForm.operaterUserRemark" resize="none" maxlength="800"
                                    show-word-limit />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="运维备注【售前】:">
                                <el-input type="textarea" :rows="3" placeholder="运维备注【售前】"
                                    v-model="ruleForm.saleBeforeUserRemark" resize="none" maxlength="800"
                                    show-word-limit />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="运维备注【售后】:">
                                <el-input type="textarea" :rows="3" placeholder="运维备注【售后】"
                                    v-model="ruleForm.saleAfterUserRemark" resize="none" maxlength="800"
                                    show-word-limit />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="优化结果:">
                                <el-select v-model="ruleForm.optimizeResultList" placeholder="请选择优化结果" clearable filterable
                                    multiple collapse-tags>
                                    <el-option v-for="item in optimizeList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="累计优化次数:">
                                {{ ruleForm.optimizeCount }}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="绑定平台运营:">
                                <el-select filterable v-model="ruleForm.operateSpecialUserIds1" collapse-tags clearable
                                    placeholder="运营专员" multiple style="width: 50%;" :multiple-limit="50">
                                    <el-option key="无运营专员" label="无运营专员" :value="0"></el-option>
                                    <el-option v-for="item in directorlist" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="是否更新聚水潭成本价:">
                                <el-select v-model="ruleForm.isAdjustJstJiangjia" placeholder="请选择类型" clearable>
                                    <el-option label="是" :value="1" />
                                    <el-option label="否" :value="0" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="是否内推运营:">
                                <el-select v-model="ruleForm.isSendToOperateUser" placeholder="请选择类型" clearable>
                                    <el-option label="是" :value="1" />
                                    <el-option label="否" :value="0" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </el-scrollbar>
        <div class="btnGroup">
            <el-button @click="$emit('close')">取消</el-button>
            <el-button type="warning" @click="resetForm('ruleForm')">清空</el-button>
            <el-button type="primary" @click="submitForm(false)" v-throttle="2000">保存</el-button>
            <el-button type="success" @click="submitForm(true)" v-throttle="2000" v-if="!isBatch">提交</el-button>
        </div>
    </div>
</template>

<script>
import yhUserselectors from '@/components/YhCom/yh-userselectors.vue'
import decimal from '@/utils/decimalToFixed.js'
import { getDirectorList } from '@/api/operatemanage/base/shop'
import { platformlist } from '@/utils/tools'
import { GetYWOptimizeGoodsRecord, SaveYWOptimizeGoodsRecord, BatchSaveYWOptimizeGoodsRecord } from '@/api/inventory/YWOptimizeGoods';
const optimizeList = [
    { label: "降本", value: "降本" },
    { label: "提质", value: "提质" },
    { label: "优化库位", value: "优化库位" },
    { label: "更换包材", value: "更换包材" },
    { label: "优化售前", value: "优化售前" },
    { label: "优化售后", value: "优化售后" },
    { label: "更换包装方式", value: "更换包装方式" },
    { label: "新建编码", value: "新建编码" },
    { label: "优化链接", value: "优化链接" },
    { label: "推给运营上架", value: "推给运营上架" }
]
export default {
    components: {
        yhUserselectors
    },
    props: {
        editRow: {
            type: Object,
            default: () => null
        },
        selectList: {
            type: Array,
            default: () => []
        },
        isBatch: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            platformlist,
            directorlist: [],
            ruleForm: {
                dtls: []
            },
            optimizeList,
            value: '',
            rules: this.isBatch ? null : {
                goodsType: [
                    { required: true, message: '类型不能为空', trigger: 'change' }
                ],
                optimizeWayList: [
                    { required: true, message: '优化方向不能为空', trigger: 'change' }
                ],
                costType: [
                    { required: true, message: '成本类型不能为空', trigger: 'change' }
                ],
                quality: [
                    { required: true, message: '品质不能为空', trigger: 'change' }
                ],
                packageChange: [
                    { required: true, message: '包装变化不能为空', trigger: 'change' }
                ],
                grammage: [
                    { required: true, message: '克重不能为空', trigger: 'blur' }
                ],
                thickness: [
                    { required: true, message: '厚度不能为空', trigger: 'blur' }
                ],
            },
        }
    },
    computed: {
        computedJiangJiaRate() {
            const newCost = this.ruleForm.newCost ?? 0
            let rate = 0
            if (this.editRow.cost == 0 || newCost == 0) {
                this.$set(this.ruleForm, 'jiangJiaRate', 0.00)
                rate = 0.00
            } else {
                rate = (((this.editRow.cost - newCost) / this.editRow.cost) * 100).toFixed(2)
            }
            this.$set(this.ruleForm, 'jiangJiaRate', rate)
            if (this.ruleForm.dtls && this.ruleForm.dtls.length > 0) {
                this.ruleForm.dtls.forEach((item, i) => {
                    //毛一利润
                    this.$set(item, 'profit1', item.salePrice - newCost - (item.ygExpressFee ?? 0))
                    console.log(item.profit1, 'profit1');
                    //毛一利润率
                    this.$set(item, 'profit1Rate', !item.salePrice ? 0 : Number(decimal(decimal(item.profit1, item.salePrice, '/'), 100, '*').toFixed(2)))
                })
            }
            console.log(this.ruleForm.dtls);
            return rate ?? 0.00

        }
    },
    created() {
        this.getDtl();
    },
    mounted() {
        this.init();
    },
    methods: {
        async submitForm(isApproved) {
            this.ruleForm.operateSpecialUserIds = (this.ruleForm.operateSpecialUserIds1 && this.ruleForm.operateSpecialUserIds1?.length > 0) ? this.ruleForm.operateSpecialUserIds1?.join(',') : ''
            if (this.ruleForm.operateSpecialUserIds1?.length > 50) return this.$message.error('运营专员最多选择50个');
            if (!this.isBatch) {
                this.$refs.ruleForm.validate(async (valid) => {
                    if (valid) {
                        const obj = {
                            'length': '长',
                            'width': '宽',
                            'height': '高',
                        }
                        for (const key in obj) {
                            if (this.ruleForm[key] === null || this.ruleForm[key] === undefined) {
                                this.$message.error(`${obj[key]}不能为空`);
                                return;
                            }
                        }
                        if (this.ruleForm.dtls?.length > 0) {
                            const obj1 = {
                                'platform': '竞品平台',
                                'chooseId': '竞品ID',
                                'monthSaleCount': '竞品月销',
                                'daySaleCount': '竞品日销',
                                'salePrice': '竞品售价',
                            }
                            this.ruleForm.dtls.forEach(item => {
                                for (const key in obj1) {
                                    if (item[key] === null || item[key] === undefined || item[key] === '') {
                                        this.$message.error(`${obj1[key]}不能为空`);
                                        throw new Error(`${obj1[key]}不能为空`);
                                    }
                                }
                            })
                        }
                        if (!this.isBatch) {
                            const { success } = await SaveYWOptimizeGoodsRecord({ ...this.ruleForm, isApproved, goodsCode: this.editRow.goodsCode })
                            if (!success) return
                            this.$message.success(isApproved ? '提交成功' : '保存成功');
                        }
                        this.$emit('close')
                        this.$emit('getList')
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            } else {
                const goodsCodes = this.selectList.map(item => item.goodsCode)
                const data = { ...this.ruleForm, isApproved }
                const { success } = await BatchSaveYWOptimizeGoodsRecord({ data, goodsCodes })
                if (!success) return
                this.$message.success(isApproved ? '批量提交成功' : '批量保存成功');
                this.$emit('close')
                this.$emit('getList')
            }
        },
        resetForm() {
            this.ruleForm = {
                dtls: [],
                goodsType: null,
                optimizeWayList: null,
                firOptimizeProgress: '',
                secOptimizeProgress: '',
                newCost: undefined,
                costType: null,
                quality: null,
                packageChange: null,
                length: undefined,
                width: undefined,
                height: undefined,
                grammage: undefined,
                thickness: undefined,
                operaterUserRemark: '',
                saleBeforeUserRemark: '',
                saleAfterUserRemark: '',
                optimizeResult: null,
                optimizeCount: this.ruleForm.optimizeCount,
                operateSpecialUserIds1: [],
                isAdjustJstJiangjia: null,
                isSendToOperateUser: null
            }
        },
        async getDtl() {
            if (!this.editRow) return
            const { data } = await GetYWOptimizeGoodsRecord({ goodsCode: this.editRow.goodsCode });
            data.newCost = data.newCost === null ? undefined : data.newCost;
            data.dtls = data.dtls || [];
            data.operateSpecialUserIds1 = data.operateSpecialUserIds ? data.operateSpecialUserIds.split(',') : [];
            this.$nextTick(() => {
                this.$refs.ruleForm?.resetFields()
            })
            this.$set(this, 'ruleForm', data)
        },
        async init() {
            var res3 = await getDirectorList();
            this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        addProps() {
            this.ruleForm.dtls.push({
                goodsCode: this.editRow.goodsCode,
                platform: null,
                chooseId: null,
                monthSaleCount: null,
                daySaleCount: null,
                salePrice: null,
                ygExpressFee: null,
                profit1: null,
                profit1Rate: null
            });
        }
    }
}
</script>

<style scoped lang="scss">
.edit_container {
    height: 500px;
}

.form_header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 10px;
}

.imgCss ::v-deep img {
    min-width: 60px !important;
    min-height: 60px !important;
    width: 60px !important;
    height: 60px !important;
}

.el-row {
    margin-bottom: 10px;
}

.btnGroup {
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

::v-deep .el-select__tags-text {
    max-width: 40px;
}
</style>