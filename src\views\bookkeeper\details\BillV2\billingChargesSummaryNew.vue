<template>
    <MyContainer>
      <template #header>
        <div class="top">
          <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
            :clearable="false" :value-format="'yyyy-MM-dd'" @change="changeTime">
          </el-date-picker>
          <el-select v-model="ListInfo.platforms" placeholder="平台" clearable filterable class="publicCss" multiple
            collapse-tags>
            <el-option label="天猫" :value="1" />
            <el-option label="天猫微信" :value="101" />
            <el-option label="拼多多" :value="2" />
            <el-option label="阿里巴巴" :value="4" />
            <el-option label="抖音" :value="6" />
            <el-option label="京东" :value="7" />
            <el-option label="淘工厂" :value="8" />
            <el-option label="淘工厂微信" :value="81" />
            <el-option label="淘宝" :value="9" />
            <el-option label="淘宝微信" :value="91" />
            <el-option label="拼多多跨境" :value="13" />
            <el-option label="快手" :value="14" />
            <el-option label="视频号" :value="20" />
          </el-select>
          <el-input v-model.trim="ListInfo.billTypes" placeholder="账单项目" maxlength="50" clearable class="publicCss" />
          <div>
            <el-button type="primary" @click="getList('search')">查询</el-button>
            <el-button type="primary" @click="exportProps(1)">导出</el-button>
            <!-- <el-dropdown @command="exportProps">
                <el-button style="height: 28px;margin-left: 5px;" type="primary">
                  导出<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown" >
                    <el-dropdown-item command="1">导出</el-dropdown-item>
                    <el-dropdown-item command="2">导出明细</el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown> -->
          </div>
        </div>
      </template>
      <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
        :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
        :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
        :height="'100%'">
      </vxetablebase>
      <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template>

      <el-dialog title="账单明细" :visible.sync="detailPopupDialog" width="70%" v-dialogDrag>
        <div slot="title">
          <span class="title-text">账单明细</span>
          <el-button type="primary" @click="detailExport(1)" style="margin-left: 20px;">导出</el-button>
        </div>
        <div style="height: 500px;" v-loading="detailloading">
          <billingChargesDetail :detailArgument="detailArgument" v-if="detailPopupDialog" />
        </div>
      </el-dialog>
    </MyContainer>
  </template>

  <script>
  import MyContainer from "@/components/my-container";
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import { pickerOptions, formatPlatform } from '@/utils/tools'
  import billingChargesDetail from './billingChargesDetail.vue'
  import { pageUnCalcCharBillItem, exportUnCalcCharBillItem, exportUnCalcCharBillItemDetail } from '@/api/bookkeeper/reportdayV2'
  import dayjs from 'dayjs'
  const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'platform', label: '平台', formatter: (row) => formatPlatform(row.platform), },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'billingItem', label: '账单项目', },
    { sortable: 'custom', width: '370', align: 'center', prop: 'inAmountIncome', label: '收入金额', type: 'click', handle: (that, row) => that.onAmountDetail(row, 1), },
    { sortable: 'custom', width: '370', align: 'center', prop: 'outAmountIncome', label: '支出金额', type: 'click', handle: (that, row) => that.onAmountDetail(row, 2), },
    { sortable: 'custom', width: '370', align: 'center', prop: 'amountIncome', label: '金额', },
  ]
  export default {
    name: "billingChargesSummaryNew",
    components: {
      MyContainer, vxetablebase, billingChargesDetail
    },
    data() {
      return {
        detailloading: false,
        detailArgument: {},
        detailName: true,
        detailPopupDialog: false,
        that: this,
        ListInfo: {
          currentPage: 1,
          pageSize: 50,
          orderBy: null,
          isAsc: false,
          startTime: null,//开始时间
          endTime: null,//结束时间
          platforms: [],//平台
          billTypes: null,//账单类型
        },
        timeRanges: [],
        tableCols,
        tableData: [],
        summaryarry: {},
        total: 0,
        loading: false,
        pickerOptions,
      }
    },
    async mounted() {
      await this.getList()
    },
    methods: {
      //账单明细导出
      async detailExport(val) {
        this.detailloading = true
        const params = {
          startTime: this.ListInfo.startTime,
          endTime: this.ListInfo.endTime,
          platform: this.detailArgument.platform,
          billType: this.detailArgument.billingItem
        }
        if(val == 2){
          params.platforms = this.ListInfo.platforms
        }
        const res = await exportUnCalcCharBillItemDetail(params)
        if (!res) {
          this.detailloading = false
          return
        }
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/zip" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '日报账单费用-账单明细汇总' + new Date().toLocaleString() + '.zip')
        aLink.click()
        this.detailloading = false
      },
      async onAmountDetail(row, val) {
        this.detailArgument = {}
        this.detailArgument = {
          startTime: this.ListInfo.startTime,
          endTime: this.ListInfo.endTime,
          platform: row.platform,
          billingItem: row.billingItem
        }
        this.detailPopupDialog = true
      },
      //时间改变
      async changeTime(e) {
        this.ListInfo.startTime = e ? e[0] : null
        this.ListInfo.endTime = e ? e[1] : null
      },
      //导出
      async exportProps(val) {
        if(val == 1){
        this.loading = true
        const { data } = await exportUnCalcCharBillItem(this.ListInfo)
        this.loading = false
        const aLink = document.createElement("a");
        let blob = new Blob([data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '日报账单费用汇总' + new Date().toLocaleString() + '.xlsx')
        aLink.click()
        }else {
          if(this.ListInfo.platforms && this.ListInfo.platforms.length == 0){
            this.$message.error('请选择平台')
            return
          }
          this.detailExport(2)
        }
      },
      //获取列表
      async getList(type) {
        if (type == 'search') {
          this.ListInfo.currentPage = 1
          this.$refs.pager.setPage(1)
        }
        if (this.timeRanges && this.timeRanges.length == 0) {
          //默认给近1天时间
          this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
          this.ListInfo.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
          this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
        }
        this.loading = true
        const { data, success } = await pageUnCalcCharBillItem(this.ListInfo)
        if (success) {
          this.tableData = data.list
          this.total = data.total
          this.summaryarry = data.summary
          this.loading = false
        } else {
          //获取列表失败
          this.$message.error('获取列表失败')
        }
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }
  </script>

  <style scoped lang="scss">
  .top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
      width: 200px;
      margin: 0 5px 5px 0;
    }
  }

  ::v-deep .el-select__tags-text {
    max-width: 55px;
  }
  </style>
