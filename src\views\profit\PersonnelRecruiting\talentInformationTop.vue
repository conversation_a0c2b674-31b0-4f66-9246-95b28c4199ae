<template>
    <div>
        <el-form :model="ruleForm" :disabled="!isEdit" :rules="rules" ref="topForm" label-width="110px" class="ruleForm">
            <el-row>
                <el-col :span="8">
                    <el-form-item label="招聘部门" prop="department">
                        <el-select ref="selectDept" :disabled="candidateInfo.candidateId != null"
                            v-model="ruleForm.department" clearable style="width: 100%" size="mini" placeholder="招聘部门">
                            <el-option hidden value="一级菜单" :label="chooseName"></el-option>
                            <el-tree style="width: 200px;" :data="deptList" :props="defaultProps"
                                :expand-on-click-node="false" :check-on-click-node="true" @node-click="handleNodeClick">
                            </el-tree>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="16">
                    <el-form-item label="招聘岗位" prop="positionName">
                        <!-- <el-input v-model="ruleForm.positionName" placeholder="请输入招聘岗位">
                        </el-input> -->
                        <el-select v-model="ruleForm.positionName" style="width: 100%;" @change="handlePosition"
                            :disabled="candidateInfo.candidateId != null">
                            <el-option v-for="item in postList" :label="item.positionName" :value="item.planId"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="招聘专员" prop="recruiter">
                        <el-select ref="recruiterSelect" v-model="ruleForm.recruiter" placeholder="招聘专员" style="width: 100%"
                            size="mini" :disabled="candidateInfo.candidateId != null" clearable
                            @change="handleRecruiterList">
                            <el-option v-for="item in recruiterList" :key="item.DDUserId" :label="item.UserName"
                                :value="item.UserName"></el-option>
                        </el-select>
                        <!-- <el-input v-model="ruleForm.recruiter" disabled></el-input> -->
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="预计到面时间" prop="preArrivedTestTime">
                        <!-- <el-date-picker type="datetime" placeholder="选择到面时间" clearable value-format="yyyy-MM-dd hh:mm:ss" v-model="ruleForm.preArrivedTestTime"
                            style="width: 100%;"></el-date-picker> -->
                        <el-date-picker type="date" value-format="yyyy-MM-dd" clearable placeholder="选择到面时间"
                            v-model="ruleForm.preArrivedTestTime" style="width: 100%;"></el-date-picker>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="初面结果" prop="initialTestResult">
                        <el-select v-model="ruleForm.initialTestResult" clearable placeholder="请选择初面结果" style="width: 100%;"
                            @clear="() => { ruleForm.initialTestResult = null }">
                            <el-option label="通过" :value="true"></el-option>
                            <el-option label="未通过" :value="false"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="到面部门" prop="initialTestDepartment">
                        <el-select ref="selectInitialTestDepartment" v-model="ruleForm.initialTestDepartment" clearable
                            style="width: 100%" size="mini" placeholder="招聘部门">
                            <el-option hidden value="一级菜单" :label="ruleForm.initialTestDepartment"></el-option>
                            <el-tree style="width: 200px;" :data="deptList" :props="defaultProps"
                                :expand-on-click-node="false" :check-on-click-node="true"
                                @node-click="handleInitialNodeClick">
                            </el-tree>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="到面时间" prop="initialTestDate">
                        <el-date-picker type="date" value-format="yyyy-MM-dd" placeholder="选择到面时间" clearable
                            :picker-options="pickerOptions" v-model="ruleForm.initialTestDate"
                            style="width: 100%;"></el-date-picker>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row v-if="ruleForm.initialTestResult">
                <el-col :span="8">
                    <el-form-item label="复试结果" prop="finalTestResult">
                        <el-select v-model="ruleForm.finalTestResult" clearable placeholder="请选择复试结果" style="width: 100%;"
                            @clear="() => { ruleForm.finalTestResult = null }">
                            <el-option label="通过" :value="true"></el-option>
                            <el-option label="未通过" :value="false"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="复试部门" prop="finalTestDepartment">
                        <el-select ref="selectFinalTestDepartmentt" v-model="ruleForm.finalTestDepartment" clearable
                            style="width: 100%" size="mini" placeholder="招聘部门">
                            <el-option hidden value="一级菜单" :label="ruleForm.finalTestDepartment"></el-option>
                            <el-tree style="width: 200px;" :data="deptList" :props="defaultProps"
                                :expand-on-click-node="false" :check-on-click-node="true"
                                @node-click="handleFinalNodeClick">
                            </el-tree>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="复试时间" prop="finalTestDate">
                        <el-date-picker type="date" value-format="yyyy-MM-dd" clearable placeholder="选择到面时间"
                            :picker-options="pickerOptions" v-model="ruleForm.finalTestDate"
                            style="width: 100%;"></el-date-picker>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div class="des-box">
            <el-descriptions title="面谈记录:" :column="4" size="medium" :colon="false">
                <!-- <template slot="extra">
                    <el-button type="primary" circle plain icon="el-icon-plus" size="mini" v-if="isEdit"
                        @click="addRow()"></el-button>
                </template> -->

                <!-- <el-descriptions-item label="" span="3" v-if="isEdit">
                    <el-form style="width: 100%;" :model="interviewMeeting" ref="interviewMeeting" label-width="90px"
                        class="ruleForm">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="面谈类型" prop="meetingType">
                                    <el-select v-model="interviewMeeting.meetingType" placeholder="请选择面谈类型"
                                        style="width: 100%;">
                                        <el-option label="转正" value="转正"></el-option>
                                        <el-option label="升职" value="升职"></el-option>
                                        <el-option label="调岗" value="调岗"></el-option>
                                        <el-option label="离职" value="离职"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12"
                                v-if="interviewMeeting.meetingType == '转正' || interviewMeeting.meetingType == '升职' || interviewMeeting.meetingType == '调岗'">
                                <el-form-item label="数据佐证" prop="dataSupport">
                                    <el-input v-model="interviewMeeting.dataSupport" placeholder="请输入佐证数据"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12" v-if="interviewMeeting.meetingType == '离职'">
                                <el-form-item label="离职类型" prop="dimissionType">
                                    <el-select v-model="interviewMeeting.dimissionType" placeholder="请选择面谈类型"
                                        style="width: 100%;">
                                        <el-option label="自离" value="自离"></el-option>
                                        <el-option label="辞职" value="辞职"></el-option>
                                        <el-option label="辞退" value="辞退"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row v-if="interviewMeeting.meetingType == '离职'">
                            <el-col :span="12">
                                <el-form-item label="离职原因" prop="dimissionReason">
                                    <el-input v-model="interviewMeeting.dimissionReason" type="textarea"
                                        placeholder="请输入离职原因"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="面谈时间" prop="meetingDate">
                                    <el-date-picker type="datetime" value-format="yyyy-MM-dd hh:mm:ss" placeholder="选择到面时间"
                                        v-model="interviewMeeting.meetingDate" style="width: 100%;"></el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="面谈内容" prop="meetingContent">
                                    <el-input v-model="interviewMeeting.meetingContent" type="textarea"
                                        placeholder="请输入面谈内容"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-form-item>
                            <el-button type="primary" @click="addRow">添加</el-button>
                        </el-form-item>
                    </el-form>
                </el-descriptions-item> -->
                <template v-if="ruleForm.interviewMeetingList.length == 0">
                    <el-descriptions-item label="" span="4">暂无面谈记录信息</el-descriptions-item>
                </template>
                <template v-else>
                    <template v-for="item in ruleForm.interviewMeetingList">
                        <el-descriptions-item label="" span="4" v-if="item.meetingType == '离职'">{{ item.meetingType
                        }}——{{ item.dimissionType }}——{{ item.dimissionReason }}</el-descriptions-item>
                        <el-descriptions-item label="" span="4" v-else> {{
                            item.meetingType }}——{{ item.dataSupport }} </el-descriptions-item>
                        <el-descriptions-item label="" span="1">{{ item.meetingDate }}
                        </el-descriptions-item>
                        <el-descriptions-item label="" span="4">{{ item.meetingContent }}</el-descriptions-item>
                    </template>
                </template>
            </el-descriptions>
            <!-- <div>
                <el-table :data="ruleForm.interviewMeetingList" style="width: 100%" empty-text="暂无面谈记录信息">
                    <el-table-column prop="meetingType" label="面谈类型" width="100">
                        <template slot-scope="scope">
                            <el-select v-model="scope.row.meetingType" placeholder="请选择面谈类型" :disabled="!isEdit"
                                style="width: 100%;">
                                <el-option label="转正" value="转正"></el-option>
                                <el-option label="升职" value="升职"></el-option>
                                <el-option label="调岗" value="调岗"></el-option>
                                <el-option label="离职" value="离职"></el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column prop="dataSupport" label="数据佐证" width="120">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.dataSupport" :disabled="!isEdit"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="meetingDate" label="面谈时间" width="200">
                        <template slot-scope="scope">
                            <el-date-picker type="datetime" value-format="yyyy-MM-dd" :disabled="!isEdit"
                                placeholder="选择面谈时间" v-model="scope.row.meetingDate" style="width: 100%;"></el-date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column prop="meetingContent" label="面谈内容">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.meetingContent" :disabled="!isEdit"
                                placeholder="请输入面谈内容"></el-input>
                        </template>
                    </el-table-column>
                </el-table>
            </div> -->
        </div>
        <el-divider></el-divider>
    </div>
</template>
  
<script>
import { getALLDDDeptTree, getDeptUsers } from '@/api/profit/personnel'
import { getPageRecruitmentPlan, getRecruitmentPlan } from '@/api/profit/hr'

export default {
    props: {
        isEdit: {
            type: Boolean,
            default: () => { return false; }
        },
        candidateInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
    },
    data () {
        return {
            activeName: '',
            isOpen: false,
            title: '标题',
            content: '这里是要折叠的内容',
            // isEdit:true,
            chooseName: '',
            chooseInitialName: '',
            chooseFinallName: '',
            defaultProps: {
                children: 'childDeptList',
                label: 'name'
            },
            deptList: [],
            recruiterList: [],
            postList: [],
            ruleForm: {
                ddDeptId: 0,
                department: null,
                positionName: null,
                recruiterDDUserId: null,
                preArrivedTestTime: null,
                recruiter: null,
                initialTestDate: null,
                initialTestResult: null,
                initialTestDepartment: null,
                finalTestResult: null,
                finalTestDepartment: null,
                finalTestDate: null,
                interviewMeetingList: [],
            },
            rules: {
                department: [
                    { required: true, message: '请选择招聘部门', trigger: 'change' }
                ],
                positionName: [
                    { required: true, message: '请选择招聘岗位', trigger: 'change' }
                ],
                recruiter: [
                    { required: !this.candidateInfo.candidateId ? true : false, message: '请选择招聘专员', trigger: 'change' }
                ],
                // preArrivedTestTime: [
                //     { required: true, message: '请选择到面时间', trigger: 'change' }
                // ],
                initialTestDate: [{
                    required: false, message: '请选择初试到面时间', trigger: 'change'
                },],
                finalTestDate: [{
                    required: false, message: '请选择复试时间', trigger: 'change'
                },]
            },
            interviewMeeting: {
                meetingDate: null,
                meetingType: null,
                dimissionType: null,
                dimissionReason: null,
                dataSupport: null,
                meetingContent: null
            },
            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() > Date.now()
                },
            },
        }
    },
    watch: {
        'ruleForm.initialTestResult': { // 对对象的某一个属性进行深度监听
            handler (nv) {
                if (nv != null) {
                    if (this.ruleForm.ddDeptId != null && this.ruleForm.initialTestDepartment == null) {
                        this.ruleForm.initialTestDepartment = this.ruleForm.department;
                    }
                    this.rules.initialTestDate[0].required = true;
                    if (!nv) {
                        this.ruleForm.finalTestDepartment = null;
                        this.ruleForm.finalTestResult = null;
                        this.ruleForm.finalTestDate = null;
                        this.rules.finalTestDate[0].required = false;
                    }
                } else {
                    this.ruleForm.initialTestDepartment = null;
                    this.ruleForm.initialTestDate = null;
                    this.rules.initialTestDate[0].required = false;
                    this.ruleForm.finalTestDepartment = null;
                    this.ruleForm.finalTestResult = null;
                    this.ruleForm.finalTestDate = null;
                    this.rules.finalTestDate[0].required = false;
                }
            },
            immediate: true,
            deep: true
        },
        'ruleForm.finalTestResult': { // 对对象的某一个属性进行深度监听
            handler (nv) {
                if (nv != null) {
                    if (this.ruleForm.ddDeptId != null && this.ruleForm.finalTestDepartment == null) {
                        this.ruleForm.finalTestDepartment = this.ruleForm.department;
                    }
                    this.rules.finalTestDate[0].required = true;
                } else {
                    this.ruleForm.finalTestDepartment = null;
                    this.ruleForm.finalTestDate = null;
                    this.rules.finalTestDate[0].required = false;
                }
            },
            immediate: true,
            deep: true
        }
    },
    mounted () {
        this.getDeptList();
        // this.getRecruiters();
        // if (this.candidateInfo.candidateId && this.isEdit) {
        for (const prop in this.candidateInfo) {
            if (prop in this.ruleForm) {
                this.ruleForm[prop] = this.candidateInfo[prop];
            }
        }
        if (!this.ruleForm.interviewMeetingList) {
            this.ruleForm.interviewMeetingList = [];
        }
        // if (this.ruleForm.ddDeptId) {
        //     this.ruleForm.initialTestDepartment =this.ruleForm.department;
        //     this.ruleForm.finalTestDepartment =this.ruleForm.department;
        // }

        // }
    },
    methods: {
        // 获取招聘岗位列表
        async getDataList (ddDeptId) {
            const params = {
                closeStatus: 0,//计划状态:-1删除、0进行中、1已完成
                ddDeptId: ddDeptId,//招聘部门
                currentPage: 1,
                pageSize: 50,
                positionName: null,//岗位名称
                recruiterIds: [],//招聘专员
                closeReason: null,//完成原因
            };
            const res = await getPageRecruitmentPlan(params);
            this.postList = res.data.list;
        },
        reset () {
            this.ruleForm = {
                ddDeptId: null,
                planId: null,
                department: null,
                positionName: null,
                preArrivedTestTime: null,
                recruiterDDUserId: null,
                recruiter: null,
                initialTestDate: null,
                initialTestResult: null,
                initialTestDepartment: null,
                finalTestResult: null,
                finalTestDepartment: null,
                finalTestDate: null,
                interviewMeetingList: [],
            }
        },
        //获取招聘专员
        getRecruiters () {
            let params = {
                deptName: '人事组',
                includeAllChildDpt: 1,
            }
            getDeptUsers(params).then(res => {
                if (res.success) {
                    this.recruiterList = res.data;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        // 节点点击事件
        handleNodeClick (data) {
            // 配置树形组件点击节点后，设置选择器的值，配置组件的数据
            this.chooseName = data.name;
            this.ruleForm.ddDeptId = data.dept_id;
            this.ruleForm.department = data.name;
            this.getDataList(data.dept_id);
            this.ruleForm.recruiter = null,
                this.ruleForm.positionName = null,
                // 选择器执行完成后，使其失去焦点隐藏下拉框效果
                this.$refs.selectDept.blur();
        },
        handleRecruiterList (val) {
            this.ruleForm.recruiterDDUserId = this.recruiterList.find(item => item.UserName == val).DDUserId
        },
        handlePosition (planId) {
            // this.chooseName = data.name;
            // this.ruleForm.ddDeptId = data.dept_id;
            // this.ruleForm.department = data.name;
            // this.getDataList(data.dept_id);
            // this.$refs.selectDept.blur();
            getRecruitmentPlan({ planId: planId }).then(res => {
                if (res.success) {
                    // this.ruleForm.recruiter = res.data.recruiters;
                    this.ruleForm.planId = res.data.planId;
                    this.ruleForm.positionName = res.data.positionName;
                    this.recruiterList = JSON.parse(res.data.recruiterJson)
                }
            })
        },
        handleInitialNodeClick (data) {
            this.chooseInitialName = data.name;
            this.ruleForm.initialTestDepartment = data.name;
            this.$refs.selectInitialTestDepartment.blur();
        },
        handleFinalNodeClick (data) {
            this.chooseFinallName = data.name;
            this.ruleForm.finalTestDepartment = data.name;
            this.$refs.selectFinalTestDepartmentt.blur();
        },
        // 获取部门列表
        async getDeptList () {
            await getALLDDDeptTree().then(res => {
                if (res.success) {
                    this.deptList = res.data.childDeptList;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        addRow () {
            // this.ruleForm.interviewMeetingList.forEach((element, i) => {
            //     if (!element.meetingType) {
            //         this.ruleForm.interviewMeetingList.splice(i, 1);
            //     }
            // });
            // this.ruleForm.interviewMeetingList.push({
            //     // meetingType: null,
            //     // dataSupport: null,
            //     // meetingContent: null,
            //     // meetingDate:null
            // });
            if (this.interviewMeeting.meetingType) {
                this.ruleForm.interviewMeetingList.push(this.interviewMeeting);
                this.interviewMeeting = {
                    meetingDate: null,
                    meetingType: null,
                    dimissionType: null,
                    dimissionReason: null,
                    dataSupport: null,
                    meetingContent: null
                }
            }

        },
        removeRow (index) {
        },

        toggleContent () {
            this.isOpen = !this.isOpen
            this.activeName = this.isOpen ? 'content' : ''
        },
        //提交
        submitForm (formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    alert('submit!');
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
    }
}
</script>
  
<style scoped>
.title {
    cursor: pointer;
}

.ruleForm {
    padding: 10px;
}

.des-box {
    padding: 0 10px 0 30px
}

::v-deep .el-descriptions__title {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
}

::v-deep .el-descriptions__header {
    margin-bottom: 10px;
}

::v-deep .el-divider--horizontal {
    margin: 5px 0;
}
</style>