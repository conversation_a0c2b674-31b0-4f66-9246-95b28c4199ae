<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <div class="top">
                <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始售后申请时间" end-placeholder="结束售后申请时间"
                    style="width: 250px" :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                <el-select v-model="filter.shopCode" placeholder="店铺" style="width:160px;" filterable clearable>
                    <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                        :value="item.shopCode">
                    </el-option>
                </el-select>
                <el-input v-model.trim="filter.goodsCode" placeholder="商品编码" style="width:160px;" clearable
                    :maxlength="50" />
                <el-input v-model.trim="filter.goodsName" placeholder="商品名称" style="width:160px;" clearable
                    :maxlength="50" />
                <el-select v-model="filter.saleAfterReason" placeholder="售后原因" style="width:160px;" filterable
                    clearable>
                    <el-option v-for="item in saleAfterReasonBaseList" :key="item" :label="item" :value="item">
                    </el-option>
                </el-select>
                <el-select v-model="filter.expressStatus" placeholder="发货物流状态" style="width:140px;" filterable
                    clearable>
                    <el-option v-for="item in expressStatusBaseList" :key="item" :label="item" :value="item">
                    </el-option>
                </el-select>
                <el-select v-model="filter.backExpressCom" placeholder="退货物流公司" style="width:140px;" filterable
                    clearable>
                    <el-option v-for="item in backExpressComBaseList" :key="item" :label="item" :value="item">
                    </el-option>
                </el-select>
                <el-select v-model="filter.isBackExpressNo" placeholder="有无退货物流单号" style="width:140px;" filterable
                    clearable>
                    <el-option :key="true" label="有" :value="true" />
                    <el-option :key="false" label="无" :value="false" />
                </el-select>
                <el-select v-model="filter.batchStr" placeholder="批次号" style="width:120px;" filterable clearable>
                    <el-option v-for="item in pettyPaymentList" :key="item.batchStr" :label="item.batchStr"
                        :value="item.batchStr">
                    </el-option>
                </el-select>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExportOnlyRefund">导出</el-button>
            </div>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='pagelist' @select='selectchange' :isSelection='false'
            :tableCols='tableCols' :loading="listLoading"
            :style="{ height: queryInfo ? '400px' : '100%', overflowY: 'hidden' }" />
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getpageList" />
        </template>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import dayjs from 'dayjs'
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import {
    getBackRefundOrderPageList, exportBackRefundOrderList
} from '@/api/customerservice/douyinrefund'

const tableCols = [
    { istrue: true, prop: 'saleAfterNo', label: '售后单号', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'orderNo', label: '订单号', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: '250', sortable: 'custom' },
    { istrue: true, prop: 'shopName', label: '店铺', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'payableAmount', label: '应付金额', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'saleAfterApplyTime', label: '售后申请时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'saleAfterEndTime', label: '售后完结时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'goodsBackAmount', label: '商品退款金额', width: '110', sortable: 'custom' },
    { istrue: true, prop: 'saleAfterReason', label: '售后原因', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'expressStatus', label: '发货物流状态', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'backExpressNo', label: '退货物流单号', width: '140', sortable: 'custom' },
    { istrue: true, prop: 'backExpressCom', label: '退货物流公司', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'batchStr', label: '批次号', width: '120', sortable: 'custom' },
];
export default {
    name: "backrefundorder",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
    props: ["saleAfterReasonBaseList", "expressStatusBaseList", "backExpressComBaseList", "batchStrBaseList", "queryInfo"],
    data() {
        return {
            that: this,
            pageLoading: false,
            filter: {
                timerange: [],
                startDate: null,
                endDate: null,
                goodsCode: null,
                goodsName: null,
                shopCode: null,
                saleAfterReason: null,
                expressStatus: null,
                backExpressCom: null,
                isBackExpressNo: null,//是否有退货物流单号
                batchStr: null,
            },
            shopList: [],
            pettyPaymentList: [],
            saleAfterReasonList: [],//售后原因
            expressStatusList: [],//发货物流状态
            backExpressComList: [],//退货物流公司
            tableCols: tableCols,
            listLoading: false,
            pagelist: [],
            total: 0,
            summaryarry: {},
            pager: { OrderBy: "createdtime", IsAsc: false },
            sels: [], // 列表选中列
            selids: [],
        };
    },
    async mounted() {
        await this.getAllShopList();
        this.onSearch();
        window.showlist_backrefundorder = this.showlist_backrefundorder1;
    },
    methods: {
      async changeTime(e) {
          this.filter.startDate = e ? e[0] : null
          this.filter.endDate = e ? e[1] : null
          this.filter.timerange = e
          this.batchScreening()
        },
        batchScreening() {
          let dates = [ new Date(this.filter.timerange[0]), new Date(this.filter.timerange[1] + ' 23:59:59') ];
          this.pettyPaymentList = this.batchStrBaseList.filter(item => {
            let createdTime = new Date(item.createdTime);
            return createdTime >= dates[0] && createdTime <= dates[1];
          });
        },
        showlist_backrefundorder1(param) {
            if (!param.startDate || !param.endDate)
                this.filter.timerange = [];
            else
                this.filter.timerange = [param.startDate, param.endDate];
            this.filter.shopCode = param.shopCode;
            this.filter.goodsCode = param.goodsCode;
            this.filter.isBackExpressNo = param.isBackExpressNo;
            this.filter.saleAfterReason = param.saleAfterReason;
            this.filter.expressStatus = param.expressStatus;
            if (param.backExpressComSort == true) {
                this.pager = { OrderBy: "backExpressCom", IsAsc: false }
            }
            this.onSearch()
        },
        showlist_onlyrefundorder(param) {
            if (!param.startDate || !param.endDate)
                this.filter.timerange = [];
            else
                this.filter.timerange = [param.startDate, param.endDate];
            this.filter.shopCode = param.shopCode;
            this.filter.goodsCode = param.goodsCode;
            this.filter.saleAfterReason = param.saleAfterReason;
            this.filter.expressStatus = param.expressStatus;
            if (param.onlysaleAfterReasonSort == true) {
                this.pager = { OrderBy: "saleAfterReason", IsAsc: false }
            }
            this.onSearch()
        },
        async getAllShopList() {
            let shops = await getAllShopList({ platforms: [6] });
            this.shopList = [];
            this.shopList.push({ shopCode: '未知', shopName: '未知' });
            shops.data?.forEach(f => {
                if (f.shopCode && f.platform == 6)
                    this.shopList.push(f);
            });
            if (this.filter.timerange.length == 0) {
              this.filter.startDate = dayjs().format('YYYY-MM-DD')
              this.filter.endDate = dayjs().format('YYYY-MM-DD')
              this.filter.timerange = [this.filter.startDate, this.filter.endDate]
              this.batchScreening()
            }
        },
        getParam() {
            if (this.filter.timerange && this.filter.timerange.length > 0) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
                this.$message({ message: "请先选择日期！", type: "warning", });
                return;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            let params = {
                ...para,
                ...this.pager,
                ...pager
            };
            return params;
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getpageList();
        },
        async getpageList() {
            let params = this.getParam();
            this.listLoading = true;
            const res = await getBackRefundOrderPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.pagelist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async onExportOnlyRefund() {
            const params = this.getParam();
            this.listLoading = true;
            const res = await exportBackRefundOrderList(params);
            this.listLoading = false;
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '抖音退货退款订单数据_' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

.top {
    display: flex;
    margin-bottom: 10px;
}
</style>
