<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" ref="refruleForm" label-width="140px" class="demo-ruleForm">
        <el-form-item label="类型：">
          {{ ruleForm.type }}
        </el-form-item>
        <el-form-item label="区域：">
          {{ ruleForm.regionName }}
        </el-form-item>
        <el-form-item label="部门类型：">
          {{ ruleForm.deptType }}
        </el-form-item>
        <el-form-item label="部门：">
          {{ ruleForm.deptName }}
        </el-form-item>
        <el-form-item label="招聘需求人数：">
          <inputNumberYh v-model="ruleForm.recruitDemandCount" :placeholder="'招聘需求人数'" class="publicCss" />
        </el-form-item>
        <!-- <el-form-item label="期初人数：">
          <inputNumberYh v-model="ruleForm.initialCount" :placeholder="'期初人数'" class="publicCss" />
        </el-form-item> -->
        <el-form-item label="入职人数：">
          <inputNumberYh v-model="ruleForm.newHiresCount" :placeholder="'入职人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="新人离职人数：">
          <inputNumberYh v-model="ruleForm.newPeopleResignCount" :placeholder="'新人离职人数'" class="publicCss" />
        </el-form-item>
        <!-- <el-form-item label="正式离职人数：">
          <inputNumberYh v-model="ruleForm.oldPeopleResignCount" :placeholder="'正式离职人数'" class="publicCss" />
        </el-form-item> -->
        <!-- <el-form-item label="调入人数：">
          <inputNumberYh v-model="ruleForm.transferCount" :placeholder="'调入人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="调出人数：">
          <inputNumberYh v-model="ruleForm.outCount" :placeholder="'调出人数'" class="publicCss" />
        </el-form-item> -->
      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { departmentDimensionSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
export default {
  name: 'departmentEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      selectProfitrates: [],
      ruleForm: {
        regionName: '',// 区域
        deptName: '',// 部门
        recruitDemandCount: '',// 招聘需求人数
        initialCount: '',// 期初人数
        newHiresCount: '',// 入职人数
        newPeopleResignCount: '',// 试用期离职人数
        oldPeopleResignCount: '',// 正式离职人数
        transferCount: '',// 调入人数
        outCount: '',// 调出人数
      },
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };
  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod', 2);
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
            this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing")
          const { data, success } = await departmentDimensionSubmit(this.ruleForm)
          if (!success) {
            return
          }
          this.$emit('cancellationMethod', 1);
          resetForm(formName);
        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
