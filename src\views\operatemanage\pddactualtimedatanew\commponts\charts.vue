<template>
 <div style="height:100%;padding-left:10px;overflow: auto; position: relative;">
     <div v-if="analysisData && analysisData.series  && analysisData.series !=null && analysisData.series.length>0" :id="'buschar'+randrom" :style="thisStyle" ></div>
     <div v-else>没有可供展示的图表数据！</div>
 </div>
</template>
<script>
 import * as echarts from 'echarts';
 export default {
     name: 'buschar',
     components: {},
     props: {
         action: { type: Function, default: null },
         parms: { type: Object, default: null },
         analysisData: { type: Object, default: null },
         thisStyle: {
             type: Object,
             default: function () {
                 return {
                     width: '100%', height: '550px', 'box-sizing': 'border-box', 'line-height': '360px'
                 }
             }
         },
         gridStyle: {
             type: Object, default: function () {
                 return {
                     top: '20%',
                     left: '5%',
                     right: '4%',
                     bottom: '15%',
                     containLabel: false
                 }
             }
         },
         legendChanges: { type: Function, default: null },

     },
     data () {
         return {
             that: this,
             randrom: "",
             period: 0,
             pageLoading: false,
             listLoading: false,
             procode: ''
         }
     },
     created () {
         var e = 10;
         var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
             a = t.length,
             n = "";
         for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
         this.randrom = n;
     },
     mounted () {
         this.initcharts();
     },
     methods: {
         initcharts (alldata) {
             let that = this;

             

             if(alldata){

                // alldata.xAxis.map(function (str) {
                //     return str.replace(/ /g, '\n');
                // })
    

                if(!(alldata && alldata.series  && alldata.series !=null && alldata.series.length>0))
                    return;

                this.$nextTick(() => {
                    var chartDom = document.getElementById('buschar' + that.randrom);
                    var myChart = echarts.init(chartDom);
                    myChart.clear();
                    if (alldata.series) {
                        var option = that.Getoptions(alldata);
                        option && myChart.setOption(option);
                    }
                    myChart.on('legendselectchanged', function (params) { 
                        if(that.legendChanges!=null){
                            that.legendChanges(params.selected);
                        }
                    });
                });
             }else{

   
                // that.analysisData.xAxis.map(function (str) {
                //     return str.replace(/ /g, '\n');
                // })

                if(!(that.analysisData && that.analysisData.series  && that.analysisData.series !=null && that.analysisData.series.length>0))
                    return;

                this.$nextTick(() => {
                    var chartDom = document.getElementById('buschar' + that.randrom);
                    var myChart = echarts.init(chartDom);
                    myChart.clear();
                    if (that.analysisData.series) {
                        var option = that.Getoptions(that.analysisData);
                        option && myChart.setOption(option);
                    }
                    myChart.on('legendselectchanged', function (params) { 
                        if(that.legendChanges!=null){
                            that.legendChanges(params.selected);
                        }
                    });
                });
             }
             
         },
         randomString () {
             var e = 10;
             var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
                 a = t.length,
                 n = "";
             for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
             return n
         },
         Getoptions (element) {
             var series = []

             element.series.forEach(s => {
                 series.push({ smooth: true, ...s })
             })
             var yAxis = []
             if (Array.isArray(element.yAxis)) {
                 element.yAxis.forEach(s => {
                     yAxis.push({
                         type: 'value', minInterval: 1, offset: s.offset, splitLine: s.splitLine, position: s.position, name: s.name, max: s.max, min: s.min, axisLabel: {
                             formatter: function (value) {
                                 if (value >= 100000000) {
                                     value = (value / 100000000).toFixed(1) + 'Y';
                                 }
                                 if (value >= 10000000) {
                                     value = (value / 10000000).toFixed(1) + 'KW';
                                 }
                                 if (value >= 10000) {
                                     value = (value / 10000).toFixed(1) + 'W';
                                 }
                                 if (value >= 1000) {
                                     value = (value / 1000).toFixed(1) + 'K';
                                 }
                                 if (value <= -100000000) {
                                     value =(value / 100000000).toFixed(1) + 'Y';
                                 }
                                 if (value <= -10000000) {
                                     value = (value / 10000000).toFixed(1) + 'KW';
                                 }
                                 if (value <= -10000) {
                                     value = (value / 10000).toFixed(1) + 'W';
                                 }
                                 if (value <= -1000) {
                                     value = (value / 1000).toFixed(1) + 'K';
                                 }
                                 return value + s.unit;
                             }
                         }
                     })
                 })
             } else {
                 yAxis = { ...element.yAxis };
             }


             var selectedLegend = {};//{};
             if (element.selectedLegend) {
                 element.legend.forEach(f => {
                     //if(!element.selectedLegend.includes(f)) selectedLegend["'"+f+"'"]=false
                     if (!element.selectedLegend.includes(f)) selectedLegend[f] = false
                 })
             }
             var option = {
                 title: { text: element.title },
                 tooltip: { trigger: 'axis' },
                 legend: {
                     show: false,
                     selected: selectedLegend,
                     data: element.legend
                 },
                 grid: this.gridStyle,
                 toolbox: {
                     feature: {
                         magicType: { show: true, type: ['line', 'bar'] },
                         //restore: {show: true},
                     }
                 },
                 tooltip: {
                     trigger: 'axis',
                     axisPointer: {
                         type: 'cross'
                     },
                     padding: [5, 10]
                 },
                 xAxis: [
                    {
                        type: 'category',
                        data: element.xAxis.map(function (str) {
                            return str.replace(/ /g, '\n');
                        })
                    }
                 ],
                 yAxis: yAxis,
                 series: series
             };

             console.log("打印数据3333",option)
             return option;
         },
     }
 }
</script>

