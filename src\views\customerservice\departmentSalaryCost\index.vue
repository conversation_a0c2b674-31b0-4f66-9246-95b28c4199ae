<template>
 <div style="height:100%;width:100%;">
     <el-tabs v-model="activeName" style="height: 94%; width:100%;" @tab-click="tabclick">
         <el-tab-pane label="客服成本核算" name="first" style="height: 100%;" lazy>
             <customerServiceCosts ref="customerServiceCosts" />
         </el-tab-pane>
         <el-tab-pane label="组成本统计" name="second" style="height: 100%; overflow: auto;" lazy>
             <compositionStatistics ref="compositionStatistics" @templatepageclose = 'templatepageclose'/>
         </el-tab-pane>
         <el-tab-pane label="个人成本统计" name="third" style="height: 100%; overflow: auto;" lazy>
             <personalCost ref="personalCost" />
         </el-tab-pane>
         <el-tab-pane label="成本数据导入" name="four" style="height: 100%; overflow: auto;" lazy>
             <costDataImport ref="costDataImport" />
         </el-tab-pane>
         <el-tab-pane label="客服等级设置" name="five" style="height: 100%; overflow: auto;" lazy>
             <serviceLevelSetting ref="serviceLevelSetting" />
         </el-tab-pane>
         <el-tab-pane label="提成系数设置" name="six" style="height: 100%; overflow: auto;" lazy>
             <coefficientSetting ref="coefficientSetting" />
         </el-tab-pane>
         <el-tab-pane label="客服人员管理" name="seven" style="height: 100%; overflow: auto;" lazy>
             <customerPeopleSetting ref="refcustomerPeopleSetting" />
         </el-tab-pane>


     </el-tabs>
 </div>
</template>

<script>

import customerServiceCosts from '@/views/customerservice/departmentSalaryCost/customerServiceCosts.vue'
import compositionStatistics from '@/views/customerservice/departmentSalaryCost/compositionStatistics.vue'
import personalCost from '@/views/customerservice/departmentSalaryCost/personalCost.vue'
import costDataImport from '@/views/customerservice/departmentSalaryCost/costDataImport.vue'

import serviceLevelSetting from '@/views/customerservice/departmentSalaryCost/serviceLevelSetting.vue'
import coefficientSetting from '@/views/customerservice/departmentSalaryCost/coefficientSetting.vue'
import customerPeopleSetting from '@/views/customerservice/departmentSalaryCost/customerPeopleSetting.vue'

export default {
 name: 'DingDingShow',
 components: { customerServiceCosts, compositionStatistics, personalCost, costDataImport, serviceLevelSetting, coefficientSetting, customerPeopleSetting  },
 data () {
     return {
         activeName: 'first',

     }
 },
 async mounted () {


 },
 methods: {
    templatepageclose(row){
      this.activeName = 'third';
      this.$nextTick(async () => {
        this.$refs.personalCost.onCostStatistics(row);
      })
    },
     async tabclick () {
         this.$nextTick(async () => {
             if (this.activeName == 'first') {
                this.$refs.customerServiceCosts.getFilterList('subgroup');
                this.$refs.customerServiceCosts.getFilterList('region');
             }
             else if (this.activeName == 'second') {
                 this.$refs.compositionStatistics.getFilterList('subgroup');
                 this.$refs.compositionStatistics.getFilterList('region');
             }else if (this.activeName == 'third') {
                 this.$refs.personalCost.getFilterList('subgroup');
                 this.$refs.personalCost.getFilterList('region');
             }else if (this.activeName == 'four') {
                 this.$refs.costDataImport.getFilterList('subgroup');
                 this.$refs.costDataImport.getFilterList('region');
             }
         })
     }
 }
}
</script>

