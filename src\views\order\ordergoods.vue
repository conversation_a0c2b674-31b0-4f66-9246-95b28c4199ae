<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
        <el-form-item >
          <el-date-picker
            v-model="Filter.timerange"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :clearable="false"
          ></el-date-picker>
        </el-form-item>
        <el-form-item >
          <inputYunhan ref="productOrderNoInner" :inputt.sync="Filter.orderNoInner" v-model="Filter.orderNoInner"
            placeholder="内部单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
            @callback="callbackOrderNoInner" title="内部单号">
          </inputYunhan>
        </el-form-item>
        <el-form-item >
          <inputYunhan ref="productOrderNo" :inputt.sync="Filter.orderNo" v-model="Filter.orderNo"
            placeholder="线上订单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
            @callback="callbackOrderNo" title="线上订单号">
          </inputYunhan>
        </el-form-item>
        <el-form-item >
          <el-input v-model.trim="Filter.proCode" placeholder="商品ID" clearable :maxlength="50" />
        </el-form-item>
        <el-form-item >
          <el-input v-model.trim="Filter.goodsCode" placeholder="商品编码" clearable :maxlength="50" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
     <ces-table ref="table" :isSelectColumnCols="true" :that='that' :isIndex='true' @sortchange='sortchange' :tableData='OrderList'
                :tableCols='tableCols' :summaryarry='summaryarry' :tableHandles='tableHandles' :loading="listLoading"></ces-table>
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getOrderList"
      />
    </template>

  </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import inputYunhan from "@/components/Comm/inputYunhan";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { pageOrderGoods, importGoodsOrderData} from "@/api/order/ordergoods";
const tableCols =[
     {istrue:true,prop:'payTime',label:'付款日期', width:'160',sortable:'custom'},
     {istrue:true,prop:'orderNoInner',label:'内部订单号', width:'130',sortable:'custom'},
     {istrue:true,prop:'orderNo',label:'线上订单号', width:'200',sortable:'custom'},
     {istrue:true,prop:'goodsName',label:'商品名称', width:'500',sortable:'custom'},
     {istrue:true,prop:'proCode',label:'商品ID', width:'130',sortable:'custom'},
     {istrue:true,prop:'goodsCode',label:'商品编码', width:'170',sortable:'custom'},
     {istrue:true,prop:'freightMoney',label:'运费', width:'80',sortable:'custom'},
     {istrue:true,prop:'goodFreightMoney',label:'商品运费', width:'120',sortable:'custom',tipmesg:'商品分摊'},
     {istrue:true,prop:'goodsAmount',label:'商品金额', width:'80',sortable:'custom'},
     {istrue:true,prop:'qty',label:'数量', width:'80',sortable:'custom'},

    ];
    const tableHandles1=[

      ];
export default {
  name: "Users",
  components: {cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, inputYunhan},
  data() {
    return {
      that:this,
      Filter: {
        StartCreatedTime: null,
        EndCreatedTime: null,
        timerange: null,
        orderNoInner: null,
        orderNo: null,
        proCode: null,
        goodsCode: null,
      },
      OrderList: [],
      total: 0,
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      dialogVisible: false,
      userNameReadonly: true,
      fileList:[],
      importFilte:{companyid:null,warehouse:null},
      batchNumber:"",
      pager:{OrderBy:"payTime",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      summaryarry:{},
    };
  },
  async mounted() {
    await this.onSearch();
  },
  async created(){
    await this.init()
  },
  methods: {
    callbackOrderNoInner(val) {
      this.Filter.orderNoInner = val;
    },
    callbackOrderNo(val) {
      this.Filter.orderNo = val;
    },
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init(){
        var date1 = new Date(); date1.setDate(date1.getDate()-7);
        var date2 = new Date(); date2.setDate(date2.getDate()-1);
        this.Filter.timerange=[];
        this.Filter.timerange[0]=this.datetostr(date1);
        this.Filter.timerange[1]=this.datetostr(date2);
      },
    sortchange(column){
      if(!column.order)
        this.pager={OrderBy:"payTime",IsAsc:false}
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getOrderList();
    },
    async getOrderList() {
      const para = {...this.Filter};
      if (para.timerange&&para.timerange.length>0)
        para.StartCreatedTime = para.timerange[0];
      if (para.timerange&&para.timerange.length>1)
        para.EndCreatedTime = para.timerange[1];

      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      this.listLoading = true;
      const res = await pageOrderGoods(params);
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      this.summaryarry=res.data?.summary;
      const data = res.data.list;
      data.forEach((d) => {
        d._loading = false;
      });
      this.OrderList = data;
    },
    // 选择
    onSelsChange(sels) {
      this.sels = sels;
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
