<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" label-width="100px" label-position="right" :disabled="!formEditMode">
                <el-row >
                    <el-col :span="6">
                        <el-form-item  label="内部订单：">
                            {{form.innerOrderNum}}
                        </el-form-item>                                         
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="线上订单：">
                            {{form.onlineOrderNum}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="店铺编号：">
                            {{form.shopNum}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="店铺名称：">
                            {{form.shopName}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row >
                    <el-col :span="6">
                        <el-form-item  label="下单时间：">
                            {{form.orderTime}}
                        </el-form-item>                                         
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="付款日期：">
                            {{form.payTime}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="已付金额：">
                            {{form.paidAmount}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="成交总额：">
                            {{form.goodsCjAmount}}
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="收件人：">
                            {{form.buyerSjrName}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="电话：">
                            {{form.buyerSjrMobile}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="地址：">
                          <template v-if="!!form.buyerSjrAddress">
                             {{form.buyerSjrAddress}}
                          </template>
                        <template v-else>
                             {{form.buyerSjrProvince}}{{form.buyerSjrCity}}{{form.buyerSjrCounty}}{{form.buyerSjrStreet}}{{form.buyerSjrDoorplate}}
                          </template>
                        </el-form-item>
                    </el-col>
                </el-row>   
                <el-row >
                    <el-col :span="12">
                        <el-form-item label="买家留言：">
                            {{form.buyerLeaveMsg}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="订单备注：">
                            {{form.orderRemark}}
                        </el-form-item>
                    </el-col>
                </el-row>              
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="异常状态：">
                            {{form.exceptionStatus}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="异常类型：">
                            {{form.exceptionType}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="店铺状态：">
                            {{form.shopStatus}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-tabs>
                            <el-tab-pane label="订单明细">
                                <div :style="'height:'+ tableHeight+'px;'">
                                    <!--列表-->
                                    <ces-table ref="dtlTable" :that='that' :isIndex='false' :hasexpandRight='true' 
                                    :hasexpand='true' :tableData='dtlTableData' 
                                    :tableCols='dtlTableCols' :loading="false" :isSelectColumn="false"
                                     rowkey="id" >                                       
                                    </ces-table>
                                </div>

                            </el-tab-pane>
                        </el-tabs>
                    </el-col>
                </el-row>

                
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">  
                    <el-button @click="onClose">关闭</el-button>   
                </el-col>
            </el-row>
        </template>
    </my-container>
</template>
<script>  


    import cesTable from "@/components/Table/table.vue";
    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import {
          GetDpImpOrderByIdAsync
    } from '@/api/order/alllinkDaiPai';

    


    const dtlTableCols = [
        { istrue: true, prop: 'skuImgUrl', label: '', width: '60',type:'image'},
        { istrue: true, prop: 'childOrderNum', label: '子单编号', width: '120', sortable: true},
        { istrue: true, prop: 'goodsCode', label: '商品编码', width: '120', sortable: true},
        { istrue: true, prop: 'goodsName', label: '商品名称', minwidth: '160', sortable: true },
        { istrue: true, prop: 'count', label: '数量', width: '80', sortable: true },
        { istrue: true, prop: 'goodsPrice', label: '单价', width: '80', sortable: true },
        { istrue: true, prop: 'goodsAmount', label: '金额', width: '80', sortable: true },
        { istrue: true, prop: 'refundStatus', label: '退款状态', width: '100', sortable: true },
       
    ];


    export default {
        name: "DpImpOrderForm",
        components: { MyContainer, MyConfirmButton, cesTable   },
        data() {
            return {              
                that: this,
                form: {
                   
                },
                dtlTableCols: dtlTableCols,
                total: 0,
                dtlTableData: [],
                //summaryarry: {},
                pageLoading: false,
                curRow: null,
                formEditMode: true,//是否编辑模式              
                mode:1,              
            };
        },
        async mounted() {
        },
        computed: {
            tableHeight() {
                let rowsCount = 1;
                if (this.dtlTableData && this.dtlTableData.length > 0) {
                    rowsCount = this.dtlTableData.length;                    
                }
                let rowsHeight = (rowsCount + 1) * 60 + 20;
                return rowsHeight > 360 ? 360 : rowsHeight;
            },       
        },
        methods: {  
            onClose(){
                this.$emit('close');
            },  
            async onSave(isClose){
                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData({oid, mode}) {                         
                    
                this.pageLoading = true;
                this.mode=mode;
                this.formEditMode = mode!=3;

                if (oid) {
                  
                     let rlt = await GetDpImpOrderByIdAsync( { oid:oid } );
                    if (rlt && rlt.success) {
                        this.form = rlt.data;
                        this.dtlTableData = rlt.data.dpImpOrderDtlList==null?[]:rlt.data.dpImpOrderDtlList;                      
                         this.pageLoading = false;
                    }
                } else {
                    Object.keys(this.form).forEach(key => (this.form[key] = null));
                    // this.form.enabled=true;
                    // this.form.yhGoodsCode='';                   
                    this.dtlTableData =[];

                    this.pageLoading = false;                    
                }
            },           
        },
    };
</script>
