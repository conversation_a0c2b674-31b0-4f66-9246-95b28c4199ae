<template>
    <container v-loading="pageLoading">
        <!--列表-->
        <cesTable :tableData='list' :tableCols='tableCols' :isSelectColumn="false"
            :loading='listLoading' :border='true' :that="that" ref="vxetable" @sortchange='sortchange' />
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </container>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { pageCollectDataDetail } from "@/api/inventory/goodscodestock"

const tableCols = [
    { istrue: true, prop: 'goodsCode', label: '编码', width: 'auto', },
    { istrue: true, prop: 'qty', label: '数量', width: 'auto', },
    ];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];

export default {
    name: 'YunHanAdminProcodedetail',
    components: { container, MyConfirmButton, vxetablebase, cesTable },

    data() {
        return {
            that: this,
            list: [],
            filter: {},
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "goodsCode", IsAsc: false },
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
        };
    },

    async mounted() {
        
    },

    methods: {
        async clearData() {
            this.list = [];
            this.total = 0;
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        async loadData(para) {
            this.filter = para;
            await this.getlist();
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            console.log('params',params)
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await pageCollectDataDetail(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
        },
        async cellclick(row, column, cell, event) {

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>

<style lang="scss" scoped>

</style>