<template>
  <div>
    <div class="outer">
      <div class="xptcblbj2">
        <div style="width:100%;">
          <commissionAppraisalForm ref="refcommissionAppraisalForm" :module="'onetable'" :ishowarr="oneclomun"
            :tableData="posttableData" @templatepageclose="getlist">

          </commissionAppraisalForm>
        </div>

        <div style="width:100%;justify-content: right;">
          <commissionAppraisalForm ref="refcommissionAppraisalForm" :module="'twotable'" :ishowarr="twoclomun"
            :tableData="sectiontableData" @templatepageclose="getlist">

          </commissionAppraisalForm>
        </div>
      </div>
      <div style="width:100%;border-bottom: 1px #dcdfe6 solid;margin:9px 0 15px 0"></div>
      <div class="xptcblbj2">
        <div style="width:100%;">
          <commissionAppraisalForm ref="refcommissionAppraisalForm" :module="'threetable'" :ishowarr="threeclomun"
            :tableData="completionpostList" @templatepageclose="getlist">

          </commissionAppraisalForm>
        </div>

        <div style="width:100%;justify-content: right;">
          <commissionAppraisalForm ref="refcommissionAppraisalForm" :module="'fourtable'" :ishowarr="fourclomun"
            :tableData="completiondeptList" @templatepageclose="getlist">

          </commissionAppraisalForm>
        </div>
      </div>
      <div style="width:100%;border-bottom: 1px #dcdfe6 solid;margin:9px 0 15px 0"></div>
      <div class="xptcblbj2">
        <div style="width:100%;">
          <commissionAppraisalForm ref="refcommissionAppraisalForm" :module="'fivetable'" :ishowarr="fiveclomun"
            :tableData="fitdegreepostList" @templatepageclose="getlist">

          </commissionAppraisalForm>
        </div>

        <div style="width:100%;justify-content: right;">
          <commissionAppraisalForm ref="refcommissionAppraisalForm" :module="'sixtable'" :ishowarr="sixclomun"
            :tableData="fitdegreedeptList" @templatepageclose="getlist">

          </commissionAppraisalForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VXETable from 'vxe-table'
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import { v4 as uuidv4 } from 'uuid';
import { add } from 'xe-utils';
import {
  getTimeLimitDrawingList, addOrUpdateTimeLimitDrawingList, delTimeLimitDrawingList,
  getShootingInTimeRateList,
  getShootingAualityAdaptabilityDeductList,
} from '@/api/media/shootingset'
import commissionAppraisalForm from "./commissionAppraisalForm.vue";

export default {
  name: 'commissionassessment',
  components: { vxetablebase, commissionAppraisalForm },
  data() {
    return {
      oneclomun: [1, 'styleName', 'urgencyJiDay', 'urgencyDay', 'normalDay'],
      twoclomun: [2, 'styleName', 'urgencyJiDay', 'urgencyDay', 'normalDay'],
      threeclomun: [3, 'equalOrGreaterScore', 'lessScore', 'commissionReduced', 'performanceScore'],
      fourclomun: [4, 'equalOrGreaterScore', 'lessScore', 'commissionReduced', 'styletype', 'performanceScore'],
      fiveclomun: [5, 'equalOrGreaterScore', 'lessScore', 'commissionReduced', 'performanceScore'],
      sixclomun: [6, 'equalOrGreaterScore', 'lessScore', 'commissionReduced', 'performanceScore'],
      listLoading: false,
      posttableData: [],
      sectiontableData: [],
      completionpostList: [],
      completiondeptList: [],
      fitdegreepostList: [],
      fitdegreedeptList: [],
    };
  },

  async mounted() {
    // await this.onSearch()
  },

  methods: {
    async onSearch() {
      await this.getlist();
    },
    async getlist() {
      this.listLoading = true
      const { data } = await getTimeLimitDrawingList()
      this.posttableData = data.postList
      this.sectiontableData = data.deptList
      const res = await getShootingInTimeRateList()
      this.completionpostList = res.data.postList
      this.completiondeptList = res.data.deptList
      const res1 = await getShootingAualityAdaptabilityDeductList()
      this.fitdegreepostList = res1.data.postList
      this.fitdegreedeptList = res1.data.deptList
      this.listLoading = false
    },
  },
};
</script>

<style lang="scss" scoped>
.custom-input {
  position: relative;
  z-index: 9999;
  /* 设置一个较高的 z-index 值 */
}

.input-wrapper {
  position: relative;
  z-index: 10;
}

::v-deep .el-drawer__header {
  margin-bottom: 25px !important;
  padding-bottom: 25px !important;
  border-bottom: 1px #dcdfe6 solid !important;
}

.xptcblbj {
  width: 100%;
  box-sizing: border-box;
  padding: 0 35px;

}

.xptcblbj2 {
  width: 100%;
  box-sizing: border-box;
  padding: 10px 50px;
  display: flex;
  height: 31%;
  // overflow: auto;
}

.outer {
  height: 860px;
  overflow: auto;
}

.xptcbl {
  width: 100%;
  margin-bottom: 30px;
  // height: 260px;
  // background-color: aqua;
}

.xptcblmc {
  width: 12%;
  color: #333;
  background-color: beige;
  display: flex;
  align-items: center;
}

.xptcblts {
  background-color: bisque;
  display: flex;
  align-items: center;
}


.jxkhcj {
  width: 100%;
  height: 300px;
  box-sizing: border-box;
  display: flex;
}

//字体样式
.lxwz {
  width: 130px;
  font-size: 14px; //字体大小
  color: #666; //颜色
  line-height: 30px; //行高
}
</style>
