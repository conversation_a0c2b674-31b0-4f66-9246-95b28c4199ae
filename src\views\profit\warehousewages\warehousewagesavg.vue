<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-row>
                <div style="padding-bottom: 5px;font-size:14px; color:#EE7700">
                    数据来源于【聚水潭数据源-导入】并添加【分摊人员】，导入聚水潭数据源后自动计算【分摊薪资】及【薪资统计(分摊薪资部分数据)】，您也可以手动重新计算。
                </div>
            </el-row>
            <el-row>
                <el-form class="ad-form-query" :inline="true">
                    <el-form-item label="仓库">
                        <el-select v-model="filter.warehouseCode" style="width: 160px" size="mini" @change="onSearch">
                            <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="日期">
                        <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            :picker-options="pickerOptions">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="">
                        <el-select v-model="filter.workType" style="width: 80px" size="mini" @change="onSearch" clearable
                            placeholder="班次">
                            <el-option label="白班" value="白班"></el-option>
                            <el-option label="晚班" value="晚班"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="">
                        <el-input v-model.trim="filter.postCode" placeholder="一级岗位编码" style="width:120px;" clearable
                            maxlength="20" />
                    </el-form-item>
                    <el-form-item label="">
                        <el-select v-model="filter.postName" style="width: 110px" size="mini" @change="onSearch" clearable
                            filterable placeholder="一级岗位名称">
                            <el-option v-for="item in myOnePostNameList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="">
                        <el-input v-model.trim="filter.workItem" placeholder="二级岗位名称" style="width:120px;" clearable
                            maxlength="20" />
                    </el-form-item>
                    <el-form-item label="">
                        <el-input v-model.trim="filter.userName" placeholder="姓名" style="width:110px;" clearable
                            maxlength="20" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSearch">查询</el-button>
                        <el-button type="primary" @click="onComputeSumPack">计算分摊薪资</el-button>
                    </el-form-item>
                </el-form>
            </el-row>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :isSelectColumn="false" :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>


        <el-dialog title="计算分摊薪资" :visible.sync="dialogVisibleComputeSumPack" width="20%" v-dialogDrag
            :close-on-click-modal="false">
            <span>
                <el-row style="margin-bottom: 5px;font-size:14px; color:#EE7700">
                    不选仓库则计算全部仓库，日期必选。
                </el-row>
                <el-row style="margin-bottom: 5px;">
                    <el-col :xs="24" :sm="18" :md="24" :lg="24" :xl="24">
                        <el-select v-model="computeAvgWarehouseCode" style="width: 280px" size="mini" clearable
                            placeholder="请选择仓库">
                            <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-date-picker style="width: 280px" v-model="computeSumPackWorkDate" type="daterange"
                            format="yyyy-MM-dd" :picker-options="pickOptions" value-format="yyyy-MM-dd" range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期">
                        </el-date-picker>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <my-confirm-button type="submit" :loading="copmuteSumPackLoading" @click="onCopmuteSumPackSave" /> &nbsp;
                <el-button @click="dialogVisibleComputeSumPack = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MySearch from "@/components/my-search";
import buschar from '@/components/Bus/buschar';
import MySearchWindow from "@/components/my-search-window";
import { getWarehouseWagesAvgPageList, computeWarehouseWagesAvg } from '@/api/profit/warehousewages';
const tableCols = [
    { istrue: true, prop: 'workDate', label: '日期', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.workDate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'warehouseName', label: '仓库', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'workType', label: '班次', width: '70', sortable: 'custom' },
    { istrue: true, prop: 'postCode', label: '一级岗位编码', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'postName', label: '一级岗位名称', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'workItem', label: '二级岗位名称', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'userName', label: '姓名', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'wages', label: '工价', width: '60', sortable: 'custom' },
    { istrue: true, prop: 'workCount', label: '计件数量', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'workDayWages', label: '工资', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'computeTime', label: '计算时间', width: '150', sortable: 'custom' },
]
const tableHandles1 = [
    //{ label: "计算人效", handle: (that) => that.onCompute() },
    // { label: "导出", handle: (that) => that.onExport() },
];
export default {
    name: 'sumpackwages',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, buschar },
    props: ['myWarehouseList', 'myOnePostNameList'],
    data() {
        return {
            that: this,
            filter: {
                timerange: [
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                ],
                startDate: null,
                endDate: null,
                warehouseCode: 10361546,//诚信仓
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "workDate", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            pickerOptions: {
                shortcuts: [
                    {
                        text: '昨天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime());
                            picker.$emit('pick', [start, start]);
                        }
                    }, {
                        text: '近三天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime());
                            const end = new Date(new Date(tdate.toLocaleDateString()));
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一周',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                            const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一个月',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                            console.log("获取前一个月的时间", tdate.getDay());
                            const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
            },
            tableHandles1: tableHandles1,

            dialogVisibleComputeSumPack: false,
            copmuteSumPackLoading: false,
            computeAvgWarehouseCode: null,
            computeSumPackWorkDate: [],
            bComputeSumPackWorkDate: null,
            eComputeSumPackWorkDate: null,
        };
    },
    async mounted() {
        await this.onSearch()
    },
    methods: {
        //查询第一页
        async onSearch() {
            console.log(this.filter)
            if (!this.filter.timerange) {
                this.$message({ message: "请选择日期", type: "warning", });
                return;
            }
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getWarehouseWagesAvgPageList(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
        },
        //排序查 询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            console.log(rows)
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onComputeSumPack() {
            this.dialogVisibleComputeSumPack = true;
            this.computeAvgWarehouseCode = this.filter.warehouseCode;
        },
        async onCopmuteSumPackSave() {
            if (!this.computeSumPackWorkDate || this.computeSumPackWorkDate.length <= 0) {
                this.$message({ type: 'error', message: '请选择要计算的日期!' });
                return;
            }
            else {
                this.bComputeSumPackWorkDate = formatTime(this.computeSumPackWorkDate[0], "YYYY-MM-DD");
                this.eComputeSumPackWorkDate = formatTime(this.computeSumPackWorkDate[1], "YYYY-MM-DD");
                this.copmuteSumPackLoading = true;
                let res = await computeWarehouseWagesAvg({
                    bWorkDate: this.bComputeSumPackWorkDate,
                    eWorkDate: this.eComputeSumPackWorkDate,
                    warehouseCode: this.computeAvgWarehouseCode
                });
                this.copmuteSumPackLoading = false;
                if (res?.success) {
                    this.$message({ type: 'success', message: '计算中..请稍后刷新查看!' });
                    this.dialogVisibleComputeSumPack = false;
                }
            }
        },
    },
};
</script>

<style lang="scss" scoped></style>
