<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker class="publicCss" v-model="ListInfo.yearMonthDay" type="date" placeholder="选择日期"
          :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
        </el-date-picker>
        <el-select v-model="ListInfo.region" placeholder="区域" class="publicCss" clearable filterable>
          <el-option v-for="item in regionList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.wmsIds" placeholder="仓库" class="publicCss" clearable filterable multiple
          collapse-tags>
          <el-option v-for="item in warehouseList" :key="item.name" :label="item.name" :value="item.wms_co_id" />
        </el-select>
        <div class="publicCss" style="width: 190px;">
          <inputYunhan ref="refgoodsCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode" width="190px"
            placeholder="商品编码/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100" :maxlength="2000"
            @callback="callbackMethod($event, 'goodsCode')" title="商品编码">
          </inputYunhan>
        </div>
        <div class="stock-query-container">
          <el-select v-model="ListInfo.queryType" clearable filterable placeholder="查询类型" class="query-type-select"
            @change="taxTypeChange">
            <el-option value="大于" label="大于" />
            <el-option value="等于" label="等于" />
            <el-option value="小于" label="小于" />
            <el-option value="介于" label="介于" />
          </el-select>
          <el-input-number :controls="false" :min="0" :max="999999" :precision="0"
            :placeholder="ListInfo.queryType == '介于' ? '可售库存-最小' : '可售库存'" class="stock-input"
            v-model="ListInfo.sellStockMin" />
          <el-input-number :controls="false" :min="0" :max="999999" :precision="0" v-show="ListInfo.queryType == '介于'"
            placeholder="可售库存-最大" class="stock-input" v-model="ListInfo.sellStockMax" />
        </div>
        <el-select v-model="ListInfo.isCreated" placeholder="是否已生成待拍" class="publicCss" clearable filterable
          style="width: 125px;">
          <el-option key="是" label="是" :value="1" />
          <el-option key="否" label="否" :value="0" />
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
        <el-button type="primary" @click="onConfiguration"
          v-if="checkPermission('ProxyBiddingSelectionConfigure')">配置</el-button>
        <el-button type="primary" @click="handleBatchGenerated">生成批量待拍</el-button>
      </div>
    </template>
    <vxetablebase :id="'proxyBiddingSelection200507111652'" :tablekey="'proxyBiddingSelection200507111652'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'" @select="selectchange"
      @checkbox-range-end="selectchange" :border="true">
      <template slot="right">
        <vxe-column title="操作" width="70" fixed="right">
          <template #default="{ row, rowIndex }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="handleGenerated(row)">生成待拍</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="配置" :visible.sync="configurationInfo.visible" width="40%" v-dialogDrag style="margin-top: -10vh;">
      <proxyBiddingConfiguration v-if="configurationInfo.visible" :warehouseList="warehouseList"
        :customList="customList" :purchasingList="purchasingList" @close="configurationInfo.visible = false" />
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { pageGetTbWarehouseAsync } from '@/api/inventory/prepack.js'
import { getConfigByParentTitle } from '@/api/admin/publicConfig';
import { pageBianMaBrand } from '@/api/inventory/warehouse'
import proxyBiddingConfiguration from './proxyBiddingConfiguration.vue'
import { getRegionSampleRegistrationPage, getBeOnTheJobBrand, addRegionSampleRegistrationForRegionToBeFilmed, exportRegionSampleRegistration } from '@/api/inventory/sampleGoods';
import inputYunhan from "@/components/Comm/inputYunhan";
const tableCols = [
  { istrue: true, width: '60', type: "checkbox" },
  { sortable: 'custom', width: '130', align: 'center', prop: 'yearMonthDay', label: '日期' },
  { sortable: 'custom', width: '130', align: 'center', prop: 'region', label: '区域' },
  { sortable: 'custom', width: '200', align: 'center', prop: 'wmsName', label: '仓库' },
  { sortable: 'custom', width: '150', align: 'center', prop: 'goodsCode', label: '商品编码' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isCreated', label: '是否已生成待拍', formatter: (row) => row.isCreated == 1 ? '是' : row.isCreated == 0 ? '否' : '' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'sellStock', label: '可售库存' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'brandName', label: '品牌' },
  { sortable: 'custom', width: '200', align: 'center', prop: 'labels', label: '标签' },
]
export default {
  name: "proxyBiddingSelection",
  components: {
    MyContainer, vxetablebase, proxyBiddingConfiguration, inputYunhan
  },
  data() {
    return {
      checkboxList: [],
      configurationInfo: {
        visible: false,
        data: [],
      },
      warehouseList: [],//仓库
      customList: [],//标签
      purchasingList: [],
      regionList: ['义乌', '南昌', '西安'],//区域
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        yearMonthDay: dayjs().format('YYYY-MM-DD'),
        isCreated: null,//是否已生成待拍
        queryType: null,//查询类型
        sellStockMin: undefined,//可售库存最小值
        sellStockMax: undefined,//可售库存最大值
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
    await this.init()
  },
  methods: {
    handleBatchGenerated() {
      if (this.checkboxList.length == 0) return this.$message.error('请选择要生成待拍的数据')
      this.$confirm('是否生成待拍?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await addRegionSampleRegistrationForRegionToBeFilmed({ ids: this.checkboxList.map(item => item.id) })
        if (success) {
          this.$message.success('生成成功')
          this.getList()
        } else {
          this.$message.error('生成失败')
        }
      }).catch(() => {
      });
    },
    selectchange(val) {
      this.checkboxList = val;
    },
    handleGenerated(row) {
      this.$confirm('是否生成待拍?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await addRegionSampleRegistrationForRegionToBeFilmed({ ids: [row.id] })
        if (success) {
          this.$message.success('生成成功')
          this.getList()
        }
      }).catch(() => {
      });
    },
    taxTypeChange(e) {
      this.ListInfo.sellStockMax = undefined
      this.ListInfo.sellStockMin = undefined
    },
    callbackMethod(val, type) {
      const map = {
        goodsCode: () => (this.ListInfo.goodsCode = val),
      };
      map[type]?.();
    },
    onConfiguration() {
      this.configurationInfo.visible = true
    },
    async init() {
      const a = { currentPage: 1, pageSize: 9999 }
      const { success, data } = await pageGetTbWarehouseAsync({
        isUse: 1,//启用
        isWc: 0,//非外仓
        ...a,
      })
      if (success && data?.list) {
        this.warehouseList = data?.list.filter(item =>
          this.regionList.includes(item.region)
        )
      }
      const { data: data1, success: success1 } = await getConfigByParentTitle({ title: '自定义标签' });
      if (success1) {
        this.customList = data1;
      }
      const { data: data2, success: success2 } = await getBeOnTheJobBrand()
      if (success2) {
        this.purchasingList = data2.map(item => { return { value: item.id, label: item.brandName }; })
      }
    },
    async exportProps() {
      const { data } = await exportRegionSampleRegistration(this.ListInfo)
      if (data.success) {
        this.$message.success(data.msg || '导出成功')
      } else {
        this.$message.error(data.msg || '导出失败')
      }
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.ListInfo.sellStockMin && this.ListInfo.sellStockMax) {
        if (this.ListInfo.sellStockMin > this.ListInfo.sellStockMax) {
          this.$message.error('最小值不能大于最大值')
          return
        }
      }
      this.loading = true
      const { data, success } = await getRegionSampleRegistrationPage(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.yearMonthDay = dayjs(item.yearMonthDay).format('YYYY-MM-DD')
        })
        this.total = data.total
        this.summaryarry = data.summary
        this.checkboxList = []
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 180px;
    margin-right: 5px;
  }

  // 库存查询组件整体样式
  .stock-query-container {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 2px;
    margin-right: 5px;
    max-height: 23px;

    .query-type-select {
      width: 90px;
      margin-right: 0;

      :deep(.el-input__inner) {
        border: none;
        background: transparent;
        border-radius: 0;
        border-right: 1px solid #e4e7ed;
      }

      :deep(.el-input__suffix) {
        right: 8px;
      }
    }

    .stock-input {
      width: 80px;
      margin-right: 0;

      :deep(.el-input__inner) {
        border: none;
        background: transparent;
        border-radius: 0;

        &:not(:last-child) {
          border-right: 1px solid #e4e7ed;
        }
      }
    }

    // 当只有一个输入框时，去掉右边框
    .stock-input:last-child {
      :deep(.el-input__inner) {
        border-right: none;
      }
    }

    // 鼠标悬停效果
    &:hover {
      border-color: #c0c4cc;
    }

    // 聚焦效果
    &:focus-within {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
}

::v-deep .el-select__tags-text {
  max-width: 50px;
}
</style>
