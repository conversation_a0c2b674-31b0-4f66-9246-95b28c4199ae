<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 230px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                <inputYunhan :inputt.sync="ListInfo.styleCode" v-model="ListInfo.styleCode" placeholder="款式编码/若输入多条请按回车"
                    :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000" width="180px"
                    @callback="styleCodeCallback" title="款式编码" style="margin: 0 5px 5px 0">
                </inputYunhan>
                <inputYunhan :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode" placeholder="商品编码/若输入多条请按回车"
                    :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000" width="180px"
                    @callback="goodsCodeCallback" title="商品编码" style="margin: 0 5px 5px 0">
                </inputYunhan>
                <inputYunhan :inputt.sync="ListInfo.claimBrandName" v-model="ListInfo.claimBrandName"
                    placeholder="认领人/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
                    @callback="claimBrandNameCallback" title="认领人" style="margin: 0 5px 5px 0" width="180px">
                </inputYunhan>
                <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.providerName" placeholder="供应商名称" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable multiple>
                    <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.brandId" collapse-tags clearable filterable placeholder="请选择采购员"
                    class="publicCss">
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.isSeasonal" clearable placeholder="是否季节款" class="publicCss">
                    <el-option label="季节款" :value="1" />
                    <el-option label="非季节款" :value="0" />
                </el-select>
                <el-select v-model="ListInfo.compareType" collapse-tags multiple clearable placeholder="聚水潭成本价对比"
                    class="publicCss">
                    <el-option label="分批含运" value="分批含运" />
                    <el-option label="分批不含运" value="分批不含运" />
                    <el-option label="按月备货含运" value="按月备货含运" />
                    <el-option label="按月备货不含运" value="按月备货不含运" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="batchOperate(true)"
                        v-if="checkPermission('api:inventory:volumeGoods:BatchResetVolumeGoods')">批量重置</el-button>
                    <el-button type="primary" @click="batchClaim">批量认领</el-button>
                    <el-dropdown @command="exportCommand" style="margin: 0 10px;" trigger="click">
                        <el-button type="primary">
                            导出<i class="el-icon-arrow-down el-icon--right"></i>
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="商品编码" :disabled="isExport">商品编码</el-dropdown-item>
                            <el-dropdown-item command="款式编码" :disabled="isStyleExport">款式编码</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                    <el-dropdown v-if="checkPermission('volumePriceImport')" @command="importProps" trigger="click">
                        <el-button type="primary">
                            导入<i class="el-icon-arrow-down el-icon--right"></i>
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="1">商品编码</el-dropdown-item>
                            <el-dropdown-item command="2">款式编码</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
                <div class="updateTime">最后更新时间:{{ calTime }}</div>
            </div>
        </template>
        <vxetablebase :id="'marketVolume_index202408041556'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :showsummary="true"
            :summaryarry="summary" @select="checkboxRangeEnd" @sortchange='sortchange' :tableData='tableData'
            keyField="oId" :toolbarshow="false" :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'"
            :treeProp="{ rowField: 'oId', parentField: 'parentId', transform: true, reserve: true }">
            <template slot="right">
                <vxe-column title="操作" width="150" fixed="right">
                    <template #default="{ row, $index }">
                        <div style="display: flex;text-align: center;">
                            <el-button type="text" @click="edit(row.goodsCode)"
                                v-if="row.parentId && row.statusName != '待认领'">编辑</el-button>
                            <el-button type="text" @click="synchronous(row.goodsCode, row.styleCode)"
                                v-if="row.parentId && row.statusName != '待认领'">同步</el-button>
                            <el-button type="text" @click="viewLog(row.goodsCode)" v-if="row.parentId">查看日志</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog :title="isReset ? '批量重置' : '批量认领'" :visible.sync="claimVisible" width="15%" v-dialogDrag>
            <div style="display: flex;justify-content: center;align-items: center;">
                <el-select v-model="claimBrandId" clearable filterable placeholder="请选择认领人" style="width: 240px">
                    <el-option label="重置" :value="0" v-if="isReset" />
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </div>
            <div class="btnGroup">
                <el-button @click="claimVisible = false">取消</el-button>
                <el-button type="primary" @click="batchSubmit(isReset)">确定</el-button>
            </div>
        </el-dialog>


        <el-drawer title="编辑" :visible.sync="drawer" direction="rtl" size="40%" :wrapperClosable="false">
            <el-scrollbar style="height: 100%;">
                <goodsCodeDetails @close="close" v-if="drawer" @getList="getList" :goodsCode="goodsCode" />
            </el-scrollbar>
        </el-drawer>

        <el-dialog title="日志" :visible.sync="logVisible" width="50%" v-dialogDrag>
            <logDetails @close="close" v-if="logVisible" @getList="getList" :goodsCode="goodsCode" />
        </el-dialog>

        <el-dialog title="同步" :visible.sync="synchronousVisible" width="30%" v-dialogDrag>
            <synchronousPage @close="close" v-if="synchronousVisible" @getList="getList" :goodsCode="goodsCode"
                :styleCode="styleCode" />
        </el-dialog>

        <el-dialog :title="title" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, formatNoLink } from '@/utils/tools'
import inputYunhan from "@/components/Comm/inputYunhan";
import { getAllProBrand } from '@/api/inventory/warehouse'
import dayjs from 'dayjs'
import goodsCodeDetails from './components/goodsCodeDetails'
import logDetails from './components/logDetails'
import synchronousPage from './components/synchronous'
import {
    getVolumeGoodsListAsync,
    batchClaimVolumeGoods,
    batchResetVolumeGoods,
    exportVolumeGoodsListAsync,
    exportVolumeGoodsListByStyleCodeAsync,
} from "@/api/inventory/volumeGoods";
import { importVolumeGoodsVolumeAsync } from "@/api/inventory/VolumeGoodsImport";
const statusList = [
    { label: '待认领', value: 1 },
    { label: '待更新', value: 2 },
    { label: '完成', value: 3 }
]
const tableCols = [
    { label: '', type: 'checkbox' },
    { width: '100', align: 'center', prop: 'status', label: '状态', treeNode: true, formatter: (row) => row.status ? statusList.find(item => item.value == row.status).label : '' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'styleCode', label: '款式编码', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'goodsCode', label: '商品编码', formatter: (row) => row.isSeasonal == 1 ? row.goodsCode + '季' : row.goodsCode },
    { sortable: 'custom', width: '100', align: 'center', prop: 'goodsName', label: '商品名称', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'brandName', label: '采购', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'cost', label: '聚水潭成本价', formatter: (row) => !row.parentId ? null : row.cost },
    { sortable: 'custom', width: '100', align: 'center', prop: 'lastPrice', label: '上次采购单价', formatter: (row) => !row.parentId ? null : row.lastPrice },
    { sortable: 'custom', width: '100', align: 'center', prop: 'salesDay30', label: '月销售量', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'volume', label: '市场体量', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'compareType', label: '聚水潭成本价对比', },
    { width: '130', align: 'left', prop: 'pddPics', label: '拼多多销量截图', type: 'images' },
    { sortable: 'custom', width: '130', align: 'center', prop: 'pddUrl', label: '拼多多销量链接', type: 'click', handle: (that, row) => that.openLink(row.pddUrl) },
    { width: '130', align: 'left', prop: 'tianMaoPics', label: '天猫销量截图', type: 'images' },
    { sortable: 'custom', width: '130', align: 'center', prop: 'tianMaoUrl', label: '天猫销量链接', type: 'click', handle: (that, row) => that.openLink(row.tianMaoUrl) },
    { sortable: 'custom', width: '100', align: 'center', prop: 'provider', label: '供应商名称', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'batchInTranCost', label: '分批含运', type: 'clickLink', style: (that, row) => that.renderCost(row.batchInTranCost, row.cost) },
    { sortable: 'custom', width: '130', align: 'center', prop: 'batchNotTranCost', label: '分批不含运', type: 'clickLink', style: (that, row) => that.renderCost(row.batchNotTranCost, row.cost) },
    { sortable: 'custom', width: '130', align: 'center', prop: 'monthInTranCost', label: '按月备货含运', type: 'clickLink', style: (that, row) => that.renderCost(row.monthInTranCost, row.cost) },
    { sortable: 'custom', width: '130', align: 'center', prop: 'monthNotTranCost', label: '按月备货不含运', type: 'clickLink', style: (that, row) => that.renderCost(row.monthNotTranCost, row.cost) },
    { width: '100', align: 'left', prop: 'voucher', label: '报价凭证', type: 'images' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'claimBrandName', label: '认领人', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'modifiedTime', label: '最新更新时间', formatter: (row) => row.modifiedTime ? dayjs(row.modifiedTime).format('YYYY-MM-DD') : '' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, inputYunhan, goodsCodeDetails, logDetails, synchronousPage
    },
    data() {
        return {
            statusList,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
                styleCode: null,//款式编码
                goodsCode: null,//商品编码
                goodsName: null,//商品名称
                providerName: null,//供应商名称
                claimBrandName: null,//认领人
                status: [],//状态
                brandId: null,//采购员
                isSeasonal: null,//是否季节款
            },
            calTime: null,
            timeRanges: [],
            brandlist: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,//是否导出
            isStyleExport: false,//是否导出
            chooseList: [],//选中的数据
            claimVisible: false,//认领弹窗
            brandlist: [],
            claimBrandId: null,
            drawer: false,
            isReset: false,//是否重置
            logVisible: false,
            synchronousVisible: false,
            summary: {},
            goodsCode: '',
            styleCode: '',
            importVisible: false,
            fileList: [],
            importLoading: false,//导入loading
            type: null,
            title: '',
        }
    },
    async mounted() {
        this.init()
        await this.getList()
    },
    methods: {
        async exportCommand(e) {
            if (e == '商品编码') {
                await this.exportProps()
            } else {
                await this.exportStyleCode()
            }
        },
        async exportStyleCode() {
            this.isStyleExport = true
            await exportVolumeGoodsListByStyleCodeAsync(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '产品市场体量价格(系列编码维度)' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isStyleExport = false
                }
            }).catch(() => {
                this.isStyleExport = false
            })
        },
        renderCost(val, cost) {
            return cost > val ? 'color: #006400;font-weight:700' : ''
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            form.append("type", this.type);
            this.importLoading = true
            await importVolumeGoodsVolumeAsync(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps(e) {
            this.title = e == 1 ? '商品编码导入' : '款式编码导入'
            this.type = e
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        async init() {
            var { data } = await getAllProBrand();
            this.brandlist = data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        openLink(url) {
            window.open(url, '_blank')
        },
        synchronous(goodsCode, styleCode) {
            this.goodsCode = goodsCode
            this.styleCode = styleCode
            this.synchronousVisible = true
        },
        viewLog(goodsCode) {
            this.goodsCode = goodsCode
            this.logVisible = true
        },
        async batchOperate(isReset) {
            this.isReset = isReset
            if (this.chooseList.length == 0) return this.$message.error('暂无选中数据')
            const { data } = await getAllProBrand();
            this.brandlist = data.map(item => {
                return { value: item.key, label: item.value };
            });
            this.claimBrandId = null
            this.claimVisible = true
        },
        async batchSubmit() {
            await this.batchReset()
            await this.getList()
        },
        close() {
            // this.getList()
            this.drawer = false
            this.logVisible = false
            this.synchronousVisible = false
        },
        edit(goodsCode) {
            this.goodsCode = goodsCode
            this.drawer = true
        },
        async batchReset() {
            const styleCode = this.chooseList.map(item => item.styleCode).join(',')
            const { success } = await batchResetVolumeGoods({ styleCode, claimBrandId: this.claimBrandId })
            if (success) {
                await this.getList()
                this.$message({
                    type: 'success',
                    message: '批量重置成功!'
                });
                this.claimVisible = false
            }
        },
        async batchClaim() {
            if (this.chooseList.length == 0) return this.$message.error('暂无选中数据')
            const styleCode = this.chooseList.map(item => item.styleCode).join(',')
            this.$confirm('此操作将进行批量领取, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await batchClaimVolumeGoods({ styleCode })
                if (success) {
                    await this.getList()
                    this.$message({
                        type: 'success',
                        message: '批量认领成功!'
                    });
                    this.claimVisible = false
                }
            }).catch((error) => {
                this.$message({
                    type: 'info',
                    message: '已取消领取'
                });
            });
        },
        styleCodeCallback(val) {
            this.ListInfo.styleCode = val
        },
        goodsCodeCallback(val) {
            this.ListInfo.goodsCode = val
        },
        claimBrandNameCallback(val) {
            this.ListInfo.claimBrandName = val
        },
        checkboxRangeEnd(row) {
            this.chooseList = row
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportVolumeGoodsListAsync(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '产品市场体量价格(商品编码维度)' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async changeTime(e) {
            this.ListInfo.startTime = e ? e[0] : null
            this.ListInfo.endTime = e ? e[1] : null
            await this.getList()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            const styleCodeNum = this.ListInfo.styleCode.split(',').length
            const goodsCodeNum = this.ListInfo.goodsCode.split(',').length
            const claimBrandNameNum = this.ListInfo.claimBrandName.split(',').length
            if (styleCodeNum > 100 || goodsCodeNum > 100 || claimBrandNameNum > 100) {
                this.$message.error('输入的款式编码/商品编码/认领人不能超过100条')
                return
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getVolumeGoodsListAsync(this.ListInfo)
                if (success) {
                    data.list.forEach(item => {
                        item.pddPics = item.pddPics ? JSON.stringify(item.pddPics.split(',').map(item => {
                            return {
                                url: item,
                                name: item
                            }
                        })) : []
                        item.tianMaoPics = item.tianMaoPics ? JSON.stringify(item.tianMaoPics.split(',').map(item => {
                            return {
                                url: item,
                                name: item
                            }
                        })) : []
                        item.voucher = item.voucher ? JSON.stringify(item.voucher.split(',').map(item => {
                            return {
                                url: item,
                                name: item
                            }
                        })) : []
                    })
                    this.tableData = data.list
                    this.total = data.total
                    this.summary = data.summary
                    this.calTime = data.extData.calTime
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
                this.loading = false
            } catch (error) {
                this.loading = false
            } 
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 5px;
    }
}

.btnGroup {
    margin-top: 10px;
    display: flex;
    justify-content: center;
}

.updateTime {
    display: flex;
    align-items: center;
    margin-left: 10px;
    font-size: 14px;
}
</style>
