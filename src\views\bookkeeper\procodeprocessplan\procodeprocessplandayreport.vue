<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-button-group>
        <el-button style="padding: 0;border: none;margin: 0;">
          <el-select v-model="styleCode" multiple collapse-tags filterable remote reserve-keyword placeholder="系列编码"
            clearable :remote-method="remoteMethod" style="width: 130px" :loading="searchloading">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-button>

        <el-button style="padding: 0;border: none;margin: 0;">
          <inputYunhan ref="proCode" v-model="filter.proCode" :inputt.sync="filter.proCode" placeholder="商品ID"
            :maxRows="3000" :maxlength="90000" :clearable="true" width="130px" @callback="callbackProCode" title="商品ID">
          </inputYunhan>
        </el-button>

        <el-button style="padding: 0;border: none;margin: 0;">
          <el-input v-model="filter.productName" placeholder="商品名称" style="width:130px;" />
        </el-button>

        <el-button style="padding: 0;border: none;margin: 0;">
          <el-select filterable v-model="filter.platforms" placeholder="请选择平台" multiple collapse-tags
            @change="onchangeplatform" clearable style="width: 180px">
            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-button>

        <el-button style="padding: 0;border: none;margin: 0;">
          <el-select filterable v-model="filter.shopCodeList" placeholder="店铺" clearable style="width: 180px" multiple
            collapse-tags>
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
              :value="item.shopCode"></el-option>
          </el-select>
        </el-button>

        <el-button style="padding: 0;border: none;margin: 0;">
          <el-select filterable v-model="filter.groupIds" collapse-tags clearable placeholder="运营组" style="width: 190px"
            multiple>
            <el-option key="无运营组" label="无运营组" :value="0"></el-option>
            <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-button>

        <el-button style="padding: 0;border: none;margin: 0;">
          <el-select filterable v-model="filter.operateSpecialUserIds" collapse-tags clearable placeholder="运营专员"
            multiple style="width: 190px">
            <el-option key="无运营专员" label="无运营专员" :value="0"></el-option>
            <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-button>

        <el-button style="padding: 0;border: none;margin: 0;">
          <el-select filterable v-model="filter.userIds" collapse-tags clearable placeholder="运营助理" style="width: 190px"
            multiple>
            <el-option key="无运营助理" label="无运营助理" :value="0"></el-option>
            <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-button>

        <el-button style="padding: 0;border: none;margin: 0;">
          <el-input-number style="width: 100px;" v-model="filter.minOnTimeNum" :min="-9999" :max="9999" :precision="0"
            :controls="false" placeholder=">上架天数最小值"></el-input-number>
        </el-button>

        <el-button style="padding: 0;border: none;margin: 0;">
          <el-input-number style="width: 100px;" v-model="filter.maxOnTimeNum" :min="-9999" :max="9999" :precision="0"
            :controls="false" placeholder="<=上架天数最大值"></el-input-number>
        </el-button>

        <el-button style="padding: 0;border: none;margin: 0;">
          <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" :clearable="false"
            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期"></el-date-picker>
        </el-button>

        <el-button style="padding: 0;border: none; margin: 0;">
          <el-input-number style="width: 110px;" v-model="filter.profit1RateLess" :min="-99999999" :max="99999999"
            :precision="2" :controls="false" placeholder="毛一利率 < x%">
          </el-input-number>
        </el-button>

        <el-button style="padding: 0;border: none; margin: 0;">
          <el-input-number style="width: 110px;" v-model="filter.profit2RateLess" :min="-99999999" :max="99999999"
            :precision="2" :controls="false" placeholder="毛二利率 < x%">
          </el-input-number>
        </el-button>

        <el-button style="padding: 0;border: none; margin: 0;">
          <el-input-number style="width: 110px;" v-model="filter.profit3RateLess" :min="-99999999" :max="99999999"
            :precision="2" :controls="false" placeholder="毛三利率 < x%">
          </el-input-number>
        </el-button>

        <el-button style="padding: 0;border: none; margin: 0;">
          <el-input-number style="width: 110px;" v-model="filter.profit33RateLess" :min="-99999999" :max="99999999"
            :precision="2" :controls="false" placeholder="毛四利率 < x%">
          </el-input-number>
        </el-button>


        <el-button style="padding: 0;border: none; margin: 0;">
          <el-input-number style="width: 140px;" v-model="filter.profit1RateLess_NoBack" :min="-99999999"
            :max="99999999" :precision="2" :controls="false" placeholder="毛一利率(减退款) < x%">
          </el-input-number>
        </el-button>

        <el-button style="padding: 0;border: none; margin: 0;">
          <el-input-number style="width: 140px;" v-model="filter.profit2RateLess_NoBack" :min="-99999999"
            :max="99999999" :precision="2" :controls="false" placeholder="毛二利率(减退款) < x%">
          </el-input-number>
        </el-button>

        <el-button style="padding: 0;border: none; margin: 0;">
          <el-input-number style="width: 140px;" v-model="filter.profit3RateLess_NoBack" :min="-99999999"
            :max="99999999" :precision="2" :controls="false" placeholder="毛三利率(减退款) < x%">
          </el-input-number>
        </el-button>

        <el-button style="padding: 0;border: none; margin: 0;">
          <el-input-number style="width: 140px;" v-model="filter.profit33RateLess_NoBack" :min="-99999999"
            :max="99999999" :precision="2" :controls="false" placeholder="毛四利率(减退款) < x%">
          </el-input-number>
        </el-button>

        <el-button style="padding: 0;border: none; margin: 0;">
          <el-input-number style="width: 110px;" v-model="filter.profit4RateLess" :min="-99999999" :max="99999999"
            :precision="2" :controls="false" placeholder="净利率 < x%">
          </el-input-number>
        </el-button>

        <el-button style="padding: 0;border: none; margin: 0;">
          <el-input-number style="width: 110px;" v-model="filter.orderCountMore" :min="-99999999" :max="99999999" :precision="0" :controls="false" placeholder="订单量大于" >
          </el-input-number>
          <el-input-number style="width: 110px;" v-model="filter.orderCountLess" :min="-99999999" :max="99999999" :precision="0" :controls="false" placeholder="订单量小于" >
          </el-input-number>
        </el-button>

        <el-button style="padding: 0;border: none; margin: 0;">
          <el-input-number style="width: 110px;" v-model="filter.profit6More" :min="-99999999" :max="99999999" :precision="2" :controls="false" placeholder="毛六利润大于" >
          </el-input-number>
          <el-input-number style="width: 110px;" v-model="filter.profit6Less" :min="-99999999" :max="99999999" :precision="2" :controls="false" placeholder="毛六利润小于" >
          </el-input-number>
        </el-button>

        <el-button style="padding: 0;border: none; margin: 0;">
          <el-input-number style="width: 110px;" v-model="filter.profit6RateMore" :precision="2" :min="-99999999" :max="99999999" :controls="false" placeholder="毛六率 > x%" >
          </el-input-number>
          <el-input-number style="width: 110px;" v-model="filter.profit6RateLess" :precision="2" :min="-99999999" :max="99999999" :controls="false" placeholder="毛六率 < x%" >
          </el-input-number>
        </el-button>
        <el-tooltip content="勾选后最大数据为500条" placement="top" style="padding: 0;border: none; margin: 0;margin-left: 10px;">
          <el-checkbox v-model="checked" label="勾选所有搜索内容" border></el-checkbox>
        </el-tooltip>
      </el-button-group>

      <el-button-group>
        <el-button style="padding: 0;border: none; margin: 0;">
          【前
          <el-input-number style="width: 60px;" v-model="filter.beforeTotalDays" :min="1" :max="100" :precision="0"
            :controls="false">
          </el-input-number>
          天合计
          <el-select filterable v-model="filter.beforeTotalDaysItem" clearable placeholder="合计指标" style="width: 130px">
            <el-option label="毛一利率" value="profit1RateLess" />
            <el-option label="毛二利率" value="profit2RateLess" />
            <el-option label="毛三利率" value="profit3RateLess" />
            <el-option label="毛四利率" value="profit33RateLess" />
            <el-option label="毛六利率" value="profit6RateLess" />
            <el-option label="毛一利率(减退款)" value="profit1RateLess_NoBack" />
            <el-option label="毛二利率(减退款)" value="profit2RateLess_NoBack" />
            <el-option label="毛三利率(减退款)" value="profit3RateLess_NoBack" />
            <el-option label="毛四利率(减退款)" value="profit33RateLess_NoBack" />
            <el-option label="净利率" value="profit4RateLess" />
          </el-select>
          低于
          <el-input-number style="width: 60px;" v-model="filter.beforeTotalDaysLess" :min="-99999999" :max="99999999"
            :precision="2" :controls="false">
          </el-input-number>
          %】
        </el-button>

        <el-button style="padding: 0;border: none; margin: 0;">
          【前
          <el-input-number style="width: 60px;" v-model="filter.beforeConnDays" :min="1" :max="100" :precision="0"
            :controls="false">
          </el-input-number>
          天连续
          <el-select filterable v-model="filter.beforeConnDaysItem" clearable placeholder="连续指标" style="width: 130px">
            <el-option label="毛一利率" value="profit1RateLess" />
            <el-option label="毛二利率" value="profit2RateLess" />
            <el-option label="毛三利率" value="profit3RateLess" />
            <el-option label="毛四利率" value="profit33RateLess" />
            <el-option label="毛六利率" value="profit6RateLess" />
            <el-option label="毛一利率(减退款)" value="profit1RateLess_NoBack" />
            <el-option label="毛二利率(减退款)" value="profit2RateLess_NoBack" />
            <el-option label="毛三利率(减退款)" value="profit3RateLess_NoBack" />
            <el-option label="毛四利率(减退款)" value="profit33RateLess_NoBack" />
            <el-option label="净利率" value="profit4RateLess" />
          </el-select>
          低于
          <el-input-number style="width: 60px;" v-model="filter.beforeConnDaysLess" :min="-99999999" :max="99999999"
            :precision="2" :controls="false">
          </el-input-number>
          %】
        </el-button>


        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onExport" :loading="isExport">导出</el-button>
        <el-button type="primary" @click="onImport" :loading="isImport">导入生成方案</el-button>
        <el-button type="primary" @click="onprocodeprocessplanadd" style="margin-left: 20px;"
          :disabled="statusReadOnly">新增方案</el-button>
        <el-button type="primary" @click="bulkLoadingUn"
          v-if="checkPermission('ProductBatchListingDelisting')">批量上下架</el-button>
        <el-switch v-model="status" active-text="启用新增方案" inactive-text="禁用新增方案" @change="changeSwitch"
          v-if="checkPermission('NewPlanPermissionControl')" />
      </el-button-group>
    </template>
    <vxetablebase :id="'productReportAll202406281344_2'" :border="true" :align="'center'"
      :tablekey="'productReportAll202406281344_2'" ref="table2" :that='that' :isIndex='true' :hasexpand='false'
      @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
      :showheaderoverflow="false" :tableData='financialreportlist' :tableCols='yytableCols' :tableHandles='tableHandles'
      :loading="listLoading" @select='chooseCode' style="width:100%;height:99%;margin: 0" :xgt="9999" isvirtual>
      <template slot='extentbtn'>
      </template>
    </vxetablebase>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
          <el-button type="primary" @click="downExcel()">下载模板</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { getDirectorGroupList, getDirectorList, getList as getshopList } from '@/api/operatemanage/base/shop'
import { formatPlatform, formatTime, formatLinkProCode, platformlist, downloadLink } from "@/utils/tools";
import { getAllProBrand } from '@/api/inventory/warehouse'
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import InputMult from "@/components/Comm/InputMult";
import { Loading } from 'element-ui';
import inputYunhan from "@/components/Comm/inputYunhan";
import { ruleDirectorGroup } from '@/utils/formruletools'
import freightDetail from '@/views/bookkeeper/reportday/freightDetail'
import ordergiftdetail from '@/views/order/gift/ordergiftDetail'
import { getListByStyleCode } from "@/api/inventory/basicgoods"
import EveryDayrefund from '@/views/bookkeeper/reportday/EveryDayrefund'
import InventoryCheckFee from '@/views/bookkeeper/reportday/InventoryCheckFee'
import { getTimeDiff } from '@/utils/getCols'
import dayjs from 'dayjs'
import {
  getProductStyleTagList
} from '@/api/operatemanage/base/product'
import { ProcodeProcessPlanGetProductDayReportPageList, ProcodeProcessPlanGetProductDayReportList, 
  UpdateProcodeProcessPlanByAnNiu, GetProcodeProcessPlanByAnNiu, 
  exportProcodeProcessPlanGetProductDayReportPageList, ImportCreateProcodeProcessPlan } from '@/api/bookkeeper/procodeprocessplan'
import middlevue from "@/store/middle.js"

let loading;
const startLoading = () => {
  loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};
const yytableCols = [
  { istrue: true, fixed: 'left', width: '40', type: "checkbox" },
  { istrue: true, fixed: 'left', prop: 'yearMonthDay', label: '年月日', sortable: 'custom', width: '60', type: 'custom' },
  { istrue: true, prop: 'platform', fix: true, exportField: 'platformstr', label: '平台', width: '45', sortable: 'custom', formatter: (row) => formatPlatform(row.platform), type: 'custom' },
  { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: '80', formatter: (row) => row.styleCode || ' ', type: 'click', handle: (that, row) => that.showProcodesimilarity(row) },
  { istrue: true, prop: 'shopCode', exportField: 'shopName', label: '店铺名称', sortable: 'custom', width: '80', formatter: (row) => row.shopName, type: 'custom' },
  { istrue: true, prop: 'groupId', exportField: 'groupName', label: '小组', sortable: 'custom', width: '45', formatter: (row) => row.groupName, type: 'custom' },
  { istrue: true, prop: 'operateSpecialUserId', exportField: 'operateSpecialUserName', label: '运营专员', sortable: 'custom', width: '45', formatter: (row) => row.operateSpecialUserName, type: 'custom' },
  { istrue: true, prop: 'userId', exportField: 'userName', label: '运营助理', sortable: 'custom', width: '45', permission: "cgcoltxpddprsi", formatter: (row) => row.userName, type: 'custom' },
  { istrue: true, prop: 'proCode', fix: true, label: '商品ID', width: '90', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
  { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'productCategoryId', exportField: 'productCategoryName', label: '类目', width: '80', sortable: 'custom', permission: "cgcoltxpddprsi", formatter: (row) => (row.productCategoryId?.length > 3 ? row.productCategoryName : " "), type: 'custom' },
  { istrue: true, prop: 'onTime', label: '上架时间', width: '75', permission: "cgcoltxpddprsi", formatter: (row) => formatTime(row.onTime, 'YYYY-MM-DD') },
  { istrue: true, prop: 'onTime', exportField: 'onTimeCount', label: '上架天数', sortable: 'custom', width: '75', permission: "cgcoltxpddprsi", formatter: (row, that) => getTimeDiff([row.onTime, that.filter.endTime]) },

  { istrue: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width: '60', formatter: (row) => !row.orderCount ? " " : row.orderCount },
  { istrue: true, prop: 'payAmont', label: '付款金额', sortable: 'custom', width: '80', formatter: (row) => !row.payAmont ? " " : row.payAmont.toFixed(2) },
  { istrue: true, prop: 'alladv', label: '总广告费', sortable: 'custom', width: '80', formatter: (row) => row.alladv == 0 ? " " : row.alladv?.toFixed(2) },
  { istrue: true, prop: 'advratio', label: '广告占比%', sortable: 'custom', width: '60', formatter: (row) => !row.advratio ? " " : row.advratio.toFixed(2) + "%" },

  { istrue: true, prop: 'yyProfit1', label: '毛一利润', sortable: 'custom', width: '60', type: 'custom', tipmesg: '销售金额-总商品成本-采购运费-代发成本', formatter: (row) => !row.yyProfit1 ? " " : row.yyProfit1.toFixed(2) },
  { istrue: true, prop: 'yyProfit1Rate', label: '毛一利率', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛一利润/销售金额', formatter: (row) => !row.yyProfit1Rate ? " " : row.yyProfit1Rate.toFixed(2) + '%' },
  { istrue: true, prop: 'yyProfit2', label: '毛二利润', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛一利润-平台扣点-包装费-快递费', formatter: (row) => !row.yyProfit2 ? " " : row.yyProfit2?.toFixed(2) },
  { istrue: true, prop: 'yyProfit2Rate', label: '毛二利率', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛二利润/销售金额', formatter: (row) => !row.yyProfit2Rate ? " " : row.yyProfit2Rate.toFixed(2) + '%' },
  { istrue: true, prop: 'yyProfit3', label: '毛三利润', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛二利润-预估费用', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3 ? " " : row.yyProfit3?.toFixed(2) },
  { istrue: true, prop: 'yyProfit3Rate', label: '毛三利率', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛二利润-预估费用', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3Rate ? " " : row.yyProfit3Rate?.toFixed(2) + '%' },
  { istrue: true, prop: 'yyProfit4', label: '毛四利润', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛三利润-出仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4 ? " " : row.yyProfit4?.toFixed(2) },
  { istrue: true, prop: 'yyProfit4Rate', label: '毛四利率', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛四利润/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4Rate ? " " : row.yyProfit4Rate?.toFixed(2) + '%' },
  
  { istrue: true, prop: 'profit6', label: '毛六利润', sortable: 'custom', width: '60', type: 'custom', formatter: (row) => row.profit6.toFixed(2) },
  { istrue: true, prop: 'profit6Rate', label: '毛六利率', sortable: 'custom', width: '60', type: 'custom', formatter: (row) => row.profit6Rate.toFixed(2) + '%' },

  { istrue: true, prop: 'yyProfit1After', label: '毛一(减退款)', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛一-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After?.toFixed(2) },
  { istrue: true, prop: 'yyProfit1AfterRate', label: '毛一利率(减退款)', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛一（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit1AfterRate ? " " : row.yyProfit1AfterRate?.toFixed(2) + '%' },
  { istrue: true, prop: 'yyProfit2After', label: '毛二(减退款）', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛二-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After?.toFixed(2) },
  { istrue: true, prop: 'yyProfit2AfterRate', label: '毛二利率(减退款)', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛二（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit2AfterRate ? " " : row.yyProfit2AfterRate?.toFixed(2) + '%' },
  { istrue: true, prop: 'yyProfit3After', label: '毛三(减退款）', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛三-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After?.toFixed(2) },
  { istrue: true, prop: 'yyProfit3AfterRate', label: '毛三利率(减退款)', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛三（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3AfterRate ? " " : row.yyProfit3AfterRate?.toFixed(2) + '%' },
  { istrue: true, prop: 'yyProfit4After', label: '毛四(减退款）', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛四-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After?.toFixed(2) },
  { istrue: true, prop: 'yyProfit4AfterRate', label: '毛四利率(减退款)', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛四（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4AfterRate ? " " : row.yyProfit4AfterRate?.toFixed(2) + '%' },
  { istrue: true, prop: 'profit4', label: '净利润', type: 'custom', tipmesg: '毛三利润-公摊费', sortable: 'custom', width: '50', permission: "lirunprsi,shareFeedetail", formatter: (row) => !row.profit4 ? " " : row.profit4.toFixed(2) },
  { istrue: true, prop: 'profit4Rate', label: '净利率', type: 'custom', tipmesg: '净利润/销售金额', sortable: 'custom', width: '50', permission: "lirunprsi,shareFeedetail", formatter: (row) => !row.profit4Rate ? " " : (row.profit4Rate).toFixed(2) + '%' },
  {
    istrue: true, rop: '', label: `退款`, width: '120', merge: true, prop: 'mergeField',
    cols: [
      { istrue: true, prop: 'yyRefundAmontBefore', label: '发货前退款', sortable: 'custom', width: '100', type: 'custom' },
      { istrue: true, prop: 'yyRefundAmontBeforeRate', label: '发货前退款率', sortable: 'custom', tipmesg: '发货前退款/付款金额', width: '120', type: 'custom', formatter: (row) => !row.yyRefundAmontBeforeRate ? " " : (row.yyRefundAmontBeforeRate).toFixed(2) + '%' },
      { istrue: true, prop: 'yyRefundAmontAfter', label: '发货后退款', sortable: 'custom', width: '100', type: 'custom' },
      { istrue: true, prop: 'yyRefundAmontAfterRate', label: '发货后退款率', sortable: 'custom', tipmesg: '发货后退款/付款金额', width: '120', type: 'custom', formatter: (row) => !row.yyRefundAmontAfterRate ? " " : (row.yyRefundAmontAfterRate).toFixed(2) + '%' },
      { istrue: true, prop: 'yyRefundAmont', label: '总退款金额', sortable: 'custom', width: '100', tipmesg: '当日发生的总退款金额，包括历史订单', formatter: (row) => !row.yyRefundAmont ? " " : row.yyRefundAmont.toFixed(2) },
    ]
  },


  { istrue: true, prop: 'yySaleAmount', label: '销售金额', sortable: 'custom', width: '50', tipmesg: '运营维度，已扣除当日发生的退款', formatter: (row) => !row.yySaleAmount ? " " : row.yySaleAmount.toFixed(2) },
  { istrue: true, prop: 'saleAmont', label: '销售金额', sortable: 'custom', width: '60', tipmesg: '非运营维度，已扣除当日发生的退款', formatter: (row) => !row.saleAmont ? " " : row.saleAmont },
  { istrue: true, prop: 'dkAmont', label: '平台扣点', sortable: 'custom', width: '50', type: 'custom', permission: "cgcoltxpddprsi", formatter: (row) => !row.dkAmont ? " " : row.dkAmont.toFixed(2) },
  { istrue: true, prop: 'packageFee', label: '包装材料', sortable: 'custom', width: '50', formatter: (row) => !row.packageFee ? " " : row.packageFee?.toFixed(2) },
  { istrue: true, prop: 'packageAvgFee', label: '包装均价', sortable: 'custom', width: '50', permission: "cgcoltxpddprsi", formatter: (row) => !row.packageAvgFee ? " " : row.packageAvgFee?.toFixed(2) },
  { istrue: true, prop: 'freightFeeTotal', label: '快递费', sortable: 'custom', width: '50', type: 'custom', tipmesg: '实际快递费+虚拟快递费', formatter: (row) => !row.freightFeeTotal ? " " : row.freightFeeTotal?.toFixed(2) },
  { istrue: true, prop: 'freightAvgFee', label: '快递均价', sortable: 'custom', width: '50', formatter: (row) => !row.freightAvgFee ? " " : row.freightAvgFee?.toFixed(2) },
  { istrue: true, prop: 'freightFee', label: '实际快递费', sortable: 'custom', width: '50', },
  { istrue: true, prop: 'freightFeeVirtual', label: '虚拟快递费', sortable: 'custom', width: '50', formatter: (row) => !row.freightFeeVirtual ? " " : row.freightFeeVirtual?.toFixed(2) },
  { istrue: true, prop: 'freightAvgWeight', label: '快递均重', sortable: 'custom', width: '50', formatter: (row) => !row.freightAvgWeight ? " " : row.freightAvgWeight?.toFixed(2) },
  { istrue: true, prop: 'brushAmont', label: '刷单金额', sortable: 'custom', width: '50', tipmesg: '刷单金额', formatter: (row) => !row.brushAmont ? " " : row.brushAmont.toFixed(2) },
  { istrue: true, prop: 'saleCost', label: '总商品成本', sortable: 'custom', width: '70', tipmesg: '付款金额对应的销售成本', style: (that, row) => that.renderCost(row), handle: (that, row) => that.showCost(row), formatter: (row) => !row.saleCost ? " " : row.saleCost.toFixed(2) },
  { istrue: true, prop: 'brushCost', label: '刷单成本', sortable: 'custom', width: '50', tipmesg: '当天付款金额总成本', formatter: (row) => !row.brushCost ? " " : row.brushCost.toFixed(2) },
  { istrue: true, prop: 'dahuixiong', label: '特殊单佣金', sortable: 'custom', width: '60', formatter: (row) => row.dahuixiong == 0 ? " " : row.dahuixiong?.toFixed(2) },
  { istrue: true, prop: 'replaceSendCost', label: '代发成本', sortable: 'custom', width: '50', tipmesg: '运营导入', formatter: (row) => !row.replaceSendCost ? " " : row.replaceSendCost.toFixed(2) },
  { istrue: true, prop: 'replaceSendFreightFee', label: '代发快递费', sortable: 'custom', width: '50', formatter: (row) => !row.replaceSendFreightFee ? " " : row.replaceSendFreightFee?.toFixed(2) },
  { istrue: true, prop: 'isExitProfit', label: '正出仓利润', sortable: 'custom', width: '60', formatter: (row) => !row.isExitProfit ? " " : row.isExitProfit.toFixed(2), },
  { istrue: true, prop: 'negativeExitProfit', label: '负出仓利润', sortable: 'custom', width: '60', formatter: (row) => !row.negativeExitProfit ? " " : row.negativeExitProfit.toFixed(2), },
  { istrue: true, prop: 'exitCost', label: '出仓成本', sortable: 'custom', width: '50', formatter: (row) => !row.exitCost ? " " : row.exitCost.toFixed(2) },
  { istrue: true, prop: 'exitCostRate', label: '出仓成本占比', sortable: 'custom', width: '60', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate.toFixed(2) + '%' },
  { istrue: true, prop: 'exitProfit', label: '出仓利润', sortable: 'custom', width: '50', formatter: (row) => !row.exitProfit ? " " : row.exitProfit.toFixed(2) },
  { istrue: true, prop: 'profit3PredictFee', label: '预估费用', type: 'custom', width: '50', tipmesg: '毛三预估比例*销售金额', permission: "lirunprsi,profit3rsi", sortable: 'custom', formatter: (row) => !row.profit3PredictFee ? " " : row.profit3PredictFee?.toFixed(2) },
  { istrue: true, prop: 'yySaleAmountAfter', label: '净销售额', sortable: 'custom', width: '50', type: 'custom', tipmesg: '销售金额-总退款金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yySaleAmountAfter ? " " : row.yySaleAmountAfter?.toFixed(2) },
  { istrue: true, prop: 'cancelCost', label: '取消单返还成本', sortable: 'custom', width: '70', tipmesg: '返还客服已确认的取消单成本,以确认时间统计', formatter: (row) => !row.cancelCost ? " " : row.cancelCost.toFixed(2), },
  { istrue: true, prop: 'refundCostSj', label: '销退仓返还成本', sortable: 'custom', width: '70', formatter: (row) => !row.refundCostSj ? " " : row.refundCostSj.toFixed(2) },
  { istrue: true, prop: 'yyProfitDiff', label: '差额（毛一，毛二，毛三）', sortable: 'custom', width: '100', type: 'custom', tipmesg: '总退款-取消单返还成本-销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfitDiff ? " " : row.yyProfitDiff?.toFixed(2) },
  { istrue: true, prop: 'goodProfitRate', label: '商品毛利率', sortable: 'custom', width: '80', tipmesg: '（付款金额-（销售成本+赠品成本+赠品链接成本+代发成本差））/付款金额', formatter: (row) => !row.goodProfitRate ? " " : (row.goodProfitRate).toFixed(2) + '%' },
  { istrue: true, prop: 'saleAmontAvg', label: '销售金额(分摊)', sortable: 'custom', width: '80', tipmesg: '销售金额(无id分摊)', formatter: (row) => !row.saleAmontAvg ? " " : row.saleAmontAvg.toFixed(2) },
  { istrue: true, prop: 'giftAmont', label: '赠品成本', sortable: 'custom', width: '50', permission: "cgcoltxpddprsi", formatter: (row) => !row.giftAmont ? ' ' : row.giftAmont.toFixed(2), },
  { istrue: true, prop: 'saleCostAvg', label: '销售成本(分摊)', sortable: 'custom', width: '80', tipmesg: '销售成本(无id分摊)', formatter: (row) => !row.saleCostAvg ? " " : row.saleCostAvg.toFixed(2) },
  { istrue: true, prop: 'allroi', label: '总推广ROI', sortable: 'custom', width: '80', formatter: (row) => row.allroi == 0 ? " " : row.allroi?.toFixed(2) },
  { istrue: true, prop: 'advprofit', label: '推广盈亏', sortable: 'custom', width: '80', formatter: (row) => row.advprofit == 0 ? " " : row.advprofit?.toFixed(2) },
  {
    istrue: true, prop: 'profit3PredictRate', label: '毛三预估比例', type: 'custom', permission: "lirunprsi,profit3rsi", tipmesg: '(空白链接ID成本+异常成本+补发成本+代发成本+采购运费+产品费用+工资+损耗)/销售金额',
    sortable: 'custom', width: '80', formatter: (row) => !row.profit3PredictRate ? " " : (row.profit3PredictRate * 100).toFixed(2) + '%'
  },
  { istrue: true, prop: 'shareRate', label: '公摊费率', type: 'custom', tipmesg: '明细2/销售金额', sortable: 'custom', width: '80', permission: "lirunprsi,shareFeedetail", formatter: (row) => !row.shareRate ? " " : (row.shareRate * 100).toFixed(2) + '%' },
  { istrue: true, prop: 'shareFee', label: '公摊费', type: 'custom', tipmesg: '公摊费(%)*销售金额', sortable: 'custom', width: '80', permission: "lirunprsi,shareFeedetail", formatter: (row) => !row.shareFee ? " " : row.shareFee.toFixed(2) }
];

const tableHandles = [
];

export default {
  name: "procodeprocessplandayreport",
  components: {
    MyContainer, MyConfirmButton, MySearch, MySearchWindow, InputMult, freightDetail,
    ordergiftdetail, EveryDayrefund, InventoryCheckFee, inputYunhan,
    vxetablebase
  },
  data() {
    return {
      that: this,
      filter: {
        isIgnoreSpecialProCode: null,
        isIgnoreRptProCode: 0,
        attributeTag: [],//属性
        seasonOrFestivalTag: [],//季节
        temperatureTag: [],//温度
        weatherTag: [],//天气
        exitProfitUnZero: null,
        refundType: 3,
        reportType: 1,
        platform: null,
        Platforms: null,
        shopCode: null,
        shopCodeList: [],
        proCode: null,
        styleCode: null,
        productName: null,
        brandId: null,
        groupId: null,
        startTime: null,
        userIds: [],//运营助理
        operateSpecialUserIds: [],//运营专员
        groupIds: [],//组
        endTime: null,
        timerange: null,
        // 运营助理
        userId: null,
        // 车手
        userId2: null,
        // 备用
        userId3: null,
        // 运营专员 ID
        operateSpecialUserId: null,
        profit2UnZero: null,
        profit3UnZero: null,
        profit33UnZero: null,
        profit4UnZero: null,
        groupType: null,
        bzCategory: null,
        bzCategory1: null,
        bzCategory2: null,
        noProfitDay: null,
        bfNdaysProfit3LessThen0: null
        // minProfit3:null,
        // maxProfit3:null,
        // minProfit3Rate:null,
        // maxProfit3Rate:null
      },
      filterPlanItems: [],
      status: true,
      statusReadOnly: false,
      downloadLink,
      onimportfilter: {
        yearmonthday: null,
      },
      filterList: {
        bussinessCategoryNames: [],
        categoryName1s: [],
        categoryName2s: []
      },
      styleCode: null,
      options: [],
      platformlist: platformlist,
      shopList: [],
      userList: [],
      brandlist: [],
      grouplist: [],
      directorlist: [],
      financialreportlist: [],
      yytableCols: yytableCols,
      tableHandles: tableHandles,
      total: 0,
      pager: { OrderBy: " saleAmont ", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      earchloading: false,
      pageLoading: false,
      summaryarry: {},
      selids: [],
      fileList: [],
      dialogVisibleData: false,
      dialogVisible: false,
      uploadLoading: false,
      importFilte: {},
      fileparm: {},
      editparmVisible: false,
      editLoading: false,
      editparmLoading: false,
      drawervisible: false,
      /* dialogDrVisibleShengYi:false, */
      dialogDrVisible: false,
      expressfreightanalysisVisible: false,
      drparamProCode: '',
      autoformparm: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
        rule: []
      },
      freightDetail: {
        visible: false,
        filter: {
          proCode: null,
          timeRange: []
        }
      },
      EveryDayrefund: {
        visible: false,
        filter: {
          proCode: null,
          timeRange: [],
          afterSaleType: "2",
          orderStatus: "已取消",
          goodsStatus: "",
          timeRange1: []
        }
      },
      InventoryCheckFee: {
        visible: false,
        filter: {
          proCode: null,
        }
      },
      giftDetail: { visible: false },
      costDialog: { visible: false, rows: [] },
      buscharDialog: { visible: false, title: "", data: {}, loading: false },
      drawervisible: false,
      searchloading: false,
      styleTaglist: [],
      unmountrows: [],
      isExport: false,
      isImport: false,
      checked: false,
      proCodeList: [],
    };
  },
  async mounted() {
    let me = this;
    this.getStatus();
    middlevue.$on('BookKeeper_ProcodeProcessPlan_GetProductDayReportPageList', async (data) => {
      let res = data;
      this.getStatus();
    })
  },
  async created() {
    await this.init()
    await this.getShopList();
    await this.initformparm();
    if (this.$route.query && this.$route.query.dayCount) {
      this.filter.noProfitDay = parseInt(this.$route.query.dayCount);
      this.filter.platform = this.$route.query.platform == null ? null : parseInt(this.$route.query.platform);
      this.filter.shopCodeList = this.$route.query.shopCodeList ? this.$route.query.shopCodeList : this.$route.query.shopCode ? [this.$route.query.shopCode] : [];
      this.filter.groupId = this.$route.query.groupId;
      this.filter.operateSpecialUserId = this.$route.query.operateSpecialUserId;
      let dateStr = this.$route.query.yearMonthDay.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
      this.filter.timerange = [dateStr, dateStr];
      this.filter.refundType = 1;
      this.onSearch();
    }
  },
  methods: {
    downExcel(){
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250704/1941007850499710977.xlsx', '产品处理方案导入模板.xlsx');
    },
    async getStatus() {
      const { data, success } = await GetProcodeProcessPlanByAnNiu({ typeData: 1});
      if (success) {
        this.statusReadOnly = !data.status;
        this.status = data.status;
      }
    },
    changeSwitch(val) {
      this.$confirm(`确定要${ val ? '启用' : '禁用'}新增方案吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let res = await UpdateProcodeProcessPlanByAnNiu({ status: val, typeData: 1 });
        this.statusReadOnly = !val;
      }).catch(() => {
        this.status = !val;
      });
    },
    bulkLoadingUn() {
      //保留(拼多多/天猫/淘宝/抖音/京东/快手)的数据
      let oldLength = this.unmountrows.length;
      this.unmountrows = this.unmountrows.filter(item => {
        return (item.platform === 1 || item.platform === 2 || item.platform === 6 || item.platform === 7 || item.platform === 9 || item.platform === 14);
      });
      if (this.unmountrows.length < oldLength && this.unmountrows.length == 0) {
        this.$message.error('未选择(拼多多/天猫/淘宝/抖音/京东/快手)的数据!');
        return;
      }
      else if (this.unmountrows.length < oldLength) {
        this.$message({ type: 'warning', message: '已过滤掉平台非(拼多多/天猫/淘宝/抖音/京东/快手)的数据，正在加载编辑页...' });
        setTimeout(() => {
          this.bulkLoadingUnloading();
        }, 2000);
        return;
      }
      else if (this.unmountrows.length == 0) {
        this.$message.error('请选择平台为(拼多多/天猫/淘宝/抖音/京东/快手)的数据!');
        return;
      }
      //数据只有拼多多的数据
      this.bulkLoadingUnloading();
    },
    bulkLoadingUnloading() {
      this.$showDialogform({
        path: `@/views/base/batchListingDelist.vue`,
        title: '批量上下架',
        autoTitle: false,
        args: {
          checkdata: this.unmountrows
        },
        height: '650px',
        width: '80%',
      })
    },
    datetostr(date) {
      let y = date.getFullYear();
      let m = ("0" + (date.getMonth() + 1)).slice(-2);
      let d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init() {
      let date1 = new Date(); date1.setDate(date1.getDate() - 2);
      let date2 = new Date(); date2.setDate(date2.getDate() - 2);
      this.filter.timerange = [];
      this.filter.timerange[0] = this.datetostr(date1);
      this.filter.timerange[1] = this.datetostr(date2);
    },
    async initformparm() {
      let that = this;
      this.autoformparm.rule = [{ type: 'hidden', field: 'id', title: 'id', value: '', col: { span: 12 } },
      { type: 'select', field: 'groupId', title: '组长', value: '', update(val, rule) { { that.updateruleGroup(val) } }, ...await ruleDirectorGroup(), props: { filterable: true } },
      { type: 'InputNumber', field: 'Profit3PredictRate', title: '毛三预估比例%', value: null, props: { min: 0, precision: 3 }, col: { span: 6 } },
      { type: 'InputNumber', field: 'ShareRate', title: '公摊费率%', value: null, props: { min: 0, precision: 3 }, col: { span: 6 } }]
    },
    async onchangeplatform(val) {
      this.categorylist = []
      const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
      this.shopList = res1.data.list
    },
    //系列编码远程搜索
    async remoteMethod(query) {
      if (query !== '') {
        this.searchloading == true
        this.options = [];
        setTimeout(async () => {
          const res = await getListByStyleCode({ currentPage: 1, pageSize: 50, styleCode: query })
          this.searchloading = false
          res?.data?.forEach(f => {
            this.options.push({ value: f.styleCode, label: f.styleCode })
          });
        }, 200)
      }
      else {
        this.options = []
      }
    },
    async getShopList() {
      const res1 = await getAllShopList();
      this.shopList = [];
      res1.data?.forEach(f => {
        if (f.isCalcSettlement && f.shopCode && (f.platform == 1 || f.platform == 8))
          this.shopList.push(f);
      });
      let res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

      let res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });

      // var res5 = await getProductStyleTagList();
      // this.styleTaglist = res5.data.map(item => {
      //   return { value: item.styleTag, label: item.styleTag };
      // });

      // var res4 = await getAllProBrand();
      // this.brandlist = res4.data.map(item => {
      //   return { value: item.key, label: item.value };
      // });
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      if (column.prop == 'onTime') {
        this.pager.IsAsc = !this.pager.IsAsc;
      }
      await this.onSearch();
    },
    onRefresh() {
      this.onSearch()
    },
    onRefund(e) {
      this.filter.refundType = e;
      this.pager.OrderBy = ''
      this.pager.IsAsc = false
      this.financialreportlist = []
      this.summaryarry = {}
      this.total = 0
    },
    async onSearch() {
      this.listLoading = true;
      this.$refs.table2.changecolumn_setTrue(["yearMonthDay"]);
      if (this.filter.groupType == 1 || this.filter.groupType == 2) {
        this.$refs.table2.changecolumn(["yearMonthDay"]);
      }
      this.$refs.pager.setPage(1);

      this.listLoading = false;
      await this.getList().then(res => { });

      // loading.close();
    },
    async getList() {
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      if (this.filter.minOnTimeNum && this.filter.maxOnTimeNum && this.filter.minOnTimeNum > this.filter.maxOnTimeNum) {
        return this.$message.error('最小上架天数不能大于最大上架天数');
      }
      if (this.filter.minOnTimeNum) {
        this.filter.listingEndTime = dayjs(this.filter.timerange[0]).subtract(this.filter.minOnTimeNum, 'day').format('YYYY-MM-DD');
      }
      else {
        this.filter.listingEndTime = null;
      }
      if (this.filter.maxOnTimeNum) {
        this.filter.listingStartTime = dayjs(this.filter.timerange[1]).subtract(this.filter.maxOnTimeNum, 'day').format('YYYY-MM-DD');
      }
      else {
        this.filter.listingStartTime = null;
      }
      this.filter.styleCode = this.styleCode.join()
      let that = this;
      let pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };
      // this.listLoading = true;
      startLoading();

      //得到筛选的指标，用做新增方案的指标
      this.getFilterPlanItems();


      const res = await ProcodeProcessPlanGetProductDayReportPageList(params).then(res => {
        loading.close();
        if (res?.data?.list && res?.data?.list.length > 0) {
          for (let i in res.data.list) {
            if (!res.data.list[i].freightFee) {
              res.data.list[i].freightFee = " ";
            }
            if (that.filter.refundType == 2) {
              res.data.list[i].RefundAmont = res.data.list[i].RefundAmontByPay;
              res.data.list[i].Profit3 = res.data.list[i].Profit3ByPay;
              res.data.list[i].Profit3Rate = res.data.list[i].Profit3RateByPay;
            }
          }
        }
        if (that.filter.refundType == 2) {
          res.data.summary.Profit3Rate_sum = res.data?.summary?.Profit3RateByPay_sum;
          res.data.summary.RefundAmontByPay = res.data?.summary?.RefundAmontByPay_sum;
          res.data.summary.Profit3ByPay = res.data?.summary?.Profit3ByPay_sum;
        }
        that.total = res.data?.total;
        that.financialreportlist = res.data?.list;
        if (that.filter.refundType < 3) {
          that.$refs.table.loadRowEcharts();
        } else {
          that.$refs.table2.loadRowEcharts();
        }
        that.summaryarry = res.data?.summary;
      });
      this.getProCodeList()
    },

    getFilterPlanItems() {
      this.filterPlanItems = [];
      if (this.filter.profit1RateLess !== null && this.filter.profit1RateLess !== undefined) this.filterPlanItems.push("yyProfit1Rate");
      if (this.filter.profit2RateLess !== null && this.filter.profit2RateLess !== undefined) this.filterPlanItems.push("yyProfit2Rate");
      if (this.filter.profit3RateLess !== null && this.filter.profit3RateLess !== undefined) this.filterPlanItems.push("yyProfit3Rate");
      if (this.filter.profit33RateLess !== null && this.filter.profit33RateLess !== undefined) this.filterPlanItems.push("yyProfit4Rate");

      if (this.filter.profit1RateLess_NoBack !== null && this.filter.profit1RateLess_NoBack !== undefined) this.filterPlanItems.push("yyProfit1AfterRate");
      if (this.filter.profit2RateLess_NoBack !== null && this.filter.profit2RateLess_NoBack !== undefined) this.filterPlanItems.push("yyProfit2AfterRate");
      if (this.filter.profit3RateLess_NoBack !== null && this.filter.profit3RateLess_NoBack !== undefined) this.filterPlanItems.push("yyProfit3AfterRate");
      if (this.filter.profit33RateLess_NoBack !== null && this.filter.profit33RateLess_NoBack !== undefined) this.filterPlanItems.push("yyProfit4AfterRate");

      if (this.filter.profit4RateLess !== null && this.filter.profit4RateLess !== undefined) this.filterPlanItems.push("Profit4Rate");

      if (this.filter.beforeTotalDays !== null && this.filter.beforeTotalDays !== undefined && this.filter.beforeTotalDays > 0
        && this.filter.beforeTotalDaysLess && this.filter.beforeTotalDaysItem) {
        if (this.filter.beforeTotalDaysItem == "profit1RateLess" && this.filterPlanItems.find(f => f == "yyProfit1Rate") == null)
          this.filterPlanItems.push("yyProfit1Rate");
        else if (this.filter.beforeTotalDaysItem == "profit2RateLess" && this.filterPlanItems.find(f => f == "yyProfit2Rate") == null)
          this.filterPlanItems.push("yyProfit2Rate");
        else if (this.filter.beforeTotalDaysItem == "profit3RateLess" && this.filterPlanItems.find(f => f == "yyProfit3Rate") == null)
          this.filterPlanItems.push("yyProfit3Rate");
        else if (this.filter.beforeTotalDaysItem == "profit33RateLess" && this.filterPlanItems.find(f => f == "yyProfit4Rate") == null)
          this.filterPlanItems.push("yyProfit4Rate");

        else if (this.filter.beforeTotalDaysItem == "profit1RateLess_NoBack" && this.filterPlanItems.find(f => f == "yyProfit1AfterRate") == null)
          this.filterPlanItems.push("yyProfit1AfterRate");
        else if (this.filter.beforeTotalDaysItem == "profit2RateLess_NoBack" && this.filterPlanItems.find(f => f == "yyProfit2AfterRate") == null)
          this.filterPlanItems.push("yyProfit2AfterRate");
        else if (this.filter.beforeTotalDaysItem == "profit3RateLess_NoBack" && this.filterPlanItems.find(f => f == "yyProfit3AfterRate") == null)
          this.filterPlanItems.push("yyProfit3AfterRate");
        else if (this.filter.beforeTotalDaysItem == "profit33RateLess_NoBack" && this.filterPlanItems.find(f => f == "yyProfit4AfterRate") == null)
          this.filterPlanItems.push("yyProfit4AfterRate");

        else if (this.filter.beforeTotalDaysItem == "profit4RateLess" && this.filterPlanItems.find(f => f == "Profit4Rate") == null)
          this.filterPlanItems.push("Profit4Rate");
      }

      if (this.filter.beforeConnDays !== null && this.filter.beforeConnDays !== undefined && this.filter.beforeConnDays > 0
        && this.filter.beforeConnDaysLess && this.filter.beforeConnDaysItem) {
        if (this.filter.beforeConnDaysItem == "profit1RateLess" && this.filterPlanItems.find(f => f == "yyProfit1Rate") == null)
          this.filterPlanItems.push("yyProfit1Rate");
        else if (this.filter.beforeConnDaysItem == "profit2RateLess" && this.filterPlanItems.find(f => f == "yyProfit2Rate") == null)
          this.filterPlanItems.push("yyProfit2Rate");
        else if (this.filter.beforeConnDaysItem == "profit3RateLess" && this.filterPlanItems.find(f => f == "yyProfit3Rate") == null)
          this.filterPlanItems.push("yyProfit3Rate");
        else if (this.filter.beforeConnDaysItem == "profit33RateLess" && this.filterPlanItems.find(f => f == "yyProfit4Rate") == null)
          this.filterPlanItems.push("yyProfit4Rate");

        else if (this.filter.beforeConnDaysItem == "profit1RateLess_NoBack" && this.filterPlanItems.find(f => f == "yyProfit1AfterRate") == null)
          this.filterPlanItems.push("yyProfit1AfterRate");
        else if (this.filter.beforeConnDaysItem == "profit2RateLess_NoBack" && this.filterPlanItems.find(f => f == "yyProfit2AfterRate") == null)
          this.filterPlanItems.push("yyProfit2AfterRate");
        else if (this.filter.beforeConnDaysItem == "profit3RateLess_NoBack" && this.filterPlanItems.find(f => f == "yyProfit3AfterRate") == null)
          this.filterPlanItems.push("yyProfit3AfterRate");
        else if (this.filter.beforeConnDaysItem == "profit33RateLess_NoBack" && this.filterPlanItems.find(f => f == "yyProfit4AfterRate") == null)
          this.filterPlanItems.push("yyProfit4AfterRate");

        else if (this.filter.beforeConnDaysItem == "profit4RateLess" && this.filterPlanItems.find(f => f == "Profit4Rate") == null)
          this.filterPlanItems.push("Profit4Rate");
      }
    },

    showEveryDayrefund(row) {
      this.EveryDayrefund.filter.proCode = row.proCode;
      if (row.yearMonthDay != null) {
        let dayStr = row.yearMonthDay.substr(0, 4) + '-' + row.yearMonthDay.substr(4, 2) + '-' + row.yearMonthDay.substr(6, 2)
        this.EveryDayrefund.filter.timeRange = [dayStr, dayStr];
      }
      else {
        this.EveryDayrefund.filter.timeRange = this.filter.timerange
      }
      this.EveryDayrefund.visible = true;
      setTimeout(async () => {
        await this.$refs.EveryDayrefund.onSearch();
      }, 100);

    },
    showInventoryCheckFee(row) {
      this.InventoryCheckFee.filter.proCode = row.proCode;
      if (row.yearMonthDay != null) {
        let dayStr = row.yearMonthDay.substr(0, 4) + '-' + row.yearMonthDay.substr(4, 2) + '-' + row.yearMonthDay.substr(6, 2)
        this.InventoryCheckFee.filter.timeRange = [dayStr, dayStr];
      }
      else {
        this.InventoryCheckFee.filter.timeRange = this.filter.timerange
      }

      this.InventoryCheckFee.visible = true;
      setTimeout(async () => {
        await this.$refs.InventoryCheckFee.onSearch();
      }, 100);

    },
    showFreightDetail(row) {
      if (row.freightFee >= 1) {
        this.freightDetail.filter.proCode = row.proCode;
        if (row.yearMonthDay != null) {
          let dayStr = row.yearMonthDay.substr(0, 4) + '-' + row.yearMonthDay.substr(4, 2) + '-' + row.yearMonthDay.substr(6, 2)
          this.freightDetail.filter.timeRange = [dayStr, dayStr];
        }
        else {
          this.freightDetail.filter.timeRange = this.filter.timerange
        }
        this.freightDetail.visible = true;
        setTimeout(async () => {
          await this.$refs.freightDetail.onSearch();
        }, 100);
      }
    },
    showProcodesimilarity(row) {
      if (row.styleCode != null) {
        this.$router.push({ path: '/order/procodesimilarity', query: { styleCode: row.styleCode } })
      }
    },
    async showGiftDetail(row) {
      let yearMonthDayStart = row.yearMonthDay
      let yearMonthDayEnd = row.yearMonthDay
      if (this.filter.groupType) {
        yearMonthDayStart = this.filter.timerange[0].replace("-", "").replace("-", "")
        yearMonthDayEnd = this.filter.timerange[1].replace("-", "").replace("-", "")
      }
      this.giftDetail.visible = true;
      let _th = this;
      await this.$nextTick(async () => { await _th.$refs.ordergiftdetail.onShow(yearMonthDayStart, yearMonthDayEnd, row.proCode); });
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    onRefresh() {
      this.onSearch()
    },
    async updateruleGroup(groupid) {
      if (!groupid)
        this.autoformparm.fApi.resetFields()
      else {
        const res = await getParm({ groupId: groupid })
        let arr = Object.keys(this.autoformparm.fApi);
        res.data.groupId = groupid;
        if (!res.data?.Profit3PredictRate) res.data.Profit3PredictRate = 0;
        if (!res.data?.ShareRate) res.data.ShareRate = 0;
        await this.autoformparm.fApi.setValue(res.data)
      }
    },
    showCost(row) {
      if (row.replaceSendCost > 0) {
        this.costDialog.visible = true;
        this.costDialog.rows = [row];
      }
    },
    renderCost(row) {
      if (row.replaceSendCost > 0) {
        return "color:blue;cursor:pointer;";
      }
      else {
        return "";
      }
    },
    async callbackProCode(val) {
      this.filter.proCode = val;
    },
    //复选框数据
    chooseCode(row) {
      this.unmountrows = row
    },
    onprocodeprocessplanadd() {
      // if (this.filter.timerange[1] > dayjs().subtract(2, 'day').format('YYYY-MM-DD')) {
      //   this.$message.error('请选择小于等于前天的日期');
      //   return;
      // }
      if (this.unmountrows.length == 0 && !this.checked) {
        this.$message.error('请选择行数据');
        return;
      }
      // if (this.filterPlanItems.length <= 0) {
      //   this.$message.error('请筛选指标，将按照筛选指标新增观察方案');
      //   return;
      // }
      let yearMonthDay = this.filter.timerange[1];
      let selProCodes = [...new Set(this.unmountrows.map(item => item.proCode))]
      if(this.checked) selProCodes = this.proCodeList
      this.$showDialogform({
        path: `@/views/bookkeeper/procodeprocessplan/procodeprocessplanadd.vue`,
        title: '新增产品处理方案',
        autoTitle: false,
        args: {
          yearMonthDay: yearMonthDay,
          selProCodes: selProCodes,
          procodeProcessPlanColKeys: this.filterPlanItems,
        },
        height: '650px',
        width: '90%',
      })
    },
    async getProCodeList(){
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      if (this.filter.minOnTimeNum && this.filter.maxOnTimeNum && this.filter.minOnTimeNum > this.filter.maxOnTimeNum) {
        return this.$message.error('最小上架天数不能大于最大上架天数');
      }
      if (this.filter.minOnTimeNum) {
        this.filter.listingEndTime = dayjs(this.filter.timerange[0]).subtract(this.filter.minOnTimeNum, 'day').format('YYYY-MM-DD');
      }
      else {
        this.filter.listingEndTime = null;
      }
      if (this.filter.maxOnTimeNum) {
        this.filter.listingStartTime = dayjs(this.filter.timerange[1]).subtract(this.filter.maxOnTimeNum, 'day').format('YYYY-MM-DD');
      }
      else {
        this.filter.listingStartTime = null;
      }
      this.filter.styleCode = this.styleCode.join()
      let pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };

      //得到筛选的指标，用做新增方案的指标
      this.getFilterPlanItems();
      const res = await ProcodeProcessPlanGetProductDayReportList(params)
      this.proCodeList = res.data.list
      return res.data.list
    },
    //导出
    async onExport(){
      let pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };
      this.isExport = true;
      let res = await exportProcodeProcessPlanGetProductDayReportPageList(params);
      this.isExport = false;
      if (!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '产品处理方案-全平台数据源' + new Date().toLocaleString() + '.xlsx');
      aLink.click()
    },
    //导入弹窗
    onImport(){
      this.fileList = []
      this.dialogVisible = true;
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    //导入生成方案
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await ImportCreateProcodeProcessPlan(form);
      if (res?.success){
        this.$message({ message: res.msg || "上传成功,正在导入中...", type: "success" });
        this.uploadLoading = false
        this.dialogVisible = false;
        await this.getList()
      }else{
        this.uploadLoading = false
      }
        
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
  },

};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

::v-deep .el-select__tags-text {
  max-width: 50px;
}
</style>
