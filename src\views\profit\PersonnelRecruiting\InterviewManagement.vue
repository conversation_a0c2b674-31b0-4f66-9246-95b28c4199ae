<template>
    <my-container v-loading="pageLoading">
        <el-tabs v-model="activeName" style="height: calc(100% - 40px);">
            <el-tab-pane label="招聘岗位" name="first1">
                <recruitmentPositions v-if="activeName == 'first1'" ref="warehouseuserworkdata"></recruitmentPositions>
            </el-tab-pane>
            <el-tab-pane label="待入职" name="first2" lazy>
                <talentPool ref="talentPool"></talentPool>
            </el-tab-pane>
            <el-tab-pane label="预备人才" name="first3" lazy>
                <reserveTalents ref="reserveTalents"></reserveTalents>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
  
<script>
import MyContainer from "@/components/my-container";
import recruitmentPositions from "@/views/profit/PersonnelRecruiting/recruitmentPositions.vue";
import talentPool from "@/views/profit/PersonnelRecruiting/talentPool.vue";
import reserveTalents from "@/views/profit/PersonnelRecruiting/reserveTalents.vue";
export default {
    name: "InterviewManagement",//面试管理
    components: {
        MyContainer, recruitmentPositions, talentPool, reserveTalents
    },
    data () {
        return {
            pageLoading: false,
            activeName: "first1",
        };
    },
    created () {
    },
    mounted () {
    },
    methods: {
    },
};
</script>
  
<style lang="scss" scoped>
.el-tabs__content {
    height: calc(100% - 20px);
}
</style>
  