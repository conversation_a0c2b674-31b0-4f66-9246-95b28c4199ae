<template>
    <MyContainer >
       <template #header>
            <el-form  :inline="true" ref="topForm" :model="queryEnum" class="demo-ruleForm">
                <el-form-item prop="timeRanges">
                  <el-date-picker v-model="queryEnum.timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" style="width: 250px;margin-right: 5px;"
                    :clearable="false" :value-format="'yyyy-MM-dd'" @change="changeTime">
                  </el-date-picker>
                </el-form-item>
                
                <!-- <el-form-item prop="wave_id">
                    <el-input  v-model="queryEnum.wave_id" placeholder="批次号" clearable style="width: 150px" >
                    </el-input>
                </el-form-item> -->
                <el-form-item prop="worker" >
                  <el-select
                    v-model="queryEnum.worker"
                    clearable
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请输入人名"
                    :remote-method="remoteMethod"
                    :loading="personLoading">
                    <el-option
                      v-for="item in personList"
                      :key="item.value"
                      :label="item.label+'-'+item.title"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <!-- <el-form-item prop="work_type" >
                  <el-select
                    v-model="queryEnum.work_type"
                    clearable
                    filterable
                    remote
                    reserve-keyword
                    placeholder="选择类型">
                    <el-option
                    key="拣货"
                      :label="'拣货'"
                      :value="'拣货'">
                    </el-option>
                    <el-option
                    key="播种"
                      :label="'播种'"
                      :value="'播种'">
                    </el-option>
                  </el-select>
                </el-form-item> -->
                <el-form-item >
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
                <el-form-item >
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </el-form-item>
            </el-form>
       </template>
    
        <template>
         <vxetablebase 
         :id="'workload20241214'" 
         :tablekey="'workload20241214'" 
         :tableData='tableData' 
         :tableCols='tableCols' 
         @sortchange='sortchange'
          :loading='loading' 
          :border='true' 
          :that="that" 
          ref="vxetable"  
          :showsummary='true'
          :summaryarry="summaryarry"
          >
          <!-- <template #wave_id>
            <vxe-column field="wave_id" title="批次号" width="150" :sortable="true">
             
            </vxe-column>
          </template> -->
         </vxetablebase>
         
       </template>
       <template #footer>
          <my-pagination ref="pager" :total="total"  @get-page="getList"/>
        </template>
     </MyContainer>
    </template>
    
    <script>
    import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
    import MyContainer from "@/components/my-container";
    import dayjs from 'dayjs';
    import {QueryWorkloadList, ExportWorkloadList} from '@/api/wmsoperation/sow.js'
    // import {  getUserListPage } from '@/api/admin/user'
    
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser';
    
   
    const tableCols = [
     { istrue: true,sortable: 'custom', prop: 'czdate', label: '操作日期', formatter:(row)=> dayjs(row.czdate).format("YYYY-MM-DD")  },
     { istrue: true,sortable: 'custom', prop: 'czr', label: '操作人',  },
     { istrue: true,sortable: 'custom', prop: 'pickqty', label: '拣货数量',  },
     { istrue: true,sortable: 'custom', prop: 'pickcodenum', label: '拣货编码数',  },
     { istrue: true,sortable: 'custom', prop: 'sowqty', label: '播种数量',  },
     { istrue: true,sortable: 'custom', prop: 'sowcodenum', label: '播种编码数',  },
    ];
    
    
    
    
    export default {
     name: 'WorkLoad',
     components: { vxetablebase, MyContainer},
     data() {
       return {
            that:this,
            queryEnum:{
                timeRanges:[],
                startTime:null,
                endTime:null,
                orderBy:'',
                isAsc: true,
            },
            summaryarry:{},
            tableData:[],
            tableCols:tableCols,
            loading:false,
            total:0,
            personList:[],
            personLoading:false
       };
     },
      async mounted(){
        await this.getList()
        
      },
     methods:{
        changeTime(e) {
          this.queryEnum.startTime = e ? e[0] : null
          this.queryEnum.endTime = e ? e[1] : null
        },
        async getList(type){
          if(type == 'search'){
            this.$refs.pager.setPage(1)
          }
          let pager = this.$refs.pager.getPager()
          
          if (this.queryEnum.timeRanges != null && this.queryEnum.timeRanges.length == 0) {
                //默认给近7天时间
                this.queryEnum.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
                this.queryEnum.endTime = dayjs().format('YYYY-MM-DD')
                this.queryEnum.timeRanges = [this.queryEnum.startTime, this.queryEnum.endTime]
            }
          let params = {
            ...pager,
            ...this.queryEnum
            
          }
          // if(!params.wave_id){
          //   delete params.wave_id
          // }

          this.loading = true
          const {list,total,summary} =  await QueryWorkloadList(params)
          this.loading = false
          this.tableData = list
          this.total = total
          this.summaryarry = summary
          console.log(list,'取值')
        },
        sortchange({ order, prop }) {
          if (prop) {
            this.queryEnum.orderBy = prop
            this.queryEnum.isAsc = order.indexOf("descending") == -1 ? true : false
            this.getList()
          }
        },
        async remoteMethod(query){
          if (query !== '') {
            this.personLoading = true;
            //接入接口
            // var dynamicFilter = { field: 'nickName', operator: 'Contains', value: query }
            this.personList = []
            const res = await QueryAllDDUserTop100({ keywords: query })
            res?.data?.forEach(f => {
                this.personList.push({ value: f.ddUserId, label: f.userName, title:f.position})
            })
            this.personLoading = false;
          } else {
            this.personList = [];
          }
        },
        async onExport() {
          
          
          let pager = this.$refs.pager.getPager()
          let res = await ExportWorkloadList({...this.queryEnum,...pager});
          if (!res?.data) {
            this.$message({ message: "没有数据", type: "warning" });
            return
          }
          const aLink = document.createElement("a");
          let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', '工作量_' + new Date().toLocaleString() + '_.xlsx')
          aLink.click()
        },
      
      }
    };
    </script>
    
    <style lang="scss" scoped>
    
    </style>
    