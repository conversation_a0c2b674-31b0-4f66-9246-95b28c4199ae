<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" label-width="130px" label-position="right" :disabled="!formEditMode">
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="竞品图片：" prop="goodsCompeteImgUrl">
                            <el-image style="width: 40px; height: 40px" :src="form.goodsCompeteImgUrl" :preview-src-list="[form.goodsCompeteImgUrl]">
                            </el-image>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="竞品ID：" prop="goodsCompeteId">
                            {{form.goodsCompeteId}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="竞品标题：" prop="goodsCompeteName">
                            {{form.goodsCompeteName}}
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row >
                    <el-col :span="6">
                        <el-form-item label="产品简称：" prop="goodsCompeteShortName">
                            {{form.goodsCompeteShortName}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="款式编码：" prop="styleCode">
                            {{form.styleCode}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="计划生成时间：" prop="createdTime">
                            {{form.createdTime}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="分配状态：" prop="distributeState">
                            {{ distributeStateFmt(form.distributeState) }}
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row >
                    <!-- v-if="form.distributeState==1"  -->
                    <el-col :span="6">
                        <el-form-item   label="参考供应商：">
                            <template >
                                 {{form.referSupplierName}}
                            </template>                            
                        </el-form-item> 
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="分配时间：" prop="distributeTime">
                            {{form.distributeTime}}
                        </el-form-item>
                    </el-col>                    
                    
                    <el-col :span="6">
                        <el-form-item label="采购人员：" prop="brandName">
                            {{form.brandName}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                       
                    </el-col>
                </el-row>

                <el-row >
                    <el-col :span="4">
                        <el-form-item label="质检报告：" prop="inspectionReportImgUrl">
                            <el-image style="width: 40px; height: 40px" :src="form.inspectionReportImgUrl" :preview-src-list="[form.inspectionReportImgUrl]">
                            </el-image>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="专利资质：">
                            <YhImgUpload :value.sync="form.patentQualificationImgUrls" :disabled="true" :limit="10"></YhImgUpload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="专利资质pdf：">
                            <YhImgUpload :value.sync="form.patentQualificationPdfUrls" :disabled="true"  :isImg="false" accept=".pdf" :limit="10" ></YhImgUpload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="包装图片：">
                            <YhImgUpload :value.sync="form.packingImgUrls" :limit="10"  :disabled="true"></YhImgUpload>
                        </el-form-item>
                    </el-col>     
                    
                    <el-col :span="4">
                        <el-form-item   label="采购供应商：">
                            <template v-if="mode==3">
                                 {{form.supplierName}}
                            </template> 
                            <yh-supplierselector  v-else :value.sync="form.supplierId" :text.sync="form.supplierName"></yh-supplierselector>

                        </el-form-item>         
                    </el-col>
                    <el-col :span="4">
                        <el-form-item   label="仓库：">
                            <template v-if="mode==3">
                                 <!-- {{ formatWarehouse(form.warehouse)}} -->
                                 {{ myWarehouse222(form.warehouse)}}
                            </template> 
                            <el-select v-else v-model="form.warehouse" placeholder="请选择分仓" >
                                <!-- <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                                    :value="item.value" /> -->
                                <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                            :value="item.wms_co_id" />
                            </el-select>

                        </el-form-item>         
                    </el-col>
                </el-row>  
                <el-row>
                    <el-col :span="6" >
                        <el-form-item prop="outerPackaLanguage" label="外包装语言："> 
                            {{ outerPackaLanguageFmt(form.outerPackaLanguage) }} 
                        </el-form-item>
                    </el-col> 
                    <el-col :span="6" >
                        <el-form-item prop="outerPackaLanguage" label="产品链接:"> 
                            <el-input v-model.trim="form.supplierGoodLink" maxlength="1000"
                                style="width:100%;" :disabled="true"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item   label="供应商链接：">
                            <template v-if="mode==3">
                                <el-input v-model.trim="form.supplierLink" maxlength="1000"
                                style="width:100%;" :disabled="true"></el-input>
                            </template> 
                            <el-input v-else  v-model.trim="form.supplierLink" > 
                            </el-input>
                        </el-form-item> 
                    </el-col>
                    <el-col :span="12" :hidden="form.remark==null || form.remark==''" >
                        <el-form-item prop="remark" label="备注：">
                            {{ form.remark }}
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- <el-row>
                    <el-col :span="6">
                        <el-form-item   label="供应商链接：">
                            <template v-if="mode==3">
                                 {{form.supplierLink}}
                            </template> 
                            <el-input v-else  v-model.trim="form.supplierLink" > 
                            </el-input>
                        </el-form-item> 
                    </el-col>
                    <el-col :span="6">
                        <el-form-item   label="供应商产品链接：">
                            <template v-if="mode==3">
                                 {{form.supplierGoodLink}}
                            </template> 
                            <el-input v-else  v-model.trim="form.supplierGoodLink" > 
                            </el-input>
                        </el-form-item> 
                    </el-col>
                </el-row>  -->
                <el-row> 
                    <el-col :span="12">
                        <el-form-item prop="chatImgUrls" label="供应商聊天记录：" >
                            <div style="width: 653px;">
                                <uploadfile ref="uploadChatImg"  :islook="true" :minisize="true"
                                 :uploadInfo="form.chatImgUrls==null?[]:form.chatImgUrls" :limit="10000" :accepttyes="'.image/jpg,image/jpeg,image/png'" />
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col v-if="form.showFirstApproveApply" :span="4">
                        <el-form-item prop="firstApproveApplyUserName" label="初审核价员：" style="float: left;"> 
                             {{ form.firstApproveApplyUserName }}
                        </el-form-item> 
                    </el-col>
                    <el-col v-if="form.showFirstApproveApply" :span="8">
                        <el-form-item prop="firstApproveApplyChatUrls" label="初审核价聊天记录："> 
                            <YhImgUpload :value.sync="form.firstApproveApplyChatUrls" :disabled="true" :limit="10"></YhImgUpload>
                        </el-form-item> 
                    </el-col>
                </el-row>
                <el-row>
                    <el-col v-if="form.hasProductionLicense==1" :span="6">
                        <el-form-item prop="hasProductionLicense" label="生产许可证：" style="float: left;"> 
                             <el-image style="margin-left:10px;width: 50px; height: 50px" :src="form.productionLicenseUrl" :preview-src-list="[form.productionLicenseUrl]">
                            </el-image> 
                        </el-form-item> 
                    </el-col>
                </el-row>        
                <el-row>
                    <el-col :span="24">
                        <el-tabs>
                            <el-tab-pane label="采购明细">
                                <div :style="'height:'+ tableHeight+'px;'">
                                    <span style="display:none">
                                       {{calcTotalAmount}}
                                       
                                    </span>
                                    <!--列表-->
                                    <ces-table ref="dtlTable" :showsummary="true" :summaryarry="summaryarry" :that='that'   :isIndex='false' :hasexpandRight='true' :hasexpand='true' :tableData='dtlTableData' :tableCols='dtlTableCols' :loading="false" :isSelectColumn="false" >
                                        <template slot="right">
                                            <el-table-column width="150" label="长宽高（cm）" >
                                                <template slot-scope="scope">
                                                    {{scope.row.yhGoodsLength ==null? '0.00':scope.row.yhGoodsLength.toFixed(2)}} *
                                                    {{scope.row.yhGoodsWidth ==null? '0.00':scope.row.yhGoodsWidth.toFixed(2)}} *
                                                    {{scope.row.yhGoodsHeigdth ==null? '0.00':scope.row.yhGoodsHeigdth.toFixed(2)}}  
                                                </template>
                                            </el-table-column>
                                            <el-table-column width="60" label="重量(kg)" >
                                                <template slot-scope="scope">  
                                                        {{scope.row.yhGoodsWeight ==null? '0.00':scope.row.yhGoodsWeight.toFixed(2)}}  
                                                </template>
                                            </el-table-column> 
                                            <el-table-column label="备注">
                                                <template slot-scope="scope">
                                                    {{scope.row.remark}}
                                                </template>
                                            </el-table-column>
                                            <el-table-column width="100" label="单价">
                                                <template slot-scope="scope">
                                                    <span v-if="mode==3">{{scope.row.costPrice}}</span>
                                                    <el-input-number v-else type="number" v-model.number="scope.row.costPrice" :min="0" :max="10000" :precision="3" :controls="false"  size="mini" style="width:100%"></el-input-number>
                                                </template>
                                            </el-table-column> 
                                            <el-table-column width="100" label="进货数量">
                                                <template slot-scope="scope">
                                                    <span v-if="mode==3">{{scope.row.stockInCount}}</span>
                                                    <el-input-number v-else type="number" v-model.number="scope.row.stockInCount" :min="0" :max="100000" :precision="0" :controls="false"  size="mini" style="width:100%"></el-input-number>
                                                </template>
                                            </el-table-column>

                                            <el-table-column width="100" label="进货金额">
                                                <template slot-scope="scope">
                                                    {{ formatmoney(scope.row.stockInAmount) }}   
                                                </template>
                                            </el-table-column>
                                        </template>
                                    </ces-table>
                                </div>

                            </el-tab-pane>
                        </el-tabs>
                    </el-col>
                </el-row>

                
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;padding-top:5px;">  
                    <el-link  v-if="mode<3" :underline="false" type="primary" @click="addSuppForm.name=null;dialogAddSuppVisible=true;" style="margin-right: 20px;">添加供应商</el-link>

                    <el-button @click="onClose">关闭</el-button>                     
                    <el-button v-if="mode<3" type="primary" @click="onSave(false)">保存</el-button>
                    <el-button v-if="mode<3" type="primary" @click="onSave(true)">保存&关闭</el-button>
                    
                </el-col>
            </el-row>
        </template>
        
        <!-- 添加供应商 -->
        <el-dialog title="添加供应商" :visible.sync="dialogAddSuppVisible" append-to-body v-dialogDrag width='30%' height='500px'>
            <el-form ref="addSuppForm" :model="addSuppForm" :rules="addSuppFormRules" label-width="100px">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item prop="name" label="供应商名称">
                            <el-input v-model="addSuppForm.name" placeholder="供应商名称" :maxlength="40" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogAddSuppVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :validate="finishSuppFormValidate" :loading="onFinishSuppLoading" @click="onSaveSupplier()">
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>

      
    </my-container>
</template>
<script>  


    import uploadfile from '@/components/Comm/uploadfile.vue'; 
    import cesTable from "@/components/Table/table.vue";
    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode , formatWarehouse,sendWarehouse4HotGoodsBuildGoodsDocList  } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import {
        GetHotPurchasePlanDtlAsnyc,  SaveHotPurchasePlanDtlAsnyc
    } from '@/api/operatemanage/productalllink/alllink';

    import { pageSupplierAll, getPurNameSimilarity, addSupplier } from '@/api/inventory/supplier'
    
    import { getAllProBrand} from '@/api/inventory/warehouse'

    import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'

    import YhImgUpload from '@/components/upload/yh-img-upload.vue';

    import goodschoice from "@/views/base/goods/goods2.vue";

    import YhSupplierselector from '@/components/YhCom/yh-supplierselector.vue'
    import { getAllWarehouse } from '@/api/inventory/warehouse'

    
    const distributeStateOpts=[
        {label:'待分配',value:0},
        {label:'已分配',value:1}
    ];
    const distributeStateFmt=(val)=>{
        let opt=distributeStateOpts.find(x=>x.value==val);
        if(opt)
            return opt.label;
        
        return val;
    } 

    const auditStateOpts=[
        {label:'已拒绝',value:-1},
        {label:'未发起',value:0},
        {label:'审核中',value:1},
        {label:'已审核',value:2},
    ]
    const auditStateFmt=(val)=>{
        let opt=auditStateOpts.find(x=>x.value==val);
        if(opt)
            return opt.label;
        
        return val;
    }

    var outerPackaLanguageOpt=[
        {label:'中文',value:1},
        {label:'英文',value:2}
    ]
    var outerPackaLanguageFmt=(val)=>{
        let opt=outerPackaLanguageOpt.find(x=>x.value==val);
        if(opt)
            return opt.label; 
        return val;
    } 

    var delayedPurchaseOpt=[
        {label:'是',value:1},
        {label:'否',value:0}
    ]
    const delayedPurchaseFmt=(val)=>{
        let opt=delayedPurchaseOpt.find(x=>x.value==val);
        if(opt)
            return opt.label; 
        return val;
    } 

    //上新仓库 
    function formatForNewWarehouse(val) {
        let item = sendWarehouse4HotGoodsBuildGoodsDocList.find(x => x.value == val);
        if (item && item.label)
            return item.label;

        return val;
    }

    const dtlTableCols = [
        { istrue: true, prop: 'yhGoodsImgUrl', label: '商品图片', width: '80', type: 'images', maxlength: 30 },
        { istrue: true, prop: 'yhGoodsCode', label: '商品编码', width: '150', maxlength: 30 },
        { istrue: true, prop: 'yhGoodsName', label: '商品名称', maxlength: 40, width: '150' },
        { istrue: true, prop: 'forNewWarehouse',  label: '上新仓库', width: '100', formatter: (row) => formatForNewWarehouse(row.forNewWarehouse) },
        { istrue: true, prop: 'goodsProgressType', label: '商品类型', width: '50' },
        { istrue: true, prop: 'isMainSale', label: '是否主卖', width: '50', formatter: (row => row.isMainSale ? "是" : "否") },
        { istrue: true, prop: 'mainSaleAvgCount',  label: '主卖人均件数', width: '80', min: 0, max: 10000,  },
        { istrue: true, prop: 'estimateStockInCount', label: '预计进货数量', width: '80', min: 0, max: 900000, },
        { istrue: true, prop: 'estimateStockInAmount', precision: 2, label: '预计进货金额', width: '80' },
        { istrue: true, prop: 'isZengPin',  label: '有无赠品', width: '50', formatter: (row => row.isZengPin ? "有" : "无") },
        { istrue: true, prop: 'isJiaGong',  label: '是否加工', width: '50', formatter: (row => row.isJiaGong ? "是" : "否") }
        //{ istrue: true, prop: 'yhGoodsUnit',  label: '品名单位', width: '80' },
        // { istrue: true, prop: 'costPrice',  precision: 2, label: '成本单价', width: '100', min: 0, max: 900000, isDisabled: (row) => true },    
        // { istrue: true, prop: 'estimateStockInCount',  label: '预计进货数量', width: '120', isDisabled: (row) => true, min: 0, max: 900000 },
        // { istrue: true, prop: 'estimateStockInAmount',  precision: 2, label: '预计进货金额', width: '120', isDisabled: (row) => true, min: 0, max: 999999999 },
        // { istrue: true, prop: 'remark',  label: '备注', width: '120', maxlength: 100, isDisabled: (row) => true },
    ];


    export default {
        name: "YhGoodsRelationForm",
        components: { MyContainer, MyConfirmButton, cesTable, YhQuillEditor, YhImgUpload,goodschoice,YhSupplierselector,formatmoney,uploadfile},
        data() {
            return {              
                that: this,
                distributeStateOpts,
                auditStateOpts,
                //warehouselist,
                warehouselist: [],
                form: {
                   
                },
                dtlTableCols: dtlTableCols,
                total: 0,
                dtlTableData: [],
                summaryarry: {},
                pageLoading: false,
                curRow: null,
                formEditMode: true,//是否编辑模式              
                mode:1,  
                brandlist: [],
                //添加供应商-------------------
                addSuppForm:{
                    name:null
                },
                dialogAddSuppVisible:false,
                addSuppFormRules:{
                    name: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
                },               
                onFinishSuppLoading:false,
                //添加供应商-------------------
              
            };
        },
        async mounted() {
            var res2 = await getAllProBrand();
            
            this.brandlist = res2.data.map(item => {
                return { value: item.key, label: item.value };
            });

            var res3 = await getAllWarehouse();
            var warehouselist1 = res3.data;
            this.warehouselist = warehouselist1;
            this.warehouselistnew = warehouselist1;

        },
        computed: {
            tableHeight() {
                let rowsCount = 1;
                if (this.dtlTableData && this.dtlTableData.length > 0) {
                    rowsCount = this.dtlTableData.length;                    
                }
                let rowsHeight = (rowsCount + 2) * 50 + 40;
                return rowsHeight > 360 ? 360 : rowsHeight;
            },        
            calcTotalAmount(){
                let total=0;
                this.dtlTableData.forEach(row=>{
                    //row.costPrice*stockInCount=stockInAmount
                    row.stockInAmount= Number( ((isNaN(row.costPrice)?0:row.costPrice)*(isNaN(row.stockInCount)?0:row.stockInCount)).toFixed(2));
                    total+=row.stockInAmount;
                });                
                return total.toFixed(2);
            }    
        },
        methods: {   
            formatmoney,        
            distributeStateFmt,
            auditStateFmt, 
            outerPackaLanguageFmt,
            delayedPurchaseFmt,
            formatWarehouse,
            //新增供应商时提交验证
            finishSuppFormValidate: function () {
                let isValid = false
                this.$refs.addSuppForm.validate(valid => {
                    isValid = valid
                })
                return isValid
            },
            myWarehouse222(warehouse) {
                const name = this.warehouselist.find(f => f.wms_co_id === warehouse)?.name;
                return name;
            },
            //新增供应商
            async onSaveSupplier() {
                var _this = this;
                if (!_this.addSuppForm.name || _this.addSuppForm.name == '') {
                    _this.$message({ type: 'error', message: '请输入供应商名称！' });
                    return;
                }
                //校验名称是否有相似的
                _this.onFinishSuppLoading = true;
                await getPurNameSimilarity({ supplierName: _this.addSuppForm.name }).then(function (res) {

                    if (res?.success && res.data) {
                        _this.$confirm("存在相似的供应商名：" + res.data + ",是否继续添加？", '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                        .then(function () {
                            _this.configSave();

                        })
                        .catch(() => {
                            _this.onFinishSuppLoading = false;
                            //取消操作
                            return;
                        });
                    } else if (!res?.success && (res?.data == null || res?.data == "")) {
                        _this.onFinishSuppLoading = false;
                        //取消操作
                        return;
                    } else {
                        _this.configSave();
                    }
                });
            },
            configSave() {
                let that = this;
                that.$confirm('确定保存吗, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                    .then(() => {
                    addSupplier(that.addSuppForm)
                        .then(function (res) {
                            if (!res?.success){
                                that.$message({ type: 'warn', message: '保存失败' });
                                that.onFinishSuppLoading = false;
                                that.dialogAddSuppVisible = false;
                            }
                            else {
                                that.$message({ type: 'success', message: '保存成功' });
                                that.onFinishSuppLoading = false;
                                that.dialogAddSuppVisible = false;
                                that.$emit("addcallback", res.data);
                            }
                        })
                        .catch(function (err) { console.error(err); });
                    }).catch(() => {
                        that.$message({ type: 'info', message: '已取消' });
                        that.onFinishSuppLoading = false;
                    });
            },
            onClose(){
                this.$emit('close');
            },  
            async onSave(isClose){
                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData({oid,mode}) {               
               
                this.pageLoading = true;
                this.mode=mode;
                this.formEditMode = mode!=3;

                if (oid) {                  
                     let rlt = await GetHotPurchasePlanDtlAsnyc( { planId:oid } );
                    if (rlt && rlt.success) {
                        this.form = rlt.data.planInfo; 
                        this.$refs.uploadChatImg.setData( this.form.chatImgUrls); 
                        this.dtlTableData = rlt.data.planDtls==null?[]:rlt.data.planDtls;                      
                        this.pageLoading = false;
                    }
                } else {
                    Object.keys(this.form).forEach(key => (this.form[key] = null));
                                   
                    this.dtlTableData =[];

                    this.pageLoading = false;                    
                }
            },
            async save() {               

                this.pageLoading = true;

                let saveData = { ...this.form };
                
                let errMsg='';
                if(this.dtlTableData&& this.dtlTableData.length>0){  
                    this.dtlTableData.forEach(element => {
                        if(!(element.stockInCount && element.stockInCount>0)){
                            errMsg='请填写有效的采购数量！';                            
                        }
                    });
                    
                }

                if(errMsg){
                    this.$alert(errMsg);   
                    this.pageLoading = false; 
                    return false;
                }
              
                saveData.planDtlList = [...this.dtlTableData];
             

                let rlt = await SaveHotPurchasePlanDtlAsnyc(saveData);
                if (rlt && rlt.success) {
                    this.$message.success('保存成功！');    
                    
                    this.form = rlt.data.planInfo;
                    this.dtlTableData = rlt.data.planDtls==null?[]:rlt.data.planDtls;        
                }

                this.pageLoading = false;
              
                return (rlt && rlt.success);
            }
        },
    };
</script>
