<template>
    <div>
        <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="标题">
                        <el-input v-model="ruleForm.title" clearable placeholder="标题" maxlength="100"
                            class="item"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="开始节点" prop="beginNode">
                        <el-select v-model="ruleForm.beginNode" placeholder="开始节点" class="item" clearable>
                            <el-option v-for="item in nodeList.filter(item => item.value != 'PlanSendTime')"
                                :label="item.label" :value="item.value" :key="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="描述">
                        <el-input v-model="ruleForm.description" type="textarea" row="3" clearable placeholder="描述"
                            maxlength="200" class="item"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="结束节点" prop="endNode">
                        <el-select v-model="ruleForm.endNode" placeholder="结束节点" class="item" clearable>
                            <el-option v-for="item in nodeList" :label="item.label" :value="item.value"
                                :key="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="平台">
                        <el-select v-model="ruleForm.platforms" placeholder="平台" class="item" clearable multiple
                            collapse-tags>
                            <el-option v-for="item in platformlist" :label="item.label" :value="item.value"
                                :key="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-tooltip class="item" effect="dark" :content="getTooltip().yjToolTip" placement="top-start">
                        <el-form-item label="预警时长" prop="warningHour">
                            <el-input-number :controls="false" :min="0" :max="99" :precision="1" placeholder="预警时长"
                                class="item" v-model="ruleForm.warningHour" />
                        </el-form-item>
                    </el-tooltip>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="标签">
                        <inputYunhan ref="productCode" :inputt.sync="ruleForm.orderLabels1" width="100%"
                            v-model="ruleForm.orderLabels1" placeholder="标签/若输入多条请按回车" :clearable="true"
                            :clearabletext="true" :maxRows="500" :maxlength="1000000" @callback="proCodeCallback"
                            title="标签" style="margin:0 10px 0 0;" class="item">
                        </inputYunhan>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-tooltip class="item" effect="dark" :content="getTooltip().csToopTip" placement="top-start">
                        <el-form-item label="超时时长" prop="overHour">
                            <el-input-number :controls="false" :min="0" :max="99" :precision="1" placeholder="超时时长"
                                class="item" v-model="ruleForm.overHour" />
                        </el-form-item>
                    </el-tooltip>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="仓库多选">
                        <chooseWareHouse v-model="ruleForm.wmsIds" class="item" multiple />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="是否发送通知">
                      <el-select v-model="ruleForm.isSendNotice" placeholder="是否发送通知" class="item" :clearable="false" filterable>
                        <el-option label="是" :value="true" />
                        <el-option label="否" :value="false" />
                      </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <div style="display: flex;justify-content: center;">
                        <el-button @click="$emit('close')">取消</el-button>
                        <el-button type="primary" @click="submitForm('ruleForm')" v-throttle="1000">保存</el-button>
                    </div>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>

<script>
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import inputYunhan from "@/components/Comm/inputYunhan";
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import request from '@/utils/request'
import { Tooltip } from 'element-ui';
const api = '/api/verifyOrder/Orders/Rule/'
const nodeList = [
    { label: '支付时间', value: 'PayTime' },
    { label: '接单时间', value: 'TakeTime' },
    { label: '审核时间', value: 'VerifyTime' },
    { label: '批次生成时间', value: 'PickGenTime' },
    { label: '拣货开始时间', value: 'PickTime' },
    { label: '拣货完成时间', value: 'PickCptTime' },
    { label: '打包时间', value: 'PackTime' },
    { label: '计划发货时间', value: 'PlanSendTime' },
    { label: '发货时间', value: 'SendTime' },
    { label: '揽收时间', value: 'CollectTime' },
    { label: '审单计算时间', value: 'VerifyCptTime' },
]
export default {
    props: {
        isAdd: {
            type: Boolean,
            default: false
        },
        editForm: {
            type: Object,
            default: () => ({})
        }
    },
    components: {
        inputYunhan, chooseWareHouse
    },
    data() {
        return {
            api,
            platformlist,
            nodeList,
            ruleForm: {
                pass: '',
                checkPass: '',
                isSendNotice: false,
                age: ''
            },
            rules: {
                beginNode: [
                    { required: true, message: '请选择开始节点', trigger: 'change' }
                ],
                endNode: [
                    { required: true, message: '请选择结束节点', trigger: 'change' }
                ],
                warningHour: [
                    { required: true, message: '请输入预警时长', trigger: 'blur' },
                ],
                overHour: [
                    { required: true, message: '请输入预警时长', trigger: 'blur' },
                ],
            }
        }
    },
    created() {
        if (this.editForm.id && !this.isAdd) {
            this.getProp()
        }
    },
    mounted() { },
    methods: {
        getTooltip() {
            let obj = {
                yjToolTip: '',
                csToopTip: '',
            }
            if (this.ruleForm.endNode == 'PlanSendTime') {
                obj = {
                    yjToolTip: '计划发货时间 - 预警时长',
                    csToopTip: '计划发货时间',
                }
            } else {
                obj = {
                    yjToolTip: '开始节点 + 预警时长',
                    csToopTip: '开始节点 + 超时时长',
                }
            }
            return obj
        },
        async getProp() {
            let { data, success } = await request.post(`${this.api}GetData`, this.editForm.id)
            if (!success) return
            data.orderLabels1 = data.orderLabels.join(',')
            data.isSendNotice = !!data?.isSendNotice
            this.ruleForm = data
            console.log(data);
        },
        proCodeCallback(val) {
            this.ruleForm.orderLabels1 = val
        },
        submitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    this.ruleForm.orderLabels = this.ruleForm.orderLabels1 ? this.ruleForm.orderLabels1?.split(',') : []
                    const { success } = await request.post(`${this.api}MergeData`, this.ruleForm)
                    if (!success) return
                    this.$message({
                        type: 'success',
                        message: '添加成功!'
                    });
                    setTimeout(() => {
                        this.$emit('getList')
                        this.$emit('close')
                    }, 500)
                } else {
                    return false;
                }
            });
        },
    }
}
</script>

<style scoped lang="scss">
.item {
    width: 100%;
}
</style>
