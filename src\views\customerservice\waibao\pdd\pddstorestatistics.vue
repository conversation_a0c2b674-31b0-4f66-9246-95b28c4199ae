<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='tableData' @select='selectchange' :isSelection='false'
            :tableCols='tableCols' :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <!-- <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.groupNameList" placeholder="分组"   multiple clearable filterable :collapse-tags="true">
                            <el-option v-for="item in filterGroupList" :key="item" :label="item"
                                :value="item">
                            </el-option>
                        </el-select>
                    </el-button> -->
                    <!-- <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.shopName" placeholder="店铺" filterable clearable>
                            <el-option v-for="item in shopList" :key="item" :label="item"
                                :value="item">
                            </el-option>
                        </el-select>
                    </el-button> -->
                    <el-button style="padding: 0;margin: 0;border:none">
                        <datepicker v-model="filter.sdate"></datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport" style="margin-left: 10px;" v-if="checkPermission(['api:Customerservice:OutSource:ExportPddShopEfficiency'])" >导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total"   @get-page="getdatalist" />
        </template>

        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="false" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
    getOSGroupByList,
    getOSPddShopEfficiencyPageList,
    getOSPddShopEfficiencyChat,
    exportPddShopEfficiency
} from "@/api/customerservice/waibao";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar';
 
 const tableCols = [
    { istrue: true, prop: 'shopName', label: '店名', width: '160' },
    { istrue: true, prop: 'inquirs', label: '咨询人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'ipscount', label: '询单人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'receivecount', label: '最终成团人数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'successpayRate', label: '询单转化率', width: '100', sortable: 'custom', formatter: (row) => (row.successpayRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'threeSecondReplyRate', label: '3分钟人工回复率', width: '100', sortable: 'custom', formatter: (row) => (row.threeSecondReplyRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'responseTime', label: '均响(秒)', width: '90', sortable: 'custom' ,formatter: (row) => (row.responseTime).toFixed(2) },
    { istrue: true, prop: 'thirtySecondResponseRate', label: '30秒应答率', width: '100', sortable: 'custom',formatter: (row) => (row.thirtySecondResponseRate * 100).toFixed(2) + "%"  },
    // { istrue: true, prop: 'outTimes', label: '出勤人次', width: '80', sortable: 'custom' },
    // { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'inquirRate', label: '询单占比', width: '80', sortable: 'custom',formatter: (row) => (row.inquirRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'inquirValue', label: '询单价值（元）', width: '100', sortable: 'custom', formatter: (row) => row.inquirValue.toFixed(2) },
    { istrue: true, prop: 'salesvol', label: '客服销售额(元)', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'serviceScore', label: '客服服务分', width: '100', sortable: 'custom', formatter: (row) => row.serviceScore.toFixed(2) },
    { istrue: true, prop: 'lowRatingOrderCount', label: '评分<=3订单数', width: '110', sortable: 'custom' },
    { istrue: true, prop: 'disputeRefundCount', label: '纠纷退款数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'complainCount', label: '投诉数', width: '80', sortable: 'custom' },
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];
export default {
    name: "pddstorestatistics",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar },
    data() {
        return {
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            filter: {
                groupType: 0,
                inquirsType: 0,
            },
            groupNameList: [],
             filterGroupList: [],
            userList: [],
            groupList: [],
            tableData: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: {},
            pager: { OrderBy: "inquirs", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
        };
    },
    async mounted() {
        // await this.getGroupNameList();
    },
    methods: {
        async getGroupNameList() {
            let groups = await getOSGroupByList({ platform: 2 });
            this.filterGroupList=groups.data
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getShopInquirsStatisticsList();
        },
        getParam() {
            if (this.filter.sdate) {
                this.filter.timeStart = this.filter.sdate[0];
                this.filter.timeEnd = this.filter.sdate[1];
            }
            else {
                this.filter.timeStart = null;
                this.filter.timeEnd = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            return params;
        },
        async getShopInquirsStatisticsList() {
            let params = this.getParam();
            this.listLoading = true;
            const res = await getOSPddShopEfficiencyPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.tableData= res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async showchart(row) {//趋势图
            let params = this.getParam();
            params.shopName = row.shopName;
            const res = await getOSPddShopEfficiencyChat(params).then(res => {
                if (res) {
                    this.dialogMapVisible.visible = true;
                    this.dialogMapVisible.data = res;
                    this.dialogMapVisible.title = res.title;
                    res.title = "";
                }
            })
            this.dialogMapVisible.visible = true
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await exportPddShopEfficiency(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '拼多多店效率统计（外包）_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 55px;
}
</style>
