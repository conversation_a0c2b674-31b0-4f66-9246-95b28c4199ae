<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.yhCheckRlt" placeholder="检查结果" class="publicCss" clearable>
                    <el-option v-for="item in statusList" :label="item.label" :value="item.value" :key="item.value" />
                </el-select>
                <el-input v-model.trim="ListInfo.yh_EXT_Keywords" placeholder="关键字查询" maxlength="50" clearable
                    class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="batchThrough(1)">批量通过</el-button>
                    <el-button type="primary" @click="batchThrough(2)">批量不通过</el-button>
                    <el-button type="primary" @click="exportProps">导出</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @select="select"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false" border
            id="20250530150945" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { PageAliYanHuoGoodsInfo, InnerCheckRlt, ExportPageAliYanHuoGoodsInfo } from '@/api/inventory/AliGfyh'
const statusList = [
    { label: '待检查', value: 0 },
    { label: '通过', value: 1 },
    { label: '不通过', value: 2 },
]
const tableCols = [
    { type: 'checkbox', label: '' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'yhSku', label: 'SKU', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'infoTime', label: '信息时间', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'yhCheckRlt', label: '检查结果', formatter: (row) => row.yhCheckRlt !== null ? statusList.find(item => item.value == row.yhCheckRlt)?.label : row.yhCheckRlt },
    { sortable: 'custom', width: '100', align: 'center', prop: 'yhCheckUserName', label: '检查人', },
    { sortable: 'custom', width: '90', align: 'left', prop: 'images', label: '商品图片', type: 'images' },
    { sortable: 'custom', width: '80', align: 'left', prop: 'outerPackingPic', label: '外包装', type: 'images' },
    { sortable: 'custom', width: '70', align: 'left', prop: 'physicalPic', label: '实物图', type: 'images' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'clWeight', label: '测量重(g)', },
    { sortable: 'custom', width: '70', align: 'left', prop: 'clWeightImages', label: '测重图', type: 'images' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'clLength', label: '测量长(mm)', },
    { sortable: 'custom', width: '100', align: 'left', prop: 'clLengthPic', label: '测长图', type: 'images' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'clWidth', label: '测量宽(mm)', },
    { sortable: 'custom', width: '70', align: 'left', prop: 'clWidthPic', label: '测宽图', type: 'images' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'clHeight', label: '测量高(mm)', },
    { sortable: 'custom', width: '70', align: 'left', prop: 'clHeightPic', label: '测高图', type: 'images' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'zzzsFile', label: '资质证书', type: 'files' },
    { sortable: 'custom', width: '100', align: 'left', prop: 'hgzPic', label: '合格证', type: 'images' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'zjReport', label: '质检报告', type: 'files' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            statusList,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            selectList: [],
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async exportProps() {
          this.loading = true
          const { data } = await ExportPageAliYanHuoGoodsInfo(this.ListInfo)
          this.loading = false
          const aLink = document.createElement("a");
          let blob = new Blob([data], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', '1688官方验货导出数据' + new Date().toLocaleString() + '.xlsx')
          aLink.click()
        },
        async batchThrough(val) {
            this.$confirm(`此操作将对该数据${val == 1 ? '批量通过' : '批量不通过'}, 是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = this.selectList.map(item => {
                    return {
                        yhCheckRlt: val,
                        verId: item.verId,
                    }
                })
                const { success } = await InnerCheckRlt(res)
                if (success) {
                    this.$message({
                        type: 'success',
                        message: `批量${val == 1 ? '通过' : '不通过'}成功`
                    });
                    this.getList()
                }
                this.selectList = []
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        select(val) {
            this.selectList = val
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await PageAliYanHuoGoodsInfo(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
