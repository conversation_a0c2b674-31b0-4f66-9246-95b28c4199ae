<template>
    <div class="imageModel" @keydown="handleKeyDown" tabindex="0" ref="imageModel">
        <i class="el-icon-close" @click="closeModal"></i>
        <i class="el-icon-arrow-left" @click="changeLocation('left')"></i>
        <i class="el-icon-arrow-right" @click="changeLocation('right')"></i>
        <div class="imageModel_bottom">
            <!-- 缩小 -->
            <i class="el-icon-zoom-out" @click="magnify('small')"></i>
            <!-- 放大 -->
            <i class="el-icon-zoom-in" @click="magnify('big')"></i>
            <!-- 还原 -->
            <i class="el-icon-full-screen" @click="clearCss"></i>
            <!-- 左转 -->
            <i class="el-icon-refresh-left" @click="revolve('left')"></i>
            <!-- 右转 -->
            <i class="el-icon-refresh-right" @click="revolve('right')"></i>
        </div>
        <div class="imageModel_info">
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].sendGoodsDate"
                placement="top-start">
                <div v-show="checkList.includes(1)">发货日期:{{ imgList[index].sendGoodsDate }}</div>
            </el-tooltip>
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].sendWareHouse"
                placement="top-start">
                <div v-show="checkList.includes(2)">发货仓:{{ imgList[index].sendWareHouse }}</div>
            </el-tooltip>
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].platform" placement="top-start">
                <div v-show="checkList.includes(3)">平台:{{ imgList[index].platformText }}</div>
            </el-tooltip>
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].styleCode"
                placement="top-start">
                <div v-show="checkList.includes(4)">系列编码:{{ imgList[index].styleCode }}</div>
            </el-tooltip>
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].goodsCode"
                placement="top-start">
                <div v-show="checkList.includes(5)">商品编码:{{ imgList[index].goodsCode }}</div>
            </el-tooltip>
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].zrDepartment"
                placement="top-start">
                <div v-show="checkList.includes(6)">损耗部门:{{ imgList[index].zrDepartment }}</div>
            </el-tooltip>
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].zrType2" placement="top-start">
                <div v-show="checkList.includes(7)">损耗类型:{{ imgList[index].zrType2 }}</div>
            </el-tooltip>
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].damagedHandType"
                placement="top-start">
                <div v-show="checkList.includes(8)">处理方式:{{ imgList[index].damagedHandType }}</div>
            </el-tooltip>
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].memberName"
                placement="top-start">
                <div v-show="checkList.includes(9)">责任人:{{ imgList[index].memberName }}</div>
            </el-tooltip>
        </div>
        <el-image :src="childimgList[index].damagedZrFileUrls" class="viewImageBox"
            :style="{ transform: `rotate(${deg}deg) scale(${scale})`, transition: transition }"></el-image>
        <!-- <div class="imageModel" ></div> -->
    </div>
</template>

<script>
import { formatTime, formatPlatform, pickerOptions } from "@/utils/tools";
import dayjs from "dayjs";
import _ from "lodash";
export default {
    name: 'imageModel',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        imgList: {
            type: Array,
            default: () => []
        },
        maxIndex: {
            type: Number,
            default: 8
        },
        closeModal: {
            type: Function,
            default: () => { }
        },
        imageInfo: {
            type: Object,
            default: () => { }
        },
        // index: {
        //     type: Number,
        //     default: 0//当前元素第几个数据
        // },
        checkList: {
            type: Array,
            default: () => []
        },
        domIndex: {
            type: Number,
            default: 0//当前元素索引
        }
    },
    data() {
        return {
            index: 0,
            deg: 0,//旋转角度
            scale: 1,//缩放
            transition: '0.3s',//过渡时间
            formatPlatform,//平台格式化
            imageIndex: 0,//当前元素当前数据第几张图片
            chidomindex: 0,//当前元素索引
            childimgList: [],//当前元素数据
        }
    },
    //监听imgList的变化
    watch: {
        imgList: {
            handler: function (newVal, oldVal) {
                this.childimgList = newVal
                this.index = 0
                this.childimgList.forEach(item => {
                    item.platformText = this.formatPlatform(item.platform)
                })
            },
            deep: true
        }
    },
    mounted() {
        this.index = 0
        this.imageIndex = 0
        this.childimgList = this.imgList
        console.log(this.childimgList, 'this.childimgList');
        this.imgList.forEach(item => {
            item.platformText = this.formatPlatform(item.platform)
        })
        this.$nextTick(() => {
            this.$refs.imageModel.focus()
        })
        this.chidomindex = this.domIndex
    },
    methods: {
        changeLocation(type) {
            if (type == 'right') {
                if (this.chidomindex == this.maxIndex && this.index == this.childimgList.length - 1) {
                    this.$message.warning('已经是最后一张了')
                    return
                }
                if (this.index >= this.childimgList.length - 1) {
                    this.$emit('childGetImgList', type)
                    this.chidomindex++
                } else {
                    this.index++
                }
                console.log(this.index, 'index');
            } else {
                if (this.chidomindex == 0 && this.index == 0) {
                    this.$message.warning('已经是第一张了')
                    return
                }
                if (this.index == 0) {
                    this.$emit('childGetImgList', type)
                    this.chidomindex--
                } else {
                    this.index--
                }
                console.log(this.index, 'index');
            }
        },
        clearCss() {
            this.$refs.imageModel.focus()
            this.deg = 0
            this.scale = 1
            this.transition = 'none'
        },
        revolve(type) {
            this.transition = '0.3s'
            console.log(this.deg, 'left');
            if (type == 'left') {
                this.deg -= 90
            }
            if (type == 'right') {
                this.deg += 90
                console.log(this.deg, 'right');
            }
        },
        magnify(type) {
            this.transition = '0.3s'
            if (type == 'big') {
                this.scale += 0.2
            } else {
                if (this.scale.toString() <= 0.21) return
                this.scale -= 0.2
            }
        },
        handleKeyDown(e) {
            this.transition = '0.3s'
            if (e.keyCode == 37) {
                this.changeLocation('left')
            } else if (e.keyCode == 39) {
                this.changeLocation('right')
            } else if (e.keyCode == 38) {
                this.magnify('big')
            }
            else if (e.keyCode == 40) {
                this.magnify('small')
            }
            else if (e.keyCode == 27) {
                this.closeModal()
            }
        }
    }
}
</script>

<style scoped lang="scss">
.imageModel {
    //宽高是整个屏幕的宽高
    width: 100vw;
    height: 100vh;
    //黑色半透明背景
    background-color: rgba(0, 0, 0, 0.4);
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 10px;
    box-sizing: border-box;
    position: relative;

    &:hover {

        .el-icon-arrow-left,
        .el-icon-arrow-right,
        .imageModel_bottom {
            display: block;
        }
    }

    .el-icon-close {
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 20px;
        color: #fff;
        cursor: pointer;
        z-index: 999;
    }

    .el-icon-arrow-left {
        position: absolute;
        top: 50%;
        left: 10px;
        font-size: 40px;
        color: #fff;
        cursor: pointer;
        display: none;
        z-index: 999;
    }

    .el-icon-arrow-right {
        position: absolute;
        top: 50%;
        right: 10px;
        font-size: 40px;
        color: #fff;
        cursor: pointer;
        display: none;
        z-index: 999;
    }

    .imageModel_bottom {
        width: 200px;
        height: 30px;
        padding: 10px 20px 0;
        background-color: rgba(94, 85, 93, 0.5);
        position: absolute;
        bottom: 17%;
        left: 42%;
        font-size: 20px;
        color: #fff;
        cursor: pointer;
        display: none;
        flex-direction: row;
        border-radius: 20px 20px 10px 10px;
        z-index: 999;

        i {
            margin-right: 20px;
        }

    }

    .imageModel_info {
        width: 70vw;
        height: 40px;
        line-height: 40px;
        text-align: center;
        background-color: rgba(142, 142, 142, 0.5);
        position: fixed;
        bottom: 5%;
        left: 16%;
        font-size: 20px;
        color: #fff;
        flex-direction: row;
        border-radius: 20px;
        z-index: 999;
        font-size: 16px;
        display: flex;
        justify-content: center;

        .imageModel_info_item {
            margin-right: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 14px;
        }

    }

}

.viewImageBox {
    min-width: 100%;
    min-height: 100%;
    width: 100%;
    height: 100%;
    transform-origin: 48% 51%;
}

.viewImageBox ::v-deep img {
    min-width: 100% !important;
    min-height: 100% !important;
    object-fit: contain !important;
}
</style>