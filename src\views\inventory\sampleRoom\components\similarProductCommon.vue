<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <number-range :min.sync="ListInfo.measuredLengthMin" :max.sync="ListInfo.measuredLengthMax" min-label="打包长-最低"
          max-label="打包长-最高" class="publicCss" :maxNumber="999999" :precision="2" />
        <number-range :min.sync="ListInfo.measuredWidthMin" :max.sync="ListInfo.measuredWidthMax" min-label="打包宽-最低"
          max-label="打包宽-最高" class="publicCss" :maxNumber="999999" :precision="2" />
        <number-range :min.sync="ListInfo.measuredHeightMin" :max.sync="ListInfo.measuredHeightMax" min-label="打包高-最低"
          max-label="打包高-最高" class="publicCss" :maxNumber="999999" :precision="2" />
        <number-range :min.sync="ListInfo.zhanKaiLengthMin" :max.sync="ListInfo.zhanKaiLengthMax" min-label="展开长-最低"
          max-label="展开长-最高" class="publicCss" :maxNumber="999999" :precision="2" />
        <number-range :min.sync="ListInfo.zhanKaiWidthMin" :max.sync="ListInfo.zhanKaiWidthMax" min-label="展开宽-最低"
          max-label="展开宽-最高" class="publicCss" :maxNumber="999999" :precision="2" />
        <number-range :min.sync="ListInfo.zhanKaiHeightMin" :max.sync="ListInfo.zhanKaiHeightMax" min-label="展开高-最低"
          max-label="展开高-最高" class="publicCss" :maxNumber="999999" :precision="2" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
      </div>
    </template>
    <vxetablebase :id="tableId" :tablekey="tableId" :ref="`table${sampleOrBrand}`" :that='that' :isIndex='true'
      :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
      :isSelection="false" :isSelectColumn="false" style="width: 100%; margin: 0" :loading="loading" :height="'100%'"
      :border="true" :tree-props="{ children: 'children' }" :isRemoteSort="true" :isWebSort="false"
      :key="`vxetable-${sampleOrBrand}-${tableId}`">
      <template slot="right">
        <vxe-column title="操作" width="120" fixed="right">
          <template #default="{ row, rowIndex }">
            <div v-if="!row.children" style="display: flex;justify-content: center;">
              <template v-if="sampleOrBrand === 1">
                <el-button v-if="row.isSimilarAccordant === null" type="text"
                  @click="handleIsConsistent(row, 'yes')">一致</el-button>
                <el-button v-if="row.isSimilarAccordant === null" type="text"
                  @click="handleIsConsistent(row, 'no')">不一致</el-button>
              </template>
              <template v-else-if="sampleOrBrand === 2">
                <el-button v-if="row.isSimilarAccordant2 === null" type="text"
                  @click="handleIsConsistent(row, 'yes')">一致</el-button>
                <el-button v-if="row.isSimilarAccordant2 === null" type="text"
                  @click="handleIsConsistent(row, 'no')">不一致</el-button>
              </template>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import { getSimilaritySampleRegistrationPage, setSampleRegistrationSimilarIsEqual } from "@/api/inventory/sampleGoods";
import numberRange from "@/components/number-range/index.vue";

// 基础表格列配置
const baseTableCols = [
  { sortable: 'custom', width: '100', align: 'center', prop: 'measuredChang', label: '打包长(mm)', treeNode: true, fixed: "left" },
  { sortable: 'custom', width: '100', align: 'center', prop: 'measuredWidth', label: '打包宽(mm)' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'measuredHeight', label: '打包高(mm)' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'zhankaiMeasuredChang', label: '展开长(mm)' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'zhankaiMeasuredWidth', label: '展开宽(mm)' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'zhankaiMeasuredHeight', label: '展开高(mm)' },
  { width: '140', align: 'center', prop: 'goodsCode', label: '商品编码' },
  { width: '140', align: 'center', prop: 'goodsName', label: '商品名称' },
  { width: '100', align: 'center', prop: 'sampleSource', label: '样品来源' },
  { width: '100', align: 'center', prop: 'goodsNumber', label: '商品编号' },
  { width: '100', align: 'center', prop: 'yhGoodsCode', label: '公司商品编码' },
  { width: '100', align: 'center', prop: 'styleCode', label: '款式名称' },
  { width: '100', align: 'center', prop: 'ckGoodsCode', label: '参考商品编码' },
  {
    width: '100', align: 'center', prop: 'goodsState', label: '编码状态', formatter: (row) => {
      return row.goodsStateStr;
    }
  },
  { width: '100', align: 'center', prop: 'bitNumber', label: '库位' },
  { width: '100', align: 'center', prop: 'warehouse', label: '仓库' },
  {
    width: '100', align: 'center', prop: 'storageStatus', label: '入库状态', formatter: (row) => {
      return row.storageStatus == 1 ? '待入库' : row.storageStatus == 2 ? '待归还' : row.storageStatus == 3 ? '在库' : row.storageStatus == 4 ? '待领取' : ''
    }
  },
  { width: '100', align: 'center', prop: 'buyNo', label: '采购单号' },
  { width: '100', align: 'center', prop: 'numberShots', label: '拍摄次数' },
  { width: '100', align: 'center', prop: 'shootingTime', label: '拍摄时间' },
  { width: '100', align: 'center', prop: 'createdUserName', label: '拍摄人' },
  { width: '100', align: 'left', prop: 'images', label: '商品图片', type: "images" },
  { width: '100', align: 'left', prop: 'outerPackingPicture', label: '外包装图片', type: "images" },
  { width: '100', align: 'left', prop: 'physicalPicture', label: '实物图片', type: "images" },
  { width: '100', align: 'center', prop: 'isChao40CM', label: '折叠单边是否超40CM', formatter: (row) => row.isChao40CM == 1 ? '是' : row.isChao40CM == 0 ? '否' : '' },
  { width: '100', align: 'center', prop: 'isZheDie', label: '是否可折叠', formatter: (row) => row.isZheDie == 1 ? '是' : row.isZheDie == 0 ? '否' : '' },
  { width: '100', align: 'center', prop: 'measuredWeight', label: '测量重(g)' },
  { width: '100', align: 'left', prop: 'measuredWeightImages', label: '测重图', type: "images" },
  // { width: '100', align: 'center', prop: 'measuredChang', label: '打包长(mm)' },
  { width: '100', align: 'left', prop: 'measuredChangImages', label: '测打包长图', type: "images" },
  { width: '100', align: 'center', prop: 'measuredWidth', label: '打包宽(mm)' },
  { width: '100', align: 'left', prop: 'measuredWidthImages', label: '测打包宽图', type: "images" },
  // { width: '100', align: 'center', prop: 'measuredHeight', label: '打包高(mm)' },
  { width: '100', align: 'left', prop: 'measuredHeightImages', label: '测打包高图', type: "images" },
  // { width: '100', align: 'center', prop: 'zhankaiMeasuredChang', label: '展开长(mm)' },
  { width: '100', align: 'left', prop: 'zhankaiMeasuredChangImages', label: '测展开长图', type: "images" },
  { width: '100', align: 'center', prop: 'zhankaiMeasuredWidth', label: '展开宽(mm)' },
  { width: '100', align: 'left', prop: 'zhankaiMeasuredWidthImages', label: '测展开宽图', type: "images" },
  // { width: '100', align: 'center', prop: 'zhankaiMeasuredHeight', label: '展开高(mm)' },
  { width: '100', align: 'left', prop: 'zhankaiMeasuredHeightImages', label: '测展开高图', type: "images" },
  { width: '100', align: 'center', prop: 'recentLender', label: '最近借出人' },
  { width: '100', align: 'center', prop: 'lastExitTime', label: '最近借出时间' },
  { width: '100', align: 'center', prop: 'deliveryTime', label: '借出时长' },
  { width: '100', align: 'center', prop: 'estimatedServiceLife', label: '预计归还时间' },
  { width: '100', align: 'center', prop: 'actualReturnTime', label: '实际归还时间' },
  { width: '100', align: 'center', prop: 'daysOverdue', label: '逾期天数' },
  { width: '100', align: 'center', prop: 'colour', label: '颜色' },
  { width: '100', align: 'center', prop: 'isJianCe', label: '是否检测', formatter: (row) => row.isJianCe == 1 ? '是' : row.isJianCe == 2 ? '否' : '' },
  { width: '100', align: 'center', prop: 'groupName', label: '运营组' },
  { width: '100', align: 'center', prop: 'brandName', label: '采购员' },
  { width: '100', align: 'center', prop: 'brandDeptName', label: '采购组' },
  { width: '100', align: 'center', prop: 'manufacturerName', label: '厂家名称' },
  { width: '100', align: 'center', prop: 'manufacturerLink', label: '厂家链接' },
  { width: '100', align: 'center', prop: 'opponentShopName', label: '对手店铺' },
  { width: '100', align: 'center', prop: 'opponentLink', label: '对手链接' },
  { width: '100', align: 'center', prop: 'expressNo', label: '快递单号/标识' },
  { width: '120', align: 'center', prop: 'packageGoodsCode', label: '包材商品编码' },
  { width: '100', align: 'center', prop: 'package', label: '包材名称' },
  { width: '100', align: 'center', prop: 'packageImage', label: '包材图片', type: "images" },
  { width: '100', align: 'center', prop: 'packageSize', label: '包材尺寸' },
  { width: '100', align: 'center', prop: 'damageRate', label: '破损率', formatter: (row) => row.damageRate || row.damageRate == 0 ? row.damageRate + '%' : '' },
  { width: '120', align: 'center', prop: 'newPackageGoodsCode', label: '新包材商品编码' },
  { width: '100', align: 'center', prop: 'newPackage', label: '新包材名称' },
  { width: '100', align: 'center', prop: 'newPackageImage', label: '新包材图片', type: "images" },
  { width: '100', align: 'center', prop: 'newPackageSize', label: '新包材尺寸' },
  { width: '100', align: 'center', prop: 'newDamageRate', label: '新破损率', formatter: (row) => row.newDamageRate || row.newDamageRate == 0 ? row.newDamageRate + '%' : '' },
  { width: '100', align: 'center', prop: 'isFuHeTongGuo', label: '复核结果', formatter: (row) => row.isFuHeTongGuo == 1 ? '复核通过' : row.isFuHeTongGuo == 0 ? '复核不通过' : '' },
  { width: '100', align: 'center', prop: 'noFuHeTongGuoRemark', label: '未通过复核原因' },
  { width: '100', align: 'center', prop: 'isRevert', label: '是否同品归还', formatter: (row) => row.isRevert == 1 ? '是' : row.isRevert == 0 ? '否' : '' },
]

export default {
  name: "similarProductCommon",
  components: {
    MyContainer, vxetablebase, numberRange
  },
  props: {
    sampleOrBrand: {
      type: Number,
      default: 1 // 1样品间  2采购
    }
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        sampleOrBrand: this.sampleOrBrand,// 1样品间  2采购
        measuredLengthMin: null, // 打包长-最低
        measuredLengthMax: null, // 打包长-最高
        measuredWidthMin: null, // 打包宽-最低
        measuredWidthMax: null, // 打包宽-最高
        measuredHeightMin: null, // 打包高-最低
        measuredHeightMax: null, // 打包高-最高
        zhanKaiLengthMin: null, // 展开长-最低
        zhanKaiLengthMax: null, // 展开长-最高
        zhanKaiWidthMin: null, // 展开宽-最低
        zhanKaiWidthMax: null, // 展开宽-最高
        zhanKaiHeightMin: null, // 展开高-最低
        zhanKaiHeightMax: null, // 展开高-最高
      },
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  computed: {
    tableId() {
      return `similarProduct${this.sampleOrBrand === 1 ? 'Ypj' : 'Cg'}202507220919`
    },
    tableCols() {
      const cols = [...baseTableCols];
      let a = this.sampleOrBrand === 1 ? 'isSimilarAccordant' : 'isSimilarAccordant2'
      let b = this.sampleOrBrand === 1 ? 'handleResult' : 'handleResult2'
      cols.push(
        {
          width: '120',
          align: 'center',
          prop: a,
          label: '是否相似品一致',
        },
        {
          width: '100',
          align: 'center',
          prop: b,
          label: '处理结果',
          formatter: (row) => {
            return row[b]
          }
        }
      );
      return cols;
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    handleIsConsistent(row, type) {
      this.$prompt(`是否执行${type == 'yes' ? '一致' : '不一致'}操作？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '可输入处理结果（最多200字）',
        inputValidator: (value) => {
          if (!value) return '请输入内容';
          if (value.length > 200) return '输入内容不能超过200字符';
          return true;
        },
        inputErrorMessage: '输入内容不符合要求',
        type: 'warning'
      }).then(async ({ value }) => {
        // 确保截取前200字符
        const trimmedValue = value ? value.substring(0, 200) : '';
        const { success } = await setSampleRegistrationSimilarIsEqual({
          ids: [row.id],
          sampleOrBrand: this.sampleOrBrand,
          isEqual: type == 'yes' ? 1 : 0,
          remark: trimmedValue,
        });
        if (success) {
          this.$message.success('操作成功');
          this.getList();
        }
      }).catch(() => {
      });
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getSimilaritySampleRegistrationPage(this.ListInfo)
      if (success && data) {
        const fieldsToProcess = [
          'images',
          'outerPackingPicture',
          'physicalPicture',
          'measuredWeightImages',
          'measuringThicknessImages',
          'measuredWidthImages',
          'measuredHeightImages',
          'measuredChangImages',
          'zhankaiMeasuredHeightImages',
          'zhankaiMeasuredWidthImages',
          'zhankaiMeasuredChangImages',
          'packageImage',
          'newPackageImage',
        ];
        const processVoucher = (voucher) => {
          if (voucher) {
            return JSON.stringify(voucher.split(',').map(url => ({ url })));
          }
          return voucher;
        };
        let c = this.sampleOrBrand === 1 ? 'isSimilarAccordant' : 'isSimilarAccordant2'
        data.list.forEach(item => {
          fieldsToProcess.forEach(field => {
            item[field] = processVoucher(item[field]);
          });
          if (item.children && item.children.length > 0) {
            item.children.forEach(child => {
              child[c] = child[c] == 1 ? '是' : child[c] == 0 ? '否' : null
            });
          }
        });
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summaryarry
        this.loading = false
      } else {
        this.loading = false
        this.$message.error('获取列表失败')
      }
    },
    async Pagechange(e) {
      this.ListInfo.currentPage = e
      await this.getList()
    },
    async Sizechange(e) {
      this.ListInfo.pageSize = e
      await this.getList()
    },
    async sortchange(e) {
      this.ListInfo.orderBy = e.prop
      this.ListInfo.isAsc = e.order == 'asc'
      await this.getList()
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 180px;
    margin-right: 5px;
  }
}
</style>
