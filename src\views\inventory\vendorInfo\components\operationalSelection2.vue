<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <div class="publicCss" style="width: 155px;">
          <inputYunhan ref="productstyleCode" :inputt.sync="ListInfo.styleCode" v-model="ListInfo.styleCode"
            width="155px" placeholder="款式名称/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'styleCode')" title="款式名称">
          </inputYunhan>
        </div>
        <div class="publicCss" style="width: 155px;">
          <inputYunhan ref="productproductName" :inputt.sync="ListInfo.productName" v-model="ListInfo.productName"
            width="155px" placeholder="商品名称/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'productName')" title="商品名称">
          </inputYunhan>
        </div>
        <div class="publicCss" style="width: 175px;">
          <inputYunhan ref="productyhGoodsCode" :inputt.sync="ListInfo.yhGoodsCode" v-model="ListInfo.yhGoodsCode"
            width="175px" placeholder="公司商品编码/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'yhGoodsCode')" title="公司商品编码">
          </inputYunhan>
        </div>
        <el-select v-model="ListInfo.catroyType" placeholder="类目" clearable filterable class="publicCss"
          style="width: 95px;">
          <el-option v-for="item in categoryList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-input v-model.trim="ListInfo.packagingSpecification" placeholder="包装" maxlength="50" clearable
          class="publicCss" style="width: 95px;" />
        <el-input v-model.trim="ListInfo.material" placeholder="材质" maxlength="100" clearable class="publicCss"
          style="width: 95px;" />
        <el-select v-model="ListInfo.isPriceControl" placeholder="是否控价" clearable class="publicCss"
          style="width: 90px;">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="ListInfo.platforms" placeholder="平台" multiple collapse-tags clearable filterable
          class="publicCss" style="width: 155px;">
          <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="ListInfo.groupIds" collapse-tags clearable placeholder="运营组" multiple filterable
          class="publicCss" style="width: 155px;">
          <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="ListInfo.dDUserIds" placeholder="请模糊输入并选择选品人" clearable filterable remote
          :filter-method="(query) => searchReferrer(query)" value-key="value" multiple collapse-tags
          :reserve-keyword="false" class="publicCss" style="width: 165px;">
          <el-option v-for="item in contactPersonList"
            :key="'userSelector' + item.value + item.ddUserId + item.extData.defaultDeptId"
            :label="`${item.name}  - ${item.position} - ${item.empStatusText}`" :value="item.value" />
        </el-select>
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="选品开始时间" end-placeholder="选品结束时间" :picker-options="pickerOptions"
          style="width: 215px;margin-right: 2px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-button class="top_button" type="primary" @click="getList('search')">搜索</el-button>
      </div>
    </template>
    <vxetablebase :id="'operationalSelection2202503161619'" :tablekey="'operationalSelection2202503161619'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableVirtualScroll.vue";
import { pickerOptions, platformlist } from '@/utils/tools'
import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";
import { getDirectorGroupList, getDirectorList, getList as getshopList } from '@/api/operatemanage/base/shop'
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'
import { getErpSampleGoodsChooseRecordList2Async, exportErpSampleGoodsChooseRecordListAsync } from '@/api/customerservice/albbinquirs'
const categoryList = ['3C数码配件、电器', '艺术收藏用品', '运动户外', '载具用品', '仪器仪表', '五金建材工具', '居家布艺', '配饰专区', '饰品装饰', '美妆美容美发美体用品', '服饰专区', '玩具动漫周边', '日用餐厨饮具', '生活工具', '收纳清洁用具', '孕产妇/婴童用品', '宠物用品', '办公文化', '节庆用品礼品']

const tableCols = [
  { width: '90', align: 'center', prop: 'image', label: '产品图片', type: "images" },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '款式名称' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'productName', label: '商品名称' },
  { sortable: 'custom', width: '130', align: 'center', prop: 'catroyType', label: '类目', },
  { sortable: 'custom', width: '70', align: 'center', prop: 'packagingSpecification', label: '包装' },
  { sortable: 'custom', width: '70', align: 'center', prop: 'colour', label: '颜色' },
  { sortable: 'custom', width: '70', align: 'center', prop: 'material', label: '材质' },
  { sortable: 'custom', width: '70', align: 'center', prop: 'weight', label: '重量' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'minimumSellingPrice', label: '供货成本价' },
  { sortable: 'custom', width: '80', align: 'center', prop: 'isPriceControl', label: '是否控价' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'minimumSellingControlPrice', label: '供货成本价(控价价格)' },
  { sortable: 'custom', width: '70', align: 'center', prop: 'isZiZhi', label: '资质' },
  { sortable: 'custom', width: '70', align: 'center', prop: 'isZhuanli', label: '专利' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'yhGoodsCode', label: '公司商品编码' },
  { sortable: 'custom', width: '70', align: 'center', prop: 'platform', label: '平台', formatter: (row) => row.platformName },
  { sortable: 'custom', width: '90', align: 'center', prop: 'groupId', label: '运营组', formatter: (row) => row.groupName },
  { sortable: 'custom', width: '70', align: 'center', prop: 'userName', label: '选品人' },
  { sortable: 'custom', width: '120', align: 'center', prop: 'b.oldCreatedTime', label: '选品时间', formatter: (row) => row.oldCreatedTime ? dayjs(row.oldCreatedTime).format('YYYY-MM-DD HH:mm:ss') : '' },

  { sortable: 'custom', width: '300', align: 'center', prop: 'remark', label: '备注' },
]
export default {
  name: "operationalSelection2",
  components: {
    MyContainer, vxetablebase, inputYunhan
  },
  data() {
    return {
      categoryList,
      contactPersonList: [],
      grouplist: [],
      yesOrNoOptions: [
        { value: 1, label: '是' },
        { value: 0, label: '否' },
      ],
      platformlist,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        styleCode: null,//款式名称
        productName: null,//商品名称
        packagingSpecification: null,//包装
        material: null,//材质
        isPriceControl: null,//是否控价
        yhGoodsCode: null,//公司商品编码
        groupIds: [],//运营组
        dDUserIds: [],//选品人
        platforms: [],//平台
        catroyType: null,//类目
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
    await this.init()
  },
  methods: {
    async searchReferrer(e) {
      //如果e的长度大于200,就提示
      if (e.length > 200) {
        this.$message.error('最多输入200个字符')
        return
      }
      // 如果输入为空，清空下拉框
      if (e === '' || e === null || e === undefined) {
        this.contactPersonList = []
        return
      }
      const { data } = await QueryAllDDUserTop100({ keywords: e });
      if (!data) return;
      const targetData = data.map(item => ({
        value: item.ddUserId,
        label: `${item.userName} - (${item.deptName})`,
        name: item.userName,
        ddUserId: item.ddUserId,
        extData: item,
        position: item.position,
        deptName: item.deptName,
        empStatusText: item.empStatusText,
      }));
      this.contactPersonList = targetData;
    },
    callbackGoodsCode(val, type) {
      const map = {
        productName: () => (this.ListInfo.productName = val),
        yhGoodsCode: () => (this.ListInfo.yhGoodsCode = val),
        styleCode: () => (this.ListInfo.styleCode = val),
      };
      map[type]?.();
    },
    async init() {
      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async exportData() {
      this.loading = true
      const res = await exportErpSampleGoodsChooseRecordListAsync(this.ListInfo)
      this.loading = false
    },
    handleImage(item, field) {
      const images = item[field];
      if (images) {
        return JSON.stringify(images.split(',').map(url => ({ url, name: '' })));
      }
      return JSON.stringify([]);
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认当天时间
        this.ListInfo.startTime = dayjs().format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await getErpSampleGoodsChooseRecordList2Async(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.image = this.handleImage(item, 'image');
        });
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 5px;
  flex-wrap: wrap;

  .publicCss {
    width: 140px;
    margin: 0 2px 0 0;
  }
}

::v-deep .el-select__tags-text {
  max-width: 30px;
}

::v-deep(.el-button.top_button + .el-button.top_button) {
  margin-left: 1px;
}
</style>
