<template>
    <container v-loading="pageLoading">
        <el-tabs v-model="activeName" style="height: 94%;">
            <el-tab-pane lazy label="普通商品资料" name="first" style="height: 100%;">
                <goods ref="goods"></goods>
            </el-tab-pane>
            <el-tab-pane lazy label="待下架" name="second" style="height: 100%;">
                <goodsrapidretire ref="goodsrapidretire"></goodsrapidretire>
            </el-tab-pane>
            <el-tab-pane lazy label="记录" v-if="checkPermission('goodstabrapidretirelog')" name="thirdly" style="height: 100%;">
                <goodsrapidretirelog ref="goodsrapidretirelog"></goodsrapidretirelog>
            </el-tab-pane>
        </el-tabs>
    </container>
</template>

<script>

import container from "@/components/my-container";
import goods from "./goods.vue";
import goodsrapidretire from "./goodsrapidretire.vue";
import goodsrapidretirelog from "./goodsrapidretirelog.vue";

export default {
    name: 'YunHanAdminGoodstab',
    components: { container, goods, goodsrapidretire, goodsrapidretirelog},

    data() {
        return {
            that: this,
            activeName: 'first',
            listLoading: false,
            pageLoading: false,
        };
    },

    async mounted() {
        await this.onSearch();
    },

    methods: {
        async onSearch() {
            this.$nextTick(() => {
                if (this.activeName == 'first') this.$refs.goods.onSearch();
                if (this.activeName == 'second') this.$refs.finishedpartVue.onSearch();
                if (this.activeName == 'thirdly') this.$refs.goodsrapidretirelog.onSearch();
            })
        },
        
    },
};
</script>
 

</style>