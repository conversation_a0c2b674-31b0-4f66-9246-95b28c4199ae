<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='groupinquirsstatisticslist' @select='selectchange' :isSelection='false'
            :tableCols='tableCols' :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.GroupNameList"   placeholder="分组" clearable filterable multiple :collapse-tags="true">
                            <el-option v-for="item in filterGroupList" :key="item" :label="item"
                                :value="item">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <datepicker v-model="filter.sdate"></datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport" style="margin-left: 10px;" v-if="checkPermission(['api:Customerservice:KuaiShouInquirs:ExportKuaiShouGroupEfficiencyList0'])">导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" 
                @get-page="getgroupinquirsstatisticsList" />
        </template>

        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="false" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
    getKuaiShouGroup,
    getKuaiShouGroupEfficiencyPageList, 
    getKuaiShouGroupEfficiencyChat,
    exportKuaiShouGroupEfficiencyList
} from '@/api/customerservice/kuaishouinquirs'


import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar'
const tableCols = [
    { istrue: true, prop: 'groupName', label: '组名', width: '150', sortable: 'custom', type: "click", handle: (that, row, column, cell) => that.groupclick(row, column, cell), formatter: (row) => row.groupname },
    { istrue: true, prop: 'inquirs', label: '咨询人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'inquireCount', label: '咨询人次', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'artificialConversation', label: '人工会话量', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'perCapitaReception', label: '人均接待量', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'conversation3MinRate', label: '三分钟回复率（会话）', width: '100', sortable: 'custom', formatter: (row) => (row.conversation3MinRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'customerService3MinRate', label: '三分钟回复率（人维度）', width: '100', sortable: 'custom', formatter: (row) => (row.customerService3MinRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'noServiceRate', label: '不服务率', width: '80', sortable: 'custom', formatter: (row) => (row.noServiceRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'artificialAvgReply', label: '人工平均回复时长', width: '130', sortable: 'custom' },
    { istrue: true, prop: 'conversationNiceCommentRate', label: '好评率（会话）', width: '130', sortable: 'custom', formatter: (row) => (row.conversationNiceCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'niceCommentRate', label: '好评率（人维度）', width: '130', sortable: 'custom', formatter: (row) => (row.niceCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'badCommentRate', label: '差评率（人维度）', width: '130', sortable: 'custom', formatter: (row) => (row.badCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'centreCommentRate', label: '中评率（人维度）', width: '130', sortable: 'custom', formatter: (row) => (row.centreCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'imBadRate', label: 'IM不满意率（人维度）', width: '100', sortable: 'custom', formatter: (row) => (row.imBadRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'changePlatformRate', label: '转平台服务率', width: '130', sortable: 'custom', formatter: (row) => (row.changePlatformRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'conversationCentreCommentRate', label: '中评率（会话）', width: '130', sortable: 'custom', formatter: (row) => (row.conversationCentreCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'conversationBadCommentRate', label: '差评率（会话）', width: '130', sortable: 'custom', formatter: (row) => (row.conversationBadCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'inviteConversationCount', label: '邀评会话数', width: '100', sortable: 'custom'},
    { istrue: true, prop: 'inviteCommentRate', label: '邀评率（人维度）', width: '130', sortable: 'custom', formatter: (row) => (row.inviteCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'commentRate', label: '评价率（人维度）', width: '130', sortable: 'custom', formatter: (row) => (row.commentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'commentConversationCount', label: '评价会话数', width: '100', sortable: 'custom'},
    { istrue: true, prop: 'badCommentCount', label: '差评人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'centreComment', label: '中评人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'niceCommentCount', label: '好评人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'askOrderRate', label: '询单转化率', width: '100', sortable: 'custom', formatter: (row) => (row.askOrderRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'askPrice', label: '询单客单价', width: '100', sortable: 'custom'},
    { istrue: true, prop: 'salePrice', label: '客服销售额', width: '100', sortable: 'custom'},
    { istrue: true, prop: 'commentCount', label: '评价人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'placeOrderCount', label: '下单人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'payOrderCount', label: '支付人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'inviteCommentCount', label: '邀评人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'askOrderCount', label: '询单人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'attendanceDays', label: '出勤人次', width: '80', sortable: 'custom'},
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar },
    props:["partInfo"],
    data() {
        return {
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            filter: {
                groupType: 0,
                inquirsType: 0,
            },
            shopList: [],
            userList: [],
            filterGroupList: [],
            groupList: [],
            groupinquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { },
            pager: { OrderBy: "inquirs", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            isleavegroup:this.partInfo,//是否离组
        };
    },
    watch:{
        partInfo(){
            this.isleavegroup = this.partInfo;
            this.getKuaiShouGroup();
        }
    },
    async mounted() {
         this.isleavegroup = this.partInfo;
        await this.getKuaiShouGroup();
    },
    methods: {
        async getKuaiShouGroup() {
            let groups = await getKuaiShouGroup({ groupType: 0 ,isleavegroup:this.isleavegroup});
            if (groups?.success && groups?.data && groups?.data.length > 0) {
              this.filterGroupList=groups.data;
            }
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getgroupinquirsstatisticsList();
        },
        getParam() {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            return params;
        },
        async getgroupinquirsstatisticsList() {
            let params = this.getParam();
            this.listLoading = true;
            const res = await getKuaiShouGroupEfficiencyPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.groupinquirsstatisticslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async showchart(row) {
            let params = this.getParam();
            params.groupName = row.groupName;
            const res = await getKuaiShouGroupEfficiencyChat(params).then(res => {
                if (res) {
                    this.dialogMapVisible.visible = true;
                    this.dialogMapVisible.data = res;
                    this.dialogMapVisible.title = res.title;
                    res.title = "";
                }
            });
            this.dialogMapVisible.visible = true;
        },
        groupclick(row) {   //组名，点击跳转个人效率
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            window.sqshowlisttab5ks(row.groupName, this.filter.startDate, this.filter.endDate)
            window.showtab5()
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await exportKuaiShouGroupEfficiencyList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '快手组效率统计(售前组)_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 60px;
}
</style>
