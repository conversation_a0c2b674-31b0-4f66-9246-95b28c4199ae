<template>
     <my-container v-loading="pageLoading">
         <el-tabs v-model="activeName" style="height:94%;"  @tab-click="tabclick"  :before-leave="beforeleave" >
            <el-tab-pane name="tab0" label="任务列表"  style="height: 100%;"  >
                <shootingvideotask  :ischecklook="false" ref="shootingvideotask"  :role="currole" style="height: 100%;"  tablekey="shootingvideotaskgrid"  :lazy="true"/>
            </el-tab-pane>
            <el-tab-pane name="tab1" label="已确认"  style="height: 100%;" :lazy="true">
                <shootingvideotaskoverNew   ref="shootingvideotaskover"   :role="currole" :tasktype="1" style="height: 100%;"  :lazy="true" :tablekey="shootingvideotaskover"/>
            </el-tab-pane>
            <el-tab-pane name="tab2" label="统计列表"   v-if="checkPermission('shootingCacl')"   style="height: 100%;" :lazy="true">
                <shootingvideotaskoverCacle  :ischecklook="false" :examine="false" ref="shootingvideotaskoverCacle"   :role="currole" :tasktype="1" style="height: 100%;"  :lazy="true" :tablekey="shootingvideotaskover"/>
            </el-tab-pane>
            <el-tab-pane name="tab3"  label="存档" v-if="checkPermission('shootingCd')" style="height: 100%;">
                <shootingvideotaskover   ref="shootingvideotaskoverShop"   :role="currole" :tasktype="2" style="height: 100%;"  :lazy="true" :tablekey="shootingvideotaskoverShop"/>
            </el-tab-pane>
            <el-tab-pane name="tab4" label="回收站" v-if="checkPermission('shootingBack')"  style="height: 100%;">
                <shootingvideotaskback   ref="shootingvideotaskoverBack"   :role="currole" :tasktype="3"  style="height: 100%;"  :lazy="true" :tablekey="shootingvideotaskoverBack" />
            </el-tab-pane>
           <el-tab-pane name="tab5" label="打包进度"  style="height: 100%;">
                <shootingPackageInfo   ref="shootingPackageInfo" style="height: 100%;"  :lazy="true" />
            </el-tab-pane>
            <!-- <el-tab-pane name="tab6" label="核算"  v-if="checkPermission('shootingHs')"   style="height: 100%;">
                <accountsIndex   ref="accountsIndex" style="height: 100%;"   :lazy="true" @editclosed="editclosed" />
            </el-tab-pane>   -->
           <el-tab-pane  name="tab7" label="统计" v-if="checkPermission('shootingTj')"  style="height: 100%;" :lazy="true" >
                <shootingchart   ref="shootingchart" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane name="tab8" label="设置" v-if="checkPermission('shootingAdminSz')"     style="height: 100%;" :lazy="true" >
                <shootingbasicset   ref="shootingbasicset" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane name="tab99"    style="height: 100%;" :lazy="true" >
                <span slot="label">
                    <el-link type=""  style="color: #fff;" @click="toResultmatter">首页</el-link>
                </span>
            </el-tab-pane>
        </el-tabs>
    </my-container>

</template>
<script>
import shootingPackageInfo from '@/views/media/shooting/shootingPackageInfo';
import shootingbasicset from '@/views/media/shooting/shootingbasicset' ;
import MyContainer from "@/components/my-container";
import shootingvideotask from '@/views/media/shooting/shootingvideotask';
import shootingvideotaskover from '@/views/media/shooting/shootingvideotaskover';
import shootingvideotaskoverNew from '@/views/media/shooting/shootingvideotaskoverNew';
import shootingvideotaskoverCacle from '@/views/media/shooting/shootingvideotaskoverCacle';
import shootingvideotaskback from '@/views/media/shooting/shootingvideotaskback';
import shootingchart from '@/views/media/shooting/shootingchart';
import accountsIndex from '@/views/media/shooting/adjustAccounts/accountsIndex';



import { getShootOperationsGroup,getOperationsGroup,getErpUserInfoView} from '@/api/media/mediashare';
import { getUserRoleList,getShootingViewPersonAsync} from '@/api/media/ShootingVideo';
export default {
    name: "Users",
    components: { MyContainer , shootingvideotask,shootingPackageInfo,shootingvideotaskover
        ,shootingvideotaskback,shootingvideotaskoverCacle,shootingvideotaskoverNew,shootingchart,shootingbasicset,accountsIndex},
    data() {
        return {
            that: this,
            pageLoading: '',
            filter: {
            },
            shootingvideotaskover:"shootingvideotaskover",
            shootingvideotaskoverShop:"shootingvideotaskoverShop",
            shootingvideotaskoverBack:"shootingvideotaskoverBack",
            shopList: [],
            currole: 'tz',
            userList: [],
            groupList: [],
            warehouseList:[],
            taskUrgencyList:[],
            platformList:[],
            selids: [],
            activeName: 'tab0',
            isedit: false,
            tab0isfirst:true,
            tab1isfirst:true,
            tab2isfirst:true,
            tab3isfirst:true,
            tab4isfirst:true,
            tab5isfirst:true,
            tab6isfirst:true,
            tab7isfirst:true,
            tab8isfirst:true
        };
    },
    //向子组件注册方法
    provide () {
        return {
            getGroupListRelod: this.getGroupList,
            getShopListRelod: this.getShopList,
            getUserListRelod : this.getUserList,
            getUrgencyListRelod : this.getUrgencyList,
            getwarehouseListRelod :this.getwarehouseList,
            getPlatformListRelod :this.getPlatformList,
            getShootingViewPersonRelod:this.getShootingViewPerson
        }
    },
    async mounted() {
        await this.getrole();
    },
    methods: {
        toResultmatter(){
            this.$router.push({path: '/media/index/homepage'})
        },
        beforeleave(visitName, currentName ){

            if(this.isedit){
                this.$message("请保存后再操作")
            }
            if(visitName== "tab99")
                return false;
            return !this.isedit;
        },
        editclosed(val){
            this.isedit = val;
        },
        async getShootingViewPerson () {
            var res = await getShootingViewPersonAsync();
            if(res?.success)
            {
                return res.data;
            }else{
               return null;
            }
        },

        async getrole() {
            var res = await getUserRoleList();
            if(res?.success)
            {
               if(res.data == null){
                    this.currole ="tz";
               }else if (res.data.indexOf("视觉部经理") >-1 || res.data.indexOf("超级管理员") >-1){

                    this.currole ="b";
               }

            }else{
                this.currole ="tz";
            }

        },
        async getGroupList() {
            this.groupList = await getOperationsGroup({type:1});
            return this.groupList;
        },
        async getUserList() {
            this.userList = await getErpUserInfoView();
            return this.userList;
        },
        async getShopList() {
            this.shopList = await getShootOperationsGroup({type:2});
            return this.shopList;
        },
        async getUrgencyList() {
            this.taskUrgencyList = await getShootOperationsGroup({type:4});
            return this.taskUrgencyList;
        },
        async getwarehouseList() {
            this.warehouseList = await getShootOperationsGroup({type:3});
            return this.warehouseList;
        },
        async getPlatformList() {
            this.platformList = await getShootOperationsGroup({type:5});
            return this.platformList;
        },
        async tabclick(){
            switch(this.activeName){
                //任务列表史
                case 'tab0' :
                   /*  if(this.tab0isfirst){
                        //this.$refs.shootingvideotask.onSearh();
                        this.tab0isfirst =false;
                    }  */
                    break;
                //已完成
                case 'tab1' :
                    if(this.tab1isfirst){
                        await this.$nextTick(() =>{
                            this.$refs.shootingvideotaskover.initSearch();
                        })
                        this.tab1isfirst =false;
                    }
                    break;
                //统计列表
                case 'tab2' :
                    if(this.tab2isfirst){
                        await this.$nextTick(() =>{
                            this.$refs.shootingvideotaskoverCacle.initSearch();
                        })
                        this.tab2isfirst =false;
                    }
                    break;
                //存档
                case 'tab3' :
                    if(this.tab3isfirst){
                        await this.$nextTick(() =>{
                            this.$refs.shootingvideotaskoverShop.initSearch();
                        })
                        this.tab3isfirst =false;
                    }
                    break;
                //回收站
                case 'tab4' :
                    if(this.tab4isfirst){
                        await this.$nextTick(() =>{
                            this.$refs.shootingvideotaskoverBack.initSearch();
                        })
                        this.tab4isfirst =false;
                    }
                    break;
                //打包进度
                case 'tab5' :
                    if(this.tab5isfirst){
                       /*  await this.$nextTick(() =>{
                            this.$refs.shootingPackageInfo.initSearch();
                        }) */
                        this.tab5isfirst =false;
                    }
                    break;
                //核算
                case 'tab6' :
                    if(this.tab6isfirst){
                        /* await this.$nextTick(() =>{
                            this.$refs.shootingPackageInfo.initSearch();
                        }) */
                        this.tab6isfirst =false;
                    }
                    break;
                //统计
                case 'tab7' :
                    if(this.tab7isfirst){
                   /*      await this.$nextTick(() =>{
                            this.$refs.shootingchart.initSearch();
                        }) */
                        this.tab7isfirst =false;
                    }
                    break;
                //设置
                case 'tab8' :
                    if(this.tab8isfirst){
                         /* await this.$nextTick(() =>{
                            this.$refs.shootingbasicset.initSearch();
                        }) */
                        this.tab8isfirst =false;
                    }
                    break;
            }
        }
    },
};
</script>
<style lang="scss" scoped>
  ::v-deep .el-form-item--mini.el-form-item,
    .el-form-item--small.el-form-item {
        margin-bottom: 20px;
    }
    ::v-deep .vxe-table--render-default .vxe-header--column{
        line-height: 18px !important;
    }

</style>

