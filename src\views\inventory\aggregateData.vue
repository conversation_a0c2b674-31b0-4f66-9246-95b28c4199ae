<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.isUpper2Month" placeholder="是否超过两个月" class="publicCss" clearable>
                    <el-option key="是" label="是" :value="true"></el-option>
                    <el-option key="否" label="否" :value="false"></el-option>
                </el-select>
                <el-input v-model="ListInfo.title" placeholder="职位" class="publicCss" maxlength="50" clearable />
                <el-input v-model="ListInfo.supplierType" placeholder="供应商类型" class="publicCss" maxlength="50" clearable />
                <el-input v-model="ListInfo.userName" placeholder="姓名" class="publicCss" maxlength="50" clearable />
                <el-input v-model="ListInfo.seriesName" placeholder="款式编码" class="publicCss" maxlength="50" clearable />
                <el-input v-model="ListInfo.goodsCode" placeholder="商品编码" class="publicCss" maxlength="50" clearable />
                <el-input v-model="ListInfo.goodsName" placeholder="商品名称" class="publicCss" maxlength="50" clearable />
                <el-button type="primary" @click="getList(true)">搜索</el-button>
                <el-button type="primary" @click="downLoad">下载核价模版</el-button>
                <el-button type="primary" @click="exportProps">导出</el-button>
                <el-button type="primary" @click="createProps">导入</el-button>
                <el-button type="primary" @click="deleteProps">一键删除</el-button>
            </div>
            <el-date-picker v-model="downPriceTimeRanger" type="daterange" unlink-panels range-separator="至"
                start-placeholder="降价开始日期" end-placeholder="降价结束日期" :picker-options="pickerOptions"
                style="width: 240px;margin-right: 10px;" @change="changeTime($event, 'downPrice')" />
            <el-date-picker v-model="codeCreationTimeRanger" type="daterange" unlink-panels range-separator="至"
                start-placeholder="编码创建开始日期" end-placeholder="编码创建结束日期" :picker-options="pickerOptions"
                style="width: 260px;margin-right: 10px;" @change="changeTime($event, 'codeCreat')" />
            <el-date-picker v-model="addTimeRanger" type="daterange" unlink-panels range-separator="至"
                start-placeholder="添加开始日期" end-placeholder="添加结束日期" :picker-options="pickerOptions"
                style="width: 240px;margin-right: 10px;" @change="changeTime($event, 'addTime')" />
        </template>
        <vxetablebase :id="'aggregateData202408041535'" @select="checkboxRangeEnd" ref="table" :that='that' :isIndex='true' :hasexpand='true'
            :showsummary="true" :summaryarry="summary" :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
            :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" v-loading="loading"
            style="width: 100%; height: 640px; margin: 10px 0 0 0" />
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />

        <!-- 全局设置 -->
        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                    <el-button size="small" type="primary">点击上传</el-button>
                </el-tooltip>
            </el-upload>
            <div class="btnGroup">
                <el-button type="primary" @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="importProps">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { replaceSpace } from '@/utils/getCols'
import dayjs from 'dayjs'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pageGetStatData, exportStatData, importStatAsync, deleteGoodsCostStat } from '@/api/inventory/goodscostpricechg'
import { pickerOptions } from '@/utils/tools'

const tableCols = [
    { istrue: true, label: '', type: "checkbox" },
    { istrue: true, prop: 'userName', label: '姓名', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'title', label: '职位', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'seriesName', label: '款式编码', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'prevPrice', label: '聚水潭成本', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'price', label: '新成本', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'time', label: '降价日期', sortable: 'custom', width: 'auto', formatter: (row) => row.time ? dayjs(row.time).format('YYYY-MM-DD') : null },
    { istrue: true, prop: 'goodsCreatedTime', label: '编码创建时间', sortable: 'custom', width: 'auto', formatter: (row) => row.goodsCreatedTime ? dayjs(row.goodsCreatedTime).format('YYYY-MM-DD') : null },
    { istrue: true, prop: 'isUpper2Month', label: '是否超过两个月', sortable: 'custom', width: 'auto', formatter: (row) => row.isUpper2Month ? '是' : '否' },
    { istrue: true, prop: 'diffPrice', label: '降价差额', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'diffPercentValue', label: '降价百分比', sortable: 'custom', width: 'auto', formatter: (row) => row.diffPercent },
    { istrue: true, prop: 'supplierType', label: '供应商类型', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'createdTime', label: '添加日期', sortable: 'custom', width: 'auto', formatter: (row) => row.createdTime ? dayjs(row.createdTime).format('YYYY-MM-DD') : null },
]
export default {
    name: "aggregateData",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            tableCols,
            pickerOptions,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                userName: null,
                userId: null,
                title: null,
                goodsCode: null,//商品编码
                goodsName: null,//商品名称
                seriesName: null,//系列编码
                beginTime: null,//降价开始日期
                endTime: null,//降价结束日期
                beginGoodsCreated: null,//编码创建时间
                endGoodsCreated: null,//编码结束创建时间
                beginCreated: null,//添加日期
                endCreated: null,//添加日期
                isUpper2Month: null,//是否超过两个月
                supplierType: null,//供应商类型
            },
            summary: {},
            downPriceTimeRanger: [],//降价日期
            codeCreationTimeRanger: [],//编码创建时间
            addTimeRanger: [],//添加日期
            loading: false,
            tableData: [],
            total: 0,
            ids: [],
            options: [],
            importVisible: false,
            file: null,
            fileList: [],
            importLoading: false
        }
    },
    async mounted() {
        this.init()
        await this.getList()
    },
    methods: {
        downLoad(){
            window.open("../../static/excel/inventory/核价汇总模版.xlsx", "_self");
        },
        async deleteProps() {
            if (this.ids.length == 0) return this.$message.error('请选择要删除的数据')
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await deleteGoodsCostStat(this.ids)
                if (success) {
                    await this.getList()
                    this.ids = []
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        changeTime(e, type) {
            e = e ? e.map(f => dayjs(f).format('YYYY-MM-DD')) : null
            if (type == 'downPrice') {
                this.ListInfo.beginTime = e ? e[0] : null
                this.ListInfo.endTime = e ? e[1] : null
            }
            if (type == 'codeCreat') {
                this.ListInfo.beginGoodsCreated = e ? e[0] : null
                this.ListInfo.endGoodsCreated = e ? e[1] : null
            }
            if (type == 'addTime') {
                this.ListInfo.beginCreated = e ? e[0] : null
                this.ListInfo.endCreated = e ? e[1] : null
            }
            this.getList()
        },
        async importProps() {
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.success('正在导入中,请稍后...')
            const form = new FormData();
            form.append("file", this.file);
            this.importLoading = true
            const { success } = await importStatAsync(form)
            if (success) {
                this.$message.success('导入成功')
                this.importLoading = false
                this.importVisible = false
                this.getList()
            } else {
                this.$message.error('导入失败')
            }
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async createProps() {
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        async exportProps() {
            const { data } = await exportStatData(this.ListInfo)
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        init() {
            if (this.downPriceTimeRanger.length == 0) {
                this.downPriceTimeRanger = [dayjs().subtract(30, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
                this.ListInfo.beginTime = this.downPriceTimeRanger[0]
                this.ListInfo.endTime = this.downPriceTimeRanger[1]
            }
            //编码创建时间
            if (this.codeCreationTimeRanger.length == 0) {
                this.codeCreationTimeRanger = [dayjs().subtract(30, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
                this.ListInfo.beginGoodsCreated = this.codeCreationTimeRanger[0]
                this.ListInfo.endGoodsCreated = this.codeCreationTimeRanger[1]
            }
            //添加日期
            if (this.addTimeRanger.length == 0) {
                this.addTimeRanger = [dayjs().subtract(30, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
                this.ListInfo.beginCreated = this.addTimeRanger[0]
                this.ListInfo.endCreated = this.addTimeRanger[1]
            }
        },
        //点击复选框
        checkboxRangeEnd(row) {
            this.ids = row.map(f => f.id)
        },
        //查询列表
        async getList(isSearch) {
            isSearch ? this.ListInfo.currentPage = 1 : null
            const replaceArr = ['goodsCode', 'goodsName', 'seriesName', 'userName','supplierType','title']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            const { data, success } = await pageGetStatData(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summary = data.summary
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>