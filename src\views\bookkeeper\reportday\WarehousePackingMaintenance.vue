<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.wareHouse" placeholder="仓库" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.consumableCode" placeholder="耗材编码" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" @click="onAddnewMethod">新增</el-button>
        <el-button type="primary" @click="onExport" :loading="isExport">导出</el-button>
      </div>
    </template>
    <vxetablebase ref="table" :id="'WarehousePackingMaintenance'" tablekey="'WarehousePackingMaintenance'"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="120" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;align-items: center;">
              <el-button type="text" @click="onEditMethod(row)">编辑</el-button>
              <el-button type="text" style="color: red;" @click="onDeleteMethod(row)">删除</el-button>
              <el-button type="text" style="color: red;" @click="onBatchDeleteMethod(row)">批次删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="editName" :visible.sync="editdialogVisible" width="25%" v-dialogDrag>
      <div style="height: 280px;width: 100%;" v-loading="editloading">
        <el-form :model="editform" ref="editform" :rules="editrules" label-width="100px" class="demo-ruleForm">
          <el-form-item label="仓库" prop="wareHouse">
            <el-input v-model.trim="editform.wareHouse" placeholder="请输入仓库" maxlength="50" clearable class="editCss" />
          </el-form-item>
          <el-form-item label="系列编码" prop="styleCode">
            <el-input v-model.trim="editform.styleCode" placeholder="请输入系列编码" maxlength="50" clearable class="editCss" />
          </el-form-item>
          <el-form-item label="商品编码" prop="goodsCode">
            <el-input v-model="editform.goodsCode" placeholder="请输入商品编码" maxlength="50" clearable class="editCss" />
          </el-form-item>
          <el-form-item label="耗材编码" prop="consumableCode">
            <el-input v-model="editform.consumableCode" placeholder="请输入耗材编码" maxlength="50" clearable class="editCss" />
          </el-form-item>
          <el-form-item label="耗材成本价" prop="consumableCost">
            <el-input-number v-model="editform.consumableCost" placeholder="耗材成本价" :min="-9999999999" :max="99999999999999" :controls="false" class="editCss" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editdialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSaveMethod">确 定</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { ImportWarehousePackagingMaintenance, GetWarehousePackagingMaintenanceList, 
    AddEditWarehousePackagingMaintenance, DeleteWarehousePackingMaintenance, ExportWarehousePackingMaintenance } from '@/api/bookkeeper/reportdayV2'

const tableCols = [
    { sortable: 'custom', width: '300', align: 'center', prop: 'wareHouse', label: '仓库'},
    { sortable: 'custom', width: '300', align: 'center', prop: 'styleCode', label: '系列编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: '300', align: 'center', prop: 'consumableCode', label: '耗材编码', },
    { sortable: 'custom', width: '300', align: 'center', prop: 'consumableCost', label: '耗材成本价', },
]
export default {
  name: "WarehousePackingMaintenance",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      editloading: false,
      editform: {
        wareHouse: null,
        styleCode: null,
        goodsCode: null,
        consumableCode: null,
        consumableCost: null,
        id: 0,
      },
      editdialogVisible: false,
      editName: '新增',
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        wareHouse: null,
        goodsCode: null,
        consumableCode: null,
      },
      editrules: {
        wareHouse: [{ required: true, message: '请输入仓库', trigger: 'blur' }],
        styleCode: [{ required: true, message: '请输入系列编码', trigger: 'blur' }],
        goodsCode: [{ required: true, message: '请输入商品编码', trigger: 'blur' }],
        consumableCode: [{ required: true, message: '请输入耗材编码', trigger: 'blur' }],
        consumableCost: [{ required: true, message: '请输入耗材成本价', trigger: 'blur' }],
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async onSaveMethod() {
      if (!this.editform.wareHouse || !this.editform.styleCode || !this.editform.goodsCode || !this.editform.consumableCode || !this.editform.consumableCost) {
        this.$message({ message: "请填写完整信息", type: "warning" });
        return false;
      }
      this.editloading = true
      var res = await AddEditWarehousePackagingMaintenance(this.editform)
      this.editloading = false
      if (res?.success) {
        this.$message({ message: "保存成功", type: "success" });
        this.editdialogVisible = false
        await this.getList()
      }
    },
    onAddnewMethod() {
      this.openEditDialog('新增', {
        wareHouse: null,
        styleCode: null,
        goodsCode: null,
        consumableCode: null,
        consumableCost: null,
        id: 0,
      });
    },
    onEditMethod(row) {
      const clonedRow = JSON.parse(JSON.stringify(row));
      this.openEditDialog('编辑', clonedRow);
    },
    openEditDialog(editName, formData) {
      this.editName = editName;
      this.editform = { ...formData };
      this.editdialogVisible = true;
    },
    onDeleteMethod(row) {
      this.$confirm('是否删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        var res = await DeleteWarehousePackingMaintenance({ ...row, DeleteType: 0 })
        if (res?.success) {
          this.$message({ message: "删除成功", type: "success" });
          await this.getList()
        }
      }).catch(() => { });
    },
    onBatchDeleteMethod(row) {
      this.$confirm('是否删除该批次数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        var res = await DeleteWarehousePackingMaintenance({ ...row, DeleteType: 1 })
        if (res?.success) {
          this.$message({ message: "删除成功", type: "success" });
          await this.getList()
        }
      }).catch(() => { });
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await ImportWarehousePackagingMaintenance(form);
      if (res?.success)
        this.$message({ message: res.message || "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await GetWarehousePackagingMaintenanceList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //导出
    async onExport(){
        this.isExport = true
        const res = await ExportWarehousePackingMaintenance(this.ListInfo)
        this.isExport = false
        if(!res?.data){
            this.$message({ message: "没有数据", type: "warning" });
            return
        }
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '包装费_仓库包装维护' + new Date().toLocaleString() + '.xlsx')
        aLink.click()
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 5px;
  }
}

.editCss {
  width: 80%;
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep .el-input-number.is-without-controls .el-input__inner {
  padding-left: 5px;
}
</style>
