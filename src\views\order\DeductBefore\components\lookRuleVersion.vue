<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss"
          :clearable="false" />
        <div>
          <el-button type="primary" @click="getIds(true)">搜索</el-button>
          <el-button type="primary" @click="ruleSet">设置</el-button>
        </div>
      </div>
    </template>
    <div class="_content">
      <div class="_content_top">
        <div class="_content_main" v-if="tableData?.length > 0">
          <template v-for="item in tableData">
            <div style="width:550px;">
              <el-card class="_content_item">
                <template slot="header">
                  <div class="header_css">
                    <div>
                      <div class="_content_item_title">{{ item.ruleInfo.title }}</div>
                      <div class="_description">
                        {{ item.ruleInfo.description }}
                      </div>
                    </div>
                    <div>
                      总订单: <span class="discriptions_content" :style="{
                        color: item.totalCount == 0 || item.totalCount == null ? '#000' : '#409eff',
                        cursor: item.totalCount == 0 || item.totalCount == null ? 'auto' : 'pointer'
                      }" @click="openLog(item, 'totalCount', 'rule_')"> {{ item.totalCount }}</span>
                      <el-tooltip placement="top">
                        <div slot="content">
                          平台: {{ formatTags(item.ruleInfo.platforms, 'platforms') }}<br />
                          仓库: {{ formatTags(item.ruleInfo.wmsIds, 'warehouse') }}<br />
                          标签: {{ formatTags(item.ruleInfo.orderLabels, 'tags') }}
                        </div>
                        <i class="el-icon-question"></i>
                      </el-tooltip>
                    </div>
                  </div>
                  <el-row style="text-align: center;" class="nodes" :gutter="10">
                    <el-col :span="8">
                      <div class="node">
                        <i class="el-icon-s-flag"></i>
                        <div>
                          <div> 开始</div>
                          <div>{{ formatNode(item.ruleInfo.beginNode) }}</div>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="node">
                        <i class="el-icon-message-solid"></i>
                        <div>
                          <div>预警时长</div>
                          <div>{{ item.ruleInfo.warningHour }}h</div>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="node">
                        <i class="el-icon-success"></i>
                        <div>
                          <div>结束</div>
                          <div>{{ formatNode(item.ruleInfo.endNode) }}({{ item.ruleInfo.overHour }}h)</div>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </template>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-card shadow="hover">
                      <template slot="header">
                        未完成: <span class="discriptions_content" :style="{
                          color: item.unCptCount == 0 || item.unCptCount == null ? '#000' : '#409eff',
                          cursor: item.unCptCount == 0 || item.unCptCount == null ? 'auto' : 'pointer'
                        }" @click="openLog(item, 'unCptCount', 'rule_')"> {{ item.unCptCount }}</span>
                      </template>
                      <el-descriptions class="margin-top" :column="1" :size="size" border>
                        <el-descriptions-item labelClassName="item-group-label">
                          <template slot="label"> 正常用时 </template>
                          <span class="discriptions_content" :style="{
                            color: item.normalUnCptCount == 0 || item.normalUnCptCount == null ? '#000' : '#409eff',
                            cursor: item.normalUnCptCount == 0 || item.normalUnCptCount == null ? 'auto' : 'pointer'
                          }" @click="openLog(item, 'normalUnCptCount', 'rule_')">{{
                            item.normalUnCptCount
                          }}</span>
                        </el-descriptions-item>

                        <el-descriptions-item labelClassName="item-group-label">
                          <template slot="label"> 即将超时 </template>
                          <span class="discriptions_content" :style="{
                            color: item.willOverTimeUnCptCount == 0 || item.willOverTimeUnCptCount == null ? '#000' : '#409eff',
                            cursor: item.willOverTimeUnCptCount == 0 || item.willOverTimeUnCptCount == null ? 'auto' : 'pointer'
                          }" @click="openLog(item, 'willOverTimeUnCptCount', 'rule_')">{{
                            item.willOverTimeUnCptCount
                          }}</span>
                        </el-descriptions-item>

                        <el-descriptions-item labelClassName="item-group-label">
                          <template slot="label"> 超时未完成 </template>
                          <span class="discriptions_content" :style="{
                            color: item.overTimeUnCptCount == 0 || item.overTimeUnCptCount == null ? '#000' : '#409eff',
                            cursor: item.overTimeUnCptCount == 0 || item.overTimeUnCptCount == null ? 'auto' : 'pointer'
                          }" @click="openLog(item, 'overTimeUnCptCount', 'rule_')">{{
                            item.overTimeUnCptCount
                          }}</span>
                        </el-descriptions-item>
                      </el-descriptions>

                    </el-card>
                  </el-col>
                  <el-col :span="12">
                    <el-card shadow="hover">
                      <template slot="header">
                        已完成: <span class="discriptions_content" :style="{
                          color: item.cptCount == 0 || item.cptCount == null ? '#000' : '#409eff',
                          cursor: item.cptCount == 0 || item.cptCount == null ? 'auto' : 'pointer'
                        }" @click="openLog(item, 'cptCount', 'rule_')"> {{ item.cptCount }}</span>
                      </template>
                      <el-descriptions class="margin-top" :column="1" :size="size" border>
                        <el-descriptions-item labelClassName="item-group-label">
                          <template slot="label"> 未超时完成 </template>
                          <span class="discriptions_content" :style="{
                            color: item.normalCptCount == 0 || item.normalCptCount == null ? '#000' : '#409eff',
                            cursor: item.normalCptCount == 0 || item.normalCptCount == null ? 'auto' : 'pointer'
                          }" @click="openLog(item, 'normalCptCount', 'rule_')">{{
                            item.normalCptCount
                          }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item labelClassName="item-group-label">
                          <template slot="label"> 超时完成 </template>
                          <span class="discriptions_content" :style="{
                            color: item.overTimeCptCount == 0 || item.overTimeCptCount == null ? '#000' : '#409eff',
                            cursor: item.overTimeCptCount == 0 || item.overTimeCptCount == null ? 'auto' : 'pointer'
                          }" @click="openLog(item, 'overTimeCptCount', 'rule_')">{{
                            item.overTimeCptCount
                          }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item labelClassName="item-group-label">
                          <template slot="label"> 超时率 </template>
                          <span class="discriptions_content">{{
                            formatRate(item.overTimeRate)
                          }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item labelClassName="item-group-label">
                          <template slot="label"> 平均时长 </template>
                          <span class="discriptions_content" :title="formatMinutes(item.timeAvg).toolTip">{{
                            formatMinutes(item.timeAvg).str
                          }}</span>
                        </el-descriptions-item>
                      </el-descriptions>
                    </el-card>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </template>
        </div>
        <div class="_content_main" v-else>
          暂无数据</div>
      </div>

    </div>
    <el-dialog :visible.sync="ruleSetVisiable" width="80%" :close-on-click-modal="false" v-dialogDrag append-to-body>
      <ruleSetting v-if="ruleSetVisiable" />
    </el-dialog>

    <el-dialog :visible.sync="logVisiable" width="60%" :close-on-click-modal="false" v-dialogDrag>
      <codeLog v-if="logVisiable" :logQuery="logQuery" />
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { pickerOptions, platformlist } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import ruleSetting from "./ruleSetting.vue";
const api = '/api/verifyOrder/Orders/Rule/'
import request from '@/utils/request'
import codeLog from './codeLog.vue'
import { getAllWarehouse } from '@/api/inventory/warehouse'
import decimal from '@/utils/decimal'
const nodeList = [
  { label: '支付时间', value: 'PayTime' },
  { label: '接单时间', value: 'TakeTime' },
  { label: '审核时间', value: 'VerifyTime' },
  { label: '批次生成时间', value: 'PickGenTime' },
  { label: '拣货开始时间', value: 'PickTime' },
  { label: '拣货完成时间', value: 'PickCptTime' },
  { label: '打包时间', value: 'PackTime' },
  { label: '计划发货时间', value: 'PlanSendTime' },
  { label: '发货时间', value: 'SendTime' },
  { label: '揽收时间', value: 'CollectTime' },
  { label: '审单计算时间', value: 'VerifyCptTime' },
]
export default {
  name: "scanCodePage",
  components: {
    MyContainer, dateRange, ruleSetting, codeLog
  },
  data() {
    return {
      api,
      nodeList,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        startTime: dayjs().subtract(7, 'day').format("YYYY-MM-DD"),
        endTime: dayjs().format("YYYY-MM-DD"),
      },
      loading: true,
      ruleSetVisiable: false,
      ids: [],
      tableData: [],
      logVisiable: false,
      logQuery: {},
      platformlist,
      wmsList: []
    }
  },
  async created() {
    this.getAllWms()
    this.getIds()
  },
  async mounted() {
  },
  methods: {
    async getAllWms() {
      const { data } = await getAllWarehouse({ orderBy: 'name' })
      var res = data.filter((x) => x.name.indexOf('代发') < 0)
      this.wmsList = res
    },
    formatTags(val, type) {
      switch (type) {
        case 'platforms':
          return this.platformlist.filter(item => val?.includes(item.value))?.map(item => item.label).join(',')
        case 'warehouse':
          return this.wmsList.filter(item => val?.includes(item.wms_co_id))?.map(item => item.name).join(',')
        case 'tags':
          return val ? val.join(',') : val
        default:
          return val
      }
    },
    formatNode(val) {
      return val ? this.nodeList.find(item => item.value == val)?.label : val
    },
    formatMinutes(val) {
      //将分钟转化为x天x小时x分钟
      if (val == 0 || val == null) {
        return 0
      }
      let day = Math.floor(val / 1440)
      let hour = Math.floor((val % 1440) / 60)
      let minute = val % 60
      const obj = {
        str: `${day},${hour},${minute}`,
        toolTip: `${day}天${hour}小时${minute}分钟`
      }
      return obj
    },
    formatRate(val) {
      return val !== null ? decimal(val, 100, 2, '*') + '%' : val
    },
    openLog(item, key, type) {
      if (item[key] == 0 || item[key] == null) return;
      const params = {
        resource: type + key,
        ruleId: item.ruleInfo.id,
        startTakeTime: this.ListInfo.startTime,
        endTakeTime: this.ListInfo.endTime,
      }
      this.logQuery = { ...this.ListInfo, ...params, }
      this.logVisiable = true
    },
    async getIds(isSearch) {
      if (isSearch) {
        this.$set(this, 'tableData', [])
        this.$set(this, 'ids', [])
      }
      const { data, success } = await await request.post(`${this.api}PageGetData`, { currentPage: 1, pageSize: 1000 })
      if (!success) return
      this.$set(this, 'ids', data.list.map(item => item.id))
      await this.getAllDom()
    },
    getAllDom() {
      let res = []
      this.ids.forEach(async (item) => {
        try {
          const { data } = await request.post(`${this.api}StatData`, { id: item, startTime: this.ListInfo.startTime, endTime: this.ListInfo.endTime })
          res.push(data)
          res.sort((a, b) => {
            return a.ruleInfo.title.localeCompare(b.ruleInfo.title, 'zh')
          })
          this.$set(this, 'tableData', res)
        } catch (error) {
          console.log(error, 'error');
        }
      })
    },
    ruleSet() {
      this.ruleSetVisiable = true
    },

  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5px;

  .publicCss {
    width: 200px;
    margin: 0 5px 5px 0px;
  }
}

::v-deep .item-group-label1 {
  background-color: white !important;
  border: 0px !important;
}

._content {
  padding: 0 10px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  height: 100%;
  font-size: 12px;

  ._content_top {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #409eff;
    }

    ._content_main,
    ._content_main1 {
      display: -webkit-box;
      width: 100%;
      flex-wrap: wrap;
      overflow-x: auto;
      scrollbar-width: thin;
      scrollbar-color: #ccc transparent;
      white-space: nowrap !important;

      ._content_item {
        // width: 400px;
        border: 1px solid #ebeef5;
        margin: 0 10px 10px 0;
      }
    }
  }

  ._content_bottom {
    ._content_main1 {
      display: flex;
      flex-direction: column;

      ._content_item {
        display: -webkit-box;
        flex-direction: row;
        background-color: transparent;
        border: none;
        box-shadow: none;
        margin: 0;
        width: 100%;
        overflow-x: auto;
        scrollbar-width: thin;
        scrollbar-color: #ccc transparent;

        // 移除 hover 效果
        &:hover {
          background-color: transparent;
        }

        ._content_item_box {
          // width: 100%;
          background-color: #f2f2f2;
          border: 1px solid #ebeef5;
          border-radius: 5px;
          height: 100%;
          display: flex;
          flex-direction: column;
          gap: 10px;
          box-sizing: border-box;
          box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
          padding: 15px;

          &:hover {
            background-color: #e6f7ff;
          }

          ._content_item_info {
            display: flex;
            flex-wrap: wrap;

            ._content_item_info_left,
            ._content_item_info_right {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            ._content_item_info_left {
              width: 70px;
            }

            ._content_item_info_right {
              width: 50px;
            }
          }
        }
      }
    }
  }
}

._description {
  color: #ccc;
  font-size: 12px;
  height: 18px;
}

.nodes {
  .node {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f2f2f2;
    padding: 10px;
    gap: 10px;

    i {
      font-size: 18px;
    }
  }
}

.header_css {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-icon-s-flag {
  color: #409eff;
}

.el-icon-message-solid {
  color: #f78989;
}

.el-icon-success {
  color: #85ce61;
}

::v-deep .item-group-label {
  color: #000;
}
</style>
