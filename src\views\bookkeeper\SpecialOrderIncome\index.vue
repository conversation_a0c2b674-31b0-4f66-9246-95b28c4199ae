<template>
    <container v-loading="pageLoading">
        <template #header>
        </template>

        <el-tabs v-model="activeName" style="height: 94%;">
            <el-tab-pane label="特殊单订单号" name="SpecialOrderImport" style="height: 100%;">
                <SpecialOrderImport :filter="filter" ref="SpecialOrderImport" />
            </el-tab-pane>
            <el-tab-pane label="特殊单收入支出" name="SpecialOrderAmount" style="height: 100%;">
                <SpecialOrderAmount :filter="filter" ref="SpecialOrderAmount" />
            </el-tab-pane>
        </el-tabs>
        

    </container>
</template>

<script>
import container from '@/components/my-container/nofooter'
import SpecialOrderImport from '@/views/bookkeeper/SpecialOrderIncome/SpecialOrderImport'
import SpecialOrderAmount from '@/views/bookkeeper/SpecialOrderIncome/SpecialOrderAmount'

export default {
    name: 'SpecialOrderImportindex',
    components: { container, SpecialOrderImport ,SpecialOrderAmount},
    data() {
        return {
            activeName: 'SpecialOrderImport',
        }
    },
    mounted() { },
    beforeUpdate() {
    },
    async mounted() {
    },
    methods: {

    }
}
</script>
