<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="采购推新报表" name="tab0" style="height: 100%;">
                <HotSaleBrandPushNewReport0 ref="HotSaleBrandPushNewReport0" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="选品运营" name="tab1" style="height: 100%;" lazy>
                <HotSaleBrandPushNewChoose ref="HotSaleBrandPushNewChoose" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="产品类目" name="tab2" style="height: 100%;" lazy>
                <HotSaleBrandPushNewChoose2 ref="HotSaleBrandPushNewChoose2" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="销售汇总明细" name="tab4" style="height: 100%;" lazy>
                <HotSaleBrandPushNewSale5 ref="HotSaleBrandPushNewSale5" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="选品销量(实际)" name="tab3" style="height: 100%;" lazy>
                <HotSaleBrandPushNewSale0 ref="HotSaleBrandPushNewSale0" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="选品销量(发生)" name="tab3_2" style="height: 100%;" lazy>
                <HotSaleBrandPushNewSale0_2 ref="HotSaleBrandPushNewSale0_2" style="height: 100%;" />
            </el-tab-pane>
            <!-- <el-tab-pane label="万单产品" name="tab4" style="height: 100%;" lazy>
                <HotSaleBrandPushNewWan0 ref="HotSaleBrandPushNewWan0" style="height: 100%;" />
            </el-tab-pane> -->

            <el-tab-pane label="运营建编码" name="tab5" style="height: 100%;" lazy>
                <HotSaleBuildDocReport0 ref="HotSaleBuildDocReport0" style="height: 100%;" />
            </el-tab-pane>

            <el-tab-pane label="库存资金明细" name="tab6" style="height: 100%;" lazy>
                <HotSaleBrandPushNewKczjDtl ref="HotSaleBrandPushNewKczjDtl" style="height: 100%;" />
            </el-tab-pane>

            <el-tab-pane label="库存资金图表" name="tab7" style="height: 100%;" lazy>
                <HotSaleBrandPushNewKczjChart ref="HotSaleBrandPushNewKczjChart" style="height: 100%;" />
            </el-tab-pane>

        </el-tabs>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";

import HotSaleBrandPushNewReport0 from '@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewReport0';
import HotSaleBrandPushNewChoose from '@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewChoose0';
import HotSaleBrandPushNewChoose2 from '@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewChoose2';
import HotSaleBrandPushNewSale0 from '@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewSale0';
import HotSaleBrandPushNewSale0_2 from '@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewSale0_2';
import HotSaleBrandPushNewWan0 from '@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewWan0';
import HotSaleBuildDocReport0 from '@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBuildDocReport0';
import HotSaleBrandPushNewSale5 from '@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewSale5';
import HotSaleBrandPushNewKczjDtl from '@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewKczjDtl';
import HotSaleBrandPushNewKczjChart from '@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewKczjChart';

export default {
    name: "Users",
    components: {
        MyContainer,
        HotSaleBrandPushNewReport0, HotSaleBrandPushNewChoose, HotSaleBrandPushNewChoose2, HotSaleBrandPushNewSale0, HotSaleBrandPushNewSale0_2,
        HotSaleBrandPushNewWan0, HotSaleBuildDocReport0, HotSaleBrandPushNewSale5, HotSaleBrandPushNewKczjDtl,
        HotSaleBrandPushNewKczjChart
    },
    data() {
        return {
            that: this,
            pageLoading: '',
            activeName: 'tab0',
        };
    },
    mounted() {
    },
    methods: {
    },

};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
