<template>
    <!-- 员工设置 + 行内编辑 -->
    <my-container v-loading="pageLoading">
        <div style="height: 100%;">
            <vxetablebase :id="'accountmanage202301291318001'" :that='that' height="98%" border ref="xtable"
                class="drraggable-table" :isIndex='true' :hasexpand='false' :isSelectColumn='true' :tableData='tasklist'
                :tableCols='tableCols' tablekey="accountmanage" :loading="listLoading" :isBorder="false" :editconfig="{
                    trigger: 'manual', mode: 'row', showStatus: true, showIcon: false
                    , autoClear: false
                }" :validRules="validRules" @sortchange='sortchange'>
                <template slot='right'>
                    <vxe-column title="操作" width="160" fixed="right">
                        <template #default="{ row }">
                            <template v-if="row.editstatus && row.editstatus == 1">
                                <vxe-button size="mini" @click="saveRowEvent(row)">保存</vxe-button>
                                <vxe-button size="mini" @click="cancelRowEvent(row)">取消</vxe-button>
                            </template>
                            <template v-else>
                                <vxe-button size="mini" @click="editRowEvent(row)">编辑</vxe-button>

                                <vxe-button size="mini" @click="onDel(row)">删除</vxe-button>
                            </template>
                        </template>
                    </vxe-column>
                </template>
                <template slot='extentbtn'>
                    <div style="margin:10px 5px 5px 0;">
                        <span style="padding: 0;margin-right:2px;">
                            <!-- <el-input style="width:120px;margin-right:5px;" v-model="filter.userName" v-model.trim="filter.userName" :maxlength=100
                            placeholder="姓名" @keyup.enter.native="onSearch" clearable /> -->
                            <el-select v-model="filter.userName" style="width:120px;margin-right:2px;"
                                :remote-method="remoteMethod" filterable placeholder="请输入姓名" remote :clearable="true">
                                <el-option v-for="item in selname" :key="item.value" :label="item.userName"
                                    :value="item.userName">
                                </el-option>
                            </el-select>
                        </span>
                        <!-- <span style="padding: 0;margin-right:5px;">
                        <el-select style="width:8%;" v-model="filter.hascode" placeholder="erp账号" clearable>
                            <el-option label="有" value="1"></el-option>
                            <el-option label="无" value="0"></el-option>
                        </el-select>
                    </span> -->
                        <span style="padding: 0;margin-right:5px;">
                            <el-select style="width:150px;" v-model="filter.workPosition" :filterable="true"
                                :clearable="true" placeholder="工作岗位">
                                <el-option v-for="item in workPositionlist" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </span>
                        <el-select v-model="filter.workTypeStr" style="width:120px;margin-right:5px;" filterable
                            placeholder="工种" :clearable="true">
                            <el-option v-for="item in worktypelist" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                        <el-select v-model="filter.employeeStatus" style="width:120px;margin-right:5px;" filterable
                            placeholder="状态" :clearable="true">
                            <el-option label="正常" value="3"></el-option>
                            <!-- <el-option label="实习" value="2"></el-option> -->
                            <el-option label="离职" value="2"></el-option>
                        </el-select>
                        <el-select v-model="filter.classes" style="width:120px;margin-right:5px;" filterable
                            placeholder="班次" :clearable="true">
                            <el-option label="白班" value="0"></el-option>
                            <!-- <el-option label="实习" value="2"></el-option> -->
                            <el-option label="夜班" value="1"></el-option>
                        </el-select>
                        <!-- <span style="padding: 0;margin-right:5px;">
                        <el-select style="width:8%;" v-model="filter.commissionPosition" :filterable="true" :clearable="true" placeholder="提成岗位">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <el-option v-for="item in commissionPositionlist" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                    </span> -->
                        <!-- <span style="padding: 0;margin-right:5px;">
                        <el-select style="width:8%;" v-model="filter.companyName" placeholder="公司" clearable>
                            <el-option label="义乌" value="义乌"></el-option>
                            <el-option label="南昌" value="南昌"></el-option>
                        </el-select>
                    </span> -->
                        <span style="padding: 0;margin-right:5px;">
                            <el-button type="primary" @click="onSearch">查询</el-button>
                        </span>
                        <el-button-group><el-button type="primary" @click="onOpenAdd">新增人员</el-button>
                            <!-- <el-button type="primary" @click="saveOrder">保存排序</el-button> -->
                            <el-button type="primary" @click="onImportSyj">导入</el-button>
                            <el-button type="primary" @click="ClickdownloadTemplate">下载导入模板</el-button>
                            <el-button type="primary" @click="exportDataEvent">导出</el-button>
                        </el-button-group>
                    </div>
                </template>
            </vxetablebase>
        </div>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList"
                :page-size="100" />
        </template>
        <el-dialog :title="editformTitle" :visible.sync="editformdialog" width="60%" :close-on-click-modal="false"
            v-loading="editLoading" element-loading-text="拼命加载中" v-dialogDrag :append-to-body="true">
            <!-- 新增人员弹窗 -->
            <el-form :model="editform" ref="editform" label-width="120px" :rules="editformRules">
                <el-row>&nbsp;</el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item prop="dDUserId" label="姓名">
                            <!-- <el-select v-model="editform.userId" :filterable="true" :clearable="true"
                                @change="selUserInfoChange(editform.userId, index)">
                                <el-option v-for="item in userList" :label="item.label" :value="item.id" :key="item.id+''"></el-option>
                            </el-select> -->
                            <!-- <el-select filterable v-model="editform.dDUserId" placeholder="姓名" clearable @change="selUserInfoChange(editform.dDUserId, index)">
                                <el-option v-for="(uitem, i) in createUser" :key="i" :value="uitem.userId" :label="uitem.userName" />
                            </el-select> -->

                            <el-select v-model="editform.dDUserId" @change="selUserInfoChange(editform.dDUserId, index)"
                                style="width:120px;margin-right:2px;" :remote-method="remoteMethod" filterable
                                placeholder="请选择姓名" remote :clearable="true">
                                <el-option v-for="item in selname" :key="item.value" :label="item.userName"
                                    :value="item.userId">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item prop="companyName" label="公司">
                            <el-select v-model="editform.companyName" :clearable="true" :collapse-tags="true" filterable>
                                <el-option label="义乌" value="义乌"></el-option>
                                <el-option label="南昌" value="南昌"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="workPosition" label="工作岗位">
                            <el-select v-model="editform.workPosition" :filterable="true" :clearable="true">
                                <el-option v-for="item in workPositionlist" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="workTypeStr" label="工种">
                            <!-- <el-select v-model="editform.workType" :filterable="true" :clearable="true">
                                <el-option v-for="item in commissionPositionlist" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select> -->
                            <el-input v-model="editform.workTypeStr" placeholder="请输入内容" :clearable="true" :maxlength="50"></el-input>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="6">
                        <el-form-item prop="workTeamType" label="工作组">
                            <el-select v-model="editform.workTeamType" :filterable="true" :clearable="true">
                                <el-option v-for="item in workteamPositionlist" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col> -->
                </el-row>

                <el-row>
                    <el-col :span="6">
                        <el-form-item prop="classes" label="班次">
                            <!-- <el-input-number :clearable="true" v-model.trim="editform.classes" :min="0" :max="1000000"
                                :controls="false" :precision="2"></el-input-number> -->
                            <el-select v-model="editform.classes" style="width:120px;margin-right:5px;" filterable
                                placeholder="班次" :clearable="true">
                                <el-option label="白班" value="0"></el-option>
                                <el-option label="夜班" value="1"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="basepay" label="底薪">
                            <el-input-number :clearable="true" v-model.trim="editform.jBasepay" :min="0" :max="1000000"
                                :controls="false" :precision="2"></el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="achievement" label="绩效">
                            <el-input-number :clearable="true" v-model.trim="editform.jAchievement" :min="0" :max="1000000"
                                :controls="false" :precision="2"></el-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>

                    <!-- <el-col :span="6">
                        <el-form-item prop="subsidy" label="补贴">
                            <el-input-number :clearable="true" v-model.trim="editform.subsidy" :min="0" :max="1000000"
                                :controls="false" :precision="2"></el-input-number>

                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="overTimePay" label="加班费">
                            <el-input-number :clearable="true" v-model.trim="editform.overTimePay" :min="0" :max="1000000"
                                :controls="false" :precision="2"></el-input-number>

                        </el-form-item>
                    </el-col> -->
                    <el-col :span="6">
                        <el-form-item prop="modephone" label="手机号">
                            <el-input :clearable="true" :maxlength="20" v-model.trim="editform.modephone"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="nowAddress" label="现住址">
                            <el-input :clearable="true" :maxlength="200" v-model.trim="editform.nowAddress"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="6">
                        <el-form-item prop="homeAddress" label="家庭住址">
                            <el-input :clearable="true" :maxlength="200" v-model.trim="editform.homeAddress"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="bank" label="银行">
                            <el-input :clearable="true" :maxlength="200" v-model.trim="editform.bank"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="bankCode" label="银行卡号">
                            <el-input :clearable="true" :maxlength="200" v-model.trim="editform.bankCode"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="editformdialog = false">关 闭</el-button>
                    <my-confirm-button type="submit" @click="onSubmit" />
                </span>
            </template>
        </el-dialog>
        <!--薪资模板导入-->
        <el-dialog title="员工导入" v-dialogDrag :visible.sync="dialogVisibleSyj" width="30%" :before-close="onImportClose">
            <span>
                <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                    :file-list="fileList" action accept=".xlsx" :http-request="uploadFile2">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                        @click="onSubmitupload2">上传</my-confirm-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="onImportClose()">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import { getRecordUser, getAllUser } from '@/api/inventory/packagesprocess';//包装加工
import vxetablebase from "@/components/VxeTable/vxetablemedianew.vue";
import MyContainer from "@/components/my-container";
import { formatTime, listToTree } from "@/utils";
import cesTable from "@/components/Table/tableforvedio.vue";
import accountsWorkPostionManage from '@/views/media/shooting/adjustAccounts/accountsWorkPostionManage';
import { getErpUserInfoViewforshoot, getWorkPostListAsync } from '@/api/media/mediashare';
// import { addOrUpdatePersonnelPositionAsync, getPersonnelPositionAsync, delPersonnelPositionAsync, sortPersonnelPosition, setPersonnelIsHsAsync, importShootingSalaryAsync } from '@/api/media/shootingset'
import {
    addOrUpdatePersonnelPositionAsync, getPersonnelPositionAsync, sortPersonnelPosition, delPersonnelPosition, setPersonnelIsHsAsync,
    getPackagesSetData, importPackagesPersonnel,getWorkTypeList
} from '@/api/inventory/packagesSetProcessing.js';
import MyConfirmButton from "@/components/my-confirm-button";
import Sortable from 'sortablejs'

export default {
    components: { MyContainer, cesTable, MyConfirmButton, accountsWorkPostionManage, vxetablebase },
    props: ['workTeamType'],
    data() {
        return {
            that: this,
            pageLoading: false,
            positionOrder: false,
            selname: [],
            summaryarry: [],
            createUser: [],
            userList: [],
            tasklist: [],
            fileList: [],
            workPositionlist: [],
            worktypelist:[],
            // commissionPositionlist: [],
            commissionPositionlist: [
                { label: '计时', value: 0 }, { label: '计件', value: 1 }, { label: '计时+计件', value: 2 }
            ],
            workteamPositionlist: [
                { label: '加工组', value: 0 }, { label: '包装组', value: 1 }
            ],
            retdata: [],
            sels: [], // 列表选中列
            //tableCols: tableCols,
            listLoading: false,
            //人员编辑模块
            editLoading: false,
            editformdialog: false,
            dialogVisibleSyj: false,
            editformTitle: null,
            editform: {
                openType: 1,
                positionId: 0,
                dDUserId: null,
                userName: null,
                companyName: null,
                workPosition: null,
                commissionPosition: null,
                commissionRate: 100,
                isCyCommission: 0,
                fpTaskAutoRate: 100,
                isCyFp: 1,
                classtype: null,
                basepay: null,
                achievement: null,
                subsidy: null,
                overTimePay: null,
                modephone: null
            },
            total: 0,
            pager: { OrderBy: "orderNum", IsAsc: true },
            filter: {
            },
            editformRules: {
                dDUserId: [{ required: true, message: '请选择', trigger: 'blur' }],
                commissionRate: [{ required: true, message: '请填写', trigger: 'blur' }],
                companyName: [{ required: true, message: '请选择', trigger: 'blur' }],
                commissionPosition: [{ required: true, message: '请填写', trigger: 'blur' }],
                workPosition: [{ required: true, message: '请填写', trigger: 'blur' }],
                workTypeStr: [{ required: true, message: '请填写', trigger: 'blur' }],
                // workTeamType: [{ required: true, message: '请填写', trigger: 'blur' }],
                classes: [{ required: true, message: '请填写', trigger: 'blur' }],
                modephone: [{ required: true, message: '请填写', trigger: 'blur' }],
            },
            //表格编辑校验
            validRules: {
                companyName: [
                    { required: true, message: '必填' }
                ],
                workPosition: [
                    { required: true, message: '必填' }
                ],
                commissionPosition: [
                    { required: true, message: '必填' }
                ],
                workTypeStr: [
                    { required: true, message: '必填' }
                ],
                workTeamType: [
                    { required: true, message: '必填' }
                ],
                classes: [
                    { required: true, message: '必填' }
                ],
            },
        };
    },
    computed: {
        tableCols() {
            let tableCols = [
                { istrue: true, type: '', prop: 'userName', label: '姓名', width: '70', fixed: "left" },
                { istrue: true, type: 'editselect', prop: 'companyName', label: '公司', fixed: "left", width: '80', options: [{ label: '南昌', value: '南昌' }, { label: '义乌', value: '义乌' }] },
                { istrue: true, type: 'editselect', prop: 'workPosition', label: '工作岗位', width: '85', options: this.workPositionlist },
                // { istrue: true, type: 'editselect', prop: 'commissionPosition', label: '提成岗位', width: '85', options: this.commissionPositionlist },
                { istrue: true, type: '', prop: 'userId', label: 'erp账号', width: '65', formatter: (row) => { return row.userId < 0 ? '无' : '' } },
                // { istrue: true, type: 'editText', maxlength: '50', prop: 'classtype', label: '类型', width: '80' },
                { istrue: true, type: 'editNumber', min: '0', max: '1000000', prop: 'jBasepay', label: '底薪', width: '80', permission: 'api:Inventory:PackagesProcessing:BasicSalary' },
                { istrue: true, type: 'editNumber', min: '0', max: '1000000', prop: 'jAchievement', label: '绩效', width: '70', permission: 'api:Inventory:PackagesProcessing:Performance' },


                { istrue: true, type: 'editselect', prop: 'classes', label: '班次', width: '70', options: [{ label: '白班', value: 0 }, { label: '夜班', value: 1 }] },
                { istrue: true, type: 'editText', prop: 'workTypeStr', label: '工种', width: '70' },
                { istrue: true, type: 'editselect', prop: 'workTeamType', label: '工作组', width: '70', options: [{ label: '加工组', value: 0 }, { label: '入库组', value: 2 }, { label: '文员组', value: 3 }, { label: '其他', value: 4 }] },
                // { istrue: true, type: 'editNumber', min: '0', max: '1000000', prop: 'pieceFee', label: '计件提成', width: '70' },
                { istrue: true, type: 'editText', maxlength: '20', prop: 'modephone', label: '手机号', width: '100' },
                { istrue: true, type: 'editText', maxlength: '200', prop: 'nowAddress', label: '现住址', width: '150' },
                { istrue: true, type: 'editText', maxlength: '200', prop: 'homeAddress', label: '家庭住址', width: '150' },
                { istrue: true, type: 'editText', maxlength: '200', prop: 'bank', label: '银行', width: '80' },
                { istrue: true, type: 'editText', maxlength: '200', prop: 'bankCode', label: '银行卡号', width: '150' },
                { istrue: true, type: '', maxlength: '200', prop: 'employeeStatusStr', label: '状态', width: '75', type: 'tag', tagstatus: (row) => { return row.employeeStatus == 3 ? 'success' : row.employeeStatus == 2 ? 'warning' : 'info' }, },

                { istrue: true, type: 'editDate', prop: 'joinedDate', label: '入职日期', width: '115', formatter: (row) => { return row.joinedDate == null ? '' : formatTime(row.joinedDate || '', 'YYYY-MM-DD') } },
                { istrue: true, type: 'editDate', prop: 'leaveDate', label: '离职日期', width: '115', formatter: (row) => { return row.leaveDate == null ? '' : formatTime(row.leaveDate || '', 'YYYY-MM-DD') } },
                { istrue: true, type: '', prop: 'yggl', label: '工龄', width: '145' },
            ];
            return tableCols;
        }
    },
    async mounted() {
        // await this.getUserList();
        await this.getWorkPostList();
        await this.onSearch();
        this.ShowHideonSearch();
        // this.rowDrop();
    },
    methods: {
      //默认列隐藏
      async ShowHideonSearch() {
          this.listLoading = true;
          var arrlist = [];
          arrlist = [ 'homeAddress', 'bank', 'bankCode' ];
          await this.$refs.xtable.ShowHidenColums(arrlist);
          this.listLoading = false;
        },
        async remoteMethod(query) {
            if (query !== '') {
                this.loading = true;

                const res = await getAllUser();
                if (!res?.success) {
                    return
                }
                this.loading = false;
                this.selname = res.data.filter(item => {
                    return item.userName.toLowerCase()
                        .indexOf(query.toLowerCase()) > -1;
                });
            } else {
                this.selname = [];
            }
        },
        // async getcrepeople(){
        //     const res = await getRecordUser();
        //     if (!res?.success) {
        //         return
        //     }
        //     this.createUser=res.data;
        // },

        //编辑保存
        async saveRowEvent(row) {
            const $table = this.$refs.xtable;
            if (! await $table.validate())
                return;
            const joinedDate = +new Date(row.joinedDate);
            const leaveDate = +new Date(row.leaveDate);
            if (joinedDate && leaveDate) {
                if (joinedDate > leaveDate) {
                    this.$message({ message: '保存失败，离职日期不能小于入职日期!', type: "fail" });
                    return
                }
            }
            if ($table.isUpdateByRow(row)) {
                row.openType = 1;
                var res = await addOrUpdatePersonnelPositionAsync(row);
                if (res?.success) {
                    this.$message({ message: '保存成功!', type: "success" });
                }
                this.getTaskList();
                // $table.reloadRow(row);
                // $table.clearActivedRowEvent(row);
            } else {
                this.$message({ message: '数据未发生变化!', type: "info" });
                $table.clearActivedRowEvent(row);
            }
        },
        //开启编辑
        editRowEvent(row) {
            this.$refs.xtable.editRowEvent(row);
        },
        //取消编辑
        cancelRowEvent(row) {
            this.$refs.xtable.cancelRowEvent(row);
        },
        //导出
        exportDataEvent() {
            this.$refs.xtable.exportData("员工信息");

        },
        onImportClose() {
            this.dialogVisibleSyj = false;
            this.fileList = [];
        },
        onImportSyj() {
            this.dialogVisibleSyj = true;
        },
        ClickdownloadTemplate() {
            window.open("../../static/excel/package/包装加工员工导入模板.xlsx", "_self");
        },
        async onSubmitupload2() {
            this.$refs.upload2.submit()
        },
        async uploadFile2(item) {
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("actiontype", 0);
            var res = await importPackagesPersonnel(form);
            this.fileList = [];
            if (res?.success) {
                if (res.data == null)
                    this.$message({ message: '上传成功!', type: "success" });
                else
                    this.$message({ message: res.data, type: "error" });
                this.onSearch();
                this.dialogVisibleSyj = false;
            }
        },
        //保存排序
        async saveOrder() {
            this.listLoading = true;
            var res = await sortPersonnelPosition({ orderType: 2, entityList: this.tasklist });
            if (res?.success)
                this.$message({ message: '操作成功', type: 'success' });
            this.listLoading = false;
        },
        //下拉排序
        rowDrop() {
            const tbody = document.querySelector('.drraggable-table .vxe-table--body-wrapper tbody')
            const _this = this
            Sortable.create(tbody, {
                onEnd({ newIndex, oldIndex }) {
                    if (newIndex == oldIndex) return
                    _this.tasklist.splice(
                        newIndex,
                        0,
                        _this.tasklist.splice(oldIndex, 1)[0]
                    )
                    var newArray = _this.tasklist.slice(0)
                    _this.tasklist = []
                    _this.$nextTick(function () {
                        _this.tasklist = newArray
                    })
                }
            })
        },
        //删除数据
        async onDel(row) {
            this.$confirm("将进行删除操作，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await delPersonnelPosition({ positionId: row.positionId });
                if (!res?.success) { return; }
                this.$message({ message: '操作成功', type: 'success' });
                await this.onSearch()
            });
        },
        //是否核算
        async CyCommission(row) {
            var res = await setPersonnelIsHsAsync({ positionId: row.positionId, isCyCommission: row.isCyCommissionbool ? 1 : 0 });
            if (!res?.success) { return; }
            this.$message({ message: this.$t('保存成功'), type: 'success' });
        },
        //下拉切换用户
        selUserInfoChange(val, index) {
            let resultArr = this.createUser.find((item) => {
                return item.userId == val;
            });
            this.editform.userName = resultArr.userName;
        },
        //获取岗位下拉数据
        async getWorkPostList() {
            var res = await getPackagesSetData({ setType: 0 });
            if (res) {
                this.workPositionlist = res.data.map(item => ({ value: item.setId, label: item.sceneCode }));
                // this.commissionPositionlist = res.commissionPositionlist.map(item => ({ value: item.id, label: item.label }));
            }
            var resWt = await getWorkTypeList();
            if(resWt?.success)
            {
                this.worktypelist = resWt.data.map(item => ({ value: item, label: item }));
            }
        },
        //获取用户信息
        async getUserList() {
            this.userList = await getErpUserInfoViewforshoot();
            return this.userList;
        },
        //打开新增窗口
        async onOpenAdd() {
            // this.getcrepeople();
            this.initform();
            this.editform = {};
            this.editformdialog = true;
            // await getWorkPostListAsync();
        },
        initform() {
            this.editform.positionId = 0;
            this.editform.userId = null;
            this.editform.userName = null;
            this.editform.companyName = null;
            this.editform.workPosition = null;
            this.editform.commissionPosition = null;
            this.editform.commissionRate = 100;
            this.editform.isCyCommission = 0;
            this.editform.fpTaskAutoRate = 100;
            this.editform.isCyFp = 1;
            this.editform.classtype = null;
            this.editform.basepay = null;
            this.editform.achievement = null;
            this.editform.subsidy = null;
            this.editform.overTimePay = null;
            this.editform.modephone = null;
        },
        //打开新增窗口
        async onEditAdd(row) {

            this.editformdialog = true;
            this.editLoading = true;
            await getWorkPostListAsync();
            this.editform = row;
            this.editform.userId = row.userId.toString();
            this.editformTitle = "编辑人员";
            this.editLoading = false;
        },
        //提交保存时验证
        onSubmitValidate: function () {
            let isValid = true;
            this.$refs.editform.validate(valid => {
                isValid = valid
            })
            return isValid;
        },
        async onSubmit() {
            if (!this.onSubmitValidate()) {
                return;
            }
            this.editform.openType = 1;
            this.editform.workTeamType = this.workTeamType
            const para = _.cloneDeep(this.editform);
            const selectedUser = this.selname.find(item => item.userId == this.editform.dDUserId);
            if (selectedUser) {
            para.userName = selectedUser.userName;
            }
            this.editLoading = true;
            var res = await addOrUpdatePersonnelPositionAsync(para);
            this.editLoading = false;
            if (!res?.success) { return; }
            this.$message({ message: this.$t('保存成功'), type: 'success' });
            this.onSearch();
            this.editformdialog = false;
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            this.getTaskList();
        },
        async getTaskList() {
            var pager = this.$refs.pager.getPager();
            let params = {
                workTeamType: this.workTeamType,
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            this.listLoading = true;
            let res = await getPersonnelPositionAsync(params);
            this.listLoading = false;
            this.total = res.data.total;
            res.data.list.forEach((item, index) => {
                item.editstatus = 0;
            });
            this.tasklist = res.data.list;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
    },
};
</script>

