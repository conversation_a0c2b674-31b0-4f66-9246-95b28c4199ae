<template>
  <my-container>
    <template #header>
      <div class="top">
        <el-date-picker v-if="boardType == 1" style="width: 280px" v-model="timeRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" 
          range-separator="至" start-placeholder="起始日期" end-placeholder="结束日期" :picker-options="pickerOptions" 
          @change="changeTime" unlink-panels>
        </el-date-picker>
        <el-select v-model="filter.enmPlatform" placeholder="选择平台" multiple clearable collapse-tags filterable style="width:190px;" >
          <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
        <el-select v-model="filter.dataType" placeholder="选择数据类型" multiple clearable collapse-tags filterable style="width:200px;" >
          <el-option v-for="item in dataTypeList" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>
        <button style="padding: 0; border: none;">
          <el-input v-model="filter.owner" v-model.trim="filter.owner" placeholder="输入单个责任人" clearable></el-input>
        </button>
        <el-button type="primary" icon="el-icon-search" @click="onSearch">查询</el-button>
        <el-button v-if="boardType == 2" type="primary" icon="el-icon-plus" @click="onAdd">新增</el-button>
      </div>
      
      <div style="margin-top: 10px;">
        <el-button-group>
          <el-radio-group v-model="boardType" size="small" @input="radioClick">
            <el-radio-button :label="1" >任务进度</el-radio-button>
            <el-radio-button :label="2" >任务设置</el-radio-button>
          </el-radio-group>
        </el-button-group>
      </div>
    </template>
    <vxetablebase v-show="boardType == 1" :id="'DayReportBeforeDataBoardWorkProgress'" ref="table" :that='that' :isIndex='true' 
      @sortchange='sortchangeWorkProgress' :tableData='tableData' :tableCols='tableColsWorkProgress' :isSelectColumn="false" 
      style="width:100%;margin: 0" v-loading="listLoading" :showsummary='false' :hasSeq="false" @cellClick="cellClick">
    </vxetablebase>

    <vxetablebase v-show="boardType == 2" :id="'DayReportBeforeDataBoardBasicSetting'" ref="table2" :that='that' :isIndex='true' 
      @sortchange='sortchangeBasicSetting' :tableData='tableData' :tableCols='tableColsBasicSetting' :isSelectColumn="false" 
      style="width: 100%;margin: 0" v-loading="listLoading" :height="'100%'" :hasSeq="false">
    </vxetablebase>

    <!--分页-->
    <template #footer>
          <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <!-- 任务设置 -->
    <el-dialog :title="title" :visible.sync="setVisible" width="440px" height="40%" @closeDialog="closeSetDialog" v-dialogDrag @close="setCancle">
      <el-form ref="formBasicSetting" :model="formBasicSetting" label-width="150px" :rules="formBasicSettingRules" >
        <el-form-item label="平  台：" prop="enmPlatform">
          <el-select v-model="formBasicSetting.enmPlatform" clearable filterable style="width:250px;" >
            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据类型：" prop="dataType">
          <el-input v-model="formBasicSetting.dataType" v-model.trim="formBasicSetting.dataType" type="text" maxlength="50" show-word-limit ></el-input>
        </el-form-item>
        <el-form-item label="检查维度：" prop="checkDimension">
          <el-select v-model="formBasicSetting.checkDimension" clearable filterable style="width:250px;" >
            <el-option v-for="item in checkDimensionlist" :key="item" :label="item" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="应开始时间：" prop="planStartTime">
          <el-time-picker v-model="formBasicSetting.planStartTime" format="HH:mm:ss" value-format="HH:mm:ss" placeholder="应开始时间"></el-time-picker>
        </el-form-item>
        <el-form-item label="应结束时间：" prop="planEndTime">
          <el-time-picker v-model="formBasicSetting.planEndTime" format="HH:mm:ss" value-format="HH:mm:ss" placeholder="应结束时间"></el-time-picker>
        </el-form-item>
        <el-form-item label="负责人：" prop="owner">
          <el-input v-model="formBasicSetting.owner" v-model.trim="formBasicSetting.owner" placeholder="多个负责人请用英文逗号隔开"></el-input>
        </el-form-item>
        <el-form-item label="异常通知对应人：" prop="errorNoticer">
          <el-input v-model="formBasicSetting.errorNoticer" v-model.trim="formBasicSetting.errorNoticer" placeholder="多个异常通知对应人请用英文逗号隔开"></el-input>
        </el-form-item>
      </el-form>
      <div style="text-align: center;">
        <el-button type="primary" @click="setCancle">取消</el-button>
        <el-button type="primary" @click="editSubmit">确认</el-button>
      </div>
    </el-dialog>

    <!-- 趋势图 -->
    <el-dialog :title="dialogVisible.title" :visible.sync="dialogVisible.visible" width="80%" 
      :close-on-click-model="false" v-dialogDrag>
      <div>
        <span>
            <buschar v-if="dialogVisible.visible" :analysisData="dialogVisible.data"></buschar>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible.visible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 异常反馈 -->
    <el-dialog title="异常反馈" :visible.sync="setErrorVisible" width="440px" height="40%" @closeDialog="closeSetErrorDialog" v-dialogDrag @close="setErrorCancle">
      <el-form ref="errorSetting" :model="errorSetting" label-width="100px" >
        <el-form-item label="异常反馈：" prop="errorBackIdea">
          <el-input v-model="errorSetting.errorBackIdea" v-model.trim="errorSetting.errorBackIdea" type="textarea" :rows="10" maxlength="500" show-word-limit placeholder="请输入异常反馈">
          </el-input>
        </el-form-item>
      </el-form>
      <div style="text-align: center;">
        <el-button type="primary" @click="setErrorCancle">取消</el-button>
        <el-button type="primary" @click="editErrorSubmit">确认</el-button>
      </div>
    </el-dialog>

  </my-container>
</template>
<script>
  import MyContainer from "@/components/my-container";
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import inputYunhan from "@/components/Comm/inputYunhan";
  import dayjs from "dayjs"
  import buschar from "@/components/Bus/buschar";
  import { formatTime } from "@/utils";
  import { formatPlatform, platformlist } from "@/utils/tools";
  import { getDayReportBeforeDataBoardWorkProgress, getDayReportBeforeDataBoardBasicSetting, getDayReportBeforeDataBoardDataType, addDayReportBeforeDataBoardBasicSetting, 
    deleteDayReportBeforeDataBoardBasicSetting, getDayReportBeforeDataBoardWorkProgressMap, editErrorReason } from "@/api/bookkeeper/DayReportBeforeDataBoard.js";

  const tableColsWorkProgress =[
    { sortable: 'custom', istrue: true, width: '85', align: 'center', label: '日期', prop: 'workDate', 
    formatter: (row) => { return row.workDate ? dayjs(row.workDate).format('YYYY-MM-DD') : ""} },
    { sortable: 'custom', istrue: true, width: '85', align: 'center', label: '平台', prop: 'enmPlatform', formatter: (row) => formatPlatform(row.enmPlatform) },
    { istrue: true, width: '170', align: 'center', label: '数据类型', prop: 'dataType' },
    { istrue: true, width: '110', align: 'center', label: '检查维度', prop: 'checkDimension' },
    { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '任务进度数值', prop: 'workProgressNum' },
    { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '任务进度总数值', prop: 'workProgressTotalNum' },
    { istrue: true, width: '115', align: 'center', label: '近7天趋势', prop: 'workProgressDtoChartData', chartProp:'workProgressDtoChartData', type: 'echarts' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '当前进度', prop: 'workProgressNow',type: 'progress' },
    { sortable: 'custom', istrue: true, width: '190', align: 'center', label: '应开始时间/实际开始时间', prop: 'startTime', tipmesg: "按照实际开始时间排序" },
    { sortable: 'custom', istrue: true, width: '190', align: 'center', label: '应结束时间/实际结束时间', prop: 'endTime', tipmesg: "按照实际结束时间排序" },
    { istrue: true, width: '100', align: 'center', label: '负责人', prop: 'owner' },
    { istrue: true, width: '80', align: 'center', label: '是否正常', prop: 'isNormal' },
    { istrue: true, width: '160', align: 'center', label: '异常反馈', prop: 'errorBackIdea' },
    {
      istrue: true, width: '70', align: 'center', type: 'button', label: '操作', btnList: [
        { label: "异常反馈", handle: (that, row) => that.onEditWorkProgress(row) },
      ]
    }
  ];
  const tableColsBasicSetting =[
    { istrue: true, width: '180', align: 'center', label: '平台', prop: 'enmPlatform', formatter: (row) => formatPlatform(row.enmPlatform) },
    { istrue: true, width: '180', align: 'center', label: '数据类型', prop: 'dataType' },
    { istrue: true, width: '180', align: 'center', label: '检查维度', prop: 'checkDimension' },
    { istrue: true, width: '185', align: 'center', label: '应开始时间', prop: 'planStartTime', 
    formatter: (row) => { return row.planStartTime ? dayjs(row.planStartTime).format('HH:mm:ss') : ""} },
    { istrue: true, width: '185', align: 'center', label: '应结束时间', prop: 'planEndTime', 
    formatter: (row) => { return row.planEndTime ? dayjs(row.planEndTime).format('HH:mm:ss') : "" } },
    { istrue: true, width: '220', align: 'center', label: '负责人', prop: 'owner' },
    { istrue: true, width: '220', align: 'center', label: '异常通知对应人', prop: 'errorNoticer' },
    {
      istrue: true, width: '185', align: 'center', type: 'button', label: '操作', btnList: [
        { label: "删除", handle: (that, row) => that.onDelete(row) }, 
        { label: "编辑", handle: (that, row) => that.onEdit(row) },
      ]
    }
  ];

  export default {
    name: 'DayReportBeforeDataBoard',
    components: { MyContainer, vxetablebase, inputYunhan, buschar },
    data() {
      return {
        that: this,
        listLoading: false,
        setVisible: false,//新增任务设置弹窗
        setErrorVisible: false,//异常反馈弹窗
        addVisible: false,//新增
        updateVisible: false,//编辑
        workProgress: [],//任务进度
        basicSetting: [],//任务设置
        dataType: [],//数据类型
        workProgressMap: {},//任务进度map
        workProgressMapKey: "",//任务进度map key
        timeRange: [],
        platformlist: platformlist,//平台
        dataTypeList: [],//数据类型
        boardType: 1,//日报前置数据看板类型
        filter: {
          currentPage: 1,
          pageSize: 50,
          orderBy: null,
          isAsc: false,
          //过滤条件
          startDate: null,
          endDate: null,
          enmPlatform: [],//平台
          dataType: [],//数据类型
          owner: "",//责任人
        },
        //趋势图使用的过滤条件,不直接使用filter
        chartFilter: {
          currentPage: 1,
          pageSize: 50,
          orderBy: null,
          isAsc: false,
          //过滤条件li
          startDate: null,
          endDate: null,
          enmPlatform: null,//平台
          dataType: null,//数据类型
          owner: "",//责任人
        },
        dialogVisible: { visible: false, title: "", data: [] },
        //任务设置表单
        formBasicSetting: {
          mesgID: null,
          enmPlatform: null,
          platform: "",
          dataType: "",
          checkDimension: "",
          planStartTime: null,
          planEndTime: null,
          owner: "",
          ownerID: null,
          errorNoticer: "",
          errorNoticerID: null,
        },
        formBasicSettingRules: {
          enmPlatform: [
            { required: true, message: '请选择平台', trigger: 'change' },
          ],
          dataType: [
            { required: true, message: '请选择数据类型', trigger: 'change' },
          ],
          checkDimension: [
            { required: true, message: '请选择检查维度', trigger: 'change' },
          ],
          planStartTime: [
            { required: true, message: '请选择应开始时间', trigger: 'change' },
          ],
          planEndTime: [
            { required: true, message: '请选择应结束时间', trigger: 'change' },
          ],
          owner: [
            { required: true, message: '请选择负责人', trigger: 'blur' },
          ],
          errorNoticer: [
            { required: true, message: '请选择异常通知对应人', trigger: 'blur' },
          ],
        },
        title: '',
        tableDataWorkProgress: [],
        tableColsWorkProgress: tableColsWorkProgress,
        tableData: [],
        total: 0,
        tableDataBasicSetting: [],
        tableColsBasicSetting: tableColsBasicSetting,
        pickerOptions: {
          shortcuts: [{
            text: '前一天',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
              end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
              picker.$emit('pick', [start, end]);
              }
            }, {
            text: '近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              end.setTime(end.getTime());
              picker.$emit('pick', [start, end]);
            }
            }, {
            text: '近一个月',
            onClick(picker) {
              const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
              const date2 = new Date(); date2.setDate(date2.getDate());
              picker.$emit('pick', [date1, date2]);
            }
          }]
        },
        checkDimensionlist: ['店铺数', '银行数'],//数据类型选择器
        errorSetting: {
          mesgID: null,
          errorBackIdea: '',
        },// 异常反馈
      }
    },
    async mounted() {
      localStorage.setItem("boardType", 1)
      this.init();
      this.getList();
    },
    methods: {
      async init() {
        //默认当天日期
        // const startDate = formatTime(dayjs().subtract(0, 'day'), "YYYY-MM-DD HH:mm:ss");
        // const endDate = formatTime(dayjs().subtract(0, 'day'), "YYYY-MM-DD HH:mm:ss");
        // this.timeRange = [startDate, endDate]
        let end = new Date();
        let start = new Date();
        start.setTime(start.getTime());
        end.setTime(end.getTime());
        this.timeRange = [start, end];
        this.filter.startDate = start;
        this.filter.endDate = end;

        //获取数据类型
        const dataType = await getDayReportBeforeDataBoardDataType();
        this.dataTypeList = dataType.data.list;
      },
      changeTime(e) {
        this.filter.startDate = e? e[0] : null;
        this.filter.endDate = e ? e[1] : null;
      },
      sortchangeWorkProgress(column) {
        if (!column.order) { 
          this.pagerWorkProgress = {};
        }
        else {
          this.pagerWorkProgress = {
            orderBy: column.prop,
            isAsc: column.order.indexOf("descending") == -1 ? true : false,
          };
          this.filter.orderBy = column.prop;
          this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false;
          this.onSearch();
        }
      },
      sortchangeBasicSetting(column) {
        if (!column.order) { 
          this.pagerBasicSetting = {};
        }
        else {
          this.pagerBasicSetting = {
            orderBy: column.prop,
            isAsc: column.order.indexOf("descending") == -1 ? true : false,
          };
        this.onSearch();
        }
      },
      // 列表趋势图点击
      cellClick(prms) {
        if (prms?.column?.field && prms.column.field == 'workProgressDtoChartData') {
          let row = prms.row;
          this.showchart(row);
        }
      },
      // 每页数量改变
      Sizechange(val) {
        this.listLoading = true;
        this.filter.currentPage = 1;
        this.filter.pageSize = val;
        this.getList();
        this.listLoading = false;
      },
      // 当前页改变
      Pagechange(val) {
        this.filter.currentPage = val;
        this.getList();
      },
      callback(val) {
        this.filter.owner = val;
      },
      callback2(val) {
        var value = val.split(",，-:：;；'‘’.。/、\\");
        var temp = value.join(',');
        console.log(temp);
        this.formBasicSetting.owner = temp;
      },
      callback3(val) {
        var value = val.split(",，-:：;；'‘’.。/、\\");
        var temp = value.join(',');
        console.log(temp);
        this.formBasicSetting.errorNoticer = temp;
      },
      // 查询
      onSearch() {
        //点击查询时才将页数重置为1
        this.filter.currentPage = 1;
        this.$refs.pager.setPage(1);
        this.getList();
      },
      async getList() {
        this.listLoading = true;
        let data, success;
        if (this.boardType == 1) {
          ({ data, success } = await getDayReportBeforeDataBoardWorkProgress(this.filter));
        } else if (this.boardType == 2) {
          ({ data, success } = await getDayReportBeforeDataBoardBasicSetting(this.filter));
        }
        this.listLoading = false;
        if (success) {
          if (this.boardType == 1) {
            this.tableDataWorkProgress = data.list;
            this.tableData = this.tableDataWorkProgress;
            this.tableData.forEach((item) => {
              let PlanStartTime = item.planStartTime ? dayjs(item.planStartTime).format("HH:mm:ss") : "无";
              let PlanEndTime = item.planEndTime ? dayjs(item.planEndTime).format("HH:mm:ss") : "";
              let ActualStartTime = item.actualStartTime ? dayjs(item.actualStartTime).format("HH:mm:ss") : "无";
              let ActualEndTime = item.actualEndTime ? dayjs(item.actualEndTime).format("HH:mm:ss") : "";
              item.startTime = PlanStartTime + "/" + ActualStartTime;
              item.endTime = PlanEndTime + "/" + ActualEndTime;
            })
            this.total = data.total;
          } else if (this.boardType == 2) {
            this.tableDataBasicSetting = data.list;
            this.tableData = this.tableDataBasicSetting;
            this.total = data.total;
          }

          this.total = data.total;
          setTimeout(()=>{
            this.$refs.table.loadRowEcharts();// 执行渲染行内图表
          }, 500)
        } else {
          this.$message.error("获取任务进度失败！");
        }
      },
      // 打开新增弹窗
      async onAdd() {
        this.setVisible = true;
        this.$nextTick(()=>{
          this.$refs.formBasicSetting.resetFields(); // 清空表单数据
        });
        this.title = '新增任务设置';
      },
      // 取消保存
      async setCancle() {
        this.$nextTick(()=>{
          this.$refs.formBasicSetting.resetFields(); // 清空表单数据
        });
        this.formBasicSetting.mesgID = -1;
        this.setVisible = false;
      },
      // 关闭弹窗
      async closeSetDialog() {
        this.setVisible = false;
        this.$nextTick(()=>{
          this.$refs.formBasicSetting.resetFields(); // 清空表单数据
        });
        this.formBasicSetting.mesgID = -1;
      },
      // 保存
      async editSubmit() {
        var admitSubmit = false;
        this.$refs.formBasicSetting.validate(valid => {
          if (valid) {
            admitSubmit = true;
          }
        });
        if (!admitSubmit) {
          this.$message.error('请检查填写的信息!');
          return;
        }
        if (this.formBasicSetting.planStartTime > this.formBasicSetting.planEndTime) {
          this.$message.error('应开始时间不能小于应结束时间！');
          return;
        }
        const res = await addDayReportBeforeDataBoardBasicSetting(this.formBasicSetting);
        if (res.success) {
          this.$message.success('保存成功!');
          this.$nextTick(()=>{
            this.$refs.formBasicSetting.resetFields(); // 清空表单数据
          });
          this.formBasicSetting.mesgID = -1;
          //获取数据类型
          const dataType = await getDayReportBeforeDataBoardDataType();
          this.dataTypeList = dataType.data.list;
          this.getList();
          this.setVisible = false;
        }
      },
      // 删除
      async onDelete(row) {
        this.$confirm('确定要删除吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteDayReportBeforeDataBoardBasicSetting(row).then(response => {
          if (response.data) {
            // 删除成功的处理逻辑
            this.$message.success('删除成功!');
            this.getList();
          } else {
            this.$message.error("删除失败");
          }
        })
        }).catch(() => {
          this.$message.info('已取消删除');
        });
      },
      // 打开编辑弹窗
      async onEdit(row) {
        this.setVisible = true;
        this.formBasicSetting.mesgID = row.mesgID;
        this.formBasicSetting.enmPlatform = row.enmPlatform;
        this.formBasicSetting.platform = row.platform;
        this.formBasicSetting.dataType = row.dataType;
        this.formBasicSetting.checkDimension = row.checkDimension;
        this.formBasicSetting.planStartTime = row.planStartTime;
        this.formBasicSetting.planEndTime = row.planEndTime;
        this.formBasicSetting.owner = row.owner;
        this.formBasicSetting.ownerID = row.ownerID;
        this.formBasicSetting.errorNoticer = row.errorNoticer;
        this.formBasicSetting.errorNoticerID = row.errorNoticerID;
        this.title = '编辑任务设置';
      },
      // 切换表格类型
      async radioClick() {
        if (this.boardType == 1) {
          this.tableData = this.tableDataWorkProgress;
          this.$refs.table.loadRowEcharts();// 执行渲染行内图表
        }
        if (this.boardType == 2) {
          this.tableData = this.tableDataBasicSetting;
        }
      },
      //趋势图
      async showchart(row) {
        let that = this;
        this.chartFilter.startDate = dayjs(row.workDate).subtract(6, 'day').format('YYYY-MM-DD HH:mm:ss');
        this.chartFilter.endDate = row.workDate;
        this.chartFilter.enmPlatform = row.enmPlatform;
        this.chartFilter.dataType = row.dataType;
        this.chartFilter.errorNoticer = row.errorNoticer;

        this.chartFilter.orderBy = 'workDate';
        this.chartFilter.isAsc = true;

        const res = await getDayReportBeforeDataBoardWorkProgressMap(this.chartFilter).then(res => {
            that.dialogVisible.visible = true;
            that.dialogVisible.data = res;
            that.dialogVisible.title = res.title;
            res.title = "";
        })
        this.dialogVisible.visible = true;
      },
      // 打开异常反馈弹窗
      async onEditWorkProgress(row) {
        this.setErrorVisible = true;
        this.errorSetting.mesgID = row.mesgID;
        this.errorSetting.errorBackIdea = '';// 清空表单数据
      },
      // 关闭异常反馈弹窗
      closeSetErrorDialog() {
        this.setErrorVisible = false;
      },
      // 取消保存
      setErrorCancle() {
        this.setErrorVisible = false;
      },
      // 保存
      async editErrorSubmit() {
        const res = await editErrorReason(this.errorSetting);
        if (res.success) {
          this.$message.success('保存成功!');
          this.getList();
          this.setErrorVisible = false;
          this.errorSetting.mesgID = row.mesgID;
          this.errorSetting.errorBackIdea = '';
        }
      },
    }
 }
</script>
<style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
      background-color: #fff;
  }

  ::v-deep .inner-container::-webkit-scrollbar {
      display: none;
  }

  ::v-deep .mycontainer {
      position: relative;
  }

  .uptime {
      font-size: 14px;
      position: absolute;
      right: 30px;
  }

  //解决下拉菜单多选由文字太长导致样式问题
  ::v-deep .el-select__tags-text {
      max-width: 45px;
  }
</style>