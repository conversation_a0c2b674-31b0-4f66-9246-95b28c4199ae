<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <el-tabs v-model="activeName" style="height:94%;">
        <el-tab-pane    label="毛三和净利润" name="tab1" style="height: 100%;">
          <GrossThirdAndProfit :filter="Filter" ref="GrossThirdAndProfit" style="height: 100%;"/>
      </el-tab-pane>
       <el-tab-pane    label="运营分成方案" name="tab2" style="height: 100%;">
          <OperationalSharingScheme :filter="Filter" ref="OperationalSharingScheme" style="height: 100%;"/>
      </el-tab-pane>
    
    <el-tab-pane   label="基础数据模板" name="tab3" style="height: 100%;">
      <UnderlyingDataTemplate :filter="Filter" ref="UnderlyingDataTemplate" style="height: 100%;"/>
  </el-tab-pane>
    </el-tabs>
    </my-container >
  
   </template>
  <script>
  import MyContainer from "@/components/my-container";
   import UnderlyingDataTemplate from '@/views/financial/Month/UnderlyingDataTemplate'
   import OperationalSharingScheme from '@/views/financial/Month/OperationalSharingScheme'
   import GrossThirdAndProfit from '@/views/financial/Month/GrossThirdAndProfit'
   import checkPermission from '@/utils/permission'
  export default {
    name: "Users",
    components: { MyContainer,UnderlyingDataTemplate,OperationalSharingScheme,checkPermission,GrossThirdAndProfit},
    data() {
      return {
        that:this,
        Filter: {
        },
        pageLoading:"",
        activeName:"tab1",
        shopList:[],
        userList:[],
        groupList:[],
        selids:[],
        dialogVisibleSyj:false,
        fileList:[],
      };
    },
    mounted() {
    },
    methods: {
  async onSearch(){
    if (this.activeName=='tab1') 
    this.$refs.GrossThirdAndProfit.onSearch();
    if (this.activeName=='tab2') 
  this.$refs.OperationalSharingScheme.onSearch();
  if (this.activeName=='tab3') 
  this.$refs.UnderlyingDataTemplate.onSearch();
  }
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
  