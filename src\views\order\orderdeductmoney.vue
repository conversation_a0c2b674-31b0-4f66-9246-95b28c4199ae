<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="发生时间:">
          <el-date-picker style="width:320px"
            v-model="filter.timerange"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始"
            end-placeholder="结束"
            :picker-options="pickerOptions"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="平台:">
          <el-select v-model="filter.platform" placeholder="请选择" style="width: 100%" :clearable="true" @change="onchangeplatform">
            <el-option
                v-for="item in platformList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="店铺:">
            <el-select
              v-model="filter.shopCode"
              placeholder="请选择"
              :clearable="true" :collapse-tags="true"  filterable>
              <el-option
                v-for="item in shopList"
                :key="item.shopCode"
                :label="item.shopName"
                :value="item.shopCode"/>
            </el-select>
          </el-form-item>
       
       <el-form-item label="订单号:">
            <el-input v-model="filter.orderNo" placeholder="订单号"/>         
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
 
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
      :tableData='list'    :tableCols='tableCols'
      :tableHandles='tableHandles'
      :loading="listLoading">
    </ces-table>
    
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getlist"
      />
    </template>

    <el-drawer
      :title="formtitle"
      :modal="false"
      :wrapper-closable="true"
      :modal-append-to-body="false"
      :visible.sync="addFormVisible"
      direction="btt"
      size="'auto'"
      class="el-drawer__wrapper"
      style="position:absolute;"
    >
    <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
      <div class="drawer-footer">
        <el-button @click.native="addFormVisible = false">取消</el-button>
        <my-confirm-button type="submit"  :loading="addLoading" @click="onAddSubmit" />
      </div>
    </el-drawer>


    <el-dialog title="导入扣款数据" :visible.sync="dialogVisible" width="30%">
      <span>
        <el-upload
          ref="upload"
          class="upload-demo"
          :auto-upload="false"
          :multiple="false"
          :limit="1"
          action
          accept=".xlsx"
          :http-request="uploadFile"
          :file-list="fileList"
        >
         <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
         </template> 
          <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>

<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { getbyid, deletebyid, addoredit, getList as getshopList } from '@/api/operatemanage/base/shop'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatPlatform,formatYesorno} from "@/utils/tools";
import { rulePlatform,ruleShopCode} from "@/utils/formruletools";
import { 
  importOrderDeductMoney,
  pageOrderDeductMoney,
  exportOrderDeductMoney,
  getById,
  addOrUpdate,
  deleteData,
} from "@/api/order/orderdeductmoney"
const tableCols =[
       {istrue:true,label:"操作",width:"85",type:'button',btnList:[{label:"修改 ",handle:(that,row)=>that.onEdit(row)},{label:" 删除",handle:(that,row)=>that.onDelete(row)}]},
       {istrue:true,prop:'platform',label:'平台', width:'100',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
       {istrue:true,prop:'shopName',label:'店铺	', width:'220',sortable:'custom',}, 
       {istrue:true,prop:'orderNo',label:'业务基础订单号	', width:'250',sortable:'custom',},
       {istrue:true,prop:'amount',label:'支出金额（元）', width:'160',sortable:'custom',},
       {istrue:true,prop:'occurrenceTime',label:'发生时间	', width:'160',sortable:'custom',},
       {istrue:true,prop:'createdTime',label:'创建时间	', width:'160',sortable:'custom',},
       {istrue:true,prop:'createdUserName',label:'创建人	', width:'100',sortable:'custom',},
       {istrue:true,prop:'modifiedTime',label:'修改时间	', width:'160',sortable:'custom',},
       {istrue:true,prop:'modifiedUserName',label:'修改人	', width:'100',sortable:'custom',},
     ];
const tableHandles1=[
        {label:"新增", handle:(that)=>that.onAdd()},
        {label:"导入模板", handle:(that)=>that.downloadTemplate()},
        {label:"导入", handle:(that)=>that.startImport()},
        {label:"导出", handle:(that)=>that.onExport()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: {
        startDate:null,
        endDate:null,
        platform:null,
        shopCode:"",
        orderNo:null,
        timerange:[formatTime(dayjs().startOf("month"), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")]
      },
      list: [],
      summaryarry:{},
      pager:{OrderBy:"occurrenceTime",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      platformList: [],
      shopList: [],
      dialogVisible: false,
      autoform:{
               fApi:{},
               rule:[],
               options:{submitBtn:false},//global: {'*': {props: {  disabled: true },col: { span: 12 }}}}
        },
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,     
      deleteLoading: false,
      formtitle:"新增",
      fileList:[],
      pickerOptions:{
        disabledDate(time){
          return time.getTime()>Date.now();
        }
      }
    }
  },
  async mounted() {
    await this.setPlatform();
    await this.getlist();
    
  },
  methods: {
    //设置平台下拉
    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;

      //自动表单数据？
      // for (const i in this.$data.autoform.rule) {
      //   if(this.$data.autoform.rule[i].field=="platform"){
      //     this.$data.autoform.rule[i].options=pfrule.options;
      //     break;
      //   }
      // }
    },
    //设置店铺下拉
    async onchangeplatform(val){
      if(!val){
        this.shopList=[];
        return;
      }
      const res = await getshopList({platform:val,CurrentPage:1,PageSize:1000});
      this.shopList=res.data.list;
      this.filter.shopCode="";
    },
    //下载导入模板
    downloadTemplate(){
        window.open("../static/excel/订单违规扣款导入模板.xlsx","_self");
    },
    //开始导入
    startImport(){
      this.dialogVisible=true;
    },
    //取消导入
    cancelImport(){
      this.dialogVisible=false;
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {        
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    //提交导入
    submitUpload() {
        this.$refs.upload.submit();       
    },
    //上传文件
    uploadFile(item) {
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      const res = importOrderDeductMoney(form);
       this.$message({
                      message: '上传成功,正在导入中...',
                      type: "success",
                    }); 
    },
    //导出
    async onExport(){
        var params=this.getCondition();
        if(params===false){
            return;
        }
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportOrderDeductMoney(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','订单扣款_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },
    //获取查询条件
    getCondition(){
        if (this.filter.timerange&&this.filter.timerange.length>1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      else {//if(!(this.filter.startDate&&this.filter.endDate)){
        this.$message({message:"请先选择发生时间！",type:"warning"});
        return false;
      }
      var pager = this.$refs.pager.getPager();
      var page  = this.pager;
      const params = {
        ...pager,
        ...page,
        ... this.filter
      }

      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    //分页查询
    async getlist() {
      var params=this.getCondition();
      if(params===false){
            return;
      }

      this.listLoading = true
      const res = await pageOrderDeductMoney(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry=res.data.summary;
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
    //排序查询
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else{
        var orderBy =column.prop=="shopName"?"shopCode":column.prop;
        this.pager={OrderBy:orderBy,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    },
    async updateruleshop(platformid) {
        await this.autoform.fApi.setValue({shopCode:''});
        await this.autoform.fApi.updateRule('shopCode',{... await ruleShopCode(platformid)});
        await this.autoform.fApi.sync('shopCode');
    },
    async updateShopName(val,rule){
      console.log(val,rule);
    },
    async onEdit(row) {
        var arr = Object.keys(this.autoform.fApi);
        if(arr.length ==0)
              await this.onAdd()
      this.formtitle='编辑';
      this.addFormVisible = true
      const res = await getById(row.id )
      if (res.data&&res.data.platform==0) res.data.platform=null
      await this.autoform.fApi.setValue(res.data)
    },
   async onAdd() {
      this.formtitle='新增';
      this.addFormVisible = true;
      let that=this;
      this.autoform.rule=[{type:'hidden',field:'id',title:'id',value: 0},
                      {type:'select',field:'platform',title:'平台', value: null, update(val, rule){ if (val) {that.updateruleshop(val)}}, ...await rulePlatform(),validate:[{type:"number",required:true,message:"请选择平台"}]},
                      {type:'select',props:{label:{in:{value:true}}},field:'shopCode',title:'店铺', value: '',validate:[{type:"string",required:true,message:"请选择店铺"}],on:{change(val,rule){that.updateShopName(val,rule)}}},
                      {type:'input',field:'orderNo',title:'业务基础订单号',width:200,validate:[{type:"string",required:true,message:"请填写单号"}]},
                      {type:'InputNumber',field:'amount',title:'支出金额（元）',validate:[{type:"number",required:true,message:"请填写支出金额（元）"}]},
                      {type:'DatePicker',props:{type:"datetime"},field:'occurrenceTime',title:'发生时间',validate: [{type: 'string', required: true, message:'请选择发生时间'}]},
                 ];
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
          this.autoform.fApi.reload()
    },
    async onEditSubmit() {
      this.addFormVisible = true
      await onAddSubmit();
    },
    async onAddSubmit() {
       this.addFormVisible=true;
       this.addLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();

          const res = await addOrUpdate(formData);
          this.onSearch();
          
          this.addFormVisible=false;
        }else{
          //todo 表单验证未通过
        }
     })
      this.addLoading=false;
    },   
    async onDelete(row) {
      row._loading = true
      this.$confirm('确认删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
            const res = await deleteData({id:row.id})
            row._loading = false
            if (!res?.success) {return }
            this.$message({
                type: 'success',
                message: '删除成功!'
            });
            this.onSearch();
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
           row._loading = false
        });
    },
    selsChange: function(sels) {
      this.sels = sels
    }
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary{
  margin-right: 7px;
}
</style>
