<template>
    <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
        <el-scrollbar style="height: 100%">
            <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="160px" class="demo-ruleForm">
                <el-form-item label="区域：" prop="regionName" :rules="[{ required: true, message: '区域不能为空', trigger: 'blur' }]">
                    <el-select v-model="ruleForm.regionName" @change="queryTool" placeholder="区域" class="publicCss" collapse-tags>
                        <el-option v-for="item in regionNameList" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="选择工具：" prop="toolName" :rules="[{ required: true, message: '工具不能为空', trigger: 'blur' }]">
                    <el-select v-model="ruleForm.parentTool" placeholder="选择工具" :disabled="this.editInfo.id?true:false" @change="getToolDesc" class="publicCss" clearable collapse-tags>
                        <el-option v-for="item in toolZhNameList" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="工具描述：" prop="toolDesc">
                    <el-input style="width:80%;"
                     type="textarea"
                     disabled
                    :autosize="{ minRows: 4, maxRows: 11 }"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.toolDesc"
                     placeholder="工具描述" clearable />
                </el-form-item>
                <el-form-item label="字段：" prop="queryFields">
                    <el-cascader placeholder="请选择工具需要的字段" @change="handleChange" :options="fieldList" :props="optionProps" v-model="ruleForm.queryFieldList" 
                        style="width:80%;"  collapse-tags collapse-tags-tooltip filterable clearable />
                </el-form-item>
                <el-form-item label="关键字：" prop="toolKeywords">
                    <el-input style="width:80%;"
                     type="textarea"
                    :autosize="{ minRows: 4, maxRows: 11 }"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.toolKeywords"
                     placeholder="关键字" clearable />
                </el-form-item>
            </el-form>
        </el-scrollbar>
        <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
            <el-button @click="cancellationMethod">取消</el-button>
            <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
        </div>
    </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { copyAiToolConfig, aiToolConfigListValue, aiToolConfigPage, aiTableFieldConfigTree } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
export default {
    name: 'departmentEdit',
    components: {
        inputNumberYh, MyConfirmButton
    },
    props: {
        editInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
        districtList: {
            type: Object,
            default: () => {
                return {}
            }
        },
        typeList: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data() {
        return {
            sectionList2:['7 days','15 days','30 days'],
            regionNameList: ['南昌','深圳', '武汉'],
            toolZhNameList: [],
            tableList: [],
            selectProfitrates: [],
            optionProps: {
                value: 'id',
                label: 'name',
                parentId: 'parentId',
                children: 'children',
                multiple: true
            },
            fieldList: [],
            ruleForm: {
                label: '',
                name: '',
                toolDesc: '',
            },
            rules: {
                attendanceFigures: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                totalAmount: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                foreignObjects: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                totalOrders: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
            }
        }
    },

    async mounted() {
        
        this.ruleForm = { ...this.editInfo };
        if (this.editInfo.id) {
            this.showFieldTree(this.editInfo.tables)
            this.ruleForm.queryFieldList = this.editInfo.queryFields.split(',').map(num => [1, BigInt(num)]);
            console.log(3333, this.ruleForm.queryFieldList)
            this.$forceUpdate()
        }
    },
    methods: {
        async queryTool() {
            const { data } = await aiToolConfigListValue('toolZhName', this.ruleForm.regionName, 1)
            if (data) {
                this['toolZhNameList'] = data;
            }
        },
        async getToolDesc(val) {
            const { data, success } = await aiToolConfigPage({"toolZhName": val})
            if (success) {
                console.log(222, data.list[0].toolDesc)
                const aiToolConfig = data.list[0];
                this.showFieldTree(aiToolConfig.tables)
                this.ruleForm.toolDesc = aiToolConfig.toolDesc
                this.ruleForm.toolName = aiToolConfig.toolName
                this.ruleForm.toolZhName = aiToolConfig.toolZhName
                this.ruleForm.databaseName = aiToolConfig.databaseName
                this.ruleForm.tables = aiToolConfig.tables
                this.ruleForm.defaultTimeRange = aiToolConfig.defaultTimeRange
                this.ruleForm.llmModel = aiToolConfig.llmModel
                this.ruleForm.dbconConfig = aiToolConfig.dbconConfig
                this.ruleForm.promptTemplate = aiToolConfig.promptTemplate
                this.ruleForm.llmConfig = aiToolConfig.llmConfig
                this.ruleForm.isMain = 0;
                this.$forceUpdate()
            }
        },
        async showFieldTree(tables) {
            const { data } = await aiTableFieldConfigTree(tables)
            if (data) {
                this.fieldList = data.map(item => {
                    const newItem = { ...item };
                    newItem.name = newItem.name + " " + newItem.remark
                    if (newItem.children) {
                        // 只保留二级节点，移除三级节点
                        newItem.children = newItem.children.map(child => {
                            const newChild = { ...child };
                            newChild.children = undefined; // 删除三级节点
                            newChild.name = newChild.name + " " + newChild.remark
                            return newChild;
                        });
                    }
                    return newItem;
                });
            }
        },
        handleChange(value) {
            // 限制选中值的深度为2级
            if (value && value.length > 2) {
                this.selectedValue = value.slice(0, 2);
            }
        },
        cancellationMethod() {
            this.$emit('cancellationMethod');
        },
        submitForm(formName) {
            console.log(this.ruleForm.label, 'this.ruleForm.label');
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    if (this.ruleForm.queryFieldList) {
                        this.ruleForm.queryFields = this.ruleForm.queryFieldList.map(arr => arr.filter(num => num !== '1')).join(',')
                    }
                    const { data, success } = await copyAiToolConfig(this.ruleForm)
                    if (!success) {
                        return
                    }
                    this.$message.success('操作成功')
                    await this.$emit("search");

                } else {
                    console.log('error submit!!');
                    this.$message.error('操作失败')
                    return false;
                }
            });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
    }
}
</script>
<style scoped lang="scss">
.publicCss {
    width: 80%;
}
</style>
