<template>
  <MyContainer>
    <div class="pattern">
      <div class="pattern_bottom">
        <div>
          应退/实退订单数
        </div>
        <div>
          <el-date-picker v-model="orderTime" type="daterange" unlink-panels range-separator="至"
            start-placeholder="发货开始时间" end-placeholder="发货结束时间" :picker-options="pickerOptions"
            style="width: 230px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" :clearable="false"
            @change="changeTime($event, 1)">
          </el-date-picker>
          <el-button type="primary" @click="onOrderAnalysisMethod">搜索</el-button>
        </div>
        <div v-loading="orderLoading" style="height: 390px;">
          <buschar v-if="orderAnalysisChar.visible" ref="orderAnalysisChar" :analysisData="orderAnalysisChar.data"
            :thisStyle="{
              width: '100%',
              height: '390px',
              'box-sizing': 'border-box',
            }">
          </buschar>
        </div>
      </div>

      <div class="pattern_bottom">
        <div>
          应退/实退金额
        </div>
        <div>
          <el-date-picker v-model="inventoryTime" type="daterange" unlink-panels range-separator="至"
            start-placeholder="发货开始时间" end-placeholder="发货结束时间" :picker-options="pickerOptions"
            style="width: 230px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" :clearable="false"
            @change="changeTime($event, 2)">
          </el-date-picker>
          <el-button type="primary" @click="onInventoryAnalysisMethod">搜索</el-button>
        </div>
        <div v-loading="inventoryLoading" style="height: 390px;">
          <buschar v-if="inventoryAnalysisChar.visible" ref="inventoryAnalysisChar"
            :analysisData="inventoryAnalysisChar.data" :thisStyle="{
              width: '100%',
              height: '390px',
              'box-sizing': 'border-box',
            }">
          </buschar>
        </div>
      </div>

      <div class="pattern_bottom">
        <div>
          应退/实退物流公司
        </div>
        <div style="display: flex;align-items: center;">
          <el-date-picker v-model="inventoryTimeCompany" type="daterange" unlink-panels range-separator="至"
            start-placeholder="开始时间" end-placeholder="结束时间" :picker-options="pickerOptions"
            style="width: 230px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" :clearable="false"
            @change="changeTime($event, 3)">
          </el-date-picker>
          <div style="width: 200px;margin-right: 5px;">
            <inputYunhan ref="refexpressCompanyNames" :inputt.sync="inventoryAnalysis.expressCompanyNames"
              v-model="inventoryAnalysis.expressCompanyNames" width="200px" placeholder="物流公司/Enter多行输入"
              :clearable="true" :clearabletext="true" :maxRows="100" :maxlength="1000000" :valuedOpen="true"
              @callback="callbackMethod($event, 'expressCompanyNames')" title="物流公司">
            </inputYunhan>
          </div>
          <el-button type="primary" @click="initCharts">搜索</el-button>
        </div>
        <div class="chart_bottom">
          <div class="chart_item">
            <div element-loading-text="加载中" element-loading-spinner="el-icon-loading">
              <div>
                <div id="shopCharts" :style="thisStylePieChart"></div>
              </div>
            </div>
          </div>
          <div class="chart_item">
            <div element-loading-text="加载中" element-loading-spinner="el-icon-loading">
              <div>
                <div id="groupCharts" :style="thisStylePieChart"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="pattern_bottom">
        <div>
          理赔原因分布
        </div>
        <div>
          <el-date-picker v-model="inventoryTimeReason" type="daterange" unlink-panels range-separator="至"
            start-placeholder="发货开始时间" end-placeholder="发货结束时间" :picker-options="pickerOptions"
            style="width: 230px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" :clearable="false"
            @change="changeTime($event, 4)">
          </el-date-picker>
          <el-button type="primary" @click="initChartsCompany">搜索</el-button>
        </div>
        <div class="chart_bottom">
          <div class="chart_item">
            <div element-loading-text="加载中" element-loading-spinner="el-icon-loading">
              <div>
                <div id="shopChartsReason" :style="thisStylePieChart"></div>
              </div>
            </div>
          </div>
          <div class="chart_item">
            <div element-loading-text="加载中" element-loading-spinner="el-icon-loading">
              <div>
                <div id="groupChartsReason" :style="thisStylePieChart"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { pickerOptions } from '@/utils/tools'
import { expressClaimOrderStatisticCountAnalysis, expressClaimOrderStatisticAmountAnalysis, expressClaimOrderStatisticExpressCompanyRate, expressClaimOrderStatisticClaimReasonRate } from '@/api/customerservice/expressClaimOrder'
import dayjs from 'dayjs'
import * as echarts from "echarts";
import buschar from "@/components/Bus/buschar";
import inputYunhan from "@/components/Comm/inputYunhan";
const startTime = dayjs().subtract(30, 'day').startOf('day').format('YYYY-MM-DD');
const endTime = dayjs().startOf('day').format('YYYY-MM-DD');
const timeRanges = [startTime, endTime];
export default {
  name: "summaryData",
  components: {
    MyContainer, buschar, inputYunhan
  },
  data() {
    return {
      timeRanges,
      myChartShop: null,
      myChartShop1: null,
      myChartShop2: null,
      myChartShop3: null,
      thisStylePieChart: {
        width: '100%', height: '270px', 'box-sizing': 'border-box', 'line-height': '420px'
      },
      orderLoading: false,
      inventoryLoading: false,
      orderTime: timeRanges,
      inventoryTime: timeRanges,
      inventoryTimeCompany: timeRanges,
      inventoryTimeReason: timeRanges,
      orderAnalysisChar: { visible: false, title: "", data: {} },
      inventoryAnalysisChar: { visible: false, title: "", data: {} },
      ListInfoTop: {
        startDate: timeRanges[0],
        endDate: timeRanges[1],
      },
      orderAnalysis: {
        startDate: timeRanges[0], //开始时间
        endDate: timeRanges[1], //结束时间
      },
      inventoryAnalysis: {
        startDate: timeRanges[0], //开始时间
        endDate: timeRanges[1], //结束时间
        expressCompanyNames: '',
      },
      inventoryReason: {
        startDate: timeRanges[0], //开始时间
        endDate: timeRanges[1], //结束时间
      },
      that: this,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    callbackMethod(val, type) {
      const map = {
        expressCompanyNames: () => (this.inventoryAnalysis.expressCompanyNames = val),
      };
      map[type]?.();
    },
    async getList() {
      await this.onOrderAnalysisMethod();
      await this.onInventoryAnalysisMethod();
      await this.initCharts();
      await this.initChartsCompany();
    },
    async initCharts() {
      const { data, success } = await expressClaimOrderStatisticExpressCompanyRate(this.inventoryAnalysis);
      if (!success) return;
      await this.initChartPair(
        ['shopCharts', 'groupCharts'],
        data,
        key => item => ({ name: item.expressCompany, value: item[key] }),
        '物流公司:',
        [this.myChartShop, this.myChartShop1]
      );
    },
    async initChartsCompany() {
      const { data, success } = await expressClaimOrderStatisticClaimReasonRate(this.inventoryReason);
      if (!success) return;
      await this.initChartPair(
        ['shopChartsReason', 'groupChartsReason'],
        data,
        key => item => ({ name: item.claimReason, value: item[key] }),
        '理赔原因:',
        [this.myChartShop2, this.myChartShop3]
      );
    },
    async initChartPair(domIds, rawData, extractFn, titlePrefix, chartRefs) {
      const [domId1, domId2] = domIds;
      const chartDom1 = document.getElementById(domId1);
      const chartDom2 = document.getElementById(domId2);
      if (!chartDom1 || !chartDom2) {
        console.warn('图表 DOM 未准备好');
        return;
      }
      const dataA = rawData.map(extractFn('returnAmount'));
      const dataB = rawData.map(extractFn('actualReturnAmount'));

      chartRefs[0]?.clear();
      chartRefs[0] = chartRefs[0] ?? echarts.init(chartDom1);
      const option1 = await this.Getoptions(dataA, `${titlePrefix}应退`);
      chartRefs[0].setOption(option1);

      chartRefs[1]?.clear();
      chartRefs[1] = chartRefs[1] ?? echarts.init(chartDom2);
      const option2 = await this.Getoptions(dataB, `${titlePrefix}实退`);
      chartRefs[1].setOption(option2);
    },
    async Getoptions(data, title) {
      const option = {
        title: {
          text: title,
          left: 'left',
          top: 0,
          textStyle: {
            fontSize: 14,
            fontWeight: 'bold',
            padding: [0, 0, 10, 0]
          }
        },
        tooltip: {
          trigger: "item",
          position: function (pos, params, dom, rect, size) {
            const obj = { top: 60 };
            obj[pos[0] < size.viewSize[0] / 2 ? 'left' : 'right'] = 5;
            return obj;
          },
          textStyle: { align: "left" }
        },
        legend: {
          type: "scroll",
          orient: 'vertical',
          x: '72%',
          y: 'center',
          itemWidth: 10,
          itemHeight: 10,
          pageIconColor: "#409EFF",
          pageIconInactiveColor: "#909399",
          formatter: function (name) {
            return echarts.format.truncateText(
              name,
              200,
              "10px Microsoft Yahei",
              "..."
            );
          },
          tooltip: { show: true }
        },
        grid: {
          left: "1%",
          right: "1%",
          bottom: "1%",
          containLabel: true
        },
        series: {
          type: 'pie',
          radius: '45%',
          center: ["40%", "58%"],
          data: data,
          labelLine: {
            show: true
          },
          label: {
            show: true,
            formatter: '{d}%',
            position: 'outside'
          }
        }
      };
      return option;
    },
    async onOrderAnalysisMethod() {
      this.fetchChartAnalysis({
        loadingKey: 'orderLoading',
        chartKey: 'orderAnalysisChar',
        apiFn: expressClaimOrderStatisticCountAnalysis,
        params: this.ListInfoTop
      });
    },
    async onInventoryAnalysisMethod() {
      this.fetchChartAnalysis({
        loadingKey: 'inventoryLoading',
        chartKey: 'inventoryAnalysisChar',
        apiFn: expressClaimOrderStatisticAmountAnalysis,
        params: this.orderAnalysis
      });
    },
    async fetchChartAnalysis({ loadingKey, chartKey, apiFn, params }) {
      this[loadingKey] = true;
      this[chartKey].visible = false;
      const { data, success } = await apiFn(params);
      this[loadingKey] = false;
      if (!success) return;
      data.series.forEach(item => {
        item.itemStyle = {
          normal: {
            label: {
              show: true,
              position: "top",
              textStyle: {
                fontSize: 14,
              },
            },
          },
        };
        item.emphasis = { focus: "series" };
        item.smooth = false;
      });
      this[chartKey].visible = true;
      this[chartKey].data = data;
    },
    async changeTime(e, val) {
      const map = {
        1: 'ListInfoTop',
        2: 'orderAnalysis',
        3: 'inventoryAnalysis',
        4: 'inventoryReason'
      };
      const target = this[map[val]];
      if (target) {
        target.startDate = e?.[0] ?? null;
        target.endDate = e?.[1] ?? null;
      }
    },
  }
}
</script>

<style scoped lang="scss">
.pattern {
  width: 100%;
  // height: 1500px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: #ececec;
  padding: 5px;
  box-sizing: border-box;

  .pattern_top,
  .pattern_bottom {
    //flex: 1;
    background-color: white;
    display: flex;
    flex-direction: column;
    padding: 10px;
  }
}

.pattern_bottom {
  gap: 10px;
}

.chart_bottom {
  display: flex;
  justify-content: space-between;
  column-gap: 10px;
  padding-top: 10px;
  height: 300px;
}

.chart_item {
  flex: 1;
  text-align: center;
  // display: flex;
  // align-items: center;
  // justify-content: center;
}

::v-deep .el-select__tags-text {
  max-width: 80px;
}
</style>
