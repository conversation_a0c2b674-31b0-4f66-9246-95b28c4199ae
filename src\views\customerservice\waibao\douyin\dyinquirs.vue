<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='inquirslist' @select='selectchange' :isSelection='false' :tableCols='tableCols'
            :loading="listLoading" :summaryarry="summaryarry">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.shopNameList" placeholder="店铺" clearable filterable multiple :collapse-tags="true">
                            <el-option v-for="item in shopList" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.nick" placeholder="客服昵称" style="width:160px;" clearable
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <datepicker v-model.trim="filter.sdate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date">
                        </datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onImportSyj" v-if="checkPermission(['api:Customerservice:OutSource:ImporDyInquirsAsync'])" >导入</el-button>
                    <el-button type="primary" @click="onImportSyjModel">下载模板</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getinquirsList" />
        </template>
        <el-dialog title="抖音咨询数据" :visible.sync="dialogVisibleSyj" width="30%" :close-on-click-modal="false"
            v-dialogDrag>
            <el-form ref="improtGroupForm" :model="improtGroupForm" label-width="55px" label-position="left">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <!-- <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2"
                            :on-change="onUploadChange2" :on-remove="onUploadRemove2">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                                @click="onSubmitupload2">上传</my-confirm-button>
                        </el-upload> -->

                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false"  action
                            accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'

import  {
    getOSDouYinInquirsList,deleteOSDyInquirs,deleteBatchOSDyInquirs,imporDyInquirsAsync
} from "@/api/customerservice/waibao";

import {getOSStoreNameList} from "@/api/customerservice/waibaocustomer";


import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
const tableCols = [
    // { istrue: false, prop: 'id', label: 'id', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'shopName', label: '店铺', width: '140', sortable: 'custom' },
    { istrue: true, prop: 'sdate', label: '日期', width: '95', sortable: 'custom', formatter: (row) => formatTime(row.sdate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'snick', label: '昵称', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'receiveds', label: '人工已接待人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'inquirs', label: '人工已接待会话量', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'noSatisfactionRate', label: '不满意率', width: '80', sortable: 'custom', formatter: (row) => (row.noSatisfactionRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'threeResponseRate', label: '3分钟人工回复率', width: '90', sortable: 'custom', formatter: (row) => (row.threeResponseRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'responseTime', label: '平均响应时长(秒)', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'satisfactionRate', label: '满意率', width: '80', sortable: 'custom', formatter: (row) => (row.satisfactionRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'salesvol', label: '客服销售额(元)', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsCount', label: '询单人数', width: '60', sortable: 'custom' },
    { istrue: true, prop: 'payers', label: '支付人数', width: '60', sortable: 'custom' },
    { istrue: true, prop: 'ipsRate', label: '询单转化率', width: '90', sortable: 'custom', formatter: (row) => (row.ipsRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'createdTime', label: '导入时间', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'batchNumber', label: '导入批次', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'noSatisfactionCount', label: '不满意人数', width: '70', sortable: 'custom' },
    { istrue: true, prop: 'satisfactionCount', label: '满意人数', width: '60', sortable: 'custom'},
    { istrue: true, prop: 'threeResponseCount', label: '3分钟回复人数', width: '80', sortable: 'custom'},
    {
        istrue: true, type: "button", label: '操作', width: "115",
        btnList: [
            { label: "删除", handle: (that, row) => that.deleteData(row), permission: "api:Customerservice:OutSource:DeleteOSDyInquirs"  },
            { label: "删除批次", handle: (that, row) => that.deleteBatch(row) , permission: "api:Customerservice:OutSource:DeleteBatchOSDyInquirs"  }
        ]
    }
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
    data() {
        return {
            that: this,
            filter: {
                    shopNameList:[],
                    nick:null,
                    sdate:null,
             },
            shopList: [],
            userList: [],
            inquirslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: {},
            pager: { OrderBy: "sdate", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            uploadLoading:false,
            improtGroupForm: {
            },
        };
    },
    async mounted() {
         this.getAllShopList();
    },
    methods: {
            async getAllShopList() {
            let shops = await getOSStoreNameList({platform:6});
            this.shopList = [];
           this.shopList=shops.data;
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getinquirsList();
        },
        async getinquirsList() {
            if (this.filter.sdate) {
                this.filter.timeStart = this.filter.sdate[0];
                this.filter.timeEnd = this.filter.sdate[1];
            }
            else {
                this.filter.timeStart = null;
                this.filter.timeEnd = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            console.log(params)
            this.listLoading = true;
            const res = await getOSDouYinInquirsList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.inquirslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async deleteData(row) {    //删除
            var that = this;
            this.$confirm("此操作将删除此客服人员分组管理数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(async () => {
                    var res = await deleteOSDyInquirs({ id: row.id })
                    if (res?.success) {
                        that.$message({ message: '已删除', type: "success" });
                        that.onSearch()
                    }

                });
        
        },
        async deleteBatch(row) {    //删除批次
            var that = this;
            this.$confirm("此操作将删除此客服人员分组管理数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(async () => {
                    var res = await deleteBatchOSDyInquirs({ batchNumber: row.batchNumber })
                    if (res) {
                        that.$message({ message: '已删除', type: "success" });
                        that.onSearch()
                    }

                });
        
        },

        onImportSyjModel() {
            window.open("/static/excel/customerservice/抖音咨询数据导入模板.xlsx", "_blank");
        },
        async onImportSyj() {
            this.dialogVisibleSyj = true;
        },

        async uploadFile () {
            if (this.fileList.length ==0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false
            };
            const form = new FormData();
            form.append("upfile", this.fileList[0].raw);
            //form.append("platform", 1);
            var res = await imporDyInquirsAsync(form);
            if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
            else this.$message({ message: res.msg, type: "warning" });
            this.$refs.upload.clearFiles()
            this.uploadLoading = false;
            this.dialogVisibleSyj = false;
            this.fileList = [];
        },
        async uploadChange (file, fileList) {
            if (fileList.length == 2) {
                fileList.splice(1, 1);
                this.$message({ message: "只允许单文件导入", type: "warning" });
                return false;
            }
            this.fileList.push(file);
        },
        uploadRemove (file, fileList) {
            this.fileList.splice(0, 1);
        },
        submitUpload () {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return;
            }
            this.uploadLoading = true
            this.$refs.upload.submit();
        },
        // async onUploadChange2(file, fileList) {
        //     this.fileList = fileList;
        // },
        // async onUploadRemove2(file, fileList) {
        //     this.fileList = [];
        // },
        // async uploadFile2(item) {
        //     if (!item || !item.file || !item.file.size) {
        //         this.$message({ message: "请先上传文件", type: "warning" }); 
        //         return false;
        //     }
        //     const form = new FormData();
        //     form.append("upfile", item.file);
        //     const res = await imporDyInquirsAsync(form);
        //     if (res?.success) {
        //         this.$message({ message: '上传成功,正在导入中...', type: "success" });
        //         this.dialogVisibleSyj = false;
        //     }
        // },
        // async uploadSuccess2(response, file, fileList) {
        //     fileList.splice(fileList.indexOf(file), 1);
        // },
        // async onSubmitupload2() {
        //     if (this.fileList.length == 0) {
        //         this.$message({ message: "请先上传文件", type: "warning" });
        //         return false;
        //     }
        //     this.$refs.upload2.submit();
        // },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 55px;
}
</style>
