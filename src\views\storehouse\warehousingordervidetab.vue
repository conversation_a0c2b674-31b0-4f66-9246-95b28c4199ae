<template>
    <container>
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-date-picker style="width: 220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束" :clearable="false"
                        :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.wms_co_id" clearable placeholder="请选择发货仓" style="width: 140px">
                        <el-option v-for="item in newWareHouseList" :key="item.name" :label="item.name"
                            :value="item.wms_co_id" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.isBindBuyNo" clearable placeholder="请选择采购单状态" style="width: 140px">
                        <el-option label="已绑定" value="true" />
                        <el-option label="未绑定" value="false" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.isPrint" clearable placeholder="请选择打单状态" style="width: 140px">
                        <el-option label="已打单" value="true" />
                        <el-option label="未打单" value="false" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.isBrand_w" clearable placeholder="请选择入库状态" style="width: 140px">
                        <el-option label="已完成" value="true" />
                        <el-option label="未完成" value="false" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.isQuality" clearable placeholder="请选择质检状态" style="width: 140px">
                        <el-option label="已质检" value="true" />
                        <el-option label="未质检" value="false" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.qualityType" clearable placeholder="请选择质检类型" style="width: 140px">
                        <el-option label="质检拍照" value="1" />
                        <el-option label="入库质检" value="2" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.status" clearable placeholder="是否驳回" style="width: 140px">
                        <el-option label="正常" value="0" />
                        <el-option label="驳回" value="2" />
                        <el-option label="退回" value="3" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-input v-model.trim="filter.modifiedUserName" type="text" maxlength="100" clearable placeholder="请输入绑单人..."
                        style="width:140px;">
                    </el-input>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-input v-model.trim="filter.keywords" type="text" maxlength="100" clearable placeholder="请输入关键字..."
                        style="width:260px;">
                        <el-tooltip slot="suffix" class="item" effect="dark" :content="keywordsTip" placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input>
                </el-button>
            </el-button-group>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                    <el-input-number placeholder="运费" :min=0 :max=9999999 v-model="filter.minFreightAmont"
                        style="width: 120px"></el-input-number>
                </el-button>
                <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">至</el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input-number placeholder="运费"  :min=0 :max=9999999 v-model="filter.maxFreightAmont"
                        style="width: 120px"></el-input-number>
                </el-button>
            </el-button-group>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                    <el-input-number placeholder="数量" :precision="0" :step="1" :min=0 :max=10000000 v-model="filter.minCount"
                        style="width: 120px"></el-input-number>
                </el-button>
                <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">至</el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input-number placeholder="数量" :precision="0" :step="1" :min=0 :max=10000000 v-model="filter.maxCount"
                        style="width: 120px"></el-input-number>
                </el-button>
            </el-button-group>
            <el-button-group>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.bindingSource" clearable placeholder="绑定来源" style="width: 140px">
                        <el-option label="人工" :value="1" />
                        <el-option label="系统" :value="2" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.hasLogisticsNo" clearable placeholder="是否有物流单号" style="width: 140px">
                        <el-option label="有" :value="1" />
                        <el-option label="无" :value="2" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <!-- <el-input v-model.trim="filter.logisticsNo" type="text" clearable placeholder="物流单号" style="width:140px;">
                    </el-input> -->
                    <el-tooltip class="item" effect="dark" content="默认为精确匹配在结尾输入*进行模糊匹配点击enter批量输入" placement="top">
                        <inputYunhan :key="'3'" :keys="'three'" :width="'150px'" ref="childLogisticsNo" v-model="filter.logisticsNo" :inputt.sync="filter.logisticsNo"
                        placeholder="物流单号" :clearable="true" @callback="callbackLogisticsNo" title="物流单号" ></inputYunhan>
                    </el-tooltip>
                </el-button>
            </el-button-group>
            <el-button style="padding: 0;margin-left: 0;">
                <el-button type="primary" @click="onSearch">查询</el-button>
            </el-button>
            <el-dropdown @command="handleCommand">
                    <el-button type="primary">
                        导出方式<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown" >
                        <el-dropdown-item command="b" >全部导出到Excel</el-dropdown-item>
                        <el-dropdown-item command="a" >加急导出到Excel</el-dropdown-item>
                    </el-dropdown-menu>
            </el-dropdown>
                <slot name="extentbtn" />
        </template>

        <el-tabs v-model="activeName" style="height: 94%;">
            <el-tab-pane label="拍摄" name="first" style="height: 100%;">
                <warehousingordervide ref="warehousingordervide" :filter="filter"></warehousingordervide>
            </el-tab-pane>
            <!-- <el-tab-pane label="详情" name="second" style="height: 100%;">
                <warehousingordervidelate ref="warehousingordervidelate" :filter="filter"></warehousingordervidelate>
            </el-tab-pane> -->
            <el-tab-pane label="加急" name="three" style="height: 100%;color:red;" class="three" >
                <span slot="label" style="color: red;">加急</span>
                <warehousingordervidelateUrgent ref="warehousingordervidelateUrgent" :filter="filter"></warehousingordervidelateUrgent>
            </el-tab-pane>

        </el-tabs>

    </container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import { getAllWarehouse } from '@/api/inventory/warehouse'
import { getTokenKeyValue } from '@/api/admin/auth'
import warehousingordervide from './warehousingordervide.vue'
import warehousingordervidelate from './warehousingordervidelate.vue'
import warehousingordervidelateUrgent from "./warehousingordervidelateUrgent.vue";
import inputYunhan from "@/components/Comm/inputYunhan";


const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunHanAdminWarehousingordervidetab',
    components: { container, MyConfirmButton, vxetablebase, warehousingordervide, warehousingordervidelate,warehousingordervidelateUrgent, inputYunhan },

    data() {
        return {
            that: this,
            filter: {
                startDate: null,
                endDate: null,
                createdUserName: null,
                modifiedUserName: null,
                buyNo: null,
                warehousNo: null,
                wms_co_id: null,
                isBrand_w: null,
                isBindBuyNo: null,
                isPrint: null,
                isQuality: null,
                qualityType: null,
                status: null,
                keywords: null,
                timerange: [startTime, endTime],
                bindingSource: null,
                hasLogisticsNo: null,
                logisticsNo: null
            },
            newWareHouseList: [],
            activeName: 'first',
            keywordsTip: '支持搜索的内容：采购单号、任务编号、上传人、品名',
            pickerOptions: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近半个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
        };
    },

    async mounted() {
        var param = { key: 'departments' }
        var res = await getTokenKeyValue(param)
        if (res.data.includes('采购部')) {
            // this.filter.isBrand_w = 'false'
            this.filter.isBindBuyNo = 'false';
            this.filter.status = '0';
        } else if (res.data.includes('仓库部')) {
            this.filter.isBrand_w = 'false'
        } 
        if (this.$route.query && this.$route.query.singBuyNo) {
            this.filter.keywords = this.$route.query.singBuyNo;
            this.filter.isBindBuyNo = null;
            this.filter.timerange=[];
        }
        await this.onSearch();
        var res = await getAllWarehouse();
        this.newWareHouseList = res.data.filter((x) => { return x.name.indexOf('代发') < 0; });
    },

    methods: {
      async handleCommand(command) {
            const params = { ... this.filter }
            if (command == 'a') {
              this.exportUrgent()
            }else{
              this.exportAll()
            }
          },
     async exportUrgent(){
      this.$refs.warehousingordervidelateUrgent.onExport();
     },
     async exportAll(){
      this.$refs.warehousingordervide.onExport();
     },
        async onSearch() {
            if (this.filter.minCount > this.filter.maxCount) {
                this.$message({
                    type: 'info',
                    message: '查询条件数量最小值不能大于最大值，请重新数据！'
                });
                return;
            }
            if (this.filter.minFreightAmont > this.filter.maxFreightAmont) {
                this.$message({
                    type: 'info',
                    message: '查询条件运费最小值不能大于最大值，请重新数据！'
                });
                return;
            }
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            this.$nextTick(() => {
                if (this.activeName == 'first') this.$refs.warehousingordervide.onSearch();
                if (this.activeName == 'second') this.$refs.warehousingordervidelate.onSearch();
                if (this.activeName == 'three') this.$refs.warehousingordervidelateUrgent.onSearch();
            })
        },
        async callbackLogisticsNo(val) {
            this.filter.logisticsNo = val;
        },
    },
};
</script>

<style lang="scss" scoped>
.three .el-tabs__item{
    color: red;
  }
</style>
