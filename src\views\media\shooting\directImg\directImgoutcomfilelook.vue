<template>
    <my-container v-loading="pageLoading">
        <el-row class="rowstyle" :gutter="0">
       
            <el-col :span="8"><el-button :type="num==3?'primary':''" @click="whactclick(3)">直通车图</el-button></el-col>
        </el-row>
        <div class="marginauto">
           <div v-if="num==1">
                <div class="bancss">
                    主图-PC端
                    <div id="click-scroll-X" class="marauto">
                        <i class="el-icon-arrow-left iconsize" @click="doubleleftSlide(1)" v-if="btn1" :class="{showcs:(left==true&&oneindex==1)}"></i>
                        <div class="scroll_wrapper doublewidth" ref="doublewrapperCon1">
                            <div class="scroll_list">
                                    <div class="itemm">
                                        <div style="display: flex;">
                                            <div class="doubleitem outline" style="display: flex; justify-content: center; align-items: center;" :key="idx" v-for="(i, idx) in indeximg.threenum">
                                                <div style="width: 55px; height:55px; margin-top: 3px;" :key="indx" v-for="(i, indx) in newmainImgList[idx]">
                                                    <img :src="i.url" width="100%" height="100%"  @click="imgclick(1,[idx,indx])"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                            </div>
                        </div>
                        <i class="el-icon-arrow-right iconsize" @click="doublerightSlide(1)" v-if="btn1" :class="{showcs:(right==true&&oneindex==1)}"></i>
                    </div>
                </div>
                <div class="danbancss">
                    主图-无线端
                    <div class="flexrow smaslling">
                        <div id="click-scroll-X">
                            <i class="el-icon-arrow-left iconsize" @click="leftSlide(12)" v-if="btn2" :class="{showcs:(left==true&&oneindex==12)}"></i>
                            <div class="scroll_wrapper" ref="wrapperCon12">
                                <div class="scroll_list" v-if="datashow">
                                    <div class="item" style="display: flex; justify-content: center; align-items: center;" v-for="(i, index) in data.photoImgList" :key="index">
                                        <div style="margin: 2px; display: flex; justify-content: center; align-items: center;">
                                            <img width="60px" height="100%" :src="i.url" @click="imgclick(2,index)" :alt="i.name" mode="center"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <i class="el-icon-arrow-right iconsize" @click="rightSlide(12)" v-if="btn2" :class="{showcs:(right==true&&oneindex==12)}"></i>
                        </div>
                    </div>
                </div>
                <div class="bancss">
                    SKU
                    <div id="click-scroll-X" class="marauto">
                        <i class="el-icon-arrow-left iconsize" @click="doubleleftSlide(2)" v-if="btn3" :class="{showcs:(left==true&&oneindex==2)}"></i>
                        <div class="scroll_wrapper doublewidth" ref="doublewrapperCon2">
                            <div class="scroll_list">
                                <div class="itemm">
                                    <div style="display: flex;">
                                        <div class="doubleitem outline" style="display: flex; justify-content: center; align-items: center;" :key="idx" v-for="(i, idx) in skuimg">
                                            <div style="width: 55px; height:55px; margin: 10px 0 0 4px;" :key="indx" v-for="(i, indx) in newskuImgList[idx]">
                                                <img :src="i.url" width="100%" height="100%" @click="imgclick(3,[idx,indx])"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <i class="el-icon-arrow-right iconsize" @click="doublerightSlide(2)" v-if="btn3" :class="{showcs:(right==true&&oneindex==2)}"></i>
                    </div>
                </div>
                <div class="disbancss">
                    详情页
                    <div v-for="(item,index) in data.detailImgList" :key="index" style="width: 100px; height: auto; margin-left: 55px;" class="flexaligncenter">
                        <img :src="item.url" alt="" width="100%" height="auto" @click="imgclick(5,index)">
                    </div>
                </div>
            </div>
           <div v-else-if="num==2">
                <div class="bancss">
                    主图视频
                    <div class="smaslling">
                        <video width="auto" height="150px" :src="data.mainVedioList!=null?data.mainVedioList.domainUrl:''" loop muted @click="showvideo(data.mainVedioList.domainUrl)"></video>
                    </div>
                </div>
                <div class="danbancss">
                    微详情视频
                    <div class="flexrow smaslling">
                        <div id="click-scroll-X">
                            <i class="el-icon-arrow-left iconsize" @click="leftSlide(22)" v-if="btn4" :class="{showcs:(left==true&&oneindex==22)}"></i>
                            <div class="scroll_wrapper" ref="wrapperCon22">
                                <div class="scroll_list">
                                    <div class="item" style="display: flex; justify-content: center; align-items: center;" v-for="(i, idx) in data.ohtersVedioList" :key="idx">
                                        <!-- <div style="margin: 2px; display: flex; justify-content: center; align-items: center;"> -->
                                            <video width="60px" height="100%" :src="data.ohtersVedioList.length>0?i.url:''" loop muted @click="showvideo(i.url)"></video>
                                        <!-- </div> -->
                                    </div>
                                </div>
                            </div>
                            <i class="el-icon-arrow-right iconsize" @click="rightSlide(22)" v-if="btn4" :class="{showcs:(right==true&&oneindex==22)}"></i>
                        </div>
                    </div>
                </div>
            </div>
           <div v-else-if="num==3">
            <div class="bancss">
                    直通车图
                    <div id="click-scroll-X" class="marauto">
                        <i class="el-icon-arrow-left iconsize" @click="doubleleftSlide(31)" v-if="btn5" :class="{showcs:(left==true&&oneindex==31)}"></i>
                        <div class="scroll_wrapper doublewidth" ref="doublewrapperCon31">
                            <div class="scroll_list">
                                <!-- <div class="doubleitem" v-for="(i, idx) in 2" :key="idx">
                                    <div style="flex: 1;" v-for="(i, idx) in 2" :key="idx" >
                                        <div style="display: flex; width: 380px; height: 60px; flex-direction: row;">
                                            <div v-for="(i, idx) in 6" style="height: 65px; width: 65px;margin-left: 5px;" class="borderstyle">
                                                <img :src="imgtest" width="100%" height="100%"  alt="" />
                                            </div>
                                        </div>
                                    </div>
                                </div> -->
                                <div class="itemm">
                                    <div style="display: flex;">
                                        <div class="doubleitem outline" style="display: flex; justify-content: center; align-items: center;" :key="idx" v-for="(i, idx) in tocarimg">
                                            <div style="width: 55px; height:55px; margin: 10px 0 0 4px;" :key="indx" v-for="(i, indx) in newdirectImgList[idx]">
                                                <img :src="i.url" width="100%" height="100%" @click="imgclick(4,[idx,indx])"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <i class="el-icon-arrow-right iconsize" @click="doublerightSlide(31)" v-if="btn5" :class="{showcs:(right==true&&oneindex==31)}"></i>
                    </div>
                </div>
            </div>
        </div>
          <!--视频播放-->
        <el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer"  :append-to-body="true" >
            <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeVideoPlyer">关闭</el-button>
            </span>
        </el-dialog>
        <el-image-viewer :nodown="false" v-if="showGoodsImage" :initialIndex="imgindex" :url-list="imgList"  :wrapperClosable="false" :on-close="closeFunc" style="z-index:9999;" />
   </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import { getPageDetailImgInfo} from '@/api/media/directImgtask';
import ElImageViewer from '@/views/media/shooting/imageviewer.vue';
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
export default { 
    props:{
        rowinfo:{ type: Number, default:0 },timee: { type: Number, default:0 }
    },
    components: { MyContainer,ElImageViewer,videoplayer}, 
   data() {
       return {
        btn1: false,
        btn2: false,
        btn3: false,
        btn4: false,
        btn5: false,
        left: false,
        right: true,
        oneindex: '',
        primary: 'primary',
        num: 3,
        pageLoading:false,
        imgtest: 'https://img.alicdn.com/imgextra/i2/725677994/O1CN01ksdbKH28vIoa7itH0_!!725677994-0-sm.jpg_430x430q90.jpg',
        videotest: 'http://*************:8004/media/video/20221202/1598502726654291968.mp4',
        dialogVisible:false,
        videoplayerReload:false,
        videoUrl: '',
        imgindex:0,
        imgList: [],
        showGoodsImage: false,
        data: {},
        datashow: false,
        indeximg: {},
        newmainImgList: []
        // clickIndex: '',
        // hoverIndex: ''
       };
   },
   watch: {
    timee: 'rowinfofunc'
    },
   created() {
    this.$nextTick(() => {
      // 禁用右键
      document.oncontextmenu = new Function("event.returnValue=false");
      // 禁用选择
      document.onselectstart = new Function("event.returnValue=false");
    });
  },
   mounted() {
        this.getlist();
    },
   methods: {
    rowinfofunc(){
      this.left = false;
      this.right = false;
  },
    whactclick(num){
        let _this = this;
        _this.num = num;
    },
    leftSlide(val){
        if(val==12){
            var doublewrapperCon=this.$refs.wrapperCon12
        }else if(val==22){
            var doublewrapperCon=this.$refs.wrapperCon22
        }
        this.oneindex =val;
        let left=doublewrapperCon.scrollLeft
        let num=0
        clearInterval(this.timer)
        this.timer=null
        this.timer=setInterval(()=>{
            if(!left||num>=300){
                // 停止滚动
                this.left = false;
                clearInterval(this.timer)
                this.timer=null
                return
            }else{
                this.right = true;
            }
            doublewrapperCon.scrollLeft=left-=30
            num+=30
        },30)
    },
    rightSlide(val){
        if(val==12){
            var doublewrapperCon=this.$refs.wrapperCon12
        }else if(val==22){
            var doublewrapperCon=this.$refs.wrapperCon22
        }
        this.oneindex =val;
        let left=doublewrapperCon.scrollLeft
        let scrollWidth=doublewrapperCon.scrollWidth
        let clientWidth=doublewrapperCon.clientWidth
        let num=0
        clearInterval(this.timer)
            this.timer=setInterval(()=>{
            if(left+clientWidth>=scrollWidth||num>=300){
                this.right = false;
                clearInterval(this.timer)
                return
            }else{
                this.left = true;
            }
            doublewrapperCon.scrollLeft=left+=30
            num+=30
        },20)
      },
      doubleleftSlide(val){
        if(val==1){
            var doublewrapperCon=this.$refs.doublewrapperCon1
        }else if(val==2){
            var doublewrapperCon=this.$refs.doublewrapperCon2
        }else if(val==31){
            var doublewrapperCon=this.$refs.doublewrapperCon31
        }
        this.oneindex =val;
            let left=doublewrapperCon.scrollLeft
            let num=0
            clearInterval(this.timer)
            this.timer=null
            console.log("左边距离1",left)
            console.log("左边滑动距离2",num)
            this.timer=setInterval(()=>{
                if(!left||num>=300){
                    // 停止滚动
                    this.left = false
                    console.log("停止滚动")
                    clearInterval(this.timer)
                    this.timer=null
                    return
                }else{
                    this.right = true
                }
                doublewrapperCon.scrollLeft=left-=130
                num+=130
                
            },30)
        },
      doublerightSlide(val){
        if(val==1){
            var doublewrapperCon=this.$refs.doublewrapperCon1
        }else if(val==2){
            var doublewrapperCon=this.$refs.doublewrapperCon2
        }else if(val==31){
            var doublewrapperCon=this.$refs.doublewrapperCon31
        }
        this.oneindex =val;
        let left=doublewrapperCon.scrollLeft
        let scrollWidth=doublewrapperCon.scrollWidth
        let clientWidth=doublewrapperCon.clientWidth
        let num=0
        clearInterval(this.timer)
            this.timer=setInterval(()=>{
            if(left+clientWidth>=scrollWidth||num>=300){
                this.right = false;
                clearInterval(this.timer)
                return
            }else{
                this.left = true;
            }
            doublewrapperCon.scrollLeft=left+=130
            num+=130
        },20)
      },
    showvideo(val){
        // this.videoplayerReload = false;
        this.videoplayerReload = true;
        this.dialogVisible = true;
        this.videoUrl = val;
    },
    closeVideoPlyer(){
        let _this = this;
        _this.dialogVisible = false;
        this.videoplayerReload = false;
    },
    //完成表单界面关闭图片
    async closeFunc() {
        this.showGoodsImage = false;
    },
    imgclick(time,index){
        // console.log("点击图片")
        this.imgList = [];

        // this.imgindex=index;
        if(time==1){
            // this.imgList = imgarray(this.data.mainImgList);
            this.imgindex=index[1];
            var imgList = [];
            var val = this.newmainImgList[index[0]];
            for(let num in val)
            {  
                imgList.push(val[num].url);
            }
            this.imgList = imgList;
        }else if(time==2){
            this.imgindex=index;
            var imgList = [];
            var val = this.data.photoImgList;
            for(let num in val)
            {  
                imgList.push(val[num].url);
            }
            this.imgList = imgList;
        }else if(time==3){
            console.log("点击图片111",index)
            // this.imgList = imgarray(this.data.skuImgList);
            // this.imgindex=index;
            var imgList = [];
            var val = this.newskuImgList[index[0]];
            this.imgindex=index[1];
            for(let num in val)
            {  
                imgList.push(val[num].url);
            }
            this.imgList = imgList;
        }else if(time==4){
            // this.imgList = imgarray(this.data.skuImgList);
            var imgList = [];
            var val = this.newdirectImgList[index[0]];
            this.imgindex=index[1];
            for(let num in val)
            {  
                imgList.push(val[num].url);
            }
            this.imgList = imgList;
        }else if(time==5){
            this.imgindex=index;
            // this.imgList = imgarray(this.data.skuImgList);
            var imgList = [];
            var val = this.data.detailImgList;
            for(let num in val)
            {  
                imgList.push(val[num].url);
            }
            this.imgList = imgList;
        }
        console.log("打印数组",this.imgList)
        // function imgarray(val){
        //     var imgList = [];
        //     for(let num in val)
        //     {  
        //         imgList.push(val[num].url);
        //         return imgList;
        //     }
        // }
        this.showGoodsImage = true;
    },
    async getlist(){
        let _this = this;
        _this.pageLoading= true;
        var res  =  await getPageDetailImgInfo({taskid: this.rowinfo});
        if(res?.success)
        {
            _this.data = res.data;
            _this.datashow = true;
            console.log("打印数据",_this.data)
            if(res.data.mainImgList.length>12){
                this.btn1 = true;
            }
            if(res.data.photoImgList.length>6){
                this.btn2 = true;
            }
            if(res.data.skuImgList.length>12){
                this.btn3 = true;
            }
            if(res.data.ohtersVedioList.length>6){
                this.btn4 = true;
            }
            if(res.data.directImgList.length>12){
                this.btn5 = true;
            }

            _this.indeximg.threenum = res.data.mainImgList!=null?Math.floor(res.data.mainImgList.length/12)+1:1;
            // _this.indeximg.twonum = res.data.mainImgList!=null?Math.floor(res.data.mainImgList.length/6)+1:1;
            console.log("多少个",res.data.mainImgList.length)
            console.log("打印第三节",_this.indeximg);
            _this.skuimg = res.data.skuImgList!=null?Math.floor(res.data.skuImgList.length/12)+1:1;
            _this.tocarimg = res.data.directImgList!=null?Math.floor(res.data.directImgList.length/12)+1:1;

            var data = res.data.mainImgList;
            var result = [];
            for(var i=0,len=data.length;i<len;i+=12){
            result.push(data.slice(i,i+12));
            }
            _this.newmainImgList = result;
            ///////////
            var data = res.data.skuImgList;
            var result = [];
            for(var i=0,len=data.length;i<len;i+=12){
            result.push(data.slice(i,i+12));
            }
            _this.newskuImgList = result;
            ////////////
            var data = res.data.directImgList;
            var result = [];
            for(var i=0,len=data.length;i<len;i+=12){
            result.push(data.slice(i,i+12));
            }
            _this.newdirectImgList = result;
            
            // _this.newmainImgList = toaaray(res.data.mainImgList);
            // _this.newskuImgList = toaaray(res.data.skuImgList);
            // _this.newdirectImgList = toaaray(res.data.directImgList);
            // console.log("分割",_this.newmainImgList)

            function toaaray(val){
                var data = val;
                var result = [];
                for(var i=0,len=data.length;i<len;i+=12){
                    result.push(data.slice(i,i+12));
                    return result;
                }
            }
        }
        _this.pageLoading= false;
    },
    mouseover(){
        
    }
   },
};
</script>
<style lang="scss" scoped>
.rowstyle{
    margin: 20px 220px;
}
.bancss{
    height: 220px;
    width: 100%;
    // border: 1px solid #eee;
}

.discloum ::-webkit-scrollbar-thumb{
  background: rgba(0, 0, 0, 0);
}
.danbancss{
    height: 130px;
    width: 100%;
    // border: 1px solid #eee;
}
::-webkit-scrollbar-thumb{
    background: rgba(0, 0, 0, 0);
}
.marginauto{
    margin: 0 50px;
    display: flex;
    flex-direction: column;
    height: 750px;
    overflow-y: auto;
}
.marginauto ::-webkit-scrollbar-thumb{
  background: rgba(0, 0, 0, 0);
}
#click-scroll-X {
    display: flex;
    align-items: center;
    .left_btn,.right_btn {
      font-size: 2.8rem;
      cursor: pointer;
    }
  .scroll_wrapper {
    width: 385px;
    overflow-x: scroll;
    // padding: 20px 20px;
    overflow: hidden;
    .scroll_list{
    display: flex;
    align-items: center;
    justify-content: space-between;
    // overflow: hidden;

        .item {
        width: 65px;
        height: 65px;
        display: flex;
        align-items: center;
        justify-content: center;
        // border: 1px solid rgb(223, 223, 223);
        box-sizing: border-box;
        flex-shrink: 0;
        }
    }
  }
}
.hoverBg{
  border-radius: 5px;
   border: 1px solid #da1f1f;
   color: #fff;
  border-radius: 8px;
 }
 .hoverimg{
  border: 1px solid #eee;
  border-radius: 8px;
 }

 .smaslling{
  width: 100%;
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.flexrow{
  display: flex;
  flex-direction: row;
}
.doubleitem{
    width: 100%;
    height: 138px;
    display: flex;
    flex-direction: column;
}
.outline{
  display: flex;
  overflow-x: hidden;
  flex-wrap: wrap;
  flex-direction: column;
  box-sizing: border-box;
  flex-shrink: 0;
}
.doubleitemm{
    box-sizing: border-box;
    width: 65px;
    height: 65px;
    // flex-shrink: 0;
}
.martop{
    margin-top: 30px;
}
.marauto{
    margin: 20px 30px;
}
.borderstyle{
    border: 1px solid #eee;
}
.content-colorimg{
    height: 65px;
    width: 65px;
}
.hiddcs{
    color: #eee;
}
.showcs{
    color: #409EFF;
    font-weight: 600;
    font-size: 22px;
}
.itemm {
    width: 370px;
    height: 140px;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    // border: 1px solid rgb(223, 223, 223);
    box-sizing: border-box;
    flex-shrink: 0;
}
.disbancss{
    width: 100px;
    height: 100%;
}
.flexaligncenter{
    display: flex; 
    flex-direction: column;
    align-items: center;
}
</style>

