<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="导入开始日期" end-placeholder="导入结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime" :clearable="false">
        </el-date-picker>
        <div>
          <queryCondition ref="refqueryCondition" :valueChanged.sync="topfilter" />
        </div>
        <el-input v-model.trim="ListInfo.shopName1" placeholder="店铺" maxlength="50" clearable class="publicCss" />
        <div style="display: flex;align-items: center;padding-top: 1px;">
          <el-checkbox v-model="ListInfo.noUseCatch" class="publicCss" style="width: 60px;">非缓存</el-checkbox>
        </div>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'agentStoreSummary202502151702'" :tablekey="'agentStoreSummary202502151702'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <!-- <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" /> -->
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { warehouselist, formatWarehouseNew, formatTime } from "@/utils/tools";
import { getExpressComanyAll, getExpressInfoData_Month, deleteExpressInfoData, getExpressComanyStationName, exportExpressInfoData_Month } from "@/api/express/express";
import dayjs from 'dayjs'
import queryCondition from "../../dailyCourierFee/components/queryCondition.vue";

const tableCols = [
  { sortable: 'custom', width: '110', align: 'center', label: '导入日期', formatter: (row) => formatTime(row.inportDate, "YYYY-MM-DD") },
  { sortable: 'custom', width: '110', align: 'center', prop: 'expressCompanyName', label: '快递公司', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'prosimstate', label: '快递公司站点', },
  { sortable: 'custom', width: '210', align: 'center', prop: 'warehouseName', label: '发货仓', },
  { sortable: 'custom',width: '210', align: 'center', prop: 'jsCount', label: '计数', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'platform', label: '平台', formatter: (row) => row.platformStr },
  { sortable: 'custom', width: '210', align: 'center', prop: 'shopName1', label: '店铺名称1', },
  // { sortable: 'custom', width: '210', align: 'center', prop: 'shopCode', label: '店铺编号', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'billingWeight', label: '快递重量', },
  { sortable: 'custom', width: '210', align: 'center', prop: 'additionalWeightFee', label: '续重费', },
  { sortable: 'custom', width: '210', align: 'center', prop: 'waybill', label: '面单', },
  { sortable: 'custom', width: '210', align: 'center', prop: 'totalFreight', label: '运费合计', },
]
export default {
  name: "agentStoreSummary",
  components: {
    MyContainer, vxetablebase, queryCondition
  },
  data() {
    return {
      topfilter: {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
      },
      formatWarehouseNew: formatWarehouseNew,
      timeRanges: [],
      that: this,
      ListInfo: {
        DataType: 12,
        yearmonth: null,
        currentPage: 1,
        pageSize: 50,
        orderBy: 'inportDate',
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        noUseCatch: false,//非缓存
        shopName1: null,//店铺
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
    }
  },
  async mounted() {
    this.ListInfo.yearmonth = formatTime(new Date(), 'YYYYMM');
    if (this.timeRanges && this.timeRanges.length == 0) {
      //默认给当前月第一天至今天
      this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
      this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
      this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
    }
    // await this.getList()
    await this.init()
  },
  methods: {
    async init() {
      this.$nextTick(() => {
        this.$refs.refqueryCondition.init()
      })
      const res = await getExpressComanyAll({});
      if (!res?.success) return
      this.expresscompanylist = res.data;
    },
    //导出
    async exportProps() {
      this.loading = true
      const res = await exportExpressInfoData_Month({ ...this.ListInfo, ...this.topfilter })
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', 'ERP导出月账单数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        //this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getExpressInfoData_Month({ ...this.ListInfo, ...this.topfilter })
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary

        let summary = data.summary || {}

        const resultsum = {};
        Object.entries(summary).forEach(([key, value]) => {
          resultsum[key] = formatNumber(value);
        });
        function formatNumber(number) {
          const options = {
            useGrouping: true,
          };
          return new Intl.NumberFormat('zh-CN', options).format(number);
        }
        this.summaryarry = resultsum
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
