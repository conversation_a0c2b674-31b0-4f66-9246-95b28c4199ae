<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="抖音供销订单日报" name="first1" style="height: 100%" v-if="checkPermission('DySupplyOrderDayReport')">
        <DySupplyOrderDayReport @ChangeActiveName2="ChangeActiveName2" ref="DySupplyOrderDayReport"
          @seriesCodingJump="seriesCodingJump" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="抖音供销编码日报" name="first2" style="height: 100%"
        v-if="checkPermission('DySupplyGoodCodeDayReport')" lazy>
        <DySupplyGoodCodeDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="DySupplyGoodCodeDayReport" @seriesCodingJump="seriesCodingJump" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="抖音供销商日报" name="first3" style="height: 100%" v-if="checkPermission('productReportDySupply')"
        lazy>
        <productReportDySupply @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="productReportDySupply" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="抖音供销明细日报" name="first4" style="height: 100%">
        <DySupplyDetailDayReport @ChangeActiveName2="ChangeActiveName2" ref="DySupplyDetailDayReport"
          style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="供货单" name="first5" style="height: 100%">
        <DySupplyOrder @ChangeActiveName2="ChangeActiveName2" ref="DySupplyOrder"
          style="height: 100%" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import DySupplyDetailDayReport from "./DySupplyDetailDayReport.vue";
import DySupplyOrderDayReport from "./DySupplyOrderDayReport.vue";
import DySupplyGoodCodeDayReport from "./DySupplyGoodCodeDayReport.vue";
import productReportDySupply from "./productReportDySupply.vue";
import DySupplyOrder from "./DySupplyOrder.vue";
export default {
  name: "productReportDySupplyIndex",
  components: {
    MyContainer, DySupplyDetailDayReport, DySupplyOrderDayReport, DySupplyGoodCodeDayReport, productReportDySupply,DySupplyOrder
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },
  async mounted() {
  },
  methods: {
    seriesCodingJump(row) {
      this.activeName = 'first4';
      this.$refs.DySupplyDetailDayReport.onSeriesCoding(row)
    },
    ChangeActiveName(activeName) {
      this.activeName = 'first1';
      this.$refs.DySupplyOrderDayReport.DySupplyGoodCodeDayReportArgument(activeName)
    },
    ChangeActiveName2(orderNo, No, Timerange, row) {
      this.$nextTick(() => {
        this.activeName = 'first4';
        this.$refs.DySupplyDetailDayReport.DySupplyDayReportArgument(orderNo, No, Timerange, row)
      });
    }
  },
};
</script>

<style lang="scss" scoped></style>
