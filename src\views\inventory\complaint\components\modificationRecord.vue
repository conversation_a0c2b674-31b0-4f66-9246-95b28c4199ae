<template>
  <MyContainer>
    <vxetablebase :id="'modificationRecord202302031421'" :tablekey="'modificationRecord202302031421'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template #annex="{ row }">
        <el-button type="text" @click="downloadAttachment(row)"
          :disabled="!row.annex || row.annex.length === 0">下载</el-button>
      </template>
    </vxetablebase>
    <!-- <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template> -->
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getEditHistoryBusinessComplaintRegister } from '@/api/operatemanage/operate'
import axios from 'axios';
import dayjs from 'dayjs'
import JSZip from 'jszip';
import { saveAs } from 'file-saver';
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'complaintType', label: '投诉类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'complaintReason', label: '投诉原因', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'proCode', label: '产品ID', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '款式编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'brandName', label: '产品负责采购', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'operateSpecialUserName', label: '产品负责运营专员', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'operateAssistantName', label: '产品负责运营助理', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'groupName', label: '产品编码挂靠组', },
  { width: '55', align: 'left', prop: 'picture', label: '图片', type: 'images', },
  { width: '55', align: 'center', prop: 'annex', label: '附件', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '订单编号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'editTime', label: '修改时间', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'editer', label: '修改人', },
]

export default {
  name: 'modificationRecord',
  components: {
    MyContainer, vxetablebase
  },
  props: {
    formDataInfo: {
      type: Object,
      default() {
        return {}
      }
    },
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
    }
  },

  async mounted() {
    this.formDataInfo = {
      ...this.formDataInfo,
      currentPage: 1,
      pageSize: 99999,
      orderBy: null,
      isAsc: false,
    }
    this.getList('search')
  },
  methods: {
    async downloadAttachment(row) {
      await this.hangrow(row);
      await this.downzip();
    },
    hangrow(row) {
      this.fileslist = [];
      this.loading = true;
      let billImgs = [row]
        .filter(item => item.annex.length > 0) // 过滤出有附件的项
        .map(item => {
          return item.annex.map((item1, index) => {
            let complaintType = item.complaintType ? item.complaintType : '';
            let proCode = item.proCode ? item.proCode : '';
            let businessId = `${complaintType}-${proCode}`;
            return {
              billImgs: item1.url, // 附件 URL
              neiname: item1.name, // 附件名称
              businessId: businessId // 拼接后的 businessId
            };
          });
        })
        .flat(); // 使用 flat 将多维数组变成一维

      return new Promise((resolve, reject) => {
        const downloadPromises = billImgs.map(async (item, index) => {
          try {
            if (!item.billImgs) {
              return;
            }
            await this.downloadFileMethod(item.billImgs, item.neiname, item.businessId);
          } catch (error) {
            console.error(`Error downloading file from ${item}:`, error);
            reject(error);
          }
        });

        Promise.all(downloadPromises)
          .then(() => {
            resolve();
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    async downloadFileMethod(url, neiname, name) {
        try {
            let response = await fetch(url);
            if(response.status != 200){
                return;
            }
            let blob = await response.blob();
            this.fileslist.push({ name: name, neiname: neiname, blob: blob });
        } catch (error) {
        }
    },
    async downzip() {
      const zip = new JSZip();
      const currentDate = new Date();
      const year = currentDate.getFullYear();
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const day = String(currentDate.getDate()).padStart(2, '0');
      const formattedDate = `${year}${month}${day}`;
      this.fileslist.forEach((file, index) => {
        zip.file(file.name + '/' + file.neiname, file.blob, { binary: true });
      });
      zip.generateAsync({ type: 'blob' }).then((content) => {
        saveAs(content, `pdf文件集合.zip`);
      });
      this.loading = false;
      return;
    },
    async getList(type) {
      // if (type == 'search') {
      //   this.ListInfo.currentPage = 1
      //   this.$refs.pager.setPage(1)
      // }
      this.loading = true
      const { data, success } = await getEditHistoryBusinessComplaintRegister({ ...this.formDataInfo, ...this.ListInfo })
      this.loading = false
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.picture = item.picture ? JSON.stringify(item.picture.map(item => { return { url: item } })) : '';
        })
        this.total = data.total
        this.summaryarry = data.summary
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 60px;
}

.editCss {
  width: 100%;
}

.containerCss {
  max-height: 90px;
  height: 90px;
  overflow-x: auto;
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep .el-select__tags-text {
  max-width: 30px;
}
</style>
