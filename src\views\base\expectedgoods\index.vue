<template>
    <container v-loading="pageLoading">
        <el-tabs v-model="activeName" style="height: 94%;">
            <el-tab-pane lazy label="预包编码" name="first" style="height: 100%;">
                <goodsrapidretirelog ref="goodsrapidretirelog"></goodsrapidretirelog>
            </el-tab-pane>
            <el-tab-pane lazy label="基础数据" name="second" style="height: 100%;">
                <goods ref="goods"></goods>
            </el-tab-pane>
            <el-tab-pane lazy label="加工记录" name="thirdly" style="height: 100%;">
                <prepackgoodsstreaterecord ref="prepackgoodsstreaterecord"></prepackgoodsstreaterecord>
            </el-tab-pane>
           
            <!-- <el-tab-pane lazy label="发货仓数据" name="fiveth" style="height: 100%;">
             <prepackinventory ref="prepackinventory"></prepackinventory>
         </el-tab-pane>  -->
            <el-tab-pane lazy label="加工清单批次" name="sixth" style="height: 100%;">
             <processingList ref="processingList"/>
         </el-tab-pane> 
        </el-tabs>
    </container>
</template>

<script>

import container from "@/components/my-container";
import goods from "./commpontes/goods.vue";
import prepackgoodsstreaterecord from "./commpontes/prepackgoodsstreaterecord.vue";
import goodsrapidretirelog from "./commpontes/goodsrapidretirelog.vue";
import prepackinventory from "./commpontes/prepackinventory.vue"
import processingList from "./commpontes/processingList.vue"
export default {
    name: 'YunHanAdminGoodstab',
    components: { container, goods, goodsrapidretirelog, prepackgoodsstreaterecord, prepacktbwarehouse, prepackinventory, processingList },

    data() {
        return {
            that: this,
            activeName: 'first',
            listLoading: false,
            pageLoading: false,
        };
    },

    async mounted() {
        // await this.onSearch();
    },

    methods: {
        async onSearch() {
            this.$nextTick(() => {
                if (this.activeName == 'first') this.$refs.goodsrapidretirelog.onSearch();
                if (this.activeName == 'second') this.$refs.goods.onSearch();
                if (this.activeName == 'thirdly') this.$refs.prepackgoodsstreaterecord.onSearch();
                if (this.activeName == 'fourth') this.$refs.prepacktbwarehouse.onSearch();
                if (this.activeName == 'fiveth') this.$refs.prepackinventory.onSearch();
            })
        },

    },
};
</script>


</style>