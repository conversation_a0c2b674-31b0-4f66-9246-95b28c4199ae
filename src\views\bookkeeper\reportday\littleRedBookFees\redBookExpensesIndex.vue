<template>
  <my-container>
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="小额打款" name="first1" style="height: 99%">
        <smallAmountPayments ref="refsmallAmountPayments" />
      </el-tab-pane>
      <el-tab-pane label="违规赔付" name="first2" style="height: 99%" lazy>
        <breachPayment ref="refbreachPayment" />
      </el-tab-pane>
      <el-tab-pane label="笔记营销" name="first3" style="height: 99%" lazy>
        <noteMarketing ref="refnoteMarketing" />
      </el-tab-pane>
      <el-tab-pane label="店铺余额" name="first4" style="height: 99%" lazy>
        <storeBalance ref="refstoreBalance" />
      </el-tab-pane>
      <el-tab-pane label="支付宝微信明细" name="first5" style="height: 99%" lazy>
        <alipayWeChatDetails ref="refalipayWeChatDetails" />
      </el-tab-pane>
      <el-tab-pane label="订单货款" name="first6" style="height: 99%" lazy>
        <orderPayment ref="reforderPayment" />
      </el-tab-pane>
      <el-tab-pane label="后台商品资料" name="first7" style="height: 99%" lazy>
        <backgroundProductLiterature ref="refbackgroundProductLiterature" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import smallAmountPayments from "./components/smallAmountPayments.vue";
import breachPayment from "./components/breachPayment.vue";
import noteMarketing from "./components/noteMarketing.vue";
import storeBalance from "./components/storeBalance.vue";
import alipayWeChatDetails from "./components/alipayWeChatDetails.vue";
import orderPayment from "./components/orderPayment.vue";
import backgroundProductLiterature from "./components/backgroundProductLiterature.vue";
export default {
  name: "redBookExpensesIndex",
  components: {
    MyContainer, smallAmountPayments, breachPayment, noteMarketing, storeBalance, alipayWeChatDetails, orderPayment, backgroundProductLiterature
  },
  data() {
    return {
      activeName: "first1",
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped></style>
