<template>
  <my-container v-loading="pageLoading" style="height: 98%;">
    <template #header>
      <div style="margin: 10px 0;">
        <div>
          <el-date-picker style="width: 290px; margin-right: 10px;" v-model="timerange" type="daterange"
            :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始时间"
            end-placeholder="结束时间">
          </el-date-picker>
          <el-select filterable clearable v-model="filter.shopCode" placeholder="店铺"
            style="width: 150px;margin-right: 10px;">
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
            </el-option>
          </el-select>
          <div style="display: inline-block; margin-right: 10px; margin-bottom: 10px;">
            <inputYunhan ref="productCode" :inputt.sync="filter.productCode" v-model="filter.productCode"
              placeholder="产品ID/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="50" :maxlength="3998"
              @callback="callbackGoodsCode" title="产品ID">
            </inputYunhan>
          </div>
          <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组"
            style="width: 130px; margin-right: 10px;">
            <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select filterable v-model="filter.operateSpecialId" placeholder="运营专员" clearable style="width: 130px; margin-right: 10px;">
            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
          <el-select filterable v-model="filter.userId" placeholder="运营助理" clearable style="width: 130px; margin-right: 10px;" >
            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
          <el-button type="primary" @click="onSearch" style="width: 80px; margin-right: 5px;">搜索</el-button>
          <el-button type="primary" @click="onclear">导入</el-button>
          <el-button type="primary" @click="onExport">导出</el-button>
        </div>
      </div>
    </template>
    <template>
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
        style="height: 712px;" :summaryarry="summaryarry" :showsummary='true' :tableData='list' :tableCols='tableCols'
        :isSelection="false" :isSelectColumn="false" :loading="listLoading">
      </ces-table>
    </template>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile" :on-success="onUploadSuccess"
          :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="趋势图" :visible.sync="drawerVisible" width="80%" v-dialogDrag @close="handleDialogClose">
      <div style="width:100%;display: inline-block;">
        <span style="display:inline-block;margin-right: 20px;">
          <el-date-picker size="mini" style="position: relative; top: 1px; width: 300px;" type="daterange" align="right"
            unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
            v-model="createdtimerange">
          </el-date-picker>
        </span>
        <span>
          <el-button type="primary" @click="showchart">查询</el-button>
        </span>
      </div>
      <div>
        <span>
          <buschar v-if="quantityprocessed.visible" ref="drawerbuschar" :analysisData="quantityprocessed.data">
          </buschar>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="drawerVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop'
import inputYunhan from "@/components/Comm/inputYunhan";
import buschar from '@/components/Bus/buschar'
import dayjs from 'dayjs'
import { importPddBackGroundVidioReportList, getPddBackGroundVidioReportAnasilys, getPddBackGroundVidioReportList, exportPddBackGroundVidioReportList } from '@/api/operatemanage/pddbgmanage/pddbgmanage'
const tableCols = [
  { istrue: true, prop: 'yearMonthDay', label: '日期', sortable: 'custom', width: '120', type: 'custom', formatter: (row) => { if (!row.yearMonthDay) { return " "; } else { return dayjs(row.yearMonthDay).format('YYYY-MM-DD') } } },
  { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom', width: '150', formatter: (row) => row.shopName, type: 'custom' },
  { istrue: true, prop: 'productCode', label: 'ID', width: '150', sortable: 'custom' },
  { istrue: true, prop: 'productName', label: '名称', sortable: 'custom', width: '255' },
  { istrue: true, prop: 'groupId', exportField: 'groupName', label: '运营组', sortable: 'custom', width: '120', formatter: (row) => row.groupName, type: 'custom' },

  { istrue: true, prop: 'daiHuoRen', label: '带货人', width: '80', sortable: 'custom', formatter: (row) => row.daiHuoRen || ' ' },

  { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', width: '80', sortable: 'custom', formatter: (row) => row.operateSpecialName || ' ' },
  { istrue: true, prop: 'userId', label: '运营助理', width: '80', sortable: 'custom', formatter: (row) => row.userName || ' ' },

  { istrue: true, prop: 'vidioDescription', label: '视频描述', sortable: 'custom', width: '240' },
  { istrue: true, prop: 'vidioOrderAmountMargin', label: '成交额', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'vidioOrderCountMargin', label: '成交订单数', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'vidioOrderPeopleCountMargin', label: '成交人数', sortable: 'custom', width: '100' },
  { istrue: true, display: true, label: '趋势图', width: '90', style: "color:red;cursor:pointer;", formatter: (row) => '趋势图', type: 'click', handle: (that, row, column) => that.showchart(row) },
];

export default {
  name: 'pddVideoData',
  components: { MyContainer, cesTable, inputYunhan, buschar },
  data() {
    return {
      feedId: null,
      shopName: null,
      productCode: null,
      fileparm: {},
      createdtimerange: [],
      drawerVisible: false,
      quantityprocessed: { visible: false, title: "", data: {} },
      listLoading: false,
      pageLoading: false,
      pager: { orderBy: '', isAsc: true },
      total: 0,
      summaryarry: {},
      that: this,
      list: [],
      sels: [],
      fileList: [],
      tableCols: tableCols,
      timerange: [],
      directorList:[],
      filter: {
        productCode: null,
        groupId: null,
        startTime: null,
        endTime: null,
        operateSpecialId: null,
        userId: null
      },
      shopList: [],//店铺
      grouplist: [],//运营组
      uploadLoading: false,
      dialogVisible: false,
      fileHasSubmit: false,
    };
  },

  async mounted() {
    await this.init();
    await this.onSearch()
  },
  async created() {
    await this.getShopList();
    await this.getDirectorlist();
  },
  methods: {
    async getDirectorlist() {
      const res1 = await getDirectorList({})
      this.directorList = [{ key: '0', value: '未知' }].concat(res1.data || []);
    },
    handleDialogClose() {
      this.createdtimerange = [];
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },

    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importPddBackGroundVidioReportList(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.onSearch()
    },

    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },

    async onUploadChange(file, fileList) {
      // let list = [];
      // list.push(file);
      this.fileList = fileList;
    },

    onUploadRemove(file, fileList) {
      this.fileList = []
    },

    async init() {
      var date1 = new Date(); date1.setDate(date1.getDate() - 1);
      var date2 = new Date(); date2.setDate(date2.getDate() - 1);
      this.timerange = [];
      this.timerange[0] = this.datetostr(date1);
      this.timerange[1] = this.datetostr(date2);
    },

    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },

    //趋势图
    async showchart(row) {
      if (row.hasOwnProperty('feedId')) {
        this.feedId = row.feedId;
      } else {
        this.feedId = this.feedId;
      }
      if (row.hasOwnProperty('shopName')) {
        this.shopName = row.shopName;
      } else {
        this.shopName = this.shopName;
      }
      if (row.hasOwnProperty('productCode')) {
        this.productCode = row.productCode;
      } else {
        this.productCode = this.productCode;
      }

      if (this.createdtimerange.length == 0) {
        var startTime = null;
        var endTime = null;
        var endTime = new Date(this.timerange[1]);
        var startTime = new Date(endTime);
        startTime.setDate(endTime.getDate() - 30);
        // 将日期转换为字符串
        var endTime = endTime.toISOString().split('T')[0];
        var startTime = startTime.toISOString().split('T')[0];
        this.createdtimerange = [];//回显
        this.createdtimerange.push(startTime, endTime);//回显
      } else {
        var startTime = null;
        var endTime = null;
        if (this.createdtimerange && this.createdtimerange.length > 1) {
          var startTime = this.createdtimerange[0];
          var endTime = this.createdtimerange[1];
        }
      }
      const params = { feedId: this.feedId, productCode:this.productCode, startTime, endTime };
      if (params === false) {
        return;
      }
      const { data } = await getPddBackGroundVidioReportAnasilys(params);
      this.quantityprocessed.visible = true;
      this.quantityprocessed.data = data
      this.quantityprocessed.title = data.legend[0]
      this.$nextTick(() => {
        this.$refs.drawerbuschar.initcharts();
      });
      this.drawerVisible = true;
    },
    //产品ID
    async callbackGoodsCode(val) {
      this.filter.productCode = val;
    },
    //导入弹窗
    onclear() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist();
    },
    async getlist() {
      if (this.filter.productCode && this.filter.productCode.length > 3997) {
        this.$message.error('输入内容超过最大限制');
        return;
      }
      this.sels = [];
      this.selids = [];
      //开始发货时间
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.timerange && this.timerange.length > 1) {
        this.filter.startTime = this.timerange[0];
        this.filter.endTime = this.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = {
        ...pager,
        ...page,
        ...this.filter
      }
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await getPddBackGroundVidioReportList(params)
      this.listLoading = false;
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      this.list = res.data.list;
      this.summaryarry = res.data.summary
    },
    //获取店铺、运营组
    async getShopList() {
      const res1 = await getAllShopList({ platforms: [2] });
      this.shopList = [];
      res1.data?.forEach(f => {
        if (f.isCalcSettlement && f.shopCode)
          this.shopList.push(f);
      });
      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
    },
    //排序查询
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    async onExport(){
      if (this.filter.productCode && this.filter.productCode.length > 3997) {
        this.$message.error('输入内容超过最大限制');
        return;
      }
      this.sels = [];
      this.selids = [];
      //开始发货时间
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.timerange && this.timerange.length > 1) {
        this.filter.startTime = this.timerange[0];
        this.filter.endTime = this.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = {
        ...pager,
        ...page,
        ...this.filter
      }
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await exportPddBackGroundVidioReportList(params);
      this.listLoading = false;
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute("download", '拼多多视频数据导出_' + new Date().toLocaleString() + '.xlsx');
      aLink.click();
    },
  },
};
</script>

<style lang="scss" scoped>
.publicCss {
  width: 220px;
  margin-right: 10px;
}
</style>
