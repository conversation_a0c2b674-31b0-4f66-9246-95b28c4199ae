<template>
    <MyContainer v-loading="loading">
        <template #header>
            <div class="top">
                <el-select v-model="ruleForm.wmsId" placeholder="退件仓" class="publicCss" clearable filterable
                    @change="changeWareHouse">
                    <el-option v-for="item in wareHouseList" :key="item.wms_co_id" :label="item.name"
                        :value="item.wms_co_id" />
                </el-select>
                <el-button type="primary" @click="getAllSetting(ruleForm.wmsId)">刷新当前数据</el-button>
            </div>
        </template>
        <el-form ref="form" label-width="130px" :model="ruleForm" :rules="rules" style="width: 40%;">
            <el-form-item label="最小拆分金额" prop="totalCostMin">
                <el-input-number v-model="ruleForm.totalCostMin" :min="0" :max="999999999" placeholder="最小拆分金额"
                    :precision="2" :controls="false" style="width: 100%;" />
            </el-form-item>
            <el-form-item label="最大拆分编码数" prop="goodsCountMax">
                <el-input-number v-model="ruleForm.goodsCountMax" :min="0" :max="999999999" placeholder="最小拆分金额"
                    :precision="0" :controls="false" style="width: 100%;" />
            </el-form-item>
            <el-form-item label="丢弃品牌">
                <el-input v-model="ruleForm.discardBrands" placeholder="丢弃品牌" maxlength="50" clearable />
            </el-form-item>
            <el-form-item label="当前拆分批次">
                <el-input v-model="ruleForm.currBatch" disabled placeholder="当前拆分批次" />
            </el-form-item>
            <el-form-item label="是否重新编排货架">
                <el-select v-model="ruleForm.isRearrange" placeholder="是否重新排列" class="publicCss" clearable
                    style="width: 100%;">
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                </el-select>
            </el-form-item>
            <el-form-item label="区域设置" prop="areas">
                <el-button type="text" @click="addAreaSetting">新增区域设置</el-button>
                <el-table :data="ruleForm.areas" style="width: 100%" height="380" border>
                    <el-table-column prop="area" label="区域" width="180">
                        <template #default="{ row }">
                            <el-input v-model="row.area" placeholder="区域" maxlength="50" clearable />
                        </template>
                    </el-table-column>
                    <el-table-column prop="shelve" label="货架数量" width="180">
                        <template #default="{ row }">
                            <el-input-number v-model="row.shelve" :min="0" :max="9999999" placeholder="货架数量"
                                :precision="0" :controls="false" style="width: 100%;" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="address" label="操作" align="center">
                        <template #default="{ $index }">
                            <i class="el-icon-remove" @click="delProps($index)"></i>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="submitForm('form')" v-throttle="1000">提交</el-button>
            </el-form-item>
        </el-form>
    </MyContainer>
</template>

<script>
import MyContainer from '@/components/my-container'
import { pageGetTbWarehouseAsync } from "@/api/inventory/prepack.js"
import request from '@/utils/request'
const api = '/api/verifyOrder/Split/'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
export default {
    components: {
        MyContainer,
        vxetablebase
    },
    data() {
        return {
            api,
            that: this,
            loading: false,
            wareHouseList: [],
            query: {
                wmsId: null,
                expressNo: null,
                isGoodsCode: false,
                wmsName: null
            },
            ruleForm: {
                wmsId: null,
                totalCostMin: undefined,
                goodsCountMax: undefined,
                discardBrands: null,
                currBatch: null,
                isRearrange: null,
                areas: []
            },
            rules: {
                totalCostMin: [{ required: true, message: '请输入最小拆分金额', trigger: 'blur' }],
                goodsCountMax: [{ required: true, message: '请输入最大拆分编码数', trigger: 'blur' }],
                // discardBrands: [{ required: true, message: '请输入丢弃品牌', trigger: 'blur' }],
                // isRearrange: [{ required: true, message: '请选择是否重新排列', trigger: 'blur' }],
                // areas: [{ required: true, message: '请输入区域设置', trigger: 'blur' }]
            }
        }
    },
    async mounted() {
        await this.getWareHouse()
        // this.getAllSetting()
    },
    methods: {
        delProps(i) {
            this.$confirm('此操作将删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.ruleForm.areas.splice(i, 1)
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        addAreaSetting() {
            if (!this.ruleForm.wmsId) return this.$message.error('请先选择仓库')
            this.ruleForm.areas.push({
                area: '',
                shelve: undefined
            })
        },
        async getAllSetting(wmsId) {
            if (!wmsId) return this.$message.error('请选择仓库')
            this.loading = true
            try {
                const { data, success } = await request.post(api + 'GetSplitSetting', { wmsId })
                if (success) {
                    this.ruleForm = data
                }
            } catch (error) {
                this.$message.error('获取拆包设置失败')
            } finally {
                this.loading = false
            }
        },
        submitForm(formName) {
            if (!this.ruleForm.wmsId) return this.$message.error('请选择仓库')
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    this.loading = true
                    const { success } = await request.post(api + 'SaveSplitSetting', this.ruleForm)
                    if (success) {
                        this.$message.success('提交成功!');
                        this.getAllSetting(this.ruleForm.wmsId)
                        this.loading = false
                    }
                } else {
                    return this.$message.error('请填写完整信息');

                }
            });
        },
        changeWareHouse(e) {
            this.ruleForm.wmsName = e ? this.wareHouseList.find(item => item.wms_co_id == e).name : null
            this.getAllSetting(e)
        },
        async getWareHouse() {
            const params = {
                currentPage: 1,
                pageSize: 1000,
                orderBy: null,
                isAsc: false,
            }
            const { data: { list } } = await pageGetTbWarehouseAsync(params)
            this.wareHouseList = list
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    align-items: center;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.el-icon-remove {
    color: red;
    cursor: pointer;
    font-size: 20px;
}
</style>