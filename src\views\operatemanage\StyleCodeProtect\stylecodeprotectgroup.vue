<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button style="padding: 0;margin: 0;border: none;">
                <el-input v-model.trim="filter.styleCode" clearable placeholder="系列编码" style="width:140px;"
                    :maxlength="20" />
            </el-button>
            <el-button style="padding: 0;margin: 0;border: none;">
                <el-select filterable v-model="filter.protectGroupIds" placeholder="保护小组" style="width: 170px" clearable
                    multiple collapse-tags>
                    <el-option v-for="item in directorGroupList" :key="'bh2' + item.key" :label="item.value"
                        :value="item.key" />
                </el-select>
            </el-button>

            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="addProtectShow">添加</el-button>
            <el-button type="primary" @click="onImport">导入</el-button>
            <el-button type="primary" @click="onImportExcel">下载导入模板</el-button>
            <el-button type="primary" @click="onEnd">结束保护</el-button>
        </template>

        <vxetablebase :id="'stylecodeprotectgroup20231019'" :border="true" :align="'center'"
            :tablekey="'stylecodeprotectgroup20231019'" ref="table2" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' @select='selectchange' :isSelectColumn="true" :showsummary='true'
            :tablefixed='true' :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols'
            :loading="listLoading" style="width:100%;height:100%;margin: 0" :xgt="9999">
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>


        <el-dialog title="添加保护系列及小组" :visible.sync="addProtectDialogData.visible" width="400px"
            :close-on-click-modal="false" v-dialogDrag>
            <span>

                <el-input v-model.trim="addProtectDialogData.styleCode" clearable placeholder="系列编码"
                    style="width:140px;" :maxlength="20" />

                <el-select filterable v-model="addProtectDialogData.protectGroupId" placeholder="保护小组"
                    style="width: 140px" clearable>
                    <el-option v-for="item in directorGroupList" :key="'bh3' + item.key" :label="item.value"
                        :value="item.key" />
                </el-select>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="addProtectDialogData.visible = false">关闭</el-button>
                <el-button @click="addProtectSave" type="primary" :loading="addProtectDialogData.loading">确定</el-button>
            </span>
        </el-dialog>



        <el-dialog title="导入保护系列及小组" :visible.sync="importProtectDialogData.visible" width="30%"
            :close-on-click-modal="false" v-dialogDrag>
            <span>
                <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2" :on-change="onUploadChange2"
                    :on-remove="onUploadRemove2">
                    <template #trigger>
                        <el-button size="small" type="primary"
                            :loading="importProtectDialogData.loading">选取文件</el-button>
                    </template>
                    <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                        :loading="importProtectDialogData.loading" @click="onSubmitupload2">上传</my-confirm-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="importProtectDialogData.visible = false">关闭</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>
<script>
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import { GetStyleCodeProtectGroupPageList, InsertStyleCodeProtectGroup, EndStyleCodeProtectGroup, ImportStyleCodeProtectGroup } from '@/api/operatemanage/stylecodeprotect'

const tableCols = [
    { istrue: true, fixed: 'left', width: '40', type: "checkbox" },
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'protectGroupId', label: '保护小组', sortable: 'custom', width: '160', formatter: (row) => row.protectGroupName },
    { istrue: true, prop: 'status', label: '状态', sortable: 'custom', width: '120', formatter: (row) => row.status == 1 ? "保护中" : "已结束保护" },
    { istrue: true, prop: 'protectStartTime', label: '开始时间', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'protectEndTime', label: '结束时间', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'createdUserName', label: '设置人', sortable: 'custom', width: '160' },
    //{ istrue: true, prop: 'createdTime', label: '设置时间', sortable: 'custom', width: '160' },
];
export default {
    name: "stylecodeprotectgroup",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, vxetablebase
    },
    data() {
        return {
            that: this,
            directorGroupList: [],
            filter: {
                styleCode: null,
                protectGroupIds: [],
            },
            tableCols: tableCols,
            total: 0,
            datalist: [],
            pager: { OrderBy: "protectStartTime", IsAsc: false },
            sels: [], // 列表选中列
            selids: [],
            listLoading: false,
            pageLoading: false,
            summaryarry: {},

            addProtectDialogData: {
                visible: false,
                loading: false,
                styleCode: null,
                protectGroupId: null,
                addData: [],
            },

            fileList: [],
            importProtectDialogData: {
                visible: false,
                loading: false,
            }

        };
    },
    async mounted() {
        await this.onSearch();
        await this.getloadgroupselect();
    },
    async created() {
    },
    methods: {
        async getloadgroupselect() {
            const res1 = await getDirectorGroupList({});
            this.directorGroupList = res1.data;
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getCondition() {
            let pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            return params;
        },
        async getList() {
            const params = this.getCondition();
            if (params === false) {
                return;
            }
            this.sels = [];
            console.log(params);
            this.listLoading = true;
            const res = await GetStyleCodeProtectGroupPageList(params);
            this.listLoading = false;
            console.log(res);
            this.total = res.data?.total;
            this.datalist = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },

        addProtectShow() {
            this.addProtectDialogData.styleCode = null;
            this.addProtectDialogData.protectGroupId = null;
            this.addProtectDialogData.addData = [];
            this.addProtectDialogData.visible = true;
        },
        async addProtectSave() {
            if (!this.addProtectDialogData.styleCode || !this.addProtectDialogData.protectGroupId) {
                this.$message({ type: 'warning', message: '请填写款式和小组' });
                return;
            }
            this.addProtectDialogData.loading = true;
            this.addProtectDialogData.addData = [{ styleCode: this.addProtectDialogData.styleCode, protectGroupId: this.addProtectDialogData.protectGroupId }];
            let res = await InsertStyleCodeProtectGroup(this.addProtectDialogData.addData);
            this.addProtectDialogData.loading = false;
            if (res?.success == true) {
                this.$message({ type: 'success', message: '添加成功' });
                this.addProtectDialogData.visible = false;
                await this.onSearch();
            }
        },
        async onEnd() {
            if (!this.selids || this.selids.length <= 0) {
                this.$message({ type: 'warning', message: '请至少勾选一行数据' });
                return;
            }
            this.$confirm('确定要结束勾选的数据吗？', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                let res = await EndStyleCodeProtectGroup(this.selids);
                if (res?.success == true) {
                    this.$message({ type: 'success', message: '操作成功' });
                    await this.onSearch();
                }
            }).catch(() => {
            });
        },


        onImportExcel() {
            window.open("/static/excel/operatemanage/系列编码管控-保护系列表编码设置导入模板.xlsx", "_blank");
        },
        onImport() {
            this.importProtectDialogData.visible = true
        },
        async onUploadChange2(file, fileList) {
            this.fileList = fileList;
        },
        async onUploadRemove2(file, fileList) {
            this.fileList = [];
        },
        async uploadFile2(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.importProtectDialogData.loading = true;
            const form = new FormData();
            form.append("upfile", item.file);
            const res = await ImportStyleCodeProtectGroup(form);
            this.importProtectDialogData.loading = false;
            if (res?.success) {
                this.$message({ message: '导入成功', type: "success" });
                this.importProtectDialogData.visible = false;
                await this.onSearch();
            }
        },
        async uploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        async onSubmitupload2() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload2.submit()
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
