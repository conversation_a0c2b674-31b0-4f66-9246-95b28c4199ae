<template>
    <MyContainer>
      <template #header>
        <div class="top">
            <el-form class="ad-form-query" :inline="true" :model="ListInfo" @submit.native.prevent>
                <el-form-item label="日期">
                    <el-date-picker class="publicCss" v-model="ListInfo.timerange" type="daterange" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" :clearable="false"
                    end-placeholder="结束" :picker-options="pickerOptions" @change="getList('search')" style="width: 249px;">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="店铺">
                    <el-input v-model.trim="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="startImport">导入</el-button>
                </el-form-item> 
            </el-form>
        </div>
      </template>
      <vxetablebase :id="'advanceCompensation202505261640'"
        :tablekey="'advanceCompensation202505261640'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
        :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
        :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0"
        :loading="loading" :height="'100%'" :border="true">
      </vxetablebase>
      <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template>
  
      <el-dialog title="导入数据" :visible.sync="dialogVisible" width="35%" v-dialogDrag :close-on-click-modal="false">
        <div class="upload-section">
          <div class="upload-row">
            <label class="required-label">
              <span class="required-mark">*</span> 日期选择：
            </label>
            <el-date-picker class="upload-month" v-model="yearMonthDay" type="date" format="yyyyMMdd" value-format="yyyyMMdd"
              placeholder="请选择月份" :clearable="false" />
          </div>
          <div class="upload-row">
            <el-upload ref="upload" class="upload-area" :auto-upload="false" :multiple="false" :limit="1" action
              accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
              :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button size="small" type="success" :loading="uploadLoading" @click="onSubmitUpload" class="upload-btn">
                {{ uploadLoading ? '上传中' : '上传' }}
              </el-button>
            </el-upload>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </MyContainer>
  </template>
  
  <script>
  import MyContainer from "@/components/my-container";
  import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
  import dayjs from 'dayjs'
  import { formatTime } from "@/utils";
  import {importAdvanceCompensation, getAdvanceCompensationPageList } from '@/api/financial/yyfyday'
  const tableCols = [
    { sortable: 'custom', width: '100', align: 'center', prop: 'yearMonthDay', label: '日期', },
    { sortable: 'custom', width: '200', align: 'center', prop: 'shopName', label: '店铺名称', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'orderNumber', label: '订单编号', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'deductible', label: '扣除金额', },
    { sortable: 'custom', width: '140', align: 'center', prop: 'compensationTime', label: '赔付时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'compensationScenarioName', label: '赔付场景名称', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'financialType', label: '财务类型', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'defaultOrderNumber', label: '违约单条数', },
  ]
  const startTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
  const endTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
  export default {
    name: "advanceCompensation",
    components: {
      MyContainer, vxetablebase
    },
    data() {
      return {
        yearMonthDay: null,//导入时间
        dialogVisible: false,//导入弹窗
        fileList: [],//上传文件列表
        uploadLoading: false,//上传按钮loading
        fileparm: {},//上传文件参数
        that: this,
        ListInfo: {
          currentPage: 1,
          pageSize: 50,
          orderBy: null,
          isAsc: false,
          timerange: [startTime, endTime],
          startTime: null,
          endTime: null,
          shopName: null,
        },
        tableCols,
        tableData: [],
        summaryarry: {},
        total: 0,
        loading: false,
        pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
      }
    },
    async mounted() {
      await this.getList()
    },
    methods: {
      //上传文件
      onUploadRemove(file, fileList) {
        this.fileList = []
      },
      async onUploadChange(file, fileList) {
        this.fileList = fileList;
      },
      onUploadSuccess(response, file, fileList) {
        fileList.splice(fileList.indexOf(file), 1);
        this.fileList = [];
        this.dialogVisible = false;
      },
      async onUploadFile(item) {
        if (!item || !item.file || !item.file.size) {
          this.$message({ message: "请先上传文件", type: "warning" });
          return false;
        }
        this.uploadLoading = true
        const form = new FormData();
        form.append("upfile", item.file);
        form.append("yearMonthDay", this.yearMonthDay);
        var res = await importAdvanceCompensation(form);
        if (res?.success)
          this.$message({ message: "上传成功,正在导入中...", type: "success" });
        this.uploadLoading = false
        this.dialogVisible = false;
        await this.getList()
      },
      onSubmitUpload() {
        if (!this.yearMonthDay) {
          this.$message({ message: "请选择月份", type: "warning" });
          return false;
        }
        if (this.fileList.length == 0) {
          this.$message({ message: "请先上传文件", type: "warning" });
          return false;
        }
        this.$refs.upload.submit();
      },
      //导入弹窗
      startImport() {
        this.fileList = []
        this.yearMonthDay = dayjs().subtract(1, 'day').format('YYYYMMDD')
        this.dialogVisible = true;
      },
      async getList(type) {
        if (type == 'search') {
          this.ListInfo.currentPage = 1
          this.$refs.pager.setPage(1)
        }
        this.ListInfo.startTime = null
        this.ListInfo.endTime = null;
        if (this.ListInfo.timerange) {
            this.ListInfo.startTime = this.ListInfo.timerange[0];
            this.ListInfo.endTime = this.ListInfo.timerange[1];
        }
        this.loading = true
        const { data, success } = await getAdvanceCompensationPageList(this.ListInfo)
        if (success) {
          this.tableData = data.list
          this.tableData.forEach(item => {
            item.storageTime = item.storageTime ? dayjs(item.storageTime).format('YYYY-MM-DD') : ''
          })
          this.total = data.total
          this.summaryarry = data.summary
          this.loading = false
        } else {
          this.$message.error('获取列表失败')
        }
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }
  </script>
  
  <style scoped lang="scss">
  .top {
    display: flex;
    margin-bottom: 10px;
  
    .publicCss {
      width: 200px;
      margin-right: 5px;
    }
  }
  
  .upload-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 12px 0;
  }
  
  .upload-row {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .required-label {
    font-weight: 500;
    color: #333;
  }
  
  .required-mark {
    color: red;
    margin-right: 4px;
  }
  
  .upload-month {
    width: 200px;
  }
  
  .upload-area {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .upload-btn {
    margin-left: 0;
  }
  </style>