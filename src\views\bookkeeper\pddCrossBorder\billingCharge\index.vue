<template>
  <MyContainer>
    <el-tabs v-model="levelOne" style="height: 95%;">
      <el-tab-pane label="TEMU全托" name="levelOne1" :lazy="true" style="height: 100%;">
        <el-tabs v-model="levelTwo1" style="height: 95%;">
          <el-tab-pane label="TEMU全托弃货" name="levelTwo1A" style="height: 98%;">
            <derelict />
          </el-tab-pane>
          <el-tab-pane label="TEMU全托环保费" name="levelTwo1B" :lazy="true" style="height: 98%;">
            <protection />
          </el-tab-pane>
          <el-tab-pane label="TEMU全托售后赔付" name="levelTwo1C" :lazy="true" style="height: 98%;">
            <afterSaleCompensate />
          </el-tab-pane>
          <el-tab-pane label="TEMU全托仓储综合服务费" name="levelTwo1D" :lazy="true" style="height: 98%;">
            <returnServiceCharge />
          </el-tab-pane>
          <el-tab-pane label="TEMU全托商品补寄赔付金" name="levelTwo1E" :lazy="true" style="height: 98%;">
            <productIndemnity />
          </el-tab-pane>
          <el-tab-pane label="TEMU全托违规扣款" name="levelTwo1F" :lazy="true" style="height: 98%;">
            <penaltyDeductio />
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>

      <el-tab-pane label="TEMU半托" :lazy="true" name="levelOne2" style="height: 100%;">
        <el-tabs v-model="levelTwo2" style="height: 95%;">
          <el-tab-pane label="TEMU半托账单费用" name="levelTwo2A" :lazy="true" style="height: 98%;">
            <billingChargeTemu ref="billingChargeTemu"  />
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>

      <el-tab-pane label="SHEIN全托" :lazy="true" name="levelOne3" style="height: 100%;">
        <el-tabs v-model="levelTwo3" style="height: 95%;">
          <el-tab-pane label="SHEIN全托账单费用" :lazy="true" name="levelTwo3A" style="height: 98%;">
            <SheInBill ref="SheInBill"  />
          </el-tab-pane>
          <el-tab-pane label="SHEIN全托库存损耗明细" :lazy="true" name="levelTwo3B" style="height: 98%;">
            <SheInInventoryLossDetail ref="SheInInventoryLossDetail"  />
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
      
      <el-tab-pane label="SHEIN自营" :lazy="true" name="levelOne4" style="height: 100%;">
        <el-tabs v-model="levelTwo4" style="height: 95%;">
          <el-tab-pane label="SHEIN自营账单费用" :lazy="true" name="levelTwo4A" style="height: 98%;">
            <SelfOperatedBill ref="SelfOperatedBill"  />
          </el-tab-pane>
          <el-tab-pane label="SHEIN自营库存损耗明细" :lazy="true" name="levelTwo4B" style="height: 98%;">
            <SelfOperatedInventoryLossDetails ref="SelfOperatedInventoryLossDetails" />
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
      <el-tab-pane label="SHEIN半托" :lazy="true" name="levelOne5" style="height: 100%;">
        <el-tabs v-model="levelTwo5" style="height: 95%;">
          <el-tab-pane label="SHEIN半托账单费用" :lazy="true" name="levelTwo5A" style="height: 98%;">
            <HalfBillingFee ref="HalfBillingFee"  />
          </el-tab-pane>
         
        </el-tabs>
      </el-tab-pane>
    
    </el-tabs>
  </MyContainer>
</template>
<script>
import MyContainer from "@/components/my-container";
import derelict from './derelict.vue'
import afterSaleCompensate from './afterSaleCompensate.vue'
import penaltyDeductio from './penaltyDeductio.vue'
import productIndemnity from './productIndemnity.vue'
import protection from './protection.vue'
import returnServiceCharge from './returnServiceCharge.vue'
import billingChargeTemu from './billingChargeTemu.vue'
import SheInBill from './SheInBill.vue'
import SheInInventoryLossDetail from './SheInInventoryLossDetail.vue'
import SelfOperatedBill from './SelfOperatedBill.vue'
import SelfOperatedInventoryLossDetails from './SelfOperatedInventoryLossDetails.vue'
import HalfBillingFee from './HalfBillingFee.vue'
export default {
  components: {
    MyContainer, derelict, protection, afterSaleCompensate, returnServiceCharge, productIndemnity, penaltyDeductio,
    billingChargeTemu,SheInBill
    ,SheInInventoryLossDetail,SelfOperatedBill,SelfOperatedInventoryLossDetails,HalfBillingFee
  },
  data() {
    return {
      levelOne: 'levelOne1',
      levelTwo1: 'levelTwo1A',
      levelTwo2: 'levelTwo2A',
      levelTwo3: 'levelTwo3A',
      levelTwo4: 'levelTwo4A',
      levelTwo5: 'levelTwo5A'
    };
  },
  methods: {

  }
};
</script>

<style lang="scss" scoped></style>
