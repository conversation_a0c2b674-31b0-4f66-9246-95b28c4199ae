<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <div style="display: flex;width: 80%;">
                    <el-input v-model.trim="ListInfo.goodsCode" placeholder="请输入商品编码" maxlength="50" clearable
                        class="publicCss" />
                    <el-input v-model.trim="ListInfo.goodsName" placeholder="请输入商品名称" maxlength="50" clearable
                        class="publicCss" />
                    <el-input v-model.trim="ListInfo.applyUserName" placeholder="请输入添加人姓名" maxlength="50" clearable
                        class="publicCss" />
                    <el-input v-model.trim="ListInfo.approvalUserName" placeholder="请输入审批人姓名" maxlength="50" clearable
                        class="publicCss" />
                    <el-select v-model="ListInfo.status" placeholder="审批状态" class="publicCss" clearable>
                        <el-option :key="'待审批'" label="待审批" value="待审批" />
                        <el-option :key="'审批中'" label="审批中" value="审批中" />
                        <el-option :key="'已通过'" label="已通过" value="已通过" />
                        <el-option :key="'已拒绝'" label="已拒绝" value="已拒绝" />
                        <el-option :key="'已撤销'" label="已撤销" value="已撤销" />
                    </el-select>
                    <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                        style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
                    </el-date-picker>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
                <div style="display: flex;justify-content: end;flex: 1;margin-right: 10px;">
                    <el-button type="primary" @click="exportProps">导出</el-button>
                    <el-button type="primary" @click="handleClick(false)">新增</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :toolbarshow="false"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :tree-config="{}" :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading"
            :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="150">
                    <template #default="{ row, $index }">
                        <div style="display: flex" v-if="row.isRoot">
                            <el-button type="text" @click="handleClick(true, row.id, true)"
                                v-if="row.status != '待审批'">查看审批</el-button>
                            <el-button type="text" @click="reInitiated(row.id)"
                                v-if="(row.status == '已拒绝' || row.status == '已撤销') && (row.isAddUserShow || row.isManagerShow)">重新发起</el-button>
                            <el-button type="text" @click="handleClick(true, row.id)"
                                v-if="row.status == '待审批' && (row.isAddUserShow || row.isManagerShow)">编辑</el-button>
                            <el-button type="text" @click="handleDelete(row.id)" style="color: red;"
                                v-if="((row.status == '已撤销' || row.status == '已拒绝') && (checkPermission('/inventory/PriceIncreaseOrders/Delete')))">删除</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
        <el-drawer :title="title" :visible.sync="addOrEditPropsDrawer" direction="rtl" :wrapperClosable="false"
            :destroy-on-close="true" size="50%">
            <addOrEditProps ref="addOrEditProps" @close="close" @getList="getList" :formData="formData"
                v-if="addOrEditPropsDrawer" :isEdit="isEdit" :auditProps="auditProps" :isView="isView"
                :isReInitiated="isReInitiated" />
        </el-drawer>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import addOrEditProps from './components/addOrEditProps.vue'
import { getCurrentUser } from '@/api/inventory/packagesprocess'
import { pageGetApplyData, exportApplyData, getApprovalRecords, getApplyData, deletePurchaseHikeApply } from '@/api/inventory/purchaseHike'
import dayjs from 'dayjs'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '审批状态', type: 'clickLink', treeNode: true, style: (that, row) => that.renderStatus(row.status), },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'goodsName', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'prevPrice', label: '原价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'price', label: '现价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'quantity', label: '进货数量', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'hikeReason', label: '涨价原因', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'createdTime', label: '添加日期', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'applyTime', label: '涨价日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'applyUserName', label: '添加人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'title', label: '岗位', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'companyName', label: '分公司', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'approvalUserName', label: '审批人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'appGrouperName', label: '审批人组长', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, addOrEditProps
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                beginCreated: null,//开始时间
                endCreated: null,//结束时间
                goodsCode: null,//商品编码
                goodsName: null,//商品名称
                applyUserName: null,//添加人
                approvalUserName: null,//审批人
                status: null//审批状态
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            isEdit: false,
            addOrEditPropsDrawer: false,
            viewAuditDrawer: false,
            formData: null,
            auditProps: null,
            isView: false,
            isReInitiated: false,
            userId: null,
            title: null,
        }
    },
    async mounted() {
        await this.getUser()
        await this.getList()
    },
    methods: {
        renderStatus(status) {
            const map = {
                '待审批': "background-color: #54bcbd;color: white;padding: 10px;",
                '审批中': "background-color: #409dfe;color: white;padding: 10px;",
                '已通过': "background-color: #81b337;color: white;padding: 10px;",
                '已拒绝': "background-color: #ff0000;color: white;padding: 10px;",
                '已撤销': "background-color: #e99d42;color: white;padding: 10px;"
            }
            return map[status]
        },
        async getUser() {
            const { data, success } = await getCurrentUser()
            if (success) {
                this.userId = data.userId
            }
        },
        async viewAuditProps(id) {
            const { data, success } = await getApprovalRecords({ id })
            if (success) {
                this.auditProps = data
            }
        },
        handleDelete(id) {
            this.$confirm('此操作将删除该条数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await deletePurchaseHikeApply({ id })
                if (success) {
                    await this.getList()
                    this.$message.success('删除成功')
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        close() {
            this.isReInitiated = false
            this.isView = false
            this.addOrEditPropsDrawer = false
        },
        async reInitiated(id) {
            await this.getEditInfo(id)
            await this.viewAuditProps(id)
            this.isEdit = true
            this.title = '重新发起'
            this.isView = false
            this.isReInitiated = true
            this.addOrEditPropsDrawer = true
        },
        //获取数据详细信息
        async getEditInfo(id) {
            const { data, success } = await getApplyData({ id })
            if (success) {
                this.formData = data
            }
        },
        async handleClick(isEdit, id, isView) {
            this.auditProps = []
            this.isReInitiated = false
            this.isEdit = isEdit
            this.title = isEdit ? '编辑' : '新增'
            this.isView = isView || false
            this.title = isView ? '查看审批' : this.title
            if (id) {
                await this.getEditInfo(id)
                await this.viewAuditProps(id)
            }
            this.addOrEditPropsDrawer = true
        },
        async exportProps() {
            const { data } = await exportApplyData(this.ListInfo)
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '涨价单审批' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        async changeTime(e) {
            this.ListInfo.beginCreated = e ? e[0] : null
            this.ListInfo.endCreated = e ? e[1] : null
            await this.getList()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data, success } = await pageGetApplyData(this.ListInfo)
            if (success) {
                data.list.forEach(item => {
                    item.isManagerShow = (this.userId == item.applyManagerId) ? true : false
                    item.isAddUserShow = (this.userId == item.applyUserId) ? true : false
                })
                this.tableData = data.list
                this.total = data.total
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
