<template>
  <MyContainer>
    <div style="overflow: auto;padding-top: 20px;">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm"
        v-loading="loading">
        <el-form-item label="仓库下拉列表" prop="wmsIds">
          <chooseWareHouse v-model="ruleForm.wmsIds" multiple class="publicCss" :collapsetags="false" />
        </el-form-item>
        <el-form-item label="品牌下拉列表" prop="brandIds">
          <el-select class="publicCss" v-model="ruleForm.brandIds" placeholder="请选择品牌" :clearable="true" multiple
            filterable>
            <el-option v-for="item in brandList" :key="item.key" :label="item.value" :value="item.key">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="最小成本" prop="amountMin">
          <el-input-number v-model="ruleForm.amountMin" placeholder="最小成本" :min="0" :max="999999999" :controls="false"
            class="publicCss" />
        </el-form-item>
        <div style="display: flex; justify-content: center;gap: 30px;margin: 50px 0 20px 0;">
          <el-button type="primary" @click="submitForm('ruleForm')">保存</el-button>
          <el-button @click="resetForm('ruleForm')">取消</el-button>
        </div>
      </el-form>
    </div>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getAllProBrand } from '@/api/inventory/warehouse';
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import request from '@/utils/request'
const api = '/api/verifyOrder/ReturnOrderScan/'

export default {
  name: "detailSettings",
  components: {
    MyContainer, vxetablebase, chooseWareHouse, inputNumberYh
  },
  data() {
    return {
      loading: false,
      api,
      brandList: [],
      that: this,
      ruleForm: {
        brandIds: [],
        wmsIds: [],
        amountMin: undefined,
        id: '',
      },
      rules: {
        brandIds: [
          { required: true, message: '请选择品牌', trigger: 'blur' }
        ],
        wmsIds: [
          { required: true, message: '请选择仓库', trigger: 'blur' }
        ],
        amountMin: [
          { required: true, message: '请输入最小成本', trigger: 'blur' },
          { type: 'number', message: '请输入数字', trigger: 'blur' },
        ],
      }
    }
  },
  async mounted() {
    await this.init();
    this.getList()
  },
  methods: {
    async init() {
      var res = await getAllProBrand();
      if (!res?.success) return;
      this.brandList = res.data;
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const params = {
            ...this.ruleForm,
            brandIds: (this.ruleForm.brandIds || []).map(Number),
            wmsIds: this.ruleForm.wmsIds || []
          };
          this.loading = true
          const { data, success } = await request.post(`${this.api}MergeSetting`, params)
          this.loading = false
          if (!success) return
          this.$message.success('保存成功')
          this.getList()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.$emit('resetFormCallback')
    },
    async getList() {
      this.loading = true;
      try {
        const { data, success } = await request.post(`${this.api}GetSetting`,)
        if (success) {
          this.ruleForm = { ...data, brandIds: data.brandIds.map(String) };
        } else {
          this.$message.error("获取列表失败");
        }
      } finally {
        this.loading = false;
      }
    },

  }
}
</script>

<style scoped lang="scss">
.publicCss {
  width: 90%;
}

::v-deep .el-input-number.is-without-controls .el-input__inner {
  padding-left: 5px;
  padding-right: 2px;
  text-align: left;
}

::v-deep .el-select__tags-text {
  max-width: 150px;
}
</style>
