<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="亏损下架ID" 
            name="tab1" style="height: 100%;">
            <profit6LossProCodes :filter="filter" ref="profit6LossProCodes" style="height: 100%;" />
          </el-tab-pane>
           <el-tab-pane label="白名单设置" 
            name="tab2" style="height: 100%;"  v-if="checkPermission('profit6Loss:whithlist:search')" >
            <whiteList :filter="filter" ref="whiteList" style="height: 100%;" />
          </el-tab-pane>
           <el-tab-pane label="平台亏损天数设置" 
            name="tab3" style="height: 100%;" v-if="checkPermission('profit6Loss:configsetting')" >
            <takeOffLossConfig :filter="filter" ref="takeOffLossConfig" style="height: 100%;" />
          </el-tab-pane>
        </el-tabs>
    </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";
import profit6LossProCodes from './profit6LossProCodes.vue'
import takeOffLossConfig from './takeOffLossConfig.vue'
import whiteList from './whiteList.vue'


export default {
    name: "Users",
    components: { MyContainer,profit6LossProCodes,takeOffLossConfig,whiteList},
    data() {
        return {
            that: this,
            filter: {
        },
            shopList: [],
            pageLoading: false,
            userList: [],
            selids: [],
            activeName:'tab1'
        };
    },
    mounted() {
      window.showtab4=this.showtab
    },
    methods: {
        showtab(){
          this.activeName=""
      }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
