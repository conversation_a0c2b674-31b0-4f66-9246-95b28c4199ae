<template>
  <MyContainer>
    <el-tabs v-model="activeName" style="height: 94%;">
      <el-tab-pane label="包装费（旧）" name="first1" style="height: 100%;">
        <PackingCharge />
      </el-tab-pane>
      <el-tab-pane label="包装费（新）" name="first2" style="height: 100%;" lazy>
        <packingChargeNew />
      </el-tab-pane>
      <el-tab-pane label="系列编码包装费" name="first3" :lazy="true" style="height: 100%;">
        <seriesCodePackagingFee ref="refseriesCodePackagingFee" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane label="纸管木条包装" name="first4" style="height: 100%;" lazy>
        <PaperTubeAndWoodenStripPackaging />
      </el-tab-pane>
      <el-tab-pane label="云仓包装出仓" name="first5" style="height: 100%;" lazy>
        <CloudWarehousePackagingForOutboundShipment />
      </el-tab-pane>
      <el-tab-pane label="仓库包装维护" name="first6" style="height: 100%;" lazy>
        <WarehousePackingMaintenance />
      </el-tab-pane>
    </el-tabs>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import PackingCharge from './PackingCharge.vue'
import packingChargeNew from './packingChargeNew.vue'
import seriesCodePackagingFee from './seriesCodePackagingFee'
import PaperTubeAndWoodenStripPackaging from './PaperTubeAndWoodenStripPackaging'
import CloudWarehousePackagingForOutboundShipment from './CloudWarehousePackagingForOutboundShipment'
import WarehousePackingMaintenance from './WarehousePackingMaintenance'

export default {
  components: {
    MyContainer, PackingCharge, packingChargeNew, seriesCodePackagingFee, PaperTubeAndWoodenStripPackaging, CloudWarehousePackagingForOutboundShipment,
    WarehousePackingMaintenance
  },
  data() {
    return {
      activeName: 'first1'
    };
  },
  methods: {

  }
};
</script>

<style lang="scss" scoped></style>
