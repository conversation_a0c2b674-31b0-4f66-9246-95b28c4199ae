<template>
  <my-container v-loading="pageLoading">
    <template #header>
    <el-form :inline="true" :model="filter" @submit.native.prevent>
      <el-form-item label="采购员:">
        <el-select v-model="filter.brandId" multiple collapse-tags clearable placeholder="请选择采购员" style="width: 300px">
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="预警:">
        <el-select v-model="filter.isError" placeholder="请选择">
          <el-option label="所有" value></el-option>
          <el-option label="正常" value='0'></el-option>
          <el-option label="进货" value="1"></el-option>
          <el-option label="缺货" value="2"></el-option>
          <el-option label="催物流" value="3"></el-option>
          <el-option label="催上架" value="4"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="商品编码:"><el-input v-model="filter.goodsCode" /></el-form-item>
      <el-form-item label="商品名称:"><el-input v-model="filter.goodsName" /></el-form-item>
      <el-form-item><el-button type="primary" @click="onSearch">查询</el-button></el-form-item>
    </el-form>
    </template>
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'  :showsummary='true' :summaryarry='summaryarry'
        @cellclick='cellclick' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
         <template slot='extentbtn'>
            <el-button-group>
              <el-button style="margin: 0;">
                {{lastUpdateTime}}
              </el-button>
            </el-button-group>
        </template>
     </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

   <el-dialog title="导入数据" :visible.sync="importVisible" width="30%">
      <span>
        <el-upload ref="upload" class="upload-demo"
          :auto-upload="false" :multiple="false" action accept=".xlsx"
          :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}   </el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="分析" :visible.sync="dialogVisible" v-dialogDrag width="80%">
      <span>
          <probianmaanalysis :filter="analysisfilter" ref="probianmaanalysis1"/>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="visiblepopover" v-dialogDrag :show-close="false">
       <goodscoderecord :filter="goodscoderecordfilter" style="height: 400px"></goodscoderecord> 
    </el-dialog>

    <el-drawer title="处理" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editVisible" 
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
       <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
       <div class="drawer-footer">
        <el-button @click.native="editVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editLoading" @click="onEditSubmit" />
      </div>
    </el-drawer>

     <el-drawer title="物流跟踪" :visible.sync="drawervisible" direction="rtl" :append-to-body="true">
       <logistics ref="logistics"></logistics>
    </el-drawer>
    
  </my-container>
</template>

<script>
import {pagePurchasePlan,getLastUpdateTimeyPurchasePlan,importPurchasePlan,exportPurchasePlan,editPurchasePlan,getPurchasePlan} from '@/api/inventory/purchase'
import {getAllProBrand} from '@/api/inventory/warehouse'
import {upLoadImage} from '@/api/upload/file'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import probianmaanalysis from '@/views/inventory/probianmaanalysis'
import goodscoderecord from '@/views/inventory/goodscoderecord'
import logistics from '@/components/Comm/logistics'
import { formatYesornoBool,formatPurchasePlanError,formatmoney,formatTime,formatNoLink,formatSecondToHour} from "@/utils/tools";
import { ruleExpressComanycode } from '@/utils/formruletools'
import { throttle } from 'throttle-debounce';
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";
const tableCols =[
      {istrue:true,prop:'goodsCode',label:'商品编码', width:'100',sortable:'custom',type:'html',formatter:(row)=>formatNoLink(row.goodsCode)},
      {istrue:true,prop:'goodsName',label:'商品名称', width:'150',sortable:'custom'},
      {istrue:true,prop:'images',label:'图片', width:'60',type:'image'},
      {istrue:true,prop:'brandName',label:'采购员', width:'65'},
      {istrue:true,prop:'isError',label:'预警', width:'50',type:'html',formatter:(row)=>formatPurchasePlanError(row.isError)},
      {istrue:true,prop:'useableCount',label:'实际可用数', width:'70',sortable:'custom'}, 
      // {istrue:true,prop:'stockDays3D',label:'3维库存天数', width:'120',sortable:'custom'},
      // {istrue:true,prop:'stockDays4D',label:'4维库存天数', width:'120',sortable:'custom'},
      {istrue:true,prop:'sumTurnover',label:'合计周转天数', width:'70',sortable:'custom'},
      {istrue:true,prop:'useableTurnover',label:'可用周转天数', width:'70',sortable:'custom'},
      {istrue:true,prop:'inTransitTurnover',label:'在途周转天数', width:'70',sortable:'custom'},
      {istrue:true,prop:'lastInTransitTime',label:'最近在途时长', width:'70',sortable:'custom',formatter:(row)=>formatSecondToHour(row.lastInTransitTime)},
      {istrue:true,prop:'avgInTransitTime',label:'历史平均时长', width:'70',sortable:'custom',formatter:(row)=>formatSecondToHour(row.avgInTransitTime)},
      {istrue:true,prop:'avgSalesDays4D',label:'4维日均销量', width:'65',sortable:'custom'},
      //{istrue:true,type:'button', width:'80',btnList:[{label:"今日销量",handle:(that,row)=>that.onanalysis(row)}]},
      {istrue:true,prop:'salesToday',label:'今日销量', width:'60',sortable:'custom',type:'html',formatter:(row)=>formatNoLink(row.salesToday)},
      {istrue:true,prop:'goodsLable',label:'商品标签', width:'80',sortable:'custom'},
      {istrue:true,prop:'inTransitNum',label:'采购在途', width:'90',sortable:'custom'},
      {istrue:true,prop:'stock',label:'进货仓库存', width:'70',sortable:'custom'}, 
      // {type:'button', width:'50',btnList:[{label:"编辑",display:(row)=>{return row.isHandle==true;},handle:(that,row)=>that.onHand(row)}]},
      {istrue:true,type:'button', width:'55',btnList:[{label:"编辑", 
              htmlformatter:(row)=>{return `<i class="el-icon-star-on" style="color:${(row.isReportToday==true?"green":"red")}"></i>`},
              display:(row)=>{return false;},handle:(that,row)=>that.onHand(row)}]},
      {istrue:true,prop:'count',label:'数量', width:'50'},
      {istrue:true,prop:'planArrivalTime',label:'预计到货日期', width:'110',sortable:'custom',formatter:(row)=>formatTime(row.planArrivalTime,'YYYY-MM-DD')},
      {istrue:true,prop:'companyName',label:'物流公司', width:'80'},
      {istrue:true,prop:'expressNo',label:'物流单号', width:'80',type:'html',formatter:(row)=>formatNoLink(row.expressNo)},
      {istrue:true,prop:'remark',label:'备注', width:'160',type:'editor'},
     ];
const tableHandles=[{label:"导入", handle:(that)=>that.startImport()},{label:"导出", handle:(that)=>that.onExport()}];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton,probianmaanalysis,goodscoderecord,logistics},
  data() {
    return {
      that:this,
      formatTime:formatTime,
      filter: {
        goodsCode:null, 
        brandId:null,
        goodsName:null,
        isError:null
      },
      analysisfilter:{
        startDate: null,
        endDate: null,
        proBianMa:""
      },
      goodscoderecordfilter:{goodsCode:"",buyNo:""},
      list: [],
      brandlist:[],
      recodelist:[],
      tableCols:tableCols,
      tableHandles:tableHandles,
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      importVisible: false,
      prevTarget: null, // 编辑 Popover 的 Reference （参照），用于 popover.js 对齐两个元素
      popperFlag: false, // 用于编辑 Popover 的刷新
      visiblepopover: false,
      visiblepopoverdetail: false,
      dialogOrderDetailVisible:false,
      popperFlagdetail: false,
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      selids: [],
      fileList:[],
      listLoading: false, 
      pageLoading: false,
      uploadLoading:false,
      lastUpdateTime:'',
      onExporting:false,
      dialogVisible:false,
      editVisible:false,
      editLoading:false,
      drawervisible:false,
      autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
        },
    }
  },
  watch: {
    value(n) {
      if(n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    }
  },
  async mounted() {
    this.init();
    this.getlist();
    this.initform();
    formCreate.component('editor', FcEditor);
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async init(){
        var date1=new Date();
        this.analysisfilter.endDate=await this.datetostr(date1);
        var date2=await this.addDate(this.analysisfilter.endDate,-90);
        this.analysisfilter.startDate=await this.datetostr(new Date(date2));

        var res2= await getAllProBrand();
        this.brandlist = res2.data.map(item => {
            return { value: item.key, label: item.value };
        });

        var res3= await getLastUpdateTimeyPurchasePlan();
        this.lastUpdateTime= "最晚更新时间:"+res3.data
    }, 
    async initform(){
       let that=this;
       this.autoform.rule= [{type:'hidden',field:'id',title:'id',value: '',col:{span:12}},
                     {type:'input',field:'goodsCode',title:'商品编码',value: '',props:{readonly:true},col:{span:12}},
                     {type:'input',field:'goodsName',title:'商品名称',value: '',props:{readonly:true},col:{span:12}},
                     {type:'select',field:'isError',title:'预警',value: '', options: [{label:'催上架',value:4},{label:'催物流',value:3},{label:'正常',value:0},{label:'进货',value:1},{label:'缺货',value:2}],col:{span:4}},
                     {type:'DatePicker',field:'planArrivalTime',title:'预计到货日期',value:'',validate: [{type: 'string', required: false, message:'请输入预计到货日期'}],props: {type:'datetime',format:'yyyy-MM-dd',placeholder:'预计到货日期'},col:{span:4}},
                     {type:'InputNumber',field:'count',title:'数量',value: 0,props:{min:0,precision:0},col:{span:4}},
                     {type:'input',field:'expressNo',title:'物流单号',value: '',col:{span:4}},
                     {type:'select',field:'companyCode',title:'物流公司',value: '', ...await ruleExpressComanycode(),validate: [{type: 'string', required: false}],col:{span:4}},
                     {type:'editor',field:'remark',title:'备注',value:'',col:{span:24},props:{maxlength:400,init:async(editor)=>{await that.initeditor(editor)}}}
                    ]
    },
    async removeEditPopoverListener(flag) {  // 监听滚动，用于编辑框的滚动移除
      let timer = setTimeout(() => {
        let scrollElement = this.$refs.table.$el.querySelector('.el-table__body-wrapper');
        console.log('监听滚动，用于编辑框的滚动移除', flag, scrollElement);
        let scrollHandle = () => {
          console.log('执行--->', this.visibleEditOpinions);
          if (this.visibleEditOpinions) {
            this.clearEditPopperComponent();
          }
        }
        if (flag) {
          // 滚动节流
          scrollElement.addEventListener('scroll', throttle(500, scrollHandle));
        } else {
          scrollElement.removeEventListener('scroll', scrollHandle);
        }
        clearTimeout(timer);
      }, 0);
    },
   async initeditor(editor){
     editor.config.uploadImgMaxSize = 3 * 1024 * 1024 
     editor.config.excludeMenus = ['emoticon','video']
     editor.config.uploadImgAccept = []
     editor.config.customUploadImg =async function (resultFiles, insertImgFn) {
          console.log('resultFiles',resultFiles)
          const form = new FormData();
          form.append("image", resultFiles[0]);
          const res =await upLoadImage(form);
          var url=`${res.data}`
          console.log('url',url)
          insertImgFn(url)
     }
    },
   async datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
   async addDate(date,days){ 
        var d=new Date(date); 
        d.setDate(d.getDate()+days); 
        var m=d.getMonth()+1; 
        return d.getFullYear()+'-'+m+'-'+d.getDate(); 
    },
    async onSearch() {      
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      this.listLoading = true
      const res = await pagePurchasePlan(params)
      this.listLoading = false
      if (!res?.success)return
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
   async cellclick(row, column, cell, event){
     if (column.property=='salesToday')  
        await this.onanalysis(row.goodsCode)
     else if (column.property=='goodsCode')
         this.$router.push({path: '/order/ordergoodssales', query: {goodsCode: row.goodsCode}})
    if (column.property=='expressNo'&&row.expressNo)
        await this.showlogistics(row.companyCode,row.expressNo);
     else if (column.property=='planArrivalTime'
         ||column.property=='companyCode'
         ||column.property=='count' ||column.property=='remark') {
      await this.getrecordlist(row.goodsCode)
      this.visiblepopover=true;
      // if (event.stopPropagation) {//阻止事件冒泡，兼容ie
      //   event.stopPropagation();
      // } else if (window.event) {
      //   window.event.cancelBubble = true;
      // }
      // let currentTarget = event.target; // 赋值当前点击的编辑
      // this.editData = row; // 设置编辑数据
      // if (this.prevTarget === currentTarget) { // 判断是否需要切换
      //   this.visiblepopover = !this.visiblepopover; // 同一个元素重复点击
      // } else {
      //   if (this.prevTarget) {  // 切换不同元素, 判断之前是否有点击其他编辑 prevTarget
      //     this.clearEditPopperComponent();   // 先清除之前的编辑框
      //     this.$nextTick(() => { // 然后生成新的编辑框
      //       this.prevTarget = currentTarget;
      //       this.visiblepopover = true;
      //     });
      //   } else {
      //     console.log('首次--->this.prevTarget'); // 首次
      //     this.prevTarget = currentTarget;
      //     this.visiblepopover = true;
      //   }
      // }
     }
    },
   async getrecordlist(goodsCode){
      this.goodscoderecordfilter={buyNo:"",goodsCode:goodsCode};
     },
   async onanalysis(goodsCode){
        this.dialogVisible=true;
        this.$nextTick(() => {
           this.$refs.probianmaanalysis1.onShow([goodsCode]);
        });
    },
    // 清空编辑组件
  async clearEditPopperComponent() {
      this.prevTarget = null;
      this.popperFlag = !this.popperFlag;
      this.popperFlagdetail= !this.popperFlagdetail;
      this.visiblepopover = false;
      this.visiblepopoverdetail= false;
    },
   async onHand(row){
      this.editVisible = true
      const res = await getPurchasePlan({goodsCode:row.goodsCode})
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields()
      await this.autoform.fApi.setValue(res.data)
    },
   async onDisPlay(row){
     return row.isHandle==true;
    },
   async onEditSubmit() {
      this.editLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          const res = await editPurchasePlan(formData);
          if(res.success){
            this.getlist();
            this.editVisible=false;
          }
        }else{}
     })
     this.editLoading=false;
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    startImport() {
      this.importVisible = true;
    },
    cancelImport() {
      this.importVisible = false;
    },
    beforeRemove() {
      return false;
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
   async submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit=true;
      this.uploadLoading=true;
      this.$refs.upload.submit();
    },
   async uploadFile(item) {
      if(!this.fileHasSubmit){
        return false;
      }
      this.fileHasSubmit=false;
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      const res =await importPurchasePlan(form);
      if (res.success)   this.$message({ message: "上传成功,正在导入中...", type: "success" });
      else  this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading=false; 
    },
   async uploadChange(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for(var i=0;i<fileList.length;i++){
          if(fileList[i].status=="success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.fileList = list;
      }
   },
   async uploadRemove(file, fileList){
       this.uploadChange(file, fileList);
    },
    selsChange: function(sels) {
      this.sels = sels
    },
  async onExport() {
     if (this.onExporting) return;
     try{
        const params = {...this.pager,... this.filter}
        var res= await exportPurchasePlan(params);
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','建议采购_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },
    async showlogistics(companycode,number){
      this.drawervisible=true;
       this.$nextTick(function(){
         this.$refs.logistics.showlogistics(companycode,number);
       })
    }
  }
}
</script>
