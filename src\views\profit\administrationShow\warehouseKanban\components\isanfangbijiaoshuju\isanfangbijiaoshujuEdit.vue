<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" ref="refruleForm" label-width="140px" class="demo-ruleForm">
        <div style="font-size: 15px; font-weight: 600; margin-left: 20px">维修项目</div>
        <el-form-item label="流水线：" prop="assemblyLineMaintenance">
          <inputNumberYh v-model="ruleForm.assemblyLineMaintenance" :fixed="2" :placeholder="'流水线维修费用'"
            class="publicCss" />
        </el-form-item>
        <el-form-item label="打包桌维修：" prop="packingTableMaintenance">
          <inputNumberYh v-model="ruleForm.packingTableMaintenance" :fixed="2" :placeholder="'打包桌维修费用'"
            class="publicCss" />
        </el-form-item>
        <el-form-item label="地面维修：" prop="groundMaintenance">
          <inputNumberYh v-model="ruleForm.groundMaintenance" :fixed="2" :placeholder="'地面维修费用'" class="publicCss" />
        </el-form-item>
        <el-form-item label="包装机械维修：" prop="packagingMachineryMaintenance">
          <inputNumberYh v-model="ruleForm.packagingMachineryMaintenance" :fixed="2" :placeholder="'包装机械维修费用'"
            class="publicCss" />
        </el-form-item>
        <el-form-item label="PDA维修：" prop="pdaMaintenance">
          <inputNumberYh v-model="ruleForm.pdaMaintenance" :fixed="2" :placeholder="'PDA维修费用'" class="publicCss" />
        </el-form-item>
        <el-form-item label="调拨车：" prop="transferVehicleMaintenance">
          <inputNumberYh v-model="ruleForm.transferVehicleMaintenance" :fixed="2" :placeholder="'调拨车维修费用'"
            class="publicCss" />
        </el-form-item>
        <el-form-item label="叉车：" prop="forkliftMaintenance">
          <inputNumberYh v-model="ruleForm.forkliftMaintenance" :fixed="2" :placeholder="'叉车维修费用'" class="publicCss" />
        </el-form-item>
        <el-form-item label="电梯维修：" prop="elevatorMaintenance">
          <inputNumberYh v-model="ruleForm.elevatorMaintenance" :fixed="2" :placeholder="'电梯维修费用'" class="publicCss" />
        </el-form-item>
        <el-form-item label="滑道维修：" prop="slideMaintenance">
          <inputNumberYh v-model="ruleForm.slideMaintenance" :fixed="2" :placeholder="'滑道维修费用'" class="publicCss" />
        </el-form-item>
        <el-form-item label="运输车维修：" prop="transportVehicleMaintenance">
          <inputNumberYh v-model="ruleForm.transportVehicleMaintenance" :fixed="2" :placeholder="'运输车维修费用'"
            class="publicCss" />
        </el-form-item>
        <el-form-item label="卫生间维修：" prop="toiletMaintenance">
          <inputNumberYh v-model="ruleForm.toiletMaintenance" :fixed="2" :placeholder="'卫生间维修费用'" class="publicCss" />
        </el-form-item>
        <el-form-item label="空调维修：" prop="airConditionerMaintenance">
          <inputNumberYh v-model="ruleForm.airConditionerMaintenance" :fixed="2" :placeholder="'空调维修费用'"
            class="publicCss" />
        </el-form-item>
        <el-form-item label="电路维修：" prop="circuitMaintenance">
          <inputNumberYh v-model="ruleForm.circuitMaintenance" :fixed="2" :placeholder="'电路维修费用'" class="publicCss" />
        </el-form-item>
        <el-form-item label="卷帘门维修：" prop="rollingDoorMaintenance">
          <inputNumberYh v-model="ruleForm.rollingDoorMaintenance" :fixed="2" :placeholder="'卷帘门维修费用'"
            class="publicCss" />
        </el-form-item>
        <el-form-item label="消防维修：" prop="fireFightingMaintenance">
          <inputNumberYh v-model="ruleForm.fireFightingMaintenance" :fixed="2" :placeholder="'消防维修费用'"
            class="publicCss" />
        </el-form-item>
        <el-form-item label="墙面维修：" prop="wallMaintenance">
          <inputNumberYh v-model="ruleForm.wallMaintenance" :fixed="2" :placeholder="'墙面维修费用'" class="publicCss" />
        </el-form-item>
        <el-form-item label="打印机维修：" prop="printerMaintenance">
          <inputNumberYh v-model="ruleForm.printerMaintenance" :fixed="2" :placeholder="'打印机维修费用'" class="publicCss" />
        </el-form-item>
        <el-form-item label="管道维修：" prop="pipelineMaintenance">
          <inputNumberYh v-model="ruleForm.pipelineMaintenance" :fixed="2" :placeholder="'管道维修费用'" class="publicCss" />
        </el-form-item>
        <el-form-item label="其他维修：" prop="otherMaintenance">
          <inputNumberYh v-model="ruleForm.otherMaintenance" :fixed="2" :placeholder="'其他维修费用'" class="publicCss" />
        </el-form-item>
        <div style="font-size: 15px; font-weight: 600; margin-left: 20px; margin-top: 20px">维修统计</div>
        <el-form-item label="维修次数：" prop="maintenanceCountSubtotal">
          <inputNumberYh v-model="ruleForm.maintenanceCountSubtotal" :placeholder="'维修次数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="维修金额小计：" prop="maintenanceAmountSubtotal">
          <inputNumberYh v-model="ruleForm.maintenanceAmountSubtotal" :fixed="2" :placeholder="'维修金额小计'"
            class="publicCss" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { warehouseMaintenanceSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
export default {
  name: 'isanfangbijiaoshujuEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      ruleForm: {
        assemblyLineMaintenance: null,
        packingTableMaintenance: null,
        groundMaintenance: null,
        packagingMachineryMaintenance: null,
        pdaMaintenance: null,
        transferVehicleMaintenance: null,
        forkliftMaintenance: null,
        elevatorMaintenance: null,
        slideMaintenance: null,
        transportVehicleMaintenance: null,
        toiletMaintenance: null,
        airConditionerMaintenance: null,
        circuitMaintenance: null,
        rollingDoorMaintenance: null,
        fireFightingMaintenance: null,
        wallMaintenance: null,
        printerMaintenance: null,
        pipelineMaintenance: null,
        otherMaintenance: null,
        maintenanceCountSubtotal: null,
        maintenanceAmountSubtotal: null
      },
      rules: {
        assemblyLineMaintenance: [
          { required: true, message: '请输入流水线维修费用', trigger: 'blur' }
        ],
        packingTableMaintenance: [
          { required: true, message: '请输入打包桌维修费用', trigger: 'blur' }
        ],
        groundMaintenance: [
          { required: true, message: '请输入地面维修费用', trigger: 'blur' }
        ],
        packagingMachineryMaintenance: [
          { required: true, message: '请输入包装机械维修费用', trigger: 'blur' }
        ],
        pdaMaintenance: [
          { required: true, message: '请输入PDA维修费用', trigger: 'blur' }
        ],
        transferVehicleMaintenance: [
          { required: true, message: '请输入调拨车维修费用', trigger: 'blur' }
        ],
        forkliftMaintenance: [
          { required: true, message: '请输入叉车维修费用', trigger: 'blur' }
        ],
        elevatorMaintenance: [
          { required: true, message: '请输入电梯维修费用', trigger: 'blur' }
        ],
        slideMaintenance: [
          { required: true, message: '请输入滑道维修费用', trigger: 'blur' }
        ],
        transportVehicleMaintenance: [
          { required: true, message: '请输入运输车维修费用', trigger: 'blur' }
        ],
        toiletMaintenance: [
          { required: true, message: '请输入卫生间维修费用', trigger: 'blur' }
        ],
        airConditionerMaintenance: [
          { required: true, message: '请输入空调维修费用', trigger: 'blur' }
        ],
        circuitMaintenance: [
          { required: true, message: '请输入电路维修费用', trigger: 'blur' }
        ],
        rollingDoorMaintenance: [
          { required: true, message: '请输入卷帘门维修费用', trigger: 'blur' }
        ],
        fireFightingMaintenance: [
          { required: true, message: '请输入消防维修费用', trigger: 'blur' }
        ],
        wallMaintenance: [
          { required: true, message: '请输入墙面维修费用', trigger: 'blur' }
        ],
        printerMaintenance: [
          { required: true, message: '请输入打印机维修费用', trigger: 'blur' }
        ],
        pipelineMaintenance: [
          { required: true, message: '请输入管道维修费用', trigger: 'blur' }
        ],
        otherMaintenance: [
          { required: true, message: '请输入其他维修费用', trigger: 'blur' }
        ],
        maintenanceCountSubtotal: [
          { required: true, message: '请输入维修次数', trigger: 'blur' }
        ],
        maintenanceAmountSubtotal: [
          { required: true, message: '请输入维修金额小计', trigger: 'blur' }
        ]
      }
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };
    console.log(this.editInfo, 'editInfo');

  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod');
    },

    async submitForm(formName) {
      try {
        const valid = await this.$refs[formName].validate();
        if (valid) {
          this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing");
          const { success, msg } = await warehouseMaintenanceSubmit(this.ruleForm);

          if (success) {
            this.$message.success(msg || '保存成功');
            this.$emit("search");
          } else {
            this.$message.error(msg || '保存失败');
          }
        }
      } catch (error) {
        console.error('表单提交失败:', error);
        this.$message.error('保存失败，请重试');
      }
    },

    resetForm(formName) {
      this.$refs[formName].resetFields();
    }
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
