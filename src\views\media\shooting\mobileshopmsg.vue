<template>
    <div class="allbody">
        <div class="content">
            <!-- 淘系 -->
            <div class="mobile centerflex" v-if="platform!=2">
                <div style="flex: 1;" class="centerflex">
                    <div class="flexone">
                        <img src="@/static/images/mobile/mobileindex.png" width="100%" height="100%" alt="">
                        <div style="position: absolute; top: 0; left: 0; height: 100%; width: 100%;">
                            <div style="margin: 20px 25px; height: 57.5%; background-color: black;" v-if="videourl!=null">
                                <video width="100%" height="100%" autoplay controls loop :src="videourl"></video>
                            </div>
                            <div v-else style="margin: 20px 25px; height: 57.5%; color: #909399; display: flex; justify-content: center; align-items: center;">
                                {{msgtext2}}
                            </div>
                        </div>
                        <div style="position: absolute; top: 0; height: 0; width: 100%; height: auto; z-index: 99;">
                            <img src="@/static/images/mobile/mobile1.png" width="100%" height="auto" alt="" />
                        </div>
                    </div>
                </div>
                <div style="flex: 1;" class="centerflex">
                    <div class="flexone">
                        <img src="@/static/images/mobile/mobileindex.png" width="100%" height="100%" alt="">
                        <div style="position: absolute; top: 0; left: 0; height: 100%; width: 100%;">
                            <div style="margin: 20px 25px; height: 57.5%;" v-if="cenimglist.length>=1">
                                <el-carousel trigger="click" height="512px" width="100%" indicator-position="none">
                                    <el-carousel-item v-for="(item,index) in cenimglist" :key="index">
                                        <img :src="item.url" width="100%" height="100%" alt="" />
                                    </el-carousel-item>
                                </el-carousel>
                            </div>
                            <div v-else style="margin: 20px 25px; height: 57.5%; color: #909399; display: flex; justify-content: center; align-items: center;">
                                {{msgtext1}}
                            </div>
                        </div>
                        <div style="position: absolute; top: 0; height: 0; width: 100%; height: auto; z-index: 10; z-index: 99;">
                            <img src="@/static/images/mobile/mobile1.png" width="100%" height="auto" alt="" />
                        </div>
                    </div>
                </div>
                <div style="flex: 1;" class="centerflex">
                    <div class="flexone">
                        <img src="@/static/images/mobile/mobilemsg.png" width="100%" height="100%" alt="">
                        <div style="position: absolute; top: 0; left: 0; height: 100%; width: 100%;" class="scrollarea" >
                            <div style="margin: 140px 13px 140px 30px; height: 75%; overflow-y: auto;" v-if="detailimgList.length>0">
                                <div v-for="(item,index) in detailimgList" :key="index">
                                    <!-- <img :src="item.url" width="100%" height="100%" alt=""> -->
                                    <imgwater :imgsrc = "item.url" :canvasmore="'clip-img-can'+index"></imgwater>
                                </div>
                            </div>
                            <div v-else style="margin: 140px 30px; height: 75%; color: #909399; display: flex; justify-content: center; align-items: center;">
                                {{msgtext}}
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>

            <!-- 拼系 -->
            <div class="mobile centerflex" v-if="platform==2">
                <div style="flex: 1;" class="centerflex">
                    <div class="flexone">
                        <img src="@/static/images/mobile/pinall.png" width="100%" height="100%" alt="">
                        <div style="position: absolute; top: 0; left: 0; height: 100%; width: 100%;">
                            <div style="margin: 20px 25px; height: 43%; background-color: black;" v-if="videourl!=null">
                                <video width="100%" height="100%" autoplay controls loop :src="videourl"></video>
                            </div>
                            <div v-else style="margin: 20px 25px; height: 57.5%; color: #909399; display: flex; justify-content: center; align-items: center;">
                                {{msgtext2}}
                            </div>
                        </div>
                        <div style="position: absolute; top: 0; height: 0; width: 100%; height: auto; z-index: 99;">
                            <img src="@/static/images/mobile/pinheard.png" width="100%" height="auto" alt="" />
                        </div>
                    </div>
                </div>
                <div style="flex: 1;" class="centerflex">
                    <div class="flexone">
                        <img src="@/static/images/mobile/pinall.png" width="100%" height="100%" alt="">
                        <div style="position: absolute; top: 0; left: 0; height: 100%; width: 100%;">
                            <div style="margin: 20px 25px; height: 43%;" v-if="cenimglist.length>=1">
                                <el-carousel trigger="click" height="382px" width="100%" indicator-position="none">
                                    <el-carousel-item v-for="(item,index) in cenimglist" :key="index">
                                        <img :src="item.url" width="100%" height="100%" alt="" />
                                    </el-carousel-item>
                                </el-carousel>
                            </div>
                            <div v-else style="margin: 20px 25px; height: 57.5%; color: #909399; display: flex; justify-content: center; align-items: center;">
                                {{msgtext1}}
                            </div>
                        </div>
                        <div style="position: absolute; top: 0; height: 0; width: 100%; height: auto; z-index: 10; z-index: 99;">
                            <img src="@/static/images/mobile/pinheard.png" width="100%" height="auto" alt="" />
                        </div>
                    </div>
                </div>
                <div style="flex: 1;" class="centerflex">
                    <div class="flexone">
                        <img src="@/static/images/mobile/pindetail.png" width="100%" height="100%" alt="">
                        <div style="position: absolute; top: 0; left: 0; height: 100%; width: 100%;" class="scrollarea" >
                            <div style="margin: 140px 13px 140px 30px; height: 72%; overflow-y: auto;" v-if="detailimgList.length>0">
                                <div v-for="(item,index) in detailimgList" :key="index">
                                    <!-- <img :src="item.url" width="100%" height="100%" alt=""> -->
                                    <imgwater :imgsrc = "item.url" :canvasmore="'clip-img-can'+index"></imgwater>
                                </div>
                            </div>
                            <div v-else style="margin: 140px 30px; height: 75%; color: #909399; display: flex; justify-content: center; align-items: center;">
                                {{msgtext}}
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
            <div class="rightthr">
                <!-- <el-radio-group v-model="ismodule">
                    <el-radio-button label="淘系" value="taoxi"></el-radio-button>
                    <el-radio-button label="拼系" value="pinxi"></el-radio-button>
                </el-radio-group> -->
                <el-button type="primary" style="margin: 10px 0; width: 100px;" @click="toshopmsg" v-if="platform!=2">PC端预览</el-button>
                <el-button type="primary" style="margin: 10px 0; width: 100px;">手机端预览</el-button>
                <el-button type="primary" style="margin: 10px 0; width: 100px;" @click="tocarmsg">车图预览</el-button>
            </div>
        </div>

    </div>
</template>
<script>
import { Loading } from 'element-ui';
import { getPageDetailImgInfo} from '@/api/media/ShootingVideo';
import imgwater from '@/views/media/shooting/imgwater.vue'
export default{
    components: { imgwater },
    data(){
        return{
            platform: null,
            ismodule: 'pinxi',
            msgtext: '暂无详情图片，请先上传后预览！',
            msgtext1: '暂无主图，请先上传后预览！',
            msgtext2: '暂无视频，请先上传后预览！',
            cenimglist: [],
            detailimgList: [],
            pageLoading: false,
            indeximg: '@/static/images/mobile/mobileindex.png',
            msgimg: '@/static/images/mobile/mobilemsg.png',
            // videourl: 'http://yuhannanchang.tpddns.cn:8001/media/video/20221128/1597116658984488960.mp4'
            videourl: ''
        }
    },
    mounted() {
        this.getlist();
        this.platform = this.$route.query.platform;
    }, 
    methods:{
        toshopmsg(){
            this.$router.push({ path: '/msgshow',query: {id:this.$route.query.id,platform:  this.platform}})
        },
        tocarmsg(){
            this.$router.push({ path: '/msgshow1',query: {id:this.$route.query.id,platform:  this.platform}})
        },
        async getlist(){
            let _this = this;
            // _this.pageLoading= true;
            let loadingInstance = Loading.service();
            Loading.service({ fullscreen: true });
            var res  =  await getPageDetailImgInfo({taskid: this.$route.query.id});
            if(res?.success)
            {
                _this.videourl = res.data?.vedioList?.domainUrl;
                _this.cenimglist = res.data?.photoImgList;
                _this.detailimgList = res.data?.detailImgList;
                // _this.imglist = res.data.mainImgList;
                // _this.videolist=res.data.vedioList;
                // _this.bottomimg = res.data.detailImgList;
                // _this.skuImgList = res.data.skuImgList;
            }
            // _this.pageLoading= false;
            loadingInstance.close();
         },
    }
}
</script>
<style lang="scss" scoped>
.allbody{
    height: 100%;
    width: 100%;
    margin: 0 auto;
}
.content{
    position: relative;
    max-height: 100vh;
}
.rightthr{
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    position: absolute;
    width: 150px;
    height: 300px;
    right: 0;
    top: 50%;
    transform: translate(0,-50%);
}
.mobile{
    // width: 100%;
    // height: 100%;
    // margin: 20px 150px;
    padding: 20px 150px;
    display: flex;
    flex-direction: row;
}
.centerflex{
    display: flex;
    justify-content: center;
    align-items: center;
}
.flexone{
    height: 95vh;
    width: auto;
    position: relative;
}
.scrollarea ::-webkit-scrollbar-thumb{
  background: rgba(0, 0, 0, 0);
}
</style>