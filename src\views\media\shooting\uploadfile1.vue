<template>
    <el-row >
      <el-row>
        <div  style="display: inline-block;">
            <el-upload ref="upload"
            class="inline-block"
            action ="#"
            :auto-upload="true"
            :multiple="true"
            :limit="limit"
            :show-file-list ="false"
            :accept ="accepttyes"
            :http-request="UpSuccessload"
            :on-exceed ="exceed"
            :file-list="retdata"
            :disabled="islook"
            :before-upload="beforeAvatarUpload"
            >
            <el-button size="mini" type="primary"   :disabled="islook" :v-loading="uploading" >{{ buttontext }}<i class="el-icon-upload el-icon--right"></i></el-button>
        </el-upload>
        <el-button v-if="isdown" @click="downExecl" style=" margin-left: 20px;font-size:14px" type="text">下载</el-button>
        </div>
        <el-progress v-if="uploadprogress&&uploading" :percentage="uploadprogressPercentage"></el-progress>
    </el-row>
        <div class="linecontent" :style="boxsize">
            <div v-for="(i,j) in retdata" :key="j" style="margin-top: 5px;" >
                <div class="linerow img-hover" :style="minisize?{height: '60px',width: '60px'}:{height: '35px',width: '35px'}">
                    <div v-show="accepttyes.indexOf('.xlsx')>-1||accepttyes.indexOf('.mp4')>-1?true:false" class="columnshow">
                        <el-tooltip class="item" effect="dark" :content="i.fileName" placement="bottom">

                            <template v-if="i.fileName.indexOf('.mp4')==-1">
                                <img src="@/static/images/excel.png"
                             @click="OpenExeclOnline(i)" style="height: 100%; width: 100%;" mode="aspectFit" />
                                </template>
                            <template v-else>
                                <img src="@/static/images/vedio.jpg"
                             @click="OpenExeclOnline(i)" style="height: 100%; width: 100%;" mode="aspectFit" />
                            </template>
                        </el-tooltip>
                    </div>
                    <img v-show="accepttyes.indexOf('.xlsx')==-1?false:true" :src="i.url" @click="showImg(i,j)"
                         style="height: 100%; width: 100%;" mode="aspectFit"  @dragover="onDragOver" @dragend="onDragEnd(j)"/>

                        <i v-if="retdata.length!=null" class="el-icon-error close-img" @click.stop="removefile(i)"></i>

                    <div v-if="retdata.length!=null" class="close-img-dask"></div>
                </div>

            </div>
        </div>
        <el-image-viewer v-if="showGoodsImage" :url-list="imgList"  :initialIndex="imgindex" :on-close="closeFunc" style="z-index:9999;" />
        <el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer"  :append-to-body="true" v-dialogDrag>
            <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="downLoadUrl(videoUrl)">下载</el-button>
                <el-button @click="closeVideoPlyer">关闭</el-button>
            </span>
        </el-dialog>
    <!-- </el-row>  -->
</el-row>
</template>
<script>
function pause(msec) {
    return new Promise(
        (resolve, reject) => {
            setTimeout(resolve, msec || 500);
        }
    );
}
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
import ElImageViewer from './imageviewer.vue';
import MyContainer from "@/components/my-container";
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
export default {
    components: { MyContainer,ElImageViewer,videoplayer},
    props: {
        uploadprogress:{ type: Boolean, default:false},//是否显示进度条
        uploadInfo:{ type: Array, default:()=>{ return []} },
        buttontext:{ type: String, default:"点击上传" },
        accepttyes:{ type: String, default:"*" },
        limit:{ type: Number, default: 100000  },
        delfunction:{ type: Function, default: null},
        islook:{ type: Boolean, default:false },
        isdown:{ type: Boolean, default:false },
        minisize: { type: Boolean, default:true },
        uploadVerifyType: { type: String, default:"" },
        boxsize: {type: Object,default:()=>{ return {width:'200px',height:'100px'} }}  //设置图片区域长宽
    },
    data() {
        return {
            uploadprogressPercentage:0,
            retdata: [],
            accepttypeinfo :"*" ,
            limitnum :100,
            uploading:false,
            imgindex:0,
            imglist: [],
            showGoodsImage: false,
            dialogVisible:false,
            videoplayerReload: false
        };
    },
    async mounted() {
        this.retdata = this.uploadInfo;
    },

    methods: {
        downLoadUrl(url){
            let name = url.split('/').pop();
            let link = document.createElement("a");
            fetch(url)
            .then((res) => res.blob())
            .then((blob) => {
                // 将链接地址字符内容转变成blob地址
                link.href = window.URL.createObjectURL(blob);
                link.download =  name;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link)
                window.URL.revokeObjectURL(link.href)
            });
        },
        playVideo(videoUrl) {
            this.videoplayerReload = false;
            this.videoplayerReload = true;
            this.dialogVisible = true;
            this.videoUrl = videoUrl;
        },
        async closeVideoPlyer() {
            this.dialogVisible = false;
            this.videoplayerReload = false;
        },
        setData(array){
            this.retdata = array;
        },
        isformat(val){
            const img = ['jpg','png','tif','gif','svg','webp','apng'];
            for(var item in img){
                if(val.includes(item,0)){
                    console.log(true)
                    return true
                }else{
                    console.log(false)
                    return false
                }
            }
            console.log("是否图片",istrueimg)
        },
        onDragOver(event) {
            event.preventDefault()
            const { src } = event.target
            this.targetSrc = src
        },
        onDragEnd(idx) {
            for(let num in this.retdata){
                if(this.retdata[num].url==this.targetSrc){
                    [this.retdata[idx], this.retdata[num]] = [this.retdata[num], this.retdata[idx]]
                    this.retdata = [...this.retdata]
                }
            }
        },
        //获取返回值
        getReturns(){
            if(this.uploading){
                this.$message({ message:"正在上传，请稍等", type: "warning" });
                return {success:false};
            }
            var curdata = [];

            this.retdata.forEach(function(item){
                curdata.push({domain:item.domain,fileName:item.fileName,relativePath:item.relativePath,url:item.url,  upLoadPhotoId:item.upLoadPhotoId})
            });

            return {success:true ,data :curdata};
        },
        //下载execl文件
        async downExecl(){
            for(let num in this.retdata)
            {
                await pause(500);
                await this.downfile(this.retdata[num]);
            }
        },
        //下载文件
        async downfile(file){
            var xhr = new XMLHttpRequest();
            xhr.open('GET', file.url, true);
            xhr.responseType = 'arraybuffer'; // 返回类型blob
            xhr.onload = function() {
            if (xhr.readyState === 4 && xhr.status === 200) {
                let blob = this.response;
                console.log(blob);
                // 转换一个blob链接
                // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，
                // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定
                let downLoadUrl = window.URL.createObjectURL(new Blob([blob], {type: 'video/mp4'}));
                // 视频的type是video/mp4，图片是image/jpeg
                // 01.创建a标签
                let a = document.createElement('a');
                // 02.给a标签的属性download设定名称
                a.download = file.fileName;
                // 03.设置下载的文件名
                a.href = downLoadUrl;
                // 04.对a标签做一个隐藏处理
                a.style.display = 'none';
                // 05.向文档中添加a标签
                document.body.appendChild(a);
                // 06.启动点击事件
                a.click();
                // 07.下载完毕删除此标签
                a.remove();
            };
            };
            xhr.send();

        },
        //移除文件
        async removefile(file){
            if(this.islook){
                return;
            }
            //发出父级页面移除请求
            var that = this;
            if (file?.upLoadPhotoId > 0)
            {
                that.$confirm('此操作将会彻底删除该文件，是否执行')
                .then( async()=> {
                    await that.delfunction(file);
                    for(let num in that.retdata){
                        if(that.retdata[num].uid==file.uid){
                            that.retdata.splice(num,1)
                        }
                    }
                })
                .catch(_ => {
                });
            } else{
                for(let num in this.retdata){
                    if(this.retdata[num].uid==file.uid){
                        this.retdata.splice(num,1)
                    }
                }
            }
        },
        //上传超出提出
        exceed(files, fileList) {
                this.$message.warning(`当前限制选择 ${this.limit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
        },
        //上传方法
        async UpSuccessload (item) {
            this.uploadprogressPercentage = 0;
            this.uploading = true;
            this.$emit('uploadFinish', this.uploading);
            await this.AjaxFile(item.file, 0,"");
            this.uploading = false;
            this.$emit('uploadFinish', this.uploading);

        },
        //切片上传
        async AjaxFile(file,i,batchnumber) {
            var name = file.name; //文件名
            var size = file.size; //总大小
            var shardSize = 15 * 1024 * 1024;//2m
            var shardCount = Math.ceil(size / shardSize); //总片数
            if (i >= shardCount) {
                return;
            }
            //计算每一片的起始与结束位置
            var start = i * shardSize;
            var end = Math.min(size, start + shardSize);
            //构造一个表单，FormData是HTML5新增的
            i=i+1;
            var form = new FormData();
            form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分
            form.append("batchnumber", batchnumber);
            form.append("fileName", name);
            form.append("total", shardCount); //总片数
            form.append("index", i); //当前是第几片
            try {
                const res = await xMTVideoUploadBlockAsync(form);
                if (res?.success) {
                    this.uploadprogressPercentage = (i / shardCount).toFixed(2) * 100;
                    if (i == shardCount) {
                        res.data.fileName = name;
                        res.data.uid = file.uid;
                        res.data.upLoadPhotoId = 0;
                        if(!this.retdata){
                          this.retdata = []
                        }
                        this.retdata.push(res.data);
                        // this.imglist.push(res.data.url)
                        this.$message.success('上传完成！')
                    } else {
                        await this.AjaxFile(file, i, res.data);
                    }
                } else {
                    this.$message({ message: res?.msg, type: "warning" });
                }
            } catch (error) {
                this.uploading = false;
                this.$emit('uploadFinish', this.uploading);
                this.$message.error('上传文件异常！')
                this.$refs.upload.clearFiles();
                this.retdata = [];
            }
        },
        //完成表单界面显示图片
        OpenExeclOnline(e) {
            if (e.url.split('.')[e.url.split('.').length-1]=="mp4"   ){
                this.playVideo(e.url);
            }else{

                let url =  process.env.VUE_APP_DOMAIN_NAME +"/"+ e.relativePath
                let officeUrl = 'https://view.xdocin.com/view?src='+url
                // 在新窗口打开编码后 的链接
                window.open(officeUrl,'_blank')

            }

        },
        //完成表单界面显示图片
        async showImg(i,j) {
            this.imgList = [];
            for(let num in this.retdata)
            {
                this.imgList.push(this.retdata[num].url);
            }
            this.imgindex=j;
            this.showGoodsImage = true;
        },
        //完成表单界面关闭图片
        async closeFunc() {
            this.showGoodsImage = false;
        },
        beforeAvatarUpload(file){
            if (this.uploadVerifyType == "图片") {
                let files=['image/jpeg','image/jpg','image/png','image/gif'];
                if(files.indexOf(file.type)==-1)
                {
                    this.$message.info('不支持的文件格式！');
                    return false;
                }
            }
        }
    },
};
</script>
<style  lang="scss" scoped>
.infinite-list-item {
    list-style:none;
    margin: 0;
    padding: 0;
}
.infinite-list-item  span{
    width: 50px !important;
    white-space: nowrap!important;
    overflow: hidden!important;
    text-overflow: ellipsis!important;
}

.linecontent{
//   background: rgb(238, 236, 236);
//   width: 100%;
//   height: 100%;
  display: flex;
//   flex-direction: row;

//   overflow: hidden;
//   overflow-x: auto;
//   white-space: nowrap;
  flex-wrap: wrap;
  overflow-y: auto;
//   margin-top: 10px;
}
.linerow{
  display: inline-block;
//   background: #fff;
  margin: 2px 2px 0 0;
//   border: 1px solid rgba(219, 217, 217, 0.781);
//   width: 100px;
//   height: 100px;
}
.img-hover {
    position: relative;
    // border: 1px solid rgb(237, 238, 240);
  }

  .img-hover:hover {
      cursor: pointer;
    .close-img,
    .close-img-dask {
      display: block;
    }
  }

  .close-img {
    display: none;
    position: absolute;
    right: -6px;
    top: -6px;
    color: rgb(255, 0, 0);
    font-size: 18px;
    z-index: 99999;
  }
  .inline-block {
  display: inline-block;
}

//   .close-img-dask {
//     display: none;
//     position: absolute;
//     top: 0;
//     left: 0;
//     width: 100%;
//     height: 40%;
//     background-color: rgba(0, 0, 0, .5) !important;
//     transition: opacity .3s;
// }
</style>
