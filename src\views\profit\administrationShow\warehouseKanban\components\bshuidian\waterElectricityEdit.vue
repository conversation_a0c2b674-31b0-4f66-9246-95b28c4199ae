<template>
    <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
      <el-scrollbar style="height: 100%">
        <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="140px" class="demo-ruleForm">
          <el-form-item label="平替人数：" prop="replacementPersonCount">
              <!-- :fixed="2" -->
            <inputNumberYh @input="computedone(1)" :fixed="0" v-model="ruleForm.replacementPersonCount"
              :placeholder="'平替人数'" class="publicCss" />
          </el-form-item>
          <el-form-item label="新增人数：" prop="newlyAddedPersonCount">
            <inputNumberYh @input="computedone(1)" :fixed="0" v-model="ruleForm.newlyAddedPersonCount"
              :placeholder="'新增人数'" class="publicCss" />
          </el-form-item>
          <el-form-item label="减员人数：" prop="reductionPersonCount">
            <inputNumberYh @input="computedone(1)" :fixed="0" v-model="ruleForm.reductionPersonCount"
              :placeholder="'减员人数'" class="publicCss" :min="-9999999" :max="9999999" />
          </el-form-item>
          <el-form-item label="异动小计：" prop="changePersonCount">
            <inputNumberYh @input="computedone(1)" :fixed="0" v-model="ruleForm.changePersonCount"
              :placeholder="'异动小计'" class="publicCss" :min="-9999999" :max="9999999" />
          </el-form-item>
          <el-form-item label="异动费用：" prop="changeFee">
            <inputNumberYh @input="computedone(1)" :fixed="2" v-model="ruleForm.changeFee"
              :placeholder="'异动费用'" class="publicCss" />
          </el-form-item>
          <el-form-item label="缴纳人数：" prop="paidPersonCount">
            <inputNumberYh @input="computedone(1)" :fixed="0" v-model="ruleForm.paidPersonCount"
              :placeholder="'缴纳人数'" class="publicCss" :min="-9999999" :max="9999999" />
          </el-form-item>
          <el-form-item label="备注：" prop="remarks">
            <el-input style="width:80%;" v-model.trim="ruleForm.remarks" :maxlength="50" placeholder="备注" clearable />
          </el-form-item>
          <!-- <el-form-item label="日常水费-费用（元）：" prop="dailyWaterExpenses">
            <inputNumberYh @input="computedone(2)" :fixed="2" v-model="ruleForm.dailyWaterExpenses"
              :placeholder="'日常水费-费用（元）'" class="publicCss" />
          </el-form-item>
          <el-form-item label="日常电费-耗电量（度）：" prop="dailyElectricityConsumption">
            <inputNumberYh @input="computedone(1)" :fixed="2" v-model="ruleForm.dailyElectricityConsumption"
              :placeholder="'日常电费-耗电量（度）'" class="publicCss" />
          </el-form-item>
          <el-form-item label="日常电费-费用（元）：" prop="dailyElectricityExpenses">
            <inputNumberYh @input="computedone(2)" :fixed="2" v-model="ruleForm.dailyElectricityExpenses"
              :placeholder="'日常电费-费用（元）'" class="publicCss" />
          </el-form-item>
          <el-form-item label="总计-费用（元）：" prop="totalCost">
            {{ ruleForm.totalCost }}
          </el-form-item>
          <el-form-item label="备注：" prop="remark">
            <el-input style="width:80%;" v-model.trim="ruleForm.remark" :maxlength="50" placeholder="备注" clearable />
          </el-form-item> -->
        </el-form>
      </el-scrollbar>
      <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
        <el-button @click="cancellationMethod">取消</el-button>
        <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
      </div>
    </div>
  </template>

  <script>
  import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
  import MyConfirmButton from '@/components/my-confirm-button'
  import { warehouseEmployerLiabilityInsuranceSubmit } from '@/api/people/peoplessc.js';
  import checkPermission from '@/utils/permission'
  import decimal from '@/utils/decimal'
  export default {
    name: 'waterElectricityEdit',
    components: {
      inputNumberYh, MyConfirmButton
    },
    props: {
      editInfo: {
        type: Object,
        default: () => {
          return {}
        }
      },
      districtList: {
        type: Object,
        default: () => {
          return {}
        }
      },
      typeList: {
        type: Object,
        default: () => {
          return {}
        }
      }
    },
    data() {
      return {
        selectProfitrates: [],
        ruleForm: {
          label: '',
          name: ''
        },
        rules: {
          // dailyElectricityExpenses: [
          //   { required: true, message: '请输入', trigger: 'blur' }
          // ],
          amount: [
            { required: true, message: '请输入', trigger: 'blur' }
          ],
          // dailyWaterExpenses: [
          //   { required: true, message: '请输入', trigger: 'blur' }
          // ],
          // dailyWaterConsumption: [
          //   { required: true, message: '请输入', trigger: 'blur' }
          // ],
        }
      }
    },
    async mounted() {
      this.$nextTick(() => {
        this.$refs.refruleForm.clearValidate();
      });
      this.ruleForm = { ...this.editInfo };
    },
    methods: {
        computedone(type) {
            return;
        if (type == 1) {
          let a = this.ruleForm.dailyElectricityConsumption ? this.ruleForm.dailyElectricityConsumption : 0;
          let b = this.ruleForm.dailyWaterConsumption ? this.ruleForm.dailyWaterConsumption : 0;
          this.ruleForm.powerConsumption = decimal(a, b, 2, '+').toFixed(2);
          return decimal(a, b, 2, '+').toFixed(2);
        } else if (type == 2) {
          let a = this.ruleForm.dailyWaterExpenses ? this.ruleForm.dailyWaterExpenses : 0;
          let b = this.ruleForm.dailyElectricityExpenses ? this.ruleForm.dailyElectricityExpenses : 0;
          this.ruleForm.totalCost = decimal(a, b, 2, '+').toFixed(2);
          return decimal(a, b, 2, '+').toFixed(2);
        }
      },
      cancellationMethod() {
        this.$emit('cancellationMethod');
      },
      submitForm(formName) {
        this.$refs[formName].validate(async (valid) => {
          if (valid) {
            this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing")
            const { data, success } = await warehouseEmployerLiabilityInsuranceSubmit(this.ruleForm)
            if (!success) {
              return
            }
            await this.$emit("search");

          } else {
            return false;
          }
        });
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
      },
    }
  }
  </script>
  <style scoped lang="scss">
  .publicCss {
    width: 80%;
  }
  </style>
