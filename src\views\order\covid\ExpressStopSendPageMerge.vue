<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button style="padding: 0;margin: 0px 10px 0px 0px;">
                <el-select v-model="filterWarehouse" placeholder="选择发货仓" style="width:120px">
                    <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-button>
            <el-button-group>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="calculExpStopAreaNow">计算合并快递</el-button>
                <el-button type="primary" @click="onExport(1)">导出元数据</el-button>
                <el-button type="primary" @click="onExport(2)">导出合并格式</el-button>
            </el-button-group>
        </template>

        <div class="divTitle">{{this.tableTitle}}</div>
        <el-table :data="tableData" border style="width: 100%; " :header-cell-style="{ 
      'font-size':'16px'}">
            <el-table-column prop="province" label="省" width="200px">
            </el-table-column>
            <el-table-column prop="citys" label="区域">
            </el-table-column>
        </el-table>
    </my-container>
</template>

<script>
    import MyContainer from '@/components/my-container'

    import { getAllExpStopArea, calculExpStopAreaNow, exportStopExpressMergeDataAsync } from "@/api/order/covidStopSendPage";

    export default {
        name: 'ExpressStopSendPageMerge',
        components: { MyContainer },

        data () {
            return {
                filterWarehouse: "南昌",
                warehouselist: [{ label: "南昌", value: "南昌" }, { label: "义乌", value: "义乌" }],
                pageLoading: false,
                tableTitle: "",
                tableData: [],
                spanArr: [],
                position: 0
            }
        },
        async mounted () {

            await this.onSearch();
        },
        methods: {
            async onSearch () {
                this.pageLoading = true;
                const res = await getAllExpStopArea({ "warehouse": this.filterWarehouse });
                this.pageLoading = false;
                if (!res?.success) {
                    return
                }
                this.tableTitle = res.data.title;
                this.tableData = res.data.list;
            },
            async calculExpStopAreaNow () {
                const form = new FormData();
                form.append("warehouse", this.filterWarehouse);
                this.pageLoading = true;
                const res = await calculExpStopAreaNow(form);
                this.pageLoading = false;
                if (res?.success) {
                    this.$message({ message: '正在计算中...', type: "success" });
                }
            },
            //导出
            async onExport (exportType) {
                var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                var res = await exportStopExpressMergeDataAsync({ "warehouse": this.filterWarehouse, "exportType": exportType });
                loadingInstance.close();
                if (!res?.data) return
                var fileName = res.headers['content-disposition'].split("=")[2];
                fileName = decodeURIComponent(fileName.split("'")[2]);
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', fileName);
                aLink.click();
            }
        }
    }
</script>
<style scoped>
    ::v-deep .el-link.el-link--primary {
        margin-right: 7px;
    }
    ::v-deep #app .el-container .is-vertical .main .el-main .el-tabs--top {
        height: 95% !important;
        background-color: red;
    }
    .divTitle {
        margin-top: 20px;
        border: 1px solid #ebeef5;
        font-size: 18px;
        font-weight: 600;
        text-align: center;
        height: 40px;
        line-height: 40px;
    }
</style>
