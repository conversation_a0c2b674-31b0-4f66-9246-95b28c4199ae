<template>
    <div class="allbody">
        <div class="imgbody">
            <div style="position: relative;">
                <img src="https://i2.100024.xyz/2022/12/01/quhytd.webp" width="100%" height="auto" alt="" />
                <div class="box-right">
                    <div class="neibox">
                        <div style="height:5000px; background-color: white;">
                            <div v-for="(item,i) in directImgList">
                                <div style="width: 270px; height:300px; border: 1px solid #eee;  display: flex; flex-direction: column; margin-bottom: 20px;">
                                    <div style="height: 270px; width: auto"><img width="100%" height="100%" :src="item.url" alt=""></div>
                                    <div style="height: 30px;width: 100%; background-color: aquamarine;"><img width="100%" height="100%" src="https://i2.100024.xyz/2022/12/01/r9g5u9.webp" alt=""></div>
                                </div>
                                <!-- <div style="width: 100%; height: 60px;"></div> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    </div>
  </template>
  
  <script>
  import { Loading } from 'element-ui';
  import { getPageDetailImgInfo} from '@/api/media/directImgtask';
  export default {
    data () {
      return {
        directImgList: []
      }
    },
    mounted() {
        this.getlist();
    }, 
    methods: {
        tomobilemsg(){
        this.$router.push({ path: '/mobilemsg',query: {id:this.$route.query.id}})
      },
        toshopmsg(){
            this.$router.push({ path: '/msgshow',query: {id:this.$route.query.id}})
        },
        async getlist(){
            let _this = this;
            // _this.pageLoading= true;
            let loadingInstance = Loading.service();
            Loading.service({ fullscreen: true });
            var res  =  await getPageDetailImgInfo({taskid: this.$route.query.id});
            if(res?.success)
            {
                _this.directImgList = res.data.directImgList;
            }
            loadingInstance.close();
         },
    }
  }
  </script>
  
  <style leng="scss" scoped>
  .allbody{
    width: 100%;
    height: 100vh;
    margin: 0 auto;
    position: relative;
  }
  .imgbody{
    width: 100%;
    height: 100vh;
    overflow-y: auto;
    position: relative;
  }
  .rightthr{
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    position: absolute;
    width: 150px;
    height: 300px;
    right: 0;
    top: 50%;
    transform: translate(0,-50%);
}
.box-right{
    position: absolute;
    left: 60%;
    top: 0;
    margin: 0 0 0 190px;
    height: 100%;
    width: 300px;
    /* padding: 130px 20px 800px 15px; */
    /* border: 1px solid orange; */
}
.neibox{
    height: 100%;
    margin: 160px 15px 0 14px;
    /* border: 1px solid red; */
}
  </style>
  
  