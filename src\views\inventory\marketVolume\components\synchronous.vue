<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable
                    class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
            </div>
            <el-radio-group v-model="submitInfo.syncType" size="medium"
                style="display: flex;justify-content: space-between;margin-bottom: 20px;">
                <el-radio label="1" border style="width: 200px;margin-right: 20px;text-align: center;">同步信息</el-radio>
                <el-radio label="2" border style="width: 200px;text-align: center;">同步部分信息(不含单价)</el-radio>
            </el-radio-group>
            <div class="styleCode">
                <div>款式编码</div>
                <div>{{ styleCode }}</div>
            </div>
        </template>
        <div class="father_bottom">
            <div class="father_bottom_topWord">已选择编码</div>
            <div style="width: 100%">
                <el-tag v-for="(item, index) in seleteData" :key="index" closable type="" style="margin: 5px;"
                    @close="delprops(item, index)">{{
                        item
                    }}</el-tag>
            </div>
        </div>
        <vxetablebase :id="'synchronous202408041557'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :toolbarshow="false"
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" @select="checkboxRangeEnd"
            :isSelectColumn="false" style="width: 100%;height: 250px;  margin: 0" v-loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <div class="btnGroup">
                <el-button type="default" @click="close" size="medium">取消</el-button>
                <el-button type="primary" @click="submit" size="medium" v-throttle="1500">确认</el-button>
            </div>
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { syncVolumeGoods, getVolumeGoodsInfoByStyleCode } from '@/api/inventory/volumeGoods'
const tableCols = [
    { label: '', type: 'checkbox', width: '60' },
    { width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase
    },
    props: {
        goodsCode: {
            type: String,
            default: ''
        },
        styleCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {
                styleCode: this.styleCode,
                goodsCode: null,
                // toGoodsCodes: null,
                // syncType: null,
            },
            submitInfo: {
                styleCode: this.styleCode,
                goodsCode: this.goodsCode,
                toGoodsCodes: null,
                syncType: null,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            seleteData: [],
        }
    },
    async mounted() {

        await this.getList()
    },
    methods: {
        delprops(item, i) {
            this.seleteData.splice(i, 1)
            this.$nextTick(() => {
                this.tableData.forEach(f => {
                    if (item == f.goodsCode) {
                        this.$refs.table.$refs.xTable.setCheckboxRow(f, false)
                    }
                })
            })
        },
        checkboxRangeEnd(row) {
            row.forEach(item => {
                if (this.seleteData.findIndex(f => f == item.goodsCode) == -1) {
                    this.seleteData.push(item.goodsCode)
                }
            })
            //如果选中的时候,没有选中seleteData中的数据,就从seleteData中删除
            this.seleteData.forEach((item, index) => {
                if (row.findIndex(f => f.goodsCode == item) == -1) {
                    this.seleteData.splice(index, 1)
                }
            })
            if (row.length == 0) {
                this.seleteData = []
            }
        },
        close() {
            this.$emit('close')
        },
        async getList() {
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getVolumeGoodsInfoByStyleCode(this.ListInfo)
                if (success) {
                    this.tableData = data
                    this.loading = false
                    if (this.seleteData && this.seleteData.length > 0) {
                        this.$nextTick(() => {
                            this.tableData.forEach(item => {
                                if (this.seleteData.includes(item.goodsCode)) {
                                    this.$refs.table.$refs.xTable.setCheckboxRow(item, true)
                                }
                            })
                        })
                    }
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        async submit() {
            if (this.submitInfo.syncType == null) return this.$message.error('请选择同步类型')
            if (this.seleteData.length == 0) return this.$message.error('暂无选择任何数据')
            this.submitInfo.toGoodsCodes = this.seleteData.join(',')
            this.submitInfo.isConfirm = null
            const { data, success } = await syncVolumeGoods(this.submitInfo)
            if (success && data) {
                this.$confirm(`${data}`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    this.submitInfo.isConfirm = 1
                    const { data, success } = await syncVolumeGoods(this.submitInfo)
                    if (success) {
                        this.$emit('getList')
                        this.$emit('close')
                        this.$message.success('同步成功')
                    }
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            } else if (success && !data) {
                this.$emit('getList')
                this.$emit('close')
                this.$message.success('同步成功')
            } else {
                this.$message.error('同步失败')
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 20px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.styleCode {
    margin: 20px 0;
    display: flex;
    width: 100%;
    margin-top: 10px;

    div {
        width: 50%;
        text-align: center;
        border: 1px solid #ebeef5;
        height: 40px;
        line-height: 40px;
    }
}

.btnGroup {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

.father_bottom {
    height: 80px;
    overflow: auto;
    border: 1px solid #ccc;
    margin: 10px 0;

    .father_bottom_topWord {
        text-align: center;
    }
}
</style>
