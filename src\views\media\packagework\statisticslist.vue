<template>
    <!-- 统计列表 -->
    <my-container>
        <!--顶部操作-->
        <template #header>
            <allheard ref="refallheard" @onSearch="onSearch" @handleExportCommand="handleExportCommand"></allheard>
        </template>
        <!--列表-->
        <div style="height: 100%;box-sizing: border-box;padding:0 0 0 5px;">
            <el-container style="height: 100%;" >
                <packagelist ref="refpackagelist" :tableData="tableData" :isCopy="isCopy" @getTaskList="getTaskList" :storagelist="storagelist" @pids="pids" @chipids="chipids" :summaryarry="summaryarry" :type="type" :productcopy="productcopy" :tabel="3"></packagelist>
            </el-container>

        </div>
        <template #footer>
            <my-pagination :sizes="[50, 100, 200, 500, 800, 1000, 2000, 5000]" :page-size="500" ref="pager" :total="total" :checked-count="sels.length" @page-change="pagechange" @size-change="sizechange" />
        </template>
        <!--创建任务-->
        <el-dialog title="."  :show-close="false" :visible.sync="addTask" top="10vh" width="750px"
            :close-on-click-modal="false"  @close="addTask = false" element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <packagetaskeditfrom ref="packagetaskeditfrom" :onCloseAddForm="onCloseAddForm"
                :taskUrgencyList="taskUrgencyList" :groupList="groupList" :warehouselist="warehouselist"
                :userList="fpPhotoLqNameList" :platformList="platformList" :islook='false'></packagetaskeditfrom>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="addTask = false">取 消</el-button>
                    <my-confirm-button type="submit" @click="onSubmit" v-show="!islook" />
                </span>
            </template>
        </el-dialog>
        <!--编辑任务-->
        <el-drawer :visible.sync="editTaskshow" v-if="editTaskshow" :close-on-click-modal="false" direction="rtl"
            :size="750" element-loading-text="拼命加载中" v-loading="addLoading" :show-close="false">
            <packagetaskeditfrom ref="packagetaskeditfrom" :onCloseAddForm="onCloseAddForm"
                style="height: 100%;width:100%" :taskUrgencyList="taskUrgencyList" :groupList="groupList"
                :warehouselist="warehouselist" :userList="fpPhotoLqNameList" :platformList="platformList" :islook='false'>
            </packagetaskeditfrom>
        </el-drawer>
        <!--查看详情-->
        <el-dialog title="查看备注" :visible.sync="viewReferenceRemark" width="60%"
            :close-on-click-modal="false" element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <shootingTaskRemark ref="shootingTaskRemark" :rowinfo="selectRowKey" :islook="islook"></shootingTaskRemark>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReferenceRemark = false">关 闭</el-button>
                    <my-confirm-button type="submit" :loading="shootingTaskRemarkrawer" @click="sumbitshootingTaskRemark"
                        v-show="!islook" />
                </span>
            </template>
        </el-dialog>
        <!--加急审核-->
        <el-dialog title="加急审核" :visible.sync="taskUrgencyAproved" width="20%" :close-on-click-modal="false"
             element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <div style="vertical-align: middle; margin-top: 20px;margin-left: 80px;">
                <el-radio v-model="taskUrgencyStatus" label="1" border>同意</el-radio>
                <el-radio v-model="taskUrgencyStatus" label="9" border>驳回</el-radio>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="(taskUrgencyAproved = false)">取 消</el-button>
                    <my-confirm-button type="submit" @click="taskUrgencyApp" />
                </span>
            </template>
        </el-dialog>


        <el-dialog title="选择商品编码" :visible.sync="orderGoodschoiceVisible" width='85%' height='500px' v-dialogDrag
            :close-on-click-modal="false">
            <goodschoice :ischoice="true" ref="orderGoodschoice" style="z-index:10000;height:500px" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="orderGoodschoiceVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onQuerenOrderGoods()">确 定</el-button>
                </span>
            </template>
        </el-dialog>


    <!-- 新加工调入 -->
        <el-dialog :title="this.workShowTitle" :visible.sync="workshow" width="60%"
            :close-on-click-modal="false" element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading" :show-close="false">
            <el-card class="box-card">
            <div slot="header" class="clearfix">
                <span>{{ typeId==2?'成品调出':'加工调入' }}</span>
                <div style="float: right; padding: 3px 0" v-if="typeId==2">
                    <el-select style="width:150px" @change="storagechange($event,typeId)" v-model="calloutlist.wareId" :clearable="true" :collapse-tags="true"
                        filterable>
                        <el-option v-for="item in storagelist" :key="item.id" :label="item.name"
                            :value="item.wms_co_id" />
                    </el-select>
                </div>
                <div style="float: right; padding: 3px 0" v-if="typeId==1">
                    <el-select style="width:150px" @change="storagechange($event,typeId)" v-model="callinlist.wareId" :clearable="true" :collapse-tags="true"
                        filterable>
                        <el-option v-for="item in storagelist" :key="item.id" :label="item.name"
                            :value="item.wms_co_id" />
                    </el-select>
                </div>
            </div>
            <div class="text item">
                <vxe-table
                :align="null"
                :data="typeId==2?selids:chiselids">
                    <vxe-column :field="typeId==2?'packagesProcessingId':'showCode'" title="编号" width="60"></vxe-column>
                    <vxe-column :field="typeId==2?'finishedProductCode':'halfProductCode'" :title="typeId==2?'成品编码':'半成品编码'"></vxe-column>
                    <vxe-column :field="typeId==2?'finishedProductName':'halfProductName'" :title="typeId==2?'成品名称':'半成品名称'"></vxe-column>
                    <vxe-column :field="typeId==2?'totalDispatchQuantity':'incomingQuantity'" :title="typeId==2?'成品数量':'调入数量'">
                        <template #default="{ row }">
                            <el-input-number @change="numberchange(row,$event,typeId)" :step="1" controls-position="right" style="width:120px" :clearable="true" :value="row.quantity"
                                 :min="1" :max="9999"></el-input-number>
                        </template>
                    </vxe-column>
                    <vxe-column :field="typeId==2?'calloutPeople':'callinPeople'" :title="typeId==2?'调出人':'调入人'">
                        <template #default="{ row }">
                            <el-select style="width:200px" disabled :value="fiftername(loginuser.userId)" :clearable="true" :collapse-tags="true"
                                filterable>
                                <el-option v-for="(uitem, i) in userlist" :key="i" :value="uitem.userId"
                                :label="uitem.userName" />
                            </el-select>
                        </template>
                    </vxe-column>
                    <vxe-column :field="typeId==2?'calloutDate':'callinDate'" :title="typeId==2?'调拨日期':'调拨日期'">
                        <template #default="{ row }">
                            <el-date-picker :value="opentime" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                type="date" style="width:150px" placeholder="结束时间">
                            </el-date-picker>
                        </template>
                    </vxe-column>
                </vxe-table>

                <div class="qualibtn" style="width: 100%;display: flex; flex-direction: column;">
                    <div class="flexrow">
                    <div class="marginrt">
                        <el-button width="200px" size="medium" @click="workshow = false">取消</el-button>
                    </div>
                    <el-button width="200px" size="medium" type="primary" @click="savestorage(typeId)">保存</el-button>
                    </div>
                </div>
            </div>
            </el-card>
        </el-dialog>


     <!--合格证-->
    <el-dialog :visible.sync="qualificationshow" title="合格证信息">
      <template #default>
        <vxe-textarea v-model="qualfifter.msg" :disabled="disabled" placeholder="" :autosize="{ minRows: 6, maxRows: 10 }"
          clearable @input="qualificationedit" :maxlength=300 style="width: 90%;"></vxe-textarea>
        <div class="qualibtn" style="width: 100%;display: flex; flex-direction: column;">
            <div class="flexrow">
              <div class="marginrt">
                <el-button width="200px" size="medium" @click="qualificationshow = false">取消</el-button>
              </div>
              <el-button width="200px" size="medium" type="primary" @click="savecertificateInfo">保存</el-button>
            </div>
          </div>
      </template>
    </el-dialog>

    </my-container>
</template>
<script>
import packagelist from '@/views/media/packagework/packagelist.vue'
import acesTable from "@/components/Table/table.vue";
import { sendWarehouse4HotGoodsBuildGoodsDocList } from "@/utils/tools";
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import cesTable from "@/components/Table/tableforvedio.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import allheard from "./allheard.vue"
import {
    pageShootingViewTaskAsync, getTaskReferenceInfo, delShootingTploadFileTaskAsync, taskOverActionsAsync, exportShootingTaskReport
    , shootUrgencyCilckAsync, getCityAllData, shootingTaskAddOrderSaveCheckTaskIds, shootingTaskAddOrderSave, getShootingTaskOrderListById
    , signShootingTaskActionAsync, deleteShootingTaskActionAsync, unSignShootingTaskActionAsync, endShootingTaskActionAsync, deleteTaskActionAsync, endRestartActionAsync,
    getHotSaleGoodInfo,caclShootingTaskActionAsync,getUserRoleList
} from '@/api/media/ShootingVideo';
import { getShootingSetDataById, getShootingSetData, saveShootingSet, deleteShootingSet, saveDataOrderListDataAsync } from '@/api/media/shootingset'
import { getStatListAsync, getRecordUser,exportPackagesProcessingListAsync,exportPackagesProcessingDetialListAsync,
    updateProcessBatch,updateDispatcheBatch,updateFinishState,updateCertificateBatch,deleteReord,deletePackagesProcessing, getCurrentUser,
    pckagesProcessingCallOut, pckagesProcessingCallIn, statAction, archiveAction, unStatAction, getAllWarehouse, getCreateUserList
} from '@/api/inventory/packagesprocess';//包装加工
import shootingvideotaskTable from '@/views/media/shooting/shootingvideotaskTable'
import uploadfile from '@/views/media/shooting/uploadfile'
// import shootinguploadaction from '@/views/media/shooting/shootinguploadaction'
import shootingTaskRemark from '@/views/media/shooting/ShootingTaskRemark'
// import shootingvideotaskuploadfile from '@/views/media/shooting/shootingvideotaskuploadfile'
// import shootingvideotaskuploadsuccessfilesocre from '@/views/media/shooting/shootingvideotaskuploadsuccessfilesocre'
import packagetaskeditfrom from '@/views/media/packagework/packagetaskeditfrom'
import shootingchartforfp from '@/views/media/shooting/shootingchartforfp'
import { getVedioTaskOrderAddressList, saveVedioTaskOrderAddress, deleteVedioTaskOrderAddress } from '@/api/media/vediotask';
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { formatTime } from "@/utils";
import { rulePlatform } from "@/utils/formruletools";
import { formatWarehouse, ShootingVideoTaskUrgencyOptions } from "@/utils/tools";
import { getDirectorGroupList } from '@/api/operatemanage/base/shop';
import goodschoice from "@/views/base/goods/goods4.vue";
import logistics from '@/components/Comm/logistics'
import orderLogPage from "@/views/order/logisticsWarning/orderLogPage.vue";
// import {getAllWarehouse} from '@/api/inventory/warehouse'


function rowChanged4EstimateStockInAmount(x) {
    //let x = row;
    let estimateStockInCount = isNaN(x.estimateStockInCount) ? 0 : x.estimateStockInCount;
    let costPrice = isNaN(x.costPrice) ? 0 : x.costPrice;
    x.estimateStockInAmount = (estimateStockInCount * costPrice);
}
const skuTableCols = [

    { istrue: true, prop: 'yhGoodsCode', label: '商品编码', width: '180' },
    { istrue: true, prop: 'yhGoodsName', label: '商品名称', minwidth: '200' },
    { istrue: true, prop: 'goodsProgressType', label: '商品类型', width: '90', formatter: (row) => row.isMainSale == 1 ? '成品' : '半成品' },
    { istrue: true, prop: 'isMainSale', label: '是否主卖', width: '80', formatter: (row) => row.isMainSale == 1 ? '是' : '否' },
    { istrue: true, prop: 'isZengPin', label: '有无赠品', width: '80', formatter: (row) => row.isZengPin == 1 ? '有' : '无' },
    { istrue: true, prop: 'isJiaGong', label: '是否加工', width: '80', formatter: (row) => row.isJiaGong == 1 ? '是' : '否' },
    { istrue: true, prop: 'remark', label: '备注', width: '150' },
];

const tableCols = [
    // { istrue: true, permission: 'shootTaskId', label: '', type:'checkbox', width: '50', fixed: 'left' },
    { istrue: true, permission: 'shootTaskId', prop: 'shootingTaskId', label: '编号', width: '50', fixed: 'left' },
    { istrue: true, permission: 'shootProudctName', prop: 'productShortName', label: '产品简称', width: '135', fixed: 'left', type: "click", handle: (that, row) => that.openComputOutInfo(row) },
    { istrue: true, type: "urgencyediticon", width: "38", fixed: 'left', label: '' },
    {
        istrue: true, permission: 'shootUrgency', prop: 'taskUrgencyName', label: '紧急程度', width: '80', fixed: 'left', type: 'UrgencyButton'
        , handle: (that, row) => that.shootUrgencyCilck(row.taskUrgencyName, row.shootingTaskId)
    },
    { istrue: true, permission: 'shootWarehouse', prop: 'warehouseStr', label: '大货仓', width: '100', fixed: 'left' },
    { istrue: true, permission: 'shootRemarks', prop: 'beizhu', label: '', width: '38', type: "clickflag", fixed: 'left', handle: (that, row) => that.openTaskRmarkInfo(row) },
    { istrue: true, permission: 'shootReference', prop: 'cankao', type: "fileicon", width: "38", label: '', fixed: 'left', handle: (that, row) => that.videotaskuploadfileDetal(row) },
    { istrue: true, permission: "api:media:shootingvideo:AddOrUpdateShootingVideoTaskAsync", prop: 'caozoulie', type: "editicon", fixed: 'left', width: "40", label: '', handle: (that, row) => that.editTask(row) },
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)' },
    //照片
    { istrue: true, summaryEvent: true, permission: 'shootPhoto', prop: 'photoLqNameStr', label: '照片', width: '65' },
    { istrue: true, permission: 'shootDays', type: "time", prop: 'photoDaysStr', label: '天数', width: '53', },
    { istrue: true, permission: 'shootCompleteDate', type: "time", prop: 'photoOverTimeStr', width: '75', label: '完成日期' },

    //视频
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)', label: '' },
    { istrue: true, summaryEvent: true, permission: 'shootVideo', prop: 'vedioLqNameStr', label: '视频', width: "65", },
    { istrue: true, permission: 'shootDays', type: "time", prop: 'vedioDaysStr', label: '天数', width: '53' },
    { istrue: true, permission: 'shootCompleteDate', type: "time", prop: 'vedioOverTimeStr', label: '完成日期', width: '75', },
    { istrue: true, permission: 'shootConfirmationPersion', prop: 'vedioConfirmNameStr', label: '确认人', width: "65", },
    { istrue: true, permission: 'shootConfirmationDate', type: "time", prop: 'vedioConfirmTimeStr', label: '确认日期', width: '75', },
    //微详情
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)', label: '' },
    { istrue: true, summaryEvent: true, permission: 'shootMicrodetails', prop: 'microDetailLqNameStr', label: '微详情', width: "65", },
    { istrue: true, permission: 'shootDays', type: "time", prop: 'microDetailDaysStr', label: '天数', width: '53', },
    { istrue: true, permission: 'shootCompleteDate', type: "time", prop: 'microDetailOverTimeStr', width: '75', label: '完成日期' },
    { istrue: true, permission: 'shootQuantity', prop: 'microDetailVedioCounts', width: '54', label: '数量' },
    { istrue: true, permission: 'shootConfirmationPersion', prop: 'microDetailConfirmNameStr', label: '确认人', width: "65", },
    { istrue: true, permission: 'shootConfirmationDate', type: "time", prop: 'microDetailConfirmTimeStr', label: '确认日期', width: '75', },
    //详情页
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)', label: '' },
    { istrue: true, summaryEvent: true, permission: 'shootDetailsPage', prop: 'detailLqNameStr', label: '详情页', width: "65", },
    { istrue: true, permission: 'shootDays', prop: 'detailDaysStr', type: "time", label: '天数', width: '53', },
    { istrue: true, permission: 'shootCompleteDate', prop: 'detailOverTimeStr', type: "time", width: '75', label: '完成日期', },
    { istrue: true, permission: 'shootConfirmationPersion', prop: 'detailConfirmNameStr', label: '确认人', width: "65", },
    { istrue: true, permission: 'shootConfirmationDate', type: "time", prop: 'detailConfirmTimeStr', label: '确认日期', width: '75', },

    //照片建模
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)', label: '' },
    { istrue: true, summaryEvent: true, permission: 'shootPhotoModeling', prop: 'modelPhotosLqNameStr', width: "75", label: '照片建模' },
    { istrue: true, permission: 'shootDays', prop: 'modelPhotosDaysStr', type: "time", label: '天数', width: '53', },
    { istrue: true, permission: 'shootCompleteDate', prop: 'modelPhotosOverTimeStr', type: "time", width: '75', label: '完成日期' },
    { istrue: true, permission: 'shootSheets', prop: 'modelPhotoCounts', width: '53', label: '张数' },


    //视频建模
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)', label: '' },
    { istrue: true, summaryEvent: true, permission: 'shootVideoModeling', prop: 'modelVideoLqNameStr', width: "75", label: '视频建模' },
    { istrue: true, permission: 'shootDays', prop: 'modelVideoDaysStr', type: "time", label: '天数', width: '53' },
    { istrue: true, permission: 'shootCompleteDate', prop: 'modelVideoOverTimeStr', type: "time", width: '80', label: '完成日期' },
    { istrue: true, permission: 'shootGs', prop: 'modelVedioCounts', width: '53', label: '个数' },

    //分配
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)', label: '' },
    { istrue: true, summaryEvent: true, permission: 'shootFpPhoto', prop: 'fpPhotoLqNameStr', width: '75', label: '分配照片' },
    { istrue: true, summaryEvent: true, permission: 'shootFpVedio', prop: 'fpVideoLqNameStr', width: '75', label: '分配视频' },
    { istrue: true, summaryEvent: true, permission: 'shootFpDetail', prop: 'fpDetailLqNameStr', width: '75', label: '分配详情' },
    { istrue: true, summaryEvent: true, permission: 'shootFpModel', prop: 'fpModelLqNameStr', width: '75', label: '分配建模' },
    //
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)', label: '' },
    { istrue: true, permission: 'shootOperate', prop: 'operationGroupstr', align: 'left', width: '80', label: '运营小组' },
    { istrue: true, permission: 'shootCounter', prop: 'dockingPeople', align: 'left', width: '65', label: '对接人' },
    { istrue: true, permission: 'shootPlatform', prop: 'platformStr', align: 'left', width: '80', label: '平台' },
    { istrue: true, permission: 'shootShop', prop: 'shopNameStr', align: 'left', width: '180', label: '店铺' },

    { istrue: true, prop: 'productID', align: 'left', width: '80', label: '产品ID' },
    { istrue: true, summaryEvent: true, permission: 'shootTaskOverTime', prop: 'taskOverTimeStr', width: '100', label: '完成时间' },
    { istrue: true, prop: 'confirmTimeStr', align: 'left', width: '100', label: '确认时间' },

    { istrue: true, permission: 'shootArrivalTime', prop: 'arrivalTimeStr', width: '75', label: '到货日期' },
    { istrue: true, permission: 'shootArrivalDays', prop: 'arrivalTimeDays', width: '53', label: '到货天数' },
    { istrue: true, permission: 'shootDeliverTime', prop: 'deliverTimeStr', width: '75', label: '发货日期' },
    { istrue: true, permission: 'shootDeliverDays', prop: 'deliverTimeDays', width: '53', label: '发货天数' },
    { istrue: true, permission: 'shootApplicationTime', prop: 'applyTimeStr', width: '75', label: '申请日期' },
    { istrue: true, permission: 'shootApplicationDay', prop: 'applyTimeDays', width: '53', label: '申请天数' },
    { istrue: true, prop: 'mainUpdateTime', align: 'left', width: '100', label: '修改日期', formatter: (row) => row.mainUpdateTime == null ? null : formatTime(row.mainUpdateTime, 'YY-MM-DD') },
    { istrue: true, permission: 'shootCreationDate', prop: 'createdTime', width: '90', label: '创建日期', sortable: 'custom', formatter: (row) => row.createdTimeStr },

    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)', label: '' },
    { istrue: true, permission: 'shootorderNo', prop: 'orderNoInner', width: '100', label: '内部单号', type: 'click', handle: (that, row, column) => that.onShowOrderDtl(row) },
    { istrue: true, permission: 'shootorderExpressNumber', prop: 'expressNo', width: '135', label: '快递单号', type: 'click', handle: (that, row, column) => that.onShowExproessHttp(row) },
    { istrue: true, permission: 'shootorderNo', prop: 'shootOrderTrack', width: '80', label: '拿样跟踪', type: 'click', handle: (that, row, column) => that.onShowOrderDtl(row) },
];

export default {
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, shootingchartforfp
        , vxetablebase, cesTable, goodschoice, logistics, uploadfile, shootingTaskRemark
        , packagetaskeditfrom, orderLogPage
        , acesTable, shootingvideotaskTable,  packagelist,
        allheard
    },
    inject: [],
    props: ["tablekey", 'role','type'],
    watch: {
    },
    data() {
        return {
            productcopy: {},//复制所有成品编码
            isCopy: true,
            taskname: '',
            storage: '',
            inputshow: true,
            addLoading: true,
            selshootingTaskId: 0,
            taskUrgencyStatus: "1",
            taskUrgencyAproved: false,
            workshow:false,
            workShowTitle:"",
            Oncommand: 'a',
            shootingTaskRemarkrawer: false,
            viewReferenceRemark: false,
            shootingvediotask: 'shootingvediotask',
            opentime: new Date(),
            tjopentime: new Date().getTime() + 1,
            urgencyopentime: new Date().getTime() + 2,
            markopentime: new Date().getTime() + 3,
            fileopentime: new Date().getTime() + 4,
            outsueccessopentime: new Date().getTime() + 5,
            outopentime: new Date().getTime() + 6,
            editopentime: new Date().getTime() + 7,
            outconfilekey: null,
            fpcharttype: null,
            //分配趋势图
            shootingchartforfpvisible: false,
            //上传成果文件
            successfiledrawer: false,
            successfileshow: false,
            //查看参考
            viewReference: false,
            //选中的行
            selectRowKey: null,
            that: this,
            pageLoading: true,
            islook: false,
            filter: {
                currentPage: 1,
                pageSize: 300,
                orderBy: "",
                isAsc: true,
                packagesProcessId: null,
                finishedProductCode: "",
                halfProductCode: "",
                brandCode: "",
                packingMaterialCode: "",
                machineTypeCode: "",
                packageSizeCode: "",
                isProcess: null,
                processer: "",
                qualityInspector: "",
                startCreateTime: "",
                endCreateTime: "",
                createdtimerange: [],
                IsNeedDetial:false,
                dateType:null,
                createPackId:"",
                quantity:""
            },
            // allsellist: {},
            callinlist: {},
            tableData: [],
            packageSizeName: [],
            machineTypeName: [],
            packingMaterialName: [],

            storagelist: [],
            supplierName: [],//供应商
            listarry: [],
            linkval: '',
            fomSendWarehouse4HotGoodsBuildGoodsDocList: sendWarehouse4HotGoodsBuildGoodsDocList,
            skuTableCols: skuTableCols,

            tasklist: [],
            taskPageTitle: "创建加工",
            referenceVideoList: [],
            multipleSelection: [],
            formatWarehouse: formatWarehouse,
            warehouselist: [],
            shopList: [],
            userList: [],
            groupList: [],
            fpDetailLqNameList: [],
            fpModelLqNameList: [],
            fpPhotoLqNameList: [],
            fpVideoLqNameList: [],
            dockingPeopleList: [],
            taskUrgencyList: ShootingVideoTaskUrgencyOptions,
            platformList: [],
            tableCols: tableCols,
            total: 0,
            //选中的行id
            selids: [],
            chiselids: [],
            taskPhotofileList: [],
            taskExeclfileList: [],

            packageSizeList: [],
            machineTypeList: [],
            packingMaterialList: [],
            brandList: [],

            addTask: false,
            editTaskshow: false,
            loginfo: null,
            calloutlist: {},
            summaryarry: {},
            pager: {},
            sels: [], // 列表选中列
            listLoading: false,
            //下单发货
            orderGoodschoiceVisible: false,//选择下单任务商品窗口
            receiverStateList: [],//省
            receiverCityList: [],//市
            receiverDistrictList: [],//区
            receiverCityList2: [],//市
            receiverDistrictList2: [],//区
            dialogAddOrderVisible: false,
            meidialogAddOrderVisible: false,
            dialogAddOrderLoading: false,
            dialogAddOrderSubmitLoding: false,
            addOrderForm: {
                shootingTaskIds: "",
                receiverName: "",
                receiverPhone: "",
                receiverStateCode: "",
                receiverCityCode: "",
                receiverDistrictCode: "",
                receiverState: "",
                receiverCity: "",
                receiverDistrict: "",
                receiverAddress: "",
                isZt: 0,
                warehouse: null,
                remark: "",
                receiverAddressAllInfo: "",
                orderGoods: [],
            },
            selShootingTaskIdSpanList: [],
            orderGoodschoiceVisible: false,
            loginuser: {},
            addOrderFormRules: {
                //receiverName: [{ required: true, message: '请输入收货人', trigger: 'blur' }],
                //receiverPhone: [{ required: true, message: '请输入收货电话', trigger: 'blur' }],
                isZt: [{ required: true, message: '请输入是否自提', trigger: 'blur' }],
                //receiverStateCode: [{ required: true, message: '请输入收货省', trigger: 'blur' }],
                //receiverCityCode: [{ required: true, message: '请输入收货市', trigger: 'blur' }],
                //receiverDistrictCode: [{ required: true, message: '请输入成收货区', trigger: 'blur' }],
                //receiverAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
                //receiverAddressAllInfo: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
            },
            dialogOrderDtlVisible: false,
            dialogOrderDtlLoading: false,

            xdfhmainlist: [],
            xdfhmainLoading: false,

            xdfhdtllist: [],
            xdfhdtlLoading: false,

            receiverAddressList: [],
            dialogAddressVisible: false,
            addressListLoading: false,
            dialogAddAddressSubmitLoding: false,
            addAddressForm: {
                receiverName: "",
                receiverPhone: "",
                receiverStateCode: null,
                receiverCityCode: null,
                receiverDistrictCode: null,
                receiverAddress: "",
            },
            addressList: [],
            addAddressFormRules: {
                receiverName: [{ required: true, message: '请输入收货人', trigger: 'blur' }],
                receiverPhone: [{ required: true, message: '请输入收货电话', trigger: 'blur' }],
                receiverStateCode: [{ required: true, message: '请输入收货省', trigger: 'blur' }],
                receiverCityCode: [{ required: true, message: '请输入收货市', trigger: 'blur' }],
                receiverDistrictCode: [{ required: true, message: '请输入成收货区', trigger: 'blur' }],
                receiverAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
            },
            drawervisible: false,
            sendOrderNoInner: "",
            dialogHisVisible: false,
            calcSkuTableData: [],
            typeId: null,
            qualificationshow:false,
            qualfifter: {
                msg:''
            },
            pl: {
                a: 0,
                b: 0,
                c: 0,
                d: 0,
                e: 0,
                f: 0,
                g: 0,
                h: 0,
                i: 1,
                k: 1
            },
            heardbtn: {
                a: false,
                b: false,
                c: false,
                d: true,
                e: true,
                f: false,
            },
            chicheckdata: {},
            checkdata: {}

        };
    },
    watch: {
    },
    async created() {
        console.log(this.type,'type');
        // await this.gettabmsg();
        // await this.getuser();
        await this.getloginuser();
        await this.getstorage();
    },
    async mounted() {
        await this.showseltype();
        await this.onSearch();
    },
    methods: {
        updatelist(val){
            this.$refs.refpackagelist.updatelist(val)
        },
        showseltype(){
            this.$refs.refallheard.showseltype(this.pl);
            this.$refs.refallheard.showheardbtn(this.heardbtn);
        },
        changearr(index,val){
            switch(index){
                case 1:
                    this.filter.packingMaterialName = val.join();
                    break;
                case 2:
                    this.filter.machineTypeName = val.join();
                    break;
                case 3:
                    this.filter.packageSizeName = val.join();
                    break;
            }
        },
        numberchange(row,num,type){
            switch (type) {
                case 1:
                    var index = this.chiselids.indexOf(row);
                    this.callinlist.detialList[index].quantity = num;
                    break;
                case 2:
                    var index = this.selids.indexOf(row);
                    this.calloutlist.detialList[index].quantity = num;
                    break;
            }


            // this.calloutlist
        },
        //提交调出申请
        async savestorage(index){
            // return
            switch (index) {
                case 1:
                    if(!this.callinlist.wareId){
                        this.$message({ type: 'warning', message: "请选择仓库" });
                        return
                    }
                    var res = await pckagesProcessingCallIn(this.callinlist);
                    if (!res?.success) {
                        return
                    }
                    // this.storagelist = res.data;
                    this.$message({ type: 'success', message: "操作成功" });
                    this.workshow = false;
                    break;
                case 2:
                    if(!this.calloutlist.wareId){
                        this.$message({ type: 'warning', message: "请选择仓库" });
                        return
                    }
                    var res = await pckagesProcessingCallOut(this.calloutlist);
                    if (!res?.success) {
                        return
                    }
                    // this.storagelist = res.data;

                    this.$message({ type: 'success', message: "操作成功" });
                    this.workshow = false;
                    break;
            }
        },
        async getstorage(){
            const res = await getAllWarehouse();
            if (!res?.success) {
                return
            }
            this.storagelist = res.data;
        },
        storagechange(val,index){
            var name = '';

            switch (index) {
                case 1:
                    this.storagelist.map((item)=>{
                        if(item.wms_co_id == val){
                            this.callinlist.wareName = item.name;
                        }
                    })
                    break;
                case 2:
                    this.storagelist.map((item)=>{
                        if(item.wms_co_id == val){
                            this.calloutlist.wareName = item.name;
                        }
                    })
                    break;
            }

        },
        async getloginuser(){
            const res = await getCurrentUser();
            if (!res?.success) {
                return
            }
            this.loginuser = res.data;
        },
        fiftername(value){
            var info = ' '
            this.userlist.forEach((item)=>{
                if (item.userId == value) info = item.userName
            })
            return info;
        },
        pids(data){
           this.selids=data;
           this.calloutlist.detialList = data;
           this.calloutlist.detialList.map((item)=>{
                item.selUserId = this.loginuser.userId;
                item.produnctName = item.finishedProductName;
                item.produnctCode = item.finishedProductCode;
                item.packaesProcessingId = item.packagesProcessingId;
           })

           ///////////////
           this.checkdata.detialList = this.calloutlist.detialList;
            this.checkdata.selids = data;

            this.$nextTick(()=>{
                    this.$refs.refallheard.checkdatafuc(this.checkdata);
            });
        },
        chipids(data){
           this.chiselids=data;
           this.callinlist.detialList = data;
           this.callinlist.detialList.map((item)=>{
                item.selUserId = this.loginuser.userId;
                item.produnctName = item.halfProductName;
                item.produnctCode = item.halfProductCode;
                item.packaesProcessingId = item.packagesProcessingId;
                item.packaesProcessingDetialId = item.id;
           })

           //////////////
           this.$nextTick(()=>{
                this.chicheckdata.chiselids = data;
                this.chicheckdata.detialList = this.callinlist.detialList;
                this.$refs.refallheard.chicheckdatafuc(this.chicheckdata);
            });
        },
        async getuser(){
            const params = {
                packagesProcessId: 1,
                typeId: 3
            }
            const res = await getRecordUser();
            if (!res?.success) {
                return
            }
            this.userlist=res.data;
            this.$refs.refpackagelist.getuser(res.data)
        },
        newgetuser(data){
            this.$refs.refpackagelist.getuser(data)
        },
        async gettabmsg(){
            await this.getDataSetList(14);
            await this.getDataSetList(15);
            await this.getDataSetList(16);
            await this.getDataSetList(17);
            await this.getcrepeople();
            this.$nextTick(()=>{
                this.$refs.refallheard.getallmsg(this.allsellist);
            });
        },
        getallmsg(data){
            this.$refs.refallheard.getallmsg(data);
        },
        async getcrepeople(){
            const res = await getCreateUserList();
            if (!res?.success) {
                return
            }
            // this.createUser = res.data;
            this.allsellist.createUser = res.data;
        },
        async getDataSetList(index) { //14包装加工-品牌，15包装加工-包装方式，16包装加工-机型，17包装加工-尺寸
            this.listLoading = true;
            const res = await getShootingSetData({ setType: index });
            if (!res?.success) {
                return
            }
            switch (index) {
                case 14:
                    this.packingMaterialList = res?.data?.data;
                    break;
                case 15:
                    this.brandList = res?.data?.data;
                    break;
                case 16:
                    this.machineTypeList = res?.data?.data;
                    break;
                case 17:
                    this.packageSizeList = res?.data?.data;
                    break;
            }

            this.allsellist.packingMaterialList = this.packingMaterialList;
            this.allsellist.brandList = this.brandList;
            this.allsellist.machineTypeList = this.machineTypeList;
            this.allsellist.packageSizeList = this.packageSizeList;

            this.listLoading = false;
        },
        async getrole() {
            var res = await getUserRoleList();
            if(res?.success)
            {
               if(res.data == null){
                    this.role ="tz";
               }else if (res.data.indexOf("视觉部经理") >-1){
                    this.role ="b";
               }

            }else{
                this.role ="tz";
            }

        },
        rowChanged4EstimateStockInAmount: rowChanged4EstimateStockInAmount,
        selchange(val) {
            this.supplierName.map((item) => {
                if (item.supplierId == val) {
                    this.filter.supplierLink = item.supplierLink;

                }
            })

            this.listarry.map((item) => {
                if (item.supplierId == val) {
                    this.calcSkuTableData = item.goods;
                }
            })

        },
        linkTo(val) {
            window.open(this.filter.supplierLink);
        },
        // 重置
        onclear() {
            this.filter = {};
        },
        getname(val) {
            this.taskname = val;
        },
        inputshowfunc() {
            this.inputshow = false;
        },
        toResultmatter(row) {
            if (row != null) {
                let routeUrl = this.$router.resolve({
                    path: '/resultmatter',
                    query: { id: row.shootingTaskId }
                });
                window.open(routeUrl.href, '_blank');
            }
        },
        async onsummaryClick(property) {
            debugger
            this.fpcharttype = property;
            this.$nextTick(function () {
                this.$refs.shootingchartforfp.showviewMain();
            });
            this.shootingchartforfpvisible = true;
        },
        async onExeprotShootingTask() {
            this.$refs.shootingvideotask.exportData("新品拍摄导出")
        },
        async handleExportCommand(command)
        {
            var res = null;
            var fileName = "包装加工-"
            this.filter.TypeList=1;
            const params = { ... this.filter }
            // this.pageLoading = true;
            this.$refs.refpackagelist.tableloading = true;
            if (command == 'a') {
                 res = await exportPackagesProcessingListAsync(params);
                 fileName=fileName+"成品_"
            }else
            {
                 res = await exportPackagesProcessingDetialListAsync(params);
                 fileName=fileName+"半成品_"
            }
            this.$refs.refpackagelist.tableloading = false;
            this.pageLoading = false;
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', fileName + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        }
        ,async handleCommand(command) {
            if (this.selids.length == 0 && command != 'x') {
                this.$message({ type: 'warning', message: "请选择加工" });
                return;
            }
            var ids = this.selids.map(a => a.packagesProcessingId).join(",");
            switch (command) {
                //批量完成
                case 'a':
                    await this.onTaskOverActionShared(ids)
                    break;
                //批量重启
                case 'b':
                    await this.onEndRestartActionAsyncShared(ids);
                    break;
                //批量终止
                case 'c':
                    await this.OnendShootingTaskAction(ids)
                    break;
                //批量标记
                case 'd':
                    await this.onSignShootingTaskActionShared(ids)
                    break;
                //取消标记
                case 'f':
                    await this.onUnSignShootingTaskActionShared(ids)
                    break;
                //取消统计
                case 'e':
                    await this.deleteTaskActionShared(ids)
                    break;
                //批量统计
                case 'g':
                    await this.caclShootingTaskActionAsyncShared(ids)
                    break;
                      //批量合格证
                case 'h':
                     this.qualificationshowfuc(this.selids)
                    break;
                     //批量归档
                case 'i':
                     this.batcharchivelist(ids)
                    break;

            }
        },
        batcharchivelist(array) {
            this.$confirm("选中的任务将进行归档，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await archiveAction(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.selids = [];
                    await this.onRefresh();
                    await this.$emit('refresh', 'tab2');
                    await this.$emit('refresh', 'tab3');
                }
            });
        },

        //批量统计
        async caclShootingTaskActionAsyncShared(array) {
            this.$confirm("选中的任务将进行统计，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await statAction(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.selids = [];
                    await this.onRefresh();
                }
            });
        },
        qualificationshowfuc(row){
            this.qualificationshow = true;
        },
        //批量更新合格证
        async savecertificateInfo()
        {
            this.$confirm("是否批量更新合格证", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var ids = this.selids.map(a => a.packagesProcessingId);
                let params = {
                    packProcessingIds: ids,
                    certificateInfo:this.qualfifter.msg
                }
                var res = await updateCertificateBatch(params);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.selids = [];
                    this.qualificationshow = false;
                    await this.onRefresh();
                }
            });
        },
        //完成操作公共
        async onTaskOverActionShared(array) {
            this.$confirm("是否确定完成", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let params = {
                    processId: array
                }
                var res = await updateFinishState(params);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.selids = [];
                    await this.onRefresh();
                }
            });
        },
        //存档操作
        async onTaskShopActionShared(array) {
            this.$confirm("选中的任务移动到存档列表，是否确定存档", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await taskShopActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //重新启动
        async onEndRestartActionAsyncShared(array) {
            this.$confirm("选中的终止任务会重新启动，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await endRestartActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //批量标记
        async onSignShootingTaskActionShared(array) {
            this.$confirm("选中的任务将会进行标记，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await signShootingTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //取消标记
        async onUnSignShootingTaskActionShared(array) {
            this.$confirm("选中的任务将会取消标记，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await unSignShootingTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },

        //回收站彻底删除操作
        async deleteTaskActionShared(array) {
            this.$confirm("是否取消统计", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await unStatAction(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //批量终止onGetdrowList
        async OnendShootingTaskAction(array) {
            this.$confirm("选中任务将会终止，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await endShootingTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //获取下拉数据
        async onGetdrowList() {
            var pfrule = await rulePlatform();
            this.platformList = pfrule.options;
            var res = await getDirectorGroupList();
            this.groupList = res.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async onGetdrowList2() {
            var res = await this.getwarehouseListRelod();
            this.warehouselist = res?.map(item => { return { value: item.id, label: item.label }; });
        },
        async onchangeplatform(val) {
            var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100 });
            this.filter.shopName = "";
            this.shopList = res1.data.list;
        },
        //获取分配人下拉，对接人下啦
        async getShootingViewPer() {
            var res = await this.getShootingViewPersonRelod();
            if (res) {
                this.dockingPeopleList = res.dockingPeopleList;
                this.fpPhotoLqNameList = res.fpallList;
            }
        },
        async ShowHideonSearch() {
            this.listLoading = true;
            var checkedColumnsFora = [];
            switch (this.Oncommand) {
                //显示全部 ,部门经理，超管
                case "a":
                    checkedColumnsFora = [];

                    // 显示所有

                    break;
                //显示默认
                case "b":
                    // checkedColumnsFora = [];
                    checkedColumnsFora =
                        ['Divisionline','caozoulie'
                            , 'photoDaysStr', 'photoOverTimeStr'
                            , 'vedioDaysStr', 'vedioOverTimeStr', 'vedioConfirmNameStr', 'vedioConfirmTimeStr'
                            , 'microDetailDaysStr', 'microDetailOverTimeStr', 'microDetailVedioCounts', 'microDetailConfirmNameStr'
                            , 'microDetailConfirmTimeStr'
                            , 'detailDaysStr', 'detailOverTimeStr', 'detailConfirmNameStr', 'detailConfirmTimeStr'
                            , 'modelPhotosDaysStr', 'modelPhotosOverTimeStr', 'modelPhotoCounts'
                            , 'modelVideoDaysStr', 'modelVideoOverTimeStr', 'modelVedioCounts'
                            , 'productID'
                            , 'operationGroupstr', 'dockingPeople', 'platformStr', 'shopNameStr'
                            , 'arrivalTimeDays', 'deliverTimeStr', 'deliverTimeDays', ];

                            // 排除
                            // 分割线，操作
                            // ，照片天数，照片完成日期
                            // ，视频天数，视频完成日期，视频确认人，视频确认日期，微。视频天数，微。视频完成日期
                            // ，微。视频数量，微。视频确认人，微。视频确认日期，详情页天数，详情页完成日期，详情页确认人，详情页确认日期
                            // ，建模照片天数，建模照片完成日期，建模照片张数，建模视频天数，建模视频完成日期，建模视频个数
                            // ，产品ID
                            // ，运营小组，对接人，平台，店铺
                            // ，到货天数，发货日期，发货天数，

                    break;

                default:
                    break;
            }
            await this.$refs.shootingvideotask.ShowHidenColums(checkedColumnsFora);
            this.listLoading = false;
        },
        //查询
        async onSearch(data) {
            // this.pageLoading = true;
            this.$refs.refpackagelist.tableloading = true;
            if(data){
                this.filter = data;
            }
            this.$refs.pager.setPage(1);
            this.getTaskList();
            this.selids = [];
        },
        //刷新当前页
        async onRefresh() {
            // this.pageLoading = true;
            this.$refs.refpackagelist.tableloading = true;
            await this.getTaskList();
        },

        keyDown() {
                var that = this;
                //监听键盘按钮
                document.onkeydown = function (event) {
                    var e = event || window.event;
                    var keyCode = e.keyCode || e.which;
                    //向前
                    if (keyCode == 13) {

                        that.onSearch()
                    }
                    if (keyCode == 50) {
                        that.Oncommand='a';
                        that.ShowHideonSearch()
                    }
                    if (keyCode == 49) {
                        that.Oncommand='b';
                        that.ShowHideonSearch()
                    }



                }
            },
        sizechange(val){
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.onRefresh();
        },
        pagechange(val){
            this.filter.currentPage = val;
            this.onRefresh();
        },

        //获取数据
        async getTaskList() {
            var pager = this.$refs.pager.getPager();
            if (this.filter.createdtimerange) {
                this.filter.startCreateTime = this.filter.createdtimerange[0];
                this.filter.endCreateTime = this.filter.createdtimerange[1];
            } else {
                this.filter.startCreateTime = null;
                this.filter.endCreateTime = null;
            }
            // this.filter.isComplate = this.filter.isComplateChecked == true ? 0 : 1;
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter,
            };
            //复制所有成品编码
            this.productcopy = { ...params , typeList: 1 };
            // debugger
            // params.platform.length>0?params.platform:params.platform=""
            this.listLoading = true;
            const res = await getStatListAsync(params);

            this.tableData = res.data.list;
            this.$refs.refpackagelist.updatelist()
            // this.$refs.refpackagelist.updatelist(res.data)
            // debugger
            if (!res?.success) return;
            this.listLoading = false;
            this.total = res.data.total;
            this.tasklist = res.data.list;
            this.summaryarry = res.data.summary;
            this.pageLoading = false;
        },
        //列表排序
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async rowChange(row) {

            await this.editTask(row);
        },
        //编辑任务
        async editTask(row) {
            this.addLoading = true;
            this.editopentime = this.editopentime + 1;
            this.taskPageTitle = "编辑任务";
            this.editTaskshow = true;
            await this.$nextTick(function () { this.$refs.packagetaskeditfrom.editTask(row); });
            this.addLoading = false;
        },
        //新增任务
        onAddTask() {
            this.addLoading = true;
            this.opentime = this.opentime + 1;
            this.addTask = true;
            this.taskPageTitle = "创建任务";
            // await this.$nextTick(function () { this.$refs.packagetaskeditfrom.initaddform(); });
            let _this = this;
            this.$nextTick(()=>{
                _this.$refs.packagetaskeditfrom.initaddform();
            })
            this.islook = false;
            this.addLoading = false;
        },
        //提交保存
        async onSubmit() {
            this.addLoading = true;
            await this.$nextTick(function () {
                this.$refs.packagetaskeditfrom.onSubmit();
            });
            this.addLoading = false;
        },
        //删除上传附件操作
        async deluplogexl(ret) {
            this.addLoading = true;

            await delShootingTploadFileTaskAsync({ upLoadPhotoId: ret.upLoadPhotoId, type: 2 }).catch(_ => {
                this.addLoading = false;
            });

            this.addLoading = false;
        },
        //删除上传图片操作
        async deluplogimg(ret) {
            this.addLoading = true;
            await delShootingTploadFileTaskAsync({ upLoadPhotoId: ret.upLoadPhotoId, type: 1 }).catch(_ => {
                this.addLoading = false;
            });
            this.addLoading = false;
        },
        //关闭窗口，初始化数
        async onCloseAddForm(type) {
            await this.onSearch();
            if (type == 1) {
                this.addTask = false;
                this.editTaskshow = false;
            }
        },

        //紧急程度按钮点击
        async shootUrgencyCilck(btnstr, shootingTaskId) {
            debugger
            var that = this;
            switch (btnstr) {
                //申请加急
                case "正常":
                    this.$confirm("是否确认加急?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    })
                        .then(async () => {
                            var res = await shootUrgencyCilckAsync({ taskid: shootingTaskId, index: 0 });
                            if (res?.success) {
                                that.$message({ message: '操作成功', type: "success" });
                                await that.onRefresh();
                            }
                        });
                    break;
                //确认加急
                case "审核":
                    this.opentime++;
                    this.selshootingTaskId = shootingTaskId;
                    this.taskUrgencyAproved = true;
                    break;
            }
        },
        async taskUrgencyApp() {
            var res = await shootUrgencyCilckAsync({ taskid: this.selshootingTaskId, index: this.taskUrgencyStatus });
            if (res?.success) {
                this.taskUrgencyAproved = false;
                this.$message({ message: '操作成功', type: "success" });
                await this.onRefresh();
            }
        },

        //查看详情备注页
        openTaskRmarkInfo(row) {
            this.markopentime = this.markopentime + 1;
            this.selectRowKey = row.shootingTaskId;
            this.viewReferenceRemark = true;
        },
        //成果文件上传
        onUploadSuccessFile(row) {
            this.opentime = this.opentime + 1;
            this.selectRowKey = row.shootingTaskId;
            this.outconfilekey = row.shootingTaskId;
            this.successfiledrawer = true;
        },
        //关闭成果文件上传
        successfiledrawerClose() {
            this.successfiledrawer = false;
        },
        //查看参考附件
        videotaskuploadfileDetal(row) {
            this.opentime = this.opentime + 1;
            this.selectRowKey = row.shootingTaskId;
            // this.viewReference = true;

            if (row != null) {
                let routeUrl = this.$router.resolve({
                    path: '/seereference',
                    query: { id: row.shootingTaskId, refid: row.referenceId }
                });
                window.open(routeUrl.href, '_blank');
            }

        },
        //查看成果文件
        openComputOutInfo(row) {
            this.outsueccessopentime = this.outsueccessopentime + 1;
            this.selectRowKey = row.shootingTaskId;
            this.successfileshow = true;
        },
        successfiledrawerscoreClose() {
            this.successfileshow = false;
        },

        async sumbitshootingTaskRemark() {
            this.shootingTaskRemarkrawer = true;
            await this.$refs.shootingTaskRemark.onsubmit();
            this.shootingTaskRemarkrawer = false;

        },
        //下单发货表单
        async getCity(parentCode, type,) {
            const res = await getCityAllData({ parentCode: parentCode });
            if (res?.success) {
                if (type == 1) {
                    this.receiverStateList = res?.data;
                }
                if (type == 2) {
                    this.receiverCityList = res?.data;
                }
                if (type == 3) {
                    this.receiverDistrictList = res?.data;
                }
            }
        },
        async receiverStateChange() {
            this.receiverCityList = [];
            this.receiverDistrictList = [];
            this.addOrderForm.receiverCityCode = "";
            this.addOrderForm.receiverDistrictCode = "";
            var parentCode = this.addOrderForm.receiverStateCode;
            if (parentCode) {
                this.getCity(parentCode, 2);
            }
        },
        async receiverCityChange() {
            this.receiverDistrictList = [];
            this.addOrderForm.receiverDistrictCode = "";
            var parentCode = this.addOrderForm.receiverCityCode;
            if (parentCode) {
                this.getCity(parentCode, 3);
            }
        },
        async getCity2(parentCode, type,) {
            const res = await getCityAllData({ parentCode: parentCode });
            if (res?.success) {
                if (type == 1) {
                    this.receiverStateList = res?.data;
                }
                if (type == 2) {
                    this.receiverCityList2 = res?.data;
                }
                if (type == 3) {
                    this.receiverDistrictList2 = res?.data;
                }
            }
        },
        async receiverStateChange2() {
            this.receiverCityList2 = [];
            this.receiverDistrictList2 = [];
            this.addAddressForm.receiverCityCode = "";
            this.addAddressForm.receiverDistrictCode = "";
            var parentCode = this.addAddressForm.receiverStateCode;
            if (parentCode) {
                this.getCity2(parentCode, 2);
            }
        },
        async receiverCityChange2() {
            this.receiverDistrictList2 = [];
            this.addAddressForm.receiverDistrictCode = "";
            var parentCode = this.addAddressForm.receiverCityCode;
            if (parentCode) {
                this.getCity2(parentCode, 3);
            }
        },
        //成品调出/加工调入
        async onAddOrder(typeId) {
            if (this.selids.length == 0&&typeId == 2) {
                this.$message({ type: 'warning', message: "请选择成品调出" });
                return;
            }
            if (this.chiselids.length == 0&&typeId == 1) {
                this.$message({ type: 'warning', message: "请选择半成品调入" });
                return;
            }
            const res = await getRecordUser();
            if (!res?.success) {
                return
            }
            this.userlist=res.data;
            this.typeId=typeId
            this.workshow=true;
            this.workShowTitle=this.typeId==2?"成品调出":"加工调入";
            this.dialogAddOrderVisible = true;
        },
        async saveworkshow(){
            var ids = this.selids.map(a => a.packagesProcessingId).join(",");
            let params = {
            packaesProcessingIds: ids,
            createPackId:this.filter.createPackId,
            quantity:this.filter.quantity,
            typeId: this.typeId
          }
          // return
          const res = this.typeId==1?await updateProcessBatch(params):await updateDispatcheBatch(params);
          if (!res?.success) {
              return
          }
          this.$message({
              message: '保存成功',
              type: 'success'
          })
          this.selids = [];
          this.workshow=false;
          await this.onRefresh();
        },
        //关闭下单发货界面
        async closeAddOrder() {
            this.dialogAddOrderSubmitLoding = false;
        },
        //下单发货：选择商品
        async onSelctOrderGoods() {
            this.orderGoodschoiceVisible = true;
            this.$refs.orderGoodschoice.removeSelData();
        },
        //同步附件中的sku商品
        async onSyncOrderGoods() {
            var res = await getTaskReferenceInfo({ shootingTaskIds: this.addOrderForm.shootingTaskIds });
            if (res?.success) {
                var temp = this.addOrderForm.orderGoods;
                var isNew = true;
                res?.data.forEach((item) => {
                    temp.forEach(old => {
                        if (old.goodsCode == item.goodsCode) {
                            isNew = false;
                        }
                    });
                    if (isNew) {
                        this.addOrderForm.orderGoods.push({
                            goodsCode: item.goodsCode,
                            goodsName: item.goodsName,
                            shopCode: null,
                            shopName: null,
                            proCode: null,
                            goodsPrice: item.costPrice ?? 0,
                            goodsQty: 1,
                            goodsAmount: item.costPrice ?? 0
                        });
                    }
                });
            }
        },
        //下单发货：选择商品确定
        async onQuerenOrderGoods() {
            var choicelist = await this.$refs.orderGoodschoice.getchoicelist();
            if (choicelist && choicelist.length > 0) {
                //已存在的不添加
                var temp = this.addOrderForm.orderGoods;
                var isNew = true;
                choicelist.forEach((item) => {
                    isNew = true;
                    temp.forEach(old => {
                        if (old.goodsCode == item.goodsCode) {
                            isNew = false;
                        }
                    });
                    if (isNew) {
                        this.addOrderForm.orderGoods.push({
                            goodsCode: item.goodsCode,
                            goodsName: item.goodsName,
                            shopCode: item.shopId,
                            shopName: item.shopName,
                            proCode: item.shopStyleCode,
                            goodsPrice: item.costPrice ?? 0,
                            goodsQty: 1,
                            goodsAmount: item.costPrice ?? 0
                        });
                    }
                });
                this.orderGoodschoiceVisible = false;
            }
        },
        //移除明细
        async onDelDtlGood(index) {
            this.addOrderForm.orderGoods.splice(index, 1);
        },
        async addOrderFormGoodsQtyChange(row) {
            row.goodsAmount = (row.goodsQty * (row.goodsPrice ?? 0)).toFixed(2);
        },
        //新增编辑提交时验证
        addOrderFormValidate: function () {
            let isValid = false
            this.$refs.addOrderForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        //下单发货-保存
        async onAddOrderSave() {
            if (this.addOrderForm.shootingTaskIds == "" || this.addOrderForm.orderGoods.length <= 0) {
                this.$message({ message: '下单发货信息不完整，请填写', type: "warning" });
                return;
            }
            if (this.addOrderForm.isZt == 1) {
                if (this.addOrderForm.warehouse == "" || this.addOrderForm.warehouse == null) {
                    this.$message({ message: '拿样方式=仓库自提时必须填写自提仓库', type: "warning" });
                    return;
                }
            }
            else {
                this.addOrderForm.warehouse = null;
            }
            if (this.addOrderForm.isZt == 0) {
                if (this.addOrderForm.receiverAddressAllInfo == "" || this.addOrderForm.receiverAddressAllInfo == null) {
                    this.$message({ message: '拿样方式=快递寄样时必须选择详细地址', type: "warning" });
                    return;
                }
                if (this.addOrderForm.receiverName == "" || this.addOrderForm.receiverPhone == "") {
                    this.$message({ message: '收货人信息错误，请刷新后重试', type: "warning" });
                    return;
                }
                if (this.addOrderForm.receiverAddress == "") {
                    this.$message({ message: '下单发货地址错误，请刷新后重试', type: "warning" });
                    return;
                }
            }
            else {
                this.addOrderForm.receiverName = "";
                this.addOrderForm.receiverPhone = "";
                this.addOrderForm.receiverState = "";
                this.addOrderForm.receiverStateCode = "";
                this.addOrderForm.receiverCity = "";
                this.addOrderForm.receiverCityCode = "";
                this.addOrderForm.receiverDistrict = "";
                this.addOrderForm.receiverDistrictCode = "";
                this.addOrderForm.receiverAddress = "";
                this.addOrderForm.receiverAddressAllInfo = "";
            }
            this.dialogAddOrderSubmitLoding = true;
            this.dialogAddOrderLoading = true;
            const res = await shootingTaskAddOrderSave(this.addOrderForm);
            this.dialogAddOrderLoading = false;
            if (res?.success) {
                if (this.addOrderForm.isZt == 1) {
                    this.$message({ message: '操作成功，请关注钉钉审批流程', type: "success" });
                }
                else {
                    this.$message({ message: '操作成功，请关注钉钉审批流程', type: "success" });
                }
                this.onSearch();
                this.dialogAddOrderVisible = false;
                this.dialogAddOrderSubmitLoding = false;
            }
            // else {
            //     if (res?.msg)
            //         this.$message({ message: res?.msg, type: "error" });
            //     else
            //         this.$message({ message: '操作失败，请刷新后重试', type: "error" });
            // }
        },
        async dialogOrderDtlColsed() {
            this.xdfhmainlist = [];
            this.xdfhdtllist = [];
        },
        async onShowOrderDtl(row) {
            this.dialogOrderDtlVisible = true;
            this.xdfhmainLoading = true;
            this.xdfhdtlLoading = true;
            var ret = await getShootingTaskOrderListById({ shootingTaskId: row.shootingTaskId });
            this.xdfhmainLoading = false;
            this.xdfhdtlLoading = false;
            if (ret?.success && ret.data.length > 0) {
                ret.data.forEach(f => f.shootingTaskId = row.shootingTaskId);
                this.xdfhmainlist = ret.data;
                this.xdfhdtllist = ret.data[0].dtlEntities;
            }
        },
        async onxdfhmainCellClick(row) {
            this.xdfhmainlist.forEach(
                f => {
                    if (f.shootingTaskOrderId == row.shootingTaskOrderId) {
                        this.xdfhdtllist = f.dtlEntities;
                    }
                }
            );
        },
        async receiverAddressSelChange() {
            this.addressList.forEach(f => {
                if (f.vedioTaskOrderAddressId == this.addOrderForm.receiverAddressAllInfo && this.addOrderForm.receiverAddressAllInfo != "") {
                    this.addOrderForm.receiverStateCode = f.receiverStateCode;
                    this.addOrderForm.receiverCityCode = f.receiverCityCode;
                    this.addOrderForm.receiverDistrictCode = f.receiverDistrictCode;
                    this.addOrderForm.receiverAddress = f.receiverAddress;
                    if (f.receiverName)
                        this.addOrderForm.receiverName = f.receiverName;
                    if (f.receiverPhone)
                        this.addOrderForm.receiverPhone = f.receiverPhone;

                    return;
                }
            });
            ;
        },
        async getAddressList() {
            this.addressList = [];
            this.addressListLoading = true;
            var ret = await getVedioTaskOrderAddressList();
            this.addressListLoading = false;
            this.addressList = ret?.data;
            this.receiverAddressList = [];
            if (ret?.success) {
                this.receiverAddressList = ret.data?.map(item => { return { value: item.vedioTaskOrderAddressId, label: item.receiverAllAddress }; });
            }
        },
        async onAddressSet() {
            this.dialogAddressVisible = true;
            this.getAddressList();
        },
        async onAddAddressSave() {
            this.dialogAddAddressSubmitLoding = true;
            var ret = await saveVedioTaskOrderAddress(this.addAddressForm);
            this.dialogAddAddressSubmitLoding = false;
            if (ret?.success) {
                this.addAddressForm = {
                    receiverName: "",
                    receiverPhone: "",
                    receiverStateCode: null,
                    receiverCityCode: null,
                    receiverDistrictCode: null,
                    receiverAddress: "",
                };
                this.getAddressList();
            }
        },
        async onAddressDelete(row) {
            this.$confirm("是否确定删除, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                this.addressListLoading = true;
                var ret = await deleteVedioTaskOrderAddress({ vedioTaskOrderAddressId: row.vedioTaskOrderAddressId });
                this.addressListLoading = false;
                this.getAddressList();
            });
        },
        async onShowExproessHttp(row) {
            this.drawervisible = true;
            let expressNo = row.expressNo;
            if (!expressNo) {
                expressNo = row.sampleExpressNo;
            }
            this.$nextTick(function () {
                this.$refs.logistics.showlogistics("", expressNo);
            })
        },
        showLogDetail(row) {
            this.dialogHisVisible = true;
            let sampleRrderNo = row.sampleRrderNo;
            if (!sampleRrderNo) {
                sampleRrderNo = row.orderNoInner;
            }
            this.sendOrderNoInner = sampleRrderNo;
        },
        async getbrandListList(index) { //14包装加工，15-品牌，16包装加工-机型，17包装加工-尺寸

            const res = await getShootingSetData({ setType: index });
            if (!res?.success) {
                return
            }
            switch (index) {
                case 14:
                    this.packingMaterialList = res?.data?.data;
                    break;
                case 15:
                    this.brandList = res?.data?.data;
                    break;
                case 16:
                    this.machineTypeList = res?.data?.data;
                    break;
                case 17:
                    this.packageSizeList = res?.data?.data;
                    break;
            }
            this.listLoading = false;
        },
    },
};
</script>
<style lang="scss" scoped>
::v-deep .el-drawer__header {
    border: none !important;
    padding: 16px 24px 0 24px;
}

::v-deep .el-header {
    padding: 10px 5px 5px 5px !important;
}

.ssanc {
    width: 100%;
    height: 38px;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
    display: flex;

}

.heardcss {
    width: 100%;
    min-width: 1150px;
    min-height: 35px;
    // background-color: aqua;
    box-sizing: border-box;
    display: inline-block;
    margin-top: 8px;
}


.heardcss span,
.ssanc span {
    padding: 4px 0.2% 4px 0;
}


::v-deep span .el-radio-button__inner {
    line-height: 14px !important;
}

::v-deep .vxetablecss {
    width: 100%;
    margin-top: -20px !important;
}

// ::v-deep .vxetoolbar20221212 {
//     top: 97px;
//     right: 15px;
// }

::v-deep .el-button-group>.el-button:last-child {
    margin-left: -2px !important;
}

::v-deep .el-button-group {
    margin: 0 !important;
    padding: 0 !important;
}

// ::v-deep .vxe-table--render-default .vxe-cell {
//     padding:0 5px !important;
// }
::v-deep .vxe-header--row {
    height: 58px;
}

::v-deep .el-table__body-wrapper {
    height: 220px !important;
}

::v-deep .el-table__body-wrapper {
    overflow-y: auto;
}


.gddwz {
    width: 100%;
    /* background-color: #F2F6FC; */
    //   border: 1px solid #ff1414;
    overflow: hidden;
    white-space: nowrap;
    box-sizing: border-box;
    text-align: center;
}

.gddwznr {
    padding-left: 20px;
    font-size: 14px;
    color: #eb0000;
    display: inline-block;
    white-space: nowrap;
    animation: 20s wordsLoop linear infinite normal;
    margin: 0;
}

@keyframes wordsLoop {
    0% {
        transform: translateX(100%);
        -webkit-transform: translateX(100%);
    }

    100% {
        transform: translateX(-100%);
        -webkit-transform: translateX(-100%);
    }
}

@-webkit-keyframes wordsLoop {
    0% {
        transform: translateX(100%);
        -webkit-transform: translateX(100%);
    }

    100% {
        transform: translateX(-100%);
        -webkit-transform: translateX(-100%);
    }
}
// 弹窗边距
::v-deep .el-dialog__body {
    padding:0 !important;
}
::v-deep .el-dialog__header {
    padding:0 !important;
}

::v-deep .mycontainer {
    padding:0 !important;
}.flexrow {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.qualibtn ::v-deep .el-button {
  margin-left: 0 !important;
  margin-top: 10px;
}

.marginrt {
  margin: 0 10px 0 auto;
}

::v-deep .el-dialog__title{
    color: #ffffff !important;
}
</style>

