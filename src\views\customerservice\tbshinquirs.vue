<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='inquirslist' @select='selectchange' :isSelection='false' :tableCols='tableCols'
            :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.shopCode" placeholder="店铺" style="width:160px;" filterable clearable>
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                :value="item.shopCode">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model="filter.snick" placeholder="旺旺" style="width:160px;" clearable :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <datepicker v-model="filter.sdate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date">
                        </datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onImportSyj">导入</el-button>
                    <el-button type="primary" @click="onImportSyjModel">下载模板</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getinquirsList" />
        </template>
        <el-dialog title="客服人员咨询数据" :visible.sync="dialogVisibleSyj" width="40%" :close-on-click-modal="false" v-dialogDrag>
            <el-form ref="improtGroupForm" :model="improtGroupForm" label-width="55px" label-position="left">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                        <el-form-item label="日期" prop="improtGroupDate"
                            :rules="[{ required: true, message: '请输入', trigger: 'blur' }]">
                            <el-date-picker v-model="improtGroupForm.improtGroupDate" format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd" type="date" style="width:160px" clearable>
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item label="店铺" prop="improtGroupShopCode"
                            :rules="[{ required: true, message: '请输入', trigger: 'blur' }]">
                            <el-select v-model="improtGroupForm.improtGroupShopCode" placeholder="请选择" style="width:270px;"
                                filterable clearable @change="onImprotGroupShopChange">
                                <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                    :value="item.shopCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2"
                            :on-change="onUploadChange2" :on-remove="onUploadRemove2">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                                @click="onSubmitupload2">上传</my-confirm-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
    getTaoBaoShouHouInquirsList, deleteTaoBaoShouHouInquirsAsync, importTaoBaoShouHouInquirsAsync
} from '@/api/customerservice/taobaoshouhou'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
const tableCols = [
    //{ istrue: false, prop: 'id', label: 'id', width: '160', sortable: 'custom', display: false, },
    { istrue: true, prop: 'shopName', label: '店铺', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'snick', label: '旺旺', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'inquirs', label: '咨询人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'receiveds', label: '接待人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'responseTime', label: '平均响应(秒)', width: '110', sortable: 'custom' },
    { istrue: true, prop: 'salesvol', label: '销售额', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'satisfaction', label: '客户满意率', width: '100', sortable: 'custom', formatter: (row) => (row.satisfaction * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'lateReceiveds', label: '慢接待人数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'responseRate', label: '回复率', width: '80', sortable: 'custom', formatter: (row) => (row.responseRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'verySatisfied', label: '很满意', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'threeResponseRate', label: '平台3分钟响应率', width: '130', sortable: 'custom', formatter: (row) => (row.threeResponseRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'satisDegree', label: '满意度', width: '100', sortable: 'custom',formatter:(row) => row.satisDegree !== null  ? (row.satisDegree).toFixed(2) +'%' : '0%' },
    { istrue: true, prop: 'sdate', label: '日期', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.sdate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'createdTime', label: '导入时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'batchNumber', label: '导入批次', width: '170', sortable: 'custom' },
    {
        istrue: true, type: "button", label: '操作', width: "110",
        btnList: [
            { label: "删除", handle: (that, row) => that.deleteBatch(row, 0) },
            { label: "删除批次", handle: (that, row) => that.deleteBatch(row, 1) }
        ]
    }
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
    data() {
        return {
            that: this,
            filter: {
            },
            shopList: [],
            userList: [],
            inquirslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "sdate", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            improtGroupForm: {
                improtGroupShopCode: null,
                improtGroupShopName: null,
            },
        };
    },
    async mounted() {
        await this.getAllShopList();
    },
    methods: {
        async getAllShopList() {
            let shops = await getAllShopList();
            this.shopList = [];
            shops.data?.forEach(f => {
                if (f.isCalcSettlement && f.shopCode && (f.platform == 1 || f.platform == 4 || f.platform == 9))
                    this.shopList.push(f);
            });
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getinquirsList();
        },
        async getinquirsList() {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            console.log(params)
            this.listLoading = true;
            const res = await getTaoBaoShouHouInquirsList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.inquirslist = res.data.list;
            //this.summaryarry=res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async deleteBatch(row, type) {
            var that = this;
            this.$confirm("确认要执行删除" + (type == 1 ? "批次" : "") + "的操作吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let del = await deleteTaoBaoShouHouInquirsAsync({ idOrBatchNum: (type == 1 ? row.batchNumber : row.id), type: type });
                if (del?.success) {
                    that.$message({ message: '已删除', type: "success" });
                    that.onSearch();
                }
                else {
                    that.$message({ message: '发生异常，请刷新后重试', type: "error" });
                }
            });
        },
        onImportSyjModel() {
            window.open("/static/excel/customerservice/淘系售后咨询数据导入模板.xlsx", "_blank");
        },
        async onImportSyj() {
            this.dialogVisibleSyj = true;
        },
        async onUploadChange2(file, fileList) {
            this.fileList = fileList;
        },
        async onUploadRemove2(file, fileList) {
            this.fileList = [];
        },
        async uploadFile2(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("shopCode", this.improtGroupForm.improtGroupShopCode);
            form.append("shopName", this.improtGroupForm.improtGroupShopName);
            const res = await importTaoBaoShouHouInquirsAsync(form);
            if (res?.success) {
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
                this.dialogVisibleSyj = false;
            }
            // else {
            //     this.$message({ message: '发生异常，请刷新后重试', type: "error" });
            // }
        },
        async uploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        async onSubmitupload2() {
            if (this.improtGroupForm.improtGroupShopCode == "" || this.improtGroupForm.improtGroupShopCode == null ||
                this.improtGroupForm.improtGroupShopName == "" || this.improtGroupForm.improtGroupShopName == null) {
                this.$message({ message: '请输入店铺', type: "error" });
                return;
            }
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload2.submit();
        },
        async onImprotGroupShopChange(value) {
            this.improtGroupForm.improtGroupShopName == ""
            if (this.shopList && this.shopList.length > 0) {
                for (let i = 0; i < this.shopList.length; i++) {
                    if (this.shopList[i].shopCode == value) {
                        this.improtGroupForm.improtGroupShopName = this.shopList[i].shopName;
                        return;
                    }
                }
            }
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
