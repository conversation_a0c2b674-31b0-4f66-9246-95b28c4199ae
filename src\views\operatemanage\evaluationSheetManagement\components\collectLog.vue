<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss" />
                <el-input v-model.trim="ListInfo.proCode" placeholder="ID" maxlength="50" clearable class="publicCss" />
                <el-select v-model="ListInfo.createdUserId" placeholder="操作人" class="publicCss" clearable filterable
                    reserve-keyword :remote-method="remoteMethod1" remote>
                    <el-option v-for="item in operateUserList" :label="item.createdUserName" :value="item.createdUserId"
                        :key="item.createdUserId" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0;height: 500px;" :loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { GetEvaluationFormManageLogPage, getLogOperateUserList } from '@/api/operatemanage/EvaluationFormManage'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'proCode', label: 'ID', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '操作人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'operateType', label: '类型', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'updateBefore', label: '操作前', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'updateAfter', label: '操作后', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            operateUserList: []
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async remoteMethod1(query) {
            if (query) {
                const { data } = await getLogOperateUserList({ operateName: query })
                this.$set(this, 'operateUserList', data)
            } else {
                this.$set(this, 'operateUserList', [])
            }
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetEvaluationFormManageLogPage(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
