<template>
    <MyContainer>
        <div class="top">
            <span>订单量小于等于</span>
            <el-input-number v-model="queryInfo.minMobilizeCount" class="publicCss" :max="9999999" :controls="false"
                placeholder="订单量" :precision="0" />
            <span>不参与蓄单</span>
        </div>
        <el-button type="text" @click="addProps">新增一行</el-button>
        <el-table :data="queryInfo.data" style="width: 100%" max-height="250">
            <el-table-column prop="date" label="最小订单量">
                <template #default="{ row }">
                    <el-input-number v-model="row.minOrderCount" class="publicCss" :max="9999999" :controls="false"
                        placeholder="最小订单量" :precision="0" />
                </template>
            </el-table-column>
            <el-table-column prop="name" label="最大订单量">
                <template #default="{ row }">
                    <el-input-number v-model="row.maxOrderCount" class="publicCss" :max="9999999" :controls="false"
                        placeholder="最大订单量" :precision="0" />
                </template>
            </el-table-column>
            <el-table-column prop="address" label="蓄单时间">
                <template #default="{ row }">
                    <el-input-number v-model="row.hour" class="publicCss" :max="9999999" :controls="false"
                        placeholder="蓄单时间" :precision="0" />
                </template>
            </el-table-column>
            <el-table-column prop="address" label="蓄单量">
                <template #default="{ row }">
                    <el-input-number v-model="row.orderCount" class="publicCss" :max="9999999" :controls="false"
                        placeholder="蓄单量" :precision="0" />
                </template> 
            </el-table-column>
            <el-table-column prop="address" label="删除">
                <template #default="{ row, $index }">
                    <el-button type="danger" @click="delProps($index)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="btnGroup">
            <el-button @click="closeDialog">取消</el-button>
            <el-button type="primary" @click="submit" v-throttle="2000">确认</el-button>
        </div>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { getMobilize, setMobilize } from '@/api/vo/VerifyOrder'
export default {
    components: { MyContainer },
    data() {
        return {
            queryInfo: {
                data: [],
            },
            tableData: []
        }
    },
    async mounted() {
        await this.getProps()
    },
    methods: {
        delProps(i) {
            this.$confirm('此操作将删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.queryInfo.data.splice(i, 1)
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        async getProps() {
            this.queryInfo = {
                data: []
            }
            const { data, success } = await getMobilize()
            if (success) {
                this.queryInfo.data = data.data
                this.queryInfo.minMobilizeCount = data.minMobilizeCount
            }
        },
        closeDialog() {
            this.$emit('close')
        },
        addProps() {
            this.queryInfo.data.push({})
        },
        async submit() {
            if (this.queryInfo.minMobilizeCount < 0) return this.$message.error('订单量不能小于0')
            if (this.queryInfo.minMobilizeCount === '' || this.queryInfo.minMobilizeCount === null || this.queryInfo.minMobilizeCount === undefined) return this.$message.error('订单量不能为空')
            if (this.queryInfo.data.length === 0) return this.$message.error('至少添加一条数据')
            if (this.queryInfo.data.length > 0) {
                this.queryInfo.data.forEach((item, i) => {
                    if (item.maxOrderCount <= 0) {
                        this.$message.error(`第${i + 1}条数据最大订单量必须大于0`)
                        throw new Error(`第${i + 1}条数据最大订单量必须大于0`)
                    }
                    if (item.maxOrderCount < item.minOrderCount) {
                        this.$message.error(`第${i + 1}条数据最大订单量小于最小订单量`)
                        throw new Error(`第${i + 1}条数据最大订单量小于最小订单量`)
                    }
                    for (let key in item) {
                        if (key !== 'id') {
                            if (item[key] === '' || item[key] === null || item[key] === undefined) {
                                this.$message.error(`第${i + 1}条数据不完整`)
                                throw new Error(`第${i + 1}条数据不完整`)
                            }
                        }
                    }
                })
            }
            if(this.queryInfo.data.length > 1){
                //取出最后一条数据的最小订单量
                let min = this.queryInfo.data[this.queryInfo.data.length - 1].minOrderCount
                let max = this.queryInfo.data[this.queryInfo.data.length - 1].maxOrderCount
                this.queryInfo.data.forEach((item, i) => {
                    if (i < this.queryInfo.data.length - 1) {
                        if (min <= item.maxOrderCount && max >= item.minOrderCount) {
                            this.$message.error(`新增的数据与原数据有交叉`)
                            throw new Error(`新增的数据与原数据有交叉`)
                        }
                    }
                })
            }
            const { data, success } = await setMobilize(this.queryInfo)
            if (success) {
                localStorage.setItem('mobilize', JSON.stringify(this.queryInfo.minMobilizeCount))
                this.$emit('close')
                this.$emit('getList', true)
                this.$message.success('设置成功')
            }
        }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;

    .publicCss {
        width: 100px;
        margin: 0 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    button {
        width: 100px;
    }
}
</style>