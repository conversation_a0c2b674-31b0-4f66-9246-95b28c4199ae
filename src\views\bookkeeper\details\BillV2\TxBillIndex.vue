<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="淘系账单" name="first1" style="height: 100%">
        <TXBill ref="refTXBill" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="淘系微信账单" name="first2" style="height: 100%" lazy>
        <TXBillWX ref="refTXBillWX" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="淘工厂账单" name="first3" style="height: 100%" lazy>
        <TGCBill ref="refTGCBill" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="淘工厂微信账单" name="first4" style="height: 100%" lazy>
        <TGCBillWX ref="refTGCBillWX" style="height: 100%" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import TXBill from "./TXBill.vue";
import TXBillWX from "./TXBillWX.vue";
import TGCBill from "./TGCBill.vue";
import TGCBillWX from "./TGCBillWX.vue";
export default {
  name: "videoBillIndex",
  components: {
    MyContainer, TXBill, TXBillWX, TGCBill,TGCBillWX
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },
};
</script>

<style lang="scss" scoped></style>
