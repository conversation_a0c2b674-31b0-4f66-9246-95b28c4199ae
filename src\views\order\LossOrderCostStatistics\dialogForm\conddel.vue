<template>
 <MyContainer>
   <template #header>
    <el-row>
      <el-date-picker  class="marginleft"  style="width: 220px; " v-model="filter.AfterSaleApproveDate" type="daterange" format="yyyy-MM-dd"
        value-format="yyyy-MM-dd" range-separator="至" :picker-options="pickerOptions"  @change="changebtn"
        :start-placeholder="'售后发起时间'"
        :end-placeholder="'售后结束时间'">
      </el-date-picker>


      <el-input class="marginleft" @change="changebtn" v-model="filter.orderNo" placeholder="线上订单号" clearable style="width: 180px"  maxlength="50"></el-input>


      <el-select class="marginleft"  v-model="filter.platform"  clearable placeholder="平台" @change="onchangeplatform" style="width: 180px"  >
        <el-option v-for="item in platformList" :key="item.label" :label="item.label" :value="item.value" />
      </el-select>

      <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺" style="width: 200px" @change="zrType2change()">
          <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"></el-option>
      </el-select>


      <el-select class="marginleft" v-model="filter.sendWareHouseId"   @change="changebtn"  clearable placeholder="发货仓" style="width: 160px; margin-left: 10px;"  >
        <el-option v-for="item in newWareHouseList" :key="item.name" :label="item.name" :value="item.wms_co_id" />
      </el-select>



      <el-input class="marginleft" v-model="filter.expressOrder" clearable placeholder="快递单号"  @change="changebtn" style="width: 180px" maxlength="50"></el-input>

      <el-input class="marginleft" v-model="filter.goodsCode" clearable placeholder="商品编码"  @change="changebtn" style="width: 180px" maxlength="50"></el-input>


      <el-select class="marginleft" v-model="filter.zrDepartment"    clearable placeholder="责任部门(大类)" style="width: 200px;" @change="getZrType(filter.zrDepartment)"  >
        <el-option v-for="item in damagedList" :key="item" :label="item" :value="item"  />
      </el-select>

      <el-select class="marginleft" v-model="filter.zrType2"    clearable placeholder="责任类型(细类)" style="width: 200px;" @change="zrType2change()" >
        <el-option v-for="item in damagedList2" :key="item" :label="item" :value="item" />
      </el-select>


      <el-input class="marginleft" v-model="filter.ZrUserName" clearable placeholder="责任人"  @change="changebtn" style="width: 160px" maxlength="50"></el-input>

      <el-input class="marginleft" v-model="filter.exportUserName" clearable placeholder="数据上传人"  @change="changebtn" style="width: 160px" maxlength="50"></el-input>

      <el-input class="marginleft" type="number" v-model="filter.batchNumber" clearable placeholder="批次号" style="width: 180px"
        maxlength="20" @blur="filter.batchNumber = filter.batchNumber.slice(0,19)"></el-input>
      <!-- <el-input-number v-model="filter.batchNumber" placeholder="批次号" type="number"
      clearable :controls="false" :precision="0" :max="99999999999999999999" :min="0"  style="width: 180px"
       /> -->

      <el-date-picker  class="marginleft"  style="width: 350px; " v-model="filter.commencementDate" type="datetimerange" format="yyyy-MM-dd HH:mm:ss"
        value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" :picker-options="pickerOptions"  @change="changebtn"
        :start-placeholder="'责任开始时间'"
        :end-placeholder="'责任结束时间'">
      </el-date-picker>



      <el-button class="marginleft"  type="primary" @click="onSearch">查询</el-button>



   </el-row>
   </template>

   <div>{{msgtitle}}</div>
 </MyContainer>
</template>

<script>
 import MyContainer from "@/components/my-container";
 import { formatTime, formatPlatform, pickerOptions } from "@/utils/tools";
 import { getAllListInCalc as getAllShopList,   getList as getshopListt } from '@/api/operatemanage/base/shop';
 import { getAllWarehouse } from '@/api/inventory/warehouse'
 import { getDamagedOrdersZrDept, getDamagedOrdersZrType, deleteDamagedOrderBefore  } from '@/api/customerservice/DamagedOrders'
export default {
 name: 'Vue2demoConddel',
 props: ['ids'],
 components: { MyContainer},
 data() {
  return {
   filter: {
    ZrSetDate: [],
    AfterSaleApproveDate: [],
    commencementDate: [],
    startZrSetDate: null,
    endZrSetDate: null,
    SendGoodsDate: [],
    zrType2: '',
    batchNumber: ''
   },
   msgtitle: '',
   pickerOptions: pickerOptions,
   shopList: [],
   newWareHouseList: [],
   damagedList: [],
   damagedList2: [],
   platformList: [{label: '天猫',value: 1},{label: '拼多多',value: 2},{label: '阿里巴巴',value: 4},
   {label: '抖音',value: 6},{label: '京东',value: 7},{label: '淘工厂',value: 8},{label: '淘宝',value: 9},{label: '苏宁',value: 10},],
  };
 },

 async mounted() {
  this.getZrDept();
  // this.getShopList();

 },

 methods: {
  zrType2change(){
    this.$forceUpdate();
    this.changebtn();
  },
  async onSearch(){
    this.filter.startZrSetDate = null;
    this.filter.endZrSetDate = null;
    if (this.filter.commencementDate && this.filter.commencementDate.length > 1) {
        this.filter.startZrSetDate = this.filter.commencementDate[0];
        this.filter.endZrSetDate = this.filter.commencementDate[1];
    }
   let params = {
      ids: this.ids,
      ...this.filter
    }
    let res = await deleteDamagedOrderBefore(params)
    if(!res.success){
      return
    }
    this.$emit('issearch', {...this.filter,ids: null});
    this.msgtitle = res.data.meg;
  },
  changebtn(){
    this.$emit('disabledfuc');
  },
  async onchangeplatform(val) {
      this.filter.shopCode=null;
      const res1 = await getshopListt({ platform: val, CurrentPage: 1, PageSize: 100000 });
      this.shopList = res1.data.list
      this.changebtn();

  },
  async getZrDept(){
    let res = await getDamagedOrdersZrDept();
    this.damagedList =  res?.data;
    console.log("打印数据",this.damagedList)

    var res1 = await getAllWarehouse();
    this.newWareHouseList = res1.data.filter((x) => { return x.name.indexOf('代发') < 0; });
    // damagedList2
  },
  async getZrType(name){
    let res = await getDamagedOrdersZrType(name);
    this.damagedList2 =  res.data;
    this.filter.zrType2=""
    console.log("打印数据",this.damagedList)
    this.changebtn();
  },
 },
};
</script>

<style lang="scss" scoped>

</style>
