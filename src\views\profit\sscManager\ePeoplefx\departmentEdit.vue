<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" ref="refruleForm" label-width="140px" class="demo-ruleForm">
        <el-form-item label="类型：">
          <el-input v-model.trim="ruleForm.type" placeholder="类型" maxlength="50" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="区域：">
          <el-input v-model.trim="ruleForm.regionName" placeholder="区域" maxlength="50" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="部门类型：">
          <el-input v-model.trim="ruleForm.deptType" placeholder="部门类型" maxlength="50" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="部门：">
          <el-input v-model.trim="ruleForm.deptName" placeholder="部门" maxlength="50" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="人数：">
          <inputNumberYh v-model="ruleForm.personCount" :placeholder="'请输入'" class="publicCss" />
        </el-form-item>
        <div style="font-size: 15px; font-weight: 600; margin-left: 20px;">工龄</div>

        <div style="dispaly: flex; padding-left: 65px">
            <el-form :model="ruleForm" :inline="true" ref="refruleForm" class="demo-ruleForm">
                <el-form-item label="0~3个月以内：">
                    <div style="width: 100px;">
                        <inputNumberYh v-model="ruleForm.personCount03" :placeholder="'请输入'" />
                    </div>
                </el-form-item>
                <el-form-item label="3~6个月以内：">
                    <div style="width: 100px;">
                        <inputNumberYh v-model="ruleForm.personCount36" :placeholder="'请输入'" />
                    </div>
                </el-form-item>
                <el-form-item label="6~12个月以内：">
                    <div style="width: 100px;">
                        <inputNumberYh v-model="ruleForm.personCount012" :placeholder="'请输入'" />
                    </div>
                </el-form-item>
                <el-form-item label="1~2年以内：">
                    <div style="width: 100px;">
                        <inputNumberYh v-model="ruleForm.personCount13" :placeholder="'请输入'" />
                    </div>
                </el-form-item>
                <el-form-item label="2年以上：">
                    <div style="width: 100px;">
                        <inputNumberYh v-model="ruleForm.personCount3" :placeholder="'请输入'" />
                    </div>
                </el-form-item>
            </el-form>
        </div>

        <div style="font-size: 15px; font-weight: 600; margin-left: 20px;">年龄</div>
        <div style="dispaly: flex; padding-left: 55px">
            <el-form :model="ruleForm" :inline="true" ref="refruleForm" class="demo-ruleForm">
                <el-form-item label="18~22岁：">
                    <div style="width: 120px;">
                        <inputNumberYh v-model="ruleForm.ageCount1822" :placeholder="'请输入'" />
                    </div>
                </el-form-item>
                <el-form-item label="23~27岁：">
                    <div style="width: 120px;">
                        <inputNumberYh v-model="ruleForm.ageCount2328" :placeholder="'请输入'" />
                    </div>
                </el-form-item>
            </el-form>
            <el-form :model="ruleForm" :inline="true" ref="refruleForm" class="demo-ruleForm">
                <el-form-item label="28~35岁：">
                    <div style="width: 120px;">
                        <inputNumberYh v-model="ruleForm.ageCount2835" :placeholder="'请输入'" />
                    </div>
                </el-form-item>
                <el-form-item label="36岁以上：">
                    <div style="width: 120px;">
                        <inputNumberYh v-model="ruleForm.ageCount35" :placeholder="'请输入'" />
                    </div>
                </el-form-item>
            </el-form>
        </div>

        <div style="font-size: 15px; font-weight: 600; margin-left: 20px;">性别</div>
        <div style="dispaly: flex; padding-left: 85px">
            <el-form :model="ruleForm" :inline="true" ref="refruleForm" class="demo-ruleForm">
                <el-form-item label="女性：">
                    <div style="width: 120px;">
                        <inputNumberYh v-model="ruleForm.femaleCount" :placeholder="'请输入'" />
                    </div>
                </el-form-item>
                <el-form-item label="男性：">
                    <div style="width: 120px;">
                        <inputNumberYh v-model="ruleForm.maleCount" :placeholder="'请输入'" />
                    </div>
                </el-form-item>
            </el-form>
        </div>

        <div style="font-size: 15px; font-weight: 600; margin-left: 20px;">学历</div>
        <div style="dispaly: flex; padding-left: 65px">
            <el-form :model="ruleForm" :inline="true" ref="refruleForm" class="demo-ruleForm">
                <el-form-item label="高中及以下：">
                    <div style="width: 100px;">
                        <inputNumberYh v-model="ruleForm.middleSchoolCount" :placeholder="'请输入'" />
                    </div>
                </el-form-item>
                <el-form-item label="中专/高中：">
                    <div style="width: 100px;">
                        <inputNumberYh v-model="ruleForm.highSchoolCount" :placeholder="'请输入'" />
                    </div>
                </el-form-item>
                <el-form-item label="专科：">
                    <div style="width: 100px;">
                        <inputNumberYh v-model="ruleForm.vocationalEducationCount" :placeholder="'请输入'" />
                    </div>
                </el-form-item>
                <el-form-item label="本科及以上：">
                    <div style="width: 100px;">
                        <inputNumberYh v-model="ruleForm.undergraduateCount" :placeholder="'请输入'" />
                    </div>
                </el-form-item>
            </el-form>
        </div>

        <!-- <el-form-item label="期初人数：">
          <inputNumberYh v-model="ruleForm.initialCount" :placeholder="'期初人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="入职人数：">
          <inputNumberYh v-model="ruleForm.newHiresCount" :placeholder="'入职人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="试用期离职人数：">
          <inputNumberYh v-model="ruleForm.newPeopleResignCount" :placeholder="'试用期离职人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="正式离职人数：">
          <inputNumberYh v-model="ruleForm.oldPeopleResignCount" :placeholder="'正式离职人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="调入人数：">
          <inputNumberYh v-model="ruleForm.transferCount" :placeholder="'调入人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="调出人数：">
          <inputNumberYh v-model="ruleForm.outCount" :placeholder="'调出人数'" class="publicCss" />
        </el-form-item> -->
      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
// import { departmentDimensionSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
import decimal from '@/utils/decimal'

import {
    personAnalysisPage, personAnalysisSubmit,
  } from '@/api/people/peoplessc.js';

export default {
  name: 'departmentEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      selectProfitrates: [],
      ruleForm: {
        regionName: '',// 区域
        deptName: '',// 部门
        recruitDemandCount: '',// 招聘需求人数
        initialCount: '',// 期初人数
        newHiresCount: '',// 入职人数
        newPeopleResignCount: '',// 试用期离职人数
        oldPeopleResignCount: '',// 正式离职人数
        transferCount: '',// 调入人数
        outCount: '',// 调出人数
      },
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };
  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod', 2);
    },

    // 校验各分组数据的和是否等于总人数
    validateGroupSums() {
      const totalCount = this.ruleForm.personCount || 0;
      const errors = [];

      // 工龄分组校验（5项）
      const workAgeSum = decimal(
        decimal(
          decimal(this.ruleForm.personCount03 || 0, this.ruleForm.personCount36 || 0, 2, '+'),
          this.ruleForm.personCount012 || 0,
          2, '+'
        ),
        decimal(this.ruleForm.personCount13 || 0, this.ruleForm.personCount3 || 0, 2, '+'),
        2, '+'
      );
      if (workAgeSum !== totalCount) {
        errors.push(`工龄分组数据不匹配：各项合计为${workAgeSum}人，应为${totalCount}人`);
      }

      // 年龄分组校验（4项）
      const ageSum = decimal(
        decimal(this.ruleForm.ageCount1822 || 0, this.ruleForm.ageCount2328 || 0, 2, '+'),
        decimal(this.ruleForm.ageCount2835 || 0, this.ruleForm.ageCount35 || 0, 2, '+'),
        2, '+'
      );
      if (ageSum !== totalCount) {
        errors.push(`年龄分组数据不匹配：各项合计为${ageSum}人，应为${totalCount}人`);
      }

      // 性别分组校验（2项）
      const genderSum = decimal(this.ruleForm.femaleCount || 0, this.ruleForm.maleCount || 0, 2, '+');
      if (genderSum !== totalCount) {
        errors.push(`性别分组数据不匹配：各项合计为${genderSum}人，应为${totalCount}人`);
      }

      // 学历分组校验（4项）
      const educationSum = decimal(
        decimal(this.ruleForm.middleSchoolCount || 0, this.ruleForm.highSchoolCount || 0, 2, '+'),
        decimal(this.ruleForm.vocationalEducationCount || 0, this.ruleForm.undergraduateCount || 0, 2, '+'),
        2, '+'
      );
      if (educationSum !== totalCount) {
        errors.push(`学历分组数据不匹配：各项合计为${educationSum}人，应为${totalCount}人`);
      }

      return errors;
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          // 校验各分组数据的和
          const validationErrors = this.validateGroupSums();
          if (validationErrors.length > 0) {
            this.$message.error(validationErrors.join('；'));
            return;
          }

          this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing");
          const { data, success } = await personAnalysisSubmit(this.ruleForm);
          if (!success) {
            return;
          }
          this.$emit('cancellationMethod', 1);
          this.resetForm(formName);
        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 60%;
}
</style>
