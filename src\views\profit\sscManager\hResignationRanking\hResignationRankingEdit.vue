<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" ref="refruleForm" :rules="rules" label-width="140px" class="demo-ruleForm">
        <el-form-item label="人数：" prop="peopleNumber">
          <inputNumberYh v-model="ruleForm.peopleNumber" :placeholder="'人数'" class="publicCss" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from '@/components/Comm/inputNumberYh.vue';
import MyConfirmButton from '@/components/my-confirm-button';
import { rankingOfResignationPositionsInWarehousingSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission';

export default {
  name: 'hResignationRankingEdit',
  components: {
    inputNumberYh,
    MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      ruleForm: {
        peopleNumber: null
      },
      rules: {
        peopleNumber: [
          { required: true, message: '请输入人数', trigger: 'blur' }
        ]
      }
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    // 只保留人数字段
    this.ruleForm = {
      ...this.editInfo,
      peopleNumber: this.editInfo.peopleNumber || null
    };
  },

  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod', 2);
    },

    async submitForm(formName) {
      try {
        const valid = await this.$refs[formName].validate();
        if (valid) {
          const submitData = {
            ...this.editInfo, // 保留原有的其他字段（如id等）
            peopleNumber: this.ruleForm.peopleNumber,
            isArchive: checkPermission("ArchiveStatusEditing")
          };

          const { success } = await rankingOfResignationPositionsInWarehousingSubmit(submitData);
          if (success) {
            this.$message.success('保存成功');
            this.$emit('cancellationMethod', 1);
            this.resetForm(formName);
          } else {
            this.$message.error('保存失败');
          }
        }
      } catch (error) {
        this.$message.error('保存失败');
      }
    },

    resetForm(formName) {
      this.$refs[formName].resetFields();
    }
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
