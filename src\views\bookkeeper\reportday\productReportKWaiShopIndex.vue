<template>
  <my-container style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="快手日报" name="first1" style="height: 100%">
        <productReportKWaiShop ref="productReportKWaiShop" style="height: 100%"></productReportKWaiShop>
      </el-tab-pane>
      <el-tab-pane label="订单日报" name="first2" :lazy="true" style="height: 100%"
        v-if="checkPermission('KWaiShopOrderDayReport')">
        <KWaiShopOrderDayReport @ChangeActiveName2="ChangeActiveName2" ref="KWaiShopOrderDayReport"
          style="height: 100%">
        </KWaiShopOrderDayReport>
      </el-tab-pane>
      <el-tab-pane label="编码日报" name="first3" :lazy="true" style="height: 100%"
        v-if="checkPermission('KWaiShopGoodCodeDayReport')">
        <KWaiShopGoodCodeDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="KWaiShopGoodCodeDayReport" style="height: 100%"></KWaiShopGoodCodeDayReport>
      </el-tab-pane>
      <el-tab-pane label="ID日报" name="first4" :lazy="true" style="height: 100%"
        v-if="checkPermission('KWaiShopIdDayReport')">
        <KWaiShopIdDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="KWaiShopIdDayReport" style="height: 100%"></KWaiShopIdDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺日报" name="first5" :lazy="true" style="height: 100%"
        v-if="checkPermission('KWaiShopShopDayReport')">
        <KWaiShopShopDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="KWaiShopShopDayReport" style="height: 100%"></KWaiShopShopDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺SKU日报" name="first7" :lazy="true" style="height: 100%"
        v-if="checkPermission('KWaiShopCommodityDayReport')">
        <KWaiShopCommodityDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="KWaiShopCommodityDayReport" style="height: 100%"></KWaiShopCommodityDayReport>
      </el-tab-pane>
      <el-tab-pane label="明细日报" name="first6" :lazy="true" style="height: 100%"
        v-if="checkPermission('KWaiShopDetailDayReport')">
        <KWaiShopDetailDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="KWaiShopDetailDayReport" style="height: 100%"></KWaiShopDetailDayReport>
      </el-tab-pane>
      <el-tab-pane label="出仓负利润ID订单明细" name="first8" :lazy="true" style="height: 100%"
        v-if="checkPermission('KWaiShopOutgoingprofitIDorderdetail')">
        <KWaiShopOutgoingprofitIDorderdetail @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="KWaiShopOutgoingprofitIDorderdetail" style="height: 100%"></KWaiShopOutgoingprofitIDorderdetail>
      </el-tab-pane>
      <el-tab-pane label="商务日报" name="first11" :lazy="true" style="height: 100%">
        <KWaiShopBusinessDaily ref="KWaiShopBusinessDaily" style="height: 100%"></KWaiShopBusinessDaily>
      </el-tab-pane>
      <el-tab-pane label="商务达人" name="first12" :lazy="true" style="height: 100%">
        <KWaiShopBusinessExpert ref="KWaiShopBusinessExpert" style="height: 100%">
        </KWaiShopBusinessExpert>
      </el-tab-pane>
      <el-tab-pane label="SKUS日报" name="first9" :lazy="true" style="height: 100%"
        v-if="checkPermission('KWaiShopOrderDayReport')">
        <KWaiShopSkusDayReport @ChangeActiveName2="ChangeActiveName2" ref="KWaiShopSkusDayReport" style="height: 100%">
        </KWaiShopSkusDayReport>
      </el-tab-pane>
      <el-tab-pane label="达人佣金预估" name="first10" :lazy="true" style="height: 100%">
        <KWaiShopCommissionEstimate ref="KWaiShopCommissionEstimate" style="height: 100%" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import productReportKWaiShop from "./productReportKWaiShop.vue";
import KWaiShopOrderDayReport from "./KWaiShopOrderDayReport.vue";
import KWaiShopSkusDayReport from "./KWaiShopSkusDayReport.vue";
import KWaiShopGoodCodeDayReport from "./KWaiShopGoodCodeDayReport.vue";
import KWaiShopIdDayReport from "./KWaiShopIdDayReport.vue";
import KWaiShopShopDayReport from "./KWaiShopShopDayReport.vue";
import KWaiShopCommodityDayReport from "./KWaiShopCommodityDayReport.vue";
import KWaiShopOutgoingprofitIDorderdetail from "./KWaiShopOutgoingprofitIDorderdetail.vue";
import KWaiShopDetailDayReport from "./KWaiShopDetailDayReport.vue";
import KWaiShopCommissionEstimate from "./KWaiShopCommissionEstimate.vue";
import KWaiShopBusinessDaily from "./KWaiShopBusiness/KWaiShopBusinessDaily.vue";
import KWaiShopBusinessExpert from "./KWaiShopBusiness/KWaiShopBusinessExpert.vue";

export default {
  name: "productReportKWaiShopIndex",
  components: {
    MyContainer, productReportKWaiShop, KWaiShopOrderDayReport, KWaiShopSkusDayReport, KWaiShopGoodCodeDayReport, KWaiShopIdDayReport, KWaiShopShopDayReport, KWaiShopDetailDayReport, KWaiShopCommodityDayReport, KWaiShopOutgoingprofitIDorderdetail, KWaiShopCommissionEstimate, KWaiShopBusinessDaily, KWaiShopBusinessExpert
  },
  data() {
    return {
      that: this,
      activeName: "first1",
    };
  },
  async mounted() {
    middlevue.$on('toLinkTxDetailDayReport', (data) => {
      if (data.isTrue && data.plat == 'tb') {
        this.activeName = 'first6';
        setTimeout(() => {
          middlevue.$emit('toLinkTxDetailDayReportQuery', data)
        }, 500);

      } else {
        this.activeName = 'first8';
        setTimeout(() => {
          middlevue.$emit('toLinkTxOutgoingprofitIDorderdetailQuery', data)
        }, 500);
      }
    })
  },
  beforeDestroy() {
    middlevue.$off('toLinkTxDetailDayReport')
  },
  methods: {
    ChangeActiveName(activeName) {
      this.activeName = 'first2';
      this.$nextTick(() => {
        this.$refs.KWaiShopOrderDayReport.KWaiShopGoodCodeDayReportArgument(activeName)
      })
    },
    ChangeActiveName2(activeName, No, Time) {
      this.activeName = 'first6';
      this.$nextTick(() => {
        this.$refs.KWaiShopDetailDayReport.KWaiShopDetailDayReportArgument(activeName, No, Time)
      })
    }
  },
};
</script>

<style lang="scss" scoped></style>
