<template>
  <div v-loading="overallLoading">
    <el-row>
      <el-col :span="24">
        <span style="text-align: center; font-size: 14px; color: red;">
          所有数据未做特殊说明，AI分析均基于近7天销量数据进行分析，数据仅供参考，具体以实际为准
        </span>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8">
        <el-scrollbar style="height: 615px">
          <el-descriptions :column="6" border :labelStyle="{ minWidth: '70px' }" :contentStyle="{ minWidth: '40px' }">
            <template v-for="(item, index) in displayItems">
              <el-descriptions-item v-if="shouldShowItem(item)" :key="index" :label="item.label" :span="item.span || 6">
                <template v-if="item.type === 'image'">
                  <el-image class="userAvatar" :style="item.style" :src="getImageUrl(item)"
                    :preview-src-list="[getImageUrl(item)]" />
                </template>
                <template v-else-if="item.type === 'table'">
                  <vxe-table :data="displayedInfo[item.dataKey]" max-height="240" border style="width: 410px;"
                    :column-config="{ resizable: true }" :row-config="{ isHover: true }">
                    <vxe-column v-for="col in item.columns" :key="col.field" :type="col.type" :field="col.field"
                      :title="col.title" :width="col.width == 'auto' ? '' : col.width"
                      :show-header-overflow="col.showHeaderOverflow" :sortable="col.sortable"
                      :show-overflow="col.showOverflow" :show-footer-overflow="col.showFooterOverflow" min-width="60">
                      <template v-if="col.slot" #default="{ row }">
                        <div v-if="col.type == 'avatarImg'" style="
                            display: flex;
                            align-items: center;
                            justify-content: center;
                          ">
                          <el-image class="userAvatar" style="width: 40px; height: 40px" :src="`http://192.168.16.240:8001/api/Admin/User/GetUserAvatar?type=2&id=${row[col.ddInfo.prop]
                            }`" :preview-src-list="[
                              `http://192.168.16.240:8001/api/Admin/User/GetUserAvatar?type=2&id=${row[col.ddInfo.prop]
                              }`,
                            ]" />
                        </div>
                        <div v-else style="
                            display: flex;
                            align-items: center;
                            justify-content: center;
                          ">
                          <el-image class="userAvatar" style="width: 40px; height: 40px" :src="`${row[col.field]}`"
                            :preview-src-list="[
                              `${row[col.field]}`,
                            ]" />
                        </div>
                      </template>
                    </vxe-column>
                  </vxe-table>
                </template>
                <template v-else>
                  <div v-if="item.type == 'ddTalk'"
                    style="display: flex; align-items: center; justify-content: center;width: 100%;">
                    <el-image :src="ddLogo" style="border-radius:5px;" class="ddTalk_Css"
                      @click="startSession(displayInfo[item.ddInfo.prop], item.ddInfo.type)" />
                    {{ getDisplayValue(item) }}
                    <el-image class="userAvatar" :style="item.style" :src="getImageUrl(item)"
                      :preview-src-list="[getImageUrl(item)]" />
                  </div>
                  <div v-else>
                    {{ getDisplayValue(item) }}
                    <span v-if="item.symbol">{{ item.symbol }}</span>
                  </div>
                </template>
              </el-descriptions-item>
            </template>
            <!-- 分析内容 - 始终显示 -->
            <el-descriptions-item :span="6" label="分析内容">
              <div style="max-height: 300px; overflow: auto; white-space: pre-wrap;white-space: pre-line">
                {{ inputTextLast }}
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </el-scrollbar>
      </el-col>
      <el-col :span="16">
        <div class="ai-container">
          <!-- 聊天区域 -->
          <div class="chat-area glassmorphism">
            <transition-group name="message-fade" tag="div" class="chat-history">
              <div v-for="(msg, index) in chatHistory" :key="index" class="message-bubble"
                :class="msg.type === 'user' ? 'user-bubble' : 'ai-bubble'">
                <div class="message-icon" v-if="msg.type !== 'user'">
                  <template>
                    <el-image :src="'https://nanc.yunhanmy.com:10010/media/video/20250407/1909135481253388289.jpg'"
                      :preview-src-list="[
                        'https://nanc.yunhanmy.com:10010/media/video/20250407/1909135481253388289.jpg',
                      ]" style="
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                        object-fit: cover;
                      ">
                    </el-image>
                  </template>
                </div>
                <div class="message-content" v-if="msg.type !== 'user'">
                  <div style="max-height: 494px; overflow-y: auto">
                    <div v-html="renderMdText(msg.content)"></div>
                  </div>
                  <div class="message-time" v-loading="inLoading" element-loading-spinner="el-icon-loading">
                    {{ formatTime(msg.timestamp) }}
                  </div>
                </div>
              </div>
            </transition-group>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import MarkdownIt from "markdown-it";
import { pageGetTbWarehouseAsync } from '@/api/inventory/prepack.js'
import { PageReqLog } from "@/api/admin/login-log.js";
import { getUserDingCode } from '@/api/admin/user'
import { toLogout } from '@/router'
const api = '/api/verifyOrder/'
import request from '@/utils/request'
import dayjs from 'dayjs'
export default {
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      api,
      ddLogo: require('@/static/images/dingding.png'),
      overallLoading: false,
      dimension: null,
      displayInfo: {},//展示的数据
      displayedInfo: {},//展示的数据
      avatar: "", //用户头像
      inputText: "",//输入文本
      inputTextLast: "",//上次的输入文本
      chatHistory: [],
      isProcessing: false,
      token: "",
      inLoading: false,
      markdownRender: new MarkdownIt({
        html: true,
        linkify: true,
        typographer: true,
      }),
      pagedata: {},
      displayItems: [
        { label: "商品编码", key: "goodsCode", span: 3, dataKey: "displayedInfo" },
        { label: "库存", key: "currentStock", span: 3, dataKey: "displayedInfo" },
        { label: "在途数量", key: "inTransitNum", span: 3, dataKey: "displayedInfo" },
        { label: "审批中数量", key: "pceAuditingQty", span: 3, dataKey: "displayedInfo" },
        { label: "发货地", key: "provinceCityDistrict", span: 3, dataKey: "displayedInfo" },
        { label: "包装材料", key: "packMtl", span: 3, dataKey: "displayedInfo" },
        {
          label: '近7天销量',
          type: 'table',  // 必须加type
          dataKey: 'sevenDaySalesVolumeList', // 对应展示的数据key
          span: 6, // 占6格，可按需要调整
          columns: [
            { type: 'seq', width: 40, title: '#' },
            { field: 'time', title: '日期', showHeaderOverflow: true, sortable: true, showOverflow: 'title', showFooterOverflow: true, width: 'auto' },
            { field: 'volume', title: '销量', showHeaderOverflow: true, sortable: true, showOverflow: 'title', showFooterOverflow: true, width: 'auto' }
          ]
        },
        {
          label: '仓库信息',
          type: 'table',
          dataKey: 'warehouseInformationList',
          span: 6,
          columns: [
            { type: 'seq', width: 40, title: '#' },
            { field: 'wmsName', title: '仓库', showHeaderOverflow: true, sortable: true, showOverflow: 'title', showFooterOverflow: true, width: '115' },
            { field: 'address', title: '收货地址', showHeaderOverflow: true, sortable: true, showOverflow: 'title', showFooterOverflow: true, width: '130' },
            { field: 'packMtl', title: '包装材料', showHeaderOverflow: true, sortable: true, showOverflow: 'title', showFooterOverflow: true, width: '110' },
            { field: 'expressFee', title: '快递费', showHeaderOverflow: true, sortable: true, showOverflow: 'title', showFooterOverflow: true, width: '90' },
          ]
        },
      ],
      currentController: null,//控制请求的取消
      isRequesting: false,//请求状态标识
      sevenDaySalesVolumeList: [],//七天销量列表
    };
  },
  async mounted() {
    // 从 cookie 中获取 token
    this.token = this.$store.getters.token;
    this.displayInfo = this.info;
    this.overallLoading = true;
    await this.handleMethod()
  },
  methods: {
    async startSession(id, type) {
      const { data, success } = await getUserDingCode({ type, id })
      if (success) {
        if (!data) return this.$message.error('未获取到钉钉id')
        window.open(`dingtalk://dingtalkclient/action/sendmsg?spm=dingtalk_id=${data}`, '_self')
      }
    },
    async handleMethod(type) {
      await this.getParameter(this.displayInfo);
      await this.setlog();
    },
    async setlog() {
      let _this = this;
      _this.pagedata.action = 'AI分析';
      setTimeout(async () => {
        for (var i = 0; i < document.getElementsByClassName('el-breadcrumb__inner').length; i++) {
          if (document.getElementsByClassName('el-breadcrumb__inner').length == i + 1) {
            _this.pagedata.modul = document.getElementsByClassName('el-breadcrumb__inner')[0]?.innerText;
            _this.pagedata.pageName = document.getElementsByClassName('el-breadcrumb__inner')[document.getElementsByClassName('el-breadcrumb__inner').length - 1]?.innerText;
          }
        }
        if (_this.pagedata.pageName) {
          for (var i = 0; i < document.getElementsByClassName('is-active').length; i++) {
            if (document.getElementsByClassName('is-active')[i].textContent == _this.pagedata.pageName) {
              _this.pagedata.pageTab = document.getElementsByClassName('is-active')[i + 2]?.textContent;
            }
          }
        }
        await PageReqLog(_this.pagedata.modul, _this.pagedata.pageName, _this.pagedata.pageTab ? _this.pagedata.pageTab : _this.pagedata.pageName, _this.pagedata.action);
      }, 500)
    },
    renderMdText(text) {
      return this.markdownRender.render(text);
    },
    async getParameter(row) {
      if (this.currentController) {
        this.currentController.abort();
      }
      this.chatHistory = []; //清空记录
      this.overallLoading = true;
      this.inLoading = true;
      try {
        let params = {
          date: dayjs(this.info.date).format('YYYY-MM-DD'),
          goodsCodes: this.info.goodsCode,
          pageSize: 50,
          currentPage: 1,
        }
        const { data, success } = await request.post(`${this.api}OrderGoods/RealTime/PageGetData`, params)
        const { data: data1, success: success1 } = await request.post(`${this.api}SaleItems/CodeStatBrand/LastSupplier`, [this.info.goodsCode])
        const { data: data2, success: success2 } = await pageGetTbWarehouseAsync({ pageSize: 50, currentPage: 1, warehouseId: row.wmsId })
        const { data: data3, success: success3 } = await request.post(`${this.api}SaleItems/SgwWmsSetting/PageGetData`, { pageSize: 999999, currentPage: 1 })
        const warehouseInformationList = data3.list;
        warehouseInformationList.forEach(item => {
          if (Array.isArray(item.packMtlKey) && item.packMtlKey.length > 0) {
            item.packMtl = item.packMtlKey.join(',');
          } else {
            item.packMtl = '';
          }
        });
        this.overallLoading = false;
        // 安全处理 list 和 item
        const list = Array.isArray(data?.list) ? data.list : [];
        const item = list[0] ?? {};
        const baseDate = item.date ? dayjs(item.date) : dayjs();
        const sevenDaySalesVolumeList = [];
        // 构建近七日销量数据
        for (let i = 0; i <= 7; i++) {
          const dayKey = `day${i}Qty`;
          if (item.hasOwnProperty(dayKey)) {
            sevenDaySalesVolumeList.push({
              time: baseDate.subtract(i, 'day').format('YYYY-MM-DD'),
              volume: item[dayKey]
            });
          }
        }
        this.sevenDaySalesVolumeList = sevenDaySalesVolumeList;
        this.displayedInfo = {
          sevenDaySalesVolumeList: sevenDaySalesVolumeList,
          warehouseInformationList: warehouseInformationList
        };
        const inputTextLines = [];
        const goodsCode = item.goodsCode ?? '-';
        const currentStock = item.jstUsableQty ?? 0;
        const inTransitNum = item.inTransitNum ?? 0;
        const pceAuditingQty = item.pceAuditingQty ?? 0;
        this.displayedInfo = {
          ...this.displayedInfo,
          goodsCode,
          currentStock,
          inTransitNum,
          pceAuditingQty
        };
        inputTextLines.push(`商品编码：【${goodsCode}】。`);
        inputTextLines.push(`近七日销量如下：`);
        sevenDaySalesVolumeList.forEach(dayItem => {
          inputTextLines.push(`${dayItem.time}：${dayItem.volume}`);
        });
        inputTextLines.push(`仓库信息如下：`);
        warehouseInformationList.forEach(item => {
          inputTextLines.push(`仓库：${item.wmsName} ，收货地址：${item.address} ，包装材料：${item.packMtl || '无'} ，快递费：${item.expressFee || '-'}`);
        });
        // 收货地处理
        let receivePlace = '义乌';
        if (Array.isArray(data2?.list) && data2.list.length > 0) {
          const region = data2.list[0]?.region;
          if (region) {
            receivePlace = region;
          }
        }
        // 平均在途时长处理
        const avgTransitHours = (data1 && typeof data1.hour === 'number') ? data1.hour : 0;
        let avgTransitDays = "假设平均时长为3天";
        if (avgTransitHours > 0) {
          avgTransitDays = `采购到货的时长为【${Math.floor(avgTransitHours / 24)}天${avgTransitHours % 24 === 0 ? '' : (avgTransitHours % 24) + '小时'}】`;
        }
        inputTextLines.push('');
        inputTextLines.push(`当前编码采购发货地为：【${data1?.provinceCityDistrict ?? row.provinceCityDistrict ?? "-"}】,包装材料为：【${data1?.packMtl ?? row.packMtl ?? "-"}】；`);
        inputTextLines.push(`请帮我基于近七日销量情况，各个仓库的收货地址、包装材料和快递费不同，${avgTransitDays}。当前库存为：【${currentStock}】，采购在途数量：【${inTransitNum}】，审批中数量：【${pceAuditingQty}】。基于快递费择优推荐哪个发货仓，采购多少数量的商品最为合适。`);
        const formattedText = inputTextLines.join('\n');
        this.inputText = formattedText;
        this.inputTextLast = formattedText;
        await this.sendMessage();
      } catch (error) {
        console.error('获取数据失败:', error);
        this.overallLoading = false;
        this.inLoading = false;
        if (error.name === 'AbortError') {
          console.log('请求被取消');
        } else {
          this.$message.error('获取数据失败');
        }
      }
    },
    async sendMessage() {
      // 如果输入为空或者正在处理中，则不发送消息
      if (!this.inputText.trim() || this.isProcessing) return;
      this.isProcessing = true;
      // 添加用户消息到历史记录
      this.chatHistory.push({
        type: 'user',
        content: this.inputText,
        timestamp: new Date()
      });
      // 添加空的 AI 消息到历史记录
      this.chatHistory.push({
        type: 'ai',
        content: '',
        timestamp: new Date()
      });
      try {
        await this.fetchStreamData(this.inputText);
      } catch (error) {
        console.error('API调用失败:', error);
        const lastIndex = this.chatHistory.length - 1;
        this.chatHistory[lastIndex].content = `错误：${error.message || '请求失败，请检查网络或稍后重试'}`;
        this.chatHistory[lastIndex].isError = true;
      } finally {
        // 重置
        this.isProcessing = false;
        this.inputText = '';
        // 自动滚动到底部
        this.$nextTick(() => {
          const chatArea = document.querySelector(".chat-area");
          if (chatArea) {
            chatArea.scrollTop = chatArea.scrollHeight;
          }
        });
      }
    },
    async fetchStreamData(question) {
      if (this.currentController) {
        this.currentController.abort();
      }
      this.currentController = new AbortController();//来控制请求的取消
      const baseUrl = 'http://192.168.16.240:8001/api/bladegateway/yunhan-gis-personnel/data-analysis/fastgptAiChat';
      if (!this.token) {
        toLogout();
        return;
      }
      try {
        this.inLoading = true;
        this.isRequesting = true;
        const formData = new FormData();
        formData.append('question', question);
        const response = await fetch(baseUrl, {
          method: 'POST',
          headers: {
            authorization: "Bearer " + this.token,
          },
          body: formData,
          signal: this.currentController.signal
        });
        this.inLoading = false;
        if (!response.ok) {
          if (response.status === 401) {
            toLogout();
            return;
          }
          throw new Error(`请求失败，状态码：${response.status}，状态文本：${response.statusText}`);
        }
        const reader = response.body.getReader();
        const decoder = new TextDecoder("utf-8");
        let buffer = '';
        let incompleteBuffer = '';
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = '';
          for (let line of lines) {
            if (!line.trim()) continue;
            let rawStr = line.replace(/^data:\s*/, '').trim();
            if (rawStr === '[DONE]' || rawStr === '"[DONE]"') continue;
            rawStr = incompleteBuffer + rawStr;
            try {
              // 处理可能的字符串包裹的 JSON
              if (rawStr.startsWith('"') && rawStr.endsWith('"')) {
                rawStr = JSON.parse(rawStr);
              }
              const parsed = JSON.parse(rawStr);
              const content = parsed?.choices?.[0]?.delta?.content || '';
              if (content) {
                const lastIndex = this.chatHistory.length - 1;
                if (lastIndex >= 0) {
                  this.chatHistory[lastIndex].content += content;
                }
              }
              incompleteBuffer = '';
            } catch (e) {
              if (e.message.includes('Unexpected end of JSON input') ||
                e.message.includes('Unterminated string')) {
                incompleteBuffer = rawStr;
              } else {
                console.warn('JSON解析失败:', {
                  errorMessage: e.message,
                  rawData: rawStr
                });
                incompleteBuffer = '';
              }
            }
          }
        }
      } catch (error) {
        if (error.name === 'AbortError') {
          console.log('请求被取消');
        } else {
          console.error('流式数据获取失败:', error);
          const lastIndex = this.chatHistory.length - 1;
          if (lastIndex >= 0) {
            this.chatHistory[lastIndex].content = `错误：${error.message || '请求失败，请检查网络或稍后重试'}`;
            this.chatHistory[lastIndex].isError = true;
          }
        }
      } finally {
        this.isRequesting = false;
        this.inLoading = false;
        this.currentController = null;
      }
    },
    formatTime(date) {
      // 格式化时间
      return date.toLocaleTimeString();
    },
    shouldShowItem(item) {
      if (item.showWhen) {
        return (
          this.dimension === item.showWhen || item.showWhen.includes(this.dimension)
        );
      }
      return true;
    },
    getImageUrl(item) {
      if (item.distinguish) {
        return `http://192.168.16.240:8001/api/Admin/User/GetUserAvatar?type=${item.imageType}&id=${this.displayInfo[item.ddInfo.prop]}`;
      } else {
        return this.displayedInfo[item.key] ? this.displayedInfo[item.key] : 'https://nanc.yunhanmy.com:10010/media/video/20250415/1912028614636257281.jpeg';
      }
    },
    getDisplayValue(item) {
      if (item.dataKey) {
        if (item.dataKey === 'displayedInfo') {
          // 根据 dimension 动态选择 key
          let actualKey = item.key;
          if (this.dimension === '002') {
            switch (item.key) {
              case 'skuimage':
                actualKey = 'images';
                break;
            }
          }
          const displayedVal = this.displayedInfo?.[actualKey];
          if (displayedVal !== undefined && displayedVal !== null) {
            return displayedVal;
          }
          const fallbackVal = this.displayInfo?.[actualKey];
          return fallbackVal !== undefined && fallbackVal !== null ? fallbackVal : '';
        } else {
          return this[item.dataKey][0]?.[item.key] === null ? 0 : this[item.dataKey][0]?.[item.key];
        }
      }
      if (item.key == 'proCode' && (this.dimension != '000')) {
        return ''
      } else {
        return this.displayInfo[item.key];
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.glassmorphism {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(12px);
}

.chat-area {
  height: 60vh;
  padding: 1.5rem;
  overflow-y: auto;
  transition: all 0.3s ease;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.chat-area::-webkit-scrollbar {
  display: none;
}

.message-bubble {
  display: flex;
  gap: 1rem;
  max-width: 100%;
  animation: floatUp 0.4s ease;
}

.user-bubble {
  margin-left: auto;
  flex-direction: row-reverse;
}

.message-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: #6C8CE7;
  font-size: 1.2rem;
}

.user-bubble .message-icon {
  background: rgba(108, 140, 231, 0.2);
}

.message-content {
  padding: 1rem 1.5rem;
  border-radius: 15px;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.user-bubble .message-content {
  background: #6C8CE7;
  color: white;
  border-radius: 15px 5px 15px 15px;
}

.ai-bubble .message-content {
  background: white;
  color: #333;
  border-radius: 5px 15px 15px 15px;
}

.message-time {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.4);
  margin-top: 0.5rem;
  text-align: right;
}

.user-bubble .message-time {
  color: rgba(255, 255, 255, 0.7);
}

.input-area {
  display: flex;
  gap: 1rem;
  padding: 1.2rem;
}

.input-field {
  border: none !important;
  background: transparent !important;
  padding: 0.8rem !important;
  flex-grow: 1;
}

.gradient-btn {
  background: linear-gradient(135deg, #6C8CE7 0%, #849dff 100%);
  border: none;
  border-radius: 12px !important;
  transition: all 0.3s ease;
  height: 50px;
  min-height: 4em;
  align-self: stretch;
  margin-top: 20px;
}

.glass-btn {
  height: 50px;
  min-height: 4em;
  align-self: stretch;
  margin-top: 20px;
}

.gradient-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(108, 140, 231, 0.3);
}

@keyframes floatUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .ai-container {
    padding: 1rem;
  }

  .message-bubble {
    max-width: 100%;
  }

  .input-area {
    flex-direction: column;
  }
}

::v-deep ul {
  line-height: 16px !important;
  margin: 0 !important;
}

::v-deep .el-button+.el-button,
.el-checkbox.is-bordered+.el-checkbox.is-bordered {
  margin-left: 3px;
}

.ddTalk_Css ::v-deep img {
  max-width: 20px !important;
  max-height: 20px !important;
  min-height: 20px !important;
  min-width: 20px !important;
  vertical-align: middle;
  cursor: pointer;
}
</style>
