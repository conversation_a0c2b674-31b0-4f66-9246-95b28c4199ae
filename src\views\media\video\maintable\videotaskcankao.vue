<template>
    <my-container v-loading="pageLoading" :element-loading-text="upmsginfo">
        <div>
            <!-- 短视频剪切 star -->
            <div>
                <div class="ckbt">参考视频 <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;任务名称：</span>{{ videoTaskId }}<span>&nbsp;&nbsp;|&nbsp;&nbsp;</span><span>{{ productShortName }}</span></div>
                <div class="ckck">
                    <div class="cksp">
                        <el-menu class="el-menu-demo" mode="horizontal" collapse-transition active-text-color="#409eff"
                            :default-active="activemenu">
                            <el-menu-item style="width: 20%; line-height: 65px" index="1" @click="showcuteinfo(1)"
                                :disabled="!taskPickList.includes('1')">视频一</el-menu-item>
                            <el-menu-item style="width: 20%; line-height: 65px" index="2" @click="showcuteinfo(2)"
                                :disabled="!taskPickList.includes('2')">视频二</el-menu-item>
                            <el-menu-item style="width: 20%; line-height: 65px" index="3" @click="showcuteinfo(3)"
                                :disabled="!taskPickList.includes('3')">视频三</el-menu-item>
                            <el-menu-item style="width: 20%; line-height: 65px" index="4" @click="showcuteinfo(4)"
                                :disabled="!taskPickList.includes('4')">视频四</el-menu-item>
                            <el-menu-item style="width: 20%; line-height: 65px" index="5" @click="showcuteinfo(5)"
                                :disabled="!taskPickList.includes('5')">视频五</el-menu-item>
                        </el-menu>
                    </div>
                    <div>
                        <div class="wzsp" @click="showcuteinfo(1)">
                            <div class="wzpd" style="height: 100%;">
                                <el-carousel style="height: 100%;" indicator-position="none">
                                    <el-carousel-item v-for="item in pdArrayCk1" :key="item.pdId">
                                        <div style="height: 100%; ">
                                            <div class="divcenter" style="height: 80%;">
                                                <video  style="width: 100%; height: 100%; object-fit: contain;" muted autoplay loop :src="item.url" controls oncontextmenu="return false;"></video>
                                            </div>
                                            <div class="wzpdbz" style="height: 20%;">
                                                <a style="text-decoration: none; color: #fff" href="#"
                                                    :title="item.title">{{ item.title }}</a>
                                            </div>
                                        </div>
                                    </el-carousel-item>
                                </el-carousel>
                            </div>
                        </div>
                        <div class="wzsp" @click="showcuteinfo(2)">
                            <div class="wzpd" style="height: 100%;">
                                <el-carousel style="height: 100%;" indicator-position="none">
                                    <el-carousel-item v-for="item in pdArrayCk2" :key="item.pdId">
                                        <div style="height: 100%; ">
                                            <div class="divcenter" style="height: 80%;">
                                                <video  style="width: 100%; height: 100%; object-fit: contain;" :src="item.url" controls  oncontextmenu="return false;"></video>
                                            </div>
                                            <div class="wzpdbz" style="height: 20%;">
                                                <a style="text-decoration: none; color: #fff" href="#"
                                                    :title="item.title">{{ item.title }}</a>
                                            </div>
                                        </div>
                                    </el-carousel-item>
                                </el-carousel>
                            </div>
                        </div>
                        <div class="wzsp" @click="showcuteinfo(3)">
                            <div class="wzpd" style="height: 100%;">
                                <el-carousel style="height: 100%;" indicator-position="none">
                                    <el-carousel-item v-for="item in pdArrayCk3" :key="item.pdId">
                                        <div style="height: 100%; ">
                                            <div class="divcenter" style="height: 80%;">
                                                <video  style="width: 100%; height: 100%; object-fit: contain;" :src="item.url" controls  oncontextmenu="return false;"></video>
                                            </div>
                                            <div class="wzpdbz" style="height: 20%;">
                                                <a style="text-decoration: none; color: #fff" href="#"
                                                    :title="item.title">{{ item.title }}</a>
                                            </div>
                                        </div>
                                    </el-carousel-item>
                                </el-carousel>
                            </div>
                        </div>
                        <div class="wzsp" @click="showcuteinfo(4)">
                            <div class="wzpd" style="height: 100%;">
                                <el-carousel style="height: 100%;" indicator-position="none">
                                    <el-carousel-item v-for="item in pdArrayCk4" :key="item.pdId">
                                        <div style="height: 100%; ">
                                            <div class="divcenter" style="height: 80%;">
                                                <video  style="width: 100%; height: 100%; object-fit: contain;" :src="item.url" controls oncontextmenu="return false;"></video>
                                            </div>
                                            <div class="wzpdbz" style="height: 20%;">
                                                <a style="text-decoration: none; color: #fff" href="#"
                                                    :title="item.title">{{ item.title }}</a>
                                            </div>
                                        </div>
                                    </el-carousel-item>
                                </el-carousel>
                            </div>
                        </div>
                        <div class="wzsp" @click="showcuteinfo(5)">
                            <div class="wzpd" style="height: 100%;">
                                <el-carousel style="height: 100%;" indicator-position="none">
                                    <el-carousel-item v-for="item in pdArrayCk5" :key="item.pdId">
                                        <div style="height: 100%; ">
                                            <div class="divcenter" style="height: 80%;">
                                                <video  style="width: 100%; height: 100%; object-fit: contain;" :src="item.url" controls oncontextmenu="return false;"></video>
                                            </div>
                                            <div class="wzpdbz" style="height: 20%;">
                                                <a style="text-decoration: none; color: #fff" href="#"
                                                    :title="item.title">{{ item.title }}</a>
                                            </div>
                                        </div>
                                    </el-carousel-item>
                                </el-carousel>
                            </div>
                        </div>
                    </div>
                    <!-- -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- -->
                    <div style="box-sizing: border-box; padding: 0 0.5%">
                        <el-button size="small" style="width: 100%" @click="opencute" type="primary"><i
                                class="el-icon-scissors"></i>&nbsp;&nbsp;剪切视频</el-button>
                    </div>
                    <div style="box-sizing: border-box; padding: 0 0.5% ;margin-top: 5px;">
                        <el-button size="small" style="width: 100%" @click="uploadVedio" type="primary"><i
                                class="el-icon-upload"></i>&nbsp;&nbsp;一键上传</el-button>
                    </div>
                    <!-- --------------------------上传相关信息------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ -->
                    <div v-if="curcuteindex == 1" >
                      <div  style="width: 96.8%; display: flex; " class="scsppdd">
                        <div v-for="item in cuteArray1" :key="item.cuteid" class="scsppd">
                          <panel   :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                   :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                   :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                   :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                   :ckVideoIndex="1" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                        </div>
                      </div>
                      <div  style="width: 96.8%; display: flex; " class="scsppdd">
                        <!-- 片段 star -->
                        <div v-for="item in cuteArray2" :key="item.cuteid" class="scsppd">
                          <panel     :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                     :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                     :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                     :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                     :ckVideoIndex="2" :vt="item.vt"  :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                        </div>
                      </div>
                      <div  style="width: 96.8%; display: flex; " class="scsppdd">
                        <!-- 片段 star -->
                        <div v-for="item in cuteArray3" :key="item.cuteid" class="scsppd">
                          <panel  :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                  :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                  :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                  :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                  :ckVideoIndex="3" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                        </div>
                      </div>
                      <div  style="width: 96.8%; display: flex; " class="scsppdd">
                        <div v-for="item in cuteArray4" :key="item.cuteid" class="scsppd">
                          <panel  :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                  :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                  :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                  :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                  :ckVideoIndex="4" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                        </div>
                      </div>
                      <div style="width: 96.8%; display: flex; " class="scsppdd">
                        <div v-for="item in cuteArray5" :key="item.cuteid" class="scsppd">
                          <panel   :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                   :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                   :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                   :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                   :ckVideoIndex="5" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                        </div>
                      </div>
                    </div>
                  <!-- 片段 end1 -->
                  <div v-if="curcuteindex == 2" >
                    <div  style="width: 96.8%; display: flex; " class="scsppdd">
                      <!-- 片段 star -->
                      <div v-for="item in cuteArray2" :key="item.cuteid" class="scsppd">
                        <panel     :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                   :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                   :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                   :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                   :ckVideoIndex="2" :vt="item.vt"  :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                    <div  style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray1" :key="item.cuteid" class="scsppd">
                        <panel   :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                 :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                 :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                 :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                 :ckVideoIndex="1" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                    <div  style="width: 96.8%; display: flex; " class="scsppdd">
                      <!-- 片段 star -->
                      <div v-for="item in cuteArray3" :key="item.cuteid" class="scsppd">
                        <panel  :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                :ckVideoIndex="3" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                    <div  style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray4" :key="item.cuteid" class="scsppd">
                        <panel  :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                :ckVideoIndex="4" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                    <div style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray5" :key="item.cuteid" class="scsppd">
                        <panel   :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                 :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                 :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                 :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                 :ckVideoIndex="5" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                  </div>
                  <!-- 片段 end2 -->
                  <div v-if="curcuteindex == 3" >
                    <div  style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray3" :key="item.cuteid" class="scsppd">
                        <panel  :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                :ckVideoIndex="3" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                    <div  style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray1" :key="item.cuteid" class="scsppd">
                        <panel   :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                 :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                 :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                 :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                 :ckVideoIndex="1" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                    <div  style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray2" :key="item.cuteid" class="scsppd">
                        <panel     :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                   :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                   :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                   :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                   :ckVideoIndex="2" :vt="item.vt"  :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                    <div  style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray4" :key="item.cuteid" class="scsppd">
                        <panel  :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                :ckVideoIndex="4" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                    <div style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray5" :key="item.cuteid" class="scsppd">
                        <panel   :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                 :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                 :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                 :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                 :ckVideoIndex="5" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                  </div>
                  <!-- 片段 end3 -->

                  <div v-if="curcuteindex == 4" >
                    <div  style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray4" :key="item.cuteid" class="scsppd">
                        <panel  :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                :ckVideoIndex="4" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                    <div  style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray1" :key="item.cuteid" class="scsppd">
                        <panel   :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                 :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                 :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                 :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                 :ckVideoIndex="1" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                    <div  style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray2" :key="item.cuteid" class="scsppd">
                        <panel     :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                   :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                   :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                   :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                   :ckVideoIndex="2" :vt="item.vt"  :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                    <div  style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray3" :key="item.cuteid" class="scsppd">
                        <panel  :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                :ckVideoIndex="3" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>

                    <div style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray5" :key="item.cuteid" class="scsppd">
                        <panel   :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                 :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                 :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                 :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                 :ckVideoIndex="5" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                  </div>
                  <!-- 片段 end4 -->
                  <div v-if="curcuteindex == 5" >
                    <div style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray5" :key="item.cuteid" class="scsppd">
                        <panel   :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                 :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                 :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                 :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                 :ckVideoIndex="5" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                    <div  style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray1" :key="item.cuteid" class="scsppd">
                        <panel   :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                 :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                 :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                 :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                 :ckVideoIndex="1" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                    <div  style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray2" :key="item.cuteid" class="scsppd">
                        <panel     :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                   :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                   :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                   :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                   :ckVideoIndex="2" :vt="item.vt"  :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                    <div  style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray3" :key="item.cuteid" class="scsppd">
                        <panel  :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                :ckVideoIndex="3" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                    <div  style="width: 96.8%; display: flex; " class="scsppdd">
                      <div v-for="item in cuteArray4" :key="item.cuteid" class="scsppd">
                        <panel  :key="refeashtimes+'23'" :ref="item.cuteid" :cuttitle="item.cutetitle" :cutmark="item.cutemark"
                                :pdtitle="item.pdtitle" :cutvideopath="item.cuteurl" :cutimgpath="item.cuteimg"
                                :uploadvideopath="item.uploadurl" :uploadimgpath="item.uploadimg"
                                :uploadInfo="item.uploadlist" :accepttyes="vediotype" :cuteid="item.cuteid"
                                :ckVideoIndex="4" :vt="item.vt" :delfunction="delfunction" @delCuteInfo="delCuteInfo" />
                      </div>
                    </div>
                  </div>
                  <!-- 片段 end5 -->


                  <!-- -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- -->
                </div>
            </div>
            <!-- 短视频参考 end -->
            <!-- 短视频剪切 star -->
            <div>
                <el-drawer title="我是标题" size="810px;" :visible.sync="drawer" direction="ttb" :with-header="false">
                    <videotaskcutvedio :videoTaskId="videoTaskId" v-if="refalshcute" :closeInfo="closeInfo" />
                </el-drawer>
            </div>
            <!-- 短视频剪切 end -->
            <el-dialog title="上传视频结果" :visible.sync="dialogVisibleSyjout" width="50%" @close="dialogVisibleSyjout">
                <my-container style="height: 450px">
                    <el-table :height="450" ref="MediamultipleTableout" row-key="id" :data="uploadFiletempList"
                        highlight-current-row>
                        <el-table-column label="参考视频" width="150">
                            <template slot-scope="scope">
                                <span> 参考视频{{ scope.row.ckVideoIndex }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="name" label="剪辑片段" width="150">
                            <template slot-scope="scope">
                                <span> {{ scope.row.cutetitle }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="失败文件" width="200">
                            <template slot-scope="scope"> {{ scope.row.file.name }} </template>
                        </el-table-column>
                    </el-table>
                </my-container>
            </el-dialog>
        </div>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import videotaskcutvedio from "@/views/media/video/maintable/videotaskcutvedio.vue";
import {
    getVideoTask2PdListInfo, getVideoTasCuteInfo, saveUploadTeskVideoAsync,
    deleteUploadVideoByIdAsync,checkVideoTaskCanChange
} from "@/api/media/vediotask";
import { delTaskCuteVideo } from "@/api/media/video";
import draggablevue from 'vuedraggable'
import ElImageViewer from '@/views/media/shooting/imageviewer.vue';
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
import panel from './commpents/panel.vue';
export default {

    components: { videotaskcutvedio, MyContainer, draggablevue, ElImageViewer, videoplayer, panel },
    data() {
        return {
            vediotype:".mp4,.mov,.vedio,.av,.wmv,.mpg,.mpeg,.rm,.flv,.swf",
            activemenu:'1',
            pageLoading: false,
            drawer: false,
            refalshcute: true,
            videoTaskId: 0,
            productShortName:null,
            pdArrayCk1: [],
            pdArrayCk2: [],
            pdArrayCk3: [],
            pdArrayCk4: [],
            pdArrayCk5: [],
            taskPickList: [],
            cuteArray1: [],
            cuteArray2: [],
            cuteArray3: [],
            cuteArray4: [],
            cuteArray5: [],
            retdata: [],
            activeNames: ['1'],
            curcuteindex: 1,
            startIndex: 0,
            accepttyes: "*",
            islook: true,
            dialogVisibleSyjout: false,
            atfterUplaodData: null,
            uploadFiletempList: [],
            tempoutlist: [],
            upmsginfo: null,
            percentage: 0,
            refeashtimes :1,
            isreLoading: true,
        };
    },
    async mounted() {
        await this.gettaskid();
        await this.getPdCuteinfo();
    },

    methods: {
        async delCuteInfo(param) {
            var res = await delTaskCuteVideo(param);
            if (res?.success) {
                if(param.vt==2)
                {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.gettaskid();
                    await this.getPdCuteinfo();
                }else{
                switch (param.ckIndex) {
                    case 1:
                        for (let num in this.cuteArray1) {
                            if (this.cuteArray1[num].cuteid == param.cuteId) {
                                this.cuteArray1.splice(num, 1)
                            }
                        }
                        break;
                    case 2:
                        for (let num in this.cuteArray2) {
                            if (this.cuteArray2[num].cuteid == param.cuteId) {
                                this.cuteArray2.splice(num, 1)
                            }
                        }
                        break;
                    case 3:
                        for (let num in this.cuteArray3) {
                            if (this.cuteArray3[num].cuteid == param.cuteId) {
                                this.cuteArray3.splice(num, 1)
                            }
                        }
                        break;
                    case 4:
                        for (let num in this.cuteArray4) {
                            if (this.cuteArray4[num].cuteid == param.cuteId) {
                                this.cuteArray4.splice(num, 1)
                            }
                        }
                        break;
                    case 5:
                        for (let num in this.cuteArray5) {
                            if (this.cuteArray5[num].cuteid == param.cuteId) {
                                this.cuteArray5.splice(num, 1)
                            }
                        }
                        break;
                }
                this.$message({ message: '操作成功', type: "success" });
               }
            }

        },
        //删除已上传信息
        async delfunction(ret) {
            this.upmsginfo=null;
            this.pageLoading = true;
            var isdel = false;
            await deleteUploadVideoByIdAsync({ videoId: ret.outComeId })
                .then(res => {
                    if (res?.success) {
                        this.$message({ message: '操作成功', type: "success" });
                        this.pageLoading = false;
                        isdel = true;
                    } else {
                        this.pageLoading = false;
                    }
                })
                .catch(_ => {
                    this.pageLoading = false;
                });
            return isdel;
        },
        //获取已上传的信息
        getUploadInfo() {
            var retarray = [];
            for (let num in this.cuteArray1) {
                var infos = this.$refs[this.cuteArray1[num].cuteid][0].getReturns();
                var cutetitle= this.cuteArray1[num].cutetitle;
                infos.data.forEach(function (item) {
                    item.cutetitle = cutetitle;
                    item.cuteindex = 1;
                    retarray.push(item);
                });
            }
            for (let num in this.cuteArray2) {
                var infos = this.$refs[this.cuteArray2[num].cuteid][0].getReturns();
                var cutetitle= this.cuteArray2[num].cutetitle;
                infos.data.forEach(function (item) {
                    item.cutetitle = cutetitle;
                    item.cuteindex = 2;
                    retarray.push(item);
                });
            }
            for (let num in this.cuteArray3) {
                var infos = this.$refs[this.cuteArray3[num].cuteid][0].getReturns();
                var cutetitle= this.cuteArray3[num].cutetitle;
                infos.data.forEach(function (item) {
                    item.cutetitle = cutetitle;
                    item.cuteindex = 3;
                    retarray.push(item);
                });
            }
            for (let num in this.cuteArray4) {
                var infos = this.$refs[this.cuteArray4[num].cuteid][0].getReturns();
                var cutetitle= this.cuteArray4[num].cutetitle;
                infos.data.forEach(function (item) {
                    item.cutetitle = cutetitle;
                    item.cuteindex = 4;
                    retarray.push(item);
                });
            }
            for (let num in this.cuteArray5) {
                var infos = this.$refs[this.cuteArray5[num].cuteid][0].getReturns();
                var cutetitle= this.cuteArray5[num].cutetitle;
                infos.data.forEach(function (item) {
                    item.cutetitle = cutetitle;
                    item.cuteindex = 5;
                    retarray.push(item);
                });
            }
            return retarray;
        },
        async AjaxFile(file, i, batchnumber) {
            var name = file.name; //文件名
            var size = file.size; //总大小shardSize = 2 * 1024 * 1024,
            var shardSize = 1  * 1024 *1024;
            var shardCount = Math.ceil(size / shardSize); //总片数
            if (i >= shardCount) {
                return;
            }
            //计算每一片的起始与结束位置
            var start = i * shardSize;
            var end = Math.min(size, start + shardSize);
            //构造一个表单，FormData是HTML5新增的
            i = i + 1;
            var form = new FormData();
            form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分
            form.append("batchnumber", batchnumber);
            form.append("fileName", name);
            form.append("total", shardCount); //总片数
            form.append("index", i); //当前是第几片

            const res = await xMTVideoUploadBlockAsync(form);
            this.percentage = (i * 100 / shardCount).toFixed(2);
            if (res?.success) {
                if (i == shardCount) {
                    this.atfterUplaodData = res.data;
                } else {
                    await this.AjaxFile(file, i, res.data);
                }
            } else {
                this.$message({ message: res?.msg, type: "warning" });
                this.ScpdLoading = false;

            }
        },
        //上传视频
        async uploadVedio() {
            var ret = await  checkVideoTaskCanChange({taskid:this.videoTaskId});
            if(!ret?.success) return;
            this.uploadFiletempList = this.getUploadInfo();
            if (this.uploadFiletempList.length == 0) {
                this.$message({ message: '请选择上传文件', type: "waring" });
                return;
            }
            await this.uploadFiletempasync()
        },
        //批量上传
        async uploadFiletempasync() {
            this.pageLoading = true;
            this.tempoutlist = [];
            this.uploadFiletempList.forEach(element => {
                this.tempoutlist.push(element);
            });
            this.startIndex = this.tempoutlist.length;
            let curcheckindex = 0;
            let canupload = true ;
            for (var i = 0; i < this.tempoutlist.length; i++) {
                var item = this.tempoutlist[i];
                this.upmsginfo = "正在上传：参考视频" + item.ckVideoIndex + "-" + item.cutetitle + "-" + item.fileName +"   " +  this.percentage +"%";;
                this.atfterUplaodData = null;
                this.percentage = 0;
                this.startIndex = this.startIndex - 1;

                if (item.filestaus == 0) {
                    if(curcheckindex != item.cuteindex ){
                        curcheckindex = item.cuteindex;
                        var ret = await  checkVideoTaskCanChange({taskid:this.videoTaskId,pdindex:item.cuteindex});
                        if(ret?.success){
                            canupload = true;
                        }else{
                            canupload = false;
                        }
                    }
                    if(canupload){
                        await this.AjaxFile(item.file, 0, "")
                        .then(async x=>{
                            if (this.atfterUplaodData != null) {
                                await this.afteruploadtempList(item, this.startIndex);
                            }
                        }).catch(err => {
                            console.log(err)
                        });
                    }
                } else {
                    for (let num in this.uploadFiletempList) {
                        if (this.uploadFiletempList[num].cuteid == item.cuteid && this.uploadFiletempList[num].uid == item.uid) {
                            this.uploadFiletempList.splice(num, 1)
                        }
                    }
                    if (this.startIndex == 0) {
                        this.pageLoading = false;
                        await this.getPdCuteinfo();
                        this.upmsginfo = "上传完成！";
                        this.$message({ message: '上传完成！', type: "success" });
                        if (this.uploadFiletempList.length > 0) {
                            this.$message({ message: '存在上传失败信息请查看，截图保存', type: "error" });
                            this.dialogVisibleSyjout = true;
                        }
                    }
                }

                this.percentage = 0;
            }
            this.pageLoading = false;
        },
        //业务处理
        async afteruploadtempList(item, startIndex) {
            const form = new FormData();
            this.atfterUplaodData.fileName = item.file.name;
            form.append("upfile", JSON.stringify(this.atfterUplaodData));
            form.append("videotaskid", this.videoTaskId);
            form.append("cuteid", item.cuteid);
            const res = await saveUploadTeskVideoAsync(form);
            if (res?.success) {
                for (let num in this.uploadFiletempList) {
                    if (this.uploadFiletempList[num].cuteid == item.cuteid && this.uploadFiletempList[num].uid == item.uid) {
                        this.uploadFiletempList.splice(num, 1)
                    }
                }
            }
            if (startIndex == 0) {
                this.pageLoading = false;

                await this.getPdCuteinfo();
                this.upmsginfo = "上传完成！";
                this.$message({ message: '上传完成！', type: "success" });
                if (this.uploadFiletempList.length > 0) {
                    this.$message({ message: '存在上传失败信息请查看，截图保存', type: "error" });
                    this.dialogVisibleSyjout = true;
                }
            }
        },
        //上传超出提出
        exceed() {
            this.$message({ message: "超出上传数量限制", type: "warning" });
        },
        //上传方法
        async UpSuccessload(file) {
            this.IsChang = true;
            this.uploading = true;
            this.retdata.push({
                fileName: file.file.name
                , url: ""
                , outComeId: 0
                , uid: file.file.uid
                , filestaus: 0
                , file: file.file
            });
            this.uploading = false;
        },
        //展示对应剪辑视频
        showcuteinfo(index) {
            this.activemenu =index.toString();
            this.curcuteindex = index;
        },
        //------------------------------------------------------------
        opencute() {
            this.drawer = true;
            this.refalshcute = true;
        },
        closeInfo(action) {
            this.drawer = false;
            if (action == 1) {
                this.refalshcute = false;
            }
        },
        async gettaskid() {
            this.videoTaskId = new Number(this.$route.query.id);
            if (this.videoTaskId > 0) {
                var res = await getVideoTask2PdListInfo({ videoTaskId: this.videoTaskId });
                if (res?.success) {
                    this.pdArrayCk1 = res.data.pdArrayCk1;
                    this.pdArrayCk2 = res.data.pdArrayCk2;
                    this.pdArrayCk3 = res.data.pdArrayCk3;
                    this.pdArrayCk4 = res.data.pdArrayCk4;
                    this.pdArrayCk5 = res.data.pdArrayCk5;
                    this.taskPickList = res.data.task.taskPickList;
                    this.productShortName= res.data.task.productShortName;
                }
            }
        },
        async getPdCuteinfo() {
            if (this.videoTaskId > 0) {

                var res = await getVideoTasCuteInfo({ videoTaskId: this.videoTaskId });
                if (res?.success) {


                    this.cuteArray1 = res.data.cuteArray1;
                    this.cuteArray2 = res.data.cuteArray2;
                    this.cuteArray3 = res.data.cuteArray3;
                    this.cuteArray4 = res.data.cuteArray4;
                    this.cuteArray5 = res.data.cuteArray5;

                    ++this.refeashtimes;

                }

            }
        }
    },
};
</script>

<style lang="scss" scoped >
/* 短视频参考 star */
.ckbt,
.ckck {
    margin: 0 auto;
    width: 100%;
    min-width: 1250px;

}

.ckbt {
    height: 55px;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 20px 275px;
    font-size: 18px;
    color: #666;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
}

.ckck {
    min-width: 1700px;
    /* height: 1000px; */
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 20px 300px;
    padding-bottom: 60px;
}

.cksp {
    width: 99%;
    background-color: antiquewhite;
    box-sizing: border-box;
    margin: 10px auto;
    /* font-weight: bold; */
    text-align: center;
}

.ckck .wzsp {
    width: 20%;
    height: 360px;
    background-color: rgb(255, 255, 255);
    display: inline-block;
    box-sizing: border-box;
    padding: 0 0.5%;
    margin-bottom: 10px;
}

.wzsp .wzpd {
    width: 100%;
    height: 80%;
    background-color: #f5f5f5;
    box-sizing: border-box;
    border: 2px solid #dcdfe6;
    text-align: center;
}

.wzsp .wzpd:hover {
    width: 100%;
    height: 80%;
    background-color: #f5f5f5;
    box-sizing: border-box;
    border: 2px solid #409eff;
    text-align: center;
}

.wzsp .wzpdbz {
    width: 100%;
    height: 20%;
    font-size: 14px;
    color: #fff;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.6);
    box-sizing: border-box;
    padding: 11px 15px;

    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;

}

.scsppd {
    width: 21%;
    /* height: 180px; */
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    overflow: hidden;
    background-color: rgb(255, 255, 255);
    // box-sizing: border-box;
    padding: 1%;
    margin: 5px 0.9%;
    border: 1px solid #dcdfe6;
}

.scsppdd {
    margin: 5px 6px;
    padding: 1%;
    border: 1px solid #dcdfe6;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    overflow: hidden;
}

.ckck .scsppdw {
    width: 100%;
    // background-color: rgb(255, 224, 224);
    display: inline-block;
    margin-right: 1%;
}

.scsppdw .sppdn {
    width: 23%;
    background-color: #ffffff;
    display: inline-block;
    box-sizing: border-box;
    padding: 0.5%;
}

.scsppdw .sppdnbz {
    width: 54%;
    background-color: #ffffff;
    display: inline-block;
    box-sizing: border-box;
    padding: 0.5%;
}

.wjdnr {
    overflow: hidden;
    -webkit-line-clamp: 1;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}

.scdpd,
.jqdpd {
    width: 100%;
    height: 80px;
    /* background-color: #077bff; */
    box-sizing: border-box;
    /* border: 1px solid #dcdfe6; */
    margin: 5px 0;
    font-size: 14px;
    color: #999;
    overflow: hidden;
    text-overflow: ellipsis;

    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
}

/* 短视频参考 end */

/* 短视频剪切 star */

.jqsp {
    background-color: #fff;
}

.jqsp .jqspbt {
    height: 55px;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 20px 35px;
    font-size: 16px;
    color: #666;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
}

.jqsp .jqwzpd {
    width: 18%;
    height: 550px;
    background-color: #ffffff;
    display: inline-block;
    box-sizing: border-box;
    padding: 10px 35px;
    overflow: hidden;
}

.jqsp .jqwzpdnr {
    width: 90%;
    height: 340px;
    background-color: #fff;
    margin: 0 auto;
    align-items: center;
    text-align: center;
    box-sizing: border-box;
    border: 1px solid #dcdfe6;
}

.jqsp .jqwzpdbz {
    width: 90%;
    height: 70px;
    background-color: rgba(0, 0, 0, 0.5);
    text-align: center;
    font-size: 14px;
    color: #fff;
    margin: 0px auto 10px auto;
    box-sizing: border-box;
    padding: 10px 15px;


    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;


}

.jqsp .jqwzpdan {
    width: 90%;
    max-height: 100px;
    background-color: rgb(255, 255, 255);
    text-align: center;
    font-size: 14px;
    color: #fff;
    margin: 0px auto;
    box-sizing: border-box;
    padding: 15px 20px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.jqsp .jqxzsp {
    width: 10%;
    height: 550px;
    background-color: rgb(255, 255, 255);
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.jqsp .jqyjdl {
    width: 72%;
    height: 550px;
    background-color: rgb(255, 255, 255);
    display: inline-block;
    box-sizing: border-box;
    padding: 20px 35px;
    overflow: auto;
}

.jqsp .jqsjx {
    width: 100%;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 10px 15px;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
}

.jqsp .qxtj {
    height: 80px;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 20px 60px;
}

.divcenter {
    width: 100%;
    background-color: rgb(255, 255, 255);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.flexwidth {
    width: 63px;
    display: flex;
}

::v-deep .el-carousel__container {
    position: relative;
    height: 100%;
}

::-webkit-scrollbar {
    width: 5px;
    height: 10px;
}
</style>
