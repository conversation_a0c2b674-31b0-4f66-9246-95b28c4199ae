<template>
    <my-container v-loading="pageLoading">
      <template #header>
        <div style="height: 40px; margin-top: 10px;">
        <el-button-group>
        <el-button style="padding: 0;margin: 0;">
            <el-date-picker style="width: 210px" v-model="filter.timerange" type="daterange" :clearable="false" format="yyyy-MM-dd" value-format="yyyyMMdd" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期" @change="datepickerfuc"></el-date-picker>
        </el-button>

        <el-button style="padding: 0;margin: 0;">
            <el-select v-model.trim="filter.platform" multiple collapse-tags filterable clearable placeholder="平台"
              style="width: 165px">
              <el-option label="天猫" value="1"></el-option>
              <el-option label="淘宝" value="9"></el-option>
              <el-option label="淘工厂" value="8"></el-option>
              <el-option label="苏宁" value="10"></el-option>
            </el-select>
        </el-button>

          <el-button style="padding: 0;margin: 0;">
            <inputYunhan  ref="proCode" v-model="filter.proCode" :inputt.sync="filter.proCode" placeholder="商品ID" :maxRows="300" :clearable="true" @callback="callbackProCode" title="商品ID" ></inputYunhan>
        </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-select filterable v-model="filter.groupIds" collapse-tags clearable placeholder="运营组" style="width: 190px" multiple>
                <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"  />
              </el-select>
            </el-button>

          <el-button style="padding: 0;margin: 0;">
            <el-select filterable v-model="filter.operateSpecialUserIds" collapse-tags clearable placeholder="运营专员" multiple
              style="width: 150px">
              <el-option key="无运营专员" label="无运营专员" :value="0"></el-option>
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"  />
            </el-select>
        </el-button>

          <el-button style="padding: 0;margin: 0;">
            <el-select filterable v-model="filter.userIds" collapse-tags clearable placeholder="运营助理" style="width: 150px" multiple>
                <el-option key="无运营助理" label="无运营助理" :value="0"></el-option>
                <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"  />
              </el-select>
            </el-button>

          <el-button style="padding: 0;margin: 0;">
            <el-select filterable v-model="filter.profit3UnZero" collapse-tags clearable placeholder="毛利3" style="width: 130px">
              <el-option label="正利润" :value="false"/>
              <el-option label="负利润" :value="true"/>
            </el-select>
        </el-button>
         
        <el-button style="padding: 0;margin: 0;">
            <el-select filterable v-model="filter.profit4UnZero" collapse-tags clearable placeholder="净利润" style="width: 130px">
              <el-option label="正利润" :value="false"/>
              <el-option label="负利润" :value="true"/>
            </el-select>
        </el-button>
  
            <el-button type="primary" @click="onSearch" style="width: 70px;">搜索</el-button>
  
            <el-button type="primary" @click="onExport" style="width: 70px;">导出</el-button>
        </el-button-group>
         
        </div>
      </template>
  
      <ces-table :id="'GuoHuoWuJie202408041427'" ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
        :summaryarry='summaryarry' :showsummary='true' :tableData='list' :tableCols='tableCols' :isSelection="false"
        :isSelectColumn="false" :loading="listLoading" style="width:100%;height:94%;margin: 0">
      </ces-table>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
      
    </my-container>
  </template>
  
  <script>
  import MyContainer from "@/components/my-container";
  import dayjs from "dayjs";
  import cesTable from "@/components/VxeTable/yh_vxetable.vue";
  import buschar from '@/components/Bus/buschar'
  import { formatTime, formatPlatform, pickerOptions ,formatLinkProCode} from "@/utils/tools";
  import {getGuoHuoWuJie_Tx as getPurchaseNewPlanTurnDayPageList,exportGuoHuoWuJie_Tx as exportPurchaseNewPlanTurnDayList }from '@/api/bookkeeper/reportdayV2'
  import { getTime } from '@/utils/getCols'
  import inputYunhan from "@/components/Comm/inputYunhan";
  import {getDirectorGroupList,getDirectorList} from '@/api/operatemanage/base/shop'
  import middlevue from "@/store/middle.js"
  const tableCols = [
    { istrue: true, prop: 'yearMonthDay', label: '日期', width: '80', align: 'center', formatter: (row) => formatTime(row.yearMonthDay, 'YYYY-MM-DD') },
    {istrue:true,prop:'platform',fix:true, exportField:'platformstr',label:'平台', width:'60',sortable:'custom',formatter:(row)=>formatPlatform(row.platForm),type:'custom'},
    {istrue:true,prop:'shopCode',exportField:'shopName',label:'店铺名称',sortable:'custom', width:'80',formatter:(row)=> row.shopName,type:'custom'},
    {istrue:true,prop:'proCode',fix:true,label:'商品ID', width:'80',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
    {istrue:true,prop:'proName',label:'商品名称',sortable:'custom', width:'80'},
    {istrue:true,prop:'groupName',exportField:'groupName',label:'小组',sortable:'custom', width:'60',formatter:(row)=> row.groupName,type:'custom'},
        {istrue:true,prop:'operateSpecialUserName',exportField:'operateSpecialUserName',label:'运营专员',sortable:'custom', width:'80',formatter:(row)=> row.operateSpecialUserName,type:'custom'},
        {istrue:true,prop:'userName',exportField:'userName',label:'运营助理',sortable:'custom', width:'80',formatter:(row)=> row.userName,type:'custom'},
        {istrue:true,prop:'onTime',label:'上架时间',sortable:'custom', width:'80',formatter:(row)=>formatTime(row.onTime, 'YYYY-MM-DD') },
        {istrue:true,summaryEvent:true,prop:'profit3',label:'毛三利润',sortable:'custom', width:'80',type:'custom',tipmesg:'毛二利润-预估费用',permission:"lirunprsi",formatter:(row)=> !row.profit3?" ": row.profit3?.toFixed(2)},
        {istrue:true,summaryEvent:true,prop:'profit3Rate',label:'毛三利润率',type:'custom',tipmesg:'毛三利润/销售金额',sortable:'custom', width:'80',permission:"lirunprsi",formatter:(row)=> !row.profit3Rate?" ": (row.profit3Rate*100).toFixed(2)+'%'},
        {istrue:true,summaryEvent:true,prop:'profit4',label:'净利润',type:'custom',tipmesg:'毛三利润-公摊费-盘点损益',sortable:'custom', width:'80',permission:"lirunprsi",formatter:(row)=> !row.profit4?" ": row.profit4.toFixed(2)},
        {istrue:true,summaryEvent:true,prop:'profit4Rate',label:'净利率',type:'custom',tipmesg:'净利润/销售金额',sortable:'custom', width:'80',permission:"lirunprsi",formatter:(row)=> !row.profit4Rate?" ": (row.profit4Rate*100).toFixed(2)+'%'},
        { istrue: true, summaryEvent: true, prop: 'alladv', label: '总广告费', sortable: 'custom', width: '80', formatter: (row) => row.alladv == 0 ? " " : row.alladv?.toFixed(2) },
        {istrue:true,summaryEvent:true,prop:'advratio',label:'总广告占比%',sortable:'custom', width:'80',formatter:(row)=> !row.advratio?" ": (row.advratio*100).toFixed(2)+"%"},

    {
    istrue: true, summaryEvent: true, prop: 'yyfy', label: `花费`, merge: true, prop: 'mergeField', permission: "cgcoltxpddprsi",
    cols: [
      { istrue: true, summaryEvent: true, prop: 'duoMuBiaoZhiTou', label: '多目标直投', sortable: 'custom', width: '80', formatter: (row) => row.duoMuBiaoZhiTou == 0 ? " " : row.duoMuBiaoZhiTou?.toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'guanJianCiTuiGuang', label: '关键词推广', sortable: 'custom', width: '80', formatter: (row) => row.guanJianCiTuiGuang == 0 ? " " : row.guanJianCiTuiGuang?.toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'huoDongJiaSu', label: '活动加速', sortable: 'custom', width: '80', formatter: (row) => row.huoDongJiaSu == 0 ? " " : row.huoDongJiaSu?.toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'huoPinJiaSu', label: '货品加速', sortable: 'custom', width: '80', formatter: (row) => row.huoPinJiaSu == 0 ? " " : row.huoPinJiaSu?.toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'jingZhunRenQunTuiGuang', label: '精准人群推广', sortable: 'custom', width: '80', formatter: (row) => row.jingZhunRenQunTuiGuang == 0 ? " " : row.jingZhunRenQunTuiGuang?.toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'laXinKuai', label: '拉新快', sortable: 'custom', width: '80', formatter: (row) => row.laXinKuai == 0 ? " " : row.laXinKuai?.toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'quanZhanTuiGuang', label: '全站推广', sortable: 'custom', width: '80', formatter: (row) => row.quanZhanTuiGuang == 0 ? " " : row.quanZhanTuiGuang?.toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'shangXinKuai', label: '上新快', sortable: 'custom', width: '80', formatter: (row) => row.shangXinKuai == 0 ? " " : row.shangXinKuai?.toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'sumCost', label: '总花费', sortable: 'custom', width: '80', formatter: (row) => row.sumCost == 0 ? " " : row.sumCost?.toFixed(2) },
    ]
  },
  {
    istrue: true, summaryEvent: true, prop: 'yyfy', label: `成交`, merge: true, prop: 'mergeField1', permission: "cgcoltxpddprsi",
    cols: [
    { istrue: true, summaryEvent: true, prop: 'duoMuBiaoZhiTouCjMoney', label: '多目标直投成交', sortable: 'custom', width: '80', formatter: (row) => row.duoMuBiaoZhiTouCjMoney == 0 ? " " : row.duoMuBiaoZhiTouCjMoney?.toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'guanJianCiTuiGuangCjMoney', label: '关键词推广成交', sortable: 'custom', width: '80', formatter: (row) => row.guanJianCiTuiGuangCjMoney == 0 ? " " : row.guanJianCiTuiGuangCjMoney?.toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'huoDongJiaSuCjMoney', label: '活动加速成交', sortable: 'custom', width: '80', formatter: (row) => row.huoDongJiaSuCjMoney == 0 ? " " : row.huoDongJiaSuCjMoney?.toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'huoPinJiaSuCjMoney', label: '货品加速成交', sortable: 'custom', width: '80', formatter: (row) => row.huoPinJiaSuCjMoney == 0 ? " " : row.huoPinJiaSuCjMoney?.toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'jingZhunRenQunTuiGuangCjMoney', label: '精准人群推广成交', sortable: 'custom', width: '80', formatter: (row) => row.jingZhunRenQunTuiGuangCjMoney == 0 ? " " : row.jingZhunRenQunTuiGuangCjMoney?.toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'laXinKuaiCjMoney', label: '拉新快成交', sortable: 'custom', width: '80', formatter: (row) => row.laXinKuaiCjMoney == 0 ? " " : row.laXinKuaiCjMoney?.toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'quanZhanTuiGuangCjMoney', label: '全站推广成交', sortable: 'custom', width: '80', formatter: (row) => row.quanZhanTuiGuangCjMoney == 0 ? " " : row.quanZhanTuiGuangCjMoney?.toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'shangXinKuaiCjMoney', label: '上新快成交', sortable: 'custom', width: '80', formatter: (row) => row.shangXinKuaiCjMoney == 0 ? " " : row.shangXinKuaiCjMoney?.toFixed(2) },
      { istrue: true, summaryEvent: true, prop: 'sumUserMoney', label: '总成交', sortable: 'custom', width: '80', formatter: (row) => row.sumUserMoney == 0 ? " " : row.sumUserMoney?.toFixed(2) },
    ]
  },
  {
    istrue: true, summaryEvent: true, prop: 'yyfy', label: `ROI`, merge: true, prop: 'mergeField2', permission: "cgcoltxpddprsi",
    cols: [
      { istrue: true, summaryEvent: true, prop: 'duoMuBiaoZhiTouRoi', label: '多目标直投ROI', sortable: 'custom',tipmesg:'总成交金额/花费', width: '80', formatter: (row) => row.duoMuBiaoZhiTouRoi == 0 ? " " : (row.duoMuBiaoZhiTouRoi*100).toFixed(2) +'%'},
      { istrue: true, summaryEvent: true, prop: 'guanJianCiTuiGuangRoi', label: '关键词推广ROI', sortable: 'custom',tipmesg:'总成交金额/花费', width: '80', formatter: (row) => row.guanJianCiTuiGuangRoi == 0 ? " " : (row.guanJianCiTuiGuangRoi*100).toFixed(2)+'%' },
      { istrue: true, summaryEvent: true, prop: 'huoDongJiaSuRoi', label: '活动加速ROI', sortable: 'custom',tipmesg:'总成交金额/花费', width: '80', formatter: (row) => row.huoDongJiaSuRoi == 0 ? " " : (row.huoDongJiaSuRoi*100).toFixed(2)+'%' },
      { istrue: true, summaryEvent: true, prop: 'huoPinJiaSuRoi', label: '货品加速ROI', sortable: 'custom',tipmesg:'总成交金额/花费', width: '80', formatter: (row) => row.huoPinJiaSuRoi == 0 ? " " : (row.huoPinJiaSuRoi*100).toFixed(2)+'%' },
      { istrue: true, summaryEvent: true, prop: 'jingZhunRenQunTuiGuangRoi', label: '精准人群推广ROI', sortable: 'custom',tipmesg:'总成交金额/花费', width: '80', formatter: (row) => row.jingZhunRenQunTuiGuangRoi == 0 ? " " : (row.jingZhunRenQunTuiGuangRoi*100).toFixed(2) +'%'},
      { istrue: true, summaryEvent: true, prop: 'laXinKuaiRoi', label: '拉新快成交ROI', sortable: 'custom',tipmesg:'总成交金额/花费', width: '80', formatter: (row) => row.laXinKuaiRoi == 0 ? " " : (row.laXinKuaiRoi*100).toFixed(2)+'%' },
      { istrue: true, summaryEvent: true, prop: 'quanZhanTuiGuangRoi', label: '全站推广成交ROI', sortable: 'custom',tipmesg:'总成交金额/花费', width: '80', formatter: (row) => row.quanZhanTuiGuangRoi == 0 ? " " : (row.quanZhanTuiGuangRoi*100).toFixed(2) +'%'},
      { istrue: true, summaryEvent: true, prop: 'shangXinKuaiRoi', label: '上新快成交ROI', sortable: 'custom',tipmesg:'总成交金额/花费', width: '80', formatter: (row) => row.shangXinKuaiRoi == 0 ? " " : (row.shangXinKuaiRoi*100).toFixed(2)+'%' },
      { istrue: true, summaryEvent: true, prop: 'sumRoi', label: '总ROI', sortable: 'custom', width: '80', formatter: (row) => row.sumRoi == 0 ? " " : (row.sumRoi*100).toFixed(2) +'%'},
    ]
  },
  ];
  const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
  export default {
    name: 'GuoHuoWuJie_Tx',
    components: { MyContainer, cesTable, buschar ,inputYunhan},
    data() {
      return {
        uploadLoading: false,
        dialogVisible: false,
        sels: [],
        listLoading: false,
        that: this,//赋值
        list: [],//表格数据
        total: null,
        tableCols: tableCols,
        summaryarry: {},
        timerange: [],
        pager: { orderBy: 'YearMonthDay', isAsc: true },
        filter: {
        proCode:null,
        startTime: null,
        endTime: null,
        profit3UnZero :null,
        userIds: [],//运营助理
        operateSpecialUserIds: [],//运营专员
        groupIds: [],//小组
        profit3UnZero :null,
        profit4UnZero:null,
        platform:[],
        },
        pageLoading: false,
        grouplist:[],
        directorlist:[],
      };
    },
  
    async mounted() {
      //await this.init();
      await this.onSearch()
    },
    async created() {
    await this.getShopList();
  },
  
    methods: {
        async getShopList(){
        var res2= await getDirectorGroupList();
        this.grouplist = res2.data?.map(item => {return { value: item.key, label: item.value };});
        
        var res3= await getDirectorList();
        this.directorlist = res3.data?.map(item => {return { value: item.key, label: item.value };});
       
    },
        async callbackProCode(val) {
        this.filter.proCode = val;
  },
    datepickerfuc(e){
      middlevue.$emit('timerangeGC',e);
    },
   
      //导出
      async onExport() {
        this.filter.startTime =null;
      this.filter.endTime =null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
        this.sels = []
        var pager = this.$refs.pager.getPager();
        var page = this.pager;
        const params = {
          ...pager,
          ...page,
          ...this.filter
        }
        this.listLoading = true
        var res = await exportPurchaseNewPlanTurnDayList(params);
        if (res.success) {
          this.$message({ type: 'success', message: '导出成功!' });
        }
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '淘系国货无界' + new Date().toLocaleString() + '_.xlsx')
        aLink.click()
        this.listLoading = false
      },
      async onSearch() {
        this.$refs.pager.setPage(1)
        this.getlist();
      },
      async getlist() {
        this.filter.startTime =null;
      this.filter.endTime =null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
        this.sels = []
        this.listLoading = true
        var pager = this.$refs.pager.getPager();
        var page = this.pager;
        const params = {
          ...pager,
          ...page,
          ...this.filter
        }
        await this.querypopup(params)
        this.listLoading = false
      },
      async querypopup(params) {
        const { data, success } = await getPurchaseNewPlanTurnDayPageList(params)
        if (!success) {
          return
        }
        this.list = data.list
        this.total = data.total;
        this.summaryarry = data.summary;
      },
      // //排序查询
      async sortchange(column) {
        if (!column.order) {
          this.filter.orderBy = 'yearMonthDay';
          this.filter.isAsc = false;
        } else {
          this.filter.orderBy = column.prop
          this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false
          this.getlist();
        }
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .tableStyle {
    .el-table__body {
      .fixed-row{
          display: inline-block;
          position: sticky;
          bottom: 0;
          width: 100%;
          td{
            border: 1px solid #fff;
            box-shadow: 2px -2px 3px 0px #ddd;
          }
      }
    }
  }
  
  </style>
  