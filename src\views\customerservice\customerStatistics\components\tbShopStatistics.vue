<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <!-- <el-input v-model="ListInfo.shopName" placeholder="店铺名称" maxlength="50" clearable class="publicCss" /> -->
                <el-select v-model="ListInfo.shopName" filterable placeholder="店铺名称" style="width:200px;" clearable>
                    <el-option v-for="item in filterShopList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>

                <el-date-picker v-model="timeList" type="daterange" align="right" unlink-panels range-separator="至"
                    :clearable="false" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    @change="changeTime" class="publicCss" />
                <el-button type="primary" @click="getList('click')">查询</el-button>
            </div>
        </template>
        <vxetablebase :id="'tbShopStatistics202408041507'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
            :showsummary="true" :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            :summaryarry="summaryarry" @summaryClick='onsummaryClick' style="width: 100%; height: 690px; margin: 0"
            v-loading="listLoading" :noToFixed="true" />
        <my-pagination ref="pager" :total="total" @page-change="detailPagechange" @size-change="detailSizechange" />

        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false" direction="btt">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" @change="chatSearch" :picker-options="pickerOptions" style="margin: 10px;" />
                <buschar :analysisData="chatProp.data" v-if="!chatProp.chatLoading"></buschar>
            </div>
            <div v-else v-loading="chatProp.chatLoading"></div>
        </el-drawer>


        <el-dialog :title="dialogTitle1" :visible.sync="dialogVisable" width="65%" :close-on-click-modal="false"
            v-dialogDrag>
            <el-date-picker v-model="logTimeList" type="daterange" align="right" unlink-panels range-separator="至"
                :clearable="false" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                @change="changeLogTime" class="publicCss" style="margin-bottom: 20px;" />
            <vxetablebase :id="'tbShopStatistics202408041507_2'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
                @sortchange='sortchange1' :showsummary="true" :tableData='dialogData' :tableCols='dialogTableCols'
                :isSelection="false" :isSelectColumn="false" :summaryarry="dialogInfo.summaryarry"
                style="width: 100%; height: 550px; margin: 0" />
            <my-pagination ref="pager" :total="dialogInfo.total" @page-change="dialogPagechange"
                @size-change="dialogSizechange" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import buschar from '@/components/Bus/buschar'
import { getTaoBaoInquireGradeComputePageList_Shop, getTaoBaoInquireGradeComputeChat_Shop, getTaoBaoInquireGradeComputePageList_Shop_User,getTaoXiShop } from '@/api/customerservice/inquirs'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
const tableCols = [
    { istrue: true, prop: 'shopName', label: '店铺名称', sortable: 'custom', type: 'click', handle: (that, row) => that.openShopDialog(row, 'first') },
    { istrue: true, prop: 'receivecount', label: '接待人数', sortable: 'custom' },
    { istrue: true, prop: 'successpayrate', label: '最终付款成功率', sortable: 'custom', formatter: (row) => row.successpayrate + '%' },
    { istrue: true, prop: 'salesvol', label: '销售额', sortable: 'custom' },
    { istrue: true, prop: 'gradeCommission', label: '提成', sortable: 'custom' },
    {
        istrue: true, prop: 'buchar', type: "button", width: '100', label: '趋势图', fixed: 'right', summaryEvent: true, btnList: [
            { label: "查看", handle: (that, row) => that.openChat(row) },
        ]
    }
]

const dialogTableCols = [
    // { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', sortable: 'custom' },
    { istrue: true, prop: 'snameLevel', label: '客服等级', sortable: 'custom' },
    { istrue: true, prop: 'receivecount', label: '接待人数', sortable: 'custom' },
    { istrue: true, prop: 'successpayrate', label: '最终付款成功率', sortable: 'custom', formatter: (row) => row.successpayrate + '%' },
    { istrue: true, prop: 'salesvol', label: '销售额', sortable: 'custom' },
    { istrue: true, prop: 'gradeCommission', label: '提成', sortable: 'custom' },
    { istrue: true, prop: 'gradeWayValue', label: '提成系数', sortable: 'custom', },
    { istrue: true, prop: 'gradeIndex', label: '排名', sortable: 'custom' },
    { istrue: true, prop: 'effectiveDate', label: '日期', sortable: 'custom', formatter: (row) => dayjs(row.effectiveDate).format('YYYY-MM-DD') },
]
export default {
    name: 'tbShopStatistics',
    components: {
        MyContainer, vxetablebase, buschar
    },
    data() {
        return {
            listLoading: true,
            tableData: [],//列表数据
            dialogData: [],//弹窗数据
            tableCols,
            dialogTableCols,
            that: this,
            total: 0,
            ListInfo: {//列表参数
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: 'successpayrate',//排序字段
                isAsc: false,//是否升序
                startDate: null,//开始时间
                endDate: null,//结束时间
                shopCode: null,//店铺编码
                shopCodeList: null,//店铺编码集合
                shopName: null,//店铺名称
                shopNameList: null,//店铺名称集合
                groupName: null,//组名称
                groupNameList: null,//组名称集合
                groupShortName: null,//分组归属
                snick: null,//昵称
                snickList: null,//昵称集合
                sname: null,//姓名
                snameList: null,//姓名集合
            },
            filterShopList:[],
            pickerOptions,
            timeList: null,//时间范围,
            logTimeList: null,//时间范围,
            summaryarry: null,
            chatProp: {
                chatDialog: false,//趋势图弹窗
                chatTime: null,//趋势图时间
                chatLoading: true,//趋势图loading
                data: [],//趋势图数据
            },
            chatInfo: {
                groupName: null,//组别
                sname: null,//姓名
                shopName: null,//店铺
                snick: null,//昵称
                startDate: null,//开始时间
                endDate: null,//结束时间
            },
            dialogTitle1: "",
            dialogVisable: false,//弹窗
            dialogInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                shopCode: null,
                summaryarry: null,
                total: 0,
                startDate: null,//开始时间
                endDate: null,//结束时间
            }
        };
    },
    mounted() {
        this.getTaoXiShop();
        this.getList()
    },
    methods: {
        async getTaoXiShop() {
            let shops = await getTaoXiShop({ groupType: 0, isGrade: "已参与计算绩效" });
            console.log(shops, "shops");
            if (shops?.success && shops?.data && shops?.data.length > 0) {
                shops?.data.forEach(f => {
                    this.filterShopList.push({ lable: f, value: f });
                });
            }
        },
        async changeLogTime(e) {
            this.logTimeList = e
            if (e) {
                this.dialogInfo.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                this.dialogInfo.endDate = dayjs(e[1]).format('YYYY-MM-DD')
            } else {
                this.dialogInfo.startDate = null
                this.dialogInfo.endDate = null
            }
            const { data, success } = await getTaoBaoInquireGradeComputePageList_Shop_User(this.dialogInfo)
            if (success) {
                this.dialogData = data.list
                this.dialogVisable = true
                this.dialogInfo.summaryarry = data.summary
                this.dialogInfo.total = data.total
            }
        },
        async openShopDialog(row, type) {
            this.dialogTitle1 = "店铺绩效按人统计-" + row.shopName;
            if (type == 'first') {
                this.dialogInfo.currentPage = 1
                this.dialogInfo.pageSize = 50
                this.logTimeList = this.timeList
            }
            this.dialogInfo.startDate = dayjs(this.logTimeList[0]).format('YYYY-MM-DD')
            this.dialogInfo.endDate = dayjs(this.logTimeList[1]).format('YYYY-MM-DD')
            this.dialogInfo.shopCode = row.shopCode
            this.dialogInfo.shopName = row.shopName
            const { data, success } = await getTaoBaoInquireGradeComputePageList_Shop_User(this.dialogInfo)
            if (success) {
                this.dialogData = data.list
                this.dialogVisable = true
                this.dialogInfo.summaryarry = data.summary
                this.dialogInfo.total = data.total
            }
        },
        //趋势图时间改变
        async chatSearch() {
            this.chatInfo.startDate = dayjs(this.chatProp.chatTime[0]).format('YYYY-MM-DD')
            this.chatInfo.endDate = dayjs(this.chatProp.chatTime[1]).format('YYYY-MM-DD')
            this.chatProp.chatLoading = true
            const data = await getTaoBaoInquireGradeComputeChat_Shop(this.chatInfo)
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        clearInfo() {
            this.chatInfo = {
                groupName: null,//组别
                sname: null,//姓名
                shopName: null,//店铺
                snick: null,//昵称
                startDate: null,//开始时间
                endDate: null,//结束时间
            }
        },
        async onsummaryClick(property) {
            this.publicChangeTime(this.chatInfo)
            this.chatInfo.groupName = null
            this.chatInfo.sname = null
            this.chatInfo.shopName = null
            this.chatInfo.snick = null
            this.chatProp.chatLoading = true
            if (property == 'buchar') {
                const data = await getTaoBaoInquireGradeComputeChat_Shop(this.chatInfo)
                this.chatProp.data = data
                this.chatProp.chatDialog = true
                this.chatProp.chatLoading = false
            }
        },
        publicChangeTime(row) {
            if (this.timeList) {
                let time = dayjs(this.timeList[1]).diff(dayjs(this.timeList[0]), 'day')
                if (time >= 30) {
                    this.chatProp.chatTime = this.timeList
                    this.chatInfo.startDate = dayjs(this.timeList[0]).format('YYYY-MM-DD')
                    this.chatInfo.endDate = dayjs(this.timeList[1]).format('YYYY-MM-DD')
                } else {
                    //否则就从timeList的结束时间往前推30天
                    this.chatProp.chatTime = [dayjs(this.timeList[1]).subtract(30, 'day').format('YYYY-MM-DD'), this.timeList[1]]
                    this.chatInfo.startDate = dayjs(this.timeList[1]).subtract(30, 'day').format('YYYY-MM-DD')
                    this.chatInfo.endDate = dayjs(this.timeList[1]).format('YYYY-MM-DD')
                }
            }
            this.chatInfo.shopName = row.shopName ? row.shopName : this.ListInfo.shopName
        },
        async openChat(row) {
            this.clearInfo()
            //如果this.timeList的时间间隔大于等于三十天,那么就提示时间间隔不能大于三十天
            this.publicChangeTime(row)
            this.chatProp.chatLoading = true
            const data = await getTaoBaoInquireGradeComputeChat_Shop(this.chatInfo)
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        //时间范围改变
        changeTime(e) {
            if (e) {
                this.ListInfo.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs(e[1]).format('YYYY-MM-DD')
            } else {
                this.ListInfo.startDate = null
                this.ListInfo.endDate = null
            }
            this.getList()
        },
        async getList(type) {
            this.listLoading = true
            if (type == 'click') {
                this.ListInfo.currentPage = 1
                this.ListInfo.orderBy = 'successpayrate'
                this.ListInfo.isAsc = false
            }
            //如果有shopName就清除空格
            if (this.ListInfo.shopName) {
                this.ListInfo.shopName = this.ListInfo.shopName ? this.ListInfo.shopName.replace(/\s+/g, "") : null;
            }
            if (!this.timeList) {
                //默认时间为当前时间往前推一个月
                this.ListInfo.startDate = dayjs().subtract(1, 'month').format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
                this.timeList = [this.ListInfo.startDate, this.ListInfo.endDate]
            }
            const { data, success } = await getTaoBaoInquireGradeComputePageList_Shop(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
                this.listLoading = false
            }
        },
        //页面数量改变
        detailSizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList();
        },
        //当前页改变
        detailPagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },
        //页面数量改变
        dialogSizechange(val) {
            this.dialogInfo.currentPage = 1;
            this.dialogInfo.pageSize = val;
            this.openShopDialog(this.dialogInfo);
        },
        //当前页改变
        dialogPagechange(val) {
            this.dialogInfo.currentPage = val;
            this.openShopDialog(this.dialogInfo);
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        sortchange1({ order, prop }) {
            if (prop) {
                this.dialogInfo.orderBy = prop
                this.dialogInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.openShopDialog(this.dialogInfo)
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.top {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.publicCss {
    width: 220px;
    margin-right: 20px;
}
</style>