<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
            <el-button style="padding: 0;margin: 0; border: 0;">
                <el-select v-model="filter.platform" placeholder="平台" style="width: 150px" clearable
                    @change="onSearch()">
                    <!-- <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" /> -->
                    <el-option label="拼多多" :value="2" />
                </el-select>
            </el-button>

            <el-button type="primary" @click="onSearch()">查询</el-button>
        </template>

        <el-table :data="tableData" border ref="table">
            <el-table-column label="序号" width="50">
                <template slot-scope="scope">{{ scope.$index + 1 }}</template>
            </el-table-column>

            <el-table-column prop="itemKey" label="名称" width="230">
                <template slot-scope="scope">
                    <el-select v-model="scope.row.itemKey" clearable style="width: 200px;" disabled>
                        <el-option label="毛一利率" value="yyProfit1Rate" key="itemKeyyyProfit1Rate"></el-option>
                        <el-option label="毛二利率" value="yyProfit2Rate" key="itemKeyyyProfit2Rate"></el-option>
                        <el-option label="毛三利率" value="yyProfit3Rate" key="itemKeyyyProfit3Rate"></el-option>
                        <el-option label="毛四利率" value="yyProfit4Rate" key="itemKeyyyProfit4Rate"></el-option>
                        <el-option label="毛一利率(减退款)" value="yyProfit1AfterRate"
                            key="itemKeyyyProfit1AfterRate"></el-option>
                        <el-option label="毛二利率(减退款)" value="yyProfit2AfterRate"
                            key="itemKeyyyProfit2AfterRate"></el-option>
                        <el-option label="毛三利率(减退款)" value="yyProfit3AfterRate"
                            key="itemKeyyyProfit3AfterRate"></el-option>
                        <el-option label="毛四利率(减退款)" value="yyProfit4AfterRate"
                            key="itemKeyyyProfit4AfterRate"></el-option>
                        <el-option label="净利率" value="Profit4Rate" key="itemKeyProfit4Rate"></el-option>
                    </el-select>
                </template>
            </el-table-column>

            <el-table-column prop="itemSymbol" label="符号" width="230">
                <template slot-scope="scope">
                    <el-select v-model="scope.row.itemSymbol" clearable style="width: 200px;">
                        <el-option label="低于(x<最小值)" value="<" key="itemSymbol1"></el-option>
                        <el-option label="等于(x=最小值)" value="=" key="itemSymbol2"></el-option>
                        <el-option label="高于(x>=最小值)" value=">=" key="itemSymbol3"></el-option>
                        <el-option label="介于(x>=最小值 & x<=最大值)" value=">=&<=" key="itemSymbol4"></el-option>
                    </el-select>
                </template>
            </el-table-column>

            <el-table-column prop="itemMinValue" label="最小值" width="170">
                <template slot-scope="scope">
                    <el-input-number type="number" :precision="2" :min="-99999999" :max="99999999" :controls="false"
                        v-model.number="scope.row.itemMinValue">
                    </el-input-number>{{ scope.row.itemUnit }}
                </template>
            </el-table-column>

            <el-table-column prop="itemMaxValue" label="最大值" width="170">
                <template slot-scope="scope">
                    <el-input-number type="number" :precision="2" :min="-99999999" :max="99999999" :controls="false"
                        v-model.number="scope.row.itemMaxValue">
                    </el-input-number>{{ scope.row.itemUnit }}
                </template>
            </el-table-column>

            <el-table-column prop="createdUserName" label="操作人" width='80'></el-table-column>
            <el-table-column prop="createdTime" label="操作时间" width='150'></el-table-column>
        </el-table>

        <template #footer>
            <span style="font-size: 12px; color:chocolate;">改了指标值时请先点击保存，再点击生成方案，方案默认以前天作为参考日期，今日作为观察起始日期，默认观察7天。</span>
            <el-button type="primary" @click="onSave()">保存</el-button>
            <el-button type="primary" @click="onCreatePlan()">生成方案</el-button>
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { platformlist } from '@/utils/tools';
import { GetProcodeProcessPlanProAutoPlanSet, SaveProcodeProcessPlanProAutoPlanSet, AutoCreateProcodeProcessPlan } from '@/api/bookkeeper/procodeprocessplan'
export default {
    name: "procodeprocessautoplanset",
    components: {
        MyContainer,
    },
    data() {
        return {
            that: this,
            pageLoading: false,
            listLoading: false,
            platformlist: platformlist,
            filter: {

            },
            editConfig: {
                trigger: 'dblclick',
                mode: 'cell'
            },
            tableData: [],
        }
    },
    async mounted() {

    },
    computed: {

    },
    methods: {
        async loadData(param) {

        },
        async onSearch() {
            if (!this.filter.platform) {
                this.$message.error('请选择平台')
                return;
            }
            this.pageLoading = true
            const res = await GetProcodeProcessPlanProAutoPlanSet(this.filter);
            this.pageLoading = false
            if (res?.success) {
                this.tableData = res.data;
            }
        },
        async onSave() {
            this.pageLoading = true
            const res = await SaveProcodeProcessPlanProAutoPlanSet(this.tableData);
            this.pageLoading = false
            if (res?.success) {
                this.$message.success('保存成功')
            }
        },
        async onCreatePlan() {
            if (!this.filter.platform) {
                this.$message.error('请选择平台')
                return;
            }
            this.pageLoading = true
            const res = await AutoCreateProcodeProcessPlan({ ymd: '', platform: this.filter.platform });
            this.pageLoading = false
            if (res?.success) {
                this.$message.success('生成成功')
                this.$emit('afterSave');
                this.$emit('close');
            }
        },

    }
}
</script>

<style scoped lang="scss"></style>
