<template>
  <container v-loading="pageLoading">
    <template #header>
     <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
      <el-form-item label="运营组:">
        <el-select v-model="filter.groupId" multiple collapse-tags clearable placeholder="请选择运营组" style="width: 100%">
            <el-option label="所有" value=""/>
            <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="采购员:">
        <el-select v-model="filter.brandId" multiple collapse-tags clearable placeholder="请选择采购员" style="width: 300px">
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="是否已完结:">
          <el-select v-model="filter.isHandle" disabled placeholder="请选择">
            <el-option label="所有" value></el-option>
            <el-option label="是" value='true'></el-option>
            <el-option label="否" value="false"></el-option>
          </el-select>
       </el-form-item>
       <el-form-item label="审单状态">
          <el-select v-model="filter.isCheckError" placeholder="请选择">
            <el-option label="所有" value></el-option>
            <el-option label="缺货" value='0'></el-option>
            <el-option label="审错" value='1'></el-option>
            <el-option label="正常" value=2></el-option>
          </el-select>
       </el-form-item>
      <el-form-item label="商品编码:">
         <el-input v-model="filter.goodsCode" />
      </el-form-item>
      <el-form-item label="商品编码名称:">
        <el-input v-model="filter.goodsName" />
      </el-form-item>
       <el-form-item label="原因:">
        <el-input v-model="filter.reason" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
      </el-form-item>
      </el-form>
    </template>
     <ces-table ref="table" :that='that' :isIndex='true' @sortchange='sortchange' @select='selectchange' @cellclick='cellclick' 
         :hasexpand='true' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"
         :showsummary='true' :summaryarry='summaryarry'>
        <template slot='extentbtn'>
          <el-button-group>
            <el-button style="margin: 0;">
              {{lastUpdateTime}}
            </el-button>
            <el-button style="margin: 0;">
              {{reasonRate}}
            </el-button>
          </el-button-group>
        </template>
     </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

     <el-dialog :visible.sync="visiblepopover" v-dialogDrag>
       <goodscoderecord ref="goodscoderecord" :filter="goodscoderecordfilter" style="height: 400px"></goodscoderecord> 
    </el-dialog>

    <el-popover ref="detailPopover" placement="bottom-end" v-model="visiblepopoverdetail" :reference="prevTarget" :key="('detail'+popperFlagdetail.toString())">
      <el-table :data="detaillist">
        <el-table-column width="150" property="firstOrderTime" label="压单日期"></el-table-column>
        <el-table-column width="80" property="warehouseAreaName" label="仓储"></el-table-column>
        <el-table-column width="120" property="goodsCode" label="商品编码"></el-table-column>
        <el-table-column width="75" property="isCheckError" label="审单状态">
          <template slot-scope="scope">
             <div class="wendang" v-html="formatIsCheckError(scope.row['isCheckError'])"></div>
          </template>
        </el-table-column>
        <el-table-column label="压单数" width="65" property="waitOrderNum">
          <template slot-scope="scope">
            <el-button @click="showOrderDetail(scope.row['id'])" type="text" size="small">{{scope.row["waitOrderNum"]}}</el-button>
          </template>
        </el-table-column>
        <el-table-column width="70" property="waitGoodNum" label="压品数"></el-table-column>
        <el-table-column width="100" property="totalWaitOrderNum" label="累计压单数"></el-table-column>
        <el-table-column width="100" property="totalWaitGoodNum" label="累计压品数"></el-table-column>
        <el-table-column width="80" property="waitDays" label="压单天数"></el-table-column>
        <el-table-column width="80" property="ninetyDaysNum" label="90天次数"></el-table-column>
      </el-table>
    </el-popover>
    
    <el-dialog :visible.sync="dialogOrderDetailVisible">
       <el-card style="padding:1px;">
         <div v-for="(item,index) in oderDetailView.procodeViews" :key="index">
             <el-descriptions :column="6" size="mini">
                  <el-descriptions-item label="平台">{{formatPlatform(item.platform)}}</el-descriptions-item>
                  <el-descriptions-item label="产品ID" :span="2">
                      <el-link type="primary" :href="`${(item.platform==1?' https://detail.tmall.com/item.htm?id':item.platform==2?'https://mobile.yangkeduo.com/goods2.html?goods_id':'')}=${item.proCode}`" target="_blank">{{item.proCode}}</el-link>
                    </el-descriptions-item>
                  <el-descriptions-item label="压单天数">{{item.waitDays}}</el-descriptions-item>
                  <el-descriptions-item label="数量">{{item.number}}</el-descriptions-item>                  
                  <el-descriptions-item label="运营小组">{{item.grouper}}</el-descriptions-item>
            </el-descriptions>
         </div>
        <el-button type="success" size="mini" @click="doCopy(oderDetailView.orderNoInners)">一键复制所有订单号</el-button>
       </el-card>
      <el-table :data="oderDetailView.details">
        <el-table-column width="50" type="index" label="#" align="center"></el-table-column>
        <el-table-column width="150" property="payTime" label="支付日期"></el-table-column>
        <el-table-column width="100" property="orderNoInner" label="内部订单号"></el-table-column>
        <el-table-column width="110" property="goodsCode" label="商品编码"></el-table-column>
        <el-table-column width="70" property="qty" label="压品数"></el-table-column>
        <el-table-column width="70" property="platform" label="平台">
          <template slot-scope="scope" >
             <span>{{formatPlatform(scope.row['platform'])}}</span>
          </template>
        </el-table-column>
        <el-table-column width="190" property="shopName" label="店铺"></el-table-column>
        <el-table-column width="150" property="proCode" label="商品ID"></el-table-column>
      </el-table>
    </el-dialog>

    <div class="imgDolg" v-show="imgPreview.show" @click.stop="imgPreview.show = false">
      <i class="el-icon-close" id="imgDolgClose" @click.stop="imgPreview.show = false"></i>
      <img @click.stop="imgPreview.show = true" :src="imgPreview.img" />
    </div>
  </container>
</template>
<script>

import {pageAbnormalInventory,queryAbnormalInventoryDetail} from '@/api/storehouse/storehouse'
import {getLastUpdateTime,getAllAbnormalCheckErrorOderNo,getAllAbnormalCheckErrorGoodsCode,getAllAbnormalReasonRate,
        queryAbnormalInventory,queryAbnormalOderDetail,exportAbnormalInventory} from '@/api/inventory/abnormal'
import {getAllProBrand} from '@/api/inventory/warehouse'
import {getDirectorGroupList} from '@/api/operatemanage/base/shop'
import {formatTime,formatYesornoBool,formatPlatform,formatNoLink,formatIsCheckError} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import goodscoderecord from '@/views/inventory/goodscoderecord'
import { throttle } from 'throttle-debounce';
 
const tableCols =[
      //{istrue:true,prop:'firstOrderTime',label:'首次压单日期',  width:'110',sortable:'custom',formatter:(row)=>formatTime(row.firstOrderTime,'MM-DD HH:mm:ss')}, `color:${(!row.isReportToday?"red":"green")}`},
      {istrue:true,prop:'firstOrderTime',label:'压单日期',  width:'80',sortable:'custom',formatter:(row)=>formatTime(row.firstOrderTimeCurrent,'MM-DD HH:mm:ss')},
      {istrue:true,prop:'goodsImage',label:'图片', width:'60',type:'image'},
      {istrue:true,prop:'isHandle',label:'状态', width:'60',type:'html',formatter:(row)=>{
        return `<a href="javascript:void(0);"  style="font-weight:bold;font-size:small;color:${(row.isHandle==true?"green":"red")};">${(row.isHandle==true?"已完结":"未完结")}</a>`;
      }},
      {istrue:true,prop:'handleTime',label:'完结时间',  width:'105',sortable:'custom',formatter:(row)=>formatTime(row.handleTime,'MM-DD HH:mm:ss')},
      {istrue:true,prop:'goodsCode',label:'商品编码', width:'110',sortable:'custom'},
      {istrue:true,prop:'goodsName',label:'商品名称', width:'160',sortable:'custom'},
      {istrue:true,prop:'isCheckError',label:'审单状态', width:'50',type:'html',formatter:(row)=>formatIsCheckError(row.isCheckError)},
      {istrue:true,prop:'waitOrderNumCurrent',label:'压单数', width:'70',sortable:'custom',type:'html',formatter:(row)=>formatNoLink(row.waitOrderNumCurrent)},
      {istrue:true,prop:'waitGoodNumCurrent',label:'压品数', width:'70',sortable:'custom'},
      {istrue:true,prop:'waitDaysc',label:'压单天数', width:'50'},
      {istrue:true,prop:'ninetyDaysNum',label:'90天次数', width:'80',sortable:'custom'},
      {istrue:true,prop:'buyPerson',label:'采购人', width:'60'},
      {istrue:true,prop:'buyNo',label:'采购单号', width:'80',type:'html',formatter:(row)=>formatNoLink(row.buyNo)},
      {istrue:true,prop:'planArrivalTime',label:'预计到货日期', width:'110',sortable:'custom',formatter:(row)=>formatTime(row.planArrivalTime,'YYYY-MM-DD')},
      {istrue:true,prop:'reason',label:'原因', width:'75'},
      {istrue:true,prop:'remark',label:'解决方案',type:'editor'},
     ];
const tableHandles=[{label:"导出", handle:(that)=>that.onExport()},
   {label:"复制所有审单异常订单号", handle:(that)=>that.copyAllAbnormalOderNo()},{label:"复制所有审单异常商品编码", handle:(that)=>that.copyAllAbnormalGoodsCode()}];
export default {
  name: "Users",
  components: { container, cesTable ,MyConfirmButton,goodscoderecord},
  data() {
    return {
      that:this,
      formatPlatform:formatPlatform,
      formatTime:formatTime,
      formatIsCheckError:formatIsCheckError,
      filter: {
        timerange:null,
        startFirstOrderTime:null, 
        endFirstOrderTime:null, 
        goodsName:null, 
        goodsCode:null, 
        brandId:null,
        groupId:null,
        warehouseArea:null,
        reason:null,
        isHandle:'false',
        isCheckError:null,
      },
      goodscoderecordfilter:{goodsCode:"",buyNo:""},
      imgPreview:{img:"",show:false},
      lastUpdateTime:'',
      reasonRate:'',
      brandlist:[],
      grouplist:[],
      list: [],
      recodelist: [],
      detaillist:[],
      oderDetailView:{},
      visiblepopover: false,
      prevTarget: null, // 编辑 Popover 的 Reference （参照），用于 popover.js 对齐两个元素
      popperFlag: false, // 用于编辑 Popover 的刷新
      visiblepopoverdetail: false,
      dialogOrderDetailVisible:false,
      popperFlagdetail: false,
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [],
      fileList:[],
      listLoading: false,
      dialogVisible: false,
      pageLoading: false,
      editVisible:false,
      editLoading:false,
      uploadLoading:false,
    };
  },
  watch: {
    value(n) {
      if(n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    }
  },
 async mounted() {
    await this.init();
    await this.onSearch();
  },
 methods: {
   async removeEditPopoverListener(flag) {  // 监听滚动，用于编辑框的滚动移除
      let timer = setTimeout(() => {
        let scrollElement = this.$refs.table.$el.querySelector('.el-table__body-wrapper');
        console.log('监听滚动，用于编辑框的滚动移除', flag, scrollElement);
        let scrollHandle = () => {
          console.log('执行--->', this.visibleEditOpinions);
          if (this.visibleEditOpinions) {
            this.clearEditPopperComponent();
          }
        }
        if (flag) {
          scrollElement.addEventListener('scroll', throttle(500, scrollHandle));
        } else {
          scrollElement.removeEventListener('scroll', scrollHandle);
        }
        clearTimeout(timer);
      }, 0);
    },
     // 复选框选中的数据
   async changeSelection(row) {
      this.selectData = row;
      console.log('复选框选中的数据', this.selectData);
      this.seqs = this.selectData.map((el) => { return el.seq; }).toString();
      console.log('seqs---->', this.seqs);
    },
  // 清空编辑组件
  async clearEditPopperComponent() {
      this.prevTarget = null;
      this.popperFlag = !this.popperFlag;
      this.popperFlagdetail= !this.popperFlagdetail;
      this.visiblepopover = false;
      this.visiblepopoverdetail= false;
    },
  async init(){
        var res1= await getDirectorGroupList();
        this.grouplist = res1.data.map(item => {
            return { value: item.key, label: item.value };
        }); 

        var res2= await getAllProBrand();
        this.brandlist = res2.data.map(item => {
            return { value: item.key, label: item.value };
        });

        var res3= await getLastUpdateTime();
        this.lastUpdateTime="最晚更新时间"+res3.data
    },
   async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
   async getlist() {
      if (!this.pager.OrderBy) this.pager.OrderBy="";
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      if (params.timerange) {
        params.startFirstOrderTime = params.timerange[0];
        params.endFirstOrderTime = params.timerange[1];
      }
      console.log('params',params)
      this.listLoading = true
      const res = await pageAbnormalInventory(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {d._loading = false})
      this.list = data
      this.summaryarry=res.data.summary;

      var res2= await  getAllAbnormalReasonRate(params);
      this.reasonRate =res2.data;
    },
   async cellclick(row, column, cell, event){
  //    if (column.property=='buyNo') {
  //      if (row.buyNo)
  //        this.$router.push({path: '/inventory/purchaseindex', query: {buyNo: row.buyNo}})
  //  }
     if (column.property=='planArrivalTime'
         ||column.property=='reason'
         ||column.property=='solution'
         ||column.property=='remark') {
      await this.getrecordlist(row.goodsCode)
        this.visiblepopover = true;
     }
     else if (column.property=='goodsCode'
         ||column.property=='goodsName'
         ||column.property=='waitOrderNumCurrent'
         ||column.property=='waitGoodNumCurrent'
         ||column.property=='waitDaysc'
         ||column.property=='ninetyDaysNum'
         ) {
      await this.getdetaillist(row.id)
      if (event.stopPropagation) {
        event.stopPropagation();
      } else if (window.event) {
        window.event.cancelBubble = true;
      }
      let currentTarget = event.target;
      this.editData = row;
      if (this.prevTarget === currentTarget) {
        this.visiblepopoverdetail = !this.visiblepopoverdetail;
      } else {
        if (this.prevTarget) {
          this.clearEditPopperComponent();
          this.$nextTick(() => {
            this.prevTarget = currentTarget;
            this.visiblepopoverdetail = true;
          });
        } else {
          this.prevTarget = currentTarget;
          this.visiblepopoverdetail = true;
        }
      }
     }
    },
   async showOrderDetail(parentid){
     this.dialogOrderDetailVisible=true;
     await this.getorderdetaillist(parentid);
   },
   async getrecordlist(goodscode){
       this.$nextTick(() => {
           this.$refs.goodscoderecord.onSearch(goodscode,'');
        });
    },
   async getdetaillist(parentid){
       this.detaillist=[];
       const res = await queryAbnormalInventoryDetail({parentid:parentid})
       if (!(res.code==1&&res.data)) return 
       this.detaillist=res.data;
    },
   async getorderdetaillist(parentid){
       this.oderDetailView={};
       const res = await queryAbnormalOderDetail({parentid:parentid})
       if (!(res.code==1&&res.data)) return 
       this.oderDetailView=res.data;
    },
   async copyAllAbnormalOderNo(){
      var res = await getAllAbnormalCheckErrorOderNo();
      if (!res.data) {
        this.$message({ message: "没有获取到订单号", type: "warning" });
        return;
      }
      this.doCopy(res.data)
   },
  async copyAllAbnormalGoodsCode(){
      var res = await getAllAbnormalCheckErrorGoodsCode();
      if (!res.data) {
        this.$message({ message: "没有获取到商品编码", type: "warning" });
        return;
      }
      this.doCopy(res.data)
   },
   async onHand(row){
      this.formtitle='编辑';
      this.editVisible = true
      const res = await queryAbnormalInventory({id:row.id})
      if (res.data.reason)
        res.data._reason=res.data.reason.split('-')
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields()
      await this.autoform.fApi.setValue(res.data)
    },
   onDisPlay(row){
     return row.isHandle==true;
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   selsChange: function(sels) {
      this.sels = sels
    },
   selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.proBianMa);
      })
    },
   doCopy: function (val) {       
      let that=this;                         
      this.$copyText(val).then(function (e) {
          that.$message({ message: "内容已复制到剪切板！", type: "success" });
      }, function (e) {
          that.$message({ message: "抱歉，复制失败！", type: "warning" });
      })
    },
    // 图片点击放大
    showImg(e) {
      // console.log(e.target)
      if (e.target.tagName == 'IMG') {
        this.imgPreview.img = e.target.src
        this.imgPreview.show = true
      }
    },
    async onExport() {
      if (!this.pager.OrderBy) this.pager.OrderBy="id";
      const params = {...this.pager,... this.filter}       
      var res= await exportAbnormalInventory(params);
      if(!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download','异常订单数据_' + new Date().toLocaleString() + '.xlsx' )
      aLink.click()
     }
  },
};
</script>
<style lang="scss" scoped>

.imgDolg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 9999;
  background-color: rgba(140, 134, 134, 0.6);
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  #imgDolgClose {
    position: fixed;
    top: 35px;
    cursor: pointer;
    right: 7%;
    font-size: 50px;
    color: white;
  }
  img{
    width: 80%;
  }
}


</style>

