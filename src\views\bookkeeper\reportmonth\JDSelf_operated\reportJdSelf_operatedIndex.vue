<template>
  <MyContainer>
    <el-tabs v-model="activeName" style="height: 95%">
      <el-tab-pane label="京东自营月报" name="tab13" :lazy="true" style="height: 100%;">
        <JdSelfMonthlyReport ref="refJdSelfMonthlyReport" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane label="京东仓自营入仓出库同步数据" name="tab12" :lazy="true" style="height: 100%;">
        <JdSelfWarehouseOutSync ref="refJdSelfWarehouseOutSync" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane label="商品编码期初期末" name="tab61" :lazy="true" style="height: 100%;">
        <jdSelfSkuPriceMonthRpt ref="refjdSelfSkuPriceMonthRpt" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane label="月账单原始数据" name="first1" style="height: 100%">
        <monthlyBillOriginalData />
      </el-tab-pane>
      <el-tab-pane label="月出库原始数据" name="first2" style="height: 100%" lazy>
        <monthlyOutboundRawData />
      </el-tab-pane>
      <el-tab-pane label="月入仓成本原始数据" name="first3" style="height: 100%" lazy>
        <monthlyWarehousingCostRawData />
      </el-tab-pane>
    </el-tabs>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import monthlyBillOriginalData from "./components/monthlyBillOriginalData.vue";
import monthlyOutboundRawData from "./components/monthlyOutboundRawData.vue";
import monthlyWarehousingCostRawData from "./components/monthlyWarehousingCostRawData.vue";
import JdSelfWarehouseOutSync from "../../../financial/yyfydayreport/operatingExpensesJd/JdSelfWarehouseOutSync.vue";
import JdSelfMonthlyReport from "../reportjdSelf.vue";
import jdSelfSkuPriceMonthRpt from "./components/jdSelfSkuPriceMonthRpt.vue";
export default {
  name: 'reportJdSelf_operatedIndex',
  components: {
    MyContainer, monthlyBillOriginalData, monthlyOutboundRawData, monthlyWarehousingCostRawData, JdSelfWarehouseOutSync, JdSelfMonthlyReport, jdSelfSkuPriceMonthRpt
  },
  data() {
    return {
      that: this,
      activeName: "tab13",
    };
  },
};
</script>

<style lang="scss" scoped></style>
