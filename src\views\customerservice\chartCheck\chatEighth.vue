<template>
    <my-container>
        <!--顶部操作-->
        <div class=".top">
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                <el-form-item>
                    <el-switch v-model="Filter.switchshow" inactive-text="售前数据" active-text="售后数据"
                        @change="changeShowgroup">
                    </el-switch>
                </el-form-item>

                <el-form-item label="数据维度:">
                    <el-select v-model="Filter.dataType" placeholder="请选择" class="el-select-content">
                        <el-option v-for="item in dataTypeList" :key="item.value" :label="item.name"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="平台:">
                    <el-select v-model="Filter.platform" placeholder="请选择" class="el-select-content" clearable
                        @change="changePlatform">
                        <el-option v-for="item in platformTypeList" :key="item.value" :label="item.name"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="售后时间:" v-if="Filter.switchshow">
                    <el-date-picker style="width: 320px" v-model="Filter.timerange" type="datetimerange"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" :picker-options="pickerOptions"
                        :default-value="defaultDate"></el-date-picker>
                </el-form-item>

                <el-form-item label="审核时间:">
                    <el-date-picker style="width: 320px" v-model="Filter.auditTime" type="datetimerange"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" :picker-options="pickerOptions"
                        :default-value="defaultDate"></el-date-picker>
                </el-form-item>

                <el-form-item label="下单时间:">
                    <el-date-picker style="width: 320px" v-model="Filter.roderTime" type="datetimerange"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" :picker-options="pickerOptions"
                        :default-value="defaultDate"></el-date-picker>
                </el-form-item>

                <el-form-item label="分组:">
                    <el-select v-model="Filter.groupNameList" placeholder="请选择" class="el-select-content" clearable
                        filterable multiple collapse-tags @focus="changPlatformState">
                        <el-option v-for="item in groupNameLists" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>

                <el-form-item label="姓名:">
                    <el-select v-model="Filter.userNameList" placeholder="请选择" class="el-select-content" clearable
                        filterable multiple collapse-tags @focus="changPlatformState">
                        <el-option v-for="item in userNameLists" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport()"
                        v-if="this.Filter.switchshow ? checkPermission(['api:customerservice:UnPayOrder:UnPayOrderAuditStatisticsExport1']) : checkPermission(['api:customerservice:UnPayOrder:UnPayOrderAuditStatisticsExport0'])">导出</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 列表 -->
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="true" :loading="listLoading" :summaryarry='summaryarry' :showsummary='true'
            style="width: 100%; height: 88%; margin: 0">
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getPageList"  :sizes="[500, 1000, 2000, 5000]" :page-size="500"/>
        </template>

        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="false" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="历史" v-if="viewChatDialogVisible" :visible.sync="viewChatDialogVisible" width="50%"
            height="600px" v-dialogDrag append-to-body>
            <ViewChatDialogueDialog ref="ViewChatDialogueDialog" :HistoryROW="HistoryROW"
                style="z-index:10000;height:600px" />
        </el-dialog>

    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import MyContainer from "@/components/my-container";
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import CesTable from "@/components/Table/table.vue";
import buschar from '@/components/Bus/buschar'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    getUnPayOrderAuditStatisticsPageList,
    getUnPayOrderAuditUserNameList,
    getUnPayOrderAuditGroupNameList,
    unPayOrderAuditStatisticsExport,
    getUnPayOrderAuditStatisticsTrendChart
} from "@/api/customerservice/chartCheck";
import ViewChatDialogueDialog from "@/views/customerservice/chartCheck/SalesDialog/ViewChatDialogueDialog"
import { orderBy } from 'lodash';


// //数据维度
const dataTypeList = [
    { name: "平台", value: 0 },
    { name: "分组", value: 1 },
    { name: "个人", value: 2 }
];
//店铺
const platformTypeList = [
    { name: "拼多多", value: 2 },
    { name: "天猫", value: 1 },
    { name: "淘宝", value: 9 },
    { name: "抖音", value: 6 }
];
//售前数据
const PreTableCols = [
    {
        istrue: true, prop: 'platform', label: '平台', width: '80', sortable: 'custom',
        formatter: (row) => {
            return platformTypeList.filter(item => item.value == row.platform)[0]?.name
        },
    },
    { istrue: true, prop: 'groupName', label: '组名', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'userName', label: '姓名', width: '100', sortable: 'custom' },
    {
        type: "button", label: "当月3%", ishide: 'function', prop: 'disclaimerCount', align: "center", width: "150", sortable: 'custom', tipmesg: '当月个人被审核总量*3％',
        btnList: [
            { color: 'black', prop: 'disclaimerCount', itemStyle: { cursor: "default", color: 'black', paddingRight: '10px' }, },
            // { color: 'black', label: "历史", itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.showHistory(row) },
        ],
    },
    { istrue: true, prop: 'problemCount', label: '问题合计', width: '100', sortable: 'custom', tipmesg: '合计=回复问题+专业问题+敷衍怠慢' },
    {
        type: "button", label: "回复问题", ishide: 'function', prop: 'column1', align: "center", minwidth: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column1', itemStyle: { cursor: "pointer", color: 'blue' },handle: (that, row) => that.groupclick(row, '回复问题',1) },
/*            { color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals1', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '回复问题', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment1', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '回复问题', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "专业能力", prop: 'column2', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column2', itemStyle: { cursor: "pointer", color: 'blue' },handle: (that, row) => that.groupclick(row, '专业能力', 1) },
          /*  { color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals2', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '专业能力', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment2', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '专业能力', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "敷衍怠慢", prop: 'column3', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column3', itemStyle: { cursor: "pointer", color: 'blue' },handle: (that, row) => that.groupclick(row, '敷衍怠慢', 1) },
           /* { color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals3', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '敷衍怠慢', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment3', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '敷衍怠慢', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "存在违规/过度承诺", prop: 'column4', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column4', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '存在违规/过度承诺', 1) },
           /* { color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals4', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '存在违规/过度承诺', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment4', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '存在违规/过度承诺', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "存在违规/引导线下交易", prop: 'column5', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column5', itemStyle: { cursor: "pointer", color: 'blue' },handle: (that, row) => that.groupclick(row, '存在违规/引导线下交易', 1) },
           /* { color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals5', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '存在违规/引导线下交易', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment5', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '存在违规/引导线下交易', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "存在违规/其他违规", prop: 'column6', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column6', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '存在违规/其他违规', 1) },
       /*     { color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals6', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '存在违规/其他违规', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment6', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '存在违规/其他违规', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "态度问题/辱骂客户", prop: 'column7', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column7', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/辱骂客户', 1)  },
            /*{ color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals7', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/辱骂客户', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment7', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/辱骂客户', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "态度问题/怒怼客户", prop: 'column8', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column8', itemStyle: { cursor: "pointer", color: 'blue' },handle: (that, row) => that.groupclick(row, '态度问题/怒怼客户', 1) },
            /*{ color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals8', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/怒怼客户', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment8', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/怒怼客户', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "态度问题/反问客户", prop: 'column9', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column9', itemStyle: { cursor: "pointer", color: 'blue' },handle: (that, row) => that.groupclick(row, '态度问题/反问客户', 1) },
           /* { color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals9', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/反问客户', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment9', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/反问客户', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "态度问题/不耐烦", prop: 'column10', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column10', itemStyle: { cursor: "pointer", color: 'blue' },handle: (that, row) => that.groupclick(row, '态度问题/不耐烦', 1) },
           /* { color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals10', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/不耐烦', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment10', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/不耐烦', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    /*{
        type: "button", label: "小额打款异常", prop: 'column10', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'smallPayment', itemStyle: { cursor: "pointer", color: 'blue' },handle: (that, row) => that.groupclick(row, '小额打款异常', 1) },
        ],
    },*/
    { istrue: true, prop: 'column12', label: '总计', width: '100', sortable: 'custom' },

    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

//售后数据
const AfterTableCols = [
    {
        istrue: true, prop: 'platform', label: '平台', width: '80', sortable: 'custom',
        formatter: (row) => {
            return platformTypeList.filter(item => item.value == row.platform)[0]?.name
        },
    },
    { istrue: true, prop: 'groupName', label: '组名', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'userName', label: '姓名', width: '100', sortable: 'custom' },
    {
        type: "button", label: "当月3%", ishide: 'function', prop: 'disclaimerCount', align: "center", width: "150", sortable: 'custom', tipmesg: '当月个人被审核总量*3％',
        btnList: [
            { color: 'black', prop: 'disclaimerCount', itemStyle: { cursor: "default", color: 'black' }, },
            // { color: 'black', label: "历史", itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.showHistory(row) },
        ],
    },
    { istrue: true, prop: 'problemCount', label: '问题合计', width: '100', sortable: 'custom', tipmesg: '合计=回复问题+专业问题+敷衍怠慢+流程问题' },
    {
        type: "button", label: "回复问题", ishide: 'function', prop: 'column1', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column1', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '回复问题', 1) },
        /*    { color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals1', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '回复问题', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment1', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '回复问题', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "流程问题", ishide: 'function', prop: 'column11', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column11', itemStyle: { cursor: "pointer", color: 'blue' },handle: (that, row) => that.groupclick(row, '流程问题', 1) },
          /*  { color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals11', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '流程问题', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment11', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '流程问题', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "专业能力", prop: 'column2', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column2', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '专业能力', 1) },
           /* { color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals2', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '专业能力', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment2', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '专业能力', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "敷衍怠慢", prop: 'column3', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column3', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '敷衍怠慢', 1)},
            /*{ color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals3', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '敷衍怠慢', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment3', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '敷衍怠慢', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "存在违规/过度承诺", prop: 'column4', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column4', itemStyle: { cursor: "pointer", color: 'blue' },handle: (that, row) => that.groupclick(row, '存在违规/过度承诺', 1) },
           /* { color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals4', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '存在违规/过度承诺', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment4', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '存在违规/过度承诺', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "存在违规/引导线下交易", prop: 'column5', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column5', itemStyle: { cursor: "pointer", color: 'blue' },handle: (that, row) => that.groupclick(row, '存在违规/引导线下交易', 1) },
            /*{ color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals5', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '存在违规/引导线下交易', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment5', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '存在违规/引导线下交易', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "存在违规/其他违规", prop: 'column6', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column6', itemStyle: { cursor: "pointer", color: 'blue' },handle: (that, row) => that.groupclick(row, '存在违规/其他违规', 1) },
           /* { color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals6', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '存在违规/其他违规', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment6', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '存在违规/其他违规', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "态度问题/辱骂客户", prop: 'column7', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column7', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/辱骂客户', 1) },
            /*{ color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals7', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/辱骂客户', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment7', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/辱骂客户', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "态度问题/怒怼客户", prop: 'column8', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column8', itemStyle: { cursor: "pointer", color: 'blue' },handle: (that, row) => that.groupclick(row, '态度问题/怒怼客户', 1) },
           /* { color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals8', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/怒怼客户', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment8', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/怒怼客户', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "态度问题/反问客户", prop: 'column9', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column9', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/反问客户', 1) },
           /* { color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals9', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/反问客户', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment9', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/反问客户', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
    {
        type: "button", label: "态度问题/不耐烦", prop: 'column10', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'column10', itemStyle: { cursor: "pointer", color: 'blue' },handle: (that, row) => that.groupclick(row, '态度问题/不耐烦', 1) },
            /*{ color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'finals10', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/不耐烦', 1) },
            { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
            { color: 'black', prop: 'judgment10', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.groupclick(row, '态度问题/不耐烦', 2) },
            { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
        ],
    },
  {
        type: "button", label: "小额打款异常", prop: 'smallPayment', align: "center", width: "200", sortable: 'custom',
        btnList: [
            { color: 'black', prop: 'smallPayment', itemStyle: { cursor: "pointer", color: 'blue' },handle: (that, row) => that.groupclick(row, '小额打款异常', 1) },
        ],
    },
    { istrue: true, prop: 'column12', label: '总计', width: '100', sortable: 'custom' },
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

export default {
    name: "salesFifth",
    components: { MyContainer, CesTable, buschar, vxetablebase, datepicker, ViewChatDialogueDialog },
    data() {
        return {
            that: this,
            Filter: {
                auditTime: [
                    formatTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"),
                    formatTime(new Date(), "YYYY-MM-DD 23:59:59"),
                ],
                timerange: [],
                dataType: 0,
                platform: 2,
                roderTime: [],
                switchshow:false,
            },
            userNameList: [],
            initialList: [],
            groupNameList: [],
            pickerOptions: {
                disabledDate(date) {
                    // 设置禁用日期
                    const start = new Date("1970/1/1");
                    const end = new Date("9999/12/31");
                    return date < start || date > end;
                },
            },
            defaultDate: new Date(),
            //默认客服
            isActive: true,
            isInactive: false,
            PreTableCols: [],     //售前的数据列表
            AfterTableCols: [], //售后的数据列表
            tableData: [],
            summaryarry: null,
            listLoading: false,
            total: 0,
            nameList: [],
            groupName: [],
            platformTypeList: platformTypeList,
            dataTypeList: dataTypeList,
            userNameLists: [],
            groupNameLists: [],
            dialogMapVisible: { visible: false, title: "", data: [] },
            tableCols: [],
            showHistoryDialogVisibleSyj: false,
            viewChatDialogVisible: false,
            HistoryROW: {},
            pager:{
                orderBy:"",
                isAsc:false
            },
        };
    },
    async mounted() {
        await this.init();
        await this.onSearch();
        await this.changePlatform();
    },
    methods: {
        async init() {
            var date1 = new Date(); date1.setDate(date1.getDate() - 6);
            var date2 = new Date(); date2.setDate(date2.getDate());
            this.Filter.auditTime = [];
            this.Filter.auditTime[0] = date1;
            this.Filter.auditTime[1] = date2

        },
        onSearch() {
            this.getPageList();

            this.$refs.table.changecolumn_setTrue(["disclaimerCount"]);
            if (this.Filter.dataType != 2) {
                this.$refs.table.changecolumn(["disclaimerCount"]);
            }

        },

        async changePlatform(val) {
            if (val == null) {
                val = this.Filter.platform;
            }

            this.userNameLists = [];
            this.groupNameLists = [];
            this.Filter.userNameList = null;
            this.Filter.groupNameList = null;

            if (val) {

                const userName = await getUnPayOrderAuditUserNameList({ platform: val, salesType: this.Filter.switchshow ? 1 : 0 });
                this.userNameLists = userName.data;

                const groupName = await getUnPayOrderAuditGroupNameList({ platform: val, salesType: this.Filter.switchshow ? 1 : 0 });
                this.groupNameLists = groupName.data;

            }
        },
        changPlatformState(val) {
            if (!this.Filter.platform) {
                this.$message({ message: "请先选择平台！", type: "warning" });
                return false;
            }
        },

        async getPageList() {
            let newArr = this.Filter.switchshow ? AfterTableCols : PreTableCols
            this.tableCols = newArr
            const para = { ...this.Filter };
            if (this.Filter.timerange) {
                para.conversationTimeStart = this.Filter.timerange[0];
                para.conversationTimeEnd = this.Filter.timerange[1];
            }
            if (this.Filter.auditTime) {
                para.operatorTimeStart = this.Filter.auditTime[0];
                para.operatorTimeEnd = this.Filter.auditTime[1];
            }
            if (this.Filter.roderTime) {
                para.orderTimeStart = this.Filter.roderTime[0];
                para.orderTimeEnd = this.Filter.roderTime[1];
            }
            para.salesType = this.Filter.switchshow ? 1 : 0


           if(this.Filter.dataType!=2 && this.pager.orderBy=="disclaimerCount"){
                this.pager={}
            }
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };
            this.listLoading = true;

            const res = await getUnPayOrderAuditStatisticsPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.tableData = res.data.list;
            this.summaryarry = res.data.summary;
        },
        changeShowgroup() {
            this.Filter.timerange = [];
            this.Filter.groupNameList = "";
            this.Filter.userNameList = "";
            this.groupNameLists = [];
            this.userNameLists = [];

            this.changePlatform();
            this.onSearch();
        },
        sortchange(column) {
            if (!column.order) this.pager = {};
            else {
                this.pager = {
                    orderBy: column.prop,
                    isAsc: column.order.indexOf("descending") == -1 ? true : false,
                };
                this.onSearch();

            }


        },
        async onExport() {  //导出
            const para = { ...this.Filter };
            if (this.Filter.timerange) {
                para.conversationTimeStart = this.Filter.timerange[0];
                para.conversationTimeEnd = this.Filter.timerange[1];
            }
            if (this.Filter.auditTime) {
                para.operatorTimeStart = this.Filter.auditTime[0];
                para.operatorTimeEnd = this.Filter.auditTime[1];
            }
            if (this.Filter.roderTime) {
                para.orderTimeStart = this.Filter.roderTime[0];
                para.orderTimeEnd = this.Filter.roderTime[1];
            }

            para.salesType = this.Filter.switchshow ? 1 : 0
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };
            var res = await unPayOrderAuditStatisticsExport(params);
            const aLink = document.createElement("a");
            let blob = new Blob([res], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);

            var excelName = this.Filter.switchshow ? '售后数据' : '售前数据'
            aLink.setAttribute(
                "download",
                excelName + new Date().toLocaleString() + ".xlsx"
            );
            aLink.click();
        },
        //趋势图
        async showchart(row) {
            const para = { ...this.Filter };
            if (this.Filter.timerange) {
                para.conversationTimeStart = this.Filter.timerange[0];
                para.conversationTimeEnd = this.Filter.timerange[1];
            }
            if (this.Filter.auditTime) {
                para.operatorTimeStart = this.Filter.auditTime[0];
                para.operatorTimeEnd = this.Filter.auditTime[1];
            }
            if (this.Filter.roderTime) {
                para.orderTimeStart = this.Filter.roderTime[0];
                para.orderTimeEnd = this.Filter.roderTime[1];
            }
            para.salesType = this.Filter.switchshow ? 1 : 0
            para.groupName = row.groupName;
            para.userName = row.userName;
            para.platform = row.platform;

            const params = {
                ...this.pager,
                ...para
            };
            const res = await getUnPayOrderAuditStatisticsTrendChart(params).then(res => {
                if (res) {
                    this.dialogMapVisible.visible = true;
                    this.dialogMapVisible.data = res;
                    this.dialogMapVisible.title = res.title;
                    res.title = "";
                } else {

                }
            });
            this.dialogMapVisible.visible = true;
        },
        groupclick(row, typeName, num) {

            if(this.Filter.dataType==0){
              var newPlotForm=row.platform
            }else{
                var newPlotForm=this.Filter.platform
            }
            const params = {
                dataplatform: this.Filter.dataType,
                platform: newPlotForm==0 ? null : newPlotForm,
                groupName: row.groupName,
                userName: row.userName,
                timerange: this.Filter.timerange ?? [],
                auditTime: this.Filter.auditTime ?? [],
                roderTime: this.Filter.roderTime ?? [],
                switchshow: this.Filter.switchshow,
                typeName: typeName,
                num: num,
            }
            this.$emit("jumpIndex", params)

        },
        showHistory(row) {
            const HistoryROW = {
                salesType: this.Filter.switchshow == true ? 1 : 0,
                platform: row.platform,
                groupName: row.groupName,
                userName: row.userName,
            };
            this.HistoryROW = HistoryROW;
            this.viewChatDialogVisible = true;
        },

    }

};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 55px;
}
</style>
