<template>
    <container v-loading="pageLoading">
        <template>
            <el-form v-show="isShowNew1" :model="formData" :rules="rules" ref="rule" label-width="130px"
                label-position="right">
                <el-form-item label="库存安全天数">
                    <el-input-number v-model="formData.safetyStockDays" controls-position="right" :precision="0" :min="1"
                        :max="100" style="width: 220px;"></el-input-number>
                </el-form-item>
                <el-form-item label="计划完成时间">
                    <el-input-number v-model="formData.plannedCompletionDate" controls-position="right" :precision="0"
                        :min="0" :max="100" style="width: 220px;"></el-input-number>
                </el-form-item>
                <el-form-item label="品牌" prop="brandCode">
                    <el-select v-model="formData.brandCode" placeholder="品牌" style="width: 220px;">
                        <el-option v-for="item in brandList" :key="item.setId" :value="item.setId"
                            :label="item.sceneCode"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
        </template>

        <template>
            <vxe-form v-show="isShowNew2" :data="formData" title-align="right" title-width="120">
                <vxe-form-item title="开单状态" field="name" span="12">
                    <template #default>
                        <vxe-switch v-model="val1" size="small" open-label="启用" close-label="禁用"></vxe-switch>
                    </template>
                </vxe-form-item>
            </vxe-form>
        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:center;padding-top:10px;">
                    <my-confirm-button v-show="isShowNew2" type="submit" :loading="onFinishLoading"
                        @click="onFinish(typeValue)">保存&关闭
                    </my-confirm-button>
                    <my-confirm-button v-show="isShowNew1" type="submit" :validate="editFormvalidate"
                        :loading="onFinishLoading" @click="onFinish(typeValue)">保存&关闭
                    </my-confirm-button>
                    <vxe-button type="reset" size="mini" @click="onClose">关闭</vxe-button>
                </el-col>
            </el-row>
        </template>
    </container>
</template>

<script>
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { updateGoodsFinishedPartAutoAsync } from "@/api/inventory/machine"
import { getShootingSetData } from '@/api/media/shootingset'


export default {
    name: 'YunHanAdminOperationGoodsFinishedpart',
    components: { MyConfirmButton, container },
    props: {
        filter: {},
    },

    data() {
        return {
            that: this,
            formData: {
                safetyStockDays: null,
                brandCode: null,
                plannedCompletionDate: null,
            },
            rules: {
                brandCode: [
                    { required: true, message: '请选择品牌', trigger: 'change' }
                ],
            },
            brandList: [],//品牌列表数据集 
            selRows: [],
            checkList: [],
            val1: false,
            pageLoading: false,
            isShowNew1: false,
            isShowNew2: false,
            onFinishLoading: false,
            dialogAddVisible: false,
            typeValue: null
        };
    },

    async mounted() {
    },

    methods: {
        async loadData() {
            this.pageLoading = true;
            this.selRows = this.filter.selData;
            this.typeValue = this.filter.type;
            if (this.typeValue == 1) {
                await this.init();
                if (this.selRows.length == 1) {
                    this.formData.safetyStockDays = this.selRows[0].safetyStockDays;
                    this.formData.brandCode = this.selRows[0]?.brandCode ?? null;
                    this.formData.plannedCompletionDate = this.selRows[0]?.plannedCompletionDate ?? 2;
                }
                this.isShowNew1 = true;
                this.isShowNew2 = false;
            } else if (this.typeValue == 2) {
                this.isShowNew1 = false;
                this.isShowNew2 = true;
            }
            this.pageLoading = false;
            console.log('选择数据', this.selRows)
        },
        async init() {
            const res = await getShootingSetData({ setType: 15 });
            if (!res?.success) {
                return
            }
            this.brandList = res?.data?.data;

        },
        async onFinish(val) {
            this.onFinishLoading = true;
            let goodsCodes = [];
            this.selRows.map((item) => {
                goodsCodes.push(item.goodsCode)
                console.log('我是报错', item)
            });
            if (val == 1) {
                var params = { goodsCodes: goodsCodes.join(), plannedCompletionDate: this.formData.plannedCompletionDate, safetyStockDays: this.formData.safetyStockDays, brandCode: this.formData.brandCode, type: val };
            } else if (val == 2) {
                var params = { goodsCodes: goodsCodes.join(), isAuto: this.val1, type: val };
            }
            var res = await updateGoodsFinishedPartAutoAsync(params)
            if (!res?.success) {
                this.$emit('closedialogEdit', true);
                return;
            }
            this.$message({
                type: 'success',
                message: '操作成功!'
            });
            this.onFinishLoading = false;
            this.$emit('closedialogEdit', true);
            this.$emit('afterSave');
        },
        onClose() {
            this.$emit('closedialogEdit', false);
        },
        editFormvalidate() {
            let isValid = false
            this.$refs.rule.validate(valid => {
                isValid = valid
            })
            return isValid
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep .el-input-number.is-controls-right .el-input__inner {
    text-align: left !important;
}
</style>