<template>
    <!-- 责任申诉明细 -->
    <div class="containal">
        <div class="top">
            <div>审核须知:</div>
            <div>1、申诉基础步骤：发起申诉-->提交申诉凭证-->审核凭证-->判定申诉结果-->申诉通过后调整新责任部门，原部门责任及相关事宜剔除。</div>
            <div>2、申诉机会仅一次，请您使用好申诉权益。</div>
            <div>3、申诉时间为责任计算时间(非扣款时间)起当天17:30至次日9:00,超时导致申诉入口关闭,无法支持再次申诉。</div>
        </div>
        <div class="center">
            <!-- 加上插值 -->
            <div class="center_item">
                <div class="item_box" style="width: 260px;">线上订单号:{{ auditDialogProps.otherInfo.b.orderNo }}</div>
                <div class="item_box">发起时间:{{ auditDialogProps.otherInfo.b.afterSaleApproveDate }}</div>
                <div class="item_box">损耗金额:{{ auditDialogProps.otherInfo.b.damagedAmount }}</div>
            </div>
            <div class="center_item">
                <div class="item_box" style="width: 260px;">原责任部门:{{ auditDialogProps.orgZrDepartment }}</div>
                <div class="item_box">原责任类型:{{ auditDialogProps.orgZrType2 }}</div>
                <div class="item_box">原责任人:{{ auditDialogProps.orgZrUserName }}</div>
            </div>
            <el-form :model="ruleForm"  ref="ruleForm" label-width="100px" disabled>
                <el-form-item label="定责理由" prop="auditDialogProps.applyReason">
                    <el-input type="textarea" maxlength="200" minlength="1" placeholder="定责理由" v-model="auditDialogProps.applyReason"
                        show-word-limit :rows="4">
                    </el-input>
                </el-form-item>
                <el-form-item label="定责资料" prop="desc">
                 <div v-html="auditDialogProps.applyContent" class="tempdivv"></div>
                </el-form-item>
                <el-form-item label="审核意见" prop="desc" v-if="auditDialogProps.applyState == 2 || auditDialogProps.applyState == -1 || auditDialogProps.applyState==3">
                 <div v-html="auditDialogProps.auditRemark"></div>
                </el-form-item>
            </el-form>

        </div>
        <div class="footer">
            <el-button size="medium" @click="handleClose">关闭</el-button>
        </div>
    </div>
</template>

<script>
import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'
export default {
    name: 'auditDialog',
    props: {
        //接收父组件传递的值
        handleClose: {
            type: Function,
            required: true
        },
        auditDialogProps: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            textarea: '',
            ruleForm:null,
        }

    },
    methods: {
       
    }
}
</script>

<style scoped lang="scss">
.containal {
    padding: 0 20px;

    .top {
        color: red;
        margin-bottom: 30px;
    }

    .center {
        .center_item {
            padding-left: 33px;
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;

            .item_box{
                width: 220px;
            }
        }
    }

    .footer{
        display: flex;
        justify-content: flex-end;
    }
}

.tempdivv ::v-deep img{ max-width: 980px}
</style>
