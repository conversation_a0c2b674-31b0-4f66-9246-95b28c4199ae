<template>
  <MyContainer>
    <vxetablebase :id="'trackRecordList202505291050'" :tablekey="'trackRecordList202505291050'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0"
      :loading="loading" :height="500" :border="true" :isNeedExpend="false">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getExpressClaimOrderHisListAsync } from '@/api/customerservice/expressClaimOrder'
const tableCols = [
  { sortable: 'custom', width: '180', align: 'center', prop: 'newLatestTrackingTime', label: '最新轨迹更新时间', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'newLatestTracking', label: '最新轨迹更新', },
  {
    sortable: 'custom', width: '180', align: 'center', prop: 'newLatestTrackingIsSame', label: '更新后最新轨迹是否一致', formatter: (row) => {
      return row.newLatestTrackingIsSame ? '是' : '否'
    },
  },
]
export default {
  name: "trackRecordList",
  components: {
    MyContainer, vxetablebase
  },
  props: {
    chuanshenData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'newLatestTrackingTime',
        isAsc: false,
        expressNo: null,
      },
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
    }
  },
  async mounted() {
    this.ListInfo.expressNo = this.chuanshenData.expressNo
    await this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      const { data, success } = await getExpressClaimOrderHisListAsync(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>
