<template>
  <MyContainer>
    <template #header>
      <div class="top">


        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" :clearable="false"
          start-placeholder="数据开始时间" end-placeholder="数据结束时间" :picker-options="pickerOptions"
          style="width: 300px;margin-right: 10px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <inputYunhan  style="width: 200px;margin-left: 0px;" ref="productCode2" :inputt.sync="ListInfo.OrderNo" v-model="ListInfo.OrderNo"
                        class="publicCss" placeholder="线上单号/多条请按回车" :clearable="true" :clearabletext="true"
                        :maxRows="2000" :maxlength="600000" @callback="orderNoInnerBack" title="线上单号">
        </inputYunhan>


        <YhShopSelector  style="margin-right: 10px;" :names="['所有店铺']" :values="[-1]"  platform="14" :checkboxOrRadio="'checkbox'" :addAllWarehouseConcept="true" @onChange="(v)=>{ListInfo.shopCode=v[0].join(','); }">
        </YhShopSelector>
        <el-select filterable v-model="accountTypeist" placeholder="请选择账单项目"  multiple  clearable style="margin-right: 10px;width: 130px">
                        <el-option label="交易退款" value="交易退款" />
                        <el-option label="技术服务费" value="技术服务费" />
                        <el-option label="达人佣金" value="达人佣金" />
                        <el-option label="团长佣金" value="团长佣金" />
                        <el-option label="快赚客佣金" value="快赚客佣金" />
                        <el-option label="服务商佣金" value="服务商佣金" />
                        <el-option label="支付营销回退" value="支付营销回退" />
                        <el-option label="其他收费" value="其他收费" />
                        <el-option label="其他收费明细" value="其他收费明细" />
                        <el-option label="订单实付" value="订单实付" />
                        <el-option label="支付营销补贴" value="支付营销补贴" />
                        <el-option label="平台补贴" value="平台补贴" />
                        <el-option label="主播补贴" value="主播补贴" />
                        <el-option label="主播补贴明细" value="主播补贴明细" />
                        <el-option label="提现" value="提现" />
                        <el-option label="集运扣款冻结" value="集运扣款冻结" />
                    </el-select>
                  <el-select filterable  v-model="BillTypeList" placeholder="请选择ERP账务类型"  multiple clearable style="margin-right: 10px;width: 130px">
                    <el-option label="交易退款" value="交易退款" />
                        <el-option label="技术服务费" value="技术服务费" />
                        <el-option label="达人佣金" value="达人佣金" />
                        <el-option label="团长佣金" value="团长佣金" />
                        <el-option label="快赚客佣金" value="快赚客佣金" />
                        <el-option label="服务商佣金" value="服务商佣金" />
                        <el-option label="支付营销回退" value="支付营销回退" />
                        <el-option label="其他收费" value="其他收费" />
                        <el-option label="其他收费明细" value="其他收费明细" />
                        <el-option label="订单实付" value="订单实付" />
                        <el-option label="支付营销补贴" value="支付营销补贴" />
                        <el-option label="平台补贴" value="平台补贴" />
                        <el-option label="主播补贴" value="主播补贴" />
                        <el-option label="主播补贴明细" value="主播补贴明细" />
                        <el-option label="提现" value="提现" />
                        <el-option label="集运扣款冻结" value="集运扣款冻结" />
                        <el-option label="无" value="无" />
                    </el-select>


        <el-input v-model="ListInfo.proCode" placeholder="商品ID" maxlength="50" clearable class="publicCss" />

        <el-switch v-model="ListInfo.isGroup"  active-color="#67C23A"  inactive-color="#409EFF"  class="publicCss"
                    @change="getList('search')"   active-text="汇总查询"  active-value="1" inactive-value="0"  inactive-text="全量查询">
                    </el-switch>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" style="margin-left: 10px;" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'KWaiShopBill202408041358'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
      :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getNewKWaiBillingCharge, exportNewKWaiBillingChargeList } from '@/api/bookkeeper/reportdayV2'
import { formatTime } from "@/utils/tools";
import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";
import YhShopSelector from "@/components/YhCom/YhShopSelector";
const tableCols = [
  { sortable: 'custom', width: '130', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'billingItem', label: '账单项目', },
  { istrue: true, prop: 'billType', label: 'ERP账务类型', tipmesg: '', width: '120', sortable: 'custom', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'onlineOrderNumber', label: '线上单号', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'orderNo', label: '订单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'businessType', label: '业务类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'settlementTime', label: '结算时间', formatter: (row) => formatTime(row.settlementTime, 'YYYY-MM-DD') },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'incomeAmount', label: '收入金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'expenseAmount', label: '支出金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'amountIncome', label: '总金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'proCode', label: '商品ID', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'productQuantity', label: '商品数量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'influencerId', label: '达人ID', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'teamLeaderId', label: '团长ID', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'settlementRule', label: '结算规则', },
]
export default {
  name: "KWaiShopBill",
  components: {
    MyContainer, vxetablebase,inputYunhan,YhShopSelector
  },
  data() {
    return {
      summaryarry: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        shopCode: null,//店铺
        billingItem: null,//补扣款分类
        proCode: null,//商品ID
        orderNo: null,//订单号
      },
      BillTypeList:[],
      accountTypeist:[],
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async exportProps() {
      this.loading = true
      this.ListInfo.BillType = this.BillTypeList.join(',');
      this.ListInfo.AccountType = this.accountTypeist.join(',');
      const { data } = await exportNewKWaiBillingChargeList(this.ListInfo)
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '快手账单费用数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给昨天时间
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.ListInfo.BillType = this.BillTypeList.join(',');
      this.ListInfo.AccountType = this.accountTypeist.join(',');
      this.loading = true
      const { data, success } = await getNewKWaiBillingCharge(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        console.log(this.summaryarry);
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    orderNoInnerBack(val) {
            this.ListInfo.OrderNo = val;
        },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}
</style>
