<template>
    <el-row :gutter="10">
        <el-col :span="6">
            <el-card>
                <el-table max-height="720px" :indent="10" :show-header="false" :data="deptList" border
                    :default-expand-all="true" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
                    row-key="id" highlight-current-row style="width: 100%;" @row-click="deptSelect">
                    <el-table-column prop="name" />
                </el-table>
                <!-- <dingdingDept ref="dingdingDept" :fatherMethod="deptOnSearch" /> -->
            </el-card>
        </el-col>
        <el-col :span="18">
            <container style="height:83vh">
                <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                    @select='selectchange' :isSelection='false' :isSelectColumn='true' :tableData='list'
                    :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
                    <template slot='extentbtn'>
                        <el-button-group>
                            <el-button style="padding: 0;margin: 0;">
                                <el-input v-model.trim="filter.userName" placeholder="姓名" maxlength="50" clearable
                                    style="width:120px;" />
                            </el-button>
                            <!-- <el-button style="padding: 0;margin: 0;">
                                <el-input v-model="filter.title" placeholder="岗位" maxlength="50" clearable
                                    style="width:120px;" />
                            </el-button> -->
                            <el-button style="padding: 0;margin: 0;">
                                <el-input v-model.trim="filter.deptName" placeholder="对应组" maxlength="50" clearable
                                    style="width:120px;" />
                            </el-button>
                            <el-button style="padding: 0;margin: 0;">
                                <el-select v-model.trim="filter.employeeStatus" placeholder="员工状态" clearable filterable
                                    style="width:120px;">
                                    <el-option label="正式" :value="3" />
                                    <el-option label="试用" :value="2" />
                                </el-select>
                            </el-button>
                            <el-button style="padding: 0;margin: 0;">
                                <el-input-number controls-position="right" v-model="filter.startIntegral"
                                    placeholder="积分起始区间" :max="99999999" :min="0" clearable style="width:160px;" />
                            </el-button>
                            <el-button style="padding: 0;margin: 0;">
                                <el-input-number controls-position="right" v-model="filter.endIntegral" placeholder="积分结束区间" :max="99999999"
                                    :min="0" clearable style="width:160px;" />
                            </el-button>
                            <el-button type="primary" @click="deptOnSearch">查询</el-button>
                        </el-button-group>
                    </template>
                </ces-table>
                <template #footer>
                    <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
                </template>
            </container>
        </el-col>
        <el-dialog title="积分日志" :visible.sync="dialogVisiblejf" width="800px" v-dialogDrag>
            <el-table :data="integralLogList" border max-height="600">
                <el-table-column prop="modifiedUserName" label="操作人" width="180">
                </el-table-column>
                <el-table-column prop="integralType" label="类型" width="180">
                </el-table-column>
                <el-table-column prop="integral" label="积分" width="180">
                    <template slot-scope="scope">
                        {{ (scope.row.addOrSub + scope.row.integral) }}
                    </template>
                </el-table-column>
                <el-table-column prop="createdTime" label="记录时间">
                </el-table-column>
            </el-table>
            <template #footer>
                <my-pagination ref="jfpager" :total="jftotal" :checked-count="sels.length" @get-page="getpageIntegralLog" />
            </template>
        </el-dialog>

        <el-dialog title="修改积分" :visible.sync="dialogVisibleupjf" width="500px" v-dialogDrag>
            <el-form label-width="80px" @submit.native.prevent>
                <el-form-item label="积分:">
                    <el-input-number placeholder="多少积分" style="width: 200px; " v-model="integral" :precision="2"
                        :max="999999" :min="0"></el-input-number>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="updateIntegral">保存</el-button>
                <el-button @click="dialogVisibleupjf = false">关闭</el-button>
            </span>
        </el-dialog>

    </el-row>
</template>
<script>
import MyConfirmButton from "@/components/my-confirm-button";
import { listToTree, getTreeParents } from '@/utils'
// import { getPageDeptUser, updateDDuserdingId } from '@/api/operatemanage/base/dingdingShow'
import { getIntegralLogAsync, updateIntegralErp, getDeptTreeInfo, getPageDeptUser } from '@/api/profit/orderfood'
import dingdingDept from '@/views/operatemanage/base/dingdingDept.vue'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
const tableCols = [
    { istrue: true, prop: 'avatar', label: '头像', type: "image", width: '70' },
    { istrue: true, prop: 'userName', label: '姓名', sortable: 'custom', width: '170' },
    { istrue: true, prop: 'title', label: '岗位', style: 'center', width: 'auto', sortable: 'custom', },
    { istrue: true, prop: 'deptName', label: '对应组', style: 'center', width: 'auto', sortable: 'custom', },
    // { istrue: true, prop: 'isDelete', label: '工位信息', style: 'center', width: 'auto', sortable: 'custom', formatter: (row) => row.isDeleteStr },
    {
        istrue: true, prop: 'EmployeeStatus', label: '员工状态', style: 'center', width: 'auto', sortable: 'custom', formatter: (row) => {
            if (row.employeeStatus == 2) return "试用";
            else if (row.employeeStatus == 3) return "正式";
            else return "离职";
        }
    },
    { istrue: true, prop: 'integral', label: '剩余积分', style: 'center', width: 'auto', sortable: 'custom', },

    {
        istrue: true, type: "button", label: '操作', width: "240", fixed: "right", permission: "dingCodeEdit",
        btnList: [
            { label: "积分明细", handle: (that, row) => that.getIntegralLog(row) },
            { label: "修改积分", handle: (that, row) => that.editIntegral(row) }
        ]
    }
];
const tableHandles = []
export default {
    name: 'dingdingDeptUser',
    components: { dingdingDept, container, cesTable, MyConfirmButton },
    data () {
        return {
            selectDept: 599262021,
            deptList: [],
            id: null,
            integral: null,
            dialogVisibleupjf: false,
            dialogVisiblejf: false,
            integralLogList: [],
            that: this,
            isLoad: false,
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: " createtime ", IsAsc: false },
            jftotal: 0,
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            pageLoading: false,
            filter: {},
            userid: '',
        }
    },
    async mounted () {
        await this.initDept();
        await this.deptOnSearch();

    },
    beforeUpdate () { },
    methods: {
        async initDept () {
            const res = await getDeptTreeInfo()
            if (res.data) {
                this.deptList = listToTree(_.cloneDeep(res.data))
            }
        },
        deptSelect (row, column) {
            this.selectDept = row.id
            this.deptOnSearch()
        },
        deptOnSearch () {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        editIntegral (row) {
            this.dialogVisibleupjf = true;
            this.id = row.ddUserId;
            this.integral = row.integral;
        },
        async updateIntegral () {
            this.$confirm('确定修改, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(async _ => {
                    let p = {
                        integral: this.integral,
                        id: this.id,
                    }
                    const res = await updateIntegralErp(p);
                    if (res.data) {
                        this.$message({ type: 'success', message: '修改成功!' });
                        this.dialogVisibleupjf = false;
                        await this.getlist();
                    }
                })
                .catch(_ => { });


        },
        async getpageIntegralLog () {
            this.dialogVisiblejf = true;
            var pager = this.$refs.jfpager.getPager()
            let par = {
                ...pager,
                id: this.userid,
            }
            const res = await getIntegralLogAsync(par)
            this.integralLogList = res?.data?.integralLogList;
            this.jftotal = res?.data?.total;
        },
        async getIntegralLog (row) {
            this.dialogVisiblejf = true;
            this.userid = row.userid;
            var pager = this.$refs.jfpager.getPager()
            let par = {
                ...pager,
                id: row.userid,
            }
            const res = await getIntegralLogAsync(par)
            this.integralLogList = res?.data?.integralLogList;
            this.jftotal = res?.data?.total;
        },
        async getlist () {
            var deptId = this.selectDept;
            if (deptId == null || deptId == "") {
                return;
            }
            this.filter.dept_id = deptId;
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }
            this.listLoading = true
            this.list = [];
            const res = await getPageDeptUser(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res?.data?.total
            const data = res?.data?.list
            data.forEach(d => {
                d._loading = false
            })
            this.list = data;
        },
        sortchange (column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.deptOnSearch();
        },
        selsChange: function (sels) {
            this.sels = sels
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
    }
}
</script>
<style scoped lang="scss" >
::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: rgb(233, 233, 248);
    border-radius: 3px;
}

::v-deep .el-table__body-wrapper {
    overflow: auto;
}
</style>
  