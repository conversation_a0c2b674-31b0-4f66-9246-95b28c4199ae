<template>
    <MyContainer>
        <template #header>
            <div class="header">
                <el-input placeholder="系列编码" v-model="ListInfo.styleCode" maxlength="50" class="publicMargin"
                    style="width: 220px;" clearable v-show="!checkList.includes('供应商')"></el-input>
                <el-input placeholder="商品编码" v-model="ListInfo.goodCode" maxlength="50" class="publicMargin"
                    style="width: 220px;" clearable v-show="checkList.includes('商品编码') || checkList.length == 0"></el-input>
                <el-input placeholder="供应商名称" v-model="ListInfo.providerName" maxlength="50" class="publicMargin"
                    style="width: 220px;" clearable v-show="checkList.includes('供应商') || checkList.length == 0"></el-input>
                <el-select v-model="ListInfo.isBY" placeholder="是否包邮" class="publicMargin" style="width: 220px;" clearable
                    v-show="checkList.length == 0">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.brandId" placeholder="采购" class="publicMargin" style="width: 220px;" clearable
                    filterable v-show="!checkList.includes('供应商')">
                    <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.sourceType" placeholder="来源" class="publicMargin" style="width: 220px;"
                    clearable v-show="checkList.includes('供应商') || checkList.length == 0">
                    <el-option v-for="item in sourceType" :key="item.label" :label="item.label" :value="item.label">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.position" placeholder="职位" class="publicMargin" style="width: 220px;" clearable
                    v-show="checkList.length == 0">
                    <el-option v-for="item in positionType" :key="item.label" :label="item.label" :value="item.label">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.isContaisTax" placeholder="是否含税" class="publicMargin" style="width: 220px;"
                    clearable v-show="checkList.length == 0">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.dockingStatus" placeholder="对接状态" style="width: 220px" class="publicMargin"
                    clearable v-show="checkList.includes('供应商') || checkList.length == 0">
                    <el-option v-for="item in status" :key="item.label" :label="item.label" :value="item.label" />
                </el-select>
                <el-select v-model="ListInfo.IsExitProvider" placeholder="重复供应商" style="width: 220px" class="publicMargin"
                    clearable v-show="checkList.includes('供应商') || checkList.length == 0">
                    <el-option label="未重复" :value="2" />
                    <el-option label="重复" :value="1" />
                </el-select>

                <el-button type="primary" @click="searchList">查询</el-button>
            </div>
            <div class="radioGrp">
                <el-checkbox-group v-model="checkList" @change="changeCheckBox">
                    <el-checkbox label="系列编码" :value="1"></el-checkbox>
                    <el-checkbox label="商品编码" :value="2"></el-checkbox>
                    <el-checkbox label="供应商" :value="3"></el-checkbox>
                </el-checkbox-group>
                <el-switch v-model="isHidden" active-text="展开" inactive-text="折叠" @change="changeHidden"
                    v-show="checkList.length == 0" />
            </div>
        </template>
        <!-- 汇总 -->
        <vendorSummary :ListInfo="ListInfo" ref="summaryTable" v-show="checkList.length == 0" :isHidden="isHidden" />
        <!-- 分组 -->
        <vendorGroup :ListInfo="ListInfo" :checkList="checkList" ref="groupTable" v-show="checkList.length != 0" />
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
import vendorSummary from "./vendorSummary.vue"
import vendorGroup from "./vendorGroup.vue"
import { getAllProBrand } from '@/api/inventory/warehouse'
const options = [
    {
        value: '1',
        label: '是'
    },
    {
        value: '0',
        label: '否'
    }
]

const positionType = [
    {
        label: '老板'
    },
    {
        label: '业务员'
    },
    {
        label: '经理'
    }
]

const sourceType = [
    {
        label: '朋友圈'
    },
    {
        label: '聊天'
    },
    {
        label: '其他'
    }
]

const status = [
    {
        label: '待沟通',
        value: '待沟通'
    },
    {
        label: '沟通中',
        value: '沟通中'
    },
    {
        label: '寄样中',
        value: '寄样中'
    },
    {
        label: '采购中',
        value: '采购中'
    },
    {
        label: '采购完成',
        value: '采购完成'
    },
    {
        label: '不适合',
        value: '不适合'
    },
]

export default {
    components: { MyContainer, cesTable, vxetablebase, vendorSummary, vendorGroup },
    name: "vendorSumIndex",
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: false,//是否升序
                styleCode: null,//系列编码
                categoryId: null,//品类id
                openId: null,//openId
                goodCode: null,//商品编码
                isBY: null,//是否包邮
                isContaisTax: null,//是否含税
                providerName: null,//供应商名称
                dockingStatus: null,//对接状态
                sourceType: null,//来源
                brandId: null,//采购人员id
                position: null,//职位
                groupType: null,//分组类型
            },
            sourceType,//来源
            positionType,//职位
            options,//是否包邮,是否含税
            status,//状态
            checkList: [],
            brandList: [],
            isHidden: false,//是否展开
        };
    },
    mounted() {
        this.getBrandList()
    },
    methods: {
        changeHidden(e) {
            if (e) {
                this.$refs.summaryTable.unfoldTree()
            } else {
                this.$refs.summaryTable.foldTree()
            }
        },
        clear() {
            this.checkList = []
        },
        // //分组改变
        changeCheckBox(e) {
            this.ListInfo = {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: false,//是否升序
                styleCode: null,//系列编码
                categoryId: null,//品类id
                openId: null,//openId
                goodCode: null,//商品编码
                isBY: null,//是否包邮
                isContaisTax: null,//是否含税
                providerName: null,//供应商名称
                dockingStatus: null,//对接状态
                sourceType: null,//来源
                brandId: null,//采购人员id
                position: null,//职位
                groupType: null,//分组类型
            }
            if (e.length > 1) {
                this.checkList.splice(0, 1)
            }
            if (this.checkList.length == 0) {
                this.$nextTick(() => {
                    this.$refs.summaryTable.getAlreadyList()
                })
            } else {
                this.$nextTick(() => {
                    this.$refs.groupTable.getGroupList(this.checkList[0])
                })
            }
        },
        //获取采购列表
        async getBrandList() {
            const { data, success } = await getAllProBrand()
            if (success) {
                this.brandList = data.map(item => {
                    return {
                        label: item.value,
                        value: item.key
                    }
                })
            } else {
                this.$message.error('获取采购人员列表失败')
            }
        },
        searchList() {
            this.ListInfo.styleCode = this.ListInfo.styleCode ? this.ListInfo.styleCode.replace(/\s+/g, "") : null;
            this.ListInfo.goodCode = this.ListInfo.goodCode ? this.ListInfo.goodCode.replace(/\s+/g, "") : null;
            this.ListInfo.providerName = this.ListInfo.providerName ? this.ListInfo.providerName.replace(/\s+/g, "") : null;
            if (this.checkList.length == 0) {
                this.$refs.summaryTable.searchList()
            } else {
                this.$refs.groupTable.getGroupList(this.checkList[0])
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.header {
    display: flex;
    margin-bottom: 10px;
}

.publicMargin {
    margin-right: 20px;
}

.detail ::v-deep .vxetoolbar20221212 {
    display: none !important;
}

::v-deep .el-badge__content {
    padding: 0 4px;
    top: 7px;
    right: 0;
}

.radioGrp {
    margin-bottom: 10px;
    display: flex;
}

::v-deep .el-checkbox__inner {
    border-radius: 7px !important;
}
</style>
