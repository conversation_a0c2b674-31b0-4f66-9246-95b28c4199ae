<template>
    <MyContainer>
        <vxetablebase ref="vxetable" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' border
            @sortchange='sortchange' :tableData='tableData' :tableCols="tableCols"
            :isSelection="false" :isSelectColumn="false" style="width: 100%; height:600px; margin: 0"
            :loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer> 
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { pageStyleCodeDirectorFullInfoWarDtlList } from '@/api/bookkeeper/styleCodeRptData'
import decimal from '@/utils/decimal.js'
const computedNum = (val) => {
    return val !== null ? decimal(val, 10000, 1, '/') + '万' : ''
}
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'indexGroupName', label: '系列编码' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderCount', label: '订单量', formatter: (row) => row.orderCount !== null ? row.orderCount : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'saleAmt', label: '销售金额', formatter: (row) => row.saleAmt !== null ? computedNum(row.saleAmt) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'zjzy', label: '占用资金', formatter: (row) => row.zjzy !== null ? computedNum(row.zjzy) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'profit33', label: '毛四利润', formatter: (row) => row.profit33 != null ? computedNum(row.profit33) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'profit5', label: '毛五利润', formatter: (row) => row.profit5 != null ? computedNum(row.profit5) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'profit6', label: '毛六利润', formatter: (row) => row.profit6 != null ? computedNum(row.profit6) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'profit6Index', label: '个人排名', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'bzProfit6Index', label: '类目排名', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'zjhbl', label: '回报率', formatter: (row) => row.zjhbl !== null ? row.zjhbl + '%' : '' },
] 
export default {
    name: "profileInformationStyleCodeDtl",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    props: {
        filter: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'profit6Index',
                isAsc: true,
                isStyleCodeIndex: true
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            dialogVisible:false,
            dialogFilter:{}
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async getList(type) { 
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const params = { ...this.filter, ...this.ListInfo };
                const { data, success } = await pageStyleCodeDirectorFullInfoWarDtlList(params)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                console.log(error);
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        } 
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
