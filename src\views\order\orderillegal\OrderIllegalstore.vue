<template>
  <container v-loading="pageLoading">
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchangeDetail' :summaryarry="detail.summaryarry"
            :tableData='detail.list' :tableHandles='tableHandles' :tableCols='detail.tableCols' :isSelection="false"
            :loading="detail.listLoading">
            </ces-table>
        <template #footer>
          <my-pagination
            ref="pagerDetail"
            :total="detail.total"
            :checked-count="detail.sels.length"
            @get-page="getDetailList"
          />
        </template>
  </container>
</template>

<script>
import container from '@/components/my-container/noheader'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatPlatform,formatSendWarehouse} from "@/utils/tools";
import { rulePlatform} from "@/utils/formruletools";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { 
  importOrderIllegal,
  getOrderIllegalShopList,
  exportOrderIllegalShop,
  getOrderIllegalWrhList,
  exportOrderIllegalWrh,
} from "@/api/order/orderdeductmoney"
const tableHandles=[
        {label:"导出仓库统计", handle:(that)=>that.onExportWrh()},       
      ];
export default {
  name: 'Roles',
  components: {cesTable, container, MyConfirmButton },
  props:{
      filter:{ }
  },
  data() {
    return {
      that:this,
      platformList: [],
      list: [],
      summaryarry:{},
      pager:{OrderBy:"platform",IsAsc:true},
      tableHandles:tableHandles,
      dialogVisible: false,
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,     
      deleteLoading: false,
      uploadLoading:false,
      fileHasSubmit:false,
      formtitle:"新增",
      fileList:[],
      selids:[],//选择的id
      detail:{
         pager:{OrderBy:"platform",IsAsc:false},
         list: [],
         tableCols:[
            {istrue:true,prop:'platform',label:'平台', width:'150',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
            {istrue:true,prop:'occurrenceTime',label:'日期', width:'150',sortable:'custom',formatter:(row)=>formatTime(row.occurrenceTime,"YYYY-MM-DD")},
            {istrue:true,prop:'sendWarehouse',label:'发货仓', width:'200',sortable:'custom',formatter:(row)=>row.sendWarehouseName},
            {istrue:true,prop:'amountPaid',label:'求和项：扣款金额', width:'auto',sortable:'custom',formatter:(row)=>parseFloat(row.amountPaid.toFixed(6))}
         ],
         listLoading: false,
         total:0,
         sels: [], 
      }
    }
  },
  async mounted() {
    await this.onSearch();  
  },
  methods: {
    //======================明细查询
    //获取查询条件
    getConditionDetail(){
      if (this.filter.timerange&&this.filter.timerange.length>1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      else {
        this.$message({message:"请先选择日期",type:"warning"});
        return false;
      }
      var pager = this.$refs.pagerDetail.getPager();
      var page  = this.detail.pager;
      const params = {
        ...pager,
        ...page,
        ... this.filter
      }

      return params;
    },
    //查询第一页
    async onSearch() {
      if (!this.filter.timerange) {this.$message({message: "请选择日期",type: "warning",});return;}  
      this.$refs.pagerDetail.setPage(1);
      await this.getDetailList();
    },
    //明细分页查询
    async getDetailList() {    
      var params=this.getConditionDetail();
      if(params===false){
            return;
      }
      this.detail.listLoading = true;
      const res = await getOrderIllegalWrhList(params)
      this.detail.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.detail.total = res.data.total;
      const data = res.data.list;
      this.detail.summaryarry=res.data.summary;
      if(this.detail.summaryarry)
        this.detail.summaryarry.amountPaid_sum=parseFloat(this.detail.summaryarry.amountPaid_sum.toFixed(6));
      data.forEach(d => {
        d._loading = false
      })
      this.detail.list = data
    },
    //排序查询
    async sortchangeDetail(column){
      if(!column.order)
        this.detail.pager={};
      else{
        this.detail.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    },
    //导出明细
    async onExportWrh(){
        var params=this.getConditionDetail();
        if(params===false){
              return;
        }
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportOrderIllegalWrh(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','订单违规扣款仓库统计_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary{
  margin-right: 7px;
}
</style>
