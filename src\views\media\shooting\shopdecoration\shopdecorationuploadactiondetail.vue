<template>
    <my-container v-loading="pageLoading" :element-loading-text="upmsginfo">
        <el-row  >
            <el-col :xs="12" :sm="12" :lg="12">
                <el-row v-for="(i,index ) in InitDataArrayOne" :key="index">
                    <el-col :xs="2" :sm="2" :lg="2">
                        &nbsp;
                    </el-col>
                    <el-col :xs="18" :sm="18" :lg="18">
                        <div>
                            <p>{{i.name}}</p>
                        </div> 
                        <div style="height: 300px; width: 380px; margin:10 0 10 0;">
                            <template>
                                <uploadfileforsuccess
                                :key="timer" 
                                    :ref="i.ref" 
                                    :uploadInfo="i.uploadinfo" 
                                    :limit="10000" 
                                    :islook="islook"
                                    :accepttyes="i.acctpye" 
                                    :uptype="i.uptype"
                                    :delfunction="deluplogimg">
                                </uploadfileforsuccess>
                            </template>
                        </div>

                    </el-col>
                </el-row>
            </el-col> 

            <el-col :xs="12" :sm="12" :lg="12">
                <el-row v-for="(i,index) in InitDataArrayTwo" :key="index">
                    <el-col :xs="2" :sm="2" :lg="2">
                        &nbsp;
                    </el-col>
                    <el-col :xs="18" :sm="18" :lg="18">
                        <div>
                            <p>{{i.name}}</p>
                        </div> 
                        <div style="height: 300px; width: 380px; margin:10 0 10 0;">
                        <uploadfileforsuccess 
                            :key="timer"
                            :ref="i.ref" 
                            :uploadInfo="i.uploadinfo" 
                            :limit="10000" 
                            :islook="islook"
                            :accepttyes="i.acctpye" 
                            :uptype="i.uptype"
                            :delfunction="deluplogimg">
                        </uploadfileforsuccess>
                        </div>
                    </el-col>
                </el-row>
            </el-col> 
        </el-row>
        <el-dialog title="上传失败结果" :visible.sync="dialogVisibleSyjout1" width="50%" @close="dialogVisibleSyjout1">
            <my-container style="height: 100%">
                <el-table :height="450" ref="MediamultipleTableout" row-key="id" :data="uploadinfos" highlight-current-row>
                    <el-table-column label="失败文件" width="200">
                        <template slot-scope="scope">
                            {{scope.row.file.name}}
                        </template>
                    </el-table-column>
                </el-table>
            </my-container>
        </el-dialog>
    </my-container>
</template>
<script>
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
import MyContainer from "@/components/my-container";
import uploadfileforsuccess from '@/views/media/shooting/uploadfileforsuccess' 
import { uploadSuccessAttachment ,checkShootingTaskAction,getUploadSuccessAttachment } from '@/api/media/shopdecoration';
export default { 
    components: { MyContainer ,uploadfileforsuccess},
    props:["rowinfo","islook"],
    data() {
        return {
            imgtype:".image/jpg,image/jpeg,image/png",
            rartype:".rar,.zip,.7z",
            psdtype:".psd,.psb",
            vediotype:".mp4,.mov,.vedio,.av,.wmv,.mpg,.mpeg,.rm,.flv,.swf",
            dialogVisibleSyjout1:false,
            curisChange:false,
            upmsginfo:'努力加载中',
            pageLoading:false, 
            uploadinfos:[],
            uploadtype:0,
            upmsgtxt:null,
            percentage:0,
            timer: '',
            InitDataArrayOne:[],
            InitDataArrayTwo:[]
        };
    },
    async mounted() {
        await this.onOpenUpload();
    }, 
    methods: {
        async  onOpenUpload(){
            var res = await getUploadSuccessAttachment({uploadType:6,shopDecorationTaskId:this.rowinfo,actionType:1});
            if(res?.success){
                //加载数据
                var uploadinfo1=[];
                var uploadinfo2=[]; 
                res.data.forEach(element => {
                    if(element.fileType == 1){
                        uploadinfo1.push(element);
                    }else if(element.fileType == 2){
                        uploadinfo2.push(element);
                    }
                });   
                this.timer = new Date().getTime();
                this.InitDataArrayOne=[];
                this.InitDataArrayTwo=[];
                
                this.InitDataArrayOne.push({ref:"u61",name:"成品JPG" ,uploadType:6,fileType:"1",acctpye:this.imgtype,uptype:"imgtype",uploadinfo:uploadinfo1});
                this.InitDataArrayTwo.push({ref:"u62",name:"源文件" ,uploadType:6,fileType:"2",acctpye:this.imgtype,uptype:"imgtype",uploadinfo:uploadinfo2});
         

               
                
                this.title="上传成果"; 
            }else{
                this.$message({ message: '数据获取失败，请稍后重试', type: "error" });
            }
        },
        async dialogVisibleSyjout(){
            await this.onOpenUpload(this.uploadType);
            this.dialogVisibleSyjout1 =false;
        },
        //移除操作
        deluplogimg(){
        },
        //判断是否改变
        getIsChange(){
            var ischange  = false;
            for(let num in this.InitDataArrayOne)
            { 
                var infos = this.$refs[this.InitDataArrayOne[num].ref][0].getChange();
                if(infos){
                    ischange =true;
                }
               
            }
            for(let num in this.InitDataArrayTwo)
            { 
                var infos = this.$refs[this.InitDataArrayTwo[num].ref][0].getChange();
                if(infos){
                    ischange =true;
                }
            }  
            return ischange;
        },
        getUploadInfo(){ 
            var retarray = [];
            for(let num in this.InitDataArrayOne)
            { 
                var infos = this.$refs[this.InitDataArrayOne[num].ref][0].getReturns();
                var uploadType = this.InitDataArrayOne[num].uploadType;
                var fileType = this.InitDataArrayOne[num].fileType;
                infos.data.forEach(function(item){
                    item.uploadType = uploadType
                    item.fileType = fileType
                    retarray.push(item);
                });
            }
            for(let num in this.InitDataArrayTwo)
            { 
                var infos = this.$refs[this.InitDataArrayTwo[num].ref][0].getReturns();
                var uploadType = this.InitDataArrayTwo[num].uploadType;
                var fileType = this.InitDataArrayTwo[num].fileType;
                infos.data.forEach(function(item){
                    item.uploadType = uploadType
                    item.fileType = fileType
                    retarray.push(item);
                });
            } 
            return retarray;
        },
         //提交上传的信息
         async sumbitUploadInfo()
         {
            var res =  await checkShootingTaskAction({taskid:this.rowinfo});
            if(!res?.success){
                return
            }
            if(!this.getIsChange()){
                this.$message({ message: '未发生改变，请勿提交', type: "error" });
                return;
            }
            //校验是否可以提交

            this.uploadinfos = this.getUploadInfo();
            this.pageLoading = true;
            this.tempoutlist = [];
            this.uploadinfos.forEach(element => { 
                this.tempoutlist.push(element); 
            });
            this.startIndex = this.tempoutlist.length;
           
            for (var i = 0; i < this.tempoutlist.length; i++) {
                var item = this.tempoutlist[i];
                this.atfterUplaodData = null;
                this.percentage = 0;
                this.startIndex = this.startIndex - 1;
                //判断是否需要上传  //只上传新加的文件
                if(item.filestaus == 0){
                    this.upmsgtxt = "正在上传："+ item.file.name 
                    this.upmsginfo =this.upmsgtxt +"   " +  this.percentage +"%";
                    await this.AjaxFile(item.file, 0, "");
                    if (this.atfterUplaodData != null) {
                        await this.afteruploadtempList(item, this.startIndex);
                    }
                }else{
                    this.upmsginfo = "正在处理："+item.fileName;
                    await this.afteruploadtempList(item, this.startIndex);
                }
                this.percentage = 0;
            }  
            this.pageLoading = false;
        },
        //上传完成进行业务处理，加放入datalist中，
        //startIndex == 0 判断全部上传完成，也代表文件的顺序，采用倒序排版
        async afteruploadtempList(item,startIndex){
            //进行业务处理
            var params = {
                ...item,
                shopDecorationTaskId:this.rowinfo,
                OrderNum: startIndex
            }
            if(item.filestaus == 0){
                params.url = this.atfterUplaodData.url;
                params.relativePath = this.atfterUplaodData.relativePath;
            }
            var res =  await uploadSuccessAttachment(params);
            //上传到数据记录中
            if(res?.success)
            {
                for (let num in this.uploadinfos) {
                    if (this.uploadinfos[num].uid == item.uid) {
                        this.uploadinfos.splice(num, 1)
                    }
                }
            }
            if (startIndex == 0) {
                this.upmsginfo = "上传完成！";
                this.$message({ message: '上传完成！', type: "success" });
                if (this.uploadinfos.length > 0) 
                {
                    this.$message({ message: '存在操作失败信息请查看，截图保存', type: "error" });
                    this.dialogVisibleSyjout = true;
                    this.uploadtype = item.uploadType;
                }else{
                    await this.onOpenUpload(item.uploadType);
                }
            }
        },
         //切片上传
        async AjaxFile(file, i, batchnumber) {
            var name = file.name; //文件名
            var size = file.size; //总大小shardSize = 2 * 1024 * 1024,
            var shardSize = 1 *1024 * 1024;
            var shardCount = Math.ceil(size / shardSize); //总片数
            if (i >= shardCount) {
                return;
            }
            //计算每一片的起始与结束位置
            var start = i * shardSize;
            var end = Math.min(size, start + shardSize);
            //构造一个表单，FormData是HTML5新增的
            i = i + 1;
            var form = new FormData();
            form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分
            form.append("batchnumber", batchnumber);
            form.append("fileName", name);
            form.append("total", shardCount); //总片数
            form.append("index", i); //当前是第几片
            const res = await xMTVideoUploadBlockAsync(form);
            if (res?.success) {
                this.percentage = (i * 100 / shardCount).toFixed(2);
                this.upmsginfo =this.upmsgtxt +"   " +  this.percentage +"%";
                if (i == shardCount) {
                    this.atfterUplaodData = res.data;
                } else {
                    await this.AjaxFile(file, i, res.data);
                }
            } else {
                this.$message({ message: res?.msg, type: "warning" });
            }
        },   
    },
};
</script>

