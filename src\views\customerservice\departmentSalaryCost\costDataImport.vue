<template>
  <!-- 成本数据导入 -->
  <container v-loading="pageLoading">
      <template #header>
          <el-row>

                  <el-input v-model.trim="filter.name" placeholder="请输入姓名" style="width: 140px; " :maxlength="50" clearable>
                  </el-input>

                  <!-- <el-cascader
                 style="width: 200px; margin-left: 10px"
                  v-model="filter.subgroupList"
                  :options="deptList"
                  filterable
                  placeholder="请选择分组"
                  :props="props"
                  collapse-tags
                  :show-all-levels="false"
                  clearable></el-cascader> -->

                  <el-select v-model="filter.subgroup" placeholder="请选择分组" clearable multiple collapse-tags filterable>
                  <el-option v-for="item in subgroupListArr" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>

                 <el-select v-model="filter.platform" placeholder="请选择平台" style="width: 160px; margin-left: 10px" clearable>
                  <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>


                 <el-select v-model="filter.region" placeholder="请选择区域" filterable style="width: 160px; margin-left: 10px" clearable>
                     <el-option :label="item.label" :value="item.value" v-for="(item,index) in quyu" :key="index"  />
                 </el-select>

                 <el-date-picker
                  type="month"
                  format="yyyy-MM"
                  value-format="yyyy-MM-dd"
                  style="width: 160px; margin-left: 10px"
                  v-model="filter.costDate"
                  clearable
                  placeholder="选择月份">
                </el-date-picker>

                <el-select v-model="filter.importType" placeholder="导入类别" filterable style="width: 160px; margin-left: 10px" clearable>
                     <el-option :label="item.label" :value="item.value" v-for="(item,index) in exportStatus" :key="index" />
                 </el-select>


             <el-button-group style="margin-left: 10px">
              <el-button type="primary" @click="onSearch">查询</el-button>

             </el-button-group>
             <el-button type="primary" style="margin-left: 10px" @click="dialogVisiblejlfuc">历史记录</el-button>

             <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button
                  type="primary" icon="el-icon-share" @command="handleCommand">
                  导入
                  <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="1">预估月薪</el-dropdown-item>
                      <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="2">真实月薪</el-dropdown-item>
                  </el-dropdown-menu>
              </el-dropdown>

             <el-button type="primary" @click="downFile" style="width: 100px; margin-left: 10px">下载模板</el-button>


          </el-row>
      </template>
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
          :isSelectColumn="false" :showsummary='true' :summaryarry='summaryarry' :tablefixed='false' :tableData='tableData' :tableCols='tableCols'
          :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:95%;margin: 0">
      </ces-table>
      <!-- <template #footer>
          <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
      </template> -->
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />

      <!-- //导入 -->
    <el-dialog :title="`导入${importType == '1' ? '预估月薪' : '真实月薪'}`" :visible.sync="dialogVisibleSyj" width="30%" v-dialogDrag>
      <el-date-picker
        format="yyyy-MM"
        value-format="yyyy-MM-dd"
        v-model="importcostDate"
        type="month"
        placeholder="选择月份">
      </el-date-picker>

      <el-upload ref="upload2" style="width: 100%;" class="upload-demo" :auto-upload="true" :multiple="false" :limit="1" action :file-list="[filedata.url]"
        accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2" :on-change="onsuccess">
        <template #trigger>
          <el-button  v-loading="uploadLoading" size="small" type="primary">选取文件</el-button>
        </template>
        <el-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传
        </el-button>
      </el-upload>
      <span v-if="filedata.url" style="margin-top: 20px;">{{filedata.fileName}}<i class="el-icon-close" style="margin-left: 50px;" @click="filedata = {}"></i></span>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisibleSyj = false">关闭</el-button>
    </span>
  </el-dialog>

    <!-- //历史记录 -->
    <el-dialog title="历史记录" :visible.sync="dialogVisibleHis" width="60%" v-dialogDrag>
      <el-table v-loading="reportloading" class="tableone" :data="hisreportData" style="width: 100%;overflow: auto">
          <el-table-column prop="updateTime" label="时间" width="180" align="center">
          </el-table-column>
          <el-table-column prop="updateUserName" label="姓名" width="180" align="center">
            <template slot-scope="scope">
              {{scope.row.historyRecordName}}
            </template>
          </el-table-column>
          <el-table-column prop="fileName" label="文件名" align="center">
          </el-table-column>
          <!-- 后端未存文件，暂时不打开 -->
          <el-table-column label="操作" align="center" width="120">
            <template slot-scope="scope">
              <el-button type="text" v-if="scope.row.fileUrl" @click="download(scope.row)" size="small">下载</el-button>
            </template>
          </el-table-column>
      </el-table>
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :page-sizes="[50, 100, 150, 200]" :current-page.sync="reportFilter.currentPage"
          :page-size="reportFilter.pageSize" layout="total,sizes, prev, pager, next" :total="reportFilter.total">
      </el-pagination>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisibleHis = false">关闭</el-button>
    </span>
  </el-dialog>



  </container>
 </template>
 <script>
 import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
 import { formatTime } from "@/utils";
 import dayjs from "dayjs";
 import container from '@/components/my-container';
 import cesTable from '@/components/Table/table.vue';
 import YhImgUpload1 from "@/components/upload/yh-img-upload1.vue";
 import { formatLinkProCode, platformlist } from '@/utils/tools'
 import MyConfirmButton from "@/components/my-confirm-button";
 import {
  importBaseSupermarketGoodAsync,
  getPageBaseSupermaketGoodAsync,
  updateBaseSupermaketGoodAsync,
  delBaseSupermaketGoodAsync,
  getGoodTypeAsync,
  saveGoodTypeAsync,
  updateBaseSupermaketGoodStockAsync
 } from '@/api/profit/orderfood';
 import { getOrderFoodMenuProvier, getAreaSetList, getUserOrderDetailList, quyuList } from '@/api/profit/orderfood';
 import fliterjs from "@/views/customerservice/departmentSalaryCost/fliterjs.js";

 import { customerCostImportPage, customerCostImportRemove, customerCostDataImport,  customerCostHistoryRecordPage } from '@/api/bladegateway/yunhangiscustomer.js';

 const tableCols = [

  { istrue: true, prop: 'region', label: '区域', width: '100', sortable: 'custom', formatter: (row) => row.regionName ? row.regionName : null },
  { istrue: true, prop: 'platform', label: '所在平台', width: '100', sortable: 'custom', formatter: (row) => row.platformName ? row.platformName : null },
  // { istrue: true, prop: 'subgroup', label: '分组',  width: '160', sortable: 'custom', },
  { istrue: true, prop: 'subgroup', label: '分组',  width: '200', sortable: 'custom', formatter: (row)=> row.subgroupName },

  { istrue: true, prop: 'name', label: '姓名', width: '120', sortable: 'custom',  },
  { istrue: true, prop: 'yfAttendanceDays', label: '当月应出勤天数', width: '120', sortable: 'custom'  },
  { istrue: true, prop: 'sfAttendanceDays', label: '实际出勤天数', width: '120',  sortable: 'custom' },
  { istrue: true, prop: 'yfBaseSalary', label: '应发底薪', width: '120', sortable: 'custom'  },
  { istrue: true, prop: 'yfPerformance', label: '应发绩效', width: '120',  sortable: 'custom' },
  { istrue: true, prop: 'commission', label: '提成', width: '120',  sortable: 'custom' },
  { istrue: true, prop: 'fullAttendanceAward', label: '全勤奖', width: 'auto', sortable: 'custom'  },

  { istrue: true, prop: 'houseSupplement', label: '房补', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'otherCosts', label: '其他成本', width: '120', sortable: 'custom'  },

  { istrue: true, prop: 'totalDeduction', label: '扣款合计', width: '120', sortable: 'custom'  },
  { istrue: true, prop: 'sfAmount', label: '实发金额', width: '120', sortable: 'custom'  },

  { istrue: true, prop: 'dailySfAmount', label: '日均实发金额', width: '120', sortable: 'custom'  },
  { istrue: true, prop: 'performanceScore', label: '绩效得分', width: '120', sortable: 'custom'  },

  { istrue: true, prop: 'otherCostRemarks', label: '其他成本备注说明', width: '150',   },
  { istrue: true, prop: 'choseTime', label: '时间', width: '120',  sortable: 'custom', formatter: (row) => row.choseTime ? formatTime(row.choseTime, 'YYYY-MM') : null },

  { istrue: true, prop: 'updateTime', label: '导入时间', width: '120', sortable: 'custom'  },
  { istrue: true, prop: 'importType', label: '导入类别', width: '120', formatter: (row) => row.importType == 2 ? '真实月薪' : '预估月薪', sortable: 'custom'  },
  {
        istrue: true, type: "button", label: '操作', align: 'center', sortable: false, width: "100", fixed: "right",
        btnList: [{ label: "删除", type: "danger", handle: (that, row) => that.ondelrow(row) }]
    }
 ];

 const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
 const endDate = startDate;
 //const endDate = formatTime(new Date(), "YYYY-MM-DD");

 export default ({
  name: "Users",
  mixins: [fliterjs],
  components: { container, cesTable, YhImgUpload1, MyConfirmButton },
  data () {
      return {
          dialogVisibleStock: false,
          dialogVisibleSyj: false,
          dialogVisibleHis: false,
          reportloading: false,
          hisreportData: [],
          fileList: [],
          gysList: [],
          importType: 0,
          dialogEdit: false,
          stockForm: {
              stock: 0,
              id: 0
          },
          editForm: {
          },
          form: {
              goodTypeList: [],
          },
          quyuList: [],
          dialogMenuType: false,
          uploadLoading: false,
          dialogVisibleData: false,
          that: this,
          reportFilter: {
            pageSize: 50,
            currentPage: 1,
          },
          filedata: {},
          filter: {
             //  timerange: [startDate, endDate],
              // orderMenuDateRange: [
              //     formatTime(dayjs().subtract(30, "day"), "YYYY-MM-DD"),
              //     formatTime(new Date(), "YYYY-MM-DD"),
              // ],
              // zoneName: '',
              // orderMenuStartDate: startDate,
              // orderMenuEndDate: endDate,
          },
          platformlist: platformlist,
          tableCols: tableCols,
          tableHandles: null,
          tableData: [],
          total: 0,
          importcostDate: '',
          pager: { OrderBy: "", IsAsc: false },
          listLoading: false,
          pageLoading: false,
          summaryarry: {},
          sels: [],
      };
  },
  async mounted () {
     //  await this.getgyss();
      // await this.getGoodType();
      await this.onSearch();
  },
  methods: {
    async download(row) {
        var xhr = new XMLHttpRequest();
        xhr.open('GET', row.fileUrl, true);
        xhr.responseType = 'arraybuffer'; // 返回类型blob
        xhr.onload = function () {
            if (xhr.readyState === 4 && xhr.status === 200) {
                let blob = this.response;
                console.log(blob);
                // 转换一个blob链接
                // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，
                // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定
                let downLoadUrl = window.URL.createObjectURL(new Blob([blob], { type: 'video/mp4' }));
                // 视频的type是video/mp4，图片是image/jpeg
                // 01.创建a标签
                let a = document.createElement('a');
                // 02.给a标签的属性download设定名称
                a.download = row.fileName;
                // 03.设置下载的文件名
                a.href = downLoadUrl;
                // 04.对a标签做一个隐藏处理
                a.style.display = 'none';
                // 05.向文档中添加a标签
                document.body.appendChild(a);
                // 06.启动点击事件
                a.click();
                // 07.下载完毕删除此标签
                a.remove();
            };
        };
        xhr.send();

    },
    async dialogVisiblejlfuc(){
      this.dialogVisibleHis = true;

      // this.reportFilter.operationItem = '客服成本'
      this.reportloading = true;
      const params = {
        ...this.reportFilter
      };
      var res = await  customerCostHistoryRecordPage(params);
      this.reportloading = false;
      if(!res.success){
          return
      }
      this.hisreportData = res.data.list;
      this.reportFilter.total = res.data.total;
    },
    searchReport(){
      this.reportFilter.currentPage = 1;
      this.dialogVisiblejlfuc();
    },
    handleCurrentChange(val){
      this.reportFilter.currentPage = val;
      this.searchReport();
    },
    handleSizeChange(val){
      this.reportFilter.pageSize = val;
      this.searchReport();
    },
    async downFile() {
        window.open("/static/excel/kefuxinzi/成本数据表.xlsx", "_blank");
    },
    async onSubmitupload2() {
      if(!this.importcostDate){
        this.$message.error("请选择导入月份");
        return;
      }
      if (!this.filedata.url) {
        this.$message({ message: "请先上传文件", type: "warning" });
        // this.uploadLoading=false;
        return false;
      }
      const form = new FormData();
      form.append("fileUrl", this.filedata.url);
      form.append("fileName", this.filedata.fileName);
      form.append("importType", this.importType);
      form.append("costDate", this.importcostDate);
      const res = await customerCostDataImport(form);
      if(!res.success){
        return
      }
      this.dialogVisibleSyj = false;
      this.onSearch();
      this.$message({ message: '上传成功,正在导入中...', type: "success" });

      // this.$refs.upload2.submit()
    },
    onsuccess(file, fileList){
      // this.fileList = fileList;
      this.fileList = [];
    },
    async uploadSuccess2(response, file, fileList) {
      //
      // this.AjaxFile(file, 0,"");
      //

      fileList.splice(fileList.indexOf(file), 1);
    },
    async AjaxFile(file,i,batchnumber) {
        var name = file.name;
        var size = file.size;
        var shardSize = 15 * 1024 * 1024;
        var shardCount = Math.ceil(size / shardSize);
        if (i >= shardCount) {
            return;
        }
        var start = i * shardSize;
        var end = Math.min(size, start + shardSize);
        i=i+1;
        var form = new FormData();
        form.append("data", file.slice(start, end));
        form.append("batchnumber", batchnumber);
        form.append("fileName", name);
        form.append("total", shardCount);
        form.append("index", i);
        try {
            const res = await xMTVideoUploadBlockAsync(form);
            if (res?.success) {
                this.uploadprogressPercentage = (i / shardCount).toFixed(2) * 100;
                if (i == shardCount) {
                    res.data.fileName = name;
                    // res.data.uid = file.uid;
                    res.data.upLoadPhotoId = 0;
                    this.filedata = res.data;
                    // this.$message.success('上传完成！')
                } else {
                    await this.AjaxFile(file, i, res.data);
                }
            } else {
                this.$message({ message: res?.msg, type: "warning" });
            }
        } catch (error) {
            this.uploading = false;
            this.$message.error('上传文件异常！')
            this.$refs.upload2.clearFiles();
            this.retdata = [];
        }
    },
    async uploadFile2(item) {
      console.log("打印item",item)
      this.uploadLoading = true;
      await this.AjaxFile(item.file, 0,"");
      setTimeout(()=>{
        this.uploadLoading = false;
      }, 0)
      // const form = new FormData();
      // form.append("file", item.file);
      // form.append("importType", this.importType);
      // form.append("costDate", this.importcostDate);
      // const res = await customerCostDataImport(form);
      // if(!res.success){
      //   return
      // }
      // this.dialogVisibleSyj = false;
      // this.onSearch();
      // this.$message({ message: '上传成功,正在导入中...', type: "success" });
    },
    ondelrow(row){
            this.$confirm("是否确定删除, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                this.addressListLoading = true;
                const form = new FormData();
                form.append("ids", row.id);
                var ret = await customerCostImportRemove(form);

                // var ret = await customerCostImportRemove({ ids: row.id });
                this.addressListLoading = false;
                this.onSearch();
            });
     },
    handleCommand(val){
      this.importType = val;
      this.importcostDate = '';
      this.filedata = {};
      this.dialogVisibleSyj = true;
      console.log("选择",val)
    },
     async getquyu(){
         const res = await getAreaSetList({getLevel: 1});
         if(!res.success){
             return
         }
         this.quyuList = res.data;
         if(!this.filter.zoneName){
             this.filter.zoneName = this.quyuList[0];
         }
     },
      async getsize(){
       let params = {

       }
       const res = await getOrderFoodMenuProvier();
          if(!res.success){
              return
          }
          this.gysList = res.data;
      },
      async getgyss(){
          const res = await getOrderFoodMenuProvier();
          if(!res.success){
              return
          }
          this.gysList = res.data;
          this.filter.gysName = this.gysList[0];
      },
      async getGoodType () {
          const res = await getGoodTypeAsync();
          this.form.goodTypeList = res?.data;
      },
      // async onSearch () {
      //   console.log("查询条件", this.filter)
      //     this.$refs.pager.setPage(1);
      //     this.$refs.table.currentLvl = 9;
      //     await this.getquyu();

      //     await this.getList();

      // },
      async sortchange (column) {
          if (!column.order)
              this.pager = {};
          else
              this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
          this.$refs.pager.setPage(1);
          await this.onSearch();
      },
      async getList () {
          if (this.filter.orderMenuDateRange && this.filter.orderMenuDateRange.length > 0) {
              this.filter.orderMenuStartDate = this.filter.orderMenuDateRange[0];
              this.filter.orderMenuEndDate = this.filter.orderMenuDateRange[1];
          }
          var that = this;
          this.listLoading = true;
          var pager = this.$refs.pager.getPager();
          const params = { ...pager, ...this.pager, ...this.filter, ...this.myfilter };
          const res = await customerCostImportPage(params).then(res => {
              that.total = res.data?.total;
              that.tableData = res.data?.list;
              that.summaryarry = res.data?.summary;
          });


          this.listLoading = false;
      },
  }
 })
 </script>
 <style scoped>
.tableone ::v-deep .el-table__body-wrapper{
  height: 420px !important;
  overflow-y: auto;
}
::v-deep .el-cascader__search-input{
  margin: 2px 0 2px 5px;
  height: 18px;
  align-items: center;
}
::v-deep .el-select__tags-text {
  max-width: 60px;
}
 </style>

