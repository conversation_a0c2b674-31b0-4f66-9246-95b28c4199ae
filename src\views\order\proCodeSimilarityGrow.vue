<template>
    <my-container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="创建日期:">
                    <el-date-picker style="width: 220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :clearable="true" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item label="系列编码:">
                    <inputYunhan :key="'3'" :width="'120px'" ref="childstyleCode" v-model="filter.styleCode" :inputt.sync="filter.styleCode"
                        placeholder="系列编码" :clearable="true" @callback="callbackGoodsCode" title="系列编码"></inputYunhan>
                </el-form-item>
                <el-form-item label="运营组:">
                    <el-select v-model="filter.groupId" style="width: 100px" placeholder="请选择" :clearable="true"
                        :collapse-tags="true" filterable @change="onSearch">
                        <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item label="采购:">
                    <el-select v-model="filter.brandId"  clearable filterable :collapse-tags="true" placeholder="请选择采购员" style="width: 100px" >
                        <el-option v-for="item in brandlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="毛三正负利润:">
                    <el-select filterable v-model="filter.isProfit" collapse-tags placeholder="毛利3" style="width: 100px">
                        <el-option label="全部" />
                        <el-option label="正利润" :value="1" />
                        <el-option label="负利润" :value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="checkPermission('proCodeSimilarityGrow')" label="是否公共款:">
                    <el-select filterable v-model="filter.isCommon" collapse-tags placeholder="公共款" style="width: 100px">
                        <el-option label="全部" />
                        <el-option label="是" :value="1" />
                        <el-option label="否" :value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="checkPermission('proCodeSimilarityGrow')" label="颜色选择">
                    <el-select filterable v-model="filter.colorType" collapse-tags placeholder="颜色选择" style="width: 100px"
                        clearable>
                        <el-option label="绿色" :value="'green'" />
                        <el-option label="红色" :value="'red'" />
                        <el-option label="黄色" :value="'yellow'" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="checkPermission('proCodeSimilarityGrow')" label="列选择">
                    <el-select filterable v-model="salesDayType" collapse-tags placeholder="列选择" style="width: 180px"
                        multiple clearable>
                        <el-option v-for="item in fpPhotoLqNameList" :key="item.val" :label="item.name" :value="item.val" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="checkPermission('proCodeSimilarityGrow')" label="是否受保护状态:">
                    <el-select filterable v-model="filter.privateStatus" collapse-tags placeholder="保护状态"
                        style="width: 100px">
                        <el-option label="全部" />
                        <el-option label="是" :value="1" />
                        <el-option label="否" :value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label="库存投资回报率:">
                    <el-button-group>
                        <el-button style="padding: 0;margin: 0;">
                            <el-input-number placeholder="库存投资回报率" :controls="false" :min=-10 :max=10
                                v-model="filter.minInvestRate" style="width: 110px"></el-input-number>
                        </el-button>
                        <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">至</el-button>
                        <el-button style="padding: 0;margin: 0;">
                            <el-input-number placeholder="库存投资回报率" :controls="false" :min=-10 :max=10
                                v-model="filter.maxInvestRate" style="width: 110px"></el-input-number>
                        </el-button>
                    </el-button-group>
                </el-form-item>
                <el-form-item label="月销投资回报率:">
                    <el-button-group>
                        <el-button style="padding: 0;margin: 0;">
                            <el-input-number placeholder="月销投资回报率" :controls="false" :min=-10 :max=10
                                v-model="filter.minMonthReportRate" style="width: 110px"></el-input-number>
                        </el-button>
                        <el-button style="padding-left:0;padding-right:0;margin-left:0;margin-right:0;">至</el-button>
                        <el-button style="padding: 0;margin: 0;">
                            <el-input-number placeholder="月销投资回报率" :controls="false" :min=-10 :max=10
                                v-model="filter.maxMonthReportRate" style="width: 110px"></el-input-number>
                        </el-button>
                    </el-button-group>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="clionSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="true" :tablefixed="true"
            @select="selectchange" :tableHandles='tableHandles' @cellclick="cellclick" :loading="listLoading"
            :hasexpandRight="true">
            <template slot="extentbtn">
                <el-button-group v-if="lastUpdateTime != null">
                    <el-button style="margin: 0" @click="getimportlist">
                        {{ lastUpdateTime }}
                    </el-button>
                </el-button-group>
            </template>
            <!-- <template slot="right">
                <el-table-column :width="210" label="图表" fixed="right">
                    <template slot-scope="scope">
                        <div style="height: 120px;width:100%;margin-left: -20px;" :ref="'echarts' + scope.row.styleCode"
                            v-loading="echartsLoading"></div>
                    </template>
                </el-table-column>
            </template> -->
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog :visible.sync="detail.visible" :show-close="false" width="80%" v-dialogDrag>
            <!-- <el-button type="primary" @click="onSearchDetail">查询</el-button> -->
            <series-goods :filter="detail.filter" ref="seriesGoods" style="height: 480px">
            </series-goods>
        </el-dialog>

        <el-dialog title="设置状态" :visible.sync="setPrivateVisible" :close-on-click-modal="false" @close="onCloseSetForm"
            width="20%">
            <el-form ref="setForm" :model="setForm" label-width="110px" :rules="setFormRules">
                <el-radio v-model="setForm.status" label="1">是</el-radio>
                <el-radio v-model="setForm.status" label="0">否</el-radio>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click.native="setPrivateVisible = false">取消</el-button>
                    <my-confirm-button type="submit" :validate="setFormValidate" :loading="addLoading"
                        @click="onSetSubmit" />
                </div>
            </template>
        </el-dialog>
        <el-dialog :visible.sync="dialogAddVisible" :show-close="false" width="80%" v-dialogDrag>
            <procodesimilaritygrowdrawer ref="procodesimilaritygrowdrawer" style="height: 660px">
            </procodesimilaritygrowdrawer>
        </el-dialog>

        <!-- 时间线弹框 -->
        <el-dialog title="" :visible.sync="importtimedialogVisible" v-dialogDrag width="30%">
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <span></span>
                </div>
                <div style="height:400px;overflow-y:auto" class="text item">
                    <el-alert v-for="item in importtimelist" :key="item" title="" type="success" :closable="false">
                        计算时间 : {{ item }}
                    </el-alert>
                </div>
            </el-card>
        </el-dialog>

    </my-container>
</template>

<script>
import inputYunhan from "@/components/Comm/inputYunhan";
import { Loading } from 'element-ui';
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { formatmoney, tothousands } from "@/utils/tools";
import {
    getTbProCodeSimilarityGrow, setTbProCodeSimilarityGrowProviteStatus, ExportTbProCodeSimilarityGrow,
    getSeriesGoodsCode, getProCodeSimilarityGroupImportLogTime, getLastUpdateGroupTime, proCodeSimilarityReceivIsGroupId, addProCodeSimilarityReceiv
} from "@/api/order/procodesimilarity"
import SeriesGoods from './procodesimilaritygrow/SeriesGoodsGrow.vue'
import procodesimilaritygrowdrawer from './procodesimilaritygrow/procodesimilaritygrowdrawer.vue'
import { getGroupKeyValue } from "@/api/operatemanage/base/product";
import { getAllProBrand } from '@/api/inventory/warehouse'

//格式化money列：大于1 时，去掉小数点，小于1时保留小数点
// var myformatmoney = function (value) {
// //   var money= formatmoney(
// //     Math.abs(value) > 1 ? Math.round(value,2) : Math.round(value, 1)
// //    );
//    var money = tothousands(
//     Math.round(value * 10) / 10,1
//    )
//   return money
// };
var thoundszero = function (value) {
    var money = tothousands(
        Math.round(value)
    )
    return money
};

const tableCols = [
    { istrue: true, fixed: true, prop: 'styleCode', label: '系列编码', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showDetail(row) },
    { istrue: true, fixed: true, prop: 'goodsCodeCount', label: '编码数', width: '54', sortable: 'custom', type: 'click', handle: (that, row) => that.showGoodsCode(row) },
    { istrue: true, fixed: true, prop: 'goodsImage', label: '图片', width: '50', type: 'imageGoodsCode', goods: { code: 'goodsCode', name: 'goodsName' } },
    { istrue: true, prop: 'invAmountTotal', label: '库存资金', permission: "proCodeSimilarityGrow", tipmesg: '', width: '70', sortable: 'custom', formatter: (row) => thoundszero(row.invAmountTotal) },
    { istrue: true, prop: 'profit', label: '近30天毛三', permission: "proCodeSimilarityprofit", tipmesg: '', width: '72', sortable: 'custom', formatter: (row) => thoundszero(row.profit) },
    { istrue: true, prop: 'investRate', label: '库存投资回报率', permission: "proCodeSimilarityGrow", tipmesg: '近30天毛三/库存资金', width: '81', sortable: 'custom', formatter: (row) => !row.investRate ? ' ' : (row.investRate * 100).toFixed(1) + '%' },
    { istrue: true, prop: 'monthReportRate', label: '月销投资回报率', permission: "proCodeSimilarityGrow", tipmesg: '近30天毛三/月销售成本', width: '83', sortable: 'custom', formatter: (row) => !row.monthReportRate ? ' ' : (row.monthReportRate * 100).toFixed(1) + '%' },
    // {istrue:true, fixed:true, prop:'platform',label:'平台占比', type:'html', width:'380',tipmesg:'平台利润比例，库存资金比例',
    //         formatter:(row)=>`<div> 利润占比： 淘宝:${(row.taoEarningsRate*100).toFixed(2)}%， 拼多多:${(row.pinEarningsRate*100).toFixed(2)}%， 淘工厂:${ (row.gongEarningsRate*100).toFixed(2)}%</div><br/>
    //                             <div>库存占用： 淘宝:${(row.taoinvAmounRate*100).toFixed(2)}%，拼多多:${(row.pinvAmounRate*100).toFixed(2)}%， 淘工厂:${ (row.gonginvAmounRate*100).toFixed(2)}%</div><br/>`},classname: 'columnstock',
    { istrue: true, prop: 'masterStock', label: '库存数量', permission: "proCodeSimilarityGrow", tipmesg: '', width: '82', sortable: 'custom', },
    { istrue: true, fixed: true, prop: 'groupId', label: '编码负责人', tipmesg: '', width: '105', formatter: (row) => !row.groupName ? '未知' : row.groupName },
    { istrue: true, fixed: true, prop: 'brandName', label: '采购负责人', tipmesg: '', width: '105', formatter: (row) => !row.brandName ? '未知' : row.brandName },
    { istrue: true, prop: 'orderCount', label: '订单量', permission: "proCodeSimilarityGrow", width: '75', sortable: 'custom', },
    { istrue: true, prop: 'salesDay30Predict', propheight: 'salesDay30Height', label: '近30天销量成本预计售卖天数', permission: "proCodeSimilarityGrow", align: 'center', width: '90', sortable: 'custom', formatter: (row) => !row.salesDay30Predict ? ' ' : row.salesDay30Predict.toFixed(0), type: 'mancolor' },
    { istrue: true, prop: 'salesDay15Predict', propheight: 'salesDay15Height', label: '近15天销量成本预计售卖天数', permission: "proCodeSimilarityGrow", align: 'center', width: '90', sortable: 'custom', formatter: (row) => !row.salesDay15Predict ? ' ' : row.salesDay15Predict.toFixed(0), type: 'mancolor' },
    { istrue: true, prop: 'salesDay7Predict', propheight: 'salesDay7Height', label: '近7天销量成本预计售卖天数', permission: "proCodeSimilarityGrow", align: 'center', width: '90', sortable: 'custom', formatter: (row) => !row.salesDay7Predict ? ' ' : row.salesDay7Predict.toFixed(0), type: 'mancolor' },
    { istrue: true, prop: 'salesDay3Predict', propheight: 'salesDay3Height', label: '近3天销量成本预计售卖天数', permission: "proCodeSimilarityGrow", align: 'center', width: '90', sortable: 'custom', formatter: (row) => !row.salesDay3Predict ? ' ' : row.salesDay3Predict.toFixed(0), type: 'mancolor' },
    { istrue: true, prop: 'salesDay3', label: '3日销量', permission: "proCodeSimilarityGrow", tipmesg: '', width: '56', sortable: 'custom', },
    { istrue: true, prop: 'salesDay3Cost', label: '3日销售成本', permission: "proCodeSimilarityGrow", tipmesg: '', width: '67', sortable: 'custom', formatter: (row) => thoundszero(row.salesDay3Cost) },
    { istrue: true, prop: 'salesDay7', label: '7日销量', permission: "proCodeSimilarityGrow", tipmesg: '', width: '57', sortable: 'custom', },
    { istrue: true, prop: 'salesDay7Cost', label: '7日销售成本', permission: "proCodeSimilarityGrow", tipmesg: '', width: '68', sortable: 'custom', formatter: (row) => thoundszero(row.salesDay7Cost) },
    { istrue: true, prop: 'salesDay15', label: '15日销量', permission: "proCodeSimilarityGrow", tipmesg: '', width: '61', sortable: 'custom', },
    { istrue: true, prop: 'salesDay15Cost', label: '15日销售成本', permission: "proCodeSimilarityGrow", tipmesg: '', width: '74', sortable: 'custom', formatter: (row) => thoundszero(row.salesDay15Cost) },
    { istrue: true, prop: 'salesDay30', label: '月销量', permission: "proCodeSimilarityGrow", tipmesg: '', width: '66', sortable: 'custom', },
    { istrue: true, prop: 'salesDay30Cost', label: '月销售成本', permission: "proCodeSimilarityGrow", tipmesg: '', width: '70', sortable: 'custom', formatter: (row) => thoundszero(row.salesDay30Cost) },
    { istrue: true, prop: 'goodsCreatedTime', label: '创建时间', permission: "proCodeSimilarityGrow", tipmesg: '', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'pricingTime', label: '最近核价时间', tipmesg: '', width: '110', sortable: 'custom', },
    // { istrue: true, prop: 'brandId', label: '最新核价人员', tipmesg: '', width: '110', sortable: 'custom', formatter: (row) => !row.brandName ? '未知' : row.brandName },
    //{istrue:true,prop:'changePrice',label:'价格变动', tipmesg:'', width:'110',sortable:'custom',},
    // { istrue: true, prop: 'ricePrice', label: '涨价', tipmesg: '', width: '110', sortable: 'custom', },
    // { istrue: true, prop: 'dropPrice', label: '降价', tipmesg: '', width: '110', sortable: 'custom', },
    { istrue: true, prop: 'isCommon', label: '是否公共款', tipmesg: '', width: '100', tipmesg: '四十五天毛三/库存资金比例小于10%为公共款', sortable: 'custom', type: 'click', formatter: (row) => row.isCommon == true ? '是' : '否', style: (that, row) => that.renderwithholdStatus(row), },
    { istrue: true, prop: 'privateStatus', label: '是否受保护状态', tipmesg: '', width: 'auto', tipmesg: '受保护系列只有操作人可以看到', sortable: 'custom', type: 'click', formatter: (row) => row.privateStatus == '1' ? '是' : '否', style: (that, row) => that.renderprivateStatus(row), },
    { istrue: true, type: 'button', label: '认领产品', btnList: [{ label: "认领", handle: (that, row) => that.onSimilarityReceiv(row) }] },
    { istrue: true, prop: 'proCodeSimilarityReceive.receiveDate', label: '认领时间', tipmesg: '', width: 'auto', tipmesg: '', formatter: (row) => row.proCodeSimilarityReceive.receiveDate },
    { istrue: true, prop: 'proCodeSimilarityReceive.receivePerson', label: '认领人', tipmesg: '', width: 'auto', tipmesg: '', formatter: (row) => row.proCodeSimilarityReceive.receivePerson },
    { istrue: true, prop: 'proCodeSimilarityReceive.receiveGroup', label: '认领小组', tipmesg: '', width: 'auto', tipmesg: '', formatter: (row) => row.proCodeSimilarityReceive.receiveGroup },
]

const tableHandles = [
    { label: "导出", type: "primary", handle: (that) => that.onExport() },
    { label: '设置公共状态状态', handle: (that) => that.onSetProvateStatus(2), permission: "onSetProvateStatus" },
    { label: '设置受保护状态', handle: (that) => that.onSetProvateStatus(1), permission: "onSetProvateStatus" }
]


const startDate = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");


export default {
    name: 'YunHanAdminProCodeSimilarityGrow',
    components: { MyContainer, MyConfirmButton, cesTable, MySearch, MySearchWindow, SeriesGoods, procodesimilaritygrowdrawer, inputYunhan },

    data() {
        return {
            that: this,
            filter: {
                styleCode: null,
                isProfit: null,
                isCommon: null,
                privateStatus: null,
                groupId: null,
                brandId: null,
                minInvestRate: undefined,
                maxInvestRate: undefined,
                minMonthReportRate: undefined,
                maxMonthReportRate: undefined,
                colorType: 'red',
                startDate: null,
                endDate: null,
                startGoodsCreatedTime: null,
                endGoodsCreatedTime: null,
                timerange: [],
            },
            fpPhotoLqNameList: [{ name: '近3天销量', val: '3' }, { name: '近7天销量', val: '7' }, { name: '近15天销量', val: '15' }, { name: '近30天销量', val: '30' }],
            salesDayType: ['3'],
            brandlist: [],
            list: [],
            groupList: [],
            goodslist: [],
            importtimelist: [],
            summaryarry: {},
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            sels: [],
            pager: { OrderBy: "pricingTime", IsAsc: false },
            dialogVisible: false,
            dialodayssisVisible: false,
            importtimedialogVisible: false,
            listLoading: false,
            dialogAddVisible: false,
            goodslistloading: false,
            lastUpdateTime: null,
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            detail: {
                visible: false,
                filter: {
                    seriesCode: null,
                    styleCode: null
                }
            },
            selids: [],
            setForm: { status: null, updType: null, ids: null },
            setPrivateVisible: false,
            setFormRules: {
                status: [{ required: true, message: '请选择是否保护', trigger: 'change' }],
            },
            status: null,
            addLoading: false,
            echartsLoading: false,
        };
    },

    async mounted() {
        await this.getLastUpdateTime();
        await this.onSearch()
        await this.setGroupSelect();
    },

    methods: {
        async onSimilarityReceiv(row) {
            var styleCode = row.styleCode
            var res = await proCodeSimilarityReceivIsGroupId({ styleCode: styleCode })
            if (res.data == true) {
                this.$alert('不属于运营组无法认领', '提示', {
                    confirmButtonText: '确定',
                    callback: action => {
                        this.$message({
                            type: 'info',
                            message: `不属于运营组无法认领`
                        });
                    }
                })
            } else {
                this.$confirm('是否领取产品', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    var res3 = await addProCodeSimilarityReceiv(res.data);
                    this.$message({
                        message: res3.data,
                    });
                    await this.onSearch()
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消认领'
                    });
                });
            }

        },
        onSetProvateStatus(type) {
            //校验是否选中
            if (this.selids.length < 1) {
                this.$message({
                    message: '请至少选择一条记录！',
                    type: 'warning'
                });
                return;
            }
            this.setForm.updType = type;
            this.setPrivateVisible = true;
            this.setForm.status = null;
        },
        //设置运营组下拉
        async setGroupSelect() {
            const res = await getGroupKeyValue({});
            this.groupList = res.data;

            var res2 = await getAllProBrand();
            this.brandlist = res2.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        async onSetSubmit() {
            this.addLoading = true
            const para = _.cloneDeep(this.setForm)
            para.ids = this.selids.join();
            const res = await setTbProCodeSimilarityGrowProviteStatus(para)
            this.addLoading = false

            if (!res?.success) {
                returnclionSearch
            }
            this.$message({
                message: "设置成功！",
                type: 'success'
            })
            this.$refs.pager.setPage(1)
            this.getlist();
            this.selids = [];
            this.$refs['setForm'].resetFields()
            this.setPrivateVisible = false
        },
        setFormValidate() {
            let isValid = false
            if (this.setForm.status == null) {
                this.$message({ message: "请先选择", type: "warning", });
                return isValid
            }
            return true
        },
        onCloseSetForm() {
            this.setPrivateVisible = false;
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async clionSearch() {
            if (!this.filter.colorType && this.salesDayType.length == 0) {
                this.$refs.pager.setPage(1)
                await this.getlist()
            } else if (!this.filter.colorType || this.salesDayType.length == 0) {
                this.$message("颜色和列请一起选择")
                return
            } else {
                this.$refs.pager.setPage(1)
                await this.getlist()
            }

            // this.$refs.pager.setPage(1)
            // await this.getlist()
        },
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startGoodsCreatedTime = null;
            this.filter.endGoodsCreatedTime = null;
            if (this.filter.timerange) {
                this.filter.startGoodsCreatedTime = this.filter.timerange[0];
                this.filter.endGoodsCreatedTime = this.filter.timerange[1];
            }

            this.filter.salesDayType = this.salesDayType.join()
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getTbProCodeSimilarityGrow(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            //this.summaryarry = res.data.summary;
            this.list = data
        },
        async showDetail(row) {

            this.detail.filter.seriesCode = row.styleCode;
            this.detail.filter.styleCode = row.styleCode;
            this.detail.visible = true

            this.$nextTick(async () => {
                await this.$refs.seriesGoods.onSearch();
            })
        },
        async showGoodsCode(row) {
            this.dialogAddVisible = true;
            this.$nextTick(async () => {
                await this.$refs.procodesimilaritygrowdrawer.onSearch(row);
            })
        },
        async onExport() {
            if (this.onExporting) return;
            let loadingInstance = Loading.service();
            Loading.service({ fullscreen: true });
            try {
                this.filter.startGoodsCreatedTime = null;
                this.filter.endGoodsCreatedTime = null;
                if (this.filter.timerange) {
                    this.filter.startGoodsCreatedTime = this.filter.timerange[0];
                    this.filter.endGoodsCreatedTime = this.filter.timerange[1];
                }
                const params = { ... this.filter, ...this.pager }
                this.pageLoading = true;
                var res = await ExportTbProCodeSimilarityGrow(params);
                this.pageLoading = false;
                if (!res?.data) return
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '系列编码增长率_' + new Date().toLocaleString() + '.xlsx')
                aLink.click()
                loadingInstance.close();
            } catch (err) {
                console.log(err)
                console.log(err.message);
                loadingInstance.close();
            }
            this.onExporting = false;
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            var self = this;
            rows.forEach(f => {
                self.selids.push(f.styleCode);
            })
            console.log(self.selids)
        },
        cellclick(row, column, cell, event) {

        },
        async getimportlist() {
            var res = await getProCodeSimilarityGroupImportLogTime();
            console.log('导入时间', res.data)
            if (!res?.success) return
            this.importtimelist = res.data
            this.importtimedialogVisible = true;
        },
        async getLastUpdateTime() {
            const res = await getLastUpdateGroupTime({});
            if (!res?.success) {
                return;
            }
            if (res.data) this.lastUpdateTime = "最晚更新时间：" + res.data;
        },
        //字体颜色
        renderwithholdStatus(row) {
            if (row.isCommon == true) {
                return "color:green;cursor:pointer;";
            } else return "color:red;cursor:pointer;";
        },
        //字体颜色
        renderprivateStatus(row) {
            if (row.privateStatus == '1') {
                return "color:green;cursor:pointer;";
            } else return "color:red;cursor:pointer;";
        },
        async callbackGoodsCode(val) {
            // this.inputedit = true;
            this.filter.styleCode = val;
            this.onSearch();
        },
    },
};
</script>

<style lang="scss" scoped>
.columnstock {
    background-color: rgb(218, 218, 218);
}
</style>
