<template>
    <container>
        <template #header>
            <el-form :inline="true">
                <el-form-item label="系列编码:">
                    <el-input v-model="filter.styleCode" style="width:120px;" maxlength="100" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="getList('Search')">查询</el-button>
                    <el-button type="primary" @click="importFile">导入</el-button>
                    <el-button type="primary" @click="exportList">导出</el-button>
                    <el-button type="primary" :disabled="selRows.length != 1"
                        @click="openDrawer">单修耗材费用</el-button>
                    <el-button type="primary" :disabled="selRows.length < 2"
                        @click="openDrawer">批量修改耗材费用</el-button>
                </el-form-item>
            </el-form>
        </template>
        <vxetablebase ref="table" :id="'StyleCodePackFee202040825'" :that='that' :isIndex='true'
            @sortchange='sortchange' :isSelection='true' @select='selectchange' :hasexpand='true' :tableData='list'
            :tableCols='tableCols' :loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
        <el-drawer
  title="耗材费用"
  :visible.sync="drawerShow"
  direction="btt"
  :before-close="handleClose">
  <el-form :inline="true" style="width: 80%;margin: 0 auto;margin-top: 30px;">
                <el-form-item v-if="selRows.length==1" label="系列编码:" style="width: 40%;">
                    <span>{{ selRows[0].styleCode }}</span>
                </el-form-item>
                <el-form-item label="包装费:" style="width: 40%;">
                    <el-input-number v-model="form.packFee":min="0" :max="9999999" :precision="5"></el-input-number>
                </el-form-item>
            </el-form>
            <div style="width: 80%;margin: 0 auto;text-align: right;margin-top: 30px;">
                <el-button type="primary" @click="submitForm">提交</el-button>
                <el-button type="primary" @click="drawerShow=false">取消</el-button>
            </div>
</el-drawer>
<el-dialog title="系列编码包材费" :visible.sync="uploadVisible" v-dialogDrag width="20%" >
            <div style="width: 200px;height: 100px;">
            <el-upload ref="upload" :auto-upload="false" :multiple="false" action accept=".xlsx"
                            :http-request="uploadRequest" :on-change="uploadChange" :on-remove="uploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploading"
                                @click="uploadSubmit">{{ (uploading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
            </div>
        </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { QueryStyleCodePackFee, ExportStyleCodePackFee, ImportStyleCodePackFee,ChangeStyleCodePackFee,BulkChangeStyleCodePackFee } from '@/api/operatemanage/StyleCodePackFee.js'

const tableCols = [
    { istrue: true, label: '', width: '100', type: "checkbox", },
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'packFee', label: '包装费', sortable: 'custom', align: 'center' },
]

export default {
    name: 'StyleCodePackFee',
    components: { container, vxetablebase },
    data() {
        return {
            that: this,
            filter: {
                styleCode: null,
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            form:{
                styleCode: null,
                styleCodeList:null,
                packFee:0,
            },
            tableCols,
            list: [],
            loading: false,
            total: null,
            selRows: [],
            drawerShow:false,
            uploadVisible:false,
            fileList: [],
            uploading: false,
        };
    },

    async mounted() {
        await this.getList();
    },
    methods: {
        //获取列表
        async getList(val) {
            if(val=="Search"){
                this.filter.currentPage=1;
                this.$refs.pager.setPage(1);
            }
            this.selRows = [];
            const { success, data } = await QueryStyleCodePackFee(this.filter);
            if (success) {
                this.list = data?.list;
                this.total = data?.total;
            }
        },
        //导出
        async exportList() {
            const res = await ExportStyleCodePackFee(this.filter);
            if (!res?.data) return this.$message.error("导出失败,请稍后重试");
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '系列编码包装费表' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
        //导入
      //导入
      async importFile() {
            this.uploadVisible = true;
            this.uploading=false;
            this.$refs.upload.clearFiles();
            this.fileList.splice(0, 1);

        },
        async uploadRequest(item) {
            const form = new FormData();
            form.append("upfile", item.file);
            const{success} = await ImportStyleCodePackFee(form);
            if(success)
            {
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
                this.uploadVisible=false;
            }
        },
        async uploadChange(file, fileList) {
            if (fileList.length == 2) {
                fileList.splice(1, 1);
                this.$message({ message: "只允许单文件导入", type: "warning" });
                return false;
            }
            this.fileList.push(file);
        },
        uploadRemove(file, fileList) {
            this.fileList.splice(0, 1);
        },
        uploadSubmit() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return;
            }
            this.$refs.upload.submit();
            this.$refs.upload.clearFiles();
            this.fileList.splice(0, 1);
        },
        //每页数量改变
        Sizechange(val) {
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.filter.currentPage = val;
            this.getList()
        },
        //排序查询
        sortchange({ order, prop }) {
            if (prop) {
                this.filter.orderBy = prop
                this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        //选择
        selectchange: function (rows, row) {
            this.selRows = [];
            rows.forEach(f => {
                this.selRows.push(f);
            })
        },
        //提交表单
       async submitForm(){
        let promise = null;
            if(this.selRows.length>1)
            {
                promise = await BulkChangeStyleCodePackFee(this.form);
            }else{
                promise = await ChangeStyleCodePackFee(this.form);
            }
            const {success} = promise;
            if(success)
                {
                    this.$message.success("修改成功");
                    this.drawerShow=false;
                    this.getList();
                }
        },
        openDrawer()
        {
            if(this.selRows.length>1)
            {
                this.form.styleCodeList=[];
                this.selRows.forEach(row=>{
                    this.form.styleCodeList.push(row.styleCode);
                });
                this.form.styleCode=null;
                this.form.packFee=0;
            }else{
                this.form.styleCode=this.selRows[0].styleCode;
                this.form.packFee=this.selRows[0].packFee;
                this.form.styleCodeList=[];
            }
            this.drawerShow=true;
        }
    },
};
</script>

<style lang="scss" scoped></style>