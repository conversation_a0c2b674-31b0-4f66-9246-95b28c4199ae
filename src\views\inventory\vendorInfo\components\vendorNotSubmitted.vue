<template>
    <MyContainer>
        <template #header>
            <div class="header">
                <el-input placeholder="系列编码" v-model="ListInfo.styleCode" maxlength="50" class="publicMargin"
                    style="width: 220px;" clearable></el-input>
                <el-input placeholder="供应商名称" v-model="ListInfo.providerName" maxlength="50" class="publicMargin"
                    style="width: 220px;" clearable></el-input>
                <el-select v-model="ListInfo.isBY" placeholder="是否包邮" class="publicMargin" style="width: 220px;" clearable>
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.brandId" placeholder="采购" class="publicMargin" style="width: 220px;" clearable
                    filterable>
                    <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.sourceType" placeholder="来源" class="publicMargin" style="width: 220px;"
                    clearable>
                    <el-option v-for="item in sourceType" :key="item.label" :label="item.label" :value="item.label">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.position" placeholder="职位" class="publicMargin" style="width: 220px;"
                    clearable>
                    <el-option v-for="item in positionType" :key="item.label" :label="item.label" :value="item.label">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.isContaisTax" placeholder="是否含税" class="publicMargin" style="width: 220px;"
                    clearable>
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="ListInfo.IsExitProvider" placeholder="重复供应商" style="width: 220px" class="publicMargin"
                    clearable>
                    <el-option label="未重复" :value="2" />
                    <el-option label="重复" :value="1" />
                </el-select>
                <el-button type="primary" @click="searchList">查询</el-button>
            </div>
        </template>
        <vxetablebase :id="'vendorNotSubmitted202408041621_1'" ref="doesnTable" :tableData="notTableData" :tableCols="tableCols3" :is-index="true" :that="that"
            style="width: 100%; height: 650px; margin: 0" @sortchange='sortchange'
            :treeProp="{ rowField: 'disId', parentField: 'disParentId' }" :loading="listLoading" class="doesn">
        </vxetablebase>
        <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="detailTotal"
            @page-change="detailPagechange" @size-change="detailSizechange" />

        <!-- 弹层部分 -->
        <el-dialog title="采购记录" :visible.sync="nameVisible" width="40%" :before-close="handleClose" v-dialogDrag>
            <vxetablebase :id="'vendorNotSubmitted202408041621_2'" ref="detailTable" :tableData="nameTableData" :tableCols="tableCols5" :is-index="true" :that="that"
                :showsummary="true"  :summaryarry="nameSummary"  style="width: 100%; height: 500px" @sortchange='sortchange3'>
            </vxetablebase>
            <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="nameTotal"
                @page-change="namePagechange" @size-change="nameSizechange" style="margin-top: 40px;" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    getProviderQuotationCGRecordPageList,
}
    from '@/api/openPlatform/ProviderQuotation'
import { pagePurchaseOrderByProviderNameAsync } from '@/api/inventory/purchase'
import { getAllProBrand } from '@/api/inventory/warehouse'
const options = [
    {
        value: '1',
        label: '是'
    },
    {
        value: '0',
        label: '否'
    }
]

const positionType = [
    {
        label: '老板'
    },
    {
        label: '业务员'
    },
    {
        label: '经理'
    }
]

const sourceType = [
    {
        label: '朋友圈'
    },
    {
        label: '聊天'
    },
    {
        label: '其他'
    }
]
//未提交
const tableCols3 = [
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: 95, treeNode: true },
    {
        istrue: true, prop: 'setStylePic', label: '系列编码图片', type: 'treeimages', isImgtree: true, width: 100, formatter: (row) => {
            if (row.setStylePic) {
                return row.setStylePic
            } else {
                return row.stylePic
            }
        }
    },
    { istrue: true, prop: 'brandName', label: '采购', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'sourceName', label: '推荐人', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'sourceType', label: '来源', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'providerName', label: '供应商名称', type: 'treeStar1', sortable: 'custom', width: 130, style: "color: rgb(72, 132, 243);cursor:pointer;", handle: (that, row) => that.openNameDialog(row) },
    { istrue: true, prop: 'phone', label: '联系电话', sortable: 'custom', width: 95, },
    { istrue: true, prop: 'isWX', label: '微信是否同号', width: 120, sortable: 'custom', formatter: (row) => row.isWX == 1 ? '是' : '否' },
    { istrue: true, prop: 'position', label: '职位', width: 70, sortable: 'custom' },
    { istrue: true, prop: 'isBY', label: '是否包邮', width: 95, sortable: 'custom', formatter: (row) => row.isBY == 1 ? '是' : '否' },
    { istrue: true, prop: 'isContaisTax', label: '是否含税', width: 95, sortable: 'custom', formatter: (row) => row.isContaisTax == 1 ? '是' : '否' },
    { istrue: true, prop: 'sheng', label: '发货地址', width: 95, sortable: 'custom', formatter: (row) => row.sheng + row.shi + row.qu },
    { istrue: true, prop: 'quotation1', label: '进货量100报价', width: 130, sortable: 'custom', formatter: (row) => row.quotation1 },
    { istrue: true, prop: 'quotation2', label: '进货量10000报价', width: 150, sortable: 'custom', formatter: (row) => row.quotation2 },
    { istrue: true, prop: 'quotation3', label: '进货量100000报价', width: 170, sortable: 'custom', formatter: (row) => row.quotation3 },
    { istrue: true, prop: 'remark', label: '备注', sortable: 'custom', width: 120 },
    { istrue: true, prop: 'modifiedTime', label: '最后填写日期', sortable: 'custom', width: '200' },
    {
        istrue: true, prop: 'address', label: '归属地', sortable: 'custom', width: 150, formatter: (row) => {
            if (row.address != null || row.ip != null) {
                return row.address + `(${row.ip})`
            } else {
                return ''
            }
        }
    },
]
//采购记录
const tableCols5 = [
    { istrue: true, prop: 'purchaseDate', label: '采购时间', sortable: 'custom' },
    { istrue: true, prop: 'buyNo', label: '采购单号', sortable: 'custom' },
    { istrue: true, prop: 'totalAmont', label: '采购金额', sortable: 'custom' },
    { istrue: true, prop: 'count', label: '采购量', sortable: 'custom' },
]

const status = [
    {
        label: '待沟通',
        value: '待沟通'
    },
    {
        label: '沟通中',
        value: '沟通中'
    },
    {
        label: '寄样中',
        value: '寄样中'
    },
    {
        label: '采购中',
        value: '采购中'
    },
    {
        label: '采购完成',
        value: '采购完成'
    },
    {
        label: '不适合',
        value: '不适合'
    },
]
export default {
    components: { MyContainer, vxetablebase, },
    name: "vendorNotSubmitted",
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: 'modifiedTime',//排序字段
                isAsc: false,//是否升序
                styleCode: null,//系列编码
                categoryId: null,//品类id
                openId: null,//openId
                goodCode: null,//商品编码
                isBY: null,//是否包邮
                isContaisTax: null,//是否含税
                providerName: null,//供应商名称
                dockingStatus: null,//对接状态
                sourceType: null,//来源
                brandId: null,//采购人员id
                position: null,//职位
            },
            logDetail: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                openId: null,//openId
                styleCode: null,//系列编码
                orderBy: null,//排序字段
                isAsc: true,//是否升序
            },
            RecordsInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                recordId: null,//记录id
                newGoodsRecordId: null,//新品记录id
                isDockingCG: 0,//是否对接采购 0已提交 1未提交
                styleCode: null,//系列编码
                openId: null,//openId
                result: null,//对接结果
                dockingStatus: null,//对接状态
                pics: null,
                picLists: []//图片列表
            },//对接记录请求参数
            nameInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                supplier: null,//供应商名称
            },
            sourceType,//来源
            positionType,//职位
            tableCols3,//未提交
            tableCols5,//点击供应商名字
            notTableData: [],//已提交
            nameTableData: [],//点击供应商名字
            brandList: [],//采购列表
            detailTotal: 0,//已提交总数
            nameTotal: 0,//供应商报价详情总数
            listLoading: true,//加载中
            nameVisible: false,//点击供应商名字弹层
            recordsTotal: 0,//对接记录总数
            options,//是否包邮,是否含税
            status,//状态
            nameSummary:null,
        };
    },
    mounted() {
        this.getNotCommitList()
        this.getBrandList()
    },
    methods: {
        //获取采购列表
        async getBrandList() {
            const { data, success } = await getAllProBrand()
            if (success) {
                this.brandList = data.map(item => {
                    return {
                        label: item.value,
                        value: item.key
                    }
                })
            } else {
                this.$message.error('获取采购人员列表失败')
            }
        },
        //点击供应商名称打开弹层
        async openNameDialog(row) {
            this.nameInfo.supplier = row.providerName ? row.providerName : row.supplier
            const { data, success } = await pagePurchaseOrderByProviderNameAsync(this.nameInfo)
            if (success) {
                this.nameTableData = data.list
                this.nameTotal = data.total
                this.nameSummary = data.summary
                this.nameVisible = true
                this.nameInfo.orderBy = null
            } else {
                this.$message.error('获取供应商采购记录失败')
            }
        },
        handleClose() {
            this.nameVisible = false
        },
        //对接记录弹层每页数量改变
        nameSizechange(val) {
            this.nameInfo.currentPage = 1;
            this.nameInfo.pageSize = val;
            this.openNameDialog(this.nameInfo)
        },
        //对接记录弹层当前页改变
        namePagechange(val) {
            this.nameInfo.currentPage = val;
            this.openNameDialog(this.nameInfo)
        },
        //页面数量改变
        detailSizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getNotCommitList();
        },
        //当前页改变
        detailPagechange(val) {
            this.ListInfo.currentPage = val;
            this.getNotCommitList();
        },
        searchList() {
            //清除styleCode,goodCode,providerName的空格
            this.ListInfo.styleCode = this.ListInfo.styleCode ? this.ListInfo.styleCode.replace(/\s+/g, "") : null;
            this.ListInfo.goodCode = this.ListInfo.goodCode ? this.ListInfo.goodCode.replace(/\s+/g, "") : null;
            this.ListInfo.providerName = this.ListInfo.providerName ? this.ListInfo.providerName.replace(/\s+/g, "") : null;
            this.getNotCommitList()
        },
        //获取供应商未提交列表
        async getNotCommitList() {
            this.listLoading = true;
            const { data, success } = await getProviderQuotationCGRecordPageList(this.ListInfo);
            if (success) {
                this.notTableData = data.list;
                this.detailTotal = data.total;
                this.listLoading = false;
                this.$forceUpdate();
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getNotCommitList()
            }
        },
        sortchange3({ order, prop }) {
            if (prop) {
                if (prop == 'count') {
                    this.nameInfo.orderBy = 'totalCount'
                } else {
                    this.nameInfo.orderBy = prop
                }
                this.nameInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.openNameDialog(this.nameInfo)
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.header {
    display: flex;
    margin-bottom: 10px;
}

.publicMargin {
    margin-right: 20px;
}

::v-deep .vxetoolbar20221212 {
    display: none !important;
}

::v-deep .el-badge__content {
    padding: 0 4px;
    top: 7px;
    right: 0;
}

.doesn ::v-deep .vxe-tools--operate {
    display: block !important;
    position: absolute;
    top: -25px !important;
    left: -36px !important;
    padding-top: 0;
    padding-bottom: 0;
    z-index: 999;
    background-color: rgb(255 255 255 / 0%);
}
</style>
