<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>

            <el-button style="padding: 0;margin: 0;border: 0;">
                <el-date-picker style="width: 120px" v-model="filter.yearMonth" type="month" format="yyyyMM"
                    value-format="yyyyMM" placeholder="月份"></el-date-picker>
            </el-button>

            <el-button style="padding: 0;margin: 0;border: 0;">
                <!-- <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺">
                    <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                        :value="item.shopCode"></el-option>
                </el-select> -->
                <el-input v-model.trim="filter.shopName" placeholder="店铺名称" style="width:160px;" maxlength="40" />
            </el-button>

            <el-button style="padding: 0;margin: 0;border: 0;">
                <el-input v-model.trim="filter.proCode" placeholder="商品ID" style="width:120px;" maxlength="40" />
            </el-button>

            <el-button style="padding: 0;margin: 0;border: 0;">
                <el-input v-model.trim="filter.businessName" placeholder="商务" style="width:120px;" maxlength="20" />
            </el-button>

            <el-button style="padding: 0;margin: 0;border: 0;">
                <el-input v-model.trim="filter.wiseManName" placeholder="达人" style="width:120px;" maxlength="20" />
            </el-button>

            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="importProps">导入商务达人数据</el-button>
            <el-button type="primary" @click="importProps2">导入销售主题分析</el-button>
            <el-button type="primary" @click="onExport" :loading="onExportLoading">导出</el-button>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :isSelectColumn="false" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='list'
            :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>



        <el-dialog title="导入商务达人数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading"
            :close-on-click-modal="false">
            <div style="display: flex;flex-direction: column;justify-content: center;">

                <el-date-picker style="width: 120px;margin-top: 20px; margin-bottom: 20px;" v-model="importYearMonth"
                    type="month" format="yyyyMM" value-format="yyyyMM" placeholder="导入月份"></el-date-picker>

                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>

            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>





        <el-dialog title="导入销售主题分析" :visible.sync="importVisible2" width="30%" v-dialogDrag v-loading="importLoading2"
            :close-on-click-modal="false">
            <div style="display: flex;flex-direction: column;justify-content: center;">

                <el-date-picker style="width: 120px;margin-top: 20px; margin-bottom: 20px;" v-model="importYearMonth2"
                    type="month" format="yyyyMM" value-format="yyyyMM" placeholder="导入月份"></el-date-picker>

                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="10" multiple
                    :on-remove="removeFile2" :file-list="fileList2" accept=".xlsx" :http-request="uploadFile2">
                    <el-tooltip class="item" effect="dark" content="最多上传10个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>

            </div>
            <div class="btnGroup">
                <el-button @click="importVisible2 = false">取消</el-button>
                <el-button type="primary" @click="sumbit2">确定</el-button>
            </div>
        </el-dialog>


    </my-container>
</template>
<script>
import { getAllList as getAllShopList, getDirectorGroupList } from '@/api/operatemanage/base/shop';
import { formatWarehouse, formatTime, formatYesornoBool, formatLinkProCode, platformlist } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { Loading } from 'element-ui';
import { ImportMonthBusinessWiseMan, ImportMonthWiseManSaleThemeAnalysis } from '@/api/monthbookkeeper/import'
import { GetMonthBusinessWiseManReport, ExportMonthBusinessWiseManReport } from '@/api/monthbookkeeper/financialreport'
let loading;
const startLoading = () => {  // 使用Element loading-start 方法
    loading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)'
    });
};
const tableCols = [
    { istrue: true, fixed: true, prop: 'yearMonth', label: '年月', sortable: 'custom', width: '80' },
    { istrue: true, fixed: true, prop: 'shopName', label: '店铺名称', sortable: 'custom', width: '150' },
    { istrue: true, fixed: true, prop: 'groupName', label: '运营组', sortable: 'custom', width: '90' },
    { istrue: true, fixed: true, prop: 'businessName', label: '商务', sortable: 'custom', width: '80' },
    { istrue: true, fixed: true, prop: 'wiseManName', label: '达人', sortable: 'custom', width: '120' },
    { istrue: true, fixed: true, prop: 'proCode', fix: true, label: '商品ID', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
    { istrue: true, fixed: true, prop: 'wiseManUID', label: '达人UID', sortable: 'custom', width: '100' },
    { istrue: true, fixed: true, prop: 'wiseManCode', label: '达人抖音号', sortable: 'custom', width: '120' },
    { istrue: true, fixed: true, prop: 'proCodeWiseManUID', label: '商品ID+达人UID', sortable: 'custom', width: '150' },

    { istrue: true, prop: 'countSale', label: '销售数量', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'amountSaleJe', label: '销售金额', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'amountRefund', label: '退货金额', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'amountSaleJeJing', label: '净销售额', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'amountCoustJe', label: '销售成本', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'amountRefundCost', label: '实退成本', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'amountCoustJeJing', label: '净销售成本', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'amountSaleGross', label: '销售毛利', sortable: 'custom', width: '70' },

];
export default {
    name: "reporttiktokwise1",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, },
    data() {
        return {
            that: this,
            filter: {
                yearMonth: null,
                platform: 6,
                shopCode: null,
                proCode: null,
                businessName: null,
                wiseManName: null,
            },
            list: [],
            shopList: [],
            userList: [],
            grouplist: [],
            tableCols: tableCols,
            tableHandles: [],
            total: 0,
            // summaryarry:{count_sum:10},
            pager: { OrderBy: "amountSaleJe", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            onExportLoading: false,

            fileList: [],
            importYearMonth: null,
            importLoading: false,
            importVisible: false,

            fileList2: [],
            fileListData2: [],
            importYearMonth2: null,
            importLoading2: false,
            importVisible2: false,
        };
    },
    async mounted() {
        //await this.onSearch()
        //await this.getShopList();
    },
    methods: {
        async getShopList() {
            const res1 = await getAllShopList({ platforms: [6] });
            this.shopList = [];
            res1.data?.forEach(f => {
                if (f.isCalcSettlement && f.shopCode)
                    this.shopList.push(f);
            });

            var res2 = await getDirectorGroupList();
            this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        onRefresh() {
            this.onSearch()
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList().then(res => { });
        },
        getParams() {
            if (!this.filter.yearMonth) {
                this.$message({ message: "请先选择月份！", type: "warning" });
                return false;
            }
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter, };

            return params;
        },
        async getList() {
            var that = this;
            const params = this.getParams();
            if (!params) {
                return false;
            }
            startLoading();
            const res = await GetMonthBusinessWiseManReport(params).then(res => {
                loading.close();
                that.total = res.data?.total
                that.list = res.data?.list;
                that.summaryarry = res.data?.summary;
            });
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            const params = this.getParams();
            if (!params) {
                return false;
            }
            this.onExportLoading = true;
            var res = await ExportMonthBusinessWiseManReport(params);
            this.onExportLoading = false;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '抖音达人报表_' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },

        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            if (!this.importYearMonth) {
                this.$message({ message: "请先选择月份！", type: "warning" });
                return;
            }
            if (this.file == null) return this.$message.error('请上传文件')
            const form = new FormData();
            form.append("upfile", this.file);
            form.append("yearmonth", this.importYearMonth);
            this.importLoading = true
            await ImportMonthBusinessWiseMan(form).then(({ success }) => {
                if (success) {
                    this.$message.success('正在后台导入中,请稍后刷新界面查看....')
                    this.importVisible = false
                    this.getList()
                }
                loading.close();
                this.importLoading = false
            }).catch(err => {
                loading.close();
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.importYearMonth = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },





        async uploadFile2(data) {
            this.file2 = data.file;
            this.fileListData2.push(data.file);
            console.log(this.fileListData2, this.fileListData2);
        },
        async sumbit2() {
            if (!this.importYearMonth2) {
                this.$message({ message: "请先选择月份！", type: "warning" });
                return;
            }
            if (this.file2 == null) return this.$message.error('请上传文件')
            const form = new FormData();
            let i = 0;
            this.fileListData2.forEach(f => {
                i++;
                form.append("upfile" + i.toString(), f);
            });
            form.append("yearmonth", this.importYearMonth2);
            this.importLoading2 = true
            await ImportMonthWiseManSaleThemeAnalysis(form).then(({ success }) => {
                if (success) {
                    this.$message.success('正在后台导入中,请稍后刷新界面查看....')
                    this.importVisible2 = false
                    this.getList()
                }
                loading.close();
                this.importLoading2 = false
            }).catch(err => {
                loading.close();
                this.importLoading2 = false
                this.$message.error('导入失败')
            })
        },
        importProps2() {
            this.fileListData2 = []
            this.fileList2 = []
            this.file2 = null
            this.importYearMonth2 = null
            this.importVisible2 = true
        },
        removeFile2(file, fileList) {
            this.file2 = null;
            this.fileList2 = [];
            this.fileListData2 = [];
        },

    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>
