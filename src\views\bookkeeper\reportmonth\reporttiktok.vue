<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent></el-form>
            <el-button-group>
                <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.version" placeholder="类型" style="width: 130px">
                        <el-option label="工资月报" value="v1"></el-option>
                        <el-option label="参考月报" value="v2"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-date-picker style="width: 120px" v-model="filter.yearMonth" type="month" format="yyyyMM"
                        value-format="yyyyMM" placeholder="核算月份"></el-date-picker>
                </el-button>
                <!-- <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="filter.proCode" placeholder="商品ID" style="width:120px;" />
                </el-button> -->
                <el-button style="padding: 0;margin: 0;">
                    <inputYunhan ref="productCode2" :inputt.sync="filter.proCode" v-model="filter.proCode"
                        class="publicCss" placeholder="商品ID/多条请按回车" :clearable="true" :clearabletext="true"
                        :maxRows="300" :maxlength="6000" @callback="proCodeBack" title="商品ID">
                    </inputYunhan>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="filter.ProductName" placeholder="商品名称" style="width:160px;" />
                </el-button>
                <!-- <el-button style="padding: 0;margin: 0;">
              <el-select filterable v-model="filter.platform" placeholder="平台" style="width:120px;">
                <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-button> -->
                <el-button style="padding: 0;">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组"
                        style="width: 90px">
                        <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                        <el-option v-for="item in grouplist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-select filterable clearable v-model="filter.isPinPai" placeholder="是否品牌" style="width:120px;">
                        <el-option label="是" :value=1></el-option>
                        <el-option label="否" :value=0></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="filter.pinPai" placeholder="品牌" style="width:120px;" />
                </el-button>

                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExport" :loading="exportloading">导出</el-button>
            </el-button-group>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :isSelectColumn="false" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
            @summaryClick='onsummaryClick' :tableData='financialreportlist' :tableCols='tableCols'
            :tableHandles='tableHandles' :loading="listLoading">
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

        <el-dialog title="明细" :visible.sync="calcdetails.visible" width="80%" v-dialogDrag>
            <calcdetails ref="calcdetails" style="height:600px;"></calcdetails>
        </el-dialog>
        <el-dialog title="淘宝现金红包明细" :visible.sync="CashRedTXDetail.visible" width="80%" v-dialogDrag>
            <div>
                <CashRedTXDetail ref="CashRedTXDetail" :filter="CashRedTXDetail.filter" style="height:600px;">
                </CashRedTXDetail>
            </div>
        </el-dialog>
        <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import { getAllList as getAllShopList, getDirectorGroupList } from '@/api/operatemanage/base/shop';
import { getFinancialReportList } from '@/api/financial/yyfy'
import { exportFinancialReport } from '@/api/monthbookkeeper/financialreport'
import { formatWarehouse, formatTime, formatYesornoBool, formatLinkProCode, platformlist } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import calcdetails from "@/views/bookkeeper/reportmonth/calcdetails";
import CashRedTXDetail from "@/views/bookkeeper/reportmonth/CashRedTXDetail";
import { Loading } from 'element-ui';
import buschar from '@/components/Bus/buschar'
import { getAnalysisCommonResponse } from '@/api/admin/common'
import inputYunhan from "@/components/Comm/inputYunhan";
let loading;
const startLoading = () => {  // 使用Element loading-start 方法
    loading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)'
    });
};
const tableCols = [
    { istrue: true, fixed: true, prop: 'yearMonth', label: '年月', sortable: 'custom', width: '65' },
    { istrue: true, fixed: true, prop: 'proCode', fix: true, label: '商品ID', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
    { istrue: true, fixed: true, prop: 'groupId', label: '运营', sortable: 'custom', width: '70', formatter: (row) => row.groupName || ' ' },
    { istrue: true, fixed: true, prop: 'operateSpecialUserName', label: '专员', sortable: 'custom', width: '70', formatter: (row) => row.operateSpecialUserName },
    { istrue: true, fixed: true, prop: 'assistantUserName', label: '助理', sortable: 'custom', width: '70', formatter: (row) => row.assistantUserName },
    {
        istrue: true, prop: '', label: `账单`, merge: true, prop: 'mergeField',
        cols: [
            { istrue: true, prop: 'goodsCodes', label: '商品编码', sortable: 'custom', width: '120' },
            { istrue: true, prop: 'proCode', label: '商品名称', sortable: 'custom', width: '150', formatter: (row) => row.proName || ' ' },
            { istrue: true, prop: 'styleCode', label: '系列编码', width: '100', sortable: 'custom' },
            { istrue: true, prop: 'shopCode', label: '店铺编码', width: '100', sortable: 'custom' },
            { istrue: true, prop: 'shopCode', label: '店铺名称', sortable: 'custom', width: '150', formatter: (row) => row.shopName || ' ' },
            { istrue: true, prop: 'pinPai', label: '品牌', width: '80', sortable: 'custom' },
            { istrue: true, prop: 'orderCount', label: '订单数', sortable: 'custom', width: '70' },
            { istrue: true, prop: 'count', label: 'ID数', sortable: 'custom', width: '70' },
            { istrue: true, prop: 'amountSettlement', label: '结算收入', sortable: 'custom', width: '100' },
            { istrue: true, prop: 'amountSettlement_1', label: '结算收入-1', sortable: 'custom', width: '95', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountSettlement_2', label: '2月之前月份收入', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountCrossMonthIn', label: '跨月收入', width: '100' },
            { istrue: true, prop: 'amountOut', label: '退款', sortable: 'custom', width: '70', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountCrossMonthOut', label: '跨月退款', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'platformSubsidy', label: '平台补贴', sortable: 'custom', width: '120' },
            { istrue: true, prop: 'amountTaoKeNot', label: '淘客不计', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountShare', label: '参与公摊金额', sortable: 'custom', width: '110' },
            { istrue: true, prop: 'amountCost', label: '结算成本', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountCost_1', label: '结算成本_1', sortable: 'custom', width: '80' },

            { istrue: true, prop: 'pinPaiAmount1', label: '品牌费用1', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },
            { istrue: true, prop: 'pinPaiAmount1Avg', label: '品牌费用1分摊', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },
            { istrue: true, prop: 'pinPaiAmount2', label: '品牌费用2', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },
            { istrue: true, prop: 'pinPaiAmount2Avg', label: '品牌费用2分摊', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },
            { istrue: true, prop: 'pinPaiAmount1_yc', label: '异常品牌费用1', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },
            { istrue: true, prop: 'pinPaiAmount2_yc', label: '异常品牌费用2', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },
            { istrue: true, prop: 'pinPaiAmount3', label: '品牌费用3', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },
            { istrue: true, prop: 'pinPaiAmount3Avg', label: '品牌费用3分摊', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },
            { istrue: true, prop: 'pinPaiAmountTotal', label: '品牌费用合计', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },

            { istrue: true, prop: 'agentCost', label: '代发成本差', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountEmptyId', label: '空白链接成本', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountReSendCost', label: '补发成本', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountExceptionCost', label: '异常成本', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'cbcamont', label: '定制护墙角差额', width: '80', sortable: 'custom' },
            { istrue: true, prop: 'amontFreightfee', label: '采购成本差', width: '80', sortable: 'custom' },
            { istrue: true, prop: 'xiaoTuiFanHuan', label: '销退仓返还', sortable: 'custom', width: '100' },
            { istrue: true, prop: 'grossProfit', label: '销售毛利', sortable: 'custom', width: '100' },
            { istrue: true, prop: 'orderCount_FX', label: '分销订单数', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'count_FX', label: '分销ID数', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountSettlement_FX', label: '分销结算收入', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountCost_FX', label: '分销结算成本', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'dingZhiKuanTotalCost', label: '定制款成本', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'dingZhiKuanAvgCost', label: '定制款分摊成本', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'dingZhiKuanExceptionCost', label: '定制款异常成本', sortable: 'custom', width: '80' },
        ]
    },
    {
        istrue: true, prop: '', label: `账单费用`, merge: true, prop: 'mergeField1',
        cols: [
            { istrue: true, prop: 'platformServiceFee', sortable: 'custom', label: '平台服务费', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'commissionFee', sortable: 'custom', label: '佣金', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'channelDivision', sortable: 'custom', label: '渠道成分', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'investmentServiceFee', sortable: 'custom', label: '招商服务费', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'promotionFee', sortable: 'custom', label: '直播间站外推广费', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'otherShareAmount', sortable: 'custom', label: '其他成分', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'freightInsureDeduction', sortable: 'custom', label: '运费险扣减货款', width: '90' },
            { istrue: true, prop: 'deliveryViolation', sortable: 'custom', label: '发货违规扣款', width: '90' },
            { istrue: true, prop: 'otherViolation', sortable: 'custom', label: '其他违规扣款', width: '90' },
            { istrue: true, prop: 'smallPayment', sortable: 'custom', label: '小额打款', width: '90' },
            { istrue: true, prop: 'workOrderCompensate', sortable: 'custom', label: '工单赔付', width: '90' },
            { istrue: true, prop: 'commentGiftEnsure', sortable: 'custom', label: '评价有礼保证金', width: '90' },
            { istrue: true, prop: 'monthPaySubsidy', sortable: 'custom', label: '抖音月付联合补贴费用划扣', width: '120' },
            { istrue: true, prop: 'paySubsidy', sortable: 'custom', label: '抖音支付联合补贴费用划扣', width: '120' },
            { istrue: true, prop: 'contribution', sortable: 'custom', label: '公益捐款支出', width: '120' },
            { istrue: true, prop: 'compensation', sortable: 'custom', label: '赔付', width: '120' },
            { istrue: true, prop: 'violationPenalties', sortable: 'custom', label: '违规处罚', width: '120' },
            { istrue: true, prop: 'invoiceDeduction', sortable: 'custom', label: '商家开票-发票补偿金扣款', width: '120' },
            { istrue: true, prop: 'insureDuctFee', sortable: 'custom', label: '保费扣除', width: '120' },
            { istrue: true, prop: 'expressInterceptFee', sortable: 'custom', label: '拦截费', width: '120' },

            { istrue: true, prop: 'deliveryFee', sortable: 'custom', label: '配送费', width: '120' },
            { istrue: true, prop: 'dyMonthlyPaymentJointFee', sortable: 'custom', label: '抖音月付与商家联合贴息活动', width: '120' },
            { istrue: true, prop: 'reviewIncentiveFundsRefund', sortable: 'custom', label: '评价有礼活动资金回退', width: '120' },
            { istrue: true, prop: 'recoverMoney', sortable: 'custom', label: '追缴', width: '90' },

            { istrue: true, prop: 'avgFee', sortable: 'custom', label: '账单费用公摊', width: '90' },
            { istrue: true, prop: 'dkTotalAmont', sortable: 'custom', label: '账单费用合计', width: '80' },
        ]
    },
    {
        istrue: true, prop: '', label: `订单费用`, merge: true, prop: 'mergeField2',
        cols: [
            { istrue: true, prop: 'freightFee', label: '快递费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'freightFeeAvg', label: '快递费均摊', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'withholdfee', label: '快递违规扣款', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'packageFee', label: '包装费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'packageFeeAuct', label: '真实包装费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'packageFeePredict', label: '预估包装费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'packageFeeShare', label: '分摊包装费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'totalOrderCost', label: '订单费用合计', sortable: 'custom', width: '80' },

            { istrue: true, prop: 'wcOrderCount', label: '外仓订单量', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'wcMinExpressFee', label: '外仓最低快递费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'wcActuExpressFee', label: '外仓快递费-实际快递费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'wcOrderCountYunYing', label: '外仓订单量(运)', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'wcOrderRateYunYing', label: '外仓率(运)', sortable: 'custom', width: '80', formatter: (row) => row.wcOrderRateYunYing + '%' },
            { istrue: true, prop: 'wcJunExpressFeeYunYing', label: '外仓快递费(运)', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'wcPackageFee', label: '外仓包装费(运)', sortable: 'custom', width: '80' },
        ]
    },
    {
        istrue: true, prop: '', label: `运营费用`, merge: true, prop: 'mergeField3',
        cols: [
            { istrue: true, prop: 'suixintui', label: '小店随心推', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'juliangqianchuan', label: '巨量前川', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'tesudanfeiyong', label: '特殊单费用', sortable: 'custom', width: '90' },
            { istrue: true, prop: 'tesudanyongjin', label: '特殊单佣金', sortable: 'custom', width: '90' },
            { istrue: true, prop: 'tesudanben', label: '特殊单成本', sortable: 'custom', width: '90' },
            { istrue: true, prop: 'advFee', sortable: 'custom', label: '运营费用合计', width: '80' },
        ]
    },
    {
        istrue: true, prop: '', label: `产品费用`, merge: true, prop: 'mergeField4',
        cols: [
            { istrue: true, prop: 'amontPick', sortable: 'custom', label: '产品运费', width: '90' },
            { istrue: true, prop: 'amontSampleBX', sortable: 'custom', label: '样品费', width: '90' },
            { istrue: true, prop: 'amontSampleGrop', sortable: 'custom', label: '运营拿样', width: '90' },
            { istrue: true, prop: 'amontSampleMG', sortable: 'custom', label: '美工拿样', width: '90' },
            { istrue: true, prop: 'amontShoot', sortable: 'custom', label: '美工拍摄费用', width: '90' },
            { istrue: true, prop: 'dianpuAmount', sortable: 'custom', label: '店铺费用', width: '90' },
            { istrue: true, prop: 'cuishou', sortable: 'custom', label: '催收费用', width: '90' },
            { istrue: true, prop: 'laxinAmount', sortable: 'custom', label: '拉新', width: '90' },
            { istrue: true, prop: 'totalProductCost', sortable: 'custom', label: '产品费用合计', width: '90' },
        ]
    },
    {
        istrue: true, prop: '', label: `工资`, merge: true, prop: 'mergeField5',
        cols: [
            { istrue: true, prop: 'amontCommissionMG', sortable: 'custom', label: '美工提成', width: '90' },
            { istrue: true, prop: 'amontCommissionCG', sortable: 'custom', label: '采购提成', width: '90' },
            { istrue: true, prop: 'amontWagesGroup', label: '小组运营', sortable: 'custom', width: '90' },
            { istrue: true, prop: 'amontMachineGZ', label: '加工工资', sortable: 'custom', width: '90' },
            { istrue: true, prop: 'amontCommissionXMT', label: '新媒体提成', sortable: 'custom', width: '90' },
            { istrue: true, prop: 'totalWagesCost', label: '工资合计', sortable: 'custom', width: '80' }
        ]
    },
    { istrue: true, prop: 'amontStoreLossfee', label: '仓储损耗', sortable: 'custom', width: '90', tipmesg: '可在"财务管理/月报/产品费用"中查看明细' },
    { istrue: true, prop: 'amontOffLinefee', label: '运营下架', sortable: 'custom', width: '90', tipmesg: '可在"财务管理/月报/产品费用"中查看明细' },
    { istrue: true, prop: 'saleProfit', label: '产品利润', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'chuCangYg', label: '预估出仓成本', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'chuCangZs', label: '真实出仓成本', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'chuCang', label: '出仓成本', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'chuCangYunYing', label: '出仓成本（运）', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'profit4', label: '毛四', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'wareWages', label: '仓库薪资', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'wareWagesYunYing', label: '仓库薪资（运）', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'profit5', label: '毛五', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'profit6', label: '毛六', sortable: 'custom', width: '120' },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, calcdetails, CashRedTXDetail, buschar,inputYunhan },
    data() {
        return {
            that: this,
            filter: {
                proCode: null,
                platform: 6,
                yearMonth: null,
                shopCode: null,
                groupId: null,
                productName: null,
                version: "v1"
            },
            platformlist: platformlist,
            shopList: [],
            userList: [],
            grouplist: [],
            financialreportlist: [],
            tableCols: tableCols,
            tableHandles: [],
            total: 0,
            // summaryarry:{count_sum:10},
            pager: { OrderBy: " AmountSettlement ", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            dialogVisibleSyj: false,
            calcdetails: { visible: false },
            fileList: [],
            exportloading: false,
            CashRedTXDetail: {
                visible: false,
                filter: {
                    proCode: null,
                    shopCode: null,
                    yearMonth: null
                }
            },
            analysisFilter: {
                searchName: "View_FinancialMonthReport_DY",
                extype: 5,
                selectColumn: "Count",
                filterTime: "YearMonth",
                isYearMonthDay: false,
                filter: null,
                columnList: [{ columnNameCN: '订单数', columnNameEN: 'Count' }]
            },
            buscharDialog: { visible: false, title: "", data: [] },
        };
    },
    async mounted() {
        //await this.onSearch()
        await this.getShopList();
    },
    methods: {
        async getShopList() {
            const res1 = await getAllShopList({ platforms: [6] });
            this.shopList = [];
            res1.data?.forEach(f => {
                if (f.isCalcSettlement && f.shopCode)
                    this.shopList.push(f);
            });

            let res2 = await getDirectorGroupList();
            this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        onRefresh() {
            this.onSearch()
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList().then(res => { });
            // loading.close();
        },
        async getList() {
            let that = this;
            let pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter, };
            // this.listLoading = true;
            startLoading();
            const res = await getFinancialReportList(params).then(res => {
                loading.close();
                that.total = res.data?.total
                that.financialreportlist = res.data?.list;
                that.summaryarry = res.data?.summary;
            });
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            if (!this.filter.yearMonth) {
                this.$message({ message: "请先选择月份！", type: "warning" });
                return;
            }
            let pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            this.exportloading = true;
            let res = await exportFinancialReport(params);
            this.exportloading = false;
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '财务账单数据' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        async showcalcdetails(row, column) {
            this.calcdetails.visible = true;
            console.log('this.$refs', this.$refs)
            console.log('this.$refs.calcdetails;', this.$refs.calcdetails)
            let calc = this.$refs.calcdetails;
            console.log('calcdetails1', calc)
            let that = this;
            await this.$nextTick(async () => {
                console.log('calcdetails2', calc)
                await that.$refs.calcdetails.onSearch(column, this.filter.version, row.shopCode, row.yearMonth, row.proCode)
            });
        },
        async onsummaryClick(property) {
            let that = this;
            this.analysisFilter.filter = {
                proCode: [that.filter.proCode, 0],
                //platform: [that.filter.platform, 0],
                yearMonth: [that.filter.yearMonth, 0],
                shopCode: [that.filter.shopCode, 0],
                groupId: [that.filter.groupId, 0],
                productName: [that.filter.productName, 5],
                version: [that.filter.version, 0],
            };
            this.analysisFilter.selectColumn = property;
            let cn = "金额";
            if (property == "orderCount" || property == "count") {
                cn = "数量";
            }
            this.analysisFilter.columnList = [{ columnNameCN: cn, columnNameEN: property }];

            const res = await getAnalysisCommonResponse(this.analysisFilter).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data
                that.buscharDialog.title = res.data.legend[0]
            });
        },
        proCodeBack(val) {
            let that = this;
            that.filter.proCode = val;
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
