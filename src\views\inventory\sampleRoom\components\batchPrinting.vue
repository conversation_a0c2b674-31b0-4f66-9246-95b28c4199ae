<template>
  <div style="height: 500px;display: flex;flex-direction: column;">
    <div>
      <el-select v-model="printSize" placeholder="请选择打印尺寸" clearable style="width: 110px;margin-right: 5px;"
        @change="changePrintSize($event, 2)">
        <el-option v-for="size in printSizeList" :key="size" :label="size + '*' + size" :value="size" />
      </el-select>
      高(厘米):
      <el-input-number v-model="qrCodeHeight" :min="1" :max="999" :controls="false" :precision="1"
        placeholder="请输入高度(厘米)" style="width:90px;margin-right: 5px;" @blur="onPrintMethod(1)" />
      长(厘米):
      <el-input-number v-model="qrCodeWidth" :min="1" :max="999" :controls="false" :precision="1"
        placeholder="请输入长度(厘米)" style="width:90px;margin-right: 5px;" @blur="onPrintMethod(1)" />
      <el-button type="primary" @click="printQRCode('#printid')">打印</el-button>
    </div>
    <div style="margin-top: 5px;">
      <el-switch @change="onPrintMethod(6)" v-model="switchshow" active-text="显示id" inactive-text="隐藏id">
      </el-switch>
    </div>
    <el-scrollbar ref="refscrollbar" style="height: 95%;margin-top: 10px;">
      <div id="printid" v-if="printVisible" v-loading="printLoading" :style="qrcodeContainerStyle">
        <div v-for="(item, index) in goodsCodeList" :key="index" class="qrcode-item" style="margin-top: 7px;">
          <!-- <canvas :id="`qrcode${index}`"></canvas> -->
          <img :id="'barcode1' + item" :style="qrcodeContainerStyle" />
          <!-- <div v-show="switchshow" class="qrcode-id">{{ item }}</div> -->
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from 'dayjs'
import QRCode from "qrcode";
import Print from 'print-js'
import printQRCode from "@/utils/printQRCode";
import _ from 'lodash'
import decimal from '@/utils/decimal'
import { getWarehouseLocationManagementBatch } from '@/api/inventory/sampleGoods';
export default {
  name: "manageStorageLocation",
  components: {
    MyContainer, vxetablebase
  },
  props: {
    checkboxList: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      printVisible: false,
      printLoading: false,
      printSizeList: [10, 8, 5, 4, 3, 2.5, 1.5],
      printSize: 4,
      qrCodeWidth: 4,
      qrCodeHeight: 4,
      goodsCodeList: [],
      switchshow: true,
      numQRCode: 0,
      scrollbarWidth: 0,
      rowDisplayNum: 1,
      that: this,
      timeRanges: [],
      loading: false,
    }
  },
  computed: {
    qrcodeContainerStyle() {
      const containerWidth = this.scrollbarWidth
      const qrCodeWidthWithMargin = this.qrCodeWidth * 37.795 + 20; // 200px宽度 + 20px的间距
      const maxRowDisplayNum = Math.floor(containerWidth / qrCodeWidthWithMargin); // 最大能显示的二维码个数
      // 计算每行显示的二维码个数，如果二维码宽度超出总宽度，则自动换行
      const rowDisplayNum = this.rowDisplayNum > maxRowDisplayNum ? maxRowDisplayNum : this.rowDisplayNum;
      return {
        width: `${decimal(decimal(this.qrCodeWidth, 37.795, 0, '*'), 110, 0, '+')}px`,
        height: `${decimal(decimal(this.qrCodeHeight, 37.795, 0, '*'), 30, 0, '+')}px`,
        // display: 'grid',
        // justifyContent: 'center',
        // alignItems: 'center',
        // pageBreakInside: 'avoid',
      };
    }
  },
  watch: {
    printVisible(val) {
      if (val) {
        this.getScrollbarWidth()
      }
    },
  },
  async mounted() {
    this.goodsCodeList = this.checkboxList.map(item => item.warehouseBitCode)
    this.printVisible = true
    this.onPrintMethod()
  },
  methods: {
    getScrollbarWidth() {
      this.$nextTick(() => {
        if (this.$refs.refscrollbar && this.$refs.refscrollbar.$el) {
          this.scrollbarWidth = this.$refs.refscrollbar.$el.offsetWidth;
        } else {
          console.warn('Scrollbar ref not found.');
        }
      });
    },
    async printQRCode(val) {
      this.$confirm('此操作将执行打印，是否继续？所选编码将会被视为已打印', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        Print({
          printable: 'printid',
          type: 'html',
          scanStyles: true,
          targetStyles: ['*'],
          onCancel: function () {
            console.log('取消');
          },
          onPrint: function () {
            console.log('打印');
          },
        });
        const { data } = await getWarehouseLocationManagementBatch({ ids: this.checkboxList.map(item => item.id) })
      })
    },
    onPrintMethod(val) {
      this.goodsCodeList.forEach((item, index) => {
        setTimeout(() => {
          JsBarcode(`#barcode1${item}`, item, {
            displayValue: this.switchshow,
            fontSize: 20,
            fontOptions: "bold",
            font: "Arial",
            textAlign: "center"
          })
        }, 0);
      })
      this.printLoading = false;
    },
    changePrintSize(e, val) {
      this.qrCodeWidth = e
      this.qrCodeHeight = e
      this.onPrintMethod(val)
    },
    cancelClick() {
      this.$emit('cancelClick')
    },
  }
}
</script>

<style scoped lang="scss">
#printid {
  page-break-inside: avoid;

  .qrcode-item {
    display: flex;
    flex-direction: column;
    // align-items: center;
    page-break-inside: avoid;
    // margin-top: 5px;
  }

  .qrcode-id {
    font-size: 9px;
    display: flex;
    justify-content: center;
  }
}
</style>
