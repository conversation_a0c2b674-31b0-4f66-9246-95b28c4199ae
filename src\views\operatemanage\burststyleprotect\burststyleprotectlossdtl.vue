<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                    <inputYunhan ref="burststyleprotectproCode" v-model="filter.proCode" :inputt.sync="filter.proCode"
                        placeholder="产品ID" :clearable="true" width="200px" @callback="callbackProCode" title="产品ID">
                    </inputYunhan>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
            </el-button-group>
        </template>

        <vxetablebase :id="'burststyleprotectlossdtl20231019'" :border="true" :align="'center'"
            :tablekey="'burststyleprotectlossdtl20231019'" ref="table2" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols' :tableHandles='tableHandles'
            :loading="listLoading" style="width:100%;height:100%;margin: 0" :xgt="9999">
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import { platformlist } from '@/utils/tools'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatPlatform, formatTime, pickerOptions } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import inputYunhan from "@/components/Comm/inputYunhan";

import { GetBurstStyleProtectPageList, ExportBurstStyleProtect } from '@/api/operatemanage/burststyleprotect'

const tableCols = [
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'proCode', label: '产品ID', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'groupId', label: '小组', sortable: 'custom', width: '80', formatter: (row) => row.groupName },
    { istrue: true, prop: 'burstStyleTime', label: '爆款时间', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'lossTime', label: '流失时间', sortable: 'custom', width: '150' },
];
const tableHandles = [
    //{ label: "编辑", handle: (that, row) => that.onEdit(row) },
];
export default {
    name: "burststyleprotect",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, vxetablebase, inputYunhan
    },
    data() {
        return {
            that: this,
            filter: {
                burstType: -1,
                daterange: [],
                shopCode: "",
                isLoss: true,
            },
            pickerOptions: pickerOptions,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            datalist: [],
            pager: { OrderBy: "burstStyleTime", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            selrows: [],
        };
    },
    async mounted() {

    },
    async created() {
    },
    methods: {
        async loadData({ startDate, endDate, styleCode }) {
            this.filter.daterange = [startDate, endDate];
            this.filter.styleCode = styleCode;

            console.log(this.filter);
            await this.onSearch();
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getCondition() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.daterange) {
                this.filter.startDate = this.filter.daterange[0];
                this.filter.endDate = this.filter.daterange[1];
            }
            else {
                this.$message({ type: 'error', message: '请输入日期!' });
                return false;
            }
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };

            return params;
        },
        async getList() {
            const params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params);
            this.listLoading = true;
            const res = await GetBurstStyleProtectPageList(params);
            this.listLoading = false;
            console.log(res);
            this.total = res.data?.total;
            this.datalist = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        async callbackProCode(val) {
            this.filter.proCode = val;
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
