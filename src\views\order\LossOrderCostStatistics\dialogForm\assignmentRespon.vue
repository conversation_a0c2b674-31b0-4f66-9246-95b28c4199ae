<template>
 <my-container v-loading="pageLoading">
     <!--顶部操作-->
     <template>
         <!--表单-->
         <el-form :model="form" ref="form" label-width="120px" label-position="right">
             <el-row >
                 <el-col :span="8"  :style="'height:'+ 300+'px;'" v-if="sellist && sellist.length > 0">
                     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :tableData='sellist'
                         :tableCols='tableCols' :isSelection="false" :isSelectColumn='false' >
                     </ces-table>
                 </el-col>

                 <el-col :span="16">
                    <el-row v-if="form.formtype=='申诉'">
                        <span style="color: red;">申诉须知：</span>
                        <br/>
                        <span style="color: red;">1、申诉基础步骤：发起申诉-->提交申诉凭证-->审核凭证-->判定申诉结果-->申诉通过后调整新责任部门，原部门责任及相关事宜剔除</span>
                        <br/>
                        <span style="color: red;">2、申诉机会仅一次，请您使用好申诉权益。</span>
                        <br/>
                        <span style="color: red;">3、申诉时间为责任人计算时间（非扣款时间）起当天17:30至次日9:00，超时导致申诉入口关闭，关闭支持再次申诉</span>
                        <br/>
                    </el-row>
                    <el-row v-else>
                        <el-col :span="6" style="margin-left: 10px;">
                            <!-- <el-form-item label="平台：">
                                {{ form.deductPlatform == 1 ? '淘系' : (form.deductPlatform == 6 ? '抖音' : '拼多多') }}
                            </el-form-item> -->
                            内部订单编号： <span style="font-size: 13px;">{{form.orderInnerNo}}</span>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="发起时间：">
                                <!-- <el-button type="text" @click="showLogDetail(form.orderNo)">{{ form.orderNo }}</el-button>
                                <span v-if="form.otherInfo && form.otherInfo.a" style="margin-left:5px;">扣款金额:{{
                                    form.otherInfo.a.amountPaid }}</span> -->
                                    {{form.afterSaleApproveDate}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="损耗金额：">
                                {{form.damagedAmount}}
                            </el-form-item>
                        </el-col>

                    </el-row>
                    <el-row v-if="sellist.length==0">
                        <el-col :span="6" >
                            <el-form-item label="原责任部门：">
                                {{form.orgZrDepartment}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="7" style="margin-left: 10px;">
                            <el-form-item label="原责任类型：">
                                {{form.orgZrType2}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="原责任人：">
                                {{form.orgZrUserName}}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                    <el-col :span="4">
                        <el-form-item label="新责任部门：" prop="newZrDepartment" :rules="[
                                { required: true, message: '请选择新责任部门', trigger: ['blur', 'change'] }
                            ]">
                            <el-select class="marginleft" v-model="form.newZrDepartment"    clearable placeholder="新责任部门(大类)" style="width: 150px;" @change="getZrType(form.newZrDepartment)"  >
                                <el-option v-for="item in damagedList" :key="item" :label="item" :value="item"  />
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="7">
                        <el-form-item label="新责任类型：" prop="newZrType2" >
                            <el-select class="marginleft" v-model="form.newZrType2"  @change="changesel"  clearable placeholder="原责任类型(细类)" style="width: 200px;"  >
                                <el-option v-for="item in damagedList2" :key="item" :label="item" :value="item" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                        <el-col :span="7" style="margin-left: 92px;">
                            <el-form-item label="新责任人：" prop="newMemberName">
                                <template v-if="formEditMode || (isAuditor && form.applyState == 1)">
                                    <el-select v-if="form.newZrDepartment == '采购'" v-model="form.newMemberId" filterable
                                        @change="newMemberIdChange">
                                        <el-option v-for="item in brandlist" :key="item.key" :label="item.value"
                                            :value="item.key" />
                                    </el-select>
                                    <el-select v-else-if="form.newZrDepartment == '运营'" v-model="form.newMemberId" filterable
                                        @change="newMemberIdChange">
                                        <el-option v-for="item in directorList" :key="item.key" :label="item.value"
                                            :value="item.key" />
                                    </el-select>
                                    <YhUserelector v-else-if="form.newZrDepartment == '客服'||form.newZrDepartment == '仓库'"
                                        :value.sync="form.newMemberDDUserId" :text.sync="form.newMemberName"
                                    ></YhUserelector>
                                    <el-input v-else v-model="form.newMemberName" @input="changesel" clearable maxlength="10" />
                                </template>
                                <span v-else>{{ form.newMemberName }}</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24" style="max-height:400px;overflow:auto">
                            <el-form-item label="定责理由：" prop="applyReason" :rules="[
                                { required: true, message: '请填写新定责资料', trigger: ['blur'] }
                            ]">
                                <el-input
                                type="textarea"
                                :autosize="{ minRows: 2, maxRows: 5}"
                                placeholder="请输入内容"
                                v-model="form.applyReason">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24" style="max-height:400px;overflow:auto">
                            <el-form-item label="定责资料：" prop="applyContent" :rules="[
                                { required: true, message: '请填写新定责资料', trigger: ['blur'] }
                            ]">
                                <yh-quill-editor :value.sync="form.applyContent" v-if="formEditMode"></yh-quill-editor>
                                <div v-else v-html="form.applyContent" class="tempdivv"></div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                 </el-col>
             </el-row>


         </el-form>

     </template>
     <template slot="footer">
         <el-row>
             <el-col :span="24" style="text-align:right;">
                 <el-button @click="onClose">关闭</el-button>
                 <el-button type="primary" @click="onSave(true)">确定</el-button>
             </el-col>
         </el-row>
     </template>

     <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
         append-to-body>
         <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" :isTx="isTx"
             style="z-index:10000;height:600px" />
     </el-dialog>

 </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode, DeductOrderZrDeptActions, DeductOrderZrReasons
 ,DeductOrderZrType12,DeductOrderZrType12Tree, formatPlatform  } from "@/utils/tools";
import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
// import { SetZrMemberCustomize } from "@/api/order/orderdeductmoney";
import { getAllWarehouse, getAllProBrand } from '@/api/inventory/warehouse'
import { getDamagedOrdersWithholdListAsync, getDeductZrAppeal4CRUD, getDamagedOrdersZrType, getDamagedOrdersZrDept, setZrMemberCustomize, saveDeductZrAppeal,
    batchDeductZrAppeal  } from '@/api/customerservice/DamagedOrders'

import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'
import YhUserelector from '@/components/YhCom/yh-userselector.vue'

import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";

import {
 getDirectorList,
 getDirectorGroupList,
 getProductBrandPageList,
 getList as getshopList,
} from "@/api/operatemanage/base/shop";

const tableCols = [
 { istrue: true, prop: 'id', label: 'id', width: '60' },

 { istrue: true, prop: 'platform', label: '平台', width: '60', formatter: (row) => formatPlatform(row.platform) },
 { istrue: true, prop: 'orderNo', label: '线上订单号', width: '180',  },
 { istrue: true, prop: 'goodsCode', label: '商品编码', },

]


export default {
 name: "OrderDeductZrSetMemberForm",
 components: { MyContainer, MyConfirmButton, YhQuillEditor, OrderActionsByInnerNos,cesTable , YhUserelector},
 data() {
     return {
         that: this,
         damagedList: [],
         damagedList2: [],
         mode: 3,
         tableCols: tableCols,
         sellist:[],
         illegalTypeList: [],
         zrDeptActions: DeductOrderZrDeptActions,
         zrReasons: DeductOrderZrReasons,

         deductOrderZrType12:DeductOrderZrType12,
         deductOrderZrType12Tree:DeductOrderZrType12Tree,


         sellistHeight:50,
         brandlist: [],
         directorList: [],

         form: {
             newMemberName: "",
             newMemberId: null,
             orderList: null,
             newMemberDDUserId:"",
             newMemberDDUserName:""
         },

         //summaryarry: {},
         pageLoading: false,
         curRow: null,
         formEditMode: true,//是否编辑模式
         dialogHisVisible: false,
         isTx: false,
         tableHeightBase:41
     };
 },
 async mounted() {
     let illegalType = await ruleIllegalType();
     this.illegalTypeList = illegalType.options;
     this.sellist = (this.form?.allorderList?.length>0)?this.form.allorderList:[];
     await this.setBandSelect();
     await this.getDirectorlist();

     this.getZrDept();
 },
 computed: {
     illegalTypeName() {
         let opt = this.illegalTypeList.find(x => x.value == this.form.illegalType);
         if (opt)
             return opt.label;
         else
             return '';
     },
     tableHeight: function () {
             let rowsCount = 1;
             if (this.sellist && this.sellist.length && this.sellist.length > 0) {
                 rowsCount = this.sellist.length;
             }
             let rowsHeight = (rowsCount + 1) * 40 + this.tableHeightBase;
             return rowsHeight > 200 ?300 : rowsHeight;
         },
 },
 methods: {
    async getZrDept(){
        let res = await getDamagedOrdersZrDept();
        if(!res.success){
            return
        }
        this.damagedList =  res?.data;
        console.log("打印数据GetDamagedOrdersZrDept",this.damagedList)
        // damagedList2
        // this.changesel();
    },
    async getZrType(name){
        let res = await getDamagedOrdersZrType(name);
        if(!res.success){
            return
        }
        this.damagedList2 =  res.data;
        this.form.newMemberName = '';
        this.form.newMemberDDUserId = '';
        this.form.newMemberId = '';
        this.form.newZrType2 = '';
        this.$forceUpdate();

        console.log("打印数据",this.damagedList)
    },
    changesel(){
        this.$forceUpdate();
    },
     //设置新责任类型原因
     newZrType1Change(){
         this.form.newZrType2="";

     },
     async getDirectorlist() {
         const res1 = await getDirectorList({});
         const res2 = await getDirectorGroupList({});

         this.directorList = res1.data;
         this.directorGroupList = [{ key: "0", value: "未知" }].concat(
          res2.data || []
         );
     },
     async setBandSelect() {
         var res = await getAllProBrand();
         if (!res?.success) return;
         this.brandlist = res.data;
     },
     showLogDetail(orderNo) {
         this.isTx = this.form.deductPlatform == 1;
         this.dialogHisVisible = true;
         this.orderNo = orderNo;
     },
     newZrDeptActionChange() {
         if (this.form.newZrDeptAction) {
             let opt = this.zrDeptActions.find(x => x.zrDeptAction == this.form.newZrDeptAction);
             if (opt) {
                 if (this.form.newZrDept != opt.zrDept) {
                     this.form.newMemberName = "";
                     this.form.newMemberId = null;
                     this.form.newMemberDDUserId="";
                     this.form.newMemberDDUserName="";
                 }
                 this.form.newZrAction = opt.zrAction;
                 this.form.newZrDept = opt.zrDept;
             } else {
                 this.form.newMemberName = "";
                 this.form.newMemberId = null;
                 this.form.newMemberDDUserId="";
                 this.form.newMemberDDUserName="";
             }
         }
     },
     newMemberIdChange() {
        //  let arr = null;
        //  if (this.form.newZrDepartment == "采购"||this.form.newZrDepartment == "运营") {
        //      arr = [...this.brandlist];
        //  }
        //  else if (this.form.newZrDept == "运营") {
        //      arr = [...this.directorList];
        //  }
        // //  if (arr != null && arr && this.form.newMemberId) {
        //      let opt = arr.find(x => x.key == this.form.newMemberId);
        //      if (opt) {
        //          this.form.newMemberName = opt.value;
        //      }
        // //  }
        let arr=null;
        if(this.form.newZrDepartment=="采购"){
            arr=[...this.brandlist];
        }
        else if(this.form.newZrDepartment=="运营"){
            arr=[...this.directorList];
        }
        // if(arr!=null && arr && this.form.newMemberId){
            let opt=arr.find(x=>x.key==this.form.newMemberId);
            if(opt){
                this.form.newMemberName=opt.value;
            }
        // }
        this.changesel();
     },
     onClose() {
         this.$emit('close');
     },
     async onSave(isClose) {
         if (await this.save()) {
             this.$emit('afterSave');
             if (isClose)
                 this.$emit('close');
         }
     },
     async loadData(filter) {
         let self = this;
         this.form = filter;
         console.log("打印数据filter",this.form.allorderList)

         if(this.form.allorderList){
            this.form.allorderList.map((item)=>{
                item.damagedOrderId = item.id
            })
         }

         return
        //  self.pageLoading = true;
        //  self.formEditMode = mode != 3;
        //  self.mode = mode;

        //  let formDto = {
        //      deductPlatform: platform,
        //      newZrType1:zrType1,
        //      newZrType2:zrType2,
        //      newZrConditionFullName: zrConditionFullName,
        //      newZrDept: zrDept,
        //      newZrAction: zrAction,
        //      newZrReason: zrReason,
        //      newZrDeptAction: zrDeptAction,
        //      newMemberId: memberId,
        //      newMemberName: memberName,
        //      orderNo: orderNo,
        //      deductOccurTime: occurrenceTime,
        //      illegalType: illegalType
        //  };

        //  if (formDto.newMemberId){
        //      formDto.newMemberId = formDto.newMemberId.toString();

        //  }

        //  this.form = { ...formDto }


        //  if(orderList && orderList.length>0){
        //      this.sellist=orderList.map(x=>{
        //          let y={...x};
        //          y.deductOccurTime=x.occurrenceTime;
        //          return y;
        //      });

        //  }


        //  self.pageLoading = false;
     },
     async save() {
         this.pageLoading = true;

          let saveData = { ...this.form };

         saveData.orderList = [];



        // if(this.sellist?.length>0){
        //     saveData.orderList = this.sellist;
        // }else{
        //     saveData.orderList.push({
        //         orderNo: this.form.orderNo,
        //         goodsCode: this.form.goodsCode,
        //     });
        // }
        if(this.form.formtype == '批量申诉'){
            saveData.orderList = this.sellist;
        }else{
            saveData.orderList.push({
                orderNo: this.form.orderNo,
                goodsCode: this.form.goodsCode,
            });
        }


        let rlt;
        if(this.form.formtype == '指派'){
            // saveData.damagedOrderId = saveData.id
            saveData.orderList.map((item)=>{
                item.damagedOrderId = saveData.id
            })
            saveData.id = 0
            rlt = await setZrMemberCustomize(saveData);
        }else if(this.form.formtype == '申诉'){
            saveData.damagedOrderId = saveData.id
            saveData.id = 0
            rlt = await saveDeductZrAppeal(saveData);

        }else if(this.form.formtype == '批量申诉'){
            rlt = await batchDeductZrAppeal(saveData);
        }

         if (rlt && rlt.success) {
            if(this.form.formtype == '批量申诉'){
                this.$message.success(rlt.data.rltMsg);
            }else{
                this.$message.success('操作成功！');
            }
         }

         this.pageLoading = false;

         return (rlt && rlt.success);
     }
 },
};
</script>
<style lang="scss" scoped>.tempdiv ::v-deep img {
 width: auto;
 max-width: 1000px;
}
.tempdivv ::v-deep img{ max-width: 980px}
</style>
