<template>
  <MyContainer>
    <el-tabs v-model="activeName" style="height: 95%">
      <el-tab-pane label="地区可视化" name="first" style="height: 100%">
        <visualizationOfRegions></visualizationOfRegions>
      </el-tab-pane>
      <el-tab-pane label="供应商信息" name="second" lazy style="height: 100%">
        <supplierInfo></supplierInfo>
      </el-tab-pane>
    </el-tabs>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import supplierInfo from "./supplierInfo.vue";
import visualizationOfRegions from "./visualizationOfRegions.vue";
export default {
  components: {
    MyContainer,
    supplierInfo,
    visualizationOfRegions,
  },
  data() {
    return {
      activeName: "first",
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped></style>
