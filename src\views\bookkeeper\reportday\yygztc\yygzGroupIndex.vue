<template>
    <MyContainer>
      <el-tabs v-model="activeName" style="height: 94%;">
        <el-tab-pane label="运营提成" name="first1" style="height: 100%;">
          <yygztichengGroup />
        </el-tab-pane>
        <el-tab-pane label="组长对应主管和平台负责人" name="first2" style="height: 100%;" lazy>
          <yyPlatFormGroup />
        </el-tab-pane>
        <el-tab-pane label="品牌店铺" name="first3" style="height: 100%;" lazy>
          <yyBrandStore/>
        </el-tab-pane>
        <el-tab-pane label="品牌工资" name="first4" style="height: 100%;" lazy>
          <yyBrandSalary/>
        </el-tab-pane>
      </el-tabs>
    </MyContainer>
  </template>
  
  <script>
  import MyContainer from "@/components/my-container";
  import yygztichengGroup from './yygztichengGroup.vue'
  import yyPlatFormGroup from './yyPlatFormGroup.vue'
  import yyBrandStore from './yyBrandStore.vue'
  import yyBrandSalary from './yyBrandSalary.vue'
  
  export default {
    components: {
      MyContainer, yygztichengGroup, yyPlatFormGroup, yyBrandStore, yyBrandSalary
    },
    data() {
      return {
        activeName: 'first1'
      };
    },
    methods: {
  
    }
  };
  </script>
  
  <style lang="scss" scoped></style>
  