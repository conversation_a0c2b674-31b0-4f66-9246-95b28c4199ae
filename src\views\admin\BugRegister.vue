<template>
    <my-container v-loading="pageLoading">
      <template #header>
       <div>
         <el-button-group>
  
            <el-button style="padding: 0;margin: 0;">
                  <el-date-picker style="width: 249px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                  :clearable="true"
                  value-format="yyyy-MM-dd" range-separator="至" start-placeholder="创建开始时间" end-placeholder="创建结束时间"></el-date-picker>
              </el-button>
              <el-button style="padding: 0;margin: 0;">
                <el-input v-model.trim="filter.CreatedUser"  maxlength="30" clearable  placeholder="创建人" style="width:130px;"/>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
                <el-input v-model.trim="filter.successUser"  maxlength="30" clearable  placeholder="完成人" style="width:130px;"/>
            </el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
         </el-button-group>
       </div>
      </template>
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'   :isSelection='false'
            :showsummary='true'  :summaryarry='summaryarry' :tableData='financialreportlist'
            :tableCols='tableCols'  :tableHandles='tableHandles'  :loading="listLoading" style="height: 740px; "@select="selectchange" :isSelectColumn="false">
      </ces-table>
      <!--分页-->
      <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
      </template>
      <el-dialog title="日志" :visible.sync="ShowlogDetail.visible" width="80%" v-dialogDrag>
        <div>
          <BugRegisterShowlogDetail ref="ShowlogDetail" :filter="ShowlogDetail.filter" style="height:600px;"></BugRegisterShowlogDetail>
        </div>
      </el-dialog>
    </my-container>
  </template>
  <script>
  import {getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
  import {getDirectorGroupList,getDirectorList,getList as getshopList} from '@/api/operatemanage/base/shop'
  import {formatPlatform,formatTime,formatYesornoBool,formatLinkProCode,platformlist} from "@/utils/tools";
  import {getReceivingInformationQuery,orderPartExport,importOrderPart,exportReceivedInformationSelectedRow} from '@/api/operatemanage/base/product'
  import {getBugRegister} from '@/api/profit/personnel'
  import {getAllProBrand} from '@/api/inventory/warehouse'
  import cesTable from "@/components/Table/table.vue";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import InputMult from "@/components/Comm/InputMult";
  import { Loading } from 'element-ui';
  import { ruleDirectorGroup } from '@/utils/formruletools'
  import importmodule from '@/components/Bus/importmodule'
  import { getListByStyleCode } from "@/api/inventory/basicgoods"
  import StyleCodeDetail from '@/views/bookkeeper/reportday/StyleCodeDetail'
  import inputYunhan from "@/components/Comm/inputYunhan";
  import BugRegisterShowlogDetail from '@/views/admin/BugRegisterShowlogDetail'
  let loading;
  const startLoading = () => {
    loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
    });
  };
  const tableCols =[
           {istrue:true ,prop:'createdApproveTime',label:'创建时间',sortable:'custom', width:'210'},
           {istrue:true ,prop:'createdUserName',label:'创建人',sortable:'custom', width:'210'},
           {istrue:true ,prop:'title',label:'标题',sortable:'custom', width:'210'},
           {istrue:true ,prop:'remark',label:'备注', width:'210',sortable:'custom'},
           {istrue:true ,prop:'imagess',label:'图片', width:'210',type: "imagess"},
           {istrue:true ,prop:'chargePerson',label:'完成人',sortable:'custom', width:'210',},
           {istrue:true ,prop:'successTime',label:'完成时间',sortable:'custom', width:'200',formatter:(row)=> row.successTime=='1970-01-01 00:00:00'?"":row.successTime=='0001-01-01 00:00:00'?"":row.successTime=='1900-01-01 00:00:00'?"":row.successTime},
           { istrue: true, type: "button", label: '', width: "200", btnList: [{ label: "日志", handle: (that, row) => that.Showlog(row) }]}
    ];
  const tableHandles=[
        ];
  export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable,InputMult,importmodule,inputYunhan,StyleCodeDetail,BugRegisterShowlogDetail},
    data() {
      return {
        that:this,
        filter: {
          startTime: null,
          endTime: null,
          timerange:null,
        },
        dialogVisibleSyj:false,
        onimportfilter:{
          yearmonthday:null,
        },
        editVisible:false,
        styleCode:null,
        profit3UnZero:null,
        options:[],
        platformlist:platformlist,
        shopList:[],
        userList:[],
        brandlist:[],
        grouplist:[],
        directorlist:[],
        financialreportlist: [],
        tableCols:tableCols,
        tableHandles:tableHandles,
        total: 0,
        pager:{OrderBy:"",IsAsc:false},
        sels: [], // 列表选中列
        listLoading: false,
        earchloading:false,
        pageLoading: false,
        summaryarry:{},
        selids:[],
        fileList:[],
        dialogVisibleData:false,
        dialogVisible:false,
        uploadLoading:false,
        importFilte:{},
        fileList:[],
        fileparm:{},
        editparmVisible:false,
        editLoading:false,
        editparmLoading:false,
        drawervisible:false,
        searchloading:false,
        /* dialogDrVisibleShengYi:false, */
        dialogDrVisible:false,
        expressfreightanalysisVisible:false,
        drparamProCode:'',
          StyleCodeDetail:{
            visible:false,
            filter:{
            StyleCode:''
            }
          },
        giftDetail:{visible:false},
        costDialog:{visible:false,rows:[]},
        buscharDialog:{visible:false,title:"",data:[]},
        drawervisible:false,
        ShowlogDetail: {
        visible: false,
        filter: {
          combineCode: null,
        }
      }
      };
    },
    async mounted() {
    },
    async created() {
      await this.getShopList();
    },
    methods: {
        Showlog(row) {
      this.ShowlogDetail.filter.instanceId = row.instanceId;
      this.ShowlogDetail.visible = true;
      this.$nextTick(async () => {
        await this.$refs.ShowlogDetail.onSearch();
      });
    },
      async callbackOnlineOrderNumber(val) {
          this.filter.OnlineOrderNumber = val;
      },
      async callbackInternalOrderNumber(val) {
          this.filter.InternalOrderNumber = val;
      },
      ImportOrderTemplate(){
        window.open("/static/excel/operateManage/导入模板.xlsx", "_blank");
      },
      ImportreceivingInformation(){
        this.dialogVisibleSyj = true
      },
      ImportOrder(){
          this.dialogVisibleSyj = true
        },
        async onSubmitupload2() {
          if (!this.fileList || this.fileList.length == 0) {
          this.$message({ message: "请先选取文件", type: "warning" });
          return false;
        }
          this.$refs.upload2.submit()
        },
        async uploadFile2(item) {
          const form = new FormData();
          form.append("upfile", item.file);
          const res = importOrderPart(form);
          this.$message({message: '上传成功,正在导入中...', type: "success"});
          this.fileList = []
          this.$refs.upload2.clearFiles();
          this.dialogVisibleSyj = false;
        },
        async uploadChange (file, fileList) {
        if (fileList && fileList.length > 0) {
          var list = [];
          for (var i = 0; i < fileList.length; i++) {
            if (fileList[i].status == "success") list.push(fileList[i]);
            else list.push(fileList[i].raw);
          }
          this.fileList = list;
        } else {
          this.fileList = [];
        }
      },
      uploadRemove (file, fileList) {
        this.uploadChange(file, fileList);
      },
      async ExportReceivedInformationSelectedRow(){
        var params = { selids: this.selids };
           var  res =await exportReceivedInformationSelectedRow(params);
           const aLink = document.createElement("a");
                      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                      aLink.href = URL.createObjectURL(blob)
                      aLink.setAttribute('download', '无收货信息订单_' + new Date().toLocaleString() + '.xlsx')
                      aLink.click();
      },
      async ExportUnreceivedOrders(){
                      const params = {
                      };
                      var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                      var res = await orderPartExport(params);
                      console.log("导出的数据...........",res.data)
                      loadingInstance.close();
                      if (!res?.data) {
                          this.$message({ message: "没有数据", type: "warning" });
                          return
                      }
                      const aLink = document.createElement("a");
                      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                      aLink.href = URL.createObjectURL(blob)
                      aLink.setAttribute('download', '无收货信息订单_' + new Date().toLocaleString() + '.xlsx')
                      aLink.click();
      },
     async sortchange(column){
        if(!column.order)
          this.pager={};
        else
          this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
       await this.onSearch();
      },
      onRefresh(){
        this.onSearch()
      },
      async onSearch(){
        this.$refs.pager.setPage(1);
        await this.getList().then(res=>{  });
      },
      async getList(){
        this.filter.startTime =null;
        this.filter.endTime =null;
        if (this.filter.timerange) {
          this.filter.startTime = this.filter.timerange[0];
          this.filter.endTime = this.filter.timerange[1];
        }
        var that=this;
        var pager = this.$refs.pager.getPager();
        const params = {...pager,...this.pager,...this.filter};
        startLoading();
        const res = await getBugRegister(params).then(res=>{
            loading.close();
            that.total = res.data?.total;
            if(res?.data?.list&&res?.data?.list.length>0){
            }
            that.financialreportlist = res.data?.list;
            that.summaryarry=res.data?.summary;
        });
      },
  
      selectchange:function(rows,row) {
        this.selids=[];
        rows.forEach(f=>{
          this.selids.push(f.id);
        })
      },
     onRefresh(){
        this.onSearch()
      },
  },
  };
  
  </script>
  <style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
      background-color: #fff;
    }
    ::v-deep .el-table td.el-table__cell div{
      color: gray;
    }
  </style>
  
  