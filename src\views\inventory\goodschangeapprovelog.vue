<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%;">
            <!-- <el-tab-pane label="汇总数据" name="first">
                <aggregateData />
            </el-tab-pane> -->
            <el-tab-pane label="待操作" name="second" lazy  style="height: 100%;">
                <toBeOperated />
            </el-tab-pane>
            <el-tab-pane label="核价日志" name="third" lazy  style="height: 100%;">
                <priceLogs />
            </el-tab-pane>
            <el-tab-pane label="摘品任务" name="four" lazy style="height: 100%;">
                <pickingTask />
            </el-tab-pane>
            <el-tab-pane label="摘品奖励" name="five" lazy style="height: 100%;">
                <pickingRewards />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import priceLogs from "./priceLogs.vue"
import toBeOperated from "./toBeOperated.vue"
import aggregateData from "./aggregateData.vue"
import pickingTask from "./pickingTask.vue"
import pickingRewards from "./pickingRewards.vue"

export default {
    components: {
        MyContainer, priceLogs, toBeOperated, aggregateData, pickingTask, pickingRewards
    },
    data() {
        return {
            activeName: 'second'
        };
    },
    methods: {

    }
};
</script>

<style lang="scss" scoped></style>