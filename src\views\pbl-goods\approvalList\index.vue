<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%">
            <el-tab-pane label="保护延期" name="protect-extend" style="height: 100%" lazy
                v-if="checkPermission('pblGoodsApproval')">
                <approvalProtection />
            </el-tab-pane>
            <el-tab-pane label="撤销保护" name="protect-cancel" style="height: 100%" lazy
                v-if="checkPermission('pblGoodsApproval')">
                <cancelProtection />
            </el-tab-pane>
            <el-tab-pane label="公共品分配" name="assign-confirm" style="height: 100%" lazy
                v-if="checkPermission('pblGoodsAssignment')">
                <assignment />
            </el-tab-pane>
            <el-tab-pane label="宝贝挂走" name="product-take" style="height: 100%" lazy
                v-if="checkPermission('pblGoodsApproval')">
                <productHangUp />
            </el-tab-pane>
            <el-tab-pane label="宝贝下架" name="product-down" style="height: 100%" lazy
                v-if="checkPermission('pblGoodsApproval')">
                <productTaken />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import approvalProtection from "./components/approval-protection.vue";
import cancelProtection from "./components/cancel-protection.vue";
import assignment from "./components/assignment.vue";
import productHangUp from "./components/product-HangUp.vue";
import productTaken from "./components/product-Taken.vue";
import checkPermission from '@/utils/permission'
export default {
    components: {
        MyContainer, approvalProtection, cancelProtection, assignment, productHangUp, productTaken
    },
    data() {
        return {
            activeName: checkPermission('pblGoodsApproval') ? 'protect-extend' : 'assign-confirm',
        };
    },
    mounted() {
        if (this.$route.query.tab) {
            this.activeName = this.$route.query.tab;
        }
    },
    methods: {},
};
</script>

<style lang="scss" scoped></style>
