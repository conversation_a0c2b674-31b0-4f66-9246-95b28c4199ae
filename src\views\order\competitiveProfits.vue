<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss" />
                <el-select v-model="ListInfo.platforms" placeholder="平台" filterable multiple collapse-tags clearable
                    class="publicCss">
                    <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.chooseIds" v-model="ListInfo.chooseIds"
                    class="publicCss" placeholder="竞品ID/若输入多条请按回车" :clearable="true" :clearabletext="true"
                    :maxRows="100" :maxlength="1000000" @callback="proCodeCallback($event, 1)" title="竞品ID"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.optimizeUserNames" class="publicCss"
                    v-model="ListInfo.optimizeUserNames" placeholder="优化人/若输入多条请按回车" :clearable="true"
                    :clearabletext="true" :maxRows="500" :maxlength="1000000" @callback="proCodeCallback($event, 2)"
                    title="优化人" style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <div class="serchDay">
                    <div>竞品月销</div>
                    <el-select v-model="ListInfo.monthSaleCountType" placeholder="符号" style="width: 60px;"
                        class="publicCss" clearable @change="e => {
                            if (e != '介于') {
                                ListInfo.monthSaleCountMax = undefined
                            }
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '竞品月销'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.monthSaleCountMin" placeholder="数量"
                        maxlength="50" clearable :max="999999" :min="0" class="publicCss" :precision="0"
                        :controls="false" v-if="ListInfo.monthSaleCountType != '介于'" />
                    <number-range v-else :min.sync="ListInfo.monthSaleCountMin" :maxNumber="999999"
                        :max.sync="ListInfo.monthSaleCountMax" min-label=" 最小值" max-label="最大值" class="publicCss" />
                </div>
                <div class="serchDay">
                    <div>竞品日销</div>
                    <el-select v-model="ListInfo.daySaleCountType" placeholder="符号" style="width: 60px;"
                        class="publicCss" clearable @change="e => {
                            if (e != '介于') {
                                ListInfo.daySaleCountMax = undefined
                            }
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '竞品日销'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.daySaleCountMin" placeholder="数量"
                        maxlength="50" clearable :max="999999" :min="0" class="publicCss" :precision="0"
                        :controls="false" v-if="ListInfo.daySaleCountType != '介于'" />
                    <number-range v-else :min.sync="ListInfo.daySaleCountMin" :maxNumber="999999"
                        :max.sync="ListInfo.daySaleCountMax" min-label=" 最小值" max-label="最大值" class="publicCss" />
                </div>
                <div class="serchDay">
                    <div>售价</div>
                    <el-select v-model="ListInfo.salePriceType" placeholder="符号" style="width: 60px;" class="publicCss"
                        clearable @change="e => {
                            if (e != '介于') {
                                ListInfo.salePriceMax = undefined
                            }
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '售价'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.salePriceMin" placeholder="售价"
                        maxlength="50" clearable :max="999999" :min="0" class="publicCss" :precision="2"
                        :controls="false" v-if="ListInfo.salePriceType != '介于'" />
                    <number-range v-else :min.sync="ListInfo.salePriceMin" :maxNumber="999999" :precision="2"
                        :max.sync="ListInfo.salePriceMax" min-label=" 最小值" max-label="最大值" class="publicCss" />
                </div>
                <div class="serchDay">
                    <div>毛一利润</div>
                    <el-select v-model="ListInfo.profit1Type" placeholder="符号" style="width: 60px;" class="publicCss"
                        clearable @change="e => {
                            if (e != '介于') {
                                ListInfo.profit1Max = undefined
                            }
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '毛一利润'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.profit1Min" placeholder="毛一利润"
                        maxlength="50" clearable :max="999999" :min="0" class="publicCss" :precision="2"
                        :controls="false" v-if="ListInfo.profit1Type != '介于'" />
                    <number-range v-else :min.sync="ListInfo.profit1Min" :maxNumber="999999" :precision="2"
                        :max.sync="ListInfo.profit1Max" min-label=" 最小值" max-label="最大值" class="publicCss" />
                </div>
                <div class="serchDay">
                    <div>毛一利润率</div>
                    <el-select v-model="ListInfo.profit1RateType" placeholder="符号" style="width: 60px;"
                        class="publicCss" clearable @change="e => {
                            if (e != '介于') {
                                ListInfo.profit1RateMax = undefined
                            }
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '毛一利润率'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.profit1RateMin" placeholder="毛一利润率"
                        maxlength="50" clearable :max="999999" :min="0" class="publicCss" :precision="2"
                        :controls="false" v-if="ListInfo.profit1RateType != '介于'" />
                    <number-range v-else :min.sync="ListInfo.profit1RateMin" :maxNumber="999999" :precision="2"
                        :max.sync="ListInfo.profit1RateMax" min-label=" 最小值" max-label="最大值" class="publicCss" />
                </div>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0;height: 400px;" :loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { platformlist, formatPlatform } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { GetYWOptimizeChooseDtlChangeLogList } from '@/api/inventory/YWOptimizeGoods'
import numberRange from "@/components/number-range/index.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
const operatorList = [
    { label: '大于', value: '大于' },
    { label: '小于', value: '小于' },
    { label: '等于', value: '等于' },
    { label: '介于', value: '介于' },
]
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'platform', label: '平台', formatter: (row) => formatPlatform(row.platform) },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'chooseId', label: '竞品ID', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'monthSaleCount', label: '竞品月销', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'daySaleCount', label: '竞品日销', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'salePrice', label: '售价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'profit1', label: '毛一利润', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'profit1Rate', label: '毛一利润率', formatter: (row) => row.profit1Rate !== null ? row.profit1Rate + '%' : row.profit1Rate },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '优化时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '优化人', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, numberRange, inputYunhan
    },
    props: {
        editRow: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            operatorList,
            that: this,
            platformlist,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                //往前推一个月
                startTime: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
                endTime: dayjs().format('YYYY-MM-DD'),
                goodsCode: this.editRow.goodsCode,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            isExport: false
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        proCodeCallback(val, type) {
            if (type == 1) {
                this.ListInfo.chooseIds = val
            } else if (type == 2) {
                this.ListInfo.optimizeUserNames = val
            }
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetYWOptimizeChooseDtlChangeLogList(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }

    .serchDay {
        display: flex;
        align-items: center;
        font-size: 14px;
        gap: 5px;
    }
}
</style>
