<template>
  <my-container>
    <el-tabs v-model="activeName" style="height: 95%">
      <el-tab-pane label="广告费汇总" name="first1" style="height: 99%">
        <expenseSummary ref="refexpenseSummary" />
      </el-tab-pane>
      <el-tab-pane label="广告费明细" name="first2" style="height: 99%" lazy>
        <expenseDetail ref="refexpenseDetail" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import expenseSummary from "./components/expenseSummary.vue";
import expenseDetail from "./components/expenseDetail.vue";
export default {
  name: "advertiseIndex",
  components: {
    MyContainer, expenseSummary, expenseDetail
  },
  data() {
    return {
      activeName: "first1",
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped></style>
