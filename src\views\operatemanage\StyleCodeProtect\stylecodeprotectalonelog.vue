<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button style="padding: 0;margin: 0;border: none;">
                <el-select filterable v-model="filter.aloneGroupIds" placeholder="独立发展小组" style="width: 170px" clearable
                    multiple collapse-tags>
                    <el-option v-for="item in directorGroupList" :key="'dlfz3' + item.key" :label="item.value"
                        :value="item.key" />
                </el-select>
            </el-button>

            <el-button type="primary" @click="onSearch">查询</el-button>
        </template>

        <vxetablebase :id="'stylecodeprotectalonelog20231019'" :border="true" :align="'center'"
            :tablekey="'stylecodeprotectalonelog20231019'" ref="table2" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols' :loading="listLoading"
            style="width:100%;height:100%;margin: 0" :xgt="9999">
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import { GetStyleCodeProtectAloneLogPageList, ExportBurstStyleProtect } from '@/api/operatemanage/stylecodeprotect'

const tableCols = [
    { istrue: true, prop: 'aloneGroupId', label: '独立发展小组', sortable: 'custom', width: '160', formatter: (row) => row.aloneGroupName },
    { istrue: true, prop: 'aloneStartTime', label: '开始时间', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'aloneEndTime', label: '结束时间', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'createdUserName', label: '设置人', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'createdTime', label: '设置时间', sortable: 'custom', width: '160' },
];
export default {
    name: "stylecodeprotectalonelog",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, vxetablebase
    },
    data() {
        return {
            that: this,
            directorGroupList: [],
            filter: {
                aloneGroupIds: [],
            },
            tableCols: tableCols,
            total: 0,
            datalist: [],
            pager: { OrderBy: "createdTime", IsAsc: false },
            sels: [], // 列表选中列
            selids: [],
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
        };
    },
    async mounted() {
    },
    async created() {
    },
    methods: {
        async loadData() {
            await this.onSearch();
            await this.getloadgroupselect();
        },
        async getloadgroupselect() {
            const res1 = await getDirectorGroupList({});
            this.directorGroupList = res1.data;
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getCondition() {
            let pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            return params;
        },
        async getList() {
            const params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params);
            this.listLoading = true;
            const res = await GetStyleCodeProtectAloneLogPageList(params);
            this.listLoading = false;
            console.log(res);
            this.total = res.data?.total;
            this.datalist = res.data?.list;
            //this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
