<template>
  <MyContainer>
    <vxetablebase :id="'supplierLog202507111857'" :tablekey="'supplierLog202507111857'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'400'" :border="true">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getSupplierIsOfPublicLogList } from '@/api/inventory/supplier'
const tableCols = [
  { istrue: true, prop: 'isOpenInvoice', label: '是否能开票', width: 'auto', align: 'center' },
  { istrue: true, prop: 'supplierIsOfPublicPay', label: '货款对公/对私', width: 'auto', align: 'center' },
  { istrue: true, prop: 'invoiceType', label: '发票类型', width: 'auto', align: 'center' },
  { istrue: true, prop: 'supplierTaxRate', label: '税点', width: 'auto', align: 'center' },
  { istrue: true, prop: 'createdTime', label: '操作时间', width: 'auto', sortable: 'custom', align: 'center' },
]
export default {
  name: "supplierLog",
  components: {
    MyContainer, vxetablebase
  },
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        id: null,
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
    }
  },
  async mounted() {
    this.ListInfo.id = this.data.supplier_id
    await this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      const { data, success } = await getSupplierIsOfPublicLogList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      }
    },
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss"></style>
