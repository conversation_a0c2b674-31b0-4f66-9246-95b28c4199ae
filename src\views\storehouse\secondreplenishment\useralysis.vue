<template>
  <container>

    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'
      :tableCols='tableCols' :isSelection="false" :tableHandles='tableHandles' :loading="listLoading"
      :summaryarry="summaryarry">
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template> 

  </container>
</template>

<script>
import container from '@/components/my-container'
import cesTable from "@/components/Table/table.vue";
import { getSecondReplenishmentDataGroupUser } from '@/api/inventory/secondreplenishment'

const tableCols = [
  { istrue: true, prop: 'name', label: '拣货人', tipmesg: '', sortable: 'custom', }, 
  { istrue: true, prop: 'count', label: '跳过次数', tipmesg: '',  sortable: 'custom', },
  { istrue: true, prop: 'replenishmentCount', label: '自己补货次数', tipmesg: '', sortable: 'custom', }
]

const tableHandles = [ 
]; 

export default {
  name: 'YunHanSecondReplenishmentUserAlysis',
  components: { container, cesTable },
  props:{
    filter: {
        startTime: null,
        endTime: null,
        timerange: [null, null], 
      },
  },
  data() {
    return {
      that: this, 
      replenishmentTime: null,
      list: [], 
      pager: { OrderBy: "jHCount", IsAsc: false },
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      sels: [], 
      listLoading: false, 
      summaryarry: {}
    };
  },

  async mounted() {
    //await this.onSearch() 
  },

  methods: { 
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    async getlist() {
      if (this.pager.OrderBy == null) {
        this.pager.OrderBy = "jHCount";
        this.pager.IsAsc = false;
      }
      let pager = this.$refs.pager.getPager();
      let page = this.pager;
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      const params = { ...pager, ...page, ... this.filter }
      if (params === false) {
        return;
      }
      this.listLoading = true
      const res = await getSecondReplenishmentDataGroupUser(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.list = data
      this.summaryarry = res.data.summary;
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    }
  }
};
</script>

<style lang="scss" scoped></style>