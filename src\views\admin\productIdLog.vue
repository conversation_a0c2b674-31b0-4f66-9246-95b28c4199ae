<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss"
                    :clearable="false" />
                <el-input v-model.trim="ListInfo.userName" placeholder="用户" maxlength="50" clearable class="publicCss"
                    style="width: 100px;" />
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.proCode" v-model="ListInfo.proCode"
                    placeholder="产品ID/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="proCodeCallback" title="产品ID"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <!-- <el-input v-model.trim="ListInfo.proCode" placeholder="产品id" maxlength="50" clearable class="publicCss"
                    style="width: 150px;" /> -->
                <el-input v-model.trim="ListInfo.action" placeholder="行为" maxlength="50" clearable class="publicCss"
                    style="width: 100px;" />
                <el-select v-model="ListInfo.region" clearable placeholder="区域" filterable class="publicCss"
                    style="width: 150px;">
                    <el-option v-for="(item, i) in dept1s" :label="item" :key="i" :value="item" />
                </el-select>
                <el-select v-model="ListInfo.depart1" clearable placeholder="部门1" filterable class="publicCss"
                    style="width: 150px;">
                    <el-option v-for="(item, i) in dept2s" :label="item" :key="i" :value="item" />
                </el-select>
                <el-select v-model="ListInfo.depart2" clearable placeholder="部门2" filterable class="publicCss"
                    style="width: 150px;">
                    <el-option v-for="(item, i) in dept3s" :label="item" :key="i" :value="item" />
                </el-select>
                <el-select v-model="ListInfo.depart3" clearable placeholder="部门3" filterable class="publicCss"
                    style="width: 150px;">
                    <el-option v-for="(item, i) in dept4s" :label="item" :key="i" :value="item" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" id="20241217142119" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { GetProductIdViewLogPage } from '@/api/operatemanage/PddChart'
import { GetPageReqLogFilters } from '@/api/admin/opration-log'
import inputYunhan from "@/components/Comm/inputYunhan";
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'proCode', label: '产品ID', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'action', label: '行为', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'region', label: '区域', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'depart1', label: '一级部门', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'depart2', label: '二级部门', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'depart3', label: '三级部门', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '用户', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '日期', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, inputYunhan
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
                endDate: dayjs().format('YYYY-MM-DD'),
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            dept1s: [],
            dept2s: [],
            dept3s: [],
            dept4s: [],
        }
    },
    async mounted() {
        let fds = await GetPageReqLogFilters();
        if (fds && fds.success) {
            this.dept1s = fds.data.dept1;
            this.dept2s = fds.data.dept2;
            this.dept3s = fds.data.dept3;
            this.dept4s = fds.data.dept4;
        }
        await this.getList()
    },
    methods: {
        proCodeCallback(val) {
            this.ListInfo.proCode = val
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetProductIdViewLogPage(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
