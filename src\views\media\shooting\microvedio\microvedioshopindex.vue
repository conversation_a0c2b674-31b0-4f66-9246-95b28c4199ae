<template>
     <my-container v-loading="pageLoading"> 
            <directImgtask></directImgtask>
    </my-container>
</template>
<script>   
import MyContainer from "@/components/my-container";  
import directImgtask from '@/views/media/shooting/microvedio/microvedioshop';
import { getShootOperationsGroup} from '@/api/media/mediashare'; 
import { getUserRoleList,getShootingViewPersonAsync} from '@/api/media/microvedio';
export default { 
    components: { MyContainer,directImgtask },
    data() {
        return {
            that: this,
            pageLoading: '',
            filter: {
            }, 
            warehouseList:[],
            currole: 'tz', 
            activeName: 'tab0'
        };
    },
    //向子组件注册方法
    provide () {
        return {
            getwarehouseListRelod :this.getwarehouseList,
            getShootingViewPersonRelod:this.getShootingViewPerson
        }
    },
    async mounted() { 
        await this.getrole();
    }, 
    methods: {
        async getShootingViewPerson () {
            var res = await getShootingViewPersonAsync();
            if(res?.success)
            { 
                return res.data;
            }else{
               return null;
            }
        }, 

        async getrole() {
            var res = await getUserRoleList();
            if(res?.success)
            { 
               if(res.data == null){
                    this.currole ="tz"; 
               }else if (res.data.indexOf("视觉部经理") >-1){
                    this.currole ="b"; 
               }
                 
            }else{
                this.currole ="tz";
            }
        },
        async getwarehouseList() {
            this.warehouseList = await getShootOperationsGroup({type:3});
            return this.warehouseList;
        } 
    },
};
</script>
<style lang="scss" scoped>
  ::v-deep .el-form-item--mini.el-form-item, 
    .el-form-item--small.el-form-item {
        margin-bottom: 20px;
    }
</style>

