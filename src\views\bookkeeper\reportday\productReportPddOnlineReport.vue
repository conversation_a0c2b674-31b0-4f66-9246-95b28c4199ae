<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <div>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;">
            <inputYunhan ref="proCode" v-model="filter.proCode" :inputt.sync="filter.proCode" placeholder="原商品ID" :clearable="true" width="150px"
              @callback="callbackProCode" :maxlength="600" title="商品ID"></inputYunhan>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model="filter.productName" placeholder="原商品名称" style="width:160px;" maxlength="20"  />
          </el-button>
          <el-button style="padding: 0;width: 160px;">
            <el-input v-model="filter.styleCode" placeholder="原系列编码" style="width:160px;" maxlength="20" />
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable clearable v-model="filter.shopCode" placeholder="原店铺" style="width: 120px">
              <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
              </el-option>
            </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-date-picker :clearable="false" style="width: 250px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-date-picker style="width: 250px" v-model="filter.timerange1" type="daterange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" range-separator="至" start-placeholder="上架开始日期" end-placeholder="上架结束日期">
            </el-date-picker>
          </el-button>
          <!-- <el-button style="padding: 0;">
            <el-select filterable v-model="filter.brandId" placeholder="采购" style="width: 90px" clearable>
              <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-button> -->
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组" style="width: 90px">
              <el-option key="无运营组" label="无运营组" :value="0"></el-option>
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.operateSpecialUserId" collapse-tags clearable placeholder="运营专员"
              style="width: 90px">
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.userId" collapse-tags clearable placeholder="运营助理" style="width: 90px">
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.userId3" collapse-tags clearable placeholder="备用负责人"
              style="width: 90px">
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.profit2UnZero" collapse-tags clearable placeholder="毛利2"
              style="width: 90px">
              <el-option label="正利润" :value="false" />
              <el-option label="负利润" :value="true" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.profit3UnZero" collapse-tags clearable placeholder="毛利3"
              style="width: 90px">
              <el-option label="正利润" :value="false" />
              <el-option label="负利润" :value="true" />
            </el-select>
          </el-button>
          <button style="padding: 0; border: none;float: left;">
            <el-select v-model="filter.star" placeholder="星星" clearable filterable style="width: 80px">
              <el-option label="空白" :value="9"><span>空白</span></el-option>
              <el-option label="灰色" style="color:gray;size: 20px;" :value="8"><i
                  class="el-icon-star-on">灰色</i></el-option>
              <el-option label="红色" style="color:red;size: 20px;" :value="1"><i class="el-icon-star-on">红色</i></el-option>
              <el-option label="橙色" style="color:orange;size: 20px;" :value="2"><i
                  class="el-icon-star-on">橙色</i></el-option>
              <el-option label="黄色" style="color:yellow;size: 10px;" :value="3"><i
                  class="el-icon-star-on">黄色</i></el-option>
              <el-option label="绿色" style="color:green" :value="4"><i class="el-icon-star-on">绿色</i></el-option>
              <el-option label="蓝色" style="color:blue" :value="5"><i class="el-icon-star-on">蓝色</i></el-option>
              <el-option label="靛色" style="color:indigo" :value="6"><i class="el-icon-star-on">靛色</i></el-option>
              <el-option label="紫色" style="color:purple" :value="7"><i class="el-icon-star-on">紫色</i></el-option>
            </el-select>
          </button>
          <button style="padding: 0; border: none;float: left;">
            <el-select v-model="filter.flag" placeholder="旗帜" clearable filterable style="width: 80px; ">
              <el-option label="空白" :value="9"><span>空白</span></el-option>
              <el-option label="灰色" style="color:gray" :value="8"><i class="el-icon-s-flag">灰色</i></el-option>
              <el-option label="红色" style="color:red" :value="1"><i class="el-icon-s-flag">红色</i></el-option>
              <el-option label="橙色" style="color:orange" :value="2"><i class="el-icon-s-flag">橙色</i></el-option>
              <el-option label="黄色" style="color:yellow" :value="3"><i class="el-icon-s-flag">黄色</i></el-option>
              <el-option label="绿色" style="color:green" :value="4"><i class="el-icon-s-flag">绿色</i></el-option>
              <el-option label="蓝色" style="color:blue" :value="5"><i class="el-icon-s-flag">蓝色</i></el-option>
              <el-option label="靛色" style="color:indigo" :value="6"><i class="el-icon-s-flag">靛色</i></el-option>
              <el-option label="紫色" style="color:purple" :value="7"><i class="el-icon-s-flag">紫色</i></el-option>
            </el-select>
          </button>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-button-group>
      </div>
    </template>
    <vxetablebase :id="'productReportPddOnlineReport20230712'" :border="true" :align="'center'"
      :tablekey="'productReportPddOnlineReport20230712'" ref="table" :that='that' :isIndex='true' :hasexpand='false'
      @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
      :tableData='reportlist' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"
      style="width:100%;height:100%;margin: 0"   :xgt="9999" @summaryClick='onsummaryClick'>
    </vxetablebase>

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="getList" />
    </template>

    <!-- 拼多多日报 -->
    <vxe-modal title="拼多多日报" v-model="detail.visible" :width="1500" marginSize='-500' :mask-closable="true">
      <template  #default>
      <productReportPddOnlineReportDetail :filter1="detail.filter" ref="productReportPddOnlineReportDetail"></productReportPddOnlineReportDetail>\
      </template>
    </vxe-modal>

    <vxe-modal title="趋势图" v-model="buscharDialog.visible" :width="1500" :height="900" marginSize='-500' :mask-closable="true" @close="closeModal" >
      <template #default>
      <span>
        <template>
            <el-form class="ad-form-query" :model="filterchart" @submit.native.prevent label-width="100px">
                        <el-form-item label="日期:">
                            <el-date-picker @change="onsummaryClicks" style="width: 260px" v-model="filterchart.timerangechart" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :clearable="true" ></el-date-picker>
                        </el-form-item>
                        <el-form-item>
                        </el-form-item>
            </el-form>
        </template>
    </span>
      <span>
        <buschar style="height: 380px;" :thisStyle="{ width: '100%', height: '350px', 'box-sizing': 'border-box', 'line-height': '360px'}" ref="buschar" v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
      </span>
      <el-tabs v-model="PddactiveName" @tab-click="handleClick">
        <!-- <el-tab-pane label="拼多多总推广" name=""></el-tab-pane> -->
        <el-tab-pane label="全站推广" name="全站推广"></el-tab-pane>
        <el-tab-pane label="搜索推广" name="搜索推广"></el-tab-pane>
        <el-tab-pane label="场景展示" name="场景展示"></el-tab-pane>
        <el-tab-pane label="直播推广" name="直播推广"></el-tab-pane>
        <el-tab-pane label="明星店铺" name="明星店铺"></el-tab-pane>
      </el-tabs>
      <span style="height: 350px;">
        <buscharPdd style="height: 350px;"  :thisStyle="{ width: '100%', height: '350px', 'box-sizing': 'border-box', 'line-height': '360px'}"  ref="buscharPdd" v-if="PddbuscharDialog.visible" :analysisData="PddbuscharDialog.data"></buscharPdd>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
      </template>
    </vxe-modal>
  </my-container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop'
import { getAllProBrand } from '@/api/inventory/warehouse'
import {  formatTime, formatLinkProCode } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import InputMult from "@/components/Comm/InputMult";
import { Loading } from 'element-ui';
import { getListByStyleCode } from "@/api/inventory/basicgoods"
import inputYunhan from "@/components/Comm/inputYunhan";
import { getDistributionGoodsOnlineReportAsync,distributionGoodsReportSummaryAnalysis,distributionGoodsSummaryOneTypeReportAnalysis} from "@/api/bookkeeper/distributiongoodsreport"
import  productReportPddOnlineReportDetail from "./productReportPddOnlineReportDetail.vue"
import buschar from '@/components/Bus/buschar'
import buscharPdd from "@/components/Bus/buscharPdd";

let loading;
const startLoading = () => {
  loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};
const tableCols = [
  { istrue: true, fixed: 'left', prop: 'styleCode', label: '系列编码', tipmesg: '', width: '100', sortable: 'custom', },
  { istrue: true, fixed: 'left', prop: 'productName', label: '商品名称', width: '100',tipmesg: '',  sortable: 'custom', formatter: (row) => row.productName},
  { istrue: true, fixed: 'left', prop: 'oldGroupId', label: '商品运营组', tipmesg: '', width: '80', sortable: 'custom', type:'custom',formatter: (row) => row.oldGroupName },
  { istrue: true, fixed: 'left', prop: 'packCost', label: '快递包装费', tipmesg: '', width: '80', sortable: 'custom' },
  { istrue: true, fixed: 'left', prop: 'profitRate', label: '利润率%', tipmesg: '', width: '80', sortable: 'custom',formatter: (row) => row.profitRate+'%' },
  { istrue: true, fixed: 'left', prop: 'shopCode', label: '原店铺名称', tipmesg: '', width: '130', sortable: 'custom', type:'custom',formatter: (row) => row.oldShopName },
  { istrue: true, fixed: 'left', prop: 'oldProductCode', label: '原商品ID', type:'html', tipmesg: '', width: '120', sortable: 'custom',formatter: (row) => formatLinkProCode(2, row.oldProductCode) },
  { istrue: true, fixed: 'left', prop: 'newIdCount', label: 'ID数', tipmesg: '', width: '40', sortable: 'custom',type: 'click', handle: (that, row) => that.showDetail(row)},
  { istrue: true, fixed: 'left', prop: 'createdTime', label: '操作时间', tipmesg: '', width: '160', sortable: 'custom', formatter: (row) => formatTime(row.createdTime, "YYYY-MM-DD HH:mm:ss") },
  { istrue: true, fixed: 'left', prop: 'createdUserName', label: '操作人', tipmesg: '', width: '80', sortable: 'custom', formatter: (row) => row.createdUserName},
  { istrue: true, summaryEvent: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width: '70', tipmesg: '（付款订单量）此字段仅供参考；如果一个订单中有3种商品，这3行商品的销售订单数均为1，所以不能把此字段的合计值作为订单数量。因为订单就只有1个。但是销售订单数合计值为3。', formatter: (row) => !row.orderCount ? " " : row.orderCount },
  { istrue: true, summaryEvent: true, prop: 'payAmont', label: '付款金额', sortable: 'custom', width: '70', formatter: (row) => !row.payAmont ? " " : row.payAmont },
  { istrue: true, summaryEvent: true, prop: 'saleAmont', label: '销售金额', sortable: 'custom', width: '80', tipmesg: '已扣除当日发生的退款', formatter: (row) => !row.saleAmont ? " " : row.saleAmont },
  { istrue: true, summaryEvent: true, prop: 'profit1', label: '毛一利润', sortable: 'custom', width: '80', type: 'custom', tipmesg: '销售金额合计-销售成本合计-代发成本-赠品成本+取消单返还成本+销退仓返还成本-定制款成本差-采购成本差价', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, summaryEvent: true, prop: 'profit1Rate', label: '毛一利率', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛一利润/销售金额', formatter: (row) => !row.profit1Rate ? " " : row.profit1Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'quanzhan_usemoney', label: '全站推广', sortable: 'custom', width: '70', formatter: (row) => row.quanzhan_usemoney == 0 ? " " : row.quanzhan_usemoney },
  { istrue: true, summaryEvent: true, prop: 'alladv', label: '总广告费', sortable: 'custom', width: '70', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
  { istrue: true, summaryEvent: true, prop: 'advratio', label: '广告占比', sortable: 'custom', width: '70', formatter: (row) => !row.advratio ? " " : (row.advratio * 100)?.toFixed(0) + "%" },
  { istrue: true, summaryEvent: true, prop: 'packageAvgFee', label: '包装均价', sortable: 'custom', width: '70', formatter: (row) => !row.packageAvgFee ? " " : row.packageAvgFee },
  { istrue: true, summaryEvent: true, prop: 'freightAvgFee', label: '快递均价', sortable: 'custom', width: '70', formatter: (row) => !row.freightAvgFee ? " " : row.freightAvgFee },
  {
    istrue: true, summaryEvent: true, prop: 'profit2', label: '毛二利润', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛一利润-平台扣点-延迟发货扣款-营销费用-淘宝客-大灰熊-包装费-快递费-代发快递费',
    formatter: (row) => !row.profit2 ? " " : row.profit2
  },
  { istrue: true, summaryEvent: true, prop: 'profit2Rate', label: '毛二利润率', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛二利润/销售金额', formatter: (row) => !row.profit2Rate ? " " : row.profit2Rate + '%' },
  { istrue: true, summaryEvent: true, prop: 'profit3', label: '毛三利润', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛二利润-预估费用', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit3 ? " " : row.profit3 },
  { istrue: true, summaryEvent: true, prop: 'profit3Rate', label: '毛三利润率', type: 'custom', tipmesg: '毛三利润/销售金额', sortable: 'custom', width: '80', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit3Rate ? " " : row.profit3Rate + '%' },

];
const tableHandles = [];

export default {
  name: "Users",
  components: {
     MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, InputMult, inputYunhan, vxetablebase, productReportPddOnlineReportDetail,buschar,buscharPdd
  },
  data() {
    return {
      filter: {
        startchartTime: null,
        endchartTime: null,
        refundType: 2,
        reportType: 0,
        platform: 2,
        shopCode: null,
        proCode: null,
        styleCode: null,
        productName: null,
        brandId: null,
        groupId: null,
        startTime: null,
        endTime: null,
        listingStartTime: null,
        listingEndTime: null,
        timerange: null,
        timerange1: null,
        // 运营助理
        userId: null,
        // 车手
        userId2: null,
        // 备用
        userId3: null,
        // 运营专员 ID
        operateSpecialUserId: null,
        profit2UnZero: null,
        profit3UnZero: null,
        profit4UnZero: null,
        groupType: null
      },
      that: this,
      pageLoading: false,
      listLoading: false,
      searchloading: false,
      summaryarry: {},
      reportlist: [],
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      pager: { OrderBy: " createdtime ", IsAsc: false },
      detail: {
        visible: false,
        filter:{
          batchId:null
        }
      },
      options: [],
      shopList: [],
      userList: [],
      brandlist: [],
      grouplist: [],
      directorlist: [],
      financialreportlist: [],
      buscharDialog: { visible: false, title: "", data: {} },
      PddbuscharDialog: { visible: false, title: "", data: {} },
      filterchart: {
        startchartTime: null,
        endchartTime: null,
        timerangechart: null
      },
      PddactiveName: '全站推广',
    };
  },
  async mounted() {
    this.init();
  },
  async created() {
    await this.getShopList();
  },
  methods: {
    datetostr (date) {
      let y = date.getFullYear();
      let m = ("0" + (date.getMonth() + 1)).slice(-2);
      let d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init () {
      let date1 = new Date(); date1.setDate(date1.getDate() - 10);
      let date2 = new Date(); date2.setDate(date2.getDate() - 1);
      this.filter.timerange = [];
      this.filter.timerange[0] = this.datetostr(date1);
      this.filter.timerange[1] = this.datetostr(date2);
    },
    async getShopList () {
      const res1 = await getAllShopList({ platforms: [2] });
      this.shopList = [];
      res1.data?.forEach(f => {
        if (f.isCalcSettlement && f.shopCode)
          this.shopList.push(f);
      });
      let res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

      let res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });

      let res4 = await getAllProBrand();
      this.brandlist = res4.data.map(item => {
        return { value: item.key, label: item.value };
      });
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      await this.onSearch();
    },
    async callbackProCode(val) {
      this.filter.proCode = val;
    },
    //系列编码远程搜索
    async remoteMethod(query) {
      if (query !== '') {
        this.searchloading = true;
        setTimeout(async () => {
          const res = await getListByStyleCode({ currentPage: 1, pageSize: 50, styleCode: query })
          this.searchloading = false
          res?.data?.forEach(f => {
            this.options.push({ value: f.styleCode, label: f.styleCode })
          });
        }, 200)
      }
      else {
        this.options = []
      }
    },
    async onSearch() {
      this.$refs.pager.setPage(1);
      await this.getList().then(res => { });
    },
    async getList() {
      this.filter.startTime = null;
      this.filter.endTime = null;
      this.filter.listingStartTime = null;
      this.filter.listingEndTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      if (this.filter.timerange1) {
        this.filter.listingStartTime = this.filter.timerange1[0];
        this.filter.listingEndTime = this.filter.timerange1[1];
      }
      var that = this;
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };
      // this.listLoading = true;
      startLoading();
      const res = await getDistributionGoodsOnlineReportAsync(params).then(res => {
        loading.close();
        if (res?.data?.list && res?.data?.list.length > 0) {
          for (var i in res.data.list) {
            if (!res.data.list[i].freightFee) {
              res.data.list[i].freightFee = " ";
            }
            if (that.filter.refundType == 2) {
              res.data.list[i].RefundAmont = res.data.list[i].RefundAmontByPay;
              res.data.list[i].Profit3 = res.data.list[i].Profit3ByPay;
              res.data.list[i].Profit3Rate = res.data.list[i].Profit3RateByPay;
            }
          }
        }
        if (res.data.summary!=null) {
          res.data.summary.advratio_sum= res.data.summary.advratio_sum + '%';
          res.data.summary.profitRate_sum = res.data.summary.profitRate_sum + '%';
          res.data.summary.profit1Rate_sum = res.data.summary.profit1Rate_sum + '%';
          res.data.summary.profit2Rate_sum = res.data.summary.profit2Rate_sum + '%';
          res.data.summary.profit3Rate_sum = res.data.summary.profit3Rate_sum + '%';
          //res.data.summary.RefundAmontByPay = res.data?.summary?.RefundAmontByPay_sum;
          //res.data.summary.Profit3ByPay = res.data?.summary?.Profit3ByPay_sum;
        }
        this.total = res.data?.total;
        this.reportlist = res.data?.list;
        this.summaryarry=res.data?.summary;
      });
    },
    showDetail(row){
      this.detail.filter.batchId = row.batchId;
      this.detail.visible=true;
      this.$nextTick(()=>{
        this.$refs.productReportPddOnlineReportDetail.clearData();
      })
    },
    async onsummaryClick (property) {
      this.filterchart.startchartTime = null;
      this.filterchart.endchartTime = null;
      this.filter.startchartTime = null;
      this.filter.endchartTime = null;
      if (this.filter.timerange) {
        this.filterchart.timerangechart=this.filter.timerange;
      }
      if (this.filterchart.timerangechart) {
        this.filter.startchartTime = this.filterchart.timerangechart[0];
        this.filter.endchartTime = this.filterchart.timerangechart[1];
      }
      this.buscharDialog.data={};
      const params = { ...this.filter,...this.filter1 };
      this.columns=property
      params.column = property;
      let that = this;
      that.listLoading = true;
      const res = await distributionGoodsReportSummaryAnalysis(params).then(res => {
        that.buscharDialog.data = res.data
        that.buscharDialog.title = res.data.legend[0]
      });
      await this.$refs.buschar.initcharts()
      const par = { ...this.filter,...this.filter1 };
      par.dataType = this.PddactiveName;
      this.PddbuscharDialog.data={};
      const res1 = await distributionGoodsSummaryOneTypeReportAnalysis(par).then(res => {
        that.PddbuscharDialog.data = res.data
        that.PddbuscharDialog.title = res.data.legend[0]
      });
      that.listLoading = false;
      that.buscharDialog.visible = true;
      that.PddbuscharDialog.visible = true;
      await this.$refs.buscharPdd.initcharts()
    },
    async onsummaryClicks () {
      this.filterchart.startchartTime = null;
      this.filterchart.endchartTime = null;
      this.filter.startchartTime = null;
      this.filter.endchartTime = null;
      if (this.filterchart.timerangechart) {
        this.filter.startchartTime = this.filterchart.timerangechart[0];
        this.filter.endchartTime = this.filterchart.timerangechart[1];
      }
      const params = { ...this.filter,...this.filter1 };
      params.column = this.columns;
      let that = this;
      that.buscharDialog.visible = true;
      this.PddbuscharDialog.data={};
      const res = await distributionGoodsReportSummaryAnalysis(params).then(res => {
        that.buscharDialog.data = res.data
        that.buscharDialog.title = res.data.legend[0]
      });

      await this.$refs.buschar.initcharts()
      this.filterchart.startchartTime = null;
      this.filterchart.endchartTime = null;
      this.filter.startchartTime = null;
      this.filter.endchartTime = null;
      if (this.filterchart.timerangechart) {
        this.filter.startchartTime = this.filterchart.timerangechart[0];
        this.filter.endchartTime = this.filterchart.timerangechart[1];
      }
      const par = { ...this.filter,...this.filter1 };
      par.dataType = this.PddactiveName;
      this.PddbuscharDialog.data={};
      const res1 = await distributionGoodsSummaryOneTypeReportAnalysis(par).then(res => {
        that.PddbuscharDialog.visible = true;
        that.PddbuscharDialog.data = res.data
        that.PddbuscharDialog.title = res.data.legend[0]
      });
      await this.$refs.buscharPdd.initcharts()
    },
    async handleClick (tab, event) {
      let that = this;
      const params = { ...this.filter,...this.filter1 };
      params.dataType = this.PddactiveName;
      this.PddbuscharDialog.data={};
      const res1 = await distributionGoodsSummaryOneTypeReportAnalysis(params).then(res => {
        that.PddbuscharDialog.visible = true;
        that.PddbuscharDialog.data = res.data
        that.PddbuscharDialog.title = res.data.legend[0]
      });
      await this.$refs.buscharPdd.initcharts()
    },
    closeModal(){
      this.PddbuscharDialog.visible = false;
      this.buscharDialog.visible = false;
    }
  }
};
</script>
<style lang="scss" scoped></style>

