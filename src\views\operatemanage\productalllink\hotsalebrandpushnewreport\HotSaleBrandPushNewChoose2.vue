<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
            <div class="top">
                <el-button-group>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.categoryDimension" placeholder="地区" style="width: 150px">
                            <el-option label="产品类目" value="产品类目" />
                            <el-option label="一级类目" value="一级类目" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期" clearable :picker-options="pickerOptions"
                            style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-input v-model.trim="filter.createdGoodsCategory" maxlength="50" clearable placeholder="产品类目"
                            style="width:150px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-input v-model.trim="filter.createdCategoryLeve1" maxlength="50" clearable placeholder="一级类目"
                            style="width:150px;" />
                    </el-button>

                    <!-- <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.createdUserId" clearable filterable placeholder="推荐人"
                            style="width: 150px">
                            <el-option v-for="item in createdUserList" :key="item.createdUserId" :label="item.createdUserName" :value="item.createdUserId" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.createUserRole" clearable filterable placeholder="职位"
                            style="width: 150px">
                            <el-option v-for="item in createUserRoleList" :key="item.createUserRole" :label="item.createUserRole" :value="item.createUserRole" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0; border: 0;">
                        <el-select v-model="filter.choosePlatform" placeholder="选品平台" style="width: 150px" clearable>
                            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button> -->

                    <el-button type="primary" @click="onSearch()">查询</el-button>
                </el-button-group>
            </div>
        </template>
        <vxetablebase :id="'HotSaleBrandPushNewChoose2202408041714'" ref="vxetable" :that='that' :isIndex='true'
            :hasexpand='true' :tablefixed='true' :border="true" @sortchange='sortchange' @select='selectchange'
            :tableData='tableData' :tableCols='tableCols' :showsummary='true' :summaryarry='summaryarry'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="listLoading"
            :height="'100%'">
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

        <el-dialog :title="indexChatDialog.title" :visible.sync="indexChatDialog.visible" width="80%" height='640px'
            :close-on-click-modal="false" element-loading-text="拼命加载中" v-dialogDrag v-loading="indexChatDialog.loading">
            <my-container>
                <el-tabs v-model="indexChatDialog.activeName">
                    <el-tab-pane label="趋势图" name="tab0">
                        <buschar ref="HotSaleBrandPushNewChoose2qst" v-if="!indexChatDialog.loading"
                            :analysisData="indexChatData1" style="height: 600px; width: 98%;"
                            :legendChanges="legendChanges1">
                        </buschar>
                    </el-tab-pane>
                    <el-tab-pane label="推荐明细" name="tab1" lazy>
                        <HotSaleBrandPushNewChoose4 ref="HotSaleBrandPushNewChoose4_1" style="height: 600px;"
                            :myCreatedGoodsCategory="indexChatDialog.createdGoodsCategory"
                            :myCreatedCategoryLeve1="indexChatDialog.createdCategoryLeve1"
                            :myTimerange="indexChatDialog.timerange" :myChoose4Type="1" />
                    </el-tab-pane>
                    <el-tab-pane label="被选中明细" name="tab2" lazy>
                        <HotSaleBrandPushNewChoose4 ref="HotSaleBrandPushNewChoose4_2" style="height: 600px;"
                            :myCreatedGoodsCategory="indexChatDialog.createdGoodsCategory"
                            :myCreatedCategoryLeve1="indexChatDialog.createdCategoryLeve1"
                            :myTimerange="indexChatDialog.timerange" :myChoose4Type="2" />
                    </el-tab-pane>
                </el-tabs>
            </my-container>
        </el-dialog>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import datepicker from '@/views/customerservice/datepicker';
import { platformlist, pickerOptions } from '@/utils/tools';
import dayjs from 'dayjs';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetHotSaleBrandPushNewChoose2PageList, GetHotSaleBrandPushNewChooseSearch, GetHotSaleBrandPushNewChoose2ChartData
} from '@/api/operatemanage/productalllink/alllink'
import buschar from '@/components/Bus/buschar';
import HotSaleBrandPushNewChoose4 from '@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewChoose4';
const tableCols = [
    { sortable: 'custom', width: '120', align: 'center', prop: 'createdGoodsCategory', label: '产品类目', },
    { sortable: 'custom', width: '200', align: 'center', prop: 'createdCategoryLeve1', label: '一级类目', },
    // { sortable: 'custom', width: '100', align: 'center', prop: 'createdUserId', label: '推荐人', formatter: (row) => row.createdUserName },
    // { sortable: 'custom', width: '150', align: 'center', prop: 'createUserRole', label: '职位', },
    // { sortable: 'custom', width: '100', align: 'center', prop: 'choosePlatform', label: '选品平台', formatter: (row) => row.choosePlatformName },
    { sortable: 'custom', width: '140', align: 'center', prop: 'pushCount', label: '推荐数量', type: 'click', handle: (that, row) => that.JumpChat1(row) },
    { sortable: 'custom', width: '140', align: 'center', prop: 'chooseCount', label: '推荐被选中数量', type: 'click', handle: (that, row) => that.JumpChat1(row) },

    { width: '130', align: 'center', prop: 'choosePlatform1Rate', label: '天猫被选中占比', formatter: (row) => row.choosePlatform1Rate + '%' },
    { width: '130', align: 'center', prop: 'choosePlatform2Rate', label: '拼多多被选中占比', formatter: (row) => row.choosePlatform2Rate + '%' },
    { width: '140', align: 'center', prop: 'choosePlatform4Rate', label: '阿里巴巴被选中占比', formatter: (row) => row.choosePlatform4Rate + '%' },
    { width: '130', align: 'center', prop: 'choosePlatform6Rate', label: '抖音被选中占比', formatter: (row) => row.choosePlatform6Rate + '%' },
    { width: '130', align: 'center', prop: 'choosePlatform7Rate', label: '京东被选中占比', formatter: (row) => row.choosePlatform7Rate + '%' },
    { width: '130', align: 'center', prop: 'choosePlatform8Rate', label: '淘工厂被选中占比', formatter: (row) => row.choosePlatform8Rate + '%' },
    { width: '130', align: 'center', prop: 'choosePlatform9Rate', label: '淘宝被选中占比', formatter: (row) => row.choosePlatform9Rate + '%' },
    { width: '130', align: 'center', prop: 'choosePlatform10Rate', label: '苏宁被选中占比', formatter: (row) => row.choosePlatform10Rate + '%' },
    { width: '130', align: 'center', prop: 'choosePlatform11Rate', label: '分销被选中占比', formatter: (row) => row.choosePlatform11Rate + '%' },
    { width: '130', align: 'center', prop: 'choosePlatform12Rate', label: '希音被选中占比', formatter: (row) => row.choosePlatform12Rate + '%' },
    { width: '160', align: 'center', prop: 'choosePlatform13Rate', label: '拼多多跨境被选中占比', formatter: (row) => row.choosePlatform13Rate + '%' },
    { width: '130', align: 'center', prop: 'choosePlatform14Rate', label: '快手被选中占比', formatter: (row) => row.choosePlatform14Rate + '%' },
    { width: '130', align: 'center', prop: 'choosePlatform0Rate', label: '其他被选中占比', formatter: (row) => row.choosePlatform0Rate + '%' },
];
export default {
    name: "HotSaleBrandPushNewChoose2",
    components: {
        MyContainer, datepicker, vxetablebase, buschar, HotSaleBrandPushNewChoose4
    },
    data() {
        return {
            that: this,
            auditVisible: false,
            activities: [],
            timeRanges: [],
            platformlist: platformlist,
            pickerOptions,
            filter: {
                categoryDimension: "一级类目",
                timerange: [(dayjs().subtract(1, 'day').format('YYYY-MM') + '-01'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')],
                createdStartDate: null,
                createdEndDate: null,
            },
            pager: { OrderBy: "chooseCount", IsAsc: false },
            tableCols: tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            summaryarry: {},

            createdUserList: [],
            createUserRoleList: [],

            indexChatDialog: {
                visible: false,
                loading: false,
                activeName: "tab0",
                createdGoodsCategory: "",
                createdCategoryLeve1: "",
                timerange: [],
            },
            indexChatData1: {},
            indexChatSelectedLegend1: [],
        }
    },
    async mounted() {
        await this.getSelectData();
        this.onSearch();
    },
    computed: {
    },
    methods: {
        async getSelectData() {
            let ret5 = await GetHotSaleBrandPushNewChooseSearch({ type: 5 });
            this.createdUserList = ret5.data;

            let ret6 = await GetHotSaleBrandPushNewChooseSearch({ type: 6 });
            this.createUserRoleList = ret6.data;
        },
        async onSearch() {
            this.$nextTick(() => {
                if (this.filter.categoryDimension == "一级类目") {
                    this.$refs.vxetable.$refs.xTable.showColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('createdCategoryLeve1'))
                }
                else if (this.filter.categoryDimension == "产品类目") {
                    this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('createdCategoryLeve1'))
                }
                this.$refs.pager.setPage(1);
            });
            await this.getList();
        },
        getParam() {
            //选品日期
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.createdStartDate = this.filter.timerange[0];
                this.filter.createdEndDate = this.filter.timerange[1];
            }
            else {
                this.filter.createdStartDate = null;
                this.filter.createdEndDate = null;
            }
            let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            const res = await GetHotSaleBrandPushNewChoose2PageList(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
                // Object.keys(res.data.summary).forEach(f => {
                //     res.data.summary[f] = res.data.summary[f].toString();
                // });
                this.summaryarry = res.data.summary;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await ExportPurchaseOrderNewApprovePageList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '新品采购单审批_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
        async legendChanges1(selected) {
            this.indexChatSelectedLegend1 = selected;
        },
        async JumpChat1(row) {
            this.indexChatDialog.activeName = "tab0";
            this.indexChatDialog.createdGoodsCategory = row.createdGoodsCategory;
            this.indexChatDialog.createdCategoryLeve1 = row.createdCategoryLeve1;
            this.indexChatDialog.timerange = this.filter.timerange;

            this.indexChatDialog.visible = true;
            let param = this.getParam();
            param.createdGoodsCategory = row.createdGoodsCategory;
            this.indexChatDialog.title = row.createdGoodsCategory;
            if (param.categoryDimension == "一级类目") {
                param.createdCategoryLeve1 = row.createdCategoryLeve1;
                this.indexChatDialog.title = row.createdGoodsCategory + "-" + row.createdCategoryLeve1;
            }
            this.indexChatDialog.loading = true;
            let res = await GetHotSaleBrandPushNewChoose2ChartData(param);
            this.indexChatDialog.loading = false;
            this.indexChatData1 = res;

            this.$nextTick(async () => {
                await this.$refs.HotSaleBrandPushNewChoose4_1.onSearch();
            })

            this.$nextTick(async () => {
                await this.$refs.HotSaleBrandPushNewChoose4_2.onSearch();
            })
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}
</style>
