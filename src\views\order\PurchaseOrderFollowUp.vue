<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.createStartTime" :endDate.sync="ListInfo.createEndTime"
                    class="publicCss" startPlaceholder="发起开始时间" endPlaceholder="发起结束时间" />
                <dateRange :startDate.sync="ListInfo.sendStartTime" :endDate.sync="ListInfo.sendEndTime"
                    class="publicCss" startPlaceholder="预计发货开始时间" endPlaceholder="预计发货结束时间" />
                <dateRange :startDate.sync="ListInfo.deliveryStartTime" :endDate.sync="ListInfo.deliveryEndTime"
                    class="publicCss" startPlaceholder="预计到货开始时间" endPlaceholder="预计到货结束时间" />
                <dateRange :startDate.sync="ListInfo.warehousingStartTime" :endDate.sync="ListInfo.warehousingEndTime"
                    class="publicCss" startPlaceholder="入库开始时间" endPlaceholder="入库结束时间" />
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable multiple
                    collapse-tags>
                    <el-option label="已提交审批中" value="已提交审批中" />
                    <el-option label="已确认未发货" value="已确认未发货" />
                    <el-option label="已发货" value="已发货" />
                    <el-option label="已完成" value="已完成" />
                </el-select>
                <el-select v-model="ListInfo.purchaseInType" placeholder="入库状态" class="publicCss" clearable multiple
                    collapse-tags>
                    <el-option label="未入库" value="未入库" />
                    <el-option label="部分入库" value="部分入库" />
                    <el-option label="全部入库" value="全部入库" />
                </el-select>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="Callback($event, 1)" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;" class="publicCss">
                </inputYunhan>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.buyNo" v-model="ListInfo.buyNo"
                    placeholder="采购单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="Callback($event, 2)" title="采购单号"
                    style="width: 200px;margin:0 10px 0 0;" class="publicCss">
                </inputYunhan>
                <chooseWareHouse v-model="ListInfo.wmsIds" style="width: 230px;" class="publicCss" multiple />
                <el-input v-model.trim="ListInfo.supplierName" placeholder="供应商" maxlength="50" clearable
                    class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :showsummary='true'
            :summaryarry="summaryarry" @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' border
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :treeProp="{}" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { pageStyleCodePurchaseOrderInfoAsync } from '@/api/bookkeeper/styleCodeRptData'
import inputYunhan from "@/components/Comm/inputYunhan";
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
const tableCols = [
    { sortable: 'custom', width: '120', align: 'center', prop: 'status', label: '状态', treeNode: true },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '发起时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'supplierName', label: '供应商', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'buyNo', label: '采购单号', formatter: (row) => String(row.buyNo) },
    {
        sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', formatter: (row) => {
            if (row.children) {
                return ''
            } else {
                return row.goodsCode
            }
        }
    },
    { sortable: 'custom', width: '100', align: 'center', prop: 'count', label: '数量', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'price', label: '成本价', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'amount', label: '合计金额', },
    { sortable: 'custom', width: '190', align: 'center', prop: 'wmsId', label: '仓库', formatter: (row) => row.wmsName },
    {
        sortable: 'custom', width: 'auto', align: 'center', prop: 'expectedSendTime', label: '预计发货/到货时间', formatter: (row) => {
            if (!row.expectedSendTime && !row.expectedDeliveryTime) {
                return '-'
            } else {
                return (row.expectedSendTime ? dayjs(row.expectedSendTime).format('YYYY-MM-DD') : '-') + '/' + (row.expectedDeliveryTime ? dayjs(row.expectedDeliveryTime).format('YYYY-MM-DD') : '-')
            }
        }
    },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'purchaseInType', label: '入库状态', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehousingDate', label: '入库时间', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, inputYunhan, chooseWareHouse
    },
    props: {
        filter: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
            },
            summaryarry: {},
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        this.ListInfo = { ...this.ListInfo, ...this.filter }
        await this.getList()
    },
    methods: {
        Callback(e, val) {
            if (val == 1) {
                this.ListInfo.goodsCodes = e
            } else {
                this.ListInfo.buyNo = e
            }
        },
        async getList(type) {
            if (type == 'search') {
                let params = {
                    currentPage: this.ListInfo.currentPage,
                    pageSize: this.ListInfo.pageSize,
                    orderBy: this.ListInfo.orderBy,
                    isAsc: this.ListInfo.isAsc,
                    startTime: this.ListInfo.createStartTime,
                    endTime: this.ListInfo.createEndTime,
                    sendStartTime: this.ListInfo.sendStartTime,
                    sendEndTime: this.ListInfo.sendEndTime,
                    deliveryStartTime: this.ListInfo.deliveryStartTime,
                    deliveryEndTime: this.ListInfo.deliveryEndTime,
                    warehousingStartTime: this.ListInfo.warehousingStartTime,
                    warehousingEndTime: this.ListInfo.warehousingEndTime,
                    status: this.ListInfo.status,
                    purchaseInType: this.ListInfo.purchaseInType,
                    goodsCodes: this.ListInfo.goodsCodes,
                    buyNo: this.ListInfo.buyNo,
                    wmsIds: this.ListInfo.wmsIds,
                    supplierName: this.ListInfo.supplierName
                }
                this.ListInfo = { ...this.ListInfo, ...this.filter, ...params }
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await pageStyleCodePurchaseOrderInfoAsync(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.summaryarry = data.summary
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
