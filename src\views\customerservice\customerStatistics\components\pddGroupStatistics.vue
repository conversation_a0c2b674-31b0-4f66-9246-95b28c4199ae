<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-input v-model="ListInfo.groupName" placeholder="组名称" maxlength="50" clearable class="publicCss" />
                <el-date-picker v-model="timeList" type="daterange" align="right" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false" :picker-options="pickerOptions"
                    @change="changeTime" class="publicCss" />
                <el-button type="primary" @click="getList('click')">查询</el-button>
            </div>
        </template>
        <vxetablebase :id="'pddGroupStatistics202408041459'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
            :showsummary="true" :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            :summaryarry="summaryarry" @summaryClick='onsummaryClick' style="width: 100%; height: 690px; margin: 0"
            :noToFixed="true" />
        <my-pagination ref="pager" :total="total" @page-change="detailPagechange" @size-change="detailSizechange" />

        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false" direction="btt">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" @change="chatSearch" :picker-options="pickerOptions" style="margin: 10px;" />
                <buschar :analysisData="chatProp.data" v-if="!chatProp.chatLoading"></buschar>
            </div>
            <div v-else v-loading="chatProp.chatLoading"></div>
        </el-drawer>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getPddInquireGradeComputePageList_Group, getPddInquireGradeComputeChat, getPddInquireGradeComputeChat_Group } from '@/api/customerservice/pddInquirs'
import buschar from '@/components/Bus/buschar'
import dayjs from 'dayjs'
import { pickerOptions } from '@/utils/tools'
import middlevue from "@/store/middle.js"
const tableCols = [
    { istrue: true, prop: 'groupName', label: '组名', sortable: 'custom', type: 'click', handle: (that, row) => that.linkToUser(row) },
    { istrue: true, prop: 'inquirs', label: '咨询人数', sortable: 'custom' },
    { istrue: true, prop: 'successpayrate', label: '询单转化率', sortable: 'custom', formatter: (row) => row.successpayrate + '%' },
    { istrue: true, prop: 'gradeCommission', label: '提成', sortable: 'custom' },
    {
        istrue: true, prop: 'buchar', type: "button", width: '100', label: '趋势图', summaryEvent: true, btnList: [
            { label: "查看", handle: (that, row) => that.openChat(row) },
        ]
    }
]
export default {
    name: 'pddGroupStatistics',
    components: {
        MyContainer, vxetablebase, buschar
    },
    data() {
        return {
            tableData: [],//列表数据
            tableCols,
            that: this,
            total: 0,
            ListInfo: {//列表参数
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: 'successpayrate',//排序字段
                isAsc: false,//是否升序
                startDate: null,//开始时间
                endDate: null,//结束时间
                shopCode: null,//店铺编码
                shopCodeList: null,//店铺编码集合
                shopName: null,//店铺名称
                shopNameList: null,//店铺名称集合
                groupName: null,//组名称
                groupNameList: null,//组名称集合
                groupShortName: null,//分组归属
                snick: null,//昵称
                snickList: null,//昵称集合
                sname: null,//姓名
                snameList: null,//姓名集合
                pddInquireGradeWayDate: null,//按指定系数计算
            },
            timeList: null,//时间范围
            pickerOptions,
            summaryarry: null,
            chatProp: {
                chatDialog: false,//趋势图弹窗
                chatTime: null,//趋势图时间
                chatLoading: true,//趋势图loading
                data: [],//趋势图数据
            },
            chatInfo: {
                groupName: null,//组别
                sname: null,//姓名
                shopName: null,//店铺
                snick: null,//昵称
                startDate: null,//开始时间
                endDate: null,//结束时间
                pddInquireGradeWayDate: null,//版本时间
            },
        };
    },
    mounted() {
        this.getList()
    },
    methods: {
        linkToUser(row) {
            this.$emit('toUser', 'third')
            let params = {
                groupName: row.groupName,
                startDate: this.ListInfo.startDate,
                endDate: this.ListInfo.endDate,
            }
            middlevue.$emit('userGetlist', params)
        },
        //趋势图时间改变
        async chatSearch() {
            this.chatInfo.startDate = dayjs(this.chatProp.chatTime[0]).format('YYYY-MM-DD')
            this.chatInfo.endDate = dayjs(this.chatProp.chatTime[1]).format('YYYY-MM-DD')
            this.chatProp.chatLoading = true
            const data = await getPddInquireGradeComputeChat(this.chatInfo)
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        clearInfo() {
            this.chatInfo = {
                groupName: null,//组别
                sname: null,//姓名
                shopName: null,//店铺
                snick: null,//昵称
                startDate: null,//开始时间
                endDate: null,//结束时间
                pddInquireGradeWayDate: null,//版本时间
            }
        },
        async onsummaryClick(property) {
            this.chatInfo.groupName = null
            this.chatInfo.sname = null
            this.chatInfo.shopName = null
            this.chatInfo.snick = null
            this.chatProp.chatLoading = true
            this.chatInfo.pddInquireGradeWayDate = this.ListInfo.pddInquireGradeWayDate ? this.ListInfo.pddInquireGradeWayDate : null
            this.publicChangeTime(this.chatInfo)
            this.chatProp.chatLoading = true
            if (property == 'buchar') {
                const data = await getPddInquireGradeComputeChat(this.chatInfo)
                this.chatProp.data = data
                this.chatProp.chatDialog = true
                this.chatProp.chatLoading = false
            }
        },
        publicChangeTime(row) {
            if (this.timeList) {
                let time = dayjs(this.timeList[1]).diff(dayjs(this.timeList[0]), 'day')
                if (time >= 30) {
                    this.chatProp.chatTime = this.timeList
                    this.chatInfo.startDate = dayjs(this.timeList[0]).format('YYYY-MM-DD')
                    this.chatInfo.endDate = dayjs(this.timeList[1]).format('YYYY-MM-DD')
                } else {
                    //否则就从timeList的结束时间往前推30天
                    this.chatProp.chatTime = [dayjs(this.timeList[1]).subtract(30, 'day').format('YYYY-MM-DD'), this.timeList[1]]
                    this.chatInfo.startDate = dayjs(this.timeList[1]).subtract(30, 'day').format('YYYY-MM-DD')
                    this.chatInfo.endDate = dayjs(this.timeList[1]).format('YYYY-MM-DD')
                }
            }
            this.chatInfo.groupName = row.groupName ? row.groupName : this.ListInfo.groupName
        },
        async openChat(row) {
            this.clearInfo()
            this.publicChangeTime(row)
            this.chatProp.chatLoading = true
            const data = await getPddInquireGradeComputeChat_Group(this.chatInfo)
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        //时间范围改变
        changeTime(e) {
            if (e) {
                this.ListInfo.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs(e[1]).format('YYYY-MM-DD')
            } else {
                this.ListInfo.startDate = null
                this.ListInfo.endDate = null
            }
            this.getList()
        },
        showChat() {

        },
        async getList(type) {
            if (type == 'click') {
                this.ListInfo.currentPage = 1
                this.ListInfo.orderBy = 'successpayrate'
                this.ListInfo.isAsc = false
            }
            if (this.ListInfo.groupName) {
                //清除空格
                this.ListInfo.groupName = this.ListInfo.groupName.replace(/\s+/g, "")
            }
            if (!this.timeList) {
                //默认时间为当前时间往前推一个月
                this.ListInfo.startDate = dayjs().subtract(1, 'month').format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
                this.timeList = [this.ListInfo.startDate, this.ListInfo.endDate]
            }
            const { data, success } = await getPddInquireGradeComputePageList_Group(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
            }
        },
        //页面数量改变
        detailSizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        detailPagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.top {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.publicCss {
    width: 220px;
    margin-right: 20px;
}
</style>