<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.updateTimeStart" :endDate.sync="ListInfo.updateTimeEnd"
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.supplierName" placeholder="供应商名称" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.aliProductName" placeholder="标题" maxlength="50" clearable
                    class="publicCss" />
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodCode" v-model="ListInfo.goodCode"
                    width="200px" placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300"
                    :maxlength="6000" @callback="productCodeCallback" title="商品编码" style="margin: 0 10px 0 0;">
                </inputYunhan>
                <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.brandId" multiple clearable filterable collapse-tags placeholder="采购员"
                    class="publicCss">
                    <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, formatLinkProCode } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { getAliGoodInfoBindRecordList, exportAliGoodInfoBindRecordList } from '@/api/inventory/basicgoods'
import { getAllProBrand, getBianManPositionListV2 } from '@/api/inventory/warehouse'
import inputYunhan from "@/components/Comm/inputYunhan";
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'supplierName', label: '供应商名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'aliProductId', label: '链接', type: 'html', formatter: (row) => `<a href="https://detail.1688.com/offer/${row.aliProductId}.html" target="_blank" class="custom-link ">${row.aliProductId}</a>` },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'aliProductName', label: '标题', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'aliProductAttribute', label: '规格属性', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'mutil', label: '倍数', },
    { width: 'auto', align: 'center', prop: 'productImgUrl', label: '图片', type: 'images', align: 'center' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'brandId', label: '采购', formatter: (row) => row.brandName },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'updateTime', label: '编辑时间', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, inputYunhan
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            brandList: []
        }
    },
    async mounted() {
        this.getAllBrand()
        await this.getList()
    },
    methods: {
        async productCodeCallback(val) {
            this.ListInfo.goodCode = val;
        },
        async getAllBrand() {
            const { data, success } = await getAllProBrand()
            if (!success) return this.$message.error('获取采购失败')
            this.brandList = data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportAliGoodInfoBindRecordList(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '1688商品编码对应关系' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getAliGoodInfoBindRecordList(this.ListInfo)
                if (success) {
                    data.list.forEach(item => {
                        item.productImgUrl = JSON.stringify(JSON.parse(item.productImgUrl).slice(-1).map(url => ({ url })))
                    })
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                console.log(error);
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

::v-deep .custom-link {
    color: blue;
    /* 设置链接颜色 */
    text-decoration: none;
    /* 去掉下划线 */
}
</style>
