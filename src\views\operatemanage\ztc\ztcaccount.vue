<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
        <el-form-item label="时间:">
          <el-date-picker style="width:320px"
            v-model="Filter.timerange"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="从："
            end-placeholder="到："
            :picker-options="pickerOptions"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="所属店铺:" label-position="right" label-width="72px">
          <el-select v-model="Filter.idShop" placeholder="请选择" class="el-select-content">
            <el-option label="所有" value></el-option> 
            <el-option 
              v-for="item in shopList"
              :key="item.id"
              :label="item.shopName"
              :value="item.id">             
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
         <el-form-item>
          <el-button type="primary" @click="onExport">导出</el-button>
        </el-form-item>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' 
              :hasexpand='false' @sortchange='sortchange' :tableData='ZTCKeyWordList' 
              @select='selectchange' :isSelection='false'
         :tableCols='tableCols' :loading="listLoading" :summaryarry='summaryarry' style="height:730px">
      <el-table-column type="expand">
        <template slot-scope="props">
        <div>
          <el-table :data="props.row.detaildata" style="width: 100%">
            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
            </el-table-column>
          </el-table>
        </div>
      </template>
      </el-table-column>
       <template slot='extentbtn'>
          <el-button-group>            
            <el-button type="primary" @click="onRefresh">刷新</el-button>
          </el-button-group>
        </template>
    </ces-table>    
    <!--分页-->
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getZTCKeyWordList"
      />
    </template>

    <el-dialog title="导入直通车-账户" :visible.sync="dialogVisibleSyj" width="30%">
      <span>
          <el-upload ref="upload2" class="upload-demo" 
                  :auto-upload="false"
                  :multiple="false"
                  :limit="1"
                  action
                  accept=".xlsx"
                  :http-request="uploadFile2"
                  :on-success="uploadSuccess2">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template> 
                <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传</my-confirm-button>
          </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList,
         getDirectorGroupList as getDirectorGroupList,
         getDirectorList as getDirectorList
       } from '@/api/operatemanage/base/shop';
import { formatPlatform,formatLink} from "@/utils/tools";
import { TimelineComponent } from 'echarts/components';
import {importZTCAccountAsync as importZTCAsync, getPageList as getZTCPageList,exportdata } from '@/api/operatemanage/ztc/ztcaccount'

const tableCols =[
      //{istrue:true,prop:'id',label:'编号', width:'200',sortable:'custom'},
      {istrue:true,prop:'platform',label:'平台', width:'50', formatter:(row)=>formatPlatform(row.platform)},
      {istrue:true,prop:'date',label:'日期', width:'150',sortable:'custom'},
      {istrue:true,prop:'nameShop',label:'店铺名', width:'80'},
      {istrue:true,prop:'countShow',label:'展现量', width:'80',sortable:'custom'},
      {istrue:true,prop:'countClick',label:'点击量', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountAll',label:'花费', width:'80',sortable:'custom'},
      {istrue:true,prop:'rateClick',label:'点击率', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.rateClick?.toFixed(2)+'%'}},
      {istrue:true,prop:'amountClickAvg',label:'平均点击花费', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountClickAvg?.toFixed(2)}},
      {istrue:true,prop:'amountShow1000',label:'千次展现花费', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountShow1000?.toFixed(2)}},
      {istrue:true,prop:'countStar',label:'总收藏数', width:'80',sortable:'custom'},
      {istrue:true,prop:'countStarPro',label:'宝贝收藏数', width:'80',sortable:'custom'},
      {istrue:true,prop:'countStarShop',label:'店铺收藏数', width:'80',sortable:'custom'},
      {istrue:true,prop:'countShoppingCarAll',label:'总购物车数', width:'80',sortable:'custom'},
      {istrue:true,prop:'countShoppingCarDirect',label:'直接购物车数', width:'80',sortable:'custom'},
      {istrue:true,prop:'countShoppingCarIndirect',label:'间接购物车数', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountShoppingCar',label:'加购成本', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountShoppingCar?.toFixed(2)}},
      {istrue:true,prop:'amountStar',label:'宝贝收藏成本', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountStar?.toFixed(2)}},
      {istrue:true,prop:'rateStarConversion',label:'宝贝收藏转化率', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.rateStarConversion?(row.rateStarConversion.toFixed(2) + '%'):null;}},
      {istrue:true,prop:'rateShoppingCarConversion',label:'加购率', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.rateShoppingCarConversion?(row.rateShoppingCarConversion.toFixed(2) + '%'):null}},
      {istrue:true,prop:'amountPresale',label:'总预售成交金额', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountPresale?.toFixed(2)}},
      {istrue:true,prop:'countPresale',label:'总预售成交笔数', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.countPresale?.toFixed(2)}},
      {istrue:true,prop:'amountPresaleDirect',label:'直接预售成交金额', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountPresaleDirect?.toFixed(2)}},
      {istrue:true,prop:'countPresaleDirect',label:'直接预售成交笔数', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountPresaleIndirect',label:'间接预售成交金额', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountPresaleIndirect?.toFixed(2)}},
      {istrue:true,prop:'countPresaleIndirect',label:'间接预售成交笔数', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountDealAll',label:'总成交金额', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountDealAll?.toFixed(2)}},
      {istrue:true,prop:'amountDealDirect',label:'直接成交金额', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountDealDirect?.toFixed(2)}},
      {istrue:true,prop:'amountDealIndirect',label:'间接成交金额', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountDealIndirect?.toFixed(2)}},
      {istrue:true,prop:'countDealAll',label:'总成交笔数', width:'80',sortable:'custom'},
      {istrue:true,prop:'countDealDirect',label:'直接成交笔数', width:'80',sortable:'custom'},
      {istrue:true,prop:'countDealIndirect',label:'间接成交笔数', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountDealAvg',label:'平均每笔花费', width:'80',type:'html',formatter:(row)=>{return row.amountDealAvg?.toFixed(2)}},
      {istrue:true,prop:'rateInputOutput',label:'投入产出比', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.rateInputOutput?.toFixed(2)}},
      {istrue:true,prop:'rateClickConversion',label:'点击转化率', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.rateClickConversion?(row.rateClickConversion.toFixed(2)+'%'):null}},
      {istrue:true,prop:'rateClickConversionDirect',label:'直接点击转化率', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.rateClickConversionDirect?(row.rateClickConversionDirect.toFixed(2)+'%'):null}},
      {istrue:true,prop:'countShoppingGoldRecharge',label:'购物金充值笔数', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountShoppingGoldRecharge',label:'购物金充值金额', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountShoppingGoldRecharge?.toFixed(2)}},
      {istrue:true,prop:'countNaturalFlow',label:'自然流量曝光', width:'80',sortable:'custom'},
      {istrue:true,prop:'amountNaturalFlow',label:'自然流量转化金额', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountNaturalFlow?.toFixed(2)}},
      {istrue:true,prop:'amountInAdvance',label:'特权订金金额', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountInAdvance?.toFixed(2)}},
      {istrue:true,prop:'amountTail',label:'尾款金额', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountTail?.toFixed(2)}},
      {istrue:true,prop:'amountFixedPrice',label:'一口价金额', width:'80',sortable:'custom',type:'html',formatter:(row)=>{return row.amountFixedPrice?.toFixed(2)}},
      {istrue:true,prop:'countNewDealUser',label:'新成交用户数', width:'80',sortable:'custom'},
      {istrue:true,prop:'rateNewDealUser',label:'新成交用户占比', width:'80',sortable:'custom', type:'html',formatter:(row)=>{return row.rateNewDealUser?(row.rateNewDealUser.toFixed(2)+'%'):null}},
     ];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      Filter: {
        timerange: [formatTime(dayjs().startOf("month"), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")],
      },
      shopList:[],
      ZTCKeyWordList: [],
      tableCols:tableCols,
      total: 0,
      summaryarry:{countShow_sum:10},
      pager:{OrderBy:"date",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      pickerOptions:{
        shortcuts: [{
            text: '今天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date().setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '昨天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 1).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '3天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 2).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '7天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 6).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '15天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 14).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '30天',
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
              const start = new Date(new Date(end.getTime() - 3600 * 1000 * 24 * 29).setHours(0,0,0,0));
              picker.$emit('pick', [start, end]);
            }
          }]
      },
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
    };
  },
  async mounted() {
    await this.getShopList();
  },
  methods: {
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    //所属店铺列表
    async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=res1.data;
      return;
    },
    onImportSyj(){
      this.dialogVisibleSyj = true
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      const res = importZTCAsync(form);
      this.$message({message: '上传成功,正在导入中...', type: "success"}); 
    },
    async uploadSuccess2(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
    },
    async onSubmitupload2() {
      this.$refs.upload2.submit()
    },
    onRefresh(){
        this.onSearch()
    },
    onSearch(){
       this.$refs.pager.setPage(1);
       this.getZTCKeyWordList();
    },


 async onExport(){
       const para = {...this.Filter};
      if (this.Filter.timerange) {
        para.dateStart = this.Filter.timerange[0];
        para.dateEnd = this.Filter.timerange[1];
      }

      if(!(para.dateStart&&para.dateEnd)){
        this.$message({message:"请先选择日期！",type:"warning"});
        return;
      }
      
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };

      console.log(para)

      this.listLoading = true;
      const res = await exportdata(params);
      this.listLoading = false;
     
  
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download','直通车账号数据_' +  new Date().toLocaleString() + '_.xlsx' )
      aLink.click()
    },


    async getZTCKeyWordList(){
      const para = {...this.Filter};
      if (this.Filter.timerange) {
        para.dateStart = this.Filter.timerange[0];
        para.dateEnd = this.Filter.timerange[1];
      }

      if(!(para.dateStart&&para.dateEnd)){
        this.$message({message:"请先选择日期！",type:"warning"});
        return;
      }
      
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };

      console.log(para)

      this.listLoading = true;
      const res = await getZTCPageList(params);
      this.listLoading = false;
      console.log(res.data.list)
      console.log(res.data.summary)

      this.total = res.data.total
      this.ZTCKeyWordList = res.data.list;
      this.summaryarry=res.data.summary;
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>