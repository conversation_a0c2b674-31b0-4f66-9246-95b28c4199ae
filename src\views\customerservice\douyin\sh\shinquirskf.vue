<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='inquirslist' @select='selectchange' :isSelection='false' :tableCols='tableCols'
            :loading="listLoading" :summaryarry="summaryarry">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.snick" placeholder="客服昵称" style="width:160px;" clearable
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <datepicker v-model.trim="filter.sdate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date"
                            :picker-options="pickerOptions" :default-value="defaultDate">
                        </datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onImportSyj">导入</el-button>
                    <el-button type="primary" @click="onImportSyjModel">下载模板</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getinquirsList" />
        </template>
        <el-dialog title="抖音客服人员咨询数据" :visible.sync="dialogVisibleSyj" width="30%" :close-on-click-modal="false"
            v-dialogDrag>
            <el-form ref="improtGroupForm" :model="improtGroupForm" label-width="55px" label-position="left">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2"
                            :on-change="onUploadChange2" :on-remove="onUploadRemove2">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                                @click="onSubmitupload2" :loading="uploadLoading">上传</my-confirm-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
    getDouYinInquirsKfPageList, deleteDouYinInquirsKfAsync, importDouYinInquirsKfAsync
} from '@/api/customerservice/douyininquirs'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
const tableCols = [
    { istrue: true, prop: 'shopCode', label: '店铺', width: '160', sortable: 'custom' , formatter: (row) => row.shopName},
    { istrue: true, prop: 'huiHuaId', label: '会话ID', width: '160', sortable: 'custom' },
    //{ istrue: true, prop: 'huiHuaTime', label: '日期描述', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'sdate', label: '日期', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'snick', label: '昵称', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'huiHuaResult', label: '评价值', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'huiHuaFlag', label: '评价标签', width: '300', sortable: 'custom' },
    { istrue: true, prop: 'huiHuaType', label: '评价类型', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '导入时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'batchNumber', label: '导入批次', width: '170', sortable: 'custom' },
    {
        istrue: true, type: "button", label: '操作', width: "115",
        btnList: [
            { label: "删除", handle: (that, row) => that.deleteBatch(row, 0) },
            { label: "删除批次", handle: (that, row) => that.deleteBatch(row, 1) }
        ]
    }
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
    data() {
        return {
            date: '',
            that: this,
            filter: {
                inquirsType: 1,
            },
            shopList: [],
            userList: [],
            inquirslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "sdate", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            improtGroupForm: {
            },
            pickerOptions: {
                disabledDate(date) {
                    // 设置禁用日期
                    const start = new Date('1970/1/1');
                    const end = new Date('9999/12/31');
                    return date < start || date > end;
                }
            },
            defaultDate: new Date('1970/1/1'),
            uploadLoading: false,
        };
    },
    async mounted() {
        //await this.getAllShopList();
    },
    methods: {
        async getAllShopList() {
            let shops = await getAllShopList();
            this.shopList = [];
            shops.data?.forEach(f => {
                if (f.shopCode && f.platform == 6)
                    this.shopList.push(f);
            });
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getinquirsList();
        },
        async getinquirsList() {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            console.log(params)
            this.listLoading = true;
            const res = await getDouYinInquirsKfPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.inquirslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async deleteBatch(row, type) {
            var that = this;
            this.$confirm("确认要执行删除" + (type == 1 ? "批次" : "") + "的操作吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let del = await deleteDouYinInquirsKfAsync({ idOrBatchNum: (type == 1 ? row.batchNumber : row.id), type: type });
                if (del?.success) {
                    that.$message({ message: '已删除', type: "success" });
                    that.onSearch();
                }
                else {
                    that.$message({ message: '发生异常，请刷新后重试', type: "error" });
                }
            });
        },
        onImportSyjModel() {
            window.open("/static/excel/customerservice/抖音服务数据模板.xlsx", "_blank");
        },
        async onImportSyj() {
            this.dialogVisibleSyj = true;
        },
        async onUploadChange2(file, fileList) {
            this.fileList = fileList;
        },
        async onUploadRemove2(file, fileList) {
            this.fileList = [];
        },
        async uploadFile2(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("inquirsType", 1);
            this.uploadLoading = true;
            const res = await importDouYinInquirsKfAsync(form);
            this.uploadLoading = false;
            if (res?.success) {
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
                this.dialogVisibleSyj = false;
            }
            // else {
            //     this.$message({ message: '发生异常，请刷新后重试', type: "error" });
            // }
        },
        async uploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        async onSubmitupload2() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload2.submit();
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
