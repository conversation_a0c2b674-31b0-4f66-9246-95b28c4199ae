<template>
    <container>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
            :tableData='list'  :tableCols='tableCols' :isSelection="false" @select="selectchange"
            :tableHandles='tableHandles' @cellclick="cellclick"
            :loading="listLoading">
            </ces-table>
            <template #footer>
                <my-pagination ref="pager" :total="total" :checked-count="sels.length"  @get-page="getlist"/>
            </template>

        <el-drawer title="设置" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editparmVisible"
                direction="btt" size="'auto'" class="el-drawer__wrapper"  style="position:absolute;">
            <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
            <div class="drawer-footer">
                <el-button @click.native="editparmVisible = false">取消</el-button>
                <my-confirm-button type="submit"  :loading="editparmLoading" @click="onSetEditParm" />
            </div>
        </el-drawer>

    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { getReplaceDayReportDetailList, batchUpdateReplace } from "@/api/profit/reportday"

const tableCols =[
        {istrue:true,prop:'payTime',label:'订单付款时间', width:'105',sortable:'custom',},
        {istrue:true,prop:'orderTime',label:'代拍操作时间', width:'105',sortable:'custom',}, 
        {istrue:true,prop:'sendTime',label:'发货时间', width:'100',sortable:'custom',}, 
        {istrue:true,prop:'createdUserName',label:'下单操作员', width:'100',},
        {istrue:true,prop:'orderType',label:'订单类型', tipmesg:'', width:'100',},
        {istrue:true,prop:'status',label:'订单状态', tipmesg:'', width:'100',sortable:'custom',},
        {istrue:true,prop:'orderNoInner',label:'聚水潭内部订单号', width:'150',sortable:'custom',}, 
        {istrue:true,prop:'shopName',label:'店铺名称', width:'200',},       
        {istrue:true,prop:'goodsCode',label:'商品编码', width:'120',},  
        {istrue:true,prop:'goodsName',label:'商品名称', width:'125',sortable:'custom'},     
        {istrue:true,prop:'orderAmount',label:'订单金额', width:'100',sortable:'custom',},
        {istrue:true,prop:'deductAmountPaid',label:'平台扣点', tipmesg:'', width:'100',sortable:'custom',},
        {istrue:true,prop:'qty',label:'数量',sortable:'custom', width:'auto',},       
        {istrue:true,prop:'productAmount',label:'产品金额', tipmesg:'实付给厂家供应商的金额', width:'100',sortable:'custom',},
        {istrue:true,prop:'freightAmount',label:'运费金额', tipmesg:'', width:'100',sortable:'custom',},
        {istrue:true,prop:'replaceAmount',label:'代拍总金额', tipmesg:'产品金额+运费金额', width:'100',sortable:'custom',},
        // {istrue:true,prop:'goodsCost',label:'订单金额合计', width:'100',sortable:'custom',},
        // {istrue:true,prop:'expressCompany',label:'快递公司', width:'auto',sortable:'custom',formatter:(row)=> !row.expressName? " " : row.expressName},
        // {istrue:true,prop:'expressNo',label:'快递单号', width:'auto',sortable:'custom',},
        // {istrue:true,prop:'replaceAmount',label:'代拍金额', width:'auto',sortable:'custom',},
        // {istrue:true,prop:'purchaseDitch',label:'采购渠道', width:'auto',sortable:'custom',},
        // {istrue:true,prop:'buyNo',label:'采购订单号', width:'100',sortable:'custom',},
        // {istrue:true,prop:'refundType',label:'退款状态', width:'auto',sortable:'custom',},
        // {istrue:true,prop:'refundAmount',label:'实际退回', width:'auto',sortable:'custom',},
        // {istrue:true,prop:'refundRemark',label:'财务备注', width:'auto',sortable:'custom',},
        // {istrue:true,type:'button',btnList:[{label:"编辑",handle:(that,row)=>that.onHand(row)}]},
]

const tableHandles=[];

const startTime = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminReplacedayreport',
    components: {container, cesTable, MyConfirmButton},

    data() {
        return {
            that: this,
            filter:{
                startTime: null,
                endTime: null,
                timerange:[startTime, endTime],
                procode:null,
                title:null,
                platform:null,
                shopCode:null,
                groupId:null,
                operateSpecialId:null,
                user1Id:null,
                user3Id:null,
                shopId:null,
                platform:null,
                newPattern:null,
                customer:null,
                orderNoInner:null,
                orderNo:null
            },
            list: [],
            summaryarry:{},
            pager:{OrderBy:"payTime",IsAsc:false},
            pickerOptions:{
                disabledDate(time){
                return time.getTime()>Date.now();
                }
            },        
            onHandNumber:null,   
            tableCols:tableCols,
            tableHandles:tableHandles,
            total: 0,
            sels: [], 
            editparmLoading: false,
            editparmVisible: false,
            dialogVisible: false,
            listLoading: false,
            dialogVisible: false,
            uploadLoading: false,
            showDetailVisible: false,
            autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[{type:'hidden',field:'id',title:'id',value: '',col:{span:12}},
                     {type:'InputNumber',field:'replaceAmount',title:'代拍金额',value: null,props:{min:0,precision:2,step:0.5},col:{span:6}},
                     {type: "select",field: "purchaseDitch",title: "采购渠道",
                        options: [
                            {"value": "阿里巴巴", "label": "阿里巴巴", "disabled": false},
                            {"value": "线下", "label": "线下", "disabled": false},
                        ],
                     },
                     {type:'input',field:'buyNo',title:'采购订单号',validate:[{ required: true, message: '请输入信息', trigger: 'blur' },],},
                     {type: "select",field: "refundType",title: "退款状态",
                        options: [
                            {"value": "退款中", "label": "退款中", "disabled": false},
                            {"value": "已退出", "label": "已退出", "disabled": false},
                        ],
                     },
                     {type:'InputNumber',field:'refundAmount',title:'实际退回',value: null,props:{min:0,precision:2,step:0.5},col:{span:6}},
                     {type:'input',field:'refundRemark',title:'财务备注',props: {type: "textarea",}},                     
                    ]
          },
        };
    },

    async mounted() {
        //await this.onSearch()
    },

    methods: {
        //查询第一页
        async onSearch1(para) {
            this.filter.orderNoInner = para.orderNoInner
            await this.onSearch()
        },
        async onSearch() {
            await this.getlist()
            this.$refs.pager.setPage(1)
        },
        async getlist() {
            
            var pager = this.$refs.pager.getPager();
            var page  = this.pager;
            const params = { ...pager,...page,... this.filter}
            if(params===false){
                return;
            }
            if(params===false){
                    return;
            }
            this.listLoading = true
            const res = await getReplaceDayReportDetailList(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry=res.data.summary;
            this.list = data
       },
       async onHand(row){   
           this.editparmVisible = true      

           var model = {id: row.id, buyNo: row.buyNo, replaceAmount: row.replaceAmount ,refundType: row.refundType,  isTrue: true,
                        refundAmount: row.refundAmount, refundRemark: row.refundRemark, purchaseDitch: row.purchaseDitch} 
           var arr = Object.keys(this.autoform.fApi)
           if (arr.length > 0)
             await this.autoform.fApi.resetFields()

           this.$nextTick(async() =>{
               await this.autoform.fApi.setValue(model)
           })  
           

       },
       async onSetEditParm(){
            this.editparmLoading = true
            this.autoform.fApi.validate(async (valid, fail) => {
                if(valid){
                    const formData = this.autoform.fApi.formData();

                    await batchUpdateReplace(formData)

                    this.editparmLoading = false
                    this.editparmVisible = false                   
                    this.getlist()
                    await this.$emit('nSearch')
                }
            })
       },
        async sortchange(column){
        if(!column.order)
            this.pager={};
        else{
            this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
        }
        await this.onSearch();
        },  
        selectchange:function(rows,row) {
            this.selids=[];console.log(rows)
            rows.forEach(f=>{
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event){
        
        }, 
    },
};
</script>

<style lang="scss" scoped>

</style>