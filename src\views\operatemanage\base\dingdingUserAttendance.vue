<template>
    <el-container style="height: 100%; border: 1px solid #eee">
        <el-aside width="245px" style="background-color: rgb(238, 241, 246)">
            <DeptViewLeftTree @selected="leftTreeSelected" />
        </el-aside>
        <el-col :span="18" style="height:780px;width: 86%;">
            <container>
                <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='false' :isSelectColumn='true' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' :loading="listLoading">

                    <template slot='extentbtn'>
                        <el-button-group>
                            <el-button style="padding: 0;margin: 0;">
                                <el-input v-model="filter.userName" placeholder="姓名" clearable style="width:120px;" />
                            </el-button>
                            <el-button style="padding: 0;margin: 0;">
                                <el-date-picker style="width: 230px" v-model="filter.Sdate" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至">
                                </el-date-picker>
                            </el-button>
                            <el-button type="primary" @click="userOnSearch">查询</el-button>
                            <el-button type="primary" @click="onExport">导出</el-button>
                        </el-button-group>
                    </template>
                </ces-table>
                <template #footer>
                    <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
                </template>
            </container>
        </el-col>
    </el-container>
</template>
  <script>
    import { getAttendanceOrderNodesList,exportUserAttendanceList } from '@/api/operatemanage/base/dingdingShow'
    import dingdingDept from '@/views/operatemanage/base/dingdingDept.vue'
    import container from '@/components/my-container/noheader'
    import cesTable from "@/components/Table/table.vue";
    import dayjs from "dayjs";
    import { formatTime } from "@/utils";
    import DeptViewLeftTree from '@/views/admin/organization/DeptViewLeftTree.vue'
    const tableCols = [
        { istrue: true, prop: 'workDate', label: '日期', style: 'center', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'avatar', label: '头像', type: "image", width: '70' },
        { istrue: true, prop: 'userId', label: 'ID', sortable: 'custom', width: '160' },
        { istrue: true, prop: 'userName', label: '姓名', width: '100'},
        { istrue: true, prop: 'hiredDate', label: '入职时间', type: 'editor', width: '150'},
        { istrue: true, prop: 'employeeStatus', label: '员工状态', style: 'center', width: 'auto', formatter: (row) => row.employeeStatus==2?"试用":row.employeeStatus==3?"正式":"离职" },
        { istrue: true, prop: 'deptName', label: '所属部门', type: 'editor', width: '100'},
        { istrue: true, prop: 'workDuration', label: '打卡时长', type: 'editor', width: '100', sortable: 'custom', tipmesg: '小时' },
        { istrue: true, prop: 'workTime', label: '打卡时间段', style: 'center', width: 'auto' },
        { istrue: true, prop: 'restTime', label: '班次内休息时段', style: 'center', width: 'auto',permission: "listcolumnshow" },
        { istrue: true, prop: 'leaveDuration', label: '请假时长', type: 'editor', width: '100', sortable: 'custom', tipmesg: '小时',permission: "listcolumnshow" },
        { istrue: true, prop: 'travelDuration', label: '出差/外出时长', type: 'editor', width: '90', sortable: 'custom', tipmesg: '小时',permission: "listcolumnshow" },
        { istrue: true, prop: 'overtimeDuration', label: '加班时长', type: 'editor', width: '100', sortable: 'custom', tipmesg: '小时',permission: "listcolumnshow" },
        { istrue: true, prop: 'actualDuration', label: '工作时长', type: 'editor', width: '90', sortable: 'custom', tipmesg: '小时',permission: "listcolumnshow" }
    ];
    const tableHandles = []
    export default {
        name: 'DingDingUserAttendance',
        components: { dingdingDept, container, cesTable, DeptViewLeftTree },
        data () {
            return {
                that: this,
                list: [],
                tableCols: tableCols,
                tableHandles: tableHandles,
                pager: { OrderBy: " workDate ", IsAsc: false },
                summaryarry: {},
                total: 0,
                sels: [],
                selids: [],
                listLoading: false,
                pageLoading: false,
                //部门编码与分公司ID
                deptCode: '',
                corpId: '',
                filter: {
                     Sdate: [formatTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")]
                    }
            }
        },
        mounted () {
            //this.$refs.dingdingDept.selectDept= 597959144;
            //this.userOnSearch();
        },
        beforeUpdate () { },
        methods: {
            //获取分公司树形结构每一行部门编码以及公司ID
            leftTreeSelected(row) {
                this.deptCode = row && row.full_code ? row.full_code : '';
                this.corpId = row && row.corpId ? row.corpId : '';
                this.deptOnSearch();
            },
            //按分公司显示考勤信息
            deptOnSearch() {
                this.$refs.pager.setPage(1)
                this.getlist()
            },
            async onExport(){
            var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            const params = {   ... this.filter, 
                                deptCode: this.deptCode,
                                corpId: this.corpId
                            }

            if (params === false) {
                return;
            }
            var res = await exportUserAttendanceList(params);
            loadingInstance.close();
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '考勤数据导出_' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
            },
            userOnSearch () {
                this.$refs.pager.setPage(1)
                this.getlist()
            },
            async getlist () {
                //var deptId = this.$refs.dingdingDeptTwo.selectDept;
                // if (deptId == null || deptId == "") {
                //     return;
                // }
                this.filter.entryStartTime = null;
                this.filter.entryEndTime = null;
                if (this.filter.timerange) {
                    this.filter.entryStartTime = this.filter.timerange[0];
                    this.filter.entryEndTime = this.filter.timerange[1];
                }
                this.filter.StartSdate = null;
                this.filter.EndSdate = null;
                if (this.filter.Sdate) {
                    var d = new Date(this.filter.Sdate[0])
                    var startsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                    this.filter.StartSdate = startsdate;

                    d = new Date(this.filter.Sdate[1])
                    var endsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                    this.filter.EndSdate = endsdate;
                }
                //this.filter.dept_id = deptId;
                var pager = this.$refs.pager.getPager()
                const params = { ...pager, ...this.pager, ... { ...this.filter } ,
                                deptCode: this.deptCode,
                                corpId: this.corpId
                            }
                this.listLoading = true
                this.list = [];
                const res = await getAttendanceOrderNodesList(params);
                this.listLoading = false
                if (!res?.success) return
                this.total = res?.data?.total
                const data = res?.data?.list
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
                this.summaryarry = res.data?.summary;
            },
            sortchange (column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.userOnSearch();
            },
            selsChange: function (sels) {
                this.sels = sels
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
        }
    }
  </script>
  <style scoped lang="scss" >
    ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
        background-color: rgb(233, 233, 248);
        border-radius: 3px;
    }
    ::v-deep .el-table__body-wrapper {
        overflow: auto;
    }
</style>
  