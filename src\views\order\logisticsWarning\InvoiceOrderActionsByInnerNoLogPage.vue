<template>
    <my-container v-loading="pageLoading" style="position: relative">
        <el-tabs v-if="activeName" v-model="activeName" @tab-click="handleClick"
            style="position: absolute;right: 0;top: 0;left: 0; background-color:white;">
            <el-tab-pane v-for="item in orderNoInners" :label="item.o_id + ''" :name="item.o_id + ''"></el-tab-pane>
        </el-tabs>
        <div
            style="cursor:pointer;margin-left:95%; margin-top: 5px;margin-bottom: 2px;position: absolute;right: 0;top: 0;left: 0; background-color:white;">
            <span @click="orderByList" style="color:#0094ff;display:inline-block;user-select:none;">
                ⬆⬇{{ isOrder ? "正序" : "倒序" }}
            </span>

        </div>
        <tbody v-for="orderLog in orderLogList">
            <tr style="line-height:30px;">
                <td style="width:150px;height:20px;">{{ orderLog.created }}</td>
                <td style="width:120px;height:20px;">{{ orderLog.creator_name }}</td>
                <td style="width:300px;height:20px;">{{ orderLog.name }}</td>
                <td style="height:20px;">{{ orderLog.remark }}</td>
            </tr>
        </tbody>

    </my-container>
</template>

<script>
import MyContainer from '@/components/my-container';

import { jstGetOrdersByOrderNo } from "@/api/order/orderdeductmoney";
import { jstGetOrderActionsByInnerNo, jstGetOrderActionsByInnerNos,GetOrderActionsByTxOrderNo } from "@/api/order/orderdeductmoney";
export default {
    name: 'OrderActionsByInnerNos',
    components: { MyContainer },
    props: {
        orderNo: { type: String, default: "" },
        orderNoInner: { type: String, default: "" },
        isTx: { type: Boolean, default: false },
    },
    data () {
        return {
            activeName: '',
            orderNoInners: [],
            orderLogList: [],
            pageLoading: false,
            isOrder: true
        }
    },
    async mounted () {
        await this.initData();
    },
    methods: {
        async initData () {
            console.log('isTx', this.isTx)
            console.log('orderNoInner', this.orderNoInner)
            this.pageLoading = true;
            if (this.orderNo) {
                //如果是淘系，无jst接口可用
                if(this.isTx){
                    let rsp=await GetOrderActionsByTxOrderNo({orderNo:this.orderNo});
                    if(rsp?.success){
                        rsp.data.sort((a, b) => {
                            return new Date(a.created) - new Date(b.created);
                        });
                        this.orderNoInners = rsp.data;
                        if (this.orderNoInners.length > 0) {
                            this.activeName = this.orderNoInners[0].o_id + '';
                            this.orderLogList = this.orderNoInners[0].orderLogByInnerNoList;
                        }
                    }
                } else {

                    let orderNos = [];
                    orderNos.push(this.orderNo);
                    const res = await jstGetOrderActionsByInnerNo(orderNos);
                    if (!res?.success) {
                        return
                    }
                    res.data.sort((a, b) => {
                        return new Date(a.created) - new Date(b.created);
                    });
                    this.orderNoInners = res.data;
                    if (this.orderNoInners.length > 0) {
                        this.activeName = this.orderNoInners[0].o_id + '';
                        this.orderLogList = this.orderNoInners[0].orderLogByInnerNoList;
                    }
                }
            }
            else {
                let orderNos = [];
                orderNos.push(this.orderNoInner);
                const res = await jstGetOrderActionsByInnerNos(orderNos);
                if (!res?.success) {
                    return
                }
                this.activeName = '';
                res.data.sort((a, b) => {
                    return new Date(a.created) - new Date(b.created);
                });
                this.orderLogList = res.data;
            }
            this.orderByList();
            this.pageLoading = false;
        },
        async handleClick (tab, event) {
            this.orderLogList = this.orderNoInners[tab.index].orderLogByInnerNoList;
        },
        orderByList () {
            this.isOrder = !this.isOrder;
            //进行数组排序
            if (this.isOrder) {
                this.sortKeyAsc(this.orderLogList, "created");
            } else {
                this.sortKeyDesc(this.orderLogList, "created");
            }
        },
        //正序
        sortKeyAsc (array, key) {
            return array.sort(function (a, b) {
                var x = a[key];
                var y = b[key];
                return ((x < y) ? -1 : (x > y) ? 1 : 0)
            })
        },
        //倒序
        sortKeyDesc (array, key) {
            return array.sort(function (a, b) {
                var x = a[key];
                var y = b[key];
                return ((x > y) ? -1 : (x < y) ? 1 : 0)
            })
        }

    },
}
</script>

<style scoped></style>
