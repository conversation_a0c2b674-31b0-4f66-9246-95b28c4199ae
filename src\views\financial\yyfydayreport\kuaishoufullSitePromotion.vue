<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->

    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :tablekey='tablekey'
      :tableData='adcostlist' @select='selectchange' :isSelection='false' :showsummary='true' :tablefixed='true'
      :summaryarry='summaryarry' :tableCols='tableCols' :loading="listLoading">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <template slot='extentbtn'>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="Filter.ShopName" placeholder="店铺" style="width:170px;" clearable />
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="Filter.productId" placeholder="商品ID" style="width:170px;" clearable />
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="Filter.advertiserId" placeholder="推广账号" style="width:170px;" clearable />
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-select v-model="Filter.tuiGuangType" placeholder="推广类型" clearable filterable style="width: 170px;">
              <el-option key="直播推广" label="直播推广" :value="1" />
              <el-option key="商品推广" label="商品推广" :value="2" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-date-picker style="width:280px" v-model="Filter.UseDate" type="daterange" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期"></el-date-picker>
          </el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onImportSyj">导入</el-button>
        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getKuaiShouAdCostList" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisibleSyj" width="30%">
      <el-form ref="improtGroupForm" :model="improtGroupForm" label-width="80px" label-position="left">
        <el-row>
          <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
            <el-form-item label="推广类型" prop="tuiGuangType"
              :rules="[{ required: true, message: '请选择推广类型', trigger: 'blur' }]">
              <el-select v-model="improtGroupForm.tuiGuangType" placeholder="请选择" clearable filterable
                style="width: 100%;">
                <el-option key="直播推广" label="直播推广" :value="1" />
                <el-option key="商品推广" label="商品推广" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
              accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2" :file-list="fileList"
              :on-change="onUploadChange">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2"
                v-loading="improtGroupForm.loading">上传
              </el-button>
            </el-upload>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
      <span>
        <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>

import { importZhiBoTuiGuangDay_KuaiShouAsync, getZhiBoTuiGuangDay_KuaiShouPageList, deleteZhiBoTuiGuangDay_KuaiShouBatchAsync } from '@/api/financial/yyfyday'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar'
const tableCols = [
  { istrue: true, prop: 'useDate', label: '时间', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.useDate, 'YYYY-MM-DD') },
  { istrue: true, prop: 'shopName', label: '店铺', width: '170', sortable: 'custom' },
  { istrue: true, prop: 'advertiserId', label: '推广账号', width: '170', sortable: 'custom' },
  { istrue: true, prop: 'productId', label: '商品ID', width: '170', sortable: 'custom' },
  { istrue: true, prop: 'productName', label: '商品名称', width: '440', sortable: 'custom' },
  { istrue: true, prop: 'productPic', label: '商品主图', width: '130', sortable: 'custom', type: 'images' },
  { istrue: true, prop: 'useMoney', label: '花费(元)', width: '170', sortable: 'custom' },
  // { istrue: true, prop: 'createdTime', label: '时间', width: '170', sortable: 'custom' },
  { istrue: true, prop: 'tuiGuangType', label: '推广类型', width: '170', sortable: 'custom', formatter: (row) => { return row.tuiGuangType == 1 ? '直播推广' : row.tuiGuangType == 2 ? '商品推广' : '' } },
  // { istrue: true, prop: 'batchNumber', label: '导入批次', width: '170', sortable: 'custom' },
  { istrue: true, type: "button", width: "110", label: '操作', btnList: [{ label: "删除批次", handle: (that, row) => that.deleteBatch(row) }] }
];
const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
export default {
  name: "kuaishoufullSitePromotion",
  props: {
    tablekey: { type: String, default: '' }
  },
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, buschar },
  data() {
    return {
      that: this,
      Filter: {
        UseDate: [startDate, endDate],
      },
      shopList: [],
      userList: [],
      groupList: [],
      adcostlist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: {},
      pager: { OrderBy: "useDate", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
      platform: 0,
      buscharDialog: {
        visible: false,
        data: [],
        title: ''
      },
      improtGroupForm: {
        tuiGuangType: null,
        loading: false,
      }
    };
  },
  async mounted() {
    this.onRefresh()
  },
  methods: {
    async deleteBatch(row) {
      var that = this;
      this.$confirm("此操作将删除此批次导入快手广告费数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          await deleteZhiBoTuiGuangDay_KuaiShouBatchAsync({ batchNumber: row.batchNumber })
          that.$message({ message: '已删除', type: "success" });
          that.onRefresh()
        });
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onImportSyj() {
      this.dialogVisibleSyj = true
      this.improtGroupForm.tuiGuangType = ''
      this.fileList = []
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("tuiGuangType", this.improtGroupForm.tuiGuangType);
      this.improtGroupForm.loading = true;
      const res = await importZhiBoTuiGuangDay_KuaiShouAsync(form);
      this.improtGroupForm.loading = false;
      if (res?.success) {
        this.$message({ message: '上传成功,正在导入中...', type: "success" });
        this.dialogVisibleSyj = false;
      }
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    async uploadSuccess2(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
    },
    async onSubmitupload2() {
      if (!this.improtGroupForm.tuiGuangType) {
        this.$message({ message: '请选择推广类型', type: "error" });
        return;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: '请选择文件', type: "error" });
        return;
      }
      this.$refs.upload2.submit()
    },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getKuaiShouAdCostList();
    },
    async getKuaiShouAdCostList() {
      this.Filter.startUseDate = null;
      this.Filter.endUseDate = null;
      if (this.Filter.UseDate) {
        this.Filter.startUseDate = this.Filter.UseDate[0];
        this.Filter.endUseDate = this.Filter.UseDate[1];
      }
      const para = { ...this.Filter };
      let pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      this.listLoading = true;
      const res = await getZhiBoTuiGuangDay_KuaiShouPageList(params);
      if (!res.success) {
        this.listLoading = false;
        this.$message.error(res.message || "获取失败");
        return;
      }
      this.listLoading = false;
      this.total = res.data.total
      this.adcostlist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
