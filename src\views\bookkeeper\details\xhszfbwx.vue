<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>

                <el-form-item label="">
                    <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM"
                        value-format="yyyyMM" placeholder="年月">
                    </el-date-picker>
                </el-form-item>

                <el-form-item label="" label-position="right">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺" style="width: 180px">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.tradeType" placeholder="交易类型" style="width:120px;" maxlength="20" />
                </el-form-item>

                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.accountSourceType" placeholder="收款账户类型" style="width:120px;"
                        maxlength="20" />
                </el-form-item>

                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.orderNumber" placeholder="订单号" style="width:160px;" maxlength="20" />
                </el-form-item>

                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.remark" placeholder="备注" style="width:160px;" maxlength="100" />
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport">汇总导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='ZTCKeyWordList' :showsummary='true' :summaryarry='summaryarry' @select='selectchange'
            :isSelection='false' :tableCols='tableCols' :loading="listLoading" :isSelectColumn='false'>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList, getList as getshopList } from '@/api/operatemanage/base/shop';
import { formatPlatform, formatLink, platformlist } from "@/utils/tools";
import { GetXhsZfbWxPageList as getPageList, ExportXhsZfbWxList } from '@/api/monthbookkeeper/financialDetail'
const tableCols = [
    { istrue: true, prop: 'yearMonth', label: '年月', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'accountTime', label: '入账时间', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'orderNumber', label: '订单号', sortable: 'custom', width: '200' },
    { istrue: true, prop: 'tradeType', label: '交易类型', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'accountSourceType', label: '收款账户类型', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'amountIncome', label: '收入（元）', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'amountPaid', label: '支出（元）', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'remark', label: '备注', sortable: 'custom', },
];
export default {
    name: "xhsshopbalance",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
    data() {
        return {
            that: this,
            filter: {
                platform: 21,
                yearMonth: null,
                shopCode: null,
                proCode: null,
                isNullProCode: null,
                RecoganizeType: null
            },
            shopList: [],
            userList: [],
            groupList: [],
            platformlist: platformlist,
            ZTCKeyWordList: [],
            tableCols: tableCols,
            summaryarry: {},
            total: 0,
            pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
        };
    },
    async mounted() {
        this.onchangeplatform();
    },
    methods: {
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async onchangeplatform() {
            const res1 = await getshopList({ platform: 21, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
        },
        onRefresh() {
            this.onSearch()
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getList();
        },
        async getList() {
            let params = this.getCondition();
            this.listLoading = true;
            const res = await getPageList(params);
            this.listLoading = false;
            this.total = res.data?.total
            this.ZTCKeyWordList = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        getCondition() {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter,
            };
            return params;
        },
        async onExport(opt) {
            if (!this.filter.yearMonth) {
                this.$message({ message: "请先选择月份", type: "warning" });
                return;
            }
            let pars = this.getCondition();
            if (pars === false) {
                return;
            }
            const params = { ...pars, ...opt };
            let res = await ExportXhsZfbWxList(params);
            if (!res?.data) {
                return
            }
            this.$message({ message: "正在后台导出中，请点击头像-在下载管理中查看", type: "success" });
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
