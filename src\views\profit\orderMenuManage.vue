<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding:0;margin:0;">
                    <el-date-picker v-model="filter.time" type="date" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" placeholder="选择日期"
                        style="width:150px;" :picker-options="pickerOptions" :clearable="false">
                    </el-date-picker>

                    <el-select v-model="filter.zoneName" placeholder="地区搜索" filterable style="width: 160px">
                        <el-option :label="item" :value="item" v-for="(item,index) in quyuList" :key="index" />
                    </el-select>

                    <el-select v-model="filter.gysName" placeholder="供应商类型"  filterable style="width: 160px">
                        <el-option :label="item" :value="item" v-for="(item,index) in gysList" :key="index" />
                    </el-select>

                    <el-select v-model="filter.menuType" placeholder="餐别" :clearable="true"  filterable style="width: 160px">
                        <el-option label="中餐" value="中餐"  />
                        <el-option label="晚餐" value="晚餐"  />
                    </el-select>

                    
                    <!-- <el-input v-model.trim="filter.zoneName" placeholder="地区搜索" style="width: 160px" :maxlength="50" clearable></el-input> -->



                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="exportCurrDataEvent4">导出订单</el-button>
            </el-button-group>


        </template>


        <yhVxetable :id="'orderMenuManage202408041858'" ref="tabletwowee" :resizable="true" :somerow="'zoneName,orderMenuDate'" :xgt="-1" :ygt="-1" :that='that' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
        :tableData='tableData' :tableCols='tableCols' :border="true" :cellClassName="cellClassName" 
        :isSelectColumn="false" :loading="listLoading">
        </yhVxetable>

        <div id="YunHanAdminGoods20231221" style="width: 100%; height: 100%" v-show="daochushow">
            <yhVxetable :resizable="false" :somerow="'zoneName,orderMenuDate'" :xgt="-1" :ygt="-1" :that='that' :hasSeq='false' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
            :tableData='tableData' :tableCols='tableCols' :border="true" :cellClassName="cellClassName"
            :isSelectColumn="false" :loading="listLoading">
            </yhVxetable>
        </div>
        


    </container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import container from '@/components/my-container';
import yhVxetable from '@/components/VxeTable/yh_vxetable.vue';
import exportExecl from "@/utils/exportExecl.js"

import { formatLinkProCode, platformlist } from '@/utils/tools'
import { getOrderFoodMenuList, exportOrderMenuManageAsync,exportOrderMenuGroupAsync,exportOrderMenuAsync, getOrderFoodMenuProvier, getAreaSetList } from '@/api/profit/orderfood';

const tableCols = [
    { istrue: true, prop: 'id', label: '订单编号', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'createdUserName', label: '点餐人', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'createdGroupName', label: '对应组', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'createdTime', label: '点餐时间', width: '160', sortable: 'custom', },
    { istrue: true, prop: 'gysName', label: '供应商名称', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'menuLabel', label: '标签', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'menuNames', label: '菜名', width: '200', sortable: 'custom' },
    {istrue:true,summaryEvent: true,prop:'',label:'退款', merge:true,permission:"cgcoltxpddprsi",
    cols:[
        {istrue:true,summaryEvent:true,prop:'cancelOrderCount',label:'发货前退款单量',sortable:'custom', width:'80',type:'custom'},
        {istrue:true,summaryEvent:true,prop:'refundAmontBefore',label:'发货前退款',sortable:'custom', width:'80',type:'custom'},
    ]},

    {
        istrue: true, prop: 'enabled', label: '状态', width: '150', sortable: 'custom',
        formatter: (row) => { return row.enabled ? "正常点餐" : "取消点餐" }
    },
];

const startDate = formatTime(dayjs(), "YYYY-MM-DD");
const endDate = startDate;
//const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
    name: "Users",
    components: { container, yhVxetable },
    data () {
        return {
            that: this,
            daochushow: false,
            gysList: [],
            pickerOptions: {  
                disabledDate(time) {  
                    const currentDate = new Date();  
                    // 设置可多选一天
                    const tomorrow = new Date(currentDate);  
                    tomorrow.setDate(currentDate.getDate() + 1);  
                    return time.getTime() > tomorrow.getTime(); 
                }  
            },
            filter: {
                time: new Date(),
                gysName: '',
                zoneName: ''
            },
            platformlist: platformlist,
            tableCols: [],
            tableCols1: [],

            tableHandles: null,
            // tableData: [],
            // total: 0,
            quyuList: [],
            pager: { OrderBy: "id", IsAsc: false },
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            sels: [],
            allAlign: 'center',
            tableData: [
            ],
            tableData1: [
            ],
            mergeCells: [
            { row: 1, col: 1, rowspan: 3, colspan: 3 },
            { row: 5, col: 0, rowspan: 2, colspan: 2 }
            ]
        };
    },
    mounted () {
        this.getgyss();
        
        this.onSearch();


        
    },
    methods: {
        cellClassName ({ row, column }) {
            return null
        },
        exportCurrDataEvent4 () {
            let _this = this;
            this.daochushow = true;
            exportExecl("YunHanAdminGoods20231221", '每日订单统计——'+_this.filter.zoneName + endDate+'.xlsx');
            setTimeout(() => {
                this.daochushow = false;  
            }, 500);
        },
        async onSearch () {
            await this.getquyu();
            await this.getgys();
            
        },
        async getgys(){
            const res = await getOrderFoodMenuProvier();
            if(!res.success){
                return
            }
            this.gysList = res.data;
        },
        async getgyss(){
            const res = await getOrderFoodMenuProvier();
            if(!res.success){
                return
            }
            this.gysList = res.data;
            this.filter.gysName = this.gysList[0];
        },
        async getquyu(){
            const res = await getAreaSetList({getLevel: 1});
            if(!res.success){
                return
            }
            this.quyuList = res.data;
            if(!this.filter.zoneName){
                this.filter.zoneName = this.quyuList[0];
            }

            this.$nextTick(async()=>{
                await this.getList();
            })
        },
        
        async exportOrderMenuGroup () {
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange;
                this.filter.endTime = this.filter.timerange;
            }
            const params = { ...this.filter };
            const res = await exportOrderMenuAsync(params);
            if (!res?.data) return;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '菜单数量合计_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
        async exportOrderMenuManage () {
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange;
                this.filter.endTime = this.filter.timerange;
            }
            const params = { ...this.filter };
            const res = await exportOrderMenuManageAsync(params);
            if (!res?.data) return;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '点餐订单数据_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
        async sortchange (column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            // this.$refs.pager.setPage(1);
            await this.onSearch();
        },
        subtractOneDay(dateString) {  //减去一天

            const date = new Date(dateString);  
         
            date.setDate(date.getDate() - 1);  

            const newDateString = date.toISOString().split('T')[0];  
        
            return newDateString;  
        },
        async getList () {
 
            var that = this;
            this.listLoading = true;
            // const params = {...this.filter, };
            const params = {
                startTime: this.subtractOneDay(that.filter.time),
                gysName: this.filter.gysName,
                zoneName: this.filter.zoneName,
            }
            const res = await getOrderFoodMenuList(params).then(res => {


                let onearr = [
                    { istrue: true, prop: 'zoneName', label: '地区',  width: '120', },
                    { istrue: true, prop: 'orderMenuDate', label: '时间',    width: '100' },
                    { istrue: true, prop: 'menuLabel', label: '餐别',    width: '100' },
                    { istrue: true, prop: 'gysName', label: '供应商',    width: '100' },
                    { istrue: true, prop: 'floorName', label: '楼层',     width: '100' },
                    { istrue: true, prop: 'groupName', label: '组',    width: '100' },
                   
                ]

                
                

                res.data.map((item)=>{
                    item.gysName = this.filter.gysName;
                    item.detialList = item.detialList.map((detailItem)=>{

                        var isexit = false;
                        onearr.map((itemm)=>{
                            if(itemm.prop == 'menuName' + item.detialList.indexOf(detailItem)){
                                isexit = true;
                            }
                        })
                        if(!isexit){
                            onearr.push({
                                istrue: true,
                                prop: 'menuName' + item.detialList.indexOf(detailItem),
                                label: detailItem.menuName,
                                width: '100',
                                type: 'custom',
                            });
                        }
                        

                        item['menuName' + item.detialList.indexOf(detailItem)] = detailItem.totalQunatity;

                        
                        return;
                    })
                })


                this.tableCols = onearr;
                this.tableData = res.data;


            });
            if(this.filter.menuType){
                this.tableData = this.tableData.filter(item=>item.menuLabel == this.filter.menuType);
            }

            this.listLoading = false;
        },
        removeDuplicatesByName(array, name) {  
            return array.filter((item, index) => {
                return array.findIndex(obj => obj[name] === item[name]) === index;  
            });  
        }  
    }
})
</script>
<style lang="scss" scoped>
::v-deep .el-table__fixed-footer-wrapper tbody td {
    color: blue;
}
// ::v-deep .vxe-table--body-wrapper{
//     overflow-x: hidden;
// }
.vxetable202212161323{
    border: none !important;
}
// ::v-deep .vxe-table--body-wrapper{
//     display: none;
// }
</style>

