<template>
  <MyContainer>
    <div class="config-container" v-loading="loading">
      <div class="config-block">
        <div class="config-header">
          <span class="config-title">配置</span>
        </div>
        <div class="config-subtitle">
          <span>抓取间隔(查询时间)</span>
          <el-button type="text" @click="onAddRow(1)">新增一行</el-button>
        </div>
        <el-table :data="catchSets" border class="config-table" show-overflow-tooltip height="200px">
          <el-table-column prop="catchCount" label="次数">
            <template #default="{ row, $index }">
              <div class="iptCss" style="display: flex;align-items: center;justify-content: center;">
                {{ row.catchCount }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="min" label="间隔上次查询时间">
            <template #default="{ row }">
              <div class="ipt-wrapper">
                <el-input-number v-model="row.min" :max="999999" :precision="0" :min="0" :controls="false" label="分钟"
                  style=" flex: 1;min-width: 0; " />
                <span class="unit-label">分钟</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button size="mini" type="text" style="color: red;"
                @click="onDeleteRow(scope.$index, scope.row, 1)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="config-block">
        <div class="config-header">
          <span class="config-title">理赔原因</span>
        </div>
        <div class="config-subtitle">
          <span></span>
          <el-button type="text" @click="onAddRow(2)">新增一行</el-button>
        </div>
        <el-table :data="reasonSets" border class="config-table" show-overflow-tooltip height="200px">
          <el-table-column prop="claimReason" label="原因">
            <template #default="{ row }">
              <el-input v-model.trim="row.claimReason" placeholder="原因" maxlength="50" clearable class="iptCss" />
            </template>
          </el-table-column>
          <el-table-column prop="key" label="关键字">
            <template #default="{ row }">
              <el-input v-model.trim="row.key" placeholder="关键字" maxlength="50" clearable class="iptCss" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button size="mini" type="text" style="color: red;"
                @click="onDeleteRow(scope.$index, scope.row, 2)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="config-footer">
        <el-button class="footer-btn" type="default" @click="onCancel">取消</el-button>
        <el-button class="footer-btn" type="primary" @click="onSubmit">提交</el-button>
      </div>
    </div>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { getExpressClaimReasonRelations, saveExpressClaimReasonRelations } from '@/api/customerservice/expressClaimOrder'
import dayjs from 'dayjs'
export default {
  name: "claimsConfiguration",
  components: {
    MyContainer
  },
  data() {
    return {
      catchSets: [],//抓取间隔
      reasonSets: [],//理赔原因
      that: this,
      loading: false,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    onCancel() {
      this.$emit('close')
    },
    onSubmit() {
      this.$confirm('确定提交吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { data, success } = await saveExpressClaimReasonRelations({
          catchSets: this.catchSets,
          reasonSets: this.reasonSets
        })
        if (success) {
          this.$message.success('提交成功')
          this.$emit('successClose')
        }
      })
    },
    onDeleteRow(index, row, type) {
      if (type === 1) {
        this.catchSets.splice(index, 1)
      } else {
        this.reasonSets.splice(index, 1)
      }
    },
    onAddRow(type) {
      if (type === 1) {
        const lastRow = this.catchSets[this.catchSets.length - 1];
        const catchCount = lastRow?.catchCount ? lastRow.catchCount + 1 : 1;
        this.catchSets.push({
          catchCount,
          min: 0
        });
      } else {
        this.reasonSets.push({
          claimReason: '',
          key: ''
        })
      }
    },
    async getList() {
      this.loading = true
      const { data, success } = await getExpressClaimReasonRelations()
      this.loading = false
      if (success) {
        this.catchSets = data?.catchSets?.length ? data.catchSets : [];
        this.reasonSets = data?.reasonSets?.length ? data.reasonSets : [];
      } else {
        this.$message.error('获取列表失败')
      }
    },
  }
}
</script>

<style scoped lang="scss">
.config-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}

.config-block {
  background-color: #f9f9f9;
  padding: 12px;
  border: 1px solid #eee;
  border-radius: 8px;
}

.config-header {
  margin-bottom: 8px;
}

.config-title {
  font-size: 16px;
  font-weight: bold;
}

.config-subtitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.config-table {
  width: 100%;
}

.config-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
}

.footer-btn {
  flex: 1;
  max-width: 100px;
}

.iptCss {
  width: 90%;
}

.ipt-wrapper {
  display: flex;
  align-items: center;
  gap: 6px;
  width: 100%;
}

.unit-label {
  white-space: nowrap;
  font-size: 14px;
}
</style>
