<template>
    <container v-loading="pageLoading">
      <template #header>
       <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="月份">
            <el-date-picker v-model="filter.nDateTime" type="month" format="yyyy-MM" value-format="yyyy-MM" placeholder="选择月份"></el-date-picker>            
        </el-form-item>
        <el-form-item>
        <el-button type="primary" @click="onExportDetail">导出</el-button>
        <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
       </el-form>
      </template>  
        <div style="overflow-x: auto; height:100%">
            <el-table :data="abnormalModel.cgItems" v-loading="listLoading" height="100%" style="width: 100%" :header-cell-style="{ 'background-color': '#f5f7fa', 'color': '#909399', 'font-weight': 'bold' }">
              <!-- <el-table-column prop="firstTime" label="最早时间" width="180"></el-table-column> -->
              <el-table-column prop="name" label="采购组" width="150">
                <template slot-scope="scope" >
                  <el-link type="primary" @click="openanalymonthsis(scope.row['groupOrBrandId'])">{{scope.row['name']}}</el-link>
                </template>
              </el-table-column>
              <el-table-column prop="monthNow" label="当前月份" width="100"></el-table-column>
              <el-table-column prop="goodsCodeCount" label="负责编码数" width="100"></el-table-column>
              <el-table-column prop="totalWaitOrderNum" label="总压单量" width="80"></el-table-column>
              <el-table-column prop="sjWaitOrderNum" label="实际压单量" width="90"></el-table-column>
              <el-table-column prop="scWaitOrderNum" label="审错压单量" width="90"></el-table-column>
              <el-table-column prop="preSaleOrderNum" label="预售压单量" width="90"></el-table-column>
              <el-table-column prop="taoKeOrderNum" label="淘客压单量" width="90"></el-table-column>
              <el-table-column prop="totalWaitGoodNum" label="压品数" width="80"></el-table-column>
              <el-table-column prop="nonCompletedPurchase" label="未完结采购单" width="100"></el-table-column>
              <el-table-column prop="endAmont" label="库存资金" width="100" v-if="checkPermission(['api:inventory:warehouse:fstockrat'])"></el-table-column>
              <!-- <el-table-column prop="nonInAmont" label="未入库总金额" width="100"></el-table-column> -->
              <el-table-column prop="purchaseCount" label="当月采购单数" width="100" v-if="checkPermission(['api:inventory:warehouse:fstockrat'])"></el-table-column>
              <el-table-column prop="purchaseGoodsCodeCount" label="当月采购编码数" width="120" v-if="checkPermission(['api:inventory:warehouse:fstockrat'])"></el-table-column>
              <el-table-column prop="fStockCount" label="当月负库存数" width="110" v-if="checkPermission(['api:inventory:warehouse:fstockrat'])"></el-table-column>
              <el-table-column prop="fStockRate" label="当月负库存率" width="100" v-if="checkPermission(['api:inventory:warehouse:fstockrat'])">
                <template slot-scope="scope" >
                  <el-link type="primary" @click="openFstockrate(scope.row['groupOrBrandId'])">{{scope.row['fStockRate']}}%</el-link>
                </template>
              </el-table-column>
            </el-table>
          </div>
     <el-dialog :visible.sync="dialogVisible" v-dialogDrag :show-close="false">
          <abnormalgeneral :filter="filter"  style="height: 500px"></abnormalgeneral> 
        </el-dialog>

         <el-dialog :visible.sync="dialogFstockrateVisible" v-dialogDrag :show-close="false">
           <fstockrate ref="fstockrate" style="height: 450px"></fstockrate> 
        </el-dialog>
        
        <el-dialog :visible.sync="dialoganalysisVisible" width="70%" v-dialogDrag :show-close="false">
           <abnormalgeneralanalmonthysis ref="abnormalgeneralanalmonthysis" style="height: 500px"></abnormalgeneralanalmonthysis> 
        </el-dialog>
  </container>
</template>

<script>
import {pageAbnormalMonthGeneral, exportAbnormalGeneralMonth} from '@/api/inventory/abnormal'
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import abnormalgeneral from '@/views/inventory/components/abnormalgeneral'
import fstockrate from '@/views/inventory/components/fstockrate'
import abnormalgeneralanalmonthysis from '@/views/inventory/components/abnormalgeneralanalmonthysis'
import { formatTime,formatSecondToHour,formatmoney} from "@/utils/tools";
export default {
    name: 'YunhanAdminMoonindex',
    components: { container, MyConfirmButton, abnormalgeneral,fstockrate,abnormalgeneralanalmonthysis },
    data() {
        return {
            that:this,
            abnormalModel:{ monthNow:'',cGItems:[],yYItems:[] },
            listLoading:true,
            pageLoading:false,
            dialogVisible:false,
            dialogFstockrateVisible:false,
            dialoganalysisVisible:false,
            filter:{
                nDateTime:null,              
            }
        };
    },
    async mounted() {      
       
    },
    methods: {
      async onSearch(){
        this.getlist()
      },
      async getlist(){  
        let date = new Date()
        let year = date.getFullYear().toString()   //'2022'
        let month = date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1).toString():(date.getMonth()+1).toString()  //'04'
        let beg = year + '-' + month + '-01'
        if(this.filter.nDateTime==null)
        {
            this.filter.nDateTime= beg
        }
          const params={...this.filter}
          this.listLoading = true
          const res= await pageAbnormalMonthGeneral(params)
          this.listLoading = false
          if (!res?.success) return           
          this.abnormalModel=res.data;         
      },
      async openGeneral(){
      this.dialogVisible=true;
      this.filter={};
    },
    async openFstockrate(brandid){
      this.dialogFstockrateVisible=true;
      this.$nextTick(() => {
          this.$refs.fstockrate.onSearch(brandid);
      });
    },
    async openanalymonthsis(brandid){
      this.dialoganalysisVisible=true;
      this.$nextTick(() => {
          this.$refs.abnormalgeneralanalmonthysis.onSearch(brandid);
      });
    },
    //导出
    async onExportDetail(){
      let date = new Date()
        let year = date.getFullYear().toString()   //'2022'
        let month = date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1).toString():(date.getMonth()+1).toString()  //'04'
        let beg = year + '-' + month + '-01'
        if(this.filter.nDateTime==null)
        {
            this.filter.nDateTime= beg
        }
        const params={...this.filter}
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportAbnormalGeneralMonth(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','月汇总详情_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },
    },
};
</script>

<style lang="scss" scoped>
.ad-form-query{
    text-align: left;
}
</style>>
