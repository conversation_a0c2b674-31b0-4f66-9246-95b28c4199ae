<template>
    <el-container style="height:100%;">
        <my-container v-loading="pageLoading" style="width:100%;">
          <template #header>
          </template>
          <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="采购个人周转天数" name="purchaseIndividualTurnoverDays" style="height:100%;">
                <purchaseIndividualTurnoverDays :filter="filter" ref="purchaseIndividualTurnoverDays" :isHistory="true"></purchaseIndividualTurnoverDays>
            </el-tab-pane>
            <el-tab-pane label="货盘资金合计" v-if="checkPermission('viewTotalPalletFunds')" name="purchaseFundDtl" style="height:100%;">
                <purchaseFundDtl :filter="filter" ref="purchaseFundDtl"></purchaseFundDtl>
            </el-tab-pane>
            <el-tab-pane label="滞销明细" v-if="checkPermission('viewUnsalableDtl')" name="unsalableDtl" style="height:100%;">
                <unsalableDtl :filter="filter" ref="unsalableDtl"></unsalableDtl>
            </el-tab-pane>
            <el-tab-pane label="商品动销明细" name="productSalesDetails" style="height:100%;">
                <productSalesDetails :filter="filter" ref="refproductSalesDetails"></productSalesDetails>
            </el-tab-pane>
          </el-tabs>
        </my-container>
    </el-container>
</template>
<script>
import MyContainer from '@/components/my-container'
import purchaseIndividualTurnoverDays from "@/views/inventory/purchaseIndividualTurnoverDays";
import purchaseFundDtl from "@/views/inventory/PurchaseNewPlanTurnDay/PurchaseFundDtl";
import unsalableDtl from "@/views/inventory/PurchaseNewPlanTurnDay/unsalableDtl";
import productSalesDetails from "@/views/inventory/PurchaseNewPlanTurnDay/productSalesDetails";

export default {
  name: 'Roles',
  components: { MyContainer, purchaseIndividualTurnoverDays, purchaseFundDtl,unsalableDtl,productSalesDetails},
  data() {
    return {
      activeName:"purchaseIndividualTurnoverDays",
      that:this,
      filter: {
      },
      pageLoading: false,
    }
  },
  async mounted() {
  },
  methods: {
    //总查询
    async Query(){
      switch(this.activeName){
        case "purchaseIndividualTurnoverDays":
          this.$refs.purchaseIndividualTurnoverDays.onSearch();
          break;
      }
    },
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary{
  margin-right: 7px;
}
</style>
