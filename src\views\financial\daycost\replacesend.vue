<template>
  <container>
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'
      :tableCols='tableCols' :tableHandles='tableHandles' :isSelection="true" @select="selectchange" :showsummary='true' :summaryarry='summaryarry'
      :loading="listLoading" :isselectable="true" @isselectablefuc="isselectablefuc">
      <template slot='extentbtn'>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;">
            <el-input style="width: 100px" v-model="filter1.orderNoInner"
              placeholder="内部订单号" maxlength="40" clearable />
          </el-button>
          <el-button style="padding: 0;margin: 0;"><el-input style="width: 100px" v-model="filter1.proCode"
              placeholder="产品ID" maxlength="40" clearable/></el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-select filterable v-model="filter1.platform" placeholder="平台" clearable style="width: 80px">
              <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;"><el-input style="width: 80px" v-model="filter1.importor" clearable
              placeholder="导入人" /></el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-select filterable v-model="filter1.syncJstStatus" placeholder="状态" clearable style="width: 80px">
              <el-option label="成功" value="成功" />
              <el-option label="失败" value="失败" />
              <el-option label="同步中" value="同步中" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-date-picker style="width: 210px" v-model="filter1.timeimportrange" type="datetimerange"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始导入时间"
              end-placeholder="结束导入时间" :clearable="true" class="date_picker"></el-date-picker>
          </el-button>
          <el-button type="primary" @click="onSearch">刷新</el-button>
        </el-button-group>
      </template>
      <template>
        <el-table-column width="100" label="操作" fixed="right">
                <template slot-scope="scope">
                    <el-button v-if="scope.row.syncJstStatus == '失败'" type="text" @click="send(scope.row)">同步</el-button>
                    <el-button type="text" @click="showLog(scope.row)">日志</el-button> 
                </template>
            </el-table-column>
      </template>
    </ces-table>

    <el-dialog title="日志" :visible.sync="showlogDetail.visible" width="40%" v-dialogDrag>
      <div style="height: 500px;">
        <ces-table ref="logTable" :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="false"  
          :showsummary='true' :tablefixed='true' :tableData='logTableData' 
          :tableCols='logTableCols' style="width:100%;height:98%;margin: 0">
        </ces-table>
      </div>
    </el-dialog>

    <el-dialog title="更换快递公司" :visible.sync="changeCompany.visible" width="20%" v-dialogDrag>
      <div style="height: 100px; margin-top: 10px;">
        <span>新快递公司：</span>
        <el-select filterable clearable v-model="changeCompany.params.expressCode" placeholder="快递公司" style="width: 200px;margin-left: 5px;">
              <el-option v-for="item in changeCompany.expressCompanys" :key="item.code" :label="item.name" :value="item.code" >
              </el-option>
        </el-select> 
      </div>
      <span class="dialog-footer" style="float: right;margin-top :-20px;">
        <el-button type="primary" v-throttle="1000" @click="saveExpressCompanys">保 存</el-button>
        <el-button @click="changeCompany.visible=false">取 消</el-button>
      </span>
    </el-dialog>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>
  </container>
</template>
<script>
import { pageReplaceSendNew, exportReplaceSendNew, sendNewReplaceSend, batchSendNewReplaceSend, getReplaceSendNewLogs,getExpressCompany,saveChangeExpressCompany } from '@/api/financial/replacesend'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import { platformlist } from '@/utils/tools'
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { formatFeeShareOper, formatTime, formatYesornoBool, companylist } from "@/utils/tools";
const tableCols = [
  { istrue: true, prop: 'payTime', label: '付款日期', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.payTime, 'YYYY-MM-DD') },
  { istrue: true, prop: 'proCode', label: '产品ID', width: '110', sortable: 'custom' },
  { istrue: true, prop: 'shopName', label: '店铺名称', width: '110' },
  { istrue: true, prop: 'orderNoInner', label: '内部订单号', width: '100', sortable: 'custom', },
  { istrue: true, prop: 'platform', label: '平台', width: '65', sortable: 'custom', formatter: (row) => row.enmPlatform },
  { istrue: true, prop: 'onLineOrderNum', label: '线上单号', width: '110', sortable: 'custom' },
  { istrue: true, prop: 'payAmount', label: '买家支付金额', width: '110', sortable: 'custom', },
  { istrue: true, prop: 'replaceCost', label: '代拍成本', width: '80', sortable: 'custom', },
  { istrue: true, prop: 'cost', label: '系统成本', width: '80', sortable: 'custom', },
  { istrue: true, prop: 'profit', tipmesg: '买家支付金额-代拍成本', label: '代拍利润',  width: '100', sortable: 'custom', },
  { istrue: true, prop: 'amounted', tipmesg: '代拍成本-系统成本', label: '代发成本差', width: '100', sortable: 'custom', },
  { istrue: true, prop: 'createdUserId', label: '导入人', width: '70', sortable: 'custom', formatter: (row) => row.createdUserName },
  { istrue: true, prop: 'createdTime', label: '导入时间', width: 'auto', sortable: 'custom', formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD HH:mm:ss') },
  { istrue: true, prop: 'expressNo', label: '快递单号', width: 'auto', sortable: 'custom' },
  { istrue: true, prop: 'expressCompanyName', label: '快递公司', width: 'auto', sortable: 'custom', type:'click',formatter: (row) => row.expressCompanyName == null? "选择":row.expressCompanyName ,handle: (that, row) => that.showChangeCompany(row) },
  { istrue: true, prop: 'expressCode', label: '快递编码', width: '110', sortable: 'custom' },
  { istrue: true, prop: 'syncJstStatus', label: '同步聚水潭状态', width: 'auto', sortable: 'custom' }
];
const tableHandles = [
  { label: "全量导入", handle: (that) => that.onimport(null) },
  { label: "下载全量导入模板", handle: (that) => that.ondownloadmb('代发成本差全量导入模板') },
  { label: "精简导入", handle: (that) => that.onimport(9) },
  { label: "下载精简导入模板", handle: (that) => that.ondownloadmb('代发成本差精简导入模板') },
  { label: "导出", handle: (that) => that.onExport() },
  // { label: "批量同步", handle: (that) => that.batchSend() },
];

const logTableCols = [
  { istrue: true, prop: 'createdUserName', label: '操作人', width: '120'},
  { istrue: true, prop: 'createdTime', label: '操作时间', width: '160' ,formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD HH:mm:ss')},
  { istrue: true, prop: 'isSuccess', label: '操作结果', width: '100', formatter: (row) =>{return row.isSuccess?"成功":"失败"}},
  { istrue: true, prop: 'errorMessage', label: '失败原因' }
];

export default {
  name: 'YunHanAdminReplacesend',
  components: { cesTable, container },
  props: {
    filter: {}
  },
  data() {
    return {
      filter1: {
        proCode: null,
        platform: null,
        fKId: null,
        groupId: null,
        shopId: null,
        type: null,
        shareOper: null,
        importor: null,
        startImportTime: null,
        endImportTime: null,
        timeimportrange: null,
        syncJstStatus:null,
        orderNoInner:null
      },
      shareFeeType: 6,
      companylist: companylist,
      that: this,
      list: [],
      grouplist: [],
      shoplist: [],
      tableCols: tableCols,
      tableHandles: tableHandles,
      pager: { OrderBy: "id", IsAsc: false },
      summaryarry: {},
      total: 0,
      sels: [],
      selids: [],
      platformlist: platformlist,
      listLoading: false,
      pageLoading: false,
      logTableCols:logTableCols,
      logTableData:[],
      showlogDetail:{
        visible:false
      },
      changeCompany:{
        visible:false,
        expressCompanys:[],
        params:{
          expressCode:null,
          orderNoInner:null
        }
      }
    }
  },
  async mounted() {
    await this.getExpressCompanys();
  },
  beforeUpdate() { },
  methods: {
    //获取店铺
    async onchangeplatform() {
      this.categorylist = []
      const res1 = await getshopList({ platform: this.filter.platform, CurrentPage: 1, PageSize: 100 });
      this.filter.shopCode = null
      this.shopList = res1.data.list
    },
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      } 
      this.filter1.startImportTime =null;
      this.filter1.endImportTime =null;
      if (this.filter1.timeimportrange) {
        this.filter1.startImportTime = this.filter1.timeimportrange[0];
        this.filter1.endImportTime = this.filter1.timeimportrange[1];
      }
      var pager = this.$refs.pager.getPager()
      this.filter.shareFeeType = this.shareFeeType;
      const params = { ...pager, ...this.pager, ... this.filter, ... this.filter1 }
      this.listLoading = true
      const res = await pageReplaceSendNew(params)
      this.listLoading = false
      if (!res?.success) return
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry = res.data.summary;
    },
    async onExport() {
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      this.filter1.startImportTime =null;
      this.filter1.endImportTime =null;
      if (this.filter1.timeimportrange) {
        this.filter1.startImportTime = this.filter1.timeimportrange[0];
        this.filter1.endImportTime = this.filter1.timeimportrange[1];
      }
      this.filter.shareFeeType = this.shareFeeType;
      let pager = this.$refs.pager.getPager()
      const params = { ...pager, ...this.pager,... this.filter, ... this.filter1 }
      this.listLoading = true
      var res = await exportReplaceSendNew(params);
      this.listLoading = false
      if (!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '代发成本差_' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    async onbatchDelete() {
      await this.$emit('ondeleteByBatch', this.shareFeeType);
    },
    async oncomput() {
      this.$emit('onstartcomput', this.shareFeeType);
    },
    async onimport(shareFeeType) {
      if (shareFeeType == null) {
        await this.$emit('onstartImport', this.shareFeeType);
      } else {
        await this.$emit('onstartImport', shareFeeType);
      }
    },
    async ondownloadmb(name) {
      await this.$emit('ondownloadmb', name);
    },
    async send(row) {
      let params = { OrderNoInner : row.orderNoInner };
      let res = await sendNewReplaceSend(params);
      if (res?.success) {
        this.$message.success('同步聚水潭成功！');
        await this.getlist();
      } else {
        this.$message.error('同步聚水潭失败！');
      }
    },
    async batchSend() {
      if (this.selids.length == 0) {
          this.$message({ type: 'warning', message: "请选择数据" });
          return;
      }
      let params = { orderNoInners :this.selids };
      let res = await batchSendNewReplaceSend(params);
      if (res?.success) {
        this.$message.success('同步聚水潭处理中，请稍后查看结果....');
        await this.getlist();
      } else {
        this.$message.error('同步聚水潭失败！');
      }
    },
    async showLog(row) {
      this.logTableData=[]; 
      let params = { 
        orderNoInner:row.orderNoInner 
      };
      let res = await getReplaceSendNewLogs(params);
      if (res?.success) {
        this.logTableData=res.data;
        this.showlogDetail.visible=true;
      }  
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.orderNoInner);
      })
    },
    isselectablefuc: function (data, callback) {
        callback((data.row.syncJstStatus == "失败") ? true : false);
    },
    async getExpressCompanys(){
      let res = await getExpressCompany();
      this.changeCompany.expressCompanys = res.data;
    }, 
    async showChangeCompany(row){
      this.changeCompany.params.expressCode=row.expressCode;
      this.changeCompany.params.orderNoInner=row.orderNoInner;
      this.changeCompany.visible=true;
    },
    async saveExpressCompanys(){
      if(this.changeCompany.params.expressCode == '' || this.changeCompany.params.expressCode==null){
        this.$message({ message: "请选择快递公司", type: "warning" });
        return;
      } 
      let res=await saveChangeExpressCompany(this.changeCompany.params);
      if(res?.success){
        this.$message.success('操作成功');
        this.changeCompany.visible=false;
        await this.getlist();
      }
    }
  }
}
</script>
  