<template>
  <my-container>
    <el-tabs v-model="activeName" style="height: 95%">
      <el-tab-pane label="统计" name="first1" style="height: 99%">
        <distributionStatistics ref="refdistributionStatistics" @changeTab="changeTab" />
      </el-tab-pane>
      <el-tab-pane label="昀晗代发" name="first2" style="height: 99%" lazy>
        <cargoOwnerOrders ref="refcargoOwnerOrders" />
      </el-tab-pane>
      <el-tab-pane label="昀晗销售" name="first3" style="height: 99%" lazy>
        <cargoOwnerSelfOrders ref="refcargoOwnerSelfOrders" />
      </el-tab-pane>
      <el-tab-pane label="库存" name="first4" style="height: 99%" lazy>
        <ShipperInventory ref="refcargoOwnerSelfOrders" />
      </el-tab-pane>
      <el-tab-pane label="计划采购建议" name="first5" style="height: 99%" lazy>
        <planProcurementProposals ref="refcargoOwnerSelfOrders" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import distributionStatistics from "./distributionStatistics.vue";
import cargoOwnerOrders from "./cargoOwnerOrders.vue";
import cargoOwnerSelfOrders from "./cargoOwnerSelfOrders.vue";
import ShipperInventory from "./ShipperInventory.vue";
import planProcurementProposals from "./planProcurementProposals.vue";
export default {
  name: "ownerDistributionIndex",
  components: {
    MyContainer,
    distributionStatistics,
    cargoOwnerOrders,
    cargoOwnerSelfOrders,
    ShipperInventory,
    planProcurementProposals
  },
  data() {
    return {
      activeName: "first1",
    };
  },
  methods: {
    changeTab(val) {
      this.activeName = val;
    },
  }
};
</script>

<style lang="scss" scoped></style>
