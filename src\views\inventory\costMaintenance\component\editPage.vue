<template>
  <div>
    <el-form label-position="right" label-width="120px" :model="formLabelAlign" :rules="addFormRules"
      ref="formLabelAlign">
      <el-form-item label="历史成本价:" prop="price">
        <el-input-number v-model="formLabelAlign.price" :min="0" :max="999999" class="publicCss" :controls="false"
          :precision="2" placeholder="请输入历史成本价"></el-input-number>
      </el-form-item>
      <el-form-item label="成本价:" prop="cost">
        <el-input-number v-model="formLabelAlign.cost" :min="0" :max="999999" class="publicCss" :controls="false"
          :precision="2" placeholder="请输入成本价"></el-input-number>
      </el-form-item>
    </el-form>
    <div
      style="display: flex;justify-content: center; width: 100%;  box-sizing: border-box;margin-top: 30px;gap: 20px;">
      <el-button type="" @click="closefuc">取消</el-button>
      <el-button type="primary" @click="savesubmit">确定</el-button>
    </div>
  </div>
</template>

<script>
import { ModifyDingApproveGoodsCostDiffs } from '@/api/cwManager/costMaintenanceManager'
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
export default {
  name: "editPage",
  components: {
    inputNumberYh
  },
  props: {
    peizhidata: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  data() {
    return {
      formLabelAlign: {
        price: null,
        cost: null,
      },
      addFormRules: {
        price: [{ required: true, message: '请输入历史成本价', trigger: 'blur' }],
        cost: [{ required: true, message: '请输入成本价', trigger: 'blur' }],
      },
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.formLabelAlign.clearValidate();
      this.$refs.formLabelAlign.resetFields();
      this.formLabelAlign = JSON.parse(JSON.stringify(this.peizhidata));
    });
  },
  methods: {
    closefuc() {
      this.$emit('closedialog');
    },
    async savesubmit() {
      //校验
      this.$refs.formLabelAlign.validate(async (valid, fail) => {
        if (valid) {
          //保存
          let params = {
            ...this.formLabelAlign
          }
          let res;
          res = await ModifyDingApproveGoodsCostDiffs(params);
          if (!res.success) {
            return;
          }
          this.$message.success("保存成功");
          this.$emit('successClosedialog');
        } else {
          return false;
        }
      })

    }
  }
}
</script>
<style scoped lang="scss">
.el-form-item {
  justify-content: space-between;
  box-sizing: border-box;
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep .el-input-number.is-without-controls .el-input__inner {
  padding-left: 6px;
}

.publicCss {
  width: 90%;
}
</style>
