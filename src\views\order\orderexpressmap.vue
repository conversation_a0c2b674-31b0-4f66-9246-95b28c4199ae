<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right" label-width="80px">
        <el-form-item label="结算月份:">
          <el-date-picker :picker-options="pickerOptions" style="width: 130px" v-model="filter.settMonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份" ></el-date-picker>
        </el-form-item>
        <el-form-item label="平台:" >
          <el-select v-model="filter.platform" placeholder="请选择" class="el-select-content" @change="onchangeplatform" style="width:130px;" filterable :clearable="true">
            <el-option 
              v-for="item in platformList"
              :key="item.value"
              :label="item.label"
              :value="item.value">             
            </el-option>
          </el-select>
        </el-form-item>    
        <el-form-item label="店铺:">
            <el-select
              v-model="filter.shopCode"
              placeholder="请选择" style="width: 130px"
              :clearable="true" :collapse-tags="true"  filterable>
              <el-option
                v-for="item in shopList"
                :key="item.shopCode"
                :label="item.shopName"
                :value="item.shopCode"/>
            </el-select>
        </el-form-item>
        <el-form-item label="快递公司:">
           <el-select v-model="filter.expressCompanyId" placeholder="请选择" style="width: 130px" filterable :clearable="true">
              <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id"/>
           </el-select>          
        </el-form-item>
        <el-form-item label="仓库:">
            <el-select
              v-model="filter.sendWarehouse"
              placeholder="请选择"
              style="width: 130px"
              :clearable="true" :collapse-tags="true"
            >
              <el-option
                v-for="item in sendWarehouseList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
        </el-form-item>
        <el-form-item label="揽收时间:">
            <el-date-picker style="width:210px;"
              v-model="filter.timerange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
        </el-form-item>
        <el-form-item label="是否匹配:">
           <el-select v-model="filter.isMap" placeholder="请选择" style="width: 130px" filterable :clearable="true">
              <el-option v-for="item in yesnoList" :key="item.key" :label="item.value" :value="item.key"/>
           </el-select>
        </el-form-item>     
        <el-form-item label="重复发货:">
           <el-select v-model="filter.isRepeat" placeholder="请选择" style="width: 130px" filterable :clearable="true">
              <el-option v-for="item in yesnoList" :key="item.key" :label="item.value" :value="item.key"/>
           </el-select>
        </el-form-item>
        <el-form-item label="异常类型:">
           <el-select v-model="filter.errorType" placeholder="请选择" style="width: 130px" filterable :clearable="true">
              <el-option v-for="item in errorTypeList" :key="item.key" :label="item.value" :value="item.key"/>
           </el-select>
        </el-form-item>
        <el-form-item label="数据源:">
           <el-select v-model="filter.sourceName" placeholder="请选择" style="width: 130px" filterable :clearable="true">
              <el-option v-for="item in sourceNameList" :key="item.key" :label="item.value" :value="item.key"/>
           </el-select>
        </el-form-item>
        <el-form-item label="备注:">
            <el-input v-model.trim="filter.remark" placeholder="备注" style="width: 130px"/>         
        </el-form-item>
        <el-form-item label="重量差:" >
            <el-input type="number" v-model="filter.diffWeightMin" placeholder="小" style="width: 65px"/>  
            <el-input type="number" v-model="filter.diffWeightMax" placeholder="大" style="width: 65px"/>          
        </el-form-item>        
        <el-form-item label="" style="margin-left:12px;">
           <el-select v-model="filter.num" :placeholder="filter.num" style="width: 100px;" :clearable="false" @change="changeNumType">
              <el-option v-for="item in numList" :key="item.id" :label="item.name" :value="item.id"/>
           </el-select>
           <el-input v-model.trim="filter.numValue" :placeholder="filter.num" style="width: 178px"/>  
        </el-form-item>
        <el-form-item label="批次号:">
            <el-input type="number" v-model="filter.batchNumber" placeholder="批次号" style="width: 130px"/>         
        </el-form-item>
      </el-form>
    </template>
 
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
      :tableData='list'    :tableCols='tableCols'
      :tableHandles='tableHandles'
      :loading="listLoading">
       <template slot='extentbtn'>
            <el-button-group >
              <el-input type="number" v-model.number="exportProcess.pageSize" placeholder="最大下载数量" title="最大下载数量"/>
            </el-button-group>
        </template>
    </ces-table>
    
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getlist"
      />
    </template>

    <el-dialog :visible.sync="calcDialog.dialogVisible" width="30%">
      <el-form class="ad-form-query" :inline="true" :model="calcDialog.filter" @submit.native.prevent>
        <el-form-item label="结算月份:">
          <el-date-picker :picker-options="pickerOptions" style="width: 130px" v-model="calcDialog.filter.settMonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份" ></el-date-picker>
        </el-form-item>

        <el-form-item label="揽收时间:">
            <el-date-picker style="width:210px;"
              v-model="calcDialog.filter.timerange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
        </el-form-item>

        <el-form-item label="快递公司:">
           <el-select v-model="calcDialog.filter.expressCompanyId" placeholder="请选择" style="width: 130px" filterable :clearable="true">
              <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id"/>
           </el-select>
        </el-form-item>
        <el-alert title="温馨提示：执行计算前，请确认已导入了聚水潭、千牛、拼多多的订单数据，以及快递费数据。如需要重新计算，请先删除已计算数据。" type="info" :closable="false" style="margin-bottom:8px;"/>
        <el-form-item>
            <el-button type="primary" @click="onCalc">计算</el-button>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="onDel">删除</el-button>
        </el-form-item>
      </el-form>
      <el-empty v-loading="calcDialog.loading" :element-loading-text="calcDialog.loadingText" description=" ">
      </el-empty>
    </el-dialog>

    <el-dialog width="0px" :visible.sync="exportProcess.visible" :show-close="false" :modal="false">
      <el-progress type="circle" :percentage="exportProcess.percentage" :status="exportProcess.status" 
      :indeterminate="true"
      :duration="5"/>
    </el-dialog>
  </my-container>
</template>

<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatPlatform,formatYesornoBool} from "@/utils/tools";
import { rulePlatform,ruleSendWarehouse} from "@/utils/formruletools";
import { 
  calcOrderExpressMap,
  pageOrderExpressMap,
  exportOrderExpressMap,
  getOrderExpressMapCount,
  delOrderExpressMap ,
  isCalcOrderExpressMapFinish,
  getOrderExpressMapSummary,
} from "@/api/order/orderexpressmap";
import {getExpressComanyAll} from "@/api/express/express";
const sourceNameList=[
        {key:1,value:'千牛'},
        {key:2,value:'拼多多'},
        {key:3,value:'聚水潭'},
      ];
const tableCols =[
        {istrue:true,prop:'settMonth',label:'结算年月', width:'80'},
        {istrue:true,prop:'batchNumber',label:'批次号', width:'180'},        
        {istrue:true,prop:'expressNo',label:'快递单号', width:'150',sortable:'custom'},
        {istrue:true,prop:'payDate',label:'付款日期', width:'100',sortable:'custom',formatter:(row)=>row.payDate&&formatTime(row.payDate,"YYYY-MM-DD")},
        {istrue:true,prop:'orderNo',label:'原始订单号', width:'180',sortable:'custom'},
        {istrue:true,prop:'orderNoInner',label:'内部订单号', width:'100',sortable:'custom',type:'orderLogInfo',orderType:'orderNoInner' },
        {istrue:true,prop:'shopCode',label:'店铺名称', width:'180',sortable:'custom',formatter:(row)=>row.shopName},
        {istrue:true,prop:'amounted',label:'已付金额', width:'100',sortable:'custom'},
        {istrue:true,prop:'status',label:'状态', width:'80',sortable:'custom'},
        {istrue:true,prop:'shopStatus',label:'店铺状态', width:'130',sortable:'custom'},
        {istrue:true,prop:'orderType',label:'订单类型', width:'100',sortable:'custom'},
        {istrue:true,prop:'buyerRemark',label:'买家留言', width:'150',sortable:'custom'},
        {istrue:true,prop:'orderRemark',label:'订单备注', width:'150',sortable:'custom'},       
        {istrue:true,prop:'label',label:'标签', width:'100',sortable:'custom'},       
        {istrue:true,prop:'proCode',label:'店铺款式编码', width:'120',sortable:'custom'},
        {istrue:true,prop:'goodsCode',label:'商品编码', width:'120',sortable:'custom'},
        {istrue:true,prop:'goodsName',label:'商品名称', width:'150',sortable:'custom'},
        {istrue:true,prop:'qty',label:'数量', width:'60',sortable:'custom'},
        {istrue:true,prop:'goodsCost',label:'成本价', width:'80',sortable:'custom'},
        {istrue:true,prop:'orderCost',label:'订单成本', width:'100',sortable:'custom'},
        {istrue:true,prop:'grossProfit',label:'毛利', width:'80',sortable:'custom'},
        {istrue:true,prop:'expressCompany',label:'快递公司', width:'100',sortable:'custom',formatter:(row)=>row.expressCompanyName},
        {istrue:true,prop:'overWeightFee',label:'续重费', width:'80',sortable:'custom'},
        {istrue:true,prop:'faceSheetFee',label:'面单费', width:'80',sortable:'custom'},
        {istrue:true,prop:'sjTotalFee',label:'运费合计', width:'80',sortable:'custom'},
        {istrue:true,prop:'sjTotalFeeShare',label:'运费分摊', width:'80',sortable:'custom'},
        {istrue:true,prop:'sourceName',label:'数据源', width:'100',sortable:'custom',formatter:(row)=>sourceNameList.find(a=>a.key==row.sourceName)?.value||" "},
        {istrue:true,prop:'errorType',label:'异常类型', width:'100',sortable:'custom'},
        {istrue:true,prop:'remark',label:'备注', width:'100',sortable:'custom'},
        {istrue:true,prop:'isRepeat',label:'是否重复发货', width:'120',sortable:'custom',type:'switch',formatter:(row)=>formatYesornoBool(row.isRepeat)},
        {istrue:true,prop:'orderGoodsWeight',label:'计算重量',tipmesg:"计算重量",width:'120',sortable:'custom'},
        {istrue:true,prop:'expressWeight',label:'快递重量', width:'100',sortable:'custom'},
        {istrue:true,prop:'diffWeight',label:'重量差', tipmesg:"快递账单重量-计算重量",width:'100',sortable:'custom'},  
        {istrue:true,prop:'createdTime',label:'计算时间', width:'160',sortable:'custom',formatter:(row)=>row.createdTime&&formatTime(row.createdTime,"YYYY-MM-DD HH:mm:ss")}, 
        {istrue:true,prop:'refundStatus',label:'退款状态', width:'100',sortable:'custom'},      
     ];
const tableHandles1=[
        {label:"查询", handle:(that)=>that.onSearch()},
        {label:"计算", handle:(that)=>that.onShowMapData()},
        {label:"导出", handle:(that)=>that.onExport()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: {
        batchNumber:null,
        settMonth:formatTime(dayjs().startOf("month").subtract(0,'month'), "YYYYMM"),
        platform:null,
        shopCode:"",
        orderNo:null,
        orderNoInner:null,
        expressCompanyId:null,
        expressNo:null,
        isRepeat:null,
        errorType:null,
        sourceName:null,
        remark:null,
        diffWeightMin:null,
        diffWeightMax:null,
        num:'快递单号',
        numValue:null,
        sendWarehouse:null,
        receiveTimeStart:null,
        receiveTimeEnd:null,
        timerange:[]
      },

      //表格配置
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      list: [],
      summaryarry:{},
      pager:{OrderBy:"settMonth",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,

      //下拉数据
      platformList: [],
      shopList: [],
      expresscompanylist:[],
      sendWarehouseList:[],
      yesnoList:[
        {key:true,value:'是'},
        {key:false,value:'否'},
      ],
      errorTypeList:[
        {key:'出库前取消',value:'出库前取消'},
        {key:'出货后取消',value:'出货后取消'},
      ],
      numList:[
        {name:'快递单号',id:'快递单号'},
        {name:'原始订单',id:'原始订单'},
        {name:'内部订单',id:'内部订单'}
      ],
      sourceNameList:sourceNameList,

      //计算弹窗配置
      calcDialog:{
         dialogVisible: false,
         loading:false,
         loadingText:"",
         filter:{
            settMonth:null,
            expressCompanyId:null,
            receiveTimeStart:null,
            receiveTimeEnd:null,
            timerange:[]
         }
      },     

      //日期配置
      pickerOptions:{
        disabledDate(time){
          return time.getTime()>Date.now();
        }
      },

      //导出
      exportProcess:{
          visible:false,
          percentage:0,
          status:null,
          pageSize:null,
      },
    }
  },
  async mounted() {
    await this.setPlatform();
    await this.getExpressComanyList();
    await this.setWarehouse();   
  },
  methods: {
    //设置平台下拉
    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
    },
    //设置店铺下拉
    async onchangeplatform(val){
      const res = await getshopList({platform:val,CurrentPage:1,PageSize:1000});
      this.shopList=res.data.list||[];
      this.filter.shopCode="";
    },
    //获取快递公司下拉
    async getExpressComanyList() {
       const res = await getExpressComanyAll({});
      if (!res?.success) {
        return;
      } 
      const data = res.data;
      this.expresscompanylist = data;
     },
     //设置仓库下拉
    async setWarehouse() {
      var whrule = await ruleSendWarehouse();
      this.sendWarehouseList = whrule.options;
    },
     changeNumType(){
        this.filter.expressNo=null;
        this.filter.orderNo=null;
        this.filter.orderNoInner=null;
        switch (this.filter.num){
          case "快递单号":
            this.filter.expressNo = this.filter.numValue;
            break;
          case "内部订单":
            this.filter.orderNoInner = this.filter.numValue;
            break;
          case "原始订单":
            this.filter.orderNo = this.filter.numValue;
            break;
        }
     },
     async onExport(){
        var countRes = await this.getcount();
        if(!countRes){
          return false;
        }
        if(!this.total){
          this.$message({message:"无数据",type:"warning"}); 
          return false;
        }
        var total=this.total;
        var params=this.getCondition();
        if(params===false){
            return;
        }
        var fileName=params.settMonth+"月";
        if(params.expressCompanyId>0 && this.expresscompanylist?.length>0){         
           fileName+=this.expresscompanylist.find(a=>a.id==params.expressCompanyId)?.name;
        }
        if(params.shopCode && this.shopList?.length>0){         
           fileName+=this.shopList.find(a=>a.shopCode==params.shopCode)?.shopName;
        }
        var that=this;
        this.$confirm('确定导出吗？','提示').then(async ()=>{         
          var pageSize=200000;    
          if(this.filter.batchNumber){
            pageSize=500000;
          }
          if(this.exportProcess.pageSize){
            pageSize=this.exportProcess.pageSize;
          }

          var totalPage=total/pageSize+1;
          that.exportProcess.visible=true;
          that.exportProcess.percentage=0;
          that.exportProcess.status=null;
          for (var i=1;i<=totalPage;i++) {            
            params.currentPage=i;
            params.pageSize=pageSize;
            var newfileName=fileName+"-"+params.currentPage;
            await that.Export(params,newfileName);
            that.exportProcess.percentage=parseInt(i/totalPage*100);
          }
          that.exportProcess.percentage=100;
          that.exportProcess.status="success";
        }).catch(()=>{});
     },
    //导出
    async Export(params,fileName){
        if(params===false){
            return;
        }         
        var res= await exportOrderExpressMap(params);
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob);        
        aLink.setAttribute('download',(fileName||"")+ '.xlsx' )
        aLink.click();          
    },
    //获取查询条件
    getCondition(){  
        this.changeNumType();  
        if(this.filter.orderNoInner && isNaN(this.filter.orderNoInner)){
            this.$message({message:"内部单号必须是数字",type:"warning"});
            return false;
        }
        //按快递单号或订单号或内部订单号不需要月份快递公司条件
        if(this.filter.expressNo || this.filter.orderNo || this.filter.orderNoInner || this.filter.batchNumber){
          if(this.filter.expressNo){
            var expressNos = this.filter.expressNo.split(" ").join(",");
            this.filter.expressNo=expressNos;
          }
        }        
        else{
          if (!this.filter.settMonth) {
            this.$message({message:"请选择结算月份",type:"warning"});
            return false;
          }
          if (!this.filter.expressCompanyId) {
            this.$message({message:"请选择快递公司",type:"warning"});
            return false;
          }
        }

        this.filter.receiveTimeStart=null;
        this.filter.receiveTimeEnd=null;
        if (this.filter.timerange && this.filter.timerange.length >1) {
            this.filter.receiveTimeStart = dayjs(this.filter.timerange[0]).format("YYYY-MM-DD");
            this.filter.receiveTimeEnd = dayjs(this.filter.timerange[1]).format("YYYY-MM-DD");
        }
        
        var pager = this.$refs.pager.getPager();
        var page  = this.pager;
        const params = {
          ...pager,
          ...page,
          ... this.filter
        }

        return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    //分页查询
    async getlist() {
      var params=this.getCondition();
      if(params===false){
            return;
      }
      await this.getcount();  
      if(!this.total){
        this.list =[];
        return;
      }
      this.listLoading = true
      const res = await pageOrderExpressMap(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      //this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry=res.data.summary;
      data.forEach(d => {
        d._loading = false
      })
      this.list = data;   
      
      await this.getSummary();
    },
    //获取总数
    async getcount(){
      var params=this.getCondition();
      if(params===false){
            return false;
      }
      const res = await getOrderExpressMapCount(params);
      if (!res?.success) {
        return false;
      }
      this.total=res.data;
      return true;
    },
    //获取汇总
    async getSummary(){
      var params=this.getCondition();
      if(params===false){
            return false;
      }
      const res = await getOrderExpressMapSummary(params);
      if (!res?.success) {
        return false;
      }
      this.summaryarry=res.data;
      return true;
    },
    //排序查询
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else{
        var orderBy =column.prop=="shopName"?"shopCode":column.prop;
        this.pager={OrderBy:orderBy,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    }, 
    selsChange: function(sels) {
      this.sels = sels
    },
    //计算弹窗
    onShowMapData(){
      this.calcDialog.dialogVisible=true;
    },
    getCalcCondition(){
      this.calcDialog.filter.receiveTimeStart=null;
      this.calcDialog.filter.receiveTimeEnd=null;
      if (this.calcDialog.filter.timerange && this.calcDialog.filter.timerange.length >1) {
            this.calcDialog.filter.receiveTimeStart = dayjs(this.calcDialog.filter.timerange[0]).format("YYYY-MM-DD");
            this.calcDialog.filter.receiveTimeEnd = dayjs(this.calcDialog.filter.timerange[1]).format("YYYY-MM-DD");
      }
      if(!(this.calcDialog.filter.settMonth||this.calcDialog.filter.receiveTimeStart&&this.calcDialog.filter.receiveTimeEnd)){
          this.$message({message:"结算月份和揽收时间不能同时为空",type:"warning"});
          return false;
        }
        if(!this.calcDialog.filter.expressCompanyId){
          this.$message({message:"请先选择快递公司！",type:"warning"});
          return false;
        }
        
        var params={...this.calcDialog.filter};
        return params;
    },
    //计算
    async onCalc(){
        var params=this.getCalcCondition();
        if(!params){
          return false;
        }
        this.calcDialog.loading = true;
        this.calcDialog.loadingText="计算中";
        const res = await calcOrderExpressMap(params)
        this.calcDialog.loading = false;
        this.calcDialog.loadingText="";
        if (!res?.success) {
          return
        }
        this.$message({message:"后台正在计算中，请耐心等待",type:"success"});
        this.refreshCalcLoading();       
    },
    //删除
    async onDel(){
        var params=this.getCalcCondition();
        if(!params){
          return false;
        }
        this.calcDialog.loading = true;
        this.calcDialog.loadingText="删除中";
        const res = await delOrderExpressMap(params)
        this.calcDialog.loading = false;
        this.calcDialog.loadingText="";
        if (!res?.success) {
          return
        }
        this.$message({message:"删除成功",type:"success"});
    },
    //刷新计算进度
    refreshCalcLoading(){
        var iv = setInterval(async () => {
          await this.isCalcOrderExpressMapFinish();
          if(this.calcDialog.loading == false){
            clearInterval(iv);
          }
        }, 1000);
    },
    //是否计算完成
    async isCalcOrderExpressMapFinish(){
        var params=this.getCalcCondition();
        if(!params){
          return false;
        }
        this.calcDialog.loading = true;
        this.calcDialog.loadingText="后台计算中";
        const res = await isCalcOrderExpressMapFinish(params)
        if (!res?.success) {
          return
        }
        if(res.data===true){
          this.$message({message:"计算完成",type:"success"});
          this.calcDialog.loading = false;
          this.calcDialog.loadingText="";
        }
    }
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary{
  margin-right: 7px;
}
</style>
