<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent size="mini">
        <el-form-item label="分仓:" size="mini">
          <el-select v-model="filter.warehouse" placeholder="请选择分仓" style="width: 100px" size="mini">
            <el-option label="请选择" value></el-option>
            <el-option label="义乌" value="0"></el-option>
            <el-option label="昌东" value="1"></el-option>
            <el-option label="昌北" value="2"></el-option>
            <el-option label="安徽" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否分仓:" size="mini">
          <el-select v-model="filter.isWarehouse" placeholder="请选择" style="width: 100px" size="mini">
            <el-option label="分仓" :value="true"></el-option>
            <el-option label="不分仓" :value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="采购员:">
        <el-select v-model="filter.brandId" multiple collapse-tags clearable placeholder="请选择采购员" style="width: 160px">
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
       </el-form-item>
       <el-form-item label="运营组:">
        <el-select v-model="filter.groupId" multiple collapse-tags clearable placeholder="请选择运营组" style="width: 140px">
            <el-option label="所有" value=""/>
            <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
       </el-form-item>
       <el-form-item label="新负责人:">
        <el-select v-model="filter.groupIdNew" multiple collapse-tags clearable placeholder="请选择运营组" style="width: 140px">
           <el-option label="所有" value=""/>
           <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
       </el-form-item>
        <el-form-item label="商品编码:">
          <el-input v-model="filter.goodsCode" placeholder="商品编码"  style="width: 120px"/>
        </el-form-item>
        <el-form-item label="商品编码名称:">
          <el-input v-model="filter.goodsName" placeholder="商品名称" style="width: 120px"/>
        </el-form-item>
        <el-form-item label="销量取值期间:">
          <el-select v-model="filter.days" placeholder="请选择周转天数" style="width: 70px">
            <el-option label="1天" value="1"></el-option>
            <el-option label="3天" value="3"></el-option>
            <el-option label="7天" value="7"></el-option>
            <el-option label="15天" value="15"></el-option>
            <el-option label="30天" value="30"></el-option>
            <el-option label="45天" value="45"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="期间销量:">
          <el-select v-model="filter.saleCountType" placeholder="期间销量" style="width: 90px">
            <el-option label="全部" value></el-option>
            <el-option label="等于0" value="0"></el-option>
            <el-option label="不等于0" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="周转区间:">
          <el-select v-model="filter.turnoverRang" placeholder="周转区间" style="width: 90px">
            <!-- 0:半个月内 1:15-30天 2:1-2个月 3:2-3个月 4:3-6个月 5:6-12个月 6:12个月以上 -->
            <el-option label="全部" value></el-option>
            <el-option label="半个月内" value="0"></el-option>
            <el-option label="15-30天" value="1"></el-option>
            <el-option label="1-2个月" value="2"></el-option>
            <el-option label="2-3个月" value="3"></el-option>
            <el-option label="3-6个月" value="4"></el-option>
            <el-option label="6-12个月" value="5"></el-option>
            <el-option label="12个月以上" value="6"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="计算方式:">
          <el-select v-model="filter.computeType" placeholder="计算方式" style="width: 100px">
            <el-option label="全部" value="0"></el-option>
            <el-option label="扣除20天内进仓数" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="20天内进仓:">
          <el-select v-model="filter.purchaseInDays20Type" placeholder="20天内进仓" style="width: 100px">
            <el-option label="全部" value></el-option>
            <el-option label="期初20天内无进仓数" value="0"></el-option>
            <el-option label="期末20天内无进仓数" value="1"></el-option>
            <el-option label="期初20天内有进仓数" value="2"></el-option>
            <el-option label="期末20天内有进仓数" value="3"></el-option>
            <el-option label="期初期末20天内无进仓数" value="4"></el-option>
            <el-option label="期初期末20天内有进仓数" value="5"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期1:">
           <el-date-picker v-model="filter.date1" type="date" placeholder="选择日期" :picker-options="pickerOptions" style="width: 125px"></el-date-picker>
        </el-form-item>
        <el-form-item label="日期2:">
           <el-date-picker v-model="filter.date2" type="date" placeholder="选择日期" :picker-options="pickerOptions" style="width: 125px"></el-date-picker>
        </el-form-item>
        <el-form-item label="库存数范围">
          <el-input-number v-model="filter.minStockNum" style="width: 110px"></el-input-number>至<el-input-number v-model="filter.maxStockNum" style="width: 110px"></el-input-number>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="startImport">导入</el-button>
          <el-button type="primary" @click="startImportV2"
          v-if="checkPermission(['api:Inventory:Warehouse:ImportWarehouseRecordv2Async'])">导入V2.0</el-button>
        </el-form-item>
      </el-form>
    </template>
     <el-tabs v-model="activeName" style="height: 94%;">
       <el-tab-pane label="详情" name="first" style="height: 100%;">
         <unsalabledetail :filter="filter" ref="unsalabledetail"/>
      </el-tab-pane>
      <!-- <el-tab-pane label="状态汇总分析" name="second" style="height: 100%;">
         <changestatusanalysis :filter="filter" ref="changestatusanalysis"/>
      </el-tab-pane> -->
      <el-tab-pane label="日分析" name="third" style="height: 100%;">
         <turnoverranganalysis1 :filter="filter" ref="turnoverranganalysis1"/>
      </el-tab-pane>
      <el-tab-pane label="周分析" name="four" style="height: 100%;">
         <turnoverranganalysis2 :filter="filter" ref="turnoverranganalysis2"/>
      </el-tab-pane>
      <el-tab-pane label="月分析" name="five" style="height: 100%;">
         <turnoverranganalysis3 :filter="filter" ref="turnoverranganalysis3"/>
      </el-tab-pane>
      <el-tab-pane label="无销量分析" name="six" style="height: 100%;">
         <nosaleanalysis :filter="filter" ref="nosaleanalysis"/>
      </el-tab-pane>
    </el-tabs>
    <el-dialog title="导入" :visible.sync="dialogVisible" width="30%" v-dialogDrag>
     <div>
       <!-- <el-alert title="温馨提示：请同时选取全仓、义乌、昌北、昌东 4个文件！" type="success" :closable="false">
      </el-alert> -->
        <!-- <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
             <el-date-picker v-model="importFilte.date" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd 23:59:59" placeholder="选择日期"></el-date-picker>
          </el-col>
          </el-row> -->
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="40" action accept=".xlsx"
                :on-change="uploadChange" :on-remove="uploadRemove" :http-request="uploadFile" :data="fileparm">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template> 
                  <el-button style="margin-left: 10px;" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?`上传中${upNumber}`:'上传' )}}</el-button>
                </el-upload>
            </el-col>
          </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
  </el-dialog>
  <el-dialog title="导入" :visible.sync="dialogV2Visible" width="30%" v-dialogDrag>
     <div>
    
        <el-row>
          <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
            <el-checkbox v-model="fileparmV2.istoday">今天</el-checkbox>
          </el-col>
          <el-col :xs="24" :sm="19" :md="19" :lg="19" :xl="19">
              <el-radio-group v-model="fileparmV2.warehouse" size="mini">
                <el-radio-button label border>总仓</el-radio-button>
                <el-radio-button label="0" border>义乌</el-radio-button>
                <el-radio-button label="2" border>昌北</el-radio-button>
                <el-radio-button label="1" border>昌东</el-radio-button>
              </el-radio-group>
          </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="40" action accept=".xlsx"
                :on-change="uploadChangeV2" :on-remove="uploadRemoveV2" :http-request="uploadFileV2" :data="fileparm">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template> 
                  <el-button style="margin-left: 10px;" size="small" type="success" :loading="uploadLoadingV2" @click="submitUploadV2">{{(uploadLoadingV2?`上传中${upNumberV2}`:'上传' )}}</el-button>
                </el-upload>
            </el-col>
          </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogV2Visible = false">关闭</el-button>
      </span>
  </el-dialog>
  </my-container>
</template>

<script>
import {getDirectorGroupList} from '@/api/operatemanage/base/shop'
import {getAllProBrand,importWarehouseRecord,importWarehouseRecordV2} from '@/api/inventory/warehouse'
import MyContainer from '@/components/my-container/nofooter'
import unsalabledetail from '@/views/inventory/unsalable/unsalabledetail'
import changestatusanalysis from '@/views/inventory/unsalable/changestatusanalysis'
import turnoverranganalysis1 from '@/views/inventory/unsalable/turnoverranganalysis1'
import turnoverranganalysis2 from '@/views/inventory/unsalable/turnoverranganalysis2'
import turnoverranganalysis3 from '@/views/inventory/unsalable/turnoverranganalysis3'
import nosaleanalysis from '@/views/inventory/unsalable/nosaleanalysis'
export default {
  name: 'Roles',
  components: { MyContainer,unsalabledetail,changestatusanalysis,turnoverranganalysis1,turnoverranganalysis2,turnoverranganalysis3,nosaleanalysis},
  data() {
    return {
      activeName: 'first',
      that:this,
      pickerOptions: {
          disabledDate(time) {return time.getTime() > Date.now();},
          shortcuts: [{
            text: '今天',
            onClick(picker) {picker.$emit('pick', new Date());}
          }, {
            text: '昨天',
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit('pick', date);
            }
          }, {
            text: '一周前',
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', date);
            }
          }]
        },
      filter: {
          warehouse:null,
          isWarehouse:false,
          goodsCode:'',
          goodsName:'',
          days :"3",
          saleCountType:null,
          turnoverRang:null,
          computeType:'0',
          purchaseInDays20Type:null,
          date1:null,
          date2:null,
          brandId:[],
          groupIdNew:[],
          groupId:[],
          minStockNum:1,
          maxStockNum:undefined,
      },
      brandlist:[],
      grouplist:[],
      pageLoading: false,
      dialogVisible: false,
      uploadLoading:false,
      upNumber:0,
      fileListV2:[],
      dialogV2Visible: false,
      uploadLoadingV2: false,
      fileList:[],
      fileparm:{ },
      fileparmV2:{istoday:true,warehouse:null},
    }
  },
  async mounted() {
    await this.init();
    await this.init1();
    await this.defaultBrandSelected();
    await this.onSearch();
  },
  beforeUpdate() {  },
  methods: {
    async init1(){
        var res1= await getDirectorGroupList();
        this.grouplist = res1.data.map(item => {
            return { value: item.key, label: item.value };
        });
        var res2= await getAllProBrand();
        this.brandlist = res2.data.map(item => {
          return { value: item.key, label: item.value };
        });
    },
    async init(){
        var date11 = new Date(); date11.setDate(date11.getDate()-3);
        var date22 = new Date(); date22.setDate(date22.getDate()-2);
        this.filter.date1=date11;
        this.filter.date2=date22;
      },
    async defaultBrandSelected(){
        this.filter.brandId=[];
        this.brandlist.forEach(f=>{
          if (f.label.indexOf("已下架")==-1)  
              this.filter.brandId.push(f.value)
        })
      },
    async datetostr(date) {
        var y = date.getFullYear();
        var m = ("0" + (date.getMonth() + 1)).slice(-2);
        var d = ("0" + date.getDate()).slice(-2);
        return y + "-" + m + "-" + d;
    },
    async addDate(date,days){ 
        var d=new Date(date); 
        d.setDate(d.getDate()+days); 
        var m=d.getMonth()+1; 
        return d.getFullYear()+'-'+m+'-'+d.getDate(); 
    },
    async onSearch() {
     if (this.filter.date1 instanceof Date) this.filter.date1=await this.datetostr(this.filter.date1)
     if (this.filter.date2 instanceof Date) this.filter.date2=await this.datetostr(this.filter.date2)      
      this.$nextTick(() => {
          if (this.activeName=='first') 
            this.$refs.unsalabledetail.onSearch();
          else if (this.activeName=='second')
            this.$refs.changestatusanalysis.onSearch();
          else if (this.activeName=='third')
            this.$refs.turnoverranganalysis1.onSearch(0);
          else if (this.activeName=='four')
            this.$refs.turnoverranganalysis2.onSearch(1);
          else if (this.activeName=='five')
            this.$refs.turnoverranganalysis3.onSearch(2);
          else if (this.activeName=='six')
            this.$refs.nosaleanalysis.onSearch();
      });
    },
    startImport(){
      this.dialogVisible=true;
    },
     uploadChange(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for(var i=0;i<fileList.length;i++){
          if(fileList[i].status=="success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.fileList = list;
      }
    },
     uploadRemove(file, fileList){
       this.uploadChange(file, fileList);
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    async submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
        }
      this.fileHasSubmit=true;
      this.uploadLoading=true;
      this.$refs.upload.submit();
    },
   async uploadFile(item) {
    if(!this.fileHasSubmit) return false; 
    this.fileHasSubmit=false;
    this.upNumber=this.fileList.length;   
    this.fileList.forEach(async f=>{
      var file=f;
      console.log('file',file) 
      const form = new FormData();
      form.append("file", file);
　    var res=await importWarehouseRecord(form); 
      if (res.code==1){
        this.$message({ message: `${file.name}上传成功,总计${this.fileList.length}个文件...`, type: "success" });
        this.uploadLoading=false;
      } 
      else this.$message({ message:`${file.name}上传失败,${res.msg}`, type: "warning" });
    })
     this.$refs.upload.clearFiles();
    //  
    },

  async startImportV2(){
      this.dialogV2Visible=true;
    },
  uploadChangeV2(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for(var i=0;i<fileList.length;i++){
          if(fileList[i].status=="success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.fileListV2 = list;
      }
    },
     uploadRemoveV2(file, fileList){
       this.uploadChangeV2(file, fileList);
    },
    //上传成功
    uploadSuccessV2(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    async submitUploadV2() {
      if (!this.fileListV2 || this.fileListV2.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
        }
      this.fileHasSubmitV2=true;
      this.uploadLoadingV2=true;
      this.$refs.upload.submit();
    },
   async uploadFileV2(item) {
    if(!item){
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
        }
    if(!this.fileHasSubmitV2) return false; 
    this.fileHasSubmitV2=false;
    this.upNumberV2=this.fileListV2.length; 
    this.fileListV2.forEach(async f=>{
      var file=f;
      const form = new FormData();
      form.append("file", file);
      form.append("istoday", this.fileparmV2.istoday);
      form.append("warehouse", this.fileparmV2.warehouse);
　    var res=await importWarehouseRecordV2(form); 
      if (res.code==1) 
      {
        this.$message({ message: `${file.name}上传成功,总计${this.fileListV2.length}个文件...`, type: "success" });
        this.uploadLoadingV2=false;
      }
      else this.$message({ message:`${file.name}上传失败,${res.msg}`, type: "warning" });
    })
     this.$refs.upload.clearFiles();
    
    },
  }
}
</script>
