<template>
  <my-container v-loading="pageLoading" >
    <el-tabs v-model="activeName" style="height: 95%">
      <el-tab-pane label="抖音日报" name="first1" style="height: 100%">
        <productReportDy ref="productReportDy" style="height: 100%"></productReportDy>
      </el-tab-pane>
      <el-tab-pane label="订单日报" name="first2" :lazy="true" style="height: 100%"
        v-if="checkPermission('DyOrderDayReport')">
        <DyOrderDayReport ref="DyOrderDayReport" @ChangeActiveName2="ChangeActiveName2" style="height: 100%">
        </DyOrderDayReport>
      </el-tab-pane>
      <el-tab-pane label="编码日报" name="first3" :lazy="true" style="height: 100%"
        v-if="checkPermission('DyGoodCodeDayReport')">
        <DyGoodCodeDayReport @ChangeActiveName="ChangeActiveName" @ChangeActiveName2="ChangeActiveName2" ref="DyGoodCodeDayReport" style="height: 100%">
        </DyGoodCodeDayReport>
      </el-tab-pane>
      <el-tab-pane label="ID日报" name="first4" :lazy="true" style="height: 100%" v-if="checkPermission('DyIdDayReport')">
        <DyIdDayReport @ChangeActiveName="ChangeActiveName" ref="DyIdDayReport" style="height: 100%"></DyIdDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺日报" name="first5" :lazy="true" style="height: 100%" v-if="checkPermission('DyShopDayReport')">
        <DyShopDayReport @ChangeActiveName="ChangeActiveName" ref="DyShopDayReport" style="height: 100%">
        </DyShopDayReport>
      </el-tab-pane>
      <el-tab-pane label="达人日报" name="first6" :lazy="true" style="height: 100%"
        v-if="checkPermission('DyMasterDayReport')">
        <DyMasterDayReport @ChangeActiveName="ChangeActiveName" @ChangeActiveName2="ChangeActiveName2"
          ref="DyMasterDayReport" style="height: 100%"></DyMasterDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺SKU日报" name="first10" :lazy="true" style="height: 100%"
        v-if="checkPermission('DyCommodityDayReport')">
        <DyCommodityDayReport @ChangeActiveName="ChangeActiveName" ref="DyCommodityDayReport" style="height: 100%">
        </DyCommodityDayReport>
      </el-tab-pane>
      <el-tab-pane label="明细日报" name="first7" :lazy="true" style="height: 100%"
        v-if="checkPermission('DyDetailDayReport')">
        <DyDetailDayReport @ChangeActiveName="ChangeActiveName" ref="DyDetailDayReport" style="height: 100%">
        </DyDetailDayReport>
      </el-tab-pane>
      <el-tab-pane label="出仓负利润ID订单明细" name="first11" :lazy="true" style="height: 100%"
        v-if="checkPermission('DyOutgoingprofitIDorderdetail')">
        <DyOutgoingprofitIDorderdetail @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="DyOutgoingprofitIDorderdetail" style="height: 100%"></DyOutgoingprofitIDorderdetail>
      </el-tab-pane>
      <el-tab-pane label="商务日报" name="first8" :lazy="true" style="height: 100%">
        <!-- v-if="checkPermission('api:OperateManage:DistributionGoodsOnline:GetDistributionGoodsOnlineRecordAsync')"  -->
        <productReportDyWise ref="productReportDyWise" style="height: 100%"></productReportDyWise>
      </el-tab-pane>
      <el-tab-pane label="商务达人" name="first9" :lazy="true" style="height: 100%"
        v-if="checkPermission('productReportDyWiseMan')">
        <!-- v-if="checkPermission('api:OperateManage:DistributionGoodsOnline:GetDistributionGoodsOnlineRecordAsync')"  -->
        <productReportDyWiseMan ref="productReportDyWiseMan" style="height: 100%"></productReportDyWiseMan>
      </el-tab-pane>
      <el-tab-pane label="寄样商务" name="first12" :lazy="true" style="height: 100%"
        v-if="checkPermission('productReportDyWiseMan')">
        <sampleBusiness ref="sampleBusiness" style="height: 100%"></sampleBusiness>
      </el-tab-pane>
      <el-tab-pane label="SKUS日报" name="first13" :lazy="true" style="height: 100%"
        v-if="checkPermission('DyOrderDayReport')">
        <DySkusDayReport ref="DySkusDayReport" @ChangeActiveName2="ChangeActiveName2" style="height: 100%">
        </DySkusDayReport>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import productReportDy from "./productReportDy.vue";
import DyOrderDayReport from "./DyOrderDayReport.vue";
import DySkusDayReport from "./DySkusDayReport.vue";
import DyGoodCodeDayReport from "./DyGoodCodeDayReport.vue";
import DyIdDayReport from "./DyIdDayReport.vue";
import DyCommodityDayReport from "./DyCommodityDayReport.vue";
import DyOutgoingprofitIDorderdetail from "./DyOutgoingprofitIDorderdetail.vue";
import DyShopDayReport from "./DyShopDayReport.vue";
import DyDetailDayReport from "./DyDetailDayReport.vue";
import DyMasterDayReport from "./DyMasterDayReport.vue";
import productReportDyWiseMan from "./DouYin/productReportDyWiseMan.vue";
import productReportDyWise from "./DouYin/productReportDyWise.vue";
import middlevue from "@/store/middle.js"
import sampleBusiness from "./sampleBusiness.vue";
export default {
  name: "productReportDyIndex",
  components: {
    MyContainer,
    productReportDy,
    productReportDyWiseMan,
    productReportDyWise,
    DyOrderDayReport,
    DySkusDayReport,
    DyGoodCodeDayReport,
    DyIdDayReport,
    DyCommodityDayReport,
    DyShopDayReport,
    DyDetailDayReport,
    DyMasterDayReport,
    DyOutgoingprofitIDorderdetail,
    sampleBusiness
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },
  async mounted() {
    middlevue.$on('toLinkTxDetailDayReport', (data) => {
      if (data.isTrue && data.plat == 'dy') {
        this.activeName = 'first7';
        setTimeout(() => {
          middlevue.$emit('toLinkTxDetailDayReportQuery', data)
        }, 500);
      } else {
        this.activeName = 'first11';
        setTimeout(() => {
          middlevue.$emit('toLinkTxOutgoingprofitIDorderdetailQuery', data)
        }, 500);
      }
    })
  },
  beforeDestroy() {
    middlevue.$off('toLinkTxDetailDayReport');
  },
  methods: {
    ChangeActiveName(activeName) {
      this.activeName = 'first2';
      this.$refs.DyOrderDayReport.DyGoodCodeDayReportArgument(activeName)
    },
    ChangeActiveName2(activeName, No, Time) {
      this.activeName = 'first7';
      this.$refs.DyDetailDayReport.DyDetailDayReportArgument(activeName, No, Time)
    }
  },
};
</script>

<style lang="scss" scoped></style>
