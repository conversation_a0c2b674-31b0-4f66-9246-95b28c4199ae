<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker class="publicCss" v-model="ListInfo.yearMonth" type="month" format="yyyyMM"
          value-format="yyyyMM" placeholder="选择月份">
        </el-date-picker>
        <el-select filterable clearable v-model="ListInfo.shopCode" placeholder="所属店铺" class="publicCss">
          <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
            :value="item.shopCode"></el-option>
        </el-select>
        <el-button type="primary" @click="getList('search')">查询</el-button>
        <el-button type="primary" @click="onExport">汇总导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'detailsFund202302031421'" :tablekey="'detailsFund202302031421'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { getFundDetail_KS, ExportFundDetail_KS } from '@/api/monthbookkeeper/financialDetail'
import { getList as getshopList } from '@/api/operatemanage/base/shop';

const tableCols = [
  { sortable: 'custom', width: '250', align: 'center', prop: 'serialNumberFinacial', label: '账务流水号', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'orderNumber', label: '关联业务单号', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'incomeTime', label: '入账时间', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'recoganizeType', label: '账务方向', formatter: (row) => row.recoganizeType == 1 ? "收" : row.recoganizeType == 2 ? "支" : "其他异常", },
  { sortable: 'custom', width: '250', align: 'center', prop: 'occurAmount', label: '发生额（元）', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'endingBalance', label: '期末余额（元）', },
  {
    sortable: 'custom', width: '250', align: 'center', prop: 'businessType', label: '业务类型', formatter: (row) => {
      const businessTypeMap = {
        1: '贷款结算',
        2: '结算后退款',
        3: '扣减前临时解冻',
        4: '佣金/技术服务费返还',
        5: '资金冻结',
        6: '资金解冻',
        7: '资金扣减',
        8: '资金转账'
      };
      return businessTypeMap[row.businessType] || '未知类型';
    }
  },
  { sortable: 'custom', width: '250', align: 'center', prop: 'businessDesc', label: '描述', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'remark', label: '备注', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'shopName', label: '店铺', },
]
export default {
  name: "detailsFund",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      shopList: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        platform: 14,
        yearMonth: '',
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    const res1 = await getshopList({ platform: 14, CurrentPage: 1, PageSize: 100000 });
    this.shopList = res1.data.list
    // await this.getList()
  },
  methods: {
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getFundDetail_KS(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async onExport(opt) {
      if (!this.ListInfo.yearMonth) {
        this.$message({ message: "请先选择月份", type: "warning" });
        return;
      }
      const params = { ...this.ListInfo, ...opt };
      let res = await ExportFundDetail_KS(params);
      if (!res?.data) {
        return
      }
      this.$message({ message: "正在后台导出中，请点击头像-在下载管理中查看", type: "success" });
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 180px;
    margin-right: 5px;
  }
}
</style>
