<template>
  <!-- 客服等级设置 -->
  <container v-loading="pageLoading">
    <template #header>
      <el-row style="width:100%; display: flex;align-items: center;">
        <span style="font-size: 12px;">提成系数设置</span>
        <span style="font-size: 12px;margin-left: 20px; color: red;">编辑后，根据设置的生效日期修改</span>
        <el-select v-model="ListInfo.region" placeholder="请选择区域" clearable multiple collapse-tags filterable style="width: 200px; margin-left: 10px">
              <el-option :label="item.label" :value="item.value" v-for="(item,index) in quyu" :key="index"  />
          </el-select>
          <el-select v-model="ListInfo.platform" placeholder="请选择平台" clearable multiple collapse-tags filterable style="margin: 0 10px;">
          <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
          <el-select v-model="ListInfo.subgroup" placeholder="请选择分组" clearable multiple collapse-tags filterable >
          <el-option v-for="item in subgroupListArr" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" style="margin-left: auto; margin-right: 10px;"
          @click="customerService">新增提成系数</el-button>
      </el-row>
    </template>
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :isSelectColumn="false" :showsummary='true' :summaryarry='summaryarry' :tablefixed='false' :tableData='tableData'
      :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"
      style="width:100%;height:95%;margin: 0">
    </ces-table>
    <!-- <template #footer>
          <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
      </template> -->
    <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />

    <el-dialog :title="ruleName ? '新增提成系数' : '编辑提成系数'" :visible.sync="dialogVisibleStock" width="35%"
      :close-on-click-modal="false" v-dialogDrag @close="()=>{ dialogVisibleStock = false; filter.subgroup = []}">
      <div style="margin-bottom: 30px;">
        <span style="color: #F56C6C;">*</span>
        <span>
          选择区域：
        </span>
        <el-select v-model="filter.region" placeholder="请选择区域" filterable style="width: 200px; margin-left: 10px"
          clearable>
          <el-option :label="item.label" :value="item.value" v-for="(item, index) in quyu" :key="index" />
        </el-select>
        <span style="color: #F56C6C;margin-left: 5px;">*</span>
        <span>
          选择平台：
        </span>
        <el-select v-model="filter.platform" placeholder="请选择平台" filterable style="width: 200px; margin-left: 10px"
          clearable>
          <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div style="margin-top: 5px;">
          <span style="color: #F56C6C;">*</span>
          <span style="margin-right: 14px;">
            选择组：
          </span>
          <!-- <el-cascader style="width: 490px !important; margin-left: 10px" v-model="filter.subgroup" :options="deptList" filterable
            :props="{ multiple: ruleName, label: 'full_name', value: 'full_name' }" placeholder="请选择分组" collapse-tags
            :show-all-levels="false" clearable>
          </el-cascader> -->
          <el-select v-model="filter.subgroup" placeholder="请选择分组" clearable :multiple="ruleName" collapse-tags filterable style="width: 200px;margin-left: 10px;">
            <el-option v-for="item in subgroupListArr" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </div>
      </div>
      <div>
        <span>
          <el-button type="text" style="color: 409EFF;margin-right: 20px;" @click="addProps">新增一行</el-button>
        </span>
        <span style="color: #F56C6C;">
          新增的规则,根据设置的最新生效日期修改
        </span>
      </div>
      <el-table :data="formData" style="width: 95%;height:95%" max-height="400">
        <el-table-column label="#" type="index" width="40" />
        <el-table-column prop="effectTime" width="135">
          <template #header="{ column }">
            <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>最新生效日期
          </template>
          <template #default="{ row }">
            <el-date-picker v-model="row.effectTime" type="date" clearable style="width: 123px;"
              :value-format="'yyyy-MM-dd'" placeholder="选择日期">
            </el-date-picker>
          </template>
        </el-table-column>
        <el-table-column prop="goldMedalRatio" width="160">
          <template #header="{ column }">
            <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>金牌
          </template>
          <template #default="{ row }">
            <el-input v-model="row.goldMedalRatio" clearable placeholder="请输入" style="width: 140px;" type="number" class="pass_input"
              @blur="validateGoldMedalRatio(row)" />
          </template>
        </el-table-column>
        <el-table-column prop="silverMedalRatio" width="160">
          <template #header="{ column }">
            <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>银牌
          </template>
          <template #default="{ row }">
            <el-input v-model="row.silverMedalRatio" clearable placeholder="请输入" style="width: 140px;" type="number" class="pass_input"
              @blur="validateGoldMedalRatio(row)" />
          </template>
        </el-table-column>
        <el-table-column prop="updateUserNo">
          <template #header="{ column }">
            <span>操作人</span>
          </template>
          <template #default="{ row }">
            <span>{{ row.updateUserNo }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="btnGroup">
        <el-button style="margin-right: 10px;"  @click="()=>{ dialogVisibleStock = false; filter.subgroup = []}">取消</el-button>
        <el-button type="primary" @click="submit" v-throttle="3000">保存</el-button>
      </div>
    </el-dialog>
  </container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import container from '@/components/my-container';
import cesTable from '@/components/Table/table.vue';
import YhImgUpload1 from "@/components/upload/yh-img-upload1.vue";
import { formatLinkProCode, formatPlatform, platformlist } from '@/utils/tools'
import { dictionary, customerLevelConfigPage, customerCommissionConfigPage, customerCommissionBatchSave, customerCommissionConfigRemove, customerCommissionConfigDetail } from '@/api/bladegateway/yunhangiscustomer.js';
import { getCurrentUser } from '@/api/inventory/packagesprocess'
import {
  importBaseSupermarketGoodAsync,
  getPageBaseSupermaketGoodAsync,
  updateBaseSupermaketGoodAsync,
  delBaseSupermaketGoodAsync,
  getGoodTypeAsync,
  saveGoodTypeAsync,
  updateBaseSupermaketGoodStockAsync
} from '@/api/profit/orderfood';
import { getOrderFoodMenuProvier, getAreaSetList, getUserOrderDetailList, quyuList } from '@/api/profit/orderfood';
import fliterjs from "@/views/customerservice/departmentSalaryCost/fliterjs.js";

const tableCols = [

  { istrue: true, align: 'center', prop: 'region', label: '区域', width: 'auto', formatter: (row) => row.regionName ? row.regionName : '' },
  { istrue: true, align: 'center', prop: 'platform', label: '平台', width: 'auto', formatter: (row) => row.platformName ? row.platformName : '' },
  // { istrue: true, align: 'center', prop: 'subgroup', label: '分组', width: 'auto' },
  { istrue: true, prop: 'subgroup', label: '分组',  width: '200', sortable: 'custom', formatter: (row)=> row.subgroupName },

  { istrue: true, align: 'center', prop: 'silverMedalRatio', label: '银牌', width: 'auto', },
  { istrue: true, align: 'center', prop: 'goldMedalRatio', label: '金牌', width: 'auto', },

  { istrue: true, align: 'center', prop: 'updateUserNo', label: '编辑人', width: 'auto', formatter: (row) => row.updateUserName ? row.updateUserName : '' },
  { istrue: true, align: 'center', prop: 'updateTime', label: '操作时间', width: 'auto', },
  {
    istrue: true, type: "button", label: '设置下月规则', align: 'center', sortable: false, width: "100", fixed: "right",
    btnList: [{ label: "编辑", handle: (that, row) => that.editOperation(row) }]
  },
  {
    istrue: true, type: "button", label: '操作', align: 'center', sortable: false, width: "100", fixed: "right",
    btnList: [{ label: "删除", type: "danger", handle: (that, row) => that.deleteOperation(row) }]
  }
];

const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = startDate;
//const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
  name: "Users",
  components: { container, cesTable, YhImgUpload1 },
  mixins: [fliterjs],
  data() {
    return {
      servicePlatform: [],
      serviceRegion: [],
      serviceGroup: [],
      formData: [],
      ruleName: true,
      dialogVisibleStock: false,
      fileList: [],
      gysList: [],
      dialogEdit: false,
      stockForm: {
        stock: 0,
        id: 0
      },
      editForm: {
      },
      form: {
        goodTypeList: [],
      },
      quyuList: [],
      dialogMenuType: false,
      uploadLoading: false,
      dialogVisibleData: false,
      that: this,
      ListInfo: {
        region: [],
        platform: [],
        subgroup: [],
      },
      filter: {
        region: '',
        platform: '',
        subgroup: [],
      },
      // filter: {
      //   //  timerange: [startDate, endDate],
      //   orderMenuDateRange: [
      //     formatTime(dayjs().subtract(30, "day"), "YYYY-MM-DD"),
      //     formatTime(new Date(), "YYYY-MM-DD"),
      //   ],
      //   zoneName: '',
      //   orderMenuStartDate: startDate,
      //   orderMenuEndDate: endDate,
      // },
      platformlist: platformlist,
      tableCols: tableCols,
      tableHandles: null,
      tableData: [],
      total: 0,
      pager: { OrderBy: "", IsAsc: false },
      listLoading: false,
      pageLoading: false,
      summaryarry: {},
      sels: [],
      dialogVisibleStock: false,
      updateUserNo: '',
    };
  },
  async mounted() {
    //  await this.getList();
    await this.getGoodType();
    await this.onSearch();
  },
  methods: {
    validateGoldMedalRatio(row) {
      if (row.goldMedalRatio != null && (row.goldMedalRatio < 0.0001 || row.goldMedalRatio > 1)) {
        this.$message.error('金牌比例提成范围为0.0001-1');
        row.goldMedalRatio = null;
      }
      if (row.silverMedalRatio != null && (row.silverMedalRatio < 0.0001 || row.silverMedalRatio > 1)) {
        this.$message.error('银牌比例提成范围为0.0001-1');
        row.silverMedalRatio = null;
      }
    },
    async editOperation(row) {
      this.ruleName = false;
      this.filter = {
        region: '',
        platform: '',
        subgroup: [],
      };
      this.formData = []
      const params = { region: row.region, subgroup: row.subgroup, platform: row.platform };
      const { data, success } = await customerCommissionConfigDetail(params);
      if (success) {
        this.formData = data.list;
        this.formData.forEach(item => {
          item.effectTime = formatTime(item.effectTime, "YYYY-MM-DD");
          item.updateUserNo = row.updateUserName;
        });
        this.filter = {
          region: Number(row.region),
          platform: Number(row.platform),
          // subgroup: row.subgroup.split(","),
          subgroup: row.subgroup?Number(row.subgroup):undefined,
        };
        this.dialogVisibleStock = true;
      } else {
        this.$message.error("获取数据失败");
      }
    },
    deleteOperation(row) {
      this.$confirm('是否删除该提成系数?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = { ids: row.id };
        const { success } = await customerCommissionConfigRemove(params);
        if (success) {
          this.$message.success("删除成功");
          this.onSearch();
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    addProps() {
      this.formData.push({
        goldMedalRatio: null,
        silverMedalRatio: null,
        updateUserNo: this.updateUserNo,
        effectTime: null,
        id: null,
      });
    },
    async submit() {
      // if (Array.isArray(this.filter.subgroup) && this.filter.subgroup.length > 0) {
      //   if(this.ruleName){
      //     let newarr = [];
      //     this.filter.subgroup.map((item)=>{
      //       newarr.push(item.join(","));
      //     });
      //     this.filter.subgroups = newarr;
      //   }else{
      //     this.filter.subgroups = [this.filter.subgroup.join(",")];
      //   }
      // }
      this.filter.subgroups = this.filter.subgroup;
      const params = { commissionConfigs: this.formData, ...this.filter };
      const { success } = await customerCommissionBatchSave(params);
      if (success) {
        this.$message.success("保存成功");
        this.dialogVisibleStock = false;
        this.getList();
      }
    },
    customerService() {
      this.formData = [];
      this.filter = {
        region: '',
        platform: '',
        subgroup: [],
      };
      this.ruleName = true;
      this.dialogVisibleStock = true;
    },
    async getquyu() {
      const res = await getAreaSetList({ getLevel: 1 });
      if (!res.success) {
        return
      }
      this.quyuList = res.data;
      if (!this.filter.zoneName) {
        this.filter.zoneName = this.quyuList[0];
      }
    },
    async getsize() {
      let params = {

      }
      const res = await getOrderFoodMenuProvier();
      if (!res.success) {
        return
      }
      this.gysList = res.data;
    },
    async getgyss() {
      const res = await getOrderFoodMenuProvier();
      if (!res.success) {
        return
      }
      this.gysList = res.data;
      this.filter.gysName = this.gysList[0];
    },
    async getGoodType() {
      const res = await getGoodTypeAsync();
      this.form.goodTypeList = res?.data;
      const { data } = await getCurrentUser();
      this.updateUserNo = data?.userName;
    },
    // async onSearch() {
    //   this.$refs.pager.setPage(1);
    //   this.$refs.table.currentLvl = 9;
    //   await this.getquyu();

    //   await this.getList();

    // },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.$refs.pager.setPage(1);
      await this.onSearch();
    },
    async getList() {
      var that = this;
      this.listLoading = true;
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.ListInfo};
      const res = await customerCommissionConfigPage(params).then(res => {
        that.total = res.data?.total;
        that.tableData = res.data?.list;
        that.summaryarry = res.data?.summary;
      });


      this.listLoading = false;
    },
  }
})
</script>
<style scoped lang="scss">
.btnGroup {
  margin-top: 40px;
  display: flex;
  justify-content: end;
}

::v-deep .pass_input input::-webkit-outer-spin-button,
::v-deep .pass_input input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

::v-deep .pass_input input[type="number"] {
  appearance: textfield;
  -moz-appearance: textfield;
}
::v-deep .el-cascader__search-input{
  margin: 2px 0 2px 5px;
  height: 18px;
  align-items: center;
}
::v-deep .el-select__tags-text {
  max-width: 65px;
}
</style>
