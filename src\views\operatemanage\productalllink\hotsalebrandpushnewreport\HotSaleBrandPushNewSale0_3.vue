<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
        </template>
        <vxetablebase :id="'HotSaleBrandPushNewSale0_3202408041719'" ref="table" :that='that' :isIndex='true'
            :hasexpand='true' :tablefixed='true' :border="true" @sortchange='sortchange' @select='selectchange'
            :tableData='tableData' :tableCols='tableCols' :showsummary='true' :summaryarry='summaryarry'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="listLoading"
            :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import datepicker from '@/views/customerservice/datepicker';
import { formatTime } from "@/utils";
import dayjs from 'dayjs'
import { pickerOptions } from '@/utils/tools';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetHotSaleBrandPushNewSaleFsDtlPageList
} from '@/api/operatemanage/productalllink/alllink'
const tableCols = [
    { istrue: true, fixed: 'left', width: '40', type: "checkbox" },
    { sortable: 'custom', width: '80', align: 'center', prop: 'createdUserName', label: '推荐人', },
    //{ sortable: 'custom', width: '120', align: 'center', prop: 'hotSaleBrandPushNewGoodsCompeteId2', label: '推荐ID', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'hotSaleBrandPushNewTime', label: '推荐时间', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'styleCode', label: '系列编码', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'orderCount', label: '总单量' },
    { sortable: 'custom', width: '120', align: 'center', prop: 'goodsCount', label: '总销量' },
    { sortable: 'custom', width: '120', align: 'center', prop: 'saleAmont', label: '总销售额' },
    { sortable: 'custom', width: '120', align: 'center', prop: 'profit4', label: '总毛四利润' },
];
export default {
    name: "HotSaleBrandPushNewSale0_3",
    components: {
        MyContainer, datepicker, vxetablebase
    },
    data() {
        return {
            auditVisible: false,
            activities: [],
            timeRanges: [],
            that: this,
            pickerOptions,
            filter: {
                timerange: [],
                createdStartDate: null,
                createdEndDate: null,
                createdUserId: 999999999,
                createdUserName: "",
            },
            pager: { OrderBy: "goodsCount", IsAsc: false },
            tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            summaryarry: {},

            isSum: false,
            delStyleCodeDialog: {
                visible: false,
            },
            delStyleCodeSel: null,
            delStyleCodeList: [],
        }
    },
    async mounted() {
    },
    computed: {
    },
    methods: {
        async loadData(args) {
            this.filter.createdUserId = args.createdUserId;
            this.filter.createdUserName = args.createdUserName;
            this.filter.timerange = args.timerange;
            this.filter.hired_dateDes = args.hired_dateDes;
            this.filter.clickColKey = args.clickColKey;

            this.filter.createdUserNames = args.createdUserNames;
            this.filter.createUserAreas = args.createUserAreas;
            this.filter.createUserRoles = args.createUserRoles;
            this.filter.createUserDeptNames = args.createUserDeptNames;

            console.log(this.filter, "this.filter");
            this.onSearch();
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getParam() {
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.createdStartDate = this.filter.timerange[0];
                this.filter.createdEndDate = this.filter.timerange[1];
            }
            else {
                this.filter.createdStartDate = null;
                this.filter.createdEndDate = null;
            }
            let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            const res = await GetHotSaleBrandPushNewSaleFsDtlPageList(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
                this.summaryarry = res.data.summary;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}
</style>
