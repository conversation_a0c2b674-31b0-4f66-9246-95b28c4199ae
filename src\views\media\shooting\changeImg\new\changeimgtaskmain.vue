<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <template #header>
            <changeimgtaskfilter :key="tabkey" @topSearch="topSearch" @onAddTask="onAddTask" @onAddOrder="onAddOrder"
                @ShowHideonSearch="ShowHideonSearch" @handleCommand="handleCommand" @batchConfirmTask="batchConfirmTask" @onExport="onExport"
                :platformList="platformList" :warehouselist="warehouselist" :dockingPeopleList="dockingPeopleList"
                :fpPhotoLqNameList="fpPhotoLqNameList" :taskUrgencyList="taskUrgencyList" :islook="islook"
                :groupList="groupList" :listtype="listtype"></changeimgtaskfilter>
        </template>
        <changeimgtaskTable ref="packDesgintask" :id="tabkey" :tableData='tasklist' :that='that' :height="'100%'"
            :showsummary='true' :summaryarry='summaryarry' @summaryClick='summaryClick' :loading='listLoading'
            :showCacle="listtype == 3" @sortchange='sortchange' @checkboxall="selectchangeevent"
            @selectchangeevent="selectchangeevent" @rowChange="rowChange" @shootUrgencyCilck="shootUrgencyCilck"
            @openTaskRmarkInfo="openTaskRmarkInfo" @videotaskuploadfileDetal="videotaskuploadfileDetal"
            @editTask="editTaskAction" @openComputOutInfo="openComputOutInfo" @onShowOrderDtl="onShowOrderDtl"
            @onShowExproessHttp="onShowExproessHttp">
        </changeimgtaskTable>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
                :sizes="[50, 200, 300, 800, 1000, 2000, 20000]" :page-size="1000" @get-page="getTaskList" />
        </template>
        <!--创建任务-->
        <div class="dialog1">
            <el-dialog title="" :visible.sync="addTask" style="position: fixed; top: -60px;" width="727px"
                :show-close="false" :before-close="handleClose" element-loading-text="拼命加载中" v-dialogDrag
                v-loading="addLoading">
                <changeimgtaskaddfrom ref="changeimgtaskaddfrom" v-if="addTask" :onCloseAddForm="onCloseAddForm"
                    :taskUrgencyList="taskUrgencyList" :groupList="groupList" :warehouselist="warehouselist"
                    :userList="fpPhotoLqNameList" :platformList="platformList" :islook='islook'></changeimgtaskaddfrom>
            </el-dialog>
        </div>
        <!--编辑任务-->
        <div class="dialog1">
            <el-drawer :visible.sync="editTask" :close-on-click-modal="false" direction="rtl" size="767px"
                element-loading-text="拼命加载中" v-loading="addLoading" :show-close="false" wrapperClosable>
                <changeimgtaskeditfrom v-if="editTask" ref="changeimgtaskeditfrom" :onCloseAddForm="onCloseAddForm"
                    style="height: 100%;width:100%" :taskUrgencyList="taskUrgencyList" :groupList="groupList"
                    :warehouselist="warehouselist" :userList="fpPhotoLqNameList" :platformList="platformList"
                    :islook='islook' :listtype="listtype"></changeimgtaskeditfrom>
                <span slot="title" style="height: 0px;"></span>
            </el-drawer>
        </div>
        <!--查看上传文件并打分----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-drawer title="查看成果文件" :visible.sync="successfileshow" direction="ltr" :wrapperClosable="true"
            :close-on-press-escape="false" size="960px">
            <microvediotaskuploadsuccessfilesocre v-if="successfileshow" ref="microvediotaskuploadsuccessfilesocre"
                :rowinfo="selectRowKey" :islook="islook" style="height: 92%;width:'960px'">
            </microvediotaskuploadsuccessfilesocre>
            <div class="drawer-footer">
                <el-button @click="successfileshow = false">关 闭</el-button>
            </div>
        </el-drawer>
        <!--查看上传附件---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="查看参考" :visible.sync="viewReference" width="60%" :close-on-click-modal="true"
            element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <microvediotaskuploadfile v-if="viewReference" ref="microvediotaskuploadfile" :rowinfo="selectRowKey">
            </microvediotaskuploadfile>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReference = false">关 闭</el-button>
                </span>
            </template>
        </el-dialog>
        <!--查看独立备注------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="查看备注" :visible.sync="viewReferenceRemark" width="60%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <microVedioTaskRemark v-if="viewReferenceRemark" ref="shootingTaskRemark" :rowinfo="selectRowKey"
                :islook="islook"></microVedioTaskRemark>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReferenceRemark = false">关 闭</el-button>
                    <my-confirm-button type="submit" :loading="shootingTaskRemarkrawer" @click="sumbitshootingTaskRemark"
                        v-show="!islook" />
                </span>
            </template>
        </el-dialog>
        <!--加急审核------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="加急审核" :visible.sync="taskUrgencyAproved" width="20%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <div style="vertical-align: middle; margin-top: 20px;margin-left: 80px;">
                <el-radio v-model="taskUrgencyStatus" label="1" border>同意</el-radio>
                <el-radio v-model="taskUrgencyStatus" label="9" border>驳回</el-radio>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="(taskUrgencyAproved = false)">取 消</el-button>
                    <my-confirm-button type="submit" @click="taskUrgencyApp" />
                </span>
            </template>
        </el-dialog>
        <el-drawer title="物流跟踪" :visible.sync="drawervisible" direction="rtl" :append-to-body="true">
            <logistics ref="logistics"></logistics>
        </el-drawer>
        <!--下单发货------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="下单发货" :visible.sync="dialogAddOrderVisible" v-loading="dialogAddOrderLoading" width="50%"
            v-dialogDrag :close-on-click-modal="false">
            <shootorderdown ref="packdesginorderdown" :warehouselist="warehouselist" :selids="selids" :orderType="2"
                :closedlg="closedlg" v-if="dialogAddOrderVisible"></shootorderdown>
            <template #footer>
                <span class="dialog-footer">
                    <span
                        style="font-size:10px;color:red;">点击提交按钮将发起钉钉审批，自提单审批通过即代表到样，非自提单审批通过则自动同步订单到聚水潭。&nbsp;&nbsp;</span>
                    <el-button @click="dialogAddOrderVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :loading="dialogAddOrderSubmitLoding" @click="onAddOrderSave"> 提交
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>
        <!--下单记录------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="任务下单记录" :visible.sync="dialogOrderDtlVisible" width='88%' v-dialogDrag
            :close-on-click-modal="false">
            <shootorderrecord ref="packdesginorderrecord" :orderType="2" :taskid="selectRowKey"
                v-if="dialogOrderDtlVisible"></shootorderrecord>
        </el-dialog>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import changeimgtaskfilter from '@/views/media/shooting/changeImg/new/changeimgtaskfilter';
import changeimgtaskaddfrom from '@/views/media/shooting/changeImg/new/changeimgtaskaddfrom';
import changeimgtaskeditfrom from '@/views/media/shooting/changeImg/new/changeimgtaskeditfrom';
import changeimgtaskTable from '@/views/media/shooting/changeImg/new/changeimgtaskTable'
import microVedioTaskRemark from '@/views/media/shooting/changeImg/changeimgTaskRemark'
import microvediotaskuploadfile from '@/views/media/shooting/changeImg/changeimgtaskuploadfile'
import microvediotaskuploadsuccessfilesocre from '@/views/media/shooting/changeImg/changeimgtaskuploadsuccessfilesocre'

import { pageChangeImgCaclTaskAsync, getHistoryChangeImgTaskInfo } from '@/api/media/shootingset';
import {
    pageShootingViewTaskAsync, taskRestartActionAsync,
    signShootingTaskActionAsync, unSignShootingTaskActionAsync,
    deleteShootingTaskActionAsync, deleteTaskActionAsync, batchConfrimShootingTaskAsync,
    endShootingTaskActionAsync, endRestartActionAsync,
    taskOverActionsAsync, taskShopActionAsync,
    unCaclShootingTaskActionAsync, caclShootingTaskActionAsync
    , shootUrgencyCilckAsync,getUserRoleList
} from '@/api/media/changeimg';
import shootorderdown from '@/views/media/shooting/shared/shootorderdown'
import shootorderrecord from '@/views/media/shooting/shared/shootorderrecord'
import logistics from '@/components/Comm/logistics'


export default {
    components: {
        MyContainer, MyConfirmButton, changeimgtaskfilter, changeimgtaskTable, changeimgtaskaddfrom, changeimgtaskeditfrom
        , microVedioTaskRemark, microvediotaskuploadfile, microvediotaskuploadsuccessfilesocre, logistics
        , shootorderrecord, shootorderdown
    },
    props: {
        taskUrgencyList: { type: Array, default: [] }, //平台
        platformList: { type: Array, default: [] }, //平台
        warehouselist: { type: Array, default: [] }, //仓库 
        dockingPeopleList: { type: Array, default: [] }, //对接人
        fpPhotoLqNameList: { type: Array, default: [] }, //分配查询
        groupList: { type: Array, default: [] }, //运营组
        role: { type: String, default: "tz" },//用户角色 
        islook: { type: Boolean, default: true }, //平台
        filter: { type: Object, default: { isShop: 0, isdel: 0, isComplate: 0 } },//
        tabkey: { type: String, default: "packdesgintask" }, //平台 
        listtype: { type: Number, default: 1 },
        versionId: { type: String, default: "0" },
    },
    data() {
        return {
            that: this,
            pageLoading: false,
            //搜索框
            seafilter: {},
            //列表相关
            selids: [],
            tasklist: [],
            // tableCols: tableCols,
            total: 0,
            summaryarry: {},
            pager: {},
            sels: [], // 列表选中列
            listLoading: false,
            //编辑新增 
            addTask: false,
            editTask: false,
            addLoading: false,
            //编辑新增
            //审核相关
            taskUrgencyAproved: false,
            taskUrgencyStatus: "1",
            selmicroVedioTaskId: 0,
            //审核end
            selectRowKey: 0,
            //打分相关
            successfileshow: false,
            //打分相关end
            viewReferenceRemark: false,
            shootingTaskRemarkrawer: false,
            //下单记录
            dialogAddOrderLoading: false,
            dialogAddOrderVisible: false,
            dialogHisVisible: false,
            dialogAddOrderSubmitLoding: false,
            dialogOrderDtlVisible: false,

            viewReference: false,
            onCommand: 'a',

            drawervisible: false
        };
    },
    async mounted() {
        this.seafilter = this.filter;
        await this.onSearch();
        await this.getrole();
        if (this.role == "b") { 
            await this.ShowHideonSearch("b");
        }
    },
    methods: {
        async getrole() {
            var res = await getUserRoleList();
            if (res?.success) {
                if (res.data == null) {
                    this.role = "tz";
                } else if (res.data.indexOf("视觉部经理") > -1) {
                    this.role = "b";
                }

            } else {
                this.role = "tz";
            }

        },
        closedlg() {
            this.dialogAddOrderVisible = false;
            this.dialogAddOrderLoading = false;
        },
        //导出事件
        async onExport(type) {
            this.$refs.packDesgintask.exportData("日常改图", type);
        },
        handleClose() {
            this.$confirm("是否确定关闭窗口", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.addTask = false;
            });
        },
        async topSearch(searchfilter) {
            this.seafilter = {};
            this.seafilter = {
                ...searchfilter,
                ...this.filter
            }
            await this.onSearch();
        },
        //查询
        async onSearch() {
            this.$refs.pager.setPage(1);
            this.getTaskList();
            this.selids = [];
        },
        //刷新当前页
        async onRefresh() {
            await this.getTaskList();
        },
        //获取数据
        async getTaskList() {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.seafilter
            };
            this.listLoading = true;
            let res;
            if (this.listtype == 3) {
                if (this.versionId != "0") {
                    res = await getHistoryChangeImgTaskInfo(params);
                } else {
                    res = await pageChangeImgCaclTaskAsync(params);
                }
            } else {
                res = await pageShootingViewTaskAsync(params);
            }
            this.listLoading = false;
            if (res?.success) {
                this.total = res.data.total
                this.tasklist = res.data.list;
                this.summaryarry = res.data.summary;
            }
        },
        //列表排序
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async selectchangeevent(records) {
            this.selids = [];
            records.forEach(f => {
                this.selids.push(f.changeImgTaskId);
            })
        },
        async rowChange(row) {
            await this.editTaskAction(row);
        },
        //紧急程度按钮点击
        async shootUrgencyCilck(row) {
            if (this.islook) return;
            var that = this;
            switch (row.taskUrgencyName) {
                //申请加急
                case "正常":
                    this.$confirm("是否确认加急?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }).then(async () => {
                        var res = await shootUrgencyCilckAsync({ taskid: row.id, index: 0 });
                        if (res?.success) {
                            that.$message({ message: '操作成功', type: "success" });
                            await that.onRefresh();
                        }
                    });
                    break;
                //确认加急
                case "审核":
                    this.selmicroVedioTaskId = row.id;
                    this.taskUrgencyAproved = true;
                    break;
            }
        },
        async taskUrgencyApp() {
            var res = await shootUrgencyCilckAsync({ taskid: this.selmicroVedioTaskId, index: this.taskUrgencyStatus });
            if (res?.success) {
                this.taskUrgencyAproved = false;
                this.$message({ message: '操作成功', type: "success" });
                await this.onRefresh();
            }
        },
        //显示-默认 b默认。a全部
          ShowHideonSearch(com) { 
            this.listLoading = true;
            var checkedColumnsFora = [];
            switch (com) {
                //显示全部 ,部门经理，超管
                case "a":
                    checkedColumnsFora = []; 
                    break;
                //显示默认
                case "b":
                    // checkedColumnsFora = [];
                    checkedColumnsFora =
                        ['Divisionline', 'cankao'
                            , 'photoDaysStr', 'photoOverTimeStr'
                            , 'vedioDaysStr', 'vedioOverTimeStr', 'vedioConfirmNameStr', 'vedioConfirmTimeStr'
                            , 'microDetailDaysStr', 'microDetailOverTimeStr', 'microDetailVedioCounts', 'microDetailConfirmNameStr'
                            , 'microDetailConfirmTimeStr'
                            , 'detailDaysStr', 'detailOverTimeStr', 'detailConfirmNameStr', 'detailConfirmTimeStr'
                            , 'modelPhotosDaysStr', 'modelPhotosOverTimeStr', 'modelPhotoCounts'
                            , 'modelVideoDaysStr', 'modelVideoOverTimeStr', 'modelVedioCounts'
                            , 'productID'
                            ,'applyTimeDays','applyTimeStr'
                            , 'arrivalTimeDays', 'deliverTimeStr', 'deliverTimeDays',]; 
                    break; 
                default:
                    break;
            } 
            this.$refs.packDesgintask.ShowHidenColums(checkedColumnsFora);
            this.listLoading = false;
        },
        //新增任务
        async onAddTask() {
            this.addTask = true;
        },
        //编辑任务
        async editTaskAction(row) {
            this.addLoading = true;
            this.editTask = true;
            await this.$nextTick(function () {
                this.$refs.changeimgtaskeditfrom.editTask(row);
            });
            this.addLoading = false;
        },
        summaryClick() {

        },
        //新增编辑窗口关闭
        async onCloseAddForm(type) {
            if (type == 1) {
                this.addTask = false;
            } else if (type == 2) {
                this.addTask = false;
                this.onSearch();
            } else {
                this.editTask = false;
                this.onSearch();
            }
        },
        //提交保存
        async onSubmit() {
            this.addLoading = true;
            await this.$nextTick(function () {
                this.$refs.changeimgtaskaddfrom.onSubmit();
            });
            this.addLoading = false;
        },
        //查看成果文件
        openComputOutInfo(row) {
            this.selectRowKey = row.changeImgTaskId;
            this.successfileshow = true;
        },
        //查看参考附件
        videotaskuploadfileDetal(row) {
            this.selectRowKey = row.changeImgTaskId;
            this.viewReference = true;
        },
        //查看详情备注页
        openTaskRmarkInfo(row) {
            this.selectRowKey = row.changeImgTaskId;
            this.viewReferenceRemark = true;
        },
        //独立备注提交-代码
        async sumbitshootingTaskRemark() {
            this.shootingTaskRemarkrawer = true;
            await this.$refs.shootingTaskRemark.onsubmit();
            this.shootingTaskRemarkrawer = false;
        },
        //下单
        async onAddOrder() {
            if (this.selids.length <= 0) {
                this.$message({ message: '请勾选任务', type: "warning" });
                return;
            }
            this.dialogAddOrderVisible = true;
        },
        //下单记录
        async onShowOrderDtl(row) {
            this.selectRowKey = row.changeImgTaskId;
            this.dialogOrderDtlVisible = true;
        },

        async onAddOrderSave() {
            this.dialogAddOrderSubmitLoding = true;
            await this.$nextTick(function () {
                this.$refs.packdesginorderdown.onAddOrderSave();
            });
            this.dialogAddOrderSubmitLoding = false;
        },
        async onShowExproessHttp(row) {
            this.drawervisible = true;
            let expressNo = row.expressNo;
            if (!expressNo) {
                expressNo = row.orderNoInner;
            }
            this.$nextTick(function () {
                this.$refs.logistics.showlogistics("", expressNo);
            })
        },
        //批量确认
        async batchConfirmTask(command) {
            if (this.selids.length == 0 && command != 'x') {
                this.$message({ type: 'warning', message: "请选择任务" });
                return;
            }
            switch (command) {
                 //视频批量确认
                 case 'm':
                    await this.batchConfrimShootingTask(this.selids, 2)
                    break;
                 //美工批量确认
                 case 'n':
                    await this.batchConfrimShootingTask(this.selids, 4)
                    break;
            }
        },
        //批量操作操作事件
        async handleCommand(command) {
            if (this.selids.length == 0 && command != 'x') {
                this.$message({ type: 'warning', message: "请选择任务" });
                return;
            }
            switch (command) {
                //批量完成
                case 'a':
                    await this.onTaskOverActionShared(this.selids)
                    break;
                //批量终止重启
                case 'b':
                    await this.onEndRestartActionAsyncShared(this.selids);
                    break;
                //批量终止
                case 'c':
                    await this.OnendShootingTaskAction(this.selids)
                    break;
                //批量标记
                case 'd':
                    await this.onSignShootingTaskActionShared(this.selids)
                    break;
                //取消标记
                case 'e':
                    await this.onUnSignShootingTaskActionShared(this.selids)
                    break;

                //批量存档
                case 'k':
                    await this.onTaskShopActionShared(this.selids)
                    break;
                //取消存档
                case 'l':
                    await this.taskRestartActionAsyncShared(this.selids)
                    break;
                //批量删除
                case 'f':
                    await this.deleteTaskActionShared(this.selids)
                    break;
                //批量统计
                case 'g':
                    await this.caclShootingTaskActionAsyncShared(this.selids)
                    break;
                //移除统计
                case 'h':
                    await this.unCaclShootingTaskActionAsyncShared(this.selids)
                    break;

                //彻底删除
                case 'i':
                    await this.dropTaskActionShared(this.selids)
                    break;
                //批量回收
                case 'j':
                    await this.taskRestartActionAsyncShared(this.selids)
                    break;

            }
        },
        //批量统计
        async caclShootingTaskActionAsyncShared(array) {
            this.$confirm("选中的任务将进行统计，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                debugger
                var res = await caclShootingTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.selids = [];
                    await this.onRefresh();
                }
            });
        },
        //移出统计
        async unCaclShootingTaskActionAsyncShared(array) {
            this.$confirm("选中的任务移出统计列表，是否确定", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await unCaclShootingTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.selids = [];
                    await this.onRefresh();
                }
            });
        },
        //完成操作   
        async onTaskOverActionShared(array) {
            this.$confirm("选中的任务移动完成列表，是否确定完成", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await taskOverActionsAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.selids = [];
                    await this.onRefresh();
                }
            });
        },
        //存档操作 
        async onTaskShopActionShared(array) {
            this.$confirm("选中的任务移动到存档列表，是否确定存档", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await taskShopActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //重新启动
        async onEndRestartActionAsyncShared(array) {
            this.$confirm("选中的终止任务会重新启动，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await endRestartActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //批量标记
        async onSignShootingTaskActionShared(array) {
            this.$confirm("选中的任务将会进行标记，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await signShootingTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //取消标记
        async onUnSignShootingTaskActionShared(array) {
            this.$confirm("选中的任务将会取消标记，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await unSignShootingTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //删除操作
        async deleteTaskActionShared(array) {
            this.$confirm("任务会移动到回收站，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await deleteShootingTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //批量确认操作
        async batchConfrimShootingTask(array, index) {
            this.$confirm("选中的任务将会进行确认，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await batchConfrimShootingTaskAsync({ changeImgTaskIds: array, index: index });
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //批量终止
        async OnendShootingTaskAction(array) {
            this.$confirm("选中任务将会终止，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await endShootingTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //任务批量重启
        async taskRestartActionAsyncShared(array) {
            this.$confirm("选中的任务将重启，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await taskRestartActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.selids = [];
                    await this.onRefresh();
                }
            });
        },
        //回收站彻底删除操作
        async dropTaskActionShared(array) {
            this.$confirm("选中的任务会彻底删除，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await deleteTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.dialog1 ::v-deep .el-dialog__body {
    padding: 0 !important;
    // background-color: red;
}

.dialog1 ::v-deep .el-drawer__header {
    display: none !important;
}

::v-deep .myheader {
    padding: 5px 0px 0px 5px;
}
</style>
