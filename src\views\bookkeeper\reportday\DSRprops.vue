<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="ListInfo.dateItem" type="date" placeholder="选择日期"
                    :picker-options="IndexPickerOptions" class="publicCss" @change="changeTime" />
                <el-select v-model="ListInfo.shopCode" placeholder="店铺" clearable class="publicCss" filterable>
                    <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                        :value="item.shopCode">
                    </el-option>
                </el-select>
                <el-select filterable v-model="ListInfo.groupId" collapse-tags clearable placeholder="运营组"
                    class="publicCss">
                    <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                    <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select filterable v-model="ListInfo.operateSpecialUserId" collapse-tags clearable placeholder="运营专员"
                    class="publicCss">
                    <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select filterable v-model="ListInfo.operateAssistantUserId" collapse-tags clearable
                    placeholder="运营助理" class="publicCss">
                    <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.standbyUserId" placeholder="备用" clearable class="publicCss" filterable>
                    <el-option v-for="item in spareList" :key="item.value" :label="item.value"
                        :value="item.key"></el-option>
                </el-select>
                <el-select v-model="ListInfo.scoreLevel" placeholder="店铺评价分" clearable class="publicCss" collapse-tags>
                    <el-option v-for="item in scoreList" :key="item.label" :label="item.label" :value="item.label">
                    </el-option>
                </el-select>
                <el-input v-model="ListInfo.seriesName" placeholder="系列编码" clearable maxlength="50"
                    class="publicCss"></el-input>
                <el-input v-model="ListInfo.title" placeholder="标题" clearable maxlength="50"
                    class="publicCss"></el-input>
                <el-select v-model="ListInfo.status" placeholder="状态" clearable filterable class="publicCss">
                    <el-option label="风险" value="风险"></el-option>
                    <el-option label="正常" value="正常"></el-option>
                </el-select>
                <div class="publicCss">
                    <inputYunhan ref="proCodes" v-model="ListInfo.proCodes" :inputt.sync="ListInfo.proCodes"
                        placeholder="产品ID" :maxRows="100" :maxlength="3500" :clearable="true" width="200px"
                        @callback="callbackProCodes" title="产品ID">
                    </inputYunhan>
                </div>
                <el-input v-model="ListInfo.bzCategory" placeholder="经营类目" clearable maxlength="50"
                    class="publicCss"></el-input>
                <el-select v-model="ListInfo.ProStatuses" placeholder="商品状态" clearable filterable multiple collapse-tags
                    class="publicCss">
                    <!-- <el-option label="删除" value="删除"></el-option> -->
                    <el-option label="上架" value="上架"></el-option>
                    <!-- <el-option label="缺货" value="缺货"></el-option> -->
                    <el-option label="下架" value="下架"></el-option>
                    <el-option label="未知" value="未知"></el-option>
                </el-select>
                <el-select v-model="ListInfo.idScoreJudge" placeholder="ID评分判定" clearable filterable multiple
                    collapse-tags class="publicCss">
                    <el-option label="优秀" value="优秀"></el-option>
                    <el-option label="普通" value="普通"></el-option>
                    <el-option label="劣质" value="劣质"></el-option>
                </el-select>
                <el-select v-model="ListInfo.seriesNameScoreJudge" placeholder="系列编码评分判定" clearable filterable multiple
                    collapse-tags class="publicCss">
                    <el-option label="优秀" value="优秀"></el-option>
                    <el-option label="普通" value="普通"></el-option>
                    <el-option label="劣质" value="劣质"></el-option>
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                </div>
            </div>
        </template>
        <vxetablebase :id="'DSRprops202408041418'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' border
            :isSelection="false" :isSelectColumn="false" v-loading="loading" style="width: 100%; margin: 0"
            :height="'100%'" />
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false"
            direction="btt">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" @change="chatSearch($event, dialogType)"
                    :picker-options="pickerOptions" style="margin: 10px;" />
                <buschar :analysisData="chatProp.data" v-if="!chatProp.chatLoading"></buschar>
            </div>
            <div v-else v-loading="chatProp.chatLoading"></div>
        </el-drawer>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop'
import { replaceSpace } from '@/utils/getCols'
import { pageGetDsrs, getTrendChart, Export } from '@/api/pddplatform/proCodeDsr'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import buschar from '@/components/Bus/buschar'
import inputYunhan from '@/components/Comm/inputYunhan.vue';

const tableCols = [
    { istrue: true, prop: 'dateItem', label: '日期', sortable: 'custom', width: '100', formatter: (row) => dayjs(row.dateItem).format('YYYY-MM-DD') },
    { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'proCode', label: '产品ID', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'title', label: '标题', sortable: 'custom', width: '180' },
    { istrue: true, prop: 'proStatus', label: '商品状态', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'seriesName', label: '系列编码', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'bzCategory', label: '经营大类', sortable: 'custom', width: '100', },
    { istrue: true, prop: 'bzCategory1', label: '一级类目', sortable: 'custom', width: '100', },
    { istrue: true, prop: 'bzCategory2', label: '二级类目', sortable: 'custom', width: '100', },
    { istrue: true, prop: 'groupId', label: '运营组', sortable: 'custom', width: '80', formatter: (row) => row.groupName },
    { istrue: true, prop: 'operateSpecialUserName', label: '运营专员', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'operateAssistantUserName', label: '运营助理', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'standbyUserName', label: '备用', sortable: 'custom', width: '70' },
    // { istrue: true, prop: 'score', label: '店铺评价分', sortable: 'custom', width: '150', formatter: (row) => row.scoreLevel ? row.score + `(${row.scoreLevel})` : row.score },
    { istrue: true, prop: 'proCodeScore', label: '店铺评价分', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'score', label: 'ID评分', sortable: 'custom', width: '90', formatter: (row) => row.scoreLevel ? row.score + `(${row.scoreLevel})` : row.score },
    {
        istrue: true, prop: 'idScoreJudge', label: 'ID评分判定', sortable: 'custom', width: '100', type: "colorClick",
        style: (that, row) => that.judgeColorFormatter(row.idScoreJudge), align: 'center',
    },
    { istrue: true, prop: 'scoreCount', label: '评价条数', sortable: 'custom', width: '80' },
    {
        istrue: true, prop: 'status', label: '状态', sortable: 'custom', width: '70', type: 'colorClick',
        style: (that, row) => that.statusFormatter(row.status)
    },
    { istrue: true, prop: 'seriesNameScore', label: '系列编码维度评分', sortable: 'custom', width: '150' },
    {
        istrue: true, prop: 'seriesNameScoreJudge', label: '系列编码评分判定', sortable: 'custom', width: '150', type: "colorClick",
        style: (that, row) => that.judgeColorFormatter(row.seriesNameScoreJudge), align: 'center',
    },
    { istrue: true, prop: 'updateTime', label: '更新时间', sortable: 'custom', width: '150' },
    {
        istrue: true, label: '', width: '70', type: 'button', btnList:
            [
                { istrue: true, label: '趋势图', handle: (that, row) => that.openDialog(row) },
            ]
    },
]

const scoreList = [
    {
        label: '高',
    },
    {
        label: '中',
    },
    {
        label: '低',
    },
]
export default {
    name: "DSRprops",
    components: {
        MyContainer, vxetablebase, buschar, inputYunhan
    },
    data() {
        return {
            tableCols,
            that: this,
            directorlist: [],
            shopList: [],
            scoreList,
            grouplist: [],
            spareList: [],
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                shopCode: null,
                groupId: null,//运营组
                operateSpecialUserId: null,//运营专员
                operateAssistantUserId: null,//运营助理
                standbyUserId: null,//备用
                seriesName: null,//系列名称
                scoreLevel: null,//店铺评价分
                title: null,//标题
                dateItem: null,//日期
                proCodes: null,
                status: null,//状态
                ProStatuses: null,//商品状态
                idScoreJudge: [],//ID评分判定
                seriesNameScoreJudge: [],//系列编码维度评分判定
            },
            total: 0,
            chatProp: {
                chatDialog: false,//趋势图弹窗
                chatTime: null,//趋势图时间
                chatLoading: true,//趋势图loading
                data: [],//趋势图数据
            },
            chatInfo: {
                proCode: null,//组别
                startDate: null,//店铺
                endDate: null,//昵称
            },
            tableData: [],
            loading: false,
            pickerOptions,
            IndexPickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                },
                shortcuts: [{
                    text: '今天',
                    onClick(picker) {
                        picker.$emit('pick', new Date());
                    }
                }, {
                    text: '昨天',
                    onClick(picker) {
                        const date = new Date();
                        date.setTime(date.getTime() - 3600 * 1000 * 24);
                        picker.$emit('pick', date);
                    }
                }, {
                    text: '一周前',
                    onClick(picker) {
                        const date = new Date();
                        date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', date);
                    }
                }]
            },
            isExport: false
        }
    },
    async mounted() {
        this.getList('init')
        this.init()
    },
    methods: {
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await Export(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', 'DSR数据' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        //状态【风险】红色展示-【正常】蓝色展示
        statusFormatter(row) {
            if (row == "风险") return "color: red";
            // let color;
            // if (row == "风险") color = "red";
            // else color = "blue";
            // const map = {
            //     "red" : "color: red",
            //     "blue" : "color: blue"
            // }
            // return map[color];
        },
        judgeColorFormatter(row) {
            if (row == "优秀") return "color: green";
            else if (row == "普通") return "color: blue";
            else if (row == "劣质") return "color: red";
        },
        async chatSearch() {
            this.chatInfo.startDate = dayjs(this.chatProp.chatTime[0]).format('YYYY-MM-DD')
            this.chatInfo.endDate = dayjs(this.chatProp.chatTime[1]).format('YYYY-MM-DD')
            this.chatProp.chatLoading = true
            const { data } = await getTrendChart(this.chatInfo)
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        async openDialog(row) {
            this.chatProp.chatDialog = true
            this.chatProp.chatTime = [dayjs().subtract(30, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
            this.chatInfo = {
                proCode: row.proCode,
                startDate: this.chatProp.chatTime[0],
                endDate: this.chatProp.chatTime[1],
            }
            this.chatProp.chatLoading = true
            const { data } = await getTrendChart(this.chatInfo)
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        changeTime(e) {
            if (e) {
                //取出日期
                this.ListInfo.dateItem = dayjs(e).format('YYYY-MM-DD')
            } else {
                this.ListInfo.dateItem = null
            }
            this.getList('changeTime')
        },
        async init() {
            const { data } = await getDirectorList();
            this.directorlist = data?.map(item => { return { value: item.key, label: item.value }; });
            const { data: data1 } = await getDirectorGroupList();
            this.grouplist = data1?.map(item => { return { value: item.key, label: item.value }; });
            const { data: data2 } = await getDirectorList({})
            this.spareList = [{ key: '0', value: '未知' }].concat(data2 || []);
            const { data: data3 } = await getAllShopList({ platforms: [2] });
            this.shopList = [];
            data3?.forEach(f => {
                if (f.isCalcSettlement && f.shopCode)
                    this.shopList.push(f);
            });
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        //查询列表
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1);
            }
            if (type == 'init') {
                if (this.ListInfo.dateItem == null && type != 'changeTime') {
                    this.ListInfo.dateItem = dayjs(new Date()).add(-1, 'day').format('YYYY-MM-DD')
                }
            }
            const replaceArr = ['seriesName', 'title']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            const { data, success } = await pageGetDsrs(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        //多行输入
        callbackProCodes(val) {
            this.ListInfo.proCodes = val;
        }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    // margin-bottom: 20px;
    flex-wrap: wrap;

    .publicCss {
        width: 200px;
        margin: 0 10px 10px 0;
    }
}
</style>
