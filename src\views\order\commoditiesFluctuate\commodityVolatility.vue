<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-switch style="display: block" v-model="ListInfo.orderByUndulate" active-color="#67C23A"
          @change="getList('search')" inactive-color="#409EFF" active-text="较前一小时波动" inactive-text="较昨天当前时段波动">
        </el-switch>
        <span style="margin-left: 1%;">商品子编码：</span>
        <el-input v-model.trim="ListInfo.skuCode" placeholder="请输入" maxlength="50" clearable style="width: 150px;" />
        <span style="margin-left: 1%;">波动率： </span>
        <el-input-number v-model="ListInfo.undulate" placeholder="请输入" :min="0" :max="9999" :controls="false"
          class="publicCss" />
        <span style="margin-right: 20px;">%</span>
        <el-button type="primary" @click="getList('search')">查询</el-button>
        <el-button style="margin-left: 30%;" type="text">
          聚水潭订单最近一次同步时间：
          {{ syncCreateTime }}
        </el-button>
      </div>
    </template>
    <div style="height: 100%;">
      <el-scrollbar style="height: 100%;width: 100%;" v-loading="loading" v-if="tableData.length > 0">
        <div style="display: flex; flex-wrap: wrap; height: 100%;">
          <div v-for="(item, index) in tableData" :key="index"
            style="border:0.75pt solid #dedede; width: 48%; padding: 1%; box-sizing: border-box;margin: 10px 1%;border-radius: 7px;">
            <div class="tendency">
              <div>
                <span class="tendency_item">商品子编码：</span>
                <span style="color: #48a7ff;" class="tendency_item_clickable" @click="onProductEncoding(item, 1)">
                  {{ item.sku }}</span>
              </div>
              <div style="margin-left: 20px;">
                <span class="tendency_item">波动率：</span>
                <span class="tendency_item">{{ item.volatility }}</span>
                <span class="tendency_item">%</span>
              </div>
              <div style="margin-left: 20px;">
                <span class="tendency_item">当前库存总量：</span>
                <span style="color: #48a7ff;" class="tendency_item_clickable" @click="onProductEncoding(item, 2)">{{
                  item.inventoryCount }}</span>
              </div>
            </div>
            <buschar v-if="item.res" :analysisData="item.res" ref="dialogMapVisible2Buscher"
              :gridStyle="{ top: '20%', left: '5%', right: '4%', bottom: '7%', containLabel: false }"
              :thisStyle="{ width: '99%', height: '260px', 'box-sizing': 'border-box', 'line-height': '360px' }">
            </buschar>
          </div>
        </div>
      </el-scrollbar>
      <div v-else style="height: 100%;width: 100%;display: flex;align-items: center;justify-content: center;"
        v-loading="loading">
        <span style="font-size: 30px;letter-spacing: 6px;"">暂无数据</span>
      </div>
    </div>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" :pageSize="20" :sizes="[10, 20, 30, 40]"
          @size-change="Sizechange" />
    </template>

    <el-dialog title="库存分析" :visible.sync="dialogMapVisible.visible" width="70%" v-dialogDrag>
      <div style="height: 600px;">
        <div class="analyzetop">
          <span class="analyzeCss">商品子编码：</span>
          <span class="analyzeCss">{{ subcodingskuCode }}</span>
          <span class="analyzeCss" style="margin-left: 30px;">库存总量：</span>
          <span class="analyzeCss">{{ totalCount }}</span>
        </div>
        <buschar v-if="dialogMapVisible.visible" ref="buschar" :analysisData="dialogMapVisible.data"
          :thisStyle="{ width: '99%', height: '550px', 'box-sizing': 'border-box', 'line-height': '460px' }">
        </buschar>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import buschar from '@/components/Bus/buschar';
import { todaySkuTimeFrame, skuInventory } from "@/api/order/orderData";
import { lastImportTime } from "@/api/order/orderData";
import middlevue from "@/store/middle.js"

export default {
  name: "commodityVolatility",
  components: {
    MyContainer, vxetablebase, buschar
  },

  data() {
    return {
      syncCreateTime: '',//最近更新时间
      subcodingskuCode: null,
      totalCount: 0,
      dialogMapVisible: { visible: false, title: "", data: {} },
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 20,
        orderBy: null,
        isAsc: false,
        orderByUndulate: true,//波动率排序
        skuCode: null,//sku编号
        undulate: undefined,//波动率
      },
      timeRanges: [],
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.init()
    await this.getList()
  },
  methods: {
    async init(){
      const { data, success } = await lastImportTime()
      if(!success) return
      this.syncCreateTime = data
    },
    onProductEncoding(item, val) {
      const params = { orderByUndulate: this.ListInfo.orderByUndulate, skuCode: item.sku, undulate: this.ListInfo.undulate }
      if (val == 1) {
        this.$nextTick(() => {
          this.$router.push({ path: '/order/commoditiesFluctuate/waveAnalysis', query: params })
          middlevue.$emit('waveAnalysis', params)
        })
      } else if (val == 2) {
        // this.$nextTick(() => {
        //   this.$router.push({ path: '/order/commoditiesFluctuate/inventoryAnalysis', query: params })
        //   middlevue.$emit('inventoryAnalysis', params)
        // })
        this.subcodingskuCode = item.sku
        this.onTotalInventory(params)
      }
    },
    async onTotalInventory(val) {
      const params = { skuCode: val.skuCode }
      const { data, success } = await skuInventory(params)
      if (success) {
        this.totalCount = data.totalCount
        let res = {
          xAxis: data.warehouses,
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '库存量',
              type: 'bar',
              emphasis: {
                focus: 'series'
              },
              data: data.orderUseQtys
            },
            {
              name: '订单占用量',
              type: 'bar',
              emphasis: {
                focus: 'series'
              },
              data: data.usableQtys
            },
            {
              name: '聚水潭可用量',
              type: 'bar',
              emphasis: {
                focus: 'series'
              },
              data: data.jstUsableQty
            },
          ]
        };
        res.series.map((item) => {
          item.itemStyle = {
            "normal": {
              "label": {
                "show": true,
                "position": "top",
                "textStyle": {
                  "fontSize": 14
                }
              }
            }
          }
          item.emphasis = {
            "focus": "series"
          }
          item.smooth = false;
        })
        this.dialogMapVisible.data = res
        this.dialogMapVisible.visible = true;
      } else {
        //获取列表失败
        this.$message.error('获取趋势图失败')
      }
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      let orderByUndulate = this.ListInfo.orderByUndulate ? 1 : 2
      const params = { ...this.ListInfo, orderByUndulate }
      if (params.undulate) {
        params.undulate = parseFloat(params.undulate)
      }
      const { data, success } = await todaySkuTimeFrame(params)
      if (success) {
        if (data.list) {
          this.tableData = [];
          setTimeout(() => {
            this.tableData = this.onTrendChart(data.list)
          }, 0)
          this.total = data.total
        } else {
          this.tableData = []
          this.total = 0
        }
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
      this.loading = false
      this.$forceUpdate()
    },
    onTrendChart(dataArray) {
      let ChartArray = dataArray.map(data => {
        let res = {
          xAxis: data.times,
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '7天前',
              type: 'bar',
              emphasis: {
                focus: 'series'
              },
              data: data.before7Counts
            },
            {
              name: '3天前',
              type: 'bar',
              emphasis: {
                focus: 'series'
              },
              data: data.before3Counts
            },
            {
              name: '昨天',
              type: 'bar',
              emphasis: {
                focus: 'series'
              },
              data: data.yesterdayCounts
            },
            {
              name: '今天',
              type: 'line',
              stack: 'Total',
              data: data.todayCounts
            },
          ]
        };
        res.series.map((item) => {
          item.itemStyle = {
            "normal": {
              "label": {
                "show": true,
                "position": "top",
                "textStyle": {
                  "fontSize": 14
                }
              }
            }
          }

          item.emphasis = {
            "focus": "series"
          }
          item.smooth = false;
        })
        if (this.ListInfo.orderByUndulate && data.todayIsMinus === 1) {
          data.volatility = '-' + data.todayUndulate.toString();
        } else if (!this.ListInfo.orderByUndulate && data.yesterdayIsMinus === 1) {
          data.volatility = '-' + data.yesterdayUndulate.toString();
        } else {
          data.volatility = this.ListInfo.orderByUndulate ? data.todayUndulate : data.yesterdayUndulate;
        }
        // 清除原对象中的相关字段
        delete data.before3Counts;
        delete data.before7Counts;
        delete data.yesterdayCounts;
        delete data.todayCounts;
        // 将res对象添加到原对象中
        data.res = res;
        return data;
      });
      return ChartArray;
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
  width: 100%;

  .publicCss {
    width: 150px;
  }
}

.tendency {
  display: flex;
  margin-bottom: 5px;

  .tendency_item,
  .tendency_item_clickable {
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(245, 240, 240, 0.5);
  }

  .tendency_item_clickable {
    cursor: pointer;
  }
}
.analyzetop{
  display: flex;
  margin: 10px 0 10px 15px;

  .analyzeCss {
    margin-right: 5px;
    font-size: 16px;
    color: #333;
    font-weight: bold;
  }
}
</style>
