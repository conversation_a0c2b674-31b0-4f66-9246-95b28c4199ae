<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item :label="(activeName == 'first' || activeName == 'second') ? '变动周期' : '变动日期'">
                    <el-date-picker style="width: 230px" v-model="filter.timerange" type="datetimerange"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" :clearable="false"></el-date-picker>
                </el-form-item>
                <el-form-item label="变动日期" v-show="activeName == 'second'">
                    <el-date-picker style="width: 230px" v-model="filter.timerange1" type="datetimerange"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" :clearable="false"></el-date-picker>
                </el-form-item>
                <el-form-item label="商品编码:">
                    <!-- <el-input v-model.trim="filter.goodsCode" maxlength="100" placeholder="商品编码" style="width: 150px"
                        @keyup.enter.native="onSearch" clearable /> -->
                    <inputYunhan ref="productCode" :inputt.sync="filter.goodsCode" v-model="filter.goodsCode"
                        width="160px" placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true"
                        :maxRows="300" :maxlength="6000" @callback="goodsCodeCallback" title="商品编码">
                    </inputYunhan>
                </el-form-item>
                <el-form-item label="采购单号:">
                    <el-input v-model.trim="filter.buyNo" maxlength="100" placeholder="采购单号" style="width: 150px"
                        @keyup.enter.native="onSearch" clearable />
                </el-form-item>
                <!-- <el-form-item label="Erp编号:">
                    <el-input v-model.trim="filter.indexNo" maxlength="100" placeholder="Erp编号" style="width: 150px"
                        @keyup.enter.native="onSearch" clearable />
                </el-form-item> -->
                <el-form-item label="分公司:">
                    <el-select filterable v-model="filter.companys" @change="changeSetCompany" multiple collapse-tags
                        clearable placeholder="分公司" style="width: 140px">
                        <el-option key="义乌" label="义乌" value="义乌"></el-option>
                        <el-option key="南昌" label="南昌" value="南昌"></el-option>
                        <el-option key="武汉" label="武汉" value="武汉"></el-option>
                        <el-option key="深圳" label="深圳" value="深圳"></el-option>
                        <el-option key="其他" label="其他" value="其他"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="岗位:" v-if="activeName == 'first'">
                    <el-select v-model="filter.titleNames" clearable filterable multiple collapse-tags
                        placeholder="请选择岗位" style="width: 153px">
                        <el-option v-for="item in positionList" :key="item.titleName" :label="item.titleName"
                            :value="item.titleName" />
                    </el-select>
                </el-form-item>
                <el-form-item label="采购员:">
                    <el-select v-model="filter.brandIds" clearable filterable multiple collapse-tags
                        placeholder="请选择采购员" style="width: 153px">
                        <el-option v-for="item in brandlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="架构:" v-if="activeName == 'first'">
                    <el-select v-model="filter.deptId" clearable filterable multiple collapse-tags placeholder="请选择架构"
                        style="width: 153px">
                        <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否调拨:" v-if="activeName == 'fourth'">
                    <el-select v-model="filter.isAllot" clearable placeholder="是否调拨" style="width: 160px">
                        <el-option label="是" :value="true" />
                        <el-option label="否" :value="false" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch('search')">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <el-tabs v-model="activeName" style="height: 94%;" @tab-click="handoverEvent">
            <el-tab-pane label="采购汇总" name="first" style="height: 100%;">
                <goodsCostPriceChgSum ref="goodsCostPriceChgSum" :filter="filter" :lastUpdateTime="lastUpdateTimee">
                </goodsCostPriceChgSum>
            </el-tab-pane>
            <el-tab-pane label="合计数据" name="second" style="height: 100%;">
                <goodsCostPriceChgCode ref="goodsCostPriceChgCode" :filter="filter" :lastUpdateTime="lastUpdateTimee">
                </goodsCostPriceChgCode>
            </el-tab-pane>
            <el-tab-pane label="基础数据" name="fourth" style="height: 100%;">
                <goodsCostPriceChg ref="goodsCostPriceChg" :filter="filter" :lastUpdateTime="lastUpdateTimee">
                </goodsCostPriceChg>
            </el-tab-pane>
            <el-tab-pane label="核价领取" name="thirdly" v-if="checkPermission('goodsCostPriceApprover')"
                style="height: 100%;">
                <goodsCostPriceApprover ref="goodsCostPriceApprover" :filter="filter"></goodsCostPriceApprover>
            </el-tab-pane>
        </el-tabs>

    </container>
</template>

<script>
import { formatTime, getFirstDayOfMonth } from "@/utils";
import dayjs from "dayjs";
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { getAllProBrand, getBianManPositionListV2 } from '@/api/inventory/warehouse'
import goodsCostPriceChg from "./goodsCostPriceChg.vue"
import goodsCostPriceChgSum from "./goodsCostPriceChgSum.vue"
import goodsCostPriceChgCode from "./goodsCostPriceChgCode.vue"
import goodsCostPriceApprover from "./goodsCostPriceApprover.vue"
import { getCostPriceLastUpdateTime } from "@/api/inventory/basicgoods"
import { getPurchaseIncreaseDecreasePriceDeptList } from '@/api/inventory/purchaseordernew'
import inputYunhan from "@/components/Comm/inputYunhan";
// const startDate = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const startDate = getFirstDayOfMonth();
const endDate = formatTime(new Date(), "YYYY-MM-DD");


export default {
    name: 'YunHanAdminGoodsCostPriceTab',
    components: { container, cesTable, MyConfirmButton, goodsCostPriceChg, goodsCostPriceChgSum, goodsCostPriceChgCode, goodsCostPriceApprover, inputYunhan },
    data() {
        return {
            lastUpdateTimee: '',
            that: this,
            activeName: 'first',
            filter: {
                companys: [],
                titleNames: [],
                brandIds: [],
                deptId: [],
                brandId: null,
                goodsCode: null,
                timerange: [startDate, endDate],
                startTime: null,
                endTime: null,
                titleName: null,
                timerange1: [],
            },
            purchasegrouplist: [],
            brandlist: [],
            brandlist1: [],
            positionList: [],
            pageLoading: false,
            showtype: 1
        };
    },

    async mounted() {
        await this.init();
        await this.onSearch();
    },

    methods: {
        goodsCodeCallback(val) {
            this.filter.goodsCode = val;
        },
        //切换tab
        handoverEvent() {
            this.filter.deptId = [];
        },
        async init() {
            var res2 = await getAllProBrand();
            this.brandlist1 = res2.data;
            this.brandlist = res2.data.map(item => {
                return { value: item.key, label: item.value };
            });
            var resPosition = await getBianManPositionListV2();
            this.positionList = resPosition?.data;
            const { data } = await getCostPriceLastUpdateTime();
            this.lastUpdateTimee = "最晚更新时间:" + data
            //采购组
            let { data: deptList, success } = await getPurchaseIncreaseDecreasePriceDeptList();
            if (success) {
                this.purchasegrouplist = deptList.map(item => { return { value: item.dept_id, label: item.full_name }; });
            }
        },
        async changeSetCompany() {
            if (this.filter.companys.includes('义乌') || this.filter.companys.includes('南昌')) {
                this.brandlist = this.brandlist1.filter(f => this.filter.companys.includes(f.company)).map(item => {
                    return { value: item.key, label: item.value };
                });
            } else if (this.filter.companys.includes('其他')) {
                this.brandlist = this.brandlist1.filter(f => !['南昌', '义乌'].includes(f.company)).map(item => {
                    return { value: item.key, label: item.value };
                });
            } else {
                this.brandlist = this.brandlist1.map(item => {
                    return { value: item.key, label: item.value };
                });
            }
            this.filter.brandId = null;
            this.filter.brandIds = [];
        },
        async onSearch(type) {
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            } else {
                this.filter.startTime = null;
                this.filter.endTime = null;
            }
            if (this.filter.timerange1 && this.filter.timerange1.length > 1 && this.activeName == 'second') {
                this.filter.targetStartDate = this.filter.timerange1[0];
                this.filter.targetEndDate = this.filter.timerange1[1];
            } else {
                this.filter.targetStartDate = null;
                this.filter.targetEndDate = null;
            }
            if (this.activeName == 'first') this.$refs.goodsCostPriceChgSum.onSearch();
            if (this.activeName == 'second') this.$refs.goodsCostPriceChgCode.onSearch(type);
            if (this.activeName == 'fourth') this.$refs.goodsCostPriceChg.onSearch();
            if (this.activeName == 'thirdly') this.$refs.goodsCostPriceApprover.onSearch();
        },
    },
};
</script>

<style lang="scss" scoped>
//控制选择器多选-标签宽度
::v-deep .el-select__tags-text {
    max-width: 30px;
}
</style>
