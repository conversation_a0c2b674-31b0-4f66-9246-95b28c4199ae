<template>
    <MyContainer>
   
       <ces-table ref="table" :that='that' :isIndex='true'
                :hasexpand='false' @sortchange='sortchange' :tableData='tableData'
                @select='selectchange' :isSelection='false' :showsummary='true' :tablefixed='true' 
           :tableCols='tableCols' :loading="listLoading">
        <el-table-column type="expand">
          <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
        </el-table-column>
         <template slot='extentbtn'>
              <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                  <el-select v-model="ListInfo.platforms" placeholder="平台" clearable filterable class="publicCss" multiple 
            collapse-tags>
            <el-option label="天猫" :value="1" />
            <el-option label="拼多多" :value="2" />
            <el-option label="阿里巴巴" :value="4" />
            <el-option label="抖音" :value="6" />
            <el-option label="京东" :value="7" />
            <el-option label="淘工厂" :value="8" />
            <el-option label="淘宝" :value="9" />
            <el-option label="希音" :value="12" />
            <el-option label="拼多多跨境" :value="13" />
            <el-option label="快手" :value="14" />

          </el-select>
                </el-button>
          <el-button style="padding: 0;margin: 0;">
                  <el-select v-model="ListInfo.isClacToDayReport" placeholder="是否计算到日报" clearable filterable class="publicCss"  
            collapse-tags>
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                  <el-select v-model="ListInfo.hasOrder" placeholder="是否有订单" clearable filterable class="publicCss"  
            collapse-tags>
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                  <el-input v-model.trim="ListInfo.accountType" placeholder="账单项目" maxlength="50" clearable class="publicCss" />
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                  <el-input v-model.trim="ListInfo.billTypes" placeholder="ERP账务类型" maxlength="50" clearable class="publicCss" />
                </el-button>
              <el-button type="primary" @click="getList('search')">查询</el-button>
       
            </el-button-group>
          </template>
      </ces-table>
      <!--分页-->
      <template #footer>
        <my-pagination
          ref="pager"
          :total="total"
     
        />
      </template>

      <el-drawer title="编辑" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editVisible" 
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
       <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
       <div class="drawer-footer">
        <el-button @click.native="editVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editLoading" @click="onEditSubmit" />
      </div>
    </el-drawer>

      <el-dialog title="日志明细" :visible.sync="detailPopupDialog" width="70%" v-dialogDrag>
        <div style="height: 500px;" v-loading="detailloading">
          <billingChargesUnClacNewLog :detailArgument="detailArgument" v-if="detailPopupDialog" />
        </div>
      </el-dialog>
    </MyContainer>
  </template>

  <script>
  import MyContainer from "@/components/my-container";
  import cesTable from "@/components/Table/table.vue";

  import { formatPlatform } from '@/utils/tools'
  import { pageUnClacChargeDealRule,editClacChargeDealRule } from '@/api/bookkeeper/reportdayV2'
  import billingChargesUnClacNewLog from './billingChargesUnClacNewLog.vue'

import MyConfirmButton from '@/components/my-confirm-button'
  const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'platform', label: '平台', formatter: (row) => formatPlatform(row.platform), },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'vauleRule', label: '取值规则', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'accountType', label: '账单项目', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'isClacToDayReport', label: '是否计算到日报', formatter: (row) => row.isClacToDayReport == 1 ? "是":"否" },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'billType', label: 'ERP账务类型', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'hasOrder', label: '是否有订单', formatter: (row) => row.hasOrder == 1 ? "是":"否" },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'remark', label: '备注', },
    {istrue: true,type: "button",label:'操作',width: "120",btnList: [{ label: "编辑", handle: (that, row) => that.EditButton(row)},
    { label: "查看日志", handle: (that, row) => that.showLog(row)}
    ]}

  ]
  export default {
    name: "billingChargesUnClacNew",
    components: {
      MyContainer, cesTable,MyConfirmButton,billingChargesUnClacNewLog
    },
    data() {
      return {
        detailloading: false,
        detailArgument: {},
        detailName: true,
        detailPopupDialog: false,
        that: this,
        ListInfo: {
          currentPage: 1,
          pageSize: 50,
          orderBy: null,
          isAsc: false,
          platforms: [],//平台
          billTypes: null,//账单类型
        },
        timeRanges: [],
        tableCols,
        tableData: [],
        summaryarry: {},
        total: 0,
        loading: false,
        autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
        },
        editVisible:false,

      }
    },
    async mounted() {
    await this.initform()

      await this.getList()
    },
    methods: {
      showLog(row){
      
        this.detailArgument = {}
        this.detailArgument = {
          id: row.id,
          
        }
        this.detailPopupDialog = true
      },
      EditButton(row){
      this.editVisible = true
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields()
          this.$nextTick(async() =>{
         await this.autoform.fApi.setValue(row)
      })
        },
        async onEditSubmit() {
      this.editLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          const res = await editClacChargeDealRule(formData);
          if(res.code==1){
            this.$message.success('修改成功！');
            this.editVisible=false;       
            this.getList() 
          }
        }else{}
     })
     this.editLoading=false;
     
    },
     
      async initform(){
       this.autoform.rule= [{type:'hidden',field:'id',title:'id',value: ''},
                     {type:'input',field:'accountType',title:'账单项目',value: '',col:{span:6}},
                     {type:'select',field:'isClacToDayReport',title:'是否计算到日报', options: [{ value: 1, label: '是', }, { value: 0, label: '否', }], props: { clearable: true },col:{span:6}},
                     {type:'input',field:'billType',title:'ERP账务类型',value: '',col:{span:6}},
                     {type:'select',field:'hasOrder',title:'是否有订单', options: [{ value: 1, label: '是', }, { value: 0, label: '否', }], props: { clearable: true },col:{span:6}},
                     {type:'input',field:'remark',title:'备注', props: { type:'textarea'},value: '',col:{span:22}},
                    ]
                  
    },
      //获取列表
      async getList(type) {
        if (type == 'search') {
          this.ListInfo.currentPage = 1
          this.$refs.pager.setPage(1)
        }
     
        this.loading = true
        const { data, success } = await pageUnClacChargeDealRule(this.ListInfo)
        if (success) {
          this.tableData = data.list
          this.total = data.total
          this.loading = false
        } else {
          //获取列表失败
          this.$message.error('获取列表失败')
        }
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }
  </script>

  <style scoped lang="scss">
  .top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
      width: 200px;
      margin: 0 5px 5px 0;
    }
  }

  ::v-deep .el-select__tags-text {
    max-width: 55px;
  }
  </style>
