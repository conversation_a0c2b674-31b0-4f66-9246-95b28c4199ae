<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>

                <el-button style="padding: 0;margin: 0;">
                    <inputYunhan ref="orderNo" v-model.trim="ListInfo.OrderNos" :inputt.sync="ListInfo.OrderNos"
                        placeholder="订单号/若输入多条请按回车" :maxRows="3000" :maxlength="90000" :clearable="true"
                        @callback="callbackProCode" title="订单号">
                    </inputYunhan>
                </el-button>

                <el-button style="padding: 0;border: none;float: left;">
                    <inputYunhan :key="'1'" :keys="'one'" :width="'220px'" ref="" :inputt.sync="ListInfo.goodsCodes"
                        v-model.trim="ListInfo.goodsCodes" placeholder="商品编码/若输入多条请按回车" :clearable="true"
                        @callback="callbackGoodsCode" title="商品编码" @entersearch="entersearch" :maxRows="100">
                    </inputYunhan>
                </el-button>

                <el-button type="primary" @click="getList('search')">搜索</el-button>
            </div>
        </template>
        <vxetablebase :id="'billingChargeTemu202408041401'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
            style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
            <div style="display: flex; align-items: baseline; height: 75px;margin-top: 10px;">
                <el-date-picker style="width: 150px; margin-right: 10px;" v-model="yearMonthDay" type="date"
                    placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
                </el-date-picker>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                    :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                </el-upload>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getTemu_BantuoDayReportChangeLogList } from '@/api/bookkeeper/crossBorderV2'
import inputYunhan from "@/components/Comm/inputYunhan";
import { formatTime } from "@/utils";
const tableCols = [
    {
        sortable: 'custom', width: 'auto', align: 'center', width: '100', prop: 'yearMonthDayDateTime', label: '日报日期', formatter: (row) => {
            return row.yearMonthDayDateTime ? formatTime(row.yearMonthDayDateTime, "YYYY-MM-DD") : "";
        },
    },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: ' 订单号', width: '120' ,},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', width: '120' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'computeTime', label: '计算时间', width: '120' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'dK2', label: '变更前-运费收入', width: '120' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'changeDK2', label: '变更后-运费收入', width: '120' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'dK5', label: '变更前-发货面单费', width: '130' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'changeDK5', label: '变更后-发货面单费', width: '130' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressFee', label: '变更前-运输费', width: '130' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'changeExpressFee', label: '变更后-运输费', width: '120' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'operatingCost', label: '变更前-操作费', width: '120' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'dK6', label: '变更前-暂估运费收入', width: '150' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'changeDK6', label: '变更后-暂估运费收入', width: '150' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'estimateDK5', label: '变更前-预估发货面单费', width: '150' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'changeEstimateDK5', label: '变更后-预估发货面单费', width: '150' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'estimateExpressFee', label: ' 变更前-预估运输费', width: '130' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'changeEstimateExpressFee', label: ' 变更后-预估运输费', width: '130' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'estimateOperatingCost', label: '变更前-预估操作费', width: '130' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'changeEstimateOperatingCost', label: '变更后-预估操作费', width: '130' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'estimateExtAmount9', label: '变更前-预估其他', width: '120' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'changeEstimateExtAmount9', label: '变更后-预估其他', width: '120' },

]
export default {
    name: "refundAfterSale",
    components: {
        MyContainer, vxetablebase, inputYunhan
    },
    data() {
        return {
            timeRanges: [],//时间范围
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startComputeTime: null,//开始时间
                endComputeTime: null,//结束时间
                OrderNos: null,
                goodsCodes: null,
            },
            tableCols,
            tableData: [],
            summaryarry: {},
            total: 0,
            loading: false,
            pickerOptions,
        }
    },
    async mounted() {
        await this.getList()

    },
    methods: {
        async changeTime(e) {
            this.ListInfo.startComputeTime = e ? e[0] : null
            this.ListInfo.endComputeTime = e ? e[1] : null
        },

        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            const { data, success } = await getTemu_BantuoDayReportChangeLogList(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
                this.loading = false
            } else {
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        async callbackProCode(val) {
            this.ListInfo.OrderNos = val;
        },
        async callbackGoodsCode(val) {
            this.ListInfo.goodsCodes = val;
        },



    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 150px;
        margin-right: 5px;
    }
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 45px;
}
</style>