<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <template #header>
            <el-button-group> 
                    <el-button style="padding: 0;">
                        <el-input-number style=" width: 100px;"  :clearable="true" v-model="filter.changeImgTaskId"   v-model.trim="filter.changeImgTaskId" :min="1" :max="10000000" :controls="false" :precision="0"  placeholder="任务编号" ></el-input-number>
                    </el-button>
                    <el-button style="padding: 0;width: 90px;"> 
                        <el-select v-model="filter.hasOverTime" placeholder="是否完成" style="width:100%;" clearable>
                            <el-option label="是" value="1"></el-option>
                            <el-option label="否" value="0"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;">
                        <el-input  style=" width: 120px;" v-model="filter.productShortName" v-model.trim="filter.productShortName"  :maxlength =100  placeholder="产品简称" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                    <el-button style="padding: 0;">
                        <el-select    filterable v-model="filter.platform" placeholder="平台" clearable :collapse-tags="true" @change="onchangeplatform" style="width: 80px">
                            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding:0;">
                        <el-select  filterable v-model="filter.shopName" placeholder="店铺" clearable style="width: 130px">
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
                        </el-select>
                    </el-button>
 
                    <el-button style="padding: 0;width: 100px;">
                       <el-select  v-model="filter.fpPhotoLqName"   placeholder="分配查询" filterable clearable style="width:100px" >  
                            <el-option v-for="item in fpPhotoLqNameList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>
                    
                    <el-button style="padding: 0;width: 120px;"> 
                        <el-select v-model="filter.warehouse" placeholder="大货仓" style="width:100%;" clearable>
                            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button> 
                    <el-button style="padding: 0;">
                        <el-select  v-model="filter.operationGroup" :clearable="true"  placeholder="运营组"  filterable  style="width:100px">
                            <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;width: 100px;">
                      <el-select  v-model="filter.dockingPeople" :clearable="true"  placeholder="对接人"  filterable  style="width:100px">
                            <el-option v-for="item in dockingPeopleList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-button>
                   
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExeprotShootingTask"   v-if="checkPermission('api:media:pddchangeimg:ExportShootingTaskReport')" >导出</el-button>
                   <el-button  style="margin-left: 20px" @click="onMoveTaskRestart" icon="el-icon-share"  v-if="checkPermission('api:media:pddchangeimg:TaskRestartActionAsync')">批量重启</el-button> 
                    <el-button style=" margin-left: 1; padding: 0 1 0 1 ;width: 100px;" type="primary" v-if="checkPermission('pddchangeimgropDownList')">
                        <el-dropdown @command="handleCommand">
                            <span class="el-dropdown-link" style="font-size: 14;color: #fff;">
                                更多列表<i class="el-icon-arrow-down el-icon--right"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="a">显示全部</el-dropdown-item>
                                <el-dropdown-item command="b">显示默认</el-dropdown-item>
                                <el-dropdown-item command="c">查看分配</el-dropdown-item>
                                <el-dropdown-item command="d">查看小组</el-dropdown-item>
                                <el-dropdown-item command="e">寄样时效</el-dropdown-item>
                                <el-dropdown-item command="f">拍摄时效</el-dropdown-item>
                                <el-dropdown-item command="g">隐藏操作</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </el-button>
            </el-button-group>
        </template>
        <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <ces-table ref="table" :that='that' :isIndex='false' :hasexpand='false'  @sortchange='sortchange'
            :tableData='tasklist' @select='selectchange' :isSelection="true" :tableCols='tableCols'
            :isSelectColumn ='true'  :tablekey="tablekey" :customRowStyle="customRowStyle"
            :loading="listLoading" :summaryarry="summaryarry"   :selectColumnHeight="'0px'"   :isBorder="false">  
            <template slot='extentbtn'>  </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList" />
        </template>

        <!--创建任务----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog :title="taskPageTitle"  :visible.sync="addTask" width="60%" :close-on-click-modal="false" :key="opentime"
            element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <microVediotaskeditfrom :key="opentime" ref="microVediotaskeditfrom"   :taskUrgencyList="taskUrgencyList"  :groupList="groupList"
            :warehouselist="warehouselist"
             :platformList="platformList" :islook='islook'  :onCloseAddForm="onCloseAddForm"></microVediotaskeditfrom>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCloseAddForm(0)">取 消</el-button> 
                </span>
            </template>  
        </el-dialog>
        <!--上传文件----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-drawer title="上传成果文件" :key="opentime" :visible.sync="successfiledrawer" direction="rtl" 
            :wrapperClosable="true"  :close-on-press-escape ="false"
            :before-close="successfiledrawerClose" size="60%"  > 
            <shootinguploadaction  ref="successfileinfo" :rowinfo="selectRowKey" :islook="islook"  style="height: 92%;width:100%"></shootinguploadaction>
            <div class="drawer-footer" >
                <el-button @click="successfiledrawer = false">取 消</el-button> 
            </div>
        </el-drawer>

         <!--查看上传文件并打分----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-drawer title="查看成果文件"  :key="opentime" :visible.sync="successfileshow" direction="ltr" 
         :wrapperClosable="true"  :close-on-press-escape ="false"
         size="50%"   > 
            <shootingvideotaskuploadsuccessfilesocre  ref="shootingvideotaskuploadsuccessfilesocre" 
            :clostfun ="successfiledrawerscoreClose"  
            :rowinfo="selectRowKey"  
            :islook="islook"
            :isOverList="true"
             style="height: 92%;width:100%"></shootingvideotaskuploadsuccessfilesocre>
             <div class="drawer-footer" >
                <el-button   @click="successfileshow=false">关 闭</el-button>   
            </div> 
        </el-drawer>

        <!--查看上传附件---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="查看参考" :key="opentime"  :visible.sync="viewReference" width="60%" :close-on-click-modal="true" 
            element-loading-text="拼命加载中"  v-dialogDrag v-loading="addLoading">
            <shootingvideotaskuploadfile  ref="shootingvideotaskuploadfile" :rowinfo="selectRowKey"></shootingvideotaskuploadfile>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReference = false">关 闭</el-button> 
                </span>
            </template>
        </el-dialog>
        <!--查看详情------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="查看备注" :key="opentime"  :visible.sync="viewReferenceRemark" width="60%" :close-on-click-modal="false" 
            element-loading-text="拼命加载中"  v-dialogDrag v-loading="addLoading">
            <microVedioTaskRemark  ref="shootingTaskRemark" :rowinfo="selectRowKey"   :islook="islook"></microVedioTaskRemark>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReferenceRemark = false">关 闭</el-button> 
                    <my-confirm-button type="submit" :loading="shootingTaskRemarkrawer"  @click="sumbitshootingTaskRemark" v-show="!islook"/>
                </span>
            </template>
        </el-dialog>
        
       
        <!--物流跟踪--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-drawer title="物流跟踪" :visible.sync="drawervisible" direction="rtl" :append-to-body="true">
            <logistics ref="logistics"></logistics>
        </el-drawer>
        <!--订单日志信息------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------>
        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag>
            <orderLogPage ref="orderLogPage" :orderNoInner="sendOrderNoInner" style="z-index:10000;height:600px" />
        </el-dialog>
         <!--任务下单记录---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="任务下单记录" :visible.sync="dialogOrderDtlVisible" width='88%' v-dialogDrag :close-on-click-modal="false" :v-loading="dialogOrderDtlLoading" @close="dialogOrderDtlColsed">
            <el-row style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black;margin-bottom: 2px;">
                <span>下单发货信息：</span>
            </el-row>
            <el-container>
                <el-main style="height:192px;">
                    <ces-table ref="tablexdfhmain" :that='that' :isIndex='true' :hasexpand='true' :tableData='xdfhmainlist' :tableCols='xdfhmainTableCols' :loading="xdfhmainLoading" style="height:190px" :isSelectColumn="false" @cellclick="onxdfhmainCellClick">
                    </ces-table>
                </el-main>
            </el-container>
            <el-row style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black; margin-top: 10px;margin-bottom: 2px;">
                <span>下单发货商品明细信息：</span>
            </el-row>
            <el-container>
                <el-main style="height:262px;">
                    <ces-table ref="tablexdfhdtl" :that='that' :isIndex='true' :hasexpand='true' :tableData='xdfhdtllist' :tableCols='xdfhdtlTableCols' :loading="xdfhdtlLoading" style="height:260px" :isSelectColumn="false">
                    </ces-table>
                </el-main>
            </el-container>
        </el-dialog>
   
    </my-container>
</template>
<script> 
import cesTable from "@/components/Table/tableforvedio.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { pageShootingViewTaskAsync,deleteShootingTaskActionAsync,endShootingTaskActionAsync,endRestartActionAsync
        ,taskOverActionsAsync,exportShootingTaskReport,taskShopActionAsync,taskRestartActionAsync, deleteTaskActionAsync
} from '@/api/media/pddchangeimg';
import uploadfile from '@/views/media/shooting/uploadfile' 
import shootinguploadaction from '@/views/media/shooting/pddchangeImg/pddchangeimguploadaction' 
import microVedioTaskRemark from '@/views/media/shooting/pddchangeImg/pddchangeimgTaskRemark' 
import shootingvideotaskuploadfile from '@/views/media/shooting/pddchangeImg/pddchangeimgtaskuploadfile' 
import shootingvideotaskuploadsuccessfilesocre from '@/views/media/shooting/pddchangeImg/pddchangeimgtaskuploadsuccessfilesocre'
import microVediotaskeditfrom from '@/views/media/shooting/pddchangeImg/pddchangeimgtaskeditfrom' 
import downOrderTaskRemark from '@/views/media/shooting/shared/downOrderTaskRemark'
import orderLogPage from "@/views/order/logisticsWarning/orderLogPage.vue";
import logistics from '@/components/Comm/logistics' 

import { getList as getshopList ,getDirectorGroupList} from '@/api/operatemanage/base/shop' 
import { rulePlatform } from "@/utils/formruletools"; 
import {  formatWarehouse } from "@/utils/tools"; 
const tableCols = [
    { istrue: true, prop: 'changeImgTaskId', label: '编号', width: '35' , fixed: true },
    { istrue: true, prop: 'productShortName', label: '产品简称', width: '180',  fixed: true  , type: "click", handle: (that, row) => that.openComputOutInfo(row) },
    { istrue: true, prop: 'taskUrgencyName', label: '紧急程度', width: '80', fixed: true ,type: 'UrgencyButton'
        , handle: (that, row) => that.shootUrgencyCilck(row.taskUrgencyName ,row.changeImgTaskId)    
    }, 
    { istrue: true, prop: 'bz', label: '备注', width: '50' , align:'center' ,type: "clickflag", fixed: true , handle: (that, row) => that.openTaskRmarkInfo(row) }, 
    
    { istrue: true, prop: 'warehouseStr', label: '大货仓', width: '100'   , fixed: true },
 
   
    { istrue: true, type: "button", width: "50", label: '参考'   ,  
        btnList: [
            { label: "查看", handle: (that, row) => that.videotaskuploadfileDetal(row)   },
        ]
    },
      //照片
      { istrue: true,  prop: 'photoLqNameStr', label: '照片', align:'center', width: '53'    }, 
    { istrue: true,  prop: 'photoDaysStr', label: '天数', width: '53'  , align:'center'  }, 
    { istrue: true,  prop: 'photoOverTimeStr', width: '75', align:'center',  label: '完成日期' },
 
    //视频 
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'},  
    { istrue: true,  prop: 'vedioLqNameStr', label: '视频',  width: "60" , align:'center' }, 

    { istrue: true,  prop: 'vedioDaysStr', label: '天数', align:'center', width: '53' }, 
    { istrue: true,  prop: 'vedioOverTimeStr',  label: '完成日期', width: '75', align:'center'},
    { istrue: true,  prop: 'vedioConfirmNameStr', label: '确认人',  width: "60" , align:'center' }, 
    { istrue: true,  prop: 'vedioConfirmTimeStr',  label: '确认日期', width: '75', align:'center'},
    
    //详情页 
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'}, 
    { istrue: true, prop: 'detailLqNameStr', label: '美工', width: "70", align:'center'},
 
    { istrue: true,  prop: 'detailDaysStr', label: '天数', width: '53', align:'center' },
    { istrue: true,  prop: 'detailOverTimeStr', width: '75',  label: '完成日期' , align:'center'},
    { istrue: true,  prop: 'detailConfirmNameStr', label: '确认人',  width: "60" , align:'center' }, 
    { istrue: true, permission:'shootConfirmationDate',  prop: 'detailConfirmTimeStr',  label: '确认日期', width: '75', align:'center'},

    //照片建模  
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'}, 
    { istrue: true,   prop: 'modelPhotosLqNameStr',  width: "53", align:'center',label: '照片建模'}, 
    { istrue: true,  prop: 'modelPhotosDaysStr', label: '天数', width: '53' , align:'center'},
    { istrue: true, prop: 'modelPhotosOverTimeStr', width: '75', align:'center',  label: '完成日期'},
    { istrue: true,  prop: 'modelPhotoCounts', width: '54', align:'center',  label: '张数'  },


    //视频建模 
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'},  
    { istrue: true, prop: 'modelVideoLqNameStr', label: '视频建模', align:'center', width: "53" }, 

    { istrue: true, prop: 'modelVideoDaysStr', label: '天数', align:'center', width: '53'  },
    { istrue: true, prop: 'modelVideoOverTimeStr', width: '75', align:'center',  label: '完成日期'  },
    { istrue: true, prop: 'modelVedioCounts', width: '54', align:'center',  label: '个数'  },
  


    { istrue: true, prop: 'modelVideoDaysStr', label: '天数', align:'center', width: '53'  },
    { istrue: true, prop: 'modelVideoOverTimeStr', width: '75', align:'center',  label: '完成日期'  },
    { istrue: true, prop: 'modelVedioCounts', width: '54', align:'center',  label: '个数'  },
     //分配
     { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'}, 
     { istrue: true, prop: 'fpPhotoLqNameStr',  width: '54', align:'center', label: '分配照片'},
    { istrue: true, prop: 'fpVideoLqNameStr', width: '54', align:'center', label: '分配视频'}, 
    { istrue: true, prop: 'fpDetailLqNameStr', width: '54', align:'center', label: '分配美工'}, 
    { istrue: true, prop: 'fpModelLqNameStr', width: '54', align:'center', label: '分配建模'}, 
    // 
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'},  
    { istrue: true, prop: 'operationGroupstr', align:'left', width: '90', label: '运营小组'},
    { istrue: true, prop: 'dockingPeople', align:'left',  width: '90', label: '对接人'},

    { istrue: true, prop: 'platformStr', align:'left', width: '100', label: '平台'},
    { istrue: true, prop: 'shopNameStr', align:'left', width: '200', label: '店铺'},

    { istrue: true,  prop: 'taskOverTimeStr',    width: '75',  label: '完成时间'  },
    { istrue: true,  prop: 'arrivalTimeStr',    width: '75',  label: '到货日期'},
    { istrue: true,  prop: 'arrivalTimeDays', align:'center',  width: '53',  label: '到货天数'},
    { istrue: true,  prop: 'deliverTimeStr',   width: '75',  label: '发货日期' },
    { istrue: true,  prop: 'deliverTimeDays', align:'center',  width: '53',  label: '发货天数' },
    { istrue: true,  prop: 'applyTimeStr',   width: '75',  label: '申请日期' },
    { istrue: true,  prop: 'applyTimeDays', align:'center',  width: '53',  label: '申请天数' },
    { istrue: true,  prop: 'createdTime',  width: '85',  label: '创建日期', sortable: 'custom' ,formatter: (row) => row.createdTimeStr },    
    
    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)'}, 
    { istrue: true,  prop: 'orderNoInner',  width: '80', label: '内部单号', type: 'click', handle: (that, row, column) => that.onShowOrderDtl(row)},
    { istrue: true, prop: 'expressNo',  width: '120',  label: '快递单号', type: 'click', handle: (that, row, column) => that.onShowExproessHttp(row)},
    { istrue: true,  prop: 'shootOrderTrack', width: '80',  label: '拿样跟踪', type: 'click', handle: (that, row, column) => that.onShowOrderDtl(row)},
    {
        istrue: true,  type: "button", label: '操作', fixed: 'right', width: "240",
        btnList: [
            { label: "详情", handle: (that, row) => that.detailTask(row) },
            { label: "成果", permission: "changeimgUploadOutComFile", handle: (that, row) => that.onUploadSuccessFile(row) },
            { label: "重启", permission: "api:media:changeimg:EndRestartActionAsync", handle: (that, row) => that.onTaskRestartAction(row) },
            {type:"danger",  permission: "api:media:changeimg:DeleteTaskActionAsync",label: "彻底删除", handle: (that, row) => that.deleteTaskAction(row) },
        ]  
    }
];

const xdfhmainTableCols = [
    { istrue: true, prop: 'changeImgTaskId', label: '当前任务编号', width: '100' },
    { istrue: true, prop: 'microVedioTaskIds', label: '涉及任务编号', width: '100' },
    { istrue: true, prop: 'shootingTaskOrderId', label: '下单号', width: '70' },
    { istrue: true, prop: 'receiverName', label: '收货人', width: '70' },
    { istrue: true, prop: 'receiverPhone', label: '收货电话', width: '80' },
    { istrue: true, prop: 'receiverState', label: '收货省', width: '80' },
    { istrue: true, prop: 'receiverCity', label: '收货市', width: '80' },
    { istrue: true, prop: 'receiverDistrict', label: '收货区', width: '80' },
    { istrue: true, prop: 'receiverAddress', label: '收货地址' },
    { istrue: true, prop: 'sampleRrderNo', label: '聚水潭内部单号', width: '120', type: 'click', handle: (that, row) => that.showLogDetail(row)},
    { istrue: true, prop: 'sampleExpressNo', label: '快递单号', width: '120', type: 'click', handle: (that, row, column) => that.onShowExproessHttp(row)},
    { istrue: true, prop: 'sampleExpressCom', label: '物流公司', width: '80' },
    { istrue: true, prop: 'arrivalDate', label: '到货日期', width: '100', formatter: (row) => row.arrivalDate == null ? null : formatTime(row.arrivalDate, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'isDy', label: '是否到样', width: '80', formatter: (row) => row.isDy == 1 ? "是" : "否" },
    { istrue: true, prop: 'isZt', label: '是否自提', width: '80', formatter: (row) => row.isZt == 1 ? "是" : "否" },
    { istrue: true, prop: 'approveStateName', label: '状态', width: '80'},
    { istrue: true, prop: 'createdTime', label: '创建时间', width: '150', formatter: (row) => row.createdTime == null ? null : formatTime(row.createdTime, 'YYYY-MM-DD HH:mm:ss') },
];
const xdfhdtlTableCols = [
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '200' },
    { istrue: true, prop: 'goodsName', label: '商品名称' },
    { istrue: true, prop: 'goodsPrice', label: '单价', width: '120', display: false },
    { istrue: true, prop: 'goodsQty', label: '数量', width: '120' },
    { istrue: true, prop: 'goodsAmount', label: '总额', width: '120', display: false },
];
export default {
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow
        ,cesTable,uploadfile,shootinguploadaction,microVedioTaskRemark,downOrderTaskRemark,logistics,orderLogPage
        ,shootingvideotaskuploadfile,shootingvideotaskuploadsuccessfilesocre,microVediotaskeditfrom
    },
    inject:[ 'getUserListRelod','getwarehouseListRelod','getShootingViewPersonRelod' ],
    watch: {  
    },
    data() {
        return {
            tablekey:"microVediotaskover",
            role:"tz",
            selmicroVedioTaskId:0,
            taskUrgencyStatus:"9",
            taskUrgencyAproved:false,
            Oncommand:'a',
            shootingTaskRemarkrawer:false,
            dialogOrderDtlLoading:false,
            viewReferenceRemark:false,
            shootingvediotask:'shootingvediotask',
            opentime:null,
            outconfilekey:null,
            fpcharttype:null,
            //分配趋势图
            shootingchartforfpvisible:false,
            //上传成果文件
            successfiledrawer:false,
            successfileshow:false,
            //查看参考
            viewReference:false,
            //选中的行
            selectRowKey:null,
            that: this,
            pageLoading :false,
            islook:true,
            filter: { 
                isShop:0,
                isdel:1,
                isComplate:null,
                shopName:null,
                operationGroup:null
            },
            tasklist:[], 
            taskPageTitle: "创建任务",
            referenceVideoList: [],
            multipleSelection: [],
            addLoading :false,
            formatWarehouse: formatWarehouse,
            warehouselist: [],
            shopList: [],
            userList: [],
            groupList: [],
            dialogOrderDtlColsed:false,
            fpDetailLqNameList: [],
            fpModelLqNameList: [],
            fpPhotoLqNameList: [],
            fpVideoLqNameList: [],
            dockingPeopleList: [],
            
            taskUrgencyList :[{value:1,label:"加急"},{value:0,label:"紧急"},{value:2,label:"确认"},{value:9,label:"正常"}],
            platformList :[],
            tableCols: tableCols,
            total: 0,
            //选中的行id
            selids : [],
            taskPhotofileList:[],
            taskExeclfileList:[],
            addTask: false,
            loginfo:null,
            summaryarry: {},
            pager: {  },
            sels: [], // 列表选中列
            listLoading: false,

            dialogAddOrderSubmitLoding:false,
            dialogAddOrderVisible:false, 
            dialogOrderDtlVisible:false, 
            drawervisible: false,  
            sendOrderNoInner: "",
            dialogHisVisible: false, 

            xdfhmainlist: [],
            xdfhmainTableCols: xdfhmainTableCols,
            xdfhmainLoading: false,

            xdfhdtllist: [],
            xdfhdtlTableCols: xdfhdtlTableCols,
            xdfhdtlLoading: false,
        };
    },
    watch: {
    },
    async created() {

    },
    async mounted() {

        await this.onSearch();
        await this.onGetdrowList();
        await this.onGetdrowList2();
       /*  this.Oncommand = this.role; 
        this.pageLoading =true;
        await this.ShowHideonSearch();
        this.pageLoading =false; */
        await this.getShootingViewPer();
        
    },
    methods: { 
        async onMoveTaskRestart()
        {
            if(this.selids.length==0){
                this.$message({ type: 'warning', message: "请选择一个任务" });
                return;
            } 
            await this.onTaskRestartActionShared(this.selids); 
              
        },
        async onMoveTaskShop()
        { 
            if(this.selids.length==0){
                this.$message({ type: 'warning', message: "请选择一个任务" });
                return;
            } 
            await this.onTaskShopActionShared(this.selids)   
        },
         //存档操作
         async onTaskShopAction(row)
        {
            await this.onTaskShopActionShared([row.changeImgTaskId]); 
        },
        async onTaskShopActionShared(array)
        {
            this.$confirm("选中的任务移动到存档列表，是否确定存档", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            }).then(async () => {
                var res =  await taskShopActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await  this.onRefresh();
                    this.selids = [];
                } 
            });
        },

         //重新启动
         async onTaskRestartAction(row)
        { 
            await this.onTaskRestartActionShared([row.changeImgTaskId]); 
        },
        //重新启动
        async onTaskRestartActionShared(array)
        {
            this.$confirm("选中的任务将会移动至任务列表，是否确定执行", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            }).then(async () => {
                var res =  await taskRestartActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await  this.onRefresh();
                    this.selids = [];
                } 
            });
        },
        //回收站彻底删除操作
        async deleteTaskAction(row)
        { 
            await this.deleteTaskActionShared([row.changeImgTaskId]); 
        },
        //回收站彻底删除操作
        async deleteTaskActionShared(array)
        { 
            this.$confirm("选中的任务会彻底删除，是否确定执行", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            }).then(async () => {
                var res =  await deleteTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await  this.onRefresh();
                    this.selids = [];
                } 
            });
        }, 
        async onExeprotShootingTask(){
            var pager = this.$refs.pager.getPager();
            this.filter.isShop = 0;
            this.filter.isdel = 1; 
            this.filter.isComplate = null;  
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.pageLoading = true;
            var res = await exportShootingTaskReport(params);
            if (res?.data?.type == 'application/json') {return;}
            this.pageLoading = false;
            const aLink = document.createElement("a");
            var blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '日常改图导出.xlsx')
            aLink.click()

        },
        customRowStyle(row,index){
            if(row.row?.isend && row.row.isend==1){ 
                let styleJson = {};
                styleJson.color = "rgb(216 216 216)"; 
                return styleJson
            }else{
                return null
            }
            
        }, 
        //完成操作
        async onTaskOverAction(row)
        {
           await this.onTaskOverActionShared([row.changeImgTaskId]) 
           if (res?.success) {
                this.$message({ message: '操作成功', type: "success" });
                await  this.onRefresh();
                this.selids = [];
            } 
        }, 
        //终止重启
        async onEndRestartAction(row)
        {
            this.$confirm("选中的任务将会重启，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res =  await endRestartActionAsync([row.changeImgTaskId]);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await  this.onRefresh();
                    this.selids = [];
                } 
            });
        },
        //终止
        async OnendShootingTaskAction(row)
        {
            this.$confirm("选中的任务将会终止，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res =  await endShootingTaskActionAsync([row.changeImgTaskId]);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await  this.onRefresh();
                    this.selids = [];
                } 
            });
        },
        // 删除操作
        async OnDeleteShootingTaskAction(row)
        { 
            this.$confirm("选中的任务会移动到回收站，是否确定执行", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            }).then(async () => {
                var res =  await deleteShootingTaskActionAsync([row.changeImgTaskId]);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await  this.onRefresh();
                    this.selids = [];
                } 
            });
        }, 
        //批量完成
        async onMoveTaskOver(){
            if(this.selids.length==0){
                this.$message({ type: 'warning', message: "请选择一个任务" });
                return;
            }
            await this.onTaskOverActionShared(this.selids) 
        },
        //完成操作公共   
        async onTaskOverActionShared(array)
        {
            this.$confirm("选中的任务移动完成列表，是否确定完成", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            }).then(async () => {
                var res =  await taskOverActionsAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.selids = [];
                    await  this.onRefresh();
                } 
            });
        },
        //获取下拉数据
        async onGetdrowList()
        {  
          var pfrule = await rulePlatform();
            this.platformList = pfrule.options;   
            var res = await getDirectorGroupList();
            this.groupList = res.data?.map(item => { return { value: item.key, label: item.value }; });       
        },
        async onGetdrowList2()
        { 
           var res =await this.getwarehouseListRelod();  
            this.warehouselist =res?.map(item => { return { value: item.id, label: item.label }; });
        },
        async onchangeplatform(val) {
            var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100 });
            this.filter.shopName = "";
            this.shopList = res1.data.list;
        },
        //获取分配人下拉，对接人下啦
        async getShootingViewPer() {
            var res = await this.getShootingViewPersonRelod(); 
            if(res){ 
                this.dockingPeopleList=res.dockingPeopleList;
                this.fpPhotoLqNameList=res.fpallList;
            } 
        },
        
       async ShowHideonSearch(){ 
            var checkedColumnsFora= [];  
            switch (this.Oncommand) {
                //显示全部 ,部门经理，超管
                case "a":
                    checkedColumnsFora=
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注', '参考',
                    '确认','完成','确认人','确认日期', '完成日期', 
                    '照片', '视频', '美工', '详情页','照片建模','视频建模',  '张数','数量', '个数', '天数',
                    '分配照片', '分配视频', '分配美工', '分配建模' ,
                    '运营小组','对接人', '平台', '店铺','完成时间','到货日期','到货天数','发货日期','发货天数','发货日期', '申请日期', '申请天数', 
                    '创建日期','内部单号','快递单号','拿样跟踪','操作'];  
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    await this.$refs["table"].initsummaryEvent(); 
                    break;
                //显示默认
                case "b":
                    checkedColumnsFora=
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注', '参考',
                     '确认人',  '完成日期', 
                    '照片', '视频', '美工', '详情页','照片建模','视频建模',  '张数','数量', '个数',  
                    '分配照片', '分配视频', '分配美工', '分配建模' ,
                    '运营小组','对接人', '平台', '店铺','完成时间','到货日期','发货日期','发货日期', '申请日期', 
                    '创建日期','内部单号','快递单号','拿样跟踪','操作'];  
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    var i  = checkedColumnsFora.indexOf('分配照片');
                    await this.oncoulumCheck([i +15,i +16,i +17,i +18]); 
                    break;
                //查看分配
                case "c":
                    checkedColumnsFora=
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注', '参考', 
                    '照片', '视频', '美工', '详情页','照片建模','视频建模',   
                    '分配照片', '分配视频', '分配美工', '分配建模' ,
                    '运营小组','对接人','创建日期'  ,'操作']; 
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    var i  = checkedColumnsFora.indexOf('分配照片');
                    await this.oncoulumCheck([i +8,i +9,i +10,i +11]);   
                    break;
                //查看小组
                case "d":
                    checkedColumnsFora=
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注',    
                    '分配照片', '分配视频', '分配美工', '分配建模' ,
                    '运营小组','对接人', '平台', '店铺', 
                    '创建日期','内部单号','快递单号','拿样跟踪','操作'];  
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    var i  = checkedColumnsFora.indexOf('分配照片');
                    await this.oncoulumCheck([i +8,i +9,i +10,i +11]);   
                    break;
                //寄样实效
                case "e":
                    checkedColumnsFora=
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注' ,
                    '分配照片', '分配视频', '分配美工', '分配建模' ,
                    '到货日期','到货天数','发货日期','发货天数','发货日期','申请日期', '申请天数', 
                    '创建日期','内部单号','快递单号','拿样跟踪','操作']; 
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    var i  = checkedColumnsFora.indexOf('分配照片');
                    await this.oncoulumCheck([i +8,i +9,i +10,i +11]);    
                    break;
                //拍摄实效
                case "f":
                    checkedColumnsFora=  
                    ['编号', '产品简称', '大货仓', '紧急程度', '备注',  
                    '确认人','确认日期', '完成日期', 
                    '照片', '视频', '美工', '详情页','照片建模','视频建模',  '张数','数量', '个数', '天数', 
                    '到货日期','创建日期', '操作']; 
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    break;
                //隐藏操作
                case "g":
                    checkedColumnsFora=  ['编号', '产品简称', '大货仓', '紧急程度', '备注', '参考',
                    '确认','完成','确认人','确认日期', '完成日期', 
                    '照片', '视频', '美工', '详情页','照片建模','视频建模',  '张数','数量', '个数', '天数',
                    '分配照片', '分配视频', '分配美工', '分配建模' ,
                    '运营小组','对接人', '平台', '店铺','完成时间','到货日期','到货天数','发货日期','发货天数','发货日期', '申请日期', '申请天数', 
                    '创建日期','内部单号','拿样跟踪','快递单号'];  
                    this.$refs["table"].checkedColumns=checkedColumnsFora;  
                    await this.$refs["table"].initsummaryEvent(); 
                    break;
                default: 
                    break;
            } 
        },
        async oncoulumCheck(array){
            await this.$refs["table"].initsummaryEventNew(array,'onsummaryClick'); 
        },
        //更多列操作
        async handleCommand(command) {
            this.Oncommand = command;
            this.pageLoading =true;
            await  this.ShowHideonSearch();
            this.pageLoading =false;
        },
        //查询
        async onSearch(){
            this.$refs.pager.setPage(1);
            this.getTaskList();
            this.selids = [];
        },
        //刷新当前页
        async onRefresh(){
             await  this.getTaskList();
        },
        //获取数据
        async getTaskList() {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.listLoading = true;
            const res = await pageShootingViewTaskAsync(params);
            this.listLoading = false;
            this.total = res.data.total
            this.tasklist = res.data.list;
            this.summaryarry =  res.data.summary;
        },
        //列表排序
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        //多选事件
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.changeImgTaskId);
            })
        },

        async onxdfhmainCellClick(row) {
            this.xdfhmainlist.forEach(
                f => {
                    if (f.shootingTaskOrderId == row.shootingTaskOrderId) {
                        this.xdfhdtllist = f.dtlEntities;
                    }
                }
            );
        },
         //任务详情
        async detailTask(row) {
            this.addLoading =true;
            this.opentime = this.opentime +1;
            this.taskPageTitle = "任务详情";
            this.addTask = true;
            this.$nextTick(function () { 
                this.$refs.microVediotaskeditfrom.editTask(row);
                this.$refs.microVediotaskeditfrom.setShootO(this.warehouselist);
                
            });
            this.addLoading =false;
        },
        //关闭窗口，初始化数
        onCloseAddForm(type){  
            this.addTask = false;
            if(type == 1){
                this.onSearch();
            }
        },
      
        //成果文件提交
        async onSubComputOutInfo(){ 
            this.filesocreLoading = true;
            await  this.$refs.shootingvideotaskuploadsuccessfilesocre.onSubComputOutInfo();
            this.filesocreLoading = false;
        },
        //查看详情备注页
        openTaskRmarkInfo(row){
            this.opentime = this.opentime +1;
            this.selectRowKey = row.changeImgTaskId;
            this.viewReferenceRemark = true;
        },
        //成果文件上传
        onUploadSuccessFile(row){
            this.opentime = this.opentime +1;
            this.selectRowKey =row.changeImgTaskId;
            this.outconfilekey = row.changeImgTaskId;
            this.successfiledrawer = true;
        },   
        //关闭成果文件上传
        successfiledrawerClose(){
            this.successfiledrawer = false;
        },
        //查看参考附件
        videotaskuploadfileDetal(row){
            this.opentime = this.opentime +1;
            this.selectRowKey = row.changeImgTaskId;
            this.viewReference = true;
        },
        //查看成果文件
        openComputOutInfo(row){
            this.opentime = this.opentime +1;
            this.selectRowKey =row.changeImgTaskId;
            this.successfileshow = true;
        },
        successfiledrawerscoreClose(){
            this.successfileshow = false;
        },

        async sumbitshootingTaskRemark(){
            this.shootingTaskRemarkrawer = true;
            await this.$refs.shootingTaskRemark.onsubmit();
            this.shootingTaskRemarkrawer = false;
        },
         //下单发货
         async onAddOrder() {
            if (this.selids.length <= 0) {
                this.$message({ message: '请勾选任务', type: "warning" });
                return;
            } 
            await this.$nextTick(function () {
                this.$refs.downOrderTaskRemark.onAddOrder(this.selids); 
            })  
            this.dialogAddOrderVisible = true;
        },
        //关闭下单发货界面
        async closeAddOrder() {
            this.dialogAddOrderSubmitLoding = false;
        },
        //下单保存
        async onAddOrderSave() {
            this.dialogAddOrderLoading =true;
            await  this.$nextTick(function () {
                this.$refs.downOrderTaskRemark.onAddOrderSave();
            }) 
            this.dialogAddOrderLoading =false;
        },
        async onShowOrderDtl(row) {
            this.dialogOrderDtlVisible = true;
            this.xdfhmainLoading = true;
            this.xdfhdtlLoading = true;
            var ret = await getShootingTaskOrderListById({ changeImgTaskId: row.changeImgTaskId });
            this.xdfhmainLoading = false;
            this.xdfhdtlLoading = false;
            if (ret?.success && ret.data.length > 0) {
                ret.data.forEach(f => f.changeImgTaskId = row.changeImgTaskId);
                this.xdfhmainlist = ret.data;
                this.xdfhdtllist = ret.data[0].dtlEntities;
            }
        },
        async onShowExproessHttp(row) {
            this.drawervisible = true;
            let expressNo=row.expressNo;
            if(!expressNo)
            {
                expressNo=row.sampleExpressNo;
            }
            this.$nextTick(function () {
                this.$refs.logistics.showlogistics("",expressNo);
            })
        },
        showLogDetail (row) {
            this.dialogHisVisible = true;
            let sampleRrderNo=row.sampleRrderNo;
            if(!sampleRrderNo)
            {
                sampleRrderNo=row.orderNoInner;
            }
            this.sendOrderNoInner = sampleRrderNo;
        },
    }, 
};
</script>