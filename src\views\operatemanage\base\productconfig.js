import {
  ruleShop, ruleDirector, ruleDirectorUserType, ruleDirectorBackupUserType, ruleProjectList
  , ruleDirectorBackup, ruleDirectorGroup, ruleProductState, ruleProductMask, rulePlatform, ruleProductCategory, ruleDirectorBrand
} from '@/utils/formruletools'
const getformaddsinglerule = async (that) => {
  let rule = [{ type: 'hidden', field: 'id', title: 'id', value: '' },
  { type: 'input', field: 'proCode', title: '宝贝ID', validate: [{ type: 'string', required: true, message: '请输入宝贝ID' }] },
  { type: 'input', field: 'snProCode', title: '苏宁宝贝ID', validate: [{ pattern: /^[0-9]*$/, message: "请输入正确的苏宁宝贝ID(数字)" }], props: { maxlength: 30 } },
  { type: 'select', field: 'platform', title: '平台', value: null, update(val, rule) { if (val) { that.updateruleshop(val) } }, ...await rulePlatform(), props: { clearable: true } },
  { type: 'select', field: 'shopId', title: '所属店铺', value: '', ...await ruleShop(1), props: { filterable: true, clearable: true } },
  { type: 'select', field: 'operateSpecialUserId', title: '运营专员', value: '', ...await ruleDirector(), props: { filterable: true, clearable: true } },
  { type: 'select', field: 'userId', title: '运营助理', value: '', ...await ruleDirectorBackup(), props: { filterable: true, clearable: true } },
  { type: 'select', field: 'userId2', title: '产品专员', value: '', ...await ruleDirectorUserType(), props: { filterable: true, clearable: true } },
  { type: 'select', field: 'userId3', title: '产品助理', value: '', ...await ruleDirectorBackupUserType(), props: { filterable: true, clearable: true } },
  { type: 'select', field: 'groupId', title: '组长', value: '', ...await ruleDirectorGroup(), props: { filterable: true, clearable: true, disabled: !that.checkPermission('api:operatemanage:productmanager:groupIdBatchEdit') } },
  { type: 'input', field: 'title', title: '产品标题', validate: [{ type: 'string', required: true, message: '请输入' }] },
  //{type:'input',field:'proBianMa',title:'商品编码', validate: [{type: 'string', required: true, message:'请输入'}]},
  { type: 'input', field: 'itemNo', title: '产品货号', validate: [{ type: 'string', required: false, message: '请输入' }] },
  { type: 'select', field: 'status', title: '状态', value: null, ...await ruleProductState(), props: { clearable: true } },
  { type: 'hidden', field: 'star', title: '星星', options: [{ value: 1, label: '红色', }, { value: 2, label: '橙色', }, { value: 3, label: '黄色', }, { value: 4, label: '绿色', }, { value: 5, label: '蓝色', }, { value: 6, label: '靛色', }, { value: 7, label: '紫色' }, { value: 8, label: '灰色' }], props: { clearable: true } },
  { type: 'hidden', field: 'flag', title: '旗帜', options: [{ value: 1, label: '红色' }, { value: 2, label: '橙色', }, { value: 3, label: '黄色', }, { value: 4, label: '绿色', }, { value: 5, label: '蓝色', }, { value: 6, label: '靛色', }, { value: 7, label: '紫色', }, { value: 8, label: '灰色' }], props: { clearable: true } },
  { type: 'select', field: 'projName', title: '项目', value: '', ...await ruleProjectList({ projName: '' }), props: { filterable: true, clearable: true } },
  { type: 'select', field: 'baoPin', title: '爆品', value: '', options: [{ value: '【大侠-爆品】', label: '【大侠-爆品】' }, { value: '【大侠-爆品-已核-已进货】', label: '【大侠-爆品-已核-已进货】' }, { value: '【大侠-爆品-已核-未进货】', label: '【大侠-爆品-已核-未进货】' }, { value: '【大侠-爆品-未核】', label: '【大侠-爆品-未核】' }, { value: '【徐琛-爆品】', label: '【徐琛-爆品】' }, { value: '【徐琛-爆品-已核-已进货】', label: '【徐琛-爆品-已核-已进货】' }, { value: '【徐琛-爆品-已核-未进货】', label: '【徐琛-爆品-已核-未进货】' }, { value: '【徐琛-爆品-未核】', label: '【徐琛-爆品-未核】' }, { value: '【左玉玲-爆品】', label: '【左玉玲-爆品】' }, { value: '【左玉玲-爆品-已核-已进货】', label: '【左玉玲-爆品-已核-已进货】' }, { value: '【左玉玲-爆品-已核-未进货】', label: '【左玉玲-爆品-已核-未进货】' }, { value: '【左玉玲-爆品-未核】', label: '【左玉玲-爆品-未核】' }], props: { filterable: true, clearable: true } },
  { type: 'input', field: 'remark', title: '备注', validate: [{ type: 'string', required: false, message: '请输入' }, { pattern: /^\S*$/, message: '备注中不能包含空格' }], props: { maxlength: 100, placeholder: '请输入备注信息' } },
  {
    type: 'el-tooltip',
    props: {
      content: '上新：该ID编码，首次在全平台销售   重开：该ID编码，已经在全平台销售',
      placement: 'top'
    },
    children: [
      {
        type: 'select',
        field: 'upNewOpen',
        title: '上新/重开',
        value: null,
        options: [{value: '上新', label: '上新'}, {value: '重开', label: '重开'}],
        // validate: [{ type: 'string', required: true, message: '请选择上新/重开' }], 
        props: { filterable: true, clearable: true }, 
      }
    ]
  },
    //{type:'hidden', field:'IsDamageLink',title:'是否货损链接',options: [{value:true, label:'是',},{value:false, label:'否',}],props:{clearable:true}},
    //{type:'radio',field:'flag',title:'旗帜2',value:false,options:[{value:1,label:"红色",disabled:false},{value:2,label:"橙色"},]},
  ];

  return rule;
}
const getformeditbatchrule = async (that) => {
  let rule = [{ type: 'hidden', field: 'ids', title: 'ids', value: '' },
  { type: 'select', field: 'platform', title: '平台', value: null, update(val, rule) { if (val) { that.updateruleshop(val) } }, ...await rulePlatform() },
  { type: 'select', field: 'shopId', title: '所属店铺', value: '', props: { filterable: true } },
  { type: 'select', field: 'groupId', title: '组长', value: '', ...await ruleDirectorGroup(), props: { filterable: true } },
  { type: 'select', field: 'operateSpecialUserId', title: '运营专员', value: '', ...await ruleDirector(), props: { filterable: true } },
  { type: 'select', field: 'userId', title: '运营助理', value: '', ...await ruleDirectorBackup(), props: { filterable: true } },
  { type: 'select', field: 'userId2', title: '产品专员', value: '', ...await ruleDirector(), props: { filterable: true } },
  { type: 'select', field: 'userId3', title: '产品助理', value: '', ...await ruleDirectorBackup(), props: { filterable: true } },
    //{type:'radio',field:'isClassCodeCalc',title:'是否参与系列编码计算',value:false,options:[{value:true,label:"是",disabled:false},{value:false,label:"否"},]},
  ];
  return rule;
}
const getformcommissionbatchrule = async () => {
  let rule = [{ type: 'hidden', field: 'ids', title: 'ids', value: '' },
  { type: 'hidden', field: 'procodes', title: 'procodes', value: '' },
  { type: 'hidden', field: 'historyTime', title: 'historyTime', value: '' },
  { type: 'select', field: 'commissionUserId1', title: '提成负责人1', value: '', ...await ruleDirector(), props: { filterable: true }, col: { span: 7 } },
  { type: 'radio', field: 'istrue1', title: '利润类型', value: true, options: [{ value: true, label: "毛利", disabled: false }, { value: false, label: "净利" },], col: { span: 7 } },
  { type: 'inputNumber', field: 'commissionRate1', title: '提成比例1(%)', value: 0, col: { span: 10 } },
  { type: 'select', field: 'commissionUserId2', title: '提成负责人2', value: '', ...await ruleDirector(), props: { filterable: true }, col: { span: 7 } },
  { type: 'radio', field: 'istrue2', title: '利润类型', value: true, options: [{ value: true, label: "毛利", disabled: false }, { value: false, label: "净利" },], col: { span: 7 } },
  { type: 'inputNumber', field: 'commissionRate2', title: '提成比例2(%)', value: 0, col: { span: 10 } },
  { type: 'select', field: 'commissionUserId3', title: '提成负责人3', value: '', ...await ruleDirector(), props: { filterable: true }, col: { span: 7 } },
  { type: 'radio', field: 'istrue3', title: '利润类型', value: true, options: [{ value: true, label: "毛利", disabled: false }, { value: false, label: "净利" },], col: { span: 7 } },
  { type: 'inputNumber', field: 'commissionRate3', title: '提成比例4(%)', value: 0, col: { span: 10 } },
  { type: 'select', field: 'commissionUserId4', title: '提成负责人4', value: '', ...await ruleDirector(), props: { filterable: true }, col: { span: 7 } },
  { type: 'radio', field: 'istrue4', title: '利润类型', value: true, options: [{ value: true, label: "毛利", disabled: false }, { value: false, label: "净利" },], col: { span: 7 } },
  { type: 'inputNumber', field: 'commissionRate4', title: '提成比例4(%)', value: 0, col: { span: 10 } }];
  return rule;
}
const getformproductCidbatchrule = async (that) => {
  let rule = [{ type: 'hidden', field: 'ids', title: 'ids', value: '' },
  { type: 'select', field: 'platform', title: '平台', value: null, update(val, rule) { if (val) { that.updaterulecategory(val) } }, ...await rulePlatform() },
  { type: 'cascader', field: 'productCategoryId', title: '类目', value: [] }];
  return rule;
}
const getformproductMaskrule = async () => {
  let rule = [{ type: 'hidden', field: 'ids', title: 'ids', value: '' }, { type: 'checkbox', field: 'mask', title: '标签', value: [], ...await ruleProductMask() }];
  return rule;
}
const getformproductBrandrule = async () => {
  let rule = [{ type: 'hidden', field: 'ids', title: 'ids', value: '' },
  { type: 'select', field: 'brandId', title: '采购负责人', value: null, update(val, rule) { if (val) { that.updaterulecategory(val) } }, ...await ruleDirectorBrand() }];
  return rule;
}
const getTagrule = async () => {
  let rule = [{ type: 'hidden', field: 'ids', title: 'ids', value: '' },
  { type: 'hidden', field: 'star', title: '星星', options: [{ value: 1, label: '红色', }, { value: 2, label: '橙色', }, { value: 3, label: '黄色', }, { value: 4, label: '绿色', }, { value: 5, label: '蓝色', }, { value: 6, label: '靛色', }, { value: 7, label: '紫色' }, { value: 8, label: '灰色' }], props: { clearable: true } },
  { type: 'hidden', field: 'flag', title: '旗帜', options: [{ value: 1, label: '红色' }, { value: 2, label: '橙色', }, { value: 3, label: '黄色', }, { value: 4, label: '绿色', }, { value: 5, label: '蓝色', }, { value: 6, label: '靛色', }, { value: 7, label: '紫色', }, { value: 8, label: '灰色' }], props: { clearable: true } }];
  return rule;
}

const getformeditstylerule = async (that) => {
  let rule = [{ type: 'hidden', field: 'ids', title: 'ids', value: '' },
  { type: 'radio', field: 'isClassCodeCalc', title: '计算系列编码', value: true, options: [{ value: true, label: "是", disabled: false }, { value: false, label: "否" },] },
  ];
  return rule;
}
export { getformaddsinglerule, getformeditbatchrule, getformcommissionbatchrule, getformproductCidbatchrule, getformproductMaskrule, getformproductBrandrule, getformeditstylerule, getTagrule }


