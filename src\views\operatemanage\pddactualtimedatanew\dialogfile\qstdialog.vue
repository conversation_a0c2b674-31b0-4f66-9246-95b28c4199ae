<template>
  <div>
    <div class="flexrow">
      <el-button-group>
        <el-button type="text" @click="seltype('time')" style="margin-right: 10px;"
          :style="datetype ? { color: '#409EFF' } : { color: 'black' }">对比时间</el-button>
        <el-button type="text" @click="seltype('target')" style="margin-right: 10px;"
          :style="!datetype ? { color: '#409EFF' } : { color: 'black' }">对比指标</el-button>
      </el-button-group>

      <el-button-group style="margin-left: auto;">
        <el-button type="text" style="margin-right: 10px;" @click="seltime('今日')"
          :style="timetype == '今日' ? { color: '#409EFF' } : { color: 'black' }">今日</el-button>
        <el-button type="text" style="margin-right: 10px;" @click="seltime('昨日')"
          :style="timetype == '昨日' ? { color: '#409EFF' } : { color: 'black' }">昨日</el-button>
        <el-button type="text" style="margin-right: 10px;" @click="seltime('7日')"
          :style="timetype == '7日' ? { color: '#409EFF' } : { color: 'black' }">7日</el-button>
        <el-button type="text" style="margin-right: 10px;" @click="seltime('30日')"
          :style="timetype == '30日' ? { color: '#409EFF' } : { color: 'black' }">30日</el-button>
        <el-button type="text" style="margin-right: 10px;" @click="seltime('90日')"
          :style="timetype == '90日' ? { color: '#409EFF' } : { color: 'black' }">90日</el-button>

        <el-date-picker v-model="filter.onealltime" type="daterange" align="right" unlink-panels range-separator="至"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd" start-placeholder="开始日期" :clearable="false"
          end-placeholder="结束日期" @change="timechange('onetime',$event)" :picker-options="pickerOptions">
        </el-date-picker>

      </el-button-group>
    </div>

    <!-- 第一个界面 -->
    <div v-show="datetype" style="width: 100%; height: 100%; margin-top: 10px;">
      <div style="width: 100%; display: flex; flex-direction: row; align-items: center;" v-if="viewData1">
        <el-select style="width: 12%;" v-model="filter.selected" @change="selchange('timetype', $event)"
          placeholder="指标选择器" :collapse-tags="true" clearable>
          <!-- multiple -->
          <el-option v-for="item in selviewData" :key="item" :label="item" :value="item" />
        </el-select>

        <!-- <div>{{ filter.referdate }}</div> -->
        <div v-if="!manydate" style="margin-left: 10px;">{{ filter.referdate | timeFormater }}</div>
        <div v-if="manydate" style="margin-left: 10px;">{{ filter.onealltime[0] | timeFormater }} ~ {{ filter.onealltime[1] | timeFormater }} </div>


        <div style="margin-left: auto;">
          <el-checkbox-group v-model="anlidata" @change="boxchange">
            <el-checkbox label="对比数据1">
              <div class="flexrow" style="align-items: center;">
                对比
                <el-date-picker v-model="filter.twoalltime" @change="timechange('twotime',$event)" type="daterange" align="right" unlink-panels
                  range-separator="至" format="yyyy-MM-dd" value-format="yyyy-MM-dd" start-placeholder="开始日期" :clearable="false"
                  end-placeholder="结束日期" :picker-options="pickerOptions">
                </el-date-picker>
              </div>
            </el-checkbox>

            <el-checkbox label="对比数据2">
              <div class="flexrow" style="align-items: center;">
                对比
                <el-date-picker v-model="filter.thralltime" @change="timechange('thrtime',$event)" type="daterange" align="right" unlink-panels
                  range-separator="至" format="yyyy-MM-dd" value-format="yyyy-MM-dd" start-placeholder="开始日期" :clearable="false"
                  end-placeholder="结束日期" :picker-options="pickerOptions">
                </el-date-picker>
              </div>
            </el-checkbox>
          </el-checkbox-group>
        </div>


      </div>

    </div>

    <!-- 第二个界面 -->
    <div v-show="!datetype" style="width: 100%; height: 100%; margin-top: 10px;">
      <div style="width: 100%; display: flex; flex-direction: row; align-items: center;" v-if="viewData2">
        <el-select style="width: 12%;" v-model="filtertwo.selected" @change="selchange('thingtype', $event)"
          placeholder="指标选择器" :collapse-tags="true" clearable>
          <el-option v-for="item in viewData2.legend" :key="item" :label="item" :value="item" />
        </el-select>

        <div v-if="!manydate" style="margin-left: 10px;">{{ filter.referdate | timeFormater }}</div>
        <div v-if="manydate" style="margin-left: 10px;">{{ filter.onealltime[0] | timeFormater }} ~ {{ filter.onealltime[1] | timeFormater }} </div>

        <div style="margin-left: auto;">

          <div class="flexrow" style="align-items: center;">
            <div style="width: 70px;">对比</div>
            <el-select style="width: 100%;" v-model="filtertwo.selectedmul" @change="selchange('thingtypemul', $event)"
              placeholder="指标选择器" multiple :collapse-tags="true" clearable>
              <el-option v-for="item in viewData2.legend" :disabled="isdiabledfuc(item)" :key="item" :label="item"
                :value="item" />
            </el-select>
          </div>

        </div>


      </div>
    </div>

    <!-- 图表 -->
    <div style="position: relative; min-height: 300px; min-width: 200px" v-show="datetype">
      <!-- <acharts></acharts> -->
      <acharts ref="achartsone" v-if="!chartloading" :analysisData="viewData1" :thisStyle="thisStyle" :gridStyle="gridStyle">
      </acharts>
      <div v-if="chartloading" style="position: absolute; left: 50%; top: 50%; font-size: 30px; color: #409EFF;"><i
          class="el-icon-loading"></i></div>
    </div>


    <!-- 图表 -->
    <div style="position: relative; min-height: 300px; min-width: 200px" v-show="!datetype">
      <!-- <acharts></acharts> -->
      <acharts ref="achartstwo" v-if="!chartloading&&viewData2" :analysisData="viewData2" :thisStyle="thisStyle" :gridStyle="gridStyle">
      </acharts>
      <div v-if="chartloading" style="position: absolute; left: 50%; top: 50%; font-size: 30px; color: #409EFF;"><i
          class="el-icon-loading"></i></div>
    </div>


  </div>
</template>

<script>
import { getPddActualTimeProAnalysisAsync } from '@/api/operatemanage/datapdd/actualtimedatapdd.js';
import acharts from '@/views/operatemanage/pddactualtimedatanew/commponts/charts.vue';
import dayjs from "dayjs";
export default {
  name: 'Vue2demoQstdialog',
  props: ['rowmsg'],
  components: { acharts },
  data() {
    return {
      checkedTime: ['对比1','对比2'],
      viewData1: null,
      viewData2: null,
      onecheck: true,
      twocheck: true,
      thisStyle: { width: '1650px', height: '460px', 'box-sizing': 'border-box', 'line-height': '120px' },
      gridStyle: {
        left: '4%',
        right: 10,
        bottom: 50,
        top: '15%',
        containLabel: false
      },
      chartloading: true,
      manydate: false,

      datetype: true,
      timetype: '今日',
      value2: '',
      filter: {
        checkedCities: '',
        onealltime: [],
        twoalltime: [],
        thralltime: []
      },
      filtertwo: {
        allselected: [],
        selected: ''
      },
      cities: ['1', '2'],
      anlidata: ['对比数据1','对比数据2'],
      directorlist: [],
      selviewData: [],
      pickerOptions: {
      },
      open: true,
      twoopen: true,
      thropen: true
    };
  },
  watch: {
    'filter.onealltime': {
      handler(val) {
        this.timeshow(val)
      },
      immediate: true
    },
  },
  mounted(){
    this.onetimechange(this.filter.onealltime,'onealltime')
  },
  filters: {
    // HH时mm分ss秒
    timeFormater(val) {
      return dayjs(val).format("YYYY年MM月DD日")
    }
  },
  created(){
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime());
    this.filter.onealltime = [start, end];
  },

  computed: {
  },

  methods: {
    boxchange(e){
      let _this = this;
      this.viewData1.selectedLegend = [this.filter.selected,...e];
      _this.$nextTick(() => {
        _this.$refs.achartsone.initcharts(_this.viewData1);
      });
      this.$forceUpdate();
    },
    timeshow(e){

      if(e[0]?.toString() == e[1]?.toString()){
        this.manydate = false;
        this.filter.referdate = e[0]
      }else{
        this.manydate = true;
        this.filter.onealltime = e;
      }

    },
    timechange(name,e){
      if(name == 'onetime'){
        this.onetimechange(e,'onealltime')
      }else if(name == 'twotime'){
        this.onetimechange(e,'twoalltime')
      }else if(name == 'thrtime'){
        this.onetimechange(e,'thralltime')
      }
    },
    onetimechange(e,type) {
      let _this = this;
      _this.chartloading = true;
  

      const date1 = new Date(e[0]);
      const date2 = new Date(e[1]);

      const timeDifference = date2 - date1;

      const days = Math.ceil(timeDifference / (1000 * 60 * 60 * 24));

      if(days == 7){
        this.timetype= '7日';
      }else if(days == 30){
        this.timetype= '30日';
      }else if(days == 90){
        this.timetype= '90日';
      }

      var onedata = [];
      var onedatae = [];

      if(type=='onealltime'){
        if(e[0]){
          if(e[0].toString() == e[1].toString()){
            this.manydate = false;
            this.filter.referdate = e[0]
          }else{
            this.manydate = true;
            this.filter.onealltime = e;
          }

          onedata = JSON.parse(JSON.stringify(e));
        }
        

        this.filter.twoalltime = this.changeseltime(onedata,days);
        onedatae = JSON.parse(JSON.stringify(_this.filter.twoalltime));
        this.$nextTick(()=>{
          const aaa = _this.changeseltime(onedatae,days);
          _this.filter.thralltime = aaa;
        });
      }else if(type=='twoalltime'){
        onedata = JSON.parse(JSON.stringify(e));

        this.filter.thralltime = this.changeseltime(onedata,days);

        this.$nextTick(()=>{
          const aaa = _this.addseltime(e,days);
          _this.filter.onealltime = aaa;
        });

        // this.opentwo = false;
      }else if(type=='thralltime'){
        this.filter.twoalltime = this.addseltime(e,days);

        this.$nextTick(()=>{
          const aaa = _this.addseltime(_this.filter.twoalltime,days);
          _this.filter.onealltime = aaa;
        });
      }


      


      setTimeout(()=>{
        this.getchartsone();
        this.getchartstwo();
      },500)
      
      this.$forceUpdate();
    },
    addseltime(e,days){
      const start = new Date(e[0]);
      var bbb = [];
      if(days>0){
        let end = new Date(e[1])
        let timestamps = end.getTime() + 3600 * 1000 * 24 * 1;
        let timestampe = end.getTime() + 3600 * 1000 * 24 * 1+ 3600 * 1000 * 24 * days;

        var bbb =[dayjs(timestamps).format("YYYY-MM-DD"),dayjs(timestampe).format("YYYY-MM-DD")];

      }else{
        let end = new Date(e[0])
        let timestamps = end.getTime() + 3600 * 1000 * 24 * 1 - 3600 * 1000 * 24 * days;
        let timestampe = end.getTime() + 3600 * 1000 * 24 * 1 - 3600 * 1000 * 24 * days;

        var bbb =[dayjs(timestamps).format("YYYY-MM-DD"),dayjs(timestampe).format("YYYY-MM-DD")];
      }
      return bbb;

    },
    changeseltime(e,days){
      const start = new Date(e[0]);
      var bbb = [];
      if(days>0){
        let end = new Date(e[0])
        let timestamps = start.getTime() - 3600 * 1000 * 24 * 1 - 3600 * 1000 * 24 * days;
        let timestampe = end.getTime() - 3600 * 1000 * 24 * 1;

        var bbb =[dayjs(timestamps).format("YYYY-MM-DD"),dayjs(timestampe).format("YYYY-MM-DD")];

      }else{
        let end = new Date(e[0])
        let timestamps = end.getTime() - 3600 * 1000 * 24 * 1 - 3600 * 1000 * 24 * days;
        let timestampe = end.getTime() - 3600 * 1000 * 24 * 1 - 3600 * 1000 * 24 * days;

        var bbb =[dayjs(timestamps).format("YYYY-MM-DD"),dayjs(timestampe).format("YYYY-MM-DD")];
      }


      
      return bbb;

    },
    timeFormater(val) {
      return dayjs(val).format("YYYY年MM月DD日 HH时mm分ss秒")
    },
    isdiabledfuc(e) {
      var isdia = false;
      if (e == this.filtertwo.selected) {
        isdia = true
      }
      return isdia;
    },
    selchange(type, e) {
      let _this = this;
      if (type == 'timetype') {
        this.viewData1.selectedLegend = [e,...this.anlidata];
        _this.$nextTick(() => {
          _this.$refs.achartsone.initcharts(_this.viewData1);
        });
        this.$forceUpdate();
      } else if (type == 'thingtype') {
        this.viewData2.selectedLegend = [e,...this.anlidata];
        this.filtertwo.selectedmul = [];
        _this.$nextTick(() => {
          _this.$refs.achartstwo.initcharts(_this.viewData2);
        });
      } else if (type == 'thingtypemul') {
        var a = e.concat([this.filtertwo.selected])
        this.viewData2.selectedLegend = a;
        _this.$nextTick(() => {
          _this.$refs.achartstwo.initcharts(_this.viewData2);
        });
      }

    },
    async getchartsone() {

      let params = {
        "productCode": this.rowmsg.proCode,
        "startTime": this.filter.onealltime[0]!='Invalid Date'?this.filter.onealltime[0]:'',
        "endTime": this.filter.onealltime[1]!='Invalid Date'?this.filter.onealltime[1]:'',
        "startTime2": this.filter.twoalltime[0]!='Invalid Date'?this.filter.twoalltime[0]:'',
        "endTime2": this.filter.twoalltime[1]!='Invalid Date'?this.filter.twoalltime[1]:'',
        "startTime3": this.filter.thralltime[0]!='Invalid Date'?this.filter.thralltime[0]:'',
        "endTime3": this.filter.thralltime[1]!='Invalid Date'?this.filter.thralltime[1]:''
      }
      let res = await getPddActualTimeProAnalysisAsync(params);
      if (!res.success) {
        return
      }
      const oldlegend = res.data[0].legend;

      //虚拟
        let arr = this.anlidata;
        res.data[0].legend = res.data[0].legend.concat(arr);
        res.data[0].selectedLegend = res.data[0].selectedLegend.concat(arr);

        if(res.data[1]&&res.data[2]){
          

          this.$nextTick(()=>{
            var onedata = res.data[1].series.find((item)=>{ if(item.name == this.filter.selected){ return item}})
            var twodata = res.data[2].series.find((item)=>{ if(item.name == this.filter.selected){ return item}})

            let newone = {
                ...onedata,
                name: '对比数据1'
              }
              let newtwo = {
                ...twodata,
                name: '对比数据2'
              }

              res.data[0].series[res.data[0].series.length] = newone;
              res.data[0].series[res.data[0].series.length] = newtwo;


          });
        }



        this.viewData1 = res.data[0];
        this.selviewData = oldlegend;

            
        this.filter.selected = res.data[0].selectedLegend[0];

        this.chartloading = false;


        
    },
    async getchartstwo() {

      let params = {
        "productCode": this.rowmsg.proCode,
        "startTime": this.filter.onealltime[0]!='Invalid Date'?this.filter.onealltime[0]:'',
        "endTime": this.filter.onealltime[1]!='Invalid Date'?this.filter.onealltime[1]:'',
      }
      let res = await getPddActualTimeProAnalysisAsync(params);
      if (!res.success) {
        return
      }
      this.viewData2 = res.data[0];
      this.filtertwo.selected = res.data[0].selectedLegend[0];
      this.filtertwo.allselected = res.data[0].selectedLegend;


    },
    seltype(e) {
      if (e == 'time') {
        this.datetype = true;
      } else if (e == 'target') {
        this.datetype = false;
      }
    },
    seltime(e) {
      // if(e){
        this.timetype = e;
      // }
      if (this.timetype == '今日') {
        this.manydate = false;

        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime());
        this.filter.onealltime = [start, end];
        this.filter.referdate = start;

        const starttwo =  new Date()
        const startthr =  new Date()

        starttwo.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
        startthr.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
        this.filter.twoalltime = [starttwo, starttwo];
        this.filter.thralltime = [startthr, startthr];

      } else if (this.timetype == '昨日') {
        this.manydate = false;

        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
        this.filter.onealltime = [start, start];
        this.filter.referdate = start;

        const starttwo =  new Date()
        const startthr =  new Date()
        starttwo.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
        startthr.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
        this.filter.twoalltime = [starttwo, starttwo];
        this.filter.thralltime = [startthr, startthr];

      } else if (this.timetype == '7日') {
        this.manydate = true;
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);

        this.filter.onealltime = [start, end];
        this.filter.referdate = start;

        const starttwo =  new Date()
        const startthr =  new Date()

        const startnext =  new Date()
        const startnexttwo =  new Date()

        startnext.setTime(start.getTime() - 3600 * 1000 * 24 * 1);

        starttwo.setTime(start.getTime() - 3600 * 1000 * 24 * 7 - 3600 * 1000 * 24 * 1);

        startnexttwo.setTime(start.getTime() - 3600 * 1000 * 24 * 7 - 3600 * 1000 * 24 * 2);

        startthr.setTime(start.getTime() - 3600 * 1000 * 24 * 14 - 3600 * 1000 * 24 * 2);

        this.filter.twoalltime = [starttwo, startnext];
        this.filter.thralltime = [startthr, startnexttwo];

      } else if (this.timetype == '30日') {
        this.manydate = true;
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);

        this.filter.onealltime = [start, end];
        this.filter.referdate = start;

        const starttwo =  new Date()
        const startthr =  new Date()
        
        const startnext =  new Date()
        const startnexttwo =  new Date()

        startnext.setTime(start.getTime() - 3600 * 1000 * 24 * 1);

        starttwo.setTime(start.getTime() - 3600 * 1000 * 24 * 30 - 3600 * 1000 * 24 * 1);

        startnexttwo.setTime(start.getTime() - 3600 * 1000 * 24 * 30 - 3600 * 1000 * 24 * 2);

        startthr.setTime(start.getTime() - 3600 * 1000 * 24 * 60 - 3600 * 1000 * 24 * 2);

        this.filter.twoalltime = [starttwo, startnext];
        this.filter.thralltime = [startthr, startnexttwo];

      } else if (this.timetype == '90日') {
        this.manydate = true;
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);

        this.filter.onealltime = [start, end];
        this.filter.referdate = start;

        const starttwo =  new Date()
        const startthr =  new Date()
        
        const startnext =  new Date()
        const startnexttwo =  new Date()

        startnext.setTime(start.getTime() - 3600 * 1000 * 24 * 1);

        starttwo.setTime(start.getTime() - 3600 * 1000 * 24 * 90 - 3600 * 1000 * 24 * 1);

        startnexttwo.setTime(start.getTime() - 3600 * 1000 * 24 * 90 - 3600 * 1000 * 24 * 2);

        startthr.setTime(start.getTime() - 3600 * 1000 * 24 * 180 - 3600 * 1000 * 24 * 2);

        this.filter.twoalltime = [starttwo, startnext];
        this.filter.thralltime = [startthr, startnexttwo];

      }
      this.chartloading = true;
      this.$nextTick(async()=>{
        await this.getchartsone();
        await this.getchartstwo();
      });

      this.$forceUpdate();
      
    }
  },
};
</script>

<style lang="scss" scoped>.flexrow {
  display: flex;
  flex-direction: row;
}</style>