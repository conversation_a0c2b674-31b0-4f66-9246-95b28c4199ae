<template>
  <container v-loading="pageLoading">
      <!-- :tableHandles='tableHandles'  -->
     <ces-table ref="table" :that='that' :isIndex='true' 
         :hasexpand='true' :tableData='list' :tableCols='tableCols' @sortchange='sortchange' :loading="listLoading"
         :showsummary='true' :summaryarry='summaryarry'>
      
         <template slot='extentbtn'>
      <el-button-group>
     
        
    <!-- <el-button type="primary" @click="PurchaseReturnButton">批次号删除</el-button>  -->
    <el-button type="primary" @click="onSearch">刷新</el-button> 
    <el-button type="primary" @click="onExport">导出</el-button> 
      
    <!-- <el-button type="primary" @click="onSearch">查询</el-button> -->
      </el-button-group>
       </template>
      </ces-table>
      
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
  </container>
</template>
<script>
import{getOperatingProfitAnalysisSum,exportOperatingProfitAnalysisSum}from '@/api/bookkeeper/financialreport'
import {formatTime,formatYesornoBool,formatWarehouseArea,formatIsOutStock,formatSecondToHour,formatPlatform} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import logistics from '@/components/Comm/logistics'
import goodscoderecord from '@/views/inventory/goodscoderecord'
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";
const tableCols =[
   
      {istrue:true,prop:'yearMonth',label:'年月',sortable:'custom', width:'60',},
      {istrue:true,prop:'platform',label:'平台',sortable:'custom', width:'60',formatter:(row)=> !row.platform?" ": row.platform==1?"淘系":row.platform==8?"淘工厂":row.platform==2?"拼多多":row.platform==9?"淘系":"未知"},
      {istrue:true,prop:'operating',label:'小组',sortable:'custom', width:'60',},
       {istrue:true,prop:'settlementAmount',label:'结算金额', width:'80',},
      {istrue:true,prop:'amountOut',label:'退款金额',sortable:'custom', width:'90',},
      {istrue:true,prop:'amountOutRet',label:'退款率', width:'90',},
      {istrue:true,prop:'netSales',label:'净销售额', width:'80',},
      {istrue:true,prop:'netSalesRateSumNetSales',label:'净销售额占比总净销售额', width:'80',formatter:(row)=> !row.netSalesRateSumNetSales?" ": (row.netSalesRateSumNetSales).toFixed(2)+'%'},
       {istrue:true,prop:'saleCostAmount',label:'结算成本', width:'90',},
       {istrue:true,prop:'replacementAmount',label:'补发成本',sortable:'custom', width:'80',},
      {istrue:true,prop:'abnormalAmount',label:'异常成本',sortable:'custom', width:'80',},
      {istrue:true,prop:'amountGrossProfitSale',label:'毛一',sortable:'custom', width:'80',},
      {istrue:true,prop:'amountGrossProfitSaleRatess',label:'毛一占比总毛一', width:'60',},
       {istrue:true,prop:'amountGrossProfitSaleRate',label:'毛一利率', width:'80',formatter:(row)=> !row.amountGrossProfitSaleRate?" ": (row.amountGrossProfitSaleRate).toFixed(2)+'%'},
       {istrue:true,prop:'courierFees',label:'快递费',sortable:'custom', width:'80',},
       {istrue:true,prop:'courierFeesWgkk',label:'其中：快递罚款', width:'120',},
       //包装费==辅料
        {istrue:true,prop:'packageFee',label:'辅料',sortable:'custom', width:'80',},
       //产品运费==物流费
       {istrue:true,prop:'productFreightfee',label:'物流费',sortable:'custom', width:'80',},
       {istrue:true,prop:'billTotalAmont',label:'账单费用',sortable:'custom', width:'80',},
       {istrue:true,prop:'billsFeeRate',label:'账单费用费率', width:'120',},
       {istrue:true,prop:'amontWagesGroup',label:'小组运营工资', width:'120',},
       {istrue:true,prop:'groupFeeRates',label:'小组运营工资占比销售额', width:'120',},
       {istrue:true,prop:'platformViolationsDeductions',label:'其中：扣款', width:'100',},
       {istrue:true,prop:'courierFeesRate',label:'快递费率', width:'100',},
       {istrue:true,prop:'operatingFeeTotal',label:'推广费',sortable:'custom', width:'80',},
       {istrue:true,prop:'promotionFeeRate',label:'推广费率', width:'100',},
       {istrue:true,prop:'grossProfit',label:'毛三',sortable:'custom', width:'80',},
       {istrue:true,prop:'profit3Ratess',label:'毛三占比总毛三', width:'80',},
       {istrue:true,prop:'profit3Rate',label:'毛三利率', width:'100',formatter:(row)=> !row.profit3Rate?" ": (row.profit3Rate).toFixed(2)+'%'},
       {istrue:true,prop:'amontAdditionfee',label:'除运营工资、美工提成、采购提成工资', width:'100',sortable:'custom'},
       {istrue:true,prop:'rent',label:'房租', width:'100',},
       {istrue:true,prop:'otherFee',label:'其他费用', width:'80',},
       {istrue:true,prop:'netProfit',label:'净利润',sortable:'custom', width:'80',},
       {istrue:true,prop:'netProfitRateSumNetProfit',label:'净利润占比总净利润', width:'80',formatter:(row)=> !row.netProfitRateSumNetProfit?" ": (row.netProfitRateSumNetProfit*100).toFixed(2)+'%'},
       {istrue:true,prop:'netProfitRate',label:'净利率', width:'80',formatter:(row)=> !row.netProfitRate?" ": (row.netProfitRate).toFixed(2)+'%'},
    
     ];


export default {
  name: "Users",
  components: {container,cesTable,MyConfirmButton,logistics},
   props:{
       filter: { }
     },
  data() {
    return {
    
    //   sels2:[],


      that:this,
    //   formatWarehouseArea:formatWarehouseArea,
    //   formatYesornoBool:formatYesornoBool,
      formatTime:formatTime,
    //   formatIsOutStock:formatIsOutStock,
    //   formatSecondToHour:formatSecondToHour,
      // filter: {
      //   timerange:'',
       
      // },
    //   goodscoderecordfilter:{goodsCode:"",buyNo:""},
    //   imgPreview:{img:"",show:false},
    
      list: [],
    //   detaillist:[],
    //   oderDetailView:{},
    //   drawervisible:false,
    //   dialoganalysisVisible:false,
    //   visiblepopover: false,
    //   prevTarget: null, // 编辑 Popover 的 Reference （参照），用于 popover.js 对齐两个元素
    //   popperFlag: false, // 用于编辑 Popover 的刷新
    //   visiblepopoverdetail: false,
    //   dialogOrderDetailVisible:false,
    //   popperFlagdetail: false,
      tableCols:tableCols,
      pager:{OrderBy:"YearMonth",IsAsc:false},
      summaryarry:{},
      total:0,
    //   total1:0,
      sels: [],
      selids: [],
      fileList:[],
      listLoading: false,
      pageLoading: false,
      importnumber:0
    //   editVisible:false,
    //   editLoading:false,
    //   hackReset:false,
    //   goodscoderecord1id: +new Date(),
    };
  },
  watch: {
    value(n) {
      if(n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    }
  },
 async mounted() {

    await this.onSearch();
    await this.getlist();
  },
 methods: {
//    onSelsChangeReturnGoods(sels){
//     this.sels2 = sels

//    },
   
 
   //导出
   async onExport() {
     if (this.onExporting) return;
     try{
       if (this.filter.yearMonth && this.filter.yearMonth.length>0) {
                this.filter.startTime = this.filter.yearMonth[0];
                this.filter.endTime = this.filter.yearMonth[1];
            }
      
        let pager = this.$refs.pager.getPager()
        let params = {...pager,...this.pager,... this.filter}
        params.SeriesCoding=this.filter.SeriesCoding.join();
        let res= await exportOperatingProfitAnalysisSum(params);
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','运营利润汇总_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },
  
  
//   async init(data){
//       const params={
//          SupplierName:data
//       }
//       var res2= await getAllSupplier(params);
//       this.suppilelist = res2.data.map(item => {
//           return { value: item.key, label: item.value };
//       });
//     },
   async onSearch() {
       this.$refs.pager.setPage(1)
       this.getlist()
    },
   async getlist() {
     this.filter.startTime =null;
       this.filter.endTime =null;
       if (this.filter.yearMonth && this.filter.yearMonth.length>0) {
                this.filter.startTime = this.filter.yearMonth[0];
                this.filter.endTime = this.filter.yearMonth[1];
            }
            
            
      if (!this.pager.OrderBy) this.pager.OrderBy="";
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      params.SeriesCoding=this.filter.SeriesCoding.join();
      this.listLoading = true
      // console.log("最终参数",this.filter)
      const res = await getOperatingProfitAnalysisSum(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {d._loading = false})
      this.list = data
      this.summaryarry=res.data.summary;
    },

   onDisPlay(row){
     return row.isHandle==true;
    },

  
    beforeRemove() {
      return false;
    },
    
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   selsChange: function(sels) {
      this.sels = sels
    },
   selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.proBianMa);
      })
    },
   doCopy: function (val) {       
      let that=this;                         
      this.$copyText(val).then(function (e) {
          that.$message({ message: "内容已复制到剪切板！", type: "success" });
      }, function (e) {
          that.$message({ message: "抱歉，复制失败！", type: "warning" });
      })
    },
  },
};
</script>


