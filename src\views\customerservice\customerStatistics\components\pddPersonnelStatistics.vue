<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-input v-model="ListInfo.groupName" placeholder="组名称" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.sname" placeholder="姓名" maxlength="50" clearable class="publicCss" />
                <el-date-picker v-model="timeList" type="daterange" align="right" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false" :picker-options="pickerOptions"
                    @change="changeTime" class="publicCss" />
                <el-button type="primary" @click="getList('click')">查询</el-button>
            </div>
        </template>
        <vxetablebase :id="'pddPersonnelStatistics202408041501'" ref="personTable" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :showsummary="true" :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" :noToFixed="true" :summaryarry="summaryarry" @summaryClick='onsummaryClick'
            style="width: 100%; height: 690px; margin: 0" />
        <my-pagination ref="pager" :total="total" @page-change="detailPagechange" @size-change="detailSizechange" />

        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false" direction="btt">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" @change="chatSearch" :picker-options="pickerOptions" style="margin: 10px;" />
                <buschar :analysisData="chatProp.data" v-if="!chatProp.chatLoading"></buschar>
            </div>
            <div v-else v-loading="chatProp.chatLoading"></div>
        </el-drawer>

        <el-dialog title="个人绩效按店统计" :visible.sync="dialogVisable" width="50%" :close-on-click-modal="false" v-dialogDrag>
            <el-date-picker v-model="logTimeList" type="daterange" align="right" unlink-panels range-separator="至"
                :clearable="false" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                @change="changeLogTime" class="publicCss" style="margin-bottom: 20px;" />
            <vxetablebase :id="'pddPersonnelStatistics202408041501_2'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
                @sortchange='sortchange1' :showsummary="true" :tableData='dialogData' :tableCols='dialogTableCols'
                :isSelection="false" :isSelectColumn="false" :summaryarry="dialogInfo.summaryarry"
                style="width: 100%; height: 690px; margin: 0" />
            <my-pagination ref="pager" :total="dialogInfo.total" @page-change="dialogPagechange"
                @size-change="dialogSizechange" />
        </el-dialog>

        <el-dialog title="名次店铺统计" :visible.sync="sortDialogVisable" width="50%" :close-on-click-modal="false" v-dialogDrag>
            <el-date-picker v-model="sortLogTimeList" type="daterange" align="right" unlink-panels range-separator="至"
                :clearable="false" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                @change="changeSortTime" class="publicCss" style="margin-bottom: 20px;" />
            <vxetablebase :id="'pddPersonnelStatistics202408041501_3'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
                @sortchange='sortchange2' :showsummary="true" :tableData='sortData' :tableCols='sortTableCols'
                :isSelection="false" :isSelectColumn="false" :summaryarry="sortDialogInfo.summaryarry"
                style="width: 100%; height: 690px; margin: 0" />
            <my-pagination ref="pager" :total="sortDialogInfo.total" @page-change="sortPagechange"
                @size-change="sortSizechange" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getPddInquireGradeComputePageList_User, getPddInquireGradeComputeChat, getPddInquireGradeComputeChat_User, getPddInquireGradeComputePageList_Shop_User, getPddInquireGradeComputePageList_UserGradeDtl } from '@/api/customerservice/pddInquirs'
import buschar from '@/components/Bus/buschar'
import dayjs from 'dayjs'
import middlevue from "@/store/middle.js"
import { pickerOptions } from '@/utils/tools'
const tableCols = [
    { istrue: true, prop: 'groupName', label: '组名', sortable: 'custom', width: '100', fixed: 'left' },
    { istrue: true, prop: 'sname', label: '姓名', sortable: 'custom', width: '100', fixed: 'left', type: 'click', handle: (that, row) => that.openShopDialog(row, 'first') },
    { istrue: true, prop: 'inquirs', label: '咨询人数', sortable: 'custom', width: '100', fixed: 'left' },
    { istrue: true, prop: 'successpayrate', label: '询单转化率', width: '130', fixed: 'left', sortable: 'custom', formatter: (row) => row.successpayrate + '%' },
    { istrue: true, prop: 'gradeCommission', label: '提成', sortable: 'custom', width: '100', fixed: 'left' },
    { istrue: true, prop: 'gradeNum1', label: '第一名', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openPensonDialog(row, 1) },
    { istrue: true, prop: 'gradeNum2', label: '第二名', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openPensonDialog(row, 2) },
    { istrue: true, prop: 'gradeNum3', label: '第三名', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openPensonDialog(row, 3) },
    { istrue: true, prop: 'gradeNum4', label: '第四名', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openPensonDialog(row, 4) },
    { istrue: true, prop: 'gradeNum5', label: '第五名', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openPensonDialog(row, 5) },
    { istrue: true, prop: 'gradeNum6', label: '第六名', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openPensonDialog(row, 6) },
    { istrue: true, prop: 'gradeNum7', label: '第七名', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openPensonDialog(row, 7) },
    { istrue: true, prop: 'gradeNum8', label: '第八名', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openPensonDialog(row, 8) },
    { istrue: true, prop: 'gradeNum9', label: '第九名', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openPensonDialog(row, 9) },
    { istrue: true, prop: 'gradeNum10', label: '第十名', sortable: 'custom', width: '100', type: 'click', handle: (that, row) => that.openPensonDialog(row, 10) },
    { istrue: true, prop: 'gradeNum11', label: '第十一名', sortable: 'custom', width: '80', type: 'click', handle: (that, row) => that.openPensonDialog(row, 11) },
    { istrue: true, prop: 'gradeNum12', label: '第十二名', sortable: 'custom', width: '80', type: 'click', handle: (that, row) => that.openPensonDialog(row, 12) },
    { istrue: true, prop: 'gradeNum13', label: '第十三名', sortable: 'custom', width: '80', type: 'click', handle: (that, row) => that.openPensonDialog(row, 13) },
    { istrue: true, prop: 'gradeNum14', label: '第十四名', sortable: 'custom', width: '80', type: 'click', handle: (that, row) => that.openPensonDialog(row, 14) },
    { istrue: true, prop: 'gradeNum15', label: '第十五名', sortable: 'custom', width: '80', type: 'click', handle: (that, row) => that.openPensonDialog(row, 15) },
    { istrue: true, prop: 'gradeNum16', label: '第十六名', sortable: 'custom', width: '80', type: 'click', handle: (that, row) => that.openPensonDialog(row, 16) },
    { istrue: true, prop: 'gradeNum17', label: '第十七名', sortable: 'custom', width: '80', type: 'click', handle: (that, row) => that.openPensonDialog(row, 17) },
    { istrue: true, prop: 'gradeNum18', label: '第十八名', sortable: 'custom', width: '80', type: 'click', handle: (that, row) => that.openPensonDialog(row, 18) },
    { istrue: true, prop: 'gradeNum19', label: '第十九名', sortable: 'custom', width: '80', type: 'click', handle: (that, row) => that.openPensonDialog(row, 19) },
    { istrue: true, prop: 'gradeNum20', label: '第二十名', sortable: 'custom', width: '80', type: 'click', handle: (that, row) => that.openPensonDialog(row, 20) },
    {
        istrue: true, prop: 'buchar', type: "button",  label: '趋势图',fixed:'right', summaryEvent: true, btnList: [
            { label: "查看", handle: (that, row) => that.openChat(row) },
        ]
    }
]
const dialogTableCols = [
    { istrue: true, prop: 'shopName', label: '店铺名称', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', sortable: 'custom' },
    { istrue: true, prop: 'inquirs', label: '咨询人数', sortable: 'custom' },
    { istrue: true, prop: 'successpayrate', label: '询单转化率', sortable: 'custom', formatter: (row) => row.successpayrate + '%' },
    { istrue: true, prop: 'gradeWayValue', label: '提成系数', sortable: 'custom',},
    { istrue: true, prop: 'gradeCommission', label: '提成', sortable: 'custom' },
    { istrue: true, prop: 'gradeIndex', label: '排名', sortable: 'custom' },
    { istrue: true, prop: 'effectiveDate', label: '日期', sortable: 'custom', formatter: (row) => dayjs(row.effectiveDate).format('YYYY-MM-DD') },
]

const sortTableCols = [
    { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', sortable: 'custom' },
    { istrue: true, prop: 'inquirs', label: '咨询人数', sortable: 'custom' },
    { istrue: true, prop: 'successpayrate', label: '询单转化率', sortable: 'custom', formatter: (row) => row.successpayrate + '%' },
    { istrue: true, prop: 'gradeCommission', label: '提成', sortable: 'custom' },
    { istrue: true, prop: 'gradeWayValue', label: '提成系数', sortable: 'custom' },
    { istrue: true, prop: 'gradeIndex', label: '排名', sortable: 'custom' },
    { istrue: true, prop: 'effectiveDate', label: '日期', sortable: 'custom', formatter: (row) => dayjs(row.effectiveDate).format('YYYY-MM-DD') },
]
export default {
    name: 'pddPersonnelStatistics',
    components: {
        MyContainer, vxetablebase, buschar
    },
    data() {
        return {
            tableData: [],//列表数据
            dialogData: [],//弹窗数据
            sortData: [],//名次弹窗数据
            tableCols,
            dialogTableCols,
            sortTableCols,
            that: this,
            total: 0,
            ListInfo: {//列表参数
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: 'successpayrate',//排序字段
                isAsc: false,//是否升序
                startDate: null,//开始时间
                endDate: null,//结束时间
                shopCode: null,//店铺编码
                shopCodeList: null,//店铺编码集合
                shopName: null,//店铺名称
                shopNameList: null,//店铺名称集合
                groupName: null,//组名称
                groupNameList: null,//组名称集合
                groupShortName: null,//分组归属
                snick: null,//昵称
                snickList: null,//昵称集合
                sname: null,//姓名
                snameList: null,//姓名集合
                pddInquireGradeWayDate: null,//按指定系数计算
            },
            pickerOptions,
            timeList: null,//时间范围
            logTimeList: null,//时间范围
            sortLogTimeList: null,//时间范围
            summaryarry: null,
            chatProp: {
                chatDialog: false,//趋势图弹窗
                chatTime: null,//趋势图时间
                chatLoading: true,//趋势图loading
                data: [],//趋势图数据
            },
            chatInfo: {
                groupName: null,//组别
                sname: null,//姓名
                shopName: null,//店铺
                snick: null,//昵称
                startDate: null,//开始时间
                endDate: null,//结束时间
                pddInquireGradeWayDate: null,//版本时间
            },
            dialogVisable: false,//弹窗
            dialogInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                sname: null,
                summaryarry: null,
                total: 0,
                startDate: null,//开始时间
                endDate: null,//结束时间
            },
            sortDialogInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                gradeIndex: null,
                sname: null,
                summaryarry: null,
                total: 0,
                startDate: null,//开始时间
                endDate: null,//结束时间
            },
            sortDialogVisable: false,//名次弹窗
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.$refs.personTable.$refs.xTable.hideColumn(this.$refs.personTable.$refs.xTable.getColumnByField('gradeNum11'))
            this.$refs.personTable.$refs.xTable.hideColumn(this.$refs.personTable.$refs.xTable.getColumnByField('gradeNum12'))
            this.$refs.personTable.$refs.xTable.hideColumn(this.$refs.personTable.$refs.xTable.getColumnByField('gradeNum13'))
            this.$refs.personTable.$refs.xTable.hideColumn(this.$refs.personTable.$refs.xTable.getColumnByField('gradeNum14'))
            this.$refs.personTable.$refs.xTable.hideColumn(this.$refs.personTable.$refs.xTable.getColumnByField('gradeNum15'))
            this.$refs.personTable.$refs.xTable.hideColumn(this.$refs.personTable.$refs.xTable.getColumnByField('gradeNum16'))
            this.$refs.personTable.$refs.xTable.hideColumn(this.$refs.personTable.$refs.xTable.getColumnByField('gradeNum17'))
            this.$refs.personTable.$refs.xTable.hideColumn(this.$refs.personTable.$refs.xTable.getColumnByField('gradeNum18'))
            this.$refs.personTable.$refs.xTable.hideColumn(this.$refs.personTable.$refs.xTable.getColumnByField('gradeNum19'))
            this.$refs.personTable.$refs.xTable.hideColumn(this.$refs.personTable.$refs.xTable.getColumnByField('gradeNum20'))
        })
        this.ListInfo = {//列表参数
            currentPage: 1,//当前页
            pageSize: 50,//每页条数
            orderBy: 'successpayrate',//排序字段
            isAsc: false,//是否升序
            startDate: null,//开始时间
            endDate: null,//结束时间
            shopCode: null,//店铺编码
            shopCodeList: null,//店铺编码集合
            shopName: null,//店铺名称
            shopNameList: null,//店铺名称集合
            groupName: null,//组名称
            groupNameList: null,//组名称集合
            groupShortName: null,//分组归属
            snick: null,//昵称
            snickList: null,//昵称集合
            sname: null,//姓名
            snameList: null,//姓名集合
            pddInquireGradeWayDate: null,//按指定系数计算
        },
            this.getList()
        middlevue.$on('userGetlist', (e) => {
            this.ListInfo.groupName = e.groupName
            this.ListInfo.startDate = e.startDate
            this.ListInfo.endDate = e.endDate
            this.timeList = [e.startDate, e.endDate]
            this.getList()
        })
        // this.$refs.personTable.$refs.xTable.hideColumn(['groupName'])

        console.log(this.$refs.personTable.$refs.xTable, 'this.$refs.personTable.$refs.xTable');
    },
    //组件销毁的时候,将事件移除掉
    beforeDestroy() {
        middlevue.$off('userGetlist')
    },
    methods: {
        changeSortTime(e) {
            this.sortLogTimeList = e
            if (e) {
                this.sortDialogInfo.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                this.sortDialogInfo.endDate = dayjs(e[1]).format('YYYY-MM-DD')
            } else {
                this.sortDialogInfo.startDate = null
                this.sortDialogInfo.endDate = null
            }
            this.openPensonDialog(this.sortDialogInfo, this.sortDialogInfo.gradeIndex, true)
        },
        async openPensonDialog(row, grade, sort) {
            if (!sort) {
                this.sortLogTimeList = this.timeList
                this.sortDialogInfo.startDate = dayjs(this.sortLogTimeList[0]).format('YYYY-MM-DD')
                this.sortDialogInfo.endDate = dayjs(this.sortLogTimeList[1]).format('YYYY-MM-DD')
            }
            this.sortDialogInfo.sname = row.sname
            this.sortDialogInfo.gradeIndex = grade
            const { data, success } = await getPddInquireGradeComputePageList_UserGradeDtl(this.sortDialogInfo)
            if (success) {
                this.sortData = data.list
                this.sortDialogInfo.summaryarry = data.summary
                this.sortDialogInfo.total = data.total
                this.sortDialogVisable = true
            }
        },
        async changeLogTime(e) {
            this.logTimeList = e
            if (e) {
                this.dialogInfo.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                this.dialogInfo.endDate = dayjs(e[1]).format('YYYY-MM-DD')
            } else {
                this.dialogInfo.startDate = null
                this.dialogInfo.endDate = null
            }
            const { data, success } = await getPddInquireGradeComputePageList_Shop_User(this.dialogInfo)
            if (success) {
                this.dialogData = data.list
                this.dialogVisable = true
                this.dialogInfo.summaryarry = data.summary
                this.dialogInfo.total = data.total
            }
        },
        async openShopDialog(row, type) {
            if (type == 'first') {
                this.dialogInfo.currentPage = 1
                this.dialogInfo.pageSize = 50
                this.logTimeList = this.timeList
            }
            this.dialogInfo.startDate = dayjs(this.logTimeList[0]).format('YYYY-MM-DD')
            this.dialogInfo.endDate = dayjs(this.logTimeList[1]).format('YYYY-MM-DD')
            this.dialogInfo.sname = row.sname
            const { data, success } = await getPddInquireGradeComputePageList_Shop_User(this.dialogInfo)
            if (success) {
                this.dialogData = data.list
                this.dialogVisable = true
                this.dialogInfo.summaryarry = data.summary
                this.dialogInfo.total = data.total
            }
        },
        //趋势图时间改变
        async chatSearch() {
            this.chatInfo.startDate = dayjs(this.chatProp.chatTime[0]).format('YYYY-MM-DD')
            this.chatInfo.endDate = dayjs(this.chatProp.chatTime[1]).format('YYYY-MM-DD')
            this.chatProp.chatLoading = true
            const data = await getPddInquireGradeComputeChat(this.chatInfo)
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        clearInfo() {
            this.chatInfo = {
                groupName: null,//组别
                sname: null,//姓名
                shopName: null,//店铺
                snick: null,//昵称
                startDate: null,//开始时间
                endDate: null,//结束时间
                pddInquireGradeWayDate: null,//版本时间
            }
        },
        async onsummaryClick(property) {
            this.chatInfo.groupName = null
            this.chatInfo.sname = null
            this.chatInfo.shopName = null
            this.chatInfo.snick = null
            this.chatInfo.pddInquireGradeWayDate = this.ListInfo.pddInquireGradeWayDate ? this.ListInfo.pddInquireGradeWayDate : null
            this.publicChangeTime(this.chatInfo)
            this.chatProp.chatLoading = true
            if (property == 'buchar') {
                const data = await getPddInquireGradeComputeChat(this.chatInfo)
                this.chatProp.data = data
                this.chatProp.chatDialog = true
                this.chatProp.chatLoading = false
            }
        },
        publicChangeTime(row) {
            if (this.timeList) {
                let time = dayjs(this.timeList[1]).diff(dayjs(this.timeList[0]), 'day')
                if (time >= 30) {
                    this.chatProp.chatTime = this.timeList
                    this.chatInfo.startDate = dayjs(this.timeList[0]).format('YYYY-MM-DD')
                    this.chatInfo.endDate = dayjs(this.timeList[1]).format('YYYY-MM-DD')
                } else {
                    //否则就从timeList的结束时间往前推30天
                    this.chatProp.chatTime = [dayjs(this.timeList[1]).subtract(30, 'day').format('YYYY-MM-DD'), this.timeList[1]]
                    this.chatInfo.startDate = dayjs(this.timeList[1]).subtract(30, 'day').format('YYYY-MM-DD')
                    this.chatInfo.endDate = dayjs(this.timeList[1]).format('YYYY-MM-DD')
                }
            }
            this.chatInfo.groupName = row.groupName ? row.groupName : this.ListInfo.groupName
            this.chatInfo.sname = row.sname ? row.sname : this.ListInfo.sname
        },
        async openChat(row) {
            this.clearInfo()
            //如果this.timeList的时间间隔大于等于三十天,那么就提示时间间隔不能大于三十天
            this.publicChangeTime(row)
            this.chatProp.chatLoading = true
            const data = await getPddInquireGradeComputeChat_User(this.chatInfo)
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        changeTime(e) {
            if (e) {
                this.ListInfo.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs(e[1]).format('YYYY-MM-DD')
            } else {
                this.ListInfo.startDate = null
                this.ListInfo.endDate = null
            }
            this.getList()
        },
        async getList(type) {
            if (type == 'click') {
                this.ListInfo.currentPage = 1
                this.ListInfo.orderBy = 'successpayrate'
                this.ListInfo.isAsc = false
            }
            if (this.ListInfo.groupName) {
                this.ListInfo.groupName = this.ListInfo.groupName.replace(/\s+/g, "")
            }
            if (this.ListInfo.sname) {
                this.ListInfo.sname = this.ListInfo.sname.replace(/\s+/g, "")
            }
            if (!this.timeList) {
                //默认时间为当前时间往前推一个月
                this.ListInfo.startDate = dayjs().subtract(1, 'month').format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
                this.timeList = [this.ListInfo.startDate, this.ListInfo.endDate]
            }
            const { data, success } = await getPddInquireGradeComputePageList_User(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
            }
        },
        //页面数量改变
        detailSizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        detailPagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        //页面数量改变
        dialogSizechange(val) {
            this.dialogInfo.currentPage = 1;
            this.dialogInfo.pageSize = val;
            this.openShopDialog(this.dialogInfo);
        },
        //当前页改变
        sortPagechange(val) {
            this.sortDialogInfo.currentPage = val;
            this.openPensonDialog(this.sortDialogInfo, this.sortDialogInfo.gradeIndex, true);
        },
        //页面数量改变
        sortSizechange(val) {
            this.sortDialogInfo.currentPage = 1;
            this.sortDialogInfo.pageSize = val;
            this.openPensonDialog(this.sortDialogInfo, this.sortDialogInfo.gradeIndex, true);
        },
        //当前页改变
        dialogPagechange(val) {
            this.dialogInfo.currentPage = val;
            this.openShopDialog(this.dialogInfo);
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        sortchange1({ order, prop }) {
            if (prop) {
                this.dialogInfo.orderBy = prop
                this.dialogInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.openShopDialog(this.dialogInfo)
            }
        },
        sortchange2({ order, prop }) {
            if (prop) {
                this.sortDialogInfo.orderBy = prop
                console.log(this.dialogInfo.orderBy, 'this.dialogInfo.orderBy ');
                this.sortDialogInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.openPensonDialog(this.sortDialogInfo, this.sortDialogInfo.gradeIndex, true)
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.top {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.publicCss {
    width: 220px;
    margin-right: 20px;
}
</style>
