1
<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
            </el-form>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-select v-model="Filter.GroupNameList" placeholder="分组" filterable multiple clearable
                        collapse-tags>
                        <el-option v-for="item in filterGroupList" :key="item" :label="item" :value="item"></el-option>
                    </el-select>
                </el-button>
                <!-- <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="Filter.Groupname" placeholder="分组" style="width:120px;" />
                </el-button> -->
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="Filter.Sname" placeholder="姓名" style="width:120px;" />
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="Filter.Snick" placeholder="昵称" style="width:120px;" />
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="Filter.Shopname" placeholder="店铺" style="width:120px;" />
                </el-button>

                <el-button style="padding: 0;margin: 0;">

                    <el-switch :width="40" @change="changeingroup" v-model="Filter.isleavegroup"
                        inactive-color="#228B22" active-text="包含离组" inactive-text="当前在组">
                    </el-switch>

                </el-button>
                <el-button style="padding: 0;margin: 0;">

                    <el-select v-model="Filter.isGrade" placeholder="是否参与计算绩效" clearable>
                        <el-option v-for="item in options" :key="item.label" :label="item.label" :value="item.label">
                        </el-option>
                    </el-select>

                </el-button>
                <!-- <el-button type="text" size="medium" disabled style="color: red;">周转对比:</el-button>
<el-button style="padding: 0;margin: 0;">
<el-input-number v-model="filter1.startdiff"></el-input-number>至<el-input-number v-model="filter1.enddiff"></el-input-number>
</el-button> -->
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="addgroupdialogVisibleSyj = true">添加</el-button>
                <el-button type="primary" @click="onImportSyj">导入</el-button>
                <el-button type="primary" @click="onImportSyjModel">下载模板</el-button>
                <el-button type="primary" @click="synchronous(true)">同步参与绩效统计</el-button>
                <el-button type="primary" @click="synchronous(false)">取消同步参与绩效统计</el-button>
                <el-button type="primary" @click="customerDialog">客服等级设置</el-button>
                <el-button type="primary" @click="showEditLog">分组编辑日志</el-button>
            </el-button-group>
        </template>
        <!--列表-->
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='grouplist' @select='selectchange' :isSelection='false' :tableCols='tableCols'
            :loading="listLoading" />
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getgroupList" />
        </template>

        <el-dialog title="添加客服人员分组管理信息" :visible.sync="addgroupdialogVisibleSyj" width="25%">
            <span>
                <el-form :model="addForm" :rules="addFormRules">
                    <el-form-item prop="groupname" label="分组">
                        <el-input style="width:83%" v-model="addForm.groupname" :maxlength="255"></el-input>
                    </el-form-item>
                    <el-form-item prop="groupManager" label="组长">
                        <el-input style="width:83%" v-model="addForm.groupManager" :maxlength="255"></el-input>
                    </el-form-item>
                    <el-form-item prop="sname" label="姓名">
                        <el-input style="width:83%" v-model="addForm.sname" :maxlength="255"></el-input>
                    </el-form-item>
                    <el-form-item prop="snick" label="昵称">
                        <el-input style="width:83%" v-model="addForm.snick" :maxlength="255"></el-input>
                    </el-form-item>
                    <el-form-item prop="shopname" label="店铺">
                        <el-input style="width:83%" v-model="addForm.shopname" :maxlength="255"></el-input>
                    </el-form-item>
                    <el-form-item label="入组日期">
                        <el-date-picker v-model="addForm.JoinDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            type="date" style="width:63%" placeholder="选择日期">
                        </el-date-picker>
                        <!-- <el-input   v-model="addForm.JoinDate"></el-input> -->
                    </el-form-item>
                    <el-form-item label="离组日期">
                        <el-date-picker format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="addForm.LeaveDate"
                            style="width:63%" type="date" placeholder="选择日期">
                        </el-date-picker>
                        <!-- <el-input   v-model="addForm.LeaveDate"></el-input> -->
                    </el-form-item>
                </el-form>
            </span>

            <span slot="footer" class="dialog-footer">
                <el-button @click="addgroupdialogVisibleSyj = false">关闭</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="addgroup">确定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="修改客服人员分组管理信息" :visible.sync="updategroupdialogVisibleSyj" width="25%">
            <span>
                <el-form :model="updateForm" :rules="addFormRules">
                    <el-form-item prop="groupname" label="分组">
                        <el-input style="width:83%" v-model="updateForm.groupname"></el-input>
                    </el-form-item>
                    <el-form-item prop="groupManager" label="组长">
                        <el-input style="width:83%" v-model="updateForm.groupManager"></el-input>
                    </el-form-item>
                    <el-form-item prop="sname" label="姓名">
                        <el-input style="width:83%" v-model="updateForm.sname"></el-input>
                    </el-form-item>
                    <el-form-item prop="snick" label="昵称">
                        <el-input style="width:83%" v-model="updateForm.snick"></el-input>
                    </el-form-item>
                    <el-form-item prop="shopname" label="店铺">
                        <el-input style="width:83%" v-model="updateForm.shopname"></el-input>
                    </el-form-item>
                    <el-form-item label="入组日期">
                        <el-date-picker v-model="updateForm.joinDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            type="date" style="width:63%" placeholder="选择日期">
                        </el-date-picker>
                        <!-- <el-input   v-model="addForm.JoinDate"></el-input> -->
                    </el-form-item>
                    <el-form-item label="离组日期">
                        <el-date-picker v-model="updateForm.leaveDate" style="width:63%" format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                        </el-date-picker>
                        <!-- <el-input   v-model="addForm.LeaveDate"></el-input> -->
                    </el-form-item>
                </el-form>
            </span>

            <span slot="footer" class="dialog-footer">
                <el-button @click="updategroupdialogVisibleSyj = false">关闭</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="updategroup()">确定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="客服人员分组管理" :visible.sync="dialogVisibleSyj" width="30%">
            <span>
                <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2" :on-change="onUploadChange2"
                    :on-remove="onUploadRemove2">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                        @click="onSubmitupload2">上传</my-confirm-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="客服等级设置" :visible.sync="customerInfo.customerVisible" width="50%" :before-close="handleClose"
            v-dialogDrag>
            <div class="levelQueryInfoBox">
                <el-input v-model="levelQueryInfo.groupName" placeholder="组名" class="publicCss" maxlength="50"
                    clearable></el-input>
                <el-input v-model="levelQueryInfo.sname" placeholder="姓名" class="publicCss" maxlength="50"
                    clearable></el-input>
                <el-button type="primary" @click="customerDialog">查询</el-button>
                <el-button type="primary" @click="setLevel(customerInfo.selectProps, 'gold')">批量设置金牌</el-button>
                <el-button type="primary" @click="setLevel(customerInfo.selectProps, 'Silver')">批量设置银牌</el-button>
            </div>
            <vxetablebase :id="'group1202408041457'" ref="customerTable" @select="checkboxRangeEnd" :that='that'
                :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='customerSort'
                :tableData='customerInfo.customerData' :tableCols='customerTableCols' :isSelection="false"
                :isSelectColumn="false" style="width: 100%; height: 700px; margin: 0" />
            <my-pagination ref="pager" :total="customerInfo.total" @page-change="detailPagechange"
                @size-change="detailSizechange" />
        </el-dialog>

    </my-container>
</template>
<script>

import { importGroupAsync, getGroupList, deleteGroupBatch, addgroup, deletegroup, updategroupinfo, GetGroupNameList } from '@/api/customerservice/group'
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getTaoBaoInquireGradeUserList, addTaoBaoInquireGradeComputeSnick, batchSetTaoBaoInquireGradeUser } from '@/api/customerservice/inquirs'
const tableCols = [
    { istrue: true, label: '', type: "checkbox" },
    { istrue: true, prop: 'groupname', label: '分组', sortable: 'custom' },
    { istrue: true, prop: 'groupManager', label: '组长', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', sortable: 'custom' },
    { istrue: true, prop: 'snick', label: '昵称', sortable: 'custom' },
    { istrue: true, prop: 'shopname', label: '店铺', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'joinDate', label: '入组日期', sortable: 'custom' },
    { istrue: true, prop: 'leaveDate', label: '离组日期', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '导入时间', sortable: 'custom' },

    { istrue: true, type: "button", label: '操作', width: "120", btnList: [{ label: "编辑", handle: (that, row) => that.handleupdategroup(row) }, { label: "删除", handle: (that, row) => that.deletegroup(row) }] }
];

//客服等级列表
const customerTableCols = [
    { istrue: true, label: '', type: "checkbox" },
    { istrue: true, prop: 'groupName', label: '组名称', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', sortable: 'custom' },
    { istrue: true, prop: 'snameLevel', label: '客服等级', sortable: 'custom' },
    {
        istrue: true, label: '操作', type: 'button', width: '150', btnList: [
            { label: "设置金牌", handle: (that, row) => that.setLevel(row, 'gold') },
            { label: "设置银牌", handle: (that, row) => that.setLevel(row, 'Silver') }
        ]
    },
]
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, vxetablebase },
    data() {
        return {
            that: this,
            Filter: {
                GroupNameList: [],
                isleavegroup: false,
            },
            addForm: {},
            addFormRules: {
                groupname: [{ required: true, message: '请输入', trigger: 'blur' }],
                groupManager: [{ required: true, message: '请输入', trigger: 'blur' }],
                sname: [{ required: true, message: '请输入', trigger: 'blur' }],
                snick: [{ required: true, message: '请输入', trigger: 'blur' }],
                shopname: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
            updateForm: {},
            shopList: [],
            userList: [],
            grouplist: [],
            tableCols: tableCols,
            customerTableCols,
            total: 0,
            addgroupdialogVisibleSyj: false,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            updategroupdialogVisibleSyj: false,
            //
            dialogVisibleSyj: false,
            fileList: [],
            customerInfo: {
                customerVisible: false,//客服等级设置弹窗
                customerData: [],//客服等级设置数据
                total: 0,
                selectProps: [],
            },
            setInfo: {
                isGrade: null,
                ids: []
            },
            options: [
                {
                    label: '已参与计算绩效',
                },
                {
                    label: '未参与计算绩效',
                }
            ],
            levelQueryInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: true,
                groupName: null,
                sname: null,
            },
            filterGroupList: [],//分组集合

        };
    },
    async mounted() {
        await this.getTaoXiGroup();
    },
    methods: {
        //淘系售前——分组集合
        async getTaoXiGroup() {
            let res = await GetGroupNameList({ isleavegroup: this.Filter.isleavegroup });
            this.filterGroupList = res.data;
        },

        onImportSyjModel() {
            window.open("/static/excel/customerservice/淘系售前分组导入模板.xlsx", "_blank");
        },
        //页面数量改变
        detailSizechange(val) {
            this.levelQueryInfo.currentPage = 1;
            this.levelQueryInfo.pageSize = val;
            this.customerDialog();
        },
        //当前页改变
        detailPagechange(val) {
            this.levelQueryInfo.currentPage = val;
            this.customerDialog();
        },
        synchronous(bol) {
            if (this.setInfo.ids.length == 0) return this.$message.error('请选择要设置的人员');
            let ids = this.setInfo.ids.join(',');
            console.log(ids, 'ids');
            this.$confirm('此操作会将改人员设置, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let isGrade;
                if (bol) {
                    isGrade = '已参与计算绩效'
                    const { success } = await addTaoBaoInquireGradeComputeSnick({ ids, isGrade });
                    if (success) {
                        this.$message({
                            type: 'success',
                            message: '设置成功!'
                        });
                        this.onRefresh()
                    }
                } else {
                    isGrade = '未参与计算绩效'
                    const { success } = await addTaoBaoInquireGradeComputeSnick({ ids, isGrade });
                    if (success) {
                        this.$message({
                            type: 'success',
                            message: '已取消!'
                        });
                        this.onRefresh()
                    }
                }

            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        async setLevel(row, type) {
            let params = []
            const isArr = Array.isArray(row)
            if (isArr) {
                if (row.length == 0) return this.$message.error('请选择要设置的人员');
                params = this.customerInfo.selectProps.map(f => {
                    return {
                        groupName: f.groupName,
                        sname: f.sname,
                        snameLevel: type == 'gold' ? '金牌' : '银牌'
                    }
                })
            } else {
                if (!row) return this.$message.error('请选择要设置的人员');
                params = [{
                    groupName: row.groupName,
                    sname: row.sname,
                    snameLevel: type == 'gold' ? '金牌' : '银牌'
                }]
            }
            const { success } = await batchSetTaoBaoInquireGradeUser(params)
            if (success) {
                this.$message({
                    type: 'success',
                    message: '设置成功!'
                });
                this.customerDialog()
            }
        },
        checkboxRangeEnd(rows, row) {
            this.customerInfo.selectProps = rows;
        },
        customerSort({ order, prop }) {
            if (prop) {
                this.levelQueryInfo.orderBy = prop
                this.levelQueryInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.customerDialog()
            }
        },
        //客服等级设置
        async customerDialog() {
            const { data, success } = await getTaoBaoInquireGradeUserList(this.levelQueryInfo)
            if (success) {
                this.customerInfo.customerData = data.list
                this.customerInfo.total = data.total
                this.customerInfo.customerVisible = true
            }
        },
        handleClose() {
            this.customerInfo.customerVisible = false
        },
        async addgroup() {
            var that = this;
            // this.$confirm("确定添加分组管理数据?", "提示", {
            //   confirmButtonText: "确定",
            //   cancelButtonText: "取消",
            //   type: "warning",
            // })
            //   .then(async() => {
            //   });
            if (this.addForm.groupname == "" || this.addForm.groupname == null ||
                this.addForm.groupManager == "" || this.addForm.groupManager == null ||
                this.addForm.sname == "" || this.addForm.sname == null ||
                this.addForm.snick == "" || this.addForm.snick == null ||
                this.addForm.shopname == "" || this.addForm.shopname == null) {
                that.$message({ message: '请输入必填字段', type: "error" });
                return;
            }
            that.addForm.platform = 1;
            await addgroup(that.addForm)
            that.$message({ message: '已添加', type: "success" });
            that.onRefresh()
            that.addForm = {}
            that.addgroupdialogVisibleSyj = false
        },
        async handleupdategroup(row) {
            this.updateForm = row
            this.updategroupdialogVisibleSyj = true;
        },
        async updategroup(row) {
            if (this.updateForm.groupname == "" || this.updateForm.groupname == null ||
                this.updateForm.groupManager == "" || this.updateForm.groupManager == null ||
                this.updateForm.sname == "" || this.updateForm.sname == null ||
                this.updateForm.snick == "" || this.updateForm.snick == null ||
                this.updateForm.shopname == "" || this.updateForm.shopname == null) {
                this.$message({ message: '请输入必填字段', type: "error" });
                return;
            }
            this.updategroupdialogVisibleSyj = false;
            var that = this
            setTimeout(() => {

                updategroupinfo(that.updateForm);
            }, 200);

        },

        async deletegroup(row) {
            var that = this;
            this.$confirm("此操作将删除此客服人员分组管理数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(async () => {
                    await deletegroup({ id: row.id })
                    that.$message({ message: '已删除', type: "success" });
                    that.onRefresh()

                });

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onImportSyj() {
            this.dialogVisibleSyj = true
        },
        async onUploadChange2(file, fileList) {
            this.fileList = fileList;
        },
        async onUploadRemove2(file, fileList) {
            this.fileList = [];
        },
        async uploadFile2(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            const form = new FormData();
            form.append("upfile", item.file);
            const res = await importGroupAsync(form);
            if (res?.success) {
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
                this.dialogVisibleSyj = false;
            }
        },
        async uploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        async onSubmitupload2() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload2.submit()
        },
        onRefresh() {
            this.onSearch()
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getgroupList();
        },
        changeingroup() {
            this.$emit("callBackInfo", this.Filter.isleavegroup)
            this.getTaoXiGroup()
            this.onSearch();
        },
        async getgroupList() {
            if (this.Filter.UseDate) {
                this.Filter.startAccountDate = this.Filter.UseDate[0];
                this.Filter.endAccountDate = this.Filter.UseDate[1];
            }
            const para = { ...this.Filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
                platform: 1
            };

            console.log(para)

            this.listLoading = true;
            const res = await getGroupList(params);
            console.log(res)
            this.listLoading = false;
            console.log(res.data.list)
            //console.log(res.data.summary)

            this.total = res.data.total
            this.grouplist = res.data.list;
            //this.summaryarry=res.data.summary;
        },
        selectchange(rows, row) {
            this.setInfo.ids = [];
            rows.forEach(f => {
                this.setInfo.ids.push(f.id);
            })
            console.log(this.setInfo.ids, 'ids');
        },
        showEditLog() {
            this.$showDialogform({
                path: `@/views/customerservice/grouplogtx.vue`,
                title: '分组编辑日志',
                args: {
                },
                height: '600px',
                width: '75%',
                callOk: this.afterSave
            });
        },
        afterSave() {

        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

.levelQueryInfoBox {
    display: flex;
    margin-bottom: 10px;
}

.publicCss {
    width: 220px;
    margin-right: 10px;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
