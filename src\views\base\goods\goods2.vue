<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right" label-width="90px">
                <el-form-item label="款式编码:">
                    <el-input v-model="filter.styleCode" placeholder="款式编码" @change="onSearch" />
                </el-form-item>
                <el-form-item label="商品编码:">
                    <!-- <el-input v-model="filter.goodsCode" placeholder="商品编码" @change="onSearch" /> -->
                    <inputYunhan ref="filtergoodsCode" v-model="filter.goodsCode" :inputt.sync="filter.goodsCode" placeholder="商品编码" :clearable="true" @callback="callback" title="商品编码"></inputYunhan>
                </el-form-item>
                <el-form-item label="商品名称:">
                    <el-input v-model="filter.goodsName" placeholder="商品名称" @change="onSearch" />
                </el-form-item>
                <el-form-item label="运营组:">
                    <el-select style="width:130px;" v-model="filter.groupId" placeholder="请选择" :clearable="true" :collapse-tags="true" @change="onSearch" filterable>
                        <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="采购组:">
                    <el-select style="width:130px;" v-model="filter.brandId" placeholder="请选择" :clearable="true" :collapse-tags="true" @change="onSearch" filterable>
                        <el-option v-for="item in brandList" :key="item.key" :label="item.value" :value="item.key">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="采购费用:">
                    <el-radio-group v-model="filter.isCost" @change="changeSelect()" size="mini">
                        <el-radio-button label="0">全部</el-radio-button>
                        <el-radio-button label="1">运费</el-radio-button>
                    </el-radio-group>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>

            <el-form :model="selectForm" ref="selectForm" label-position="right" label-width="90px">
                <el-form-item label="已选商品:">
                    <div style="max-height: 50px; overflow: auto;">
                        <span v-for="(goodsGood, index) in selGoodList" :key="index" class="spangoods">
                            {{goodsGood}}
                            <a href="javascript:void(0)" class="spangoods_a" @click="onRemoveSelItem(index)">x</a>
                        </span>
                    </div>
                </el-form-item>
            </el-form>
        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' @select='selectchange' :isSelection.sync="ischoice" :tableHandles='tableHandles' :isSelectColumn="!ischoice" :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="margin: 0;" type="primary" v-if="!ischoice" @click="startImport">导入</el-button>
                </el-button-group>
            </template>
        </ces-table>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag>
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx" :http-request="uploadFile" :file-list="fileList">
                    <template #trigger>
                        <el-button size="small" type="primary">选取数据文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传</el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>

<script>
    import { formatTime } from "@/utils";
    import MyContainer from '@/components/my-container'
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import { getGroupKeyValue } from '@/api/operatemanage/base/product';
    import { getAllProBrand } from '@/api/inventory/warehouse';
    import inputYunhan from '@/components/Comm/inputYunhan';
    import {
        //分页查询店铺商品资料
        getList, getListSql,
        //导入
        importData,
    } from "@/api/inventory/basicgoods"
    const tableCols = [
        { istrue: true, prop: 'goodsCode', label: '商品编码', width: '100', sortable: 'custom', },
        { istrue: true, prop: 'goodsName', label: '商品名称', width: '230', },
        { istrue: true, prop: 'pictureBig', label: '图片', width: '60', type: 'imageGoodsCode', goods: { code: 'goodsCode', name: 'goodsName' } },
        { istrue: true, prop: 'styleCode', label: '款式编码', width: '130', sortable: 'custom', },
        { istrue: true, prop: 'shortName', label: '简称', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'costPrice', label: '成本价', width: '80', sortable: 'custom', },
        { istrue: true, prop: 'groupId', label: '运营组', width: '80', sortable: 'custom', formatter: (row) => row.groupId == 0 ? " " : row.groupName },
        { istrue: true, prop: 'modified', label: '修改时间', width: '150', sortable: 'custom', formatter: (row) => formatTime(row.modified, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'isEnabled', label: '是否启用', width: '80', sortable: 'custom', formatter: (row) => row.isEnabled == 0 ? "备用" : (row.isEnabled == 1 ? "启用" : (row.isEnabled == -1 ? "禁用" : "")) },
        { istrue: true, prop: 'weight', label: '重量', width: '60', sortable: 'custom' },
        { istrue: true, prop: 'packCount', label: '装箱数', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'expressWeight', label: '快递账单重量', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'computeRatio', label: '计算重量占比', width: '80', sortable: 'custom', formatter: (row) => !row.computeRatio ? " " : row.computeRatio * 100 + '%' },
        { istrue: true, prop: 'weightDifference', label: '重量差', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'brandId', label: '采购组', width: '80', sortable: 'custom', formatter: (row) => row.brandId == 0 ? " " : row.brandName },
        { istrue: true, prop: 'supplierCode', label: '供应商编码', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'supplierName', label: '供应商名称', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'barcode', label: '国际条码', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'color', label: '颜色', width: '60', sortable: 'custom' },
        { istrue: true, prop: 'supplierGoodsCode', label: '供应商商品编码', width: '130', sortable: 'custom' },
        { istrue: true, prop: 'supplierStyle', label: '供应商商品款号', width: '130', sortable: 'custom' },
        { istrue: true, prop: 'virtualType', label: '虚拟分类', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'goodsType', label: '商品类型', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'remark', label: '备注', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'itemType', label: '商品属性', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'stockDisabled', label: '是否禁止同步', width: '120', sortable: 'custom', formatter: (row) => row.stockDisabled == 0 ? "启用同步" : (row.stockDisabled == 1 ? "禁用同步" : (row.stockDisabled == -1 ? "部分禁用" : "")) },
        { istrue: true, prop: 'unit', label: '单位', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'labels', label: '标签', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'stockType', label: '链接同步状态', width: '120', sortable: 'custom' },
        { istrue: true, prop: 'endNum', label: '库存数量', width: '100', sortable: 'custom' },
       // { istrue: true, prop: 'qty', label: '在途数量', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'skuCodes', label: '辅助码', width: '100', sortable: 'custom' }
    ];
    const tableHandles1 = [
        // {label:"导入", handle:(that)=>that.startImport()},
        // {label:"确定选择", handle:(that)=>that.onselected()},
    ];
    export default {
        name: 'goods',
        components: { cesTable, MyContainer, MyConfirmButton, inputYunhan },
        props: {
            ischoice: { type: Boolean, default: false },
        },
        data() {
            return {
                that: this,
                filter: {
                    styleCode: null,
                    goodsCode: null,
                    goodsName: null,
                    groupId: null,
                    brandId: null,
                    isCost: 0
                },
                selectForm: { selGoodCodes: '' },
                selGoodList: [],
                list: [],
                summaryarry: {},
                pager: { OrderBy: "goodsCode", IsAsc: true },
                tableCols: tableCols,
                tableHandles: tableHandles1,
                platformList: [],
                shopList: [],
                groupList: [],
                brandList: [],
                dialogVisible: false,
                total: 0,
                sels: [],
                listLoading: false,
                pageLoading: false,
                fileList: [],
                yesnoList: [
                    { value: true, label: "是" },
                    { value: false, label: "否" }
                ],
                selrows: [],
            }
        },
        async mounted() {
            await this.setGroupSelect();
            await this.setBandSelect();
            //await this.getlist();
        },
        methods: {
            async callback(val) {
                this.filter.goodsCode = val;
                await this.onSearch();
            },
            async setGroupSelect() {
                const res = await getGroupKeyValue({});
                this.groupList = res.data;
            },
            async setBandSelect() {
                var res = await getAllProBrand();
                if (!res?.success) return;
                this.brandList = res.data;
            },
            //获取查询条件
            getCondition() {
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = { ...pager, ...page, ... this.filter }
                return params;
            },
            async changeSelect() {
                if (this.filter.isCost == 1) {
                    this.filter.goodsCode = "CGBCJ-001,CGYF";
                    await this.onSearch();
                } else {
                    this.filter.goodsCode = null;
                    this.$refs.filtergoodsCode.clear();
                }
            },
            //查询第一页
            async onSearch() {
                this.$refs.pager.setPage(1);
                await this.getlist();
                this.list.forEach(f => {
                    let obj = this.selGoodList.findIndex((v) => (v == f.goodsCode));
                    if (obj !== -1) {
                        this.$refs.table.toggleRowSelection(f);
                    }
                });
            },
            onShowChoice() {
                // this.ischoice=true;
            },
            //分页查询
            async getlist() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true
                var res = await getListSql(params);
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summary;
                data.forEach(d => {
                    d._loading = false;
                    d.id = d.goodsCode;
                })
                this.list = data
            },

            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderBy = column.prop;
                    this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selsChange: function (sels) {
                this.sels = sels
            },
            selectchange: function (rows, row) {
                //先把当前也的数据全部移除
                this.list.forEach(f => {
                    let index = this.selGoodList.findIndex((v) => (v === f.goodsCode));
                    if (index !== -1) {
                        this.selGoodList.splice(index, 1);
                        this.selrows.splice(index, 1);
                    }
                });
                //把选中的添加 
                rows.forEach(f => {
                    let index = this.selGoodList.findIndex((v) => (v === f.goodsCode));
                    if (index === -1) {
                        this.selGoodList.push(f.goodsCode);
                        this.selrows.push(f);
                    }
                });
                console.log("选中的数据",this.selrows)
            },
            onRemoveSelItem(index) {
                this.selGoodList.splice(index, 1);
                this.selrows.splice(index, 1);
                this.list.forEach(f => {
                    let obj = this.selGoodList.findIndex((v) => (v == f.goodsCode));
                    if (obj === -1) {
                        this.$refs.table.toggleRowSelection(f, false);
                    }
                });
            },
            async removeSelData() {
                this.selGoodList = [];
                this.selrows = [];
                this.$refs.table.clearSelection()
            },
            async getchoicelist() {
                if (!this.selrows || this.selrows.length == 0)
                    this.$message({ message: "你还没有选择", type: "warning", });
                return this.selrows
            },
            async getchoicelistOnly() {
                if (!this.selrows || this.selrows.length == 0)
                    this.$message({ message: "请选择一条数据，", type: "warning", });
                if (!this.selrows || this.selrows.length > 1)
                    this.$message({ message: "只能选择一条数据", type: "warning", });
                return this.selrows
            },
            startImport() {
                this.dialogVisible = true;
            },
            cancelImport() {
                this.dialogVisible = false;
            },
            beforeRemove() {
                return false;
            },
            //上传成功
            uploadSuccess(response, file, fileList) {
                if (response.code == 200) {
                } else {
                    fileList.splice(fileList.indexOf(file), 1);
                }
            },
            submitUpload() {
                this.$refs.upload.submit();
            },
            async uploadFile(item) {
                const form = new FormData();
                form.append("token", this.token);
                form.append("upfile", item.file);
                const res = await importData(form);
                if (res?.success) {
                    this.$message({ message: '上传成功,正在导入中...', type: "success", });
                }
                else {
                    this.$message({ message: res.message, type: "warning", });
                }
            },
        }
    }
</script>
<style scoped>
    .spangoods {
        display: block;
        float: left;
        margin-left: 10px;
    }
    .spangoods_a {
        border-radius: 5px;
        font-family: "微软雅黑";
        width: 8px;
        height: 8px;
        border: 1px solid #ccc;
        display: block;
        float: right;
        line-height: 4px;
        text-align: center;
        color: #ccc;
        font-size: 12px;
        margin-left: 3px;
    }
    ::v-deep .el-link.el-link--primary {
        margin-right: 7px;
    }
</style>
