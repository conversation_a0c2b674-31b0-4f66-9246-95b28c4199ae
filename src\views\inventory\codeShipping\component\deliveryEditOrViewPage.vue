<template>
    <MyContainer>
        <el-form :model="ruleform" status-icon :rules="rules" ref="ruleform" label-width="100px" class="demo-ruleform"
            :disabled="isView" v-loading="formLoading">
            <el-form-item label="采购单号:">
                <div>{{ ruleform.buyNo }}</div>
            </el-form-item>
            <el-form-item label="不包邮运费:" v-if="payment == '寄付'">
                <div>{{ ruleform.postageChargeFreightFee }}</div>
            </el-form-item>
            <el-form-item label="不包邮运费:" prop="postageChargeFreightFee" v-if="payment == '仓库到付'">
                <el-input-number v-model="ruleform.postageChargeFreightFee" placeholder="不包邮运费" :min="0" :max="9999.999"
                    :precision="4" :controls="false" style="width: 120px;" @change="changeMoney" />
            </el-form-item>
            <el-form-item label="配送方式:" prop="deliveryMethod">
                <el-select v-model="ruleform.deliveryMethod" placeholder="配送方式" class="publicCss" clearable>
                    <el-option label="物流自提" :value="1" />
                    <el-option label="送货上门" :value="2" />
                </el-select>
            </el-form-item>
            <el-form-item label="运费凭证:" prop="chatUrls" v-if="payment == '仓库到付'">
                <div class="chatPicUrl">
                    <uploadimgFile ref="uploadimgFile" :disabled="isView" :ispaste="!isView" :noDel="isView"
                        :accepttyes="accepttyes" :isImage="true" :uploadInfo="ruleform.chatUrls" :keys="[1, 1]"
                        v-if="uploadimgFileVisable" @callback="getImg" :imgmaxsize="9" :limit="9" :multiple="true">
                    </uploadimgFile>
                    <span class="picTips" v-if="!isView">提示:点击灰框可直接贴图,最多可上传9张！！！</span>
                </div>
            </el-form-item>
            <el-form-item label="报价凭证:" prop="quotationVoucherUrl">
                <div class="chatPicUrl">
                    <uploadimgFile ref="uploadimgFile" :disabled="isView" :ispaste="!isView" v-if="uploadimgFileVisable"
                        :accepttyes="accepttyes" :isImage="true" :uploadInfo="ruleform.quotationVoucherUrl"
                        :keys="[1, 1]" @callback="quotationVoucherGet" :imgmaxsize="9" :limit="9" :multiple="true">
                    </uploadimgFile>
                    <span class="picTips" v-if="!isView">提示:点击灰框可直接贴图,最多可上传9张！！！</span>
                </div>
            </el-form-item>
            <el-form-item label="备注:">
                <el-input v-model="ruleform.remark" type="textarea" placeholder="备注" maxlength="500" clearable
                    style="width: 500px;" show-word-limit/>
            </el-form-item>
            <div style="height: 400px;">
                <el-table :data="ruleform.goodsDtl" style="width: 100%" height="400" max-height="400">
                    <el-table-column type="index" width="50" />
                    <el-table-column prop="goodsCode" label="商品编码" />
                    <el-table-column prop="goodsName" label="商品名称" width="220" />
                    <el-table-column prop="qty" label="数量" width="50" />
                    <el-table-column prop="postageFreePrice" label="包邮单价">
                        <template #header="{ column }">
                            <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>包邮单价
                        </template>
                        <template #default="{ row, $index }">
                            <el-input-number v-model="row.postageFreePrice" placeholder="包邮单价" :min="0" :max="9999.999"
                                :precision="6" @change="changeMoney(row)" :controls="false" style="width: 120px;"
                                :disabled="row.isCanEdit == 0" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="postageChargePrice" label="不包邮单价" />
                    <el-table-column prop="postageFreePriceDiff" label="包邮单价差额" align="center" />
                    <el-table-column prop="bytotal" label="包邮产品金额" align="center" />
                    <el-table-column prop="bbytotal" label="不包邮产品金额" align="center" />
                    <el-table-column prop="payment" label="编码状态" align="center" />
                </el-table>
            </div>
            <div class="msgBox">
                <div class="msgBox_item">
                    包邮/不包邮总计金额: {{ ruleform.bytotalAmount }}/{{ ruleform.bbytotalAmount }}
                </div>
                <div class="msgBox_item">
                    <div>包邮差额总计金额:{{ ruleform.totalAmountDiff }}</div>
                    <div class="toolTips">包邮总计金额-不包邮总计金额</div>
                    <div class="toolTips">计算结果小于等于0的,包邮划算</div>
                </div>
                <div class="msgBox_item">
                    计算结果: <span style="color: blue;">{{ ruleform.csResult }}</span>
                </div>
            </div>
            <el-form-item v-if="!isView" class="btnGroup">
                <el-button @click="close">取消</el-button>
                <el-button type="primary" @click="submitForm('ruleform')">提交</el-button>
            </el-form-item>
        </el-form>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import decimal from '@/utils/decimal'
import { getPurchaseOrderGoodsPostageByCharge, savePurchaseOrderGoodsPostageByCharge } from '@/api/inventory/purchaseOrderGoodsPostage'
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, uploadimgFile
    },
    props: {
        isView: {
            type: Boolean,
            default: false
        },
        payment: {
            type: String,
            default: '寄付'
        },
        buyNo: {
            type: String,
            default: ''
        },
        payment: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            rules: {
                deliveryMethod: [
                    { required: true, message: '请选择配送方式', trigger: 'change' }
                ],
                postageChargeFreightFee: [
                    { required: true, message: '请输入不包邮运费', trigger: 'change' }
                ],
                chatUrls: [
                    { required: true, message: '请上传运费凭证', trigger: 'change' }
                ],
                quotationVoucherUrl: [
                    { required: true, message: '请上传报价凭证', trigger: 'change' }
                ]
            },
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            ruleform: {
                deliveryMethod: null,//采购单号
                postageChargeFreightFee: null,//不包邮运费
                buyNo: null,//配送方式
                quotationVoucher: '',//报价凭证
                freightVoucher: '',//运费凭证
                csResult: '',//计算结果
                goodsDtl: [],
                quotationVoucherUrl: [],//报价凭证
                chatUrls: [],//运费凭证
            },
            queryInfo: {
                buyNo: null,
                payment: null
            },
            formLoading: true,
            uploadimgFileVisable: false,
        }
    },
    async mounted() {
        console.log(this.isView, 'this.isView');
        this.queryInfo.buyNo = this.buyNo
        this.queryInfo.payment = this.payment
        await this.getList()
        this.uploadimgFileVisable = true
    },
    methods: {
        //计算包邮差额单价
        changeMoney(row) {
            if (row && typeof row === 'object') {
                if (row.postageFreePrice === null || row.postageFreePrice === '') {
                    row.postageFreePrice = undefined
                }
                if (row.postageChargePrice === null || row.postageChargePrice === '') {
                    row.postageChargePrice = undefined
                }
                row.postageFreePriceDiff = row.postageFreePrice && row.postageChargePrice ? decimal(row.postageFreePrice, row.postageChargePrice, 6, '-') : null
            }
            //校验单价是否全部都填写
            this.ruleform.goodsDtl.forEach((item, i) => {
                if (item.postageFreePrice === null || item.postageFreePrice === undefined || item.postageFreePrice === '') {
                    item.postageFreePrice = 0
                }
                //计算包邮差额单价
                item.postageFreePriceDiff = decimal(item.postageFreePrice, item.postageChargePrice, 6, '-')
                // 计算单行包邮总计金额  包邮产品金额=包邮单价*数量
                item.bytotal = this.$set(item, 'bytotal', decimal(item.postageFreePrice, item.qty, 4, '*'))
                // 计算单行不包邮总计金额 不包邮单价*数量
                item.bbytotal = this.$set(item, 'bbytotal', decimal(item.postageChargePrice, item.qty, 4, '*'))
            })

            //计算包邮和不包邮金额总和
            const result = this.ruleform.goodsDtl.reduce((prev, item) => {
                prev.bytotal = decimal(prev.bytotal, item.bytotal, 4, '+')
                prev.bbytotal = decimal(prev.bbytotal, item.bbytotal, 4, '+')
                return prev
            }, { bytotal: 0, bbytotal: 0 })

            // 计算出来的结果赋值
            this.ruleform.bytotalAmount = result.bytotal
            //不包邮总计金额=包邮总计金额+不包邮运费
            this.ruleform.bbytotalAmount = decimal(result.bbytotal, this.ruleform.postageChargeFreightFee, 4, '+')
            //计算包邮差额总计金额 = 包邮总计金额-不包邮总计金额
            this.ruleform.totalAmountDiff = decimal(this.ruleform.bytotalAmount, this.ruleform.bbytotalAmount, 4, '-')
            //如果小于0,包邮划算,反之不划算
            this.ruleform.csResult = this.ruleform.totalAmountDiff <= 0 ? '包邮划算' : '包邮不划算'
            this.$forceUpdate();//强制刷新
        },
        getImg(data) {
            if (data) {
                this.ruleform.chatUrls = data
                this.ruleform.freightVoucher = data.map(item => item.url).join(',')
            }
        },
        quotationVoucherGet(data) {
            if (data) {
                this.ruleform.quotationVoucherUrl = data
                this.ruleform.quotationVoucher = data.map(item => item.url).join(',')
            }
        },
        validate() {
            //校验是否有图片
            if (this.payment == '仓库到付' && !this.ruleform.freightVoucher) {
                this.$message.error('请上传运费凭证')
                throw new Error('请上传运费凭证')
            }
            if (this.payment == '仓库到付' && this.ruleform.postageChargeFreightFee < 1) {
                this.$message.error('不包邮运费最小值应为1')
                throw new Error('不包邮运费最小值应为1')
            }
            if ((this.payment == '寄付' || this.payment == '仓库到付') && !this.ruleform.quotationVoucher) {
                this.$message.error('请上传报价凭证')
                throw new Error('请上传报价凭证')
            }

            this.ruleform.goodsDtl.forEach((item, i) => {
                if (item.postageFreePrice === null || item.postageFreePrice === '' || item.postageFreePrice === undefined) {
                    this.$message.error(`第${i + 1}行包邮单价不能为空`)
                    throw new Error(`第${i + 1}行包邮单价不能为空`)
                }
            })
        },
        submitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    this.validate()
                    try {
                        await savePurchaseOrderGoodsPostageByCharge(this.ruleform)
                        this.$message.success('保存成功')
                        this.$emit('getList')
                        this.$emit('close')
                    } catch (error) {
                        this.$message.error('保存失败')
                    }
                } else {
                    return false;
                }
            });
        },
        close() {
            this.$emit('close')
        },
        async getList() {
            const { data, success } = await getPurchaseOrderGoodsPostageByCharge(this.queryInfo)
            if (success) {
                data.goodsDtl.forEach(item => {
                    if (item.postageFreePrice === null || item.postageFreePrice === '') {
                        item.postageFreePrice = undefined
                    }
                })
                data.postageChargeFreightFee = data.postageChargeFreightFee ? data.postageChargeFreightFee : 0
                this.ruleform = data
                if ((this.payment == '寄付' || this.payment == '仓库到付') && data.quotationVoucher) {
                    this.ruleform.quotationVoucherUrl = data.quotationVoucher.split(',').map((item, i) => {
                        return {
                            url: item,
                            name: `报价凭证${i + 1}`
                        }
                    })
                }
                if (this.payment == '仓库到付' && data.freightVoucher) {
                    this.ruleform.chatUrls = data.freightVoucher.split(',').map((item, i) => {
                        return {
                            url: item,
                            name: `运费凭证${i + 1}`
                        }
                    })
                }
                data.goodsDtl.forEach(item => {
                    this.changeMoney(item)
                })
            }
            this.formLoading = false
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.chatPicUrl {
    position: relative;

    .picTips {
        position: absolute;
        top: 0;
        left: 150px;
        color: #ff0000;
        font-size: 16px;
    }
}

.msgBox {
    height: 60px;
    display: flex;
    font-size: 14px;
    font-weight: 700;
    margin-top: 20px;

    .msgBox_item {
        margin-right: 60px;

        .toolTips {
            margin-top: 5px;
            font-size: 12px;
            color: #999;
            font-weight: normal;
        }
    }
}

.btnGroup {
    display: flex;
    justify-content: end;
}
</style>
