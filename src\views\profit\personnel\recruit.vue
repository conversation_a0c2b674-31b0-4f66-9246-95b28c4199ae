<template>
    <el-card style="margin-top: 5px;">
        <el-tabs v-model="activeName" style="height: calc(100% - 40px);">
            <el-tab-pane label="招聘专员" name="first1">
                <div class="table-top">
                    <div>招聘专员</div>
                    <div>
                        <el-link :underline="false" type="primary" @click="onExport">下载报表</el-link>
                    </div>
                </div>
                <!-- <vxetablebase ref="shootinghomeindex1" :height="'200px'" :id="'re0'" :tableData='departmentlist'
                    :tableCols='tableColsDepartment' :that='that' @sortchange='sortchange' :loading='listLoading'
                    :hascheck='false' :hasSeq='false' :showToolbar='false'>
                </vxetablebase> -->
                <vxe-table :height="'380px'" :data="departmentlist" size="mini" stripe :loading='listLoading' :footer-method="()=>attachedata" show-footer
                    @scroll="getScroll">
                    <template v-for="(col, colIndex) in tableColsDepartment">
                        <vxe-column :field="col.prop" :title="col.label" :width="col.width" :sortable="!!col.sortable">
                            <template #default="{ row }" v-if="col.formatter">
                                {{ col.formatter ? col.formatter(row) : row[col.prop] }}
                            </template>
                        </vxe-column>
                    </template>
                </vxe-table>
            </el-tab-pane>
            <el-tab-pane label="招聘申请人" name="first2" lazy>
                <div class="table-top">
                    <div>招聘申请人</div>
                    <div>
                        <el-link :underline="false" type="primary" @click="onExport">下载报表</el-link>
                    </div>
                </div>
                <!-- <vxetablebase ref="shootinghomeindex2" :height="'400px'" :id="'re1'" :tableData='postList'
                    :tableCols='tableColsPost' :that='that' @sortchange='sortchange' :loading='listLoading1'
                    :hascheck='false' :hasSeq='false' :showToolbar='false'>
                </vxetablebase> -->
                <vxe-table :height="'380px'" :data="postList" size="mini" stripe :loading='listLoading1' :footer-method="()=>applicantdata" show-footer
                    @scroll="getScroll">
                    <template v-for="(col, colIndex) in tableColsPost">
                        <vxe-column :field="col.prop" :title="col.label" :width="col.width" :sortable="!!col.sortable">
                            <template #default="{ row }" v-if="col.formatter">
                                {{ col.formatter ? col.formatter(row) : row[col.prop] }}
                            </template>
                        </vxe-column>
                    </template>
                </vxe-table>
            </el-tab-pane>
        </el-tabs>
    </el-card>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import bus from './bus.js';
import { hrHomePageRecruiterList, exportHrHomePageRecruiterList, hrHomePageApplicantList, exportHrHomePageApplicantList, hrHomePageRecruiterList_Stat, hrHomePageApplicantList_Stat } from "@/api/profit/hr"


const tableColsDepartment = [
    { istrue: true, prop: 'recruiterName', label: '姓名',sortable: true  },
    { istrue: true, prop: 'positionCountSum', label: '岗位数',sortable: true  },
    { istrue: true, prop: 'recruitmentCountSum', label: '预计招聘', sortable: true },
    { istrue: true, prop: 'yaoYueCountSum', label: '邀约人数',sortable: true  },
    { istrue: true, prop: 'comeCountSum', label: '到面人数', sortable: true  },
    { istrue: true, prop: 'initialTestPassCountSum', label: '初试通过',sortable: true  },
    { istrue: true, prop: 'finalTestPassCountSum', label: '复试通过',sortable: true  },
    { istrue: true, prop: 'initialTestPassCountSumRate', label: '初试通过率',sortable: true,formatter: (row) => row.initialTestPassCountSumRate ? row.initialTestPassCountSumRate + "%" : ""  },
    { istrue: true, prop: 'finalTestPassCountSumRate', label: '复式通过率', sortable: true,formatter: (row) => row.finalTestPassCountSumRate ? row.finalTestPassCountSumRate + "%" : ""   },
    { istrue: true, prop: 'lostCountSum', label: '流失人数', sortable: true  },
    { istrue: true, prop: 'lostCountSumRate', label: '流失率（%）',width: '120', sortable: true ,sortable: true,formatter: (row) => row.lostCountSumRate ? row.lostCountSumRate + "%" : ""  },
    { istrue: true, prop: 'employeeCountSum', label: '入职人数', sortable: true  },
    { istrue: true, prop: 'employeeRate', label: '入职率（%）',width: '120', sortable: true,formatter: (row) => row.employeeRate ? row.employeeRate + "%" : "" },
    { istrue: true, prop: 'employeeNowCountSum', label: '留存人数', sortable: true  },
    { istrue: true, prop: 'employeeNowCountSumRate', label: '留存率（%）', width: '120',sortable: true,formatter: (row) => row.employeeNowCountSumRate ? row.employeeNowCountSumRate + "%" : "" },
    { istrue: true, prop: 'workDaysSumRate', label: '人效', sortable: true },

];
const tableColsPost
    = [
        { istrue: true, prop: 'applicant', label: '姓名',sortable: true  },
        // { istrue: true, prop: 'positionName', label: '岗位', width: '80' },
        { istrue: true, prop: 'positionCountSum', label: '岗位数', sortable: true },
        { istrue: true, prop: 'recruitmentCountSum', label: '预计招聘', sortable: true  },
        { istrue: true, prop: 'yaoYueCountSum', label: '邀约人数',sortable: true },
        { istrue: true, prop: 'comeCountSum', label: '到面人数', sortable: true  },
        { istrue: true, prop: 'initialTestPassCountSum', label: '初试通过',sortable: true  },
        { istrue: true, prop: 'finalTestPassCountSum', label: '复试通过',sortable: true  },
        { istrue: true, prop: 'initialTestPassCountSumRate', label: '初试通过率', sortable: true,formatter: (row) => row.initialTestPassCountSumRate ? row.initialTestPassCountSumRate + "%" : ""  },
        { istrue: true, prop: 'finalTestPassCountSumRate', label: '复式通过率',sortable: true,formatter: (row) => row.finalTestPassCountSumRate ? row.finalTestPassCountSumRate + "%" : ""   },
        { istrue: true, prop: 'lostCountSum', label: '流失人数', sortable: true },
        { istrue: true, prop: 'lostCountSumRate', label: '流失率（%）',width: '120', sortable: true ,formatter: (row) => row.lostCountSumRate ? row.lostCountSumRate + "%" : "" },
        { istrue: true, prop: 'employeeCountSum', label: '入职人数', sortable: true },
        { istrue: true, prop: 'employeeRate', label: '入职率（%）',width: '120', sortable: true,formatter: (row) => row.employeeRate ? row.employeeRate + "%" : "" },
        { istrue: true, prop: 'employeeNowCountSum', label: '留存人数', sortable: true },
        { istrue: true, prop: 'employeeNowCountSumRate', label: '留存率（%）', width: '120',sortable: true,formatter: (row) => row.employeeNowCountSumRate ? row.employeeNowCountSumRate + "%" : ""  },
        { istrue: true, prop: 'workDaysSumRate', label: '人效', sortable: true,formatter: (row) => row.workDaysSumRate ? row.workDaysSumRate + "%" : "" },
    ];
export default {
    name: "recruit", // 招聘
    components: {
        MyContainer, vxetablebase
    },
    data () {
        return {
            attachedata: [],
            applicantdata: [],
            that: this,
            pageLoading: false,
            activeName: "first1",
            filter: {
                department: null,//部门
                position: null,//岗位
                recruiter: null,//招聘专员
                completionDegree: null,//完成度
            },
            listLoading: false,
            listLoading1: false,
            departmentlist: [],
            postList: [],
            tableColsPost: tableColsPost,
            tableColsDepartment: tableColsDepartment,
            pager: {},
            total: 0,
            total1: 0,
            filter: {
                currentPage: 1,
                pageSize: 1000,
                orderBy: 'employeeRate',
                isAsc: true,
                startDate: null,
                endDate: null,
                deptId: 0,
                position: null,
            },
            filter1: {
                currentPage: 1,
                pageSize: 1000,
                orderBy: 'employeeRate',
                isAsc: true,
                startDate: null,
                endDate: null,
                deptId: 0,
                position: null,
            },

        };
    },
    async mounted () {
        await bus.$on('filter', data => {
            for (const prop in data) {
                if (prop in this.filter) {
                    this.filter[prop] = data[prop];
                    this.filter1[prop] = data[prop];
                }
            }
            this.getDept();
            this.getPosit();
        })
    },
    methods: {
        getScroll (type) {
            if (type.scrollHeight - type.scrollTop - 380 == -36) {
                if (this.activeName == 'first1') {
                    if (this.total > this.departmentlist.length) {
                        this.filter.currentPage++
                        hrHomePageRecruiterList(this.filter).then(res => {
                            if (res.success) {
                                this.departmentlist = this.departmentlist.concat(res.data.list);
                            }
                        })
                    }
                } else {
                    if (this.total1 > this.postList.length) {
                        this.filter1.currentPage++
                        hrHomePageApplicantList(this.filter1).then(res => {
                            if (res.success) {
                                this.postList = this.postList.concat(res.data.list);
                            }
                        })
                    }
                }

            }
        },
        // getScroll1 (type) {
        //     if (type.scrollHeight - type.scrollTop - 100 == -36) {
        //         if (this.total1 > this.postList.length) {
        //             this.filter1.currentPage++
        //             hrHomePageApplicantList(this.filter1).then(res => {
        //                 if (res.success) {
        //                     this.postList = this.postList.concat(res.data.list);
        //                 }
        //             })
        //         }
        //     }
        // },
        async onExport () {
            var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            const params = { ... this.filter }
            if (params === false) {
                return;
            }
            let name = '';
            if (this.activeName == 'first1') {
                //招聘专员
                var res = await exportHrHomePageRecruiterList(params);
                name = '招聘专员数据导出_';
            } else {
                //招聘申请人
                var res = await exportHrHomePageApplicantList(params);
                name = '招聘申请人数据导出_';
            }
            loadingInstance.close();
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', name + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        //列表排序
        sortchange (column) {
            if (column.order) {
                let pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.filter.orderBy = pager.OrderBy
                this.filter.isAsc = pager.IsAsc
            }
            if (this.activeName == 'first1') {
                this.getDept();
            } else {
                this.getPosit();
            }
        },
        formatNumber(number){
            const absNumber = Math.abs(number);
            const options = {
              minimumFractionDigits: absNumber >= 100 ? 0 : 2,
              maximumFractionDigits: absNumber >= 100 ? 0 : 2,
                    };
                 return new Intl.NumberFormat('zh-CN', options).format(number);
                },         

        async getDept () {
          var attacheResult = await hrHomePageRecruiterList_Stat(this.filter)
            if (!attacheResult.success) {
                return;
              }
            let attachesummary = attacheResult.data.data;
            var attachetemp = [];
            this.tableColsDepartment.forEach(prop => {
                var num1=attachesummary[prop.prop]
                   if (num1 == null) ;
                   else if ((typeof num1 == 'string') && num1.constructor == String) num1=num1;
                   else if (Math.abs(parseInt(num1)) < 100) num1 = num1.toFixed(2);
                  else num1 = this.formatNumber(num1);
                attachetemp.push(num1)
              });
            this.attachedata = [attachetemp];
            //获取部门数据
            this.listLoading = true;
            hrHomePageRecruiterList(this.filter).then(res => {
                this.listLoading = false;
                if (res.success) {
                    res.data.list.forEach(item => {
                        for (const prop in item) {
                            if (typeof item[prop] == 'string' && item[prop].indexOf('%') > -1) {
                                item[prop] = item[prop].replace('%', '');
                            }
                        }
                    });
                    this.departmentlist = res.data.list;
                    this.total = res.data.total;
                }
            });
              return this.attachedata
        },
        async getPosit () {
          var applicantstatResult = await hrHomePageApplicantList_Stat(this.filter)
              if (!applicantstatResult.success) {
                  return;
                }
              let applicantsummary = applicantstatResult.data.data;
              var applicanttemp = [];
              this.tableColsPost.forEach(prop => {
                var num1=applicantsummary[prop.prop]
                   if (num1 == null) ;
                   else if ((typeof num1 == 'string') && num1.constructor == String) num1=num1;
                   else if (Math.abs(parseInt(num1)) < 100) num1 = num1.toFixed(2);
                  else num1 = this.formatNumber(num1);
                applicanttemp.push(num1)

              });
            this.applicantdata = [applicanttemp];
            this.listLoading1 = true;
            hrHomePageApplicantList(this.filter1).then(res => {
                this.listLoading1 = false;
                if (res.success) {
                    res.data.list.forEach(item => {
                        for (const prop in item) {
                            if (typeof item[prop] == 'string' && item[prop].indexOf('%') > -1) {
                                item[prop] = item[prop].replace('%', '');
                            }
                        }
                    });
                    this.postList = res.data.list;
                    this.total1 = res.data.total;
                }
            });
            return this.applicantdata
        },
        async getList () {
            if (this.activeName == 'frist1') {
                //获取招聘专员数据
            } else {
                //获取招聘申请人数据
            }
            // var pager = this.$refs.pager.getPager();
            // const params = {
            //     ...pager,
            //     ...this.pager,
            //     ...this.filter
            // };
            // this.listLoading = true;
            // const res = await pageShootingViewTaskAsync(params);
            // this.listLoading = false;
            // this.total = res.data.total
            // this.datalist = res.data.list;
        },
    },
};
</script>

<style lang="scss" scoped>
.table-top {
    padding: 10px 0 0 0;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
}

::v-deep .el-button-group {
    display: none;
}
</style>
