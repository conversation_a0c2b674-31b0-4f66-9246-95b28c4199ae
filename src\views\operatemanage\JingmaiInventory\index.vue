<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startOffTime" :endDate.sync="ListInfo.endOffTime" class="publicCss"
                    startPlaceholder="上架时间" endPlaceholder="上架时间" />
                <el-select v-model="ListInfo.shopId" placeholder="请输入店铺" clearable filterable class="publicCss">
                    <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.id" />
                </el-select>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsId" v-model="ListInfo.goodsId"
                    placeholder="商品ID/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="codeCallback($event, '1')" title="商品ID"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.skuId" v-model="ListInfo.skuId"
                    placeholder="SKU编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="codeCallback($event, '2')" title="SKU编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode"
                    placeholder="商家SKU编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="codeCallback($event, '3')" title="商家SKU编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <!-- <el-input v-model.trim="ListInfo.goodsId" placeholder="商品ID" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.skuId" placeholder="SKU编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.goodsCode" placeholder="商家SKU编码" maxlength="50" clearable
                    class="publicCss" /> -->
                <el-input v-model.trim="ListInfo.category1" placeholder="一级类目" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.category2" placeholder="二级类目" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.category3" placeholder="三级类目" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.styleCode" style="width: 130px" placeholder="款式编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.goodsName" style="width: 130px" placeholder="sku名称" maxlength="50" clearable
                    class="publicCss" />
                <el-select filterable v-model="ListInfo.groupId" collapse-tags clearable placeholder="运营组" multiple
                    class="publicCss">
                    <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                    <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select filterable v-model="ListInfo.specialUserId" collapse-tags clearable class="publicCss"
                    placeholder="运营专员" multiple>
                    <el-option key="无运营专员" label="无运营专员" :value="0"></el-option>
                    <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select filterable v-model="ListInfo.assistUserId" collapse-tags clearable placeholder="运营助理"
                    multiple class="publicCss" style="width: 170px;">
                    <el-option key="无运营助理" label="无运营助理" :value="0"></el-option>
                    <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.brandIds" clearable filterable multiple collapse-tags placeholder="采购"
                    class="publicCss" style="width: 170px;">
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <!-- <el-select v-model="ListInfo.jstStock" clearable filterable multiple collapse-tags placeholder="聚水潭库存"
                    class="publicCss">
                    <el-option v-for="item in jstStockList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select> -->
                <el-select v-model="ListInfo.jstStock" @change="inputchange(ListInfo.jstStock, 1)" :style="ListInfo.jstStock?{width: '100px'}:{width: '180px'}" clearable filterable collapse-tags placeholder="聚水潭库存（已配置仓库）"
                    class="publicCss">
                    <el-option v-for="item in jstStockList2" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <!-- startJstStock   endJstStock     -->
                <div style="display: flex; align-items: center; margin-bottom: 5px;" v-if="ListInfo.jstStock">
                    <el-input-number v-model="ListInfo.startJstStock" :min="0" :max="999999999" :precision="0"
                    :controls="false" placeholder="请输入" @blur="endinput2(1)"
                    style="width: 80px;margin-right: 5px;" />
                    
                    <div style="display: flex; align-items: center" v-if="ListInfo.jstStock == 4">
                        -<el-input-number v-model="ListInfo.endJstStock" @blur="endinput(1)" :min="0" :max="999999999" :precision="0"
                        :controls="false" placeholder="请输入"
                        style="width: 80px;margin-right: 5px;" />
                    </div>
                </div>

                <!-- startAvailableStock  endAvailableStock -->
                <el-select v-model="ListInfo.availableStock" @change="inputchange(ListInfo.availableStock, 2)" :style="ListInfo.availableStock?{width: '100px'}:{width: '150px'}" clearable filterable collapse-tags placeholder="店铺库存"
                    class="publicCss">
                    <el-option v-for="item in jstStockList2" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <div style="display: flex; align-items: center; margin-bottom: 5px;" v-if="ListInfo.availableStock">
                    <el-input-number v-model="ListInfo.startAvailableStock" :min="0" :max="999999999" :precision="0"
                    :controls="false" placeholder="请输入" @blur="endinput2(2)"
                    style="width: 80px;margin-right: 5px;" />
                    
                    <div style="display: flex; align-items: center" v-if="ListInfo.availableStock == 4">
                        -<el-input-number v-model="ListInfo.endAvailableStock" @blur="endinput(2)" :min="0" :max="999999999" :precision="0"
                        :controls="false" placeholder="请输入"
                        style="width: 80px;margin-right: 5px;" />
                    </div>
                </div>


                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="batchEdit">批量编辑</el-button>
                    <el-button type="primary" @click="SynchronizeSet"
                        v-if="checkPermission('SynchronizeSet')">同步库存设置</el-button>
                    <el-button type="primary" @click="batchFlushed">批量刷新数据</el-button>
                    <el-button type="primary" v-if="checkPermission('JmskuExport')" @click="exportData">导出</el-button>
                    <el-button type="primary" @click="downloadTemplate">下载模板</el-button>
                    <el-upload
                        action=""
                        :before-upload="beforeUpload"
                        :http-request="onImport"
                        :show-file-list="false"
                        style="display: inline-block; margin-left: 10px;">
                        <el-button type="primary">导入库存</el-button>
                    </el-upload>
                    <el-button style="margin-left: 10px" type="primary" @click="openWarehouseConfig">分仓配置</el-button>
                    <el-button type="primary" @click="openOperationLog">操作日志</el-button>
                </div>
            </div>
        </template>

        <vxetablebase ref="table" id="20241021133008" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            border @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            @select="select" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
            <template #changeStock="{ row }">
                <div v-if="row.isEdit == false">{{ row.changeStock }}</div>
                <div style="display: flex;" v-else>
                    <el-input-number v-model="row.changeStock" :min="0" :max="999999999" :precision="0"
                        :controls="false" placeholder="请输入调整库存" :disabled="row.Synchronize"
                        style="width: 100px;margin-right: 5px;" />
                    <el-button type="text" @click="changeStockSubmit(row)" v-throttle="1000">保存</el-button>
                </div>
            </template>
            <template #jstStock="{ row }">
                <el-button type="text" @click="viewJstStockDetails(row)">{{ row.jstStock }}</el-button>
            </template>
            <template slot="right">
                <vxe-column title="操作" width="150" fixed="right">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <el-button type="text" @click="SynchronizeInventory(row)">同步库存</el-button>
                            <el-button type="text" @click="row.isEdit = true" v-if="!row.isEdit">编辑</el-button>
                            <el-button type="text" @click="catchEdit(row)" v-else>取消</el-button>
                            <el-button type="text" @click="openLogDetails(row.id)">日志</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="日志" :visible.sync="logDetailsVisible" width="70%" v-dialogDrag>
            <logDetails v-if="logDetailsVisible" :id="id" />
        </el-dialog>

        <el-dialog title="批量编辑" :visible.sync="batchEditVisible" width="30%" v-dialogDrag>
            <div style="display: flex;justify-content: center; width: 100%;">
                
                    <el-radio-group v-model="submitobj.editType" style="width: 300px">
                        <el-row style="width: 100%; display: flex; align-items: center;">
                            <el-radio label="1" :value="1" style="display: flex; align-items: center;">
                                统一修改为
                            </el-radio> 
                            <div style="display: flex; flex-direction: row; align-items: center;">
                                <el-input-number v-model="batchNum" :min="0" :max="99999999" :precision="0" placeholder="请输入调整库存"
                                :controls="false" />
                            </div>
                        </el-row>
                        <el-row style="margin-top: 20px;">
                            <el-radio label="2" :value="2">
                                代入各商品编码聚水潭库存(已配置仓库) 
                            </el-radio> 
                        </el-row>
                        
                    </el-radio-group>
            </div>
            <div class="btnGroup" style="margin-top: 20px">
                <el-button @click="batchEditVisible = false">取消</el-button>
                <el-button type="primary" @click="batchSumbit" v-throttle="1000">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog title="同步库存设置(单位:%)" :visible.sync="SynchronizeVisible" width="15%" v-dialogDrag>
            <div style="display: flex;justify-content: center;">
                <el-input-number v-model="SynchronizeNum" :min="1" :max="100" :precision="0" placeholder="请输入比例(%)"
                    :controls="false" />
            </div>
            <div class="btnGroup">
                <el-button @click="SynchronizeVisible = false">取消</el-button>
                <el-button type="primary" @click="SynchronizeSumbit" v-throttle="1000">确定</el-button>
            </div>
        </el-dialog>

        <!-- 聚水潭库存详情对话框 -->
        <el-dialog title="聚水潭库存详情" :visible.sync="jstStockDetailsVisible" width="30%"  v-dialogDrag>
            <el-table :data="jstStockList" border style="width: 100%;overflow-y: auto;" height="300px">
                <el-table-column prop="shopName" label="仓库名称" width="240px" />
                <el-table-column prop="jstStock" label="数量"  />
                <el-table-column prop="goodsCode" label="商品编码" />
            </el-table>
        </el-dialog>

        <!-- 添加分仓配置对话框 -->
<!--        <el-dialog title="添加分仓配置" :visible.sync="addWarehouseVisible" width="30%" v-dialogDrag>
            <el-form :model="warehouseForm" label-width="120px">
                <el-form-item label="仓库名称">
                    <el-select v-model="warehouseForm.shopId" filterable clearable placeholder="请选择仓库" style="width: 100%" @change="handleShopChange">
                        <el-option v-for="item in warehouseList" :key="item.wmsCoId" :label="item.name" :value="item.wmsCoId" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否启用">
                    <el-switch v-model="warehouseForm.enable" />
                </el-form-item>
            </el-form>
            <div class="btnGroup">
                <el-button @click="addWarehouseVisible = false">取消</el-button>
                <el-button type="primary" @click="submitWarehouseConfig">确定</el-button>
            </div>
        </el-dialog>-->

        <!-- 分仓配置对话框 -->
      <el-dialog
        title="分仓配置"
        :visible.sync="warehouseConfigVisible"
        width="50%"
        v-dialogDrag
        class="warehouse-config-dialog"
      >
        <div class="top">
          <el-select
            v-model="warehouseQuery.shopId"
            filterable
            clearable
            placeholder="仓库名称"
            class="publicCss"
          >
            <el-option
              v-for="item in warehouseList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
          <el-button type="primary" @click="handleWarehouseSearch">查询</el-button>
          <el-button type="primary" @click="openBatchAddWarehouseConfigDialog">批量添加</el-button>
        </div>

        <div class="table-container">
          <el-table
            :data="warehouseConfigList"
            border
            style="width: 100%"
            height="400px"
          >
            <el-table-column prop="shopName" label="仓库名称" width="240px"  />
            <el-table-column prop="shopCode" label="仓库代码" />
            <el-table-column prop="enable" label="是否启用" >
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable" @change="updateWarehouseConfig(scope.row)" />
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" />
            <el-table-column label="操作" >
              <template slot-scope="scope">
                <el-button type="text" @click="deleteWarehouseConfig(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-dialog>

        <!-- 批量添加分仓配置对话框 -->
        <el-dialog title="批量添加分仓配置" :visible.sync="batchAddWarehouseVisible" width="30%" v-dialogDrag>
            <el-form :model="batchWarehouseForm" label-width="120px">
                <el-form-item label="仓库名称">
                    <el-select v-model="batchWarehouseForm.shopIds" multiple filterable clearable placeholder="请选择仓库" style="width: 100%" collapse-tags>
                        <el-option
                            v-for="item in warehouseList"
                            :key="item.wmsCoId"
                            :label="item.name"
                            :value="item.wmsCoId"
                            :disabled="isWarehouseConfigured(item.wmsCoId)"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否启用">
                    <el-switch v-model="batchWarehouseForm.enable" />
                </el-form-item>
            </el-form>
            <div class="btnGroup">
                <el-button @click="batchAddWarehouseVisible = false">取消</el-button>
                <el-button type="primary" @click="submitBatchWarehouseConfig" v-throttle="1000">确定</el-button>
            </div>
        </el-dialog>

        <!-- 操作日志对话框 -->
        <el-dialog title="操作日志" :visible.sync="operationLogVisible" width="61%" v-dialogDrag
                   class="warehouse-config-dialog"
        >
            <div class="top">
                <el-select v-model="operationLogQuery.shopCode" filterable clearable placeholder="店铺名称" class="publicCss">
                    <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
                </el-select>
                <el-select v-model="operationLogQuery.status" filterable clearable placeholder="状态" class="publicCss">
                    <el-option label="全部" value="" />
                    <el-option label="成功" :value="1" />
                    <el-option label="失败" :value="0" />
                </el-select>
                <dateRange :startDate.sync="operationLogQuery.operationTimeStart" :endDate.sync="operationLogQuery.operationTimeEnd" class="publicCss"
                    startPlaceholder="操作时间" endPlaceholder="操作时间" />
                <el-button type="primary" @click="getOperationLogList">搜索</el-button>
            </div>
          <div class="table-container">
            <el-table :data="operationLogList" border style="width: 100%;overflow-x: hidden" height="400px" >
                <el-table-column prop="shopName" label="店铺名称" width="250px" />
                <el-table-column prop="operator" label="操作人" />
                <el-table-column prop="operationType" label="操作类型"  />
                <el-table-column prop="operationTime" label="操作时间" width="180px" />
                <el-table-column prop="status" label="状态">
                    <template slot-scope="scope">
                        <el-tag type="success" v-if="scope.row.status === 1">成功</el-tag>
                        <el-tag type="danger" v-else>失败</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="result" label="结果" width="180px" />
                <el-table-column prop="remarks" label="备注" width="200px"/>
            </el-table>
          </div>
            <div style="margin-top: 20px;">
                <el-pagination
                    @size-change="operationLogSizeChange"
                    @current-change="operationLogPageChange"
                    :current-page="operationLogQuery.currentPage"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="operationLogQuery.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="operationLogTotal">
                </el-pagination>
            </div>
        </el-dialog>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import { getDirectorGroupList, getDirectorList, getList as getshopList, } from '@/api/operatemanage/base/shop'
import { getAllProBrand, } from '@/api/inventory/warehouse'
import dateRange from "@/components/date-range/index.vue";
import logDetails from './components/logDetails.vue'
import { jdSkuRecordPage, jdSkuRecordOneClickSending, jdSkuRecordSetting, jdSkuRecordGetSetting, jdSkuRecordSyncJdSkuRecord, jdSkuRecordExport, getJstUsableQtyList, warehouseConfigList, warehouseConfigSave, warehouseConfigBatchSave, warehouseConfigUpdate, warehouseConfigRemove, shopOperationLogPage, jdSkuJstStockImport,selectWarehousePage, } from '@/api/bladegateway/worcestorejk'
import inputYunhan from "@/components/Comm/inputYunhan";
import { downloadLink } from '@/utils/tools'
const goodsStatusList = [
    {
        label: '售卖中',
        value: 1
    },
    {
        label: '已下架',
        value: 2
    },
]
const changeStatusList = [
    {
        label: '未调整',
        value: 0
    },
    {
        label: '已申请',
        value: 1
    },
    {
        label: '申请成功',
        value: 2
    },
    {
        label: '申请失败',
        value: 3
    },
]

const jstStockList = [
    {
        label: '负库存',
        value: 1
    },
    {
        label: '零库存',
        value: 2
    },
    {
        label: '正库存',
        value: 3
    },
    {
        label: '未获取',
        value: 4
    },
]
const jstStockList2 = [
    {
        label: '大于',
        value: 1
    },
    {
        label: '等于',
        value: 2
    },
    {
        label: '小于',
        value: 3
    },
    {
        label: '介于',
        value: 4
    },
]
const tableCols = [
    { type: 'checkbox', label: '', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'shopName', label: '店铺名称', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'category1', label: '一级类目', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'category2', label: '二级类目', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'category3', label: '三级类目', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'groupId', label: '运营组', formatter: (row) => row.groupName },
    { sortable: 'custom', width: '100', align: 'center', prop: 'specialUserId', label: '运营专员', formatter: (row) => row.specialUser },
    { sortable: 'custom', width: '100', align: 'center', prop: 'assistUserId', label: '运营助理', formatter: (row) => row.assistUser },
    { sortable: 'custom', width: '100', align: 'center', prop: 'skuCreateTime', label: '上架时间', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'styleCode', label: '款式编码', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'brandId', label: '采购', formatter: (row) => row.brand },
    { sortable: 'custom', width: '100', align: 'center', prop: 'goodsId', label: '商品ID', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'skuId', label: 'SKU编码', },

    { sortable: 'custom', width: '100', align: 'center', prop: 'goodsCode', label: '商家SKU编码', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'goodsName', label: 'sku名称', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'totalStock', label: '总库存', formatter: (row) => row.totalStock !== null ? row.totalStock : row.totalStockStr },
    { sortable: 'custom', width: '100', align: 'center', prop: 'nnStock', label: 'nn可用库存' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'availableStock', label: '店铺当前库存', formatter: (row) => row.availableStock !== null ? row.availableStock : row.availableStockStr },
    { sortable: 'custom', width: '100', align: 'center', prop: 'nnStock', label: 'nn当前库存', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'changeStock', label: '调整库存', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'changeStatus', label: '调整状态', formatter: (row) => changeStatusList.find(item => item.value == row.changeStatus)?.label },
    { sortable: 'custom', width: '100', align: 'center', prop: 'sendTime', label: '调整发起时间', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'responseTime', label: '调整结束时间', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'jstStock', label: '聚水潭库存', slots: { default: 'jstStock' } },
    { sortable: 'custom', width: '200', align: 'center', prop: 'assembleJstStock', label: '聚水潭库存（已配置仓库）',},

    { sortable: 'custom', width: '100', align: 'center', prop: 'stockPercent', label: '同步库存比例', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'jdPrice', label: '京东价', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'purchasePrice', label: 'L1等级采购价', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'cost', label: '成本', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'profit', label: '利润', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'profitRate', label: '利润率', formatter: (row) => row.profitRate ? row.profitRate + '%' : '' },
    // { sortable: 'custom', width: '100', align: 'center', prop: 'brandName', label: '品牌', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'goodsStatus', label: '商品状态', formatter: (row) => goodsStatusList.find(item => item.value == row.goodsStatus)?.label },
    { sortable: 'custom', width: '100', align: 'center', prop: 'createTime', label: '创建时间', },
    // { sortable: 'custom', width: '100', align: 'center', prop: 'evaluationCount', label: '累计评', },
    // { sortable: 'custom', width: '100', align: 'center', prop: 'praiseDegree', label: '好评度', formatter: (row) => row.praiseDegree ? row.praiseDegree + '%' : '' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, logDetails, inputYunhan
    },
    data() {
        return {
            that: this,
            jstStockList2,
            submitobj: {
                editType: '1'
            },
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startOffTime: null,//开始时间
                endOffTime: null,//结束时间
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            selectList: [],
            batchEditVisible: false,
            batchNum: undefined,
            grouplist: [],
            directorlist: [],
            brandlist: [],
            logDetailsVisible: false,
            id: null,
            historyList: [],
            SynchronizeVisible: false,
            SynchronizeNum: 1,
            shopList: [],
            wareList: [],
            historySynchronize: null,
            jstStockList,
            warehouseConfigVisible: false,
            warehouseConfig: {},
            warehouseConfigList: [],
            jstStockDetailsVisible: false,
            operationLogVisible: false,
            operationLogQuery: {
                currentPage: 1,
                pageSize: 10,
              operationTimeStart: null,
              operationTimeEnd: null,
            },
            operationLogList: [],
            operationLogTotal: 0,
            warehouseQuery: {
                shopId: null
            },
            addWarehouseVisible: false,
            batchAddWarehouseVisible: false,
            warehouseForm: {},
            batchWarehouseForm: {
                shopIds: [],
                enable: true
            },
            configuredWarehouses: [], // 新增：存储已配置的仓库列表
        }
    },
    async mounted() {
        this.init()
        // this.getSynchronize()
        await this.getList()
    },
    methods: {
        endinput2(type){
            // startJstStock
            // endJstStock
            if(type==1){
                if(!this.ListInfo.endJstStock){
                    this.ListInfo.endJstStock = this.ListInfo.startJstStock;
                }else{
                    if(this.ListInfo.startJstStock>this.ListInfo.endJstStock){
                        this.ListInfo.endJstStock = this.ListInfo.startJstStock;
                    }
                }
            }else if(type==2){
                // this.ListInfo.startAvailableStock = undefined;
                // this.ListInfo.endAvailableStock = undefined;

                if(!this.ListInfo.startAvailableStock){
                    this.ListInfo.startAvailableStock = 0;
                }else{
                    if(this.ListInfo.startAvailableStock>this.ListInfo.endAvailableStock){
                        this.ListInfo.endAvailableStock = this.ListInfo.startAvailableStock;
                    }
                }
            }
        },
        endinput(type){
            if(type==1){
                if(!this.ListInfo.startJstStock){
                    this.ListInfo.startJstStock = 0;
                }else{
                    if(this.ListInfo.startJstStock>this.ListInfo.endJstStock){
                        this.ListInfo.endJstStock = this.ListInfo.startJstStock;
                    }
                }
            }else if(type==2){
                // this.ListInfo.startAvailableStock = undefined;
                // this.ListInfo.endAvailableStock = undefined;

                if(!this.ListInfo.startAvailableStock){
                    this.ListInfo.startAvailableStock = 0;
                }else{
                    if(this.ListInfo.startAvailableStock>this.ListInfo.endAvailableStock){
                        this.ListInfo.endAvailableStock = this.ListInfo.startAvailableStock;
                    }
                }
            }
        },
        inputchange(val,type){
            if(type==1&&!val){
                this.ListInfo.startJstStock = undefined;
                this.ListInfo.endJstStock = undefined;
            }else if(type==2&&!val){
                this.ListInfo.startAvailableStock = undefined;
                this.ListInfo.endAvailableStock = undefined;
            }
        },
        codeCallback(e, val) {
            if (val == '1') {
                this.ListInfo.goodsId = e
            } else if (val == '2') {
                this.ListInfo.skuId = e
            } else if (val == '3') {
                this.ListInfo.goodsCode = e
            }
        },
        batchFlushed() {
            if (this.selectList.length == 0) return this.$message.warning('请选择要刷新的数据')
            this.$confirm('确认刷新这几条数据吗, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const params = this.selectList.map(item => {
                    return {
                        id: item.id,
                    }
                })
                const { success } = await jdSkuRecordSyncJdSkuRecord(params)
                if (success) {
                    this.getList()
                    this.$message({
                        type: 'success',
                        message: '刷新成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消刷新'
                });
            });
        },
        async getSynchronize() {
            const { data, success } = await jdSkuRecordGetSetting()
            if (success) {
                this.historySynchronize = data
            }
        },
        async SynchronizeSumbit() {
            if (this.SynchronizeNum === undefined || this.SynchronizeNum === null || this.SynchronizeNum === 0 || this.SynchronizeNum === '') return this.$message.warning('同步库存比例不能为0或空')
            const params = this.selectList.map(item => {
                return {
                    id: item.id,
                    stockPercent: this.SynchronizeNum
                }
            })
            const { success } = await jdSkuRecordSetting(params)
            if (success) {
                this.selectList = []
                this.historySynchronize = this.SynchronizeNum
                this.getList()
                this.SynchronizeVisible = false
                this.$message.success('设置成功')
            }
        },
        async SynchronizeSet() {
            if (this.selectList.length == 0) return this.$message.warning('请选择要同步的数据')
            this.SynchronizeNum = undefined
            this.SynchronizeVisible = true
        },
        catchEdit(row) {
            row.isEdit = false
            if (row.id) {
                const history = this.historyList.find(x => x.id == row.id).changeStock
                this.$set(row, 'changeStock', history)
            }
        },
        async changeStockSubmit(row) {
            const { success } = await jdSkuRecordOneClickSending([{ id: row.id, skuStock: row.changeStock, editType: 1 }])
            if (success) {
                this.$message.success('保存成功')
                this.getList()
            } else {
                this.$message.error('保存失败')
            }
        },
        async init() {
            const { data } = await getDirectorGroupList();
            this.grouplist = data?.map(item => { return { value: item.key, label: item.value }; });
            var { data: data1 } = await getDirectorList();
            this.directorlist = data1?.map(item => { return { value: item.key, label: item.value }; });
            var { data: data2 } = await getAllProBrand();
            this.brandlist = data2.map(item => {
                return { value: item.key, label: item.value };
            });
            const { data: data3 } = await getshopList({
                platform: 7,
                CurrentPage: 1,
                PageSize: 10000,
            })
            this.shopList = data3.list

            const { data: data4 } = await selectWarehousePage({
                currentPage: 1,
                pageSize: 10000,
            })
            this.warehouseList = data4.list
        },
        async batchSumbit() {
            if ((this.batchNum === undefined || this.batchNum === null || this.batchNum === 0 || this.batchNum === '')&& this.submitobj.editType == 1) return this.$message.warning('调整库存不能为0或空')
            // const res = this.selectList.map(item => {
            //     return { id: item.id, skuStock: this.batchNum }
            // })

            const res = this.selectList.map(item => {
                return { 
                    editType: this.submitobj.editType,
                    id: item.id,
                    skuStock: this.submitobj.editType ==1 ? this.batchNum : null,
                    jstStock: this.submitobj.editType ==2 ? item.assembleJstStock : null,
                }
            })
            console.log("整理请求参数",res)
            // return;

            const { success } = await jdSkuRecordOneClickSending(res)
            if (success) {
                this.$message.success('保存成功')
                this.getList()
                this.selectList = []
                this.batchEditVisible = false
            } else {
                this.$message.error('保存失败')
            }
        },
        select(val) {
            console.log(val, 'val');
            this.selectList = val
        },
        batchEdit() {
            if (this.selectList.length == 0) return this.$message.warning('请选择要编辑的数据')
            this.batchNum = undefined
            this.batchEditVisible = true
        },
        openLogDetails(id) {
            this.id = id
            this.logDetailsVisible = true
        },
        SynchronizeInventory(row) {
            row.isEdit = true
            //向上取整
            row.changeStock = Math.ceil(row.jstStock * (row.stockPercent ? (row.stockPercent / 100) : 1))
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await jdSkuRecordPage(this.ListInfo)
                if (success) {
                    data.list.forEach(item => {
                        item.isEdit = false
                        item.Synchronize = false
                    })
                    this.tableData = data.list
                    this.historyList = JSON.parse(JSON.stringify(data.list))
                    this.total = data.total
                    this.loading = false
                } else {
                    this.loading = false
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        async exportData() {
          this.loading = true
          const res = await jdSkuRecordExport(this.ListInfo)
          const aLink = document.createElement('a');
          let blob = new Blob([res.data], { type: 'application/vnd.ms-excel' });
          aLink.href = URL.createObjectURL(blob);
          aLink.setAttribute("download", '京麦库存导出_' + new Date().toLocaleString() + '.xlsx');
          aLink.click();
          this.loading = false
        },
        async openWarehouseConfig() {
            this.warehouseConfigVisible = true
            await this.getWarehouseConfigList()
        },
        async getWarehouseConfigList(params) {
            const { data } = await warehouseConfigList(params || {})
            this.warehouseConfigList = data
        },
        async handleWarehouseSearch() {
            const params = {}
            if (this.warehouseQuery.shopId) {
                params.shopId = this.warehouseQuery.shopId
            }
            await this.getWarehouseConfigList(params)
        },
        async addWarehouseConfig() {
            this.openAddWarehouseConfigDialog();
        },
        async updateWarehouseConfig(row) {
            const { data } = await warehouseConfigUpdate(row)
            if (data) {
                this.$message.success('仓库配置更新成功')
                await this.getWarehouseConfigList()
            }
        },
        async deleteWarehouseConfig(row) {
            const { success } = await warehouseConfigRemove(row)
            if (success) {
                this.$message.success('仓库配置删除成功')
                await this.getWarehouseConfigList()
            }
        },
        async getJstStockDetails() {
            this.jstStockDetailsVisible = true
            const { data } = await getJstUsableQtyList()
            this.jstStockList = data
        },
        async getOperationLogList() {
            const { data, success } = await shopOperationLogPage(this.operationLogQuery)
            if (success && data) {
                this.operationLogList = data.list
                this.operationLogTotal = data.total
            }
        },
        operationLogSizeChange(val) {
            this.operationLogQuery.pageSize = val
            this.getOperationLogList()
        },
        operationLogPageChange(val) {
            this.operationLogQuery.currentPage = val
            this.getOperationLogList()
        },
        async viewJstStockDetails(row) {
            try {
                this.jstStockDetailsVisible = true
                const { data } = await getJstUsableQtyList({ goodsCode: row.goodsCode })
                this.jstStockList = data || []
            } catch (error) {
                this.$message.error('获取聚水潭库存详情失败')
            }
        },
        async openOperationLog() {
            this.operationLogVisible = true
            this.operationLogQuery = {
                currentPage: 1,
                pageSize: 10,
              operationTimeStart: null,
              operationTimeEnd: null,
            }
            await this.getOperationLogList()
        },
        async submitWarehouseConfig() {
            if (!this.warehouseForm.shopId) {
                return this.$message.warning('请选择仓库');
            }

            // 确保shopCode和shopName已填充
            if (!this.warehouseForm.shopCode || !this.warehouseForm.shopName) {
                const shop = this.shopList.find(item => item.id === this.warehouseForm.shopId);
                if (shop) {
                    this.warehouseForm.shopCode = shop.shopCode;
                    this.warehouseForm.shopName = shop.shopName;
                }
            }

            const { success } = await warehouseConfigSave(this.warehouseForm)
            if (success) {
                this.$message.success('仓库配置添加成功')
                this.addWarehouseVisible = false
                await this.getWarehouseConfigList()
            }
        },
        openAddWarehouseConfigDialog() {
            this.warehouseForm = {
                shopId: null,
                shopCode: '',
                shopName: '',
                enable: true
            }
            this.addWarehouseVisible = true
        },
        async openBatchAddWarehouseConfigDialog() {
            this.batchWarehouseForm = {
                shopIds: [],
                enable: true
            }
            // 获取已配置的仓库列表
            const { data } = await warehouseConfigList({})
            this.configuredWarehouses = data || []
            this.batchAddWarehouseVisible = true
        },
        async submitBatchWarehouseConfig() {
            if (!this.batchWarehouseForm.shopIds || this.batchWarehouseForm.shopIds.length === 0) {
                return this.$message.warning('请选择至少一个仓库');
            }

            // 准备批量保存的数据
            const warehouseConfigs = this.batchWarehouseForm.shopIds.map(shopId => {
                const shop = this.warehouseList.find(item => item.wmsCoId === shopId);
                return {
                    shopId: shop.id,
                    shopCode: shop ? shop.wmsCoId : '',
                    shopName: shop ? shop.name : '',
                    enable: this.batchWarehouseForm.enable
                };
            });

            try {
                const { success } = await warehouseConfigBatchSave(warehouseConfigs);
                if (success) {
                    this.$message.success('批量添加仓库配置成功');
                    this.batchAddWarehouseVisible = false;
                    await this.getWarehouseConfigList();
                }
            } catch (error) {
                this.$message.error('批量添加失败: ' + (error.message || '未知错误'));
            }
        },
        handleShopChange(shopId) {
            if (shopId) {
                const shop = this.shopList.find(item => item.id === shopId);
                if (shop) {
                    this.warehouseForm.shopCode = shop.shopCode;
                    this.warehouseForm.shopName = shop.shopName;
                }
            } else {
                this.warehouseForm.shopCode = '';
                this.warehouseForm.shopName = '';
            }
        },
        // 下载模板方法
        downloadTemplate() {
            downloadLink("https://nanc.yunhanmy.com:10010/media/video/20250322/1903390908614959104.xlsx", '京麦库存导入模板.xlsx');
        },
        // 上传前验证
        beforeUpload(file) {
            const isExcel = file.type === 'application/vnd.ms-excel' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            if (!isExcel) {
                this.$message.error('只能上传Excel文件 (.xlsx, .xls)!');
                return false;
            }
            // 添加文件大小校验，限制文件大小不超过10MB
            const maxSize = 10 * 1024 * 1024; // 10MB
            if (file.size > maxSize) {
                this.$message.error('文件大小不能超过10MB!');
                return false;
            }
            return true;
        },
        // 导入方法
        async onImport(options) {
            const file = options.file;
            // 添加 loading 状态
            this.loading = true;
            try {
                const response = await jdSkuJstStockImport(file);
                if (response?.success) {
                    this.$message.success('上传成功,正在导入中(可在任务进度中查看)..');
                    this.getList();
                } else {
                    this.$message.error('导入失败: ' + (response?.msg || '未知错误'));
                }
            } catch (error) {
                console.error('导入错误:', error);
                this.$message.error('导入失败: ' + (error.message || '未知错误'));
            } finally {
                // 确保 loading 状态在最后被关闭
                this.loading = false;
            }
        },
        // 新增：检查仓库是否已配置
        isWarehouseConfigured(warehouseId) {
            return this.configuredWarehouses.some(item => item.shopCode == warehouseId);
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 10px 5px 0;
    }
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}
.warehouse-config-dialog {
  /* Set max height for dialog */
  display: flex;
  flex-direction: column;
}

.warehouse-config-dialog .el-dialog__body {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.table-container {
  //flex: 1;
  //overflow: auto;
  margin-top: 15px;
}

.top {
  margin-bottom: 15px;
}
</style>
