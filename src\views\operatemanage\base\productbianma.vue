<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="编号:">
          <el-input v-model="filter.id" placeholder="编号"/>
        </el-form-item>
         <el-form-item label="标题:">
          <el-input v-model="filter.title" placeholder="标题"/>
        </el-form-item>
        <el-form-item label="商品编码:">
          <el-input v-model="filter.proBianma" placeholder="商品编码"/>
        </el-form-item>
        <el-form-item label="供应商:">
          <el-input v-model="filter.supplierName" placeholder="供应商"/>
        </el-form-item>
        <el-form-item label="平台:">
          <el-select v-model="filter.bianMaPlatform" placeholder="请选择">
            <el-option label="所有" value></el-option>
            <el-option label="国内" value='1'></el-option>
            <el-option label="跨境" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分类:">
          <el-select v-model="filter.BianMaCategoryId" placeholder="请选择">
             <el-option label="所有" value></el-option>
             <el-option v-for="item in bianMaCategoryPageList" :key="item.id" :label="item.categoryName" :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item label="状态:">
          <el-select v-model="filter.status" placeholder="请选择">
            <el-option label="所有" value></el-option>
            <el-option label="正常" value='1'></el-option>
            <el-option label="缺货" value="2"></el-option>
            <el-option label="停用" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'  @select='selectchange' :isSelection='true'
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='false'
              :loading="listLoading">
    </ces-table>
    
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getlist"
      />
    </template>

    <el-dialog title="导入商品编码" :visible.sync="dialogVisible1" width="30%">
      <span>
          <el-upload ref="upload1" class="upload-demo" 
                  :auto-upload="false"
                  :multiple="false"
                  :limit="1"
                  action
                  accept=".xlsx"
                  :http-request="uploadFile1"
                  :on-success="uploadSuccess1"
                  :file-list="fileList">
                <template #trigger>
                    <el-button size="small" type="primary">选取商品编码文件</el-button>
                </template> 
                <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload1">上传</my-confirm-button>
          </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible1 = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="导入组合商品编码" :visible.sync="dialogVisible2" width="30%">
      <span>
          <el-upload ref="upload2" class="upload-demo" 
                  :auto-upload="false"
                  :multiple="false"
                  :limit="1"
                  action
                  accept=".xlsx"
                  :http-request="uploadFile2"
                  :on-success="uploadSuccess2">
                <template #trigger>
                    <el-button size="small" type="primary">选取组合商品编码文件</el-button>
                </template> 
                <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传</my-confirm-button>
          </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible2 = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-drawer
      :title="formtitle"
      :modal="false"
      :wrapper-closable="true"
      :modal-append-to-body="false"
      :visible.sync="addFormVisible"
      direction="btt"
      size="'auto'"
      class="el-drawer__wrapper"
      style="position:absolute;"
    >
    <form-create ref="formcreate" :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
      <div class="drawer-footer">
        <el-button @click.native="addFormVisible = false">取消</el-button>
        <my-confirm-button type="submit"  :loading="addLoading" @click="onAddSubmit" />
      </div>
    </el-drawer>
  </my-container>
</template>

<script>
import { addOrUpdate, batchDeleteProductBianMa,getProductBianMaById,getPageList,importProductBianMaAsync,importProductBianMaZhuHeAsync,exportProductBianMa} from '@/api/operatemanage/base/productbianma'
import {getProductBianMaCategoryPageList } from '@/api/operatemanage/base/shop'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatYesornoBool,formatBianMaPlatform,formatYesornofinished,formatbianmastatus} from "@/utils/tools";
import {ruleProductBianMaCategory} from '@/utils/formruletools'
const tableCols =[
      {istrue:true,prop:'id',label:'编号', width:'180',sortable:'custom'},
      {istrue:true,prop:'proBianMa',label:'编码', width:'150',sortable:'custom'},
      {istrue:true,prop:'hasChildren',label:'是否组合', width:'120',sortable:'custom',formatter:(row)=>formatYesornoBool(row.hasChildren)},
      {istrue:true,prop:'bianMaPlatform',label:'平台', width:'100',formatter:(row)=>formatBianMaPlatform(row.bianMaPlatform)},
      {istrue:true,prop:'itemNo',label:'货号', width:'150',sortable:'custom'},      
      {istrue:true,prop:'title',label:'商品名称', width:'150',sortable:'custom'},
      {istrue:true,prop:'jianChen',label:'商品简称', width:'150',sortable:'custom'},
      {istrue:true,prop:'bianMaCategoryName',label:'分类', width:'150',sortable:'custom'},
      {istrue:true,prop:'virtualCategory',label:'虚拟分类', width:'150',sortable:'custom'},
      {istrue:true,prop:'brandName',label:'品牌', width:'100',sortable:'custom'},
      {istrue:true,prop:'spec',label:'颜色及规格', width:'150',sortable:'custom'},
      {istrue:true,prop:'costPrice',label:'成本价', width:'100',sortable:'custom'},
      {istrue:true,prop:'marketPrice',label:'市场价', width:'100',sortable:'custom'},
      {istrue:true,prop:'price',label:'销售价', width:'100',sortable:'custom'},
      {istrue:true,prop:'weight',label:'重量(kg)', width:'110',sortable:'custom'},
      {istrue:true,prop:'supplierName',label:'供应商', width:'150',sortable:'custom'},
      {istrue:true,prop:'isFinished',label:'成品?', width:'100',sortable:'custom',formatter:(row)=>formatYesornofinished(row.isFinished)},
      {istrue:true,prop:'status',label:'状态', width:'100',sortable:'custom',formatter:(row)=>formatbianmastatus(row.status)},
      {istrue:true,prop:'createdTime',label:'添加时间', width:'160'},
      {istrue:true,prop:'modifiedTime',label:'更新时间', width:'150',sortable:'custom'}
     ];
const tableHandles1=[
        {label:"新增", handle:(that)=>that.onAdd()},{label:"单个修改", handle:(that)=>that.onEdit()},{label:"批量删除", handle:(that)=>that.onDelete()},
        {label:"下载导入编码模板", handle:(that)=>that.onDownBianma()},{label:"导入编码", handle:(that)=>that.onImportBianma()},
        {label:"下载导入组合编码模板", handle:(that)=>that.onDownBianmaZhuhe()},{label:"导入组合编码", handle:(that)=>that.onImportBianmaZhuhe()},
        {label:"导出", handle:(that)=>that.onExport()}
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: { },
      list: [],
      bianMaCategoryPageList:[],
      pager:{OrderBy:"id",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      autoform:{
               fApi:{},
               rule:[],
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 8 }}}},
               rule:[{type:'hidden',field:'id',title:'id',value: '0'},
                      {type:'input',field:'proBianMa',title:'商品编码',validate: [{type: 'string', required: true, message:'请输入'}]},
                      {type:'input',field:'itemNo',title:'产品货号',validate: [{type: 'string', required: true, message:'请输入'}]},
                      {type:'select',field:'bianMaPlatform',title:'平台', value: 1,options: [{value:1, label:'国内'},{value:2, label:'跨境'}]},
                      {type:'input',field:'title',title:'商品标题',validate: [{type: 'string', required: true, message:'请输入商品标题'}]},
                      {type:'input',field:'jianChen',title:'商品简称',validate: [{type: 'string', required: true, message:'请输入'}]},
                      {type:'input',field:'spec',title:'颜色及规格',validate: [{type: 'string', required: true, message:'请输入'}]},
                      {type:'inputNumber',field:'costPrice',title:'成本价',value: 0,validate: [{type: 'number', required: true, message:'请输入'}]},
                      {type:'inputNumber',field:'marketPrice',title:'市场价',value: 0,validate: [{type: 'number', required: true, message:'请输入'}]},
                      {type:'inputNumber',field:'price',title:'销售价',value: 0,validate: [{type: 'number', required: true, message:'请输入'}]},
                      {type:'inputNumber',field:'weight',title:'重量(kg)',value: 0,validate: [{type: 'number', required: true, message:'请输入'}]},
                      {type:'inputNumber',field:'tJL',title:'体积长',value: 0,validate: [{type: 'number', required: true, message:'请输入'}]},
                      {type:'inputNumber',field:'tJW',title:'体积宽',value: 0,validate: [{type: 'number', required: true, message:'请输入'}]},
                      {type:'inputNumber',field:'tJH',title:'体积高',value: 0,validate: [{type: 'number', required: true, message:'请输入'}]},
                      {type:'input',field:'supplierName',title:'供应商名称',validate: [{type: 'string', required: true, message:'请输入'}]},
                      {type:'select',field:'bianMaCategoryId',title:'分类'},
                      {type:'input',field:'virtualCategory',title:'虚拟分类',validate: [{type: 'string', required: true, message:'请输入'}]},
                      {type:'input',field:'categoryName',title:'品牌',validate: [{type: 'string', required: true, message:'请输入'}]},
                      {type:'select',field:'isFinished',title:'成品?',value: true,options: [{value:true, label:'成品'},{value:false, label:'半成品'}]},
                      {type:'select',field:'status',title:'状态',value: 1,options: [{value:1, label:'正常'},{value:2, label:'缺货'},{value:3, label:'禁用'}]}]
        },
      total: 0,
      sels: [], 
      selids:[],
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,     
      deleteLoading: false,
      formtitle:"新增",
      dialogVisible1:false,
      dialogVisible2:false,
      fileList:[],
    }
  },
  mounted() {
    this.getlist()
    this.getBianMaCategory()
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async getBianMaCategory() {
      const res1 = await getProductBianMaCategoryPageList({CurrentPage:1,PageSize:100})
      this.bianMaCategoryPageList = res1.data?.list
    },
   async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {
        ...pager,
        ... this.filter
      }
      this.listLoading = true
      const res = await getPageList(params)
      this.listLoading = false
      if (!res?.success) { return }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => { d._loading = false })
      this.list = data
      this.selids=[]
    },
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    async onEdit() {
      if (this.selids.length==0){
           this.$message({message: "请先选择",type: "warning",});
           this.addFormVisible = false
           return
      }
      else if (this.selids.length>1){
           this.$message({message: "只能选择1行",type: "warning",});
           this.addFormVisible = false
           return
      }
      this.formtitle='编辑';
      this.addFormVisible = true
      var arr =await Object.keys(this.autoform.fApi)
      if(arr.length >0)  {
        await this.autoform.fApi.setValue({bianMaCategoryId:''})
        await this.autoform.fApi.updateRule('bianMaCategoryId',{... await ruleProductBianMaCategory()})
        await this.autoform.fApi.sync('bianMaCategoryId')
        const res = await getProductBianMaById(this.selids[0])
        await this.autoform.fApi.setValue(res.data)
     }
    },
    async onAdd() {
      this.formtitle='新增';
      this.addFormVisible = true
      var arr =await Object.keys(this.autoform.fApi)
      console.log(this.autoform.fApi)
     // if(arr.length ==0)
      //formCreate.create(rules, options) 
     // this.autoform.fApi.reload()
      // if(arr.length >0) {
        await this.autoform.fApi.setValue({bianMaCategoryId:''})
        await this.autoform.fApi.updateRule('bianMaCategoryId',{... await ruleProductBianMaCategory()})
        await this.autoform.fApi.sync('bianMaCategoryId')  
       //this.autoform.fApi.reload()
    },
    async onEditSubmit() {
      this.addFormVisible = true
      await onAddSubmit();
    },
    async onAddSubmit() {
       this.addFormVisible=true;
       this.addLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData()
          const res = await addOrUpdate(formData)
          this.getlist()
          console.log(formData)
          this.addFormVisible=false;
        }else{
          //todo 表单验证未通过
        }
     })
      this.addLoading=false;
    },
    async onDelete() {
      if (this.selids.length==0){
           this.$message({message: "请先选择",type: "warning"})
           return
      }
        this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const res = await batchDeleteProductBianMa({ids:this.selids.join()})
          if (!res?.success) return 
            this.getlist()
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },
   async onDownBianma(){
      let url = "../static/excel/operation/商品编码导入模板.xlsx";
      let link = document.createElement("a");
      let fileName = "商品编码导入模板" + ".xlsx";
      document.body.appendChild(link);
      link.href = url;
      link.dowmload = fileName;
      link.click();
      link.remove();
    },
  async onDownBianmaZhuhe(){
      let url = "../static/excel/operation/组合商品编码导入模板.xlsx";
      let link = document.createElement("a");
      let fileName = "组合商品编码导入模板" + ".xlsx";
      document.body.appendChild(link);
      link.href = url;
      link.dowmload = fileName;
      link.click();
      link.remove();
    },
    async onImportBianma(){this.dialogVisible1=true},
    async onImportBianmaZhuhe(){this.dialogVisible2=true},
    async onSubmitupload1() {this.$refs.upload1.submit()},
    async onSubmitupload2() {this.$refs.upload2.submit()},
    async uploadSuccess1(response, file, fileList) {
      debugger
      console.log(response)
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    async uploadSuccess2(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    async uploadFile1(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      const res = importProductBianMaAsync(form);
      this.$message({message: '上传成功,正在导入中...', type: "success"}); 
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      const res = importProductBianMaZhuHeAsync(form);
      this.$message({message: '上传成功,正在导入中...', type: "success"}); 
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
    async onExport() {
      const params = {...this.filter};
      var hasparm=false;
      for (let key of Object.keys(params)) {
        if( params[key])
            hasparm=true;
      }
      if(!hasparm){
         this.$message({
                      message: "请选择条件后导出！",
                      type: "warning",
                    });
            return;
       }
      var res= await exportProductBianMa(params);
      if(!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download','商品编码_' + new Date().toLocaleString() + '.xlsx' )
      aLink.click()
     },
  }
}
</script>
