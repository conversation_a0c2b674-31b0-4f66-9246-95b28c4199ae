<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>

                <el-button style="padding: 0;margin: 0;border: 0;">
                    发起时间
                    <el-date-picker style="width: 240px" v-model="filter.daterange1" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        clearable :picker-options="pickerOptions"></el-date-picker>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: 0;">
                    加班日期
                    <el-date-picker style="width: 240px" v-model="filter.daterange2" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        clearable :picker-options="pickerOptions"></el-date-picker>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-select v-model.trim="filter.status" filterable placeholder="审批状态"
                        style="width:120px" clearable>
                        <el-option label="异常" value="异常" />
                        <el-option label="审批中" value="审批中" />
                        <el-option label="审批通过" value="审批通过" />
                        <el-option label="审批拒绝" value="审批拒绝" />
                        <el-option label="已撤销" value="已撤销" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-input v-model.trim="filter.FqDeptArea" placeholder="区域" style="width:120px;" clearable
                        maxlength="20" />
                </el-button>
                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-input v-model.trim="filter.deptName" placeholder="部门" style="width:120px;" clearable
                        maxlength="20" />
                </el-button>
                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-input v-model.trim="filter.fqDeptFullName" placeholder="组织架构" style="width:120px;" clearable
                        maxlength="50" />
                </el-button>
                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-input v-model.trim="filter.fqUserName" placeholder="发起人" style="width:120px;" clearable
                        maxlength="20" />
                </el-button>
                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-select v-model="filter.rankId" style="width: 120px" size="mini"  placeholder="发起人职级" clearable>
                        <el-option v-for="item in rankList" :key="'rankId'+item.rankId" :label="item.rankName"
                            :value="item.rankId" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-input v-model.trim="filter.curSpDDUserNames" placeholder="当前审批人" style="width:120px;" clearable
                        maxlength="20" />
                </el-button>
                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-select v-model.trim="filter.isXingZheng" filterable placeholder="到达行政审批" 
                        style="width:120px" clearable>
                        <el-option label="已到行政审批" :value=true />
                        <el-option label="未到行政审批" :value=false />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-input v-model.trim="filter.errorReason" placeholder="异常原因" style="width:150px;" clearable
                        maxlength="20" />
                </el-button>

                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-input v-model.trim="filter.operUserName" placeholder="操作人" style="width:120px;" clearable
                        maxlength="20" />
                </el-button>
                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-input v-model.trim="filter.operResult" placeholder="操作结果" style="width:150px;" clearable
                        maxlength="20" />
                </el-button>

                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onTongBuShow">同步加班审批数据</el-button>
                <el-button type="primary" @click="onTongGuo">批量审批通过</el-button>
                <el-button type="primary" @click="onJuJueShow">批量审批拒绝</el-button>
                <el-button type="primary" @click="exportProps">导出</el-button>
            </el-button-group>
        </template>

        <vxetablebase :id="'OverWorkApproveWorkData20250425'" :border="true" :align="'center'" @select="selectchange"
            :tablekey="'OverWorkApproveWorkData20250425'" ref="table2" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols' 
            :loading="listLoading" style="width:100%;height:100%;margin: 0" :xgt="9999">
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

        
        <el-dialog title="同步加班审批数据" :visible.sync="dialogTongBuData.visible" width="30%" v-dialogDrag
            :close-on-click-modal="false">
            <span>
                流程发起日期
                <el-date-picker v-model="dialogTongBuData.fqDate" type="date" placeholder="选择日期">
                </el-date-picker>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogTongBuData.visible = false">关闭</el-button>
                <el-button @click="onTongBu" type="primary">确定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="拒绝加班审批" :visible.sync="dialogJuJueData.visible" width="30%" v-dialogDrag
            :close-on-click-modal="false">
            <span>
                拒绝原因
                <el-input v-model.trim="dialogJuJueData.remark" placeholder="拒绝原因" style="width:400px;" clearable
                maxlength="50" />
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogJuJueData.visible = false">关闭</el-button>
                <el-button @click="onJuJue" type="primary">确定</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>
<script>
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {  formatTime, pickerOptions } from "@/utils/tools";
import MyContainer from "@/components/my-container";

import { 
    GetOverWorkApproveRankList, GetOverWorkApproveDeptList,
    GetOverWorkApproveWorkDataPageList,OverWorkApproveProcessInstancesExecute,
    SyncOverWorkApproveWorkData,exportOverWorkApproveWorkData
} from '@/api/profit/overworkapprove'

const tableCols = [
    { type: 'checkbox', label: '', },
    { istrue: true, prop: 'isLock', label: '处理状态', sortable: 'custom', width: '80',type: 'html', formatter: (row) => {
            if (row.isLock == true) {
                return "<div style='color:orange;text-align: center;'>处理中</div>"
            } else {
                return "<div style='text-align: center;'>可处理</div>"
            }
        }  },
    { istrue: true, prop: 'fqUserName', label: '发起人', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'fqDeptArea', label: '区域', width: '80' },
    { istrue: true, prop: 'fqDeptName', label: '部门', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'fqDeptFullName', label: '组织架构', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'fqRankId', label: '职级', sortable: 'custom', width: '80', formatter: (row) => row.fqRankName },
    { istrue: true, prop: 'approveFqTime', label: '发起时间', sortable: 'custom', width: '140' },
    { istrue: true, prop: 'status', label: '数据状态', sortable: 'custom', width: '80',type: 'html', formatter: (row) => {
            if (row.status == "审批通过") {
                return "<div style='color:green;text-align: center;'>" + row.status + "</div>"
            } else if (row.status == "异常") {
                return "<div style='color:red;text-align: center;'>" + row.status + "</div>"
            } else {
                return "<div style='text-align: center;'>" + row.status + "</div>"
            }
        } 
    },
    { istrue: true, prop: 'overWorkStartTime', label: '加班开始时间', sortable: 'custom', width: '140' },
    { istrue: true, prop: 'overWorkEndTime', label: '加班结束时间', sortable: 'custom', width: '140' },
    { istrue: true, prop: 'overWorkHour', label: '加班时长', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'overWorkImg', label: '加班截图', width: '80', type: 'images'  },
    { istrue: true, prop: 'curSpDDUserNames', label: '当前审批人', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'syncDDTime', label: '最新同步时间', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'errorReason', label: '异常原因', sortable: 'custom', width: '150',type: 'html', 
        formatter: (row) => {return "<div style='color:red;text-align: center;'>" + row.errorReason + "</div>"}  
    },
    { istrue: true, prop: 'operUserName', label: '操作人', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'operTime', label: '操作时间', sortable: 'custom', width: '140' },
    { istrue: true, prop: 'operType', label: '操作类型', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'operResult', label: '操作结果', sortable: 'custom', width: '300' },
];
const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

export default {
    name: "OverWorkApproveWorkData",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            that: this,
            filter: {
                daterange1: [startDate, endDate],
                approveFqStratDate:null,
                approveFqEndDate:null,
                
                daterange2: [],
                overWorkStartDate:null,
                overWorkEndDate:null,

                isXingZheng:true,
            },
            pickerOptions: pickerOptions,
            tableCols: tableCols,
            total: 0,
            datalist: [],
            pager: { OrderBy: "approveFqTime", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],

            rankList:[],
            deptist:[],

            dialogTongBuData:{
                visible:false,
                fqDate:null,
            },
            dialogJuJueData:{
                visible:false,
                remark:null,
            }

        };
    },
    async created() {
    },
    async mounted() {
        await this.getSelectData();
        await this.onSearch();
    },
    methods: {
        async exportProps() {
            const params = {...this.pager,...this.filter}
        this.loading = true
        const { data } = await exportOverWorkApproveWorkData(params)
        this.loading = false
        const aLink = document.createElement("a");
        let blob = new Blob([data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', `加班审批${dayjs().format('MMDD')}.xlsx`)
        aLink.click()
    },

        async getSelectData() {
            const res1 = await GetOverWorkApproveRankList();
            this.rankList=res1?.data;

            const res2 = await GetOverWorkApproveDeptList();
            this.deptist=res2?.data;
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getpara() {
            this.filter.approveFqStratDate = null;
            this.filter.approveFqEndDate = null;
            if (this.filter.daterange1&&this.filter.daterange1.length>1) {
                this.filter.approveFqStratDate = this.filter.daterange1[0];
                this.filter.approveFqEndDate = this.filter.daterange1[1];
            }
            this.filter.overWorkStartDate = null;
            this.filter.overWorkEndDate = null;
            if (this.filter.daterange2&&this.filter.daterange2.length>1) {
                this.filter.overWorkStartDate = this.filter.daterange2[0];
                this.filter.overWorkEndDate = this.filter.daterange2[1];
            }

            let pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            return params;
        },
        async getList() {
            let params = this.getpara();
            console.log(params);
            this.listLoading = true;
            const res = await GetOverWorkApproveWorkDataPageList(params);
            this.listLoading = false;
            this.sels=[];
            console.log(this.sels,"this.selsthis.selsthis.sels");
            this.total = res.data?.total;
            this.datalist = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.sels=rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        onTongBuShow(){
            this.dialogTongBuData.fqDate=null;
            this.dialogTongBuData.visible=true;
        },
        //同步数据
        async onTongBu(){
            if(!this.dialogTongBuData.fqDate){
                this.$message.error('请选择发起日期')
                return;
            }
            this.$confirm("确认要执行同步的操作吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let res = await SyncOverWorkApproveWorkData({msg:this.dialogTongBuData.fqDate});
                if (res?.success) {
                    this.$message({ message: '正在后台执行中......请稍后刷新查看', type: "success" });
                    this.dialogTongBuData.visible=false;
                }
                else {
                    //this.$message({ message: '发生异常，请刷新后重试', type: "error" });
                }
            });
        },
        //审批通过
        async onTongGuo(){
            if(!this.sels||this.sels.length<=0){
                this.$message.error('请先勾选数据')
                return;
            }
            this.$confirm("确认要执行批量通过的操作吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let newSels=[];
                this.sels.forEach(f=>{
                    newSels.push({Id:f.id,status:"审批通过"});
                });
                let res = await OverWorkApproveProcessInstancesExecute(newSels);
                if (res?.success) {
                    this.$message({ message: '正在后台执行中......请稍后刷新查看', type: "success" });
                    this.onSearch();
                }
                else {
                    //this.$message({ message: '发生异常，请刷新后重试', type: "error" });
                }
            });
        },
        //审批拒绝
        onJuJueShow(){
            this.dialogJuJueData.remark=null;
            this.dialogJuJueData.visible=true;
        },
        async onJuJue(){
            if(!this.sels||this.sels.length<=0){
                this.$message.error('请先勾选数据')
                return;
            }
            this.$confirm("确认要执行批量拒绝的操作吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let newSels=[];
                this.sels.forEach(f=>{
                    newSels.push({Id:f.id,status:"审批拒绝",remark: this.dialogJuJueData.remark});
                });
                let res = await OverWorkApproveProcessInstancesExecute(newSels);
                if (res?.success) {
                    this.$message({ message: '正在后台执行中......请稍后刷新查看', type: "success" });
                    this.onSearch();
                }
                else {
                    //this.$message({ message: '发生异常，请刷新后重试', type: "error" });
                }
            });
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
