<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.materialCode" placeholder="耗材编码" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.orderNoInner" placeholder="订单号" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.orderNo" placeholder="线上订单号" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品" maxlength="50" clearable class="publicCss" />
        <!-- <el-input v-model.trim="ListInfo.expressNumber" placeholder="快递单号" maxlength="20" clearable class="publicCss" /> -->
         <div  class="publicCss" >
          <inputYunhan ref="productCode" :inputt.sync="ListInfo.expressNumber" v-model="ListInfo.expressNumber" width="200px"
              placeholder="快递单号(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
              :maxlength="21000" @callback="productCodeCallback" title="快递单号">
            </inputYunhan>
         </div>
     
        <el-input v-model.trim="ListInfo.expressCompany" placeholder="快递公司" maxlength="20" clearable
          class="publicCss"  style="margin-left: 40px;"/>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入新版包装费</el-button>
        <el-button type="primary" @click="setExportCols" :loading="exportloading">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'packingChargeNew202408041432'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :cstmExportFunc="onExport" :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入新版包装费" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="2" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
         :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getPackingChargeNewAsync, importPackingChargeNewAsync, ExportPackingChargeNewAsync,DeletePackingChargeNewAsync } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";

const tableCols = [
  { sortable: 'custom', width: '110', align: 'center', prop: 'dataDate', label: '日期', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'materialCode', label: '耗材编码', tipmesg: '商品编码', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'materialName', label: '耗材名称', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'expressNumber', label: '快递单号', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'expressCompany', label: '快递公司', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'basePrice', label: '基本售价', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'historicalCostPrice', label: '历史成本价', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'costPrice', label: '成本价', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'packer', label: '打包员', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'orderNoInner', label: '订单号', tipmesg: '内部订单号', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'channel', label: '渠道', },
  { sortable: 'custom', width: '120', align: 'center', prop: 'goodsCode', label: '商品', tipmesg: '商品编码', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'shipmentWarehouse', label: '发货仓', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'orderNo', label: '线上订单号', },
  { sortable: 'custom', width: '120', align: 'center', prop: 'productAbbreviation', label: '商品简称', },
  {istrue: true,type: "button",label:'操作',width: "80",btnList: [{ label: "当日删除", handle: (that, row) => that.deleteDay(row.dataDate)}]}

]
export default {
  name: "packingChargeNew",
  components: {
    MyContainer, vxetablebase,inputYunhan
  },
  data() {
    return {
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        materialCode: null,//耗材编码
        orderNoInner: null,//订单号
        orderNo: null,//线上订单号
        goodsCode: null,//商品
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      exportloading: false,
      fileHasSubmit:false,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    deleteDay(date){

      this.$confirm('是否删除该日期数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await DeletePackingChargeNewAsync({ date: date})
        if (success) {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      });
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      let files=[];
      files.push(file)
      this.fileList = fileList;
    },
    productCodeCallback(val) {
      this.ListInfo.expressNumber = val;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!this.fileHasSubmit) {
                return false;
            }
            this.fileHasSubmit = false;
            if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", this.fileList[0].raw);
      form.append("upfile2", this.fileList[1].raw);
      var res = await importPackingChargeNewAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit = true;
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getPackingChargeNewAsync(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
        this.tableData.forEach(item => {
          item.dataDate = dayjs(item.dataDate).format('YYYY-MM-DD')
        })
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async setExportCols() {

      await this.getList()
      if (this.total >= 999999) {
        this.$message.error('数据量过大，请添加条件筛选后导出')
        return;
      }

      await this.$refs.table.setExportCols();
    },
    async onExport(opt) {
      let pars = this.ListInfo;
      if (pars === false) {
        return;
      }
      const params = { ...pars, ...opt };
      let res = await ExportPackingChargeNewAsync(params);
      if (!res?.data) {
        return
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '违规前置分析_订单节点明细_' + new Date().toLocaleString() + '_.xlsx')
      aLink.click()
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 160px;
    margin-right: 5px;
  }
}
</style>
