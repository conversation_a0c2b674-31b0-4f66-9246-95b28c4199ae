<template>
  <MyContainer>
    <el-tabs v-model="activeName" style="height: 95%" @tab-click="tabClickMethod">
      <el-tab-pane label="汇总数据" name="first1" style="height: 100%">
        <summaryData ref="refsummaryData" />
      </el-tab-pane>
      <el-tab-pane label="快递理赔" name="first2" style="height: 100%" lazy>
        <expressDeliveryCompensation ref="refexpressDeliveryCompensation" />
      </el-tab-pane>
      <el-tab-pane label="客服导入数据" name="first3" style="height: 100%" lazy>
        <customerServiceImportData ref="refcustomerServiceImportData" />
      </el-tab-pane>
    </el-tabs>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import summaryData from "./components/summaryData.vue";
import expressDeliveryCompensation from "./components/expressDeliveryCompensation.vue";
import customerServiceImportData from "./components/customerServiceImportData.vue";
export default {
  name: 'expressClaimsIndex',
  components: {
    MyContainer, summaryData, expressDeliveryCompensation, customerServiceImportData
  },
  data() {
    return {
      that: this,
      activeName: "first1",
    };
  },
  methods: {
    tabClickMethod(e) {
      if (e.name == 'first1') {
        this.$nextTick(() => {
          this.$refs.refsummaryData.getList()
        })
      }
    },
  }
};
</script>

<style lang="scss" scoped></style>
