<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTakeTime" :endDate.sync="ListInfo.endTakeTime"
                    :clearable="false" class="publicCss" startPlaceholder="绑定批次剩余超时时间" endPlaceholder="绑定批次剩余超时时间"
                    style="width: 220px;" />
                <dateRange :startDate.sync="ListInfo.startPayTime" :endDate.sync="ListInfo.endPayTime" class="publicCss"
                    startPlaceholder="平台付款时间" endPlaceholder="平台付款时间" style="width: 220px;" />
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.orderNoInners" v-model="ListInfo.orderNoInners"
                    placeholder="内部订单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="orderNoInnersCallback($event, 1)" title="内部订单号"
                    style="width: 150px;margin:0 5px 0 0;" width="150px">
                </inputYunhan>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.orderNoOnlines" v-model="ListInfo.orderNoOnlines"
                    placeholder="线上订单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="orderNoInnersCallback($event, 2)" title="线上订单号"
                    style="width: 150px;margin:0 5px 0 0;" width="150px">
                </inputYunhan>
                <inputYunhan :inputt.sync="ListInfo.waveIds" v-model="ListInfo.waveIds" placeholder="批次/若输入多条请按回车"
                    :clearable="true" :clearabletext="true" :maxRows="500" :maxlength="1000000"
                    @callback="orderNoInnersCallback($event, 3)" title="批次" style="width: 150px;margin:0 5px 0 0;"
                    width="150px" />
                <el-input v-model.trim="ListInfo.expressCompanyName" placeholder="物流公司" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.pickType" placeholder="类型" class="publicCss" clearable>
                    <el-option label="组团" value="组团" />
                    <el-option label="杂单" value="杂单" />
                </el-select>
                <el-select v-model="ListInfo.platforms" placeholder="平台" class="publicCss" clearable multiple
                    collapse-tags filterable>
                    <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <chooseWareHouse v-model="ListInfo.wmsIds" class="publicCss" style="width: 180px;"
                    :filter="sendWmsesFilter" multiple />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%"
            :showsummary="data.summary ? true : false" :summaryarry="data.summary" @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog :title="logQuery.title" :visible.sync="dialogVisible" width="70%" height="600px" v-dialogDrag>
            <codeLog v-if="dialogVisible" :logQuery="logQuery" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/Orders/InWmsNode/'
import { mergeTableCols } from '@/utils/getCols'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import codeLog from './codeLog.vue'
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, chooseWareHouse, codeLog
    },
    data() {
        return {
            api,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false,
                summarys: [],
                startTakeTime: dayjs().format('YYYY-MM-DD'),
                endTakeTime: dayjs().format('YYYY-MM-DD'),
                platforms: []
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            logQuery: {
                resource: null,
                unBatch_GoodsCode: null,
                title: null,
            },
            dialogVisible: false
        }
    },
    async mounted() {
        await this.getCol();
        await this.getList()
    },
    methods: {
        showLog(row, prop) {
            const params = {
                resource: prop,
                waveIds: row.waveId,
            }
            this.logQuery = { ...this.ListInfo, ...params, orderBy: null }
            this.title = row.waveId + '-' + row.pickType
            this.dialogVisible = true
        },
        sendWmsesFilter(wmses) {
            return wmses.filter((a) =>  a.isSendWarehouse == '是' && a.isWc != 1);
        },
        orderNoInnersCallback(val, type) {
            if (type == 1) {
                this.ListInfo.orderNoInners = val
            } else if (type == 2) {
                this.ListInfo.orderNoOnlines = val
            } else if (type == 3) {
                this.ListInfo.waveIds = val
            }
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                const arr = ['unPickGenCount', 'normalUnPickGenCount', 'willOverTimeUnPickGenCount', 'overTimeUnPickGenCount',
                    'unPickCount', 'normalUnPickCount', 'willOverTimeUnPickCount', 'overTimeUnPickCount',
                    'unPickCptCount', 'normalUnPickCptCount', 'willOverTimeUnPickCptCount', 'overTimeUnPickCptCount',
                    'unPackCount', 'normalUnPackCount', 'willOverTimeUnPackCount', 'overTimeUnPackCount',
                    'unCollectCount', 'normalUnCollectCount', 'willOverTimeUnCollectCount', 'overTimeUnCollectCount',
                    'collectCount', 'unOverTimeCollectCount', 'overTimeCollectCount']

                data.forEach(item => {
                    if (item.prop == 'orderNoInner') {
                        item.type = 'orderLogInfo'
                        item.orderType = 'orderNoInner'
                    }
                    if (item.prop == 'proCode') {
                        item.type = 'html'
                        item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
                    }
                    if (arr.includes(item.prop)) {
                        item.type = 'click'
                        item.handle = (that, row) => that.showLog(row, `inWmsNode_${item.prop}`)
                    }
                })
                this.tableCols = mergeTableCols(data)
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    data.list.forEach(item => {
                        item.waveId = item.waveId ? String(item.waveId) : item.waveId
                    })
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                console.log(error);
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 5px;

    .publicCss {
        width: 150px;
        margin: 0 5px 5px 0;
    }
}

::v-deep .el-select__tags-text {
    max-width: 40px;
}
</style>
