<template>
 <MyContainer >
   <template #header>
        <div style="margin-bottom:10px">
            <el-input placeholder="请输入内容" v-model="keyword" maxlength="20" class="input-with-select" style="width:200px">
                <template slot="prepend">sku</template>
                <el-button slot="append" icon="el-icon-search" @click="getList()"></el-button>
            </el-input>
        </div>
   </template>

   <template>
     <vxetablebase 
     :id="'skuList20240911'" 
     :tablekey="'skuList20240911'" 
     :tableData='tableData' 
     :tableCols='tableCols' 
     @sortchange='sortchange'
      :loading='loading' 
      :border='true' 
      :that="that" 
      ref="vxetable"  
      :showsummary='false' 
      @cellClick="selectColumn"
      >

      <template #left>
        <vxe-column  type="radio" width="60"  >
            <template #radio="{ row, checked }">
            <span class="custom-radio">
                <i v-if="checked" class="vxe-icon-success-circle-fill" style="color: green;"></i>
                <i v-else class="vxe-icon-success-circle"></i>
            </span>
            </template>
      </vxe-column>
      </template>
     </vxetablebase>
   </template>

   <!-- <template #footer>
      <my-pagination ref="pager" :total="total"  @get-page="getList"/>
    </template> -->
 </MyContainer>

 
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import dayjs from 'dayjs';
import { fuzzyQuerySku} from '@/api/kj/skuPricing.js';
const tableCols = [
    {  prop: 'product_sku', label: 'sku',  width: 'auto',},
    {  label: '尺寸',  width: 'auto',formatter: (row) => { 
        return row.product_length+"*"+row.product_width+"*"+row.product_height+'cm'}},
        // +(row.product_length*row.product_width*row.product_height).toFixed(2)
 { prop: 'product_weight', label: '重量',  width: '120px',},
];



export default {
 name: 'skuList',
 components: { vxetablebase, MyContainer, },
 props:{
    oldCurrentRadioRow:{
        type:Object,
        default:()=>{return {}}
    }
 },
 data() {
   return {
        that:this,
        // total:0,
        tableCols:tableCols,
        tableData:[],
        keyword:'',
        loading:false,
        currentRadio:null,
        oldRadio:null,
   };
 },
  async mounted(){
//    this.setSelectRow(this.currentRadio)
  },
 methods:{
    async getList(){
        if(!this.keyword) return
        this.tableData  = await fuzzyQuerySku({skuKeyWorld:this.keyword,limitCount:10})
        
    },
    sortchange(){

    },
    // setSelectRow(row){
    //     this.oldRadio = this.currentRadio
    //     this.currentRadio = row
    //     this.$refs.vxetable.$children[1].setRadioRow(row)
    //     console.log(1111)
    // },
    setSelectRow(row){
        this.$refs.vxetable.$children[1].setRadioRow(row)
    },
    selectColumn(current){
       
        this.currentRadio = current.row
        this.$refs.vxetable.$children[1].setRadioRow(current.row)
    },
    
 }
};
</script>

<style lang="scss" scoped>
::v-deep .el-input-group__prepend{
    background: white;
}
</style>
