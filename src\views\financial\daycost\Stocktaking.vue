<template>
  <container>
       <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='false'
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' :loading="listLoading">
    <template slot='extentbtn'>
      <el-button-group>
            <el-button style="padding: 0;margin: 0;"><el-input style="width: 160px" v-model.trim="filter1.goodscode"   placeholder="商品编码"/></el-button>
              <el-button style="padding: 0;margin: 0;">
                 <el-select filterable clearable v-model="filter1.sttype" placeholder="类型" style="width: 100px">
                  <el-option label="盘盈" :value="1"></el-option>
                  <el-option label="盘亏" :value="0"></el-option>            
                </el-select>
              </el-button>
          <el-button type="primary" @click="getlist">查询</el-button>
          <el-button type="primary" @click="getlist">刷新</el-button>
      </el-button-group>
    </template>
   </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
  </container>
</template>
<script>
import {pageDayCashRed,pageStockTaking} from '@/api/inventory/warehouse'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import {formatFeeShareOper,formatTime,formatYesornoBool} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'io_date',label:'单据日期', width:'150',sortable:'custom',formatter:(row)=>formatTime(row.io_date,'YYYY-MM-DD HH:mm:ss')},
      {istrue:true,prop:'creator_name',label:'创建人', width:'90',sortable:'custom'},
      {istrue:true,prop:'warehouse',label:'仓库', width:'200',sortable:'custom',formatter:(row)=>row.warehouse},
      {istrue:true,prop:'goodscode',label:'商品编码', width:'100',sortable:'custom'},
      {istrue:true,prop:'name',label:'商品名称', width:'300',},
      {istrue:true,prop:'qty',label:'盘点数量', width:'80',sortable:'custom'},
      {istrue:true,prop:'amont',label:'盘点损益', width:'80',sortable:'custom'},
      {istrue:true,prop:'r_qty',label:'盘点后数量', width:'100',sortable:'custom'},
      {istrue:true,prop:'ptype',label:'类型', width:'60',},
      {istrue:true,prop:'computeLoseStatus',label:'盘亏计算状态', width:'120',sortable:'custom',formatter:(row)=>{return row.computeLoseStatus==false?'未计算':'已计算'}},
      {istrue:true,prop:'status',label:'状态', width:'70',sortable:'custom',formatter:(row)=>{return row.status=='WaitConfirm'?'待确认':row.status=='Confirmed'?'生效':row.status=='Archive'?'归档':row.status=='Cancelled'?'取消':'作废'}},
     ];
const tableHandles=[
        // {label:"导入", handle:(that)=>that.onimport()},
        // {label:"下载导入模板", handle:(that)=>that.ondownloadmb('现金红包导入模板')},
        // //{label:"计算分摊", handle:(that)=>that.oncomput()},
        // //{label:"批量删除", handle:(that)=>that.onbatchDelete()},
        // {label:"刷新", handle:(that)=>that.getlist()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, container },
   props:{
       filter: { }
     },
  data() {
    return {
      filter1:{
        goodscode:null,
        sttype:null
      },
      that:this,
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [], 
      listLoading: false,
      pageLoading: false
    }
  },
  mounted() {
     //this.onSearch()
  },
  beforeUpdate() { },
  methods: {
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
         this.filter.startTime = null;
         this.filter.endTime = null;
          if (this.filter.timerange!=null) {
         this.filter.startTime = this.filter.timerange[0];
         this.filter.endTime = this.filter.timerange[1];
       }
      var pager = this.$refs.pager.getPager()
      const params = {...pager, ...this.pager, ... this.filter,... this.filter1}
      this.listLoading = true
      console.log("查询参数库存盘点",params)
      const res = await pageStockTaking(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      console.log("库存盘点数据",data)
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async onbatchDelete() {
       await this.$emit('ondeleteByBatch',this.shareFeeType);
    },
   async oncomput(){
      this.$emit('onstartcomput',this.shareFeeType);
   },
   async onimport(){
     await this.$emit('onstartImport',this.shareFeeType);
   },
   async ondownloadmb(name){
     await this.$emit('ondownloadmb',name);
   },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
  }
}
</script>
