<template>
    <MyContainer>
        <vxetablebase :id="'historyGoodsProps202408041705'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            style="width: 100%; height: 620px; margin: 0" />
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        <template #footer>
            <div class="btnGroup">
                <el-button @click="close">取 消</el-button>
                <el-button type="primary" @click="applyProduct"
                    v-if="checkPermission('pageGetSeriesGoodsData')">申请进货</el-button>
            </div>
        </template>

        <el-dialog title="进货" :visible.sync="applyVisible" width="70%" v-dialogDrag :modal="false">
            <el-table :data="applyProps" style="width: 100%" max-height="250" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55">
                </el-table-column>
                <el-table-column prop="picture" label="图片" width="150">
                    <template slot-scope="scope">
                        <el-image :src="scope.row.picture"></el-image>
                    </template>
                </el-table-column>
                <el-table-column prop="goodsCode" label="商品编码" width="120">
                </el-table-column>
                <el-table-column prop="goodsName" label="商品名称" width="120">
                </el-table-column>
                <el-table-column prop="styleCode" label="系列编码" width="120">
                </el-table-column>
                <el-table-column prop="count" label="进货数量" width="300">
                    <template slot-scope="scope">
                        <el-input-number v-model="scope.row.count" placeholder="进货数量" clearable :precision="0" :min="0"
                            :max="999999" :controls="false" />
                    </template>
                </el-table-column>
                <el-table-column prop="zip" label="仓库" width="120">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.wmsCoId" placeholder="仓库" @change="changeWms($event, scope.$index)">
                            <el-option v-for="item in newWareHouseList" :key="item.name" :label="item.name"
                                :value="item.wms_co_id" />
                        </el-select>
                    </template>
                </el-table-column>
            </el-table>
            <div style="display: flex;justify-content: space-between;">
                <div>
                    共{{ applyTableData.length }}条
                </div>
                <div class="btnGroup">
                    <el-button @click="applyVisible = false">取 消</el-button>
                    <el-button type="primary" @click="declareSubmit" :disabled="isDeclare"
                        v-if="checkPermission('pageGetSeriesGoodsData')">确认申报</el-button>
                </div>
            </div>

        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    pageGetCompanyGoodsForHistory,
    createCompanyGoods
} from '@/api/inventory/companyGoods'
import { getAllWarehouse } from '@/api/inventory/warehouse';
const platformStatus = [
    {
        label: 'pdd',
        value: '拼多多'
    },
    {
        label: 'jd',
        value: '京东'
    },
    {
        label: 'dy',
        value: '抖音'
    },
    {
        label: 'fx',
        value: '分销'
    },
    {
        label: 'tx',
        value: '淘系'
    },
    {
        label: 'al',
        value: '阿里'
    },
]
const tableCols = [
    { istrue: true, prop: 'picture', label: '图片', type: 'images', fixed: 'left' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', fixed: 'left', width: '150' },
    { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom' },
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', fixed: 'left' },
    { istrue: true, prop: 'count', label: '进货数量', sortable: 'custom', },
    { istrue: true, prop: 'wmsCoId', label: '仓库', sortable: 'custom', formatter: (row) => row.wmsName },
    { istrue: true, prop: 'platform', label: '运营平台', sortable: 'custom', formatter: (row) => platformStatus.find(x => x.label == row.platform).value },
    { istrue: true, prop: 'approveId', label: '申请人', sortable: 'custom', width: '150', formatter: (row) => row.approveName },
    { istrue: true, prop: 'approveTime', label: '申请时间', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'approveStatus', label: '状态', width: '150' },
]

export default {
    name: "historyGoodsProps",
    components: {
        MyContainer, vxetablebase
    },
    props: {
        historyRestockeInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
        applyTableData: {
            type: Array,
            default: () => {
                return []
            }
        },
        paltForm: {
            type: String,
            default: ''
        },
    },
    data() {
        return {
            platformStatus,
            isDeclare: false,
            that: this,
            tableCols,
            tableData: [],
            total: 0,
            ListInfo: {},
            options: [{
                value: '选项1',
                label: '黄金糕'
            },
            {
                value: '选项2',
                label: '双皮奶'
            },
            {
                value: '选项3',
                label: '蚵仔煎'
            },
            {
                value: '选项4',
                label: '龙须面'
            },
            {
                value: '选项5',
                label: '北京烤鸭'
            }
            ],
            applyVisible: false,
            applyProps: [],
            newWareHouseList: [],
            postData: []
        }
    },
    mounted() {
    },
    methods: {
        close() {
            this.$emit('close')
        },
        changeWms(e, i) {
            this.applyProps[i].wmsName = this.newWareHouseList.find(x => x.wms_co_id == e).name
        },
        async declareSubmit() {
            if (this.postData.length == 0) {
                return this.$message.error('请选择需要进货的商品')
            }
            this.postData.forEach(item => {
                if (item.count == null || item.count == '' || item.count == 0) {
                    this.$message.error('进货数量不能为空或者为0')
                    throw ('进货数量不能为空或者为0')
                }
                if (item.wmsCoId == null || item.wmsCoId == '') {
                    this.$message.error('请选择仓库')
                    throw ('请选择仓库')
                }
            })
            this.isDeclare = true
            const { success } = await createCompanyGoods(this.postData)
            if (success) {
                this.getList()
                this.$message.success('申报成功')
                this.$emit('getUserPlat')
                this.postData = []
                this.applyVisible = false
                this.isDeclare = false
            } else {
                this.isDeclare = false
            }
        },
        async applyProduct() {
            this.postData = []
            this.applyProps = this.applyTableData.map(item => {
                return {
                    picture: item.picture,
                    goodsCode: item.goodsCode,
                    goodsName: item.goodsName,
                    styleCode: item.styleCode,
                    seriesBrandId: item.brandId,
                    seriesBrandName: item.brandName,
                    platform: item.platform,
                    count: null,
                    wmsCoId: null,
                    wmsName: null,
                    platform: this.paltForm,
                }
            })
            const { data } = await getAllWarehouse();
            this.newWareHouseList = data.filter((x) => { return x.name.indexOf('代发') < 0; });
            this.applyVisible = true
        },
        handleSelectionChange(e) {
            this.postData = e
        },
        async getList() {
            this.ListInfo = this.historyRestockeInfo
            const { data, success } = await pageGetCompanyGoodsForHistory(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
            }
        },
        //页面数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pagesize = val;
            this.getList();
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },
        //排序查询
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.btnGroup {
    padding: 20px 50px 0;
    display: flex;
    justify-content: end;
}
</style>