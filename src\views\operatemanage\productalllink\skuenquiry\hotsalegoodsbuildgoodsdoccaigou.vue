<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :inline="true" :model="form" label-width="110px" label-position="right" :rules="cgjbmFormRules">
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="竞品平台：">
                            <el-select disabled v-model="form.platform" style="width:140px;">
                                <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="竞品ID：">
                            <span v-html="formatLinkProCode(form.platform, form.goodsCompeteId)"></span>
                            <!-- {{ form.goodsCompeteId}} -->
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="运营组：">
                            {{ form.yyGroupName }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="brandId" label="采购组：">
                            <el-select v-model="form.brandId" filterable style="width:200px">
                                <el-option v-for="item in brandlist" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :hidden="true">
                    <el-col :span="24">
                        <el-form-item label="表单主键：">
                            {{ form.id }}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="产品简称：">
                            {{ form.goodsCompeteShortName }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="竞品标题：">
                            {{ form.goodsCompeteName }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="项目：">
                            <el-select v-model="form.projectDept" style="width:172px; height: 29px"
                                placeholder="请选择项目" clearable>
                                <el-option label="项目部-大侠" value="项目部-大侠"/>
                                <el-option label="项目部-徐琛" value="项目部-徐琛"/>
                                <el-option label="项目部-左玉玲" value="项目部-左玉玲"/>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="编码附件：">
                            <a :href="form.attachmentExcel" style="color:blue">点击此处下载编码附件Excel</a>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="styleCode" label="款式编码：">
                            <el-input v-model="form.styleCode" :maxlength="40" style="width:180px"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="商品标签：" prop="labels">
                            <el-input v-model="form.labels" :maxlength="40" style="width:180px"></el-input>
                        </el-form-item>
                        <el-tooltip class="item" effect="dark" content="多个标签请用英文逗号隔开" placement="top-end">
                            <span><i class="el-icon-question"></i></span>
                        </el-tooltip>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="上新仓库：">
                            <el-select v-model="form.forNewWarehouse" disabled style="width:200px">
                                <!-- <el-option v-for="item in fomSendWarehouse4HotGoodsBuildGoodsDocList" :key="item.value" :label="item.label" :value="item.value" /> -->
                                <el-option v-for="item in fomSendWarehouse4HotGoodsBuildGoodsDocList" :key="item.name"
                                    :label="item.name" :value="item.wms_co_id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="竞品图片：">
                            <el-image style="width: 50px; height: 50px" :src="form.goodsCompeteImgUrl"
                                :preview-src-list="[form.goodsCompeteImgUrl]">
                            </el-image>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="质检报告：">
                            <el-image style="width: 50px; height: 50px" :src="form.inspectionReportImgUrl"
                                :preview-src-list="[form.inspectionReportImgUrl]">
                            </el-image>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="专利资质：">
                            <YhImgUpload :value.sync="form.patentQualificationImgUrls" :disabled="true" :limit="10">
                            </YhImgUpload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="专利资质pdf：">
                            <YhImgUpload :value.sync="form.patentQualificationPdfUrls" :disabled="true" :isImg="false"
                                accept=".pdf" :limit="10"></YhImgUpload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="属性：">
                          <el-input v-model="form.attributeTag" maxlength="100" :disabled="true" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="季节/节日：">
                          <el-input v-model="form.seasonOrFestivalTag" maxlength="100" :disabled="true" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="天气：">
                          <el-input v-model="form.weatherTag" maxlength="100" :disabled="true" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="温度：">
                          <el-input v-model="form.temperatureTag" maxlength="100" :disabled="true" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item prop="goodsState" label="产品状态：">
                            <el-select v-model="form.goodsState" disabled style="width:140px;">
                                <el-option v-for="item in goodsStatelist" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="包装图片：">
                            <YhImgUpload :value.sync="form.packingImgUrls" :limit="10" :disabled="true"></YhImgUpload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="编码标签：">
                            <el-input v-model="form.labels2" :maxlength="40" style="width:200px" disabled></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row v-if="form.isGroupMember">
                    <el-col :span="6">
                        <el-form-item prop="supplierPlatForm" label="供应商平台：">
                            <el-select v-model="calcCurSupplier.supplierPlatForm" style="width:140px;" disabled>
                                <el-option v-for="item in supplierPlatFormList" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6" :hidden="(calcCurSupplier.supplierPlatForm != 1)">
                        <el-form-item prop="supplierWxNum" label="微信账号：">
                            <!-- {{ calcCurSupplier.supplierWxNum}} -->
                            <el-input v-model="calcCurSupplier.supplierWxNum" style="width:200px" disabled></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="supplierName" label="供应商名称：">
                            <!-- {{ calcCurSupplier.supplierName}} -->
                            <el-input v-model="calcCurSupplier.supplierName" style="width:200px" disabled></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6" :hidden="(calcCurSupplier.supplierPlatForm != 2)">
                        <el-form-item label="供应商链接：">
                            <!-- {{ calcCurSupplier.supplierLink}} -->
                            <el-input v-model="calcCurSupplier.supplierLink" style="width:200px" disabled></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6" :hidden="(calcCurSupplier.supplierPlatForm != 2)">
                        <el-form-item prop="supplierGoodLink" label="产品链接：">
                            <!-- {{ calcCurSupplier.supplierGoodLink}} -->
                            <el-input v-model="calcCurSupplier.supplierGoodLink" style="width:200px" disabled></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item prop="outerPackaLanguage" label="外包装语言：">
                            {{ outerPackaLanguageFmt(form.outerPackaLanguage) }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="delayedPurchase" label="延时进货：">
                            {{ delayedPurchaseFmt(form.delayedPurchase) }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="remark" label="备注：">
                            {{ form.remark }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="ingredient" label="产品成分：">
                            <el-input v-model="form.ingredient" style="width:200px" disabled></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="18" v-show="form.isGroupMember">
                        <el-form-item prop="chatImgUrls" label="供应商聊天记录：">
                            <div style="width: 975px;">
                                <uploadfile ref="uploadChatImg" :islook="true" :minisize="true"
                                    :uploadInfo="form.chatImgUrls == null ? [] : form.chatImgUrls" :limit="10000"
                                    :accepttyes="'.image/jpg,image/jpeg,image/png'" />
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col v-if="calcCurSupplier.hasProductionLicense == 1" :span="6">
                        <el-form-item prop="hasProductionLicense" label="生产许可证：" style="float: left;">
                            <el-image style="margin-left:10px;width: 50px; height: 50px"
                                :src="calcCurSupplier.productionLicenseUrl"
                                :preview-src-list="[calcCurSupplier.productionLicenseUrl]">
                            </el-image>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-radio-group v-model="curSupplierId" size="mini" style="margin-right:10px;"
                            v-if="form.isGroupMember">
                            <el-radio-button v-for="item in form.supplierList" :label="item.id" :key="item.id">
                                {{ item.supplierName }}
                            </el-radio-button>
                        </el-radio-group>

                        <el-input v-model="searchJstSkuCode" :maxlength="40" style="width:200px"
                            placeholder="输入检索的编码关键字"></el-input>
                        <el-button type="primary" @click="onGetSearchJstSkuList()" :loading="searchJstSkuListLoading">
                            检索商品编码
                        </el-button>
                        <span v-html="searchJstSkuList"
                            style="color:black;font-size: 14px;font-weight: bold; margin-left: 10px;"></span>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <div :style="'height:' + tableHeight + 'px;'">
                            <!--列表-->
                            <ces-table ref="skutable" :showsummary="true" :summaryarry="summaryarry" :that='that'
                                :isIndex='false' :hasexpandRight='true' :hasexpand='true' :tableData='calcSkuTableData'
                                :tableCols='skuTableCols' :loading="listLoading" :isSelectColumn="false">
                                <template slot="right">
                                    <el-table-column width="200" label="长宽高（cm）" :render-header="renderHeader">
                                        <template slot-scope="scope">
                                            <el-input-number type="number" :precision="2" :min="0" :max="10000"
                                                :controls="false" v-model.number="scope.row.goodsLength" style="width:50px;"
                                                disabled>
                                            </el-input-number>*
                                            <el-input-number type="number" :precision="2" :min="0" :max="10000"
                                                :controls="false" v-model.number="scope.row.goodsWidth" style="width:50px;"
                                                disabled>
                                            </el-input-number>*
                                            <el-input-number type="number" :precision="2" :min="0" :max="10000"
                                                :controls="false" v-model.number="scope.row.goodsHeigdth"
                                                style="width:50px;" disabled>
                                            </el-input-number>
                                        </template>
                                    </el-table-column>
                                    <el-table-column width="70" label="重量（kg）" :render-header="renderHeader">
                                        <template slot-scope="scope">
                                            <el-input-number type="number" :precision="2" :min="0" :max="10000"
                                                :controls="false" v-model.number="scope.row.goodsWeight" style="width:50px;"
                                                disabled>
                                            </el-input-number>
                                        </template>
                                    </el-table-column>
                                    <el-table-column width="150" label="备注">
                                        <template slot-scope="scope">
                                            <el-input type="text" :maxlength="100" :controls="false"
                                                v-model="scope.row.remark" style="width:130px;" disabled>
                                            </el-input>
                                        </template>
                                    </el-table-column>
                                </template>
                            </ces-table>
                        </div>
                    </el-col>
                </el-row>
            </el-form>
        </template>
    </my-container>
</template>
<script>
import uploadfile from '@/components/Comm/uploadfile.vue';
import cesTable from "@/components/Table/table.vue";
import { platformlist, formatLinkProCode, sendWarehouse4HotGoodsBuildGoodsDocList } from "@/utils/tools";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import { getBuildGoodsDocAsync, buildGoodsDocUpdateCodeAsync, buildGoodsDocSyncJstAndDingDingPassAsync, getShowJstSkuCode, validateGoodsCodesAsync, buildGoodsDocDingDingNoPassAsync } from '@/api/operatemanage/productalllink/alllink';
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import { getAllProBrand } from '@/api/inventory/warehouse'
import { assertLiteral } from '@babel/types';
import { getAllWarehouse } from '@/api/inventory/warehouse'
const skuTableCols = [
    { istrue: true, prop: 'id', type: "inputtext", label: '主键', width: '180', display: false, isDisabled: (row) => true },
    { istrue: true, prop: 'goodsImageUrl', label: '商品图片', width: '80', type: 'images', isDisabled: (row) => true },
    { istrue: true, prop: 'yhGoodsCode', type: "inputtexttrim", label: '商品编码', width: '180', maxlength: 30, isRequired: true },
    { istrue: true, prop: 'tt', type: "click", label: '', width: '30', formatter: (row) => "🠟", handle: (that, row, column) => that.onCopyIndex(row) },
    { istrue: true, prop: 'yhGoodsName', type: "inputtext", label: '商品名称', maxlength: 40, isRequired: true },
    //{ istrue: true, prop: 'yhGoodsUnit', type: "inputtext", label: '品名单位', width: '80', isRequired: true },
    { istrue: true, prop: 'costPrice', type: "inputnumber", precision: 3, label: '成本单价', width: '100', min: 0, max: 900000, isDisabled: (row) => true },
    { istrue: true, prop: 'forNewWarehouse', type: "select", label: '上新仓库', width: '100', display: false, isDisabled: (row) => true, options: sendWarehouse4HotGoodsBuildGoodsDocList },
    { istrue: true, prop: 'goodsProgressType', type: "select", label: '商品类型', width: '90', isDisabled: (row) => true, options: [{ label: '成品', value: '成品' }, { label: '半成品', value: '半成品' }] },
    { istrue: true, prop: 'isMainSale', type: "select", label: '是否主卖', width: '80', isDisabled: (row) => true, options: [{ label: '是', value: true }, { label: '否', value: false }] },
    { istrue: true, prop: 'mainSaleAvgCount', type: "inputnumber", label: '主卖人均件数', width: '120', isDisabled: (row) => true, min: 0, max: 100000 },
    { istrue: true, prop: 'estimateStockInCount', type: "inputnumber", label: '预计进货数量', width: '120', isDisabled: (row) => true, min: 0, max: 900000 },
    { istrue: true, prop: 'estimateStockInAmount', type: "inputnumber", precision: 2, label: '预计进货金额', width: '120', isDisabled: (row) => true, min: 0, max: 999999999 }
];

var outerPackaLanguageOpt = [
    { label: '中文', value: 1 },
    { label: '英文', value: 2 }
]
var outerPackaLanguageFmt = (val) => {
    let opt = outerPackaLanguageOpt.find(x => x.value == val);
    if (opt)
        return opt.label;
    return val;
}

var delayedPurchaseOpt = [
    { label: '是', value: 1 },
    { label: '否', value: 0 }
]
const delayedPurchaseFmt = (val) => {
    let opt = delayedPurchaseOpt.find(x => x.value == val);
    if (opt)
        return opt.label;
    return val;
}

export default {
    name: "hotsalegoodsbuildgoodsdoccaigou",
    components: { MyContainer, MyConfirmButton, cesTable, YhImgUpload, uploadfile },
    data() {
        return {
            that: this,
            goodsStatelist: [
                { label: '新品', value: 1 },
                { label: '老品补SKU', value: 2 },
                { label: '代拍', value: 3 },
            ],
            //fomSendWarehouse4HotGoodsBuildGoodsDocList: sendWarehouse4HotGoodsBuildGoodsDocList,
            fomSendWarehouse4HotGoodsBuildGoodsDocList: [],
            form: {
                id: null,
                platform: null,
                goodsCompeteId: null,
                yyGroupName: null,
                goodsCompeteName: null,
                goodsCompeteImgUrl: null,
                patentQualificationImgUrl: null,
                patentQualificationImgUrls: null,
                patentQualificationPdfUrls: null,
                attachmentExcel: null,
                styleCode: null,
                labels: null,
                forNewWarehouse: null,
                goodsState: null,
                attributeTag: null,//属性
                seasonOrFestivalTag: null,//季节/节日
                weatherTag: null,//天气
                temperatureTag: null,//温度
                supplierList: []
            },
            cgjbmFormRules: {
                styleCode: [{ required: true, message: '请输入款式编码', trigger: 'blur' }],
                brandId: [{ required: true, message: '请输入采购组', trigger: 'blur' }],
            },
            skuTableCols: skuTableCols,
            platformlist: platformlist,
            skuTableData: [],
            summaryarry: {
                estimateStockInCount_sum: 0,
                estimateStockInAmount_sum: 0
            },
            listLoading: false,
            pageLoading: false,
            brandlist: [],
            searchJstSkuCode: "",
            searchJstSkuList: "",
            searchJstSkuListLoading: false,

            supplierPlatFormList: [{ value: 1, label: "微信" }, { value: 2, label: "1688" }],
            curSupplierId: null,
        };
    },
    async mounted() {
        this.getBrand();
    },
    computed: {
        tableHeight: function () {
            let rowsCount = 1;
            if (this.calcSkuTableData && this.calcSkuTableData.length > 0) {
                rowsCount = this.calcSkuTableData.length;
            }
            let rowsHeight = (rowsCount + 2) * 50 + 80;
            return rowsHeight > 400 ? 400 : rowsHeight;
        },
        calcCurSupplier: function () {
            if (this.form.supplierList && this.form.supplierList.length > 0) {
                let sp = this.form.supplierList.find(x => x.id == this.curSupplierId);
                if (sp)
                    return sp;
            }

            return {
                id: '',
                supplierName: '未知',
                bldDocId: "",
                createdTime: null,
                enabled: true,
                supplierGoodLink: null,
                supplierLink: null,
                supplierName: null,
                supplierPlatForm: 2,
                supplierWxNum: null,
            }
        },
        calcSkuTableData: function () {
            return this.skuTableData.filter(x => {
                if (x.id > -10000 && x.supplierId == this.curSupplierId) {
                    return true;
                }
                return false;
            });
        },
        calcSaveSkuTableData: function () {
            return this.skuTableData.filter(x => {
                if (x.id > -10000) {
                    return true;
                }
                return false;
            });
        },
    },
    methods: {
        outerPackaLanguageFmt,
        delayedPurchaseFmt,
        async getBrand() {
            let res2 = await getAllProBrand();
            let bl = [];
            res2.data.forEach(f => {
                if (f.value.indexOf("系统挂靠") > -1 || f.value.indexOf("代拍产品") > -1 || f.value.indexOf("无人认领") > -1)
                    bl.push({ value: f.key, label: f.value });
            });
            res2.data.forEach(f => {
                if (f.value.indexOf("系统挂靠") < 0 && f.value.indexOf("代拍产品") < 0 && f.value.indexOf("无人认领") < 0)
                    bl.push({ value: f.key, label: f.value });
            });
            this.brandlist = bl;
            var res3 = await getAllWarehouse();
            var warehouselist1 = res3.data;
            this.fomSendWarehouse4HotGoodsBuildGoodsDocList = warehouselist1;
        },
        formatLinkProCode: formatLinkProCode,
        async getSkuTableData(id) {
            console.log('dd ss bb')
            let form = this.form;
            // 清空from对象
            Object.keys(form).forEach(key => (form[key] = null));
            this.listLoading = true;
            const res = await getBuildGoodsDocAsync({ docOrChooseId: id });

            this.listLoading = false;
            if (res.data.brandId > 0) {
                res.data.brandId = res.data.brandId.toString();
            }
            this.skuTableData = res?.data?.goodsDtlEntities;
            let one = 0;
            this.skuTableData.map((item) => { one += item.estimateStockInCount });
            let two = 0;
            this.skuTableData.map((item) => { two += item.estimateStockInAmount });
            this.summaryarry.estimateStockInCount_sum = one;
            this.summaryarry.estimateStockInAmount_sum = two;

            this.form = { ...res?.data };

            if (this.form.supplierList && this.form.supplierList.length > 0)
                this.curSupplierId = this.form.supplierList[0].id;


            if (this.form.labels == null || this.form.labels == "") {
                this.form.labels = this.getwlabel(this.form.forNewWarehouse);
                //(this.form.forNewWarehouse == 0 ? "义乌" : this.form.forNewWarehouse == 1 ? "昌东" : this.form.forNewWarehouse == 91 ? "代拍产品" : "");
            }

            this.$nextTick(() => {
                this.$refs.uploadChatImg.setData(this.form.chatImgUrls);
            })
        },
        getwlabel(forNewWarehouse) {
            let wlabel = "";
            if (forNewWarehouse && forNewWarehouse > 0 && this.fomSendWarehouse4HotGoodsBuildGoodsDocList && this.fomSendWarehouse4HotGoodsBuildGoodsDocList.length > 0) {
                let opt = this.fomSendWarehouse4HotGoodsBuildGoodsDocList.find(x => x.wms_co_id == forNewWarehouse);
                if (opt && opt.goodsTag)
                    wlabel = opt.goodsTag;
            }

            return wlabel;
        },
        async saveSkuTableData() {
            let listData = [...this.calcSaveSkuTableData];
            let dtoData = {
                ...this.form
            };
            dtoData.goodsDtlEntities = listData;

            //20240624:所有建编码必须大写
            let lower=false;
            dtoData.goodsDtlEntities.forEach(f=>{
                if(f.yhGoodsCode&&f.yhGoodsCode!=f.yhGoodsCode.toUpperCase()){
                    lower=true;
                    //this.$message({ message: '商品编码必须大写', type: "error" });
                    return false;
                }
            });
            if(lower==true){
                this.$message({ message: '商品编码必须大写', type: "error" });
                return false;
            }
            var res = await buildGoodsDocUpdateCodeAsync(dtoData);
            if (res?.success) {
                this.$message({ message: '保存成功', type: "success" });
                return true;
            } else {
                return false;
            }

        },
        async spSkuTableData() {
            let listData = [...this.calcSaveSkuTableData];
            let dtoData = {
                ...this.form
            };
            dtoData.goodsDtlEntities = listData;

            //20240624:所有建编码必须大写
            let lower=false;
            dtoData.goodsDtlEntities.forEach(f=>{
                if(f.yhGoodsCode&&f.yhGoodsCode!=f.yhGoodsCode.toUpperCase()){
                    lower=true;
                    //this.$message({ message: '商品编码必须大写', type: "error" });
                    return false;
                }
            });
            if(lower==true){
                this.$message({ message: '商品编码必须大写', type: "error" });
                return false;
            }

            dtoData.brandApprovelPass = 1;

            let validateRes = await validateGoodsCodesAsync(dtoData);
            if (validateRes != null && validateRes.success) {
                if (validateRes.data != null && validateRes.data != "") {
                    this.$confirm('商品编码：' + validateRes.data + '在ERP中重复，继续提交将会覆盖，是否继续?', '提示', {
                        confirmButtonText: '继续', cancelButtonText: '取消', type: 'warning'
                    }).then(async () => {
                        let res = await buildGoodsDocSyncJstAndDingDingPassAsync(dtoData);
                        if (res?.success) {
                            this.$message({ message: '保存成功，请关注钉钉流程和聚水潭商品', type: "success" });
                            return true;
                        } else {
                            return false;
                        }
                    }).catch(() => {

                    });
                } else {
                    let res = await buildGoodsDocSyncJstAndDingDingPassAsync(dtoData);
                    if (res?.success) {
                        this.$message({ message: '保存成功，请关注钉钉流程和聚水潭商品', type: "success" });
                        return true;
                    } else {
                        return false;
                    }
                }
            } else {
                return false;
            }
        },
        async spNoSkuTableData() {
            var res = await buildGoodsDocDingDingNoPassAsync({ docId: this.form.id });
            if (res?.success) {
                this.$message({ message: '已驳回至运营', type: "success" });
                return true;
            } else {
                return false;
            }
        },
        async onCopyIndex(row) {
            let curBeCode = "";
            let curLateCode = "";
            let end2nBeCode = "";
            let curNum = -999;
            let isEndB = false;//数字拼了一个B
            let isEnd2n = false;//末尾只有2位数字
            let isTwoB = false;//第二行和第一行编码一致，末尾多了个B
            if (row && row.yhGoodsCode) {
                //得到下一行
                let myRowIndex = this.skuTableData.findIndex(f => f.id == row.id);
                let nextRowIndex = myRowIndex + 1;
                debugger
                let nextRow = this.skuTableData[nextRowIndex];
                if (nextRow && nextRow.yhGoodsCode && nextRow.yhGoodsCode.length > 1) {
                    let nextYhGoodsCode = nextRow.yhGoodsCode.substring(0, nextRow.yhGoodsCode.length - 1);
                    if (row.yhGoodsCode == nextYhGoodsCode) {
                        isTwoB = true;
                    }
                }
                let code = row.yhGoodsCode;
                let split = code.split('-');
                //通过  -  分割
                if (code && code.length > 0 && split.length > 1) {
                    // - 的前半部分
                    curBeCode = code.substring(0, code.lastIndexOf('-') + 1);
                    // - 的后半部分
                    curLateCode = split[split.length - 1];
                    if (!curLateCode || curLateCode.length < 2) {
                        this.$message({ message: '输入错误，此编码格式不支持递增', type: "error" });
                        return;
                    }
                    if (isNaN(curLateCode) && curLateCode.substring(curLateCode.length - 1) == "B") {
                        isEndB = true;
                        curLateCode = curLateCode.substring(0, curLateCode.length - 1);
                        if (isNaN(curLateCode)) {
                            isEnd2n = true;
                            end2nBeCode = code.substring(0, code.length - 3);
                            curLateCode = curLateCode.substring(curLateCode.length - 2);
                        }
                    }
                    else if (isNaN(curLateCode) && !isNaN(curLateCode.substring(curLateCode.length - 2))) {
                        isEnd2n = true;
                        end2nBeCode = code.substring(0, code.length - 2);
                        curLateCode = curLateCode.substring(curLateCode.length - 2);
                    }
                    //经过一番处理还是不能转数字，就提示
                    if (isNaN(curLateCode)) {
                        this.$message({ message: '输入错误，此编码格式不支持递增', type: "error" });
                        return;
                    }
                    curNum = parseInt(curLateCode);
                }
                else {
                    this.$message({ message: '输入错误，此编码格式不支持递增', type: "error" });
                    return;
                }
            }
            else {
                this.$message({ message: '请填写商品编码！', type: "error" });
                return;
            }
            let isBegin = false;
            this.form.supplierList.forEach(sp => {
                //已开始 或当前商品供应商 进入循环，当前供应商
                if (isBegin || sp.id == row.supplierId) {
                    var spSkuData = this.skuTableData.filter(sk => sk.supplierId == sp.id);
                    if (spSkuData && spSkuData.length > 0) {
                        let isStrB = true;
                        spSkuData.forEach(item => {
                            if (item.id == row.id) {
                                //找到商品 标记开始
                                isBegin = true;
                                if (!isTwoB)
                                    curNum++;
                            }
                            else if (isBegin) {
                                let strNum = curNum.toString();
                                if (strNum.length < curLateCode.length) {
                                    var bq = curLateCode.length - strNum.length;
                                    for (let j = 0; j < bq; j++) {
                                        strNum = "0" + strNum;
                                    }
                                }

                                if (isTwoB) {
                                    //两行一起递增模式
                                    item.yhGoodsCode = curBeCode + strNum + (isStrB ? "B" : "");
                                    if (isStrB) {
                                        curNum++;
                                        isStrB = false;
                                    }
                                    else {
                                        isStrB = true;
                                    }
                                }
                                else {
                                    //单行递增模式
                                    if (isEndB && isEnd2n) {
                                        item.yhGoodsCode = end2nBeCode + strNum + (isEndB ? "B" : "");
                                    }
                                    else if (isEnd2n) {
                                        item.yhGoodsCode = end2nBeCode + strNum;
                                    }
                                    else {
                                        item.yhGoodsCode = curBeCode + strNum + (isEndB ? "B" : "");
                                    }
                                    curNum++;
                                }
                            }

                        })
                    }
                }
            });
        },
        async onGetSearchJstSkuList() {
            this.searchJstSkuListLoading = true;
            var res = await getShowJstSkuCode({ skuCode: this.searchJstSkuCode });
            this.searchJstSkuListLoading = false;
            if (res.length > 0) {
                console.log(res);
                this.searchJstSkuList = res;
            }
            else {
                this.searchJstSkuList = "未匹配到此商品编码";
            }
        }
    },
};
</script>
<style lang="scss" scoped>.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

.tempdiv ::v-deep img {
    width: auto;
    max-width: 1000px;
}</style>
