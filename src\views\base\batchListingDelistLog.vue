<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <div class="top_one">
          <el-date-picker style="width: 220px" v-model="ListInfo.timerange" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" :start-placeholder="'开始时间'" :end-placeholder="'结束时间'"
            :picker-options="pickerOptions">
          </el-date-picker>
          <el-input v-model="ListInfo.logName" placeholder="操作人" style="width:110px;" maxlength="20" />
          <span class="spanCss">商品ID:</span>
          <div class="publicCss">
            <inputYunhan ref="productCode" :inputt.sync="ListInfo.proCodes" v-model="ListInfo.proCodes" width="250px"
              placeholder="请输入(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
              :maxlength="21000" @callback="productCodeCallback" title="商品ID">
            </inputYunhan>
          </div>
          <el-button type="primary" @click="getList('search')" style="margin-right: 20px;">搜索</el-button>
          <el-button type="primary" @click="onExport()" style="margin-right: 20px;">导出</el-button>
        </div>
      </div>
    </template>
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import inputYunhan from "@/components/Comm/inputYunhan";
import { GetProductUpDownLogPageList,ExportProductUpDownLogList } from "@/api/operatemanage/productmanager"

const tableCols = [
  { prop: 'proCode', label: '商品ID', sortable: 'custom', width: '120' },
  { prop: 'upDownStatus', label: '操作状态', sortable: 'custom', width: '80', align: 'center', formatter: (row) => row.upDownStatus == 1 ? '上架中' : row.upDownStatus == 2 ? '下架中' : row.upDownStatus == 3 ? '已上架' : row.upDownStatus == 4 ? '已下架' : row.upDownStatus == 5 ? '上架失败' : row.upDownStatus == 6 ? '下架失败' : row.upDownStatus == 7 ? '已删除':row.upDownStatus == 8 ? '删除失败':'' },
  { prop: 'opTime', label: '操作时间', sortable: 'custom', width: '150' },
  { prop: 'opEndTime', label: '操作完成时间', sortable: 'custom', width: '150' },
  { prop: 'opUserName', label: '操作人', sortable: 'custom', width: '80' },
  { prop: 'opResult', label: '操作结果', sortable: 'custom', width: '300' },
]
export default {
  name: "batchListingDelist",
  components: {
    MyContainer, vxetablebase, inputYunhan
  },
  data() {
    return {
      checkdata: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startDate: null,
        endDate: null,
        timerange: [
          formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
          formatTime(dayjs(), "YYYY-MM-DD"),
        ],
        proCodes: '',//商品ID
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    // await this.getList()
  },
  methods: {
    //加载父组件过来的数据
    async loadData(args) {
      // this.checkdata = checkdata
      this.ListInfo.proCodes = args.proCodes;
      await this.getList()
    },
    //获取列表
    async getList(type) {

      if (!this.ListInfo.timerange || this.ListInfo.timerange.length < 2) {
        this.$message({ type: 'error', message: '请输入日期' });
        return;
      }
      this.ListInfo.startDate = this.ListInfo.timerange[0];
      this.ListInfo.endDate = this.ListInfo.timerange[1];
      this.loading = true
      const res = await GetProductUpDownLogPageList({ ...this.ListInfo })
      this.loading = false
      if (res?.success) {
        this.tableData = res.data.list;
        this.total = res.data.total;
      } else {
        //获取列表失败
        //this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    //排序
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    //商品ID数据回调
    productCodeCallback(val) {
      this.ListInfo.proCodes = val;
    },
    async onExport() {
        if (!this.ListInfo.timerange || this.ListInfo.timerange.length < 2) {
          this.$message({ type: 'error', message: '请输入日期' });
          return;
        }
        this.ListInfo.startDate = this.ListInfo.timerange[0];
        this.ListInfo.endDate = this.ListInfo.timerange[1];
        this.loading = true
        var res = await ExportProductUpDownLogList({ ...this.ListInfo });
        this.loading = false
        if (!res?.data) {
            this.$message({ message: "没有数据", type: "warning" });
            return
        }
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '产品管理-批量上下架日志_' + new Date().toLocaleString() + '_.xlsx')
        aLink.click()
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 3px;

  .spanCss {
    display: flex;
    align-items: center;
    margin-right: 5px;
  }

  .publicCss {
    width: 250px;
    margin-right: 10px;
  }
}

.top_one {
  display: flex;
  width: 100%;
  margin-bottom: 10px;
}
</style>
