<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss"
          :clearable="false" />
        <el-select v-model="ListInfo.status" placeholder="状态" clearable style="width: 100px" class="publicCss" >
            <el-option label="待同步" value="待同步"></el-option>
            <el-option label="已同步" value="已同步"></el-option>
            <el-option label="同步失败" value="同步失败"></el-option>
        </el-select>
        <inputYunhan ref="proCodes" :inputt.sync="ListInfo.proCodes" v-model="ListInfo.proCodes"
          placeholder="产品ID/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500" :maxlength="1000000"
          @callback="proCodeCallback" title="产品ID" style="width: 200px;margin:0 10px 0 0;">
        </inputYunhan>
        <div>
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" @click="addSyncProduct">手动添加</el-button>
        </div>
      </div>
    </template>
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' border
      :summaryarry='summaryarry' :showsummary='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" style="width: 100%; margin: 0"
      :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
<!--
    <vxe-modal v-model="addSyncProCodeDialog.visible" :width="300" marginSize='-500' title="同步宝贝">
      <div class="top">
          <inputYunhan ref="addSyncProductIds" :inputt.sync="addSyncProCodeDialog.productIds"
            v-model="ListInfo.productIds" placeholder="产品ID/若输入多条请按回车" :clearable="true" :clearabletext="true"
            :maxRows="500" :maxlength="1000000" @callback="proCodeCallback" title="产品ID"
            style="width: 200px;margin:0 10px 0 0;">
          </inputYunhan>
          <el-button type="primary" @click="syncProduct">同步</el-button>
        </div>
    </vxe-modal> -->

  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { addOperateSyncProductRecord,getOperateSyncProductRecord } from "@/api/operatemanage/productmanager"
import inputYunhan from "@/components/Comm/inputYunhan";
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'proCode', label: '产品ID', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '状态', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '添加时间', formatter: (row) => row.createdTime ? dayjs(row.createdTime).format('YYYY-MM-DD HH:mm:ss') : '' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'syncTime', label: '同步时间', formatter: (row) => row.syncTime ? dayjs(row.syncTime).format('YYYY-MM-DD HH:mm:ss') : '' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '添加人' }
]
export default {
  name: "YunHanSyncPoructFromJst",
  components: {
    MyContainer, vxetablebase, dateRange, inputYunhan
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        proCodes: null,
        startTime: null,//开始时间
        endTime: null,//结束时间
        status:null
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
      isExport: false,
      summaryarry: {},
      // addSyncProCodeDialog: {
      //   visible: false,
      //   productids: null
      // }
    }
  },
  async mounted() {
    await this.getList();
  },
  methods: {
    proCodeCallback(val) {
      this.ListInfo.proCodes = val
    },
    async addSyncProduct() {
      if (!this.ListInfo.proCodes) {
        this.$message({
          message: '请输入产品ID',
          type: 'warning'
        })
        return
      }
      this.$confirm('将同步输入框内的产品, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async() => {
          const { data, success } = await addOperateSyncProductRecord(this.ListInfo);
          if (success) {
          this.$message.success('添加成功,请稍后查看同步结果！');
          this.getList();
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
    },
    // addSyncProCodeCallback(val) {
    //   this.addSyncProCodeDialog.productids = val
    // },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data, success } = await getOperateSyncProductRecord(this.ListInfo)
        if (success) {
          this.tableData = data.list
          this.summaryarry = data.summary
          this.total = data.total
          this.loading = false
        }
      } catch (error) {

      }
      this.loading = false
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5px;

  .publicCss {
    width: 200px;
    margin: 0 5px 5px 0px;
  }
}
</style>
