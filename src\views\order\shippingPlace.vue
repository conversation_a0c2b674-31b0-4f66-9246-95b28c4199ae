<template>
    <MyContainer>
        <!-- <template #header>
            <div class="top">
                <el-cascader style="width: 330px;" placeholder="发货地" collapse-tags clearable :options="options1"
                    :props="{ multiple: true, checkStrictly: true, filterable: true }" v-model="ListInfo.provinceCodes1"
                    filterable class="publicCss cascaderCss"></el-cascader>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
            </div>
        </template> -->
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' showsummary
            :summaryarry="summaryarry" @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' border
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { getSeriesMainPurchseSendGoodsInfo } from '@/api/bookkeeper/styleCodeRptData'
import inputYunhan from "@/components/Comm/inputYunhan";
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import { GetProvinceCityDistrict } from '@/api/inventory/purchase'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '系列编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'indexNo', label: 'erp编号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'purchaseDate', label: '采购日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'checkDate', label: '审核时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'qty', label: '数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'province', label: '发货省', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'provinceCityDistrict', label: '明细发货地', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, inputYunhan, chooseWareHouse
    },
    props: {
        filter: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
                provinceCodes1: [],//发货地
                provinceCodes: [],//发货地
            },
            summaryarry: {},
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            options1: []
        }
    },
    async mounted() {
        this.ListInfo = { ...this.ListInfo, ...this.filter }
        this.getGetProvinceCityDistrict()
        await this.getList()
    },
    methods: {
        async getGetProvinceCityDistrict() {
            const { data, success } = await GetProvinceCityDistrict();
            if (!success) {
                return;
            }
            data.forEach(item => {
                delete item.children
            })
            this.options1 = data ? data : [];
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo = { ...this.ListInfo, ...this.filter,}
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            // this.ListInfo.provinceCodes = this.ListInfo.provinceCodes1.flat()
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getSeriesMainPurchseSendGoodsInfo(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.summaryarry = data.summary
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}

.cascaderCss ::v-deep .el-input__inner {
    height: 28px !important;
}

::v-deep .el-cascader__search-input {
    margin: 0 0 0 2px;
}
</style>
