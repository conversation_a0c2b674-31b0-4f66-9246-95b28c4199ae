<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right" label-width="90px">
                <el-form-item label="加工单号:">
                    <el-input type="number" v-model="filter.receiptNo" placeholder="加工单号" style="width: 140px" />
                </el-form-item>
                <el-form-item label="日期:">
                    <el-date-picker style="width:230px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="提交开始日期" end-placeholder="提交结束日期" clearable :picker-options="pickerOptions"></el-date-picker>
                </el-form-item>
                <el-form-item label="标题:">
                    <el-input v-model="filter.title" placeholder="标题" />
                </el-form-item>
                <el-form-item label="成品编码:">
                    <el-input v-model="filter.goodsCode" placeholder="成品编码" />
                </el-form-item>
                <el-form-item label="半成品编码:">
                    <el-input v-model="filter.partiallyGoodsCode" placeholder="半成品编码" />
                </el-form-item>
                <br />
                <el-form-item label="仓库:">
                    <el-select v-model="filter.warehouse" placeholder="仓库" :clearable="true" :collapse-tags="true" style="width: 140px" filterable>
                        <el-option v-for="item in warehouseList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="平台:">
                    <el-select v-model="filter.platform" placeholder="平台" :clearable="true" :collapse-tags="true" style="width: 122px" filterable>
                        <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="运营组:" />
                <el-select v-model="filter.groupId" placeholder="运营组" :clearable="true" :collapse-tags="true" style="width: 122px" filterable>
                    <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-form-item label="状态:">
                    <el-select v-model="filter.status" placeholder="状态" :clearable="true" :collapse-tags="true" style="width: 122px" filterable>
                        <el-option label="加工待领" value='加工待领'>加工待领</el-option>
                        <el-option label="加工已领" value="加工已领">加工已领</el-option>
                        <el-option label="加工完成" value="加工完成">加工完成</el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="是否完成:">
                    <el-select v-model="filter.isFinish" placeholder="是否完成" :clearable="true" :collapse-tags="true" style="width: 80px" filterable>
                        <el-option v-for="item in IsFinishList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="加工人:">
                    <el-input v-model="filter.userNameList" placeholder="加工人" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>

        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :isSelectColumn="!isHistory" :loading="listLoading">
        </ces-table>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <!--加工单表单-->
        <el-dialog :title="addDialogTitle" :visible.sync="dialogFormVisible" width="50%" :close-on-click-modal="false" @close="onCloseAddForm" v-dialogDrag v-loading="addFromLoading" element-loading-text="拼命加载中">
            <el-form ref="addForm" :model="addForm" :rules="addFormRules" label-width="120px">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="21" :xl="21">
                                <el-form-item prop="goodsCode" label="成品编码">
                                    <el-input v-model="addForm.goodsCode" auto-complete="off" :readonly="true" />
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="24" :md="24" :lg="3" :xl="3">
                                <el-button @click="onSelctCp(0)" style="float: right ; font-size:14px" type="text">选择
                                </el-button>
                            </el-col>
                        </el-row>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="hopeFinishDate" label="希望完成时间">
                            <el-date-picker v-model="addForm.hopeFinishDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" style="width:100%" placeholder="请选择希望完成时间">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="hopeGoodsAmount" label="计划成品数量">
                            <el-input-number v-model="addForm.hopeGoodsAmount" :min="1" :max="100000000" placeholder="数量" auto-complete="off" style="width:100%" :precision="0">
                            </el-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
                        <el-form-item prop="goodsName" label="成品名称">
                            <el-input v-model="addForm.goodsName" auto-complete="off" :readonly="true" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="groupId" label="运营组">
                            <el-select v-model="addForm.groupId" placeholder="请选择运营组" style="width:100%;">
                                <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
                        <el-form-item prop="title" label="标题">
                            <el-input v-model="addForm.title" auto-complete="off" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="warehouse" label="加工仓库">
                            <el-select v-model="addForm.warehouse" placeholder="请选择加工仓库" style="width:100%;">
                                <el-option v-for="item in warehouseList" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
                        <el-form-item prop="remark" label="备注">
                            <el-input v-model="addForm.remark" auto-complete="off" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="platform" label="平台">
                            <el-select v-model="addForm.platform" placeholder="请选择平台" style="width:100%;">
                                <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- 隐藏字段 -->
                <el-row :hidden="true">
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="receiptNo" label="加工单号">
                            <el-input v-model="addForm.receiptNo" auto-complete="off" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-card class="box-card" style="width:100% ;height: 45px;overflow: hidden;">
                        <div slot="header" class="clearfix">
                            <span>半成品明细</span>
                            <el-button @click="onSelctCp(1)" style="float: right; padding: 3px 0" type="text">添加半成品明细</el-button>
                        </div>
                    </el-card>
                    <el-card class="box-card" style="width:100% ;height: 300px;overflow: auto;">
                        <el-table :data="addForm.dtlGoods">
                            <el-table-column label="序号" width="50">
                                <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                            </el-table-column>
                            <el-table-column prop="id" label="id" v-if="false" />
                            <el-table-column prop="dtlGoodsCode" label="商品编码" />
                            <el-table-column prop="dtlGoodsName" label="商品名称" />
                            <el-table-column prop="dtlGoodsAmount" label="计划数量">
                                <template slot-scope="scope">
                                    <el-input-number v-model="scope.row.dtlGoodsAmount" :min="1" :max="100000000" placeholder="数量" :precision="0">
                                    </el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column lable="操作">
                                <template slot-scope="scope">
                                    <el-button type="danger" @click="onDelDtlGood(scope.$index)">删除 <i class="el-icon-remove-outline"></i>
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-card>
                </el-row>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogFormVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :validate="addFormValidate" :loading="addLoading" @click="onAddSave" />
                </div>
            </template>
        </el-dialog>
        <!--领取加工单-->
        <el-dialog title="领取加工单" :visible.sync="dialogGetFormVisible" width="50%" v-dialogDrag v-loading="getFormLoading" :close-on-click-modal="false" @close="onCloseGetForm" element-loading-text="拼命加载中">
            <el-form ref="getForm" :model="getForm" label-width="120px">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="goodsCode" label="成品编码">
                            <el-input v-model="getForm.goodsCode" auto-complete="off" :disabled="true" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="receiptNo" label="加工单号">
                            <el-input v-model="getForm.receiptNo" auto-complete="off" :disabled="true" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="hopeFinishDate" label="希望完成时间">
                            <el-date-picker v-model="getForm.hopeFinishDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" :disabled="true" style="width:100%" placeholder="请选择希望完成时间">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
                        <el-form-item prop="goodsName" label="成品名称">
                            <el-input v-model="getForm.goodsName" auto-complete="off" :disabled="true" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="hopeGoodsAmount" label="计划成品数量">
                            <el-input v-model="getForm.hopeGoodsAmount" auto-complete="off" :disabled="true" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
                        <el-form-item prop="userNameList" label="加工人状态">
                            <el-input v-model="getForm.userNameList" auto-complete="off" :readonly="true" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="warehouse" label="加工仓库">
                            <el-select v-model="getForm.warehouse" placeholder="请选择加工仓库" style="width:100%;" :disabled="true">
                                <el-option v-for="item in warehouseList" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
                        <el-form-item prop="title" label="标题">
                            <el-input v-model="getForm.title" auto-complete="off" :disabled="true" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="groupId" label="运营组">
                            <el-select v-model="getForm.groupId" placeholder="请选择运营组" style="width:100%;" :disabled="true">
                                <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
                        <el-form-item prop="remark" label="备注">
                            <el-input v-model="getForm.remark" auto-complete="off" :disabled="true" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="platform" label="平台">
                            <el-select v-model="getForm.platform" placeholder="请选择平台" style="width:100%;" :disabled="true">
                                <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                    <el-tabs type="border-card" v-model="editableTabsValue">
                        <el-tab-pane label="半成品明细" key="tab1" name="tab1">
                            <el-container style="height:240px;">
                                <el-main style="height:240px;">
                                    <el-table :data="getForm.dtlGoods" :height="240">
                                        <el-table-column label="序号" width="50">
                                            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                                        </el-table-column>
                                        <el-table-column prop="id" label="id" v-if="false" />
                                        <el-table-column prop="dtlGoodsCode" label="商品编码" width="150" />
                                        <el-table-column prop="dtlGoodsName" label="商品名称" />
                                        <el-table-column prop="dtlGoodsAmount" label="计划数量" width="150" align="center" />
                                    </el-table>
                                </el-main>
                            </el-container>
                        </el-tab-pane>
                        <el-tab-pane label="半成品进度" key="tab2-2" name="tab2-2">
                            <el-container style="height:240px;">
                                <el-main style="height:240px;">
                                    <el-table :data="getForm.finishDtlGoods" :height="240">
                                        <el-table-column label="序号" width="50">
                                            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                                        </el-table-column>
                                        <el-table-column prop="receiptNo" label="receiptNo" v-if="false" />
                                        <el-table-column prop="userId" label="userId" v-if="false" />
                                        <el-table-column prop="processReceiptDetailId" label="processReceiptDetailId" v-if="false" />
                                        <el-table-column prop="userName" label="加工人" width="120" />
                                        <el-table-column prop="dtlGoodsCode" label="商品编码" width="150" />
                                        <el-table-column prop="dtlGoodsName" label="商品名称" />
                                        <el-table-column prop="dtlGoodsAmount" label="计划数量" width="120" align="center" />
                                        <el-table-column prop="dtlActualGoodsAmount" label="个人实际数量" width="120" align="center" />
                                    </el-table>
                                </el-main>
                            </el-container>
                        </el-tab-pane>
                        <el-tab-pane label="成品进度" key="tab2-1" name="tab2-1">
                            <el-container style="height:240px;">
                                <el-main style="height:240px;">
                                    <el-table :data="getForm.finishGoods" :height="240">
                                        <el-table-column label="序号" width="50">
                                            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                                        </el-table-column>
                                        <el-table-column prop="receiptNo" label="receiptNo" v-if="false" />
                                        <el-table-column prop="userId" label="userId" v-if="false" />
                                        <el-table-column prop="userName" label="加工人" width="120" />
                                        <el-table-column prop="goodsCode" label="成品编码" width="150" />
                                        <el-table-column prop="goodsName" label="成品名称" />
                                        <el-table-column prop="hopeGoodsAmount" label="计划成品数量" width="120" align="center" />
                                        <el-table-column prop="goodsAmount" label="个人实际数量" width="120" align="center" />
                                    </el-table>
                                </el-main>
                            </el-container>
                        </el-tab-pane>
                    </el-tabs>
                </el-col>
            </el-row>
            <el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeFunc" style="z-index:9999;" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogGetFormVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" @click="onReceive">
                        领取任务
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>
        <!--完成加工单-->
        <el-dialog :title="finishDialogTitle" :visible.sync="dialogFinishFormVisible" width="50%" v-dialogDrag v-loading="finishFormLoading" :close-on-click-modal="false" @close="onCloseFinishForm" element-loading-text="拼命加载中">
            <el-row style="width:98%;">
                <el-form ref="finishForm" :model="finishForm" :rules="finishFormRules" label-width="120px">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="receiptNo" label="加工单号">
                                <el-input v-model="finishForm.receiptNo" auto-complete="off" :disabled="true" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="hopeFinishDate" label="希望完成时间">
                                <el-date-picker v-model="finishForm.hopeFinishDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" :disabled="true" style="width:100%" placeholder="请选择希望完成时间">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="hopeGoodsAmount" label="计划成品数量">
                                <el-input v-model="finishForm.hopeGoodsAmount" auto-complete="off" :disabled="true" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="userName" label="当前加工人" v-if="!seeModel">
                                <el-input v-model="finishForm.userName" auto-complete="off" :disabled="true" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="finishDate" label="实际完成时间">
                                <el-date-picker v-model="finishForm.finishDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" :disabled="seeModel" style="width:100%" placeholder="请选择实际完成时间">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="goodsAmount" label="成品数量">
                                <el-input-number v-model="finishForm.goodsAmount" :min="0" :max="100000000" auto-complete="off" :disabled="seeModel" :precision="0">
                                </el-input-number>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="goodsCode" label="成品编码">
                                <el-input v-model="finishForm.goodsCode" auto-complete="off" :disabled="true" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
                            <el-form-item prop="goodsName" label="成品名称">
                                <el-input v-model="finishForm.goodsName" auto-complete="off" :disabled="true" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-row>
                                <el-col :xs="24" :sm="24" :md="24" :lg="20" :xl="20">
                                    <el-form-item prop="consumeGoodsCode" label="耗材编码">
                                        <el-input v-model="finishForm.consumeGoodsCode" auto-complete="off" :readonly="true" />
                                    </el-form-item>
                                </el-col>
                                <el-col :xs="24" :sm="24" :md="24" :lg="4" :xl="4">
                                    <el-button @click="onSelctCp(2)" style="float: right ; font-size:14px" type="text" :disabled="seeModel">选择</el-button>
                                </el-col>
                            </el-row>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="consumeGoodsName" label="耗材名称">
                                <el-input v-model="finishForm.consumeGoodsName" auto-complete="off" :readonly="true" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="consumePicture" label="耗材图片">
                                <el-image :src="(!!finishForm.consumePicture ? finishForm.consumePicture : imagedefault)" fit="scale-down" @click="showImg(finishForm.consumePicture)" :lazy="true"></el-image>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
                            <el-form-item prop="userNameList" label="加工人状态">
                                <el-input v-model="finishForm.userNameList" auto-complete="off" :readonly="true" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="warehouse" label="加工仓库">
                                <el-select v-model="finishForm.warehouse" placeholder="请选择加工仓库" style="width:100%;" :disabled="true">
                                    <el-option v-for="item in warehouseList" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
                            <el-form-item prop="title" label="标题">
                                <el-input v-model="finishForm.title" auto-complete="off" :disabled="true" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="groupId" label="运营组">
                                <el-select v-model="finishForm.groupId" placeholder="请选择运营组" style="width:100%;" :disabled="true">
                                    <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
                            <el-form-item prop="remark" label="备注">
                                <el-input v-model="finishForm.remark" auto-complete="off" :disabled="true" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="platform" label="平台">
                                <el-select v-model="finishForm.platform" placeholder="请选择平台" style="width:100%;" :disabled="true">
                                    <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </el-row>
            <el-row style="width:98%;">
                <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                    <el-tabs type="card" v-model="editableTabsValue2">
                        <el-tab-pane label="半成品明细" key="tab3" name="tab3">
                            <el-container style="height:220px;">
                                <el-main style="height:220px;">
                                    <el-table :data="finishForm.dtlGoods" :height="220">
                                        <el-table-column label="序号" width="50">
                                            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                                        </el-table-column>
                                        <el-table-column prop="receiptNo" label="receiptNo" v-if="false" />
                                        <el-table-column prop="userId" label="userId" v-if="false" />
                                        <el-table-column prop="processReceiptDetailId" label="processReceiptDetailId" v-if="false" />
                                        <el-table-column prop="dtlGoodsCode" label="商品编码" width="150" />
                                        <el-table-column prop="dtlGoodsName" label="商品名称" />
                                        <el-table-column prop="dtlGoodsAmount" label="计划数量" width="80" align="center" />
                                        <el-table-column prop="dtlActualGoodsAmount" :label="dtlActualGoodsAmountLable" width="150" align="center">
                                            <template slot-scope="scope">
                                                <el-input-number v-model="scope.row.dtlActualGoodsAmount" :min="0" :max="100000000" placeholder="实际数量" :disabled="seeModel" :precision="0">
                                                </el-input-number>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </el-main>
                            </el-container>
                        </el-tab-pane>
                        <el-tab-pane label="半成品进度" key="tab4-2" name="tab4-2">
                            <el-container style="height:220px;">
                                <el-main style="height:220px;">
                                    <el-table :data="finishForm.finishDtlGoods" :height="220">
                                        <el-table-column label="序号" width="50">
                                            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                                        </el-table-column>
                                        <el-table-column prop="receiptNo" label="receiptNo" v-if="false" />
                                        <el-table-column prop="userId" label="userId" v-if="false" />
                                        <el-table-column prop="processReceiptDetailId" label="processReceiptDetailId" v-if="false" />
                                        <el-table-column prop="userName" label="加工人" width="80" />
                                        <el-table-column prop="dtlGoodsCode" label="商品编码" width="150" />
                                        <el-table-column prop="dtlGoodsName" label="商品名称" />
                                        <el-table-column prop="dtlGoodsAmount" label="计划数量" width="100" align="center" />
                                        <el-table-column prop="dtlActualGoodsAmount" label="个人实际数量" width="140" align="center" />
                                    </el-table>
                                </el-main>
                            </el-container>
                        </el-tab-pane>
                        <el-tab-pane label="成品进度" key="tab4-1" name="tab4-1">
                            <el-container style="height:220px;">
                                <el-main style="height:220px;">
                                    <el-table :data="finishForm.finishGoods" :height="220">
                                        <el-table-column label="序号" width="50">
                                            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                                        </el-table-column>
                                        <el-table-column prop="receiptNo" label="receiptNo" v-if="false" />
                                        <el-table-column prop="userId" label="userId" v-if="false" />
                                        <el-table-column prop="consumeGoodsCode" label="consumeGoodsCode" v-if="false" />
                                        <el-table-column prop="consumeGoodsName" label="consumeGoodsName" v-if="false" />
                                        <el-table-column prop="consumePicture" label="consumePicture" v-if="false" />
                                        <el-table-column prop="finishDate" label="finishDate" v-if="false" />
                                        <el-table-column prop="userName" label="加工人" width="80" />
                                        <el-table-column prop="goodsCode" label="成品编码" width="150" />
                                        <el-table-column prop="goodsName" label="成品名称" />
                                        <el-table-column prop="hopeGoodsAmount" label="计划成品数量" width="100" align="center" />
                                        <el-table-column prop="goodsAmount" label="个人实际数量" width="140" align="center" />
                                        <el-table-column prop="" label="操作" width="80" align="center">
                                            <template slot-scope="scope">
                                                <el-button type="text" @click="onClickUpdateUserGoodsAmount(scope.row)" v-if="checkPermission('api:inventory:processingorder:AdminFinishProcessReceipt')||checkPermission('api:inventory:processingorder:AdminUpdateProcessReceipt')">修改</el-button>
                                                <el-button type="text" @click="onProcessReceiptOutUser(scope.row)" v-if="checkPermission('api:inventory:processingorder:AdminProcessReceiptOutUser')">踢掉</el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </el-main>
                            </el-container>
                        </el-tab-pane>
                    </el-tabs>
                </el-col>
            </el-row>

            <el-dialog title="个人明细" :visible.sync="showUpdateUserGoodsAmount" width='50%' height='500px' v-dialogDrag append-to-body :close-on-click-modal="false">
                <el-row style="width:98%;">
                    <el-form ref="updateUserGoodsAmountData" :model="updateUserGoodsAmountData" :rules="finishFormRules" label-width="120px">
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                                <el-form-item prop="userName" label="当前加工人">
                                    <el-input v-model="updateUserGoodsAmountData.userName" auto-complete="off" :disabled="true" />
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                                <el-form-item prop="finishDate" label="实际完成时间">
                                    <el-date-picker v-model="updateUserGoodsAmountData.finishDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" style="width:100%" placeholder="请选择实际完成时间">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                                <el-form-item prop="goodsAmount" label="成品数量">
                                    <el-input-number v-model="updateUserGoodsAmountData.goodsAmount" :min="0" :max="100000000" auto-complete="off" :precision="0">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                                <el-form-item prop="goodsCode" label="成品编码">
                                    <el-input v-model="updateUserGoodsAmountData.goodsCode" auto-complete="off" :disabled="true" />
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
                                <el-form-item prop="goodsName" label="成品名称">
                                    <el-input v-model="updateUserGoodsAmountData.goodsName" auto-complete="off" :disabled="true" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                                <el-row>
                                    <el-col :xs="24" :sm="24" :md="24" :lg="20" :xl="20">
                                        <el-form-item prop="consumeGoodsCode" label="耗材编码">
                                            <el-input v-model="updateUserGoodsAmountData.consumeGoodsCode" auto-complete="off" :readonly="true" />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :xs="24" :sm="24" :md="24" :lg="4" :xl="4">
                                        <el-button @click="onSelctCp(10)" style="float: right ; font-size:14px" type="text">选择</el-button>
                                    </el-col>
                                </el-row>
                            </el-col>
                            <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                                <el-form-item prop="consumeGoodsName" label="耗材名称">
                                    <el-input v-model="updateUserGoodsAmountData.consumeGoodsName" auto-complete="off" :readonly="true" />
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                                <el-form-item prop="consumePicture" label="耗材图片">
                                    <el-image :src="(!!updateUserGoodsAmountData.consumePicture ? updateUserGoodsAmountData.consumePicture : imagedefault)" fit="scale-down" @click="showImg(updateUserGoodsAmountData.consumePicture)" :lazy="true"></el-image>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-row>
                <el-row style="width:98%;">
                    <el-container style="height:300px;">
                        <el-main style="height:300px;">
                            <el-table :data="updateUserGoodsAmountData.dtlGoods" :height="220">
                                <el-table-column label="序号" width="50">
                                    <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                                </el-table-column>
                                <el-table-column prop="receiptNo" label="receiptNo" v-if="false" />
                                <el-table-column prop="userId" label="userId" v-if="false" />
                                <el-table-column prop="processReceiptDetailId" label="processReceiptDetailId" v-if="false" />
                                <el-table-column prop="userName" label="加工人" width="80" />
                                <el-table-column prop="dtlGoodsCode" label="商品编码" width="150" />
                                <el-table-column prop="dtlGoodsName" label="商品名称" />
                                <el-table-column prop="dtlGoodsAmount" label="计划数量" width="100" align="center" />
                                <el-table-column prop="dtlActualGoodsAmount" label="个人实际数量" width="140" align="center">
                                    <template slot-scope="scope">
                                        <el-input-number v-model="scope.row.dtlActualGoodsAmount" :min="0" :max="99999999" placeholder="个人实际数量" :precision="0">
                                        </el-input-number>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-main>
                    </el-container>
                </el-row>

                <template #footer>
                    <span class="dialog-footer">
                        <el-button type="primary" @click="onOkFinishUserGoodsAmount()" v-if="checkPermission('api:inventory:processingorder:AdminFinishProcessReceipt')">帮他完成</el-button>
                        <el-button type="primary" @click="onOkUpdateUserGoodsAmount()" v-if="checkPermission('api:inventory:processingorder:AdminUpdateProcessReceipt')">修改他的数据</el-button>
                        <el-button @click="showUpdateUserGoodsAmount = false">取 消</el-button>
                    </span>
                </template>
            </el-dialog>

            <el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeFunc" style="z-index:9999;" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogFinishFormVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :validate="finishFormValidate" @click="onFinish" v-if="!seeModel">
                        完成
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>
        <!--选择商品-->
        <el-dialog title="选择编码" :visible.sync="goodschoiceVisible" width='88%' height='500px' v-dialogDrag>
            <goodschoice :ischoice="true" ref="goodschoice" style="z-index:10000;height:500px" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="goodschoiceVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onQueren()">确 定</el-button>
                </span>
            </template>
        </el-dialog>
    </my-container>
</template>

<script>
    import { formatTime } from "@/utils";
    import { getDirectorGroupList } from '@/api/operatemanage/base/shop';
    import {
        pageProcessingOrder,
        deleteProcessingOrderAsync,
        addOrUpdateProcessReceipt,
        getProcessReceiptById,
        finishProcessReceipt, receiveProcessReceipt,
        getProcessReceiptUserFinish, getProcessReceiptUserFinishDetail, finishProcessReceiptBeforeCheck,
        getProcessReceiptFinish, getProcessReceiptFinishDetail,
        passProcessReceipt, cancelProcessReceipt,
        adminFinishProcessReceipt, adminUpdateProcessReceipt, adminProcessReceiptOutUser
    } from '@/api/inventory/processingorder';
    import MyContainer from '@/components/my-container';
    import MyConfirmButton from '@/components/my-confirm-button';
    import cesTable from "@/components/Table/table.vue";
    import { rulePlatform } from "@/utils/formruletools";
    import goodschoice from "@/views/base/goods/goods2.vue";
    import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
    import ret from "bluebird/js/release/util";

    const tableCols = [
        { istrue: true, prop: 'receiptNo', label: '加工单号', width: '95', sortable: 'custom' },
        { istrue: true, prop: 'warehouse', label: '仓库', width: '95', formatter: (row) => row.warehouseName || ' ', sortable: 'custom' },
        { istrue: true, prop: 'title', label: '标题', width: '95', sortable: 'custom', },
        { istrue: true, prop: 'goodsCode', label: '成品编码', width: '120', sortable: 'custom', },
        { istrue: true, prop: 'goodsName', label: '成品名称', width: '150', sortable: 'custom', },
        { istrue: true, prop: 'hopeGoodsAmount', label: '成品数量', width: '80', sortable: 'custom', },
        { istrue: true, prop: 'partiallyGoodsCode', label: '半成品编码', width: '200' },
        { istrue: true, prop: 'platform', label: '平台', width: '80', sortable: 'custom', formatter: (row) => row.platformName || ' ' },
        { istrue: true, prop: 'groupId', label: '运营组', width: '80', sortable: 'custom', formatter: (row) => row.groupName || ' ' },
        { istrue: true, prop: 'consumeGoodsCode', label: '耗材编码', width: '120', sortable: 'custom', },
        { istrue: true, prop: 'consumeGoodsName', label: '耗材名称', width: '150', sortable: 'custom', },
        { istrue: true, prop: 'consumePicture', label: '耗材图片', width: '80', type: 'image' },
        { istrue: true, prop: 'status', label: '状态', width: '80', sortable: 'custom', },
        { istrue: true, prop: 'isFinish', label: '是否完成', width: '60' },
        { istrue: true, prop: 'finishDate', label: '完成时间', width: '150', sortable: 'custom', },
        { istrue: true, prop: 'userCount', label: '加工人数', width: '80', },
        { istrue: true, prop: 'userNameList', label: '加工人', width: '120', sortable: 'custom', },
        { istrue: true, prop: 'goodsAmount', label: '完成数量', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'createName', label: '创建人', width: '80', sortable: 'custom', },
        { istrue: true, prop: 'createdTime', label: '创建时间', width: '150', sortable: 'custom', formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD HH:mm:ss') },
        {
            istrue: false, type: 'button', label: '操作', width: '280', fixed: 'right',
            btnList: [
                { label: "查看", handle: (that, row) => that.onLook(row) },
                { label: "编辑", handle: (that, row) => that.onHand(row), permission: "api:inventory:processingorder:AddOrUpdateProcessReceiptAsync" },
                { label: "删除", handle: (that, row) => that.onDelete(row), permission: "api:inventory:processingorder:DeleteProcessingOrderAsync" },
                { label: "领取", handle: (that, row) => that.onGet(row), },
                { label: "完成", handle: (that, row) => that.OpenFinish(row), permission: "api:inventory:processingorder:FinishProcessReceiptAsync" },
                { label: "归档", handle: (that, row) => that.onPass(row), permission: "api:inventory:processingorder:PassProcessReceiptAsync" },
                { label: "关闭", handle: (that, row) => that.onCancel(row), permission: "api:inventory:processingorder:CancelProcessReceiptAsync" }
            ]
        }];
    const tableHandles1 = [
        { label: "新增加工单", handle: (that) => that.OpenAdd(), permission: "api:inventory:processingorder:AddOrUpdateProcessReceiptAsync" },
    ];
    export default {
        name: 'Roles',
        components: { cesTable, MyContainer, MyConfirmButton, goodschoice, ElImageViewer },
        props: {
            isHistory: false,
        },
        data() {
            return {
                that: this,
                sels: [],
                //界面标题
                addDialogTitle: "",
                finishDialogTitle: "",
                filter: {
                    receiptNo: null,
                    beginDate: null,
                    endDate: null,
                    title: null,
                    goodsCode: "",
                    partiallyGoodsCode: "",
                    warehouse: null,
                    platform: null,
                    groupId: null,
                    isFinish: null,
                    status: null,
                    userName: null,
                    userNameList: null,
                    timerange: []
                },
                list: [],
                summaryarry: {},
                pager: { OrderBy: "CreatedTime", IsAsc: false },
                tableCols: tableCols,
                tableHandles: tableHandles1,
                groupList: [],//运营组下拉
                platformList: [],//平台下拉
                warehouseList: [
                    { label: '（本仓）', value: 1 },
                    { label: '南昌昌东', value: 3 }
                ],//仓库下拉
                IsFinishList: [
                    { label: '是', value: 1 },
                    { label: '否', value: 0 }
                ],
                total: 0,
                imgList: [],//完成界面图片显示器
                showGoodsImage: false,
                imagedefault: require('@/assets/images/detault.jpeg'),
                seeModel: false,//控制查看界面字段只读
                listLoading: false,
                pageLoading: false,
                addLoading: false,
                addFromLoading: false,
                getFormLoading: false,
                finishFormLoading: false,
                deleteLoading: false,
                pickerOptions: {
                    disabledDate(time) {
                        return time.getTime() > Date.now();
                    }
                },
                yesnoList: [
                    { value: true, label: "是" },
                    { value: false, label: "否" }
                ],
                dialogFormVisible: false,
                dialogGetFormVisible: false,
                dialogFinishFormVisible: false,
                addFormRules: {
                    goodsCode: [{ required: true, message: '请输入成品编码', trigger: 'blur' }],
                    goodsName: [{ required: true, message: '请输入成品名称', trigger: 'blur' }],
                    hopeGoodsAmount: [{ required: true, message: '请输入计划成品数量', trigger: 'blur' }],
                    warehouse: [{ required: true, message: '请输入加工仓库', trigger: 'blur' }],
                    hopeFinishDate: [{ required: true, message: '请输入希望完成时间', trigger: 'blur' }],
                    groupId: [{ required: true, message: '请输入运营组', trigger: 'blur' }],
                    platform: [{ required: true, message: '请输入希望平台', trigger: 'blur' }],
                    title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
                    remark: [{ required: true, message: '请输入备注', trigger: 'blur' }]
                },
                finishFormRules: {
                    finishDate: [{ required: true, message: '请输入实际完成时间 ', trigger: 'blur' }],
                    goodsAmount: [{ required: true, message: '请输入成品数量 ', trigger: 'blur' }],
                },
                //新增编辑界面数据
                addForm: {
                    receiptNo: 0,
                    goodsCode: '',
                    goodsName: '',
                    hopeGoodsAmount: 0,
                    warehouse: null,
                    groupId: null,
                    platform: null,
                    hopeFinishDate: null,
                    title: '',
                    remark: '',
                    dtlGoods: []
                },
                // 领取界面数据
                getForm: {
                    receiptNo: 0,
                    goodsCode: '',
                    goodsName: '',
                    warehouse: null,
                    groupId: null,
                    platform: null,
                    hopeFinishDate: null,
                    hopeGoodsAmount: 0,
                    title: '',
                    remark: '',
                    userNameList: '',
                    dtlGoods: [],
                    finishGoods: [],
                    finishDtlGoods: [],
                },
                // 完成界面数据
                finishForm: {
                    receiptNo: 0,
                    goodsCode: '',
                    goodsName: '',
                    consumeGoodsCode: '',
                    consumeGoodsName: '',
                    consumePicture: '',
                    hopeGoodsAmount: 0,
                    goodsAmount: 0,
                    warehouse: null,
                    groupId: null,
                    platform: null,
                    hopeFinishDate: null,
                    finishDate: null,
                    title: '',
                    remark: '',
                    userName: '',
                    userNameList: '',
                    dtlGoods: [],
                    finishGoods: [],
                    finishDtlGoods: [],
                },
                //选择商品窗口
                goodschoiceVisible: false,
                //0成品选择，1半成品选择
                seltype: 0,
                editableTabsValue: "tab1",
                editableTabsValue2: "tab3",
                dtlActualGoodsAmountLable: "实际总数量",

                //最高权限修改个人完成数量
                showUpdateUserGoodsAmount: false,
                updateUserGoodsAmountData: {
                    receiptNo: 0,
                    userId: null,
                    goodsCode: '',
                    goodsName: '',
                    consumeGoodsCode: '',
                    consumeGoodsName: '',
                    consumePicture: '',
                    goodsAmount: 0,
                    dtlGoods: [],
                },

            }
        },
        async mounted() {
            await this.getGroupList();
            await this.setPlatform();
            await this.getlist();
        },
        methods: {
            //设置平台下拉
            async setPlatform() {
                var pfrule = await rulePlatform();
                this.platformList = pfrule.options;
                if (this.platformList && this.platformList.length)
                    this.platformList.push({ label: '未知', value: 0 });
            },
            //获取运营组下拉
            async getGroupList() {
                var res2 = await getDirectorGroupList();
                this.groupList = res2.data?.map(item => { return { value: item.key, label: item.value }; });
            },
            //获取查询条件
            getCondition() {
                if (this.filter.timerange && this.filter.timerange.length > 1) {
                    this.filter.beginDate = this.filter.timerange[0];
                    this.filter.endDate = this.filter.timerange[1];
                }
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }
                return params;
            },
            //查询第一页
            async onSearch() {
                if (this.filter.receiptNo < 0 || this.filter.receiptNo > 999999999) {
                    this.$message({ message: '加工单号输入错误', type: 'error' });
                    return;
                }
                if (this.filter.receiptNo && this.filter.receiptNo.toString().indexOf(".") > -1) {
                    this.$message({ message: '加工单号输入错误', type: 'error' });
                    return;
                }
                this.$refs.pager.setPage(1)
                await this.getlist()
            },
            //分页查询
            async getlist() {
                this.filter.beginDate = null;
                this.filter.endDate = null;
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                if (this.filter.timerange) {
                    this.filter.beginDate = this.filter.timerange[0];
                    this.filter.endDate = this.filter.timerange[1];
                }

                this.listLoading = true
                var res = await pageProcessingOrder(params);
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summary;
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
            },
            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderBy = column.prop;
                    this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selsChange: function (sels) {
                this.sels = sels
            },
            //新增
            async OpenAdd() {
                this.addDialogTitle = "创建加工表单";
                this.dialogFormVisible = true;
            },
            //编辑按钮
            async onHand(row) {
                await this.onHandOpen(row, '编辑加工单');
            },
            async onHandOpen(row, title) {
                this.addDialogTitle = title;
                this.dialogFormVisible = true;
                this.addFromLoading = true;
                var Info = await getProcessReceiptById(row.receiptNo);
                if (Info) {
                    this.addForm.receiptNo = Info.receiptNo;
                    this.addForm.goodsCode = Info.goodsCode;
                    this.addForm.goodsName = Info.goodsName;
                    this.addForm.hopeGoodsAmount = Info.hopeGoodsAmount;
                    this.addForm.warehouse = Info.warehouse;
                    this.addForm.groupId = Info.groupId.toString();
                    this.addForm.platform = Info.platform;
                    this.addForm.hopeFinishDate = Info.hopeFinishDate;
                    this.addForm.title = Info.title;
                    this.addForm.remark = Info.remark;
                    this.addForm.dtlGoods = [];
                    Info.dtlGoods.forEach(f => {
                        this.addForm.dtlGoods.push({
                            id: f.id,
                            dtlGoodsCode: f.dtlGoodsCode, dtlGoodsName: f.dtlGoodsName, dtlGoodsAmount: f.dtlGoodsAmount
                        });
                    });
                } else {
                    this.$message({ message: '信息不存在！', type: 'success' });
                }
                this.addFromLoading = false;
            },
            //查看按钮
            async onLook(row) {
                this.seeModel = true;
                this.dtlActualGoodsAmountLable = "实际总数量";
                await this.OpenFinishDialog(row, '加工单详情');
            },
            //删除按钮
            async onDelete(row) {
                var deleteid = row.receiptNo;
                this.$confirm('确认要执行删除操作吗?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    const res = await deleteProcessingOrderAsync({ receiptNo: deleteid })
                    if (!res?.success) {
                        //this.$message({ type: 'success', message: res?.msg });
                    } else {
                        this.$message({ type: 'success', message: '删除成功!' });
                    }
                    this.onSearch()
                }).catch(() => {
                    //this.$message({ type: 'info', message: '已取消删除' });
                });
            },
            //移除明细
            async onDelDtlGood(index) {
                this.addForm.dtlGoods.splice(index, 1);
            },
            //关闭编辑界面时请清空表单数据
            onCloseAddForm() {
                this.addForm.receiptNo = 0;
                this.addForm.goodsCode = '';
                this.addForm.goodsName = '';
                this.addForm.hopeGoodsAmount = 0;
                this.addForm.warehouse = null;
                this.addForm.groupId = null;
                this.addForm.platform = null;
                this.addForm.hopeFinishDate = null;
                this.addForm.title = '';
                this.addForm.remark = '';
                this.addForm.dtlGoods = [];
            },
            //关闭领取界面时请清空表单数据
            onCloseGetForm() {
                this.getForm.receiptNo = 0;
                this.getForm.goodsCode = '';
                this.getForm.goodsName = '';
                this.getForm.warehouse = null;
                this.getForm.groupId = null;
                this.getForm.platform = null;
                this.getForm.hopeFinishDate = null;
                this.getForm.hopeGoodsAmount = 0;
                this.getForm.title = '';
                this.getForm.remark = '';
                this.getForm.userNameList = '';
                this.getForm.dtlGoods = [];
                this.getForm.finishGoods = [];
                this.getForm.finishDtlGoods = [];

                this.editableTabsValue = "tab1";
            },
            //关闭完成界面时请清空表单数据
            onCloseFinishForm() {
                this.finishForm.receiptNo = 0;
                this.finishForm.goodsCode = '';
                this.finishForm.goodsName = '';
                this.finishForm.consumeGoodsCode = '';
                this.finishForm.consumeGoodsName = '';
                this.finishForm.consumePicture = '';
                this.finishForm.hopeGoodsAmount = 0;
                this.finishForm.goodsAmount = 0;
                this.finishForm.warehouse = null;
                this.finishForm.groupId = null;
                this.finishForm.platform = null;
                this.finishForm.hopeFinishDate = null;
                this.finishForm.finishDate = null;
                this.finishForm.title = '';
                this.finishForm.remark = '';
                this.finishForm.userName = '';
                this.finishForm.userNameList = '';
                this.finishForm.dtlGoods = [];
                this.finishForm.finishGoods = [];
                this.finishForm.finishDtlGoods = [];

                this.editableTabsValue2 = "tab3";
            },
            //完成表单界面显示图片
            async showImg(e) {
                this.showGoodsImage = true;
                this.imgList = [];
                if (e) {
                    this.imgList.push(e);
                }
                else {
                    this.imgList.push(this.imagedefault);
                }
            },
            //完成表单界面关闭图片
            async closeFunc() {
                this.showGoodsImage = false;
            },
            //新增编辑提交时验证
            addFormValidate: function () {
                let isValid = false
                this.$refs.addForm.validate(valid => {
                    isValid = valid
                })
                return isValid
            },
            //新增编辑保存
            async onAddSave() {
                const para = _.cloneDeep(this.addForm);
                var res = await addOrUpdateProcessReceipt(para);
                if (!res?.success) {
                    return;
                }
                this.$message({
                    message: this.$t('保存成功'),
                    type: 'success'
                })
                this.$refs['addForm'].resetFields();
                this.dialogFormVisible = false;
                await this.onSearch();
            },
            //打开领取窗口
            async onGet(row) {
                this.getFormLoading = true;
                this.dialogGetFormVisible = true;
                var getLoadData = await getProcessReceiptById(row.receiptNo);
                if (getLoadData) {
                    this.getForm.receiptNo = getLoadData.receiptNo;
                    this.getForm.goodsCode = getLoadData.goodsCode;
                    this.getForm.goodsName = getLoadData.goodsName;
                    this.getForm.hopeGoodsAmount = getLoadData.hopeGoodsAmount;
                    this.getForm.hopeFinishDate = getLoadData.hopeFinishDate;
                    this.getForm.goodsAmount = getLoadData.goodsAmount;
                    this.getForm.warehouse = getLoadData.warehouse;
                    this.getForm.groupId = getLoadData.groupId.toString();
                    this.getForm.platform = getLoadData.platform;
                    this.getForm.title = getLoadData.title;
                    this.getForm.remark = getLoadData.remark;
                    this.getForm.userNameList = getLoadData.userNameList;
                    this.getForm.dtlGoods = [];
                    getLoadData.dtlGoods.forEach(f => {
                        this.getForm.dtlGoods.push({
                            id: f.id,
                            dtlGoodsCode: f.dtlGoodsCode, dtlGoodsName: f.dtlGoodsName, dtlGoodsAmount: f.dtlGoodsAmount
                        });
                    });
                    var finish = await getProcessReceiptFinish(row.receiptNo);
                    if (finish) {
                        finish.forEach(f => {
                            this.getForm.finishGoods.push({
                                receiptNo: f.receiptNo, userId: f.userId,
                                userName: f.userName,
                                goodsCode: f.goodsCode,
                                goodsName: f.goodsName,
                                consumeGoodsCode: f.consumeGoodsCode,
                                consumeGoodsName: f.consumeGoodsName,
                                consumePicture: f.consumePicture,
                                finishDate: f.finishDate,
                                hopeGoodsAmount: f.hopeGoodsAmount, goodsAmount: f.goodsAmount
                            });
                        });
                    }
                    var finishDtl = await getProcessReceiptFinishDetail(row.receiptNo);
                    if (finishDtl) {
                        finishDtl.forEach(f => {
                            this.getForm.finishDtlGoods.push({
                                receiptNo: f.receiptNo, userId: f.userId, processReceiptDetailId: f.processReceiptDetailId,
                                userName: f.userName,
                                dtlGoodsCode: f.dtlGoodsCode, dtlGoodsName: f.dtlGoodsName,
                                dtlGoodsAmount: f.dtlGoodsAmount, dtlActualGoodsAmount: f.dtlActualGoodsAmount
                            });
                        });
                    }
                }
                this.getFormLoading = false;
            },
            //领任务
            async onReceive() {
                const para = _.cloneDeep(this.getForm);
                var res = await receiveProcessReceipt(para);
                if (!res?.success) {
                    return;
                }
                this.$message({
                    message: this.$t('领取成功'),
                    type: 'success'
                });
                this.dialogGetFormVisible = false;
                await this.onSearch();
            },
            //打开完成窗口
            async OpenFinish(row) {
                var check = await finishProcessReceiptBeforeCheck(row.receiptNo);
                if (!check.success || !check.data) {
                    return;
                }
                this.seeModel = false;
                this.dtlActualGoodsAmountLable = "个人实际数量";
                await this.OpenFinishDialog(row, "完成加工单");
            },
            //打开完成窗口
            async OpenFinishDialog(row, title) {
                this.finishDialogTitle = title;//标题
                this.dialogFinishFormVisible = true;//显示弹窗
                this.finishFormLoading = true;//弹窗加载中
                await this.OpenFinishGetData(row.receiptNo);
                this.finishFormLoading = false;
            },
            async OpenFinishGetData(receiptNo) {
                var finishLoadData = await getProcessReceiptById(receiptNo);
                if (finishLoadData) {
                    this.finishForm.receiptNo = finishLoadData.receiptNo;
                    this.finishForm.goodsCode = finishLoadData.goodsCode;
                    this.finishForm.goodsName = finishLoadData.goodsName;
                    this.finishForm.consumeGoodsCode = finishLoadData.consumeGoodsCode;
                    this.finishForm.consumeGoodsName = finishLoadData.consumeGoodsName;
                    this.finishForm.consumePicture = finishLoadData.consumePicture;
                    this.finishForm.hopeGoodsAmount = finishLoadData.hopeGoodsAmount;
                    this.finishForm.warehouse = finishLoadData.warehouse;
                    this.finishForm.groupId = finishLoadData.groupId.toString();
                    this.finishForm.platform = finishLoadData.platform;
                    this.finishForm.hopeFinishDate = finishLoadData.hopeFinishDate;
                    this.finishForm.title = finishLoadData.title;
                    this.finishForm.remark = finishLoadData.remark;
                    this.finishForm.userNameList = finishLoadData.userNameList;
                    this.finishForm.dtlGoods = [];
                    if (this.seeModel) {//查看模式
                        this.finishForm.goodsAmount = finishLoadData.goodsAmount;
                        this.finishForm.finishDate = finishLoadData.finishDate;
                        this.finishForm.userName = finishLoadData.userName;
                        finishLoadData.dtlGoods.forEach(f => {
                            this.finishForm.dtlGoods.push({
                                id: f.id,
                                dtlGoodsCode: f.dtlGoodsCode, dtlGoodsName: f.dtlGoodsName,
                                dtlGoodsAmount: f.dtlGoodsAmount, dtlActualGoodsAmount: f.dtlActualGoodsAmount
                            });
                        });
                    }
                    else {//非查看模式
                        //个人完成信息
                        var finish = await getProcessReceiptUserFinish(receiptNo);
                        if (finish) {
                            //实际完成数量，实际完成时间
                            this.finishForm.goodsAmount = finish.goodsAmount;
                            this.finishForm.finishDate = finish.finishDate;
                            //实际当前加工人
                            this.finishForm.userName = finish.userName;
                            var finishDtl = await getProcessReceiptUserFinishDetail(receiptNo);
                            if (finishDtl) {
                                this.finishForm.dtlGoods = [];
                                //实际完成明细
                                finishDtl.forEach(f => {
                                    this.finishForm.dtlGoods.push({
                                        receiptNo: f.receiptNo, userId: f.userId, processReceiptDetailId: f.processReceiptDetailId,
                                        dtlGoodsCode: f.dtlGoodsCode, dtlGoodsName: f.dtlGoodsName,
                                        dtlGoodsAmount: f.dtlGoodsAmount, dtlActualGoodsAmount: f.dtlActualGoodsAmount
                                    });
                                });
                            }
                        }
                    }
                    this.finishForm.finishGoods = [];
                    var finish = await getProcessReceiptFinish(receiptNo);
                    if (finish) {
                        finish.forEach(f => {
                            this.finishForm.finishGoods.push({
                                receiptNo: f.receiptNo, userId: f.userId,
                                userName: f.userName,
                                goodsCode: f.goodsCode,
                                goodsName: f.goodsName,
                                consumeGoodsCode: f.consumeGoodsCode,
                                consumeGoodsName: f.consumeGoodsName,
                                consumePicture: f.consumePicture,
                                finishDate: f.finishDate,
                                hopeGoodsAmount: f.hopeGoodsAmount, goodsAmount: f.goodsAmount
                            });
                        });
                    }
                    this.finishForm.finishDtlGoods = [];
                    var finishDtl = await getProcessReceiptFinishDetail(receiptNo);
                    if (finishDtl) {
                        finishDtl.forEach(f => {
                            this.finishForm.finishDtlGoods.push({
                                receiptNo: f.receiptNo, userId: f.userId, processReceiptDetailId: f.processReceiptDetailId,
                                userName: f.userName,
                                dtlGoodsCode: f.dtlGoodsCode, dtlGoodsName: f.dtlGoodsName,
                                dtlGoodsAmount: f.dtlGoodsAmount, dtlActualGoodsAmount: f.dtlActualGoodsAmount
                            });
                        });
                    }
                }
            },
            //完成时验证
            finishFormValidate: function () {
                const para = _.cloneDeep(this.finishForm);
                let isValid = false
                this.$refs.finishForm.validate(valid => {
                    isValid = valid
                })
                return isValid
            },
            //完成
            async onFinish() {
                const para = _.cloneDeep(this.finishForm);
                para.userId = null;
                para.userName = null;
                var res = await finishProcessReceipt(para);
                if (!res?.success) {
                    return;
                }
                this.$message({
                    message: this.$t('完成'),
                    type: 'success'
                })
                this.$refs['finishForm'].resetFields();
                this.dialogFinishFormVisible = false;
                await this.onSearch();
            },
            //新增/编辑/完成界面的【选择商品】按钮
            onSelctCp(type) {
                this.seltype = type;
                this.goodschoiceVisible = true;
                this.$refs.goodschoice.removeSelData();
            },
            //选择商品确定
            async onQueren() {
                if (this.seltype == 0) {
                    //选择成品商品确定
                    var choicelist = await this.$refs.goodschoice.getchoicelistOnly();
                    if (choicelist && choicelist.length == 1) {
                        choicelist.forEach(f => {
                            //反填数据
                            this.addForm.goodsCode = f.goodsCode;
                            this.addForm.goodsName = f.goodsName;
                        })
                        this.goodschoiceVisible = false;
                    }
                }
                else if (this.seltype == 2) {
                    //完成界面选择商品
                    var choicelist = await this.$refs.goodschoice.getchoicelistOnly();
                    if (choicelist && choicelist.length == 1) {
                        choicelist.forEach(f => {
                            //反填数据
                            this.finishForm.consumeGoodsCode = f.goodsCode;
                            this.finishForm.consumeGoodsName = f.goodsName;
                            this.finishForm.consumePicture = f.picture;
                        })
                        this.goodschoiceVisible = false;
                    }
                }
                else if (this.seltype == 10) {
                    //完成界面选择商品
                    var choicelist = await this.$refs.goodschoice.getchoicelistOnly();
                    if (choicelist && choicelist.length == 1) {
                        choicelist.forEach(f => {
                            //反填数据
                            this.updateUserGoodsAmountData.consumeGoodsCode = f.goodsCode;
                            this.updateUserGoodsAmountData.consumeGoodsName = f.goodsName;
                            this.updateUserGoodsAmountData.consumePicture = f.picture;
                        })
                        this.goodschoiceVisible = false;
                    }
                }
                else {
                    //选择半成品商品确定
                    var choicelist = await this.$refs.goodschoice.getchoicelist();
                    if (choicelist && choicelist.length > 0) {
                        //反填数据,
                        if (this.addForm.dtlGoods) {
                            //已存在的不添加
                            var temp = this.addForm.dtlGoods;
                            var isNew = true;
                            choicelist.forEach(f => {
                                isNew = true;
                                temp.forEach(old => {
                                    if (old.dtlGoodsCode == f.goodsCode) {
                                        isNew = false;
                                    }
                                });
                                //
                                if (isNew) {
                                    this.addForm.dtlGoods.push({ dtlGoodsCode: f.goodsCode, dtlGoodsName: f.goodsName, dtlGoodsAmount: 0, dtlActualGoodsAmount: 0 });
                                } else {
                                    this.addForm.dtlGoods.forEach(told => {
                                        if (told.dtlGoodsCode == f.goodsCode) {
                                            told.dtlGoodsName = f.goodsName;
                                        }
                                    });
                                }
                            })

                        }
                        this.goodschoiceVisible = false;
                    }
                }
            },
            getDtlActualGoodsAmountLable() {
                if (seeModel == true) {
                    return "实际总数量";
                }
                return "个人实际数量";
            },
            async onPass(row) {
                this.$confirm('归档后无法执行任何其他操作了，确认要执行归档操作吗?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    const res = await passProcessReceipt(row.receiptNo)
                    if (res?.success) {
                        this.$message({ type: 'success', message: '归档成功!' });
                        this.onSearch()
                    }
                }).catch(() => {
                    //this.$message({ type: 'info', message: '已取消归档' });
                });
            },
            async onCancel(row) {
                this.$confirm('关闭后无法执行任何其他操作了，确认要执行关闭操作吗?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    const res = await cancelProcessReceipt(row.receiptNo)
                    if (res?.success) {
                        this.$message({ type: 'success', message: '关闭成功!' });
                        this.onSearch()
                    }
                }).catch(() => {
                    //this.$message({ type: 'info', message: '已取消' });
                });
            },
            async onClickUpdateUserGoodsAmount(row) {
                this.showUpdateUserGoodsAmount = true;
                this.updateUserGoodsAmountData = {
                    receiptNo: row.receiptNo,
                    userId: row.userId,
                    userName: row.userName,
                    finishDate: row.userName,
                    goodsCode: row.goodsCode,
                    goodsName: row.goodsName,
                    goodsAmount: row.goodsAmount,
                    consumeGoodsCode: row.consumeGoodsCode,
                    consumeGoodsName: row.consumeGoodsName,
                    consumePicture: row.consumePicture,
                    finishDate: row.finishDate,
                    dtlGoods: [],
                };
                console.log(row)
                console.log(this.updateUserGoodsAmountData)
                if (this.finishForm.finishDtlGoods && this.finishForm.finishDtlGoods.length > 0) {
                    this.finishForm.finishDtlGoods.forEach(f => {
                        if (f.userId == row.userId) {
                            this.updateUserGoodsAmountData.dtlGoods.push({
                                receiptNo: f.receiptNo,
                                userId: f.userId,
                                userName: f.userName,
                                processReceiptDetailId: f.processReceiptDetailId,
                                dtlGoodsCode: f.dtlGoodsCode,
                                dtlGoodsName: f.dtlGoodsName,
                                dtlGoodsAmount: f.dtlGoodsAmount,
                                dtlActualGoodsAmount: f.dtlActualGoodsAmount,
                            });
                        }
                    });
                }
            },
            async onOkFinishUserGoodsAmount() {
                this.$confirm('确认要执行【帮他完成】的操作吗?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    const res = await adminFinishProcessReceipt(this.updateUserGoodsAmountData)
                    if (res?.success) {
                        this.$message({ type: 'success', message: '操作成功!' });
                        await this.OpenFinishGetData(this.updateUserGoodsAmountData.receiptNo);
                        this.onSearch();
                        this.showUpdateUserGoodsAmount = false;
                    }
                }).catch(() => {

                });
            },
            async onOkUpdateUserGoodsAmount() {
                this.$confirm('确认要执行【修改他的数据】的操作吗?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    const res = await adminUpdateProcessReceipt(this.updateUserGoodsAmountData)
                    if (res?.success) {
                        this.$message({ type: 'success', message: '操作成功!' });
                        await this.OpenFinishGetData(this.updateUserGoodsAmountData.receiptNo);
                        this.onSearch();
                        this.showUpdateUserGoodsAmount = false;
                    }
                }).catch(() => {

                });
            },
            async onProcessReceiptOutUser(row) {
                this.$confirm('确认要踢掉' + row.userName + '吗?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    const res = await adminProcessReceiptOutUser({ receiptNo: row.receiptNo, userId: row.userId });
                    if (res?.success) {
                        this.$message({ type: 'success', message: '操作成功!' });
                        await this.OpenFinishGetData(row.receiptNo)
                        this.onSearch();
                    }
                }).catch(() => {

                });
            },
        }
    }
</script>
<style scoped>
    ::v-deep .el-link.el-link--primary {
        margin-right: 7px;
    }

    .el-form-item--mini.el-form-item,
    .el-form-item--small.el-form-item {
        margin-bottom: 15px;
    }
    .el-form-item >>> .el-image__inner.el-image__inner {
        width: 28px;
        height: 28px;
        max-width: 28px;
        max-height: 28px;
        line-height: 28px;
    }
    .el-form-item >>> .el-form-item__content {
        height: 29px;
    }
</style>
<style >
    .el-image__inner {
        max-width: 50px;
        max-height: 50px;
    }
</style>
