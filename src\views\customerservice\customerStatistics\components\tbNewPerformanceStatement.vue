<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <!-- <el-input v-model="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss" /> -->
                <!-- <el-input v-model="ListInfo.groupName" placeholder="组名称" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.sname" placeholder="姓名" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.snick" placeholder="昵称" maxlength="50" clearable class="publicCss" /> -->

                <el-select v-model="ListInfo.groupName" filterable placeholder="组名称" style="width:200px;" clearable
                    @change="onGroupNameChange">
                    <el-option v-for="item in filterGroupList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>

                <el-select v-model="ListInfo.sname" filterable placeholder="姓名" style="width:200px;" clearable
                    @change="onSnameChange">
                    <el-option v-for="item in filterSnameList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>

                <el-select v-model="ListInfo.snick" filterable placeholder="昵称" style="width:200px;" clearable>
                    <el-option v-for="item in filterSnickList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>

                <el-select v-model="ListInfo.snameLevel" placeholder="客服等级" clearable class="publicCss" style="width:200px;" >
                    <el-option v-for="(item, i) in options" :key="i" :label="item.label" :value="item.label" />
                </el-select>
                <el-date-picker v-model="timeList" type="daterange" align="right" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" class="publicCss"
                    @change="changeTime" :clearable="false">
                </el-date-picker>
                <el-button type="primary" @click="getList('search')">查询</el-button>
                <el-button type="primary" @click="exportProps">导出</el-button>
                <el-button type="primary" @click="openDialogVisible">设置提成系数</el-button>
                <el-button type="primary" @click="openGradeDlg">设置接待人数阈值</el-button>
            </div>
        </template>
        <vxetablebase :id="'tbNewPerformanceStatement202408041505'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
            :showsummary="true" :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            :noToFixed="true" :summaryarry="summaryarry" @summaryClick='onsummaryClick' v-loading="listLoading"
            style="width: 100%; height: 690px; margin: 0" />
        <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="total" @page-change="Pagechange"
            @size-change="Sizechange" />

        <el-dialog title="设置提成系数" :visible.sync="dialogVisible" width="50%" :close-on-click-modal="false" v-dialogDrag
            @close="handleClose">
            <div class="topbox">
                <el-select v-model="groupName" placeholder="组名选择" @change="changeGroupName" clearable>
                    <el-option v-for="(item, i) in GroupNameList" :key="i" :label="item" :value="item">
                    </el-option>
                </el-select>
                <el-select v-model="commission" placeholder="当前组历史提成系数" @change="changehistoryCom" clearable filterable>
                    <el-option v-for="(item, i) in historyComList" :key="i" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-date-picker v-model="commissionDate" type="daterange" align="right" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 220px;"
                    @change="changeComTime">
                </el-date-picker>
                <el-button type="primary" @click="clear(true)">新增提成系数</el-button>
                <el-button @click="addTable" type="primary">新增一行</el-button>
            </div>
            <el-table :data="commissionList" max-height="400" style="margin-bottom: 10px;">
                <el-table-column type="index" width="50" />
                <el-table-column prop="gradeWayValue1" label="金牌提成系数">
                    <template #default="{ row, $index }">
                        <el-input-number v-model="row.gradeWayValue1" placeholder="金牌提成系数" :min="0" :max="9999"
                            :controls="false" :precision="5" />
                    </template>
                </el-table-column>
                <el-table-column prop="gradeWayValue2" label="银牌提成系数">
                    <template #default="{ row, $index }">
                        <el-input-number v-model="row.gradeWayValue2" placeholder="银牌提成系数" :min="0" :max="9999"
                            :controls="false" :precision="5" />
                    </template>
                </el-table-column>
                <el-table-column label="计算日期范围">
                    <template #default="{ row, $index }">
                        <div v-if="commissionDate">
                            <span>{{ row.startEffectiveDate ? row.startEffectiveDate : '' }}</span>至
                            <span>{{ row.endEffectiveDate ? row.endEffectiveDate : '' }} </span>
                        </div>
                        <span v-else></span>
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template #default="{ row, $index }">
                        <el-button type="danger" @click="deleteCommission(row, $index)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="footer">
                <div class="btnbox">
                    <el-button @click="handleClose" style="margin-right: 20px;">取消</el-button>
                    <el-button type="primary" @click="handleSubmit">保存</el-button>
                </div>
            </div>
            <div style="color: red;margin-top:20px;text-align:center">
                特殊说明:提成系数失效后,不会默认使用原有提成系数计算,将按照0来进行计算,请在提成系数有效期内,及时设置下一个时间范围提成系数。</div>
        </el-dialog>

        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false" direction="btt">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" @change="chatSearch" :picker-options="pickerOptions" style="margin: 10px;" />
                <buschar :analysisData="chatProp.data"></buschar>
            </div>
            <div v-else v-loading="chatProp.chatLoading"></div>
        </el-drawer>

        <el-dialog title="设置接待人数阈值" :visible.sync="gradeCommissionVisible" width="50%" :before-close="handleClose"
            v-dialogDrag>
            <el-table :data="gradeCommissionData" style="width: 100%">
                <el-table-column type="index" label="#" />
                <el-table-column prop="groupName" label="组名称" />
                <el-table-column prop="modifiedTime" label="保存日期" />
                <el-table-column prop="name" label="接待人数" width="150">
                    <template slot-scope="scope">
                        <el-input :value="scope.row.receiveCount == -1 ? null : scope.row.receiveCount" type="number"
                            :maxlength="5" @input="changeCount($event, scope.$index)" placeholder="不参与计算" clearable
                            @clear="clearIpt($event, scope.$index)" />
                    </template>
                </el-table-column>
            </el-table>
            <div style="color: red;text-align: center;margin-top: 10px;">小于等于该接待人数的,提成=销售额*前一天提成系数</div>
            <div class="btnbox">
                <el-button @click="handleClose" style="margin-right: 20px;">取消</el-button>
                <el-button type="primary" @click="handleGradeSubmit">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import buschar from '@/components/Bus/buschar'
import dayjs from 'dayjs'
import { pickerOptions } from '@/utils/tools'
import {
    getTaoBaoInquireGradeSetList,
    SaveTaoBaoInquireGradeSet,
    getTaoBaoGroupNameList,
    getTaoBaoInquireGradeWayNameList,
    saveTaoBaoInquireGradeWay,
    getTaoBaoInquireGradeWayByName,
    getTaoBaoInquireGradeComputePageList,
    getTaoBaoInquireGradeComputeChat,
    exportTaoBaoInquireGradeComputeList,
    getTaoXiGroup, getTaoXiSname, getTaoXiSnick
} from '@/api/customerservice/inquirs'
import { replaceSpace } from '@/utils/getCols'
const tableCols = [
    { istrue: true, prop: 'groupName', label: '组别', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', sortable: 'custom' },
    { istrue: true, prop: 'snameLevel', label: '客服等级', sortable: 'custom' },
    // { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom' },
    { istrue: true, prop: 'snick', label: '昵称', sortable: 'custom' },
    { istrue: true, prop: 'receivecount', label: '接待人数', sortable: 'custom' },
    { istrue: true, prop: 'salesvol', label: '销售额', sortable: 'custom' },
    { istrue: true, prop: 'successpayrate', label: '最终付款成功率', width: '150', sortable: 'custom', formatter: (row) => row.successpayrate + '%' },
    { istrue: true, prop: 'gradeCommission', label: '提成', sortable: 'custom' },
    { istrue: true, prop: 'gradeWayValue', label: '提成系数', sortable: 'custom', },
    { istrue: true, prop: 'gradeIndex', label: '排名', sortable: 'custom', formatter: (row) => row.gradeIndex == '0' ? '' : row.gradeIndex },
    { istrue: true, prop: 'effectiveDate', label: '日期', sortable: 'custom', formatter: (row) => dayjs(row.effectiveDate).format('YYYY-MM-DD') },
    {
        istrue: true, prop: 'buchar', type: "button", label: '趋势图', fixed: 'right', summaryEvent: true, btnList: [
            { label: "查看", handle: (that, row) => that.openChat(row) },
        ]
    }
]

const gradeCommissionTableCols = [
    { istrue: true, prop: 'effectiveDate', label: '保存日期', sortable: 'custom' },
    { istrue: true, prop: 'effectiveDate', label: '接待人数', sortable: 'custom' },
    {
        istrue: true, type: "button", width: '100', label: '操作', summaryEvent: true, btnList: [
            { label: "删除", handle: (that, row) => that.deleteProps(row) },
        ]
    }
]
export default {
    name: 'tbNewPerformanceStatement',
    props: {
        filter: {
            type: Object,
            default: () => { }
        }
    },
    components: { MyContainer, vxetablebase, buschar },
    data() {
        return {
            ListInfo: {//列表参数
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                startDate: null,//开始时间
                endDate: null,//结束时间
                shopCode: null,//店铺编码
                shopName: null,//店铺
                groupName: null,//分组
                groupShortName: null,//分组归属
                snick: null,//昵称
                sname: null,//姓名
                snameLevel: null,//客服等级
            },
            commissionList: [],
            commissionDate: [],//生效时间
            pickerOptions,
            historyInfo: {//历史提成系数参数
                orderBy: 'effectiveDate',
                isAsc: false,
            },
            chatProp: {
                chatDialog: false,//趋势图弹窗
                chatTime: null,//趋势图时间
                chatLoading: true,//趋势图loading
                data: [],//趋势图数据
            },
            chatInfo: {
                groupName: null,//组别
                sname: null,//姓名
                shopName: null,//店铺
                snick: null,//昵称
                startDate: null,//开始时间
                endDate: null,//结束时间
            },
            listLoading: true,
            summaryarry: null,
            that: this,
            tableCols,//表格列
            gradeCommissionTableCols,
            timeList: null,//时间
            total: 0,//总条数
            tableData: [],//表格数据
            gradeCommissionData: [],//接待人数阈值数据
            historyTableInfo: {//历史提成系数
                tableData: [],
                total: 0,
            },
            dialogVisible: false,//设置提成系数弹窗
            computeDialog: false,//一键计算弹窗
            historyDialog: false,//历史提成系数弹窗
            gradeCommissionVisible: false,//提成系数提示弹窗
            thresholdInfo: {//设置阈值
                id: null,
                enabled: true,
                createdTime: null,
                total: null,
                index: null,
                batchNumber: null,
                createdUserId: null,
                createdUserName: null,
                groupName: null,
                receiveCount: null
            },
            GroupNameList: [],//组别下拉框
            historyComList: [],//提成系数列表
            groupName: null,//组别
            commission: null,//提成系数
            options: [
                {
                    label: '金牌'
                },
                {
                    label: '银牌'
                }
            ],
            filterGroupList: [],
            filterSnameList: [],
            filterSnickList: [],
        }
    },
    mounted() {
        this.getTaoXiGroup();
        this.getTaoXiSname();
        this.getTaoXiSnick();
        this.getList()
    },
    methods: {
        async getTaoXiGroup() {
            let groups = await getTaoXiGroup({ groupType: 0, isGrade: "已参与计算绩效" });
            console.log(groups, "groups");
            if (groups?.success && groups?.data && groups?.data.length > 0) {
                groups?.data.forEach(f => {
                    this.filterGroupList.push({ lable: f, value: f });
                });
            }
        },
        async onGroupNameChange() {
            await this.getTaoXiSname();
            await this.getTaoXiSnick();
        },
        async getTaoXiSname() {
            this.ListInfo.sname = null;
            this.filterSnameList = [];
            let snames = await getTaoXiSname({ groupType: 0, isGrade: "已参与计算绩效", groupName: this.ListInfo.groupName });
            console.log(snames, "snames");
            if (snames?.success && snames?.data && snames?.data.length > 0) {
                snames?.data.forEach(f => {
                    this.filterSnameList.push({ lable: f, value: f });
                });
            }
        },
        async onSnameChange() {
            await this.getTaoXiSnick();
        },
        async getTaoXiSnick() {
            this.ListInfo.snick = null;
            this.filterSnickList = [];
            let snicks = await getTaoXiSnick({ groupType: 0, isGrade: "已参与计算绩效", groupName: this.ListInfo.groupName, sname: this.ListInfo.sname });
            console.log(snicks, "snicks");
            if (snicks?.success && snicks?.data && snicks?.data.length > 0) {
                snicks?.data.forEach(f => {
                    this.filterSnickList.push({ lable: f, value: f });
                });
            }
        },
        clearIpt(e, i) {
            this.gradeCommissionData[i].receiveCount = null
        },
        changeComTime(e) {
            if (e) {
                this.commissionList.forEach((item, i) => {
                    item.startEffectiveDate = dayjs(e[0]).format('YYYY-MM-DD')
                    item.endEffectiveDate = dayjs(e[1]).format('YYYY-MM-DD')
                    item.groupName = this.groupName
                    item.gradeIndex = i + 1
                })
            } else {
                this.commissionList.forEach(item => {
                    item.effectiveDateName = null
                    item.startEffectiveDate = null
                    item.endEffectiveDate = null
                    item.groupName = null
                    item.gradeIndex = null
                })
            }
        },
        deleteCommission(row, i) {
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.commissionList.splice(i, 1)
                this.publicLoop()
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        async changeGroupName(e) {
            this.commission = null
            this.historyComList = []
            if (e) {
                const { data } = await getTaoBaoInquireGradeWayNameList({ groupName: e })
                this.historyComList = data.map(item => {
                    return {
                        label: dayjs(item.startEffectiveDate).format('YYYY-MM-DD') + '至' + dayjs(item.endEffectiveDate).format('YYYY-MM-DD'),
                        value: item.effectiveDateName
                    }
                })
                console.log(this.historyComList, 'this.historyComList');
            } else {
                this.historyComList = []
                this.commission = null
                this.commissionDate = []
                this.commissionList = []
            }
        },
        async changehistoryCom(e) {
            if (e) {
                const { data } = await getTaoBaoInquireGradeWayByName({ name: e })
                this.commissionList = data.map(item => {
                    return {
                        effectiveDateName: item.effectiveDateName,
                        groupName: item.groupName,
                        startEffectiveDate: dayjs(item.startEffectiveDate).format('YYYY-MM-DD'),
                        endEffectiveDate: dayjs(item.endEffectiveDate).format('YYYY-MM-DD'),
                        gradeIndex: item.gradeIndex,
                        gradeWayValue1: item.gradeWayValue1,
                        gradeWayValue2: item.gradeWayValue2
                    }
                })
                //根据e找出对应的时间
                this.commissionDate = [this.commissionList[0].startEffectiveDate, this.commissionList[0].endEffectiveDate]
            } else {
                this.commissionDate = []
                this.commissionList = []
            }
        },
        async openDialogVisible() {
            this.clear()
            const { data } = await getTaoBaoGroupNameList()
            this.GroupNameList = data
            this.dialogVisible = true
        },
        changeCount(e, i) {
            //不准输入小数
            if (e.toString().indexOf('.') != -1) {
                e = e.toString().substring(0, e.toString().indexOf('.'))
            }
            //e超过五位数就截断
            if (e.toString().length > 5) {
                e = e.toString().substring(0, 5)
            }
            this.gradeCommissionData[i].receiveCount = Number(e)
            if (e == '' || e == null) {
                this.gradeCommissionData[i].receiveCount = null
            }
        },
        //设置接待人数阈值
        async handleGradeSubmit() {
            this.gradeCommissionData.forEach(item => {
                if (item.receiveCount == null || item.receiveCount == '' || item.receiveCount == undefined || item.receiveCount <= 0) {
                    item.receiveCount = -1
                }
            })
            const { success } = await SaveTaoBaoInquireGradeSet(this.gradeCommissionData)
            if (success) {
                this.$message.success('保存成功')
                this.gradeCommissionVisible = false
            }
        },
        async openGradeDlg() {
            const { data, success } = await getTaoBaoInquireGradeSetList()
            if (success) {
                this.gradeCommissionData = data
                this.gradeCommissionVisible = true
            }
        },
        async exportProps() {
            const { data } = await exportTaoBaoInquireGradeComputeList(this.ListInfo)
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '淘系绩效结果' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        //趋势图时间改变
        async chatSearch() {
            this.chatInfo.startDate = dayjs(this.chatProp.chatTime[0]).format('YYYY-MM-DD')
            this.chatInfo.endDate = dayjs(this.chatProp.chatTime[1]).format('YYYY-MM-DD')
            this.chatProp.chatLoading = true
            const data = await getTaoBaoInquireGradeComputeChat(this.chatInfo)
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        //汇总趋势图
        async onsummaryClick(property) {
            this.chatInfo.groupName = null
            this.chatInfo.sname = null
            this.chatInfo.shopName = null
            this.chatInfo.snick = null
            this.chatProp.chatLoading = true
            this.publicChangeTime(this.chatInfo)
            if (property == 'buchar') {
                const data = await getTaoBaoInquireGradeComputeChat(this.chatInfo)
                this.chatProp.data = data
                this.chatProp.chatDialog = true
                this.chatProp.chatLoading = false
            }
        },
        publicChangeTime(row) {
            if (this.timeList) {
                let time = dayjs(this.timeList[1]).diff(dayjs(this.timeList[0]), 'day')
                if (time >= 30) {
                    this.chatProp.chatTime = this.timeList
                    this.chatInfo.startDate = dayjs(this.timeList[0]).format('YYYY-MM-DD')
                    this.chatInfo.endDate = dayjs(this.timeList[1]).format('YYYY-MM-DD')
                } else {
                    //否则就将时间间隔设置为三十天
                    this.chatProp.chatTime = [dayjs(this.timeList[1]).subtract(30, 'day').format('YYYY-MM-DD'), this.timeList[1]]
                    this.chatInfo.startDate = dayjs(this.timeList[1]).subtract(30, 'day').format('YYYY-MM-DD')
                    this.chatInfo.endDate = dayjs(this.timeList[1]).format('YYYY-MM-DD')
                }
            }
            this.chatInfo.groupName = row.groupName
            this.chatInfo.sname = row.sname
            this.chatInfo.shopName = row.shopName
            this.chatInfo.snick = row.snick
        },
        async openChat(row) {
            //如果this.timeList的时间间隔大于等于三十天,那么就提示时间间隔不能大于三十天
            this.publicChangeTime(row)
            this.chatProp.chatLoading = true
            const data = await getTaoBaoInquireGradeComputeChat(this.chatInfo)
            this.chatProp.data = data
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = false
        },
        //查看历史提成系数
        async viewHistory() {
            this.getVersion()
            this.historyDialog = true
        },

        //保存提成系数
        async handleSubmit() {
            if (this.groupName == null) {
                return this.$message.error('请选择组名')
            }
            if (this.commissionDate == null || this.commissionDate.length == 0) return this.$message.error('请选择生效时间')
            this.publicLoop()
            if (this.commissionList.length > 0) {
                this.commissionList.forEach(item => {
                    if (item.gradeWayValue1 == null || item.gradeWayValue1 == '' || item.gradeWayValue1 == undefined || item.gradeWayValue1 == 0) {
                        this.$message.error('金牌提成系数不能为0')
                        throw ('')
                    }
                    if (item.gradeWayValue2 == null || item.gradeWayValue2 == '' || item.gradeWayValue2 == undefined || item.gradeWayValue2 == 0) {
                        this.$message.error('银牌提成系数不能为0')
                        throw ('')
                    }
                })
            }
            console.log(this.commissionList, 'this.commissionList');
            const { success } = await saveTaoBaoInquireGradeWay(this.commissionList)
            if (success) {
                this.$message.success('保存成功')
                this.clear()
                this.dialogVisible = false
            }
        },
        changeTime(e) {
            if (!e) {
                this.ListInfo.startDate = null
                this.ListInfo.endDate = null
            } else {
                this.ListInfo.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs(e[1]).format('YYYY-MM-DD')
            }
            this.getList()
        },
        deleteProps(row, i) {
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.commissionList.splice(i, 1)
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        addTable() {
            if (this.commissionDate == null || this.commissionDate.length == 0) return this.$message.error('请选择生效时间')
            if (this.groupName == null) return this.$message.error('请选择组名')
            this.commissionList.push({
                groupName: this.groupName,
                startEffectiveDate: dayjs(this.commissionDate[0]).format('YYYY-MM-DD'),
                endEffectiveDate: dayjs(this.commissionDate[1]).format('YYYY-MM-DD'),
                gradeIndex: this.commissionList.length + 1,
                gradeWayValue1: null,
                gradeWayValue2: null
            })
            this.publicLoop()
            console.log(this.commissionList, 'this.commissionList');
        },
        publicLoop() {
            if (this.commissionDate != null && this.commissionDate.length > 0) {
                this.commissionList.forEach((item, i) => {
                    item.startEffectiveDate = dayjs(this.commissionDate[0]).format('YYYY-MM-DD')
                    item.endEffectiveDate = dayjs(this.commissionDate[1]).format('YYYY-MM-DD')
                    item.groupName = this.groupName
                    item.gradeIndex = i + 1
                })
            }
        },

        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        //每页数量改变
        historySizechange(val) {
            this.historyInfo.currentPage = 1;
            this.historyInfo.pageSize = val;
            this.viewHistory()
        },
        //当前页改变
        historyPagechange(val) {
            this.historyInfo.currentPage = val;
            this.viewHistory()
        },
        //查询列表
        async getList(type) {
            this.listLoading = true
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.ListInfo.pageSize = 50
                this.ListInfo.isAsc = false
            }
            const arr = ['shopName', 'snick', 'sname', 'groupName']
            replaceSpace(arr, this.ListInfo)
            if (!this.timeList) {
                //默认时间为当前时间往前推一个月
                this.ListInfo.startDate = dayjs().subtract(1, 'month').format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
                this.timeList = [this.ListInfo.startDate, this.ListInfo.endDate]
            }
            const { data, success } = await getTaoBaoInquireGradeComputePageList(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
                this.listLoading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        handleClose() {
            this.clear()
            this.dialogVisible = false;
            this.gradeCommissionVisible = false
        },
        //清空
        clear(isAdd) {
            if (!isAdd) {
                this.groupName = null
                this.historyComList = []
            } else {
                if (this.groupName == null) {
                    this.$message.error('请选择组名')
                    return
                }
            }
            this.commissionDate = null
            this.commission = null
            this.commissionList = []
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        sortchange1({ order, prop }) {
            if (prop) {
                this.historyInfo.orderBy = prop
                this.historyInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getVersion()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
}

.publicCss {
    width: 220px;
    margin-right: 10px;
}

.topbox {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    justify-content: space-between;
}

.btnbox {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

.history {
    cursor: pointer;
    font-size: 16px;
    margin-right: 10px;
    line-height: 16px;
    color: #409EFF;
}

.footer {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    justify-content: end;
}

.historyBox {
    width: 90%;
    height: 500px;
    overflow: auto;

    .parBox {
        margin-bottom: 20px;
    }

    .childBox {
        width: 90%;
        display: flex;
        justify-content: space-between;
        margin: 10px;

        .child_item {
            width: 45%;
        }
    }
}
</style>