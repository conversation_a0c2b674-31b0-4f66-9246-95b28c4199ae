<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss"
                    startPlaceholder="创建时间" endPlaceholder="创建时间" />
                <dateRange :startDate.sync="ListInfo.payStartTime" :endDate.sync="ListInfo.payEndTime" class="publicCss"
                    startPlaceholder="付款时间" endPlaceholder="付款时间" />
                <el-input v-model.trim="ListInfo.buyUserId" placeholder="买家ID" maxlength="200" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.orderNo" placeholder="订单号" maxlength="200" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.internalOrderNo" placeholder="内部订单号" maxlength="200" clearable
                    class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                <!-- <el-button type="primary" @click="importProps" v-if="checkPermission('tmzdImport')">导入</el-button> -->
                <el-button type="primary" @click="openProfit3Log">毛三预算</el-button>
            </div>
        </template>
        <vxetablebase :id="'tmzdQuoteDetails202408041632'" ref="table" :tableData="tableData" :tableCols="tableCols"
            :is-index="true" :that="that" style="width: 100%;  margin: 0" @sortchange='sortchange' :height="'100%'"
            :showsummary="true" class="detail" :summaryarry="summaryarry"
            :treeProp="{ rowField: 'id', parentField: 'pId', transform: true, }" v-loading="loading">
            <!-- isCustomization = 1:定制款  2：常规款（不显示编辑按钮） -->
            <template slot="right">
                <vxe-column title="操作" width="80" align="center" fixed="right">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <el-button type="text"
                                v-if="row.pId == '0' && checkPermission('api:inventory:customNormsGoods:ReSetTMZDWeight')"
                                @click="resetWeight(row.internalOrderNo, row.orderWeight)">重置重量</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="毛三预算" :visible.sync="drawer1" width="80%" :close-on-click-modal="false" v-dialogDrag>
            <el-button type="text" @click="addProps">新增一行</el-button>
            <el-table :data="Profit3Data" style="width: 95%;height:95%" max-height="400">
                <el-table-column label="#" type="index" width="40" />
                <el-table-column prop="norms" label="规格" width="75">
                    <template #default="{ row, $index }">
                        <el-select v-model="row.norms" placeholder="规格" @change="changeNorms($index)">
                            <el-option v-for="item in ggList" :key="item.value" :label="item.label"
                                :value="item.label" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="actualLand" label="实际厚度" width="75">
                    <template #default="{ row, $index }">
                        <el-input-number v-model="row.actualLand" :max="10000" :precision="3" :controls="false"
                            label="实际厚度" class="iptCss" :min="0" @change="changeNorms($index)" disabled />
                    </template>
                </el-table-column>
                <el-table-column prop="length" label="长" width="65">
                    <template #default="{ row, $index }">
                        <el-input-number v-model="row.length" :max="10000" :precision="4" :controls="false" label="长"
                            class="iptCss" :min="0" @change="changeNorms($index)" />
                    </template>
                </el-table-column>
                <el-table-column prop="width" label="宽" width="65">
                    <template #default="{ row, $index }">
                        <el-input-number v-model="row.width" :max="10000" :precision="4" :controls="false" label="宽"
                            class="iptCss" :min="0" @change="changeNorms($index)" />
                    </template>
                </el-table-column>
                <el-table-column prop="sheet" label="张数" width="65">
                    <template #default="{ row, $index }">
                        <el-input-number v-model="row.sheet" :max="99999999" :precision="0" :controls="false" label="张数"
                            class="iptCss" :min="0" @change="changeNorms($index)" />
                    </template>
                </el-table-column>
                <el-table-column prop="pf" label="平方" width="65">
                    <template #default="{ row, $index }">
                        <el-input-number v-model="row.pf" :max="99999999" :precision="4" :controls="false" label="平方"
                            class="iptCss" :min="0" @change="changezl($index)" disabled />
                    </template>
                </el-table-column>
                <el-table-column prop="proportion" label="比重/kg" width="75">
                    <template #default="{ row, $index }">
                        <el-input-number v-model="row.proportion" :max="99999999" :precision="4" :controls="false"
                            label="比重/kg" style="width: 65px;" :min="0" @change="changezl($index)" disabled />
                    </template>
                </el-table-column>
                <el-table-column prop="zl" label="重量" width="75">
                    <template #default="{ row, $index }">
                        <el-input-number v-model="row.zl" :max="99999999" :precision="4" :controls="false"
                            label="产品成本(重量)" class="iptCss" :min="0" @change="changecbcb($index, 'zl')" disabled />
                    </template>
                </el-table-column>
                <el-table-column prop="kgPriceCost" label="公斤价格" width="75">
                    <template #default="{ row, $index }">
                        <el-input-number v-model="row.kgPriceCost" :max="99999999" :precision="4" :controls="false"
                            label="公斤价格" style="width: 65px;" :min="0" @change="changecbcb($index)" disabled />
                    </template>
                </el-table-column>
                <el-table-column prop="kgPriceCost" label="产品成本(重量)" width="75">
                    <template #default="{ row, $index }">
                        <el-input-number v-model="row.cpcb" :max="99999999" :precision="4" :controls="false"
                            label="公斤价格" style="width: 65px;" :min="0" @change="changeccjg($index)" disabled />
                    </template>
                </el-table-column>
                <el-table-column prop="croppingCost" label="裁剪费(张)" width="75">
                    <template #default="{ row, $index }">
                        <el-input-number v-model="row.croppingCostTotal" :max="99999999" :precision="4"
                            :controls="false" label="裁剪费(张)" class="iptCss" :min="0" @change="changeccjg($index)"
                            disabled />
                    </template>
                </el-table-column>
                <el-table-column prop="packProcessCost" label="打包费(单)" width="75">
                    <template #default="{ row, $index }">
                        <el-input-number v-model="row.packProcessCost" :max="99999999" :precision="4" :controls="false"
                            label="打包费(单)" class="iptCss" :min="0" @change="changeccjg($index)" disabled />
                    </template>
                </el-table-column>
                <el-table-column prop="sjccjg" label="实际出仓价格" width="75">
                    <template #default="{ row }">
                        <el-input-number v-model="row.sjccjg" :max="99999999" :precision="4" :controls="false"
                            label="实际出仓价格" class="iptCss" :min="0" disabled />
                    </template>
                </el-table-column>
                <el-table-column prop="kdf" label="快递费" width="70">
                    <template #default="{ row }">
                        <el-input-number v-model="row.kdf" :max="99999999" :precision="4" :controls="false" label="快递费"
                            class="iptCss" :min="0" disabled />
                    </template>
                </el-table-column>
                <el-table-column prop="bhkdcb" label="包含快递成本" width="75">
                    <template #default="{ row }">
                        <el-input-number v-model="row.bhkdcb" :max="99999999" :precision="4" :controls="false"
                            label="包含快递成本" class="iptCss" :min="0" disabled />
                    </template>
                </el-table-column>
                <el-table-column prop="sj" label="售价" width="65">
                    <template #default="{ row }">
                        <el-input-number v-model="row.sj" :max="99999999" :precision="2" :controls="false" label="售价"
                            class="iptCss" :min="0" disabled />
                    </template>
                </el-table-column>
                <el-table-column prop="kd" label="扣点" width="65">
                    <template #default="{ row }">
                        <el-input-number v-model="row.kd" :max="99999999" :precision="4" :controls="false" label="扣点"
                            class="iptCss" :min="0" disabled />
                    </template>
                </el-table-column>
                <el-table-column prop="qtfy" label="其他费用" width="75">
                    <template #default="{ row }">
                        <el-input-number v-model="row.qtfy" :max="99999999" :precision="4" :controls="false"
                            label="其他费用" class="iptCss" :min="0" disabled />
                    </template>
                </el-table-column>
                <el-table-column prop="mslr" label="毛三利润" width="75">
                    <template #default="{ row }">
                        <el-input-number v-model="row.mslr" :max="99999999" :precision="4" :controls="false"
                            label="毛三利润" class="iptCss" :min="0" disabled />
                    </template>
                </el-table-column>
                <el-table-column prop="msll" label="毛三利率" width="75">
                    <template #default="{ row }">
                        {{ row.msll + '%' }}
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog title="日志" :visible.sync="viewLogsVisible" width="30%" v-dialogDrag>
            <viewLogsVue :parentId="viewLogsInfo.parentId" tableName="TMZDNormsCSRecordDtl" v-if="viewLogsVisible" />
        </el-dialog>

        <!-- 新增 -->
        <el-drawer title="编辑" :visible.sync="drawer" direction="rtl" size="90%" :wrapperClosable="false">
            <el-form :model="ruleForm" status-icon ref="ruleForm" label-width="100px" class="demo-ruleForm"
                style="padding: 16px;box-sizing: border-box;" :rules="rules">
                <el-form-item label="买家id:" prop="pass">
                    <div>{{ ruleForm.buyUserId }}</div>
                </el-form-item>
                <el-form-item label="聊天记录:" prop="chatPicUrl">
                    <div class="chatPicUrl">
                        <uploadimgFile v-if="drawer" ref="uploadimgFile" :ispaste="false" :noDel="false"
                            :accepttyes="accepttyes" :isImage="true" :uploadInfo="chatUrls" :keys="[1, 1]"
                            @callback="getImg" :imgmaxsize="10" :limit="10" :multiple="true">
                        </uploadimgFile>
                        <span class="picTips">提示:点击灰框可直接贴图！！！</span>
                    </div>
                </el-form-item>
                <el-form-item label="规格报价明细:" prop="pass">
                    <span style="color: red;">(禁止截图给客户！！！)</span>
                    <el-button style="margin-left: 20px;" @click="addProps1" type="primary">新增一行</el-button>
                </el-form-item>
                <div style="height: 400px;">
                    <el-table :data="ruleForm.dtls" style="width: 100%;height:100%" max-height="450">
                        <el-table-column prop="norms" label="规格" width="75">
                            <template #header="{ column }">
                                <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>规格
                            </template>
                            <template #default="{ row, $index }">
                                <el-select v-model="row.norms" placeholder="规格" @change="changeNorms1($event, $index)">
                                    <el-option v-for="item in ggList" :key="item.value" :label="item.label"
                                        :value="item.label" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="isDK" label="类型" width="70">
                            <template #default="{ row, $index }">
                                <el-select v-model="row.type" placeholder="类型" @change="Cpt">
                                    <el-option label="磨砂" value="磨砂" v-show="row.norms != 1" />
                                    <el-option label="透明" value="透明" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="sheetLength" label="长(米)" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.sheetLength" :min="0" :max="10000" :precision="4"
                                    :controls="false" label="长(米)" class="iptCss" @change="Cpt" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="sheetWidth" label="宽(米)" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.sheetWidth" :min="0" :max="10000" :precision="4"
                                    :controls="false" label="宽(米)" class="iptCss" @change="Cpt" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="sheetCount" label="张" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.sheetCount" :min="0" :max="10000" :precision="0"
                                    :controls="false" label="张" class="iptCss" @change="Cpt" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="internalTotalAmount" label="售价" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.internalTotalAmount" :min="0" :precision="2"
                                    :controls="false" label="售价" class="iptCss" disabled />
                            </template>
                        </el-table-column>
                        <el-table-column prop="remark" label="备注" width="auto">
                            <template #default="{ row }">
                                <el-input v-model="row.remark" maxlength="50" clearable placeholder="备注" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="orderFreightBagFee" label="操作" width="70">
                            <template #default="{ row, $index }">
                                <el-button type="danger" @click="delRuleFormDtls($index)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <el-form-item label="合计:" prop="pass">
                    <div>{{ ruleForm.internalTotalAmount }}</div>
                </el-form-item>
                <el-form-item label="实际报价:" prop="pass">
                    <el-input-number v-model="ruleForm.actualTotalAmount" :min="0" :max="9999999" :precision="2"
                        :controls="false" label="实际报价" class="iptCss" />
                </el-form-item>
                <div style="display: flex;justify-content: end;margin-top: 100px;">
                    <el-button type="primary" @click="submitForm(true)">保存</el-button>
                    <el-button type="primary" @click="copyProps('bj')" :disabled="false">复制报价</el-button>
                    <el-button type="primary" @click="copyProps('bz')" :disabled="false">复制加工备注</el-button>
                </div>
            </el-form>
        </el-drawer>

        <el-dialog title="重置重量" :visible.sync="resetWeightVisible" width="15%" v-dialogDrag>
            <div style="display: flex;justify-content: center;align-items: center">
                <el-input-number v-model="resetWeightInfo.weight" :min="0" :max="9999999999" placeholder="重量(kg)"
                    :precision="2" :controls="false" />
            </div>
            <div class="btnGroup1">
                <el-button @click="resetWeightVisible = false">取消</el-button>
                <el-button type="primary" @click="resetWeightSumbit">确定</el-button>
            </div>
        </el-dialog>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import {
    getTMZDSaleOrderRecordList,
    getCostSetByTMZD,
    getTMZDCSRecordById,
    updateTMZDRecordDtlAsync,
    getCostSetByTMZDCache,
    exportTMZDSaleOrderRecordList,
    importCustomMadeMultipleAsync,
    reSetTMZDWeight
} from '@/api/inventory/customNormsGoods'
import * as math from 'mathjs'
import viewLogsVue from './viewLogs.vue'
import dateRange from "@/components/date-range/index.vue";
const tableCols = [
    { sortable: 'custom', width: '80', align: 'left', prop: 'expressCompany', label: '快递公司', treeNode: true, },
    { sortable: 'custom', width: '120', align: 'left', prop: 'internalOrderNo', label: '内部订单号', type: 'orderLogInfo', orderType: 'orderNoInner', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'actualOrderFreightBagFee', label: '快递费', },
    { sortable: 'custom', width: '70', align: 'left', prop: 'sheng', label: '省份', },
    { sortable: 'custom', width: '70', align: 'left', prop: 'shi', label: '城市', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'orderNo', label: '订单编号' },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'buyUserId', label: '买家ID', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'createdTime', label: '创建时间', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'payTime', label: '付款时间', },
    // { sortable: 'custom', width: 'auto', align: 'left', prop: 'createdUserName', label: '创建人', },
    // { width: 'auto', align: 'left', prop: 'pics', label: '聊天记录', type: 'images' },
    {
        sortable: 'custom', width: '80', align: 'center', prop: 'analysisError', label: '状态', formatter: (row) => {
            if (row.pId == 0) {
                return row.analysisError == false ? '正常' : '异常'
            } else {
                return ''
            }
        }
    },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'norms', label: '规格', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'type', label: '类型', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'sheetLength', label: '长', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'sheetWidth', label: '宽', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'totalSheetCount', label: '总张数', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'weight', label: '预估重量', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'orderWeight', label: '实际重量', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'totalCost', label: '预估成本', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'realTotalCost', label: '实际成本', },
    // { sortable: 'custom', width: 'auto', align: 'left', prop: 'orderFreightBagFee', label: '预估快递费', },
    // { sortable: 'custom', width: 'auto', align: 'left', prop: 'actualOrderFreightBagFee', label: '实际快递费', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'actualTotalAmount', label: '实际报价', },
    { width: 'auto', align: 'left', prop: 'orderRemark', label: '订单备注', },
    { sortable: 'custom', width: '90', align: 'center', prop: 'updatedUserName', label: '修改人', },
    // {
    //     width: '90', align: 'center', label: '操作', type: 'button', btnList: [
    //         { label: "编辑", handle: (that, row) => that.editProps(row.id) },
    //         { label: "日志", handle: (that, row) => that.viewLogs(row) }
    //     ]
    // },
]

const viewsLogCols = [
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'createdUserName', label: '操作人', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'createdTime', label: '时间', formatter: (row) => row.createdTime ? dayjs(row.createdTime).format('YYYY-MM-DD HH:mm:ss') : '' },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'content', label: '操作内容', },
]
const ggList = [
    {
        label: '1.0',
        value: 0.4
    },
    {
        label: '1.5',
        value: 0.7
    },
    {
        label: '2.0',
        value: 1.1
    },
    {
        label: '3.0',
        value: 1.4
    },
    {
        label: '5.0',
        value: 2.2
    },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, uploadimgFile, viewLogsVue, dateRange
    },
    data() {
        return {
            resetWeightVisible: false,
            resetWeightInfo: {
                weight: 0,
                internalOrderNo: ''
            },
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'createdTime',
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
                buyUserId: null,//买家ID
                orderNo: null,//订单号
                createdUserName: null,//创建人
                payStartTime: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),//付款开始时间
                payEndTime: dayjs().format('YYYY-MM-DD'),//付款结束时间
                analysisError: null
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            summaryarry: {},
            Profit3Data: [
                {
                    norms: null,
                    actualLand: null,
                    length: null,
                    width: null,
                    sheet: null,
                    pf: null,
                    proportion: null,
                    kgPriceCost: null,
                    zl: null,
                    croppingCost: null,
                    dbf: null,
                    cpcb: null,
                    packProcessCost: null,
                    sjccjg: null,
                    kdf: null,
                    bhkdcb: null,
                    sj: null,
                    kd: null,
                    qtfy: null,
                    mslr: null,
                    msll: 0,
                }
            ],
            list: [
                {
                    norms: '1',
                    proportion: 2,
                    kgPriceCost: 3,
                    croppingCost: 1,
                    packProcessCost: 4,
                }
            ],
            ggList,
            drawer1: false,
            isExport: false,
            importVisible: false,
            fileList: [],
            file: null,
            importLoading: false,
            viewLogsVisible: false,
            viewsLogCols,
            viewLogsData: [],
            viewLogsInfo: {
                parentId: null,
                tableName: 'TMZDNormsCSRecordDtl'
            },
            ruleForm: {
                buyUserId: null,//买家id
                orderNo: null,//订单号
                internalTotalAmount: null,//总价
                actualTotalAmount: null,//实际总价
                chatPicUrl: null,//聊天记录
                orderWeight: 0,//快递重量
                totalSheetCount: 0,//总张数
                weight: 0,//重量
                cost: 0,//成本(不含快递+打包)
                totalCost: 0,//成本(含快递+打包)
                dtls: [
                    {
                        norms: null,//规格
                        type: null,//类型
                        sheetWidth: null,//单张宽
                        sheetLength: null,//单张长
                        sheetCount: null,//张数
                        internalTotalAmount: null,//售价
                        remark: null,//备注
                    }
                ]
            },
            chatUrls: [],
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            rules: {
                chatPicUrl: [
                    { required: true, message: '请上传聊天记录', trigger: 'change' }
                ]
            },
            chatPicUrl: [],
            drawer: false,
            catchList: [],
            paytimeRanges: []
        }
    },
    async mounted() {
        await this.getSetList()
        await this.getList()
    },
    methods: {
        async resetWeightSumbit() {
            if (this.resetWeightInfo.weight <= 0) return this.$message.error('重量不能小于等于0')
            const { success } = await reSetTMZDWeight(this.resetWeightInfo)
            if (success) {
                this.$message.success('重置成功')
                this.getList()
                this.resetWeightVisible = false
            } else {
                this.$message.error('重置失败')
            }
        },
        resetWeight(internalOrderNo, weight) {
            this.resetWeightInfo = {
                weight,
                internalOrderNo
            }
            this.resetWeightVisible = true
        },
        delRuleFormDtls(i) {
            this.ruleForm.dtls.splice(i, 1)
            this.Cpt()
        },
        async viewLogs(id) {
            this.viewLogsInfo.parentId = id
            this.viewLogsVisible = true
        },
        async submitForm(isOrder) {
            if (this.ruleForm.dtls.length == 0) return this.$message.error('至少添加一条数据')
            // this.ruleForm.buyUserId = this.buyUserInfo.buyUserId
            for (const key in this.ruleForm) {
                if (key != 'orderNo' && key != 'actualTotalAmount') {
                    //判断是否有空值
                    if (key == 'chatPicUrl') {
                        if (this.ruleForm[key] === null || this.ruleForm[key] === undefined || this.ruleForm[key] === '') {
                            this.$message.error('请上传聊天记录')
                            throw new Error('请上传聊天记录')
                        }
                    }
                }
            }
            this.validate()
            // if (isOrder && !this.ruleForm.orderNo) return this.$message.error('请输入订单号')
            // this.ruleForm.orderNo = isOrder ? this.ruleForm.orderNo : null
            const { data, success } = await updateTMZDRecordDtlAsync(this.ruleForm)
            if (success) {
                await this.getList()
                this.$message.success('提交成功')
                this.drawer = false
                // this.orderVisable = false
            } else {
                this.$message.error('提交失败')
            }
        },
        copyProps(type) {
            this.validate()
            let textarea = document.createElement("textarea")
            let e = ''
            let totalSheetCount = this.ruleForm.dtls.reduce((total, item) => total + item.sheetCount, 0);
            if (type == 'bj') {
                this.ruleForm.dtls.forEach(item => {
                    e += `${item.type}${item.norms}定制#${item.goodsCode ? item.goodsCode : ''}@${item.sheetLength}*${item.sheetWidth}*${item.sheetCount}*张#,总共${item.sheetCount}张,售价:${item.internalTotalAmount}\n`
                })
                e = e + `共${totalSheetCount}张,合计:${this.ruleForm.internalTotalAmount}元,优惠后总价:${this.ruleForm.actualTotalAmount}元\n`
            } else {
                this.ruleForm.dtls.forEach(item => {
                    e += `${item.type}${item.norms}定制#${item.goodsCode ? item.goodsCode : ''}@${item.sheetLength}*${item.sheetWidth}*${item.sheetCount}*张#,总共${item.sheetCount}张,售价:${item.internalTotalAmount},备注:${item.remark ? item.remark : ''}\n`
                })
                e = e + `共${totalSheetCount}张\n`
            }
            textarea.value = e
            textarea.readOnly = "readOnly"
            document.body.appendChild(textarea)
            textarea.select()
            let result = document.execCommand("copy")
            if (result) {
                this.$message({
                    message: '复制成功',
                    type: 'success'
                })
            }
            textarea.remove()
        },
        validate() {
            if (this.ruleForm.dtls.length == 0) return this.$message.error('至少添加一条数据')
            this.ruleForm.dtls.forEach((item, i) => {
                const arr = ['norms', 'type', 'sheetWidth', 'sheetLength', 'sheetCount', 'internalTotalAmount']
                for (const key in item) {
                    if (arr.includes(key)) {
                        if (item[key] === null || item[key] === undefined || item[key] === '') {
                            this.$message.error(`第${i + 1}行${this.ys[key]}不能为空`)
                            throw new Error(`第${i + 1}行${this.ys[key]}不能为空`)
                        }
                    }
                }
            })
            if (this.ruleForm.actualTotalAmount <= 0 || this.ruleForm.actualTotalAmount === null || this.ruleForm.actualTotalAmount === undefined) {
                this.$message.error('实际报价不能为空或小于等于0')
                throw new Error('实际报价不能为空或小于等于0')
            }
        },
        addProps1() {
            this.ruleForm.dtls.push({
                norms: null,//规格
                type: null,//类型
                sheetWidth: null,//单张宽
                sheetLength: null,//单张长
                sheetCount: null,//张数
                internalTotalAmount: null,//售价
                remark: null,//备注
            })
        },
        getImg(data) {
            if (data) {
                this.chatUrls = data
                this.ruleForm.chatPicUrl = data.map(item => item.url).join(',')
            }
        },
        //获取缓存
        async getCatchList() {
            const { data, success } = await getCostSetByTMZDCache()
            if (success) {
                this.catchList = data
            }
        },
        changeNorms1(e, i) {
            this.ruleForm.dtls[i].actualLand = this.ggList.find(item => item.label == e).value
            this.ruleForm.dtls[i].type = e ? null : this.ruleForm.dtls[i].type
            this.Cpt()
        },
        Cpt() {
            let res = []
            this.ruleForm.dtls.forEach((item, i) => {
                res = this.catchList.filter(item => item.norms == this.ruleForm.dtls[i].norms && item.type == this.ruleForm.dtls[i].type)
                if (res && res.length > 0) {
                    this.ruleForm.dtls[i].packProcessCost = res[0].packProcessCost //打包费
                    this.ruleForm.dtls[i].proportion = res[0].proportion //比重
                    this.ruleForm.dtls[i].kgPriceCost = res[0].kgPriceCost //公斤价格
                    this.ruleForm.dtls[i].croppingCost = res[0].croppingCost //裁切费
                    this.ruleForm.dtls[i].actualLand = res[0].actualLand //实际厚度
                    this.ruleForm.dtls[i].goodsCode = res[0].goodsCode//商品编码
                    let pf = this.getNumber(Number(this.ruleForm.dtls[i].sheetLength * this.ruleForm.dtls[i].sheetWidth * this.ruleForm.dtls[i].sheetCount), 10000) //平方单行
                    let weight = this.getNumber(this.getNumber(Number((pf * this.ruleForm.dtls[i].actualLand * this.ruleForm.dtls[i].proportion)), 10000), 10000)//单行重量单行
                    let croppingCost = this.getNumber(this.getNumber(Number(this.ruleForm.dtls[i].croppingCost * this.ruleForm.dtls[i].sheetCount), 10000), 10000)//裁剪费单行
                    let cost = this.getNumber(weight * this.ruleForm.dtls[i].kgPriceCost + croppingCost, 10000)//单行成本
                    this.ruleForm.dtls[i].pf = pf //平方
                    this.ruleForm.dtls[i].weight = weight
                    this.ruleForm.dtls[i].croppingCost = croppingCost
                    this.ruleForm.dtls[i].cost = cost
                } else {
                    this.ruleForm.dtls[i].packProcessCost = 0 //打包费
                    this.ruleForm.dtls[i].proportion = 0 //比重
                    this.ruleForm.dtls[i].kgPriceCost = 0 //公斤价格
                    this.ruleForm.dtls[i].croppingCost = 0 //裁切费
                    this.ruleForm.dtls[i].actualLand = 0 //实际厚度
                    this.ruleForm.dtls[i].goodsCode = ''//商品编码
                    this.ruleForm.dtls[i].pf = 0 //平方
                    this.ruleForm.dtls[i].weight = 0
                    this.ruleForm.dtls[i].croppingCost = 0
                    this.ruleForm.dtls[i].cost = 0
                }
            })
            if (res.length == 0) return
            const kdfArr = [2.4, 2.7, 3.4, 5.4, 6.3, 7.2, 8.1]
            let totalWeight = this.ruleForm.dtls.reduce((total, item) => total + item.weight, 0) || 0
            let costTotal = this.ruleForm.dtls.reduce((total, item) => total + item.cost, 0) || 0
            const packProcessCost = this.getNumber(this.ruleForm.dtls.reduce((total, item) => total > (item.packProcessCost ? item.packProcessCost : 0) ? total : item.packProcessCost, 0), 10000)//打包费 = 明细中最大的打包费
            const kdf = this.getNumber(kdfArr.find((item, index) => totalWeight <= index + 1) || 8.1, 10000) // 快递费 = 总重量  对应的  快递费
            //计算分摊
            this.ruleForm.dtls.forEach((item, i) => {
                let kdfft = 0
                if (totalWeight != 0) {
                    //计算快递费分摊
                    kdfft = this.getNumber(Number((Number(kdf) * (Number(item.weight) / Number(totalWeight)))), 10000) || 0 //快递费分摊
                }
                this.ruleForm.dtls[i].kdfft = kdfft
                //计算打包费分摊
                let packProcessCostFt = 0
                if (costTotal != 0) {
                    packProcessCostFt = this.getNumber(Number(Number(packProcessCost * (Number(item.cost) / Number(costTotal)))), 10000) || 0//打包费分摊
                }
                this.ruleForm.dtls[i].packProcessCostFt = packProcessCostFt
            })

            //计算补差
            const kdfTotal = this.getNumber(this.ruleForm.dtls.reduce((total, item) => total + item.kdfft, 0), 10000)//总快递费分摊
            if (kdfTotal != kdf) {
                const maxKdf = this.ruleForm.dtls.reduce((total, item) => total > item.kdfft ? total : item.kdfft, 0)//最大的快递费分摊
                const maxIndex = this.ruleForm.dtls.findIndex(item => item.kdfft == maxKdf)
                if (maxIndex >= 0) {
                    let a = this.getNumber(((kdf * 10000 - kdfTotal * 10000) / 10000), 10000)
                    let b = this.getNumber(math.add(this.ruleForm.dtls[maxIndex].kdfft, a), 10000)
                    this.ruleForm.dtls[maxIndex].kdfft = b
                }
            }
            //根据打包费和总分摊打包费分相比较,如果不相等,则最大的行上补差
            const packProcessCostTotal = this.getNumber(this.ruleForm.dtls.reduce((total, item) => total + item.packProcessCostFt, 0), 10000) //总打包费分摊
            if (packProcessCostTotal != packProcessCost) {
                const maxPackProcessCost = this.ruleForm.dtls.reduce((total, item) => total > item.packProcessCostFt ? total : item.packProcessCostFt, 0)//最大的打包费分摊
                const maxIndex = this.ruleForm.dtls.findIndex(item => item.packProcessCostFt == maxPackProcessCost)
                if (maxIndex >= 0) {
                    let a = this.getNumber(((packProcessCost * 10000) - (packProcessCostTotal * 10000)) / 10000, 10000)
                    let b = this.getNumber(math.add(this.ruleForm.dtls[maxIndex].packProcessCostFt, a), 10000)
                    this.ruleForm.dtls[maxIndex].packProcessCostFt = b
                }
            }
            //计算含快递费成本 以及售价
            this.ruleForm.dtls.forEach(item => {
                if (item.sheetLength && item.sheetWidth && item.sheetCount) {
                    // const totalCost = this.getNumber(Number((Number(item.cost) + Number(item.kdfft) + Number(item.packProcessCostFt))), 10000)//成本(含快递+打包) 单行
                    const totalCost = this.getNumber(Number((Number(item.cost) + Number(item.packProcessCostFt))), 10000)//成本(含快递+打包) 单行
                    // item.internalTotalAmount = this.getNumber(Number((totalCost) * 1.7), 100)//成本(含快递+打包) 单行
                    item.internalTotalAmount = this.getNumber(Number((totalCost)), 100)
                }
            })
            //合计
            this.ruleForm.internalTotalAmount = this.getNumber(this.ruleForm.dtls.reduce((total, item) => math.add(total, Number(item.internalTotalAmount)), 0), 100)
        },
        async editProps(id) {
            this.isView = true
            // this.buyUserInfo.id = id
            // this.buyUserInfo.buyUserId = null
            const { data, success } = await getTMZDCSRecordById({ id })
            if (success) {
                if (data.chatPicUrl) {
                    this.chatUrls = data.chatPicUrl.split(',').map((item, i) => {
                        return {
                            url: item,
                            name: `聊天截图${i + 1}`
                        }
                    })
                } else {
                    this.chatUrls = []
                }
                this.ruleForm = data
                this.ruleForm.dtls = data.dtls.map(item => {
                    return {
                        id: item.id,
                        norms: item.norms,//规格
                        type: item.type,//类型
                        sheetWidth: item.sheetWidth,//单张宽
                        sheetLength: item.sheetLength,//单张长
                        sheetCount: item.sheetCount,//张数
                        internalTotalAmount: item.internalTotalAmount,//售价
                        remark: item.remark,//备注
                        goodsCode: item.goodsCode,//商品编码
                    }
                })
                await this.getCatchList()
                this.drawer = true
            }
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            //没有时间就提示
            // if (!this.yearMonthDay) return this.$message.error('请选择日期')
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            // form.append("yearMonthDay", this.yearMonthDay);
            this.importLoading = true
            await importCustomMadeMultipleAsync(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            // this.yearMonthDay = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportTMZDSaleOrderRecordList(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '透明桌垫报价明细' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
            this.isExport = false
        },
        async getSetList() {
            const params = {
                currentPage: 1,
                pageSize: 10000,
                orderBy: 'createdTime',
                isAsc: false,
            }
            // 使用时将下面的方法替换成自己的接口
            const { data, success } = await getCostSetByTMZD(params)
            if (success) {
                this.list = data
            } else {
                //获取列表失败
                this.$message.error('获取数据失败')
            }
        },
        getNumber(num, precision) {
            return Math.floor(Number(num) * precision) / precision
        },
        //匹配数据
        changeNorms(i) {
            //匹配出对应规格的厚度,比重,公斤价格,裁剪费,打包费
            this.Profit3Data[i].actualLand = this.list.filter(item => item.norms == this.Profit3Data[i].norms && item.type == '透明')[0].actualLand//实际厚度
            this.Profit3Data[i].proportion = this.list.filter(item => item.norms == this.Profit3Data[i].norms && item.type == '透明')[0].proportion //比重
            this.Profit3Data[i].kgPriceCost = this.list.filter(item => item.norms == this.Profit3Data[i].norms && item.type == '透明')[0].kgPriceCost//公斤价格
            this.Profit3Data[i].croppingCost = this.list.filter(item => item.norms == this.Profit3Data[i].norms && item.type == '透明')[0].croppingCost//裁剪费
            this.Profit3Data[i].packProcessCost = this.list.filter(item => item.norms == this.Profit3Data[i].norms && item.type == '透明')[0].packProcessCost//打包费
            this.Profit3Data[i].pf = this.getNumber((this.Profit3Data[i].length * this.Profit3Data[i].width * this.Profit3Data[i].sheet), 10000) //平方
            this.Profit3Data[i].zl = this.getNumber((this.Profit3Data[i].actualLand * this.Profit3Data[i].pf * this.Profit3Data[i].proportion), 10000)//重量
            if (this.Profit3Data[i].pf > 0 && this.Profit3Data[i].zl > 0) {
                this.Profit3Data[i].cpcb = this.getNumber((this.Profit3Data[i].zl * this.Profit3Data[i].kgPriceCost), 10000)//产品成本
                this.Profit3Data[i].sjccjg = this.getNumber((this.Profit3Data[i].cpcb + (this.Profit3Data[i].croppingCost * this.Profit3Data[i].sheet) + this.Profit3Data[i].packProcessCost), 10000)//实际出仓价格
                const zl = this.Profit3Data[i].zl
                const kdf = [2.4, 2.7, 3.4, 5.4, 6.3, 7.2, 8.1]
                this.Profit3Data[i].kdf = kdf.find((item, index) => zl <= index + 1) || 8.1//快递费
                this.Profit3Data[i].bhkdcb = this.getNumber((this.Profit3Data[i].sjccjg + this.Profit3Data[i].kdf), 10000)//包含快递成本
                this.Profit3Data[i].croppingCostTotal = this.getNumber((this.Profit3Data[i].sheet * this.Profit3Data[i].croppingCost), 10000)//裁剪费
                this.Profit3Data[i].sj = this.getNumber((this.Profit3Data[i].bhkdcb * 1.7), 100)//售价
                //扣点=售价*0.05
                this.Profit3Data[i].kd = this.getNumber((this.Profit3Data[i].sj * 0.05), 10000)//扣点
                //其他费用=售价*0.08
                this.Profit3Data[i].qtfy = this.getNumber((this.Profit3Data[i].sj * 0.08), 10000)//其他费用
                //毛三利润=售价-快递成本-扣点-其他费用
                //使用mathjs库计算
                this.Profit3Data[i].mslr = math.subtract(math.subtract(math.subtract(this.Profit3Data[i].sj, this.Profit3Data[i].bhkdcb), this.Profit3Data[i].kd), this.Profit3Data[i].qtfy)
                //毛三利率=毛三利润/售价
                this.Profit3Data[i].msll = this.getNumber(((this.Profit3Data[i].mslr / this.Profit3Data[i].sj)), 10000) * 100//毛三利率
            }
        },
        addProps() {
            this.Profit3Data.push({
                norms: null,
                actualLand: null,
                length: null,
                width: null,
                sheet: null,
                pf: null,
                proportion: null,
                kgPriceCost: null,
                zl: null,
                croppingCost: null,
                dbf: null,
                packProcessCost: null,
                sjccjg: null,
                kdf: null,
                bhkdcb: null,
                sj: null,
                cpcb: null,
                kd: null,
                qtfy: null,
                mslr: null,
                msll: 0,
            })
        },
        async openProfit3Log() {
            await this.getSetList()
            this.Profit3Data = []
            this.drawer1 = true
        },
        async changeTime(e, type) {
            if (type == 'create') {
                this.ListInfo.startTime = e ? e[0] : null
                this.ListInfo.endTime = e ? e[1] : null
            } else {
                this.ListInfo.payStartTime = e ? e[0] : null
                this.ListInfo.payEndTime = e ? e[1] : null
            }
            await this.getList()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data, success } = await getTMZDSaleOrderRecordList(this.ListInfo)
            if (success) {
                data.list.forEach((item, index) => {
                    if (item.chatPicUrl) {
                        item.pics = JSON.stringify(item.chatPicUrl.split(",").map(a => {
                            return {
                                url: a
                            }
                        }));
                    }
                });
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
                this.loading = false
            } else {
                //获取列表失败
                this.loading = false
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.detail ::v-deep .vxetoolbar20221212 {
    display: none !important;
}

.iptCss {
    width: 60px;
}

::v-deep .cell {
    padding-left: 3px;
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}

.chatPicUrl {
    position: relative;

    .picTips {
        position: absolute;
        top: 0;
        left: 150px;
        color: #ff0000;
        font-size: 16px;
    }
}

.btnGroup1 {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}
</style>
