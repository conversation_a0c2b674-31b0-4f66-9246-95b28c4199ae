<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="140px" class="demo-ruleForm">
        <el-form-item label="区域：">
          <el-input v-model.trim="ruleForm.regionName" placeholder="区域" maxlength="50" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="部门类型：">
          <el-input v-model.trim="ruleForm.deptName" placeholder="部门类型" maxlength="50" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="部门：">
          <el-input v-model.trim="ruleForm.dept" placeholder="部门" maxlength="50" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="类型：">
          <el-input v-model.trim="ruleForm.type" placeholder="类型" maxlength="50" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="总人数：" prop="total">
          <inputNumberYh v-model="ruleForm.total" :placeholder="'总人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="上月人数：" prop="lastMonthPersonnel">
           {{ ruleForm.lastMonthPersonnel }}
        </el-form-item>
        <el-form-item label="人员增减浮动：" prop="personnelFluctuation">
           {{ ruleForm.personnelFluctuation }}
        </el-form-item>
        <el-form-item label="试用期人数：" prop="probationCount">
          <inputNumberYh v-model="ruleForm.probationCount" :placeholder="'试用期人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="正式人数：" prop="regularCount">
          <inputNumberYh v-model="ruleForm.regularCount" :placeholder="'正式人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="计件人数：" prop="regularPieceCount">
          <inputNumberYh v-model="ruleForm.regularPieceCount" :placeholder="'计件人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="月薪人数：" prop="regularMonthsalaryCount">
          <inputNumberYh v-model="ruleForm.regularMonthsalaryCount" :placeholder="'月薪人数'" class="publicCss" />
        </el-form-item>

        <el-form-item label="助理人数：" prop="assistantCount">
          <inputNumberYh v-model="ruleForm.assistantCount" :placeholder="'助理人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="专员人数：" prop="attacheCount">
          <inputNumberYh v-model="ruleForm.attacheCount" :placeholder="'专员人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="组长人数：" prop="groupLeaderCount">
          <inputNumberYh v-model="ruleForm.groupLeaderCount" :placeholder="'组长人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="主管人数：" prop="superviseCount">
          <inputNumberYh v-model="ruleForm.superviseCount" :placeholder="'主管人数'" class="publicCss" />
        </el-form-item>

        <el-form-item label="经理人数：" prop="managerCount">
          <inputNumberYh v-model="ruleForm.managerCount" :placeholder="'经理人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="总监/区负责人数：" prop="directorCount">
          <inputNumberYh v-model="ruleForm.directorCount" :placeholder="'总监/区负责人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="总经理：" prop="generalManager">
          <inputNumberYh v-model="ruleForm.generalManager" :placeholder="'总经理'" class="publicCss" />
        </el-form-item>
        <el-form-item label="总裁：" prop="ceoCount">
          <inputNumberYh v-model="ruleForm.ceoCount" :placeholder="'总裁'" class="publicCss" />
        </el-form-item>

      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { allRegionPersonSubmit } from '@/api/people/peoplessc.js';
export default {
  name: 'departmentEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      selectProfitrates: [],
      ruleForm: {
        label: '',
        name: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' }
        ],
        label: [
          { required: true, message: '请输入活动名称', trigger: 'blur' }
        ]
      }
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };
  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod');
    },
    submitForm(formName) {
      console.log(this.ruleForm.label, 'this.ruleForm.label');
      this.$refs[formName].validate(async(valid) => {
          if (valid) {
            // alert('submit!');
            const { data, success } = await allRegionPersonSubmit(this.ruleForm)
            if(!success){
                return
            }
            await this.$emit("search");

          } else {
            console.log('error submit!!');
            return false;
          }
        });
    //   this.$confirm('是否保存?', '提示', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(async () => {
    //     this.$refs[formName].validate(async(valid) => {
    //       if (valid) {
    //         // alert('submit!');
    //         const { data, success } = await allRegionPersonSubmit(this.ruleForm)
    //         if(!success){
    //             return
    //         }
    //         await this.$emit("search");

    //       } else {
    //         console.log('error submit!!');
    //         return false;
    //       }
    //     });
    //   }).catch(() => {
    //   });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
