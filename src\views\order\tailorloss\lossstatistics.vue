<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-button type="text" size="medium" disabled>统计维度:</el-button>
                <el-button style="padding: 4px;margin: 0;">
                    <el-checkbox-group v-model="filter.groupList" @change="groupchange">
                        <el-checkbox label="日期">日期</el-checkbox>
                        <el-checkbox label="操作人">操作人</el-checkbox>
                        <el-checkbox label="半成品编码">半成品编码</el-checkbox>
                    </el-checkbox-group>
                </el-button>
                <el-form-item label="日期：">
                    <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        :picker-options="pickerOptions">
                    </el-date-picker>
                </el-form-item>
                <el-form-item v-if="(!filter.isCheckWD || filter.groupSemiFinished)">
                    <!-- <el-select filterable clearable v-model="filter.goodsCode" placeholder="半成品编码">
                        <el-option v-for="item in goodCodeList" :key="item" :label="item" :value="item"></el-option>
                    </el-select> -->
                    <el-input v-model.trim="filter.goodsCode" style="width: 140px" placeholder="半成品编码" clearable
                        maxlength="40" />
                </el-form-item>
                <el-form-item v-if="(!filter.isCheckWD || filter.groupOperator)">
                    <el-select filterable clearable v-model="filter.picker" placeholder="操作人">
                        <el-option v-for="item in operatorList" :key="item" :label="item" :value="item"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button-group>
                        <!-- <el-button type="primary" @click="onshowdanalysis">查看图表</el-button> -->
                        <el-button type="primary" @click="onExport">导出</el-button>
                    </el-button-group>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :loading="listLoading" :tableHandles="tableHandles1" :isSelectColumn="false">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getLossStatisticsPageList, exportLossStatisticsList, getGoodsCodeHalfList, getOutStoreOperator } from '@/api/order/tailorloss';
const tableCols = [
    { istrue: true, prop: 'deliveryTime', label: '日期', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.deliveryTime, 'YYYY-MM-DD') },
    { istrue: true, prop: 'picker', label: '操作人', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'goodsCodeHalf', label: '半成品编码', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'goodsNameHalf', label: '半成品名称', width: '400' },
    { istrue: true, prop: 'outStoreCount', label: '出库次数', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'orderCount', label: '订单数', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'outStoreTotalArea', label: '出库总面积', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'saleTotalArea', label: '售出总面积', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'lossTotalArea', label: '损耗总面积', width: '150', sortable: 'custom' },
];
const tableHandles1 = [
    { label: "导出", handle: (that) => that.onExport() },
];
export default {
    name: 'lossstatistics',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow },
    props: {

    },
    data() {
        return {
            that: this,
            tableHandles1: tableHandles1,
            goodCodeList: [],
            operatorList: [],
            filter: {
                orderNoInner: null,
                operator: null,
                remark: null,
                logName: null,
                timerange: [
                    formatTime(dayjs().subtract(1, "month"), "YYYY-MM-DD"),
                    formatTime(new Date(), "YYYY-MM-DD"),
                ],
                startDate: null,
                endDate: null,
                groupList: [],
                groupDate: false,
                groupOperator: false,
                groupSemiFinished: false,
                isCheckWD: false

            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "outStoreCount", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            pickerOptions: {
                shortcuts: [
                    {
                        text: '昨天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime());
                            picker.$emit('pick', [start, start]);
                        }
                    }, {
                        text: '近三天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime());
                            const end = new Date(new Date(tdate.toLocaleDateString()));
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一周',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                            const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一个月',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                            console.log("获取前一个月的时间", tdate.getDay());
                            const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
            },
        };
    },
    async mounted() {
        await this.getGoodCodeList();
        await this.getOperatorList();
        await this.onSearch()
    },
    methods: {
        async getGoodCodeList() {
            const res1 = await getGoodsCodeHalfList();
            this.goodCodeList = [];
            res1.data?.forEach((f) => {
                if (f) this.goodCodeList.push(f);
            });
            this.goodCodeList.push(" ");
        },
        async getOperatorList() {
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            const res1 = await getOutStoreOperator({ stDate: this.filter.startDate, edDate: this.filter.endDate });
            this.operatorList = [];
            res1.data?.forEach((f) => {
                if (f) this.operatorList.push(f);
            });
        },
        //查询第一页
        async onSearch() {
            console.log(this.filter)
            if (!this.filter.timerange) {
                this.$message({ message: "请选择日期", type: "warning", });
                return;
            }
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message({ message: "请先选择时间", type: "warning" });
                return false;
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getLossStatisticsPageList(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
            await this.filtercols();
        },
        //排序查询
        async sortchange(column) {
            if (!column.order)
                this.pager = { OrderBy: "outStoreCount", IsAsc: false };
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const rlt = await exportLossStatisticsList(params);
            this.listLoading = false;
            if (rlt && rlt.data) {
                const aLink = document.createElement("a");
                let blob = new Blob([rlt.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '裁剪损耗-损耗统计_' + new Date().toLocaleString() + '_.xlsx')
                aLink.click()
            }
        },
        async groupchange(arry) {
            this.filter.groupDate = false;
            this.filter.groupOperator = false;
            this.filter.groupSemiFinished = false;
            this.filter.isCheckWD = false;
            if (arry.length > 0) {
                arry.forEach(f => {
                    if (f == "日期") this.filter.groupDate = true;
                    else if (f == "操作人") this.filter.groupOperator = true;
                    else if (f == "半成品编码") this.filter.groupSemiFinished = true;
                })
                this.filter.isCheckWD = true;
            } else {
                this.filter.isCheckWD = false;
            }
        },
        async filtercols() {
            this.tableCols.forEach(f => {
                if (this.filter.groupDate && !this.filter.groupOperator && !this.filter.groupSemiFinished) {
                    console.log(33333333);
                    if (f.prop == 'deliveryTime') f.istrue = true;
                    if (f.prop == 'picker') f.istrue = false;
                    if (f.prop == 'goodsCodeHalf') f.istrue = false;
                    if (f.prop == 'goodsNameHalf') f.istrue = false;
                }
                else if (this.filter.groupDate && !this.filter.groupOperator && this.filter.groupSemiFinished) {
                    if (f.prop == 'deliveryTime') f.istrue = true;
                    if (f.prop == 'picker') f.istrue = false;
                    if (f.prop == 'goodsCodeHalf') f.istrue = true;
                    if (f.prop == 'goodsNameHalf') f.istrue = true;
                }
                else if (this.filter.groupDate && this.filter.groupOperator && !this.filter.groupSemiFinished) {
                    if (f.prop == 'deliveryTime') f.istrue = true;
                    if (f.prop == 'picker') f.istrue = true;
                    if (f.prop == 'goodsCodeHalf') f.istrue = false;
                    if (f.prop == 'goodsNameHalf') f.istrue = false;
                }
                else if (this.filter.groupDate && this.filter.groupOperator && this.filter.groupSemiFinished) {
                    if (f.prop == 'deliveryTime') f.istrue = true;
                    if (f.prop == 'picker') f.istrue = true;
                    if (f.prop == 'goodsCodeHalf') f.istrue = true;
                    if (f.prop == 'goodsNameHalf') f.istrue = true;
                }
                else if (this.filter.groupOperator && !this.filter.groupDate && !this.filter.groupSemiFinished) {
                    if (f.prop == 'deliveryTime') f.istrue = false;
                    if (f.prop == 'picker') f.istrue = true;
                    if (f.prop == 'goodsCodeHalf') f.istrue = false;
                    if (f.prop == 'goodsNameHalf') f.istrue = false;
                }
                else if (this.filter.groupSemiFinished && !this.filter.groupDate && !this.filter.groupOperator) {
                    console.log(66666);
                    if (f.prop == 'deliveryTime') f.istrue = false;
                    if (f.prop == 'picker') f.istrue = false;
                    if (f.prop == 'goodsCodeHalf') f.istrue = true;
                    if (f.prop == 'goodsNameHalf') f.istrue = true;
                }
                else if (this.filter.groupSemiFinished && !this.filter.groupDate && this.filter.groupOperator) {
                    if (f.prop == 'deliveryTime') f.istrue = false;
                    if (f.prop == 'picker') f.istrue = true;
                    if (f.prop == 'goodsCodeHalf') f.istrue = true;
                    if (f.prop == 'goodsNameHalf') f.istrue = true;
                }
                else if (!this.filter.groupSemiFinished && !this.filter.groupDate && !this.filter.groupOperator) {
                    if (f.prop == 'deliveryTime') f.istrue = true;
                    if (f.prop == 'picker') f.istrue = true;
                    if (f.prop == 'goodsCodeHalf') f.istrue = true;
                    if (f.prop == 'goodsNameHalf') f.istrue = true;
                }
            })
        }
    },
};
</script>

<style lang="scss" scoped></style>
