<template>
  <div style="height: 600px; overflow-y: auto; overflow-x: hidden;">
    <!-- <div @click="buscharclick(1,'reftm')"> -->
      <div>
    <buschar :charid="'charNoProfit1'" ref="reftm" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :chartsdata="chartsdata1" v-if="isselplatform.indexOf('天猫')!=-1&&chartsdata1" ></buschar>
    </div>
    <!-- <div @click="buscharclick(4,'refalbb')"> -->
      <div>
    <buschar :charid="'charNoProfit2'" ref="refalbb" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :chartsdata="chartsdata2" v-if="isselplatform.indexOf('阿里巴巴')!=-1&&chartsdata2" ></buschar>
    </div>

    <!-- <div @click="buscharclick(6,'refdy')"> -->
      <div>
    <buschar :charid="'charNoProfit3'" ref="refdy" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :chartsdata="chartsdata3" v-if="isselplatform.indexOf('抖音')!=-1&&chartsdata3" ></buschar>
    </div>

    <!-- <div @click="buscharclick(7,'refjd')"> -->
      <div>
    <buschar :charid="'charNoProfit4'" ref="refjd" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :chartsdata="chartsdata4" v-if="isselplatform.indexOf('京东')!=-1&&chartsdata4" ></buschar>
    </div>

    <!-- <div @click="buscharclick(8,'reftgc')"> -->
      <div>
    <buschar :charid="'charNoProfit5'" ref="reftgc" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :chartsdata="chartsdata5" v-if="isselplatform.indexOf('淘工厂')!=-1&&chartsdata5" ></buschar>
    </div>

    <!-- <div @click="buscharclick(9,'reftb')"> -->
      <div>
    <buschar :charid="'charNoProfit6'" ref="reftb" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :chartsdata="chartsdata6" v-if="isselplatform.indexOf('淘宝')!=-1&&chartsdata6" ></buschar>
    </div>

    <!-- <div @click="buscharclick(10,'refsn')"> -->
      <div>
    <buschar :charid="'charNoProfit7'" ref="refsn" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :chartsdata="chartsdata7" v-if="isselplatform.indexOf('苏宁')!=-1&&chartsdata7" ></buschar>
    </div>

    <!-- <div @click="buscharclick(2,'refpdd')"> -->
      <div>
    <buschar :charid="'charNoProfit8'" ref="refpdd" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :chartsdata="chartsdata8" v-if="isselplatform.indexOf('拼多多')!=-1&&chartsdata8" ></buschar>
    </div>

    <div>
    <buschar :charid="'charNoProfit9'" ref="refxy" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :chartsdata="chartsdata9" v-if="isselplatform.indexOf('希音')!=-1&&chartsdata9" ></buschar>
    </div>

    <div v-if="chartsdatalist.length==0" style="width: 100%; height: 100%; color: #606266; display: flex; justify-content: center; align-items: center;">暂无数据...</div>
  </div>
</template>
<script>
import buschar from './buschar.vue'
import MyContainer from "@/components/my-container";
import * as echarts from 'echarts/core';
import { getContinuousNoProfit, newGetContinuousNoProfitAnalysisAsync } from "@/api/bookkeeper/continuousprofitanalysis"

import {
  getPlatformsLossesLinkAnalysis
 } from '@/api/operatemanage/continuLosses' //持续亏损
export default {
  name: "ConsecutiveNoProfitShowChar",
  components: {
    MyContainer, buschar
  },
  props: {
    filter: {
      
    },
    isselplatformlr: [],
    isselplatform: ['天猫',
        '阿里巴巴',
        '抖音',
        '京东',
        '淘工厂',
        '淘宝',
        '苏宁',
        '拼多多',
        '希音'
      ]
  },
  data() {
    return {
      that: this,
      emphasisStyle: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0,0,1,0.3)'
        }
      },
      filter1: {
        currentPage: 1,
        pageSize: 50
      },
      chartsdata: {},
      chartsdata1: null,
      chartsdata2: null,
      chartsdata3: null,
      chartsdata4: null,
      chartsdata5: null,
      chartsdata6: null,
      chartsdata7: null,
      chartsdata8: null,
      chartsdata9: null,
      chartsdatalist: []

    };
  },
  async mounted() {
    
    await this.showChar();
  },
  // watch: {
  //   isselplatformlr: {
  //     handler(newVal){
  //     },
  //     deep: true
  //   }
  // },
  methods: {
    buscharclick(e, name){
      

      
        let queryObj = {
         
          isPositive: false
        }

        queryObj.platform = e;
        // queryObj.shopCode = (that.filter.shopCode==""?null:that.filter.shopCode);
        // queryObj.groupId =  (that.filter.groupId==""?null:that.filter.groupId);
        // queryObj.operateSpecialUserId =  (that.filter.operateSpecialUserId==""?null:that.filter.operateSpecialUserId);
        if(!JSON.parse(localStorage.getItem('isselplatformlrnew'))){
          this.$message.info('请只选择一个标签负利润后才能跳转平台！');
          return
        }
        let seriesName = JSON.parse(localStorage.getItem('isselplatformlrnew'))[0];
        if (seriesName == "3天负利润") {
          queryObj.dayCount = 3;
        } else if(seriesName == "5天负利润"){
          queryObj.dayCount = 5;
        }else if (seriesName == "7天负利润") {
          queryObj.dayCount = 7;
        } else if(seriesName == "10天负利润"){
          queryObj.dayCount = 10;
        } else if (seriesName == "15天负利润") {
          queryObj.dayCount = 15;
        } else if (seriesName == "30天负利润") {
          queryObj.dayCount = 30;
        }
        // queryObj.dayCount = JSON.parse(localStorage.getItem('isselplatformlrnew'))[0];

        let patams = {}
        patams.query = queryObj;
        patams.path = "/bookkeeper/reportday/productReportAllIndex";
        if (e == 2) {
          patams.path = "/bookkeeper/reportday/productReportPddIndex";
          this.$refs.refpdd.routerpush(patams);
        } else if (e == 4) {
          patams.path = "/bookkeeper/reportday/productReportAlibabaIndex";
          this.$refs.refalbb.routerpush(patams);
        } else if (e == 6) {
          patams.path = "/bookkeeper/reportday/productReportDyIndex";
          this.$refs.refdy.routerpush(patams);
        } else if (e == 7) {
          patams.path = "/bookkeeper/reportday/productReportJDIndex";
          this.$refs.refjd.routerpush(patams);
        } else if (e == 8) {
          patams.path = "/bookkeeper/reportday/productReportGCIndex";
          this.$refs.reftgc.routerpush(patams);
        } else if ( e == 1) {
          patams.path = "/bookkeeper/reportday/productReportTxIndex";
          this.$refs.reftm.routerpush(patams);
        } else if (e == 9 ) {
          patams.path = "/bookkeeper/reportday/productReportTaoBaoIndex";
          this.$refs.reftb.routerpush(patams);
        } else if (e == 10) {
          patams.path = "/bookkeeper/reportday/productReportSuNingIndex";
          this.$refs.refsn.routerpush(patams);
        }

        
        

        // that.$router.push({ path: url, query: queryObj })
    },
    async showChar() {
      this.chartsdata1 = null;
      this.chartsdata2 = null;
      this.chartsdata3 = null;
      this.chartsdata4 = null;
      this.chartsdata5 = null;
      this.chartsdata6 = null;
      this.chartsdata7 = null;
      this.chartsdata8 = null;
      this.chartsdata9 = null;


      this.filter.profitTypes = [];

      let arr =  JSON.parse(localStorage.getItem('isselplatformlrnew'));
      var newass = [];
      if(arr.length>0){
        arr.map((item)=>{
          newass.push(item.slice('0',item.indexOf('天')))
        })
        this.filter.profitTypes = newass;
      }else{
        this.filter.profitTypes = null
      }
      


      let param = { 
  
        ...this.filter,
        selectColumn: ''
       };

      let res = await getPlatformsLossesLinkAnalysis(param);

      this.chartsdatalist = res.data;
      res.data.map((item)=>{
        if(item.platformName == '天猫'){
          this.chartsdata1 = item;
        }else if(item.platformName == '阿里巴巴'){
          this.chartsdata2 = item;
        }else if(item.platformName == '抖音'){
          this.chartsdata3 = item;
        }else if(item.platformName == '京东'){
          this.chartsdata4 = item;
        }else if(item.platformName == '淘工厂'){
          this.chartsdata5 = item;
        }else if(item.platformName == '淘宝'){
          this.chartsdata6= item;
        }else if(item.platformName == '苏宁'){
          this.chartsdata7 = item;
        }else if(item.platformName == '拼多多'){
          this.chartsdata8 = item;
        }else if(item.platformName == '希音'){
          this.chartsdata9 = item;
        }
      })


     
    }
  }
};
</script>
<style>
.a{
  overflow-y: auto;
  position: relative;
  
}
</style>