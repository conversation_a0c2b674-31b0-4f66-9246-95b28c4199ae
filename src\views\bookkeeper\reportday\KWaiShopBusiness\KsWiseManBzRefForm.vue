<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template>
      <!--表单-->
      <el-form :model="form" :rules="rules" ref="form" label-width="140px" label-position="right">
        <el-row>
          <el-col :span="24">
            <el-form-item label="达人快手号：" prop="wiseManAccountCode">
              <el-input v-model="form.wiseManAccountCode" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="商品ID" prop="proCode">
              <el-input v-model="form.proCode" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="商务" prop="businessDDUserId">
              <!-- <el-input v-model="form.businessMan" maxlength="50" /> -->
              <el-select v-model="form.businessDDUserId" style="width: 100%" placeholder="请选择" clearable filterable>
                <el-option v-for="item in businessManList" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="微信号" prop="weChat">
              <el-input v-model.trim="form.weChat" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="佣金率（%）" prop="yjRate">
            <el-input v-model.trim="form.yjRate" maxlength="50" />
             <span style="color: red;"> 温馨提示：如果达人佣金率为3%，请填写3 </span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

    </template>
    <template slot="footer">
      <el-row>
        <el-col :span="24" style="text-align:right;">
          <el-button @click="onClose">关闭</el-button>
          <el-button v-if="mode < 3" type="primary" @click="onSave(true)">保存</el-button>
        </el-col>
      </el-row>
    </template>

  </my-container>
</template>
<script>

import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import { SaveProductDyWiseManBzRef, GetBusinessMan } from "@/api/bookkeeper/reportdayDouYin";
import { saveProductWiseManBzRef } from '@/api/bookkeeper/reportdayV2'

export default {
  name: "DyWiseManBzRefForm",
  components: { MyContainer, MyConfirmButton, },
  data() {
    return {
      platForm: 14,
      that: this,
      mode: 3,
      userName: '',
      businessManList: [],
      form: {
        id: 0,
        wiseManAccountCode: '',
        proCode: '',
        businessDDUserId: null,
        weChat: '',
        businessMan: ''
      },
      rules: {
        businessDDUserId: [{ required: true, message: '请输入商务', trigger: 'blur' }],
        wiseManAccountCode: [{ required: true, message: '请输入达人快手号', trigger: 'blur' }],
        proCode: [{ required: true, message: '请输入商品ID', trigger: 'blur' }],
        weChat: [{ required: true, message: '请输入微信号', trigger: 'blur' }],
        yjRate: [{ required: true, message: '请输入佣金率', trigger: 'blur' }],
      },

      pageLoading: false,
      formEditMode: true,//是否编辑模式
    };
  },
  async mounted() {
  },
  computed: {
  },
  methods: {
    async onBusinessManMethod(value) {
      this.businessManList = [];
      let pos = await GetBusinessMan({ keywords: value });
      if (pos?.success && pos?.data && pos?.data.length > 0) {
        pos?.data.forEach(f => {
          if (f.key === "" && f.value1 === "无商务") {
            this.businessManList.push({ value: "noCommerce", label: f.value1 });
          } else {
            this.businessManList.push({ value: f.key, label: f.value1 });
          }
        });
      }
    },
    onClose() {
      this.$emit('close');
    },
    async onSave(isClose) {
      if (await this.save()) {
        this.$emit('afterSave');
        if (isClose)
          this.$emit('close');
      }
    },
    async loadData({ data, mode }) {
      let self = this;
      self.pageLoading = true;
      self.formEditMode = mode != 3;
      self.mode = mode;

      await this.onBusinessManMethod("");

      if (data) {
        let formDto = { ...data }
        this.form = formDto;
      }
      if (this.form.businessDDUserId == "" && this.form.businessMan == "无商务") {
        this.form.businessDDUserId = "noCommerce";
      }

      this.pageLoading = false;

    },
    async save() {
      this.pageLoading = true;
      let la = this.businessManList.find(f => f.value == this.form.businessDDUserId)?.label;
      if (la) {
        this.form.businessMan = la;
      }
      let saveData = { ...this.form, platForm: this.platForm };
      try {
        await this.$refs["form"].validate();
      } catch (error) {
        this.pageLoading = false;
        return false;
      }
      if (saveData.businessDDUserId == "noCommerce" && saveData.businessMan == "无商务") {
        saveData.businessDDUserId = "";
      }

      let rlt = await saveProductWiseManBzRef(saveData);
      if (rlt && rlt.success) {
        this.$message.success('保存成功！');
      }

      this.pageLoading = false;

      return (rlt && rlt.success);
    }
  },
};
</script>
