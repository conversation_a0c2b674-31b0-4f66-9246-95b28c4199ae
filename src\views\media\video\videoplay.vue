
<template>
    <el-row :gutter="15"  >
        <el-col :xs="24" :sm="24" :lg="8">
            <div class="video-container" style="padding-left:22px">
                
                <video class="my-video" ref="originVideo"  oncontextmenu="return false;"></video>
                <!-- <div class="block"><input type="file" @change="select_origin_file" /></div> -->
            </div>

        </el-col>
        <el-col :xs="24" :sm="24" :lg="4">
            <div class="block">
                <my-container style="height: 400px;">
                <el-table :data="referenceVideoList" highlight-current-row style="max-height: 400px;">
                    <el-table-column prop="title" label="参考备注" width="160">
                    </el-table-column>
                    <el-table-column label="裁剪参考">
                        <template slot-scope="scope">
                            <el-button type="primary" @click="playvideo(scope.row)">裁剪参考{{scope.row.ckVideoIndex}}
                            </el-button>
                        </template>
                    </el-table-column>

                </el-table>
                </my-container>
            </div>
        </el-col>
        <el-col :xs="24" :sm="24" :lg="12">
            <div class="block">
                <my-container style="height: 400px;">
                <el-table :data="clipArray" highlight-current-row  style="max-height: 400px;" :row-class-name="rowClassName">
                    <el-table-column label="参考视频" width="80">
                        <template slot-scope="scope">
                           <span>参考视频{{scope.row.videoIndex}}</span>  
                        </template>
                    </el-table-column>
                    <el-table-column prop="title" label="第几段" align="center" width="80">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.title" :disabled="true"> </el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="firstSceneIds" label="场景" align="center" width="100">
                        <template slot-scope="scope">
                            <el-cascader  v-model="scope.row.firstSceneIds" :options="taskGridFirstSceneList"> </el-cascader>
                        </template>
                    </el-table-column>
                    <el-table-column prop="remark" label="备注" align="center">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.remark" placeholder="备注"> </el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="beginTime" label="开始时间" align="center" width="120">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.beginTime" > </el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="endTime" label="结束时间" align="center" width="120">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.endTime"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column lable="操作" width="150">
                        <template slot-scope="scope">
                            <el-button type="danger" @click="onDelCutVideo(scope.row,scope.$index)">移除<i class="el-icon-remove-outline"></i>
                            </el-button>
                            <el-button type="primary" @click="palyClipV(scope.row)">播放
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </my-container>
            </div>
        </el-col>
        <el-col :xs="24" :sm="24" :lg="24">

            <div v-show="isShow" style="padding-left:22px;padding-right:22px">
                <div class="block">
                    <!-- <button type="button" @click="clipVideo">剪辑</button>
                    <button type="button" @click="clearClip">清空</button> -->
                    <!-- <button type="button" @click="moveFrame('up')">上一帧</button> -->
                    <!-- <button type="button" @click="playVio">{{this.isPlay ?'暂停':'播放' }}</button> -->
                    <!-- <button type="button" @click="moveFrame('down')">下一帧</button> -->
                    <!-- <el-button type="primary" @click="clearClip">清空</el-button> -->
                    <!-- <el-button type="primary" @click="moveFrame('up')">上一帧</el-button> -->
                    <el-button type="primary" @click="playerVioder">{{this.isPlay ?'暂停':'播放' }}</el-button>
                    <!-- <el-button type="primary" @click="moveFrame('down')">下一帧</el-button> -->
                    <el-button type="primary" @click="clipVideo">裁剪</el-button>
                </div>
                <!-- <button type="button" @click="setVoice">{{this.isSound ?'关闭声音':'打开声音' }}</button>
                    <span>音量:</span>
                    <input type="range" v-model="rangeData" max="100" min="0" @change="setRangevalue" /> -->
                <div class="block">
                    <div style="float:right;color: #72767b;font-size: 9px;">{{videoTime}}</div>
                    <el-slider v-model="videorage" range :max="maxVideoV" :marks="sliderMarks" :format-tooltip="formatTooltip" @input="setNowVideoV"
                 
                    >
                    </el-slider>
                </div>
            </div>

            <div id="kdx" class="axis" data-v-6b73916c="" style="height: 150px;margin-top:20px;display:none">
                <div class="axis-dragbar" data-v-56d3f446="" data-v-6b73916c=""></div>

                <div class="axis-play" data-v-6b73916c="" style="padding-left: 0px;">

                    <div class="axis-play-main" data-v-6b73916c="" style="height: 150px;">
                        <svg id="svg" width="100000" height="16" data-v-dfd7e312="" data-v-6b73916c="">
                            <line y1="14.5" y2="14.5" x2="100000" id="y1" stroke-width="3" stroke="#d9d9d9" stroke-dasharray="2,8" data-v-dfd7e312=""></line>
                            <line y1="13" y2="13" x2="100000" id="y2" stroke-width="6" stroke="#d9d9d9" stroke-dasharray="2,48" data-v-dfd7e312=""></line>
                            <g font-size="8" fill="#86909c" text-anchor="middle" data-v-dfd7e312="" id="glist">
                            </g>
                        </svg>
                        <div class="axis-timeline axis-play-pic-bar" data-log="{mod: 'axis', c: 'dragTimeline'}" data-v-1cd08a9c="" data-v-6b73916c="" style="left: 9px;">
                            <div class="axis-timeline-top" data-v-1cd08a9c=""></div>
                        </div>
                        <div class="free-axis free-axis--free" data-v-67e71ce5="" style="width: 1386px; height: 164px;">
                            <ul class="free-axis__stickers" data-v-67e71ce5="" style="height: 64px;"></ul>

                            <!--v-if-->
                            <ul class="free-axis__audios" data-v-67e71ce5="" style="height: 64px;"></ul>
                            <!--v-if-->
                        </div>
                        <!--v-if-->
                        <!--v-if-->

                    </div>
                </div>
            </div>
        </el-col>
    </el-row>

</template>
<script>
import MyContainer from "@/components/my-container";
import { getCuteVideoAsyncByTaskIdList} from '@/api/media/video'
    export default {
        components: { MyContainer},
        props: {
            videopath: '',
            videoid: '',
            videoduration: 100000,
            taskGridFirstSceneList: [],
            referenceVideoList: []
        },
        data() {
            return {
                curindex :0,
                videoTime: '00:00:00.000',
                isShow: false,
                isPlay: false,
                isPlaying: false,
                isSound: true,
                rangeData: 10,
                ckvideorow: null,
                oldRangeData: 10,
                maxVideoV: 1000,
                nowVideoV: 0,
                videorage: [0, 0],
                rageFirst: 0,
                rageCur:0,
                rageEnd:0,
                clickClipRow: {},
                lineInterval: null,
                clipArray: [], //存储剪辑的数组
                sliderMarks: {
                },
                showclipArray: [], //存储剪辑的数组
            }
        },
        mounted() {
            //获取已剪切视频片段，
            this.getOldLsit();
            // 监听视频播放
            this.$refs.originVideo.addEventListener("error", () => {
                this.$message({ message: '视频源不可用，请检查参考视频链接', type: "error" });
                //this.openTimer();
            });
            // 监听视频播放
            this.$refs.originVideo.addEventListener("play", () => {
                console.log("video is playing");
                //this.openTimer();
            });
            // 监听视频暂停
            this.$refs.originVideo.addEventListener("pause", () => {
                console.log("video is stop");
                this.isPlay =  false;
            });
            //监听视频可以播放
            this.$refs.originVideo.addEventListener("canplay", () => {
                this.isShow = true;
                this.maxVideoV = Math.floor(this.$refs.originVideo.duration * 100);
                //开始渲染进度条 
                // this.sliderMarks = {};
                // var maxFor = Math.floor(this.maxVideoV / 100)
                // for (var i = 1; i <= maxFor; i++) {
                //     this.$set(this.sliderMarks, i * 100, this.transformSecondToVideoFormat(i * 100, true));
                // }
                //this.$refs.originVideo.playbackRate = 0.5;
            });
            //监听视频播放时间改变
            this.$refs.originVideo.addEventListener("timeupdate", () => {
                console.log("video is timeupdate");
                //console.log(this.$refs.originVideo.duration);
                // this.nowVideoV = Math.floor(this.$refs.originVideo.currentTime * 100)
                // if (this.clickClipRow.endTime !== undefined) {
                //     if (this.nowVideoV >= Math.floor(this.clickClipRow.endTime * 100)) {
                //         this.isPlay = true;
                //         this.playVio();
                //     }
                // }
                // console.log(this.transformSecondToVideoFormat(this.nowVideoV, true));
                //this.setVideoTime();
            });
            //视频停止
            this.$refs.originVideo.addEventListener("ended", () => {
                this.isPlay = false;
                // clearInterval(this.lineInterval);
                // this.syncKd();
            });
            this.splength = this.videoduration / 1000;
            //this.select_origin_file();
            this.keyDown();
        },
        methods: {
            rowClassName: function ({ row }) {
                if ( row.videoPdId != this.ckvideorow.pdId) {
                    return "vedioplayshowRow";
                }
            },
            async getOldLsit(){
              var res =   await getCuteVideoAsyncByTaskIdList({videoTaskId : this.videoid});
              if (res?.success) {
                this.clipArray = res.data.data;
              }
            },
            //按钮监听键盘
            keyDown() {
                var that = this;
                //监听键盘按钮
                document.onkeydown = function (event) {
                    var e = event || window.event;
                    var keyCode = e.keyCode || e.which;
                    //向前
                    if (keyCode == 37) {
                    
                        if (that.rageFirst == that.rageCur) {
                           
                            that.videorage = [that.videorage[0]- 10, that.videorage[1] ];
                            that.$refs.originVideo.currentTime = that.videorage[0] / 100;
                            that.rageCur = that.videorage[0];
                            that.rageFirst = that.videorage[0];
                        } else {
                            that.videorage = [that.videorage[0] , that.videorage[1]- 10];
                            that.$refs.originVideo.currentTime = that.videorage[1] / 100
                            that.rageCur = that.videorage[1];
                            that.rageEnd = that.videorage[1];
                        }
                    } else if (keyCode == 39) {
                        if (that.rageFirst == that.rageCur) {
                            that.videorage = [that.videorage[0] + 10, that.videorage[1]];
                            that.$refs.originVideo.currentTime = that.videorage[0] / 100;
                            that.rageCur = that.videorage[0];
                            that.rageFirst = that.videorage[0];
                        } else {
                            that.videorage = [that.videorage[0] , that.videorage[1]+ 10];
                            that.$refs.originVideo.currentTime = that.videorage[1] / 100
                            that.rageCur = that.videorage[1];
                            that.rageEnd = that.videorage[1];
                        }
                    }
                    that.nowVideoV = that.videorage[1];
                    that.clickClipRow = {
                        "beginTime": that.videorage[0] / 100,
                        "endTime": that.videorage[1] / 100
                    };

                }
            },
            onDelCutVideo(row,index) {
                /* if(row.cuteId == 0){
                    this.clipArray.forEach(element => {
                        if(element.videoIndex == row.videoIndex){
                            if(element.cuteId == 0 && row.ckCounts < element.ckCounts ){
                                element.ckCounts = element.ckCounts-1;
                                element.title="第" + element.ckCounts + "段";
                            }
                        }
                    });
                }
                for(let num in this.clipArray){
                    if(this.clipArray[num].videoIndex==row.videoIndex &&  this.clipArray[num].ckCounts==ckCounts){
                        this.clipArray.splice(num,1)
                    }
                } */

                if(row.cuteId == 0){
                    this.clipArray.forEach(element => {
                        if(element.videoPdId == row.videoPdId){
                            if(element.cuteId == 0 && row.ckCounts < element.ckCounts ){
                                element.ckCounts = element.ckCounts-1;
                                element.title="第" + element.ckCounts + "段";
                            }
                        }
                    });
                    this.clipArray.splice(index, 1);
                }
                else
                {
                    this.clipArray.splice(index, 1);
                }
            },
            setVideoTime() {
                this.videoTime = this.transformSecondToVideoFormat(this.videorage[0], false) + " - " + this.transformSecondToVideoFormat(this.videorage[1], false);
            },
            playvideo(row) {
                    this.isShow = false;
                    this.ckvideorow = row;
                    this.videopath = row.url;
                    var url = this.videopath;//'http://www.si-tech.com.cn/pub-ui/images/radio/sitech.mp4';
                    // var url = 'http://localhost:8300/statics/upload/media/video/1570610230377713664.mp4'
                    this.$refs.originVideo.src = url;
                    this.videorage = [0, 0];
                    this.$refs.originVideo.volume = this.rangeData / 100;
                    this.$refs.originVideo.muted = false;
                    this.isPlay =  false;
               
            },

            select_origin_file(e) {
                //const file = e.target.files[0]
                // const url = window.webkitURL.createObjectURL(file)

                var url = this.videopath;//'http://www.si-tech.com.cn/pub-ui/images/radio/sitech.mp4';
                // var url = 'http://localhost:8300/statics/upload/media/video/1570610230377713664.mp4'
                this.$refs.originVideo.src = url;
                this.$refs.originVideo.volume = this.rangeData / 100;
                this.$refs.originVideo.muted = false;
            },
            playerVioder() {
                this.playVio();
                this.isPlay = !this.isPlay;
            },
            playVio() {

                if (this.isPlay) {
                    this.$refs.originVideo.pause();
                    this.isPlaying = false;
                    //clearInterval(this.lineInterval);
                } else {
                   this.$refs.originVideo.play();
                   this.isPlaying = true;
                }
            },
            moveTimeline() {
                //  $(".axis-timeline").css("left", (parseFloat($(".axis-timeline").css("left").replace("px", '')) + 1) + "px");
            },
            setVoice() {
                const isSoundThat = this.isSound;
                this.$refs.originVideo.muted = isSoundThat;
                if (isSoundThat) {
                    this.oldRangeData = this.rangeData;
                    this.rangeData = 0
                } else {
                    if (this.rangeData != 10) {
                        this.rangeData = this.oldRangeData;
                    }
                }
                this.isSound = !isSoundThat;
            },
            //拖动进度条
            setNowVideoV() {
                if (this.isPlaying) {
                    this.isPlay = true;
                    this.playVio();
                }

                this.clickClipRow = {
                    "beginTime": this.videorage[0] / 100,
                    "endTime": this.videorage[1] / 100
                };

             
                //如果移动的是前面点
                if (this.rageFirst != this.videorage[0]) {
                    this.$refs.originVideo.currentTime = this.videorage[0] / 100;
                    this.rageFirst = this.videorage[0];
                    this.rageCur= this.videorage[0];
                } 
                
                if(this.rageEnd != this.videorage[1]) {
                    this.$refs.originVideo.currentTime = this.videorage[1] / 100
                    this.rageEnd = this.videorage[1];
                    this.rageCur= this.videorage[1];
                }
               


                this.nowVideoV = this.videorage[1];
                this.setVideoTime();
                // this.syncKd();
            },
            //设置视频声音
            setRangevalue() {
                if (this.rangeData == 0) {
                    this.isSound = false;
                } else {
                    this.$refs.originVideo.volume = this.rangeData / 100;
                    this.$refs.originVideo.muted = false;
                    this.isSound = true;
                }

            },
            //帧移动
            moveFrame(actionType) {
                this.isPlay = true;
                this.playVio();
                if (actionType == "up") {
                    if (this.nowVideoV > 0) {
                        this.$refs.originVideo.currentTime = this.nowVideoV / 100 - 0.04;
                    }
                } else {
                    if (this.nowVideoV < this.maxVideoV) {
                        this.$refs.originVideo.currentTime = this.nowVideoV / 100 + 0.04;
                    }
                }
                this.syncKd();
            },
            clipVideo() {
                //先视频暂停播放
                //debugger;
                if (this.isPlaying) {
                    this.isPlay = true;
                    this.playVio();
                }
              if(this.videorage[1] ==this.videorage[0]){

                    this.$message({ type: 'warning', message: "剪辑视频不能小于0秒" });
                    return;
                }  
                var hasold = true;
                this.clipArray.forEach(element => {
                    //element.videoPdId == row.videoPdId
                    if(element.videoPdId == this.ckvideorow.pdId){
                        this.curindex = element.ckCounts+1;
                        hasold =false;
                    }
                });
               
                //未找到
                if(hasold){
                    this.curindex = 1;
                }
                this.clipArray.push({
                    "cuteId":0,
                    "ckCounts" :this.curindex,
                    "title": "第" + this.curindex + "段",
                    "remark": this.ckvideorow.remark, 
                    "firstSceneIds": this.ckvideorow.firstSceneIds,
                    "videoIndex": this.ckvideorow.ckVideoIndex, 
                    "videoPdId": this.ckvideorow.pdId, 
                    "beginTime": this.formatTooltip(this.videorage[0]),
                    "endTime":  this.formatTooltip(this.videorage[1]),
                    "beginTime1": this.videorage[0],
                    "endTime1":  this.videorage[1]
                 })
            },
            getData() {
                return this.clipArray;
            },
            palyClipV(row) {
                this.referenceVideoList.forEach(element => {
                    if(element.pdId == row.videoPdId){
                        this.$refs.originVideo.src = element.url;
                    }
                });
                this.changeTime(row,1);
                this.changeTime(row,0);
                
            
                this.clickClipRow = {
                    "beginTime": row.beginTime1 / 100,
                    "endTime": row.endTime1 / 100
                };
                this.videorage = [row.beginTime1, row.endTime1];
                this.$refs.originVideo.currentTime = row.beginTime1/100;
                this.isPlay = false;
                this.playVio();
                this.textindex = row.beginTime1 * 10;
                this.setVideoTime();
            },
            clearClip() {
                this.clickClipRow = {};
                this.clipArray = [];
                this.$refs.originVideo.currentTime = 0;
                this.isPlay = true;
                this.playVio();
            },
            formatTooltip(val) {
                return this.transformSecondToVideoFormat(val, false);
            },
            changeTime(row, index) {
                if (index == 1) {
                    row.beginTime1 = this.transformTimeToSecondFormat(row.beginTime);
                } else {
                    row.endTime1 = this.transformTimeToSecondFormat(row.endTime);
                }
            },
          
            transformTimeToSecondFormat(t) {
                var data = 0;
                var arr = t.split(":");
                let hour = parseInt(arr[0]) * (1000 * 60 * 60);
                let minutes = parseInt(arr[1]) * (1000 * 60);
                var arr2 = arr[2].split(".");
                let second = parseInt(arr2[0]) * (1000);
                let millisecond = parseInt(arr2[1]);
                data = hour + minutes + second + millisecond;
                return data / 10;
            },
            transformSecondToVideoFormat(value = 0, isSecond = false) {
                const totalMilliSecond = Number(value * 10)
                let hours = Math.floor((totalMilliSecond % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
                let minutes = Math.floor((totalMilliSecond % (1000 * 60 * 60)) / (1000 * 60))
                let second = Math.floor((totalMilliSecond % (1000 * 60)) / 1000)

                let hoursText = ''
                let minutesText = ''
                let secondText = ''
                let millisecondText = ''
                if (hours < 10) {
                    hoursText = `0${hours}`
                } else {
                    hoursText = `${hours}`
                }
                if (minutes < 10) {
                    minutesText = `0${minutes}`
                } else {
                    minutesText = `${minutes}`
                }
                if (second < 10) {
                    secondText = `0${second}`
                } else {
                    secondText = `${second}`
                }

                if (!isSecond) {
                    if (totalMilliSecond % 1000 < 10) {
                        millisecondText = `00${Math.floor((totalMilliSecond % 1000))}`
                    } else if (totalMilliSecond % 1000 < 100) {
                        millisecondText = `0${Math.floor((totalMilliSecond % 1000))}`
                    } else {
                        millisecondText = `${Math.floor((totalMilliSecond % 1000))}`
                    }
                    //:${millisecondText}
                    return `${hoursText}:${minutesText}:${secondText}.${millisecondText}`
                } else {

                    return `${hoursText}:${minutesText}:${secondText}`
                }

            }
        
        }
    }
</script>

<style>
.vedioplayshowRow {
display: none;
}
    .my-video {
        width: 600px;
        height: 400px;
    }
    .slider-container {
        padding: 0 16px;
    }


</style>
