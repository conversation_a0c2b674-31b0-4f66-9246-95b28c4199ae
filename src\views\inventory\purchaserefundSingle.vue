<template>
  <container v-loading="pageLoading">
     <ces-table ref="table" :that='that' :isIndex='true' @sortchange='sortchange' @select='selectchange'
         :hasexpand='true' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"
         :showsummary='true' :summaryarry='summaryarry'>
         <template slot='extentbtn'>
       <el-button-group>
           <el-button style="padding: 0;margin: 0;">
         <el-input v-model.trim="filter.buyNo" clearable style="width: 200px" placeholder="空格分隔采购单号"/>
         </el-button>

        <el-button style="padding: 0;margin: 0;">

         <el-input v-model.trim="filter.SuName" maxlength="30" clearable style="width: 150px" placeholder="供应商"/>
      </el-button>
         <el-button style="padding: 0;margin: 0;">
         <el-date-picker style="width: 300px" v-model="filter.timerangetk" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至" start-placeholder="退款开始日期" end-placeholder="退款结束日期"></el-date-picker>

         </el-button>
       <el-button type="primary" @click="onSearch">查询</el-button>
        </el-button-group>
       </template>
      </ces-table>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
   <el-drawer title="新增退款单" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="addVisible"
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
       <form-create :rule="autoform.rule1" v-model="autoform.fApi" :option="autoform.options"/>
       <div class="drawer-footer">
        <el-button @click.native="addVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editLoading" @click="onaddSubmit" />
      </div>
    </el-drawer>
<el-drawer title="编辑退款单" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editVisible"
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
       <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
       <div class="drawer-footer">
        <el-button @click.native="editVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editLoading" @click="onEditSubmit" />
      </div>
    </el-drawer>

    <!-- <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%">
      <span>
        <el-upload ref="upload" class="upload-demo"
          :auto-upload="false" :multiple="false" action accept=".xlsx"
          :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}   </el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog> -->
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

  </container>
</template>
<script>

import {importReturnAmount,getReturnList,editReturnAmount,deleteReturnAmount,exportReturnAmount} from '@/api/inventory/purchase'
import {formatTime} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import logistics from '@/components/Comm/logistics'
import {getAllSupplier} from '@/api/inventory/supplier'
import { downloadLink } from "@/utils/tools";

const tableCols =[
      {istrue:true,prop:'buyNo',label:'采购单号', width:'200',sortable:'custom'},
      {istrue:true,prop:'supplier',label:'供应商', width:'300',sortable:'custom'},
      {istrue:true,prop:'returnAmount',label:'退款金额', width:'200',sortable:'custom'},
       {istrue:true,prop:'refundAccount',label:'退款账户', width:'300',sortable:'custom'},
      {istrue:true,prop:'returnDate',label:'退款时间',  width:'175',sortable:'custom',formatter:(row)=>formatTime(row.returnDate,'YYYY-MM-DD ')},
      {istrue:true,prop:'batchNumber',label:'批次号', width:'180'},
       {istrue:true,prop:'createdTime',label:'导入时间',  width:'150',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD HH:mm:ss')},
         {istrue:true,type:'button', width:'55',label:'操作', width:'100',btnList:[{label:"编辑",handle:(that,row)=>that.onHand(row)},{label:"删除",handle:(that,row)=>that.onDelete(row)}]},
     ];
const tableHandles=[
    {label:"导入", handle:(that)=>that.startImport()},
    {label:"退款单导入模板", handle:(that)=>that.downloadTemplate()},
     {label:"导出", handle:(that)=>that.onExport()},
      {label:"添加退款单", handle:(that)=>that.addReturnAmount()},
];
export default {
  name: "Users",
  components: {container,cesTable,MyConfirmButton,logistics},

  data() {
    return {
      fileparm: {},
       editLoading:false,
      addVisible:false,
      deletefilter:{
       id:"",
      },

      editVisible:false,
      that:this,
      formatTime:formatTime,
      filter: {
        paymentAccount:'',
        timerangetk:'',
        buyNo:'',
        SuName:null
      },

      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"batchNumber",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [],
      fileList:[],
      suppilelist:[],
      listLoading: false,
      dialogVisible: false,
      pageLoading: false,
      uploadLoading:false,
      autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
        },
    };
  },
  watch: {
    value(n) {
      if(n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    }
  },
 async mounted() {
    await this.onSearch();
    await this.init();
    await this.initform();
  },
 methods: {
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      var res = await importReturnAmount(form);
      this.uploadLoading = false
      if (res?.success){
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
        this.dialogVisible = false;
        await this.getList()
      } else {
        this.fileList = []
      }
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },

//添加退款单信息
    addReturnAmount(){
      this.addVisible=true;

    },
async onaddSubmit(){
      this.editLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          const res = await editReturnAmount(formData);
          await this.autoform.fApi.resetFields()
          if(res.success){
            this.$message.success('添加成功！');
            this.getlist();
            this.addVisible=false;
          }
        }else{}
     })
     this.editLoading=false;


     },
  //单删
   async onDelete(row){

     this.deletefilter.id=row.id
     console.log("删除参数传递",this.deletefilter);
       this.$confirm('确认删除, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
        }).then(async () => {
            const res = await deleteReturnAmount(this.deletefilter)
            if (!res?.success) {return }
            this.$message({type: 'success',message: '删除成功!'});
            this.onSearch()
        }).catch(() => {
          this.$message({type: 'info',message: '已取消删除'});
        });

   },
  //编辑
     async onHand(row){
      this.formtitle='编辑';
      this.editVisible = true

      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields()
          this.$nextTick(async() =>{
      await this.autoform.fApi.setValue(row)
      })
    },
 async onEditSubmit() {
      this.editLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          const res = await editReturnAmount(formData);
          if(res.code==1){
            this.$message.success('修改成功！');
             this.getlist();
            this.editVisible=false;
          }
        }else{}
     })
     this.editLoading=false;
    },

    async initform(){

       this.autoform.rule= [{type:'hidden',field:'id',title:'id',value: ''},
                     {type:'input',field:'buyNo',title:'采购单号',value: '',props:{readonly:true},col:{span:6}},
                     {type:'input',field:'supplier',title:'供应商',value: '',props:{readonly:false},col:{span:6}},
                     {type:'input',field:'returnAmount',title:'退款金额',value: '',props:{min:0,precision:0,maxlength:7},validate: [{pattern:/(^[1-9](\d+)?(\.\d{1,2})?$)|(^0$)|(^\d\.\d{1,2}$)/,message:"输入金额的格式不正确！"}]},
                     {type:'input',field:'refundAccount',title:'退款账户',value: '',props:{min:0,precision:0}},
                     {type:'DatePicker',field:'returnDate',title:'退款时间',value:'',validate: [{type: 'string', required: true, message:'请输入退款时间'}],props: {type:'datetime',format:'yyyy-MM-dd',placeholder:'退款时间'},col:{span:6}},

                    ]
                    , this.autoform.rule1= [
                     {type:'input',field:'buyNo',title:'采购单号',value: '',validate: [{type: 'string', required: true, message:'请输入采购单号'}],col:{span:6},props: { maxlength:12}},
                     {type:'input',field:'supplier',title:'供应商',value: '',props:{readonly:false},col:{span:6}},
                     {type:'input',field:'returnAmount',title:'退款金额',value:'',props:{min:0,precision:0,maxlength:7},validate: [{pattern:/(^[1-9](\d+)?(\.\d{1,2})?$)|(^0$)|(^\d\.\d{1,2}$)/,message:"输入金额的格式不正确！"}]},
                     {type:'input',field:'refundAccount',title:'退款账户',value: '',props:{min:0,precision:0,maxlength:15}},
                     {type:'DatePicker',field:'returnDate',title:'退款时间',value:'',validate: [{type: 'string', required: true, message:'请输入退款时间'}],props: {type:'datetime',format:'yyyy-MM-dd',placeholder:'退款时间'},col:{span:6}}
                    ]


    },
     //导出
   async onExport() {
     if (this.onExporting) return;
     try{
        const params = {...this.pager,...this.filter}
        if (params.timerangetk) {
         params.StartDate = params.timerangetk[0];
         params.EndDate = params.timerangetk[1];
      }
      //  params.SuName=this.filter.SuName.join();
        var res= await exportReturnAmount(params);
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','退款单_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },


  async init(data){
      const params={
         SupplierName:data
      }
      var res2= await getAllSupplier(params);
      this.suppilelist = res2.data.map(item => {
          return { value: item.key, label: item.value };
      });
    },

     //下载付款信息导入模板
   downloadTemplate(){
        //window.open("../../static/excel/inventory/退款单导入模板.xlsx","_self");
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250414/1911668142953979905.xlsx', '退款单导入模板.xlsx');
    },
   async onSearch() {
       this.$refs.pager.setPage(1)
       this.getlist()
    },
   async getlist() {
      if (!this.pager.OrderBy) this.pager.OrderBy="";
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
     if (params.timerangetk) {
        params.startDate = params.timerangetk[0];
        params.endDate = params.timerangetk[1];
      }
      //  params.SuName=this.filter.SuName.join();
      this.listLoading = true
      const res = await getReturnList(params)
      this.listLoading = false
      if (!res?.success) return
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {d._loading = false})
      this.list = data
      this.summaryarry=res.data.summary;
    },
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    cancelImport() {
      this.dialogVisible = false;
    },
    beforeRemove() {
      return false;
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
   async submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit=true;
      this.uploadLoading=true;
      this.$refs.upload.submit();
    },
   async uploadFile(item) {
      if(!this.fileHasSubmit){
        return false;
      }
      this.fileHasSubmit=false;
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      const res =await importReturnAmount(form);
      if (res.success)   this.$message({ message: "上传成功,正在导入中...", type: "success" });

      else  this.$message({ message: res.msg, type: "warning" });

      this.uploadLoading=false;    },
   async uploadChange(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for(var i=0;i<fileList.length;i++){
          if(fileList[i].status=="success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.fileList = list;
      }
    },
    uploadRemove(file, fileList){
       this.uploadChange(file, fileList);
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   selsChange: function(sels) {
      this.sels = sels
    },
   selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.proBianMa);
      })
    },
  },
};
</script>


