<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%;" @tab-click="handoverEvent">
            <el-tab-pane label="数据明细" name="first" style="height: 100%;">
                <propDetails />
            </el-tab-pane>
            <el-tab-pane label="数据看板" name="second" style="height: 100%;" :lazy="true">
                <propView v-if="boardData"/>
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import propDetails from './components/propDetails.vue';
import propView from './components/propView.vue';
export default {
    components: {
        MyContainer, propDetails, propView,
    },
    data() {
        return {
            boardData: false,
            activeName: 'first'
        };
    },
    methods: {
      handoverEvent(e){
        this.$nextTick(() => {
          if(e.name == 'first'){
            this.boardData = false;
          } else if(e.name == 'second') {
            this.boardData = true;
          }
        });
      }

    }
};
</script>

<style lang="scss" scoped></style>
