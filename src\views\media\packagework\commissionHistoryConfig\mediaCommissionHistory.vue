<template>
    <!-- 历史版本 -->
    <my-container>
        <template #header>
            <el-select v-if="versionList.length>0" style="margin-right:5px;" v-model="versionId" filterable laceholder="选择版本" @change="verChange">
                <el-option v-for="item in versionList" :key="item.versionId" :label="item.versionName"
                    :value="item.versionId" />
            </el-select>
            <el-select v-else style="margin-right:5px;" v-model="noversionId" filterable laceholder="选择版本" @change="verChange">
                <el-option v-for="item in []" :key="item.versionId" :label="item.versionName"
                    :value="item.versionId" />
            </el-select>
            <el-button @click="deleteHis" type="danger">删除当前版本</el-button>
            <el-button v-if="checkPermission('api:packprocess:PackagesSetProcessing:RecycleBinPurview')" type="primary" @click="recycle">回收站</el-button>
            <div style="float: right; margin-right: 30px;margin-top:6px;font-size:14px;color:#999;">
                <span>归档人： {{ createdUserName }}</span> <span>归档时间： {{ versionTime }}</span>
            </div>
        </template>
        <el-tabs v-model="activeName" @tab-click="tabclick">
            <el-tab-pane label="薪资核算" name="tab0" :lazy="true" style="height:80vh;">
                <mediaTotalCommissionHistory ref="mediaTotalCommissionHistory" key="mediaTotalCommissionHistory" :historical="version"
                    :versionId="versionId">
                </mediaTotalCommissionHistory>
            </el-tab-pane>

            <el-tab-pane label="加工数据" name="tab1" :lazy="true" style="height:80vh;width: 100%;">
                <!-- <shootingvideotaskoverCacleHistory ref="shootingvideotaskoverCacleHistory"
                    key="shootingvideotaskoverCacleHistory" :versionId="versionId">
                </shootingvideotaskoverCacleHistory> -->
                <!-- :allsellist="allsellist" @refresh="refresh" -->
                <historystatisticslist :versionId="versionId" ref="refhistorystatisticslist"
                   style="height: 100%;"  tablekey="refstatisticslistgrid"  :lazy="true" :isCopy="isCopy" :isHistory="isHistory"/>
            </el-tab-pane>

            <!-- <el-tab-pane label="工作统计" name="tab2" :lazy="true" style="height:80vh;">
                <mediaWorkCommissionHistory ref="refmediaWorkCommissionHistory" key="mediaWorkCommissionHistory"
                    :versionId="versionId">
                </mediaWorkCommissionHistory>
            </el-tab-pane> -->

            <el-tab-pane label="卸货数据" name="tab2" :lazy="true" style="height:80vh">
                <unloadProps :type="type" :versionId="versionId"/>
            </el-tab-pane>

            <el-tab-pane label="移箱入库" name="tab3" :lazy="true" style="height:80vh">
                <moveStorage :type="type" :versionId="versionId"/>
            </el-tab-pane>

            <el-tab-pane label="计件一" name="tab4" :lazy="true" style="height:80vh;">
                计件一
            </el-tab-pane>

            <el-tab-pane label="计件二" name="tab5" :lazy="true" style="height:80vh;">
                计件二
            </el-tab-pane>

            <el-tab-pane label="其他计件" name="tab6" :lazy="true" style="height:80vh;">
                其他计件
            </el-tab-pane>




        </el-tabs>
        <el-dialog title="回收站" :visible.sync="recycleBindialog" width="40%"  v-dialogDrag style="margin-top: -5vh;">
          <div style="border-top: 1px solid #dcdfe6;height: 420px;padding-top: 10px;">
            <div v-for="(item, index) in recycleBinData" :key="index" class="flex-item">
              <div style="font-size: 15px;">
                {{ item.versionName }}
              </div>
              <div>
                <el-button type="primary" @click="editItem(item,1)">恢复</el-button>
                <el-button type="danger" @click="editItem(item,2)">删除</el-button>
              </div>
            </div>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <el-pagination layout="prev, pager, next" :total="recycleBintotal" @current-change="Pagechange"
              @size-change="Sizechange">
            </el-pagination>
          </div>
        </el-dialog>
    </my-container>
</template>
<script>
import historystatisticslist from '@/views/media/packagework/commissionHistoryConfig/historystatisticslist.vue';
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import MyContainer from "@/components/my-container";
import unloadProps from "../../packagework/unloadProps.vue";
import moveStorage from "../../packagework/moveStorage.vue";

import mediaWorkCommissionHistory from '@/views/media/packagework/commissionConfig/mediaWorkCommission.vue';

import mediaNCCommissionHistory from '@/views/media/packagework/commissionHistoryConfig/mediaCommissionHistory.vue';
// import mediaTotalCommissionHistory from '@/views/media/packagework/commissionHistoryConfig/mediaTotalCommissionHistory.vue';
import mediaTotalCommissionHistory from '@/views/media/packagework/commissionConfig/mediaTotalCommission.vue';
// import mediaWorkCommissionHistory from '@/views/media/packagework/commissionHistoryConfig/mediaWorkCommissionHistory.vue';
import shootingvideotaskoverCacleHistory from '@/views/media/packagework/commissionHistoryConfig/shootingvideotaskoverCacleHistory.vue';
import videotasknewover from '@/views/media/video/videotasknewover'
import microvediotaskmain from '@/views/media/shooting/microvedio/new/microvediotaskmain';
import directimgtaskmain from '@/views/media/shooting/directImg/new/directImgtaskmain';
import changeimgtaskmain from '@/views/media/shooting/changeImg/new/changeimgtaskmain';
import shopdecorationtaskmain from '@/views/media/shooting/shopdecoration/new/shopdecorationtaskmain';
import packdesgintask from '@/views/media/shooting/packdesgin/maintasklist/packdesgintask';
// import { getHistoryVersionInfo, delHistoryVersionInfo } from '@/api/media/shootingset';
import { getHistoryVersionInfo, delHistoryVersionInfo, getHistoryVersionInfoPage, restoreFromRecycleBin, delFromRecycleBin } from '@/api/inventory/packagesSetProcessing.js';


import { getShootOperationsGroup, getErpUserInfoView } from '@/api/media/mediashare';//主要获取配置的仓库
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'//erp运营组
import { rulePlatform } from "@/utils/formruletools";//平台
import { ShootingVideoTaskUrgencyOptions } from "@/utils/tools";//视觉部紧急程度
export default {
    components: {
        MyContainer, vxetablebase, mediaTotalCommissionHistory,
        historystatisticslist, mediaWorkCommissionHistory,unloadProps,moveStorage
    },
    props: {
        isHistory: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            version:3,//区分历史版本打分页和薪资核算打分页
            isCopy:true,
            type: false,
            versionId: '0',
            noversionId: null,
            versionName: "",
            versionTime: null,
            createdUserName: null,
            that: this,
            listLoading: false,
            activeName: 'tab0',
            tab0isfirst: true,
            tab1isfirst: true,
            tab2isfirst: true,
            tab3isfirst: true,
            tab4isfirst: true,
            tab5isfirst: true,
            tab6isfirst: true,
            menuview: 1,
            versionList: [],
            platformList: [],//平台
            warehouselist: [],//仓库
            groupList: [],//运营组
            userList: [],//erp用户
            userList2: [],//erp用户
            recycleBindialog: false,
            recycleBinData: [],
            recycleBintotal: 0,
            recycleBinlist:{
              currentPage: 1,
              pageSize: 10,
              orderBy: null,
              isAsc: false,
              recycleBin: 1,
            },
            activetypelist: [],
            taskUrgencyList: ShootingVideoTaskUrgencyOptions  // 紧急程度

        };
    },
    //向子组件注册方法
    provide() {
        return {
        }
    },
    async created() {
        await this.getDropDownList();
    },
    async mounted() {
        //await this.initVersionInfo();
    },
    methods: {
        //每页数量改变
        Sizechange(val) {
          this.recycleBinlist.currentPage = 1;
          this.recycleBinlist.pageSize = val;
          this.recycle()
        },
        //当前页改变
        Pagechange(val) {
          this.recycleBinlist.currentPage = val;
          this.recycle()
        },
        editItem(item, val) {
          let action = val === 1 ? '恢复' : '删除';
          let message = val === 1 ? `确定恢复“${item.versionName}”` : `确定删除“${item.versionName}”`;
          this.$confirm(message, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async() => {
            if(val == 1){
              var {success} = await restoreFromRecycleBin({ versionId: item.versionId })
            }else{
              var {success} = await delFromRecycleBin({ versionId: item.versionId});
            }
            if(success){
              this.$message({ type: 'success', message: `${action}成功!`});
              this.recycle()
              this.initVersionInfo()
            }else{
              this.$message({ type: 'error', message: `${action}失败!`});
            }
          }).catch(() => {
          });
        },
        async recycle(){
          const{data,success} = await getHistoryVersionInfoPage(this.recycleBinlist);
          if (success) {
            this.recycleBinData = data.list;
            this.recycleBintotal = data.total;
          }
          this.recycleBindialog = true
        },
        async getDropDownList() {
            //仓库
            var res = await getShootOperationsGroup({ type: 3 });
            this.warehouselist = res?.map(item => { return { value: item.id, label: item.label }; });
            //包装类型
            var res = await getShootOperationsGroup({ type: 12 });
            this.packclasslist = res?.map(item => { return { value: item.id, label: item.label }; });
            //品牌
            var res = await getShootOperationsGroup({ type: 13 });
            this.brandList = res?.map(item => { return { value: item.id, label: item.label }; });
            //平台
            var pfrule = await rulePlatform();
            this.platformList = pfrule.options;
            //运营组
            var res = await getDirectorGroupList();
            this.groupList = res.data?.map(item => { return { value: item.key, label: item.value }; });
            var res = await getShootOperationsGroup({ type: 9 });
            this.activetypelist = res?.map(item => { return { value: item.id, label: item.label }; });

            //erp用户
            var res = await getErpUserInfoView();
            this.userList = res || [];
            this.userList2 = res?.map(item => { return item.label; });;
        },
        async deleteHis() {
            this.$confirm("将进行删除操作，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await delHistoryVersionInfo({ versionId: this.versionId });
                if (res?.success) {
                    this.$message({ message: this.$t('操作成功'), type: 'success' });
                    this.initVersionInfo();
                }
            });



        },
        async initVersionInfo() {
            this.versionList = await getHistoryVersionInfo();
            if (this.versionList.length > 0) {
                this.versionName = this.versionList[0].versionName;
                this.versionId = this.versionList[0].versionId;
                this.versionTime = this.versionList[0].versionTime;
                this.createdUserName = this.versionList[0].createdUserName;

            }
        },
        async verChange(val) {
            for (let num in this.versionList) {
                if (this.versionList[num].versionId == val) {
                    this.versionName = this.versionList[num].versionName;
                    this.versionId = this.versionList[num].versionId;
                    this.versionTime = this.versionList[num].versionTime;
                    this.createdUserName = this.versionList[num].createdUserName;
                }
            }
            //同步刷新列表
            await this.$nextTick(() => {
                var toatla = this.$refs;

                if(this.$refs.mediaTotalCommissionHistory)
                this.$refs.mediaTotalCommissionHistory.tasklist = [];

                if(this.$refs.shootingvideotaskoverCacleHistory)
                this.$refs.shootingvideotaskoverCacleHistory.tasklist = [];

                if(this.$refs.videotasknewoverHistory)
                this.$refs.videotasknewoverHistory.tasklist = [];

                if(this.$refs.microvediotaskmainHistory)
                this.$refs.microvediotaskmainHistory.tasklist = [];

                if(this.$refs.directimgtaskmainHistory)
                this.$refs.directimgtaskmainHistory.tasklist = [];

                if(this.$refs.changeimgtasktaskHistory)
                this.$refs.changeimgtasktaskHistory.tasklist = [];

                if(this.$refs.shopdecorationtaskHistory)
                this.$refs.shopdecorationtaskHistory.tasklist = [];

                if(this.$refs.packdesgintasktaskHistory)
                this.$refs.packdesgintasktaskHistory.tasklist = [];

                if(this.$refs.mediaYYCommissionHistory)
                this.$refs.mediaYYCommissionHistory.tasklist = [];

                if(this.$refs.mediaNCCommissionHistory)
                this.$refs.mediaNCCommissionHistory.tasklist = [];

                if(this.$refs.mediaWorkCommissionHistory)
                this.$refs.mediaWorkCommissionHistory.tasklist = [];
            });
        },
        async tabclick() {
            switch(this.activeName){
                //薪资核算-历史
                case 'tab0' :
                    if(this.tab0isfirst){
                        this.$refs.mediaTotalCommissionHistory.onSearch();
                        this.tab0isfirst =false;
                    }
                    break;
                //加工数据-历史
                case 'tab1' :
                    if(this.tab1isfirst){
                        // await this.$nextTick(() =>{  this.$refs.refhistorystatisticslist.onSearch(); });
                        this.tab1isfirst =false;
                    }
                    break;
                // //工作统计-历史
                // case 'tab2' :
                //     if(this.tab2isfirst){
                //         await this.$nextTick(() =>{  this.$refs.mediaWorkCommissionHistory.onSearch(); });

                //     }
                //     this.tab2isfirst =false;
                //     break;
                // //道具统计-历史
                // case 'tab3' :
                //     if(this.tab3isfirst){
                //         this.tab3isfirst =false;
                //     }
                //     break;
                // //义乌提成-历史
                // case 'tab4' :
                //     if(this.tab4isfirst){
                //         await this.$nextTick(() =>{
                //             this.$refs.mediaYYCommissionHistory.onSearch();
                //         })
                //         this.tab4isfirst =false;
                //     }
                //     break;
                // //南昌提成-历史
                // case 'tab5' :
                //     if(this.tab5isfirst){
                //         await this.$nextTick(() =>{
                //             this.$refs.mediaNCCommissionHistory.onSearch();
                //         })
                //         this.tab5isfirst =false;
                //     }
                //     break;
                //加工调入调出-历史
                // case 'tab2' :
                //     if(this.tab6isfirst){
                //         await this.$nextTick(() =>{
                //             this.$refs.refmediaWorkCommissionHistory.onSearch();
                //         })
                //         this.tab6isfirst =false;
                //     }
                //     break;
            }
        }
    },
};
</script>
<style lang="scss" scoped>
.content {
    display: flex;
    flex-direction: row;
}

.flex-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  min-height: 40px;
}
::v-deep .vxe-table--header {
    width: 500px !important;
}
</style>

