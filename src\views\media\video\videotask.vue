1addForm
<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作  -->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='false' :hasexpand='false' @sortchange='sortchange' :tableData='tasklist' @select='selectchange' :isSelection='true' :tableCols='tableCols' :loading="listLoading" :summaryarry="summaryarry">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;width: 150px;">
                        <el-input v-model="Filter.videoTaskId" style="padding: 0;width: 130px;" type="number" placeholder="任务编号" clearable></el-input>
                    </el-button>
                    <el-button style="padding: 0;width: 100px;">
                        <el-input v-model="Filter.productId" placeholder="产品Id" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                    <el-button style="padding: 0;width: 155px;">
                        <el-input v-model="Filter.productShortName" placeholder="产品简称" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                    <el-button style="padding: 0;width: 100px;">
                        <el-select v-model="Filter.isDelivered" :clearable="true" :collapse-tags="true" filterable placeholder="是否到样">
                            <el-option key="0" label="否" value="0"></el-option>
                            <el-option key="1" label="是" value="1"></el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;width: 100px;">
                        <el-select style="width:100%" v-model="Filter.taskUrgency" :clearable="true" filterable placeholder="紧急程度">
                            <el-option v-for="item in taskUrgencyList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;width: 100px;">
                        <el-input v-model="Filter.cuteLqName" placeholder="负责人" @keyup.enter.native="onSearch" clearable />
                    </el-button>

                    <el-button style="padding: 0;width: 100px;">
                        <el-input v-model="Filter.dockingPeopleStr" placeholder="分配拍摄" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onAddTask">新建任务</el-button>
                    <el-button style=" margin-left: 1; padding: 0 1 0 1 ;width: 100px;" type="primary">
                        <el-dropdown @command="handleCommand">
                            <span class="el-dropdown-link" style="font-size: 14;color: #fff;">
                                批量操作<i class="el-icon-arrow-down el-icon--right"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="a">下单发货</el-dropdown-item>
                                <el-dropdown-item command="b">分配拍摄</el-dropdown-item>
                                <el-dropdown-item command="c">运营小组</el-dropdown-item>
                                <el-dropdown-item command="d">内部单号</el-dropdown-item>
                                <el-dropdown-item command="e">快递单号</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </el-button>
                    <el-button type="primary" @click="onExeprotVedioTask"  v-if="checkPermission('api:media:vediotask:ExportVedioTaskReport')" >导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList" />
        </template>
        <!-- 新建任务 -->
        <el-dialog :title="taskPageTitle" :visible.sync="addTask" width="70%" :close-on-click-modal="false" element-loading-text="拼命加载中" @close="onCloseAddForm" :v-loading="addLoading" v-dialogDrag>
            <span>
                <el-form :model="addForm" ref="addForm" label-width="120px" :rules="addFormRules" :disabled="islook">
                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="平台:">
                                <el-select v-model="addForm.platform" :clearable="true" :collapse-tags="true" filterable :disabled="true">
                                    <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-row>
                                <el-col :span="20">
                                    <el-form-item label="产品ID" prop="productId">
                                        <el-input style="width:100%" :clearable="true" v-model="addForm.productId" :disabled="true"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-button @click="onSelctProduct" style="float: right ;font-size:14px" type="text">
                                        选择</el-button>
                                </el-col>
                            </el-row>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="店铺" prop="shopName">
                                <el-input style="width:100%" :clearable="true" v-model="addForm.shopName" :disabled="true"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="产品简称" prop="productShortName">
                                <el-input style="width:100%" :clearable="true" v-model="addForm.productShortName" :disabled="addForm.audioStatus>=1"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="24">
                            <el-row>
                                <el-col :span="23">
                                    <el-form-item label="商品编码" prop="goodCode">
                                        <el-input style="width:100%" :clearable="true" v-model="addForm.goodCode" :readonly="true">
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="1">
                                    <el-button @click="onSelctCp" style="float: right ;font-size:14px" type="text"> 选择
                                    </el-button>
                                </el-col>
                            </el-row>
                        </el-col>

                    </el-row>

                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="紧急程度" prop="taskUrgency">
                                <el-select style="width:100%" v-model="addForm.taskUrgency" :clearable="true" filterable :disabled="addForm.audioStatus>=1">
                                    <el-option v-for="item in taskUrgencyList" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="内部订单号" prop="shopName">
                                <el-input style="width:100%" :clearable="true" v-model="addForm.sampleRrderNo">
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="快递单号" prop="shopName">
                                <el-input style="width:100%" :clearable="true" v-model="addForm.sampleExpressNo">
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="日期" prop="taskDate">
                                <el-date-picker style="width:100%" v-model="addForm.taskDate" :disabled="addForm.audioStatus>=1" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>

                    </el-row>
                    <el-row>
                        <el-col :span="6">
                            <el-form-item prop="isDelivered" label="是否到样">
                                <el-radio-group v-model="addForm.isDelivered">
                                    <el-radio-button label="1">是</el-radio-button>
                                    <el-radio-button label="0">否</el-radio-button>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item prop="isReissue" label="是否补发">
                                <el-radio-group v-model="addForm.isReissue">
                                    <el-radio-button label="1">是</el-radio-button>
                                    <el-radio-button label="0">否</el-radio-button>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item prop="warehouse" label="仓库" :span="12">
                                <el-select v-model="addForm.warehouse" placeholder="请选择仓库" style="width:100%;" :disabled="addForm.audioStatus>=1">
                                    <el-option v-for="item in warehouseList" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6">
                            <el-form-item prop="operationsGroup" label="运营组" :span="12">
                                <el-select v-model="addForm.operationsGroup" placeholder="请选择" style="width:100%;">
                                    <el-option v-for="item in operationsGroupList" :key="item.label" :label="item.label" :value="item.label" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <!--     <el-col :span="6">
                            <el-form-item prop="warehouse" label="对接人" :span="12">
                                <el-input style="width:100%" :clearable="true" v-model="addForm.dockingPeopleStr"> </el-input>
                            </el-form-item>
                        </el-col> -->

                        <el-col :span="6">
                            <el-form-item label="到货日期" prop="arrivalDate">
                                <el-date-picker style="width:100%" v-model="addForm.arrivalDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="备注" prop="remark">
                                <el-input type="textarea" :rows="3" v-model="addForm.remark" placeholder="请输入备注" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-col :xs="24" :sm="24" :lg="24" v-if="addForm.videoTaskId>0">
                        <el-card class="box-card" style="width:100% ;height: 45px; overflow: hidden;">
                            <div slot="header" class="clearfix" style="">
                                <span>视频片段</span>
                                <span style="float:right">
                                    <el-button type="primary" @click="onAddpdArray()">增加一行</el-button>
                                </span>
                            </div>
                        </el-card>
                        <el-card class="box-card" style="width:100% ;height: 350px; overflow: auto;">
                            <div class="block">
                                <el-table :data="addForm.pdArray" highlight-current-row>
                                    <el-table-column label="参考" width="120">
                                        <template slot-scope="scope">
                                            <el-select v-model="scope.row.ckVideoIndex" placeholder="请选择" :disabled="IsPDreading(scope.row.ckVideoIndex,addForm)" @change="pdChanged">
                                                <el-option v-for="item in pdseloptions" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled">
                                                </el-option>
                                            </el-select>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="name" label="备注" align="center" width="120">
                                        <template slot-scope="scope">
                                            <el-input v-model="scope.row.title" placeholder="备注" :disabled="IsPDreading(scope.row.ckVideoIndex,addForm)">
                                            </el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="name" label="场景" align="center" width="150">
                                        <template slot-scope="scope">
                                            <el-cascader v-model="scope.row.firstSceneIds" :options="taskFirstSceneList" @change="handleChange" :disabled="IsPDreading(scope.row.ckVideoIndex,addForm)">
                                            </el-cascader>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="name" label="片段链接" align="center">
                                        <template slot-scope="scope">
                                            <el-input v-model="scope.row.url" placeholder="片段链接" :disabled="true">
                                            </el-input>
                                        </template>
                                    </el-table-column>

                                    <el-table-column lable="操作" width="200px">
                                        <template slot-scope="scope">
                                            <el-button type="primary" :disabled="IsPDreading(scope.row.ckVideoIndex,addForm)" @click="onUploadVideoPd(scope.$index)">上传片段
                                            </el-button>
                                            <el-button type="danger" :disabled="IsPDreading(scope.row.ckVideoIndex,addForm)" @click="onDelpdArray(scope.$index)">移除<i class="el-icon-remove-outline"></i>
                                            </el-button>

                                        </template>
                                    </el-table-column>
                                </el-table>

                            </div>
                        </el-card>

                    </el-col>
                </el-form>
            </span>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="addTask = false">取 消</el-button>
                    <my-confirm-button type="submit" :validate="onSubmitValidate" @click="onSubmit" v-show="!islook" />
                </span>
            </template>
        </el-dialog>

        <!--选择商品-->
        <el-dialog title="选择产品" :visible.sync="productVisible" width='90%' height='500px' v-dialogDrag>
            <productselect :ischoice="true" ref="productselect" style="z-index:10000;height:500px" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="productVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onQuerenProduct()">确 定</el-button>
                </span>
            </template>
        </el-dialog>

        <!--选择商品-->
        <el-dialog title="选择商品编码" :visible.sync="goodschoiceVisible" width='88%' height='500px' v-dialogDrag>
            <goodschoice :ischoice="true" ref="goodschoice" style="z-index:10000;height:500px" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="goodschoiceVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onQueren()">确 定</el-button>
                </span>
            </template>
        </el-dialog>

        <!--视频播放-->
        <el-dialog title="已剪切视频预览" :visible.sync="yjqdialogVisible" width="60%" @close="closeYjqVideo">
            <taskcutevideolist v-if="yjqdialogReload" ref="taskcutevideolist" @playVideo="playVideo" :taskid='videotaskid' :ckindex='cankaoindex' />
            <span slot="footer" class="dialog-footer">
                <el-button @click="yjqdialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <!--视频播放-->
        <el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer" v-dialogDrag>
            <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <!--上传片段-->
        <el-dialog title="上传片段" :visible.sync="uploadpdVisibleSyj" width="40%" :v-loading="ScpdLoading" @close="closedialogVisibleScpd">

            <el-progress v-if="showProgress" :text-inside="true" :stroke-width="26" :percentage="percentage"></el-progress>
            <span>
                <el-upload ref="upload3" class="upload-demo" :auto-upload="false" :http-request="uploadpdFile" action="#" accept=".mp4,.mov,.vedio" :on-success="uploadpdSuccess" :limit="1" :file-list="SypdjfileList">
                    <template #trigger>
                        <el-button size="small" :loading="ScpdLoading" type="primary">选取文件</el-button>
                    </template>
                    <my-confirm-button style="margin-left: 10px;" size="small" type="success" :loading="ScpdLoading" @click="onPdSubmitupload">
                        上传</my-confirm-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closedialogVisibleScpd">关闭</el-button>
            </span>
        </el-dialog>
        <!--上传视频-->
        <el-dialog title="上传视频" :visible.sync="dialogVisibleSyj" width="65%" :show-close="false">
            <!--上传进度条-->

            <el-card class="box-card" style="width:100% ;height: 500px; overflow: auto;">
                <div class="block">
                    <my-container style="height: 450px">

                        <el-table :height="450" ref="MediamultipleTable" row-key="id" :data="selPdArray" default-expand-all :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" highlight-current-row>
                            <el-table-column label="参考视频" width="120">
                                <template slot-scope="scope">
                                    <span v-if="scope.row.parent==0"> 参考视频{{scope.row.ckVideoIndex}}</span>
                                    <span v-else> {{scope.row.title}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="name" label="剪辑片段" width="80">
                                <template slot-scope="scope">
                                    <div style="position: relative;" v-if="scope.row.parent>0">
                                        <el-image :src="scope.row.imgPath" style="max-width: 50px; max-height: 50px;" fit="fill" :lazy="true"></el-image>
                                        <span style="display: block;position: absolute;top: 10px;left: 10px;">
                                            <a size="mini" class="el-link el-link--primary is-underline" @click="playVideo(scope.row.videoPath)" style="margin-left: 3px;color:#ccc;font-size: 23px;">
                                                <i class="el-icon-video-play"></i>
                                            </a>
                                        </span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="已上传数" width="120">
                                <template slot-scope="scope">
                                    {{scope.row.uploadCounts}}
                                </template>
                            </el-table-column>
                            <el-table-column prop="senceName" label="场景" width="100">
                            </el-table-column>
                            <el-table-column prop="remark" label="备注" width="200">
                            </el-table-column>
                            <el-table-column prop="remark" label="操作">
                                <template slot-scope="scope">
                                    <el-upload v-if="scope.row.parent>0 && scope.row.videoType!=2" class="upload-demo" :multiple="true" :file-list="scope.row.fileList" accept=".mp4,.mov,.vedio" action="#" :http-request=" (file) => {return uploadFiletemp(file, scope.row);} " :on-remove="(file) => {return uploadFileReomveTemp(file, scope.row);} " :show-file-list="true">
                                        <el-button type="text" size="small">选择文件</el-button>
                                    </el-upload>
                                </template>
                            </el-table-column>
                        </el-table>
                    </my-container>
                </div>

            </el-card>
            <span>{{upmsginfo}} </span>
            <el-progress v-if="showProgress" :text-inside="true" :stroke-width="26" :percentage="percentage"></el-progress>
            <el-button v-if="hasfailInfo" type="primary" @click="dialogVisibleSyjout=true">查看失败信息</el-button>
            <span slot="footer" class="dialog-footer">
                <my-confirm-button style="margin-left: 10px;" size="small" type="primary" :loading="ScpdLoading" @click="onSubmitupload2">
                    提交 </my-confirm-button>
                <el-button style="margin-left: 10px;" @click="closedialogVisibleSyj">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="上传视频结果" :visible.sync="dialogVisibleSyjout" width="50%" @close="dialogVisibleSyjout">
            <my-container style="height: 450px">
                <el-table :height="450" ref="MediamultipleTableout" row-key="id" :data="uploadFiletempList" highlight-current-row>
                    <el-table-column label="参考视频" width="150">
                        <template slot-scope="scope">
                            <span> 参考视频{{scope.row.ckVideoIndex}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="剪辑片段" width="150">
                        <template slot-scope="scope">
                            <span> {{scope.row.title}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="失败文件" width="200">
                        <template slot-scope="scope">
                            {{scope.row.file.name}}
                        </template>
                    </el-table-column>
                </el-table>
            </my-container>
        </el-dialog>
        <el-drawer title="视频剪辑" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editVideoVisible" direction="btt" size="600px" style="position:absolute;overflow:hidden;" @close="closeCuteForm">
            <section>
                <videoplay v-if="videoplayReload" :taskGridFirstSceneList="taskGridFirstSceneList" :referenceVideoList="referenceVideoList" ref="videoplay" :videopath="videopath" :videoduration="videoduration" :videoid='videoid' style="height: 80%;width:100%" />
            </section>
            <div class="drawer-footer">
                <el-button @click.native="editVideoVisible = false">取消</el-button>
                <my-confirm-button type="submit" @click="onCutVideoSubmit" />
            </div>
        </el-drawer>
        <el-drawer title="上传视频审核" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="uploadVideoAudioVisible" direction="btt" size="'auto'" style="position:absolute;overflow:hidden" @close="closeVideoAudioCuteForm">
            <section>
                <videotaskaudio v-if="uploadVideoAudioReload" @playVideo="playVideo" :cardHeight="'500px'" :islook="false" ref="videotaskaudio" :videoTaskId='videotaskid' style="height: 80%;width:100%" />
            </section>
            <div class="drawer-footer">
                <el-button @click.native="uploadVideoAudioVisible = false">取消</el-button>
                <my-confirm-button type="submit" @click="onVideoAudioSubmit" />
            </div>
        </el-drawer>
        <el-drawer title="审核记录" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="videVideoAudioVisible" direction="btt" size="'auto'" style="position:absolute;overflow:hidden" @close="closeViewVideoAudioForm">
            <section>
                <videotaskaudio v-if="videVideoAudioReload" :cardHeight="'600px'" :islook="true" ref="videotaskaudio" :videoTaskId='videotaskid' style="height: 80%;width:100%" />
            </section>
            <div class="drawer-footer">
                <el-button @click.native="videVideoAudioVisible = false">关闭</el-button>
            </div>
        </el-drawer>

        <el-dialog title="下单发货" :visible.sync="dialogAddOrderVisible" width="50%" @close="closeAddOrder" v-dialogDrag v-loading="dialogAddOrderLoading" :close-on-click-modal="false">
            <template>
                <el-form class="ad-form-query" :model="addOrderForm" ref="addOrderForm" :rules="addOrderFormRules" label-position="right" label-width="100px">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="19" :xl="19">
                            <el-form-item label="已选任务编号:">
                                <span v-for="(item, index) in selVideoTaskIdSpanList" :key="index" v-html="item"></span>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="5" :xl="5">
                            <el-form-item label="">
                                <el-button @click="onAddressSet" type="text">发货地址维护
                                </el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :hidden="true">
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                            <el-form-item label="收货人" prop="receiverName">
                                <el-input v-model="addOrderForm.receiverName" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                            <el-form-item label="收货电话" prop="receiverPhone">
                                <el-input v-model="addOrderForm.receiverPhone" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item label="拿样方式" prop="isZt">
                                <el-radio v-model="addOrderForm.isZt" label="1">仓库自提</el-radio>
                                <el-radio v-model="addOrderForm.isZt" label="0">快递寄样</el-radio>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item prop="calculateUnit" label="核算单位">
                                <el-select v-model="addOrderForm.calculateUnit" placeholder="请选择核算单位" style="width:100%;">
                                    <el-option v-for="item in calculateUnitlist" :key="item" :label="item"
                                        :value="item" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="addOrderForm.isZt!=1">
                            <el-form-item prop="warehouse" label="自提仓库">
                                <el-select v-model="addOrderForm.warehouse" placeholder="请选择自提仓库" style="width:100%;">
                                    <el-option v-for="item in warehouseList" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" :hidden="addOrderForm.isZt!=0">
                            <el-form-item label="详细地址" prop="receiverAddressAllInfo">
                                <el-select v-model="addOrderForm.receiverAddressAllInfo" placeholder="选择详细地址" style="width:100%;" @change="receiverAddressSelChange">
                                    <el-option v-for="item in receiverAddressList" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                            <el-form-item label="收货省" prop="receiverStateCode">
                                <el-select v-model="addOrderForm.receiverStateCode" placeholder="收货省" style="width:100%;" @change="receiverStateChange">
                                    <el-option v-for="item in receiverStateList" :key="item.code" :label="item.name" :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                            <el-form-item label="收货市" prop="receiverCityCode">
                                <el-select v-model="addOrderForm.receiverCityCode" placeholder="收货市" style="width:100%;" @change="receiverCityChange">
                                    <el-option v-for="item in receiverCityList" :key="item.code" :label="item.name" :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="true">
                            <el-form-item label="收货区" prop="receiverDistrictCode">
                                <el-select v-model="addOrderForm.receiverDistrictCode" placeholder="收货区" style="width:100%;">
                                    <el-option v-for="item in receiverDistrictList" :key="item.code" :label="item.name" :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" :hidden="true">
                            <el-form-item label="详细地址" prop="receiverAddress">
                                <el-input v-model="addOrderForm.receiverAddress" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="备注" prop="remark">
                                <el-input v-model="addOrderForm.remark" type="textarea" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </template>
            <template>
                <el-row>
                    <el-card class="box-card" style="width:100% ;height: 45px;overflow: hidden;">
                        <div slot="header" class="clearfix">
                            <span>商品明细</span>
                            <el-button @click="onSelctOrderGoods()" style="float: right; padding: 3px 0" type="text">添加商品明细</el-button>
                        </div>
                    </el-card>
                    <el-card class="box-card" style="width:100% ;height: 300px;overflow: auto;">
                        <el-table :data="addOrderForm.orderGoods">
                            <el-table-column label="序号" width="50">
                                <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                            </el-table-column>
                            <el-table-column prop="goodsCode" width="150" label="商品编码" />
                            <el-table-column prop="goodsName" label="商品名称" />
                            <el-table-column prop="goodsPrice" width="100" label="单价" v-if="false" />
                            <el-table-column prop="goodsQty" width="150" label="数量">
                                <template slot-scope="scope">
                                    <el-input-number v-model="scope.row.goodsQty" :min="1" :max="100000000" placeholder="数量" :precision="0" @change="addOrderFormGoodsQtyChange(scope.row)">
                                    </el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column prop="goodsAmount" width="100" label="总额" v-if="false" />
                            <el-table-column lable="操作" width="100">
                                <template slot-scope="scope">
                                    <el-button type="danger" @click="onDelDtlGood(scope.$index)">删除 <i class="el-icon-remove-outline"></i>
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-card>
                </el-row>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <span style="font-size:10px;color:red;">点击提交按钮将发起钉钉审批，自提单审批通过即代表到样，非自提单审批通过则自动同步订单到聚水潭。&nbsp;&nbsp;</span>
                    <el-button @click="dialogAddOrderVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :validate="addOrderFormValidate" :loading="dialogAddOrderSubmitLoding" @click="onAddOrderSave">
                        提交
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>

        <!--选择下单任务的商品-->
        <el-dialog title="选择商品编码" :visible.sync="orderGoodschoiceVisible" width='85%' height='500px' v-dialogDrag :close-on-click-modal="false">
            <goodschoice :ischoice="true" ref="orderGoodschoice" style="z-index:10000;height:500px" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="orderGoodschoiceVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onQuerenOrderGoods()">确 定</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="收货地址维护" :visible.sync="dialogAddressVisible" width='50%' height='400px' v-dialogDrag :close-on-click-modal="false">
            <template>
                <el-form class="ad-form-query" :model="addAddressForm" ref="addAddressForm" :rules="addAddressFormRules" label-position="right" label-width="100px">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item label="收货人" prop="receiverName">
                                <el-input v-model="addAddressForm.receiverName" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item label="收货电话" prop="receiverPhone">
                                <el-input v-model="addAddressForm.receiverPhone" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item label="收货省" prop="receiverStateCode">
                                <el-select v-model="addAddressForm.receiverStateCode" placeholder="收货省" style="width:100%;" @change="receiverStateChange2">
                                    <el-option v-for="item in receiverStateList" :key="item.code" :label="item.name" :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item label="收货市" prop="receiverCityCode">
                                <el-select v-model="addAddressForm.receiverCityCode" placeholder="收货市" style="width:100%;" @change="receiverCityChange2">
                                    <el-option v-for="item in receiverCityList2" :key="item.code" :label="item.name" :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                            <el-form-item label="收货区" prop="receiverDistrictCode">
                                <el-select v-model="addAddressForm.receiverDistrictCode" placeholder="收货区" style="width:100%;">
                                    <el-option v-for="item in receiverDistrictList2" :key="item.code" :label="item.name" :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="详细地址" prop="receiverAddress">
                                <el-input v-model="addAddressForm.receiverAddress" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <ces-table ref="addresstable" :that='that' :isIndex='true' :hasexpand='false' :isSelectColumn="false" style="height:350px" :tableData='addressList' :tableCols='addressTableCols' :loading="addressListLoading">
                            </ces-table>
                        </el-col>
                    </el-row>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogAddressVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :loading="dialogAddAddressSubmitLoding" @click="onAddAddressSave">
                        提交
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>

        <!--分配拍摄-->
        <el-dialog title="分配拍摄" :visible.sync="dialogfppcVisible" width='30%' height='200px' v-dialogDrag :close-on-click-modal="false">
            <template>
                <el-form class="ad-form-query" label-position="right" label-width="120px">
                    <el-form-item label="已选任务编号：">
                        <div>
                            <span v-for="(item, index) in selVideoTaskIdSpanList" :key="index" v-html="item"></span>
                        </div>
                    </el-form-item>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="24">
                            <el-form-item label="分配拍摄:">
                                <el-select style="width:100%" v-model="plAssignshootingName" filterable :clearable="true">
                                    <el-option v-for="item in erpUserInfoList" :key="item.id" :label="item.label" :value="item.id" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogfppcVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :validate="addAssignshootingValidate" :loading="dialogAddOrderSubmitLoding" @click="onAssignshootingSave">
                        提交
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="任务下单记录" :visible.sync="dialogOrderDtlVisible" width='88%' v-dialogDrag :close-on-click-modal="false" :v-loading="dialogOrderDtlLoading" @close="dialogOrderDtlColsed">
            <el-row style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black;margin-bottom: 2px;">
                <span>下单发货信息：</span>
            </el-row>
            <el-container>
                <el-main style="height:192px;">
                    <ces-table ref="tablexdfhmain" :that='that' :isIndex='true' :hasexpand='true' :tableData='xdfhmainlist' :tableCols='xdfhmainTableCols' :loading="xdfhmainLoading" style="height:190px" :isSelectColumn="false" @cellclick="onxdfhmainCellClick">
                    </ces-table>
                </el-main>
            </el-container>
            <el-row style="height:35px;line-height:35px; font-size: 16px;font-weight: bold;color: black; margin-top: 10px;margin-bottom: 2px;">
                <span>下单发货商品明细信息：</span>
            </el-row>
            <el-container>
                <el-main style="height:262px;">
                    <ces-table ref="tablexdfhdtl" :that='that' :isIndex='true' :hasexpand='true' :tableData='xdfhdtllist' :tableCols='xdfhdtlTableCols' :loading="xdfhdtlLoading" style="height:260px" :isSelectColumn="false">
                    </ces-table>
                </el-main>
            </el-container>
        </el-dialog>

        <el-drawer title="物流跟踪" :visible.sync="drawervisible" direction="rtl" :append-to-body="true">
            <logistics ref="logistics"></logistics>
        </el-drawer>

        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag>
            <orderLogPage ref="orderLogPage" :orderNoInner="sendOrderNoInner" style="z-index:10000;height:600px" />
        </el-dialog>
    </my-container>
</template>
<script>
    import checkPermission from '@/utils/permission';
    import videoplay from '@/views/media/video/videoplay'
    import taskcutevideolist from '@/views/media/video/taskcutevideolist'
    //播放器
    import videoplayer from '@/views/media/video/videoplayer'
    import videotaskaudio from '@/views/media/video/videotaskaudio'
    import { getList as getshopList } from '@/api/operatemanage/base/shop'
    import { rulePlatform } from "@/utils/formruletools";
    import cesTable from "@/components/Table/table.vue";
    import { formatTime, listToTree } from "@/utils";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import {
        pageViewTaskAsync, getTaskUrgencyList, assignshootingSave, getSceneListTree, addOrUpdateVideoTaskAsync,
        deleteVideoTaskAsync, pickVideoTaskAsync, unPickVideoTaskAsync, saveUploadTeskVideoAsync, uplodPdVideoAsync,
        getVideoTaskPdList, complatVideoTask, getCityAllData, vedioTaskAddOrderSave, vedioTaskAddOrderSaveCheckTaskIds, getVedioTaskOrderListById,
        getVedioTaskOrderAddressList, saveVedioTaskOrderAddress, deleteVedioTaskOrderAddress,exportVedioTaskReport
    } from '@/api/media/vediotask';
    import { cuteVideo, getTaskCuteVideoList, videoTaskAudioAsync } from '@/api/media/video'
    import { getErpUserInfoView, getOperationsGroup } from '@/api/media/mediashare'
    import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
    import goodschoice from "@/views/base/goods/goods3.vue";
    import productselect from "@/views/operatemanage/base/productselect";
    import logistics from '@/components/Comm/logistics'
    import orderLogPage from "@/views/order/logisticsWarning/orderLogPage.vue";

    const tableCols = [
        { istrue: true, prop: 'videoTaskId', label: '任务编号', width: '50', sortable: 'custom', fixed: true },
        { istrue: true, prop: 'productShortName', label: '产品简称', width: '200', fixed: true },
        { istrue: true, fixed: true, prop: 'warehouse',   label: '仓库', width: '50' , formatter:(row) =>row.warehouse==1?"义乌":
                                                                                                        row.warehouse==2?"南昌":
                                                                                                        row.warehouse==3?"南昌":""
        },
        { istrue: true, prop: 'cuteLqName', dingdingcode: 'cuteLqNameDDingUserId', label: '负责人', width: '80', type: 'ddingtalk', fixed: true },
        {
            istrue: true, type: "button", width: "50", label: '剪切领取',
            btnList: [
                { label: "认领", ishide: (that, row) => that.IsNotPicking(row, 0), handle: (that, row) => that.pickTask(row, 0) },
                { label: "取消", ishide: (that, row) => !that.IsNotPicking(row, 0), handle: (that, row) => that.unPickTask(row, 0) }
            ]
        },


        /*  {
             istrue: false, prop: 'taskDate', label: '日期', width: '100', sortable: 'custom',
             formatter: (row) => { if (!row.taskDate) { return '' } else { return formatTime(row.taskDate || '', 'YY年MM月DD日') } }
         }, */
        { istrue: true, prop: 'goodCode', label: '产品编码', width: '100' },

        {
            istrue: true, prop: 'taskUrgency', label: '紧急程度', width: '50',
            type: 'html',
            formatter: (row) => row.taskUrgency == 9 ? `<div>${row.taskUrgencyName}</div>` :
                (row.taskUrgency == 2 ? `<div style="color:#f59e1a">${row.taskUrgencyName}</div>` :
                    (row.taskUrgency == 1 ? `<div style="color:red">${row.taskUrgencyName}</div>` : ''))

        },
        { istrue: true, type: 'color', backgroudColor: 'rgb(200,200,200)' },
        {
            istrue: true, prop: 'isDelivered', label: '到样', width: '50',
            type: 'click', handle: (that, row, column) => that.onShowOrderDtl(row),
            formatter: (row) => row.isDelivered == 1 ? '是' : ' '
        },
        {
            istrue: true, prop: 'isReissue', label: '补发', width: '50',
            type: 'click', handle: (that, row, column) => that.onShowOrderDtl(row),
            formatter: (row) => row.isReissue == 1 ? '是' : ' '
        },
        { istrue: true, type: 'color', backgroudColor: 'rgb(200,200,200)' },
        {
            istrue: true, width: '140', prop: '', label: '视频一', merge: true, prop: 'mergeField',
            cols: [
                { istrue: true, width: '50', prop: 'referenceVideo1', label: '参考', type: "click", handle: (that, row, column, cell) => that.onViewYjqVideo(row, 1) },


                {
                    istrue: true, type: "button", width: "50", label: '完成',
                    btnList: [
                        { label: "完成", permission: "api:vediotask:pickvideotaskasync", ishide: (that, row) => that.IsNotPicking(row, 1), handle: (that, row) => that.pickTask(row, 1) },
                        { label: "取消", permission: "api:media:vediotask:UnPickVideoTaskAsync", ishide: (that, row) => !that.IsNotPicking(row, 1), handle: (that, row) => that.unPickTask(row, 1) }
                    ]
                },
                { istrue: true, prop: 'claimant1', dingdingcode: 'claimantId1DDingUserId', label: '拍摄人', width: '80', type: 'ddingtalk' },
                { istrue: true, width: '75', prop: 'claimTime1', label: '日期', formatter: (row) => { if (!row.claimTime1) { return '' } else { return formatTime(row.claimTime1 || '', 'MM月DD日') } } },

            ]
        },
        { istrue: true, type: 'color', backgroudColor: 'rgb(200,200,200)' },
        {
            istrue: true, width: '140', prop: '', label: '视频二', merge: true, prop: 'mergeField1',
            cols: [
                { istrue: true, width: '50', prop: 'referenceVideo2', label: '参考', type: "click", handle: (that, row, column, cell) => that.onViewYjqVideo(row, 2) },
                {
                    istrue: true, type: "button", width: "50", label: '完成',
                    btnList: [
                        { label: "完成", permission: "api:vediotask:pickvideotaskasync", ishide: (that, row) => that.IsNotPicking(row, 2), handle: (that, row) => that.pickTask(row, 2) },
                        { label: "取消", permission: "api:media:vediotask:UnPickVideoTaskAsync", ishide: (that, row) => !that.IsNotPicking(row, 2), handle: (that, row) => that.unPickTask(row, 2) }
                    ]
                },
                { istrue: true, prop: 'claimant2', dingdingcode: 'claimantId2DDingUserId', label: '拍摄人', width: '80', type: 'ddingtalk' },
                { istrue: true, width: '75', prop: 'claimTime2', label: '日期', formatter: (row) => { if (!row.claimTime2) { return '' } else { return formatTime(row.claimTime2 || '', 'MM月DD日') } } },
            ]
        },
        { istrue: true, type: 'color', backgroudColor: 'rgb(200,200,200)' },
        {
            istrue: true, width: '140', prop: '', label: '视频三', merge: true, prop: 'mergeField2',
            cols: [
                { istrue: true, width: '50', prop: 'referenceVideo3', label: '参考', type: "click", handle: (that, row, column, cell) => that.onViewYjqVideo(row, 3) },


                {
                    istrue: true, type: "button", width: "50", label: '完成',
                    btnList: [
                        { label: "完成", permission: "api:vediotask:pickvideotaskasync", ishide: (that, row) => that.IsNotPicking(row, 3), handle: (that, row) => that.pickTask(row, 3) },
                        { label: "取消", permission: "api:media:vediotask:UnPickVideoTaskAsync", ishide: (that, row) => !that.IsNotPicking(row, 3), handle: (that, row) => that.unPickTask(row, 3) }
                    ]
                },
                { istrue: true, prop: 'claimant3', dingdingcode: 'claimantId3DDingUserId', label: '拍摄人', width: '80', type: 'ddingtalk' },
                { istrue: true, width: '75', prop: 'claimTime3', label: '日期', formatter: (row) => { if (!row.claimTime3) { return '' } else { return formatTime(row.claimTime3 || '', 'MM月DD日') } } },

            ]
        },
        { istrue: true, type: 'color', backgroudColor: 'rgb(200,200,200)' },
        {
            istrue: true, width: '140', prop: '', label: '视频四', merge: true, prop: 'mergeField3',
            cols: [
                { istrue: true, width: '50', prop: 'referenceVideo4', label: '参考', type: "click", handle: (that, row, column, cell) => that.onViewYjqVideo(row, 4) },


                {
                    istrue: true, type: "button", width: "50", label: '完成',
                    btnList: [
                        { label: "完成", permission: "api:vediotask:pickvideotaskasync", ishide: (that, row) => that.IsNotPicking(row, 4), handle: (that, row) => that.pickTask(row, 4) },
                        { label: "取消", permission: "api:media:vediotask:UnPickVideoTaskAsync", ishide: (that, row) => !that.IsNotPicking(row, 4), handle: (that, row) => that.unPickTask(row, 4) }
                    ]
                },
                { istrue: true, prop: 'claimant4', dingdingcode: 'claimantId4DDingUserId', label: '拍摄人', width: '80', type: 'ddingtalk' },
                { istrue: true, width: '75', prop: 'claimTime4', label: '日期', formatter: (row) => { if (!row.claimTime4) { return '' } else { return formatTime(row.claimTime4 || '', 'MM月DD日') } } },

            ]
        },
        { istrue: true, type: 'color', backgroudColor: 'rgb(200,200,200)' },
        {
            istrue: true, width: '140', prop: '', label: '视频五', merge: true, prop: 'mergeField4',
            cols: [
                { istrue: true, width: '50', prop: 'referenceVideo5', label: '参考', type: "click", handle: (that, row, column, cell) => that.onViewYjqVideo(row, 5) },
                {
                    istrue: true, type: "button", width: "50", label: '完成',
                    btnList: [
                        { label: "完成", permission: "api:vediotask:pickvideotaskasync", ishide: (that, row) => that.IsNotPicking(row, 5), handle: (that, row) => that.pickTask(row, 5) },
                        { label: "取消", permission: "api:media:vediotask:UnPickVideoTaskAsync", ishide: (that, row) => !that.IsNotPicking(row, 5), handle: (that, row) => that.unPickTask(row, 5) }
                    ]
                },
                { istrue: true, prop: 'claimant5', dingdingcode: 'claimantId5DDingUserId', label: '拍摄人', width: '80', type: 'ddingtalk' },
                { istrue: true, width: '75', prop: 'claimTime5', label: '日期', formatter: (row) => { if (!row.claimTime5) { return '' } else { return formatTime(row.claimTime5 || '', 'MM月DD日') } } },

            ]
        },
        { istrue: true, type: 'color', backgroudColor: 'rgb(200,200,200)' },
        { istrue: true, prop: 'operationsGroup', label: '运营', width: '65' },
        { istrue: true, prop: 'dockingPeopleStr', label: '分配拍摄', width: '90' },
        {
            istrue: true, prop: 'shopId', label: '店铺', width: '175', sortable: 'custom',
            formatter: (row) => row.shopName || ' '
        },
        { istrue: true, prop: 'productId', label: '产品ID', width: '120' },
        {
            istrue: true, prop: 'arrivalDate', label: '到货日期', width: '75',
            formatter: (row) => { if (!row.arrivalDate) { return '' } else { return formatTime(row.arrivalDate || '', 'MM月DD日') } }
        },
        {
            istrue: true, prop: 'createdTime', label: '创建日期', width: '75',
            formatter: (row) => { if (!row.createdTime) { return '' } else { return formatTime(row.createdTime || '', 'MM月DD日') } }
        },
        { istrue: true, prop: 'sampleRrderNo', label: '内部单号', width: '200', type: 'click', handle: (that, row, column) => that.showLogDetail(row) },

        { istrue: true, prop: 'sampleExpressNo', label: '快递单号', width: '200', type: 'click', handle: (that, row, column) => that.onShowExproessHttp(row), },
        { istrue: true, prop: 'orderTrack',   label: '拿样跟踪', type: 'click', handle: (that, row, column) => that.onShowOrderDtl(row)},
        {
            istrue: true, type: "button", label: '操作', fixed: 'right', width: "240",
            btnList: [
                { label: "详情", permission: "viewvediotask", handle: (that, row) => that.lookTask(row) },
                { label: "编辑", permission: "api:vediotask:addorupdatevideotaskasync", handle: (that, row) => that.editTask(row) },
                { label: "剪切", permission: "api:Video:CuteVideoAsync", handle: (that, row) => that.onCuteVideo(row) },
                { label: "上传视频", permission: "api:vediotask:saveuploadteskvideoasync", ishide: (that, row) => !that.IsNotPicking(row), handle: (that, row) => that.onUploadVideo(row) },

                { type: "danger", permission: "api:vediotask:deletevideotaskasync", ishide: (that, row) => that.IsNotPickingDel(row), label: "删除", handle: (that, row) => that.deleteTask(row) }
            ]
        }
    ];
    const xdfhmainTableCols = [
        { istrue: true, prop: 'vedioTaskId', label: '当前任务', width: '80' },
        { istrue: true, prop: 'vedioTaskIds', label: '涉及任务', width: '100' },
        { istrue: true, prop: 'vedioTaskOrderId', label: '下单号', width: '70' },
        { istrue: true, prop: 'createdUserName', label: '下单人', width: '70' },
        { istrue: true, prop: 'receiverName', label: '收货人', width: '70' },
        { istrue: true, prop: 'receiverPhone', label: '收货电话', width: '80' },
        { istrue: true, prop: 'receiverState', label: '收货省', width: '70' },
        { istrue: true, prop: 'receiverCity', label: '收货市', width: '80' },
        { istrue: true, prop: 'receiverDistrict', label: '收货区', width: '80' },
        { istrue: true, prop: 'receiverAddress', label: '收货地址' },
        { istrue: true, prop: 'sampleRrderNo', label: '聚水潭内部单号', width: '120', type: 'click', handle: (that, row) => that.showLogDetail(row) },
        { istrue: true, prop: 'sampleExpressNo', label: '快递单号', width: '120', type: 'click', handle: (that, row, column) => that.onShowExproessHttp(row), },
        { istrue: true, prop: 'sampleExpressCom', label: '物流公司', width: '80' },
        { istrue: true, prop: 'arrivalDate', label: '到货日期', width: '100', formatter: (row) => row.arrivalDate == null ? null: formatTime(row.arrivalDate, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'isDy', label: '是否到样', width: '80', formatter: (row) => row.isDy == 1 ? "是" : "否" },
        { istrue: true, prop: 'isZt', label: '是否自提', width: '80', formatter: (row) => row.isZt == 1 ? "是" : "否" },
        { istrue: true, prop: 'approveStateName', label: '状态', width: '80'},
        { istrue: true, prop: 'createdTime', label: '创建时间', width: '150', formatter: (row) => row.createdTime == null ? null : formatTime(row.createdTime, 'YYYY-MM-DD HH:mm:ss') },
    ];
    const xdfhdtlTableCols = [
        { istrue: true, prop: 'goodsCode', label: '商品编码', width: '200' },
        { istrue: true, prop: 'goodsName', label: '商品名称' },
        { istrue: true, prop: 'goodsPrice', label: '单价', width: '120', display: false },
        { istrue: true, prop: 'goodsQty', label: '数量', width: '120' },
        { istrue: true, prop: 'goodsAmount', label: '总额', width: '120', display: false },
    ];
    const addressTableCols = [
        { istrue: true, prop: 'receiverAllAddress', label: '地址' },
        { istrue: true, prop: 'receiverName', label: '收货人', width: '120' },
        { istrue: true, prop: 'receiverPhone', label: '收货电话', width: '120' },
        {
            istrue: true, type: 'button', label: '操作', width: '60',
            btnList: [
                { label: "删除", handle: (that, row) => that.onAddressDelete(row) }
            ]
        }
    ];
    export default {
        name: "Users",
        inject: ['reload'],
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, goodschoice, productselect, videoplayer, 
            videoplay, videotaskaudio, taskcutevideolist, logistics, orderLogPage },
        data() {
            return {
                plAssignshootingName: null,
                uploadFiletempList: [],
                dialogVisibleSyjout: false,
                dialogfppcVisible: false,
                hasfailInfo: false,
                that: this,
                Filter: {
                    // taskDate: formatTime(new Date(), "YYYY-MM-DD"),
                    isAudioComplate: "0",
                    isShop: "0"
                },
                upmsginfo: "",
                showProgress: false,
                sydislook: false,
                percentage: 0,
                uploadVideoAudioVisible: false,
                uploadVideoAudioReload: true,
                videVideoAudioVisible: false,
                videVideoAudioReload: true,
                uploadpdrow: null,
                videotaskid: '',
                cutVideoList: [],
                videopath: '',
                videoduration: 10000,
                videoid: '',
                videoplayReload: true,
                editVideoVisible: false,
                islook: false,
                taskPageTitle: "新增任务",
                referenceVideoList: [],
                multipleSelection: [],
                addForm: {
                    productId: null,
                    shopId: null,
                    shopName: null,
                    productShortName: null,
                    goodCode: null,
                    taskDate: formatTime(new Date(), "YYYY-MM-DD"),
                    taskUrgency: 9,
                    firstSceneId: null,
                    secondSceneId: null,
                    thirdSceneId: null,
                    arrivalDate: null,
                    dockingPeopleStr: null,
                    operationsGroup: null,
                    remark: "",
                    referenceVideo1: "",
                    referenceVideo2: "",
                    referenceVideo3: "",
                    referenceVideo4: "",
                    referenceVideo5: "",
                    referenceVideo1Type: 0,
                    referenceVideo2Type: 0,
                    referenceVideo3Type: 0,
                    referenceVideo4Type: 0,
                    referenceVideo5Type: 0,
                    claimantId1: 0,
                    claimantId2: 0,
                    claimantId3: 0,
                    claimantId4: 0,
                    claimantId5: 0,
                    taskStatus: 0,
                    platform: 0,
                    sampleRrderNo: '',
                    sampleExpressNo: '',
                    pdIds: [],
                    pdArray: [],
                    isDelivered: 0,
                    isReissue: 0,
                    warehouse: null,
                    audioStatus: 0,
                },
                warehouseList: [
                    { label: '义乌--诚信仓', value: 1 },
                    { label: '南昌--定制仓', value: 2 },
                    { label: '南昌--全品仓', value: 3 }
                ],
                platformList: [],//平台下拉
                shopList: [],
                updateForm: {},
                taskUrgencyList: [],
                erpUserInfoList: [],
                operationsGroupList: [],
                taskFirstSceneList: [],
                taskSecondSceneList: [],
                taskThirdSceneList: [],
                taskGridFirstSceneList: [],
                taskGridSecondSceneList: [],
                taskGridThirdSceneList: [],
                tasklist: [],
                tableCols: tableCols,
                total: 0,
                addTask: false,
                summaryarry: {},
                pager: {},
                sels: [], // 列表选中列
                listLoading: false,
                ScpdLoading: false,
                pageLoading: false,
                addLoading: false,
                updategroupdialogVisibleSyj: false,
                dialogVisibleSyj: false,
                dialogVisible: false,
                selids: [],
                fileList: [],
                selPdArray: [],
                pdId: 0,
                addFormRules: {
                    // videoTaskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
                    productId: [{ required: true, message: '请选择产品', trigger: 'blur' }],
                    // goodCode: [{ required: true, message: '请选择商品', trigger: 'blur' }],
                    taskDate: [{ required: true, message: '请选择日期', trigger: 'blur' }],
                    // warehouse: [{ required: true, message: '请选择仓库', trigger: 'blur' }],
                    // firstSceneId: [{ required: true, message: '请选择场景一', trigger: 'blur' }],
                    // secondSceneId: [{ required: true, message: '请选择场景二', trigger: 'blur' }],
                    // thirdSceneId: [{ required: true, message: '请选择场景三', trigger: 'blur' }]
                    // referenceVideo1: [{ required: true, message: '请输入参考视频1', trigger: 'blur' }],
                    // referenceVideo2: [{ required: true, message: '请输入参考视频2', trigger: 'blur' }],
                    // referenceVideo3: [{ required: true, message: '请输入参考视频3', trigger: 'blur' }],
                    // referenceVideo4: [{ required: true, message: '请输入参考视频4', trigger: 'blur' }],
                    // referenceVideo5: [{ required: true, message: '请输入参考视频5', trigger: 'blur' }]
                },
                selTeskId: 0,
                productVisible: false,//选择产品窗口
                goodschoiceVisible: false,//选择商品窗口
                orderGoodschoiceVisible: false,//选择下单任务商品窗口
                isError: false, // 是否不能播放视频
                errMsg: "",
                videoUrls: 'http://www.si-tech.com.cn/pub-ui/images/radio/sitech.mp4',
                videoUrl: '',
                videoplayerReload: true,
                cankaoindex: 0,
                uploadpdVisibleSyj: false,
                yjqdialogVisible: false,
                yjqdialogReload: false,
                pdseloptions: [
                    { value: '1', label: '参考1' },
                    { value: '2', label: '参考2' },
                    { value: '3', label: '参考3' },
                    { value: '4', label: '参考4' },
                    { value: '5', label: '参考5' }
                ],
                SyjfileList: [],
                SypdjfileList: [],
                typeArr: ["video/mp4", "video/avi", "video/wmv", "video/rmvb", "video/quicktime", "video/mov"],

                uploadFiletempList: [],

                //下单发货
                receiverStateList: [],//省
                receiverCityList: [],//市
                receiverDistrictList: [],//区
                receiverCityList2: [],//市
                receiverDistrictList2: [],//区
                dialogAddOrderVisible: false,
                dialogAddOrderLoading: false,
                dialogAddOrderSubmitLoding: false,
                addOrderForm: {
                    vedioTaskIds: "",
                    receiverName: "",
                    receiverPhone: "",
                    receiverStateCode: "",
                    receiverCityCode: "",
                    receiverDistrictCode: "",
                    receiverState: "",
                    receiverCity: "",
                    receiverDistrict: "",
                    receiverAddress: "",
                    isZt: null,
                    warehouse: null,
                    remark: "",
                    receiverAddressAllInfo: "",
                    orderGoods: [],
                },
                calculateUnitlist: ['国内', '1688选品中心' , '跨境'],
                selVideoTaskIdSpanList: [],
                orderGoodschoiceVisible: false,
                addOrderFormRules: {
                    //receiverName: [{ required: true, message: '请输入收货人', trigger: 'blur' }],
                    //receiverPhone: [{ required: true, message: '请输入收货电话', trigger: 'blur' }],
                    isZt: [{ required: true, message: '请输入是否自提', trigger: 'blur' }],
                    calculateUnit: [{ required: true, message: '请选择核算单位', trigger: 'blur' }],
                    //receiverStateCode: [{ required: true, message: '请输入收货省', trigger: 'blur' }],
                    //receiverCityCode: [{ required: true, message: '请输入收货市', trigger: 'blur' }],
                    //receiverDistrictCode: [{ required: true, message: '请输入成收货区', trigger: 'blur' }],
                    //receiverAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
                    //receiverAddressAllInfo: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
                },
                dialogOrderDtlVisible: false,
                dialogOrderDtlLoading: false,

                xdfhmainlist: [],
                xdfhmainTableCols: xdfhmainTableCols,
                xdfhmainLoading: false,

                xdfhdtllist: [],
                xdfhdtlTableCols: xdfhdtlTableCols,
                xdfhdtlLoading: false,

                receiverAddressList: [],
                dialogAddressVisible: false,
                addressTableCols: addressTableCols,
                addressListLoading: false,
                dialogAddAddressSubmitLoding: false,
                addAddressForm: {
                    receiverName: "",
                    receiverPhone: "",
                    receiverStateCode: null,
                    receiverCityCode: null,
                    receiverDistrictCode: null,
                    receiverAddress: "",
                },
                addressList: [],
                addAddressFormRules: {
                    receiverName: [{ required: true, message: '请输入收货人', trigger: 'blur' }],
                    receiverPhone: [{ required: true, message: '请输入收货电话', trigger: 'blur' }],
                    receiverStateCode: [{ required: true, message: '请输入收货省', trigger: 'blur' }],
                    receiverCityCode: [{ required: true, message: '请输入收货市', trigger: 'blur' }],
                    receiverDistrictCode: [{ required: true, message: '请输入成收货区', trigger: 'blur' }],
                    receiverAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
                },
                drawervisible: false,

                sendOrderNoInner: "",
                dialogHisVisible: false,
            };
        },
        watch: {
        },
        async created() {

        },
        async mounted() {
            this.onSearch();
            this.checkPress();
            await this.setPlatform();//平台下拉
            await this.getTaskUrgencyList();//紧急状态下拉
            await this.getFirstSceneList();
            await this.getUserInfo();//获取erp用户
            await this.getyunYingGroupInfo();//获取分组

            //省
            await this.getCity(0, 1);
            await this.getAddressList();
        },
        methods: {
            // getWareHouseName(ware){
            //     let name=""; 
            //     this.warehouseList.forEach(f=>{
            //         if(f.value==ware)
            //         {
            //             name=f.label;
            //         }
            //     });
            //    return name;
            // },
            async onExeprotVedioTask(){
            var pager = this.$refs.pager.getPager(); 
            const params = {
                ...pager,
                ...this.pager,
                ...this.Filter,
                exporttype:"over1"

            };
            this.pageLoading = true;
            var res = await exportVedioTaskReport(params);
            if (res?.data?.type == 'application/json') {return;}
            this.pageLoading = false;
            const aLink = document.createElement("a");
            var blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '短视频任务列表导出.xlsx')
            aLink.click()

        },
            handleCommand(command) {
                switch (command) {
                    case "a":
                        this.onAddOrder();
                        break;
                    case "b":
                        this.onAssignShooting();
                        break;
                    default:
                        this.$message({ type: 'warning', message: "功能暂未开放" });
                        break;
                }
            },
            //打开分配加工表
            onAssignShooting() {
                if (this.selids.length <= 0) {
                    this.$message({ message: '请勾选任务', type: "warning" });
                    return;
                }
                this.plAssignshootingName = null;
                this.selVideoTaskIdSpanList = [];
                this.selids.forEach(videoTaskId => {
                    this.addOrderForm.vedioTaskIds += (videoTaskId.toString() + ";");
                    this.selVideoTaskIdSpanList.push((videoTaskId.toString() + ";&nbsp"));
                });
                this.dialogfppcVisible = true;

            },
            //分配前校验
            addAssignshootingValidate() {
                return true;
            },
            //批量修改分配拍摄人
            async onAssignshootingSave() {
                this.dialogAddOrderSubmitLoding = true;
                /*  this.$confirm("此操作将会覆盖之前的负责人, 是否继续?", "提示", {
                     confirmButtonText: "确定",
                     cancelButtonText: "取消",
                     type: "warning",
                 }).then(async () => { */
                var res = await assignshootingSave({ taskids: this.selids, SetName: this.plAssignshootingName });
                if (res?.success) {
                    this.$message({ type: 'success', message: "操作成功" });
                    this.onRefresh();
                    this.dialogAddOrderSubmitLoding = false;
                    this.dialogfppcVisible = false;
                }
                //});
                this.dialogAddOrderSubmitLoding = false;
            },
           
            async getUserInfo() {
                const res = await getErpUserInfoView();
                this.erpUserInfoList = res || [];
            },
            async getyunYingGroupInfo() {
                const res = await getOperationsGroup({ type: 1 });
                this.operationsGroupList = res || [];
            },
            checkPress() {
                this.delpress = checkPermission("api:vediotask:deletevideotaskasync");
            },
            handleChange(value) {
                //console.log(value);
            },

            onAddpdArray() {
                if (this.addForm.claimantId1 == 0 || this.addForm.claimantId1 == null) {
                    this.addForm.pdArray.push({ ckVideoIndex: "1", url: '' });
                } else if (this.addForm.claimantId2 == 0 || this.addForm.claimantId2 == null) {
                    this.addForm.pdArray.push({ ckVideoIndex: "2", url: '' });
                } else if (this.addForm.claimantId3 == 0 || this.addForm.claimantId3 == null) {
                    this.addForm.pdArray.push({ ckVideoIndex: "3", url: '' });
                } else if (this.addForm.claimantId4 == 0 || this.addForm.claimantId4 == null) {
                    this.addForm.pdArray.push({ ckVideoIndex: "4", url: '' });
                } else if (this.addForm.claimantId5 == 0 || this.addForm.claimantId5 == null) {
                    this.addForm.pdArray.push({ ckVideoIndex: "5", url: '' });
                } else {
                    this.$message({ type: 'warning', message: "已无‘参考视频’可完成！" });
                }

            },
            onDelpdArray(index) {
                this.$confirm("是否确定删除, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(async () => {
                    this.addForm.pdArray.splice(index, 1);
                });

            },
            async onComplatVideoTask(row) {
                var that = this;
                this.$confirm("确认完成, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(async () => {
                    await complatVideoTask({ videoTaskId: row.videoTaskId, type: 1 });
                    that.$message({ message: '操作成功', type: "success" });
                    that.onRefresh();
                });
            },
            async onUploadVideoAudio(row) {
                this.uploadVideoAudioReload = false;
                this.uploadVideoAudioVisible = true;
                this.uploadVideoAudioReload = true;
                this.videotaskid = row.videoTaskId;

            },
            async closeVideoAudioCuteForm() {
                this.uploadVideoAudioReload = false;
            },
            async onViewVideoAudio(row) {
                this.videVideoAudioReload = false;
                this.videVideoAudioVisible = true;
                this.videVideoAudioReload = true;
                this.videotaskid = row.videoTaskId;
            },
            async closeViewVideoAudioForm() {
                this.uploadVideoAudioReload = false;
            },
            async closeCuteForm() {
                this.videoplayReload = false;
            },
            async closeVideoPlyer() {
                this.videoplayerReload = false;
            },
            async onViewYjqVideo(row, index) {
                this.yjqdialogReload = false;
                this.yjqdialogVisible = true;
                this.yjqdialogReload = true;
                this.videotaskid = row.videoTaskId;
                this.cankaoindex = index;
            },
            async closeYjqVideo() {
                this.yjqdialogReload = false;
            },
            async onVideoAudioSubmit() {
                var that = this;

                var data = this.$refs.videotaskaudio.getSaveData();
                if (data.audioStatus == 1) {
                    that.$message({ type: 'warning', message: "请选择是否通过" });
                    return;
                }
                const res = await videoTaskAudioAsync(data);
                if (!res?.success) {
                    that.$message({ type: 'warning', message: res?.msg });
                } else {
                    that.$message({ type: 'success', message: '提交成功' });
                    this.$refs.videotaskaudio.loadList();
                }
            },
            async onCutVideoSubmit() {
                var that = this;

                var data = this.$refs.videoplay.getData()
                if (data.length == 0) {
                    that.$message({ type: 'warning', message: "请至少剪切一段" });
                    return;
                }
                var para = [];
                data.forEach((item, index) => {
                    var video = {};
                    video.ID = that.videoid;
                    video.videoPdId = item.videoPdId;
                    video.remark = item.remark;
                    video.firstSceneId = item.firstSceneId;
                    video.firstSceneIds = item.firstSceneIds;
                    video.beginTime = item.beginTime;
                    video.endTime = item.endTime;
                    video.title = item.title;
                    video.videoIndex = item.videoIndex;
                    video.cuteId = item.cuteId;
                    para.push(video);
                });
                const res = await cuteVideo(para);
                if (!res?.success) {
                    that.$message({ type: 'warning', message: res?.msg });
                } else {
                    that.$message({ type: 'success', message: '提交成功,排队剪切中，请到剪切进度中查看...' });
                    that.videoplayReload = false;
                    that.editVideoVisible = false;
                }

            },
            async onCuteVideo(row) {
                await this.getFirstSceneList();
                this.editVideoVisible = true;
                this.videoplayReload = true;
                this.videopath = row.videoPath;
                this.videoid = row.videoTaskId;
                this.referenceVideoList = [];
                this.referenceVideoList = await this.getPdArray(row, 1);

            },
            async pushReferenceVideoList(url, index) {
                if (url != '') {
                    var ckspList = {};
                    ckspList.index = index;
                    ckspList.name = '参考视频' + index;
                    ckspList.url = url;
                    this.referenceVideoList.push(ckspList);
                }

            },
            async openVideo(url) {
                var that = this;
                this.$copyText(url).then(function (e) {
                    that.$message({ type: 'success', message: '复制成功' });
                    window.open(url);
                }, function (e) {
                    that.$message({ type: 'success', message: '复制失败' });
                    window.open(url);
                })
            },
            //设置平台下拉
            async setPlatform() {
                var pfrule = await rulePlatform();
                this.platformList = pfrule.options;
            },
            async onchangeplatform(val) {
                const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100 });
                this.shopList = res1.data.list;
            },
            //选择产品窗口
            onSelctProduct() {
                this.productVisible = true;
            },
            //选择商品
            onSelctCp() {
                this.goodschoiceVisible = true;
            },
            //选择产品确定
            async onQuerenProduct() {
                var choicelist = await this.$refs.productselect.getchoicelistOnly();
                if (choicelist && choicelist.length == 1) {
                    this.addForm.productId = choicelist[0].proCode;
                    this.addForm.productShortName = choicelist[0].styleCode;
                    this.addForm.shopId = choicelist[0].shopId;
                    this.addForm.shopName = choicelist[0].shopName;
                    this.addForm.platform = choicelist[0].platform;
                    this.productVisible = false;
                }
            },
            //选择商品确定
            async onQueren() {
                var choicelist = await this.$refs.goodschoice.getchoicelist();
                if (choicelist) {
                    var goodCodeList = [];
                    choicelist.forEach((item) => {
                        goodCodeList.push(item.goodsCode);
                    });
                    // this.addForm.goodCode = choicelist[0].goodsCode;
                    this.addForm.goodCode = goodCodeList.join(',');
                    this.goodschoiceVisible = false;
                }
            },
            //提交保存时验证
            onSubmitValidate: function () {
                let isValid = false
                this.$refs.addForm.validate(valid => {
                    isValid = valid
                })
                return isValid
            },
            //提交保存
            async onSubmit() {
                var ids = [];
                this.cutVideoList.forEach((item, index) => {
                    ids.push(item.id);
                });
                this.addForm.pdIds = ids;
                const para = _.cloneDeep(this.addForm);
                debugger;
                var res = await addOrUpdateVideoTaskAsync(para);
                if (!res?.success) {
                    return;
                }
                this.$message({
                    message: this.$t('保存成功'),
                    type: 'success'
                })
                //this.$refs['addForm'].resetFields();
                this.addTask = false;
                await this.onSearch();
            },
            async closedialogVisibleSyj() {
                if (this.uploadFiletempList.length > 0) {
                    await this.$confirm("还有文件未上传完成，或存在上传失败视频。是否确认关闭", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }).then(() => {
                        this.SyjfileList = [];
                        this.dialogVisibleSyj = false;
                    });

                } else {
                    this.SyjfileList = [];
                    this.dialogVisibleSyj = false;
                }
            },
            //关闭完成界面时请清空表单数据
            onCloseAddForm() {
                this.addForm = {
                    productId: null,
                    shopId: null,
                    shopName: null,
                    productShortName: null,
                    goodCode: null,
                    taskDate: formatTime(new Date(), "YYYY-MM-DD"),
                    taskUrgency: 9,
                    firstSceneId: null,
                    secondSceneId: null,
                    thirdSceneId: null,
                    isReissue: 0,
                    remark: "",
                    arrivalDate: null,
                    dockingPeopleStr: null,
                    operationsGroup: null,
                    referenceVideo1: "",
                    referenceVideo2: "",
                    referenceVideo3: "",
                    referenceVideo4: "",
                    referenceVideo5: "",
                    referenceVideo1Type: 0,
                    referenceVideo2Type: 0,
                    referenceVideo3Type: 0,
                    referenceVideo4Type: 0,
                    referenceVideo5Type: 0,
                    claimantId1: 0,
                    claimantId2: 0,
                    claimantId3: 0,
                    claimantId4: 0,
                    claimantId5: 0,
                    taskStatus: 0,
                    platform: 0,
                    sampleRrderNo: '',
                    sampleExpressNo: '',
                    pdIds: [],
                    pdArray: [],
                    audioStatus: 0,
                    isDelivered: 0,
                    isReissue: 0,

                };
                this.pdId = 0,
                    this.addLoading = false,
                    this.ScpdLoading = false,
                    this.pdseloptions = [
                        { value: '1', label: '参考1' },
                        { value: '2', label: '参考2' },
                        { value: '3', label: '参考3' },
                        { value: '4', label: '参考4' },
                        { value: '5', label: '参考5' }
                    ];
                this.SyjfileList = [];
                this.SypdjfileList = [];
            },
            //设置紧急状态下拉
            async getTaskUrgencyList() {
                const res = await getTaskUrgencyList();
                this.taskUrgencyList = res.data || [];
            },
            //设置场景一下拉源
            async getFirstSceneList(ischeck) {
                const res = await getSceneListTree();

                this.taskFirstSceneList = res.data || [];
                this.taskGridFirstSceneList = res.data || [];
            },
            async RefreshData() {
                this.$forceUpdate();
            },
            //新增任务
            async onAddTask() {
                this.addTask = true;
                this.taskPageTitle = "新增任务";
                this.islook = false;
                this.addForm.taskUrgency = 9;
                await this.getFirstSceneList();
            },
            //查看任务按钮显藏
            showlook(row) {
                if (row.videoTaskId === 1) {
                    return true;
                }
                return false;
            },
            IsNotPickingDel(row) {
                var ret = true;
                if (this.delpress) {
                    ret = false;
                }
                return ret;
            },
            //查看删除拍摄完成任务按钮显藏
            IsNotPicking(row, index) {
                var result = false;
                switch (index) {
                    case 0:
                        if (row.cuteLqId != null && row.cuteLqId != "") {
                            result = true;
                        }
                        break;
                    case 1:
                        if (row.claimant1 != null && row.claimant1 != "") {
                            result = true;
                        }
                        break;
                    case 2:
                        if (row.claimant2 != null && row.claimant2 != "") {
                            result = true;
                        }
                        //console.log(result)
                        break;
                    case 3:
                        if (row.claimant3 != null && row.claimant3 != "") {
                            result = true;
                        }
                        break;
                    case 4:
                        if (row.claimant4 != null && row.claimant4 != "") {
                            result = true;
                        }
                        break;
                    case 5:
                        if (row.claimant5 != null && row.claimant5 != "") {
                            result = true;
                        }
                        break;
                    case 6:
                        if (row.cutClaimant1 != null && row.cutClaimant1 != "") {
                            result = true;
                        }
                        break;
                    case 7:
                        if (row.cutClaimant2 != null && row.cutClaimant2 != "") {
                            result = true;
                        }
                        break;
                    case 8:
                        if (row.cutClaimant3 != null && row.cutClaimant3 != "") {
                            result = true;
                        }
                        break;
                    case 9:
                        if (row.cutClaimant4 != null && row.cutClaimant4 != "") {
                            result = true;
                        }
                        break;
                    case 10:
                        if (row.cutClaimant5 != null && row.cutClaimant5 != "") {
                            result = true;
                        }
                        break;
                    default:
                        if (row.audioStatus != null && row.audioStatus > 0) {
                            result = true;
                        }
                        break;

                }
                return result;
            },
            //查看任务
            async lookTask(row) {
                this.taskPageTitle = "查看任务";
                this.islook = true;
                this.addForm = _.cloneDeep(row);
                this.addTask = true;
                await this.getFirstSceneList();
                this.addForm.warehouse = this.addForm.warehouse == 0 ? null : this.addForm.warehouse;
                this.addForm.pdArray = await this.getPdArray(row, 1);
                this.addLoading = false;

            },
            //编辑任务
            async editTask(row) {
                this.addLoading = true;
                this.taskPageTitle = "编辑任务";
                this.islook = false;
                this.addForm = _.cloneDeep(row);
                this.addForm.warehouse = this.addForm.warehouse == 0 ? null : this.addForm.warehouse;
                this.addTask = true;
                this.pdseloptions = [

                    { label: "参考1", value: "1", disabled: this.addForm.claimantId1 > 0 },
                    { label: "参考2", value: "2", disabled: this.addForm.claimantId2 > 0 },
                    { label: "参考3", value: "3", disabled: this.addForm.claimantId3 > 0 },
                    { label: "参考4", value: "4", disabled: this.addForm.claimantId4 > 0 },
                    { label: "参考5", value: "5", disabled: this.addForm.claimantId5 > 0 }];

                await this.getFirstSceneList();
                this.addForm.pdArray = await this.getPdArray(row, 1);
                this.addLoading = false;

            },
            async getPdArray(row, type) {
                const res = await getVideoTaskPdList({ videoTaskId: row.videoTaskId, type: type });
                var pdlist = [];
                res.data.list.forEach((item, index) => {
                    item.ckVideoIndex = item.ckVideoIndex.toString();
                    pdlist.push(item);
                });

                return pdlist;
            },
            //删除任务
            async deleteTask(row) {
                var that = this;
                this.$confirm("确认删除, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(async () => {
                    var res = await deleteVideoTaskAsync({ videoTaskId: row.videoTaskId })
                    if (res?.success) {
                        that.$message({ message: '删除成功', type: "success" });
                        that.onRefresh();
                    }
                });

            },
            //拍摄完成任务
            async pickTask(row, index) {
                var that = this;
                var msg = "确认拍摄完成, 是否继续?";
                if (index == 0)
                    msg = "确认领取, 是否继续?";
                this.$confirm(msg, "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(async () => {
                        var res = await pickVideoTaskAsync({ videoTaskId: row.videoTaskId, index: index })
                        if (res?.success) {
                            that.$message({ message: '完成成功', type: "success" });
                            that.onRefresh();

                        }

                    });
            },
            //取消拍摄完成任务
            async unPickTask(row, index) {
                var that = this;
                var msg = "确认取消拍摄完成, 是否继续?";
                if (index == 0)
                    msg = "确认取消领取, 是否继续?";

                this.$confirm(msg, "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(async () => {
                        var res = await unPickVideoTaskAsync({ videoTaskId: row.videoTaskId, index: index })
                        if (res?.success) {
                            that.$message({ message: '取消成功', type: "success" });
                            that.onRefresh();
                        }
                    });
            },


            //列表设置场景一下拉源
            async getGridFirstSceneList() {
                this.Filter.firstSceneId = null;
                //const res = await getSceneList({ parentid: null, level: 1 });
                //this.taskGridFirstSceneList = res.data.list || [];
                const res = await getSceneListTree();

                this.taskGridFirstSceneList = res.data || [];
            },

            playVideo(videoUrl) {
                this.videoplayerReload = false;
                this.videoplayerReload = true;
                this.dialogVisible = true;
                this.videoUrl = videoUrl;
                //  this.$refs.video.src = this.videoUrl;
            },
            error(e) {
                //console.log(e);
                if (this.videoUrl == '') {
                    this.isError = true
                    this.errMsg = "该文章暂无视频！"
                } else {
                    this.isError = true
                    this.errMsg = "视频链接失效！无法播放！"
                }
            },
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            onRefresh() {
                this.getTaskList();
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.getTaskList();
                this.selids = [];
            },
            async getTaskList() {
                // if (this.Filter.UseDate) {
                //     this.Filter.startAccountDate = this.Filter.UseDate[0];
                //     this.Filter.endAccountDate = this.Filter.UseDate[1];
                // }
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...this.Filter
                };
                this.listLoading = true;
                const res = await pageViewTaskAsync(params);
                //console.log(res)
                this.listLoading = false;
                //console.log(res.data.list)
                //console.log(res.data.summary)

                this.total = res.data.total
                this.tasklist = res.data.list;
                this.summaryarry = { videoTaskId_sum: " _" };
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.videoTaskId);
                });
            },
            async onUploadVideo(row) {
                // this.selPdArray = await this.getPdArray(row, 2);
                var that = this;
                this.upmsginfo = "";
                this.uploadFiletempList = [];
                this.percentage = 0;
                this.hasfailInfo = false;
                var res = await getTaskCuteVideoList({ videoTaskId: row.videoTaskId, ckindex: 0, type: 2 });
                if (!res?.success) {
                    return
                }
                this.selPdArray = [];
                var list = res.data.list;
                list.forEach((item, index) => {
                    item.videoList.forEach((v, index) => {
                        v.fileList = [];
                        that.selPdArray.push(v);
                    });
                    that.selPdArray.push({ id: item.id, pdId: item.pdid, parent: 0, ckVideoIndex: item.ckVideoIndex, title: item.title });
                });
                that.selPdArray = listToTree(that.selPdArray, null, "id", "parent");

                //console.log(that.selPdArray);
                this.selTeskId = row.videoTaskId;
                this.dialogVisibleSyj = true;
            },
            async uploadFile2(item) {

                this.ScpdLoading = true;
                this.atfterUplaodData = null;
                this.showProgress = true;
                this.percentage = 0;
                await this.AjaxFile(item.file, 0, "");
                if (this.atfterUplaodData != null) {
                    await this.afteruploadFile2();
                }
                this.ScpdLoading = false;
                this.showProgress = false;
                this.percentage = 0;
                this.dialogVisibleSyj = false;
            },


            async uploadSuccess2(response, file, fileList) {
                fileList.splice(fileList.indexOf(file), 1);
            },


            //移除视频
            uploadFileReomveTemp(file, row) {
                for (let num in this.uploadFiletempList) {
                    if (this.uploadFiletempList[num].cuteid == row.id && this.uploadFiletempList[num].file.uid == file.uid) {
                        this.uploadFiletempList.splice(num, 1)
                    }
                }
                //console.log(this.uploadFiletempList);
            },
            //上传视频列表选择文件
            uploadFiletemp(file, row) {
                this.uploadFiletempList.push({ cuteid: row.id, taskid: this.selTeskId, file: file.file, title: row.title, ckVideoIndex: row.ckVideoIndex });

            },
            //上传视频
            async onSubmitupload2() {
                if (this.uploadFiletempList.length == 0) {
                    this.$message({ message: '请选择一个视频文件', type: "waring" });
                    return;
                }
                await this.uploadFiletempasync()
            },
            //批量上传
            async uploadFiletempasync() {
                this.ScpdLoading = true;
                this.showProgress = true;
                this.tempoutlist = [];
                this.uploadFiletempList.forEach(element => {
                    this.tempoutlist.push(element);
                });
                this.startIndex = this.tempoutlist.length;
                for (var i = 0; i < this.tempoutlist.length; i++) {
                    var item = this.tempoutlist[i];
                    this.upmsginfo = "正在上传：参考视频" + item.ckVideoIndex + "-" + item.title + "-" + item.file.name;
                    this.atfterUplaodData = null;
                    this.percentage = 0;
                    this.startIndex = this.startIndex - 1;
                    await this.AjaxFile(item.file, 0, "");
                    if (this.atfterUplaodData != null) {
                        await this.afteruploadtempList(item, this.startIndex);
                    }
                    this.percentage = 0;
                }
                this.ScpdLoading = false;
            },
            //业务处理
            async afteruploadtempList(item, startIndex) {
                const form = new FormData();
                this.atfterUplaodData.fileName = item.file.name;
                form.append("upfile", JSON.stringify(this.atfterUplaodData));
                form.append("videotaskid", item.taskid);
                form.append("cuteid", item.cuteid);
                const res = await saveUploadTeskVideoAsync(form);
                if (res?.success) {
                    for (let num in this.uploadFiletempList) {
                        if (this.uploadFiletempList[num].cuteid == item.cuteid && this.uploadFiletempList[num].file.uid == item.file.uid) {
                            this.uploadFiletempList.splice(num, 1)
                        }
                    }

                }
                if (startIndex == 0) {
                    var rest = await getTaskCuteVideoList({ videoTaskId: item.taskid, ckindex: 0, type: 2 });
                    if (rest?.success) {
                        this.selPdArray = [];
                        var list = rest.data.list;
                        list.forEach((it, index) => {
                            it.videoList.forEach((v, index) => {
                                this.selPdArray.push(v);
                            });
                            this.selPdArray.push({ id: it.id, pdId: it.pdid, parent: 0, ckVideoIndex: it.ckVideoIndex, title: it.title });
                        });
                        this.selPdArray = listToTree(this.selPdArray, null, "id", "parent");
                        this.$nextTick(() => {
                            this.$refs.MediamultipleTable.doLayout();
                        });

                    }
                    this.upmsginfo = "上传完成！";
                    this.$message({ message: '上传完成！', type: "success" });

                    if (this.uploadFiletempList.length > 0) {

                        this.hasfailInfo = true;
                        this.$message({ message: '存在上传失败信息请查看，截图保存', type: "error" });
                    }

                    this.ScpdLoading = false;
                }
            },
            //打开上传片段
            async onUploadVideoPd(index) {
                this.cankaoindex = index;
                this.uploadpdVisibleSyj = true;
            },
            //上传片段
            async uploadpdFile(item) {
                if (!this.typeArr.includes(item.file.type)) {
                    this.$message.warning("上传文件格式错误!")
                    return
                }
                this.ScpdLoading = true;
                this.atfterUplaodData = null;
                this.percentage = 0;
                this.showProgress = true;
                await this.AjaxFile(item.file, 0, "");
                if (this.atfterUplaodData != null) {
                    await this.AfteruploadpdFile();
                }
                this.showProgress = false;
                this.ScpdLoading = false;
            },
            async AjaxFile(file, i, batchnumber) {

                var name = file.name; //文件名
                var size = file.size; //总大小shardSize = 2 * 1024 * 1024,
                var shardSize = 15 * 1024 * 1024;
                var shardCount = Math.ceil(size / shardSize); //总片数
                if (i >= shardCount) {
                    return;
                }
                //计算每一片的起始与结束位置
                var start = i * shardSize;
                var end = Math.min(size, start + shardSize);
                //构造一个表单，FormData是HTML5新增的
                i = i + 1;
                var form = new FormData();
                form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分
                form.append("batchnumber", batchnumber);
                form.append("fileName", name);
                form.append("total", shardCount); //总片数
                form.append("index", i); //当前是第几片

                const res = await xMTVideoUploadBlockAsync(form);
                if (res?.success) {
                    this.percentage = (i * 100 / shardCount).toFixed(2);

                    if (i == shardCount) {
                        this.atfterUplaodData = res.data;
                    } else {
                        await this.AjaxFile(file, i, res.data);
                    }
                } else {
                    this.$message({ message: res?.msg, type: "warning" });
                    this.ScpdLoading = false;

                }
            },

            async AfteruploadpdFile() {
                var form = new FormData();
                var pdid = this.addForm.pdArray[this.cankaoindex].pdId ?? 0;
                var ckindex = this.addForm.pdArray[this.cankaoindex].ckVideoIndex;
                //console.log(this.atfterUplaodData);
                form.append("upfile", JSON.stringify(this.atfterUplaodData));
                form.append("index", ckindex);
                form.append("pdId", pdid);
                form.append("videotaskid", this.addForm.videoTaskId);
                this.addLoading = true;
                const res = await uplodPdVideoAsync(form);
                if (res?.success) {
                    this.addForm.pdArray[this.cankaoindex].url = res.data.videoFullPath;
                    this.addForm.pdArray[this.cankaoindex].pdId = res.data.pdId;
                    this.addForm.pdArray[this.cankaoindex].pdId = res.data.pdId;
                    this.addForm.pdArray[this.cankaoindex].cuteId = res.data.cuteId;
                    this.$message({ message: '上传成功', type: "success" });
                    this.addLoading = false;
                    // this.cutVideoList.push({ videoPath: res.data.videoPath, imgPath: res.data.videoImgPath, id: res.data.cutid });
                } else {
                    this.addLoading = false;
                    this.$message({ message: '上传失败', type: "warning" });
                }
                this.percentage = 0;
                this.showProgress = false;
                this.uploadpdVisibleSyj = false;

            },

            async uploadpdSuccess(response, file, fileList) {

                this.SypdjfileList.splice(fileList.indexOf(file), 1);
            },

            closedialogVisibleScpd() {
                this.uploadpdVisibleSyj = false;
                this.SypdjfileList = [];

            },
            async onPdSubmitupload() {
                this.$refs.upload3.submit();
            },
            //下拉修改
            pdChanged(index) {
                var result = false;
                var that = this.addForm;
                switch (index) {
                    case "1":
                        if (that.claimantId1 > 0) {
                            result = true;
                        }
                        break;
                    case "2":
                        if (that.claimantId2 > 0) {
                            result = true;
                        }
                        break;
                    case "3":
                        if (that.claimantId3 > 0) {
                            result = true;
                        }
                        break;
                    case "4":
                        if (that.claimantId4 > 0) {
                            result = true;
                        }
                        break;
                    case "5":
                        if (that.claimantId5 > 0) {
                            result = true;
                        }
                        break;
                }
                return !result;
            },
            //pdreadonly
            IsPDreading(index, that) {
                var result = false;

                switch (index) {
                    case "1":
                        if (that.claimantId1 > 0) {
                            result = true;
                        }
                        break;
                    case "2":
                        if (that.claimantId2 > 0) {
                            result = true;
                        }
                        break;
                    case "3":
                        if (that.claimantId3 > 0) {
                            result = true;
                        }
                        break;
                    case "4":
                        if (that.claimantId4 > 0) {
                            result = true;
                        }
                        break;
                    case "5":
                        if (that.claimantId5 > 0) {
                            result = true;
                        }
                        break;
                }
                if (this.islook)
                    result = true;

                return result;
            },
            //下单发货表单
            async getCity(parentCode, type,) {
                const res = await getCityAllData({ parentCode: parentCode });
                if (res?.success) {
                    if (type == 1) {
                        this.receiverStateList = res?.data;
                    }
                    if (type == 2) {
                        this.receiverCityList = res?.data;
                    }
                    if (type == 3) {
                        this.receiverDistrictList = res?.data;
                    }
                }
            },
            async receiverStateChange() {
                this.receiverCityList = [];
                this.receiverDistrictList = [];
                this.addOrderForm.receiverCityCode = "";
                this.addOrderForm.receiverDistrictCode = "";
                var parentCode = this.addOrderForm.receiverStateCode;
                if (parentCode) {
                    this.getCity(parentCode, 2);
                }
            },
            async receiverCityChange() {
                this.receiverDistrictList = [];
                this.addOrderForm.receiverDistrictCode = "";
                var parentCode = this.addOrderForm.receiverCityCode;
                if (parentCode) {
                    this.getCity(parentCode, 3);
                }
            },
            async getCity2(parentCode, type,) {
                const res = await getCityAllData({ parentCode: parentCode });
                if (res?.success) {
                    if (type == 1) {
                        this.receiverStateList = res?.data;
                    }
                    if (type == 2) {
                        this.receiverCityList2 = res?.data;
                    }
                    if (type == 3) {
                        this.receiverDistrictList2 = res?.data;
                    }
                }
            },
            async receiverStateChange2() {
                this.receiverCityList2 = [];
                this.receiverDistrictList2 = [];
                this.addAddressForm.receiverCityCode = "";
                this.addAddressForm.receiverDistrictCode = "";
                var parentCode = this.addAddressForm.receiverStateCode;
                if (parentCode) {
                    this.getCity2(parentCode, 2);
                }
            },
            async receiverCityChange2() {
                this.receiverDistrictList2 = [];
                this.addAddressForm.receiverDistrictCode = "";
                var parentCode = this.addAddressForm.receiverCityCode;
                if (parentCode) {
                    this.getCity2(parentCode, 3);
                }
            },
            //下单发货
            async onAddOrder() {
                if (this.selids.length <= 0) {
                    this.$message({ message: '请勾选任务', type: "warning" });
                    return;
                }
                const res = await vedioTaskAddOrderSaveCheckTaskIds(this.selids);
                if (!res?.success) {
                    return;
                }
                this.addOrderForm.vedioTaskIds = "";
                this.addOrderForm.orderGoods = [];
                this.selVideoTaskIdSpanList = [];
                this.selids.forEach(videoTaskId => {
                    this.addOrderForm.vedioTaskIds += (videoTaskId.toString() + ";");
                    this.selVideoTaskIdSpanList.push((videoTaskId.toString() + ";&nbsp"));
                });
                this.dialogAddOrderVisible = true;
            },
            //关闭下单发货界面
            async closeAddOrder() {
                this.dialogAddOrderSubmitLoding = false;
            },
            //下单发货：选择商品
            async onSelctOrderGoods() {
                this.orderGoodschoiceVisible = true;
                this.$refs.orderGoodschoice.removeSelData();
            },
            //下单发货：选择商品确定
            async onQuerenOrderGoods() {
                var choicelist = await this.$refs.orderGoodschoice.getchoicelist();
                if (choicelist && choicelist.length > 0) {
                    //已存在的不添加
                    var temp = this.addOrderForm.orderGoods;
                    var isNew = true;
                    choicelist.forEach((item) => {
                        isNew = true;
                        temp.forEach(old => {
                            if (old.goodsCode == item.goodsCode) {
                                isNew = false;
                            }
                        });
                        if (isNew) {
                            this.addOrderForm.orderGoods.push({
                                goodsCode: item.goodsCode, goodsName: item.goodsName,
                                shopCode: item.shopId, shopName: item.shopName, proCode: item.shopStyleCode,
                                goodsPrice: item.costPrice ?? 0,
                                goodsQty: 1,
                                goodsAmount: item.costPrice ?? 0
                            });
                        }
                    });
                    this.orderGoodschoiceVisible = false;
                }
            },
            //移除明细
            async onDelDtlGood(index) {
                this.addOrderForm.orderGoods.splice(index, 1);
            },
            async addOrderFormGoodsQtyChange(row) {
                row.goodsAmount = (row.goodsQty * (row.goodsPrice ?? 0)).toFixed(2);
            },
            //新增编辑提交时验证
            addOrderFormValidate: function () {
                let isValid = false
                this.$refs.addOrderForm.validate(valid => {
                    isValid = valid
                })
                return isValid
            },
            //下单发货-保存
            async onAddOrderSave() {
                if (this.addOrderForm.vedioTaskIds == "" || this.addOrderForm.orderGoods.length <= 0) {
                    this.$message({ message: '下单发货信息不完整，请填写', type: "warning" });
                    return;
                }
                if (this.addOrderForm.isZt == 1) {
                    if (this.addOrderForm.warehouse == "" || this.addOrderForm.warehouse == null) {
                        this.$message({ message: '拿样方式=仓库自提时必须填写自提仓库', type: "warning" });
                        return;
                    }
                }
                else {
                    this.addOrderForm.warehouse = null;
                }
                if (this.addOrderForm.isZt == 0) {
                    if (this.addOrderForm.receiverAddressAllInfo == "" || this.addOrderForm.receiverAddressAllInfo == null) {
                        this.$message({ message: '拿样方式=快递寄样时必须选择详细地址', type: "warning" });
                        return;
                    }
                    if (this.addOrderForm.receiverName == "" || this.addOrderForm.receiverPhone == "") {
                        this.$message({ message: '收货人信息错误，请刷新后重试', type: "warning" });
                        return;
                    }
                    if (this.addOrderForm.receiverAddress == "") {
                        this.$message({ message: '下单发货地址错误，请刷新后重试', type: "warning" });
                        return;
                    }
                }
                else {
                    this.addOrderForm.receiverName = "";
                    this.addOrderForm.receiverPhone = "";
                    this.addOrderForm.receiverState = "";
                    this.addOrderForm.receiverStateCode = "";
                    this.addOrderForm.receiverCity = "";
                    this.addOrderForm.receiverCityCode = "";
                    this.addOrderForm.receiverDistrict = "";
                    this.addOrderForm.receiverDistrictCode = "";
                    this.addOrderForm.receiverAddress = "";
                    this.addOrderForm.receiverAddressAllInfo = "";
                }
                this.dialogAddOrderSubmitLoding = true;
                this.dialogAddOrderLoading = true;
                const res = await vedioTaskAddOrderSave(this.addOrderForm);
                this.dialogAddOrderLoading = false;
                if (res?.success) {
                    this.$message({ message: '操作成功，请关注钉钉审批流程', type: "success" });
                    this.onSearch();
                    this.dialogAddOrderVisible = false;
                    this.dialogAddOrderSubmitLoding = false;
                }
                // else {
                //     if (res?.msg)
                //         this.$message({ message: res?.msg, type: "error" });
                //     else
                //         this.$message({ message: '操作失败，请刷新后重试', type: "error" });
                // }
            },
            async dialogOrderDtlColsed() {
                this.xdfhmainlist = [];
                this.xdfhdtllist = [];
            },
            async onShowOrderDtl(row) {
                this.dialogOrderDtlVisible = true;
                this.xdfhmainLoading = true;
                this.xdfhdtlLoading = true;
                var ret = await getVedioTaskOrderListById({ vedioTaskId: row.videoTaskId });
                this.xdfhmainLoading = false;
                this.xdfhdtlLoading = false;
                if (ret?.success && ret.data.length > 0) {
                    ret.data.forEach(f => f.vedioTaskId = row.videoTaskId);
                    this.xdfhmainlist = ret.data;
                    this.xdfhdtllist = ret.data[0].dtlEntities;
                }
            },
            async onxdfhmainCellClick(row) {
                this.xdfhmainlist.forEach(
                    f => {
                        if (f.vedioTaskOrderId == row.vedioTaskOrderId) {
                            this.xdfhdtllist = f.dtlEntities;
                        }
                    }
                );
            },
            async receiverAddressSelChange() {
                this.addressList.forEach(f => {
                    if (f.vedioTaskOrderAddressId == this.addOrderForm.receiverAddressAllInfo && this.addOrderForm.receiverAddressAllInfo != "") {
                        this.addOrderForm.receiverStateCode = f.receiverStateCode;
                        this.addOrderForm.receiverCityCode = f.receiverCityCode;
                        this.addOrderForm.receiverDistrictCode = f.receiverDistrictCode;
                        this.addOrderForm.receiverAddress = f.receiverAddress;
                        if (f.receiverName)
                            this.addOrderForm.receiverName = f.receiverName;
                        if (f.receiverPhone)
                            this.addOrderForm.receiverPhone = f.receiverPhone;

                        return;
                    }
                });
                ;
            },
            async getAddressList() {
                this.addressList = [];
                this.addressListLoading = true;
                var ret = await getVedioTaskOrderAddressList();
                this.addressListLoading = false;
                this.addressList = ret?.data;
                this.receiverAddressList = [];
                if (ret?.success) {
                    this.receiverAddressList = ret.data?.map(item => { return { value: item.vedioTaskOrderAddressId, label: item.receiverAllAddress }; });
                }
            },
            async onAddressSet() {
                this.dialogAddressVisible = true;
                this.getAddressList();
            },
            async onAddAddressSave() {
                this.dialogAddAddressSubmitLoding = true;
                var ret = await saveVedioTaskOrderAddress(this.addAddressForm);
                this.dialogAddAddressSubmitLoding = false;
                if (ret?.success) {
                    this.addAddressForm = {
                        receiverName: "",
                        receiverPhone: "",
                        receiverStateCode: null,
                        receiverCityCode: null,
                        receiverDistrictCode: null,
                        receiverAddress: "",
                    };
                    this.getAddressList();
                }
            },
            async onAddressDelete(row) {
                this.$confirm("是否确定删除, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(async () => {
                    this.addressListLoading = true;
                    var ret = await deleteVedioTaskOrderAddress({ vedioTaskOrderAddressId: row.vedioTaskOrderAddressId });
                    this.addressListLoading = false;
                    this.getAddressList();
                });
            },
            async onShowExproessHttp(row) {
                this.drawervisible = true;
                this.$nextTick(function () {
                    this.$refs.logistics.showlogistics("",row.sampleExpressNo);
                })
            },
            showLogDetail (row) {
                this.dialogHisVisible = true;
                this.sendOrderNoInner = row.sampleRrderNo;
            },
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }

    .el-form-item--mini.el-form-item,
    .el-form-item--small.el-form-item {
        margin-bottom: 15px;
    }
</style>
