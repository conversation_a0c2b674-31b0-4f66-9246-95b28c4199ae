<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
         <el-form-item label="创建时间:">
            <el-date-picker style="width: 330px"
                v-model="filter.timerange"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
           ></el-date-picker>
         </el-form-item>
        <el-form-item label="标题:">
          <el-input v-model="filter.title" placeholder="标题" style="width: 350px"/>
        </el-form-item>
        <el-form-item label="是否已处理:">
          <el-select v-model="filter.isCurrentSelf" placeholder="请选择" style="width: 100px">
            <el-option label="全部" value></el-option>
            <el-option label="是" value="false"></el-option>
            <el-option label="否" value="true"></el-option>
          </el-select>
        </el-form-item>
         <el-form-item label="是否已归档:">
          <el-select v-model="filter.isFinish" placeholder="请选择" style="width: 100px">
            <el-option label="全部" value></el-option>
            <el-option label="是" value="true"></el-option>
            <el-option label="否" value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否合并:">
          <el-select v-model="filter.isMerge" placeholder="请选择" style="width: 100px">
            <el-option label="全部" value></el-option>
            <el-option label="是" value="true"></el-option>
            <el-option label="否" value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='true'
        :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
        <template #column>
          <el-table-column>
            <span>
              <el-button>处理</el-button>
            </span>
          </el-table-column>
        </template>
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
    <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="handVisible" 
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute">
    <div class="block table-wrapper" style="height:550px;padding:15px;">
        <el-scrollbar>
            <el-card v-if="!(handtype==1||handtype==5)" style="padding:1px;">
             <el-descriptions :column="4" size="mini">
                    <el-descriptions-item :span="4" label="标题">{{reportsingle.title}}</el-descriptions-item>
                    <el-descriptions-item :span="4" label="抄送">{{reportsingle.persons}}</el-descriptions-item>
                    <el-descriptions-item label="宝贝ID">{{reportsingle.proCode}}</el-descriptions-item>
                    <el-descriptions-item label="创建人">{{reportsingle.startUserName}}</el-descriptions-item>
                    <el-descriptions-item label="创建时间">{{reportsingle.startDate}}</el-descriptions-item>
                    <el-descriptions-item :span="4" label="附件" >
                        <li v-for="(file, indexf) in reportsingle.files" :key="indexf">
                          <el-link type="primary" size="mini" target=" blank" :download="file.fileName" :href="`${file.filePath}`">{{file.fileName}}</el-link>
                        </li>
                  </el-descriptions-item>
            </el-descriptions>
           </el-card>
         <el-collapse v-model="collapseactiveName" accordion v-if="handtype!=1&&reportsingle.children">
           <el-collapse-item v-for="(child, index1) in reportsingle.children" :key="index1" :title="`${child.startDate} ${child.proCode} ${child.title}`" :name="index1" >
            <el-timeline>
             <el-timeline-item v-for="(item, index) in child.processList" :key="index" :timestamp="`${item.handleDate} ${item.userName}`" placement="top">
              <el-card>
                <div v-if="item.handleDate">
                  <div v-if="item.files&&item.files.length>0">
                    <ui>
                      <li v-for="(file, indexf) in item.files" :key="indexf"><a :href="file.filePath">{{item.fileName}}</a></li>
                    </ui>
                  </div>
                  <div v-html="item.content">{{item.content}}</div>
                </div>
             </el-card>
            </el-timeline-item>
            </el-timeline>
          </el-collapse-item>
         </el-collapse>
         <div v-else-if="handtype!=1">
            <el-card>
              <el-collapse accordion>
                  <el-collapse-item title="报送流程" name="1">
                    <el-timeline>
                      <el-timeline-item v-for="(item, index) in reportsingle.processList" :key="index" :timestamp="`${item.handleDate} ${item.userName}`" placement="top">
                      <el-card>
                          <div v-if="item.handleDate">
                            <div v-html="item.content">{{item.content}}</div>
                          </div>
                      </el-card> 
                      </el-timeline-item>
                  </el-timeline>
                  </el-collapse-item>
              </el-collapse>
              </el-card>
         </div>
         <div v-if="handtype!=4">
           <el-card>
            <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
           </el-card>
         </div>
         <div class="drawer-footer">
            <el-button @click.native="handVisible = false">取消</el-button>
            <my-confirm-button type="submit" :loading="handLoading" @click="onSubmit()" v-if="handtype!=4" />
         </div>
      </el-scrollbar>
    </div>
    </el-drawer>
  </my-container>
</template>

<script>
 //import { Descriptions } from 'element-ui';
//import ElementUI from 'element-ui';
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";
import {createReport,createMergeReport,flowReport,finishReport,getReport,getReportMerge,pageReport} from '@/api/operatemanage/monitorreport'
import {getDirectorAndGroupUserList} from '@/api/operatemanage/base/shop'
//getDirectorAndGroupUserList
import {upLoadFile,upLoadImage} from '@/api/upload/file'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import {formatYesornoBool,formatLink,htmlDecode} from "@/utils/tools";
import {ruleDirectorUser,ruleDirectorGroupUser} from '@/utils/formruletools'
const tableCols =[
      {istrue:true,prop:'proCode',label:'宝贝ID', width:'120',sortable:'custom',type:'html', formatter:(row)=>formatLink(row.proCode,`https://detail.tmall.com/item.htm?id=${row.proCode}`)},
      {istrue:true,prop:'title',label:'报告名称', width:'280'},
      {istrue:true,prop:'startDate',label:'创建时间', width:'160'},
      {istrue:true,prop:'startUserName',label:'创建人', width:'100'},
      {istrue:true,prop:'currentUserName',label:'当前处理人', width:'100'},
      {istrue:true,prop:'arriveDate',label:'流转时间', width:'160'},
      //{istrue:true,prop:'handleDate',label:'处理时间', width:'160'},
      {istrue:true,prop:'isMerge',label:'合并', width:'100',sortable:'custom',formatter:(row)=>formatYesornoBool(row.isMerge)},
      {istrue:true,prop:'isFinish',label:'归档', width:'100',sortable:'custom',formatter:(row)=>formatYesornoBool(row.isFinish)},
     ];
const tableHandles1=[
         {label:"新增报告", handle:(that)=>that.onHand(1)},
         {label:'处理', handle:(that)=>that.onHand(2)},
         {label:'合并报送', handle:(that)=>that.onHand(5)},
         {label:'归档', handle:(that)=>that.onHand(3)},
         {label:'查看', handle:(that)=>that.onHand(4)}
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton},
  data() {
    return {
      that:this,
      filter: {
        timerange:null,
        isCurrentSelf:'true',
        isFinish:null,
        isMerge:null,
        startDate:null,
        endDate:null,
        title:'',
      },
      list: [],
      pager:{OrderBy:"id",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      uploadfilelist:[],
      fileList:[],
      uploadimagelist:[],
      imageList:[],
      autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 16 }},
                                              upload: {props: {onError: function(r){alert('上传失败')}}}}},
               rule:[]
        },
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      handtype:1,
      handVisible: false,
      handLoading: false,
      infoVisible: false,
      reportsingle:{processList:[]},
      formtitle:"新增",
      collapseactiveName: '1',
      groupList:[],
      selids:[],
      csoptions:[]
    }
  },
  async mounted() {
    formCreate.component('editor', FcEditor);
    await this.onSearch();
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async onSearch() {      
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {
        ...pager,
        ...this.pager,
        ... this.filter
      }
      this.listLoading = true
      if (this.filter.timerange) {
        params.startDate = this.filter.timerange[0];
        params.endDate = this.filter.timerange[1];
      }
      const res = await pageReport(params)
      this.listLoading = false
      if (!res?.code) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async initeditor(editor){
     editor.config.uploadImgMaxSize = 3 * 1024 * 1024 
     editor.config.excludeMenus = ['emoticon','video']
     // editor.config.uploadImgAccept = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
     editor.config.uploadImgAccept = []
     // editor.customConfig.debug = true;
     editor.config.customUploadImg =async function (resultFiles, insertImgFn) {
          console.log('resultFiles',resultFiles)
          const form = new FormData();
          form.append("image", resultFiles[0]);
          const res =await upLoadImage(form);
          var url=`${res.data}`
          console.log('url',url)
          insertImgFn(url)
     }
    },
    async uploadfile(parms){
      var item;
      const form = new FormData();
      form.append("file", parms.file);
      const res =await upLoadFile(form);     
      if (res.code==1)item={uid:parms.file.uid,url:res.data,name:parms.file.name};
      else throw new Error(res.msg);
      return item;
    },
    async remoteChaoSong(query) {
      var list= await getDirectorAndGroupUserList({nickName:query })
      var options=[];
      if (list.data&&list.data.length>0) {
        list.data.forEach(f=>{
          var per=this.csoptions.filter(function(item, index) {return item.value== f.key; })
            if(per.length==0)
               this.csoptions.push({value:f.key,label:f.value})
            options.push({value:f.key,label:f.value})
          })
        }
      await this.autoform.fApi.updateRule('cspersons',{options:options})
      await this.autoform.fApi.sync('cspersons')
    },
    async initform(){
       let that=this;
       this.autoform.rule= [{type:'hidden',field:'id',title:'id',value: ''},
       {type:'select',field:'cspersons',title:'抄送',value: [],col:{span:24},options:[],style:"width: 100%;",
       props:{placeholder:"",multiple:true,filterable:true,remote:true,reserveKeyword:true,remoteMethod:async(parms)=>{await that.remoteChaoSong(parms)}}},
                     {type:'select',field:'toUserId1',title:'报送给组长',value: "",col:{span:8},...await ruleDirectorGroupUser(),update(val, rule){ if (val) {that.updateruledirector(val)}}},
                     {type:'select',field:'toUserId2',title:'报送给负责人',value:"",col:{span:8}},
                     {type:'input',field:'proCode',title:'宝贝ID',value: '',col:{span:8},validate: [{type: 'string', required: true, message:'请输入宝贝ID'}]},
                     {type:'input',field:'title',title:'标题',value: '',col:{span:24},validate: [{type: 'string', required: true, message:'请输入标题'}]},
                     {type:'upload',field:'upfile',title:'附件',value: [],props:{disabled: false,uploadType:'file', action: '/',multiple:true,showFileList:true,multiple:false,
                          httpRequest : async(parms)=> { var item = await that.uploadfile(parms); if (item) that.uploadfilelist.push(item)},
                          onSuccess:function (res, file,fileList ) {},
                          onError:function ( error, file, fileList) {console.log('error',error);},
                          onRemove:function ( file,fileList ) {console.log('fileList',fileList);
                          for(var i=0;i<that.uploadfilelist.length;i++){
                            if (fileList.map(a => a.uid).indexOf(that.uploadfilelist[i].uid)==-1) {
                              that.uploadfilelist.splice(i,1); 
                              i--;
                            }
                          }},
                          }},
                     {type:'editor',field:'content',title:'报告内容2',value:'',col:{span:24},validate: [{type: 'string', required: true, message:'必填'}],
                      props:{init:async(editor)=>{await that.initeditor(editor) }}}]
       this.autoform.rule.forEach(f=>{
            if (f.field=='toUserId1')  f.validate=[]
            if (f.field=='toUserId2')  f.validate=[]
        })
    },
    async initcreatemergeform(){
       let that=this;
       this.autoform.rule= [{type:'hidden',field:'reportIds',title:'reportIds',value: []},
       {type:'select',field:'cspersons',title:'抄送',value: [],col:{span:24},options:[],style:"width: 100%;",
       props:{placeholder:"",multiple:true,filterable:true,remote:true,reserveKeyword:true,remoteMethod:async(parms)=>{await that.remoteChaoSong(parms)}}},
                     {type:'select',field:'toUserId1',title:'报送给组长',value: "", col:{span:12},...await ruleDirectorGroupUser(),update(val, rule){ if (val) {that.updateruledirector(val)}}},
                     {type:'select',field:'toUserId2',title:'报送给负责人',value:"",col:{span:12}},
                     {type:'input',field:'title',title:'标题',value: '',validate: [{type: 'string', required: true, message:'请输入标题'}]},
                     {type:'upload',field:'upfile',title:'附件',value: [],props:{disabled: false,uploadType:'file', action: '/',multiple:true,showFileList:true,multiple:false,
                          httpRequest:async(parms)=> { var item = await that.uploadfile(parms); if (item) that.uploadfilelist.push(item); },
                          onSuccess:function (res, file,fileList ) { },
                          onError:function ( error, file, fileList) {console.log('error',error);},
                          onRemove:function ( file,fileList ) {console.log('fileList',fileList);
                          for(var i=0;i<that.uploadfilelist.length;i++ ){
                            if (fileList.map(a => a.uid).indexOf(that.uploadfilelist[i].uid)==-1) {
                              that.uploadfilelist.splice(i,1); 
                              i--;
                            }
                          }},
                          }},
                     {type:'editor',field:'content',title:'报告内容1',value:'',col:{span:24},validate: [{type: 'string', required: true, message:'必填'}],
                     props:{init:async(editor)=>{await that.initeditor(editor) }}}]
       this.autoform.rule.forEach(f=>{
            if (f.field=='toUserId1')  f.validate=[]
            if (f.field=='toUserId2')  f.validate=[]
        })
    },
    async inithandform(){
       let that=this;
       this.autoform.rule= [{type:'hidden',field:'reportId',title:'reportId',value: ''},
                      {type:'select',field:'toUserId1',title:'报送给组长',value: "",col:{span:12}, ...await ruleDirectorGroupUser(),update(val, rule){ if (val) {that.updateruledirector(val)}}},
                      {type:'select',field:'toUserId2',title:'报送给负责人',value:"",col:{span:12}},
                      {type:'editor',field:'content',title:'处理内容',value:'',col:{span:24},validate: [{type: 'string', required: true, message:'必填'}],
                       props:{init:async(editor)=>{await that.initeditor(editor) }}}]
       this.autoform.rule.forEach(f=>{
           if (f.field=='toUserId1')  f.validate=[]
           if (f.field=='toUserId2')  f.validate=[]
        })
    },
    async initfinshform(){
       let that=this;
       this.autoform.rule= [{type:'hidden',field:'reportId',title:'reportId',value: ''},
                     {type:'editor',field:'content',title:'归档内容',value:'',col:{span:24},validate: [{type: 'string', required: true, message:'必填'}],
                      props:{init:async(editor)=>{await that.initeditor(editor) }}}]
    }, 
    async onHand(type) {
      this.uploadfilelist=[];
      if (type>1&&(!this.selids||this.selids.length==0)) {
         this.$message({ message: "请选择报告！", type: "warning" });
         return;
        }
      else if (type>1&& type!=5&&  this.selids.length>1) {
         this.$message({ message: "只能选择一行报告！", type: "warning" });
         return;
        }
      else if (type==5&&  this.selids.length<2) {
         this.$message({ message: "合并报告需要至少选择2个报告！", type: "warning" });
         return;
        }
      this.handVisible=true;
      this.handtype=type
      if (type==1) {
          this.formtitle='新增报告';
          await this.initform();
      }
      else{
        if (type==2) this.formtitle='处理报告';
        if (type==3) this.formtitle='归档报告';
        if (type==4) this.formtitle='详情';
        if (type==5) this.formtitle='合并报送';
        this.reportsingle={processList:[]};
        var reportid=this.selids[0]
        var res;
        var rids=this.selids.join(',');
        if (type==5) res = await getReportMerge({ids:rids});
        else         res = await getReport({id:reportid});
        if(res.code==1){
            this.reportsingle=res.data;
            if (type==4) return
            else if (type==2) this.inithandform();
            else if (type==3) this.initfinshform();
            else if (type==5) this.initcreatemergeform();
            var arr = Object.keys(this.autoform.fApi);
            if(arr.length >0){ 
                this.autoform.fApi.resetFields()
                await this.autoform.fApi.setValue({reportId:reportid,reportIds:this.selids})
             }
        }
      }
    },
    async onSubmit() {
      const formData = this.autoform.fApi.formData();
      formData.reportId=this.reportsingle.id;
      console.log('formData',formData)
      this.handLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          formData.id=formData.id?formData.id:0;
          formData.Enabled=true;
          if (!formData.toUserId1&&!formData.toUserId2&&this.handtype!=3) {
            this.$message({ message: "报送给组长、报送给负责人二者必选一", type: "warning" });
            return;
          }
          if (formData.toUserId2){
               formData.toUserId=formData.toUserId2
               formData.toUserName= this.autoform.rule.filter((item, index,arr)=>{
                            return item.field=='toUserId2'
                    })[0].options.filter((item, index,arr)=>{return item.value== formData.toUserId})[0].label;
          }
          else if (formData.toUserId1){
               formData.toUserId=formData.toUserId1
               formData.toUserName= this.autoform.rule.filter((item, index,arr)=>{
                            return item.field=='toUserId1'
                    })[0].options.filter((item, index,arr)=>{return item.value== formData.toUserId})[0].label;
          }
          var res ;
          if (this.handtype==1||this.handtype==5) {
            formData.persons=[]
            formData.cspersons.forEach(f=>{
              var per= this.csoptions.filter(function(item, index) {return item.value== f; })
              formData.persons.push({userId:per[0].value,userName:per[0].label})
            })
            formData.files=[];
            if (this.uploadfilelist.length>0) {
              this.uploadfilelist.forEach(f=>{
                  formData.files.push({fileUrl:f.url,fileName:f.name })
              })
            }
          }
          if (this.handtype==1)      res = await createReport(formData);
          else if (this.handtype==2) res = await flowReport(formData);
          else if (this.handtype==3) res = await finishReport(formData);
          else if (this.handtype==5) res = await createMergeReport(formData);
          if(res.code==1){
            this.getlist();
            this.handVisible=false;
          }
        }else{ }
     })
     this.handLoading=false;
    },
  async updateruledirector(groupid){
      await this.autoform.fApi.setValue({toUserId2:null})
      await this.autoform.fApi.updateRule('toUserId2',{... await ruleDirectorUser({groupuserid:groupid})})
      this.autoform.rule.forEach(f=>{
          if (f.field=='toUserId1')  f.validate=[]
          if (f.field=='toUserId2')  f.validate=[]
      })
      await this.autoform.fApi.sync('toUserId2')
  },
  async onShow(){
      this.infoVisible=true;
    },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
  }
}
</script>
<style>
._fc-upload .fc-upload-btn, ._fc-upload .fc-files {
    display: block;
    width: 58px;
    height: 20px;
    text-align: center;
    line-height: 1px;
    border: 1px solid #c0ccda;
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
    position: relative;
    -webkit-box-shadow: 2px 2px 5px rgb(0 0 0 / 10%);
    box-shadow: 2px 2px 5px rgb(0 0 0 / 10%);
    /* margin-right: 4px; */
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 2px;
}

.drawer-footer {
    position: relative;
    left: 0px;
    bottom: 0px;
    border-top: 1px solid #e9e9e9;
    padding: 10px 30px 10px 0;
    /* padding: 16px; */
    background: white;
    text-align: right;
    z-index: 1;
}

.table-wrapper {
    width: 100%;
    height: calc(100% - 35px);
    margin: 0;
 }
</style>
<style scoped> 
.el-drawer{
  height: 80%; 
  overflow: auto;
}
.el-scrollbar__wrap {
  height: 100%;
  margin: 15px;
}
/* table 样式 */
 table {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
table td,
table th {
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
  padding: 3px 5px;
}
table th {
  border-bottom: 2px solid #ccc;
  text-align: center;
}

/* blockquote 样式 */
blockquote {
  display: block;
  border-left: 8px solid #d0e5f2;
  padding: 5px 10px;
  margin: 10px 0;
  line-height: 1.4;
  font-size: 100%;
  background-color: #f1f1f1;
}

/* code 样式 */
code {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  background-color: #f1f1f1;
  border-radius: 3px;
  padding: 3px 5px;
  margin: 0 3px;
}
pre code {
  display: block;
}

/* ul ol 样式 */
ul, ol {
  margin: 10px 0 10px 20px;
}
</style>
