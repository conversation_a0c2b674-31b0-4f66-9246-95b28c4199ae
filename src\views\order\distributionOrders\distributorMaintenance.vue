<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-input v-model.trim="ListInfo.shipperFxName" placeholder="货主分销名称" maxlength="50" clearable
                    class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="handleAdd">新增</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' id="20250610142932"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false" border :isNeedExpend="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="120">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="remove(row.shipperFxName)">删除</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="新增货主分销" :visible.sync="addVisible" width="20%" v-dialogDrag append-to-body>
            <el-form :model="addInfo" ref="refAddForm" label-width="120px" :rules="addRules" class="demo-ruleForm">
                <el-form-item label="货主分销名称" prop="shipperFxName">
                    <el-input v-model.trim="addInfo.shipperFxName" placeholder="请输入货主分销名称" maxlength="50" clearable
                        style="width: 90%;" />
                </el-form-item>
                <el-form-item label="供应商" prop="supplierId">
                  <el-select v-model="addInfo.supplierId" ref="refSupplierId" clearable filterable remote @change="handleSupplierChange"
                    reserve-keyword placeholder="请模糊输入并选择供应商" :remote-method="remoteSearchSupplier" :loading="supplierLoading" style="width:90%">
                    <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
                <el-form-item label="是否月结" prop="isMonthSettlement">
                  <el-select v-model="addInfo.isMonthSettlement" placeholder="请选择是否月结" style="width:90%" clearable filterable>
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                  </el-select>
                </el-form-item>
            </el-form>
            <div style="display: flex;align-items: center;justify-content: center;margin-top: 40px;">
                <el-button type="primary" @click="handleAddConfirm" style="margin-right: 10px;">确定</el-button>
                <el-button @click="addVisible = false">取消</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { GetShipperFxNameListAsync, DeleteShipperFxName, AddShipperFxName } from '@/api/order/shipperFxOrder';
import { pageSupplierAll } from '@/api/inventory/supplier'
import dateRange from "@/components/date-range/index.vue";
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shipperFxName', label: '货主分销名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'supplierName', label: '供应商', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'isMonthSettlement', label: '是否月结', formatter: (row) => { return row.isMonthSettlement ? '是' : '否'}},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '创建时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '创建人', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'createdTime',
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
            },
            supplierOptions: [],
            supplierLoading: false,
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            addVisible: false,
            addInfo: {
                shipperFxName: null,
                supplierId: null,
                isMonthSettlement: null,
                supplierName: null
            },
            addRules: {
                shipperFxName: [{ required: true, message: '请输入货主分销名称', trigger: 'blur' }],
                supplierId: [{ required: true, message: '请选择供应商', trigger: 'blur' }],
                isMonthSettlement: [{ required: true, message: '请选择是否月结', trigger: 'blur' }],
            }
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        handleSupplierChange(e){
          this.addInfo.supplierName = this.supplierOptions.find(item => item.value == e)?.label
        },
        async remoteSearchSupplier(parm) {
            this.supplierOptions = [];
            if (!parm) {
                return;
            }
            var options = [];
            const res = await pageSupplierAll({ currentPage: 1, pageSize: 50, name: parm });
            res?.data?.list.forEach(f => {
                options.push({ value: f.supplier_id, label: f.name })
            });
            this.supplierOptions = options;
        },
        async handleAddConfirm() {
            this.$refs.refAddForm.validate(async (valid) => {
                if (!valid) return
                if (!this.addInfo.shipperFxName) {
                    this.$message.error('货主分销名称不能为空')
                    return
            }
            this.$set(this.addInfo, 'shipperFxName', this.addInfo.shipperFxName.trim())
            const { success } = await AddShipperFxName(this.addInfo)
            if (!success) return
            this.$message({
                type: 'success',
                message: '新增成功!'
            });
                this.addVisible = false
                this.addInfo.shipperFxName = ''
                await this.getList()
            })
        },
        handleAdd() {
            this.addVisible = true
            this.supplierOptions = []
            this.$nextTick(() => {
                this.$refs.refAddForm?.resetFields();
                this.$refs.refAddForm?.clearValidate()
            })
        },
        remove(shipperFxName) {
            this.$confirm('此操作将永久删除该货主, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await DeleteShipperFxName({ shipperFxName })
                if (!success) return
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
                await this.getList()
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await ExportExpressInterceptList(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '快递拦截明细' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetShipperFxNameListAsync(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
