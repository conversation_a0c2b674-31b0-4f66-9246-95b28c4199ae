<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs style="height:94%;">
            <el-tab-pane label="一站式智投汇总" v-if="checkPermission(['api:financial:Shizhitouday:ImportShizhitouDayAsync'])"
                name="tab0" style="height: 100%;">
                <shizhitou ref="shizhitou" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="直播-标准" v-if="checkPermission(['api:financial:Tuijianday:ImportTuijianDayAsync'])"
                name="tab1" style="height: 100%;">
                <tuijian ref="tuijian" :tablekey='tablekey1' style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="直播-全站" v-if="checkPermission(['api:financial:Tuijianday:ImportTuijianDayAsync'])"
                name="tab22" style="height: 100%;" lazy>
                <liveSiteWide ref="liveSiteWide" :tablekey='tablekey1' style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="阿里妈妈" v-if="checkPermission(['api:financial:Taobaokeday:ImportTaobaokeDayAsync'])"
                name="tab2" style="height: 100%;">
                <taobaoke ref="taobaoke" :tablekey='tablekey2' style="height: 100%;" />
            </el-tab-pane>

            <el-tab-pane label="直通车" v-if="checkPermission(['api:financial:Zhitongcheday:ImportZhitongcheDayAsync'])"
                name="tab4" style="height: 100%;">
                <zhitongche ref="zhitongche" :tablekey='tablekey3' style="height: 100%;" />
            </el-tab-pane>

            <el-tab-pane label="引力魔方"
                v-if="checkPermission(['api:financial:YinglimofangDay:ImportYinglimofangDayAsync'])" name="tab16"
                style="height: 100%;">
                <yinglimofangday ref="yinglimofangday" :tablekey='tablekey4' style="height: 100%;" />
            </el-tab-pane>

            <el-tab-pane label="万相台" v-if="checkPermission(['api:financial:WanxiangtaiDay:ImportWanxiangtaiDayAsync'])"
                name="tab17" style="height: 100%;">
                <wanxiangtaiday ref="wanxiangtaiday" :tablekey='tablekey5' style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="万相台-优惠券" name="tab19" style="height: 100%;">
              <wanxiangtaiCoupon ref="wanxiangtaiCoupon" :tablekey="'tablekey19'" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="首单礼金"
                v-if="checkPermission(['api:financial:ShoudanlijingDay:ImportShoudanlijingDayAsync'])" name="tab18"
                style="height: 100%;">
                <shoudanlijingday ref="shoudanlijingday" :tablekey='tablekey6' style="height: 100%;" />
            </el-tab-pane>

            <el-tab-pane label="淘特推广"
                v-if="checkPermission(['api:financial:Taotetuiguangday:ImportTaotetuiguangDayAsync'])" name="tab3"
                style="height: 100%;">
                <taotetuiguang ref="taotetuiguang" :tablekey='tablekey7' style="height: 100%;" />
            </el-tab-pane>

            <el-tab-pane label="特殊单费用" v-if="checkPermission(['api:financial:Dahuixiongday:ImportDahuixiongDayAsync'])"
                name="tab6" style="height: 100%;">
                <dahuixiong ref="dahuixiong" :tablekey='tablekey8' style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="淘礼金" v-if="checkPermission(['api:financial:Taolijingday:ImportTaolijingDayAsync'])"
                name="tab7" style="height: 100%;">
                <taolijing ref="taolijing" :tablekey='tablekey9' style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="营销费用" v-if="checkPermission(['api:financial:Yingxiaoday:ImportYingxiaoDayAsync'])"
                name="tab8" style="height: 100%;">
                <yingxiao ref="yingxiao" :tablekey='tablekey10' style="height: 100%;" />
            </el-tab-pane>




            <el-tab-pane label="核算" name="tab10" style="height: 100%;">
                <accountresult ref="accountresult"  :tablekey='tablekey11'  style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="异常数据表" name="tab15" style="height: 100%;">
                <unusual ref="unusual" style="height: 100%;" />
            </el-tab-pane>

            <el-tab-pane label="全站推广" v-if="checkPermission(['api:financial:WanxiangtaiDay:ImportWanxiangtaiDayAsync'])"
                name="tab20" style="height: 100%;">
                <SiteWidePromotionInfo ref="SiteWidePromotionInfo" :tablekey='tablekey5' style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="天猫品牌新享预估"
                name="tab21" style="height: 100%;">
                <pinPaiNewEnjoy ref="pinPaiNewEnjoy" :tablekey='tablekey55' style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="淘宝品牌新享-后台"
                name="tab23" style="height: 100%;">
                <taobaoBrandNewEnjoyment ref="taobaoBrandNewEnjoyment" :tablekey='tablekey56' style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="淘宝星河" name="tab24" style="height: 100%;">
                <TaobaoStarRiver :filter="filter" ref="refTaobaoStarRiver" style="height: 100%;"/>
            </el-tab-pane>
        </el-tabs>
    </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";
import shizhitou from '@/views/financial/yyfydayreport/shizhitou'
import tuijian from '@/views/financial/yyfydayreport/tuijian'
import taobaoke from '@/views/financial/yyfydayreport/taobaoke'
import yinglimofangday from '@/views/financial/yyfydayreport/yinglimofangday'
import wanxiangtaiday from '@/views/financial/yyfydayreport/wanxiangtaiday'
import taotetuiguang from '@/views/financial/yyfydayreport/taotetuiguang'
import zhitongche from '@/views/financial/yyfydayreport/zhitongche'
import dahuixiong from '@/views/financial/yyfydayreport/dahuixiong'
import taolijing from '@/views/financial/yyfydayreport/taolijing'
import yingxiao from '@/views/financial/yyfydayreport/yingxiao'
import accountresult from '@/views/financial/yyfydayreport/accountresult'
import pingduoduo from '@/views/financial/yyfydayreport/pingduoduo'
import unusual from '@/views/financial/yyfydayreport/unusual'
// import unusual from '@/views/order/procodesimilarity'
import shoudanlijingday from '@/views/financial/yyfydayreport/shoudanlijingday'
import SiteWidePromotionInfo from '@/views/financial/yyfydayreport/SiteWidePromotionInfo'
import pinPaiNewEnjoy from '@/views/financial/yyfydayreport/pinPaiNewEnjoy'
import taobaoBrandNewEnjoyment from '@/views/financial/yyfydayreport/taobaoBrandNewEnjoyment'
import liveSiteWide from '@/views/financial/yyfydayreport/liveSiteWide'
import wanxiangtaiCoupon from '@/views/financial/yyfydayreport/wanxiangtaiCoupon'
 import TaobaoStarRiver from '@/views/financial/yyfydayreport/taobaoStarRiver'

export default {
    name: "Users",
    components: { MyContainer, shoudanlijingday, shizhitou, tuijian, taobaoke, taotetuiguang, zhitongche, dahuixiong, taolijing, yingxiao, accountresult
        , pingduoduo, unusual, yinglimofangday, wanxiangtaiday ,SiteWidePromotionInfo,pinPaiNewEnjoy,liveSiteWide,taobaoBrandNewEnjoyment,wanxiangtaiCoupon,TaobaoStarRiver},
    data() {
        return {
            that: this,
            Filter: {
            },
            shopList: [],
            pageLoading: false,
            userList: [],
            groupList: [],
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            tablekey1:"tablekey1",
            tablekey2:"tablekey2",
            tablekey3:"tablekey3",
            tablekey4:"tablekey4",
            tablekey5:"tablekey5",
            tablekey6:"tablekey6",
            tablekey7:"tablekey7",
            tablekey8:"tablekey8",
            tablekey9:"tablekey9",
            tablekey10:"tablekey10",
            tablekey11:"tablekey11",
        };
    },

    methods: {
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
