<template>
  <container>
    <template #header>
      <div style="display:flex;flex-direction: column;">
        <el-button-group>
          <el-button style="margin-top:0px">
              <button style="padding: 0;width: 410px; border: none;">
              <el-date-picker style="width: 410px" v-model="filter1.timerange" @change="changeDate()" type="daterange"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" clearable></el-date-picker>
              </button>

              <button style="padding: 0;width: 200px; border: none;"> 
              <inputYunhan title="宝贝ID"  placeholder="宝贝ID" :maxlength="200" :inputt.sync="filter1.productCodes" :clearable="true" @callback="callbackProCode"></inputYunhan>
              </button> 

              <button style="padding: 0;width: 150px; border: none;">
              <el-input style="width: 150px;" v-model="filter1.goodsCode"
              v-model.trim="filter1.goodsCode" :maxlength=100 placeholder="商品编码" @keyup.enter.native="onSearch"
              clearable />
              </button>
              <el-select v-model="filter1.skuSealStatus" placeholder="预售类型" clearable>
                <el-option label="所有" value></el-option>
                <el-option v-if="filter.type=='正常'" label="上架" value='1'></el-option>
                <el-option v-if="filter.type=='正常'" label="非预售" value='2'></el-option>
                <el-option v-if="filter.type=='预售'"  label="规格预售" value="3"></el-option>
                <el-option v-if="filter.type=='预售'"  label="时段预售" value="4"></el-option>
              </el-select>
            <el-button type="primary" @click="onSearch()" style="margin-left: 10px;">查询</el-button>
          </el-button>
        </el-button-group> 
      </div>
    </template>
    <vxetablebase :id="'pass202408041559'" :tableHandles='tableHandles' :tableData='list'
            :tableCols='tableCols' :tablefixed='true' :loading='listLoading' :border='true' :that="that" ref="vxetable"
            @sortchange='sortchange' :isIndexFixed="false" height="100%" >
        </vxetablebase> 
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>
  </container>
</template>
<script>
import { pagePddSafeInventoryPassLogAsync } from '@/api/inventory/pddSafeInventorys'
import container from "@/components/my-container";
import { Loading } from 'element-ui';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import inputYunhan from '@/components/Comm/inputYunhan.vue'
let loading;
const startLoading = () => {
  loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};

const tableCols = [ 
  { istrue: true, prop: 'shopCode', sortable: 'custom', label: '店铺', width: '200' ,type: 'custom' ,formatter:(row)=>row.shopName},
  { istrue: true, prop: 'productCode', sortable: 'custom', label: '宝贝ID', width: '120' },
  { istrue: true, prop: 'goodsCode', sortable: 'custom', label: '商品编码', width: '120' },
  { istrue: true, prop: 'images', label: '图片', type: 'images', width: '100', align: 'center' },
  { istrue: true, prop: 'goodsName', label: '商品名称' },
  { istrue: true, prop: 'skuSealStatus', sortable: 'custom', label: '预售类型', width: '120' },
  { istrue: true, prop: 'modifiedTime', sortable: 'custom', label: '修改时间', width: '120' },
  { istrue: true, prop: 'isError',type: 'custom', sortable: 'custom', label: '状态', width: '120',formatter:(row)=>row.isError == 1 ?"失败":(row.isError==0?"成功":"") },
  { istrue: true, prop: 'errorInfo',  label: '详情', width: '120' },
];
const tableHandles = [
];

export default {
  name: "Pddsafeinventory",
  components: { container, vxetablebase,inputYunhan },
  props:{
        filter:{ type: Object, default:()=>{ } },
  },
  data() {
    return {
      that: this,
      dialog: {
        visible: false,
        filter: {
          goodsCode: null
        }
      },
      filter1: {
        goodsCode: null,
        startTime: null,
        endTime: null,
        timerange: null,
        skuSealStatus:null,
        productCodes:null
      },
      tableCols: tableCols,
      tableHandles: tableHandles,
      list: [],
      total: 0,
      pager: { OrderBy: "goodsCode", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      summaryarry: {},
      selids: [],
      pickerOptions: {
        shortcuts: [{
          text: '前一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate() - 1);
            const date2 = new Date(); date2.setDate(date2.getDate() - 1);
            picker.$emit('pick', [date1, date2]);
            window.setshowprogress(false);
          }
        }]
      }
    };
  },
  async mounted() {
    this.onSearch();
  },
  async created() {

  },
  methods: {
    async sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      await this.onSearch();
    },
    async onSearch() {
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      let pager = this.$refs.pager.getPager()
      this.filter1.startTime = null;
      this.filter1.endTime = null;
      if (this.filter1.timerange) {
        this.filter1.startTime = this.filter1.timerange[0];
        this.filter1.endTime = this.filter1.timerange[1];
      } 
      this.listLoading = true;
      let params = { ...pager, ...this.pager, ... this.filter ,...this.filter1}
      let res = await pagePddSafeInventoryPassLogAsync(params);
      this.listLoading = false;
      this.list = res.data?.list;
      this.total = res.data?.total;
    },
    async callbackProCode(val){
      this.filter1.productCodes = val
    }
  }
}

</script>
<style></style>