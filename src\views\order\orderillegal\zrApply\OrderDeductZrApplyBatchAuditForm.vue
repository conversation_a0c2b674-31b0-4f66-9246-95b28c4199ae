<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" ref="form" label-width="120px" label-position="right" >    
                <el-row >
                    <el-col :span="24" style="height:400px">
                        <ces-table ref="table" :that="that" :isIndex="true" :hasexpand="false"  :isSelectColumn="false"
                             :tableData="list" :tableCols="tableCols" :isSelection="false" >
                        </ces-table>

                    </el-col>
                </el-row> 
                 <el-row>
                    <el-col :span="24">
                        <el-form-item label="定责方式：" prop="appointByAudtior" :rules="[
                        { required: true, message: '请选择定责方式', trigger: ['blur', 'change'] }    
                        ]">
                            <el-radio-group v-model="form.appointByAudtior">
                                <el-radio :label="0">按申诉填报新责任执行</el-radio>
                                <el-radio :label="1">指定新责任部门责任人</el-radio>                                
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row> 
                 
                 <el-row  v-if="form.appointByAudtior==1">
                   
                    <el-col :span="6" >
                        <el-form-item label="新责任部门：" prop="newZrDeptAction" :rules="[
                        { required: true, message: '请选择新责任部门', trigger: ['blur', 'change'] }    
                        ]">
                            <el-select  v-model="form.newZrDeptAction" @change="newZrDeptActionChange">
                                <el-option v-for="item in zrDeptActions" :value="item.zrDeptAction" :label="item.zrDeptAction"></el-option>
                            </el-select>                        
                        </el-form-item>
                    </el-col>   
                    <!-- <el-col :span="6" v-if="form.appointByAudtior==1">
                        <el-form-item label="新责任原因2：" prop="newZrReason" :rules="[
                        { required: true, message: '请选择新责任原因2', trigger: [ 'change'] }    
                        ]">
                            <el-select   v-model="form.newZrReason">
                                <el-option v-for="item in zrReasons" :value="item" :label="item"></el-option>
                            </el-select>       
                        </el-form-item>
                    </el-col> -->
                    <el-col :span="6" >
                        <el-form-item label="新责任人：" prop="newMemberName" >
                            <template >
                                <el-select v-if="form.newZrDept=='采购'" v-model="form.newMemberId" filterable @change="newMemberIdChange">
                                    <el-option v-for="item in brandlist" :key="item.key" :label="item.value"  :value="item.key"/>
                                </el-select>
                                <el-select v-else-if="form.newZrDept=='运营'" v-model="form.newMemberId" filterable @change="newMemberIdChange">
                                    <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
                                </el-select>  
                                <YhUserelector v-else-if="form.newZrDept!='机器人' && form.newZrDept!='外部'  && form.newZrDept!='快递'" 
                                            :value.sync="form.newMemberDDUserId" :text.sync="form.newMemberDDUserName"
                                        ></YhUserelector>
                                <el-input  v-else  v-model="form.newMemberName" clearable maxlength="10"  />
                            </template>                            
                         
                        </el-form-item> 
                    </el-col>

                    <el-col :span="6" >
                        <el-form-item label="新责任类型：" prop="newZrType1" :rules="[
                                                        { required: true, message: '请选择新责任类型', trigger: ['blur', 'change'] }    
                                                        ]">
                                    <el-select  v-model="form.newZrType1" @change="newZrType1Change">
                                        <el-option v-for="(v,key) in deductOrderZrType12Tree" :key="key" :label="key" :value="key"></el-option>
                                    </el-select>                                 
                                </el-form-item>
                    </el-col>    
                    <el-col :span="6" >
                        <el-form-item label="新责任原因：" prop="newZrType2" :rules="[
                                                        { required: true, message: '请选择新责任原因', trigger: ['blur', 'change'] }    
                                                        ]">
                                    <el-select    v-model="form.newZrType2" >
                                        <el-option v-for="item in deductOrderZrType12Tree[form.newZrType1]" :key="item.zrType2" :label="item.zrType2"  :value="item.zrType2"></el-option>
                                    </el-select>                                   
                                </el-form-item>
                    </el-col>    

                </el-row> 
                 <el-row>
                    <el-col :span="24">
                        <el-form-item label="审核意见：" :rules="[
                        { required: true, message: '请填写审核意见', trigger: ['blur'] }    
                        ]">
                            <el-input  type="textarea"  :rows="3" v-model="form.auditRemark" clearable maxlength="100" show-word-limit />                                                  
                        </el-form-item>
                    </el-col>     
                </el-row>                      
               
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">  
                    <el-button @click="onClose">关闭</el-button>   
                    <el-button  type="primary" @click="audit(true)">审核通过</el-button>
                    <el-button  type="danger" @click="audit(false)">审核驳回</el-button>
                </el-col>
            </el-row>
        </template>

    </my-container>
</template>
<script>  

    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode ,DeductOrderZrDeptActions,DeductOrderZrReasons
        ,DeductOrderZrType12,DeductOrderZrType12TreeBz } from "@/utils/tools";
    import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import cesTable from "@/components/Table/table.vue";
    import { GetDeductZrAppeal4CRUD,SaveDeductZrAppeal,AuditDeductZrAppeal,BatchAuditDeductZrAppeal } from "@/api/order/orderdeductmoney";
    import {getAllWarehouse,getAllProBrand} from '@/api/inventory/warehouse'

    import YhUserelector from '@/components/YhCom/yh-userselector.vue'
    
    import {
        getDirectorList,
        getDirectorGroupList,
        getProductBrandPageList,
        getList as getshopList,
    } from "@/api/operatemanage/base/shop";
    


    const fmtApplyState=function(val){
        if(val==-1) return "已拒绝";
        else if(val==0) return "待申请";
        else if(val==1) return "申请中";
        else if(val==2) return "已通过";
        else if(val==3) return "已指派";
        return val;
    }

    const tableCols = [
        { istrue: true, prop: 'deductPlatform', label: '平台', width: '60', sortable: true, formatter: (row) => row.deductPlatformText},
        { istrue: true, prop: 'orderNo', label: '订单编号', width: '180', sortable: true},
        { istrue: true, prop: 'deductOccurTime', label: '扣款日期', width: '90', sortable: true, formatter: (row) => formatTime(row.deductOccurTime, "YYYY-MM-DD") },
        { istrue: true, prop: 'illegalType', label: '平台原因', width: 'auto', sortable: true, formatter: (row) => {return row.illegalTypeText;}},

        { istrue: true, prop: 'orgZrType1', label: '原责任类型', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'orgZrType2', label: '原责任原因', width: '90', sortable: 'custom' },


        { istrue: true, prop: 'orgZrDeptAction', label: '原责任部门', width: '98', sortable: true },
        { istrue: true, prop: 'orgZrReason', label: '原责任原因2', width: '90', sortable: true },
        { istrue: true, prop: 'orgMemberName', label: '原责任人', width: '76', sortable: 'custom' },
        { istrue: true, prop: 'orgZrSetTime', label: '原责任设定时间', width: '128', sortable:true, formatter: (row) => formatTime(row.orgZrSetTime, "YYYY-MM-DD HH:mm") },
        { istrue: true, prop: 'orgZrConditionFullName', label: '原责任规则', width: '200', sortable: true },
        
        
        { istrue: true, prop: 'applyUserName', label: '申请人', width: '76', sortable: true },
        { istrue: true, prop: 'applyTime', label: '申请时间', width: '128', sortable: true, formatter: (row) => formatTime(row.applyTime, "YYYY-MM-DD HH:mm") },

        
        { istrue: true, prop: 'newZrType1', label: '新责任类型', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'newZrType2', label: '新责任原因', width: '90', sortable: 'custom' },


        { istrue: true, prop: 'newZrDeptAction', label: '新责任部门', width: '98', sortable: true },
        { istrue: true, prop: 'newZrReason', label: '新责任原因2', width: '90', sortable: true },
        { istrue: true, prop: 'newZrConditionFullName', label: '申诉理由', minwidth: '160', sortable: true },
        { istrue: true, prop: 'newMemberName', label: '新责任人', width: '76', sortable: 'custom' },

        { istrue: true, prop: 'applyContent', label: '申请内容', width: '128', sortable: true},
        // { istrue: true, prop: 'auditUserName', label: '审核人', width: '80', sortable: 'custom' },
        // { istrue: true, prop: 'auditTime', label: '审核时间', width: '128', sortable: 'custom', formatter: (row) => !!row.auditTime ? formatTime(row.auditTime, "YYYY-MM-DD HH:mm"):"" },
        // { istrue: true, prop: 'auditRemark', label: '审核意见', width: '120', sortable: 'custom' },      
        // { istrue: true, prop: 'applyState', label: '审核状态', width: '86', sortable: 'custom', formatter: (row) => fmtApplyState(row.applyState)},      
    ]

    export default {
        name: "OrderDeductZrApplyBatchAuditForm",
        components: { MyContainer, MyConfirmButton,cesTable ,YhUserelector},
        data() {
            return {              
                that: this,
                mode:3,
                list:[],              
                zrDeptActions:DeductOrderZrDeptActions,
                zrReasons:DeductOrderZrReasons,

                deductOrderZrType12:DeductOrderZrType12,
                deductOrderZrType12Tree:DeductOrderZrType12TreeBz,
               

                brandlist: [], 
                directorList: [],

                form: {
                    newZrType1:null,
                    newZrType2:null,
                    appointByAudtior:0,
                    newMemberName:"",
                    newMemberId:null,
                    newMemberDDUserId:"",
                    newMemberDDUserName:""
                },
                tableCols:tableCols,
                //summaryarry: {},
                pageLoading: false,
                curRow: null,
                formEditMode: true,//是否编辑模式              
                dialogHisVisible:false,
                isTx:false,      
            };
        },
        async mounted() {    
            await this.setBandSelect();
            await this.getDirectorlist();     
        },
        computed: {     
        },
        methods: {   
             //设置新责任类型原因
             newZrType1Change(){             
                this.form.newZrType2="";              
            
            }, 
            async getDirectorlist () {
                const res1 = await getDirectorList({});
                const res2 = await getDirectorGroupList({});

                this.directorList = res1.data;
                this.directorGroupList = [{ key: "0", value: "未知" }].concat(
                    res2.data || []
                );
            }, 
            async setBandSelect(){
                var res= await  getAllProBrand();
                if (!res?.success) return;
                this.brandlist = res.data;
            }, 
            fmtApplyState:fmtApplyState,   
            newZrDeptActionChange(){
                if(this.form.newZrDeptAction){
                    let opt=this.zrDeptActions.find(x=>x.zrDeptAction==this.form.newZrDeptAction);
                    if(opt){
                        if(this.form.newZrDept!=opt.zrDept){
                            this.form.newMemberName="";
                            this.form.newMemberId=null;
                            this.form.newMemberDDUserId="";
                            this.form.newMemberDDUserName="";
                        }
                        this.form.newZrAction=opt.zrAction;
                        this.form.newZrDept=opt.zrDept;
                    }else{
                        this.form.newMemberName="";
                        this.form.newMemberId=null;
                        this.form.newMemberDDUserId="";
                        this.form.newMemberDDUserName="";
                    }                    
                }
            },   
            newMemberIdChange(){  
                let arr=null;
                if(this.form.newZrDept=="采购"){
                    arr=[...this.brandlist];                   
                }
                else if(this.form.newZrDept=="运营"){
                    arr=[...this.directorList];                    
                }    
                
                if(arr!=null && arr && this.form.newMemberId){                  
                    let opt=arr.find(x=>x.key==this.form.newMemberId);
                    if(opt){
                        this.form.newMemberName=opt.value;                      
                    }
                }
            },                     
            //审核
            async audit(isPass){
                if(!this.form.auditRemark){
                    this.$message.error('请填写审批意见！');
                    return false;
                }

                let dto={
                    ids:[],
                    auditState:isPass?1:0,
                    auditRemark:this.form.auditRemark
                };
                dto.appointByAudtior=this.form.appointByAudtior;
                dto.newZrDeptAction=this.form.newZrDeptAction;
                dto.newZrDept=this.form.newZrDept;
                dto.newZrAction=this.form.newZrAction;
                dto.newZrReason=this.form.newZrReason;
                dto.newMemberId=this.form.newMemberId;
                dto.newMemberName=this.form.newMemberName;
                dto.newMemberDDUserId=this.form.newMemberDDUserId;
                dto.newMemberDDUserName=this.form.newMemberDDUserName;

                dto.newZrType1=this.form.newZrType1;
                dto.newZrType2=this.form.newZrType2;
                dto.newZrReason=dto.newZrType2;

                this.list.forEach(x=>{
                    dto.ids.push(x.id);
                });

                this.pageLoading = true;
              
                let rlt=await BatchAuditDeductZrAppeal(dto);
                if(rlt && rlt.success){
                    this.$message.success(rlt.data.rltMsg);
                    this.$emit('afterSave');
                    this.$emit('close');
                }      
                this.pageLoading = false;          
            },   
            onClose(){
                this.$emit('close');
            },             
            async loadData({rows,mode}) {     
                let self=this;         
                self.pageLoading = true;
                self.formEditMode = mode!=3;
                self.mode = mode;     
                self.list=rows;   
              
                self.pageLoading = false;
            },            
        },
    };
</script>
<style lang="scss" scoped>
   
    .tempdiv ::v-deep img {
        width: auto;
        max-width: 1000px;
    }
</style>