<template>
    <my-container v-loading="pageLoading">
        <el-tabs v-model="activeName" style="height: 95%;" >
            <el-tab-pane label="试用人才" name="first1">
                <probationer ref="probationer"></probationer>
            </el-tab-pane>
            <el-tab-pane label="正式人才" name="first2" lazy style="height: 100%;">
                <regular ref="regular"></regular>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
  
<script>
import MyContainer from "@/components/my-container";
import probationer from "@/views/profit/PersonnelRecruiting/probationer.vue";
import regular from "@/views/profit/PersonnelRecruiting/regular.vue";
export default {
    name: "recruitmentPositions", // 招聘岗位
    components: {
        MyContainer, probationer, regular
    },
    data () {
        return {
            pageLoading: false,
            activeName: "first1",
            filter: {
                department: null,//部门
                position: null,//岗位
                recruiter: null,//招聘专员
                completionDegree:null,//完成度
            },
            showDialog: false,

        };
    },
    async mounted () {

    },
    methods: {
        addPosition () {
            
        }
    },
};
</script>
  
<style lang="scss" scoped></style>
  