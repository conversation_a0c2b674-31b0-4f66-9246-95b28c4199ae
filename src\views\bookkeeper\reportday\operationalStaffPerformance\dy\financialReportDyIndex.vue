<template>
  <my-container>
    <template #header>
      <div class="top">
        <el-date-picker class="timeCss" v-model="filter.timerange" @change="changeTime($event, 1)" type="daterange"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="fastTimePickerOptions">
        </el-date-picker>
        <el-date-picker class="timeCss" v-model="filter.timerangeOnTime" @change="changeTime($event, 2)"
          type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="true" range-separator="至"
          start-placeholder="上架开始日期" end-placeholder="上架结束日期" :picker-options="fastTimePickerOptions">
        </el-date-picker>
        <el-date-picker v-show="(activeName == 'first2' || activeName == 'first1')" class="timeCss"
          v-model="filter.timerangeEntry" @change="changeTime($event, 3)" type="daterange" format="yyyy-MM-dd"
          value-format="yyyy-MM-dd" :clearable="true" range-separator="至" start-placeholder="入职开始日期"
          end-placeholder="入职结束日期" :picker-options="fastTimePickerOptions">
        </el-date-picker>
        <el-select v-show="activeName == 'first6'" filterable v-model="filter.shopManager" collapse-tags clearable
          placeholder="店铺负责人" class="publicCss">
          <el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.label" />
        </el-select>
        <el-select v-show="activeName == 'first6'" v-model="filter.property" placeholder="属性" collapse-tags clearable
          filterable class="publicCss">
          <el-option v-for="item in propertyList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="filter.shopCodeList" placeholder="店铺" clearable filterable multiple collapse-tags
          class="publicCss" v-if="activeName == 'first6'" style="width: 160px;">
          <el-option v-for="item in shopOptions" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
        </el-select>
        <el-select v-show="activeName != 'first5'" filterable v-model="filter.groupIds" collapse-tags multiple clearable
          placeholder="运营组" class="publicCss" style="width: 160px;">
          <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="activeName == 'first4' || activeName == 'first5'" filterable v-model="filter.superviseIds"
          collapse-tags clearable placeholder="运营主管" multiple class="publicCss" style="width: 160px;">
          <el-option v-for="item in superviselist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="activeName != 'first5'" filterable v-model="filter.company" collapse-tags clearable
          placeholder="分公司" class="publicCss">
          <el-option v-for="item in childcompany" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="activeName != 'first5'" filterable v-model="filter.onlineStatus" collapse-tags clearable
          placeholder="员工状态" class="publicCss">
          <el-option key="正式" label="正式" :value="3"></el-option>
          <el-option key="试用" label="试用" :value="2"></el-option>
          <el-option key="离职" label="离职" :value="1"></el-option>
        </el-select>
        <el-select filterable v-model="filter.Profit3Lose" collapse-tags clearable placeholder="毛三利润" class="publicCss">
          <el-option key="正利润" label="正利润" :value="0"></el-option>
          <el-option key="负利润" label="负利润" :value="1"></el-option>
        </el-select>
        <el-select filterable v-model="filter.Profit33Lose" collapse-tags clearable placeholder="毛四利润"
          class="publicCss">
          <el-option key="正利润" label="正利润" :value="0"></el-option>
          <el-option key="负利润" label="负利润" :value="1"></el-option>
        </el-select>
        <el-date-picker v-show="activeName == 'first6'" class="timeCss" v-model="filter.timerange2" type="daterange"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="店铺创建时间开始"
          end-placeholder="店铺创建时间结束" :picker-options="fastTimePickerOptions" @change="changeTime($event, 4)" />
        <el-select filterable clearable v-model="filter.projName" placeholder="排除项目" style="width: 160px" multiple
          collapse-tags>
          <el-option v-for="item in projectList" :key="item.projName" :label="item.projName"
            :value="item.projName"></el-option>
        </el-select>
        <el-select filterable clearable v-model="filter.baoPinList" placeholder="排除爆品" style="width: 160px" multiple
          collapse-tags>
          <el-option v-for="item in popularOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="filter.xiFenPlatformList" filterable clearable placeholder="细分平台" multiple collapse-tags style="width: 170px" v-if="checkPermission('SegmentationPlatform')" >
          <el-option v-for="item in segmentationList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-button type="primary" @click="handleClick" style="width: 60px;">搜索</el-button>
      </div>
      <div style="margin-top: 10px;">
        <el-button-group style="margin-right: 5px;">
          <!-- <el-button type="primary" @click="editZlparam"
            v-if="checkPermission('OperatingPerformanceObjectivePermissionsDy')">设置运营业绩目标</el-button> -->
          <el-button v-for="(button, index) in buttons" :key="button.type"
            :type="button.type === targetType ? 'primary' : 'info'" @click="onShowTarget(button.type)"
            v-if="checkPermission(button.permission)">
            {{ button.label }}
          </el-button>
        </el-button-group>
        <el-button type="primary" @click="onExport">导出</el-button>
      </div>
    </template>
    <el-tabs v-model="activeName" style="height: 94%" @tab-click="handleClick" v-loading="loading">
      <el-tab-pane label="运营助理" name="first1" style="height: 100%">
        <operationsAssistantDy ref="reffirst1" :ListInfo="filter" :targetType="targetType"
          @onExportProps="onExportProps" :yyYeJiDto="yyYeJiDto" @onConfigureTarget="editZyparam" />
      </el-tab-pane>
      <el-tab-pane label="运营专员" name="first2" style="height: 100%" lazy>
        <operationsSpecialistDy ref="reffirst2" :ListInfo="filter" :targetType="targetType"
          @onExportProps="onExportProps" @onConfigureTarget="editZyparam" />
      </el-tab-pane>
      <el-tab-pane label="运营带教" name="first3" style="height: 100%" lazy>
        <operationsTeachingDy ref="reffirst3" :ListInfo="filter" :targetType="targetType" @onExportProps="onExportProps"
          :yyYeJiDto="yyYeJiDto" @onConfigureTarget="editZyparam" />
      </el-tab-pane>
      <el-tab-pane label="运营组" name="first4" style="height: 100%" lazy>
        <operationsGroupsDy ref="reffirst4" :ListInfo="filter" :targetType="targetType" @onExportProps="onExportProps"
          :yyYeJiDto="yyYeJiDto" @onConfigureTarget="editZyparam" />
      </el-tab-pane>
      <el-tab-pane label="运营主管" name="first5" style="height: 100%" lazy>
        <operationsSupervisorDy ref="reffirst5" :ListInfo="filter" :targetType="targetType"
          @onExportProps="onExportProps" />
      </el-tab-pane>
      <el-tab-pane label="店铺" name="first6" style="height: 100%" lazy>
        <operationsStore ref="reffirst6" :ListInfo="filter" :targetType="targetType" @onExportProps="onExportProps" />
      </el-tab-pane>
    </el-tabs>

    <el-dialog title="设置运营业绩目标" :visible.sync="dialogSetParamZlVisible" width="25%" v-dialogDrag>
      <!-- <span>
        <el-row>毛三目标(元)
          <el-input-number v-model="targetData" :min="1" :max="99999999" :controls="false" />
        </el-row>
        <el-row>净利目标(元)
          <el-input-number v-model="targetData1" :min="1" :max="99999999" :controls="false" />
        </el-row>
      </span> -->
      <div>
        <el-table :data="yyYeJiDto" style="width: 100%">
          <el-table-column prop="date" label="毛六目标">
            <template slot-scope="scope">
              <!-- <el-select v-model="scope.row.targetName" filterable clearable placeholder="毛四利润" style="width: 100%;">
                <el-option key="抖音运营组毛六目标" label="运营组" value="抖音运营组毛六目标"></el-option>
                <el-option key="抖音运营带教毛六目标" label="运营带教" value="抖音运营带教毛六目标"></el-option>
                <el-option key="抖音运营助理毛六目标" label="运营助理" value="抖音运营助理毛六目标"></el-option>
              </el-select> -->
              {{ scope.row.targetName }}
            </template>
          </el-table-column>
          <el-table-column prop="targetData" label="目标金额">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.targetData" :min="0" :max="9999999999" style="width:100%"
                placeholder="请输入目标金额" :precision="0" :controls="false" />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogSetParamZlVisible = false">取消</el-button>
        <el-button @click="saveZl()" type="primary">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="设置运营业绩目标" :visible.sync="dialogSetParamZyVisible" width="25%" v-dialogDrag
      style="margin-top: 23vh;">
      <div style="margin-top: 20px;">
        <el-form :model="objective" label-width="120px" :rules="objectiveRules" ref="objectiveForm">
          <el-form-item label="毛六目标(元)" prop="targetData">
            <el-input-number v-model="objective.targetData" :min="0" :max="9999999999" style="width:100%"
              placeholder="请输入目标金额" :precision="0" :controls="false" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogSetParamZyVisible = false">取消</el-button>
        <el-button @click="saveZy()" type="primary">确定</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import { childcompany, popularOptions } from '@/utils/tools';
import { getProductProjectList } from "@/api/operatemanage/productmanager"
import { fastTimePickerOptions } from '@/utils/getCols'
import operationsAssistantDy from "./operationsAssistantDy.vue";
import operationsSpecialistDy from "./operationsSpecialistDy.vue";
import operationsTeachingDy from "./operationsTeachingDy.vue";
import operationsGroupsDy from "./operationsGroupsDy.vue";
import operationsSupervisorDy from "./operationsSupervisorDy.vue";
import operationsStore from "./operationsStoreDy.vue";
import dayjs from 'dayjs'
import { getDirectorGroupList, getDirectorGroupList2, getAllList as getAllShopList, getList as getshopList, getAllShopXiFenPlatform } from '@/api/operatemanage/base/shop'
import { SetPerformanceTarget, getPerformanceTarget, exportPerformanceStaticticsByGroupForDouYin, exportPerformanceStaticticsByShopForDouYin, exportPerformanceStaticticsByUser, setTargetDyProfit6 } from '@/api/bookkeeper/reportday'
const cstTargetName_ZlProfit3 = "抖音运营助理毛三目标";
const cstTargetName_ZlProfit4 = "抖音运营助理净利目标";
const cstTargetName_ZyProfit3 = "抖音运营专员毛三目标";
const cstTargetName_ZyProfit4 = "抖音运营专员净利目标";
export default {
  name: "financialReportDyIndex",
  components: {
    MyContainer, operationsAssistantDy, operationsSpecialistDy, operationsTeachingDy, operationsGroupsDy, operationsSupervisorDy, operationsStore
  },
  data() {
    return {
      popularOptions,
      objective: {
        id: undefined,
        targetName: undefined,
        targetData: undefined,
      },
      objectiveRules: {
        targetData: [{ required: true, message: '请输入目标金额', trigger: 'blur' }],
      },
      yyYeJiDto: [],
      loading: false,
      buttonStyle: ["primary", "info"],
      fastTimePickerOptions,
      targetData1: 0,
      targetData: 0,
      dialogSetParamZlVisible: false,
      dialogSetParamZyVisible: false,
      profitAuthority: true,
      targetType: 1,
      buttons: [
        { label: "毛三利润", type: 1, permission: 'DyPerformance_DyPerformanceProfit3' },
        { label: "净利润", type: 2, permission: 'DyPerformance_DyPerformanceNetProfit' }
        // 可以继续添加更多按钮项, 例如 { label: "毛四利润", type: 3, permission: 'DyPerformance_DyPerformanceProfit4' },
        // permission为权限码，label为按钮显示的文字，type为按钮对应的targetType
      ],
      shopList: [],
      shopOptions: [],
      propertyList: [
        { value: 1, label: '达播' },
        { value: 2, label: '商品卡' },
      ],
      grouplist: [],
      superviselist: [],
      projectList: [],
      childcompany,
      activeName: "first1",
      segmentationList: [],
      filter: {
        timerange: [dayjs().subtract(1, 'day').format('YYYY-MM-DD'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')],
        timerangeOnTime: [],
        timerangeEntry: [],
        shopManager: "",
        property: "",
        groupIds: [],
        superviseIds: [],
        company: "",
        onlineStatus: "",
        Profit3Lose: "",
        Profit33Lose: "",
        timerange2: [],
        shopCodeList: [],
        startTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
        endTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
        targetData: 0,
        targetData1: 0,
        xiFenPlatformList: [],
      },
    };
  },
  async mounted() {
    // await this.getParameter()
    if (this.checkPermission('DyPerformance_DyPerformanceProfit3') && this.checkPermission('DyPerformance_DyPerformanceNetProfit')) {
      this.profitAuthority = false
    } else if (this.checkPermission('DyPerformance_DyPerformanceProfit3')) {
      this.targetType = 1
    } else if (this.checkPermission('DyPerformance_DyPerformanceNetProfit')) {
      this.targetType = 2
    }
    await this.handleClick()
    await this.init()
    await this.onverifyMethod()
  },
  methods: {
    async onConfigureTarget(row) {
      this.objective.id = row.id
      this.objective.targetName = row.targetName
      this.dialogSetParamZlVisible = true
      this.$nextTick(() => {
        this.$refs.objectiveForm.resetFields()
      })
    },
    async onverifyMethod() {
      const { data: data1, success: success1 } = await getProductProjectList({ projName: '' });
      if (success1) {
        this.projectList = data1;
      }
    },
    onExport() {
      this.$refs[`ref${this.activeName}`].onSublevelExport()
    },
    async onExportProps(tableCols, userType) {
      this.loading = true;
      //refundType为1是发生，为2是付款
      const exportCnColumns = tableCols
        .filter(item => item.label && item.prop)
        .map(item => {
          return this.filter.refundType == 2
            ? item.label.replace('(发生)', '(付款)').replace('(付款)', '(发生)')
            : item.label;
        });
      const exportColumns = tableCols
        .filter(item => item.label && item.prop)
        .map(item => item.exportField || item.prop);
      let groupIdsArr = this.filter.groupIds;
      const params = {
        ...this.filter,
        userType,
        targetType: this.targetType,
        YH_EXT_ExportCnColumns: exportCnColumns,
        YH_EXT_ExportColumns: exportColumns,
        groupIds: groupIdsArr.map(id => Number(id)),
      };
      const userTypeMap = {
        3: { func: exportPerformanceStaticticsByGroupForDouYin, name: "运营组" },
        4: { func: exportPerformanceStaticticsByShopForDouYin, name: "店铺" },
        8: { func: exportPerformanceStaticticsByUser, name: "运营主管" },
        1: { func: exportPerformanceStaticticsByUser, name: "运营助理" },
        21: { func: exportPerformanceStaticticsByUser, name: "运营带教" },
        default: { func: exportPerformanceStaticticsByUser, name: "运营专员" }
      };
      const { func, name } = userTypeMap[userType] || userTypeMap.default;
      const res = await func(params);
      this.loading = false;
      if (!res?.data) return;
      const aLink = document.createElement("a");
      const blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute('download', `抖音人员业绩统计_${name}_${new Date().toLocaleString()}.xlsx`);
      aLink.click();
    },
    async init() {
      const { data: data2 } = await getAllShopXiFenPlatform();
      this.segmentationList = data2;
      // 获取运营组
      let res1 = await getDirectorGroupList();
      this.grouplist = res1.data?.map(item => { return { value: item.key, label: item.value }; });

      // 获取运营主管
      this.superviselist = [];
      const res2 = await getDirectorGroupList2();
      const uniqueSupervisors = Array.from(
        new Set(res2.data?.filter(f => f.superviseId > 0).map(m => m.superviseId))
      );
      this.superviselist = uniqueSupervisors
        .map(id => res2.data.find(x => x.id === id))
        .filter(Boolean)
        .map(cur => ({ value: cur.id, label: cur.userName }));

      // 获取店铺负责人
      const { data } = await getAllShopList({ platforms: [6] });
      this.shopList = data
        .filter((item) => item.shopManager !== null)
        .map((item) => ({ label: item.shopManager, value: item.shopCode }))
        .reduce((acc, current) => {
          // 使用 Set 记录已存在的 shopManager
          if (!acc.uniqueLabels.has(current.label)) {
            acc.uniqueLabels.add(current.label);
            acc.result.push(current);
          }
          return acc;
        }, { uniqueLabels: new Set(), result: [] }).result;

      const res3 = await getshopList({ platform: 6, CurrentPage: 1, PageSize: 100000 });
      this.shopOptions = res3.data.list
    },
    async saveZl() {
      this.$confirm('确定要设置运营业绩目标吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await setTargetDyProfit6(this.yyYeJiDto)
        if (success) {
          this.$message.success('设置运营业绩目标成功');
          this.dialogSetParamZlVisible = false;
        }
      });
      // SetPerformanceTarget({ targetName: cstTargetName_ZlProfit3, targetData: this.targetData });
      // SetPerformanceTarget({ targetName: cstTargetName_ZlProfit4, targetData: this.targetData1 });
    },
    async getParameter() {
      const targetNames = [
        '抖音运营组毛六目标',
        '抖音运营带教毛六目标',
        '抖音运营助理毛六目标',
      ];
      const results = await Promise.all(
        targetNames.map(name => getPerformanceTarget({ targetName: name }))
      );
      this.yyYeJiDto = targetNames.map((name, index) => ({
        targetName: name,
        targetData: results[index].data,
      }));
    },
    async saveZy() {
      this.$refs.objectiveForm.validate((valid) => {
        if (valid) {
          this.$confirm('确定要设置运营业绩目标吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async () => {
            let targetName = `${this.objective.id}${this.objective.targetName}`
            const params = {
              targetName,
              targetData: this.objective.targetData
            }
            const { success } = await SetPerformanceTarget(params)
            if (success) {
              this.$message.success('设置运营业绩目标成功');
              this.dialogSetParamZyVisible = false;
            }
          });
        }
      });
    },
    async editZlparam() {
      await this.getParameter()
      this.dialogSetParamZlVisible = true;
    },
    async editZyparam(row) {
      this.objective.id = row.id
      this.objective.targetName = row.targetName
      this.dialogSetParamZyVisible = true
      this.$nextTick(async () => {
        this.$refs.objectiveForm.resetFields()
        let targetName = `${row.id}${row.targetName}`
        const { data, success } = await getPerformanceTarget({ targetName })
        if (success) {
          this.objective.targetData = data || undefined
        }
      })
    },
    async handleClick() {
      let e = this.activeName
      if (e !== 'first6') {
        this.filter.shopCodeList = []
      }
      this.$nextTick(() => {
        this.$refs[`ref${this.activeName}`].getList();
      });
    },
    changeTime(e, val) {
      const [startKey, endKey] = val === 1 ? ['startTime', 'endTime'] : val === 2 ? ['startTime3', 'endTime3'] : val === 3 ? ['startTimeEntry', 'endTimeEntry'] : ['startTime2', 'endTime2'];
      this.filter[startKey] = e ? e[0] : null;
      this.filter[endKey] = e ? e[1] : null;
    },
    onShowTarget(type) {
      this.targetType = type;
      this.handleClick();
    },
  }
};
</script>

<style lang="scss" scoped>
.top {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 5px;

  .publicCss {
    width: 105px;
  }
}

.timeCss {
  width: 230px;
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
