<template>
    <!-- 任务列表 -->
    <my-container>
        <!--顶部操作-->
        <template #header>
            <el-button-group>
                <div class="ssanc">


                    <div style="width:61%;display: inline-block;text-align: left;">
                        <span style="margin-right:0.4%;">
                            <el-input style="width:18%;" v-model.trim="filter.packagesProcessId" :maxlength=100
                                placeholder="编号" @keyup.enter.native="onSearch" clearable />
                        </span>
                        <div style="display: inline-block; margin-right: 0.4%; margin-bottom: 10px;width: 25%;">
                          <inputYunhan ref="productCode" :inputt.sync="filter.finishedProductCode" v-model="filter.finishedProductCode"
                            placeholder="成品编码" :clearable="true" :clearabletext="true" :maxRows="50" :maxlength="3998" :width="'238'"
                            @callback="callbackGoodsCode" title="成品编码">
                          </inputY<PERSON>han>
                        </div>
                        <span style="margin-right:0.4%;">
                            <el-input style="width:30%;" v-model.trim="filter.finishedProductName" :maxlength=100
                                placeholder="成品名称"  clearable />
                                <!-- suffix-icon="el-icon-search" -->
                        </span>
                        <span style="margin-right:0.4%;">
                        <el-select style="width:10%;" v-model="filter.sourceType" :clearable="true" placeholder="来源">
                            <el-option label="预包" value="预包"></el-option>
                            <el-option label="人工" value="人工"></el-option>
                            <el-option label="自动" value="自动"></el-option>
                        </el-select>
                        </span>
                        <span style="margin-right:0.4%;">
                        <el-select style="width:13%;" v-model="filter.saveType" :clearable="true" placeholder="保存状态">
                            <el-option label="是" value="1"></el-option>
                            <el-option label="否" value="0"></el-option>
                        </el-select>
                        </span>
                        <!-- <span style="margin-right:0.4%;">
                            <el-date-picker style="width:22%;position: relative;top:1px;margin-right: 0;" type="daterange"
                                format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="加工日期"
                                end-placeholder="加工日期" v-model="filter.createdtimerange" />
                        </span> -->

                    </div>


                    <div style="display: flex; align-items: center; margin-bottom: 10px;">

                        <span style="margin-left:10px;">
                            <el-button style="width:90px;" type="primary" @click="onSearch">查&nbsp;询</el-button>
                        </span>

                        <span style="margin-left:5px;">
                            <el-button @click="onclear" plain>重置</el-button>
                        </span>


                    </div>

                    <div style="width:35%;display: inline-block;text-align: right;">

                        <span style=" margin-left:20px;padding:0 2px;">
                            <el-button size="mini" style="width:100px;border: 1px solid #b3d8ff;" type="primary" plain
                                @click="onAddTask"><i class="el-icon-plus"></i>&nbsp;新建加工</el-button>

                        </span>

                        <!-- <span style="box-sizing: border-box; margin-left:6px;">
                            <el-button size="mini" type="primary" v-throttle="3000" @click="onAddOrder(1)"
                                v-if="checkPermission('api:Inventory:PackagesProcessing:CallIn')">加工调入</el-button>
                        </span> -->
                        <!-- <span style="box-sizing: border-box; margin-left:6px;">
                            <el-button size="mini" type="primary" v-throttle="3000" @click="onAddOrder(2)"
                                v-if="checkPermission('api:Inventory:PackagesProcessing:CallOut')">成品调出</el-button>
                        </span> -->
                        <span style="box-sizing: border-box; margin-left:6px;">
                            <el-button size="mini" type="primary" v-throttle="3000" @click="updateWorkPriceList"
                                v-if="checkPermission('packages:replaceworkprice')" >获取工价</el-button>
                        </span>
                        <span>
                            <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button
                                type="primary" icon="el-icon-share" @command="handleCommand">
                                批量操作
                                <el-dropdown-menu slot="dropdown">
                                    <!-- <el-dropdown-item class="Batcoperation" style="height: 10px"></el-dropdown-item> -->
                                    <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="h"
                                        v-if="checkPermission('api:Inventory:PackagesProcessing:BaCertificate')">更新合格证</el-dropdown-item>
                                    <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a"
                                        v-if="checkPermission('api:Inventory:PackagesProcessing:BaComplete')">批量完成</el-dropdown-item>
                                    <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="b"
                                        v-if="checkPermission('api:Inventory:PackagesProcessing:BaReset')">批量重启</el-dropdown-item>
                                    <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="c"
                                        v-if="checkPermission('api:Inventory:PackagesProcessing:BaReset')">批量终止</el-dropdown-item>
                                    <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="j"
                                        v-if="checkPermission('api:Inventory:PackagesProcessing:SaveWorkPrice')">保存工价</el-dropdown-item>
                                    <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="k"
                                        v-if="checkPermission('api:Inventory:PackagesProcessing:SaveWorkPrice')">保存模板</el-dropdown-item>
                                    <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="d"
                                        v-if="checkPermission('api:Inventory:PackagesProcessing:BaMark')">批量标记</el-dropdown-item>
                                    <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="f"
                                        v-if="checkPermission('api:Inventory:PackagesProcessing:BaUnMark')">取消标记</el-dropdown-item>
                                    <!-- <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                                        command="d" >批量标记</el-dropdown-item>
                                    <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                                        command="f" >取消标记</el-dropdown-item> -->
                                    <!-- <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="g"
                                        v-if="checkPermission('api:Inventory:PackagesProcessing:BaStat')">批量统计</el-dropdown-item> -->
                                    <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="e"
                                        v-if="checkPermission('api:Inventory:PackagesProcessing:EdDel')">批量删除</el-dropdown-item>
                                    <!-- <el-dropdown-item class="Batcoperation" style="height: 10px"></el-dropdown-item> -->
                                </el-dropdown-menu>
                            </el-dropdown>
                        </span>

                        <span style="box-sizing: border-box; margin-left:6px;" v-if="checkPermission('api:Inventory:PackagesProcessing:Export')">
                            <!-- <el-button type="primary" @click="onExeprotShootingTask">导出</el-button> -->
                            <el-dropdown @command="handleExportCommand">
                                <el-button type="primary">
                                    导出<i class="el-icon-arrow-down el-icon--right"></i>
                                </el-button>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="a">成品导出</el-dropdown-item>
                                    <el-dropdown-item command="b">半成品导出</el-dropdown-item>
                                    <!-- <el-dropdown-item command="c">计件提成导出</el-dropdown-item> -->
                                </el-dropdown-menu>
                            </el-dropdown>
                        </span>

                        <span style="margin-left:6px;">
                            <el-checkbox @change="checkboxchange" style="width: 111px;position: relative;top: 1px;"
                                v-model="filter.IsHideComplete" :checked-value="1" :unchecked-value="0"
                                border>隐藏已完成</el-checkbox>
                        </span>
                    </div>
                </div>

                <div class="heardcss">
                    <span>
                        <el-input style="width:6%;" v-model.trim="filter.halfProductCode" :maxlength=100 placeholder="半成品编码"
                            @keyup.enter.native="onSearch" clearable />
                    </span>
                    <span>
                        <el-input style="width:12%;" v-model.trim="filter.halfProductName" :maxlength=100
                            placeholder="半成品名称" suffix-icon="el-icon-search" @keyup.enter.native="onSearch" clearable />
                    </span>
                    <span>
                        <el-select style="width: 6%;" v-model="filter.brandName" placeholder="品牌" :collapse-tags="true"
                            clearable>
                            <el-option v-for="item in allsellistfuc.brandList" :key="item.setId" :label="item.sceneCode"
                                :value="item.setId" />
                        </el-select>
                    </span>
                    <span>
                        <el-select class="selectmin" style="width: 10%;" @change="changearr(1, $event)"
                            v-model="packingMaterialName" multiple placeholder="包装材料" :collapse-tags="true" filterable
                            clearable>
                            <el-option v-for="item in allsellistfuc.packingMaterialList" :key="item.setId"
                                :label="item.sceneCode" :value="item.setId" />
                        </el-select>
                    </span>
                    <span>
                        <el-select class="selectmin" style="width:8%;" @change="changearr(2, $event)"
                            v-model="machineTypeName" :collapse-tags="true" multiple :clearable="true" placeholder="机型"
                            filterable>
                            <el-option v-for="item in allsellistfuc.machineTypeList" :key="item.setId"
                                :label="item.sceneCode" :value="item.setId" />
                        </el-select>
                    </span>
                    <!-- <span>
                        <el-select class="selectmin" style="width: 8%;" @change="changearr(3, $event)"
                            v-model="packageSizeName" :collapse-tags="true" multiple :clearable="true" placeholder="包装尺寸"
                            filterable>
                            <el-option v-for="item in allsellistfuc.packageSizeList" :key="item.setId"
                                :label="item.sceneCode" :value="item.setId" />
                        </el-select>
                    </span> -->
                    <span>
                        <el-select class="selectmin" style="width: 8%;"
                            v-model="filter.halfProcessWarehouse" :collapse-tags="true" :clearable="true" placeholder="半成品加工仓"
                            filterable>
                            <el-option v-for="item in storagelist" :key="item.wms_co_id" :label="item.name" :value="item.wms_co_id" />
                        </el-select>
                    </span>
                    <!-- <span>
                        <el-select style="width:12%;" filterable v-model="filter.qualityInspector" placeholder="质检员" clearable>
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                :value="item.shopCode" />
                        </el-select>
                    </span> -->
                    <span>
                        <el-select style="width:6.3%;" v-model="filter.typeStr" placeholder="类型" :collapse-tags="true"
                            clearable>
                            <el-option v-for="(item, i) in typeStrList" :key="i + 1" :label="item" :value="item" />
                        </el-select>
                    </span>
                    <span>
                        <el-select style="width:10%;" v-model="filter.sortType" placeholder="正常排序" :collapse-tags="true"
                            clearable>
                            <el-option label="正常排序" value="0"></el-option>
                            <el-option label="创建日期降序" value="1"></el-option>
                            <el-option label="完成日期降序" value="2"></el-option>
                            <el-option label="成品编码升序" value="3"></el-option>
                            <el-option label="加工员降序" value="4"></el-option>
                            <el-option label="加工次数降序" value="5"></el-option>
                            <el-option label="加工日期降序" value="6"></el-option>
                            <el-option label="加工数量降序" value="7"></el-option>
                            <el-option label="加工工价降序" value="8"></el-option>
                            <!-- <el-option label="质检员降序" value="9"></el-option>
                            <el-option label="质检次数降序" value="10"></el-option>
                            <el-option label="质检日期降序" value="11"></el-option>
                            <el-option label="质检数量降序" value="12"></el-option>
                            <el-option label="质检工价降序" value="13"></el-option> -->
                        </el-select>
                    </span>
                    <span>
                        <el-select style="width:6%;" filterable v-model="filter.createdUserName" placeholder="创建人"
                            clearable>
                            <el-option v-for="(item, i) in createUser" :key="i + 1" :label="item" :value="item" />
                        </el-select>
                    </span>
                    <!--下拉（完成时间，创建时间，到货日期，申请日期，）-->
                    <span>
                        <el-select style="width:5.5%;" v-model="filter.dateType" :clearable="true" placeholder="创建时间">

                            <el-option label="创建时间" value="0"></el-option>
                            <el-option label="完成时间" value="1"></el-option>
                            <!-- <el-option label="确认时间" value="2"></el-option> -->
                        </el-select>
                    </span>
                    <span>
                        <el-date-picker style="width:20%;position: relative;top:1px;margin-right: 0;" type="daterange"
                            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                            end-placeholder="结束日期" v-model="filter.createdtimerange" />
                    </span>



                </div>

            </el-button-group>
        </template>
        <!--列表-->
        <div style="height: 100%;box-sizing: border-box;padding:0 0 0 5px;">
            <el-container style="height: 100%;">
                <packagelist :tableData="tableData" ref="refpackagelist" @getTaskList="getTaskList" :islook="islook"
                    @getfinishedProductQuantity="getfinishedProductQuantity" :storagelist="storagelist" @pids="pids"
                    @chipids="chipids" :summaryarry="summaryarry" :productcopy="productcopy" :tabel="1"></packagelist>
            </el-container>

        </div>
        <template #footer>
            <my-pagination :sizes="[50, 100, 200, 500, 800, 1000, 2000, 5000]" :page-size="500" ref="pager" :total="total"
                :checked-count="sels.length" @page-change="pagechange" @size-change="sizechange" />
        </template>
        <!--创建任务-->
        <el-dialog title="." :show-close="false" :visible.sync="addTask" top="10vh" width="750px" close-on-click-modal
            @close="addTask = false" element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <packagetaskeditfrom ref="packagetaskeditfrom" :onCloseAddForm="onCloseAddForm"
                :taskUrgencyList="taskUrgencyList" :groupList="groupList" :warehouselist="warehouselist"
                :userList="fpPhotoLqNameList" :platformList="platformList" :islook='false'></packagetaskeditfrom>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="addTask = false">取 消</el-button>
                    <el-button type="primary" @click="onSubmit" v-show="!islook" v-throttle="3000">提 交</el-button>
                </span>
            </template>
        </el-dialog>
        <!--编辑任务-->
        <!-- <el-drawer :visible.sync="editTaskshow" v-if="editTaskshow" :close-on-click-modal="false" direction="rtl"
            :size="750" element-loading-text="拼命加载中" v-loading="addLoading" :show-close="false">
            <packagetaskeditfrom ref="packagetaskeditfrom" :onCloseAddForm="onCloseAddForm"
                style="height: 100%;width:100%" :taskUrgencyList="taskUrgencyList" :groupList="groupList"
                :warehouselist="warehouselist" :userList="fpPhotoLqNameList" :platformList="platformList" :islook='false'>
            </packagetaskeditfrom>
        </el-drawer> -->
        <!--查看详情-->
        <el-dialog title="查看备注" :visible.sync="viewReferenceRemark" width="60%" close-on-click-modal
            element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <shootingTaskRemark ref="shootingTaskRemark" :rowinfo="selectRowKey" :islook="islook"></shootingTaskRemark>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReferenceRemark = false">关 闭</el-button>
                    <my-confirm-button type="submit" :loading="shootingTaskRemarkrawer" @click="sumbitshootingTaskRemark"
                        v-show="!islook" />
                </span>
            </template>
        </el-dialog>
        <!--加急审核-->
        <el-dialog title="加急审核" :visible.sync="taskUrgencyAproved" width="20%" close-on-click-modal
            element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <div style="vertical-align: middle; margin-top: 20px;margin-left: 80px;">
                <el-radio v-model="taskUrgencyStatus" label="1" border>同意</el-radio>
                <el-radio v-model="taskUrgencyStatus" label="9" border>驳回</el-radio>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="(taskUrgencyAproved = false)">取 消</el-button>
                    <my-confirm-button type="submit" @click="taskUrgencyApp" />
                </span>
            </template>
        </el-dialog>


        <el-dialog title="选择商品编码" :visible.sync="orderGoodschoiceVisible" width='85%' height='500px' v-dialogDrag
            close-on-click-modal>
            <goodschoice :ischoice="true" ref="orderGoodschoice" style="z-index:10000;height:500px" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="orderGoodschoiceVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onQuerenOrderGoods()" v-throttle="2000">确 定</el-button>
                </span>
            </template>
        </el-dialog>


        <!-- 新加工调入 -->
        <el-dialog :title="this.workShowTitle" :visible.sync="workshow" width="60%" close-on-click-modal top="30px"
            element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading" :show-close="false">
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <span>{{ typeId == 2 ? '成品调出' : '加工调入' }}</span>
                    <!-- <div style="float: right; padding: 3px 0" v-if="typeId==2">
                    <el-select style="width:150px" @change="storagechange($event,typeId)" v-model="calloutlist.wareId" :clearable="true" :collapse-tags="true"
                        filterable>
                        <el-option v-for="item in storagelist" :key="item.id" :label="item.name"
                            :value="item.wms_co_id" />
                    </el-select>
                </div> -->

                    <div style="float: right; padding: 3px 0" v-if="typeId == 1">
                        <div style="display: flex; flex-direction: row;">
                            <div style="line-height: 30px;margin-right: 20px;">所需数量： {{ finishedProductQuantity }}</div>
                            <!-- <el-select style="width:150px" @change="storagechange($event,typeId)" v-model="callinlist.wareId" :clearable="true" :collapse-tags="true"
                            filterable>
                            <el-option v-for="item in storagelist" :key="item.id" :label="item.name"
                                :value="item.wms_co_id" />
                        </el-select> -->
                        </div>
                    </div>
                </div>
                <div class="text item" style="height: 700px;">
                    <vxe-table :align="null" resizable border="default" height="80%" :key="tokey"
                        :data="typeId == 2 ? calloutlist.detialList : chiselids"
                        :tree-config="{ accordion: true, lazy: true, hasChild: true, toggleMethod: toggleTreeMethod }"
                        :expand-config="{ accordion: true, toggleMethod: toggleTreeMethod }">

                        <vxe-column align="left" :field="typeId == 2 ? 'packagesProcessingId' : 'showCode'" title="编号"
                            width="70"></vxe-column>
                        <vxe-column align="left" :field="typeId == 2 ? 'finishedProductCode' : 'halfProductCode'"
                            :title="typeId == 2 ? '成品编码' : '半成品编码'"></vxe-column>
                        <vxe-column align="left" :field="typeId == 2 ? 'finishedProductName' : 'halfProductName'"
                            :title="typeId == 2 ? '成品名称' : '半成品名称'"></vxe-column>
                        <vxe-column type="expand" align="left" width="25" v-if="typeId == 2">
                            <template #content="{ row, rowIndex }">
                                <div>
                                    <vxe-table height="250px" resizable border="default" :align="'left'"
                                        :data="row.recordList" :cell-class-name="'cellheight1'"
                                        @checkbox-change="checkchange" @checkbox-all="checkchange"
                                        :checkbox-config="{ visibleMethod: checkisbtn, }">
                                        <vxe-column type="checkbox" width="28"></vxe-column>
                                        <vxe-column type="seq" width="50"></vxe-column>
                                        <vxe-column field="createUserName" title="姓名" width="100">
                                        </vxe-column>
                                        <vxe-column field="quantity" title="数量" width="120">
                                            <template #default="{ row }">
                                                {{ row.quantity }}
                                            </template>
                                        </vxe-column>
                                        <vxe-column field="state" title="状态" width="100" v-if="typeId == 2 || typeId == 4">
                                            <template #default="{ row }">
                                                <div class="flexrow statuscss">
                                                    <el-tag
                                                        :color="row.state == '已撤销' ? '#909399' : row.state == '审批中' ? '#409EFF' : row.state == '已完成' ? '#67C23A' : row.state == '已拒绝' ? '#F56C6C' : '#ffffff'"
                                                        effect="dark">{{ row.state }}</el-tag>

                                                </div>
                                            </template>
                                        </vxe-column>
                                        <vxe-column field="createDate" title="日期" width="90">
                                            <template #default="{ row }">
                                                <div class="flexrow">
                                                    {{ formatIsCommission(row.createDate) }}
                                                </div>
                                            </template>
                                        </vxe-column>

                                    </vxe-table>
                                </div>
                            </template>
                        </vxe-column>
                        <vxe-column v-if="typeId == 1" :field="'combinationQuantity'" :title="'组合数量'" width="90"></vxe-column>
                        <vxe-column :field="typeId == 2 ? 'totalQuantity' : 'incomingQuantity'" :title="typeId == 2 ? '成品数量' : '调入数量'">
                            <template #default="{ row }">
                                <el-input-number :controls="false" @change="numberchange(row, $event, typeId)" :step="1"
                                    controls-position="right" style="width:120px" :clearable="true" :value="row.quantity"
                                    :min="0" :precision="0" :max="1000000"
                                    :key="typeId == 2 ? row.packagesProcessingId : row.showCode"></el-input-number>
                            </template>
                        </vxe-column>
                        <vxe-column :field="typeId == 2 ? 'storage' : 'storage'" :title="typeId == 2 ? '仓储' : '仓储'">
                            <template #default="{ row }">
                                <el-select style="width:150px" @change="storagechange($event, typeId, row)"
                                    v-model="row.wareId" :clearable="true" :collapse-tags="true" filterable>
                                    <el-option v-for="(item, i) in storagelist" :key="i + 1" :label="item.name"
                                        :value="item.wms_co_id" />
                                </el-select>
                            </template>
                        </vxe-column>
                        <vxe-column :field="typeId == 2 ? 'calloutPeople' : 'callinPeople'" :title="typeId == 2 ? '调出人' : '调入人'">
                            <template #default="{ row }">
                                <el-select style="width:100px" disabled :value="fiftername(loginuser.userId)"
                                    :clearable="true" :collapse-tags="true" filterable>
                                    <el-option v-for="(uitem, i) in userlist" :key="i" :value="uitem.userId"
                                        :label="uitem.userName" />
                                </el-select>
                            </template>
                        </vxe-column>
                        <vxe-column :field="typeId == 2 ? 'calloutDate' : 'callinDate'" :title="typeId == 2 ? '调拨日期' : '调拨日期'">
                            <template #default="{ row }">
                                <el-date-picker disabled :value="opentime" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                    type="date" style="width:130px" placeholder="结束时间">
                                </el-date-picker>
                            </template>
                        </vxe-column>
                    </vxe-table>

                    <div class="qualibtn" style="width: 100%; height:100px; display: flex; flex-direction: column;">
                        <div class="flexrow">
                            <div class="marginrt">
                                <el-button width="200px" size="medium" @click="workshow = false">取消</el-button>
                            </div>
                            <el-button v-loading="saveloading" width="200px" size="medium" type="primary" v-throttle="2000"
                                @click="savestorage(typeId)">保存</el-button>
                        </div>
                    </div>
                </div>
            </el-card>
        </el-dialog>


        <vxe-modal v-model="qualificationshow" title="合格证信息" mask-closable>
            <template #default>
                <vxe-textarea v-model="qualfifter.msg" :disabled="disabledd" placeholder=""
                    :autosize="{ minRows: 6, maxRows: 10 }" clearable :maxlength=300></vxe-textarea>
                <div class="qualibtn" style="width: 100%;display: flex; flex-direction: column;">
                    <el-button width="200px" size="medium" @click="copytext(qualfifter.msg)">复制信息</el-button>
                    <el-button width="200px" type="warning" size="medium"
                        @click="qualificationedit('无合格证')">无合格证</el-button>
                    <el-button v-if="disabledd" width="200px" size="medium" type="primary"
                        @click="disabledd = false">编辑</el-button>
                    <div v-else>
                        <div class="flexrow">
                            <div class="marginrt">
                                <el-button width="200px" size="medium" @click="qualificationshow = false">取消</el-button>
                            </div>
                            <el-button width="200px" size="medium" type="primary"
                                @click="savecertificateInfo">保存</el-button>
                        </div>
                    </div>
                </div>
            </template>
        </vxe-modal>


    </my-container>
</template>
<script>
import packagelist from '@/views/media/packagework/packagelist.vue'
import acesTable from "@/components/Table/table.vue";
import { sendWarehouse4HotGoodsBuildGoodsDocList } from "@/utils/tools";
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import cesTable from "@/components/Table/tableforvedio.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import {
    pageShootingViewTaskAsync, getTaskReferenceInfo, delShootingTploadFileTaskAsync, taskOverActionsAsync, exportShootingTaskReport
    , shootUrgencyCilckAsync, getCityAllData, shootingTaskAddOrderSaveCheckTaskIds, shootingTaskAddOrderSave, getShootingTaskOrderListById
    , signShootingTaskActionAsync, deleteShootingTaskActionAsync, unSignShootingTaskActionAsync, endShootingTaskActionAsync, deleteTaskActionAsync, endRestartActionAsync,
    getHotSaleGoodInfo, caclShootingTaskActionAsync, getUserRoleList,
} from '@/api/media/ShootingVideo';
import { getShootingSetDataById, getShootingSetData, saveShootingSet, deleteShootingSet, saveDataOrderListDataAsync } from '@/api/media/shootingset'
import {
    getPackagesProcessingListAsync, getRecordUser, exportPackagesProcessingListAsync, exportPackagesProcessingDetialListAsync,
    updateProcessBatch, updateDispatcheBatch, updateFinishState, updateCertificateBatch, deleteReord, deletePackagesProcessing, getCurrentUser,
    pckagesProcessingCallOut, pckagesProcessingCallIn, statAction, getAllWarehouse, getCreateUserList, getCallOutSelRecordListAsync, restartAction, endAction,
    saveWorkPriceTemplate, unMarkAction, markAction, getPieceDeductExportData,getPackagesTypeListAsync,saveWorkPrice,updatePackProcessWorkPriceList
} from '@/api/inventory/packagesprocess';//包装加工
import shootingvideotaskTable from '@/views/media/shooting/shootingvideotaskTable'
import uploadfile from '@/views/media/shooting/uploadfile'
// import shootinguploadaction from '@/views/media/shooting/shootinguploadaction'
import shootingTaskRemark from '@/views/media/shooting/ShootingTaskRemark'
// import shootingvideotaskuploadfile from '@/views/media/shooting/shootingvideotaskuploadfile'
// import shootingvideotaskuploadsuccessfilesocre from '@/views/media/shooting/shootingvideotaskuploadsuccessfilesocre'
import packagetaskeditfrom from '@/views/media/packagework/packagetaskeditfrom'
import shootingchartforfp from '@/views/media/shooting/shootingchartforfp'
import { getVedioTaskOrderAddressList, saveVedioTaskOrderAddress, deleteVedioTaskOrderAddress } from '@/api/media/vediotask';
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { formatTime } from "@/utils";
import { rulePlatform } from "@/utils/formruletools";
import { formatWarehouse, ShootingVideoTaskUrgencyOptions } from "@/utils/tools";
import { getDirectorGroupList } from '@/api/operatemanage/base/shop';
import goodschoice from "@/views/base/goods/goods4.vue";
import logistics from '@/components/Comm/logistics'
import orderLogPage from "@/views/order/logisticsWarning/orderLogPage.vue";
// import {getAllWarehouse} from '@/api/inventory/warehouse'
import inputYunhan from "@/components/Comm/inputYunhan";


export default {
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, shootingchartforfp
        , vxetablebase, cesTable, goodschoice, logistics, uploadfile, shootingTaskRemark
        , packagetaskeditfrom, orderLogPage
        , acesTable, shootingvideotaskTable, packagelist, inputYunhan

    },
    inject: ['allsellist'],
    props: ["tablekey", 'role'],
    data() {
        return {
            productcopy: {},//复制所有成品编码
            // pageloading: true,
            tableData: null,
            tokey: 'one',
            taskname: '',
            storage: '',
            disabledd: true,
            saveloading: false,
            inputshow: true,
            addLoading: true,
            selshootingTaskId: 0,
            taskUrgencyStatus: "1",
            taskUrgencyAproved: false,
            workshow: false,
            workShowTitle: "",
            Oncommand: 'a',
            shootingTaskRemarkrawer: false,
            viewReferenceRemark: false,
            shootingvediotask: 'shootingvediotask',
            opentime: new Date(),
            tjopentime: new Date().getTime() + 1,
            urgencyopentime: new Date().getTime() + 2,
            markopentime: new Date().getTime() + 3,
            fileopentime: new Date().getTime() + 4,
            outsueccessopentime: new Date().getTime() + 5,
            outopentime: new Date().getTime() + 6,
            editopentime: new Date().getTime() + 7,
            outconfilekey: null,
            fpcharttype: null,
            outindex: 0,
            //分配趋势图
            shootingchartforfpvisible: false,
            //上传成果文件
            successfiledrawer: false,
            successfileshow: false,
            //查看参考
            viewReference: false,
            nohgz: false,
            //选中的行
            selectRowKey: null,
            that: this,
            pageLoading: true,
            islook: false,
            finishedProductQuantity: null,
            filter: {
                currentPage: 1,
                pageSize: 300,
                orderBy: "",
                isAsc: true,
                packagesProcessId: null,
                finishedProductCode: "",
                halfProductCode: "",
                brandCode: "",
                packingMaterialCode: "",
                machineTypeCode: "",
                packageSizeCode: "",
                isProcess: null,
                processer: "",
                qualityInspector: "",
                startCreateTime: "",
                endCreateTime: "",
                createdtimerange: [],
                IsNeedDetial: false,
                dateType: null,
                createPackId: "",
                quantity: "",
                IsHideComplete: true,
                typeStr:"",
                halfProcessWarehouse : "",
            },
            typeStrList:[],
            packageSizeName: [],
            machineTypeName: [],
            packingMaterialName: [],
            createUser: null,
            callinlist: {},
            storagelist: [],
            supplierName: [],//供应商
            listarry: [],
            linkval: '',
            fomSendWarehouse4HotGoodsBuildGoodsDocList: sendWarehouse4HotGoodsBuildGoodsDocList,
            // skuTableCols: skuTableCols,

            tasklist: [],
            taskPageTitle: "创建加工",
            referenceVideoList: [],
            multipleSelection: [],
            formatWarehouse: formatWarehouse,
            warehouselist: [],
            shopList: [],
            userList: [],
            groupList: [],
            fpDetailLqNameList: [],
            fpModelLqNameList: [],
            fpPhotoLqNameList: [],
            fpVideoLqNameList: [],
            dockingPeopleList: [],
            taskUrgencyList: ShootingVideoTaskUrgencyOptions,
            platformList: [],
            // tableCols: tableCols,
            total: 0,
            //选中的行id
            selids: [],
            chiselids: [],
            taskPhotofileList: [],
            taskExeclfileList: [],

            packageSizeList: [],
            machineTypeList: [],
            packingMaterialList: [],
            brandList: [],

            addTask: false,
            editTaskshow: false,
            loginfo: null,
            calloutlist: {},
            summaryarry: {},
            pager: {},
            sels: [], // 列表选中列
            listLoading: false,
            //下单发货
            orderGoodschoiceVisible: false,//选择下单任务商品窗口
            receiverStateList: [],//省
            receiverCityList: [],//市
            receiverDistrictList: [],//区
            receiverCityList2: [],//市
            receiverDistrictList2: [],//区
            dialogAddOrderVisible: false,
            meidialogAddOrderVisible: false,
            dialogAddOrderLoading: false,
            dialogAddOrderSubmitLoding: false,
            addOrderForm: {
                shootingTaskIds: "",
                receiverName: "",
                receiverPhone: "",
                receiverStateCode: "",
                receiverCityCode: "",
                receiverDistrictCode: "",
                receiverState: "",
                receiverCity: "",
                receiverDistrict: "",
                receiverAddress: "",
                isZt: 0,
                warehouse: null,
                remark: "",
                receiverAddressAllInfo: "",
                orderGoods: [],
            },
            // allsellist: {},
            selShootingTaskIdSpanList: [],
            orderGoodschoiceVisible: false,
            loginuser: {},
            addOrderFormRules: {
                //receiverName: [{ required: true, message: '请输入收货人', trigger: 'blur' }],
                //receiverPhone: [{ required: true, message: '请输入收货电话', trigger: 'blur' }],
                isZt: [{ required: true, message: '请输入是否自提', trigger: 'blur' }],
                //receiverStateCode: [{ required: true, message: '请输入收货省', trigger: 'blur' }],
                //receiverCityCode: [{ required: true, message: '请输入收货市', trigger: 'blur' }],
                //receiverDistrictCode: [{ required: true, message: '请输入成收货区', trigger: 'blur' }],
                //receiverAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
                //receiverAddressAllInfo: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
            },
            dialogOrderDtlVisible: false,
            dialogOrderDtlLoading: false,

            xdfhmainlist: [],
            xdfhmainLoading: false,

            xdfhdtllist: [],
            xdfhdtlLoading: false,

            receiverAddressList: [],
            dialogAddressVisible: false,
            addressListLoading: false,
            dialogAddAddressSubmitLoding: false,
            addAddressForm: {
                receiverName: "",
                receiverPhone: "",
                receiverStateCode: null,
                receiverCityCode: null,
                receiverDistrictCode: null,
                receiverAddress: "",
            },
            outids: [],
            addressList: [],
            addAddressFormRules: {
                receiverName: [{ required: true, message: '请输入收货人', trigger: 'blur' }],
                receiverPhone: [{ required: true, message: '请输入收货电话', trigger: 'blur' }],
                receiverStateCode: [{ required: true, message: '请输入收货省', trigger: 'blur' }],
                receiverCityCode: [{ required: true, message: '请输入收货市', trigger: 'blur' }],
                receiverDistrictCode: [{ required: true, message: '请输入成收货区', trigger: 'blur' }],
                receiverAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
            },
            drawervisible: false,
            sendOrderNoInner: "",
            dialogHisVisible: false,
            calcSkuTableData: [],
            typeId: null,
            qualificationshow: false,
            qualfifter: {
                msg: ''
            },
            allmsg: {},
        };
    },
    watch: {
    },
    async created() {
        // await this.gettabmsg();
        // await this.getuser();
        await this.getloginuser();
        await this.getstorage();
        await this.getcrepeople();
        await this.gettypeStrList();
    },
    async mounted() {
        await this.onSearch();
    },
    computed: {
        // sumdata(){
        //     // this.allmsg = this.allsellist;
        //     // this.allsellist = this.allsellist;
        //     this.packingMaterialList = this.allsellist?.packingMaterialList;
        //     this.brandList = this.allsellist?.brandList;
        //     this.machineTypeList = this.allsellist?.machineTypeList;
        //     this.packageSizeList = this.allsellist?.packageSizeList;
        // },
        allsellistfuc() {
            return this.allsellist()
        }
    },
    methods: {
        //成品编码
        async callbackGoodsCode(val) {
          this.filter.finishedProductCode = val;
        },
        checkisbtn({ row }) {
            if (row.state == '审批中' || row.state == '已完成') {
                return false;
            } else {
                return true;
            }
        },
        checkchange(data) {
            var allsum = 0;
            var arr = [];
            data.records.map((item) => {
                allsum = allsum + item.quantity;
                arr.push(item.recordId);
            })
            if (!this.calloutlist.detialList[this.outindex].quantity) {
                this.calloutlist.detialList[this.outindex].quantity = 0;
            }
            this.calloutlist.detialList[this.outindex].recordIds = arr.join();
            this.calloutlist.detialList[this.outindex].quantity = allsum;
            this.$forceUpdate();
        },
        footerMethod({ columns, data }) {
            const sums = [];
            return [sums]
        },
        toggleTreeMethod({ expanded, row, column, columnIndex }) {
            this.outindex = this.calloutlist.detialList.indexOf(row);
            return true;
        },
        formatIsCommission(value) {
            return value == null ? null : formatTime(value, 'YY-MM-DD')
        },
        changearr(index, val) {
            switch (index) {
                case 1:
                    this.filter.packingMaterialName = val.join();
                    break;
                case 2:
                    this.filter.machineTypeName = val.join();
                    break;
                case 3:
                    this.filter.packageSizeName = val.join();
                    break;
            }
        },
        async getcrepeople() {
            const res = await getCreateUserList();
            if (!res?.success) {
                return
            }
            this.createUser = res.data;
        },
        async gettypeStrList() {
            const res = await getPackagesTypeListAsync();
            if (!res?.success) {
                return
            }
            this.typeStrList = res.data;
        },
        qualificationedit(e) {
            this.qualfifter.msg = '无合格证';
            this.nohgz = true;
            this.$nextTick(() => {
                this.savecertificateInfo();
            });
        },
        copytext(e) {
            let textarea = document.createElement("textarea")
            textarea.value = e
            textarea.readOnly = "readOnly"
            document.body.appendChild(textarea)
            textarea.select()
            let result = document.execCommand("copy")
            if (result) {
                this.$message({
                    message: '复制成功',
                    type: 'success'
                })
            }
            textarea.remove()
        },
        checkboxchange(val) {
            // this.pageLoading = true;
            // this.getTaskList();
        },
        numberchange(row, num, type) {
            switch (type) {
                case 1:
                    var index = this.chiselids.indexOf(row);
                    this.callinlist.detialList[index].quantity = num;
                    break;
                case 2:
                    var index = this.selids.indexOf(row);
                    this.calloutlist.detialList[index].quantity = num;
                    break;
            }


            // this.calloutlist
        },
        //提交调出申请
        async savestorage(index) {

            this.saveloading = true;
            switch (index) {
                case 1:
                    var a = false;
                    this.callinlist.detialList.map((item) => {
                        if (!item.wareId) {
                            a = true;
                        }
                    })
                    if (a) {
                        this.$message({ type: 'warning', message: "请选择仓库" });
                        this.saveloading = false;
                        return
                    }
                    var res = await pckagesProcessingCallIn(this.callinlist);
                    if (!res?.success) {
                        this.saveloading = false;
                        return
                    }

                    this.$message({ type: 'success', message: "操作成功" });
                    this.$refs.refpackagelist.getneireq();
                    this.workshow = false;


                    break;
                case 2:
                    var a = false;
                    this.calloutlist.detialList.map((item) => {
                        if (!item.wareId) {
                            a = true;
                        }
                    })
                    if (a) {
                        this.$message({ type: 'warning', message: "请选择仓库" });
                        this.saveloading = false;
                        return
                    }
                    var res = await pckagesProcessingCallOut(this.calloutlist);
                    if (!res?.success) {
                        this.saveloading = false;
                        return
                    }


                    this.$message({ type: 'success', message: "操作成功" });
                    this.workshow = false;
                    break;
            }
            this.saveloading = false;
        },
        async getstorage() {
            const res = await getAllWarehouse();
            if (!res?.success) {
                return
            }
            this.storagelist = res.data;
        },
        storagechange(val, index, row) {

            var name = '';

            switch (index) {
                case 1://半成品
                    this.storagelist.map((item) => {
                        if (item.wms_co_id == val) {
                            this.chiselids[this.chiselids.indexOf(row)].wareName = item.name;
                            // this.callinlist.wareName = item.name;
                        }
                    })
                    break;
                case 2://成品
                    this.storagelist.map((item) => {
                        if (item.wms_co_id == val) {
                            // this.calloutlist.wareName = item.name;
                            this.calloutlist.detialList[this.calloutlist.detialList.indexOf(row)].wareName = item.name;
                        }
                    })
                    break;
            }

        },
        async getloginuser() {
            const res = await getCurrentUser();
            if (!res?.success) {
                return
            }
            this.loginuser = res.data;
        },
        fiftername(value) {
            var info = ' '
            this.userlist.forEach((item) => {
                if (item.userId == value) info = item.userName
            })
            return info;
        },
        getfinishedProductQuantity(val) {
            this.finishedProductQuantity = val;
        },
        pids(data) {
            this.selids = data;
            var arr = [];
            data.map((item) => {
                arr.push(item.packagesProcessingId)
            })
            this.outids = arr;
        },
        chipids(data) {
            this.chiselids = data;
            this.callinlist.detialList = data;
            this.callinlist.detialList.map((item) => {
                item.selUserId = this.loginuser.userId;
                item.produnctName = item.halfProductName;
                item.produnctCode = item.halfProductCode;
                item.packaesProcessingId = item.packagesProcessingId;
                item.packaesProcessingDetialId = item.id;
            })
        },
        async getuser() {
            const res = await getRecordUser();
            if (!res?.success) {
                return
            }
            this.userlist = res.data;
            this.$refs.refpackagelist.getuser(res.data)
        },
        // async gettabmsg(){
        //     await this.getDataSetList(14);
        //     await this.getDataSetList(15);
        //     await this.getDataSetList(16);
        //     await this.getDataSetList(17);
        // },

        newgetuser(data) {
            // this.$refs.refpackagelist.getuser(data)
            this.userlist = data;
            this.$nextTick(() => {
                this.$refs.refpackagelist.getuser(data)
            });

        },
        // getallmsg(data){
        //     let _this = this;
        //     this.$nextTick(()=>{
        //         // this.$refs.refallheard.getallmsg(data);
        //         _this.allmsg = data;
        //         _this.allsellist = data;
        //         this.packingMaterialList = data?.packingMaterialList;
        //         this.brandList = data?.brandList;
        //         this.machineTypeList = data?.machineTypeList;
        //         this.packageSizeList = data?.packageSizeList;

        //     });
        // },
        // async getDataSetList(index) { //14包装加工-品牌，15包装加工-包装方式，16包装加工-机型，17包装加工-尺寸
        //     this.listLoading = true;
        //     const res = await getShootingSetData({ setType: index });
        //     if (!res?.success) {
        //         return
        //     }
        //     switch (index) {
        //         case 14:
        //             this.packingMaterialList = res?.data?.data;
        //             break;
        //         case 15:
        //             this.brandList = res?.data?.data;
        //             break;
        //         case 16:
        //             this.machineTypeList = res?.data?.data;
        //             break;
        //         case 17:
        //             this.packageSizeList = res?.data?.data;
        //             break;
        //     }

        //    this.allsellist.packingMaterialList = this.packingMaterialList;
        //    this.allsellist.brandList = this.brandList;
        //    this.allsellist.machineTypeList = this.machineTypeList;
        //    this.allsellist.packageSizeList = this.packageSizeList;

        //     this.listLoading = false;
        // },
        // async getrole() {
        //     var res = await getUserRoleList();
        //     if(res?.success)
        //     {
        //        if(res.data == null){
        //             this.role ="tz";
        //        }else if (res.data.indexOf("视觉部经理") >-1){
        //             this.role ="b";
        //        }

        //     }else{
        //         this.role ="tz";
        //     }

        // },
        // rowChanged4EstimateStockInAmount: rowChanged4EstimateStockInAmount,
        selchange(val) {
            this.supplierName.map((item) => {
                if (item.supplierId == val) {
                    this.filter.supplierLink = item.supplierLink;

                }
            })

            this.listarry.map((item) => {
                if (item.supplierId == val) {
                    this.calcSkuTableData = item.goods;
                }
            })

        },
        linkTo(val) {
            window.open(this.filter.supplierLink);
        },
        // 重置
        onclear() {
            this.filter = {
                IsNeedDetial: false,
                IsHideComplete: true
            };
            this.packageSizeName = [];
            this.machineTypeName = [];
            this.packingMaterialName = [];

        },
        getname(val) {
            this.taskname = val;
        },
        inputshowfunc() {
            this.inputshow = false;
        },
        toResultmatter(row) {
            if (row != null) {
                let routeUrl = this.$router.resolve({
                    path: '/resultmatter',
                    query: { id: row.shootingTaskId }
                });
                window.open(routeUrl.href, '_blank');
            }
        },
        async onsummaryClick(property) {
            debugger
            this.fpcharttype = property;
            this.$nextTick(function () {
                this.$refs.shootingchartforfp.showviewMain();
            });
            this.shootingchartforfpvisible = true;
        },
        async onExeprotShootingTask() {
            this.$refs.shootingvideotask.exportData("新品拍摄导出")
        },
        async handleExportCommand(command) {
            console.log(command, "command");
            var res = null;
            var fileName = "包装加工-"
            var ids = this.selids.map(a => a.packagesProcessingId).join(",");
            if (ids) {
                this.filter.PackagesProcessIds = ids
            } else {
                this.filter.PackagesProcessIds = null
            }
            const params = { ... this.filter }
            this.pageLoading = true;
            if (command == 'a') {
                res = await exportPackagesProcessingListAsync(params);
                fileName = fileName + "成品_"
            } else if (command == 'b') {

                res = await exportPackagesProcessingDetialListAsync(params);
                fileName = fileName + "半成品_"
            } else {
                res = await getPieceDeductExportData(params)
                fileName = fileName + "计件提成_"
                console.log(res, "res");
                const aLink = document.createElement("a");
                let blob = new Blob([res], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', fileName + new Date().toLocaleString() + '.xlsx')
                aLink.click()
                return
            }

            this.pageLoading = false;
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', fileName + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        }
        , async handleCommand(command) {
            if (this.selids.length == 0 && command != 'x') {
                this.$message({ type: 'warning', message: "请选择加工" });
                return;
            }
            var ids = this.selids.map(a => a.packagesProcessingId).join(",");
            switch (command) {
                //批量完成
                case 'a':
                    await this.onTaskOverActionShared(ids)
                    break;
                //批量重启
                case 'b':
                    await this.onEndRestartActionAsyncShared(ids);
                    break;
                //批量终止
                case 'c':
                    await this.OnendShootingTaskAction(ids)
                    break;
                //批量标记
                case 'd':
                    await this.onSignShootingTaskActionShared(ids)
                    break;
                //取消标记
                case 'f':
                    await this.onUnSignShootingTaskActionShared(ids)
                    break;
                //批量删除
                case 'e':
                    await this.deleteTaskActionShared(ids)
                    break;
                //批量统计
                case 'g':
                    await this.caclShootingTaskActionAsyncShared(ids)
                    break;
                //批量合格证
                case 'h':
                    this.qualificationshowfuc(this.selids)
                    break;
                //批量保存工价
                case 'j':
                    await this.saveWorkPriceTemplatefuc(ids)
                    break;
                //批量工价模板
                case 'k':
                    await this.ratetemplate(this.selids.map(a => a.packagesProcessingId))
                    break;
            }
        },
        //批量工价模板
        async ratetemplate(array) {
            this.$confirm("选中的任务将保存为工价模板，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await saveWorkPrice({packagesProcessList:array});
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    // this.selids = [];
                    await this.onRefresh();
                }
            });
        },
        //批量保存工价
        async saveWorkPriceTemplatefuc(array) {
            this.$confirm("选中的任务将保存为工价列表，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await saveWorkPriceTemplate(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    // this.selids = [];
                    // await this.onRefresh();
                    await this.$emit('getPriceTemplateListfuc', 'tab2');
                }
            });
        },

        //批量统计
        async caclShootingTaskActionAsyncShared(array) {
            this.$confirm("选中的任务将进行统计，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await statAction(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.selids = [];
                    await this.onRefresh();
                    await this.$emit('refresh', 'tab2');
                }
            });
        },
        qualificationshowfuc(row) {
            this.qualfifter.msg = "";
            // this.disabledd = true;
            this.disabledd = true;
            this.qualificationshow = true;
        },
        //批量更新合格证
        async savecertificateInfo() {
            this.$confirm("是否批量更新合格证", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var ids = this.selids.map(a => a.packagesProcessingId);
                let params = {
                    packProcessingIds: ids,
                    certificateInfo: this.qualfifter.msg,
                    noCertificate: this.nohgz ? true : false,
                }
                var res = await updateCertificateBatch(params);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.selids = [];
                    this.qualificationshow = false;
                    this.noCertificate = false;
                    await this.onRefresh();
                }
            });
        },
        //完成操作公共
        async onTaskOverActionShared(array) {
            this.$confirm("是否确定完成", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let params = {
                    processId: array
                }
                var res = await updateFinishState(params);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.selids = [];
                    await this.onRefresh();
                }
            });
        },
        //存档操作
        async onTaskShopActionShared(array) {
            this.$confirm("选中的任务移动到存档列表，是否确定存档", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await taskShopActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //重新启动
        async onEndRestartActionAsyncShared(array) {
            this.$confirm("选中的终止任务会重新启动，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await restartAction(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //批量标记
        async onSignShootingTaskActionShared(array) {
            this.$confirm("选中的任务将会进行标记，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await markAction(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //取消标记
        async onUnSignShootingTaskActionShared(array) {
            this.$confirm("选中的任务将会取消标记，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await unMarkAction(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },

        //回收站彻底删除操作
        async deleteTaskActionShared(array) {
            this.$confirm("是否确定删除", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await deletePackagesProcessing(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //批量终止onGetdrowList
        async OnendShootingTaskAction(array) {
            this.$confirm("选中任务将会终止，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await endAction(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onRefresh();
                    this.selids = [];
                }
            });
        },
        //获取下拉数据
        // async onGetdrowList() {
        //     var pfrule = await rulePlatform();
        //     this.platformList = pfrule.options;
        //     var res = await getDirectorGroupList();
        //     this.groupList = res.data?.map(item => { return { value: item.key, label: item.value }; });
        // },
        // async onGetdrowList2() {
        //     var res = await this.getwarehouseListRelod();
        //     this.warehouselist = res?.map(item => { return { value: item.id, label: item.label }; });
        // },
        // async onchangeplatform(val) {
        //     var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100 });
        //     this.filter.shopName = "";
        //     this.shopList = res1.data.list;
        // },
        //获取分配人下拉，对接人下啦
        // async getShootingViewPer() {
        //     var res = await this.getShootingViewPersonRelod();
        //     if (res) {
        //         this.dockingPeopleList = res.dockingPeopleList;
        //         this.fpPhotoLqNameList = res.fpallList;
        //     }
        // },
        async ShowHideonSearch() {
            this.listLoading = true;
            var checkedColumnsFora = [];
            switch (this.Oncommand) {
                //显示全部 ,部门经理，超管
                case "a":
                    checkedColumnsFora = [];

                    // 显示所有

                    break;
                //显示默认
                case "b":
                    // checkedColumnsFora = [];
                    checkedColumnsFora =
                        ['Divisionline', 'caozoulie'
                            , 'photoDaysStr', 'photoOverTimeStr'
                            , 'vedioDaysStr', 'vedioOverTimeStr', 'vedioConfirmNameStr', 'vedioConfirmTimeStr'
                            , 'microDetailDaysStr', 'microDetailOverTimeStr', 'microDetailVedioCounts', 'microDetailConfirmNameStr'
                            , 'microDetailConfirmTimeStr'
                            , 'detailDaysStr', 'detailOverTimeStr', 'detailConfirmNameStr', 'detailConfirmTimeStr'
                            , 'modelPhotosDaysStr', 'modelPhotosOverTimeStr', 'modelPhotoCounts'
                            , 'modelVideoDaysStr', 'modelVideoOverTimeStr', 'modelVedioCounts'
                            , 'productID'
                            , 'operationGroupstr', 'dockingPeople', 'platformStr', 'shopNameStr'
                            , 'arrivalTimeDays', 'deliverTimeStr', 'deliverTimeDays',];

                    // 排除
                    // 分割线，操作
                    // ，照片天数，照片完成日期
                    // ，视频天数，视频完成日期，视频确认人，视频确认日期，微。视频天数，微。视频完成日期
                    // ，微。视频数量，微。视频确认人，微。视频确认日期，详情页天数，详情页完成日期，详情页确认人，详情页确认日期
                    // ，建模照片天数，建模照片完成日期，建模照片张数，建模视频天数，建模视频完成日期，建模视频个数
                    // ，产品ID
                    // ，运营小组，对接人，平台，店铺
                    // ，到货天数，发货日期，发货天数，

                    break;

                default:
                    break;
            }
            await this.$refs.shootingvideotask.ShowHidenColums(checkedColumnsFora);
            this.listLoading = false;
        },
        //查询
        async onSearch() {
            // this.pageLoading = true;
            this.$refs.refpackagelist.tableloading = true;
            this.$refs.pager.setPage(1);
            this.getTaskList();
            this.getcrepeople();
            this.gettypeStrList();
            this.selids = [];
        },
        //刷新当前页
        async onRefresh() {
            // this.pageLoading = true;
            this.$refs.refpackagelist.tableloading = true;
            await this.getTaskList();
        },

        // keyDown() {
        //         var that = this;
        //         //监听键盘按钮
        //         document.onkeydown = function (event) {
        //             var e = event || window.event;
        //             var keyCode = e.keyCode || e.which;
        //             //向前
        //             if (keyCode == 13) {

        //                 that.onSearch()
        //             }
        //             if (keyCode == 50) {
        //                 that.Oncommand='a';
        //                 that.ShowHideonSearch()
        //             }
        //             if (keyCode == 49) {
        //                 that.Oncommand='b';
        //                 that.ShowHideonSearch()
        //             }



        //         }
        //     },
        sizechange(val) {
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.onRefresh();
        },
        pagechange(val) {
            this.filter.currentPage = val;
            this.onRefresh();
        },

        //获取数据
        async getTaskList() {
            var pager = this.$refs.pager.getPager();
            if (this.filter.createdtimerange) {
                this.filter.startCreateTime = this.filter.createdtimerange[0];
                this.filter.endCreateTime = this.filter.createdtimerange[1];
            } else {
                this.filter.startCreateTime = null;
                this.filter.endCreateTime = null;
            }
            // this.filter.isComplate = this.filter.isComplateChecked == true ? 0 : 1;
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter,
                isNeedDetial: false,
            };
            //复制所有成品编码
            this.productcopy = params
            // debugger
            // params.platform.length>0?params.platform:params.platform=""
            // this.listLoading = true;
            // this.$refs.refpackagelist.tableloading = true;
            const res = await getPackagesProcessingListAsync(params);
            this.tableData = res.data.list;
            this.$refs.refpackagelist.updatelist()
            // debugger
            if (!res?.success) return;
            this.listLoading = false;
            this.total = res.data.total;
            console.log("777", res)
            this.tasklist = res.data.list;
            this.summaryarry = res.data.summary;
            this.pageLoading = false;
        },
        //列表排序
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async rowChange(row) {

            await this.editTask(row);
        },
        //编辑任务
        async editTask(row) {
            this.addLoading = true;
            this.editopentime = this.editopentime + 1;
            this.taskPageTitle = "编辑任务";
            this.editTaskshow = true;
            await this.$nextTick(function () { this.$refs.packagetaskeditfrom.editTask(row); });
            this.addLoading = false;
        },
        //新增任务
        onAddTask() {
            this.addLoading = true;
            this.opentime = this.opentime + 1;
            this.addTask = true;
            this.taskPageTitle = "创建任务";
            // await this.$nextTick(function () { this.$refs.packagetaskeditfrom.initaddform(); });
            let _this = this;
            this.$nextTick(() => {
                _this.$refs.packagetaskeditfrom.initaddform();
                // _this.$refs.packagetaskeditfrom.getallmsg(_this.allmsg);
            })
            this.islook = false;
            this.addLoading = false;
        },
        //提交保存
        async onSubmit() {
            this.addLoading = true;
            await this.$nextTick(function () {
                this.$refs.packagetaskeditfrom.onSubmit();
            });
            this.addLoading = false;
        },
        //删除上传附件操作
        async deluplogexl(ret) {
            this.addLoading = true;

            await delShootingTploadFileTaskAsync({ upLoadPhotoId: ret.upLoadPhotoId, type: 2 }).catch(_ => {
                this.addLoading = false;
            });

            this.addLoading = false;
        },
        //删除上传图片操作
        async deluplogimg(ret) {
            this.addLoading = true;
            await delShootingTploadFileTaskAsync({ upLoadPhotoId: ret.upLoadPhotoId, type: 1 }).catch(_ => {
                this.addLoading = false;
            });
            this.addLoading = false;
        },
        //关闭窗口，初始化数
        async onCloseAddForm(type) {
            await this.onSearch();
            if (type == 1) {
                this.addTask = false;
                this.editTaskshow = false;
            }
        },

        //紧急程度按钮点击
        async shootUrgencyCilck(btnstr, shootingTaskId) {
            debugger
            var that = this;
            switch (btnstr) {
                //申请加急
                case "正常":
                    this.$confirm("是否确认加急?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    })
                        .then(async () => {
                            var res = await shootUrgencyCilckAsync({ taskid: shootingTaskId, index: 0 });
                            if (res?.success) {
                                that.$message({ message: '操作成功', type: "success" });
                                await that.onRefresh();
                            }
                        });
                    break;
                //确认加急
                case "审核":
                    this.opentime++;
                    this.selshootingTaskId = shootingTaskId;
                    this.taskUrgencyAproved = true;
                    break;
            }
        },
        async taskUrgencyApp() {
            var res = await shootUrgencyCilckAsync({ taskid: this.selshootingTaskId, index: this.taskUrgencyStatus });
            if (res?.success) {
                this.taskUrgencyAproved = false;
                this.$message({ message: '操作成功', type: "success" });
                await this.onRefresh();
            }
        },

        //查看详情备注页
        openTaskRmarkInfo(row) {
            this.markopentime = this.markopentime + 1;
            this.selectRowKey = row.shootingTaskId;
            this.viewReferenceRemark = true;
        },
        //成果文件上传
        onUploadSuccessFile(row) {
            this.opentime = this.opentime + 1;
            this.selectRowKey = row.shootingTaskId;
            this.outconfilekey = row.shootingTaskId;
            this.successfiledrawer = true;
        },
        //关闭成果文件上传
        successfiledrawerClose() {
            this.successfiledrawer = false;
        },
        //查看参考附件
        videotaskuploadfileDetal(row) {
            this.opentime = this.opentime + 1;
            this.selectRowKey = row.shootingTaskId;
            // this.viewReference = true;

            if (row != null) {
                let routeUrl = this.$router.resolve({
                    path: '/seereference',
                    query: { id: row.shootingTaskId, refid: row.referenceId }
                });
                window.open(routeUrl.href, '_blank');
            }

        },
        //查看成果文件
        openComputOutInfo(row) {
            this.outsueccessopentime = this.outsueccessopentime + 1;
            this.selectRowKey = row.shootingTaskId;
            this.successfileshow = true;
        },
        successfiledrawerscoreClose() {
            this.successfileshow = false;
        },

        async sumbitshootingTaskRemark() {
            this.shootingTaskRemarkrawer = true;
            await this.$refs.shootingTaskRemark.onsubmit();
            this.shootingTaskRemarkrawer = false;

        },
        //下单发货表单
        async getCity(parentCode, type,) {
            const res = await getCityAllData({ parentCode: parentCode });
            if (res?.success) {
                if (type == 1) {
                    this.receiverStateList = res?.data;
                }
                if (type == 2) {
                    this.receiverCityList = res?.data;
                }
                if (type == 3) {
                    this.receiverDistrictList = res?.data;
                }
            }
        },
        async receiverStateChange() {
            this.receiverCityList = [];
            this.receiverDistrictList = [];
            this.addOrderForm.receiverCityCode = "";
            this.addOrderForm.receiverDistrictCode = "";
            var parentCode = this.addOrderForm.receiverStateCode;
            if (parentCode) {
                this.getCity(parentCode, 2);
            }
        },
        async receiverCityChange() {
            this.receiverDistrictList = [];
            this.addOrderForm.receiverDistrictCode = "";
            var parentCode = this.addOrderForm.receiverCityCode;
            if (parentCode) {
                this.getCity(parentCode, 3);
            }
        },
        async getCity2(parentCode, type,) {
            const res = await getCityAllData({ parentCode: parentCode });
            if (res?.success) {
                if (type == 1) {
                    this.receiverStateList = res?.data;
                }
                if (type == 2) {
                    this.receiverCityList2 = res?.data;
                }
                if (type == 3) {
                    this.receiverDistrictList2 = res?.data;
                }
            }
        },
        async receiverStateChange2() {
            this.receiverCityList2 = [];
            this.receiverDistrictList2 = [];
            this.addAddressForm.receiverCityCode = "";
            this.addAddressForm.receiverDistrictCode = "";
            var parentCode = this.addAddressForm.receiverStateCode;
            if (parentCode) {
                this.getCity2(parentCode, 2);
            }
        },
        async receiverCityChange2() {
            this.receiverDistrictList2 = [];
            this.addAddressForm.receiverDistrictCode = "";
            var parentCode = this.addAddressForm.receiverCityCode;
            if (parentCode) {
                this.getCity2(parentCode, 3);
            }
        },
        //成品调出/加工调入
        async onAddOrder(typeId) {
            this.pageloading = true;
            this.typeId = typeId
            if (this.outids.length == 0 && typeId == 2) {
                this.$message({ type: 'warning', message: "请选择成品调出" });
                this.pageloading = false;
                return;
            }
            if (this.chiselids.length == 0 && typeId == 1) {
                this.$message({ type: 'warning', message: "请选择半成品调入" });
                this.pageloading = false;
                return;
            }
            this.saveloading = false;


            if (typeId == 1) {
                const res = await getRecordUser();
                if (!res?.success) {
                    return
                }
                this.userlist = res.data;
                this.chiselids.map((item) => {
                    item.quantity = (item.combinationQuantity * this.finishedProductQuantity) > 1000000 ? 1000000 : (item.combinationQuantity * this.finishedProductQuantity);
                })
                this.tokey = 'one'


            } else if (typeId == 2) {
                // this.selids.map((item)=>{
                //     item.quantity = item.totalProcessQuantity;
                // })
                let params = this.outids.join();
                let res = await getCallOutSelRecordListAsync(params);
                if (!res?.success) {
                    return
                }

                this.calloutlist.detialList = res.data;

                this.calloutlist.detialList.map((item) => {
                    item.selUserId = this.loginuser.userId;
                    item.produnctName = item.finishedProductName;
                    item.produnctCode = item.finishedProductCode;
                    item.packaesProcessingId = item.packagesProcessingId;
                })
                this.tokey = 'two'
                // this.selids = res.data.map((item)=>{
                //     item.quantity = 0;
                // })
                console.log("打印获取数据", res)

            }


            this.workshow = true;
            this.workShowTitle = this.typeId == 2 ? "成品调出" : "加工调入";
            this.pageloading = false;
            this.dialogAddOrderVisible = true;


        },
        async saveworkshow() {
            var ids = this.selids.map(a => a.packagesProcessingId).join(",");
            let params = {
                packaesProcessingIds: ids,
                createPackId: this.filter.createPackId,
                quantity: this.filter.quantity,
                typeId: this.typeId
            }
            // return
            const res = this.typeId == 1 ? await updateProcessBatch(params) : await updateDispatcheBatch(params);
            if (!res?.success) {
                return
            }
            this.$message({
                message: '保存成功',
                type: 'success'
            })
            this.selids = [];
            this.workshow = false;
            await this.onRefresh();
        },
        //关闭下单发货界面
        async closeAddOrder() {
            this.dialogAddOrderSubmitLoding = false;
        },
        //下单发货：选择商品
        async onSelctOrderGoods() {
            this.orderGoodschoiceVisible = true;
            this.$refs.orderGoodschoice.removeSelData();
        },
        //同步附件中的sku商品
        async onSyncOrderGoods() {
            var res = await getTaskReferenceInfo({ shootingTaskIds: this.addOrderForm.shootingTaskIds });
            if (res?.success) {
                var temp = this.addOrderForm.orderGoods;
                var isNew = true;
                res?.data.forEach((item) => {
                    temp.forEach(old => {
                        if (old.goodsCode == item.goodsCode) {
                            isNew = false;
                        }
                    });
                    if (isNew) {
                        this.addOrderForm.orderGoods.push({
                            goodsCode: item.goodsCode,
                            goodsName: item.goodsName,
                            shopCode: null,
                            shopName: null,
                            proCode: null,
                            goodsPrice: item.costPrice ?? 0,
                            goodsQty: 1,
                            goodsAmount: item.costPrice ?? 0
                        });
                    }
                });
            }
        },
        //下单发货：选择商品确定
        async onQuerenOrderGoods() {
            var choicelist = await this.$refs.orderGoodschoice.getchoicelist();
            if (choicelist && choicelist.length > 0) {
                //已存在的不添加
                var temp = this.addOrderForm.orderGoods;
                var isNew = true;
                choicelist.forEach((item) => {
                    isNew = true;
                    temp.forEach(old => {
                        if (old.goodsCode == item.goodsCode) {
                            isNew = false;
                        }
                    });
                    if (isNew) {
                        this.addOrderForm.orderGoods.push({
                            goodsCode: item.goodsCode,
                            goodsName: item.goodsName,
                            shopCode: item.shopId,
                            shopName: item.shopName,
                            proCode: item.shopStyleCode,
                            goodsPrice: item.costPrice ?? 0,
                            goodsQty: 1,
                            goodsAmount: item.costPrice ?? 0
                        });
                    }
                });
                this.orderGoodschoiceVisible = false;
            }
        },
        //移除明细
        async onDelDtlGood(index) {
            this.addOrderForm.orderGoods.splice(index, 1);
        },
        async addOrderFormGoodsQtyChange(row) {
            row.goodsAmount = (row.goodsQty * (row.goodsPrice ?? 0)).toFixed(2);
        },
        //新增编辑提交时验证
        addOrderFormValidate: function () {
            let isValid = false
            this.$refs.addOrderForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        //下单发货-保存
        async onAddOrderSave() {
            if (this.addOrderForm.shootingTaskIds == "" || this.addOrderForm.orderGoods.length <= 0) {
                this.$message({ message: '下单发货信息不完整，请填写', type: "warning" });
                return;
            }
            if (this.addOrderForm.isZt == 1) {
                if (this.addOrderForm.warehouse == "" || this.addOrderForm.warehouse == null) {
                    this.$message({ message: '拿样方式=仓库自提时必须填写自提仓库', type: "warning" });
                    return;
                }
            }
            else {
                this.addOrderForm.warehouse = null;
            }
            if (this.addOrderForm.isZt == 0) {
                if (this.addOrderForm.receiverAddressAllInfo == "" || this.addOrderForm.receiverAddressAllInfo == null) {
                    this.$message({ message: '拿样方式=快递寄样时必须选择详细地址', type: "warning" });
                    return;
                }
                if (this.addOrderForm.receiverName == "" || this.addOrderForm.receiverPhone == "") {
                    this.$message({ message: '收货人信息错误，请刷新后重试', type: "warning" });
                    return;
                }
                if (this.addOrderForm.receiverAddress == "") {
                    this.$message({ message: '下单发货地址错误，请刷新后重试', type: "warning" });
                    return;
                }
            }
            else {
                this.addOrderForm.receiverName = "";
                this.addOrderForm.receiverPhone = "";
                this.addOrderForm.receiverState = "";
                this.addOrderForm.receiverStateCode = "";
                this.addOrderForm.receiverCity = "";
                this.addOrderForm.receiverCityCode = "";
                this.addOrderForm.receiverDistrict = "";
                this.addOrderForm.receiverDistrictCode = "";
                this.addOrderForm.receiverAddress = "";
                this.addOrderForm.receiverAddressAllInfo = "";
            }
            this.dialogAddOrderSubmitLoding = true;
            this.dialogAddOrderLoading = true;
            const res = await shootingTaskAddOrderSave(this.addOrderForm);
            this.dialogAddOrderLoading = false;
            if (res?.success) {
                if (this.addOrderForm.isZt == 1) {
                    this.$message({ message: '操作成功，请关注钉钉审批流程', type: "success" });
                }
                else {
                    this.$message({ message: '操作成功，请关注钉钉审批流程', type: "success" });
                }
                this.onSearch();
                this.dialogAddOrderVisible = false;
                this.dialogAddOrderSubmitLoding = false;
            }
            // else {
            //     if (res?.msg)
            //         this.$message({ message: res?.msg, type: "error" });
            //     else
            //         this.$message({ message: '操作失败，请刷新后重试', type: "error" });
            // }
        },
        async dialogOrderDtlColsed() {
            this.xdfhmainlist = [];
            this.xdfhdtllist = [];
        },
        async onShowOrderDtl(row) {
            this.dialogOrderDtlVisible = true;
            this.xdfhmainLoading = true;
            this.xdfhdtlLoading = true;
            var ret = await getShootingTaskOrderListById({ shootingTaskId: row.shootingTaskId });
            this.xdfhmainLoading = false;
            this.xdfhdtlLoading = false;
            if (ret?.success && ret.data.length > 0) {
                ret.data.forEach(f => f.shootingTaskId = row.shootingTaskId);
                this.xdfhmainlist = ret.data;
                this.xdfhdtllist = ret.data[0].dtlEntities;
            }
        },
        async onxdfhmainCellClick(row) {
            this.xdfhmainlist.forEach(
                f => {
                    if (f.shootingTaskOrderId == row.shootingTaskOrderId) {
                        this.xdfhdtllist = f.dtlEntities;
                    }
                }
            );
        },
        async receiverAddressSelChange() {
            this.addressList.forEach(f => {
                if (f.vedioTaskOrderAddressId == this.addOrderForm.receiverAddressAllInfo && this.addOrderForm.receiverAddressAllInfo != "") {
                    this.addOrderForm.receiverStateCode = f.receiverStateCode;
                    this.addOrderForm.receiverCityCode = f.receiverCityCode;
                    this.addOrderForm.receiverDistrictCode = f.receiverDistrictCode;
                    this.addOrderForm.receiverAddress = f.receiverAddress;
                    if (f.receiverName)
                        this.addOrderForm.receiverName = f.receiverName;
                    if (f.receiverPhone)
                        this.addOrderForm.receiverPhone = f.receiverPhone;

                    return;
                }
            });
            ;
        },
        async getAddressList() {
            this.addressList = [];
            this.addressListLoading = true;
            var ret = await getVedioTaskOrderAddressList();
            this.addressListLoading = false;
            this.addressList = ret?.data;
            this.receiverAddressList = [];
            if (ret?.success) {
                this.receiverAddressList = ret.data?.map(item => { return { value: item.vedioTaskOrderAddressId, label: item.receiverAllAddress }; });
            }
        },
        async onAddressSet() {
            this.dialogAddressVisible = true;
            this.getAddressList();
        },
        async onAddAddressSave() {
            this.dialogAddAddressSubmitLoding = true;
            var ret = await saveVedioTaskOrderAddress(this.addAddressForm);
            this.dialogAddAddressSubmitLoding = false;
            if (ret?.success) {
                this.addAddressForm = {
                    receiverName: "",
                    receiverPhone: "",
                    receiverStateCode: null,
                    receiverCityCode: null,
                    receiverDistrictCode: null,
                    receiverAddress: "",
                };
                this.getAddressList();
            }
        },
        async onAddressDelete(row) {
            this.$confirm("是否确定删除, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                this.addressListLoading = true;
                var ret = await deleteVedioTaskOrderAddress({ vedioTaskOrderAddressId: row.vedioTaskOrderAddressId });
                this.addressListLoading = false;
                this.getAddressList();
            });
        },
        async onShowExproessHttp(row) {
            this.drawervisible = true;
            let expressNo = row.expressNo;
            if (!expressNo) {
                expressNo = row.sampleExpressNo;
            }
            this.$nextTick(function () {
                this.$refs.logistics.showlogistics("", expressNo);
            })
        },
        showLogDetail(row) {
            this.dialogHisVisible = true;
            let sampleRrderNo = row.sampleRrderNo;
            if (!sampleRrderNo) {
                sampleRrderNo = row.orderNoInner;
            }
            this.sendOrderNoInner = sampleRrderNo;
        },
        async updateWorkPriceList()
        {
           if (this.selids.length == 0) {
                this.$message({ type: 'warning', message: "请选择加工" });
                return;
            }
            var ids = this.selids.map(a => a.packagesProcessingId);
            this.$confirm("是否将选中的任务工价替换为模板中的工价，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await updatePackProcessWorkPriceList({packagesProcessList:ids});
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.selids = [];
                    await this.onRefresh();
                }
            });
        }

    },
};
</script>
<style lang="scss" scoped>
::v-deep .el-drawer__header {
    border: none !important;
    padding: 16px 24px 0 24px;
}

::v-deep .el-header {
    padding: 10px 5px 5px 5px !important;
}

.ssanc {
    width: 100%;
    height: 38px;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
    display: flex;

}

.heardcss {
    width: 100%;
    min-width: 1150px;
    min-height: 35px;
    // background-color: aqua;
    box-sizing: border-box;
    display: inline-block;
    margin-top: 8px;
}


.heardcss span,
.ssanc span {
    padding: 4px 0.2% 4px 0;
}


::v-deep span .el-radio-button__inner {
    line-height: 14px !important;
}

::v-deep .vxetablecss {
    width: 100%;
    margin-top: -20px !important;
}

// ::v-deep .vxetoolbar20221212 {
//     top: 97px;
//     right: 15px;
// }

::v-deep .el-button-group>.el-button:last-child {
    margin-left: -2px !important;
}

::v-deep .el-button-group {
    margin: 0 !important;
    padding: 0 !important;
}

// ::v-deep .vxe-table--render-default .vxe-cell {
//     padding:0 5px !important;
// }
::v-deep .vxe-header--row {
    height: 58px;
}

::v-deep .el-table__body-wrapper {
    height: 220px !important;
}

::v-deep .el-table__body-wrapper {
    overflow-y: auto;
}


.gddwz {
    width: 100%;
    /* background-color: #F2F6FC; */
    //   border: 1px solid #ff1414;
    overflow: hidden;
    white-space: nowrap;
    box-sizing: border-box;
    text-align: center;
}

.gddwznr {
    padding-left: 20px;
    font-size: 14px;
    color: #eb0000;
    display: inline-block;
    white-space: nowrap;
    animation: 20s wordsLoop linear infinite normal;
    margin: 0;
}

@keyframes wordsLoop {
    0% {
        transform: translateX(100%);
        -webkit-transform: translateX(100%);
    }

    100% {
        transform: translateX(-100%);
        -webkit-transform: translateX(-100%);
    }
}

@-webkit-keyframes wordsLoop {
    0% {
        transform: translateX(100%);
        -webkit-transform: translateX(100%);
    }

    100% {
        transform: translateX(-100%);
        -webkit-transform: translateX(-100%);
    }
}

// 弹窗边距
::v-deep .el-dialog__body {
    padding: 0 !important;
}

::v-deep .el-dialog__header {
    padding: 0 !important;
}

::v-deep .mycontainer {
    padding: 0 !important;
}

.flexrow {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.qualibtn ::v-deep .el-button {
    margin-left: 0 !important;
    margin-top: 10px;
}

.marginrt {
    margin: 0 10px 0 auto;
}

::v-deep .el-dialog__title {
    color: #ffffff !important;
}

.qualibtn ::v-deep .el-button {
    margin-left: 0 !important;
    margin-top: 10px;
}

.selectmin ::v-deep .el-select__tags {
    // max-width: 115px !important;
}

.cellheight1 {
    height: 300px;
}

.statuscss ::v-deep .el-tag--dark {
    border-color: #fff !important;
}</style>

