<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" :before-leave="beforeLeave" style="height:94%;">
            <!-- <el-tab-pane name="switch">
                <span slot="label">
                    <el-switch v-model="switchshow" @change="changeShowgroup" :disabled="isAllcheck" active-text="售后管理"
                        inactive-text="售前管理">
                    </el-switch>
                </span>
            </el-tab-pane> -->
          <el-tab-pane label="店铺分区管理" name="tab6" style="height: 100%;" lazy>
            <shoppartition :filter="filter" ref="shoppartition" style="height: 100%;" />
          </el-tab-pane>
            <el-tab-pane label="分组管理" name="tab0" style="height: 100%;">
                <group1 :filter="filter" ref="group1" style="height: 100%;" @callBackInfo="handleInfo" />
            </el-tab-pane>
            <el-tab-pane label="咨询数据导入" name="tab1" style="height: 100%;" lazy>
                <inquirs :filter="filter" ref="inquirs" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="组效率统计" name="tab2" style="height: 100%;" lazy>
                <pddGroupStaticsIndex :filter="filter" style="height: 100%;"
                    :partInfo="infoBool" />
            </el-tab-pane>
            <!-- <el-tab-pane label="组效率统计(售前组)" name="tab999" style="height: 100%;" lazy>
                <groupinquirsstatistics :filter="filter" ref="groupinquirsstatistics" style="height: 100%;"
                    :partInfo="infoBool" />
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="组效率统计(一体组)" name="tab5" style="height: 100%;" lazy>
                <groupyitiinquirsstatistics :filter="filter" ref="groupyitiinquirsstatistics" style="height: 100%;"
                    :partInfo="infoBool" />
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="组效率统计（离线留言）" name="tab7" style="height: 100%;" lazy>
                <pddofflinemessagestatistics :filter="filter" ref="pddofflinemessagestatistics" style="height: 100%;"
                    :partInfo="infoBool" />
            </el-tab-pane> -->
            <el-tab-pane label="店效率统计" name="tab4" style="height: 100%;" lazy>
                <shopinquirsstatistics :filter="filter" ref="shopinquirsstatistics" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="个人效率统计" name="tab3" style="height: 100%;" >
                <inquirsstatistics :filter="filter" ref="inquirsstatistics" style="height: 100%;"
                    :partInfo="infoBool" />
            </el-tab-pane>

          <!-- <el-tab-pane v-if="showSq" label="未匹配咨询数据" name="tab12" style="height: 100%;" lazy>
                <pddinquirsno :filter="filter" ref="pddinquirsno" style="height: 100%;" :isSq="true" />
            </el-tab-pane> -->

          <!-- <el-tab-pane v-if="showSq" label="店铺组效率统计" name="tabsh45" style="height: 100%;" lazy>
            <inquirsstatisticsmonth :filter="filter" ref="inquirsstatisticsmonth" style="height: 100%;" />
          </el-tab-pane> -->

          <!-- <el-tab-pane v-if="showSq" label="合并店效率统计" name="tab10" style="height: 100%;" lazy>
            <pddmergeinquirsstatistics :filter="filter" ref="pddmergeinquirsstatistics" style="height: 100%;" />
          </el-tab-pane> -->
<!--售后-->
          <!-- <el-tab-pane v-if="showSh" label="店铺分区管理(售后组)" name="tabsh7" style="height: 100%;" lazy>
            <shoppartitionsh :filter="filter" ref="shoppartitionsh" style="height: 100%;" />
          </el-tab-pane>
            
            <el-tab-pane v-if="showSh" label="分组管理(售后组)" name="tabsh0" style="height: 100%;" lazy>
                <group1sh :filter="filter" ref="group1sh" style="height: 100%;" @callBackInfoH="handleInfoH" />
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="咨询数据导入(售后组)" name="tabsh1" style="height: 100%;" lazy>
                <inquirssh :filter="filter" ref="inquirssh" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="组效率统计(售后组)" name="tabsh2" style="height: 100%;" lazy>
                <groupinquirsstatisticssh :filter="filter" ref="groupinquirsstatisticssh" style="height: 100%;"
                    :partInfo="infoBoolH" />
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="组效率统计(一体组)" name="tabsh5" style="height: 100%;" lazy>
                <groupyitiinquirsstatisticssh :filter="filter" ref="groupyitiinquirsstatisticssh" style="height: 100%;"
                    :partInfo="infoBoolH" />
            </el-tab-pane>
          <el-tab-pane v-if="showSh" label="组效率统计（离线留言）" name="tab7" style="height: 100%;" lazy>
            <pddofflinemessagestatisticssh :filter="filter" ref="pddofflinemessagestatisticssh" style="height: 100%;"
                                         :partInfo="infoBool" />
          </el-tab-pane>
            <el-tab-pane v-if="showSh" label="店效率统计(售后组)" name="tabsh3" style="height: 100%;" lazy>
                <shopinquirsstatisticssh :filter="filter" ref="shopinquirsstatisticssh" style="height: 100%;"
                    :partInfo="infoBoolH" />
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="个人效率统计(售后组)" name="tabsh4" style="height: 100%;" lazy>
                <inquirsstatisticssh :filter="filter" ref="inquirsstatisticssh" style="height: 100%;"
                    :partInfo="infoBoolH" />
            </el-tab-pane> -->

            <el-tab-pane label="未匹配咨询数据" name="tab11" style="height: 100%;" lazy>
                <pddinquirsno1 :filter="filter" ref="pddinquirsno1" style="height: 100%;" />
            </el-tab-pane>

          <!-- <el-tab-pane v-if="showSh" label="店铺组效率统计" name="tab13" style="height: 100%;" lazy>
            <inquirsstatisticsmonth1 :filter="filter" ref="inquirsstatisticsmonth1" style="height: 100%;" />
          </el-tab-pane> -->

            <el-tab-pane label="合并店效率统计" name="tab23" style="height: 100%;" lazy>
                <pddmergeinquirsstatistics1 :filter="filter" ref="pddmergeinquirsstatistics1" style="height: 100%;" />
            </el-tab-pane>

        </el-tabs>
    </my-container>

</template>
<script>
import checkPermission from '@/utils/permission';
import MyContainer from "@/components/my-container";
import group1 from '@/views/customerservice/pdd/pddgroup1new';
import inquirs from '@/views/customerservice/pdd/pddinquirsnew';
// import groupinquirsstatistics from '@/views/customerservice/pdd/pddgroupinquirsstatisticsnew';
// import groupyitiinquirsstatistics from '@/views/customerservice/pdd/pddgroupyitiinquirsstatisticsnew';
import inquirsstatistics from '@/views/customerservice/pdd/pddinquirsstatisticsnew';
import shopinquirsstatistics from '@/views/customerservice/pdd/pddshopinquirsstatisticsnew';
// import pddinquirsno from '@/views/customerservice/pdd/pddinquirsnonew'


// import group1sh from '@/views/customerservice/pdd/sh/pddgroup1shnew'
// import inquirssh from '@/views/customerservice/pdd/sh/pddinquirsshnew'
// import groupinquirsstatisticssh from '@/views/customerservice/pdd/sh/pddgroupinquirsstatisticsshnew'
// import groupyitiinquirsstatisticssh from '@/views/customerservice/pdd/sh/pddgroupyitiinquirsstatisticsshnew'
// import inquirsstatisticssh from '@/views/customerservice/pdd/sh/pddinquirsstatisticsshnew'
// import shopinquirsstatisticssh from '@/views/customerservice/pdd/sh/pddshopinquirsstatisticsshnew'
// import newPerformanceStatement from '@/views/customerservice/pdd/newPerformanceStatement'
// import inquirsstatisticsmonth from '@/views/customerservice/pdd/sh/inquirsstatisticsmonthnew'
// import inquirsstatisticsmonth1 from '@/views/customerservice/pdd/sh/inquirsstatisticsmonth1new'
import pddinquirsno1 from '@/views/customerservice/pdd/pddinquirsno1new'

// import pddmergeinquirsstatistics from '@/views/customerservice/pdd/pddmergeinquirsstatisticsnew'
import pddmergeinquirsstatistics1 from '@/views/customerservice/pdd/pddmergeinquirsstatistics1new'
import shoppartition from '@/views/customerservice/pdd/shoppartition'
// import shoppartitionsh from '@/views/customerservice/pdd/sh/shoppartitionsh'
// import pddofflinemessagestatistics from '@/views/customerservice/pdd/pddofflinemessagestatistics'
// import pddofflinemessagestatisticssh from '@/views/customerservice/pdd/pddofflinemessagestatisticssh'
import pddGroupStaticsIndex from '@/views/customerservice/pdd/pddGroupStaticsIndex'

export default {
    name: "Users",
    provide() {
        return {
            reload: this.reload
        }
    },
    components: {
        MyContainer, group1, inquirs, /*groupinquirsstatistics, groupyitiinquirsstatistics,*/ inquirsstatistics, shopinquirsstatistics
        // , group1sh, inquirssh, groupinquirsstatisticssh, groupyitiinquirsstatisticssh, inquirsstatisticssh, shopinquirsstatisticssh
        /*, inquirsstatisticsmonth, inquirsstatisticsmonth1, pddmergeinquirsstatistics,*/, pddmergeinquirsstatistics1, /*pddinquirsno,*/ pddinquirsno1, shoppartition//, shoppartitionsh, pddofflinemessagestatistics,pddofflinemessagestatisticssh
        , pddGroupStaticsIndex 
    },
    data() {
        return {
            that: this,
            pageLoading: '',
            filter: {
            },
            shopList: [],
            userList: [],
            groupList: [],
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            activeName: 'tab0',
            //判断权限用
            IsSq: true,

            switchshow: false,
            //默认展示售前
            showSq: true,
            //默认展示售前
            showSh: false,
            isAllcheck: false,
            isShowTj: false,
            infoBool: false,//是否包含离组
            infoBoolH: false,
        };
    },
    mounted() {
        //售前组效率跳转到个人
        window.showpddtab4 = this.showtab4
        //售后组效率跳转到个人
        window.showpddtabSh4 = this.showtSh4

        if (checkPermission("PddSaleAfterInfo") && checkPermission("PddSaleBeforeInfo")) {
            this.isAllcheck = false;
            this.isShowTj = true;
        } else {
            this.isAllcheck = true;
            if (checkPermission("PddSaleBeforeInfo")) {
                this.activeName = 'tab0';
                this.showSq = true;
                this.switchshow = false;
                this.isShowTj = true;
            }
            else if (checkPermission("PddSaleAfterInfo")) {
                this.activeName = 'tabsh0';
                this.showSh = true;
                this.switchshow = true;
                this.showSq = false;
                this.isShowTj = true;
            } else {
                this.showSq = false;
                this.showSh = false;
                this.isShowTj = false;

            }
        }
    },
    methods: {
        showtab4() {
            this.activeName = "tab3"
        },
        showtSh4() {
            this.activeName = "tabsh4"
        },
        beforeLeave(visitName, currentName) {
            if (visitName == "switch")
                return false;
        },
        changeShowgroup() {
            if (this.switchshow) {
                this.activeName = 'tabsh0';
                this.showSh = true;
                this.showSq = false;
            } else {
                this.activeName = 'tab0';
                this.showSh = false;
                this.showSq = true;
            }
        },
        reload() {
            console.log('刷新所有分组下拉');
            //刷新其他页面的下拉框选项
            this.$refs.inquirssh.setShopSelect();
            this.$refs.groupinquirsstatisticssh.setGroupSelect();
            this.$refs.groupyitiinquirsstatisticssh.setGroupSelect();
            this.$refs.shopinquirsstatisticssh.setShopSelect();
            this.$refs.inquirsstatisticssh.setGroupSelect();

            this.$refs.inquirsstatisticsmonth.setGroupSelect();
            this.$refs.inquirsstatisticsmonth.setShopSelect();
            this.$refs.inquirsstatisticsmonth1.setGroupSelect();
            this.$refs.inquirsstatisticsmonth1.setShopSelect();
        },
        handleInfo(data) {
            this.infoBool = data
        },
        handleInfoH(data) {
            this.infoBoolH = data
        },
    },


};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
