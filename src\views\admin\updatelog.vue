<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="创建时间:">
                    <el-date-picker style="width: 240px"
                        v-model="filter.timerange"
                        type="datetimerange"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        range-separator="至"
                        start-placeholder="开始"
                        end-placeholder="结束"
                ></el-date-picker>
                </el-form-item>
                <el-form-item label="标题:">
                <el-input v-model="filter.title" placeholder="标题" style="width: 230px" @keyup.enter.native="onSearch" clearable/>
                </el-form-item>
                <el-form-item>
                <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
            </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='true'
        :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
            <template #column>
            <el-table-column>
                <span>
                <el-button>处理</el-button>
                </span>
            </el-table-column>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
        </template>

            <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="handVisible" 
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute">
            <div class="block table-wrapper" style="height:80%;padding:15px;">
                <el-scrollbar>
                    <el-card v-if="(handtype==2)" style="padding:1px;">
                    <el-descriptions :column="4" size="mini">
                            <el-descriptions-item :span="4" label="标题">{{reportsingle.title}}</el-descriptions-item>
                            <el-descriptions-item :span="4" label="更新内容">
                                   <div style="margin-top:-10px" v-html="reportsingle.content" >{{reportsingle.content}}</div> 
                                </el-descriptions-item>
                            <el-descriptions-item label="创建人">{{reportsingle.createdUserName}}</el-descriptions-item>
                            <el-descriptions-item label="创建时间">{{reportsingle.createdTime}}</el-descriptions-item>
                    </el-descriptions>
                </el-card>
                <div v-if="handtype!=2">
                <el-card>
                    <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
                </el-card>
                </div>
                <div class="drawer-footer">
                    <el-button @click.native="handVisible = false">取消</el-button>
                    <my-confirm-button type="submit" :loading="handLoading" @click="onSubmit()" v-if="handtype!=4" />
                </div>
            </el-scrollbar>
            </div>
            </el-drawer>    
    </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import {upLoadFile,upLoadImage} from '@/api/upload/file'
import { addLogUpdate, getUpdateLog, getUpdate } from '@/api/admin/opration-log'

const tableCols =[
      {istrue:true,prop:'title',label:'标题', width:'280'},
      {istrue:true,prop:'content',label:'更新内容', type:'editor',width:'460'},
      {istrue:true,prop:'createdUserName',label:'创建人', width:'100'},
      {istrue:true,prop:'createdTime',label:'创建时间', width:'auto'},
     ];
const tableHandles1=[
         {label:"新增日志", handle:(that)=>that.onHand(1)},
         {label:'编辑日志', handle:(that)=>that.onHand(3)},
         {label:'查看', handle:(that)=>that.onHand(2)},
         
      ];
const startDate = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminUpdatelog',
    components: {cesTable, MyContainer, MyConfirmButton},
    data() {
        return {
            that:this,
            filter: {
                title:'',
                timerange: [startDate, endDate],
                startDate:null,
                endDate:null,
            },
            list: [],
            pager:{OrderBy:"id",IsAsc:false},
            tableCols:tableCols,
            tableHandles:tableHandles1,
            uploadfilelist:[],
            fileList:[],
            uploadimagelist:[],
            imageList:[],
            handtype:1,
            formtitle:null,
            autoform:{
                    fApi:{},
                    options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 16 }},
                                                    upload: {props: {onError: function(r){alert('上传失败')}}}}},
                    rule:[]
                },
            total: 0,
            sels: [], 
            listLoading: false,
            pageLoading: false,
            handVisible: false,
            handLoading: false,
            reportsingle:{processList:[]},
            collapseactiveName: '1',
        };
    },
    async mounted() {
        formCreate.component('editor', FcEditor);
        await this.onSearch()
    },
    methods: {

        async initform(){
            let that = this
            this.autoform.rule= [{type:'hidden',field:'id',title:'id',value: ''},
                            {type:'input',field:'title',title:'标题',value: '',col:{span:24},validate: [{type: 'string', required: true, message:'请输入标题'}]},
                            {type:'editor',field:'content',title:'更新内容',value:'',col:{span:24},validate: [{type: 'string', required: true, message:'必填'}],
                            props:{init:async(editor)=>{await that.initeditor(editor) }}}]
            this.autoform.rule.forEach(f=>{
                    if (f.field=='toUserId1')  f.validate=[]
                    if (f.field=='toUserId2')  f.validate=[]
                })
        },
        async onSearch() {      
            this.$refs.pager.setPage(1)
            this.getlist()
            },
            async getlist() {
            var pager = this.$refs.pager.getPager()
            const params = {
                ...pager,
                ...this.pager,
                ... this.filter
            }
            this.listLoading = true
            if (this.filter.timerange) {
                params.startDate = this.filter.timerange[0];
                params.endDate = this.filter.timerange[1];
            }
            const res = await getUpdateLog(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total
            const data = res.data.list
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
        },
        async onHand(type){
            if (type>1&&(!this.selids||this.selids.length==0)){
                this.$message.warning("请先选择")
                return
            }
            else if ((type==3 || type == 2) &&(!this.selids||this.selids.length>1)){
                this.$message.warning("只能选择1行")
                return
            }

            this.handVisible = true
            this.handtype = type
           var res;
            if (type == 1){
                this.formtitle = '新增更新日志'
                await this.initform()
            }  
            else if (type == 2){
                var reportid=this.selids[0]
                res = await getUpdate({id:reportid})
                if (res.success){
                    this.reportsingle=res.data
                }
            }
            else if (type == 3){
                this.formtitle = '编辑更新日志'
                await this.initform()
                var reportid=this.selids[0]
                res = await getUpdate({id:reportid})
                if (res.success){
                    var model = {id: reportid, title: res.data? res.data.title : '', content: res.data? res.data.content : ''}
                    this.$nextTick(async () =>{
                        var arr = Object.keys(this.autoform.fApi)
                        if(arr.length > 0)
                        await this.autoform.fApi.resetFields()
                        await this.autoform.fApi.setValue(model)
                    })
                }
            }
            
        },
        async initeditor(editor){
            editor.config.uploadImgMaxSize = 3 * 1024 * 1024 
            editor.config.excludeMenus = ['emoticon','video']
            // editor.config.uploadImgAccept = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
            editor.config.uploadImgAccept = []
            // editor.customConfig.debug = true;
            editor.config.customUploadImg =async function (resultFiles, insertImgFn) {
                // console.log('resultFiles',resultFiles)
                // const form = new FormData();
                // form.append("image", resultFiles[0]);
                // const res =await upLoadImage(form);
                // var url=`${res.data}`
                // console.log('url',url)
                // insertImgFn(url)

                var xhr = new XMLHttpRequest()
                var formData = new FormData()
                formData.append('file', resultFiles[0])
                xhr.open('post', '/api/uploadnew/file/UploadCommonFileAsync')
                xhr.withCredentials = true
                xhr.responseType = 'json'
                xhr.send(formData)
                xhr.onreadystatechange = () => {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                         console.log('url', xhr.response.data.url)
                         insertImgFn(xhr.response.data.url)
                    }
                }
            }
        },
        async onSubmit() {
            const formData = this.autoform.fApi.formData();
            formData.reportId = this.reportsingle.id;
            
            await this.autoform.fApi.validate(async (valid, fail) => {
                if (valid){
                    this.handLoading = true
                    const formData = this.autoform.fApi.formData();
                    formData.id=formData.id?formData.id:0;
                    formData.Enabled=true;
                    var res;

                    if (this.handtype == 1){
                        res = await addLogUpdate(formData)
                    }
                    else if (this.handtype == 3){
                        res = await addLogUpdate(formData)
                    }
                    if (res?.success){
                        await this.getlist()
                        this.handVisible=false
                        this.handLoading = false
                    }
                }
                else{}
            })     
        },
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            this.onSearch();
        },
        selectchange:function(rows,row) {
            this.selids=[];
            rows.forEach(f=>{
                this.selids.push(f.id);
            })
        },
    },
};
</script>

<style lang="scss" scoped>

</style>