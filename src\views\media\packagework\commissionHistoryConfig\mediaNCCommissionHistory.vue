<template>
    <!-- 义乌提成 历史版本 -->
     <my-container> 
        <template #header> 
            <el-button style="height: 28px;" type="primary" @click="onSearch">刷新</el-button>  
            <el-button style="height: 28px;" type="primary" @click="onExeprotShootingTask"  >导出</el-button>  
        </template> 
        
            <vxetablebase :id="'mediaNCCommission'" 
            :hasSeq="false"
            :border="true" 
            :hasexpand='true' 
            :hascheck="false"
            :align="'center'"
            ref="table"  
            :that='that'  
            :tableData='tasklist'  
            :tableCols='tableCols'  
            :loading="listLoading" 
            ></vxetablebase> 
     
    </my-container>
</template>
<script>  
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import MyContainer from "@/components/my-container";   
import { getHistoryCommissionPositionInfo } from '@/api/media/shootingset';
 
const tableCols = [
    { istrue: true, label: '', width: '105', merge: true, prop: 'mergeField', 
        cols:[ { istrue: true, prop: 'sceneCode', label: '提成岗位', width: '105',  align: 'center' , fixed: 'left'  },
    ]},
    //新品拍摄
    { istrue: true, prop: '', label: '新品拍摄', merge: true, prop: 'mergeField1',
    cols:[
        { istrue: true, prop: 'shootingDeptCommission' , label: '部门总款', width: '52', align: 'center' },
        { istrue: true, prop: 'shootingPhotoCommission' , label: '照片总款', width: '52', align: 'center' },
        { istrue: true, prop: 'shootingPhotoPrice' , label: '照片单款', width: '52', align: 'center' },
        { istrue: true, prop: 'shootingVedioCommission' , label: '视频总款', width: '52', align: 'center' },
        { istrue: true, prop: 'shootingVedioPrice' , label: '视频单款', width: '52', align: 'center' },
        { istrue: true, prop: 'shootingMicroVedioPrice' , label: '微。单条', width: '52',  align: 'center' },
        { istrue: true, prop: 'shootingDetailCommission' , label: '详情页总款', width: '52', align: 'center' },
        { istrue: true, prop: 'shootingDetailPrice' , label: '详情页单款', width: '52', align: 'center' },
        { istrue: true, prop: 'shootingModelPhotoCommission' , label: '建模照片总数', width: '52', align: 'center' },
        { istrue: true, prop: 'shootingModelPhotoPrice' , label: '建模照片单张', width: '52', align: 'center' },
        { istrue: true, prop: 'shootingModelVedioCommission' , label: '建模视频总数', width: '52', align: 'center' },
        { istrue: true, prop: 'shootingModelVedioPrice' , label: '建模视频单个', width: '52', align: 'center' },
    ]},
    //微详情视频
    { istrue: true, prop: '', label: '微。视频', width: '140',merge: true, prop: 'mergeField2',
    cols:[
        { istrue: true, prop: 'microVedioCommission' , label: '微。拍摄单条', width: '52',  align: 'center' },
        { istrue: true, prop: 'microVedioPrice' , label: '微。建模单条', width: '52',  align: 'center' },
    ]},
    //直通车图
    { istrue: true, prop: '', label: '直通车图', width: '140',merge: true, prop: 'mergeField3',
    cols:[
        { istrue: true, prop: 'directDeptCommission' , label: '部门总组', width: '52',  align: 'center' },
        { istrue: true, prop: 'directPhotoCommission' , label: '照片总组', width: '52',  align: 'center' },  
        { istrue: true, prop: 'directPhotoPrice' , label: '照片单组', width: '52',  align: 'center' },
        { istrue: true, prop: 'directDesignCommission' , label: '设计总组', width: '52',  align: 'center' },
        { istrue: true, prop: 'directDesignPirce' , label: '设计单组', width: '52',  align: 'center' },
        { istrue: true, prop: 'directModelPhotoCommission' , label: '建模照片总张', width: '52',  align: 'center' },
        { istrue: true, prop: 'directModelPhotoPrice' , label: '建模照片单张', width: '52',  align: 'center' },
    ]},
    //店铺装修
    { istrue: true, prop: '', label: '店铺装修', width: '140',merge: true, prop: 'mergeField4',
    cols:[
        { istrue: true, prop: 'shopDeptCommission' , label: '部门总套', width: '52',  align: 'center' },
        { istrue: true, prop: 'shopDesignPrice' , label: '设计单套', width: '52',  align: 'center' },
        { istrue: true, prop: 'shopModelPhotoCommission' , label: '建模照片总张', width: '52',  align: 'center' },
        { istrue: true, prop: 'shopModelPhotoDirectPrice' , label: '建模照片单张', width: '52',  align: 'center' },
    ]},
    //包装设计
    { istrue: true, prop: '', label: '包装设计', width: '140',merge: true, prop: 'mergeField5',
    cols:[
        { istrue: true, prop: 'packageDeptCommission' , label: '部门总款', width: '52',  align: 'center' },
        { istrue: true, prop: 'packageDesignPrice' , label: '设计单套', width: '52',  align: 'center' },
        { istrue: true, prop: 'packageModelPhotoCommission' , label: '建模照片总张', width: '52',  align: 'center' }, 
        { istrue: true, prop: 'packageModelPhotoPrice' , label: '建模照片单张', width: '52',  align: 'center' },
    ]},
    //改图
    { istrue: true, prop: '', label: '改图', width: '140',merge: true, prop: 'mergeField6',
    cols:[
        { istrue: true, prop: 'imgCDeptCommission' , label: '部门总组', width: '52', align: 'center' },
        { istrue: true, prop: 'imgCPhotoCommission' , label: '照片总组', width: '52', align: 'center' },
        { istrue: true, prop: 'imgCPhotoPrice' , label: '照片单组', width: '52', align: 'center' },
        { istrue: true, prop: 'imgCVedioCommission' , label: '视频总组', width: '52', align: 'center' },
        { istrue: true, prop: 'imgCVedioPrice' , label: '视频单组', width: '52', align: 'center' },
        { istrue: true, prop: 'imgCDetailCommission' , label: '详情页总组', width: '52', align: 'center' },
        { istrue: true, prop: 'imgCDetailPrice' , label: '详情页单组', width: '52', align: 'center' },
        { istrue: true, prop: 'imgCModelPhotoCommission' , label: '建模照片总张', width: '52', align: 'center' },
        { istrue: true, prop: 'imgCModelPhotoPrice' , label: '建模照片单张', width: '52', align: 'center' },
        { istrue: true, prop: 'imgCModelVedioCommission' , label: '建模视频总数', width: '52', align: 'center' },
        { istrue: true, prop: 'imgCModelVedioPrice' , label: '建模视频单个', width: '52', align: 'center' },
    ]},
      //其他
      {
        istrue: true, prop: '', label: '短视频', width: '140', merge: true, prop: 'mergeField7',
        cols: [
            { istrue: true, prop: 'deptShortVideoPriceCommission', type: 'editNumber', label: '部门总条', width: '52', align: 'center' },
            { istrue: true, prop: 'shootShortVideoPriceCommission', type: 'editNumber', label: '拍摄总款', width: '52', align: 'center' },
            { istrue: true, prop: 'cuteShortVideoPriceCommission', type: 'editNumber', label: '剪切总款', width: '52', align: 'center' },
            { istrue: true, prop: 'shootShortVideoPrice', type: 'editNumber', label: '拍摄单款', width: '52', align: 'center' },
            { istrue: true, prop: 'cuteShortVideoPrice', type: 'editNumber', label: '剪切单款', width: '52', align: 'center' }, 
        ]
    },
    //其他
    { istrue: true, prop: '', label: '其他', width: '140',merge: true, prop: 'mergeField8',
    cols:[
        { istrue: true, prop: 'otherDeptCommission' , label: '部门总组', width: '52', align: 'center' },
        { istrue: true, prop: 'otherPhotoCommission' , label: '照片总组', width: '52', align: 'center' },
        { istrue: true, prop: 'otherPhotoPrice' , label: '照片单组', width: '52', align: 'center' },
        { istrue: true, prop: 'otherVedioCommission' , label: '视频总组', width: '52', align: 'center' },
        { istrue: true, prop: 'otherVedioPrice' , label: '视频单组', width: '52', align: 'center' },
        { istrue: true, prop: 'otherDetailCommission' , label: '详情页总组', width: '52', align: 'center' },
        { istrue: true, prop: 'otherDetailPrice' , label: '详情页单组', width: '52', align: 'center' },
        { istrue: true, prop: 'otherModelPhotoCommission' , label: '建模照片总张', width: '52', align: 'center' },
        { istrue: true, prop: 'otherModelPhotoPrice' , label: '建模照片单张', width: '52', align: 'center' },
        { istrue: true, prop: 'otherModelVedioCommission' , label: '建模视频总数', width: '52', align: 'center' },
        { istrue: true, prop: 'otherModelVedioPrice' , label: '建模视频单个', width: '52', align: 'center' },
    ]},
    
];
export default {
    components: { MyContainer,vxetablebase  }, 
    props:{ 
        classType:{type:Number,default:0}, 
        versionId:{type:String,default:"0"}, 
    },
    data() {
        return {
            that: this,  
            listLoading: false,
            tableCols: tableCols,  
            tasklist:[], 
            caclenum:1
        };
    },
    //向子组件注册方法
    provide () {
        return { 
        }
    },
    async mounted() {
        //await this.onSearh();
    }, 
    methods: {
        async onExeprotShootingTask() {
            this.$refs.table.exportData("薪资核算历史")
        },
        async onSearch(){ 
            this.listLoading = true; 
            let _this = this;
            let ret =  await getHistoryCommissionPositionInfo({companyType:_this.classType,versionId:_this.versionId});
            if(ret?.success){
                this.tasklist = ret.data;
            }
            this.listLoading = false;
        },
    },
};
</script> 
<style lang="scss" scoped>
.content{
    display: flex;
    flex-direction: row;
}
::v-deep .vxe-header--column .vxe-cell--edit-icon,::v-deep span.vxe-input--extra-suffix{
    display: none;
}
::v-deep .vxetoolbar20221212 {
    position: absolute;
    top: 30px;
    right: 5px;
    padding-top: 0;
    padding-bottom: 0;
    z-index: 999;
    background-color: rgb(*********** / 0%);
}

</style>

