<template>
     <my-container v-loading="pageLoading">
        <template #header> 
        </template>  
        <ces-table ref="table" 
                    :that='that' 
                    :isIndex='false' 
                    :hasexpand='false'
                    :isSelectColumn='true'  
                    :tableData='tasklist' 
                    :tableCols='tableCols' 
                    :tablekey="shootingPackageInfo" 
                    :loading="listLoading" > 
                    <el-table-column type="expand">
                        <template slot-scope="props">
                            <div>
                                <el-table :data="props.row.detaildata" style="width: 100%">
                                    <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                                    </el-table-column>
                                </el-table>
                            </div>
                        </template> 
                    </el-table-column>
                    <template slot='extentbtn'>
                    <el-button-group>
                        <el-button style="padding: 0;">
                            <el-input-number style=" width: 100px;"  :clearable="true" v-model="filter.microvedioTaskId"   v-model.trim="filter.microvedioTaskId" :min="1" :max="10000000" :controls="false" :precision="0"  placeholder="任务编号"  
                            ></el-input-number>
                        </el-button>
                        <el-button style="padding: 0;width: 150px;">
                            <el-input v-model="filter.productShortName"   v-model.trim="filter.productShortName" :maxlength =100 placeholder="产品简称" @keyup.enter.native="onSearch" clearable />
                        </el-button>
                        <el-button style="padding: 0;width: 90px;">
                            <el-select style="width:100%" v-model="filter.packStatus"  clearable filterable placeholder="打包状态">
                                <el-option label="排队中" value="0"/>
                                <el-option label="打包中" value="1"/>
                                <el-option label="打包失败" value="2"/>
                                <el-option label="打包成功" value="3"/>
                            </el-select>
                        </el-button>

                        <el-button style="padding: 0;width: 90px;">
                            <el-select style="width:100%" v-model="filter.enabled"    clearable filterable placeholder="状态">
                                <el-option label="正常" value="1"/> 
                                <el-option label="已删除" value="0"/>
                            </el-select>
                        </el-button>
                        <el-button style="padding: 0;margin: 0;">
                            <datepicker v-model="filter.Sdate"></datepicker>
                        </el-button>

                        <el-button type="primary" @click="onSearch">查询</el-button> 
                    </el-button-group>
            </template>
        </ces-table> 
        
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList" />
        </template>
    </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";  
import cesTable from "@/components/Table/table.vue";
import { pageShootingPackageViewTaskAsync} from '@/api/media/microvedio';
import { formatTime } from "@/utils";
const tableCols = [
    { istrue: true, prop: 'microVedioTaskId', label: '任务', width: '50'   },
    { istrue: true, prop: 'productShortName', label: '产品简称', width: '200'   },
    { istrue: true, prop: 'packStatusStr', label: '打包状态', width: '80' },
    { istrue: true, prop: 'deltr', label: '状态', width: '80'  },
  /*   { istrue: true, prop: 'pathOrErr', label: '错误信息', width: '80'  }, */
    { istrue: true, prop: 'createdUserName', label: '提交人', width: '100' },
    { istrue: true, prop: 'createdTime', label: '开始时间', width: '180'  },
    { istrue: true, prop: 'overTime', label: '完成时间', width: '180'  },
    {
        istrue: true, type: "button", label: '操作', width: "200",
        btnList: [
            { label: "下载",  handle: (that, row) => that.downUrl(row) },
            
        ]
    }
];   
export default { 
  components: { MyContainer , cesTable},
    data() {
        return { 
            shootingPackageInfo:'shootingPackageInfo',
            that: this,
            pageLoading: false,
            summaryarry:[],
            tasklist:[],
            sels: [], // 列表选中列
            tableCols: tableCols,
            listLoading: false,
            total:0,
            pager: { OrderBy: "createdTime", IsAsc: false },
            filter: {
                Sdate: [formatTime(new Date(), "YYYY-MM-DD "), formatTime(new Date(), "YYYY-MM-DD ")],
            }, 
        };
    },
    
    async mounted() {
    }, 
    methods: {
         downUrl(row) {
            if(row.packStatus ==3 && row.enabled){
                const domain = "/api/media/statics/"  + row.pathOrErr;
                window.open(domain,"_blank")
            }else{
                this.$message({message: "文件已不存在！",type: 'error'});
            }
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            this.getTaskList();
        },
        async getTaskList() 
        { 
            if (this.filter.Sdate) {
                    this.filter.startSdate = this.filter.Sdate[0];
                    this.filter.endSdate = this.filter.Sdate[1];
                }
                else {
                    this.filter.startSdate = null;
                    this.filter.endSdate = null;
                }
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.listLoading = true;
            const res = await pageShootingPackageViewTaskAsync(params); 
            this.listLoading = false;  
            this.total = res.data.total
            this.tasklist = res.data.list;
            //this.summaryarry = { videoTaskId_sum: " 0" };
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
    },
};
</script>
 

