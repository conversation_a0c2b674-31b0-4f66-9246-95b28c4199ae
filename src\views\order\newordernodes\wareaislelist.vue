<template >
    <my-container v-loading="pageLoading" >
        <ces-table style="height:95%" ref="openwebjushuitanjhpctable" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='aisleList' :isSelection='false' :summaryarry="summaryarry"
            :tableCols='tableCols' :isSelectColumn="true">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.warehouse" clearable filterable placeholder="仓库" style="width: 200px">
                            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input placeholder="通道" v-model="filter.aisleInfo" style="width: 130px" clearable
                            maxlength="20"></el-input>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input placeholder="白班负责人" v-model="filter.aisleNickName" style="width: 130px" clearable
                            maxlength="20"></el-input>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input placeholder="晚班负责人" v-model="filter.aisleNickName2" style="width: 130px" clearable
                            maxlength="20"></el-input>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onAddAisle">新增</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getNewOrderListAsync" />
        </template>

        <el-dialog :title="aisleDialogData.title" :visible.sync="aisleDialogData.visible" width="25%"
            :close-on-click-modal="false" element-loading-text="拼命加载中" v-dialogDrag v-loading="aisleDialogData.loading">
            <span>
                <el-form :model="aisleDialogData.formData" ref="addForm" :rules="aisleDialogData.formRules"
                    label-width="100px">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="仓库" prop="wms_co_id" v-if="addmode">
                                <el-select v-model="aisleDialogData.formData.wms_co_id" clearable filterable
                                    style="width:83%" placeholder="仓库">
                                    <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="白班负责人" prop="aisleDDUserId">
                                <el-select v-model="aisleDialogData.formData.aisleDDUserId" ref="aisleDDUserId" filterable
                                    remote reserve-keyword placeholder="白班负责人" :remote-method="getWareAisleUserList"
                                    :loading="aisleUserLoading" style="width:83%">
                                    <el-option v-for="item in aisleUserlist" :key="item.value" :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="晚班负责人" prop="aisleDDUserId2">
                                <el-select v-model="aisleDialogData.formData.aisleDDUserId2" ref="aisleDDUserId2" filterable
                                    remote reserve-keyword placeholder="晚班负责人" :remote-method="getWareAisleUserList2"
                                    :loading="aisleUserLoading" style="width:83%">
                                    <el-option v-for="item in aisleUserlist2" :key="item.value" :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="通道描述" prop="aisleInfo">
                                <el-input style="width:83%" :clearable="true"
                                    v-model.trim="aisleDialogData.formData.aisleInfo"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="通道前缀" prop="aislePrex">
                                <el-input style="width:83%" :clearable="true"
                                    v-model.trim="aisleDialogData.formData.aislePrex"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="起始序号" prop="aisleMin">
                                <el-input style="width:83%" :clearable="true"
                                    oninput="if(value){value=value.replace(/[^\-\d]/g,'')} if(value>1000000){value=1000000} if(value<0){value=0}"
                                    maxlength="10" v-model.trim="aisleDialogData.formData.aisleMin"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item label="终止序号" prop="aisleMax">
                                <el-input style="width:83%" :clearable="true"
                                    oninput="if(value){value=value.replace(/[^\-\d]/g,'')} if(value>1000000){value=1000000} if(value<0){value=0}"
                                    v-model.trim="aisleDialogData.formData.aisleMax"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="onAddAisleSave">保存</el-button>&nbsp;&nbsp;
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="aisleDialogData.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import { getTbWarehouseList } from '@/api/inventory/openwebjushuitan';
import { getWareAislePageList, getWareAisleUser, addOrUpdateWareAisle } from '@/api/inventory/inventoryorder';
const tableCols = [
    { istrue: true, prop: 'wms_co_id', label: '仓库', width: '260', sortable: 'custom', formatter: (row) => row.wms_co_name },
    { istrue: true, prop: 'aisleInfo', label: '通道描述', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'aislePrex', label: '通道前缀', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'aisleMin', label: '起始序号', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'aisleMax', label: '终止序号', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'aisleNickName', label: '白班负责人', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'nickName', label: '白班负责人上级', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'aisleNickName2', label: '晚班负责人', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'nickName2', label: '晚班负责人上级', width: '120', sortable: 'custom' },
    {
        istrue: true, type: 'button', label: '操作', width: '80',
        btnList: [
            {
                label: "编辑", handle: (that, row) => that.onEditAisle(row),
            },
        ]
    }
];
export default {
    name: "wareaislelist",
    components: { cesTable, MyContainer, datepicker },
    props: {

    },
    data() {
        return {
            addmode: true,
            aisleUserlist: [],
            aisleUserlist2: [],
            aisleUserLoading: false,
            warehouselist: [],
            pageLoading: false,
            tableCols: tableCols,
            aisleList: [],
            that: this,
            sels: [], // 列表选中列
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "createdTime", IsAsc: false },
            filter: {
                warehouse: null,
                aisleInfo: null,
                aisleNickName: null,
            },
            aisleDialogData: {
                title: "新增通道",
                visible: false,
                loading: false,
                formData: {
                    wms_co_id: null,
                    aisleDDUserId: null,
                    aisleDDUserId2: null,
                    aisleNickName: null,
                    aisleNickName2: null,
                    aisleInfo: null,
                    aislePrex: null,
                    aisleMin: null,
                    aisleMax: null,
                },
                formRules: {
                    wms_co_id: [{ required: true, message: '必填', trigger: 'blur' }],
                    aisleDDUserId: [{ required: true, message: '必填', trigger: 'blur' }],
                    aisleDDUserId2: [{ required: true, message: '必填', trigger: 'blur' }],
                    aisleInfo: [{ required: true, message: '必填', trigger: 'blur' }],
                    aislePrex: [{ required: true, message: '必填', trigger: 'blur' }],
                    aisleMin: [{ required: true, message: '必填', trigger: 'blur' }],
                    aisleMax: [{ required: true, message: '必填', trigger: 'blur' }],
                },
            },
        }
    },
    async mounted() {
        await this.getInventoryWareHouseList()
        this.onSearch();
    },
    methods: {
        async getWareAisleUserList(parm, parm2) {
            this.aisleUserlist = [];
            let options = [];
            let aisleUsers = await getWareAisleUser({ ddUserId: parm2, userName: parm });
            aisleUsers.forEach(f => {
                options.push({ value: f.ddUserId, label: f.userName });
            });
            this.aisleUserlist = options;
        },
        async getWareAisleUserList2(parm, parm2) {
            this.aisleUserlist2 = [];
            let options = [];
            let aisleUsers = await getWareAisleUser({ ddUserId: parm2, userName: parm });
            aisleUsers.forEach(f => {
                options.push({ value: f.ddUserId, label: f.userName });
            });
            this.aisleUserlist2 = options;
        },
        async getInventoryWareHouseList() {
            if (this.warehouselist.length <= 0) {
                let wares = await getTbWarehouseList();
                if (wares?.success && wares?.data && wares?.data.length > 0) {
                    wares?.data.forEach(f => {
                        this.warehouselist.push({ value: f.wms_co_id, label: f.name });
                    });
                }
            }
        },
        //排序查询      
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getNewOrderListAsync();
        },
        async getNewOrderListAsync() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            this.pageLoading = true;
            let res = await getWareAislePageList(params);
            this.pageLoading = false;
            if (res?.success) {
                this.total = res.data.total
                this.aisleList = res.data.list;
                //this.summaryarry = res.summary;
            }
        },
        async onAddAisle() {
            this.aisleDialogData.visible = true;
            this.aisleDialogData.title = "新增通道";
            this.addmode = true;
            this.aisleDialogData.formData = {};
        },
        async onAddAisleSave() {
            console.log(this.aisleDialogData.formData, "formData")
            this.aisleDialogData.loading = true;
            var res = await addOrUpdateWareAisle(this.aisleDialogData.formData);
            this.aisleDialogData.loading = false;
            if (res?.success) {
                this.$message({ message: '操作成功', type: "success" });
                this.aisleDialogData.visible = false;
                this.aisleDialogData.formData = {};
                this.onSearch()
            }
        },
        async onEditAisle(row) {
            this.addmode = false;
            this.aisleDialogData.visible = true;
            this.aisleDialogData.title = "编辑通道";
            this.aisleDialogData.formData = {};
            this.aisleDialogData.loading = true;
            await this.getWareAisleUserList("", row.aisleDDUserId);
            await this.getWareAisleUserList2("", row.aisleDDUserId2);
            const params = { currentPage: 1, pageSize: 1, aisleId: row.id };
            console.log(params);
            let res = await getWareAislePageList(params);
            this.aisleDialogData.loading = false;
            if (res?.success) {
                console.log(res.data.list[0]);
                this.aisleDialogData.formData = res.data.list[0];
            }
        },
    }
}
</script>