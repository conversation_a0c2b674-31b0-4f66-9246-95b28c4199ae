<template>
    <my-container v-loading="pageLoading" style="height: 100%">
        <el-tabs v-model="activeName" style="height: 94%">
            <el-tab-pane label="认领系列编码" name="first1" style="height: 100%">
                <procodesclaimlist ref="procodesclaimlist" style="height: 100%"></procodesclaimlist>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
  
<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import procodesclaimlist from "@/views/order/procodesclaim/procodesclaimlist.vue";
export default {
    name: "ProCodeClaimIndex",
    components: {
        cesTable, MyContainer, MyConfirmButton, procodesclaimlist
    },
    data() {
        return {
            that: this,
            pageLoading: false,
            activeName: "first1",
        };
    },
    async mounted() {

    },
    methods: {
    },
};
</script>
  
<style lang="scss" scoped></style>
  