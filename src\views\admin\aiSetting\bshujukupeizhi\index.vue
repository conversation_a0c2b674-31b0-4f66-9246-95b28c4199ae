<template>
  <MyContainer>
      <template #header>
      <div class="top">
        <el-select v-model="ListInfo.regionName" placeholder="区域" class="publicCss" clearable>
           <el-option v-for="(item) in regionNameList" :key="item" :label="item" :value="item" />
          </el-select>
        <el-select v-model="ListInfo.databaseName" placeholder="数据库" class="publicCss" clearable>
          <el-option v-for="(item) in lableList2" :key="item" :label="item" :value="item" />
        </el-select>
        <el-button type="primary" @click="getList('search')">查询</el-button>
        
        <el-button type="danger" @click="alldel" >批量删除</el-button>
        <el-button type="primary" @click="handleEdit({})" >新增</el-button>
      </div>
    </template>
    <!-- :footer-data="footerData" -->
    <template >
      <vxe-table
      border
      show-footer
      width="100%"
      height="100%"
      ref="newtable"
      :row-config="{height: 40}"
      show-overflow
      :loading="loading"
      :column-config="{resizable: true}"
      :merge-footer-items="mergeFooterItems"
      :footer-method="footerMethod"
      :span-method="mergeRowMethod"
      :row-class-name="rowClassName"
      @checkbox-all="selectAllChangeEvent"
      @checkbox-change="selectChangeEvent"
      :data="tableData">
      <vxe-column type="checkbox" width="60"></vxe-column>
      <vxe-column field="regionName" width="80" sortable='custom' title="区域"></vxe-column>
      <vxe-column field="dbType" width="150" sortable='custom' title="数据库类型"></vxe-column>
      <vxe-column field="dbHost" width="170" sortable='custom' title="主机" ></vxe-column>
      <vxe-column field="dbPort" width="220" sortable='custom' title="端口"></vxe-column>

      <vxe-column field="databaseName" width="200" sortable='custom' title="数据库名称" ></vxe-column>
      <vxe-column field="dbUsername" width="200" sortable='custom' title="用户名" ></vxe-column>
      <vxe-column field="dbPwd" width="180" sortable='custom' title="密码" ></vxe-column>
      <vxe-column field="updateTime" width="160"  sortable='custom' title="最后编辑时间" ></vxe-column>
      <vxe-column field="updateUserName" width="120" sortable='custom' title="最后编辑人" ></vxe-column>
      <vxe-column title="操作" footer-align="left" width="100" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="mini"  @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" size="mini" style="color:red" :disabled="scope.row.status==1" @click="handleRemove(scope.$index, scope.row)">删除</el-button>
          </template>
      </vxe-column>
    </vxe-table>
  </template>
    <!-- <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template> -->
    <el-dialog :title="title" :visible.sync="dialogVisibleEdit" size="25%" v-dialogDrag>
      <departmentEdit ref="departmentEdit" v-if="dialogVisibleEdit" :editInfo="editInfo" @search="closeGetlist"
          @cancellationMethod="dialogVisibleEdit = false" :lableList="lableList" :lableList2="lableList2" />
    </el-dialog>
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
          <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
              <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
              @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
          </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
      </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { downloadLink } from "@/utils/tools.js";
import { aiDbConfigPage, aiDbConfigListValue, employeeMealImport, aiDbConfigRemove } from '@/api/people/peoplessc.js';
import departmentEdit from "./departmentEdit.vue";
import checkPermission from '@/utils/permission'
const tableCols = [
]
export default {
  name: "scanCodePage",
  components: {
    MyContainer, vxetablebase, departmentEdit
  },
  data() {
      const tableData = [
  ]
  const footerData = [
  ]
  const mergeFooterItems = [
  ]
    return {
      downloadLink,
      dialogVisibleEdit: false,
      editInfo: {},
      fileList: [],
      dialogVisible: false,
      regionNameList: ['南昌','深圳', '武汉'],
        districtList: [],
        lableList2: [],
        lableList: [],
      timeCundang: '',
      title: '',
      tableData,
      footerData,
      mergeFooterItems,
      somerow: 'costType,region,monthDate',
        that: this,
        selList: [],
      timeRanges: [],
      tableCols,
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      ListInfo: {
            calculateMonthArr: [
                dayjs().subtract(1, 'month').format('YYYY-MM'),
                dayjs().subtract(1, 'month').format('YYYY-MM')],
        //   currentPage: 1,
        //   pageSize: 50,
        //   orderBy: null,
        //   isAsc: false,
        //   startTime: null,//开始时间
            // ledgerDate: dayjs().subtract(0, 'month').format('YYYY-MM'),//结束时间
        },
    }
  },
  async mounted() {
      await this.getxiala('databaseName', 'lableList2');

    await this.getList()
  },
  methods: {
      async getxiala(name, arr) {
          const { data } = await aiDbConfigListValue(name)
          if (data) {
              this[arr] = data;
          }
      },
      alldel() {
          if (this.selList.length == 0) {
              this.$message({ message: "请选择数据", type: "warning" });
              return false;
          }
          let ids = this.selList.map(item => item.id).join(',');
          this.$confirm('是否删除？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
          }).then(async() => {
              const { success } = await aiDbConfigRemove({ids: ids})
              if (success) {
                  this.$message({
                  type: 'success',
                  message: '删除成功!'
                  })
                  this.selList = []
                  await this.getList()
              }
          })
      },
      selectChangeEvent(data) {
          console.log("======11", data.records)
          this.selList = data.records;
      },
      selectAllChangeEvent(data) {
          console.log("======1", data)
          this.selList = data.records;
      },
      footerMethod({ columns, data }) {
          const sums = [];
          if (!this.footerData)
              return sums
          let newfield = columns.map(item => item.field)
          let newfooterdata = [];
          this.footerData.forEach((item, index) => {
              let newarr2 = [];
              newfield.forEach((item2, index2) => {
                  newarr2.push(item[item2])
              })
              newfooterdata.push(newarr2)
          })

          return newfooterdata;
      },
      rowClassName (event) {
          if(event.row.region == '合计'){
              return 'row-green'
          }
          return null
      },
      //上传文件
      onUploadRemove(file, fileList) {
      this.fileList = []
      },
      async onUploadChange(file, fileList) {
      this.fileList = fileList;
      },
      onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
      },
      async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
          this.$message({ message: "请先上传文件", type: "warning" });
          return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("file", item.file);
      form.append("isArchive", checkPermission("ArchiveStatusEditing"));
      form.append("calculateMonth", this.ListInfo.calculateMonth);
      var res = await employeeMealImport(form);
      if (res?.success){
          this.$message({ message: res.msg, type: "success" });
      }
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
      },
      onSubmitUpload() {
      if (this.fileList.length == 0) {
          this.$message({ message: "请先上传文件", type: "warning" });
          return false;
      }
      this.$refs.upload.submit();
      },
      //导入弹窗
      startImport() {
      this.fileList = []
      this.dialogVisible = true;
      },
      downExcel(){
          //下载excel
          downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250505/1919276865462648832.xlsx', '员工餐导入模板.xlsx');
      },
      async saveBane(){
            this.$confirm('是否存档？存档后不可修改！', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
              }).then(async () => {
                  const { data, success } = await dimissionManageArchive(this.ListInfo)
                  if(!success){
                      return;
                  }
                  this.getList();
                  this.$message.success('保存存档成功！')

              }).catch(() => {
                  // this.$message.error('取消')
              });
      },
      exportExcel(){
          this.$refs.newtable.exportData({filename:'员工餐',    sheetName: 'Sheet1',type: 'xlsx' })
      },
      closeGetlist(){
          this.dialogVisibleEdit = false;
          this.getList()
      },
      handleEdit(row){
        this.title = row.id?'编辑':'新增';
          this.editInfo = row;
          this.dialogVisibleEdit = true;
      },
      async handleRemove(index, row) {
        this.$confirm('是否删除！', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.editInfo = row;
          this.loading = true
          const {data, success} = await aiDbConfigRemove({ids: row.id})
          this.loading = false
          if (success) {
            this.$message.success('删除成功')
            this.getList();
          } else {
            this.$message.error('删除失败')
          }
        }).catch(() => {

        });
      },
      // 通用行合并函数（将相同多列数据合并为一行）
      mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
          const fields = this.somerow.split(',')
          const cellValue = row[column.property]
          if (cellValue && fields.includes(column.property)) {
          const prevRow = visibleData[_rowIndex - 1]
          let nextRow = visibleData[_rowIndex + 1]
          if (prevRow && prevRow[column.property] === cellValue) {
              return { rowspan: 0, colspan: 0 }
          } else {
              let countRowspan = 1
              while (nextRow && nextRow[column.property] === cellValue) {
              nextRow = visibleData[++countRowspan + _rowIndex]
              }
              if (countRowspan > 1) {
              return { rowspan: countRowspan, colspan: 1 }
              }
          }
          }
      },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    //导出数据,使用时将下面的方法替换成自己的接口
    // async exportProps() {
    //     const { data } = await exportStatData(this.ListInfo)
    //     const aLink = document.createElement("a");
    //     let blob = new Blob([data], { type: "application/vnd.ms-excel" })
    //     aLink.href = URL.createObjectURL(blob)
    //     aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
    //     aLink.click()
    // },
    async getList(type) {
      if (this.ListInfo.calculateMonthArr && this.ListInfo.calculateMonthArr.length > 0) {
            this.ListInfo.monthDate = this.ListInfo.calculateMonthArr.join(',');
        }else{
            this.ListInfo.monthDate = null;
        }
      this.loading = true
      const { data, success } = await aiDbConfigPage(this.ListInfo)
      if (success) {
        this.tableData = data.list
        console.log("111111", this.footerData)
      //   

      //取列表中的区域
      const newDistricts = this.tableData.map(item => item.costType).filter(district => district !== undefined && district !== null&& district.indexOf('小计')==-1&& district.indexOf('占比')==-1)
      this.districtList = Array.from(new Set([...this.districtList, ...newDistricts]));

        this.loading = false
      } else {
        this.loading = false
        this.$message.error('获取列表失败')
      }
    },
    formatNumberWithThousandSeparator(value){
      if (value === null || value === undefined) return value;
      return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

:deep(.vxe-header--column){
  // background: #00937e;
  // color: white;
  font-weight: 600;
}
:deep(.vxe-footer--row){
  background: #00937e;
  color: white;
  font-weight: 600;
}
:deep(.row-green) {
  background-color: rgb(247, 230, 193);
  // color: #fff;
}
</style>
