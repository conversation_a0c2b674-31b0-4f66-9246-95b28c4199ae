<template>
  <my-container v-loading="pageLoading" style="width: 100%;">
    <!-- 背景 star -->
    <div class="ybnrbj">
      <!-- 数据 star -->
      <div class="ybnrbjzt">
        <!-- 左边数据 star -->
        <div class="neiybnrbjzt ybnrbjl" v-show="!iswidth"
          :style="{ width: !leftiswidthTypestatistics ? '50%' : '100%', }">
          <!-- 左- 任务统计 star -->
          <div class="ybnrsj1">
            <!-- 左- 任务统计-标题 star -->
            <div class="ybnrbt">
              <div style="text-align:left;width:25%;">任务统计</div>
              <el-tooltip class="item" effect="dark" :content="leftdatadate.checkDataStr" placement="top">
                <div class="ybnrrqgd">
                  <div style="width:80px;"><i class="el-icon-date"></i>&nbsp;&nbsp;数据日期：</div>
                  <div>{{ leftdatadate.startDateTime ? leftdatadate.startDateTime.split(' ')[0] : '-' }}&nbsp;&nbsp;&nbsp;
                    至&nbsp;&nbsp;&nbsp;
                    {{ leftdatadate.endDateTime ? leftdatadate.endDateTime.split(' ')[0] : '-' }}
                  </div>
                </div>
              </el-tooltip>
              <div style="justify-content:right;width:25%;display: flex;">
                <div class="ybnrxtb" @click="switching('left')"><el-link :underline="false"><vxe-icon
                      name="repeat"></vxe-icon></el-link></div>
                <div class="ybnrxtb" @click="centerDialogVisible = true"><el-link :underline="false"><vxe-icon
                      name="save"></vxe-icon></el-link></div>
                <div class="ybnrxtb" @click="switchingBoth(1)"><el-link :underline="false"><vxe-icon
                      name="maximize"></vxe-icon></el-link></div>
                <div class="ybnrxtb" @click="openshowbar(1, 'left')"><el-link :underline="false"><vxe-icon
                      name="zoom-out"></vxe-icon></el-link></div>
              </div>
            </div>
            <!-- 左- 任务统计-标题 end -->

            <!-- 左- 任务统计-数据1 star -->
            <div v-if="ybisrwtjleft">
              <newcharbus v-if="taskswitching.visible" :toolbox="taskswitching.data.toolbox" ref="taskswitchingleft"
                :thisStyle="{
                  width: '100%', height: '275px', 'box-sizing': 'border-box', 'line-height': '300px'
                }" :analysisData="taskswitching.data" @baraction="taskwitching" :end="tableend">
              </newcharbus>
            </div>
            <!-- 左- 任务统计-数据1 end -->

            <!-- 左- 任务统计-数据2 star -->
            <div style="display: flex;justify-content: center;" v-show="!ybisrwtjleft">
              <div class="ybnrszkb">
                <div style="display: flex;justify-content: center;">
                  <div class="ybnrszkbtc" @click="tasknumberonedrawer(1, 'left')"
                    :class="ftybisrwz ? 'ybnrszkbcg1q' : 'ybnrszkbcg1h'" @mouseover="ftybisrwz = true"
                    @mouseout="ftybisrwz = false">
                    <div style="width:100%;display:flex;justify-content:center;">{{ fttaskstatistics.maintasktotal }}
                    </div>
                    <div style="width:100%;display:flex;justify-content:center;font-size: 14px;">任务条数</div>
                  </div>
                  <div class="ybnrszkbcg" @click="tasknumberonedrawer(2, 'left')"
                    :class="ftybisywc ? 'ybnrszkbcg2q' : 'ybnrszkbcg2h'" @mouseover="ftybisywc = true"
                    @mouseout="ftybisywc = false">
                    <div style="width:100%;display:flex;justify-content:center;">{{ fttaskstatistics.maintaskovertotal }}
                    </div>
                    <div style="width:100%;display:flex;justify-content:center;font-size: 14px;">已完成</div>
                  </div>
                  <div class="ybnrszkbcy" @click="tasknumberonedrawer(3, 'left')"
                    :class="ftybisjxz ? 'ybnrszkbcg3q' : 'ybnrszkbcg3h'" @mouseover="ftybisjxz = true"
                    @mouseout="ftybisjxz = false">
                    <div style="width:100%;display:flex;justify-content:center;">{{ fttaskstatistics.maintasklasttotal }}
                    </div>
                    <div style="width:100%;display:flex;justify-content:center;font-size: 14px;">进行中</div>
                  </div>
                </div>
                <div style="display: flex;justify-content: center;">
                  <div class="ybnrszkbtc" style="padding:30px 35px;" @click="tasknumberonedrawer(4, 'left')"
                    :class="ftybisrwz2 ? 'ybnrszkbcg1q' : 'ybnrszkbcg1h'" @mouseover="ftybisrwz2 = true"
                    @mouseout="ftybisrwz2 = false">
                    <div style="width:100%;display:flex;justify-content:center;font-size: 22px;">{{
                      fttaskstatistics.tasktotal }}</div>
                    <div style="width:100%;display:flex;justify-content:center;font-size: 14px;">任务总数</div>
                  </div>
                  <div class="ybnrszkbcg" style="padding:30px 35px;" @click="tasknumberonedrawer(5, 'left')"
                    :class="ftybisywc2 ? 'ybnrszkbcg2q' : 'ybnrszkbcg2h'" @mouseover="ftybisywc2 = true"
                    @mouseout="ftybisywc2 = false">
                    <div style="width:100%;display:flex;justify-content:center;font-size: 22px;">{{
                      fttaskstatistics.taskovertotal }}</div>
                    <div style="width:100%;display:flex;justify-content:center;font-size: 14px;">已完成</div>
                  </div>
                  <div class="ybnrszkbcy" style="padding:30px 35px;" @click="tasknumberonedrawer(6, 'left')"
                    :class="ftybisjxz2 ? 'ybnrszkbcg3q' : 'ybnrszkbcg3h'" @mouseover="ftybisjxz2 = true"
                    @mouseout="ftybisjxz2 = false">
                    <div style="width:100%;display:flex;justify-content:center;font-size: 22px;">{{
                      fttaskstatistics.tasklasttotal }}</div>
                    <div style="width:100%;display:flex;justify-content:center;font-size: 14px;">进行中</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 左- 任务统计-数据2 end -->
          </div>
          <!-- 左- 任务统计 end -->

          <!-- 左- 平台上新 star -->
          <div class="ybnrsj1">
            <!-- 左- 平台上新-标题 star -->
            <div class="ybnrbt">
              <div style="text-align:left;width:25%;">平台上新</div>
              <el-tooltip class="item" effect="dark" :content="leftdatadate.checkDataStr" placement="top">
                <div class="ybnrrqgd">
                  <div style="width:80px;"><i class="el-icon-date"></i>&nbsp;&nbsp;数据日期：</div>
                  <div>{{ leftdatadate.startDateTime ? leftdatadate.startDateTime.split(' ')[0] : '-' }}&nbsp;&nbsp;&nbsp;
                    至&nbsp;&nbsp;&nbsp;
                    {{ leftdatadate.endDateTime ? leftdatadate.endDateTime.split(' ')[0] : '-' }}
                  </div>
                </div>
              </el-tooltip>
              <div style="justify-content:right;width:25%;display: flex;">
                <div class="ybnrxtb" @click="switchtheplatform('left')"><el-link :underline="false"><vxe-icon
                      name="repeat"></vxe-icon></el-link></div>
                <div class="ybnrxtb" @click="centerDialogVisible = true"><el-link :underline="false"><vxe-icon
                      name="save"></vxe-icon></el-link></div>
                <div class="ybnrxtb" @click="switchtheplatformBoth(1)"><el-link :underline="false"><vxe-icon
                      name="maximize"></vxe-icon></el-link></div>
                <div class="ybnrxtb" @click="openshowbar(2, 'left')"><el-link :underline="false"><vxe-icon
                      name="zoom-out"></vxe-icon></el-link></div>
              </div>
            </div>
            <!-- 左- 平台上新-标题 end -->

            <!-- 左- 平台上新-数据1 star -->
            <div v-if="ybisptsxform">
              <newcharbus v-if="sptsxformching.visible" :toolbox="sptsxformching.data.toolbox"
                ref="sptsxformchingchingright" :thisStyle="{
                  width: '100%', height: '275px', 'box-sizing': 'border-box', 'line-height': '300px'
                }" :analysisData="sptsxformching.data" @baraction="formchingching" :end="formcritableend">
              </newcharbus>
              <!-- <div id="ybrwpttb" style="width: 100%; height: 300px"></div> -->
            </div>
            <!-- 左- 平台上新-数据1 end -->

            <!-- 左- 平台上新-数据2 star -->
            <div style="display: flex;justify-content: center;" v-show="!ybisptsxform">
              <div class="ybnrszkb" style="margin:0 10px;">
                <div style="display: flex;justify-content: center;margin:0 1.5%;">
                  <div class="ybnrszkbtx" @click="platformdrawer(1, 'left')"
                    :class="ftybisptz ? 'ybnrszkbtx4q' : 'ybnrszkbtx4h'" @mouseover="ftybisptz = true"
                    @mouseout="ftybisptz = false">
                    <div style="width:50%;display: flex;">淘系</div>
                    <div style="width:50%;display: flex;justify-content:right;font-size: 20px;">{{
                      ftplatformsummary.taoxinum }}</div>
                  </div>
                </div>
                <div style="display: flex;justify-content: center;margin:0 1.5%;">
                  <div class="ybnrszkbpdd" @click="platformdrawer(2, 'left')"
                    :class="ftybispddz ? 'ybnrszkbtx5q' : 'ybnrszkbtx5h'" @mouseover="ftybispddz = true"
                    @mouseout="ftybispddz = false">
                    <div style="width:50%;display: flex;">拼多多</div>
                    <div style="width:50%;display: flex;justify-content:right;font-size: 20px;">{{
                      ftplatformsummary.pddnum }}</div>
                  </div>
                </div>
                <div style="display: flex;justify-content: center;margin:0 1.5%;">
                  <div class="ybnrszkbqt" @click="platformdrawer(3, 'left')"
                    :class="ftybisqtz ? 'ybnrszkbtx6q' : 'ybnrszkbtx6h'" @mouseover="ftybisqtz = true"
                    @mouseout="ftybisqtz = false">
                    <div style="width:50%;display: flex;">其他</div>
                    <div style="width:50%;display: flex;justify-content:right;font-size: 20px;">{{
                      ftplatformsummary.allnum }}</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 左- 平台上新-数据2 end -->
          </div>
          <!-- 左- 平台上新 end -->

          <!-- 左- 类型统计 star -->
          <div class="ybnrsj1">
            <!-- 左- 类型统计-标题 star -->
            <div class="ybnrbt">
              <div style="text-align:left;width:25%;">类型统计</div>
              <el-tooltip class="item" effect="dark" :content="leftdatadate.checkDataStr" placement="top">
                <div class="ybnrrqgd">
                  <div style="width:80px;"><i class="el-icon-date"></i>&nbsp;&nbsp;数据日期：</div>
                  <div>{{ leftdatadate.startDateTime ? leftdatadate.startDateTime.split(' ')[0] : '-' }}&nbsp;&nbsp;&nbsp;
                    至&nbsp;&nbsp;&nbsp;
                    {{ leftdatadate.endDateTime ? leftdatadate.endDateTime.split(' ')[0] : '-' }}
                  </div>
                </div>
              </el-tooltip>
              <div style="justify-content:right;width:25%;display: flex;">
                <div class="ybnrxtb" @click="leftlingTaskStatistics"><el-link :underline="false"><vxe-icon
                      name="repeat"></vxe-icon></el-link></div>
                <div class="ybnrxtb" @click="centerDialogVisible = true"><el-link :underline="false"><vxe-icon
                      name="save"></vxe-icon></el-link></div>
                <div class="ybnrxtb"><el-link :underline="false"><vxe-icon name="maximize"></vxe-icon></el-link></div>
                <div class="ybnrxtb" @click="openshowbar(3, 'left')"><el-link :underline="false"><vxe-icon
                      name="zoom-out"></vxe-icon></el-link></div>
              </div>
            </div>
            <!-- 左- 类型统计-标题 end -->

            <!-- 左- 类型统计-数据1表 star -->
            <div v-show="!iswidth">
              <!-- <div id="ybrwlxtb" style="width: 100%; height: 300px"></div> -->
              <newcharbus v-if="statisticsType.visible" :toolbox="statisticsType.data.toolbox" ref="sptsxformchTyperight"
                :thisStyle="{
                  width: '100%', height: '275px', 'box-sizing': 'border-box', 'line-height': '300px'
                }" :analysisData="statisticsType.data" @baraction="statichingching" :end="statiritableend">
              </newcharbus>
            </div>
            <!-- 左- 类型统计-数据1表 end -->
          </div>
          <!-- 左- 类型统计 end -->
        </div>
        <!-- 左边数据 end -->
        <div class="neiybnrbjzt ybnrbjc" v-show="!iswidth"
          :style="{ width: !iswidth ? '0.5%' : '0', width: !iswidth2 ? '0.5%' : '0', }"></div>
        <!-- 右边数据 star -->
        <div class="neiybnrbjzt ybnrbjr" v-show="!iswidth2"
          :style="{ width: !rightiswidthTypestatistics ? '49.5%' : '100%', }">
          <!-- 右- 任务统计 star -->
          <div class="ybnrsj1">
            <!-- 右- 任务统计-标题 star -->
            <div class="ybnrbt">
              <div style="text-align:left;width:25%;">任务统计</div>
              <el-tooltip class="item" effect="dark" :content="rightdatadate.checkDataStr" placement="top">
                <div class="ybnrrqgd">
                  <div style="width:80px;"><i class="el-icon-date"></i>&nbsp;&nbsp;数据日期：</div>
                  <div>{{ rightdatadate.startDateTime ? rightdatadate.startDateTime.split(' ')[0] : '-'
                  }}&nbsp;&nbsp;&nbsp;
                    至&nbsp;&nbsp;&nbsp;
                    {{ rightdatadate.endDateTime ? rightdatadate.endDateTime.split(' ')[0] : '-' }}
                  </div>
                </div>
              </el-tooltip>
              <div style="justify-content:right;width:25%;display: flex;">
                <div class="ybnrxtb" @click="switching('right')"><el-link :underline="false"><vxe-icon
                      name="repeat"></vxe-icon></el-link></div>
                <div class="ybnrxtb" @click="centerDialogVisible = true"><el-link :underline="false"><vxe-icon
                      name="save"></vxe-icon></el-link></div>
                <div class="ybnrxtb" @click="switchingBoth(2)"><el-link :underline="false"><vxe-icon
                      name="maximize"></vxe-icon></el-link></div>
                <div class="ybnrxtb" @click="openshowbar(1, 'right')"><el-link :underline="false"><vxe-icon
                      name="zoom-out"></vxe-icon></el-link></div>
              </div>
            </div>
            <!-- 右- 任务统计-标题 end -->

            <!-- 右- 任务统计-数据1 star -->
            <div v-if="ybisrwtjright">
              <!-- <div id="ybrwtstb2" style="width: 100%; height: 300px"></div> -->
              <newcharbus v-if="ritaskswitching.visible" :toolbox="ritaskswitching.data.toolbox" ref="taskswitchingright"
                :thisStyle="{
                  width: '100%', height: '275px', 'box-sizing': 'border-box', 'line-height': '300px'
                }" :analysisData="ritaskswitching.data" @baraction="ritaskwitching" :end="ritableend">
              </newcharbus>
            </div>
            <!-- 右- 任务统计-数据1 end -->

            <!-- 右- 任务统计-数据2 star -->
            <div style="display: flex;justify-content: center;" v-show="!ybisrwtjright">
              <div class="ybnrszkb">
                <div style="display: flex;justify-content: center;">
                  <div class="ybnrszkbtc" @click="tasknumberonedrawer(1, 'right')"
                    :class="ftybisrwz ? 'ybnrszkbcg1q' : 'ybnrszkbcg1h'" @mouseover="ftybisrwz = true"
                    @mouseout="ftybisrwz = false">
                    <div style="width:100%;display:flex;justify-content:center;">{{ httaskstatistics.maintasktotal }}
                    </div>
                    <div style="width:100%;display:flex;justify-content:center;font-size: 14px;">任务条数</div>
                  </div>
                  <div class="ybnrszkbcg" @click="tasknumberonedrawer(2, 'right')"
                    :class="ftybisywc ? 'ybnrszkbcg2q' : 'ybnrszkbcg2h'" @mouseover="ftybisywc = true"
                    @mouseout="ftybisywc = false">
                    <div style="width:100%;display:flex;justify-content:center;">{{ httaskstatistics.maintaskovertotal }}
                    </div>
                    <div style="width:100%;display:flex;justify-content:center;font-size: 14px;">已完成</div>
                  </div>
                  <div class="ybnrszkbcy" @click="tasknumberonedrawer(3, 'right')"
                    :class="ftybisjxz ? 'ybnrszkbcg3q' : 'ybnrszkbcg3h'" @mouseover="ftybisjxz = true"
                    @mouseout="ftybisjxz = false">
                    <div style="width:100%;display:flex;justify-content:center;">{{ httaskstatistics.maintasklasttotal }}
                    </div>
                    <div style="width:100%;display:flex;justify-content:center;font-size: 14px;">进行中</div>
                  </div>
                </div>
                <div style="display: flex;justify-content: center;">
                  <div class="ybnrszkbtc" style="padding:30px 35px;" @click="tasknumberonedrawer(4, 'right')"
                    :class="ftybisrwz2 ? 'ybnrszkbcg1q' : 'ybnrszkbcg1h'" @mouseover="ftybisrwz2 = true"
                    @mouseout="ftybisrwz2 = false">
                    <div style="width:100%;display:flex;justify-content:center;font-size: 22px;">{{
                      httaskstatistics.tasktotal }}</div>
                    <div style="width:100%;display:flex;justify-content:center;font-size: 14px;">任务总数</div>
                  </div>
                  <div class="ybnrszkbcg" style="padding:30px 35px;" @click="tasknumberonedrawer(5, 'right')"
                    :class="ftybisywc2 ? 'ybnrszkbcg2q' : 'ybnrszkbcg2h'" @mouseover="ftybisywc2 = true"
                    @mouseout="ftybisywc2 = false">
                    <div style="width:100%;display:flex;justify-content:center;font-size: 22px;">{{
                      httaskstatistics.taskovertotal }}</div>
                    <div style="width:100%;display:flex;justify-content:center;font-size: 14px;">已完成</div>
                  </div>
                  <div class="ybnrszkbcy" style="padding:30px 35px;" @click="tasknumberonedrawer(6, 'right')"
                    :class="ftybisjxz2 ? 'ybnrszkbcg3q' : 'ybnrszkbcg3h'" @mouseover="ftybisjxz2 = true"
                    @mouseout="ftybisjxz2 = false">
                    <div style="width:100%;display:flex;justify-content:center;font-size: 22px;">{{
                      httaskstatistics.tasklasttotal }}</div>
                    <div style="width:100%;display:flex;justify-content:center;font-size: 14px;">进行中</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 右- 任务统计-数据2 end -->
          </div>
          <!-- 右- 任务统计 end -->

          <!-- 右- 平台上新 star -->
          <div class="ybnrsj1">
            <!-- 右- 平台上新-标题 star -->
            <div class="ybnrbt">
              <div style="text-align:left;width:25%;">平台上新</div>
              <el-tooltip class="item" effect="dark" :content="rightdatadate.checkDataStr" placement="top">
                <div class="ybnrrqgd">
                  <div style="width:80px;"><i class="el-icon-date"></i>&nbsp;&nbsp;数据日期：</div>
                  <div>{{ rightdatadate.startDateTime ? rightdatadate.startDateTime.split(' ')[0] : '-'
                  }}&nbsp;&nbsp;&nbsp;
                    至&nbsp;&nbsp;&nbsp;
                    {{ rightdatadate.endDateTime ? rightdatadate.endDateTime.split(' ')[0] : '-' }}
                  </div>
                </div>
              </el-tooltip>
              <div style="justify-content:right;width:25%;display: flex;">
                <div class="ybnrxtb" @click="switchtheplatform('right')"><el-link :underline="false"><vxe-icon
                      name="repeat"></vxe-icon></el-link></div>
                <div class="ybnrxtb" @click="centerDialogVisible = true"><el-link :underline="false"><vxe-icon
                      name="save"></vxe-icon></el-link></div>
                <div class="ybnrxtb" @click="switchtheplatformBoth(2)"><el-link :underline="false"><vxe-icon
                      name="maximize"></vxe-icon></el-link></div>
                <div class="ybnrxtb" @click="openshowbar(2, 'right')"><el-link :underline="false"><vxe-icon
                      name="zoom-out"></vxe-icon></el-link></div>
              </div>
            </div>
            <!-- 右- 平台上新-标题 end -->

            <!-- 右- 平台上新-数据1 star -->
            <div v-if="platybisrwtj">
              <!-- <div id="ybrwpttb2" style="width: 100%; height: 300px"></div> -->
              <newcharbus v-if="platyxformching.visible" :toolbox="platyxformching.data.toolbox"
                ref="platyxformchingchingright" :thisStyle="{
                  width: '100%', height: '275px', 'box-sizing': 'border-box', 'line-height': '300px'
                }" :analysisData="platyxformching.data" @baraction="platychingching" :end="platycritableend">
              </newcharbus>
            </div>
            <!-- 右- 平台上新-数据1 end -->

            <!-- 右- 平台上新-数据2 star -->
            <div style="display: flex;justify-content: center;" v-show="!platybisrwtj">
              <div class="ybnrszkb" style="margin:0 10px;">
                <div style="display: flex;justify-content: center;margin:0 1.5%;">
                  <div class="ybnrszkbtx" @click="platformdrawer(1, 'right')"
                    :class="ftybisptz ? 'ybnrszkbtx4q' : 'ybnrszkbtx4h'" @mouseover="ftybisptz = true"
                    @mouseout="ftybisptz = false">
                    <div style="width:50%;display: flex;">淘系</div>
                    <div style="width:50%;display: flex;justify-content:right;font-size: 20px;">{{
                      htplatformsummary.taoxinum }}</div>
                  </div>
                </div>
                <div style="display: flex;justify-content: center;margin:0 1.5%;">
                  <div class="ybnrszkbpdd" @click="platformdrawer(2, 'right')"
                    :class="ftybispddz ? 'ybnrszkbtx5q' : 'ybnrszkbtx5h'" @mouseover="ftybispddz = true"
                    @mouseout="ftybispddz = false">
                    <div style="width:50%;display: flex;">拼多多</div>
                    <div style="width:50%;display: flex;justify-content:right;font-size: 20px;">{{
                      htplatformsummary.pddnum }}</div>
                  </div>
                </div>
                <div style="display: flex;justify-content: center;margin:0 1.5%;">
                  <div class="ybnrszkbqt" @click="platformdrawer(3, 'right')"
                    :class="ftybisqtz ? 'ybnrszkbtx6q' : 'ybnrszkbtx6h'" @mouseover="ftybisqtz = true"
                    @mouseout="ftybisqtz = false">
                    <div style="width:50%;display: flex;">其他</div>
                    <div style="width:50%;display: flex;justify-content:right;font-size: 20px;">{{
                      htplatformsummary.allnum }}</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 右- 平台上新-数据2 end -->
          </div>
          <!-- 右- 平台上新 end -->

          <!-- 右- 类型统计 star -->
          <div class="ybnrsj1">
            <!-- 右- 类型统计-标题 star -->
            <div class="ybnrbt">
              <div style="text-align:left;width:25%;">类型统计</div>
              <el-tooltip class="item" effect="dark" :content="rightdatadate.checkDataStr" placement="top">
                <div class="ybnrrqgd">
                  <div style="width:80px;"><i class="el-icon-date"></i>&nbsp;&nbsp;数据日期：</div>
                  <div>{{ rightdatadate.startDateTime ? rightdatadate.startDateTime.split(' ')[0] : '-'
                  }}&nbsp;&nbsp;&nbsp;
                    至&nbsp;&nbsp;&nbsp;
                    {{ rightdatadate.endDateTime ? rightdatadate.endDateTime.split(' ')[0] : '-' }}
                  </div>
                </div>
              </el-tooltip>
              <div style="justify-content:right;width:25%;display: flex;">
                <div class="ybnrxtb" @click="rightlingTaskStatistics"><el-link :underline="false"><vxe-icon
                      name="repeat"></vxe-icon></el-link></div>
                <div class="ybnrxtb" @click="centerDialogVisible = true"><el-link :underline="false"><vxe-icon
                      name="save"></vxe-icon></el-link></div>
                <div class="ybnrxtb"><el-link :underline="false"><vxe-icon name="maximize"></vxe-icon></el-link></div>
                <div class="ybnrxtb" @click="openshowbar(3, 'right')"><el-link :underline="false"><vxe-icon
                      name="zoom-out"></vxe-icon></el-link></div>
              </div>
            </div>
            <!-- 右- 类型统计-标题 end -->

            <!-- 右- 类型统计-数据1表 star -->
            <div v-show="!iswidth2">
              <newcharbus v-if="rightsticsType.visible" :toolbox="rightsticsType.data.toolbox" ref="sptsxformchTyright"
                :thisStyle="{
                  width: '100%', height: '275px', 'box-sizing': 'border-box', 'line-height': '300px'
                }" :analysisData="rightsticsType.data" @baraction="statichingright" :end="statiritabright">
              </newcharbus>
            </div>
            <!-- 右- 类型统计-数据1表 end -->
          </div>
          <!-- 右- 类型统计 end -->
        </div>
        <!-- 右边数据 end -->
      </div>
      <!-- 数据 end -->

      <!-- 任务条数弹窗 star -->
      <div style="width:100%;">
        <el-drawer title="数据统计" size="60%" style="width:100%;" :visible.sync="tasknumberdrawer" :direction="direction"
          @close="statisticsClose">
          <div style="width:100%;box-sizing: border-box;padding: 0 35px;">
            <div class="tjbt" style="display: flex;">
              <div style="display: inline-block;">
                <span style="margin-right:5px;">
                  <el-date-picker size="mini" style="position: relative; top: 1px; width: 400px;" type="daterange"
                    align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" :picker-options="pickerOptions"
                    v-model="datastatistics.timerange" @change="changeTime" :disabled="questRadio.length > 0">
                  </el-date-picker>
                </span>
              </div>
              <div style="width:10%;display: inline-block;" v-if="questRadio.length > 0 && questRadio.includes('月统计')">
                <span style="margin-right:5px;">
                  <el-date-picker v-model="datastatistics.selYear" style="width: 175px;" type="year" placeholder="选择年"
                    format="yyyy" value-format="yyyy">
                  </el-date-picker>
                </span>
              </div>
              <div style="width:10%;display: inline-block;" v-if="questRadio.length > 0 && questRadio.includes('日统计')">
                <span style="margin-right:5px;">
                  <el-date-picker v-model="datastatistics.selMonth" style="width: 175px;" type="month" format="yyyy-MM"
                    value-format="yyyy-MM" placeholder="选择月">
                  </el-date-picker>
                </span>
              </div>
              <div style="width:5%;display: inline-block;">
                <span>
                  <el-button size="mini" type="primary" style="width: 75px;"
                    @click="tasknumberonedrawer(valNum, 'search')">查询</el-button>
                </span>
              </div>
              <div style="width:25%;display: inline-block;">
                <el-checkbox-group v-model="questRadio" @change="platformstatistics($event, 'one')"
                  class="questmy-checkbox-group">
                  <el-checkbox label="月统计" />
                  <el-checkbox label="日统计" />
                </el-checkbox-group>
              </div>
              <div style="flex: 1; display: inline-block;text-align:right;">
                <el-checkbox-group v-model="datastatistics.checkData">
                  <el-checkbox style="margin: 0px 10px" label="任务列表">任务列表</el-checkbox>
                  <el-checkbox style="margin: 0px 10px" label="已确认">已确认</el-checkbox>
                  <el-checkbox style="margin: 0px 10px" label="统计列表">统计列表</el-checkbox>
                  <el-checkbox style="margin: 0px 0px 0px 10px" label="存档">存档</el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
            <div style="font-size: 12px; color: #999; line-height: 36px; text-align: left;">
              <span v-for="(item, index) in summuydata" :key="item.seriesName">
                {{ item.seriesName }}: {{ item.seriestotal }}
                <span v-if="index !== summuydata.length - 1" style="margin-left: 15px;"></span>
              </span>
            </div>
            <div style="width:100%;">
              <newcharbus v-if="quantityprocessed.data" :toolbox="quantityprocessed.data.toolbox" ref="typrocesnewcharbus"
                :thisStyle="{
                  width: '100%', height: '400px', 'box-sizing': 'border-box', 'line-height': '300px'
                }" :analysisData="quantityprocessed.data" @action="tasknumberonedrawer(valNum, 'action')">
              </newcharbus>
            </div>
          </div>
        </el-drawer>
      </div>
      <!-- 任务条数弹窗 end -->

      <!-- 平台上新弹窗 star -->
      <div style="width:100%;">
        <el-drawer title="数据统计" size="60%" style="width:100%;" :visible.sync="newonplatformdrawer" :direction="direction"
          @close="classesClose">
          <div style="width:100%;box-sizing: border-box;padding: 0 35px;">
            <div class="tjbt" style="display: flex;">
              <div style="display: inline-block;">
                <span style="margin-right:5px;">
                  <el-date-picker size="mini" style="position: relative; top: 1px; width: 400px;" type="daterange"
                    align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" :picker-options="pickerOptions"
                    v-model="platformcs.timerange" @change="platformchangeTime" :disabled="platRadio.length > 0">
                  </el-date-picker>
                </span>
              </div>
              <div style="width:10%;display: inline-block;" v-if="platRadio.length > 0 && platRadio.includes('月统计')">
                <span style="margin-right:5px;">
                  <el-date-picker v-model="platformcs.selYear" style="width: 175px;" type="year" placeholder="选择年"
                    format="yyyy" value-format="yyyy">
                  </el-date-picker>
                </span>
              </div>
              <div style="width:10%;display: inline-block;" v-if="platRadio.length > 0 && platRadio.includes('日统计')">
                <span style="margin-right:5px;">
                  <el-date-picker v-model="platformcs.selMonth" style="width: 175px;" type="month" format="yyyy-MM"
                    value-format="yyyy-MM" placeholder="选择月">
                  </el-date-picker>
                </span>
              </div>
              <div style="width:5%;display: inline-block;">
                <span>
                  <el-button size="mini" type="primary" style="width: 75px;"
                    @click="platformdrawer(platNum, 'search')">查询</el-button>
                </span>
              </div>
              <div style="width:25%;display: inline-block;">
                <el-checkbox-group v-model="platRadio" @change="platformstatistics($event, 'two')"
                  class="platformy-checkbox-group">
                  <el-checkbox label="月统计" />
                  <el-checkbox label="日统计" />
                </el-checkbox-group>
              </div>
              <div style="flex: 1; display: inline-block;text-align:right;">
                <el-checkbox-group v-model="platformcs.checkData">
                  <el-checkbox style="margin: 0px 10px" label="任务列表">任务列表</el-checkbox>
                  <el-checkbox style="margin: 0px 10px" label="已确认">已确认</el-checkbox>
                  <el-checkbox style="margin: 0px 10px" label="统计列表">统计列表</el-checkbox>
                  <el-checkbox style="margin: 0px 0px 0px 10px" label="存档">存档</el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
            <div style="font-size: 12px; color: #999; line-height: 36px; text-align: left;">
              <span v-for="(item, index) in platformonsummuydata" :key="item.seriesName">
                {{ item.seriesName }}: {{ item.seriestotal }}
                <span v-if="index !== platformonsummuydata.length - 1" style="margin-left: 15px;"></span>
              </span>
            </div>
            <div style="width:100%;">
              <!-- <div id="ybyrsjtj3" style="width: 100%; height: 380px"></div> -->
              <newcharbus v-if="platformon.visible" ref="newcharbuschingright" :thisStyle="{
                width: '100%', height: '400px', 'box-sizing': 'border-box', 'line-height': '300px'
              }" :analysisData="platformon.data" @action="tasknumberonedrawer(valNum, 'action')">
              </newcharbus>
            </div>
          </div>
        </el-drawer>
      </div>
      <!-- 平台上新弹窗 end -->

      <!-- 保存版本弹窗 star -->
      <div>
        <el-dialog title="选择版本" :visible.sync="centerDialogVisible" width="30%" center>
          <div style="width:100%;padding:20px  0px;">
            <div style="height:50px;display: flex;justify-content: center;">
              <div style="width:100%;text-align:center;">
                <el-select size="mini" style="width:60%;" v-model="versionId" clearable placeholder="请选择版本名称">
                  <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
                <el-button size="mini" @click="refreshversion" style="margin-left:10px;" plain><i
                    class="el-icon-refresh-right"></i></el-button>
              </div>
            </div>
            <div style="display: flex;justify-content: center;">
              <div style="width:100%;text-align:center;">
                <el-input size="mini" style="width:60%;" v-model="versionName" placeholder="请输入版本名称"></el-input>
                <el-button size="mini" @click="newversioninformation" style="margin-left:10px;" plain><i
                    class="el-icon-plus"></i></el-button>
              </div>
            </div>
          </div>
          <div style="display: flex;justify-content: center;padding: 10px 0px 0px 0px;">
            <div style="width:100%;text-align:center;">
              <el-button type="danger" size="mini" style="margin-left:10px;" @click="deltask">删除版本</el-button>
            </div>
          </div>
          <div slot="footer" class="dialog-footer" style="margin-top: -20px;">
            <el-button size="mini" style="width:80px;" @click="centerDialogVisible = false">取 消</el-button>
            <el-button size="mini" style="width:80px;" type="primary" @click="versionarchive">确 定</el-button>
          </div>
        </el-dialog>
      </div>
      <!-- 保存版本弹窗 end -->
    </div>
    <!-- 背景 end -->
  </my-container>
</template>

<script>
import {
  getMainTaskStatistics, getMainTaskTotalStatistics, getMainShootingTaskStatistics, getMainStatTaskPopLittleTaskList,
  getMainStatPlatStatistics, getMainStatPlatStatisticsChat, getMainStatPlatGroupStatisticsChat, getMainStatPlatGroupPopStatisticsChat,
  getMainStatModelingTaskStatistics, getMainStatModelingTaskGroupStatistics, getMainStatTaskPopList
} from '@/api/media/directImgtask';
import { getMainStatVersionInfo, getMainStatVersionList, addainStatVersionList, saveMainStatVersionList, confirmMainStatVersionList, getMainStatVersionInfoById, delMainStatVersionInfoById } from '@/api/media/shootingset'
import * as echarts from 'echarts';
import MyContainer from "@/components/my-container";
import buschar from '@/components/Bus/buschar'
import newcharbus from "@/views/media/shooting/monthlyReport/newcharbus.vue";
import dayjs from 'dayjs'
import { formatTime } from "@/utils/tools";
export default {
  name: 'productShooting',
  components: { buschar, newcharbus, MyContainer },
  props: ['myValue', 'leftparams', 'rightparams'],

  data() {
    return {
      pageLoading: false,
      rightiswidthTypestatistics: false,
      rightiswidthplatform: false,
      rightiswidthstatistics: false,
      leftiswidthTypestatistics: false,
      leftiswidthplatform: false,
      leftiswidthstatistics: false,
      checked: '',
      leftvariable: {},//左数据
      rightvariable: {},//右数据
      leftdatadate: {},//数据日期
      rightdatadate: {},//数据日期
      statisticsType: { visible: false, title: "", data: {} },
      rightsticsType: { visible: false, title: "", data: {} },
      platyxformching: { visible: false, title: "", data: {} },
      sptsxformching: { visible: false, title: "", data: {} },
      taskswitching: { visible: false, title: "", data: {} },
      ritaskswitching: { visible: false, title: "", data: {} },
      quantityprocessed: { visible: false, title: "", data: {} },
      platformon: { visible: false, title: "", data: {} },
      platycritableend: false,
      statiritableend: false,
      statiritabright: false,
      onetable: {},
      twotable: {},
      statisticstableright: {},
      statisticstableleft: {},
      statisticsticstableright: {},
      statisticsticstableleft: {},

      summuydata: [],
      platformonsummuydata: [],
      statisticsdate: {},
      datastatistics: {
        timerange: [],
        selYear: null,
        selMonth: null,
        selByMonthOrDay: null,
        checkData: ['任务列表', '已确认', '存档'],
        startTime: null,
        endTime: null,
      },
      platformcs: {
        timerange: [],
        selYear: null,
        selMonth: null,
        selByMonthOrDay: null,
        checkData: ['任务列表', '已确认', '存档'],
        startTime: null,
        endTime: null,
      },
      valNum: null,
      platNum: null,
      tableend: false,
      formcritableend: false,
      ritableend: false,
      ftybisjxz2: false,
      ftybisywc2: false,
      ftybisrwz2: false,
      ftybisjxz: false,
      ftybisywc: false,
      ftybisrwz: false,
      fttypestatistics: {},//统计类型左
      httypestatistics: {},//类型统计右
      httaskstatistics: {},
      fttaskstatistics: {},
      htplatformsummary: {},
      ftplatformsummary: {},
      versionId: null,
      grouplist: [],
      typeId: 3,//直通车图为3
      versionName: null,
      ybisptsx: null,
      ybisptsxform: null,
      platybisrwtj: null,
      pickerOptions: {
        shortcuts: [{
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近半个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      bocty: [],
      value: '',
      input: null,
      statisticalyear: false,
      platformstatisticalyear: false,
      platformstatisticalmonth: false,
      statisticalmonth: false,
      checkList: ['任务列表', '已确认'],
      checkList2: ['任务列表', '已确认', '存档'],
      questRadio: [],
      platRadio: [],
      value2: '',
      value3: '',
      ybisrwtjright: false,
      ybisrwtjleft: false,
      iswidth: false,
      iswidth2: false,
      ybisrwtj: false,
      ybislxtj: false,
      iswidth4: false,
      htybisrwz: false,
      htybisywc: false,
      htybisjxz: false,
      htybisrwz2: false,
      htybisywc2: false,
      htybisjxz2: false,
      ftybisptz: false,
      htybisptz: false,
      ftybispddz: false,
      htybispddz: false,
      htybisqtz: false,
      ftybisqtz: false,
      num: 2,
      show: true,
      tasknumberdrawer: false,
      totaltasksdrawer: false,
      newonplatformdrawer: false,
      direction: 'btt',
      centerDialogVisible: false,
      divStyles: {
        ybnrsj1: '100%',
        ybnrsj2: '100%',
        ybnrsj3: '100%',
        ybnrsj4: '100%',
        ybnrsj5: '100%',
        ybnrsj6: '100%',
      },
    };
  },

  mounted() {
    this.onSearch()
  },

  methods: {
    async deltask() {
      if (this.versionId == null) {
        this.$message.error('请选择版本名称！');
        return
      }
      this.$confirm("是否删除版本？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const { success } = await delMainStatVersionInfoById({ versionId: this.versionId });
          if (success) {
            this.$message({ message: '删除版本成功！', type: "success" });
            await this.onSearch();
            this.$emit("deleteversion");
            this.versionId = null
            this.versionName = null
          }
        })
    },
    statisticsClose() {
      this.questRadio = []
      this.datastatistics.typeStr = null
      this.datastatistics.selMonth = null
      this.datastatistics.selYear = null
      this.datastatistics.selByMonthOrDay = null
      this.leftparams.startTime = null
      this.leftparams.endTime = null
      this.rightparams.startTime = null
      this.rightparams.endTime = null
    },
    classesClose() {
      this.platRadio = []
      this.platformcs.typeStr = null
      this.platformcs.selMonth = null
      this.platformcs.selYear = null
      this.platformcs.selByMonthOrDay = null
      this.leftparams.startTime = null
      this.leftparams.endTime = null
      this.rightparams.startTime = null
      this.rightparams.endTime = null
    },
    //右类型统计内
    async statichingright(params) {
      this.rightsticsType.visible = false;
      let queryInfo = { ...this.rightparams, clickName: params.name }
      const rest = await getMainStatModelingTaskGroupStatistics(queryInfo)
      this.statisticsticstableright = JSON.parse(JSON.stringify(this.rightsticsType.data));
      let res = JSON.parse(JSON.stringify(rest));
      res.tooltip = { trigger: "axis", axisPointer: { type: "cross", crossStyle: { color: "#999", }, }, };
      res.toolbox = { show: true, orient: 'vertical', left: 'right', top: 'center', feature: { mark: { show: true }, dataView: { show: true, readOnly: false }, magicType: { show: true, type: ['line', 'bar', 'stack'] }, restore: { show: true }, saveAsImage: { show: true } } };
      res.grid = { top: "55px", bottom: "60px", left: "60", right: "65", };
      res.series.map((item) => {
        item.itemStyle = {
          "normal": {
            "label": {
              "show": true,
              "position": "top",
              "textStyle": {
                "fontSize": 14
              }
            }
          }
        }
        item.emphasis = {
          "focus": "series"
        }
        item.smooth = false;
      })
      res.yAxis.forEach(item => {
        item.splitLine = {
          show: true
        };
      });
      // this.$nextTick(() => {
      //   this.$refs.sptsxformchingchingright.initcharts();
      // });
      this.rightsticsType.data = res
      let that = this;
      let graphic = [
        {
          type: "text",
          left: "right",
          top: 12,
          style: {
            text: "Back",
            fontSize: 14,
          },
          onclick: function () {
            that.statiritabright = false;
            that.rightsticsType.visible = false;
            that.rightsticsType.data = that.statisticsticstableright;
            setTimeout(() => {
              that.rightsticsType.visible = true;
            }, 200)
          },
        },
      ];
      that.statiritabright = true;
      this.rightsticsType.data.graphic = graphic
      this.rightsticsType.visible = true;
    },
    //左类型统计内
    async statichingching(params) {
      this.statisticsType.visible = false;
      let queryInfo = { ...this.leftparams, clickName: params.name }
      const rest = await getMainStatModelingTaskGroupStatistics(queryInfo)
      this.statisticsticstableleft = JSON.parse(JSON.stringify(this.statisticsType.data));
      let res = JSON.parse(JSON.stringify(rest));
      res.tooltip = { trigger: "axis", axisPointer: { type: "cross", crossStyle: { color: "#999", }, }, };
      res.toolbox = { show: true, orient: 'vertical', left: 'right', top: 'center', feature: { mark: { show: true }, dataView: { show: true, readOnly: false }, magicType: { show: true, type: ['line', 'bar', 'stack'] }, restore: { show: true }, saveAsImage: { show: true } } };
      res.grid = { top: "55px", bottom: "60px", left: "60", right: "65", };
      res.series.map((item) => {
        item.itemStyle = {
          "normal": {
            "label": {
              "show": true,
              "position": "top",
              "textStyle": {
                "fontSize": 14
              }
            }
          }
        }
        item.emphasis = {
          "focus": "series"
        }
        item.smooth = false;
      })
      res.yAxis.forEach(item => {
        item.splitLine = {
          show: true
        };
      });
      // this.$nextTick(() => {
      //   this.$refs.sptsxformchingchingright.initcharts();
      // });
      this.statisticsType.data = res
      let that = this;
      let graphic = [
        {
          type: "text",
          left: "right",
          top: 12,
          style: {
            text: "Back",
            fontSize: 14,
          },
          onclick: function () {
            that.statiritableend = false;
            that.statisticsType.visible = false;
            that.statisticsType.data = that.statisticsticstableleft;
            setTimeout(() => {
              that.statisticsType.visible = true;
            }, 200)
          },
        },
      ];
      that.statiritableend = true;
      this.statisticsType.data.graphic = graphic
      this.statisticsType.visible = true;
    },
    //右平台上新内
    async platychingching(params) {
      this.platyxformching.visible = false;
      let queryInfo = { ...this.rightparams, clickName: params.name }
      const rest = await getMainStatPlatGroupStatisticsChat(queryInfo)
      this.onetable = JSON.parse(JSON.stringify(this.platyxformching.data));
      let res = JSON.parse(JSON.stringify(rest));
      res.tooltip = { trigger: "axis", axisPointer: { type: "cross", crossStyle: { color: "#999", }, }, };
      res.toolbox = { show: true, orient: 'vertical', left: 'right', top: 'center', feature: { mark: { show: true }, dataView: { show: true, readOnly: false }, magicType: { show: true, type: ['line', 'bar', 'stack'] }, restore: { show: true }, saveAsImage: { show: true } } };
      res.grid = { top: "55px", bottom: "60px", left: "60", right: "65", };
      res.series.map((item) => {
        item.itemStyle = {
          "normal": {
            "label": {
              "show": true,
              "position": "top",
              "textStyle": {
                "fontSize": 14
              }
            }
          }
        }
        item.emphasis = {
          "focus": "series"
        }
        item.smooth = false;
      })
      res.yAxis.forEach(item => {
        item.splitLine = {
          show: true
        };
      });
      // this.$nextTick(() => {
      //   this.$refs.sptsxformchingchingright.initcharts();
      // });
      this.platyxformching.data = res
      let that = this;
      let graphic = [
        {
          type: "text",
          left: "right",
          top: 12,
          style: {
            text: "Back",
            fontSize: 14,
          },
          onclick: function () {
            that.platycritableend = false;
            that.platyxformching.visible = false;
            that.platyxformching.data = that.onetable;
            setTimeout(() => {
              that.platyxformching.visible = true;
            }, 200)
          },
        },
      ];
      that.platycritableend = true;
      this.platyxformching.data.graphic = graphic
      this.platyxformching.visible = true;
    },
    //右任务统计内
    async ritaskwitching(params) {
      this.ritaskswitching.visible = false;
      var queryInfo = { ...this.rightparams, clickName: params.seriesName }
      const rest = await getMainShootingTaskStatistics(queryInfo)
      this.statisticstableright = JSON.parse(JSON.stringify(this.ritaskswitching.data));
      //修改样式
      let res = JSON.parse(JSON.stringify(rest));
      res.tooltip = { trigger: "axis", axisPointer: { type: "cross", crossStyle: { color: "#999", }, }, };
      res.toolbox = { show: true, orient: 'vertical', left: 'right', top: 'center', feature: { mark: { show: true }, dataView: { show: true, readOnly: false }, magicType: { show: true, type: ['line', 'bar', 'stack'] }, restore: { show: true }, saveAsImage: { show: true } } };
      res.grid = { top: "55px", bottom: "60px", left: "60", right: "65", };
      res.series.map((item) => {
        item.itemStyle = {
          "normal": {
            "label": {
              "show": true,
              "position": "top",
              "textStyle": {
                "fontSize": 14
              }
            }
          }
        }
        item.emphasis = {
          "focus": "series"
        }
        item.smooth = false;
      })
      res.yAxis.forEach(item => {
        item.splitLine = {
          show: true
        };
      });
      // this.$nextTick(() => {
      //   this.$refs.taskswitchingright.initcharts();
      // });
      //
      this.ritaskswitching.data = res


      let that = this;
      let graphic = [
        {
          type: "text",
          left: "right",
          top: 12,
          style: {
            text: "Back",
            fontSize: 14,
          },
          onclick: function () {
            that.ritableend = false;
            that.ritaskswitching.visible = false;
            that.ritaskswitching.data = that.statisticstableright;
            setTimeout(() => {
              that.ritaskswitching.visible = true;
            }, 200)
          },
        },
      ];
      that.ritableend = true;
      this.ritaskswitching.data.graphic = graphic
      this.ritaskswitching.visible = true;
    },
    //左平台上新内
    async formchingching(params) {
      this.sptsxformching.visible = false;
      let queryInfo = { ...this.leftparams, clickName: params.name }
      const rest = await getMainStatPlatGroupStatisticsChat(queryInfo)
      this.twotable = JSON.parse(JSON.stringify(this.sptsxformching.data));
      let res = JSON.parse(JSON.stringify(rest));
      res.tooltip = { trigger: "axis", axisPointer: { type: "cross", crossStyle: { color: "#999", }, }, };
      res.toolbox = { show: true, orient: 'vertical', left: 'right', top: 'center', feature: { mark: { show: true }, dataView: { show: true, readOnly: false }, magicType: { show: true, type: ['line', 'bar', 'stack'] }, restore: { show: true }, saveAsImage: { show: true } } };
      res.grid = { top: "55px", bottom: "60px", left: "60", right: "65", };
      res.series.map((item) => {
        item.itemStyle = {
          "normal": {
            "label": {
              "show": true,
              "position": "top",
              "textStyle": {
                "fontSize": 14
              }
            }
          }
        }
        item.emphasis = {
          "focus": "series"
        }
        item.smooth = false;
      })
      res.yAxis.forEach(item => {
        item.splitLine = {
          show: true
        };
      });
      // this.$nextTick(() => {
      //   this.$refs.sptsxformchingchingright.initcharts();
      // });
      this.sptsxformching.data = res
      let that = this;
      let graphic = [
        {
          type: "text",
          left: "right",
          top: 12,
          style: {
            text: "Back",
            fontSize: 14,
          },
          onclick: function () {
            that.formcritableend = false;
            that.sptsxformching.visible = false;
            that.sptsxformching.data = that.twotable;
            setTimeout(() => {
              that.sptsxformching.visible = true;
            }, 200)
          },
        },
      ];
      that.formcritableend = true;
      this.sptsxformching.data.graphic = graphic
      this.sptsxformching.visible = true;
    },
    async switchtheplatformBoth(val) {
      this.switchtheplatform('left', val);
      this.switchtheplatform('right', val);
    },
    //左右平台上新
    async switchtheplatform(type, val) {
      if (type == 'left') {
        var params = { ...this.leftparams }
        if (val !== undefined) {
          this.ybisptsxform = !this.ybisptsxform;
        }
      } else {
        var params = { ...this.rightparams }
        if (val !== undefined) {
          this.platybisrwtj = !this.platybisrwtj;
        }
      }
      if (this.ybisptsxform || this.platybisrwtj) {
        this.formcritableend = false;
      }
      const rest = await getMainStatPlatStatisticsChat(params)
      let res = JSON.parse(JSON.stringify(rest));
      res.tooltip = { trigger: "axis", axisPointer: { type: "cross", crossStyle: { color: "#999", }, }, };
      res.toolbox = { show: true, orient: 'vertical', left: 'right', top: 'center', feature: { mark: { show: true }, dataView: { show: true, readOnly: false }, magicType: { show: true, type: ['line', 'bar', 'stack'] }, restore: { show: true }, saveAsImage: { show: true } } };
      res.grid = { top: "55px", bottom: "60px", left: "60", right: "65", };
      res.series.map((item) => {
        item.itemStyle = {
          "normal": {
            "label": {
              "show": true,
              "position": "top",
              "textStyle": {
                "fontSize": 14
              }
            }
          }
        }
        item.emphasis = {
          "focus": "series"
        }
        item.smooth = false;
      })
      res.yAxis.forEach(item => {
        item.splitLine = {
          show: true
        };
      });
      if (type == 'left') {
        this.sptsxformching.data = res
        this.sptsxformching.visible = false;
        setTimeout(() => {
          this.sptsxformching.visible = true;
        }, 200)
      } else {
        this.platyxformching.data = res
        this.platyxformching.visible = false;
        setTimeout(() => {
          this.platyxformching.visible = true;
        }, 200)
        // this.$nextTick(() => {
        //   this.$refs.taskswitchingright.initcharts();
        // });
      }
      // this.$nextTick(() => {
      //   this.$refs.sptsxformchingchingright.initcharts();
      // });
    },
    //左任务统计内
    async taskwitching(params) {
      this.taskswitching.visible = false;
      var queryInfo = { ...this.leftparams, clickName: params.seriesName }
      const rest = await getMainShootingTaskStatistics(queryInfo)
      this.statisticstableleft = JSON.parse(JSON.stringify(this.taskswitching.data));
      //修改样式
      let res = JSON.parse(JSON.stringify(rest));
      res.tooltip = { trigger: "axis", axisPointer: { type: "cross", crossStyle: { color: "#999", }, }, };
      res.toolbox = { show: true, orient: 'vertical', left: 'right', top: 'center', feature: { mark: { show: true }, dataView: { show: true, readOnly: false }, magicType: { show: true, type: ['line', 'bar', 'stack'] }, restore: { show: true }, saveAsImage: { show: true } } };
      res.grid = { top: "55px", bottom: "60px", left: "60", right: "65", };
      res.series.map((item) => {
        item.itemStyle = {
          "normal": {
            "label": {
              "show": true,
              "position": "top",
              "textStyle": {
                "fontSize": 14
              }
            }
          }
        }
        item.emphasis = {
          "focus": "series"
        }
        item.smooth = false;
      })
      res.yAxis.forEach(item => {
        item.splitLine = {
          show: true
        };
      });
      //
      this.taskswitching.data = res


      let that = this;
      let graphic = [
        {
          type: "text",
          left: "right",
          top: 12,
          style: {
            text: "Back",
            fontSize: 14,
          },
          onclick: function () {
            that.tableend = false;
            that.taskswitching.visible = false;
            that.taskswitching.data = that.statisticstableleft;
            setTimeout(() => {
              that.taskswitching.visible = true;
            }, 200)
          },
        },
      ];
      that.tableend = true;

      this.taskswitching.data.graphic = graphic

      this.taskswitching.visible = true;
      // this.$nextTick(() => {
      //   this.$refs.taskswitchingleft.initcharts();
      // });
    },
    changeTime(e) {
      if (e) {
        this.datastatistics.timerange = e
        this.datastatistics.startTime = e[0]
        this.datastatistics.endTime = e[1]
      } else {
        this.datastatistics.timerange = null
        this.datastatistics.startTime = null
        this.datastatistics.endTime = null
      }
    },
    platformchangeTime(e) {
      if (e) {
        this.platformcs.timerange = e
        this.platformcs.startTime = e[0]
        this.platformcs.endTime = e[1]
      } else {
        this.platformcs.timerange = null
        this.platformcs.startTime = null
        this.platformcs.endTime = null
      }
    },
    async leftproduct(ftlist) {
      this.leftvariable = ftlist
      const res1 = await getMainTaskStatistics(ftlist)
      if (!res1.success) {
        return
      }
      this.fttaskstatistics = res1.data
      const res2 = await getMainStatPlatStatistics(ftlist)
      if (!res2.success) {
        return
      }
      this.ftplatformsummary = res2.data
      this.leftlingTaskStatistics(ftlist)
      // this.getlist();//刷新数据日期
      this.refreshdata()
      this.switchingBoth()
      this.switchtheplatformBoth()
    },
    async leftlingTaskStatistics() {
      const res3 = await getMainStatModelingTaskStatistics(this.leftvariable)
      if (res3) {
        this.fttypestatistics = res3
        res3.series.map((item) => {
          item.itemStyle = {
            "normal": {
              "label": {
                "show": true,
                "position": "top",
                "textStyle": {
                  "fontSize": 14
                }
              }
            }
          }
          item.emphasis = {
            "focus": "series"
          }
          item.smooth = false;
        })
        this.statisticsType.data = res3
        this.statisticsType.visible = false;
        setTimeout(() => {
          this.statisticsType.visible = true
        }, 200)
      }
    },
    refreshdata() {
      this.refreshdatadate(this.leftvariable.versionId, 'left');
      this.refreshdatadate(this.rightvariable.versionId, 'right');
    },
    async refreshdatadate(val, label) {
      if (val) {
        const { data, success } = await getMainStatVersionInfoById({ versionId: val })
        if (label == 'left' && data !== null) {
          this.leftdatadate = { endDateTime: data.endDateTime, startDateTime: data.startDateTime, checkDataStr: data.checkDataStr ? data.checkDataStr : '' }
        }
        if (label == 'right' && data !== null) {
          this.rightdatadate = { endDateTime: data.endDateTime, startDateTime: data.startDateTime, checkDataStr: data.checkDataStr ? data.checkDataStr : '' }
        }
      }

      if (this.leftvariable.versionId == null || this.leftvariable.versionId == "") {
        this.leftdatadate = { endDateTime: this.leftvariable.endTime, startDateTime: this.leftvariable.startTime, checkDataStr: this.leftvariable.checkData ? this.leftvariable.checkData.join(',') : '' }
      }
      if (this.rightvariable.versionId == null || this.rightvariable.versionId == "") {
        this.rightdatadate = { endDateTime: this.rightvariable.endTime, startDateTime: this.rightvariable.startTime, checkDataStr: this.rightvariable.checkData ? this.rightvariable.checkData.join(',') : '' }
      }
    },
    async rightproduct(htlist) {
      this.rightvariable = htlist
      const res1 = await getMainTaskStatistics(htlist)
      if (!res1.success) {
        return
      }
      this.httaskstatistics = res1.data
      const res2 = await getMainStatPlatStatistics(htlist)
      if (!res2.success) {
        return
      }
      this.htplatformsummary = res2.data
      this.rightlingTaskStatistics(htlist)
    },
    async rightlingTaskStatistics() {
      const res3 = await getMainStatModelingTaskStatistics(this.rightvariable)
      if (res3) {
        this.httypestatistics = res3
        res3.series.map((item) => {
          item.itemStyle = {
            "normal": {
              "label": {
                "show": true,
                "position": "top",
                "textStyle": {
                  "fontSize": 14
                }
              }
            }
          }
          item.emphasis = {
            "focus": "series"
          }
          item.smooth = false;
        })
        this.rightsticsType.data = res3
        this.rightsticsType.visible = false;
        setTimeout(() => {
          this.rightsticsType.visible = true
        }, 200)
      }
    },
    versionarchive() {
      if (this.versionId == null) {
        this.$message({ message: "请选择版本名称！", type: "warning" });
        return
      }
      this.$emit("version", this.versionId);
    },
    onSearch() {
      // this.$refs.pager.setPage(1)
      this.getlist();
    },
    async getlist() {
      const { data, success } = await getMainStatVersionList({ typeId: this.typeId })
      if (success) {
        this.grouplist = data?.map(item => { return { value: item.id, label: item.versionName }; });
        if (Array.isArray(data)) {
          let finitiveData = null;
          let versionData = null;
          data.forEach(item => {
            if (item.leftOrRight == 1) {
              finitiveData = item;
            }
            if (item.leftOrRight == 2) {
              versionData = item;
            }
          });
          if (!finitiveData) {
            finitiveData = { endDateTime: this.leftvariable.endTime, startDateTime: this.leftvariable.startTime, checkDataStr: this.leftvariable.checkData ? this.leftvariable.checkData.join(',') : '' }
          }
          if (!versionData) {
            versionData = { endDateTime: this.rightvariable.endTime, startDateTime: this.rightvariable.startTime, checkDataStr: this.rightvariable.checkData ? this.rightvariable.checkData.join(',') : '' }
          }
          this.leftdatadate = finitiveData;
          this.rightdatadate = versionData;
        }

      }
    },
    async refreshversion() {
      const { data, success } = await getMainStatVersionList({ typeId: this.typeId, issave: false })
      if (success) {
        this.grouplist = data?.map(item => { return { value: item.id, label: item.versionName }; });
        this.$message({ message: "刷新版本成功", type: "success" });
      }
    },
    async newversioninformation() {
      if (this.versionName == null) {
        this.$message({ message: "请填写版本名称！", type: "warning" });
        return
      }
      const { success } = await addainStatVersionList({ versionName: this.versionName, typeId: this.typeId })
      if (success) {
        this.$message({ message: "新增版本成功", type: "success" });
      }
    },
    platformstatistics(val, type) {
      if (type == 'one') {
        this.datastatistics.timerange = []
        this.datastatistics.startTime = null
        this.datastatistics.endTime = null
        if (this.questRadio.length > 1) {
          this.questRadio.splice(0, 1)
          if (this.questRadio[0] == "日统计") {
            this.datastatistics.selYear = null
          } else {
            this.datastatistics.selMonth = null
          }
        }
        if (this.questRadio.length == 0) {
          let endTime = new Date();
          let startTime = new Date(endTime);
          startTime.setMonth(startTime.getMonth() - 1);
          this.datastatistics.startTime = startTime.toISOString().split('T')[0];
          this.datastatistics.endTime = endTime.toISOString().split('T')[0];
          this.datastatistics.timerange = [this.datastatistics.startTime, this.datastatistics.endTime]
          this.datastatistics.typeStr = ''
          this.datastatistics.selByMonthOrDay = null
          this.datastatistics.selYear = null
          this.datastatistics.selMonth = null
        }
        if (this.questRadio.includes('日统计')) {
          this.statisticalyear = true
          this.statisticalmonth = false
          this.datastatistics.selByMonthOrDay = 2
          this.datastatistics.selMonth = null
        } else {
          this.statisticalmonth = true
          this.statisticalyear = false
          this.datastatistics.selYear = null
          this.datastatistics.selByMonthOrDay = 1
        }
      } else {
        this.platformcs.timerange = []
        this.platformcs.startTime = null
        this.platformcs.endTime = null
        if (this.platRadio.length > 1) {
          this.platRadio.splice(0, 1)
          if (this.platRadio[0] == "日统计") {
            this.platformcs.selYear = null
          } else {
            this.platformcs.selMonth = null
          }
        }
        if (this.platRadio.length == 0) {
          let endTime = new Date();
          let startTime = new Date(endTime);
          startTime.setMonth(startTime.getMonth() - 1);
          this.platformcs.startTime = startTime.toISOString().split('T')[0];
          this.platformcs.endTime = endTime.toISOString().split('T')[0];
          this.platformcs.timerange = [this.platformcs.startTime, this.platformcs.endTime]
          this.platformcs.typeStr = ''
          this.platformcs.selByMonthOrDay = null
          this.platformcs.selYear = null
          this.platformcs.selMonth = null
        }
        if (this.platRadio.includes('日统计')) {
          this.platformstatisticalyear = true
          this.platformstatisticalmonth = false
          this.platformcs.selMonth = null
          this.platformcs.selByMonthOrDay = 2
        } else {
          this.platformstatisticalmonth = true
          this.platformstatisticalyear = false
          this.platformcs.selYear = null
          this.platformcs.platformcs = 1
        }
      }
    },
    async tasknumberonedrawer(val, type) {
      if (type == 'search' && this.datastatistics.selByMonthOrDay == 1 && this.datastatistics.timerange[0] == null) {
        if (this.datastatistics.selYear == null && this.questRadio.length > 0) {
          this.$message.error('请选择年份');
          return
        }
      }
      if (type == 'search' && this.datastatistics.selByMonthOrDay == 2 && this.datastatistics.timerange[0] == null) {
        if (this.datastatistics.selMonth == null && this.questRadio.length > 0) {
          this.$message.error('请选择月份');
          return
        }
      }
      if (this.datastatistics.timerange !== null && this.datastatistics.timerange.length !== 0) {
        this.datastatistics.selByMonthOrDay = null
      }
      if (this.datastatistics.selMonth) {
        this.datastatistics.selByMonthOrDay = 2
      }
      if (this.datastatistics.selYear) {
        this.datastatistics.selByMonthOrDay = 1
      }
      if (this.datastatistics.timerange == null) {
        this.datastatistics.timerange = []
      }
      if (type !== 'search' && this.datastatistics.timerange.length == 0) {
        if (type == 'left') {
          if (this.leftdatadate.startDateTime !== null && this.leftdatadate.startDateTime !== undefined && this.leftdatadate.endDateTime !== null && this.leftdatadate.endDateTime !== undefined) {
            this.datastatistics.startTime = this.leftdatadate.startDateTime.split(' ')[0];
            this.datastatistics.endTime = this.leftdatadate.endDateTime.split(' ')[0];
          } else {
            if (this.leftdatadate.endDateTime !== null && this.leftdatadate.endDateTime !== undefined) {
              let endDateTime = new Date(this.leftdatadate.endDateTime.split(' ')[0]);
              let startDateTime = new Date(endDateTime);
              startDateTime.setDate(startDateTime.getDate() - 30);
              this.datastatistics.startTime = startDateTime.toISOString().split('T')[0];
              this.datastatistics.endTime = endDateTime.toISOString().split('T')[0];
            } else {
              if (this.leftvariable.endTime !== null && this.leftvariable.endTime !== undefined && this.leftvariable.startTime !== null && this.leftvariable.startTime !== undefined) {
                this.datastatistics.startTime = this.leftvariable.startTime
                this.datastatistics.endTime = this.leftvariable.endTime
              } else {
                let endTime = new Date();
                let startTime = new Date(endTime);
                startTime.setMonth(startTime.getMonth() - 1);
                this.datastatistics.startTime = startTime.toISOString().split('T')[0];
                this.datastatistics.endTime = endTime.toISOString().split('T')[0];
              }
            }
          }
        } else {
          if (this.rightdatadate.startDateTime !== null && this.rightdatadate.startDateTime !== undefined && this.rightdatadate.endDateTime !== null && this.rightdatadate.endDateTime !== undefined) {
            this.datastatistics.startTime = this.rightdatadate.startDateTime.split(' ')[0];
            this.datastatistics.endTime = this.rightdatadate.endDateTime.split(' ')[0];
          } else {
            if (this.rightdatadate.endDateTime !== null && this.rightdatadate.endDateTime !== undefined) {
              let endDateTime = new Date(this.rightdatadate.endDateTime.split(' ')[0]);
              let startDateTime = new Date(endDateTime);
              startDateTime.setDate(startDateTime.getDate() - 30);
              this.datastatistics.startTime = startDateTime.toISOString().split('T')[0];
              this.datastatistics.endTime = endDateTime.toISOString().split('T')[0];
            } else {
              if (this.rightvariable.endTime !== null && this.rightvariable.endTime !== undefined && this.rightvariable.startTime !== null && this.rightvariable.startTime !== undefined) {
                this.datastatistics.startTime = this.rightvariable.startTime
                this.datastatistics.endTime = this.rightvariable.endTime
              } else {
                let endTime = new Date();
                let startTime = new Date(endTime);
                startTime.setMonth(startTime.getMonth() - 1);
                this.datastatistics.startTime = startTime.toISOString().split('T')[0];
                this.datastatistics.endTime = endTime.toISOString().split('T')[0];
              }
            }
          }
        }
      }
      // this.datastatistics.timerange = [];
      this.datastatistics.timerange[0] = this.datastatistics.startTime;
      this.datastatistics.timerange[1] = this.datastatistics.endTime;
      if (this.questRadio.length == 0 && type !== 'search') {
        this.questRadio = ["月统计"]
        this.datastatistics.timerange = []
        this.datastatistics.startTime = null
        this.datastatistics.endTime = null
        const currentYear = formatTime(dayjs(), "YYYY")
        this.datastatistics.selYear = currentYear;
        this.datastatistics.selByMonthOrDay = 1;
      }
      let params = {}
      this.valNum = val
      const clickNameMap = {
        1: '任务条数',
        2: '已完成',
        3: '进行中',
        4: '任务总数',
        5: '已完成1',
        6: '进行中1',
      };
      const clickName = clickNameMap[val];
      if (type == 'search' && type) {
        if (this.datastatistics.timerange == null || this.datastatistics.timerange.length == 0 || this.datastatistics.timerange == '') return this.$message.error('请选择日期')
        params = { ...this.datastatistics, clickName: clickName }
      } else {
        if (type == 'left') {
          this.leftparams.startTime = this.datastatistics.startTime;
          this.leftparams.endTime = this.datastatistics.endTime;
          params = { ...this.leftparams, clickName: clickName, ...this.datastatistics }
          params.checkData = ['任务列表', '已确认', '存档']
        } else {
          this.rightparams.startTime = this.datastatistics.startTime;
          this.rightparams.endTime = this.datastatistics.endTime;
          params = { ...this.rightparams, clickName: clickName, ...this.datastatistics }
          params.checkData = ['任务列表', '已确认', '存档']
        }
      }
      if (type == 'action') {
      } else {
        this.quantityprocessed.visible = false
        if (type !== 'search') {
          this.pageLoading = true;
        }
        delete params.versionId;
        let rest = await getMainStatTaskPopList(params)
        this.pageLoading = false;
        if (rest.success == false) {
          return
        }
        let res = JSON.parse(JSON.stringify(rest));
        res.tooltip = { trigger: "axis", axisPointer: { type: "cross", crossStyle: { color: "#999", }, }, };
        res.toolbox = { show: true, orient: 'vertical', left: 'right', top: 'center', feature: { mark: { show: true }, dataView: { show: true, readOnly: false }, magicType: { show: true, type: ['line', 'bar', 'stack'] }, restore: { show: true }, saveAsImage: { show: true } } };
        res.grid = { top: "55px", bottom: "60px", left: "60", right: "65", };
        // let optiondata = [{ smooth: 0, name: "任务总数", type: "line", id: "sales", barGap: "5%", label: { show: true, position: "top", }, emphasis: { focus: "series", }, tooltip: { valueFormatter: function (value) { return value + ""; }, }, },];
        res.series.map((item) => {
          item.itemStyle = {
            "normal": {
              "label": {
                "show": true,
                "position": "top",
                "textStyle": {
                  "fontSize": 14
                }
              }
            }
          }
          item.emphasis = {
            "focus": "series"
          }
          item.smooth = false;
        })
        // res.series.forEach((data, index) => {
        //   Object.assign(data, optiondata[index]);
        // });
        // this.statisticsdate = res
        this.quantityprocessed.visible = true;
        this.quantityprocessed.data = res
        this.summuydata = res.summuydata
        this.$nextTick(() => {
          this.$refs.typrocesnewcharbus.initcharts();
        });
        this.tasknumberdrawer = true
      }
    },

    async platformdrawer(val, type) {
      if (type == 'search' && this.platformcs.selByMonthOrDay == 1 && this.platformcs.timerange[0] == null) {
        if (this.platformcs.selYear == null && this.platRadio.length > 0) {
          this.$message.error('请选择年份');
          return
        }
      }
      if (type == 'search' && this.platformcs.selByMonthOrDay == 2 && this.platformcs.timerange[0] == null) {
        if (this.platformcs.selMonth == null && this.platRadio.length > 0) {
          this.$message.error('请选择月份');
          return
        }
      }
      if (this.platformcs.timerange !== null && this.platformcs.timerange.length !== 0) {
        this.platformcs.selByMonthOrDay = null
      }
      if (this.platformcs.selMonth) {
        this.platformcs.selByMonthOrDay = 2
      }
      if (this.platformcs.selYear) {
        this.platformcs.selByMonthOrDay = 1
      }
      if (this.platformcs.timerange == null) {
        this.platformcs.timerange = []
      }
      if (type !== 'search' && this.platformcs.timerange.length == 0) {
        if (type == 'left') {
          if (this.leftdatadate.startDateTime !== undefined && this.leftdatadate.endDateTime !== undefined && this.leftdatadate.startDateTime !== null && this.leftdatadate.endDateTime !== null) {
            this.platformcs.startTime = this.leftdatadate.startDateTime.split(' ')[0];
            this.platformcs.endTime = this.leftdatadate.endDateTime.split(' ')[0];
          } else {
            if (this.leftdatadate.endDateTime !== null) {
              let endDateTime = new Date(this.leftdatadate.endDateTime.split(' ')[0]);
              let startDateTime = new Date(endDateTime);
              startDateTime.setDate(startDateTime.getDate() - 30);
              this.platformcs.startTime = startDateTime.toISOString().split('T')[0];
              this.platformcs.endTime = endDateTime.toISOString().split('T')[0];
            } else {
              if (this.leftvariable.endTime !== null && this.leftvariable.startTime !== null) {
                this.platformcs.startTime = this.leftvariable.startTime
                this.platformcs.endTime = this.leftvariable.endTime
              } else {
                let endTime = new Date();
                let startTime = new Date(endTime);
                startTime.setMonth(startTime.getMonth() - 1);
                this.platformcs.startTime = startTime.toISOString().split('T')[0];
                this.platformcs.endTime = endTime.toISOString().split('T')[0];
              }
            }
          }
        } else {
          if (this.rightdatadate.startDateTime !== undefined && this.rightdatadate.endDateTime !== undefined && this.rightdatadate.startDateTime !== null && this.rightdatadate.endDateTime !== null) {
            this.platformcs.startTime = this.rightdatadate.startDateTime.split(' ')[0];
            this.platformcs.endTime = this.rightdatadate.endDateTime.split(' ')[0];
          } else {
            if (this.rightdatadate.endDateTime !== null) {
              let endDateTime = new Date(this.rightdatadate.endDateTime.split(' ')[0]);
              let startDateTime = new Date(endDateTime);
              startDateTime.setDate(startDateTime.getDate() - 30);
              this.platformcs.startTime = startDateTime.toISOString().split('T')[0];
              this.platformcs.endTime = endDateTime.toISOString().split('T')[0];
            } else {
              if (this.rightvariable.endTime !== null && this.rightvariable.startTime !== null) {
                this.platformcs.startTime = this.rightvariable.startTime
                this.platformcs.endTime = this.rightvariable.endTime
              } else {
                let endTime = new Date();
                let startTime = new Date(endTime);
                startTime.setMonth(startTime.getMonth() - 1);
                this.platformcs.startTime = startTime.toISOString().split('T')[0];
                this.platformcs.endTime = endTime.toISOString().split('T')[0];
              }
            }
          }
        }
      }
      this.platformcs.timerange = [];
      this.platformcs.timerange[0] = this.platformcs.startTime;
      this.platformcs.timerange[1] = this.platformcs.endTime;
      if (this.platRadio.length == 0 && type !== 'search') {
        this.platRadio = ["月统计"]
        this.platformcs.timerange = []
        this.platformcs.startTime = null
        this.platformcs.endTime = null
        const currentYear = formatTime(dayjs(), "YYYY")
        this.platformcs.selYear = currentYear;
        this.platformcs.selByMonthOrDay = 1;
      }
      let params = {}
      this.platNum = val
      const clickNameMap = {
        1: '淘系',
        2: '拼多多',
        3: '其他',
      };
      const clickName = clickNameMap[val];
      if (type == 'search' && type) {
        if (this.platformcs.timerange == null || this.platformcs.timerange.length == 0 || this.platformcs.timerange == '') return this.$message.error('请选择日期')
        params = { ...this.platformcs, clickName: clickName }
      } else {
        if (type == 'left') {
          this.leftparams.startTime = this.platformcs.startTime;
          this.leftparams.endTime = this.platformcs.endTime;
          params = { ...this.leftparams, clickName: clickName, ...this.platformcs }
          params.checkData = ['任务列表', '已确认', '存档']
        } else {
          this.rightparams.startTime = this.platformcs.startTime;
          this.rightparams.endTime = this.platformcs.endTime;
          params = { ...this.rightparams, clickName: clickName, ...this.platformcs }
          params.checkData = ['任务列表', '已确认', '存档']
        }
      }
      if (type !== 'search') {
        this.pageLoading = true;
      }
      delete params.versionId;
      const rest = await getMainStatPlatGroupPopStatisticsChat(params)
      this.pageLoading = false;
      let res = JSON.parse(JSON.stringify(rest));
      res.tooltip = { trigger: "axis", axisPointer: { type: "cross", crossStyle: { color: "#999", }, }, };
      res.toolbox = { show: true, orient: 'vertical', left: 'right', top: 'center', feature: { mark: { show: true }, dataView: { show: true, readOnly: false }, magicType: { show: true, type: ['line', 'bar', 'stack'] }, restore: { show: true }, saveAsImage: { show: true } } };
      res.grid = { top: "55px", bottom: "60px", left: "60", right: "65", };
      // let optiondata = [{ smooth: 0, name: "上新总数", type: "line", color: ['#5470c6',], id: "sales", barGap: "5%", label: { show: true, position: "top", }, emphasis: { focus: "series", }, tooltip: { valueFormatter: function (value) { return value + ""; }, }, },];
      res.series.map((item) => {
        item.itemStyle = {
          "normal": {
            "label": {
              "show": true,
              "position": "top",
              "textStyle": {
                "fontSize": 14
              }
            }
          }
        }
        item.emphasis = {
          "focus": "series"
        }
        item.smooth = false;
      })
      // res.series.forEach((data, index) => {
      //   Object.assign(data, optiondata[index]);
      // });
      this.platformon.visible = true;
      this.platformon.data = res
      this.platformonsummuydata = res.summuydata
      this.newonplatformdrawer = true
      this.$nextTick(() => {
        this.$refs.newcharbuschingright.initcharts();
      });
      // setTimeout(() => {
      //   this.ninechart();
      // }, 200)
    },
    async switchingBoth(val) {
      this.switching('left', val);
      this.switching('right', val);
    },
    //左右任务统计
    async switching(type, val) {
      // this.ybisrwtj = !this.ybisrwtj;
      if (type == 'left') {
        var params = { ...this.leftparams }
        if (val !== undefined) {
          this.ybisrwtjleft = !this.ybisrwtjleft;
        }
      } else {
        var params = { ...this.rightparams }
        if (val !== undefined) {
          this.ybisrwtjright = !this.ybisrwtjright;
        }
      }
      if (this.ybisrwtjright || this.ybisrwtjleft) {
        if (this.ybisrwtjleft) {
          this.tableend = false;
        } else if (this.ybisrwtjright) {
          this.ritableend = false;
        }
      }
      const rest = await getMainTaskTotalStatistics(params)
      let res = JSON.parse(JSON.stringify(rest));
      res.tooltip = { trigger: "axis", axisPointer: { type: "cross", crossStyle: { color: "#999", }, }, };
      res.toolbox = { show: true, orient: 'vertical', left: 'right', top: 'center', feature: { mark: { show: true }, dataView: { show: true, readOnly: false }, magicType: { show: true, type: ['line', 'bar', 'stack'] }, restore: { show: true }, saveAsImage: { show: true } } };
      res.grid = { top: "55px", bottom: "60px", left: "60", right: "65", };
      // let optiondata = [{ smooth: 0, name: "任务总数", type: "bar", id: "sales", barGap: "5%", label: { show: true, position: "top", }, emphasis: { focus: "series", }, tooltip: { valueFormatter: function (value) { return value + ""; }, }, },];
      res.series.map((item) => {
        item.itemStyle = {
          "normal": {
            "label": {
              "show": true,
              "position": "top",
              "textStyle": {
                "fontSize": 14
              }
            }
          }
        }
        item.emphasis = {
          "focus": "series"
        }
        item.smooth = false;
      })
      res.yAxis.forEach(item => {
        item.splitLine = {
          show: true
        };
      });
      if (type == 'left') {
        this.taskswitching.data = res
        this.taskswitching.visible = false;
        setTimeout(() => {
          this.taskswitching.visible = true;
        }, 200)
        // this.$nextTick(() => {
        //   this.$refs.taskswitchingleft.initcharts();
        // });
      } else {
        this.ritaskswitching.visible = false;
        this.ritaskswitching.data = res
        setTimeout(() => {
          this.ritaskswitching.visible = true;
        }, 200)
        // this.$nextTick(() => {
        //   this.$refs.taskswitchingright.initcharts();
        // });
      }
    },
    openshowbar(val, type) {
      if (type === 'left') {
        this.switchingBoth(1)
        this.switchtheplatformBoth(1)
        this.leftlingTaskStatistics()
        this.rightlingTaskStatistics()
        this.statisticsType.visible = false
        this.sptsxformching.visible = false
        this.taskswitching.visible = false
        this.leftiswidthTypestatistics = !this.leftiswidthTypestatistics
        this.iswidth2 = !this.iswidth2
        setTimeout(() => {
          this.statisticsType.visible = true
          this.sptsxformching.visible = true
          this.taskswitching.visible = true
        }, 200)
      } else if (type === 'right') {
        this.switchingBoth(2)
        this.switchtheplatformBoth(2)
        this.rightlingTaskStatistics()
        this.leftlingTaskStatistics()
        this.rightsticsType.visible = false
        this.platyxformching.visible = false
        this.ritaskswitching.visible = false
        this.rightiswidthTypestatistics = !this.rightiswidthTypestatistics
        this.iswidth = !this.iswidth
        setTimeout(() => {
          this.rightsticsType.visible = true
          this.platyxformching.visible = true
          this.ritaskswitching.visible = true
        }, 200)
      }
    },
  },
};

</script>

<style lang="scss" scoped>
// 背景
.ybnrbj {
  width: 100%;
  min-width: 1250px;
  background-color: #f3f4f6;
  box-sizing: border-box;
  padding: 0 10px;
}

// 背景


.ybnrbjzt {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.ybnrbjl {
  width: 50%;
  transition: 0.2s;
}

.ybnrbjr {
  width: 50%;
  transition: 0.2s;
}

.ybnrsj1 {
  width: 100%;
  height: 350px;
  background-color: #fff;
  border-radius: 10px;
  margin: 10px 0;
  box-sizing: border-box;
  padding: 15px 25px;
}

.ybnrbt {
  width: 100%;
  height: 38px;
  line-height: 30px;
  font-size: 14px;
  color: #333;
  /* background-color: #6085ff;  */
  display: flex;
  -webkit-user-select: none;
  user-select: none;
  display: flex;
  justify-content: space-between;
}

.ybnrrqgd {
  width: 50%;
  height: 30px;
  line-height: 30px;
  font-size: 12px;
  color: #aaa;
  box-sizing: border-box;
  padding: 0 30px;
  display: flex;
  justify-content: center;
  -webkit-user-select: none;
  user-select: none;
}

.ybnrxtb {
  width: 28px;
  height: 28px;
  /* background-color: #000; */
  text-align: center;
  border-radius: 5px;
}

.ybnrxtb:hover {
  width: 28px;
  height: 28px;
  background-color: #f2f2f2;
  text-align: center;
  border-radius: 5px;
}

.ybnrszkb {
  width: 100%;
  height: 260px;
  // background-color: #0b2a3a;
  box-sizing: border-box;
  padding: 0 80px;
}

// 任务统计-数据2
.ybnrszkbtc {
  width: 30%;
  height: 110px;
  font-size: 26px;
  color: #409eff;
  margin: 10px 2%;
  background-color: #f5faff;
  border-radius: 10px;
  box-sizing: border-box;
  padding: 28px 35px;
  cursor: pointer;
}

.ybnrszkbcg1q {
  height: 110px;
  font-size: 30px;
  color: #fff;
  background-color: #409eff;
  transition: 0.5s;
  -webkit-user-select: none;
  user-select: none;
}

.ybnrszkbcg {
  width: 30%;
  height: 110px;
  font-size: 26px;
  color: #67c23a;
  margin: 10px 2%;
  background-color: #f0f9eb;
  border-radius: 10px;
  box-sizing: border-box;
  padding: 28px 35px;
  cursor: pointer;
}

.ybnrszkbcg2q {
  height: 110px;
  font-size: 30px;
  color: #fff;
  background-color: #67c23a;
  transition: 0.5s;
  -webkit-user-select: none;
  user-select: none;
}

.ybnrszkbcy {
  width: 30%;
  height: 110px;
  font-size: 26px;
  color: #f5be6d;
  margin: 10px 2%;
  background-color: #fdf6ec;
  border-radius: 10px;
  box-sizing: border-box;
  padding: 28px 35px;
  cursor: pointer;
}

.ybnrszkbcg3q {
  height: 110px;
  font-size: 30px;
  color: #fff;
  background-color: #f5be6d;
  transition: 0.5s;
  -webkit-user-select: none;
  user-select: none;
}

.ybnrszkbtc {
  width: 30%;
  height: 110px;
  font-size: 26px;
  color: #409eff;
  margin: 10px 2%;
  background-color: #f5faff;
  border-radius: 10px;
  box-sizing: border-box;
  padding: 28px 35px;
  cursor: pointer;
}

.ybnrszkbcg1q {
  height: 110px;
  font-size: 30px;
  color: #fff;
  background-color: #409eff;
  transition: 0.5s;
  -webkit-user-select: none;
  user-select: none;
}

.ybnrszkbcg {
  width: 30%;
  height: 110px;
  font-size: 26px;
  color: #67c23a;
  margin: 10px 2%;
  background-color: #f0f9eb;
  border-radius: 10px;
  box-sizing: border-box;
  padding: 28px 35px;
  cursor: pointer;
}

.ybnrszkbcg2q {
  height: 110px;
  font-size: 30px;
  color: #fff;
  background-color: #67c23a;
  transition: 0.5s;
  -webkit-user-select: none;
  user-select: none;
}

.ybnrszkbcy {
  width: 30%;
  height: 110px;
  font-size: 26px;
  color: #f5be6d;
  margin: 10px 2%;
  background-color: #fdf6ec;
  border-radius: 10px;
  box-sizing: border-box;
  padding: 28px 35px;
  cursor: pointer;
}

.ybnrszkbcg3q {
  height: 110px;
  font-size: 30px;
  color: #fff;
  background-color: #f5be6d;
  transition: 0.5s;
  -webkit-user-select: none;
  user-select: none;
}

// 任务统计-数据2

// 平台上新-数据2
.ybnrszkbtx {
  width: 100%;
  height: 75px;
  font-size: 14px;
  color: #555;
  margin: 6px 0;
  background-color: #f7f7f7;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  padding: 0 35px;
}

.ybnrszkbtx4q {
  width: 100%;
  height: 75px;
  font-size: 16px;
  color: #fff;
  margin: 6px 0;
  background-color: #ff5200;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  padding: 0 35px;
  transition: 0.5s;
  -webkit-user-select: none;
  user-select: none;
}

.ybnrszkbpdd {
  width: 100%;
  height: 75px;
  font-size: 14px;
  color: #555;
  margin: 6px 0;
  background-color: #f7f7f7;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  padding: 0 35px;
}

.ybnrszkbtx5q {
  width: 100%;
  height: 75px;
  font-size: 16px;
  color: #fff;
  margin: 6px 0;
  background-color: #f50008;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  padding: 0 35px;
  transition: 0.5s;
  -webkit-user-select: none;
  user-select: none;
}

.ybnrszkbqt {
  width: 100%;
  height: 75px;
  font-size: 14px;
  color: #555;
  margin: 6px 0;
  background-color: #f7f7f7;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  padding: 0 35px;
}

.ybnrszkbtx6q {
  width: 100%;
  height: 75px;
  font-size: 16px;
  color: #fff;
  margin: 6px 0;
  background-color: #3c3c3c;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  padding: 0 35px;
  transition: 0.5s;
  -webkit-user-select: none;
  user-select: none;
}

// 平台上新-数据2
::v-deep .el-drawer__header {
  margin-bottom: 20px !important;
  padding-bottom: 15px;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
}

.radioGrp {
  margin-bottom: 10px;
  display: flex;
}

.questmy-checkbox-group ::v-deep .el-checkbox__inner {
  border-radius: 7px !important;
}

.platformy-checkbox-group ::v-deep .el-checkbox__inner {
  border-radius: 7px !important;
}
</style>
