1
<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-select v-model="Filter.platformInt" placeholder="平台" filterable clearable>
                        <el-option label="淘系" value=1></el-option>
                        <el-option label="拼多多" value=2></el-option>
                        <el-option label="阿里巴巴" value=4></el-option>
                        <el-option label="抖音" value=6></el-option>
                        <el-option label="京东" value=7></el-option>
                        <el-option label="快手" value=14></el-option>
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border:none">
                    <el-select v-model="Filter.groupType" placeholder="分类" filterable clearable>
                        <el-option label="售前" value=0></el-option>
                        <el-option label="售后" value=1></el-option>
                        <el-option label="一体" value=2></el-option>
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border:none">
                    <el-input v-model.trim="Filter.groupArea" placeholder="分区" maxlength="10" clearable />
                </el-button>

                <el-button style="padding: 0;margin: 0;border:none">
                    <el-input v-model.trim="Filter.groupname" placeholder="分组" maxlength="10" clearable />
                </el-button>

                <el-button style="padding: 0;margin: 0;border:none">
                    <el-input v-model.trim="Filter.groupManager" placeholder="组长" maxlength="10" clearable />
                </el-button>

                <el-button style="padding: 0;margin: 0;border:none">
                    <el-input v-model.trim="Filter.shopname" placeholder="店铺" maxlength="20" clearable />
                </el-button>

                <el-button style="padding: 0;margin: 0;border:none">
                    <el-input v-model.trim="Filter.sname" placeholder="姓名" maxlength="10" clearable />
                </el-button>

                <el-button style="padding: 0;margin: 0;border:none">
                    <el-input v-model.trim="Filter.snick" placeholder="昵称" maxlength="20" clearable />
                </el-button>
              <el-button style="padding: 0;margin: 0;border:none">
                <el-input
                  v-model="Filter.inactiveDays"
                  placeholder="请输入X天内数据"
                  type="text"
                  clearable
                  style="width: 150px; margin-left: 10px;"
                  oninput="value=value.replace(/[^\d]/g,''); if(value > 9999 || value < 0) value = '';"
                ></el-input>
              </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
            </el-button-group>
        </template>
        <!--列表-->
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='list' @select='selectchange' :isSelection='false' :tableCols='tableCols'
            :summaryarry='summaryarry' :showsummary='true' :loading="listLoading" />
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </my-container>
</template>
<script>

import { GetKeFuHuiZongDtlPageList } from '@/api/customerservice/group'
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
const tableCols = [
    { istrue: true, prop: 'platform', label: '平台', sortable: 'custom', formatter: (row) => row.platformName },
    { istrue: true, prop: 'shopCode', label: '店铺', sortable: 'custom', formatter: (row) => row.shopname },
    { istrue: true, prop: 'groupType', label: '分类', sortable: 'custom', formatter: (row) => row.groupTypeStr },
    { istrue: true, prop: 'groupArea', label: '所在分区', sortable: 'custom' },
    { istrue: true, prop: 'groupname', label: '分组', sortable: 'custom' },
    { istrue: true, prop: 'groupManager', label: '组长', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', sortable: 'custom' },
    { istrue: true, prop: 'snick', label: '昵称', sortable: 'custom' },
];

export default {
    name: "kefuhuizongdtl",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, vxetablebase },
    data() {
        return {
            that: this,
            Filter: {
                platformInt: null,
                groupType: null,
                groupArea: null,
                groupname: null,
                groupManager: null,
                shopname: null,
                sname: null,
                snick: null,
                inactiveDays: null,
            },
            tableCols: tableCols,
            list: [],
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "platform", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
        };
    },
    async mounted() {
        await this.onSearch();
    },
    methods: {
        loadData2(params) {
            this.Filter.platformInt = (params.platform ? params.platform.toString() : null);
            this.Filter.groupType = (params.groupType ? params.groupType.toString() : null);
            this.Filter.groupArea = params.groupArea;
            this.Filter.groupname = params.groupname;
            this.Filter.groupManager = params.groupManager;
            this.Filter.shopname = params.shopname;
            this.Filter.sname = params.sname;
            this.$nextTick(async () => {
                await this.onSearch();
            });
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getlist();
        },
        async getlist() {
            const para = { ...this.Filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
                platform: 1
            };
            console.log(para)
            this.listLoading = true;
            const res = await GetKeFuHuiZongDtlPageList(params);
            this.listLoading = false;
            console.log(res)

            this.total = res.data.total
            this.list = res.data.list;
            this.summaryarry = res.data.summary;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange(rows, row) {
            this.sels = rows;
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

.levelQueryInfoBox {
    display: flex;
    margin-bottom: 10px;
}

.publicCss {
    width: 220px;
    margin-right: 10px;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
