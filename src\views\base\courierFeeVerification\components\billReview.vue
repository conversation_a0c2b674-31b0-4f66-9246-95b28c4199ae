<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <!-- <dateRange :startDate.sync="ListInfo.startReceiveDate" :endDate.sync="ListInfo.endReceiveDate" class="publicCss"
          startPlaceholder="收寄时间" endPlaceholder="收寄时间" :clearable="false" style="width: 220px;" /> -->
        <el-date-picker v-model="timeRange1" type="daterange" unlink-panels range-separator="至" start-placeholder="收寄时间"
          end-placeholder="收寄时间" :picker-options="pickerOptions" style="width: 220px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime($event, 1)" :clearable="false">
        </el-date-picker>
        <el-date-picker v-model="timeRangesdr" type="daterange" unlink-panels range-separator="至"
          start-placeholder="导入开始日期" end-placeholder="导入结束日期" :picker-options="pickerOptions"
          style="width: 220px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime($event, 2)">
        </el-date-picker>
        <div style="margin-right: 5px;">
          <inputYunhan ref="productmailNumber" :inputt.sync="ListInfo.mailNumber" v-model="ListInfo.mailNumber"
            width="130px" placeholder="邮件号(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000"
            :valuedOpen="true" :maxlength="21000" @callback="mailNumberCallback" title="邮件号">
          </inputYunhan>
        </div>
        <el-select v-model="ListInfo.expressCompanyFullName" clearable filterable placeholder="快递公司名称" class="publicCss"
          :style="{ width: width }" @change="getExpressCompany($event)">
          <el-option v-for="(item, i) in expressCompany" :key="item.value + '-' + i" :label="item.label"
            :value="item.label" />
        </el-select>
        <el-select v-model="ListInfo.expressName" clearable filterable placeholder="快递公司" class="publicCss"
          :style="{ width: width }" @change="getprosimstatelist($event)">
          <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-select v-model="ListInfo.prosimstate" clearable filterable placeholder="快递站点" class="publicCss"
          :style="{ width: width }" @clear="handleClear">
          <el-option label="暂无站点" value="" />
          <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
        </el-select>
        <el-select v-model="ListInfo.warehouse" clearable filterable placeholder="发货仓库" class="publicCss"
          :style="{ width: width }">
          <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-input v-model.trim="ListInfo.batchNumber" placeholder="批次号" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
        <el-tooltip content="如果输入正数 则为 重量-聚水潭重量，如果输入负数 则为 聚水潭重量-重量 " placement="top">
          <el-input-number v-model="ListInfo.weightDiff" :min="-9999" :max="9999" :controls="false"
            style="width: 60px;margin-right: 5px;" placeholder="重量差"></el-input-number>
        </el-tooltip>
        <div style="display: flex;align-items: center;padding-top: 1px;">
          <el-checkbox v-model="ListInfo.noUseCatch" class="publicCss" style="width: 60px;">非缓存</el-checkbox>
        </div>
        <number-range :min.sync="ListInfo.weightMin" :max.sync="ListInfo.weightMax" min-label="重量(kg) - 最小值"
          max-label="重量(kg) - 最大值" class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>

      </div>
    </template>
    <vxetablebase :ispoint="false" :id="'billReview202410161532'" :tablekey="'billReview202410161532'" ref="table"
      :isNeedExpend="false" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' border :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0;" :loading="loading" height="100%">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 75px;">
        <el-date-picker style="width: 50%; float: left;" v-model="creatTime" type="date" format="yyyy-MM-dd"
          value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="dTitle" :visible.sync="dialogVisible2" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 50px;">
        <el-select v-model="expressCompanyId" placeholder="快递公司"
          style="width: 200px;margin-right: 10px;margin-bottom: 10px;" clearable @change="getprosimstatelist(1)">
          <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-select v-model="prosimstate" placeholder="请选择快递站点" clearable style="width: 130px">
          <el-option label="暂无站点" value="" />
          <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
        </el-select>
        <el-select v-model="warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 110px">
          <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>

        <el-date-picker style="width: 50%; float: left;" v-model="createTime" type="date" format="yyyy-MM-dd"
          value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible2 = false">关闭</el-button>
        <el-button type="primary" @click="onBatchConfirmation">提交确认</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="carouselPopupWindow" width="50%" :append-to-body="true" v-dialogDrag @close="pictureOff"
      @keyup.native.37="arrowClick('prev')">
      <div v-loading="echartsLoading">
        <div class="carousel-container">
          <div class="arrow prev">
            <i class="el-icon-arrow-left arrow-icon" @click="arrowClick('prev')"></i>
          </div>
          <div class="image-container">
            <el-image class="custom-image" :src="picture" :preview-src-list="[picture]">
            </el-image>
          </div>
          <div class="arrow next">
            <i class="el-icon-arrow-right arrow-icon" @click="arrowClick('next')"></i>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible1" width="80%" v-dialogDrag>
      <courierFeeBreakdown :info="info" v-if="dialogVisible1" @linkToDetail="dialogVisible1" />
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import courierFeeBreakdown from './courierFeeBreakdown.vue'
import { getExpressComanyAll, getExpressComanyStationName, getExpressBankInfoList, getDayExpressPage, exportDayExpressList, importDayExpress_recheck, deleteDayExpressClacInfoData_recheck, batchIntoExpressFreight, monthExpressCompanyFeeCalculate } from "@/api/express/express";
import dayjs from 'dayjs'
import { formatTime } from "@/utils";
import { warehouselist, formatWarehouseNew } from "@/utils/tools";
import dateRange from "@/components/date-range/index.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import numberRange from "@/components/number-range/index.vue";
const tableCols = [
  { sortable: 'custom', width: '140', align: 'center', prop: 'batchNumber', label: '批次', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'expressCompany', label: '快递公司', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'prosimstate', label: '快递站点', formatter: (row) => row.prosimstateName, type: 'custom' },
  { sortable: 'custom', width: '110', align: 'center', prop: 'warehouse', label: '发货仓库', formatter: (row) => formatWarehouseNew(row.warehouse) },
  { sortable: 'custom', width: '80', align: 'center', prop: 'isSelfCompany', label: '是否我司', formatter: (row) => row.isSelfCompany == 1 ? '我司' : row.isSelfCompany == 0 ? '非我司' : '' },
  { sortable: 'custom', width: '80', align: 'center', prop: 'inportDate', label: '导入日期', formatter: (row) => formatTime(row.inportDate, "YYYY-MM-DD") },
  { sortable: 'custom', width: '170', align: 'center', prop: 'originalOnlineOrderNo', label: '原始线上单号', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'receiveDate', label: '收寄日期', },
  { sortable: 'custom', width: '150', align: 'center', prop: 'mailNumber', label: '邮件号', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'province', label: '省', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'city', label: '市', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'weight', label: '重量', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'billingWeight', label: '计费重量', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'expressFee', label: '运费', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'jstWeight', label: '聚水潭重量', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'weightDiff', label: '重量差', tipmesg: '重量（快递公司重量，对应重量列）-聚水潭重量' },
  { sortable: 'custom', width: '75', align: 'center', prop: 'auditAmount', label: '核算金额', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'difference', label: '差额(元)', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'czFee', label: '操作费', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'packageFee', label: '包材费', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'addFee', label: '加收', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'SJTotalFee', label: '运费合计', formatter: (row) => row.sjTotalFee ? row.sjTotalFee : '', },
  {
    istrue: true, prop: 'inpout_path', label: '快递图片', width: '56', type: 'carouselimages', align: 'center', formatter: (row) => {
      if (row.rltisnormal === 0) {
        return row.output_normal_path
      } else if (row.rltisnormal === 1) {
        return row.output_abnormal_path
      } else if (row.rltisnormal === 2) {
        return row.output_normal_path
      } else {
        return ''
      }
    }, handle: (that, row, column, cell) => that.canclick(row, column, cell)
  },
]
export default {
  name: "billReview",
  components: {
    MyContainer, vxetablebase, inputYunhan, dateRange, numberRange, courierFeeBreakdown
  },
  props: {
    info: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      topfilter: {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
      },
      dialogVisible: false,
      yearMonthDay: null,
      fileList: [],
      uploadLoading: false,
      fileparm: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'inportDate',
        isAsc: false,
        startTime: dayjs().subtract('1', 'day').format('YYYY-MM-DD'),
        endTime: dayjs().subtract('1', 'day').format('YYYY-MM-DD'),
        recheckExpress: 2,//是否复核快递公司,1为源数据,2为复核数据
        expressName: null,
        prosimstate: null,
        noUseCatch: false,
        startReceiveDate: null,
        endReceiveDate: this.info.endReceiveDate ? this.info.endReceiveDate : null,
        expressCompanyId: null,
        prosimstateId: null,
        warehouse: null,
        expressCompanyFullName: null,
        weightMin: this.info.weightMin ? this.info.weightMin : null,
        weightMax: this.info.weightMax ? this.info.weightMax : null,
        goodsCode: this.info.goodsCode ? this.info.goodsCode : null,
      },
      dTitle: '同步进快递',
      clacType: 1,
      timeRanges: [],
      timeRangesdr: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      isExport: false,
      expresscompanylist: [],
      creatTime: null,
      formatWarehouseNew,
      warehouselist: warehouselist,
      warehouse: null,
      dialogVisible2: false,
      prosimstate: null,
      prosimstatelist: [],
      expressCompanyId: null,
      createTime: null,
      closePopup: false,//关闭弹窗
      carouselPopupWindow: false,//图片弹窗
      picture: '',//图片
      rectifyconsumable: '',//纠正耗材
      pictureconsumable: '',//图片耗材
      echartsLoading: false,//图片加载
      pictureexpressno: '',//图片快递单号
      consumableData: [],//耗材类别
      correctiveConsumables: false,//纠正耗材
      prosimstatelist: [],
      expresscompanylist: [],
      expressCompany: [],
      expressData: [],
      timeRange1: [dayjs().subtract('1', 'day').format('YYYY-MM-DD'), dayjs().subtract('1', 'day').format('YYYY-MM-DD')],
      dialogVisible: false,
      dialogVisible1: false,
      info: {},
    }
  },
  created() {
    if (this.info.expressCompanyId) {
      this.getprosimstatelist(this.info.expressCompanyId)
    }
  },
  async mounted() {
    // if (this.timeRangesdr && this.timeRangesdr.length == 0) {
    //   //默认给当前月第一天至今天
    //   this.ListInfo.startCreateTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
    //   this.ListInfo.endCreateTime = dayjs().format('YYYY-MM-DD')
    //   this.timeRangesdr = [this.ListInfo.startCreateTime, this.ListInfo.endCreateTime]
    // }
    // await this.getList()
    await this.init()
    await this.init1()
  },
  methods: {
    getData({ params, changeTabInfo }) {
      console.log('params', params);
      this.ListInfo.startTime = (params.startReceiveDate != null && params.startReceiveDate != undefined) ? params.startReceiveDate : null
      this.ListInfo.endTime = (params.endReceiveDate != null && params.startReceiveDate != undefined) ? params.endReceiveDate : null
      this.ListInfo.weightMin = (params.weightMin != null && params.startReceiveDate != undefined) ? params.weightMin : undefined
      this.ListInfo.weightMax = (params.weightMax != null && params.startReceiveDate != undefined) ? params.weightMax : undefined
      this.ListInfo.warehouse = (params.warehouseId != null && params.startReceiveDate != undefined) ? params.warehouseId : null
      this.ListInfo.expressName = (params.expressCompanyId != null && params.startReceiveDate != undefined) ? String(params.expressCompanyId) : null
      this.ListInfo.goodsCode = (params.goodsCode != null && params.startReceiveDate != undefined) ? params.goodsCode : null
      this.timeRange1 = [this.ListInfo.startTime, this.ListInfo.endTime]
      this.$forceUpdate()
      this.getList('search')
      // if (changeTabInfo && changeTabInfo.type == 0) {
      //   this.openDetails(changeTabInfo.row, changeTabInfo.range, changeTabInfo.weightType, changeTabInfo)
      // } else if (changeTabInfo && changeTabInfo.type == 1) {
      //   this.openDetails1(changeTabInfo.row, changeTabInfo.range, changeTabInfo.weightType, changeTabInfo)
      // }
    },
    extractNumbers(str) {
      const matches = str.match(/\d+(\.\d+)?/g); // 匹配整数或小数
      return matches ? matches.map(Number) : []; // 转换为数字数组
    },
    openDetails(row, range, weightType, changeTabInfo) {
      this.info = {
        row,
        range,
        type: 0,
        list: [
          { label: '总快递费', prop: 'totalFee', },
          { label: '总订单量', prop: 'totalCount', },
          { label: '仓库', prop: 'warehouse', },
        ],
        weightType,
        startReceiveDate: changeTabInfo.startReceiveDate,
        endReceiveDate: changeTabInfo.endReceiveDate,
        isDetails: true,
      }
      if (weightType != 11) {
        this.info.warehouseId = [row.warehouse]
        let weightMin;//起始值
        let weightMax;//结束值
        [weightMin, weightMax] = this.extractNumbers(this.info.range)
        if (weightMax == 500) {
          weightMax = 0.5
        } else if (weightMin == 500) {
          weightMin = 0.5
        }
        this.info.weightMin = weightMin
        this.info.weightMax = weightMax
        this.dialogVisible1 = true
      } else {
        const params = { ...this.info, weightMin: 10, weightMax: null, warehouseId: row.warehouse, }
        this.$emit('linkToDetail', { params, changeTabInfo: this.changeTabInfo })
      }
    },
    openDetails1(row, range, weightType, changeTabInfo) {
      this.info = {
        row,
        range,
        type: 1,
        list: [
          { label: '总快递费', prop: 'totalFee', },
          { label: '总订单量', prop: 'totalCount', },
          { label: '快递公司', prop: 'expressCompany', },
        ],
        weightType,
        startReceiveDate: changeTabInfo.startReceiveDate,
        endReceiveDate: changeTabInfo.endReceiveDate,
        isDetails: true,
      }
      if (weightType != 11) {
        this.info.expressCompanyId = [String(row.expressCompanyId)]
        let weightMin;//起始值
        let weightMax;//结束值
        [weightMin, weightMax] = this.extractNumbers(this.info.range)
        if (weightMax == 500) {
          weightMax = 0.5
        } else if (weightMin == 500) {
          weightMin = 0.5
        }
        this.info.weightMin = weightMin
        this.info.weightMax = weightMax
        this.dialogVisible1 = true
      } else {
        const params = { ...this.info, weightMin: 10, weightMax: null, expressCompanyId: String(row.expressCompanyId), }
        this.$emit('linkToDetail', { params, changeTabInfo: this.changeTabInfo })
      }
    },
    async init1() {
      const res = await getExpressComanyAll({});
      if (!res?.success) return
      this.expresscompanylist = res.data;
      const { data, success } = await getExpressBankInfoList({});
      if (!success) return
      this.expressData = data.list;
      this.expressCompany = Array.from(new Set(this.expressData.map(item => ({ label: item.expressCompanyFullName, value: item.expressCompanyId }))));
    },
    async getExpressCompany(e) {
      var itemInfo = {};
      this.expressData.forEach(item => {
        if (item.expressCompanyFullName == e) {
          itemInfo = item;
        }
      });
      if (itemInfo && typeof itemInfo === 'object' && Object.keys(itemInfo).length > 0) {
        let a = (itemInfo.expressCompanyId).toString();
        this.ListInfo.expressCompanyId = a;
        await this.getprosimstatelist(a, 1);
        this.ListInfo.warehouseId = itemInfo.warehouseId;
        this.ListInfo.prosimstateId = (itemInfo.prosimstateId).toString();
      } else {
        this.ListInfo.expressCompanyId = null;
        this.ListInfo.warehouseId = null;
        this.ListInfo.prosimstateId = null;
        this.prosimstatelist = [];
      }
    },
    async getprosimstatelist(e, val) {
      var res = await getExpressComanyStationName({ id: e });
      if (res?.code) {
        this.prosimstatelist = res.data;
      }
    },
    mailNumberCallback(val) {
      this.ListInfo.mailNumber = val
    },
    async onDeleteOperation(row) {
      this.$confirm('是否删除该批次数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await deleteDayExpressClacInfoData_recheck({ batchNumber: row.batchNumber, inportDate: row.inportDate })
        if (success) {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      });
    },
    // async getprosimstatelist(val) {

    //   var id;
    //   if (val == 1) {
    //     id = this.expressCompanyId
    //     this.prosimstate = null
    //   }
    //   else if (val == 2) {
    //     id = this.ListInfo.expressName
    //     this.ListInfo.prosimstate = null
    //   }

    //   var res = await getExpressComanyStationName({ id: id });
    //   if (res?.code) {
    //     this.prosimstatelist = res.data
    //   }
    // },
    pictureOff() {
      if (this.closePopup) {
        this.getList()
        return
      }
    },
    //图片点击
    async arrowClick(val) {
      const index = this.tableData.findIndex(item => item.expressno === this.pictureexpressno);
      const isNext = val === 'next';
      const isEnd = isNext ? index === this.tableData.length - 1 : index === 0;

      if (isEnd) {
        this.$message.error(isNext ? '已经是当前页最后一张了,正在加载下一页' : '已经是当前页第一张了,正在加载上一页');
        this.echartsLoading = true;
        if (this.tableData.length >= this.ListInfo.pageSize) {
          await this.Pagechange(this.ListInfo.currentPage + (isNext ? 1 : -1));
        }
        const targetIndex = isNext ? 0 : this.tableData.length - 1;
        this.updatePictureAndExpressno(targetIndex);
        this.echartsLoading = false;
      } else {
        const newIndex = isNext ? index + 1 : index - 1;
        this.updatePictureAndExpressno(newIndex);
      }
    },
    updatePictureAndExpressno(index) {
      this.picture = this.tableData[index].output_normal_path;
      this.pictureexpressno = this.tableData[index].expressno;
      this.pictureconsumable = this.tableData[index].package;
    },
    canclick(row, column, cell) {
      if (!row) return
      this.picture = row.output_normal_path
      this.pictureexpressno = row.expressno
      this.pictureconsumable = row.package
      this.carouselPopupWindow = true;
    },
    async onBatchConfirmation() {
      this.$confirm('是否确认该批次?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {

        const { success } =
          this.clacType == 1 ? await batchIntoExpressFreight({ inportDate: this.createTime, expressCompanyId: this.expressCompanyId, warehouse: this.warehouse, prosimstate: this.prosimstate })
            : await monthExpressCompanyFeeCalculate({ inportDate: this.createTime, expressCompanyId: this.expressCompanyId, warehouse: this.warehouse, prosimstate: this.prosimstate })
        if (success) {
          this.$message.success('确认提交成功，请到任务进度中查看同步进度')
        } else {
          this.$message.error('确认失败')
        }
      }).catch(() => {
        this.$message.info('已取消确认')
      });
    },

    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("creatTime", this.creatTime);
      // form.append("yearMonthDay", this.yearMonthDay);
      var res = await importDayExpress_recheck(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      // if (!this.yearMonthDay) {
      //   this.$message({ message: "请选择日期", type: "warning" });
      //   return false;
      // }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.creatTime = null
      this.dialogVisible = true;
    },
    startClac(lx) {

      if (lx == 1) {
        this.dTitle = "同步进快递费";
      } else {
        this.dTitle = "月账单计算";
      }
      this.clacType = lx;

      this.expressCompanyId = null
      this.prosimstate = null
      this.warehouse = null

      this.createTime = null
      this.expressCompanyId = this.topfilter.expressCompanyId ? (this.topfilter.expressCompanyId).toString() : null
      setTimeout(async () => {
        this.getprosimstatelist(1)
        this.prosimstate = this.topfilter.prosimstateId
        this.warehouse = this.topfilter.warehouseId
      }, 100);
      this.dialogVisible2 = true;
    },
    async exportProps() {
      this.isExport = true
      const params = { ...this.ListInfo, expressName: this.topfilter.expressCompanyId, prosimstate: this.topfilter.prosimstateId, warehouse: this.topfilter.warehouseId }
      await exportDayExpressList(params).then(({ data }) => {
        if (data) {
          const aLink = document.createElement("a");
          let blob = new Blob([data], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', '快递拦截明细' + new Date().toLocaleString() + '.xlsx')
          aLink.click()
          this.isExport = false
        }
      }).catch(() => {
        this.isExport = false
      })
    },
    async init() {
      const res = await getExpressComanyAll({});
      if (!res?.success) return
      this.expresscompanylist = res.data;
    },
    async changeTime(e, val) {
      if (val == 1) {
        this.ListInfo.startTime = e ? e[0] : null
        this.ListInfo.endTime = e ? e[1] : null
      } else if (val == 2) {
        this.ListInfo.startCreateTime = e ? e[0] : null
        this.ListInfo.endCreateTime = e ? e[1] : null
      }
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      // if (this.timeRanges && this.timeRanges.length == 0) {
      //   //默认给近7天时间
      //   this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
      //   this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
      //   this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      // }
      this.loading = true
      // const params = { ...this.ListInfo,  }
      const { data, success } = await getDayExpressPage(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        let summary = data.summary || {}
        summary.SJTotalFee_sum = String(summary.sjTotalFee_sum)
        // this.summaryarry = summary

        const resultsum = {};
        Object.entries(summary).forEach(([key, value]) => {
          resultsum[key] = formatNumber(value);
        });

        function formatNumber(number) {
          const options = {
            useGrouping: true,
          };
          return new Intl.NumberFormat('zh-CN', options).format(number);
        }

        this.summaryarry = resultsum



        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },

    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 130px;
    margin-right: 5px;
  }
}

::v-deep .custom-image img {
  max-width: 100% !important;
  max-height: 610px !important;
  object-fit: contain;
}

.centered-content {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

.carousel-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 574px;
}

.image-container {
  flex-grow: 1;
  display: flex;
  justify-content: center;
}

.arrow-icon {
  font-size: 30px !important;
  cursor: pointer;
}

::v-deep .c--tooltip {
  display: flex !important;
}

::v-deep .el-select__tags-text {
  max-width: 60px;
}
</style>
