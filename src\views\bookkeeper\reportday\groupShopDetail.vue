<template>
    <my-container v-loading="pageLoading">
      <template>
        <ces-table ref="table" :that='that'  tablekey="tablekey20230512001"  :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry='summaryarry' :showsummary='true'
          :tableData='list'  :tableCols='tableCols' :isSelection="false" @select="selectchange" :isSelectColumn='false'
          :tableHandles='tableHandles' @cellclick="cellclick"
          :loading="listLoading">
        </ces-table>
    </template>
    <template #footer>
          <my-pagination
            ref="pager"
            :total="total"
            :checked-count="sels.length"
            @get-page="getlist"
          />
        </template>
    </my-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import {formatLinkProCode, formatSendWarehouse,formatPlatform} from "@/utils/tools";
import { getPerformanceStaticticsByGroupDialog } from  '@/api/bookkeeper/reportday'
import { rulePlatform, ruleIllegalType} from "@/utils/formruletools";
import * as echarts from "echarts";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

const tableCols =[

        { istrue: true, summaryEvent:false, prop: 'groupName', label: '所在组', width: '70', formatter: (row) => row.groupName },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'shopCode', label: '店铺', width: '170', formatter: (row) => row.shopName },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'payAmont', label: '付款金额', width: '100', formatter: (row) => !row.payAmont ? " " : row.payAmont.toFixed(2) },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'saleAmont', label: '销售金额', width: '120', formatter: (row) => !row.saleAmont ? " " : row.saleAmont.toFixed(2) },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'refundAmontBefore', label: '退款', width: '120', formatter: (row) => !row.refundAmontBefore ? "0" : row.refundAmontBefore.toFixed(2) },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'refundAmontBeforeRate', label: '退款率', width: '120', formatter: (row) => !row.refundAmontBeforeRate ? "0%" : row.refundAmontBeforeRate.toFixed(2).toString()+"%" },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'saleCost', label: '成本', width: '120', formatter: (row) => !row.saleCost ? " " : row.saleCost.toFixed(2) },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1.toFixed(2) },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1.toFixed(2) },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'profit1Rate', label: '毛一利润率(发生)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toFixed(2).toString() + "%" },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '90', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toFixed(2).toString() + "%" },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'alladv', label: '广告费', width: '100', formatter: (row) => row.alladv == 0 ? " " : row.alladv?.toFixed(2) },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'alladv_rate', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate)?.toFixed(2).toString() + "%" },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'profit3', label: '毛三利润(发生)', width: '120', type: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3?.toFixed(2) },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'profit31', label: '毛三利润(付款)', width: '120', type: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3?.toFixed(2) },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'profit3_rate', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate)?.toFixed(2).toString() + "%" },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '120', formatter: (row) => (row.profit3_rate)?.toFixed(2).toString() + "%" },
        { istrue: true, sortable: 'custom', summaryEvent: false, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33.toFixed(2) },
        { istrue: true, sortable: 'custom', summaryEvent: false, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33 ? " " : row.profit33.toFixed(2) },
        { istrue: true, sortable: 'custom', summaryEvent: false, prop: 'profit6', label: '毛六利润', width: '90', type: 'custom', formatter: (row) => row.profit6 == 0 ? " " : row.profit6?.toFixed(2), permission: "profit6SixtyCents" },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'profit6Rate', label: '毛六利润率', width: '90', formatter: (row) => row.profit6Rate ? (row.profit6Rate)?.toFixed(2).toString() + "%" : '', permission: "profit6SixtyCents" },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'profit4', label: '净利润(发生)', width: '100', formatter: (row) => row.profit4 == 0 ? " " : row.profit4?.toFixed(2) },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'profit41', label: '净利润(付款)', width: '100', formatter: (row) => row.profit4 == 0 ? " " : row.profit4?.toFixed(2) },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'profit4_rate', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate)?.toFixed(2).toString() + "%" },
        { istrue: true,sortable:'custom', summaryEvent:false, prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate)?.toFixed(2).toString() + "%" },


]

const tableHandles=[

      ];

export default {
    name: 'YunhanAdminOrderillegaldetail',
    components: {cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, },
    props:{
        filter:{ }
    },
    data() {
        return {
            that:this,
            list: [],
            platformList: [],
            illegalTypeList : [],
            summaryarry:{},
            pager:{OrderBy:"PayAmont",IsAsc:false},
            filterImport:{

            occurrenceTime:formatTime(dayjs().subtract(1,"day"), "YYYY-MM-DD")
            },
            pickerOptions:{
                disabledDate(time){
                return time.getTime()>Date.now();
                }
            },
            tableCols:tableCols,
            tableHandles:tableHandles,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            dialogVisible: false,
            uploadLoading: false,
            uploadLoading: false,
            timerangeOnTime:[],
        };
    },

    async mounted() {
        //await this.setPlatform()
        //await this.onSearch()

    },

    methods: {
    onShowupMethod(){
      let that = this;
      that.$nextTick(() => {
      const takePlace = ['profit1', 'profit1Rate', 'profit3', 'profit3_rate', 'profit33', 'profit33Rate', 'exitCost', 'profit4', 'profit4_rate'];
      const payment = ['profit11', 'profit1Rate1', 'profit31', 'profit3_rate1', 'profit331', 'profit33Rate1', 'exitCost1', 'profit41', 'profit4_rate1'];
          if (that.$refs.table) {
            if (that.filter.refundType === 1) {
              that.$refs.table.changecolumn_setTrue(takePlace);
              that.$refs.table.changecolumn(payment);
            } else {
              that.$refs.table.changecolumn(takePlace);
              that.$refs.table.changecolumn_setTrue(payment);
            }
          }
      });
    },
    //查询第一页
    async onSearch() {
      if (!this.filter.timerange) {this.$message({message: "请选择日期",type: "warning",});return;}
      this.$refs.pager.setPage(1)
      this.onShowupMethod();
      await this.getlist();
    },
    //获取查询条件
    getCondition(){
      if (this.filter.timerange&&this.filter.timerange.length>1) {
        this.filter.StartTime = this.filter.timerange[0];
        this.filter.EndTime = this.filter.timerange[1];
      }
      else {
        this.$message({message:"请先选择日期",type:"warning"});
        return false;
      }
      if (this.filter.timerangeOnTime) {
        this.filter.startTime3 = this.filter.timerangeOnTime[0];
        this.filter.endTime3 = this.filter.timerangeOnTime[1];
      }
      //this.filter.platform=2
      var pager = this.$refs.pager.getPager();
      var page  = this.pager;
      if(pager.OrderBy == null){
        pager.OrderBy ="profit3";
        pager.IsAsc = false;
      }
      const params = {
        ...pager,
        ...page,
        ... this.filter
      }

      return params;
    },
    //分页查询
     async getlist() {
        var params=this.getCondition();
        if(params===false){
                return;
        }
        this.listLoading = true
        const res = await getPerformanceStaticticsByGroupDialog(params)
        this.listLoading = false
        if (!res?.success) {
            return
        }
        this.total = res.data.total;
        const data = res.data.list;
        this.summaryarry=res.data.summary;
        if(this.summaryarry)
        this.summaryarry.profit1Rate_sum=(this.summaryarry.profit1Rate_sum*100).toFixed(2)+'%';
        this.summaryarry.alladv_rate_sum=(this.summaryarry.alladv_rate_sum*100).toFixed(2)+'%';
        this.summaryarry.profit3_rate_sum=(this.summaryarry.profit3_rate_sum*100).toFixed(2)+'%';
        this.summaryarry.profit4_rate_sum=(this.summaryarry.profit4_rate_sum*100).toFixed(2)+'%';
        this.summaryarry.refundAmontBeforeRate_sum=(this.summaryarry.refundAmontBeforeRate_sum * 100).toFixed(2) + '%';
        this.summaryarry.profit6Rate_sum=(this.summaryarry.profit6Rate_sum).toFixed(2) + '%';
        data.forEach(d => {
            d._loading = false
        })
        this.list = data
       },
    //排序查询
    async sortchange(column){
    if(!column.order)
        this.pager={};
    else{
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
    }
    await this.onSearch();
    },
    selectchange:function(rows,row) {
      this.selids=[];console.log(rows)
      rows.forEach(f=>{
          this.selids.push(f.id);
      })
    },
    cellclick(row, column, cell, event){

    },
    },
};
</script>

<style lang="scss" scoped>
</style>
