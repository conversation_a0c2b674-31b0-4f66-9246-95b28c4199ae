<template v-loading="pageLoading">
    <my-container v-loading="pageLoading">
        <template #header>
            <div style="height: 40px; margin-top: 10px">
              <!-- <span>
                <el-input v-model="filter.vendorID" placeholder="供销商ID" style="width: 200px;" clearable></el-input>
              </span> -->
              <span>
                <el-input v-model="filter.vendorName" placeholder="供销商名称" style="width: 200px;" clearable></el-input>
              </span>
              <span>
                <el-input v-model="filter.phoneNumber" placeholder="手机号" style="width: 200px;" clearable></el-input>
              </span>
                
              <span style="margin-left:5px;">
                <el-button type="primary" @click="onSearch" style="width: 70px;">搜索</el-button>
              </span>
              <span style="margin-left:5px;">
                <el-button type="primary" @click="onAdd" style="width: 70px;">新增</el-button>
              </span>
              <span style="margin-left:5px;">
                <el-button type="primary" @click="onImport" style="width: 60px;">导入</el-button>
              </span>
              <span style="margin-left:5px;">
                <el-button type="primary" @click="onExport" style="width: 60px;">导出</el-button>
              </span>
            </div>
        </template>

        <vxetablebase ref="table" :tableData="tableData" :tableCols="tableCols" :is-index="true" :that="that" :id="'SupplierProductCodeManage202409121029'" 
        :showsummary="false" style="width: 100%; height: 100%; margin: 0" @sortchange='sortchange'
         :loading="listLoading" class="already">
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" :height="'10px'"/>
        </template>

        <el-dialog title="新增供应商商品编码" :visible.sync="addVisible" width="30%" @closeDialog="closeAddDialog" v-dialogDrag
          @close="addCancle">
          <el-form ref="fromAdd" :model="fromAdd" label-width="100px">
            <!-- <el-form-item label="供销商ID" prop="vendorID">
              <el-input v-model="fromAdd.vendorID" placeholder="供销商ID" style="width: 200px;" clearable></el-input>
            </el-form-item> -->
            <el-form-item label="供销商名称" prop="vendorName">
              <el-input v-model="fromAdd.vendorName" placeholder="供销商名称" style="width: 200px;" clearable></el-input>
            </el-form-item>
            <el-form-item label="手机号" prop="phoneNumber">
              <el-input v-model="fromAdd.phoneNumber" placeholder="手机号" style="width: 200px;" clearable></el-input>
            </el-form-item>
            <el-form-item label="供应商ID" prop="supplierID">
              <el-input v-model="fromAdd.supplierID" placeholder="供应商ID" style="width: 200px;" clearable ></el-input>
            </el-form-item>
            <el-form-item label="供应商名称" prop="supplierName">
              <el-input v-model="fromAdd.supplierName" placeholder="供应商名称" style="width: 200px;" clearable></el-input>
            </el-form-item>
          </el-form>

          <div style="text-align: center;">      
            <el-button type="primary" @click="addCancle">取消</el-button>
            <el-button type="primary" @click="addSubmit">保存</el-button>
          </div>
        </el-dialog>

        <el-dialog title="编辑供应商商品编码" :visible.sync="updateVisible" width="30%" @closeDialog="closeUpdateDialog" v-dialogDrag
          @close="updateCancle">
          <el-form ref="fromUpdate" :model="fromUpdate" label-width="100px">
            <el-form-item label="供销商ID" prop="vendorID">
              <el-input v-model="fromUpdate.vendorID" placeholder="供销商ID" style="width: 200px;" clearable :disabled="true"></el-input>
            </el-form-item>
            <el-form-item label="供销商名称" prop="vendorName">
              <el-input v-model="fromUpdate.vendorName" placeholder="供销商名称" style="width: 200px;" clearable></el-input>
            </el-form-item>
            <el-form-item label="手机号" prop="phoneNumber">
              <el-input v-model="fromUpdate.phoneNumber" placeholder="手机号" style="width: 200px;" clearable></el-input>
            </el-form-item>
            <el-form-item label="供应商ID" prop="supplierID">
              <el-input v-model="fromUpdate.supplierID" placeholder="供应商ID" style="width: 200px;" clearable ></el-input>
            </el-form-item>
            <el-form-item label="供应商名称" prop="supplierName">
              <el-input v-model="fromUpdate.supplierName" placeholder="供应商名称" style="width: 200px;" clearable></el-input>
            </el-form-item>
          </el-form>

          <div style="text-align: center;">      
            <el-button type="primary" @click="updateCancle">取消</el-button>
            <el-button type="primary" @click="updateSubmit">保存</el-button>
          </div>
        </el-dialog>
        
        <el-dialog title="导入供销商-供应商关系" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
            <el-row>
                <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                    <el-upload ref="upload" :auto-upload="false" :multiple="false" action
                        accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                        <template #trigger>
                            <el-button size="small" type="primary">选取文件</el-button>
                        </template>
                        <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                            @click="submitupload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                    </el-upload>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getSupplierProductCodePageList, addSupplierProductCodePageList, updateSupplierProductCode, deleteSupplierProductCode, 
  importSupplierProductCode, exportSupplierProductCode } from '@/api/inventory/SupplierProductCodeManage'

const tableCols = [
  { istrue: true, sortable: 'custom', width: '120', align: 'center', prop: 'vendorID', label: '供销商ID' },
  { istrue: true, sortable: 'custom', width: '180', align: 'center', prop: 'vendorName', label: '供销商名称' },
  { istrue: true, sortable: 'custom', width: '120', align: 'center', prop: 'phoneNumber', label: '手机号' },
  { istrue: true, sortable: 'custom', width: '120', align: 'center', prop: 'supplierID', label: '供应商ID' },
  { istrue: true, sortable: 'custom', width: '180', align: 'center', prop: 'supplierName', label: '供应商名称' },
  {
    istrue: true, type: 'button', width: '120', label: '操作', btnList: [
      { label: "编辑", handle: (that, row) => that.onUpdate(row) },
      { label: "删除", handle: (that, row) => that.onDelete(row) }
    ]
  },
]

export default {
  name: "SupplierProductCodeManage",
  components: { MyContainer, vxetablebase },
  data() {
    return {
      pageLoading: false,
      dialogVisible: false,//上传对话框
      uploadLoading: false,//上传过程
      addVisible: false,//新增对话框
      updateVisible: false,//编辑对话框
      that: this,
      listLoading: false,//查询过程
      total: 0,
      tableData: [],//表格数据
      tableCols: tableCols,
      pager: { orderBy: 'vendorID', isAsc: true },
      // 过滤条件
      filter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        vendorID: null,//供销商ID
        vendorName: null,//供销商名称
        phoneNumber: null,//手机号
      },
      // 新增
      fromAdd: {
        vendorID: null,//供销商ID
        vendorName: null,//供销商名称
        phoneNumber: null,//手机号
        supplierID: null,//供应商ID
        supplierName: null,//供应商名称
      },
      // 编辑
      fromUpdate: {
        vendorID: null,//供销商ID
        vendorName: null,//供销商名称
        phoneNumber: null,//手机号
        supplierID: null,//供应商ID
        supplierName: null,//供应商名称
      },
      selectList: [],//复选框选中数据
    };
  },
  mounted() {
    this.onSearch();
  },
  methods: {
    // 排序查询
    async sortchange(column) {
      if (column.order) {
        this.filter.orderBy = column.prop;
        this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false;
        this.getList();
      }
    },
    // 复选框数据
    selectchange:function(rows,row) {
        this.selectList = [];
        rows.forEach(f => {
            this.selectList.push(f);
        })
    },
    // 每页数量改变
    Sizechange(val) {
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.getList()
    },
    // 当前页改变
    Pagechange(val) {
      this.filter.currentPage = val;
      this.getList()
    },
    // 查询列表
    async onSearch() {
      // 点击查询时才将页数重置为1
      this.filter.currentPage = 1;
      this.$refs.pager.setPage(1)
      this.getList();
    },
    async getList() {
      this.listLoading = true;
      const { data, success } = await getSupplierProductCodePageList(this.filter);
      if (success) {
        this.tableData = data.list;
        this.total = data.total;
        this.listLoading = false;
      } else {
        this.$message.error('获取列表失败')
      }
    },
    // 显示新增对话框
    onAdd() {
        this.addVisible = true;
    },
    // 取消新增
    addCancle() {
      this.addVisible = false;
      this.fromAdd = {
        vendorID: null,//供销商ID
        vendorName: null,//供销商名称
        phoneNumber: null,//手机号
      };
    },
    // 关闭新增对话框
    closeAddDialog() {
      this.addVisible = false;
      this.fromAdd = {
        vendorID: null,//供销商ID
        vendorName: null,//供销商名称
        phoneNumber: null,//手机号
      };
    },
    // 提交新增
    async addSubmit() {
      if (this.fromAdd.vendorName === 'undefined' || this.fromAdd.vendorName == null || this.fromAdd.vendorName === '')
      {
        this.$message.error('供销商名称不能为空');
        return;
      }
      else if (this.fromAdd.phoneNumber === 'undefined' || this.fromAdd.phoneNumber == null || this.fromAdd.phoneNumber === '')
      {
        this.$message.error('手机号不能为空');
        return;
      }
      console.log("继续验证手机号是否符合规格：");
      const mobileRegex = /^1[3-9]\d{9}$/; // 定义手机号的正则表达式（以中国手机号为例）
      const landlineRegex = /^(0\d{2,3})-\d{7,8}$/; // 定义电话座机的正则表达式
      this.fromAdd.phoneNumber = this.fromAdd.phoneNumber.replace(/\D/g, ''); // 去掉空格和非数字字符
      if (!mobileRegex.test(this.fromAdd.phoneNumber) && !landlineRegex.test(this.fromAdd.phoneNumber)) {
        this.$message.error("手机号或电话座机无效！");
        return;
      }
      this.$refs.fromAdd.validate(async valid => {
        if (valid) {
          const res = await addSupplierProductCodePageList(this.fromAdd);
          if (res.success) {
            this.$message({ message: '添加成功', type: "success" });
            this.addVisible = false;
            this.getList();
          } else {
            this.$message.error('添加失败')
          }
        }
      })
    },
    // 显示编辑对话框
    async onUpdate(row) {
      this.updateVisible = true;
      this.fromUpdate = {
        vendorID: row.vendorID,//供销商ID
        vendorName: row.vendorName,//供销商名称
        phoneNumber: row.phoneNumber,//手机号
      };
    },
    // 取消编辑操作
    updateCancle() {
      this.updateVisible = false;
    },
    // 提交编辑操作
    async updateSubmit() {
      this.$refs.fromUpdate.validate(async valid => {
        if (valid) {
          const res = await updateSupplierProductCode(this.fromUpdate);
          if (res.success) {
            this.$message({ message: '编辑成功', type: "success" });
            this.updateVisible = false;
            this.getList();
          } else {
            this.$message.error('编辑失败')
          }
        }
      })
    },
    // 关闭编辑对话框
    closeUpdateDialog() {
      this.updateVisible = false;
    },
    // 删除
    onDelete(row) {
      console.log("行数据：");
      console.log(row);
      this.fromUpdate = {
        vendorID: row.vendorID,//供销商ID
        vendorName: row.vendorName,//供销商名称
        phoneNumber: row.phoneNumber,//手机号
      };
      console.log("传给后端的数据：");
      console.log(this.fromUpdate);
      this.$confirm('确定删除该记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await deleteSupplierProductCode(this.fromUpdate);
        if (res.success) {
          this.$message({ message: '删除成功', type: "success" });
          this.getList();
        } else {
            this.$message.error('删除失败')
        }
        
        this.fromUpdate = {
          vendorID: null,//供销商ID
          vendorName: null,//供销商名称
          phoneNumber: null,//手机号
        };
      }).catch(() => {
        this.$message({ type: 'info', message: '已取消删除' });
      });
    },
    // 打开上传弹窗
    onImport() {
      this.dialogVisible = true;
      this.uploadLoading = false;
      this.$nextTick(() => {
          if (this.$refs.upload) {
              this.$refs.upload.clearFiles();
          }
      });
      this.fileList.splice(0, 1);
    },
    // 上传文件
    async uploadFile(item) {
      this.uploadLoading = true;
      const form = new FormData();
      form.append("upfile", item.file);
      const res = importSupplierProductCode(form);
      if (res?.success) {
        this.$message({message: '上传成功,正在导入中...', type: "success"});
        // this.uploadLoading = false;
        // this.dialogVisible = false;
      }
      // this.getList();
    },
    // 更改上传文件
    async uploadChange(file, fileList) {
      if (fileList.length == 2) {
          fileList.splice(1, 1);
          this.$message({ message: "只允许单文件导入", type: "warning" });
          return false;
      }
      this.fileList.push(file);
    },
    // 移除上传文件
    uploadRemove() {
        this.fileList.splice(0, 1);
    },
    // 提交上传文件
    async submitupload() {
      if (this.fileList.length == 0) {
          this.$message.warning('您没有选择任何文件！');
          return;
      }
      this.$refs.upload.submit();
      this.$refs.upload.clearFiles();
      this.fileList.splice(0, 1);
      this.dialogVisible = false;
      this.getList();
    },
    // 导出
    async onExport() {
      this.listLoading = true;
      const res = await exportSupplierProductCode(this.filter);
      this.listLoading = false;
      if (!res?.data) return;
      const aLink = document.createElement('a');
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute('download', '供销商-供应商关系_' + new Date().toLocaleString() + '.xlsx');
      aLink.click();
    },
  }
}
</script>
<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

// 新增、编辑el-input行间距
.custom-form .el-form-item {
  margin-bottom: 120px;
  /* 自定义行间距 */
}
</style>