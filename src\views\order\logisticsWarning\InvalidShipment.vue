<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button style="padding: 0;margin:0;" clearable>
                <el-radio-group v-model="payTimeSel" @change="refundTimeSelChange">
                    <el-radio-button label="1">昨天至今日</el-radio-button>
                    <el-radio-button label="2">近7天</el-radio-button>
                    <el-radio-button label="3">近14天</el-radio-button>
                    <el-radio-button label="4">近30天</el-radio-button>
                </el-radio-group>
            </el-button>
            <el-button style="padding: 0;margin:0;">
                <el-date-picker style="width:220px;height: 30px;" v-model="filter.refundrange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="退款开始时间" end-placeholder="退款结束时间" @change="refundTimeSelChangePick" clearable></el-date-picker>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
                <el-date-picker style="width:220px;height: 30px;" v-model="filter.deltimerange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="发货开始时间" end-placeholder="发货结束时间" clearable></el-date-picker>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
                <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组" style="width: 90px">
                    <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
                <el-select style="width:90px" v-model="filter.platform" placeholder="选择平台" clearable @change="onchangeplatform">
                    <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" clearable />
                </el-select>
            </el-button>
        </template>
        <ces-table :ref="tablekey" :key="tablekey" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :loading="listLoading">
            <template slot='extentbtn' style="height:100px">
                <el-button-group>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button-group>
            </template>
        </ces-table>

        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getlist" />
        </template>
        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag>
            <orderLogPage ref="orderLogPage" :orderNoInner="sendOrderNoInner" style="z-index:10000;height:600px" />
        </el-dialog>
        <el-dialog title="快递物流信息" v-if="drawerVisible" :visible.sync="drawerVisible" width="70%" height="600px" v-dialogDrag>
            <orderLogisticsPage ref="orderLogisticsPage" :ticketNumber="ticketNumber" style="z-index:10000;height:600px" />
        </el-dialog>
    </my-container>
</template>

<script>
    import dayjs from "dayjs";
    import MyContainer from '@/components/my-container'
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import orderLogPage from "@/views/order/logisticsWarning/LogisticsEarlyWarLog.vue";
    import orderLogisticsPage from "@/views/order/logisticsWarning/orderLogisticsPage.vue";
    import { formatTime } from "@/utils";
    import { rulePlatform } from "@/utils/formruletools";
    import { getOrderInvalidShipmentDataAsync } from "@/api/order/logisticsEarlyWarPage";
    import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
    const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    export default {
        name: 'InvalidShipment',
        components: { cesTable, MyContainer, MyConfirmButton, orderLogPage, orderLogisticsPage },
        props: {
            tablekey: {
                type: String,
                default: ''
            },
        },
        data () {
            return {
                that: this,
                tableCols: [],
                filter: {
                    platform: null,
                    refundrange: [startTime, endTime],
                    deltimerange: [],
                    sendMsgTimerange: [],
                    refundStartdate: "",
                    refundEnddate: "",
                    delStartdate: "",
                    delEnddate: "",
                    groupId: null,
                },
                sendOrderNoInner: "",
                dialogHisVisible: false,
                ticketCompanyName: "",
                ticketNumber: "",
                drawerVisible: false,
                payTimeSel: "",
                list: [],
                summaryarry: {},
                pager: {},
                platformlist: [],
                grouplist: [],
                total: 0,
                listLoading: false,
                pageLoading: false,
                importLoading: false,
                fileList: [],
                oneCount: "",
                twoCount: "",
                threeCount: "",
                historyCount: "",
                overCount: "",
            }
        },
        async mounted () {
            this.initTable();
            await this.initDirectorGroupList();
            await this.setPlatform();
            await this.onSearch();
        },
        methods: {
            initTable () {
                this.initTableCols();
            },
            initTableCols () {
                var showTableCols = [];
                showTableCols.push({ istrue: true, prop: 'orderNoInner', label: '内部订单号', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogDetail(row) });
                showTableCols.push({ istrue: true, prop: 'orderNo', label: '线上订单号', width: '100', sortable: 'custom', });
                showTableCols.push({ istrue: true, prop: 'platform', label: '平台', width: '80', sortable: 'custom', formatter: (row) => row.platformStr });
                showTableCols.push({ istrue: true, prop: 'payTime', label: '支付时间', width: '110', sortable: 'custom' });
                showTableCols.push({ istrue: true, prop: 'deliveryTime', label: '发货时间', width: '110', sortable: 'custom' });
                showTableCols.push({ istrue: true, prop: 'refundTime', label: '退款时间', width: '110', sortable: 'custom' });
                showTableCols.push({ istrue: true, prop: 'refundSpanTimeHH', label: '时间间隔', width: '110', sortable: 'custom', tipmesg: '小时:发货时间-退款时间' });
                showTableCols.push({ istrue: true, prop: 'orderAmount', label: '退款金额', width: '120', sortable: 'custom', });
                showTableCols.push({ istrue: true, prop: 'groupName', label: '运营组', width: '110', sortable: 'custom', });
                showTableCols.push({ istrue: true, prop: 'ticketNumber', label: '物流单号', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogistics(row) });
                this.tableCols = showTableCols;
            },
            showLogDetail (row) {
                this.dialogHisVisible = true;
                this.sendOrderNoInner = row.orderNoInner;
            },
            showLogistics (row) {
                this.drawerVisible = true;
                this.ticketCompanyName = row.ticketCompanyStr;
                this.ticketNumber = row.ticketNumber;
            },
            async initDirectorGroupList () {
                var res2 = await getDirectorGroupList();
                this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
            },
            //时间设置
            refundTimeSelChangePick () {
                if (this.filter.refundrange && this.filter.refundrange.length > 1) {
                    if (this.filter.refundrange[1] != formatTime(new Date(), "YYYY-MM-DD")) {
                        this.payTimeSel = "";
                        return;
                    }
                    var d1 = dayjs(this.filter.refundrange[0]);
                    var d2 = dayjs(this.filter.refundrange[1]);
                    switch (d2.diff(d1, "day")) {
                        //昨天至今日
                        case 1:
                            this.payTimeSel = "1";
                            break;
                        //近7天
                        case 6:
                            this.payTimeSel = "2";
                            break;
                        //近14天
                        case 13:
                            this.payTimeSel = "3";
                            break;
                        //近30天
                        case 29:
                            this.payTimeSel = "4";
                            break;
                        //默认
                        default:
                            this.payTimeSel = "";
                            break;
                    }
                }
                else {

                    this.payTimeSel = "";
                }

            },
            refundTimeSelChange () {
                let oneDayTime = 24 * 60 * 60 * 1000;
                switch (this.payTimeSel) {
                    //昨天至今日
                    case "1":
                        this.filter.refundrange = [
                            formatTime(new Date(new Date().getTime() - 1 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //近7天
                    case "2":
                        this.filter.refundrange = [
                            formatTime(new Date(new Date().getTime() - 6 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //近14天
                    case "3":
                        this.filter.refundrange = [
                            formatTime(new Date(new Date().getTime() - 13 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //近30天
                    case "4":
                        this.filter.refundrange = [
                            formatTime(new Date(new Date().getTime() - 29 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //默认
                    default:
                        this.filter.refundrange = [];
                        break;

                }
            },
            //设置平台下拉
            async setPlatform () {
                var pfrule = await rulePlatform();
                this.platformlist = pfrule.options;
            },
            //获取查询条件
            getCondition () {
                if (this.filter.refundrange && this.filter.refundrange.length > 1) {
                    this.filter.refundStartdate = this.filter.refundrange[0];
                    this.filter.refundEnddate = this.filter.refundrange[1];
                } else {
                    this.filter.refundStartdate = null;
                    this.filter.refundEnddate = null;
                }
                if (this.filter.deltimerange && this.filter.deltimerange.length > 1) {
                    this.filter.delStartdate = this.filter.deltimerange[0];
                    this.filter.delEnddate = this.filter.deltimerange[1];
                } else {
                    this.filter.delStartdate = null;
                    this.filter.delEnddate = null;
                }
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }
                return params;
            },
            //查询第一页
            async onSearch () {
                this.$refs.pager.setPage(1)
                await this.getlist()
            },
            //分页查询
            async getlist () {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true
                const res = await getOrderInvalidShipmentDataAsync(params)
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summary;
                this.oneCount = res.data.summary.oneCount;
                this.twoCount = res.data.summary.twoCount;
                this.threeCount = res.data.summary.threeCount;
                this.overCount = res.data.summary.overCount;
                this.historyCount = res.data.summary.historyCount;
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
            },
            //排序查询
            async sortchange (column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderBy = column.prop;
                    if (orderBy.endsWith("HH")) {
                        orderBy = orderBy.replace("HH", "");
                    }
                    this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            }
        }
    }
</script>
<style scoped>
    ::v-deep .el-link.el-link--primary {
        margin-right: 7px;
    }
    ::v-deep #app .el-container .is-vertical .main .el-main .el-tabs--top {
        height: 95% !important;
        background-color: red;
    }
</style>
