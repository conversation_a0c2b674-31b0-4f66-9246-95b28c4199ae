\<template>
    <my-container>
        <!--顶部操作-->
        <template #header>
            <div class=".top">
                <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                    <el-form-item>
                        <el-switch v-model="Filter.switchshow" inactive-text="售前数据" active-text="售后数据"
                            @change="changeShowgroup">
                        </el-switch>
                    </el-form-item>

                    <el-form-item label="">
                        <el-date-picker style="width: 320px" v-model="Filter.conversationTime" type="datetimerange"
                            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="会话开始日期"
                            end-placeholder="会话结束日期" :picker-options="pickerOptions"
                            :default-value="defaultDate"></el-date-picker>
                    </el-form-item>

                    <el-form-item label="">
                        <el-date-picker style="width: 320px" v-model="Filter.lastOperatorTime" type="datetimerange"
                            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="审核开始日期"
                            end-placeholder="审核结束日期" :picker-options="pickerOptions"
                            :default-value="defaultDate"></el-date-picker>
                    </el-form-item>


                    <el-form-item label="">
                        <el-button-group>
                            <!-- <el-input maxlength="50" v-model.trim="Filter.orderNo" placeholder="线上订单号" clearable /> -->
                            <inputYunhan :key="'1'" :keys="'one'" :width="'150px'" ref="" :inputt.sync="Filter.orderNo"
                                v-model.trim="Filter.orderNo" placeholder="线上订单号" :clearable="true" @callback="callback"
                                title="线上订单号" @entersearch="entersearch">
                            </inputYunhan>
                        </el-button-group>

                    </el-form-item>
                    <el-form-item label="">
                        <!-- <el-input maxlength="20" v-model.trim="Filter.proId" placeholder="宝贝ID" style="width:120px"
                            clearable /> -->
                        <inputYunhan :key="'2'" :keys="'two'" :width="'150px'" ref="" :inputt.sync="Filter.proId"
                            v-model.trim="Filter.proId" placeholder="宝贝ID" :clearable="true" @callback="callbackProId"
                            title="宝贝ID" @entersearch="entersearch">
                        </inputYunhan>
                    </el-form-item>


                    <el-form-item label="" prop="refuseAuditTypeList" v-if="!Filter.switchshow">
                        <el-select v-model="Filter.refuseAuditTypeList" placeholder="评判类型" class="el-select-content"
                            clearable filterable multiple collapse-tags>
                            <el-option v-for="item in PreStatusList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-form-item>


                    <el-form-item label="" prop="refuseAuditTypeList" v-if="Filter.switchshow">
                        <el-select v-model="Filter.refuseAuditTypeList" placeholder="评判类型" class="el-select-content"
                            clearable filterable multiple collapse-tags>
                            <el-option v-for="item in AfterStatusList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-form-item>

                    <!-- <el-form-item label="" prop="newMemberName">
                        <template>
                            <el-select v-model="Filter.responsibleName" placeholder="责任客服" :remote-method="remoteMethod"
                                remote clearable filterable maxlength="20">
                                <el-option v-for="item in customerlist" :key="item.ddUserId" :label="item.label"
                                    :value="item.userName" />
                            </el-select>
                        </template>
</el-form-item> -->
                    <el-form-item label="">
                        <el-input v-model.trim="Filter.responsibleName" placeholder="责任客服" style="width:120px"
                            maxlength="20" clearable />
                    </el-form-item>

                    <el-form-item label="" prop="">
                        <el-select v-model="Filter.appealStatusList" placeholder="状态" class="el-select-content"
                            clearable filterable multiple collapse-tags>
                            <el-option v-for="item in appealTypeList" :key="item.value" :label="item.name"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>

                  <el-form-item >
                    <el-select v-model="Filter.platform" placeholder="平台" style="width:120px" class="el-select-content" clearable
                               @change="changePlatform">
                      <el-option v-for="item in platformList" :key="item.value" :label="item.name" :value="item.value" />
                    </el-select>
                  </el-form-item>

                  <el-form-item >
                    <el-select v-model="Filter.groupNameList" placeholder="分组" class="el-select-content" filterable multiple
                               clearable collapse-tags @focus="changPlatformState">
                      <el-option v-for="item in groupNameList" :key="item" :label="item" :value="item" />
                    </el-select>
                  </el-form-item>

                  <el-form-item >
                    <el-select v-model="Filter.groupManagerList" placeholder="组长" class="el-select-content" multiple collapse-tags
                               clearable filterable @focus="changPlatformState">
                      <el-option v-for="item in groupManagerList" :key="item" :label="item" :value="item" />
                    </el-select>
                  </el-form-item>
                  <el-form-item >
                    <el-input v-model="Filter.responsiblePersonName" placeholder="请输入认责人" clearable></el-input>
                  </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="onSearch()">查询</el-button>
                        <!-- <el-button type="primary" @click="onExport()">导出</el-button> -->


                    </el-form-item>
                </el-form>
            </div>
        </template>

        <vxetablebase ref="table" :that='that' v-if="tableshow" :isIndex='true' :hasexpand='true' :tablefixed='true' :toolbarshow="false"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :tree-config="{}" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="listLoading"
            :height="'100%'">
        </vxetablebase>


        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getPageList" />
        </template>

        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="false" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="历史" v-if="viewChatDialogVisible" :visible.sync="viewChatDialogVisible" width="50%"
            height="600px" v-dialogDrag append-to-body>
            <ViewChatDialogueDialog ref="ViewChatDialogueDialog" :HistoryROW="HistoryROW"
                style="z-index:10000;height:600px" />
        </el-dialog>
        <!-- 订单日志信息 -->
        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            v-dialogDrag append-to-body>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" :isTx="isTx"
                style="z-index:10000;height:600px" />
        </el-dialog>
        <!-- 聊天记录 -->
        <ChartListDialog :v-if="resourcedialogVisible" :isShow="resourcedialogVisible"
            @closeDialog="resourcedialogVisible = false" ref="chartRef">
        </ChartListDialog>

        <AppealDialog :v-if="appealdialogVisible" :isShow="appealdialogVisible"
            @closeDialog="appealdialogVisible = false" ref="appealRef" :HistoryROW1="HistoryROW1"  @upData="onSearch">
        </AppealDialog>

    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import MyContainer from "@/components/my-container";
import CesTable from "@/components/Table/table.vue";
import buschar from '@/components/Bus/buschar'
import {
    getUnPayOrderAppealPageList, getUnPayOrderAppealAfterSalesPageList, unPayOrderAppealExport, unPayOrderAppealAfterSalesExport
} from "@/api/customerservice/chartAppeal";
import ViewChatDialogueDialog from "@/views/customerservice/chartCheck/SalesDialog/ViewChatDialogueDialog"
import { formatLinkProCode } from "@/utils/tools";
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
import { formatTime } from "@/utils";
import ChartListDialog from "@/views/customerservice/chartCheck/chartListDialog.vue";
import AppealDialog from "@/views/customerservice/chartCheckAppeal/SalesDialog/AppealDialog.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import { queryAllCustomerServiceDDUserTop100 } from "@/api/admin/deptuser";
import {
  getGroupManagerList,
  getGruopNameList,
  getUnPayOrderById, GetUnpayOrderSalesList,
  getUserNameList
} from "@/api/customerservice/chartCheck";
import {GetVxeTableColumnCacheAsync} from "@/api/admin/business";
import {getList as getshopList} from "@/api/operatemanage/base/shop";
const appealTypeList = [
    { name: "待申诉", value: 0 },
    { name: "申诉中", value: 1 },
    { name: "已通过", value: 2 },
    { name: "不通过", value: 3 },
    { name: "已过期", value: 4 }
];
//店铺
const platformList = [
    { name: "拼多多", value: 2 },
    { name: "抖音", value: 6 },
    { name: "天猫", value: 1 },
    // { name: "淘工厂", value: 8 },
    { name: "淘宝", value: 9 },
];
const PreList = ["回复问题", "专业能力", "敷衍怠慢", "存在违规/过度承诺", "存在违规/引导线下交易", "存在违规/其他违规", "态度问题/辱骂客户", "态度问题/怒怼客户", "态度问题/反问客户", "态度问题/不耐烦"];//全部
const AfterList = ["回复问题", "流程问题", "专业能力", "敷衍怠慢", "存在违规/过度承诺", "存在违规/引导线下交易", "存在违规/其他违规", "态度问题/辱骂客户", "态度问题/怒怼客户", "态度问题/反问客户", "态度问题/不耐烦", "小额打款异常"];//全部

// ["回复问题", "流程问题", "敷衍怠慢", "专业能力", "存在违规/过度承诺", "存在违规/引导线下交易", "存在违规/其他违规", "态度问题/辱骂客户", "态度问题/怒怼客户", "态度问题/反问客户", "态度问题/不耐烦"];//全部
//售前数据
const tableCols = [
{ istrue: true, prop: 'conversationId', label: '数据编码', sortable: 'custom',width:"150" },
    {
        istrue: true,
        prop: "orderNo",
        label: "线上订单号",
        sortable: "custom",
        type: 'click',
        handle: (that, row) => that.showLogDetail(row)
    },
    {
        istrue: true,
        display: true,
        prop: "proId",
        label: "宝贝ID",
        sortable: "custom",
        type: "html",
        formatter: (row) => formatLinkProCode(row.platform, row.proId),
    },
    {
        istrue: true,
        prop: "conversationTime",
        label: "会话时间",
        sortable: "custom",
        formatter: (row) => {
            return row.conversationTime ? formatTime(row.conversationTime, "YYYY-MM-DD") : "";
        },
    },
    {
        istrue: true,
        prop: "platform",
        label: "平台",
        width: "60",
        sortable: "custom",
        formatter: (row) => {
            return platformList.filter(item => item.value == row.platform)[0].name
        },
    },
    { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom' },
    { istrue: true, prop: 'chatAccount', label: '聊天账号', sortable: 'custom' },
    {
        sortable: 'custom', width: 'auto', align: 'center', prop: 'appealStatus', label: '状态', type: 'clickLink', treeNode: true,
        style: (that, row) => that.renderStatus(row.appealStatus),
        formatter: (row) => {
            return appealTypeList.filter(item => item.value == row.appealStatus)[0].name
        },
    },
    { istrue: true, prop: 'groupName', label: '分组', sortable: 'custom' },
    { istrue: true, prop: 'groupManager', label: '组长', sortable: 'custom' },
    { istrue: true, prop: 'responsiblePersonName', label: '认责人', sortable: 'custom' },
    { istrue: true, prop: 'reviewedBy', label: '审核稽查', sortable: 'custom' },
    { istrue: true, prop: 'responsibleName', label: '责任客服', sortable: 'custom' },
    { istrue: true, prop: 'refuseAuditType', label: '评判类型', sortable: 'custom' },
    { istrue: true, prop: 'lastOperator', label: '最后审核人', sortable: 'custom' },
    {
        istrue: true,
        prop: "lastOperatorTime",
        label: "最后审核时间",
        sortable: "custom",
        formatter: (row) => {
            return row.lastOperatorTime ? formatTime(row.lastOperatorTime, "YYYY-MM-DD") : "";
        },
    },
    {
        istrue: true,
        prop: "expirationTime",
        label: "过期时间",
        sortable: "custom",
        formatter: (row) => {
            return row.expirationTime ? formatTime(row.expirationTime, "YYYY-MM-DD HH:mm") : "";
        },
    },
    {
        istrue: true,
        type: "button",
        label: "操作",
        align: "center",
        fixed: "right",
        btnList: [
            // { label: "聊天记录", handle: (that, row) => that.showDetail(row) },
            { label: "申诉", display: (row) => { return row.appealStatus == 0 ? false : true; }, handle: (that, row) => that.appealResultLog(row),permission: "api:customerservice:UnPayOrderAppeal:AppealApplication"},
        ],
    },
];
const tableCols1 = [
    {
        istrue: true,
        prop: "orderNo",
        label: "线上订单号",
        sortable: "custom",
        type: 'click',
        handle: (that, row) => that.showLogDetail(row)
    },
    {
        istrue: true,
        display: true,
        prop: "proId",
        label: "宝贝ID",
        sortable: "custom",
        type: "html",
        formatter: (row) => formatLinkProCode(row.platform, row.proId),
    },
    {
        istrue: true,
        prop: "conversationTime",
        label: "会话时间",
        sortable: "custom",
        formatter: (row) => {
            return row.conversationTime ? formatTime(row.conversationTime, "YYYY-MM-DD") : "";
        },
    },
    {
        istrue: true,
        prop: "platform",
        label: "平台",
        width: "60",
        sortable: "custom",
        formatter: (row) => {
            return platformList.filter(item => item.value == row.platform)[0].name
        },
    },
    { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom' },
    { istrue: true, prop: 'chatAccount', label: '聊天账号', sortable: 'custom' },
    {
        sortable: 'custom', width: 'auto', align: 'center', prop: 'appealStatus', label: '状态', type: 'clickLink', treeNode: true,
        style: (that, row) => that.renderStatus(row.appealStatus),
        formatter: (row) => {
            return appealTypeList.filter(item => item.value == row.appealStatus)[0].name
        },
    },
    { istrue: true, prop: 'groupName', label: '分组', sortable: 'custom' },
    { istrue: true, prop: 'groupManager', label: '组长', sortable: 'custom' },
    { istrue: true, prop: 'responsiblePersonName', label: '认责人', sortable: 'custom' },
    { istrue: true, prop: 'reviewedBy', label: '审核稽查', sortable: 'custom' },
    { istrue: true, prop: 'responsibleName', label: '责任客服', sortable: 'custom' },
    { istrue: true, prop: 'refuseAuditType', label: '评判类型', sortable: 'custom' },
    { istrue: true, prop: 'lastOperator', label: '最后审核人', sortable: 'custom' },
    {
        istrue: true,
        prop: "lastOperatorTime",
        label: "最后审核时间",
        sortable: "custom",
        formatter: (row) => {
            return row.lastOperatorTime ? formatTime(row.lastOperatorTime, "YYYY-MM-DD") : "";
        },
    },
    {
        istrue: true,
        prop: "expirationTime",
        label: "过期时间",
        sortable: "custom",
        formatter: (row) => {
            return row.expirationTime ? formatTime(row.expirationTime, "YYYY-MM-DD HH:mm") : "";
        },
    },
    {
        istrue: true,
        type: "button",
        label: "操作",
        align: "center",
        fixed: "right",
        btnList: [
            // { label: "聊天记录", handle: (that, row) => that.showDetail(row) },
            { label: "申诉", display: (row) => { return row.appealStatus == 0 ? false : true; }, handle: (that, row) => that.appealResultLog(row),permission: "api:customerservice:UnPayOrderAppeal:AppealApplicationAfterSales"},
        ],
    },
];

export default {
    name: "salesFifth",
    components: { MyContainer, CesTable, buschar, datepicker, ViewChatDialogueDialog, OrderActionsByInnerNos, ChartListDialog, AppealDialog, vxetablebase, inputYunhan },
    data() {
        return {
            that: this,
            tableshow:  true,
            Filter: {
                orderNo: '',
                proId: '',
                groupNameList: [],
                groupManagerList:[],
                responsiblePersonName:null,
                // appealStatusList: '',
            },
            userNameList: [],
            initialList: [],
            groupNameList: [],
            pickerOptions: {
                disabledDate(date) {
                    // 设置禁用日期
                    const start = new Date("1970/1/1");
                    const end = new Date("9999/12/31");
                    return date < start || date > end;
                },
            },
            defaultDate: new Date(),
            tableData: [],
            summaryarry: null,
            listLoading: false,
            total: 0,
            nameList: [],
            groupName: [],
            platformList: platformList,
            appealTypeList: appealTypeList,
            userNameLists: [],
            groupNameLists: [],
            dialogMapVisible: { visible: false, title: "", data: [] },
            tableCols: tableCols,
            showHistoryDialogVisibleSyj: false,
            viewChatDialogVisible: false,
            HistoryROW: {},
            HistoryROW1: {},
            NumRow:{},
            pager: {
                orderBy: "",
                isAsc: false
            },
            PreStatusList: PreList,
            AfterStatusList: AfterList,
            dialogHisVisible: false,
            resourcedialogVisible: false,
            appealdialogVisible: false,
            appealStatusList: [],
            refuseAuditTypeList: [],
            isTx: false,
            orderNo: "",
            proId: '',
            customerlist: [],
            orgOptions: [],
        };
    },
    mounted() {
        this.orgOptions = [...this.customerlist];
        this.onSearch();
    },
    methods: {
        async entersearch(val) {
            // this.filter.indexNo = val;
            this.onSearch();
        },
        async callback(val) {
            this.Filter.orderNo = val;
            //this.onSearch();
        },
        async callbackProId(val) {
            this.Filter.proId = val;
            //this.onSearch();
        },

        renderStatus(appealStatus) {
            const map = {
                '0': "background-color: #54bcbd;color: white;padding: 10px;",
                '1': "background-color: #409dfe;color: white;padding: 10px;",
                '2': "background-color: #81b337;color: white;padding: 10px;",
                '3': "background-color: #ff0000;color: white;padding: 10px;",
                '4': "background-color: #e99d42;color: white;padding: 10px;"
            }
            return map[appealStatus]
        },

        async onSearch() {
            await this.getPageList();

        },
        showLogDetail(row) {
            console.log("row", row)
            this.dialogHisVisible = true;
            this.isTx = row.platform == 1 || row.platform == 9 ? true : false;
            this.orderNo = row.orderNo;
            console.log("this.orderNo", this.orderNo)
            console.log("row.orderNo", row.orderNo)
        },
        async getPageList() {

            let newArr = this.Filter.switchshow ? tableCols1 : tableCols
            this.tableCols = newArr
            // this.tableCols = tableCols


            const para = { ...this.Filter };
            if (this.Filter.conversationTime) {
                para.conversationTimeStart = this.Filter.conversationTime[0];
                para.conversationTimeEnd = this.Filter.conversationTime[1];
            }
            if (this.Filter.lastOperatorTime) {
                para.lastOperatorTimeStart = this.Filter.lastOperatorTime[0];
                para.lastOperatorTimeEnd = this.Filter.lastOperatorTime[1];
            }
            para.salesType = this.Filter.switchshow ? 1 : 0
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };
            this.listLoading = true;

            const res = this.Filter.switchshow ? await getUnPayOrderAppealAfterSalesPageList(params) : await getUnPayOrderAppealPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.tableData = res.data.list;
            this.summaryarry = res.data.summary;
            this.tableshow = true;
        },
        async changeShowgroup() {
            this.Filter.timerange = [];
            this.Filter.groupNameList = "";
            this.Filter.userNameList = "";
            this.groupNameLists = [];
            this.userNameLists = [];
            this.tableshow = false;
            this.onSearch();
        },
        sortchange(column) {

            console.log("column", column)
            if (!column.order) this.pager = {};
            else {
                this.pager = {
                    orderBy: column.prop,
                    isAsc: column.order.indexOf("descending") == -1 ? true : false,
                };
                this.onSearch();
            }
        },
        changPlatformState(val) {
          if (!this.Filter.platform) {
            this.$message({ message: "请先选择平台！", type: "warning" });
            return false;
          }
        },
        async changePlatform(val) {
          this.groupManagerList = [];
          this.groupNameList = [];
          this.Filter.groupManagerList = null
          this.Filter.groupNameList = null

          if (val) {
            const groupManagerList = await getGroupManagerList({ platform: val});
            const gruopName = await getGruopNameList({ platform: val});
            this.groupManagerList = groupManagerList.data;
            this.groupNameList = gruopName.data;
          }
        },
        showDetail(row) { //查看聊天记录
            this.$refs.chartRef.keyWord = row.conversationId
            this.$refs.chartRef.platform = row.platform
            this.$refs.chartRef.dataJson = row;
            this.$refs.chartRef.tableData = this.tableData;
            this.resourcedialogVisible = true;
        },
      async appealResultLog(row) {
            const HistoryROW1 = {
                salesType: this.Filter.switchshow == true ? 1 : 0
            };
            const res = await getUnPayOrderById({id:row.conversationId});
            if (res.success) {
              //
              const thisData = res.data;
              console.log("thisData",thisData);
              if (thisData) {
                //将审核类型,责任人,责任客服等等放入
                row.initialAuditType = thisData.initialAuditType;
                row.refuseInitialAuditType = thisData.refuseInitialAuditType;
                row.oldResponsibleName = thisData.responsibleName;
                row.initialAuditRemark = thisData.initialAuditRemark;
                row.initialOperator = thisData.initialOperator;
                row.initialAuditImgs = thisData.initialAuditImgs;
                row.person = thisData.person;
                row.reasonForRefund = thisData.reasonForRefund;
              }
            }
          if (row.conversationUserId) {
            var params = {
              conversationUserId: row.conversationUserId,
              conversationId: row.conversationId,
              salesType: 0,
              shopId: row.shopId,
              auditState:2,
              orderBy: "createdTime",
              isAsc: false
            }
            const res = await GetUnpayOrderSalesList(params);
            if (!res?.success) {
              return;
            }
            this.$refs.appealRef.reviewList = res.data.list;
          } else {
            this.$refs.appealRef.reviewList = [];
          }
            console.log("HistoryROW1",HistoryROW1)
            this.HistoryROW1 = HistoryROW1;
            this.$refs.appealRef.dataJson = row;
            this.$refs.appealRef.tableData = this.tableData;
            this.$refs.appealRef.keyWord = row.conversationId;
            this.$refs.appealRef.responsiblePersonName = row.responsiblePersonName;
            this.appealdialogVisible = true;
        },
        async onExport() {  //导出
            const para = { ...this.Filter };
            if (this.Filter.conversationTime) {
                para.conversationTimeStart = this.Filter.conversationTime[0];
                para.conversationTimeEnd = this.Filter.conversationTime[1];
            }
            if (this.Filter.lastOperatorTime) {
                para.lastOperatorTimeStart = this.Filter.lastOperatorTime[0];
                para.lastOperatorTimeEnd = this.Filter.lastOperatorTime[1];
            }


            para.salesType = this.Filter.switchshow ? 1 : 0
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };
            var res = this.Filter.switchshow ? await unPayOrderAppealAfterSalesExport(params) : await unPayOrderAppealExport(params);
            const aLink = document.createElement("a");
            let blob = new Blob([res], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);

            var excelName = this.Filter.switchshow ? '售后数据' : '售前数据'
            aLink.setAttribute(
                "download",
                excelName + new Date().toLocaleString() + ".xlsx"
            );
            aLink.click();
        },
        // async remoteMethod(query) {
        //     if (query && query.length > 50) return this.$message.error("输入内容过长");
        //     if (query !== '') {
        //         var res = await queryAllCustomerServiceDDUserTop100({ keywords: query });
        //         if (res && res.success) {
        //             this.customerlist = res.data?.map(item => {
        //                 return {
        //                     label: [
        //                         item.userName ? item.userName : '',
        //                         // item.position ? `(${item.position}` : '',
        //                         // item.empStatusText ? `, ${item.empStatusText})` : '',
        //                         // item.deptName ? ` ${item.deptName}` : ''
        //                     ].filter(Boolean).join(''),
        //                     value: item.ddUserId || null,
        //                     userName: item.userName,
        //                     extData: item
        //                 }
        //             });
        //         }
        //     } else {
        //         this.customerlist = [...this.orgOptions];
        //     }
        // },

    }

};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 55px;
}
</style>
