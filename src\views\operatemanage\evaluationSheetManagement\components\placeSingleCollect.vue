<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss"
                    type="datetimerange" valueFormat="yyyy-MM-dd HH:mm:ss" style="width: 200px" endPlaceholder="放单时间" startPlaceholder="开始时间"/>
                <el-select v-model="ListInfo.platform" placeholder="平台" class="publicCss" clearable filterable>
                    <el-option v-for="item in platformlist" :label="item.label" :value="item.value" :key="item.value" />
                </el-select>
                <el-select v-model="ListInfo.shopCode" placeholder="店铺名称" class="publicCss" clearable filterable
                    reserve-keyword :remote-method="remoteMethod1" remote>
                    <el-option v-for="item in shopList" :label="item.shopName" :value="item.shopCode"
                        :key="item.shopCode" />
                </el-select>
                <el-input v-model.trim="ListInfo.proCode" placeholder="ID" maxlength="50" clearable class="publicCss" />
                <el-select v-model="ListInfo.isNeedEvaluation" placeholder="是否需要评价" class="publicCss" clearable
                    style="width: 100px">
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                </el-select>
                <el-select filterable v-model="ListInfo.groupId" placeholder="放单小组" class="publicCss"
                    style="width: 100px" clearable>
                    <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value"
                        :value="item.key" />
                </el-select>
                <el-select filterable v-model="ListInfo.releaseWorkerId" collapse-tags clearable class="publicCss"
                    placeholder="放单人" style="width: 120px" reserve-keyword :remote-method="remoteMethod" remote>
                    <el-option v-for="item in directorlist" :key="item.userId" :label="item.userName"
                        :value="item.userId" />
                </el-select>
                <el-select filterable v-model="ListInfo.groupContactId" collapse-tags clearable class="publicCss"
                    placeholder="小组对接人" style="width: 150px">
                    <el-option v-for="item in operationsCenterList" :key="item.userId" :label="item.userName"
                        :value="item.userId" />
                </el-select>
                <el-select filterable v-model="ListInfo.claimantId" collapse-tags clearable placeholder="认领人"
                    class="publicCss" style="width: 120px">
                    <el-option v-for="item in operationsCenterList" :key="item.userId" :label="item.userName"
                        :value="item.userId" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="downExcel">模板下载</el-button>

                    <el-button type="primary" @click="importProps">导入</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                    <el-button type="primary" @click="openCollectLog">日志</el-button>
                    <el-button type="primary" @click="handleAdd">新增</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' id="20250321142957"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="120">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <el-button type="text" @click="claim(row)"
                                :disabled="row.status == 1 || row.status == 2">认领</el-button>
                            <el-button type="text" @click="handleAdd(row)"
                                :disabled="row.status == 1 || row.status == 2">编辑</el-button>
                            <el-button type="text" @click="quash(row)"
                                :disabled="row.status == 1 || row.status == 2">撤销</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog title="日志" :visible.sync="logVisible" width="50%" v-dialogDrag>
            <collectLog v-if="logVisible" />
        </el-dialog>

        <el-dialog :title="id?'编辑':'新增'" :visible.sync="addVisible" width="40%" v-dialogDrag>
            <collectAddOrEdit v-if="addVisible" @close="addVisible = false" @getList="getList" :id="id" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions, platformlist, downloadLink } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import collectLog from './collectLog.vue'
import collectAddOrEdit from './collectAddOrEdit.vue'
import YhUserelector from '@/components/YhCom/yh-userselector.vue'
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop'
import {
    getEvaluationFormManagePage,
    exportEvaluationFormManage,
    getOperationsCenterList,
    claimEvaluationFormManage,
    revokeEvaluationFormManage,
    getShopList,
    importEvaluationFormManageData,
    getReleaseWorkerList
} from '@/api/operatemanage/EvaluationFormManage'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'platform', label: '平台', formatter: (row) => row.platformStr },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'proCode', label: 'ID', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'proName', label: '产品简称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderCount', label: '单量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'specialPrecautions', label: '特殊注意事项', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'isNeedEvaluation', label: '是否需要评价', formatter: (row) => row.isNeedEvaluation == true ? '是' : row.isNeedEvaluation == false ? '否' : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'releaseTime', label: '放单时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'groupId', label: '放单小组', formatter: (row) => row.groupName },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'releaseLink', label: '放单链接', type: 'images' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'releaseWorkerId', label: '放单人', formatter: (row) => row.releaseWorkerName },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'groupContactId', label: '小组对接人', formatter: (row) => row.groupContactName },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'claimantId', label: '认领人', formatter: (row) => row.claimantName },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'claimantTime', label: '认领时间', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, collectLog, collectAddOrEdit, YhUserelector
    },
    data() {
        return {
            that: this,
            platformlist,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: null,//开始时间
                endDate: null,//结束时间
                shopCode: '',
                releaseWorkerId: '',
                groupId: null,
                groupContactId: null,
                claimantId: null,
                isNeedEvaluation: null,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            isExport: false,
            fileList: [],
            importLoading: false,
            importVisible: false,
            file: null,
            logVisible: false,
            addVisible: false,
            directorlist: [],
            directorGroupList: [],
            operationsCenterList: [],
            shopList: [],
            id: null
        }
    },
    async mounted() {
        await this.getList()
        await this.getDirectorlist1()
    },
    methods: {
        downExcel(){
        downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250322/1903379713736564737.xlsx', '放单收集导入模板.xlsx')
        },
        async remoteMethod1(query) {
            if (query) {
                const { data } = await getShopList({ shopName: query })
                this.$set(this, 'shopList', data)
            } else {
                this.$set(this, 'shopList', [])
            }
        },
        async remoteMethod(query) {
            if (query) {
                const { data } = await getReleaseWorkerList({ keywords: query })
                this.$set(this, 'directorlist', data)
            } else {
                this.$set(this, 'directorlist', [])
            }
        },
        quash(row) {
            this.$confirm('此操作将撤销该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await revokeEvaluationFormManage({ id: row.id })
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '撤销成功!'
                    });
                    this.getList()
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消认领'
                });
            });
        },
        claim(row) {
            this.$confirm('此操作将认领该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await claimEvaluationFormManage({ id: row.id })
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '认领成功!'
                    });
                    this.getList()
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消认领'
                });
            });
        },
        async getDirectorlist1() {
            const res2 = await getDirectorGroupList({})
            this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);
            const { data } = await getOperationsCenterList()
            this.operationsCenterList = data
        },
        handleAdd(row) {
            this.id = row ? row.id : null
            this.addVisible = true
        },
        openCollectLog() {
            this.logVisible = true
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            //没有时间就提示
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            this.importLoading = true
            await importEvaluationFormManageData(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportEvaluationFormManage(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '放单收集' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getEvaluationFormManagePage(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 150px;
        margin: 0 5px 5px 0px;
    }
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>
