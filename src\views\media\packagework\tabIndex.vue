<template>
    <MyContainer style="width:100%">
        <el-tabs v-model="activeName"  @tab-click="tabclick" style="height: 95%;">
            <el-tab-pane label="加工数据" name="first" style="height: 100%;">
                <copy :isCopy="isCopy"/>
                <!-- <statisticslist :type="type" :allsellist="allsellist" :refresh="refresh" ref="refstatisticslist" :role="currole" tablekey="refstatisticslistgrid"  :lazy="true"/> -->
            </el-tab-pane>
            <el-tab-pane label="卸货数据" name="second" style="height: 100%;">
                <unloadProps />
            </el-tab-pane>
            <el-tab-pane label="移箱入库" name="third" style="height: 100%;">
                <moveStorage />
            </el-tab-pane>
            <el-tab-pane label="计件一" name="forth" style="height: 100%;">
            </el-tab-pane>
            <el-tab-pane label="计件二" name="fifth" style="height: 100%;">
            </el-tab-pane>
            <el-tab-pane label="其他计件" name="sixth" style="height: 100%;">
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import {  getCreateUserList, getRecordUser, getPriceTemplateList
} from '@/api/inventory/packagesprocess';//包装加工
import { getShootingSetData, } from '@/api/media/shootingset'
import MyContainer from "@/components/my-container";
import unloadProps from "./unloadProps.vue";
import moveStorage from "./moveStorage.vue";
import historystatisticslist from '@/views/media/packagework/commissionHistoryConfig/historystatisticslist.vue';
import { getUserRoleList,getShootingViewPersonAsync} from '@/api/media/ShootingVideo';
import copy from './copy.vue'
export default {
    name: "tabIndex",
    components: {
        MyContainer, unloadProps, moveStorage,historystatisticslist,copy,
        'statisticslist': function (resolve) {
            import('@/views/media/packagework/statisticslist.vue').then(function (module) {
                resolve(module.default);
            });
        },
    },
    data() {
        return {
            activeName: 'first',
            isCopy:true,
        };
    },
   mounted() {
    },
    methods: {
      
    }
};
</script>

<style lang="scss" scoped></style>