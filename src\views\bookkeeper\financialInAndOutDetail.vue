<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
        <el-form-item  label="年月:">
          <el-date-picker
            v-model="Filter.timerange"
            type="monthrange"
            align="right"
            unlink-panels
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            :picker-options="pickerOptions">
            </el-date-picker>
        </el-form-item>
        <el-form-item label="所属店铺:" label-position="right" >
          <el-select filterable v-model="Filter.nameShop" placeholder="请选择" class="el-select-content">
            <el-option label="所有" value></el-option> 
            <el-option 
              v-for="item in shopList"
              :key="item.shopName"
              :label="item.shopName"
              :value="item.shopName">             
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单编号:" label-position="right" >
            <el-input v-model="Filter.serialNumberOrder" style="width:183px;"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' 
              :hasexpand='false' @sortchange='sortchange' :tableData='ZTCKeyWordList' 
              @select='selectchange' :isSelection='false'
         :tableCols='tableCols' :loading="listLoading" style="height:730px">
      <el-table-column type="expand">
        <template slot-scope="props">
        <div>
          <el-table :data="props.row.detaildata" style="width: 100%">
            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
            </el-table-column>
          </el-table>
        </div>
      </template>
      </el-table-column>
       <template slot='extentbtn'>
          <el-button-group>            
           
          </el-button-group>
        </template>
    </ces-table>    
    <!--分页-->
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getList"
      />
    </template>
  </my-container>
</template>
<script>
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
import { formatPlatform,formatLink} from "@/utils/tools";
import {getFinancialInAndOutDetailPageList as getPageList } from '@/api/bookkeeper/financialDetail'

const tableCols =[
      {istrue:true,prop:'timeOrder',label:'订单月', width:'80'},
      {istrue:true,prop:'numberOnlineOrderOrigin',label:'原始线上订单号', width:'80'},
      {istrue:true,prop:'timeOccurIn',label:'时间-收', width:'80'},
      {istrue:true,prop:'typeBusinessIn',label:'类型-收', width:'80'},
      {istrue:true,prop:'timeOccurOut1',label:'时间-退1', width:'80'},
      {istrue:true,prop:'typeBusinessOut1',label:'类型-退1', width:'80'},
      {istrue:true,prop:'timeOccurOut2',label:'时间-退2', width:'80'},
      {istrue:true,prop:'typeBusinessOut2',label:'类型-退2', width:'80'},
     ];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      Filter: {
        timerange: [formatTime(dayjs().subtract(3, 'month'), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")],
      },
       pickerOptions: {
          shortcuts: [{
            text: '本月',
            onClick(picker) {
              picker.$emit('pick', [new Date(), new Date()]);
            }
          },  {
            text: '最近3个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setMonth(start.getMonth() - 3);
              picker.$emit('pick', [start, end]);
            }
          }]
        },
      shopList:[],
      userList:[],
      groupList:[],
      ZTCKeyWordList: [],
      tableCols:tableCols,
      total: 0,
      pager:{OrderBy:"date",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
    };
  },
  async mounted() {
    await this.getShopList();
  },
  methods: {
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=[];
        res1.data?.forEach(f => {
          if(f.isOpen==1&&f.shopName.indexOf("拼多多")>=0&&f.shopCode)
              this.shopList.push(f);
        });
    },
    onRefresh(){
        this.onSearch()
    },
    onSearch(){
       this.$refs.pager.setPage(1);
       this.getList();
    },
    async getList(){
      const para = {...this.Filter};
      if (this.Filter.timerange) {
        para.dateStart = this.Filter.timerange[0];
        para.dateEnd = this.Filter.timerange[1];
      }

      if(!(para.dateStart&&para.dateEnd)){
        this.$message({message:"请先选择日期！",type:"warning"});
        return;
      }

      var  beginMonth = dayjs(this.Filter.timerange[0]).startOf('month')
      var endMonth = dayjs(this.Filter.timerange[1]).endOf('month')
       para.dateStart = formatTime(beginMonth, 'YYYY-MM-DD HH:mm:ss')
       para.dateEnd = formatTime(endMonth, 'YYYY-MM-DD HH:mm:ss')

      
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };

      console.log(para)

      this.listLoading = true;
      const res = await getPageList(params);
      this.listLoading = false;

     if(null == res)
     return

      console.log(res.data.list)

      this.total = res.data.total
      this.ZTCKeyWordList = res.data.list;
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>