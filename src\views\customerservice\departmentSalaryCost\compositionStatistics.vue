<template>
  <!-- 组成本统计 -->
  <container v-loading="pageLoading">
      <template #header>
          <el-row>

                  <!-- <el-input v-model.trim="filter.userName" placeholder="请输入姓名" style="width: 140px; " :maxlength="50" clearable>
                  </el-input> -->


                  <el-select v-model="filter.region" placeholder="请选择区域" filterable style="width: 160px; margin-left: 10px" clearable>
                     <el-option :label="item.label" :value="item.value" v-for="(item,index) in quyu" :key="index"  />
                 </el-select>

                 <el-select v-model="filter.platform" placeholder="请选择平台" clearable>
                  <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>

                  <!-- <el-cascader
                 style="width: 200px; margin-left: 10px"
                  v-model="filter.subgroupList"
                  :options="deptList"
                  filterable
                  placeholder="请选择分组"
                  :props="props"
                  collapse-tags
                  :show-all-levels="false"
                  clearable></el-cascader> -->

                <el-select v-model="filter.subgroup" placeholder="请选择分组" clearable multiple collapse-tags filterable>
                  <el-option v-for="item in subgroupListArr" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>

                 <el-select v-model="filter.state" placeholder="人员状态" filterable style="width: 160px; margin-left: 10px" clearable>
                     <el-option :label="item.label" :value="item.value" v-for="(item,index) in peopleStatus" :key="index" />
                 </el-select>

                 <!-- <el-select v-model="filter.zoneName" placeholder="客服等级" filterable style="width: 160px; margin-left: 10px">
                     <el-option :label="item" :value="item" v-for="(item,index) in quyuList" :key="index" />
                 </el-select> -->

                 <el-date-picker  v-show="!filter.timeTypeName" style="width: 260px; margin-left: 10px" v-model="filter.CostDate" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                      range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="timechange"  :clearable="true"></el-date-picker>

                <el-date-picker
                  v-show="filter.timeTypeName"
                  type="month"
                  format="yyyy-MM"
                  value-format="yyyy-MM-dd"
                  style="width: 160px; margin-left: 10px"
                  v-model="filter.costDate"
                  @change="mothchange"
                  :clearable="true"
                  placeholder="选择月份">
                </el-date-picker>


             <el-button-group style="margin-left: 10px">
              <el-button type="primary" @click="onSearch">查询</el-button>
              <!-- <el-button type="primary" @click="onSearch">操作记录</el-button> -->
             </el-button-group>

          </el-row>
          <el-row>
            <el-switch
              v-model="filter.timeTypeName"
              active-text="月"
              inactive-text="日">
            </el-switch>
            <span style="font-size: 12px;margin-left: 20px;">默认本月</span>
          </el-row>
      </template>
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
          :isSelectColumn="false" :showsummary='true' :summaryarry='summaryarry' :tablefixed='false' :tableData='tableData' :tableCols='tableCols'
          :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:95%;margin: 0">
      </ces-table>
      <!-- <template #footer>
          <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
      </template> -->
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />

      <el-dialog title="趋势图" :visible.sync="dialogMapVisible.visible" width="80%"
          :close-on-click-modal="false" v-dialogDrag>
          <div>
              <span>
                  <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
              </span>
          </div>
          <span slot="footer" class="dialog-footer">
              <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
          </span>
      </el-dialog>

  </container>
 </template>
 <script>
 import { formatTime } from "@/utils";
 import dayjs from "dayjs";
 import container from '@/components/my-container';
 import cesTable from '@/components/Table/table.vue';
 import YhImgUpload1 from "@/components/upload/yh-img-upload1.vue";
 import { formatLinkProCode, platformlist } from '@/utils/tools'
 import {
  importBaseSupermarketGoodAsync,
  getPageBaseSupermaketGoodAsync,
  updateBaseSupermaketGoodAsync,
  delBaseSupermaketGoodAsync,
  getGoodTypeAsync,
  saveGoodTypeAsync,
  updateBaseSupermaketGoodStockAsync
 } from '@/api/profit/orderfood';
 import { getOrderFoodMenuProvier, getAreaSetList, getUserOrderDetailList, quyuList } from '@/api/profit/orderfood';
 import fliterjs from "@/views/customerservice/departmentSalaryCost/fliterjs.js";

 import { groupCostsPage, groupCostsTrendChart } from '@/api/bladegateway/yunhangiscustomer.js';
 import buschar from '@/components/Bus/buschar'

 const tableCols = [

  { istrue: true, prop: 'regionName', label: '区域',  width: '100' },
  { istrue: true, prop: 'platformName', label: '所在平台', width: '100', },
//   { istrue: true, prop: 'subgroup', label: '分组',  width: '160' },
  { istrue: true, prop: 'subgroup', label: '分组',  width: '200', type: "click", sortable: 'custom', formatter: (row)=> row.subgroupName, handle:(that,row)=>that.onPacketJump(row) },

  { istrue: true, prop: 'regularAttendance', label: '转正出勤天数', width: '120',   },
  { istrue: true, prop: 'probationaryAttendance', label: '试用出勤天数', width: '120',   },
  { istrue: true, prop: 'totalAttendance', label: '合计出勤天数', width: '120',   },
  { istrue: true, prop: 'regularActualAmount', label: '转正实发金额', width: '120',   },
  { istrue: true, prop: 'probationaryActualAmount', label: '试用实发金额', width: '120',   },
  { istrue: true, prop: 'totalActualAmount', label: '合计实发金额', width: '120',   },
  { istrue: true, prop: 'receptionNum', label: '接待量', width: 'auto',   },

  { istrue: true, prop: 'regularPersonNum', label: '转正人数', width: '120',   },
  { istrue: true, prop: 'probationaryPersonNum', label: '试用人数', width: '120',   },

  { istrue: true, prop: 'individualCost', label: '单个成本', width: '120',   },
  { istrue: true, prop: 'receptionAvgAmount', label: '转正人员平均薪资', width: '160',   },

  { istrue: true, prop: 'probationaryAvgAmount', label: '试用人员平均薪资', width: '160',   },
  { istrue: true, prop: 'totalAvgAmount', label: '整体平均薪资', width: '120',   },
  {
        istrue: true, type: "button", label: '趋势图', align: 'center', sortable: false, width: "100", fixed: "right",
        btnList: [{ label: "趋势图", handle: (that, row) => that.onCtrl(row) }]
    }
 ];

 const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
 const endDate = startDate;
 //const endDate = formatTime(new Date(), "YYYY-MM-DD");

 export default ({
  name: "Users",
  mixins: [fliterjs],
  components: { container, cesTable, YhImgUpload1,buschar },
  data () {
      return {
          dialogVisibleStock: false,
          fileList: [],
          gysList: [],
          dialogEdit: false,
          stockForm: {
              stock: 0,
              id: 0
          },
          editForm: {
          },
          form: {
              goodTypeList: [],
          },
          dialogMapVisible: {
            visible: false,
          },
          quyuList: [],
          dialogMenuType: false,
          uploadLoading: false,
          dialogVisibleData: false,
          that: this,
          filter: {
             //  timerange: [startDate, endDate],
             CostDate: [
                  // formatTime(dayjs().subtract(30, "day"), "YYYY-MM-DD"),
                  // formatTime(new Date(), "YYYY-MM-DD"),
              ],
              timeTypeName: true,
          },
          platformlist: platformlist,
          tableCols: tableCols,
          tableHandles: null,
          tableData: [],
          total: 0,
          pager: { OrderBy: "", IsAsc: false },
          listLoading: false,
          pageLoading: false,
          summaryarry: {},
          sels: [],
      };
  },
  async mounted () {
      await this.onSearch();
  },
  methods: {
    onPacketJump(row){
      this.$emit('templatepageclose', row);
    },
    timechange(val){
      if(val){
        this.filter.CostDate = val;
      }else{
        this.filter.CostDate = [];
      }
    },
    mothchange(val){
        if(!val){
            this.filter.CostDate = [];
            return
        }
        this.filter.CostDate = [this.filter.costDate, this.addMonthToDate(this.filter.costDate)];
    },
    async onCtrl(row){
        let params = {
          ...row,
          "startCostDate": this.filter.CostDate?this.filter.CostDate[0]:null,
          "endCostDate": this.filter.CostDate?this.filter.CostDate[1]:null,
          // "timeType": this.filter.timeTypeName?2:1,
          // "sfAmount": row.sfAmount,
          // "sfAttendanceDays": row.sfAttendanceDays,
          // "commission": row.royaltyAmount,

          "platform": row.platform,
          "region": row.region,
          "subgroup": [row.subgroup],
        };
        const res = await groupCostsTrendChart(params).then(res => {
            if (res) {
                this.dialogMapVisible.visible = true;
                this.dialogMapVisible.data = res.data;
            }
        });
        this.dialogMapVisible.visible = true;
      },
      async getsize(){
       let params = {

       }
       const res = await getOrderFoodMenuProvier();
          if(!res.success){
              return
          }
          this.gysList = res.data;
      },
      async getgyss(){
          const res = await getOrderFoodMenuProvier();
          if(!res.success){
              return
          }
          this.gysList = res.data;
          this.filter.gysName = this.gysList[0];
      },
      async getGoodType () {
          const res = await getGoodTypeAsync();
          this.form.goodTypeList = res?.data;
      },
      // async onSearch () {
      //   console.log("查询条件", this.filter)
      //     this.$refs.pager.setPage(1);
      //     // this.$refs.table.currentLvl = 9;
      //     // await this.getquyu();

      //     await this.getList();

      // },
      async sortchange (column) {
          if (!column.order)
              this.pager = {};
          else
              this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
          this.$refs.pager.setPage(1);
          await this.onSearch();
      },
      async getList () {
          if (this.filter.CostDate && this.filter.CostDate.length > 0) {
              this.filter.startCostDate = this.filter.CostDate[0];
              this.filter.endCostDate = this.filter.CostDate[1];
          }else{
            this.filter.startCostDate = null;
            this.filter.endCostDate = null;
          }
          var that = this;
          console.log("5555",that.filter.timeTypeName)
          that.filter.timeType =  that.filter.timeTypeName?2:1;
          this.listLoading = true;
          var pager = this.$refs.pager.getPager();
          console.log("查询条件",this.filter)
          const params = { ...pager, ...this.pager, ...this.filter, ...this.myfilter };
          const res = await groupCostsPage(params).then(res => {
              that.total = res.data?.total;
              that.tableData = res.data?.list;
              that.summaryarry = res.data?.summary;
          });


          this.listLoading = false;
      },
  }
 })
 </script>
 <style scoped>
::v-deep .el-cascader__search-input{
  margin: 2px 0 2px 5px;
  height: 18px;
  align-items: center;
}
::v-deep .el-select__tags-text {
  max-width: 60px;
}
 </style>

