<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="">
                    <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM"
                        value-format="yyyyMM" placeholder="年月">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="">
                    <el-select filterable v-model="filter.platform" placeholder="平台" disabled clearable
                        style="width:110px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="" label-position="right">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺" style="width: 180px">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.serialNumberOrder" placeholder="订单编号" style="width:160px;" />
                </el-form-item>
                <el-form-item label="" label-position="right">
                    <el-select filterable clearable v-model="filter.recoganizeType" placeholder="业务类型"
                        style="width:100px;">
                        <el-option v-for="item in recoganizePddList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.orderType" placeholder="账务类型" style="width:160px;" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport">汇总导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='ZTCKeyWordList' :showsummary='true' :summaryarry='summaryarry' @select='selectchange'
            :isSelection='false' :tableCols='tableCols' :loading="listLoading" :isSelectColumn='false'>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList, getList as getshopList } from '@/api/operatemanage/base/shop';
import { formatPlatform, formatLink, platformlist } from "@/utils/tools";
import { getFinancialDetailPDD as getPageList, getRecoganizePddDic, ExportFinancialDetailPddList } from '@/api/monthbookkeeper/financialDetail'
const tableCols = [
    { istrue: true, prop: 'yearMonth', label: '年月', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'orderNumber', label: '订单编号', sortable: 'custom', width: '200', type: 'html' },
    { istrue: true, prop: 'timeOccur', label: '发生时间', sortable: 'custom', width: '150', type: 'html' },
    { istrue: true, prop: 'amountIncome', label: '收入金额', sortable: 'custom', width: '100', type: 'html', formatter: (row) => { return row.amountIncome?.toFixed(2) } },
    { istrue: true, prop: 'amountPaid', label: '支出金额', sortable: 'custom', width: '100', type: 'html', formatter: (row) => { return row.amountPaid?.toFixed(2) } },
    { istrue: true, prop: 'recoganizeType', label: '业务类型', sortable: 'custom', width: '150', type: 'html', formatter: (row) => { return row.recoganizeTypeName } },
    { istrue: true, prop: 'orderType', label: '账务类型', sortable: 'custom', width: '150', type: 'html' },
    { istrue: true, prop: 'busRemark', label: '业务描述', sortable: 'custom', width: '200', type: 'html' },
    { istrue: true, prop: 'remark', label: '备注', sortable: 'custom', width: '200', type: 'html' },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
    data() {
        return {
            that: this,
            filter: {
                platform: 2,
                yearMonth: null,
                shopCode: null,
                serialNumberOrder: null,
                recoganizeType: null,
                OrderType: null
            },
            shopList: [],
            userList: [],
            groupList: [],
            platformlist: platformlist,
            ZTCKeyWordList: [],
            tableCols: tableCols,
            summaryarry: {},
            total: 0,
            pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            recoganizePddList: [],
        };
    },
    async mounted() {
        this.onchangeplatform();
        this.getRecoganizePddList();
    },
    methods: {
        async getRecoganizePddList() {
            const res = await getRecoganizePddDic();
            if (!res?.success) return
            this.recoganizePddList = res?.data;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async onchangeplatform() {
            const res1 = await getshopList({ platform: 2, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
        },
        // async getShopList(){
        //   const res1 = await getAllShopList();
        //   this.shopList=[];
        //     res1.data?.forEach(f => {
        //       if(f.isCalcSettlement&&f.shopCode)
        //           this.shopList.push(f);
        //     });
        // },
        onRefresh() {
            this.onSearch()
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getList();
        },
        async getList() {
            let params = this.getCondition();
            this.listLoading = true;
            const res = await getPageList(params);
            this.listLoading = false;
            this.total = res.data?.total
            res?.data?.list.forEach(d => {
                d.recoganizeTypeName = this.recoganizePddList.find(t => t.value === d.recoganizeType)?.label ?? ""
            });
            this.ZTCKeyWordList = res?.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        getCondition() {
            let pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter,
            };
            return params;
        },
        async onExport(opt) {
            if (!this.filter.yearMonth) {
                this.$message({ message: "请先选择月份", type: "warning" });
                return;
            }
            let pars = this.getCondition();
            const params = { ...pars, ...opt };
            let res = await ExportFinancialDetailPddList(params);
            if (!res?.data) {
                return
            }
            this.$message({ message: "正在后台导出中，请点击头像-在下载管理中查看", type: "success" });
            // const aLink = document.createElement("a");
            // let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            // aLink.href = URL.createObjectURL(blob)
            // aLink.setAttribute('download', '月报按店铺汇总导出_支付宝_' + new Date().toLocaleString() + '_.xlsx')
            // aLink.click()
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
