<template>
  <MyContainer v-loading="loading">
    <template #header>
      <div class="top">
        <el-date-picker class="publicCss " v-model="timeRange" align="left" type="daterange" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions1" value-format="yyyy-MM-dd"
          v-show="ListInfo.isContainOldData" @change="changeTime" :clearable="false" />
        <el-date-picker class="publicCss " v-model="timeRange" align="left" type="daterange" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" value-format="yyyy-MM-dd"
          v-show="!ListInfo.isContainOldData" @change="changeTime" :clearable="false" />
        <div style="height: 28px;">
          <el-cascader class="publicCss cascaderCss" style="width: 330px" collapse-tags clearable :options="options1"
            placeholder="请选择发货地" :props="{ multiple: true, filterable: true, checkStrictly: true }"
            v-model="ListInfo.provinceCityDistricts1" filterable></el-cascader>
        </div>
        <el-select v-model="ListInfo.isProxyShipping" placeholder="是否能代发" class="publicCss" clearable @change="(e) => {
          if (e == 0) {
            ListInfo.printOrderPlatform = null
          }
        }">
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
          <el-option label="未绑定" :value="2" />
        </el-select>
        <el-input v-model.trim="ListInfo.printOrderPlatform" placeholder="打单平台名称" maxlength="50" clearable
          class="publicCss" :disabled="ListInfo.isProxyShipping === 0" />
        <el-select v-model="ListInfo.labels" style="width: 320px ;height: 29px" placeholder="季节/节日" multiple filterable
          collapse-tags clearable>
          <el-option v-for="item in seasonList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div>
          <el-button type="primary" @click="getList('search')">查询</el-button>
          <el-button type="primary" @click="exportProps">导出</el-button>
          <el-button type="primary" @click="tocreateimg">复制图片</el-button>
          <el-switch v-model="ListInfo.isContainOldData" active-text="不含历史数据" inactive-text="含历史数据"
            @change="changeSwitch">
          </el-switch>
        </div>
      </div>
    </template>
    <div style="position: relative;" ref="main1" id="main1">
      <div id="main" ref="main" style="width: 100%; height: 710px"></div>
      <div class="card" v-if="card && card.length > 0">
        <div class="card_top">{{ card[0].name }}</div>
        <div class="card_bottom">
          <div v-for="(item, i) in card" :key="item.code" v-if="i != 0" class="card_bottom_item" v-throttle="3000"
            @click="cardClick(item)">{{
              item.name }}</div>
        </div>
      </div>
      <div v-show="dialogPurchaseVisible" :style="{
        position: 'fixed',
        top: position.top + 'px',
        left: position.left + 'px',
      }" class="tooltip">
        <div>{{ mouseMoveData.name }}: {{ mouseMoveData.value ? formatNum(mouseMoveData.value[2]) : '' }} ({{
          mouseMoveData.value_Rate
        }}%)</div>
        <div>采购数量: {{ mouseMoveData.qty }} ({{ mouseMoveData.qty_Rate }}%)</div>
        <div class="tooltipTop">采购资金: {{ mouseMoveData.cost }} ({{ mouseMoveData.cost_Rate }}%)</div>
        <div class="tooltipTop">订单量: {{ mouseMoveData.orderCount }}</div>
        <div class="tooltipTop">销售数量: {{ mouseMoveData.saleCount }}</div>
        <div class="tooltipTop" style="display: flex;flex-wrap: wrap;">
          <el-tooltip v-if="tag" v-for="(tag, i) in mouseMoveData.labels" :key="i" class="item" effect="dark"
            placement="bottom">
            <div slot="content">
              <div>
                采购数量: {{ getLablePorps(tag, 'qty') }} ({{ getLablePorps(tag, 'qty_Rate') }}%)
                <br />
                采购资金: {{ getLablePorps(tag, 'cost') }} ({{ getLablePorps(tag, 'cost_Rate') }}%)
                <br />
                订单量: {{ getLablePorps(tag, 'orderCount') }}
                <br />
                销售数量: {{ getLablePorps(tag, 'saleCount') }}
              </div>
            </div>
            <el-tag style="margin:0 5px 5px 0;cursor: pointer;">
              {{ tag }}
            </el-tag>
          </el-tooltip>
        </div>
      </div>
    </div>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
// import { pickerOptions } from "@/utils/tools";
import { getSupplierMapInfos, ExportSupplierTagGoodsInfosByMap } from '@/api/inventory/supplier'
import dayjs from "dayjs";
import dateRange from "@/components/date-range/index.vue";
import * as echarts from "echarts";
import "echarts/extension/bmap/bmap";
import html2canvas from 'html2canvas'
import {
  GetProvinceCityDistrictAndNo
} from '@/api/inventory/purchase'
import _ from 'lodash'
const seasonList = [
  { label: '无标签', value: '无标签' },
  { label: '四季款（常年）', value: '四季款（常年）' },
  { label: '春季款（1月1日-4月底）', value: '春季款（1月1日-4月底）' },
  { label: '夏季款（3月15日-9月底）', value: '夏季款（3月15日-9月底）' },
  { label: '秋季款（8月15日-10月底）', value: '秋季款（8月15日-10月底）' },
  { label: '冬季款（9月15日-1月底）', value: '冬季款（9月15日-1月底）' },
  { label: '开学季（1月1日至2月底）', value: '开学季（1月1日至2月底）' },
  { label: '开学季（7月1日至8月底）', value: '开学季（7月1日至8月底）' },
  { label: '清明节（3月1日至3月底）', value: '清明节（3月1日至3月底）' },
  { label: '端午节（农历四月初五至四月底）', value: '端午节（农历四月初五至四月底）' },
  { label: '中秋节（农历七月十五至八月初十）', value: '中秋节（农历七月十五至八月初十）' },
  { label: '国庆节（9月1日至9月25日）', value: '国庆节（9月1日至9月25日）' },
  { label: '圣诞节（不允许进货）', value: '圣诞节（不允许进货）' },
  { label: '元旦节（农历十一月初一至腊月十五）', value: '元旦节（农历十一月初一至腊月十五）' },
  { label: '春节（农历十一月初一至腊月十五）', value: '春节（农历十一月初一至腊月十五）' }
]
export default {
  name: "scanCodePage",
  components: {
    MyContainer,
    vxetablebase,
    dateRange,
  },
  data() {
    return {
      pickerOptions1: {
        disabledDate(time) {
          return time.getTime() < new Date('2024-11-28').getTime();
        },
        shortcuts: [{
          text: '昨天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近半月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },]
      },
      pickerOptions: {
        shortcuts: [{
          text: '昨天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近半月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        },]
      },
      seasonList,
      options1: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        isProxyShipping: null,
        printOrderPlatform: null,
        isContainOldData: true,
        provinceCityDistrict: null,
        provinceCityDistricts1: [],
        areaName: null,
        areaCode: null,
        startTime: dayjs('2024-11-29').format('YYYY-MM-DD'),
        endTime: dayjs().format('YYYY-MM-DD'),
      },
      timeRange: [dayjs('2024-11-29').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      tableData: [],
      total: 0,
      loading: true,
      isExport: false,
      data: [],
      geoCoordMap: {},
      zoomendName: '',
      dbClickName: '',
      card: [],
      eventType: null,
      scalName: '',
      option: {},
      myChart: null,
      throttledDblClickHandler: null,
      throttledZoomendHandler: null,
      c: null,
      center: null,
      manyArea: false,//如果查询的时候有多个地区就不需要地图的缩放事件,只需要地图的点击事件
      zoom: 5,
      mouseMoveData: [],
      dialogPurchaseVisible: false,
      position: {
        top: 0,
        left: 0
      }
    };
  },
  async mounted() {
    await this.getGetProvinceCityDistrict()
    await this.getList('init');
    const tooltip = document.querySelector('.tooltip')
    const msgToolTips = document.querySelector('.el-tooltip__popper')
    tooltip.addEventListener('mouseenter', () => {
      this.dialogPurchaseVisible = true
      msgToolTips?.addEventListener('mouseenter', () => {
        this.dialogPurchaseVisible = true
      })
    })
    const main1 = document.querySelector('#main1')
    main1.addEventListener('click', () => {
      this.dialogPurchaseVisible = false
    })
  },
  methods: {
    loadBaiduMapScript() {
      return new Promise((resolve, reject) => {
        // 如果已经加载过，直接返回
        if (this.isBaiduMapLoaded) {
          resolve();
          return;
        }
        // 添加全局回调函数
        const callbackName = `bmap_init_callback_${Date.now()}`;
        window[callbackName] = () => {
          this.isBaiduMapLoaded = true;
          delete window[callbackName];
          resolve();
        };
        // 创建script标签
        const script = document.createElement('script');
        script.src = `https://api.map.baidu.com/api?v=3.0&ak=rGpSC799A52GdPmQ1mBzCZON&callback=${callbackName}`;
        script.onerror = (error) => {
          delete window[callbackName];
          console.error('百度地图加载失败', error);
          reject(new Error('百度地图加载失败'));
          this.$message.error('百度地图加载失败，请检查网络或密钥');
        };

        // 添加到文档中
        document.head.appendChild(script);
      });
    },
    // 检查BMap是否可用
    waitForBMap() {
      return new Promise((resolve) => {
        if (window.BMap) {
          resolve();
          return;
        }
        const checkInterval = setInterval(() => {
          if (window.BMap) {
            clearInterval(checkInterval);
            resolve();
          }
        }, 100);
      });
    },
    findAreaCode(val) {
      const stack = this.options1.map(item => ({ item, path: [item.value] }));
      while (stack.length > 0) {
        const { item, path } = stack.pop();
        if (item.value === val) {
          return path;
        }
        if (item.children) {
          for (const child of item.children) {
            stack.push({ item: child, path: [...path, child.value] });
          }
        }
      }
      return null;
    },
    changeSwitch(e) {
      if (this.ListInfo.isContainOldData) {
        this.timeRange = [dayjs('2024-11-29').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
        this.ListInfo.startTime = dayjs('2024-11-29').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
      }
    },
    changeTime(e) {
      this.ListInfo.startTime = e[0] ? e[0] : null
      this.ListInfo.endTime = e[1] ? e[1] : null
    },
    async exportProps() {
      this.isExport = true
      await ExportSupplierTagGoodsInfosByMap(this.ListInfo).finally(() => {
        this.isExport = false
      })
    },
    getLablePorps(tag, prop) {
      const res = this.mouseMoveData.tags.find(item => item.seasonOrFestivalTag?.includes(tag))
      return (res[prop] !== null || res[prop] !== undefined) ? res[prop].toLocaleString() : 0
    },
    tocreateimg() {
      const main1 = this.$refs.main1; // 获取引用的 DOM 元素
      if (!main1) {
        return;
      }
      html2canvas(main1, {
        allowTaint: true,  // 允许跨域资源截图
        useCORS: true,     // 使用 CORS 模式
        scale: 2,          // 提高截图分辨率
      })
        .then((canvas) => {
          const imgData = canvas.toDataURL("image/png");
          const link = document.createElement("a");
          link.href = imgData;
          link.download = `供应商分布图_${new Date().getTime()}.png`;
          document.body.appendChild(link); // 临时加入 DOM
          link.click();                   // 触发下载
          document.body.removeChild(link); // 清理 DOM
        })
        .catch((error) => {
        });
    },
    cardClick(item) {
      this.manyArea = false //如果点击了卡片就需要地图的缩放事件
      const name = item.name.split('：')[0]
      const res = this.geoCoordMap[name];
      if (res) {
        this.center = {
          lat: res[0],
          lng: res[1]
        }
      } else {
        this.center = null
      }
      this.ListInfo.areaCode = item.code
      this.ListInfo.areaName = null
      this.getList('cardClick')
    },
    async getGetProvinceCityDistrict() {
      const { data, success } = await GetProvinceCityDistrictAndNo();
      if (!success) {
        return;
      }
      this.options1 = data ? data : [];
    },
    async initCharts() {
      // 确保百度地图API已加载
      await this.loadBaiduMapScript();
      // 额外等待BMap对象可用
      await this.waitForBMap();
      var chartDom = document.getElementById("main");
      this.myChart = echarts.init(chartDom);
      var option;
      option = {
        backgroundColor: "transparent",
        title: {
          left: "center",
        },
        // tooltip: {
        //   trigger: "none",
        //   // triggerOn:'mousemove',
        //   formatter: function (params) {
        //     console.log(params, 'params');
        //     const res = JSON.parse(JSON.stringify(params.data.tags))
        //     let str = ''
        //     const tag = res?.map(item => {
        //       if (item.seasonOrFestivalTag?.includes('（')) {
        //         item.seasonOrFestivalTag = item.seasonOrFestivalTag.split('（')[0]
        //       }
        //       return item.seasonOrFestivalTag
        //     })
        //     tag?.forEach((item, i) => {
        //       if ((i + 1) % 3 != 0) {
        //         str += `<button style='margin-bottom:5px'>${item}</button> `
        //       } else {
        //         str += `<button style='margin-bottom:5px'>${item}</button> <br/>`
        //       }
        //     })
        //     return params.name + ': ' + params.value[2] + "<br/>"
        //       + '采购数量: ' + params.data.qty + `  (${params.data.qty_Rate})%` + "<br/>"
        //       + '采购资金: ' + params.data.cost + `  (${params.data.cost_Rate})%` + "<br/>"
        //       + '订单量: ' + params.data.orderCount + "<br/>"
        //       + '销售额: ' + params.data.saleCount + (str ? `<br/>` + str : '')
        //   },
        // },
        bmap: {
          center: [104.114129, 37.550339], // 中国地理中心点
          zoom: 5,//地图缩放比例
          roam: true,
        },
        series: [
          {
            scaleLimit: { //滚轮缩放的极限控制
              min: 0.1,
              max: 0.2
            },
            type: "scatter",
            coordinateSystem: "bmap",
            data: this.convertData(this.data),
            symbol: "pin", // 更改散点的形状
            symbolSize: function (val) {
              if (val[2] > 10000) {
                return 60;
              } else if (val[2] > 1000) {
                return 50;
              } else if (val[2] > 100) {
                return 40;
              } else {
                return 30;
              }
            },
            encode: {
              value: 2,
            },
            itemStyle: {
              color: "#EE2C2C", // 更改散点的颜色
            },
            label: {
              show: true,
              position: "inside",
              formatter: function (param) {
                return param.data.value[3];
              },
              textStyle: {
                color: "white",
                fontSize: 12,
              },
            },
            emphasis: {
              label: {
                show: true,
              },
            },
          },
        ],
        dataZoom: [
          {
            type: 'inside',  // 使用内置型DataZoom
            start: 0,  // 初始缩放起始比例（0 - 1之间）
            end: 1  // 初始缩放结束比例（0 - 1之间）
          },
        ]
      };
      option && this.myChart.setOption(option);
      this.throttledDblClickHandler = _.throttle(this.handleDblClick, 1000);
      this.myChart.on("dblclick", this.throttledDblClickHandler);
      this.myChart.on('mousemove', this.mousemoveHandler)
      // this.myChart.on('mouseout', this.mouseoutHandler)
      this.myChart.on("click", this.clickHandler);
      const bmap = this.myChart.getModel().getComponent('bmap').getBMap();
      // bmap.setMinZoom(5); // 设置最小缩放级别
      // bmap.setMaxZoom(15); // 设置最大缩放级别
      this.throttledZoomendHandler = _.throttle(this.handleZoomend, 1000);
      window.addEventListener("resize", () => {
        this.myChart.resize();
      });
    },
    mousemoveHandler: function (e) {
      this.ListInfo.areaName = e.name
      this.position = {
        top: e.event.event.clientY + 10,
        left: e.event.event.clientX + 10
      }
      const res = JSON.parse(JSON.stringify(e.data))
      res.labels = res.tags.map(item => {
        if (item.seasonOrFestivalTag?.includes('1月1日至2月底')) {
          item.seasonOrFestivalTag = '开学季(春)'
        } else if (item.seasonOrFestivalTag?.includes('7月1日至8月底')) {
          item.seasonOrFestivalTag = '开学季(秋)'
        } else if (item.seasonOrFestivalTag?.includes('（')) {
          item.seasonOrFestivalTag = item.seasonOrFestivalTag.split('（')[0]
        }
        return item.seasonOrFestivalTag
      })
      this.$set(this, 'mouseMoveData', res)
      this.dialogPurchaseVisible = true
    },
    clickHandler(e) {
      this.tags = e.data.tags
      this.dialogPurchaseVisible = true
    },
    handleZoomend(e) {
      const bmap = this.myChart.getModel().getComponent('bmap').getBMap();
      if (this.manyArea) return//如果查询有多个地区就不需要地图的缩放事件
      if (this.eventType == 'dblclick') {//如果是双击事件就不需要地图的缩放事件
        return
      }
      if (bmap.getZoom() < 6) {
        this.ListInfo.areaCode = null
        this.ListInfo.areaName = null
        this.getList();
      }
    },
    handleDblClick(e) {
      this.manyArea = false //如果点击了卡片就需要地图的缩放事件
      if (this.dbClickName == e.name) return
      this.center = {
        lat: e.value[0],
        lng: e.value[1]
      }
      this.eventType = e.type
      this.ListInfo.areaCode = e.data.code
      this.ListInfo.provinceCityDistricts1 = this.$set(this.ListInfo, 'provinceCityDistricts1', [this.findAreaCode(e.data.code)])
      this.ListInfo.areaName = null
      this.getList();
      this.dbClickName = e.name
      setTimeout(() => {
        this.eventType = null
      }, 1000)
    },
    convertData(data) {
      var res = [];
      for (var i = 0; i < data.length; i++) {
        var geoCoord = this.geoCoordMap[data[i].name];
        if (geoCoord) {

          res.push({
            code: data[i].code,
            name: data[i].name,
            value: geoCoord.concat(data[i].value),
            qty: this.formatNum(data[i].qty),
            cost: this.formatNum(data[i].cost),
            orderCount: this.formatNum(data[i].orderCount),
            saleCount: this.formatNum(data[i].saleCount),
            orderCount_Rate: data[i].orderCount_Rate,
            qty_Rate: data[i].qty_Rate,
            saleAount_Rate: data[i].saleAount_Rate,
            cost_Rate: data[i].cost_Rate,
            tags: data[i].tags,
            value_Rate: data[i].value_Rate
          });
        }
      }
      return res;
    },
    formatNum(num) {
      return num ? num.toLocaleString() : 0
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.areaCode = null
      }
      if (this.ListInfo.provinceCityDistricts1 && this.ListInfo.provinceCityDistricts1.length > 0) {
        this.ListInfo.provinceCityDistrict = this.ListInfo.provinceCityDistricts1.map((item) => {
          return item[item.length - 1];
        }).join(',');
      } else {
        this.ListInfo.provinceCityDistrict = null
      }
      this.loading = true;
      try {
        const { data, success } = await getSupplierMapInfos(this.ListInfo);
        if (success) {
          this.$set(this, 'data', data.list);
          this.$set(this, 'geoCoordMap', data.extData.areaDic);
          this.$set(this, 'card', data.extData.cardDic)
          if (type == 'init') {
            this.initCharts()
          } else if (type == 'search' || type == 'cardClick') {
            let bmap;
            let center;
            let mapZoom = 5
            bmap = this.myChart.getModel().getComponent('bmap').getBMap(); // 获取百度地图实例
            center = bmap.getCenter(); // 获取当前中心点
            const res = this.ListInfo.provinceCityDistrict?.split(',')?.length || 0
            const typeRes = data.extData.cardDic.find(item => item.type > 0)
            const index = data.extData.cardDic.findIndex(item => item.type > 0)
            if (this.ListInfo.areaCode) {
              if (typeRes) {
                switch (typeRes.type) {
                  case 1:
                    mapZoom = 8
                    break;
                  case 2:
                    mapZoom = 10
                    break;
                  case 3:
                    mapZoom = 14
                    break;
                }
              } else {
                mapZoom = bmap.getZoom();
              }
              const point = new window.BMap.Point(data.extData.cardDic[index].lng, data.extData.cardDic[index].lat);
              bmap.centerAndZoom(point, mapZoom);
            } else {
              if (res == 1) {
                if (data.extData.cardDic && data.extData.cardDic.length > 0) {
                  const point = new window.BMap.Point(data.extData.cardDic[index].lng, data.extData.cardDic[index].lat);
                  bmap.centerAndZoom(point, mapZoom);
                } else {
                  bmap.centerAndZoom(center, zoom);
                }
              } else if (res > 1) {
                this.manyArea = true
                bmap.centerAndZoom(center, 5);
              } else {
                const zoom = bmap.getZoom(); // 获取当前缩放级别
                bmap.centerAndZoom(center, 5);
              }
            }
            // 更新图表数据
            this.myChart.setOption({
              series: [
                {
                  data: this.convertData(this.data),
                },
              ],
            });
          } else {
            // 更新图表数据
            this.myChart.setOption({
              series: [
                {
                  data: this.convertData(this.data),
                },
              ],
            });
            const zoom = bmap.getZoom(); // 获取当前缩放级别
            bmap.centerAndZoom(center, zoom);
          }
        }
        this.loading = false
      } catch (error) {
        this.loading = false
      }
    },
  },
  beforeDestroy() {
    const bmap = this.myChart.getModel().getComponent("bmap").getBMap();
    bmap.removeEventListener("zoomend", this.zoomHandler);
    this.myChart.off("dblclick");
  },
};
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5px;

  .publicCss {
    width: 200px;
    margin: 0 5px 5px 0px;
  }
}

.cascaderCss ::v-deep .el-input__inner {
  height: 28px !important;
}

::v-deep .el-cascader__search-input {
  margin: 0 0 0 2px;
}

.card {
  padding: 10px;
  position: fixed;
  bottom: 0px;
  right: 10px;
  height: 350px;
  width: 440px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .card_top {
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid #ebeef5;
    margin-bottom: 5px;
    box-sizing: border-box;
    font-weight: 700;
  }

  .card_bottom {
    display: flex;
    flex-wrap: wrap;

    .card_bottom_item {
      display: flex;
      padding: 10px;
      height: 40px;
      box-sizing: border-box;
      justify-content: center;
      align-items: center;
      margin-bottom: 5px;
      outline: 2px solid #ebeef5;
      width: 200px;
      font-weight: 700;
      font-size: 14px;
      cursor: pointer;
      margin-right: 17px;

      &:last-child {
        margin-bottom: 0px;
      }

      &:nth-child(2n) {
        margin-right: 0px;
      }
    }
  }
}

.tooltip {
  border: 1px solid red;
  min-width: 140px;
  max-width: 300px;
  padding: 10px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.tooltipTop {
  margin-top: 5px;
}
</style>
