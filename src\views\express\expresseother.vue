<template>
  <container>
       <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='true'
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry'
              @summaryClick='onsummaryClick' :tablekey='tablekey' :loading="listLoading"/>
      <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
     </template>
      <!-- 系列编码趋势图 -->
      <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
      <span>
      <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
      <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
      </el-dialog>
  </container>
</template>
<script>
import {batchDeleteExpressFreightAP,pageExpressFreightAP} from '@/api/express/express'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import {formatWarehouse,formatTime,formatYesornoBool} from "@/utils/tools";
import buschar from '@/components/Bus/buschar'
import { getAnalysisCommonResponse } from '@/api/admin/common'
const tableCols =[
      {istrue:true,prop:'warehouse',label:'仓库', width:'80',sortable:'custom',formatter:(row)=>formatWarehouse(row.warehouse)},
      {istrue:true,prop:'expressCompanyName',label:'快递公司', width:'90'},
      {istrue:true,prop:'billnumber',label:'快递编号', width:'130'},
      {istrue:true,prop:'apSmallTypestr',label:'类别', width:'100'},
      {istrue:true,summaryEvent: true,prop:'apAmount',label:'金额', width:'100',sortable:'custom'},
      {istrue:true,prop:'isBindBill',label:'是否匹配', width:'120',sortable:'custom',formatter:(row)=>formatYesornoBool(row.isBindBill)},
      {istrue:true,prop:'settDate',label:'结算时间', width:'150',sortable:'custom',formatter:(row)=>formatTime(row.settDate,'YYYY-MM-DD HH:mm:ss')},
      {istrue:true,prop:'createdTime',label:'导入时间', width:'150',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD HH:mm:ss')},
      {istrue:true,prop:'remark',label:'原因', width:'120'},
      {istrue:true,prop:'batchNumber',label:'批次号', width:'160',sortable:'custom'}
     ];
const tableHandles=[
        {label:"批量删除", handle:(that)=>that.onbacthDelete()},
        {label:"批次号删除", handle:(that)=>that.onDelete()},
        {label:"刷新", handle:(that)=>that.getlist()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, container,buschar },
  props:{
      filter: { },
      tablekey: { type: String, default:'' },
     },
  data() {
    return {
      that:this,
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"id",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [], 
      listLoading: false,
      pageLoading: false,
      analysisFilter: {
        searchName: "ExpressFreightAPRecode",
        isYearMonthDay:true,
        isTimeFormat:true,
        extype: 2,
        selectColumn: "",
        filterTime: "createdTime",
        filter: null,
        columnList: [],
      },
      buscharDialog: { visible: false, title: "", data: [] },
    }
  },
  mounted() {
     this.onSearch()
  },
  beforeUpdate() {
    //console.log('update')
  },
  methods: {
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      this.filter.aPBigType=5
      this.filter.startTime = null;
      this.filter.endTime = null;
      this.filter.startImpotTime = null;
      this.filter.endImpotTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      if (this.filter.timerange1) {
        this.filter.startImpotTime = this.filter.timerange1[0];
        this.filter.endImpotTime = this.filter.timerange1[1];
      }
      const params = {
        ...pager,
        ...this.pager,
        ... this.filter
      }
      this.listLoading = true
      const res = await pageExpressFreightAP(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async onbacthDelete() {
      if (this.selids.length==0) {
       this.$message({type: 'warning',message: '请先选择!'});
       return;
      } 
      this.$confirm('确认删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
            const res = await batchDeleteExpressFreightAP({ids:this.selids.join()})
            if (!res?.success) {return }
            this.$message({type: 'success',message: '删除成功!'});
            this.getlist()
        }).catch(() => {
          this.$message({type: 'info',message: '已取消删除'});
        });
    },
    async onDelete(){
      this.$emit('onDelete',5);
   },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
    async onsummaryClick(property) {
      this.analysisFilter.columnList = [];
      this.analysisFilter.filter = null;
      let that = this;
      let summaryEventList = this.tableCols.filter(f => f.summaryEvent);
      summaryEventList.forEach(element => {
        this.analysisFilter.columnList.push({ columnNameCN: element.label, columnNameEN: element.prop });
      });

     
      this.analysisFilter.filter = {
        aPBigType: [this.filter.aPBigType,0],
        billnumber:[this.filter.billnumber,0],
        warehouse: [this.filter.warehouse, 0],
        companyId: [this.filter.companyId, 0],
      }
      if (this.filter.timerange) {
        this.analysisFilter.filter.settDate =  [this.filter.timerange[0], this.filter.timerange[1],0];
      }
      if (this.filter.timerange1) {
        this.analysisFilter.filter.createdTime =  [this.filter.timerange1[0], this.filter.timerange1[1],0];
      }
      this.analysisFilter.selectColumn = property;
      const res = await getAnalysisCommonResponse(that.analysisFilter).then(res => {
        that.buscharDialog.visible = true;
        that.buscharDialog.data = res.data
        that.buscharDialog.title = res.data.legend[0]
      });
    },
  }
}
</script>
