<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.buildDocId" :maxlength="40" clearable placeholder="建编码ID"
                            style="width:180px;" @keyup.enter.native="onSearch" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.goodsCompeteId" :maxlength="40" clearable placeholder="竞品ID"
                            style="width:180px;" @keyup.enter.native="onSearch" />
                    </el-button>
                    <el-button type="primary" @click="onSearch" style="margin-left: 5px;">查 询</el-button>
                </el-button-group>
            </el-form>
        </template>
        <div style="height: 500px;">
             <!--列表-->
            <ces-table ref="table" :that='that' :isIndex='true' :isSelectColumn='false' :tableData='list' :tableCols='tableCols'
                rowkey="id">
            </ces-table>
        </div>

        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;padding-top:5px;">
                    <el-button @click="onClose">关闭</el-button>
                </el-col>
            </el-row>
        </template>
    </my-container>
</template>
<script>



import cesTable from "@/components/Table/table.vue";
import { formatTime,formatLinkProCode, formatWarehouse, sendWarehouse4HotGoodsBuildGoodsDocList } from "@/utils/tools";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import {
    GetHotSaleGoodsBuildGoodsRejectRecords,
} from '@/api/operatemanage/productalllink/alllink';


const tableCols = [
    { istrue: true, prop: 'instanceId', label: '流程编码', width: '80', sortable: true },
    { istrue: true, prop: 'hotSaleGoodsBuildGoodsDocId', label: '建编码ID', width: '180' , sortable: true},
    { istrue: true, prop: 'goodsCompeteId', label: '竞品ID', width: '170'},

    { istrue: true, prop: 'applyUserName', label: '申请人', width: '70', sortable: true },
    { istrue: true, prop: 'applyTime', label: '申请时间', width: '150', sortable: true, formatter: (row) => row.applyTime == null ? null : formatTime(row.applyTime, 'YYYY-MM-DD HH:mm:ss') },


    { istrue: true, prop: 'rejectTime', label: '审核时间', width: '150', sortable: true, formatter: (row) => row.rejectTime == null ? null : formatTime(row.rejectTime, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'rejectUserName', label: '审核人', width: '70', sortable: true },
    { istrue: true, prop: 'rejectReason', label: '审批拒绝原因', sortable: true }
];


export default {
    name: "YhGoodsRelationForm",
    components: { MyContainer, MyConfirmButton, cesTable, },
    data() {
        return {
            that: this,

            filter: {
                buildDocId: null,
                goodsCompeteId: "",
            },
            tableCols: tableCols,
            list: [],
            total: 0,
            pageLoading: false,
            formEditMode: true,//是否编辑模式
            mode: 1,
        };
    },
    async mounted() {

    },
    computed: {
    },
    methods: {
        onClose() {
            this.$emit('close');
        },
        async loadData({ buildDocId, goodsCompeteId }) {
            this.filter.buildDocId = buildDocId;
            this.filter.goodsCompeteId = goodsCompeteId;
            await this.onSearch();
        },
        async onSearch() {
            this.pageLoading = true;

            let rlt = await GetHotSaleGoodsBuildGoodsRejectRecords({
                buildDocId: this.filter.buildDocId,
                goodsCompeteId: this.filter.goodsCompeteId
            });
            if (rlt && rlt.success) {
                this.list = rlt.data;
            }
            this.pageLoading = false;
        }
    },
};
</script>
