<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <!-- <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker> -->
        <el-select filterable clearable v-model="ListInfo.shopCode" placeholder="店铺" class="publicCss">
          <el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-input v-model.trim="ListInfo.sku" placeholder="SKU" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.styleCode" placeholder="系列编码" maxlength="50" clearable class="publicCss" />
        <el-select filterable v-model="ListInfo.groupId" clearable placeholder="运营组" class="publicCss">
          <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select filterable v-model="ListInfo.operateSpecialUserId" clearable placeholder="运营专员" class="publicCss">
          <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select filterable v-model="ListInfo.userId" clearable placeholder="运营助理" class="publicCss">
          <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select class="publicCss" v-model="ListInfo.bzCategory" placeholder="经营大类" :collapse-tags="true" remote
          :remote-method="remoteMethodBusinessCategory" clearable filterable>
          <el-option v-for="(item, i) in filterList.bussinessCategoryNames" :key="'bussinessCategoryNames' + i + 1"
            :label="item" :value="item" />
        </el-select>
        <el-select class="publicCss" v-model="ListInfo.bzCategory1" placeholder="一级类目" :collapse-tags="true" remote
          :remote-method="remoteMethodCategoryName1s" clearable filterable>
          <el-option v-for="(item, i) in filterList.categoryName1s" :key="'categoryName1Level' + i + 1" :label="item"
            :value="item" />
        </el-select>
        <el-select class="publicCss" v-model="ListInfo.bzCategory2" placeholder="二级类目" :collapse-tags="true" remote
          :remote-method="remoteMethodCategoryName2s" clearable filterable>
          <el-option v-for="(item, i) in filterList.categoryName2s" :key="'categoryName2Level' + i + 1" :label="item"
            :value="item" />
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="onAddMethod">新增</el-button>
        <el-button type="success" @click="startImport">导入</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
        <el-button type="primary" v-if="checkPermission('SKUProductMaintenanceBatchModify')" @click="bulkEdit">批量修改</el-button>
      </div>
    </template>
    <vxetablebase :id="'productMaintenanceSku202411282239'" :tablekey="'productMaintenanceSku202411282239'" ref="table"
      :that='that' :isIndex='true' @select='selectchange' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="70">
          <template #default="{ row, $index }">
            <div style="display: flex">
              <el-button type="text" @click="onEdit(row)">编辑</el-button>
              <el-button type="text" style="color: red;" @click="onDelete(row)">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="ruleTitle" :visible.sync="newEditVisible" width="35%" v-dialogDrag @close="editClose">
      <div style="padding: 15px 5px 0 5px;">
        <el-form :model="ruleForm" :rules="editrules" ref="ruleForm" label-width="110px" class="demo-ruleForm">
          <el-row>
            <el-col :span="12">
              <el-form-item label="SKU" prop="shopGoodsCode">
                <el-input v-model.trim="ruleForm.shopGoodsCode" placeholder="请输入SKU" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="系列编码" prop="styleCode">
                <el-input v-model.trim="ruleForm.styleCode" placeholder="请输入系列编码" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="店铺" prop="shopCode">
                  <el-select v-model="ruleForm.shopCode" clearable filterable placeholder="请选择店铺"
                  class="editCss">
          <el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品名称" prop="productName">
                <el-input v-model.trim="ruleForm.productName" placeholder="请输入产品名称" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="运营专员" prop="operateSpecialUserId">
                <el-select v-model="ruleForm.operateSpecialUserId" clearable filterable placeholder="请选择运营专员"
                  class="editCss">
                  <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="运营助理" prop="userId">
                <el-select v-model="ruleForm.userId" clearable filterable placeholder="请选择运营助理" class="editCss">
                  <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="小组" prop="operateSpecialUserId">
                <el-select v-model="ruleForm.groupId" clearable filterable placeholder="请选择小组" class="editCss">
                  <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="经营大类" prop="bzCategory">
                <el-select class="editCss" v-model="ruleForm.bzCategory" placeholder="请选择经营大类" :collapse-tags="true"
                  remote :remote-method="remoteMethodBusinessCategory" clearable filterable>
                  <el-option v-for="(item, i) in filterList.bussinessCategoryNames"
                    :key="'bussinessCategoryNames' + i + 1" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="一级类目" prop="bzCategory1">
                <el-select class="editCss" v-model="ruleForm.bzCategory1" placeholder="请选择一级类目" :collapse-tags="true"
                  remote :remote-method="remoteMethodCategoryName1s" clearable filterable>
                  <el-option v-for="(item, i) in filterList.categoryName1s" :key="'categoryName1Level' + i + 1"
                    :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="二级类目" prop="bzCategory2">
                <el-select class="editCss" v-model="ruleForm.bzCategory2" placeholder="请选择二级类目" :collapse-tags="true"
                  remote :remote-method="remoteMethodCategoryName2s" clearable filterable>
                  <el-option v-for="(item, i) in filterList.categoryName2s" :key="'categoryName2Level' + i + 1"
                    :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否刷单" prop="isSd">
                <el-select class="editCss" v-model="ruleForm.isSd" placeholder="请选择是否刷单" :collapse-tags="true"
                  clearable filterable>
                  <el-option   label="是" value="1" />
                  <el-option   label="否" value="0" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="newEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="onStorageMethodDebounced" :loading="btnLoading">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 批量修改 -->
    <el-drawer title="批量修改" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
      :visible.sync="batchEditFormVisible" direction="btt" size="'auto'" class="el-drawer__wrapper"
      style="position:absolute;">
      <el-form :model="batchEditFormData" label-width="100px" ref="batchEditForm" label-position="right">
        <!-- <el-row>
          <el-col :span="6">
            <el-form-item label="SKU" prop="shopGoodsCode">
              <el-col :span="16">
                <el-input style="float: left;" placeholder="请输入SKU" clearable v-model="batchEditFormData.shopGoodsCode"></el-input>
              </el-col>
              <el-col :span="8">
                <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.shopGoodsCode_Checked">
                  <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
                </el-checkbox>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="系列编码" prop="styleCode">
              <el-col :span="16">
                <el-input style="float: left;" placeholder="请输入系列编码" clearable v-model="batchEditFormData.styleCode"></el-input>
              </el-col>
              <el-col :span="8">
                <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.styleCode_Checked">
                  <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
                </el-checkbox>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="店铺" prop="shopId">
              <el-select style="float: left;" filterable v-model="batchEditFormData.shopId" clearable placeholder="请选择店铺">
                <el-option v-for="item in shopList" :key="item.value" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
              <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.shopId_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="产品名称" prop="productName">
              <el-col :span="16">
                <el-input style="float: left;" placeholder="请输入产品名称" clearable v-model="batchEditFormData.productName"></el-input>
              </el-col>
              <el-col :span="8">
                <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.productName_Checked">
                  <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
                </el-checkbox>
              </el-col>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col :span="6">
            <el-form-item label="小组" prop="groupId">
              <el-select style="float: left;" filterable clearable v-model="batchEditFormData.groupId"
                placeholder="请选择小组">
                <el-option v-for="item in grouplist" :key="item.value" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
              <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.groupId_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="运营专员" prop="operateSpecialUserId">
              <el-select style="float: left;" v-model="batchEditFormData.operateSpecialUserId" clearable filterable placeholder="请选择运营专员">
                <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.operateSpecialUserId_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="运营助理" prop="userId">
              <el-select style="float: left;" filterable clearable v-model="batchEditFormData.userId"
                placeholder="请选择运营助理">
                <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
              <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.userId_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="6">
            <el-form-item label="经营大类" prop="bussinessCategoryNames">
              <el-col :span="16">
                <el-input placeholder="请输入经营大类" style="float: left;" clearable v-model="batchEditFormData.bussinessCategoryNames"></el-input>
              </el-col>
              <el-col :span="8">
                <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.bussinessCategoryNames_Checked">
                  <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
                </el-checkbox>
              </el-col>
            </el-form-item>
          </el-col> -->
        </el-row>
        <!-- <el-row>
          <el-col :span="8">
            <el-form-item label="一级类目" prop="bzCategory1">
              <el-col :span="16">
                <el-input v-model="batchEditFormData.bzCategory1"></el-input>
              </el-col>
              <el-col :span="8">
                <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.bzCategory1_Checked">
                  <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
                </el-checkbox>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="二级类目" prop="bzCategory2">
              <el-col :span="16">
                <el-input v-model="batchEditFormData.bzCategory2"></el-input>
              </el-col>
              <el-col :span="8">
                <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.bzCategory2_Checked">
                  <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
                </el-checkbox>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否刷单" prop="isSd"> 
              <el-select filterable clearable v-model="batchEditFormData.isSd" placeholder="请选择">
                <el-option label="是" value="1"></el-option>
                <el-option label="否" value="0"></el-option>
              </el-select>
              <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.isSd_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
        </el-row> -->
      </el-form>

      <div class="drawer-footer" style="position: relative">
        <el-button @click.native="batchEditFormVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="batchEditFormLoading" @click="batchEditFormSubmit" />
      </div>
    </el-drawer>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions ,formatPlatform} from '@/utils/tools'
import MyConfirmButton from '@/components/my-confirm-button'
import { getBusinessCategorySelectData } from '@/api/operatemanage/base/category'
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop'
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { importSKUShopGoodsCode_JingDongSelfSupportsync, getSKUShopGoodsCode_JingDongSelfSupport, addorEditSKUShopGoodsCode_JingDongSelfSupport, bulkEditSKUShopGoodsCode_JingDongSelfSupport, exportSKUShopGoodsCode_JingDongSelfSupport, deleteSKUShopGoodsCode_JingDongSelfSupportAsync } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
const tableCols = [
  { type:'checkbox',label:'' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopGoodsCode', label: 'SKU', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'platform', label: '平台', formatter: (row) => formatPlatform(row.platform), type: 'custom'},
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'isSd', label: '是否刷单',formatter: (row) => row.isSd == 0 ?"否" :"是" , },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '系列编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'productName', label: '产品名称', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'groupName', label: '小组', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'operateSpecialUserName', label: '专员', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'userName', label: '助理', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'bzCategory', label: '经营大类', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'bzCategory1', label: '一级类目', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'bzCategory2', label: '二级类目', },
]
export default {
  name: "productMaintenanceSku",
  components: {
    MyContainer, vxetablebase, MyConfirmButton
  },
  data() {
    return {
      dialogVisible: false,
      btnLoading:false,
      ruleTitle: '新增',
      ruleForm: {
        shopGoodsCode: null,
        styleCode: null,
        shopName: null,
        productName: null,
        operateSpecialUserId: null,
        userId: null,
        groupId: null,
        bzCategory: null,
        bzCategory1: null,
        bzCategory2: null,
        isSd:'0',
      },
      editrules: {
        shopGoodsCode: [{ required: true, message: '请输入SKU', trigger: 'blur' }],
        styleCode: [{ required: true, message: '请输入系列编码', trigger: 'blur' }],
        shopName: [{ required: true, message: '请输入店铺', trigger: 'blur' }],
        productName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        operateSpecialUserId: [{ required: true, message: '请选择运营专员', trigger: 'blur' }],
       // userId: [{ required: true, message: '请选择运营助理', trigger: 'blur' }],
        groupId: [{ required: true, message: '请选择小组', trigger: 'blur' }],
        bzCategory: [{ required: true, message: '请选择经营大类', trigger: 'blur' }],
        bzCategory1: [{ required: true, message: '请选择一级类目', trigger: 'blur' }],
        bzCategory2: [{ required: true, message: '请选择二级类目', trigger: 'blur' }],
       // isSd: [{ required: true, message: '请选择是否刷单', trigger: 'blur' }],
      },
      batchEditFormData: {
        ids: "",
        // shopGoodsCode: null,
        // shopGoodsCode_Checked: null,
        // styleCode: null,
        // styleCode_Checked: null,
        // shopId: null,
        // shopId_Checked: null,
        // productName: null,
        // productName_Checked: null,
        groupId: null,
        groupName: null,
        groupId_Checked: false,
        operateSpecialUserId: null,
        operateSpecialUserName: null,
        operateSpecialUserId_Checked: false,
        userId: null,
        userName: null,
        userId_Checked: false,
        // bussinessCategoryNames: null,
        // bussinessCategoryNames_Checked: null,
        // bzCategory1: null,
        // bzCategory1_Checked: null,
        // bzCategory2: null,
        // bzCategory2_Checked: null,
        // isSd: null,
        // isSd_Checked: null
      },
      selids: [],
      newEditVisible: false,//新增编辑弹窗
      dialogVisible: false,
      uploadLoading: false,
      batchEditFormVisible: false,
      batchEditFormLoading: false,
      fileList: [],
      fileparm: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startUseDate: null,//开始时间
        endUseDate: null,//结束时间
        shopCodeList: [],
        sku: null,
        styleCode: null,
        groupId: null,
        operateSpecialUserId: null,
        userId: null,
        bzCategory: null,
        bzCategory1: null,
        bzCategory2: null,
      },
      shopList: [],
      grouplist: [],
      directorlist: [],
      filterList: {
        bussinessCategoryNames: [],
        categoryName1s: [],
        categoryName2s: []
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
    this.getShopList()
  },
  methods: {
    editClose() {
      this.filterList = {
        bussinessCategoryNames: [],
        categoryName1s: [],
        categoryName2s: []
      }
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();//重置表单
        this.$refs.ruleForm.clearValidate();//清除验证
      });
      this.newEditVisible = false
    },
    async exportProps() {
      this.loading = true
      const { data } = await exportSKUShopGoodsCode_JingDongSelfSupport(this.ListInfo)
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    //防抖
    onStorageMethodDebounced: _.debounce(function (param) {
      this.onSingleSave(param);
    }, 1000),
    onSingleSave() {
      
    this.$refs.ruleForm.validate(async (valid) => {
      if (valid) {
        this.ruleForm.userName = this.directorlist.find(x=>x.value==this.ruleForm.userId).label
        this.ruleForm.operateSpecialUserName = this.directorlist.find(x=>x.value==this.ruleForm.operateSpecialUserId).label
        this.ruleForm.groupName = this.grouplist.find(x=>x.value==this.ruleForm.groupId).label
        if(this.ruleForm.shopCode)
        this.ruleForm.shopName = this.shopList.find(x=>x.value==this.ruleForm.shopCode).label
        this.btnLoading = true;
        const { success } = await addorEditSKUShopGoodsCode_JingDongSelfSupport(this.ruleForm)
        if (success) {
            this.$message.success('操作成功')
            this.newEditVisible = false
            this.getList()
            this.btnLoading = false;
          } else {
            this.$message.error('操作失败')
            this.btnLoading = false;
          }
        }
      })
    },
    onCleardataMethod() {
      this.ruleForm = {
        shopGoodsCode: null,
        styleCode: null,
        shopName: null,
        productName: null,
        operateSpecialUserId: null,
        userId: null,
        groupId: null,
        bzCategory: null,
        bzCategory1: null,
        bzCategory2: null,
        isSd:'0'
      }
    },
    onAddMethod() {
      this.onCleardataMethod()
      this.ruleTitle = '新增'
      this.newEditVisible = true
    },
    async onDelete(row) {
      this.$confirm('是否删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await deleteSKUShopGoodsCode_JingDongSelfSupportAsync({ id: row.id })
        if (success) {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      });
    },
    async onEdit(row) {
      this.onCleardataMethod()
      setTimeout(() => {
        this.ruleForm = JSON.parse(JSON.stringify(row))
        this.ruleTitle = '编辑'
        this.ruleForm.userId = this.ruleForm.userId ? this.ruleForm.userId.toString() : ''
        this.ruleForm.operateSpecialUserId = this.ruleForm.operateSpecialUserId ? this.ruleForm.operateSpecialUserId.toString() : ''
        this.ruleForm.groupId = this.ruleForm.groupId ? this.ruleForm.groupId.toString() : ''
        this.ruleForm.isSd = this.ruleForm.isSd ? this.ruleForm.isSd.toString() : ''
        this.newEditVisible = true
      }, 100)
    },
    remoteMethodBusinessCategory(query) {
      setTimeout(async () => {
        const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 1, categoryName: query })
        this.filterList.bussinessCategoryNames = res.data
      }, 200)
    },
    remoteMethodCategoryName1s(query) {
      setTimeout(async () => {
        const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 2, categoryName: query })
        this.filterList.categoryName1s = res.data
      }, 200)
    },
    remoteMethodCategoryName2s(query) {
      setTimeout(async () => {
        const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 3, categoryName: query })
        this.filterList.categoryName2s = res.data
      }, 200)
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("dataType", 1);
      var res = await importSKUShopGoodsCode_JingDongSelfSupportsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.startUseDate = e ? e[0] : null
      this.ListInfo.endUseDate = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getSKUShopGoodsCode_JingDongSelfSupport(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    async bulkEdit(){
      if(this.selids.length == 0){
        this.$message.error('请选择数据！');
        return;
      }
      this.batchEditFormData.ids = this.selids.join()
      this.batchEditFormVisible = true
    },
    async batchEditFormSubmit() {
      this.batchEditFormLoading = true;
      this.batchEditFormData.userName = !this.batchEditFormData.userId ? null : this.directorlist.find(x=>x.value==this.batchEditFormData.userId).label
      this.batchEditFormData.operateSpecialUserName = !this.batchEditFormData.operateSpecialUserId ? null : this.directorlist.find(x=>x.value==this.batchEditFormData.operateSpecialUserId).label
      this.batchEditFormData.groupName = !this.batchEditFormData.groupId ? null : this.grouplist.find(x=>x.value==this.batchEditFormData.groupId).label
      const { success } = await bulkEditSKUShopGoodsCode_JingDongSelfSupport(this.batchEditFormData)
      if (success) {
          this.$message.success('操作成功')
           this.batchEditFormVisible = false
          this.getList()
          this.batchEditFormLoading = false;
          this.batchEditFormData = {
            ids: '',
            groupId: null,
            groupName: null,
            groupId_Checked: false,
            operateSpecialUserId: null,
            operateSpecialUserName: null,
            operateSpecialUserId_Checked: false,
            userId: null,
            userName: null,
            userId_Checked: false,
          }
        } else {
          this.$message.error('操作失败')
          this.batchEditFormLoading = false;
        }
    },
    async getShopList() {
      const res1 = await getAllShopList({ platforms: [74,5] });
      this.shopList = res1.data?.map(item => { return { value: item.shopCode, label: item.shopName }; });
      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 130px;
    margin-right: 5px;
  }
}

.editCss {
  width: 80%;
}
</style>
