<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height: 93%;">
            <el-tab-pane label="趋势图" name="tab0" style="height: 100%;">
                <HotSaleBrandPushNewSale1_2 ref="HotSaleBrandPushNewSale1_2" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="明细" name="tab1" style="height: 100%;">
                <HotSaleBrandPushNewSale1 ref="HotSaleBrandPushNewSale1" style="height: 100%;" />
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";

import HotSaleBrandPushNewSale1 from '@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewSale1';
import HotSaleBrandPushNewSale1_2 from '@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewSale1_2';

export default {
    name: "HotSaleBrandPushNewSale1main",
    components: {
        MyContainer,
        HotSaleBrandPushNewSale1, HotSaleBrandPushNewSale1_2
    },
    data() {
        return {
            that: this,
            pageLoading: '',
            activeName: 'tab0',
            filter: {
                timerange: [],
                saleStartDate: null,
                saleEndDate: null,
                createdUserId: 999999999,
                createdUserName: "",
                styleCodeCount: 0,
            },
        };
    },
    mounted() {
    },
    methods: {
        async loadData(args) {
            this.filter.createdUserId = args.createdUserId;
            this.filter.createdUserName = args.createdUserName;
            this.filter.styleCodeCount = args.styleCodeCount;
            this.filter.timerange = args.timerange;
            this.isSum = args.isSum;

            this.filter.createdUserNames = args.createdUserNames;
            this.filter.createUserAreas = args.createUserAreas;
            this.filter.createUserRoles = args.createUserRoles;
            this.filter.createUserDeptNames = args.createUserDeptNames;
            console.log(this.filter, "this.filter");

            this.$refs.HotSaleBrandPushNewSale1.onSearch(this.filter);
            this.$refs.HotSaleBrandPushNewSale1_2.onSearch(this.filter);
        },
    },

};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
