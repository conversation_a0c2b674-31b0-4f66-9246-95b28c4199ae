<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->

    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :tablekey='tablekey'
      :tableData='adflowlist' @select='selectchange' :isSelection='false'
      :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableCols='tableCols' :loading="listLoading">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <template slot='extentbtn'>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model="Filter.ShopName" placeholder="店铺" style="width:120px;" clearable/>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model="Filter.AccountId" placeholder="账户快手id" style="width:120px;" clearable/>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model="Filter.AdvertiserId" placeholder="广告主账户id" style="width:120px;" clearable/>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-date-picker style="width:280px" v-model="Filter.UseDate" type="datetimerange"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>

          <el-button type="primary" @click="onImportSyj">导入</el-button>
        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getKuaiShouAdFlowList" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisibleSyj" width="30%">
      <span>
        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传
          </my-confirm-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
      <span>
        <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>

import {importKuaiShouAdFlowAsync,getKuaiShouAdFlowList,deleteKuaiShouAdFlowBatch,queryAdFlowAnalysisAsync } from '@/api/financial/yyfyday'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar'
const tableCols =[
      {istrue:true,prop:'useDate',label:'日期', width:'200',sortable:'custom', formatter: (row) => formatTime(row.useDate, 'YYYY-MM-DD') },
      {istrue:true,prop:'accountId',label:'快手账户id', width:'200',sortable:'custom'},
      {istrue:true,prop:'advertiserId',label:'广告主账户id', width:'200',sortable:'custom'},
      {istrue:true,prop:'plannedCost',label:'计划花费', width:'200',sortable:'custom'},
      {istrue:true,prop:'rechargeCost',label:'充值花费', width:'200',sortable:'custom'},
      {istrue:true,prop:'frameRebateCost',label:'框返花费', width:'200',sortable:'custom'},
      {istrue:true,prop:'incentiveCost',label:'激励花费', width:'200',sortable:'custom'},
      {istrue:true,prop:'eCommerceCost',label:'电商花费', width:'200',sortable:'custom'},
      {istrue:true,prop:'rechargeTransferIn',label:'充值转入', width:'200',sortable:'custom'},
      {istrue:true,prop:'frameRebateTransferIn',label:'框返转入', width:'200',sortable:'custom'},
      {istrue:true,prop:'incentiveTransferIn',label:'激励转入', width:'200',sortable:'custom'},
      {istrue:true,prop:'eCommerceTransferIn',label:'电商转入', width:'200',sortable:'custom'},
      {istrue:true,prop:'totalTransferIn',label:'总转入', width:'200',sortable:'custom'},
      {istrue:true,prop:'rechargeTransferOut',label:'充值转出', width:'200',sortable:'custom'},
      {istrue:true,prop:'frameRebateTransferOut',label:'框返转出', width:'200',sortable:'custom'},
      {istrue:true,prop:'incentiveTransferOut',label:'激励转出', width:'200',sortable:'custom'},
      {istrue:true,prop:'eCommerceTransferOut',label:'电商转出', width:'200',sortable:'custom'},
      {istrue:true,prop:'totalTransferOut',label:'总转出', width:'200',sortable:'custom'},
      {istrue:true,prop:'orderPayment',label:'订单支付', width:'200',sortable:'custom'},
      {istrue:true,prop:'orderRefund',label:'订单退款', width:'200',sortable:'custom'},
      {istrue:true,prop:'brandPayment',label:'品牌支付', width:'200',sortable:'custom'},
      {istrue:true,prop:'brandRefund',label:'品牌退款', width:'200',sortable:'custom'},
      {istrue:true,prop:'dailyClosingBalance',label:'日终结余', width:'200',sortable:'custom'},
      {istrue:true,prop:'shopName',label:'店铺名', width:'200',sortable:'custom'},
      {istrue:true,prop:'createdTime',label:'导入时间', width:'200',sortable:'custom'},
      {istrue:true,prop:'batchNumber',label:'导入批次', width:'200',sortable:'custom'},
      {istrue: true,type: "button",width: "430",label: '操作', btnList: [{ label: "删除批次", handle: (that, row) => that.deleteBatch(row)}]}
     ];

const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

export default {
  name: "Users",
  props: {
    tablekey: { type: String, default:'' }
  },
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, buschar},
  data() {
    return {
      that:this,
      Filter: {
        UseDate:[startDate, endDate],
      },
      shopList:[],
      userList:[],
      groupList:[],
      adflowlist: [],
      tableCols:tableCols,
      total: 0,
      summaryarry:{},
      pager:{OrderBy:"id",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
      platform:0,
      buscharDialog:{
        visible:false,
        data:[],
        title:''
      }
    };
  },
  async mounted() {
  },
  methods: {
    async onsummaryClick(property) {
      this.Filter.startUseDate = null;
        this.Filter.endUseDate = null;
      if (this.Filter.UseDate) {
        this.Filter.startUseDate = this.Filter.UseDate[0];
        this.Filter.endUseDate = this.Filter.UseDate[1];
      }
      let pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.Filter };
      params.column = property;
      let that = this;
      await queryAdFlowAnalysisAsync(params).then(res => {
        that.buscharDialog.visible = true;
        that.buscharDialog.data = res.data;
        that.buscharDialog.title = res.data.legend[0];
      });
    },
    async deleteBatch(row) {
      var that = this;
      this.$confirm("此操作将删除此批次导入快手推广流水数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          await deleteKuaiShouAdFlowBatch({ batchNumber: row.batchNumber })
          that.$message({ message: '已删除', type: "success" });
          that.onRefresh()

        });

    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onImportSyj() {
      this.dialogVisibleSyj = true
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      const res =await importKuaiShouAdFlowAsync(form);
      if(res?.success){
         this.$message({ message: '上传成功,正在导入中...', type: "success" });
         this.dialogVisibleSyj = false;
      }
    },
    async uploadSuccess2(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
    },
    async onSubmitupload2() {
      this.$refs.upload2.submit()
    },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getKuaiShouAdFlowList();
    },
    async getKuaiShouAdFlowList() {
      this.Filter.startUseDate = null;
        this.Filter.endUseDate = null;
      if (this.Filter.UseDate) {
        this.Filter.startUseDate = this.Filter.UseDate[0];
        this.Filter.endUseDate = this.Filter.UseDate[1];
      }
      const para = { ...this.Filter };
      let pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,

      };

      console.log(para)

      this.listLoading = true;
      const res = await getKuaiShouAdFlowList(params);
      console.log(res)
      this.listLoading = false;
      console.log(res.data.list)
      //console.log(res.data.summary)

      this.total = res.data.total
      this.adflowlist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
