<template>
    <MyContainer>
      <template #header>
        <div class="top">
          <div class="publicCss" style="width: 250px;">
            <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" :clearable="false"
                start-placeholder="评价开始时间" end-placeholder="评价结束时间" :picker-options="pickerOptions"
                style="width: 250px;margin-right: 2px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
            </el-date-picker>
          </div>
          <div class="publicCss" style="width: 155px;">
            <!-- {{ListInfo.proCodeArr}}=
            {{ListInfo.proCode}} -->

            <inputYunhan ref="productstyleCode" :inputt.sync="ListInfo.proCode" v-model="ListInfo.proCode"
              width="155px" placeholder="ID/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
              :maxlength="2000" @callback="callbackGoodsCode($event, 'proCode')" title="ID">
            </inputYunhan>
          </div>
          <div class="publicCss" style="width: 155px;">
            <inputYunhan ref="productproductName" :inputt.sync="ListInfo.orderNo" v-model="ListInfo.orderNo"
              width="155px" placeholder="订单编号/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
              :maxlength="2000" @callback="callbackGoodsCode($event, 'orderNo')" title="订单编号">
            </inputYunhan>
          </div>
          <!-- <div class="publicCss" style="width: 175px;">
            <inputYunhan ref="productyhGoodsCode" :inputt.sync="ListInfo.yhGoodsCode" v-model="ListInfo.yhGoodsCode"
              width="175px" placeholder="公司商品编码/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
              :maxlength="2000" @callback="callbackGoodsCode($event, 'yhGoodsCode')" title="公司商品编码">
            </inputYunhan>
          </div> -->
          <el-select v-model="ListInfo.cmtStartLevel" collapse-tags placeholder="用户评价分" multiple clearable filterable class="publicCss"
            style="width: 150px;">
            <el-option v-for="item in categoryList" :key="item" :label="item" :value="item&&item.substring(0, 1)" />
          </el-select>

          <div class="publicCss" style="width: 155px;">
            <inputYunhan ref="productproductName" :inputt.sync="ListInfo.cmtKeyWords" v-model="ListInfo.cmtKeyWords"
              width="155px" placeholder="评价关键字/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
              :maxlength="2000" @callback="callbackGoodsCode($event, 'cmtKeyWords')" title="评价关键字">
            </inputYunhan>
          </div>

          <!-- <el-input v-model.trim="ListInfo.packagingSpecification" placeholder="包装" maxlength="50" clearable
            class="publicCss" style="width: 95px;" />
          <el-input v-model.trim="ListInfo.material" placeholder="材质" maxlength="100" clearable class="publicCss"
            style="width: 95px;" />
          <el-select v-model="ListInfo.isPriceControl" placeholder="是否控价" clearable class="publicCss"
            style="width: 90px;">
            <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-model="ListInfo.platforms" placeholder="平台" multiple collapse-tags clearable filterable
            class="publicCss" style="width: 155px;">
            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-model="ListInfo.groupIds" collapse-tags clearable placeholder="运营组" multiple filterable
            class="publicCss" style="width: 155px;">
            <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-model="ListInfo.dDUserIds" placeholder="请模糊输入并选择选品人" clearable filterable remote
            :filter-method="(query) => searchReferrer(query)" value-key="value" multiple collapse-tags
            :reserve-keyword="false" class="publicCss" style="width: 165px;">
            <el-option v-for="item in contactPersonList"
              :key="'userSelector' + item.value + item.ddUserId + item.extData.defaultDeptId"
              :label="`${item.name}  - ${item.position} - ${item.empStatusText}`" :value="item.value" />
          </el-select> -->
          
          <el-button class="top_button" type="primary" @click="getList('search')">搜索</el-button>
          <el-button class="top_button" type="primary" @click="exportData">导出</el-button>
        </div>
      </template>
      <vxetablebase :id="'operationalSelection202503161619'" :tablekey="'operationalSelection202503161619'" ref="table"
        :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
        :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
        style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      </vxetablebase>
      <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template>
    </MyContainer>
  </template>
  
  <script>
  import MyContainer from "@/components/my-container";
  import vxetablebase from "@/components/VxeTable/yh_vxetableVirtualScroll.vue";
  import { pickerOptions, platformlist } from '@/utils/tools'
  import dayjs from 'dayjs'
  import inputYunhan from "@/components/Comm/inputYunhan";
  import { PagePDDCommentListAsync, ExportPDDCommentListAsync, } from '@/api/operatemanage/OperationalMiddleOfficeManage/InventoryFundTurnover.js'
  import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'
//   import { getErpSampleGoodsChooseRecordListAsync, exportErpSampleGoodsChooseRecordListAsync } from '@/api/customerservice/albbinquirs'
  const categoryList = ['1星', '2星', '3星', '4星', '5星']
  
  const tableCols = [
    { sortable: 'custom', width: '200', align: 'center', prop: 'proCode', label: 'ID' },
    { sortable: 'custom', width: '200', align: 'center', prop: 'orderNo', label: '订单编号' },
    { sortable: 'custom', width: '200', align: 'center', prop: 'cmtTime', label: '评价时间', },
    { sortable: 'custom', width: '200', align: 'center', prop: 'cmtStartLevel', label: '用户评价分', formatter: (row) => row.cmtStartLevel ? row.cmtStartLevel+'星' : '' },
    { sortable: 'custom', width: '200', align: 'center', prop: 'praiseCount', label: '被点赞数' },
    { sortable: 'custom', width: '200', align: 'center', prop: 'interactcount', label: '互动数' },
    { sortable: 'custom', width: '200', align: 'center', prop: 'cashBacked', label: '已返现金' },
    { width: '90', align: 'center', prop: 'image', label: '图片', type: "images", align: 'left' },
    { width: 'auto', align: 'center', prop: 'comment', label: '评价' },


    // { sortable: 'custom', width: '80', align: 'center', prop: 'isPriceControl', label: '是否控价' },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'minimumSellingControlPrice', label: '供货成本价(控价价格)' },
    // { sortable: 'custom', width: '70', align: 'center', prop: 'isZiZhi', label: '资质' },
    // { sortable: 'custom', width: '70', align: 'center', prop: 'isZhuanli', label: '专利' },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'yhGoodsCode', label: '公司商品编码' },
    // { sortable: 'custom', width: '70', align: 'center', prop: 'platform', label: '平台', formatter: (row) => row.platformName },
    // { sortable: 'custom', width: '90', align: 'center', prop: 'groupId', label: '运营组', formatter: (row) => row.groupName },
    // { sortable: 'custom', width: '70', align: 'center', prop: 'userName', label: '选品人' },
    // { sortable: 'custom', width: '120', align: 'center', prop: 'b.createdTime', label: '选品时间', formatter: (row) => row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '' },
  ]
  export default {
    name: "operationalSelection",
    components: {
      MyContainer, vxetablebase, inputYunhan
    },
    data() {
      return {
        categoryList,
        contactPersonList: [],
        grouplist: [],
        yesOrNoOptions: [
          { value: 1, label: '是' },
          { value: 0, label: '否' },
        ],
        platformlist,
        that: this,
        ListInfo: {
          currentPage: 1,
          pageSize: 50,
          orderBy: null,
          isAsc: false,
          cmtStartTime: null,//开始时间
          cmtEndTime: null,//结束时间
        //   styleCode: null,//款式名称
        //   productName: null,//商品名称
        //   packagingSpecification: null,//包装
        //   material: null,//材质
        //   isPriceControl: null,//是否控价
        //   yhGoodsCode: null,//公司商品编码
        //   groupIds: [],//运营组
        //   dDUserIds: [],//选品人
        //   platforms: [],//平台
        //   catroyType: null,//类目
        },
        timeRanges: [
            dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
            dayjs().format('YYYY-MM-DD')
        ],
        tableCols,
        tableData: [],
        summaryarry: {},
        total: 0,
        loading: false,
        pickerOptions,
      }
    },
    async mounted() {
      await this.getList()
      await this.init()
    },
    methods: {
      async searchReferrer(e) {
        //如果e的长度大于200,就提示
        if (e.length > 200) {
          this.$message.error('最多输入200个字符')
          return
        }
        // 如果输入为空，清空下拉框
        if (e === '' || e === null || e === undefined) {
          this.contactPersonList = []
          return
        }
        const { data } = await QueryAllDDUserTop100({ keywords: e });
        if (!data) return;
        const targetData = data.map(item => ({
          value: item.ddUserId,
          label: `${item.userName} - (${item.deptName})`,
          name: item.userName,
          ddUserId: item.ddUserId,
          extData: item,
          position: item.position,
          deptName: item.deptName,
          empStatusText: item.empStatusText,
        }));
        this.contactPersonList = targetData;
      },
      callbackGoodsCode(val, type) {
        const map = {
          cmtKeyWords: () => (this.ListInfo.cmtKeyWords = val),
          orderNo: () => (this.ListInfo.orderNo = val),
          proCode: () => (this.ListInfo.proCode = val),
        };
        map[type]?.();
      },
      async init() {
        // var res2 = await getDirectorGroupList();
        // this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
      },
      async changeTime(e) {
        this.ListInfo.cmtStartTime = e ? e[0] : null
        this.ListInfo.cmtEndTime = e ? e[1] : null
      },
      async exportData() {
        this.loading = true
        var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
        const res = await ExportPDDCommentListAsync(this.ListInfo);
        loadingInstance.close();
        if (!res?.data) {
            this.loading = false;
            this.$message({ message: "没有数据", type: "warning" });
            return
        }
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '拼多多图片评价_' + new Date().toLocaleString() + '.xlsx')
        aLink.click();
        this.loading = false
      },
      handleImage(item, field) {
        const images = item[field];
        if (images) {
          return JSON.stringify(images.split(',').map(url => ({ url, name: '' })));
        }
        return JSON.stringify([]);
      },
      async getList(type) {
        if (type == 'search') {
          this.ListInfo.currentPage = 1
          this.$refs.pager.setPage(1)
        }
        if (this.timeRanges && this.timeRanges.length > 0) {
          //默认当天时间
          this.ListInfo.cmtStartTime = this.timeRanges[0];
          // 获取当前日期
          this.ListInfo.cmtEndTime = this.timeRanges[1];
        }
        if(this.ListInfo.cmtStartLevel){
            this.ListInfo.cmtStartLevelList = this.ListInfo.cmtStartLevel;
        }else{
            this.ListInfo.cmtStartLevelList = []
        }
        if(this.ListInfo.cmtKeyWords){
            this.ListInfo.cmtKeyWordsList = this.ListInfo.cmtKeyWords.split(',');
        }else{
            this.ListInfo.cmtKeyWordsList = []
        }
        if(this.ListInfo.orderNo){
            this.ListInfo.orderNoList = this.ListInfo.orderNo.split(',');
        }else{
            this.ListInfo.orderNoList = []
        }
        if(this.ListInfo.proCode){
            this.ListInfo.proCodeList = this.ListInfo.proCode.split(',');
        }else{
            this.ListInfo.proCodeList = []
        }
        this.loading = true
        const { data, success } = await PagePDDCommentListAsync(this.ListInfo)
        if (success) {
          this.tableData = data.list
          this.tableData.forEach(item => {
            // item.image = this.handleImage(item, 'image');
            console.log("1111111", JSON.parse(item.image))
          });
          this.total = data.total
          this.summaryarry = data.summary
          this.loading = false
        } else {
          this.$message.error('获取列表失败')
          this.loading = false
        }
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }
  </script>
  
  <style scoped lang="scss">
  .top {
    display: flex;
    margin-bottom: 5px;
    flex-wrap: wrap;
  
    .publicCss {
      width: 140px;
      margin: 0 2px 0 0;
    }
  }
  
  ::v-deep .el-select__tags-text {
    max-width: 30px;
  }
  
  ::v-deep(.el-button.top_button + .el-button.top_button) {
    margin-left: 1px;
  }
  </style>
  