<template>
    <my-container v-loading="pageLoading">
      <template #header>
        <div>
        <el-button-group>
            <el-button style="padding: 0;margin: 0;">
              <inputYunhan  ref="proCode" v-model="filter.proCode" :inputt.sync="filter.proCode" :maxlength="150" placeholder="商品编码/Enter输入多条" :clearable="true" @callback="callbackProCode" title="商品编码" ></inputYunhan>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
               <el-input v-model.trim="filter.productName" :maxlength="150" clearable placeholder="商品名称" style="width:100px;"/>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <inputYunhan ref="productOrderNoInner" :inputt.sync="filter.OrderNoInner" v-model="filter.OrderNoInner"
                width="160px" placeholder="内部单号/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
                :maxlength="2000" @callback="callbackGoodsCode($event, 'OrderNoInner')" title="内部单号">
              </inputYunhan>
           </el-button>
           <el-button style="padding: 0;margin: 0;">
            <inputYunhan ref="productorderNo" :inputt.sync="filter.orderNo" v-model="filter.orderNo"
              width="160px" placeholder="原始线上单号/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
              :maxlength="2000" @callback="callbackGoodsCode($event, 'orderNo')" title="原始线上单号">
            </inputYunhan>
         </el-button>
            <el-button style="padding: 0;width: 200px;">
              <el-select v-model.trim="styleCode" multiple collapse-tags filterable remote reserve-keyword placeholder="系列编码" clearable :remote-method="remoteMethod"
              style="width: 200px" :loading="searchloading">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" > </el-option>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
                <el-date-picker style="width: 210px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                                  start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="filter.distributor" :maxlength="150" clearable placeholder="分销商" style="width:100px;"/>
           </el-button>
           <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="filter.orderSource" :maxlength="150" clearable placeholder="订单来源" style="width:100px;"/>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="filter.shopName" :maxlength="150" clearable placeholder="店铺" style="width:160px;"/>
            </el-button>

          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.profit1UnZero" collapse-tags clearable placeholder="毛利1" style="width: 90px">
              <el-option label="正利润" :value="false"/>
              <el-option label="负利润" :value="true"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.profit2UnZero" collapse-tags clearable placeholder="毛利2" style="width: 90px">
              <el-option label="正利润" :value="false"/>
              <el-option label="负利润" :value="true"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.profit3UnZero" collapse-tags clearable placeholder="毛利3" style="width: 90px">
              <el-option label="正利润" :value="false"/>
              <el-option label="负利润" :value="true"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.profit33UnZero" collapse-tags clearable placeholder="毛利4"
              style="width: 90px">
              <el-option label="正利润" :value="false" />
              <el-option label="负利润" :value="true" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.profit6UnZero" collapse-tags clearable placeholder="毛利6"
              style="width: 90px">
              <el-option label="正利润" :value="false" />
              <el-option label="负利润" :value="true" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.exitProfitUnZero" collapse-tags clearable placeholder="出仓利润" style="width: 90px">
              <el-option label="正利润" :value="false"/>
              <el-option label="负利润" :value="true"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.orderLxList" multiple collapse-tags clearable placeholder="类型" style="width: 70px">
              <el-option label="聚水潭" :value="2"/>
              <el-option label="独立站" :value="1"/>
              <el-option label="旺店通" :value="5"/>
              <el-option label="售后" :value="3"/>
              <el-option label="普通" :value="4"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select v-model="filter.ModeType" placeholder="选品中心编码标签" clearable filterable multiple collapse-tags
                    class="publicCss">
                    <el-option label="自费采购" value="自费采购"></el-option>
                    <el-option label="免费入仓" value="免费入仓"></el-option>
                </el-select>
          </el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-button-group>
        <!-- <div class="el-backtop" style="right: 5px;" @click="showupload"><i class="el-icon-caret-right"></i></div> -->
        </div>
     </template>
      <vxetablebase
        :id="'productReportFenXiao202302031421'" :border="true" :align="'center'" :tablekey="'productReportFenXiao202302031421'"
        ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
        :isSelectColumn="true" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :cstmExportFunc="onExport"
        :tableData='financialreportlist' @summaryClick='onsummaryClick' :tableCols='tableCols' @cellClick="cellClick"
        :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:95%;margin: 0"   :xgt="9999" :showheaderoverflow="false">
         <template slot='extentbtn'>
          <el-button type="primary" size="small" @click="dialogConfirmdata =true">日报确认</el-button>
          <!-- <el-button type="primary" size="small" @click="dialogConfirmdata2 =true">违规扣款确认</el-button> -->
          <el-button-group>
              <el-radio-group v-model="filter.refundType" size="small">
                <el-radio-button :label="1">发生维度</el-radio-button>
                <el-radio-button :label="2">付款维度</el-radio-button>
                <!-- <el-radio-button :label="3">运营维度</el-radio-button> -->
             </el-radio-group>
          </el-button-group>
         </template>
      </vxetablebase>
      <!--分页-->
      <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
      </template>

      <el-dialog title="计算日报" :visible.sync="dialogCalDayRepotVis" width="40%" v-dialogDrag>
        <span>
          <el-row>
            <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
              <el-date-picker style="width: 50%; float: left;" v-model="calDayRepotyDate" type="date" format="yyyyMMdd"
                value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
                <el-button  type="success" style="margin-left:  30px;"  @click="calDayRepoty">计算日报</el-button>
            </el-col>
          </el-row>
        </span>
      </el-dialog>

      <el-dialog title="导入胜算" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
        <span>
            <el-row>
              <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
              <el-date-picker style="width: 100%" v-model="onimportfilter.yearmonthday" type="date" format="yyyyMMdd"   value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
           </el-col>
           </el-row>
            <el-row>
              <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                  <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx"
                    :http-request="uploadFile" :file-list="fileList" :data="fileparm">
                  <template #trigger>
                      <el-button size="small" type="primary">选取文件</el-button>
                  </template>
                  <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
                </el-upload>
              </el-col>
            </el-row>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </el-dialog>

      <el-dialog title="确认数据" :visible.sync="dialogConfirmdata" width="40%" v-dialogDrag>
        <span>
          <el-row>
            <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
              <el-date-picker style="width: 50%; float: left;" v-model="confirmDate" type="date" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
              <el-button type="success" style="margin-left:  30px;" @click="confirmData">确认</el-button>
            </el-col>
          </el-row>
        </span>
      </el-dialog>

      <!-- <el-dialog title="确认违规扣款数据" :visible.sync="dialogConfirmdata2" width="40%" v-dialogDrag>
        <span>
          <el-row>
            <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
              <el-date-picker style="width: 50%; float: left;" v-model="confirmDate2" type="date" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
              <el-button type="success" style="margin-left:  30px;" @click="confirmData2">确认</el-button>
            </el-col>
          </el-row>
        </span>
      </el-dialog> -->
      <!-- <el-drawer title="参数设置" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editparmVisible"
                  direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
         <form-create :rule="autoformparm.rule" v-model="autoformparm.fApi" :option="autoformparm.options"/>
         <div class="drawer-footer">
          <el-button @click.native="editparmVisible = false">取消</el-button>
          <my-confirm-button type="submit" :loading="editparmLoading" @click="onSetEditParm" />
         </div>
     </el-drawer> -->

     <el-dialog title="商品数据趋势图" :visible.sync="dialogDrVisible" width="80%" v-dialogDrag>
        <span>
            <productdrchart v-if="dialogDrVisible"></productdrchart>
        </span>

        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogDrVisible = false">关闭</el-button>
        </span>
      </el-dialog>

      <el-dialog title="实际快递费明细" :visible.sync="freightDetail.visible" width="80%" v-dialogDrag>
         <freightDetail ref="freightDetail" :filter="freightDetail.filter" style="height:600px;"></freightDetail>
      </el-dialog>

      <el-dialog title="每日退款明细" :visible.sync="EveryDayrefund.visible" width="80%" v-dialogDrag>
        <div>
         <EveryDayrefund ref="EveryDayrefund" :filter="EveryDayrefund.filter" style="height:600px;"></EveryDayrefund>
        </div>
      </el-dialog>
       <el-dialog title="赠品成本明细" :visible.sync="giftDetail.visible" width="80%" v-dialogDrag>
         <ordergiftdetail ref="ordergiftdetail" style="height:600px;"></ordergiftdetail>
      </el-dialog>
      <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
        <span>
          <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="buscharDialog.visible = false">关闭</el-button>
        </span>
    </el-dialog>

      <el-dialog title="快递分析" :visible.sync="expressfreightanalysisVisible" width="80%" v-dialogDrag>
        <span>
          <expressfreightanalysis ref="expressfreightanalysis"></expressfreightanalysis>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="expressfreightanalysisVisible = false">关闭</el-button>
        </span>
    </el-dialog>
    <!-- <el-drawer title="每日文件上传跟踪" :visible.sync="drawervisible" direction="rtl">
        <importmodule ref="importmodule" :id="2" v-if="drawervisible"></importmodule>
      </el-drawer> -->
    </my-container>
  </template>
  <script>
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import { getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
  import {getDirectorGroupList,getDirectorList} from '@/api/operatemanage/base/shop'
  import EveryDayrefund from '@/views/bookkeeper/reportday/EveryDayrefund'
  import {pageProductDayReport,queryDayReportAnalysis,exportProductDayReport,getParm,setParm,calDayRepoty, insertDailyReportConfirmList} from '@/api/bookkeeper/reportday'
  import {importProductDayReport} from '@/api/bookkeeper/import'
  import {getAllProBrand} from '@/api/inventory/warehouse'
  import productdrchart from '@/views/bookkeeper/reportday/productdrchart'
  import ProductADPDD from '@/views/bookkeeper/reportday/ProductADPDD'
  import {formatPlatform,formatTime,formatYesornoBool,formatLinkProCode} from "@/utils/tools";
  import cesTable from "@/components/Table/table.vue";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import InputMult from "@/components/Comm/InputMult";
  import { Loading } from 'element-ui';
  import { ruleDirectorGroup } from '@/utils/formruletools'
  import freightDetail from '@/views/bookkeeper/reportday/freightDetail'
  import expressfreightanalysis from '@/views/express/expressfreightanalysis'
  import buschar from '@/components/Bus/buschar'
  import importmodule from '@/components/Bus/importmodule'
  import ordergiftdetail from '@/views/order/gift/ordergiftDetail'
  import { getListByStyleCode } from "@/api/inventory/basicgoods"
  import inputYunhan from "@/components/Comm/inputYunhan";
  let loading;
  const startLoading = () => {
    loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
    });
  };
  const tableCols =[
          {istrue:true,fixed: 'left',prop:'yearMonthDay',label:'年月日',sortable:'custom', width:'60',type:'custom'},
        //   {istrue:true,fixed: 'left',prop:'platform',fix:true,label:'平台', width:'45',sortable:'custom',formatter:(row)=>formatPlatform(row.platform),type:'custom'},
          { istrue: true, fixed: 'left', prop: 'orderNo', label: '内部单号',  width: '80' ,type:'custom',type:'orderLogInfo',orderType:'orderNoInner'  },
          { istrue: true, fixed: 'left', prop: 'originalOnlineOrderNumber', label: '原始线上单号',  width: '80' ,type:'custom'},
          { istrue: true, fixed: 'left', prop: 'orderSource', label: '订单来源',  width: '80' ,type:'custom'},
          { istrue: true, fixed: 'left', prop: 'orderStatus', label: '订单状态',  width: '80' ,type:'custom'},
          { istrue: true, fixed: 'left', prop: 'orderType', label: '订单类型',  width: '80' ,type:'custom',formatter:(row)=>row.orderType==0?"普通订单":row.orderType==1?"补发订单":row.orderType==2?"供销Plus":"其他"},
          {istrue:true,fixed: 'left',prop:'orderLx',label:'类型', width:'70',type:'custom'},
          {istrue:true,fixed: 'left',prop:'modeType',label:'选品中心编码标签', width:'110',type:'custom'},
          { istrue: true, fixed: 'left', prop: 'distributor', label: '分销商',  width: '80' ,type:'custom'},
          {istrue:true,fixed: 'left',prop:'shopName',label:'店铺',sortable:'custom', width:'70',formatter:(row)=> row.shopName,type:'custom'},
          {istrue:true,fixed: 'left',prop:'proCode',fix:true,label:'商品编码', width:'80',sortable:'custom'},
          {istrue:true,fixed: 'left',prop:'goodsName',label:'商品名称',sortable:'custom', width:'80'},
          {istrue:true,fixed: 'left',prop:'styleCode',label:'系列编码',sortable:'custom', type:'click', width:'80',formatter:(row)=> row.styleCode || ' ',handle:(that,row)=>that.showProcodesimilarity(row)},
          { istrue: true, summaryEvent: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width: '65', tipmesg: '（付款订单量）此字段仅供参考；如果一个订单中有3种商品，这3行商品的销售订单数均为1，所以不能把此字段的合计值作为订单数量。因为订单就只有1个。但是销售订单数合计值为3。', formatter: (row) => !row.orderCount ? " " : row.orderCount },
          {istrue:true,summaryEvent:true,prop:'payAmont',label:'付款金额',sortable:'custom', width:'80',formatter:(row)=> !row.payAmont?" ": row.payAmont},
          { istrue: true, summaryEvent: true, prop: 'saleAmont', label: '销售金额', sortable: 'custom', width: '80', type: 'custom', tipmesg: '付款金额-刷单金额-发货前退款-发货后退款', formatter: (row) => !row.saleAmont ? " " : row.saleAmont },
  {istrue:true,summaryEvent:true,prop:'exitProfit',label:'出仓利润',sortable:'custom', width:'80',type:'custom',tipmesg:'付款金额-总销售成本-出仓成本-快递费',formatter:(row)=> !row.exitProfit?" ": row.exitProfit},
  { istrue: true, summaryEvent: true, prop: 'saleCost', label: '总销售成本', sortable: 'custom', width: '80', tipmesg: '付款金额对应的销售成本', style: (that, row) => that.renderCost(row), handle: (that, row) => that.showCost(row), formatter: (row) => !row.saleCost ? " " : row.saleCost },
  { istrue: true, summaryEvent: true, prop: 'goodProfitRate', label: '商品毛利率', sortable: 'custom', width: '80', tipmesg: '（付款金额-（销售成本+赠品成本+赠品链接成本+代发成本差））/付款金额', formatter: (row) => !row.goodProfitRate ? " " : row.goodProfitRate + '%' },
  {istrue:true,summaryEvent:true,prop:'freightFeeTotal',label:'快递费',sortable:'custom', width:'80',type:'custom',tipmesg:'实际快递费+预估快递费',formatter:(row)=> !row.freightFeeTotal?" ": row.freightFeeTotal},
  {istrue:true,summaryEvent:true,prop:'freightAvgFee',label:'快递均价',sortable:'custom', width:'80',formatter:(row)=> !row.freightAvgFee?" ": row.freightAvgFee},


          {istrue:true,summaryEvent:true,prop:'giftAmont',label:'赠品成本',sortable:'custom', width:'80',permission:"cgcoltxpddprsi",formatter:(row)=> !row.giftAmont?' ': row.giftAmont,type:'click',handle:(that,row)=>that.showGiftDetail(row)},
          // {istrue:true,summaryEvent:true,prop:'brushAmont',label:'刷单金额',sortable:'custom', width:'80',tipmesg:'刷单金额',formatter:(row)=> !row.brushAmont?" ": row.brushAmont},
          {istrue:true,summaryEvent: true,prop:'',label:'退款', merge:true,permission:"cgcoltxpddprsi", width: '80',
              cols:[
            //   {istrue:true,summaryEvent:true,prop:'cancelOrderCount',label:'发货前退款单量',sortable:'custom', width:'80',type:'custom'},
              {istrue:true,summaryEvent:true,prop:'refundAmontBefore',label:'发货前退款',sortable:'custom', width:'80',type:'custom'},
            //   {istrue:true,summaryEvent:true,prop:'refundAmontBeforeRate',label:'发货前退款率',sortable:'custom', width:'80',type:'custom',formatter:(row)=> !row.refundAmontBeforeRate?" ": (row.refundAmontBeforeRate* 100)?.toFixed(0)+'%'},
              {istrue:true,summaryEvent:true,prop:'refundAmontAfter',label:'发货后退款',sortable:'custom', width:'80',type:'custom'},
            //  {istrue:true,summaryEvent:true,prop:'refundAmontAfterRate',label:'发货后退款率',sortable:'custom', width:'80',type:'custom',formatter:(row)=> !row.refundAmontAfterRate?" ": (row.refundAmontAfterRate* 100)?.toFixed(0)+'%'},
             {istrue:true,summaryEvent:true,prop:'refundAmont',label:'总退款金额',sortable:'custom', width:'80',tipmesg:'当日发生的退款金额，包括历史订单',formatter:(row)=> !row.refundAmont?" ": row.refundAmont},
            ]},
            // {istrue:true,summaryEvent:true,prop:'saleAmont',label:'销售金额',sortable:'custom', width:'80',type:'custom',tipmesg:'付款金额-刷单金额-发货前退款-发货后退款',formatter:(row)=> !row.saleAmont?" ": row.saleAmont},
            {istrue:true,summaryEvent:true,prop:'cancelCost',label:'取消单成本',sortable:'custom', width:'80',tipmesg:'返还客服已确认的取消单成本,以确认时间统计',formatter:(row)=> !row.cancelCost?" ": row.cancelCost,type:'click',handle:(that,row)=>that.showEveryDayrefund(row)},
            {istrue:true,summaryEvent:true,prop:'purchaseFreight',label:'采购成本差价',sortable:'custom', width:'100',formatter:(row)=> row.purchaseFreight==0?0: row.purchaseFreight,tipmesg:'入库单的采购运费分摊到该入库单的商品编码对应的ID上'},
            {istrue:true,summaryEvent:true,prop:'replaceSendCost',label:'代发成本差',sortable:'custom', width:'80',tipmesg:'每日杂费-代发成本差',formatter:(row)=> !row.replaceSendCost?" ": row.replaceSendCost,type:'custom'},
            {istrue:true,summaryEvent:true,prop:'profit1',label:'毛一',sortable:'custom', width:'80',type:'custom',tipmesg:'销售金额-总销售成本+取消单返还成本-采购成本差',formatter:(row)=> !row.profit1?" ": row.profit1},
            {istrue:true,summaryEvent:true,prop:'profit1Rate',label:'毛一利润率',sortable:'custom', width:'80',type:'custom',tipmesg:'毛一利润/销售金额',formatter:(row)=> !row.profit1Rate?" ": row.profit1Rate+'%'},
            {istrue:true,summaryEvent:true,prop:'exitCost',label:'出仓成本',sortable:'custom',tipmesg:'辅料+人工', width:'80',formatter:(row)=> !row.exitCost?" ": row.exitCost},
            {istrue:true,summaryEvent:true,prop:'packageFee',label:'包装费用',sortable:'custom',tipmesg:'辅料+包材', width:'80',formatter:(row)=> !row.packageFee?" ": row.packageFee},
            { istrue: true, summaryEvent: true, prop: 'packageAvgFee', label: '包装均价', sortable: 'custom', width: '70', formatter: (row) => !row.packageAvgFee ? " " : row.packageAvgFee },
          {istrue:true,summaryEvent:true,prop:'freightFee',label:'实际快递费',sortable:'custom', width:'80',type:'custom'},
          {istrue:true,summaryEvent:true,prop:'freightFeeVirtual',label:'虚拟快递费',sortable:'custom', width:'80',formatter:(row)=> !row.freightFeeVirtual?" ": row.freightFeeVirtual},
          {istrue:true,summaryEvent:true,prop:'extRemark',label:'取值规则',sortable:'custom', width:'80',type:'custom'},
          // {istrue:true,summaryEvent: true,prop:'ygfy',label:`预估费用(正式)`, merge:true,
          //   cols:[
            //   {istrue:true,summaryEvent:true,prop:'cuiShouFee',label:'催收费',sortable:'custom', width:'80',formatter:(row)=> row.cuiShouFee==0?" ": row.cuiShouFee,tipmesg:'每日导入'},
            //   {istrue:true,summaryEvent:true,prop:'sampleFeeBX',label:'样品费',sortable:'custom', width:'80',formatter:(row)=> row.sampleFeeBX==0?" ": row.sampleFeeBX,tipmesg:'每日导入'},
            //   {istrue:true,summaryEvent:true,prop:'sampleFee',label:'拿样费',sortable:'custom', width:'80',formatter:(row)=> row.sampleFee==0?" ": row.sampleFee,tipmesg:'运营/美工拿样，预估，5000元/月，每日167元'},
            //   {istrue:true,summaryEvent:true,prop:'shootFee',label:'道具费',sortable:'custom', width:'80',formatter:(row)=> row.shootFee==0?" ": row.shootFee,tipmesg:'美工拍摄道具费，预估，10000元/月'},
            //   {istrue:true,summaryEvent:true,prop:'wages',label:'运营小组工资',sortable:'custom', width:'80',formatter:(row)=> row.wages==0?" ": row.wages,tipmesg:'参考各组工资占比'},
            //   { istrue: true, summaryEvent: true, prop: 'customerServiceWages', label: '客服工资', sortable: 'custom', width: '70', formatter: (row) => row.customerServiceWages == 0 ? " " : row.customerServiceWages, tipmesg: '' },
            //   { istrue: true, summaryEvent: true, prop: 'processingCost', label: '加工部工资', sortable: 'custom', width: '70', formatter: (row) => row.processingCost == 0 ? " " : row.processingCost, tipmesg: '参考日报数据维护-加工费（工价*（订单量-发货前退款单量））' },
            //   {istrue:true,summaryEvent:true,prop:'lossOffFee',label:'损耗下架',sortable:'custom', width:'80',formatter:(row)=> row.lossOffFee==0?" ": row.lossOffFee,tipmesg:'预估，0.1%'},
            //   {istrue:true,summaryEvent:true,prop:'serviceFee',label:'客服费',sortable:'custom', width:'80',formatter:(row)=> row.serviceFee==0?" ": row.serviceFee,},
            //   {istrue:true,summaryEvent:true,prop:'warehousingFee',label:'仓储费',sortable:'custom', width:'80',formatter:(row)=> row.warehousingFee==0?" ": row.warehousingFee,},
        //  ]},
         {istrue:true,summaryEvent:true,prop:'profit2',label:'毛二',sortable:'custom', width:'80',type:'custom',tipmesg:'毛一-包装费-快递费合计-产品运费',
                                                formatter:(row)=>!row.profit2?" ": row.profit2},
         {istrue:true,summaryEvent:true,prop:'profit2Rate',label:'毛二利润率',sortable:'custom', width:'80',type:'custom',tipmesg:'毛二利润/销售金额',formatter:(row)=>!row.profit2Rate?" ": row.profit2Rate+'%'},
        {istrue:true,summaryEvent:true,prop:'wages',label:'运营工资+客服工资',sortable:'custom', width:'80',formatter:(row)=> row.wages==0?" ": row.wages,tipmesg:'参考各组工资占比'},
        { istrue: true, summaryEvent: true, prop: 'processingCost', label: '加工费', sortable: 'custom', width: '70', formatter: (row) => row.processingCost == 0 ? " " : row.processingCost, tipmesg: '参考日报数据维护-加工费（工价*（订单量-发货前退款单量））' },
        {istrue:true,summaryEvent:true,prop:'productFreight',label:'产品运费',sortable:'custom', width:'80',formatter:(row)=> row.productFreight==0?" ": row.productFreight,tipmesg:'（总销售成本-取消单成本）*2%'},
        {istrue:true,summaryEvent:true,prop:'profit3',label:'毛三利润',sortable:'custom', width:'80',type:'custom',tipmesg:'毛二利润-预估费用',permission:"lirunprsi,profit3rsi",formatter:(row)=> !row.profit3?" ": row.profit3},
        {istrue:true,summaryEvent:true,prop:'profit3Rate',label:'毛三利润率',type:'custom',tipmesg:'毛三利润/销售金额',sortable:'custom', width:'80',permission:"lirunprsi,profit3rsi",formatter:(row)=> !row.profit3Rate?" ": row.profit3Rate+'%'},
        {istrue:true,summaryEvent:true,prop:'profit33',label:'毛四利润',sortable:'custom', width:'80',type:'custom',tipmesg:'毛三利润-出仓成本',permission:"lirunprsi,profit3rsi",formatter:(row)=> !row.profit33?" ": row.profit33},
        {istrue:true,summaryEvent:true,prop:'profit33Rate',label:'毛四利润率',sortable:'custom', width:'80',type:'custom',tipmesg:'毛四利润/销售金额',permission:"lirunprsi,profit3rsi",formatter:(row)=> !row.profit33Rate?" ": row.profit33Rate+ '%' },
  { istrue: true, summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '70', formatter: (row) => !row.warehouseSalary ? " " : row.warehouseSalary },
  { istrue: true, summaryEvent: true, prop: 'profit5', label: '毛五利润', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛四利润-仓库薪资', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit5 ? " " : row.profit5 },
        { istrue: true, summaryEvent: true, prop: 'profit5Rate', label: '毛五利润率', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛五利润/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + '%' },
        { istrue: true, summaryEvent: true, prop: 'realFreightFee', label: '外仓快递费', sortable: 'custom', width: '80', type: 'custom', permission: "DistributionExpensePermissions", formatter: (row) => !row.realFreightFee ? " " : row.realFreightFee },
        { istrue: true, summaryEvent: true, prop: 'realPackageFee', label: '外仓包装费', sortable: 'custom', width: '80', type: 'custom', permission: "DistributionExpensePermissions", formatter: (row) => !row.realPackageFee ? " " : row.realPackageFee },
        { istrue: true, summaryEvent: true, prop: 'profit6', label: '毛六利润', sortable: 'custom', width: '80', type: 'custom', permission: "DistributionExpensePermissions", formatter: (row) => !row.profit6 ? " " : row.profit6 },
        { istrue: true, summaryEvent: true, prop: 'profit6Rate', label: '毛六利润率', sortable: 'custom', width: '80', type: 'custom', tipmesg: '毛六利润/销售金额', permission: "DistributionExpensePermissions", formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + '%' },
        // { istrue: true, summaryEvent: true, prop: 'exitCostRate', label: '出仓成本占比', sortable: 'custom', width: '70', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate+ '%'  },
        ];
  const tableHandles=[
          //{label:"导入胜算", handle:(that)=>that.onstartImport()},
          //{label:"参数设置", handle:(that)=>that.onShowEditParm()},
          { label: "导出", handle: (that) => that.onDerivationMethod() },
          {label:"每日快递", handle:(that)=>that.showexpressfreightanalysis()},
          { label: "计算日报", handle: (that) => that.showCalDayRepoty() },
          {label:"刷新", handle:(that)=>that.onRefresh()},
        ];

  export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable,productdrchart,InputMult,freightDetail, buschar, expressfreightanalysis,importmodule,ordergiftdetail,ProductADPDD,EveryDayrefund,inputYunhan,vxetablebase},
    data() {
      return {
        dialogConfirmdata: false,
        // dialogConfirmdata2: false,
        confirmDate:'',
        confirmDate2:'',
        searchloading:'',
        dialogCalDayRepotVis:false,
        calDayRepotyDate:null,
        that:this,
        filter: {
          orderNo: null,
          OrderNoInner: null,
          refundType: 1,
          reportType:0,
          platform:11,
          shopCode:null,
          proCode:null,
          styleCode:null,
          productName:null,
          brandId:null,
          groupId:null,
          startTime: null,
          endTime: null,
          timerange:null,
          // 运营助理
          userId :null,
          // 车手
          userId2:null,
          // 备用
          userId3:null,
          // 运营专员 ID
          operateSpecialUserId:null,
          shopName:null,
          orderSource:null,
          distributor:null,
          profit1UnZero:null,
          profit2UnZero:null,
          profit3UnZero :null,
          profit33UnZero: null,
          profit6UnZero: null,
          profit4UnZero:null,
          groupType: null,
          timerange1:null,
          listingStartTime: null,
          listingEndTime: null,
          noProfitDay:null,
          orderLx:'',
          orderLxList:[],
          ModeType:[],
        },
        onimportfilter:{
          yearmonthday:null,
        },
        styleCode:null,
        shopList:[],
        userList:[],
        brandlist:[],
        grouplist:[],
        directorlist:[],
        financialreportlist: [],
        tableCols:tableCols,
        tableHandles:tableHandles,
        total: 0,
        pager:{OrderBy:"YearMonthDay",IsAsc:false},
        sels: [], // 列表选中列
        options:[],
        listLoading: false,
        pageLoading: false,
        earchloading:false,
        summaryarry:{},
        selids:[],
        fileList:[],
        dialogVisible:false,
        uploadLoading:false,
        importFilte:{},
        fileList:[],
        fileparm:{},
        editparmVisible:false,
        editLoading:false,
        editparmLoading:false,
        drawervisible:false,
        dialogDrVisible:false,
        expressfreightanalysisVisible:false,
        drparamProCode:'',
        autoformparm:{
                 fApi:{},
                 options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
                 rule:[]
          },
        freightDetail:{
          visible:false,
          filter:{
            proCode:null,
            timeRange:[]
          },
        },
        EveryDayrefund:{
             visible:false,
          filter:{
            proCode:null,
            timeRange:[],
            afterSaleType:"2",
            orderStatus:"已取消",
            goodsStatus:"",
            timeRange1:[],
            platform:11
          }
          },
        giftDetail:{visible:false},
        buscharDialog:{visible:false,title:"",data:[]},
        drawervisible:false,
      };
    },
    async mounted() {
      this.init();
    },
    async created() {
      await this.getShopList();
      await this.initformparm();
      if (this.$route.query && this.$route.query.dayCount) {

        this.filter.noProfitDay = parseInt(this.$route.query.dayCount);
        this.filter.shopCode = this.$route.query.shopCode;
        this.filter.groupId = this.$route.query.groupId;
        this.filter.operateSpecialUserId=this.$route.query.operateSpecialUserId;
        let dateStr = this.$route.query.yearMonthDay.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
        this.filter.timerange=[dateStr,dateStr];
        this.filter.refundType=1;
        this.onSearch();
      }
    },
    methods: {
      callbackGoodsCode(val, type) {
        const map = {
          OrderNoInner: () => (this.filter.OrderNoInner = val),
          orderNo: () => (this.filter.orderNo = val),
        };
        map[type]?.();
      },
      FenXiaoDayReportArgument(e, No, Timerange, row){
        if (Timerange) {
          this.filter.timerange = Timerange;
          if (this.filter.timerange) {
            this.filter.startTime = this.filter.timerange[0];
            this.filter.endTime = this.filter.timerange[1];
          }
        }
        if(No==1)
        {
          this.filter.distributor=e;
        }else if(No==2)
        {
          this.filter.orderNo=e;
          this.filter.OriginalOnlineOrderNumber = row.originalOnlineOrderNumber;
          this.filter.distributor = null;
          this.filter.styleCode = null;
          this.filter.shopName = null;
        }
       this.getList();
      },
      cellClick(prms){
        if(prms?.column?.field   && prms?.column?.field==='profit3IncreaseGoOnDays'  ){
          let row=prms.row;
          this.showprchart2(row.proCode, row.platform) ;
        }

      },
      async confirmData () {
        if (!this.confirmDate) {
          this.$message({ type: 'warning', message: '请选择日期!' });
          return;
        }
        let par = {
          dailyReportDate: this.confirmDate,
          dailyReportType: '分销日报',
        };
        let confirmtitle = '【' + this.confirmDate + '】' + par.dailyReportType + '数据，确定确认？';
        this.$confirm(confirmtitle)
          .then(async _ => {
            let res = await insertDailyReportConfirmList(par);
            if (res.data) {
              this.$message({ type: 'success', message: '保存成功!' });
            }
            this.dialogConfirmdata = false;
          })
          .catch(_ => { });
      },

      async showCalDayRepoty(){
        this.dialogCalDayRepotVis = true;
      },
      async calDayRepoty(){
        if(this.calDayRepotyDate==null)
      {
        this.$message({ type: 'warning', message: '请先选择日期!' });
        return
      }
        let res= await calDayRepoty({type:'FenXiao',yearMonthDay:this.calDayRepotyDate});
        if (!res?.success)  return
        this.$message({type: 'success',message: '正在计算中,请稍候...'});
        this.dialogCalDayRepotVis=false;
      },
      showEveryDayrefund(row){
        this.EveryDayrefund.filter.proCode = row.proCode;
        if (row.yearMonthDay != null){
          var dayStr =row.yearMonthDay.substr(0,4)+'-'+row.yearMonthDay.substr(4,2)+'-'+row.yearMonthDay.substr(6,2)
          this.EveryDayrefund.filter.timeRange = [dayStr,dayStr];
        }
          else {
          this.EveryDayrefund.filter.timeRange = this.filter.timerange
        }
        this.EveryDayrefund.visible=true;
         setTimeout(async () => {
          await this.$refs.EveryDayrefund.onSearch();
        }, 100);

      },
      datetostr(date) {
        var y = date.getFullYear();
        var m = ("0" + (date.getMonth() + 1)).slice(-2);
        var d = ("0" + date.getDate()).slice(-2);
        return y + "-" + m + "-" + d;
      },
      async init(){
          var date1 = new Date(); date1.setDate(date1.getDate()-1);
          var date2 = new Date(); date2.setDate(date2.getDate()-1);
          this.filter.timerange=[];
          this.filter.timerange[0]=this.datetostr(date1);
          this.filter.timerange[1]=this.datetostr(date2);
          console.log(this.filter)
        },
     async showprchart2(prcode,platform){
        window['lastseeprcodedrchart']=prcode
        window['lastseeprcodedrchart1']= platform
        window['lastseeprcodedrchart2'] = this.filter.refundType
        this.drparamProCode=prcode
        this.dialogDrVisible=true
     } ,
     async initformparm(){
        let that=this;
         this.autoformparm.rule= [{type:'hidden',field:'id',title:'id',value: '',col:{span:12}},
                       {type:'select',field:'groupId',title:'组长',value:'', update(val, rule){{that.updateruleGroup(val)}},...await ruleDirectorGroup(),props:{filterable:true}},
                       {type:'InputNumber',field:'Profit3PredictRate',title:'毛三预估比例%',value: null,props:{min:0,precision:3},col:{span:6}},
                       {type:'InputNumber',field:'ShareRate',title:'公摊费率%',value: null,props:{min:0,precision:3},col:{span:6}},
                      ]
      },
      onSeriesCoding(row) {
        this.remoteMethod(row.styleCode);
        this.styleCode = row.styleCode.split(',');
      },
      //系列编码远程搜索
      async remoteMethod(query){
        if (query !== ''){
            this.searchloading == true;
            this.options=[];
            setTimeout(async () => {
                const res = await getListByStyleCode({currentPage:1,pageSize:50, styleCode: query})
                console.log("系列编码远程搜索",res);
                this.searchloading = false
                res?.data?.forEach(f=>{
                this.options.push({value:f.styleCode,label:f.styleCode})
                });
            }, 200)
        }
        else{
            this.options = []
        }
      },
     async getShopList(){
        const res1 = await getAllShopList({platforms:[11]});
        this.shopList=res1.data?.map(item=>{return{value:item.shopCode,label:item.shopName};});
          // res1.data?.forEach(f => {
          //   if(f.isCalcSettlement&&f.shopCode)
          //       this.shopList.push(f);
          // });
          var res2= await getDirectorGroupList();
          this.grouplist = res2.data?.map(item => {return { value: item.key, label: item.value };});

          var res3= await getDirectorList();
          this.directorlist = res3.data?.map(item => {return { value: item.key, label: item.value };});

          var res4= await getAllProBrand();
          this.brandlist = res4.data.map(item => {
              return { value: item.key, label: item.value };
          });
      },
     async sortchange(column){
        if(!column.order)
          this.pager={};
        else
          this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
       await this.onSearch();
      },
      onRefresh(){
        this.onSearch()
      },
      async onSearch(){
      this.$refs.table.changecolumn_setTrue(["yearMonthDay"]);
      if(this.filter.groupType == 1 || this.filter.groupType == 2){
        this.$refs.table.changecolumn(["yearMonthDay"]);
      }
      this.$refs.pager.setPage(1);
      await this.getList().then(res=>{  });

      // loading.close();
    },
      async getList(){
        this.filter.startTime =null;
        this.filter.endTime =null;
        this.filter.listingStartTime = null;
      this.filter.listingEndTime = null;
        if (this.filter.timerange) {
          this.filter.startTime = this.filter.timerange[0];
          this.filter.endTime = this.filter.timerange[1];
        }
        if (this.filter.timerange1) {
        this.filter.listingStartTime = this.filter.timerange1[0];
        this.filter.listingEndTime = this.filter.timerange1[1];
      }
      if(this.filter.starNumber!=null&&this.filter.endNumber!=null)
      {
        if(this.filter.starNumber>this.filter.endNumber)
        {
          this.$message({ type: 'warning', message: '开始订单量值不能大于结束订单量值!' });
         return
        }
      }
        this.filter.styleCode = this.styleCode.join()
        this.filter.orderLx = this.filter.orderLxList.join(',')
        var that=this;
        var pager = this.$refs.pager.getPager();
        const params = {...pager,...this.pager,...this.filter};
       // this.listLoading = true;
        startLoading();
        const res = await pageProductDayReport(params).then(res=>{
            loading.close();
            if(res?.data?.list&&res?.data?.list.length>0){
              for (var i in res.data.list) {
                if (!res.data.list[i].freightFee) {
                  res.data.list[i].freightFee =" ";
                }
               if(that.filter.refundType == 2){
                res.data.list[i].RefundAmont = res.data.list[i].RefundAmontByPay;
                res.data.list[i].Profit3 = res.data.list[i].Profit3ByPay;
                res.data.list[i].Profit3Rate = res.data.list[i].Profit3RateByPay;
              }
              }
            }
            if(that.filter.refundType==2){
              res.data.summary.Profit3Rate_sum = res.data?.summary?.Profit3RateByPay_sum;
              res.data.summary.RefundAmontByPay = res.data?.summary?.RefundAmontByPay_sum;
              res.data.summary.Profit3ByPay = res.data?.summary?.Profit3ByPay_sum;
            }
            that.total = res.data?.total;
            that.financialreportlist = res.data?.list;
            that.$refs.table.loadRowEcharts();
            that.summaryarry=res.data?.summary;
        });
      },
      // showFreightDetail(row){
      //     if(row.freightFee>=1){
      //       this.freightDetail.filter.proCode = row.proCode;
      //       if (row.yearMonthDay != null){
      //         var dayStr =row.yearMonthDay.substr(0,4)+'-'+row.yearMonthDay.substr(4,2)+'-'+row.yearMonthDay.substr(6,2)
      //         this.freightDetail.filter.timeRange = [dayStr,dayStr];
      //       }
      //       else {
      //         this.freightDetail.filter.timeRange = this.filter.timerange
      //       }
      //       this.freightDetail.visible=true;
      //       setTimeout(async () => {
      //         await this.$refs.freightDetail.onSearch();
      //       }, 100);
      //     }
      // },
      showProcodesimilarity(row){
          if(row.styleCode!=null){
            this.$router.push({path: '/order/procodesimilarity', query: {styleCode: row.styleCode}})
          }
      },
      async showGiftDetail(row){
        var yearMonthDayStart=row.yearMonthDay
        var yearMonthDayEnd=row.yearMonthDay
        if(this.filter.groupType) {
          yearMonthDayStart= this.filter.timerange[0].replace("-","").replace("-","")
          yearMonthDayEnd= this.filter.timerange[1].replace("-","").replace("-","")
        }
        this.giftDetail.visible=true;
        let _th=this;
        await this.$nextTick(async () => {  await _th.$refs.ordergiftdetail.onShow(yearMonthDayStart,yearMonthDayEnd,row.proCode); });
      },
      selectchange:function(rows,row) {
        this.selids=[];
        rows.forEach(f=>{
          this.selids.push(f.id);
        })
      },
     onRefresh(){
          this.onSearch()
      },
    async updateruleGroup(groupid) {
       if(!groupid)
          this.autoformparm.fApi.resetFields()
       else{
         const res = await getParm({groupId:groupid})
         var arr = Object.keys(this.autoformparm.fApi);
         res.data.groupId=groupid;
         if(!res.data?.Profit3PredictRate) res.data.Profit3PredictRate=0;
         if(!res.data?.ShareRate) res.data.ShareRate=0;
         await this.autoformparm.fApi.setValue(res.data)
        }
      },
    async onstartImport(){
        this.dialogVisible=true;
      },
      uploadSuccess(response, file, fileList) {
        if (response.code == 200) {
        } else {
          fileList.splice(fileList.indexOf(file), 1);
        }
      },
      submitUpload() {
        if (!this.onimportfilter.yearmonthday) {
         this.$message({type: 'warning',message: '请选择年月!'});
         return;
        }
        this.uploadLoading=true
        this.$refs.upload.submit();
      },
     async uploadFile(item) {
       if(!item||!item.file||!item.file.size){
          this.$message({message: "请先上传文件", type: "warning" });
          return false;
        }
        const form = new FormData();
        form.append("upfile", item.file);
        form.append("platform", 11);
        form.append("yearmonthday", this.onimportfilter.yearmonthday);
        var res = await importProductDayReport(form);
        if (res.code==1) this.$message({message: "上传成功,正在导入中...", type: "success" });
        else this.$message({message: res.msg, type: "warning" });
        this.uploadLoading=false
      },
      onDerivationMethod(){
        let that = this
        if(that.filter.refundType < 3){
          that.$refs.table.setExportCols()
        }
      },
    async onExport(opt){
        this.filter.startTime =null;
        this.filter.endTime =null;
        if (this.filter.timerange) {
          this.filter.startTime = this.filter.timerange[0];
          this.filter.endTime = this.filter.timerange[1];
        }
        var pager = this.$refs.pager.getPager();
        const params = {  ...pager,   ...this.pager,   ...this.filter, ...opt};
        var res= await exportProductDayReport(params);
        if(!res?.data) {
           return
        }
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','分销日报数据' +  new Date().toLocaleString() + '_.xlsx' )
        aLink.click()
      },
    async onShowEditParm(){
        this.editparmVisible = true
        const res = await getParm()
        var arr = Object.keys(this.autoformparm.fApi);
        if(arr.length >0)
           this.autoformparm.fApi.resetFields()
        await this.autoformparm.fApi.setValue(res.data)
      },
    async onSetEditParm(){
        this.editparmLoading=true;
        await this.autoformparm.fApi.validate(async (valid, fail) => {
        if(valid){
            const formData = this.autoformparm.fApi.formData();
            const res = await setParm(formData);
            if(res.code==1) this.editparmVisible=false;
          }else{}
       })
       this.editparmLoading=false;
      },
      async onsummaryClick(property){
        let a = {}
        let YH_EXT_ExportColumns = []
        let YH_EXT_ExportCnColumns = []
        a = this.$refs.table.getColumnsInfo();
        a?.fullColumn.forEach((item)=>{
          if(item.title && item.field){
            YH_EXT_ExportColumns.push(item.field)
            YH_EXT_ExportCnColumns.push(item.title)
          }
        })
        // this.$message({message:property,type:"warning"});
          this.filter.startTime =null;
          this.filter.endTime =null;
          if (this.filter.timerange) {
            this.filter.startTime = this.filter.timerange[0];
            this.filter.endTime = this.filter.timerange[1];
          }
          var pager = this.$refs.pager.getPager();
          const params = {...pager,...this.pager,...this.filter};
          params.column=property;
          params.YH_EXT_ExportColumns = YH_EXT_ExportColumns
          params.YH_EXT_ExportCnColumns = YH_EXT_ExportCnColumns
          let that=this;
          that.buscharDialog.loading= true;
          that.listLoading = true;
          const res = await queryDayReportAnalysis(params).then(res=>{
            that.buscharDialog.loading= false;
            that.listLoading = false;
            that.buscharDialog.data=res.data
            that.buscharDialog.title=res.data.legend[0]
            that.buscharDialog.visible=true;
          });
          await that.$refs.buschar.initcharts();
      },
    showexpressfreightanalysis() {
        this.expressfreightanalysisVisible=true;
        this.$nextTick(() => { this.$refs.expressfreightanalysis.onSearch()});
      },
    renderCost(row){
      if(row.replaceSendCost >0){
        return "color:blue;cursor:pointer;";
      }
      else{
        return "";
      }
    },
    showupload() {
       this.drawervisible=true;
     }
     ,async callbackProCode(val) {
          this.filter.proCode = val;
      },
    }
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  ::v-deep .el-select__tags-text {
    max-width: 30px;
  }
  </style>

