<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <inputYunhan ref="refgoodsCodes" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes" width="200px"
          placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500" :maxlength="1000000"
          @callback="goodsCodesCallback($event, 1)" title="商品编码" style="margin:0 2px 0 0;" class="publicCss">
        </inputYunhan>
        <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
        <inputYunhan title="款式编码" placeholder="款式编码/多行输入请按回车" :maxRows="2000" :inputshow="0" :clearable="true"
          :clearabletext="true" :showRowCount="true" :showBlank="false" @callback="goodsCodesCallback($event, 2)"
          :inputt.sync="ListInfo.styleCodes" v-model="ListInfo.styleCodes" :maxlength="30000" class="publicCss"
          style="margin:0 2px 0 0;" width="200px">
        </inputYunhan>
        <inputYunhan title="包材编码" placeholder="包材编码/多行输入请按回车" :maxRows="2000" :inputshow="0" :clearable="true"
          :clearabletext="true" :showRowCount="true" :showBlank="false" @callback="goodsCodesCallback($event, 3)"
          :inputt.sync="ListInfo.packMtlCodes" v-model="ListInfo.packMtlCodes" :maxlength="30000" class="publicCss"
          style="margin:0 2px 0 0;" width="200px">
        </inputYunhan>
        <el-input v-model.trim="ListInfo.packMtl" placeholder="包材名称" maxlength="50" clearable class="publicCss" />
        <el-select v-model="ListInfo.isMaintain" clearable filterable placeholder="是否已维护" class="publicCss"
          style="width: 110px;">
          <el-option label="是" :value="true" />
          <el-option label="否" :value="false" />
        </el-select>
        <number-range :min.sync="ListInfo.orderCountPayMin" :max.sync="ListInfo.orderCountPayMax" min-label="销量 - 最小值"
          max-label="销量 - 最大值" class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
        <el-button type="primary" size="mini" @click="importProps">导入包材</el-button>
        <el-button type="primary" @click="PackagSet"
          v-if="checkPermission('PackagingMaterialSettings')">包材设置</el-button>
      </div>
    </template>
    <vxetablebase ref="table" :id="'packagingMaterials202505091710'" :tablekey="'packagingMaterials202505091710'"
      :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true" :has-seq="false"
      :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false" :is-select-column="true"
      :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%" :showsummary="data.summary ? true : false"
      :summaryarry="data.summary" @sortchange="sortchange" @select="selectCheckBox" />
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div slot="title" class="header-title">
        <span class="title-text"><span>导入数据</span></span>
        <span class="title-close"><el-button @click="downLoadFile">下载模版</el-button></span>
      </div>
      <div>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog title="包材设置" :visible.sync="PackagSetVisible" width="20%" v-dialogDrag>
      <el-form :model="PackagSetForm" :rules="rules2" ref="ruleForm2" label-width="120px" class="demo-ruleForm"
        v-if="PackagSetVisible" v-loading="packagSetFormLoading">
        <el-form-item label="包装材料" prop="packMtlCode">
          <el-select v-model="PackagSetForm.packMtlCode" placeholder="请选择包装材料" style="width: 200px;" clearable
            filterable>
            <el-option v-for="item in basicData" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="PackagSetVisible = false">关闭</el-button>
          <el-button type="primary" @click="PackagSetSubmit('ruleForm2')" v-throttle="1000">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode, downloadLink } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/SaleItems/Weight/PackMtl/'
const api1 = '/api/verifyOrder/SaleItems/Weight/'
import { mergeTableCols } from '@/utils/getCols'
import { getList as getGoodsList } from "@/api/inventory/basicgoods"
export default {
  name: "packagingMaterials",
  components: {
    MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan
  },
  data() {
    return {
      api,
      api1,
      platformlist,
      uploadLoading: false,
      fileList: [],
      file: null,
      fileparm: {},
      importVisible: false,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        summarys: [],
        goodsCodes: '',
        goodsName: '',
        styleCodes: '',
        packMtlCodes: '',
        packMtl: '',
        isMaintain: false,
        orderCountPayMin: undefined,
        orderCountPayMax: undefined
      },
      PackagSetForm: {
        packMtlCode: ''
      },
      rules2: {
        packMtlCode: [{ required: true, message: '请选择包装材料', trigger: 'change' }],
      },
      basicData: [],
      PackagSetVisible: false,
      packagSetFormLoading: false,
      selectList: [],//选中行
      data: {},
      tableCols: [],
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
      isExport: false,
    }
  },
  async mounted() {
    await this.getCol();
    await this.getList()
    await this.init()
  },
  methods: {
    // 导出数据,这里前端可以封装一个方法
    async exportProps() {
        this.isExport = true
        await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
            this.isExport = false
        })
    },
    async init() {
      const params = {
        currentPage: 1,
        pageSize: 2000,
        brandId: ['5'],
        groupId: ['23'],
        unBrandId: ["23", "12", "141", "150", "10101"]
      }
      const { data, success } = await getGoodsList(params)
      if (success) {
        this.basicData = data.list.map(item => {
          return {
            label: item.goodsCode + ' - ' + item.goodsName,
            value: item.goodsCode
          }
        })
      }
    },
    PackagSetSubmit(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const res = this.selectList.map(item => {
            return {
              packMtlCode: this.PackagSetForm.packMtlCode,
              goodsCode: item.goodsCode,
            }
          })
          this.packagSetFormLoading = true
          const { success } = await request.post(`/api/verifyOrder/SaleItems/Weight/BatchEditPackMtl`, res)
          this.packagSetFormLoading = false
          if (success) {
            this.$message({
              type: 'success',
              message: '保存成功!'
            });
            this.PackagSetVisible = false
            this.getList()
            this.selectList = []
          }
        } else {
          return false;
        }
      });
    },
    PackagSet() {
      if (this.selectList.length == 0) return this.$message.error('请选择要设置的数据!')
      this.PackagSetForm.packMtlCode = null
      this.PackagSetVisible = true
    },
    downLoadFile() {
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250216/1891025598059085825.xlsx', '包装耗材导入模版.xlsx');
    },
    importProps() {
      this.fileList = []
      this.file = null
      this.importVisible = true
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.importVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("file", item.file);
      var res = await request.post(`${this.api1}ImportPackMtl`, form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.importVisible = false;
      await this.getList()
    },
    goodsCodesCallback(val, type) {
      if (type == 1) {
        this.ListInfo.goodsCodes = val;
      } else if (type == 2) {
        this.ListInfo.styleCodes = val;
      } else if (type == 3) {
        this.ListInfo.packMtlCodes = val;
      }
    },
    selectCheckBox(val) {
      this.selectList = JSON.parse(JSON.stringify(val))
    },
    async getCol() {
      const { data, success } = await request.post(`${this.api}GetColumns`)
      if (success) {
        data.forEach(item => {
          if (item.prop == 'orderNoInner') {
            item.type = 'orderLogInfo'
            item.orderType = 'orderNoInner'
          }
          if (item.prop == 'proCode') {
            item.type = 'html'
            item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
          }
        })
        let a = mergeTableCols(data)
        a.unshift({ label: '', type: 'checkbox' })
        this.tableCols = a
        this.ListInfo.summarys = data
          .filter((a) => a.summaryType)
          .map((a) => {
            return { column: a["sort-by"], summaryType: a.summaryType };
          });
      }
    },
    async getList(type) {
      if (type === "search") {
        this.ListInfo.currentPage = 1;
        this.$refs.pager.setPage(1);
      }
      this.loading = true;
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
        if (success) {
          this.data = data;
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.loading = false;
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 5px;
  align-items: center;

  .publicCss {
    width: 200px;
    margin-right: 5px;
  }
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px 0 0;

  .title-text {
    display: flex;
    align-items: center;

    .title-close {
      margin-left: 10px;
    }
  }
}
</style>
