<template>
    <MyContainer>
        <template #header>
            <div class="header_top">
                <el-date-picker v-model="ListInfo.timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="审核通过开始日期" end-placeholder="审核通过结束日期" style="width: 340px;margin-right: 10px;"
                    format="yyyy-MM-dd" :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                <el-select style="width:120px" v-model="ListInfo.auditState" placeholder="状态" clearable
                    class="publicCss">
                    <el-option v-for="item in auditStatus" :key=item.value :value=item.value
                        :label=item.label></el-option>
                </el-select>
                <el-select v-model="ListInfo.newPlatforms" placeholder="运营平台" :clearable="true" :collapse-tags="true"
                    multiple style="width: 120px" filterable class="publicCss">
                    <el-option v-for="item in platformlist" :key="'plat-' + item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-input v-model.trim="ListInfo.yyGroupName" maxlength="50" clearable placeholder="运营组"
                    class="publicCss" @keyup.enter.native="getList('click')" style="width: 140px" />
                <el-input v-model="ListInfo.goodsCompeteId" placeholder="竞品ID" clearable maxlength="50"
                    class="publicCss" style="width: 140px" />
                <el-input v-model="ListInfo.goodsCompeteName" placeholder="竞品标题" clearable maxlength="50"
                    class="publicCss" style="width: 140px" />
                <el-input v-model="ListInfo.goodsCompeteShortName" placeholder="产品简称" clearable maxlength="50"
                    class="publicCss" style="width: 140px" />
                <el-select v-model="ListInfo.assignedDDUserId" placeholder="核价人员" :clearable="true"
                    :collapse-tags="true" style="width: 120px" filterable class="publicCss"
                    :disabled="!checkPermission('yyxpxpchhjry')">
                    <el-option v-for="item in hejiaList" :key="item.ddUserId" :label="item.userName"
                        :value="item.ddUserId" />
                </el-select>
                <el-button type="primary" @click="getList('click')">搜索</el-button>
                <el-button type="primary" @click="openSet">设置</el-button>

                <el-button type="primary" @click="onShowAutoSet">自动分配核价设置</el-button>
                <el-button type="primary" @click="onExport">导出</el-button>
                <el-button type="primary" @click="onExportApprovalRecord">导出商品明细</el-button>
            </div>
        </template>
        <vxetablebase :id="'newGoodsAudit202408041726'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' :summaryarry="summaryarry" :showsummary='true' @sortchange='sortchange'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            v-loading="listLoading" style="width: 100%; height: 690px; margin: 0" />
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />

        <el-dialog title="设置" :visible.sync="dialogVisible" width="20%" v-dialogDrag>
            <div class="dialogBox">
                <div class="dialogTop">
                    <div>设置自动通过时间</div>
                    <!-- <el-input v-model="hourInt" type="number" placeholder="时间" style="width: 100px;margin: 0 5px;"
                        @input="changeTime"></el-input> -->
                    <el-input-number v-model="hourInt" type="number" placeholder="时间" :controls="false" :max="48"
                        :min="0" :precision="0" style="width: 100px;margin: 0 5px;"></el-input-number>
                    <div>小时</div>
                </div>
                <div style="color: red;margin-top: 10px;">审核通过时间+自动通过时间，系统默认通过</div>
                <div class="btnBox">
                    <el-button @click="dialogVisible = false" style="margin-right: 20px;">取消</el-button>
                    <el-button type="primary" @click="setTimeSubmit">确定</el-button>
                </div>
            </div>
        </el-dialog>

        <el-dialog title="修改价格" :visible.sync="editPriceVisible" width="40%" v-dialogDrag>
            <div class="dialogBox1">
                <div class="dialogBox_top" v-if="dialogDetail.supplierPlatForm == 1">
                    <div class="dialogBox_publicCss">
                        <div class="top_item">
                            <div class="item_left">供应商名称</div>
                            <el-tooltip class="item" effect="dark" content="微信" placement="top-start">
                                <div>微信</div>
                            </el-tooltip>
                        </div>
                        <div class="top_item">
                            <div class="item_left">微信账号</div>
                            <el-tooltip class="item" effect="dark" :content="dialogDetail.supplierWxNum"
                                placement="top-start">
                                <div>{{ dialogDetail.supplierWxNum }}</div>
                            </el-tooltip>
                        </div>
                    </div>
                    <div class="dialogBox_publicCss">
                        <div class="top_item">
                            <div class="item_left">供应商名称</div>
                            <el-tooltip class="item" effect="dark" :content="dialogDetail.supplierName"
                                placement="top-start">
                                <div>{{ dialogDetail.supplierName }}</div>
                            </el-tooltip>
                        </div>
                    </div>
                </div>
                <div class="dialogBox_top" v-else>
                    <div class="dialogBox_publicCss">
                        <div class="top_item">
                            <div class="item_left">供应商名称</div>
                            <el-tooltip class="item" effect="dark" :content="dialogDetail.supplierName"
                                placement="top-start">
                                <div>{{ dialogDetail.supplierName }}</div>
                            </el-tooltip>
                        </div>
                        <div class="top_item">
                            <div class="item_left">供应商平台</div>
                            <el-tooltip class="item" effect="dark" content="1688" placement="top-start">
                                <div>1688</div>
                            </el-tooltip>
                        </div>
                    </div>
                    <div class="dialogBox_publicCss">
                        <div class="top_item">
                            <div class="item_left">供应商链接</div>
                            <el-tooltip class="item" effect="dark" :content="dialogDetail.supplierLink"
                                placement="top-start">
                                <div>{{ dialogDetail.supplierLink }}</div>
                            </el-tooltip>
                        </div>
                        <div class="top_item">
                            <div class="item_left">产品链接</div>
                            <el-tooltip class="item" effect="dark" :content="dialogDetail.supplierGoodLink"
                                placement="top-start">
                                <div>{{ dialogDetail.supplierGoodLink }}</div>
                            </el-tooltip>
                        </div>
                    </div>
                </div>

                <el-table :data="dialogDetail.goodsDtls" max-height="250" border style="width: 100%">
                    <el-table-column prop="date" label="商品图片">
                        <template slot-scope="scope">
                            <el-image :src="scope.row.goodsImageUrl" :preview-src-list="[scope.row.goodsImageUrl]"
                                class="imgcss" maxlength="4">
                            </el-image>
                        </template>
                    </el-table-column>
                    <el-table-column prop="yhGoodsCode" label="商品编码">
                    </el-table-column>
                    <el-table-column prop="yhGoodsName" label="商品名称">
                    </el-table-column>
                    <el-table-column prop="costPrice" label="原成本单价">
                    </el-table-column>
                    <el-table-column prop="firstApproveCostPrice" label="新成本单价">
                        <template slot-scope="scope">
                            <!-- <el-input v-model="scope.row.firstApproveCostPrice" type="number" placeholder="新成本单价" clearable
                                @input="changePrice($event, scope.$index)" /> -->
                            <el-input-number v-model="scope.row.firstApproveCostPrice" type="number" placeholder="新成本单价"
                                clearable :controls="false" :precision="3" :max="900000" :min="0" :disabled="isView"
                                @input="changePrice($event, scope.$index)" />
                        </template>
                    </el-table-column>
                </el-table>
                <div style="margin-top: 10px;">降价供应商</div>
                <div style="margin: 10px 0;">原供应商降价</div>
                <div style="color: red;">核价截图or聊天截图</div>
                <div>
                    <uploadimgFile v-if="editPriceVisible" ref="uploadimgFile" :disabled="!formEditMode"
                        :noDel="!formEditMode" :accepttyes="accepttyes" :isImage="true" :uploadInfo="chatUrls"
                        :keys="[1, 1]" @callback="getImg" :filemaxsize="9" :imgmaxsize="9" :limit="9" :multiple="true">
                    </uploadimgFile>
                </div>
                <div style="margin: 10px 0;">附件表格</div>
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-success="handleSuccess" :file-list="FileList" list-type="text" accept=".xlsx">
                    <el-tooltip class="item" effect="dark" content="最多只能上传一个.xlsx文件" placement="top-start">
                        <el-button class="addsc" type="primary">上传表格</el-button>
                    </el-tooltip>
                </el-upload>
                <div class="btnBox">
                    <el-button @click="editPriceVisible = false" style="margin-right: 20px;">取消</el-button>
                    <el-button type="primary" @click="editPriceSubmit" :disabled="isView"
                        v-throttle="3000">提交申请</el-button>
                </div>
            </div>
            <!-- <div> -->

            <!-- </div> -->
        </el-dialog>

        <el-dialog title="自动分配采购设置（勾选则代表该核价员参与自动分配）" :visible.sync="autoSetVisible" width="40%"
            :close-on-click-modal="false" v-loading="autoSetListLoading" element-loading-text="拼命加载中" v-dialogDrag
            append-to-body>
            <template>
                <el-container style="height:580px;">
                    <el-table ref="autoSetTable" :data="autoSetTableList" @select='autosetselectchange'
                        @select-all="autosetselectall" :height="580">
                        <el-table-column type="selection" width="55">
                        </el-table-column>
                        <el-table-column prop="id" label="id" width="55" v-if="false" />
                        <el-table-column prop="setIndex" label="分配顺序" width="100" align="center">
                            <template slot-scope="scope">
                                <el-input v-model="scope.row.setIndex" maxlength="4"
                                    oninput="value=value.replace(/[^\d]/g,'')">
                                </el-input>
                            </template>
                        </el-table-column>
                        <el-table-column prop="userName" label="核价员姓名" width="120" align="center" />
                        <el-table-column prop="area" label="区域" width="80" align="center" />
                        <el-table-column prop="title" label="岗位名称" width="80" align="center" />
                        <el-table-column prop="employeeStatus" label="状态" width="80" align="center" />
                        <el-table-column prop="topCount" label="设置上限" width="100" align="center">
                            <template slot-scope="scope">
                                <el-input v-model="scope.row.topCount" maxlength="4"
                                    oninput="value=value.replace(/[^\d]/g,'')">
                                </el-input>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-container>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="autoSetVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onOkAutoSet()">保 存</el-button>
                    <el-tooltip effect="dark" placement="top" content="一键分配历史未被分配(手动/自动)的新品初核">
                        <el-button type="primary" @click="onOkAutoSet(true)">保存&一键分配</el-button>
                    </el-tooltip>
                </span>
            </template>
        </el-dialog>
        <el-dialog title="分配核价员" :visible.sync="assignedVisible" width="20%" v-loading="assignedLoading" v-dialogDrag
            append-to-body>
            <div class="dialogBox">
                <div class="dialogTop" style="-webkit-box-pack:left">
                    <el-select v-model="assignedSel" placeholder="核价员" clearable style="width: 200px" collapse-tags>
                        <el-option v-for="item in assignedList" :key="item.ddUserId" :label="item.userName"
                            :value="item.ddUserId" />
                    </el-select>
                </div>
                <div class="btnBox" style="padding-top: 50px;">
                    <el-button @click="assignedVisible = false" style="margin-right: 20px;">取消</el-button>
                    <el-button type="primary" @click="onAssignedSubmit">确定</el-button>
                </div>
            </div>
        </el-dialog>
        <el-dialog title="驳回" :visible.sync="rejectDialog.visible" width="20%" v-dialogDrag append-to-body>
            <div class="dialogBox">
                <div class="dialogTop">
                    <el-select v-model="rejectDialog.data.reason" placeholder="请选择驳回原因" clearable style="width:96%">
                        <el-option label="新品重复" value="新品重复" />
                        <el-option label="价格填写错误" value="价格填写错误" />
                        <el-option label="其他原因" value="其他原因" />
                    </el-select>
                    <i style="color:red">*</i>
                </div>
                <div class="btnBox">
                    <el-button @click="rejectDialog.visible = false" style="margin-right: 20px;">取消</el-button>
                    <el-button type="primary" @click="rejectRecord">确定</el-button>
                </div>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { platformlist, formatLinkProCode } from "@/utils/tools";
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import { replaceSpace } from '@/utils/getCols'
import checkPermission from '@/utils/permission'
import { rulePlatform } from "@/utils/formruletools";
import { formatTime } from "@/utils/tools";
import hotsalegoodsbuildgoodsdoccaigou from '@/views/operatemanage/productalllink/skuenquiry/hotsalegoodsbuildgoodsdoccaigou.vue';
import {
    getUserInfo,
    pageHotSaleGoodsFirstApproveAsync, exportHotSaleGoodsFirstApproveAsync, exportGoodsDetail_GoodsFirstApproveAsync,
    setHotSaleGoodsFirstApproveAutoTime,
    setHotSaleGoodsFirstApproveYH,
    getHotSaleGoodsFirstApproveById,
    saveHotSaleGoodsFirstApprove,
    getSetHotSaleGoodsFirstApproveAutoTime,
    getGenerateFirstAutoSetList, saveGenerateFirstAutoSetList, onekeyGenerateFirstAutoSetList, setHotyGenerateFirstAssignedSingle,
    rejectHotSaleGoodsFirstApprove
} from '@/api/operatemanage/productalllink/alllink.js'
const auditStatus = [
    { label: "已拒绝", value: -1 },
    { label: "未核", value: 0 },
    { label: "审批中", value: 1 },
    { label: "已核", value: 2 },
]
const tableCols = [
    { istrue: true, prop: 'auditStateName', label: '状态' },
    { istrue: true, prop: 'goodsCompeteId', label: '竞品ID', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.goodsCompeteId) },
    { istrue: true, prop: 'goodsCompeteName', label: '竞品标题', sortable: 'custom' },
    { istrue: true, prop: 'supplierName', label: '供应商名称', sortable: 'custom' },
    { istrue: true, prop: 'goodsCompeteShortName', label: '产品简称', sortable: 'custom' },
    { istrue: true, prop: 'goodsCompeteImgUrl', label: '竞品图片', type: 'images' },
    {
        istrue: true, prop: 'goodsState', label: '产品状态', sortable: 'custom', align: 'center',
        formatter: row => { return row.goodsState == 1 ? "新品" : row.goodsState == 2 ? "老品补SKU" : row.goodsState == 3 ? "待拍" : "" }
    },
    { istrue: true, prop: 'platform', label: '平台', sortable: 'custom', formatter: (row) => row.platformName || ' ' },
    { istrue: true, prop: 'newPlatform', label: '运营平台', sortable: 'custom', formatter: (row) => row.newPlatformName || ' ' },
    { istrue: true, prop: 'yyGroupName', label: '运营组', sortable: false },
    { istrue: true, prop: 'assignedUserName', label: '核价人员' },
    { istrue: true, prop: 'patentQualificationImgUrls', label: '专利资质', type: 'images' },
    { istrue: true, prop: 'patentQualificationPdfUrls', label: '专利PDF', type: 'files' },
    {
        istrue: true, prop: 'outerPackaLanguage', label: '外包装语言', width: '80',
        formatter: (row) => (row.outerPackaLanguage == -1 ? "无" : row.outerPackaLanguage == 1 ? "中文" : row.outerPackaLanguage == 2 ? "英文" : "")
    },
    { istrue: true, prop: 'packingImgUrls', label: '包装图片', type: 'images' },
    { istrue: true, prop: 'isSyncJST', label: '是否同步聚水潭', formatter: row => row.isSyncJST == 1 ? "是" : "否" },
    { istrue: true, prop: 'auditTime', label: '审核通过时间', width: '150', sortable: 'custom', formatter: (row) => row.auditTime == null ? null : formatTime(row.auditTime, 'YYYY-MM-DD HH:mm:ss') },
    { istrue: true, prop: 'oldCostPrice', label: '核价幅度', width: '80', sortable: 'custom', formatter: (row) => row.auditState == 1 || row.auditState == 2 ? row.oldCostPrice : null },

    {
        istrue: true, type: 'button', label: '操作', width: '230', btnList: [
            { label: "修改价格", display: (row) => row.auditState == 1 || row.auditState == 2 || row.isCheckApprove != 0, handle: (that, row) => that.editPrice(row, false) },
            { label: "已核", display: (row) => row.auditState == 1 || row.auditState == 2 || row.isCheckApprove != 0, handle: (that, row) => that.editStatus(row) },
            { label: "查看", handle: (that, row) => that.editPrice(row, true) },
            {
                label: "分配核价", display: (row) => (row.auditStateName != "未核" || row.isCheckApprove == -2),
                handle: (that, row) => that.onAssignedUserName(row)
            },
            {
                label: "驳回", display: (row) => row.auditState == 1 || row.auditState == 2 || row.isCheckApprove != 0, handle: (that, row) => that.showReject(row, true)
            }
        ]
    },
]

export default {
    name: "newGoodsAudit",
    components: { MyContainer, vxetablebase, hotsalegoodsbuildgoodsdoccaigou, uploadimgFile },
    data() {
        return {
            that: this,
            tableCols,
            tableData: [],
            summaryarry: {},
            platformlist,
            hejiaList: [],
            auditStatus,
            FileList: [],
            chatUrls: [],
            ListInfo: {
                timeRanges: [],
                AuditStartTime: null,
                AuditEndTime: null,
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                auditState: null,//状态
                newPlatforms: null,//运营平台
                yyGroupName: null,//运营组
                goodsCompeteId: null,//竞品ID
                goodsCompeteName: null,//竞品标题
                goodsCompeteShortName: null,//产品简称
                assignedDDUserId: null,
            },
            dialogVisible: false,//设置
            editPriceVisible: false,//修改价格
            hourInt: null,
            total: 0,
            listLoading: false,
            dialogTitle: '',
            buildgoodscaigouVisible: false,
            dialogBuildgoodscaigouLoading: false,
            buildgoodscaigouIsEditMode: false,
            dialogDetail: {},
            formEditMode: true,
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            isView: false,

            autoSetVisible: false,
            autoSetListLoading: false,
            autoSetTableList: [],

            assignedVisible: false,
            assignedLoading: false,
            assignedList: [],
            assignedSelDataId: 0,
            assignedSelDataSupplierId: 0,
            assignedSel: null,

            rejectDialog: {
                visible: false,
                data: {
                    reason: "",
                    id: 0
                }
            }
        }
    },
    mounted() {
        this.getHeJiaList();
        this.getList();
    },
    methods: {
        async getHeJiaList() {
            const res = await getGenerateFirstAutoSetList();
            this.hejiaList = res.data;
        },
        changePrice(e, i) {
            this.dialogDetail.goodsDtls[i].firstApproveCostPrice = e
            if (this.dialogDetail.goodsDtls[i].firstApproveCostPrice.length > 8) {
                this.dialogDetail.goodsDtls[i].firstApproveCostPrice = this.dialogDetail.goodsDtls[i].firstApproveCostPrice.slice(0, 6)
            }
        },
        changeTime(e) {
            //超过五位就截断
            if (e.length > 5) {
                this.hourInt = e.slice(0, 5)
            }
        },
        getImg(data) {
            this.dialogDetail.chatUrls = data.map(item => item.url)
            this.dialogDetail.chatUrls = this.dialogDetail.chatUrls.join(',')
        },
        async handleSuccess({ data }) {
            const { url } = data
            this.dialogDetail.attachmentExcel = url
        },
        uploadRemove() {
            this.fileList = []
        },
        async editPriceSubmit() {
            this.dialogDetail.goodsDtls.forEach(item => {
                item.firstApproveCostPrice = Number(item.firstApproveCostPrice)
            })
            if (this.dialogDetail.chatUrls == []) {
                this.dialogDetail.chatUrls = ''
            }
            this.dialogDetail.goodsDtls.forEach(item => {
                if (item.firstApproveCostPrice == 0 || item.firstApproveCostPrice == '' || item.firstApproveCostPrice == null) {
                    item.firstApproveCostPrice = null
                }
                if (item.firstApproveCostPrice < 0) {
                    this.$message.error('新成本单价不能小于0')
                    throw ('新成本单价不能小于0')
                }
            })
            this.dialogDetail.saveAndAppply = true
            // this.dialogDetail.saveAndAppply = false
            const { success } = await saveHotSaleGoodsFirstApprove(this.dialogDetail)
            if (success) {
                this.$message.success('修改成功')
                this.getList()
                this.editPriceVisible = false
            }
        },
        editStatus(row) {
            this.$confirm('此操作将改变状态为已核, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await setHotSaleGoodsFirstApproveYH({ id: row.id, supplierId: row.supplierId })
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '设置成功!'
                    });
                    this.getList()
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        async editPrice(row, type) {
            this.isView = type ? true : false
            this.chatUrls = []
            this.FileList = []
            this.$nextTick(() => {
                this.dialogDetail = null
            })
            const { data, success } = await getHotSaleGoodsFirstApproveById({ id: row.id, supplierId: row.supplierId })
            if (success) {
                this.dialogDetail = data
                this.dialogDetail.chatUrls = this.dialogDetail.chatUrls ? this.dialogDetail.chatUrls : null
                if (this.dialogDetail.attachmentExcel != null && this.dialogDetail.attachmentExcel != '') {
                    this.FileList = [
                        {
                            name: this.dialogDetail.attachmentExcel ? this.dialogDetail.attachmentExcel : '表格文件',
                            url: this.dialogDetail.attachmentExcel
                        }
                    ]
                }
                if (this.dialogDetail.chatUrls) {
                    this.chatUrls = this.dialogDetail.chatUrls.split(',').map(item => {
                        return {
                            url: item,
                            fileName: item
                        }
                    })
                }
            }
            this.editPriceVisible = true
        },
        async setTimeSubmit() {
            console.log(this.hourInt, 'this.hourInt');
            if (!this.hourInt || this.hourInt < 0) return this.$message.error('设置时间不能为空或小于0')
            this.hourInt = Number(this.hourInt)
            const { success } = await setHotSaleGoodsFirstApproveAutoTime(this.hourInt)
            if (success) {
                this.$message.success('设置成功')
                this.dialogVisible = false
                this.getList()
            }
        },
        //页面数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList();
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },
        async openSet() {
            const { data, success } = await getSetHotSaleGoodsFirstApproveAutoTime()
            if (success) {
                this.hourInt = data ? data : null
            }
            this.dialogVisible = true
        },
        getParam(type) {
            this.ListInfo.AuditStartTime = null;
            this.ListInfo.AuditEndTime = null;
            if (this.ListInfo.timeRanges) {
                this.ListInfo.AuditStartTime = this.ListInfo.timeRanges[0];
                this.ListInfo.AuditEndTime = this.ListInfo.timeRanges[1];
            }

            if (type == 'click') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            const replaceArr = ['goodsCompeteId', 'goodsCompeteName', 'goodsCompeteShortName', 'yyGroupName']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
        },
        async getList(type) {
            //没有权限的人进来只能看到自己的
            if (!checkPermission('yyxpxpchhjry')) {
                let my = await getUserInfo();
                this.ListInfo.assignedDDUserId = my.data.ddUserId;
            }
            this.getParam(type);
            this.listLoading = true
            const { data, success } = await pageHotSaleGoodsFirstApproveAsync(this.ListInfo)
            if (!success) return
            this.tableData = data.list
            this.total = data.total
            this.summaryarry = data.summary
            this.listLoading = false
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        autosetselectchange: function (rows, row) {
            //先把当前也的数据全部移除
            this.selAutoSetTableList = [];
            this.index = 1
            // //把选中的添加
            rows.forEach(f => {
                let index = this.selAutoSetTableList.findIndex((v) => (v === f.brandId));
                if (index === -1) {
                    this.selAutoSetTableList.push(f);
                }
            });
        },
        autosetselectall: function (rows) {
            this.selAutoSetTableList = rows;
        },
        async onShowAutoSet() {
            this.autoSetVisible = true;
            this.selAutoSetTableList = [];
            this.autoSetListLoading = true;
            const res = await getGenerateFirstAutoSetList();
            this.autoSetListLoading = false;
            if (res?.success) {
                this.autoSetTableList = res.data;
                if (this.autoSetTableList && this.autoSetTableList.length > 0) {
                    this.autoSetTableList.forEach(f => {
                        if (f.id > 0) {
                            this.selAutoSetTableList.push(f);
                        }
                    });
                    this.$nextTick(() => {
                        this.selAutoSetTableList.forEach(f => {
                            if (f.id > 0) {
                                this.$refs.autoSetTable.toggleRowSelection(f, true);
                            }
                        });
                    });
                }
            }
        },
        async onOkAutoSet(isFenPei) {
            this.autoSetListLoading = true;
            const res = await saveGenerateFirstAutoSetList(this.selAutoSetTableList);
            this.autoSetListLoading = false;
            if (res?.success) {
                if (isFenPei == true) {
                    this.autoSetListLoading = true;
                    const res2 = await onekeyGenerateFirstAutoSetList(this.selAutoSetTableList);
                    this.autoSetListLoading = false;
                    if (res2?.success) {
                        this.$message.success('保存&一键分配成功');
                        this.getList('click');
                    }
                }
                else {
                    this.$message.success('保存成功');
                }
            }
        },
        async onAssignedUserName(row) {
            this.assignedVisible = true;
            this.assignedSelDataId = row.id;
            this.assignedSelDataSupplierId = row.supplierId;
            this.assignedLoading = true;
            const res = await getGenerateFirstAutoSetList();
            this.assignedLoading = false;
            this.assignedList = res.data;
        },
        async onAssignedSubmit() {
            let find = this.assignedList.find(f => f.ddUserId == this.assignedSel);
            if (!find) {
                this.$message.error('选择核价员错误');
                return;
            }
            if (!this.assignedSelDataId) {
                this.$message.error('获取数据源失败，请刷新后充实');
                return;
            }
            let param = {
                id: this.assignedSelDataId,
                supplierId: this.assignedSelDataSupplierId,
                assignedDDUserId: this.assignedSel
            };
            const res = await setHotyGenerateFirstAssignedSingle(param);
            if (res?.success) {
                this.$message.success('分配成功');
                this.getList('click');
                this.assignedVisible = false;
            }
        },
        async onExportApprovalRecord() {
            this.listLoading = true
            const res = await exportGoodsDetail_GoodsFirstApproveAsync(this.ListInfo)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '新品初核-商品明细' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        async onExport() {
            //没有权限的人进来只能看到自己的
            if (!checkPermission('yyxpxpchhjry')) {
                let my = await getUserInfo();
                this.ListInfo.assignedDDUserId = my.data.ddUserId;
            }
            this.getParam('click');
            this.listLoading = true
            const res = await exportHotSaleGoodsFirstApproveAsync(this.ListInfo);
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '新品初核_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()

        },
        async showReject(row) {
            this.rejectDialog.visible = true;
            this.rejectDialog.data.id = row.id;
            this.rejectDialog.data.reason = null;
        },
        async rejectRecord() {
            if (this.rejectDialog.data.reason == '' || this.rejectDialog.data.reason == null) {
                this.$message.error('请选择驳回原因');
                return;
            }
            let par = {
                id: this.rejectDialog.data.id,
                rejectReason: this.rejectDialog.data.reason,
            }
            const res = await rejectHotSaleGoodsFirstApprove(par);
            if (res?.success) {
                this.$message.success('驳回成功');
                this.getList('click');
                this.rejectDialog.visible = false;
            }
        }
    }
}
</script>

<style scoped lang="scss">
.header_top {
    display: flex;
    margin-bottom: 10px;
}

.publicCss {
    width: 220px;
    margin-right: 10px;
}

.dialogBox {
    display: flex;
    flex-direction: column;



    .dialogTop {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
    }

    .btnBox {
        display: flex;
        justify-content: center;
        margin-top: 20px;
    }
}

.dialogBox1 {

    .dialogBox_top {
        height: 60px;
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 30px;

        .dialogBox_publicCss {
            width: 100%;
            display: flex;

            .top_item {
                width: 50%;
                display: flex;
                align-items: center;

                .item,
                .item_left {
                    width: 50%;
                    height: 40px;
                    line-height: 40px;
                    border: 1px solid #797575;
                    box-sizing: border-box;
                    border-left: none; // 移除 .item 的左边框
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .item_left {
                    background-color: #efefef;
                    font-weight: 700;
                    font-family: 'Microsoft YaHei';
                    border-left: 1px solid #797575; // 为 .item_left 设置右边框
                }
            }
        }
    }

    .btnBox {
        display: flex;
        justify-content: center;
        margin-top: 20px;
    }
}
</style>
