<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                    <el-input style="width: 200px" v-model="filter.serialNumberOrder" placeholder="订单号" clearable />
                </el-button>

                <el-button type="primary" @click="onSearch()">查询</el-button>
                <el-button type="primary" @click="onImportShow()">导入</el-button>
                <el-button type="primary" @click="onImportModel()">下载模板</el-button>
                <el-button type="primary" @click="onGetData()">拉取收入支出</el-button>
                <el-button type="primary" @click="onAlipayGetData()">拉取支付宝收入支出</el-button>

            </el-button-group>
        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            @select='selectchange' :isSelection='false' :isSelectColumn='false' :tableData='list' :tableCols='tableCols'
            :showsummary='true' :summaryarry='summaryarry' :loading="listLoading" />

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="特殊单导入" :visible.sync="dialogVisibleSyj" width="30%" :close-on-click-modal="false"
            v-dialogDrag>
            <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2" :on-change="onUploadChange2"
                :on-remove="onUploadRemove2">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <my-confirm-button style="margin-left: 10px;" size="small" type="success" :loading="dialogLoadingSyj"
                    @click="onSubmitupload2">上传</my-confirm-button>
            </el-upload>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import { importSpecialOrders, } from '@/api/monthbookkeeper/import'
import { GetSpecialOrdersPageList, GetSpecialOrdersAmount, updateSpecialOrderIncomeAmountInfo } from '@/api/monthbookkeeper/financialDetail'
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
const tableCols = [
    { istrue: true, prop: 'orderNo', label: '订单号', sortable: 'custom', width: '300' },
];
export default {
    name: 'SpecialOrderImport',
    components: {MyContainer,  cesTable, MyConfirmButton },
    data() {
        return {
            that: this,
            filter:{},
            list: [],
            tableCols: tableCols,
            pager: { OrderBy: "orderNo", IsAsc: false },
            summaryarry: {},
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            pageLoading: false,


            dialogVisibleSyj: false,
            dialogLoadingSyj: false,
            fileList: [],
            improtForm: {
            },
        }
    },
    mounted() {
        this.onSearch();
    },
    methods: {
        onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }
            this.listLoading = true
            const res = await GetSpecialOrdersPageList(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res.data?.total ?? 0
            const data = res.data?.list
            data?.forEach(d => {
                d._loading = false
            })
            this.list = data
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },


        onImportModel() {
            window.open("/static/excel/book/特殊单回款查询-特殊单导入.xlsx", "_blank");
        },
        async onImportShow() {
            this.dialogVisibleSyj = true;
        },
        async onUploadChange2(file, fileList) {
            this.fileList = fileList;
        },
        async onUploadRemove2(file, fileList) {
            this.fileList = [];
        },
        async uploadFile2(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.dialogLoadingSyj = true;
            const form = new FormData();
            form.append("upfile", item.file);
            const res = await importSpecialOrders(form);
            this.dialogLoadingSyj = false;
            if (res?.success) {
                this.$message({ message: '上传成功', type: "success" });
                this.dialogVisibleSyj = false;
                this.onSearch();
            }
        },
        async uploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        async onSubmitupload2() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload2.submit();
        },
        onAlipayGetData(){
            this.$confirm(`确定要根据最新的特殊单订单号拉取支付宝收入支出数据吗?`, '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await updateSpecialOrderIncomeAmountInfo();
                if(res.success)
                this.$message({ message: "操作成功，正在后台拉取中.....", type: "success" });
            }).catch(() => {
            });
        },
        async onGetData(){
            this.$confirm(`确定要根据最新的特殊单订单号拉取数据吗?`, '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {

                const res = await GetSpecialOrdersAmount();
                if(res.success)
                this.$message({ message: "操作成功，正在后台拉取中.....稍后在“特殊单收入支出”界面刷新查看", type: "success" });

            }).catch(() => {
            });
        }
    }
}
</script>
