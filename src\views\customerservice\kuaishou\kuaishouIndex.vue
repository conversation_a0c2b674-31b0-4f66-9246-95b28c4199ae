<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" :before-leave="beforeLeave" style="height:94%;">
            <el-tab-pane name="switch">
                <span slot="label">
                    <el-switch v-model="switchshow" :disabled="switchshowPermission" @change="changeShowgroup"
                        active-text="售后管理" inactive-text="售前管理">
                    </el-switch>
                </span>
            </el-tab-pane>
            <!-- 售前 -->
            <el-tab-pane v-if="!switchshowSqSh" label="分组管理(售前)" name="tab1" style="height: 100%;">
                <sqgroup :filter="filter" ref="sqgroup" style="height: 100%;" @callBackInfo="handleInfo"></sqgroup>
            </el-tab-pane>
            <el-tab-pane v-if="!switchshowSqSh" label="咨询数据导入(售前)" name="tab2" style="height: 100%;">
                <sqinquirs :filter="filter" ref="sqinquirs" style="height: 100%;"></sqinquirs>
            </el-tab-pane>
            <el-tab-pane v-if="!switchshowSqSh" label="组效率统计(售前)" name="tab3" style="height: 100%;">
                <sqgroupinquirsstatistics :filter="filter" ref="sqgroupinquirsstatistics" style="height: 100%;"
                    :partInfo="infoBool"></sqgroupinquirsstatistics>
            </el-tab-pane>
            <el-tab-pane v-if="!switchshowSqSh" label="店效率统计(售前)" ref="tab4" name="tab4" style="height: 100%;">
                <sqstoreinquirsstatistics :filter="filter" ref="sqinquirsstatisticsmonth" style="height: 100%;"
                    :partInfo="infoBool"></sqstoreinquirsstatistics>
            </el-tab-pane>
            <el-tab-pane v-if="!switchshowSqSh" label="个人效率统计(售前)" name="tab5" style="height: 100%;">
                <sqinquirsstatistics :filter="filter" ref="sqinquirsstatistics" style="height: 100%;"
                    :partInfo="infoBool"></sqinquirsstatistics>
            </el-tab-pane>

            <el-tab-pane v-if="!switchshowSqSh" label="未匹配咨询数据" name="tab6" style="height: 100%;" lazy>
                <kuaishouinquirsno :filter="filter" ref="kuaishouinquirsno" style="height: 100%;" />
            </el-tab-pane>
            <!-- 售后 -->
            <el-tab-pane v-if="switchshowSqSh" label="分组管理(售后)" name="tab11" style="height: 100%;">
                <shgroup :filter="filter" ref="shgroup" style="height: 100%;" @callBackInfoH="handleInfoH"></shgroup>
            </el-tab-pane>
            <el-tab-pane v-if="switchshowSqSh" label="咨询数据导入(售后)" name="tab22" style="height: 100%;">
                <shinquirs :filter="filter" ref="shinquirs" style="height: 100%;"></shinquirs>
            </el-tab-pane>
            <el-tab-pane v-if="switchshowSqSh" label="组效率统计(售后)" name="tab33" style="height: 100%;">
                <shgroupinquirsstatistics :filter="filter" ref="shgroupinquirsstatistics" style="height: 100%;"
                    :partInfo="infoBoolH"></shgroupinquirsstatistics>
            </el-tab-pane>
            <el-tab-pane v-if="switchshowSqSh" label="店效率统计(售后)" ref="tab44" name="tab14" style="height: 100%;">
                <shstoreinquirsstatistics :filter="filter" ref="shinquirsstatistics" style="height: 100%;"
                    :partInfo="infoBoolH"></shstoreinquirsstatistics>
            </el-tab-pane>
            <el-tab-pane v-if="switchshowSqSh" label="个人效率统计(售后)" name="tab55" style="height: 100%;">
                <shinquirsstatistics :filter="filter" ref="shinquirsstatistics" style="height: 100%;"
                    :partInfo="infoBoolH"></shinquirsstatistics>
            </el-tab-pane>

            <el-tab-pane v-if="switchshowSqSh" label="未匹配咨询数据" name="tab60" style="height: 100%;" lazy>
                <kuaishouinquirsno1 :filter="filter" ref="kuaishouinquirsno1" style="height: 100%;" />
            </el-tab-pane>

        </el-tabs>
    </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";
import sqgroup from "@/views/customerservice/kuaishou/sq/sqgroup";
import sqgroupinquirsstatistics from "@/views/customerservice/kuaishou/sq/sqgroupinquirsstatistics";
import sqinquirs from "@/views/customerservice/kuaishou/sq/sqinquirs";
import sqinquirsstatistics from "@/views/customerservice/kuaishou/sq/sqinquirsstatistics";
import sqstoreinquirsstatistics from "@/views/customerservice/kuaishou/sq/sqstoreinquirsstatistics";
import kuaishouinquirsno from "@/views/customerservice/kuaishou/kuaishouinquirsno";

import shgroup from "@/views/customerservice/kuaishou/sh/shgroup";
import shgroupinquirsstatistics from "@/views/customerservice/kuaishou/sh/shgroupinquirsstatistics";
import shinquirs from "@/views/customerservice/kuaishou/sh/shinquirs";
import shinquirsstatistics from "@/views/customerservice/kuaishou/sh/shinquirsstatistics";
import shstoreinquirsstatistics from "@/views/customerservice/kuaishou/sh/shstoreinquirsstatistics";
import kuaishouinquirsno1 from "@/views/customerservice/kuaishou/kuaishouinquirsno1";




export default {
    name: "Users",
    components: {
        MyContainer, sqgroup, sqgroupinquirsstatistics, sqinquirs, sqinquirsstatistics, sqstoreinquirsstatistics, kuaishouinquirsno,
        shgroup, shgroupinquirsstatistics, shinquirs, shinquirsstatistics, shstoreinquirsstatistics, kuaishouinquirsno1,

    },
    data() {
        return {
            that: this,
            pageLoading: '',
            filter: {
            },
            shopList: [],
            userList: [],
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            activeName: 'tab1',

            //默认展示售前 true售前，false售后
            switchshow: false,
            switchshowSqSh: false,
            switchshowPermission: false,
            infoBool: false,//是否包含离组
            infoBoolH: false,
        };
    },
    mounted() {
        window.showtab5 = this.showtab5
        window.showtab55 = this.showtab55
    },
    methods: {
        showtab5() {
            this.activeName = "tab5"
        },
        showtab55() {
            this.activeName = "tab55"
        },
        beforeLeave(visitName, currentName) {
            if (visitName == "switch")
                return false;
        },
        changeShowgroup() {
            if (!this.switchshow) {
                this.activeName = 'tab1';
                this.switchshowSqSh = false;
            }
            else {
                this.activeName = 'tab11';
                this.switchshowSqSh = true;
            }
        },
        handleInfo(data) {
            this.infoBool = data
        },
        handleInfoH(data) {
            this.infoBoolH = data
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
