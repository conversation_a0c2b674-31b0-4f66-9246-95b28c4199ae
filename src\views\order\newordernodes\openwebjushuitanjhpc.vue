<template >
    <my-container v-loading="pageLoading" style="height:100%">
        <ces-table style="height:90%" ref="openwebjushuitanjhpctable" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='jhpcList' :isSelection='false' :summaryarry="summaryarry"
            :tableCols='tableCols' :isSelectColumn="true">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-date-picker style="width: 225px" v-model="filter.dates" type="daterange" format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始生成时间"
                            end-placeholder="结束生成时间">
                        </el-date-picker>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="filter.warehouse" clearable filterable placeholder="发货仓" style="width: 200px">
                            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input placeholder="批次号" v-model="filter.wave_id" style="width: 130px" clearable
                            oninput="if(value){value=value.replace(/[^\-\d]/g,'')} if(value>2147483647){value=2147483647} if(value<0){value=0}"
                            maxlength="10"></el-input>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getNewOrderListAsync" />
        </template>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import { getJstJhpcPageList, getTbWarehouseList } from '@/api/inventory/openwebjushuitan';
const tableCols = [
    { istrue: true, prop: 'createdTime', label: '同步时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'wave_id', label: '批次号', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'type', label: '类型', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'picker_name', label: '拣货员', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'order_count', label: '订单数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'sku_qty', label: '商品总数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'status', label: '状态', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'wms_co_name', label: '仓库', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'created', label: '生成时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'issend', label: '是否通知', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'sendtime', label: '通知时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'sendusername', label: '通知人员', width: '100', sortable: 'custom' },
];
export default {
    name: "openwebjushuitanjhpc",
    components: { cesTable, MyContainer, datepicker },
    props: {

    },
    data() {
        return {
            warehouselist: [],
            pageLoading: false,
            tableCols: tableCols,
            jhpcList: [],
            that: this,
            sels: [], // 列表选中列
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "createdTime", IsAsc: false },
            filter: {
                dates: [formatTime(dayjs(), "YYYY-MM-DD"), formatTime(dayjs(), "YYYY-MM-DD")],
                sdate: null,
                edate: null,
                warehouse: null,
                wave_id: null,
            },
        }
    },
    async mounted() {
        await this.getInventoryWareHouseList()
        this.onSearch();
    },
    methods: {
        async getInventoryWareHouseList() {
            if (this.warehouselist.length <= 0) {
                let wares = await getTbWarehouseList();
                if (wares?.success && wares?.data && wares?.data.length > 0) {
                    wares?.data.forEach(f => {
                        if (f.manager_ddid)
                            this.warehouselist.push({ value: f.wms_co_id, label: f.name });
                    });
                }
            }
        },
        //排序查询      
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getNewOrderListAsync();
        },
        async getNewOrderListAsync() {
            if (this.filter.dates && this.filter.dates.length > 1) {
                this.filter.sdate = this.filter.dates[0];
                this.filter.edate = this.filter.dates[1];
            }
            else {
                this.filter.sdate = null;
                this.filter.edate = null;
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            this.pageLoading = true;
            let res = await getJstJhpcPageList(params);
            this.pageLoading = false;
            if (res?.success) {
                this.total = res.data.total
                this.jhpcList = res.data.list;
                //this.summaryarry = res.summary;
            }
        },
    }
}
</script>