<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" ref="form" label-width="120px" label-position="right" > 
                <el-form-item label="平台：">
                            <el-radio-group v-model="form.extData4">
                                <el-radio :label="0" disabled >拼多多</el-radio>
                                <el-radio :label="1" disabled >淘系</el-radio>                                
                                <el-radio :label="6" disabled >抖音</el-radio>  
                                <el-radio :label="14" disabled >快手</el-radio>
                                <el-radio :label="7" disabled>京东</el-radio>
                                <el-radio :label="20" disabled>视频号</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="线上单号(可以逗号或换行分开)：">
                            <el-input v-model="form.oids" type="textarea" :rows="2"></el-input>
                        </el-form-item>  
                        <el-form-item label="扣款时间区间：">
                            <el-date-picker
                            v-model="form.extData5"
                            type="date" value-format="yyyy-MM-dd"
                            placeholder="选择日期">
                            </el-date-picker>
                            -
                            <el-date-picker
                            v-model="form.extData6"
                            type="date" value-format="yyyy-MM-dd"
                            placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="是否重算：">
                            <el-radio-group v-model="form.extData1">
                                <el-radio :label="0" >否</el-radio>
                                <el-radio :label="1" >是</el-radio>                                
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="重算内容：">
                            <el-radio-group v-model="form.extData3">
                                <el-radio :label="0" >全部(订单日志、责任、运营采购仓库快递等信息)</el-radio>
                                <el-radio :label="1" >仅责任</el-radio>                                
                                <el-radio :label="2" >仅运营采购仓库快递等信息</el-radio>                                
                            </el-radio-group>
                        </el-form-item>    
                        <el-form-item>
                            <span style="color:red">
                                1、已申诉过的无法重算
                                <br/>
                                2、正常情况下重算无法改变责任结果，除非修订过责任规则                                
                            </span>
                        </el-form-item>          
                      
              
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">  
                    <el-button @click="onClose">关闭</el-button>                     
                    <el-button  type="primary" @click="onSave(true)">提交重算</el-button>  
                </el-col>
            </el-row>
        </template>

    </my-container>
</template>
<script>  

    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode } from "@/utils/tools";
    import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import { AddDeductPddOrder2Queue4Sql } from "@/api/order/orderdeductmoney";


    export default {
        name: "OrderDeductZrRecalcForm",
        components: { MyContainer, MyConfirmButton  },
        data() {
            return {              
                that: this,
                mode:3,
                illegalTypeList:[],
                zrDeptActions:[
                    {zrDeptAction:'仓库',zrDept:'仓库',zrAction:'仓库'},
                    {zrDeptAction:'仓库审单',zrDept:'仓库',zrAction:'审单'},
                    {zrDeptAction:'仓库库维',zrDept:'仓库',zrAction:'库维'},
                    {zrDeptAction:'运营',zrDept:'运营',zrAction:'运营'},
                    {zrDeptAction:'采购',zrDept:'采购',zrAction:'采购'},
                    {zrDeptAction:'快递',zrDept:'快递',zrAction:'快递'},
                ],
                form: {
                   
                },
            
                //summaryarry: {},
                pageLoading: false,
                curRow: null,
                formEditMode: true,//是否编辑模式              
                dialogHisVisible:false,
                isTx:false,      
            };
        },
        async mounted() {          
        },
        computed: {    
        },
        methods: {              
            onClose(){
                this.$emit('close');
            },  
            async onSave(isClose){
                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData({ orderNo, occTime, platform,mode}) {     
                let self=this;    
                self.formEditMode = mode!=3;
                self.mode = mode;     
                
                this.form= {
                    "extData4":platform,
                    "oids":orderNo,
                    "extData5":occTime,
                    "extData6":occTime,
                    "extData1":1 ,
                    "extData3":1 ,
                };
            },
            async save() {
                this.pageLoading = true;
                
                let saveData = { ...this.form };   

                let rlt = await AddDeductPddOrder2Queue4Sql(saveData);
                if (rlt && rlt.success) {
                    this.$message.success('提交成功！');           
                }

                this.pageLoading = false;
              
                return (rlt && rlt.success);
            }
        },
    };
</script>
<style lang="scss" scoped>
   
</style>