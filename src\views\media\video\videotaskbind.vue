<template>
    <div id="videobind" >
        <i class="el-icon-refresh iconsize" @click.stop="refresh"></i>
        <div v-for="(item,i) in comments" :key="i" class="author-title reply-father">
            <div class="author-info">
                <span class="author-time textsmall">{{item.time}}</span>
            </div>
            <div class="icon-btn">
                <span>
                    <i class="iconfont el-icon-position" @click="closelist(i,item)"></i>{{item.reply.length}}</span>
                <i class="iconfont el-icon-caret-top"  @click="closelist(i,item)"></i>
            </div>
            <div class="talk-box">
                <p>
                    <div class="reply" v-html="item.comment"  @click="editorClick"></div>
                </p>
            </div>
                <div class="reply-box" v-show="item.openshow">
                <div v-for="(reply,j) in item.reply" :key="j" class="author-title">
                    <div class="author-info">
                        <span class="author-time textsmall">{{reply.time}}</span>
                    </div>
                    <div class="icon-btn">
                        <span v-show="!disabled" @click="showReplyInput(i,reply.name,reply.id)"><i class="iconfont el-icon-s-comment" ></i></span>
                        <span>{{reply.commentNum}}<i class="iconfont el-icon-caret-top"  @click="twocloselist(j,reply)"></i></span>
                    </div>
                    <div class="talk-box">
                        <p>
                            <span>{{reply.name}}:</span>
                            <div class="reply" v-html="reply.comment"  @click="editorClick"></div>
                        </p>
                    </div>

                    <div v-show="reply.inputShow"  class="my-reply my-comment-reply">
                        <div class="reply-info">
                            <div tabindex="0" id="replyInput" @paste="changeContent"  contenteditable="true" spellcheck="false" :placeholder="placetext"
                            @input="onDivInput($event)" class="reply-input reply-comment-input"></div>
                                <!-- //输入框 @input="onDivInput($event)" -->
                            <!-- <input @paste="pasting"> -->
                        </div>
                        <div class=" reply-btn-box">
                            <el-button class="reply-btn" size="mini" @click="sendComment(i)" type="primary">{{puttext}}</el-button>
                        </div>
                    </div>
                    <div v-show="reply.openshow">
                        <div class="twotalk-box" v-for="(reply,j) in reply.reply"  :key="j">
                        <div class="author-info">
                            <span class="author-time textsmall">{{reply.time}}</span>
                        </div>
                        <div class="icon-btn">
                        </div>

                        <!-- <div v-show="reply.inputShow"  class="my-reply my-comment-reply">
                            <el-avatar class="header-img" :size="30" :src="myHeader"></el-avatar>
                            <div class="reply-info">
                                <div tabindex="0" id="tworeplyInput" contenteditable="true" spellcheck="false" :placeholder="placetext"
                                    @input="twoonDivInput($event)" class="reply-input reply-comment-input"></div>
                            </div>
                            <div class=" reply-btn-box">
                                <el-button class="reply-btn" size="mini" @click="sendCommentReply(i)" type="primary">{{puttext}}</el-button>
                            </div>
                        </div> -->



                        <div class="talk-box">
                            <p>
                                <span>{{reply.name}}:</span>
                                <span class="reply" v-html="reply.comment"  @click="editorClick"></span>
                            </p>
                        </div>


                    </div>
                    </div>
                </div>
            </div>
        </div>
        <el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeFunc" style="z-index:9999;" />

    </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer';
import {saveReplyCommentInfo} from '@/api/media/video';
import { container, ImageExtend, QuillWatch } from 'quill-image-extend-module'
    const clickoutside = {
        bind(el, binding, vnode) {
            function documentHandler(e) {
                if (el.contains(e.target)) {
                    return false;
                }
                if (binding.expression) {
                    binding.value(e);
                }
            }

            el.vueClickOutside = documentHandler;
            document.addEventListener('click', documentHandler);
        },
        // update() {
        // },
        unbind(el, binding) {
            document.removeEventListener('click', el.vueClickOutside);
            delete el.vueClickOutside;
        },
    };
    export default {
        name: 'ArticleComment',
        components: {ElImageViewer},
        props: {
            commendList: {
                type: Array,
                required: true,
            },
            disabled: {
                type: '',
                required: false,
            },
        },
        // props: [commendList],
        data() {
            return {
                activeNames: ['1'],
                placetext: '输入评论...',
                putplacetext: '输入评论...',
                replyopenclose: false,
                btnShow: false,
                index: '0',
                replyComment: '',
                tworeplyComment: '',
                myName: '',
                myHeader: 'https://ae01.alicdn.com/kf/Hdd856ae4c81545d2b51fa0c209f7aa28Z.jpg',
                myId: 19870621,
                to: '',
                toId: -1,
                imgList: [],
                showGoodsImage: false,
                puttext: '发表评论',
                comments: this.commendList,
                fortime: 0,
                outthree: [],
                replyCommentt: '',
                imgurllist: [],
                changeContentis: false
            }
        },
        directives: {clickoutside},
        watch: {
            commendList:{
                deep: true,
                handler(v) {
                    this.comments = v
                }
            }
        },
        mounted(){
           this.threetoall(this.comments)
        },
        methods: {
            suanfa(){
                let _this = this;
                if(this.replyComment.length!=null&&this.imgurllist.length!=null){
                    let mapnew = _this.replyComment.map((item,index)=>{
                        return item = item.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi, function (match, capture) {
                            _this.bingval = _this.bingval.replace(capture,_this.imgurllist[index])
                            // _this.replyCommentt = newarr
                            return  _this.bingval
                        });
                        
                    })
                    // this.replyCommentt = mapnew
                }
                

            },
            parseURL(url) {
                var a = document.createElement('a');
                a.href = url;
                return {
                    source: url,
                    protocol: a.protocol.replace(':', ''),
                    host: a.hostname,
                    port: a.port,
                    query: a.search,
                    params: (function () {
                        var ret = {},
                        seg = a.search.replace(/^\?/, '').split('&'),
                        len = seg.length, i = 0, s;
                        for (; i < len; i++) {
                            if (!seg[i]) { continue; }
                            s = seg[i].split('=');
                            ret[s[0]] = s[1];
                        }
                        return ret;

                    })(),
                    file: (a.pathname.match(/\/([^\/?#]+)$/i) || [, ''])[1],
                    hash: a.hash.replace('#', ''),
                    path: a.pathname.replace(/^([^\/])/, '/$1'),
                    relative: (a.href.match(/tps?:\/\/[^\/]+(.+)/) || [, ''])[1],
                    segments: a.pathname.replace(/^\//, '').split('/')
                };
            },
            replaceUrlParams(myUrl, newParams) {
                /*
                    for (var x in myUrl.params) {
                        for (var y in newParams) {
                            if (x.toLowerCase() == y.toLowerCase()) {
                                myUrl.params[x] = newParams[y];
                            }
                        }
                    }
                    */

                    for (var x in newParams) {
                        var hasInMyUrlParams = false;
                        for (var y in myUrl.params) {
                            if (x.toLowerCase() == y.toLowerCase()) {
                                myUrl.params[y] = newParams[x];
                                hasInMyUrlParams = true;
                                break;
                            }
                        }
                        //原来没有的参数则追加
                        if (!hasInMyUrlParams) {
                            myUrl.params[x] = newParams[x];
                        }
                    }
                    var _result = myUrl.protocol + "://" + myUrl.host + ":" + myUrl.port + myUrl.path + "?";

                    for (var p in myUrl.params) {
                        _result += (p + "=" + myUrl.params[p] + "&");
                    }

                    if (_result.substr(_result.length - 1) == "&") {
                        _result = _result.substr(0, _result.length - 1);
                    }

                    if (myUrl.hash != "") {
                        _result += "#" + myUrl.hash;
                    }
                    return _result;
            },
            //辅助输出
            w(str) {
                document.write(str + "");
            },
            changeContent(e){
                let _this = this;
                const dataTransferItemList = e.clipboardData.items;
                // 过滤非图片类型
                const items = [].slice.call(dataTransferItemList).filter(function (item) {
                    return item.type.indexOf('image') !== -1;
                });
                console.log("item",items)
                if (items.length === 0) {
                    return;
                }
                const dataTransferItem = items[0];
                const file = dataTransferItem.getAsFile();
                _this.uploadToServer(file, (res) => {
                    this.imgurllist.push(res.data.url)
                    _this.changeContentis = true;
                    // this.suanfa()
                })
                
                // if(this.replyComment.length>0){
                //     this.replyComment.map((item,i)=>{

                //         let srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/i;
                //         let imgurl=item.match(srcReg)[i];

                //         _this.replyCommentt=_this.replyComment.replace(imgurl,res.data.url);
                        
                //     })
                // }       
            },
            pasting(e){
                const file = e.clipboardData.files[0]
                const windowURL = window.URL || window.webkitURL
                let a = windowURL.createObjectURL(file)
            },
            handleChange(val) {
                console.log(val);
            },
              messageTitleClick(num) {
                console.log("点击了消息", num);
            },
            inputFocus() {
                var replyInput = document.getElementById('replyInput');
                replyInput.style.padding = "8px 8px"
                replyInput.style.border = "2px solid #409EFF"
                replyInput.focus()
            },
            showReplyBtn() {
                this.btnShow = true
            },
            hideReplyBtn() {
                 let replyInput = document.getElementById('replyInput')
                this.btnShow = false
                replyInput.style.padding = "10px"
                replyInput.style.border = "none"
            },
            showReplyInput(i, name, id) {
                this.toId = id
                let a = this.arrList(this.comments,id)
                // this.placetext = '@'+name+'输入评论...'
                this.placetext = '输入评论...'

            },
            _inputShow(i) {
                return this.comments[i].inputShow
            },
            _twoinputShow(i) {
                return this.comments[i].inputShow
            },
            _openShow(i) {
                return this.comments[i].openshow
            },
            async sendComment(val) {
                let _this = this;
                if(_this.changeContentis){
                     await this.suanfa();
                }
                _this.replyCommentt = _this.bingval
                if (!this.replyCommentt) {
                    this.$message({
                        showClose: true,
                        type: 'warning',
                        message: '评论不能为空'
                    })
                } else {
                    let a = {}
                    // let input = document.getElementById('replyInput')
                    // input.innerHTML = '';

                    let params = {
                        ToId: this.toId,
                        comment: this.replyCommentt
                    }
                    let res = await saveReplyCommentInfo(params);
                    if (!res?.success) {
                        return
                    }

                    _this.replyCommentt = ''
                    _this.imgurllist = []
                    _this.bingval = ''
                    _this.changeContentis= false
                    let msglength = document.getElementsByClassName("reply-comment-input").length
                    for(var i =0; i<msglength; i++){
                        document.getElementsByClassName("reply-comment-input")[i].innerHTML = ''
                    }
                    this.refresh(1)

                }
            },
            // 展开关闭
            closelist(i,form){
                console.log("点击收缩列表",[i,form])
                this.comments[i].openshow = !this.comments[i].openshow
                this.comments[i].inputShow = false
            },
            twocloselist(i,form){
                let a = this.openarrList(this.comments,form.id)
            },
            threetoall(array){
                this.fortime =this.fortime +1;
                array.map((item,index)=>{
                    if(item.reply.length>0&&this.fortime<=2){
                        this.threetoall(item.reply)
                        return
                    }
                    if(item.reply.length>0&&this.fortime>2){
                        console.log("遍历大于3数组",item)
                        console.log("遍历大于3数组index",index)
                        // this.comments.push(item);
                    }
                })
            },
            arrList(arr,code) {
                arr.map(element => {
                    if(element.id !== code){
                        element.inputShow = false
                    }
                    if(element.id === code){
                        element.inputShow = !element.inputShow
                        return element
                    }
                    if(element.reply.length>0){
                        this.arrList(element.reply,code)
                    }
                });
            },
            openarrList(arr,code) {
                // debugger
                arr.map(element => {
                    if(element.id === code){
                        element.openshow = !element.openshow
                        return element
                    }
                    if(element.reply.length>0){
                        this.openarrList(element.reply,code)
                    }
                });
            },
            // async sendCommentReply(i) {
            //     if (!this.tworeplyCommentt) {
            //         this.$message({
            //             showClose: true,
            //             type: 'warning',
            //             message: '评论不能为空'
            //         })
            //     } else {
            //         let params = {
            //             ToId: this.toId,
            //             comment: this.tworeplyCommentt
            //         }
            //         let res = await saveReplyCommentInfo(params);
            //         if (!res?.success) {
            //             return
            //         }

            //         this.refresh(1)
            //         this.tworeplyCommentt = ''
            //         document.getElementsByClassName("reply-comment-input")[i].innerHTML = ""
            //     }
            // },
            onDivInput: function (e) {
                let filterImgContent = e.target.innerHTML.replace(/\<img/g,"<img style='width:50px;height:50px;'")
                this.bingval = filterImgContent;
                // this.replyComment = filterImgContent;
                        // let newmsg = e.target.innerHTML.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi, function (match, capture) {
                        //     let newarr = e.target.innerHTML.replace(capture,_this.imgurllist[index])
                        //     // _this.replyCommentt = newarr
                        //     return newarr
                        // });
                        

                let DomList=document.getElementById('videobind').querySelectorAll('img')
                for(let i in  DomList){
                        if( DomList[i].style){
                            DomList[i].style.width='50px'
                            DomList[i].style.height='50px'
                        }
                    }
                    let _this = this;
                    this.replyComment = filterImgContent.match(/<img.*?>/g);
            },
            uploadToServer(file, callback) {
                var xhr = new XMLHttpRequest()
                var formData = new FormData()
                formData.append('file', file)
                xhr.open('post', '/api/uploadnew/file/UploadCommonFileAsync')
                xhr.withCredentials = true
                xhr.responseType = 'json'
                xhr.send(formData)
                xhr.onreadystatechange = () => {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                        // debugger;
                        callback(xhr.response)
                    }
                }
            },
            twoonDivInput: function (e) {
                let filterImgContent = e.target.innerHTML.replace(/\<img/g,"<img style='width:15%;height:15%;'")
                this.tworeplyComment = filterImgContent;
                let twoDomList=document.getElementById('tworeplyInput').querySelectorAll('img')
                for(let i in  twoDomList){
                        if( twoDomList[i].style){
                            twoDomList[i].style.width='10%'
                            twoDomList[i].style.height='10%'
                        }
                    }
            },
            deletelist(i,data){
                console.log("点击删除",[i,data])
            },
            //完成表单界面显示图片
            async showImg(e) {
                this.showGoodsImage = true;
                this.imgList = [];
                if (e) {
                    this.imgList.push(e);
                }
                else {
                    this.imgList.push(this.imagedefault);
                }
            },
            //完成表单界面关闭图片
            async closeFunc() {
                this.showGoodsImage = false;
            },
            async editorClick(e) {
                if (e.target.nodeName.toLocaleLowerCase() == 'img') {
                    this.showImg(e.target.src);
                }
            },
            async refresh(val){
                if(val==1){
                    this.$message({ message: '评论成功', type: "success" });
                    await this.$emit('getList')
                }else{
                    this.$message({ message: '刷新成功', type: "success" });
                    await this.$emit('getList')
                }
            },
            dateStr(date) {
                var time = new Date().getTime();
                time = parseInt((time - date) / 1000);
                var s;
                if (time < 60 * 10) {
                    return '刚刚';
                } else if ((time < 60 * 60) && (time >= 60 * 10)) {
                    s = Math.floor(time / 60);
                    return s + "分钟前";
                } else if ((time < 60 * 60 * 24) && (time >= 60 * 60)) {
                    s = Math.floor(time / 60 / 60);
                    return s + "小时前";
                } else if ((time < 60 * 60 * 24 * 30) && (time >= 60 * 60 * 24)) {
                    s = Math.floor(time / 60 / 60 / 24);
                    return s + "天前";
                } else {
                    // var date = new Date(parseInt(date));
                    date = new Date(parseInt(date));
                    return date.getFullYear() + "/" + (date.getMonth() + 1) + "/" + date.getDate();
                }
            }
        },
    }
</script>

<style scoped lang="scss">
    .my-reply {
        padding: 10px;
        background-color: #fafbfc;
    }

    .my-reply .header-img {
        display: inline-block;
        vertical-align: top;
    }

    .my-reply .reply-info {
        display: inline-block;
        margin-left: 5px;
        width: 90%;
    }

    @media screen and (max-width: 1200px) {
        .my-reply .reply-info {
            width: 80%;
        }
    }

    .my-reply .reply-info .reply-input {
        min-height: 20px;
        line-height: 22px;
        padding: 10px 10px;
        color: #ccc;
        background-color: #fff;
        border-radius: 5px;
    }

    .my-reply .reply-info .reply-input:empty:before {
        content: attr(placeholder);
    }

    .my-reply .reply-info .reply-input:focus:before {
        content: none;
    }

    .my-reply .reply-info .reply-input:focus {
        padding: 8px 8px;
        border: 2px solid #409EFF;
        box-shadow: none;
        outline: none;
    }

    .my-reply .reply-btn-box {
        height: 25px;
        margin: 10px 0;
    }

    .my-reply .reply-btn-box .reply-btn {
        position: relative;
        float: right;
        margin-right: 15px;
    }

    .my-comment-reply {
        margin-left: 50px;
    }

    .my-comment-reply .reply-input {
        width: flex;
    }

    .author-title:not(:last-child) {
        border-bottom: 1px solid rgba(178, 186, 194, 0.3);
    }

    .author-title {
        /* margin-top: 1rem; */
        padding: 10px;
    }

    .author-title .header-img {
        display: inline-block;
        vertical-align: top;
    }

    .author-title .author-info {
        display: inline-block;
        margin-left: 5px;
        width: 60%;
        /* height: 40px; */
        line-height: 20px;
    }

    .author-title .author-info > span {
        display: block;
        cursor: pointer;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .author-title .author-info .author-name {
        color: #409EFF;
        font-size: 18px;
        font-weight: bold;
    }

    .author-title .author-info .author-time {
        font-size: 14px;
    }

    .author-title .icon-btn {
        width: 30%;
        padding: 0 !important;
        float: right;
    }

    @media screen and (max-width: 1200px) {
        .author-title .icon-btn {
            width: 20%;
            padding: 7px;
        }
    }

    .author-title .icon-btn > span {
        cursor: pointer;
    }

    .author-title .icon-btn .iconfont {
        margin: 0 5px;
    }

    .author-title .talk-box {
        margin: 0 0;
    }

    .author-title .talk-box > p {
        margin: 0;
    }

    .author-title .talk-box .reply {
        font-size: 16px;
        color: #000;
        overflow:hidden;
        word-break:break-all;
        word-wrap:break-word;
    }

    .author-title .reply-box {
        margin: 10px 0 0 50px;
        background-color: #efefef;
    }

    .textsmall{
        font-size: 5px;
        color: #909399;
    }

    .position{
        position: relative;
    }

    .iconsize{
        font-size: 24px;
        position: absolute;
        right: 1px;
        top: 10px;
    }

    .twotalk-box{
        margin-left: 50px;
        margin-top: 10px;
        padding: 10px;
        border: 1px solid #fff;
    }

</style>
