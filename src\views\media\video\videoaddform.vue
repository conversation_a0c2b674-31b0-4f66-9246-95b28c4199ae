<template>
  <my-container v-loading="pageLoading"> 
    <el-form :model="addForm" :rules="calcAddFormRules" ref="addForm" label-width="100px" class="demo-addForm">
      <div class="dspbjrw">
        <div class="bt">
          <span style="float: left">创建任务</span>
        </div>
        <!-- 表单 star -->
        <div class="dspbjlx">
          <div class="lxwz">产品简称</div>
          <div style="display: inline-block">
            <el-form-item label=" " label-width="12px" prop="productShortName">
              <el-input size="mini" style="width: 100%" v-model.trim="addForm.productShortName" :maxlength="50"
                :clearable="true"></el-input>
            </el-form-item>
          </div>
        </div>
        <div>
          <div class="dspbjlx">
            <div class="lxwz">产品ID</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="productId">
                <el-input size="mini" style="width: 100%" v-model.trim="addForm.productId" :disabled="true"></el-input>
              </el-form-item>
            </div>
            <a href="#" @click="onSelctProduct">
              <i style="  font-size: 18px;  color: #409eff;  font-weight: bold; position: relative;  top: 3px; left: 20px;  "
                title="选择Id" class="el-icon-circle-plus-outline"></i></a>
          </div>
          <div class="dspbjlx">
            <div class="lxwz">商品编码</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="goodCode">
                <el-tooltip class="item" effect="dark" :content="addForm.goodCode" placement="top">
                  <el-input size="mini" style="width: 200%" v-model.trim="addForm.goodCode" :maxlength="255"
                    :clearable="true"></el-input>
                </el-tooltip>
              </el-form-item>
            </div>
            <a href="#" @click="onSelctCp">
              <i style="  font-size: 18px;  color: #409eff;  font-weight: bold; position: relative;  top: 3px; left: 190px;  "
                title="选择Id" class="el-icon-circle-plus-outline"></i></a>
          </div>
          <div class="dspbjlx">
            <div class="lxwz">平台</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="platform">
                <el-select size="mini" style="width: 60%" v-model="addForm.platform" placeholder="请选择平台" :disabled="true">
                  <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="dspbjlx">
            <div class="lxwz">店铺</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="shopName">
                <el-select size="mini" style="width: 100%" v-model="addForm.shopName" placeholder="请选择店铺"
                  :disabled="true">
                  <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                    :value="item.shopCode" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="dspbjlx">
            <div class="lxwz">运营小组</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="operationsGroup">
                <el-select size="mini" style="width: 60%" v-model="addForm.operationsGroup" placeholder="请选择小组">
                  <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <!-- <div class="dspbjlx" v-if="checkPermission('vedioTask-jjcd')">
            <div class="lxwz">紧急程度</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="taskUrgency" :disabled="true">
                <el-select size="mini" style="width: 60%" v-model="addForm.taskUrgency" placeholder="请选择小组">
                  <el-option v-for="item in taskUrgencyList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="dspbjlx">
            <div class="lxwz">负责人</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="dockingPeople">
                <el-input size="mini" style="width: 50%" v-model="addForm.dockingPeople"></el-input>
              </el-form-item>
            </div>
          </div> -->
          <div class="dspbjlx">
            <div class="lxwz">拍摄样品</div>
            <div style="display: inline-block">
              <el-form-item label="" label-width="12px" label-position="left" prop="warehouse">
                <el-select size="mini" style="width: 80%" v-model="addForm.warehouse" placeholder="请选择仓库">
                  <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="dspbjlx">
            <div class="lxwz">订单号</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="sampleRrderNo">
                <el-input size="mini" style="width: 70%" v-model="addForm.sampleRrderNo" placeholder="请填写订单号">
                </el-input>
              </el-form-item>
            </div>
          </div>
          <div class="dspbjlx">
            <div class="lxwz">快递单号</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="sampleExpressNo">
                <el-input size="mini" style="width: 100%" v-model="addForm.sampleExpressNo"
                  placeholder="请填写快递单号"></el-input>
              </el-form-item>
            </div>
          </div>

          <div class="dspbjlx">
            <div class="lxwz">负责人</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="">
                <!-- <el-input size="mini" style="width: 50%" v-model="addForm.dockingPeopleStr"></el-input> -->
                <el-select  size="mini" style="width:50%" v-model="addForm.cuteLqName" filterable :clearable="true">
                  <el-option v-for="item in erpUserInfoList" :key="item.id" :label="item.label" :value="item.label" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="dspbjlx"  v-if="checkPermission('vedioTask-pcps')">
            <div class="lxwz">排除拍摄</div>
            <div style="display: inline-block">
            
              <el-form-item label=" " label-width="12px" prop=""> 
                <el-select  size="mini" style="width:50%" v-model="addForm.unCuteLqName" filterable :clearable="true" >
                  <el-option v-for="item in fpPhotoLqNameList" :key="item.id" :label="item.label" :value="item.label" />
                </el-select>
              </el-form-item> 
            </div>
          </div>
          <div class="dspbjlx" style="margin-top: 10px;">
            <div class="lxwz">拍摄条数</div>
            <div style="display: inline-block">
              <el-form-item label=" " label-width="12px" prop="taskPickList">
                <el-checkbox-group v-model="addForm.taskPickList">
                  <el-checkbox style="margin-right: -5px" size="mini" label="1" border>视频一</el-checkbox>
                  <el-checkbox style="margin-right: -5px" size="mini" label="2" border>视频二</el-checkbox>
                  <el-checkbox style="margin-right: -5px" size="mini" label="3" border>视频三</el-checkbox>
                  <el-checkbox style="margin-right: -5px" size="mini" label="4" border>视频四</el-checkbox>
                  <el-checkbox style="margin-right: -5px" size="mini" label="5" border>视频五</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </div>
          </div>
          <div class="dspbjlx" style="margin: 20px auto 40px auto">
            <el-tooltip class="item" effect="dark" :content="addForm.remark" placement="top">
              <el-input type="textarea" :rows="2" :maxlength="800" show-word-limit placeholder="请输入内容"
                v-model="addForm.remark">
              </el-input>
            </el-tooltip>
          </div>
        </div>
        <!-- 表单 end -->
        <div class="qxtj">
          <span style="float: left"></span>
          <span style="float: right">
            <el-form-item>
              <el-button size="mini" @click="onCloseAddForm(1)">取消</el-button>
              <el-button size="mini" type="primary" @click="submitForm('addForm')">创建任务</el-button>
            </el-form-item>
          </span>
        </div>
      </div>
    </el-form>
    <!--选择商品-->
    <el-dialog title="选择产品" :visible.sync="productVisible" width='95%' v-dialogDrag append-to-body>
      <productselect :ischoice="true" ref="productselect" style="z-index:10000;height:500px" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="productVisible = false">取 消</el-button>
          <el-button type="primary" @click="onQuerenProduct()">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!--选择商品-->
    <el-dialog title="选择商品编码" :visible.sync="goodschoiceVisible" width='95%' height='500px' append-to-body v-dialogDrag>
      <goodschoice :ischoice="true" ref="goodschoice" style="z-index:10000;height:500px" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="goodschoiceVisible = false">取 消</el-button>
          <el-button type="primary" @click="onQueren()">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </my-container>
</template>
  
<script>
import MyContainer from "@/components/my-container";
import uploadfile from '@/views/media/shooting/uploadfile';
import productselect from "@/views/operatemanage/base/productselect";
import goodschoice from "@/views/base/goods/goods3.vue";
import { addOrUpdateVideoTaskAsync } from '@/api/media/vediotask';
export default {
  props: {
    fpPhotoLqNameList:{ type: Array, default: () => { return []; } },
    erpUserInfoList:{ type: Array, default: () => { return []; } },
    taskUrgencyList: { type: Array, default: () => { return []; }},
    groupList: { type: Array, default: () => { return []; } },
    platformList: { type: Array, default: () => { return []; } },
    warehouselist: { type: Array, default: () => { return []; } },
    onCloseAddForm: { type: Function, default: null },
    islook: { type: Boolean, default: false },
  },
  components: { uploadfile, productselect, goodschoice,MyContainer },
  data() {
    return {
      addForm: {
        TaskId: null,
        productShortName: null,
        goodCode: null,
        productId: null,
        platform: null,
        shopName: null,
        shopId: null,
        operationsGroup: null,
        dockingPeople: null,
        taskUrgency: 9,
        warehouse: null,
        sampleRrderNo: null,
        sampleExpressNo: null,
        taskPickList: [],
        photoUpfiles: [],
        execlUpfiles: [],
        taskRemark: null,
      },
      shopList: [],
      productVisible: false,
      goodschoiceVisible: false,
      pageLoading: false,
    };
  },
  computed: {
    calcAddFormRules() {
      return {
        productShortName: [{ required: true, message: '请填写', trigger: 'blur' }],
        shopId: [{ required: true, message: '请选择', trigger: 'blur' }],
        operationsGroup: [{ required: true, message: '请选择', trigger: 'blur' }],

        taskPickList: [{ required: true, message: '请选择', trigger: 'blur' }],
        platform: [{ required: true, message: '请选择', trigger: 'blur' }],
        warehouse: [{ required: true, message: '请选择', trigger: 'blur' }],
        taskUrgency: [{ required: true, message: '请选择', trigger: 'blur' }],
        productId: [{ required: true, message: '请选择', trigger: 'blur' }],
 
        fpDetailLqName: [{ required: true, message: '请选择', trigger: 'blur' }],
        packClass: [{ required: true, message: '请选择', trigger: 'blur' }],
        brand: [{ required: true, message: '请选择', trigger: 'blur' }],
        izcjdz: [{ required: true, message: '请选择', trigger: 'blur' }],
      }
    }
  },
  async mounted() {

  },
  methods: {
    //视频任务选择
    taskPickChange() {

    },
    //选择商品
    onSelctCp() {
      this.goodschoiceVisible = true;
    },
    //打开选择产品id窗口
    onSelctProduct() {
      this.productVisible = true;
    },
    //选择商品确定
    async onQueren() {
      var choicelist = await this.$refs.goodschoice.getchoicelist();
      if (choicelist) {
        var goodCodeList = [];
        choicelist.forEach((item) => {
          goodCodeList.push(item.goodsCode);
        });
        this.addForm.goodCode = goodCodeList.join(',');
        this.goodschoiceVisible = false;
      }
    },
    //选择产品id
    async onQuerenProduct() {
      var choicelist = await this.$refs.productselect.getchoicelistOnly();
      if (choicelist && choicelist.length == 1) {
        this.addForm.productId = choicelist[0].proCode;
        this.addForm.productShortName = choicelist[0].styleCode;
        this.addForm.shopId = choicelist[0].shopId;
        this.addForm.shopName = choicelist[0].shopName;
        this.addForm.platform = choicelist[0].platform;
        this.addForm.operationsGroup = choicelist[0].groupId;
        this.productVisible = false;
      }
    },
    //提交保存
    async submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {


          let para = _.cloneDeep(this.addForm);
          this.pageLoading = true;
          var res = await addOrUpdateVideoTaskAsync(para);
          this.pageLoading = false;
          if (!res?.success) { return; }
          this.$message({ message: this.$t('保存成功'), type: 'success' });
          this.onCloseAddForm(2);
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style  lang="scss" scoped>
::v-deep .el-form-item {
  margin: 0px !important;
}

::v-deep .el-input--mini {
  margin: 0 !important;
}

::v-deep .el-form-item__error {
  position: absolute !important;
  top: 30% !important;
  left: 400px !important;
  width: 60px !important;
}

::v-deep .dspbjrw {
  width: 700px;
  background-color: #fff;
}

::v-deep .dspbjrw .dspbjbt {
  height: 60px;
  background-color: rgb(255, 255, 255);
  font-size: 18px;
  color: #666;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 20px 35px;
}

::v-deep .dspbjrw .dspbjbt i {
  color: #999;
}

::v-deep .dspbjrw .dspbjbt i {
  margin-left: 8px;
  line-height: 26px;
}

::v-deep .dspbjrw .dspbjbt i:hover {
  margin-left: 8px;
  line-height: 26px;
  color: #409eff;
  position: relative;
  top: -2px;
}

::v-deep .dspbjrw .rwmc {
  width: 700px;
  height: 70px;
  background-color: rgb(255, 255, 255);
  float: left;
  box-shadow: 0px 3px 5px #eeeeee;
  box-sizing: border-box;
  padding: 0 56px;
}

::v-deep .dspbjrw .rwmc .xh {
  height: 65px;
  font-size: 18px;
  line-height: 68px;
  box-sizing: border-box;
  padding: 0 2px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666;
}

::v-deep .dspbjrw .rwmc .mc,
.icon {
  height: 65px;
  font-size: 18px;
  line-height: 68px;
  box-sizing: border-box;
  margin-left: 10px;
  padding: 0 2px;
  display: inline-block;
  color: #666;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .dspbjrw .dspbjlx {
  width: 100%;
  height: 40px;
  /* background-color: bisque; */
  box-sizing: border-box;
  padding: 0 60px;
}

::v-deep .dspbjrw .dspbjlxf {
  width: 100%;
  /* background-color: bisque; */
  box-sizing: border-box;
  padding: 0 60px;
}

::v-deep .dspbjrw .dspbjlx .lxwz {
  width: 112px;
  font-size: 14px;
  color: #666;
  line-height: 26px;
  /* background-color: rgb(204, 204, 255); */
  display: inline-block;
}

::v-deep .dspbjrw .scpd {
  width: 100px;
  font-size: 14px;
  color: #666;
  line-height: 36px;
  /* background-color: rgb(204, 204, 255); */
  float: left;
}

::v-deep .dspbjrw .zjck {
  display: inline-block;
  float: right;
  position: relative;
  top: 3px;
}

::v-deep .dspbjrw .dspczxx {
  width: 100%;
  box-sizing: border-box;
  /* padding: 0 60px; */
  text-align: center;
  border: 1px solid #dcdfe6;
  border-right: 0px;
  border-bottom: 0px;
  border-left: 0px;
  margin: 0 0;
}

::v-deep .dspscpd {
  width: 100%;
  /* background-color: bisque; */
  box-sizing: border-box;
  padding: 5px 60px;
}

::v-deep .dspbjrw .qxtj {
  height: 30px;
  /* background-color: aquamarine; */
  box-sizing: border-box;
  padding: 10px 60px;
}

::v-deep .dspbjrw .dspsccg {
  width: 100%;
  box-sizing: border-box;
  padding: 30px 60px;
}

::v-deep .dspbjrw .dspczx {
  box-sizing: border-box;
  padding: 5px 60px;
  font-size: 14px;
  color: #666;
}

::v-deep .dspbjrw .spscpd .spczan,
.spczs,
.spczan,
.spczmz,
.spczsj {
  min-width: 50px;
  max-width: 80px;
  display: inline-block;
  margin-right: 12px;
  /* background-color: aquamarine; */
}

::v-deep .dspbjrw .spczsj {
  color: #999;
}

::v-deep .dspbjrw .spczk {
  height: 35px;
}

::v-deep .dspbjrw .spczksps {
  width: 14%;
  display: inline-block;
}

::v-deep .dspbjrw .spczkzy {
  width: 43%;
  /* background-color: aqua; */
  display: inline-block;
}

::v-deep .dspbjrw .spczs {
  width: 65px;
}

::v-deep .dspbjrw .dspbjfgx {
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 10px 0;
  padding-bottom: 35px;
}

::v-deep .dspczx {
  box-sizing: border-box;
  padding: 5px 60px;
  font-size: 14px;
  color: #666;
}

::v-deep .dspbjrw .dspczrz {
  box-sizing: border-box;
  padding: 15px 60px;
}

::v-deep .dspbjrw .dspczrzx {
  box-sizing: border-box;
  padding: 10px 60px;
}

::v-deep .dspbjrw .rztx,
.rzmz,
.rzxgx,
.rzxgsj {
  height: 30px;
  display: inline-block;
  font-size: 14px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .dspbjrw .rztx {
  width: 25px;
  height: 25px;
  background-color: #f5f5f5;
  border-radius: 50%;
  margin-right: 15px;
}

::v-deep .dspbjrw .rzmz {
  width: 50px;
  margin-right: 5px;
}

::v-deep .dspbjrw .rzxgx {
  max-width: 200px;
  margin-right: 10px;
  color: #999;
}

::v-deep .dspbjrw .rzxgsj {
  max-width: 200px;
  color: #999;
}

::v-deep .dspbjrw .rzxgq,
.rzxgnr {
  max-width: 450px;
  line-height: 15px;
  display: inline-block;
  font-size: 12px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .dspbjrw .rzxgq {
  width: 50px;
  margin-left: 43px;
  margin-right: 2px;
}

::v-deep ::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}

.dspbjrw .qxtj {
  height: 100px;
  /* background-color: aquamarine; */
  box-sizing: border-box;
  padding: 25px 60px;
}

::v-deep .dspbjrw .bt {
  height: 40px;
  /* background-color: aquamarine; */
  font-size: 18px;
  color: #666;
  margin-bottom: 20px;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 0 35px;
}
</style>