<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="数据开始时间" end-placeholder="数据结束时间" :picker-options="pickerOptions"
          style="width: 300px;margin-right: 10px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-input v-model="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss" />
        <el-select v-model="ListInfo.billTypeList" placeholder="账单费用类型" class="publicCss" clearable multiple filterable
          collapse-tags>
          <el-option v-for="item in billingExpenseType" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button> 

        <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button @click="startImport"
          type="primary" icon="el-icon-share" @command="handleCommand"> 导入
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">下载模版</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="primary" @click="onExport" style="margin-left: 5px;" v-if="checkPermission('shein_SheInBill_export')">导出</el-button>
      </div>
    </template>
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" v-loading="loading"
      :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>


    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="display: flex; align-items: baseline; height: 75px;margin-top: 10px;">
        <el-date-picker style="width: 150px; margin-right: 10px;" v-model="yearMonthDay" type="date" placeholder="选择日期"
          :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
        </el-date-picker>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import { getNewSheInBillingCharge, getBillingTypeSheIn, importBillingCharge as importBillFee } from '@/api/bookkeeper/reportdayV2'
import { exporBillingChargeSheIn } from '@/api/bookkeeper/crossBorderV2'
import { formatTime } from "@/utils/tools";
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopCode', label: '店铺ID' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'yearMonthDay', label: '日期' },
  { sortable: 'custom', width: '80', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'paymentType', label: '款项类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'replenishmentDeductionCategory', label: '账单费用类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'matchingType', label: '对单类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'associatedDocument', label: '关联单据', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'unitPrice', label: '单价', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'quantity', label: '数量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalAmount', label: '总金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'documentStatus', label: '单据状态', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'associatedReportBill', label: '关联报账单', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'skc', label: 'SKC', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'amount', label: '金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'accountingDate', label: '会计日期', formatter: (row) => formatTime(row.accountingDate, 'YYYY-MM-DD') },
]
export default {
  name: "SheInBill",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      summaryarry: {},
      billingExpenseType: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        shopName: null,//店铺
        billTypeList: null,//补扣款分类
        debitSlipNumber: null,//补扣款单号
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
      dialogVisible: false,//导入弹窗
      fileList: [],//上传文件列表
      uploadLoading: false,//上传按钮loading
      fileparm: {},//上传文件参数
      yearMonthDay: null,//日期
    }
  },
  async mounted() {
    const res = await getBillingTypeSheIn()
    this.billingExpenseType = res.data?.map(item => { return { value: item.billingItem, label: item.billingItem }; });
    await this.getList()
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
      await this.getList()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
      }
      const replaceArr = ['shopName'] //替换空格的方法,该数组对应str类型的input双向绑定的值
      this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
      this.loading = true
      const { data, success } = await getNewSheInBillingCharge(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.yearMonthDay = null
      this.dialogVisible = true;
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      form.append("PlatForm", 12);
      form.append("yearMonthDay", dayjs(this.yearMonthDay).format('YYYYMMDD'));
      var res = await importBillFee(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (!this.yearMonthDay) {
        this.$message({ message: "请选择日期", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    async handleCommand(command) {
      switch (command) {
        //下载模版
        case 'a':
          await this.downLoadFile()
          break;
      }
    },
    async downLoadFile() {
      window.open("/static/excel/KjBillCharges/希音全托账单费用.xlsx", "_blank");
    },
    async onExport() {//导出列表数据；
      const replaceArr = ['shopName'] //替换空格的方法,该数组对应str类型的input双向绑定的值
      this.ListInfo = replaceSpace(replaceArr, this.ListInfo)

      var res = await exporBillingChargeSheIn(this.ListInfo);
      if (res?.success) {
        this.$message({ message: res.msg, type: "success" });
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 45px;
}
</style>
