<template>
  <el-popover placement="bottom" trigger="manual" v-model="show" popper-class="popCusClass">
    <JWChat ref="jwchat" :winBarlist="winBarlist" :hotSaleGoodsChooseEnquiryId="-1"></JWChat>
  </el-popover>
</template>

<script>
import JWChat from '@/components/Chat/ChatInterface/chatComponents'

export default {
  name: "chat",
  components: { JWChat },
  data() {
    return {
      show: true,
      winBarlist: []
    }
  }
}
</script>

<style>
.popCusClass {
  margin: 50px 15%;
  padding-left: 0px;
  width: 940px;
  height: 663px;
  overflow: unset;
}
</style>