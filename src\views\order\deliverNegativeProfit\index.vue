<template>
  <my-container style="height: 100%">
    <template #header>
      <el-row style="width: 100%; display: flex; flex-direction: row; align-items: center;">
        <el-date-picker style="width: 210px" :picker-options="pickerOptions" v-model="filter.timerange" type="daterange"
          :clearable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>

        <el-select filterable v-model="filter.payAmountX" collapse-tags clearable placeholder="付款金额" style="width: 120px">
            <el-option key="付款金额>=2" label="付款金额>=2" :value=true></el-option>
            <el-option key="付款金额<2" label="付款金额<2" :value=false></el-option>
        </el-select>

        <el-button type="primary" style="margin-left: 10px; " @click="onSearch">查 询</el-button>
 
 
 
 
      </el-row>
 
      <el-row>
        <!-- <el-row> -->
        <!-- v-show="activeName=='first2'?false:true" -->
        <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
          <el-checkbox :checked="allplatform" v-model="allplatform" @change="allCheckChange()">全选</el-checkbox>
          <el-checkbox-group style="margin-left: 10px;" v-model="isselplatform" @change="handleCheckChange($event, 'fat')">
 
            <el-checkbox v-for="item in platForms" :label="item.platform"
              :key="item.platform">{{ item.platform }}</el-checkbox>
 
          </el-checkbox-group>
        </div>
 
      </el-row>
 
      <div style="margin-left: 220px;">
        <div style="display: flex; flex-direction: row;" v-show="activeName == 'first1' ? true : false">
        </div>
 
 
 
 
      </div>
    </template>
    <!-- @tab-click="handleClick" -->
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="趋势图" name="first1" style="height: 100%">
        <showChar ref="refshowChar" :filter="filter" style="height: 100%" :isselplatformlr="isselplatformlr"
          :isselplatform="isselplatform"></showChar>
      </el-tab-pane>
      <el-tab-pane label="数据看板" name="first2" style="height: 100%">
        <showTable ref="showTable" :fatimerange="filter.timerange" :filter2="filter2" style="height: 100%"></showTable>
      </el-tab-pane>
    </el-tabs>
  </my-container>
 </template>
 
 <script>
 import MyContainer from "@/components/my-container";
 import showChar from "./showChar.vue";
 import showTable from "./showTable.vue";
 import { formatTime, pickerOptions } from "@/utils/tools";
 
 import { productOrderDayReport as pageProductDayReport, exportProductOrderDayReport, queryProductOrderDayReportSumChart, orderDayRpt_OrderTypes, orderDayRpt_OrderTypes_fmtFunc, orderDayRpt_OrderStatuss } from '@/api/bookkeeper/reportdayV2'
 const platformvalue = [{ label: '天猫', value: 1 }, { label: '拼多多', value: 2 }, { label: '阿里巴巴', value: 4 },
 { label: '抖音', value: 6 }, { label: '京东', value: 7 }, { label: '淘工厂', value: 8 }, { label: '淘宝', value: 9 }, { label: '苏宁', value: 10 }, { label: '分销', value: 11 },]
 export default {
  name: 'Vue2demoIndex',
  components: {
    MyContainer, showChar, showTable
  },
 
  data() {
    return {
      orderDayRpt_OrderTypes: orderDayRpt_OrderTypes,
      orderDayRpt_OrderStatuss: orderDayRpt_OrderStatuss,
      filter2: {},
      filter: {
        timeType: 1,
        timerange: null,
        orderNo: null,
        orderNoInner: null,
        orderStatusS: [],
        orderTypes: [],
        exceptOrderTypes: [],
        platForms: null,
        payAmountX: null
      },
      pickerOptions: pickerOptions,
      activeName: 'first1',
      // filter: {},
      isselplatformlr: [],
      timerangetwo: [],
      timerange: [],
      allplatformlr: true,
      allplatform: true,
      lirunlists: [],
      isselplatform: [
        '淘工厂',
        '抖音',
        '拼多多',
        '阿里巴巴',
        '天猫',
        '京东',
        '淘宝',
        '苏宁',
 
        // '分销'
      ],
      timer: null,
      platForms: [
        { platform: '天猫', istrue: true, },
        { platform: '阿里巴巴', istrue: true, },
        { platform: '抖音', istrue: true, },
        { platform: '京东', istrue: true, },
        { platform: '淘工厂', istrue: true, },
 
 
        { platform: '淘宝', istrue: true, },
        { platform: '苏宁', istrue: true, },
        { platform: '拼多多', istrue: true, },
 
        // {platform:'分销',istrue: true, },
 
 
      ],
      zhiweilist: [
 
        { label: '运 营 组', value: 3, istrue: false },
        { label: '运营专员', value: 4, istrue: false },
 
        { label: '运营助理', value: 5, istrue: false },
        { label: '店铺名称', value: 2, istrue: false },
 
      ],
    };
  },
 
  mounted() {
    this.init();
    if (this.isselplatform.length == this.platForms.length) {
      // this.filter.platForms = null
      this.filter.platForms = [1, 2, 4, 6, 7, 8, 9, 10]
 
      // platformvalue.map((item)=>{
      //   if(this.platForms.indexOf(item.label) != -1){
      //     this.filter.platForms.push(item.value)
      //   }
      // })
    }
 
    // this.filter.orderStatusS = filterStatus('已取消', this.orderDayRpt_OrderStatuss, this.filter.orderStatusS);
    // this.filter.orderTypes = filterStatus(["补发订单", "换货订单"], this.orderDayRpt_OrderTypes, this.filter.orderTypes);
    // this.filter.exceptOrderTypes = filterStatus(["普通订单", "分销Plus", "分销"], this.orderDayRpt_OrderTypes, this.filter.exceptOrderTypes);
    let end = new Date();
    let start = new Date();
    start.setDate(start.getDate() - 30);
    // this.timerange = [formatTime(start, "YYYY-MM-DD"), formatTime(end, "YYYY-MM-DD")]
    this.filter.timerange = [formatTime(start, "YYYY-MM-DD"), formatTime(end, "YYYY-MM-DD")]
 
    this.$nextTick(() => {
      this.$refs.refshowChar.showChar()
      this.tofilter();
    });
 
  },
 
  methods: {
    async init() {
      var date1 = new Date(); date1.setDate(date1.getDate() - 1);
      var date2 = new Date(); date2.setDate(date2.getDate() - 1);
      this.filter.timerange = [];
      this.filter.timerange[0] = this.datetostr(date1);
      this.filter.timerange[1] = this.datetostr(date2);
    },
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    handleClick() {
      if (this.activeName == 'first2') {
        this.tofilter();
      } else if (this.activeName == 'first1') {
        this.$refs.refshowChar.showChar()
      }
    },
    handleCheckChange(e, type) {
      // if(type == 'chil'){
      //   console.log("打印数据1",e)
 
      // }else if(type == 'fat'){
 
      let selplatform = [
        '淘工厂',
        '抖音',
        '拼多多',
        '阿里巴巴',
        '天猫',
        '京东',
        '淘宝',
        '苏宁',
 
        // '分销'
      ]
 
 
 
 
      if (selplatform.length == e.length) {
        this.filter.platForms = null;
        this.allplatform = true;
        
      } else {
        this.filter.platForms = []
        platformvalue.map((item) => {
          if (e.indexOf(item.label) != -1) {
            this.filter.platForms.push(item.value)
          }
        })
        this.allplatform = false;
      }
 
      this.$nextTick(()=>{
        this.onSearch();
      });
 
      
      // }
      // this.$refs.showTable.filterget(params)
 
 
    },
    allCheckChange(e) {
      if (this.allplatform) {
        this.isselplatform = [
          '淘工厂',
          '抖音',
          '拼多多',
          '阿里巴巴',
          '天猫',
          '京东',
          '淘宝',
          '苏宁',
 
          // '分销'
        ]
        this.filter.platForms = [1, 2, 4, 6, 7, 8, 9, 10]
        this.onSearch();
      } else {
        this.isselplatform = [];
        this.filter.platForms = null;
      }
      
      // this.filter.platForms = null;
 
      // this.tofilter();
    },
    changedate(val) {
      let days = this.getDaysDifference(val[0], val[1]);
      if (days < 30) {
        this.$message.info("请选择至少一个月时间，已为您自动选择一个月时间")
        this.filter.startTime = this.getPrevious30Days(val[1]);
        this.filter.endTime = val[1];
      } else {
        this.filter.startTime = val[0];
        this.filter.endTime = val[1];
      }
 
      this.filter.timerange = [this.filter.startTime, this.filter.endTime];
 
      // this.tofilter();
    },
    changedatetwo(val) {
      // let days = this.getDaysDifference(val[0],val[1]);
      // this.timerangetwo = [this.filter.startTime, this.filter.endTime];
      this.tofilter();
    },
    async getchartlist() {
 
    },
    tofilter() {
      let params = {
        startTime: this.filter.timerange[0],
        endTime: this.filter.timerange[1],
        platForms: this.filter.platForms ? this.filter.platForms.join(",") : null,
        payAmountX: this.filter.payAmountX, 
      }
      this.filter2 = params;
      this.$refs.showTable.filterget(params)
    },
    onSearch() {
      
      clearInterval(this.timer)
          this.timer=setInterval(()=>{
            // this.timer=setInterval(()=>{
              if (this.isselplatform.length == 0) return this.$message.info("请选择平台")
              if (this.activeName == 'first2') {
                this.tofilter();
              } else if (this.activeName == 'first1') {
                this.$refs.refshowChar.pageloading = true;
                this.$refs.refshowChar.chartsdata9 = null;
 
                this.$refs.refshowChar.allcharbusdata = [];
 
                this.$refs.refshowChar.showChar()
              }
              clearInterval(this.timer)
        },2000)
    }
  },
 };
 </script>
 
 <style lang="scss" scoped></style>