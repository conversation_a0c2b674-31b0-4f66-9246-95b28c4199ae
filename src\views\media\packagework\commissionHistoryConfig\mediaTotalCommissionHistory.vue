<template>
    <!-- 薪资核算 历史版本 -->
    <my-container> 
        <template #header> 
            <el-button-group> 
                <el-button style="height: 28px;" type="primary" @click="onSearch">刷新</el-button>  
                <el-button style="height: 28px;" type="primary" @click="onExeprotShootingTask"  >导出</el-button>  
            </el-button-group>
        </template>  
            <vxetablebase :id="'mediaTotalCommissionHistory'" 
            :hasSeq="false" 
            :border="true" 
            :align="'center'"
            ref="tablex" :that='that' :hasexpand='true'
            :tableCols='tableCols'
            :tableData='tasklist'  
            :loading="listLoading"
            :checkbox-config="{labelField: 'id', highlight: true, range: true}"
            > 
            </vxetablebase>    
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList"  :page-size="300" />
        </template>
        
   </my-container>
</template>

<script>  
// import { getHistorytPersonnelPositionInfo } from '@/api/media/shootingset'
import { getHistorytPersonnelPositionInfo } from '@/api/inventory/packagesSetProcessing.js'

import MyConfirmButton from "@/components/my-confirm-button"; 
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import MyContainer from "@/components/my-container";   
const tableCols = [
    { istrue: true, prop: 'userName', label: '姓名', width: '70',fixed:'left' },
    { istrue: true, prop: 'companyName', label: '公司', width: '50',fixed:'left'  ,fixed:'left'  },
    { istrue: true, prop: 'workPositionStr', label: '工作岗位', width: '85',fixed:'left'  },
    { istrue: true, prop: 'commissionPositionStr', label: '提成岗位', width: '85' ,fixed:'left' },
    // { istrue: true, prop: 'classtype', label: '类型', width: '55'  }, 
    { istrue: true, prop: 'jycqts', label: '应出勤', width: '70'  }, 
    { istrue: true, prop: 'ycqts', label: '实际出勤', width: '70'  }, 
    { istrue: true, prop: 'quanqingAward', label: '全勤奖', width: '70'  },
    { istrue: true, prop: 'jBasepay', label: '当月底薪', width: '70'  },  
    { istrue: true, prop: 'basepay', label: '应发底薪', width: '70'  },  
    { istrue: true, prop: 'jAchievement', label: '当月绩效', width: '70'  },
    { istrue: true, prop: 'achievement', label: '应发绩效', width: '70'  },


  
    { istrue: true, prop: 'sbSubsidy', label: '设备补助', width: '55'  }, 
    { istrue: true, prop: 'subsidy', label: '补贴', width: '55'  }, 
 
    
    { istrue: true, prop: 'supplyAgain', label: '福利', width: '55'  }, 
    { istrue: true, prop: 'shootingAjustCount', label: '新品', width: '70'},
    { istrue: true, prop: 'vedioTaskAjustCount', label: '短视频拍摄', width: '85'},
    { istrue: true, prop: 'directImgAjustCount', label: '车图', width: '70',  align: 'center' },
    { istrue: true, prop: 'kuangJinFee', label: '跨境提成', width: '70',  align: 'center' },
    { istrue: true, prop: 'douYinFee', label: '抖音提成', width: '70',  align: 'center' },
    { istrue: true, prop: 'microDetailAjustCount', label: '微详情', width: '70'},
    { istrue: true, prop: 'shopDecorationAjustCount', label: '店铺装修', width: '70'},
    { istrue: true, prop: 'packingDesignAjustCount', label: '包装设计', width: '70'},
    { istrue: true, prop: 'dayModImgAjustCount', label: '日常改图', width: '70'},
    { istrue: true, prop: 'overTimePay', label: '加班费', width: '70'  }, 
    { istrue: true, prop: 'fuLiFee', label: '补发', width: '55'  }, 
    { istrue: true, prop: 'otherAjustCount', label: '其他', width: '70'},
    { istrue: true, prop: 'shouldFee', label: '应发金额', width: '70'},
    { istrue: true, prop: 'shuiDianFee', label: '水电费', width: '70'},
    { istrue: true, prop: 'cutPayment', label: '责任扣款', width: '70'},
    { istrue: true, prop: 'chiDaoFee', label: '迟到', width: '70'},
    { istrue: true, prop: 'zhaoTuiFee', label: '早退', width: '70'},
    { istrue: true, prop: 'queKaFee', label: '缺卡', width: '70'},
    { istrue: true, prop: 'kuangGongFee', label: '旷工', width: '70'},
    { istrue: true, prop: 'yuZhiFee', label: '预支工资', width: '70'},
    { istrue: true, prop: 'socialSecurityFee', label: '社保', width: '70'},
    { istrue: true, prop: 'otherFee', label: '其他减', width: '70'}, 
    { istrue: true, prop: 'commissionRate',fixed:'right' , label: '比例', width: '65', align: 'center', formatter: (row) => row.commissionRate  + "%"  },
    { istrue: true, prop: 'isCyCommissionbool',fixed:'right' , label: '是否核算', width: '50', align: 'center', formatter: (row) => row.isCyCommission==1  ? "是" : "否"    },
    { istrue: true, prop: 'actualFee', fixed:'right' , label: '实发金额', width: '70'},
    { istrue: true, prop: 'joinedDate', label: '入职日期', width: '98' },
    { istrue: true, prop: 'leaveDate', label: '离职日期', width: '98' },
]; 
export default {
   components: { MyContainer,vxetablebase ,MyConfirmButton },
   props:{  
        versionId:{type:String,default:"0"}, 
    },
   data() {
       return {
           that: this,  
           listLoading: false,
           tableCols: tableCols,   
           tasklist:[], 
           fileList:[],
           total:0,
           sels: [], // 列表选中列
           pager: { OrderBy: "orderNum", IsAsc: true },
           editformdialog:false,
           editLoading:false,
           archivedialogVisible:false,
           dialogVisibleSyj:false,
           editformTitle:null,
           archiveform:{
            versionName:null,
           },
           editform:{
                openType:2,
                commissionRate:0,
                isCyCommission:1,
                ycqts:1,
                basepay:1
           },
           filter: { 
                needCacl:1,
           }, 
           editformRules: { 
                commissionRate: [{ required: true, message: '请填写', trigger: 'blur' }], 
                isCyCommission: [{ required: true, message: '请选择', trigger: 'blur' }],  
            },
       };
   },
   //向子组件注册方法
   provide () {
       return { 
       }
   },
   async mounted() {
      await this.onSearch();
   }, 
   methods: {  
    async onExeprotShootingTask() {
            this.$refs.tablex.exportData("薪资核算历史")
    },
    exportDataEvent () { 
        this.$refs.tablex.exportData()
    },
   
    async onSearch() {
        this.$refs.pager.setPage(1);
        this.getTaskList();
    },
    async getTaskList() 
    {  
        var pager = this.$refs.pager.getPager();
        const params = {
            ...this.filter,
            ...pager,
            ...this.pager, 
        };
        this.listLoading = true;
        var res = await getHistorytPersonnelPositionInfo({versionId:this.versionId}); 
        this.listLoading = false;   
        if(!res?.success) return;
        this.tasklist = res.data.list; 
        this.total = res.data.total;
    },
    
   },
};
</script> 
<style lang="scss" scoped>
.content{
   display: flex;
   flex-direction: row;
}
::v-deep .vxetoolbar20221212 {
    position: absolute;
    top: 30px;
    right: 5px;
    padding-top: 0;
    padding-bottom: 0;
    z-index: 999;
    background-color: rgb(255 255 255 / 0%);
}

</style>

