<template>
  <my-container v-loading="pageLoading">
    <template>
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' style="height:94%"
        :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="true" @select="selectchange"
        :isSelectColumn='true' :tableHandles='tableHandles' @cellclick="cellclick" @summaryClick='onsummaryClick'
        :loading="listLoading">
      </ces-table>

    </template>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <el-dialog :visible.sync="showDetailVisible.visible" width="72%" append-to-body>
      <span>
        <template>
          <el-date-picker v-model="filter.timerange" type="datetimerange" :picker-options="pickerOptions1"
            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期"></el-date-picker>
          <el-button type="primary" @click="onSearch1()">查询</el-button>
        </template>
      </span>
      <span>
        <buschar v-if="showDetailVisible.visible" ref="buschar" :analysisData="showDetailVisible.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showDetailVisible.visible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
      append-to-body>
      <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" style="z-index:10000;height:600px" />
    </el-dialog>
    <el-dialog title="内部订单日志信息" v-if="dialogHisVisible1" :visible.sync="dialogHisVisible1" width="70%" height="600px"
      append-to-body>
      <OrderActionsByInnerNos ref="OrderActionsByInnerNos1" :orderNoInner="orderNoInner"
        style="z-index:10000;height:600px" />
    </el-dialog>

  </my-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import buschar from "@/components/Bus/buschar";
import { formatLinkProCode, formatSendWarehouse, formatPlatform, formatExpressCompany } from "@/utils/tools";
import { getWithholdSumTable as getOrderWithholdList, getOrderWithholdListChart, exportOrderWithhold, importPinOrderIllegal } from "@/api/order/orderdeductmoney";
import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
import * as echarts from "echarts";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";

const tableCols = [
  //产品ID、内部订单号、线上订单号、扣款金额、扣款日期，付款日期、发货日期、扣款原因、是否预售、责任、发货仓、物流公司、平台、店铺、运营组、采购组、
  { istrue: true, prop: 'proCode', label: '宝贝ID', width: '110', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
  { istrue: true, prop: 'orderNoInner', label: '内部单号', width: '84', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogDetail1(row) },
  { istrue: true, prop: 'orderNo', label: '线上订单号', width: '180', sortable: 'custom', formatter: (row) => !row.orderNo ? " " : row.orderNo, type: 'click', handle: (that, row) => that.showLogDetail(row) },
  { istrue: true, prop: 'amountPaid', label: '扣款金额', width: '54', sortable: 'custom', summaryEvent: true, },
  { istrue: true, prop: 'occurrenceTime', label: '扣款日期', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.occurrenceTime, "YYYY-MM-DD") },
  { istrue: true, prop: 'payTime', label: '付款日期', width: '124', sortable: 'custom', formatter: (row) => !row.payTime ? " " : formatTime(row.payTime, "YYYY-MM-DD HH:mm") },
  { istrue: true, prop: 'sendTime', label: '发货日期', width: '124', sortable: 'custom', formatter: (row) => !row.sendTime ? " " : formatTime(row.sendTime, "YYYY-MM-DD HH:mm") },
  { istrue: true, prop: 'planDeliveryDate', label: '预计发货时间', width: '124', sortable: 'custom', formatter: (row) => !row.planDeliveryDate ? " " : formatTime(row.planDeliveryDate, "YYYY-MM-DD HH:mm") },
  { istrue: true, prop: 'illegalType', label: '扣款原因', width: '70', sortable: 'custom', formatter: (row) => !row.illegalTypeName ? " " : row.illegalTypeName },
  { istrue: true, prop: 'ysLx', label: '是否预售', width: '60', sortable: 'custom' },
  { istrue: true, prop: 'dept', label: '责任部门', width: '64', sortable: 'custom' },
  { istrue: true, prop: 'zrReason', label: '责任原因', width: '70', sortable: 'custom' },
  { istrue: true, prop: 'zrSetTime', label: '责任计算时间', width: '130', sortable: 'custom', formatter: (row) => !row.zrSetTime ? " " : formatTime(row.zrSetTime, "YYYY-MM-DD HH:mm") },
  { istrue: true, prop: 'sendWarehouseName', label: '发货仓', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'expressNo', label: '快递单号', width: '125', sortable: 'custom',type: 'click', handle: (that, row) => that.onShowLogistics(row)},
  { istrue: true, prop: 'expressCompanyName', label: '快递公司', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'platform', label: '平台', width: '60', sortable: 'custom', formatter: (row) => formatPlatform(row.platform) },
  { istrue: true, prop: 'shopName', label: '店铺', minwidth: '120', sortable: 'custom', },
  { istrue: true, prop: 'groupName', label: '运营组', width: '62', sortable: 'custom' },
  { istrue: true, prop: 'brandName', label: '采购组', width: '62', sortable: 'custom' },
  { istrue: true, prop: 'goodsName', label: '商品名称', width: '180', sortable: 'custom' },
  { istrue: true, prop: 'zrConditionFullName', label: '划责规则', width: '180', sortable: 'custom',     formatter: (row) => (row.zrConditionFullName ?? "")  },
  { istrue: true, prop: 'memberName', label: '责任人', width: '70', sortable: 'custom' },
        { istrue: true, prop: 'memberConditionContent', label: '责任人规则', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'zrAppealState', label: '申诉状态', width: '80', formatter: (row) => !row.zrAppealStateText ? " " : row.zrAppealStateText },
  //fixed: 'right',
  { istrue:true,label:'功能',width:'80', type:'button',btnList:[
    {
      label:'申诉',
      handle:(that,row)=>that.zrApply(row),
      ishide: (that, row) => { return !row.showZrAppealBtn ; }
    },
    // {
    //   label:'重算',
    //   handle:(that,row)=>that.zrRecalc(row),
    //   ishide:(that,row)=>{ return [1,2,3,9].indexOf(row.illegalType)<0; }    
    // }
  ]}
]

const tableHandles = [
        { label: "批量申诉", handle: (that) => that.batchZrApply() },
];



export default {
  name: 'YunhanAdminOrderillegaldetail',
  components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, vxetablebase, buschar, OrderActionsByInnerNos },
  props: {
    filter: {}
  },
  data () {
    return {
      dialogHisVisible1: false,
      orderNoInner: '',
      orderNo: '',
      dialogHisVisible: false,
      that: this,
      list: [],
      platformList: [],
      chatType: '',
      chatVal: '',
      illegalTypeList: [],
      summaryarry: {},
      pager: { OrderBy: "OccurrenceTime", IsAsc: false },
      filterImport: {
        platform: 1,
        occurrenceTime: formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD")
      },
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() > Date.now();
        }
      },
      pickerOptions1: {
        shortcuts: [{
          text: '近一周',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近十五天',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近一个月',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近三个月',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      sels: [],
      listLoading: false,
      pageLoading: false,
      dialogVisible: false,
      uploadLoading: false,
      uploadLoading: false,
      showDetailVisible: { visible: false, title: "", data: [] },
      selids:[],
      selrows:[],
    };
  },

  async mounted () {
    //await this.setPlatform()
    //await this.onSearch()

  },

  methods: {
    //申诉
    async zrApply(row){      
      let self=this;      
      this.$showDialogform({
                    path: `@/views/order/orderillegal/zrApply/OrderDeductZrApplyForm.vue`,
                    title: '责任申诉',
                    autoTitle:false,
                    args: {id:0, orderNo:row.orderNo, occTime:row.occurrenceTime, platform:2,mode:1},
                    height: 300,
                    width: '80%',
                    callOk: self.onSearch
                })
    },
    //重算责任
    async zrRecalc(row){      
      let self=this;      
      this.$showDialogform({
                    path: `@/views/order/orderillegal/zrApply/OrderDeductZrRecalcForm.vue`,
                    title: '重算责任',
                    autoTitle:false,
                    args: {orderNo:row.orderNo, occTime:row.occurrenceTime, platform:0,mode:2},
                    height: 300,
                    width: '80%',
                    //callOk: self.onSearch
                })
    },
    //批量申诉
    async batchZrApply(){      
      let self=this;      
      if(!self.selrows||self.selrows.length<=0)
      {
        this.$message({ message: "请勾选至少一行数据", type: "warning" });
        return;
      }
      this.$showDialogform({
                    path: `@/views/order/orderillegal/zrApply/OrderDeductBatchZrApplyForm.vue`,
                    title: '批量责任申诉',
                    autoTitle:false,
                    args: {selRows:self.selrows,platform:2},
                    height: 600,
                    width: '80%',
                    callOk: self.onSearch
                })
    },
    showLogDetail1 (row) {
      this.dialogHisVisible1 = true;
      this.orderNoInner = row.orderNoInner;
    },
    showLogDetail (row) {
      this.dialogHisVisible = true;
      this.orderNo = row.orderNo;
    },
    onShowLogistics(row) {
            let self = this;
            this.$showDialogform({
                path: `@/views/order/logisticsWarning/DbLogisticsRecords.vue`,
                title: '物流明细',
                args: { expressNos:row.expressNo },
                height: 300,
            });
    },

    //查询第一页
    async onSearch () {

      if (!this.filter.timerange) { this.$message({ message: "请选择日期", type: "warning", }); return; }
      this.$refs.pager.setPage(1)
      await this.getlist();
    },
    //获取查询条件
    getCondition () {
      this.filter.startPayDate = null
      this.filter.endPayDate = null
      this.filter.startSendDate = null
      this.filter.endSendDate = null
      if (this.filter.timerange && this.filter.timerange.length > 1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      else {
        this.$message({ message: "请先选择日期", type: "warning" });
        return false;
      }

      if (this.filter.timerange2 && this.filter.timerange2.length > 1) {
        this.filter.startPayDate = this.filter.timerange2[0];
        this.filter.endPayDate = this.filter.timerange2[1];
      }

      if (this.filter.timerange3 && this.filter.timerange3.length > 1) {
        this.filter.startSendDate = this.filter.timerange3[0];
        this.filter.endSendDate = this.filter.timerange3[1];
      }
      //this.filter.platform=2
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = {
        ...pager,
        ...page,
        ... this.filter
      }

      return params;
    },
    //分页查询
    async getlist () {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      console.log("部门看板最终参数", params);
      if (params.dutyDept == "未知") {
        params.dutyDept = ""
      }
      this.listLoading = true
      const res = await getOrderWithholdList(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry = res.data.summary;
      if (this.summaryarry)
        this.summaryarry.amountPaid_sum = parseFloat(this.summaryarry.amountPaid_sum.toFixed(6));
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
    //排序查询
    async sortchange (column) {
      if (!column.order)
        this.pager = {};
      else {
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    },
    selectchange: function (rows, row) {
      console.log(rows)
      this.selrows=rows;

      this.selids = []; 
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    cellclick (row, column, cell, event) {

    },
    async onSearch1 () {
      this.showchart();
      //this.filter1.timerange1=[];

    },
    // 趋势图
    async showchart () {
      if (this.filter.timerange && this.filter.timerange.length > 1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      } else {
        this.$message({ message: "请先选择日期", type: "warning" });
        return false;
      }


      var paras = { ...this.filter };

      let that = this;
      this.$nextTick(async () => {
        const res = await getOrderWithholdListChart(paras).then((res) => {
          that.showDetailVisible.visible = true;
          that.showDetailVisible.data = res.data;
          that.showDetailVisible.title = res.data.legend[0];
        });
        await this.$refs.buschar.initcharts()
      });

    },
    //汇总趋势图
    async onsummaryClick (property) {
      //console.log("我是汇总趋势图传递的参数呀",property)
      if (property && property == "amountPaid") {
        await this.showchart();
      }
    }
  },
};
</script>

<style lang="scss" scoped></style>
