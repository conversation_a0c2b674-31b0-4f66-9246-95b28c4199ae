<template>
    <MyContainer >
       <template #header>
            <el-form  :inline="true" ref="topForm" :model="queryEnum" class="demo-ruleForm">
                  <el-form-item prop="timeRanges">
                    <el-date-picker v-model="queryEnum.timeRanges" type="daterange" unlink-panels range-separator="至"
                      start-placeholder="开始生效日期" end-placeholder="结束生效日期" style="width: 250px;margin-right: 5px;"
                      :clearable="true" :value-format="'yyyy-MM-dd'" @change="changeTime">
                    </el-date-picker>
                </el-form-item>
                <el-form-item >
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="edit({},'add')">新增</el-button>
                    <el-button type="primary" @click="edit({},'editBatch')">批量修改</el-button>
                </el-form-item>
            </el-form>
       </template>
    
        <template>
         <vxetablebase 
         :id="'sow20240913'" 
         :tablekey="'sow20240913'" 
         :tableData='tableData' 
         :tableCols='tableCols' 
         @sortchange='sortchange'
          :loading='loading' 
          :border='true' 
          :that="that" 
          ref="vxetable"  
          >
         </vxetablebase>
         <el-dialog :title="isEditOpen=='edit'?'编辑':isEditOpen=='add'?'新增':'批量修改'" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="450px"  v-dialogDrag>
            <el-form  ref="editForm" :model="editEnum" label-width="80px" style="margin-top: 40px;display: flex;flex-direction: column;align-items: center;" >
                <el-form-item prop="pickUnitPrice" label="拣货单价" :rules="{required:true,message:'拣货单价不能为空',trigger:'blur'}">
                  <el-input-number  style="width:200px" v-model="editEnum.pickUnitPrice" :precision="2" :step="1" ></el-input-number>
                </el-form-item>
                <el-form-item prop="sowUnitPrice" label="播种单价" :rules="{required:true,message:'播种单价不能为空',trigger:'blur'}">
                  <el-input-number  style="width:200px" v-model="editEnum.sowUnitPrice" :precision="2" :step="1" ></el-input-number>
                </el-form-item>
                <el-form-item v-if="isEditOpen !=='editBatch'" prop="effectiveDate" label="生效日期" :rules="{required:true,message:'生效日期不能为空',trigger:'blur'}" >
                  <el-date-picker
                  style="width:200px"
                    v-model="editEnum.effectiveDate"
                    type="date"
                    placeholder="选择日期"
                    value-format="yyyy-MM-dd"
                    >
                  </el-date-picker>
                </el-form-item>
                <el-form-item v-else prop="effectiveDate" label="生效日期" :rules="{required:true,message:'生效日期不能为空',trigger:'blur'}" >
                    <el-date-picker v-model="editEnum.effectiveDate" type="daterange" unlink-panels range-separator="至"
                      start-placeholder="开始生效日期" end-placeholder="结束生效日期" style="width:200px" 
                      :clearable="true" :value-format="'yyyy-MM-dd'" @change="changeEditTime">
                    </el-date-picker>
                </el-form-item>
               
            </el-form>
            <template slot="footer" >
                <el-button @click="dialogHisVisible = false">关闭</el-button>
                <el-button @click="saveEdit" type="primary" >保存</el-button>
            </template>
        </el-dialog>
              </template>
              <el-dialog title="播种订单" v-if="orderListVisible" :visible.sync="orderListVisible" width="70%" height="600px" v-dialogDrag>
                  <vxetablebase 
                    :id="'orderList20240913'" 
                    :tablekey="'orderList20240913'" 
                    :tableData='orderList' 
                    :tableCols='orderListTableCols' 
                    @sortchange='sortchange'
                      :loading='orderListLoading' 
                      :border='true' 
                      :that="that" 
                      ref="orderListVxetable"  
                    
                      />
                      <my-pagination ref="pageLog" :total="totalOrderList"  @get-page="getOrderList()"/>
              </el-dialog>
    
       <template #footer>
          <my-pagination ref="pager" :total="total"  @get-page="getList"/>
        </template>
     </MyContainer>
    </template>
    
    <script>
    import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
    import MyContainer from "@/components/my-container";
    import dayjs from 'dayjs';
    import {GetPriceMaintenance,EditPriceMaintenance,AddPriceMaintenance,BatchEditPriceMaintenance} from '@/api/wmsoperation/sow.js'
    
    import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
    
    
    const statuOption = [{
              value: '0',
              label: '离线'
            }, {
              value: '1',
              label: '在线'
            }, {
              value: '2',
              label: '异常'
            }]
    const tableCols = [
    
     { istrue: true,sortable: 'custom', prop: 'effectiveDate', label: '生效日期',  width: 'auto' ,formatter: (row,that) => dayjs(row.effectiveDate).format('YYYY-MM-DD')},
     {istrue: true, sortable: 'custom',prop:'pickUnitPrice',label: '拣货单价',  width: 'auto' ,},
     {istrue: true, sortable: 'custom',prop: 'sowUnitPrice', label: '播种单价',  width: '200px',},
     {istrue: true, sortable: 'custom',prop: 'createTime', label: '创建时间',  width: '200px',},
     { istrue: true,sortable: 'custom',prop: 'createUser', label: '创建人',   width: '200px',}, 
     { istrue: true,sortable: 'custom',prop: 'updateTime', label: '修改时间',   width: '200px',}, 
     { istrue: true,sortable: 'custom',prop: 'updateUser', label: '修改人',   width: '200px',}, 
     {type:'button',label:'操作',width:'150px',btnList:[
     {label:"编辑",/*display:(row)=>{return true},display:true,*/  handle:(that,row)=>that.edit(row,'edit')},
      ]
                                              },
    ];
    
    
    const orderListTableCols = [
    
     {istrue: true, sortable: 'custom',prop:'shipout_edison_sellable',label: '订单号',  width: '80px' , },
    ];
    
    export default {
     name: 'sow',
     components: { vxetablebase, MyContainer, OrderActionsByInnerNos},
     data() {
       return {
            that:this,
            statuOption: statuOption,
            queryEnum:{
                timeRanges:[],
                orderBy:'',
                isAsc: true,
            },
            editEnum:{
              pickUnitPrice:null,
              sowUnitPrice:null,
              effectiveDate:null,
            },
            tableData:[],
            tableCols:tableCols,
            loading:false,
            summaryarry:[],
            total:0,
            isEditOpen:'',//是否编辑唤起表单
            //订单dialog
            orderNo:null,
            orderListVisible:false,
            orderListTableCols:orderListTableCols,
            orderList:[],
            orderListLoading:false,
            totalOrderList:0,
            dialogHisVisible:false,
       };
     },
      async mounted(){
      
          
        await this.getList()
    
      },
     methods:{
        formatChanneInfo(value) {
          let info = ' '
          this.sowingChannelList.forEach(item => { if (item.id === value) info = item.name })
          return info
        },
        changeTime(e) {
          this.queryEnum.startTime = e ? e[0] : null
          this.queryEnum.endTime = e ? e[1] : null
        },
        changeEditTime(e) {
          this.editEnum.startDate = e ? e[0] : null
          this.editEnum.endDate = e ? e[1] : null
        },
        async getList(type){
          if(type == 'search'){
            this.$refs.pager.setPage(1)
          }
          let pager = this.$refs.pager.getPager()
    
          let params = {
            ...pager,
            ...this.queryEnum
            
          }
          this.loading = true
          const {list,total} =  await GetPriceMaintenance(params)
          this.loading = false
          this.tableData = list
         
          this.total = total
        },
        sortchange({ order, prop }) {
          if (prop) {
            this.queryEnum.orderBy = prop
            this.queryEnum.isAsc = order.indexOf("descending") == -1 ? true : false
            this.getList()
          }
        },
        
        async saveEdit(){
          this.$refs.editForm.validate(async(valid) => {
            if (valid) {
              const data  = this.isEditOpen == 'edit'? await EditPriceMaintenance(this.editEnum):this.isEditOpen == 'add'? await AddPriceMaintenance(this.editEnum):await BatchEditPriceMaintenance(this.editEnum)
              if(data.isSuccess){
                this.$message({ 
                  message:"操作成功",
                  type: "success" 
                });
                await this.getList()
                this.dialogHisVisible = false;
              }else{
                this.$message({ 
                  message:data.message,
                  type: "error" 
                });
              }
            }
          });
        },
        getOrderList(){
    
        },
        async sowing(row){
          const data = await  StartSow({ wave_id:row.id})
          if(data.isSuccess){
            this.$message({ 
              message:"操作成功",
              type: "success" 
            });
            await this.getList()
          }
        },
        // async edit(row){
        //   this.editEnum = row
        //   if(this.sowingChannelList.length>0){
        //     this.editEnum.sow_channel_id = this.sowingChannelList[0].id
        //   }
        //   this.dialogHisVisible = true;
        // },
        async edit(row,type) {
          if(type == 'edit'){
            this.isEditOpen = 'edit';
          }else if(type == 'add'){
            this.isEditOpen = 'add';
          }else if(type == 'editBatch'){
            this.isEditOpen = 'editBatch';
          }
          this.editEnum = JSON.parse(JSON.stringify(row))
          // 查找对应的播种通道 ID
         
          this.dialogHisVisible = true;
          this.$nextTick(() => {
            this.$refs.editForm.clearValidate();
          });
    },
        onsummaryClick(){
    
        }
     }
    };
    </script>
    
    <style lang="scss" scoped>
    
    </style>
    