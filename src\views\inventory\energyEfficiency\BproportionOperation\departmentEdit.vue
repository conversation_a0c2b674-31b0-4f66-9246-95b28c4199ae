<template>
    <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
      <div style="display: flex; flex-direction: row; align-items: center;">
        <!-- {{ruleForm}}
        {{ruleoptions.platformlist}} -->
        <el-date-picker v-model="ruleForm.datetimerange" unlink-panels range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期"  type="datetimerange" style="width: 250px;margin-right: 5px;" :clearable="false"
            format="yyyy-MM-dd" value-format="yyyy-MM-dd" >
        </el-date-picker>
        <el-select v-model="ruleForm.fullNameBefore" style="width: 150px;margin-right: 5px;" placeholder="片区" class="publicCss" clearable >
          <el-option v-for="item in ruleoptions.regionNamelist" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select filterable v-model="ruleForm.platformBefore" placeholder="请选择平台"   collapse-tags style="width: 150px;margin-right: 5px;"
             clearable >
            <el-option v-for="(item,i) in ruleoptions.platformlist" :key="i" :label="item" :value="item" />
        </el-select>
        <el-select filterable v-model="ruleForm.groupIdBefore" collapse-tags clearable placeholder="运营组"
            style="width: 160px"   >
            <el-option key="无运营组" label="无运营组" :value="'无运营组'"></el-option>
            <el-option v-for="(item,i) in ruleoptions.grouplist" :key="i" :label="item.label" :value="item.value" />
        </el-select>
        <div style="width: 150px; font-weight: 600; text-align: center">对比</div>
        <el-select v-model="ruleForm.fullNameAfter" style="width: 150px;margin-right: 5px;" placeholder="片区" class="publicCss" clearable>
          <el-option v-for="item in ruleoptions.regionNamelist" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select filterable v-model="ruleForm.platformAfter" placeholder="请选择平台"   collapse-tags style="width: 150px;margin-right: 5px;"
             clearable >
            <el-option v-for="(item,i) in ruleoptions.platformlist" :key="i" :label="item" :value="item" />
        </el-select>
        <el-select filterable v-model="ruleForm.groupIdAfter" collapse-tags clearable placeholder="运营组"
            style="width: 160px"  >
            <el-option key="无运营组" label="无运营组" :value="'无运营组'"></el-option>
            <el-option v-for="(item,i) in ruleoptions.grouplist" :key="i" :label="item.label" :value="item.value" />
        </el-select>
        <el-button @click="getList" type="primary">查询</el-button>
        <div style="width: 130px; text-align: center">
            <el-radio-group v-model="radio" style="width: 130px;">
                <el-radio :label="1" >月</el-radio>
                <el-radio :label="2" >日</el-radio>
            </el-radio-group>
        </div>


      </div>
      <div style="margin-top: 20px;">
        <el-checkbox-group v-model="ruleForm.checkList" @change="handleCheckedCitiesChange($event)">
            <el-checkbox label="人数"></el-checkbox>
            <el-checkbox label="人数占比"></el-checkbox>
            <el-checkbox label="三项占比"></el-checkbox>
            <el-checkbox label="差异值"></el-checkbox>
            <el-checkbox label="四项低于均值人数"></el-checkbox>
            <el-checkbox label="四项占比"></el-checkbox>
            <el-checkbox label="订单量"></el-checkbox>
            <el-checkbox label="销售金额"></el-checkbox>

            <el-checkbox label="毛四利润"></el-checkbox>
            <el-checkbox label="订单量占比"></el-checkbox>
            <el-checkbox label="销售金额占比"></el-checkbox>
            <el-checkbox label="毛四利润占比"></el-checkbox>
            <el-checkbox label="按毛四分配人力资源"></el-checkbox>
            <el-checkbox label="人数差异值"></el-checkbox>
            <el-checkbox label="差异值*50%"></el-checkbox>
            <el-checkbox label="按毛四定编制人数"></el-checkbox>
            <el-checkbox label="增减人数"></el-checkbox>

        </el-checkbox-group>
      </div>
      <div  style="height: 100%; width: 100%; margin-top: 50px;min-height: 300px;">
        <buschar v-if="viewData5" ref="view5" :analysisData="viewData5"  :thisStyle="thisStyleView5" :gridStyle="gridStyleView5" :toolbox="null">
                </buschar>
        <div style="height: 100%; width: 100%;display: flex; justify-content: center; align-items: center; " v-else>
            <i class="el-icon-loading" color="#409EFF" style="color: #409EFF; font-size: 25px;"></i>
        </div>
      </div>

      <!-- <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
        <el-button @click="cancellationMethod">取消</el-button>
        <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
      </div> -->
    </div>
  </template>

  <script>
  import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
  import MyConfirmButton from '@/components/my-confirm-button'
  import { getOperatePerformanceProportionPageReport, getOperateListValue } from '@/api/people/peoplessc.js';
  import buschar from '@/components/Bus/buscharforShooting.vue';
  export default {
    name: 'departmentEdit',
    components: {
      inputNumberYh, MyConfirmButton, buschar
    },
    props: {
      ListInfo: {
        type: Object,
        default: () => {
          return {}
        }
      },
      editInfo: {
        type: Object,
        default: () => {
          return {}
        }
      },
      options: {
        type: Object,
        default: () => {
          return {}
        }
      }
    },
    data() {
      return {
        viewData5: null,
        thisStyleView5: { width: '100%', height: '600px', 'box-sizing': 'border-box', 'line-height': '500px' },
        gridStyleView5: { left: '1%', right: 15, bottom: 20, top: '10%', containLabel: true },
        selectProfitrates: [],
        ruleForm: {
          label: '',
          name: '',
          checkList: [],
          radio: 1,
          "startQueryTime": "", // 开始查询时间
          "fullNameBefore": "", // 查询前的区域部门小组职位
          "fullNameAfter": "", // 查询后的区域部门小组职位
          "platformBefore": "", // 查询前的平台
          "platformAfter": "", // 查询后的平台
          "deptNameBefore": "", // 查询前的部门名称
          "deptNameAfter": "", // 查询后的部门名称
          "groupIdBefore": "", // 查询前的部门名称
          "groupIdAfter": "", // 查询后的部门名称
          "userNameBefore": "", // 查询前的姓名
          "userNameAfter": "",// 查询后的姓名
          "endQueryTime": "", // 结束查询时间
        },
        radio: 1,
        ruleoptions: {

        },
        rules: {
          name: [
            { required: true, message: '请输入活动名称', trigger: 'blur' }
          ],
          label: [
            { required: true, message: '请输入活动名称', trigger: 'blur' }
          ]
        }
      }
    },

    async mounted() {
    //   this.$nextTick(() => {
    //     this.$refs.refruleForm.clearValidate();
    //   });
    //   this.ruleForm.fullNameBefore = this.ListInfo.regionName
    //   this.ruleForm = { regionName: this.ListInfo.regionName };
    console.log("打印数据===", this.ListInfo);
    console.log("this.editInfo",this.editInfo);
      this.ruleForm.datetimerange = [this.ListInfo.startQueryTime, this.ListInfo.endQueryTime];
      this.ruleoptions = {...this.options};
      this.ruleForm.fullNameBefore = this.editInfo.regionName;
      this.ruleForm.groupIdBefore = this.editInfo.groupId;
      this.ruleForm.deptNameBefore = this.editInfo.deptName;
      this.ruleForm.platformBefore = this.editInfo.platform;

      this.ruleForm.checkList = [this.editInfo.checkName];

      this.getList();
    },
    methods: {
        handleCheckedCitiesChange(){
            // this.ruleForm.checkList = this.ruleForm.checkList.filter(item => item != '人数');
            console.log("打印数据1", this.ruleForm.checkList);
            console.log("打印数据2", this.viewData5);
            this.getList();
        },
        onchangeplatform(){
        //    this.ruleoptions.platformlist = this.$emit("onchangeplatform");
           this.getList();
        },
      async getList(){
            this.viewData5 = null;
            let params = {
                ...this.ruleForm,
                startQueryTime: this.ruleForm.datetimerange&&this.ruleForm.datetimerange.length>0? this.ruleForm.datetimerange[0]:null,
                endQueryTime: this.ruleForm.datetimerange&&this.ruleForm.datetimerange.length>0? this.ruleForm.datetimerange[1]:null,
                groupBy: this.radio == 1?'月':'日'
            }
            const { data, success } = await getOperatePerformanceProportionPageReport(params)
            if(!success){
                return
            }
            data.yAxis = {
                type: 'value'
            };


            function filterArrayBySuffix(arrayA, arrayB) {
                return arrayB.filter(item => {
                    const suffix = item.split('-')[0];
                    return arrayA.includes(suffix);
                });
            }
            function filterArrayBySuffixnei(arrayA, arrayB) {
                return arrayB.filter(item => {
                    const suffix = item.name.split('-')[0];
                    return arrayA.includes(suffix);
                });
            }

            data.legend = filterArrayBySuffix(this.ruleForm.checkList, data.legend);
            data.series = filterArrayBySuffixnei(this.ruleForm.checkList, data.series);


            this.viewData5 = data;

      },
      cancellationMethod() {
        this.$emit('cancellationMethod');
      },
      submitForm(formName) {
        console.log(this.ruleForm.label, 'this.ruleForm.label');
        this.$refs[formName].validate(async(valid) => {
          if (valid) {
          // alert('submit!');
          const { data, success } = await sscDataSubmit(this.ruleForm)
          if(!success){
              return
          }
          await this.$emit("search");

          } else {
          console.log('error submit!!');
          return false;
          }
      });
      //   this.$confirm('是否保存?', '提示', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     type: 'warning'
      //   }).then(async () => {
      //     this.$refs[formName].validate((valid) => {
      //       if (valid) {
      //         alert('submit!');
      //       } else {
      //         console.log('error submit!!');
      //         return false;
      //       }
      //     });
      //   }).catch(() => {
      //   });
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
      },
    }
  }
  </script>
  <style scoped lang="scss">
  .publicCss {
    width: 80%;
  }
  </style>
