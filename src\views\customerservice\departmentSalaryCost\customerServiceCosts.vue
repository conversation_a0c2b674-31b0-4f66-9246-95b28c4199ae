<template>
  <!-- 客服成本核算 -->
  <container v-loading="pageLoading">
      <template #header>
          <el-row>

                  <el-input v-model.trim="filter.name" placeholder="请输入姓名" style="width: 140px; " :maxlength="50" clearable>
                  </el-input>


                 <el-select v-model="filter.region" placeholder="请选择区域" filterable style="width: 160px; margin-left: 10px" clearable>
                     <el-option :label="item.label" :value="item.value" v-for="(item,index) in quyu" :key="index"  />
                 </el-select>


                 <el-select v-model="filter.platform" placeholder="请选择平台" clearable>
                  <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>

                 <!-- <el-select v-model="filter.subgroup" placeholder="请选择分组" filterable style="width: 160px; margin-left: 10px" clearable>
                     <el-option :label="item" :value="item" v-for="(item,index) in quyuList" :key="index" />
                 </el-select> -->

                 <!-- <el-cascader
                 style="width: 200px; margin-left: 10px"
                  v-model="filter.subgroupList"
                  :options="deptList"
                  filterable
                  placeholder="请选择分组"
                  :props="props"
                  collapse-tags
                  :show-all-levels="false"
                  clearable></el-cascader> -->

                 <el-select v-model="filter.subgroup" placeholder="请选择分组" clearable multiple collapse-tags filterable >
                  <el-option v-for="item in subgroupListArr" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>

                 <el-select v-model="filter.state" placeholder="人员状态" filterable style="width: 160px; margin-left: 10px" clearable>
                     <el-option :label="item.label" :value="item.value" v-for="(item,index) in peopleStatus" :key="index" />
                 </el-select>

                 <el-select v-model="filter.grade" placeholder="客服等级" filterable style="width: 160px; margin-left: 10px" clearable>
                     <el-option :label="item.label" :value="item.value" v-for="(item,index) in peopleDevp" :key="index" />
                 </el-select>

                 <!-- <el-date-picker style="width: 260px; margin-left: 10px" v-model="filter.costDate" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                      range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false"></el-date-picker> -->

                <el-date-picker v-model="filter.costDate" type="date" style="width: 130px; margin-left: 10px" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" placeholder="选择日期"
                         :clearable="true">
                </el-date-picker>


             <el-button-group style="margin-left: 10px">
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button type="primary" style="margin-left: 10px" @click="dialogVisiblejlfuc">操作记录</el-button>
             </el-button-group>

             <!-- <el-button type="primary" style="margin-left: 10px" @click="newadd">新增人员</el-button> -->
             <el-button type="primary" style="margin-left: 10px;" @click="downFile">下载模板</el-button>
             <el-button type="primary" @click="importantfile">导入</el-button>
             <el-button type="primary" @click="onExeprotShootingTask">导出</el-button>
          </el-row>
      </template>
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
          :isSelectColumn="false" :showsummary='true' :summaryarry='summaryarry' :tablefixed='false' :tableData='tableData' :tableCols='tableCols'
          :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:95%;margin: 0">
      </ces-table>
      <!-- <template #footer>
          <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
      </template> -->
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />

      <el-dialog
      :title="isEdit ? '编辑' : '添加'"
      :visible.sync="peoplediag"
      :close-on-click-modal="false"
      v-dialogDrag
    >
      <el-form ref="refpermissionDot" :model="permissionDot" label-width="100px" v-if="peoplediag">
        <el-form-item label="区域" prop="region" :rules="{ required: true, message: '请选择区域', trigger: ['blur','change'] }">
          <!-- <el-input v-model="permissionDot.region" auto-complete="off" /> -->
          <el-select v-model="permissionDot.region" placeholder="请选择区域" filterable style="width: 300px;" clearable>
                     <el-option :label="item.label" :value="item.value" v-for="(item,index) in quyu" :key="index"  />
                 </el-select>
        </el-form-item>


        <el-form-item label="分组" prop="subgroup" :rules="{ required: true, message: '请选择分组', trigger: ['blur','change'] }">
          <!-- <el-cascader
          style="width: 300px;"
          v-model="permissionDot.subgroup"
          :options="deptList"
          placeholder="请选择分组"

          filterable
          :props="{ multiple: false, label: 'full_name', value: 'full_name' }"
          collapse-tags
          :show-all-levels="false"
          clearable></el-cascader> -->
          <el-select v-model="permissionDot.subgroup" placeholder="请选择分组" clearable multiple>
            <el-option v-for="item in subgroupListArr" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <!-- @change="subgroupchange" -->



        <el-form-item label="姓名" prop="name" :rules="{ required: true, message: '请填写姓名', trigger: 'input' }">
          <el-input style="width: 300px;" :maxlength="50"  v-model="permissionDot.name" auto-complete="off" />
        </el-form-item>


        <el-form-item label="所在平台" prop="platform" :rules="{ required: true, message: '请选择平台', trigger: ['blur','change'] }">
          <el-select v-model="permissionDot.platform" placeholder="请选择平台" clearable style="width: 300px;" >
            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
           <!-- <el-input-number :disabled="true" v-model="permissionDot.platform"  :min="1" :max="50" label="排序"></el-input-number> -->
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click.native="peoplediag = false">取消</el-button>
          <el-button @click="onSubmitDot" type="primary" v-throttle="3000">保存</el-button>
          <!-- <my-confirm-button type="submit" :validate="validateDot" :loading="permissionDot.loading" @click="onSubmitDot" /> -->
        </div>
      </template>
    </el-dialog>


    <!-- //导入 -->
    <el-dialog title="导入" :visible.sync="dialogVisibleSyj" width="30%" v-dialogDrag>
      <span>
        <el-date-picker v-model="importcostDate" type="date" style="width: 180px; margin-left: 10px" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" placeholder="选择日期"
                  :clearable="false">
        </el-date-picker>

        <el-upload ref="upload2" style="width: 100%;" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2" :on-change="onsuccess">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传
          </my-confirm-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 操作记录 -->
    <el-dialog title="操作记录" :visible.sync="dialogVisiblejl" width="50%" height="500px" v-dialogDrag>
      <el-row>
        <el-date-picker v-model="reportFilter.dateTime" type="daterange" style="width: 250px;" :picker-options="pickerOptions" range-separator="——"
            start-placeholder="开始时间" end-placeholder="结束时间" @change="cleartime"
            value-format="yyyy-MM-dd">
        </el-date-picker>
        <el-input style="width: 170px;" :maxlength="50" placeholder="操作人" clearable v-model="reportFilter.updateUserName" auto-complete="off" />
        <el-button @click="searchReport('search')" type="primary" v-throttle="3000">查询</el-button>
      </el-row>


      <el-table v-loading="reportloading" :data="reportData" class="tableone" style="width: 100%;height: 420px" height="420">
          <el-table-column prop="updateTime" label="操作时间" width="180" align="center">
          </el-table-column>
          <el-table-column prop="updateUserName" label="操作人" width="180" align="center">
          </el-table-column>
          <el-table-column prop="operationContent" label="操作内容" align="center">
          </el-table-column>
      </el-table>
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :page-sizes="[50, 100, 150, 200]" :current-page.sync="reportFilter.currentPage"
          :page-size="reportFilter.pageSize" layout="total,sizes, prev, pager, next" :total="reportFilter.total">
      </el-pagination>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisiblejl = false">关闭</el-button>
      </span>
    </el-dialog>

  </container>
 </template>
 <script>
 import { formatTime } from "@/utils";
 import dayjs from "dayjs";
 import container from '@/components/my-container';
 import cesTable from '@/components/Table/table.vue';
 import YhImgUpload1 from "@/components/upload/yh-img-upload1.vue";
 import { formatLinkProCode, platformlist } from '@/utils/tools'

 import fliterjs from "@/views/customerservice/departmentSalaryCost/fliterjs.js";
 import MyConfirmButton from "@/components/my-confirm-button";

 import { customerCostPage, customerPersonnelSave, customerCostImport, customerCostRemove, customerCostExport, customerPersonnelDetail, customerPersonnelUpdate,
  operationLogPage } from '@/api/bladegateway/yunhangiscustomer.js';



 const tableCols = [

  { istrue: true, prop: 'region', label: '区域', width: '100', sortable: 'custom', formatter: (row) => row.regionName ? row.regionName : null },
  // { istrue: true, prop: 'subgroup', label: '分组',  width: '160', sortable: 'custom', },
  { istrue: true, prop: 'subgroup', label: '分组',  width: '200', sortable: 'custom', formatter: (row)=> row.subgroupName },
  { istrue: true, prop: 'platform', label: '所在平台', width: '100', sortable: 'custom', formatter: (row) => row.platformName ? row.platformName : null },
  { istrue: true, prop: 'name', label: '姓名', width: '100', sortable: 'custom',  },
  { istrue: true, prop: 'state', label: '人员状态', width: '100', sortable: 'custom', formatter: (row) => row.stateName ? row.stateName : null  },
  { istrue: true, prop: 'grade', label: '客服等级', width: '120', sortable: 'custom', formatter: (row) => row.gradeName ? row.gradeName : null  },
  { istrue: true, prop: 'receptionNum', label: '接待量提成依据（个）', width: '160', sortable: 'custom' },
  { istrue: true, prop: 'saleAmount', label: '销售额提成依据（元）', width: '160', sortable: 'custom' },
  { istrue: true, prop: 'royaltyRate', label: '提成系数', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'royaltyAmount', label: '提成金额', width: 'auto', sortable: 'custom' },
  // formatter:(row)=> row.fenShu,
  { istrue: true, prop: 'actualAmount', label: '实发金额', width: '120', sortable: 'custom',  },
  { istrue: true, prop: 'totalAmount', label: '合计金额', width: '120', sortable: 'custom' },

  { istrue: true, prop: 'costDate', label: '时间', width: '120', sortable: 'custom', formatter: (row) => row.costDate ? formatTime(row.costDate, 'YYYY-MM-DD') : null  },
  { istrue: true, prop: 'updateTime', label: '编辑时间', width: '120', sortable: 'custom'  },

  { istrue: true, prop: 'updateUserNo', label: '编辑人', width: '120', sortable: 'custom', formatter: (row) => row.updateUserName ? row.updateUserName : null  },
  {
        istrue: true, type: "button", label: '操作', align: 'center', sortable: false, width: "100", fixed: "right",
        btnList: [
          // { label: "编辑", handle: (that, row) => that.onEdit(row) },
        { label: "删除", type: "danger", handle: (that, row) => that.ondelrow(row) }]
    }
 ];


 //const endDate = formatTime(new Date(), "YYYY-MM-DD");

 export default ({
  name: "Users",
  components: { container, cesTable, YhImgUpload1, MyConfirmButton },
  mixins: [fliterjs],
  data () {
      return {
          dialogVisibleStock: false,
          dialogVisibleSyj: false,
          dialogVisiblejl: false,
          fileList: [],
          gysList: [],
          isEdit: true,
          peoplediag: false,

          permissionDot: {
            name: '',
            platform: -1,
            region: -1,
            subgroup: []
          },
          reportFilter: {
            pageSize: 50,
            currentPage: 1,
            dateTime: []
          },
          reportData: [],
          dialogEdit: false,
          stockForm: {
              stock: 0,
              id: 0
          },
          editForm: {
          },
          form: {
              goodTypeList: [],
          },
          reportloading: false,
          quyuList: [],
          tableCols: tableCols,
          dialogMenuType: false,
          uploadLoading: false,
          dialogVisibleData: false,
          that: this,
          tableHandles: null,
          tableData: [],
          total: 0,
          importcostDate: '',
          pager: { OrderBy: "", IsAsc: false },
          listLoading: false,
          pageLoading: false,
          summaryarry: {},
          editrow: {},
          sels: [],

      };
  },
  async mounted () {
      await this.onSearch();
  },
  methods: {
    newadd(){
      this.permissionDot.platform = '';
      this.permissionDot.name = '';
      this.permissionDot.subgroup = [];
      this.permissionDot.region = '';
      // this.permissionDot.subgroupList = [];

      this.$nextTick(async () => {
        this.peoplediag = true;
        this.isEdit = false;
      })

    },
    importantfile(){
      this.importcostDate = '';

      this.dialogVisibleSyj = true;
    },
    subgroupchange(val){
      // console.log("1111222", val)
      // if(val){
      //   // this.reportFilter.startQueryTime = val[0];
      //   // this.reportFilter.endQueryTime = val[1];
      // }else{
      //   // this.subgroup = null;
      //   this.permissionDot.subgroup = this.permissionDot.subgroup.join(",");
      // }
    },
    cleartime(val){
      console.log("更改时间",val)
      if(val){
        this.reportFilter.startQueryTime = val[0];
        this.reportFilter.endQueryTime = val[1];
      }else{
        this.reportFilter.startQueryTime = null;
        this.reportFilter.endQueryTime = null;
      }
    },
    async dialogVisiblejlfuc(){
      this.dialogVisiblejl = true;
      // this.reportFilter.dateTime = this.reportFilter.dateTime?this.reportFilter.dateTime:[];
      // if(this.reportFilter.dateTime.length>0){
      //   this.reportFilter.startQueryTime = this.reportFilter.dateTime[0];
      //   this.reportFilter.endQueryTime = this.reportFilter.dateTime[1];
      // }
      this.reportFilter.operationItem = '客服成本'
      this.reportloading = true;
      const params = {
        ...this.reportFilter
      };
      var res = await operationLogPage(params);
      this.reportloading = false;
      if(!res.success){
          return
      }
      this.reportData = res.data.list;
      this.reportFilter.total = res.data.total;
      console.log("res777788888888", res)
    },
    async onExeprotShootingTask(){
        var pager = this.$refs.pager.getPager();
        const params = {
            ...pager,
            ...this.pager,
            ...this.filter
        };
        this.pageLoading = true;
        var res = await customerCostExport(params);
        // if (res?.data?.type == 'application/json') {return;}
        this.pageLoading = false;
        const aLink = document.createElement("a");
        var blob = new Blob([res], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '客服成本统计导出.xlsx')
        aLink.click()

    },
    async downFile() {
        window.open("/static/excel/kefuxinzi/成本核算表.xlsx", "_blank");
    },
    searchReport(type){
      if (type == 'search') {
        this.reportFilter.currentPage = 1;
      }
      this.dialogVisiblejlfuc();
      console.log("搜索", this.reportFilter)
    },
    handleCurrentChange(val){
      this.reportFilter.currentPage = val;
      this.searchReport();
      console.log("页变化",val)
    },
    handleSizeChange(val){
      this.reportFilter.pageSize = val;
      this.searchReport();
      console.log("大小变化",val)
    },
    async onSubmitupload2() {
      if(!this.importcostDate){
        this.$message.error("请选择导入时间");
        return;
      }
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        this.uploadLoading=false;
        return false;
      }
      this.uploadLoading=true;
      this.$refs.upload2.submit()
    },
    onsuccess(file, fileList){
      this.fileList = fileList;
    },
    async uploadSuccess2(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("file", item.file);
      const res = await customerCostImport([form, this.importcostDate]);
      if(!res.success){
        return
      }
      this.dialogVisibleSyj = false;
      this.$message({ message: '上传成功,正在导入中...', type: "success" });
    },
    onSubmitValidate: function () {
        let isValid = true;
        this.$refs.refpermissionDot.validate(valid => {
            isValid = valid
        })
        return isValid;
    },
    async onSubmitDot(){

    if (!this.onSubmitValidate()) {
        return;
    }

    if(this.isEdit){
      this.permissionDot.id = this.editrow.personnelId;

      // this.permissionDot.subgroup = [this.permissionDot.subgroupList.join(",")];
      // this.permissionDot.subgroup = this.permissionDot.subgroupList.join(",");

      const res = await customerPersonnelUpdate(this.permissionDot);
      if(!res.success){
          return
      }
      this.$message({
          message: '保存成功',
          type: 'success'
      });
      this.onSearch();
      this.peoplediag= false;
      console.log(res)
      return
    }

    // this.permissionDot.subgroup = this.permissionDot.subgroupList.join(",");

    const res = await customerPersonnelSave(this.permissionDot);
    if(!res.success){
        return
    }
    this.$message({
        message: '保存成功',
        type: 'success'
    });
    this.onSearch();
    this.peoplediag= false;
    console.log(res)
      // this.permissionDot
    },
    async onEdit(row){
      console.log("44444", row)
      this.editrow = row;
      this.isEdit = true;
      this.peoplediag = true;
      this.permissionDot.platform = Number(row.platform);
      this.permissionDot.name = row.name;
      this.permissionDot.subgroup = row.subgroup.split(',');
      this.permissionDot.region = Number(row.region);


        // var res = await customerPersonnelDetail(row);
        // if(!res.success){
        //      return
        //  }
        //  console.log("打印详情", res)
    },
    ondelrow(row){
            this.$confirm("是否确定删除, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                this.addressListLoading = true;
                const form = new FormData();
                form.append("ids", row.id);
                var ret = await customerCostRemove(form);
                this.addressListLoading = false;
                this.onSearch();
            });
     },
     async getquyu(){
         const res = await getAreaSetList({getLevel: 1});
         if(!res.success){
             return
         }
         this.quyuList = res.data;
         if(!this.filter.zoneName){
             this.filter.zoneName = this.quyuList[0];
         }
     },
      async getsize(){
       let params = {

       }
       const res = await getOrderFoodMenuProvier();
          if(!res.success){
              return
          }
          this.gysList = res.data;
      },
      async getgyss(){
          const res = await getOrderFoodMenuProvier();
          if(!res.success){
              return
          }
          this.gysList = res.data;
          this.filter.gysName = this.gysList[0];
      },
      async getGoodType () {
          const res = await getGoodTypeAsync();
          this.form.goodTypeList = res?.data;
      },
      // async onSearch () {
      //   let newarr = [];
      //   this.filter.subgroupList.map((item)=>{
      //     if(item.length>0){
      //       newarr.push(item.join(","))
      //     }
      //   })
      //   this.filter.subgroup = newarr;
      //   this.$refs.pager.setPage(1);

      //   console.log("查询条件", this.filter)

      //   await this.getList();

      // },
      async sortchange (column) {
          if (!column.order)
              this.pager = {};
          else
              this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
          this.$refs.pager.setPage(1);
          await this.onSearch();
      },
      async getList () {

          // if (this.filter.orderMenuDateRange && this.filter.orderMenuDateRange.length > 0) {
          //     this.filter.orderMenuStartDate = this.filter.orderMenuDateRange[0];
          //     this.filter.orderMenuEndDate = this.filter.orderMenuDateRange[1];
          // }
          var that = this;
          this.listLoading = true;
          var pager = this.$refs.pager.getPager();
          const params = { ...pager, ...this.pager, ...this.filter, ...this.myfilter };
          const res = await customerCostPage(params).then(res => {
              that.total = res.data?.total;
              that.tableData = res.data?.list;
              // that.tableData = [{},{}];

              that.summaryarry = res.data?.summary;
          });


          this.listLoading = false;
      },
  }
 })
 </script>
 <style scoped>
.tableone ::v-deep .el-table__body-wrapper{
  height: 420px !important;
}
::v-deep .el-cascader__search-input{
  margin: 2px 0 2px 5px;
  height: 18px;
  align-items: center;
}
::v-deep .el-select__tags-text {
  max-width: 60px;
}
 </style>

