<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-input v-model.trim="ListInfo.expressCompany" maxlength="50" autocomplete="off" clearable
                    style="width: 220px;" placeholder="请输入快递公司" class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="handleAdd">新增</el-button>
                <el-button type="primary" @click="importProps">导入</el-button>
                <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            :id="'TMZD20240826112730'" :editconfig="{ trigger: 'manual', mode: 'row' }" @sortchange='sortchange'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="120">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <el-button type="text" @click="editRow(row)">编辑</el-button>
                            <el-button type="text" @click="handleDelete(row.id)">删除</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog :title="isEdit ? '编辑' : '新增'" :visible.sync="addVisible" width="30%" v-dialogDrag>
            <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="150px" v-if="addVisible">
                <el-form-item label="快递公司" prop="expressCompany">
                    <el-input v-model.trim="ruleForm.expressCompany" maxlength="50" autocomplete="off" clearable
                        style="width: 220px;" placeholder="请输入快递公司" />
                </el-form-item>
                <el-form-item label="省份" prop="sheng">
                    <el-input v-model.trim="ruleForm.sheng" maxlength="10" autocomplete="off" clearable
                        style="width: 220px;" placeholder="请输入省份" />
                </el-form-item>
                <el-form-item label="首重（kg）" prop="startWeight">
                    <el-input-number v-model="ruleForm.startWeight" :min="0" :max="9999" :controls="false"
                        placeholder="请输入首重（kg）" style="width: 220px;" :precision="0" />
                </el-form-item>
                <el-form-item label="首价" prop="startWeightFee">
                    <el-input-number v-model="ruleForm.startWeightFee" :min="0" :max="9999.99" :controls="false"
                        placeholder="请输入首价" style="width: 220px;" :precision="2" />
                </el-form-item>
                <el-form-item label="含首重（kg）" prop="contaisWeight">
                    <el-input-number v-model="ruleForm.contaisWeight" :min="0" :max="9999" :controls="false"
                        placeholder="请输入含首重（kg）" style="width: 220px;" :precision="0" />
                </el-form-item>
                <el-form-item label="续重单位（kg）" prop="continueWeightUnit">
                    <el-input-number v-model="ruleForm.continueWeightUnit" :min="0" :max="9999" :controls="false"
                        placeholder="请输入续重单位（kg）" style="width: 220px;" :precision="0" />
                </el-form-item>
                <el-form-item label="续重价" prop="continueWeightFee">
                    <el-input-number v-model="ruleForm.continueWeightFee" :min="0" :max="9999.99" :controls="false"
                        placeholder="请输入续重价" style="width: 220px;" :precision="2" />
                </el-form-item>
            </el-form>
            <div class="btnGroup">
                <el-button @click="addVisible = false">取消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import {
    importCustomNormsFreightFeeSet,
    exportCustomNormsFreightFeeSet,
    getCustomNormsFreightFeeSet,
    saveCustomNormsFreightFeeSet,
    deleteCustomNormsFreightFeeSet
} from '@/api/inventory/customNormsGoods'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressCompany', label: '快递公司', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'sheng', label: '省份', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'startWeight', label: '首重（kg）' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'startWeightFee', label: '首重价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'contaisWeight', label: '含首重（kg）' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'continueWeightUnit', label: '续重单位（kg）' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'continueWeightFee', label: '续重价', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            fileList: [],
            importLoading: false,
            importVisible: false,
            file: null,
            addVisible: false,
            ruleForm: {
                expressCompany: null,//快递公司
                sheng: null,//省
                startWeight: undefined,//首重
                startWeightFee: undefined,//首价
                contaisWeight: undefined,//含首重(kg)
                continueWeightUnit: undefined,//续重单位(kg)
                continueWeightFee: undefined,//续重价
            },
            rules: {
                expressCompany: [{ required: true, message: '请选择快递公司', trigger: 'blur' }],
                sheng: [{ required: true, message: '请输入省', trigger: 'blur' }],
                startWeight: [{ required: true, message: '请输入首重', trigger: 'blur' }],
                startWeightFee: [{ required: true, message: '请输入首价', trigger: 'blur' }],
                contaisWeight: [{ required: true, message: '请输入含首重(kg)', trigger: 'blur' }],
                continueWeightUnit: [{ required: true, message: '请输入续重单位(kg)', trigger: 'blur' }],
                continueWeightFee: [{ required: true, message: '请输入续重价', trigger: 'blur' }],
            },
            isEdit: false
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        handleDelete(id) {
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await deleteCustomNormsFreightFeeSet({ id })
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                    this.getList()
                } else {
                    this.$message({
                        type: 'error',
                        message: '删除失败!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        submitForm(formName) {
            // const map = {
            //     startWeight: '首重',
            //     startWeightFee: '首价',
            //     contaisWeight: '含首重(kg)',
            //     continueWeightUnit: '续重单位(kg)',
            //     continueWeightFee: '续重价'
            // }
            // for (let key in map) {
            //     if (this.ruleForm[key] === undefined || this.ruleForm[key] === null || this.ruleForm[key] === 0) return this.$message.error(`${map[key]}不能为空或0`)
            // }
            if (this.ruleForm.contaisWeight > this.ruleForm.startWeight) return this.$message.error('含首重不能大于首重')
            // if (this.ruleForm.contaisWeight > this.ruleForm.startWeight) return this.$message.error('含首重不能大于首重')
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    const { success } = await saveCustomNormsFreightFeeSet(this.ruleForm)
                    if (success) {
                        this.$message.success('提交成功')
                        this.addVisible = false
                        this.getList()
                    } else {
                        this.$message('提交失败!!');
                        return false;
                    }
                } else {
                    this.$message('提交失败!!');
                    return false;
                }
            });
        },
        handleAdd() {
            this.ruleForm = {
                expressCompany: null,//快递公司
                sheng: null,//省
                startWeight: undefined,//首重
                startWeightFee: undefined,//首价
                contaisWeight: undefined,//含首重(kg)
                continueWeightUnit: undefined,//续重单位(kg)
                continueWeightFee: undefined,//续重价
            }
            this.isEdit = false
            this.addVisible = true
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            //没有时间就提示
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            this.importLoading = true
            await importCustomNormsFreightFeeSet(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        editRow(row) {
            this.ruleForm = JSON.parse(JSON.stringify(row))
            this.isEdit = true
            this.addVisible = true
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportCustomNormsFreightFeeSet(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '快递费设置' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async changeTime(e) {
            this.ListInfo.startTime = e ? e[0] : null
            this.ListInfo.endTime = e ? e[1] : null
            await this.getList()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getCustomNormsFreightFeeSet(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>
