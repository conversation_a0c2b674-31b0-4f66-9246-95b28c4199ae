<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss" />
                <el-input v-model="ListInfo.deviceId" placeholder="设备编号" clearable class="publicCss" />

                <div>
                    <el-button type="primary" @click="getList('search')">查询</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :summaryarry='summaryarry' :showsummary='true' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { electronicScalePage } from '@/api/order/orderData'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'id', label: '重量id', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'deviceId', label: '设备编号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'macAddress', label: '设备硬件地址', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'lastWeight', label: '旧重量(g)', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight', label: '新重量(g)', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createTime', label: '称重时间', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            that: this,
            summaryarry: {},
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口

          if (this.ListInfo.startTime && this.ListInfo.startTime.length < 16) {
            this.ListInfo.startTime = this.ListInfo.startTime + " 00:00:00";
          }
          if (this.ListInfo.endTime  && this.ListInfo.endTime.length < 16) {
            this.ListInfo.endTime = this.ListInfo.endTime + " 23:59:59";
          }
            try {
                const { data, success } = await electronicScalePage(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.summaryarry = data.summary

                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
