<template>
    <container v-loading="pageLoading">
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :showsummary="true"
                :tableData='list' :tableCols='tableCols' :isSelection="false" :loading="listLoading" :tableHandles='tableHandlesDc'>
            </ces-table>
        </template>
        <!-- <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template> -->
    </container>
</template>

<script>
    import container from '@/components/my-container/noheader'
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import dayjs from "dayjs";
    import { formatTime, } from "@/utils";
    import { formatLinkProCode, formatSendWarehouse, formatExpressCompany } from "@/utils/tools";
    import { exportOrderWithholdListTotal, getOrderWithholdListTotal } from "@/api/order/orderdeductmoney"
    import { getBianManPositionList } from '@/api/inventory/warehouse'
    import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    const tableCols = [
        { istrue: true, prop: 'brandName', label: '采购组', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'titleName', label: '岗位', width: '100'},
        { istrue: true, prop: 'company', label: '分区', width: '100'},
        { istrue: true, prop: 'purDept', label: '架构', width: '100'},
        { istrue: true, prop: 'pddOrderTotal', label: '拼多多订单量', width: '100', sortable: 'custom'},
        { istrue: true, prop: 'pddTotal', label: '拼多多扣款总额', width: '100', sortable: 'custom', formatter: (row) => parseFloat(row.pddTotal.toFixed(4)) },
        { istrue: true, prop: 'txOrderTotal', label: '淘系订单量', width: '100', sortable: 'custom'},
        { istrue: true, prop: 'txTotal', label: '淘系扣款总额', width: '100', sortable: 'custom', formatter: (row) => parseFloat(row.txTotal.toFixed(4)) },
        { istrue: true, prop: 'tgcOrderTotal', label: '淘工厂订单量', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'tgcTotal', label: '淘工厂扣款总额', width: '100', sortable: 'custom', formatter: (row) => parseFloat(row.tgcTotal.toFixed(4)) },
        { istrue: true, prop: 'douOrderTotal', label: '抖音订单量', width: '100', sortable: 'custom'},
        { istrue: true, prop: 'douTotal', label: '抖音扣款总额', width: '105', sortable: 'custom', formatter: (row) => parseFloat(row.douTotal.toFixed(4)) },
        { istrue: true, prop: 'otherOrderTotal', label: '其他订单量', width: '105', sortable: 'custom'},
        { istrue: true, prop: 'otherTotal', label: '其他扣款总额', width: '105', sortable: 'custom', formatter: (row) => parseFloat(row.otherTotal.toFixed(4)) },
        { istrue: true, prop: 'orderTotal', label: '总订单量', width: '105', sortable: 'custom' },
        { istrue: true, prop: 'total', label: '合计扣款总额', width: '105', sortable: 'custom', formatter: (row) => parseFloat(row.total.toFixed(4)) },
    ]
    const tableHandlesDc = [
        { label: '导出', handle: (that) => that.onExportDetail() },
    ];
    export default {
        name: 'OrderIllegaldetailTotal',
        components: { cesTable, container, MyConfirmButton, MySearch, MySearchWindow },
        props: {
            filter: {},
            platforms: {
                type: Array,
                default: () => {
                    return null;
                }
            }
        },
        data() {
            return {
                dialogHisVisible: false,
                orderNo: '',
                that: this,
                list: [],
                platformList: [],
                brandlist: [],
                illegalTypeList: [],
                summaryarry: {},
                pager: { OrderBy: "total", IsAsc: false },
                filterImport: {
                    platform: 1,
                    occurrenceTime: formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD")
                },
                pickerOptions: {
                    disabledDate(time) {
                        return time.getTime() > Date.now();
                    }
                },
                tableCols: tableCols,
                total: 0,
                sels: [],
                listLoading: false,
                pageLoading: false,
                dialogVisible: false,
                uploadLoading: false,
                uploadLoading: false,
                tableHandlesDc: tableHandlesDc,
            };
        },
        async mounted() {
            await this.setPlatform()
            await this.onSearch()
        },
        methods: {
            //设置平台,扣款因下拉
            async setPlatform() {
                var pfrule = await rulePlatform();
                this.platformList = pfrule.options;
                var ilrule = await ruleIllegalType();
                this.illegalTypeList = ilrule.options;
            },
            //开始导入
            //查询第一页
            async onSearch() {
                if (!this.filter.timerange) { this.$message({ message: "请选择日期", type: "warning", }); return; }
                //this.$refs.pager.setPage(1)
                await this.getlist();
            },
            //获取查询条件
            getCondition() {
                this.filter.startPayDate = null
                this.filter.endPayDate = null
                this.filter.startSendDate = null
                this.filter.endSendDate = null
                if (this.filter.timerange && this.filter.timerange.length > 1) {
                    this.filter.startDate = this.filter.timerange[0];
                    this.filter.endDate = this.filter.timerange[1];
                }
                else {
                    this.$message({ message: "请先选择日期", type: "warning" });
                    return false;
                }
                if (this.filter.timerange2 && this.filter.timerange2.length > 1) {
                    this.filter.startPayDate = this.filter.timerange2[0];
                    this.filter.endPayDate = this.filter.timerange2[1];
                }
                //1
                if (this.filter.timerange3 && this.filter.timerange3.length > 1) {
                    this.filter.startSendDate = this.filter.timerange3[0];
                    this.filter.endSendDate = this.filter.timerange3[1];
                }

                //this.filter.platform=2

                //var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...page,
                    ... this.filter
                }

                return params;
            },
            //分页查询
            async getlist() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true
                const res = await getOrderWithholdListTotal(params)
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summary;
                //console.log(this.summaryarry, 'sadsd')
                data.forEach(d => {
                    d._loading = false
                    d.payTime = d.payTime == "1900-01-01 00:00:00" ? "" : d.payTime;
                })
                this.list = data

            },
            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selectchange: function (rows, row) {
                this.selids = []; console.log(rows)
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
            //导出
            async onExportDetail() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                var res = await exportOrderWithholdListTotal(params);
                loadingInstance.close();
                if (!res?.data) return
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '订单违规扣款详情统计_' + new Date().toLocaleString() + '.xlsx')
                aLink.click();
            },
        },
    };
</script>

<style lang="scss" scoped></style>
