<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <!-- <el-date-picker v-model="ListInfo.calculateMonth" unlink-panels range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期"  type="month" style="width: 250px;margin-right: 5px;" :clearable="false"
            :value-format="'yyyy-MM'" >
          </el-date-picker> -->
                <!-- <el-date-picker v-model="ListInfo.ledgerDate" unlink-panels placeholder="年/月"
           type="month" style="width: 250px;margin-right: 5px;" :clearable="false"
            :value-format="'yyyy-MM'" >
          </el-date-picker> -->
                <el-date-picker v-model="ListInfo.year" unlink-panels range-separator="至" placeholder="年份"
                  end-placeholder="结束日期" type="year" style="width: 230px;margin-right: 5px;" :clearable="false"
                  :value-format="'yyyy'">
                </el-date-picker>

                <el-select v-model="ListInfo.region" placeholder="区域" class="publicCss" clearable>
                    <el-option v-for="item in districtList1" :key="item" :label="item" :value="item" />
                </el-select>
                <el-select v-model="ListInfo.costType" placeholder="类型" class="publicCss" clearable>
                    <el-option v-for="item in districtList2" :key="item" :label="item" :value="item" />
                </el-select>
                <el-select style="width: 200px;" :collapse-tags="true" multiple v-model="ListInfo.regionalDivisionArr" placeholder="分区"
                    class="publicCss" clearable>
                    <el-option v-for="item in districtList3" :key="item" :label="item" :value="item" />
                </el-select>

                <!-- <el-input v-model.trim="ListInfo.orderNo" placeholder="退件快递单号" maxlength="50" clearable class="publicCss" />
          <el-input v-model.trim="ListInfo.orderNoInner" placeholder="原订单号" maxlength="50" clearable class="publicCss" /> -->
                <el-button type="primary" @click="getList('search')">查询</el-button>
                <el-button type="primary" @click="startImport">导入</el-button>
                <el-button type="primary" @click="downExcel">模板下载</el-button>
                <el-button type="primary" @click="exportExcel('search')">导出</el-button>
                <!-- <el-button type="primary" @click="saveBane('search')">存档</el-button>
          <div style="color: red; margin-left: 5px;">
            存档时间：{{timeCundang??'-'}}
          </div> -->

            </div>
        </template>
        <!-- :footer-data="footerData"  -->
        <vxe-table border show-footer width="100%" height="100%" ref="newtable" :row-config="{ height: 40 }" show-overflow
            :loading="loading" :footer-method="footerMethod" :column-config="{ resizable: true }" :merge-footer-items="mergeFooterItems"
            :span-method="mergeRowMethod" :row-class-name="rowClassName" :data="tableData" :row-style="rowStyleMethod">
            <!-- <vxe-column field="ledgerDate" width="150" title="日期"></vxe-column> -->
            <vxe-column field="region" width="135" title="区域"></vxe-column>
            <vxe-column field="costType" width="80" title="类型"></vxe-column>
            <vxe-column field="regionalDivision" width="80" title="分区"></vxe-column>
            <vxe-column field="expenseType" width="150" title="费用类型"></vxe-column>
            <vxe-column field="year" width="70" title="年份"></vxe-column>

            <vxe-colgroup title="1月" align="center">
                <vxe-column field="janQuantity" width="70" title="数量"></vxe-column>
                <vxe-column field="janMoney" width="70" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="2月" align="center">
                <vxe-column field="febQuantity" width="70" title="数量"></vxe-column>
                <vxe-column field="febMoney" width="70" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="3月" align="center">
                <vxe-column field="marQuantity" width="70" title="数量"></vxe-column>
                <vxe-column field="marMoney" width="70" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="4月" align="center">
                <vxe-column field="aprQuantity" width="70" title="数量"></vxe-column>
                <vxe-column field="aprMoney" width="70" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="5月" align="center">
                <vxe-column field="mayQuantity" width="70" title="数量"></vxe-column>
                <vxe-column field="mayMoney" width="70" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="6月" align="center">
                <vxe-column field="junQuantity" width="70" title="数量"></vxe-column>
                <vxe-column field="junMoney" width="70" title="金额"></vxe-column>
            </vxe-colgroup>

            <vxe-colgroup title="7月" align="center">
                <vxe-column field="julQuantity" width="70" title="数量"></vxe-column>
                <vxe-column field="julMoney" width="70" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="8月" align="center">
                <vxe-column field="augQuantity" width="70" title="数量"></vxe-column>
                <vxe-column field="augMoney" width="70" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="9月" align="center">
                <vxe-column field="septQuantity" width="70" title="数量"></vxe-column>
                <vxe-column field="septMoney" width="70" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="10月" align="center">
                <vxe-column field="octQuantity" width="70" title="数量"></vxe-column>
                <vxe-column field="octMoney" width="70" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="11月" align="center">
                <vxe-column field="novQuantity" width="70" title="数量"></vxe-column>
                <vxe-column field="novMoney" width="70" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="12月" align="center">
                <vxe-column field="decQuantity" width="70" title="数量"></vxe-column>
                <vxe-column field="decMoney" width="70" title="金额"></vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="年度小计" align="center">
                <vxe-column field="annualSubtotalQuantity" width="70" title="数量"></vxe-column>
                <vxe-column field="annualSubtotalMoney" width="70" title="金额"></vxe-column>
            </vxe-colgroup>
        </vxe-table>
        <!-- <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template> -->
        <el-drawer title="编辑" :visible.sync="dialogVisibleEdit" size="25%">
            <departmentEdit ref="departmentEdit" v-if="dialogVisibleEdit" :editInfo="editInfo" @search="closeGetlist"
                @cancellationMethod="dialogVisibleEdit = false" />
        </el-drawer>
        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
            <span>
                <span style="color: red; font-size: 12px;">导入文件每个区域必须有固定预算列</span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                    :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { downloadLink } from "@/utils/tools.js";
import { procurementBudgetTablePage, dimissionManageArchive, procurementBudgetTableImport, procurementBudgetTableRemove } from '@/api/people/peoplessc.js';
import departmentEdit from "./departmentEdit.vue";
import checkPermission from '@/utils/permission'
const tableCols = [
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '审批状态', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNoInner', label: '商品编码', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'importTime', label: '商品名称', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'planSendTime', label: '原价', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'timePay', label: '现价', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight', label: '进货数量', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderStatus', label: '涨价原因', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'seriesName', label: '添加日期', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCount', label: '涨价日期', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '添加人', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '岗位', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '分公司', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人', },
    // { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人组长', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, departmentEdit
    },
    data() {
        const tableData = [
            //   { id: 10001, calculateMonth: '12月', regionName: '义务', name: 'Test1', nickname: 'T1', role: 'Develop', sex: 'Man', age: 28, address: 'test abc' },
            //   { id: 10002, calculateMonth: '12月', regionName: '义务', name: 'Test2', nickname: 'T2', role: 'Test', sex: 'Women', age: 22, address: 'Guangzhou' },
            //   { id: 10003, calculateMonth: '12月', regionName: '南昌', name: 'Test3', nickname: 'T3', role: 'PM', sex: 'Man', age: 32, address: 'Shanghai' },
            //   { id: 10004, calculateMonth: '12月', regionName: '南昌', name: 'Test4', nickname: 'T4', role: 'Designer', sex: 'Women', age: 23, address: 'test abc' },
            //   { id: 10005, calculateMonth: '12月', regionName: '北京', name: 'Test5', nickname: 'T5', role: 'Develop', sex: 'Women', age: 30, address: 'Shanghai' },
            //   { id: 10006, calculateMonth: '12月', regionName: '北京', name: 'Test6', nickname: 'T6', role: 'Designer', sex: 'Women', age: 21, address: 'test abc' },
            //   { id: 10007, calculateMonth: '12月', regionName: '深圳', name: 'Test7', nickname: 'T7', role: 'Test', sex: 'Man', age: 29, address: 'test abc' },
            //   { id: 10008, calculateMonth: '12月', regionName: '深圳', name: 'Test8', nickname: 'T8', role: 'Develop', sex: 'Man', age: 35, address: 'test abc' }
        ]
        const footerData = [
            //   { calculateMonth: '12月', regionName: '办公室合计', name: '办公室合计', role: '33', rate: '56' },
            //   { calculateMonth: '12月', regionName: '仓储合计', name: '仓储合计', role: 'bb', rate: '56' },
            //   { calculateMonth: '12月', regionName: '全区域合计', name: '全区域合计', role: 'bb', rate: '1235' }
        ]
        const mergeFooterItems = [
            //   { row: 0, col: 0, rowspan: 3, colspan: 1 },
            //   { row: 0, col: 1, rowspan: 0, colspan: 2 },
            //   { row: 1, col: 1, rowspan: 1, colspan: 2 },
            //   { row: 2, col: 1, rowspan: 2, colspan: 2 }
        ]
        return {
            downloadLink,
            dialogVisibleEdit: false,
            editInfo: {},
            fileList: [],
            dialogVisible: false,
            districtList: [],
            timeCundang: '',
            tableData,
            footerData,
            mergeFooterItems,
            somerow: 'costType,region,regionalDivision',
            that: this,
            ListInfo: {
                calculateMonthArr: [
                dayjs().subtract(1, 'month').format('YYYY-MM'),
                dayjs().subtract(1, 'month').format('YYYY-MM')],
                year: dayjs().subtract(0, 'year').format('YYYY'),
                //   currentPage: 1,
                //   pageSize: 50,
                //   orderBy: null,
                //   isAsc: false,
                //   startTime: null,//开始时间
                // ledgerDate: dayjs().subtract(0, 'month').format('YYYY-MM'),//结束时间
            },
            timeRanges: [],
            tableCols,
            summaryarry: {},
            total: 0,
            loading: false,
            pickerOptions,
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        rowStyleMethod({ row, column, rowIndex, columnIndex }) {
          if (row.expenseType == '固定预算') {
            return { color: '#FFFFFF' }
          }
        },
        footerMethod ({ columns, data }) {
            const sums = [];
            if (!this.footerData)
                return sums
            let newfield = columns.map(item => item.field)
            let newfooterdata = [];
            this.footerData.forEach((item, index) => {
                let newarr2 = [];
                newfield.forEach((item2, index2) => {
                    newarr2.push(item[item2])
                })
                newfooterdata.push(newarr2)
            })

            return newfooterdata;
        },
        rowClassName(event) {
            if (event.row.expenseType == '固定预算') {
                return 'row-green'
            }
            if (event.row.expenseType == '当月实际产生费用') {
                return 'row-yellow'
            }
            if (event.row.expenseType == '差异值') {
                return 'row-yellow'
            }
            if (event.row.expenseType == '差异率') {
                return 'row-yellow'
            }
            return null
        },
        //上传文件
        onUploadRemove(file, fileList) {
            this.fileList = []
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            this.dialogVisible = false;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("file", item.file);
            form.append("isArchive", checkPermission("ArchiveStatusEditing"));
            form.append("calculateMonth", this.ListInfo.calculateMonth);
            var res = await procurementBudgetTableImport(form);
            if (res?.success) {
                this.$message({ message: res.msg, type: "success" });
            }
            this.uploadLoading = false
            this.dialogVisible = false;
            await this.getList()
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        //导入弹窗
        startImport() {
            this.fileList = []
            this.dialogVisible = true;
        },
        downExcel() {
            //下载excel
            downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250508/1920370654059085824.xlsx', '采购预算表导入模板.xlsx');
        },
        async saveBane() {
            this.$confirm('是否存档？存档后不可修改！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { data, success } = await dimissionManageArchive(this.ListInfo)
                if (!success) {
                    return;
                }
                this.getList();
                this.$message.success('保存存档成功！')

            }).catch(() => {
                // this.$message.error('取消')
            });
        },
        exportExcel() {
            // this.listLoading = true;
            // var dataall = [...this.tableData, ...this.footerData];
            // console.log(dataall, 'dataall');
            // return;
            // // if (res?.data?.type == 'application/json') { this.listLoading = false; return;}
            // const aLink = document.createElement("a");
            // var blob = new Blob([dataall], { type: "application/vnd.ms-excel" })
            // aLink.href = URL.createObjectURL(blob)
            // aLink.setAttribute('download', '采购预算表导出.xlsx')
            // aLink.click()
            // this.listLoading = false;
            // beforeExportMethod: this.beforeExportMethod
            if(this.$refs.newtable == null){
                return;
            }
            this.$refs.newtable.exportData({filename:"采购预算表",    sheetName: 'Sheet1',type: 'xlsx' })
            // this.$refs.newtable.exportData({ filename: '采购预算表', type: 'xlsx', original: true })

            // this.$refs.newtable.exportData({
            //     filename: '采购预算表', remote: true,
            //     type: 'csv',
            //     exportMethod: this.exportMethod
            // })
        },
        exportMethod(file) {
            console.log(file, 'file======');
            // return;
            // file.optionstype.type = "xlsx"

            this.listLoading = true;
            // var dataall = file;

            // if (res?.data?.type == 'application/json') { this.listLoading = false; return;}
            const aLink = document.createElement("a");
            var blob = new Blob(file.options, { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '采购预算表导出.xlsx')
            aLink.click()
            this.listLoading = false;
        },
        closeGetlist() {
            this.dialogVisibleEdit = false;
            this.getList()
        },
        handleEdit(index, row) {
            this.editInfo = row;
            this.dialogVisibleEdit = true;
        },
        async handleRemove(index, row) {
            this.$confirm('是否删除！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.editInfo = row;
                // this.editInfo.calculateMonthStart = this.ListInfo.calculateMonthArr[0]
                // this.editInfo.calculateMonthEnd = this.ListInfo.calculateMonthArr[1]
                this.loading = true
                const { data, success } = await procurementBudgetTableRemove({ ids: row.id })
                this.loading = false
                if (success) {
                    this.$message.success('删除成功')
                    this.getList();
                } else {
                    this.$message.error('删除失败')
                }
            }).catch(() => {

            });
        },
        // 通用行合并函数（将相同多列数据合并为一行）
        mergeRowMethod({ row, _rowIndex, column, visibleData }) {
          const fields = this.somerow.split(',')
          const cellValue = row[column.property]
          if (cellValue && fields.includes(column.property)) {
            const prevRow = visibleData[_rowIndex - 1]
            let countRowspan = 1
            // 【核心】如果当前字段是 regionalDivision，还需要同时判断 costType 相等
            const isSameGroup = (a, b) => {
              if (!a || !b) return false
              if (column.property === 'regionalDivision') {
                return a.regionalDivision === b.regionalDivision && a.costType === b.costType
              }
              return a[column.property] === b[column.property]
            }
            // 判断上一行是否属于同一组，如果是则 return 0 合并到上一行
            if (isSameGroup(row, prevRow)) {
              return { rowspan: 0, colspan: 0 }
            }
            // 向下查找有多少行可合并
            for (let i = _rowIndex + 1; i < visibleData.length; i++) {
              const nextRow = visibleData[i]
              if (isSameGroup(row, nextRow)) {
                countRowspan++
              } else {
                break
              }
            }
            if (countRowspan > 1) {
              return { rowspan: countRowspan, colspan: 1 }
            }
          }
        },
        async changeTime(e) {
            this.ListInfo.startTime = e ? e[0] : null
            this.ListInfo.endTime = e ? e[1] : null
        },
        async getList(type) {
            // if (type == 'search') {
            //   this.ListInfo.currentPage = 1
            //   this.$refs.pager.setPage(1)
            // }
            if (this.ListInfo.calculateMonthArr && this.ListInfo.calculateMonthArr.length > 0) {
                this.ListInfo.calculateMonthStart = this.ListInfo.calculateMonthArr[0]
                this.ListInfo.calculateMonthEnd = this.ListInfo.calculateMonthArr[1]
            }
            this.ListInfo.regionalDivision = this.ListInfo.regionalDivisionArr.join(',');
            this.loading = true
            const { data, success } = await procurementBudgetTablePage(this.ListInfo)
            if (success) {
                //   data.list.map((row)=>{
                //     row.twoYearRate =  row.twoYearRate === 100 ? "100%" : row.twoYearRate ? Number(row.twoYearRate).toFixed(2) + "%" : ''
                //     row.notAdaptedWorkRate =  row.notAdaptedWorkRate === 100 ? "100%" : row.notAdaptedWorkRate ? Number(row.notAdaptedWorkRate).toFixed(2) + "%" : ''
                //     row.hasWorkRate =  row.hasWorkRate === 100 ? "100%" : row.hasWorkRate ? Number(row.hasWorkRate).toFixed(2) + "%" : ''

                //     row.familyReasonsRate =  row.familyReasonsRate === 100 ? "100%" : row.familyReasonsRate ? Number(row.familyReasonsRate).toFixed(2) + "%" : ''
                //     row.bodyReasonsRate =  row.bodyReasonsRate === 100 ? "100%" : row.bodyReasonsRate ? Number(row.bodyReasonsRate).toFixed(2) + "%" : ''
                //     row.eliminateRate =  row.eliminateRate === 100 ? "100%" : row.eliminateRate ? Number(row.eliminateRate).toFixed(2) + "%" : ''
                //   })
                let aaa = ['year','expenseType','regionalDivision','costType','region'];
                data.list.map((item) => {
                    if (item.expenseType == '差异率') {
                        Object.keys(item).map((index) => {
                            aaa.indexOf(index) > -1 || item[index]==0 || !item[index] ? '' : item[index] = item[index] + '%'
                        })
                    }
                })
                this.tableData = data.list
                //   this.total = data.total
                //   this.summaryarry = data.summary
                // data.summary.regionName = '合计';
                // data.summary.calculateMonth = data.list.length > 0 ? data.list[0].calculateMonth : '';
                // this.timeCundang = data.list.length>0?data.list[0].archiveTime:''
                // if (data.summary) {
                //     this.timeCundang = data.summary.archiveTime
                // }

                // let zhanbi = ['eliminateRate', 'bodyReasonsRate', 'familyReasonsRate', 'hasWorkRate', 'notAdaptedWorkRate'];

                // zhanbi.map((item) => {
                //     data.summary[item] = data.summary[item] ? data.summary[item] + '%' : ''
                // })

                this.footerData = data.summary

                const fieldsToFormat = [
                    "septQuantity",
                    "septMoney",
                    "octQuantity",
                    "octMoney",
                    "novQuantity",
                    "novMoney",

                    "mayQuantity",
                    "mayMoney",
                    "marQuantity",
                    "marMoney",
                    "junQuantity",
                    "junMoney",

                    "julQuantity",
                    "julMoney",
                    "janQuantity",
                    "janMoney",
                    "febQuantity",
                    "febMoney",

                    "decQuantity",
                    "decMoney",
                    "augQuantity",
                    "augMoney",

                    "aprMoney",
                    "aprQuantity",
                    "annualSubtotalMoney",
                    "annualSubtotalQuantity",
                ];
                this.footerData.forEach((item) => {
                    fieldsToFormat.forEach((field) => {
                        if (item[field] !== null && item['region'] == '差异率') {
                            item[field] = item[field] + '%'
                            return;
                        }
                        if (item[field] !== null && item[field] !== undefined) {
                            item[field] = this.formatNumberWithThousandSeparator(item[field]);
                        }
                    });
                    // zhanbi.indexOf()

                });
                // const fieldsToFormat = [
                //     "monthCount",
                //     "day7Count",
                //     "day15Count",
                //     "day30Count",
                //     "day90Count",
                //     "yearCount",
                //     "twoYearCount",
                //     "resignCount",
                //     "quitCount",
                //     "selfDeparture",
                //     "probationQuitCount",
                //     "regularQuitCount",
                //     "totalResignationCost",
                //     "perResignationCost",
                //   ];
                //   this.footerData.forEach((item) => {
                //     fieldsToFormat.forEach((field) => {
                //       if (item[field] !== null && item[field] !== undefined) {
                //         item[field] = this.formatNumberWithThousandSeparator(item[field]);
                //       }
                //     });
                //     // zhanbi.indexOf()

                //   });
                console.log("111111", this.footerData)
                //

                //取列表中的区域
                this.nameget('districtList3', 'regionalDivision')
                this.nameget('districtList2', 'costType')
                this.nameget('districtList1', 'region')



                this.loading = false
            } else {
                this.loading = false
                this.$message.error('获取列表失败')
            }
        },
        nameget(namearr, name) {
            if(!this[namearr]){
                this[namearr] = []
            }
            const newDistricts = this.tableData.map(item => item[name]).filter(district => district !== undefined && district !== null && district.indexOf('小计') == -1 && district.indexOf('占比') == -1)
            let aaa = JSON.parse(JSON.stringify(this[namearr]))
            this[namearr] = Array.from(new Set([...aaa, ...newDistricts]));
        },
        formatNumberWithThousandSeparator(value) {
            if (value === null || value === undefined) return value;
            return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 150px;
        margin-right: 5px;
    }
}

:deep(.vxe-header--column) {
    background: #00937e;
    color: white;
    font-weight: 600;
}

:deep(.vxe-footer--row) {
    background: #00937e;
    color: white;
    font-weight: 600;
}

:deep(.row-green) {
    background-color: rgb(0, 147, 126, 0.9);
    // color: #fff;
}
:deep(.row-yellow) {
    background-color: rgb(247, 230, 193);
    // color: #fff;
}
</style>
