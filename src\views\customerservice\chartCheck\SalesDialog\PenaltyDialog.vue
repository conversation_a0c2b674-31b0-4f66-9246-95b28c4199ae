<template>
  <el-dialog title="复审判罚" :visible.sync="isShow" width="50%" :before-close="closeDialog" v-if="isShow" v-dialogDrag>
  <div style="">
      <el-descriptions size="small" class="margin-top" title="" :column="3">
        <el-descriptions-item label="平台/店铺"> {{platformName}} / {{dataJson.shopName}}</el-descriptions-item>
        <el-descriptions-item label="违规单号">{{dataJson.violationOrderNo}}</el-descriptions-item>
        <el-descriptions-item label="违规时间">{{violationTime }} <el-button style="margin-left:20px;" type="primary" @click="showOrhide">{{this.isShowOrHide?"收起聊天记录":"展开聊天记录"}}</el-button></el-descriptions-item>
      </el-descriptions>
      <el-descriptions size="small" class="margin-top" title="" :column="3">
        <el-descriptions-item label="聊天账号"> {{dataJson.chatAccount}}  </el-descriptions-item>
        <el-descriptions-item label="账号使用人/分组">{{dataJson.userName}} / {{dataJson.groupName}}</el-descriptions-item>
      </el-descriptions>
  </div>
<!-- 聊天记录 -->
  <div v-show="isShowOrHide"> 
      <PenaltyComponent   ref="chartRef"    :isShow="isShow" ></PenaltyComponent>
  </div>

  <el-form ref="formDataRef" :model="formData" style="margin-top: 20px"  label-width="120px" >
 <!-- 初审  -->
      <div style="border: 1px #eeeeee solid;padding:20px">
          <div style="display: flex">
              <el-form-item label="判罚初审:" prop="firstStatus" class="first custom-label">
                <div>{{dataJson.refuseInitialAuditType }}</div>
              </el-form-item>
               <el-form-item label="初审人:" prop="firstStatus" class="custom-label" style="margin-left:80px" >
                <div>{{dataJson.initialOperator}}</div>
              </el-form-item>
              <el-form-item label="说明：" prop="firstExplain" style="margin-left:80px" class="custom-label">
                <div>{{ dataJson.remark }}</div>
              </el-form-item>
          </div>
          <el-form-item label="初审凭证:" prop="firstStatus" class="first custom-label">
              <span v-for="(image, index) in imgSplitList" :key="index" >
                  <el-image  v-if="image" :src="image" :preview-src-list="[image]" style="padding-right:10px"></el-image>
                  <span v-else>无</span>
              </span>
          </el-form-item>
      </div>
<!-- 初审  END-->

     <div style="display: flex; margin-top: 20px">
          <el-form-item label="判罚复审" prop="auditType" :rules="{required: true,message: '请选择',trigger: ['blur', 'change'],}">
                <el-select   v-model="formData.auditType" placeholder="请选择" class="el-select-content" clearable>
                    <el-option v-for="item in auditTypeList" :key="item" :label="item" :value="item" />
                </el-select>
          </el-form-item>
          <el-form-item label="判罚支付凭证:" prop="firstStatus" class="first" v-if="isShow">
                  <uploadimgFile v-if="editPriceVisible" ref="uploadimgFile" :disabled="isView" :ispaste="!isView"
                            :noDel="isView" :accepttyes="accepttyes" :isImage="true" :uploadInfo="chatUrls" :keys="[1, 1]"
                            @callback="getImg" :imgmaxsize="9" :limit="9" :multiple="true">
                  </uploadimgFile>
          </el-form-item>
     </div>

  <el-form-item label="说明" prop="remark">
        <el-input   placeholder="请输入内容" show-word-limit :maxlength="100" v-model="formData.remark" type="textarea"   :autosize="{ minRows: 2, maxRows: 4}" clearable/>
      </el-form-item>

  </el-form>

    <template #footer>
                 <div class="dialog-footer" style="display:flex;justify-content: flex-end;">
                    <div style="position: relative;">
                        <el-button @click="btnChange('last')" :disabled="isLastButtonDisabled" type="primary">查看上一个</el-button>
                        <div style="position: absolute;right:-20px;top:-20px;cursor:pointer;" > 
                            <el-tooltip class="item" effect="dark" content="点击键盘的上箭头↑，可以快速查看上一个" placement="top"><i class="el-icon-question"></i></el-tooltip>
                        </div>
                    </div>
                    <div style="position: relative;margin-left:20px;">
                         <el-button @click="btnChange('next')" :disabled="isNextButtonDisabled" type="primary" >查看下一个</el-button>
                          <div  style="position: absolute;right:-20px;top:-20px; cursor:pointer;" >
                              <el-tooltip class="item" effect="dark" content="点击键盘的下箭头↓，可以快速查看下一个" placement="top"> <i class="el-icon-question"></i></el-tooltip>
                         </div>
                    </div>
                    <div style="position: relative;margin-left:20px;">
                          <el-button @click="submitForm" :disabled="isSubmitDisabled" :loading="isLoading" type="primary" v-throttle="3000">保存关闭</el-button>
                           <el-button @click="saveNextSubmitDot" :disabled="isNextSubmitDisabled" :loading="isLoading2" type="primary" v-throttle="3000">保存复审下一个</el-button>
                    </div>
         </div>
    </template>
  </el-dialog>
</template>
<script>
import { formatTime } from "@/utils";
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import PenaltyComponent from "@/views/customerservice/chartCheck/SalesDialog/penaltyComponent.vue"

import {uploadCredentialsAsync,getPlatformPunishById} from "@/api/customerservice/chartCheck";
const signalR = require('@microsoft/signalr')
import store from '@/store'

export default {
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
     isView: {
            type: Boolean,
            default: false
        },
  },
  components:{uploadimgFile,PenaltyComponent},
  computed: {
    platformName(){ //平台初始化
      let platformList = [
        { name: "拼多多", value: 2 },
        { name: "抖音", value: 6 },
        { name: "天猫", value: 1 },
        { name: "淘工厂", value: 8 },
        { name: "淘宝", value: 9 },
      ]
        console.log(this.dataJson);
      if (this.dataJson?.platform) {
        return platformList.filter(item => item.value == this.dataJson?.platform)[0].name
      } else {
        return ""
      }
    },
    violationTime(){  //日期转换
      return this.dataJson.violationTime?formatTime(this.dataJson.violationTime , "YYYY-MM-DD"):""
    },
     imgSplitList(){  //图片分割
      return  this.dataJson?.punishImgs?this.dataJson?.punishImgs.split(","):"";
    },
  },
  mounted(){
    if (this.pictures) {
            this.chatUrls = this.pictures.split(',').map((item, i) => {
                return {
                    url: item,
                    name: `聊天截图${i + 1}`
                }
            })
        }
     this.editPriceVisible = true
  },
    created(){
document.addEventListener('keydown', this.handleArrowUp);
  },
  watch: {
    isShow(newVal, oldVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.$refs.chartRef.dataJson = this.dataJson
        });
        this.buttonDisabled() //按钮是否禁用
        this.initWareSignalR();
      }
    },
  },
  data() {
    return {
      formData: {
        auditType: null,
        remark:null,
      },
        keyword:null,
        dataJson: {},
        chatUrls: [],
        editPriceVisible: false,//图片
        accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',//上传格式
        pictures:null,
        auditTypeList:["客服责任","平台误判"],//审核类型
        isShowOrHide:true,
        currentInfo:null,
        isLoading2:false,
        isLoading:false,
        tableData:[],
        isLastButtonDisabled:false,
      isNextButtonDisabled:false,
      isNextSubmitDisabled:false,
      isSubmitDisabled:false,
    };
  },
  methods: {
    async initWareSignalR() {
        const signalWareRconnection = new signalR.HubConnectionBuilder().withUrl(process.env.VUE_APP_BASE_API_CustomerService + '/ChatAuditNoticeHub', {
            accessTokenFactory: () => { return store.getters.token },
            skipNegotiation: true,
            transport: signalR.HttpTransportType.WebSockets,
        }).withAutomaticReconnect([3000, 5000, 10000, 15000, 30000]).build();
             // this.signalWareRconnection.on('ConnectSucceeded', data => { console.log('', data) })
         signalWareRconnection.on('ChatAuditNotice', data => { 
            setTimeout(() => {
                  const ref= JSON.parse(data);
                if(ref.PageType==2 && this.dataJson.id==ref.Id && (ref.Data?.SystemState==true || ref.Data?.RefuseInitialAuditType!='平台误判' ))
                {
                  this.isSubmitDisabled=true;
                  this.isNextSubmitDisabled=true;
                }
                this.reciveWareMessage(data); 
            }, 2000);
          })
        signalWareRconnection.start()
     },
     handleArrowUp(event) {
        if(!this.isShow){ 
        return
      }
      if (event.key === 'ArrowUp' && !this.isLastButtonDisabled) {
        // 处理向上键被按下的逻辑
        this.btnChange('last');
      }
      if (event.key === 'ArrowDown' && !this.isNextButtonDisabled) {
        // 处理向上键被按下的逻辑
        this.btnChange('next');
      }
    },
    getImg(data) {
        if (data) 
        {
            this.chatUrls = data
            this.pictures = data.map(item => item.url).join(',')
        }
    },
    //关闭当前页面
    closeDialog() {
        this.pictures = []; 
        this.chatUrls=[];
        this.$refs.formDataRef.resetFields();
        this.$emit("closeDialog");
    },
  onSubmitValidate: function () {
      let isValid = true;
      this.$refs.formDataRef.validate((valid) => {
        isValid = valid;
      });
      return isValid;
    },
    async submitForm() {  //保存关闭
          if (!this.onSubmitValidate()) {
              return;
            }
            if (this.pictures.length<1) {
                return this.$message.error('请上传图片！！！')
            }
           const params={
              id:this.dataJson.id,
              punishImgs:this.pictures,
              reviewType:2,
              ...this.formData,
           }
            const { success } = await uploadCredentialsAsync(params)
            if (success) {
                this.$message.success('操作成功');
                 this.pictures = []; 
                 this.chatUrls=[];
                 this.$refs.formDataRef.resetFields();
                this.$emit("upData");
                this.$emit("closeDialog");
            } else {
               this.pictures = []; 
                 this.chatUrls=[];
              this.$refs.formDataRef.resetFields();
                this.$message.error('操作失败')
            }
    },
    async saveNextSubmitDot() {  //保存审核下一个
          if (!this.onSubmitValidate()) {
              return;
            }
            if (this.pictures.length<1) {
                return this.$message.error('请上传图片！！！')
            }
           const params={
              id:this.dataJson.id,
              punishImgs:this.pictures,
              reviewType:2,
              ...this.formData,
           }
            const { success } = await uploadCredentialsAsync(params)
            if (success) {
                this.$message.success('操作成功');
                 this.pictures = []; 
                 this.chatUrls=[];
                 this.$refs.uploadimgFile.setData([]);
                 this.$refs.formDataRef.resetFields();
                this.btnChange('next','submit');//下一条数据
                 this.$emit("upData"); 
            } else {
               this.pictures = []; 
               this.chatUrls=[];
               this.$refs.uploadimgFile.setData([]);
               this.$refs.formDataRef.resetFields();
               this.$message.error('操作失败')
            }
    },
   async btnChange(last,btn){//查看上一个、查看下一个
      const index = this.tableData.findIndex(index=>index.id==this.dataJson.id);
      
      if(btn=='submit'){//已审核过后的数据进行移除
          this.tableData = this.tableData.filter((item) => {return item.id != this.dataJson.id;}); 
      }    

      if(last=='last' && this.tableData.length>0){

          var number=index-1;
          //这种情况只能是最后一条数据审核完了，并且table里面已经清除当前审核id，
          //所以找不到当前审核的id下标，但table中还有数据，我需要上翻页
          if(this.tableData.length>0 && index==-1)
          {
              number=this.tableData.length-1
          }

            const info=  this.tableData[number]
            this.dataJson=JSON.parse(JSON.stringify(info))
            this.keyWord=info?.id ?? null;
            this.platform=info.platform;
            this.$refs.chartRef.dataJson = info;
            this.$refs.chartRef.getChartList()
      }
      else if(last=='next' && this.tableData.length>0)
      {
            const number=btn =='submit' ? index : index+1;
            const info=this.tableData[number]
            this.dataJson= JSON.parse(JSON.stringify(info))
            this.keyWord=info?.id ?? null;
            this.platform=info.platform;
            this.$refs.chartRef.dataJson = info;
            this.$refs.chartRef.getChartList()
      }
            this.chatUrls = [];
            this.pictures = [];
            this.$refs.uploadimgFile.setData([]);
            this.$refs.formDataRef.resetFields();
            this.buttonDisabled(btn,index)//按钮是否禁用
    },
   async  buttonDisabled(btn,indexs){ //按钮是否禁用
  
        this.isLastButtonDisabled=false;
        this.isNextButtonDisabled=false;
        this.isSubmitDisabled=false;
        this.isNextSubmitDisabled=false;
        const index = btn =='submit' ? indexs : this.tableData.findIndex(index=>index.id==this.dataJson.id);
      if (this.tableData.length === 1) {
          this.isLastButtonDisabled = true;
          this.isNextButtonDisabled = true;
      }
        if(index==0 || this.tableData.length==0){
          this.isLastButtonDisabled=true;
        }
        if(index==this.tableData.length-1 || (btn=='submit' && index==this.tableData.length)){
          this.isNextButtonDisabled=true;
        }
             this.currentInfo = await getPlatformPunishById({id:this.dataJson.id});
        if(this.currentInfo.data.systemState==true ||
          (this.currentInfo.data.systemState==true && this.currentInfo.data.refuseInitialAuditType != "客服责任")
        ){
           this.isSubmitDisabled=true;
           this.isNextSubmitDisabled=true;
        }
    },
    showOrhide(){
      if(this.isShowOrHide)
       this.isShowOrHide=false;
      else this.isShowOrHide=true
    },
  },
};
</script>


<style lang="scss" scoped>
//顶部可点击div样式
::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell{
  padding: 15px;
}
::v-deep .el-descriptions__body .el-descriptions__table{
  background-color: rgb(242, 244, 245);
}

::v-deep .el-descriptions__body .el-descriptions-item__container {
  font-size: 14px;
}

.first ::v-deep .el-form-item__label:before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}
</style>
