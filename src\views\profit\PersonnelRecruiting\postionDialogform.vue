<template >
    <el-form :model="ruleForm" :disabled="isCheck" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item label="招聘部门" prop="department">
            <el-col :span="24">
                <el-select :disabled="ruleForm.planId != null" ref="dddept" v-model="ruleForm.department"
                    style="width: 100%" size="mini" placeholder="招聘部门">
                    <el-option hidden value="一级菜单" :label="chooseName"></el-option>
                    <el-tree style="width: 200px;" :data="deptList" :props="defaultProps" :expand-on-click-node="false"
                        :check-on-click-node="true" @node-click="handleNodeClick">
                    </el-tree>
                </el-select>
            </el-col>
        </el-form-item>
        <el-form-item label="招聘小组" prop="departmentGroup">
            <el-col :span="24">
                <el-select ref="groupdept" :disabled="ruleForm.planId != null" v-model="groupName" style="width: 100%"
                    size="mini" placeholder="招聘小组">
                    <el-option hidden value="一级菜单" :label="groupName"></el-option>
                    <el-tree style="width: 200px;" :data="groupList" :props="defaultProps" :expand-on-click-node="false"
                        :check-on-click-node="true" @node-click="handleGropuNodeClick">
                    </el-tree>
                </el-select>
            </el-col>
        </el-form-item>
        <el-form-item label="招聘岗位" prop="positionName">
            <!-- <el-input :disabled="ruleForm.planId != null" v-model="ruleForm.positionName" placeholder="请输入招聘岗位">
            </el-input> -->
            <el-select ref="selectPosit" style="width: 100%;" v-model="ruleForm.positionName" placeholder="请选择岗位"
                :disabled="ruleForm.planId != null">
                <el-option hidden value="一级菜单" :label="ruleForm.positionName"></el-option>
                <el-tree style="width: 200px;" :data="postList" :props="postProps" :expand-on-click-node="false"
                    :check-on-click-node="true" @node-click="handleNodePostClick">
                </el-tree>
            </el-select>
        </el-form-item>
        <el-form-item label="申请人" prop="applicantId">
            <el-input v-if="isCheck" disabled v-model="ruleForm.applicant" placeholder="请输入申请人"></el-input>
            <el-select v-else ref="applicantSelect" style="width: 100%;" :default-first-option="true"
                v-model="ruleForm.applicantId" placeholder="请选择申请人" filterable @change="getlable">
                <el-option v-for="item in applicantList" :key="item.ddUserId + item.userName" :label="item.userName"
                    :value="item.ddUserId">
                    <span style="float: left">{{ item.userName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.title }}</span>
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="招聘人数" prop="recruitmentCount">
            <el-col :span="24">
                <el-input v-model="ruleForm.recruitmentCount" style="width: 100%;" placeholder="请填写招聘人数"
                onkeyup="this.value = this.value.replace(/[^\d.]/g,'');" maxlength="3"></el-input>
                <!-- <el-input-number placeholder="请填写招聘人数"  :controls="false" :min="1" style="width: 100%"
                        v-model="ruleForm.recruitmentCount">
                    </el-input-number> -->
            </el-col>
        </el-form-item>
        <el-form-item label="招聘时间" prop="dateRange">
            <el-col :span="24">
                <el-date-picker style="width: 100%;" value-format="yyyy-MM-dd" v-model="ruleForm.dateRange" type="daterange"
                    range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
                </el-date-picker>
            </el-col>
        </el-form-item>
        <el-form-item label="招聘专员" prop="recruiters">
            <el-col :span="24">
                <el-select ref="recruiterJsonSelect" style="width: 100%;" v-model="ruleForm.recruiters"
                    :label-in-value="true" multiple placeholder="请选择招聘专员" filterable>
                    <el-option v-for="item in recruiterList" :key="item.ddUserId" :label="item.userName"
                        :value="item.ddUserId">
                    </el-option>
                </el-select>
            </el-col>
        </el-form-item>
        <el-form-item label="岗位要求" prop="jobRequirements">
            <el-col :span="24">
                <el-input type="textarea" v-model="ruleForm.jobRequirements" placeholder="请填写岗位要求" maxlength="300"
                    show-word-limit></el-input>
            </el-col>
        </el-form-item>
        <template v-if="isCheck">
            <el-divider></el-divider>
            <el-form-item label="完成时间" prop="closeDate">
                <el-col :span="24">
                    <el-date-picker style="width: 100%;" v-model="ruleForm.closeDate" type="datetime">
                    </el-date-picker>
                </el-col>
            </el-form-item>
            <el-form-item label="完成原因" prop="planStatus">
                <el-col :span="24">
                    <el-select v-model="ruleForm.planStatus" placeholder="完成原因" style="width: 100%" size="mini" clearable>
                        <el-option label="逾期关闭" :value="21"></el-option>
                        <el-option label="手动关闭" :value="22"></el-option>
                        <el-option label="满员关闭" :value="23"></el-option>
                    </el-select>
                </el-col>
            </el-form-item>
            <el-form-item label="完成备注" prop="closeReason">
                <el-col :span="24">
                    <el-input type="textarea" v-model="ruleForm.closeReason" placeholder="请填写完成招聘备注" maxlength="80"
                        show-word-limit></el-input>
                </el-col>
            </el-form-item>
        </template>
        <el-form-item v-if="!isCheck">
            <div style="width: 100%; text-align: right;">
                <el-button type="primary" :loading="subLoad" @click="submitForm('ruleForm')">保存</el-button>
            </div>
        </el-form-item>
    </el-form>
</template>

<script>
import { getALLDDDeptTree, getDeptUsers, getDingRolesTree } from '@/api/profit/personnel'
import { createRecruitmentPlan, getRecruitmentPlan, saveRecruitmentPlan } from '@/api/profit/hr'
import { formatTime } from "@/utils/tools";
const valida = (rule, value, callback) => {
    if (!value) {
        return callback(new Error('请输入招聘人数'));
    }
    if (value < 1) {
        callback(new Error('招聘人数不少于1'));
    }
    else if (value > 100) {
        callback(new Error('招聘人数不大于100'));
    } else {
        callback();
    }
};
export default {
    props: {
        isCheck: { type: Boolean, default: false },
    },
    name: "postionDialogform",//
    data () {
        return {
            userList:[],
            subLoad: false,
            chooseName: '',
            groupName: '',
            defaultProps: {
                children: 'childDeptList',
                label: 'name'
            },
            deptList: [],
            postProps: {
                children: 'roles',
                label: 'name'
            },
            postList: [],
            groupList: [],
            applicantList: [],
            recruiterList: [],
            ruleForm: {
                ddDeptId: null,
                department: null,
                departmentGroup: null,
                ddDeptGroupId: null,
                positionName: null,
                applicant: null,
                applicantId: null,
                recruitmentCount: null,
                dateRange: [],
                recruitmentStartDate: null,
                recruitmentEndDate: null,
                recruiters: null,
                recruiterJson: [],//招聘专员们Json:[{"UserId":1,"UserName":"王六"}]
                jobRequirements: null,
            },
            rules: {
                department: [
                    { required: true, message: '请选择招聘部门', trigger: 'change' }
                ],
                positionName: [
                    { required: true, message: '请选择招聘岗位', trigger: 'change' }
                ],
                applicantId: [
                    { required: true, message: '请填写申请人', trigger: 'change' }
                ],
                recruitmentCount: [
                    {
                        required: true,
                        validator: valida,
                        trigger: 'blur'
                    }

                ],
                recruiters: [
                    { required: true, message: '请选择招聘专员', trigger: 'change' }
                ],
                dateRange: [
                    { required: true, message: '请选择日期', trigger: 'change' }
                ],
                jobRequirements: [
                    { required: true, message: '请填写岗位要求', trigger: 'blur' }
                ]
            }
        }
    },
    computed: {

    },
    created () {
    },
    beforeMount () {
        this.getDeptList();
        this.getRecruiters();
        this.getPositList();
        this.getDeptUsersList(); 
    },
    mounted () {
    },
    methods: {
        getlable (e) {
            this.ruleForm.applicant = this.applicantList.filter(item => item.ddUserId == e)[0].userName;
        },
        // 获取招聘岗位列表
        async getPositList (ddDeptId) {
            const params = {
                closeStatus: 0,//计划状态:-1删除、0进行中、1已完成
                ddDeptId: ddDeptId,//招聘部门
                currentPage: 1,
                pageSize: 50,
                positionName: null,//岗位名称
                recruiterIds: [],//招聘专员
                closeReason: null,//完成原因
            };
            const res = await getDingRolesTree();
            this.postList = res.data;
            this.postList.forEach(item => {
                item.disabled = true;
            })
        },
        handleNodePostClick (data) {
            if (!data.disabled) {
                this.ruleForm.positionName = data.name;
                this.$refs.selectPosit.blur();
            }
        },
        reset () {
            this.ruleForm = {
                ddDeptId: null,
                department: null,
                departmentGroup: null,
                ddDeptGroupId: null,
                positionName: null,
                applicant: null,
                applicantId: null,
                recruitmentCount: null,
                dateRange: [],
                recruitmentStartDate: null,
                recruitmentEndDate: null,
                recruiters: [],
                recruiterJson: [],//招聘专员们Json:[{"UserId":1,"UserName":"王六"}]
                jobRequirements: null,
            }
            this.groupName = null;
        },

        // 获取部门列表
        async getDeptList () {
            await getALLDDDeptTree().then(res => {
                if (res.success) {
                    this.deptList = res.data.childDeptList;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        // 节点点击事件
        handleNodeClick (data) {
            // 配置树形组件点击节点后，设置选择器的值，配置组件的数据
            this.chooseName = data.name
            this.ruleForm.ddDeptId = data.dept_id
            this.ruleForm.department = data.name
            if (data.childDeptList)
                this.groupList = data.childDeptList
            this.groupName = '';
            this.ruleForm.ddDeptGroupId = 0
            this.ruleForm.departmentGroup = ''
            // this.getDeptUsersList(this.ruleForm.ddDeptId);
            // 选择器执行完成后，使其失去焦点隐藏下拉框效果
            this.$refs.dddept.blur()
        },
        handleGropuNodeClick (data) {
            // 配置树形组件点击节点后，设置选择器的值，配置组件的数据
            this.groupName = data.name
            this.ruleForm.ddDeptGroupId = data.dept_id
            this.ruleForm.departmentGroup = data.name
            // this.getDeptUsersList(this.ruleForm.ddDeptGroupId );
            this.$refs.groupdept.blur()
        },
        // 获取部门成员
        getDeptUsersList (query) {
            let params = {
                deptId: null,
                includeAllChildDpt: 1,
                userName: query
            }
            getDeptUsers(params).then(res => {
                if (res.success) {
                    this.applicantList = res.data
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        }, 
        //  //获取招聘组
         async getRecruiters () {
            let params = {
                deptName: '招聘组,人事组,SSC&员工关系组,培训组',
                includeAllChildDpt: 1,
            }
             const { data } = await getDeptUsers(params)
             console.log(data,'获取招聘组');
            this.recruiterList = data;
            this.userList = this.recruiterList.map(item => {
                return {
                    userName: item.userName,
                    ddUserId: item.ddUserId
                }
            });
        },
        //获取招聘岗位
        async getInfo (planId) {
        //获取招聘专员
        //    let params = {
        //         deptName: '人事组',
        //         includeAllChildDpt: 1,
        //     }
        //     const {data} = await getDeptUsers(params)
        //     if(data.length > 0){
        //       this.recruiterList = data
        //       this.userList = data.map(item => {
        //                 return {
        //                     userName: item.userName,
        //                     ddUserId: item.ddUserId
        //                 }
        //             });

        //     }
            if (planId) {
                //获取岗位信息
                getRecruitmentPlan({ planId: planId }).then(res => {
                    if (res.success) {
                        this.ruleForm = res.data;
                        this.ruleForm.recruiterJson = JSON.parse(this.ruleForm.recruiterJson);
                        if (this.ruleForm.recruiterJson) {
                            this.ruleForm.recruiters = this.ruleForm.recruiterJson.map(item => {
                              const matchingUser = this.userList.find(user => user.ddUserId === item.DDUserId);
                                  if(matchingUser) {
                                        return item.DDUserId;
                                    }else{
                                        return item.UserName;
                                  }
                            });
                        }
                        let riqi = [this.ruleForm.recruitmentStartDate, this.ruleForm.recruitmentEndDate]
                        this.$set(this.ruleForm, 'dateRange', riqi)
                        // this.ruleForm.dateRange = [formatTime(this.ruleForm.recruitmentStartDate, 'YYYY-MM-DD'),
                        // formatTime(this.ruleForm.recruitmentEndDate, 'YYYY-MM-DD')];
                        // this.$set(this.pushForm, "interviewStartTime", start);
                        // this.$set(this.pushForm, "interviewEndTime", end);
                        this.groupName = this.ruleForm.departmentGroup
                    }
                })
            }
        },
        //提交招聘岗位
        submitForm (formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.subLoad = true
                    this.ruleForm.recruitmentStartDate = this.ruleForm.dateRange[0];
                    this.ruleForm.recruitmentEndDate = this.ruleForm.dateRange[1];
                    if (this.ruleForm.recruiterJson.every(recruiter => this.userList.some(user => user.ddUserId === recruiter.DDUserId))) {
                        this.ruleForm.recruiterJson = [];
                        this.$refs.recruiterJsonSelect.selected.forEach(element => {
                            let item = {
                                UserName: element.label, DDUserId: element.value
                            }
                            this.ruleForm.recruiterJson.push(item);
                        });
                    } else {
                        for (let i = 0; i < this.ruleForm.recruiters.length; i++) {
                            let recruiter = this.ruleForm.recruiters[i];
                            let found = this.ruleForm.recruiterJson.find(item => item.UserName === recruiter);
                            if (found) {
                                this.ruleForm.recruiters[i] = found.DDUserId;
                            }
                        }
                        this.$refs.recruiterJsonSelect.selected.forEach(element => {
                            if (Object.keys(element).length === 1 && 'value' in element) { // 判断element是否只有value属性
                                let found = this.ruleForm.recruiterJson.find(item => item.DDUserId === element.value);
                                if (found) {
                                    let item = {
                                        UserName: found.UserName,
                                        DDUserId: element.value
                                    }
                                    this.ruleForm.recruiterJson.push(item);
                                }
                            } else {
                                let item = {
                                    UserName: element.label,
                                    DDUserId: element.value
                                }
                                this.ruleForm.recruiterJson.push(item);
                            }
                        });
                    }
                    this.ruleForm.recruiterJson = this.ruleForm.recruiterJson.filter(item => item.UserName !== undefined);//去除UserName为undefined的对象
                    this.ruleForm.recruiterJson = JSON.stringify(this.ruleForm.recruiterJson);
                    this.ruleForm.recruitmentCount = Number(this.ruleForm.recruitmentCount);
                    // 新增或修改方法
                    if (this.ruleForm.planId) {
                        //修改
                        saveRecruitmentPlan(this.ruleForm).then(res => {
                            this.subLoad = false;
                            if (res.success) {
                                this.$message({ message: '修改成功', type: "success" });
                                this.$emit('subSucce');
                            }
                        })
                    } else {
                        //新增
                        createRecruitmentPlan(this.ruleForm).then(res => {
                            this.subLoad = false;
                            if (res.success) {
                                this.$message({ message: '新增成功', type: "success" });
                                this.$emit('subSucce');
                            }
                        })
                    }
                } else {
                    return false;
                }
            });
        },
    }
}
</script>
<style lang="scss" scoped>
::v-deep .el-input-number.is-without-controls .el-input__inner {
    padding-left: 5px;
    padding-right: 2px;
    text-align: left;
}
</style>
