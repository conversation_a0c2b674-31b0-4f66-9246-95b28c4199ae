<template>
    <my-container v-loading="pageLoading">
        <!--列表-->
        <!-- <vxetablebase :id="'goodsCostPriceChgList202301031318001'" :tableData='list' :tableCols='tableCols'
            :loading='listLoading' :border='true' :that="that" ref="vxetable" /> -->
                <!-- <template> -->
                <el-table :data="list" border height="400">
                <el-table-column label="#" width="40">
                    <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                </el-table-column>
                <el-table-column prop="id" label="id" v-if="false" />
                <el-table-column prop="pic" label="图片" width="60">
                    <template slot-scope="scope">
                        <el-image style="width: 30px; height: 30px"
                            :src="(!!scope.row.pic ? scope.row.pic : imagedefault)"
                            @click="showImgDtl(scope.row.pic)"></el-image>
                    </template>
                </el-table-column>
                <el-table-column prop="goodsName" label="商品名称" width="100" />
                <el-table-column prop="goodsCode" label="商品编码" width="100">
                    <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="clickProfit(scope.row)">
                            {{ scope.row.goodsCode }}
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="styleCode" label="系列编码" width="100" />
                <el-table-column prop="offTime" label="截止时间" width="120" />
                <el-table-column prop="personUsableQty" label="个人可用数" width="100" />
                <el-table-column prop="inTransitNum" label="采购在途数" width="100" >
                    <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="getPurchaseOrderDetail(scope.row.goodsCode)">
                            {{ scope.row.inTransitNum }}
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="avgInTransitTime" label="平均在途时长" width="100" >
                    <template slot-scope="scope">
                        {{ scope.row.avgInTransitTime  | myformatSecondToHour }}
                    </template>
                </el-table-column>
                <el-table-column prop="" label="趋势图" width="80" >
                    <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="getbirchart(scope.row.goodsCode, 30, 0)">
                            趋势图
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="salesDay" label="昨天销量" width="80" />
                <el-table-column prop="salesDay" label="3天销量" width="80" />
                <el-table-column prop="salesDay" label="3天均销量" width="80" >
                    <template slot-scope="scope">
                        {{ (scope.row.salesDay3 / 3).toFixed(0) }}
                    </template>
                </el-table-column>
                <el-table-column prop="salesDay7" label="7天销量" width="80" />
                <el-table-column prop="salesDay" label="7天均销量" width="80" >
                    <template slot-scope="scope">
                        {{ (scope.row.salesDay7 / 7).toFixed(0) }}
                    </template>
                </el-table-column>
                <el-table-column prop="salesDay15" label="15天销量" width="80" />
                <el-table-column prop="salesDay" label="15天均销量" width="80" >
                    <template slot-scope="scope">
                        {{ (scope.row.salesDay15 / 15).toFixed(0) }}
                    </template>
                </el-table-column>
                <el-table-column prop="salesDay30" label="30天销量" width="80" />
                <el-table-column prop="salesDay" label="30天均销量" width="80" >
                    <template slot-scope="scope">
                        {{ (scope.row.salesDay30 / 30).toFixed(0) }}
                    </template>
                </el-table-column>
                <el-table-column prop="turnoverDays" label="1天周转天数" width="100" >
                    <template slot-scope="scope">
                        {{ scope.row.turnoverDays.toFixed(2) }}
                    </template>
                </el-table-column>
                <el-table-column prop="turnoverDays3" label="3天周转天数" width="100" >
                    <template slot-scope="scope">
                        {{ scope.row.turnoverDays3.toFixed(2) }}
                    </template>
                </el-table-column>
                <el-table-column prop="claimCount" label="数量" width="auto">
                    <template slot-scope="scope">
                        <el-input-number style="width: 100px;" v-model="scope.row.claimCount" :controls="false" :precision="0" :min="0" :max="100000000"
                            placeholder="数量" align="center">
                        </el-input-number>
                    </template>
                </el-table-column>
                <!-- <el-table-column lable="操作" width="150">
                    <template slot-scope="scope">
                        <el-button type="danger" @click="onDelDtlGood(scope.$index)">删除
                        </el-button>
                    </template>
                </el-table-column> -->
            </el-table>
            <!-- </template> -->

            <template #footer>
                <el-row>
                <el-col :span="24" style="text-align:right;padding-top:10px;">
                    <el-button @click="$emit('close');">取消</el-button>
                    <my-confirm-button type="submit" :loading="onFinishLoading" @click="onFinish()">保存&关闭
                    </my-confirm-button>
                </el-col>
            </el-row>
            </template>

            <!-- 销量趋势 -->
        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="75%" height='700px'
            v-dialogDrag append-to-body>
            <span>
                <!-- <template>
                    <el-form class="ad-form-query" :model="filterchart" @submit.native.prevent label-width="100px">
                        <el-row>
                            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                                <el-form-item>
                                    <el-button type="primary"
                                        @click="getbirchartOnSearch(goodsCode, timeNum, timeType)">刷新</el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </template> -->
            </span>
            <span>
                <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>

         <!-- 编码明细 -->
         <el-dialog :visible.sync="dialogVisible" title="详情信息" width="1200" v-dialogDrag append-to-body>
            <procodedetail :filter="filterdetail" ref="procodedetail"></procodedetail>
        </el-dialog>

        <vxe-modal v-model="visiblepopoverdetail" :width="750" :position="{top: 380, left: '100px'}"  resize mask-closable>
          <template #default>
                <el-table :data="detaillist" border max-height="300" :loading="popoverdetailloding">
                    <el-table-column width="auto" property="goodsCode" label="商品编码"></el-table-column>
                    <el-table-column width="auto" property="price" label="单价">
                        <template slot-scope="scope">
                            <span>{{scope.row.price}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column width="60" property="count" label="数量">
                        <template slot-scope="scope">
                            <span>{{scope.row.count}}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
        </vxe-modal>

        <el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeFuncDtl" style="z-index:9999;" />
    </my-container>
</template>

<script>
import { Loading } from 'element-ui';
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import {  addOrUpdateProductGoodsCode, getPurOrderAnalysisForOperate, getGoodsCodeAllocationList, getPurchaseOrderDetail } from "@/api/inventory/goodscodestock"
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import buschar from '@/components/Bus/buschar'
import { formatSecondNewToHour  } from "@/utils/tools";
import procodedetail from './procodedetail.vue'


const tableCols = [
    { istrue: true, prop: 'pic', label: '图片', width: '120', type: 'images' },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: '120', },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '120', },
    { istrue: true, prop: 'styleCode', label: '系列编码', width: '120', sortable: 'custom', },
    { istrue: true, prop: '', label: '个人可用数', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'inTransitNum', label: '采购在途数', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'avgInTransitTime', label: '平均在途时长', width: '100', },
    {
        istrue: true, label: `销量`, merge: true, prop: 'mergeField',
        cols: [
            { istrue: true, prop: 'salesDay', label: '日销量', width: '80', },
            { istrue: true, prop: 'salesDay3', label: '3天销量', width: '80', },
            { istrue: true, prop: 'salesDay3', label: '3天均销量', width: '80', formatter: (row) => (row.salesDay3 / 3).toFixed(0) },
            { istrue: true, prop: 'salesDay7', label: '7天销量', width: '80', },
            { istrue: true, prop: 'salesDay7', label: '7天均销量', width: '80', formatter: (row) => (row.salesDay7 / 7).toFixed(0) },
            { istrue: true, prop: 'salesDay15', label: '15天销量', width: '80', },
            { istrue: true, prop: 'salesDay15', label: '15天均销量', width: '80', formatter: (row) => (row.salesDay15 / 15).toFixed(0) },
            { istrue: true, prop: 'salesDay30', label: '30天销量', width: '80', },
            { istrue: true, prop: 'salesDay30', label: '30天均销量', width: '80', formatter: (row) => (row.salesDay30 / 30).toFixed(0) },
        ]
    },
    { istrue: true, prop: 'turnoverDays', label: '1天周转天数', width: '80', sortable: 'custom', formatter: (row) => (row.turnoverDays).toFixed(2) },
    { istrue: true, prop: 'turnoverDays3', label: '3天周转天数', width: '80', sortable: 'custom', formatter: (row) => (row.turnoverDays3).toFixed(2) },
    { istrue: true, prop: 'claimTime', label: '认领时间', width: '100', },
    { istrue: true, prop: 'claimCount', label: '认领数量', width: '80', sortable: 'custom', },
    { istrue: true, prop: 'claimStatus', label: '状态', width: '120', sortable: 'custom', formatter: (row) =>
                row.claimStatus == 0 ? "领取中" : row.claimStatus == 1 ? "认领成功" : row.claimStatus == 2 ? "认领失败" : ""},
];

const tableHandles = [

];

const startDate = formatTime(dayjs().subtract(15, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

var formatSecondToHour1 = function(time) {
    return formatSecondNewToHour(time);
}

export default {
    name: 'YunHanAdminSetgoodscodeallocation',
    components: { MyContainer, MyConfirmButton, vxetablebase, ElImageViewer, buschar, procodedetail },
    filters: {
        myformatSecondToHour(val) {
            return formatSecondToHour1(val)
        }
    },
    data() {
        return {
            that: this,
            tableCols: tableCols,
            tableHandles: tableHandles,
            list: [],
            imgList: [],
            detaillist: [],
            pageLoading: false,
            listLoading: false,
            onFinishLoading: false,
            showGoodsImage: false,
            dialogVisible: false,
            visiblepopoverdetail: false,
            popoverdetailloding: false,
            timeType: null,
            goodsCode: null,
            imagedefault: require('@/assets/images/detault.jpeg'),
            buscharDialog: { visible: false, title: "", data: [] },
            filterdetail: {
                goodsCode: null,
                startDate: null,
                endDate: null,
                timerange: [startDate, endDate]
            },
            filterchart: {
                startDate: null,
                endDate: null,
                timerange: [startDate, endDate]
            },
            pickerOptionspie: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近半个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
        };
    },

    async mounted() {

    },

    methods: {
        async loadData(list) {
            this.pageLoading = true;
            console.log('带入的数据',Object.values(list))

            var data = Object.values(list);
            var goodsCodes = data.map((item) => item.goodsCode).join();

            var res = await getGoodsCodeAllocationList({goodsCodes})
            var resList = res.data;

            resList.forEach((f, i) => {
            const correspondingItem = data.find((item) => item.goodsCode === f.goodsCode);
            if (correspondingItem) {
                resList[i] = {
                ...f,
                pic: correspondingItem.picture,
                goodsName: correspondingItem.goodsName,
                styleCode: correspondingItem.styleCode,
                salesDay: correspondingItem.salesDay,
                salesDay3: correspondingItem.salesDay3,
                salesDay7: correspondingItem.salesDay7,
                salesDay15: correspondingItem.salesDay15,
                salesDay30: correspondingItem.salesDay30,
                turnoverDays: correspondingItem.turnoverDays,
                turnoverDays3: correspondingItem.turnoverDays3,
                personUsableQty: correspondingItem.personUsableQty,
                };
            }
            });

            console.log('编码', goodsCodes)

            this.list = resList;
            this.pageLoading = false;
            return;
        },
        async dtlCountChange(row) {

        },
        async onFinish() {
            this.onFinishLoading = true;
            // if (this.addForm.count == null || this.addForm.count == 0) {
            //     this.$message({ message: '请填写进货量！', type: "warning" });
            //     return false;
            // }
            var para = [];
            this.list.forEach(f => {
                para.push({
                    id: f.id,
                    indexNo: f.indexNo,
                    offTime: f.offTime,
                    goodsCode: f.goodsCode,
                    styleCode: f.styleCode,
                    count: f.count,
                    claimCount: f.claimCount,
                    salesDay: f.salesDay,
                    salesDay3: f.salesDay3,
                    salesDay7: f.salesDay7,
                    salesDay15: f.salesDay15,
                    salesDay30: f.salesDay30,
                    turnoverDays: f.turnoverDays,
                    turnoverDays3: f.turnoverDays3,
                    personUsableQty: f.personUsableQty,
                    inTransitNum: f.inTransitNum,
                    avgInTransitTime: f.avgInTransitTime,
                })
            });

            var res = await addOrUpdateProductGoodsCode(para);
            if (res?.success) {
                this.$message({ message: '成功', type: "success" });
                this.$emit('afterSave');
                this.$emit('close');
            } else {
                //this.$message({ message: res.msg, type: "warning" });
            }
            this.onFinishLoading = false;
        },
        async getbirchart(goodsCode, number, type) {
            this.startDate = formatTime(dayjs().subtract(number, 'day'), "YYYY-MM-DD");
            this.endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
            this.filterchart.timerange = [this.startDate, this.endDate];
            this.filterchart.startDate = null;
            this.filterchart.endDate = null;
            if (this.filterchart.timerange) {
                this.filterchart.startDate = this.startDate;
                this.filterchart.endDate = this.endDate;
            }
            this.goodsCode = goodsCode;
            this.timeNum = number;
            this.timeType = type;
            let loadingInstance = Loading.service();
            Loading.service({ fullscreen: true });
            var that = this;
            const params = { goodsCode: goodsCode, day: number, timeType: type, ...this.filterchart };
            //console.log('数据来了', params);
            await getPurOrderAnalysisForOperate(params).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data;
                that.buscharDialog.title = '商品编码：' + goodsCode;
            });
            await this.$refs.buschar.initcharts()
            loadingInstance.close();
        },
        //分页查询
        async getlist() {

        },
        //查看明细图片
        async showImgDtl(e) {
            this.showGoodsImage = true;
            this.imgList = [];
            if (e) {
                this.imgList.push(e);
            }
            else {
                this.imgList.push(this.pic);
            }
        },
        //关闭查看明细图片
        async closeFuncDtl() {
            this.showGoodsImage = false;
        },
        async clickProfit(row) {
            this.dialogVisible = true;
            this.filterdetail.goodsCode = row.goodsCode
            this.filterdetail.startDate = null;
            this.filterdetail.endDate = null;
            if (this.filterdetail.timerange) {
                this.filterdetail.startDate = this.filterdetail.timerange[0];
                this.filterdetail.endDate = this.filterdetail.timerange[1];
            }
            this.$nextTick(() => {
                this.$refs.procodedetail.clearData();
                this.$refs.procodedetail.onSearch();
            })
        },
        async getPurchaseOrderDetail(goodsCode) {
            this.visiblepopoverdetail = true;
            this.popoverdetailloding = true;
            var para = {goodsCode: goodsCode};
            var res = await getPurchaseOrderDetail(para);
            if (!(res.code == 1 && res.data)) return
                this.detaillist = res.data;
            this.popoverdetailloding = false;
        }
    },
};
</script>

<style lang="scss" scoped>

</style>
