<template>
    <my-container v-loading="pageLoading">
      <template>             
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
          :tableData='list'  :tableCols='tableCols' :isSelection="false" @select="selectchange" :isSelectColumn='false'
          :tableHandles='tableHandles' @cellclick="cellclick"
          :loading="listLoading">
        </ces-table>
    </template>
    <template #footer>
          <my-pagination
            ref="pager"
            :total="total"
            :checked-count="sels.length"
            @get-page="getlist"
          />
        </template>
    </my-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import {formatLinkProCode, formatSendWarehouse,formatPlatform} from "@/utils/tools";
import { getOrderWithholdTXList as getOrderWithholdList , exportOrderWithhold, importPinOrderIllegal} from "@/api/order/orderdeductmoney"
import { rulePlatform, ruleIllegalType} from "@/utils/formruletools";
import * as echarts from "echarts";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

const tableCols =[
        {istrue:true,prop:'proCode',label:'宝贝ID', width:'120',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
        {istrue:true,prop:'occurrenceTime',label:'日期', width:'100',sortable:'custom',formatter:(row)=>formatTime(row.occurrenceTime,"YYYY-MM-DD")},
        {istrue:true,prop:'platform',label:'平台', width:'80',sortable:'custom',formatter:(row)=>formatPlatform(row.platform) }, 
        {istrue:true,prop:'goodsName',label:'商品名称', width:'200',formatter:(row)=> !row.goodsName? "" : row.goodsName},       
        {istrue:true,prop:'shopId',label:'店铺', width:'150',formatter:(row)=> !row.shopName? " " : row.shopName},  
        {istrue:true,prop:'sendWarehouse',label:'发货仓', width:'125',sortable:'custom',formatter:(row)=>formatSendWarehouse(row.sendWarehouse)},     
        {istrue:true,prop:'groupId',label:'小组', width:'60',formatter:(row)=> !row.groupName?" " : row.groupName },
        {istrue:true,prop:'operateSpecialUserId',label:'运营专员', width:'80',formatter:(row)=> !row.operateSpecialUserName?" " : row.operateSpecialUserName},       
        {istrue:true,prop:'amountPaid',label:'扣款金额', width:'100',sortable:'custom',formatter:(row)=>parseFloat(row.amountPaid.toFixed(6))},
        {istrue:true,prop:'errorType',label:'扣款原因', width:'auto',sortable:'custom',formatter:(row)=> !row.errorType?" " : row.errorType},
          {istrue:true,prop:'dept',label:'部门', width:'auto',sortable:'custom',formatter:(row)=> !row.dept?" " : row.dept}
]

const tableHandles=[
              
      ];

export default {
    name: 'YunhanAdminOrderillegaldetail',
    components: {cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, },
    props:{
        filter:{ }
    },
    data() {
        return {
            that:this,
            list: [],
            platformList: [],
            illegalTypeList : [],
            summaryarry:{},
            pager:{OrderBy:"OccurrenceTime",IsAsc:false},
            filterImport:{
            platform:1,
            occurrenceTime:formatTime(dayjs().subtract(1,"day"), "YYYY-MM-DD")
            },
            pickerOptions:{
                disabledDate(time){
                return time.getTime()>Date.now();
                }
            },           
            tableCols:tableCols,
            tableHandles:tableHandles,
            total: 0,
            sels: [], 
            listLoading: false,
            pageLoading: false,
            dialogVisible: false,
            uploadLoading: false,
            uploadLoading: false,
            orderIllgalBoardTableTx:{
              filter:{     
                timerange:[],
                remark:'',
                groupId:'',
                dutyDept:'',
                IllegalType:''
        }
      },
        };
    },

    async mounted() {
        //await this.setPlatform()
        //await this.onSearch()
        
    },

    methods: {
    //查询第一页
    async onSearch() {
      if (!this.filter.timerange) {this.$message({message: "请选择日期",type: "warning",});return;}
      this.$refs.pager.setPage(1)
      await this.getlist();
    },   
    //获取查询条件
    getCondition(){
      if (this.filter.timerange&&this.filter.timerange.length>1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      else {
        this.$message({message:"请先选择日期",type:"warning"});
        return false;
      }
      //this.filter.platform=2
      var pager = this.$refs.pager.getPager();
      var page  = this.pager;
      const params = {
        ...pager,
        ...page,
        ... this.filter
      }

      return params;
    }, 
    //分页查询
     async getlist() {
        var params=this.getCondition();
        if(params===false){
                return;
        }
        console.log("部门看板最终参数",params);
        if(params.dutyDept=="未知")
        {
          params.dutyDept=""
        }
        this.listLoading = true
        const res = await getOrderWithholdList(params)
        this.listLoading = false
        if (!res?.success) {
            return
        }
        this.total = res.data.total;
        const data = res.data.list;
        this.summaryarry=res.data.summary;
        if(this.summaryarry)
            this.summaryarry.amountPaid_sum=parseFloat(this.summaryarry.amountPaid_sum.toFixed(6));
        data.forEach(d => {
            d._loading = false
        })
        this.list = data
       },
    //排序查询
    async sortchange(column){
    if(!column.order)
        this.pager={};
    else{
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
    }
    await this.onSearch();
    },  
    selectchange:function(rows,row) {
      this.selids=[];console.log(rows)
      rows.forEach(f=>{
          this.selids.push(f.id);
      })
    },
    cellclick(row, column, cell, event){
    
    }, 
    },
};
</script>

<style lang="scss" scoped>
</style>
