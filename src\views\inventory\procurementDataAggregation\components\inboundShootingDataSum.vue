<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss"
                    :clearable="false" />
                <el-input v-model.trim="ListInfo.userName" placeholder="姓名" maxlength="50" clearable class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false" :treeProp="{}"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%"
            :isNeedExpend="false" :showsummary="data.summary ? true : false" :summaryarry="data.summary"
            @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <vxe-modal title="入库拍摄绑单记录" v-model="warehousingOrderVideoBrandVisible" :esc-closable="true" :width='1200' :height='600'>
            <warehousingOrderVideoBrand :detailsInfo="warehousingOrderVideoBrandParam" v-if="warehousingOrderVideoBrandVisible" />
        </vxe-modal>

        <vxe-modal title="入库拍摄驳回记录" v-model="warehousingOrderVideoRejectVisible" :esc-closable="true" :width='1200' :height='600'>
            <warehousingOrderVideoReject :detailsInfo="warehousingOrderVideoRejectParam" v-if="warehousingOrderVideoRejectVisible" />
        </vxe-modal>

        <vxe-modal :title="this.hotSaleGoodsBuildDocAuditParam.title" v-model="hotSaleGoodsBuildDocAuditVisible" :esc-closable="true" :width='1200' :height='600'>
            <hotSaleGoodsBuildDocAudit :detailsInfo="hotSaleGoodsBuildDocAuditParam" v-if="hotSaleGoodsBuildDocAuditVisible" />
        </vxe-modal>

        <vxe-modal :title="this.hotSaleGoodsBuildDocAuditProcessParam.title" v-model="hotSaleGoodsBuildDocAuditProcessVisible" :esc-closable="true" :width="1200" :height="600">
            <hotSaleGoodsBuildDocAuditProcess :detailsInfo="hotSaleGoodsBuildDocAuditProcessParam" v-if="hotSaleGoodsBuildDocAuditProcessVisible" />
        </vxe-modal>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
import warehousingOrderVideoBrand from './warehousingOrderVideoBrand.vue';
import warehousingOrderVideoReject from './warehousingOrderVideoReject.vue';
import hotSaleGoodsBuildDocAudit from './hotSaleGoodsBuildDocAudit.vue';
import hotSaleGoodsBuildDocAuditProcess from './hotSaleGoodsBuildDocAuditProcess.vue';
const api = '/api/inventory/PurchaseSummary/InWms/';

export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, warehousingOrderVideoBrand, warehousingOrderVideoReject, hotSaleGoodsBuildDocAudit, hotSaleGoodsBuildDocAuditProcess
    },
    data() {
        return {
            api,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false,
                summarys: [],
                startDate: (dayjs().subtract(1, 'day').format('YYYY-MM') + '-01'),
                endDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            warehousingOrderVideoBrandVisible: false,
            warehousingOrderVideoBrandParam: {
                startDate: null,
                endDate: null,
                userId: null
            },
            warehousingOrderVideoRejectVisible: false,
            warehousingOrderVideoRejectParam: {
                startDate: null,
                endDate: null,
                userId: null
            },
            hotSaleGoodsBuildDocAuditVisible: false,
            hotSaleGoodsBuildDocAuditParam: {
                startDate: null,
                endDate: null,
                userId: null,
                auditType: null,
                title: ''
            },
            hotSaleGoodsBuildDocAuditProcessVisible: false,
            hotSaleGoodsBuildDocAuditProcessParam:{
                startDate: null,
                endDate: null,
                userId: null,
                auditType: null,
                title: ''
            }
        }
    },
    async mounted() {
        await this.getCol();
        await this.getList()
    },
    methods: {
        proCodeCallback(val) {
            this.ListInfo.proCode = val
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                data.forEach(item => {
                    if (item.field == 'dayItem') {
                        item.formatter = (row) => {
                            if (row.isParent) {
                                return row.dayItem ? dayjs(row.dayItem).format('YYYY-MM') : ''
                            } else {
                                return row.dayItem ? dayjs(row.dayItem).format('MM-DD') : ''
                            }
                        }
                    }
                    if (item.prop == 'bindCount'){
                        item.type = 'click'
                        item.handle = (that, row) => that.openBindCountDetails(row)
                    }
                    if (item.prop == 'rejectCount'){
                        item.type = 'click'
                        item.handle = (that, row) => that.openRejectCountDetails(row)
                    }
                    if (item.prop == 'passCount'){
                        item.type = 'click'
                        item.handle = (that, row) => that.openPassCountDetails(row)
                    }
                    if (item.prop == 'unPassCount'){
                        item.type = 'click'
                        item.handle = (that, row) => that.openUnPassCountDetails(row)
                    }
                    if (item.prop == 'processPassCount'){
                        item.type = 'click'
                        item.handle = (that, row) => that.openProcessApplyCountDetails(row,2)
                    }
                    if (item.prop == 'processUnPassCount'){
                        item.type = 'click'
                        item.handle = (that, row) => that.openProcessApplyCountDetails(row,-1)
                    }
                })
                this.tableCols = data;
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        openBindCountDetails(row) {
            this.warehousingOrderVideoBrandParam = {
                startDate: this.ListInfo.startDate,
                endDate: this.ListInfo.endDate,
                userId: row.userId
            };
            this.warehousingOrderVideoBrandVisible = true;
        },
        openRejectCountDetails(row) {
            this.warehousingOrderVideoRejectParam = {
                startDate: this.ListInfo.startDate,
                endDate: this.ListInfo.endDate,
                userId: row.userId
            };
            this.warehousingOrderVideoRejectVisible = true;
        },
        openPassCountDetails(row) {
            this.hotSaleGoodsBuildDocAuditParam = {
                startDate: this.ListInfo.startDate,
                endDate: this.ListInfo.endDate,
                userId: row.userId,
                auditType: 2
            };
            this.hotSaleGoodsBuildDocAuditParam.title = "建编码初审通过";
            this.hotSaleGoodsBuildDocAuditVisible = true;
        },
        openUnPassCountDetails(row) {
            this.hotSaleGoodsBuildDocAuditParam = {
                startDate: this.ListInfo.startDate,
                endDate: this.ListInfo.endDate,
                userId: row.userId,
                auditType: -1
            };
            this.hotSaleGoodsBuildDocAuditParam.title = "建编码初审驳回";
            this.hotSaleGoodsBuildDocAuditVisible = true;
        },
        openProcessApplyCountDetails(row, status){
            this.hotSaleGoodsBuildDocAuditProcessParam = {
                startDate: this.ListInfo.startDate,
                endDate: this.ListInfo.endDate,
                userId: row.userId,
                auditType: status
            };
            this.hotSaleGoodsBuildDocAuditProcessParam.title = status == 2 ? "建编码审核通过" : "建编码审核驳回";
            this.hotSaleGoodsBuildDocAuditProcessVisible = true;
            console.log(this.hotSaleGoodsBuildDocAuditProcessParam,'this.hotSaleGoodsBuildDocAuditProcessParam');
        }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
