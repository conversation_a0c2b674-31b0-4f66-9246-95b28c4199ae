<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                    <el-date-picker size="mini" v-model="filter.daterange" type="daterange" range-separator="至"
                        start-placeholder="开始日期" value-format="yyyy-MM-dd" end-placeholder="结束日期" style="width: 240px;"
                        :picker-options="pickerOptions" :clearable="false" @change="onSearch()">
                    </el-date-picker>
                </el-button>
                <el-button style="padding: 0;">
                    <el-select v-model.trim="filter.businessMan" filterable clearable placeholder="商务"
                        style="width:120px" @change="onSearch()">
                        <el-option label="无商务" value="无商务"></el-option>
                        <el-option v-for="item in myBusinessManList" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组"
                        style="width:120px" @change="onSearch()">
                        <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                        <el-option v-for="item in myGrouplist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.isDelete" collapse-tags clearable placeholder="状态"
                        style="width:120px" @change="onSearch()">
                        <el-option label="在职" :value="0"></el-option>
                        <el-option label="离职" :value="1"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.full_code" collapse-tags clearable placeholder="公司"
                        style="width:120px" @change="onSearch()">
                        <el-option label="义乌" value="597959144"></el-option>
                        <el-option label="南昌" value="599262021"></el-option>
                        <el-option label="武汉" value="847451347"></el-option>
                        <el-option label="杭州" value="854910527"></el-option>
                        <el-option label="西安" value="851106774"></el-option>
                    </el-select>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
            </el-button-group>
        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :isSelectColumn="false" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
            :tableData='datalist' :tableCols='tableCols1' :loading="listLoading" @summaryClick='onsummaryClick'
            style="width:100%;height:100%;margin: 0">
        </ces-table>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>



        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%" v-dialogDrag
            :close-on-click-modal="false">
            <my-container>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data">
                    </buschar>
                </span>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
                </span>
            </my-container>
        </el-dialog>

    </my-container>
</template>
<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import {
    GetDyWiseManWorkPageList, GetDyWiseManWorkChat
} from "@/api/bookkeeper/reportdayDouYin";
import { getBusinessOrderDayReport_AllList, getBusinessOrderDayReport_AllChartList } from '@/api/bookkeeper/reportdayV2'
import buschar from '@/components/Bus/buschar';

const tableCols1 = [
    { istrue: true, prop: 'businessMan', label: '商务BD', sortable: 'custom', width: '120', formatter: (row) => row.businessMan, type: 'click', handle: (that, row) => that.formatLinkBusinessDDUserId(row.businessMan) },
    { istrue: true, prop: 'isDelete', label: '状态', sortable: 'custom', width: '80', formatter: (row) => row.isDelete == 1 ? "离职" : (row.isDelete == 0 ? "在职" : "") },
    // { istrue: true, prop: 'groupId', label: '所在组', sortable: 'custom', width: '120', formatter: (row) => row.groupName },
    { istrue: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width: '100', summaryEvent: true, },
    { istrue: true, prop: 'payAmount', label: '付款金额', sortable: 'custom', width: '100', formatter: (row) => row.payAmount ? row.payAmount.toFixed(2) : "", summaryEvent: true, },
    { istrue: true, prop: 'saleAmount', label: '销售金额', sortable: 'custom', width: '100', formatter: (row) => row.saleAmount ? row.saleAmount.toFixed(2) : "", summaryEvent: true, },
    { istrue: true, prop: 'commissionAmount', label: '精选联盟佣金', sortable: 'custom', width: '140', formatter: (row) => row.commissionAmount ? row.commissionAmount.toFixed(2) : "", summaryEvent: true, },
    { istrue: true, prop: 'commissionRate', label: '佣金率', sortable: 'custom', width: '100', formatter: (row) => row.commissionRate ? row.commissionRate.toFixed(2) + "%" : "", summaryEvent: true, },
    { istrue: true, prop: 'hired_date', label: '入职日期', sortable: 'custom', width: '120', formatter: (row) => formatTime(row.hired_date, "YYYY-MM-DD") },
    { istrue: true, prop: 'workDuration', label: '上班时长', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'full_code', label: '公司', sortable: 'custom', width: '90' },
    { istrue: true, prop: 'profit3', label: '毛三', sortable: 'custom', width: '80', formatter: (row) => row.profit3 ? row.profit3.toFixed(2) : "", summaryEvent: true, },
    { istrue: true, prop: 'profit3Rate', label: '毛三率', sortable: 'custom', width: '90', formatter: (row) => row.profit3Rate ? row.profit3Rate.toFixed(2) + "%" : "", summaryEvent: true, },
    { istrue: true, prop: 'profit4', label: '毛四', sortable: 'custom', width: '80', formatter: (row) => row.profit4 ? row.profit4.toFixed(2) : "", summaryEvent: true, },
    { istrue: true, prop: 'profit4Rate', label: '毛四率', sortable: 'custom', width: '90', formatter: (row) => row.profit4Rate ? row.profit4Rate.toFixed(2) + "%" : "", summaryEvent: true, },
    { istrue: true, prop: 'profit6', label: '毛六', sortable: 'custom', width: '80', formatter: (row) => row.profit6 ? row.profit6.toFixed(2) : "", summaryEvent: true, },
    { istrue: true, prop: 'profit6Rate', label: '毛六率', sortable: 'custom', width: '90', formatter: (row) => row.profit6Rate ? row.profit6Rate.toFixed(2) + "%" : "", summaryEvent: true, },
    { istrue: true, label: "趋势图", style: "color:red;cursor:pointer;", width: 70, type: "click", handle: (that, row) => that.showchart(row, 'user'), formatter: (row) => "趋势图", },
];
const tableCols2 = [
    { istrue: true, prop: 'businessDDUserId', label: '商务BD', sortable: 'custom', width: '100', formatter: (row) => row.businessMan },
    { istrue: true, prop: 'isDelete', label: '状态', sortable: 'custom', width: '80', formatter: (row) => row.isDelete == 1 ? "离职" : (row.isDelete == 0 ? "在职" : "") },
    { istrue: true, prop: 'shopCode', label: '店铺', sortable: 'custom', width: '180', formatter: (row) => row.shopName },
    { istrue: true, prop: 'groupId', label: '所在组', sortable: 'custom', width: '110', formatter: (row) => row.groupName },
    { istrue: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width: '90', summaryEvent: true, },
    { istrue: true, prop: 'payAmount', label: '付款金额', sortable: 'custom', width: '100', formatter: (row) => row.payAmount ? row.payAmount.toFixed(2) : "", summaryEvent: true, },
    { istrue: true, prop: 'saleAmount', label: '销售金额', sortable: 'custom', width: '100', formatter: (row) => row.saleAmount ? row.saleAmount.toFixed(2) : "", summaryEvent: true, },
    { istrue: true, prop: 'commissionAmount', label: '精选联盟佣金', sortable: 'custom', width: '135', formatter: (row) => row.commissionAmount ? row.commissionAmount.toFixed(2) : "", summaryEvent: true, },
    { istrue: true, prop: 'commissionRate', label: '佣金率', sortable: 'custom', width: '100', formatter: (row) => row.commissionRate ? row.commissionRate.toFixed(2) + "%" : "", summaryEvent: true, },
    { istrue: true, prop: 'hired_date', label: '入职日期', sortable: 'custom', width: '110', formatter: (row) => formatTime(row.hired_date, "YYYY-MM-DD") },
    { istrue: true, prop: 'workDuration', label: '上班时长', sortable: 'custom', width: '90' },
    { istrue: true, prop: 'profit3', label: '毛三', sortable: 'custom', width: '80', formatter: (row) => row.profit3 ? row.profit3.toFixed(2) : "", summaryEvent: true, },
    { istrue: true, prop: 'profit3Rate', label: '毛三率', sortable: 'custom', width: '90', formatter: (row) => row.profit3Rate ? row.profit3Rate.toFixed(2) + "%" : "", summaryEvent: true, },
    { istrue: true, prop: 'profit4', label: '毛四', sortable: 'custom', width: '80', formatter: (row) => row.profit4.toFixed(2), summaryEvent: true, },
    { istrue: true, prop: 'profit4Rate', label: '毛四率', sortable: 'custom', width: '90', formatter: (row) => row.profit4Rate ? row.profit4Rate.toFixed(2) + "%" : "", summaryEvent: true, },
    { istrue: true, prop: 'profit6', label: '毛六', sortable: 'custom', width: '80', formatter: (row) => row.profit6 ? row.profit6.toFixed(2) : "", summaryEvent: true, },
    { istrue: true, prop: 'profit6Rate', label: '毛六率', sortable: 'custom', width: '90', formatter: (row) => row.profit6Rate ? row.profit6Rate.toFixed(2) + "%" : "", summaryEvent: true, },
    { istrue: true, label: "趋势图", style: "color:red;cursor:pointer;", width: 70, type: "click", handle: (that, row) => that.showchart(row, 'usershop'), formatter: (row) => "趋势图", },
];
export default {
    name: "DyWiseManWorkUser",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, buschar,
    },
    props: ['myGrouplist', 'myBusinessManList', "myShopList"],
    data() {
        return {
            that: this,
            filter: {
                businessMan: "",
                daterange: [
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD HH:mm"),
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD HH:mm")
                ],
                dataType: 2,
            },
            tableCols1: tableCols1,
            total: 0,
            datalist: [],
            pager: { OrderBy: "businessDDUserId", IsAsc: true },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            pickerOptions: {
                shortcuts: [{
                    text: '近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },

            dialogMapVisible: { visible: false, title: "", data: {} },
        };
    },
    async mounted() {
        await this.onSearch();
    },
    async created() {
    },
    methods: {
        //排序
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            if (this.filter.businessMan)
                this.tableCols1 = tableCols2
            else
                this.tableCols1 = tableCols1
            await this.getList();
        },
        getListParam() {
            if (this.filter.daterange) {
                this.filter.startTime  = this.filter.daterange[0];
                this.filter.endTime  = this.filter.daterange[1];
            } else {
                this.$message({ type: 'error', message: '请选择要查询的日期!' });
                return;
            }
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            return params
        },
        async getList() {
            var that = this;
            const params = this.getListParam();

            that.pageLoading = true;
            const res = await getBusinessOrderDayReport_AllList(params);
            that.pageLoading = false;

            that.total = res.data?.total;
            that.datalist = res.data?.list;
            that.summaryarry = res.data?.summary;
            that.summaryarry.profit3Rate_sum = that.summaryarry && that.summaryarry.profit3Rate_sum ? String(that.summaryarry.profit3Rate_sum) + "%" : "";
            that.summaryarry.profit4Rate_sum = that.summaryarry && that.summaryarry.profit4Rate_sum ? String(that.summaryarry.profit4Rate_sum) + "%" : "";
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async formatLinkBusinessDDUserId(businessMan) {
            this.filter.businessMan = businessMan;
            await this.onSearch();
        },
        async showchart(row, type) {
            let me = this;
            const params = this.getListParam();
            if (params === false) {
                return;
            }
            params.usershop = type;
            if (type == "user") {
                params.businessMan = row.businessMan;
            }
            if (type == "usershop") {
                params.businessMan = row.businessMan;
                params.shopCode = row.shopCode;
            }
            this.dialogMapVisible.title = "汇总趋势图";
            const res = await getBusinessOrderDayReport_AllChartList(params).then(res => {
                me.dialogMapVisible.visible = true;
                me.dialogMapVisible.data = res.data;
                console.log(res);
            })
        },
        async onsummaryClick(property) {
            const params = this.getListParam();
            if (params === false) {
                return;
            }
            params.usershop = "user";
            if (this.filter.businessMan)
                params.usershop = "usershop";

            this.pageLoading = true;
            this.dialogMapVisible.title = "汇总趋势图";
            const res = await getBusinessOrderDayReport_AllChartList(params);

            this.pageLoading = false;
            this.dialogMapVisible.visible = true;
            res.selectedLegend = res.legend;
            this.dialogMapVisible.data = res.data;
            console.log(res);

        },
    }
};
</script>
