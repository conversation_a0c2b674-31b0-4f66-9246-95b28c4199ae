<template>

    <div style="height:100%;padding:10px;overflow: auto;">

        <div id="echartturnoverranganalysis11"
            :style="{ width: '100%', height: height, boxSizing: 'border-box', lineHeight: '360px' }" />
    </div>
</template>
<script>
import * as echarts from 'echarts';
import container from '@/components/my-container/nofooter'
import cesTable from "@/components/Table/table.vue";
import { getProCodeUnsalableAnalysisByDate, getProCodeUnsalableAnalysisByGroup } from '@/api/inventory/unsalable'
import { getproductinfo } from '@/api/bookkeeper/crossBorderV2'
export default {
    name: 'Roles',
    components: { container, cesTable },
    props: {
        filter: {},
        submitdata: [],
        height: { type: String, default: '600px' },
    },
    data() {
        return {
            that: this,
            period: 0,
            pageLoading: false,
            listLoading: false,
            procode: '',
            platform: '',
            refundType: 2
        }
    },
    async mounted() {
        this.procode = window['lastseeprcodedrchart'];
        this.platform = window['lastseeprcodedrchart1'];
        this.refundType = window['lastseeprcodedrchart2'];
        await this.getanalysisdata();
    },
    beforeUpdate() {
    },
    async onload() {

        this.procode = window['lastseeprcodedrchart'];
        this.platform = window['lastseeprcodedrchart1'];
        this.refundType = window['lastseeprcodedrchart2'];
        await this.getanalysisdata();
    },
    methods: {

        async getanalysisdata() {
            var p = this.submitdata ? {
                ProCode: this.procode, Platform: this.platform, refundType: this.refundType,
                startDate: this.submitdata.createdtimerange[0],
                endDate: this.submitdata.createdtimerange[1],
            } : {
                ProCode: this.procode, Platform: this.platform, refundType: this.refundType,
            };
            const respr = await getproductinfo(p);
            var chartDom = document.getElementById('echartturnoverranganalysis11');
            var myChart = echarts.init(chartDom);
            myChart.clear();
            if (!respr.data) {
                this.$message({ message: "没有数据!", type: "warning", });
                return;
            }
            //console.log(respr.data.series[0].data);
            var max1 = Math.max.apply(Math, respr.data.series[0].data);
            var max2 = Math.max.apply(Math, respr.data.series[1].data);
            var max3 = Math.max.apply(Math, respr.data.series[3].data);
            var max4 = Math.max.apply(Math, respr.data.series[4].data);
            var max5 = Math.max.apply(Math, respr.data.series[5].data);
            var max = Math.max.apply(Math, [max1, max2, max3, max4, max5]);

            var min1 = Math.min.apply(Math, respr.data.series[0].data);
            var min2 = Math.min.apply(Math, respr.data.series[1].data);
            var min3 = Math.min.apply(Math, respr.data.series[3].data);
            var min4 = Math.min.apply(Math, respr.data.series[4].data);
            var min5 = Math.min.apply(Math, respr.data.series[5].data);
            var min = Math.min.apply(Math, [min1, min2, min3, min4, min5]);


            var minmaolilv = Math.min.apply(Math, respr.data.series[2].data);

            //respr.data.series[2].type='line'
            // if(max<200)
            //  max=200;
            max = (max / 10) * 10;
            respr.data.yAxis = [{ position: "left", splitLine: { show: true }, min: 0, max: max, unit: "元", name: "总额", offset: 0 }, { position: "right", splitLine: { show: false }, unit: "%", name: "毛利率(%)", offset: 0 }];
            var option = this.Getoptions(respr.data);
            //option.yAxis[1].max=100;

            //  if(min<0)
            //   {
            //   option.yAxis[0].splitNumber=10;
            //   option.yAxis[1].splitNumber=10;
            //   var minx=Math.abs(min)/(Math.abs(min)+max);
            //   var yx=minx*10;

            //    if(minmaolilv>-Math.ceil(yx)*10)
            //   option.yAxis[1].min=-Math.ceil(yx)*10;

            //   }
            //   else
            //   {
            // option.yAxis[0].splitNumber=10;
            // option.yAxis[1].splitNumber=10;
            // option.yAxis[1].min=0;
            // option.yAxis[1].max=100;

            //   }



            option && myChart.setOption(option);
            let that = this;
        },

        Getoptions(element) {



            var series = []
            element.series.forEach(s => {
                series.push({ smooth: true, ...s })
            })
            var selectedLegend = {};
            if (element.selectedLegend) {
                const processLegend = (legend) => {
                    legend.forEach(f => {
                        if (!element.selectedLegend.includes(f)) selectedLegend[f] = false;
                    });
                };

                if (element.legend.data) {
                    processLegend(element.legend.data);
                } else {
                    processLegend(element.legend);
                }
            }
            if (element && element.legend && element.legend.data) {
                var legends = element.legend.data;
            } else {
                var legends = element.legend;
            }
            var yAxis = []
            element.yAxis.forEach(s => {
                yAxis.push({ type: 'value', minInterval: 10, offset: s.offset, splitLine: s.splitLine, position: s.position, name: s.name, axisLabel: { formatter: '{value}' + s.unit } })
            })
            var option = {
                title: { text: element.title },
                tooltip: { trigger: 'axis' },
                legend: {
                    padding: [
                        30,  // 上
                        10, // 右
                        50,  // 下
                        10, // 左
                    ],
                    height: '100px',
                    selected: selectedLegend,
                    data: legends,
                    //  selected:{
                    //           // 选中'系列1'
                    //          '付款金额': false,
                    //           // 不选中'系列2'
                    //           '广告费': false,
                    //           '毛利3': true,
                    //           '订单量': false,
                    //           '销售总额': false,
                    //           '快递费': false,
                    //           '毛利率(%)': false,
                    //           '毛利2': false,
                    //           '净利': false,
                    //  },
                    //  data: element.legend
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '13%',
                    top: '25%',
                    containLabel: true
                },
                toolbox: {
                    feature: {
                        magicType: { show: true, type: ['line', 'bar'] },
                        //restore: {show: true},
                    }
                },
                xAxis: {
                    type: 'category',
                    data: element.xAxis
                },
                yAxis: yAxis,
                series: series
            };
            return option;
        },
    }
}
</script>
<style scoped lang="scss">
.el-select-content {
    width: calc(100% - 10px);
    margin: 0;
}
</style>