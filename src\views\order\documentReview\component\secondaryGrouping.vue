<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="dayItem" type="date" placeholder="选择日期" :picker-options="pickerOptions"
                    class="publicCss" @change="changeTime" :clearable="false" />
                <el-input v-model="ListInfo.seriesName" placeholder="系列编码" class="publicCss" clearable maxlength="50" />
                <el-input v-model="ListInfo.sku" placeholder="SKUS" class="publicCss" clearable maxlength="50" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
            </div>
        </template>
        <vxetablebase :id="'secondaryGrouping202408041754'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;margin: 0;height: 100%;" v-loading="loading" :showsummary='true'
            :summaryarry='summaryarry' />
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
        <!-- 仓位信息 -->
        <el-dialog title="仓位信息" :visible.sync="distributionVisible" width="40%" v-dialogDrag>
            <positionInfo ref="positionInfo" v-if="distributionVisible" :jsonParse="jsonParse" />
        </el-dialog>
        <!-- 订单日志信息 -->
        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            v-dialogDrag>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNoInner="orderNoInner"
                style="z-index:10000;height:600px" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import positionInfo from './positionInfo'
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
import { getSecMbzReleaseRecord } from '@/api/vo/VerifyOrder'
import dayjs from 'dayjs'
const tableCols = [
    { istrue: true, prop: 'sku', label: 'SKUS', sortable: 'custom', align: 'left', width: ' 80' },
    { istrue: true, prop: 'seriesName', label: '系列编码', sortable: 'custom', width: '250', align: 'left' },
    { istrue: true, prop: 'orderNoInner', label: '内部订单号', sortable: 'custom', width: 'auto', type: 'click', handle: (that, row) => that.showLogDetail(row) },
    { istrue: true, prop: 'timePay', label: '付款日期', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'inSecMobilizeTime', label: '二次组团时间', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'syncRpaTime', label: '二次放单时间', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'releaseNo', label: '二次放单批次', align: 'center', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'secMbzSetting', label: '规则', align: 'left', sortable: 'custom', width: 'auto' },
    { istrue: true, type: 'button', align: 'center', width: 'auto', label: '仓位', btnList: [{ label: "查看", handle: (that, row) => that.showPositionDetail(row) }] }
]

export default {
    name: "secondaryGrouping",
    components: {
        MyContainer, vxetablebase, positionInfo, OrderActionsByInnerNos
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                endDate: null,//结束日期
                startDate: null,//开始日期
                seriesName: null,//系列编码
                sku: null,//SKUS
            },
            jsonParse: [],
            dayItem: null,//日期
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            distributionVisible: false,//仓位信息
            dialogHisVisible: false,//订单日志信息
            orderNoInner: null,
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                },
                shortcuts: [{
                    text: '今天',
                    onClick(picker) {
                        picker.$emit('pick', new Date());
                    }
                }, {
                    text: '昨天',
                    onClick(picker) {
                        const date = new Date();
                        date.setTime(date.getTime() - 3600 * 1000 * 24);
                        picker.$emit('pick', date);
                    }
                }, {
                    text: '一周前',
                    onClick(picker) {
                        const date = new Date();
                        date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', date);
                    }
                }]
            },
            summaryarry: {}
        }
    },
    async mounted() {
        this.init()
        await this.getList()
    },
    methods: {
        showLogDetail(row) {
            this.orderNoInner = row.orderNoInner
            this.dialogHisVisible = true;
        },
        showPositionDetail(row) {
            this.jsonParse = []
            this.jsonParse = JSON.parse(row.thoroughfare);
            this.distributionVisible = true
        },
        changeTime(e) {
            this.dayItem = e ? dayjs(e).format('YYYY-MM-DD') : null
            this.getList()
        },
        init() {
            // dayItem默认给今天
            this.dayItem = !this.dayItem ? dayjs().subtract(0, 'day').format('YYYY-MM-DD') : this.dayItem
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
            }
            this.ListInfo.startDate = this.dayItem ? dayjs(this.dayItem).format('YYYY-MM-DD') : null
            this.ListInfo.endDate = this.dayItem ? dayjs(this.dayItem).format('YYYY-MM-DD') : null
            const replaceArr = ['seriesName', 'sku'] //替换空格的方法,该数组对应str类型的input双向绑定的值
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data, success } = await getSecMbzReleaseRecord(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>