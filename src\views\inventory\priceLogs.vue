<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-date-picker style="width: 220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :clearable="false" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                    <el-tooltip class="item" effect="dark" content="发起时间" placement="top-start">
                        <i class="el-icon-question" style="margin-right: 10px;"></i>
                    </el-tooltip>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-input v-model.trim="filter.businessId" type="text" maxlength="50" clearable placeholder="审批编号"
                        style="width:180px;" />
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-tooltip class="item" effect="dark" content="默认为精确匹配在开始或者结尾输入*进行模糊匹配" placement="top">
                        <inputYunhan :key="'3'" :keys="'three'" :width="'180px'" ref="childGoodsCode"
                            :inputt.sync="filter.goodsCode" v-model="filter.goodsCode" placeholder="商品编码"
                            :clearable="true" @callback="callbackGoodsCode" title="商品编码"></inputYunhan>
                    </el-tooltip>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-input v-model.trim="filter.dingUserName" type="text" maxlength="20" clearable placeholder="发起人"
                        style="width:180px;" />
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-input v-model.trim="filter.department" type="text" maxlength="50" clearable placeholder="所在部门"
                        style="width:180px;" />
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select filterable v-model="filter.status" collapse-tags clearable placeholder="状态"
                        style="width: 150px">
                        <el-option key="审批中" label="审批中" value="审批中"></el-option>
                        <el-option key="已完成" label="已完成" value="已完成"></el-option>
                        <el-option key="已拒绝" label="已拒绝" value="已拒绝"></el-option>
                        <el-option key="已撤销" label="已撤销" value="已撤销"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport">导出商品明细</el-button>
                </el-button>
            </el-button-group>
        </template>
        <vxetablebase :id="'YunHanAdminGoodschangeapprovelog20230804'" :isIndex='true' :tableHandles='tableHandles'
            :tableData='list' :tableCols='tableCols' :tablefixed='true' :loading='listLoading' :border='true'
            :that="that" ref="vxetable" @sortchange='sortchange' @toggleTreeMethod="toggleTreeMethod"
            :isIndexFixed="false" :tablekey="'YunHanAdminGoodschangeapprovelog20230804'" height="100%">
            <template slot="left">
                <vxe-column type="expand" width="60">
                    <template #content="{ row }">
                        <div>
                            <el-row>
                                <el-col :span="10">
                                    <vxe-table border show-header-overflow show-overflow align="center"
                                        :row-config="{ isHover: true }" :data="detailData.goodsDetail">
                                        <vxe-column type="seq" title="序号" width="6x0"></vxe-column>
                                        <vxe-column field="goodsCode" title="商品编码"></vxe-column>
                                    </vxe-table>
                                </el-col>
                                <el-col :span="14">
                                    <vxe-table border show-header-overflow show-overflow align="center"
                                        :row-config="{ isHover: true }" :data="detailData.logDetail">
                                        <vxe-column type="seq" title="序号" width="6x0"></vxe-column>
                                        <vxe-column field="approveName" title="审批人"></vxe-column>
                                        <vxe-column field="approveTime" title="审批时间"></vxe-column>
                                        <vxe-column field="result" title="操作结果"></vxe-column>
                                    </vxe-table>
                                </el-col>
                            </el-row>

                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

    </container>
</template>

<script>

import { Loading } from 'element-ui';
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import inputYunhan from "@/components/Comm/inputYunhan";
import { getGoodsChangeApproveLogAsync, getGoodsChangeApproveLogDetailAsync, exportGoodsChangeApproveLog } from '@/api/inventory/goodscostpricechg'


const tableCols = [
    { istrue: true, treeNode: true, prop: 'businessId', label: '审批编号', width: '180', align: 'center', sortable: 'custom', },
    { istrue: true, prop: 'dingUserName', label: '发起人', width: '130', },
    { istrue: true, prop: 'department', label: '所在部门', width: '260', },
    { istrue: true, prop: 'reason_department', label: '降价部门', width: '220', sortable: 'custom', },
    { istrue: true, prop: 'supplier_name', label: '降价供应商', width: '220', },
    { istrue: true, prop: 'pic', label: '图片', type: 'imagess', width: '220', },
    { istrue: true, prop: 'status', label: '状态', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'createdTime', label: '发起时间', width: 'auto', sortable: 'custom', },
];
const tableHandles = [
];

const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'priceLogs',
    components: { container, MyConfirmButton, vxetablebase, inputYunhan },

    data() {
        return {
            that: this,
            filter: {
                startTime: null,
                endTime: null,
                goodsCode: null,
                businessId: null,
                status: null,
                department: null,
                dingUserName: null,
                timerange: [startTime, endTime],
            },
            tableData: [],
            list: [],
            detailData: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "createdTime", IsAsc: false },
            total: 0,
            sels: [],
            pickerOptions: {
                shortcuts: [{
                    text: '近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近半个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            listLoading: false,
            pageLoading: false,
        };
    },

    async mounted() {
        await this.onSearch();
    },

    methods: {
        async onExport() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            this.listLoading = true
            const res = await exportGoodsChangeApproveLog(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '核价日志-商品明细' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await getGoodsChangeApproveLogAsync(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            this.list = res.data.list
        },
        async callbackGoodsCode(val) {
            this.filter.goodsCode = val;
        },
        async toggleTreeMethod(row) {
            if (!row)
                return;
            this.listLoading = true;
            var para = { businessId: row.businessId };
            var res = await getGoodsChangeApproveLogDetailAsync(para);
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            this.detailData = res.data
            return;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
    },
};
</script>

<style lang="scss" scoped></style>
