<template>
    <my-container v-loading="pageLoading">
       <el-form  style = "height: 400px;">
            <el-row style = "height: 250px;">
                <el-col :span="2">
                    &nbsp;
                </el-col>
                <!--     <el-col :span="10" >
                        <el-row>
                            <el-form-item label="图片"> 
                            </el-form-item>
                            <el-form-item label="">
                                <div> <el-button    @click="pldownfile(0)" :loading="pageLoading"> <img src="@/static/images/downflieicon.png" fit="contain"  
                                 
                                    style="width: 10px;height: 10px;  "/>&nbsp; &nbsp;一键下载</el-button></div>
                                <div class="linecontent">
                                    <div v-for="(item,index) in photoUpfiles" :key="index"  >
                                        <div class="linerow img-hover" >
                                            <img :src="item.url" @click="showImg(item,index)" style="height: 50px; width: 50px;"   />  
                                        </div> 
                                    </div>
                                </div>
                            </el-form-item>
                        </el-row>  
                    </el-col>  -->
                    <el-col :span="20" >
                        <el-row>
                            <el-form-item label="附件">
                            </el-form-item>
                            <el-form-item label="">
                                <div> <el-button   @click="pldownfile(1)" :loading="pageLoading"> <img src="@/static/images/downflieicon.png" fit="contain"  
                                   
                                     style="width: 10px;height: 10px;  "/>&nbsp; &nbsp;一键下载</el-button></div>
                                <p></p>
                                <div> 
                                    <div v-for="(i,index) in pxeclUpfiles" :key="index" >
                                        <div class="flexrow">
                                            <el-tooltip class="item" effect="dark" :content="i.fileName" placement="top"> 
                                                <div class="outline">{{ i.fileName }}</div>
                                            </el-tooltip>  
                                        </div> 
                                    </div> 
                                </div>
                            </el-form-item>
                        </el-row>  
                    </el-col> 
                    <el-col :span="2">
                        &nbsp;
                    </el-col>
            </el-row>
            <el-row style = "height: 150px;">
                <el-col :span="2">
                    &nbsp;
                </el-col>
                <el-col :span="20">
                    <el-form-item label="备注:" prop="remark">
                        <el-input type="textarea" :value="remark" :rows="6" :disabled="true"  placeholder="请输入备注" />
                    </el-form-item>
                </el-col>
                <el-col :span="2">
                    &nbsp;
                </el-col>
            </el-row>
       </el-form>
       <el-image-viewer v-if="showGoodsImage" :url-list="imgList"   :initialIndex="imgindex"  :on-close="closeFunc" style="z-index:9999;" />
   </my-container>
</template>
<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer';
import MyContainer from "@/components/my-container";
import { getShootingTaskFliesAsync} from '@/api/media/microvedio';
function pause(msec) {
    return new Promise(
        (resolve, reject) => {
            setTimeout(resolve, msec || 500);
        }
    );
}
export default {
   components: { MyContainer,ElImageViewer},
   props:["rowinfo"],
   data() {
       return {
           photoUpfiles:[],
           pxeclUpfiles:[],
           pageLoading:false,
           showGoodsImage: false,
           imgindex:0,
           remark:null
       };
   },
  
   async mounted() {
     await   this.onload();
   }, 
   methods: {
        async onload() {
           //获取拍摄上传的附件
            this.pageLoading= true;
            var res  =  await getShootingTaskFliesAsync({taskid:this.rowinfo});
            this.photoUpfiles=[];
            this.pxeclUpfiles=[];
            this.remark=null;
            if(res?.success){
                res.data.data.forEach(element => {
                    if(element.upLoadType ==1){
                        this.photoUpfiles.push(element);
                    }
                    if(element.upLoadType == 2){
                        this.pxeclUpfiles.push(element);
                    }
                }); 
                this.remark=res.data.remark;
            }
            this.pageLoading= false;
        },
 
          //完成表单界面关闭图片
        async closeFunc() {
            this.showGoodsImage = false;
        },
        async showImg(i,j) {
            this.imgList = [];
            for(let num in this.photoUpfiles)
            {  
                this.imgList.push(this.photoUpfiles[num].url); 
            }  
            this.imgindex=j;
            this.showGoodsImage = true;
        },
        //批量下载
        async pldownfile(index){
            this.pageLoading= true;
            if(index == 1){
                for(var num in this.pxeclUpfiles){ 
                    await this.downfile(this.pxeclUpfiles[num]);
                    await pause(500);
                }
            }else{
                for(var num in this.photoUpfiles){
                   await this.downfile(this.photoUpfiles[num]);
                   await pause(500);
                } 
            }
            this.pageLoading= false;
        },
        //下载文件
        async downfile(file)
        {
            var xhr = new XMLHttpRequest();
            xhr.open('GET', file.url, true);
            xhr.responseType = 'arraybuffer'; // 返回类型blob
            xhr.onload =await  function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    var blob = this.response; 
                    // 转换一个blob链接
                    // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，
                    // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定
                    var downLoadUrl = window.URL.createObjectURL(new Blob([blob], {type: 'image/jpeg'}));
                    // 视频的type是video/mp4，图片是image/jpeg
                    // 01.创建a标签
                    var a = document.createElement('a');
                    // 02.给a标签的属性download设定名称
                    a.download = file.fileName;
                    // 03.设置下载的文件名
                    a.href = downLoadUrl;
                    // 04.对a标签做一个隐藏处理
                    a.style.display = 'none';
                    // 05.向文档中添加a标签
                    document.body.appendChild(a);
                    // 06.启动点击事件
                    a.click();
                    // 07.下载完毕删除此标签
                    a.remove();
                };
            };
            await  xhr.send();
        },
    },
};
</script>
<style  lang="scss" scoped> 
.linecontent{
//   background: rgb(238, 236, 236);
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    overflow: hidden;
    overflow-x: auto;
    white-space: nowrap;
    margin-top: 10px;
}
.linerow{
    display: inline-block;
//   background: #fff;
    margin: 5px;
//   border: 1px solid rgba(219, 217, 217, 0.781);
//   width: 100px;
//   height: 100px;
}
.img-hover {
    position: relative;
}

.img-hover:hover {
    cursor: pointer;
    .close-img,
    .close-img-dask {
        display: block;
    }
}

.close-img {
    display: none;
    position: absolute;
    right: 0px;
    top: 0px;
    color: rgb(255, 0, 0);
    font-size: 18px;
    z-index: 99999;
}
.flexrow{
    width: 220px;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    color: rgb(50,50,50);
}
.rightflex{
    // width: 100%;
    // background-color: red;
    margin-right: 10px;
    display: flex;
    justify-content: flex-end;
    margin-left: auto;
}
.outline{
    width: 300px;
    line-height: 20px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
}
</style>