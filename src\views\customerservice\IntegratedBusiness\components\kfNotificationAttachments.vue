<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="addTimeRanges" type="daterange" unlink-panels range-separator="至"
                    :clearable="false" start-placeholder="附件生成日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 270px;margin-right: 10px;" :value-format="'yyyy-MM-dd'"
                    @change="changeTime($event, 'add')">
                </el-date-picker>
                <el-select v-model="ListInfo.expressCompanyNameList" placeholder="快递公司"
                    style="width: 250px;margin-right: 5px;" clearable collapse-tags multiple filterable>
                    <el-option v-for="item in kdCompany" :key="item" :label="item" :value="item" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import {
    GetToolkitExpressClaimNoticeLog
} from '@/api/customerservice/toolkitExpressClaimOrder'
import { GetExpressCompanyNameList, } from '@/api/customerservice/expressIntercept'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '附件生成时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressCompanyName', label: '快递公司', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'workStartTime', label: '工作时间', formatter: (row) => (row.workStartTime ? dayjs(row.workStartTime).format('HH:mm') : '') + '-' + (row.workEndTime ? dayjs(row.workEndTime).format('HH:mm') : '') },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'noticeIntervalTime', label: '通知间隔', formatter: (row) => row.noticeIntervalTime ? dayjs(row.noticeIntervalTime).format('HH:mm') : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'noticeTime', label: '通知时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'operateTime', label: '操作时间', },
    {
        istrue: true, label: '操作', width: '80', type: 'button', btnList:
            [
                { istrue: true, label: '下载附件', handle: (that, row) => that.downLoadProps(row) },
            ]
    },
]
const options = [
    { value: 4, label: '虚假签收' },
    { value: 3, label: '催件' },
    { value: 2, label: '丢件' },
    { value: 1, label: '破损' },
    { value: 0, label: '改地址' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            options,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startAddTime: null,//开始时间
                endAddTime: null,//结束时间
                expressCompanyNameList: [],//快递公司名称
            },
            addTimeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            kdCompany: []
        }
    },
    async mounted() {
        await this.getKdCompany()
        await this.getList()
    },
    methods: {
        async changeTime(e, type) {
            if (type == 'add') {
                this.ListInfo.startAddTime = e ? e[0] : null
                this.ListInfo.endAddTime = e ? e[1] : null
            }
            await this.getList()
        },
        async getKdCompany() {
            const { data, success } = await GetExpressCompanyNameList()
            if (success) {
                this.kdCompany = data
            }
        },
        downLoadProps(row) {
            if (!row.noticeFileUrl) return this.$message.error('暂无文件')
            const aLink = document.createElement("a");
            aLink.style = 'display: none'; // 创建一个隐藏的a标签
            aLink.href = row.noticeFileUrl
            aLink.setAttribute('download', '异常数据' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
            document.body.removeChild(aLink);
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            if (this.addTimeRanges && this.addTimeRanges.length == 0) {
                //默认一个月时间
                this.ListInfo.startAddTime = dayjs().subtract(1, 'month').format('YYYY-MM-DD')
                this.ListInfo.endAddTime = dayjs().format('YYYY-MM-DD')
                this.addTimeRanges = [this.ListInfo.startAddTime, this.ListInfo.endAddTime]
            }
            // 使用时将下面的方法替换成自己的接口
            const { data: { list, total }, success } = await GetToolkitExpressClaimNoticeLog(this.ListInfo)
            if (success) {
                this.tableData = list
                this.total = total
                this.loading = false
            } else {
                //获取列表失败
                this.loading = false
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>