<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%">
            <el-tab-pane label="基础数据" name="first" style="height: 100%" lazy v-if="checkPermission('BasicDataPermission')">
                <basicData />
            </el-tab-pane>
            <el-tab-pane label="仓库指派" name="Eleven" style="height: 100%" lazy v-if="checkPermission('WarehouseAssignmentPermission')">
                <warehouseAssignment />
            </el-tab-pane>
            <el-tab-pane label="待审批数据" name="ninth" style="height: 100%" lazy v-if="checkPermission('DataAwaitingApprovalPermission')">
                <basicData_c />
            </el-tab-pane>
            <el-tab-pane label="仓库统计" name="fifth" style="height: 100%" lazy v-if="checkPermission('WarehouseStatisticsPermission')">
                <shipmentStatistics />
            </el-tab-pane>
            <el-tab-pane label="仓库设置" name="second" style="height: 100%" lazy v-if="checkPermission('WarehouseSetupPermission')">
                <warehouseSettings />
            </el-tab-pane>
            <el-tab-pane label="仓库发货省" name="eighth" style="height: 100%" lazy v-if="checkPermission('WarehouseDeliveryProvincePermission')">
                <placeShipmentSettings />
            </el-tab-pane>
            <el-tab-pane label="编码统计" name="third" style="height: 100%" lazy v-if="checkPermission('CodingStatisticsPermission')">
                <encodingStatistics />
            </el-tab-pane>
            <el-tab-pane label="采购维度" name="forth" style="height: 100%" lazy v-if="checkPermission('ProcurementDimensionsPermission')">
                <procurementDimensions />
            </el-tab-pane>
            <el-tab-pane label="云调本库存统计" name="sixth" style="height: 100%" lazy v-if="checkPermission('CloudBasedInventoryStatisticsPermission')">
                <cloudbookInventoryStatistics />
            </el-tab-pane>
            <el-tab-pane label="调拨记录" name="seventh" style="height: 100%" lazy v-if="checkPermission('TransferRecordPermission')">
                <operationRecord />
            </el-tab-pane>
            <el-tab-pane label="编码长度" name="tenth" style="height: 100%" lazy v-if="checkPermission('EncodingLengthPermission')">
                <codelength />
            </el-tab-pane>
            <el-tab-pane label="包装材料" name="twelfth" style="height: 100%" lazy v-if="checkPermission('PackingMaterialPermission')">
                <packagingMaterials />
            </el-tab-pane>
            <el-tab-pane label="品牌数据" name="thirteenth" style="height: 100%" lazy v-if="checkPermission('brandPropsPermission')">
                <brandProps />
            </el-tab-pane>
            <el-tab-pane label="品牌包材" name="forteenth" style="height: 100%" lazy v-if="checkPermission('brandPackMtlPermission')">
                <brandPackMtl />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import basicData from "./components/basicData.vue";
import basicData_c from "./components/basicData_c.vue";
import warehouseSettings from "./components/warehouseSettings.vue";
import placeShipmentSettings from "./components/placeShipmentSettings.vue";
import encodingStatistics from "./components/encodingStatistics.vue";
import procurementDimensions from "./components/procurementDimensions.vue";
import shipmentStatistics from "./components/shipmentStatistics.vue";
import cloudbookInventoryStatistics from "./components/cloudbookInventoryStatistics.vue";
import operationRecord from "./components/operationRecord.vue";
import codelength from './components/codelength.vue';
import warehouseAssignment from './components/warehouseAssignment.vue';
import packagingMaterials from './components/packagingMaterials.vue';
import brandProps from './components/brandProps.vue';
import brandPackMtl from './components/brandPackMtl.vue';
export default {
    components: {
        MyContainer,
        basicData,
        warehouseSettings,
        encodingStatistics,
        procurementDimensions,
        shipmentStatistics,
        cloudbookInventoryStatistics,
        operationRecord,
        placeShipmentSettings,
        basicData_c,
        warehouseAssignment,
        packagingMaterials,
        codelength,
        brandProps,
        brandPackMtl,
    },
    data() {
        return {
            activeName: 'first',
            tabConfigs: [
              { name: 'first', permission: 'BasicDataPermission' },
              { name: 'Eleven', permission: 'WarehouseAssignmentPermission' },
              { name: 'ninth', permission: 'DataAwaitingApprovalPermission' },
              { name: 'fifth', permission: 'WarehouseStatisticsPermission' },
              { name: 'second', permission: 'WarehouseSetupPermission' },
              { name: 'eighth', permission: 'WarehouseDeliveryProvincePermission' },
              { name: 'third', permission: 'CodingStatisticsPermission' },
              { name: 'forth', permission: 'ProcurementDimensionsPermission' },
              { name: 'sixth', permission: 'CloudBasedInventoryStatisticsPermission' },
              { name: 'seventh', permission: 'TransferRecordPermission' },
              { name: 'tenth', permission: 'EncodingLengthPermission' },
              { name: 'twelfth', permission: 'PackingMaterialPermission' },
            ],
        };
    },
    created() {
    },
    mounted() {
      this.setDefaultTab();
    },
    methods: {
      setDefaultTab() {
        for (const tab of this.tabConfigs) {
          if (this.checkPermission(tab.permission)) {
            this.activeName = tab.name;
            break;
          }
        }
      }
    },
};
</script>

<style lang="scss" scoped></style>
