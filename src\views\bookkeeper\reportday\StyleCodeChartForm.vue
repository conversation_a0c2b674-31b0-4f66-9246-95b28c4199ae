<template>
     <my-container v-loading="pageLoading">
        <el-row>
            <el-col :span="24">   
                <el-button-group>   
                    <strong style="float: left;">销售日期</strong>
                    <el-date-picker style="width:220px;margin-left:10px;float: left;"   @change="onSearch" 
                    v-model="Filter.gDate" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" :clearable="false" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions">
                    </el-date-picker>
                         
                    <el-checkbox-group  style="float: left;" v-model="Filter.dataGroupNames" @change="onSearch" >
                        <el-checkbox v-if="Filter.groupName" label="按人" ></el-checkbox>
                        <el-checkbox v-if="!Filter.groupName" label="按组" ></el-checkbox>
                        <el-checkbox  label="按天" ></el-checkbox>
                    </el-checkbox-group>
                </el-button-group>
                <!-- <el-button type="primary" @click="onSearch">查询</el-button> -->
            </el-col>
        </el-row>
        <el-row style="width:100%;">
            <el-col :span="24" style="width:100%;margin-top:20px;">
                <div style="min-width:1024px;height:400px;width:100%;" :ref="'echarts1'" :id="'rptIdecharts1'" ></div>
            </el-col>
        </el-row>

        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">  
                    <el-button @click="onClose">关闭</el-button>   
                </el-col>
            </el-row>
        </template>
    </my-container>
</template>

<script>
    import * as echarts from 'echarts'
    import MyContainer from "@/components/my-container";
    import {
        platformlist, pickerOptions, formatTime
    } from "@/utils/tools";
    import dayjs from "dayjs";
    
    import { DayReportStyleCodeAnalysisPdd } from '@/api/bookkeeper/reportday'

    export default {
        name: "StyleCodeChartForm",
        components: { MyContainer,   },
        data() {
            return {  
                Filter:{
                    gDate:[],
                    startTime:null,
                    endTime:null,
                    groupName:"",
                    dataGroupNames:[]
                },
                that: this,
                pickerOptions:pickerOptions,
                pageLoading:false,
                myChart1:{},
            };
        },
        async mounted() {

        },
        computed: {
            CalcGroupName(){
                if(this.Filter.groupName)
                    return "-"+this.Filter.groupName;
                else
                    return "";
            }
        },
        methods: {  
            onClose(){
                this.$emit('close');
            },             
            async loadData({startTime,endTime,groupName}) {
                this.Filter.startTime=startTime;
                this.Filter.endTime=endTime;
                this.Filter.groupName=groupName;
                this.Filter.gDate=[startTime,endTime];
                if(groupName){
                    this.Filter.dataGroupNames.push("按人");
                }else{
                    this.Filter.dataGroupNames.push("按组");
                }
                await this.onSearch();
            },
            async onSearch(){
                let self = this;
                this.pageLoading=true;
                
                this.Filter.startTime = this.Filter.gDate[0];
                this.Filter.endTime = this.Filter.gDate[1];

                let rlt = await DayReportStyleCodeAnalysisPdd({ ...this.Filter });
                if (rlt && rlt.success) {
                    let opt1 ={...rlt.data};
                    opt1.xAxis= {
                        type: 'category',
                        data:  rlt.data.xAxis
                    };
                    opt1.legend={
                        selected: rlt.data.selectedLegend,
                        data: rlt.data.legend
                    };
                    opt1.series.forEach(x=>{
                        x.smooth=true
                    });
                    opt1.tooltip= {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            crossStyle: {
                                color: '#999'
                            }
                        }
                    };
                  

                    //检测是否已经存在echarts实例，如果不存在，则不再去初始化
                    let myChart = echarts.getInstanceByDom(
                        this.$refs['echarts1']
                    );
                    if (myChart == null) {
                        myChart = echarts.init(this.$refs['echarts1']);

                        //小组不再有穿透事件
                        if(!this.Filter.groupName){
                            myChart.on('selectchanged',function(v){    
                                //console.log('s1',self.Filter.dataGroupNames)
                                //仅为小组时，点击才弹出 小组
                                if(self.Filter.dataGroupNames && self.Filter.dataGroupNames.length==1 && self.Filter.dataGroupNames[0]=="按组")                                
                                    self.groupDtlReload(self.chart1Opt.xAxis.data[v.fromActionPayload.dataIndexInside]);                                

                            });
                        }                       
                      
                    }

                    self.myChart1=myChart;                 
              
                 
                    self.chart1Opt=opt1;

                    myChart.setOption(self.chart1Opt,true);
                }

                this.pageLoading=false;
            },
            groupDtlReload(v){
             
                let self=this;
                this.$showDialogform({
                    path: `@/views/bookkeeper/reportday/StyleCodeChartForm.vue`,
                    title: '系列编码统计-'+v,
                    autoTitle: false,
                    args: {  mode: 3,
                        startTime:self.Filter.startTime,
                        endTime:self.Filter.endTime,
                        groupName:v,
                    },
                    height: 400,
                    width: '80%',
                    //callOk: self.onSearch
                });

              },
        },
    };
</script>

