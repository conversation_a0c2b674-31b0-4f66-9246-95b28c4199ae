<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="功能权限" name="tab0" style="height: 100%;">
                <purchrasedeptrolepermission ref="purchrasedeptrolepermission" style="height: 100%;" />
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";

import purchrasedeptrolepermission from '@/views/inventory/purchrasedeptpermission/purchrasedeptrolepermission';

export default {
    name: "purchrasedeptpermissionIndex",
    components: {
        MyContainer,
        purchrasedeptrolepermission,
    },
    data() {
        return {
            that: this,
            pageLoading: '',
            activeName: 'tab0',
        };
    },
    mounted() {
    },
    methods: {
    },

};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
