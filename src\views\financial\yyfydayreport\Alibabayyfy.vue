<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <el-tabs v-model="activeName" style="height:94%;">

      <el-tab-pane   label="营效宝" name="tab1" style="height: 100%;">
          <YingXiaobao :filter="Filter" ref="YingXiaobao" style="height: 100%;"/>
      </el-tab-pane>

       <el-tab-pane    label="展效宝" name="tab2" style="height: 100%;">
          <ZhanXiaobao :filter="Filter" ref="ZhanXiaobao" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane    label="首位展示" name="tab3" style="height: 100%;">
        <FirstDisplay :filter="Filter" ref="FirstDisplay" style="height: 100%;"/>
    </el-tab-pane>
    <el-tab-pane    label="智能营销方案" name="tab4" style="height: 100%;">
      <Intelligentmarketingprogram :filter="Filter" ref="Intelligentmarketingprogram" style="height: 100%;"/>
    </el-tab-pane>
    <el-tab-pane    label="全站投放" name="tab5" style="height: 100%;">
      <AllStationDelivery :filter="Filter" ref="AllStationDelivery" style="height: 100%;"/>
    </el-tab-pane>
    <el-tab-pane label="严选订单处理" name="tab6" style="height: 100%;">
      <strictOrderProcessing ref="refstrictOrderProcessing" style="height: 100%;"/>
    </el-tab-pane>


    </el-tabs>
    </my-container >

   </template>
  <script>
  import MyContainer from "@/components/my-container";
   import ZhanXiaobao from '@/views/financial/yyfydayreport/ZhanXiaobao'
   import YingXiaobao from '@/views/financial/yyfydayreport/YingXiaobao'
   import FirstDisplay from '@/views/financial/yyfydayreport/FirstDisplay'
   import strictOrderProcessing from '@/views/financial/yyfydayreport/strictOrderProcessing'

   import Intelligentmarketingprogram from '@/views/financial/yyfydayreport/Intelligentmarketingprogram'
   import AllStationDelivery from '@/views/financial/yyfydayreport/AllStationDelivery'
   import checkPermission from '@/utils/permission'
  export default {
    name: "Users",
    components: { MyContainer,YingXiaobao,ZhanXiaobao,checkPermission,FirstDisplay,Intelligentmarketingprogram,AllStationDelivery,strictOrderProcessing},
    data() {
      return {
        that:this,
        Filter: {
        },
        pageLoading:"",
        activeName:"tab1",
        shopList:[],
        userList:[],
        groupList:[],
        selids:[],
        dialogVisibleSyj:false,
        fileList:[],
      };
    },
    mounted() {
    },
    methods: {
  async onSearch(){
    if (this.activeName=='tab1')
    this.$refs.YingXiaobao.onSearch();
    if (this.activeName=='tab2')
  this.$refs.ZhanXiaobao.onSearch();
  if (this.activeName=='tab3')
  this.$refs.FirstDisplay.onSearch();
  if (this.activeName=='tab4')
  this.$refs.Intelligentmarketingprogram.onSearch();
  if (this.activeName=='tab5')
  this.$refs.AllStationDelivery.onSearch();
  }
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
