<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始时间"
          end-placeholder="结束时间" :picker-options="pickerOptions" style="width: 300px;margin-right: 10px;"
          :value-format="'yyyy-MM-dd'">
        </el-date-picker>

        <el-input v-model="ListInfo.shopCode" placeholder="店铺名称" maxlength="50" clearable class="publicCss" />

        <el-select v-model="ListInfo.documentType" placeholder="单据类型" class="publicCss" clearable filterable>
          <el-option v-for="item in documentTypeList" :key="item" :label="item" :value="item" />
        </el-select>

        <el-input v-model="ListInfo.skc" placeholder="SKC" maxlength="50" clearable class="publicCss" />
        <el-input v-model="ListInfo.sku" placeholder="SKU" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>

        <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button @click="startImport"
          type="primary" icon="el-icon-share" @command="handleCommand"> 导入
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">下载模版</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="primary" @click="onExport" style="margin-left: 5px;" v-if="checkPermission('sheinBan_SelfOperatedInventoryLossDetails_export')">导出</el-button>
      </div>
    </template>
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" v-loading="loading"
      :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="45%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 100px;">
        <el-date-picker style="width: 200px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
          placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
        </el-date-picker>

        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action=""
          accept=".xlsx" :file-list="fileList" :http-request="onUploadFile" :on-success="onUploadSuccess"
          :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary" style="width: 90px;">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px;width: 90px;" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import { formatTime } from "@/utils/tools";
import { pageSheInInvertoryLossResult, importInventoryLoss_SheInSelf, exportInventorySheInSelf } from '@/api/bookkeeper/crossBorderV2'
import { concat } from "lodash";

const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopCode', label: '店铺ID', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: '150', align: 'center', prop: 'yearMonthDayDateTime', label: '日期', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'documentNumber', label: '单据号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'documentType', label: '单据类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'returnStatus', label: '退货状态', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'returnType', label: '退货/报废类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'returnMethod', label: '退货方式', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'returnWarehouse', label: '退货仓库', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '订单', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'fulfillmentOrderNo', label: '发货单', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'sourceNo', label: '来源单', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'skc', label: 'SKC', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'pendingReturnTotal', label: '待退总数', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'actualReturnTotal', label: '实退总数', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'scrapAmount', label: '报废数量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '创建时间', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'sku', label: 'SKU', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'returnDetail', label: '实退明细', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'lossCost', label: '单个成本', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'lossCostTotal', label: '总成本', },
]

var documentTypeList = ["退货", "报废"]

export default {
  name: "SelfOperatedInventoryLossDetails",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      summaryarry: {},
      dialogVisible: false,//导入弹窗
      uploadLoading: false,//上传loading
      fileList: [],//上传文件列表
      yearMonthDay: null,//导入日期
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        dataStartDate: null,//开始时间
        dataEndDate: null,//结束时间
        sku: null,//SKU
        skc: null,//SKC
        documentType: "",//单据类型
        shopCode: ""//店铺名称
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
      documentTypeList: documentTypeList,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearMonthDay", this.yearMonthDay);
      var res = await importInventoryLoss_SheInSelf(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.yearMonthDay = null;
      this.uploadLoading = false;
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (!this.yearMonthDay) {
        this.$message({ message: "请选择日期", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    // async changeTime(e) {
    //   this.ListInfo.dataStartDate = e ? e[0] : null
    //   this.ListInfo.dataEndDate = e ? e[1] : null
    //   await this.getList()
    // },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
      }
      const replaceArr = ['documentType', 'shopCode', 'sku', 'skc'] //替换空格的方法,该数组对应str类型的input双向绑定的值
      this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
      if (this.timeRanges) {
        this.ListInfo.dataStartDate = this.timeRanges[0]
        this.ListInfo.dataEndDate = this.timeRanges[1]
      } else {
        this.ListInfo.dataStartDate = null;
        this.ListInfo.dataEndDate = null;
      }
      this.loading = true
      const { data, success } = await pageSheInInvertoryLossResult(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async handleCommand(command) {
      switch (command) {
        //下载模版
        case 'a':
          await this.downLoadFile()
          break;
      }
    },
    async downLoadFile() {
      window.open("/static/excel/KjBillCharges/希音自营库存损耗明细.xlsx", "_blank");
    },
    async onExport() {//导出列表数据；

      const replaceArr = ['documentType', 'shopCode', 'sku', 'skc'] //替换空格的方法,该数组对应str类型的input双向绑定的值
      this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
      var res = await exportInventorySheInSelf(this.ListInfo);
      console.log("希音自营库存损耗明细",res)
      if (res?.data.success) {
        this.$message({ message: res.data.msg, type: "success" });
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}
</style>