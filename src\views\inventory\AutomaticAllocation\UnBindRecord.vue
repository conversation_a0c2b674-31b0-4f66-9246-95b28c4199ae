<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template #header>
        <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent></el-form>
      </template>
      <!--列表-->
      <ces-table v-if="showtable" ref="table" :that="that" :isIndex="true" :hasexpand="false" @sortchange="sortchange"
        :tableData="pddcontributeinfolist" @select="selectchange" :isSelection="false" :tableCols="tableCols"
        :loading="listLoading" :summaryarry='summaryarry'>
        <el-table-column type="expand">
          <template slot-scope="props">
            <div>
              <el-table :data="props.row.detaildata" style="width: 100%">
                <el-table-column v-for="col in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <template slot="extentbtn">
          <el-button-group>
            <el-button style="padding: 0;">
             <el-date-picker style="width: 260px"
                v-model="Filter.Timerange"
                    type="daterange"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    range-separator="至"
                    start-placeholder="创建开始时间"
                    end-placeholder="创建结束时间"
                    :picker-options="pickerOptions">
             </el-date-picker>
           </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="Filter.GoodsCode" maxlength="50" clearable placeholder="编码" style="width:150px;" />
            </el-button>
            <el-button style="padding: 0;">
             <el-input v-model.trim="Filter.WarehouseBin" maxlength="50" clearable placeholder="仓位" style="width:150px;" />
            </el-button>
            <el-button style="padding: 0;">
                    <el-select v-model="Filter.UnState" clearable placeholder="状态" >
                        <el-option key="0" label="拒绝" value="0"></el-option>
                        <el-option key="1" label="发起" value="1"></el-option>
                        <el-option key="2" label="通过" value="2"></el-option>
                       
                    </el-select> 
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="Filter.Remark" maxlength="50" clearable placeholder="备注" style="width:150px;" />
            </el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-button-group>
        </template>
      </ces-table>
      <!--分页-->
      <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
      </template>
    </my-container>
  </template>
  <script>

  import dayjs from "dayjs";
  import cesTable from "@/components/Table/table.vue";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import { formatPlatform, formatLinkProCode, formatTime ,formatwarehouseIdList} from "@/utils/tools";
  import { getListByStyleCode } from "@/api/inventory/basicgoods"
  import { getUnBindRecord } from "@/api/inventory/allocate"
  import {getWarehouses} from "@/api/storehouse/storehouse";
    
  const startDate = formatTime(dayjs().subtract(6,'day'), "YYYY-MM-DD");
  const endDate = formatTime(new Date(), "YYYY-MM-DD");
  
  export default {
    name: "AfterSaleTx",
    components: {
      MyContainer,
      MyConfirmButton,
      MySearch,
      MySearchWindow,
      cesTable,
    },
    data() {
      return {
        onExportDialogVisible: false,
        importfilter: {
          version: ''
        },
        that: this,
        Filter: {
           GoodsCode: null,
           Timerange:null,
           StartAllocationDate: startDate,
           EndAllocationDate: endDate,
           Remark:null,
           WarehouseBin:null,
           UnState:null
        },
        shopList: [],
        styleCode: null,
        userList: [],
        groupList: [],
        pddcontributeinfolist: [],
        tableCols: this.gettableCols(),
        total: 0,
        summaryarry: {},
        pager: { OrderBy: "GoodsCode", IsAsc: false },
        sels: [], // 列表选中列
        listLoading: false,
        pageLoading: false,
        selids: [],
        fileList: [],
        dialogVisible: false,
        uploadLoading: false,
        showtable: true,
        Warehouseslist:[],
        pickerOptions: {
          disabledDate(time) {
          // 设置禁用最小日期为2022年1月1日
          const minDate = new Date(1970, 0, 1);
          return time.getTime() < minDate.getTime();
        },
        }
      };
    },
    async created() { 
    },  
    mounted() {
      this.Filter.Timerange = [startDate,endDate]
  },
    methods: {
      async getWarehousesList() {
      var res3 = await getWarehouses();
      this.Warehouseslist = res3.data?.map(item => { return { value: item.wms_co_id, label: item.name }; });
    },
      gettableCols() {
        return [
        { istrue: true,  prop: 'goodsCode', label: '编码', sortable: 'custom', width: '150' },
        { istrue: true,  prop: 'goodsCodeName',  label: '编码名称', width: '210',sortable: 'custom' },
        { istrue: true,  prop: 'brandName',  label: '采购', width: '110',sortable: 'custom' },
        { istrue: true,  prop: 'warehouseBin', label: '解绑仓位', sortable: 'custom', width: '150'},
        { istrue: true,  prop: 'name', label: '解绑仓库', sortable: 'custom', width: '200' },
        { istrue: true,  prop: 'createTime', label: '创建时间', sortable: 'custom', width: '200'},
        { istrue: true,  prop: 'completeTime', label: '完成时间', sortable: 'custom', width: '200'},
        { istrue: true,  prop: 'completedUserName', label: '完成人', sortable: 'custom', width: '120'},
        { istrue: true,  prop: 'unState', label: '状态', sortable: 'custom', width: '100' ,formatter:(row)=>row.unState=="0"?"拒绝":row.unState=="1"?"发起":row.unState=="2"?"通过":""},
        { istrue: true,  prop: 'remark', label: '备注', sortable: 'custom', width: '200', },
       ]
      },
      showClo(){
        return this.Filter.startTime==this.Filter.endTime;
      },
      changeSelectType() { 
        this.getList();
      },
      sortchange(column) {
        if (!column.order) this.pager = {};
        else
          this.pager = {
            OrderBy: column.prop,
            IsAsc: column.order.indexOf("descending") == -1 ? true : false,
          };
        this.onSearch();
      },
      onSearch() {
        this.$refs.pager.setPage(1);
        this.getList();
      },
      async getList() {
         if (this.Filter.Timerange && this.Filter.Timerange.length > 0) {
                this.Filter.StartAllocationDate = this.Filter.Timerange[0];
                this.Filter.EndAllocationDate = this.Filter.Timerange[1];
           }else
           {
                this.Filter.StartAllocationDate=null;
                this.Filter.EndAllocationDate=null
           }
        const para = { ...this.Filter };
        let pager = this.$refs.pager.getPager();
        const params = {
          ...pager,
          ...this.pager,
          ...para,
        };
        this.listLoading = true;
        const res = await getUnBindRecord(params);
        if (!res.success) {
          this.listLoading = false;
          return;
        }
        this.listLoading = false;
        this.total = res.data.total;
        this.pddcontributeinfolist = res.data.list;
        this.summaryarry = res.data.summary;
      },
      selectchange: function (rows, row) {
        this.selids = [];
        rows.forEach((f) => {
          this.selids.push(f.id);
        });
      },
      async onExport()
      {
        if (this.Filter.Timerange && this.Filter.Timerange.length > 0) {
                this.Filter.StartAllocationDate = this.Filter.Timerange[0];
                this.Filter.EndAllocationDate = this.Filter.Timerange[1];
           }else
           {
                this.Filter.StartAllocationDate=null;
                this.Filter.EndAllocationDate=null
           }
          var res = null;
          const para = { ...this.Filter };
          let pager = this.$refs.pager.getPager();
          const params = {
            ...pager,
            ...this.pager,
            ...para,
          };
          this.pageLoading = true;
          var res = await exportAllocateList(params);
          var fileName="自动调拨_仓位解绑记录_"
          this.pageLoading = false;
          const aLink = document.createElement("a");
          let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', fileName + new Date().toLocaleString() + '.xlsx')
          aLink.click()
      },
    },
  };
  
  
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
    
    