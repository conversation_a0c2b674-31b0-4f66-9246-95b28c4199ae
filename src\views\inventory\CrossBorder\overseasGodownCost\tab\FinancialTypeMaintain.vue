<template>
    <MyContainer>
        <template #header>
            <div class="top">

                <el-select @change="changeStore" v-model="queryInfo.houseName" value-key="sort" placeholder="仓库名称" style="width:120px; margin-left: 5px;"
                    class="el-select-content" clearable filterable>
                    <el-option v-for="item in DepotNameList" :key="item.sort" :label="item.codeName"
                        :value="item" />
                </el-select>
                <el-select v-model="queryInfo.houseFeeName" placeholder="费用类型" class="publicCss" clearable filterable multiple collapse-tags style="margin-left: 10px;">
                    <el-option v-for="item in CostList" :key="item.key" :label="item.value" :value="item.value" />
                </el-select>

                <el-select v-model="queryInfo.financeName" placeholder="财务类型" class="publicCss" clearable filterable multiple collapse-tags style="margin-left: 5px;"> 
                    <el-option v-for="item in financeFeeNameType" :key="item.key" :label="item.value" :value="item.value" />
                </el-select>

                
                <el-select v-model="queryInfo.dailyReportName" placeholder="日报类型" class="publicCss" clearable filterable multiple collapse-tags style="margin-left: 5px;"> 
                    <el-option v-for="item in dailyList" :key="item.key" :label="item.value" :value="item.value" />
                </el-select>
                <!-- <el-select v-model="queryInfo.dailyReportName" placeholder="日报类型" style="width:120px; margin-left: 10px;"
                    class="el-select-content" clearable filterable>
                    <el-option v-for="item in dailyList" :key="item.key" :label="item.value" :value="item.value" />
                </el-select> -->


                <el-button type="primary" style="margin-left: 10px;" @click="getList('search')">搜索</el-button>
                <el-button type="primary" style="margin-left: 10px;" @click="openVisible">新增财务类型</el-button>
                <!-- <el-button type="primary" style="margin-left: 10px;" @click="exportList">导出</el-button> -->
            </div>
        </template>

        <template>
            <vxetablebase :id="'FinancialTypeMaintain20241203'" :tablekey="'FinancialTypeMaintain20241203'"
                :tableData='tableData' :tableCols='tableCols' @sortchange='sortchange' :loading='loading' :border='true'
                :that="that" ref="vxetable" >
            </vxetablebase>
        </template>

        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getList" />
        </template>

        <!--新增财务类型 or 修改财务类型 or 日报类型 -->
        <el-dialog :visible.sync="visible" width="400px"   :title="editNoteForm.openType ==1 ? '财务类型':editNoteForm.openType ==2 ?'修改财务类型' :'修改日报类型'" center>
            <el-form ref="editNoteForm" :model="editNoteForm" >
                <el-form-item v-if="editNoteForm.openType == 1" label="" prop="add_finance_name">
                    <el-input
                    style="width: 200px;"
                    placeholder="请输入新增加财务类型"
                    v-model.trim="editNoteForm.add_finance_name"
                    maxlength="20"
                    >
                    </el-input>
                </el-form-item>

                <el-form-item v-else-if="editNoteForm.openType == 2" label="" prop="finance_name">
                    <el-select v-model="editNoteForm.finance_name" filterable  placeholder="财务类型" style="width:200px; "
                        class="el-select-content" clearable>
                        <el-option v-for="item in financeFeeNameType" :key="item.key+'dialog'" :label="item.value" :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item v-else label="" prop="dailyType">
                    <el-select v-model="editNoteForm.dailyType" filterable  placeholder="日报类型" style="width:200px; "
                        class="el-select-content" clearable>
                        <el-option v-for="item in dailyList" :key="item.key+'dialog'" :label="item.value" :value="item.value" />
                    </el-select>
                </el-form-item>

            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="visible = false">取 消</el-button>
                <el-button type="primary" :loading="saveLoading" @click="saveOperation()">保 存</el-button>
            </div>
        </el-dialog>
        <el-dialog title="操作日志" :visible.sync="logVisible" :center="true" width="60%"   v-dialogDrag >
            <viewLog :currentRow="currentRow"  ref="viewLog" type="维护"></viewLog>
        </el-dialog>

    </MyContainer>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import { getCostBillFinanceTypePageList, exportCombineList,getDailyReportFeeNames,getFeeNames,createCofingFinanceType,updateCostBillFinanceType } from '@/api/kj/cost.js';
import { getThirdPlatform } from '@/api/kj/system'
import dayjs from 'dayjs';
import viewLog from '../components/viewLog.vue'

const tableCols = [
    { istrue: true, sortable: 'custom', prop: 'houseName', label: '仓库名称' },
    { istrue: true, sortable: 'custom', prop: 'houseFeeName', label: '费用类型' },
    { istrue: true, sortable: 'custom', prop: 'financeName', label: '财务类型' ,type: 'clickLink',align:'center',
        style: (that, row) => { if (!row.financeName) return { color: '#409EFF', cursor: 'pointer' }  },
        handle: (that, row) => { if (!row.financeName) {that.editNoteForm.openType = 2; that.visible = true; that.editNoteForm.currentRow = row} },
        formatter: (row) => { if (!row.financeName) { return '未知类型' } }
    },
    { istrue: true, sortable: 'custom', prop: 'dailyReportName', label: '日报类型',type: 'clickLink',align:'center',
        style: (that, row) => { if (!row.dailyReportName) return { color: '#409EFF', cursor: 'pointer' }  },
        handle: (that, row) => { if (!row.dailyReportName) {that.editNoteForm.openType = 3; that.visible = true; that.editNoteForm.currentRow = row} },
        formatter: (row) => { if (!row.dailyReportName) { return '未知类型' } }
    },
    {type:'button',label:'操作',width:'150px',btnList:[
    {label:"查看日志", handle:(that,row)=>{that.currentRow = row;that.logVisible = true;that.$nextTick(()=>{that.$refs.viewLog.getList('search')})}}]}
];

export default {
    name: 'FinancialTypeMaintain',
    components: { vxetablebase, MyContainer,viewLog  },
    props: {
        financeFeeNameType:{
            type: Array,
            required: true
        }
    },
    data() {
        return {
            logVisible:false,
            that: this,
            currentRow:{},
            queryInfo: {
                financeName: null,
                houseName: {},
                houseFeeName:null,
                dailyReportName:null,
                page: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: null,
                endDate: null,
            },
            tableData: [],
            tableCols: tableCols,
            loading: false,
            total: 0,
            DepotNameList: [],
            selids:[],
            //费用类型列表
            CostList:[],
            //日报类型列表
            dailyList:[],

            //弹窗 新增财务类型 or 修改财务类型 or 修改日报类型
            editNoteForm:{
                openType:null,
            },
            visible:false,
            saveLoading:false,
        };
    },
    async mounted() {
        await this.getDropdownList()
        await this.getList()
    },
    methods: {
        async getDropdownList() {
            const thirdPlatform = await getThirdPlatform();
            this.DepotNameList = thirdPlatform.data;
            const DailyReportFeeNames = await getDailyReportFeeNames();
            this.dailyList = DailyReportFeeNames.data;
        },
        async getList(type) {
            if(type == 'search'){
                this.$refs.pager.setPage(1)
            }
            this.selids = []
            let page = this.$refs.pager.getPager()
            this.queryInfo.page = page.currentPage
            this.queryInfo.pageSize = page.pageSize
            let params = {
                ...this.queryInfo,
                houseName:this.queryInfo.houseName.codeName ? this.queryInfo.houseName.codeName : null,
            }
            this.loading = true
            const { data, total } = await getCostBillFinanceTypePageList(params)
            this.loading = false
            this.total = total
            this.tableData = data;

        },
        async exportList() {
            // this.loading = true
            // let page = this.$refs.pager.getPager()
            // this.queryInfo.page = page.currentPage
            // this.queryInfo.pageSize = page.pageSize
            // this.loading = true
      
            // const { data } = await exportCombineList(this.queryInfo)
            // this.loading = false
            // const aLink = document.createElement("a");
            // let blob = new Blob([data], { type: "application/vnd.ms-excel;charset=utf-8" })
            // aLink.href = URL.createObjectURL(blob)
            // aLink.setAttribute('download',  '-' + dayjs().format('YYYYMMDD') + '.xlsx')
            // aLink.click()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.queryInfo.orderBy = prop
                this.queryInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        
        //仓库联动 费用类型
        async changeStore(val) {
            //调用获取费用类型接口
            if (!!val.code) {
                const { data } = await getFeeNames({
                    thirdPlatform: val.code
                })
                this.CostList = data;
            }
        },

        //弹窗保存操作
        async saveOperation(){
            if(this.editNoteForm.openType == 1){
                if(this.editNoteForm.add_finance_name){
                     let params={
                        configKey:this.editNoteForm.add_finance_name,
                        configVal:this.editNoteForm.add_finance_name
                     }
                    this.saveLoading = true
                    let res =   await createCofingFinanceType(params)
                    this.saveLoading = false
                    this.visible = false
                    this.$refs.editNoteForm.resetFields()
                    this.$emit('getfinanceTypeList')
                    if(res.data.isSuccess){
                        this.$message({ message: res.data.message, type: "success" });
                    }else{
                        this.$message({ message: res.data.message, type: "error" });
                    }
                }else{
                    this.$message({ message: '请输入需要新增的财务类型', type: "warning" });
                }
                //新增财务类型接口
               
                // this.$confirm("确认要执行批量通过的操作吗?", "提示", {
                //     confirmButtonText: "确定",
                //     cancelButtonText: "取消",
                //     type: "warning",
                // }).then(async () => {

                //     const params = {
                //         // thirdPlatform: this.type,
                //         ids: this.selids
                //     }
                //     let res = await deleteCostBill(params);
                //     if (res?.isSuccess) {
                //         this.$message({ message: '已删除', type: "success" });
                //         this.getList();
                //     }
                //     else {
                //         this.$message({ message: '发生异常，请刷新后重试', type: "error" });
                //     }
                // });
            }else if(this.editNoteForm.openType == 2){
                //行内修改财务类型接口
                if(this.editNoteForm.finance_name){
                    let params = {
                        ...this.editNoteForm.currentRow,
                        financeName:this.editNoteForm.finance_name,
                        // houseFeeName:this.editNoteForm.currentRow.houseFeeName,
                        // houseName:this.editNoteForm.currentRow.houseName,
                    }
                    this.saveLoading = true
                    let res  =   await updateCostBillFinanceType(params)
                    this.saveLoading = false
                    this.visible = false
                    this.$refs.editNoteForm.resetFields()
                    this.getList('search')
                    if(res.isSuccess){
                        this.$message({ message: res.message, type: "success" });
                    }else{
                        this.$message({ message: res.message, type: "error" });
                    }
                }else{
                    this.$message({ message: '请选择需要修改的财务类型', type: "warning" });
                }
                
            }else {
                //行内修改日报类型接口
                if(this.editNoteForm.dailyType){
                    let params = {
                        ...this.editNoteForm.currentRow,
                        dailyReportName:this.editNoteForm.dailyType,
                    }
                    this.saveLoading = true
                    let res  =   await updateCostBillFinanceType(params)
                    this.saveLoading = false
                    this.visible = false
                    this.$refs.editNoteForm.resetFields()
                    this.getList('search')
                    if(res.isSuccess){
                        this.$message({ message: res.message, type: "success" });
                    }else{
                        this.$message({ message: res.message, type: "error" });
                    }
                }else{
                    this.$message({ message: '请选择需要修改的日报类型', type: "warning" });
                }
            }
        },
        openVisible(){
            this.editNoteForm.openType = 1
           
            this.visible = true

        }
    }
};
</script>

<style lang="scss" scoped>
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 45px;
}
</style>