<template>
    <div class="dialogBox">
        <div class="dialogBox_top">
            <div>
                <div>线上订单号</div>
                <el-tooltip effect="dark" :content="extData.orderNo" placement="top-start">
                    <div class="word">{{ extData.orderNo ? extData.orderNo : '' }}</div>
                </el-tooltip>
            </div>
            <div>
                <div>付款日期</div>
                <el-tooltip effect="dark" :content="extData.timePay" placement="top-start">
                    <div class="word">{{ extData.timePay ? extData.timePay : '' }}</div>
                </el-tooltip>
            </div>
            <div>
                <div>系列编码</div>
                <el-tooltip effect="dark" :content="extData.seriesName" placement="top-start">
                    <div class="word">{{ extData.seriesName ? extData.seriesName : '' }}</div>
                </el-tooltip>
            </div>
        </div>
        <vxetablebase :id="'logList202408041749'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange1' :tableData='dialogTableData' :tableCols='dialogTableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%; height: 300px; margin: 0" />
        <my-pagination ref="pager" :total="dialogTotal" @page-change="dialogPagechange" @size-change="dialogSizechange" />
    </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pageGetLogs } from '@/api/vo/VerifyOrder'
const dialogTableCols = [
    { istrue: true, prop: 'createdTime', label: '操作时间', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'operationType', label: '操作类型', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'orderNoInner', label: '内部订单号', sortable: 'custom', width: '120'},
    { istrue: true, prop: 'operationResult', label: '操作结果', sortable: 'custom', width: 'auto', formatter: (row) => row.goodsName ? row.operationResult + ' ' + row.goodsName : row.operationResult + ' ' },
]
export default {
    name: "logList",
    components: {
        MyContainer, vxetablebase
    },
    props: {
        orderNo: {
            type: String,
            default: ''
        },
        orderNoInner: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            that: this,
            dialogTableCols,
            dialogTableData: [],
            dialogQueryInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'createdTime',
                isAsc: true,
                orderNo: null,
                orderNoInner: null,
            },
            dialogTotal: 0,
            extData: {},
        }
    },
    async mounted() {
        this.dialogQueryInfo.orderNo = this.orderNo
        this.dialogQueryInfo.orderNoInner = this.orderNoInner
        await this.openDialog()
    },
    methods: {
        async openDialog() {
            const { data, success } = await pageGetLogs(this.dialogQueryInfo)
            if (!success) return
            this.extData = data.extData
            this.dialogTableData = data.list
            this.dialogTotal = data.total
            this.dialogVisible = true
        },
        sortchange1({ order, prop }) {
            if (prop) {
                this.dialogQueryInfo.orderBy = prop
                this.dialogQueryInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.openDialog()
            }
        },
        //每页数量改变
        dialogSizechange(val) {
            this.dialogQueryInfo.currentPage = 1;
            this.dialogQueryInfo.pageSize = val;
            this.openDialog()
        },
        //当前页改变
        dialogPagechange(val) {
            this.dialogQueryInfo.currentPage = val;
            this.openDialog()
        },
    }
}
</script>

<style scoped lang="scss">
.dialogBox {
    display: flex;
    flex-direction: column;

    .dialogBox_top {
        display: flex;
        flex-direction: column;
        margin-bottom: 10px;

        div {
            display: flex;

            div {
                width: 80%;
                height: 40px;
                line-height: 40px;
                border: 1px solid #e9e4e4;

                &:nth-child(1) {
                    background-color: rgb(239, 239, 239);
                    width: 20%;
                    border: 1px solid #e9e4e4;
                }
            }
        }
    }
}
</style>