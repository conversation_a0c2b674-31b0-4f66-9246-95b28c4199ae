<template>
    <my-container v-loading="pageLoading">
        <template #header>
        </template>
        <template>
            <div class="div-pagecontent_pdd2">
                <div style="width:99%; height: 30px; line-height: 30px; border-bottom: 1px solid #EBEEF5;margin-left: 0px;">
                    <div style="font-size:18px; font-weight: bold;">
                        <span style=" width: 5px;height: 20px; background-color: #1378FC; border-radius:5px">&nbsp;&nbsp;</span>&nbsp;&nbsp;数据概览
                    </div>
                </div>
                <div style="width:99%;font-size: 14px;">
                    <table style="width:100%;  text-align: center;">
                        <tr style="width:100%; height: 40px; line-height: 40px; color:#007FFF">
                            <td colspan="2">昨日数据</td>
                            <td colspan="2">近7日数据</td>
                            <td colspan="2">直播销售额</td>
                        </tr>
                        <tr style="width:100%; height: 28px;">
                            <td>估算销量</td>
                            <td>估算销售额</td>
                            <td>估算销量</td>
                            <td>估算销售额</td>
                            <td>估算销量</td>
                            <td>估算销售额</td>
                        </tr>
                        <tr style="width:100%; height: 20px; line-height: 20px; font-size: 14px; font-weight: bold;">
                            <td>{{this.pageLoadData.jyCout}}</td>
                            <td>{{this.pageLoadData.jyAmount}}</td>
                            <td>{{this.pageLoadData.jyCout7Days}}</td>
                            <td>{{this.pageLoadData.jyAmount7Days}}</td>
                            <td>{{this.pageLoadData.jyCout30Days}}</td>
                            <td>{{this.pageLoadData.jyAmount30Days}}</td>
                        </tr>
                        <tr style="width:100%; height: 30px;vertical-align: bottom; font-size: 8px;">
                            <td>较前天<span :class="this.pageLoadData.jyCoutCpZtRatio>0?'tdcolorred':'tdcolorgreen'">&nbsp;{{this.pageLoadData.jyCoutCpZtRatio}}</span></td>
                            <td>较前天<span :class="this.pageLoadData.jyAmountCpZtRatio>0?'tdcolorred':'tdcolorgreen'">&nbsp;{{this.pageLoadData.jyAmountCpZtRatio}}</span></td>
                            <td>较前天<span :class="this.pageLoadData.jyCoutCpZtRatio7Days>0?'tdcolorred':'tdcolorgreen'">&nbsp;{{this.pageLoadData.jyCoutCpZtRatio7Days}}</span></td>
                            <td>较前天<span :class="this.pageLoadData.jyAmountCpZtRatio7Days>0?'tdcolorred':'tdcolorgreen'">&nbsp;{{this.pageLoadData.jyAmountCpZtRatio7Days}}</span></td>
                            <td>较前天<span :class="this.pageLoadData.jyCoutCpZtRatio30Days>0?'tdcolorred':'tdcolorgreen'">&nbsp;{{this.pageLoadData.jyCoutCpZtRatio30Days}}</span></td>
                            <td>较前天<span :class="this.pageLoadData.jyAmountCpZtRatio30Days>0?'tdcolorred':'tdcolorgreen'">&nbsp;{{this.pageLoadData.jyAmountCpZtRatio30Days}}</span></td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="div-pagecontent_pdd3">
                <div style="width:99%; height: 30px; line-height: 30px; border-bottom: 1px solid #EBEEF5;margin-left: 0px;">
                    <div style="float: left; font-size:18px; font-weight: bold;">
                        <span style=" width: 5px;height: 20px; background-color: #1378FC; border-radius:5px">&nbsp;&nbsp;</span>&nbsp;&nbsp;基本信息
                    </div>
                    <div style="float: right; margin-right: 150px;">
                        <el-date-picker type="datetimerange" v-model="pddsalescharTime" :minTime="pddsalescharTimeMin" :maxTime="pddsalescharTimeMax" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="pddsalescharTimeChange">
                        </el-date-picker>
                    </div>
                </div>
                <div>
                    <buschar ref="pddsaleschar" :analysisData="pddsalesData" :thisStyle="pddsalescharThisStyle" :GridStyle="pddsalescharGridStyle"></buschar>
                </div>
            </div>
        </template>
    </my-container>
</template>
<script>  
    import cesTable from "@/components/Table/table.vue";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import buschar from '@/components/Bus/buschar'
    import { getHotGoodsCmRefInfoPddData, getProductSalesTrendByDate } from '@/api/operatemanage/productalllink/alllink'
    export default {
        name: "Users",
        components: { MyContainer, MyConfirmButton, cesTable, buschar },
        data() {
            return {
                that: this,
                pageLoading: false,
                pageLoadData: {},
                pagePlanform: 0,
                pageGoodsCompeteId: "",
                //时间
                pddsalescharTime: [],
                pddsalescharTimeMin: "2000-01-01",
                pddsalescharTimeMax: "2099-12-31",
                //趋势图
                pddsalescharThisStyle: { width: '96%', height: '360px', 'box-sizing': 'border-box', 'line-height': '120px' },
                pddsalescharGridStyle: { left: '1%', right: 15, bottom: 20, top: '12%', containLabel: true },
                //趋势图Data
                pddsalesData: {},

            };
        },
        async mounted() {
            //await this.pddsalescharLoadData();
        },
        methods: {
            async clearData() {
                this.pddsalescharTime = [];
                this.pddsalescharTimeMin = "2000-01-01";
                this.pddsalescharTimeMax = "2099-12-31";
                //趋势图Data
                this.pddsalesData = {};
            },

            //初始化：弹开此界面后调用此函数
            //调用步骤 1.pageProductId 主键赋值，2.调用 loadData()
            async pddsalescharLoadData(planform, goodsCompeteId) {
                this.clearData();
                this.pagePlanform = planform;
                this.pageGoodsCompeteId = goodsCompeteId;
                await this.getpddsalescharInfo();
                await this.getpddsalescharChar();
            },
            //加载趋势图数据
            async getpddsalescharChar() {
                await this.$refs.pddsaleschar.initcharts();
            },
            //获取界面初始数据
            async getpddsalescharInfo() {
                this.pageLoading = true;
                try {
                    var parm = { platform: this.pagePlanform, goodsCompeteId: this.pageGoodsCompeteId };
                    var allData = await getHotGoodsCmRefInfoPddData(parm);
                    if (allData && allData.success && allData.data && allData.data.charData) {
                        this.pageLoadData = allData.data;
                        this.pddsalescharTime = allData.data.pddsalescharTime;
                        this.pddsalesData = allData.data.charData;
                    }
                    this.pageLoading = false;
                } catch (error) {
                    this.pageLoading = false;
                }
            },
            async pddsalescharTimeChange() {
                await this.getpddsalescharByDate();
            },
            async getpddsalescharByDate() {
                if (this.pddsalescharTime.length == 2) {
                    this.pageLoading = true;
                    try {
                        var parm = { platform: this.pagePlanform, goodsCompeteId: this.pageGoodsCompeteId, startDate: this.pddsalescharTime[0], endDate: this.pddsalescharTime[1] };
                        var allData = await getHotGoodsCmRefInfoPddData(parm);
                        if (allData && allData.success && allData.data && allData.data.charData) {
                            //this.pageLoadData = allData.data;
                            this.pddsalesData = allData.data.charData;
                            await this.getpddsalescharChar();
                        }
                        this.pageLoading = false;
                    } catch (error) {
                        this.pageLoading = false;
                    }
                }
            }
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
<style>
    .div-pagecontent_pdd2 {
        margin-left: 0px;
        margin-top: 5px;
        margin-bottom: 5px;
        width: 99%;
        height: 170px;
        border: 1px solid #ebeef5;
        padding-left: 10px;
        color: #303133;
    }
    .div-pagecontent_pdd3 {
        margin-left: 0px;
        margin-top: 5px;
        margin-bottom: 5px;
        width: 99%;
        height: 400px;
        border: 1px solid #ebeef5;
        padding-left: 10px;
        color: #303133;
    }
    .tdcolorgreen {
        color: green;
    }
    .tdcolorred {
        color: red;
    }
</style>