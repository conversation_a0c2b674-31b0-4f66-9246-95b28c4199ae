<template>
    <MyContainer v-loading="pageLoading" style="height:600px">
        <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <ces-table ref="table" :that='that' :isIndex='false' :hasexpand='false' @sortchange='sortchange'
            :tableData='datalist' @select='selectchange' :isSelection="false" :tableCols='tableCols' :isSelectColumn='true'
            :customRowStyle="customRowStyle" :loading="listLoading" :summaryarry="summaryarry" :selectColumnHeight="'0px'"
            :isBorder="false">
            <template slot='extentbtn'> </template>
        </ces-table>
        <template #footer>
            <span style="font-size: 13px;">共{{total}}条</span>
            <!-- <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getDataList" /> -->
        </template>
        <el-dialog title="人才信息" :visible.sync="showInfo" append-to-body width="50%" height="60%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <div style="height: 65vh; overflow-x: hidden;">
           <talentInformation :isEdit="false" :candidateInfo="candidateInfo"></talentInformation>
        </div>
        </el-dialog>
    </MyContainer>
</template>
<script>
import cesTable from "@/components/Table/tableforvedio.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import postionDialogform from "@/views/profit/PersonnelRecruiting/postionDialogform";
import talentInformation from "@/views/profit/PersonnelRecruiting/talentInformation";
import { pagePositionCandidate,getCandidateInfo } from '@/api/profit/hr'
import { formatTime } from "@/utils/tools";

const tableCols = [
    { istrue: true, prop: 'name', align: 'left', label: '姓名',  type: "click",  handle: (that, row) => that.editPostion(row.candidateId)  },
    { istrue: true, prop: 'gender', align: 'left', label: '性别', formatter: (row) =>  row.gender == null?'':row.gender == 1 ? '男' : '女' },
    { istrue: true, prop: 'positionName', align: 'left', label: '岗位名称' },
    { istrue: true, prop: 'employmentDate', align: 'left', label: '入职时间',formatter: (row) => formatTime(row.employmentDate, 'YYYY-MM-DD')  },
    { istrue: true, prop: 'employeeStatus', align: 'left', label: '员工状态',formatter: (row) => row.employeeStatus == -1 ? '离职' : row.employeeStatus == 0?'未知' : row.employeeStatus == 1? '试用':'正式'},
    { istrue: true, prop: 'recruiter', align: 'left', label: '招聘专员' },
];

export default {
    name: "headcount",//招聘人数列表
    components: {
        MyContainer, postionDialogform, MyConfirmButton,talentInformation
        , cesTable,
    },
    props: {
        showDialog: {
            type: Boolean,
            default: () => { return false; }
        },
        diologTitle: {
            type: String,
            default: () => { return ''; }
        },
        planId: {
            type: String,
            default: () => { return 0; }
        },
    },
    watch: {
    },
    data () {
        return {
            showInfo:false,
            istLoading: false,
            summaryarry: {},
            datalist: [
                { v1: 1, v2: 1, v3: 1, v4: 1, v5: 1, v6: 1,  },
                { v1: 1, v2: 1, v3: 1, v4: 1, v5: 1, v6: 1,  },
            ],
            islook: false,
            total: 0,
            sels: [], // 列表选中列
            listLoading: false,
            that: this,
            pageLoading: false,
            pager: {},
            tableCols: tableCols,
            isEdit: false,
            filter: {
                planId:this.planId
            },
            candidateInfo:{},

        };
    },
    watch: {
    },
    async created () {

    },
    async mounted () {
        console.log(this.planId)
        this.getDataList();
    },
    methods: {
        async editPostion (candidateId) {
            await getCandidateInfo({ candidateId: candidateId }).then(res => {
                if (res.success) {
                    this.candidateInfo = res.data;
                }
            })
                this.showInfo = true;
                this.isEdit = false;
        },
        //获取数据
        async getDataList () {
            // var pager = this.$refs.pager.getPager();
            var pager = this.pager;

            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.listLoading = true;
            const res = await pagePositionCandidate(params);
            this.listLoading = false;
            this.total = res.data.total
            this.datalist = res.data.list;
            this.summaryarry = res.data.summary;
        },

        //列表排序
        sortchange (column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        //多选事件
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.shopDecorationTaskId);
            })
        },
        customRowStyle (row, index) {
            if (row.row?.isend && row.row.isend == 1) {
                let styleJson = {};
                styleJson.color = "rgb(216 216 216)";
                return styleJson
            } else {
                return null
            }

        },
    },
};
</script>