<template>
    <MyContainer>
        <template #header>
            <div class="header_top">
                <el-date-picker v-model="timeRange" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" class="publicCss"
                    @change="changeTime">
                </el-date-picker>
                <el-input v-model="ListInfo.seriesCode" placeholder="系列编码" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.groupName" placeholder="运营组" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.approveName" placeholder="运营" maxlength="50" clearable class="publicCss" />
                <el-select v-model="ListInfo.hasPurchaseOrder" placeholder="是否生成采购单" clearable class="publicCss">
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                </el-select>
                <el-select v-model="ListInfo.hasPo" placeholder="是否分配采购" clearable class="publicCss">
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                </el-select>
                <el-select v-model="ListInfo.purchaseOrderStatus" placeholder="采购单状态" clearable class="publicCss">
                    <el-option v-for="item in purOrderStatusList" :key="'purchaseOrderStatus' + item" :label="item"
                        :value="item"></el-option>
                </el-select>
                <el-button type="primary" @click="getList(true)">搜索</el-button>
                <el-button type="primary" @click="assignmentStart"
                    v-if="checkPermission('pageGetCompanyGoods')">批量分配</el-button>
                <el-button type="primary" @click="applyProcurement">同供应商申请采购</el-button>
            </div>
        </template>
        <vxetablebase :id="'companyGoods202408041720'" ref="table" @select="checkboxRangeEnd" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            v-loading="loading" :isSelection="false" :isSelectColumn="false" :treeProp="{}"
            style="width: 100%;  margin: 0" height="100%">
            <template slot="right">
                <vxe-column title="" width="200" fixed="right">
                    <template #default="{ row, $index }">
                        <div>
                            <el-button type="text" @click="assignmentStartOne(row)"
                                v-if="checkPermission('pageGetCompanyGoods') && row.id != 0">分配采购</el-button>
                            <el-button type="text" @click="openCgdDialogVisible(row)"
                                v-if="checkPermission('pageGetCompanyGoods') && row.id == 0">生成采购单</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
        <el-dialog title="分配采购员" :visible.sync="dialogVisible" width="20%" v-dialogDrag>
            <div class="dialogBox">
                <el-select v-model="assignmentInfo.brandId" clearable filterable placeholder="请选择采购员" class="publicCss"
                    @change="changeBrandName">
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <div class="btnGroup" style="margin-top:10px">
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="assignmentSubmit">确认分配</el-button>
                </div>
            </div>
        </el-dialog>

        <!-- 建采购单 -->
        <el-dialog :visible.sync="dialogAddVisible" v-dialogDrag width='50%' height='700px'>
            <el-form ref="addForm" :model="addForm" :rules="addFormRules" label-width="100px">
                <el-row :hidden="true">
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item field="id" label="id">
                            <el-input v-model="addForm.id" auto-complete="off" :disabled="true" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item field="indexNo" label="序号">
                            <el-input v-model="addForm.indexNo" auto-complete="off" :disabled="true"
                                style="width:88%" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="warehouse" label="仓库">
                            <el-select v-model="addForm.warehouse" placeholder="请选择分仓" style="width: 130px"
                                @change="changeHouse">
                                <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                                    :value="item.wms_co_id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="supplier_id" label="供应商">
                            <el-select v-model="addForm.supplier_id" ref="supplier_id" filterable remote reserve-keyword
                                placeholder="请输入供应商" :remote-method="remoteSearchSupplier" style="width:250px"
                                @change="changeSupplier">
                                <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <div style="height: 400px;">
                            <vxe-table :data="buyNoList" height="100%" style="width: 100%;" border stripe>
                                <vxe-column title="采购单号" field="buyNo" width="130">
                                    <template slot-scope="scope">
                                        <el-tag v-if="scope.row.isFirst" type="success">{{ scope.row.buyNo
                                            }}(最近一单)</el-tag>
                                        <span v-else>{{ scope.row.buyNo }}</span>
                                    </template>
                                </vxe-column>
                                <vxe-column title="商品编码" field="goodsCode" width="120"></vxe-column>
                                <vxe-column title="供应商" field="supplier" width="200"></vxe-column>
                                <vxe-column title="进货仓" field="warehouseName" width="120"></vxe-column>
                                <vxe-column title="供应商链接" field="address1688" width="120">
                                    <template #default="{ row }">
                                        <el-link v-if="row.address1688" :href="row && row.address1688" type="primary"
                                            target="_blank">供应商链接</el-link>
                                    </template>
                                </vxe-column>
                                <vxe-column title="操作" width="100">
                                    <template slot-scope="scope">
                                        <el-button @click="handleClick(scope.row)" type="text"
                                            size="small">选择</el-button>
                                    </template>
                                </vxe-column>
                            </vxe-table>
                        </div>


                    </el-col>
                </el-row>
            </el-form>

            <template #footer>
                <span class="dialog-footer">
                    <el-link :underline="false" type="primary" style="margin-right: 20px;"
                        @click="addsupplier">添加供应商</el-link>
                    <el-button @click="dialogAddVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onFinish">提交</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 添加供应商 -->
        <el-dialog title="添加供应商" :visible.sync="dialogAddSuppVisible" :close-on-click-modal="false" append-to-body
            v-dialogDrag width='30%' height='500px'>
            <el-form ref="addSuppForm" :model="addSuppForm" :rules="addSuppFormRules" label-width="100px">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item field="name" title="供应商名称">
                            <el-input v-model="addSuppForm.name" placeholder="供应商名称" :maxlength="40"
                                clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogAddSuppVisible = false">取 消</el-button>
                    <!-- <my-confirm-button type="submit" :validate="finishSuppFormValidate" :loading="onFinishSuppLoading"
                        @click="onSave()">
                    </my-confirm-button> -->
                    <el-button type="primary" @click="onSave">提交</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="同供应商申请采购" :visible.sync="applyDialogVisable" width="30%" height='500px'
            :close-on-click-modal="false" v-dialogDrag append-to-body>
            <el-form :model="ruleForm" status-icon ref="ruleForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="供应商:" prop="supplierName">
                    <el-input v-model="ruleForm.supplierName" disabled style="width: 200px;" />
                </el-form-item>
                <el-form-item label="仓库:" prop="warehouseName">
                    <el-input v-model="ruleForm.warehouseName" disabled style="width: 200px;" />
                </el-form-item>
                <el-form-item label="采购人员:" prop="brandName">
                    <el-input v-model="ruleForm.brandName" disabled style="width: 200px;" />
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="applyDialogVisable = false">取 消</el-button>
                <el-button type="primary" @click="applySubmit" v-throttle="1000">确 定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="选择供应商&仓库" :visible.sync="propsVisable" width="20%" height='500px'
            :close-on-click-modal="false" v-dialogDrag append-to-body>
            <el-form :model="ruleForm" status-icon ref="ruleForm" label-width="100px" class="demo-ruleForm"
                :rules="propsRules">
                <el-form-item prop="warehouse" label="仓库">
                    <el-select v-model="ruleForm.warehouse" placeholder="请选择分仓" style="width:250px"
                        @change="changeProps($event, '仓库')">
                        <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                            :value="item.wms_co_id" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="supplierId" label="供应商">
                    <el-select v-model="ruleForm.supplierId" ref="supplier_id" filterable remote reserve-keyword
                        placeholder="请输入供应商" :remote-method="remoteSearchSupplier" :loading="supplierLoading"
                        style="width:250px" @change="changeProps($event, '供应商')">
                        <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="propsVisable = false">取 消</el-button>
                <el-button type="primary" @click="propsSubmit">确 定</el-button>
            </span>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { batchPlanCreatePurchaseOrder } from '@/api/operatemanage/productalllink/alllink'
import { getDirectorList, getDirectorGroupList, } from '@/api/operatemanage/base/shop'
import { replaceSpace, pickerOptions } from '@/utils/getCols'
import { pageGetCompanyGoods, companyGoodsPoBrand, generatePurchaseOrder, butchCreatedPurchaseOrder } from '@/api/inventory/companyGoods'
import { getAllProBrand } from '@/api/inventory/warehouse'
import { TaskGetPurchaseOrder } from "@/api/inventory/purchaseordernew";
import { getAllWarehouse } from '@/api/inventory/warehouse'
import { formatNoLink, warehouselist } from "@/utils/tools";
import { pageSupplierAll, getPurNameSimilarity, addSupplier } from '@/api/inventory/supplier'
import dayjs from 'dayjs'
const tableCols = [
    { istrue: true, label: '', type: "checkbox" },
    { istrue: true, width: '150', fixed: 'left', label: '系列编码/商品编码', prop: 'styleCode', sortable: 'custom', treeNode: true, formatter: (row) => row.goodsCode ? row.goodsCode : row.styleCode },
    { istrue: true, width: '120', label: '系列编码采购员', prop: 'seriesBrandId', formatter: (row) => row.seriesBrandName },
    { istrue: true, width: '120', label: '产品名称', prop: 'goodsName', },
    { istrue: true, width: '120', label: '商品标签', prop: 'labels', },
    { istrue: true, width: '120', label: '生成时间', prop: 'createdTime', sortable: 'custom', formatter: (row) => row.createdTime ? dayjs(row.createdTime).format('YYYY-MM-DD') : null },
    {
        istrue: true, width: '120', label: '分配状态', prop: 'poStatus', formatter: (row) => {
            if (row.id == 0) {
                return ''
            } else {
                return row.poStatus
            }
        }
    },
    { istrue: true, width: '120', label: '分配人', prop: 'poUserId', formatter: (row) => row.poUserName },
    { istrue: true, width: '120', label: '分配时间', prop: 'poTime', formatter: (row) => row.poTime ? dayjs(row.poTime).format('YYYY-MM-DD') : null },
    { istrue: true, width: '120', label: '数量', prop: 'count', sortable: 'custom', },
    { istrue: true, width: '120', label: '采购人员', prop: 'brandId', formatter: (row) => row.brandName },
    { istrue: true, width: '120', label: '生成采购单时间', prop: 'purchaseDate', formatter: (row) => row.purchaseDate ? dayjs(row.purchaseDate).format('YYYY-MM-DD') : null },
    { istrue: true, width: '120', label: '采购单审核时间', prop: 'checkDate', formatter: (row) => row.checkDate ? dayjs(row.checkDate).format('YYYY-MM-DD') : null },
    { istrue: true, width: '120', label: '采购单号', prop: 'buyNo', },
    { istrue: true, width: '120', label: 'ERP单号', prop: 'indexNo', },
    { istrue: true, width: '120', label: '采购单状态', prop: 'purchaseOrderStatus', },
    { istrue: true, width: '120', label: '运营', prop: 'approveName', },
    { istrue: true, width: '120', label: '运营组', prop: 'groupId', formatter: (row) => row.groupName },
    { istrue: true, width: '120', label: '供应商', prop: 'supplierId', formatter: (row) => row.supplierName },
    { istrue: true, width: '120', label: '仓库', prop: 'wmsCoId', formatter: (row) => row.wmsName },
]
export default {
    name: "companyGoods",
    components: { MyContainer, vxetablebase },
    data() {
        return {
            dialogAddSuppVisible: false,
            addFormRules: {
                warehouse: [{ required: true, message: '请输入仓库', trigger: 'blur' }],
                supplier_id: [{ required: true, message: '请输入供应商', trigger: 'blur' }],
            },
            dialogAddVisible: false,
            that: this,
            pickerOptions,
            tableCols,
            tableData: [],
            timeRange: [],
            total: 0,
            loading: true,
            ListInfo: {
                currentPage: 1,
                pagesize: 50,
                orderBy: null,
                isAsc: false,
                seriesCode: null,//系列编码
                goodsCode: null,//商品编码
                groupName: null,//运营组
                approveName: null,//运营
                hasPurchaseOrder: null,//是否生成采购单
                purchaseOrderStatus: null,//采购单状态
                startDate: null,//开始时间
                endDate: null,//结束
                approveStatus: '申请通过',
                hasPo: null,//是否分配采购
            },
            purOrderStatusList: ['待审核', '已确认', '作废', '完成'],
            assignmentInfo: {
                ids: [],
                brandId: null,
                brandName: null
            },
            brandlist: [],
            dialogVisible: false,
            cgdDialogVisible: false,
            buyNoList: [],
            generateList: [],
            addForm: {
                id: 0,
                warehouse: null,
                supplier_id: null,
                supplier: null,
                wmsCoId: null,
            },
            onFinishLoading: false,
            supplierOptions: [],
            warehouselist: [],
            addSuppFormRules: {
                name: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
            },
            addSuppForm: {
                name: null
            },
            submitInfo: {
                ids: [],
                supplierId: null,
                supplierName: null,
                wmsCoId: null,
                wmsName: null,
            },
            applyProcurementList: [],
            applyDialogVisable: false,
            ruleForm: {
                supplierName: '',//供应商
                supplierId: '',//供应商id
                warehouse: '',//仓库 
                warehouseName: '',//仓库名称
                brandId: '',//采购员
                brandName: '',//采购员名称
                companyGoodsIds: [],//采购计划id
            },
            propsVisable: false,
            propsRules: {
                warehouse: [{ required: true, message: '请选择仓库', trigger: 'blur' }],
                supplierId: [{ required: true, message: '请选择供应商', trigger: 'blur' }],
            },
            supplierOptions: []
        }
    },
    async mounted() {
        this.getList()
        const { data } = await getAllProBrand();
        this.brandlist = data.map(item => {
            return { value: item.key, label: item.value };
        });
        const { data: data1 } = await getAllWarehouse();
        this.warehouselist = data1;
    },
    methods: {
        async remoteSearchSupplier(parm) {
            this.supplierOptions = [];
            if (!parm) {
                return;
            }
            var options = [];
            const res = await pageSupplierAll({ currentPage: 1, pageSize: 50, name: parm });
            res?.data?.list.forEach(f => {
                options.push({ value: f.supplier_id, label: f.name })
            });
            this.supplierOptions = options;
        },
        propsSubmit() {
            if (!this.ruleForm.warehouse) return this.$message.error('请选择仓库');
            if (!this.ruleForm.supplierId) return this.$message.error('请选择供应商');
            this.ruleForm.companyGoodsIds = this.applyProcurementList.map(item => item.id);
            this.propsVisable = false;
            this.applyDialogVisable = true;
        },
        changeProps(e, type) {
            if (type == '仓库') {
                this.ruleForm.warehouseName = e ? this.warehouselist.find(x => x.wms_co_id == e).name : '';
                console.log(this.ruleForm.warehouseName, 'warehouseName');
            } else {
                this.ruleForm.supplierName = e ? this.supplierOptions.find(x => x.value == e).label : '';
                console.log(this.ruleForm.supplierName, 'supplierName');
            }
        },
        async applySubmit() {
            const { success } = await butchCreatedPurchaseOrder(this.ruleForm);
            if (!success) return this.$message.error('申请采购失败');
            this.applyDialogVisable = false;
            this.getList();
            this.$message.success('申请采购成功');
        },
        applyProcurement() {
            if (this.applyProcurementList.length == 0) {
                return this.$message.error('请选择需要申请采购的数据');
            }
            this.ruleForm = {
                supplierName: '',//供应商
                supplierId: '',//供应商id
                warehouse: '',//仓库 
                warehouseName: '',//仓库名称
                brandId: '',//采购员
                brandName: '',//采购员名称
                companyGoodsIds: [],//采购计划id
            }
            // let warehouseName = this.applyProcurementList[0].wmsName;
            let brandName = this.applyProcurementList[0].brandName;
            let brandId = this.applyProcurementList[0].brandId;
            // let supplierName = this.applyProcurementList[0].supplierName;
            // let warehouse = this.applyProcurementList[0].wmsCoId;
            this.applyProcurementList.forEach(item => {
                if (!item.brandId) {
                    this.$message.error('选中数据中存在未分配采购员的数据，请分配后再操作！');
                    throw new Error('选中数据中存在未分配采购员的数据，请分配后再操作！');
                }
                if(item.purchaseDate){
                    this.$message.error('选中数据中存在已生成采购单的数据，请重新选择！');
                    throw new Error('选中数据中存在已生成采购单的数据，请重新选择！');
                }
            })
            let flag = this.applyProcurementList.every(item => {
                return item.brandId == brandId && item.brandName == brandName
            });
            console.log(this.applyProcurementList, 'this.applyProcurementList');
            if (!flag) {
                return this.$message.error('请选择同一采购员的数据');
            }
            this.supplierOptions = [];
            this.ruleForm.brandName = brandName;
            this.ruleForm.brandId = brandId;
            this.propsVisable = true
        },
        assignmentStartOne(row) {
            this.assignmentInfo.ids = [row.id]
            this.dialogVisible = true
        },
        changeSupplier(e) {
            this.addForm.supplier = this.supplierOptions.filter(item => item.value == e)[0].label
            console.log(this.addForm, 'addForm1');
        },
        changeHouse(e) {
            this.warehouselist.filter(item => item.wms_co_id == e).map(item => {
                this.addForm.wmsCoId = item.wms_co_id
                this.addForm.warehouse = item.name
            })
        },
        async onFinish() {
            //如果没有仓库或者供应商
            if (!this.addForm.warehouse || !this.addForm.supplier_id) {
                this.$message.error('请选择仓库和供应商')
                return
            }
            this.submitInfo = {
                ids: this.assignmentInfo.ids,
                supplierId: this.addForm.supplier_id,
                supplierName: this.addForm.supplier,
                wmsCoId: this.addForm.wmsCoId,
                wmsName: this.addForm.warehouse,
            }
            const { success } = await generatePurchaseOrder(this.submitInfo)
            if (success) {
                this.$message.success('生成采购单成功')
                this.dialogAddVisible = false
                this.getList()
            }
        },
        async onSave() {
            var _this = this;
            if (!_this.addSuppForm.name || _this.addSuppForm.name == '') {
                _this.$message({ type: 'error', message: '请输入供应商名称！' });
                return;
            }
            //校验名称是否有相似的
            _this.onFinishSuppLoading = true;
            await getPurNameSimilarity({ supplierName: _this.addSuppForm.name }).then(function (res) {
                if (res?.success && res.data) {
                    _this.$confirm("存在相似的供应商名：" + res.data + ",是否继续添加？", '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                        .then(function () {
                            _this.configSave();

                        })
                        .catch(() => {
                            _this.onFinishSuppLoading = false;
                            //取消操作
                            return;
                        });
                } else if (!res?.success && (res?.data == null || res?.data == "")) {
                    _this.onFinishSuppLoading = false;
                    //取消操作
                    return;
                } else {
                    _this.configSave();
                }
            });
        },
        async addsupplier() {
            var _this = this;
            _this.addSuppForm.name = null;
            _this.dialogAddSuppVisible = true;
        },
        configSave() {
            let that = this;
            that.$confirm('确定保存吗, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                .then(() => {
                    addSupplier(that.addSuppForm)
                        .then(function (res) {
                            if (!res?.success) {
                                that.$message({ type: 'warn', message: '保存失败' });
                                that.onFinishSuppLoading = false;
                                that.dialogAddSuppVisible = false;
                            }
                            else {
                                that.$message({ type: 'success', message: '保存成功' });
                                that.onFinishSuppLoading = false;
                                that.dialogAddSuppVisible = false;
                                that.$emit("addcallback", res.data);
                            }
                        })
                        .catch(function (err) { console.error(err); });
                }).catch(() => {
                    that.$message({ type: 'info', message: '已取消' });
                    that.onFinishSuppLoading = false;
                });
        },
        async handleClick(row) {
            this.addForm.warehouse = row.warehouseName
            var res = await this.remoteSearchSupplierasync({ supplier_id: row.supplier_id, name: row.supplier });
            if (res == false) {
                this.$message.error("温馨提示：此供应商信息历史数据被更改，请手动输入供应商！");
                return;
            }
            this.addForm.supplier_id = row.supplier_id;
            console.log(this.addForm.supplier_id, 'this.addForm.supplier_id');
            //找出对应的供应商
            this.addForm.supplier = row.supplier;
            console.log(this.addForm.supplier, 'this.addForm.supplier');
        },
        async remoteSearchSupplierasync(parm) {
            this.supplierOptions = [];
            if (!parm) {
                return;
            }
            var options = [];
            const res = await pageSupplierAll({ currentPage: 1, pageSize: 50, name: parm.name });
            if (res?.data.total == 0) {
                this.addForm.supplier_id = null;
                return false;
            }
            var data = res?.data?.list;
            for (var i = 0; i < data?.length; i++) {
                if (data[i].supplier_id != parm.supplier_id) {
                    this.addForm.supplier_id = null;
                    return false;
                }
                options.push({ value: data[i].supplier_id, label: data[i].name })
            }
            this.supplierOptions = options;
            return true;
        },
        async remoteSearchSupplier(parm) {
            this.supplierOptions = [];
            if (!parm) {
                return;
            }
            const { data } = await pageSupplierAll({ currentPage: 1, pageSize: 50, name: parm });
            this.supplierOptions = data.list.map(item => {
                return { value: item.supplier_id, label: item.name };
            });
        },
        async openCgdDialogVisible(row) {
            console.log(row, 'row');
            const a = this.generateList.every(item => this.generateList[0].styleCode == item.styleCode)
            if (a) {
                this.cgdDialogVisible = true
            } else {
                return this.$message.error('请选择相同系列编码的商品')
            }
            const params = this.generateList.filter(item => item.id != 0).map(item => item.goodsCode).join(',')
            if (params.length == 0 || params == null || params == undefined || params == '') {
                this.$message.error('请选择商品')
                return
            }
            const { data, success } = await TaskGetPurchaseOrder({ goodsCode: params })
            if (success) {
                const arr = this.generateList.filter(item => item.id != 0)
                this.addForm.warehouse = arr[0].wmsName
                this.addForm.wmsCoId = arr[0].wmsCoId
                this.addForm.supplier_id = arr[0].supplierId
                this.addForm.supplier = arr[0].supplierName
                if (arr[0].supplierName) {
                    this.remoteSearchSupplier(arr[0].supplierName)
                }
                this.buyNoList = data
                this.dialogAddVisible = true
            }
        },
        async assignmentSubmit() {
            //如果没有选择采购员
            if (!this.assignmentInfo.brandId) {
                this.$message.error('请选择采购员')
                return
            }
            const { success } = await companyGoodsPoBrand(this.assignmentInfo)
            if (success) {
                this.$message.success('分配成功')
                this.assignmentInfo.ids = []
                this.assignmentInfo.brandId = null
                this.assignmentInfo.brandName = null
                this.dialogVisible = false
                this.getList()
            }
        },
        assignmentStart() {
            if (this.assignmentInfo.ids.length == 0) {
                this.$message.error('请选择要分配的商品')
                return
            }
            this.dialogVisible = true
        },
        changeBrandName(e) {
            console.log(e, 'e');
            //根据e找出对应的brandName
            this.assignmentInfo.brandName = this.brandlist.find(item => item.value == e).label
        },
        changeTime(e) {
            if (e) {
                this.ListInfo.startDate = dayjs(e[0]).format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs(e[1]).format('YYYY-MM-DD')
            } else {
                this.ListInfo.startDate = null
                this.ListInfo.endDate = null
            }
            this.getList()
        },
        checkboxRangeEnd(rows) {
            this.assignmentInfo.ids = rows.filter(item => item.id != 0).map(item => item.id)
            this.generateList = rows
            this.applyProcurementList = rows.filter(item => item.id != 0);
        },
        //页面数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pagesize = val;
            this.getList();
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },
        async getList(isSearch) {
            if (isSearch) {
                this.ListInfo.currentPage = 1
                this.ListInfo.pagesize = 50
            }
            this.loading = true
            const replaceArr = ['seriesCode', 'goodsCode', 'groupName', 'approveName']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            const { data, success } = await pageGetCompanyGoods(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
            }
            this.loading = false
        },
        //页面数量改变
        detailSizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pagesize = val;
            this.getList();
        },
        //当前页改变
        detailPagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.header_top {
    display: flex;
    margin-bottom: 10px;
}

.publicCss {
    width: 220px;
    margin-right: 10px;
}

.dialogBox {
    display: flex;
    flex-direction: column;
    align-items: center;
}
</style>