<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="希音-全托交易收入" name="first1" :lazy="true" style="height: 98%;">
        <primeOperatingRevenue />
      </el-tab-pane>
      <el-tab-pane label="希音-全托账单费用结算" name="first2" :lazy="true" style="height: 98%;">
        <settlementFullTrustBillFees />
      </el-tab-pane>
        </el-tabs>
    </my-container>

</template>
<script>
    import MyContainer from "@/components/my-container";
    import primeOperatingRevenue from './primeOperatingRevenue.vue'
    import settlementFullTrustBillFees from './settlementFullTrustBillFees.vue'

    export default {
        name: "Users",
        components: { MyContainer,primeOperatingRevenue, settlementFullTrustBillFees },
        data() {
            return {
                activeName: 'first1'
            };
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
