<template>
    <div class="el-quarter-picker" style="display: flex; align-items: center;">
                  
        <el-date-picker
            v-model="yeartsStart"
            :picker-options="startDatePicker"
            value-format="yyyy"
            type="year"
            @change="startTime"
            placeholder="开始时间"
          >
          </el-date-picker>
          ~
          <el-date-picker
            v-model="yeartsEnd"
            value-format="yyyy"
            @change="startTime"
            :picker-options="endDatePicker"
            type="year"
            placeholder="结束时间"
          >
          </el-date-picker>

    </div>
  </template>
  <script>
  export default {
    name: 'ElQuarterPicker',
    props: {
      placeholder: {
        type: String,
        default: ''
      },
      size: {
        type: String,
        default: ''
      },
      readonly: {
        type: Boolean,
        default: false
      },
      clearable: {
        type: Boolean,
        default: true
      },
      editable: {
        type: Boolean,
        default: true
      },
      disabled: {
        type: Boolean,
        default: false
      },
      format: {
        type: String,
        default: 'yyyy年第Q季度'
      },
      valueFormat: {
        type: String,
        default: 'yyyy-qq'
      },
      value: {
        type: String,
        default: ''
      }
    },
    model: {
      prop: 'value',
      event: 'change'
    },
    watch: {
      value(val) {
        // console.log('change-------', val)
        this.changeValue(val)
      },
      readonly(val) {
        this.canEdit = !val && this.editable
        this.canPopover = !this.disabled && !val
      },
      editable(val) {
        this.canEdit = !this.readonly && val
      },
      disabled(val) {
        this.canPopover = !val && !this.readonly
      }
    },
    data() {
      return {
        startDatePicker: this.beginDate(),
        endDatePicker: this.processDate(),   
        yeartsStart:'',
        yeartsEnd:''

      }
    },
    mounted() {
      
    },
    destroyed() {
    },
     
    methods: {
        startTime(){
            let _this = this;
            if(_this.yeartsStart !== ''&& _this.yeartsEnd !== ''){
                _this.$emit('changetime', [_this.yeartsStart, _this.yeartsEnd])
            }
        },
        beginDate() {
            let self = this
            return {
                disabledDate(time) {
                if (self.yeartsEnd !== '') {
                    let fixedTime = new Date(time)
                    return fixedTime.getFullYear() > self.yeartsEnd
                }
                }
            }
            },
            // 提出结束时间必须大于提出开始时间
            processDate() {
            let self = this
            return {
                disabledDate(time) {
                // let fixedTime = new Date(self.oldTime).getTime()
                // return time.getTime() < fixedTime
                let fixedTime = new Date(time)
                return fixedTime.getFullYear() < self.yeartsStart
                }
            }
            },
    }

  }
  </script>
  <style scoped>
  /* 顶部样式 */
  .card-head{
    padding:12px 16px;
    background-color: #fff;
    position:relative;
  }
  /* 数据日期字体样式 */
  .date-font{
    /* color: var(---Gray-6, #78808F); */
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
  /*弹性布局：两端对齐 */
  .data-flex-sabw{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  /* 弹性布局：左对齐*/
  .data-flex-start {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }
  /* 弹性布局：右对齐*/
  .data-flex-end {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
  }
  .tp-con{
    width: 100%;
    height: 56px;
    box-sizing: border-box;
    display: flex;
    padding: 12px 0px;
    align-items: flex-start;
    gap: 20px;
  }
  /*隐藏显示，占位*/
  .with-hide {
    opacity: 0;
    color: var(---Neutral-8, #292929);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .with-d {
    width: 24px;
  }
  .with-t {
    width: 80px;
  }
  .with-l {
    width: 38px;
  }
  .with-s {
    box-sizing: border-box;
    padding-right: 8px;
    color: var(---Neutral-8, #292929);
    text-align: right;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .st_line {
    box-sizing: border-box;
    margin: 0 2px;
    width: 9px;
    height: 22px;
    color: var(---Gray-6, #78808F);
    text-align: center;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .min-wid{
    min-width:160px;
  }
  .query-bt {
    background-color: #00B0B0;
    color: #FFF;
    outline: none;
  }
  .center-deep{
    height: 100%;
    width: 100%;
    background-color: #fff;
    margin-top: 16px;
  }
  /* ::v-deep .treeselect-main {
    width: 204px;
    line-height: 28px;
  }
  ::v-deep .vue-treeselect__placeholder {
    line-height: 28px;
  }
  ::v-deep .vue-treeselect__control {
    height: 32px !important;
    line-height: 32px;
    border-radius: 4px;
  } */
  ::v-deep.vue-treeselect__single-value {
    font-size: 14px;
    top: 0%;
    color: #606266;
    font-weight: 400;
  }
  /* ::v-deep.vue-treeselect__input {
    height: 32px;
    line-height: 32px;
  } */
  /*顶部筛选器输入框宽度*/
  ::v-deep.el-select el-select--small,el-input el-input--small el-input--suffix{
    width: 100% !important;
    flex: 1;
  }
  /*年范围选择器*/
  .year-range-picker {
    color: black;
    text-align: center;
    /* border: 1px solid #dcdfe6; */
    border-radius:4px;
    /* line-height: 32px; */
    overflow: hidden;
    display: flex;
    /* margin: 4px 0; */
  }
  /* ::v-deep.el-input--mini .el-input__inner {
    height: 32px;
    line-height: 32px;
    border: none
  } */
  .range-word {
    box-sizing: border-box;
    padding-top: 6px;
    color: var(---Neutral-8, #292929);
    text-align: right;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }

  .year-range-picker .year-picker {
    max-width: 150px;
    margin:0;
  }
</style>
