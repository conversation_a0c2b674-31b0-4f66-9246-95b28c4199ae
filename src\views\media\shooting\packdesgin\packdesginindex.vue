<template>
    <my-container v-loading="pageLoading">
        <el-tabs v-model="activeName" style="height:94%;" :before-leave="beforeLeave">
            <el-tab-pane v-if="checkPermission('packdesgin-xlbm')" name="tab44" label="系列编码" style="height: 100%;"
                :lazy="true">
                <procodesimilarityshoot :platformListpack="platformList" :warehouselist="warehouselist"
                    :dockingPeopleList="dockingPeopleList" :fpPhotoLqNameList="fpPhotoLqNameList"
                    :taskUrgencyList="taskUrgencyList" :packclasslist="packclasslist" :brandList="brandList"
                    :groupListpack="groupList" />
            </el-tab-pane>
            <el-tab-pane name="tab0" label="任务列表" style="height: 100%;" :lazy="true">
                <packdesgintask ref="packdesgintask" key="packdesgintask" :tabkey="'packdesgintask'" :listtype="1"
                    :role="currole" :platformList="platformList" :warehouselist="warehouselist"
                    :dockingPeopleList="dockingPeopleList" :fpPhotoLqNameList="fpPhotoLqNameList"
                    :taskUrgencyList="taskUrgencyList" :packclasslist="packclasslist" :brandList="brandList"
                    :groupList="groupList" :tablekey="'maintask'" :islook="false"
                    :filter="{ isShop: 0, isdel: 0, isComplate: 0 }" />
            </el-tab-pane>
            <el-tab-pane name="tab1" label="完成设计" style="height: 100%;" :lazy="true">
                <packdesgintask ref="packdesgintaskover" key="packdesgintaskover" :tabkey="'packdesgintaskover'"
                    :listtype="2" :role="currole" :platformList="platformList" :warehouselist="warehouselist"
                    :dockingPeopleList="dockingPeopleList" :fpPhotoLqNameList="fpPhotoLqNameList"
                    :taskUrgencyList="taskUrgencyList" :packclasslist="packclasslist" :brandList="brandList"
                    :groupList="groupList" :tablekey="'maintask'" :filter="{ isShop: 0, isdel: 0, isComplate: 1 }" />
            </el-tab-pane>
            <el-tab-pane name="tab2" label="确认信息" style="height: 100%;" :lazy="true">
                <packdesgintask ref="packdesgintaskconfirm" key="packdesgintaskconfirm" :tabkey="'packdesgintaskconfirm'"
                    :listtype="3" :role="currole" :platformList="platformList" :warehouselist="warehouselist"
                    :dockingPeopleList="dockingPeopleList" :fpPhotoLqNameList="fpPhotoLqNameList"
                    :taskUrgencyList="taskUrgencyList" :packclasslist="packclasslist" :brandList="brandList"
                    :groupList="groupList" :tablekey="'maintask'" :filter="{ isShop: 0, isdel: 0, isComplate: 2 }" />
            </el-tab-pane>
            <el-tab-pane name="tab3" label="已使用" style="height: 100%;" :lazy="true">
                <packdesgintask ref="packdesgintaskuesd" key="packdesgintaskuesd" :tabkey="'packdesgintaskuesd'"
                    :listtype="4" :role="currole" :platformList="platformList" :warehouselist="warehouselist"
                    :dockingPeopleList="dockingPeopleList" :fpPhotoLqNameList="fpPhotoLqNameList"
                    :taskUrgencyList="taskUrgencyList" :packclasslist="packclasslist" :brandList="brandList"
                    :groupList="groupList" :tablekey="'maintask'" :filter="{ isShop: 0, isdel: 0, isComplate: 3 }" />
            </el-tab-pane>
            <el-tab-pane name="tab4" label="统计列表" v-if="checkPermission('packdesgin-tongji')" style="height: 100%;"
                :lazy="true">
                <packdesgintask ref="packdesgintaskcacl" key="packdesgintaskcacl" :tabkey="'packdesgintaskcacl'"
                    :listtype="5" :role="currole" :platformList="platformList" :warehouselist="warehouselist"
                    :dockingPeopleList="dockingPeopleList" :fpPhotoLqNameList="fpPhotoLqNameList"
                    :taskUrgencyList="taskUrgencyList" :packclasslist="packclasslist" :brandList="brandList"
                    :groupList="groupList" :tablekey="'maintask'" :filter="{ isShop: 0, isdel: 0, isCacl: 1 }" />
            </el-tab-pane>
            <el-tab-pane name="tab5" label="存档信息" style="height: 100%;" :lazy="true">
                <packdesgintask ref="packdesgintaskshopinfo" key="packdesgintaskshopinfo" :tabkey="'packdesgintaskshopinfo'"
                    :listtype="6" :role="currole" :platformList="platformList" :warehouselist="warehouselist"
                    :dockingPeopleList="dockingPeopleList" :fpPhotoLqNameList="fpPhotoLqNameList"
                    :taskUrgencyList="taskUrgencyList" :packclasslist="packclasslist" :brandList="brandList"
                    :groupList="groupList" :tablekey="'maintask'" :filter="{ isShop: 2, isdel: 0 }" />
            </el-tab-pane>
            <el-tab-pane name="tab6" v-if="checkPermission('packdesgin-cundang')" label="存档" style="height: 100%;"
                :lazy="true">
                <packdesgintask ref="packdesgintaskshop" key="packdesgintaskshop" :tabkey="'packdesgintaskshop'"
                    :listtype="7" :role="currole" :platformList="platformList" :warehouselist="warehouselist"
                    :dockingPeopleList="dockingPeopleList" :fpPhotoLqNameList="fpPhotoLqNameList"
                    :taskUrgencyList="taskUrgencyList" :packclasslist="packclasslist" :brandList="brandList"
                    :groupList="groupList" :tablekey="'maintask'" :filter="{ isShop: 1, isdel: 0 }" />
            </el-tab-pane>
            <el-tab-pane name="tab7" v-if="checkPermission('packdesgin-huisou')" label="回收站" style="height: 100%;"
                :lazy="true">
                <packdesgintask ref="packdesgintaskback" key="packdesgintaskback" :tabkey="'packdesgintaskback'"
                    :listtype="8" :role="currole" :platformList="platformList" :warehouselist="warehouselist"
                    :dockingPeopleList="dockingPeopleList" :fpPhotoLqNameList="fpPhotoLqNameList"
                    :taskUrgencyList="taskUrgencyList" :packclasslist="packclasslist" :brandList="brandList"
                    :groupList="groupList" :tablekey="'maintask'" :filter="{ isdel: 1 }" />
            </el-tab-pane>
            <el-tab-pane name="tab8" label="打包进度" style="height: 100%;" :lazy="true">
                <packdesginPackageInfo ref="packdesginPackageInfo" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane name="tab9" v-if="checkPermission('packdesgin-shezhi')" label="设置" style="height: 100%;"
                :lazy="true">
                <packdesgintaskset ref="packdesgintaskset" style="height: 100%;" />
            </el-tab-pane> 
            <el-tab-pane name="tab99" style="height: 100%;" :lazy="true">
                <span slot="label">
                    <el-link type="" style="color: #fff;" @click="toResultmatter">首页</el-link>
                </span>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import packdesgintask from '@/views/media/shooting/packdesgin/maintasklist/packdesgintask';
import packdesginPackageInfo from '@/views/media/shooting/packdesgin/packdesginPackageInfo';
import packdesgintaskset from '@/views/media/shooting/packdesgin/packdesgintaskset';
import { getShootOperationsGroup } from '@/api/media/mediashare';
import { getUserRoleList, getShootingViewPersonAsync } from '@/api/media/packdesgin';
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import { rulePlatform } from "@/utils/formruletools";
import { ShootingVideoTaskUrgencyOptions } from "@/utils/tools";
import procodesimilarityshoot from '@/views/order/procodesimilarityshoot' 
export default {
    components: { MyContainer, packdesgintask, packdesgintaskset, packdesginPackageInfo, procodesimilarityshoot },
    data() {
        return {
            currole: 'tz',
            pageLoading: false,
            platformList: [],//平台
            warehouselist: [],//仓库
            groupList: [],//运营组
            dockingPeopleList: [],//对接人
            fpPhotoLqNameList: [],//分配查询
            packclasslist: [],
            brandList: [],
            taskUrgencyList: ShootingVideoTaskUrgencyOptions,
            that: this,
            packdesgintask: "packdesgintask",
            filter: {},
            activeName: 'tab0'
        };
    },
    async mounted() {
        await this.getrole();
        await this.getDropDownList();
        await this.getShootingViewPerson();
    },
    methods: {
        toResultmatter() {
            this.$router.push({ path: '/media/index/homepage' })
        },
        beforeLeave(visitName, currentName) {
            if (visitName == "tab99")
                return false;
        },
        async getDropDownList() {
            var res = await getShootOperationsGroup({ type: 3 });
            this.warehouselist = res?.map(item => { return { value: item.id, label: item.label }; });

            var res = await getShootOperationsGroup({ type: 12 });
            this.packclasslist = res?.map(item => { return { value: item.id, label: item.label }; });

            var res = await getShootOperationsGroup({ type: 13 });
            this.brandList = res?.map(item => { return { value: item.id, label: item.label }; });

            var pfrule = await rulePlatform();
            this.platformList = pfrule.options;
            var res = await getDirectorGroupList();
            this.groupList = res.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async getShootingViewPerson() {
            var res = await getShootingViewPersonAsync();
            if (res?.success) {
                this.dockingPeopleList = res.data.dockingPeopleList;
                this.fpPhotoLqNameList = res.data.fpallList;
            }
        },

        async getrole() {
            var res = await getUserRoleList();
            if (res?.success) {
                if (res.data == null) {
                    this.currole = "tz";
                } else if (res.data.indexOf("视觉部经理") > -1) {
                    this.currole = "b";
                }

            } else {
                this.currole = "tz";
            }
        },

    },
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
    margin-bottom: 20px;
}
</style>

