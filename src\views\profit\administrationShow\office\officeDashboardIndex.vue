<template>
  <my-container>
    <el-tabs v-model="activeName" style="height:95%;" @tab-click="tabClickMethod">
      <el-tab-pane label="行政数据可视化看板" name="first1" style="height: 100%;">
        <administrativeDataDashboards ref="refadministrativeDataDashboards" />
      </el-tab-pane>
      <el-tab-pane label="OA台账" name="first12" style="height: 100%;" lazy>
        <aoataizhang ref="aoataizhang" />
      </el-tab-pane>
      <el-tab-pane label="采购预算表" name="first13" style="height: 100%;" lazy>
        <bcaigouyusuan ref="bcaigouyusuan" />
      </el-tab-pane>
      <el-tab-pane label="员工餐" name="first14" style="height: 100%;" lazy>
        <cyuangongcan ref="cyuangongcan" />
      </el-tab-pane>
      <el-tab-pane label="电费" name="first15" style="height: 100%;" lazy>
        <ddianfei ref="ddianfei" />
      </el-tab-pane>
      <el-tab-pane label="工位" name="first16" style="height: 100%;" lazy>
        <egongwei ref="egongwei" />
      </el-tab-pane>
      <el-tab-pane label="总违纪情况" name="first17" style="height: 100%;" lazy>
        <fzongweijiqingkuang ref="fzongweijiqingkuang" />
      </el-tab-pane>
      <el-tab-pane label="部门违纪数据" name="first18" style="height: 100%;" lazy>
        <gbumenweijishuju ref="gbumenweijishuju" />
      </el-tab-pane>
      <el-tab-pane label="绩效乐捐" name="first19" style="height: 100%;" lazy>
        <hjixiaolejuan ref="hjixiaolejuan" />
      </el-tab-pane>
      <el-tab-pane label="员工活动台账" name="first110" style="height: 100%;" lazy>
        <iyuangonghuodongtaizhang ref="iyuangonghuodongtaizhang" />
      </el-tab-pane>
      <el-tab-pane label="公关接待" name="first112" style="height: 100%;" lazy>
        <jgongguanjiedai ref="jgongguanjiedai" />
      </el-tab-pane>
      <el-tab-pane label="押金" name="first117" style="height: 100%;" lazy>
        <kyajin ref="kyajin" />
      </el-tab-pane>
      <el-tab-pane label="满意度评分" name="first113" style="height: 100%;" lazy>
        <lmanyidupingfen ref="lmanyidupingfen" />
      </el-tab-pane>
      <el-tab-pane label="固定资产" name="first114" style="height: 100%;" lazy>
        <mgudingzichan ref="mgudingzichan" />
      </el-tab-pane>
      <el-tab-pane label="区域监控自查" name="first115" style="height: 100%;" lazy>
        <nquyujiankongzicha ref="nquyujiankongzicha" />
      </el-tab-pane>
      <el-tab-pane label="区域监控互查" name="first116" style="height: 100%;" lazy>
        <oqujianjiankonghucha ref="oqujianjiankonghucha" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import administrativeDataDashboards from './components/administrativeDataDashboards/administrativeDataDashboards.vue'
import aoataizhang from './components/aoataizhang/aoataizhang.vue'
import bcaigouyusuan from './components/bcaigouyusuan/bcaigouyusuan.vue'
import cyuangongcan from './components/cyuangongcan/cyuangongcan.vue'
import ddianfei from './components/ddianfei/ddianfei.vue'
import egongwei from './components/egongwei/egongwei.vue'
import fzongweijiqingkuang from './components/fzongweijiqingkuang/fzongweijiqingkuang.vue'
import gbumenweijishuju from './components/gbumenweijishuju/gbumenweijishuju.vue'
import hjixiaolejuan from './components/hjixiaolejuan/hjixiaolejuan.vue'
import iyuangonghuodongtaizhang from './components/iyuangonghuodongtaizhang/iyuangonghuodongtaizhang.vue'
import jgongguanjiedai from './components/jgongguanjiedai/jgongguanjiedai.vue'
import kyajin from './components/kyajin/kyajin.vue'
import lmanyidupingfen from './components/lmanyidupingfen/lmanyidupingfen.vue'
import mgudingzichan from './components/mgudingzichan/mgudingzichan.vue'
import nquyujiankongzicha from './components/nquyujiankongzicha/nquyujiankongzicha.vue'
import oqujianjiankonghucha from './components/oqujianjiankonghucha/oqujianjiankonghucha.vue'















export default {
  name: "officeDashboardIndex",
  components: {
    MyContainer, administrativeDataDashboards,aoataizhang,bcaigouyusuan,cyuangongcan,ddianfei,
    egongwei,fzongweijiqingkuang,gbumenweijishuju, hjixiaolejuan, iyuangonghuodongtaizhang,
    jgongguanjiedai, kyajin, lmanyidupingfen, mgudingzichan, nquyujiankongzicha, oqujianjiankonghucha

  },
  data() {
    return {
      activeName: 'first1',
    };
  },
  methods: {
    tabClickMethod(e) {
      if(e.name == 'first1'){
        this.$nextTick(() => {
          this.$refs.refadministrativeDataDashboards.onRefreshMethod()
        })
      }
    },

  },
};
</script>
<style lang="scss" scoped></style>
