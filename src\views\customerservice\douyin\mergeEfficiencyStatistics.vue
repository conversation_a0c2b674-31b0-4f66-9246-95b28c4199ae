<template>
  <my-container v-loading="pageLoading">
    <!--列表-->
    <ces-table ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
      @sortchange='sortchange' :tableData='groupinquirsstatisticslist' @select='selectchange' :isSelection='false'
      :tableCols='tableCols' :loading="listLoading">
      <template slot='extentbtn'>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;border:none">
            <el-select v-model="filter.groupNameList" placeholder="分组"  multiple clearable filterable :collapse-tags="true">
              <el-option v-for="item in filterGroupList" :key="item.value" :label="item.value" :value="item.value">
              </el-option>
            </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;border:none">
            <el-select v-model="filter.shopCode" placeholder="店铺" style="width:180px;" filterable clearable>
              <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
              </el-option>
            </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;border:none">
            <el-date-picker v-model="filter.sdate" type="daterange" unlink-panels range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" :clearable='false'
              style="width: 320px;" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button>
        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getShopInquirsStatisticsList" />
    </template>
    <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
      :close-on-click-modal="false" v-dialogDrag>
      <div>
        <span>
          <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="服务数据统计" :visible.sync="correspondingStatistics" width="70%" v-dialogDrag>
      <div style="height: 630px;">
        <storeServiceDataStatistics ref="refstoreServiceDataStatistics" />
      </div>
    </el-dialog>

    <el-dialog title="对应客服服务数据统计" :visible.sync="serviceStatistics" width="45%" v-dialogDrag>
      <div style="height: 600px;">
        <customerServiceDataStatistics ref="refcustomerServiceDataStatistics" />
      </div>
    </el-dialog>

  </my-container>
</template>
<script>
import storeServiceDataStatistics from '@/views/customerservice/douyin/storeServiceDataStatistics';
import customerServiceDataStatistics from '@/views/customerservice/douyin/customerServiceDataStatistics';
import { pickerOptions } from '@/utils/tools'
import { getDouYinGroup, getDouYinShopEfficiencyPageList, getDouYinShopEfficiencyChat, exportDouYinShopEfficiencyPageList } from '@/api/customerservice/douyininquirs'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar';
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import dayjs from 'dayjs'
const tableCols = [
  { istrue: true, prop: 'shopName', label: '店名', width: '200', sortable: 'custom', type: 'click', handle: (that, row) => that.tradeName(row) },
  { istrue: true, prop: 'inquirs', label: '人工已接待会话量', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'receiveds', label: '人工已接待人数', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'noSatisfactionRate', label: '不满意率', width: '90', sortable: 'custom', formatter: (row) => (row.noSatisfactionRate).toFixed(2) + "%", type: 'click', handle: (that, row) => that.dissatisfactionRate(row) },
  { istrue: true, prop: 'noSatisfactions', label: '不满意人数', width: '70', sortable: 'custom', formatter: (row) => (row.noSatisfactions).toFixed(0) },
  { istrue: true, prop: 'threeResponseRate', label: '3分钟人工回复率', width: '80', sortable: 'custom', formatter: (row) => (row.threeResponseRate).toFixed(2) + "%" },
  { istrue: true, prop: 'threeResponses', label: '3分钟回复人数', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'responseTime', label: '平均响应时长(秒)', width: '90', sortable: 'custom', formatter: (row) => (row.responseTime).toFixed(2) },
  { istrue: true, prop: 'satisfactionRate', label: '满意率', width: '90', sortable: 'custom', formatter: (row) => (row.satisfactionRate).toFixed(2) + "%" },
  { istrue: true, prop: 'satisfactions', label: '满意人数', width: '80', sortable: 'custom', formatter: (row) => (row.satisfactions).toFixed(0) },
  { istrue: true, prop: 'salesvol', label: '客服销售额(元)', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'ipsCount', label: '询单人数', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'payers', label: '支付人数', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'ipsRate', label: '询单转化率', width: '80', sortable: 'custom', formatter: (row) => (row.ipsRate).toFixed(2) + "%" },
  { istrue: true, prop: 'outTimes', label: '出勤人次', width: '110', sortable: 'custom' },
  { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '100', sortable: 'custom' },
  { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];
export default {
  name: "mergeEfficiencyStatistics",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, buschar, storeServiceDataStatistics, customerServiceDataStatistics },
  props:["partInfo2"],
  data() {
    return {
      serviceStatistics: false,//服务数据统计
      correspondingStatistics: false,//对应客服服务数据统计
      dialogMapVisible: { visible: false, title: "", data: [] },//图表
      that: this,
      filter: {
        sdate: [],
        groupType: null,
        inquirsType: null,
      },
      shopList: [],//店铺
      filterGroupList: [],//分组
      groupinquirsstatisticslist: [],//列表数据
      tableCols: tableCols,//表头
      total: 0,//总数
      summaryarry: { count_sum: 10 },//合计
      pager: { OrderBy: "inquirs", IsAsc: false },//排序
      sels: [], // 列表选中列
      listLoading: false,//加载
      pageLoading: false,//加载
      selids: [],
      pickerOptions,
      isleavegroup:this.partInfo2,//是否离组
    };
  },
  watch:{
        partInfo2(){
            this.isleavegroup = this.partInfo2;
            this.getDouYinGroup();
        }
    },
  async mounted() {
    if (this.filter.sdate.length == 0) {
      //默认给近7天时间
      this.filter.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
      this.filter.endDate = dayjs().format('YYYY-MM-DD')
      this.filter.sdate = [this.filter.startDate, this.filter.endDate]
    }
    this.isleavegroup = this.partInfo2;
    await this.getDouYinGroup();
    await this.getAllShopList();
  },
  methods: {
    dissatisfactionRate(row) {
      this.serviceStatistics = true
      console.log(this.filter, "this.filter1");
      this.$nextTick(() => {
        this.$refs.refcustomerServiceDataStatistics.onSearch(row, this.filter);
      })
    },
    tradeName(row) {
      this.correspondingStatistics = true
      console.log(this.filter, "this.filter2");
      this.$nextTick(() => {
        this.$refs.refstoreServiceDataStatistics.onSearch(row, this.filter);
      })
    },
    // 获取分组
    async getDouYinGroup() {
      let groups = await getDouYinGroup({ groupType: 0,isleavegroup:this.isleavegroup });
      if (groups?.success && groups?.data && groups?.data.length > 0) {
        groups?.data.forEach(f => {
          if (this.filterGroupList.find(w => w.value == f) == null)
            this.filterGroupList.push({ lable: f, value: f });
        });
      }
      groups = await getDouYinGroup({ groupType: 1,isleavegroup:this.isleavegroup });
      if (groups?.success && groups?.data && groups?.data.length > 0) {
        groups?.data.forEach(f => {
          if (this.filterGroupList.find(w => w.value == f) == null)
            this.filterGroupList.push({ lable: f, value: f });
        });
      }
    },
    // 获取店铺
    async getAllShopList() {
      let shops = await getAllShopList();
      this.shopList = [];
      shops.data?.forEach(f => {
        if (f.shopCode && f.platform == 6)
          this.shopList.push(f);
      });
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getShopInquirsStatisticsList();
    },
    getParam() {
      if (this.filter.sdate) {
        this.filter.startDate = this.filter.sdate[0];
        this.filter.endDate = this.filter.sdate[1];
      }
      else {
        this.filter.startDate = null;
        this.filter.endDate = null;
      }
      const para = { ...this.filter };
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      return params;
    },
    async getShopInquirsStatisticsList() {
      let params = this.getParam();
      this.listLoading = true;
      const res = await getDouYinShopEfficiencyPageList(params);
      this.listLoading = false;
      this.total = res.data.total
      this.groupinquirsstatisticslist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    async showchart(row) {
      let params = this.getParam();
      params.shopCode = row.shopCode;
      const res = await getDouYinShopEfficiencyChat(params).then(res => {
        if (res) {
          this.dialogMapVisible.visible = true;
          this.dialogMapVisible.data = res;
          this.dialogMapVisible.title = res.title;
          res.title = "";
        }
      })
      this.dialogMapVisible.visible = true
    },
    async onExport() {
      let params = this.getParam();
      this.listLoading = true
      const res = await exportDouYinShopEfficiencyPageList(params)
      this.listLoading = false
      if (!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '合并店效率统计数据' + new Date().toLocaleString() + '.xlsx');
      aLink.click()
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 60px;
}
</style>
