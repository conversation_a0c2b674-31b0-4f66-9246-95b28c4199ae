<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <div class="top">
        <el-date-picker class="publicCss" v-model="filter.yearMonth" type="month" format="yyyyMM" value-format="yyyyMM"
          placeholder="选择月份"></el-date-picker>
        <!-- <el-select filterable v-model="filter.platform" placeholder="请选择平台" disabled clearable class="publicCss">
          <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select> -->
        <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺" class="publicCss">
          <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
            :value="item.shopCode"></el-option>
        </el-select>
        <el-input v-model="filter.serialNumberOrder" placeholder="商品订单编号" class="publicCss" />
        <el-input v-model="filter.proCode" placeholder="宝贝ID" class="publicCss" />
        <el-select filterable clearable v-model="filter.isNullProCode" placeholder="请选择空白ID" class="publicCss">
          <el-option label="空白ID" :value="true"></el-option>
          <el-option label="非空白ID" :value="false"></el-option>
        </el-select>
        <el-select filterable clearable v-model="filter.Status" placeholder="请选择状态" class="publicCss">
          <el-option label="正常" :value="1"></el-option>
          <el-option label="不正常" :value="0"></el-option>
        </el-select>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onExport">汇总导出</el-button>
      </div>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :id="'sellBackgroundDetail202302031421'" :tablekey="'sellBackgroundDetail202302031421'"
      :tableData='ZTCKeyWordList' :showsummary='true' :summaryarry='summaryarry' @select='selectchange'
      :isSelection='false' :tableCols='tableCols' :loading="listLoading" :isSelectColumn='false'>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>
  </my-container>
</template>
<script>
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList, getList as getshopList } from '@/api/operatemanage/base/shop';
import { formatPlatform, formatLink, platformlist } from "@/utils/tools";
import { getSaleDetailPDD as getPageList, getSaleDetail_Ks, ExportSaleDetail_Ks } from '@/api/monthbookkeeper/financialDetail'
const tableCols = [
  { istrue: true, prop: 'yearMonth', label: '年月', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'orderNumber', label: '订单号', sortable: 'custom', width: '180' },
  { istrue: true, prop: 'proCode', label: '宝贝ID', sortable: 'custom', width: '120' },
  { istrue: true, prop: 'orderPayTime', label: '支付时间', sortable: 'custom', width: '150' },
  { istrue: true, prop: 'orderStatus', label: '订单状态', sortable: 'custom', width: '120' },
  { istrue: true, prop: 'actualPayAmount', label: '实付款', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'expressFeeAmount', label: '快递费', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'shopPreferentialAmount', label: '店铺优惠', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'platformSubsidyAmount', label: '平台补贴', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'talentSubsidyAmount', label: '主播补贴', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'mixEventDiscountsAmount', label: '混资活动优惠', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'paymentDiscountAmount', label: '支付优惠', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'channels', label: '渠道', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'payMethod', label: '支付方式', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'transactionQty', label: '成交数量', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'deliveryTime', label: '发货时间', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'afterSalesStatus', label: '售后状态', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'proCode', label: '商品ID', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'goodsCode', label: 'SKU编码', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'ygTgCommissionAmount', label: '预估推广佣金', sortable: 'custom', width: '80' },
];
export default {
  name: "sellBackgroundDetail",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
  data() {
    return {
      that: this,
      filter: {
        platform: 14,
        yearMonth: null,
        shopCode: null,
        isNullProCode: null
      },
      shopList: [],
      userList: [],
      groupList: [],
      platformlist: platformlist,
      ZTCKeyWordList: [],
      tableCols: tableCols,
      summaryarry: {},
      total: 0,
      pager: { OrderBy: "id", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
    };
  },
  async mounted() {
    this.onchangeplatform();
  },
  methods: {
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    async onchangeplatform() {
      const res1 = await getshopList({ platform: 14, CurrentPage: 1, PageSize: 100000 });
      this.shopList = res1.data.list
    },
    // async getShopList(){
    //   const res1 = await getAllShopList();
    //   this.shopList=[];
    //     res1.data?.forEach(f => {
    //       if(f.isCalcSettlement&&f.shopCode)
    //           this.shopList.push(f);
    //     });
    // },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      let params = this.getCondition();
      this.listLoading = true;
      const res = await getSaleDetail_Ks(params);
      this.listLoading = false;
      this.total = res.data?.total
      this.ZTCKeyWordList = res.data?.list;
      this.summaryarry = res.data?.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    getCondition() {
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...this.filter,
      };
      return params;
    },
    async onExport(opt) {
      if (!this.filter.yearMonth) {
        this.$message({ message: "请先选择月份", type: "warning" });
        return;
      }
      let pars = this.getCondition();
      if (pars === false) {
        return;
      }
      const params = { ...pars, ...opt };
      let res = await ExportSaleDetail_Ks(params);
      if (!res?.data) {
        return
      }
      this.$message({ message: "正在后台导出中，请点击头像-在下载管理中查看", type: "success" });
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 140px;
    margin-right: 5px;
  }
}
</style>
