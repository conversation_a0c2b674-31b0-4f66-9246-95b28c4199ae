<template>
  <my-container>
    <!--顶部操作-->
    <template #header>
      <div class=".top">
        <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
        <el-form-item>
          <el-switch v-model="Filter.switchshow" inactive-text="稽查数据" active-text="客服数据" @change="changeShowgroup">
          </el-switch>
        </el-form-item>

        <el-form-item label="审核时间:">
          <el-date-picker style="width: 320px" v-model="Filter.timerange" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>

        <el-form-item label="客服:" v-if="isActive">
          <el-select v-model="Filter.nameList" placeholder="请选择" class="el-select-content" clearable filterable multiple
            collapse-tags>
            <el-option v-for="item in userNameList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item label="审核人:" v-if="isInactive">
          <el-select v-model="Filter.initialOperatorList" placeholder="请选择" class="el-select-content" clearable
            filterable multiple collapse-tags>
            <el-option v-for="item in initialList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item label="分组:">
          <el-select v-model="Filter.groupName" placeholder="请选择" class="el-select-content" clearable filterable>
            <el-option v-for="item in groupNameList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport()"
            v-if="Filter.switchshow ? checkPermission(['api:customerservice:UnPayOrder:PlatformPunishExport']) : checkPermission(['api:customerservice:UnPayOrder:JudgmentDataStatisticsExport'])">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    </template>
    <!-- </template> -->
    <!--列表-->
    <!-- <Ces-table ref="table" :that="that" :isIndex="true" @sortchange="sortchange" :tableData="tableData"
      :showsummary="true" :summaryarry="summaryarry" :tableCols="tableCols" :loading="listLoading">
    </Ces-table> -->

    <!-- <div style="height: calc(100% - 8%);"> -->
      <vxetablebase ref="table" v-if="tableshow" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
        @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
        :isSelectColumn="true" :loading="listLoading" :summaryarry='summaryarry' :showsummary='true'>
      </vxetablebase>
    <!-- </div> -->
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="getPageList" />
    </template>


    <!-- <AuditDataStatistics :v-if="auditStatistics" :isShow="auditStatistics" @closeDialog="auditStatistics = false" ref="recordithRef"></AuditDataStatistics> -->


    <el-dialog :title="getTitle" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="55%" height="600px"
      v-dialogDrag append-to-body>
      <AuditStatistics ref="AuditStatistics" :totalROW="totalROW" style="z-index:10000;height:600px" />
    </el-dialog>

    <el-dialog :title="firstTitle" v-if="dialogFirstHisVisible" :visible.sync="dialogFirstHisVisible" width="55%"
      height="600px" v-dialogDrag append-to-body>
      <AuditFirstStatistics ref="AuditFirstStatistics" :firstROW="firstROW" style="z-index:10000;height:600px" />
    </el-dialog>

    <el-dialog :title="reviewTitle" v-if="dialogReviewHisVisible" :visible.sync="dialogReviewHisVisible" width="55%"
      height="600px" v-dialogDrag append-to-body>
      <AuditReviewStatistics ref="AuditReviewStatistics" :ReviewROW="ReviewROW" style="z-index:10000;height:600px" />
    </el-dialog>

    <el-dialog :title="judgeTitle" v-if="dialogJudgeHisVisible" :visible.sync="dialogJudgeHisVisible" width="55%"
      height="600px" v-dialogDrag append-to-body>
      <AuditJudgeStatistics ref="AuditJudgeStatistics" :JudgeROW="JudgeROW" style="z-index:10000;height:600px" />
    </el-dialog>

  </my-container>
</template>
<script>
import AuditStatistics from "@/views/customerservice/chartCheck/SalesDialog/AuditStatistics.vue";
import AuditFirstStatistics from "@/views/customerservice/chartCheck/SalesDialog/AuditFirstStatistics.vue";
import AuditReviewStatistics from "@/views/customerservice/chartCheck/SalesDialog/AuditReviewStatistics.vue";
import AuditJudgeStatistics from "@/views/customerservice/chartCheck/SalesDialog/AuditJudgeStatistics.vue";
import MyContainer from "@/components/my-container";
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import CesTable from "@/components/Table/table.vue";
import { log } from 'mathjs';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
  getUserNameDataStatisticsPageList,
  getJudgmentDataStatisticsPageList,
  getUnPayOrderUserNameList,
  getUnPayOrderInitialOperatorList,
  getUnPayOrderGroupNameList,
  getInitialOperatorGroupNameList,
  userNameDataStatisticsExport,
  judgmentDataStatisticsExport,
} from "@/api/customerservice/chartCheck";

//客服列头
const tableCols = [
  {
    istrue: true, prop: 'minLastOperatorTime', label: '审核时间', width: '320', sortable: 'custom', align: "center",
    formatter: (row) => {
      const minTime = row.minLastOperatorTime ?? '';
      const maxTime = row.maxLastOperatorTime ?? '';
      return minTime + '—' + maxTime
    },
  },
  { istrue: true, prop: 'userName', label: '客服', width: '100', sortable: 'custom', align: "center" },
  { istrue: true, prop: 'groupManager', label: '客服组长', width: '100', sortable: 'custom', align: "center" },
  { istrue: true, prop: 'groupName', label: '分组', width: 'auto', sortable: 'custom', align: "center" },
  {
    istrue: true, prop: "totalCount", label: "审核总量", width: "180", type: 'click', handle: (that, row) => that.showDetail(row, -1), sortable: 'custom', align: "center"
  },
  { istrue: true, prop: 'salesCount', label: '售前审核量', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showDetail(row, 0), align: "center" },
  { istrue: true, prop: 'afterSalesCount', label: '售后审核量', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showDetail(row, 1), align: "center" },
  { istrue: true, prop: 'initialOk', label: '审核合格', width: '100', type: 'click', handle: (that, row) => that.showFirstDetail(row, 1), sortable: 'custom', align: "center" },
  { istrue: true, prop: 'initialNo', label: '审核不合格', width: '120', type: 'click', handle: (that, row) => that.showFirstDetail(row, 2), sortable: 'custom', align: "center" },
  {
    istrue: true, prop: 'initialOkRate', label: '审核合格率', width: '120', sortable: 'custom', align: "center",
    formatter: (row) => {
      const initialOkRate = row.initialOkRate ?? 0;
      return initialOkRate + '%'
    },
  },
 /* { istrue: true, prop: 'finalOk', label: '复审合格', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showReviewDetail(row, 1), tipmesg: '统计有审核结果的数据，未审核的不统计。', sortable: 'custom', align: "center" },
  { istrue: true, prop: 'finalNo', label: '复审不合格', width: '120', sortable: 'custom', type: 'click', handle: (that, row) => that.showReviewDetail(row, 2), tipmesg: '统计有审核结果的数据，未审核的不统计。', sortable: 'custom', align: "center" },
  {
    istrue: true, prop: 'finalOkRate', label: '复审合格率', width: '120', sortable: 'custom', align: "center",
    formatter: (row) => {
      const finalOkRate = row.finalOkRate ?? 0;
      return finalOkRate + '%'
    },
  },
  { istrue: true, prop: 'judgmentOk', label: '评判合格', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showJudgeDetail(row, 1), tipmesg: '统计有审核结果的数据，未审核的不统计。', sortable: 'custom', align: "center" },
  { istrue: true, prop: 'judgmentNo', label: '评判不合格', width: '120', sortable: 'custom', type: 'click', handle: (that, row) => that.showJudgeDetail(row, 2), tipmesg: '统计有审核结果的数据，未审核的不统计。', sortable: 'custom', align: "center" },
  {
    istrue: true, prop: 'judgmentOkRate', label: '评判合格率', width: '120', sortable: 'custom', align: "center",
    formatter: (row) => {
      const judgmentOkRate = row.judgmentOkRate ?? 0;
      return judgmentOkRate + '%'
    },
  },*/
  { istrue: true, prop: 'totalOkCount', label: '总合格数量', width: '100', sortable: 'custom', align: "center" },
  {
    istrue: true, prop: 'userOkRate', label: '客服合格率',  sortable: 'custom', align: "center",
    formatter: (row) => {
      const userOkRate = row.userOkRate ?? 0;
      return userOkRate + '%'
    },
  },
];

//稽查列头
const tableCols2 = [
  {
    istrue: true, prop: 'minLastOperatorTime', label: '审核时间', width: '320', sortable: 'custom', align: "center",
    formatter: (row) => {
      const minTime = row.minLastOperatorTime ?? '';
      const maxTime = row.maxLastOperatorTime ?? '';
      return minTime + '—' + maxTime
    },
  },
  { istrue: true, prop: 'operator', label: '审核人(稽查)', width: '100', sortable: 'custom', align: "center" },
  { istrue: true, prop: 'operatorGroupName', label: '分组', width: 'auto', sortable: 'custom', align: "center" },
  { istrue: true, prop: 'operatorManager', label: '稽查组长', width: '100', sortable: 'custom', align: "center" },
  {
    type: "button", label: "审核总量", ishide: 'function', prop: 'totalCount', align: "center", width: "250", sortable: 'custom',

    btnList: [
      { color: 'black', prop: 'totalCount', itemStyle: { cursor: "pointer", color: 'blue' },handle: (that, row) => that.showDetail(row, 2) , },
/*      { color: 'black', label: "(", itemStyle: { cursor: "default", color: 'black' }, },
      { color: 'black', label: "审核", itemStyle: { cursor: "default", color: 'black' }, },
      { color: 'black', prop: 'initialCount', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.showDetail(row, 2) },
      { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
      { color: 'black', label: "复审", itemStyle: { cursor: "default", color: 'black' }, },
      { color: 'black', prop: 'finalCount', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.showDetail(row, 3) },
      { color: 'black', label: "/", itemStyle: { cursor: "default", color: 'black' }, },
      { color: 'black', label: "评判", itemStyle: { cursor: "default", color: 'black' }, },
      { color: 'black', prop: 'judgmentCount', itemStyle: { cursor: "pointer", color: 'blue' }, handle: (that, row) => that.showDetail(row, 4) },
      { color: 'black', label: ")", itemStyle: { cursor: "default", color: 'black' }, },*/
    ],
  },
  { istrue: true, prop: 'salesCount', label: '售前审核量', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showDetail(row, 0), align: "center" },
  { istrue: true, prop: 'afterSalesCount', label: '售后审核量', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showDetail(row, 1), align: "center" },
  { istrue: true, prop: 'initialOk', label: '审核合格', width: '90', sortable: 'custom', type: 'click', handle: (that, row) => that.showFirstDetail(row, 1), sortable: 'custom', align: "center" },
  { istrue: true, prop: 'initialNo', label: '审核不合格', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showFirstDetail(row, 2), sortable: 'custom', align: "center" },
  {
    istrue: true, prop: 'initialOkRate', label: '审核合格率', width: '100', sortable: 'custom',
    formatter: (row) => {
      const initialOkRate = row.initialOkRate ?? 0;
      return initialOkRate + '%'
    },
  },
  /*{ istrue: true, prop: 'finalOk', label: '复审合格', width: '100', sortable: 'custom', align: "center", type: 'click', handle: (that, row) => that.showReviewDetail(row, 1), tipmesg: '统计有审核结果的数据，未审核的不统计。', sortable: 'custom' },
  { istrue: true, prop: 'finalNo', label: '复审不合格', width: '120', sortable: 'custom', align: "center", type: 'click', handle: (that, row) => that.showReviewDetail(row, 2), tipmesg: '统计有审核结果的数据，未审核的不统计。', sortable: 'custom' },
  {
    istrue: true, prop: 'finalOkRate', label: '复审合格率', width: '120', sortable: 'custom',
    formatter: (row) => {
      const finalOkRate = row.finalOkRate ?? 0;
      return finalOkRate + '%'
    },
  },
  { istrue: true, prop: 'judgmentOk', label: '评判合格', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showJudgeDetail(row, 1), tipmesg: '统计有审核结果的数据，未审核的不统计。', sortable: 'custom', align: "center" },
  { istrue: true, prop: 'judgmentNo', label: '评判不合格', width: '120', sortable: 'custom', type: 'click', handle: (that, row) => that.showJudgeDetail(row, 2), tipmesg: '统计有审核结果的数据，未审核的不统计。', sortable: 'custom', align: "center" },
  {
    istrue: true, prop: 'judgmentOkRate', label: '评判合格率', width: '120', sortable: 'custom',
    formatter: (row) => {
      const judgmentOkRate = row.judgmentOkRate ?? 0;
      return judgmentOkRate + '%'
    },
  },*/
  { istrue: true, prop: 'totalOkCount', label: '总合格数量', width: '100', sortable: 'custom', align: "center" },
  {
    istrue: true, prop: 'userOkRate', label: '稽查正确率',  sortable: 'custom',
    formatter: (row) => {
      const userOkRate = row.userOkRate ?? 0;
      return userOkRate + '%'
    },
  },
];

export default {
  name: "salesFifth",
  components: { MyContainer, CesTable, AuditStatistics, AuditFirstStatistics, AuditReviewStatistics, AuditJudgeStatistics, vxetablebase },
  data() {
    return {
      that: this,
      tableshow: true,
      Filter: {
        timerange: [
          formatTime(dayjs().startOf("month"), "YYYY-MM-DD"),
          formatTime(new Date(), "YYYY-MM-DD"),
        ],
        userName: null,
        initialOperator: null,
        groupName: null,
        switchshow: true,
      },
      userNameList: [],
      initialList: [],
      groupNameList: [],
      pickerOptions: {
        disabledDate(date) {
          // 设置禁用日期
          const start = new Date("1970/1/1");
          const end = new Date("9999/12/31");
          return date < start || date > end;
        },
      },
      defaultDate: new Date(),
      //默认客服
      isActive: true,
      isInactive: false,
      tableCols: [],
      tableData: [],
      summaryarry: null,
      listLoading: false,
      total: 0,
      nameList: [],
      groupName: [],
      dialogHisVisible: false,
      totalROW: {},
      dialogFirstHisVisible: false,
      firstROW: {},
      dialogReviewHisVisible: false,
      ReviewROW: {},
      dialogJudgeHisVisible: false,
      JudgeROW: {},
      DetailTimeStart: null,
      DetailTimeEnd: null,
      firstTitle: '',
      reviewTitle: '',
      judgeTitle: '',
      getTitle: '',
    };
  },
  async mounted() {
    await this.init();
    await this.onSearch()
  },
  methods: {
    async init() {
      var date1 = new Date(); date1.setDate(date1.getDate() - 6);
      var date2 = new Date(); date2.setDate(date2.getDate());
      this.Filter.timerange = [];
      this.Filter.timerange[0] = date1;
      this.Filter.timerange[1] = date2;
    },
    async selectList() {
      const para = {};
      if (this.Filter.timerange) {
        para.timeStart = this.Filter.timerange[0];
        para.timeEnd = this.Filter.timerange[1];
      }

      if (this.Filter.switchshow) {
        const groupName = await getUnPayOrderGroupNameList(para);
        const userName = await getUnPayOrderUserNameList(para);
        this.groupNameList = groupName.data;//分组
        this.userNameList = userName.data;//客服客服
      } else {

        const initialOperator = await getUnPayOrderInitialOperatorList(para);
        const operatorGroupName = await getInitialOperatorGroupNameList(para);
        this.initialList = initialOperator.data;//稽查审核人
        this.groupNameList = operatorGroupName.data;//分组
      }

    },
    changeShowgroup() {
      this.tableshow = false;
      this.$refs.pager.setPage(1);

      this.Filter.groupName = null;
      this.Filter.initialList = null;
      this.Filter.userNameList = null;
      if (this.Filter.switchshow) {
        this.isActive = true;
        this.isInactive = false;
      } else {
        this.isActive = false;
        this.isInactive = true;
      }
      this.onSearch();
    },
    onSearch() {
      this.getPageList()
      this.selectList();
    },
    async onExport() {  //导出
      const para = { ...this.Filter };
      if (this.Filter.timerange) {
        para.timeStart = this.Filter.timerange[0];
        para.timeEnd = this.Filter.timerange[1];
      }

      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para
      };
      var res = this.Filter.switchshow ? await userNameDataStatisticsExport(params) : await judgmentDataStatisticsExport(params);
      const aLink = document.createElement("a");
      let blob = new Blob([res], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);

      var excelName = this.Filter.switchshow ? '客服数据' : '稽查数据'
      aLink.setAttribute(
        "download",
        excelName + new Date().toLocaleString() + ".xlsx"
      );
      aLink.click();
    },
    sortchange(column) {    //表头排序
      if (!column.order) this.pager = {};
      else
        this.pager = {
          orderBy: column.prop,
          isAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.onSearch();
    },
    async getPageList() {
      let newArr = this.Filter.switchshow ? tableCols : tableCols2

        this.tableCols = newArr
      const para = { ...this.Filter };
      if (this.Filter.timerange) {
        para.timeStart = this.Filter.timerange[0];
        para.timeEnd = this.Filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para
      };
      this.listLoading = true;
      const res = this.Filter.switchshow ? await getUserNameDataStatisticsPageList(params) : await getJudgmentDataStatisticsPageList(params);
      this.listLoading = false;
      this.total = res.data.total
      this.tableData = res.data.list;
      this.summaryarry = res.data.summary;
      this.tableshow = true;
    },
    showDetail(row, num) {

      if (this.getTitle = num === -1) this.getTitle = '审核总量';
      else if (this.getTitle = num === 0) this.getTitle = '售前审核量';
      else if (this.getTitle = num === 1) this.getTitle = '售后审核量';
      else if (this.getTitle = num === 2) this.getTitle = '审核';
      else if (this.getTitle = num === 3) this.getTitle = '复审';
      else if (this.getTitle = num === 4) this.getTitle = '评判';
      this.DetailTimeStart = null;
      this.DetailTimeEnd = null;
      if (this.Filter.timerange != null && this.Filter.timerange.length > 0) {

        this.DetailTimeStart = this.Filter.timerange[0]
        this.DetailTimeEnd = this.Filter.timerange[1]
      }
      const totalROW = {
        totalType: num,
        switchshow: this.Filter.switchshow,
        timeStart: this.DetailTimeStart ? formatTime(this.DetailTimeStart, "YYYY-MM-DD") : null,
        timeEnd: this.DetailTimeEnd ? formatTime(this.DetailTimeEnd, "YYYY-MM-DD") : null,
        operator: this.Filter.switchshow ? row.userName : row.operator,
        groupName: this.Filter.groupName,
        initialOperator: row.initialOperator,
        initialOperatorGroupName: row.initialOperatorGroupName
      };

      this.totalROW = totalROW;
      this.dialogHisVisible = true;
    },

    showFirstDetail(row, num) {
      this.firstTitle = num === 1 ? '审核合格' : '审核不合格';
      this.DetailTimeStart = null;
      this.DetailTimeEnd = null;
      if (this.Filter.timerange != null && this.Filter.timerange.length > 0) {
        this.DetailTimeStart = this.Filter.timerange[0]
        this.DetailTimeEnd = this.Filter.timerange[1]
      }
      const firstROW = {
        switchshow: this.Filter.switchshow,
        timeStart: this.DetailTimeStart ? formatTime(this.DetailTimeStart, "YYYY-MM-DD") : null,
        timeEnd: this.DetailTimeEnd ? formatTime(this.DetailTimeEnd, "YYYY-MM-DD") : null,
        // userName: row.userName,
        operator: this.Filter.switchshow ? row.userName : row.operator,
        groupName: this.Filter.groupName,
        auditType: num,
        initialOperator: row.initialOperator,
        initialOperatorGroupName: row.initialOperatorGroupName
      };

      this.firstROW = firstROW;
      this.dialogFirstHisVisible = true;
    },

    showReviewDetail(row, num) {
      this.reviewTitle = num === 1 ? '复审合格' : '复审不合格';
      this.DetailTimeStart = null;
      this.DetailTimeEnd = null;
      if (this.Filter.timerange != null && this.Filter.timerange.length > 0) {
        this.DetailTimeStart = this.Filter.timerange[0]
        this.DetailTimeEnd = this.Filter.timerange[1]
      }
      const ReviewROW = {
        switchshow: this.Filter.switchshow,
        timeStart: this.DetailTimeStart ? formatTime(this.DetailTimeStart, "YYYY-MM-DD") : null,
        timeEnd: this.DetailTimeEnd ? formatTime(this.DetailTimeEnd, "YYYY-MM-DD") : null,
        // userName: row.userName,
        operator: this.Filter.switchshow ? row.userName : row.operator,
        groupName: this.Filter.groupName,
        auditType: num,
        initialOperator: row.initialOperator,
        initialOperatorGroupName: row.initialOperatorGroupName
      };
      this.ReviewROW = ReviewROW;
      this.dialogReviewHisVisible = true;
    },
    showJudgeDetail(row, num) {
      this.judgeTitle = num === 1 ? '评判合格' : '评判不合格';
      this.DetailTimeStart = null;
      this.DetailTimeEnd = null;
      if (this.Filter.timerange != null && this.Filter.timerange.length > 0) {
        this.DetailTimeStart = this.Filter.timerange[0]
        this.DetailTimeEnd = this.Filter.timerange[1]
      }
      const JudgeROW = {
        switchshow: this.Filter.switchshow,
        timeStart: this.DetailTimeStart ? formatTime(this.DetailTimeStart, "YYYY-MM-DD") : null,
        timeEnd: this.DetailTimeEnd ? formatTime(this.DetailTimeEnd, "YYYY-MM-DD") : null,
        // userName: row.userName,
        operator: this.Filter.switchshow ? row.userName : row.operator,
        groupName: this.Filter.groupName,
        auditType: num,
        initialOperator: row.initialOperator,
        initialOperatorGroupName: row.initialOperatorGroupName
      };
      this.JudgeROW = JudgeROW;
      this.dialogJudgeHisVisible = true;
    },

  }

};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

::v-deep .mycontainer {
  position: relative;
}

.uptime {
  font-size: 14px;
  position: absolute;
  right: 30px;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 45px;
}
</style>
