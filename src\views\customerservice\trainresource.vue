<template>
  <div class="ces-main">
   <el-table :data="data">
      <el-table-column type="index" width="60"></el-table-column>
       
      <el-table-column
        prop="fileName"
        sortable
        label="资料名称"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="createdTime"
        sortable
        label="上传时间"
        width="220"
      ></el-table-column
    >
    <el-table-column prop="xiaz" label="下载资料" min-width="120" align="center" >
      <template  #default="{ row }" >
         <a target="_blank" :href="row.filePath">下载</a>
         <!-- window.open("../static/excel/订单数据导入模板.xlsx", "_self"); -->
   </template> 

 
</el-table-column>
     
    
    </el-table>
    <div class="block">
      <el-pagination
        layout="prev, pager, next"
        @current-change="changehisPage"
        :total="pager.total"
        :page-size="pager.pagesize"
      >
      </el-pagination>
    </div>
  </div>
</template>
 
        
   
<script>
import { getTrainResourceDataList } from "@/api/customerservice/trainplan";
export default {
  data() {
    return {
      that: this,

      loading: false,
      productid:'',
      total:0,
pageSize:5,
      pager: {
        total: 0,
        pageIndex: 1,
        pageSize: 5,
        OrderBy: "CreatedTime",
        isAsc:false,
        Trainstatus: 2,
      },
      data: [],
    };
  },
  components: {},
  computed: {},
  methods: {
    clear(){
  this.data=[];

    },
    changePage(e) {

     var that = this;
     // that.data = [];
      

      const params = {
         
        ...this.pager,
      };
      params.productid = that.productid;
      this.pager.pageIndex=e;

      getTrainResourceDataList(params).then((res) => {
          that.data = res.data.list;
        that.pager.total=res.data.total;
        that.total=res.data.total;
      });



    },
   changehisPage(e){
       var that = this;
      this.pager.pageIndex=e;
      that.data = [];
      // var ProductID=this.$router.query.
    that.pager.total=0;
      
   
      const params = {
         
        ...this.pager,
      };
      params.productid = productid;
       getTrainResourceDataList(params).then(res=>{
        
        
        that.data = res.data.list;
        that.pager.total=res.data.total;
        that.total=res.data.total;



       });

   },
    init(productid) {
      var that = this;
      this.pager.pageIndex=0;
      that.data = [];
      // var ProductID=this.$router.query.
    that.pager.total=0;
      
    console.log("what");

      const params = {
         
        ...this.pager,
      };
      params.productid = productid;
       getTrainResourceDataList(params).then(res=>{
        
        
        that.data = res.data.list;
        that.pager.total=res.data.total;
        that.total=res.data.total;



       });

     
    },
  },
  mounted() {
   // this.data = [];
   // this.pageIndex=0;
    //this.init();
  },
};
</script>