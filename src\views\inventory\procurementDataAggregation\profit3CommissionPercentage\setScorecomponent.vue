<template>
  <div style="display: flex;flex-direction: column;" v-loading="loading">
    <div>得分权重配置</div>
    <el-table :data="tableData" style="width: 100%" max-height="400" border>
      <el-table-column width="100">
        <template slot-scope="scope">
          {{ scope.row.scoreType }}
        </template>
      </el-table-column>
      <el-table-column label="上限">
        <template #header>
          <el-tooltip class="item" effect="dark" content="该字段第一名的得分" placement="top-end">
            <span><i class="el-icon-question"></i></span>
          </el-tooltip>
          <span>上限</span>
        </template>
        <template slot-scope="scope">
          <inputNumberYh v-model="scope.row.maxScore" :max="100" :placeholder="'请输入上限'" class="publicCss"
            @input="onMaxScoreMethod($event, scope.$index)" />
        </template>
      </el-table-column>
      <el-table-column label="带宽">
        <template #header>
          <el-tooltip class="item" effect="dark" content="一个名次的间隔分值" placement="top-end">
            <span><i class="el-icon-question"></i></span>
          </el-tooltip>
          <span>带宽</span>
        </template>
        <template slot-scope="scope">
          <inputNumberYh v-model="scope.row.deductScore" :max="100" :placeholder="'请输入带宽'" class="publicCss" />
        </template>
      </el-table-column>
      <el-table-column label="下限">
        <template #header>
          <el-tooltip class="item" effect="dark" content="该字段最低得分" placement="top-end">
            <span><i class="el-icon-question"></i></span>
          </el-tooltip>
          <span>下限</span>
        </template>
        <template slot-scope="scope">
          <inputNumberYh v-model="scope.row.minScore" :min="0" :max="100" :placeholder="'请输入下限'" class="publicCss" />
        </template>
      </el-table-column>
      <el-table-column prop="rankingOrder" label="排名" width="100">
        <template #header>
          <el-tooltip class="item" effect="dark" content="出现多人分值相同的字段，取分值相同人员的其他字段排名作参考，排名越小，优先级越大" placement="top-end">
            <span><i class="el-icon-question"></i></span>
          </el-tooltip>
          <span>排名</span>
        </template>
        <template slot-scope="scope">
          <inputNumberYh v-model="scope.row.rankingOrder" :min="0" :max="100" :placeholder="'排名'" class="publicCss" />
        </template>
      </el-table-column>
    </el-table>
    <div v-if="!selectGroup">扣分权重配置</div>
    <el-table v-if="!selectGroup" :data="deductPoints" style="width: 100%" max-height="250" border>
      <el-table-column width="100">
        <template slot-scope="scope">
          {{ scope.row.scoreType }}
        </template>
      </el-table-column>
      <el-table-column label="标准数量">
        <template #header>
          <el-tooltip class="item" effect="dark" content="需要达标的最低标准数" placement="top-end">
            <span><i class="el-icon-question"></i></span>
          </el-tooltip>
          <span>标准数量</span>
        </template>
        <template slot-scope="scope">
          <inputNumberYh v-model="scope.row.deductScoreStartCount" :min="0" :max="100" :placeholder="'请输入标准数量'"
            class="publicCss" @input="onMaxScoreMethod($event, scope.$index)" />
        </template>
      </el-table-column>
      <el-table-column label="带宽">
        <template slot-scope="scope">
          <inputNumberYh v-model="scope.row.deductScore" :min="0" :max="100" :placeholder="'请输入带宽'" class="publicCss" />
        </template>
      </el-table-column>
      <el-table-column label="公司实际回收标准" prop="noDeductScoreStartCount">
        <template #header>
          <el-tooltip class="item" effect="dark" content="公司实际回收需要达到的最低标准" placement="top-end">
            <span><i class="el-icon-question"></i></span>
          </el-tooltip>
          <span>公司实际回收标准</span>
        </template>
        <template slot-scope="scope">
          <inputNumberYh v-model="scope.row.noDeductScoreStartCount" :min="0"
            :max="scope.row.scoreType == '滞销退款扣分' ? 9999999 : 100" placeholder="公司实际回收标准" class="publicCss"
            :disabled="scope.row.scoreType == '摘品扣分'" />
        </template>
      </el-table-column>
    </el-table>
    <div style="display: flex;justify-content: center;gap: 20px;margin-top: 20px;">
      <!-- <el-button type="primary" @click="onSaveMethod">确定</el-button>
      <el-button @click="onCancelMethod">取消</el-button> -->
      <el-button type="primary" @click="onCalculate(1)">试算</el-button>
      <el-button type="success" @click="onCalculate(2)">计算</el-button>
    </div>
    <el-dialog :title="calculate ? '试算' : '计算'" :visible.sync="computeVisible" width="20%" v-dialogDrag append-to-body
      :close-on-click-modal="false">
      <div style="height: 100px;display: flex;align-items: center;justify-content: center;gap: 15px;">
        <div>{{ calculate ? '试算' : '计算' }}日期：</div>
        <div>
          <el-date-picker style="width: 100%" v-model="startTime" :clearable="false" type="month"
            :placeholder="calculate ? '请选择试算月份' : '请选择计算月份'" icon="el-icon-date" format="yyyy-MM"
            value-format="yyyy-MM"></el-date-picker>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="computeVisible = false">取 消</el-button>
        <el-button type="primary" @click="computeMethod">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import dayjs from 'dayjs'
import {
  addProfit3CommissionPercentageScoreSet,
  getProfit3CommissionPercentageInitSetScore,
  getProfit3GroupDetailInitSetScore,
  addProfit3GroupDetailScoreSet,
  calPurchaseSumProfit3BrandStopRpt,
  calPurchaseSumProfit3BrandRpt,
  calPurchaseSumProfit3GroupRpt,
  calPurchaseSumProfit3GroupStopRpt
} from '@/api/inventory/purchaseSummary';
export default {
  name: 'setScorecomponent',
  components: {
    vxetablebase, inputNumberYh
  },
  props: {
    userNameList: {
      type: Array,
      default() { return [] }
    },
    selectGroup: {
      type: Boolean,
      default() { return false }
    }
  },
  data() {
    return {
      tableData: [],
      loading: false,
      calculate: false,
      computeVisible: false,
      deductPoints: [],
      startTime: null,
      debouncedCompute: null
    }
  },
  created() {
    this.debouncedCompute = _.debounce(async () => {
      let information = this.calculate ? '试算' : '计算';
      if (!this.startTime) {
        this.$message({ message: `请选择${information}日期`, type: "error" });
        return;
      }
      let calFunction;
      let params;
      if (this.selectGroup) {
        calFunction = this.calculate ? calPurchaseSumProfit3GroupStopRpt : calPurchaseSumProfit3GroupRpt;
        params = {
          startTime: this.startTime,
          scoreSets: {
            scoreSets: this.tableData
          }
        }
      } else {
        calFunction = this.calculate ? calPurchaseSumProfit3BrandStopRpt : calPurchaseSumProfit3BrandRpt;
        params = {
          startTime: this.startTime,
          scoreSets: {
            scoreSets: this.tableData,
            deductScoreSets: this.deductPoints
          }
        }
      }
      const { success: calcSuccess } = await calFunction(params);
      if (calcSuccess) {
        this.$message({ message: `操作成功，请稍后查看${information}结果.....`, type: "success" });
        this.computeVisible = false;
        this.$emit('onCancelMethod', 1)
      } else {
        this.$message.error('计算失败');
      }
    }, 1000);
  },
  async mounted() {
    await this.getList();
  },
  methods: {
    // 计算
    async onCalculate(val) {
      this.startTime = dayjs().format('YYYY-MM');
      if (val == 1) {
        this.calculate = true;
      } else if (val == 2) {
        this.calculate = false;
      }
      this.computeVisible = true
    },
    onMaxScoreMethod(e, i) {
      if (e == 0) {
        this.tableData[i].deductScore = 0;
        this.tableData[i].minScore = 0;
      }
    },
    async onSaveMethod() {
      let hasError = false;
      const fieldsToCheck = [
        { field: 'rankingOrder', label: '排名' },
        { field: 'minScore', label: '下限' },
        { field: 'deductScore', label: '带宽' },
        { field: 'maxScore', label: '上限' }
      ];
      for (const { field, label } of fieldsToCheck) {
        for (let i = 0; i < this.tableData.length; i++) {
          if (this.tableData[i][field] == null || this.tableData[i][field] === '') {
            this.$message.error(`${label}（第${i + 1}行）不能为空`);
            hasError = true;
            break;
          }
        }
        if (hasError) break;
      }
      if (hasError) {
        return;
      }
      this.loading = true;
      //小组明细
      if (this.selectGroup) {
        const { data, success } = await addProfit3GroupDetailScoreSet(this.tableData);
        this.loading = false;
        if (!success) return
        this.$message({ message: '操作成功', type: "success" });
        this.$emit('onCancelMethod', 1)
      } else {
        //毛三提成比例
        const { data, success } = await addProfit3CommissionPercentageScoreSet(this.tableData);
        this.loading = false;
        if (!success) return
        this.$message({ message: '操作成功', type: "success" });
        this.$emit('onCancelMethod', 1)
      }
    },
    onCancelMethod() {
      this.$emit('onCancelMethod', 2)
    },
    async getList() {
      let dataList = [];
      this.loading = true;
      //小组明细
      if (this.selectGroup) {
        const { data, success } = await getProfit3GroupDetailInitSetScore({});
        if (!success) return;
        dataList = data.list;
      } else {
        //毛三提成比例
        const { data, success } = await getProfit3CommissionPercentageInitSetScore({});
        if (!success) return;
        dataList = data.scoreSets;
        this.deductPoints = data.deductScoreSets
      }
      this.loading = false;
      this.tableData = dataList;
    },
    async computeMethod() {
      this.debouncedCompute();
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 100%;
}
</style>
