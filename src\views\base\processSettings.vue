<template>
  <div style="width: 98%;">
    <el-form :model="ruleForm" ref="ruleForm" class="demo-ruleForm">
      <el-form-item label="小组" prop="region" label-width="120px">
        <div class="flex-container">
          <!-- <div class="flex-item">
            <el-select v-model="ruleForm.groupType" placeholder="请选择类别" clearable class="flex-item_top"
              @change="operateChange">
              <el-option label="运营组" value="运营组"></el-option>
              <el-option label="采购组" value="采购组"></el-option>
            </el-select>
          </div> -->
          <div class="flex-item">
            <el-select v-model="ruleForm.groupId" clearable filterable placeholder="请选择小组" class="flex-item_top"
              @change="onGroupChange">
              <el-option v-for="(item, index) in grouplist" :key="item.value + index" :label="item.label"
                :value="item.value" />
            </el-select>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="抄送人" prop="userIdser" label-width="120px">
        <el-select v-model="userIdser" filterable placeholder="请输入通过抄送人" :filter-method="recipientMethod" clearable
          style="margin: 0 10px 5px 0;">
          <el-option v-for="(item, index) in recipientList" :key="item.value + index" :label="item.label"
            :value="item.value">
            <span>{{ item.label }}</span>
            <span class="option-details">
              ({{ item.extData.position }},{{ item.extData.empStatusText }}{{ item.extData.jstUserName ?
                `,${item.extData.jstUserName}` : '' }})
            </span>
            <span class="option-details">{{ item.extData.deptName }}</span>
          </el-option>
        </el-select>
        <el-button type="primary" @click="appendRecipient()" :loading="recipientLoading">{{
          (recipientLoading ? '加载中' : '添加') }}</el-button>
        <div class="recipient_bottom">
          <div style="width: 100%">
            <el-tag v-for="(item, i) in ruleForm.cCUser" :key="i" closable type="" style="margin: 2px 5px;"
              @close="removeUser('cCUser', null, i)">{{ item.userName }}</el-tag>
          </div>
        </div>
      </el-form-item>
      <el-scrollbar style="height: 585px;width: 100%;">
        <el-card v-for="(activity, index) in ruleForm.actions" :key="index"
          style="margin-bottom:10px;border-radius: 4px;">
          <div slot="header">{{ activity.content }}</div>
          <el-form-item label="审批类型" prop="actionType" label-width="100px">
            <el-select v-model="activity.actionType" placeholder="请选择审批类型" clearable class="publicCss">
              <el-option label="会签" value="AND"></el-option>
              <el-option label="或签" value="OR"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="添加人" prop="a" label-width="100px">
            <el-select v-model="activity.ddUserId" filterable placeholder="请输入并选择姓名" :filter-method="remoteMethod"
              clearable style="margin-right: 10px;" class="publicCss">
              <el-option v-for="(item, index) in optionslist" :key="item.value + index" :label="item.label"
                :value="item.value">
                <span>{{ item.label }}</span>
                <span class="option-details">
                  ({{ item.extData.position }},{{ item.extData.empStatusText }}{{ item.extData.jstUserName ?
                    `,${item.extData.jstUserName}` : '' }})
                </span>
                <span class="option-details">{{ item.extData.deptName }}</span>
              </el-option>
            </el-select>
            <el-button type="primary" @click="addPeople(activity, index)" :loading="appendLoading">{{ (appendLoading ?
              '加载中' : '添加') }}</el-button>
          </el-form-item>
          <div class="father_bottom">
            <!-- <div class="father_bottom_topWord">已选择人员</div> -->
            <div style="width: 100%">
              <el-tag v-for="(item, i) in activity.ddUsers" :key="i" closable type="" style="margin: 2px 5px;"
                @close="removeUser('ddUsers', index, i)">{{
                  item.userName
                }}</el-tag>
            </div>
          </div>
        </el-card>
      </el-scrollbar>
    </el-form>
  </div>
</template>

<script>
import { pickerOptions } from '@/utils/tools'
import { timePickerOptions } from '@/utils/getCols'
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import { getPurchaseDeptList } from '@/api/inventory/purchaseordernew'
import request from '@/utils/request'
const api = '/api/inventory/ApprovalProcess/'

export default {
  props: {
    sceneName: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      api,
      grouplist: [],
      operatelist: [],
      purchasegrouplist: [],
      reverse: true,
      recipientList: [],
      recipientLoading: false,
      appendLoading: false,
      tableData: [],
      optionslist: [],
      userIdser: '',
      ruleForm: {
        id: '',
        region: '',
        userId: '',
        groupId: '',
        groupType: '运营组',
        groupName: '',
        cCUser: [],
        userIdser: '',
        actions: [{
          content: '审批流程1',
          actionType: '',
          ddUserId: '',
          ddUsers: [],
        }, {
          content: '审批流程2，不设置则无该审批步骤',
          actionType: '',
          ddUserId: '',
          ddUsers: [],
        }, {
          content: '审批流程3，不设置则无该审批步骤',
          actionType: '',
          ddUserId: '',
          ddUsers: [],
        }, {
          content: '审批流程4，不设置则无该审批步骤',
          actionType: '',
          ddUserId: '',
          ddUsers: [],
        }],
      },
    }
  },
  watch: {

  },
  mounted() {
    this.init()
  },
  methods: {
    removeUser(listName, index, i) {
      if (listName === 'ddUsers') {
        this.ruleForm.actions[index].ddUsers.splice(i, 1);
      } else if (listName === 'cCUser') {
        this.ruleForm.cCUser.splice(i, 1);
      }
    },
    onGroupChange() {
      this.ruleForm.groupName = this.grouplist.find(item => item.value === this.ruleForm.groupId)?.label;
    },
    deleteprops(item, index, i) {
      this.ruleForm.cCUser.splice(i, 1)
    },
    appendRecipient() {
      if (this.userIdser) {
        if (this.ruleForm.cCUser.find(item => item.ddUserId === this.userIdser)) {
          this.userIdser = '';
          this.$message.error('已添加该人员');
          return;
        }
        const selectedItem = this.recipientList.find(item => item.value === this.userIdser);
        if (selectedItem) {
          this.ruleForm.cCUser.push({
            ddUserId: selectedItem.value,
            userName: selectedItem.label
          });
        }
        this.userIdser = '';
      }
    },
    populateActions(row) {
      const actions = ['action1', 'action2', 'action3', 'action4'];
      actions.forEach((action, index) => {
        const userIds = row[`${action}UId`];
        const userNames = row[`${action}UName`];
        if (userIds && userNames) {
          const userIdArray = userIds.split(',');
          const userNameArray = userNames.split(',');
          this.ruleForm.actions[index].actionType = row[`${action}Type`];
          this.ruleForm.actions[index].ddUsers = userIdArray.map((id, i) => ({
            ddUserId: id,
            userName: userNameArray[i]
          }));
        }
      });
      const ccUserId = row.ccUserId;
      const ccUserName = row.ccUserName;
      if (ccUserId && ccUserName) {
        const ccUserIdArray = ccUserId.split(',');
        const ccUserNameArray = ccUserName.split(',');
        this.ruleForm.cCUser = ccUserIdArray.map((id, i) => ({
          ddUserId: id,
          userName: ccUserNameArray[i]
        }));
      }
    },
    editProcessMethod(row) {
      this.populateActions(row);
      this.ruleForm.id = row.id;
      if (row.groupName && row.groupId) {
        this.ruleForm.groupId = ''
        this.ruleForm.groupName = '';
        this.grouplist.push({
          value: row.groupId,
          label: row.groupName,
        });
        this.ruleForm.groupType = row.groupType ? row.groupType : '运营组';
        this.ruleForm.groupId = row.groupId;
        this.ruleForm.groupName = row.groupName;
      }
    },
    onverify() {
      const hasValidAction = this.ruleForm.actions.some(item => item.actionType && item.ddUsers.length);
      const hasEmptyAction = this.ruleForm.actions.some(item => !item.actionType || !item.ddUsers.length);
      if (!hasValidAction) {
        this.$message.error('请填写一个完整审批流程');
        return true;
      }
      if (!this.ruleForm.groupId) {
        this.$message.error('请选择小组');
        return true;
      }
      if (hasEmptyAction && !this.ruleForm.groupId) {
        this.$message.error('请填写一个审批流程并选择小组');
        return true;
      }
      return false;
    },
    addProcessMethod(val) {
      let ver = this.onverify();
      if (ver) {
        return;
      }
      if (this.ruleForm.groupType == '运营组') {
        this.ruleForm.groupName = this.operatelist.find(item => item.value == this.ruleForm.groupId)?.label;
      } else if (this.ruleForm.groupType == '采购组') {
        this.ruleForm.groupName = this.purchasegrouplist.find(item => item.value == this.ruleForm.groupId)?.label;
      }
      const confirmMessage = val ? '是否确认编辑审批流程' : '是否确认添加审批流程';
      const apiEndpoint = val ? `${this.api}MergeApprovalProcess` : `${this.api}MergeApprovalProcess`;
      this.$confirm(confirmMessage, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const groupId = this.ruleForm.groupId ? Number(this.ruleForm.groupId) : 0;
        request.post(apiEndpoint, { ...this.ruleForm, sceneName: this.sceneName, groupId }).then(res => {
          if (res.success) {
            this.$message.success(val ? '编辑成功' : '添加成功');
            this.$emit('close');
          }
        });
      }).catch((e) => {
        this.$message.info('已取消操作');
      });
    },
    operateChange(e) {
      this.grouplist = []
      if (e == '运营组') {
        this.grouplist = this.operatelist
      } else if (e == '采购组') {
        this.grouplist = this.purchasegrouplist
      } else {
        this.grouplist = []
      }
      this.ruleForm.groupId = ''
    },
    async init() {
      var res2 = await getDirectorGroupList();
      this.operatelist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
      this.grouplist = this.operatelist;
      var { data } = await getPurchaseDeptList();
      this.purchasegrouplist = data.map(item => { return { value: item.dept_id, label: item.full_name }; });
    },
    delprops(item, index, i) {
      this.ruleForm.actions[index].ddUsers.splice(i, 1)
    },
    addPeople(i, index) {
      if (this.ruleForm.actions[index].ddUsers.find(item => item.ddUserId === i.ddUserId)) {
        this.$message.error('已添加该人员');
        return;
      }
      const selectedItem = this.optionslist.find(item => item.value === i.ddUserId);
      if (selectedItem) {
        this.ruleForm.actions[index].ddUsers.push({
          ddUserId: selectedItem.value,
          userName: selectedItem.label
        });
      }
      this.ruleForm.userId = '';
    },
    async fetchUserList(query, list, loadingState) {
      if (query !== '') {
        this[loadingState] = true;
        let rlt = await QueryAllDDUserTop100({ keywords: query });
        this[loadingState] = false;
        if (rlt && rlt.success) {
          this[list] = rlt.data.map(item => ({
            label: item.userName,
            value: item.ddUserId,
            extData: item
          }));
        }
      } else {
        this[list] = list === 'optionslist' ? [...this.orgOptions] : [];
      }
      if (list === 'optionslist') {
        this.ruleForm.userId = '';
      }
    },
    recipientMethod(query) {
      this.fetchUserList(query, 'recipientList', 'recipientLoading');
    },
    remoteMethod(query) {
      this.fetchUserList(query, 'optionslist', 'appendLoading');
    },
  }
}
</script>

<style scoped lang="scss">
.timeCss {
  width: 100%;
}

.option-details {
  color: #8492a6;
}

.publicCss {
  margin-top: 5px;
  width: 40%;
}

.father_bottom {
  max-height: 80px;
  overflow: auto;
  // border: 1px solid #ccc;
  margin: 0 20px 0 0;
  border-radius: 5px;
  margin-left: 100px;

  .father_bottom_topWord {
    text-align: center;
  }
}

.flex-container {
  display: flex;
  align-items: center;
}

.flex-item {
  margin-right: 5px;
}

.flex-item:last-child {
  margin-right: 0;
}

.flex-item_top {
  width: 126px;
  margin-right: 10px;
}

.recipient_bottom {
  max-height: 65px;
  overflow: auto;
  margin: 0 20px 0 0;
  border-radius: 5px;
  margin-left: -4px;
}
</style>
