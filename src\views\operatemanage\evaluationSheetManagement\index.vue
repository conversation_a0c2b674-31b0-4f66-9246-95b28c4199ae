<template>
  <MyContainer>
    <el-tabs v-model="activeName" style="height: 95%">
      <el-tab-pane label="放单收集" name="first" style="height: 100%" lazy>
        <placeSingleCollect />
      </el-tab-pane>
      <el-tab-pane label="评价收集" name="fifth" style="height: 100%" lazy>
        <reviewCollection />
      </el-tab-pane>
    </el-tabs>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import reviewCollection from "./components/reviewCollection.vue";
import placeSingleCollect from "./components/placeSingleCollect.vue";
export default {
  components: {
    MyContainer,
    placeSingleCollect,
    reviewCollection,
  },
  data() {
    return {
      activeName: "first",
    };
  },
  async mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped></style>
