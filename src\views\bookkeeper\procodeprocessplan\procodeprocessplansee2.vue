<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form ref="addForm" :model="addForm" label-width="120px">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
                        <el-form-item prop="addForm.id" label="方案名称">
                            <el-select v-model="addForm.id" placeholder="请选择方案名称" @change="(val) =>loadData2({ id: val })" filterable>
                                <el-option v-for="item in plansList" :key="'m2' + item.id" :label="item.processPlanName" :value="item.id" />
                            </el-select>
                            <!-- <el-input v-model="addForm.processPlanName" auto-complete="off" placeholder="请填写方案名称"
                                maxlength="20" /> -->
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
                        <el-form-item prop="curDate" label="日报参考日期">
                            <el-date-picker v-model="addForm.curDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                disabled type="date" placeholder="请选择日报参考日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
                        <el-form-item prop="startDate" label="观察开始日期">
                            <el-date-picker v-model="addForm.startDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                disabled type="date" placeholder="请选择观察开始日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
                        <el-form-item prop="endDate" label="观察结束日期">
                            <el-date-picker v-model="addForm.endDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                disabled type="date" placeholder="请选择观察结束日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item prop="procodeProcessPlanColCheck1" label="被观察指标">
                            <el-checkbox-group v-model="procodeProcessPlanColCheckList" size="mini">
                                <el-checkbox v-for="item in procodeProcessPlanColList" :key="item.value" disabled
                                    :label="item.label" :value="item.value" border></el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row v-if="permissionDelDtl">
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-dropdown style="box-sizing: border-box;width: 160px;" size="mini" split-button
                            type="primary" icon="el-icon-share" @command="handleCommand">
                            批量操作明细数据
                            <el-dropdown-menu slot="dropdown">

                                <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                                    command="a">批量填写运营处理方案</el-dropdown-item>

                                <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                                    command="b">批量删除明细</el-dropdown-item>

                            </el-dropdown-menu>
                        </el-dropdown>

                        <!-- <el-button type="primary" @click="onBatchDeleteDtl" :loading="deldtlLoading"
                            style="margin-left: 10px;">批量填写运营处理方案</el-button>
                        <el-button type="primary" @click="onBatchDeleteDtl" :loading="deldtlLoading"
                            style="margin-left: 10px;">批量删除明细</el-button> -->

                        <my-confirm-button type="submit" :loading="addLoading" @click="onProcessPlan">
                            提交运营处理方案
                        </my-confirm-button>

                        <el-button type="primary" @click="bulkLoadingUn" style="margin-left: 10px;">批量上下架</el-button>

                        <el-select v-model="dtlFilter.yyProfit1RateLxDays" placeholder="毛一7日趋势" clearable filterable
                            v-if="(procodeProcessPlanColKeys.indexOf('yyProfit1Rate') >= 0)"
                            style="width: 120px; margin-left:10px;">
                            <el-option v-for="item in lxDaysList" :key="'m1' + item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                        <el-select v-model="dtlFilter.yyProfit2RateLxDays" placeholder="毛二7日趋势" clearable filterable
                            v-if="(procodeProcessPlanColKeys.indexOf('yyProfit2Rate') >= 0)"
                            style="width: 120px; margin-left:10px;">
                            <el-option v-for="item in lxDaysList" :key="'m2' + item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                        <el-select v-model="dtlFilter.yyProfit3RateLxDays" placeholder="毛三7日趋势" clearable filterable
                            v-if="(procodeProcessPlanColKeys.indexOf('yyProfit3Rate') >= 0)"
                            style="width: 120px; margin-left:10px;">
                            <el-option v-for="item in lxDaysList" :key="'m3' + item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                        <el-select v-model="dtlFilter.yyProfit4RateLxDays" placeholder="毛四7日趋势" clearable filterable
                            v-if="(procodeProcessPlanColKeys.indexOf('yyProfit4Rate') >= 0)"
                            style="width: 120px; margin-left:10px;">
                            <el-option v-for="item in lxDaysList" :key="'m4' + item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                        <el-select v-model="dtlFilter.yyProfit6RateLxDays" placeholder="毛六7日趋势" clearable filterable
                            v-if="(procodeProcessPlanColKeys.indexOf('yyProfit6Rate') >= 0)"
                            style="width: 120px; margin-left:10px;">
                            <el-option v-for="item in lxDaysList" :key="'m6' + item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>


                        <el-select v-model="dtlFilter.yyProfit1AfterRateLxDays" placeholder="毛一7日(减退款)趋势" clearable
                            v-if="(procodeProcessPlanColKeys.indexOf('yyProfit1AfterRate') >= 0)" filterable
                            style="width: 120px; margin-left:10px;">
                            <el-option v-for="item in lxDaysList" :key="'m12' + item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                        <el-select v-model="dtlFilter.yyProfit2AfterRateLxDays" placeholder="毛二7日(减退款)趋势" clearable
                            v-if="(procodeProcessPlanColKeys.indexOf('yyProfit2AfterRate') >= 0)" filterable
                            style="width: 120px; margin-left:10px;">
                            <el-option v-for="item in lxDaysList" :key="'m22' + item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                        <el-select v-model="dtlFilter.yyProfit3AfterRateLxDays" placeholder="毛三7日(减退款)趋势" clearable
                            v-if="(procodeProcessPlanColKeys.indexOf('yyProfit3AfterRate') >= 0)" filterable
                            style="width: 120px; margin-left:10px;">
                            <el-option v-for="item in lxDaysList" :key="'m32' + item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                        <el-select v-model="dtlFilter.yyProfit4AfterRateLxDays" placeholder="毛四7日(减退款)趋势" clearable
                            v-if="(procodeProcessPlanColKeys.indexOf('yyProfit4AfterRate') >= 0)" filterable
                            style="width: 120px; margin-left:10px;">
                            <el-option v-for="item in lxDaysList" :key="'m42' + item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                        <el-select v-model="dtlFilter.yyProfit6AfterRateLxDays" placeholder="毛六7日(减退款)趋势" clearable
                            v-if="(procodeProcessPlanColKeys.indexOf('yyProfit6AfterRate') >= 0)" filterable
                            style="width: 120px; margin-left:10px;">
                            <el-option v-for="item in lxDaysList" :key="'m62' + item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>

                        <el-select v-model="dtlFilter.Profit4RateLxDays" placeholder="净利率7日趋势" clearable filterable
                            v-if="(procodeProcessPlanColKeys.indexOf('Profit4Rate') >= 0)"
                            style="width: 120px; margin-left:10px;">
                            <el-option v-for="item in lxDaysList" :key="'jlv1' + item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                        <el-button style="padding: 0;margin: 0;">
                            <inputYunhan ref="productCode20250619" :inputt.sync="dtlFilter.proCode" v-model="dtlFilter.proCode"
                                class="publicCss" placeholder="产品ID，回车可输入多个" :clearable="true" :clearabletext="true"
                                :maxRows="200" :maxlength="4000"  @callback="orderNoInnerBack" title="产品ID">
                            </inputYunhan>
                        </el-button>
                        <el-input v-model.trim="dtlFilter.styleCode" placeholder="系列编码" maxlength="50" clearable class="publicCss" style="width: 200px;"/>
                        <el-button style="padding: 0;width: 160px;border: none;">
                            <el-select filterable v-model="dtlFilter.groupId" placeholder="运营组长" style="width: 160px" clearable multiple collapse-tags>
                                <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
                            </el-select>
                        </el-button>
                        <el-button style="padding: 0;width: 160px;border: none;">
                            <el-select filterable clearable v-model="dtlFilter.operateSpecialUserId" placeholder="运营专员" style="width: 160px" multiple collapse-tags>
                            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key">
                            </el-option>
                            </el-select>
                        </el-button>
                        <el-select filterable v-model="dtlFilter.userId" placeholder="运营助理" clearable multiple collapse-tags style="width: 160px">
                            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
                        </el-select>

                        <el-button type="primary" @click="onSearch" style="margin-left: 10px;">查询</el-button>
                        <el-button type="primary" @click="onExportDtl" style="margin-left: 10px;">导出</el-button>
                    </el-col>
                </el-row>
            </el-form>
        </template>
        <template>
            <vxetablebase :id="'procodeprocessplanadd_see1'" :border="true" :align="'center'" :isRemoteSort="false"
                :tablekey="'procodeprocessplanadd_see1'" ref="table2" :that='that' :isIndex='true' :hasexpand='false'
                :isSelectColumn="false" :isNeedExpend="false" :showsummary='false' :tablefixed='true'
                :summaryarry='summaryarry' :tableData='addForm.proList' :tableCols='tableCols' :loading="listLoading"
                :tableHandles='tableHandles' @select='onSelectDtl' :showheaderoverflow="false"
                style="width:100%;height:95%;margin: 0" :xgt="9999">
                <template v-slot:operateProcessPlan="{ row }">
                    <div v-if="!operateProcessPlanVisable || row.isBmd">
                        {{ row.operateProcessPlan }}
                    </div>
                    <div v-else>
                        <el-select filterable v-model="row.operateProcessPlanValue" placeholder="请选择运营处理方案" clearable @change="onPlanValueChange">
                            <el-option v-for="item in operateProcessPlanList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </div>
                </template>
                <template v-slot:targetRate="{ row }">
                    <div v-if="row.operateProcessPlanValue == '提升利润' || row.operateProcessPlan == '提升利润'">
                        <div v-if="!operateProcessPlanVisable || row.isBmd">
                            {{ row.targetRate }}
                        </div>
                        <div v-else>
                            <el-input-number style="width: 105px;" v-model="row.targetRate" :precision="2" :min="-99999999.99" :max="99999999.99" :controls="false" placeholder="目标利润" >
                            </el-input-number>
                        </div>
                    </div>
                </template>
            </vxetablebase>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="selectAddRows.length" @get-page="getList" />
        </template>


        <el-dialog title="批量填写运营处理方案" :visible.sync="dialogVisiblePltxyyclfa" width="20%" :close-on-click-modal="false"
            v-dialogDrag>
            <el-select filterable v-model="dialogPltxyyclfa_Value" placeholder="请选择运营处理方案" clearable>
                <el-option v-for="item in operateProcessPlanList" :key="item" :label="item" :value="item" />
            </el-select>
            <div v-if="dialogPltxyyclfa_Value == '提升利润'">
                <div>目标利润:</div>
                <el-input-number style="width: 110px;" v-model="targetRate_value" :precision="2" :min="-99999999.99" :max="99999999.99" :controls="false" placeholder="目标利润" >
                </el-input-number>
            </div>
            <div style="font-size: 12px; color: chocolate;">【确认】按钮仅批量选择，点击【提交运营处理方案】按钮才会存档。</div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisiblePltxyyclfa = false">关闭</el-button>
                <el-button type="primary" @click="onPltxyyclfaSave"
                    :loading="dialogVisiblePltxyyclfaLoad">确认</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import { platformlist, formatPlatform, formatLinkProCode} from "@/utils/tools";
import { getDirectorList, getDirectorGroupList } from '@/api/operatemanage/base/shop'
import dayjs from 'dayjs';
import MyContainer from '@/components/my-container';
import inputYunhan from "@/components/Comm/inputYunhan";
import MyConfirmButton from '@/components/my-confirm-button';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
     GetProcodeProcessPlanById2, WriteOperateProcessPlan,
    DeleteProcodeProcessPlanProByIds, GetProcodeProcessPlanBmdListByProCodes, ExportProcodeProcessPlanById2, GetPlanPageList
} from '@/api/bookkeeper/procodeprocessplan'

const tableCols = [
];

const tableHandles1 = [

];

const LxDaysList = [
    { label: "连续1日下滑", value: -1 },
    { label: "连续2日下滑", value: -2 },
    { label: "连续3日下滑", value: -3 },
    { label: "连续4日下滑", value: -4 },
    { label: "连续5日下滑", value: -5 },
    { label: "连续6日下滑", value: -6 },
    { label: "连续7日下滑", value: -7 },
];
const operateProcessPlanList = [
    "正常售卖",
    "提升利润",
    "战略产品",
    "3元3件店铺",
    "活动低价",
    "下架链接",
    "虚拟链接（赠品、邮费）",
];

export default {
    name: 'procodeprocessplansee',
    components: { MyContainer, MyConfirmButton, vxetablebase, inputYunhan },
    props: {
    },
    data() {
        return {
            that: this,
            thismainId: null,
            pageLoading: false,
            sels: [],
            platformlist: platformlist,
            tableCols: [],
            tableHandles: tableHandles1,
            procodeProcessPlanColKeys: [],
            procodeProcessPlanColList: [],
            procodeProcessPlanColCheckList: [],
            addLoading: false,
            selProCodes: [],
            permissionDelDtl: false,//是否有权限删除明细
            addForm: {
                id: null,
                processPlanName: "",
                curDate: null,
                startDate: null,
                endDate: null,
                procodeItemCols: "",
                procodeItemColNames: "",
                proList: [],
            },
            addFormTemp: {},
            operateProcessPlanVisable: true,
            listLoading: false,
            summaryarry: {},
            operateProcessPlanList: operateProcessPlanList,
            lxDaysList: LxDaysList,
            selectAddRows: [],
            total: 0,
            deldtlLoading: false,
            directorList: [],
            directorListUserType: [],
            directorGroupList: [],

            dialogVisiblePltxyyclfa: false,
            dialogVisiblePltxyyclfaLoad: false,
            dialogPltxyyclfa_Value: null,
            targetRate_value: null,

            dtlFilter: {
                yyProfit1RateLxDays: null,
                yyProfit2RateLxDays: null,
                yyProfit3RateLxDays: null,
                yyProfit4RateLxDays: null,
                yyProfit6RateLxDays: null,
                yyProfit1AfterRateLxDays: null,
                yyProfit2AfterRateLxDays: null,
                yyProfit3AfterRateLxDays: null,
                yyProfit4AfterRateLxDays: null,
                yyProfit6AfterRateLxDays: null,
                Profit4RateLxDays: null,
                proCode: null,
                styleCode: "",
                groupId: [],
                operateSpecialUserId: [],
                userId: []
            },
            plansList: []
        }
    },
    async mounted() {
        this.onPlanyMethod()
    },
    methods: {
        onPlanValueChange(e){
            this.$nextTick(() => {
                if(e == '提升利润'){
                    this.$refs.table2.changecolumn_setTrue(["targetRate"]);
                } else {
                    this.$refs.table2.changecolumn(["targetRate"]);
                }
            });
        },
        orderNoInnerBack(val) {
            this.dtlFilter.proCode = val;
        },
        async getDirectorlist() {
            const res1 = await getDirectorList({})
            const res2 = await getDirectorGroupList({})
            this.directorList = [{ key: '0', value: '未知' }].concat(res1.data || []);
            this.directorListUserType = [];
            res1.data.forEach(f => {
                if (f.userType)
                this.directorListUserType.push(f);
            });

            this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.loadData2({ id: this.thismainId, isClick: true });
        },
        async getList() {
            await this.loadData2({ id: this.thismainId, isClick: true });
        },
        async onPlanyMethod(){
            const params = {
                OrderBy: "createdTime", IsAsc: false
            };
            var res = await GetPlanPageList(params)
            this.plansList = res.data.list;
        },
        async loadData2(seeparam) {
            this.getDirectorlist()
            this.onPlanyMethod()
            if (!seeparam.id) {
                return;
            }
            this.pageLoading = false;
            this.pageLoading = true;
            this.thismainId = seeparam.id;
            //获取详情主表
            let param = { id: this.thismainId, };
            let pager = this.$refs.pager.getPager();
            const params = {
                ...param,
                ...pager,
                ...this.dtlFilter
            };
            let main = await GetProcodeProcessPlanById2(params);
            if (main?.success == true) {
                this.addFormTemp = main.data;
                this.total = main.data.proTotal;
                this.permissionDelDtl = main.data.permissionDelDtl;
                //拼指标勾选
                this.procodeProcessPlanColKeys = main.data.procodeItemCols.trim().split(/[, ，]/)
                this.getProcodeProcessPlanColList();
                this.procodeProcessPlanColCheckList = main.data.procodeItemColNames.trim().split(/[, ，]/)
                this.procodeProcessPlanColKeys.push('yyProfit6')
                this.procodeProcessPlanColCheckList.push('毛六利润')
                //拼明细列头
                if (seeparam.isClick) {
                    await this.onSeeDtlData();
                }
                else {
                    await this.onSeeDtl();
                }

                //拼数据源
                this.addForm = this.addFormTemp;
                this.selProCodes = this.addForm.proList.map(item => item.proCode);
                //隐藏目标利润
                this.$nextTick(() => {
                    let a = true
                    this.addForm.proList.find(item =>{
                        if(item.operateProcessPlanValue == '提升利润'){
                            a = false
                        }
                    })
                    if(a){
                        this.$refs.table2.changecolumn(["targetRate"]);
                    } else{
                        this.$refs.table2.changecolumn_setTrue(["targetRate"]);
                    }
                });
            }
            //获取详情明细列头
            this.selectAddRows = [];
            this.pageLoading = false;
        },
        async getProcodeProcessPlanColList() {
            this.procodeProcessPlanColList = [];
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit1Rate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit1Rate", label: "毛一利率" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit2Rate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit2Rate", label: "毛二利率" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit3Rate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit3Rate", label: "毛三利率" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit4Rate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit4Rate", label: "毛四利率" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit6Rate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit6Rate", label: "毛六利率" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit1AfterRate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit1AfterRate", label: "毛一利率(减退款)" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit2AfterRate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit2AfterRate", label: "毛二利率(减退款)" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit3AfterRate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit3AfterRate", label: "毛三利率(减退款)" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit4AfterRate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit4AfterRate", label: "毛四利率(减退款)" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit6AfterRate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit6AfterRate", label: "毛六利率(减退款)" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "Profit4Rate") != null) {
                this.procodeProcessPlanColList.push({ value: "Profit4Rate", label: "净利率" });
            }
        },
        async onSeeDtl() {
            this.listLoading = true;
            let procodeProcessPlanColCheckList_Order = [];//用于指标按照顺序，避免指标是乱的
            this.procodeProcessPlanColList.forEach(f => {//循环的目的就是保持指标按照顺序
                let find = this.procodeProcessPlanColCheckList.find(s => s == f.label);
                if (find) {
                    procodeProcessPlanColCheckList_Order.push(f);
                }
            });
            procodeProcessPlanColCheckList_Order.push({
                label: '毛六利润',value: 'yyProfit6'
            })
            let curymd = dayjs(this.addFormTemp.curDate).format('YYYY-MM-DD');
            let curymd_3 = dayjs(this.addFormTemp.curDate).add(-3, 'day').format('YYYY-MM-DD');
            let curymd_7 = dayjs(this.addFormTemp.curDate).add(-7, 'day').format('YYYY-MM-DD');

            this.tableCols = [];
            let newcols = [
                { istrue: true, fixed: 'left', width: '40', type: "checkbox" },
                { istrue: true, prop: 'processPlanName', label: '方案名称', width: '80' },
                { istrue: true, prop: 'platform', label: '平台', width: '40', formatter: row => formatPlatform(row.platform) },//
                { istrue: true, prop: 'shopName', label: '店铺名称', width: '80' },
                { istrue: true, prop: 'styleCode', label: '系列编码', width: '80' },
                { istrue: true, prop: 'proCode', label: '产品ID', width: '100', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode)},
                { istrue: true, prop: 'proName', label: '产品名称', width: '80' },
                { istrue: true, prop: 'groupId', label: '运营组', width: '55', formatter: (row) => row.groupName },
                { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', width: '55', formatter: (row) => row.operateSpecialUserName },
                { istrue: true, prop: 'userId', label: '运营助理', width: '55', formatter: (row) => row.userName },
                { istrue: true, prop: 'onTime', label: '上架时间', width: '80' },
                { istrue: true, prop: 'onTimeCount', label: '上架天数', width: '40' },
                { istrue: true, prop: 'productCategoryId', label: '类目', width: '80', formatter: (row) => row.productCategoryName },
                { istrue: true, prop: 'operateProcessPlan', label: '运营处理方案', width: '150' },
                { istrue: true, prop: 'targetRate', label: '目标利润', width: '150' }
            ];

            let newcols2 = [
                { istrue: true, prop: 'orderCount', label: '订单量', width: '60', formatter: (row) => !row.orderCount ? " " : row.orderCount },
                { istrue: true, prop: 'payAmont', label: '付款金额', width: '80', formatter: (row) => !row.payAmont ? " " : row.payAmont.toFixed(2) },
                { istrue: true, prop: 'alladv', label: '总广告费', width: '80', formatter: (row) => row.alladv == 0 ? " " : row.alladv?.toFixed(2) },
                { istrue: true, prop: 'advratio', label: '广告占比', width: '60', formatter: (row) => !row.advratio ? " " : row.advratio.toFixed(2) + "%" },

                { istrue: true, prop: 'yyProfit1', label: '毛一利润', width: '60', type: 'custom', tipmesg: '销售金额-总商品成本-采购运费-代发成本', formatter: (row) => !row.yyProfit1 ? " " : row.yyProfit1.toFixed(2) },
                { istrue: true, prop: 'yyProfit1Rate', label: '毛一利率', width: '60', type: 'custom', tipmesg: '毛一利润/销售金额', formatter: (row) => !row.yyProfit1Rate ? " " : row.yyProfit1Rate.toFixed(2) + '%' },
                { istrue: true, prop: 'yyProfit2', label: '毛二利润', width: '60', type: 'custom', tipmesg: '毛一利润-平台扣点-包装费-快递费', formatter: (row) => !row.yyProfit2 ? " " : row.yyProfit2?.toFixed(2) },
                { istrue: true, prop: 'yyProfit2Rate', label: '毛二利率', width: '60', type: 'custom', tipmesg: '毛二利润/销售金额', formatter: (row) => !row.yyProfit2Rate ? " " : row.yyProfit2Rate.toFixed(2) + '%' },
                { istrue: true, prop: 'yyProfit3', label: '毛三利润', width: '60', type: 'custom', tipmesg: '毛二利润-预估费用', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3 ? " " : row.yyProfit3?.toFixed(2) },
                { istrue: true, prop: 'yyProfit3Rate', label: '毛三利率', width: '60', type: 'custom', tipmesg: '毛二利润-预估费用', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3Rate ? " " : row.yyProfit3Rate?.toFixed(2) + '%' },
                { istrue: true, prop: 'yyProfit4', label: '毛四利润', width: '60', type: 'custom', tipmesg: '毛三利润-出仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4 ? " " : row.yyProfit4?.toFixed(2) },
                { istrue: true, prop: 'yyProfit4Rate', label: '毛四利率', width: '60', type: 'custom', tipmesg: '毛四利润/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4Rate ? " " : row.yyProfit4Rate?.toFixed(2) + '%' },
                
                { istrue: true, prop: 'yyProfit6', label: '毛六利润', width: '60', type: 'custom', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit6 ? " " : row.yyProfit6?.toFixed(2) },
                { istrue: true, prop: 'yyProfit6Rate', label: '毛六利率', width: '60', type: 'custom', tipmesg: '毛六利润/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit6Rate ? " " : row.yyProfit6Rate?.toFixed(2) + '%' },

                { istrue: true, prop: 'yyProfit1After', label: '毛一(减退款)', width: '70', type: 'custom', tipmesg: '毛一-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After?.toFixed(2) },
                { istrue: true, prop: 'yyProfit1AfterRate', label: '毛一利率(减退款)', width: '70', type: 'custom', tipmesg: '毛一（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit1AfterRate ? " " : row.yyProfit1AfterRate?.toFixed(2) + '%' },
                { istrue: true, prop: 'yyProfit2After', label: '毛二(减退款）', width: '70', type: 'custom', tipmesg: '毛二-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After?.toFixed(2) },
                { istrue: true, prop: 'yyProfit2AfterRate', label: '毛二利率(减退款)', width: '70', type: 'custom', tipmesg: '毛二（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit2AfterRate ? " " : row.yyProfit2AfterRate?.toFixed(2) + '%' },
                { istrue: true, prop: 'yyProfit3After', label: '毛三(减退款）', width: '70', type: 'custom', tipmesg: '毛三-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After?.toFixed(2) },
                { istrue: true, prop: 'yyProfit3AfterRate', label: '毛三利率(减退款)', width: '70', type: 'custom', tipmesg: '毛三（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3AfterRate ? " " : row.yyProfit3AfterRate?.toFixed(2) + '%' },
                { istrue: true, prop: 'yyProfit4After', label: '毛四(减退款）', width: '70', type: 'custom', tipmesg: '毛四-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After?.toFixed(2) },
                { istrue: true, prop: 'yyProfit4AfterRate', label: '毛四利率(减退款)', width: '70', type: 'custom', tipmesg: '毛四（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4AfterRate ? " " : row.yyProfit4AfterRate?.toFixed(2) + '%' },
                
                { istrue: true, prop: 'yyProfit6After', label: '毛六(减退款）', width: '70', type: 'custom', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After?.toFixed(2) },
                { istrue: true, prop: 'yyProfit6AfterRate', label: '毛六利率(减退款)', width: '70', type: 'custom', tipmesg: '毛六（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit6AfterRate ? " " : row.yyProfit6AfterRate?.toFixed(2) + '%' },
                
                { istrue: true, prop: 'profit4', label: '净利润', type: 'custom', tipmesg: '毛三利润-公摊费', width: '50', permission: "lirunprsi,shareFeedetail", formatter: (row) => !row.profit4 ? " " : row.profit4.toFixed(2) },
                { istrue: true, prop: 'profit4Rate', label: '净利率', type: 'custom', tipmesg: '净利润/销售金额', width: '50', permission: "lirunprsi,shareFeedetail", formatter: (row) => !row.profit4Rate ? " " : (row.profit4Rate).toFixed(2) + '%' },
                {
                    istrue: true, rop: '', label: `退款`, width: '120', merge: true, prop: 'mergeField',
                    cols: [
                        { istrue: true, prop: 'yyRefundAmontBefore', label: '发货前退款', width: '100', type: 'custom' },
                        { istrue: true, prop: 'yyRefundAmontBeforeRate', label: '发货前退款率', tipmesg: '发货前退款/付款金额', width: '120', type: 'custom', formatter: (row) => !row.yyRefundAmontBeforeRate ? " " : (row.yyRefundAmontBeforeRate).toFixed(2) + '%' },
                        { istrue: true, prop: 'yyRefundAmontAfter', label: '发货后退款', width: '100', type: 'custom' },
                        { istrue: true, prop: 'yyRefundAmontAfterRate', label: '发货后退款率', tipmesg: '发货后退款/付款金额', width: '120', type: 'custom', formatter: (row) => !row.yyRefundAmontAfterRate ? " " : (row.yyRefundAmontAfterRate).toFixed(2) + '%' },
                        { istrue: true, prop: 'yyRefundAmont', label: '总退款金额', width: '100', tipmesg: '当日发生的总退款金额，包括历史订单', formatter: (row) => !row.yyRefundAmont ? " " : row.yyRefundAmont.toFixed(2) },
                    ]
                },
            ];

            let c1 = { align: 'center', prop: '', label: curymd, merge: true, width: '80', cols: [] }
            let c2 = { align: 'center', prop: '', label: "前3天", merge: true, width: '80', cols: [] }
            let c3 = { align: 'center', prop: '', label: "前7天", merge: true, width: '80', cols: [] }
            //当天，前3天，前7天
            procodeProcessPlanColCheckList_Order.forEach(w => {
                //拼列头
                if (w.value.indexOf("Rate") > 0) {
                    c1.cols.push({ istrue: true, prop: curymd + w.value, label: w.label, width: '80', formatter: (row) => (row[curymd + w.value] + '%') });
                    c2.cols.push({ istrue: true, prop: curymd_3 + w.value, label: w.label, width: '80', formatter: (row) => (row[curymd_3 + w.value] + '%') });
                    c3.cols.push({ istrue: true, prop: curymd_7 + w.value, label: w.label, width: '80', formatter: (row) => (row[curymd_7 + w.value] + '%') });
                }
                else {
                    c1.cols.push({ istrue: true, prop: curymd + w.value, label: w.label, width: '80' });
                    c2.cols.push({ istrue: true, prop: curymd_3 + w.value, label: w.label, width: '80' });
                    c3.cols.push({ istrue: true, prop: curymd_7 + w.value, label: w.label, width: '80' });
                }
                //循环数据源
                this.addFormTemp.proList.forEach(f => {
                    //拼数据源
                    f[(curymd + w.value)] = f.proItemList.find(x => x.itemDateStr == curymd && x.itemKey == w.value)?.itemValue ?? 0;
                    f[(curymd_3 + w.value)] = f.proItemList.find(x => x.itemDateStr == curymd_3 && x.itemKey == w.value)?.itemValue ?? 0;
                    f[(curymd_7 + w.value)] = f.proItemList.find(x => x.itemDateStr == curymd_7 && x.itemKey == w.value)?.itemValue ?? 0;
                })
            });
            newcols.push(c1);
            newcols.push(c2);
            newcols.push(c3);

            //循环观察日期
            for (let i = dayjs(this.addFormTemp.startDate); i <= dayjs(this.addFormTemp.endDate); i = i.add(1, 'day')) {
                let mycols = [];
                //循环指标
                procodeProcessPlanColCheckList_Order.forEach(w => {
                    //拼列头
                    if (w.value.indexOf("Rate") > 0) {
                        mycols.push({ istrue: true, prop: (i.format('YYYY-MM-DD') + w.value), label: w.label, width: '80', formatter: (row) => (row[i.format('YYYY-MM-DD') + w.value] + '%') });
                    }
                    else {
                        mycols.push({ istrue: true, prop: (i.format('YYYY-MM-DD') + w.value), label: w.label, width: '80' });
                    }

                    //拼数据源
                    this.addFormTemp.proList.forEach(f => {
                        f[(i.format('YYYY-MM-DD') + w.value)] = f.proItemList.find(x => x.itemDateStr == i.format('YYYY-MM-DD') && x.itemKey == w.value)?.itemValue ?? 0;
                    });

                });
                newcols.push({ align: 'center', prop: '', label: i.format('YYYY-MM-DD'), merge: true, prop: 'mergeField', width: '80', cols: mycols });
            }

            //newcols = newcols.concat(newcols2);

            this.$nextTick(() => {
                this.tableCols = newcols;
            });
            this.listLoading = false;
        },
        async onSeeDtlData() {
            this.listLoading = true;
            let procodeProcessPlanColCheckList_Order = [];//用于指标按照顺序，避免指标是乱的
            this.procodeProcessPlanColList.forEach(f => {//循环的目的就是保持指标按照顺序
                let find = this.procodeProcessPlanColCheckList.find(s => s == f.label);
                if (find) {
                    procodeProcessPlanColCheckList_Order.push(f);
                }
            });
            procodeProcessPlanColCheckList_Order.push({
                label: '毛六利润',value: 'yyProfit6'
            })
            let curymd = dayjs(this.addFormTemp.curDate).format('YYYY-MM-DD');
            let curymd_3 = dayjs(this.addFormTemp.curDate).add(-3, 'day').format('YYYY-MM-DD');
            let curymd_7 = dayjs(this.addFormTemp.curDate).add(-7, 'day').format('YYYY-MM-DD');
            //当天，前3天，前7天
            procodeProcessPlanColCheckList_Order.forEach(w => {
                //循环数据源
                this.addFormTemp.proList.forEach(f => {
                    //拼数据源
                    f[(curymd + w.value)] = f.proItemList.find(x => x.itemDateStr == curymd && x.itemKey == w.value)?.itemValue ?? 0;
                    f[(curymd_3 + w.value)] = f.proItemList.find(x => x.itemDateStr == curymd_3 && x.itemKey == w.value)?.itemValue ?? 0;
                    f[(curymd_7 + w.value)] = f.proItemList.find(x => x.itemDateStr == curymd_7 && x.itemKey == w.value)?.itemValue ?? 0;
                })
            });

            //循环观察日期
            for (let i = dayjs(this.addFormTemp.startDate); i <= dayjs(this.addFormTemp.endDate); i = i.add(1, 'day')) {
                //循环指标
                procodeProcessPlanColCheckList_Order.forEach(w => {
                    //拼数据源
                    this.addFormTemp.proList.forEach(f => {
                        f[(i.format('YYYY-MM-DD') + w.value)] = f.proItemList.find(x => x.itemDateStr == i.format('YYYY-MM-DD') && x.itemKey == w.value)?.itemValue ?? 0;
                    });

                });
            }
            this.listLoading = false;
        },
        //仅保存处理方案
        async onProcessPlan() {
            let dtos = [];
            let stop = false;
            this.addForm.proList.forEach(f => {
                if (f.operateProcessPlanValue) {
                    if(f.operateProcessPlanValue == '提升利润'){
                        if(!f.targetRate){
                            stop = true
                            return;
                        }
                    } else {
                        f.targetRate = null
                    }
                    dtos.push({targetRate: f.targetRate, id: f.id, operateProcessPlan: f.operateProcessPlanValue})
                }
            });
            if(stop){
                this.$message.warning('运营处理方案为提升利润时，请填写目标利润');
                return;
            }
            this.pageLoading = true;
            let res = await WriteOperateProcessPlan(dtos);
            if (res?.success == true) {
                this.$message.success('提交成功')
            }
            this.pageLoading = false;
        },
        onSelectDtl(rows) {
            this.selectAddRows = rows;
        },
        //批量删除
        async onBatchDeleteDtl() {
            if (this.selectAddRows.length <= 0) {
                this.$message.warning('请至少勾选一行');
                return;
            }

            this.$confirm('删除后将不可恢复，确定要删除吗？', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                this.deldtlLoading = true;
                this.pageLoading = true;
                let proIds = this.selectAddRows.map(item => item.id);
                let res = await DeleteProcodeProcessPlanProByIds(proIds);
                if (res?.success == true) {
                    this.$message.success('删除成功');
                    this.selectAddRows.forEach(row => {
                        this.addForm.proList.splice(this.addForm.proList.indexOf(row), 1);
                    });
                    this.selectAddRows = [];
                }
                this.deldtlLoading = false;
                this.pageLoading = false;
            }).catch(() => {
                //this.$message({ type: 'info', message: '已取消计算' });
                this.deldtlLoading = false;
                this.pageLoading = false;
            });
        },
        onPltxyyclfaShow() {
            if (this.selectAddRows.length <= 0) {
                this.$message.warning('请至少勾选一行');
                return;
            }
            this.dialogPltxyyclfa_Value = null;
            this.targetRate_value = null;
            this.dialogVisiblePltxyyclfaLoad = false;
            this.dialogVisiblePltxyyclfa = true;
        },
        onPltxyyclfaSave() {
            if (!this.dialogPltxyyclfa_Value) {
                this.$message.warning('请选择运营处理方案');
                return;
            }
            if(this.dialogPltxyyclfa_Value == '提升利润' && !this.targetRate_value){
                this.$message.warning('运营处理方案为提升利润时，请填写目标利润');
                return;
            }
            this.selectAddRows.forEach(f => {
                f.operateProcessPlanValue = this.dialogPltxyyclfa_Value;
                let a = false;
                if(this.dialogPltxyyclfa_Value == '提升利润'){
                    f.targetRate = this.targetRate_value;
                    a = true
                } else {
                    f.targetRate = null
                }
                this.$nextTick(() => {
                    if(a){
                        this.$refs.table2.changecolumn_setTrue(["targetRate"]);
                    } else {
                        this.$refs.table2.changecolumn(["targetRate"]);
                    }
                });
            });
            this.dialogVisiblePltxyyclfa = false;
        },
        async handleCommand(command) {
            switch (command) {
                //批量填写运营处理方案
                case 'a':
                    await this.onPltxyyclfaShow()
                    break;
                //批量删除
                case 'b':
                    await this.onBatchDeleteDtl()
                    break;
            }
        },
        async bulkLoadingUn() {
            if (this.selectAddRows.length <= 0) {
                this.$message.warning('请至少勾选一行');
                return;
            }
            let fin = this.selectAddRows.find(item => !(item.platform === 1 || item.platform === 2 || item.platform === 6 || item.platform === 7 || item.platform === 9 || item.platform === 14));
            if (fin) {
                this.$message.warning('目前仅支持拼多多/天猫/淘宝/抖音/京东/快手');
                return;
            }

            //从后端判定是不是白名单 
            let selPros = this.selectAddRows.filter((f) => f.isBmd == false).map(m => m.proCode);
            let pros = await GetProcodeProcessPlanBmdListByProCodes(selPros)
            if (pros.success && pros.data.length > 0) {
                this.$message.warning('白名单发生变化，请刷新再操作');
                return;
            }
            let noBmdList = this.selectAddRows
                .filter((f) => f.isBmd == false)
                .map((item) => {
                    return item;
                });

            this.$showDialogform({
                path: `@/views/base/batchListingDelist.vue`,
                title: '批量上下架',
                autoTitle: false,
                args: {
                    checkdata: noBmdList
                },
                height: '650px',
                width: '80%',
            })
        },
        async onExportDtl() {
            this.$confirm('确定要导出吗？', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                this.pageLoading = true;
                let res = await ExportProcodeProcessPlanById2({ id: this.thismainId, ...this.dtlFilter });
                this.pageLoading = false;
                if (!res?.data) return
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '产品处理方案明细_' + new Date().toLocaleString() + '.xlsx');
                aLink.click()
            }).catch(() => {
                this.pageLoading = false;
            });
        },
    }
}
</script>
<style scoped>
.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
    margin-bottom: 15px;
}
::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
