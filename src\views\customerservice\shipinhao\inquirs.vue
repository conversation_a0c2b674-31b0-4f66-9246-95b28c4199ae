<template>
  <my-container v-loading="pageLoading">
    <!-- 顶部操作 -->
    <template #header>
      <el-button style="padding: 0;margin: 0;border: none;">
        <el-select v-model="filter.shopCodeList" placeholder="店铺" filterable multiple clearable collapse-tags style="width: 180px;">
          <el-option v-for="item in filterShopList" :key="item.shopCode" :label="item.shopName"
            :value="item.shopCode"></el-option>
        </el-select>
      </el-button>
      <el-button style="padding: 0;margin: 0;border: none;">
        <el-input v-model.trim="filter.snick" placeholder="昵称" style="width:160px;" clearable :maxlength="50" />
      </el-button>
      <el-button style="padding: 0;margin: 0;border:none">
        <el-date-picker style="width: 280px" v-model="filter.sdate" type="daterange" format="yyyy-MM-dd"
          value-format="yyyy-MM-dd" range-separator="至" start-placeholder="起始支付日期" end-placeholder="结束支付日期"
          :picker-options="pickerOptions" @change="handleDateChange" :clearable="false">
        </el-date-picker>
      </el-button>
      <el-button type="primary" @click="onSearch">查询</el-button>
      <el-button type="primary" @click="showSubstitute">代班管理</el-button>
      <!-- <el-button type="primary" @click="onImport">导入</el-button> -->
    </template>

    <!-- 列表 -->
    <yh_vxetableNotFixNum ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      @select='selectchange' :isSelection='false' :tableData='data.list' :tableCols='tableCols'
      :loading="listLoading" />

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="data.total" :checked-count="sels.length" @page-change="Pagechange"
        @size-change="Sizechange" />
    </template>

    <el-dialog title="咨询数据导入" :visible.sync="importdialogVisible" width="30%" v-dialogDrag>
      <el-row>
        <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
          <el-upload ref="upload" :auto-upload="false" :multiple="false" action accept=".xlsx"
            :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
            <template #trigger>
              <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
              @click="submitupload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
          </el-upload>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importdialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import yh_vxetableNotFixNum from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import cesTable from "@/components/Table/table.vue";
import YhUserelector from '@/components/YhCom/yh-userselector.vue';
import { formatTime } from "@/utils";
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { getInquirsList, deleteInquireAsync, batchDeleteInquirsAsync, importSPHInquirsAsyncSQ } from '@/api/customerservice/shipinhaoinquirs.js';

const tableCols = [
  { sortable: 'custom', istrue: true, width: '180', align: 'center', label: '店铺', prop: 'shopName' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '日期', prop: 'sdate', formatter: (row) => formatTime(row.sdate, 'YYYY-MM-DD') },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '客服昵称', prop: 'snick' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '咨询用户数', prop: 'inquirsCount' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '咨询会话数', prop: 'receiveCount' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '3分钟人工回复率', prop: 'threeSecondReplyRate', formatter: (row) => row.threeSecondReplyRate.toFixed(2) + "%" },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '平均回复时长（秒）', prop: 'responseTime' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '用户满意度', prop: 'satisDegree', formatter: (row) => row.satisDegree.toFixed(2) + "%" },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '不回复率', prop: 'noReplyRate', formatter: (row) => row.noReplyRate.toFixed(2) + "%" },
  { sortable: 'custom', istrue: true, width: '150', align: 'center', label: '导入时间', prop: 'createdTime' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '批次号', prop: 'batchNumber' },
  {
    istrue: true, type: "button", label: '操作', width: "120", btnList: [
      { label: "删除", handle: (that, row) => that.deleteInquire(row) },
      { label: '批次删除', handle: (that, row) => that.batchDeleteInquirs(row) }
    ]
  }
];

export default {
  name: "inquirs",
  components: { MyContainer, vxetablebase, YhUserelector, cesTable, yh_vxetableNotFixNum },
  data() {
    return {
      that: this,
      pageLoading: false,
      listLoading: false,
      uploadLoading: false,// 上传
      importdialogVisible: false,// 导入
      substitutedialogVisible: false,// 代班管理
      filter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        //过滤条件
        shopCodeList: [],
        snick: '',
        sdate: [],
        startDate: '',
        endDate: '',
        inquirsType: 0,
      },
      sels: [],// 列表选中列
      data: {},// 查询返回数据集
      tableCols: tableCols,
      fileList: [],// 导入文件列表
      filterShopList: [],// 店铺选择器列表

      pickerOptions: {
        shortcuts: [{
          text: '前一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime());
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
            const date2 = new Date(); date2.setDate(date2.getDate());
            picker.$emit('pick', [date1, date2]);
          }
        }]
      },

    };
  },
  async mounted() {
    //默认昨天日期
    let start = new Date();
    let end = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
    end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
    this.filter.sdate = [start, end];
    this.filter.startDate = start;
    this.filter.endDate = end;
    //获取店铺列表
    await this.getSPHShop();
    this.onSearch();
  },
  methods: {
    //店铺选择器
    async getSPHShop() {
      let res = await getshopList({ platform: 20, CurrentPage: 1, PageSize: 100000 });
      this.filterShopList = res.data.list;
    },
    selectchange: function (rows, row) {
      this.sels = [];
      rows.forEach(f => {
        this.sels.push(f);
      });
    },
    // 检查是否选择时间
    handleDateChange() {
      this.filter.startDate = this.filter.sdate ? this.filter.sdate[0] : null;
      this.filter.endDate = this.filter.sdate ? this.filter.sdate[1] : null;
    },
    //每页数量改变
    Sizechange(val) {
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.getList();
    },
    //当前页改变
    Pagechange(val) {
      this.filter.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.filter.orderBy = prop
        this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async getList() {
      this.listLoading = true;
      this.handleDateChange();
      try {
        const { data, success } = await getInquirsList(this.filter);
        if (success) {
          this.data = data;
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.listLoading = false;
      }
    },
    //查询分组
    onSearch() {
      //点击查询按钮时才将页数重置为1
      this.filter.currentPage = 1;
      this.$refs.pager.setPage(1);
      this.getList();
    },
    //删除
    async deleteInquire(row) {
      var that = this;
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteInquireAsync({ id: row.id }).then(response => {
          if (response?.success) {
            // 删除成功的处理逻辑
            this.$message.success('删除成功!');
            that.onSearch();
          } else {
            this.$message.error("删除失败");
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    //批量删除
    async batchDeleteInquirs(row) {
      var that = this;
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        batchDeleteInquirsAsync({ batchNumber: row.batchNumber }).then(response => {
          if (response?.success) {
            // 删除成功的处理逻辑
            this.$message.success('删除成功!');
            that.onSearch();
          } else {
            this.$message.error("删除失败");
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    //代班管理弹窗
    showSubstitute() {
      this.$showDialogform({
        path: `@/views/customerservice/shipinhao/sphSubstitute.vue`,
        title: '代班管理',
        args: {},
        height: '600px',
        width: '61%',
        callOk: this.afterSave,
      });
    },
    afterSave() {

    },
    onImport() {
      this.importdialogVisible = true;
      this.uploadLoading = false;
      this.$nextTick(() => {
        if (this.$refs.upload) {
          this.$refs.upload.clearFiles();
        }
      });
      this.fileList.splice(0, 1);
    },
    //上传文件
    async uploadFile(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      // form.append("groupType", 0);
      this.uploadLoading = true;
      const res = await importSPHInquirsAsyncSQ(form);
      this.uploadLoading = false;
      if (res?.success) {
        this.$message({ message: '上传成功,正在导入中...', type: "success" });
        this.importdialogVisible = false;
      }
    },
    //更改上传文件
    async uploadChange(file, fileList) {
      if (fileList.length == 2) {
        fileList.splice(1, 1);
        this.$message({ message: "只允许单文件导入", type: "warning" });
        return false;
      }
      this.fileList.push(file);
    },
    //移除上传文件
    uploadRemove() {
      this.fileList.splice(0, 1);
    },
    //提交上传文件
    async submitupload() {
      if (this.fileList.length == 0) {
        this.$message.warning('您没有选择任何文件！');
        return;
      }
      this.$refs.upload.submit();
      this.$refs.upload.clearFiles();
      this.fileList.splice(0, 1);
      this.importdialogVisible = false;
    },

  },
}

</script>
<style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }

  .levelQueryInfoBox {
    display: flex;
    margin-bottom: 10px;
  }

  .publicCss {
    width: 220px;
    margin-right: 10px;
  }

  //解决下拉菜单多选由文字太长导致样式问题
  ::v-deep .el-select__tags-text {
    max-width: 60px;
  }
</style>