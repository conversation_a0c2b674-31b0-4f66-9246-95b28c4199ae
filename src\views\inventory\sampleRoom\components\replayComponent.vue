<template>
  <div style="height: 500px; display: flex; flex-direction: column;">
    <el-button type="primary" @click="printQRCode()" style="margin-bottom: 10px;">打印</el-button>
    <div id="printid" v-if="printVisible" v-loading="printLoading" :style="qrcodeContainerStyle">
      <div v-for="(item, index) in goodsCodeList" :key="index" class="qrcode-item" style="margin-top: 2px;">
        <div style="display: flex; flex-direction: column; align-items: flex-start;">
          <img :id="'barcode1' + item" style="width: 90%; height: 40px;" />
          <div class="qrcode-id" style="text-align: left; font-size: 17px; margin-top: 1px;">
            <div style="margin-bottom: 1px;">{{ rowData.goodsCode || '--' }}</div>
            <div
              style="margin-bottom: 1px; width: 92%; display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical; overflow: hidden; text-overflow: ellipsis; word-break: break-word;  ">
              {{ rowData.goodsName || '--' }}
            </div>
            <div style="margin-bottom: 1px;">{{ rowData.goodsNumber || '--' }}　{{ rowData.bitNumber || '--' }}</div>
            <div>
              {{ rowData.numberShots || '--' }}次　
              {{ rowData.createdUserName || '--' }}　
              {{ rowData.shootingTime || '--' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import JsBarcode from 'jsbarcode'
import Print from 'print-js'

export default {
  name: "manageStorageLocation",
  props: {
    rowData: {
      type: Object,
      default: () => ({})
    },
    qrCodeWidth: {
      type: Number,
      default: 5
    },
    qrCodeHeight: {
      type: Number,
      default: 4
    }
  },
  data() {
    return {
      printVisible: false,
      printLoading: false,
      goodsCodeList: [],
      scrollbarWidth: 0,
      rowDisplayNum: 1
    }
  },
  computed: {
    qrcodeContainerStyle() {
      const pxPerCm = 37.795;
      return {
        width: `${this.qrCodeWidth * pxPerCm + 110}px`,
        height: `${this.qrCodeHeight * pxPerCm + 10}px`,
        display: 'grid',
        justifyContent: 'center',
        alignItems: 'center',
        pageBreakInside: 'avoid',
        paddingTop: '10px',
      }
    }
  },
  mounted() {
    this.goodsCodeList.push(this.rowData.goodsCode);
    this.printVisible = true;
    this.$nextTick(() => {
      this.onPrintMethod();
    });
  },
  methods: {
    async onPrintMethod() {
      this.goodsCodeList.forEach((item) => {
        JsBarcode(`#barcode1${item}`, item, { displayValue: false });
      });

      // 等待 DOM 和 JsBarcode 渲染完毕后自动触发打印
      setTimeout(() => {
        this.printQRCode();
      }, 300); // 可根据实际渲染速度调整时间
    },
    printQRCode() {
      Print({
        printable: 'printid',
        type: 'html',
        scanStyles: true,
        targetStyles: ['*'],
        onPrint: () => console.log('打印成功'),
        onError: () => console.error('打印失败')
      });
    }
  }
}
</script>

<style scoped lang="scss">
#printid {
  page-break-inside: avoid;

  .qrcode-item {
    display: flex;
    flex-direction: column;
    page-break-inside: avoid;
  }

  .qrcode-id {
    font-size: 10px;
    color: #333;
    text-align: left;
    max-height: 100px;
  }
}
</style>
