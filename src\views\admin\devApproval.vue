<template>
  <MyContainer>
    <template #header>
      <el-form class="top" :inline="true">
        <el-form-item label="申请时间">
          <el-date-picker v-model="createTimeRanges" type="daterange" unlink-panels range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 210px;"
            :value-format="'yyyy-MM-dd'" @change="changeCreateTime">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="期望交付时间">
          <el-date-picker v-model="expectTimeRanges" type="daterange" unlink-panels range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 210px;"
            :value-format="'yyyy-MM-dd'" @change="changeExpectTime">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-select v-model="ListInfo.status" clearable placeholder="请选择审批状态" style="width: 130px;">
            <el-option label="审批中" value="Running">
            </el-option>
            <el-option label="通过" value="Agree">
            </el-option>
            <el-option label="拒绝" value="Refuse">
            </el-option>
            <el-option label="中止" value="Terminated">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入审批编号" v-model="ListInfo.businessId" :clearable="true" maxlength="30" style="width: 130px;"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入提交人" v-model="ListInfo.originatorUserName" :clearable="true" style="width: 130px;"
            maxlength="10"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入验收人" v-model="ListInfo.checkUserName" :clearable="true" maxlength="10" style="width: 130px;"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入待审批人" v-model="ListInfo.userName" :clearable="true" maxlength="10" style="width: 130px;"></el-input>
        </el-form-item>
        <el-button type="primary" @click="getList('search')">查询</el-button>
      </el-form>
    </template>

    <el-dialog title="当前审批流程" class="dialog" :visible.sync="dialog.visiable" width="530px" v-dialogDrag>

      <el-timeline>
        <el-timeline-item v-for="(value, index) in tasks" :key="value.id""
      >
      <el-image
      class=" avatar" :src="value.avatar" fit="contain">
          <div slot="error" class="image-slot">
            <el-image class="image-slot"></el-image>
          </div>
          </el-image>
          <div class="user">
            {{ value.userName }}

            <span
              v-show="value.status == 'Apply' && (value.type == 'START_PROCESS_INSTANCE' || value.type == 'PROCESS_CC')">
              (申请人)
            </span>
            <span v-show="value.status == 'RUNNING'">
              (审核中)
            </span>
            <span v-show="value.status == 'TERMINATED' || value.type == 'TERMINATE_PROCESS_INSTANCE'">
              (已中止)
            </span>
            <span v-show="value.status == 'COMPLETED' && value.result == 'AGREE'">
              (已同意)
            </span>
            <span v-show="value.status == 'COMPLETED' && value.result == 'REFUSE'">
              (已拒绝)
            </span>
          </div>
          <div class="time">
            {{ value.finishTime }}
          </div>

          <div class="ccUsers">

            <div v-show="value.ccUsers != null && value.ccUsers.length != 0" class="ccTips">抄送人</div>
            <div v-for="(ccUser, index) in value.ccUsers" :key="index" class="ccUser">
              <el-image class="ccUserAvatar" :src="ccUser.ccUserAvatar" fit="contain">
                <div slot="error" class="image-slot">
                  <el-image class="image-slot"></el-image>
                </div>
              </el-image>
              <div class="ccUserName">
                {{ ccUser.ccUserName }}
              </div>
            </div>
          </div>

          <div class="remark" v-if="value.remark != '' && value.remark != null">
            {{ value.remark }}
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-dialog>

    <vxetablebase :id="'devApproval202408041348'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :row-config="{ isHover: true }" :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="审批过程" width="110" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex">
              <el-button type="text" style="margin: 0 auto;" @click="viewApproval(row)">查看</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getDevApprovalList, getDevApprovalView } from '@/api/admin/devApproval'
import dayjs from 'dayjs'
import { formatTime } from "../../utils/tools";

const tableCols = [
  { istrue: true, prop: 'createTime', label: '申请时间', width: '120', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'expectTime', label: '期望交付日期', width: '100', sortable: 'custom', align: 'center', formatter: (row) => formatTime(row.expectTime, "YYYY-MM-DD") },
  { istrue: true, prop: 'deadlineTime', label: '最晚交付时间', width: '120', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'businessId', label: '审批编号', width: '150', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'originatorDeptName', label: '所在部门', width: '240', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'applyDeptName', label: '申请部门', width: '80', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'originatorUserName', label: '提交人', width: '60', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'devSectionName', label: '开发板块名称', width: '270', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'requireResource', label: '需求资源', width: '120', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'prodUserName', label: '产品', width: '60', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'devUserName', label: '开发', width: '60', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'testUserName', label: '测试', width: '60', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'status', label: '审批状态', width: '80', align: 'center' },
  { istrue: true, prop: 'userName', label: '待审批人', width: '80', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'checkUserName', label: '验收人', width: '60', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'finishTime', label: '完成时间', width: '120', sortable: 'custom', align: 'center' },
]
export default {
  name: "devApproval",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startCreateTime: null,
        endCreateTime: null,
        startExpectTime: null,
        endExpectTime: null,
        businessId: null,
        originatorDeptName: null,
        applyDeptName: null,
        originatorUserName: null,
        applyUserName: null,
        createTime: null,
        devSectionName: null,
        expectTime: null,
        deadlineTime: null,
        requireResource: null,
        status: null,
        userName: null,
        result: null,
        finishTime: null,
        checkUserName: null,
        processInstanceId: null,
      },
      createTimeRanges: [],
      expectTimeRanges: [],
      tableCols,
      tableData: [],
      dialog: {
        visiable: false,
      },
      tasks: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    ///默认根据申请时间进行倒序排序
    if (this.ListInfo.orderBy == null) {
      this.ListInfo.orderBy = 'createTime';
      this.ListInfo.isAsc = false;
    }
    await this.getList()
  },
  methods: {
    async changeCreateTime(e) {
      this.ListInfo.startCreateTime = e ? e[0] : null;
      this.ListInfo.endCreateTime = e ? e[1] : null;
    },
    async changeExpectTime(e) {
      this.ListInfo.startExpectTime = e ? e[0] : null;
      this.ListInfo.endExpectTime = e ? e[1] : null;
    },
    async viewApproval(row) {
      this.ListInfo.processInstanceId = row.processInstanceId;
      const { data, success } = await getDevApprovalView(this.ListInfo);
      if (success) {
        this.tasks = data.list;
        this.dialog.visiable = true;
      } else {
        this.$message.error('请稍后重试');
      }
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.createTimeRanges != null && this.createTimeRanges.length == 0 && this.expectTimeRanges != null && this.expectTimeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startCreateTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endCreateTime = dayjs().format('YYYY-MM-DD')
        this.createTimeRanges = [this.ListInfo.startCreateTime, this.ListInfo.endCreateTime]
      }
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      const { data, success } = await getDevApprovalList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        for (let i = 0; i < data.list.length; i++) {
          if (data.list[i].requireResource != null) {
            const temp = JSON.parse(data.list[i].requireResource);
            for (let j = 0; j < temp.length; j++) {
              this.tableData[i].requireResource = j == 0 ? temp[j] : this.tableData[i].requireResource + "、" + temp[j];
            }
          }
        }

        this.total = data.total
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.dialog {
  text-align: center;

  .el-timeline {
    margin-top: 30px;
    height: 500px;
    overflow-y: scroll;

    .el-timeline-item {
      height: 100px;
      position: relative;
      padding-bottom: 50px;

      .avatar {
        width: 100px;
        height: 150px;
        position: absolute;
        left: 0;

        .image-slot {
          width: 50px;
          height: 50px;
        }
      }

      .user {
        width: 200px;
        text-align: left;
        margin-left: 60px;

      }

      .time {
        position: absolute;
        top: 0;
        right: 20px;
      }

      .remark {
        width: 310px;
        height: 50px;
        text-align: left;
        padding: 16px;
        margin-top: 10px;
        margin-left: 60px;
        padding-top: 8px;
        padding-bottom: 8px;
        background-color: rgb(242, 242, 247);
        color: rgb(120, 123, 129);
        overflow-y: scroll;
      }



      .ccUsers {
        width: 300px;
        margin-left: 50px;
        overflow: hidden;

        .ccTips {
          margin-top: 20px;
          margin-left: 10px;
          text-align: left;
        }

        .ccUser {
          float: left;
          margin: 10px;

          .ccUserAvatar {
            height: 40px;
            width: 40px;
          }

          .ccUserName {
            width: 40px;
            height: 16px;
            font-size: 12px;
            line-height: 16px;
          }
        }
      }
    }
  }
}

/*  工具箱位置  */
::v-deep .vxetoolbar20221212 {
  position: absolute;
  top: 100px;
  right: 5px;
  padding-top: 0;
  padding-bottom: 0;
  z-index: 999;
  background-color: rgb(255 255 255 / 0%);
}
</style>