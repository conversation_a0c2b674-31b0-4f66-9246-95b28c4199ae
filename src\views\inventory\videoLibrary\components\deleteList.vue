<template>
    <div>
        <vxetablebase :id="'deleteList202408041631'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='downRecordsortchange' :tableData='downRecordTableData' :tableCols='downloadRecordList'
            :isSelection="false" :isSelectColumn="false" v-loading="loading"
            style="width: 100%; height: 700px; margin: 0" />
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </div>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pageGetVideoMtlRec } from '@/api/operatemanage/VideoMaterial/VideoMaterial'
const downloadRecordList = [
    { istrue: true, prop: 'type', label: '操作类型', sortable: 'custom', width: 'auto', formatter: (row) => row.type == 0 ? '下载' : '删除' },
    { istrue: true, prop: 'dptName', label: '部门名称', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'operatorName', label: '操作人名称', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'title', label: '视频名称', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'createdTime', label: '删除时间', sortable: 'custom', width: 'auto' },
]
export default {
    name: "deleteList",
    components: {
        vxetablebase
    },
    data() {
        return {
            that: this,
            downloadRecordList,
            downRecordTableData: [],
            total: 0,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                type: 1,//0下载记录 1删除记录
            },
            loading: false
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        async getList() {
            this.loading = true
            const { data, success } = await pageGetVideoMtlRec(this.ListInfo)
            if (success) {
                this.downRecordTableData = data.list
                this.total = data.total
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        downRecordsortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss"></style>