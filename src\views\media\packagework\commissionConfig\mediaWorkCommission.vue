<template>
    <!-- 工作统计 -->
    <my-container> 
       <template #header>
           <el-button style="height: 28px;" type="primary" @click="onSearh">刷新</el-button>  
           <el-button style="height: 28px;" type="primary" @click="onExeprotShootingTask"  >导出</el-button>  
       </template> 
       <div style="height: 100%; min-width: 190px;"> 
           <vxetablebase :id="'mediaNCCommission'" 
           :hasSeq="false"
           :border="true" 
           :hasexpand='true' 
           :hascheck="false"
           :align="'center'"
           ref="table" 
           :that='that'  
           :tableData='tasklist'  
           :tableCols='tableCols'  
           :loading="listLoading" 
           :showsummary='true'
           :summaryarry='summaryarry'
           ></vxetablebase> 
       </div> 
   </my-container>
</template>
<script>  
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import MyContainer from "@/components/my-container";   
// import { getPersonnelPositionWorkInfo } from '@/api/media/shootingset';
import { addOrUpdatePersonnelPositionAsync, getPersonnelPositionAsync, sortPersonnelPosition, setPersonnelIsHsAsync, sendSalery,
     getPersonnelPositionWorkInfo, getHistoryPersonnelPositionWorkInfo } from '@/api/inventory/packagesSetProcessing.js';

const tableCols = [
       { istrue: true, prop: 'userName', label: '姓名', width: '120',  align: 'center' , fixed: 'left'  },
       { istrue: true, prop: 'companyName', label: '公司', width: '55',  align: 'center' , fixed: 'left'  },
       { istrue: true, prop: 'workPosition', label: '工作岗位', width: '80',  align: 'center' , fixed: 'left'  },
    //    { istrue: true, prop: 'commissionPosition', label: '提成岗位', width: '80',  align: 'center' , fixed: 'left'  },
       { istrue: true, prop: 'processCount', label: '加工', width: '120',  align: 'center' , fixed: 'left'  },
       { istrue: true, prop: 'quatitlyCount', label: '质检', width: '120',  align: 'center' , fixed: 'left'  },
       { istrue: true, prop: 'allotCount', label: '调拨', width: '120',  align: 'center' , fixed: 'left'  },
//    { istrue: true, label: '员工信息', width: '100', merge: true, prop: 'mergeField', 
//        cols:[ 
//        { istrue: true, prop: 'userName', label: '姓名', width: '80',  align: 'center' , fixed: 'left'  },
//        { istrue: true, prop: 'companyName', label: '公司', width: '55',  align: 'center' , fixed: 'left'  },
//        { istrue: true, prop: 'workPosition', label: '工作岗位', width: '80',  align: 'center' , fixed: 'left'  },
//        { istrue: true, prop: 'commissionPosition', label: '提成岗位', width: '80',  align: 'center' , fixed: 'left'  },
//     ]},
   //新品拍摄
   
];
export default {
   components: { MyContainer,vxetablebase  }, 
   props:{ 
       classType:{type:Number,default:0}, versionId:{type:String,default:"0"}, 
   },
   data() {
       return {
           that: this,  
           listLoading: false,
           tableCols: tableCols,  
           tasklist:[], 
           caclenum:1,
           summaryarry: {}
       };
   },
   //向子组件注册方法
   provide () {
       return { 
       }
   },
   async mounted() {
       await this.onSearh();
   }, 
   methods: {
    async onExeprotShootingTask() {
            this.$refs.table.exportData("工作统计")
    },
    async onSearh(){ 
        if(this.versionId == "0"){
            this.listLoading = true; 
            let ret =  await getPersonnelPositionWorkInfo();
            if(ret?.success){
                this.tasklist = ret.data?.list;
                this.summaryarry = ret.data?.summary;
            }
            this.listLoading = false;
        }else{
            this.onSearch();
        }
        
    },
    async onSearch() 
    {  
        // var pager = this.$refs.pager.getPager();
        this.listLoading = true;
        var res = await getHistoryPersonnelPositionWorkInfo({versionId:this.versionId}); 
        this.listLoading = false;   
        if(!res?.success) return;
        this.tasklist = res.data;
    }
   },
};
</script> 
<style lang="scss" scoped>
.content{
   display: flex;
   flex-direction: row;
}
::v-deep .vxe-header--column .vxe-cell--edit-icon,::v-deep span.vxe-input--extra-suffix{
   display: none;
}
::v-deep .vxe-table--render-default .vxe-header--column{
    line-height: 18px !important;
}
::v-deep .vxetoolbar20221212 {
    position: absolute;
    top: 30px;
    right: 5px;
    padding-top: 0;
    padding-bottom: 0;
    z-index: 999;
    background-color: rgb(255 255 255 / 0%);
}
</style>

