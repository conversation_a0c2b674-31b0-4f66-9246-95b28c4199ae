<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="年月:">
                    <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="平台:">
                    <el-select filterable v-model="filter.platform" placeholder="请选择平台" disabled clearable style="width:110px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="所属店铺:" label-position="right">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺" style="width: 180px">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="订单编号:" label-position="right">
                    <el-input v-model="filter.OrderNumber" placeholder="订单号" style="width:160px;" />
                </el-form-item>
                <el-form-item label="账单类型" label-position="right">
                    <el-select filterable clearable v-model="filter.FeeType" placeholder="请选择" style="width:160px;">
                        <el-option label="京东联盟扣款" value="京东联盟扣款"></el-option>
                        <el-option label="直赔退款代扣" value="直赔退款代扣"></el-option>
                        <el-option label="收付费违约金" value="收付费违约金"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>

                    <el-button type="primary" @click="onExportWalletDetailJDList(1)" style="width: 150px;" :loading="dialogShopSumLoading">导出店铺汇总</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :tableData='ZTCKeyWordList' :showsummary='true' :summaryarry='summaryarry' @select='selectchange' :isSelection='false' :tableCols='tableCols' :loading="listLoading" :isSelectColumn='false'>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </my-container>
</template>
<script>
    import cesTable from "@/components/Table/table.vue";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import { getAllList as getAllShopList, getList as getshopList } from '@/api/operatemanage/base/shop';
    import { formatPlatform, formatLink, platformlist } from "@/utils/tools";
    import { getWalletDetailJD as getPageList ,exportWalletDetailJDList} from '@/api/monthbookkeeper/financialDetail'
    const tableCols = [
        { istrue: true, prop: 'yearMonth', label: '年月', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'orderNo', label: '订单号', sortable: 'custom', width: '160', type: 'html' },
        { istrue: true, prop: 'accountCode', label: '账户代码', sortable: 'custom', width: '160'},
        { istrue: true, prop: 'accountName', label: '账户名称', sortable: 'custom', width: '160'},
        { istrue: true, prop: 'timeOccur', label: '创建时间', sortable: 'custom', width: '160'},
        { istrue: true, prop: 'currency', label: '币种', sortable: 'custom', width: '160'},
        { istrue: true, prop: 'inAmount', label: '收入金额', sortable: 'custom', width: '160', formatter: (row) => { return row.inAmount?.toFixed(2) } },
        { istrue: true, prop: 'outAmount', label: '支出金额', sortable: 'custom', width: '160', formatter: (row) => { return row.outAmount?.toFixed(2) } },
        { istrue: true, prop: 'accountBalance', label: '账户余额', sortable: 'custom', width: '160', formatter: (row) => { return row.accountBalance?.toFixed(2) } },
        { istrue: true, prop: 'billItem', label: '交易类型', sortable: 'custom', width: '160'},
        { istrue: true, prop: 'billType', label: '账单类型', sortable: 'custom', width: '160'},
        { istrue: true, prop: 'merchantOrder', label: '商户订单号', sortable: 'custom', width: '160'},
        { istrue: true, prop: 'transactionOrder', label: '交易订单号', sortable: 'custom', width: '160'},
        { istrue: true, prop: 'sourceMerchantOrder', label: '原商户订单号', sortable: 'custom', width: '160'},
        { istrue: true, prop: 'settlementRemark', label: '资金动账备注', sortable: 'custom', width: '160'}
    ];
    export default {
        name: "Users",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
        data() {
            return {
                that: this,
                filter: {
                    platform: 7,
                    yearMonth: null,
                    shopCode: null,
                    proCode: null,
                    OrderNumber: null,
                    FeeType: null
                },
                shopList: [],
                userList: [],
                groupList: [],
                platformlist: platformlist,
                ZTCKeyWordList: [],
                tableCols: tableCols,
                summaryarry: {},
                total: 0,
                pager: { OrderBy: "id", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                selids: [],
                dialogVisibleSyj: false,
                fileList: [],
                dialogShopSumLoading:false,
            };
        },
        async mounted() {
            this.onchangeplatform();
        },
        methods: {
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            async onchangeplatform() {
                const res1 = await getshopList({ platform: 7, CurrentPage: 1, PageSize: 100000 });
                this.shopList = res1.data.list
            },
            // async getShopList(){
            //   const res1 = await getAllShopList();
            //   this.shopList=[];
            //     res1.data?.forEach(f => {
            //       if(f.isCalcSettlement&&f.shopCode)
            //           this.shopList.push(f);
            //     });
            // },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.getList();
            },
            async getList() {
                
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...this.filter,
                };
                this.listLoading = true;
              
                const res = await getPageList(params);
               
                this.listLoading = false;
                this.total = res.data?.total
                this.ZTCKeyWordList = res.data?.list;
                this.summaryarry = res.data?.summary;
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
            
            async onExportWalletDetailJDList(curpage) {
            if (!this.filter.yearMonth || !this.filter.platform) {
                this.$message({ message: "请输入年月和平台", type: "warning" });
                return
            }
            this.$confirm('确认要导出吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...this.filter,
                };
                params.currentPage = curpage, params.pageSize = 100;
                console.log(params, "params");
                var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                this.dialogShopSumLoading = true;
                var res = await exportWalletDetailJDList(params);
                this.dialogShopSumLoading = false;
                loadingInstance.close();
                if (!res?.data) {
                    this.$message({ message: "没有数据", type: "warning" });
                    return
                }
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '京东钱包汇总_' + new Date().toLocaleString() + '.xlsx')
                aLink.click();
            }).catch(() => {
            });
        }
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
