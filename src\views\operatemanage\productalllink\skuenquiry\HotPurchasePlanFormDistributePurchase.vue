<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" label-width="130px" label-position="right" :disabled="!formEditMode">
                <el-form-item label="产品简称：" prop="goodsCompeteShortName"
                    v-if="form.planIds && form.planIds.length == 1">
                    {{ form.goodsCompeteShortName }}
                </el-form-item>
                <el-form-item v-if="mode == 1" label="供应商：" prop="supplierName">
                    {{ form.supplierName }}
                </el-form-item>
                <el-form-item label="采购：">
                    <el-select v-model="form.brandId" clearable filterable placeholder="请选择采购" @change="brandIdChange">
                        <el-option v-for="item in brandlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:center;padding-top:10px;">
                    <el-button @click="onClose">关闭</el-button>
                    <el-button type="primary" @click="onSave(true)">分配</el-button>
                </el-col>
            </el-row>
        </template>



    </my-container>
</template>
<script>


import MyContainer from "@/components/my-container";

import { getAllProBrand } from '@/api/inventory/warehouse'
import {
    SetHotPurchasePlanBrand
} from '@/api/operatemanage/productalllink/alllink';


export default {
    name: "HotPurchasePlanFormDistributePurchase",
    components: { MyContainer },
    data() {
        return {
            that: this,
            form: {
                planId: '',
                planIds: [],
                goodsCompeteShortName: '',
                supplierName: null,
                brandId: null,
                brandName: '',
                buildDocId: '',
            },
            //summaryarry: {},
            pageLoading: false,
            curRow: null,
            formEditMode: true,//是否编辑模式
            mode: 1,
            brandlist: []
        };
    },
    async mounted() {
        var res2 = await getAllProBrand();
        this.brandlist = res2.data.map(item => {
            return { value: item.key, label: item.value };
        });
    },
    computed: {

    },
    methods: {
        brandIdChange(v) {
            //debugger;
            if (v && v > 0) {
                let b = this.brandlist.find(x => x.value == v);
                if (b)
                    this.form.brandName = b.label;
            } else {
                this.form.brandName = '';
            }
        },
        onClose() {
            this.$emit('close');
        },
        async onSave(isClose) {
            if (await this.save()) {
                this.$emit('afterSave');
                if (isClose)
                    this.$emit('close');
            }
        },
        async loadData({ oid, goodsCompeteShortName, supplierName, brandId, brandName, mode, planIds, buildDocId }) {
            console.log(oid, 'oid');
            this.form.planId = oid;
            this.form.planIds = planIds
            this.form.goodsCompeteShortName = goodsCompeteShortName;
            this.form.supplierName = supplierName;
            this.form.brandId = brandId > 0 ? brandId + '' : null;
            this.form.brandName = brandName;
            this.form.buildDocId = buildDocId;
            this.mode = mode;
            return;
        },
        async save() {
            this.pageLoading = true;
            let saveData = { ...this.form, mode: this.mode };
            let errMsg = '';
            if (!(saveData.brandId && saveData.brandId > 0)) {
                errMsg = '请选择对应的采购！';
            }
            if (errMsg) {
                this.$alert(errMsg);
                this.pageLoading = false;
                return false;
            }
            let rlt = await SetHotPurchasePlanBrand(saveData);
            if (rlt && rlt.success) {
                this.$message.success('分配成功！');
            }
            this.pageLoading = false;
            return (rlt && rlt.success);
        }
    },
};
</script>
