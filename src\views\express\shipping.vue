<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="ListInfo.receiptDate" type="datetimerange" :picker-options="pickerOptions" range-separator="至"
                        start-placeholder="收寄开始日期" end-placeholder="收寄结束日期" style="width: 280px;margin-right: 10px;"
                        value-format="yyyy-MM-dd" format="yyyy-MM-dd" @change="changeTime" />
        <el-input v-model="ListInfo.waybillNo" placeholder="运单号" clearable class="publicCss" />
        <el-input v-model="ListInfo.deviceNo" placeholder="设备编号" clearable class="publicCss" />
        <el-input v-model="ListInfo.deviceGroupName" placeholder="设备组名称" clearable class="publicCss" />
        <div>
          <el-button type="primary" @click="getList('search')">查询</el-button>
          <el-button type="primary" @click="onModel">下载模板</el-button>
          <el-upload
            action=""
            :before-upload="beforeUpload"
            :http-request="onImport"
            :show-file-list="false"
            style="display: inline-block; margin-left: 10px;"
          >
            <el-button type="primary">导入模板</el-button>
          </el-upload>
          <!-- 添加清空按钮 -->
          <el-button type="warning" @click="clearFilters" style="margin-left: 10px;">清空</el-button>
        </div>
      </div>
    </template>
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
                  @sortchange='sortchange' :summaryarry='summaryarry' :showsummary='true' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
                  :isSelectColumn="false" style="width: 100%;  margin: 0; " :loading="loading" :height="'100%'"
                  @summaryClick="handleSummaryClick" />
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
    <!-- 添加趋势图弹窗 -->
    <el-dialog :visible.sync="dialogVisible" width="80%"  v-dialogDrag>
      <div class="trend-header">
        <el-select v-model="selectedTimeRange" placeholder="选择时间范围" @change="handleTimeRangeChange" style="width: 120px;margin-top: 10px;">
          <el-option
            v-for="item in timeRangeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </div>
      <div ref="trendChart" style="width: 100%; height: 400px;"></div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {downloadLink,   pickerOptions} from '@/utils/tools'

import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import {shippingDataPage, shippingDataImport, getShippingDataTrend} from '@/api/order/orderData'
// 引入 ECharts
import * as echarts from 'echarts';

const tableCols = [
  {sortable: 'custom', width: '150', align: 'center', prop: 'deviceGroupName', label: '设备组名称',},
  {sortable: 'custom', width: '120', align: 'center', prop: 'deviceNo', label: '设备编号',},
  {sortable: 'custom', width: '120', align: 'center', prop: 'packagePersonName', label: '集包人员',},
  {sortable: 'custom', width: '120', align: 'center', prop: 'channel', label: '电商渠道',},
  {sortable: 'custom', width: '120', align: 'center', prop: 'shopCode', label: '店铺代码',},
  {sortable: 'custom', width: '150', align: 'center', prop: 'waybillNo', label: '运单号',},
  {sortable: 'custom', width: '100', align: 'center', prop: 'status', label: '状态',},
  {
    sortable: 'custom',
    width: '180',
    align: 'center',
    prop: 'receiptTime',
    label: '收寄时间',
  },
  // {sortable: 'custom', width: '200', align: 'center', prop: 'sendAddress', label: '发货地址',},
  {sortable: 'custom', width: '120', summaryEvent: true, align: 'center', prop: 'actualWeight', label: '系统重量(g)',},
  {sortable: 'custom', width: '120', summaryEvent: true, align: 'center', prop: 'actualFee', label: '实收资费（元）',},
  {
    sortable: 'custom',
    width: '180',
    align: 'center',
    prop: 'createTime',
    label: '创建时间',
  },
]
export default {
  name: "scanCodePage",
  components: {
    MyContainer, vxetablebase, dateRange
  },
  data() {
    return {
      that: this,
      summaryarry: {},
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        receiptDate: [], // 收寄日期
        waybillNo: null,
        deviceNo: null,
        deviceGroupName: null,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
      isExport: false,
      dataTime:[],
      // 添加趋势图数据存储变量
      trendData: {
        dates: [],
        avgActualWeights: [],
        totalActualFees: [],
        totalActualWeights: [], // 新增总重量数据,
        totalWaybillNo: []
      },
      // 添加弹窗显示变量
      dialogVisible: false,
      // 添加时间范围选择
      timeRangeOptions: [
        { label: '近一周', value: 7 },
        { label: '近15天', value: 15 },
        { label: '近一月', value: 30 },
        { label: '近三月', value: 90 },
        { label: '近半年', value: 180 }
      ],
      selectedTimeRange: null, // 修改默认值为null
    }
  },
  async mounted() {
    // 设置默认收寄日期范围为近3天
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
    this.ListInfo.receiptDate = [start, end];
    this.ListInfo.receiptStartTime = dayjs(this.ListInfo.receiptDate[0]).format('YYYY-MM-DD') + ' 00:00:00';
    this.ListInfo.receiptEndTime = dayjs(this.ListInfo.receiptDate[1]).format('YYYY-MM-DD') + ' 23:59:59';
    await this.getList();
  },
  methods: {
    async changeTime(e) {
      if (this.ListInfo.receiptDate) {
        this.ListInfo.receiptStartTime = dayjs(this.ListInfo.receiptDate[0]).format('YYYY-MM-DD') + ' 00:00:00';
        this.ListInfo.receiptEndTime = dayjs(this.ListInfo.receiptDate[1]).format('YYYY-MM-DD') + ' 23:59:59';
      } else {
        this.ListInfo.receiptStartTime = null;
        this.ListInfo.receiptEndTime = null;
      }
      await this.getList();
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1;
        this.$refs.pager.setPage(1);
      }

      this.loading = true;
      // 使用时将下面的方法替换成自己的接口
      try {
        const {data, success} = await shippingDataPage(this.ListInfo);
        if (success) {
          console.log("data", data);
     this.tableData = data.list;
          this.total = Number(data.total);
          this.summaryarry = data.summary
          this.loading = false;
        } else {
          //获取列表失败
          this.loading = false;
          this.$message.error('获取列表失败');
        }
      } catch (error) {
        //
        this.loading = false;
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({order, prop}) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    onModel() {
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250301/1895676609533018113.xlsx', '集包揽收模板.xlsx');
    },
    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.ms-excel' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      if (!isExcel) {
        this.$message.error('只能上传Excel文件 (.xlsx, .xls)!');
        return false;
      }
      // 添加文件大小校验，限制文件大小不超过10MB
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        this.$message.error('文件大小不能超过10MB!');
        return false;
      }
      return true;
    },
    async onImport(options) {
      const file = options.file;
      // 添加 loading 状态
      this.loading = true;
      try {
        const response = await shippingDataImport(file);
        if (response?.success) {
          this.$message.success('上传成功,正在导入中(可在任务进度中查看)..');
          this.getList();
        } else {
          this.$message.error('导入失败');
        }
      } catch (error) {
        this.$message.error('导入失败');
      } finally {
        // 确保 loading 状态在最后被关闭
        this.loading = false;
      }
    },
    clearFilters() {
      this.ListInfo.receiptDate = null;
      this.ListInfo.receiptStartTime = null;
      this.ListInfo.receiptEndTime = null;
      this.ListInfo.waybillNo = null;
      this.ListInfo.deviceNo = null;
      this.ListInfo.deviceGroupName = null;
      this.getList();
    },
    // 添加字数限制的方法
    validateInputLength(value, maxLength) {
      if (value && value.length > maxLength) {
        this.$message.error(`输入内容不能超过 ${maxLength} 个字符`);
        return value.slice(0, maxLength);
      }
      return value;
    },
    // 获取趋势图数据的方法
    async getTrendData() {
      try {
        // 根据选择的时间范围设置日期
        let end = new Date();
        let start = new Date();
        if (this.selectedTimeRange) {
          start.setTime(start.getTime() - 3600 * 1000 * 24 * this.selectedTimeRange);
        } else {
          if (!this.ListInfo.receiptDate) {
             // 开始时间变成30天以前
             start = dayjs(start).add(-30, 'day')
          } else {
            start = this.ListInfo.receiptDate[0];
            end = this.ListInfo.receiptDate[1];
          }
        }


        // 创建趋势图专用的查询参数，保留主查询条件
        const trendQueryParams = {
          ...this.ListInfo,
          receiptDate: [start, end],
          receiptStartTime: dayjs(start).format('YYYY-MM-DD') + ' 00:00:00',
          receiptEndTime: dayjs(end).format('YYYY-MM-DD') + ' 23:59:59'
        };

        const { data, success } = await getShippingDataTrend(trendQueryParams);
        if (success) {
          this.trendData.dates = data.map(item => item.date);
          this.trendData.avgActualWeights = data.map(item => item.avgActualWeight);
          this.trendData.totalActualFees = data.map(item => item.totalActualFee);
          this.trendData.totalActualWeights = data.map(item => item.totalActualWeight);
          this.trendData.totalWaybillNo = data.map(item => item.totalWaybillNo);
          this.initTrendChart();
        } else {

          this.$message.error('获取趋势数据失败');
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 初始化趋势图的方法
    initTrendChart() {
      const chartDom = this.$refs.trendChart;
      const myChart = echarts.init(chartDom);
      const option = {
        title: {
          text: '趋势图'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['平均重量', '总实收资费', '总重量', '包裹数量']
        },
        xAxis: {
          type: 'category',
          data: this.trendData.dates
        },
        yAxis: [
          {
            type: 'value',
            name: '重量(g)',
            position: 'left'
          },
          {
            type: 'value',
            name: '实收资费（元）',
            position: 'right'
          },
          {
            type: 'value',
            name: '数量(个)',
            position: 'left',
            offset: 80  // 偏移量，避免与第一个y轴重叠
          }
        ],
        series: [
          {
            name: '平均重量',
            type: 'line',
            yAxisIndex: 0,
            data: this.trendData.avgActualWeights
          },
          {
            name: '总实收资费',
            type: 'line',
            yAxisIndex: 1,
            data: this.trendData.totalActualFees
          },
          {
            name: '总重量',
            type: 'line',
            yAxisIndex: 0,
            data: this.trendData.totalActualWeights
          },
          {
            name: '包裹数量',
            type: 'line',
            yAxisIndex: 2,  // 修改为使用第三个y轴
            data: this.trendData.totalWaybillNo
          }
        ]
      };
      option && myChart.setOption(option);
    },
    // 处理点击汇总行的方法
    handleSummaryClick(column) {
      this.dialogVisible = true;
      this.selectedTimeRange = null;
      this.getTrendData();
    },
    // 处理时间范围变化
    handleTimeRangeChange(value) {
      if (value) {
        this.selectedTimeRange = value;
        this.getTrendData();
      }
    },
    // 监听查询条件变化
    watchQueryConditions: {
      handler(newVal) {
        if (this.dialogVisible) {
          this.getTrendData();
        }
      },
      deep: true
    }
  },
  watch: {
    'ListInfo.waybillNo': function (newVal) {
      this.ListInfo.waybillNo = this.validateInputLength(newVal, 50);
    },
    'ListInfo.deviceNo': function (newVal) {
      this.ListInfo.deviceNo = this.validateInputLength(newVal, 50);
    },
    'ListInfo.deviceGroupName': function (newVal) {
      this.ListInfo.deviceGroupName = this.validateInputLength(newVal, 50);
    },
    // 添加对查询条件的监听
    ListInfo: {
      handler(newVal) {
        if (this.dialogVisible) {
          this.getTrendData();
        }
      },
      deep: true
    }
  }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}

.trend-header {
  margin-bottom: 20px;
  text-align: right;
}

/* 添加拖拽相关样式 */
.dialog-title-container {
  cursor: move;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 0;
}

/* 确保el-dialog在拖拽时保持在视口内 */
:deep(.el-dialog) {
  position: absolute;
  margin: 0 auto !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

:deep(.el-dialog__header) {
  cursor: move;
}
</style>
