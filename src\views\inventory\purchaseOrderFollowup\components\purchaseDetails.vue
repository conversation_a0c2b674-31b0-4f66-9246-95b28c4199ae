<template>
    <MyContainer>
        <vxetablebase ref="table" id="20241101175606" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            :isRemoteSort="false" style="width: 100%;height: 95%;  margin: 0" :loading="loading">
        </vxetablebase>
        <div>共{{ tableData ? tableData.length + '条' : 0 + '条' }}</div>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import dateRange from "@/components/date-range/index.vue";
import { queryPurchaseOrderDetailAsync } from '@/api/inventory/purchaseOrderTrack'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsName', label: '名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'count', label: '采购数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'price', label: '单价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'sumPrice', label: '金额', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'packCount', label: '标准装箱数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'packedCount', label: '箱数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'inCount', label: '已入库数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'nonInCount', label: '差异数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'nonInAmont', label: '未入库金额', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'inventoryDay', label: '分仓可用天数', },
    {
        sortable: 'custom', width: '120', align: 'center', prop: 'remainingAvailableDays', label: '分仓剩余库存可用天数', formatter: (row) => {
            if (row.remainingAvailableDays === 'Infinity') {
                return '&'
            } else if (row.remainingAvailableDays !== null) {
                return row.remainingAvailableDays
            } else {
                return ''
            }
        }
    },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    props: {
        buyNo: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            that: this,
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async getList() {
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await queryPurchaseOrderDetailAsync(this.buyNo)
                if (success) {
                    this.tableData = data
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                }
            } catch (error) {
                this.loading = false
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
