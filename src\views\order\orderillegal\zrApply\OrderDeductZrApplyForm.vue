<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" ref="form" label-width="120px" label-position="right" >       
                <el-row>
                    <el-col :span="23" :offset="1">
                        <span style="color:red">
                            申诉须知：
                            <br/>
1、申诉基础步骤：发起申诉 --> 提交申诉凭证 --> 审核凭证 --> 判定申诉结果 --> 申诉通过后调整新责任部门，原部门责任及相关事宜剔除
<br/>
2、申诉机会仅一次，请您使用好申诉权益。
<br/>
3、申诉时间为责任计算时间(非扣款时间)起当天17:30至次日10:00，超时导致申诉入口关闭，无法支持再次申诉。
                        </span>
                    </el-col>
                </el-row>        
                <el-row>
                    <el-col :span="4">
                        <el-form-item label="平台：">
                            {{ form.deductPlatform==1?'淘系':'拼多多' }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="线上单号：">
                            <el-button type="text" @click="showLogDetail(form.orderNo)">{{ form.orderNo }}</el-button>
                            <span v-if="form.otherInfo && form.otherInfo.a" style="margin-left:5px;">扣款金额:{{ form.otherInfo.a.amountPaid }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="扣款时间：">
                            {{form.deductOccurTime}}
                        </el-form-item>
                    </el-col>    
                    <el-col :span="7">
                        <el-form-item label="扣款原因：">
                            {{illegalTypeName}}
                        </el-form-item>
                    </el-col>                 
                </el-row>  
                <el-row>  
                    <el-col :span="4">
                        <el-form-item label="原责任类型：">
                            {{form.orgZrType1}}
                        </el-form-item>
                    </el-col>   
                    <el-col :span="7">
                        <el-form-item label="原责任原因：">
                            {{form.orgZrType2}}
                        </el-form-item>
                    </el-col>                      
                    <el-col :span="6">
                        <el-form-item label="新责任类型：" prop="newZrType1" :rules="[
                                                        { required: true, message: '请选择新责任类型', trigger: ['blur', 'change'] }    
                                                        ]">
                                    <el-select v-if="formEditMode || (isAuditor && form.applyState==1)" v-model="form.newZrType1" @change="newZrType1Change">
                                        <el-option v-for="(v,key) in deductOrderZrType12Tree" :key="key" :label="key" :value="key"></el-option>
                                    </el-select>
                                    <span v-else>{{ form.newZrType1 }}</span>
                                </el-form-item>
                    </el-col>    
                    <el-col :span="7">
                        <el-form-item label="新责任原因：" prop="newZrType2" :rules="[
                                                        { required: true, message: '请选择新责任原因', trigger: ['blur', 'change'] }    
                                                        ]">
                                    <el-select  v-if="formEditMode || (isAuditor && form.applyState==1)"  v-model="form.newZrType2" >
                                        <el-option v-for="item in deductOrderZrType12Tree[form.newZrType1]" :key="item.zrType2" :label="item.zrType2"  :value="item.zrType2"></el-option>
                                    </el-select>
                                    <span v-else>{{ form.newZrType2 }}</span>
                                </el-form-item>
                    </el-col>                  
                </el-row>  
                <el-row>  
                    <el-col :span="4">
                        <el-form-item label="原责任部门：">
                            {{form.orgZrDeptAction}}
                        </el-form-item>
                    </el-col>   
                    <el-col :span="7">
                        <el-form-item label="原责任规则：">
                            {{form.orgZrConditionFullName}}
                        </el-form-item>
                    </el-col>                      
                    <el-col :span="6">
                        <el-form-item label="原责任原因2：" prop="orgZrReason" >                            
                            {{ form.orgZrReason }}
                        </el-form-item>
                    </el-col>    
                    <el-col :span="7">
                        <el-form-item label="原责任人：">
                            {{form.orgMemberName}}
                        </el-form-item>
                    </el-col>                  
                </el-row>  
                <el-row>     
                    
                    <el-col :span="4">
                        <el-form-item label="新责任部门：" prop="newZrDeptAction" :rules="[
                        { required: true, message: '请选择新责任部门', trigger: ['blur', 'change'] }    
                        ]">
                            <el-select v-if="formEditMode || (isAuditor && form.applyState==1)" v-model="form.newZrDeptAction" @change="newZrDeptActionChange">
                                <el-option v-for="item in zrDeptActions" :value="item.zrDeptAction" :label="item.zrDeptAction"></el-option>
                            </el-select>
                            <span v-else>{{ form.newZrDeptAction }}</span>
                        </el-form-item>
                    </el-col>   
                    <el-col :span="7">
                        <el-form-item label="申诉理由：" prop="newZrConditionFullName" :rules="[
                        { required: true, message: '请填写新责任部门对应申诉理由', trigger: ['blur'] }    
                        ]">
                            <el-input  v-if="formEditMode" type="textarea" v-model="form.newZrConditionFullName" clearable maxlength="100" show-word-limit />
                            <span v-else>{{ form.newZrConditionFullName }}</span>
                        </el-form-item>
                    </el-col>                      
                    <el-col :span="6">
                        <el-form-item label="新责任原因2：" prop="newZrReason" >                                              
                            <span >{{ form.newZrType2 }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="新责任人：" prop="newMemberName" >
                            <template  v-if="formEditMode || (isAuditor && form.applyState==1)" >
                                <el-select v-if="form.newZrDept=='采购'" v-model="form.newMemberId" filterable @change="newMemberIdChange">
                                    <el-option v-for="item in brandlist" :key="item.key" :label="item.value"  :value="item.key"/>
                                </el-select>
                                <el-select v-else-if="form.newZrDept=='运营'" v-model="form.newMemberId" filterable @change="newMemberIdChange">
                                    <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
                                </el-select>  
                                <YhUserelector v-else-if="form.newZrDept!='机器人' && form.newZrDept!='外部'  && form.newZrDept!='快递'" 
                                            :value.sync="form.newMemberDDUserId" :text.sync="form.newMemberDDUserName"
                                        ></YhUserelector>
                                <el-input  v-else  v-model="form.newMemberName" clearable maxlength="10"  />
                            </template>                            
                            <span v-else>{{ form.newMemberName }}</span>
                        </el-form-item> 
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24" style="max-height:400px;overflow:auto">
                        <el-form-item label="定责资料："  prop="applyContent" :rules="[
                        { required: true, message: '请填写新定责资料', trigger: ['blur'] }    
                        ]">
                            <yh-quill-editor :value.sync="form.applyContent" v-if="formEditMode"></yh-quill-editor>
                            <div v-else v-html="form.applyContent" class="tempdiv"></div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row v-if="!!form.firstAuditState">                 
                    <el-col :span="4">
                        <el-form-item label="是否认可：" prop="firstAuditState" >
                            <el-radio-group v-model="form.firstAuditState" disabled>
                                <el-radio :label="-1">不认可</el-radio>
                                <el-radio :label="1">认可</el-radio>
                            </el-radio-group>           
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="初审时间：" prop="firstAuditTime" >
                            {{ form.firstAuditTime }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="初审人：" prop="firstAuditUserName" >
                            {{ form.firstAuditUserName }}
                        </el-form-item>
                    </el-col>
                    
               </el-row>
               
               <el-row  v-if="!!form.firstAuditState">
                    <el-col :span="24" style="max-height:400px;overflow:auto">
                        <el-form-item label="初审资料："  prop="firstAuditRemark" >                          
                            <div v-html="form.firstAuditRemark" class="tempdiv"></div>
                        </el-form-item>                        
                    </el-col>
               </el-row>
               <el-row v-if="form.firstAuditRecords && form.firstAuditRecords.length>0">
                    <el-col :span="24">
                        <el-form-item label="初审转派记录：" >
                            <table>
                                <tr v-for=" r in form.firstAuditRecords">
                                    <td>                                        
                                        {{ r.opTime }}
                                        <br/>
                                        【{{ r.opUserName }}】转【{{r.newMemberName}}】                                       

                                    </td>
                                    <td>
                                        <div  v-html="r.opContent" class="tempdiv"></div> 
                                    </td>
                                </tr>
                            </table>
                        </el-form-item>
                    </el-col>
               </el-row>
                      

                <el-row v-if="form.id>0 && (form.applyState==-1 || form.applyState==2)">
                    <el-col :span="4">
                        <el-form-item label="审核状态：">
                            {{fmtApplyState(form.applyState)}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="审核时间：">
                            {{form.auditTime}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="审核人：" v-if="checkPermission('order:orderdeductmoney:ShowZrApplyAuditor')">
                            {{form.auditUserName}}
                        </el-form-item>
                    </el-col>
                                   
                </el-row>
                <el-row  v-if="form.id>0 && (isAuditor || form.applyState==-1 || form.applyState==2)">
                    <el-col :span="24">
                        <el-form-item label="审核意见：" :rules="[
                        { required: true, message: '请填写审核意见', trigger: ['blur', 'change'] }    
                        ]">
                            <el-input  v-if="isAuditor && form.applyState==1" type="textarea"  :rows="3" v-model="form.auditRemark" clearable maxlength="100" show-word-limit />
                            <span v-else>{{ form.auditRemark }}</span>                         
                        </el-form-item>
                    </el-col>     
                </el-row>
                      
                
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">  
                    <el-button @click="onClose">关闭</el-button>                     
                    <el-button v-if="mode<3 && (!form.applyState || form.applyState<1)" type="primary" @click="onSave(true)">提交申诉</el-button>   
                    <el-button v-if="form.applyState==1 && isAuditor" type="primary" @click="audit(true)">审核通过</el-button>
                    <el-button  v-if="form.applyState==1 && isAuditor" type="danger" @click="audit(false)">审核驳回</el-button>
                </el-col>
            </el-row>
        </template>

        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            append-to-body>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" :isTx="isTx" style="z-index:10000;height:600px" />
        </el-dialog>

    </my-container>
</template>
<script>  

    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode ,DeductOrderZrDeptActions,DeductOrderZrReasons
        ,DeductOrderZrType12,DeductOrderZrType12Tree } from "@/utils/tools";
    import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import { GetDeductZrAppeal4CRUD,SaveDeductZrAppeal,AuditDeductZrAppeal } from "@/api/order/orderdeductmoney";
    import {getAllWarehouse,getAllProBrand} from '@/api/inventory/warehouse'

    import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'

    import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";

    import YhUserelector from '@/components/YhCom/yh-userselector.vue'

    import {
        getDirectorList,
        getDirectorGroupList,
        getProductBrandPageList,
        getList as getshopList,
    } from "@/api/operatemanage/base/shop";


    //机器人查询状态 1成功、0下架、-1失败
    const fmtJqrNoticeState=(v)=>{
        switch(v){
            case 0:return '下架';
            case 1:return '已查询';
            case -1:return '失败';
        }
        return ' ';
    };

    const fmtApplyState=function(val){
        if(val==-1) return "已拒绝";
        else if(val==0) return "待申请";
        else if(val==1) return "申请中";
        else if(val==2) return "已审核";
        return val;
    }

    export default {
        name: "OrderDeductZrApplyForm",
        components: { MyContainer, MyConfirmButton,  YhQuillEditor ,OrderActionsByInnerNos , YhUserelector},
        data() {
            return {              
                that: this,
                mode:3,
                illegalTypeList:[],
                zrDeptActions:DeductOrderZrDeptActions,
                zrReasons:DeductOrderZrReasons,

                deductOrderZrType12:DeductOrderZrType12,
                deductOrderZrType12Tree:DeductOrderZrType12Tree,
               

                brandlist: [], 
                directorList: [],

                form: {
                    firstAuditRecords:null,
                    newMemberName:"",
                    newMemberId:null,
                    newMemberDDUserId:"",
                    newMemberDDUserName:""
                },
            
                //summaryarry: {},
                pageLoading: false,
                curRow: null,
                formEditMode: true,//是否编辑模式              
                dialogHisVisible:false,
                isTx:false,      
            };
        },
        async mounted() {
            let illegalType = await ruleIllegalType();
            this.illegalTypeList = illegalType.options;
            await this.setBandSelect();
            await this.getDirectorlist();
        },
        computed: {      
            illegalTypeName(){
                let opt=this.illegalTypeList.find(x=>x.value==this.form.illegalType);
                if(opt)
                    return opt.label;
                else
                    return '';
            }
        },
        methods: {   
             //设置新责任类型原因
             newZrType1Change(){             
                this.form.newZrType2="";              
            
            },
            async getDirectorlist () {
                const res1 = await getDirectorList({});
                const res2 = await getDirectorGroupList({});

                this.directorList = res1.data;
                this.directorGroupList = [{ key: "0", value: "未知" }].concat(
                    res2.data || []
                );
            }, 
            async setBandSelect(){
                var res= await  getAllProBrand();
                if (!res?.success) return;
                this.brandlist = res.data;
            }, 
            fmtApplyState:fmtApplyState, 
            fmtJqrNoticeState:fmtJqrNoticeState,
            showLogDetail (orderNo) {
                this.isTx=this.form.deductPlatform==1;
                this.dialogHisVisible = true;
                this.orderNo = orderNo;
            },  
            newZrDeptActionChange(){
              
                if(this.form.newZrDeptAction){                    
                    let opt=this.zrDeptActions.find(x=>x.zrDeptAction==this.form.newZrDeptAction);
                    if(opt){
                        if(this.form.newZrDept!=opt.zrDept){
                            this.form.newMemberName="";
                            this.form.newMemberId=null;
                            this.form.newMemberDDUserId="";
                            this.form.newMemberDDUserName="";
                        }
                        this.form.newZrAction=opt.zrAction;
                        this.form.newZrDept=opt.zrDept;
                    }else{
                        this.form.newMemberName="";
                        this.form.newMemberId=null;
                        this.form.newMemberDDUserId="";
                        this.form.newMemberDDUserName="";
                    }                    
                }
            },   
            newMemberIdChange(){  
                let arr=null;
                if(this.form.newZrDept=="采购"){
                    arr=[...this.brandlist];                   
                }
                else if(this.form.newZrDept=="运营"){
                    arr=[...this.directorList];                    
                }    
                
                if(arr!=null && arr && this.form.newMemberId){                  
                    let opt=arr.find(x=>x.key==this.form.newMemberId);
                    if(opt){
                        this.form.newMemberName=opt.value;                      
                    }
                }
            },   
            //审核
            async audit(isPass){
                if(!this.form.auditRemark){
                    this.$message.error('请填写审批意见！');
                    return false;
                }

                if(isPass && this.form.newZrDept!="快递" && !this.form.newMemberName){
                    this.$message.error('请填写责任人！');
                    return false;
                }

                // let dto={
                //     ...this.form,                                          
                // };
                let dto={};
                dto.id=this.form.id;
                dto.auditState=isPass?1:0;
                dto.auditRemark=this.form.auditRemark;
                dto.newZrDeptAction=this.form.newZrDeptAction;
                dto.newZrDept=this.form.newZrDept;
                dto.newZrAction=this.form.newZrAction;
                dto.newZrReason=this.form.newZrReason;
                dto.newMemberId=this.form.newMemberId;
                dto.newMemberName=this.form.newMemberName;
                dto.newMemberDDUserId=this.form.newMemberDDUserId;
                dto.newMemberDDUserName=this.form.newMemberDDUserName;

                dto.newZrReason=this.form.newZrType2;
                dto.newZrType1=this.form.newZrType1;
                dto.newZrType2=this.form.newZrType2;


                this.pageLoading = true;

                let rlt=await AuditDeductZrAppeal(dto);
                if(rlt && rlt.success){
                    this.$message.success('操作成功！');
                    this.$emit('afterSave');
                    this.$emit('close');
                }      
                this.pageLoading = false;          
            },   
            onClose(){
                this.$emit('close');
            },  
            async onSave(isClose){
                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData({id, orderNo, occTime, platform,mode,isAuditor}) {     
                let self=this;         
                self.pageLoading = true;
                self.formEditMode = mode!=3;
                self.mode = mode;     
                self.isAuditor= !!isAuditor;       

                let rlt = await GetDeductZrAppeal4CRUD( {id:id, orderNo:orderNo, occTime:occTime, platform:platform} );
                if (rlt && rlt.success) {
                    let formDto= rlt.data;   
                    if(formDto.newMemberId)   
                        formDto.newMemberId=formDto.newMemberId.toString();

                    this.form = formDto;                                           
                    if(this.form.applyState>0){
                        self.mode=3;
                        self.formEditMode = self.mode!=3;
                    }
                    this.pageLoading = false;
                }else{
                    this.onClose();
                }
                self.pageLoading = false;
            },
            async save() {
                this.pageLoading = true;
                
                let saveData = { ...this.form };            
                saveData.applyState=1;

                saveData.newZrReason=this.form.newZrType2;
              
                try {
                    await this.$refs["form"].validate();
                } catch (error) {
                    this.pageLoading = false;
                    return false;
                } 
               

                let rlt = await SaveDeductZrAppeal(saveData);
                if (rlt && rlt.success) {
                    this.$message.success('提交成功！');           
                }

                this.pageLoading = false;
              
                return (rlt && rlt.success);
            }
        },
    };
</script>
<style lang="scss" scoped>
   
    .tempdiv ::v-deep img {
        width: auto;
        max-width: 1000px;
    }
</style>