<template >
    <my-container v-loading="pageLoading" style="height:100%">
        <ces-table style="height:90%" ref="dingdingcallbackfailedtable" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='failedList' :isSelection='false' :summaryarry="summaryarry"
            :tableCols='tableCols' :isSelectColumn="true">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-date-picker style="width: 225px" v-model="filter.dates" type="daterange" format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始时间"
                            end-placeholder="结束时间">
                        </el-date-picker>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input placeholder="流程名称" v-model="filter.title" style="width: 130px" clearable
                            maxlength="20"></el-input>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getNewOrderListAsync" />
        </template>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import { callBackFailedPageList } from '@/api/admin/dingding'
const tableCols = [
    { istrue: true, prop: 'createdTime', label: '事件时间', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'type', label: '事件类型', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'processCode', label: '所属KEY', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'processInstanceId', label: '流程实例ID', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'title', label: '流程名称', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'corpId', label: 'corpId', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'againCount', label: '状态', width: '150', sortable: 'custom', formatter: (row) => row.againCount>0?"已同步":"未同步" },
    { istrue: true, prop: 'againTime', label: '同步时间', width: '150', sortable: 'custom' },
];
export default {
    name: "dingdingcallbackfailed",
    components: { cesTable, MyContainer, datepicker },
    props: {

    },
    data() {
        return {
            pageLoading: false,
            tableCols: tableCols,
            failedList:[],
            that: this,
            sels: [], // 列表选中列
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "createdTime", IsAsc: false },
            filter: {
                dates: [formatTime(dayjs(), "YYYY-MM-DD"), formatTime(dayjs(), "YYYY-MM-DD")],
                sdate: null,
                edate: null,
                title: null,
            },
        }
    },
    async mounted() {
        this.onSearch();
    },
    methods: {
        //排序查询      
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getNewOrderListAsync();
        },
        async getNewOrderListAsync() {
            if (this.filter.dates && this.filter.dates.length > 1) {
                this.filter.sdate = this.filter.dates[0];
                this.filter.edate = this.filter.dates[1];
            }
            else {
                this.filter.sdate = null;
                this.filter.edate = null;
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            this.pageLoading = true;
            let res = await callBackFailedPageList(params);
            this.pageLoading = false;
            if (res?.success) {
                this.total = res.data.total
                this.failedList = res.data.list;
                //this.summaryarry = res.summary;
            }
        },
    }
}
</script>