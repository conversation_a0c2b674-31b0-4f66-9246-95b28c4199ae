<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="ListInfo.syncTime" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"
                    class="publicCss" :clearable="false" />
                <el-select filterable v-model="ListInfo.company" clearable placeholder="请选择地区" class="publicCss">
                    <el-option key="义乌" label="义乌" value="义乌"></el-option>
                    <el-option key="南昌" label="南昌" value="南昌"></el-option>
                    <el-option key="武汉" label="武汉" value="武汉"></el-option>
                    <el-option key="深圳" label="深圳" value="深圳"></el-option>
                    <el-option key="其他" label="其他" value="其他"></el-option>
                </el-select>
                <!-- <el-input v-model.trim="ListInfo.title" maxlength="10" placeholder="请输入职位"  clearable class="publicCss"/> -->
                <el-select v-model="ListInfo.titles" multiple collapse-tags clearable filterable placeholder="请选择职位" class="publicCss">
                    <el-option v-for="item in purTitleList" :key="item" :label="item" :value="item"></el-option>
                </el-select>
                <el-select v-model="ListInfo.deptId" clearable filterable placeholder="请选择架构" class="publicCss">
                    <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.brandName" clearable filterable placeholder="请选择采购员" class="publicCss">
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.label" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%"
            :isNeedExpend="false" :showsummary="data.summary ? true : false" :summaryarry="data.summary"
            @sortchange="sortchange" @summaryClick='onsummaryClick' />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false"
            direction="btt">
            <div v-if="!chatProp.chatLoading">
                <div style="width:220px;">
                    <dateRange :startDate.sync="chatProp.startDate" :endDate.sync="chatProp.endDate"
                        @change="changeChart" :clearable="false" />
                </div>
                <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data" />
            </div>
            <div v-else v-loading="chatProp.chatLoading" />
        </el-drawer>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
import { getAllProBrand, getBianManPositionListV2 } from '@/api/inventory/warehouse'
import { getPurchaseDeptList } from '@/api/inventory/purchaseordernew'
const api = '/api/inventory/PurchaseSummary/Goods/'
import decimal from '@/utils/decimal'
import { pageBianMaBrand } from '@/api/inventory/warehouse'

export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan
    },
    data() {
        return {
            api,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false,
                summarys: [],
                syncTime: dayjs().format('YYYY-MM-DD')
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                startDate: null, // 趋势图开始时间
                endDate: null, // 趋势图结束时间
                chatLoading: true, // 趋势图loading
                brandId: null, //趋势图采购Id
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            positionList: [],
            brandlist: [],
            purchasegrouplist: [],
            purTitleList: []
        }
    },
    async mounted() {
        this.init()
        await this.getCol();
        await this.getList()
    },
    methods: {
        async init() {
            // var res2 = await getAllProBrand();
            // this.brandlist1 = res2.data;
            // this.brandlist = res2.data.filter(item => item.leaveDate == null).map(item => {
            //     return { value: item.key, label: item.value };
            // });
            let res2 = await pageBianMaBrand({ pageSize: 999999, currentPage: 1, OrderBy: "", IsAsc: true, LeaveDate: '在职', enabled: true });
            this.brandlist = res2.data.list.filter(item => item.purDept != null).map(item => { return { value: item.id, label: item.brandName }; });

            var resPosition = await getBianManPositionListV2();
            this.positionList = resPosition?.data;
            //采购组
            let { data: deptList, success } = await getPurchaseDeptList();
            if (success) {
                this.purchasegrouplist = deptList.map(item => { return { value: item.dept_id, label: item.full_name }; });
            }
            //职位
            const res = await request.post(`${this.api}GetBrandGoodsCountStatTitles`);
            if (res?.success){
                this.purTitleList = res?.data ?? [];
            }
        },
        proCodeCallback(val) {
            this.ListInfo.proCode = val
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                data.forEach(item => {
                    if (item.prop == 'goodsRate') {
                        item.formatter = (row) => row.goodsRate !== null ? decimal(row.goodsRate, 100, 2, '*') + '%' : ''
                    }
                    if (item.prop == 'styleRate') {
                        item.formatter = (row) => row.styleRate !== null ? decimal(row.styleRate, 100, 2, '*') + '%' : ''
                    }
                    if (item.prop == 'goodsCount') {
                        item.type = 'colorClick';
                        item.summaryEvent = true;
                        item.handle = (that, row) => that.openChart(row)
                    }
                    if (item.prop == 'styleCount') {
                        item.type = 'colorClick';
                        item.summaryEvent = true;
                        item.handle = (that, row) => that.openChart(row)
                    }
                })
                this.tableCols = data;
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        onsummaryClick() {
            this.chatProp.startDate = dayjs(this.ListInfo.syncTime).subtract(1, 'month').format('YYYY-MM-DD');
            this.chatProp.endDate = dayjs(this.ListInfo.syncTime).format('YYYY-MM-DD');
            this.chatProp.chatDialog = true
            this.chatProp.chatLoading = true
            this.changeChart()
        },
        async changeChart(e) {
            this.chatProp.chatLoading = true;
            this.chatProp.BrandId = null;
            var param = {
                ...this.chatProp,
                ...this.ListInfo
            };
            const { data, success } = await request.post(`${this.api}GetDataTrendChart`, param);
            if (!success) return
            this.chatProp.data = data;
            this.chatProp.chatLoading = false;
        },
        async openChart(row) {
            this.chatProp.startDate = dayjs(this.ListInfo.syncTime).subtract(1, 'month').format('YYYY-MM-DD');
            this.chatProp.endDate = dayjs(this.ListInfo.syncTime).format('YYYY-MM-DD');
            this.chatProp.BrandId = row.brandId;
            this.chatProp.chatDialog = true;
            this.chatProp.chatLoading = true;
            var param = {
                ...this.chatProp,
                ...this.ListInfo
            };
            const { data, success } = await request.post(`${this.api}GetDataTrendChart`, param);
            if (!success) return
            this.chatProp.data = data;
            this.chatProp.chatLoading = false;
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    for (let key in data.summary) {
                        if (key == 'goodsRate_sum' || key == 'styleRate_sum') {
                            data.summary[key] = data.summary[key] !== null ? decimal(data.summary[key], 100, 2, '*') + '%' : ''
                        }
                    }
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
