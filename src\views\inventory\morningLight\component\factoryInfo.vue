<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <inputYunhan :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode" placeholder="商品编码/若输入多条请按回车"
                    :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
                    @callback="goodsCodeCallback" title="商品编码" class="publicCss" style="margin: 0 5px 5px 0;">
                </inputYunhan>
                <inputYunhan :inputt.sync="ListInfo.artNo" v-model="ListInfo.artNo" placeholder="货号/若输入多条请按回车"
                    :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000" @callback="artNoCallback"
                    title="货号" class="publicCss" style="margin: 0 5px 5px 0;">
                </inputYunhan>
                <el-select v-model="ListInfo.enabled" placeholder="状态" class="publicCss" clearable collapse-tags>
                    <el-option label="禁用" :value="false" />
                    <el-option label="启用" :value="true" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                    <el-button type="primary" @click="importProps"
                        v-if="checkPermission('Api:ImportInventory:Purchase:ImportPurchaseExtInfo')">导入</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="150">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <el-button type="text" @click="editProps(row.id)">编辑</el-button>
                            <el-button type="text" @click="isAble(row.id, row.enabled)">{{ row.enabled === true ? '禁用' :
                                '启用' }}</el-button>
                            <el-button type="text" @click="deleteProps(row.id)">删除</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <!-- 导入数据 -->
        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                    <el-button size="small" type="primary">点击上传</el-button>
                </el-tooltip>
            </el-upload>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

        <!-- 编辑 -->
        <el-dialog title="编辑" :visible.sync="editVisable" width="20%" v-dialogDrag v-loading="importLoading">
            <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="100px"
                class="demo-ruleForm" v-if="editVisable">
                <el-form-item label="商品编码" prop="goodsCode">
                    <el-input v-model="ruleForm.goodsCode" placeholder="商品编码" clearable style="width: 180px;"
                        maxlength="50" disabled />
                </el-form-item>
                <el-form-item label="状态" prop="enabled">
                    <el-switch style="display: block" v-model="ruleForm.enabled" active-color="#13ce66"
                        inactive-color="#ff4949" active-text="启用" inactive-text="禁用">
                    </el-switch>
                </el-form-item>
                <el-form-item label="货号" prop="artNo">
                    <el-input v-model="ruleForm.artNo" placeholder="请输入货号" clearable style="width: 180px;" />
                </el-form-item>
                <el-form-item label="厂家箱规" prop="boxSpecs">
                    <el-input v-model="ruleForm.boxSpecs" placeholder="请输入厂家箱规" style="width: 180px;" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
                    <el-button @click="editVisable = false">关闭</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import inputYunhan from "@/components/Comm/inputYunhan";
import dayjs from 'dayjs'
import {
    getPurchaseExtInfoPage,
    getPurchaseExtInfoById,
    editPurchaseExtInfo,
    setPurchaseExtInfoEnabled,
    delPurchaseExtInfo,
    exportPurchaseExtInfo,
} from '@/api/inventory/purchase'
import { importPurchaseExtInfo } from '@/api/inventory/purchaseImport'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'enabled', label: '状态', formatter: (row) => row.enabled === true ? '启用' : '禁用' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'artNo', label: '货号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'boxSpecs', label: '厂家箱规', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, inputYunhan
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,//导出加载
            importLoading: false,//导入加载
            fileList: [],//上传文件列表
            importVisible: false,//导入弹窗
            ruleForm: {},//编辑表单
            editVisable: false,//编辑弹窗
            rules: {//编辑表单验证
                artNo: [
                    { required: true, message: '请输入货号', trigger: 'blur' }
                ],
                boxSpecs: [
                    { required: true, message: '请输入厂家箱规', trigger: 'blur' }
                ],
                enabled: [
                    { required: true, message: '请选择状态', trigger: 'blur' }
                ],
            },
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        artNoCallback(val) {
            this.ListInfo.artNo = val;
        },
        goodsCodeCallback(val) {
            this.ListInfo.goodsCode = val;
        },
        submitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    if (this.ruleForm.boxSpecs <= 0) return this.$message.error('厂家箱规必须大于0')
                    const { success } = await editPurchaseExtInfo(this.ruleForm)
                    if (success) {
                        this.editVisable = false
                        this.getList()
                        this.$message.success('保存成功!')
                    } else {
                        this.$message.error('保存失败!')
                    }
                } else {
                    this.$message.error('表单验证失败!');
                    return false;
                }
            });
        },
        async editProps(id) {
            const { data, success } = await getPurchaseExtInfoById({ id })
            if (success) {
                this.ruleForm = data
                this.editVisable = true
            } else {
                this.$message.error('获取数据失败')
            }
        },
        async isAble(id, enabled) {
            this.$confirm('此操作将改变数据状态, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await setPurchaseExtInfoEnabled({ id, enabled: !enabled })
                if (success) {
                    this.getList()
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        async deleteProps(id) {
            this.$confirm('此操作将删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await delPurchaseExtInfo({ id })
                if (success) {
                    this.getList()
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在上传中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            this.importLoading = true
            await importPurchaseExtInfo(form).then(({ success }) => {
                if (success) {
                    this.$message.success('上传成功，正在排队导入中...')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            await exportPurchaseExtInfo(this.ListInfo).then(({ data }) => {
                if (data) {
                    this.isExport = true
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '厂家信息' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getPurchaseExtInfoPage(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
</style>
