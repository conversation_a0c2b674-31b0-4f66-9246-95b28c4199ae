<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right" label-width="120px">
                <el-form-item label="修改时间:">
                    <el-date-picker style="width:230px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item label="平台:">
                    <el-select v-model="filter.platform" placeholder="请选择" :clearable="true" :collapse-tags="true" filterable @change="onchangeplatform">
                        <el-option v-for="item in platformList" :key="item.value" :label="item.label" @change="onSearch" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="店铺:">
                    <el-select v-model="filter.shopId" placeholder="请选择" @change="onSearch" :clearable="true" :collapse-tags="true" filterable>
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
                    </el-select>
                </el-form-item>
                <el-form-item label="款式编码:">
                    <el-input v-model="filter.styleCode" placeholder="款式编码" @change="onSearch" />
                </el-form-item>
                <br />
                <el-form-item label="店铺款式编码:">
                    <el-input v-model="filter.shopStyleCode" placeholder="店铺款式编码" style="width:230px;" @change="onSearch" />
                </el-form-item>
                <el-form-item label="商品编码:">
                    <el-input v-model="filter.goodsCode" placeholder="商品编码" style="width:193px;" @change="onSearch" />
                </el-form-item>
                <el-form-item label="是否在售:">
                    <el-select v-model="filter.isOnSale" @change="onSearch" placeholder="请选择" :clearable="true" :collapse-tags="true" filterable>
                        <el-option v-for="item in yesnoList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :isSelectColumn="!isHistory" :loading="listLoading">
        </ces-table>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag>
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="4" action accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}} </el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>

<script>
    import dayjs from "dayjs";
    import { formatTime } from "@/utils";
    import { getList as getshopList } from '@/api/operatemanage/base/shop'
    import MyContainer from '@/components/my-container'
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import { formatPlatform, formatLinkProCode } from "@/utils/tools";
    import { rulePlatform } from "@/utils/formruletools";
    import {
        //分页查询店铺商品资料
        pageShopGoods,
        //分页查询店铺商品资料 历史
        pageShopGoodsHistory,
        //导入
        importShopGoods,
    } from "@/api/operatemanage/base/basicgoods"
    const tableCols = [
        { istrue: true, prop: 'platform', label: '平台', width: '60', sortable: 'custom', formatter: (row) => formatPlatform(row.platform) },
        { istrue: true, prop: 'shopName', label: '店铺', width: '150' },
        { istrue: true, prop: 'channel', label: '来源平台', width: '70', sortable: 'custom', },
        { istrue: true, prop: 'styleCode', label: '款式编码', width: '100', sortable: 'custom', },
        { istrue: true, prop: 'goodsCode', label: '商品编码', width: '100', sortable: 'custom', },
        { istrue: true, prop: 'giftlinkID', label: '赠品链接主ID', width: '110', sortable: 'custom', },
        { istrue: true, prop: 'categoryId', label: '聚水潭类目ID', width: '105', sortable: 'custom', },
        { istrue: true, prop: 'goodsName', label: '商品名称', width: '120', sortable: 'custom', },
        { istrue: true, prop: 'onlineGoodsName', label: '线上商品名称', width: '110', sortable: 'custom', },
        { istrue: true, prop: 'goodsImage', label: '图片', width: '60', type: 'image', },
        { istrue: true, prop: 'shopStyleCode', label: '店铺款式编码', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.shopStyleCode) },
        { istrue: true, prop: 'shopStyleName', label: '店铺款式名称', width: '110', sortable: 'custom', },
        { istrue: true, prop: 'shopGoodsCode', label: '店铺商品编码', width: '120', sortable: 'custom', },
        { istrue: true, prop: 'modified', label: '修改时间', width: '140', sortable: 'custom', formatter: (row) => formatTime(row.modified, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'isOnSale', label: '是否在售', width: '80', type: 'switch', isDisabled: (row, that) => true, sortable: 'custom', },
        { istrue: true, prop: 'versions', label: '版本号', width: '70', sortable: 'custom', }
    ];
    const tableHandles1 = [
        { label: "导入", handle: (that) => that.startImport() },
    ];

    const startDate = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
    const endDate = formatTime(new Date(), "YYYY-MM-DD");

    export default {
        name: 'Roles',
        components: { cesTable, MyContainer, MyConfirmButton },
        props: {
            isHistory: false,
        },
        data () {
            return {
                that: this,
                filter: {
                    modifiedStart: null,
                    modifiedEnd: null,
                    platform: null,
                    shopId: "",
                    styleCode: null,
                    shopStyleCode: null,
                    goodsCode: null,
                    isOnSale: null,
                    timerange: [startDate, endDate]
                },
                list: [],
                summaryarry: {},
                pager: { OrderBy: "modified", IsAsc: false },
                tableCols: tableCols,
                tableHandles: tableHandles1,
                platformList: [],
                shopList: [],
                dialogVisible: false,
                total: 0,
                sels: [],
                listLoading: false,
                pageLoading: false,
                addFormVisible: false,
                addLoading: false,
                deleteLoading: false,
                uploadLoading: false,
                formtitle: "新增",
                fileList: [],
                pickerOptions: {
                    disabledDate (time) {
                        return time.getTime() > Date.now();
                    }
                },
                yesnoList: [
                    { value: true, label: "是" },
                    { value: false, label: "否" }
                ],
            }
        },
        async mounted () {
            await this.setPlatform();
            await this.getlist();
        },
        methods: {
            //设置平台下拉
            async setPlatform () {
                var pfrule = await rulePlatform();
                this.platformList = pfrule.options;
                if (this.platformList && this.platformList.length)
                    this.platformList.push({ label: '未知', value: 0 });
            },
            //设置店铺下拉
            async onchangeplatform (val) {
                const res = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
                this.shopList = res.data.list || [];
                this.shopList.push({ shopCode: "{线下}", shopName: "{线下}" });
                this.filter.shopCode = "";
                await this.onSearch();
            },
            //导出
            async onExport () {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                var res;
                if (this.isHistory) {
                    res = await pageShopGoodsHistory(params);
                }
                else {
                    res = await pageShopGoods(params);
                }
                loadingInstance.close();
                if (!res?.data) return
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '店铺商品资料_' + new Date().toLocaleString() + '.xlsx')
                aLink.click();
            },
            //获取查询条件
            getCondition () {
                this.filter.modifiedStart = null;
                this.filter.modifiedEnd = null;
                if (this.filter.timerange && this.filter.timerange.length > 1) {
                    this.filter.modifiedStart = this.filter.timerange[0];
                    this.filter.modifiedEnd = this.filter.timerange[1];
                }
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }

                return params;
            },
            //查询第一页
            async onSearch () {
                this.$refs.pager.setPage(1)
                await this.getlist()
            },
            //分页查询
            async getlist () {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }

                this.listLoading = true
                var res;
                if (this.isHistory) {
                    res = await pageShopGoodsHistory(params);
                }
                else {
                    res = await pageShopGoods(params);
                }
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summary;
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
            },
            //排序查询
            async sortchange (column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderBy = column.prop;
                    this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selsChange: function (sels) {
                this.sels = sels
            },
            startImport () {
                this.dialogVisible = true;
            },
            cancelImport () {
                this.dialogVisible = false;
            },
            beforeRemove () {
                return false;
            },
            //上传成功
            uploadSuccess (response, file, fileList) {
                if (response.code == 200) {
                } else {
                    fileList.splice(fileList.indexOf(file), 1);
                }
            },
            submitUpload () {
                console.log('打印上传文件', this.fileList)
                if (!this.fileList || this.fileList.length == 0) {
                    this.$message({ message: "请先选取文件", type: "warning" });
                    return false;
                }
                //this.uploadLoading=true;
                this.$refs.upload.submit();
            },
            uploadFile (item) {
                const form = new FormData();
                form.append("token", this.token);
                form.append("upfile", item.file);
                const res = importShopGoods(form);
                this.$message({
                    message: '上传成功,正在导入中...',
                    type: "success",
                });
                this.uploadLoading = false;
            },
            async uploadChange (file, fileList) {
                if (fileList && fileList.length > 0) {
                    var list = [];
                    for (var i = 0; i < fileList.length; i++) {
                        if (fileList[i].status == "success")
                            list.push(fileList[i]);
                        else
                            list.push(fileList[i].raw);
                    }
                    this.fileList = list;
                }
            },
            uploadRemove (file, fileList) {
                this.uploadChange(file, fileList);
            },
        }
    }
</script>
<style scoped>
    ::v-deep .el-link.el-link--primary {
        margin-right: 7px;
    }
</style>
