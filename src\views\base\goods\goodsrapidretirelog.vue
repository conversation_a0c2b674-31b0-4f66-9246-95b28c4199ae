<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-date-picker style="width: 220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :clearable="false" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-tooltip class="item" effect="dark" content="默认为精确匹配在开始或者结尾输入*进行模糊匹配" placement="top">
                        <inputYunhan :key="'3'" :keys="'three'" :width="'180px'" ref="childGoodsCode"
                            v-model="filter.goodsCode" :inputt.sync="filter.goodsCode" placeholder="商品编码" :clearable="true"
                            @callback="callbackGoodsCode" title="商品编码"></inputYunhan>
                    </el-tooltip>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select style="width:130px;" v-model="filter.groupId" placeholder="原运营组" :clearable="true"
                        :collapse-tags="true">
                        <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key">
                        </el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select style="width:130px;" v-model="filter.newGroupId" placeholder="新运营组" :clearable="true"
                        :collapse-tags="true">
                        <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key">
                        </el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select filterable v-model="filter.newOperationId" placeholder="新运营专员" clearable
                        style="width: 100px">
                        <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-select v-model="filter.operatingResult" clearable filterable placeholder="请选择状态"
                        style="width: 140px">
                        <el-option label="是" value="true" />
                        <el-option label="否" value="false" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-input v-model="filter.goodsName" placeholder="商品名称" maxlength="50"  />
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-input v-model="filter.firstUserName" placeholder="开款人" maxlength="20" />
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button>
            </el-button-group>
        </template>
        <!--列表-->
        <vxetablebase :id="'goodsrapidretirelog20230701'" :tableData='list' :tableCols='tableCols' @cellClick='cellclick'
            @select='selectchange' :tableHandles='tableHandles' :loading='listLoading' :border='true' :that="that"
            ref="vxetable" @sortchange='sortchange'>
            <template slot="right">
                <vxe-column title="操作" :field="'col_opratorcol'" width="120" fixed="right">
                    <template #default="{ row }">
                        <template>
                            <el-button type="text" @click="onSetInfo(row)">查看</el-button>
                        </template>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <!--  -->
        <vxe-modal title="详情" v-model="dialogAddVisible" :esc-closable="true" :width='1200' :height='800' marginSize='-500'>
            <template  #default>
            <goodsrapidretirelogdetail ref="goodsrapidretirelogdetail" style="height: 600px;" :filter="jstfilter">
            </goodsrapidretirelogdetail>
            </template>
        </vxe-modal>
    </container>
</template>

<script>
import { Loading } from 'element-ui';
import dayjs from "dayjs";
import { formatTime, formatNoLink } from "@/utils/tools";
import inputYunhan from "@/components/Comm/inputYunhan";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { getGroupKeyValue } from '@/api/operatemanage/base/product';
import { getDirectorList } from '@/api/operatemanage/base/shop'
import { getGoodsRapidRetireLogAsync, getGoodsRapidRetireLogDetailAsync } from "@/api/inventory/goodsrapidretire"
import goodsrapidretirelogdetail from './goodsrapidretirelogdetail.vue';

const tableCols = [
    { istrue: true, prop: 'goodsCode', align: 'center', label: '商品编码', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'goodsName', align: 'center', label: '商品名称', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'warehouseName', label: '仓库', width: '200', },
    { istrue: true, prop: 'firstUserName', align: 'center', label: '开款人', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'proCount', align: 'center', label: '对应ID数', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'sellStock', label: '库存数', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'groupId', label: '运营组', width: '120', sortable: 'custom', formatter: (row) => row.groupName },
    { istrue: true, prop: 'goodsCodeCreatedTime', label: '添加时间', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'operatingResult', label: '是否卖完下架', width: '120', sortable: 'custom', formatter: (row) => row.operatingResult == true ? "是" : "否" },
    { istrue: true, prop: 'newGroupId', label: '新运营组', width: '120', sortable: 'custom', formatter: (row) => row.newGroupName },
    { istrue: true, prop: 'newOperationId', label: '新运营专员', width: '120', sortable: 'custom', formatter: (row) => row.newOperationName },
    { istrue: true, prop: 'modifiedTime', label: '修改时间', width: 'auto', sortable: 'custom', },
];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];


const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunHanAdminGoodsrapidretire',
    components: { MyConfirmButton, container, vxetablebase, inputYunhan, goodsrapidretirelogdetail },

    data() {
        return {
            that: this,
            filter: {
                beginDate: null,
                endDate: null,
                goodsCode: null,
                operatingResult: null,
                newOperationId: null,
                newGroupId: null,
                groupId: null,
                timerange: [startTime, endTime],
                goodsName: null,
                firstUserName: null,
            },
            jstfilter: {
                id: null
            },
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "GoodsCode", IsAsc: false },
            total: 0,
            sels: [],
            selids: [],
            groupList: [],
            directorList: [],
            pickerOptions: {
                shortcuts: [{
                    text: '近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近半个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            listLoading: false,
            pageLoading: false,
            dialogAddVisible: false,
            onFinishLoading: false,
            dialogLoading: false
        };
    },

    async mounted() {
        await this.init();
        await this.onSearch();
    },

    methods: {
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        async init() {
            const res = await getGroupKeyValue({});
            this.groupList = res.data;
            const res1 = await getDirectorList({});
            this.directorList = [{ key: '0', value: '未知' }].concat(res1.data || []);
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.beginDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.beginDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await getGoodsRapidRetireLogAsync(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
        },
        async callbackGoodsCode(val) {
            this.filter.goodsCode = val;
            //this.onSearch();
        },
        async onSetInfo(row) {
            this.jstfilter.id = row.id;
            this.dialogAddVisible = true;
            this.$nextTick(async () => {
                await this.$refs.goodsrapidretirelogdetail.onSearch();
            })
        },
        async cellclick({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>

<style lang="scss" scoped></style>
