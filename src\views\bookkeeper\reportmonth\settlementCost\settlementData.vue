<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <div class="top">
        <el-date-picker class="publicCss" v-model="filter.yearMonth" type="month" format="yyyyMM" value-format="yyyyMM"
          placeholder="选择月份">
        </el-date-picker>
        <!-- <el-select filterable v-model="filter.platform" placeholder="请选择平台" clearable  class="publicCss">
            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select> -->
        <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺" class="publicCss">
          <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
            :value="item.shopCode"></el-option>
        </el-select>
        <el-input v-model="filter.mainOrderNumber" placeholder="主订单号" class="publicCss" />
        <el-input v-model="filter.childOrderNumber" placeholder="子订单号" class="publicCss" />
        <el-input v-model="filter.proCode" placeholder="宝贝ID" class="publicCss" />
        <el-select filterable clearable v-model="filter.isNullProCode" placeholder="请选择空白ID" class="publicCss">
          <el-option label="空白ID" :value="true"></el-option>
          <el-option label="非空白ID" :value="false"></el-option>
        </el-select>
        <el-select filterable clearable v-model="filter.RecoganizeType" placeholder="请选择动账方向" class="publicCss">
          <el-option label="入账" :value="0"></el-option>
          <el-option label="出账" :value="1"></el-option>
        </el-select>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onExport">汇总导出</el-button>
      </div>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :id="'settlementData202302031421'" :tablekey="'settlementData202302031421'" :tableData='ZTCKeyWordList'
      :showsummary='true' :summaryarry='summaryarry' @select='selectchange' :isSelection='false' :tableCols='tableCols'
      :loading="listLoading" :isSelectColumn='false'>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>
  </my-container>
</template>
<script>
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList, getList as getshopList } from '@/api/operatemanage/base/shop';
import { formatPlatform, formatLink, platformlist } from "@/utils/tools";
import { getFinancialDetailKs, ExportFinancialDetailKs } from '@/api/monthbookkeeper/financialDetail'

const tableCols = [
  { istrue: true, prop: 'yearMonth', label: '年月', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'merchantId', label: '商家ID', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'orderNumber', label: '订单号', sortable: 'custom', width: '160' },
  { istrue: true, prop: 'proCode', label: '商品ID', sortable: 'custom', width: '160' },
  { istrue: true, prop: 'proName', label: '商品名称', sortable: 'custom', width: '160' },
  { istrue: true, prop: 'qty', label: '数量', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'payAmount', label: '订单实付(元)', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'paySaleSubsidyAmount', label: '支付营销补贴', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'platformSubsidyAmount', label: '平台补贴(元)', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'talentSubsidyAmount', label: '主播补贴(元)', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'totalIncomeAmount', label: '合计收入(元)', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'orderRefundAmount', label: '订单退款(元)', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'paySaleBackAmount', label: '支付营销回退（元）', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'technicalServiceAmount', label: '技术服务费(元)', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'talentCommissionsAmount', label: '达人佣金(元)', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'groupCommissionsAmount', label: '团长佣金(元)', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'commissionsMode', label: '佣金模式', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'kzkCommissionsAmount', label: '快赚客佣金(元)', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'fwsCommissionsAmount', label: '服务商佣金(元)', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'otherChargesAmount', label: '其他收费', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'totalOutcomeAmount', label: '合计支出(元)', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'actualSettlementAmount', label: '实际结算金额(元)', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'actualSettlementTime', label: '实际结算时间', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'settlementRule', label: '结算规则', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'settlementMerchantNo', label: '结算商户号', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'remark', label: '备注', sortable: 'custom', width: '80' },
];
export default {
  name: "settlementData",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
  data() {
    return {
      that: this,
      filter: {
        platform: 14,
        yearMonth: null,
        shopCode: null,
        proCode: null,
        isNullProCode: null,
        RecoganizeType: null
      },
      shopList: [],
      userList: [],
      groupList: [],
      platformlist: platformlist,
      ZTCKeyWordList: [],
      tableCols: tableCols,
      summaryarry: {},
      total: 0,
      pager: { OrderBy: "id", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
    };
  },
  async mounted() {
    this.onchangeplatform();
  },
  methods: {
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    async onchangeplatform() {
      const res1 = await getshopList({ platform: 14, CurrentPage: 1, PageSize: 100000 });
      this.shopList = res1.data.list
    },
    // async getShopList(){
    //   const res1 = await getAllShopList();
    //   this.shopList=[];
    //     res1.data?.forEach(f => {
    //       if(f.isCalcSettlement&&f.shopCode)
    //           this.shopList.push(f);
    //     });
    // },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      let params = this.getCondition();
      this.listLoading = true;
      const res = await getFinancialDetailKs(params);
      this.listLoading = false;
      this.total = res.data?.total
      this.ZTCKeyWordList = res.data?.list;
      this.summaryarry = res.data?.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    getCondition() {
      let pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...this.filter,
      };
      return params;
    },
    async onExport(opt) {
      if (!this.filter.yearMonth) {
        this.$message({ message: "请先选择月份", type: "warning" });
        return;
      }
      let pars = this.getCondition();
      if (pars === false) {
        return;
      }
      const params = { ...pars, ...opt };
      let res = await ExportFinancialDetailKs(params);
      if (!res?.data) {
        return
      }
      this.$message({ message: "正在后台导出中，请点击头像-在下载管理中查看", type: "success" });
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 140px;
    margin-right: 5px;
  }
}
</style>
