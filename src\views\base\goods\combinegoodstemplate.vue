<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-button-group>
        <!-- <el-button style="padding: 0;">
          <el-select v-model="filter.SeparateWarehouse" placeholder="分仓" >
            <el-option v-for="item in Warehouseslist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-button>  -->
        <el-button style="padding: 0;">
          <el-input style="width: 110px" v-model.trim="filter.combineCode" maxlength="20" clearable
            placeholder="组合商品编码" />
          <el-input style="width: 110px" v-model.trim="filter.combineName" maxlength="20" clearable
            placeholder="组合编码名称" />
          <el-input style="width: 100px" v-model.trim="filter.brand" maxlength="20" clearable placeholder="品牌" />
          <el-input style="width: 100px" v-model.trim="filter.createUserName" maxlength="20" clearable
            placeholder="创建人" />
        </el-button>
        <el-button style="padding: 0;">
          <el-select style="width: 120px" v-model="filter.isERP" placeholder="是否ERP创建" :clearable="true">
            <el-option v-for="item in isERP" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-button>
        <el-button style="padding: 0;">
          <el-select style="width: 80px" v-model="filter.status" placeholder="状态" clearable>
            <el-option label="启用" value="1"></el-option>
            <el-option label="备用" value="0"></el-option>
            <el-option label="禁用" value="-1"></el-option>
            <el-option label="删除" value="2"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;">
          <el-select style="width: 90px" v-model="filter.labels2YN" placeholder="标签共性" clearable>
            <el-option label="是" :value='true'></el-option>
            <el-option label="否" :value="false"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;">
          <el-select style="width: 90px" v-model="filter.wmsName2YN" placeholder="仓库共性" clearable>
            <el-option label="是" :value='true'></el-option>
            <el-option label="否" :value="false"></el-option>
          </el-select>
        </el-button>
        <el-button style="padding: 0;">
          <el-date-picker style="width: 250px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="创建开始日期" end-placeholder="创建结束日期">
          </el-date-picker>
        </el-button>
        <el-button style="padding: 0;margin: 0;">
                    <el-select v-model="filter.wmsIds" multiple clearable filterable :collapse-tags="true" placeholder="请选择仓库" style="width: 300px">
                        <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                            :value="item.wms_co_id" />
                    </el-select>
                </el-button>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onTagMaintenance" v-if="checkPermission('LabelMaintenanceAuthoritys')">标签维护</el-button>
      </el-button-group>
    </template>
    <el-container style="height: 100%; border: 1px solid #eee">
      <el-aside width="80%" style="background-color: rgb(238, 241, 246)">
        <noheaderContainer>
          <ces-table ref="table" :that='that' :isIndex='false' :hasexpand='true' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="true" @select="selectchange"
            :tableHandles='tableHandles' @cellclick="cellclick" :loading="listLoading">
          </ces-table>
          <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
          </template>
        </noheaderContainer>
      </el-aside>
      <noheaderContainer>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchangeDetail'
          :height='"90%"' style="width:700px;" :tableData='detail.list' :tableCols='detail.tableCols' :isSelection="false"
          :isSelectColumn="false" :loading="detail.listLoading">
        </ces-table>
        <template #footer>
          <my-pagination ref="pagerDetail" :total="detail.total" :checked-count="detail.sels.length"
            @get-page="getDetailList" />
        </template>
      </noheaderContainer>
    </el-container>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag>
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :http-request="uploadFile" :file-list="fileList">
          <template #trigger>
            <el-button size="small" type="primary">选取数据文件</el-button>
          </template>
          <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="导入组合编码" :visible.sync="dialogCombineCodeVisible" width="30%" v-dialogDrag>
      <span>
        <el-upload ref="upload1" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :http-request="uploadFile1" :file-list="fileList1" :on-change="onsuccess">
          <template #trigger>
            <el-select v-model="Uplodfilter.SeparateWarehouse" placeholder="分仓" filterable clearable>
              <el-option v-for="item in Warehouseslist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-button style="margin-left: 10px;" size="small" type="primary">选取数据文件</el-button>
          </template>
          <el-button style="margin-left: 10px;" size="small" type="success" :loading="uploadLoading" @click="submitUpload1">{{(uploadLoading?'上传中':'上传' )}}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogCombineCodeVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="组合编码 新增/编辑" :visible.sync="CombineCode.visible" width="80%" v-dialogDrag
      @close="closedialogCombineCode">
      <div>
        <el-button-group>
          <el-button style="color: white; background-color:red;" @click="importCombineCode">
            <span>批量导入</span>
          </el-button>
        </el-button-group>
        <vxe-table border show-overflow ref="table" :loading="listLoading" :data="combincodelist" height="300px"
          :row-config="{ height: 45 }" :column-config="{ resizable: true }" header-align="center">
          <vxe-column type="seq" width="60" align="center"></vxe-column>
          <vxe-column field="warehouse" title="仓库" width="260" align="center"></vxe-column>
          <vxe-column field="combineCode" title="组合编码" width="130" align="center">
            <template #default="{ row }">
              <template>
                <span>{{ row.combineCode }}</span>
              </template>
            </template>
          </vxe-column>
          <vxe-column field="combineName" title="组合编码名称" width="150" align="center"></vxe-column>
          <vxe-column field="goodsCode" title="子编码" width="150" align="center"></vxe-column>
          <vxe-column field="goodsName" title="子编码名称" width="100" align="center"></vxe-column>
          <vxe-column field="childeqty" title="子编码库存数" width="120" align="center"> </vxe-column>
          <!-- <vxe-column field="childeOrder_lock" title="子编码订单占有数" width="200" align="center">
        </vxe-column> -->
          <vxe-column field="qty" title="组合数量" width="100" align="center"></vxe-column>
          <vxe-column field="status" title="子编码状态" width="150" align="center">
            <template #default="{ row }">
              <span>{{ row.status == 0 ? "聚水潭中未存在" : "聚水潭中存在" }}</span>
            </template>
          </vxe-column>
          <vxe-column width="240" title="操作" align="center">
            <template #default="{ row }">
              <vxe-button style="color: blue;" type="text" @click="DeleteImportCombineCode(row)">删除</vxe-button>
            </template>
          </vxe-column>
        </vxe-table>
        <el-button @click="onCancelSubmit" style="margin-left: 1300px; margin-top: 10px;">取消</el-button>
        <my-confirm-button type="submit" style="margin-left: 50px;margin-top: 10px;" :loading="editLoading"
          @click="onAddCombineCodeSubmit" />
      </div>
    </el-dialog>
    <el-dialog title="申请调拨" :visible.sync="applyTransferDetail" width="82%" v-dialogDrag
      @close="closedialogapplyTransferDetail">
      <form-create :rule="autoform1.rule1" v-model="autoform1.fApi" :option="autoform1.options" />
      <el-button style="margin-left: 1300px;" @click.native="applyTransferDetail = false">取消</el-button>
      <my-confirm-button type="submit" :loading="editLoading" @click="onEditSubmitDetail" />
    </el-dialog>
    <el-dialog title="日志" :visible.sync="ShowlogDetail.visible" width="80%" v-dialogDrag>
      <div>
        <ShowlogDetail ref="ShowlogDetail" :filter="ShowlogDetail.filter" style="height:600px;"></ShowlogDetail>
      </div>
    </el-dialog>

    <el-dialog title="编辑组合编码" :visible.sync="dialogFormVisible" :close-on-click-modal="false"  v-dialogDrag >
      <el-form :model="form">
        <el-form-item label="仓库:" :label-width="formLabelWidth">
          <!-- @change="warehousenamech(subfilter.warehousename)" -->
          <el-select v-if="selshow" v-model="subfilter.warehouseid" placeholder="请选择仓库"  >
            <el-option v-for="item in Warehouseslist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>

        </el-form-item>
        <div style="display: flex; flex-direction: row;">
          <el-form-item label="组合编码:" :label-width="formLabelWidth">
            <el-input placeholder="组合编码" v-model="subfilter.combineCode" style="width: 100%;" :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="组合名称:" :label-width="formLabelWidth"  >
            <el-input placeholder="组合名称" v-model="subfilter.combineCodeName" style="width:
            170%;" autocomplete="off"></el-input>
          </el-form-item>
          <div style="margin: 0 0 0 auto;"><el-button @click="onAdd">新增一行</el-button></div>
        </div>
        <div style="height: 240px;">
          <el-table :data="detail.editList" height="240" border style="width: 100%;">
            <el-table-column prop="goodsCode" label="子编码" v-model="subfilter.goodsCode" width="300"></el-table-column>
            <el-table-column prop="qty" label="数量" v-model="subfilter.qty" width="300"></el-table-column>
            <el-table-column label="操作" width="auto">
              <div  slot-scope="scope" >
                <el-button @click.native.prevent="addRow(scope.$index, scope.row)" type="text"
                  size="small">编辑</el-button>
                <el-button @click.native.prevent="deleteRow(scope.$index)" type="text"
                  size="small">删除</el-button>
              </div>
            </el-table-column>
          </el-table>
        </div>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitend">提 交</el-button>
      </div>
    </el-dialog>

    <el-dialog title="编辑" :visible.sync="editdialogVisible" width="30%">
    <div>
      <el-input v-model="editrow.goodsCode" placeholder="请输入编码"></el-input>
      <el-input v-model="editrow.qty" type='number'  placeholder="请输入数量"></el-input>
      <!-- <div style="float: right;"><el-button type="primary">保存</el-button></div> -->
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="editdialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="endedit">确 定</el-button>
    </span>
  </el-dialog>

  <el-dialog title="维护自定义标签" :visible.sync="labelMaintenanceVisible" width="55%" v-dialogDrag>
      <el-scrollbar style="min-height: 200px;height:500px;max-height:500px" v-loading="customTagLoading">
        <div style="display: flex; flex-wrap: wrap;">
          <div v-for="(item, index) in customTag" :key="index" class="tagLabelStyle" @mouseover="showCloseIcon(index)" @mouseleave="hideCloseIcon(index)" style="position: relative;">
            <el-tooltip class="item" effect="dark" :content="item.key" placement="top">
              <span style="margin-left: 10px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; max-width: 150px; display: inline-block;">{{ item.key }}</span>
            </el-tooltip>
            <i v-show="showClose[index]" class="el-icon-close" style="position: absolute; top: 0; right: 0; color: red; cursor: pointer;" @click="tagDeleteClick(item)"></i>
          </div>
        </div>
      </el-scrollbar>
      <div slot="footer" class="dialog-footer">
        <el-input style="width: 20%; float: left;" @keyup.enter.native="tagHandleEnter" v-model.trim="customTagValue" maxlength="20" clearable placeholder="请输入内容,回车添加" />
        <el-button @click="labelMaintenanceVisible = false">关闭</el-button>
      </div>
    </el-dialog>

  </my-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import noheaderContainer from '@/components/my-container/noheader'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { getConfigByParentTitle, addChildrenConfig, delChildrenConfig } from '@/api/admin/publicConfig';
import {
  //分页查询组合装商品资料
  pageCombineGoods,
  exportCombineGoods,
  //分页查询组合装商品资料 明细
  pageCombineGoodsDetail,
  //分页查询组合装商品资料 历史
  pageCombineGoodsHistory,
  //分页查询组合装商品资料 明细 历史
  pageCombineGoodsDetailHistory,
  //导入
  importCombineGoods, importCombineGoodsV2
} from "@/api/operatemanage/base/basicgoods"
import { formatTime } from '@/utils';
import { getWarehouses } from "@/api/storehouse/storehouse";
import { inventoryQuery, creatAllocate } from "@/api/inventory/allocate";
import ShowlogDetail from '@/views/base/goods/ShowlogDetail'
import { addCombineGoods, SyncToJst, editCombineGoodsAsync } from "@/api/operatemanage/base/basicgoods"
import {    getAllWarehouse } from '@/api/inventory/warehouse'
import { formatKeyWord } from "@/utils/tools";
const tableCols = [
  { istrue: true, prop: 'combineCode', label: '组合编码', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'combineName', label: '组合名称', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'brand', label: '采购员', width: '70', sortable: 'custom' },
  { istrue: true, prop: 'skuQty', label: '编码数量', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'status', label: '状态', width: '60', sortable: 'custom', formatter: (row) => row.status == 1 ? "启用" : row.status == 0 ? "备用" : row.status == -1 ? "禁用" : "" },
  { istrue: true, prop: 'labels', label: '组合编码标签', width: '105', sortable: 'custom' },
  { istrue: true, prop: 'labels2', label: '共性标签', width: '100', sortable: 'custom', type: 'html', formatter: (row) => formatKeyWord(row.labels2, row.labels2YN ? 0 : 2), tipmesg: '红色代表子编码无共性标签' },
  { istrue: true, prop: 'wmsName2', label: '共性仓库', width: '100', sortable: 'custom', type: 'html', formatter: (row) => formatKeyWord(row.wmsName2, row.wmsName2YN ? 0 : 2), tipmesg: '红色代表子编码无共性仓库' },
  //{istrue:true,prop:'wmsBin2',label:'共性库位', width:'120',sortable:'custom',type:'html',formatter:(row)=>formatKeyWord(row.wmsBin2,row._type),tipmesg: '红色代表子编码在同一个仓'},
  { istrue: true, prop: 'salesQty20', label: '20天销量', width: '70', sortable: 'custom' },
  { istrue: true, prop: 'orderCount20', label: '20天订单量', width: '70', sortable: 'custom' },
  { istrue: true, prop: 'orderCount20Avg', label: '20天日均订单量', width: '75', sortable: 'custom' },
  { istrue: true, prop: 'salesQty5', label: '5天销量', width: '68', sortable: 'custom' },
  { istrue: true, prop: 'orderCount5', label: '5天订单量', width: '70', sortable: 'custom' },
  { istrue: true, prop: 'orderCount5Avg', label: '5天日均订单量', width: '70', sortable: 'custom' },
  { istrue: true, prop: 'isCreateByErp', label: '是否ERP创建', width: '70', formatter: (row) => row.isCreateByErp == true ? "是" : "否", sortable: 'custom' },
  { istrue: true, prop: 'createdUserName', label: '创建人', width: '70', sortable: 'custom' },
  { istrue: true, prop: 'created', label: '创建时间', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'Modified', label: '同步时间', width: '90', sortable: 'custom' },
  //{istrue:true,prop:'WarehouseStatus',label:'仓库状态', width:'110' ,formatter:(row)=>row.warehouseStatus==null?"未检测":row.warehouseStatus==0?"有缺货":row.warehouseStatus==-1?"同步失败":"已同步",sortable:'custom'},
  //{istrue:true,prop:'detectionTime',label:'检测时间', width:'110',sortable:'custom' ,formatter:(row)=>row.detectionTime=="0001-01-01 00:00:00"?"":row.detectionTime=="1970-01-01 00:00:00"?"":row.detectionTime},
  //{istrue:true,prop:'combineName',label:'状态', width:'80',sortable:'custom'},
  { istrue: true, type: "button", label: '', width: "60", btnList: [{ label: "编辑", handle: (that, row) => that.onEdit(row) }] },
  { istrue: true, type: "button", label: '', width: "60", btnList: [{ label: "日志", handle: (that, row) => that.Showlog(row) }] }
];
const tableHandles1 = [
  { label: "新增组合编码", handle: (that) => that.addCombineCode() },
  // {label:"检查选中行库存", handle:(that)=>that.checkSelectedRowInventory()},
  { label: "组合编码导入模板", handle: (that) => that.onloadCombincode() },
  { label: "导出", handle: (that) => that.onExport() },
  // {label:"组合编码同步到聚水潭", handle:(that)=>that.SyncToJstMethod()},
];
export default {
  name: 'Roles',
  components: { cesTable, MyContainer, MyConfirmButton, ShowlogDetail, noheaderContainer },
  props: { isHistory: false },
  data() {
    return {
      customTagLoading: false,//自定义标签加载
      customTagValue: '',//自定义标签值
      labelMaintenanceVisible: false,//自定义标签弹框
      customTag:[],//自定义标签数据
      showClose: [],
      warehouselist: [],
      uploadLoading:false,
      editLoading: false,
      WarehouseStatus1: null,
      applyTransferDetail: false,
      dialogFormVisible11: false,
      editdialogVisible: false,
      that: this,
      qty:'',
      subfilter:{
        warehouseid: null,
        combineCodeName: '',
        combineCode: ''
      },
      filter: {
        SeparateWarehouse: null,
        combineCode: null,
        combineName: null,
        skuQty:null,
        goodsCode: null,
        isERP: null,
        createUserName: null,
        timerange: [],
        CreatedStart: null,
        CreatedEnd: null,
        wmsIds:[]
      },
      Uplodfilter: {
        SeparateWarehouse: null
      },
      list: [],

      isERP: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
      warehouseList2: [
        { label: '义乌仓', value: 0 },
        { label: '南昌昌东', value: 1 },
        { label: '南昌昌北', value: 2 },
        { label: '安徽', value: 3 },
        { label: '上海', value: 4 },
        { label: '跨境', value: 5 },
        { label: '南昌高科云仓', value: 6 },
        { label: '江苏云仓', value: 7 },
        { label: 'JD-昀晗义乌仓', value: 8 },
        { label: '南昌定制仓', value: 9 },
        { label: '圆通孵化仓', value: 10 },
        { label: '义乌圆通', value: 11 }
      ],//仓库下拉
      summaryarry: {},
      pager: { OrderBy: "combineCode", IsAsc: true },
      tableCols: tableCols,
      tableHandles: tableHandles1,
      dialogVisible: false,
      dialogCombineCodeVisible: false,
      autoform: {
        fApi: {},
        rule: [],
        options: { submitBtn: false },
      },
      autoform1: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
        rule1: [{ required: false }]
      },
      total: 0,
      sels: [],
      WarehouseStatuss: [],
      listLoading: false,
      pageLoading: false,

      dialogFormVisible: false,

      addLoading: false,
      deleteLoading: false,
      formtitle: "新增",
      fileList: [],
      fileList1: [],
      combincodelist: [],
      selids: [],//选择的id
      detail: {
        pager: { OrderBy: "goodsCode", IsAsc: true },
        list: [],
        editList: [],
        tableCols: [
          { istrue: true, prop: 'combineCode', label: '组合编码', width: '80', },
          { istrue: true, prop: 'goodsCode', label: '商品编码', width: '80', },
          { istrue: true, prop: 'qty', label: '数量', width: '60',  },
          { istrue: true, prop: 'wmsBin', label: '库位', width: '100', },
          { istrue: true, prop: 'wmsName', label: '仓库', width: '100',  },
          //{istrue:true,prop:'goodsImage',label:'图片', width:'60',type:'image',},
          { istrue: true, prop: 'goodsName', label: '商品名称', width: 'auto'},
          { istrue: true, prop: 'labels', label: '商品标签', width: 'auto',  },
          //{ istrue: true, type: "button", label: '操作', width: "120", btnList: [{ label: "申请调拨", handle: (that, row) => that.ApplyTransfer(row) }] }
        ],
        listLoading: false,
        total: 0,
        sels: [],
      },
      CombineCode: {
        visible: false,
        filter: {
          warehouse: null,
          CombineCode: null
        }
      },
      tableData: [],

      input: '',
      form: {
        name: '',
        region: '',
        date1: '',
        date2: '',
        delivery: false,
        type: [],
        resource: '',
        desc: ''
      },
      houseid: null,
      editindex: 0,
      editrow: {},
      formLabelWidth: '120px',
      Sku_ids: null,
      selshow:true,
      Warehouseslist: [{}],
      ShowlogDetail: {
        visible: false,
        filter: {
          combineCode: null,
        }
      }
    }
  },
  async mounted() {
    var res3 = await getAllWarehouse();
            var warehouselist1 = res3.data.filter((x) => x.name.indexOf('代发') < 0);
           // warehouselist1.unshift({ name: "全仓", co_id: 10361546, wms_co_id: 11793337, is_main: false, remark1: null, remark2: null, wms_co_id: -1 });
            this.warehouselist = warehouselist1;


    await this.initform();
    await this.getlist();
  },
  async created() {
    await this.getWarehousesList();
  },
  methods: {
    //标签删除
    async tagDeleteClick(item) {
      this.$confirm('此操作将删除该标签, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        const params = { parentTitle: '自定义标签', key: item.key }
        this.customTagLoading = true;
        const {success} = await delChildrenConfig(params);
        if(success){
          this.$message({ type: 'success', message: '删除标签成功!' });
          await this.onTagMaintenance()
        }
        this.customTagLoading = false;
      }).catch(() => {
      });
    },
    //标签新增
    async tagHandleEnter() {
      if (this.customTagValue) {
        const params = { parentTitle: '自定义标签', key: this.customTagValue, val:'' }
        this.customTagLoading = true;
        const {success} = await addChildrenConfig(params);
        if(success){
          this.$message({ type: 'success', message: '添加标签成功!' });
          this.customTagValue = '';
          await this.onTagMaintenance()
        }
        this.customTagLoading = false;
      }
    },
    //删除图标显示
    showCloseIcon(index) {
      this.$set(this.showClose, index, true);
    },
    //删除图标显示
    hideCloseIcon(index) {
      this.$set(this.showClose, index, false);
    },
    //获取标签数据打开弹框
    async onTagMaintenance(){
      const params = { title: '自定义标签' }
      this.customTagLoading = true;
      const {data,success} = await getConfigByParentTitle(params);
      if(success){
        this.customTag = data;
      }
      this.customTagValue = '';
      this.customTagLoading = false;
      this.labelMaintenanceVisible = true;
    },
    onsuccess(file, fileList){
      this.fileList1 = fileList;
    },
    warehousenamech(val){
      this.selshow = false;
      this.subfilter.warehouseid = val;
      this.selshow = true;

    },

    warehousenamefuc(val){
      var name;

      this.Warehouseslist.map((item)=>{
        if(val == item.label){
          this.subfilter.warehouseid = item.value
          name  = item.value;
        }
      })
      return name;
    },
    async submitend(){
      let params = {
      ...this.subfilter,
      "items": this.detail.editList
    };
    if(!this.subfilter.warehouseid){
      this.$message({type:'fail',message:'请选择仓库'});
      return
    }
    let res = await editCombineGoodsAsync(params);
    if(!res?.success){
      return
    }

    this.dialogFormVisible = false;
    this.$message({type:'success',message:'保存成功'});
    },
    endedit(){

      this.tableData[this.editindex] = this.editrow;
      this.$forceUpdate();
      this.editdialogVisible = false;
    },
    addRow(index,row){
      this.editindex = index;
      this.editrow = row;
      this.editdialogVisible = true;
    },
    // async checkcan(wmsName2){
    //   if(!label) return 0;
    //   else if(label.indexOf("义乌")>-1||label.indexOf("南昌")>-1||label.indexOf("昌东")>-1||label.indexOf("圆通5楼")>-1)  return 2;
    //   else return 0;
    // },
    deleteRow(index) {
      //rows.splice(index, 1);
      this.detail.editList.splice(index, 1);
    },
    async SyncToJstMethod() {
      if (this.selids == null || this.selids == "") {
        this.$message.error('请选择行数据！');
        return;
      }
      this.WarehouseStatuss.forEach(f => {
        if (f == null) {
          this.$message.error('选中的组合编码存在未检测,请先检测');
          throw ("禁止操作");
        }
        if (f == 0) {
          this.$message.error('选中的组合编码存在有缺货');
          throw ("禁止操作");
        }
        if (f == 1) {
          this.$message.error('选中的组合编码存在已同步');
          throw ("禁止操作");
        }
      })
      this.Sku_ids = this.selids.join(",");
      var params = { CombineCode: this.Sku_ids };
      var res = await SyncToJst(params);
      if (res.success == true) {
        this.$message.success('成功同步组合编码');
        this.WarehouseStatus1 = res.data;
        await this.onSearch();
      }
    },
    onloadCombincode() {
      window.open("../../static/excel/inventory/组合编码导入模板.xlsx", "_self");
    },
    closedialogapplyTransferDetail() {
      this.autoform1.fApi.resetFields()
    },
    closedialogCombineCode() {
      this.CombineCode.visible = false;
      this.combincodelist = nulleditList
    },
    onCancelSubmit() {
      this.CombineCode.visible = false;
      this.combincodelist = null
    },
    DeleteImportCombineCode(row) {
      this.combincodelist.splice(this.combincodelist.indexOf(row), 1)
    },
    Showlog(row) {
      this.ShowlogDetail.filter.combineCode = row.combineCode;
      this.ShowlogDetail.visible = true;
      this.$nextTick(async () => {
        await this.$refs.ShowlogDetail.onSearch();
      });
    },

    async onAdd(){
      //this.tableData.push({qty:'',goodsCode:''})
      this.detail.editList.push({qty:'',goodsCode:''})
      // this.dialogFormVisible11=true
      // this.listLoading=false;
    },



    // 显示编辑界面
    async onEdit(row) {
      this.formtitle = '编辑';
      this.dialogFormVisible = true
      this.subfilter.combineCode = row.combineCode;
      this.subfilter.combineCodeName = row.combineName;
      this.subfilter.goodsCode = row.goodsCode;
      this.subfilter.qty = row.skuQty;


      // this.subfilter.warehousename = row.wmsName2;
      this.subfilter.warehouseid = null;

      // this.subfilter.warehouseid = this.warehousenamefuc(row.wmsName2);


      // const res = await EditCombineGoodsAsync(row.id)
      // await this.autoform.fApi.setValue(res.data)
    },

    //检查选中行的库存
    async checkSelectedRowInventory() {
      if (this.filter.SeparateWarehouse == null || this.filter.SeparateWarehouse == "") {
        this.$message.error('请选择分仓！');
        return;
      }
      if (this.selids == null || this.selids == "") {
        this.$message.error('请选择行数据！');
        return;
      }
      this.Sku_ids = this.selids.join(",");
      var params = {
        Sku_ids: this.Sku_ids,
        Wms_co_id: this.filter.SeparateWarehouse
      };
      var res = await inventoryQuery(params);
      if (res.success == true) {
        this.$message.success('检测完成');
        this.WarehouseStatus1 = res.data;
        this.onSearch();
      }
    },
    async getWarehousesList() {
      var res3 = await getWarehouses();
      this.Warehouseslist = res3.data?.map(item => { return { value: item.wms_co_id, label: item.name }; });
    },
    //申请调拨
    async ApplyTransfer(row) {

      this.applyTransferDetail = true
      this.$nextTick(() => {
        this.autoform1.fApi.setValue(row);
      });
    },
    async onEditSubmitDetail() {
      this.editLoading = true;
      await this.autoform1.fApi.validate(async (valid, fail) => {
        if (valid) {
          const formData = this.autoform1.fApi.formData();
          const res = await creatAllocate(formData);
          if (res.code == 1) {
            this.$message.success('创建成功！');
            this.autoform1.fApi.resetFields()
            this.applyTransferDetail = false;
            this.onSearch();
          }
        } else { }
      })
      this.editLoading = false;
    },
    async onAddCombineCodeSubmit() {
      var statusflag = 0;
      this.combincodelist.forEach((element) => {
        // if(element.childeqty<=50){
        //   this.$message.error('存在缺货的子编码库存数小于等于50!');
        //   throw("禁止操作");
        // }
        if (element.status == 0 || element.status == "0") {
          statusflag += 1;
        }
      });
      if (statusflag > 0) {
        this.$message.error('请删除聚水潭中未存在的子编码!');
        return;
      }
      else {
        const uniqueArr = Array.from(this.combincodelist.reduce((map, obj) => map.set(obj.combineCode, obj), new Map()).values());
        uniqueArr.forEach(async (element) => {
          var tjlist = this.combincodelist.filter(f => f.combineCode == element.combineCode);
          var entry = { warehouseid: element.warehouseid, combineCode: element.combineCode, combineCodeName: element.combineName, goodsCode: element.goodsCode,qty: element.skuQty,items: tjlist };
          const res = await addCombineGoods(entry);
          if (res.code == 1) {
            this.$message.success('创建成功!');
            statusflag = 0;
          }
        });
      }
      this.CombineCode.visible = false;
    },
    addCombineCode() {
      this.CombineCode.visible = true;
    },
    async onExport() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
      var res = await exportCombineGoods(params);
      loadingInstance.close();
      if (!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '组合编码_' + new Date().toLocaleString() + '.xlsx')
      aLink.click();
    },
    async setWarehousesList() {
      await this.getWarehousesList();
      let rule1 = { validate: [{ type: 'number', required: true, message: '请输入调出仓' }], options: [{ value: null, label: '请选择' }] }
      rule1.options = this.Warehouseslist;
      return rule1;
    },
    async initform() {
      let that = this;
      this.autoform1.rule1 = [
        { type: 'hidden', field: 'id', title: 'id', value: '', col: { span: 12 } },
        { type: 'hidden', field: 'fkCode', title: '编码类型', value: '1', col: { span: 12 } },
        { type: 'hidden', field: 'combineCode', title: '组合编码', value: '', col: { span: 12 } },
        { type: 'select', field: 'warehouse', title: '调出仓', props: { clearable: true, disabled: false }, value: '', col: { span: 6 }, options: [], ...await this.setWarehousesList() },
        { type: 'select', field: 'link_warehouse', title: '调入仓', props: { clearable: true, disabled: false }, value: '', col: { span: 6 }, options: [], ...await this.setWarehousesList() },
        { type: 'input', field: 'receiver_name', title: '收货人', value: '', col: { span: 6 }, props: { maxlength: 20 }, validate: [{ type: 'string', required: true, message: '请输入收货人' }] },
        { type: 'input', field: 'goodsCode', title: '调拨编码', value: '', col: { span: 6 }, props: { maxlength: 30, disabled: true }, validate: [{ type: 'string', required: true, message: '请输入调拨编码' }] },
        { type: 'inputnumber', field: 'qty', title: '调拨数量', value: '0', col: { span: 6 }, props: { min: 0, max: 999999999999, precision: 0 }, validate: [{ type: 'number', required: true, message: '请输入调拨数量' }] },
        { type: 'DatePicker', field: 'expiration_date', title: '调拨有效期', value: '', validate: [{ type: 'string', required: true, message: '请输入调拨有效期' }], props: { type: 'datetime', format: 'yyyy-MM-dd', placeholder: '调拨有效期' }, col: { span: 6 } },
        { type: 'input', field: 'remark', title: '备注', value: '', col: { span: 6 }, props: { maxlength: 100 } },
      ]
    },
    //获取查询条件
    getCondition() {
      this.filter.CreatedStart = null;
      this.filter.CreatedEnd = null;
      if (this.filter.timerange && this.filter.timerange.length > 1) {
        this.filter.CreatedStart = this.filter.timerange[0];
        this.filter.CreatedEnd = this.filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = { ...pager, ...page, ... this.filter }
      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    //分页查询
    async getlist() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true
      var res = await pageCombineGoods(params);
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry = res.data.summary;
      this.list = data
    },
    //排序查询
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    },
    selsChange: function (sels) {
      this.sels = sels;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      this.WarehouseStatuss = []
      rows.forEach(f => {
        this.selids.push(f.combineCode);
        this.WarehouseStatuss.push(f.warehouseStatus);
      })
    },
    cellclick(row, column, cell, event) {
      this.onSearchDetail(row.combineCode);
    },
    //明细查询
    //查询第一页
    async onSearchDetail(combineCode) {
      this.$refs.pagerDetail.setPage(1);
      await this.getDetailList(combineCode);
    },
    //明细分页查询
    async getDetailList(combineCode) {
      //this.detail.listLoading = true;
      var pager = this.$refs.pagerDetail.getPager();
      var page = this.detail.pager;
      const params = {
        ...pager,
        ...page,
        combineCode: combineCode
      }
      var res;
      if (this.isHistory) {
        res = await pageCombineGoodsDetailHistory(params);
      }
      else {
        res = await pageCombineGoodsDetail(params);
      }
      this.listLoading = false;
      if (!res?.success) {
        return
      }
      this.detail.total = res.data.total;
      const data = res.data.list;
      data.forEach(d => {
        d._loading = false
      })
      this.detail.list = data;
      var newarr = [];
      data.map((item)=>{
        newarr.push({qty: item.qty, goodsCode: item.goodsCode})
      });
      this.detail.editList =newarr;


    },
    //排序查询
    async sortchangeDetail(column) {
      if (!column.order)
        this.detail.pager = {};
      else {
        this.detail.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearchDetail();
    },
    startImport() {
      this.dialogVisible = true;
    },
    importCombineCode() {
      this.dialogCombineCodeVisible = true;
      this.uploadLoading=false;
    },
    cancelImport() {
      this.dialogVisible = false;
    },
    beforeRemove() {
      return false;
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    uploadFile(item) {
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      const res = importCombineGoods(form);
      this.$message({
        message: '上传成功,正在导入中...',
        type: "success",
      });
      this.$refs.upload.clearFiles();
    },
    uploadSuccess1(response, file, fileList1) {
      if (response.code == 200) {
      } else {
        fileList1.splice(fileList1.indexOf(file), 1);
      }
    },
    submitUpload1() {
      if (this.Uplodfilter.SeparateWarehouse == null) {
        this.$message.error('请选择分仓！');
        // this.$refs.upload1.clearFiles();
        // this.dialogCombineCodeVisible = false;
        return;
      }
      if(this.fileList1.length<1){
        this.$message.error('请选择文件！');
        return;
      }
      this.uploadLoading=true;
      this.$refs.upload1.submit();
    },
    async uploadFile1(item) {
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      form.append("SeparateWarehouse", this.Uplodfilter.SeparateWarehouse);
      const res = await importCombineGoodsV2(form);
      if (res.success == true) {
        this.$message({
          message: '上传成功,正在导入中...',
          type: "success",
        });
        this.uploadLoading=false
        this.$refs.upload1.clearFiles();
        this.fileList1= [];
        this.dialogCombineCodeVisible = false;
      }
      this.$refs.upload1.clearFiles();
      this.fileList1= [];
      this.combincodelist = res.data;
      this.dialogCombineCodeVisible = false;
      this.Uplodfilter.SeparateWarehouse = null
      this.uploadLoading=false
    },
  }
}
</script>
<style scoped>::v-deep .el-link.el-link--primary {
  margin-right: 7px;
}

.tagLabelStyle{
  display: flex;
  align-items: center;
  width: 160px;
  margin: 10px 8px;
  border: 1px solid #000;
  height: 30px;
  max-height: 30px;
  border-radius: 5px;
}

.columnamont {
  background-color: rgb(241, 230, 223);
}</style>
