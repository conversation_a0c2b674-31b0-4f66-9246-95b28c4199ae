<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.styleCodes" v-model="ListInfo.styleCodes"
                    placeholder="系列编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="styleCodesCallback($event, 1)" title="系列编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="styleCodesCallback($event, 2)" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.combineCodes" v-model="ListInfo.combineCodes"
                    placeholder="组合编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="styleCodesCallback($event, 3)" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <el-select v-model="ListInfo.isMoreThen" placeholder="是否超长" class="publicCss" clearable>
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                </el-select>
                <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.isCheck" placeholder="是否核实" class="publicCss" clearable>
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
                    <el-button type="primary" size="mini" @click="verify">核实</el-button>
                    <el-button type="primary" size="mini" @click="oneClickRelease">一键放行</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            id="20250429160726" :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols"
            @select="select" :is-selection="false" :is-select-column="true" :is-index-fixed="false"
            style="width: 100%; margin: 0;" height="100%" :showsummary="data.summary ? true : false"
            :summaryarry="data.summary" @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="编码备注" :visible.sync="verifyVisible" width="30%" v-dialogDrag>
            <el-form :model="verifyForm" status-icon :rules="rules1" ref="ruleForm1" label-width="120px"
                class="demo-ruleForm" v-if="verifyVisible">
                <el-form-item label="是否核实" prop="remark">
                    <el-select v-model="verifyForm.isCheck" placeholder="是否核实" class="publicCss" clearable>
                        <el-option label="是" :value="true" />
                        <el-option label="否" :value="false" />
                    </el-select>
                </el-form-item>
                <el-form-item label="备注内容" prop="remark">
                    <el-input v-model="verifyForm.remark" type="textarea" row="5" placeholder="请输入备注内容" maxlength="500"
                        show-word-limit></el-input></el-form-item>
                <el-form-item>
                    <el-button @click="verifyVisible = false">关闭</el-button>
                    <el-button type="primary" @click="verifySubmit('ruleForm1')" v-throttle="1000">提交</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/SaleItems/GoodsLwg/'
import { mergeTableCols } from '@/utils/getCols'
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan
    },
    data() {
        return {
            api,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false,
                summarys: [],
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            selectList: [],
            verifyForm: {
                goodsCodes: '',
                remark: '',
                isCheck: null
            },
            verifyVisible: false,
        }
    },
    async mounted() {
        await this.getCol();
        await this.getList()
    },
    methods: {
        oneClickRelease() {
            if (this.selectList.length == 0) {
                this.$message.error('请至少选择一条数据')
                return
            }
            this.$confirm('是否一键放行?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = this.selectList.map(item => item.goodsCode)
                const { success } = await request.post(`/api/verifyOrder/SaleItems/Weight/AddPassGoodsCodes`, res)
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '放行成功!'
                    });
                }
                this.selectList = []
                this.getList()
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消!'
                });
            });
        },
        verify() {
            if (this.selectList.length == 0) {
                this.$message.error('请至少选择一条数据')
                return
            }
            this.verifyVisible = true
            this.verifyForm.remark = ''
            this.verifyForm.isCheck = null
        },
        verifySubmit(ref) {
            this.$refs[ref].validate(async (valid) => {
                if (valid) {
                    const res = this.selectList.map(item => {
                        return {
                            goodsCode: item.goodsCode,
                            remark: this.verifyForm.remark,
                            isCheck: this.verifyForm.isCheck
                        }
                    })
                    const { success } = await request.post(`${this.api}Check`, res)
                    if (success) {
                        this.$message.success('操作成功')
                        this.getList()
                        this.selectList = []
                        this.verifyVisible = false
                    }
                } else {
                    return false;
                }
            });
        },
        select(val) {
            this.selectList = val
        },
        styleCodesCallback(val, type) {
            if (type == 1) {
                this.ListInfo.styleCodes = val
            } else if (type == 2) {
                this.ListInfo.goodsCodes = val
            } else if (type == 3) {
                this.ListInfo.combineCodes = val
            }
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                data.unshift({ type: 'checkbox', label: '', visible: true })
                data.forEach(item => {
                    if (item.prop == 'orderNoInner') {
                        item.type = 'orderLogInfo'
                        item.orderType = 'orderNoInner'
                    }
                    if (item.prop == 'proCode') {
                        item.type = 'html'
                        item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
                    }
                })
                this.tableCols = mergeTableCols(data)
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0;
    }
}
</style>
