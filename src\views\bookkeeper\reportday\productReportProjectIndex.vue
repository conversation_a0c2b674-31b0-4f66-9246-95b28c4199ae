<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="项目组日报" name="first1" style="height: 100%">
        <productReportProject ref="productReportProject" style="height: 100%"></productReportProject>
      </el-tab-pane>
      <el-tab-pane label="订单日报" name="first2" :lazy="true" style="height: 100%"
        v-if="checkPermission('ProjectOrderDayReport')">
        <ProjectOrderDayReport @ChangeActiveName2="ChangeActiveName2" ref="ProjectOrderDayReport" style="height: 100%">
        </ProjectOrderDayReport>
      </el-tab-pane>
      <el-tab-pane label="编码日报" name="first3" :lazy="true" style="height: 100%"
        v-if="checkPermission('ProjectGoodCodeDayReport')">
        <ProjectGoodCodeDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="ProjectGoodCodeDayReport" style="height: 100%"></ProjectGoodCodeDayReport>
      </el-tab-pane>
      <el-tab-pane label="ID日报" name="first4" :lazy="true" style="height: 100%"
        v-if="checkPermission('ProjectIdDayReport')">
        <ProjectIdDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="ProjectIdDayReport" style="height: 100%"></ProjectIdDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺日报" name="first5" :lazy="true" style="height: 100%"
        v-if="checkPermission('ProjectShopDayReport')">
        <ProjectShopDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="ProjectShopDayReport" style="height: 100%"></ProjectShopDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺SKU日报" name="first7" :lazy="true" style="height: 100%"
        v-if="checkPermission('ProjectCommodityDayReport')">
        <ProjectCommodityDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="ProjectCommodityDayReport" style="height: 100%"></ProjectCommodityDayReport>
      </el-tab-pane>
      <el-tab-pane label="明细日报" name="first6" :lazy="true" style="height: 100%"
        v-if="checkPermission('ProjectDetailDayReport')">
        <ProjectDetailDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="ProjectDetailDayReport" style="height: 100%"></ProjectDetailDayReport>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import productReportProject from "./productReportProject.vue";
import ProjectOrderDayReport from "./ProjectOrderDayReport.vue";
import ProjectGoodCodeDayReport from "./ProjectGoodCodeDayReport.vue";
import ProjectIdDayReport from "./ProjectIdDayReport.vue";
import ProjectShopDayReport from "./ProjectShopDayReport.vue";
import ProjectCommodityDayReport from "./ProjectCommodityDayReport.vue";
import ProjectDetailDayReport from "./ProjectDetailDayReport.vue";

import middlevue from "@/store/middle.js"
export default {
  name: "productReportProjectIndex",
  components: {
    MyContainer, productReportProject, ProjectOrderDayReport, ProjectGoodCodeDayReport, ProjectIdDayReport, ProjectShopDayReport, ProjectDetailDayReport, ProjectCommodityDayReport
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },
  async mounted() {
    middlevue.$on('toLinkTxDetailDayReport', (data) => {
      if (data.isTrue && data.plat == 'qpt') {
        this.activeName = 'first6';
        setTimeout(() => {
          middlevue.$emit('toLinkTxDetailDayReportQuery', data)
        }, 500);

      } else {
        this.activeName = 'first8';
        setTimeout(() => {
          middlevue.$emit('toLinkTxOutgoingprofitIDorderdetailQuery', data)
        }, 500);
      }
    })
  },
  beforeDestroy() {
    middlevue.$off('toLinkTxDetailDayReport')
  },
  methods: {
    ChangeActiveName(activeName) {
      this.activeName = 'first2';
      this.$refs.ProjectOrderDayReport.ProjectGoodCodeDayReportArgument(activeName)
    },
    ChangeActiveName2(activeName, No, Time) {
      this.activeName = 'first6';
      this.$refs.ProjectDetailDayReport.ProjectDetailDayReportArgument(activeName, No, Time)
    }
  },
};
</script>

<style lang="scss" scoped></style>
