<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='pddgroupinquirsstatisticslist' @select='selectchange'
            :isSelection='false' :tableCols='tableCols' :loading="listLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
                <el-button-group>
                  <el-button style="padding: 0;margin: 0;">
                    <el-select style="float:left;width:120px;" v-model="Filter.PartitionID" placeholder="分区" clearable
                               filterable @change="handlePartitionChange">
                      <el-option v-for="item in partitionList" :key="item.id" :label="item.partitionName"
                                 :value="item.id" />
                    </el-select>
                  </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="Filter.Shopname" placeholder="店铺名称" clearable :collapse-tags="true" style="width: 140px" filterable>
                            <el-option v-for="item in shopList" :key="item.shopname" :label="item.shopname" :value="item.shopname" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <datepicker v-model="Filter.Sdate"></datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>

                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
                @get-page="getGroupInquirsStatisticsList" />
        </template>

    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
  getPddGroup,
  getInquirsStatisticsByShopListMonth,
  getPddShop,
  getGroupNamesByPartitionId, getAllPartitions
} from '@/api/customerservice/pddInquirsnew'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar';
import dayjs from "dayjs";
import { formatTime } from "@/utils";

const tableCols =[
  {istrue:true,prop:'shopName',label:'店名', width:'200',sortable:'custom'},
  { istrue: true, prop: 'partitionName', label: '分区名称', width: '120', sortable: 'custom' },

  {
    istrue:true,label:'销售额',  merge: true, prop: 'mergeField',
    cols:  [
      {istrue:true,prop:'totalSalesAmount', label: '总销售额(合计)', width: '120', sortable: 'custom', formatter: (row) => { return row.totalSalesAmount ? row.totalSalesAmount.toFixed(2) : 0  } },
      {istrue:true,prop:'saleBeforeSalesAmount', label: '销售额(售前)', width: '120', sortable: 'custom', formatter: (row) => { return row.saleBeforeSalesAmount ? row.saleBeforeSalesAmount.toFixed(2) : 0  } },
      {istrue:true,prop:'saleingSalesAmount', label: '销售额(售中)', width: '120', sortable: 'custom', formatter: (row) => { return row.saleingSalesAmount ? row.saleingSalesAmount.toFixed(2): 0  } },
      {istrue:true,prop:'saleAfterSalesAmount', label: '销售额(售后)', width: '120', sortable: 'custom', formatter: (row) => { return row.saleAfterSalesAmount ? row.saleAfterSalesAmount.toFixed(2) : 0 } },
    ]
  },
  {
    istrue:true,prop:'',label:'接待量',  merge: true, prop: 'mergeField1',
    cols:  [
      {istrue:true,prop:'totalReception', label: '总接待量(合计)', width: '120', sortable: 'custom'},
      {istrue:true,prop:'saleBeforeReception', label: '接待量(售前)', width: '120', sortable: 'custom'},
      {istrue:true,prop:'saleingReception', label: '接待量(售中)', width: '120', sortable: 'custom' },
      {istrue:true,prop:'saleAfterReception', label: '接待量(售后)', width: '120', sortable: 'custom'},
    ]
  },
  {
    istrue:true,prop:'',label:'服务分',  merge: true, prop: 'mergeField2',
    cols:  [
      {istrue:true,prop:'totalServiceScore', label: '服务分(总计)', width: '120', sortable: 'custom',formatter:(row)=>{ return Math.floor(row.totalServiceScore*10)/10 }},
      {istrue:true,prop:'beforeServiceScore', label: '服务分(售前)', width: '120', sortable: 'custom'},
      {istrue:true,prop:'afterServiceScore', label: '服务分(售后)', width: '120', sortable: 'custom'},
    ]
  },
  {
    istrue:true,prop:'',label:'评分≤3订单数',  merge: true, prop: 'mergeField3',
    cols:  [
      {istrue:true,prop:'totalLowRatingOrderCount', label: '评分≤3订单数(总计)', width: '170', sortable: 'custom'},
      {istrue:true,prop:'beforeLowRatingOrderCount', label: '评分≤3订单数(售前)', width: '170', sortable: 'custom'},
      {istrue:true,prop:'afterLowRatingOrderCount', label: '评分≤3订单数(售后)', width: '170', sortable: 'custom'},
    ]
  },
  {
    istrue:true,prop:'',label:'纠纷退款数',  merge: true, prop: 'mergeField4',
    cols:  [
      {istrue:true,prop:'totalDisputeRefundCount', label: '纠纷退款数(总计)', width: '150', sortable: 'custom'},
      {istrue:true,prop:'beforeDisputeRefundCount', label: '纠纷退款数(售前)', width: '150', sortable: 'custom'},
      {istrue:true,prop:'afterDisputeRefundCount', label: '纠纷退款数(售后)', width: '150', sortable: 'custom'},
    ]
  },
  {
    istrue:true,prop:'',label:'投诉数',  merge: true, prop: 'mergeField5',
    cols:  [
      {istrue:true,prop:'totalComplainCount', label: '投诉数(总计)', width: '120', sortable: 'custom'},
      {istrue:true,prop:'beforeComplainCount', label: '投诉数(售前)', width: '120', sortable: 'custom'},
      {istrue:true,prop:'afterComplainCount', label: '投诉数(售后)', width: '120', sortable: 'custom'},
    ]
  },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar },
    data() {
        return {
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            Filter: {

                Sdate: [formatTime(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30), "YYYY-MM-DD 00:00:00"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],

            },
            shopList: [],
          partitionList: [], // 分区列表

            userList: [],
            groupList: [],
            pddgroupinquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "shopName", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            //
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
        };
    },
    async mounted() {
        //await this.setGroupSelect();
        await this.setShopSelect();
      await this.getPartitionList();

    },
    methods: {
        async setGroupSelect(){
                const form = new FormData();
                form.append("enmPddGroupType", 0);
                const res = await getPddGroup(form);
                this.groupList=res.data;
        },
      async getPartitionList() {
        try {
          const res = await getAllPartitions({ EnmPddGroupType: 0 });
          this.partitionList = res.data;
        } catch (error) {
          console.error('获取分区列表失败', error);
        }
      },
        async setShopSelect(){
            const form = new FormData();
            form.append("enmPddGroupType", 0);
            const res = await getPddShop(form);
            this.shopList=res.data;
        },
        async showchart(row) {

            if (this.Filter.timerange) {
                var d = new Date(this.Filter.Sdate[0])
                var startsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                this.Filter.startSdate = startsdate;

                d = new Date(this.Filter.Sdate[1])
                var endsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                this.Filter.startSdate = endsdate;
            }

            var params = { groupName: row.groupName, StartSdate: this.Filter.startSdate, EndSdate: this.Filter.endSdate ,EnmPddGroupType:0}
            let that = this;

            const res = await pagePddGroupKfEfficiencyListMap(params).then(res => {
                that.dialogMapVisible.visible = true;
                that.dialogMapVisible.data = res;
                that.dialogMapVisible.title = res.legend[0]
            })
            this.dialogMapVisible.visible = true

        },

        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onImportSyj() {
            this.dialogVisibleSyj = true
        },
      async handlePartitionChange(partitionId) {
        if (partitionId) {
          try {
            const res = await getGroupNamesByPartitionId({ partitionId });
            if (res.data && res.data.length > 0) {
              // 将分区下的组名转换为grouplistsel格式，并只保留一体组
              this.grouplistsel = res.data.map(groupName => ({
                groupname: groupName
              })).filter(item => item.groupname.includes("一体"));
            } else {
              this.grouplistsel = [];
            }
            // 清空已选择的组名
            this.Filter.GroupNames = [];
          } catch (error) {
            console.error('获取分区下组名失败', error);
          }
        } else {
          // 如果清空分区选择，则获取所有组
          await this.setGroupSelect();
        }
      },

        async onSubmitupload2() {
            this.$refs.upload2.submit()
        },
        onRefresh() {
            this.setGroupSelect();
            this.onSearch()
        },
        onSearch() {
            this.$refs.pager.setPage(1);

            this.getGroupInquirsStatisticsList();
        },

        async getGroupInquirsStatisticsList() {

            if (this.Filter.Sdate) {
                this.Filter.startSdate = this.Filter.Sdate[0];
                this.Filter.endSdate = this.Filter.Sdate[1];
            }
            else {
                this.Filter.startSdate = null;
                this.Filter.endSdate = null;
            }
            if(this.Filter.startSdate == null ||   this.Filter.endSdate == null){
                this.$message({ message: '请选择时间范围', type: "warning" });
                return;

            }
             this.Filter.EnmPddGroupType=0 ;
            const para = { ...this.Filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para

            };


            this.listLoading = true;
            const res = await getInquirsStatisticsByShopListMonth(params);
            this.listLoading = false;

            this.total = res.total
            this.pddgroupinquirsstatisticslist = res.list;
            this.summaryarry = res.summary;
        },
        groupclick(row) {

            if (this.Filter.Sdate) {
                this.Filter.startSdate = this.Filter.Sdate[0];
                this.Filter.endSdate = this.Filter.Sdate[1];
            }
            else {
                this.Filter.startSdate = null;
                this.Filter.endSdate = null;
            }

            window.showpddpddlist(row.groupName, this.Filter.startSdate == null ? "" : this.Filter.startSdate
                , this.Filter.endSdate == null ? "" : this.Filter.endSdate)

            window.showpddtabSh4()

        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
