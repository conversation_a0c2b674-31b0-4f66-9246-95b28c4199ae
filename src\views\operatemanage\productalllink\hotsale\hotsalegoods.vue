<template>
    <my-container v-loading="pageLoading">

        <el-tabs v-model="activeName" style="height: 95%" @tab-click="handleClick">

            <el-tab-pane label="热销" name="tab1" style="height: 100%;">
                <hotsalegoodslist ref="hotsalegoodslist" />
            </el-tab-pane>

            <el-tab-pane label="采购推新" name="tab7" style="height: 100%;"  :lazy="true">
                <hotsalebrandpushlist ref="hotsalebrandpushlist" />
            </el-tab-pane>

            <el-tab-pane label="跨境运营选品" name="tab12" style="height: 100%;"  :lazy="true"  v-if="checkPermission('HotSaleBrandPushNewKJSel')" >
                <HotSaleBrandPushNewKJSel ref="HotSaleBrandPushNewKJSel" />
            </el-tab-pane>

            <el-tab-pane label="运营询价" name="tab15" style="height: 100%;"  :lazy="true"  v-if="checkPermission('OperationalInquiryIndex')" >
                <operationalinquiry ref="operationalinquiry" />
            </el-tab-pane>

            <el-tab-pane label="采购推新(新版)" name="tab10" style="height: 100%;"  :lazy="true">
                <hotsalebrandpushlistnew ref="hotsalebrandpushlistnew" />
            </el-tab-pane>

            <el-tab-pane label="采购推新报表" name="tab11" style="height: 100%;" :lazy="true" v-if="checkPermission('HotSaleBrandPushNewReport')" >
                <HotSaleBrandPushNewReport ref="HotSaleBrandPushNewReport" />
            </el-tab-pane>

            <el-tab-pane label="已选品" name="tab2" style="height: 100%;" :lazy="true">
                <hotsalegoodschooselist ref="hotsalegoodschooselist" />
            </el-tab-pane>

            <el-tab-pane label="采样" name="tab3" style="height: 100%;" :lazy="true">
                <hotsalegoodsskuorderlist ref="hotsalegoodsskuorderlist" />
            </el-tab-pane>

            <el-tab-pane label="编码建档" name="tab4" style="height: 100%;" :lazy="true">
                <hotsalegoodsbuildgoodsdoclist ref="hotsalegoodsbuildgoodsdoclist" />
            </el-tab-pane>

            <el-tab-pane label="选品统计报表" name="tab5" style="height: 100%;" :lazy="true">
                <ChooseSummaryCharts ref="ChooseSummaryCharts" />
            </el-tab-pane>

            <el-tab-pane label="新品进货进度" name="tab6" style="height: 100%;" :lazy="true">
                <HotPurchasePlanProcessList ref="HotPurchasePlanProcessList" />
            </el-tab-pane>
            <el-tab-pane label="申报进货进度" name="tab14" style="height: 100%;" :lazy="true">
                <purchasegoods ref="purchasegoods" :isBrand="false" />
            </el-tab-pane>

            <el-tab-pane label="比样" name="tab8" style="height: 100%;" :lazy="true">
                <hotsalegoodsselection ref="hotsalegoodsselection" />
            </el-tab-pane>

            <el-tab-pane label="公司商品" v-if="checkPermission('pageGetSeriesGoodsData')" name="tab9" style="height: 100%;"
                :lazy="true">
                <companyGoods ref="companyGoods" />
            </el-tab-pane>

        </el-tabs>

    </my-container>
</template>
<script>

import { rulePlatform } from "@/utils/formruletools";
import MyContainer from "@/components/my-container";
import hotsalegoodschooselist from "@/views/operatemanage/productalllink/hotsale/hotsalegoodschooselist.vue";
import hotsalegoodslist from "@/views/operatemanage/productalllink/hotsale/hotsalegoodslist.vue";
import hotsalegoodsskuorderlist from "@/views/operatemanage/productalllink/hotsale/hotsalegoodsskuorderlist.vue";
import hotsalegoodsbuildgoodsdoclist from "@/views/operatemanage/productalllink/hotsale/hotsalegoodsbuildgoodsdoclist.vue";
import ChooseSummaryCharts from "@/views/operatemanage/productalllink/hotsale/ChooseSummaryCharts.vue";
import HotPurchasePlanProcessList from "@/views/operatemanage/productalllink/hotsale/HotPurchasePlanProcessList.vue";
import hotsalebrandpushlist from "@/views/operatemanage/productalllink/hotsale/hotsalebrandpushlist.vue";
import operationalinquiry from "@/views/operatemanage/productalllink/operationalinquiry/index.vue";
import hotsalebrandpushlistnew from "@/views/operatemanage/productalllink/hotsale/hotsalebrandpushlistnew.vue";
import hotsalegoodsselection from "@/views/operatemanage/productalllink/hotsale/hotsalegoodsselection.vue";
import HotSaleBrandPushNewReport from "@/views/operatemanage/productalllink/hotsalebrandpushnewreport/HotSaleBrandPushNewReport.vue";
import HotSaleBrandPushNewKJSel from "@/views/operatemanage/productalllink/hotsale/HotSaleBrandPushNewKJSel.vue";
import purchasegoods from "../goodscodestock/purchasegoods.vue";
import companyGoods from './companyGoods.vue'

export default {
    name: "hotsalegoods",
    components: {
        MyContainer, hotsalegoodschooselist, hotsalegoodslist,
        hotsalegoodsskuorderlist, hotsalegoodsbuildgoodsdoclist,
        ChooseSummaryCharts, HotPurchasePlanProcessList, hotsalebrandpushlist, operationalinquiry, hotsalebrandpushlistnew, 
        hotsalegoodsselection, companyGoods, HotSaleBrandPushNewReport,HotSaleBrandPushNewKJSel,purchasegoods
    },
    data() {
        return {
            filter: {
                yearMonth: '',
                groupName: '',
                shopName: '',
                Platform: [],
                PlatformName: null,
                startTime: '',
                endTime: ''
            },
            activeName: 'tab1',
            that: this,
            pageLoading: false,
            importNumber: ''
        };
    },
    methods: {
        handleClick(tab, event) {
            if (tab && tab.$options && tab.$options.propsData.name == "tab5") {
                this.$nextTick(() => {
                    this.$refs["ChooseSummaryCharts"].resize();
                });
            }
        }
    }
}

</script>
