<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-select v-model="ListInfo.complaintType" placeholder="投诉类型" class="publicCss" clearable filterable multiple
          collapse-tags>
          <el-option v-for="item in complaintList" :key="item" :label="item" :value="item" />
        </el-select>
        <div class="publicCss">
          <inputYunhan ref="productproCode" :inputt.sync="ListInfo.proCode" v-model="ListInfo.proCode" width="155px"
            placeholder="产品ID/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="50" :maxlength="1000"
            @callback="callbackMethod($event, 1)" title="产品ID">
          </inputYunhan>
        </div>
        <div class="publicCss">
          <inputYunhan ref="productStyleCode" :inputt.sync="ListInfo.styleCode" v-model="ListInfo.styleCode"
            width="155px" placeholder="款式编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="50"
            :maxlength="1000" @callback="callbackMethod($event, 2)" title="款式编码">
          </inputYunhan>
        </div>
        <div class="publicCss">
          <inputYunhan ref="productGoodsCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode"
            width="155px" placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="50"
            :maxlength="1000" @callback="callbackMethod($event, 3)" title="商品编码">
          </inputYunhan>
        </div>
        <el-select v-model="ListInfo.brandName" placeholder="采购" class="publicCss" clearable filterable multiple
          collapse-tags>
          <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="ListInfo.operaterName" placeholder="运营" class="publicCss" clearable filterable multiple
          collapse-tags>
          <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
        </el-select>
        <el-select v-model="ListInfo.groupName" placeholder="小组" class="publicCss" clearable filterable multiple
          collapse-tags>
          <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
        </el-select>
        <el-input v-model.trim="ListInfo.adder" placeholder="添加人" maxlength="50" clearable class="publicCss" />
        <div class="publicCss">
          <inputYunhan ref="productOrderNo" :inputt.sync="ListInfo.orderNo" v-model="ListInfo.orderNo" width="155px"
            placeholder="订单编号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="50" :maxlength="1000"
            @callback="callbackMethod($event, 4)" title="订单编号">
          </inputYunhan>
        </div>
        <el-date-picker v-model="addtimeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="添加开始时间" end-placeholder="添加结束时间" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime($event, 1)">
        </el-date-picker>
        <el-date-picker v-model="edittimeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="更新开始时间" end-placeholder="更新结束时间" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime($event, 2)">
        </el-date-picker>
        <el-button type="primary" @click="getList('search')" style="width: 80px;">搜索</el-button>
        <el-button type="success" @click="startImport">导入</el-button>
        <el-button type="danger" @click="deletionMethod"
          v-if="checkPermission('ComplaintRegistrationDeletion')">删除</el-button>
        <el-button type="primary" @click="append">添加投诉</el-button>
        <el-dropdown @command="exportProps" style="margin-left: 10px;">
          <el-button style="height: 28px;" type="warning">
            导出<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="a">导出数据</el-dropdown-item>
            <el-dropdown-item command="b">导出选择附件</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </template>
    <vxetablebase :id="'businessRegistration202411251552'" :tablekey="'businessRegistration202411251552'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'" @select="checkboxMethod">
      <template #annex="{ row }">
        <el-button type="text" @click="downloadAttachment(row)"
          :disabled="!row.annex || row.annex.length === 0">下载</el-button>
      </template>
      <template slot="right">
        <vxe-column title="操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="onEditMethod(row)">编辑</el-button>
              <el-button type="text" @click="onModificationMethod(row)">修改记录</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog :title="complainTitle" :visible.sync="complainVisible" width="48%" v-dialogDrag style="margin-top: -5vh;"
      :close-on-click-modal="false">
      <div>
        <complaintInformation ref="refcomplaintInformation" v-if="complainVisible"
          :selectorParameter="selectorParameter" :formDataInfo="formDataInfo" :verifyWhether="verifyWhether"
          @closeCallback="closeCallback" />
      </div>
      <div style="display: flex;justify-content: end;">
        <el-button @click="complainVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSaveMethod">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div slot="title" class="header-title">
        <span class="title-text"><span>导入数据</span></span>
        <span class="title-close"><el-button @click="downLoadTemplateFile">下载模版</el-button></span>
      </div>
      <div>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog title="修改记录" :visible.sync="modificationVisible" width="70%" v-dialogDrag>
      <div style="height: 500px;">
        <modificationRecord ref="refmodificationRecord" v-if="modificationVisible" :formDataInfo="formDataInfo" />
      </div>
      <div style="display: flex;justify-content: end;margin: 15px 10px 0 0;">
        <el-button @click="modificationVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import { pickerOptions } from '@/utils/tools'
import complaintInformation from "./components/complaintInformation.vue";
import modificationRecord from "./components/modificationRecord.vue";
import { getDirectorList, getDirectorGroupList, getProductBrandPageList, getList as getshopList } from '@/api/operatemanage/base/shop'
import { getBusinessComplaintRegister, exportBusinessComplaintRegister, deleteBusinessComplaintRegister, importBusinessComplaintRegister, getComplaintType } from '@/api/operatemanage/operate'
import { getAllProBrand } from '@/api/inventory/warehouse'
import axios from 'axios';
import dayjs from 'dayjs'
import JSZip from 'jszip';
import { saveAs } from 'file-saver';
const tableCols = [
  { istrue: true, width: '40', type: "checkbox" },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'complaintType', label: '投诉类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'complaintReason', label: '投诉原因', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'proCode', label: '产品ID', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '款式编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'brandName', label: '产品负责采购', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'operateSpecialUserName', label: '产品负责运营专员', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'operateAssistantName', label: '产品负责运营助理', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'groupName', label: '产品编码挂靠组', },
  { width: '55', align: 'left', prop: 'picture', label: '图片', type: 'images', },
  { width: '55', align: 'center', prop: 'annex', label: '附件', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '订单编号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'addTime', label: '添加时间', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'editTime', label: '更新时间', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'adder', label: '添加人', },
]
export default {
  name: "businessRegistration",
  components: {
    MyContainer, vxetablebase, complaintInformation, inputYunhan, modificationRecord
  },
  data() {
    return {
      complaintList: [],
      modificationVisible: false,
      fileslist: [],
      verifyWhether: false,
      formDataInfo: {},
      complainTitle: '新增投诉信息',
      selectorParameter: {
        directorGroupList: [],
        directorList: [],
        brandlist: []
      },
      checkboxContent: [],
      brandlist: [],//采购
      directorList: [],//运营
      directorGroupList: [],//小组
      importVisible: false,
      fileList: [],
      fileparm: {},
      uploadLoading: false,
      complainVisible: false,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        addTimeStart: null,//开始时间
        addTimeEnd: null,//结束时间
        editTimeStart: null,//开始时间
        editTimeEnd: null,//结束时间
        complaintType: [],//投诉类型
        proCode: '',//产品ID
        styleCode: '',//款式编码
        goodsCode: '',//商品编码
        brandName: [],//采购
        operaterName: [],//运营
        groupName: [],//小组
        adder: '',//添加人
        orderNo: '',//订单编号
        exportType: 0,//导出类型
      },
      addtimeRanges: [],
      edittimeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.init()
    await this.getList()
  },
  methods: {
    deletionMethod() {
      if (this.checkboxContent.length === 0) return this.$message({ message: "请先选择数据", type: "warning" });
      this.$confirm('是否删除选中数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const mesgIDs = this.checkboxContent.map(item => item.mesgID)
        const { success } = await deleteBusinessComplaintRegister(mesgIDs)
        if (success) {
          this.getList()
          this.checkboxContent = []
          this.$message({ message: "删除成功", type: "success" });
        } else {
          this.$message({ message: "删除失败", type: "error" });
        }
      }).catch(() => {
        this.$message({ message: "已取消删除", type: "info" });
      });
    },
    onModificationMethod(row) {
      this.$nextTick(() => {
        this.formDataInfo = JSON.parse(JSON.stringify(row))
        this.modificationVisible = true
      })
    },
    async closeCallback() {
      this.complainVisible = false
      await this.acquisitionType()
      this.getList()
    },
    onSaveMethod: _.debounce(function (param) {
      this.onSingleSaves(param);
    }, 1000),
    onSingleSaves() {
      this.$nextTick(() => {
        this.$refs.refcomplaintInformation.onSingleSave()
      })
    },
    onEditMethod(row) {
      this.complainTitle = '编辑投诉信息'
      this.verifyWhether = true
      this.formDataInfo = JSON.parse(JSON.stringify(row))
      this.complainVisible = true
    },
    async downloadAttachment(row) {
      await this.hangrow(row);
      await this.downzip();
    },

    hangrow(row) {
      this.fileslist = [];
      this.loading = true;
      let billImgs = [row]
        .filter(item => item.annex.length > 0) // 过滤出有附件的项
        .map(item => {
          return item.annex.map((item1, index) => {
            let complaintType = item.complaintType ? item.complaintType : '';
            let proCode = item.proCode ? item.proCode : '';
            let businessId = `${complaintType}-${proCode}`;
            return {
              billImgs: item1.url, // 附件 URL
              neiname: item1.name, // 附件名称
              businessId: businessId // 拼接后的 businessId
            };
          });
        })
        .flat(); // 使用 flat 将多维数组变成一维

      return new Promise((resolve, reject) => {
        const downloadPromises = billImgs.map(async (item, index) => {
          try {
            if (!item.billImgs) {
              return;
            }
            await this.downloadFileMethod(item.billImgs, item.neiname, item.businessId);
          } catch (error) {
            console.error(`Error downloading file from ${item}:`, error);
            reject(error);
          }
        });

        Promise.all(downloadPromises)
          .then(() => {
            resolve();
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    async onBatchDownload() {
      if (this.checkboxContent.length === 0) return this.$message({ message: "请先选择数据", type: "warning" });
      await this.getfileList();
      await this.downzip();
    },

    async getfileList() {
      this.fileslist = [];
      this.loading = true;
      // 遍历 checkboxContent 中的每一项
      let billImgs = this.checkboxContent
        .filter(item => item.annex.length > 0) // 过滤出有附件的项
        .map(item => {
          // 处理每个 item 中的 annex 数组
          return item.annex.map((item1, index) => {
            // 拼接 businessId: styleCode + proCode，索引从 1 开始
            let complaintType = item.complaintType ? item.complaintType : '';
            let proCode = item.proCode ? item.proCode : '';
            let businessId = `${complaintType}-${proCode}`;
            return {
              billImgs: item1.url, // 附件 URL
              neiname: item1.name, // 附件名称
              businessId: businessId // 拼接后的 businessId
            };
          });
        })
        .flat(); // 使用 flat 将多维数组变成一维

      await Promise.all(billImgs.map(async (img) => {
        if (!img['billImgs']) {
          return;
        }
        await this.downloadFileMethod(img.billImgs, img.neiname, img.businessId);
      }));

      this.downend = true;
      this.loading = false;
    },

    async downloadFileMethod(url, neiname, name) {
      try {
        let response = await fetch(url);
        if (response.status != 200) {
          return;
        }
        let blob = await response.blob();
        this.fileslist.push({ name: name, neiname: neiname, blob: blob });
      } catch (error) {
      }
    },
    async downzip() {
      const zip = new JSZip();
      const currentDate = new Date();
      const year = currentDate.getFullYear();
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const day = String(currentDate.getDate()).padStart(2, '0');
      const formattedDate = `${year}${month}${day}`;
      this.fileslist.forEach((file, index) => {
        zip.file(file.name + '/' + file.neiname, file.blob, { binary: true });
      });
      zip.generateAsync({ type: 'blob' }).then((content) => {
        saveAs(content, `pdf文件集合.zip`);
      });
      this.loading = false;
      return;
    },
    checkboxMethod(e) {
      this.checkboxContent = e;
    },
    callbackMethod(e, type) {
      if (type == 1) {
        this.ListInfo.proCode = e;
      } else if (type == 2) {
        this.ListInfo.styleCode = e;
      } else if (type == 3) {
        this.ListInfo.goodsCode = e;
      } else if (type == 4) {
        this.ListInfo.orderNo = e;
      }
    },
    append() {
      this.complainTitle = '新增投诉信息'
      this.verifyWhether = false
      this.complainVisible = true
    },
    async changeTime(e, type) {
      if (type == 1) {
        this.ListInfo.addTimeStart = e ? e[0] : null
        this.ListInfo.addTimeEnd = e ? e[1] : null
      } else if (type == 2) {
        this.ListInfo.editTimeStart = e ? e[0] : null
        this.ListInfo.editTimeEnd = e ? e[1] : null
      }
    },
    async exportProps(e) {
      if (e == 'a') {
        this.loading = true
        this.ListInfo.exportType = 0
        const { data, success } = await exportBusinessComplaintRegister(this.ListInfo)
        this.loading = false
        const aLink = document.createElement("a");
        let blob = new Blob([data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '工商投诉导出数据' + new Date().toLocaleString() + '.xlsx')
        aLink.click()
      } else {
        this.ListInfo.exportType = 1
        this.onBatchDownload()
      }
    },
    downLoadTemplateFile() {
      window.open("../../static/excel/inventory/工商投诉导入.xlsx", "_self");
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.importVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importBusinessComplaintRegister(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.importVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.importVisible = true;
    },
    async init() {
      const { data, success } = await getDirectorGroupList({})
      if (!success) return
      this.directorGroupList = [{ key: '0', value: '未知' }].concat(data || []);
      const { data: data1, success: success1 } = await getDirectorList({})
      if (!success1) return
      this.directorList = [{ key: '0', value: '未知' }].concat(data1 || []);
      const { data: data2, success: success2 } = await getAllProBrand({})
      if (!success2) return
      this.brandlist = data2.map(item => { return { value: item.key, label: item.value }; });
      this.selectorParameter = {
        directorGroupList: this.directorGroupList,
        directorList: this.directorList,
        brandlist: this.brandlist
      }
      await this.acquisitionType()
    },
    async acquisitionType() {
      const { data: data3, success: success3 } = await getComplaintType({})
      if (!success3) return
      this.complaintList = data3
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.addtimeRanges && this.addtimeRanges.length == 0) {
        this.ListInfo.addTimeStart = dayjs().format('YYYY-MM-DD')
        this.ListInfo.addTimeEnd = dayjs().format('YYYY-MM-DD')
        this.addtimeRanges = [this.ListInfo.addTimeStart, this.ListInfo.addTimeEnd]
      }
      this.loading = true
      const { data, success } = await getBusinessComplaintRegister(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.picture = item.picture ? JSON.stringify(item.picture.map(item => { return { url: item } })) : '';
          // item.annex = item.annex ? JSON.stringify(item.annex) : '[]';
        });
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.publicCss {
  width: 155px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px 0 0;

  .title-text {
    display: flex;
    align-items: center;

    .title-close {
      margin-left: 10px;
    }
  }
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
