<template>
    <section style="padding:10px;">
      <!--查询-->
      <el-form class="ad-form-query" :inline="true" :model="filters" @submit.native.prevent>
        <el-form-item>
          <el-input v-model="filters.processName" placeholder="流程名称" maxlength="50" style="width:30%" clearable="clearable">
          </el-input>
          &nbsp;
          <el-input v-model="filters.cstmNodeName" placeholder="自定义节点名称"  maxlength="50" style="width:30%" clearable="clearable">
          </el-input>
          &nbsp;
          <el-input v-model="filters.nodeDDUserName" placeholder="节点钉用户"  maxlength="50" style="width:30%" clearable="clearable">
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onGetList">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onAdd">新增</el-button>
        </el-form-item>
      </el-form>
  
      <!--列表-->
      <el-table
        v-loading="listLoading"
        row-key="id"
        :data="apiTree"
        :default-expand-all="false"
        highlight-current-row
        style="width: 100%;"
      >
        <el-table-column type="index" width="50" label="#" />
        <el-table-column prop="processCode" width label="流程编码"  />
        <el-table-column prop="processName" width="280" label="流程名称" />
        <el-table-column prop="cstmNodeCode" label="自定义节点编码" width />
        <el-table-column prop="cstmNodeName" label="自定义节点名称" width="280" />
        <el-table-column prop="nodeDDUserName" label="节点钉用户" width="200" />
        <el-table-column prop="exceptionDDUserName" label="异常通知人" width="200" />
        <el-table-column  label="操作" width="180">
          <template #default="{ $index, row }">
            <el-button  @click="onEdit($index, row)">编辑</el-button>
            <my-confirm-button  type="delete" :loading="row._loading" @click="onDelete($index, row)" />
          </template>
        </el-table-column>
      </el-table>
  
      <!--新增窗口-->
      <el-dialog
        title="新增"
        :visible.sync="addFormVisible"
        :close-on-click-modal="true"
        @close="onCloseAddForm"
         v-dialogDrag
      >
        <el-form ref="addForm" :model="addForm" label-width="100px" :rules="addFormRules">
          <el-form-item label="流程编码" prop="processCode">
            <el-input v-model="addForm.processCode"  maxlength="50" />
          </el-form-item>
          <el-form-item label="流程名称" prop="processName">
            <el-input v-model="addForm.processName"  maxlength="50"  />
          </el-form-item>
          <el-form-item label="自定义节点编码" prop="cstmNodeCode">
            <el-input v-model="addForm.cstmNodeCode"  maxlength="50" />
          </el-form-item>
          <el-form-item label="自定义节点名称" prop="cstmNodeName">
            <el-input v-model="addForm.cstmNodeName" maxlength="50" />
          </el-form-item>
          <el-form-item label="节点钉用户" prop="nodeDDUserld">
            <el-select v-model="addForm.nodeDDUserld" placeholder="请选择" filterable>
            <el-option v-for="item in this.userList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
        </el-select>
        </el-form-item>
        <el-form-item label="异常通知人" prop="exceptionDDUserld">
            <el-select v-model="addForm.exceptionDDUserld" placeholder="请选择" filterable>
            <el-option v-for="item in this.userList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click.native="addFormVisible = false">取消</el-button>
            <my-confirm-button type="submit" :validate="addFormValidate" :loading="addLoading" @click="onAddSubmit" />
          </div>
        </template>
      </el-dialog>
  
      <!--编辑窗口-->
      <el-dialog
        title="编辑"
        :visible.sync="editFormVisible"
        :close-on-click-modal="true"
        @close="onCloseEditForm"
        v-dialogDrag
      >
        <el-form ref="editForm" :model="editForm"  :rules="editFormRules" label-width="100px">
            <el-form-item label="流程编码" prop="processCode">
            <el-input v-model="editForm.processCode" maxlength="50"/>
          </el-form-item>
          <el-form-item label="流程名称" prop="processName">
            <el-input v-model="editForm.processName" maxlength="50"/>
          </el-form-item>
          <el-form-item label="自定义节点编码" prop="cstmNodeCode">
            <el-input v-model="editForm.cstmNodeCode" maxlength="50" />
          </el-form-item>
          <el-form-item label="自定义节点名称" prop="cstmNodeName">
            <el-input v-model="editForm.cstmNodeName" maxlength="50" />
          </el-form-item>
          <el-form-item label="节点钉用户" prop="nodeDDUserld">
            <el-select v-model="editForm.nodeDDUserld" placeholder="请选择" filterable>
            <el-option v-for="item in this.userList" :key="item.value" :label="item.label" :value="item.value" >
            </el-option>
        </el-select>
          </el-form-item>
          <el-form-item label="异常通知人" prop="exceptionDDUserld">
            <el-select v-model="editForm.exceptionDDUserld" placeholder="请选择" filterable>
            <el-option v-for="item in this.userList" :key="item.value" :label="item.label" :value="item.value" >
            </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click.native="editFormVisible = false">取消</el-button>
            <my-confirm-button type="submit" :validate="editFormValidate" :loading="editLoading" @click="onEditSubmit" />
          </div>
        </template>
      </el-dialog>
    </section>
  </template>
  
  <script>
  import { formatTime, treeToList, listToTree, getTreeParents } from '@/utils'
  import {
    removeApi,
    editApi,
    addApi,
    getV2SwaggerJson,
    getFinancialV2SwaggerJson,
    getOrderV2SwaggerJson,
    getOperateManageV2SwaggerJson,
    getInventoryV2SwaggerJson,
    syncApi,
    getApiList,
    batchRemoveApi,
    getApi
  } from '@/api/admin/api'
  import { gtApprovalNodeListAsync,deleteApprovalNodeAsync,updateApprovalNodeAsync,
    addApprovalNodeAsync,getUserList,getApprovalNode} from '@/api/admin/approvalNode.js'
  import MyConfirmButton from '@/components/my-confirm-button'
  
  export default {
    name: 'Api',
    components: {
      MyConfirmButton
    },
    data() {
      return {
        filters: {
            processName:'',
            cstmNodeName:'',
            nodeDDUserName:''
        },
        apiTree: [],
        listLoading: false,
        sels: [], // 列表选中列
        userList:[],
        selExceptionDDUserld:"",
        addDialogFormVisible: false,
        editFormVisible: false, // 编辑界面是否显示
        editLoading: false,
        editFormRules: {
            processCode: [{ required: true, message: '请输入', trigger: 'blur' }],
            processName: [{ required: true, message: '请输入', trigger: 'blur' }],
            cstmNodeCode: [{ required: true, message: '请输入', trigger: 'blur' }],
            cstmNodeName: [{ required: true, message: '请输入', trigger: 'blur' }],
            nodeDDUserld: [{ required: true, message: '请选择', trigger: 'blur' }],
            exceptionDDUserld: [{ required: true, message: '请选择', trigger: 'blur' }],
        },
        // 编辑界面数据
        editForm: {
          id: 0,
          processCode: '',
          processName: '',
          cstmNodeCode: '',
          nodeDDUserld: '',
          exceptionDDUserld: ''
        },
        addFormVisible: false, // 新增界面是否显示
        addLoading: false,
        addFormRules: {
            processCode: [{ required: true, message: '请输入', trigger: 'blur' }],
            processName: [{ required: true, message: '请输入', trigger: 'blur' }],
            cstmNodeCode: [{ required: true, message: '请输入', trigger: 'blur' }],
            cstmNodeName: [{ required: true, message: '请输入', trigger: 'blur' }],
            nodeDDUserld: [{ required: true, message: '请选择', trigger: 'blur' }],
            exceptionDDUserld: [{ required: true, message: '请选择', trigger: 'blur' }],
        },
        // 新增界面数据
        addForm: {
            processCode: '',
            processName: '',
            cstmNodeCode: '',
            nodeDDUserld: '',
            exceptionDDUserld: ''
        },
        addFormKey: 1,
        modules: [],
        syncManagerLoading: false,
        syncFinancialLoading: false,
        syncOrderLoading: false,
        deleteLoading: false
      }
    },
    mounted() {
      this.onGetList();
      this.getUserSelList();
    },
    methods: {
     //获取用户下拉数据
     async getUserSelList() {
            var resWt = await getUserList();
            if(resWt?.success)
            {
                this.userList = resWt.data?.userList.map(item => ({ value: item.ddUserId, label: item.userName }));
                this.selExceptionDDUserld=resWt.data?.selDDUserId;
            }
        },
      // 获取列表
      async onGetList() {
        const para = { ...this.filters };
        this.listLoading = true
        const res = await gtApprovalNodeListAsync(para)
        this.listLoading = false
        if (!res?.success) {
          return
        }
        this.apiTree = res.data?.list;
    
      },
      // 显示编辑界面
      async onEdit(index, row) {
        const loading = this.$loading()
        const res = await getApprovalNode({id:row.id})
        loading.close()
        if (res && res.success) {
          this.editForm = res.data
          this.editFormVisible = true
        }
      },
      onCloseEditForm() {
        this.$refs.editForm.resetFields()
      },
      // 显示新增界面
      onAdd() {
        this.addForm.exceptionDDUserld=this.selExceptionDDUserld;
        this.addFormVisible = true
      },
      onCloseAddForm() {
        this.$refs.addForm.resetFields()
        ++this.addFormKey
      },
      // 编辑
      editFormValidate: function() {
        let isValid = false
        this.$refs.editForm.validate(valid => {
          isValid = valid
        })
        return isValid
      },
      async onEditSubmit() {
        this.editLoading = true
        const res = await updateApprovalNodeAsync(this.editForm)
        this.editLoading = false
        if (!res?.success) {
          return
        }
        this.$message({
          message: this.$t('admin.updateOk'),
          type: 'success'
        })
        this.$refs['editForm'].resetFields()
        this.editFormVisible = false
        this.onGetList()
      },
      // 新增
      addFormValidate: function() {
        let isValid = false
        this.$refs.addForm.validate(valid => {
          isValid = valid
        })
        return isValid
      },
      async onAddSubmit() {
        this.addLoading = true
        const res = await addApprovalNodeAsync(this.addForm)
        this.addLoading = false
  
        if (!res?.success) {
          return
        }
        this.$message({
          message: this.$t('admin.addOk'),
          type: 'success'
        })
        this.$refs['addForm'].resetFields()
        this.addFormVisible = false
        this.onGetList()
      },
      // 删除
      async onDelete(index, row) {
        row._loading = true
        const para = { id: row.id }
        const res = await deleteApprovalNodeAsync(para)
  
        row._loading = false
  
        if (!res?.success) {
          return
        }
        this.$message({
          message: this.$t('admin.deleteOk'),
          type: 'success'
        })
        this.onGetList()
      },
    }
  }
  </script>
  