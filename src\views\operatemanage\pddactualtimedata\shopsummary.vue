<template>
  <container>
    <template #header>
      <div style="display:flex;flex-direction: column;">
        <el-button-group>
          <el-button style="margin-top:0px">
            <el-date-picker style="width: 410px" v-model="filter.timerange"  type="daterange"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"></el-date-picker>
            <el-select filterable v-model="filter.shopCode" placeholder="店铺" clearable
              style="width: 130px;margin-left: 10px;">
              <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
            </el-select>
            <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组"
              style="width: 90px;margin-left: 5px;">
              <el-option key="无运营组" label="无运营组" :value="0"></el-option>
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-button type="primary" @click="onSearch()" style="margin-left: 10px;">查询</el-button>
            <el-button type="primary" @click="onExport()" style="margin-left: 10px;">导出</el-button>
          </el-button>
        </el-button-group>
      </div>
    </template>
    <template>
      <ces-table :id="'shopsummary202408041659'" :tablekey="'ActualTimeShopSummary202305141124'" ref="TableCols" :showsummary='true'
        :summaryarry='proSummaryarry' :that='that' :isIndex='true' :tablefixed='true' :hasexpand='false'
        :isSelectColumn="false" :tableData='proTableList' :tableCols='TableCols' :tableHandles='tableHandles'
        @sortchange="sortchange" :loading="listLoading"   :border="true" @summaryClick='onsummaryClick'>
      </ces-table>
    </template>

    <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
      <span>
        <template>
          <el-date-picker style="width: 410px" v-model="buscharDialog.filter.timerange" type="daterange"
            @change="similarityDateChange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="pickerOptions"></el-date-picker>
        </template>
      </span>
      <span>
        <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 汇总趋势图 -->
    <el-dialog :title="summaryDialog.title" :visible.sync="summaryDialog.visible" width="80%" height="700px" v-dialogDrag>
      <span>
        <template>
          <el-date-picker style="width: 410px" v-model="summaryDialog.filter.timerange" type="daterange"
            @change="summaryDateChange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="pickerOptions"></el-date-picker>
        </template>
      </span>
      <span>
        <buschar v-if="summaryDialog.visible" ref="summaryBuschar" :analysisData="summaryDialog.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="summaryDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>
  </container>
</template>
<script>
import { getPddActualTimeDataByShopOverviewAsync, getPddActualTimeAnalysisByShopOverviewAsync, getPddActualTimeAnalysisByShopSummaryAsync,
  exportPddActualTimeDataByShopOverviewAsync } from '@/api/operatemanage/pddactualtimedata'
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { getDirectorGroupList } from '@/api/operatemanage/base/shop';
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import { Loading } from 'element-ui';
import buschar from '@/components/Bus/buschar'
let loading;
const startLoading = () => {
  loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};

const tableCols1 = [
  { istrue: true, prop: 'yearMonthDay', sortable: 'custom', label: '年月日', width: '70' },
  { istrue: true, prop: 'shopName', sortable: 'custom', label: '店铺名称',  formatter: (row) => row.shopName },
  { istrue: true, prop: 'groupId', sortable: 'custom', label: '小组', width: '70', formatter: (row) => row.groupName },
  { istrue: true, prop: 'ccc', type: 'click', label: '趋势图', width: '50', align: 'center', formatter: (row) => '趋势图', handle: (that, row) => that.showSimilarity(row) },
  { istrue: true, summaryEvent: true, prop: 'allPayAmount', sortable: 'custom', label: '总支付金额', width: '85' },
  { istrue: true, summaryEvent: true, prop: 'allPayOrderCount', sortable: 'custom', label: '总支付订单数', width: '85' },
  // { istrue: true, summaryEvent: true, prop: 'overviewSpendAmount', sortable: 'custom', label: '总览花费（元）', width: '85' },
  // { istrue: true, summaryEvent: true, prop: 'overviewOrderAmount', sortable: 'custom', label: '总览交易额（元）', width: '85' },
  { istrue: true, summaryEvent: true, prop: 'allStationExtendSpendAmount', sortable: 'custom', label: '总花费（元）', width: '85' },
  { istrue: true, summaryEvent: true, prop: 'allStationExtendOrderSpendAmount', sortable: 'custom', label: '成交花费（元）', width: '85' },
  { istrue: true, summaryEvent: true, prop: 'allStationExtendOrderAmount', sortable: 'custom', label: '交易额（元）', width: '85' },
  { istrue: true, summaryEvent: true, prop: 'allStationExtendInvestmentRate', sortable: 'custom', label: '实际投产比', width: '85' },
  { istrue: true, summaryEvent: true, prop: 'allStationExtendOrderCount', sortable: 'custom', label: '成交笔数', width: '85' },
  { istrue: true, summaryEvent: true, prop: 'allStationExtendAvgSpendAmount', sortable: 'custom', label: '每笔成交花费（元）', width: '85' },
  { istrue: true, summaryEvent: true, prop: 'allStationExtendAvgOrderAmount', sortable: 'custom', label: '每笔成交金额（元）', width: '85' },
  { istrue: true, summaryEvent: true, prop: 'allStationExtendExtendFeeRate', sortable: 'custom', label: '全站推广费比', width: '85',formatter:(row)=>row.allStationExtendExtendFeeRate+"%" },
  { istrue: true, summaryEvent: true, prop: 'allStationExtendExposureCount', sortable: 'custom', label: '曝光量', width: '80' },
  { istrue: true, summaryEvent: true, prop: 'allStationExtendClickCount', sortable: 'custom', label: '点击量', width: '70' },
  { istrue: true, summaryEvent: true, prop: 'allStationExtendClickRate', sortable: 'custom', label: '点击率', width: '70' ,formatter:(row)=>row.allStationExtendClickRate+"%" },
  { istrue: true, summaryEvent: true, prop: 'allStationExtendTrunOutRate', sortable: 'custom', label: '点击转换率', width: '80',formatter:(row)=>row.allStationExtendTrunOutRate+"%" },
];

const tableHandles = [
];

export default {
  name: "PddActualTimeDataShopSummary",
  components: { container, cesTable, buschar },
  data() {
    return {
      that: this,
      filter: {
        shopCode: null,
        startTime: null,
        endTime: null,
        timerange: null,
        groupId: null
      },
      shopList: [],
      grouplist: [],
      TableCols: tableCols1,
      tableHandles: tableHandles,
      total: 0,
      proPager: { OrderBy: null, IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      proTableList: [],
      proSummaryarry: {},
      selids: [],
      pickerOptions: {
        shortcuts: [{
          text: '前一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime());
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
            const date2 = new Date(); date2.setDate(date2.getDate());
            picker.$emit('pick', [date1, date2]);
            window.setshowprogress(false);
          }
        }]
      },
      buscharDialog: {
        title: "趋势图",
        visible: false,
        data: [],
        filter: {
          shopName:null,
          startTime: null,
          endTime: null,
          timerange: null
        }
      },
      summaryDialog: {
        title: "趋势图",
        visible: false,
        data: [],
        filter: {
          startTime: null,
          endTime: null,
          timerange: null,
          column: null
        }
      },
    };
  },
  async mounted() {
    let end = new Date();
    let start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
    end.setTime(end.getTime());
    this.filter.timerange = [start, end];
    let res1 = await getshopList({ platform: 2, CurrentPage: 1, PageSize: 100000 });
    this.filter.shopCode = null
    this.shopList = res1.data.list
    let res2 = await getDirectorGroupList();
    this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
    this.onSearch();
  },
  async created() {
  },
  methods: {
    async sortchange(column) {
      if (!column.order) this.proPager = {};
      else
        this.proPager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      await this.onSearch();
    },
    async onSearch() {
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      let pager = this.$refs.pager.getPager()
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      this.listLoading = true;
      let params = { ...pager, ...this.proPager, ... this.filter }
      let res = await getPddActualTimeDataByShopOverviewAsync(params);
      this.listLoading = false;
      this.proTableList = res.data?.list;
      this.total = res.data?.total;
      this.proSummaryarry = res.data?.summary;
    },
    async showSimilarity(row) {
      let loadingInstance = Loading.service();
      Loading.service({ fullscreen: true });
      let that = this;
      const date = new Date();
      date.setYear(row.yearMonthDay.substr(0,4));
      date.setMonth(row.yearMonthDay.substr(4,2)-1);
      date.setDate(row.yearMonthDay.substr(6,2));

      this.buscharDialog.filter.timerange=[date,date];
      if (this.buscharDialog.filter.timerange) {
        this.buscharDialog.filter.startTime = this.buscharDialog.filter.timerange[0];
        this.buscharDialog.filter.endTime = this.buscharDialog.filter.timerange[1];
      }
      this.buscharDialog.filter.shopName=row.shopName;
      let params = { shopName: row.shopName, startTime: this.buscharDialog.filter.startTime, endTime: this.buscharDialog.filter.endTime };
      await getPddActualTimeAnalysisByShopOverviewAsync(params).then(res => {
        that.buscharDialog.visible = true;
        that.buscharDialog.data = res.data;
      });
      await this.$refs.buschar.initcharts()
      loadingInstance.close();
    },
    async similarityDateChange() {
      this.buscharDialog.filter.startTime = null;
      this.buscharDialog.filter.endTime = null;
      if (this.buscharDialog.filter.timerange) {
        this.buscharDialog.filter.startTime = this.buscharDialog.filter.timerange[0];
        this.buscharDialog.filter.endTime = this.buscharDialog.filter.timerange[1];
      }
      let params = { shopName: this.buscharDialog.filter.shopName, startTime: this.buscharDialog.filter.startTime, endTime: this.buscharDialog.filter.endTime };
      let that = this;
      await getPddActualTimeAnalysisByShopOverviewAsync(params).then(res => {
        that.buscharDialog.data = res.data;
      });
      await this.$refs.buschar.initcharts()
    },


    async onsummaryClick(property) {
      // this.$message({message:property,type:"warning"});
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.summaryDialog.filter.startTime = this.filter.timerange[0];
        this.summaryDialog.filter.endTime = this.filter.timerange[1];
      }
      let pager = this.$refs.pager.getPager();
      this.summaryDialog.filter.column = property;
      let params = { ...pager, ...this.pager, ...this.filter, ...this.summaryDialog.filter };
      let that = this;
      let res = await getPddActualTimeAnalysisByShopSummaryAsync(params).then(res => {
        that.summaryDialog.visible = true;
        that.summaryDialog.data = res.data
        that.summaryDialog.filter.timerange = that.filter.timerange;
      });
    },
    async summaryDateChange() {
      this.summaryDialog.filter.startTime = null;
      this.summaryDialog.filter.endTime = null;
      if (this.summaryDialog.filter.timerange) {
        this.summaryDialog.filter.startTime = this.summaryDialog.filter.timerange[0];
        this.summaryDialog.filter.endTime = this.summaryDialog.filter.timerange[1];
      }
      let pager = this.$refs.pager.getPager();
      let params = { ...pager, ...this.pager, ...this.filter, ...this.summaryDialog.filter };
      let that = this;
      let res = await getPddActualTimeAnalysisByShopSummaryAsync(params).then(res => {
        that.summaryDialog.data = res.data
      });
      await this.$refs.summaryBuschar.initcharts()
    },
    async onExport(){
      let pager = this.$refs.pager.getPager()
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      const params = {  ...pager,   ...this.proPager,   ...this.filter};
      let res= await exportPddActualTimeDataByShopOverviewAsync(params);
      if(!res?.data) {
         this.$message({message:"没有数据",type:"warning"});
         return
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download','拼多多实时数据店铺概览' +  new Date().toLocaleString() + '_.xlsx' )
      aLink.click()
    },
  }
}

</script>
<style></style>
