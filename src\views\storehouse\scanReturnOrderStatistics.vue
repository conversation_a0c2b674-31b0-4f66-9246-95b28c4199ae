<template>
  <my-container>

    <template #header>
      <el-date-picker style="width: 280px" v-model="timeRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" 
        range-separator="至" start-placeholder="起始操作日期" end-placeholder="结束操作日期" :picker-options="pickerOptions" 
        @change="changeTime" unlink-panels :clearable="false">
      </el-date-picker>
      <el-input v-model="filter.operator" v-model.trim="filter.operator" placeholder="操作员" style="width:200px;" clearable>
      </el-input>
      <el-select v-model="filter.wmsIdList" placeholder="仓库" multiple clearable collapse-tags filterable style="width:280px;">
        <el-option v-for="item in wareHouseList" :key="item.wms_co_id" :label="item.name" :value="item.wms_co_id" ></el-option>
      </el-select>

      <el-button type="primary" icon="el-icon-search" @click="onSearchList">查询</el-button>
      <!-- <el-button type="primary" icon="el-icon-upload2" @click="onImportList">导入列表</el-button>
      <el-button type="primary" icon="el-icon-upload2" @click="onImportDetail">导入明细</el-button> -->
      <el-button type="primary" @click="openExport('list')">导入列表</el-button>
      <el-button type="primary" @click="openExport('detail')">导入明细</el-button>
      <el-button type="primary" @click="onExportList">导出</el-button>
    </template>
    
    <vxetablebase :id="'ScanReturnOrderListStatistics'" ref="tableList" :that="that" @sortchange="sortchangeList" :tableData="tableDataList" 
      :tableCols="tableColsList" :isSelection="false" :isSelectColumn="false" :tableFixed="true" v-loading="listLoadingList" 
      style="width: 100%; margin: 0" :height="'100%'" :showsummary='true' :summaryarry="summaryarryList" :ispoint="false" >
    </vxetablebase>
    
    <!--分页-->
    <template #footer>
      <my-pagination ref="pagerList" :total="totalList" @page-change="PagechangeList" @size-change="SizechangeList" />
    </template>

    <!-- 扫码退件工作量统计售后明细 -->
    <!-- <el-drawer title="扫码退件工作量统计明细" direction="btt" :visible.sync="detailVisible" size="45%"
      :wrapper-closable="true" :modal="false" :modal-append-to-body="false" > -->
    <el-dialog title="扫码退件工作量售后统计明细" :visible.sync="detailVisible" width="76%" v-dialogDrag >
      <div style="margin: 5px 0;height: 80%;padding: 0 10px;">
        <div style="display: flex;justify-content: end;margin-bottom: 5px;">
          <el-button type="primary" @click="onExportDetail">导出为excel</el-button>
          <el-button type="primary" @click="detailVisible = false">关闭</el-button>
        </div>

        <vxetablebase :id="'ScanReturnOrderDetailStatistics'" v-if="detailVisible && footerDataArray.length > 0"
          ref="tableDetail" :that="that" @sortchange="sortchangeDetail" :tableData="tableDataDetail" :tableCols="tableColsDetail" 
          :isSelection="false" :isSelectColumn="true" :tableFixed="true" v-loading="listLoadingDetail" 
          style="width: 100%; margin: 0" :height="'600px'" :showsummary='true' :footerDataArray="footerDataArray" :showoverflow="true" 
          @footercellclick="onsummaryClick" :ispoint="false" >
        </vxetablebase>

        <my-pagination ref="pagerDetail" :total="totalDetail" @page-change="PagechangeDetail" @size-change="SizechangeDetail" />
      </div>
    <!-- </el-drawer> -->
    </el-dialog>
    
    <!-- 售后销退仓/次品仓合计明细 -->
    <el-dialog :title="detailFilter.warehouseRemark" :visible.sync="totalDetailVisible" width="45%" @closeDialog="onCloseDialog" 
      v-dialogDrag>
      <div style="display: flex;justify-content: end;margin-bottom: 5px;">
        <el-button type="primary" @click="onExportTotalDetail">导出为excel</el-button>
        <el-button type="primary" @click="totalDetailVisible = false">关闭</el-button>
      </div>
      
      <vxetablebase :id="'ScanReturnOrderTotalDetailStatistics'" ref="tableTotalDetail" :that="that" @sortchange="sortchangeTotalDetail" :tableData="tableDataTotalDetail" 
        :tableCols="tableColsTotalDetail" :isSelection="false" :isSelectColumn="false" :tableFixed="true" v-loading="listLoadingTotalDetail" 
        style="width: 100%; margin: 0" :height="'500px'" :showsummary='true' :summaryarry="summaryarryTotalDetail" :ispoint="false" >
      </vxetablebase>
      
      <my-pagination ref="pagerTotalDetail" :total="totalTotalDetail" @page-change="PagechangeTotalDetail" @size-change="SizechangeTotalDetail" />
    </el-dialog>

    <!-- 销退上架明细 -->
    <el-dialog title="销退上架明细" :visible.sync="cancelReturnDetailVisible" width="46%" v-dialogDrag>
      <div style="margin: 5px 0;height: 80%;padding: 0 10px;">
        <div style="display: flex;justify-content: end;margin-bottom: 5px;">
          <el-button type="primary" @click="onExportCancelReturnDetail">导出为excel</el-button>
          <el-button type="primary" @click="cancelReturnDetailVisible = false">关闭</el-button>
        </div>

        <vxetablebase :id="'ScanReturnOrderCancelReturnDetailStatistics'" ref="tableCancelReturnDetail" :that="that" @sortchange="sortchangeCancelReturnDetail" :tableData="tableDataCancelReturnDetail" 
          :tableCols="tableColsCancelReturnDetail" :isSelection="false" :isSelectColumn="false" :tableFixed="true" v-loading="listLoadingCancelReturnDetail" 
          style="width: 100%; margin: 0" :height="'500px'" :showsummary='true' :summaryarry="summaryarryCancelReturnDetail" :ispoint="false" >
        </vxetablebase>
        
        <my-pagination ref="pagerCancelReturnDetail" :total="totalCancelReturnDetail" @page-change="PagechangeCancelReturnDetail" @size-change="SizechangeCancelReturnDetail" />
      </div>
    </el-dialog>

    <!-- 导入 -->
    <el-dialog :title="importTitle" :visible.sync="importVisible" width="20%" height="600px" v-dialogDrag>
      <el-upload class="uploadList" :http-request="uploadFileList" :limit="1" :file-list="fileList"
        :on-remove="uploadRemoveList" accept=".xlsx">
        <el-button class="addsc" type="text">点击上传</el-button>
      </el-upload>

      <div style="color: red;text-align: center;">温馨提示:只能上传一个xlsx文件</div>
      
      <div style="display: flex;justify-content: center;margin-top: 10px;">
        <el-button type="primary" @click="submituploadList">确定</el-button>
        <el-button type="primary" @click="importVisible = false">取消</el-button>
      </div>
    </el-dialog>
    
  </my-container>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import MyContainer from "@/components/my-container";
import { pageGetTbWarehouseAsync } from "@/api/inventory/prepack";
import { formatTime } from "@/utils/tools";
import { importScanReturnOrderWorkload, importScanReturnOrderWorkloadAfterSaleDetail, importScanReturnOrderWorkloadCancelReturnDetail, 
  getScanReturnOrderWorkload, exportScanReturnOrderWorkload, 
  getScanReturnOrderWorkloadAfterSaleDetail, exportScanReturnOrderWorkloadAfterSaleDetail, 
  exportScanReturnOrderWorkloadWarehouseDetail,
  getScanReturnOrderWorkloadCancelReturnDetail, exportScanReturnOrderWorkloadCancelReturnDetail
 } from '@/api/vo/ReturnOrderScan';

const tableColsList = [
  { istrue: true, width: '190', align: 'center', label: '日期', prop: 'operateDate' },
  { sortable: 'custom', istrue: true, width: '180', align: 'center', label: '仓库', prop: 'wmsName' },
  { sortable: 'custom', istrue: true, width: '180', align: 'center', label: '操作员', prop: 'operator' },
  { sortable: 'custom', istrue: true, width: '160', align: 'center', label: '售后', prop: 'afterSaleNum', style: "color:blue;cursor:pointer;", type: 'click', handle: (that, row) => that.showAfterSaleDrawer(row) },
  { sortable: 'custom', istrue: true, width: '160', align: 'center', label: '销退仓数量', prop: 'cancelWarehouseQty', style: "color:blue;cursor:pointer;", type: 'click', handle: (that, row) => that.onsummaryClick1(row) },
  { sortable: 'custom', istrue: true, width: '160', align: 'center', label: '销退仓金额', prop: 'cancelWarehouseTotalAmount', style: "color:blue;cursor:pointer;", type: 'click', handle: (that, row) => that.onsummaryClick1(row) },
  { sortable: 'custom', istrue: true, width: '160', align: 'center', label: '次品仓数量', prop: 'defectiveWarehouseQty', style: "color:blue;cursor:pointer;", type: 'click', handle: (that, row) => that.onsummaryClick2(row) },
  { sortable: 'custom', istrue: true, width: '160', align: 'center', label: '次品仓金额', prop: 'defectiveWarehouseTotalAmount', style: "color:blue;cursor:pointer;", type: 'click', handle: (that, row) => that.onsummaryClick2(row) },
  { sortable: 'custom', istrue: true, width: '160', align: 'center', label: '销退上架数量', prop: 'cancelReturnQty', style: "color:blue;cursor:pointer;", type: 'click', handle: (that, row) => that.showCancelReturnDrawer(row) },
  { sortable: 'custom', istrue: true, width: '160', align: 'center', label: '销退上架金额', prop: 'cancelReturnTotalAmount' },
];
const tableColsDetail = [
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '操作人', prop: 'operator' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '操作日期', prop: 'operateDate', formatter: (row) => formatTime(row.operateDate, 'YYYY-MM-DD') },
  { sortable: 'custom', istrue: true, width: '140', align: 'center', label: '商品编码', prop: 'goodsCode' },
  { sortable: 'custom', istrue: true, width: '140', align: 'center', label: '成本价', prop: 'costPrice' },
  { sortable: 'custom', istrue: true, width: '140', align: 'center', label: '售后单号', prop: 'afterSaleOrderId' },
  { sortable: 'custom', istrue: true, width: '140', align: 'center', label: '快递单号', prop: 'expressOrderId' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '数量', prop: 'qty', summaryEvent: true },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '合计金额', prop: 'totalAmount', summaryEvent: true },
  { sortable: 'custom', istrue: true, width: '240', align: 'center', label: '备注', prop: 'remark' },
];
const tableColsTotalDetail = [
  { sortable: 'custom', istrue: true, width: '140', align: 'center', label: '操作人', prop: 'operator' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '操作日期', prop: 'operateDate', formatter: (row) => formatTime(row.operateDate, 'YYYY-MM-DD') },
  { sortable: 'custom', istrue: true, width: '150', align: 'center', label: '商品编码', prop: 'goodsCode' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '数量', prop: 'qty' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '成本价', prop: 'costPrice' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '合计金额', prop: 'totalAmount' },
];
const tableColsCancelReturnDetail = [
  { sortable: 'custom', istrue: true, width: '140', align: 'center', label: '操作人', prop: 'operator' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '操作日期', prop: 'operateDate', formatter: (row) => formatTime(row.operateDate, 'YYYY-MM-DD') },
  { sortable: 'custom', istrue: true, width: '150', align: 'center', label: '商品编码', prop: 'goodsCode' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '数量', prop: 'qty' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '成本价', prop: 'costPrice' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '合计金额', prop: 'totalAmount' },
];

export default {
  name: "scanReturnOrderStatistics",
  components: { vxetablebase, MyContainer },
  data() {
    return {
      that: this,
      wareHouseList: [],
      listLoadingList: false,//列表
      tableColsList: tableColsList,
      tableDataList: [],
      totalList: 0,
      summaryarryList: null,

      listLoadingDetail: false,//统计明细
      tableColsDetail: tableColsDetail,
      tableDataDetail: [],
      totalDetail: 0,
      footerDataArray: [],
      
      listLoadingTotalDetail: false,//合计明细
      tableColsTotalDetail: tableColsTotalDetail,
      tableDataTotalDetail: [],
      totalTotalDetail: 0,
      summaryarryTotalDetail: null,

      listLoadingCancelReturnDetail: false,//销退上架明细
      tableColsCancelReturnDetail: tableColsCancelReturnDetail,
      tableDataCancelReturnDetail: [],
      totalCancelReturnDetail: 0,
      summaryarryCancelReturnDetail: null,

      detailVisible: false,//扫码退件工作量统计明细抽屉
      totalDetailVisible: false,//销退仓/次品仓合计明细弹窗
      cancelReturnDetailVisible: false,//销退上架明细弹窗

      timeRange: [],//操作日期
      filter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        //过滤条件
        startOperateDate: null,//起始操作日期
        endOperateDate: null,//结束操作日期
        operator: null,//操作员
        wmsIdList: [],//仓库ID
      },
      detailFilter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        //过滤条件
        startOperateDate: null,//其实操作日期
        endOperateDate: null,//结束操作日期
        operator: null,//操作员
        wmsIdList: [],//仓库ID
        warehouseRemark: null,//仓库备注
      },
      pickerOptions: {
        shortcuts: [{
        text: '前一天',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
          end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
          picker.$emit('pick', [start, end]);
        }
        }, {
        text: '近一周',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          end.setTime(end.getTime());
          picker.$emit('pick', [start, end]);
        }
        }, {
        text: '近一个月',
        onClick(picker) {
          const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
          const date2 = new Date(); date2.setDate(date2.getDate());
          picker.$emit('pick', [date1, date2]);
        }
        }]
      },

      uploadLoading: false,
      importVisible: false,
      exportQueryInfo: {
        file: null,//文件
      },
      importTitle: null,
      detailFileType: null,//区分导入售后/销退上架文件
    };
  },
  async mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getWareHouse();
      //默认当天日期
      let start = new Date();
      let end = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
      end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
      this.timeRange = [start, end];
      this.filter.startOperateDate = start;
      this.filter.endOperateDate = end;
      
      this.onSearchList();
    },
    async getWareHouse() {
      const params = {
        currentPage: 1,
        pageSize: 1000,
        orderBy: null,
        isAsc: false,
      };
      const { data: { list } } = await pageGetTbWarehouseAsync(params);
      this.wareHouseList = list;
      this.wareHouseList.forEach(a => {
          if (a.name == "义乌市昀晗供应链管理有限公司") 
            a.name = "--本仓--"
        }
      );
    },
    async changeTime(e) {
      this.filter.startOperateDate = e? e[0] : null;
      this.filter.endOperateDate = e? e[1] : null;
    },

    //列表
    async sortchangeList({ order, prop }) {
      if (prop) {
        this.filter.orderBy = prop;
        this.filter.isAsc = order.indexOf("descending") == -1 ? true : false;
        this.onSearchList();
      }
    },
    //每页数量改变
    SizechangeList(val) {
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.getList();
    },
    //当前页改变
    PagechangeList(val) {
      this.filter.currentPage = val;
      this.getList();
    },
    onSearchList() {
      //点击查询时才将页数重置为1
      this.filter.currentPage = 1;
      this.$refs.pagerList.setPage(1);
      this.getList();
    },
    async getList() {
      this.listLoadingList = true;
      let data, success;
      ({ data, success } = await getScanReturnOrderWorkload(this.filter));
      this.listLoadingList = false;
      if (success) {
        this.tableDataList = data.list;
        this.totalList = data.total;
        this.summaryarryList = data.summary;
      } else {
        this.$message.error("获取数据失败！");
      }
    },
    // 导出
    async onExportList() {
      this.listLoadingList = true;
      const res = await exportScanReturnOrderWorkload(this.filter);
      this.listLoadingList = false;
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute("download", '扫码退件工作量_' + new Date().toLocaleString() + '.xlsx');
      aLink.click();
    },

    //售后明细
    showAfterSaleDrawer(row) {
      this.detailVisible = true;
      this.detailFilter.startOperateDate = this.filter.startOperateDate;
      this.detailFilter.endOperateDate = this.filter.endOperateDate;
      this.detailFilter.operator = row.operator;
      this.detailFilter.wmsIdList = [row.wmsId];
      this.$nextTick(() => {
        this.onSearchDetail();
      });
    },
    async sortchangeDetail({ order, prop }) {
      if (prop) {
        this.detailFilter.orderBy = prop;
        this.detailFilter.isAsc = order.indexOf("descending") == -1 ? true : false;
        this.onSearchDetail();
      }
    },
    //每页数量改变
    SizechangeDetail(val) {
      this.detailFilter.currentPage = 1;
      this.detailFilter.pageSize = val;
      this.getDetailList();
    },
    //当前页改变
    PagechangeDetail(val) {
      this.detailFilter.currentPage = val;
      this.getDetailList();
    },
    onSearchDetail() {
      //点击查询时才将页数重置为1
      this.detailFilter.currentPage = 1;
      this.$refs.pagerDetail.setPage(1);
      this.getDetailList();
    },
    async getDetailList() {
      this.listLoadingDetail = true;
      let data, success;
      this.detailFilter.warehouseRemark = null;
      ({ data, success } = await getScanReturnOrderWorkloadAfterSaleDetail(this.detailFilter));
      this.listLoadingDetail = false;
      if (success) {
        this.tableDataDetail = data.list;
        this.totalDetail = data.total;
        this.footerDataArray= data.summary;
      } else {
        this.$message.error("获取数据失败！");
      }
    },
    // 导出
    async onExportDetail() {
      this.listLoadingDetail = true;
      const res = await exportScanReturnOrderWorkloadAfterSaleDetail(this.detailFilter);
      this.listLoadingDetail = false;
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute("download", '扫码退件工作量售后明细_' + new Date().toLocaleString() + '.xlsx');
      aLink.click();
    },

    //销退上架明细
    showCancelReturnDrawer(row) {
      this.cancelReturnDetailVisible = true;
      this.detailFilter.startOperateDate = this.filter.startOperateDate;
      this.detailFilter.endOperateDate = this.filter.endOperateDate;
      this.detailFilter.operator = row.operator;
      this.detailFilter.wmsIdList = [row.wmsId];
      this.$nextTick(() => {
        this.onSearchCancelReturnDetail();
      });
    },
    async sortchangeCancelReturnDetail({ order, prop }) {
      if (prop) {
        this.detailFilter.orderBy = prop;
        this.detailFilter.isAsc = order.indexOf("descending") == -1 ? true : false;
        this.onSearchCancelReturnDetail();
      }
    },
    //每页数量改变
    SizechangeCancelReturnDetail(val) {
      this.detailFilter.currentPage = 1;
      this.detailFilter.pageSize = val;
      this.getCancelReturnDetailList();
    },
    //当前页改变
    PagechangeCancelReturnDetail(val) {
      this.detailFilter.currentPage = val;
      this.getCancelReturnDetailList();
    },
    onSearchCancelReturnDetail() {
      //点击查询时才将页数重置为1
      this.detailFilter.currentPage = 1;
      this.$nextTick(() => {
        this.$refs.pagerCancelReturnDetail.setPage(1);
      });
      this.getCancelReturnDetailList();
    },
    async getCancelReturnDetailList() {
      this.listLoadingCancelReturnDetail = true;
      let data, success;
      this.detailFilter.warehouseRemark = null;
      ({ data, success } = await getScanReturnOrderWorkloadCancelReturnDetail(this.detailFilter));
      this.listLoadingCancelReturnDetail = false;
      if (success) {
        this.tableDataCancelReturnDetail = data.list;
        this.totalCancelReturnDetail = data.total;
        this.summaryarryCancelReturnDetail = data.summary;
      } else {
        this.$message.error("获取数据失败！");
      }
    },
    // 导出
    async onExportCancelReturnDetail() {
      this.listLoadingCancelReturnDetail = true;
      const res = await exportScanReturnOrderWorkloadCancelReturnDetail(this.detailFilter);
      this.listLoadingCancelReturnDetail = false;
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute("download", '扫码退件工作量销退上架明细_' + new Date().toLocaleString() + '.xlsx');
      aLink.click();
    },

    //销退仓、次品仓明细
    async sortchangeTotalDetail({ order, prop }) {
      if (prop) {
        this.detailFilter.orderBy = prop;
        this.detailFilter.isAsc = order.indexOf("descending") == -1 ? true : false;
        this.onSearchTotalDetail();
      }
    },
    //每页数量改变
    SizechangeTotalDetail(val) {
      this.detailFilter.currentPage = 1;
      this.detailFilter.pageSize = val;
      this.getTotalDetailList();
    },
    //当前页改变
    PagechangeTotalDetail(val) {
      this.detailFilter.currentPage = val;
      this.getTotalDetailList();
    },
    onSearchTotalDetail() {
      //点击查询时才将页数重置为1
      this.detailFilter.currentPage = 1;
      this.$nextTick(() => {
        this.$refs.pagerTotalDetail.setPage(1);
      });
      
      this.getTotalDetailList();
    },
    async getTotalDetailList() {
      this.listLoadingTotalDetail = true;
      let data, success;
      ({ data, success } = await getScanReturnOrderWorkloadAfterSaleDetail(this.detailFilter));
      this.listLoadingTotalDetail = false;
      if (success) {
        this.tableDataTotalDetail = data.list;
        this.totalTotalDetail = data.total;
        this.summaryarryTotalDetail = data.summary;
      } else {
        this.$message.error("获取数据失败！");
      }
    },
    // 导出
    async onExportTotalDetail() {
      this.listLoadingTotalDetail = true;
      const res = await exportScanReturnOrderWorkloadWarehouseDetail(this.detailFilter);
      this.listLoadingTotalDetail = false;
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute("download", this.detailFilter.warehouseRemark + '扫码退件工作量售后明细_' + new Date().toLocaleString() + '.xlsx');
      aLink.click();
    },
    onCloseDialog() {
      this.totalDetailVisible = false;
    },
    //售后明细弹窗内部：合计栏点击事件
    async onsummaryClick(row) {
      this.totalDetailVisible = true;
      this.detailFilter.warehouseRemark = row.operator;
      this.onSearchTotalDetail();
    },
    //列表：销退仓数量、销退仓金额点击事件
    async onsummaryClick1(row) {
      this.detailFilter.startOperateDate = this.filter.startOperateDate;
      this.detailFilter.endOperateDate = this.filter.endOperateDate;
      this.detailFilter.operator = row.operator;
      this.detailFilter.wmsIdList = [row.wmsId];
      
      this.totalDetailVisible = true;
      this.detailFilter.warehouseRemark = "销退仓合计";
      this.onSearchTotalDetail();
    },
    //列表：次品仓数量、次品仓金额点击事件
    async onsummaryClick2(row) {
      this.detailFilter.startOperateDate = this.filter.startOperateDate;
      this.detailFilter.endOperateDate = this.filter.endOperateDate;
      this.detailFilter.operator = row.operator;
      this.detailFilter.wmsIdList = [row.wmsId];
      
      this.totalDetailVisible = true;
      this.detailFilter.warehouseRemark = "次品仓合计";
      this.onSearchTotalDetail();
    },

    
    // //打开上传列表弹窗
    openExport(type) {
      this.fileList = []
      if (type == 'list') {
        this.importTitle = '扫码退件工作量统计列表导入';
      } else {
        this.importTitle = '扫码退件工作量统计明细导入';
      }
      this.importVisible = true;
    },
    //上传文件
    async uploadFileList(item) {
      const form = new FormData();
      form.append("file", item.file);
      this.exportQueryInfo.file = form;

      this.detailFileType = item.file.name;
      
    },
    //移除上传文件
    uploadRemoveList() {
      this.exportQueryInfo.file = null;
    },
    //提交上传文件
    async submituploadList() {
      if (!this.exportQueryInfo.file) {
        this.$message({ message: '请上传文件', type: "error" });
        return;
      }
      if (this.importTitle == '扫码退件工作量统计列表导入') {
        const { success } = await importScanReturnOrderWorkload(this.exportQueryInfo.file);
        if (success) {
          this.$message({ message: '数据上传成功,正在导入中...', type: "success" });
          this.importVisible = false;
        } else {
          this.$message({ message: '上传失败', type: "error" });
        }
      } else if (this.detailFileType.indexOf("售后") != -1) {
        const { success } = await importScanReturnOrderWorkloadAfterSaleDetail(this.exportQueryInfo.file);
        if (success) {
          this.$message({ message: '数据上传成功,正在导入中...', type: "success" });
          this.importVisible = false;
        } else {
          this.$message({ message: '上传失败', type: "error" });
        }
      } else if (this.detailFileType.indexOf("销退上架") != -1) {
        const { success } = await importScanReturnOrderWorkloadCancelReturnDetail(this.exportQueryInfo.file);
        if (success) {
          this.$message({ message: '数据上传成功,正在导入中...', type: "success" });
          this.importVisible = false;
        } else {
          this.$message({ message: '上传失败', type: "error" });
        }
      } else {
        this.$message({ message: '请上传正确的文件', type: "error" });
      }
    },
    
  }
};

</script>
