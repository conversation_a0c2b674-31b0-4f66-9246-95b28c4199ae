<template>
    <my-container>
      <!-- 顶部操作/宝贝ID -->
      <div>
        <el-form class="ad-form-query" :inline="true" :model="proCodeFilter" @submit.native.prevent>
          <el-form-item label="审核时间：">
            <el-date-picker style="width: 280px" v-model="operatorTimeRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" 
              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" 
              @change="changeOperatorTime" unlink-panels>
            </el-date-picker>
          </el-form-item>
          <el-form-item label="解决时间：">
            <el-date-picker style="width: 280px" v-model="solveTimeRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" 
              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" 
              @change="changeSolveTime" unlink-panels>
            </el-date-picker>
          </el-form-item>
          <el-form-item label="" >
            <el-select v-model="proCodeFilter.directorGroupIdList" placeholder="小组" class="el-select-content" filterable multiple clearable collapse-tags>
              <el-option v-for="item in directoList" :key="item.key" :label="item.value" :value="item.key" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="proCodeFilter.DirectorUserNameList" placeholder="运营" class="el-select-content" filterable multiple clearable collapse-tags>
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.label" />
            </el-select>
            <!-- <el-input v-model.trim="proCodeFilter.directorUserName" placeholder="运营" style="width:120px" maxlength="20" clearable /> -->
          </el-form-item>
          <el-form-item>
            <el-select v-model="proCodeFilter.reasonForRefund" placeholder="退款原因" class="el-select-content" filterable multiple clearable collapse-tags>
              <el-option v-for="item in reasonForRefundList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="proCodeFilter.isSolved" placeholder="是否解决" class="el-select-content" clearable>
              <el-option key="是" label="是" value=1 />
              <el-option key="否" label="否" value=0 />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input v-model.trim="proCodeFilter.solver" placeholder="解决人" style="width:120px" maxlength="20" clearable />
          </el-form-item>
          <el-button-group>
            <inputYunhan title="请分行输入宝贝ID" placeholder="宝贝ID/若输入多条请按回车" :maxRows="300" :inputshow="0" :clearable="true" 
              @callback="proCodeCallback" :inputt.sync="proCodeFilter.proId">
            </inputYunhan>
          </el-button-group>
          <el-form-item>
            <el-input v-model.trim="proCodeFilter.styleCode" placeholder="系列编码" style="width:120px" maxlength="20" clearable />
          </el-form-item>
          <el-form-item>
            <el-col :span="11">
              <el-input placeholder="ID评分大于" v-model="proCodeFilter.minScore" maxlength="4" clearable @input="validateNumber($event, 'minScore')">
              </el-input>
            </el-col>
            <el-col class="line" :span="2">至</el-col>
            <el-col :span="11">
              <el-input placeholder="ID评分小于" v-model="proCodeFilter.maxScore" maxlength="4" clearable @input="validateNumber($event, 'maxScore')">
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-col :span="11">
              <el-input placeholder="编码评分大于" v-model="proCodeFilter.minSeriesNameScore" maxlength="4" clearable @input="validateNumber($event, 'minSeriesNameScore')">
              </el-input>
            </el-col>
            <el-col class="line" :span="2">至</el-col>
            <el-col :span="11">
              <el-input placeholder="编码评分小于" v-model="proCodeFilter.maxSeriesNameScore" maxlength="4" clearable @input="validateNumber($event, 'maxSeriesNameScore')">
              </el-input>
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch2">查询</el-button>
            <el-button type="primary" @click="onExport2">导出</el-button>
            <el-button type="primary" @click="onSolverAdd">解决</el-button>
            <el-button type="primary" @click="onSolutionAdd">方案填写</el-button>
            <el-button type="primary" @click="onEditSolute" v-if="checkPermission('api:operatemanage:operate:EditAfterSalesProCodeSoluteData')">修改解决人</el-button>
          </el-form-item>
        </el-form>
      </div>
      <!-- 列表 -->
      <div style="height: 87%;">
        <vxetablebase  :id="'Salesinth202412151316'" ref="table3" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' 
          @sortchange='sortchangeProCode' :tableData='tableDataProCode' :tableCols='tableColsProCode' :isSelection="false" :isSelectColumn="false" 
          style="width: 100%;margin: 0" v-loading="listLoading" :height="'100%'" @select="selectchange">
        </vxetablebase>
      </div>
      <!--分页-->
      <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template>

      <el-dialog :visible.sync="solverVisable" width="400px" height="40%" @closeDialog="closeSolverDialog"
        v-dialogDrag @close="closeSolverDialog">
        <div style="text-align: center; margin-bottom: 20px;">问题解决</div>
        <div style="text-align: center; margin-bottom: 20px;">
          <el-select v-model="solver" placeholder="请选择解决人" clearable filterable >
            <el-option label="运营" value="运营"></el-option>
            <el-option v-for="item in solutionList" :key="item" :label="item" :value="item"></el-option>
          </el-select>
        </div>
        <div style="text-align: center; margin-bottom: 20px;">
          <el-button @click="solverSubmit">确认</el-button>
        </div>
      </el-dialog>

      <el-dialog :visible.sync="solutionVisable" width="400px" height="40%" @closeDialog="closeSolutionDialog"
        v-dialogDrag @close="closeSolutionDialog">
        <div style="text-align: center; margin-bottom: 20px;">方案填写</div>
        <div style="text-align: center; margin-bottom: 20px;"><el-input type="textarea" v-model="solution" :rows="10" placeholder="请输入解决方案" maxlength="500" show-word-limit></el-input></div>
        <div style="text-align: center; margin-bottom: 20px;"><el-button @click="solutionSubmit">确认</el-button></div>
      </el-dialog>
      
      <el-dialog :visible.sync="editSoluteVisable" width="400px" height="40%" @closeDialog="closeEditSoluteDialog"
        v-dialogDrag @close="closeEditSoluteDialog">
        <div style="text-align: center; margin-bottom: 20px;">修改数据</div>
        <div style="text-align: center; margin-bottom: 20px;">
          <el-select v-model="solver" clearable filterable  placeholder="请选择解决人" ><!-- @change="handleSelectionChange" -->
            <el-option label="空白" value="空白"></el-option>
            <el-option label="运营" value="运营"></el-option>
            <el-option v-for="item in solutionList" :key="item" :label="item" :value="item"></el-option>
          </el-select>
        </div>
        <div style="text-align: center; margin-bottom: 20px;">
          <el-input type="textarea" v-model="solution" :rows="10" placeholder="请输入解决方案" maxlength="500" show-word-limit ></el-input>
        </div>
        <div style="text-align: center"><el-button @click="editSoluteSubmit">确认</el-button></div>
      </el-dialog>

    </my-container>
  </template>
  <script>
  import MyContainer from "@/components/my-container";
  import {
    getAfterSalesReasonStaticsProCode,
    addAfterSalesProCodeSolver,
    addAfterSalesProCodeSolution,
    editAfterSalesProCodeSoluteData,
    reasonForRefundProCodeStatisticsExport
  } from "@/api/operatemanage/OperationalMiddleOfficeManage/ServiceChatHistory";
  import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
  import inputYunhan from "@/components/Comm/inputYunhan";
  import { getDirectorGroupList, getDirectorList } from "@/api/operatemanage/base/shop.js";
  import { getClaimUserList } from "@/api/operatemanage/OperationalMiddleOfficeManage/BrushOrderProcess";

  const tableColsProCode = [
    { istrue: true, width: '60', align: 'center', type: "checkbox" },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', prop: 'styleCode', label: '系列编码', },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', prop: 'seriesNameScore', label: '系列编码DSR', },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', prop: 'proId', label: '宝贝ID', },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', prop: 'score', label: 'IDDSR', },
    { sortable: 'custom', istrue: true, width: '120', align: 'center', prop: 'reasonForRefund', label: '退款原因', },
    { sortable: 'custom', istrue: true, width: '90', align: 'center', prop: 'directorGroupUserName', label: '小组', },
    { sortable: 'custom', istrue: true, width: '90', align: 'center', prop: 'directorUserName', label: '运营', },
    { sortable: 'custom', istrue: true, width: '90', align: 'center', prop: 'groupAssistanter', label: '助理', },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', prop: 'exitAllNum', label: '出现次数(总)', },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', prop: 'exitNum', label: '出现次数', },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', prop: 'exitPercentage2', label: '出现占比', },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', prop: 'afterSolveExitNum', label: '解决后出现次数', },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', prop: 'isSolved', label: '是否解决', formatter: (row) => { return row.isSolved == 1 ? "是" : "否" } },
    { sortable: 'custom', istrue: true, width: '90', align: 'center', prop: 'solver', label: '解决人', },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', prop: 'solution', label: '解决方案' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', prop: 'sloveTime', label: '解决时间', },
  ];
  const reasonForRefundList = ["运营问题/虚假宣传", "运营问题/编码问题", "运营问题/低价引流问题", "仓库问题/订单错漏发", "仓库问题/包装问题", "仓库问题/定制问题", "售前问题/推荐错误", "售前问题/过度承诺", "售前问题/漏备注或备注错", "快递问题", "采购问题", "售后问题", "产品质量问题"]

  export default {
    name: "afterSales",
    components: {
      MyContainer,
      vxetablebase,
      inputYunhan
    },
    data() {
      return {
        that: this,
        total: 0,////宝贝ID维度
        summaryarry: null,
        pager: { orderBy: "", isAsc: false },
        listLoading: false,
        pickerOptions: {
          disabledDate(date) {
            // 设置禁用日期
            const start = new Date("1970/1/1");
            const end = new Date("9999/12/31");
            return date < start || date > end;
          },
        },
        tableDataProCode: [],//宝贝ID维度
        // totalProCode: 0,//宝贝ID维度
        tableColsProCode: tableColsProCode,
        // pager2: { orderBy: "", isAsc: false },
        proCodeFilter: {
          currentPage: 1,
          pageSize: 50,
          orderBy: null,
          isAsc: false,
          //过滤条件
          operatorTimeStart: null,//审核开始日期
          operatorTimeEnd: null,//审核结束日期
          solveTimeStart: null,//解决开始时间
          solveTimeEnd: null,//解决结束时间
          directorGroupIdList: [],//运营人员组
          // directorUserIdList: [],//运营人员
          DirectorUserNameList: [],//运营人员
          reasonForRefund: [],//退款原因
          minScore: null,//最小ID评分
          maxScore: null,//最大ID评分
          minSeriesNameScore: null,//最小系列编码评分
          maxSeriesNameScore: null,//最大系列编码评分
          isSolved: null,//是否解决
          solver: null,//解决人
          proId: "",//宝贝ID
          styleCode: "",//系列编码
        },
        operatorTimeRange: [],//审核时间
        solveTimeRange: [],//解决时间
        directoList: [],//组长
        directorlist: [],//运营
        reasonForRefundList: reasonForRefundList,
        solutionList: [],//解决人：【运营】+ 拼多多所有中台人员名称
        selectList: [],//复选框选中数据
        solverVisable: false,//问题解决人
        solutionVisable: false,//问题解决方案
        editSoluteVisable: false,//修改数据
        solver: null,//解决人
        solution: null,//解决方案
      };
    },
    async mounted() {
      await this.init();
      await this.onSearch2();
    },
    methods: {
      async init(){
        //小组
        const directo = await getDirectorGroupList();
        this.directoList = directo.data;

        //运营：专员+助理
        var res3 = await getDirectorList();
        this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });

        // 分配人-拼多多运营中台
        var { data } = await getClaimUserList();
        //解决人：【运营】+ 拼多多所有中台人员名称
        // this.solutionList = res3.data?.map(item => item.value).concat(data4);
        this.solutionList = data;
      },
      changeOperatorTime(e) {
        this.proCodeFilter.operatorTimeStart = e? e[0] : null;
        this.proCodeFilter.operatorTimeEnd = e? e[1] : null;
      },
      changeSolveTime(e) {
        this.proCodeFilter.solveTimeStart = e? e[0] : null;
        this.proCodeFilter.solveTimeEnd = e? e[1] : null;
      },
      // 每页数量改变
      Sizechange(val) {
        this.listLoading = true;
        this.proCodeFilter.currentPage = 1;
        this.proCodeFilter.pageSize = val;
        this.getList();
        this.listLoading = false;
      },
      // 当前页改变
      Pagechange(val) {
        this.proCodeFilter.currentPage = val;
        this.getList();
      },
      sortchangeProCode(column) {
        if (!column.order) {
          this.pagerProCode = {};
        } else {
          this.pagerProCode = {
            orderBy: column.prop,
            isAsc: column.order.indexOf("descending") == -1 ? true : false,
          };
        }
        this.proCodeFilter.orderBy = column.prop;
        this.proCodeFilter.isAsc = column.order.indexOf("descending") == -1 ? true : false;
        this.onSearch2();
      },
      // 查询
      onSearch2() {
        //点击查询时才将页数重置为1
        this.proCodeFilter.currentPage = 1;
        this.$refs.pager.setPage(1);
        this.getList();
      },
      async getList() {
        this.listLoading = true;
        var pager = this.$refs.pager.getPager();
        const para = { ...this.proCodeFilter };
        const params = {
          ...pager,
          ...this.pager,
          ...para,
        };
        
        // if (!this.proCodeFilter.minScore) {
        //     params.minScore = 0;
        // }
        // if (!this.proCodeFilter.maxScore) {
        //     params.maxScore = 5;
        // }
        // if (!this.proCodeFilter.minSeriesNameScore) {
        //     params.minSeriesNameScore = 0;
        // }
        // if (!this.proCodeFilter.maxSeriesNameScore) {
        //     params.maxSeriesNameScore = 5;
        // }
        let data, success;
        // ({ data, success } = await getAfterSalesReasonStaticsProCode(this.proCodeFilter));
        ({ data, success } = await getAfterSalesReasonStaticsProCode(params));
        this.listLoading = false;
        if (success) {
            this.tableDataProCode = data.list;
            this.total = data.total;
        } else {
            this.$message.error("获取数据失败！");
        }
        
      },
      proCodeCallback(val) {
        let val1 = val.replace(/[^a-zA-Z0-9()\u4e00-\u9fa5]/g, ',');
        let val2 = val1.split(',');
        this.proCodeFilter.proId = val2.join(',');
      },
      validateNumber(value, field) {
        let numberValue = value.replace(/[^0-9.]/g, '');  // 只保留数字和小数点

        // 确保小数点只能出现一次，限制小数点后最多只能有两位数字
        if (numberValue.indexOf('.') !== -1) {
          numberValue = numberValue.replace(/(\.\d{2})\d+/g, '\$1');
        }
        if (numberValue && parseFloat(numberValue) < 0) {
          numberValue = '';
        }

        // 更新绑定的数据
        this.proCodeFilter[field] = numberValue;
      },
      // 复选框数据
      selectchange:function(rows,row) {
          this.selectList = [];
          rows.forEach(f => {
              this.selectList.push(f);
          });
      },
      //打开解决人
      async onSolverAdd() {
        this.solver = null;
        if (this.selectList.length == 0) {
          this.$message.warning("请选中至少一条列表数据!");
          return;
        }
        var IsExitData = this.selectList.filter(a => a.solver != null);
        if (null != IsExitData && IsExitData.length > 0) {
          this.$message.warning("已有解决人的数据不可点击解决!");
          return;
        }
        this.solverVisable = true;
      },
      //取消解决人
      closeSolverDialog() {
        this.solverVisable = false;
      },
      //提交解决人
      async solverSubmit() {
        const para = { ...this.selectList };
        // var pager2 = this.$refs.pager2.getPager();
        const params = {
          // ...pager2,
          // ...this.pager2,
          // ...para,
          data: this.selectList,
          solver: this.solver
        };

        // this.$refs.solver.validate(async valid => {
        //   if (valid) {
            const res = await addAfterSalesProCodeSolver(params);
            if (res.success) {
              this.$message.success('解决人设置成功！');
              this.solverVisable = false;
              this.listLoading = false;
              this.getList();
            }
        //     else {
        //       this.$message.error('问题解决失败！');
        //     }
        //   }
        // });
        // this.selectList = [];
      },
      //打开问题解决-方案填写
      onSolutionAdd() {
        this.solution = null;
        if (this.selectList.length == 0) {
          this.$message.warning("请选中至少一条列表数据!");
          return;
        }
        var IsExitSolverData = this.selectList.filter(a => a.solver != null);
        if (IsExitSolverData.length == 0 || IsExitSolverData.length < this.selectList.length) {
          this.$message.warning("无解决人的数据不可点击方案填写!");
          return;
        }
        var IsExitSolutionData = this.selectList.filter(a => a.solution != null);
        if (IsExitSolutionData.length > 0) {
          this.$message.warning("已有解决方案的数据不可点击方案填写!");
          return;
        }
        this.solutionVisable = true;
      },
      //取消解决方案
      closeSolutionDialog() {
        this.solutionVisable = false;
      },
      //提交解决方案
      async solutionSubmit() {
        const para = { ...this.selectList };
        // var pager2 = this.$refs.pager2.getPager();
        const params = {
          // ...pager2,
          // ...this.pager2,
          // ...para,
          data: this.selectList,
          solution: this.solution
        };
        // this.$refs.solution.validate(async valid => {
        //   if (valid) {
            const res = await addAfterSalesProCodeSolution(params);
            if (res.success) {
              this.$message.success('解决方案填写成功！');
              this.solutionVisable = false;
              this.listLoading = false;
              this.getList();
            }
        //     else {
        //       this.$message.error('解决方案填写失败！');
        //     }
        //   }
        // });
        // this.selectList = [];
      },
      //打开修改数据
      onEditSolute() {
        this.solver = null;
        this.solution = null;
        if (this.selectList.length == 0) {
          this.$message.warning("请选中至少一条列表数据!");
          return;
        }
        var IsExitSolverData = this.selectList.filter(a => a.solver == null);
        var IsExitSolutionData = this.selectList.filter(a => a.solution == null);
        if (IsExitSolverData.length > 0 || IsExitSolutionData.length > 0) {
          this.$message.warning("只能对已有解决人和有解决方案数据进行操作!");
          return;
        }
        else {
          this.editSoluteVisable = true;
        }
      },
      //取消修改数据
      closeEditSoluteDialog() {
        this.editSoluteVisable = false;
      },
      //提交修改数据
      async editSoluteSubmit(){
        const para = { ...this.selectList };
        // var pager2 = this.$refs.pager2.getPager();
        const params = {
          // ...pager2,
          // ...this.pager2,
          // ...para,
          data: this.selectList,
          solver: this.solver,
          solution: this.solution
        };
        console.log(this.solver);
        console.log(this.solution);
        if (null == this.solver && null == this.solution) {
          this.$message.warning("请至少选择解决人或解决方案!");
        }
        if (this.solver == "空白") {
            this.$confirm('解决人选择"空白"将清空解决信息，确定要清空吗？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              editAfterSalesProCodeSoluteData(params).then(response => {
                if (response.success) {
                  this.$message.success('解决数据修改成功！');
                  this.editSoluteVisable = false;
                  this.listLoading = false;
                  this.getList();
                }
              })
            }).catch(() => {
              this.$message.info("已取消!");
            })
        }
        // const res = await editAfterSalesProCodeSoluteData(params);
        // if (res.success) {
        //   this.$message.success('解决数据修改成功！');
        //   this.editSoluteVisable = false;
        //   this.listLoading = false;
        //   this.getList();
        // }
        // this.selectList = [];
      },
      // //选中"空白"清空数据
      // handleSelectionChange(val) {
      //   if (val == "空白") {
      //     this.solver = null;
      //     this.solution = null;
      //   }
      // },
      //导出
      async onExport2() {
        var pager = this.$refs.pager.getPager();
        const para = { ...this.proCodeFilter };
        const params = {
          ...pager,
          ...this.pager,
          ...para,
        };
        // if (!this.proCodeFilter.minScore) {
        //   params.minScore = 0;
        // }
        // if (!this.proCodeFilter.maxScore) {
        //   params.maxScore = 5;
        // }
        // if (!this.proCodeFilter.minSeriesNameScore) {
        //   params.minSeriesNameScore = 0;
        // }
        // if (!this.proCodeFilter.maxSeriesNameScore) {
        //   params.maxSeriesNameScore = 5;
        // }
        // const res = await reasonForRefundProCodeStatisticsExport(params);
        const res = await reasonForRefundProCodeStatisticsExport(this.proCodeFilter);
        // if (!res?.data) return;
        const aLink = document.createElement('a');
        let blob = new Blob([res], { type: 'application/vnd.ms-excel' });
        aLink.href = URL.createObjectURL(blob);
        aLink.setAttribute("download", '售后数据统计(宝贝ID维度)_' + new Date().toLocaleString() + '.xlsx');
        aLink.click();
      },
    },
  };
  </script>
  <style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
      background-color: #fff;
    }
    
    ::v-deep .inner-container::-webkit-scrollbar {
      display: none;
    }
    
    ::v-deep .mycontainer {
      position: relative;
    }
    
    .uptime {
      font-size: 14px;
      position: absolute;
      right: 30px;
    }
    //解决下拉菜单多选由文字太长导致样式问题
    ::v-deep .el-select__tags-text {
      max-width: 45px;
    }
  </style>
  