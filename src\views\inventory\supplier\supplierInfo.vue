<template>
  <container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="供应商编码">
          <el-input v-model.trim="filter.code" style="width: 120px" :clearable="true" placeholder="供应商编码" />
        </el-form-item>
        <el-form-item label="供应商名称">
          <el-input v-model.trim="filter.name" style="width: 150px" :clearable="true" placeholder="供应商名称" />
        </el-form-item>
        <el-form-item label="发货地">
          <el-cascader class="publicCss cascaderCss" style="width: 300px" collapse-tags clearable :options="options1"
            placeholder="请选择发货地" :props="{ multiple: true, filterable: true, checkStrictly: true }"
            v-model="filter.provinceCityDistricts1" filterable></el-cascader>
        </el-form-item>
        <el-form-item label="采购员">
          <el-select v-model="filter.brandId" collapse-tags clearable filterable placeholder="请选择采购员"
            style="width: 200px">
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="付款方式">
          <el-select v-model="filter.payType" placeholder="请选择状态" style="width: 100px">
            <el-option label="所有" value />
            <el-option label="转账" value="转账" />
            <el-option label="1688" value="1688" />
            <el-option label="月结" value="月结" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否能代发">
          <el-select v-model="filter.isProxyShipping" placeholder="是否能代发" style="width: 80px;" clearable @change="(e) => {
            if (e == 0) {
              filter.printOrderPlatform = null
            }
          }">
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
            <el-option label="未绑定" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="打单平台">
          <el-input v-model.trim="filter.printOrderPlatform" placeholder="打单平台名称" maxlength="50" clearable
            style="width: 150px;" :disabled="filter.isProxyShipping === 0" />
        </el-form-item>
        <el-form-item label="供应商发货地">
          <el-select v-model="filter.isShippingLocationConsistent" placeholder="供应商发货地" multiple collapse-tags clearable style="width: 130px">
            <el-option key="一致" label="一致" value="一致"></el-option>
            <el-option key="不一致" label="不一致" value="不一致"></el-option>
            <el-option key="无法匹配" label="无法匹配" value="无法匹配"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="采购时间">
          <el-date-picker style="width: 310px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" :clearable="false" range-separator="至" @change="purchaseDateChange"
            start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"></el-date-picker>
        </el-form-item>
        <el-form-item label="是否能开票">
          <el-select v-model="filter.isOpenInvoice" clearable filterable placeholder="是否能开票" style="width: 100px">
            <el-option value="是" label="是" />
            <el-option value="否" label="否" />
          </el-select>
        </el-form-item>
        <el-form-item label="货款对公/对私">
          <el-select v-model="filter.supplierIsOfPublicPay" clearable filterable placeholder="货款对公/对私" style="width: 100px">
            <el-option value="货款对公" label="货款对公" />
            <el-option value="货款对私" label="货款对私" />
          </el-select>
        </el-form-item>
        <el-form-item label="发票类型">
          <el-select v-model="filter.invoiceType" clearable filterable placeholder="发票类型" style="width: 100px">
            <el-option value="普票" label="普票" />
            <el-option value="专票" label="专票" />
          </el-select>
        </el-form-item>
        <el-form-item label="税点类型">
          <el-select v-model="filter.supplierTaxRateCondition" clearable filterable placeholder="税点类型" style="width: 80px" @change="taxTypeChange">
            <el-option value="大于" label="大于" />
            <el-option value="等于" label="等于" />
            <el-option value="小于" label="小于" />
            <el-option value="介于" label="介于" />
          </el-select>
          <el-input-number :controls="false" :min="0.1" :max="999" :precision="1"
              :placeholder="filter.supplierTaxRateCondition == '介于' ? '税点(%)最小值' : '税点(%)'" style="width: 80px" v-model="filter.supplierMinTaxRate" />
          <el-input-number :controls="false" :min="0.1" :max="999" :precision="1" v-show="filter.supplierTaxRateCondition == '介于'"
              placeholder="税点(%)最大值" style="width: 80px" v-model="filter.supplierMaxTaxRate" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>

    <ces-table ref="table" :that='that' :isIndex='true' @sortchange='sortchange' @select='selectchange'
      :isSelectColumn='true' :tableData='list' :tableCols='tableCols' :tableHandles="tableHandles"
      :loading="listLoading" :showsummary='true' :summaryarry='summaryarry' height="95%">

      <template slot="extentbtn">
        <el-switch :width="40" @change="changeingroup" v-model="filter.isNonPurchaseOrder" inactive-color="#228B22"
          active-text="不含采购单" inactive-text="含采购单">
        </el-switch>
      </template>
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <el-dialog :visible.sync="dialogAddSupVisible" v-dialogDrag width="80%">
      <div style="height:600px;">
        <addsupplier ref="addsupplierdetail" @addcallback="addCallBack" style="height:100%;" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogAddSupVisible = false">关闭</el-button>
        <el-button type="primary" @onSearch="onSearch" @click="onSaveAddSupDetail">保存</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="dialogAddSupSymbolVisible" v-dialogDrag width="50%">
      <div style="height:400px;">
        <el-form>
          <el-row>
            <el-col>供应商名称标点符号白名单</el-col>
            <el-col>
              <el-input ref="regioninput" v-model.trim="supSymbol" placeholder="请输入标点符号" maxlength="1" clearable
                style="width: 80%;" />
            </el-col>
          </el-row>
          <el-row>
            <el-col style="margin-top: 10px;">
              <el-button type="primary" @click="onSaveSupplierSymbol">添加白名单</el-button>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <div style="border: 1px solid #dcdfe6;border-radius: 5px;height: 150px;margin: 10px 0;">
                <el-scrollbar style="height: 100%;">
                  <el-tag v-for="(item, i) in symbolList" :key="item" closable style="margin: 5px;"
                    @close="handleClose(item, i)">
                    {{ item }}
                  </el-tag>
                </el-scrollbar>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogSupVisible" v-dialogDrag width="80%">
      <div style="height:600px;">
        <supplierdetail ref="supplierdetail" @onSearch="onSearch" style="height:100%;" :supplier_id="supplier_id"
          v-if="dialogSupVisible" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="checkPermission('Api:Inventory:Supplier:SyncJstSupplier')" type="primary"
          @click="onSynchronizedPool">同步聚水潭</el-button>
        <el-button @click="dialogSupVisible = false">关闭</el-button>
        <el-button type="primary" @click="onSaveSupDetail">保存</el-button>
      </span>
    </el-dialog>
    <!-- v-dialogDrag -->
    <el-dialog :visible.sync="dialogPurchaseVisible" v-dialogDrag width="80%">
      <div style="height:600px;">
        <supplierpurchase ref="supplierpurchase" style="height:100%;" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogPurchaseVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="supInfoVisible" v-dialogDrag width="70%" :title="supInfo.name">
      <supplierDtl v-if="supInfoVisible" :supInfo="supInfo" />
    </el-dialog>

    <el-dialog :visible.sync="supplierFromInfo.visible" v-dialogDrag width="50%" title="货款对公/对私/税点变更日志">
      <supplierLog v-if="supplierFromInfo.visible" :data="supplierFromInfo.data" />
    </el-dialog>
  </container>
</template>
<script>
import { pageSupplier, exportSupplier, getSupplierNameWhitelist, saveSupplierNameWhitelist, delSupplierNameWhitelist } from '@/api/inventory/supplier'
import { upLoadImage } from '@/api/upload/file'
import { getAllProBrand } from '@/api/inventory/warehouse'
import { getRegion } from '@/api/admin/business'
import { formatTime, formatYesornoBool, formatWarehouseArea, formatNoLink } from "@/utils/tools";
import { ruleExpressComanycode } from '@/utils/formruletools'
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import logistics from '@/components/Comm/logistics'
import supplierdetail from '@/views/inventory/supplier/supplierdetail'
import addsupplier from '@/views/inventory/supplier/supplierdetailadd'
import supplierpurchase from '@/views/inventory/supplier/supplierpurchase'
import { GetProvinceCityDistrictAndNo } from '@/api/inventory/purchase'
import supplierDtl from './supplierDtl.vue'
import supplierLog from './supplierLog.vue'
import dayjs from 'dayjs'
const tableCols = [
  { istrue: true, prop: 'code', label: '供应商编码', width: '170', sortable: 'custom' },
  { istrue: true, prop: 'name', label: '供应商名称/简称', width: '220', type: 'click', handle: (that, row) => that.openSupplierDtl(row) },//type:'html',formatter:(row)=>formatNoLink(row.name)},
  { istrue: true, prop: 'province', label: '发货地址/省', width: '130', sortable: 'custom', formatter: (row) => { return row.provinceName ?? '' } },
  { istrue: true, prop: 'city', label: '发货地址/市', width: '130', sortable: 'custom', formatter: (row) => { return row.cityName ?? '' } },
  { istrue: true, prop: 'districts', label: '发货地址/区', width: '130', sortable: 'custom', formatter: (row) => { return row.districtsName ?? '' } },
  { istrue: true, prop: 'brandNamesHtml', label: '采购员', width: '200', type: 'html' },
  { istrue: true, prop: 'styleCode', label: '款式编码', width: '120' },
  { istrue: true, prop: 'isOpenInvoice', label: '是否能开票', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'supplierIsOfPublicPay', label: '货款对公/对私', width: '80', sortable: 'custom', type: 'click', handle: (that, row) => that.onLogMethod(row) },
  { istrue: true, prop: 'invoiceType', label: '发票类型', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'supplierTaxRate', label: '税点', width: '80', sortable: 'custom', type: 'click', handle: (that, row) => that.onLogMethod(row), formatter:(row)=> row.supplierTaxRate == 0 ? '0%' : row.supplierTaxRate ? row.supplierTaxRate + "%" : "" },
  { istrue: true, prop: 'isProxyShipping', label: '是否能代发', width: '120', sortable: 'custom', formatter: (row) => row.isProxyShipping == true ? '是' : row.isProxyShipping == false ? '否' : '' },
  { istrue: true, prop: 'printOrderPlatform', label: '打单平台', width: '120', sortable: 'custom', },
  { istrue: true, prop: 'totalCS', label: '累计采购次数', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'totalAmont', label: '累计采购金额', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'payType', label: '付款方式', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'firstPurchaseDate', label: '首单采购时间', width: '100' },
  { istrue: true, prop: 'lastPurchaseDate', label: '最近一单采购时间', width: '100' },
  {
    istrue: true, type: 'button', fixed: 'right', width: '160', btnList: [{ label: "供应商详情", display: (row) => { return row.isHandle == true; }, handle: (that, row) => that.onSupplierDetail(row) },
    { label: "采购详情", display: (row) => { return row.isHandle == true; }, handle: (that, row) => that.onPurchaseDetail(row) }]
  },
];
const tableHandles = [
  { label: "导出", handle: (that) => that.onExport() },
  { label: "新增供应商", handle: (that) => that.onAddSupplier() },
  { label: "供应商名称符号设置", permission: 'addSupplierNameSymbolSet', handle: (that) => that.onSetSupplierSymbol() }
];
export default {
  name: "Users",
  components: { container, cesTable, MyConfirmButton, logistics, supplierdetail, supplierpurchase, addsupplier, supplierDtl, supplierLog },
  data() {
    return {
      supplierFromInfo: {
        visible: false,
        data: {},
      },
      supInfo: {
        supplier_id: null,
        name: null
      },
      supInfoVisible: false,
      that: this,
      filter: {
        code: null,
        name: null,
        province: null,
        city: null,
        districts: null,
        payType: null,
        brandId: null,
        isProxyShipping: null,
        printOrderPlatform: null,
        provinceCityDistricts1: null,
        provinceCityDistrict: null,
        purchaseStartTime: dayjs().subtract(6, 'month').format('YYYY-MM-DD'),
        purchaseEndTime: dayjs().format('YYYY-MM-DD'),
        timerange: [dayjs().subtract(6, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        isShippingLocationConsistent: null,
        isOpenInvoice: null,
        supplierIsOfPublicPay: null,
        invoiceType: null,
        supplierTaxRateCondition: null,
        supplierMinTaxRate: undefined,
        supplierMaxTaxRate: undefined,
        isNonPurchaseOrder: false
      },
      brandlist: [],
      list: [],
      supplier_id: null,
      supSymbol: null,
      tableCols: tableCols,
      tableHandles: tableHandles,
      pager: { OrderBy: "", IsAsc: false },
      summaryarry: {},
      total: 0,
      sels: [],
      selids: [],
      listLoading: false,
      pageLoading: false,
      dialogSupVisible: false,
      dialogAddSupVisible: false,
      dialogPurchaseVisible: false,
      dialogAddSupSymbolVisible: false,
      provinces: [],
      cities: [],
      districtss: [],
      symbolList: [],
      pickerOptions: {
        shortcuts: [{
          text: '前一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime());
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
            const date2 = new Date(); date2.setDate(date2.getDate());
            picker.$emit('pick', [date1, date2]);
          }
        }]
      },
      options1: [],
    };
  },
  async mounted() {
    this.getGetProvinceCityDistrict()
    await this.init1();
    await this.init2();
    await this.onSearch();
  },
  methods: {
    onLogMethod(row){
      if(!row.supplier_id) return this.$message.warning('无效供应商数据')
      this.supplierFromInfo.data = row
      this.supplierFromInfo.visible = true
    },
    taxTypeChange(e){
      this.filter.supplierMaxTaxRate = undefined
      this.filter.supplierMinTaxRate = undefined
    },
    openSupplierDtl(row) {
      this.supInfo = {
        supplier_id: row.supplier_id,
        name: row.name,
        startTime: this.filter.purchaseStartTime,
        endTime: this.filter.purchaseEndTime
      }
      this.supInfoVisible = true
    },
    async getGetProvinceCityDistrict() {
      const { data, success } = await GetProvinceCityDistrictAndNo();
      if (!success) {
        return;
      }
      this.options1 = data ? data : [];
    },
    //同步聚水潭
    onSynchronizedPool() {
      this.$nextTick(() => {
        this.$refs.supplierdetail.onSynchronizedMethod();
      });
    },
    async purchaseDateChange() {
      this.filter.purchaseStartTime = null;
      this.filter.purchaseEndTime = null;
      if (this.filter.timerange) {
        this.filter.purchaseStartTime = this.filter.timerange[0];
        this.filter.purchaseEndTime = this.filter.timerange[1];
      }
      await this.getlist();
    },
    changeingroup() {
      this.onSearch();
    },
    async initeditor(editor) {
      editor.config.uploadImgMaxSize = 3 * 1024 * 1024
      editor.config.excludeMenus = ['emoticon', 'video']
      editor.config.uploadImgAccept = []
      editor.config.customUploadImg = async function (resultFiles, insertImgFn) {
        console.log('resultFiles', resultFiles)
        const form = new FormData();
        form.append("image", resultFiles[0]);
        const res = await upLoadImage(form);
        var url = `${res.data}`
        console.log('url', url)
        insertImgFn(url)
      }
    },
    async init1() {
      var res = await getRegion({ parentcode: 0 })
      this.provinces = res.data
    },
    async init2() {
      var res2 = await getAllProBrand();
      this.brandlist = res2.data.map(item => {
        return { value: item.key, label: item.value };
      });
    },
    async SelectProvinces(code) {
      this.filter.districts = null;
      this.districtss = []
      this.filter.city = null;
      this.cities = []
      if (code) {
        var res = await getRegion({ parentcode: code })
        this.cities = res.data
      }
    },
    async SelectCity(code) {
      this.filter.districts = null;
      this.districtss = []
      if (code) {
        var res = await getRegion({ parentcode: code })
        this.districtss = res.data
      }
    },
    async onSearch() {
      this.dialogSupVisible = false
      this.dialogAddSupVisible = false
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      if (this.filter.provinceCityDistricts1) {
        this.filter.provinceCityDistrict = this.filter.provinceCityDistricts1.map((item) => {
          return item[item.length - 1];
        }).join(',');
      }
      if (!this.pager.OrderBy) this.pager.OrderBy = "";
      var pager = this.$refs.pager.getPager()
      const params = { ...pager, ...this.pager, ... this.filter }
      this.listLoading = true
      const res = await pageSupplier(params)
      this.listLoading = false
      if (!res?.success) return
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => { d._loading = false })
      this.list = data
      this.summaryarry = res.data.summary;
    },
    onAddSupplier() {
      this.dialogAddSupVisible = true;
      this.$nextTick(() => {
        this.$refs.addsupplierdetail.onclear();
      });
    },
    async onSetSupplierSymbol() {
      this.symbolList = [];
      var res = await getSupplierNameWhitelist();
      if (res?.success) {
        if (res?.data) {
          res.data.map(item => {
            this.symbolList.push(item.key);
          });
        }
        this.dialogAddSupSymbolVisible = true;
      } else {
        this.$message({ message: res?.msg ?? "获取标点符号白名单失败！", type: "error" });
      }
    },
    addCallBack(id) {
      this.onSearch();
      this.dialogAddSupVisible = false;
    },
    async onSupplierDetail(row) {
      console.log('row', row);
      this.supplier_id = row.supplier_id;
      this.dialogSupVisible = true;
      this.$nextTick(() => {
        this.$refs.supplierdetail.getDtail(row.id);
      });
    },
    async onPurchaseDetail(row) {
      this.dialogPurchaseVisible = true;
      let that = this;
      this.$nextTick(() => {
        this.$refs.supplierpurchase.getDtail(row.id, that.filter);
      });
    },
    onSaveSupDetail() {
      this.$nextTick(() => {
        this.$refs.supplierdetail.verifyPurchaseSupplierShipmentPlaceAsync();
      });
    },
    onSaveAddSupDetail() {
      this.$nextTick(() => {
        this.$refs.addsupplierdetail.verifyPurchaseSupplierShipmentPlaceAsync();
      });
    },
    async onSaveSupplierSymbol() {
      if (this.supSymbol && this.symbolList.indexOf(this.supSymbol) === -1) {
        var params = { Key: this.supSymbol };
        var res = await saveSupplierNameWhitelist(params);
        if (res?.success) {
          this.symbolList.push(this.supSymbol);
          this.supSymbol = null;
        }
      } else if (this.symbolList.indexOf(this.supSymbol) > -1) {
        this.$message({ message: "该标点符号已在白名单中！", type: "error" });
      }
    },
    async handleClose(e, i) {
      var params = { key: e };
      var res = await delSupplierNameWhitelist(params);
      if (res?.success) {
        this.symbolList.splice(i, 1);
      }
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    async onExport() {
      if (this.onExporting) return;
      try {
        const params = { ...this.pager, ... this.filter }
        var res = await exportSupplier(params);
        console.log('供应商导出', res)
        if (!res?.data) return

        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '供应商导出_' + new Date().toLocaleString() + '.xlsx')
        aLink.click()
      } catch (err) {
        console.log(err)
        console.log(err.message);
      }
      this.onExporting = false;
    },
    selsChange: function (sels) {
      this.sels = sels
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.proBianMa);
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.cascaderCss ::v-deep .el-input__inner {
  height: 28px !important;
}

::v-deep .el-cascader__search-input {
  margin: 0 0 0 2px;
}

::v-deep .cell {
    padding: 0 5px !important;
}

//控制选择器多选-标签宽度
::v-deep .el-select__tags-text {
  max-width: 30px;
}

</style>
