<template>
    <div style="margin-top: 10px;">
        <!-- 汇总 -->
        <!-- :showsummary="true" -->
        <vxetable ref="alreadyTable" :id="'vendorSummary20240821151856'" :tableData="tableData" :tableCols="tableCols"
            :is-index="true" :that="that" style="width: 100%; height: 500px; margin: 0" @sortchange='sortchange'
            @rowStyle="rowStyle" :treeProp="{ rowField: 'id', parentField: 'parentId', transform: true,}" :loading="listLoading"
            class="detail">
            <template #styleCode="{ row }">
                <div class="styleCode">{{ row.styleCode }}</div>
            </template>
            <template #warehousNo="{ row }">
                <div v-if="row.orderVideoList">
                    <div v-for="item in row.orderVideoList">{{ item.warehousNo }}</div>
                </div>
            </template>
            <template #modifiedUserName="{ row }">
                <div v-if="row.orderVideoList">
                    <div v-for="item in row.orderVideoList">{{ item.modifiedUserName }}</div>
                </div>
            </template>
            <template #createdUserName="{ row }">
                <div v-if="row.orderVideoList">
                    <div v-for="item in row.orderVideoList">{{ item.createdUserName }}</div>
                </div>
            </template>
            <template #modifiedTime="{ row }">
                <div v-if="row.orderVideoList">
                    <div v-for="item in row.orderVideoList">{{ item.modifiedTime }}</div>
                </div>
            </template>
        </vxetable>
        <my-pagination :page-size="50" ref="pager" :total="detailTotal" @page-change="detailPagechange"
            @size-change="detailSizechange" />

        <!-- 弹层部分 -->
        <el-dialog title="日志" :visible.sync="RecordsVisible" width="60%" :before-close="handleClose1" v-dialogDrag>
            <vxetable :id="'vendorSummary202408041624'" ref="detailTable" :tableData="doTableData" :toolbarshow="false"
                :tableCols="tableCols4" :is-index="true" :that="that" :showsummary="true"
                style="width: 100%; height: 500px" class="detail">
            </vxetable>
        </el-dialog>

        <!-- 入库以及驳回 -->
        <el-dialog :title="dialogtitle" :visible.sync="peizhiVisible" width="20%" v-dialogDrag>
            <rcpeizhi :peizhidata="peizhidata" @closedialog="peizhiVisible = false;" v-if="peizhiVisible"></rcpeizhi>
        </el-dialog>
    </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetable from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import rcpeizhi from "./rcpeizhi.vue"
import {
    getProviderQuotationHisRecordPageList,
    GetProviderDockingResultList,
    getProviderNewGoodsDockingResultList,
    getProviderQuotationRecordOverViewPageList,
}
    from '@/api/openPlatform/ProviderQuotation'
import { pagePurchaseOrderByProviderNameAsync } from '@/api/inventory/purchase'
import { getAllProBrand } from '@/api/inventory/warehouse'
import { setOrderVideoWeighingStatus, batchSetOrderVideoWeighingStatus, getPurchaseQualityMetageWeightLog, GetWarehouseWorkloadDtl } from '@/api/inventory/purchasequality.js'
const options = [
    {
        value: '1',
        label: '是'
    },
    {
        value: '0',
        label: '否'
    }
]

//对接记录
const tableCols4 = [
    { istrue: true, prop: 'createdUserName', label: '操作人', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '操作时间', sortable: 'custom' },
    { istrue: true, prop: 'logContent', label: '操作内容', sortable: 'custom' },
]
//汇总
const tableCols = [
    { istrue: true, prop: 'createdUserName', label: '操作员', treeNode: true, width: 'auto', sortable: 'custom'  },
    { istrue: true, prop: 'createdTime', label: '操作时间', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'packSum', label: '数量', width: 'auto', sortable: 'custom' },
]
export default {
    components: { MyContainer, vxetable, vxetablebase, rcpeizhi },
    name: "vendorSummary",
    inject: ['faListInfo'],
    props: {
        ListInfo: {
            type: Object,
            default: () => { }
        },
        rowfilter: {
            type: Object,
            default: () => { }
        },
        isHidden: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            that: this,
            dialogtitle: '',
            peizhiVisible: false,
            peizhidata: {},
            logDetail: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                openId: null,//openId
                styleCode: null,//系列编码
                orderBy: null,//排序字段
                isAsc: true,//是否升序
            },
            RecordsInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                id: null,//id
            },//对接记录请求参数
            nameInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                supplier: null,//供应商名称
            },
            // sourceType,//来源
            // positionType,//职位
            tableCols,//已提交
            tableCols4,//对接记录
            // tableCols5,//点击供应商名字
            tableData: [],//已提交
            tableData1: [],//详情
            xltableData: [],//系列编码
            newTableData: [],//新品提交
            doTableData: [],//对接记录
            brandList: [],//采购列表
            logTotal: 0,//详情总数
            detailTotal: 0,//已提交总数
            nameTotal: 0,//供应商报价详情总数
            listLoading: true,//加载中
            dialogVisible: false,//详情弹层
            RecordsVisible: false,//对接记录弹层
            operateVisible: false,//操作弹层
            nameVisible: false,//点击供应商名字弹层
            recordsTotal: 0,//对接记录总数
            options,//是否包邮,是否含税
            procurementList: [],//采购人员列表
            checkList: [],
            query: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            }
        };
    },
    mounted() {
        this.query = { ...this.ListInfo, ...this.query }
        this.getAlreadyList()
        // this.getBrandList()
    },
    methods: {
        changepage(value){
            if(!value){
                // setTimeout(()=>{
                    this.$refs.alreadyTable.changecolumn('caozuo')
                // },0)
            }else{
                // setTimeout(()=>{
                    this.$refs.alreadyTable.changecolumn_setTrue('caozuo')
                // },0)

            }
        },
        rowStyle(row, callback) {

            let obj = {};
            if (row.parentId == null) {
                obj = {
                    height: '100px',
                }
            }
            callback(obj);
        },
        rukuedit(row, name) {
            this.dialogtitle = name;
            this.peizhidata = row;
            if (name == '暂缓') {
                this.$confirm('此操作将此数据暂缓, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    let success
                    if (row.parentId) {
                        const { success: success1 } = await setOrderVideoWeighingStatus({ operateType: '暂缓', id: row.id, mainId: row.mainId, buyNo: row.buyNo })
                        success = success1
                    } else {
                        const { success: success1 } = await batchSetOrderVideoWeighingStatus({ operateType: '暂缓', buyNo: row.buyNo })
                        success = success1
                    }
                    if (success) {
                        this.$message({
                            type: 'success',
                            message: '操作成功!'
                        });
                        this.getAlreadyList()
                    }
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消操作'
                    });
                });
                return;
            }
            this.peizhidata.operateType = name;
            this.peizhiVisible = true;
        },
        //获取采购列表
        async getBrandList() {
            const { data, success } = await getAllProBrand()
            if (success) {
                this.brandList = data.map(item => {
                    return {
                        label: item.value,
                        value: item.key
                    }
                })
            } else {
                this.$message.error('获取采购人员列表失败')
            }
        },
        //汇总对接记录
        async dockingRecords(row) {
            this.RecordsInfo.id = row.id
            const { data, success } = await getPurchaseQualityMetageWeightLog(this.RecordsInfo)
            if (success) {
                this.doTableData = data
                this.RecordsVisible = true

            } else {
                this.$message.error('获取对接记录失败')
            }
        },
        handleClose1() {
            this.RecordsVisible = false
        },
        //页面数量改变
        detailSizechange(val) {
            this.query.currentPage = 1;
            this.query.pageSize = val;
            this.getAlreadyList();
        },
        //当前页改变
        detailPagechange(val) {
            this.query.currentPage = val;
            this.getAlreadyList();
        },
        //获取供应商汇总列表
        async getAlreadyList(isSeach) {
            if (isSeach) {
                this.$refs.pager.setPage(1)
            }
            this.query = { ...this.rowfilter, ...this.query, queryTime: this.rowfilter.createdTimeStr, goodsCode: this.faListInfo.goodsCode,
                timerange:  this.faListInfo.timerange }

            if (this.query.timerange && this.query.timerange.length > 0) {
                this.query.startDate = this.query.timerange[0];
                this.query.endDate = this.query.timerange[1];
            } 
            // else {
            //     this.query.startDate = null;
            //     this.query.endDate = null;
            // }

            this.listLoading = true;
            const { data, success } = await GetWarehouseWorkloadDtl(this.query);
            if (success) {
                // data.list.forEach(item => {
                //     item.evidence = item.evidence ? item.evidence.split(',') : []
                // })
                this.tableData = data.list;
                console.log(this.tableData, 'this.tableData');
                this.detailTotal = data.total;
                setTimeout(()=>{
                    this.listLoading = false;
                },500)
            } else {
                setTimeout(()=>{
                    this.listLoading = false;
                },500)
                this.$message.error('获取列表失败')
            }
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.query.orderBy = prop
                this.query.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getAlreadyList()
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.header {
    display: flex;
    margin-bottom: 10px;
}

.publicMargin {
    margin-right: 20px;
}

.detail ::v-deep .vxe-custom--wrapper {
    display: none !important;
}

::v-deep .el-badge__content {
    padding: 0 4px;
    top: 7px;
    right: 0;
}
</style>
