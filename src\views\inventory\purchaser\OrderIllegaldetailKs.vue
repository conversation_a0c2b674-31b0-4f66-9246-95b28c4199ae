<template>
  <container v-loading="pageLoading">
    <template>
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
        :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="true" @select="selectchange"
        :tableHandles='tableHandlesDc' :loading="listLoading">
      </ces-table>
    </template>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <el-dialog title="订单日志信息" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag>
      <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" style="z-index:10000;height:600px"
        v-if="dialogHisVisible" />
    </el-dialog>
  </container>
</template>

<script>
import container from '@/components/my-container/noheader'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { formatLinkProCode, formatSendWarehouse, formatExpressCompany } from "@/utils/tools";
import { GetOrderWithholdListOther4Cg, ExportOrderWithholdListOther4Cg } from "@/api/order/orderdeductmoney"
import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
const tableCols = [
  { istrue: true, prop: 'proCode', label: '宝贝ID', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
  { istrue: true, prop: 'orderNo', label: '订单编号', width: '150', sortable: 'custom', formatter: (row) => !row.orderNo ? " " : row.orderNo, type: 'click', handle: (that, row) => that.showLogDetail(row) },
  { istrue: true, prop: 'orderNoInner', label: '内部订单号', width: '120', sortable: 'custom', type: 'orderLogInfo', orderType: 'orderNoInner' },
  { istrue: true, prop: 'amountPaid', label: '扣款金额', width: '100', sortable: 'custom', formatter: (row) => parseFloat(row.amountPaid.toFixed(2)) },
  { istrue: true, prop: 'brandTitle', label: '岗位', width: '110', },
  { istrue: true, prop: 'brandName', label: '采购组', width: '110', sortable: 'custom' },
  { istrue: true, prop: 'zrSetTime', label: '责任小时', width: '100', sortable: 'custom', formatter: (row) => row.zrSetTime2NowHours },
  { istrue: true, prop: 'zrSetTime', label: '责任计算时间', width: '130', sortable: 'custom', formatter: (row) => !row.zrSetTime ? " " : formatTime(row.zrSetTime, "YYYY-MM-DD HH:mm") },
  //{ istrue: true, prop: 'brandAmount', label: '占比后扣款金额', width: '80', sortable: 'custom', formatter: (row) => parseFloat(row.brandAmount.toFixed(4)) },
  { istrue: true, prop: 'occurrenceTime', label: '扣款日期', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.occurrenceTime, "YYYY-MM-DD") },
  { istrue: true, prop: 'payTime', label: '付款日期', width: '130', sortable: 'custom', formatter: (row) => !row.payTime ? " " : formatTime(row.payTime, "YYYY-MM-DD HH:mm") },
  { istrue: true, prop: 'sendTime', label: '发货日期', width: '130', sortable: 'custom', formatter: (row) => !row.sendTime ? "" : formatTime(row.sendTime, "YYYY-MM-DD HH:mm") },
  { istrue: true, prop: 'planDeliveryDate', label: '预计发货时间', width: '124', sortable: 'custom', formatter: (row) => !row.planDeliveryDate ? " " : formatTime(row.planDeliveryDate, "YYYY-MM-DD HH:mm") },
  { istrue: true, prop: 'illegalType', label: '平台原因', width: '100', sortable: 'custom', formatter: (row) => !row.illegalTypeName ? " " : row.illegalTypeName },
  { istrue: true, prop: 'zrType1', label: '扣款类型', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'zrType2', label: '扣款原因', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'ysLx', label: '预售', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'sendWarehouseName', label: '发货仓', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'shopId', label: '店铺', width: '150', formatter: (row) => !row.shopName ? " " : row.shopName },
  { istrue: true, prop: 'groupId', label: '运营组', width: '90', sortable: 'custom', formatter: (row) => !row.groupName ? " " : row.groupName },
  { istrue: true, prop: 'brandRegion', label: '分区', width: '80', sortable: 'custom' },
  //{ istrue: true, prop: 'platform', label: '平台', width: '60', sortable: 'custom', formatter: (row) => !row.platformName ? " " : row.platformName },
  { istrue: true, prop: 'goodsName', label: '商品名称', width: '100', sortable: 'custom', formatter: (row) => !row.goodsName ? "" : row.goodsName },
  // { istrue: true, prop: 'expressNo', label: '快递单号', width: '125', sortable: 'custom', formatter: (row) => !row.expressNo ? "" : row.expressNo },
  // { istrue: true, prop: 'expressCompany', label: '快递公司', width: '125', sortable: 'custom', formatter: (row) => row.expressCompanyName },
  // { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', sortable: 'custom', width: '80', formatter: (row) => !row.operateSpecialUserName ? " " : row.operateSpecialUserName },
  // { istrue: true, prop: 'zrDept', label: '部门', width: 'auto', sortable: 'custom', formatter: (row) => !row.zrDept ? " " : row.zrDept },
  // { istrue: true, prop: 'zrAction', label: '操作', width: 'auto', sortable: 'custom', formatter: (row) => !row.zrAction ? " " : row.zrAction },
  { istrue: true, prop: 'zrConditionFullName', label: '划责规则', width: '100', sortable: 'custom', formatter: (row) => (row.zrConditionFullName ?? "") },
  { istrue: true, prop: 'zrAppealState', label: '申诉状态', width: '100', formatter: (row) => !row.zrAppealStateText ? " " : row.zrAppealStateText },
  //{ istrue: true, prop: 'deductZrMemberApplyState', label: '内部申诉状态', width: '80', formatter: (row) => !row.deductZrMemberApplyStateText ? " " : row.deductZrMemberApplyStateText },
  {
    istrue: true, label: '功能', width: '80', type: 'button', btnList: [
      {
        label: '申诉',
        handle: (that, row) => that.zrApply(row),
        ishide: (that, row) => { return !row.showZrAppealBtn; }
      },
      // {
      //     label: '内部申诉',
      //     handle: (that, row) => that.zrApplyInner(row),
      //     ishide: (that, row) => { return !row.showZrAppealMemberBtn ; }
      // }
    ]
  }
]

const tableHandlesDc = [
  { label: '导出', handle: (that) => that.onExportDetail() },
  { label: "批量申诉", handle: (that) => that.batchZrApply() },
];


export default {
  name: 'OrderIllegaldetailOther',
  components: { cesTable, container, MyConfirmButton, MySearch, MySearchWindow, OrderActionsByInnerNos },
  props: {
    filter: {},
  },
  data() {
    return {
      dialogHisVisible: false,
      orderNo: '',
      that: this,
      list: [],
      platformList: [],
      brandlist: [],
      illegalTypeList: [],
      summaryarry: {},
      pager: { OrderBy: "OccurrenceTime", IsAsc: false },
      filterImport: {
        platform: 1,
        occurrenceTime: formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD")
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      tableCols: tableCols,
      total: 0,
      sels: [],
      listLoading: false,
      pageLoading: false,
      dialogVisible: false,
      uploadLoading: false,
      uploadLoading: false,
      tableHandlesDc: tableHandlesDc,
      selids: [],
      selrows: [],
    };
  },
  async mounted() {
    await this.setPlatform()
    await this.onSearch()
  },
  methods: {
    //申诉
    async zrApply(row) {
      let self = this;
      this.$showDialogform({
        path: `@/views/order/orderillegal/zrApply/OrderDeductZrApplyForm.vue`,
        title: '责任申诉',
        autoTitle: false,
        args: { id: 0, orderNo: row.orderNo, occTime: row.occurrenceTime, platform: row.platform, mode: 1 },
        height: 300,
        width: '80%',
        callOk: self.onSearch
      })
    },
    //批量申诉
    async batchZrApply() {
      let self = this;
      if (!self.selrows || self.selrows.length <= 0) {
        this.$message({ message: "请勾选至少一行数据", type: "warning" });
        return;
      }


      let firstRowPlatform=self.selrows[0].platform;
        if(self.selrows.filter(f=>f.platform!=firstRowPlatform).length>0)
        {
            this.$message({ message: "请勾选同一平台数据", type: "warning" });
            return;
        }

      this.$showDialogform({
        path: `@/views/order/orderillegal/zrApply/OrderDeductBatchZrApplyForm.vue`,
        title: '批量责任申诉',
        autoTitle: false,
        args: { selRows: self.selrows, platform: self.selrows[0].platform },
        height: 600,
        width: '80%',
        callOk: self.onSearch
      })
    },
    //内部申诉
    async zrApplyInner(row) {
      let self = this;

        

      this.$showDialogform({
        path: `@/views/order/orderillegal/OrderDeductZrMemberApplyForm.vue`,
        title: '责任申诉',
        autoTitle: false,
        args: { id: 0, orderNo: row.orderNo, deductZrMemberId: row.deductZrMemberId, memberType: 1, occTime: row.occurrenceTime, platform: row.platform, mode: 1 },
        height: 300,
        width: '80%',
        callOk: self.onSearch
      })
    },
    showLogDetail(row) {
      this.dialogHisVisible = true;
      this.orderNo = row.orderNo;
    },
    //设置平台,扣款因下拉
    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
      var ilrule = await ruleIllegalType();
      this.illegalTypeList = ilrule.options;
    },
    //查询第一页
    async onSearch() {
      if (!this.filter.timerange) { this.$message({ message: "请选择日期", type: "warning", }); return; }
      this.$refs.pager.setPage(1)
      await this.getlist();
    },
    //获取查询条件
    getCondition() {
      this.filter.startPayDate = null
      this.filter.endPayDate = null
      this.filter.startSendDate = null
      this.filter.endSendDate = null
      if (this.filter.timerange && this.filter.timerange.length > 1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      else {
        this.$message({ message: "请先选择日期", type: "warning" });
        return false;
      }
      if (this.filter.timerange2 && this.filter.timerange2.length > 1) {
        this.filter.startPayDate = this.filter.timerange2[0];
        this.filter.endPayDate = this.filter.timerange2[1];
      }
      //1
      if (this.filter.timerange3 && this.filter.timerange3.length > 1) {
        this.filter.startSendDate = this.filter.timerange3[0];
        this.filter.endSendDate = this.filter.timerange3[1];
      }
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = {
        ...pager,
        ...page,
        ... this.filter
      }
      return params;
    },
    //分页查询
    async getlist() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true
      const res = await GetOrderWithholdListOther4Cg(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      let p = {
        amountPaid_sum: res.data.summary.amountPaid_sum,
        brandAmount_sum: res.data.summary.brandAmount_sum,
      }
      this.summaryarry = p;
      if (this.summaryarry) {
        this.summaryarry.amountPaid_sum = parseFloat(p.amountPaid_sum.toFixed(2));
        this.summaryarry.brandAmount_sum = parseFloat(p.brandAmount_sum.toFixed(2));
      }
      data.forEach(d => {
        d._loading = false
        d.payTime = d.payTime == "1900-01-01 00:00:00" ? "" : d.payTime;
      })
      this.list = data

    },
    //排序查询
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    },
    selectchange: function (rows, row) {
      this.selrows = rows;
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    //导出
    async onExportDetail() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
      var res = await ExportOrderWithholdListOther4Cg(params);
      loadingInstance.close();
      if (!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '采购-抖音扣款详情_' + new Date().toLocaleString() + '.xlsx')
      aLink.click();
    },
  },
};
</script>

<style lang="scss" scoped></style>
