<template>
    <container>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            @select='selectchange' :isSelection='false' :isSelectColumn='true' :tableData='list' :tableCols='tableCols'
            :tableHandles='tableHandles' :showsummary='false' :loading="listLoading" />
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </container>
</template>
<script>
import { getMonthReportLoseProCodePageList, exportMonthReportLoseProCodeList } from '@/api/monthbookkeeper/financialDetail'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
const tableCols = [
    { istrue: true, prop: 'yearMonth', label: '月份', sortable: 'custom',width: '80' },
    { istrue: true, prop: 'version', label: '版本', sortable: 'custom',width: '80' },
    { istrue: true, prop: 'platform', label: '平台', sortable: 'custom', width: '100', formatter: (row) => row.platformName },
    { istrue: true, prop: 'shopCode', label: '店铺编码', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'shopCode', label: '店铺名称', sortable: 'custom', width: '220', formatter: (row) => row.shopName },
    { istrue: true, prop: 'proCode', label: '产品ID', sortable: 'custom', width: '160' },
];
const tableHandles = [
    { label: "导出", handle: (that) => that.onExport() },
]
export default {
    name: 'Roles',
    components: { cesTable, container },
    props: {
        filter: {}
    },
    data() {
        return {
            shareFeeType: 11,
            that: this,
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "yearMonth", IsAsc: false },
            summaryarry: {},
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            pageLoading: false
        }
    },
    mounted() {

    },
    beforeUpdate() { },
    methods: {
        //导出
        async onExport() {
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }

            if(!params.yearMonth){
                this.$message({ message: "请选择月份", type: "warning" });
                return;
            }
            if(!params.platform){
                this.$message({ message: "请选择平台", type: "warning" });
                return;
            }

            var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            var res = await exportMonthReportLoseProCodeList(params);
            loadingInstance.close();
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '导出缺失ID_' + new Date().toLocaleString() + '.xlsx')
            aLink.click();
        },
        onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        async getlist() {
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }

            if(!params.yearMonth){
                this.$message({ message: "请选择月份", type: "warning" });
                return;
            }
            if(!params.platform){
                this.$message({ message: "请选择平台", type: "warning" });
                return;
            }

            this.listLoading = true
            const res = await getMonthReportLoseProCodePageList(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res.data?.total ?? 0
            const data = res.data?.list
            data?.forEach(d => {
                d._loading = false
            })
            this.list = data
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selsChange: function (sels) {
            this.sels = sels
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
    }
}
</script>
