<template>
 <div>
  <div class="block">
    <span>
      <el-date-picker  style="position: relative; top: 1px; width: 400px"
          type="daterange" align="right" unlink-panels range-separator="至" start-placeholder="开始日期"
            format="yyyy-MM-dd" value-format="yyyy-MM-dd"
          end-placeholder="结束日期" v-model="daterange"  :picker-options="pickerOptions" @change="handleDateChange">
      </el-date-picker>
    </span>
    <el-button type="primary" @click="onSearch">查询</el-button>
  </div>

  <my-container>
    <ces-table :id="'czjldialog202408041700'" ref="table" :that="that" :isIndex='true' :hasexpand='false' style="height: 500px;" @sortchange='sortchange'
      :tableData='logList' :isSelection="false" :tableCols='tableCols' :isSelectColumn="true" :isBorder="false">
    </ces-table>
    <template #footer>
          <my-pagination ref="pagerlog" :total="totallog" @get-page="getlogList" />
    </template>
  </my-container>
 </div>
</template>

<script>
import dayjs from "dayjs";
import MyContainer from "@/components/my-container";
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import { formatTime,pickerOptions } from "@/utils/tools";
import { getAllStationExtendOperateLogByTimeId } from '@/api/operatemanage/datapdd/actualtimedatapdd.js';

const startDate = formatTime(dayjs().subtract(6,'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(0,'day'), "YYYY-MM-DD");

const tableCols = [
    { istrue: true, prop: 'operateType', align: 'left', label: '操作类型',width: '200'},
    { istrue: true, prop: 'operateModule', align: 'left', label: '修改内容',width: '200'},
    { istrue: true, prop: 'operateName', align: 'left', label: '操作人',width: '200'},
    { istrue: true, prop: 'operateTime', align: 'left', label: '操作时间',width: '200'},
    { istrue: true, prop: 'operateInfo', align: 'left', label: '备注'},
];

export default {
 name: 'Vue2demoCzjldialog',
 props:['rowmsg'],
 components:{cesTable,MyContainer},
 data() {
  return {
    that: this,
    //操作日志
    logList:[],
    pagerlog: { ordeBy:null, IsAsc: false },
    daterange:[startDate,endDate],
    startTime:null,
    endTime:null,
    totallog: 0,
    tableCols: tableCols,
    pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
  };
 },

 async mounted() {
      // await this.onSearch();
},

computed:{

},

methods: {
  onSearch () {
            this.$refs.pagerlog.setPage(1);
            this.getlogList();
        },
  handleDateChange(value) {
      this.$emit('date-change', value);
    },
  //排序查询
  async sortchange(column) {
          if (!column.order)
              this.pagerlog = {};
          else {
              this.pagerlog = { ordeBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
          }
          await this.onSearch();
    },
  //操作日志
  async getlogList () {
      this.startTime = null;
      this.endTime = null;
      if(this.daterange){
        this.startTime = this.daterange[0];
        this.endTime = this.daterange[1];
      }else{
            this.$message({ message: "请先选择日期", type: "warning" });
            return false;
      }

      let param = {
        pageSize: 50,
        currentPage: 1,
        yearMonthDay: this.rowmsg.yearMonthDay,
        productCode: this.rowmsg.proCode,
        IsAsc: true,
        startTime:this.startTime,
        endTime:this.endTime,
        ordeBy:"",
      }
      const {data} = await getAllStationExtendOperateLogByTimeId(param)
      this.listLoading = false
      if (!data){
        return
      }else{
        this.logList = data.list;
        this.totallog = data.total;
      }
      this.twomsg = data;
  },
 },
};
</script>

<style lang="scss" scoped>

</style>
