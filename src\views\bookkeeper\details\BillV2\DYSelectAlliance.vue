<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="时间">
                    <el-date-picker style="width: 249px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" :clearable="false"
                        end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item label="店铺款式编码:">
                    <el-input v-model.trim="filter.ProCode" :clearable="true" maxlength="20" placeholder="店铺款式编码" style="width:130px;"/>
                </el-form-item>
                <el-form-item label="店铺:">
                    <el-input v-model.trim="filter.shopCode" :clearable="true" maxlength="20" placeholder="店铺" style="width:130px;"/>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <!-- <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item> -->
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
            @select="selectchange" :tableHandles='tableHandles' @cellclick="cellclick" :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" :close-on-click-modal="false" width="40%" v-dialogDrag>
            <el-row :gutter="20">
                <el-col :xs="4" :sm="6" :md="8" :lg="6">
                    <el-date-picker style="width: 100%" v-model="importDialog.filter.YearMonthDay" type="date" format="yyyyMMdd"
                    value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
                  </el-col>
                <el-col :xs="4" :sm="6" :md="8" :lg="6">
                    <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="4" action
                        accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove"
                        :file-list="fileList">
                        <template #trigger>
                            <el-button size="small" type="primary">选取文件</el-button>
                        </template>
                        <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                            @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
                    </el-upload>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { platformlist} from '@/utils/tools'
import { formatTime, formatTime1 } from "@/utils";
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { importDYSelectAlliance as importBillFee,getDYSelectAlliance } from '@/api/bookkeeper/reportdayV2'
//import { getBillFeePageList } from '@/api/bookkeeper/financialDetail'
import {getNewPddBillingCharge,exportNewPddBillingCharge} from '@/api/bookkeeper/reportday'


const tableCols = [
    { istrue: true, prop: 'yearMonthDay', label: '日期', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'orderNo', label: '订单号', tipmesg: '', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'proCode', label: '商品ID', tipmesg: '', width: '150', sortable: 'custom', },
    { istrue: true, prop: 'productName', label: '商品名称', tipmesg: '', width: '150', sortable: 'custom', },
    { istrue: true, prop: 'shopName', label: '店铺名称', tipmesg: '', width: '150', formatter: (row) => !row.shopName ? " " : row.shopName },
    { istrue: true, prop: 'price', label: '支付金额', tipmesg: '', width: '150', sortable: 'custom', },
    { istrue: true, prop: 'topSellers', label: '出单达人数', tipmesg: '', width: '150', sortable: 'custom', },
    { istrue: true, prop: 'totalOrders', label: '成交订单数',  width: '150', sortable: 'custom', },
    { istrue: true, prop: 'totalAmount', label: '成交金额', tipmesg: '', width: '150', sortable: 'custom', },
    { istrue: true, prop: 'estimatedCommission', label: '预估佣金支出', width: '150', sortable: 'custom', },
    { istrue: true, prop: 'averageCommissionRate', label: '平均佣金率', width: '150', sortable: 'custom', },
]

const tableHandles = [
    { label: "导入", handle: (that) => that.startImport() },
];

const startTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const curMonth = formatTime1(dayjs().startOf("month").subtract(1, "month"), "yyyyMM");

export default {
    name: 'YunHanAdminIndex',
    components: { container, cesTable, MyConfirmButton },

    data() {
        return {
            that: this,
            filter: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
                shopCode: null,
                BillType:null
            },
            platformlist:platformlist,
            importDialog: {
                filter: {
                    YearMonthDay: null,
                    //PlatForm: null
                }
            },
            list: [],
            shopList: [],
            summaryarry: {},
            pager: { OrderBy: "YearMonthDay", IsAsc: false },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            // onHandNumber: null,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            sels: [],
            // editparmLoading: false,
            uploadLoading: false,
            // editparmLoading1: false,
            // editparmLoading2: false,
            // editparmVisible: false,
            // editparmVisible1: false,
            // editparmVisible2: false,
            dialogVisible: false,
            listLoading: false,
            // showDetailVisible: false,
            fileList: []
        };
    },

    async mounted() {
        await this.onSearch()
        await this.onchangeplatform()
    },

    methods: {
    // async onExport() {
    //  if (this.onExporting) return;
    //  try{
    //     this.filter.startTime = null;
    //     this.filter.endTime = null;
    //     if (this.filter.timerange) {
    //             this.filter.startTime = this.filter.timerange[0];
    //             this.filter.endTime = this.filter.timerange[1];
    //         }
    //     this.uploadLoading = true;
    //     const params = {...this.pager,...this.filter}
    //     var res= await exportNewPddBillingCharge(params);
    //     if(!res?.data) return
    //     const aLink = document.createElement("a");
    //     let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
    //     aLink.href = URL.createObjectURL(blob)
    //     aLink.setAttribute('download','新版拼多多账单费用_' + new Date().toLocaleString() + '.xlsx' )
    //     this.uploadLoading = false;

    //     aLink.click()
    //     }catch(err){
    //       console.log(err)
    //       console.log(err.message);
    //     }
    //   this.onExporting=false;
    //  },

        //获取店铺
        async onchangeplatform() {
            this.categorylist = []
            const res1 = await getshopList({ platform: this.filter.platform, CurrentPage: 1, PageSize: 100000 });
            this.filter.shopCode = null
            this.shopList = res1.data.list
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getDYSelectAlliance(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            this.list = data
        },
        async nSearch() {
            await this.getlist()
        },
        //字体颜色
        // renderRefundStatus(row) {
        //     if (row.refundStatus == '成功退款' || row.refundStatus == '等待退款') {
        //         return "color:red;cursor:pointer;";
        //     } else return "";
        // },
        //开始导入
        startImport() {
           // this.importDialog.filter.platform = null
            this.fileList = []
            this.uploadLoading=false
            this.dialogVisible = true;
        },
        //取消导入
        cancelImport() {
            this.dialogVisible = false;
        },
        uploadSuccess(response, file, fileList) {
            if (response.code == 200) {
            } else {
                fileList.splice(fileList.indexOf(file), 1);
            }
        },
        async submitUpload() {
            if (this.importDialog.filter.YearMonthDay == null) {
                this.$message({ message: "请选择日期", type: "warning" });
                return false;
            }
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit = true;
            this.uploadLoading = true;
            this.$refs.upload.submit();
        },
        clearFiles(){
            this.$refs['upload'].clearFiles();
        },
        async uploadFile(item) {
            if (!this.fileHasSubmit) {
                return false;
            }
            this.fileHasSubmit = false;
            this.uploadLoading = true;
            const form = new FormData();
            form.append("token", this.token);
            form.append("upfile", item.file);
            //form.append("PlatForm", this.importDialog.filter.PlatForm);
            form.append("YearMonthDay", this.importDialog.filter.YearMonthDay);
            let res = await importBillFee(form);
                if (res.code == 1) {
                    this.$message({ message: "上传成功,正在导入中...", type: "success" });
                    this.$refs.upload.clearFiles();
                    this.dialogVisible = false;
                }
            this.fileList = []
            this.uploadLoading = false;
        },
        async uploadChange(file, fileList) {
            let files=[];
            files.push(file)
            this.fileList = files;
        },
        async uploadRemove(file, fileList) {
            this.fileList = []
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event) {

        },
    }
};
</script>

<style lang="scss" scoped>

</style>
