<template>
    <container v-loading="pageLoading">
        <template>
            <vxetablebase style="overflow-x: hidden" :id="'intransitdetail202408041603'" ref="table" :that='that' :hasSeq="false" @sortchange='sortchange' :tableData='list'
                :tableCols='tableCols' :treeProp="{ childrenField: 'children' }" :loading='listLoading' @cellClick='cellclick'
                border>
                <template #left>
                    <vxe-column width="40" fixed="left">
                        <template #header>
                            <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange"></el-checkbox>
                        </template>
                        <template #default="{ row }">
                            <el-checkbox v-model="row.isCheck" v-if="row.isTop" @change="checkChange"></el-checkbox>
                        </template>
                    </vxe-column>
                    <vxe-column type="seq" width="70" title="序号" align="center" tree-node></vxe-column>
                </template>
                <template #colslot="{col}">
                    <span class="consignmentTime">
                        {{getConsignmentTimeData(col)}}
                    </span>
                    <span class="consignmentTime" @click.stop="clearTime(col)" v-if="col.canClear && checkPermission('api:inventory:purchase:ClearConsignment')">
                        清空
                    </span>
                </template>
            </vxetablebase>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <vxe-modal v-model="visiblepopoverdetailbuyno" :loading="listLoadingbuyno" :width="800"
            :position="{ top: 380, left: 480 }" resize mask-closable>
            <template #default>
            <template>
                <el-table :data="detaillistbuyno" border max-height="400" :show-summary="true">
                    <el-table-column width="130" property="goodsCode" label="商品编码" sortable></el-table-column>
                    <el-table-column width="260" property="goodsName" label="商品名称"></el-table-column>
                    <el-table-column width="80" property="oldPrice" label="上次单价"></el-table-column>
                    <el-table-column width="60" property="price" label="单价">
                        <template slot-scope="scope">
                            <!-- 上次单价比这次单价低 的 标红 -->
                            <span v-if="(scope.row.price - scope.row.oldPrice) > 0"
                                style="color: red;">{{ scope.row.price }}</span>
                            <span v-else>{{ scope.row.price }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column width="60" property="count" label="数量"></el-table-column>
                    <el-table-column width="60" property="sumPrice" label="金额"></el-table-column>
                    <el-table-column width="90" property="inCount" label="已入库数量">
                        <template slot-scope="scope">
                            <span v-if="(scope.row.inCount > 0 && (scope.row.count - scope.row.inCount) > 0)"
                                style="color: red;">{{ scope.row.inCount }}</span>
                            <span v-else>{{ scope.row.inCount }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column width="75" property="nonInCount" label="未入库数">
                        <template slot-scope="scope">
                            <div v-if="(scope.row.inCount > 0 && (scope.row.count - scope.row.inCount) > 0)">
                                {{ scope.row.nonInCount }}<strong style="color:green"> 缺</strong></div>
                            <div v-else>{{ scope.row.nonInCount }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column width="100" property="nonInAmont" label="未入库金额"></el-table-column>
                    <el-table-column width="150" property="warehouse" label="第三方物流和分仓">
                        <template slot-scope="scope">
                            <span>{{ (scope.row.warehouseName) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column width="150" property="goodsLable" label="商品标签"></el-table-column>
                    <el-table-column width="100" property="isError" label="是否异常">
                        <template slot-scope="scope">
                            <span>{{ formatYesornoBool(scope.row["isError"]) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column width="100" property="lastWarehousingDate" label="入库时间">
                        <template slot-scope="scope">
                            <div v-html="formatTime(scope.row['lastWarehousingDate'], 'MM-DD')"></div>
                        </template>
                    </el-table-column>
                    <el-table-column width="100" property="lastInTransitTime" label="在途时长">
                        <template slot-scope="scope">
                            <div v-html="formatSecondToHour(scope.row['lastInTransitTime'])"></div>
                        </template>
                    </el-table-column>
                    <el-table-column width="auto" property="isOutStock" label="缺货状态">
                        <template slot-scope="scope">
                            <div v-html="formatIsOutStock(scope.row['isOutStock'])"></div>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
            </template>
        </vxe-modal>

        <vxe-modal v-model="visiblepopoverdetailware" :loading="listLoadingware" :width="800"
            :position="{ top: 380, left: 550 }" resize mask-closable>
            <template #default>
            <template>
                <el-table :data="detaillistware">
                    <el-table-column width="130" property="goodsCode" label="商品编码"></el-table-column>
                    <el-table-column width="260" property="goodsName" label="商品名称"></el-table-column>
                    <el-table-column width="60" property="price" label="单价"></el-table-column>
                    <el-table-column width="60" property="count" label="数量"></el-table-column>
                    <el-table-column width="60" property="amont" label="金额"></el-table-column>
                    <el-table-column width="90" property="weight" label="重量"></el-table-column>
                    <el-table-column width="75" property="volume" label="体积"></el-table-column>
                </el-table>
            </template>
            </template>
        </vxe-modal>

        <!-- 编辑发货时间、到站点时间、质检时间 -->
        <el-dialog :title="'编辑' + editTimeTitle" :visible.sync="editTimeVisible" width="30%" v-if="editTimeVisible"
            element-loading-text="拼命加载中" v-dialogDrag @close="resetEditTimeForm">
            <el-form label-width="110px" ref="editTimeFormRef" :model="editTimeForm">
                <el-form-item :label="editTimeTitle + '：'" prop="optTime" :rules="[
                    { required: true, message: '请选择操作时间', trigger: ['blur', 'change'] }
                ]">
                    <el-date-picker v-model="editTimeForm.optTime" format="yyyy-MM-dd HH:mm" type="datetime"
                        placeholder="选择日期时间" :clearable="false" :picker-options="{
                            disabledDate: disabledDate,
                            selectableRange: selectableRange
                        }" @change="timeChange">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="操作人：">
                    <el-input v-model="editTimeForm.optUserName" style="width:220px;" disabled></el-input>
                </el-form-item>
                <el-form-item label="图片：">
                    <YhImgUpload :value.sync="editTimeForm.imgUrl" :limit="1" :ismultiple="false" ref="img5">
                    </YhImgUpload>
                </el-form-item>
                <el-form-item label="发货方式：" v-if="editTimeType == 0">
                    <el-select v-model="editTimeForm.consignmentType" style="width:220px;" clearable>
                        <el-option value="物流" label="物流"></el-option>
                        <el-option value="其他" label="其他"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="editTimeVisible = false">取 消</el-button>
                    <el-button type="primary" @click="saveEditTime">保存</el-button>
                </span>
            </template>
        </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container/noheader'
import MyConfirmButton from '@/components/my-confirm-button'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import checkPermission from '@/utils/permission'
import { getPurchaseInTransitDetailPageList, queryPurchaseOrderDetail, queryWarehousingOrderDetail, saveConsignment, saveArrivalStation, saveQuality,getPubchaseInTransitDetailBuyNosTop1000,clearConsignment } from '@/api/inventory/purchase';
import {
    formatMinuteToHour,
    formatWarehouse,
    formatNoLink,
    formatTime,
    formatYesornoBool,
    formatIsOutStock,
    formatSecondToHour,
} from "@/utils/tools";
const tableCols = [
    { istrue: true, prop: 'goodsImgUrl', label: '商品图片', width: '70', type: 'images' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'supplier_id', label: '供应商ID', width: '85', sortable: 'custom', formatter: (row) => row.supplier_id },
    { istrue: true, prop: 'supplier', label: '供应商', width: '140', sortable: 'custom' },
    { istrue: true, prop: 'buyNo', label: '采购单号', width: '85', sortable: 'custom', type: "html", formatter: (row) => formatNoLink(row.buyNo) },
    { istrue: true, prop: 'brandName', label: '采购员', width: '60' },
    { istrue: true, prop: 'warehousingNo', label: '入库单号', width: '85', sortable: 'custom', type: "html", formatter: (row) => formatNoLink(row.warehousingNo) },
    { istrue: true, prop: 'stockOutWarehouseName', label: '入库仓', width: '80', sortable: 'custom', formatter: (row) => row.stockOutWarehouseName },
    { istrue: true, prop: 'purchaseDate', label: '采购时间', width: '130', sortable: 'custom', formatter: (row) => formatTime(row.purchaseDate, 'YYYY-MM-DD HH:mm') },
    { istrue: true, prop: 'checkDate', label: '财审时间', width: '130', sortable: 'custom', formatter: (row) => formatTime(row.checkDate, 'YYYY-MM-DD HH:mm') },
    // { istrue: true, prop: 'consignmentUserName', label: '操作人', width: '60' },
    { istrue: true, prop: 'newConsignmentName', label: '最新操作人', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'bindTime', label: '最新操作时间', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '数据接入时间', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'consignmentData', label: '发货时间', type:'colslot', width: '140', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'consignmentType', label: '发货方式', width: '80' },
    { istrue: true, prop: 'arrivalStationData', label: '到站点时间', width: '130', sortable: 'custom', type: "html", align: 'center', formatter: (row) => row.canEditArrivalStationData ? formatNoLink(row.arrivalStationData ? formatTime(row.arrivalStationData, 'YYYY-MM-DD HH:mm') : '编辑') : row.arrivalStationData ? formatTime(row.arrivalStationData, 'YYYY-MM-DD HH:mm') : '' },
    { istrue: true, prop: 'arrivalDate', label: '到货时间', width: '130', sortable: 'custom', formatter: (row) => formatTime(row.arrivalDate, 'YYYY-MM-DD HH:mm') },
    { istrue: true, prop: 'qualityData', label: '质检时间', width: '130', sortable: 'custom', formatter: (row) => formatTime(row.qualityData, 'YYYY-MM-DD HH:mm') },
    { istrue: true, prop: 'warehousingDate', label: '入库时间', width: '130', sortable: 'custom', formatter: (row) => formatTime(row.warehousingDate, 'YYYY-MM-DD HH:mm') },
    { istrue: true, prop: 'checkSpan', label: '财审时长', width: '100', sortable: 'custom', formatter: (row) => formatMinuteToHour(row.checkSpan) },
    { istrue: true, prop: 'consignmentSpan', label: '发货时长', width: '100', sortable: 'custom', formatter: (row) => row.consignmentSpan == 0 ? null : row.consignmentSpanStr },
    { istrue: true, prop: 'totalInTransitSpan', label: '在途时长', width: '100', sortable: 'custom', formatter: (row) => row.totalInTransitSpan == 0 ? null : row.totalInTransitSpanStr },
    { istrue: true, prop: 'arrivalStationSpan', label: '提货时长', width: '100', sortable: 'custom', formatter: (row) => row.arrivalStationSpan == 0 ? null : row.arrivalStationSpanStr },
    { istrue: true, prop: 'qualitySpan', label: '质检时长', width: '100', sortable: 'custom', formatter: (row) => row.qualitySpan == 0 ? null : row.qualitySpanStr },
    { istrue: true, prop: 'warehousingSpan', label: '入库时长', width: '100', sortable: 'custom', formatter: (row) => row.warehousingSpan == 0 ? null : row.warehousingSpanStr },
]
export default {
    name: 'intransitdetail',
    components: { vxetablebase, container, MyConfirmButton, MySearch, MySearchWindow, YhImgUpload },
    props: {
        filter: {},
    },
    data() {
        return {
            that: this,
            list: [],
            summaryarry: {},
            pager: { OrderBy: "purchaseDate", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            formatMinuteToHour: formatMinuteToHour,
            formatTime: formatTime,
            formatYesornoBool: formatYesornoBool,
            formatIsOutStock: formatIsOutStock,
            formatSecondToHour: formatSecondToHour,

            visiblepopoverdetailbuyno: false,
            listLoadingbuyno: false,
            detaillistbuyno: [],

            visiblepopoverdetailware: false,
            listLoadingware: false,
            detaillistware: [],
            brandlist: [],
            editTimeVisible: false,
            editTimeType: 0,
            editTimeTitle: '',
            editTimeForm: {
                goodsCode: '',
                supplierId: 0,
                buyNo: '',
                warehousingNo: '',
                optTime: formatTime(new Date(), 'YYYY-MM-DD HH:mm:ss'),
                imgUrl: '',
                consignmentType: '',
                optUserName: '',
                isEditByBuyNo: false,
            },
            disabledDate: time => { return false; },
            selectableRange: '00:00:00 23:59:59',
            editTimeFormMinTime: '',
            editTimeFormMaxTime: '',
            checkAll: false,
            isIndeterminate: false
        };
    },
    async mounted() {
        await this.onSearch()
        this.editTimeForm.optUserName = this.$store.state.user.userName
    },
    methods: {
        //查询第一页
        async onSearch() {
            console.log(this.$refs);
            // if (!this.filter.timerange) { this.$message({ message: "请选择日期", type: "warning", }); return; }
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            this.filter.startPurchaseDate = null;
            this.filter.endPurchaseDate = null;
            this.filter.startArrivalDate = null;
            this.filter.endArrivalDate = null;
            this.filter.startWarehousingDate = null;
            this.filter.endWarehousingDate = null;
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.startPurchaseDate = this.filter.timerange[0];
                this.filter.endPurchaseDate = this.filter.timerange[1];
            }
            // else {
            //     this.$message({ message: "请先选择采购时间", type: "warning" });
            //     return false;
            // }

            if (this.filter.timerange2 && this.filter.timerange2.length > 1) {
                this.filter.startArrivalDate = this.filter.timerange2[0];
                this.filter.endArrivalDate = this.filter.timerange2[1];
            }
            if (this.filter.timerange3 && this.filter.timerange3.length > 1) {
                this.filter.startWarehousingDate = this.filter.timerange3[0];
                this.filter.endWarehousingDate = this.filter.timerange3[1];
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }

            if (params.OrderBy == 'stockOutWarehouseName') {
                params.OrderBy = 'stockInWarehouse';
            }

            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            this.listLoading = true;
            const res = await getPurchaseInTransitDetailPageList(params);
            this.listLoading = false;
            if (!res?.success) {
                return;
            }
            this.total = res.data.buyNoTotal;
            const data = res.data.list;
            this.summaryarry = res.data.summaryarry;
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
            this.$emit("setTotalData", res.data.buyNoTotal, res.data.total)
        },
        //排序查询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            });
        },
        //导出
        async onExportDetail() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            console.log(params, 'params')
            var res = await exportOrderWithholdListNew(params);
            loadingInstance.close();
            console.log(res, 'res')
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '采购-拼多多扣款详情_' + new Date().toLocaleString() + '.xlsx')
            aLink.click();
        },
        async cellclick(event) {
            let row = event.row;
            let column = event.column;
            this.listLoadingbuyno = true;
            if (column.property == 'buyNo') {
                this.visiblepopoverdetailbuyno = true;
                this.detaillistbuyno = [];
                this.listLoadingbuyno = true;
                const res = await queryPurchaseOrderDetail({ buyno: row.buyNo });
                this.listLoadingbuyno = false;
                if (res.code == 1 && res.data)
                    this.detaillistbuyno = res.data;
            }
            if (column.property == 'warehousingNo') {
                this.visiblepopoverdetailware = true;
                this.detaillistware = [];
                this.listLoadingware = true;
                const res = await queryWarehousingOrderDetail({ warehousingNo: row.warehousingNo })
                this.listLoadingware = false;
                if (res.code == 1 && res.data)
                    this.detaillistware = res.data;
            }
            if (column.property == 'consignmentData') {
                if (!checkPermission('api:inventory:purchase:SaveConsignment')) {
                    this.$message({ message: "没有操作权限", type: "error" });
                    return;
                }

                this.initEditFormData(0, row);
                this.setDatePickOption(row.checkDate, row.arrivalStationData);
                this.editTimeVisible = true;
            }
            if (column.property == 'arrivalStationData') {
                if (!row.canEditArrivalStationData) {
                    return;
                }

                if (!checkPermission('api:inventory:purchase:SaveArrivalStation')) {
                    this.$message({ message: "没有操作权限", type: "error" });
                    return;
                }

                if (!row.consignmentData) {
                    this.$message({ message: "未选择发货时间", type: "error" });
                    return;
                }

                this.initEditFormData(1, row);
                this.setDatePickOption(row.consignmentData, row.qualityData);
                this.editTimeVisible = true;
            }
        },
        initEditFormData(type, row) {
            const typePrev = type == 0 ? 'consignment' : type == 1 ? 'arrivalStation' : 'quality';
            this.editTimeType = type;
            this.editTimeForm.goodsCode = row.goodsCode;
            this.editTimeForm.supplierId = row.supplier_id;
            this.editTimeForm.buyNo = row.buyNo;
            this.editTimeForm.warehousingNo = row.warehousingNo;
            this.editTimeForm.optUserName = row[typePrev + 'UserName'] || this.$store.state.user.userName;
            this.editTimeForm.optTime = row[typePrev + 'Data'] || formatTime(new Date(), 'YYYY-MM-DD HH:mm:ss');
            this.editTimeForm.imgUrl = row[typePrev + 'Img'];
            this.editTimeTitle = type == 0 ? '发货时间' : type == 1 ? '到站点时间' : '质检时间';
            this.editTimeForm.consignmentType = row.consignmentType;
            this.editTimeForm.isEditByBuyNo = row.children == null ? false : (row.children.length > 0);
        },
        // 保存发货、到站点、质检时间
        async saveEditTime() {
            this.$refs.editTimeFormRef.validate(async (valid) => {
                if (!valid) return;

                let formData = {
                    goodsCode: this.editTimeForm.goodsCode,
                    supplier_id: this.editTimeForm.supplierId,
                    buyNo: this.editTimeForm.buyNo,
                    warehousingNo: this.editTimeForm.warehousingNo,
                    consignmentType: this.editTimeForm.consignmentType,
                    isEditByBuyNo: this.editTimeForm.isEditByBuyNo
                }
                const typePrev = this.editTimeType == 0 ? 'consignment' : this.editTimeType == 1 ? 'arrivalStation' : 'quality';
                formData[typePrev + 'Data'] = formatTime(this.editTimeForm.optTime, 'YYYY-MM-DD HH:mm:ss');
                formData[typePrev + 'UserId'] = this.editTimeForm.optUserId;
                formData[typePrev + 'UserName'] = this.editTimeForm.optUserName;
                formData[typePrev + 'Img'] = this.editTimeForm.imgUrl;

                let res;
                // 编辑发货时间
                if (this.editTimeType == 0) {
                    res = await saveConsignment(formData);
                }
                // 编辑到站时间
                else if (this.editTimeType == 1) {
                    res = await saveArrivalStation(formData);
                }
                // 编辑质检时间
                else if (this.editTimeType == 2) {
                    res = await saveQuality(formData);
                }

                if (res?.success) {
                    this.getlist();
                    this.editTimeVisible = false;
                    this.resetEditTimeForm();
                }
            })
        },
        resetEditTimeForm() {
            this.editTimeForm = {
                goodsCode: '',
                supplierId: 0,
                buyNo: '',
                warehousingNo: '',
                optUserName: this.$store.state.user.userName,
                optTime: formatTime(new Date(), 'YYYY-MM-DD HH:mm:ss'),
                imgUrl: '',
                consignmentType: '',
                isEditByBuyNo: false
            };
        },
        setDatePickOption(min, max) {
            this.editTimeFormMinTime = min;
            this.editTimeFormMaxTime = max;
            // 在初始化时间时不会出发change事件，有可能会出现时间不在范围的情况
            this.timeChange(this.editTimeForm.optTime);

            const minDay = new Date(new Date(min).setHours(0, 0, 0, 0)).getTime();
            const maxDay = new Date(new Date(max).setHours(0, 0, 0, 0)).getTime();
            // 限制日期选择范围
            this.disabledDate = (time) => {
                const date = new Date(new Date(time).setHours(0, 0, 0, 0)).getTime()
                if (min && max) {
                    return date < minDay || date > maxDay
                } else if (min) {
                    return date < minDay
                } else if (max) {
                    return date > maxDay
                } else {
                    return false;
                }
            };
        },
        // 选择时间改变时 比较允许的时间范围
        timeChange(time) {
            const curTime = new Date(time).getTime();
            const min = this.editTimeFormMinTime;
            const max = this.editTimeFormMaxTime;

            if (min && curTime <= new Date(min).getTime()) {
                const minTime = new Date(min);

                let minhour = minTime.getHours();
                let minminute = minTime.getMinutes() + 1;
                if (minminute >= 60) {
                    minhour = minhour + 1
                    minminute = 0;
                }

                this.editTimeForm.optTime = formatTime(minTime.setHours(minhour, minminute, 0), 'YYYY-MM-DD HH:mm:ss');
            }
            if (max && curTime >= new Date(max).getTime()) {
                const maxTime = new Date(max);

                let maxhour = maxTime.getHours();
                let maxminute = maxTime.getMinutes() - 1;
                if (maxminute < 0) {
                    maxhour = maxhour - 1;
                    maxminute = 59;
                }
                this.editTimeForm.optTime = formatTime(maxTime.setHours(maxhour, maxminute, 0), 'YYYY-MM-DD HH:mm:ss');
            }
        },
        // 全选/全不选
        handleCheckAllChange(){
            this.isIndeterminate = false;
            var check = this.checkAll;
            this.list.forEach(data=>{
                data.isCheck = check;
            })
        },
        // 选择列表项
        checkChange(){
            var noChecks = this.list.filter(s=>!s.isCheck);
            var noCheckCount = noChecks?noChecks.length:0;
            if(noCheckCount==0){
                this.isIndeterminate=false;
                this.checkAll=true;
            } else if(noCheckCount==this.list.length){
                this.isIndeterminate=false;
                this.checkAll=false;
            } else {
                this.isIndeterminate = true;
            }
        },
        // 获取选中的采购单号
        getCheckBuyNo(){
            var checks = this.list.filter(s=>s.isCheck);
            if(checks && checks.length>0){
                return checks.map(s=>s.buyNo);
            }
            return [];
        },
        // 复制采购单号
        async copyBuyNo(){
            var buyNos = this.getCheckBuyNo();
            if(buyNos.length<=0){
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                const res = await getPubchaseInTransitDetailBuyNosTop1000(params);
                if (!res?.success) {
                    this.$message({ message: res.msg, type: "error" });
                    return;
                }
                console.log(res);
                buyNos = res.data;
            }

            if(buyNos.length<=0){
                this.$message({ message: '未找到符合条件的记录', type: "error" });
                return;
            }

            if(buyNos.length>1000){
                buyNos = buyNos.slice(0,1000);
            }

            var buyNoStr = buyNos.join(',');
            this.copytext(buyNoStr);
        },
        // 复制内容到剪切板
        copytext(e) {
            let textarea = document.createElement("textarea")
            textarea.value = e
            textarea.readOnly = "readOnly"
            document.body.appendChild(textarea)
            textarea.select()
            let result = document.execCommand("copy")
            if (result) {
                this.$message({
                message: '复制成功',
                type: 'success'
                })
            }
            textarea.remove()
        },
        getConsignmentTimeData(row){
            return row.consignmentData ? formatTime(row.consignmentData, 'YYYY-MM-DD HH:mm') : '编辑';
        },
        async clearTime(row){
            this.$confirm('确认清空已编辑信息？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let formData = {
                    goodsCode: row.goodsCode,
                    supplier_id: row.supplier_id,
                    buyNo: row.buyNo,
                    warehousingNo: row.warehousingNo,
                    isEditByBuyNo: row.children == null ? false : (row.children.length > 0)
                }
                const res = await clearConsignment(formData);

                if (res?.success) {
                    this.getlist();
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消清空'
                });
            });
        }
    },
    watch: {
        'editTimeForm.optTime'(selectTime) {
            const date = new Date(new Date(selectTime).setHours(0, 0, 0, 0)).getTime()

            const min = this.editTimeFormMinTime;
            const max = this.editTimeFormMaxTime;
            const minTime = new Date(min); // 最小时间
            const maxTime = new Date(max); // 最大时间
            const minDay = new Date(new Date(min).setHours(0, 0, 0, 0)).getTime(); // 最小时间日期
            const maxDay = new Date(new Date(max).setHours(0, 0, 0, 0)).getTime(); // 最大时间日期
            let minhour = minTime.getHours(); // 最小时间小时
            let minminute = minTime.getMinutes() + 1; // 最小时间分钟 需要比最小时间分钟大1 小时进位未处理
            if (minminute >= 60) {
                minhour = minhour + 1
                minminute = 0;
            }
            let maxhour = maxTime.getHours(); // 最大时间小时
            let maxminute = maxTime.getMinutes() - 1; // 最大时间分钟 需要比最大时间分钟小1 小时退位未处理
            if (maxminute < 0) {
                maxhour = maxhour - 1;
                maxminute = 59;
            }
            let selectableRange = '00:00:00 - 23:59:59'
            if (min && max) {
                if (date == minDay && date == maxDay) {
                    selectableRange = `${String(minhour).padStart(2, '0')}:${String(minminute).padStart(2, '0')}:00 - ${String(maxhour).padStart(2, '0')}:${String(maxminute).padStart(2, '0')}:59`
                } else if (date == minDay) {
                    selectableRange = `${String(minhour).padStart(2, '0')}:${String(minminute).padStart(2, '0')}:00 - 23:59:59`
                } else if (date == maxDay) {
                    selectableRange = `00:00:00 - ${String(maxhour).padStart(2, '0')}:${String(maxminute).padStart(2, '0')}:59`
                }
            } else if (min) {
                if (date == minDay) {
                    selectableRange = `${String(minhour).padStart(2, '0')}:${String(minminute).padStart(2, '0')}:00 - 23:59:59`
                }
            } else if (max) {
                if (date == maxDay) {
                    selectableRange = `00:00:00 - ${String(maxhour).padStart(2, '0')}:${String(maxminute).padStart(2, '0')}:59`
                }
            }
            this.selectableRange = selectableRange;
            this.timeChange(this.editTimeForm.optTime);
        }
    }
};
</script>

<style lang="scss" scoped>
.consignmentTime{
    color:#1000ff;
    cursor:pointer;
}
</style>
