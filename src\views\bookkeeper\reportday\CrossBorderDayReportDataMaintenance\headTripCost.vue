<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-input v-model.trim="ListInfo.containerCode" placeholder="批次号" maxlength="50" clearable
                    class="publicCss" />
                <el-button style="padding: 0;margin-right: 10px;border: none;float: left;">
                    <inputYunhan :key="'1'" :keys="'one'" :width="'220px'" ref="" :inputt.sync="ListInfo.goodsCodes"
                        v-model.trim="ListInfo.goodsCodes" placeholder="商品编码/若输入多条请按回车" :clearable="true"
                        @callback="callbackGoodsCode" title="商品编码" @entersearch="entersearch" :maxRows="100">
                    </inputYunhan>
                </el-button>
                <el-date-picker v-model="ListInfo.timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="费用开始日期" end-placeholder="费用结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                <!-- <el-select v-model="ListInfo.status" placeholder="消耗状态" style="width:120px;margin-right: 10px;" class="el-select-content"
                    clearable filterable>
                    <el-option v-for="item in DepletionTypeList" :key="item.value" :label="item.name" :value="item.value" />
                </el-select> -->

                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="onExport" style="margin-left: 5px;"
                    v-if="checkPermission('DailyDataMaintenance_kj_export')">导出</el-button>
                <el-button type="primary" @click="logisticsVisibleOpen">查看消耗明细</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :showsummary='true'
            :summaryarry='summaryarry' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>


        <!-- 查看消耗明细 -->
        <el-dialog title="消耗明细" :visible.sync="logisticsVisible" v-dialogDrag center>
            <div class="top" style="margin-bottom: 20px;">
                <el-input v-model.trim="logisticsQuery.goodsCode" placeholder="商品编码" maxlength="50" clearable
                    class="publicCss" />
                <el-date-picker v-model="logisticsQuery.timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="日报开始日期" end-placeholder="日报结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'" @change="changeTime1">
                </el-date-picker>
                <el-button type="primary" @click="logisticsListGet('search')">查询</el-button>
                <el-button type="primary" @click="onExportVisible" style="margin-left: 5px;">导出</el-button>
            </div>
            <vxetablebase :id="'headTripCostlogistics20241129'" :tablekey="'headTripCostlogistics20241129'"
                :tableData='logisticsData' :tableCols='logisticstableCols' :loading='logisticsLoading'
                @sortchange='logisticsSortchange' :border='true' :that="that" height="440px" ref="logisticstable"
                :showsummary='true' :summaryarry='logisticsSummaryarry'>
            </vxetablebase>
            <my-pagination ref="logisticsPage" :total="logisticsTotal" @get-page="logisticsListGet()" />

        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";
import { formatTime } from "@/utils";
import { GetTemu_Bantuo_FirstLegCost_Export, GetFirstLegDetail_Temu_BantuoPageList, GetTemu_Bantuo_FirstLegCostPageList, firstLegDetail_Temu_Bantuo_Export } from '@/api/bookkeeper/crossBorderV2'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'containerCode', label: '批次号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shelfTime', label: '费用日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'quantity', label: '数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'freightFee_Price', label: '运费单价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'freightFee', label: '运费', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'loadingFee_Price', label: '装车费单价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'loadingFee', label: '装车费', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'packCost_Price', label: '包装成本单价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'packCost', label: '包装成本', },

]

//消耗明细
const logisticstableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    {
        sortable: 'custom', width: 'auto', align: 'center', prop: 'yearMonthDayDate', label: '日报日期',
        formatter: (row) => {
            return row.yearMonthDayDate ? formatTime(row.yearMonthDayDate, "YYYY-MM-DD") : "";
        },
    },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'quantity', label: '数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'freightFee', label: '运费', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'loadingFee', label: '装车费', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'packCost', label: '包装成本', },
]
const DepletionTypeList = [
    { name: "未消耗", value: 0 },
    { name: "消耗中", value: 1 },
    { name: "已消耗", value: 2 },
]
export default {
    name: "headTripCost",
    components: {
        MyContainer, vxetablebase, inputYunhan
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                timeRanges: [],
            },
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            fileList: [],
            importLoading: false,
            importVisible: false,
            file: null,
            summaryarry: {},
            DepletionTypeList: DepletionTypeList,
            //查看消耗明细
            logisticsVisible: false,
            logisticsTotal: 0,
            logisticsData: [],
            logisticstableCols: logisticstableCols,
            logisticsLoading: false,
            logisticsQuery: {
                timeRanges: []
            },
            logisticsSummaryarry: {},
            godownList: [],
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async changeTime(e) {
            this.ListInfo.ShelfTimeStart = e ? e[0] : null
            this.ListInfo.ShelfTimeEnd = e ? e[1] : null
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            //没有时间就提示
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            form.append("billingType", 2);
            this.importLoading = true
            // await ImportIncidentalExpensesAsync(form).then(({ success }) => {
            //     if (success) {
            //         this.$message.success('导入成功')
            //         this.importVisible = false
            //         this.getList()
            //     }
            //     this.importLoading = false
            // }).catch(err => {
            //     this.importLoading = false
            //     this.$message.error('导入失败')
            // })
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            if (this.ListInfo.timeRanges != null && this.ListInfo.timeRanges.length == 0) {
                //默认给近7天时间
                this.ListInfo.ShelfTimeStart = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
                this.ListInfo.ShelfTimeEnd = dayjs().format('YYYY-MM-DD')
                this.ListInfo.timeRanges = [this.ListInfo.ShelfTimeStart, this.ListInfo.ShelfTimeEnd]
            }

            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetTemu_Bantuo_FirstLegCostPageList(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.summaryarry = data.summary
                    this.loading = false
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        async handleCommand(command) {
            switch (command) {
                //下载模版
                case 'a':
                    await this.downLoadFile()
                    break;
            }
        },
        async downLoadFile() {
            window.open("/static/excel/CrossBorderDailyDataMaintenance/头程导入模版.xlsx", "_blank");
        },

        async onExport() {//导出列表数据；
            var res = await GetTemu_Bantuo_FirstLegCost_Export(this.ListInfo);
            if (res?.success) {
                this.$message({ message: res.msg, type: "success" });
            }
        },
        //多条查询部分
        async entersearch(val) {
            this.getList('search');
        },
        async callbackGoodsCode(val) {
            this.ListInfo.goodsCodes = val;
        },

        //打开查看消耗明细列表 1
        async logisticsVisibleOpen() {
            this.logisticsVisible = true
            this.$nextTick(async () => {
                await this.logisticsListGet('search')
            })
        },
        // 获取列表 1
        async logisticsListGet(type) {
            if (type == 'search') {
                this.$refs.logisticsPage.setPage(1)
            }
            if (this.logisticsQuery.timeRanges != null && this.logisticsQuery.timeRanges.length == 0) {
                //默认给近7天时间
                this.logisticsQuery.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
                this.logisticsQuery.endDate = dayjs().format('YYYY-MM-DD')
                this.logisticsQuery.timeRanges = [this.logisticsQuery.startDate, this.logisticsQuery.endDate]
            }
            let pager = this.$refs.logisticsPage.getPager()
            let params = {
                ...this.logisticsQuery,
                ...pager,
            }
            this.logisticsLoading = true
            const { data } = await GetFirstLegDetail_Temu_BantuoPageList(params)  //改接口
            this.logisticsLoading = false

            this.logisticsTotal = data.total
            this.logisticsData = data.list
            this.logisticsSummaryarry = data.summary
        },
        //  排序 1
        logisticsSortchange({ order, prop }) {
            if (prop) {
                this.logisticsQuery.orderBy = prop
                this.logisticsQuery.isAsc = order.indexOf("descending") == -1 ? true : false
                this.logisticsListGet()
            }
        },
        // 明细时间 1
        async changeTime1(e) {
            this.logisticsQuery.startDate = e ? e[0] : null
            this.logisticsQuery.endDate = e ? e[1] : null
        },

        async onExportVisible() {//导出列表数据；
            if (this.logisticsQuery.timeRanges != null && this.logisticsQuery.timeRanges.length == 0) {
                //默认给近7天时间
                this.logisticsQuery.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
                this.logisticsQuery.endDate = dayjs().format('YYYY-MM-DD')
                this.logisticsQuery.timeRanges = [this.logisticsQuery.startDate, this.logisticsQuery.endDate]
            }
            let pager = this.$refs.logisticsPage.getPager()
            let params = {
                ...this.logisticsQuery,
                ...pager,
            }
            var res = await firstLegDetail_Temu_Bantuo_Export(params);
            if (res?.success) {
                this.$message({ message: res.msg, type: "success" });
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>
