<template>
  <container v-loading="pageLoading">
    <template #header>
       <div id="echartchangestatus" style="width: 100%;height: 389px; box-sizing:border-box; line-height: 360px;"/>
    </template>
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :isSelectColumn="false"
       :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
    </ces-table>
  </container>
</template>
<script>
import * as echarts from 'echarts';
import container from '@/components/my-container/nofooter'
import cesTable from "@/components/Table/table.vue";
import {getUnsalableChangeStatusAnalysis,getUnsalableChangeStatus} from '@/api/inventory/unsalable'
const tableCols =[
      {istrue:true,prop:'groupId',label:'运营组', width:'80',sortable:'custom',formatter:(row)=>row.groupName},
      {istrue:true,prop:'goodsCodeCount',label:'编码总个数', width:'105',sortable:'custom'},
      {istrue:true,prop:'goodCount',label:'编码变好个数', width:'105',sortable:'custom'},
      {istrue:true,prop:'badCount',label:'编码变坏个数', width:'105',sortable:'custom'},
      {istrue:true,prop:'goodRate',label:'变好占比%', width:'90',sortable:'custom'},
      {istrue:true,prop:'badRate',label:'变坏占比%', width:'90',sortable:'custom'},
      {istrue:true,prop:'goodStockAmont',label:'变好金额', width:'105',sortable:'custom'},
      {istrue:true,prop:'badStockAmont',label:'变坏金额', width:'105',sortable:'custom'},
      {istrue:true,prop:'totalAmont1',label:'期初库存金额', width:'105',sortable:'custom'},
      {istrue:true,prop:'totalAmont2',label:'期末库存金额', width:'105',sortable:'custom'},
     ];
const tableHandles=[
      ];
export default {
  name: 'Roles',
  components: {container,cesTable},
   props:{
       filter: { }
     },
  data() {
    return {
      that:this,
      filter1:{
        jpProCodes:[],
        leftY:0,
        rightY:1
      },
 
      jpprocodes:[],
      list: [],
      pager:{OrderBy:"GroupId",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles,
      pageLoading: false,
      listLoading:false
    }
  },
  mounted() {
  },
  beforeUpdate() {
  },
methods: {
   async onSearch() {
      if (!this.filter.date1) {this.$message({message: "请选择日期1",type: "warning",});return;}
      if (!this.filter.date2) {this.$message({message: "请选择日期2",type: "warning",});return;}   
     await this.getanalysisdata()
     await this.getlist()
    },
   async onfresh() {
     await this.onSearch()
    },
   async getlist() {
      const params = {...this.pager,... this.filter}
      this.listLoading = true
      const res = await getUnsalableChangeStatus(params)
      this.listLoading = false
      if (!res?.success)         return
      const data = res.data
      data.forEach(d => {
        d._loading = false;
      })
      this.list = data
      //var sum={goodsCodeCount  goodCount  badCount  goodStockAmont  badStockAmont  totalAmont1 totalAmont2}

    },
   async getanalysisdata() {
      var parm={...this.filter};       
      const res = await getUnsalableChangeStatusAnalysis(parm);      
      if (!res?.code)  return;       
      var chartDom = document.getElementById('echartchangestatus');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      if (!res.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
       }
      var option = this.Getoptions(res.data);
      option && myChart.setOption(option); 
    },
    Getoptions(element){
     var series=[]
     element.series.forEach(s=>{
       series.push({  smooth: true, ...s})
     })
     var yAxis=[]
     element.yAxis.forEach(s=>{
        yAxis.push({type: 'value',offset:s.offset,position:s.position,name: s.name,axisLabel: {formatter: '{value}'+s.unit}})
     })
     var option = {
        title: {text: element.title},
        tooltip: {trigger: 'axis'},
        legend: {data: element.legend},
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {feature: {
            magicType: {show: true, type: ['line', 'bar']},
            //restore: {show: true},
        }},
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis: yAxis,
        series:  series
    };
    return option;
   },
  async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.getlist();
    },
  }
}
</script>
<style>
.el-select-content { 
    width: calc(100% - 10px);
    margin: 0;
 }
 
</style>
