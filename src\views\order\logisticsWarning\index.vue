<template>
    <my-container>
        <el-tabs v-model="activeName" style="height: 95%;" type="card">
            <el-tab-pane label="发货快超时" name="one" style="height: 100%;">
                <logisticsEarlyWarPage ref="logisticsWar1" :tablekey="'logisticsWarOne'" :logisticsEarlyWarDataType="1" :rangeList="getRangeList(1)" :remainingTimeStr="'剩余发货时间'" :exportName="'发货快超时明细模板'" />
            </el-tab-pane>
            <el-tab-pane label="揽收快超时" name="two" style="height: 100%;">
                <logisticsEarlyWarPage ref="logisticsWar2" :tablekey="'logisticsWarTwo'" :logisticsEarlyWarDataType="2" :rangeList="getRangeList(2)" :remainingTimeStr="'剩余揽收时间'" :exportName="'揽收快超时明细模板'" />
            </el-tab-pane>
            <el-tab-pane label="订单已揽收" name="three" style="height: 100%;">
                <logisticsEarlyWarPage ref="logisticsWar3" :tablekey="'logisticsWarThree'" :logisticsEarlyWarDataType="3" :rangeList="getRangeList(3)" :remainingTimeStr="'剩余更新时间'" :exportName="'揽收未更新明细模板'" />
            </el-tab-pane>
            <!-- <el-tab-pane label="揽收未更新" name="three" style="height: 100%;">
                <logisticsEarlyWarPage ref="logisticsWar3" :tablekey="'logisticsWarThree'" :logisticsEarlyWarDataType="3" :rangeList="getRangeList(3)" :remainingTimeStr="'剩余更新时间'" :exportName="'揽收未更新明细模板'" />
            </el-tab-pane>
            <el-tab-pane label="预警汇总" name="four" style="height: 100%;">
                <logisticsEarlyWarStat ref="logisticsWar4" />
            </el-tab-pane>
            <el-tab-pane label="无效发货" name="five" style="height: 100%;">
                <invalidShipment ref="logisticsWar5" :tablekey="'logisticsWarFive'" />
            </el-tab-pane> -->

        </el-tabs>
    </my-container>
</template>
<script>
    import MyContainer from "@/components/my-container";
    import logisticsEarlyWarPage from '@/views/order/logisticsWarning/LogisticsEarlyWarPage'
    import logisticsEarlyWarStat from '@/views/order/logisticsWarning/LogisticsEarlyWarStat'
    import invalidShipment from '@/views/order/logisticsWarning/InvalidShipment'
    export default {
        name: 'logisticsWarningIndex',
        components: { MyContainer, logisticsEarlyWarPage, logisticsEarlyWarStat, invalidShipment },
        props: {

        },
        data () {
            return {
                activeName: "one",
            }
        },
        async mounted () {

        },
        methods: {
            getRangeList (tabtype) {
                var rangeList = [];
                if (tabtype == 1) {
                    rangeList.push("剩余发货时间0-4小时")
                    rangeList.push("剩余发货时间4-8小时")
                    rangeList.push("剩余发货时间8-12小时")
                } else if (tabtype == 2) {
                    rangeList.push("剩余揽收时间0-4小时")
                    rangeList.push("剩余揽收时间4-6小时")
                    rangeList.push("剩余揽收时间6-12小时")
                }
                return rangeList;
            }
        }
    }
</script>
