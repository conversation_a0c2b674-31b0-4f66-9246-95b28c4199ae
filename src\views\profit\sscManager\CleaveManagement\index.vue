<template>
    <MyContainer>
        <template #header>
        <div class="top">
          <!-- <el-date-picker v-model="ListInfo.calculateMonth" unlink-panels range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期"  type="month" style="width: 250px;margin-right: 5px;" :clearable="false"
            :value-format="'yyyy-MM'" >
          </el-date-picker> -->
          <el-date-picker v-model="ListInfo.calculateMonthArr" unlink-panels range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期"   type="monthrange" style="width: 250px;margin-right: 5px;" :clearable="false"
            :value-format="'yyyy-MM'" >
          </el-date-picker>

          <!-- <el-select v-model="ListInfo.isStock" placeholder="区域" class="publicCss" clearable>
            <el-option :key="'是'" label="是" :value="0" />
            <el-option :key="'否'" label="否" :value="1" />
          </el-select> -->
        <el-select v-model="ListInfo.type" placeholder="类型" class="publicCss" clearable @change="changeType">
          <el-option v-for="item in typeList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.regionName" placeholder="区域" class="publicCss" clearable>
          <el-option v-for="item in districtList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.deptType" placeholder="部门类型" class="publicCss" clearable>
          <el-option v-for="item in sectionList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.deptName" placeholder="部门" class="publicCss" clearable>
          <el-option v-for="item in deptList" :key="item" :label="item" :value="item" />
        </el-select>

          <!-- <el-input v-model.trim="ListInfo.orderNo" placeholder="退件快递单号" maxlength="50" clearable class="publicCss" />
          <el-input v-model.trim="ListInfo.orderNoInner" placeholder="原订单号" maxlength="50" clearable class="publicCss" /> -->
          <el-button type="primary" @click="getList('search')">查询</el-button>
          <!-- <el-button type="primary" @click="startImport">导入</el-button> -->
          <el-button type="primary" @click="startImport" v-if="checkPermission('ArchiveStatusEditing')">导入</el-button>
          <el-button type="primary" @click="startImport" v-else :disabled="timeCundang">导入</el-button>
          <el-button type="primary" @click="downExcel">模板下载</el-button>
          <el-button type="primary" @click="exportExcel('search')">导出</el-button>
          <el-button type="primary" @click="saveBane('search')">存档</el-button>
          <div style="color: red; margin-left: 5px;">
            存档时间：{{timeCundang??'-'}}
          </div>

          <!-- <div class="user-info-block" @click="notifyMe">
            <el-avatar shape="square" :size="100" :fit="fit" :src="'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'"></el-avatar>
          </div> -->

        </div>
      </template>
      <vxe-table
        border
        show-footer
        width="100%"
        height="100%"
        ref="newtable"
        :row-config="{height: 40}"
        show-overflow
        :loading="loading"
        :column-config="{resizable: true}"
        :footer-span-method="footerSpanMethod"
        :footer-data="footerData"
        :span-method="mergeRowMethod"
        :cell-class-name="cellClassName"
        :data="tableData">
        <vxe-column width="70" type="seq"></vxe-column>

        <vxe-column field="calculateMonth" width="80" title="月份">
          <template #footer="{ items, _columnIndex, row }">
            <span v-if="mergeColumn?.column?.includes('calculateMonth')" class="display_centered">
              {{ getMergeDisplayValue(row) }}
            </span>
            <span v-else>{{ items[_columnIndex] }}</span>
          </template>
        </vxe-column>
        <vxe-column field="type" width="70" title="类型">
          <template #footer="{ items, _columnIndex, row }">
            <span v-if="mergeColumn?.column?.includes('type')" class="display_centered">
              {{ getMergeDisplayValue(row) }}
            </span>
            <span v-else>{{ items[_columnIndex] }}</span>
          </template>
        </vxe-column>

        <vxe-column field="regionName" width="80" title="区域">
          <template #footer="{ items, _columnIndex, row }">
            <span v-if="mergeColumn?.column?.includes('regionName')" class="display_centered">
              {{ getMergeDisplayValue(row) }}
            </span>
            <span v-else>{{ items[_columnIndex] }}</span>
          </template>
        </vxe-column>
        <vxe-column field="deptType" width="80" title="部门类型" >
          <template #footer="{ items, _columnIndex, row }">
            <span v-if="mergeColumn?.column?.includes('deptType')" class="display_centered">
              {{ getMergeDisplayValue(row) }}
            </span>
            <span v-else>{{ items[_columnIndex] }}</span>
          </template>
        </vxe-column>
        <vxe-column field="deptName" width="80" title="部门" >
          <template #footer="{ items, _columnIndex, row }">
            <span v-if="mergeColumn?.column?.includes('deptName')" class="display_centered">
              {{ getMergeDisplayValue(row) }}
            </span>
            <span v-else>{{ items[_columnIndex] }}</span>
          </template>
        </vxe-column>
        <vxe-column field="monthCount" width="80" title="月离职人数"  ></vxe-column>

        <vxe-colgroup title="在职天数分析" align="center">
            <vxe-column field="day7Count" width="80" title="7天内离职人数"  >
                <template #header>
                    <div>
                        7天内<br/>离职人数
                    </div>
                </template>
            </vxe-column>
            <vxe-column field="day15Count" width="80" title="8~15天离职人数"  >
                <template #header>
                    <div>
                        8~15天<br/>离职人数
                    </div>
                </template>
            </vxe-column>
            <vxe-column field="day30Count" width="80" title="16~30天离职人数"  >
                <template #header>
                    <div>
                        16~30天<br/>离职人数
                    </div>
                </template>
            </vxe-column>
            <vxe-column field="day90Count" width="80" title="30天以上离职人数"  >
                <template #header>
                    <div>
                        30天以上<br/>离职人数
                    </div>
                </template>
            </vxe-column>
            <vxe-column field="yearCount" width="80" title="1年以上离职人数"  >
                <template #header>
                    <div>
                        1年以上<br/>离职人数
                    </div>
                </template>
            </vxe-column>
            <!-- <vxe-column field="twoYearCount" width="80" title="2年以上离职人数"  >
                <template #header>
                    <div>
                        2年以上<br/>离职人数
                    </div>
                </template>
            </vxe-column> -->
        </vxe-colgroup>

        <vxe-colgroup title="离职类型" align="center">
            <vxe-column field="resignCount" width="80" title="辞职"  ></vxe-column>
            <vxe-column field="quitCount" width="80" title="劝退"  ></vxe-column>
            <vxe-column field="selfDeparture" width="80" title="自离"  ></vxe-column>
        </vxe-colgroup>

        <vxe-colgroup title="员工结构" align="center">
            <vxe-column field="probationQuitCount" width="80" title="试用期离职"  ></vxe-column>
            <vxe-column field="regularQuitCount" width="80" title="正式离职"  ></vxe-column>
        </vxe-colgroup>

        <vxe-colgroup title="离职原因分析" align="center">
            <vxe-colgroup title="职业发展有变" align="center">
                <vxe-column field="hasWorkCount" width="80" title="人数"  ></vxe-column>
                <vxe-column field="hasWorkRate" width="80" title="占比"  >
                    <template slot-scope="scope">
                        {{scope.row.hasWorkRate?scope.row.hasWorkRate+'%':'0%'}}
                    </template>
                </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="淘汰" align="center">
                <vxe-column field="eliminateCount" width="80" title="人数"  ></vxe-column>
                <vxe-column field="eliminateRate" width="80" title="占比"  >
                    <template slot-scope="scope">
                        {{scope.row.eliminateRate?scope.row.eliminateRate+'%':'0%'}}
                    </template>
                </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="不适应工作" align="center">
                <vxe-column field="highStressCount" width="80" title="人数"  ></vxe-column>
                <vxe-column field="highStressRate" width="80" title="占比"  >
                    <template slot-scope="scope">
                        {{scope.row.highStressRate?scope.row.highStressRate+'%':'0%'}}
                    </template>
                </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="家庭原因" align="center">
                <vxe-column field="familyReasonsCount" width="80" title="人数"  ></vxe-column>
                <vxe-column field="familyReasonsRate" width="80" title="占比"  >
                    <template slot-scope="scope">
                        {{scope.row.familyReasonsRate?scope.row.familyReasonsRate+'%':'0%'}}
                    </template>
                </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="身体原因" align="center">
                <vxe-column field="bodyReasonsCount" width="80" title="人数"  ></vxe-column>
                <vxe-column field="bodyReasonsRate" width="80" title="占比"  >
                    <template slot-scope="scope">
                        {{scope.row.bodyReasonsRate?scope.row.bodyReasonsRate+'%':'0%'}}
                    </template>
                </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="其他原因" align="center">
                <vxe-column field="otherCount" width="80" title="人数"  ></vxe-column>
                <vxe-column field="otherCountRate" width="80" title="占比"  >
                    <template slot-scope="scope">
                        {{scope.row.otherCountRate?scope.row.otherCountRate+'%':'0%'}}
                    </template>
                </vxe-column>
            </vxe-colgroup>

            <vxe-colgroup title="自离原因" align="center">
                <vxe-column field="resignationOneselfCount" width="80" title="人数"  ></vxe-column>
                <vxe-column field="resignationOneselfRate" width="80" title="占比"  >
                    <template slot-scope="scope">
                        {{scope.row.resignationOneselfRate?scope.row.resignationOneselfRate+'%':'0%'}}
                    </template>
                </vxe-column>
            </vxe-colgroup>

            <vxe-colgroup title="单量原因" align="center">
                <vxe-column field="orderQuantityCount" width="80" title="人数"  ></vxe-column>
                <vxe-column field="orderQuantityRate" width="80" title="占比"  >
                    <template slot-scope="scope">
                        {{scope.row.orderQuantityRate?scope.row.orderQuantityRate+'%':'0%'}}
                    </template>
                </vxe-column>
            </vxe-colgroup>
        </vxe-colgroup>



        <vxe-colgroup title="离职成本分析" align="center">
            <vxe-column field="totalResignationCost" width="80" title="总离职成本" footer-align="left"></vxe-column>
            <vxe-column field="perResignationCost" width="80" title="人均离职成本" footer-align="left"></vxe-column>
        </vxe-colgroup>



        <!-- <vxe-colgroup title="离职主要类型占比" align="center">
            <vxe-colgroup title="职业发展有变/升学 去外地/家里安排工作" align="center">
                <vxe-column field="hasWorkCount" width="100" title="人数"  ></vxe-column>
                <vxe-column field="hasWorkRate" width="130" title="占比"  >
                    <template slot-scope="scope">
                        {{scope.row.hasWorkRate?scope.row.hasWorkRate+'%':'0'}}
                    </template>
                </vxe-column>
            </vxe-colgroup>





            <vxe-colgroup title="部门解散/优化/工作量不饱和" align="center">
                <vxe-column field="deptDissolutionCount" width="100" title="人数"  ></vxe-column>
                <vxe-column field="deptDissolutionRate" width="130" title="占比"  >
                    <template slot-scope="scope">
                        {{scope.row.deptDissolutionRate?scope.row.deptDissolutionRate+'%':'0'}}
                    </template>
                </vxe-column>
            </vxe-colgroup>

            <vxe-colgroup title="违反规章制度/工作失误" align="center">
                <vxe-column field="workErrorCount" width="100" title="人数"  ></vxe-column>
                <vxe-column field="workErrorRate" width="130" title="占比"  >
                    <template slot-scope="scope">
                        {{scope.row.workErrorRate?scope.row.workErrorRate+'%':'0'}}
                    </template>
                </vxe-column>
            </vxe-colgroup>
        </vxe-colgroup>



        <vxe-colgroup title="员工结构" align="center">
            <vxe-column field="probationQuitCount" width="100" title="试用期离职"  ></vxe-column>
            <vxe-column field="regularQuitCount" width="100" title="正式离职"  ></vxe-column>
        </vxe-colgroup> -->



        <!-- <vxe-column field="age" title="转正人数" footer-align="left"></vxe-column>

        <vxe-column field="rate1" title="晋升人数" footer-align="left"></vxe-column>
        <vxe-column field="age2" title="异动合计" footer-align="left"></vxe-column>
        <vxe-column field="rate3" title="参保人数" footer-align="left"></vxe-column>
        <vxe-column field="age4" title="参保占比" footer-align="left"></vxe-column>

        <vxe-column field="rate5" title="企业社保费用" footer-align="left"></vxe-column> -->
        <vxe-column title="操作" footer-align="left" width="100" fixed="right">
            <template slot-scope="scope">
              <!-- <el-button type="text" size="mini" :disabled="scope.row.status==1" v-if="scope.row.regionName.indexOf('合计')==-1" @click="handleEdit(scope.$index, scope.row)">编辑</el-button> -->
              <el-button type="text" size="mini" v-if="!scope.row.deptType || scope.row.deptType.indexOf('小计') === -1" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
              <el-button type="text" size="mini" style="color:red" v-if="!scope.row.deptType || scope.row.deptType.indexOf('小计') === -1" @click="handleRemove(scope.$index, scope.row)">删除</el-button>
              <!-- <el-button type="text" size="mini" :disabled="scope.row.status==1" v-if="scope.row.deptName.indexOf('小计')==-1" @click="handleEdit(scope.$index, scope.row)">编辑</el-button> -->
              <!-- <el-button type="text" style="color: red" size="mini" @click="handleDelete(scope.$index, scope.row)">删除</el-button> -->
            </template>
        </vxe-column>
      </vxe-table>
      <template #footer>
        <div class="footer-container">
          <span class="total-count-badge">
            共{{ total || 0 }}条
          </span>
        </div>
      </template>
      <!-- <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template> -->
      <el-drawer title="编辑" :visible.sync="dialogVisibleEdit" size="25%">
        <departmentEdit ref="departmentEdit" v-if="dialogVisibleEdit" :editInfo="editInfo" @search="closeGetlist"
            @cancellationMethod="dialogVisibleEdit = false" />
      </el-drawer>
      <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
        <span>
            <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
            accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
            <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
        </span>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
        </el-dialog>
    </MyContainer>
  </template>

  <script>
  import MyContainer from "@/components/my-container";
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import { pickerOptions } from '@/utils/tools'
  import dayjs from 'dayjs'
  import { downloadLink } from "@/utils/tools.js";
  import { dimissionManagePage, dimissionManageArchive, dimissionManageImport, dimissionManageRemove } from '@/api/people/peoplessc.js';
  import departmentEdit from "./departmentEdit.vue";
  import checkPermission from '@/utils/permission'
  import tableFooterMerge from "@/views/profit/sscManager/tableFooterMerge.js";
  const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '审批状态', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNoInner', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'importTime', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'planSendTime', label: '原价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'timePay', label: '现价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight', label: '进货数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderStatus', label: '涨价原因', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'seriesName', label: '添加日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCount', label: '涨价日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '添加人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '岗位', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '分公司', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人组长', },
  ]
  export default {
    name: "scanCodePage",
    mixins: [tableFooterMerge],
    components: {
      MyContainer, vxetablebase, departmentEdit
    },
    data() {
        const tableData = [
    //   { id: 10001, calculateMonth: '12月', regionName: '义务', name: 'Test1', nickname: 'T1', role: 'Develop', sex: 'Man', age: 28, address: 'test abc' },
    //   { id: 10002, calculateMonth: '12月', regionName: '义务', name: 'Test2', nickname: 'T2', role: 'Test', sex: 'Women', age: 22, address: 'Guangzhou' },
    //   { id: 10003, calculateMonth: '12月', regionName: '南昌', name: 'Test3', nickname: 'T3', role: 'PM', sex: 'Man', age: 32, address: 'Shanghai' },
    //   { id: 10004, calculateMonth: '12月', regionName: '南昌', name: 'Test4', nickname: 'T4', role: 'Designer', sex: 'Women', age: 23, address: 'test abc' },
    //   { id: 10005, calculateMonth: '12月', regionName: '北京', name: 'Test5', nickname: 'T5', role: 'Develop', sex: 'Women', age: 30, address: 'Shanghai' },
    //   { id: 10006, calculateMonth: '12月', regionName: '北京', name: 'Test6', nickname: 'T6', role: 'Designer', sex: 'Women', age: 21, address: 'test abc' },
    //   { id: 10007, calculateMonth: '12月', regionName: '深圳', name: 'Test7', nickname: 'T7', role: 'Test', sex: 'Man', age: 29, address: 'test abc' },
    //   { id: 10008, calculateMonth: '12月', regionName: '深圳', name: 'Test8', nickname: 'T8', role: 'Develop', sex: 'Man', age: 35, address: 'test abc' }
    ]
    const footerData = [
    //   { calculateMonth: '12月', regionName: '办公室合计', name: '办公室合计', role: '33', rate: '56' },
    //   { calculateMonth: '12月', regionName: '仓储合计', name: '仓储合计', role: 'bb', rate: '56' },
    //   { calculateMonth: '12月', regionName: '全区域合计', name: '全区域合计', role: 'bb', rate: '1235' }
    ]
      return {
        mergeColumn: {
          column: ['calculateMonth', 'type', 'regionName', 'deptName', 'deptType'], // 需要合并的列
          default: 'type' // 默认显示字段
        },
        downloadLink,
        dialogVisibleEdit: false,
        editInfo: {},
        fileList: [],
        dialogVisible: false,
        districtList: [],
        allDistrictList: [],
        typeList: [],
        sectionList: [],
        deptList: [],
        timeCundang: '',
        tableData,
        footerData,
        somerow: 'type,regionName,calculateMonth',
        that: this,
        ListInfo: {
            calculateMonthArr: [dayjs().subtract(0, 'month').format('YYYY-MM'), dayjs().subtract(0, 'month').format('YYYY-MM')]
        //   currentPage: 1,
        //   pageSize: 50,
        //   orderBy: null,
        //   isAsc: false,
        //   startTime: null,//开始时间
        //   endTime: null,//结束时间
        },
        timeRanges: [],
        tableCols,
        summaryarry: {},
        total: 0,
        loading: false,
        pickerOptions,
      }
    },
    async mounted() {
      await this.getList()
    },
    methods: {
        changeType(e){
          if (!e) {
            this.districtList = [...this.allDistrictList];
            return;
          }
          if (e === '办公室') {
            this.districtList = this.allDistrictList.filter(item => !item.includes('仓'));
          } else {
            this.districtList = this.allDistrictList.filter(item => item.includes('仓'));
          }
        },
        cellClassName(val){
            if(val && val.row && val.row.deptType && val.row.deptType.indexOf("小计") > -1){
                return 'coloryellow'
            }
            return ''
        },
        notifyMe() {
            if (!("Notification" in window)) {
                // 检查浏览器是否支持通知
                alert("当前浏览器不支持桌面通知");
            } else if (Notification.permission === "granted") {
                // 检查是否已授予通知权限；如果是的话，创建一个通知
                const notification = new Notification("你好！");
                // …
            } else if (Notification.permission === "denied") {
                // 我们需要征求用户的许可
                Notification.requestPermission().then((permission) => {
                // 如果用户接受，我们就创建一个通知
                if (permission === "granted") {
                    const notification = new Notification("你好！");
                    // …
                }
                });
            }
            console.log(Notification.permission,"点击通知");
            // 最后，如果用户拒绝了通知，并且你想尊重用户的选择，则无需再打扰他们
        },
        //上传文件
        onUploadRemove(file, fileList) {
        this.fileList = []
        },
        async onUploadChange(file, fileList) {
        this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
        fileList.splice(fileList.indexOf(file), 1);
        this.fileList = [];
        this.dialogVisible = false;
        },
        async onUploadFile(item) {
        if (!item || !item.file || !item.file.size) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
        }
        this.uploadLoading = true
        const form = new FormData();
        form.append("file", item.file);
        form.append("isArchive", checkPermission("ArchiveStatusEditing"));
        form.append("calculateMonth", this.ListInfo.calculateMonth);
        var res = await dimissionManageImport(form);
        if (res?.success){
            this.$message({ message: res.msg, type: "success" });
        }
        this.uploadLoading = false
        this.dialogVisible = false;
        await this.getList()
        },
        onSubmitUpload() {
        if (this.fileList.length == 0) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
        }
        this.$refs.upload.submit();
        },
        //导入弹窗
        startImport() {
        this.fileList = []
        this.dialogVisible = true;
        },
        downExcel(){
            //下载excel
            downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250729/1950084166972841984.xlsx', '离职数据分析导入模板.xlsx');
        },
        async saveBane(){
              this.$confirm('是否存档？存档后不可修改！', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const { data, success } = await dimissionManageArchive(this.ListInfo)
                    if(!success){
                        return;
                    }
                    this.getList();
                    this.$message.success('保存存档成功！')

                }).catch(() => {
                    // this.$message.error('取消')
                });
        },
        exportExcel(){
            this.$refs.newtable.exportData({filename:'离职数据分析',    sheetName: 'Sheet1',type: 'xlsx' })
        },
        closeGetlist(){
            this.dialogVisibleEdit = false;
            this.getList()
        },
        handleEdit(index, row){
            this.editInfo = row;
            this.dialogVisibleEdit = true;
        },
        async handleRemove(index, row) {
          this.$confirm('是否删除！', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async () => {
            this.editInfo = row;
            this.editInfo.calculateMonthStart = this.ListInfo.calculateMonthArr[0]
            this.editInfo.calculateMonthEnd = this.ListInfo.calculateMonthArr[1]
            this.loading = true
            const {data, success} = await dimissionManageRemove(this.editInfo)
            this.loading = false
            if (success) {
              this.$message.success('删除成功')
              this.getList();
            } else {
              this.$message.error('删除失败')
            }
          }).catch(() => {

          });
        },
        // 通用行合并函数（将相同多列数据合并为一行）
        mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
            const fields = this.somerow.split(',')
            const cellValue = row[column.property]
            if (cellValue && fields.includes(column.property)) {
            const prevRow = visibleData[_rowIndex - 1]
            let nextRow = visibleData[_rowIndex + 1]
            if (prevRow && prevRow[column.property] === cellValue) {
                return { rowspan: 0, colspan: 0 }
            } else {
                let countRowspan = 1
                while (nextRow && nextRow[column.property] === cellValue) {
                nextRow = visibleData[++countRowspan + _rowIndex]
                }
                if (countRowspan > 1) {
                return { rowspan: countRowspan, colspan: 1 }
                }
            }
            }
        },
      async changeTime(e) {
        this.ListInfo.startTime = e ? e[0] : null
        this.ListInfo.endTime = e ? e[1] : null
      },
      //导出数据,使用时将下面的方法替换成自己的接口
      // async exportProps() {
      //     const { data } = await exportStatData(this.ListInfo)
      //     const aLink = document.createElement("a");
      //     let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      //     aLink.href = URL.createObjectURL(blob)
      //     aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
      //     aLink.click()
      // },
      async getList(type) {
        // if (type == 'search') {
        //   this.ListInfo.currentPage = 1
        //   this.$refs.pager.setPage(1)
        // }
        if (this.ListInfo.calculateMonthArr && this.ListInfo.calculateMonthArr.length > 0) {
            this.ListInfo.calculateMonthStart = this.ListInfo.calculateMonthArr[0]
            this.ListInfo.calculateMonthEnd = this.ListInfo.calculateMonthArr[1]
        }
        this.loading = true
        const { data, success } = await dimissionManagePage(this.ListInfo)
        if (success) {
        //   data.list.map((row)=>{
        //     row.twoYearRate =  row.twoYearRate === 100 ? "100%" : row.twoYearRate ? Number(row.twoYearRate).toFixed(2) + "%" : ''
        //     row.highStressRate =  row.highStressRate === 100 ? "100%" : row.highStressRate ? Number(row.highStressRate).toFixed(2) + "%" : ''
        //     row.hasWorkRate =  row.hasWorkRate === 100 ? "100%" : row.hasWorkRate ? Number(row.hasWorkRate).toFixed(2) + "%" : ''

        //     row.familyReasonsRate =  row.familyReasonsRate === 100 ? "100%" : row.familyReasonsRate ? Number(row.familyReasonsRate).toFixed(2) + "%" : ''
        //     row.bodyReasonsRate =  row.bodyReasonsRate === 100 ? "100%" : row.bodyReasonsRate ? Number(row.bodyReasonsRate).toFixed(2) + "%" : ''
        //     row.eliminateRate =  row.eliminateRate === 100 ? "100%" : row.eliminateRate ? Number(row.eliminateRate).toFixed(2) + "%" : ''
        //   })
          this.tableData = data.list
        this.total = data.total
        //   this.summaryarry = data.summary
        data.summary.regionName = '合计';
        data.summary.calculateMonth = data.list.length>0? data.list[0].calculateMonth : '';
        // this.timeCundang = data.list.length>0?data.list[0].archiveTime:''
        if (data.summary) {
          this.timeCundang = data.summary.archiveTime
        }

        let zhanbi = [
            'eliminateRate',           // 淘汰占比
            'bodyReasonsRate',         // 身体原因占比
            'familyReasonsRate',       // 家庭原因占比
            'hasWorkRate',             // 职业发展有变占比
            'highStressRate',      // 不适应工作占比
            'otherCountRate',          // 其他原因占比
            'resignationOneselfRate',  // 自离原因占比
            'orderQuantityRate'        // 单量原因占比
        ];
        data.summary.forEach(summaryItem => {
          zhanbi.forEach(key => {
            if (summaryItem[key] != null && summaryItem[key] !== '') {
              summaryItem[key] = `${summaryItem[key]}%`;
            } else {
              summaryItem[key] = '0%';
            }
          });
        });

        this.footerData = data.summary;
        this.$nextTick(() => {
          if (this.$refs.newtable) {
            this.$refs.newtable.reloadData(this.tableData);
          }
        });


        // this.footerData = [data.summary]
        // const fieldsToFormat = [
        //     "monthCount",
        //     "day7Count",
        //     "day15Count",
        //     "day30Count",
        //     "day90Count",
        //     "yearCount",
        //     "twoYearCount",
        //     "resignCount",
        //     "quitCount",
        //     "selfDeparture",
        //     "probationQuitCount",
        //     "regularQuitCount",
        //     "totalResignationCost",
        //     "perResignationCost",
        //   ];
        //   this.footerData.forEach((item) => {
        //     fieldsToFormat.forEach((field) => {
        //       if (item[field] !== null && item[field] !== undefined) {
        //         item[field] = this.formatNumberWithThousandSeparator(item[field]);
        //       }
        //     });
        //     // zhanbi.indexOf()

        //   });
          console.log("111111", this.footerData)
        //

        //取列表中的区域
        const newDistricts = this.tableData.map(item => item.regionName).filter(district => district !== undefined && district !== null && typeof district === 'string' && district.indexOf('合计')==-1)
        this.allDistrictList = Array.from(new Set([...this.allDistrictList, ...newDistricts]));
        if (!this.ListInfo.type) {
          this.districtList = [...this.allDistrictList];
        }
        const newTypes = this.tableData.map(item => item.type).filter(district => district !== undefined && district !== null)
        this.typeList = Array.from(new Set([...this.typeList, ...newTypes]));
        const newsections = this.tableData.map(item => item.deptType).filter(section => section !== undefined && section !== null && section != '小计')
        this.sectionList = Array.from(new Set([...this.sectionList, ...newsections]));

        const newdept = this.tableData.map(item => item.deptName).filter(dept => dept !== undefined && dept !== null && dept != '小计')
        this.deptList = Array.from(new Set([...this.deptList, ...newdept]));

          this.loading = false
        } else {
          this.loading = false
          this.$message.error('获取列表失败')
        }
      },
      formatNumberWithThousandSeparator(value){
        if (value === null || value === undefined) return value;
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order && order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }
  </script>

  <style scoped lang="scss">
  .top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
      width: 150px;
      margin-right: 5px;
    }
  }

  :deep(.vxe-header--column){
    background: #00937e;
    color: white;
    font-weight: 600;
}
:deep(.vxe-footer--row){
    background: #00937e;
    color: white;
    font-weight: 600;
}
// .user-info-block:hover{
//     transform: rotate(666);
//     transition-delay: 1s;
//     transform-property: all;
//     transform-duration: 59s;
//     transition-timing-function: cubic-bezier(0.34, 0, 0.84, 1);
// }
.user-info-block:hover {
  animation: spin-accelerate 3s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  transform-origin: center center;
}

@keyframes spin-accelerate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(1080deg); /* 3圈 */
  }
}
:deep(.coloryellow){
        background: #fff2cc;
        color: black;
        font-weight: 600;
    }

.footer-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 8px 3px;
  background-color: #fafafa;
  border-top: 1px solid #e6e6e6;
}

.total-count-badge {
  display: inline-block;
  font-size: 13px;
  min-width: 35.5px;
  height: 28px;
  line-height: 28px;
  vertical-align: top;
  box-sizing: border-box;
  color: #606266;
  background-color: #f5f7fa;
  padding: 0 2px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.display_centered {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
  </style>
