<template>
  <div style="display: flex; justify-content: space-between;">
    <div style="flex: 1;">
      <el-select v-model="ListInfo.expressCompanyFullName" clearable filterable placeholder="快递公司名称" class="publicCss"
        :style="{ width: width }" @change="getExpressCompany($event)">
        <el-option v-for="(item, i) in expressCompany" :key="item.value + '-' + i" :label="item.label"
          :value="item.label" />
      </el-select>
      <el-select v-model="ListInfo.expressCompanyId" clearable filterable placeholder="快递公司" class="publicCss"
        :style="{ width: width }" @change="getprosimstatelist($event)">
        <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-select v-model="ListInfo.prosimstateId" clearable filterable placeholder="快递站点" class="publicCss"
        :style="{ width: width }" @clear="handleClear">
        <el-option label="暂无站点" value="" />
        <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
      </el-select>
      <el-select v-model="ListInfo.warehouseId" clearable filterable placeholder="发货仓库" class="publicCss"
        :style="{ width: width }">
        <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </div>
  </div>
</template>

<script>
import { warehouselist, formatWarehouseNew } from "@/utils/tools";
import { getExpressComanyAll, getExpressComanyStationName, getExpressBankInfoList } from "@/api/express/express";

export default {
  name: 'queryCondition',
  props: {
    width: {
      type: String,
      default() {
        return '120px';
      }
    },
    clearable: {
      type: Boolean,
      default() { return true; }
    },
    valueChanged: {
      type: Object,
      default() {
        return {}
      }
    },
  },
  data() {
    return {
      warehouselist,
      prosimstatelist: [],
      expresscompanylist: [],
      expressCompany: [],
      expressData: [],
      ListInfo: {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
        expressCompanyFullName: null
      }
    }
  },

  watch: {
    ListInfo: {
      handler: function (newValue, oldValue) {
        this.$emit("update:valueChanged", newValue);
      },
      deep: true
    }
  },

  async mounted() {

  },
  methods: {
    handleClear() {
      this.ListInfo.prosimstateId = null;
    },
    async init() {
      const res = await getExpressComanyAll({});
      if (!res?.success) return
      this.expresscompanylist = res.data;
      const { data, success } = await getExpressBankInfoList({});
      if (!success) return
      this.expressData = data.list;
      this.expressCompany = Array.from(new Set(this.expressData.map(item => ({ label: item.expressCompanyFullName, value: item.expressCompanyId }))));
    },
    async getExpressCompany(e) {
      var itemInfo = {};
      this.expressData.forEach(item => {
        if (item.expressCompanyFullName == e) {
          itemInfo = item;
        }
      });
      if (itemInfo && typeof itemInfo === 'object' && Object.keys(itemInfo).length > 0) {
        let a = (itemInfo.expressCompanyId).toString();
        this.ListInfo.expressCompanyId = a;
        await this.getprosimstatelist(a, 1);
        this.ListInfo.warehouseId = itemInfo.warehouseId;
        this.ListInfo.prosimstateId = (itemInfo.prosimstateId).toString();
      } else {
        this.ListInfo.expressCompanyId = null;
        this.ListInfo.warehouseId = null;
        this.ListInfo.prosimstateId = null;
        this.prosimstatelist = [];
      }
    },
    async getprosimstatelist(e, val) {
      var res = await getExpressComanyStationName({ id: e });
      if (res?.code) {
        this.prosimstatelist = res.data;
      }
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  margin-right: 5px;
}
</style>
