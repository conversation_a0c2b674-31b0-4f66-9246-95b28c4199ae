<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss"
                    :clearable="false" />
                <inputYunhan ref="goodsCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode"
                    placeholder="商品编码/若输入多条请按回车" :clearable="false" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="proCodeCallback" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <el-select v-model="ListInfo.type" placeholder="类型" class="publicCss" clearable>
                    <el-option key="盘点" label="盘点" value="盘点" />
                    <el-option key="其它进仓" label="其它进仓" value="其它进仓" />
                    <el-option key="其它出库" label="其它出库" value="其它出库" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps">导出</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' border
            :summaryarry='summaryarry' :showsummary='true' @sortchange='sortchange' :tableData='tableData'
            :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            style="width: 100%; height:600px; margin: 0" :loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { getShipperFxGoodsRptPdDetailList, exportShipperFxGoodsRptPdDetailList } from '@/api/order/shipperFxOrder';
import inputYunhan from "@/components/Comm/inputYunhan";
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'io_date', label: '单据日期', formatter: (row) => row.io_date ? dayjs(row.io_date).format('YYYY-MM-DD HH:mm:ss') : row.io_date },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'io_id', label: '进出仓单号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'type', label: '类型', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'sku_id', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQty', label: '数量' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'cost', label: '金额' }
]
export default {
    name: "YunHanShipperInventoryPdDetail",
    components: {
        MyContainer, vxetablebase, dateRange, inputYunhan
    },
    props: {
        filter: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                goodsCode: this.filter.goodsCode ? this.filter.goodsCode : null,
                startTime: this.filter.startTime ? dayjs(this.filter.startTime).format('YYYY-MM-DD') : null,//开始时间
                endTime: this.filter.endTime ? dayjs(this.filter.endTime).format('YYYY-MM-DD') : null,//结束时间
                type: ''
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            summaryarry: {}
        }
    },
    async mounted() {
        await this.getList();
    },
    methods: {
        proCodeCallback(val) {
            this.ListInfo.goodsCode = val
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportShipperFxGoodsRptPdDetailList(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '分销库存盘点明细-' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getShipperFxGoodsRptPdDetailList(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.summaryarry = data.summary
                    this.total = data.total
                    this.loading = false
                }
            } catch (error) {

            }
            this.loading = false
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
