<template>
    <container>
        <el-tabs v-model="activeName" tab-position="top" @tab-click="handleClick" style="height: 94%;">
            <el-tab-pane lazy label="图片" name="first" style="height: 100%;">
                <el-row :v-loading="listLoading">
                    <el-col :span="6" v-for="(item, index) in list" :key="index">
                        <el-card
                            style="height: 320px; width: 300px; margin-bottom: 20px; box-shadow: 20px 0px 10px 0px rgba(0,0,0,0.4)"
                            body-style="padding: 0px">
                            <div style="height: 270px; width: 300px;  position: relative;">
                                <img :src="item" class="sacimg" @click="onshowMv(item)"
                                    style="max-width: 420PX; max-height: 235PX;">
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
            </el-tab-pane>
            <el-tab-pane lazy label="入库质检备注" name="second" style="height: 100%;">
                <div class="block">
                    <el-timeline>
                        <el-timeline-item
                        v-for="(activity, index) in videoRemarks"
                        :key="index"
                        :timestamp="activity.createdTime">
                         创建人：{{ activity.createdUserName }} 备注：{{activity.remark}}
                        </el-timeline-item>
                    </el-timeline>
                </div>
            </el-tab-pane>
            <el-tab-pane lazy label="质检拍照备注" name="third" style="height: 100%;">
                <div class="block">
                    <el-timeline>
                        <el-timeline-item
                        v-for="(activity, index) in videoRemarks"
                        :key="index"
                        :timestamp="activity.createdTime">
                         创建人：{{ activity.createdUserName }} 备注：{{activity.remark}}
                        </el-timeline-item>
                    </el-timeline>
                </div>
            </el-tab-pane>
        </el-tabs>


        <!-- 图片信息 -->
        <el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeVideoPlyer" style="z-index:9999;" />
    </container>
</template>

<script>
import container from "@/components/my-container"
import MyConfirmButton from "@/components/my-confirm-button";
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import { getPurchaseQualityGoodsRecordImgs } from "@/api/inventory/purchasequality"
import { getWarehousingVideoRemarkAsync } from "@/api/inventory/warehousingordervide"

export default {
    name: 'YunHanAdminPurchasequalityimages',
    components: { container, MyConfirmButton, ElImageViewer },
    props: {
        //filter: {}
    },

    data() {
        return {
            that: this,
            filter: {
                buyNo: null,
                warehousNo:null
            },
            activeName: 'first',
            reType: 0,
            list: [],
            videoRemarks: [],
            listLoading: true,
            dialogVisible: false,
            videoplayerReload: false,
            showGoodsImage: false
        };
    },

    async mounted() {

    },

    methods: {
        async onSearch(param) {
            this.activeName = 'first';
            this.filter = { ...param };
            this.list = [];
            this.getlist();
        },
        async getlist() {
            const params = { buyNo: this.filter.buyNo }
            this.listLoading = true
            var res = await getPurchaseQualityGoodsRecordImgs(params);
            setTimeout(() => {
                this.listLoading = false
            }, 1000)

            if (!res?.success) {
                return
            }
            const data = res.data;
            this.list = data
        },
        handleClick(tab, event) {
            console.log(tab, event);
            
            this.activeName = tab.name;
            this.$nextTick(async () => {
                if (this.activeName == 'first') {
                    await this.getlist();
                };
                if (this.activeName == 'second') {
                    this.reType = 3;
                    await this.getWarehousingVideoRemark();
                };
                if (this.activeName == 'third') {
                    this.reType = 2;
                    await this.getWarehousingVideoRemark();
                };
            })
        },
        async onshowMv(row) {
            this.videoUrl = row;
            this.showGoodsImage = true;
            this.imgList = [];
            this.imgList.push(row);

        },
        async closeVideoPlyer() {
            let _this = this;
            _this.dialogVisible = false;
            _this.videoplayerReload = false;
            _this.showGoodsImage = false;
        },
        async getWarehousingVideoRemark() {
            var _this = this;
            const params = { ..._this.filter, reType: _this.reType }
            _this.listLoading = true;
            var res = await getWarehousingVideoRemarkAsync(params);
            setTimeout(() => {
                _this.listLoading = false;
            }, 1000)

            if (!res?.success) {
                return
            }
            const data = res.data;
            _this.videoRemarks = data
        }
    },
};
</script>

<style lang="scss" scoped>
.time {
    font-size: 13px;
    color: #999;
}

.bottom {
    margin-top: 13px;
    line-height: 12px;
}

.button {
    padding: 0;
    float: right;
}

.image {
    width: 100%;
    display: block;
}

.clearfix:before,
.clearfix:after {
    display: table;
    content: "";
}

.clearfix:after {
    clear: both
}

.sacimg {
    height: 250px;
    width: 250px;
    position: absolute;
    clip: rect(0, 220px, 200px, 0);
    transform: scale(2, 2);
}</style>