<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->

    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :tablekey='tablekey'
      :tableData='adflowlist' @select='selectchange' :isSelection='false'
      :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableCols='tableCols' :loading="listLoading">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <template slot='extentbtn'>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model="Filter.ShopName" placeholder="店铺" style="width:120px;" clearable/>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-date-picker style="width:280px" v-model="Filter.UseDate" type="datetimerange"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button>
        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getKuaiShouAdFlowList" />
    </template>
  </my-container>
</template>
<script>

import {getAdFlowReportPageList,exportKuaiShouAdFlowReport} from '@/api/financial/yyfyday'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar'
const tableCols =[
      {istrue:true,prop:'shopName',label:'店铺', width:'200',sortable:'custom'},
      {istrue:true,prop:'useDate',label:'日期', width:'200',sortable:'custom', formatter: (row) => formatTime(row.useDate, 'YYYY-MM-DD')},
      {istrue:true,prop:'rechargeTransferIn',label:'充值转入', width:'200',sortable:'custom'},
      {istrue:true,prop:'rechargeTransferOut',label:'充值转出', width:'200',sortable:'custom'},
      {istrue:true,prop:'actualRechargeAmount',label:'实际充值金额', width:'200',sortable:'custom'},
      {istrue:true,prop:'rechargeRebate',label:'充值返点', width:'200',sortable:'custom'},
      {istrue:true,prop:'rechargeCost',label:'推广花费', width:'200',sortable:'custom'},
      {istrue:true,prop:'actualRechargeRebate',label:'充值实际返点', width:'200',sortable:'custom'},
      {istrue:true,prop:'balance',label:'余额', width:'200',sortable:'custom'}
     ];

const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

export default {
  name: "Users",
  props: {
    tablekey: { type: String, default:'' }
  },
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, buschar},
  data() {
    return {
      that:this,
      Filter: {
        UseDate:[startDate, endDate],
      },
      shopList:[],
      userList:[],
      groupList:[],
      adflowlist: [],
      tableCols:tableCols,
      total: 0,
      summaryarry:{},
      pager:{OrderBy:"useDate",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
      platform:0,
      isExport:false,
    };
  },
  async mounted() {
  },
  methods: {
    async deleteBatch(row) {
      var that = this;
      this.$confirm("此操作将删除此批次导入快手推广流水数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          await deleteKuaiShouAdFlowBatch({ batchNumber: row.batchNumber })
          that.$message({ message: '已删除', type: "success" });
          that.onRefresh()

        });

    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getKuaiShouAdFlowList();
    },
    async getKuaiShouAdFlowList() {
      this.Filter.startUseDate = null;
        this.Filter.endUseDate = null;
      if (this.Filter.UseDate) {
        this.Filter.startUseDate = this.Filter.UseDate[0];
        this.Filter.endUseDate = this.Filter.UseDate[1];
      }
      const para = { ...this.Filter };
      let pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,

      };

      console.log(para)

      this.listLoading = true;
      const res = await getAdFlowReportPageList(params);
      console.log(res)
      this.listLoading = false;
      console.log(res.data.list)
      //console.log(res.data.summary)

      this.total = res.data.total
      this.adflowlist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    getParam(){
      this.Filter.startUseDate = null;
        this.Filter.endUseDate = null;
      if (this.Filter.UseDate) {
        this.Filter.startUseDate = this.Filter.UseDate[0];
        this.Filter.endUseDate = this.Filter.UseDate[1];
      }
      const para = { ...this.Filter };
      let pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      return params;
    },
    async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await exportKuaiShouAdFlowReport(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '快手运营费用推广报表_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        }
  }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
