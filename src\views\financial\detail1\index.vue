<template>
  <container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="年月:">
          <el-date-picker style="width: 240px" v-model="filter.yearmonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
  <el-tabs v-model="activeName" style="height: 94%;">
    <el-tab-pane label="淘系红包" name="first" style="height: 100%;">
       <cashRedTX :filter="filter" ref="cashRedTX"  @ondownloadmb='ondownloadmb' @onstartImport='onstartImport' @ondeleteByBatch='ondeleteByBatch' @onstartcomput="onstartcomput"/>
    </el-tab-pane>
    <el-tab-pane label="淘客不计" name="second" style="height: 100%;">
       <taoKeNot :filter="filter" ref="taoKeNot" @ondownloadmb='ondownloadmb' @onstartImport='onstartImport' @ondeleteByBatch='ondeleteByBatch' @onstartcomput="onstartcomput"/>
    </el-tab-pane>
     <el-tab-pane label="代发" name="resendagent" style="height: 100%;">
       <resendagent :filter="filter" ref="resendagent" @ondownloadmb='ondownloadmb' @onstartImport='onstartImport' @ondeleteByBatch='ondeleteByBatch' @onstartcomput="onstartcomput"/>
    </el-tab-pane>
    <el-tab-pane label="明细1汇总" name="third" style="height: 100%;">
       <monthsumfee :filter="filter" ref="monthsumfee"/>
    </el-tab-pane>
  </el-tabs>
 <el-dialog title="计算分摊" :visible.sync="dialogcomputVisible" width="500px" v-dialogDrag>
      <el-row>
         <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
           <el-select filterable v-model="computfilter.version" placeholder="类型">
              <el-option label="工资月报" value="v1"></el-option>
              <el-option label="参考月报" value="v2"></el-option>
           </el-select>
         </el-col>
        <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
          <el-date-picker style="width: 100%" v-model="computfilter.yearmonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
        </el-col>
         <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
           <el-button type="primary" @click="oncomput">计算分摊</el-button>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogcomputVisible = false">关闭</el-button>
      </span>
  </el-dialog>
  <el-dialog title="批量删除" :visible.sync="dialogdeletebatchNumberVisible" width="500px" v-dialogDrag>
      <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker v-model="deletefilter.yearmonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
            <el-button type="primary" @click="deleteByBatch">删除</el-button>
          </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogdeletebatchNumberVisible = false">关闭</el-button>
      </span>
  </el-dialog>
   <el-dialog title="导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
           <el-date-picker style="width: 100%" v-model="onimportfilter.yearmonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
         </el-col>
         </el-row>
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx"
                  :http-request="uploadFile" :file-list="fileList" :data="fileparm">
                <template #trigger>
                  <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
              </el-upload>
            </el-col>
          </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
   </el-dialog>
  </container>
</template>
<script>
import {computDetail1,importCashRedTX,importTaoKeNoMeter,importResendAgent} from '@/api/financial/detail1'
import {deleteProductCost as deleteWages} from '@/api/financial/productcost'
import cashRedTX from '@/views/financial/detail1/cashRedTX'
import taoKeNot from '@/views/financial/detail1/taoKeNot'
import resendagent from '@/views/financial/detail1/resendagent'
import monthsumfee from '@/views/financial/detail1/monthsumfee'
import container from '@/components/my-container/nofooter'
import { formatPlatform,formatLink,platformlist} from "@/utils/tools";
export default {
  name: 'Roles',
  components: {container,taoKeNot,monthsumfee,cashRedTX,resendagent},
  data() {
    return {
      activeName: 'first',
      filter: {
        startTime:null,
        endTime:null,
        companyId:null,
        warehouse:null,
        timerange:null,
        timerange1:null
      },
      platformlist:platformlist,
      deletefilter: {shareFeeType:0,yearmonth:'' },
      computfilter: {version:'v1',shareFeeType:0,yearmonth:'',amont:null},
      onimportfilter: {shareFeeType:0 ,yearmonth:'' },
      expresscompanylist: [],
      pageLoading: false,    
      dialogVisible: false,
      uploadLoading:false,
      importFilte:{},
      fileList:[],
      fileparm:{},
      dialogdeletebatchNumberVisible:false,
      dialogcomputVisible:false
    }
  },
  mounted() { },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    onSearch() {
       if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      if (this.activeName=='first') this.$refs.cashRedTX.onSearch();
      if (this.activeName=='second') this.$refs.taoKeNot.onSearch();
      if (this.activeName=='resendagent') this.$refs.resendagent.onSearch();
      else if (this.activeName=='third') this.$refs.monthsumfee.onSearch();
    },
    async oncomput(){
      if (!this.computfilter.yearmonth) {
        this.$message({type: 'warning',message: '请选择年月!'});
        return;
      }
      this.$confirm('确认计算分摊, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
        }).then(async () => {
            const res = await computDetail1(this.computfilter)
            if (!res?.success) {return }
            this.$message({type: 'success',message: '提交成功,正在后台计算分摊...'});
        }).catch(() => {
          this.$message({type: 'info',message: '已取消计算'});
        });
    },
    async onstartImport(shareFeeType){
      this.dialogVisible=true;
      this.onimportfilter.shareFeeType=shareFeeType;
    },
    async ondeleteByBatch(shareFeeType) {
      this.dialogdeletebatchNumberVisible=true;
      this.deletefilter.shareFeeType=shareFeeType;
      this.deletefilter.batchNumber='';
    },
   async onstartcomput(shareFeeType) {
      this.dialogcomputVisible=true;
      this.computfilter.shareFeeType=shareFeeType;
      this.computfilter.yearmonth='';
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      if (!this.onimportfilter.yearmonth) {
       this.$message({type: 'warning',message: '请选择月份!'});
       return;
      }
      this.uploadLoading=true
      this.$refs.upload.submit();
    },
   async uploadFile(item) {
     if(!item||!item.file||!item.file.size){
        this.$message({message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearmonth", this.onimportfilter.yearmonth);
      var res;
      if (this.onimportfilter.shareFeeType==41) res=await importCashRedTX(form);
      else if (this.onimportfilter.shareFeeType==42) res=await importTaoKeNoMeter(form);
      else if (this.onimportfilter.shareFeeType==43) res=await importResendAgent(form);
      if (res.code==1) this.$message({message: "上传成功,正在导入中...", type: "success" });
      else this.$message({message: res.msg, type: "warning" });
      this.uploadLoading=false
    },
  async deleteByBatch() {
      if (!this.deletefilter.yearmonth) {
       this.$message({type: 'warning',message: '请选择月份!'});
       return;
      } 
      this.$confirm('确认删除, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
        }).then(async () => {
            const res = await deleteWages(this.deletefilter)
            if (!res?.success) {return }
            this.$message({type: 'success',message: '删除成功!'});
            this.onSearch()
        }).catch(() => {
          this.$message({type: 'info',message: '已取消删除'});
        });
    },
    async ondownloadmb(name) {
       var alink = document.createElement("a");
       alink.href =`../../static/excel/financial2/detail1/${name}.xlsx`;     
       alink.click();
    },
   
  }
}
</script>
