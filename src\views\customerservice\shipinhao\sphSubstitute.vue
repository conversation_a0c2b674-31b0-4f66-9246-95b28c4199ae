<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-date-picker style="width: 280px" v-model="filter.sdate" type="daterange" format="yyyy-MM-dd"
        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="起始代班日期" end-placeholder="结束代班日期"
        @change="handleDateChange">
      </el-date-picker>
      <el-button style="padding: 0;margin: 0;border: none;">
        <el-input v-model.trim="filter.snick" placeholder="昵称" style="width:160px;" clearable :maxlength="50" />
      </el-button>
      <el-button style="padding: 0;margin: 0;border: none;">
        <el-input v-model.trim="filter.sname" placeholder="代班人" style="width:160px;" clearable :maxlength="50" />
      </el-button>
      <el-button style="padding: 0;margin: 0;border: none;">
        <el-select v-model="filter.inquirsType" placeholder="售前/后" clearable >
          <el-option label="售前" :value=0></el-option>
          <el-option label="售后" :value=1></el-option>
        </el-select>
      </el-button>
      <el-button type="primary" @click="onSearch">查询</el-button>
      <el-button type="primary" @click="onAdd">新增</el-button>
      <el-button type="primary" @click="onExport">导出</el-button>
    </template>

    <!-- 列表 -->
    <vxetableNotFixNum ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :loading="listLoading" >
      <template slot="right">
        <vxe-column title="操作" footer-align="center">
          <template #default="{ row, $index }">
            <el-button type="text" :disabled="!row.isPermitDel" @click="deleteSubstitute(row)">删除</el-button>
          </template>
        </vxe-column>
      </template>
    </vxetableNotFixNum>

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="新增代班客服" :visible.sync="addSubstituteDialogVisible" width="25%" v-dialogDrag append-to-body>
      <el-form ref="addForm" label-width="80px" :model="addForm" :rules="formRules">
        <el-form-item prop="sdate" label="代班日期">
          <el-date-picker style="width: 280px" v-model="addForm.sdate" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            @change="addSubstituteHandleDateChange" :clearable="false">
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="snick" label="昵称">
          <el-input style="width: 280px;" v-model="addForm.snick" :maxlength="20" show-word-limit clearable></el-input>
        </el-form-item>
        <el-form-item prop="shopCode" label="店铺">
          <el-select style="width: 280px;" v-model="addForm.shopCode" placeholder="店铺" @change="addShopChange"
            filterable clearable>
            <el-option v-for="item in filterShopList" :key="item.shopCode" :label="item.shopName"
              :value="item.shopCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="inquirsType" label="售前/售后">
          <el-radio-group v-model="addForm.inquirsType">
            <el-radio :label=0>售前</el-radio>
            <el-radio :label=1>售后</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="sname_DDid" label="代班人">
          <el-select style="width: 280px;" v-model="addForm.sname_DDid" placeholder="代班人" @change="addSnameChange"
            filterable clearable>
            <el-option v-for="item in groupSnameList" :key="item.sname_DDid" :label="item.sname"
              :value="item.sname_DDid"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div style="text-align: center;margin-top: 30px;">
        <el-button @click="addSubstituteDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="addSubstitute">确定</el-button>
      </div>
    </el-dialog>

  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import vxetableNotFixNum from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import datepicker from '@/views/customerservice/datepicker';
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser';
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { getGroupSnameList, getSPHSubstituteAsync, exportSPHSubstituteAsync, addSPHSubstitute, deleteSPHSubstitute } from '@/api/customerservice/shipinhaoinquirs.js';

const tableCols = [
  { istrue: true, width: '200', align: 'center', label: '代班时间', prop: 'showDate' },
  { sortable: 'custom', istrue: true, width: '170', align: 'center', label: '店铺', prop: 'shopName' },
  { sortable: 'custom', istrue: true, width: '170', align: 'center', label: '昵称', prop: 'snick' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '代班人', prop: 'sname' },
  {
    sortable: 'custom', istrue: true, width: '100', align: 'center', label: '售前/售后', prop: 'inquirsType', formatter: (row) => {
      return row.inquirsType === 0 ? '售前' : '售后'
    }
  },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '创建人', prop: 'createdUserName' },
  { sortable: 'custom', istrue: true, width: '150', align: 'center', label: '创建时间', prop: 'createdTime' },
  // {
  //   istrue: true, type: "button", label: '操作', width: "50", btnList: [
  //     { label: "删除", handle: (that, row) => that.deleteSubstitute(row) },
  //   ]
  // }
];

export default {
  name: "sphgrouplogtx",
  components: { MyContainer, vxetablebase, datepicker, vxetableNotFixNum },
  data() {
    return {
      that: this,
      pageLoading: false,
      listLoading: false,
      addSubstituteDialogVisible: false,
      filter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        //过滤条件
        sdate: [],
        startDate: '',
        endDate: '',
        snick: '',
        sname: '',
        inquirsType: null,
      },
      addForm: {
        inquirsType: 0,
        groupName: '',
        groupManager_DDid: '',
        groupManager: '',
        sname_DDid: '',
        sname: '',
        snick: '',
        shopCode: '',
        shopID: '',
        shopName: '',
        phoneNo: '',
        startDate: '',
        endDate: '',
        sdate: [],
      },
      formRules: {
        groupName: [{ required: true, message: '请输入分组', trigger: 'change' }],
        groupManager_DDid: [{ required: true, message: '请输入组长', trigger: 'change' }],
        groupManager: [{ required: true, message: '请输入组长', trigger: 'change' }],
        sname_DDid: [{ required: true, message: '请输入姓名', trigger: 'change' }],
        sname: [{ required: true, message: '请输入姓名', trigger: 'change' }],
        snick: [{ required: true, message: '请输入昵称', trigger: 'change' }],
        shopCode: [{ required: true, message: '请输入店铺', trigger: 'change' }],
        startDate: [{ required: true, message: '请输入入组日期', trigger: 'change' }],
        sdate: [{ required: true, message: '请输入代班日期', trigger: 'change' }],
        endDate: [{ required: true, message: '请输入离组日期', trigger: 'change' }],
      },
      filterShopList: [],// 店铺选择器列表
      groupSnameList: [],// 分组客服信息列表
      tableCols: tableCols,
      tableData: [],
      total: 0,
      sels: [],
      selids: [],
    }
  },
  async mounted() {
    this.getSPHShop();
    this.getGroupSnameList();
    this.onSearch();
  },
  methods: {
    //店铺选择器
    async getSPHShop() {
      let res = await getshopList({ platform: 20, CurrentPage: 1, PageSize: 100000 });
      this.filterShopList = res.data.list;
    },
    //分组客服信息列表
    async getGroupSnameList() {
      let res = await getGroupSnameList();
      this.groupSnameList = res.data;
    },
    // 检查是否选择时间
    handleDateChange() {
      this.filter.startDate = this.filter.sdate ? this.filter.sdate[0] : null;
      this.filter.endDate = this.filter.sdate ? this.filter.sdate[1] : null;
    },
    //每页数量改变
    Sizechange(val) {
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.getList();
    },
    //当前页改变
    Pagechange(val) {
      this.filter.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.filter.orderBy = prop
        this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async onSearch() {
      //点击查询按钮时才将页数重置为1
      this.filter.currentPage = 1;
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      this.listLoading = true;
      this.handleDateChange();
      if (this.filter.sdate) {
        this.filter.startDate = this.filter.sdate[0];
        this.filter.endDate = this.filter.sdate[1];
      }
      else {
        this.filter.startDate = null;
        this.filter.endDate = null;
      }
      const para = { ...this.filter };
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      try {
        const { data, success } = await getSPHSubstituteAsync(params);
        if (success) {
          this.tableData = data.list;
          this.total = data.total;
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.listLoading = false;
      }
    },
    // 导出
    async onExport() {
      this.listLoading = true;
      const res = await exportSPHSubstituteAsync(this.filter);
      this.listLoading = false;
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute("download", '代班客服信息_' + new Date().toLocaleString() + '.xlsx');
      aLink.click();
    },
    // 删除代班客服数据
    async deleteSubstitute(row) {
      var that = this;
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteSPHSubstitute({ batchNumber: row.batchNumber, inquirsType: row.inquirsType }).then(response => {
          if (response?.success) {
            // 删除成功的处理逻辑
            this.$message.success('删除成功!');
            that.onSearch();
          } else {
            this.$message.error("删除失败");
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    //打开新增代班弹窗
    onAdd() {
      this.addSubstituteDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate(); // 清除上次的校验结果
        this.$refs.addForm.resetFields();   // 重置表单数据
      });
      this.getGroupSnameList();//每次打开，刷新分组管理客服信息列表
    },
    // 检查是否选择时间
    addSubstituteHandleDateChange() {
      this.addForm.startDate = this.addForm.sdate ? this.addForm.sdate[0] : null;
      this.addForm.endDate = this.addForm.sdate ? this.addForm.sdate[1] : null;
    },
    //添加代班客服-店铺选择
    addShopChange() {
      if (null == this.addForm.shopCode) {
        this.addForm.shopName = null;
        this.addForm.shopID = null;
        return;
      }
      var shop = this.filterShopList.find(f => f.shopCode == this.addForm.shopCode);
      this.addForm.shopName = shop.shopName;
      this.addForm.shopID = shop.platformShopID;
    },
    //添加代班客服-代班人选择
    async addSnameChange() {
      var res = await QueryAllDDUserTop100({ keywords: this.addForm.sname_DDid });
      if (res?.success) {
        this.addForm.sname = res.data[0].userName;
        var groupSname = this.groupSnameList.filter(item => item.sname_DDid == this.addForm.sname_DDid);
        if (groupSname.length > 0) {
          this.addForm.groupName = groupSname[0].groupName;
          this.addForm.groupManager_DDid = groupSname[0].groupManager_DDid;
          this.addForm.groupManager = groupSname[0].groupManager;
          this.addForm.phoneNo = groupSname[0].phoneNo;
        }
      }
      console.log(this.addForm);
    },
    //添加代班客服数据
    async addSubstitute() {
      var that = this;
      this.$refs.addForm.validate(async valid => {
        if (valid) {
          var res = await addSPHSubstitute(that.addForm);
          if (res?.success) {
            this.$message({ message: '已添加', type: "success" });
            this.addSubstituteDialogVisible = false;
            this.getList();
          }
        } else {
          // 表单验证失败，提示用户
          this.$message.error('请检查填写的信息!');
        }
      });
    },

  }
}
</script>
