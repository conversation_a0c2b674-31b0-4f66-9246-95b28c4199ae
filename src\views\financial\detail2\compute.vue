<template>
  <container>
       <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='true'
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry'
              :loading="listLoading">
         <template slot='extentbtn'>
          <el-button-group>
            <el-button style="padding: 0;margin: 0;">
                <el-select filterable v-model="filter1.version" placeholder="类型" style="width: 100px">
                  <el-option label="工资月报" value="v1"></el-option>
                  <el-option label="参考月报" value="v2"></el-option>
               </el-select>
            </el-button> 
            <el-button style="padding: 0;margin: 0;"><el-input style="width: 100px" v-model="filter1.proCode"  placeholder="产品ID"/></el-button>
            <el-button style="padding: 0;margin: 0;"><el-input style="width: 100px" v-model="filter1.platform"  placeholder="平台"/></el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select filterable clearable v-model="filter1.groupId" placeholder="运营组" style="width: 110px">
                 <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select filterable clearable v-model="filter1.shopId" placeholder="店铺" style="width: 110px">
                <el-option label="全部" value/>
                <el-option v-for="item in shoplist" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"/>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select filterable clearable v-model="filter1.type" placeholder="业务" style="width: 110px">
                <el-option v-for="item in detail2list" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter1.shareOper" placeholder="分摊方式" style="width: 110px">
                  <el-option label="按源数据分摊" value='0'/>
                  <el-option label="按产品ID分摊" value='1'/>
                  <el-option label="按店铺分摊" value='2'/>
                  <el-option label="按组分摊" value='3'/>
                  <el-option label="按所有组分摊" value='4'/>
                  <el-option label="按商品编码分摊" value='5'/>
                  <el-option label="按平台分摊" value='7'/>
              </el-select>
            </el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-button-group>
        </template>
       </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

    <el-dialog title="计算分摊" :visible.sync="dialogcomputVisible" width="850px" v-dialogDrag>
      <el-row>
         <el-col :xs="24" :sm="4" :md="4" :lg="4" :xl="4">
           <el-select filterable v-model="computfilter.version" placeholder="类型">
              <el-option label="工资月报" value="v1"></el-option>
              <el-option label="参考月报" value="v2"></el-option>
           </el-select>
         </el-col>
        <el-col :xs="24" :sm="4" :md="4" :lg="4" :xl="4">
          <el-date-picker style="width: 100%" v-model="computfilter.yearmonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
        </el-col>
        <el-col :xs="24" :sm="4" :md="4" :lg="4" :xl="4">
          <el-input-number v-model="computfilter.amont" :step="1" placeholder="分摊金额" style="width: 100%"></el-input-number>
        </el-col>
        <el-col :xs="24" :sm="4" :md="4" :lg="4" :xl="4">
          <el-select filterable clearable v-model="computfilter.shareFeeType" placeholder="业务">
            <el-option v-for="item in detail2list" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-col>
      </el-row>
      <el-row>
        <el-col  :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
          <el-select filterable clearable multiple collapse-tags v-model="computfilter.platforms" placeholder="选择平台">
           <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
         </el-select>
        </el-col>
        <el-col  :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
          <el-checkbox v-model="computfilter.isrecover" border>是否覆盖业务计算</el-checkbox>          
        </el-col>
         <el-col :xs="24" :sm="2" :md="2" :lg="2" :xl="2">
           <el-button type="primary" @click="oncomput">计算分摊</el-button>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogcomputVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </container>
</template>
<script>
import {pageDetail2Compute,computDetail2} from '@/api/financial/detail2'
import {getList as getshopList } from '@/api/operatemanage/base/shop'
import {getDirectorGroupList} from '@/api/operatemanage/base/shop'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import {formatPlatform,formatTime,formatFeeShareOper,formatShareFeeType,platformlist,detail2list} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'yearMonth',label:'年月', width:'70',sortable:'custom'},
      {istrue:true,prop:'proCode',label:'宝贝ID', width:'120',sortable:'custom'},
      //{istrue:true,prop:'platform',label:'平台', width:'80'},
      //{istrue:true,prop:'fKId',label:'源ID', width:'80'},
      {istrue:true,prop:'groupId',label:'运营组', width:'70',formatter:(row)=>{return row.groupName}},
      {istrue:true,prop:'shopId',label:'店铺', width:'120',formatter:(row)=>{return row.shopName}},
      {istrue:true,prop:'type',label:'业务', width:'120',sortable:'custom',formatter:(row)=>formatShareFeeType(row.type)},
      {istrue:true,prop:'shareOper',label:'分摊方式', width:'100',sortable:'custom',formatter:(row)=>formatFeeShareOper(row.shareOper)},
      // {istrue:true,prop:'predictAmont',label:'预估分摊', width:'80'},
      {istrue:true,prop:'actualAmont',label:'实际分摊', width:'80'},
      {istrue:true,prop:'createdTime',label:'计算时间', width:'150',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD HH:mm:ss')},
     ];
const tableHandles=[{label:"计算分摊", handle:(that)=>that.onstartcomput()},];
export default {
  name: 'Roles',
  components: {cesTable, container },
   props:{
       filter: { }
     },
  data() {
    return {
      filter1: {
        proCode:null,
        platform:null,
        originId:null,
        groupId:null,
        shopId :null,
        type :null,
        shareOper :null
       },
      platformlist:platformlist,
      detail2list:detail2list,
      computfilter: {version:'v1',shareFeeType:0,yearmonth:'',amont:0,shareFeeType:undefined,platforms:[],isrecover:false},
      grouplist:[],
      shoplist:[],
      that:this,
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"id",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [], 
      listLoading: false,
      pageLoading: false,
      dialogcomputVisible:false
    }
  },
  async mounted() {
     await this.init()
    await this.onSearch()
  },
  beforeUpdate() { },
  methods: {
    async init(){
        this.shoplist =[]
        const res1 = await getshopList({isOpen:1,platform:1,CurrentPage:1,PageSize:100});
        res1?.data?.list.forEach(f=>{this.shoplist.push(f)})
       
        const res11 = await getshopList({isOpen:1,platform:2,CurrentPage:1,PageSize:100});
        res11?.data?.list.forEach(f=>{this.shoplist.push(f)})

        var res2= await getDirectorGroupList();
        this.grouplist = res2.data.map(item => {return { value: item.key, label: item.value };}); 
    },
   async onSearch() {
      this.$refs.pager.setPage(1)
     await this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager, ...this.pager,... this.filter, ... this.filter1}
      this.listLoading = true
      const res = await pageDetail2Compute(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
   async oncomput(){
      if (!this.computfilter.yearmonth) {
        this.$message({type: 'warning',message: '请选择年月!'});
        return;
      }
      this.$confirm('确认计算分摊, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
        }).then(async () => {
            const res = await computDetail2(this.computfilter)
            if (!res?.success) {return }
            this.$message({type: 'success',message: '提交成功,正在后台计算分摊...'});
        }).catch(() => {
          this.$message({type: 'info',message: '已取消计算'});
        });
    },
   async onstartcomput() {
      this.dialogcomputVisible=true;
      //this.computfilter.shareFeeType=shareFeeType;
      this.computfilter.yearmonth='';
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
    
  }
}
</script>
