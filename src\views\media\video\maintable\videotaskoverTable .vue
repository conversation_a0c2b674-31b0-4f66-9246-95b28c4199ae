<template>
    <!-- 短视频共用表 -->
    <div style="height: 100%;">
        <vxe-toolbar ref="xToolbar" custom class="vxetoolbar20221212" v-if="showToolbar">
            <template #buttons>
                <slot name="tbHeader" />
            </template>
        </vxe-toolbar>
        <vxe-table ref="xTable" border="default" :show-footer="showsummary" style="width: 100%;"
            class="vxetable202212161323 mytable-scrollbar20221212" resizable stripe :id="id" show-overflow
            show-footer-overflow keep-source size="mini" :height="height" :loading="loading" :data="tableData"
            :scroll-y="{ gt: 150, enabled: true  }" :scroll-x="{ gt: 150, enabled: true }" :footer-method="footerMethod"
            @footer-cell-click="footercellclick" @checkbox-change="selectChangeEvent" @checkbox-all="checkboxall"
            :row-config="{ isCurrent: true, isHover: true }" @cell-dblclick="rowChange" :row-style="rowStyleFun">
            <vxe-column field="" type="checkbox" width="40" fixed="left"></vxe-column>
            <vxe-column field="videoTaskId" title="编号" width='50' fixed='left'></vxe-column>
            <vxe-column field="productShortName" title="产品简称" width='128' fixed='left'>
                <template #default="{ row }">
                    <a type="text" show-overflow="ellipsis" style="color:#409EFF" @click="openComputOutInfo(row)"> {{
                        row.productShortName }} </a>
                </template>
            </vxe-column>
            <vxe-column field="urgencyediticon" title="" width='28' fixed='left'>
                <template #default="{ row }">
                    <template v-if="row.isTopOldNum == '0'">
                        <span></span>
                    </template>
                    <template v-else>
                        <i class="vxe-icon-dot" style="color: #ff0101;"></i>
                    </template>
                </template>
            </vxe-column>
            <vxe-column field="taskUrgencyStr" title="紧急程度" width='72' fixed='left'>
                <template #default="{ row }">
                    <template v-if="row.taskUrgency == 1">
                        <el-tag size="mini" effect="plain" type="danger" @click="shootUrgencyCilck(row)">{{
                            row.taskUrgencyStr }}</el-tag>
                    </template>
                    <template v-else-if="(row.taskUrgency == 2)">
                        <el-tag size="mini" effect="plain" type="" @click="shootUrgencyCilck(row)">{{
                            checkPermission('api:media:vediotask:ShootUrgencyTaskTgAsync') ? '审核' : '待审' }}</el-tag>
                    </template>
                    <template v-else-if="(row.taskUrgency == 0)">
                        <el-tag size="mini" effect="plain" type="info" @click="shootUrgencyCilck(row)">{{ row.taskUrgencyStr
                        }}</el-tag>
                    </template>
                    <template v-else>
                        <el-tag size="mini" effect="plain" type="info" @click="shootUrgencyCilck(row)">{{ row.taskUrgencyStr
                        }}</el-tag>
                    </template>
                </template>
            </vxe-column>
            <vxe-column field="warehouseStr" title="拍摄样品" width='92' fixed='left'></vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-bz')" field="baiz" title=" " width='28' fixed='left'>
                <template #default="{ row }">
                    <el-popover style="overflow-y: hidden;" v-if="row.remarkList?.length > 0" placement="right" trigger="hover"
                    width="500">
                    <el-card class="box-card">
                        <div style="display: flex; flex-direction: column; max-height: 250px; min-height: 60px; overflow-y: auto;">
                        <div v-for="(item, i) in row.remarkList" :key="i" style="border: 1px solid #fff;">
                            <span style="font-weight: 600;">{{ i + 1 }}</span>、{{ item }}
                        </div>
                        </div>

                    </el-card>
                     <i class="vxe-icon-flag-fill"  style="font-size: 14px;color: #F56C6C;" slot="reference"
                        @click="openTaskRmarkInfo(row)"></i>
                    </el-popover>

                    <i v-else class="vxe-icon-flag-fill" style="font-size: 15px;color: #dcdfe6;" slot="reference"
                    @click="openTaskRmarkInfo(row)"></i>

                </template>
            </vxe-column>
            <vxe-column field="cankao" title="" width='28' fixed='left'>
                <template #default="{ row }">
                    <i class="vxe-icon-file-txt" @click="videotaskuploadfileDetal(row)"></i>
                </template>
            </vxe-column>
            <vxe-column field="caozuo" title="" width='28' fixed='left'>
                <template #default="{ row }">
                    <i class="vxe-icon-ellipsis-h" @click="editTask(row)"></i>
                </template>
            </vxe-column>
            <!-- ---------------------------------------------------------------------------------------------------------------- -->
            <vxe-column field="fengex1" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="claimant1" title="拍摄一" width='65'> </vxe-column>
            <vxe-column field="claimTime1Str" title="拍摄日期" width='70'>  </vxe-column>
            <vxe-column field="claimAudioStatus1" title="审核" width='55'>
                <template #default="{ row }">
                    <template v-if="row.claimAudioTime1Str == '-'">
                            -
                    </template>
                    <template v-else-if="row.claimAudioStatus1 == 2">
                        <el-tag size="mini" effect="dark" type="success" @click="onUploadVideoAudio(row, 1)">通过</el-tag>
                    </template>
                    <template v-else-if="(row.claimAudioStatus1 == 3)">
                        <el-tag size="mini" effect="dark" type="warning" @click="onUploadVideoAudio(row, 1)">补拍</el-tag>
                    </template>
                    <template v-else-if="(row.claimAudioStatus1 == 1)">
                        <el-tag size="mini" effect="dark" type="info" @click="onUploadVideoAudio(row, 1)">待审</el-tag>
                    </template>
                    <template v-else-if="(row.claimAudioStatus1 == 4)">
                        <el-tag size="mini" effect="dark" type="danger" @click="onUploadVideoAudio(row, 1)">重拍</el-tag>
                    </template>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-shrq')" field="claimAudioTime1Str" title="审核日期" width='70'>
            </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-shts')" field="claimAudio1" title="天数" width='50'>
            </vxe-column>
            <vxe-column field="cutClaimant1" title="剪辑一" width='65'> </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-jjts')" field="cutClaimDay1" title="天数" width='50'>
            </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-jjrq')" field="cutClaimTime1Str" title="剪辑日期" width='70'>
            </vxe-column>
            <vxe-column field="fengex1" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="claimant2" title="拍摄二" width='65'> </vxe-column>
            <vxe-column field="claimTime2Str" title="拍摄日期" width='70'>  </vxe-column>
            <vxe-column field="claimAudioStatus2" title="审核" width='55'>
                <template #default="{ row }">
                    <template v-if="row.claimAudioTime2Str == '-'">
                            -
                    </template>
                    <template v-else-if="row.claimAudioStatus2 == 2">
                        <el-tag size="mini" effect="dark" type="success" @click="onUploadVideoAudio(row, 2)">通过</el-tag>
                    </template>
                    <template v-else-if="(row.claimAudioStatus2 == 3)">
                        <el-tag size="mini" effect="dark" type="warning" @click="onUploadVideoAudio(row, 2)">补拍</el-tag>
                    </template>
                    <template v-else-if="(row.claimAudioStatus2 == 1)">
                        <el-tag size="mini" effect="dark" type="info" @click="onUploadVideoAudio(row, 2)">待审</el-tag>
                    </template>
                    <template v-else-if="(row.claimAudioStatus2 == 4)">
                        <el-tag size="mini" effect="dark" type="danger" @click="onUploadVideoAudio(row, 2)">重拍</el-tag>
                    </template>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-shrq')" field="claimAudioTime2Str" title="审核日期" width='70'>
             </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-shts')" field="claimAudio2" title="天数" width='50'>
            </vxe-column>
            <vxe-column field="cutClaimant2" title="剪辑二" width='65'> </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-jjts')" field="cutClaimDay2" title="天数" width='50'>
            </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-jjrq')" field="cutClaimTime2Str" title="剪辑日期" width='70'>
                <template #default="{ row }">{{ formatIsCommission(row.cutClaimTime2) }} </template>
            </vxe-column>
            <vxe-column field="fengex1" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="claimant3" title="拍摄三" width='65'> </vxe-column>
            <vxe-column field="claimTime3Str" title="拍摄日期" width='70'>  </vxe-column>
            <vxe-column field="claimAudioStatus3" title="审核" width='55'>
                <template #default="{ row }">
                    <template v-if="row.claimAudioTime3Str == '-'">
                            -
                    </template>
                    <template v-else-if="row.claimAudioStatus3 == 2">
                        <el-tag size="mini" effect="dark" type="success" @click="onUploadVideoAudio(row, 3)">通过</el-tag>
                    </template>
                    <template v-else-if="(row.claimAudioStatus3 == 3)">
                        <el-tag size="mini" effect="dark" type="warning" @click="onUploadVideoAudio(row, 3)">补拍</el-tag>
                    </template>
                    <template v-else-if="(row.claimAudioStatus3 == 1)">
                        <el-tag size="mini" effect="dark" type="info" @click="onUploadVideoAudio(row, 3)">待审</el-tag>
                    </template>
                    <template v-else-if="(row.claimAudioStatus3 == 4)">
                        <el-tag size="mini" effect="dark" type="danger" @click="onUploadVideoAudio(row, 3)">重拍</el-tag>
                    </template>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-shrq')" field="claimAudioTime3Str" title="审核日期" width='70'>
            </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-shts')" field="claimAudio3" title="天数" width='50'>
            </vxe-column>
            <vxe-column field="cutClaimant3" title="剪辑三" width='65'> </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-jjts')" field="cutClaimDay3" title="天数" width='50'>
            </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-jjrq')" field="cutClaimTime3Str" title="剪辑日期" width='70'>
            </vxe-column>
            <vxe-column field="fengex1" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="claimant4" title="拍摄四" width='65'> </vxe-column>
            <vxe-column field="claimTime4Str" title="拍摄日期" width='70'>  </vxe-column>
            <vxe-column field="claimAudioStatus4" title="审核" width='55'>
                <template #default="{ row }">
                    <template v-if="row.claimAudioTime4Str == '-'">
                            -
                    </template>
                    <template v-else-if="row.claimAudioStatus4 == 2">
                        <el-tag size="mini" effect="dark" type="success" @click="onUploadVideoAudio(row, 4)">通过</el-tag>
                    </template>
                    <template v-else-if="(row.claimAudioStatus4 == 3)">
                        <el-tag size="mini" effect="dark" type="warning" @click="onUploadVideoAudio(row, 4)">补拍</el-tag>
                    </template>
                    <template v-else-if="(row.claimAudioStatus4 == 1)">
                        <el-tag size="mini" effect="dark" type="info" @click="onUploadVideoAudio(row, 4)">待审</el-tag>
                    </template>
                    <template v-else-if="(row.claimAudioStatus4 == 4)">
                        <el-tag size="mini" effect="dark" type="danger" @click="onUploadVideoAudio(row, 4)">重拍</el-tag>
                    </template>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-shrq')" field="claimAudioTime4Str" title="审核日期" width='70'>
            </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-shts')" field="claimAudio4" title="天数" width='50'>
            </vxe-column>
            <vxe-column field="cutClaimant4" title="剪辑四" width='65'> </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-jjts')" field="cutClaimDay4" title="天数" width='50'>
            </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-jjrq')" field="cutClaimTime4Str" title="剪辑日期" width='70'>
            </vxe-column>
            <vxe-column field="fengex1" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="claimant5" title="拍摄五" width='65'> </vxe-column>
            <vxe-column field="claimTime5Str" title="拍摄日期" width='70'>  </vxe-column>
            <vxe-column field="claimAudioStatus5" title="审核" width='55'>
                <template #default="{ row }">
                    <template v-if="row.claimAudioTime5Str == '-'">
                            -
                    </template>
                    <template v-else-if="row.claimAudioStatus5 == 2">
                        <el-tag size="mini" effect="dark" type="success" @click="onUploadVideoAudio(row, 5)">通过</el-tag>
                    </template>
                    <template v-else-if="(row.claimAudioStatus5 == 3)">
                        <el-tag size="mini" effect="dark" type="warning" @click="onUploadVideoAudio(row, 5)">补拍</el-tag>
                    </template>
                    <template v-else-if="(row.claimAudioStatus5 == 1)">
                        <el-tag size="mini" effect="dark" type="info" @click="onUploadVideoAudio(row, 5)">待审</el-tag>
                    </template>
                    <template v-else-if="(row.claimAudioStatus5 == 4)">
                        <el-tag size="mini" effect="dark" type="danger" @click="onUploadVideoAudio(row, 5)">重拍</el-tag>
                    </template>
                </template>
            </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-sqrq')" field="claimAudioTime5Str" title="审核日期" width='70'>
            </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-shts')" field="claimAudio5" title="天数" width='50'>
            </vxe-column>
            <vxe-column field="cutClaimant5" title="剪辑五" width='65'> </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-jjts')" field="cutClaimDay5" title="天数" width='50'>
            </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-jjrq')" field="cutClaimTime5Str" title="剪辑日期" width='70'>
            </vxe-column>
            <!-- ---------------------------------------------------------------------------------------------------------------- -->
            <vxe-column field="fengex2" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="cuteLqName" title="负责人" width='70'> </vxe-column>
            <vxe-column field="dockingPeopleStr" title="分配拍摄一" width='70'> </vxe-column>
            <vxe-column field="dockingPeopleStr2" title="分配拍摄二" width='70'> </vxe-column>
            <vxe-column field="dockingPeopleStr3" title="分配拍摄三" width='70'> </vxe-column>
            <vxe-column field="dockingPeopleStr4" title="分配拍摄四" width='70'> </vxe-column>
            <vxe-column field="dockingPeopleStr5" title="分配拍摄五" width='70'> </vxe-column>
            <vxe-column field="fengex2" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="operationsGroupStr" title="运营小组" width='70'> </vxe-column>
            <vxe-column field="productId" title="产品ID" width='80'>
                <template #default="{ row }">
                    <a :href="formatProCode(row)" target="_black" style="color:blue">{{ row.productId }}</a>
                </template>
            </vxe-column>
            <vxe-column field="platformStr" title="平台" width='65'> </vxe-column>
            <vxe-column field="shopName" title="店铺" width='145'> </vxe-column>
            <vxe-column field="fengex3" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="claimOverDate" title="拍摄完成" width='70'>   <template #default="{ row }">{{ formatIsCommission(row.claimOverDate) }} </template></vxe-column>
            <vxe-column field="cutClaimOverDate" title="剪辑完成" width='70'>   <template #default="{ row }">{{ formatIsCommission(row.cutClaimOverDate) }} </template></vxe-column>
            <vxe-column field="updateDate" title="修改日期" width='70'>
                <template #default="{ row }">{{ formatIsCommission(row.updateDate) }} </template></vxe-column>
            <vxe-column field="createdTime" title="创建日期" width='70'>
                <template #default="{ row }">{{ formatIsCommission(row.createdTime) }} </template>
            </vxe-column>
            <vxe-column field="fengex4" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="sampleRrderNo" title="内部单号" width='100'>
                <template #default="{ row }">
                    <a type="text" show-overflow="ellipsis" style="color:#409EFF" @click="onShowOrderDtl(row)"> {{
                        row.orderNoInner }} </a>
                </template>
            </vxe-column>
            <vxe-column field="sampleExpressNo" title="快递单号" width='135'>
                <template #default="{ row }">
                    <a type="text" show-overflow="ellipsis" style="color:#409EFF" @click="onShowExproessHttp(row)"> {{
                        row.expressNo }} </a>
                </template>
            </vxe-column>
            <vxe-column field="orderTrack" title="拿样跟踪" width='80'>
                <template #default="{ row }">
                    <a type="text" show-overflow="ellipsis" style="color:#409EFF" @click="onShowOrderDtl(row)"> {{
                        row.shootOrderTrack }} </a>
                </template>
            </vxe-column>
            <vxe-column field="fengex5" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <!-- 占位列，勿动 -->
            <vxe-column field="" title="" width='5 '> </vxe-column>
            <div v-if="showCacle">
                <!-- <vxe-column    field="shootShortUserRate" title="拍摄占比" width='65'> </vxe-column>
                <vxe-column    field="cuteShortUserRate" title="剪辑占比" width='65'> </vxe-column>  -->
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="deptShortVideoCommission" title="部门总条" width='65'> </vxe-column>
                <!-- <vxe-column v-if="checkPermission('shootlist-mx')"  field="deptShootShortVideoCommission1" title="部门拍摄一总款" width='65'> </vxe-column> -->
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideoCommissionYw1" title="义乌拍摄一总条" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideoCommissionNc1" title="南昌拍摄一总条" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideoCommissionWh1" title="武汉拍摄一总条" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideo1" title="拍摄一单条" width='65'> </vxe-column>
<!--
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="deptShootShortVideoCommission2" title="部门拍摄二总款" width='65'> </vxe-column> -->
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideoCommissionYw2" title="义乌拍摄二总条" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideoCommissionNc2" title="南昌拍摄二总条" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideoCommissionWh2" title="武汉拍摄二总条" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideo2" title="拍摄二单条" width='65'> </vxe-column>
<!--
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="deptShootShortVideoCommission3" title="部门拍摄三总款" width='65'> </vxe-column> -->
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideoCommissionYw3" title="义乌拍摄三总条" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideoCommissionNc3" title="南昌拍摄三总条" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideoCommissionWh3" title="武汉拍摄三总条" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideo3" title="拍摄三单条" width='65'> </vxe-column>
<!--
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="deptShootShortVideoCommission4" title="部门拍摄四总款" width='65'> </vxe-column> -->
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideoCommissionYw4" title="义乌拍摄四总条" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideoCommissionNc4" title="南昌拍摄四总条" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideoCommissionWh4" title="武汉拍摄四总条" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideo4" title="拍摄四单条" width='65'> </vxe-column>
<!--
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="deptShootShortVideoCommission5" title="部门拍摄五总款" width='65'> </vxe-column> -->
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideoCommissionYw5" title="义乌拍摄五总条" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideoCommissionNc5" title="南昌拍摄五总条" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideoCommissionWh5" title="武汉拍摄五总条" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="shootShortVideo5" title="拍摄五单条" width='65'> </vxe-column>
<!--
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="deptCuteShortVideoCommission1" title="部门剪辑一总款" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="cuteShortVideoCommissionYw1" title="义乌剪辑一总款" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="cuteShortVideoCommissionNc1" title="南昌剪辑一总款" width='65'> </vxe-column> -->
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="cuteShortVideo1" title="剪辑一单条" width='65'> </vxe-column>
<!--
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="deptCuteShortVideoCommission2" title="部门剪辑二总款" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="cuteShortVideoCommissionYw2" title="义乌剪辑二总款" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="cuteShortVideoCommissionNc2" title="南昌剪辑二总款" width='65'> </vxe-column> -->
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="cuteShortVideo2" title="剪辑二单条" width='65'> </vxe-column>
<!--
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="deptCuteShortVideoCommission3" title="部门剪辑三总款" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="cuteShortVideoCommissionYw3" title="义乌剪辑三总款" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="cuteShortVideoCommissionNc3" title="南昌剪辑三总款" width='65'> </vxe-column> -->
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="cuteShortVideo3" title="剪辑三单条" width='65'> </vxe-column>
<!--
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="deptCuteShortVideoCommission4" title="部门剪辑四总款" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="cuteShortVideoCommissionYw4" title="义乌剪辑四总款" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="cuteShortVideoCommissionNc4" title="南昌剪辑四总款" width='65'> </vxe-column> -->
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="cuteShortVideo4" title="剪辑四单条" width='65'> </vxe-column>
<!--
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="deptCuteShortVideoCommission5" title="部门剪辑五总款" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="cuteShortVideoCommissionYw5" title="义乌剪辑五总款" width='65'> </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="cuteShortVideoCommissionNc5" title="南昌剪辑五总款" width='65'> </vxe-column> -->
                <vxe-column v-if="checkPermission('shootlist-mx')"  field="cuteShortVideo5" title="剪辑五单条" width='65'> </vxe-column>
                <!-- <vxe-column field="shootBase" title="拍摄底薪" width='65'> </vxe-column>
                <vxe-column field="cuteBase" title="剪辑底薪" width='65'> </vxe-column> -->
                <vxe-column field="commissionTotal" title="提成合计" width='65'> </vxe-column>
                <!-- <vxe-column field="sarlayTotal" title="合计" width='65'> </vxe-column> -->
           </div>

        </vxe-table>
    </div>
</template>
<script>
// import VXETable from 'vxe-table'
// import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
// import ExcelJS from 'exceljs'
// VXETable.use(VXETablePluginExportXLSX, {
//       ExcelJS
//     })
    
import { formatTime } from "@/utils";
export default {
    props: {
        editconfig: { type: Object, default: () => { return {} } },
        treeProp: { type: Object, default: () => { return {} } },
        hasSeq: { type: Boolean, default: () => { return true } },
        hascheck: { type: Boolean, default: () => { return false } },
        showToolbar: { type: Boolean, default: () => { return true } },
        // 表格数据
        tableData: { type: Array, default: () => [] },
        // 表格型号：mini,medium,small
        size: { type: String, default: 'mini' },
        type: { type: String, default: 'primary' },
        isBorder: { type: Boolean, default: true },
        // 表格列配置
        tableCols: { type: Array, default: () => [] },
        isRemoteSort: { type: Boolean, default: () => { return true } },
        id: { type: String, default: () => { return new Date().valueOf().toString() } },
        that: { type: Object, default: () => { return null } },
        loading: { type: Boolean, default: () => { return false; } },
        border: { type: Boolean | Object, default: () => { return 'default' } },
        tableHandles: { type: Array, default: () => [] },
        showsummary: { type: Boolean, default: false },
        align: { type: String, default: '' }, //对齐方式
        summaryarry: { type: Object, default: () => { } },
        tablekey: { type: String, default: '' },//表格key
        height: { type: String, default: '95%' },//固定表头作用

        showCacle: { type: Boolean, default: () => { return false } },

    },
    created() {
        // VXETable.use(VXETablePluginExportXLSX);
        this.$nextTick(() => {
            // 手动将表格和工具栏进行关联
            this.$refs.xTable.connect(this.$refs.xToolbar)
        })
    },
    data() {
        return {
            lastSortArgs: {
                field: "",
                order: "",
            },
            arrlist: [],
            summarycolumns: [],
            tablecolumns: [],
            aaaa: '',
            markopentime: new Date().getTime() + 3,
             //选中的行
             selectRowKey: null
        }
    },
    async mounted() {
        this.$nextTick(() => {
            this.tablecolumns = this.$refs.xTable.getColumns()

        })
    },
    methods: {
        formatProCode(row) {
            var proBaseUrl = '';
            switch (row.platform) {
                case 1://淘系
                case '淘系'://淘系
                    proBaseUrl = "https://detail.tmall.com/item.htm?id=" + row.productId;
                    break;
                case 2://拼多多
                case '拼多多'://拼多多
                    proBaseUrl = "https://mobile.yangkeduo.com/goods2.html?goods_id=" + row.productId;
                    break;
                case 8://淘系
                case '淘工厂'://淘系
                    proBaseUrl = "https://detail.tmall.com/item.htm?id=" + row.productId;
                    break;
                case 9://淘系
                case '淘宝'://淘系
                    proBaseUrl = "https://detail.tmall.com/item.htm?id=" + row.productId;
                    break;
                case 7://京东
                case '京东'://京东
                    proBaseUrl = "https://item.jd.com/" + + row.productId + ".html";
                    break;

            }
            return proBaseUrl == '' ? "#" : proBaseUrl;
        },
        rowStyleFun({ row, rowIndex, $rowIndex }) {

            if (row && row.isend == 0) {
                return '';
            } else {
                return 'droprow';
            }
        },
        onUploadVideoAudio(row, ckindex) {
            this.$emit('onUploadVideoAudio', row, ckindex)
        },
        async checkboxall() {
            const records = this.$refs.xTable.getCheckboxRecords()
            this.$emit('checkboxall', records);
        },
        onShowOrderDtl(row) {
            this.$emit('onShowOrderDtl', row)
        },
        onShowExproessHttp(row) {
            this.$emit('onShowExproessHttp', row)
        },
        //行切换事件
        rowChange({ newValue, oldValue, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }) {
            this.$emit('rowChange', row);
        },
        openComputOutInfo(row) {
            this.$emit('openComputOutInfo', row)
        },
        shootUrgencyCilck(row) {
            this.$emit('shootUrgencyCilck', row)
        },
        openTaskRmarkInfo(row) {

            this.$emit('openTaskRmarkInfo', row)
        },
        videotaskuploadfileDetal(row) {

            this.$emit('videotaskuploadfileDetal', row)
        },
        editTask(row) {
            this.$emit('editTask', row)
        },
        selectChangeEvent({ checked }) {
            const records = this.$refs.xTable.getCheckboxRecords()
            this.$emit('selectchangeevent', records);
        },
        //导出
        exportData(filename) {
            // this.$refs.xTable.exportData({ filename: filename, type: 'csv' })
            this.$refs.xTable.exportData({filename:filename,    sheetName: 'Sheet1',type: 'xlsx' })
        },
        formatIsCommission(value) {
            return value == null ? null : formatTime(value, 'YY-MM-DD')
        },
        //批量控制列的显影
        async ShowHidenColums(arrlist) {
            this.$refs.xTable.getTableColumn().collectColumn.forEach(column => {
                if (arrlist.includes(column.property)) {
                    column.visible = false
                } else {
                    column.visible = true
                }
            })
            if (this.$refs.xTable) {
                this.$refs.xTable.refreshColumn()
            }
        },
        //清空全选
        clearSelection() {
            this.$refs.xTable.clearCheckboxRow()
        },
        footerMethod({ columns, data }) {
            const sums = [];
            if (!this.summaryarry)
                return sums
            var arr = Object.keys(this.summaryarry);
            if (arr.length == 0)
                return sums
            //const { columns, data } = param;
            var hashj = false;
            columns.forEach((column, index) => {
                if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
                    var sum = this.summaryarry[column.property + '_sum'];
                    if (sum == null) return;
                    else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
                    else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum.toFixed(2)
                    else sums[index] = sum.toFixed(2)
                }
                else sums[index] = ''
            });
            return [sums]
        },
        footercellclick({ items, $rowIndex, column, columnIndex, $columnIndex, $event }) {

            let self = this;
            var col = findcol(self.tableCols, column.property);
            if (col && col.summaryEvent)
                self.$emit('summaryClick', column.property)

            function findcol(cols, property) {
                let column;
                for (var i = 0; i < cols.length; i++) {
                    var c = cols[i];
                    if (column) break
                    else if (c.prop && c.prop.toLowerCase() == property.toLowerCase()) {
                        column = c;
                        break
                    }
                    else if (c.cols && c.cols.length > 0) column = findcol(c.cols, property)
                }
                return column
            }
        }
    }


}
</script>


<style lang="scss" scoped> .vxe-table--render-default.border--default .vxe-table--header-wrapper {
     background-color: #fafbff;
 }

 /*斑马线颜色*/
 .vxe-table--render-default .vxe-body--row.row--stripe {
     background-color: #fafbff;
 }

 .vxe-table--render-default .vxe-body--row.row--current {
     background-color: #e5ecf5;
 }

 /*滚动条整体部分*/
 .mytable-scrollbar20221212 ::-webkit-scrollbar {
     width: 18px;
     height: 26px;
 }

 /*滚动条的轨道*/
 .mytable-scrollbar20221212 ::-webkit-scrollbar-track {
     background-color: #f1f1f1;
 }

 /*滚动条里面的小方块，能向上向下移动*/
 .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
     background-color: #c1c1c1;
     border-radius: 3px;
     box-sizing: border-box;
     border: 2px solid #F1F1F1;
     box-shadow: inset 0 0 6px rgba(255, 255, 255, .5);
 }

 // 滚动条鼠标悬停颜色
 .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
     background-color: #A8A8A8;
 }

 // 滚动条拖动颜色
 .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
     background-color: #787878;
 }

 /*边角，即两个滚动条的交汇处*/
 .mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
     background-color: #dcdcdc;
 }

 // 图片大小
 .mytable-scrollbar20221212 .images20221212 {
     max-width: 150px;
     max-height: 150px;
     width: 40px !important;
     height: 40px !important;
 }

 // 图片张数标记
 .mytable-scrollbar20221212 .badgeimage20221212 .el-badge__content.is-fixed {
     top: 10px;
 }

 /*  工具箱位置  */
 .vxetoolbar20221212 {
     position: absolute;
     top: 53px;
     right: 2px;
     padding-top: 0;
     padding-bottom: 0;
     z-index: 999;
     background-color: rgb(255 255 255 / 0%);
 }

 // 表头高度
 ::v-deep .vxe-table--render-default.size--mini .vxe-header--column:not(.col--ellipsis) {
     height: 50px !important;
 }

 // 表头文字行间距
 ::v-deep .vxe-header--column {
     line-height: 18px !important;
 }

 // 表格内边距
 ::v-deep .vxe-table--render-default .vxe-cell {
     padding: 0 0 0 8px !important;
 }

 ::v-deep span .el-radio-button__inner {
     line-height: 14px !important;
 }

 .vxetableheadercell-left-20221216 {
     text-align: left;
 }

 .vxetableheadercell-center-20221216 {
     text-align: center;
 }

 .vxetableheadercell-right-20221216 {
     text-align: right;
 }

 .vxe-icon-ellipsis-h:hover {
     color: #409EFF;
     margin-left: 2px;
     background-color: #F1F1F1;
 }

 .vxe-icon-ellipsis-h {
     color: #999;
     font-size: 15px;
 }

 .vxe-icon-file-txt:hover {
     color: #409EFF;
     margin-left: 2px;
     background-color: #F1F1F1;
     font-weight: 600;
 }

 .vxe-icon-file-txt {
     color: #999;
     font-size: 15px;
 }

 .vxetablecss {
     margin: 0;
 }

 ::v-deep span.vxe-cell--item {
     cursor: pointer !important;
 }

 ::v-deep .el-tag {

     cursor: pointer !important;
 }


 ::v-deep .droprow td {
     color: rgb(250, 9, 9);
     position: relative;
 }

 ::v-deep .droprow ::after {
     content: "";
     position: absolute;
     top: 50%;
     left: 0;
     width: 100%;
     height: 0.1px;
     background-color: rgb(250, 9, 9);
     transform: translateY(-50%);
 }
</style>
