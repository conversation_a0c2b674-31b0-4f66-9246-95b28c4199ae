<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.wareName" placeholder="仓库" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList">搜索</el-button>
      </div>
    </template>
    <vxetablebase :id="'productLiteratureSettings202408041540'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :toolbarshow='false'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template #isExternal="{ row }">
        <el-switch style="display: block" v-model="row.isExternal" active-color="#67C23A" inactive-color="#409EFF"
          active-text="是" inactive-text="否" :disabled="!checkPermission('Api:Inventory:BasicGoods:SetWarehouseIsExt')">
        </el-switch>
      </template>
    </vxetablebase>
    <template #footer>
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getWareList, setWarehouseIsExt } from "@/api/inventory/basicgoods"
import dayjs from 'dayjs'
const tableCols = [
  { width: 'auto', align: 'center', prop: 'warehouseName', label: '仓库名称', },
  { prop: 'isExternal', label: '是否内外仓', }
]
export default {
  name: "productLiteratureSettings",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      that: this,
      ListInfo: {
        wareName: null,//仓库名称
      },
      tableCols,//表格列
      tableData: [],//表格数据
      loading: false,//加载状态
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    //保存
    async onStorage() {
      let isExtIitems = []
      isExtIitems = this.tableData.map(item => {
        return {
          wms_co_id: item.wms_co_id,
          isExternal: item.isExternal
        };
      });
      this.$confirm('此操作将执行保存操作, 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.loading = true
        const { success } = await setWarehouseIsExt({ isExtIitems: isExtIitems })
        this.loading = false
        if (success) {
          this.$emit('storageMethod')
          this.$message.success('保存成功')
        } else {
          this.$message.error('保存失败')
        }
      }).catch(() => {
      });
    },
    //获取列表
    async getList() {
      this.loading = true
      const { data, success } = await getWareList(this.ListInfo)
      if (success) {
        this.tableData = data
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 5px;
  }
}
</style>
