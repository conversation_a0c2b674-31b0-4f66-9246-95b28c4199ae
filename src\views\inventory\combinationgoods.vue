<template>
  <el-container style="height:100%;">
      <my-container v-loading="pageLoading" style="width:50%;">
        <template #header>
          <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>      
          <el-form-item label="组合商品编码:">
                <el-input v-model="filter.combinationCode" placeholder="组合商品编码"/>         
            </el-form-item>
            <el-form-item label="组合编码名称:">
                <el-input v-model="filter.combinationName" placeholder="组合编码名称"/>         
            </el-form-item>
            <el-form-item label="商品编码:">
                <el-input v-model="filter.goodsCode" placeholder="商品编码"/>         
            </el-form-item>
          </el-form>
        </template>
    
        <ces-table ref="table" :that='that' :isIndex='false' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
          :tableData='list'    :tableCols='tableCols' :isSelection="false" @select="selectchange"
          :tableHandles='tableHandles' @cellclick="cellclick"
          :loading="listLoading">
        </ces-table>
        
        <template #footer>
          <my-pagination
            ref="pager"
            :total="total"
            :checked-count="sels.length"
            @get-page="getlist"
          />
        </template>

        <el-drawer
          :title="formtitle"
          :modal="false"
          :wrapper-closable="true"
          :modal-append-to-body="false"
          :visible.sync="addFormVisible"
          direction="btt"
          size="'auto'"
          class="el-drawer__wrapper"
          style="position:absolute;"
        >
        <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" style="padding-top:10px;"/>
          <div class="drawer-footer">
            <el-button @click.native="addFormVisible = false">取消</el-button>
            <my-confirm-button type="submit"  :loading="addLoading" @click="onAddSubmit" />
          </div>
        </el-drawer>

        <el-dialog title="导入组合编码数据" :visible.sync="dialogVisible" width="30%">
          <span>
            <el-upload
              ref="upload"
              class="upload-demo"
              :auto-upload="false"
              :multiple="false"
              :limit="1"
              action
              accept=".xlsx"
              :http-request="uploadFile"
              :file-list="fileList"
            >
            <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
            </template> 
              <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传</el-button>
            </el-upload>
          </span>
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">关闭</el-button>
          </span>
        </el-dialog>
      </my-container>
      <my-container style="width:50%;">
          <template #header>
              <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>      
                  <el-form-item label="商品名称:">
                      <el-input v-model="filter.goodsName" placeholder="商品名称"/>         
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                  </el-form-item>
                </el-form>
          </template>
          <div style="margin-top:30px;height:100%;">
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchangeDetail'
            :tableData='detail.list'    :tableCols='detail.tableCols' :isSelection="false" :isSelectColumn="false"
            :loading="detail.listLoading">
            </ces-table>
          </div>
        <template #footer>
          <my-pagination
            ref="pagerDetail"
            :total="detail.total"
            :checked-count="detail.sels.length"
            @get-page="getDetailList"
          />
        </template>
      </my-container>
  </el-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { 
  importData,
  getList,
  exportData,
  getById,
  addOrUpdate,
  deleteData,
  deleteDataBatch,
  getDetailList,
  addOrUpdateDetail,
  deleteDetail,
} from "@/api/inventory/combinationgoods"
const tableCols =[
      //  {label:"操作",width:"85",type:'button',btnList:[
      //     {label:"修改 ",handle:(that,row)=>that.onEdit(row)},
      //     {label:" 删除",handle:(that,row)=>that.onDelete(row)}
      //   ]},
       {istrue:true,prop:'combinationCode',label:'组合商品编码', width:'200',sortable:'custom'},
       {istrue:true,prop:'combinationName',label:'组合商品名称', width:'auto'}, 
       {istrue:true,prop:'createdTime',label:'创建时间', width:'160',sortable:'custom'},
       {istrue:true,prop:'createdUserName',label:'创建人', width:'100',sortable:'custom'},
       //{istrue:true,prop:'modifiedTime',label:'修改时间', width:'160',sortable:'custom'},
       //{istrue:true,prop:'modifiedUserName',label:'修改人', width:'100',sortable:'custom'}
     ];
const tableHandles1=[
        //{label:"新增", handle:(that)=>that.onAdd()},
        //{label:"批量删除", handle:(that)=>that.onDeleteBatch()},
        {label:"导入模板", handle:(that)=>that.downloadTemplate()},
        {label:"导入", handle:(that)=>that.startImport()},
        {label:"导出", handle:(that)=>that.onExport()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: {
        combinationCode:null,
        combinationName:null,
        goodsCode:null,
        goodsName:null
      },
      list: [],
      summaryarry:{},
      pager:{OrderBy:"combinationCode",IsAsc:true},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      dialogVisible: false,
      autoform:{
               fApi:{},
               rule:[],
               options:{submitBtn:false},
        },
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,     
      deleteLoading: false,
      formtitle:"新增",
      fileList:[],
      selids:[],//选择的id
      detail:{
         pager:{OrderBy:"goodsCode",IsAsc:true},
         list: [],
         tableCols:[
            {istrue:true,prop:'goodsCode',label:'商品编码', width:'120',sortable:'custom'},
            {istrue:true,prop:'goodsName',label:'商品名称', width:'auto'}, 
            {istrue:true,prop:'combinationQty',label:'数量', width:'100',sortable:'custom'},
         ],
         listLoading: false,
         total:0,
         sels: [], 
      },
      detailParentId:null,
    }
  },
  async mounted() {
    await this.getlist();  
  },
  methods: {
    //下载导入模板
    downloadTemplate(){
        window.open("../static/excel/组合编码导入模板.xlsx","_self");
    },
    //开始导入
    startImport(){
      this.dialogVisible=true;
    },
    //取消导入
    cancelImport(){
      this.dialogVisible=false;
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {        
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    //提交导入
    submitUpload() {
        this.$confirm('即将覆盖原有组合编码数据，是否继续？', '提示', {
          confirmButtonText: '继续',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$refs.upload.submit();  
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
             
    },
    //上传文件
    uploadFile(item) {
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      const res = importData(form);
       this.$message({
                      message: '上传成功,正在导入中...',
                      type: "success",
                    }); 
    },
    //导出
    async onExport(){
        var params=this.getCondition();
        if(params===false){
            return;
        }
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportData(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','组合编码_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },
    //获取查询条件
    getCondition(){
      var pager = this.$refs.pager.getPager();
      var page  = this.pager;
      const params = {
        ...pager,
        ...page,
        ... this.filter
      }

      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
      await this.onSearchDetail(0);
    },
    //分页查询
    async getlist() {
      var params=this.getCondition();
      if(params===false){
            return;
      }

      this.listLoading = true
      const res = await getList(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry=res.data.summary;
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
    //排序查询
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else{
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    },
    async onEdit(row) {
        var arr = Object.keys(this.autoform.fApi);
        if(arr.length ==0)
              await this.onAdd()
      this.formtitle='编辑';
      this.addFormVisible = true
      const res = await getById(row.id )
      if (res.data&&res.data.platform==0) res.data.platform=null
      await this.autoform.fApi.setValue(res.data)
    },
   async onAdd() {
      this.formtitle='新增';
      this.addFormVisible = true;
      let that=this;
      this.autoform.rule=[{type:'hidden',field:'id',title:'id',value: 0},
                      {type:'input',field:'combinationCode',title:'组合商品编码',width:200,validate:[{type:"string",required:true,message:"请填写组合商品编码"}]},
                      {type:'input',field:'combinationName',title:'组合商品名称',width:200,validate:[{type:"string",required:true,message:"请填写组合商品名称"}]}
                 ];
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
          this.autoform.fApi.reload()
    },
    async onEditSubmit() {
      this.addFormVisible = true
      await onAddSubmit();
    },
    async onAddSubmit() {
       this.addFormVisible=true;
       this.addLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();

          const res = await addOrUpdate(formData);
          this.onSearch();
          
          this.addFormVisible=false;
        }else{
          //todo 表单验证未通过
        }
     })
      this.addLoading=false;
    },   
    async onDelete(row) {
      row._loading = true
      this.$confirm('确定删除吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
            const res = await deleteData({id:row.id})
            row._loading = false
            if (!res?.success) {return }
            this.$message({
                type: 'success',
                message: '删除成功!'
            });
            this.onSearch();
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
           row._loading = false
        });
    },
    async onDeleteBatch() {
      if(!this.selids||this.selids.length==0){
        this.$message({
                type: 'warning',
                message: '请勾选删除的行'
            });
        return;
      }
      this.$confirm('确定删除吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
            const res = await deleteDataBatch({ids:this.selids.join(",")})
            if (!res?.success) {return }
            this.$message({
                type: 'success',
                message: '删除成功!'
            });
            this.onSearch();
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },
    selsChange: function(sels) {
      this.sels = sels;
    },
    selectchange:function(rows,row) {
      this.selids=[];console.log(rows)
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
    cellclick(row, column, cell, event){
       this.onSearchDetail(row.id);
    },
    //明细查询
    //查询第一页
    async onSearchDetail(parentId) {
      this.$refs.pagerDetail.setPage(1);
      this.detailParentId=parentId;
      await this.getDetailList(parentId);
    },
    //明细分页查询
    async getDetailList(parentId) {
      //this.detail.listLoading = true;
      var pager = this.$refs.pagerDetail.getPager();
      var page  = this.detail.pager;
      const params = {
        ...pager,
        ...page,
        parentId:parentId
      }
      const res = await getDetailList(params)
      this.listLoading = false;
      if (!res?.success) {
        return
      }
      this.detail.total = res.data.total;
      const data = res.data.list;
      data.forEach(d => {
        d._loading = false
      })
      this.detail.list = data
    },
    //排序查询
    async sortchangeDetail(column){
      if(!column.order)
        this.detail.pager={};
      else{
        this.detail.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearchDetail(this.detailParentId);
    },
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary{
  margin-right: 7px;
}
</style>
