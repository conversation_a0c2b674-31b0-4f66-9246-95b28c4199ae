<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-select multiple v-model="filter.groupNameList" placeholder="组名称" clearable :collapse-tags="true" filterable
          class="publicCss">
          <el-option v-for="item in filterGroupList" :key="item" :label="item" :value="item"></el-option>
        </el-select>
        <el-input v-model="filter.sname" v-model.trim="filter.sname" placeholder="姓名" clearable :maxlength="50" class="publicCss" />
        <el-date-picker v-model="filter.sdate" type="daterange" unlink-panels range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" :clearable="false" :picker-options="pickerOptions">
        </el-date-picker>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button>
      </div>
    </template>
    <vxetableNotFixNum :id="'sqSphPersonalEfficiencyStatistics202506201539'"
      :tablekey="'sqSphPersonalEfficiencyStatistics202506201539'" ref="table" :that='that' :isIndex='true'
      :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='inquirslist' :tableCols='tableCols'
      :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="listLoading" :height="'100%'" :border="true">
    </vxetableNotFixNum>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getinquirsList" />
    </template>
    <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
      :close-on-click-modal="false" v-dialogDrag>
      <div>
        <span>
          <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>
<script>

import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import dayjs from "dayjs";
import datepicker from '@/views/customerservice/datepicker'
import buschar from '@/components/Bus/buschar'
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import vxetableNotFixNum from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { formatTime } from "@/utils";
import { getGroupNameList, getSPHPersonalInquirsAsync, getSPHPersonalInquirsList, exportSPHPersonalInquirsAsync, getSPHPersonalInquirsMap } from '@/api/customerservice/shipinhaoinquirs.js';


import Decimal from 'decimal.js';
function precision(number, multiple) {
  return new Decimal(number).mul(new Decimal(multiple));
}

const tableCols = [
  { istrue: true, prop: 'groupName', label: '组名称', width: '150', sortable: 'custom' },
  {
    istrue: true, prop: 'sname', label: '姓名', width: '100', type: "click",
    handle: (that, row, column, cell) => that.canclick(row, column, cell), formatter: (row) => row.sname
  },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '咨询用户数', prop: 'inquirsCount' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '咨询会话数', prop: 'receiveCount' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '3分钟人工回复率', prop: 'threeSecondReplyRate', formatter: (row) => row.threeSecondReplyRate.toFixed(2) + "%" },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '3分钟回复人数', prop: 'threeSecondReplyCount', },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '平均回复时长（秒）', prop: 'responseTime' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '用户满意度', prop: 'satisDegree', formatter: (row) => row.satisDegree.toFixed(2) + "%" },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '满意人数', prop: 'satisDegreeCount', },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '不回复率', prop: 'noReplyRate', formatter: (row) => row.noReplyRate.toFixed(2) + "%" },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '不回复人数', prop: 'noReplyCount', },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '出勤人次', prop: 'dutyCount' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '人均接待量', prop: 'perReceptionCount' },

  { istrue: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 'auto', formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];

export default {
  name: "sqSphPersonalEfficiencyStatistics",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar, vxetablebase, vxetableNotFixNum },
  props: {
    pickerOptions: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      that: this,
      filter: {
        groupType: 0,
        inquirsType: 0,
        isLeaveGroup: false,
        sdate: [formatTime(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30), "YYYY-MM-DD 00:00:00"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],
        groupNameList: [],
      },
      filterGroupList: [],
      inquirslist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: { count_sum: 10 },
      pager: { OrderBy: "groupName", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids: [],
      dialogMapVisible: { visible: false, title: "", data: [] },
      fileList: [],
    };
  },
  async mounted() {
    this.onSearch();
  },
  methods: {
    loadData2(args) {
      this.filter.sdate = args.sdate;
      this.filter.groupNameList = [args.groupName];
      this.onSearch();
    },
    async getSPHGroup() {
      let res = await getGroupNameList({ isLeaveGroup: this.filter.isLeaveGroup, groupType: this.filter.groupType });
      this.filterGroupList = res.data;
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getinquirsList();
      this.getSPHGroup();
    },
    getParam() {
      if (this.filter.sdate) {
        this.filter.startDate = this.filter.sdate[0];
        this.filter.endDate = this.filter.sdate[1];
      }
      else {
        this.filter.startDate = null;
        this.filter.endDate = null;
      }
      const para = { ...this.filter };
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      return params;
    },
    async getinquirsList() {
      let params = this.getParam();
      this.listLoading = true;
      const res = await getSPHPersonalInquirsAsync(params);
      this.listLoading = false;
      this.total = res.data.total;
      this.inquirslist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    async showchart(row) {
      let params = this.getParam();
      params.groupName = row.groupName;
      params.sname_DDid = row.sname_DDid;
      params.sname = row.sname;
      let that = this;
      const res = await getSPHPersonalInquirsMap(params).then(res => {
        that.dialogMapVisible.visible = true;
        that.dialogMapVisible.data = res
        that.dialogMapVisible.title = res.title;
        res.title = "";
      })
      this.dialogMapVisible.visible = true
    },
    afterSave() {

    },
    async canclick(row, column, cell) {
      let args = {
        ...this.filter,
        groupName: row.groupName,
        sname_DDid: row.sname_DDid,
        sname: row.sname,
      };
      this.$showDialogform({
        path: `@/views/customerservice/shipinhao/sphInquirsstatisticsusershop.vue`,
        title: '个人效率按店统计',
        args: args,
        height: '600px',
        width: '75%',
        callOk: this.afterSave
      });
    },
    async onExport() {
      let params = this.getParam();
      this.listLoading = true
      const res = await exportSPHPersonalInquirsAsync(params)
      this.listLoading = false
      if (!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '视频号个人效率统计(售前)_' + new Date().toLocaleString() + '.xlsx');
      aLink.click()
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 60px;
}

.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 250px;
    margin-right: 5px;
  }
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
