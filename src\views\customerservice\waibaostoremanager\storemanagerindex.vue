<template>
    <my-container>
      <!--顶部操作-->
      <div class=".top">
        <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
           <el-form-item label="平台:">
                <el-select v-model="Filter.platform" placeholder="请选择" class="el-select-content" clearable>
                      <el-option v-for="item in platformTypeList" :key="item.value" :label="item.name" :value="item.value" />
                </el-select>
          </el-form-item>

          <el-form-item label="店铺:">
                <el-input maxlength="50" placeholder="请输入" v-model.trim="Filter.shopName" clearable />
          </el-form-item>
          <el-form-item>

            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onImportSyj" v-if="checkPermission(['api:Customerservice:OutSource:ImportOutSourceStoreAsync'])" >导入</el-button>
            <el-button type="primary" @click="addgroupdialogVisibleSyj = true" v-if="checkPermission(['api:Customerservice:OutSource:AddOutSourceStoreInfo'])" >新增</el-button>
            <el-button type="primary" @click="ClickdownloadTemplate">下载模板</el-button>
          </el-form-item> 
        </el-form>
      </div>

  
      <!--列表-->
      <Ces-table ref="table" :that="that" :isIndex="true" @sortchange="sortchange" :tableData="tableData"
        :showsummary="true"   :tableCols="tableCols" :loading="listLoading" style="width: 100%;
      height: calc(100% - 10%); margin: 0">
      </Ces-table>
  
      <!--分页-->
      <template #footer>
        <my-pagination ref="pager" :total="total" @get-page="gettrainplanList" />
      </template>
      <!-- 添加店铺管理 -->
      <el-dialog title="添加客服人员分组管理信息" :visible.sync="addgroupdialogVisibleSyj" width="40%" :close-on-click-modal="false" v-dialogDrag>
            <span>
                <el-form :model="addForm" ref="addForm" :rules="addFormRules" label-width="100px" >
                    <el-form-item label="平台:" prop="platform">
                          <el-select v-model="addForm.platform" placeholder="请选择" class="el-select-content"  style="width:83%" clearable>
                                <el-option v-for="item in platformTypeList" :key="item.value" :label="item.name" :value="item.value" />
                          </el-select>
                    </el-form-item>
                    <el-form-item label="店铺：" prop="shopName">
                            <el-input style="width:83%"  v-model="addForm.shopName" :maxlength="20"></el-input>
                    </el-form-item>
                </el-form>
            </span>

            <span slot="footer" class="dialog-footer">
                <el-button @click="addgroupdialogVisibleSyj = false">关闭</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="addgroup">确定</el-button>
            </span>
      </el-dialog>
  
      <!-- 编辑店铺管理 -->
      <el-dialog title="编辑店铺管理" :visible.sync="updategroupdialogVisibleSyj" width="40%"
            :close-on-click-modal="false" v-dialogDrag>
           <span>
                <el-form :model="updateForm" ref="updateForm" :rules="addFormRules" label-width="100px" >
                 
                    <el-form-item label="平台:" prop="platform">
                      <el-select v-model="updateForm.platform" placeholder="请选择" class="el-select-content" style="width:83%" clearable>
                        <el-option  v-for="item in platformTypeList" :key="item.value" :label="item.name" :value="item.value" />
                      </el-select>
                    </el-form-item>
 
                    <el-form-item label="店铺" prop="shopName">
                        <el-input style="width:83%" v-model="updateForm.shopName" :maxlength="20"></el-input>
                    </el-form-item>

                </el-form>
            </span> 

            <span slot="footer" class="dialog-footer">
                <el-button @click="updategroupdialogVisibleSyj = false">关闭</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="updategroup()">确定</el-button>
            </span> 
      </el-dialog>



       <!-- 导入 -->
      <el-dialog title="外包客服店铺管理" :visible.sync="dialogVisibleSyj" width="30%" :close-on-click-modal="false" v-dialogDrag>
            <span>
                  <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false"  action
                            accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                    </el-upload>
                <!-- <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2" :on-change="onUploadChange2"
                    :on-remove="onUploadRemove2">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                        @click="onSubmitupload2">上传</my-confirm-button>
                </el-upload> -->
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
      </el-dialog>

    </my-container>
  </template>
  <script>
  import MyContainer from "@/components/my-container";
  import CesTable from "@/components/Table/table.vue";
  import MyConfirmButton from "@/components/my-confirm-button";
  import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
  import {getOutSourceStoreList,updateOutSourceStoreInfo,deleteOutSourceStore,addOutSourceStoreInfo,importOutSourceStoreAsync} from "@/api/customerservice/waibaocustomer";
  
  //平台下拉
   const platformTypeList=[
          { name: "拼多多", value: 2 },
          { name: "抖音", value: 6 },
   ];
  
  const tableCols = [
   {
      istrue: true,
      prop: "id",
      label: "ID",
    },
    {
      istrue: true,
      prop: "platform",
      label: "平台",
       formatter:(row)=>
        {
          return  platformTypeList.filter(item=>item.value==row.platform)[0].name 
        },
    },
    {
      istrue: true,
      prop: "shopName",
      label: "店铺",
    },
    {
      istrue: true,
      type: "button",
      label: "操作",
      align: "center",
       fixed: "right",
      btnList: [
         { label: "修改", handle: (that, row) => that.handleupdategroup(row), permission: "api:Customerservice:OutSource:UpdateOutSourceStoreInfo" },
         { label: "删除", handle: (that, row) => that.deletegroup(row) , permission: "api:Customerservice:OutSource:DeleteOutSourceStore" }
        ],
    },
  ];
  export default {
    name: "storemanagerindex",
    components: {
      MyContainer,
      CesTable,
      OrderActionsByInnerNos,
    },
    data() {
      return {
        that: this,
        Filter: {
          platform:null,
          shopName:null,
        },
        platformTypeList:platformTypeList,
        tableData: [],
        tableCols: tableCols,
        total: 0,
        pager: { orderBy: "createdTime", isAsc: false },
        listLoading: false,
        shopList: [],
        addFormRules: {
                 platform: [{ required: true, message: '必填', trigger: 'blur' }],
                 shopName: [{ required: true, message: '必填', trigger: 'blur' }],
            },     //表单校验
        updategroupdialogVisibleSyj: false,  //修改弹出框控制
        addgroupdialogVisibleSyj: false,     //添加弹出框控制
        dialogVisibleSyj: false,             //导入弹出框控制
        fileList: [],                        //上传文件
        updateForm: {
          platform: null,
            shopName: null,
        },  //修改框绑定值
        addForm: {
            platform: null,
            shopName: null,
        }, //添加绑定值
        uploadLoading:false,
      };
    },
    async mounted() {
      await this.onSearch();
    },
    methods: {
      // 查询
      async onSearch() {
          await this.gettrainplanList();
      },
      getCondition() {
        const para = { ...this.Filter };
        var pager = this.$refs.pager.getPager();
        const params = {
          ...pager,
          ...this.pager,
          ...para,
        };
        return params;
      },
      async gettrainplanList() {
        var params = this.getCondition();
        if (params === false) {
          return;
        }
        this.listLoading = true;
        const res = await getOutSourceStoreList(params);
        this.listLoading = false;
        if (!res?.success) {
          return;
        }
        this.total = res.data.total;
        const resData = res.data.list;
        this.tableData = resData;
      },
      sortchange(column) {
        if (!column.order) this.pager = {};
        else
          this.pager = {
            orderBy: column.prop,
            isAsc: column.order.indexOf("descending") == -1 ? true : false,
          };
        this.onSearch();
      },
      //修改弹出框控制方法
      async handleupdategroup(row) {
            this.updateForm = JSON.parse(JSON.stringify(row));
            this.updategroupdialogVisibleSyj = true;

        },
        //修改
      async updategroup(row) {
           if (!this.updayeSubmitValidate()) {
              return;
            }
            var res = await updateOutSourceStoreInfo(this.updateForm)
            if (res?.success) {
                this.$message({ message: '操作成功', type: "success" });
                this.onRefresh()
                this.updateForm = {}
                this.updategroupdialogVisibleSyj = false
            }else{
                this.$message({ message: res?.data.msg, type: "error" });
            }


        },
        //删除
        async deletegroup(row) {
            var that = this;
            this.$confirm("此操作将删除此客服人员分组管理数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(async () => {
                    var res = await deleteOutSourceStore({ id: row.id })
                    if (res?.success) {
                        that.$message({ message: '已删除', type: "success" });
                        that.onRefresh()
                    }

                });

        },
        updayeSubmitValidate: function () {
            let isValid = true;
            this.$refs.updateForm.validate((valid) => {
              isValid = valid;
            });
            return isValid;
        },
        onSubmitValidate: function () {
            let isValid = true;
            this.$refs.addForm.validate((valid) => {
              isValid = valid;
            });
            return isValid;
        },
        //添加
        async addgroup() {
           if (!this.onSubmitValidate()) {
              return;
            }
            var res = await addOutSourceStoreInfo(this.addForm)
            if (res?.success) {
              this.$message({ message: '已添加', type: "success" });
              this.onRefresh()
              this.addForm = {}
              this.addgroupdialogVisibleSyj = false
            }else{
                this.$message({ message: res?.data.msg, type: "error" });
            }
        },
        onRefresh() {    //页面刷新
            this.onSearch()
            var self = this;
            setTimeout(() => {
                self.reload();
            }, 100);
        },
        onImportSyj() {          //导入弹出框
            this.dialogVisibleSyj = true
        },
       

        ClickdownloadTemplate() {    //下载模版
            window.open("/static/excel/customerservice/店铺导入模板.xlsx", "_blank");
            //window.location.href = '/static/excel/customerservice/咨询数据模板.xlsx';
        },

        async uploadFile () {
            if (this.fileList.length ==0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false
            };
            const form = new FormData();
            form.append("upfile", this.fileList[0].raw);
            //form.append("platform", 1);
            var res = await importOutSourceStoreAsync(form);
            if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
            else this.$message({ message: res.msg, type: "warning" });
            this.$refs.upload.clearFiles()
            this.uploadLoading = false;
            this.dialogVisibleSyj = false;
            this.fileList = [];
        },
        async uploadChange (file, fileList) {
            if (fileList.length == 2) {
                fileList.splice(1, 1);
                this.$message({ message: "只允许单文件导入", type: "warning" });
                return false;
            }
            this.fileList.push(file);
        },
        uploadRemove (file, fileList) {
            this.fileList.splice(0, 1);
        },
        submitUpload () {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return;
            }
            this.uploadLoading = true
            this.$refs.upload.submit();
        },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  
  ::v-deep .mycontainer {
    position: relative;
  }
  
  .uptime {
    font-size: 14px;
    position: absolute;
    right: 30px;
  }
  //解决下拉菜单多选由文字太长导致样式问题
  ::v-deep .el-select__tags-text {
    max-width: 45px;
  }
  </style>
  