<template>
  <container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
      <el-form-item  label="确认日期">
         <!-- <el-date-picker style="width: 120px" v-model="filter.timeRefund" type="date" format="yyyyMMdd" value-format="yyyy-MM-dd" placeholder="选择时间"></el-date-picker> -->
          <el-date-picker style="width: 210px" v-model="filter.TimeRefundtimerange" type="datetimerange" format="yyyyMMdd" value-format="yyyy-MM-dd" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false"></el-date-picker>
        </el-form-item>
      <el-form-item  label="进仓日期">
         <!-- <el-date-picker style="width: 120px" v-model="filter.yearMonthDayStockIn" type="date" format="yyyyMMdd" value-format="yyyy-MM-dd" placeholder="选择时间"></el-date-picker> -->
          <el-date-picker style="width: 210px" v-model="filter.TimeStockIntimerange" type="datetimerange" format="yyyyMMdd" value-format="yyyy-MM-dd" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item  label="付款日期">
         <!-- <el-date-picker style="width: 120px" v-model="filter.yearmonthdaypay" type="date" format="yyyyMMdd" value-format="yyyy-MM-dd" placeholder="选择时间"></el-date-picker> -->
          <el-date-picker style="width: 210px" v-model="filter.TimePaytimerange" type="datetimerange" format="yyyyMMdd" value-format="yyyy-MM-dd" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item  label="发货日期">
         <!-- <el-date-picker style="width: 120px" v-model="filter.timeSend" type="date" format="yyyyMMdd" value-format="yyyy-MM-dd" placeholder="选择时间"></el-date-picker> -->
          <el-date-picker style="width: 210px" v-model="filter.TimeSendtimerange" type="datetimerange" format="yyyyMMdd" value-format="yyyy-MM-dd" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item  label="订单日期">
         <!-- <el-date-picker style="width: 120px" v-model="filter.orderTime" type="date" format="yyyyMMdd" value-format="yyyy-MM-dd" placeholder="选择时间"></el-date-picker> -->
          <el-date-picker style="width: 210px" v-model="filter.OrderTimetimerange" type="datetimerange" format="yyyyMMdd" value-format="yyyy-MM-dd" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>

         <el-form-item label="线上订单号:" label-position="right" >
            <el-input v-model="filter.orderNo" style="width:183px;" maxlength="20"/>
        </el-form-item>

       <el-form-item label="产品ID:" label-position="right" >
            <el-input v-model="filter.proCode" style="width:183px;" maxlength="20"/>
        </el-form-item>
        <el-form-item label="商品编码:" label-position="right" >
            <el-input v-model="filter.goodsCode" style="width:183px;" maxlength="20"/>
        </el-form-item>

          <el-form-item label="平台:">
         <el-select filterable v-model="filter.platform" placeholder="请选择平台" @change="onchangeplatform" clearable style="width: 130px">
            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="所属店铺:">
          <el-select filterable v-model="filter.shopCode" placeholder="请选择店铺" clearable style="width: 130px">
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"/>
          </el-select>
        </el-form-item>

        <el-form-item label="售后分类:">
          <el-select filterable v-model="filter.afterSaleType" placeholder="请选择售后分类" clearable style="width: 130px">
            <el-option label="补发" value="0"/>
            <el-option label="换货" value="1"/>
            <el-option label="仅退款" value="2"/>
            <el-option label="拒绝退货" value="3"/>
            <el-option label="普通退货" value="4"/>
          </el-select>
        </el-form-item>

         <el-form-item label="订单状态:">
          <el-select filterable v-model="filter.orderStatus" placeholder="请选择订单状态" clearable style="width: 130px">
            <el-option label="待付款" value="待付款"/>
            <el-option label="已付款待审核" value="已付款待审核"/>
            <el-option label="已客审待财审" value="已客审待财审"/>
            <el-option label="发货中" value="发货中"/>
            <el-option label="已发货" value="已发货"/>
            <el-option label="异常" value="异常"/>
            <el-option label="已取消" value="已取消"/>
          </el-select>
        </el-form-item>
          <!-- <el-form-item label="店铺名称:" label-position="right" >
            <el-input v-model="filter.shopName" style="width:183px;"/>
        </el-form-item>-->
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
    <!--列表-->
    <!-- <ces-table ref="table" :that='that' :isIndex='true'  :hasexpand='false' @sortchange='sortchange' :tableData='list'
              :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' @summaryClick='onsummaryClick'
              @select='selectchange' :isSelection='false' :tableCols='tableCols' :loading="listLoading" :isSelectColumn='true'>
    </ces-table> -->

    <vxetablebase
      :id="'refunddetail202302031421'" :border="true" :align="'center'" :tablekey="'refunddetail202302031421'"
      ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :isSelectColumn="true" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
      :tableData='list' @summaryClick='onsummaryClick' :tableCols='tableCols'
      :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:96%;margin: 0"   :xgt="9999">
    </vxetablebase>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length"  @get-page="getList"/>
    </template>
    <el-dialog title="导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <span>
          <!-- <el-row>
            <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
              <el-date-picker style="width: 100%" v-model="onimportfilter.yearmonth" type="date" format="yyyyMMdd" value-format="yyyyMMdd" placeholder="选择时间"></el-date-picker>
            </el-col>
         </el-row> -->
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx"
                  :http-request="uploadFile" :file-list="fileList" :data="fileparm" :on-change="onsuccess" :on-remove="onremove">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
              </el-upload>
            </el-col>
          </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="批量删除" :visible.sync="dialogdeletebatchNumberVisible" width="500px">
      <el-row>
           <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
              <el-date-picker style="width: 100%" v-model="deletefilter.timeStockInDay" type="date" format="yyyyMMdd" value-format="yyyyMMdd" placeholder="选择进仓日期"></el-date-picker>
              <!-- <el-input placeholder="请输入批次号" v-model="deletefilter.batchNumber" style="width: 100%"></el-input> -->
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
             <el-button type="primary" @click="deleteByBatch">删除</el-button>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogdeletebatchNumberVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 趋势图 -->
    <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
      <span>
        <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>

  </container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList,getList as getshopList } from '@/api/operatemanage/base/shop';
import {pageRefundDetail,deleteRefundDetail,importRefundDetail, pageRefundDetailAnalysis} from '@/api/financial/refund'
import {formatPlatform,formatYesornoBool,formatLinkProCode,platformlist} from "@/utils/tools";
import buschar from '@/components/Bus/buschar'

const tableCols =[
      // {istrue:true,prop:'yearMonthDay',label:'年月日', width:'90',sortable:'custom'},
      // {istrue:true,prop:'numberInternalOrder',label:'内部订单号', width:'120',sortable:'custom'},
      {istrue:true,prop:'orderNo',label:'原始线上单号', width:'150',sortable:'custom'},
      {istrue:true,prop:'proCode',label:'宝贝ID',sortable:'custom', width:'120',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
      {istrue:true,prop:'platform',fix:true,label:'平台', width:'60',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
      {istrue:true,prop:'goodsCode',label:'商品编码',sortable:'custom', width:'120'},
      {istrue:true,prop:'shopCode',label:'店铺', width:'160',sortable:'custom',formatter:(row)=> row.shopName},
      {istrue:true,prop:'warehouse',label:'发货仓', width:'100',sortable:'custom',formatter:(row)=> row.warehouseStr},
      {istrue:true,prop:'warehouseReceive',label:'收货仓', width:'80',sortable:'custom'},
      {istrue:true,summaryEvent:true,prop:'refundAmount',label:'退货金额',sortable:'custom', width:'80'},
       {istrue:true,summaryEvent:true,prop:'refundCost',label:'退货成本',sortable:'custom', width:'80'},
      {istrue:true,summaryEvent:true,prop:'refundCostSj',label:'实退成本',sortable:'custom', width:'80'},
      {istrue:true,prop:'orderTime',label:'订单日期',sortable:'custom', width:'150'},
      {istrue:true,prop:'timePay',label:'付款日期',sortable:'custom', width:'150'},
      {istrue:true,prop:'timeSend',label:'发货日期',sortable:'custom', width:'150',},
      {istrue:true,prop:'timeRefund',label:'确认日期',sortable:'custom', width:'150'},
      {istrue:true,prop:'timeStockIn',label:'进仓日期',sortable:'custom', width:'150'},
      {istrue:true,prop:'orderStatus',label:'订单状态',sortable:'custom', width:'auto'},
      {istrue:true,prop:'afterSaleType',label:'售后分类',sortable:'custom', width:'auto',formatter:(row)=> !row.afterSaleTypeName? " " : row.afterSaleTypeName},
      {istrue:true,prop:'typeProblem',label:'问题类型', width:'auto'},
      {istrue:true,prop:'goodsStatus',label:'货物状态',sortable:'custom', width:'auto'},
      //{istrue:true,prop:'amountRefundCost',label:'当期退货成本',sortable:'custom', width:'auto'},
     ];
 const tableHandles=[
        {label:"导入", handle:(that)=>that.onstartImport()},
        {label:"批量删除", handle:(that)=>that.ondeleteByBatch()},
        {label:"刷新", handle:(that)=>that.onRefresh()},
      ];

const startTime = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
  name: "Users",
  components: { container, MyConfirmButton, MySearch, MySearchWindow ,cesTable, buschar,vxetablebase},
  data() {
    return {
      that:this,
      filter: {
        startTimeStockIn:null,
        endTimeStockIn:null,
        startTimePay:null,
        endTimePay:null,
        startTimeSend:null,
        endTimeSend:null,
        startTimeOrder:null,
        endTimeOrder:null,
        startTimeRefund:null,
        endTimeRefund:null,
        yearMonthDayStockIn:null,
        yearmonthdaypay:null,
        //timeSend:null,//发货日期
        //orderTime:null,//订单日期
        //timeRefund:null,//退款时间
        //afterSaleType:null,//售后分类
        orderNo:null,
        proCode:null,
        goodsCode:null,
        platform:null,
        shopCode:null,
        TimeStockIntimerange:null,
        TimePaytimerange:null,
        TimeSendtimerange:null,
        OrderTimetimerange:null,
        TimeRefundtimerange:[startTime, endTime]
      },
      onimportfilter:{
        yearmonth:null,
      },
      deletefilter:{timeStockInDay:null},
      summaryarry:{},
      platformlist:platformlist,
      list:[],
      shopList:[],
      userList:[],
      groupList:[],
      tableCols:tableCols,
      tableHandles:tableHandles,
      total: 0,
      pager:{OrderBy:"id",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids:[],
      fileList:[],
      dialogVisible:false,
      uploadLoading:false,
      importFilte:{},
      fileparm:{},
      dialogdeletebatchNumberVisible:false,
      buscharDialog:{visible:false,title:"",data:[]},
    };
  },
  async mounted() {
    //await this.getShopList();
  },
  methods: {
    onsuccess(file, fileList){
      this.fileList = fileList;
    },
    onremove(file, fileList){
      this.fileList = fileList;
    },
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    // async getShopList(){
    //   const res1 = await getAllShopList();
    //   this.shopList=[];
    //   res1.data?.forEach(f => {
    //     if(f.isCalcSettlement&&f.shopCode)
    //       this.shopList.push(f);
    //   });
    // },
    onRefresh(){
        this.onSearch()
    },
    onSearch(){
       this.$refs.pager.setPage(1);
       this.getList();
    },
    async getList(){
      var pager = this.$refs.pager.getPager();

      this.filter.startTimeStockIn = null;
      this.filter.endTimeStockIn = null;
      if (this.filter.TimeStockIntimerange) {
          this.filter.startTimeStockIn = this.filter.TimeStockIntimerange[0];
          this.filter.endTimeStockIn = this.filter.TimeStockIntimerange[1];
      }
      this.filter.startTimePay = null;
      this.filter.endTimePay = null;
      if (this.filter.TimePaytimerange) {
          this.filter.startTimePay = this.filter.TimePaytimerange[0];
          this.filter.endTimePay = this.filter.TimePaytimerange[1];
      }
      this.filter.startTimeSend = null;
      this.filter.endTimeSend = null;
      if (this.filter.TimeSendtimerange) {
          this.filter.startTimeSend = this.filter.TimeSendtimerange[0];
          this.filter.endTimeSend = this.filter.TimeSendtimerange[1];
      }
      this.filter.startTimeOrder = null;
      this.filter.endTimeOrder = null;
      if (this.filter.OrderTimetimerange) {
          this.filter.startTimeOrder = this.filter.OrderTimetimerange[0];
          this.filter.endTimeOrder = this.filter.OrderTimetimerange[1];
      }
      this.filter.startTimeRefund = null;
      this.filter.endTimeRefund = null;
      if (this.filter.TimeRefundtimerange) {
          this.filter.startTimeRefund = this.filter.TimeRefundtimerange[0];
          this.filter.endTimeRefund = this.filter.TimeRefundtimerange[1];
      }


      const params = {...pager,...this.pager,...this.filter};
      console.log(params)
      this.listLoading = true;
      const res = await pageRefundDetail(params);
      this.listLoading = false;
      this.total = res.data?.total
      this.list = res.data?.list;
      this.summaryarry= res.data?.summary;
    },
    async onchangeplatform(val){
      this.categorylist =[]
      const res1 = await getshopList({platform:val,CurrentPage:1,PageSize:300});
      this.shopList=res1.data.list
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
    async deleteByBatch() {
      if (!this.deletefilter.timeStockInDay) {
       this.$message({type: 'warning',message: '请输入开始进仓日期!'});
       return;
      }
      this.$confirm('确认删除, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
        }).then(async () => {
            const res = await deleteRefundDetail(this.deletefilter)
            if (!res?.success) {return }
            this.$message({type: 'success',message: '删除成功!'});
            this.onSearch()
        }).catch(() => {
          this.$message({type: 'info',message: '已取消删除'});
        });
    },
   async onstartImport(){
      this.dialogVisible=true;
      this.uploadLoading=false;
    },
   async ondeleteByBatch() {
        this.dialogdeletebatchNumberVisible=true;
        this.deletefilter.batchNumber='';
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      if(this.fileList.length<1){
        this.$message.error('请先选择文件！');
        return;
      }
      this.uploadLoading=true
      this.$refs.upload.submit();
    },
   async uploadFile(item) {

      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearmonthday", this.onimportfilter.yearmonth);
      var res = await importRefundDetail(form);
      if (res.code==1)
      {
      this.$message({message: "上传成功,正在导入中...", type: "success" });
      this.$refs.upload.clearFiles();
      this.uploadLoading=false
      this.dialogVisible=false;
      this.fileList=[];
      }
      else
      {
        this.$refs.upload.clearFiles();
        this.uploadLoading=false
        this.dialogVisible=false;
        this.fileList=[];
      }

    },
    //汇总趋势图
    async onsummaryClick(property){
      // this.$message({message:property,type:"warning"});
      var pager = this.$refs.pager.getPager();

    this.filter.startTimeStockIn = null;
    this.filter.endTimeStockIn = null;
    if (this.filter.TimeStockIntimerange) {
        this.filter.startTimeStockIn = this.filter.TimeStockIntimerange[0];
        this.filter.endTimeStockIn = this.filter.TimeStockIntimerange[1];
    }
    this.filter.startTimePay = null;
    this.filter.endTimePay = null;
    if (this.filter.TimePaytimerange) {
        this.filter.startTimePay = this.filter.TimePaytimerange[0];
        this.filter.endTimePay = this.filter.TimePaytimerange[1];
    }
    this.filter.startTimeSend = null;
    this.filter.endTimeSend = null;
    if (this.filter.TimeSendtimerange) {
        this.filter.startTimeSend = this.filter.TimeSendtimerange[0];
        this.filter.endTimeSend = this.filter.TimeSendtimerange[1];
    }
    this.filter.startTimeOrder = null;
    this.filter.endTimeOrder = null;
    if (this.filter.OrderTimetimerange) {
        this.filter.startTimeOrder = this.filter.OrderTimetimerange[0];
        this.filter.endTimeOrder = this.filter.OrderTimetimerange[1];
    }
    this.filter.startTimeRefund = null;
    this.filter.endTimeRefund = null;
    if (this.filter.TimeRefundtimerange) {
        this.filter.startTimeRefund = this.filter.TimeRefundtimerange[0];
        this.filter.endTimeRefund = this.filter.TimeRefundtimerange[1];
    }


const params = {...pager,...this.pager,...this.filter};
        params.column=property;
        let that=this;
        const res = await pageRefundDetailAnalysis(params).then(res=>{
          that.buscharDialog.visible=true;
          that.buscharDialog.data=res.data
          that.buscharDialog.title=res.data.legend[0]
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
