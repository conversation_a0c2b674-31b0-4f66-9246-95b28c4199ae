<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right"
        label-width="90px">
        <el-form-item>
          <el-input v-model="filter.styleCode" placeholder="系列编码" style="width: 120px" clearable />
        </el-form-item>
        <el-form-item>
          <el-input v-model="filter.proCode" placeholder="宝贝ID" style="width: 120px" clearable />
        </el-form-item>
        <el-form-item>
          <el-input v-model="filter.receiveUserName" placeholder="被通知人" style="width: 120px" clearable />
        </el-form-item>
        <el-form-item>
          <el-date-picker style="width: 230px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="通知时间" end-placeholder="通知时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport">导出</el-button>
        </el-form-item>
      </el-form>
    </template>

    <vxetablebase :id="'productnoticelog202408041642'" ref="table" :that='that' :isIndex='true' @sortchange='sortchange' :tableData='list' :tableCols='tableCols'
      :loading="listLoading" :isSelectColumn="false">
    </vxetablebase>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>
  </my-container>
</template>

<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import cesTable from "@/components/Table/table.vue";
import MyConfirmButton from "@/components/my-confirm-button";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
  getProductNoticeLogPageList,
  exportProductNoticeLogPageList,
} from "@/api/operatemanage/base/product";
const tableCols = [
  {
    istrue: true,
    prop: "styleCode",
    label: "系列编码",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "sendUserName",
    label: "通知人",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "receiveUserNames",
    label: "被通知人",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "noticeTime",
    label: "通知时间",
    sortable: "custom",
  },
  // {
  //   istrue: true,
  //   prop: "proCode",
  //   label: "宝贝ID",
  //   width: "120",
  //   sortable: "custom",
  // },

  {
    istrue: true,
    prop: "noticeStatus",
    label: "通知状态",
    sortable: "custom",
    formatter: (row) => (row.noticeStatus == 1 ? "是" : "否"),
  },
 
 
  {
    istrue: true,
    prop: "noticeTitle",
    label: "标题",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "proCode",
    label: "合计条数",
    sortable: "custom",
    formatter: (row) => row.proCodeCount,
  },
  {
    istrue: true,
    prop: "noticeContext",
    label: "内容",
    sortable: "custom",
  },
  { istrue: true, prop: 'noticeImgs', align: 'left', label: '图片', type: "images", sortable: 'custom',  },
];
export default {
  name: "productnoticelog",
  components: { MyContainer, MyConfirmButton, cesTable,vxetablebase },
  data() {
    return {
      that: this,
      filter: {
        receiveUserName:null,
        proCode:null,
        //.subtract(1, "day")
        timerange: [
          formatTime(dayjs(), "YYYY-MM-DD"),
          formatTime(dayjs(), "YYYY-MM-DD"),
        ],
      },
      tableCols: tableCols,
      pageLoading: false,
      list: [],
      summaryarry: {},
      total: 0,
      pager: { OrderBy: "NoticeTime", IsAsc: false },
      listLoading: false,
      sels: [],
      selectlist: [],
    };
  },
  async mounted() {
    //await this.getlist();
  },
  methods: {
    async loadData() {
      this.onSearch();
    },

    //获取查询条件
    getCondition() {
      let pager = this.$refs.pager.getPager();
      let page = this.pager;
      const params = {
        ...pager,
        ...page,
        ...this.filter,
      };
      return params;
    },
    //分页查询
    async getlist() {
      this.filter.startDate = null;
      this.filter.endDate = null;
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      } else {
        this.$message({ message: "请选择日期", type: "error" });
        return;
      }
      let params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      let res = await getProductNoticeLogPageList(params);
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry = res.data.summary;
      data.forEach((d) => {
        d._loading = false;
      });
      this.list = data;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1);
      await this.getlist();
    },
    //排序查询
    async sortchange(column) {
      if (!column.order) this.pager = {};
      else {
        let orderBy = column.prop;
        this.pager = {
          OrderBy: orderBy,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      }
      await this.onSearch();
    },
    async onExport() {
      this.filter.startDate = null;
      this.filter.endDate = null;
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      } else {
        this.$message({ message: "请选择日期", type: "error" });
        return;
      }
      let params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await exportProductNoticeLogPageList(params);
      this.listLoading = false;
      if (!res?.data) {
        this.$message({ type: "error", message: "没有数据可导出或导出失败!" });
        return;
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute(
        "download",
        "产品管理-通知日志_" + new Date().toLocaleString() + ".xlsx"
      );
      aLink.click();
    },
  },
};
</script>
