<template>
  <div class="quality-inspection-container">
    <!-- 对比内容区域 -->
    <div class="comparison-content">
      <!-- 左侧：历史数据 -->
      <div class="comparison-panel left-panel">
        <div class="panel-header">
          <i class="el-icon-document"></i>
          <span>历史检验数据</span>
        </div>
        <el-descriptions :column="2" border class="custom-descriptions" :labelStyle="label_style"
          :contentStyle="content_style">
          <el-descriptions-item label="折叠单边是否超40CM">
            <span class="value-text">{{ onEchoingMethod(lastData.isChao40CM) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="是否可折叠">
            <span class="value-text">{{ onEchoingMethod(lastData.isZheDie) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="是否检测">
            <span class="value-text">{{ onEchoingMethod(lastData.isJianCe) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="颜色">
            <span class="value-text">{{ lastData.colour }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="打包长(mm)">
            <span class="value-text">{{ lastData.measuredChang }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="打包宽(mm)">
            <span class="value-text">{{ lastData.measuredWidth }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="打包高(mm)">
            <span class="value-text">{{ lastData.measuredHeight }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="材质">
            <span class="value-text">{{ lastData.material }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="展开长(mm)">
            <span class="value-text">{{ lastData.zhankaiMeasuredChang }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="展开宽(mm)">
            <span class="value-text">{{ lastData.zhankaiMeasuredWidth }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="展开高(mm)">
            <span class="value-text">{{ lastData.zhankaiMeasuredHeight }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="测量重(g)">
            <span class="value-text">{{ lastData.measuredWeight }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="测量厚(mm)">
            <span class="value-text">{{ lastData.measuringThickness }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="箱规长(mm)">
            <span class="value-text">{{ lastData.cartonLength }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="箱规宽(mm)">
            <span class="value-text">{{ lastData.cartonGaugeWidth }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="箱规高(mm)">
            <span class="value-text">{{ lastData.boxGaugeHeight }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 中间：对比标识 -->
      <div class="comparison-divider">
        <div class="divider-line"></div>
        <div class="comparison-badge">
          <i class="el-icon-refresh"></i>
          <span>对比</span>
        </div>
        <div class="divider-line"></div>
      </div>

      <!-- 右侧：当前数据 -->
      <div class="comparison-panel right-panel">
        <div class="panel-header">
          <i class="el-icon-document-checked"></i>
          <span>当前检验数据</span>
          <span class="header-tip">
            （为<i class="el-icon-check" style="color: #67c23a; margin-right: 4px;"></i>则为一致）
          </span>
        </div>
        <el-descriptions :column="2" border class="custom-descriptions" :labelStyle="label_style"
          :contentStyle="content_style">
          <el-descriptions-item label="折叠单边是否超40CM">
            <div class="value-container">
              <span class="value-text">{{ onEchoingMethod(thisData.isChao40CM) }}</span>
              <i :class="getComparisonIcon(thisData.isChao40CM, lastData.isChao40CM)"
                :style="getComparisonStyle(thisData.isChao40CM, lastData.isChao40CM)"></i>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="是否可折叠">
            <div class="value-container">
              <span class="value-text">{{ onEchoingMethod(thisData.isZheDie) }}</span>
              <i :class="getComparisonIcon(thisData.isZheDie, lastData.isZheDie)"
                :style="getComparisonStyle(thisData.isZheDie, lastData.isZheDie)"></i>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="是否检测">
            <div class="value-container">
              <span class="value-text">{{ onEchoingMethod(thisData.isJianCe) }}</span>
              <i :class="getComparisonIcon(thisData.isJianCe, lastData.isJianCe)"
                :style="getComparisonStyle(thisData.isJianCe, lastData.isJianCe)"></i>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="颜色">
            <div class="value-container">
              <span class="value-text">{{ thisData.colour }}</span>
              <i :class="getComparisonIcon(thisData.colour, lastData.colour)"
                :style="getComparisonStyle(thisData.colour, lastData.colour)"></i>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="打包长(mm)">
            <div class="value-container">
              <span class="value-text">{{ thisData.measuredChang }}</span>
              <i :class="getComparisonIcon(thisData.measuredChang, lastData.measuredChang)"
                :style="getComparisonStyle(thisData.measuredChang, lastData.measuredChang)"></i>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="打包宽(mm)">
            <div class="value-container">
              <span class="value-text">{{ thisData.measuredWidth }}</span>
              <i :class="getComparisonIcon(thisData.measuredWidth, lastData.measuredWidth)"
                :style="getComparisonStyle(thisData.measuredWidth, lastData.measuredWidth)"></i>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="打包高(mm)">
            <div class="value-container">
              <span class="value-text">{{ thisData.measuredHeight }}</span>
              <i :class="getComparisonIcon(thisData.measuredHeight, lastData.measuredHeight)"
                :style="getComparisonStyle(thisData.measuredHeight, lastData.measuredHeight)"></i>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="材质">
            <div class="value-container">
              <span class="value-text">{{ thisData.material }}</span>
              <i :class="getComparisonIcon(thisData.material, lastData.material)"
                :style="getComparisonStyle(thisData.material, lastData.material)"></i>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="展开长(mm)">
            <div class="value-container">
              <span class="value-text">{{ thisData.zhankaiMeasuredChang }}</span>
              <i :class="getComparisonIcon(thisData.zhankaiMeasuredChang, lastData.zhankaiMeasuredChang)"
                :style="getComparisonStyle(thisData.zhankaiMeasuredChang, lastData.zhankaiMeasuredChang)"></i>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="展开宽(mm)">
            <div class="value-container">
              <span class="value-text">{{ thisData.zhankaiMeasuredWidth }}</span>
              <i :class="getComparisonIcon(thisData.zhankaiMeasuredWidth, lastData.zhankaiMeasuredWidth)"
                :style="getComparisonStyle(thisData.zhankaiMeasuredWidth, lastData.zhankaiMeasuredWidth)"></i>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="展开高(mm)">
            <div class="value-container">
              <span class="value-text">{{ thisData.zhankaiMeasuredHeight }}</span>
              <i :class="getComparisonIcon(thisData.zhankaiMeasuredHeight, lastData.zhankaiMeasuredHeight)"
                :style="getComparisonStyle(thisData.zhankaiMeasuredHeight, lastData.zhankaiMeasuredHeight)"></i>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="测量重(g)">
            <div class="value-container">
              <span class="value-text">{{ thisData.measuredWeight }}</span>
              <i :class="getComparisonIcon(thisData.measuredWeight, lastData.measuredWeight)"
                :style="getComparisonStyle(thisData.measuredWeight, lastData.measuredWeight)"></i>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="测量厚(mm)">
            <div class="value-container">
              <span class="value-text">{{ thisData.measuringThickness }}</span>
              <i :class="getComparisonIcon(thisData.measuringThickness, lastData.measuringThickness)"
                :style="getComparisonStyle(thisData.measuringThickness, lastData.measuringThickness)"></i>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="箱规长(mm)">
            <div class="value-container">
              <span class="value-text">{{ thisData.cartonLength }}</span>
              <i :class="getComparisonIcon(thisData.cartonLength, lastData.cartonLength)"
                :style="getComparisonStyle(thisData.cartonLength, lastData.cartonLength)"></i>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="箱规宽(mm)">
            <div class="value-container">
              <span class="value-text">{{ thisData.cartonGaugeWidth }}</span>
              <i :class="getComparisonIcon(thisData.cartonGaugeWidth, lastData.cartonGaugeWidth)"
                :style="getComparisonStyle(thisData.cartonGaugeWidth, lastData.cartonGaugeWidth)"></i>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="箱规高(mm)">
            <div class="value-container">
              <span class="value-text">{{ thisData.boxGaugeHeight }}</span>
              <i :class="getComparisonIcon(thisData.boxGaugeHeight, lastData.boxGaugeHeight)"
                :style="getComparisonStyle(thisData.boxGaugeHeight, lastData.boxGaugeHeight)"></i>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <div v-if="isNote" class="remark-section">
      <div class="remark-header">
        <i class="el-icon-edit-outline"></i>
        <span>备注</span>
      </div>
      <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 4 }" placeholder="请输入备注内容..." v-model="remark"
        maxlength="100" show-word-limit class="remark-input" resize="none" />
    </div>

    <div class="action-buttons">
      <div>
        <el-button type="success" @click="handleIsConsistent('yes')">一致</el-button>
        <el-button type="primary" @click="handleIsConsistent('no')">不一致</el-button>
        <el-button type="info" @click="handleClose">关闭</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { sampleRegistrationVerify } from "@/api/inventory/sampleGoods";

const isOptions = [{ label: "是", value: 1 }, { label: "否", value: 0 }]
export default {
  name: "qualityInspection",
  components: {},
  props: {
    rowData: {
      type: Object,
      default: () => { },
    },
    displayData: {
      type: Object,
      default: () => { },
    },
    isNote: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      label_style: {
        'width': '52%!important',
        'min-width': '120px'
      },
      content_style: {
        'text-align': 'center',
        'width': '48%!important',
        'min-width': '80px',
        'max-width': '120px',
        'word-break': 'break-all'
      },
      isOptions,
      that: this,
      thisData: {},
      lastData: {},
      loading: false,
      remark: '',
    };
  },
  async mounted() {
    this.lastData = this.displayData.lastData || {}
    this.thisData = this.displayData.thisData || {}
  },
  methods: {
    onEchoingMethod(e) {
      const index = this.isOptions.findIndex(item => item.value == e)
      return this.isOptions[index]?.label || ''
    },
    compareValues(currentValue, historyValue) {
      const current = currentValue === null || currentValue === undefined ? '' : String(currentValue);
      const history = historyValue === null || historyValue === undefined ? '' : String(historyValue);
      return current === history;
    },
    getComparisonIcon(currentValue, historyValue) {
      const isEqual = this.compareValues(currentValue, historyValue);
      return isEqual ? 'el-icon-check' : 'el-icon-close';
    },
    getComparisonStyle(currentValue, historyValue) {
      const isEqual = this.compareValues(currentValue, historyValue);
      return {
        color: isEqual ? '#67c23a' : '#f56c6c',
        fontSize: '14px'
      };
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null;
      this.ListInfo.endTime = e ? e[1] : null;
    },
    async handleIsConsistent(type) {
      this.$confirm(`是否执行${type == 'yes' ? '一致' : '不一致'}操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await sampleRegistrationVerify({
          ids: [this.rowData.id],//样品数据Id
          sampleOrBrand: this.isNote ? 2 : 1,// 样品登记质检传1  不一致质检传2   1样品间  2采购
          isEqual: type == 'yes' ? 1 : 0,// 是否一致  1是 0否
          remark: this.remark,
        })
        if (success) {
          this.$message.success('操作成功')
          this.$emit('successClose')
        }
      })
    },
    handleClose() {
      this.$emit("close")
    },
  },
};
</script>

<style scoped lang="scss">
.quality-inspection-container {
  height: 600px;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.comparison-content {
  display: flex;
  align-items: stretch;
  gap: 0;
  flex: 1;
}

.comparison-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  .panel-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 10px;
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 14px;

    i {
      margin-right: 8px;
      font-size: 18px;
    }

    .header-tip {
      margin-left: 8px;
      font-weight: 400;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.8);

      i {
        margin-right: 2px;
        font-size: 12px;
      }
    }
  }

  &.left-panel .panel-header {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  }

  &.right-panel .panel-header {
    background: linear-gradient(135deg, #00937e 0%, #00b894 100%);
  }
}

.custom-descriptions {
  border: none !important;
  width: 100% !important;
  table-layout: fixed !important;

  ::v-deep .el-descriptions__header {
    display: none;
  }

  ::v-deep .el-descriptions__body {
    background: white;
    width: 100% !important;
  }

  ::v-deep .el-descriptions__table {
    width: 100% !important;
    table-layout: fixed !important;
  }

  ::v-deep .el-descriptions-item__label {
    background: #f8f9fa !important;
    color: #606266;
    font-weight: 500;
    padding: 9px 12px;
    border-right: 1px solid #ebeef5;
    width: 60% !important;
    min-width: 120px !important;
  }

  ::v-deep .el-descriptions-item__content {
    padding: 9px 12px;
    background: white;
    width: 40% !important;
    min-width: 80px !important;
    max-width: 120px !important;
    text-align: center !important;
  }

  ::v-deep tr:nth-child(even) .el-descriptions-item__label {
    background: #fafafa !important;
  }

  ::v-deep tr:hover {
    background: #f5f7fa;
  }
}

.value-text {
  font-weight: 500;
  color: #303133;
  display: inline-block;
  padding: 4px 12px;
  font-size: 13px;
}

.comparison-divider {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  position: relative;
  z-index: 10;

  .divider-line {
    width: 2px;
    height: 80px;
    background: linear-gradient(to bottom, transparent, #dcdfe6, transparent);
    border-radius: 1px;
  }

  .comparison-badge {
    background: linear-gradient(135deg, #ff8a65 0%, #ffb74d 50%, #ffa726 100%);
    color: white;
    padding: 12px 16px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(255, 138, 101, 0.4);
    display: flex;
    align-items: center;
    gap: 6px;
    margin: 20px 0;
    position: relative;
    animation: pulse 2s infinite;

    i {
      font-size: 16px;
      animation: rotate 3s linear infinite;
    }

    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(45deg, #ff8a65, #ffb74d, #ffa726);
      border-radius: 50px;
      z-index: -1;
      opacity: 0.3;
    }
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 5px;
  margin-top: 10px;
}

.value-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .value-text {
    flex: 1;
  }

  i {
    margin-left: 8px !important;
    flex-shrink: 0;
  }
}

.remark-section {
  margin-top: 20px;
  padding: 16px;
  background: #fafbfc;
  border: 1px solid #ebeef5;
  border-radius: 6px;

  .remark-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    color: #606266;
    font-weight: 500;
    font-size: 14px;

    i {
      margin-right: 6px;
      color: #409eff;
      font-size: 16px;
    }
  }

  .remark-input {
    ::v-deep .el-textarea__inner {
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background: #fff;
      transition: border-color 0.2s;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
      }

      &::placeholder {
        color: #c0c4cc;
      }
    }

    ::v-deep .el-input__count {
      background: transparent;
      color: #909399;
      font-size: 12px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .quality-inspection-container {
    flex-direction: column;
    height: auto;
    gap: 20px;
  }

  .comparison-divider {
    width: 100%;
    flex-direction: row;
    height: 60px;

    .divider-line {
      width: 100px;
      height: 2px;
    }

    .comparison-badge {
      margin: 0 20px;
    }
  }
}
</style>
