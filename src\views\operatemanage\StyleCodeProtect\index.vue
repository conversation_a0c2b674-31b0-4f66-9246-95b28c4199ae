<template>
    <my-container v-loading="pageLoading">
        <el-tabs v-model="activeName" style="height: 95%">
            <el-tab-pane label="系列编码管控" name="first1" style="height: 100%">
                <stylecodeprotect ref="stylecodeprotect"></stylecodeprotect>
            </el-tab-pane>
            <el-tab-pane label="独立发展设置" name="first2" style="height: 100%" lazy>
                <stylecodeprotectalone ref="stylecodeprotectalone" @updateksjgroup="updateksjgroup">
                </stylecodeprotectalone>
            </el-tab-pane>
            <el-tab-pane label="保护系列编码设置" name="first3" style="height: 100%" lazy>
                <stylecodeprotectgroup ref="stylecodeprotectgroup"></stylecodeprotectgroup>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import stylecodeprotect from "./stylecodeprotect.vue";
import stylecodeprotectalone from "./stylecodeprotectalone.vue";
import stylecodeprotectgroup from "./stylecodeprotectgroup.vue";
export default {
    name: "burststyleprotectindex",
    components: {
        MyContainer, stylecodeprotect, stylecodeprotectalone, stylecodeprotectgroup,
    },
    data() {
        return {
            that: this,
            pageLoading: false,
            activeName: "first1",
        };
    },
    async mounted() {
    },
    methods: {
        updateksjgroup() {
            this.$refs.stylecodeprotect.getloadgroupselect_ksj()
        },
    },
};
</script>

<style lang="scss" scoped></style>