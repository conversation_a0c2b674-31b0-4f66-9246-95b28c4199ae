<template>
    <MyContainer>
        <table>
            <thead>
                <tr>
                    <td colspan="3">日期:{{ props.createdTime }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 批次:{{
                        props.batchNo
                    }}</td>
                    <td style="white-space: nowrap;">成品编码</td>
                    <td colspan="4">{{ props.prePackCode }}</td>
                </tr>
                <tr>
                    <td>组合编码</td>
                    <td>商品明细</td>
                    <td>名称</td>
                    <td>数量</td>
                    <td>条码</td>
                    <td style="white-space: nowrap;">子箱库存</td>
                    <td style="white-space: nowrap;">周转天数</td>
                    <td style="white-space: nowrap;">加工数量</td>
                </tr>
            </thead>
            <tr v-for="(item, index) in props.items" :key="item.id">
                <td class="name1" v-if="index == 0" :rowspan="props.items.length + 1" style="width: 160px;">
                    <div>{{ props.combineCode
                        }}<br />{{ props.name }}</div>
                </td>
                <td style="width: 90px;">{{ item.goodsCode }}</td>
                <td style="width: 180px;" class="name">
                    <div>{{ item.goodsName }}</div>
                </td>
                <td style="text-align: center;" class="ct">{{ item.quantity }}</td>
                <td style="text-align: center;width:120px" class="ct">
                    <img :id="'barcode' + item.id" style="width: 100%;height: 100%;" />
                </td>
                <td style="text-align: center;" class="ct">{{ item.invBoxCount }}
                </td>
                <td style="text-align: center;" class="ct">{{ item.inventoryDay }}</td>
                <td v-if="index == 0" :rowspan="props.items.length" style="text-align: center;" class="ct">{{
                        props.quantity }}
                </td>
            </tr>
            <tr>
                <td>打包人签名</td>
                <td colspan="3"></td>
                <td>加工日期</td>
                <td colspan="2"></td>
            </tr>
        </table>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import JsBarcode from 'jsbarcode'
export default {
    name: 'printTable',
    components: { MyContainer },
    data() {
        return {
            props: {}
        }
    },
    props: {
        model: {
            type: Object,
            default: () => { }
        }
    },
    mounted() {
        this.props = this.model
        this.$nextTick(() => {
            this.props.items.forEach((item, index) => {
                setTimeout(() => {
                    this.createBarCode(item.id, item.goodsCode)
                }, 10);
            })
        })
    },
    methods: {
        createBarCode(i, goodsCode) {
            JsBarcode(`#barcode${i}`, goodsCode, {
                displayValue: false // 是否在条形码下方显示文字
            })
        },
    }
}
</script>

<style scoped lang="scss">
table,
th,
tr,
td {
    border: 1px solid #000;
    border-collapse: collapse;
}

td {
    line-height: 20px;
    padding-left: 5px;
    padding-right: 5px;
    width: auto;
    height: 40px;
    table-layout: fixed !important;
}

table {
    width: 100%;
}
</style>