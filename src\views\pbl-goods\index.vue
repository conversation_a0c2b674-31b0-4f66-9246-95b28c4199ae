<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss" />
                <el-input v-model.trim="ListInfo.styleCode" placeholder="系列编码" maxlength="50" clearable
                    class="publicCss" />
                <el-select filterable v-model="ListInfo.hasOrderGroupId" collapse-tags clearable placeholder="销量小组"
                    class="publicCss">
                    <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select filterable v-model="ListInfo.groupId" collapse-tags clearable placeholder="挂靠小组"
                    class="publicCss">
                    <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select filterable v-model="ListInfo.assignGroupId" collapse-tags clearable placeholder="认领小组"
                    class="publicCss">
                    <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.isPbl" placeholder="公共品" class="publicCss" clearable>
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="applyReference(0)">延长保护</el-button>
                <el-button type="primary" @click="applyReference(1)">撤销保护</el-button>
                <el-button type="primary" @click="Anchored">挂靠商品</el-button>
                <el-button type="primary" @click="toLink" v-if="checkPermission('messageAudit')">消息审批</el-button>
                <el-button type="primary" :disabled="isExport" @click="exportProps">导出</el-button>
            </div>
        </template>
        <vxetablebase :id="'pbl-goods_index202408041832'" ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0" :height="'100%'"
            :showsummary="data.summary ? true : false" :summaryarry="data.summary" @sortchange="sortchange"
            @select="checkboxRangeEnd" @onTrendChart="trendChart">
            <template #hasOrderGroupName="{ row, index }">
                <div>
                    <el-popover style="overflow: hidden;scrollbar-width: thin;" placement="left" width="300"
                        :trigger="row.hasOrderGroupNames && row.hasOrderGroupNames.length > 1 ? 'hover' : 'manual'">
                        <div v-if="row.hasOrderGroupNames && row.hasOrderGroupNames.length > 1">{{
                            row.hasOrderGroupNames.join('，') }}</div>
                        <div slot="reference">{{ row.hasOrderGroupName }}</div>
                    </el-popover>
                </div>
            </template>
            <template slot="right">
                <vxe-column title="操作" width="200">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="openBenchmarkingLog(row.styleCode)">对标详情</el-button>
                            <el-button type="text"
                                @click="openViolationsLog(row.styleCode, row.assignGroupId)">违规详情</el-button>
                            <el-button type="text" @click="openOperateLog(row.styleCode)">操作详情</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false"
            direction="btt">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                    :picker-options="pickerOptions" style="margin: 10px" @change="
                        trendChart({
                            ...chatPropOption,
                            startDate: $event[0],
                            endDate: $event[1],
                        })
                        " />
                <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data" />
            </div>
            <div v-else v-loading="chatProp.chatLoading" />
        </el-drawer>

        <el-dialog :title="type == 0 ? '延长保护' : '撤销保护'" :visible.sync="applyProtectionVisible" width="40%" v-dialogDrag>
            <protectionPage :assignIdList="assignIdList" v-if="applyProtectionVisible" @close="close" @getList="getList"
                :type="type" />
        </el-dialog>

        <el-dialog title="对标详情" :visible.sync="BenchmarkingVisible" width="70%" v-dialogDrag>
            <BenchmarkingDetails v-if="BenchmarkingVisible" :styleCode="styleCode" />
        </el-dialog>

        <el-dialog title="违规详情" :visible.sync="ViolationsDetailsVisible" width="70%" v-dialogDrag>
            <ViolationsDetails v-if="ViolationsDetailsVisible" :styleCode="styleCode" :groupId="groupId" />
        </el-dialog>

        <el-dialog title="挂靠" :visible.sync="AnchoredVisible" width="40%" v-dialogDrag>
            <AnchoredPage v-if="AnchoredVisible" :styleCode="styleCode" @close="close" @getList="getList" />
        </el-dialog>
        
        <el-dialog title="操作详情" :visible.sync="operateVisible" width="70%" v-dialogDrag>
            <operateDetails v-if="operateVisible" :styleCode="styleCode" @close="close" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from "@/utils/tools";
import dateRange from "@/components/date-range/index.vue";
import dayjs from "dayjs";
import buschar from "@/components/Bus/buschar";
import protectionPage from "./components/protection_page.vue";
import request from '@/utils/request'
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import { download } from '@/utils/download'
import AnchoredPage from './components/Anchored_Page.vue'
import BenchmarkingDetails from './components/Benchmarking_details.vue'
import ViolationsDetails from './components/Violations_details.vue'
import operateDetails from './components/operate_details.vue'
export default {
    name: "scanCodePage",
    components: {
        MyContainer,
        vxetablebase,
        dateRange,
        buschar,
        protectionPage,
        BenchmarkingDetails,
        AnchoredPage,
        ViolationsDetails,
        operateDetails
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'date',
                isAsc: false,
                startDate: dayjs().subtract(7, 'day').format('YYYY-MM-DD'), //开始时间
                endDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'), //结束时间
                summarys: []
            },
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            chatPropOption: {},
            data: {},
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            applyProtectionVisible: false,//申请保护
            BenchmarkingVisible: false,//对标详情
            ViolationsDetailsVisible: false,//违规详情
            styleCode: '',//系列编码
            assignIdList: [],
            type: null,
            AnchoredVisible: false,
            styleCodeList: [],
            operateVisible: false,
            grouplist: []
        };
    },
    async mounted() {
        this.init()
        await this.getCol();
        await this.getList();
    },
    methods: {
        async init() {
            const { data } = await getDirectorGroupList();
            this.grouplist = data?.map(item => { return { value: item.key, label: item.value }; });
        },
        toLink() {
            this.$router.push({ path: '/pbl-goods/approvalList/index' })
        },
        openOperateLog(styleCode) {
            this.styleCode = styleCode
            this.operateVisible = true
        },
        Anchored() {
            if (this.styleCodeList.length > 1) return this.$message.error("只能选择一条数据");
            this.styleCode = this.styleCodeList[0]
            this.AnchoredVisible = true
        },
        close() {
            this.assignIdList = []
            this.styleCodeList = []
            this.styleCode = ''
            this.applyProtectionVisible = false
            this.AnchoredVisible = false
            this.ViolationsDetailsVisible = false
        },
        openViolationsLog(styleCode, groupId) {
            this.groupId = groupId
            this.styleCode = styleCode
            this.ViolationsDetailsVisible = true
        },
        openBenchmarkingLog(styleCode) {
            this.styleCode = styleCode
            this.BenchmarkingVisible = true
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post('/api/bookkeeper/pblGoodPdd/ExportData', this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        applyReference(type) {//申请保护
            if (this.assignIdList.length == 0) return this.$message.error("请选择要操作的数据");
            this.type = type
            this.applyProtectionVisible = true
        },
        async getCol() {
            const { data, success } = await request.post('/api/bookkeeper/pblGoodPdd/GetColumns')
            if (success) {
                // 在data顶部添加一列
                data.unshift({
                    label: "",
                    type: "checkbox",
                });
                this.tableCols = data;
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async trendChart(option) {
            var endDate = null;
            var startDate = null;

            if (option.startDate && option.endDate) {
                startDate = option.startDate;
                endDate = option.endDate;
            } else {
                endDate = option.date;
                startDate = new Date(option.date);
                startDate.setDate(option.date.getDate() - 30);

                startDate = dayjs(startDate).format("YYYY-MM-DD");
                endDate = dayjs(endDate).format("YYYY-MM-DD");
            }
            option.filter.filters = option.filter.filters.filter((item) => item.field !== option.dateField);
            option.filter.filters.push({
                field: option.dateField,
                operator: "GreaterThanOrEqual",
                value: startDate,
            });
            option.filter.filters.push({
                field: option.dateField,
                operator: "LessThanOrEqual",
                value: endDate,
            });

            option.startDate = startDate;
            option.endDate = endDate;

            this.chatProp.chatTime = [startDate, endDate];

            this.chatProp.chatLoading = true;

            const { data, success } = await request.post('/api/bookkeeper/pblGoodPdd/GetTrendChart', option);
            if (success) {
                this.chatProp.data = data;
            }

            this.chatProp.chatLoading = false;
            this.chatProp.chatDialog = true;

            this.chatPropOption = option;
        },
        checkboxRangeEnd(row) {
            this.styleCodeList = row.map((item) => item.styleCode);
            this.assignIdList = row.map((item) => item.assignId);
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post('/api/bookkeeper/pblGoodPdd/PageGetData', this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList();
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList();
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop;
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false;
                this.getList();
            }
        },
    },
};
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
