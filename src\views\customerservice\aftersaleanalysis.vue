<template>
  <!-- <container> -->
  <el-container style="height: 100%; border: 1px solid #eee">

    <el-main>

      <!-- <el-aside width="800px" style="background-color: rgb(238, 241, 246)"> -->
      <container>

        <ces-table
          ref="table"
          :that="that"
          :is-index="true"
          :hasexpand="true"
          :table-data="list"
          :table-cols="tableCols"
          :tableHandles="tableHandles"
          :showsummary="true"
          @sortchange='sortchange'
          :summaryarry="summaryarry"
          @summaryClick='onsummaryClick'
          :loading="listLoading"
          style="width: 100%; height: 91%; margin: 0"
        >
      <template slot='extentbtn'>
        <el-button-group>
      <el-button style="padding: 0;margin: 0;">
              <el-input placeholder="ERP售后单号" v-model="filter.afterNo" style="width: 110px"></el-input>
          </el-button>
               <el-button style="padding: 0;margin: 0;">
              <el-input placeholder="内部单号" v-model="filter.orderNoInner" style="width: 110px"></el-input>
          </el-button>
           <el-button style="padding: 0;margin: 0;">
              <el-input placeholder="商品名称" v-model.trim="filter.goodsName" style="width: 110px"></el-input>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
              <el-input placeholder="商品编码" v-model.trim="filter.goodsCode" style="width: 110px"></el-input>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
                    <el-select filterable clearable v-model="filter.resonLevel1" placeholder="大原因" style="width: 100px" @change="resonLevel1chang" >
                      <el-option v-for="item in cashRedResonlist" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                    <el-select v-if="(cashRedResonItems.length>0)" filterable clearable v-model="filter.resonLevel2" placeholder="小原因" style="width: 100px">
                      <el-option v-for="item in cashRedResonItems" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                  </el-button>
          <el-button style="padding: 0;margin: 0;">
                    <el-select   v-model="filter.saleAfter_Type" clearable placeholder="售后类型" style="width: 100px" >
                     <el-option label="投诉" value="4">投诉</el-option>
                      <el-option  label="补发" value="5" >补发</el-option>
                      <el-option label="换货" value="6">换货</el-option>
                      <el-option label="其他" value="7">其他</el-option>
                      <el-option label="仅退款" value="3">仅退款</el-option>
                      <el-option label="普通退货"  value='1'>普通退货</el-option>
                      <el-option label="拒收退货" value="2">拒收退货</el-option>
                    </el-select>
                  </el-button>
          <el-button style="padding: 0;margin: 0;">
                    <el-select   v-model="filter.shop_Status" clearable placeholder="店铺状态" style="width: 100px" >
                      <el-option label="退款成功"  value='6'>退款成功</el-option>
                      <el-option label="退款关闭" value="5">退款关闭</el-option>
                      <el-option label="卖家拒绝退款" value="4">卖家拒绝退款</el-option>
                      <el-option label="买家已退货，等待卖家确认" value="3">买家已退货，等待卖家确认</el-option>
                      <el-option label="卖家同意退款，等待买家退货" value="2">卖家同意退款，等待买家退货</el-option>
                      <el-option label="买家申请退款，等待卖家同意" value="1">买家申请退款，等待卖家同意</el-option>
                    </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;">

          <el-date-picker style="width: 260px"
                v-model="filter.timerange"
                type="datetimerange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
                :default-value="defaultDate1"
           ></el-date-picker>


          </el-button>
            <el-button type="primary" @click="onSearch" >查询</el-button>
          </el-button-group>
       </template>
          </ces-table>
        <template #footer>
          <my-pagination
            ref="pager"
            :total="total"
            :checked-count="sels.length"
            @get-page="getlist"
          />
        </template>
      </container>
    </el-main>
    <el-footer height="400px">
      <aftersaleecharts ref="aftersaleecharts"></aftersaleecharts>
    </el-footer>

      <!-- 系列编码趋势图 -->
    <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%">
      <span>
        <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>

  </el-container>

</template>
<script>
import container from '@/components/my-container';
import cesTable from "@/components/Table/table.vue";
import MyConfirmButton from '@/components/my-confirm-button';
import aftersaleecharts from "@/views/customerservice/aftersaleecharts.vue";
import {GetSaleAfterList ,ExportDataAsync,querySaleAfterAnalysisAsync} from '@/api/customerservice/saleafteranalysis';
import {cashRedResonlist} from "@/utils/tools";
import buschar from '@/components/Bus/buschar'

const tableCols = [
  {istrue: true,prop: 'afterNo',label: 'ERP售后单号',width: '110',sortable: 'custom' },
  {istrue: true,prop: 'orderNoInner',label: '内部单号',width: '80',sortable: 'custom',type:'orderLogInfo',orderType:'orderNoInner'},
  {istrue: true,prop: 'orderNo',label: '线上单号',width: '120',sortable: 'custom'},
  {istrue: true,prop: 'goodsCode',label: '商品编码',width: '100',sortable: 'custom'},
  {istrue: true, prop: 'goodsName',label: '商品名称',width: '200',sortable: 'custom'},
  {istrue: true,prop: 'as_Date', label: '申请时间', width: '120',sortable: 'custom' },
  {istrue: true,prop: 'confirm_Date',label: '最后确认时间', width: '120',sortable: 'custom'},
  {istrue: true,prop: 'receive_Date',label: '入仓时间',width: '120',sortable: 'custom' },
  //{istrue: true,prop: 'outher_As_Id',label: '退货单号',width: '150',sortable: 'custom'},
  {istrue: true,prop: 'saleAfter_Type',label: '售后类型',width: '80',},
  {istrue: true,prop: 'status',label: '状态',width: '60'},
  {istrue: true,prop: 'shop_Status',label: '店铺状态',width: '80',},
  {istrue: true,prop: 'question_Type',label: '问题类型',width: '80',sortable: 'custom'},
  {istrue: true,prop: 'type',label: '类型',width: '60'},
  {istrue: true,prop: 'order_Status',label: '原订单状态',width: '100',sortable: 'custom',},
  {istrue: true,summaryEvent: true,prop: 'qty',label: '退货数量',width: '80',sortable: 'custom'},
  {istrue: true,summaryEvent: true,prop: 'r_Qty',label: '实收数量',width: '80',sortable: 'custom'},
  {istrue: true,summaryEvent: true,prop: 'defective_Qty',label: '次品数量',width: '80',sortable: 'custom'},
  {istrue: true,summaryEvent: true,prop: 'price',label: '单价',width: '70',sortable: 'custom'},
  {istrue: true,summaryEvent: true,prop: 'amount',label: '金额',width: '70',sortable: 'custom'},
  {istrue: true,summaryEvent: true,prop: 'refundAmont',label: '退款金额',width: '80',sortable: 'custom'},
  {istrue: true,summaryEvent: true,prop: 'paymentAmont',label: '补偿金额',width: '80',sortable: 'custom'},
  {istrue: true,prop: 'shopCode',label: '店铺编号',width: '80',sortable: 'custom'},
  {istrue: true,prop: 'i_Id',label: '款式编码',width: '95',sortable: 'custom'},
  {istrue: true,prop: 'warehouse',label: '仓库',width: '150'},
  {istrue: true,prop: 'remark',label: '备注',width: '150',sortable: 'custom'},
  {istrue: true,prop: 'node',label: '售后线下备注',width: '120',sortable: 'custom'},
  {istrue: true,prop: 'modified',label: '编辑时间',width: '120',sortable: 'custom'},
]
const tableHandles = [
  { label: '刷新', handle: (that) => that.onSearch() },
  { label:"导出", handle:(that)=>that.onExport()},
]

export default {
  name: 'SaleAfterRefund',
   components: { cesTable, container, MyConfirmButton, aftersaleecharts , buschar },
  // props: {
  //   filter: {}
  // },
  data() {
    return {
      date: '',
       cashRedResonlist:cashRedResonlist,
       cashRedResonItems:[],
      filter:{
        afterNo:'',
        orderNoInner:'',
        goodsName:'',
        //confirm_Date:'',
        // startTime:null,
        // endTime:null,
        goodsCode:'',
        resonLevel1:'',
        resonLevel2:'',
        saleAfter_Type:'5',
        timerange:[],
        startTime:null,
        endTime:null,
      },
     // this.confirm_Date[startDate,endDate],
      shareFeeType: 5,
      params: {},
      that: this,
      list: [],
      picList:[],
      saleList: [],
      tableCols: tableCols,
      tableHandles: tableHandles,
      pager: { OrderBy: 'CreatedTime', IsAsc: false },
      summaryarry: {},
      total: 0,
      sels: [],
      selids: [],
      listLoading: false,
      pageLoading: false,
      buscharDialog: { visible: false, title: "", data: [] },
      pickerOptions: {
          disabledDate(date) {
          // 设置禁用日期
          const start = new Date('1970/1/1');
          const end = new Date('9999/12/31');
          return date < start || date > end;
          }
        },
        defaultDate1: new Date('1970/1/1')

    };
  },
  methods: {
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else{
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    },
    async onSearch() {
      if (!this.filter.timerange||this.filter.timerange.length<2){
        this.$message({message: "请先选择日期！",type: "warning",});
        return;
      }
        this.$refs.pager.setPage(1)
        await this.getlist()
        await this.loaddaycashredanalysis()
    },
    async resonLevel1chang(val){
      if(val){
         this.cashRedResonItems=this.cashRedResonlist.find(f=>f.value==val).items
        }
      else this.cashRedResonItems=[];
    },
    //售后明细列表
    async  getlist() {
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      const params = { ...pager,...page,...this.filter}
       if(params==false){
        return;
       }


       this.listLoading=true
       const res= await GetSaleAfterList(params)
       this.listLoading=false
       console.log()
       if(!res?.success){
        return
       }
       this.total=res.data.total;
       const data=res.data.list;
       this.list=data;
       this.summaryarry=res.data.summary;
    },
    async loaddaycashredanalysis() {
      let _th=this;
      let params={...this.pager, ... this.filter};
      if (this.filter.timerange) {
        params.startTime = this.filter.timerange[0];
        params.endTime = this.filter.timerange[1];
      }
       await this.$nextTick(async () => {
         await _th.$refs.aftersaleecharts.getAnalysis(params);
      });
    },
    //时间查询默认一个月
    async  defaultDate(){
       const end = new Date();
       const start = new Date();
       start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
       this.filter.timerange = [start,end]
       console.log('時間',this.filter.timerange)
    },

    //导出
    async onExport(){
        var params=this.getCondition();
        if(params===false){
            return;
        }
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await ExportDataAsync(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");

        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','售后分析_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },
    //获取查询条件
    getCondition(){
      const params = {
        ... this.filter
      }
      return params;
    },
     //显示汇总趋势图
    async onsummaryClick(property){
      this.filter.startTime = null;
        this.filter.endTime = null;
        if (this.filter.timerange) {
          this.filter.startTime = this.filter.timerange[0];
          this.filter.endTime = this.filter.timerange[1];
        }
        let pager = this.$refs.pager.getPager();
        const params = { ...pager, ...this.pager, ...this.filter };
        params.column = property;
        let that = this;
        await querySaleAfterAnalysisAsync(params).then(res => {
          that.buscharDialog.visible = true;
          that.buscharDialog.data = res.data;
          that.buscharDialog.title = res.data.legend[0];
        });
    },
  },
  async mounted() {
    await this.defaultDate();
    await this.onSearch();

  },
}
</script>
