<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form
        class="ad-form-query"
        :inline="true"
        :model="filter"
        @submit.native.prevent
      >
                    <el-input v-model.trim="deletefilter.batchNumber" autocomplete="off" style="width:230px;"></el-input>
                     <el-button type="primary" @click="deleteByBatchNumber">删除</el-button>
               
             
                    <el-date-picker v-model="filter.timeRange" type="daterange" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                        range-separator="至" start-placeholder="导入开始日期" end-placeholder="导入结束日期" style="width:230px;margin-left: 20px;"
                    >
                    </el-date-picker>
                             
                <!-- <el-button  style="padding:0;margin:0;">
                    <el-input v-model.trim="filter.proCode" placeholder="商品ID" :clearable="true" ></el-input>
                </el-button>
                <el-button  style="padding:0;margin:0;">
                    <el-input v-model.trim="myfilter.orderNoInner" placeholder="内部订单号" :clearable="true" ></el-input>
                </el-button>
                <el-button  style="padding:0;margin:0;">
                    <el-input v-model.trim="myfilter.orderNo" placeholder="原始订单号" :clearable="true" ></el-input>
                </el-button>
                <el-button  style="padding:0;margin:0;">
                    <el-input v-model.trim="myfilter.expressNo" placeholder="快递单号" :clearable="true" ></el-input>
                </el-button> -->
                <el-button type="primary" @click="onSearch">查询</el-button>
           </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="true"  
          :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='tableData' 
          :tableCols='tableCols'   style="width:100%;height:92%;margin: 0">
        </ces-table>
        <!--分页-->
        <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
        </template>
    </my-container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import MyContainer from '@/components/my-container';
import cesTable from '@/components/Table/table.vue';
import {formatLinkProCode} from '@/utils/tools'
import {
    getOrderDetail, exportOrderDetail
} from '@/api/order/ordergoods';
import {queryPurchaseOrderDetail,importPurchaseReturnGoods,getpurchaseReturnGoodsList,deletePurchaseReturnGoods} from '@/api/inventory/purchase'
const tableCols =[
    {istrue:true,prop:'batchNumber',label:'批次号',sortable:'custom', width:'200'},
    {istrue:true,prop:'supplier',label:'供应商名称',sortable:'custom', width:'220'},
    {istrue:true,prop:'outKuAmount',label:'出库金额',sortable:'custom', width:'120'},
    {istrue:true,prop:'outKuDate',label:'出库时间', width:'110',sortable:'custom',width:'150'},
    {istrue:true,prop:'createdTime',label:'导入时间', width:'140',sortable:'custom',width:'220'}
   
];

// const tableHandles=[
//         {label:"导出", handle:(that)=>that.onExport()},
//       ];

// const startDate = formatTime(dayjs().subtract(1,'day'), "YYYY-MM-DD");
// const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
    name:"Users",
    components:{MyContainer,cesTable},
    
    data(){
        return {

    deletefilter:{
        batchNumber:''
      },
            that:this, 
            // myfilter:{
            //     //proCode:null,
            //     // timeRange:[startDate,endDate],
            //     // startDate:null,
            //     // endDate:null,
            //     orderNo:null,
            //     orderNoInner:null,
            //     expressNo:null
            // },                      
            tableCols:tableCols,
            // tableHandles:tableHandles,
            tableData:[],
            total: 0,
            pager:{OrderBy:"OutKuAmount",IsAsc:false},
            // listLoading: false,
            pageLoading: false,
            summaryarry:{},    
            sels:[],
              filter:{
            // proCode:null,
            startDate:null,
            endDate:null,
            //orderNo:null,
            //orderNoInner:null
        }, 
        };
    },
   
    async mounted() {
   
    await this.getList();
   
  },
    methods:{
        //按批次号删除
   async deleteByBatchNumber(){
          if (!this.deletefilter.batchNumber) {
       this.$message({type: 'warning',message: '请输入批次号!'});
       return;
      } 
      this.$confirm('确认删除, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
        }).then(async () => {
            const res = await deletePurchaseReturnGoods(this.deletefilter)
            if (!res?.success) {return }
            this.$message({type: 'success',message: '删除成功!'});
            this.onSearch()
        }).catch(() => {
          this.$message({type: 'info',message: '已取消删除'});
        });
              
   },
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            await this.onSearch();
            },
            async onSearch(){
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async getList(){
            this.filter.startDate =null;
            this.filter.endDate =null;
            if (this.filter.timeRange && this.filter.timeRange.length>0) {
                this.filter.startDate = this.filter.timeRange[0];
                this.filter.endDate = this.filter.timeRange[1];
            }
         
            
            var pager = this.$refs.pager.getPager();
            const params = {...pager,...this.pager,...this.filter};
            const res = await getpurchaseReturnGoodsList(params)
            .then(res=>{
                this.total = res.data?.total;
                this.tableData = res.data?.list;
                that.summaryarry=res.data?.summary;     
            });
           
            // this.listLoading=false;
        },
        // async onExport(){
        //     this.filter.startDate =null;
        //     this.filter.endDate =null;
        //     if (this.filter.timeRange && this.filter.timeRange.length>0) {
        //         this.filter.startDate = this.filter.timeRange[0];
        //         this.filter.endDate = this.filter.timeRange[1];
        //     }
        //     else{
        //         this.$message({type: 'warning',message: '请选择日期范围'});
        //         return;
        //     }
        //     if(!this.filter.proCode){
        //         this.$message({type: 'warning',message: '请输入商品ID'});
        //         return;
        //     }
        //     var pager = this.$refs.pager.getPager();
        //     const params = {...pager,...this.pager,...this.filter,...this.myfilter};
        //     var res= await exportOrderDetail(params);
        //     if(!res?.data) {
        //         this.$message({message:"没有数据",type:"warning"});
        //         return
        //     }
        //     const aLink = document.createElement("a");
        //     let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        //     aLink.href = URL.createObjectURL(blob)
        //     aLink.setAttribute('download','订单明细数据' +  new Date().toLocaleString() + '_.xlsx' )
        //     aLink.click()
        // },
    }
})
</script>
<style scoped>
    ::v-deep .el-table__fixed-footer-wrapper tbody td{
        color:blue;
    }
</style>

