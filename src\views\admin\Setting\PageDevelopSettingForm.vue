<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form ref="form" :model="form" label-width="80px">
                <el-form-item label="应用类型">
                    <el-select prop="appType"  v-model="form.appType" >
                        <el-option  label="ERP"  value="ERP"></el-option>
                        <el-option  label="钉钉小程序"  value="钉钉小程序"></el-option>
                        <el-option  label="安卓APP"  value="安卓APP"></el-option>
                        <el-option  label="苹果APP"  value="苹果APP"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="模块">
                    <el-input v-model="form.moduleName"></el-input>
                </el-form-item>
                <el-form-item label="页面">
                    <el-input v-model="form.pageName"></el-input>
                </el-form-item>
                <el-form-item label="页签">
                    <el-input v-model="form.pageTab"></el-input>
                </el-form-item>

                <el-form-item label="产品">
                    <el-input v-model="form.productManager"></el-input>
                </el-form-item>
                <el-form-item label="开发">
                    <el-input v-model="form.developer"></el-input>
                </el-form-item>
                <el-form-item label="测试">
                    <el-input v-model="form.tester"></el-input>
                </el-form-item>
<!--    -->
                <el-form-item label="需求一级部门">
                    <el-input v-model="form.XqDept1"></el-input>
                </el-form-item>
                <el-form-item label="需求一级部门">
                    <el-input v-model="form.XqDept2"></el-input>
                </el-form-item>
                <el-form-item label="需求提出人">
                    <el-input v-model="form.XqUserName"></el-input>
                </el-form-item>
                </el-form>         
           
        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">
                    <el-button type="primary" @click="addSave">保存</el-button>
                    <el-button @click="onClose">关闭</el-button>
                </el-col>
            </el-row>
        </template>
    </my-container>
</template>
<script>


import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";

import { AddOperationLogAsync } from '@/api/admin/opration-log' 



export default {
    name: "PageDevelopSettingForm",
    components: {  MyContainer, MyConfirmButton },
    data() {
        return {
            that: this,
           
            form:{appType:"",moduleName:"",pageName:"",pageTab:"",productManager:"",developer:"",tester:"", XqDept1:"",XqDept2:"",XqUserName:""},          
        
            mode: 3,         
       
            //summaryarry: {},
            pageLoading: false,
            curRow: null,
            formEditMode: true,//是否编辑模式              
        };
    },
    async mounted() {
       
    },
    computed: {

    },
    methods: {  
        async addSave(){
            this.pageLoading = true;
            let dto={...this.form};
            let rsp= await AddOperationLogAsync(dto);
          
            this.pageLoading = false;
            if(rsp && rsp.success){
                this.$message.success('操作成功！');     
                this.onClose();           
            }

        },
       
        async loadData({row}) {
            if(row )
                this.form={...row};          
        },
        onClose() {
            this.$emit('close');
        },
        async onSave(isClose) {
            if (await this.save()) {
                this.$emit('afterSave');
                if (isClose)
                    this.$emit('close');
            }
        }        
    },
};
</script>
