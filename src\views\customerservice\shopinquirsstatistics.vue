<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
      @sortchange='sortchange' :tableData='groupinquirsstatisticslist' @select='selectchange' :isSelection='false'
      :tableCols='tableCols' :loading="listLoading">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <template slot='extentbtn'>
        <el-button-group>

          <el-button style="padding: 0;margin: 0;">
            <datepicker v-model="Filter.Sdate"></datepicker>
          </el-button>
          <!-- <el-button type="text" size="medium" disabled style="color: red;">周转对比:</el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input-number v-model="filter1.startdiff"></el-input-number>至<el-input-number v-model="filter1.enddiff"></el-input-number>
            </el-button> -->
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button>
        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length"
        @get-page="getShopInquirsStatisticsList" />
    </template>
    <el-dialog title="添加客服组效率统计信息" :visible.sync="add客服组效率统计dialogVisibleSyj" width="30%">
      <span>

      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="客服组效率统计" :visible.sync="dialogVisibleSyj" width="30%">
      <span>
        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx">
          <!-- :http-request="uploadFile2"
                  :on-success="uploadSuccess2" -->
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <my-confirm-button style="margin-left: 10px;" size="small" type="success"
            @click="onSubmitupload2">上传</my-confirm-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="趋势图" :visible.sync="dialogMapVisible.visible" width="80%">
      <div>
        <span>
          <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
  importGroupInquirsStatisticsAsync, getShopInquirsStatisticsListMap, deleteGroupInquirsStatisticsBatch,
  getShopInquirsStatisticsList, exportShopInquirsStatisticsList
} from '@/api/customerservice/groupinquirsstatistics'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar'
const tableCols = [
  { istrue: true, prop: 'shopname', label: '店名', width: '200', sortable: 'custom' },
  { istrue: true, prop: 'inquirs', label: '咨询人数', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'receivecount', label: '接待人数', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'ipscount', label: '询单人数', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'nextdaysuccesspaycount', label: '询单>次日付款人数', width: '160', sortable: 'custom' },
  { istrue: true, prop: 'nextdaysuccesspayrate', label: '询单>次日付款成功率', width: '160', sortable: 'custom', formatter: (row) => { return row.nextdaysuccesspayrate ? (row.nextdaysuccesspayrate * 100).toFixed(2) + "%" : 0 } },
  { istrue: true, prop: 'successpaycount', label: '询单>最终付款人数', width: '160', sortable: 'custom' },
  { istrue: true, prop: 'successpayrate', label: '询单>最终付款成功率', width: '160', sortable: 'custom', formatter: (row) => { return row.successpayrate ? (row.successpayrate * 100).toFixed(2) + "%" : 0 } },
  { istrue: true, prop: 'responseTime', label: '平均响应(秒)', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'salesvol', label: '销售额', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'dutycount', label: '出勤人次', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'ipspergroup', label: '人均接待量', width: '100', sortable: 'custom' },
  { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar },
  data() {
    return {
      dialogMapVisible: { visible: false, title: "", data: [] },
      that: this,
      Filter: {
      },
      shopList: [],
      userList: [],
      groupList: [],
      groupinquirsstatisticslist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: { count_sum: 10 },
      pager: { OrderBy: "ipscount", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
      add客服组效率统计dialogVisibleSyj: false,
    };
  },
  async mounted() {
  },
  methods: {
    async showchart(row) {

      if (this.Filter.timerange) {
        this.Filter.startSdate = this.Filter.Sdate[0];
        this.Filter.endSdate = this.Filter.Sdate[1]
      }
      var params = { shopname: row.shopname, StartSdate: this.Filter.startSdate, EndSdate: this.Filter.endSdate }
      let that = this;

      const res = await getShopInquirsStatisticsListMap(params).then(res => {
        that.dialogMapVisible.visible = true;
        that.dialogMapVisible.data = res.data
        that.dialogMapVisible.title = res.data.legend[0]
      })
      this.dialogMapVisible.visible = true

    },

    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onImportSyj() {
      this.dialogVisibleSyj = true
    },

    async onSubmitupload2() {
      this.$refs.upload2.submit()
    },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getShopInquirsStatisticsList();
    },
    getParam() {
      if (this.Filter.UseDate) {
        this.Filter.startAccountDate = this.Filter.UseDate[0];
        this.Filter.endAccountDate = this.Filter.UseDate[1];
      }
      if (this.Filter.Sdate) {
        this.Filter.startSdate = this.Filter.Sdate[0];
        this.Filter.endSdate = this.Filter.Sdate[1];
      }
      else {
        this.Filter.startSdate = null;
        this.Filter.endSdate = null;
      }
      const para = { ...this.Filter };
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      return params;
    },
    async getShopInquirsStatisticsList() {
      let params = this.getParam();
      console.log(params)

      this.listLoading = true;
      const res = await getShopInquirsStatisticsList(params);
      console.log(res)
      this.listLoading = false;
      console.log(res.data.list)
      //console.log(res.data.summary)

      this.total = res.data.total
      this.groupinquirsstatisticslist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    async onExport() {
      let params = this.getParam();
      this.listLoading = true
      const res = await exportShopInquirsStatisticsList(params)
      this.listLoading = false
      if (!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '淘系店效率统计(售前组)_' + new Date().toLocaleString() + '.xlsx');
      aLink.click()
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
