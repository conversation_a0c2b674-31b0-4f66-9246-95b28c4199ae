<template>
    <div class="PlayVideo">
        <!-- 视频播放 -->
        <video class="video" controls="" ref="video" id="video"
        controlslist="nodownload noremoteplayback" 
        :autoplay="autoplay" oncontextmenu="return false;"
        v-on:error.prevent="error($event)" style="width:100%;height:100%">
        </video>
        <!-- 信息提示 -->
        <div class="msg" v-if="isError">
            <div style="color: #fff">{{errMsg}}</div>
        </div>
    </div>

</template>

<script>
    export default {
        props: { videoUrl: '', autoplay: false },
        data() {
            return {
                isError:false
            }
        },
        mounted() {
            this.playVideo(this.videoUrl);
        },
        methods: {

            playVideo(videoUrl) {
                var that = this;
                // this.videoUrl = videoUrl,
                this.$refs.video.src = this.videoUrl;
                // this.$refs.video.disablePictureInPicture = true;
            },
            close() {
                this.show = false;
                this.$emit("close");
            },
            error(e) {
                console.log(e);
                if (this.videoUrl == '') {
                    this.isError = true
                    this.errMsg = "该文章暂无视频！"
                } else {
                    this.isError = true
                    this.errMsg = "视频链接失效！无法播放！"
                }
            }
        }
    }
</script>
<style lang='scss' scoped>
    .PlayVideo {
        width: 45.3125vw;
        height: 50.1vh;
        position: relative;
        // z-index: 99999;
        // background: rgba(0, 0, 0, 0.5);
        .close {
            position: absolute;
            top: -32px;
            right: -32px;
            z-index: 9999;
        }
        .video {
            width: 45.3125vw;
            height: 50.1vh;
            background: #000;
            // position: absolute;
            // top: 50%;
            // left: 50%;
            // transform: translate(-50%,-50%);
            // z-index: 100000;
        }
        .msg {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }
    }
</style>
