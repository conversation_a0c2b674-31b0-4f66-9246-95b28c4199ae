<template>
    <!-- 新品共用表 -->
    <div style="height: 100%;">
        <vxe-toolbar ref="xToolbar" custom class="vxetoolbar20221212" v-if="showToolbar">
            <template #buttons>
                <slot name="tbHeader" />
            </template>
        </vxe-toolbar>
        <vxe-table ref="xTable" border="default" :show-footer="true" style="width: 100%;"
            class="vxetable202212161323 mytable-scrollbar20221212" resizable stripe :id="id" show-overflow
            show-footer-overflow keep-source size="mini" :height="height" :loading="loading" :data="tableData"
            :scroll-y="{ gt: 100, enabled: true }" :scroll-x="{ gt: 100, enabled: true }" :footer-method="footerMethod" @cell-dblclick="rowChange"
            @footer-cell-click="footercellclick" @checkbox-change="selectChangeEvent" @checkbox-all="selectChangeEvent"
            :sort-config="{ sortMethod: customSortMethod }" :row-class-name="rowStyleFun"
            :row-config="{ isCurrent: true, isHover: true }">
            <vxe-column type="checkbox" width="35" fixed="left"></vxe-column>
            <vxe-column field="directImgTaskId" title="编号" width='50' fixed='left'>
            </vxe-column>
            <vxe-column field="productShortName" title="产品简称" width='128' fixed='left'>
                <template #default="{ row }">
                    <a type="text" show-overflow="ellipsis" style="color:#409EFF" @click="openComputOutInfo(row)"> {{
                        row.productShortName }} </a>
                </template>
            </vxe-column>
            <!-- 标记 -->
            <vxe-column field="isTopOldNum" title="" width='28' fixed='left'>
                <template #default="{ row }">
                    <template v-if="row.isTopOldNum == '0'">
                        <span></span>
                    </template>
                    <template v-else>
                        <i class="vxe-icon-dot" style="color: #f56c6c;"></i>
                    </template>
                </template>
            </vxe-column>
            <vxe-column field="taskUrgencyName" title="紧急程度" width='72' fixed='left'>
                <template #default="{ row }">
                    <template v-if="row.taskUrgency == 1">
                        <vxe-button status="danger" size="mini"
                            @click="shootUrgencyCilck({ taskUrgencyName: row.taskUrgencyName, id: row.directImgTaskId })">{{
                                row.taskUrgencyName }}</vxe-button>
                    </template>
                    <template v-else-if="(row.taskUrgency == 2)">
                        <vxe-button status="primary" size="mini"
                            @click="shootUrgencyCilck({ taskUrgencyName: row.taskUrgencyName, id: row.directImgTaskId })">{{
                                row.taskUrgencyName }}</vxe-button>
                    </template>
                    <template v-else-if="(row.taskUrgency == 0)">
                        <vxe-button status="warning" size="mini"
                            @click="shootUrgencyCilck({ taskUrgencyName: row.taskUrgencyName, id: row.directImgTaskId })">{{
                                row.taskUrgencyName }}</vxe-button>
                    </template>
                    <template v-else>
                        <vxe-button size="mini"
                            @click="shootUrgencyCilck({ taskUrgencyName: row.taskUrgencyName, id: row.directImgTaskId })">{{
                                row.taskUrgencyName }}</vxe-button>
                    </template>
                </template>
            </vxe-column>
            <vxe-column field="warehouseStr" title="大货仓" width='92' fixed='left'></vxe-column>
            <!-- 备注 -->
            <vxe-column field="beizhu" title=" " width='28' fixed='left'>
                <template #default="{ row }">
                    <template v-if="row.markCssName == '0'">
                        <i class="vxe-icon-flag-fill" style="color: #F56C6C;" @click="openTaskRmarkInfo(row)"></i>
                    </template>
                    <template v-else>
                        <i class="vxe-icon-flag-fill" style="color: #dcdfe6;" @click="openTaskRmarkInfo(row)"></i>
                    </template>
                </template>
            </vxe-column>
            <!-- 参考 -->
            <vxe-column field="cankao" title="" width='28' fixed='left'>
                <template #default="{ row }">
                    <i class="vxe-icon-file-txt" @click="videotaskuploadfileDetal(row)"></i>
                </template>
            </vxe-column>
            <!-- 操作 -->
            <vxe-column v-if="checkPermission('api:media:directImg:AddOrUpdateShootingVideoTaskAsync')" field="caozoulie"
                title="" width='38' fixed='left'>
                <template #default="{ row }">
                    <i class="vxe-icon-ellipsis-h" @click="editTask(row)"></i>
                </template>
            </vxe-column>
            <!-- ----------------------------------------------------------- -->
            <vxe-column field="" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="photoLqNameStr" title="照片" width='65'> </vxe-column>
            <vxe-column v-if="checkPermission('directImg-list-ts')" field="photoDaysStr" title="天数" width='50'>
            </vxe-column>
            <vxe-column v-if="checkPermission('directImg-list-wcrq')" field="photoOverTimeStr" title="完成日期" width='70'>
            </vxe-column>
            <vxe-column field="Divisionline2" title="|" width='28'> <span style="color: #999;">|</span> </vxe-column>
            <!-- ----------------------------------------------------------- -->
            <vxe-column field="detailLqNameStr" title="美工" width='65'>
            </vxe-column>
            <vxe-column v-if="checkPermission('directImg-list-ts')" field="detailDaysStr" title="天数" width='50'>
            </vxe-column>
            <vxe-column v-if="checkPermission('directImg-list-wcrq')" field="detailOverTimeStr" title="完成日期" width='70'>
            </vxe-column>
            <vxe-column field="detailConfirmNameStr" title="确认人" width='65'> </vxe-column>
            <vxe-column v-if="checkPermission('directImg-list-qrrq')" field="detailConfirmTimeStr" title="确认日期" width='70'>
            </vxe-column>
            <vxe-column field="Divisionline2" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <!-- ----------------------------------------------------------- -->
            <vxe-column field="modelPhotosLqNameStr" title="照片建模" width='70'>
            </vxe-column>
            <vxe-column v-if="checkPermission('directImg-list-ts')" field="modelPhotosDaysStr" title="天数" width='50'>
            </vxe-column>
            <vxe-column v-if="checkPermission('directImg-list-wcrq')" field="modelPhotosOverTimeStr" title="完成日期"
                width='70'>
            </vxe-column>
            <vxe-column field="modelPhotoCounts" title="张数" width='50'> </vxe-column>
            <!-- ----------------------------------------------------------- -->
            <vxe-column field="" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="fpPhotoLqNameStr" title="分配照片" width='70'>
            </vxe-column>
            <vxe-column field="fpDetailLqNameStr" title="分配美工" width='70'>
            </vxe-column>
            <vxe-column field="fpModelLqNameStr" title="分配建模" width='70'>
            </vxe-column>
            <vxe-column field="Divisionline2" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="operationGroupstr" title="运营小组" width='70'>
            </vxe-column>
            <vxe-column field="dockingPeople" title="对接人" width='65'> </vxe-column>
            <vxe-column field="platformStr" title="平台" width='65'> </vxe-column>
            <vxe-column field="shopNameStr" title="店铺" width='145'> </vxe-column>
            <vxe-column field="Divisionline" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <!-- <vxe-column field="productID" title="产品ID" width='80'> </vxe-column> -->
            <vxe-column field="taskOverTimeStr" title="完成时间" width='75'>
            </vxe-column>
            <vxe-column field="confirmTimeStr" title="确认时间" width='75'> </vxe-column>
            <vxe-column field="arrivalTimeStr" title="到货日期" width='75'>
            </vxe-column>
            <vxe-column field="arrivalTimeDays" title="到货天数" width='35'>
            </vxe-column>
            <vxe-column field="deliverTimeStr" title="发货日期" width='75'>
            </vxe-column>
            <vxe-column field="deliverTimeDays" title="发货天数" width='35'>
            </vxe-column>
            <vxe-column field="applyTimeStr" title="申请日期" width='75'>
            </vxe-column>
            <vxe-column field="applyTimeDays" title="申请天数" width='35'>
            </vxe-column>
            <vxe-column field="mainUpdateTime" title="修改日期" width='75'> <template #default="{ row }">{{
                formatIsCommission(row.mainUpdateTime) }} </template></vxe-column>
            <vxe-column field="createdTime" title="创建日期" width='78' sortable>
                <template #default="{ row }">{{ formatIsCommission(row.createdTime) }} </template>
            </vxe-column>
            <vxe-column field="" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="orderNoInner" title="内部单号" width='100'>
                <template #default="{ row }">
                    <a type="text" show-overflow="ellipsis" style="color:#409EFF" @click="onShowOrderDtl(row)"> {{
                        row.orderNoInner }} </a>
                </template> </vxe-column>
            <vxe-column field="expressNo" title="快递单号" width='135'>
                <template #default="{ row }">
                    <a type="text" show-overflow="ellipsis" style="color:#409EFF" @click="onShowExproessHttp(row)"> {{
                        row.expressNo }} </a>
                </template> </vxe-column>
            <vxe-column field="shootOrderTrack" title="拿样跟踪" width='80'>
                <template #default="{ row }">
                    <a type="text" show-overflow="ellipsis" style="color:#409EFF" @click="onShowOrderDtl(row)"> {{
                        row.shootOrderTrack }} </a>
                </template>
            </vxe-column>
            <vxe-column field="" title=" " width='1'> </vxe-column>
            <div v-if="showCacle">
                <vxe-column field="Divisionline2" title="|" width='28'> <span style="color: #999;"> | </span> </vxe-column>
                <!-- <vxe-column field="taskType" title="类型" width='75'> </vxe-column>
                <vxe-column field="shootingRate" title="拍摄使用率" width='75'>
                    <template #default="{ row }">{{row.shootingRate ? row.shootingRate.toFixed(4) + '%' : '0% '}} </template>
                </vxe-column>
                <vxe-column field="modelingRate" title="建模使用率" width='75'>
                    <template #default="{ row }">{{ row.modelingRate ?row.modelingRate.toFixed(4) + '%' : '0% '}} </template>
                </vxe-column> -->
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDeptCommission" title="部门总款" width='65'>
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.shootingDeptCommission, '部门总款') }}</div>
                    </template>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDeptPhotoCommission" title="部门照片总款"
                    width='65'> 
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.shootingDeptPhotoCommission, '部门照片总款') }}</div>
                    </template>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingPhotoCommission" title="义乌照片总款"
                    width='65'> 
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.shootingPhotoCommission, '义乌照片总款') }}</div>
                    </template>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingPhotoCommissionNc" title="南昌照片总款"
                    width='65'> 
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.shootingPhotoCommissionNc, '南昌照片总款') }}</div>
                    </template>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingPhotoCommissionWh" title="武汉照片总款"
                    width='65'> 
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.shootingPhotoCommissionWh, '武汉照片总款') }}</div>
                    </template>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingPhotoPrice" title="照片单款" width='65'>
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.shootingPhotoPrice, '照片单款') }}</div>
                    </template>
                </vxe-column>

                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDeptDetailCommission" title="部门美工总款"
                    width='75'>
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.shootingDeptDetailCommission, '部门美工总款') }}</div>
                    </template>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDetailCommission" title="义乌美工总款"
                    width='75'> 
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.shootingDetailCommission, '义乌美工总款') }}</div>
                    </template>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDetailCommissionNc" title="南昌美工总款"
                    width='75'> 
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.shootingDetailCommissionNc, '南昌美工总款') }}</div>
                    </template>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDetailCommissionWh" title="武汉美工总款"
                    width='75'>
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.shootingDetailCommissionWh, '武汉美工总款') }}</div>
                    </template>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDetailPrice" title="美工单款" width='55'>
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.shootingDetailPrice, '美工单款') }}</div>
                    </template>
                </vxe-column>

                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingDeptModelPhotoCommission" title="部门建模照片总数"
                    width='65'> 
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.shootingDeptModelPhotoCommission, '部门建模照片总数') }}</div>
                    </template>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingModelPhotoCommission" title="义乌建模照片总数"
                    width='65'>
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.shootingModelPhotoCommission, '义乌建模照片总数') }}</div>
                    </template>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingModelPhotoCommissionNc" title="南昌建模照片总数"
                    width='65'> 
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.shootingModelPhotoCommissionNc, '南昌建模照片总数') }}</div>
                    </template>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingModelPhotoCommissionWh" title="武汉建模照片总数"
                    width='65'>
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.shootingModelPhotoCommissionWh, '武汉建模照片总数') }}</div>
                    </template>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-mx')" field="shootingModelPhotoPrice" title="建模照片单张"
                    width='65'>
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.shootingModelPhotoPrice, '建模照片单张') }}</div>
                    </template>
                </vxe-column>

                <vxe-column v-if="checkPermission('shootlist-hjl')" field="shootingCommissionTotal" title="提成合计" width='65'>
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.shootingCommissionTotal, '提成合计') }}</div>
                    </template>
                </vxe-column>
                <!-- <vxe-column v-if="checkPermission('shootlist-hjl')" field="shootingBaseSalary" title="拍摄底薪" width='65'>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-hjl')" field="modelBaseSalary" title="建模底薪" width='65'>
                </vxe-column>
                <vxe-column v-if="checkPermission('shootlist-hjl')" field="shootinPropPrice" title="道具费" width='65'>
                </vxe-column> 
                <vxe-column v-if="checkPermission('shootlist-hjl')" field="shootingTotal" title="合计" width='100'>
                </vxe-column>-->
            </div>
        </vxe-table>
    </div>
</template>
<script>
// import VXETable from 'vxe-table'
// import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
// import ExcelJS from 'exceljs'
import { tonumfuc } from '@/utils/tonumqian.js'
// VXETable.use(VXETablePluginExportXLSX, {
//       ExcelJS
//     })
    
import { formatTime } from "@/utils";
export default {
    props: {
        editconfig: { type: Object, default: () => { return {} } },
        treeProp: { type: Object, default: () => { return {} } },
        hasSeq: { type: Boolean, default: () => { return true } },
        hascheck: { type: Boolean, default: () => { return false } },
        showToolbar: { type: Boolean, default: () => { return true } },
        // 表格数据
        tableData: { type: Array, default: () => [] },
        // 表格型号：mini,medium,small
        size: { type: String, default: 'mini' },
        type: { type: String, default: 'primary' },
        isBorder: { type: Boolean, default: true },
        // 表格列配置
        tableCols: { type: Array, default: () => [] },
        isRemoteSort: { type: Boolean, default: () => { return true } },
        id: { type: String, default: () => { return new Date().valueOf().toString() } },
        that: { type: Object, default: () => { return null } },
        loading: { type: Boolean, default: () => { return false; } },
        border: { type: Boolean | Object, default: () => { return 'default' } },
        tableHandles: { type: Array, default: () => [] },
        showsummary: { type: Boolean, default: false },
        align: { type: String, default: '' }, //对齐方式
        summaryarry: { type: Object, default: () => { } },
        tablekey: { type: String, default: '' },//表格key
        height: { type: String, default: '100%' },//固定表头作用
        showCacle: { type: Boolean, default: () => { return false } },
    },
    created() {
        // VXETable.use(VXETablePluginExportXLSX);
    },
    data() {
        return {
            tonumfuc,
            lastSortArgs: {
                field: "",
                order: "",
            },
            arrlist: [],
            summarycolumns: [],
            tablecolumns: [],
        }
    },
    async mounted() { 
        this.$nextTick(() => {
            this.tablecolumns = this.$refs.xTable.getColumns()
        });
    },
    methods: {
        customSortMethod({ data, sortList }) {
            if (this.isRemoteSort) {
                if (sortList && sortList.length > 0) {
                    if (sortList[0].field != this.lastSortArgs.field || sortList[0].order != this.lastSortArgs.order) {
                        this.lastSortArgs = { ...sortList[0] };
                        this.$emit('sortchange', {
                            order: (this.lastSortArgs.order.indexOf('desc') > -1 ? 'descending' : 'asc'),
                            prop: this.lastSortArgs.field
                        });
                    }
                }
            } else {
                this.$refs.xTable.sort(sortList[0].field, sortList[0].order)
            }
        },
        rowStyleFun({ row, rowIndex, $rowIndex }) {
            if (row && row.isend == 0) {
                return '';
            } else {
                return 'droprow';
            }
        },
        onShowOrderDtl(row) {
            this.$emit('onShowOrderDtl', row)
        },
        onShowExproessHttp(row) {
            this.$emit('onShowExproessHttp', row)
        },
        //行切换事件
        rowChange({ newValue, oldValue, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }) {
            this.$emit('rowChange', row);
        },
        openComputOutInfo(row) {
            this.$emit('openComputOutInfo', row)
        },
        shootUrgencyCilck(one) {
            this.$emit('shootUrgencyCilck', one)
        },
        openTaskRmarkInfo(row) {

            this.$emit('openTaskRmarkInfo', row)
        },
        videotaskuploadfileDetal(row) {

            this.$emit('videotaskuploadfileDetal', row)
        },
        editTask(row) {
            this.$emit('editTask', row)
        },
        selectChangeEvent({ checked }) {
            const records = this.$refs.xTable.getCheckboxRecords()
            this.$emit('selectchangeevent', records);
        },
        //导出
        exportData(filename) {
            this.$refs.xTable.exportData({ filename: filename, sheetName: 'Sheet1', type: 'xlsx' })
        },
        formatIsCommission(value) {
            return value == null ? null : formatTime(value, 'YY-MM-DD')
        },
        //批量控制列的显影
        async ShowHidenColums(arrlist) {
      
            this.$refs.xTable.getTableColumn().collectColumn.forEach(column => {   
             
                if (arrlist.includes(column.field)) {
                    column.visible = false
                } else {
                    column.visible = true
                }
            })
          
            this.$refs.xTable.refreshColumn()
           
        },
        //清空全选
        clearSelection() {
            this.$refs.xTable.clearCheckboxRow()
        },
        footerMethod({ columns, data }) {
            const sums = [];
            if (!this.summaryarry)
                return sums
            var arr = Object.keys(this.summaryarry);
            if (arr.length == 0)
                return sums
            //const { columns, data } = param;
            var hashj = false;
            columns.forEach((column, index) => {
                if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
                    var sum = this.summaryarry[column.property + '_sum'];
                    if (sum == null) return;
                    else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
                    else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum
                    else sums[index] = sum
                }
                else sums[index] = ''
            });
            return [sums]
        },
        footercellclick({ items, $rowIndex, column, columnIndex, $columnIndex, $event }) {

            let self = this;
            var col = findcol(self.tableCols, column.property);
            if (col && col.summaryEvent)
                self.$emit('summaryClick', column.property)

            function findcol(cols, property) {
                let column;
                for (var i = 0; i < cols.length; i++) {
                    var c = cols[i];
                    if (column) break
                    else if (c.prop && c.prop.toLowerCase() == property.toLowerCase()) {
                        column = c;
                        break
                    }
                    else if (c.cols && c.cols.length > 0) column = findcol(c.cols, property)
                }
                return column
            }
        }
    }


}
</script> 


<style lang="scss" scoped> .vxe-table--render-default.border--default .vxe-table--header-wrapper {
     background-color: #fafbff;
 }

 /*斑马线颜色*/
 .vxe-table--render-default .vxe-body--row.row--stripe {
     background-color: #fafbff;
 }

 .vxe-table--render-default .vxe-body--row.row--current {
     background-color: #e5ecf5;
 }


 .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
     background-color: #A8A8A8;
 }

 .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
     background-color: #787878;
 }

 /*滚动条整体部分*/
 .mytable-scrollbar20221212 ::-webkit-scrollbar {
     width: 18px;
     height: 26px;
 }

 /*滚动条的轨道*/
 .mytable-scrollbar20221212 ::-webkit-scrollbar-track {
     background-color: #f1f1f1;
 }

 /*滚动条里面的小方块，能向上向下移动*/
 .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
     background-color: #c1c1c1;
     border-radius: 3px;
     box-sizing: border-box;
     border: 2px solid #F1F1F1;
     box-shadow: inset 0 0 6px rgba(255, 255, 255, .5);
 }

 // 滚动条鼠标悬停颜色
 .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
     background-color: #A8A8A8;
 }

 // 滚动条拖动颜色
 .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
     background-color: #787878;
 }

 /*边角，即两个滚动条的交汇处*/
 .mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
     background-color: #dcdcdc;
 }


 // 图片大小
 .mytable-scrollbar20221212 .images20221212 {
     max-width: 150px;
     max-height: 150px;
     width: 40px !important;
     height: 40px !important;
 }

 // 图片张数标记
 .mytable-scrollbar20221212 .badgeimage20221212 .el-badge__content.is-fixed {
     top: 10px;
 }

 /*  工具箱位置  */
 .vxetoolbar20221212 {
     position: absolute;
     top: 58px;
     right: 3px;
     padding-top: 0;
     padding-bottom: 0;
     z-index: 999;
     background-color: rgb(255 255 255 / 0%);
 }

 // 表头高度
 ::v-deep .vxe-table--render-default.size--mini .vxe-header--column:not(.col--ellipsis) {
     height: 50px !important;
 }

 // 表头文字行间距
 ::v-deep .vxe-header--column {
     line-height: 18px !important;
 }

 // 表格内边距
 ::v-deep .vxe-table--render-default .vxe-cell {
     padding: 0 0 0 8px !important;
 }

 .vxetableheadercell-left-20221216 {
     text-align: left;
 }

 .vxetableheadercell-center-20221216 {
     text-align: center;
 }

 .vxetableheadercell-right-20221216 {
     text-align: right;
 }

 .vxe-icon-ellipsis-h:hover {
     color: #409EFF;
     margin-left: 2px;
     background-color: #F1F1F1;
 }

 .vxe-icon-ellipsis-h {
     color: #999;
     font-size: 15px;
 }

 .vxe-icon-file-txt:hover {
     color: #409EFF;
     margin-left: 2px;
     background-color: #F1F1F1;
     font-weight: 600;
 }

 .vxe-icon-file-txt {
     color: #999;
     font-size: 15px;
 }

 .vxetablecss {
     margin: 0;
 }

 ::v-deep span.vxe-cell--item {
     cursor: pointer !important;
 }

 ::v-deep .droprow td {
     color: rgb(250, 9, 9);
     position: relative;
 }

 ::v-deep .droprow ::after {
     content: "";
     position: absolute;
     top: 50%;
     left: 0;
     width: 100%;
     height: 0.1px;
     background-color: rgb(250, 9, 9);
     transform: translateY(-50%);
 }
</style>