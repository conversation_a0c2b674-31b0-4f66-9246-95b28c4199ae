<template>
  <div class="allbody">
    <my-container v-loading="pageLoading">
      <div>
        <img src="https://s1.ax1x.com/2022/11/23/z8wQAS.png" alt="" width="100%" mode="aspectFit" />
      <div class="body">
        <div class="heard flexrow columnflex">
            <el-avatar icon="el-icon-user-solid" style="width: 45px; height:45px; margin-left: 1rem;"></el-avatar>
            <div class="columnflex column">
              <span>XXX旗舰店</span>
              <span style="margin-right: auto;">天猫</span>
            </div>
            <div class="marginleft flexrow" style="margin-left: 5rem;">
              <div class="column marginleft"><span class="smalltext">宝贝描述</span><span>5.0</span></div>
              <div class="column marginleft"><span class="smalltext">卖家服务</span><span>5.0</span></div>
              <div class="column marginleft"><span class="smalltext">物流服务</span><span>5.0</span></div>
            </div>
            <div style="margin-left: auto;margin-right: 2rem;" class="flexrow">
              <div class="marginleft sectionalizer smalltext iconcover"><span style="margin: 0 7px;"><i class="el-icon-chat-line-round"></i>联系客服</span></div>
              <div class="marginleft sectionalizer smalltext iconcover"><span style="margin: 0 7px;"><i class="el-icon-goods"></i>进入店铺</span></div>
            </div>
        </div>
        <div class="bgcolor">
          <div class="content">
              <div class="flexleft paddingtwo">
                <div class="left-img">
                  <div class="relete-block">
                    <!-- <el-carousel trigger="click" indicator-position="none" v-show="!clickbtn" height="450px" :autoplay="false" ref="carousel" @setActiveItem="mouseover()" @change="changecarou">
                      <el-carousel-item  v-for="(item,i) in imglist" :key="i">
                        <img width="450px" height="450px" :src="item.url" :alt="item.name">
                      </el-carousel-item>
                    </el-carousel> -->
                    <div style="height:450px;width:450px;z-index: 99; border-radius: 20px;" v-if="imglist.length>0">
                        <div class="fdj" @mousemove="mousemove"  v-show="!clickbtn" >
                          <div class="small" ref="small"  @mousemove="handerover" @mouseleave="handOut">
                              <img :src="imgshow?imgshow:imglist.length>0?imglist[0].url:''" alt=""  style="border-radius: 20px;"/>
                              <div v-show="overarea">
                                <div  class="mask" :style="{'left':left + 'px','top':top + 'px',}" ref="mask"></div>
                              </div>
                          </div>
                          <div class="big" ref="big" v-show="overarea">
                              <img ref="bigimg" :src="imgshow?imgshow:imglist.length>0?imglist[0].url:''" alt=""  :style="{'left':imgX + 'px','top':imgY + 'px',}"/>
                          </div>
                      </div>
                    </div>
                    <div v-else style="height:450px;width:450px;z-index: 99; border-radius: 20px; color: #909399; display: flex; justify-content: center; align-items: center;">
                        {{msgtext}}
                    </div>
                    <div id="divhover" v-show="clickbtn" height="450px"  class="videoshow">
                        <video width="450px" height="450px" muted autoplay loop :src="videolist!=null?videolist.domainUrl:''">
                            <!-- <source :src="videolist.url" type="video/mp4"> -->
                        </video>
                        <div><i class="el-icon-close deleteicon" @click="clickbtn = false"></i></div>
                    </div>

                    <div class="square" v-show="!clickbtn&&videolist!=null" @click="clicksquare"><i class="el-icon-caret-right" style="font-size: 3rem; color:white;"></i></div>
                  </div>
                </div>
                <!-- <div class="flexrow smaslling">
                  <div id="click-scroll-X">
                    <i class="el-icon-caret-left iconsize" @click="leftSlide"></i>
                    <div class="scroll_wrapper" ref="wrapperCon">
                        <div class="scroll_list">
                            <div class="item" v-for="(i, idx) in imglist" :key="idx" @mouseenter="mouseover(idx,i.url)" :class="{'clickBg':idx==clickIndex,'hoverBg':idx==hoverIndex}">
                                <div style="margin: 2px; display: flex; justify-content: center; align-items: center;">
                                 <img width="95px" height="95px" :src="i.url" :alt="i.name" mode="center"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <i class="el-icon-caret-right iconsize" @click="rightSlide"></i>
                  </div>
                </div> -->
                <div class="flexrow smaslling">
                    <div class="scroll_wrapper">
                        <div class="scroll_list" style="margin-left: 17px;">
                            <div class="content-colorimg outline" v-if="imglist.length>0">

                              <div class="centerflex">
                                <div :class="[clickbtn?'hoverBg':'hoverimg']" style="margin: 5px 0 0 6.2px; display: flex; justify-content: center; align-items: center;" >
                                  <img @mouseenter="mouseover(-1,videolist!=null?videolist.domainUrl:'')" :src="imglist[0]?imglist[0].url:''" alt="" width="68px" height="68px" style="border-radius: 8px;" />
                                </div>
                              </div>

                              <div class="centerflex"  v-for="(i, idx) in imglist" :key="idx">
                                <div :class="[idx==hoverIndex?'hoverBg':'hoverimg']" style="margin: 5px 0 0 6.2px; display: flex; justify-content: center; align-items: center;" >
                                  <img @mouseenter="mouseover(idx,i.url)" :src="i.url" :alt="i.name" width="68px" height="68px" style="border-radius: 8px;"/>
                                </div>
                              </div>
                            </div>
                            <div class="content-colorimg outline" v-else style="color: #909399; display: flex; justify-content: center; align-items: center;">
                                {{msgtext}}
                            </div>
                        </div>
                    </div>
                </div>
              </div>
              <div class="flexright paddingtwo">
                <el-row>
                  <el-col :span="24"><div class="grid-content bg-purple-dark margintop weightsize">标题</div></el-col>
                </el-row>
                <el-row>
                  <el-col :span="24"><div class="grid-content bg-purple-dark margintop">月销量2万+</div></el-col>
                </el-row>
                <div style="margin-top: 1rem;" class="margintop flexrow">
                  <div><div class="grid-content bg-purple-dark color-red">券后￥<span style="font-size: 2rem;">999.9</span>起</div></div>
                  <div class="grid-content bg-purple-dark btn-quan" ><div class="btn-nei">活动券后￥99.99起</div></div>
                </div>
                <el-row class="margintop">
                  <el-col :span="24"><div class="grid-content bg-purple-dark">
                    <div class="flexrow">
                      <div class="sectionalizer flexrow">
                        <div class="el-dropdown-link marginlr">优惠</div>
                        <span class="columnline"></span>
                        <div class="el-dropdown-link marginlr">保障</div>
                        <span class="columnline"></span>
                        <el-dropdown>
                          <span class="el-dropdown-link marginlr">
                            参数<i class="el-icon-arrow-down el-icon--right"></i>
                          </span>
                          <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item>黄金糕</el-dropdown-item>
                            <el-dropdown-item>狮子头</el-dropdown-item>
                            <el-dropdown-item>螺蛳粉</el-dropdown-item>
                            <el-dropdown-item disabled>双皮奶</el-dropdown-item>
                            <el-dropdown-item divided>蚵仔煎</el-dropdown-item>
                          </el-dropdown-menu>
                        </el-dropdown>
                      </div>
                    </div>
                  </div></el-col>
                </el-row>
                <el-row class="margintop">
                  <el-col :span="24"><div class="grid-content bg-purple-dark"><div class="smalltext">配送：</div>江西南昌 至 金华市义乌市城西街道</div></el-col>
                </el-row>
                <el-row class="margintop">
                  <el-col :span="24"><div class="grid-content bg-purple-dark"><div class="smalltext">快递：</div> 免快递</div></el-col>
                </el-row>
                <div class="margintop content-colorimg-flex">
                  <div class="smalltext">颜色分类：</div>
                  <div class="content-colorimg outline" v-if="skuImgList.length>0">
                    <div style="width: 2.4rem;height:2.4rem; margin-left: 5px;" class="centerflex" :class="{'clickBg':i==riclickIndex}"  :style="imgboder" @click="isimg(itemm,i)" v-for="(itemm,i) in skuImgList" :key="i"><img :src="itemm.url" :alt="itemm.name" width="98%" height="98%"></div>
                  </div>
                  <div class="content-colorimg outline" v-else style="color: #909399; display: flex;  align-items: center;">
                      {{msgtext}}
                  </div>

                </div>

                <div class="bgcolor margintop flexrow columnflex margintopbot">
                  <span class="smalltext" style="margin-right: 40px;">数量：</span><el-input-number v-model="num" @change="handleChange" :min="1" :max="10" label="数量"></el-input-number><span class="smalltext">有货</span>
                </div>

                <div class="margintop flexrow btncss">
                  <div class="button-buy">
                    <div class="btn-left centerflex btntobuy">领券购买</div>
                    <div class="btn-right centerflex btntoup">加入购物车</div>
                  </div>
                  <div style="margin-left: 20px;"><i class="el-icon-star-off"></i>收藏</div>
                </div>
              </div>
          </div>

          <div class="rightthr">
            <el-button type="primary" style="margin: 10px 0; width: 100px;">PC端预览</el-button>
            <el-button type="primary" style="margin: 10px 0; width: 100px;" @click="tomobilemsg">手机端预览</el-button>
            <el-button type="primary" style="margin: 10px 0; width: 100px;" @click="tocarmsg">车图预览</el-button>
          </div>


          <div class="margintop flexrow topborder">
            <div>宝贝详情</div>
            <div style="margin-left: 20px;">宝贝评价</div>
          </div>
          <div style="flex-direction: column;">
            <el-row>
              <el-col :offset="5" :span="5"><div class="grid-content bg-purple smalltext">品牌：</div></el-col>
              <el-col :span="5"><div class="grid-content bg-purple-light smalltext">颜色：</div></el-col>
              <el-col :span="5"><div class="grid-content bg-purple-light smalltext">场景：</div></el-col>
            </el-row>
            <el-row>
              <el-col :offset="5" :span="5"><div class="grid-content bg-purple smalltext">型号：</div></el-col>
              <el-col :span="5"><div class="grid-content bg-purple-light smalltext">材质：</div></el-col>
              <el-col :span="5"><div class="grid-content bg-purple-light smalltext">人群：</div></el-col>
            </el-row>
          </div>


          <div id="alldemo" v-if="bottomimg.length>0">
            <div v-for="(item,i) in bottomimg" :key="i" class="flexcolumn imglist">
              <div class="img_box" :id="'clip-img-w'+i" style="display: flex; flex-direction: row; justify-content: center;">
                <imgwater :imgsrc = "item.url" :canvasmore="'clip-img-can'+i"></imgwater>
              </div>
            </div>

          </div>


          <div v-else style="color: #909399; display: flex; justify-content: center; align-items: center;">
              {{msgtext}}
          </div>
          <button @click="intosql">存储数据</button>
        </div>
        

         
      </div>
   <img src="https://s1.ax1x.com/2022/11/22/z1JzQO.png" alt="" width="100%" mode="aspectFit">
    </div>
     

   </my-container>

  </div>
    

</template>
<script>
import MyContainer from "@/components/my-container";
import { getPageDetailImgInfo} from '@/api/media/ShootingVideo';
import imgwater from '@/views/media/shooting/imgwater.vue'

export default {
   name: "Users", 
   components: { MyContainer, imgwater },
   data() {
       return {
        imgboder: {border:''},
        top:'',
        left:'',
        imgX:'',
        imgY:'',
        overarea: false,
        pageLoading: false,
        num: 1,
        clickbtn: false,
        hoverIndex: -1,
        clickIndex: -1,
        riclickIndex: -1,
        imgshow: '',
        platform: null,
        imglist:[
         {name:'',url:''}
        ],
        videolist:'',
        bottomimg: {},
        skuImgList: [],
        isimghover: false,
        cutImgSrc: '',
        draw: false,
        msgtext: '暂无内容，请先上传后预览！'
       };
   },
   //向子组件注册方法
   provide () {
       return {
        timer:null
       }
   },
   created() {
    // win.setContentProtection(true)
    // window.addFlags(WindowManager.LayoutParams.FLAG_SECURE)
    this.$nextTick(() => {
      // 禁用右键
      document.oncontextmenu = new Function("event.returnValue=false");
      // 禁用选择
      document.onselectstart = new Function("event.returnValue=false");
    });
  },
   async mounted() {
    this.getlist();
    this.platform = this.$route.query.platform;
   }, 
   methods: {
    async getlist(){
      let _this = this;
      _this.pageLoading= true;
      var res  =  await getPageDetailImgInfo({taskid: this.$route.query.id});
      if(res?.success)
      {
          _this.imglist = res.data.mainImgList;
          _this.videolist=res.data.vedioList;
          _this.bottomimg = res.data.detailImgList;

          _this.skuImgList = res.data.skuImgList;
      }
      _this.pageLoading= false;
    },
    handleChange(value) {
        console.log(value);
      },
    setActiveItem(val){
      return 2;
    },
    clicksquare(){
      this.clickbtn = true;
      this.riclickIndex = -1;
      this.hoverIndex = -1;
      // console.log("点击圈圈")
    },
    selectStyle(){
      console.log("hover事件")
    },
    leftSlide(){
        let left=this.$refs.wrapperCon.scrollLeft
        let num=0
        clearInterval(this.timer)
        this.timer=null
        this.timer=setInterval(()=>{
            if(!left||num>=300){
                // 停止滚动
                clearInterval(this.timer)
                this.timer=null
                return
            }
            this.$refs.wrapperCon.scrollLeft=left-=30
            num+=30
        },30)
        // 20：速度
    },
    rightSlide(){

        let left=this.$refs.wrapperCon.scrollLeft
        let scrollWidth=this.$refs.wrapperCon.scrollWidth
        let clientWidth=this.$refs.wrapperCon.clientWidth
        let num=0
        clearInterval(this.timer)
            this.timer=setInterval(()=>{
            if(left+clientWidth>=scrollWidth||num>=300){
                clearInterval(this.timer)
                return
            }
            this.$refs.wrapperCon.scrollLeft=left+=30
            num+=30
        },20)
      },
      mouseover(index,img){
        if(img==this.videolist?.domainUrl&&index==-1){
          this.clickbtn = true;
          this.hoverIndex = -1;
          console.log("打印数据",this.clickbtn)
          return
        }
        this.clickbtn = false;
        this.hoverIndex = index;
        this.imgshow = img;
        this.riclickIndex = -1;
      },
      isimg(val,index){
        this.clickbtn = false;
        this.imgshow = val.url;
        this.riclickIndex = index;
        this.hoverIndex = -1;
      },
      changecarou(index){
        this.hoverIndex = index;
      },
      mousemove(e){
        let small = this.$refs.small
        let mask = this.$refs.mask
        let big = this.$refs.big
        let bigimg = this.$refs.bigimg
        
        let maskX =  e.pageX - small.offsetLeft
        let maskY = e.pageY - small.offsetTop
  
        maskX = maskX - mask.offsetWidth +110;
        maskY = maskY - mask.offsetHeight+255;

        maskX = maskX < 0 ? 0 : maskX;
        maskY = maskY < 0 ? 0 : maskY;
        
       
        if(maskX>450&&maskX<760){
          maskX = maskX-450
        }else if(maskX>=760){
          // this.overarea =false
          maskX = 310
        }else if(maskX<=450){
          maskX = 0
        }

        if(maskY>450&&maskY<760){
          maskY = maskY-450
        }else if(maskY>=760){
          maskY = 310
        }else if(maskY<=450){
          maskY = 0
        }
        // maskX = maskX > small.offsetWidth - mask.offsetWidth ? small.offsetWidth - mask.offsetWidth : maskX;
        // maskY = maskY > small.offsetHeight - mask.offsetHeight ? small.offsetHeight - mask.offsetHeight : maskY;
        // console.log("计算后msk位置",[maskX,maskY])
        let bigImgX = maskX * (big.offsetWidth - bigimg.offsetWidth) / (small.offsetWidth - mask.offsetWidth);
        let bigImgY = maskY * (big.offsetHeight - bigimg.offsetHeight) / (small.offsetHeight - mask.offsetHeight)
  
        this.left = maskX
        this.top = maskY
        this.imgX = bigImgX
        this.imgY = bigImgY  
      },
      handerover(){
        this.overarea =true
      },
      handOut(){
        this.overarea =false
      },
      imgmouseover(val,index){
        let _this = this;
        _this.isimghover = true;

        var wrap = document.getElementById("imgid"+index);
        // var width = wrap.offsetWidth;
        // var height = wrap.offsetHeight;
        // _this.x = e.offsetX
        // _this.y = e.offsetY
      },
      tomessage(val,index){
        var thiz = this;
        thiz.draw = true;
        var wrap = document.getElementById("imgid"+index);
        var width = wrap.offsetWidth;
        var height = wrap.offsetHeight;
        console.log("打印原图宽高",[width,height])

        var clipcanvas = document.getElementById("clipcanvas"+index);
        var drawcanvas = document.getElementById("drawcanvas"+index);
        clipcanvas.width = width;
        clipcanvas.height = height;
        // clipcanvas.style.backgroundColor = 'rgba(0,0,0,0.1)';
        drawcanvas.width = width;
        drawcanvas.height = height;

        var clipCtx = drawcanvas.getContext("2d");
/////////////////////////////////////////////////////////////
        clipCtx.font = '30px Arial'; // 设置字体大小和字体
        clipCtx.rotate(-0.4);
        const gradient = clipCtx.createLinearGradient(0, 0, width, 0);
        gradient.addColorStop(0, 'red');
        gradient.addColorStop(0.5, 'yellow');
        gradient.addColorStop(1, 'green');
        clipCtx.fillStyle = gradient; // 颜色
        
        clipCtx.fillText('这是水印', width / 3, height / 5);

        const main = document.querySelector("clipcanvas"+index);
        main.style.backgroundImage = `url(${val.url})`;
//////////////////////////////////////





        var clipImg = document.createElement("img");
        clipImg.classList.add('img_anonymous');
        clipImg.crossOrigin = "anonymous";
        clipImg.src = val.url;
        clipImg.width = width+'px';
        clipImg.height = height+'px';
        // clipImg.style.width = width+'px';
        // clipImg.style.height = height+'px';
        clipImg.style.zIndex = 250;
        clipImg.mode = 'scaleToFill';
        var timg = clipImg.cloneNode();
        wrap.appendChild(clipImg);
        clipImg.onload = function(){
            var x = Math.floor((width - this.width)/2);
            var y = Math.floor((height - this.height)/2);
            // console.log("画图宽高",[timg.width,timg.height]);
            // console.log("this指向",this);
            clipCtx.drawImage(this,0,0,timg.width,timg.height,x,y,this.width,this.height);
            // clipCtx.drawImage(this,0,0,width,height,x,y,this.width,this.height);
        };

        var ctx = clipcanvas.getContext("2d");
            ctx.fillStyle = 'rgba(0,0,0,0.4)';
            ctx.strokeStyle="rgba(0,143,255,1)";
            var start = null;
            var clipArea = {};//裁剪范围

            clipcanvas.onmousedown = function(e){
                start = {
                    x:e.offsetX,
                    y:e.offsetY
                };
            };
            clipcanvas.onmousemove = function(e){
                if(start){
                    fill(start.x,start.y,e.offsetX-start.x,e.offsetY-start.y)
                }
            };
            document.addEventListener("mouseup",function(e){
                if(start){
                    start = null;
                    var url = startClip(clipArea);
                    //生成base64格式的图
                    thiz.cutImgSrc = url;


                     //添加输入框
                    let input = document.createElement('input');
                    let canvasArea = document.getElementsByClassName('img_box')[index];
                    // let canvasArea = document.getElementById("clipcanvas"+index)[0];
                    canvasArea.appendChild(input);
                    input.style.left = `${clipArea.x}px`;
                    input.style.top = `${clipArea.y+clipArea.h}px`;
                    input.style.border = "2px dashed red";
                    input.style.zIndex = 101;
                    input.style.height = '30px';
                    input.style.position = 'absolute';
                    input.focus();
                    // console.log("输入框信息",input)
                }
            });
            function fill(x,y,w,h){
                ctx.clearRect(0,0,width,height);
                ctx.beginPath();
                //遮罩层
                ctx.globalCompositeOperation = "source-over";
                ctx.fillRect(0,0,width,height);
                //画框
                ctx.globalCompositeOperation = 'destination-out';
                ctx.fillRect(x,y,w,h);
                //描边
                ctx.globalCompositeOperation = "source-over";
                ctx.moveTo(x,y);
                ctx.lineTo(x+w,y);
                ctx.lineTo(x+w,y+h);
                ctx.lineTo(x,y+h);
                ctx.lineTo(x,y);
                ctx.stroke();
                ctx.closePath();
                clipArea = {
                    x,
                    y,
                    w,
                    h
                };
            }
            function startClip(area){
                var canvas = document.createElement("canvas");
                canvas.width = area.w;
                canvas.height = area.h;

                var data = clipCtx.getImageData(area.x,area.y,area.w,area.h);

                var context = canvas.getContext("2d");
                context.putImageData(data,0,0);
                return canvas.toDataURL("image/png",1);
            }
            // 移除创建的img节点
            // var imglist = document.getElementById("clip-img-w").getElementsByTagName("img")
            // // imglist.remove();
            // for(var i=0; i<imglist.length;i++){
            //     imglist[i].remove()
            // }
      },
      imgmouseleave(){
        let _this = this;
        // _this.isimghover = false;
      },
      tomobilemsg(){
        // let routeUrl = this.$router.resolve({
        //        path: "/mobilemsg",
        //       //  query: {id:this.rowinfo}
        //   });
        // window.open(routeUrl.href, '_blank');
        this.$router.push({ path: '/mobilemsg',query: {id:this.$route.query.id,platform:  this.platform}})
      },
      tocarmsg(){
            this.$router.push({ path: '/msgshow1',query: {id:this.$route.query.id,platform:  this.platform}})
        },
      intosql(){
        let allmsg = document.getElementById('alldemo');
        // console.log("打印",allmsg)
      }

   },
};
</script>
<style lang="scss" scoped>
.allbody {
  height: 100%;
  width: 100%;
}
.whitecolor{
  background-color: white;
}
// .heard-title{
//   height: 3rem;
//   padding: 0 80px;
//   background-color: white;
// }
.body{
  height: 100%; 
  // border-radius: 20px; 
  padding: 20px 22rem;
  background-color: #E4E7ED;
}
.heard{
  height: 4rem; 
  background-color: white;
  border-radius: 30px;
}
.content{
  min-height: 36rem; 
  background-color: white;
  border-radius: 20px;
  margin: 20px 0;
  display: flex;
  position: relative;
}
.rightthr{
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  position: absolute;
  width: 150px;
  height: 300px;
  // background-color: red;
  right: 0;
  top: 50%;
  transform: translate(0,-50%);
}
.flexrow{
  display: flex;
  flex-direction: row;
}
.flexleft{
  flex: 3;
  min-height: 40rem; 
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.flexright{
  flex: 7;
}
.paddingtwo{
padding: 30px;
}
.margintop{
  margin-top: 2rem;
}
// .margintopbot{
//   margin: 30px 0 100px 0;
// }
.marginleft{
  margin-left: 1rem;
}
.column{
  display: flex;
  flex-direction: column;
}
.bgcolor{
  background-color: white;
}
.centerflex{
  display: flex;
  justify-content: center;
  align-items: center;
}
.columnflex{
  display: flex;
  align-items: center;
}
.left-img{
  background-color: #EBEEF5;
  border-radius: 20px;
  // height: 22rem;
  // width: 22rem;
}
.left-smallimg{
  height: 5rem;
  width: 100%;
  // margin-top: 1rem;
  background-color: #EBEEF5;
}
.content-colorimg{
  height: 100%;
  width: 480px;
  // background-color: #EBEEF5;
}
.content-colorimg-flex{
  display: flex;
  flex-direction: row;
}
.btncss{
  line-height: 2rem;
}
.button-buy{
  border-radius: 20px;
  display: flex;
  flex-direction: row;
}
.btn-left{
background-color: #e43112;
color: white;
padding: 5px;
width: 7rem;
border-radius: 20px 0 0 20px;
}
.btn-right{
  background-color: #ebb608;
  color: white;
  padding: 5px;
  width: 7rem;
  border-radius: 0 20px 20px 0;
}
.color-red{
  color: #f52f0c;
}
.btn-quan{
  margin-left: 20px;
  border-radius: 20px;
  background-color: #e43112;
  color: white;
  display: flex;
  align-items: center;
}
.btn-nei{
  margin: 0 15px;
}
.smalltext{
  color: #909399;
  font-size: 0.8rem;margin-right: 10px;
}
.sectionalizer{
  border-radius: 20px;
  padding: 2px;
  // background-color: #e43112;
  border: 1px solid #999695e5;
}
.marginlr{
  margin: 0 15px;
  color: #606266;
  font-size: 0.9rem;
  // width: 100%;
  // border-right: 1px solid #999695e5;
}
.columnline{
  height: 20px;
  width: 1px;
  background-color: #909399;
}

.btntobuy {
 border: 0;
 background-color: #e46852;
 box-shadow: rgb(0 0 0 / 5%) 0 0 8px;
 letter-spacing: 1.5px;
 text-transform: uppercase;
 font-size: 0.9rem;
 transition: all .5s ease;
}
.btntoup {
 border: 0;
 background-color: #ebb608;
 box-shadow: rgb(0 0 0 / 5%) 0 0 8px;
 letter-spacing: 1.5px;
 text-transform: uppercase;
 font-size: 0.9rem;
 transition: all .5s ease;
}
.btntoup:hover{
 letter-spacing: 3px;
 background-color: rgb(241, 168, 9);
 color: hsl(0, 0%, 100%);
 box-shadow: rgb(241, 168, 9) 0px 7px 29px 0px;
}

.btntoup:active{
 letter-spacing: 3px;
 background-color: rgb(241, 168, 9);
 color: hsl(0, 0%, 100%);
 box-shadow: rgb(241, 168, 9) 0px 0px 0px 0px;
 transform: translateY(10px);
 transition: 100ms;
}

.btntobuy:hover{
 letter-spacing: 3px;
 background-color: rgb(236, 46, 12);
 color: hsl(0, 0%, 100%);
 box-shadow: rgb(236, 46, 12) 0px 7px 29px 0px;
}

.btntobuy:active{
 letter-spacing: 3px;
 background-color: rgb(236, 46, 12);
 color: hsl(0, 0%, 100%);
 box-shadow: rgb(236, 46, 12) 0px 0px 0px 0px;
 transform: translateY(10px);
 transition: 100ms;
}
.smaslling{
  width: 100%;
  // justify-content: center;
  align-items: center;
  margin-top: 1rem;
}
.iconsize{
  font-size: 1.8rem;
}
.iconcover:hover{
  color: #409EFF;
  border-color: #409EFF;
}
.flexcolumn{
  display: flex;
  flex-direction: column;
}
.outline{
  display: flex;
  overflow-x: hidden;
  flex-wrap: wrap;
  flex-direction: row;
}
.imglist{
  margin: 1rem 5rem;
  display: flex;
  align-items: center;
}
::v-deep .el-main{
  background-color: #E4E7ED;
}
.relete-block{
  position: relative;
}
.square{
  background-color: rgba(157, 161, 170, 0.461); 
  width: 40px; 
  height:40px;
  border-radius: 40px;
  position: absolute;
  top: 86%;
  left: 0%;
  z-index: 201;
  // transform: translate(-50%,-50%);
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px solid #fff;
}
.videoshow{
  z-index: 200;
  top: 0;
  position: absolute;
}
.deleteicon{
  position: absolute;
  top: 5px;
  right: 5px;
  color: #909399;
  font-size: 2.6rem;
  display: none;
  // color: red;
}
#divhover:hover .deleteicon{
  display: block;
}
.topborder{
  border-top: 1px solid #9e9e9ea2;
  // line-height: 2rem;
  padding: 1rem 2rem;
  font-weight: 600;
}
#click-scroll-X {
    display: flex;
    align-items: center;
    .left_btn,.right_btn {
      font-size: 2.8rem;
      cursor: pointer;
    }
  .scroll_wrapper {
    width: 410px;
    overflow-x: scroll;
    padding: 20px 20px;
    overflow: hidden;
    .scroll_list{
    display: flex;
    align-items: center;
    justify-content: space-between;
    // overflow: hidden;

        .item {
        width: 100px;
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid rgb(223, 223, 223);
        box-sizing: border-box;
        flex-shrink: 0;
        }
    }
  }
}
.hoverBg{
  //  background: #da1f1f;
  border-radius: 5px;
   border: 1px solid #da1f1f;
   color: #fff;
  border-radius: 8px;
 }
 .hoverimg{
  border: 1px solid #eee;
  border-radius: 8px;
 }
 .clickBg{
  //  background: red;
   border: 1px solid red;
   color: #fff;
 }
 ::v-deep .el-header{
  background-color: rgba(208, 210, 214, 0.693);
 }
 .small {
      width: 450px;
      height: 450px;
      position: relative;
      cursor: move;
    }
  .small img {
    width: 100%;
    height: 100%;
  }
  .big {
        width: 450px;
        height: 450px;
        position: absolute;
        top: 0;
        left: 450px;
        overflow: hidden;
    }
    .big img {
        position: absolute;
        width: (45*45/14)px;
        height: (45*45/14)px;
        left:0;
        top:0;
        z-index: 89;
    }
  
    .mask {
        width: 140px;
        height: 140px;
        background: rgba(0, 119, 255, 0.4);
        position: absolute;
        top: 0px;
        left: 0px;
        z-index: 150;
        cursor: move;
    }
    .fdj{
        border-radius: 20px;
        height: 450px;
        width: 450px;
        background-color: #F2F6FC;
    }
    .img_box{
    width: 790px;
    height: 100%;
    position:relative;
    }
    .img_box canvas{
      width: 790px;
      position: absolute;
      }
    .img_box #clipcanvas{
        z-index: 2;
      }
    .img_box #drawcanvas{
        z-index: 1;
    }
    .btntomsg{
      position: absolute;
      left: 790px;
    }
    .weightsize{
      font-weight: 600;
      font-size: 23px;
    }
</style>

