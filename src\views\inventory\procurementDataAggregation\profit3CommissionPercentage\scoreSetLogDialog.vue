<template>
  <vxetablebase :id="'scoreSetLogDialog202501031832'" :tablekey="'scoreSetLogDialog202501031832'" ref="table"
    :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
    :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0"
    :loading="loading" :height="'450px'" :isNeedExpend="false" :border="true">
  </vxetablebase>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatTime } from "@/utils";
import { getProfit3CommissionPercentageScoreSetLog } from '@/api/inventory/purchaseSummary';

const tableCols1 = [
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '设置时间', prop: 'rptDate', formatter: (row) => formatTime(row.rptDate, "YYYY-MM") },
  {
    istrue: true, summaryEvent: true, rop: '', label: `降价`, merge: true, prop: 'mergeField',
    cols: [
      { istrue: true, width: '100', prop: 'allotDownAmountMaxScore', label: '上限' },
      { istrue: true, width: '100', prop: 'allotDownAmountDeductScore', label: '带宽' },
      { istrue: true, width: '100', prop: 'allotDownAmountMinScore', label: '下限' },
      { istrue: true, width: '100', prop: 'allotDownAmountRank', label: '排名' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `扣款金额`, merge: true, prop: 'mergeField1',
    cols: [
      { istrue: true, width: '100', prop: 'deductAmountMaxScore', label: '上限' },
      { istrue: true, width: '100', prop: 'deductAmountDeductScore', label: '带宽' },
      { istrue: true, width: '100', prop: 'deductAmountMinScore', label: '下限' },
      { istrue: true, width: '100', prop: 'deductAmountRank', label: '排名' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `滞销退款金额`, merge: true, prop: 'mergeField2',
    cols: [
      { istrue: true, width: '100', prop: 'refundAmountMaxScore', label: '上限' },
      { istrue: true, width: '100', prop: 'refundAmountDeductScore', label: '带宽' },
      { istrue: true, width: '100', prop: 'refundAmountMinScore', label: '下限' },
      { istrue: true, width: '100', prop: 'refundAmountRank', label: '排名' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `库存周转`, merge: true, prop: 'mergeField3',
    cols: [
      { istrue: true, width: '100', prop: 'brandTurnoverDayMaxScore', label: '上限' },
      { istrue: true, width: '100', prop: 'brandTurnoverDeductScore', label: '带宽' },
      { istrue: true, width: '100', prop: 'brandTurnoverDayMinScore', label: '下限' },
      { istrue: true, width: '100', prop: 'brandTurnoverDayRank', label: '排名' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `全仓缺货得分`, merge: true, prop: 'mergeField4',
    cols: [
      { istrue: true, width: '100', prop: 'fullWmsMonthLackCountMaxScore', label: '上限' },
      { istrue: true, width: '100', prop: 'fullWmsMonthLackCountDeductScore', label: '带宽' },
      { istrue: true, width: '100', prop: 'fullWmsMonthLackCountMinScore', label: '下限' },
      { istrue: true, width: '100', prop: 'fullWmsMonthLackCountRank', label: '排名' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `本仓缺货得分`, merge: true, prop: 'mergeField5',
    cols: [
      { istrue: true, width: '100', prop: 'inWmsMonthLackCountMaxScore', label: '上限' },
      { istrue: true, width: '100', prop: 'inWmsMonthLackCountDeductScore', label: '带宽' },
      { istrue: true, width: '100', prop: 'inWmsMonthLackCountMinScore', label: '下限' },
      { istrue: true, width: '100', prop: 'inWmsMonthLackCountRank', label: '排名' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `云仓缺货得分`, merge: true, prop: 'mergeField6',
    cols: [
      { istrue: true, width: '100', prop: 'wmsMonthLackCountMaxScore', label: '上限' },
      { istrue: true, width: '100', prop: 'wmsMonthLackCountDeductScore', label: '带宽' },
      { istrue: true, width: '100', prop: 'wmsMonthLackCountMinScore', label: '下限' },
      { istrue: true, width: '100', prop: 'wmsMonthLackCountRank', label: '排名' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `摘品扣分`, merge: true, prop: 'mergeField7',
    cols: [
      { istrue: true, width: '100', prop: 'packingGoodsDeductScoreStartCount', label: '标准数量' },
      { istrue: true, width: '100', prop: 'packingGoodsDeductScore', label: '带宽' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `滞销退款扣分`, merge: true, prop: 'mergeField8',
    cols: [
      { istrue: true, width: '100', prop: 'refundDeductScoreStartCount', label: '标准数量' },
      { istrue: true, width: '100', prop: 'refundDeductScore', label: '带宽' },
      { istrue: true, width: '100', prop: 'refundNoDeductScoreStartCount', label: '公司实际回收标准' },
    ]
  },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '操作人', prop: 'operateUserName' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '操作时间', prop: 'operateTime' },
];

const tableCols2 = [
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '设置时间', prop: 'rptDate', formatter: (row) => formatTime(row.rptDate, "YYYY-MM") },
  {
    istrue: true, summaryEvent: true, rop: '', label: `全量得分`, merge: true, prop: 'mergeField',
    cols: [
      { istrue: true, width: '100', prop: 'deductAmountAvgScoreMaxScore', label: '上限' },
      { istrue: true, width: '100', prop: 'deductAmountAvgScoreDeductScore', label: '带宽' },
      { istrue: true, width: '100', prop: 'deductAmountAvgScoreMinScore', label: '下限' },
      { istrue: true, width: '100', prop: 'deductAmountAvgScoreRank', label: '排名' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `个人得分`, merge: true, prop: 'mergeField1',
    cols: [
      { istrue: true, width: '100', prop: 'deductAmountScoreMaxScore', label: '上限' },
      { istrue: true, width: '100', prop: 'deductAmountScoreDeductScore', label: '带宽' },
      { istrue: true, width: '100', prop: 'deductAmountScoreMinScore', label: '下限' },
      { istrue: true, width: '100', prop: 'deductAmountScoreRank', label: '排名' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `降价得分`, merge: true, prop: 'mergeField2',
    cols: [
      { istrue: true, width: '100', prop: 'allotDownAmountDeptMaxScore', label: '上限' },
      { istrue: true, width: '100', prop: 'allotDownAmountDeptDeductScore', label: '带宽' },
      { istrue: true, width: '100', prop: 'allotDownAmountDeptMinScore', label: '下限' },
      { istrue: true, width: '100', prop: 'allotDownAmountDeptRank', label: '排名' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `退款得分`, merge: true, prop: 'mergeField7',
    cols: [
      { istrue: true, width: '100', prop: 'refundAmountMaxScore', label: '上限' },
      { istrue: true, width: '100', prop: 'refundAmountDeductScore', label: '带宽' },
      { istrue: true, width: '100', prop: 'refundAmountMinScore', label: '下限' },
      { istrue: true, width: '100', prop: 'refundAmountRank', label: '排名' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `周转得分`, merge: true, prop: 'mergeField3',
    cols: [
      { istrue: true, width: '100', prop: 'brandTurnoverDayDeptMaxScore', label: '上限' },
      { istrue: true, width: '100', prop: 'brandTurnoverDeptDeductScore', label: '带宽' },
      { istrue: true, width: '100', prop: 'brandTurnoverDayDeptMinScore', label: '下限' },
      { istrue: true, width: '100', prop: 'brandTurnoverDayDeptRank', label: '排名' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `全仓缺货得分`, merge: true, prop: 'mergeField4',
    cols: [
      { istrue: true, width: '100', prop: 'fullWmsMonthLackCountMaxScore', label: '上限' },
      { istrue: true, width: '100', prop: 'fullWmsMonthLackCountDeductScore', label: '带宽' },
      { istrue: true, width: '100', prop: 'fullWmsMonthLackCountMinScore', label: '下限' },
      { istrue: true, width: '100', prop: 'fullWmsMonthLackCountRank', label: '排名' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `本仓缺货得分`, merge: true, prop: 'mergeField5',
    cols: [
      { istrue: true, width: '100', prop: 'inWmsMonthLackCountMaxScore', label: '上限' },
      { istrue: true, width: '100', prop: 'inWmsMonthLackCountDeductScore', label: '带宽' },
      { istrue: true, width: '100', prop: 'inWmsMonthLackCountMinScore', label: '下限' },
      { istrue: true, width: '100', prop: 'inWmsMonthLackCountRank', label: '排名' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `云仓缺货得分`, merge: true, prop: 'mergeField6',
    cols: [
      { istrue: true, width: '100', prop: 'wmsMonthLackCountMaxScore', label: '上限' },
      { istrue: true, width: '100', prop: 'wmsMonthLackCountDeductScore', label: '带宽' },
      { istrue: true, width: '100', prop: 'wmsMonthLackCountMinScore', label: '下限' },
      { istrue: true, width: '100', prop: 'wmsMonthLackCountRank', label: '排名' },
    ]
  },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '操作人', prop: 'operateUserName' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '操作时间', prop: 'operateTime' },
];

export default {
  name: "scoreSetLogDialog",
  components: {
    vxetablebase
  },
  props: {
    rptDate: {
      type: String,
      default: ""
    },
    selectGroup: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    selectGroup: {
      handler(val) {
        if (!val) {
          this.tableCols = tableCols1;
        } else {
          this.tableCols = tableCols2;
        }
      },
      immediate: true, // 立即执行一次
    },
  },
  data() {
    return {
      loading: false,
      tableCols: tableCols1,
      tableData: null,
      filter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'rptDate',
        isAsc: false,
        type: '个人',
      },
    };
  },
  async mounted() {
    await this.getList();
  },
  methods: {
    async getList() {
      this.loading = true;
      if (!this.selectGroup) {
        // const { data, success } = await getProfit3CommissionPercentageScoreSetLog({ ...this.filter, rptDate: this.rptDate });
        const { data, success } = await getProfit3CommissionPercentageScoreSetLog({ ...this.filter });
        this.loading = false;
        if (!success) return;
        this.tableData = data.list;
      } else {
        this.filter.type = "小组";
        // const { data, success } = await getProfit3CommissionPercentageScoreSetLog({ ...this.filter, rptDate: this.rptDate });
        const { data, success } = await getProfit3CommissionPercentageScoreSetLog({ ...this.filter });
        this.loading = false;
        if (!success) return;
        this.tableData = data.list;
      }
    },
    async sortchange({ order, prop }) {
      if (prop) {
        this.filter.orderBy = prop
        this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>
