<template>
  <div style="height: 100%;overflow: auto;">
     <template>
      <el-form class="ad-form-query" :model="filter" @submit.native.prevent label-width="100px">
        <el-form-item label="关键词:">
            <el-input v-model="filter1.keyWords" placeholder="输入关键词，多个关键词用,隔开" style="width:100%"/>
        </el-form-item>
          <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                 <el-form-item label="指标分析:">
                    <el-select v-model="filter1.Y" placeholder="请选择" class="el-select-content">
                        <el-option v-for="item in Ylist" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                  </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="指标值范围:">
                  <el-row>
                      <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-input type="number" placeholder="最小值" v-model="filter1.minY"/>
                      </el-col>
                      <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-input type="number" placeholder="最大值" v-model="filter1.maxY"/>
                      </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
           </el-row>        
        <el-form-item>
          <el-button type="primary" @click="onfresh">刷新</el-button>
        </el-form-item>
      </el-form>
      </template> 
       <div id="echartmonit2-1" style="width: 100%;height: 700px; box-sizing:border-box; line-height: 700px;">
      </div>
       <br>
       <div id="echartmonit2-2" style="width: 100%;height: 700px; box-sizing:border-box; line-height: 700px;">
      </div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
import {productMonitorAnalysis2,getAllJpProducts} from '@/api/operatemanage/operate'
export default {
  name: 'Roles',
  components: { },
   props:{
       filter: { }
     },
  data() {
    return {
      filter1:{
         Y:0
      },
   Ylist:[
          {value:0,unit:"",label:"交易金额"},
          {value:1,unit:"",label:"访客人数"},
          {value:2,unit:"",label:"搜索人数"},
          {value:3,unit:"%",label:"搜索占比"},
          {value:4,unit:"%",label:"支付人数"},
          {value:5,unit:"%",label:"支付转化率"},
          {value:6,unit:"",label:"收藏人数"},
          {value:7,unit:"%",label:"收藏率"},
          {value:8,unit:"",label:"加购人数"},
          {value:9,unit:"%",label:"加购率"},
          {value:10,unit:"",label:"客单价"},
          {value:11,unit:"",label:"uv价值"}
         ],
      pageLoading: false
    }
  },
  mounted() {
  },
  beforeUpdate() {
  },
methods: {
  async onSearch() {
      //if (!this.filter.selfProCode) return;
      if (this.filter.categoryids.length==0){
        this.$message({message: "请先选择类目",type: "warning",});
        return;
      }
      this.getdata1()
      this.getdata2()
    },
  async onfresh() {
      if (this.filter.categoryids.length==0){
        this.$message({message: "请先选择类目",type: "warning",});
        return;
      }
      this.getdata1()
      this.getdata2()
    },
  async getdata1() {
    if (!this.filter.timerange||this.filter.timerange.length<2){
        this.$message({message: "请先选择日期！",type: "warning",});
        return;
      }
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      var parm={...this.filter, ...this.filter1};      
      var hasparm=false;
      var arry= Object.keys(parm)
      if (arry.length==0)  return;
      for (let key of Object.keys(parm)) {
        if(parm[key])
            hasparm=true;
      }
      if(!hasparm) return;
      parm.categoryId=parm.categoryids[parm.categoryids.length-1]
      parm.groupId=parm.groupId
      parm.sort=0
      const res = await productMonitorAnalysis2(parm);
      if (!res?.code)  return;       
      var chartDom = document.getElementById('echartmonit2-1');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      if (!res.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
       }
      var option = this.Getoptions(res.data);
      //console.log(JSON.stringify(option))
      option && myChart.setOption(option); 
    },
    async getdata2() {
    if (!this.filter.timerange||this.filter.timerange.length<2){
        this.$message({message: "请先选择日期！",type: "warning",});
        return;
      }
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      var parm={...this.filter, ...this.filter1};      
      var hasparm=false;
      var arry= Object.keys(parm)
      if (arry.length==0)  return;
      for (let key of Object.keys(parm)) {
        if(parm[key])
            hasparm=true;
      }
      if(!hasparm) return;
      parm.categoryId=parm.categoryids[parm.categoryids.length-1]
      parm.groupId=parm.groupId
      parm.sort=1
      const res = await productMonitorAnalysis2(parm);
      if (!res?.code)  return;       
      var chartDom = document.getElementById('echartmonit2-2');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      if (!res.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
       }
      var option = this.Getoptions(res.data);
      //console.log(JSON.stringify(option))
      option && myChart.setOption(option); 
    },
  Getoptions(element){
     var series=[]
     element.series.forEach(s=>{
       series.push({  smooth: true,...s})
     })
     var yAxis=[{ type: 'value',name: "公司占比",axisLabel: {formatter: '{value}%'}}]
     this.Ylist.forEach(s=>{
       if (s.value==this.filter1.Y)
          yAxis.push({ type: 'value',name: s.label,axisLabel: {formatter: '{value}'+s.unit}})
     })
     var option = {
        title: {text: element.title},
        tooltip: {trigger: 'axis', order:'valueDesc'},
        legend: { 
           //type:'scroll',
          // top:-20,
           formatter: function (name) {
              return echarts.format.truncateText(name, 120, '10px Microsoft Yahei', '...');
            },
            tooltip: {
                show: true
            },
            padding: [0, 80],
            data: element.legend
        },
        grid: {
            left: '3%',
            right: '3%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {feature: {
            magicType: {show: true, type: ['line', 'bar']},
            //restore: {show: true},
        }},
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis: yAxis,
        series:  series
    };
    return option;
   },
  }
}
</script>
<style>
.el-select-content { 
    width: calc(100% - 10px);
    margin: 0;
 }
 
</style>
