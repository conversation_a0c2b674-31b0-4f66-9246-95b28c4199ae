<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker class="publicCss" v-model="ListInfo.yearMonth" type="month" format="yyyyMM"
          value-format="yyyyMM" placeholder="请选择月份" :clearable="false" style="width: 130px;">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.skuCode" placeholder="SKU" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.combinedCoding" placeholder="组合编码" maxlength="50" clearable
          class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
      </div>
    </template>
    <vxetablebase :id="'monthlyWarehousingCostRawData202505221316'"
      :tablekey="'monthlyWarehousingCostRawData202505221316'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'" :border="true">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="35%" v-dialogDrag :close-on-click-modal="false">
      <div class="upload-section">
        <div class="upload-row">
          <label class="required-label">
            <span class="required-mark">*</span> 月份选择：
          </label>
          <el-date-picker class="upload-month" v-model="yearMonth" type="month" format="yyyyMM" value-format="yyyyMM"
            placeholder="请选择月份" :clearable="false" />
        </div>
        <div class="upload-row">
          <el-upload ref="upload" class="upload-area" :auto-upload="false" :multiple="false" :limit="1" action
            accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
            <template #trigger>
              <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button size="small" type="success" :loading="uploadLoading" @click="onSubmitUpload" class="upload-btn">
              {{ uploadLoading ? '上传中' : '上传' }}
            </el-button>
          </el-upload>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { importMonthWarehouseEntryCost, } from '@/api/monthbookkeeper/import'
import { getMonthWarehouseEntryCostPageList, deleteMonthWarehouseEntryCost } from '@/api/monthbookkeeper/financialDetail'
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'yearMonth', label: '年月', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'storageTime', label: '入库时间', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'skuCode', label: 'SKU', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'combinedCoding', label: '组合编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'number', label: '数量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'costPrice', label: '成本价', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'cost', label: '成本', },
  { istrue: true, fixed: 'right',align: 'center', type: "button", label: '操作', width: "120", 
    btnList: 
    [
      { label: "删除", handle: (that, row) => that.delete(row) }
    ] 
  }
]
export default {
  name: "monthlyWarehousingCostRawData",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      yearMonth: null,//导入时间
      dialogVisible: false,//导入弹窗
      fileList: [],//上传文件列表
      uploadLoading: false,//上传按钮loading
      fileparm: {},//上传文件参数
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        yearMonth: dayjs().subtract(1, 'month').format('YYYYMM'),//年月
        skuCode: null,//SKU
        combinedCoding: null,//组合编码
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearMonth", this.yearMonth);
      var res = await importMonthWarehouseEntryCost(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (!this.yearMonth) {
        this.$message({ message: "请选择月份", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //删除
    async delete(row){
      this.$confirm('此操作将删除此月数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const request = {
          YearMonth : row.yearMonth
        }
        deleteMonthWarehouseEntryCost(request).then(res => {
          if (res.success) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      })
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.yearMonth = dayjs().subtract(1, 'month').format('YYYYMM')
      this.dialogVisible = true;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getMonthWarehouseEntryCostPageList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.storageTime = item.storageTime ? dayjs(item.storageTime).format('YYYY-MM-DD') : ''
        })
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 5px;
  }
}

.upload-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 12px 0;
}

.upload-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.required-label {
  font-weight: 500;
  color: #333;
}

.required-mark {
  color: red;
  margin-right: 4px;
}

.upload-month {
  width: 200px;
}

.upload-area {
  display: flex;
  align-items: center;
  gap: 10px;
}

.upload-btn {
  margin-left: 0;
}
</style>
