<template>
    <!-- <my-container> -->
        <div :class="className" :style="{height:height,width:width}" />        
    <!-- </my-container> -->
</template>

<script>
import * as echarts from 'echarts'
import MyContainer from "@/components/my-container";
import { getHomeOrderExpressDeduct } from "@/api/order/orderdeductmoney"

export default {
    name: 'YunhanAdminLineOrderExpress',
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '350px'
        },
        title: {
            type: String,
            default: '快递扣款分析（扣款时间维度）'
        },
        filter:{},
    },
    components:{MyContainer,},
    data() {
        return {
            expressList: [],
            chart: null
        };
    },

    async mounted() {
        //await this.getlistexpresslist()
    window.addEventListener('resize', this.handleResizeChart);
    },
    destroyed () {
    window.removeEventListener('resize', this.handleResizeChart);
  },

    methods: {
        async getlistexpresslist(timeType){
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = {...this.filter, timeType: timeType};

            var res = await getHomeOrderExpressDeduct(params)
            this.expressList = res.data

            await this.initChart()
        },
        initChart() {
            this.chart = echarts.init(this.$el, 'macarons')
            this.setOptions(this.expressList)
        },
        setOptions(element) {
            var series = []
            if ( !element || element?.legend.length <=1 ) return;
            element.series.forEach(s=>{
                series.push({...s,barWidth: 30,  barGap:'0%',})
            })
            var yAxis = []
            
            element.yAxis.forEach(s => {
                if (s.position == 'left') {
                    s.offset = 0;
                }
                yAxis.push({axisLine:{show:true},type: 'value',minInterval:10,offset:s.offset,splitLine:s.splitLine,position:s.position,name: s.name,axisLabel: {formatter: '{value}'+s.unit}})
            }) 
            // barCategoryGap:'30%', 
            // yAxis.splice(1, 0, {});
            // yAxis.splice(1, 0, {});
            // console.log(yAxis,999);
            var selectedLegend={};
            if(element.selectedLegend){
            element.legend.forEach(f=>{
                if(!element.selectedLegend.includes(f)) selectedLegend[f]=false
                })
            }
            var _this = this
            _this.chart.setOption({
                title: {
                    text: _this.title,
                    // left: 'center'
                },
                toolbox: {
                    feature: {
                        //magicType: { show: true, type: ['line', 'bar'] },
                    }
                },
                xAxis: {
                    type: 'category',
                    data: element.xAxis,
                    boundaryGap: true,
                    axisPointer: {
                        type: 'shadow'
                    },
                    axisLabel:{ 
                        // rotate : 30 
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                    type: 'cross',
                    crossStyle: {
                        color: '#999'
                    }
                    }
                },
                grid: {
                    left: '2%',
                    right: 50,
                    bottom: 50,
                    top: '12%',
                    containLabel: true
                },
                legend: {
                    bottom: '5%',
                    data: element.legend
                },
                yAxis:  yAxis,
                series: series
            })
        },
        handleResizeChart () {
            if (this.chart) {
                this.chart.resize();
            }
        }
    },
   
};
</script>

<style lang="scss" scoped>

</style>