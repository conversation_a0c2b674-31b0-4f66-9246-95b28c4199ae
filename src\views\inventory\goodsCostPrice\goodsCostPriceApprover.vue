<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            <el-form-item label="申请人：">
                <el-input v-model.trim="filter.createdUserName" maxlength="100" placeholder="申请人" style="width: 150px"
                    @keyup.enter.native="onSearch" clearable />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onSearch">查询</el-button>
            </el-form-item>
        </el-form>
        </template>

        <!--列表-->
        <vxetablebase :id="'goodsCostPriceChgList202301031318001'" :tableData='list' :tableCols='tableCols'
            :loading='listLoading' :border='true' :that="that" ref="vxetable" @sortchange='sortchange' height="100%">
            <template slot="right">
                <vxe-column title="操作" :field="'col_opratorcol'" width="120" fixed="right">
                    <template #default="{ row }">
                        <template width="auto" v-if="row.approveType == 0">
                            <el-button type="text" :loading="onHandLoading" size="default"
                                @click="approveCost(row, 1)">同意</el-button>
                            <el-button type="text" :loading="onHandLoading" size="default"
                                @click="approveCost(row, 2)">拒绝</el-button>
                        </template>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

    </container>
</template>

<script>
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { getGoodsCostChgInfoReceive, approveCostPriceBrand } from "@/api/inventory/basicgoods"
import {
    getSplitsAsync,
    approveSplit
} from '@/api/inventory/purchaseData'

const tableCols = [
    { istrue: true, prop: 'buyNo', label: '采购单号', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'indexNo', label: 'Erp编号', width: '100', },
    { istrue: true, prop: 'picture', label: '图片', width: '100', type: 'images' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '100', },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: '100', },
    { istrue: true, prop: 'checkDate', label: '变动时间', width: '120', },
    { istrue: true, prop: 'fromPrice', label: '原成本', width: '80', sortable: 'custom', },
    { istrue: true, prop: 'toPrice', label: '新成本', width: '80', sortable: 'custom', },
    { istrue: true, prop: 'diffPrice', label: '差价单价', width: '80', sortable: 'custom', },
    { istrue: true, prop: 'count', label: '采购量', width: '80', sortable: 'custom', },
    { istrue: true, prop: 'diffAmount', label: '差价总额', width: '80', sortable: 'custom', },
    { istrue: true, prop: 'brandName', label: '申请人', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'checkDate', label: '申请时间', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'purImageUrl', label: '申请图片', width: '100', type: 'images' },
    { istrue: true, prop: 'remark', label: '申请备注', width: '100', },
    {
        istrue: true, prop: 'approveType', label: '领取状态', width: '80', sortable: 'custom', formatter: (row) =>
            row.approveType == 0 ? "申请中" : row.approveType == 1 ? "同意" : "驳回"
    },
];

const tableHandles = [];


export default {
    name: 'YunHanAdminGoodsCostPriceApprover',
    components: { cesTable, container, MyConfirmButton, vxetablebase },
    props: {
        filter: {},
    },

    data() {
        return {
            that: this,
            addForm: {
                id: null,
            },
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "checkDate", IsAsc: false },
            summaryarry: {},
            total: 0,
            sels: [],
            pageLoading: false,
            listLoading: false,
            onHandLoading: false,
        };
    },

    async mounted() {

    },

    methods: {
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        async getlist() {
            this.listLoading = true
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }
            this.listLoading = true
            const { data, success } = await getSplitsAsync(params)
            this.listLoading = false
            if (!success) return
            this.total = data.total
            this.list = data.list
            this.summaryarry = data.summary;
        },
        async approveCost(row, num) {
            let para = {};
            para.id = row.id
            para.approveType = num
            var res = await approveSplit(para);
            if (res.success) {
                this.$message.success("操作成功");
                this.getlist();
            } else {
                this.$message.error(res.msg);
            }
        },
        async cellclick(row, column, cell, event) {

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>

<style lang="scss" scoped></style>