<template>
  <el-dialog title="查看提取话术" :visible.sync="isShow" width="50%" :before-close="closeDialog" v-if="isShow" v-dialogDrag >


              <!-- <div style="margin-top: 20px; margin-left: 20px;">
                <div v-for="(chart, index) in chartList" :key="index">
                  <div style="width: 200px; height: 50px;">{{ index + 1 }}. {{ chart.info }}</div>
                </div>
              </div> -->



              <div style="margin-top: 20px; margin-left: 20px;">
                   <div v-if="chartList.length>0">
                        <div style="display: flex; margin-bottom: 20px;" v-for="(chart, index) in chartList" :key="index">
                            <div style="display: inline-block; width: 30px;">{{ index + 1 }}.</div>
                            <div style="display: inline-block;  margin-left: 10px;">{{ chart.info }}</div>
                        </div>
                   </div>
                    <div  v-else style="color:gray;text-align: center;">暂无数据</div>
              </div>


  </el-dialog>
</template>
<script>
import { formatTime } from "@/utils";

import {getQualityChatInfoList} from "@/api/customerservice/chartCheck";


export default {
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      chartTotal: 0,
      isLoading: false,
      chartList:[],
      dataJson:null,
    };
  },
  async mounted(){
     
  },
  watch: {
    isShow(newVal, oldVal) {
      if (newVal) {
        this.getChartList();

      }
    },
},
   methods: {
    async getChartList() {
        var data={orderNo:this.dataJson.orderNo}
        const res=  await getQualityChatInfoList(data);
        console.log(res,'res')
       
        this.chartList = res.data;

        this.isLoading = false;
    },


    closeDialog() {
      this.$emit("closeDialog");
    },
    
 
  },
};
</script>
<style lang="scss" scoped>

//顶部可点击div样式
::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell{
  padding: 15px;
}
::v-deep .el-descriptions__body .el-descriptions__table{
  background-color: rgb(242, 244, 245);
}

::v-deep .el-form-item__label {
  color: #888888 !important; /* 更改为你想要的颜色 */
}
.tipColor{
  color: red;
} 
.violations{
 
  margin-top: 20px;
  margin-left: 20px;
  margin-right: 20px;
  height:200px;
}
.msg_merchant{
  padding: 10px; 
  color: white;
   border-radius: 5px;
  background-color: rgb(64, 158, 255);
}
.msg_consumer{
   padding: 10px;
 border-radius: 5px;
  background-color: rgb(240, 246, 255);
}

.system-box {
  background-color: #fafafa;
  padding: 10px;
  box-sizing: border-box;
  width: 300px;
}

.system {
  display: flex;
  margin-bottom: 4px;
  color: #999;
}

.message-container {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.name {
  text-align: right;
  margin: 5px 0;
  color: #999;
}

.avatar {
  float: right;
  margin-right: 0px;
  /* 修改这里将头像放在消息框的右边 */
  display: flex;
  align-items: center;
  text-align: right
}

.avatar-image {
  width: 400px;
  height: 400px;
  // object-fit: cover;
}

.bubble {
  color: #000;
  border-radius: 5px;
  padding-left: 10px;
}
.bubble2 {
  color: #000;
  border-radius: 5px;
  padding-right: 10px;
}

.message {
  text-align: left;
  margin: 0;
  width: 280px;
}

.message-container-right {
  justify-content: flex-end;
  margin-right: 10px !important;
}

.message-container-left {
  justify-content: flex-start;
}
.merchant{
  color: rgb(88, 170, 255) !important;
  border: 1px rgb(88, 170, 255) solid;
}
.consumer{
  color:red !important;
   border: 1px red solid;
}
.username{
  color: black !important;
  margin-right: 5px;
  margin-left: 5px;
}
.recordTime{
  color: gray !important;
}
  ::v-deep .el-image .el-image__inner {
       max-width: 500px !important;
        max-height: 1000px !important;
        height: auto;
        width: 500px!important;
    }
</style>
