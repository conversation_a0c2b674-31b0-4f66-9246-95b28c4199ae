<template>
    <my-container>
        <template #header>
            <el-form :inline="true" >
                <el-form-item>
                    <el-input v-model="filter.distName" placeholder="请输入分销商名称" maxlength="50" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <span>登记时间 </span>
                    <el-select v-model="startMonth" @change="getWeeksInMonth(startMonth, 'start')" clearable
                        placeholder="请选择" style="width: 80px">
                        <el-option v-for="item in months" :key="item" :label="item" :value="item">
                        </el-option>
                    </el-select>
                    <span> 月第 </span>
                    <el-select v-model="startWeek" clearable placeholder="请选择" style="width: 80px">
                        <el-option v-for="item in startWeeks" :key="item" :label="item" :value="item">
                        </el-option>
                    </el-select>
                    <span> 周 至 </span>
                    <el-select v-model="endMonth" @change="getWeeksInMonth(endMonth, 'end')" clearable placeholder="请选择"
                        style="width: 80px">
                        <el-option v-for="item in months" :key="item" :label="item" :value="item">
                        </el-option>
                    </el-select>
                    <span> 月第 </span>
                    <el-select v-model="endWeek" clearable placeholder="请选择" style="width: 80px">
                        <el-option v-for="item in endWeeks" :key="item" :label="item" :value="item">
                        </el-option>
                    </el-select>
                    <span> 周</span>
                </el-form-item>
                <el-form-item>
                    <span>合作时间 </span>
                    <el-date-picker style="width: 240px" v-model="timerange" :picker-options="pickerOptions"
                        type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                        start-placeholder="开始" end-placeholder="结束" @change="changeTime"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="getList('Search')" v-throttle="1000">搜索</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="getTemplate">下载导入模板</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="ImportDistOrderData" v-throttle="1000">导入</el-button>
                </el-form-item>
            </el-form>
        </template>
        <vxetablebase id="DisOrd2024071301" ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :row-config="{ isHover: true }"
            :showsummary='true' :tablefixed='true' :summaryarry="summary" :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false"  v-loading="loading"
            @cellClick="cellClick" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="120" fixed="right" align="center">
                    <template #default="{ row, $index }">
                        <div style="display: flex; justify-content: center;">
                            <el-button type="text" @click="addRemark(row)">添加备注</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>

        <el-dialog title="导入分销商单量" :visible.sync="importVisible" width="600px" style="height: 300px;" v-dialogDrag>
            <span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" :auto-upload="false" :multiple="false" action accept=".xlsx"
                            :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
        </el-dialog>

        <el-dialog title="趋势图" :visible.sync="buscharDialog.visible" width="70%" v-dialogDrag>
            <span>
                <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data">
                </buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="添加备注" :visible.sync="editVisible" width="30%" v-dialogDrag>
            <el-form>
                <el-form-item>
                    <el-input type="textarea" :rows="5" placeholder="请输入" v-model="disOrdShare.remarks" maxlength="50"
                        show-word-limit></el-input>
                </el-form-item>
                <el-form-item style="text-align: right;">
                    <el-button @click="editVisible = false">取消</el-button>
                    <el-button type="primary" @click="onSubmit" v-throttle="1000">确定</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import { ImportDistOrderFile, getDistOrderData, getLastDistOrderData, upDateDistOrderFile } from "@/api/operatemanage/DistOrderShare";
import { pickerOptions } from '@/utils/tools'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import buschar from '@/components/Bus/buschar'

const tableCols = [
    { istrue: true, prop: 'distName', align: 'center', label: '分销商名称',  },
    { istrue: true, prop: 'regTime', align: 'center', label: '登记时间',  },
    { istrue: true, prop: 'dailyShipments', align: 'center', label: '日均发货单', sortable: 'custom' },
    { istrue: true, prop: 'distOrders', align: 'center', label: '分销订单数',  sortable: 'custom'},
    { istrue: true, prop: 'distAvg', align: 'center', label: '分销日均',  sortable: 'custom', },
    { istrue: true, prop: 'ourRatio', align: 'center', label: '我方占比',  sortable: 'custom', },
    { prop: 'echart', istrue: true, type: 'echarts', chartProp: 'ratioTrend', fix: true, label: '分销订单占比趋势', width: '110' },
    { istrue: true, prop: 'coopTime', align: 'center', label: '合作时间',  sortable: 'custom', },
    { istrue: true, prop: 'remarks', align: 'center', label: '备注',  },
];

export default {
    name: "DistOrderShare",
    components: {
        MyContainer, pickerOptions, vxetablebase, buschar
    },
    data() {
        return {
            that: this,
            filter: {
                orderby: null,
                isAsc: false,
                currentPage: 1,
                pageSize: 50,
                id: null,
                distName: null,
                startCompare: null,
                endCompare: null,
                startCoopTime: null,
                endCoopTime: null,
                remarks: null,
            },
            importVisible: false,
            uploadLoading: false,
            fileList: [],
            startYear: 0,
            endYear: 0,
            months: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
            startMonth: null,
            endMonth: null,
            startWeeks: [],
            startWeek: null,
            endWeeks: [],
            endWeek: null,
            tableCols,
            tableData: [],
            total: null,
            summary: {},
            loading: false,
            pickerOptions,
            timerange: [],
            buscharDialog: { visible: false, title: "", data: {} },
            editVisible: false,
            disOrdShare: {},
        }
    },
    async mounted() {
        this.year = new Date().getFullYear();
        this.filter.orderby = 'eventTime';
        this.filter.isAsc = true;
        await this.getLastList();
    },
    methods: {
        //获取模板
        getTemplate() {
            const aLink = document.createElement("a");
            aLink.href = "/static/excel/operateManage/分销商单量占比导入模板.xlsx";
            aLink.setAttribute('download', '分销商单量占比导入模板.xlsx')
            aLink.click();
        },
        //打开上传弹窗
        ImportDistOrderData() {
            this.importVisible = true;
            this.uploadLoading = false;
            this.$refs.upload.clearFiles();
            this.fileList.splice(0, 1);
        },
        //上传文件
        async uploadFile(item) {
            const form = new FormData();
            form.append("upfile", item.file);
            const res = ImportDistOrderFile(form);
            this.$message({ message: '上传成功,正在导入中...', type: "success" });
        },
        //更改上传文件
        async uploadChange(file, fileList) {
            if (fileList.length == 2) {
                fileList.splice(1, 1);
                this.$message({ message: "只允许单文件导入", type: "warning" });
                return false;
            }
            this.fileList.push(file);
        },
        //移除上传文件
        uploadRemove(file, fileList) {
            this.fileList.splice(0, 1);
        },
        //提交上传文件
        submitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return;
            }
            this.$refs.upload.submit();
            this.$refs.upload.clearFiles();
            this.fileList.splice(0, 1);
            this.importVisible = false;
        },
        cellClick({ row, column }) {
            if (column?.field === 'echart') {
                const { ratioTrend } = row;
                const series = ratioTrend.series[0];
                Object.assign(series, {
                    type: "line",
                    yAxisIndex: 0,
                    backColor: null,
                    stack: null,
                    itemStyle: {
                        normal: {
                            label: {
                                show: true,
                                position: "top",
                                textStyle: {
                                    fontSize: 14
                                }
                            }
                        }
                    },
                    emphasis: {
                        focus: "series"
                    },
                    smooth: true
                });
                delete series.lineStyle;
                ratioTrend.yAxis = [{ position: "left", unit: "", name: "", offset: 0 }];
                this.buscharDialog = {
                    visible: true,
                    data: ratioTrend
                };
                this.$nextTick(() => {
                    this.$refs.buschar.initcharts();
                });
            }
        },
        //获取月份对应的周数
        getWeeksInMonth(month, type) {
            // 注意：JavaScript中的月份是从0开始计数的，所以1代表二月，以此类推。
            // 因此，我们需要将月份减1来使用。
            //计算月份
            month -= 1;

            // 获取该月的第一天是星期几
            const firstDayOfMonth = new Date(this.year, month, 1);
            const dayOfWeek = firstDayOfMonth.getDay();
            // 计算该月总天数
            const daysInMonth = new Date(this.year, month + 1, 0).getDate();
            // 计算第一个周二是哪天
            const firstTuesday = (dayOfWeek === 2) ? 1 : ((7 - dayOfWeek + 2) % 7 + 1);
            // 计算该月有几个周二
            const tuesdaysCount = Math.floor((daysInMonth - firstTuesday) / 7) + 1;

            if (type == "start") {
                //清除周数
                this.startWeek = null;
                this.startWeeks = [];
                for (let index = 1; index <= tuesdaysCount; index++) {
                    this.startWeeks.push(index);
                }
            } else {
                this.endWeek = null;
                this.endWeeks = [];
                for (let index = 1; index <= tuesdaysCount; index++) {
                    this.endWeeks.push(index);
                }
            }
        },
        //获取最近一周的列表
        async getLastList() {
            const { success, data } = await getLastDistOrderData();
            if (success) {
                this.total = data.total;
                this.tableData = data.list;
                this.summary = data.summary;
                this.summary.distName_sum = "合计";
                this.summary.ourRatio_sum = this.summary.distAvg_sum==0?0:((this.summary.distAvg_sum/this.summary.dailyShipments_sum)*100.000).toFixed(3) + "%";
                this.$refs.table.loadRowEcharts();//加载echarts
                this.startMonth = data.extData.lastMonth == null ? 1 : data.extData.lastMonth;
                this.endMonth = data.extData.lastMonth == null ? 1 : data.extData.lastMonth;
                this.getWeeksInMonth(this.startMonth, "start");
                this.getWeeksInMonth(this.endMonth, "end");
                this.startWeek = data.extData.lastWeek == null ? 1 : data.extData.lastWeek;
                this.endWeek = data.extData.lastWeek == null ? 1 : data.extData.lastWeek;
            } else {
                this.$message.error("发生错误，列表获取失败");
            }
        },
        //获取列表
        async getList(type) {
            if (type == "Search") {
                this.filter.currentPage = 1;
            }
            //判断开始登记月份
            if (this.startMonth != null && this.startMonth != "") {
                //判断月份是否大于10
                if (this.startMonth < 10) {
                    //判断开始登记周数
                    if (this.startWeek != null && this.startWeek != "") {
                        this.filter.startCompare = `${this.year}${'0'}${this.startMonth}${'0'}${this.startWeek}`;
                    } else {
                        this.filter.startCompare = `${this.year}${'0'}${this.startMonth}${'00'}`;
                    }
                } else {
                    //判断开始登记周数
                    if (this.startWeek != null && this.startWeek != "") {
                        this.filter.startCompare = `${this.year}${this.startMonth}${'0'}${this.startWeek}`;
                    } else {
                        this.filter.startCompare = `${this.year}${this.startMonth}${'00'}`;
                    }
                }
            } else {
                this.filter.startCompare = `${this.year}${'00'}${'00'}`;
            }
            if (this.endMonth != null && this.endMonth != "") {
                //判断月份是否大于10
                if (this.endMonth < 10) {
                    //判断开始登记周数
                    if (this.endWeek != null && this.endWeek != "") {
                        this.filter.endCompare = `${this.year}${'0'}${this.endMonth}${'0'}${this.endWeek}`;
                    } else {
                        this.filter.endCompare = `${this.year}${'0'}${this.endMonth}${'00'}`;
                    }
                } else {
                    //判断开始登记周数
                    if (this.endWeek != null && this.endWeek != "") {
                        this.filter.endCompare = `${this.year}${this.endMonth}${'0'}${this.endWeek}`;
                    } else {
                        this.filter.endCompare = `${this.year}${this.endMonth}${'00'}`;
                    }
                }
            } else {
                this.filter.endCompare = `${this.year}${'12'}${'06'}`;
            }
            const { success, data } = await getDistOrderData(this.filter);
            if (success) {
                this.total = data.total;
                this.tableData = data.list;
                this.summary = data.summary;
                this.summary.distName_sum = "合计";
                this.summary.ourRatio_sum = this.summary.distAvg_sum==0?0:((this.summary.distAvg_sum/this.summary.dailyShipments_sum)*100.000).toFixed(3) + "%";
                this.$refs.table.loadRowEcharts();//加载echarts
            } else {
                this.$message.error("发生错误，列表获取失败");
            }
        },
        //改变排序
        async sortchange(column) {
            this.filter.orderBy = column.prop
            this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false
            await this.getList();
        },
        changeTime(e) {
            this.filter.startCoopTime = e ? e[0] : null
            this.filter.endCoopTime = e ? e[1] : null
        },
        //每页数量改变
        Sizechange(val) {
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.filter.currentPage = val;
            this.getList()
        },
        addRemark(row) {
            this.disOrdShare = this.clone(row);
            this.editVisible = true;
        },
        async onSubmit() {
            const { success } = await upDateDistOrderFile(this.disOrdShare);
            if (success) {
                this.editVisible = false;
                this.disOrdShare = {};
                await this.getList();
            } else {
                this.$message.error("添加备注失败，请稍后重试");
            }
        },
        clone(obj) {
            if (typeof obj !== 'object' || obj === null) {
                return obj;
            }

            let newObj;
            if (Array.isArray(obj)) {
                newObj = [];
                for (let i = 0; i < obj.length; i++) {
                    newObj.push(this.clone(obj[i]));
                }
            } else {
                newObj = {};
                for (let key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        newObj[key] = this.clone(obj[key]);
                    }
                }
            }
            return newObj;
        }
    }
}
</script>