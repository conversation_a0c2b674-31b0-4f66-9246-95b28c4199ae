<template>
    <div class="dashboard-editor-container">
      <div style="display: flex;justify-content: end;gap: 10px;margin: 5px 0;">
        <el-date-picker style="width: 205px;margin-left:5px; margin-top: 5px;" v-model="timerange" type="daterange"
            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" :clearable="false" @change="handoverEvent()">
        </el-date-picker>
        <el-select v-model="isIgnoreSpecialProCode" placeholder="请选择" style="width: 205px;margin-left:5px; margin-top: 5px;" filterable @change="handoverEvent()">
          <el-option v-for="item in hearlist" :key="item.label" :label="item.label" :value="item.value" />
        </el-select>
      </div>

        <icon-index class="indexnew" ref="iconIndexRef" @handleSetLineChartData="handleSetLineChartData"
            :day-sum="daySumList" @getChatInfos="getChatInfos" :groupList="groupList"></icon-index>

        <el-row :gutter="15">
            <el-col :xs="24" :sm="24" :lg="18">
                <el-row>
                    <div style="padding: 10px 1px 0;">
                        <icon-indexs class="indexnew" :day-sum="daySumList"
                            @handleSetLineChartData="handleSetLineChartData"></icon-indexs>
                   </div>
                </el-row>
                <el-row :gutter="15">
                    <el-col :xs="24" :sm="24" :lg="16">
                        <div class="chart-wrapper11">
                            <div v-if="checkPermission('homepermission')" class="chart-wrapper">
                                <el-form class="ad-form-query">
                                    <el-row>
                                        <el-col :span="24">
                                            <el-form-item>
                                                <div
                                                    style="display: flex; flex-direction: row; width: 100%;justify-content: space-between;">
                                                    <!-- <el-date-picker style="width: 260px;float: left;"
                                                        v-model="filterchartPay.timerange" type="daterange"
                                                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                                                        start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false"
                                                        @change="getChatInfos()"></el-date-picker> -->
                                                    <div
                                                        style="display: flex; flex-direction: row; color: red; font-size: 10px;">
                                                        <span
                                                            :style="daySumList?.confirmationTypeTao?.indexOf('已确认') > -1 ? 'color: #3BA272;' : 'color:red; '">
                                                            &nbsp;&nbsp;{{ daySumList.confirmationTypeTao }}
                                                        </span>
                                                        <span
                                                            :style="daySumList?.confirmationTypeTian?.indexOf('已确认') > -1 ? 'color: #3BA272;' : 'color:red; '">
                                                            &nbsp;&nbsp;{{ daySumList.confirmationTypeTian }}
                                                        </span>
                                                        <span
                                                            :style="daySumList?.confirmationTypePDD?.indexOf('已确认') > -1 ? 'color: #3BA272;' : 'color:red;'">
                                                            &nbsp;&nbsp;{{ daySumList.confirmationTypePDD }}
                                                        </span>
                                                        <span
                                                            :style="daySumList?.confirmationTypeTGC?.indexOf('已确认') > -1 ? 'color: #3BA272;' : 'color:red;'">
                                                            &nbsp;&nbsp;{{ daySumList.confirmationTypeTGC }}
                                                        </span>
                                                        <span
                                                            :style="daySumList?.confirmationTypeJD?.indexOf('已确认') > -1 ? 'color: #3BA272;' : 'color:red;'">
                                                            &nbsp;&nbsp;{{ daySumList.confirmationTypeJD }}
                                                        </span>
                                                        <span
                                                            :style="daySumList?.confirmationTypeDY?.indexOf('已确认') > -1 ? 'color: #3BA272;' : 'color:red;'">
                                                            &nbsp;&nbsp;{{ daySumList.confirmationTypeDY }}
                                                        </span>
                                                        <span
                                                            :style="daySumList?.confirmationTypeSuNing?.indexOf('已确认') > -1 ? 'color: #3BA272; ' : 'color:red;'">
                                                            &nbsp;&nbsp;{{ daySumList.confirmationTypeSuNing }}
                                                        </span>
                                                        <span
                                                        :style="daySumList?.confirmationTypeFenXiao?.indexOf('已确认') > -1 ? 'color: #3BA272; ' : 'color:red;'">
                                                        &nbsp;&nbsp;{{ daySumList.confirmationTypeFenXiao }}
                                                    </span>

                                                    <!-- <span
                                                    :style="daySumList?.confirmationTypeSheIn?.indexOf('已确认') > -1 ? 'color: #3BA272; ' : 'color:red;'">
                                                    &nbsp;&nbsp;{{ daySumList.confirmationTypeSheIn }}
                                                </span> -->
                                                    </div>
                                                    <div style="display: flex; flex-direction: row; margin-left: 10px;">
                                                        <el-button type="text" @click="showxinzi" style="margin-right: 10px;">薪资</el-button>
                                                        <el-select style="width: 50px;margin-right:5px;"
                                                            v-model="filterchartPay.type" @change="getChatInfos()">
                                                            <el-option label="日" value="日" />
                                                            <el-option label="周" value="周" />
                                                            <el-option label="月" value="月" />
                                                        </el-select>
                                                    </div>
                                                </div>
                                                <div style="display: flex; flex-direction: row; width: 100%;justify-content: space-between;">
                                                  <div style="display: flex; flex-direction: row; color: red; font-size: 10px;">
                                                    <span :style="daySumList?.confirmationTypeAlibaba?.indexOf('已确认') > -1 ? 'color: #3BA272;' : 'color:red;'">
                                                      &nbsp;&nbsp;{{ daySumList.confirmationTypeAlibaba }}
                                                    </span>
                                                    <span
                                                        :style="daySumList?.confirmationTypeDYGX?.indexOf('已确认') > -1 ? 'color: #3BA272; ' : 'color:red;'">
                                                        &nbsp;&nbsp;{{ daySumList.confirmationTypeDYGX }}
                                                    </span>
                                                    <span
                                                        :style="daySumList?.confirmationTypeKuaiShop?.indexOf('已确认') > -1 ? 'color: #3BA272; ' : 'color:red;'">
                                                        &nbsp;&nbsp;{{ daySumList.confirmationTypeKuaiShop }}
                                                    </span>
                                                    <span
                                                        :style="daySumList?.confirmationTypeWeChat?.indexOf('已确认') > -1 ? 'color: #3BA272; ' : 'color:red;'">
                                                        &nbsp;&nbsp;{{ daySumList.confirmationTypeWeChat }}
                                                    </span>
                                                    <span
                                                        :style="daySumList?.confirmationTypeRedBook?.indexOf('已确认') > -1 ? 'color: #3BA272; ' : 'color:red;'">
                                                        &nbsp;&nbsp;{{ daySumList.confirmationTypeRedBook }}
                                                    </span>
                                                  </div>
                                                </div>

                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                                <line-chart  gridbottom="160" height="372px" :chart-data="lineChartData" :areaStyle="{}" />
                            </div>
                        </div>
                    </el-col>
                    <el-col :xs="24" :sm="24" :lg="8">
                        <div class="chart-wrapper11">

                            <div v-if="checkPermission('homepermission')" class="chart-wrapper">
                                <!-- <span>
                                    <template>
                                        <el-form class="ad-form-query" :model="filterdetail" @submit.native.prevent>
                                            <el-row>
                                                <el-col :xs="24" :sm="24" :lg="24">
                                                    <el-form-item>
                                                        <el-date-picker style="width: 210px"
                                                            v-model="filterdetail.timerange" type="daterange"
                                                            format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                                            range-separator="至" start-placeholder="开始日期"
                                                            end-placeholder="结束日期" :clearable="false"
                                                            @change="getPie()"></el-date-picker>
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>
                                        </el-form>
                                    </template>
                                </span> -->
                                <span>
                                    <pie-chart :pie-data="pieList" height="430px" />
                                </span>

                            </div>
                        </div>
                    </el-col>
                </el-row>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="6">
                <div class="chart-wrapper11">
                    <div class="chart-wrapper" style="height: 550px;">
                        <div style="padding-top:5px;">
                            <span style="margin-left: 10px;">财务确认记录</span>
                            <!-- <el-date-picker style="width: 210px;float: right;" v-model="dailyreportDate" type="daterange" format="yyyy-MM-dd"
                                                value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                                :clearable="false" @change="getDailyReportConfirmList()"></el-date-picker> -->
                        </div>
                        <div class="timecard">
                            <el-timeline>
                                <el-timeline-item v-for="item in dailyReportConfirmList" :key="item.id" placement="top"
                                    type="primary" style="font-size: 12px; ">
                                    <span style="margin-top:20px">{{ item.createTime }}{{ (item.confirmUserName + '已核对' +
                                        '[' + item.dailyReportDate.substr(0, 10) + ']' +
                                        item.dailyReportType) }}</span>
                                    <br>
                                    <!-- <span>{{item.createTime}}</span> -->
                                </el-timeline-item>
                            </el-timeline>
                        </div>
                        <!-- <div style="margin: 1px 10px 0;font-size:20px">
                            <span>版本升级记录</span>
                        </div>
                        <div class="timecard">
                            <el-timeline>
                                <el-timeline-item v-for="item in list" :key="item.id" @click.native="clicktimelist(item)" placement="top" type="primary">
                                    <el-link :underline="false" style="margin-top:-5%">{{item.title}}</el-link>
                                </el-timeline-item>
                            </el-timeline>
                        </div> -->
                    </div>
                </div>
            </el-col>
        </el-row>
        <!-- <el-row :gutter="15">
            <el-col :xs="24" :sm="24" :lg="24">
                <div class="chart-wrapper44">
                    <div v-if="checkPermission('homepermission')" class="chart-wrapper441">
                        <el-radio-group v-model="isTrue1" @change="getExpress()">
                            <el-radio-button label="1">金额</el-radio-button>
                            <el-radio-button label="2">单量</el-radio-button>
                        </el-radio-group>
                        <el-row :gutter="15">
                            <span>

                            </span>
                            <span>
                                <el-col :xs="24" :sm="24" :lg="6">
                                    <pie-all-chart :pie-data="pieWihholdList" :height="pieWihhold" />
                                </el-col>
                                <el-col :xs="24" :sm="24" :lg="6">
                                    <pie-all-chart :pie-data="pieDeptList" :title="piealltiele" :height="pieWihhold" />
                                </el-col>
                                <el-col :xs="24" :sm="24" :lg="6">
                                    <pie-all-chart :pie-data="ExpressPie" :title="piealltiele2" :height="pieWihhold" />
                                </el-col>
                                <el-col :xs="24" :sm="24" :lg="6">
                                    <pie-all-chart :pie-data="PlatformPie" :title="piealltiele1" :height="pieWihhold" />
                                </el-col>
                            </span>
                        </el-row>

                    </div>
                </div>
            </el-col>
        </el-row> -->

        <el-row :gutter="15">
            <el-col :xs="24" :sm="24" :lg="12">
                <div class="chart-wrapper22">
                    <div class="chart-wrapper332">
                        <line-order-express ref="LineOrderExpress1" :filter="filterdetail" height="500px"
                            :title="LineOrderExpresstitle1" />
                    </div>
                </div>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="12">
                <div class="chart-wrapper22">
                    <div v-if="checkPermission('homepermission')" class="chart-wrapper332">
                        <line-order-express ref="LineOrderExpress2" :filter="filterdetail" height="500px"
                            :title="LineOrderExpresstitle2" />
                    </div>
                </div>
            </el-col>
        </el-row>
        <!-- <el-row :gutter="15">
            <el-col :xs="24" :sm="24" :lg="24">
                <div class="chart-wrapper22">
                    <div v-if="checkPermission('homepermission')" class="chart-wrapper332">
                        <line-order-express ref="LineOrderExpress2" :filter="filterdetail"
                            :title="LineOrderExpresstitle2" />
                    </div>
                </div>
            </el-col>
        </el-row> -->
        <el-row :gutter="15">
            <el-col :xs="24" :sm="24" :lg="24">
                <div class="chart-wrapper22">
                    <div v-if="checkPermission('homepermission')" class="chart-wrapper332">
                        <daycashredanalysis ref="daycashredanalysis" :height="daycashredheight"></daycashredanalysis>
                    </div>
                </div>
            </el-col>
        </el-row>

        <el-row :gutter="15">
            <el-col :xs="24" :sm="24" :lg="14">
                <div class="chart-wrapper22" style="height: 30px;padding-bottom: 0px;margin-bottom: 0px;">
                    <div v-if="checkPermission('homepermission')" class="chart-wrapper332"
                        style="height:30px; padding-bottom: 0px;">
                        <el-radio-group v-model="isTuikuan" @change="getTuikuanList()">
                            <el-radio-button label="1">售前退款</el-radio-button>
                            <el-radio-button label="3">售前退款（排除30分钟内订单）</el-radio-button>
                            <el-radio-button label="2">售后退款</el-radio-button>
                        </el-radio-group>
                    </div>
                </div>
                <div class="chart-wrapper22" style=" padding-top: 0px;">
                    <div v-if="checkPermission('homepermission')" class="chart-wrapper332"
                        style="border-radius: 0 0 10px 10px;">
                        <tuikuan-table :tuikuanList="tuikuanList" :sortchange="sortchange" />
                    </div>
                </div>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="10">
                <div class="chart-wrapper22" style="height:510px">
                    <div v-if="checkPermission('homepermission')" class="chart-wrapper332" style="height: 100%;">
                        <stockout-table :stockout-list="stockoutlist" />
                    </div>
                </div>
            </el-col>
        </el-row>
        <el-row :gutter="15">
            <el-col :xs="24" :sm="24" :lg="24">
                <div class="chart-wrapper22">
                    <div v-if="checkPermission('homepermission')" class="chart-wrapper332">
                        <template>
                            <span>
                                <el-form class="ad-form-query" :model="filterInventory" @submit.native.prevent>
                                    <el-row>
                                        <el-col :xs="24" :sm="3" :lg="3">
                                            <el-form-item>
                                                <el-date-picker style="width: 210px" v-model="filterInventory.timerange"
                                                    :picker-options="pickerOptions" type="daterange" format="yyyy-MM-dd"
                                                    value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                                                    end-placeholder="结束日期" :clearable="false"
                                                    @change="getInventory()"></el-date-picker>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :xs="24" :sm="3" :lg="3">
                                            <el-radio-group v-model="isTrue" @change="getInventory()">
                                                <el-radio-button label="1">在维护</el-radio-button>
                                                <el-radio-button label="2">所有编码</el-radio-button>
                                                <el-radio-button label="3">滞销</el-radio-button>
                                            </el-radio-group>
                                        </el-col>
                                        <span style="margin-left: 20px;color: red;"> 从2024-03-29当日起，新计算规则已生效 </span>
                                    </el-row>
                                </el-form>
                            </span>

                        </template>

                        <line-chart height="450px" :chart-data="InventoryAnalyse" :smooth="true" />
                    </div>
                </div>
            </el-col>

        </el-row>

        <!-- //薪资 -->
        <el-dialog title="趋势图" width="90%" size="380px"
        :visible.sync="showchartotal" v-dialogDrag>
            <div style="width: 100%; height: 28px; margin-bottom: 20px; direction: row;box-sizing: border-box;padding-left: 20px; display: flex;">

                <el-date-picker
                v-model="startQueryTimeArr"
                type="daterange"
                clearable
                :value-format="'yyyy-MM-dd'"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
                </el-date-picker>

                <el-select v-model="busfilter.regionId" placeholder="区域" clearable>
                    <el-option
                    v-for="item in chartarr.quyuoptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                </el-select>

                <el-select v-model="busfilter.queryDeptIds" placeholder="部门" clearable>
                    <el-option
                    v-for="(item,index) in chartarr.bumenoptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                </el-select>

                <el-select v-model="busfilter.manageLevel" placeholder="管理级" clearable>
                    <el-option
                    v-for="item in chartarr.guanlioptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                </el-select>



                <el-button style="width:80px; margin-left: 20px;" type="primary" icon="ele-Search"
                    @click="totalNumberTrendChartfuc()"> 查询
                </el-button>
                <el-button icon="ele-Refresh" @click="onReset()" style="margin-left: 8px"> 重置
                </el-button>

                <el-tooltip style="margin-left: auto;" class="item" effect="dark" :content="'隐藏显示趋势图上数据'" placement="top">
                    <el-switch v-model="switchactivenum" @change="switchactive"
                        inactive-text="隐藏数据" :active-value="true" :inactive-value="false"
                        active-text="显示数据">
                    </el-switch>
                </el-tooltip>
            </div>
            <yhcharts  ref="refyhabcharts"
            :protitle="'chuqin'"
            v-if="showchartotaldata"
            :analysisData="totalchardata"
            ></yhcharts>
        </el-dialog>


        <el-dialog :visible.sync="dialodayssisVisible" ref="elDialog" width="75%" v-dialogDrag :fullscreen="isfull"
            :lock-scroll='false' :center="true" :show-close="false">
            <div class="updatediv">
                <updateindex ref="updateindex" class="updateindex"></updateindex>
            </div>
        </el-dialog>

        <el-dialog :visible.sync="dialoganalysisVisible" width="80%" v-dialogDrag :show-close="false">

            <div>


                <el-date-picker style="width: 260px;margin-left: 100px; margin-bottom: 20px;"
                    v-model="filterchart2.timerange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                    range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>

                <el-button type="primary" style="margin-left: 10px;" @click="onSearchChart">刷新</el-button>
                <!-- 彭世昌暂注释-发包前记得取消注释
                <span style="margin-left: 15px; color: red;">{{ daySumList.confirmationDeductionTypeTX }}</span>
                <span style="margin-left: 15px; color: red;">{{ daySumList.confirmationDeductionTypePDD }}</span>
                <span style="margin-left: 15px; color: red;">{{ daySumList.confirmationDeductionTypeTGC }}</span> -->


            </div>
            <div>
                <el-alert title="温馨提示：平台扣款数据趋势图中的拼多多数据只取拼多多扣款中：延迟发货，虚假发货，虚假轨迹，缺货" type="error" :closable="false"
                    style="margin-bottom:10px;">
                </el-alert>
                <!-- 所有平台扣款汇总数据趋势图 -->
                <span>
                    平台扣款数据趋势图
                    <orderIllAnalysis ref="orderIllAnalysis" style="height: 550px"></orderIllAnalysis>
                </span>
            </div>


            <!-- 淘系扣款原因趋势图 -->
            <div>

                <span>
                    淘系扣款原因趋势
                    <buschar v-if="TxbuscharDialog.visible" ref="Txbuschar" :analysisData="TxbuscharDialog.data"></buschar>
                </span>
            </div>
            <!-- 拼多多扣款原因趋势图 -->
            <div>

                <span>
                    拼多多扣款原因趋势
                    <buschar v-if="PddbuscharDialog.visible" ref="Pddbuschar" :analysisData="PddbuscharDialog.data">
                    </buschar>
                </span>
            </div>

        </el-dialog>

        <!-- 退款趋势 -->
        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <template>
                    <el-form class="ad-form-query" :model="filterchart" @submit.native.prevent label-width="100px">
                        <el-row>
                            <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                                <el-form-item label="日期:">
                                    <el-date-picker style="width: 260px" v-model="filterchart.timerange" type="daterange"
                                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                                        start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false"></el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                                <el-form-item>
                                    <el-button type="primary" @click="getecharts">刷新</el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </template>
            </span>
            <span>
                <buschar v-if="buscharDialog.visible" ref="RefundBuschar" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <!-- 客单价趋势 -->
        <el-dialog :title="buscharDialog1.title" :visible.sync="buscharDialog1.visible" width="80%" v-dialogDrag>
            <span>
                <template>
                    <el-form class="ad-form-query" :model="filterchart" @submit.native.prevent label-width="100px">
                        <el-row>
                            <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                                <el-form-item label="日期:">
                                    <el-date-picker style="width: 260px" v-model="filterchart.timerange" type="daterange"
                                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                                        start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false"></el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                                <el-form-item>
                                    <el-button type="primary" @click="geteunitpricecharts">刷新</el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </template>
            </span>
            <span>
                <buschar v-if="buscharDialog1.visible" ref="buschar" :analysisData="buscharDialog1.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog1.visible = false">关闭</el-button>
            </span>
        </el-dialog>
        <!-- 新品拍摄统计图 -->
        <el-row :gutter="15">
            <el-col :xs="24" :sm="24" :lg="12">
                <div class="chart-wrapper11">
                    <div v-if="checkPermission('homepermission')" class="chart-wrapper">
                        <el-form class="ad-form-query">
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item>
                                        <div
                                            style="display: flex; flex-direction: row; width: 100%;justify-content: space-between;padding:10px 0 0 10px">
                                            <div>
                                                <el-date-picker style="width: 260px;float: left;"
                                                    v-model="filtershoot.timerange" type="daterange" format="yyyy-MM-dd"
                                                    value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                                                    end-placeholder="结束日期" :clearable="false"
                                                    @change="getShootData()"></el-date-picker>
                                                <el-button style="padding: 4px;margin: 0;">
                                                    <el-checkbox-group v-model="filtershoot.groupList"
                                                        @change="groupchange">
                                                        <el-checkbox label="bytask" checked="checked">按任务</el-checkbox>
                                                        <el-checkbox label="byday">按天</el-checkbox>
                                                    </el-checkbox-group>
                                                </el-button>
                                            </div>
                                            <span
                                                style="margin-right: 20px;font-weight: bold; font-size: 18px;color: #333333;font-family: PingFang SC;">新品拍摄</span>
                                        </div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                        <buscharr v-if="shootbuscharDialog.visible" :toolbox="toolbox"
                            :analysisData="shootbuscharDialog.data" :legendPistion="{ bottom: '1%' }"
                            :thisStyle="thisStyleView5" :gridStyle="gridStyleView5">
                        </buscharr>
                    </div>
                </div>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="12">
                <div class="chart-wrapper11">
                    <div v-if="checkPermission('homepermission')" class="chart-wrapper">
                        <span>
                            <template>
                                <el-form class="ad-form-query">
                                    <el-row>
                                        <el-col :xs="24" :sm="24" :lg="24">
                                            <el-form-item>
                                                <div
                                                    style="display: flex; flex-direction: row; width: 100%;justify-content: space-between;padding:10px 10px 0 10px">
                                                    <div>
                                                        <el-date-picker style="width: 210px"
                                                            v-model="filterpackages.timerange" type="daterange"
                                                            format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                                            range-separator="至" start-placeholder="开始日期"
                                                            end-placeholder="结束日期" :clearable="false"
                                                            @change="getPackagesData()"></el-date-picker>
                                                        <el-button style="padding: 4px;margin: 0;">
                                                            <el-checkbox-group v-model="filterpackages.groupList"
                                                                @change="groupchangepackages">
                                                                <!-- <el-checkbox label="bytk" checked="checked">按任务</el-checkbox> -->
                                                                <el-checkbox label="bydy">按天</el-checkbox>
                                                            </el-checkbox-group>
                                                        </el-button>
                                                    </div>
                                                    <span
                                                        style="margin-right: 20px;font-weight: bold; font-size: 18px;color: #333333;font-family: PingFang SC;">包装加工</span>
                                                </div>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                            </template>
                        </span>
                        <span>
                            <buscharr v-if="packagesbuscharDialog.visible" :toolbox="toolbox"
                                :analysisData="packagesbuscharDialog.data" :thisStyle="thisStyleView5"
                                :gridStyle="gridStyleView5">
                            </buscharr>
                        </span>

                    </div>
                </div>
            </el-col>
        </el-row>

        <!-- 仓库薪资统计 -->
        <el-row :gutter="15">
            <el-col :xs="24" :sm="24" :lg="24">
                <div class="chart-wrapper557">
                    <div v-if="checkPermission('homepermission')" class="chart-wrapper558">
                        <span>
                            <el-form class="ad-form-query" :model="filterwarehouseWages" @submit.native.prevent
                                :inline="true">
                                <el-form-item style="padding: 0;margin: 0;">
                                    <el-date-picker style="width: 210px" v-model="filterwarehouseWages.timerange"
                                        :picker-options="pickerOptions" type="daterange" format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                                        end-placeholder="结束日期" @change="showchartWarehouseWages"
                                        :clearable="false"></el-date-picker>
                                </el-form-item>
                                <el-form-item style="padding: 0;margin: 0;">
                                    <el-radio-group v-model="filterwarehouseWages.wareDataType"
                                        @input="onWareDataTypeChange">
                                        <el-radio-button label="分仓" name="wareDataType">分仓</el-radio-button>
                                        <el-radio-button label="各仓对比" name="wareDataType">各仓对比</el-radio-button>
                                    </el-radio-group>
                                </el-form-item>
                                <el-form-item style="padding: 0;margin: 0;"
                                    v-if="(filterwarehouseWages.wareDataType == '分仓')">
                                    <el-select v-model="filterwarehouseWages.warehouseCode" style="width: 200px" size="mini"
                                        @change="showchartWarehouseWages" placeholder="仓库" clearable>
                                        <el-option v-for="item in warehouseWagesWarehouseList" :key="item.value"
                                            :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item style="padding: 0;margin: 0;"
                                    v-if="(filterwarehouseWages.wareDataType == '各仓对比')">
                                    <el-select v-model="filterwarehouseWages.lineName" style="width: 110px"
                                        @change="showchartWarehouseWages" size="mini" clearable filterable placeholder="线条">
                                        <el-option label="出勤数量" value="出勤数量" />
                                        <el-option label="仓发件量" value="仓发件量" />
                                        <el-option label="工资总和" value="工资总和" />
                                        <el-option label="出仓成本" value="出仓成本" />
                                        <el-option label="计件计时比(人数)" value="计件计时比(人数)" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item style="padding: 0;margin: 0;">
                                    <el-select v-model="filterwarehouseWages.workType" style="width: 80px" size="mini"
                                        @change="showchartWarehouseWages" clearable placeholder="班次">
                                        <el-option label="白班" value="白班"></el-option>
                                        <el-option label="晚班" value="晚班"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item style="padding: 0;margin: 0;">
                                    <el-select v-model="filterwarehouseWages.postName" style="width: 110px"
                                        @change="showchartWarehouseWages" size="mini" clearable filterable
                                        placeholder="一级岗位">
                                        <el-option v-for="item in warehouseWagesOnePostNameList" :key="item.value"
                                            :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item style="padding: 0;margin: 0;">
                                    <el-input v-model.trim="filterwarehouseWages.workItem" placeholder="二级岗位名称"
                                        style="width:110px;" clearable maxlength="20" @change="showchartWarehouseWages" />
                                </el-form-item>
                                <el-form-item style="padding: 0;margin: 0;">
                                    <el-select v-model="filterwarehouseWages.showType" style="width: 100px" size="mini"
                                        @change="showchartWarehouseWages" clearable placeholder="计件计时">
                                        <el-option label="计件" value="计件"></el-option>
                                        <el-option label="计时" value="计时"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item style="padding: 0;margin: 0;">
                                    <el-select v-model="filterwarehouseWages.userPostType" style="width: 100px" size="mini"
                                        @change="showchartWarehouseWages" clearable placeholder="员工岗位类型">
                                        <el-option label="普通员工" value="普通员工"></el-option>
                                        <el-option label="管理员" value="管理员"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item style="padding: 0;margin-left: 100px;">
                                    <span
                                        style="margin-right: 20px;font-weight: bold; font-size: 18px;color: #333333;font-family: PingFang SC;">
                                        仓库薪资
                                    </span>
                                </el-form-item>
                            </el-form>
                        </span>
                        <span>
                            <buscharr v-if="warehouseWagesData.visible" :analysisData="warehouseWagesData.data"
                                ref="warehouseWagesDataBuscharr">
                            </buscharr>
                        </span>
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import { queryDayReportAnalysisHome, getDayReportSumSaleAmont, getHomeGuestUnitPriceAnalysis, getPageDailyReportConfirmList,getDayReportGroupSaleAmontAsync ,getHomeReturnAnalysis as getRefundDetailAnalysis} from '@/api/bookkeeper/reportday'//日报趋势图
import { getHomeStockout, getInventoryAnalyseChart, getInventoryAnalyse } from '@/api/inventory/abnormal'
import { getHomeWithholdboardList, queryTxWithholdAnalysis, queryPddWithholdAnalysis } from "@/api/order/orderdeductmoney"
import { getLogTimeAsync } from '@/api/admin/opration-log'
//import { getRefundDetailAnalysis } from '@/api/financial/refund'
import { getTuiKuanData } from '@/api/customerservice/saleafteranalysis'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import cescolumnmerge from "@/components/Table/yhcolumnmerge.vue";
import cescolumn from "@/components/Table/yhcolumn.vue";
import IconIndex from './index.vue'
import IconIndexs from './indexs.vue'
import updateindex from './home-updatelog.vue'
import LineChart from './LineChart.vue'
import PieChart from './PieChart.vue'
import PieAllChart from './PieAllChart.vue'
import StockoutTable from './stockoutTable.vue'
import TuikuanTable from './tuikuanTable.vue'
import buschar from '@/components/Bus/buschar'
import buscharr from '@/components/Bus/buscharforShooting.vue';
import orderIllAnalysis from '../../order/orderillegal/OrderIllAnalysis.vue'
import editorindex from "./editor-index.vue";
import daycashredanalysis from "@/views/financial/daycost/daycashredanalysis.vue";
import LineOrderExpress from "./LineOrderExpress.vue"
import { getTaskInfoStatisticsHome } from '@/api/media/ShootingVideo'
import { getStatProcessHomeList } from '@/api/inventory/packagesprocess'
import { getTbWarehouseList, getOnePostNameList, getWarehouseWagesComputeChat3, getWarehouseWagesComputeChat4 } from '@/api/profit/warehousewages';
import { totalNumberTrendChart, dictionary, departmentRelationList, departmentListByParentId, } from '@/api/bladegateway/xzgateway.js';

import yhcharts from "@/components/Bus/buscharforShooting.vue";

const lineChartData = { legend: ["订单量"] }
const hearlist = [{ label: '查看全部', value: null }, { label: '查看普通ID', value: 1 }, { label: '查看特殊ID/店铺', value: 2 }];

const startDate = formatTime(dayjs().subtract(90, 'day'), "YYYY-MM-DD");
const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminHomeIndex',
    components: {
        cescolumnmerge, cescolumn, ElImageViewer, IconIndex, IconIndexs, updateindex, LineChart, PieChart,
        PieAllChart, TuikuanTable, StockoutTable, orderIllAnalysis, buschar, editorindex, daycashredanalysis, LineOrderExpress, buscharr, yhcharts
    },

    data() {
        return {
            chartarr: {
                quyuoptions: [],
                bumenoptions: [],
                guanlioptions: []
            },
            hearlist,
            timerange: [startTime, endTime],
            isIgnoreSpecialProCode: null,
            startQueryTimeArr: [],
            busfilter: {},
            switchactivenum: false,
            showchartotaldata: false,
            totalchardata: {},
            showchartotal: false,
            groupId: null,
            groupList: [],
            toolbox: {
                magicType: { show: true, type: ['line', 'bar'] },
                restore: { show: false },
                saveAsImage: { show: true }
            },
            thisStyleView5: { width: '100%', height: '350px', 'box-sizing': 'border-box', 'line-height': '240px' },
            gridStyleView5: { left: '1%', right: 15, bottom: 50, top: '10%', containLabel: true },
            filtershoot: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
                groupList: [],
                groupTask: true,
                groupDate: false
            },
            filterpackages: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
                groupList: [],
                groupTask: true,
                groupDate: false
            },
            dailyreportDate: [],
            dailyReportConfirmList: [],
            that: this,
            filterchartPay: {
                timerange: [startTime, endTime],
                type: "日"
            },
            filter: {
                reportType: 0,
                platform: 1,
                column: null,
                isHome: true
            },
            filterdetail: {
                isIgnoreSpecialProCode: null,
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime]
            },
            filterInventory: {
                startTime: null,
                endTime: null,
                timerange: [startDate, endTime]
            },
            filterchart: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime]
            },
            filterchart2: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime]
            },

            filterwarehouseWages: {
                startDate: null,
                endDate: null,
                timerange: [startTime, endTime],
                wareDataType: "分仓",
                warehouseCode: null,
                postName: null,
                lineName: null,
            },
            warehouseWagesWarehouseList: [],
            warehouseWagesOnePostNameList: [],

            isTrue: 1,
            isTrue1: 1,
            isTuikuan: 1,
            handType: null,
            platform: null,
            reportType: null,
            timer: null,
            pager: { OrderBy: "BrandName", IsAsc: false },
            tkPager: { OrderBy: "tuikuanOrderNum", IsAsc: false },
            lineChartData: lineChartData,
            daySumList: [],
            pieList: [],
            pieWihholdList: [],
            pieDeptList: [],
            PlatformPie: [],
            ExpressPie: [],
            pieWihhold: '400px',
            daycashredheight: '450px',
            piealltiele: '违规扣款分析（部门）',
            piealltiele1: '违规扣款分析（平台汇总）',
            piealltiele2: '违规扣款分析（快递）',
            LineOrderExpresstitle1: '快递扣款分析（扣款时间维度）',
            LineOrderExpresstitle2: '快递扣款分析（付款时间维度）',
            list: [],
            InventoryAnalyse: {},
            tuikuanList: [],
            stockoutlist: [],
            dialodayssisVisible: false,
            dialoganalysisVisible: false,
            isfull: false,
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近六个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            buscharDialog: { visible: false, title: "", data: [] },
            buscharDialog1: { visible: false, title: "", data: [] },
            buscharDialog2: { visible: false, title: "", data: [] },
            TxbuscharDialog: { visible: false, title: "", data: [] },
            PddbuscharDialog: { visible: false, title: "", data: [] },
            shootbuscharDialog: { visible: false, title: "", data: [] },
            packagesbuscharDialog: { visible: false, title: "", data: [] },
            warehouseWagesData: { visible: true, title: "", data: {} },

        };
    },

    async mounted() {
        await this.getDailyReportConfirmList()
        this.handleSetLineChartData('shoppings')
        this.getlist()
        this.getstockoutlist()

        this.getTuikuanList()
        await this.getInventory()
        this.getShootData();
        this.getPackagesData();


        //每小时更新
        if (this.timer) {
            clearInterval(this.timer)
        } else {
            this.timer = setInterval(async () => {
                await this.getlist();
                await this.handleSetLineChartData(this.handType);
                await this.getstockoutlist();
                await this.getTuikuanList();
            }, 3600000)
        }

        //仓库薪资
        await this.getWarehouseList();
        await this.getOnePostNameList();
        await this.showchartWarehouseWages();
        //
        let end = new Date();
        let start = new Date();
        start.setDate(start.getDate() - 7);
        this.startQueryTimeArr = [formatTime(start, "YYYY-MM-DD"), formatTime(end, "YYYY-MM-DD")]

        await this.getdepartmentListByParentIdTwo();
        await this.departmentSelectListfuctwo();
        await this.getdictionarytwo();

    },
    destroyed() {
        clearInterval(this.timer)
    },
    methods: {
        handoverEvent(){
          this.getChatInfos(this.timerange, this.isIgnoreSpecialProCode)
          let param = {
              reportType: 1,
              platform: 1,
              column: 'profit3',
              isHome: true,
              isIgnoreSpecialProCode: this.isIgnoreSpecialProCode,
              startTime: dayjs(this.timerange[0]).format('YYYY-MM-DD'),
              endTime: dayjs(this.timerange[1]).format('YYYY-MM-DD')
          }
          this.$nextTick(() => {
            this.$refs.iconIndexRef.getgroupList(param);
          })
        },
        onReset(){
            this.busfilter = {};
        },
        //字典获取
        async getdictionarytwo (){
            const res = await dictionary('manageLevel')
            if (!res?.success) return;
            res.data.map((item)=>{
                if(item?.dictKey){
                    item.label = item.dictValue;
                    item.value = item.dictKey;
                }
            })
            this.chartarr.guanlioptions = res.data?res.data:[];
        },
         //获取部门，
         async departmentSelectListfuctwo (name, index){
            const res = await departmentRelationList({})
            if (!res?.success) return;
            res.data.map((item)=>{
                item.label = item.name;
                item.value = item.deptIds;
            })
            this.chartarr.bumenoptions = res.data?res.data:[];
        },
        //获取部门，
        async getdepartmentListByParentIdTwo (name, index){
            const res = await departmentListByParentId({parentId: 1})
            if (!res?.success) return;
            res.data.map((item)=>{
            item.label = item.name;
            item.value = item.deptId;
            })
            this.chartarr.quyuoptions = res.data?res.data:[];
        },
        switchactive(val){
            this.showchartotaldata = false;
            this.totalchardata.series.map((item)=>{
                item.itemStyle.normal.label.show = this.switchactivenum;
            })
            this.$nextTick(() => {
                this.showchartotaldata = true;
            });
        },
        async totalNumberTrendChartfuc(){
            this.showchartotaldata = false;
            let params = {...this.busfilter};
            if(this.startQueryTimeArr?.length>0){
                params.startCalculateTime = this.startQueryTimeArr[0];
                params.endCalculateTime = this.startQueryTimeArr[1];
            }else{
                params.startCalculateTime = null;
                params.endCalculateTime = null;
            }

            const res = await totalNumberTrendChart(params)
            if (!res?.success) return;

            res.data.tooltip = { trigger: "axis", axisPointer: { type: "jicross", crossStyle: { color: "#999", }, }, };

            res.data.selectedLegend = ['计件薪资', '固定薪资','工资总和'];

            res.data.toolbox = {
            show: true, orient: 'vertical', left: 'right', top: 'center',
            feature: {
                mark: { show: true },
                dataView: { show: true, readOnly: false },
                magicType: {
                    show: true,
                    type: ['line', 'bar', 'stack']
                    },
            saveAsImage: { show: true } } };
            res.data.grid = { top: "55px", bottom: "60px", left: "60", right: "65", };
            res.data.series.map(item => {
                // item.type = "line";
                item.itemStyle = {
                "normal": {
                    "label": {
                    "show": this.switchactivenum,
                    "position": "top",
                    "textStyle": {
                        "fontSize": 14
                    }
                    }
                }
                }
                item.emphasis = {
                "focus": "series"
                }
                item.smooth = false;
            });
            if(!res.data.yAxis){
                res.data.yAxis = [{position: "left", unit: "", name: "", offset: 0}];
            }
            res.data.yAxis.forEach(item => {
                item.splitLine = {
                show: true
                };
            });
            this.totalchardata = res.data;

            this.$nextTick(() => {
                this.showchartotaldata = true;
            });
        },
        showxinzi(){
            this.showchartotal = true;
            this.totalNumberTrendChartfuc();
        },
        // getGroupId (id) {
        //     this.groupId = id
        // },
        async sortchange(column) {
            if (!column.order)
                this.tkPager = {};
            else {
                this.tkPager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.getTuikuanList();
        },
        async getDailyReportConfirmList() {
            let par = {
                currentPage: 1,
                pageSize: 500
            }
            if (this.dailyreportDate) {
                par.startTime = this.dailyreportDate[0];
                par.endTime = this.dailyreportDate[1];
            }
            let res = await getPageDailyReportConfirmList(par);
            if (res.data) {
                res.data.map(f => {

                    f.dailyReportDate = f.dailyReportDate.slice(0, 10);
                    f.dailyReportDate = f.dailyReportDate.replace(/-/g, "");
                    f.createTime = f.createTime.slice(0, 16);
                });
                this.dailyReportConfirmList = res.data;

            }
        },
        async handleSetLineChartData(type, id) {
            this.handType = type
            this.filterdetail.startTime = null;
            this.filterdetail.endTime = null;
            if (this.filterdetail.timerange) {
                this.filterdetail.startTime = this.filterdetail.timerange[0];
                this.filterdetail.endTime = this.filterdetail.timerange[1];
            }
            const params = { ...this.filter, ...this.filterdetail };
            params.column = 'profit3';
            if (type == 'saleAmonttian') {
                this.platform = 1;
                params.platform = 1;
                this.reportType = 0;
                params.reportType = 0
            }
            else if (type == 'saleAmonttao') {
                this.platform = 9;
                params.platform = 9;
                this.reportType = 0;
                params.reportType = 0
            }
            else if (type == 'messages') {
                this.platform = 2;
                params.platform = 2;
                this.reportType = 0;
                params.reportType = 0
            }
            else if (type == 'purchases') {
                this.platform = 8;
                params.platform = 8;
                this.reportType = 0;
                params.reportType = 0
            }
            else if (type == 'saleAmontDY') {
                this.platform = 6;
                params.platform = 6;
                this.reportType = 0;
                params.reportType = 0
            }
            else if (type == 'saleAmontSheIn') {
                this.platform = 12;
                params.platform = 12;
                this.reportType = 0;
                params.reportType = 0
            }
            else if (type == 'saleAmontKWaiShop') {
                this.platform = 14;
                params.platform = 14;
                this.reportType = 0;
                params.reportType = 0
            }
            else if (type == 'saleAmontWeChat') {
                this.platform = 20;
                params.platform = 20;
                this.reportType = 0;
                params.reportType = 0
            }
            else if (type == 'saleAmontJD') {
                this.platform = 7;
                params.platform = 7;
                this.reportType = 0;
                params.reportType = 0
            }
            else if (type == 'saleAmontJingXi') {
                this.platform = 72;
                params.platform = 72;
                this.reportType = 0;
                params.reportType = 0
            }
            else if (type == 'saleAmontJingXiNN') {
                this.platform = 73;
                params.platform = 73;
                this.reportType = 0;
                params.reportType = 0
            }
            else if (type == 'saleAmontAlibaba') {
                this.platform = 4;
                params.platform = 4;
                this.reportType = 0;
                params.reportType = 0
            }
            else if (type == 'saleAmontSuNing') {
                this.platform = 10;
                params.platform = 10;
                this.reportType = 0;
                params.reportType = 0
            }
            else if (type == 'saleAmontFenXiao') {
                this.platform = 11;
                params.platform = 11;
                this.reportType = 0;
                params.reportType = 0
            }
            else if (type == 'saleAmontDYGX') {
                this.platform = 17;
                params.platform = 17;
                this.reportType = 0;
                params.reportType = 0
            }
            else if (type == 'shoppings') {
                this.reportType = 1;
                params.reportType = 1;
            }
            else if (type == 'sumAmountPaid') {
                this.dialoganalysisVisible = true;
                this.getTxAmountPaid();
                this.getPddAmountPaid();
                let para = { proCode: null, timerange: this.filterchart2.timerange, platform: this.platform, reportType: this.reportType }
                this.$nextTick(async () => {
                    await this.$refs.orderIllAnalysis.onSearch(para);
                });
            }
            else if (type == 'refundAmont') {
                this.getecharts()
            }
            else if (type == 'guestUnitPrice') {
                this.geteunitpricecharts()
            }
            else {
                return;
            }

            if (id != null) {
                if (id == 0) {
                    id = null;
                }
                params.groupId = id;
            } else {
                // this.$refs.iconIndexRef.getgroupList(params);
                this.$nextTick(() => {
                    this.$refs.iconIndexRef.getgroupList(params)
                })
            }

            if (type == 'saleAmonttian' ||type == 'saleAmonttao'|| type == 'messages' || type == 'purchases' || type == 'shoppings' || type == 'saleAmontDY' || type == 'saleAmontJD'|| type == 'saleAmontJingXi'|| type == 'saleAmontJingXiNN' || type == 'saleAmontAlibaba' || type == 'saleAmontSuNing'|| type == 'saleAmontFenXiao'|| type == 'saleAmontDYGX'|| type == 'saleAmontSheIn'|| type == 'saleAmontKWaiShop'|| type == 'saleAmontWeChat' ) {
                const res = await getDayReportSumSaleAmont(params);
                this.daySumList = res.data
                // this.$nextTick(() => {
                // this.$refs.iconIndexRef.getgroupList(params)
                // })
                //    await getDayReportGroupSaleAmontAsync(params).then(res =>{
                //     if (res.success) {
                //         this.groupList = res.data
                //         }
                //     })
                let that = this;
                params.charShowType = that.filterchartPay.type
                params.isIgnoreSpecialProCode = this.filterdetail.isIgnoreSpecialProCode
                await queryDayReportAnalysisHome(params).then(res => {
                    that.lineChartData = res.data.analysisList
                    that.pieList = res.data.pieList
                });

                params.isTrue = this.isTrue1
                var ress = await getHomeWithholdboardList(params)
                this.pieWihholdList = ress.data.analyboard
                this.pieDeptList = ress.data.deptboard
                this.PlatformPie = ress.data.platformPie
                this.ExpressPie = ress.data.expressPie

                let _th = this;
                await this.$nextTick(async () => {
                    await _th.$refs.daycashredanalysis.getAnalysis(params);
                });
            }
            this.$nextTick(async () => {
                await this.$refs.LineOrderExpress1.getlistexpresslist(1)
                await this.$refs.LineOrderExpress2.getlistexpresslist(2)
            })
            // await getDayReportGroupSaleAmontAsync(params).then(res =>{
            //     if (res.success) {
            //         this.groupList = res.data
            //         }
            //     })
            //  this.$nextTick(() => {
            //     this.$refs.iconIndexRef.getgroupList(params)
            //     })

        },
        onSearchChart() {
            this.dialoganalysisVisible = true;

            this.getTxAmountPaid();
            this.getPddAmountPaid();
            let para = { proCode: null, timerange: this.filterchart2.timerange, platform: this.platform, reportType: this.reportType }
            this.$nextTick(async () => {
                await this.$refs.orderIllAnalysis.onSearch(para);
            });

        },
        async getTxAmountPaid() {
            this.filterchart2.startTime = null;
            this.filterchart2.endTime = null;
            if (this.filterchart2.timerange) {
                this.filterchart2.startTime = this.filterchart2.timerange[0];
                this.filterchart2.endTime = this.filterchart2.timerange[1];
            }
            else if (this.filterdetail.timerange) {
                this.filterchart2.startTime = this.filterdetail.timerange[0];
                this.filterchart2.endTime = this.filterdetail.timerange[1];
            }
            var params = { ...this.filterchart2, platform: this.platform, reportType: this.reportType, isreFund: true, isHome: true }
            let that = this;
            const res = await queryTxWithholdAnalysis(params).then((res) => {

                that.TxbuscharDialog.visible = true;
                that.TxbuscharDialog.data = res.data
                that.TxbuscharDialog.title = ""
            });

            this.$nextTick(async () => {
                await this.$refs.Txbuschar.initcharts()
            });
        },
        async getPddAmountPaid() {
            this.filterchart2.startTime = null;
            this.filterchart2.endTime = null;
            if (this.filterchart2.timerange) {
                this.filterchart2.startTime = this.filterchart2.timerange[0];
                this.filterchart2.endTime = this.filterchart2.timerange[1];
            }
            else if (this.filterdetail.timerange) {
                this.filterchart2.startTime = this.filterdetail.timerange[0];
                this.filterchart2.endTime = this.filterdetail.timerange[1];
            }
            var params = { ...this.filterchart2, platform: this.platform, reportType: this.reportType, isreFund: true, isHome: true }
            let that = this;
            const res = await queryPddWithholdAnalysis(params).then((res) => {

                that.PddbuscharDialog.visible = true;
                that.PddbuscharDialog.data = res.data
                that.PddbuscharDialog.title = ""
            });

            this.$nextTick(async () => {
                await this.$refs.Pddbuschar.initcharts()
            });
        },

        async getlist() {
            const res = await getLogTimeAsync();

            if (!res?.success) {
                return
            }
            const data = res.data
            this.list = data
        },
        async clicktimelist(row) {
            this.dialodayssisVisible = true
            this.$nextTick(async () => {
                await this.$refs.updateindex.getlist(row)
            })
        },
        async getPie() {
            this.filterchartPay.timerange = this.filterdetail.timerange;
            this.getTuikuanList()
            if (this.handType == 'saleAmonttian' || this.handType == 'saleAmonttao' || this.handType == 'messages' || this.handType == 'purchases' || this.handType == 'shoppings' || this.handType == 'saleAmontDY' || this.handType == 'saleAmontJD'|| this.handType == 'saleAmontJingXi'|| this.handType == 'saleAmontJingXiNN' || this.handType == 'saleAmontAlibaba' || this.handType == 'saleAmontSuNing' || this.handType =='saleAmontFenXiao'|| this.handType =='saleAmontSheIn'|| this.handType =='saleAmontKWaiShop'|| this.handType =='saleAmontWeChat' ) {
                await this.handleSetLineChartData(this.handType, 0)
            }
            else {
                this.filterdetail.startTime = null;
                this.filterdetail.endTime = null;
                if (this.filterdetail.timerange) {
                    this.filterdetail.startTime = this.filterdetail.timerange[0];
                    this.filterdetail.endTime = this.filterdetail.timerange[1];
                }
                const params = { ...this.filter, ...this.filterdetail };
                params.column = 'profit3';
                params.platform = this.platform
                params.reportType = this.reportType;
                //const res = await getDayReportSumSaleAmont(params);
                //this.daySumList = res.data
                this.$nextTick(() => {
                this.$refs.iconIndexRef.getgroupList(params)
                })
                // await getDayReportGroupSaleAmontAsync(params).then(res => {
                //     if (res.success) {
                //     this.groupList = res.data
                //     }
                // })
                let that = this;
                params.charShowType = that.filterchartPay.type
                params.isIgnoreSpecialProCode = this.filterdetail.isIgnoreSpecialProCode
                await queryDayReportAnalysisHome(params).then(res => {
                    that.lineChartData = res.data.analysisList
                    that.pieList = res.data.pieList
                });
            }
            //库存分析
            //this.getInventory()

            //await this.$refs.LineOrderExpress.getlistexpresslist(1)
        },
        // 获取违规扣款分析饼图
        async getstockoutlist() {
            const res = await getHomeStockout();
            if (!res?.success) {
                return
            }
            const data = res.data.list
            this.stockoutlist = data
        },
        // 退款排行榜数据
        async getTuikuanList() {

            this.filterdetail.startTime = null;
            this.filterdetail.endTime = null;
            if (this.filterdetail.timerange) {
                this.filterdetail.startTime = this.filterdetail.timerange[0];
                this.filterdetail.endTime = this.filterdetail.timerange[1];
            }
            var params = { ...this.filterdetail, ...this.tkPager, platform: this.platform, afterSaleType: this.isTuikuan }
            const res = await getTuiKuanData(params);
            if (!res?.success) {
                return
            }
            const data = res.data
            this.tuikuanList = data

        },

        //每日退款趋势
        async getecharts() {
            this.filterchart.startTime = null;
            this.filterchart.endTime = null;
            if (this.filterchart.timerange) {
                this.filterdetail.startTime = this.filterchart.timerange[0];
                this.filterdetail.endTime = this.filterchart.timerange[1];
            }
            else if (this.filterdetail.timerange&&this.filterchart.timerange.length<1) {
                this.filterchart.startTime = this.filterdetail.timerange[0];
                this.filterchart.endTime = this.filterdetail.timerange[1];
            }
            var params = { ...this.filterdetail, platform: this.platform, reportType: this.reportType }
            let that = this;
            const res = await getRefundDetailAnalysis(params).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data
                that.buscharDialog.title = res.data.legend[0];
            })
            await this.$refs.RefundBuschar.initcharts()
        },
        //客单价趋势
        async geteunitpricecharts() {
            this.filterchart.startTime = null;
            this.filterchart.endTime = null;
            if (this.filterchart.timerange) {
                this.filterchart.startTime = this.filterchart.timerange[0];
                this.filterchart.endTime = this.filterchart.timerange[1];
            }
            else if (this.filterdetail.timerange) {
                this.filterchart.startTime = this.filterdetail.timerange[0];
                this.filterchart.endTime = this.filterdetail.timerange[1];
            }
            var params = { ...this.filterchart, platform: this.platform, reportType: this.reportType, isreFund: true, isHome: true }
            let that = this;
            const res = await getHomeGuestUnitPriceAnalysis(params).then((res) => {

                that.buscharDialog1.visible = true;
                that.buscharDialog1.data = res.data
                that.buscharDialog1.title = "客单价趋势图"
            });

            this.$nextTick(async () => {
                await this.$refs.buschar.initcharts()
            });

        },

        async getInventory() {
            this.filterInventory.startTime = null;
            this.filterInventory.endTime = null;
            if (this.filterInventory.timerange) {
                this.filterInventory.startTime = this.filterInventory.timerange[0];
                this.filterInventory.endTime = this.filterInventory.timerange[1];
            }
            let isTrue, isHome
            if (this.isTrue == '1') {
                isTrue = true;
                isHome = true;
            }
            else if (this.isTrue == '2') {
                isTrue = false;
                isHome = false;
            }
            else if (this.isTrue == '3') {
                isTrue = false;
                isHome = true;
            }
            const params = { ...this.filterInventory, ...this.pager, isHome: isHome, isTrue: isTrue };

            //var res = await getInventoryAnalyse(params)
            var res = await getInventoryAnalyseChart(params)
            this.InventoryAnalyse = res?.data
        },
        async getExpress() {
            this.filterdetail.startTime = null;
            this.filterdetail.endTime = null;
            if (this.filterdetail.timerange) {
                this.filterdetail.startTime = this.filterdetail.timerange[0];
                this.filterdetail.endTime = this.filterdetail.timerange[1];
            }
            const params = { ...this.filter, ...this.filterdetail };
            params.column = 'profit3';
            params.platform = this.platform
            params.reportType = this.reportType;
            params.isTrue = this.isTrue1

            var ress = await getHomeWithholdboardList(params)
            this.pieWihholdList = ress.data.analyboard
            this.pieDeptList = ress.data.deptboard
            this.PlatformPie = ress.data.platformPie
            this.ExpressPie = ress.data.expressPie
        },
        async getChatInfos(data, isIgnoreSpecialProCode) {
            console.log(data, 222);
            if (data) {
                this.filterdetail.timerange = data
            } else {
                this.filterdetail.timerange = this.filterchartPay.timerange;
            }
            this.filterdetail.isIgnoreSpecialProCode = isIgnoreSpecialProCode !== undefined  ? isIgnoreSpecialProCode : this.filterdetail.isIgnoreSpecialProCode || null;
            await this.getPie();
        },
        async getShootData() {
            this.shootbuscharDialog.visible = false;
            this.filtershoot.startTime = null;
            this.filtershoot.endTime = null;
            if (this.filtershoot.timerange) {
                this.filtershoot.startTime = this.filtershoot.timerange[0];
                this.filtershoot.endTime = this.filtershoot.timerange[1];
            }
            const params = { ...this.filter, ...this.filtershoot };
            var res = await getTaskInfoStatisticsHome(params);
            this.shootbuscharDialog.visible = true;
            this.shootbuscharDialog.data = res?.data
            this.shootbuscharDialog.title = ""
        },
        async getPackagesData() {
            this.packagesbuscharDialog.visible = false;
            this.filterpackages.startTime = null;
            this.filterpackages.endTime = null;
            if (this.filterpackages.timerange) {
                this.filterpackages.startTime = this.filterpackages.timerange[0];
                this.filterpackages.endTime = this.filterpackages.timerange[1];
            }
            const params = { ...this.filter, ...this.filterpackages };
            var res = await getStatProcessHomeList(params);
            this.packagesbuscharDialog.visible = true;
            this.packagesbuscharDialog.data = res?.data
            this.packagesbuscharDialog.title = ""
        },
        async groupchange(arry) {
            this.filtershoot.groupTask = false;
            this.filtershoot.groupDate = false;
            if (arry.length > 0) {
                arry.forEach(f => {
                    if (f == "bytask") this.filtershoot.groupTask = true;
                    else if (f == "byday") this.filtershoot.groupDate = true;
                });
            } else {
                this.filtershoot.groupTask = true;
            }
            this.getShootData();
        },
        async groupchangepackages(arry) {
            this.filterpackages.groupTask = false;
            this.filterpackages.groupDate = false;
            if (arry.length > 0) {
                arry.forEach(f => {
                    if (f == "bytk") {
                        this.filterpackages.groupTask = true;
                        this.filterpackages.groupDate = false;
                    }
                    if (f == "bydy") { this.filterpackages.groupDate = true; this.filterpackages.groupTask = false; }
                });
            } else {
                this.filterpackages.groupTask = true;
            }

            this.getPackagesData();
        },

        async getWarehouseList() {
            this.warehouseWagesWarehouseList = [];
            const res = await getTbWarehouseList();
            if (res && res.length > 0) {
                for (let i = 0; i < res.length; i++) {
                    this.warehouseWagesWarehouseList.push({ label: res[i].name, value: res[i].wms_co_id })
                }
            }
        },
        async getOnePostNameList() {
            this.warehouseWagesOnePostNameList = [];
            const res = await getOnePostNameList();
            if (res && res.length > 0) {
                for (let i = 0; i < res.length; i++) {
                    this.warehouseWagesOnePostNameList.push({ label: res[i], value: res[i] })
                }
            }
        },
        onWareDataTypeChange(e) {
            this.filterwarehouseWages.wareDataType = e;
            if (e == "分仓") {
                this.filterwarehouseWages.lineName = null;
            }
            else if (e == "各仓对比") {
                this.filterwarehouseWages.warehouseCode = null;
            }
            this.showchartWarehouseWages();
        },
        async showchartWarehouseWages() {
            // this.filterwarehouseWages.startDate = null;
            // this.filterwarehouseWages.endDate = null;
            if (this.filterwarehouseWages.timerange && this.filterwarehouseWages.timerange.length > 1) {
                this.filterwarehouseWages.startDate = this.filterwarehouseWages.timerange[0];
                this.filterwarehouseWages.endDate = this.filterwarehouseWages.timerange[1];
            }
            else {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            console.log(this.filterwarehouseWages, 'showchartWarehouseWages-params')
            let that = this;
            if (this.filterwarehouseWages.wareDataType == "分仓") {
                this.filterwarehouseWages.lineName = null;
                const res = await getWarehouseWagesComputeChat3(this.filterwarehouseWages).then(res => {
                    that.warehouseWagesData.visible = true;
                    that.warehouseWagesData.data = res;
                    that.$refs.warehouseWagesDataBuscharr.initcharts();
                    console.log(res);
                })
            }
            else if (this.filterwarehouseWages.wareDataType == "各仓对比") {
                this.filterwarehouseWages.warehouseCode = null;
                if (!this.filterwarehouseWages.lineName) {
                    this.filterwarehouseWages.lineName = '出仓成本'
                }
                const res = await getWarehouseWagesComputeChat4(this.filterwarehouseWages).then(res => {
                    that.warehouseWagesData.visible = true;
                    that.warehouseWagesData.data = res;
                    that.$refs.warehouseWagesDataBuscharr.initcharts();
                    console.log(res);
                })
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.indexnew {
    background-color: #fff;
    border-radius: 10px;
}

.timecard {
    // margin: 10px;
    margin: 10px 10px 0 -25px;
    height: 90%;
    overflow-y: auto;
}

.updateindex {
    margin: 10px;
    height: 550px;
    overflow-y: auto;
}

.dashboard-editor-container {
    padding: 0 20px 20px 20px;
    background-color: rgb(240, 242, 245);
    position: relative;

    .github-corner {
        position: absolute;
        top: 0px;
        border: 0;
        right: 0;
    }

    .chart-wrapper {
        background: #fff;
        // padding: 10px 1px 0;
        //margin-bottom: 10px;
        height: 440px;
        border-radius: 10px;
    }

    .chart-wrapper11 {
        background: rgb(240, 242, 245);
        padding: 10px 1px 0;
        margin-bottom: 1px;
        height: 450px;
    }

    .chart-wrapper22 {
        background: rgb(240, 242, 245);
        padding: 10px 1px 0;
        margin-bottom: 1px;
        height: 500px;
    }

    .chart-wrapper33 {
        background: #fff;
        padding: 1px 1px 0;
        //margin-bottom: 10px;
        height: 480px;
    }

    .chart-wrapper331 {
        background: #fff;
        padding: 10px 16px 0;
        //margin-bottom: 10px;
        height: 5000px;
        overflow-y: auto;
    }

    .chart-wrapper332 {
        background: #fff;
        padding: 10px 16px 0;
        //margin-bottom: 10px;
        height: 480px;
        border-radius: 10px;
    }

    .chart-wrapper44 {
        background: rgb(240, 242, 245);
        padding: 10px 1px 0;
        margin-bottom: 1px;
        height: 480px;
    }

    .chart-wrapper441 {
        background: #fff;
        padding: 10px 16px 0;
        //margin-bottom: 10px;
        height: 480px;
        overflow-y: auto;
    }

    .chart-wrapper555 {
        background: rgb(240, 242, 245);
        padding: 10px 1px 0;
        margin-bottom: 1px;
        height: 400px;
    }

    .chart-wrapper556 {
        background: #fff;
        padding: 10px 16px 0;
        height: 400px;
    }

    .chart-wrapper557 {
        background: rgb(240, 242, 245);
        padding: 10px 1px 0;
        margin-bottom: 1px;
        height: 600px;
    }

    .chart-wrapper558 {
        background: #fff;
        padding: 10px 16px 0;
        //margin-bottom: 10px;
        height: 590px;
        border-radius: 10px;
    }

}

@media (max-width: 1024px) {
    .chart-wrapper {
        padding: 8px;
    }
}

::v-deep .el-timeline-item__content {
    margin-top: -10px;
}

::v-deep .el-timeline-item__tail {
    border-left: 2px dashed #E4E7ED;
}
</style>
