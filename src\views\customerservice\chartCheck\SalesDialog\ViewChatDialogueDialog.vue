<template>
    <my-container>
        <!--列表-->
        <Ces-table ref="table" :that="that" :isIndex="true" @sortchange="sortchange" :tableData="tableData"
            :showsummary="true" :tableCols="tableCols" :loading="listLoading" style="width: 100%;
    height: calc(100% - 10%); margin: 0">
        </Ces-table>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getPageList" />
        </template>

    </my-container>
</template>

<script>
//审核总量
import {
    getUnPayOrderProblemPageList
} from "@/api/customerservice/chartCheck";
import MyContainer from "@/components/my-container";
import CesTable from "@/components/Table/table.vue";

const tableCols = [
    {
        istrue: true, prop: 'yearMonth', label: '时间', width: '320', sortable: 'custom', align: "center",

    },
    {
        istrue: true,
        prop: "disclaimerCount",
        label: "免责数",
        sortable: "custom",
        align: "center"
    },
    {
        istrue: true,
        prop: "problemCount",
        label: "问题合计",
        sortable: "custom",
        align: "center"
    },
];

export default {
    props: {
        HistoryROW: { type: Object, default: () => { } },
    },
    components: { MyContainer, CesTable },
    data() {
        return {
            that: this,
            tableData: [

            ],
            summaryarry: null,
            tableCols: tableCols,
            listLoading: false,
            total: 0,
        }
    },
    mounted() {

        this.onSearch();
    },
    methods: {
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getPageList();
        },

        sortchange(column) {    //表头排序
            if (!column.order) this.pager = {};
            else
                this.pager = {
                    orderBy: column.prop,
                    isAsc: column.order.indexOf("descending") == -1 ? true : false,
                };
            this.onSearch();
        },
        formatDate(numDate) {
            if (!numDate) return '';

            // 将数值转换为字符串
            const dateStr = numDate.toString();
            // 提取年、月、日
            const year = dateStr.slice(0, 4);
            const month = parseInt(dateStr.slice(4, 6), 10); // 转为数字

            // 格式化为“2024年1月2号”
            return `${year}年${month}月`;
        },
        async getPageList() {
            var pager = this.$refs.pager.getPager();
            var params = {
                ...pager,
                ...this.pager,
                SalesType: this.HistoryROW.salesType,
                userName: this.HistoryROW.userName,
                groupName: this.HistoryROW.groupName,
                platform: this.HistoryROW.platform,

            }
            const res = await getUnPayOrderProblemPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.tableData = res.data.list;
            // 格式化表格数据中的时间字段

            // 格式化表格数据中的时间字段
            this.tableData = res.data.list.map(item => ({
                ...item,
                yearMonth: this.formatDate(item.yearMonth)
            }));
        },
    },




}
</script>

<style></style>