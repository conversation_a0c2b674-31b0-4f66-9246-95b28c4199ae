<template>
    <div class="imageModel" @keydown="handleKeyDown" tabindex="0" ref="imageModel">
        <i class="el-icon-close" @click="closeModal"></i>
        <i class="el-icon-arrow-left left" @click="changeLocation('left')"></i>
        <i class="el-icon-arrow-right right" @click="changeLocation('right')"></i>
        <div class="imageModel_bottom">
            <!-- 缩小 -->
            <i class="el-icon-zoom-out" @click="magnify('small')"></i>
            <!-- 放大 -->
            <i class="el-icon-zoom-in" @click="magnify('big')"></i>
            <!-- 还原 -->
            <i class="el-icon-full-screen" @click="clearCss"></i>
            <!-- 左转 -->
            <i class="el-icon-refresh-left" @click="revolve('left')"></i>
            <!-- 右转 -->
            <i class="el-icon-refresh-right" @click="revolve('right')"></i>
        </div>
        <div class="imageModel_info">
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].sendGoodsDate"
                placement="top-start">
                <div v-show="checkList.includes(1)">发货日期:{{ imgList[index].sendGoodsDate }}
                </div>
            </el-tooltip>
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].sendWareHouse"
                placement="top-start">
                <div v-show="checkList.includes(2)">发货仓:{{ imgList[index].sendWareHouse }}
                </div>
            </el-tooltip>
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].platform" placement="top-start">
                <div v-show="checkList.includes(3)">平台:{{ imgList[index].platform }}
                </div>
            </el-tooltip>
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].styleCode"
                placement="top-start">
                <div v-show="checkList.includes(4)">系列编码:{{ imgList[index].styleCode }}
                </div>
            </el-tooltip>
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].goodsCode"
                placement="top-start">
                <div v-show="checkList.includes(5)">商品编码:{{ imgList[index].goodsCode }}
                </div>
            </el-tooltip>
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].zrDepartment"
                placement="top-start">
                <div v-show="checkList.includes(6)">损耗部门:{{ imgList[index].zrDepartment }}
                </div>
            </el-tooltip>
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].zrType2" placement="top-start">
                <div v-show="checkList.includes(7)">损耗类型:{{ imgList[index].zrType2 }}
                </div>
            </el-tooltip>
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].damagedHandType"
                placement="top-start">
                <div v-show="checkList.includes(8)">处理方式:{{ imgList[index].damagedHandType }}
                </div>
            </el-tooltip>
            <el-tooltip class="imageModel_info_item" effect="dark" :content="imgList[index].memberName"
                placement="top-start">
                <div v-show="checkList.includes(9)">责任人:{{ imgList[index].memberName }}
                </div>
            </el-tooltip>
        </div>
        <el-image :src="imgList[index].damagedZrFileUrls[imageIndex]" class="viewImageBox"
            :style="{ transform: `rotate(${deg}deg) scale(${scale})`, transition: transition }" />
        <!-- <div class="bottom_imgList">
            <div :class="['viewImageBox_item', index == i ? 'active' : '']" v-for="(item, i) in domList">
                <el-image class="viewImageBox_item_img bottomImg" :src="item.damagedZrFileUrls[index]"></el-image>
                <div class="viewImageBox_item_bottom">
                    <div class="viewImageBox_item_info">
                        <div class="viewImageBox_item_info_left">发货日期</div>
                        <el-tooltip class="viewImageBox_item_info_right" effect="dark" :content="item.sendGoodsDate"
                            placement="top-end">
                            <div>{{ item.sendGoodsDate }}</div>
                        </el-tooltip>
                    </div>
                    <div class="viewImageBox_item_info">
                        <div class="viewImageBox_item_info_left">发货仓</div>
                        <el-tooltip class="viewImageBox_item_info_right" effect="dark" :content="item.sendWareHouse"
                            placement="top-end">
                            <div>{{ item.sendWareHouse }}</div>
                        </el-tooltip>
                    </div>
                    <div class="viewImageBox_item_info">
                        <div class="viewImageBox_item_info_left">平台</div>
                        <el-tooltip class="viewImageBox_item_info_right" effect="dark" :content="item.platform"
                            placement="top-end">
                            <div>{{ item.platform }}</div>
                        </el-tooltip>
                    </div>
                    <div class="viewImageBox_item_info">
                        <div class="viewImageBox_item_info_left">系列编码</div>
                        <el-tooltip class="viewImageBox_item_info_right" effect="dark" :content="item.styleCode"
                            placement="top-end">
                            <div>{{ item.styleCode }}</div>
                        </el-tooltip>
                    </div>
                    <div class="viewImageBox_item_info">
                        <div class="viewImageBox_item_info_left">商品编码</div>
                        <el-tooltip class="viewImageBox_item_info_right" effect="dark" :content="item.goodsCode"
                            placement="top-end">
                            <div>{{ item.goodsCode }}</div>
                        </el-tooltip>
                    </div>
                    <div class="viewImageBox_item_info">
                        <div class="viewImageBox_item_info_left">损耗部门</div>
                        <el-tooltip class="viewImageBox_item_info_right" effect="dark" :content="item.zrDepartment"
                            placement="top-end">
                            <div>{{ item.zrDepartment }}</div>
                        </el-tooltip>
                    </div>
                    <div class="viewImageBox_item_info">
                        <div class="viewImageBox_item_info_left">损耗类型</div>
                        <el-tooltip class="viewImageBox_item_info_right" effect="dark" :content="item.zrType2"
                            placement="top-end">
                            <div>{{ item.zrType2 }}</div>
                        </el-tooltip>
                    </div>
                    <div class="viewImageBox_item_info">
                        <div class="viewImageBox_item_info_left">处理方式</div>
                        <el-tooltip class="viewImageBox_item_info_right" effect="dark" :content="item.damagedHandType"
                            placement="top-end">
                            <div>{{ item.damagedHandType }}</div>
                        </el-tooltip>
                    </div>
                </div>
                <div class="viewImageBox_item_fixed">{{ item.damagedZrFileUrlCount }}</div>
            </div>
            <div class="leftBox">
                <i class="el-icon-arrow-left leftBox_left"></i>
            </div>
            <div class="rightBox">
                <i class="el-icon-arrow-right"></i>
            </div>
        </div> -->
    </div>
</template>

<script>
import { formatTime, formatPlatform, pickerOptions, platformlist } from "@/utils/tools";
import dayjs from "dayjs";
import _ from "lodash";
export default {
    name: 'viewImageCarousel',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        imgList: {
            type: Array,
            default: () => []
        },
        maxIndex: {
            type: Number,
            default: 8
        },
        closeModal: {
            type: Function,
            default: () => { }
        },
        imageInfo: {
            type: Object,
            default: () => { }
        },
        domList: {
            type: Array,
            default: () => []
        },
        index: {
            type: Number,
            default: 0
        },
        checkList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            deg: 0,
            scale: 1,
            transition: '0.3s',
            formatPlatform,
            // index: 0,
            imageIndex: 0,
            platformlist,
        }
    },
    mounted() {
        this.imageIndex = 0
        console.log(this.imgList, 'this.imgList');
        this.$nextTick(() => {
            this.$refs.imageModel.focus()
        })
        // this.platformlist
        // this.imageInfo.platform = this.formatPlatform(this.imageInfo.platform)
        // this.imageInfo.forEach(item => {
        //     item.platform = this.formatPlatform(item.platform)
        // })
        //找出this.imgList.platform在this.platformlist中的label
        this.imgList.forEach(item => {
            item.platform = this.platformlist.find(v => v.value == item.platform).label
        })
        console.log(this.imgList, 'this.imgList');
        this.imageInfo.sendGoodsDate = dayjs(this.imageInfo.sendGoodsDate).format('YYYY-MM-DD')
    },
    methods: {
        changeLocation(type) {
            console.log(type, 'type');
            if (this.index == null) {
                this.index = 0
            }
            if (type == 'right') {
                if (this.index == this.imgList.length - 1 && this.imageIndex == this.imgList[this.index].damagedZrFileUrls.length - 1) return this.$message.warning('已经是最后一张了')
                this.imageIndex++
                if (this.imageIndex > this.imgList[this.index].damagedZrFileUrls.length - 1) {
                    this.index++
                    this.imageIndex = 0
                }
                if (this.index > this.imgList.length - 1) {
                    this.index = 0
                }
            } else if (type == 'left') {
                if (this.index == 0 && this.imageIndex == 0) return this.$message.warning('已经是第一张了')
                this.imageIndex--
                if (this.imageIndex < 0) {
                    this.index--
                    this.imageIndex = this.imgList[this.index].damagedZrFileUrls.length - 1
                }
            }
        },
        clearCss() {
            this.$refs.imageModel.focus()
            this.deg = 0
            this.scale = 1
            this.transition = 'none'
        },
        revolve(type) {
            this.transition = '0.3s'
            if (type == 'left') {
                this.deg -= 90
            }
            if (type == 'right') {
                this.deg += 90
            }
        },
        magnify(type) {
            this.transition = '0.3s'
            if (type == 'big') {
                this.scale += 0.2
            } else {
                if (this.scale.toString() <= 0.21) return
                this.scale -= 0.2
            }
        },
        handleKeyDown(e) {
            this.transition = '0.3s'
            if (e.keyCode == 37) {
                this.changeLocation('left')
            } else if (e.keyCode == 39) {
                this.changeLocation('right')
            } else if (e.keyCode == 38) {
                this.magnify('big')
            }
            else if (e.keyCode == 40) {
                this.magnify('small')
            }
            else if (e.keyCode == 27) {
                this.closeModal()
            }
        }
    }
}
</script>

<style scoped lang="scss">
.imageModel {
    //宽高是整个屏幕的宽高
    // width: 30vw;
    width: 100vw;
    height: 100vh;
    //黑色半透明背景
    background-color: rgba(0, 0, 0, 0.4);
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 10px;
    box-sizing: border-box;
    position: absolute;
    box-sizing: border-box;

    &:hover {

        .left,
        .right,
        .imageModel_bottom {
            display: block;
        }
    }

    .el-icon-close {
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 20px;
        color: #fff;
        cursor: pointer;
        z-index: 999;
    }

    .left {
        position: absolute;
        // top: 25%;
        top: 50%;
        left: 10px;
        font-size: 40px;
        color: #fff;
        cursor: pointer;
        display: none;
        z-index: 999;
    }

    .right {
        position: absolute;
        // top: 25%;
        top: 50%;
        right: 10px;
        font-size: 40px;
        color: #fff;
        cursor: pointer;
        display: none;
        z-index: 999;
    }

    .imageModel_bottom {
        width: 200px;
        height: 30px;
        padding: 10px 20px 0;
        background-color: rgba(102, 103, 107, 0.5);
        position: absolute;
        bottom: 18%;
        // bottom: 48%;
        left: 46%;
        font-size: 20px;
        color: #fff;
        cursor: pointer;
        display: none;
        flex-direction: row;
        border-radius: 20px 20px 10px 10px;
        z-index: 999;

        i {
            margin-right: 20px;
        }

    }

    .imageModel_info {
        width: 80vw;
        // padding:0 10px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        background-color: rgba(0, 0, 0, 0.4);
        position: fixed;
        // bottom: 43%;
        bottom: 10%;
        // left: 16%;
        left: 10%;
        font-size: 20px;
        color: #fff;
        flex-direction: row;
        border-radius: 20px;
        z-index: 999;
        font-size: 14px;
        display: flex;
        justify-content: center;

        .imageModel_info_item {
            margin-right: 20px;
            // width: 200px;
            //超出隐藏,省略号   
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

    }



}

.viewImageBox {
    min-width: 100%;
    min-height: 100%;
    width: 100%;
    height: 100%;
    overflow: hidden;
    // transform-origin: 985px 225px;
    transform-origin: 985px 425px;
}

.viewImageBox ::v-deep img {
    min-width: 100% !important;
    min-height: 100% !important;
    object-fit: contain !important;
}

.viewImageBox ::v-deep div {
    min-width: 100% !important;
    min-height: 100% !important;
    object-fit: contain !important;
}

.bottomImg ::v-deep div {
    min-width: 220px !important;
    min-height: 150px !important;
    width: 220px !important;
    height: 150px !important;
}

.viewImageBox_item_img ::v-deep img {
    min-width: 220px !important;
    min-height: 150px !important;
    object-fit: contain !important;
    width: 220px !important;
    height: 150px !important;
}

.viewImageBox_item_img {
    min-width: 220px !important;
    min-height: 150px !important;
    width: 220px !important;
    object-fit: contain !important;
    height: 150px !important;
}

.bottom_imgList {
    width: 100vw;
    height: 40vh;
    background-color: rgba(0, 0, 0, 0.45);
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 30px 100px 0;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    overflow: hidden;


    .viewImageBox_item {
        height: 310px;
        display: flex;
        flex-direction: column;
        margin-right: 10px;
        position: relative;

        .viewImageBox_item_fixed {
            position: absolute;
            top: 0;
            left: 0;
            width: 50px;
            height: 30px;
            background-color: red;
            color: #fff;
            text-align: center;
            line-height: 30px;
        }


        .viewImageBox_item_bottom {

            width: 220px;

            .viewImageBox_item_info {
                display: flex;
                background-color: #fff;
                color: #000;

                .viewImageBox_item_info_left,
                .viewImageBox_item_info_right {
                    text-align: center;
                    height: 20px;
                    box-sizing: border-box;
                    border: 1px solid #ccc;
                    line-height: 20px;
                    font-size: 12px;
                }

                .viewImageBox_item_info_left {
                    width: 80px;
                }

                .viewImageBox_item_info_right {
                    flex: 1;
                }
            }
        }
    }

    .leftBox {
        width: 40px;
        height: 40px;
        background-color: #fff;
        position: absolute;
        top: 50%;
        left: 30px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;

        .el-icon-arrow-left {
            color: #000;
            font-size: 20px;
            cursor: pointer;
        }
    }

    .rightBox {
        width: 40px;
        height: 40px;
        background-color: #fff;
        position: absolute;
        top: 50%;
        right: 30px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;

        .el-icon-arrow-right {
            color: #000;
            font-size: 20px;
            cursor: pointer;
        }
    }
}

.active {
    border: 2px solid red;
    box-shadow: 0 0 10px red;
}
</style>