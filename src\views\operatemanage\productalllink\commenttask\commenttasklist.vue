<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right" label-width="90px">
                <el-form-item>
                    <el-input v-model="filter.proCode" placeholder="产品ID" style="width: 120px" />
                </el-form-item>
                <el-form-item>
                    <el-input v-model="filter.proName" placeholder="产品名称" style="width: 120px" />
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.platform" placeholder="平台" :clearable="true" :collapse-tags="true" style="width: 120px" filterable>
                        <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.groupId" placeholder="运营组" :clearable="true" :collapse-tags="true" style="width: 120px" filterable>
                        <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.statusEnum" placeholder="状态" :clearable="true" :collapse-tags="true" style="width: 120px" filterable>
                        <el-option label="待发布" value=0>待发布</el-option>
                        <el-option label="已发布" value=1>已发布</el-option>
                        <!-- <el-option label="已认领" value=2>已认领</el-option>
                        <el-option label="已完成" value=3>已完成</el-option> -->
                        <el-option label="已推送任务" value=4>已推送任务</el-option>
                        <el-option label="已推送评价" value=5>已推送评价</el-option>
                        <el-option label="完成" value=6>完成</el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input v-model="filter.groupUserName" placeholder="发布人(运营)" style="width: 120px" />
                </el-form-item>
                <!-- <el-form-item>
                    <el-input v-model="filter.receiveUserName" placeholder="认领人" style="width: 120px" />
                </el-form-item> -->
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :loading="listLoading" :tableHandles='tableHandles'>
            <template slot='extentbtn'>
                &nbsp;
                <span style="color:red;font-size: 12px;">
                    注意事项：1.发布后会自动在一小时内自动推送到酒坛子，也可手动推送，2.状态为发布或已推送任务时机器人每小时会自动同步评价过来，同步评价成功时自动推送到酒坛子。
                </span>
            </template>
        </ces-table>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="评价任务" top="40px" :visible.sync="dialogAddFormVisible" width="65%" :close-on-click-modal="false" @close="onCloseAddForm" v-dialogDrag v-loading="addFromLoading" element-loading-text="拼命加载中">
            <el-collapse @change="collapasechange" v-model="activeNames">
                <el-collapse-item :title="collatitle" name="1">
                    <el-form ref="addForm" :model="addForm" :rules="addFormRules" label-width="110px" :disabled="!addFormEditMode">
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                                <el-form-item prop="proCode" label="产品ID">
                                    <el-select v-model="addForm.proCode" filterable remote reserve-keyword placeholder="请输入产品ID" :remote-method="onAddFormProCodeMethod" :loading="addFormProCodeLoading" style="width:100%" @change="onAddFormProCodeChange">
                                        <el-option v-for="item in addFormProCodeList" :key="item.proCode" :label="item.proCodeName" :value="item.proCode">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <!-- <el-col :xs="24" :sm="24" :md="24" :lg="2" :xl="2">
                            <el-form-item prop="" label="">
                                <el-button @click="onGetTaoBaoOrTmallMainImage()" style="float: right; font-size:14px" type="text">获取主图
                                </el-button>
                            </el-form-item>
                        </el-col> -->
                            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                                <el-form-item prop="proName" label="产品名称">
                                    <el-input v-model="addForm.proName" auto-complete="off" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                                <el-form-item prop="proImageUrl" label="产品主图">
                                    <yh-img-upload :value.sync="addForm.proImageUrl" :limit="2"></yh-img-upload>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                                <el-form-item prop="taoKouLing" label="淘口令">
                                    <el-input v-model="addForm.taoKouLing" auto-complete="off" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
                                <el-form-item prop="platform" label="平台">
                                    <el-select v-model="addForm.platform" style="width:100%" disabled>
                                        <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
                                <el-form-item prop="groupId" label="运营组">
                                    <el-select v-model="addForm.groupId" style="width:100%" disabled>
                                        <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
                                <el-form-item prop="groupUserName" label="发布人(运营)">
                                    <el-input v-model="addForm.groupUserName" auto-complete="off" :disabled="true" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
                                <el-form-item prop="startDate" label="开始日期">
                                    <el-date-picker v-model="addForm.startDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" style="width:100%">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
                                <el-form-item prop="endDate" label="结束日期">
                                    <el-date-picker v-model="addForm.endDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" style="width:100%">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="24" :md="24" :lg="10" :xl="10">
                                <el-form-item prop="goodsCompeteIds" label="竞品ID">
                                    <el-input v-model="addForm.goodsCompeteIds" auto-complete="off" :disabled="true" />
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="24" :md="24" :lg="2" :xl="2">
                                <el-form-item prop="" label="">
                                    <el-button @click="onShowCompeteId()" style="float: right; font-size:14px" type="text">添加竞品ID&nbsp;&nbsp;&nbsp;&nbsp;
                                    </el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
                                <el-form-item prop="hopeOrderCount" label="预计单量">
                                    <el-input-number v-model="addForm.hopeOrderCount" :min="0" :max="30" auto-complete="off" :precision="0">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
                                <el-form-item prop="payAmount" label="实际付款价格">
                                    <el-input-number v-model="addForm.payAmount" :min="0" :max="10000000" auto-complete="off" :precision="2">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <!-- <el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6">
                            <el-form-item prop="hopeCommentCount" label="预计评价数">
                                <el-input-number v-model="addForm.hopeCommentCount" :min="0" :max="100000000" auto-complete="off" :precision="0">
                                </el-input-number>
                            </el-form-item>
                        </el-col> -->
                            <!-- <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :hidden="addFormEditMode">
                            <el-form-item prop="finishOrderCount" label="完成单量">
                                <el-input-number v-model="addForm.finishOrderCount" :min="0" :max="100000000" auto-complete="off" :precision="0">
                                </el-input-number>
                            </el-form-item>
                        </el-col> -->
                            <el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6" :hidden="true">
                                <el-form-item prop="showAmount" label="展示价格">
                                    <el-input-number v-model="addForm.showAmount" :min="0" :max="10000000" auto-complete="off" :precision="2">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                                <el-form-item prop="goodsSku" label="SKU规格">
                                    <el-input v-model="addForm.goodsSku" auto-complete="off" :maxlength="50" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                                <el-form-item prop="remark" label="试用要求">
                                    <el-input v-model="addForm.remark" auto-complete="off" :maxlength="500" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-collapse-item>
            </el-collapse>
            <el-row>
                <el-tabs type="card" v-model="editableTabsValue">
                    <el-tab-pane label="附加商品" key="tab2" name="tab2">
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                                <el-button type="primary" @click="onAddDtl2" :disabled="!addFormEditMode">新增附加商品</el-button>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                                <ces-table ref="tabledtl2" :that='that' :isIndex='false' :hasexpand='true' :tableData='this.addForm.attachList' :tableCols='dtlTableCols2' rowkey="rowIndex" :loading="dtllistLoading2" style="height:230px" :isSelection='false' :isSelectColumn="false">
                                </ces-table>
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                    <el-tab-pane label="图片及评论" key="tab1" name="tab1">
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="3" :xl="3">
                                <el-button type="primary" @click="onAddDtl" :disabled="!addFormEditMode">新增图片及评论</el-button>
                            </el-col>
                            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                                <el-row>
                                    <el-col :xs="24" :sm="24" :md="24" :lg="15" :xl="15">
                                        <el-button type="text" @click="setallcn(true)" v-if="!addFormEditMode&&(addForm.statusEnum==1||addForm.statusEnum==4)">批量采纳</el-button>
                                        <el-button type="text" @click="setallcn(false)" v-if="!addFormEditMode&&(addForm.statusEnum==1||addForm.statusEnum==4)">批量取消采纳</el-button>
                                        <el-button type="text" @click="setallby(true)" v-if="!addFormEditMode&&(addForm.statusEnum==1||addForm.statusEnum==4||addForm.statusEnum==5||addForm.statusEnum==6)">批量备用</el-button>
                                        <el-button type="text" @click="setallby(false)" v-if="!addFormEditMode&&(addForm.statusEnum==1||addForm.statusEnum==4||addForm.statusEnum==5||addForm.statusEnum==6)">批量取消备用</el-button>
                                        <el-button type="text" @click="setalldel" style="color: red;" v-if="!addFormEditMode&&(addForm.statusEnum==1||addForm.statusEnum==4)">批量删除</el-button>
                                    </el-col>
                                    <el-col :xs="24" :sm="24" :md="24" :lg="3" :xl="3" style="height: 27px;line-height: 27px;" v-if="!addFormEditMode">
                                        SKU名称：
                                    </el-col>
                                    <el-col :xs="24" :sm="24" :md="24" :lg="6" :xl="6" v-if="!addFormEditMode">
                                        <el-select v-model="dtlSkuData" style="width:100%" clearable :disabled="addFormEditMode" @change="onDtlSkuDataChange">
                                            <el-option v-for="item in dtlSkuDataList" :key="item.value" :label="item.label" :value="item.value" />
                                        </el-select>
                                    </el-col>
                                </el-row>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                                <!-- <ces-table ref="tabledtl" :that='that' :isIndex='false' :hasexpand='true' :tableData='this.addForm.dtlList' :tableCols='dtlTableCols'
                                 rowkey="rowIndex" :loading="dtllistLoading" style="height:250px" :isSelection='false' :isSelectColumn="false">
                                </ces-table> -->
                                <div style="margin-top: -10px;">
                                    <vxetablebase :toolbarshow="false" :height="vxetableheight" :id="'commenttasklist20230215'" :hasSeq="false" :border="true" :align="'center'" ref="tabledtl" :that='that' :hasexpand='true' :tableData='this.addForm.dtlList' :tableCols='dtlTableCols' :isSelectColumn="true" :loading="dtllistLoading" :checkbox-config="{labelField: 'id', highlight: true, range: true}" @checkbox-range-end="callback"   @sortchange='onDtlListSortChange'></vxetablebase>
                                </div>
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                </el-tabs>
            </el-row>
            <template #footer>
                <span class="dialog-footer">
                    <span style="color:red;font-size: 12px;">
                        1.手动新增的评价保存后默认为“已采纳”状态，2.采纳数量必须等于预计单量才会同步评价到酒坛子
                    </span>
                    <el-button @click="dialogAddFormVisible = false">关 闭</el-button>
                    <my-confirm-button type="submit" @click="onAddSave" :validate="addFormValidate" :loading="addLoading" :disabled="!addFormEditMode">
                        保存&关闭
                    </my-confirm-button>
                    <my-confirm-button type="submit" @click="onSendJtz(addForm.commentTaskId,1)" :loading="sendJtzButtonLoading" :disabled="addForm.statusEnum!=1">
                        推送任务
                    </my-confirm-button>
                    <my-confirm-button type="submit" @click="onSendJtz(addForm.commentTaskId,2)" :loading="sendJtzButtonLoading" :disabled="addForm.statusEnum!=4">
                        推送评价
                    </my-confirm-button>
                </span>
            </template>
            <el-dialog title="图片及评论" :visible.sync="dialogAddFormDtlVisible" width="50%" :close-on-click-modal="false" @close="onCloseAddFormDtl" v-dialogDrag v-loading="addFromDtlLoading" element-loading-text="拼命加载中" append-to-body>
                <el-form ref="addFormDtl" :model="addFormDtl" label-width="80px" :rules="addFormDtlRules">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item prop="goodsImageUrls" label="图片">
                                <yh-img-upload :value.sync="addFormDtl.goodsImageUrls" :limit="6"></yh-img-upload>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item prop="goodsCommentSku" label="SKU名称">
                                <el-input v-model="addFormDtl.goodsCommentSku" auto-complete="off" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item prop="goodsComment" label="评价">
                                <el-input v-model="addFormDtl.goodsComment" auto-complete="off" type="textarea" :maxlength="1000" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="dialogAddFormDtlVisible = false">关 闭</el-button>
                        <el-button type="primary" @click="onAddDtlOK">确 定</el-button>
                    </span>
                </template>
            </el-dialog>
            <el-dialog title="添加竞品ID" :visible.sync="addCompeteIdDialogVisible" width="20%" :close-on-click-modal="false" @close="onCloseCompeteIdDialog" v-dialogDrag v-loading="addCompeteIdDialogLoading" element-loading-text="拼命加载中" append-to-body>
                <template>
                    <div>
                        <el-button type="primary" @click="onAddCompeteId">新增一行</el-button>
                    </div>
                </template>
                <ces-table ref="tableAddCompeteId" :that='that' :isIndex='false' :hasexpand='true' :tableData='this.addCompeteIdList' :tableCols='addCompeteIdListCols' style="height:300px" :isSelection='false' :isSelectColumn="false">
                </ces-table>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="addCompeteIdDialogVisible = false">取 消</el-button>
                        <el-button type="primary" @click="onSaveCompeteId">确 定</el-button>
                    </span>
                </template>
            </el-dialog>
            <el-dialog title="附加商品" :visible.sync="dialogAddFormAttachDtlVisible" width="30%" :close-on-click-modal="false" @close="onCloseAddFormAttachDtl" v-dialogDrag v-loading="addFromAttachDtlLoading" element-loading-text="拼命加载中" append-to-body>
                <el-form ref="addFormAttachDtl" :model="addFormAttachDtl" label-width="80px" :rules="addFormAttachDtlRules">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item prop="proCode" label="产品ID">
                                <!-- <el-input v-model="addFormAttachDtl.proCode" auto-complete="off" :disabled="addFormAttachDtlDisable" :maxlength="50" /> -->
                                <el-select v-model="addFormAttachDtl.proCode" filterable remote reserve-keyword placeholder="请输入产品ID" :remote-method="onDtlProCodeMethod" :loading="dtlProCodeLoading" style="width:100%" @change="onDtlProCodeChange">
                                    <el-option v-for="item in dtlProCodeList" :key="item.proCode" :label="item.proCodeName" :value="item.proCode">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item prop="proName" label="产品名称">
                                <el-input v-model="addFormAttachDtl.proName" auto-complete="off" :maxlength="200" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item prop="proImageUrl" label="产品主图">
                                <yh-img-upload :value.sync="addFormAttachDtl.proImageUrl" :limit="1"></yh-img-upload>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :hidden="true">
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" :hidden="true">
                            <el-form-item prop="showAmount" label="展示价格">
                                <el-input-number v-model="addFormAttachDtl.showAmount" :min="0" :max="10000000" auto-complete="off" :precision="2">
                                </el-input-number>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item prop="payAmount" label="付款价格">
                                <el-input-number v-model="addFormAttachDtl.payAmount" :min="0" :max="10000000" auto-complete="off" :precision="2">
                                </el-input-number>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item prop="goodsSku" label="SKU规格">
                                <el-input v-model="addFormAttachDtl.goodsSku" auto-complete="off" :maxlength="50" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="dialogAddFormAttachDtlVisible = false">关 闭</el-button>
                        <el-button type="primary" @click="onAddDtlOK2">确 定</el-button>
                    </span>
                </template>
            </el-dialog>
            <el-dialog title="修改评价" :visible.sync="dialogAddFormDtl2Visible" width="30%" :close-on-click-modal="false" @close="onCloseAddFormDtl2" v-dialogDrag v-loading="addFromDtl2Loading" element-loading-text="拼命加载中" append-to-body>
                <el-form ref="addFormDtl" :model="addFormDtl2" label-width="80px" :rules="addFormDtl2Rules">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                            <el-form-item prop="goodsComment" label="评价">
                                <el-input v-model="addFormDtl2.goodsComment" auto-complete="off" rows="4" type="textarea" :maxlength="500" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="dialogAddFormDtl2Visible = false">关 闭</el-button>
                        <el-button type="primary" @click="onUpdateDtlCommentOK">保 存</el-button>
                    </span>
                </template>
            </el-dialog>
        </el-dialog>

        <el-dialog title="完成评价任务" :visible.sync="dialogFinishFormVisible" width="50%" :close-on-click-modal="false" @close="onCloseFinishForm" v-dialogDrag v-loading="finishFromLoading" element-loading-text="拼命加载中">
            <el-form ref="finishForm" :model="finishForm" :rules="finishFormRules" label-width="90px">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="proCode" label="产品ID">
                            <el-input v-model="finishForm.proCode" auto-complete="off" :disabled="true" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="proName" label="产品名称">
                            <el-input v-model="finishForm.proName" auto-complete="off" :disabled="true" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="proImageUrl" label="产品主图">
                            <yh-img-upload :value.sync="finishForm.proImageUrl" :limit="2" :disabled="true"></yh-img-upload>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="taoKouLing" label="淘口令">
                            <el-input v-model="finishForm.taoKouLing" auto-complete="off" :disabled="true" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="platform" label="平台">
                            <el-select v-model="finishForm.platform" style="width:100%" :disabled="true">
                                <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="groupId" label="运营组">
                            <el-select v-model="finishForm.groupId" style="width:100%" :disabled="true">
                                <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="groupUserName" label="发布人(运营)">
                            <el-input v-model="finishForm.groupUserName" auto-complete="off" :disabled="true" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="startDate" label="开始日期">
                            <el-date-picker v-model="finishForm.startDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" style="width:100%" :disabled="true">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="endDate" label="结束日期">
                            <el-date-picker v-model="finishForm.endDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" style="width:100%" :disabled="true">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="hopeOrderCount" label="预计单量">
                            <el-input-number v-model="finishForm.hopeOrderCount" :min="0" :max="100000000" auto-complete="off" :precision="0" :disabled="true">
                            </el-input-number>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="hopeCommentCount" label="预计评价数">
                            <el-input-number v-model="finishForm.hopeCommentCount" :min="0" :max="100000000" auto-complete="off" :precision="0" :disabled="true">
                            </el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                        <el-form-item prop="finishOrderCount" label="完成单量">
                            <el-input-number v-model="finishForm.finishOrderCount" :min="0" :max="100000000" auto-complete="off" :precision="0" :disabled="true">
                            </el-input-number>
                        </el-form-item>
                    </el-col> -->
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <div style="height:30px;">
                            <span style="font-size: 16px;font-weight: bold;">订单明细：</span>
                            <el-button type="primary" @click="onImportOrderModel">下载模板</el-button>
                            <el-button type="primary" @click="onImportOrder">导入订单</el-button>
                        </div>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <ces-table ref="tablefinishdtl" :that='that' :isIndex='true' :hasexpand='true' :tableData='this.finishForm.orderDtlList' :tableCols='dtlFinishTableCols' :loading="dtllistFinishLoading" style="height:250px" :isSelection='false' :isSelectColumn="false">
                        </ces-table>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogFinishFormVisible = false">关 闭</el-button>
                    <my-confirm-button type="submit" @click="onSaveFinish" :loading="finishSaveLoading">
                        完成&关闭
                    </my-confirm-button>
                </span>
            </template>
            <el-dialog title="导入订单" :visible.sync="dialogFinishImportOrder" width="30%" append-to-body v-loading="dialogFinishImportOrderLoading">
                <span>
                    <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2">
                        <template #trigger>
                            <el-button size="small" type="primary">选取文件</el-button>
                        </template>
                        <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2" :loading="dtllistFinishImportOrderLonging">上传</my-confirm-button>
                    </el-upload>
                </span>
                <span>界面提示导入成功前请勿关闭界面，时长大约每个订单0.5秒。</span>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="dialogFinishImportOrder = false">关闭</el-button>
                </span>
            </el-dialog>
        </el-dialog>
        <el-drawer title="打包进度" :visible.sync="downloadProgressVisible" @close="onDownloadClose">
            <el-table :data="downloadProgressList">
                <el-table-column prop="businesTypeContext" label="文件信息" width="400" />
                <el-table-column prop="countRate" label="进度">
                    <template slot-scope="scope">
                        <el-progress type="circle" :percentage="scope.row.countRate" :width="50" />
                    </template>
                </el-table-column>
                <el-table-column lable="下载">
                    <template slot-scope="scope">
                        <el-button type="primary" @click="onDownloadGet(scope.row)">下载
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-drawer>
    </my-container>
</template>

<script>
    import { formatTime } from "@/utils";
    import MyContainer from '@/components/my-container';
    import cesTable from "@/components/Table/table.vue";
    import { platformlist } from '@/utils/tools';
    import MyConfirmButton from '@/components/my-confirm-button';
    import { getDirectorGroupList } from '@/api/operatemanage/base/shop';
    import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
    import {
        getUserInfo, pageCommentTaskAsync, saveCommentTaskAsync, deleteCommentTaskAsync,
        releaseCommentTaskAsync, receiveCommentTaskAsync, finishCommentTaskAsync, importCommentTaskOrderAsync,
        deleteCommentTaskOrderAsync, exportCommentTaskAsync, openCloseCommentTaskDtlSync, sendJiuTanZi, getTaoBaoOrTmallMainImage,
        getCommentProductListByCode, getCommentTaskFirstDetailByDtlId, adoptCommentTaskDetail, deleteCommentTaskDtlAsync,
        getCommentTaskDownloadProgressAsync, getCommentTaskDownloadProgress2Async, updateCommentTaskDetail, spareCommentTaskDetail
    } from '@/api/operatemanage/productalllink/alllink';
    import YhImgUpload from "@/components/upload/yh-img-upload.vue";
    import ret from "bluebird/js/release/util";
    import { assertLiteral, tSParenthesizedType } from "@babel/types";
    const tableCols = [
        { istrue: true, prop: 'commentTaskId', label: '主键', width: '80', sortable: 'custom', display: false },
        { istrue: true, prop: 'dtlSyncStatus', label: '是否开启机器人同步', sortable: 'custom', display: false },
        { istrue: true, prop: 'createdTime', label: '创建时间', width: '150', sortable: 'custom', formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'proCode', label: '产品ID', width: '130', sortable: 'custom' },
        { istrue: true, prop: 'proImageUrl', label: '产品图片', width: '80', type: 'images' },
        { istrue: true, prop: 'proName', label: '产品名称', sortable: 'custom' },
        { istrue: true, prop: 'platform', label: '平台', width: '80', sortable: 'custom', formatter: (row) => row.platformName || ' ' },
        { istrue: true, prop: 'taoKouLing', label: '淘口令', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'hopeOrderCount', label: '预计单量', width: '55', sortable: 'custom' },
        { istrue: true, prop: 'dtlCount', label: '评论数量', width: '55' },
        { istrue: true, prop: 'startDate', label: '开始时间', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.startDate, 'YYYY-MM-DD') },
        { istrue: true, prop: 'endDate', label: '结束时间', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.endDate, 'YYYY-MM-DD') },
        { istrue: true, prop: 'groupId', label: '运营组', width: '80', sortable: 'custom', formatter: (row) => row.groupName || ' ' },
        { istrue: true, prop: 'groupUserName', label: '发布人(运营)', width: '80', sortable: 'custom' },
        //{ istrue: true, prop: 'dtlSyncStatus', label: '机器人同步评价明细', width: '90', sortable: 'custom', formatter: (row) => row.dtlSyncStatus == 1 ? "已开启" : "已关闭" },
        {
            istrue: false, type: 'button', label: '下载', width: '80',
            btnList: [
                { label: "打包下载 ", handle: (that, row) => that.onDownload(row) }
            ]
        },
        {
            istrue: false, type: 'button', label: '详情', width: '60',
            btnList: [
                { label: " 详情", handle: (that, row) => that.onOpenEdit(row, false) },
            ]
        },
        //{ istrue: true, prop: 'receiveUserName', label: '认领人', width: '80', sortable: 'custom', display: false },
        //{ istrue: true, prop: 'receiveDate', label: '认领时间', width: '150', sortable: 'custom', display: false, formatter: (row) => row.receiveDate == null ? null : formatTime(row.receiveDate, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'finishOrderCount', label: '完成单量', width: '80', sortable: 'custom', display: false },
        { istrue: true, prop: 'finishDate', label: '完成时间', width: '150', sortable: 'custom', display: false, formatter: (row) => row.finishDate == null ? null : formatTime(row.finishDate, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'statusEnum', label: '状态', width: '80', sortable: 'custom', formatter: (row) => row.statusName || ' ' },
        //{ istrue: true, prop: 'sendStatusName', label: '推送状态', width: '80', formatter: (row) => row.sendStatusName || ' ' },
        {
            istrue: false, type: 'button', label: '操作', width: '140',
            btnList: [
                { label: "编辑", handle: (that, row) => that.onOpenEdit(row, true), ishide: (that, row) => row.statusEnum != 0 },
                { label: "删除", handle: (that, row) => that.onDelete(row), ishide: (that, row) => row.statusEnum != 0 },
                { label: "发布", handle: (that, row) => that.onRelease(row), ishide: (that, row) => row.statusEnum != 0 },//ishide: (that, row) => row.statusEnum != 0
                { label: "取消发布", handle: (that, row) => that.onNoRelease(row), ishide: (that, row) => row.statusEnum != 1 },//ishide: (that, row) => row.statusEnum != 1
                { label: "推送任务", handle: (that, row) => that.onSendJtz(row.commentTaskId, 1), ishide: (that, row) => row.statusEnum != 1 },
                { label: "推送评价", handle: (that, row) => that.onSendJtz(row.commentTaskId, 2), ishide: (that, row) => row.statusEnum != 4 },
                { label: "认领", handle: (that, row) => that.onReceive(row), ishide: true },//(that, row) => row.statusEnum != 1
                { label: "完成", handle: (that, row) => that.onOpenFinish(row), ishide: true },// (that, row) => row.statusEnum < 1
                //{ label: "开启同步", handle: (that, row) => that.onSetSyncStatus(row, true), ishide: (that, row) => (row.dtlSyncStatus == 1 || row.statusEnum != 0) },
                //{ label: "关闭同步", handle: (that, row) => that.onSetSyncStatus(row, false), ishide: (that, row) => (row.dtlSyncStatus == 0 || row.statusEnum != 0) },
            ]
        },
    ];
    const tableHandles = [
        { label: "新 增", handle: (that) => that.onOpenAdd() },
        { label: "查看打包进度", handle: (that) => that.onOpenDownload() },
    ];
    const dtlTableCols = [
        { istrue: true, label: '', width: '100', type: "checkbox", },
        { istrue: true, prop: 'rowIndex', label: '序号', width: '40', },
        { istrue: true, prop: 'goodsImageUrls', label: '图片', width: '60', type: 'images', columnKey: "rowIndex", sortable: 'custom' },
        //{ istrue: true, prop: 'goodsImageCount', label: '图数', width: '60', sortable: 'custom' },
        { istrue: true, prop: 'goodsCommentSku', label: 'SKU名称', width: '150', },
        //{ istrue: true, prop: 'dtlCommentUser', label: '评价人' ,width: '100'},
        { istrue: true, prop: 'goodsComment', label: '评价' },
        {
            istrue: true, type: 'button', label: '编辑', width: '40',
            btnList: [
                { label: "编辑", handle: (that, row) => that.onUpdateDtlComment(row), ishide: (that, row) => (that.addFormEditMode || row.dtlStatus != 0) },
            ]
        },
        { istrue: true, prop: 'dtlStatus', label: '状态', width: '60', formatter: (row) => row.dtlStatus == 1 ? "已采纳" : row.dtlStatus == 2 ? "已推送" : row.dtlStatus == 3 ? "已备用" : "未采纳" },
        { istrue: true, prop: 'commentAdoptTime', label: '采纳时间', width: '140', formatter: (row) => row.commentAdoptTime == null ? null : formatTime(row.commentAdoptTime, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'commentSyncTime', label: '推送评价时间', width: '140', formatter: (row) => row.commentSyncTime == null ? null : formatTime(row.commentSyncTime, 'YYYY-MM-DD HH:mm:ss') },
        {
            istrue: true, type: 'button', label: '操作', width: '120',
            btnList: [
                { label: "修改", handle: (that, row) => that.onEditDtl(row), ishide: (that, row) => (!that.addFormEditMode || row.dtlCommentUser == '机器人') },
                { label: "移除", type: "danger", handle: (that, row) => that.onDelDtl(row), ishide: (that, row) => (!that.addFormEditMode || row.dtlCommentUser == '机器人') },
                { label: "采纳", handle: (that, row) => that.onAdoptCommentTaskDetail(row, true), ishide: (that, row) => (that.addFormEditMode || row.dtlStatus != 0 || (that.addForm.statusEnum != 1 && that.addForm.statusEnum != 4)) },
                { label: "删除", handle: (that, row) => that.onDeleteCommentTaskDtlAsync(row, true), ishide: (that, row) => (that.addFormEditMode || row.dtlStatus != 0) },
                { label: "取消采纳", handle: (that, row) => that.onAdoptCommentTaskDetail(row, false), ishide: (that, row) => (that.addFormEditMode || row.dtlStatus != 1) },
            ]
        },
    ];
    const dtlTableCols2 = [
        { istrue: true, prop: 'proCode', label: '产品ID', width: '140' },
        { istrue: true, prop: 'proName', label: '产品名称', },
        { istrue: true, prop: 'proImageUrl', label: '产品主图', width: '80', type: 'images', columnKey: "proCode" },
        //{ istrue: true, prop: 'showAmount', label: '展示价格', width: '80' },
        { istrue: true, prop: 'payAmount', label: '付款价格', width: '80' },
        { istrue: true, prop: 'goodsSku', label: 'SKU规格', width: '180' },
        { istrue: true, prop: 'buyCount', label: '购买件数', width: '80' },
        {
            istrue: true, type: 'button', label: '', width: '80',
            btnList: [
                { label: "修改", handle: (that, row) => that.onEditDtl2(row), ishide: (that, row) => !that.addFormEditMode },
                { label: "移除", type: "danger", handle: (that, row) => that.onDelDtl2(row), ishide: (that, row) => !that.addFormEditMode },
            ]
        },
    ];
    const dtlFinishTableCols = [
        { istrue: true, prop: 'commentTaskId', label: '主键', width: '150', display: false },
        { istrue: true, prop: 'commentTaskOrderDtlId', label: '主键', width: '150', display: false },
        { istrue: true, prop: 'orderNumber', label: '订单号', width: '150' },
        { istrue: true, prop: 'platformCommissionAmount', label: '平台佣金', width: '120' },
        { istrue: true, prop: 'fishPondCommissionAmount', label: '鱼塘佣金', width: '120' },
        { istrue: true, prop: 'principalAmount', label: '本金', width: '120' },
        {
            istrue: true, type: 'button', label: '', width: '60',
            btnList: [
                { label: "移除", type: "danger", handle: (that, row) => that.onDelOrder(row) },
            ]
        },
    ];
    const addCompeteIdListCols = [
        { istrue: true, prop: 'competeId', label: '竞品ID', type: "input" },
        { istrue: true, prop: 'competeCount', label: '抓取数量', width: '80', type: "inputnumber", min: 0, max: 100 },
        {
            istrue: true, type: 'button', label: '', width: '60',
            btnList: [
                { label: "移除", type: "danger", handle: (that, row) => that.onDelCompeteId(row) },
            ]
        },
    ];
    export default {
        name: "commenttasklist",
        components: { MyContainer, MyConfirmButton, cesTable, YhImgUpload, vxetablebase },
        data() {
            return {
                that: this,
                vxetableheight: '221',
                filter: {},
                tableCols: tableCols,
                tableHandles: tableHandles,
                pageLoading: false,
                list: [],
                summaryarry: {},
                total: 0,
                pager: { OrderBy: "CreatedTime", IsAsc: false },
                groupList: [],//运营组下拉
                platformList: platformlist,//平台下拉
                listLoading: false,
                sels: [],
                addFormEditMode: true,
                collatitle: '折叠',
                selectlist: [],
                activeNames: '1',
                addForm: {
                    commentTaskId: null,
                    proCode: null, proName: null, proImageUrl: null, taoKouLing: null,
                    platform: null, groupId: null, groupUserName: null,
                    startDate: null, endDate: null, hopeOrderCount: 0, statusEnum: null,
                    //hopeCommentCount: 0,
                    finishOrderCount: 0,
                    goodsCompeteIds: null,
                    dtlList: [],
                    showAmount: 0,
                    payAmount: 0,
                    remark: null,
                    goodsSku: null,
                    attachList: [],
                },
                addFormRules: {
                    proCode: [{ required: true, message: '请输入产品ID', trigger: 'blur' }],
                    proName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
                    //proImageUrl: [{ required: true, message: '请输入产品主图', trigger: 'blur' }],
                    taoKouLing: [{ required: true, message: '请输入淘口令', trigger: 'blur' }],
                    platform: [{ required: true, message: '请输入平台', trigger: 'blur' }],
                    startDate: [{ required: true, message: '请输入开始日期', trigger: 'blur' }],
                    endDate: [{ required: true, message: '请输入结束日期', trigger: 'blur' }],
                    hopeOrderCount: [{ required: true, message: '请输入预计单量', trigger: 'blur' }],
                    //hopeCommentCount: [{ required: true, message: '请输入预计评价数', trigger: 'blur' }],
                    groupId: [{ required: true, message: '请输入运营组', trigger: 'blur' }],
                    groupUserName: [{ required: true, message: '请输入运营', trigger: 'blur' }],
                    //showAmount: [{ required: true, message: '请输入展示价格', trigger: 'blur' }],
                    payAmount: [{ required: true, message: '请输入实际付款价格', trigger: 'blur' }],
                    goodsSku: [{ required: true, message: '请输入SKU规格', trigger: 'blur' }],
                },
                dialogAddFormVisible: false,
                addFromLoading: false,
                addLoading: false,
                dtlTableCols: dtlTableCols,
                dtllistLoading: false,
                dtlTableCols2: dtlTableCols2,
                dtllistLoading2: false,

                editableTabsValue: "tab2",

                dialogAddFormDtlVisible: false,
                addFromDtlLoading: false,
                addFormDtl: { rowIndex: null, goodsImageUrls: null, goodsComment: null, goodsCommentSku: null },
                addFormDtlRules: {
                    goodsImageUrls: [{ required: true, message: '请输入图片', trigger: 'blur' }],
                    goodsComment: [{ required: true, message: '请输入评价', trigger: 'blur' }],
                },

                dialogAddFormDtl2Visible: false,
                addFromDtl2Loading: false,
                addFormDtl2: { commentTaskDtlId: 0, rowIndex: null, goodsComment: null },
                addFormDtl2Rules: {
                    goodsComment: [{ required: true, message: '请输入评价', trigger: 'blur' }],
                },

                dialogAddFormAttachDtlVisible: false,
                addFromAttachDtlLoading: false,
                addFormAttachDtl: { proCode: null, proName: null, proImageUrl: null, showAmount: 0, payAmount: 0, goodsSku: null, buyCount: 0 },
                addFormAttachDtlRules: {
                    proCode: [{ required: true, message: '请输入产品ID', trigger: 'blur' }],
                    proName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
                    //proImageUrl: [{ required: true, message: '请输入产品主图', trigger: 'blur' }],
                    goodsSku: [{ required: true, message: '请输入产品规格', trigger: 'blur' }],
                    //showAmount: [{ required: true, message: '请输入展示价格', trigger: 'blur' }],
                    payAmount: [{ required: true, message: '请输入付款价格', trigger: 'blur' }],
                },
                addFormAttachDtlDisable: false,

                dialogFinishFormVisible: false,
                finishFromLoading: false,
                finishForm: {
                    commentTaskId: null,
                    proCode: null, proName: null, proImageUrl: null, taoKouLing: null,
                    platform: null, groupId: null, groupUserName: null,
                    startDate: null, endDate: null, hopeOrderCount: 0, finishOrderCount: 0,
                    dtlList: [],
                },
                finishFormRules: {
                    finishOrderCount: [{ required: true, message: '请输入完成单量', trigger: 'blur' }]
                },
                dialogFinishImportOrder: false,
                dialogFinishImportOrderLoading: false,
                finishSaveLoading: false,

                dtlFinishTableCols: dtlFinishTableCols,
                dtllistFinishLoading: false,
                dtllistFinishImportOrderLonging: false,

                addCompeteIdListCols: addCompeteIdListCols,
                addCompeteIdList: [],
                addCompeteIdDialogVisible: false,
                addCompeteIdDialogLoading: false,

                sendJtzButtonLoading: false,

                addFormProCodeLoading: false,
                addFormProCodeList: [],

                dtlSkuData: null,
                dtlSkuDataList: [],

                dtlProCodeLoading: false,
                dtlProCodeList: [],
                thisTimer: null,
                downloadProgressVisible: false,
                downloadProgressList: [],
            };
        },
        async mounted() {
            await this.getGroupList();
            await this.getlist();
        },
        methods: {
            setalldel() {
                if (this.selectlist && this.selectlist.length > 0) {
                    //console.log("打印全选值", this.selectlist)
                    this.$confirm('删除后将无法找回，是否确定删除?', '提示', {
                        confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                    }).then(async () => {
                        this.dtllistLoading = true;
                        for (let i = 0; i < this.selectlist.length; i++) {
                            if (this.selectlist[i].dtlStatus == 0) {
                                let dtlId = this.selectlist[i].commentTaskDtlId;
                                let res = await deleteCommentTaskDtlAsync({ dtlId: dtlId });
                                if (res?.success) {
                                    let curIndex = this.addForm.dtlList.findIndex(item => item.commentTaskDtlId == dtlId);
                                    if (curIndex > -1) {
                                        this.addForm.dtlList.splice(curIndex, 1);
                                    }
                                }
                            }
                        }
                        this.dtllistLoading = false;
                    }).catch(() => {
                    });
                }
                else {
                    this.$message({ type: 'error', message: '请选择!' });
                }
            },
            setallcn(isAdopt) {
                if (this.selectlist && this.selectlist.length > 0) {
                    //console.log("打印全选值", this.selectlist)
                    let cancel = '';
                    if (isAdopt == false)
                        cancel = '取消';
                    this.$confirm('确认将选中的明细' + cancel + '采纳吗?', '提示', {
                        confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                    }).then(async () => {
                        this.dtllistLoading = true;
                        for (let i = 0; i < this.selectlist.length; i++) {
                            if ((isAdopt == true && this.selectlist[i].dtlStatus == 0) || (isAdopt == false && this.selectlist[i].dtlStatus == 1)) {
                                let dtlId = this.selectlist[i].commentTaskDtlId;
                                let adopt = await adoptCommentTaskDetail({ dtlId: dtlId, isAdopt: isAdopt });
                            }
                        }

                        let param = { currentPage: 1, pageSize: 20, commentTaskId: this.selectlist[0].commentTaskId, isDtl: 1 };
                        let res = await pageCommentTaskAsync(param);
                        if (res?.success && res.data.list.length > 0) {
                            let dtlEntities = res.data.list[0].dtlEntities;
                            if (dtlEntities && dtlEntities.length > 0) {
                                dtlEntities.forEach(f => {
                                    let obj = this.addForm.dtlList.find((item) => {
                                        return item.commentTaskDtlId === f.commentTaskDtlId; //筛选出匹配的数据
                                    });
                                    if (obj) {
                                        obj.dtlStatus = f.dtlStatus;
                                        obj.commentAdoptTime = f.commentAdoptTime;
                                    }
                                });
                            }
                        }
                        this.dtllistLoading = false;
                    }).catch(() => {
                        this.dtllistLoading = false;
                    });
                }
                else {
                    this.$message({ type: 'error', message: '请选择!' });
                }
            },
            setallby(isSpare) {
                if (this.selectlist && this.selectlist.length > 0) {
                    //console.log("打印全选值", this.selectlist)
                    let cancel = '';
                    if (isSpare == false)
                        cancel = '取消';
                    this.$confirm('确认将选中的明细' + cancel + '备用吗?', '提示', {
                        confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                    }).then(async () => {
                        this.dtllistLoading = true;
                        for (let i = 0; i < this.selectlist.length; i++) {
                            if ((isSpare == true && this.selectlist[i].dtlStatus == 0) || (isSpare == false && this.selectlist[i].dtlStatus == 3)) {
                                let dtlId = this.selectlist[i].commentTaskDtlId;
                                let spare = await spareCommentTaskDetail({ dtlId: dtlId, isSpare: isSpare });
                            }
                        }

                        let param = { currentPage: 1, pageSize: 20, commentTaskId: this.selectlist[0].commentTaskId, isDtl: 1 };
                        let res = await pageCommentTaskAsync(param);
                        if (res?.success && res.data.list.length > 0) {
                            let dtlEntities = res.data.list[0].dtlEntities;
                            if (dtlEntities && dtlEntities.length > 0) {
                                dtlEntities.forEach(f => {
                                    let obj = this.addForm.dtlList.find((item) => {
                                        return item.commentTaskDtlId === f.commentTaskDtlId; //筛选出匹配的数据
                                    });
                                    if (obj) {
                                        obj.dtlStatus = f.dtlStatus;
                                    }
                                });
                            }
                        }
                        this.dtllistLoading = false;
                    }).catch(() => {
                        this.dtllistLoading = false;
                    });
                }
                else {
                    this.$message({ type: 'error', message: '请选择!' });
                }
            },
            collapasechange(val) {
                if (val.length) {
                    this.collatitle = '折叠';
                    this.vxetableheight = '221';
                } else {
                    this.collatitle = ' 展开';
                    this.vxetableheight = '550';
                }
            },
            callback(val) {
                this.selectlist = [];
                this.selectlist = val;
            },
            //获取运营组下拉
            async getGroupList() {
                let res2 = await getDirectorGroupList();
                this.groupList = res2.data?.map(item => { return { value: item.key, label: item.value }; });
            },
            //获取当前人当前运营组
            async getCurUser() {
                let res2 = await getUserInfo();
                let groid = res2?.data?.groupId;
                if (groid)
                    this.addForm.groupId = groid.toString();
                this.addForm.groupUserName = res2?.data?.nickName;
            },
            //获取查询条件
            getCondition() {
                let pager = this.$refs.pager.getPager();
                let page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }
                return params;
            },
            //分页查询
            async getlist() {
                let params = this.getCondition();
                if (params === false) {
                    return;
                }
                //params.isDtl = 1;
                this.listLoading = true
                let res = await pageCommentTaskAsync(params);
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summary;
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
            },
            //查询第一页
            async onSearch() {
                this.$refs.pager.setPage(1)
                await this.getlist()
            },
            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    let orderBy = column.prop;
                    this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            async onCloseAddForm() {
                this.addFormEditMode = true;
                let form = this.addForm;
                Object.keys(form).forEach(key => {
                    if (key != "dtlList") {
                        form[key] = null;
                    }
                });
                this.addForm.dtlList = [];
            },
            async onOpenAdd() {
                await this.getCurUser();
                this.dialogAddFormVisible = true;
                this.addForm.hopeOrderCount = 5;
                this.addForm.startDate = formatTime(new Date(), 'YYYY-MM-DD');
                this.addForm.endDate = formatTime(new Date(), 'YYYY-MM-DD');
                this.addForm.remark = "下单请备注1";
            },
            //新增编辑提交时验证
            addFormValidate: function () {
                let isValid = false;
                this.$refs.addForm.validate(valid => {
                    isValid = valid;
                });
                return isValid;
            },
            async onAddSave() {
                if (this.addForm.hopeOrderCount == 0) {
                    this.$message({ message: '预计单量必须大于0', type: 'error' });
                    return;
                }
                // if (this.addForm.showAmount == 0) {
                //     this.$message({ message: '展示价格必须大于0', type: 'error' });
                //     return;
                // }
                if (this.addForm.payAmount == 0) {
                    this.$message({ message: '实际付款价格必须大于0', type: 'error' });
                    return;
                }
                if (this.addForm.platform == null) {
                    this.$message({ message: '请填写平台', type: 'error' });
                    return;
                }
                let form = this.addForm;
                let isCheck = false;
                Object.keys(form).forEach(key => {
                    if (key != "dtlList" && key != "commentTaskId" && key != "finishOrderCount" && key != "proImageUrl" &&
                        key != "platform" && key != "goodsCompeteIds" && key != "mainTaskID" && key != "remark" && key != "showAmount" &&
                        key != "statusEnum" && key != "attachList") {//key != "goodsSku" &&
                        if (!form[key]) {
                            console.log(key)
                            isCheck = true;
                        }
                    }
                });
                if (isCheck) {
                    this.$message({ message: '请输入必填数据', type: 'error' });
                    return;
                }
                this.addLoading = true;
                let res = await saveCommentTaskAsync(this.addForm);
                this.addLoading = false;
                if (!res?.success) {
                    return;
                }
                this.$message({
                    message: this.$t('保存成功'),
                    type: 'success'
                })
                this.dialogAddFormVisible = false;
                await this.onSearch();
            },
            async onAddDtl() {
                this.dialogAddFormDtlVisible = true;
            },
            async onAddDtlOK() {
                if (this.addFormDtl.goodsComment == null || this.addFormDtl.goodsComment == "" || this.addFormDtl.goodsImageUrls == null || this.addFormDtl.goodsImageUrls == "") {
                    this.$message({ message: '请输入图片及评价', type: 'error' });
                    return;
                }
                if (this.addFormDtl.goodsComment.length > 2000) {
                    this.$message({ message: '评价超长', type: 'error' });
                    return;
                }
                if (this.addFormDtl.rowIndex > 0) {
                    this.addForm.dtlList.forEach(f => {
                        if (f.rowIndex == this.addFormDtl.rowIndex) {
                            f.goodsImageUrls = this.addFormDtl.goodsImageUrls;
                            f.goodsCommentSku = this.addFormDtl.goodsCommentSku;
                            f.goodsComment = this.addFormDtl.goodsComment;
                        }
                    });
                }
                else {
                    let rowindex = this.addForm.dtlList.length + 1;
                    let curIndex = this.addForm.dtlList.findIndex(item => item.rowIndex == rowindex);
                    if (curIndex > -1) {
                        let maxIndex = rowindex;
                        this.addForm.dtlList.forEach(f => {
                            if (f.rowIndex > maxIndex) {
                                maxIndex = f.rowIndex;
                            }
                        });
                        rowindex = (maxIndex + 1);
                    }
                    this.addForm.dtlList.push({
                        rowIndex: rowindex, goodsImageUrls: this.addFormDtl.goodsImageUrls,
                        goodsComment: this.addFormDtl.goodsComment, goodsCommentSku: this.addFormDtl.goodsCommentSku,
                    });
                }
                this.dialogAddFormDtlVisible = false;
            },
            async onEditDtl(row) {
                this.dialogAddFormDtlVisible = true;
                this.addFormDtl.rowIndex = row.rowIndex;
                this.addFormDtl.goodsImageUrls = row.goodsImageUrls;
                this.addFormDtl.goodsComment = row.goodsComment;
                this.addFormDtl.goodsCommentSku = row.goodsCommentSku;
            },
            async onDelDtl(row) {
                this.$confirm('确认移除?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    let thisIndex = -1;
                    let curIndex = -1;
                    this.addForm.dtlList.forEach(f => {
                        thisIndex++;
                        if (f.rowIndex == row.rowIndex) {
                            curIndex = thisIndex;
                        }
                    });
                    if (curIndex > -1) {
                        this.addForm.dtlList.splice(curIndex, 1);
                        //重新计算序号
                        let newIndex = 0;
                        this.addForm.dtlList.forEach(f => {
                            newIndex++;
                            f.rowIndex = newIndex;
                        });
                    }
                }).catch(() => {
                });
            },
            async onCloseAddFormDtl() {
                let form = this.addFormDtl;
                Object.keys(form).forEach(key => {
                    form[key] = null;
                });
            },
            async onCloseAddFormDtl2() {
                let form = this.addFormDtl2;
                Object.keys(form).forEach(key => {
                    form[key] = null;
                });
            },
            async onOpenEdit(row, mode) {
                this.addFormEditMode = mode;
                this.dialogAddFormVisible = true;

                let param = { currentPage: 1, pageSize: 20, commentTaskId: row.commentTaskId, isDtl: 1, IsAttachDtl: 1 };
                this.addFromLoading = true
                let res = await pageCommentTaskAsync(param);
                if (res?.success && res.data.list.length > 0) {
                    let editData = res.data.list[0];
                    this.addForm = {
                        commentTaskId: editData.commentTaskId,
                        proCode: editData.proCode, proName: editData.proName, proImageUrl: editData.proImageUrl, taoKouLing: editData.taoKouLing,
                        platform: editData.platform, groupId: editData.groupId.toString(), groupUserName: editData.groupUserName,
                        startDate: editData.startDate, endDate: editData.endDate,
                        hopeOrderCount: editData.hopeOrderCount,
                        //hopeCommentCount: editData.hopeCommentCount??0,
                        finishOrderCount: editData.finishOrderCount ?? 0,
                        goodsCompeteIds: editData.goodsCompeteIds,
                        dtlList: editData.dtlEntities,
                        showAmount: editData.showAmount ?? 0,
                        payAmount: editData.payAmount ?? 0,
                        remark: editData.remark,
                        goodsSku: editData.goodsSku,
                        statusEnum: editData.statusEnum,
                        attachList: editData.attachDtlEntities,
                    }

                    let curIndex = this.addFormProCodeList.findIndex(item => item.proCode == editData.proCode);
                    if (curIndex == -1) {
                        this.addFormProCodeList.push({
                            proCode: editData.proCode,
                            title: editData.proName, images: editData.proImageUrl, platform: editData.platform,
                            proCodeName: "【" + editData.proCode + "】" + editData.proName
                        });
                    }

                    if (editData.dtlEntities && editData.dtlEntities.length > 0) {
                        this.dtlSkuDataList = [];
                        editData.dtlEntities.forEach(f => {
                            let obj = this.dtlSkuDataList.find((item) => {
                                return item.value === f.goodsCommentSku; //筛选出匹配的数据
                            });
                            if (!obj) {
                                this.dtlSkuDataList.push({ value: f.goodsCommentSku, label: f.goodsCommentSku })
                            }
                        });
                    }
                    else {
                        this.dtlSkuData = null;
                        this.dtlSkuDataList = [];
                    }
                }
                this.addFromLoading = false
            },
            async onDelete(row) {
                this.$confirm('确定要删除吗?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    const res = await deleteCommentTaskAsync({ commentTaskId: row.commentTaskId })
                    if (res?.success) {
                        this.$message({ type: 'success', message: '删除成功!' });
                    }
                    this.onSearch()
                }).catch(() => {
                    this.$message({ type: 'info', message: '已取消删除' });
                });
            },
            async onRelease(row) {
                this.listLoading = true;
                let res = await releaseCommentTaskAsync({ commentTaskId: row.commentTaskId, release: true });
                this.listLoading = false;
                if (res?.success) {
                    this.$message({ type: 'success', message: '发布成功!' });
                    this.onSearch();
                }
            },
            async onNoRelease(row) {
                this.listLoading = true;
                let res = await releaseCommentTaskAsync({ commentTaskId: row.commentTaskId, release: false });
                this.listLoading = false;
                if (res?.success) {
                    this.$message({ type: 'success', message: '取消发布成功!' });
                    this.onSearch();
                }
            },
            async onReceive(row) {
                this.listLoading = true;
                let res = await receiveCommentTaskAsync({ commentTaskId: row.commentTaskId });
                this.listLoading = false;
                if (res?.success) {
                    this.$message({ type: 'success', message: '认领成功!' });
                    this.onSearch();
                }
            },
            async onCloseFinishForm() {
                let form = this.finishForm;
                Object.keys(form).forEach(key => {
                    if (key != "dtlList") {
                        form[key] = null;
                    }
                });
                this.finishForm.dtlList = [];
            },
            async onOpenFinish(row) {
                this.dialogFinishFormVisible = true;
                let param = { currentPage: 1, pageSize: 20, commentTaskId: row.commentTaskId, isDtl: 1, IsOrderDtl: 1 };
                this.finishFromLoading = true
                let res = await pageCommentTaskAsync(param);
                this.finishFromLoading = false
                if (res?.success && res.data.list.length > 0) {
                    let editData = res.data.list[0];
                    this.finishForm = {
                        commentTaskId: editData.commentTaskId,
                        proCode: editData.proCode, proName: editData.proName, proImageUrl: editData.proImageUrl, taoKouLing: editData.taoKouLing,
                        platform: editData.platform, groupId: editData.groupId.toString(), groupUserName: editData.groupUserName,
                        startDate: editData.startDate, endDate: editData.endDate,
                        hopeOrderCount: editData.hopeOrderCount,
                        //hopeCommentCount: editData.hopeCommentCount??0,
                        finishOrderCount: editData.finishOrderCount ?? 0,
                        goodsCompeteIds: editData.goodsCompeteIds,
                        orderDtlList: editData.orderDtlEntities,
                        showAmount: editData.showAmount ?? 0,
                        payAmount: editData.payAmount ?? 0,
                        remark: editData.remark,
                        goodsSku: editData.goodsSku,
                        statusEnum: editData.statusEnum,
                        attachList: editData.attachDtlEntities,
                    }
                }
            },
            async onImportOrder() {
                this.dialogFinishImportOrder = true;
            },
            async onSaveFinish(row) {
                if (this.finishForm.finishOrderCount == 0) {
                    this.$message({ message: '完成单量必须大于0', type: 'error' });
                    return;
                }
                this.finishSaveLoading = true;
                let res = await finishCommentTaskAsync(this.finishForm);
                this.finishSaveLoading = false;
                if (!res?.success) {
                    return;
                }
                this.$message({
                    message: this.$t('操作成功【已完成】'),
                    type: 'success'
                })
                this.dialogFinishFormVisible = false;
                await this.onSearch();
            },
            async onImportOrderModel() {
                window.open("/static/excel/productalllink/全链路-评价任务-导入订单模板.xlsx", "_blank");
            },
            async uploadFile2(item) {
                let commentTaskId = this.finishForm.commentTaskId;
                const form = new FormData();
                form.append("upfile", item.file);
                form.append("commentTaskId", commentTaskId);
                this.dtllistFinishImportOrderLonging = true;
                let res = await importCommentTaskOrderAsync(form);
                this.dtllistFinishImportOrderLonging = false;
                if (res?.success) {
                    this.$message({ message: '导入成功', type: "success" });
                    this.dialogFinishImportOrder = false;
                    //重新加载数据
                    this.onLoadOrderDtl(commentTaskId);
                }
            },
            async onLoadOrderDtl(commentTaskId) {
                let param = { currentPage: 1, pageSize: 20, commentTaskId: commentTaskId, IsOrderDtl: 1 };
                this.dtllistFinishLoading = true;
                let res = await pageCommentTaskAsync(param);
                this.dtllistFinishLoading = false;
                if (res?.success && res.data.list.length > 0) {
                    let editData = res.data.list[0];
                    this.finishForm.finishOrderCount = editData.finishOrderCount;
                    this.finishForm.orderDtlList = editData.orderDtlEntities;
                }
            },
            async uploadSuccess2(response, file, fileList) {
                fileList.splice(fileList.indexOf(file), 1);
            },
            async onSubmitupload2() {
                this.$refs.upload2.submit()
            },
            async onDelOrder(row) {
                this.$confirm('确认移除?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    this.dtllistFinishLoading = true;
                    let res = await deleteCommentTaskOrderAsync({ commentTaskOrderDtlId: row.commentTaskOrderDtlId });
                    this.dtllistFinishLoading = false;
                    //重新加载数据
                    this.onLoadOrderDtl(row.commentTaskId);
                }).catch(() => {
                });
            },
            async onOpenDownload() {
                let res = await getCommentTaskDownloadProgressAsync();
                if (res?.success) {
                    this.downloadProgressList = res.data;
                }

                clearInterval(this.thisTimer);
                this.thisTimer = null;
                this.thisTimer = setInterval(() => {
                    this.getCommentTaskDownloadProgress();
                }, 2000);
                this.downloadProgressVisible = true;
            },
            async onDownload(row) {
                if (!row.dtlCount) {
                    this.$message({ message: '没有评价。', type: "error" });
                    return;
                }
                this.$message({ message: '正在打包中...，可点击“查看打包进度”按钮查看即时打包进度。', type: "success" });
                //this.onOpenDownload();
                // this.listLoading = true;
                let res = await exportCommentTaskAsync({ commentTaskId: row.commentTaskId });

                // this.listLoading = false;
                // const aLink = document.createElement("a");
                // let blob = new Blob([res.data], { type: "application/zip" })
                // aLink.href = URL.createObjectURL(blob)
                // aLink.setAttribute('download', row.proName + new Date().toLocaleString() + '.zip')
                // aLink.click();
            },
            async onDownloadClose() {
                clearInterval(this.thisTimer);
                this.thisTimer = null;
            },
            async getCommentTaskDownloadProgress() {
                try {
                    let res = await getCommentTaskDownloadProgress2Async(this.downloadProgressList);
                    if (this.downloadProgressList.length > 0 && res && res.length > 0) {
                        res.forEach(f => {
                            let obj = this.downloadProgressList.find((item) => {
                                return item.id === f.id; //筛选出匹配的数据
                            });
                            if (obj) {
                                obj.countRate = f.countRate ?? 0;
                            }
                        });
                    }
                    else {
                        clearInterval(this.thisTimer);
                        this.thisTimer = null;
                    }
                } catch (error) {
                    clearInterval(this.thisTimer);
                    this.thisTimer = null;
                }
            },
            async onDownloadGet(row) {
                if (row.countRate < 100) {
                    this.$message({ message: '打包中...请稍后', type: "info" });
                    return;
                }
                window.open(process.env.VUE_APP_DOMAIN + ":8030/operatemanage" + row.downloadLink, "_blank")
            },
            async onShowCompeteId() {
                this.addCompeteIdDialogVisible = true;
                this.addCompeteIdDialogLoading = true;
                if (this.addForm.goodsCompeteIds != "" && this.addForm.goodsCompeteIds != null) {
                    let competeIdSplit = this.addForm.goodsCompeteIds.split(',');
                    if (competeIdSplit.length > 0) {
                        competeIdSplit.forEach((f) => {
                            let of = f.indexOf('（');
                            let ofl = f.indexOf('）');
                            if (of > -1 && ofl > -1) {
                                let co = f.substring(0, of);
                                let col = f.substring(of + 1, ofl);
                                this.addCompeteIdList.push({
                                    competeId: co, competeCount: col
                                });
                            }
                            else {
                                this.addCompeteIdList.push({
                                    competeId: f, competeCount: 30
                                });
                            }
                        });
                    }
                }
                else {
                    this.onAddCompeteId();
                }
                this.addCompeteIdDialogLoading = false;
            },
            async onAddCompeteId() {
                this.addCompeteIdList.push({
                    competeId: "", competeCount: 30
                });
            },
            async onDelCompeteId(row) {
                let rIndex = -1;
                let curIndex = -1;
                this.addCompeteIdList.forEach(f => {
                    rIndex++;
                    if (f.competeId == row.competeId) {
                        curIndex = rIndex;
                        return;
                    }
                });
                if (curIndex > -1) {
                    this.addCompeteIdList.splice(curIndex, 1);
                }
            },
            async onSaveCompeteId() {
                // if (this.addCompeteIdList.length <= 0) {
                //     this.$message({ message: '请新增至少一个竞品ID', type: "error" });
                //     return;
                // }
                let addCompeteIds = [];
                let errRow = 0;
                this.addCompeteIdList.forEach((f) => {
                    if (f.competeId.indexOf(",") > -1 || f.competeId == "" || f.competeId == null) {
                        errRow++;
                    }
                    if (f.competeId.replace(/\s/g, "") == "") {
                        errRow++;
                    }
                    if (!f.competeCount || f.competeCount <= 0) {
                        errRow++;
                    }
                });
                if (errRow > 0) {
                    this.$message({ message: '竞品ID格式错误，不允许空或空格，且抓取数量必须大于0', type: "error" });
                    return;
                }
                this.addCompeteIdList.forEach((f) => {
                    let t = f.competeId.replace(/\s/g, "") + "（" + f.competeCount.toString() + "）";
                    addCompeteIds.push(t);
                });
                this.addForm.goodsCompeteIds = addCompeteIds.join(',');
                this.addCompeteIdDialogVisible = false;
            },
            async onCloseCompeteIdDialog() {
                this.addCompeteIdList = [];
            },
            async onSetSyncStatus(row, sync) {
                let ttile = sync ? "确定开启同步吗？" : "确定关闭同步吗？";
                this.$confirm(ttile, '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    let res = await openCloseCommentTaskDtlSync({ commentTaskId: row.commentTaskId, sync: sync });
                    if (res?.success) {
                        this.onSearch();
                    }
                }).catch(() => {
                });
            },
            async onSendJtz(commentTaskId, type)//推送到酒坛子,type:1.推任务，2.推评价，3.都推
            {
                this.$confirm("确定推送至酒坛子吗？", '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    this.pageLoading = true;
                    this.sendJtzButtonLoading = true;
                    let res = await sendJiuTanZi({ commentTaskId: commentTaskId, type: type });
                    this.pageLoading = false;
                    this.sendJtzButtonLoading = false;
                    if (res?.success) {
                        this.$message({ message: '推送成功', type: "success" });
                        this.onSearch();
                    }
                }).catch(() => {
                });
            },
            async onCloseAddFormAttachDtl() {
                let form = this.addFormAttachDtl;
                Object.keys(form).forEach(key => {
                    form[key] = null;
                });
                form.showAmount = 0;
                form.payAmount = 0;
                form.buyCount = 1;
            },
            async onAddDtl2() {
                this.addFormAttachDtlDisable = false;
                this.dialogAddFormAttachDtlVisible = true;
            },
            async onAddDtlOK2() {
                if (this.addForm.attachList == null) this.addForm.attachList = [];
                if (this.addForm.attachList.length >= 4) {
                    this.$message({ message: '最多4个附件商品', type: "error" });
                    return;
                }
                if (!this.addFormAttachDtl.payAmount) {
                    this.$message({ message: '付款价格必须大于0', type: "error" });
                    return;
                }
                if (!this.addFormAttachDtl.proCode || !this.addFormAttachDtl.proName ||
                    !this.addFormAttachDtl.payAmount || !this.addFormAttachDtl.goodsSku) {//!this.addFormAttachDtl.showAmount || !this.addFormAttachDtl.proImageUrl ||
                    this.$message({ message: '请输入必填字段', type: "error" });
                    return;
                }
                let isHave = false;
                if (this.addForm.attachList.length > 0) {
                    this.addForm.attachList.forEach(f => {
                        if (this.addFormAttachDtl.proCode == f.proCode) {
                            f.proCode = this.addFormAttachDtl.proCode;
                            f.proName = this.addFormAttachDtl.proName;
                            f.proImageUrl = this.addFormAttachDtl.proImageUrl;
                            f.showAmount = this.addFormAttachDtl.showAmount;
                            f.payAmount = this.addFormAttachDtl.payAmount;
                            f.goodsSku = this.addFormAttachDtl.goodsSku;
                            isHave = true;
                        }
                    });
                }
                if (isHave == false) {
                    this.addFormAttachDtl.buyCount = 1;
                    this.addForm.attachList.push(
                        {
                            proCode: this.addFormAttachDtl.proCode, proName: this.addFormAttachDtl.proName,
                            proImageUrl: this.addFormAttachDtl.proImageUrl, goodsSku: this.addFormAttachDtl.goodsSku,
                            showAmount: this.addFormAttachDtl.showAmount, payAmount: this.addFormAttachDtl.payAmount,
                        });
                }
                this.dialogAddFormAttachDtlVisible = false;
            },
            async onEditDtl2(row) {
                this.addFormAttachDtlDisable = true;
                this.dialogAddFormAttachDtlVisible = true;
                this.addFormAttachDtl.proCode = row.proCode;
                this.addFormAttachDtl.proName = row.proName;
                this.addFormAttachDtl.proImageUrl = row.proImageUrl;
                this.addFormAttachDtl.showAmount = row.showAmount;
                this.addFormAttachDtl.payAmount = row.payAmount;
                this.addFormAttachDtl.goodsSku = row.goodsSku;

                let curIndex = this.dtlProCodeList.findIndex(item => item.proCode == row.proCode);
                if (curIndex == -1) {
                    this.dtlProCodeList.push({
                        proCode: row.proCode,
                        title: row.proName, images: row.proImageUrl,
                        proCodeName: "【" + row.proCode + "】" + row.proName
                    });
                }
            },
            async onDelDtl2(row) {
                this.$confirm('确认移除?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    let thisIndex = -1;
                    let curIndex = -1;
                    this.addForm.attachList.forEach(f => {
                        thisIndex++;
                        if (f.proCode == row.proCode) {
                            curIndex = thisIndex;
                        }
                    });
                    if (curIndex > -1) {
                        this.addForm.attachList.splice(curIndex, 1);
                    }
                }).catch(() => {
                });
            },
            async onGetTaoBaoOrTmallMainImage() {
                let res = await getTaoBaoOrTmallMainImage({ proCode: this.addForm.proCode });
            },
            async onAddFormProCodeMethod(query) {
                if (query !== '') {
                    this.addFormProCodeLoading = true;
                    let res = await getCommentProductListByCode({ proCode: query });
                    if (res?.success) {
                        this.addFormProCodeList = res.data;
                        if (this.addFormProCodeList && this.addFormProCodeList.length > 0) {
                            this.addFormProCodeList.forEach(f => {
                                f.proCodeName = "【" + f.proCode + "】" + f.title;
                            });
                        }
                    }
                    this.addFormProCodeLoading = false;
                } else {
                    this.addFormProCodeList = [];
                }
            },
            async onAddFormProCodeChange() {
                if (this.addFormProCodeList && this.addFormProCodeList.length > 0) {
                    this.addFormProCodeList.forEach(f => {
                        if (this.addForm.proCode == f.proCode) {
                            this.addForm.proName = f.title;
                            this.addForm.platform = f.platform;
                            if (f.images) {
                                let im = f.images.split('/');
                                if (im.length > 0) {
                                    let m = { name: im[im.length - 1], url: f.images };
                                    this.addForm.proImageUrl = JSON.stringify([m]);
                                }
                            }
                            else {
                                this.addForm.proImageUrl = null;
                            }
                        }
                    });
                }
            },
            async onUpdateDtlComment(row) {
                this.dialogAddFormDtl2Visible = true;
                this.addFormDtl2 = {
                    commentTaskDtlId: row.commentTaskDtlId,
                    rowIndex: row.rowIndex,
                    goodsComment: row.goodsComment
                };
            },
            async onUpdateDtlCommentOK() {
                if (!this.addFormDtl2.goodsComment) {
                    this.$message({ message: '请填写正确的评价内容', type: "error" });
                    return;
                }
                let res = await updateCommentTaskDetail(this.addFormDtl2);
                if (res?.success) {
                    this.dtllistLoading = true;
                    this.addForm.dtlList.forEach(f => {
                        if (this.addFormDtl2.commentTaskDtlId == f.commentTaskDtlId) {
                            f.goodsComment = this.addFormDtl2.goodsComment;
                            return;
                        }
                    });
                    this.dtllistLoading = false;
                    this.dialogAddFormDtl2Visible = false;
                }
            },
            async onAdoptCommentTaskDetail(row, isAdopt) {
                let adopt = await adoptCommentTaskDetail({ dtlId: row.commentTaskDtlId, isAdopt: isAdopt });
                if (adopt?.success) {
                    this.dtllistLoading = true;
                    let res = await getCommentTaskFirstDetailByDtlId({ dtlId: row.commentTaskDtlId });
                    this.dtllistLoading = false;
                    if (res?.success && res?.data && this.addForm.dtlList.length > 0) {
                        this.addForm.dtlList.forEach(f => {
                            if (row.commentTaskDtlId == f.commentTaskDtlId) {
                                f.dtlStatus = res.data.dtlStatus;
                                f.commentAdoptTime = res.data.commentAdoptTime;
                            }
                        });
                    }
                }
            },
            async onDeleteCommentTaskDtlAsync(row) {
                this.$confirm('删除后将无法找回，是否确定删除?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    let adopt = await deleteCommentTaskDtlAsync({ dtlId: row.commentTaskDtlId });
                    if (adopt?.success) {
                        let curIndex = this.addForm.dtlList.findIndex(item => item.commentTaskDtlId == row.commentTaskDtlId);
                        if (curIndex > -1) {
                            this.addForm.dtlList.splice(curIndex, 1);
                        }
                    }
                }).catch(() => {
                });
            },
            async onDtlSkuDataChange(value) {
                let param = { currentPage: 1, pageSize: 20, commentTaskId: this.addForm.commentTaskId, isDtl: 1 };
                this.dtllistLoading = true
                let res = await pageCommentTaskAsync(param);
                this.dtllistLoading = false
                if (res?.success && res.data.list.length > 0) {
                    let editData = res.data.list[0];
                    if (editData.dtlEntities && editData.dtlEntities.length > 0) {
                        if (!value) {
                            this.addForm.dtlList = editData.dtlEntities;
                        }
                        else {
                            let leList = [];
                            editData.dtlEntities.forEach(f => {
                                if (f.goodsCommentSku == value) {
                                    leList.push(f);
                                }
                            });
                            this.addForm.dtlList = leList;
                        }
                    }
                }
            },
            async onDtlProCodeMethod(query) {
                if (query !== '') {
                    let res = await getCommentProductListByCode({ proCode: query });
                    if (res?.success) {
                        this.dtlProCodeList = res.data;
                        if (this.dtlProCodeList && this.dtlProCodeList.length > 0) {
                            this.dtlProCodeList.forEach(f => {
                                f.proCodeName = "【" + f.proCode + "】" + f.title;
                            });
                        }
                    }
                } else {
                    this.dtlProCodeList = [];
                }
            },
            async onDtlProCodeChange() {
                if (this.dtlProCodeList && this.dtlProCodeList.length > 0) {
                    this.dtlProCodeList.forEach(f => {
                        if (this.addFormAttachDtl.proCode == f.proCode) {
                            this.addFormAttachDtl.proName = f.title;
                            if (f.images) {
                                let im = f.images.split('/');
                                if (im.length > 0) {
                                    let m = { name: im[im.length - 1], url: f.images };
                                    this.addFormAttachDtl.proImageUrl = JSON.stringify([m]);
                                }
                            }
                            else {
                                this.addFormAttachDtl.proImageUrl = null;
                            }
                        }
                    });
                }
            },
            async onDtlListSortChange(col) {
                if (col.prop != "goodsImageUrls")
                    return;
                if (!this.addForm.dtlList)
                    return;
                if (this.addForm.dtlList.length <= 0)
                    return;
                if (this.addFormEditMode != false) {
                    this.$message({ message: '对不起，非详情模式目前不支持排序', type: "info" });
                    return;
                }
                this.addForm.dtlList = this.addForm.dtlList.sort((a, b) => {
                    return col.order == "descending" ? b.goodsImageCount - a.goodsImageCount : a.goodsImageCount - b.goodsImageCount
                })
            },
        }
    }
</script>
