<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="数据日期">
                    <el-date-picker style="width: 230px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :picker-options="pickerOptions"></el-date-picker>
                </el-form-item>
                <el-form-item label="运营组：">
                    <el-select filterable v-model="filter.groupId" placeholder="运营组" style="width: 110px" clearable>
                        <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value"
                            :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item label="店铺名称:">
                    <el-select filterable v-model="filter.shopCode" placeholder="请选择店铺" clearable style="width: 120px">
                        <el-option v-for="item in shopList" :key="item.id" :label="item.shopName"
                            :value="item.shopCode" />
                    </el-select>
                </el-form-item>
                <el-form-item label="店铺创建时间">
                    <el-date-picker style="width: 230px" v-model="filter.timerange2" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="银行卡:">
                    <el-select filterable v-model="filter.isBundledBankCard" placeholder="请选择银行卡" clearable style="width: 120px">
                        <el-option label="是" value="是"></el-option>
                        <el-option label="否" value="否"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="startImport" v-if="checkPermission('api:operatemanage:PddBackGroundManage:ImportPingduoduoFundDetail')">导入</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="importTask" v-if="checkPermission('api:operatemanage:PddBackGroundManage:ImportPingduoduoFundDetail')">导入记录</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'
            :tableCols='tableCols' :isSelection="false" :tableHandles='tableHandles' :loading="listLoading" :summaryarry="summaryarry"
            @summaryClick='onsummaryClick' :showsummary='true' :isSelectColumn='true' tablekey="pddfunddetail20241027">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="40%">
            <div slot="title" class="header-title">
                <span class="title-text"><span>导入数据</span></span>
                <span class="title-close"><el-button @click="downLoadTemplateFile">下载模版</el-button></span>
            </div>
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="4" action
                    accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :file-list="fileList">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                        @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

        <!-- 趋势图 -->
        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" 
            :close-on-click-model="false" v-dialogDrag>
            <el-date-picker style="width: 230px" v-model="filter2.timerange" type="daterange" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                :picker-options="pickerOptions" @change="selectMapTime" >
            </el-date-picker>
            <div>
                <span>
                    <buschar ref="buschar" v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
        
        <el-dialog title="导入记录" :visible.sync="importRecordVisible" width="60%" v-dialogDrag>
            <!-- <ces-table :id="'pddfunddetail202412101050'" ref="table2" :that='that' :isIndex="true" :hasexpand="true" 
                :tableData='tableData2' :tableCols='tableCols2' :isSelection="false" :isSelectColumn="false" 
                style="width: 100%;margin: 0; height: 410px" :height="'100%'" >"
            </ces-table> -->
            <div style="height: 500px;">
            <vxetablebase :id="'pddfunddetail202412101050'" ref="table2" :that='that' :isIndex='true' :hasexpand='true' :toolbarshow="false" 
                :tableData='tableData2' :tableCols='tableCols2' :isSelection="false" 
                :isSelectColumn="false" style="width: 100%;margin: 0" :height="'100%'">
            </vxetablebase>
            </div>
            <template #footer>
                <my-pagination ref="pager2" :total="total2" @get-page="getImportTaskList" />
                <!-- <my-pagination ref="pager2" :total="total2" @page-change="Pagechange2" @size-change="Sizechange2" /> -->
            </template>
        </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getPingduoduoFundDetailList, importPingduoduoFundDetailAsync,exportPingduoduoFundDetailList, getPingduoduoFundDetailListMap, 
    importPingduoduoFundDetail, getImportPingduoduoFundDetailTaskLog } from '@/api/operatemanage/pddbgmanage/pddbgmanage'
import { getDirectorGroupList,getList as getshopList } from '@/api/operatemanage/base/shop'
import dayjs from "dayjs";
// import { formatTime } from "@/utils";
import { formatTime ,downloadLink } from "@/utils/tools";
import buschar from "@/components/Bus/buschar";

const tableCols = [
    { istrue: true, prop: 'date', label: '数据日期', tipmesg: '数据日期', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '最新同步时间', tipmesg: '最新同步时间', width: '200', sortable: 'custom', formatter: (row) => formatTime(row.createdTime, "YYYY-MM-DD HH:mm:ss") },
    { istrue: true, prop: 'groupId', label: '运营组', tipmesg: '', width: '120', sortable: 'custom', formatter: (row) => row.groupName },
    { istrue: true, prop: 'shopName', label: '店铺名称', tipmesg: '', width: '250', sortable: 'custom', },
    { istrue: true, prop: 'shopCreateTime', label: '店铺创建时间', tipmesg: '', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'shopAmount', label: '店铺资金',  width: '130', tipmesg: '', sortable: 'custom',summaryEvent:true, type:'button', 
    display: true, style: "color:blue;cursor:pointer;", type: 'click', handle: (that, row) => that.showchart(row), formatter: (row) => {
        const value = row.shopAmount;
        if (value > 100 && Math.abs(value % 1) === 0.5) {
            let roundedValue = Math.round(value);
            if (roundedValue % 2 !== 0) {
                // 如果四舍五入结果不是偶数，调整为最近的偶数
                roundedValue = Math.floor(value);
                }
                return roundedValue;
            }
        }
    },
    { istrue: true, prop: 'canUseAmount', label: '可用资金',  width: '130', tipmesg: '',sortable: 'custom',summaryEvent:true, 
    display: true, style: "color:blue;cursor:pointer;", type: 'click', handle: (that, row) => that.showchart(row), formatter: (row) => {
        const value = row.canUseAmount;
        if (value > 100 && Math.abs(value % 1) === 0.5) {
            let roundedValue = Math.round(value);
            if (roundedValue % 2 !== 0) {
                // 如果四舍五入结果不是偶数，调整为最近的偶数
                roundedValue = Math.floor(value);
                }
                return roundedValue;
            }
        }
    },
    { istrue: true, prop: 'feezeAmount', label: '冻结资金', width: '130', tipmesg: '', sortable: 'custom',summaryEvent:true, 
    display: true, style: "color:blue;cursor:pointer;", type: 'click', handle: (that, row) => that.showchart(row), formatter: (row) => {
        const value = row.feezeAmount;
        if (value > 100 && Math.abs(value % 1) === 0.5) {
            let roundedValue = Math.round(value);
            if (roundedValue % 2 !== 0) {
                // 如果四舍五入结果不是偶数，调整为最近的偶数
                roundedValue = Math.floor(value);
                }
                return roundedValue;
            }
        }
    },
    { istrue: true, prop: 'feezeAmountDifference', label: '冻结资金差额', width: '130', tipmesg: '', sortable: 'custom',summaryEvent:true, 
    display: true, style: "color:blue;cursor:pointer;", type: 'click', handle: (that, row) => that.showchart(row), formatter: (row) => {
        const value = row.feezeAmountDifference;
        if (value > 100 && Math.abs(value % 1) === 0.5) {
            let roundedValue = Math.round(value);
            if (roundedValue % 2 !== 0) {
                // 如果四舍五入结果不是偶数，调整为最近的偶数
                roundedValue = Math.floor(value);
                }
                return roundedValue;
            }
        }
    },
    { istrue: true, prop: 'isBundledBankCard', label: '银行卡', width: '130', tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'endBalanceAmount', label: '期末余额', width: '130', tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'isCanCashWithdrawal', label: '是否可提现', width: '100', tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'lastWithdrawalTime ', label: '最后提现时间', width: '160', tipmesg: '', sortable: 'custom' ,formatter: (row) => row.lastWithdrawalTime==null?"-":formatTime(row.lastWithdrawalTime, "YYYY-MM-DD HH:mm:ss") },
];
const tableCols2 = [
    { istrue: true, width: '100', align: 'center', label: '操作人', prop: 'operater' },
    { istrue: true,  align: 'center', label: '操作时间', prop: 'operateTime', formatter: (row) => formatTime(row.operateTime, "YYYY-MM-DD HH:mm:ss") },
    { istrue: true, width: '100', label: '下载', prop: 'fileUrl', type: 'button', btnList: [
        {
            label: '下载',
            handle: (that, row) => { that.downloadLink(row.fileUrl, row.title); },
            ishide: (that, row) => !row.fileUrl,
        },
    ]},
];

const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() }
];

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunHanAdminPddFundDetail',
    components: { container, cesTable, MyConfirmButton, buschar, vxetablebase },
    data() {
        return {
            buscharDialog: { visible: false, title: "", data: [] },
            that: this,
            filter: {
                startTime: null,
                endTime: null,
                groupId: null,
                shopCode: null,
                timerange: [startDate, endDate],
                timerange2:[], 
                isBundledBankCard: null,
            },
            filter2: {
                startTime: null,
                endTime: null,
                startTime2: null,
                endTime2: null,
                groupId: null,
                shopCode: null,
                timerange: [startDate, endDate],
                timerange2:[], 
                isBundledBankCard: null,
            },//趋势图过滤条件
            list: [],
            shopList: [],
            directorGroupList: [],
            pager: { OrderBy: "date", IsAsc: false },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            sels: [],
            uploadLoading: false,
            dialogVisible: false,
            listLoading: false,
            fileList: [],
            summaryarry:{},
            tableCols2: tableCols2,
            tableData2: [],
            total2: 0,
            importRecordVisible: false,
            importRecordFilter: {
                currentPage: 1,
                pageSize: 50,
                orderBy: "createdTime",
                isAsc: false,
            },
        };
    },

    async mounted() {
        await this.onSearch()
        await this.loadData()
    },

    methods: {
        downloadLink:downloadLink,
        //获取店铺
        async loadData() {
            this.categorylist = []
            const res1 = await getshopList({ platform: 2, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list;
            let res3 = await getDirectorGroupList({})
            this.directorGroupList = res3.data
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            if(this.pager.OrderBy==null){
                this.pager.OrderBy="date";
                this.pager.IsAsc=false;
            }
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            this.filter.startTime2 = null;
            this.filter.endTime2 = null;
            if (this.filter.timerange2) {
                this.filter.startTime2 = this.filter.timerange2[0];
                this.filter.endTime2 = this.filter.timerange2[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getPingduoduoFundDetailList(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
            this.summaryarry=res.data.summary;
        },
        async nSearch() {
            await this.getlist()
        },
        //开始导入
        startImport() {
            this.fileList = [];
            this.uploadLoading = false;
            this.dialogVisible = true;
            this.$refs.upload.clearFiles();
        },
        //取消导入
        cancelImport() {
            this.dialogVisible = false;
        },
        async submitUpload() {
            console.log(this.fileList);
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请先选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit = true;
            this.uploadLoading = true;
            this.$refs.upload.submit();
        },
        async uploadFile(item) {
            if (!this.fileHasSubmit) {
                return false;
            }
            this.fileHasSubmit = false;
            this.uploadLoading = true;
            const form = new FormData();
            // form.append("token", this.token);
            form.append("upfile", item.file);
            // const res = await importPingduoduoFundDetailAsync(form);
            const res = await importPingduoduoFundDetail(form);
            if (res.code == 1) {
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
                this.dialogVisible = false;
            }
            this.uploadLoading = false;
            this.$refs.upload.clearFiles();
        },
        async uploadChange(file, fileList) {
            let files = [];
            files.push(file)
            this.fileList = files;
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        async onExport(){
            if(this.pager.OrderBy==null){
                this.pager.OrderBy="date";
                this.pager.IsAsc=false;
            }
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }else {
                this.$message({ message: "请选择时间！", type: "warning" });
                return;
            }
            let pager = this.$refs.pager.getPager();
            let params = {  ...pager,   ...this.pager,   ...this.filter};
            let res = await exportPingduoduoFundDetailList(params);
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }

            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '拼多多账户资金_' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        // 点击行内数据趋势图
        async showchart(row) {
            this.filter2.timerange = this.filter.timerange;
            this.filter2.groupId = this.filter.groupId;
            this.filter2.shopCode = this.filter.shopCode;
            this.filter2.isBundledBankCard = this.filter.isBundledBankCard;
            this.filter2.startTime2 = null;
            this.filter2.endTime2 = null;
            if (this.filter.timerange2) {
                this.filter2.startTime2 = this.filter.timerange2[0];
                this.filter2.endTime2 = this.filter.timerange2[1];
            }
            let that = this;
            this.filter2.startTime = null;
            this.filter2.endTime = null;
            if (this.filter2.timerange) {
                this.filter2.startTime = this.filter.timerange[0];
                this.filter2.endTime = this.filter.timerange[1];
            }
            this.filter2.shopCode = row.shopCode;
            this.pager.OrderBy="date";
            this.pager.IsAsc=false;
            let pager = this.$refs.pager.getPager();
            const params = { ...pager, ... this.filter2 };
            if (params === false) {
                return;
            }
            that.buscharDialog.title = "";
            const res = await getPingduoduoFundDetailListMap(params).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res;
                that.buscharDialog.title = res.title;
                res.title = "";
            })
        },
        // 动态时间改变趋势图
        async selectMapTime(val) {
            let that = this;
            if(val){
                this.filter2.startTime = val[0];
                this.filter2.endTime = val[1];
            }else{
                this.filter2.startTime = "";
                this.filter2.endTime = "";
            }
            
            var params = new Object();
            params.startTime = this.filter2.startTime;
            params.endTime = this.filter2.endTime;
            params.shopCode = this.filter2.shopCode;
            params.OrderBy = "date";
            params.IsAsc = false;
            if (params === false) {
                return;
            }
            that.buscharDialog.title = "";
            await getPingduoduoFundDetailListMap(params).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res;
                that.buscharDialog.title = res.title;
                res.title = "";
            });
            await this.$refs.buschar.initcharts();
        },
        // 点击汇总趋势图
        async onsummaryClick() {
            this.filter2.timerange = this.filter.timerange;
            this.filter2.groupId = this.filter.groupId;
            this.filter2.shopCode = this.filter.shopCode;
            this.filter2.isBundledBankCard = this.filter.isBundledBankCard;
            this.filter2.startTime2 = null;
            this.filter2.endTime2 = null;
            if (this.filter.timerange2) {
                this.filter2.startTime2 = this.filter.timerange2[0];
                this.filter2.endTime2 = this.filter.timerange2[1];
            }
            let that = this;
            if(this.filter2.timerange){
                this.filter2.startTime = this.filter2.timerange[0];
                this.filter2.endTime = this.filter2.timerange[1];
            }else{
                this.filter2.startTime = "";
                this.filter2.endTime = "";
            }
            var params = new Object();
            params.startTime = this.filter2.startTime;
            params.endTime = this.filter2.endTime;
            params.groupId = this.filter2.groupId;
            params.shopCode = this.filter2.shopCode;
            params.isBundledBankCard = this.filter2.isBundledBankCard;
            params.startTime2 = this.filter2.startTime2;
            params.endTime2 = this.filter2.endTime2;
            params.OrderBy = "date";
            params.IsAsc = false;
            if (params === false) {
                return;
            }
            that.buscharDialog.title = "数据总览";
            await getPingduoduoFundDetailListMap(params).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res;
                that.buscharDialog.title = "数据总览";
                res.title = "";
            });
            await this.$refs.buschar.initcharts();
        },
        downLoadTemplateFile() {
            window.open("../../static/excel/financial/拼多多资金站内信_账户资金导入.xlsx", "_self");
        },
        // 打开导入记录弹窗
        importTask() {
            this.importRecordVisible = true;
            this.getImportTaskList();
        },
        // 每页数量改变
        Sizechange2(val) {
            this.importRecordFilter.currentPage = 1;
            this.importRecordFilter.pageSize = val;
            this.getImportTaskList();
        },
        // 当前页改变
        Pagechange2(val) {
            this.importRecordFilter.currentPage = val;
            this.getImportTaskList();
        },
        // 获取导入记录
        async getImportTaskList() {
            this.importRecordVisible = true;
            const { data, success } = await getImportPingduoduoFundDetailTaskLog(this.importRecordFilter);
            if (success) {
                this.tableData2 = data.list;
                this.total2 = data.total;
            } else {
                this.$message.error("获取导入记录失败，存在未查询到的店铺！");
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px 0 0;

  .title-text {
    display: flex;
    align-items: center;

    .title-close {
      margin-left: 10px;
    }
  }
}
</style>

